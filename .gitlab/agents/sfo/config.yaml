gitops:
  # Manifest projects are watched by the agent. Whenever a project changes,
  # GitLab deploys the changes using the agent.
  manifest_projects:
    # No authentication mechanisms are currently supported.
    # The `id` is a path to a Git repository with Kubernetes resource definitions
    # in YAML or JSON format.
    - id: republix/bento-3
      # Namespace to use if not set explicitly in object manifest.
      # Also used for inventory ConfigMap objects.
      default_namespace: sfo-ns
      # Reconcile timeout defines whether the applier should wait
      # until all applied resources have been reconciled, and if so,
      # how long to wait.
      reconcile_timeout: 3600s # 1 hour by default
      # Dry run strategy defines whether changes should actually be performed,
      # or if it is just talk and no action.
      # https://github.com/kubernetes-sigs/cli-utils/blob/d6968048dcd80b1c7b55d9e4f31fc25f71c9b490/pkg/common/common.go#L68-L89
      # Can be: none, client, server
      dry_run_strategy: none # 'none' by default
      # Prune defines whether pruning of previously applied
      # objects should happen after apply.
      prune: true # enabled by default
      # Prune timeout defines whether we should wait for all resources
      # to be fully deleted after pruning, and if so, how long we should
      # wait.
      prune_timeout: 3600s # 1 hour by default
      # Prune propagation policy defines the deletion propagation policy
      # that should be used for pruning.
      # https://github.com/kubernetes/apimachinery/blob/44113beed5d39f1b261a12ec398a356e02358307/pkg/apis/meta/v1/types.go#L456-L470
      # Can be: orphan, background, foreground
      prune_propagation_policy: foreground # 'foreground' by default
      # Inventory policy defines if an inventory object can take over
      # objects that belong to another inventory object or don't
      # belong to any inventory object.
      # This is done by determining if the apply/prune operation
      # can go through for a resource based on the comparison
      # the inventory-id value in the package and the owning-inventory
      # annotation (config.k8s.io/owning-inventory) in the live object.
      # https://github.com/kubernetes-sigs/cli-utils/blob/d6968048dcd80b1c7b55d9e4f31fc25f71c9b490/pkg/inventory/policy.go#L12-L66
      # Can be: must_match, adopt_if_no_inventory, adopt_all
      inventory_policy: must_match # 'must_match' by default
