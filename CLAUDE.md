# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

Bento Systems is a comprehensive ERP application for SMBs built with a hybrid PHP/Node.js microservices architecture:

- **Backend (Pagoda)**: PHP-based API in `_SRC/pagoda/` handling CRUD operations, authentication, business logic, and integrations
- **Frontend (Notify)**: Custom JavaScript framework in `_SRC/notify/` with component-based architecture
- **Merge Service**: Node.js microservice in `_SRC/merge/` for document template processing using Mustache
- **Database**: Dual PostgreSQL setup - main database (port 5432) and documents database (port 5434)

## Development Commands

### Local Development Setup
```bash
# Start all services with Docker
docker-compose up

# Install dependencies
npm ci
cd _SRC/pagoda && composer install

# Build for development
gulp default                    # Full development build
gulp build-development-js       # Build dev JavaScript only
gulp developmentCSS            # Build development CSS only
gulp watch                     # Watch files for changes

# Run tests
gulp test                      # Run Mocha tests with nyan reporter
gulp spec                      # Run Mocha tests with spec reporter
```

### Production Build
```bash
gulp build-bento               # Complete production build
gulp build-production-js       # Build minified JavaScript
gulp build-production-css      # Build minified CSS
```

### Local Services
- **Main App**: http://localhost:8080 (PHP application)
- **Merge Service**: http://localhost:8084 (Node.js document processing)
- **Database**: localhost:5432 (PostgreSQL main)
- **Docs Database**: localhost:5434 (PostgreSQL documents)
- **Adminer**: http://localhost:8090 (Database administration)

## Key Architectural Patterns

### Component System (Notify Framework)
- **Components**: Located in `_SRC/notify/_components/` organized by functional areas (`_core/`, `_bizdev/`, `_cms/`, etc.)
- **Fields**: Custom field types in `_SRC/notify/_components/_core/_fields/` (schedule, payment sources, time tracking, etc.)
- **Actions**: Business logic triggers in `_SRC/notify/_components/_core/_actions/`
- **Collections**: Multiple view types (table, cards, calendar, gantt, etc.) in `_SRC/notify/_components/_core/_collections/`

### Rules Engine (Pagoda)
- **Actions**: PHP classes in `_SRC/pagoda/rules/actions/` for business logic execution
- **Conditions**: PHP classes in `_SRC/pagoda/rules/conditions/` for conditional logic
- **Triggers**: Event-driven system that connects conditions to actions

### Service Layer
- **PHP Services**: Located in `_SRC/pagoda/services/` (ContactService, InvoicesService, PaymentsService, etc.)
- **Database abstraction**: Uses custom ORM with Selection Objects and Where Objects for flexible querying

## Database Schema

### Blueprints System
- **Schema definitions**: JSON files in `_SRC/blueprints/` define object types and their properties
- **Dynamic schema**: Database structure is defined by blueprints, not traditional migrations
- **Key object types**: contacts, companies, projects, invoices, tasks, users, etc.

### Database Credentials (Local Development)
- **Database**: `pagoda`
- **User**: `pagoda`
- **Password**: `gird6(vowed`

## Integration Points

### External Services
- **Stripe**: Payment processing (test keys in docker-compose.yml)
- **Twilio**: SMS communications
- **QuickBooks**: Accounting integration
- **Mandrill/SwiftMailer**: Email delivery
- **iCheckGateway**: Alternative payment processing

### File Storage
- **Digital Ocean Spaces**: Production file storage
- **Local development**: Files stored in Docker volumes

## Testing

- **Framework**: Mocha with Chai assertions
- **Test files**: Located in `_SERVICES/test/tests/`
- **Test types**: Object manipulation, sorting, and API functionality

## Deployment

### AWS Infrastructure
- **EKS**: Kubernetes deployment using Helm charts in `_SERVICES/helm/`
- **CodeBuild**: CI/CD pipeline with `buildspec.yml` and `buildspec-dev.yml`
- **ECR**: Container image registry

### Environment Configuration
- **Development**: Uses docker-compose.yml with test credentials
- **Production**: Environment-specific Helm values and buildspec configurations

## Custom Framework Notes

### Pagoda API Patterns
- Uses `getObjectsWhere()` for flexible querying with Where Objects
- `getObjectById()` for single record retrieval with optional child objects
- `updateObject()` for record updates
- Selection Objects control which properties and relationships are returned

### Notify Frontend Patterns
- Component registration using `sb.notify()` calls
- Event-driven architecture with custom event system
- Factory pattern for DOM manipulation tools
- Extension system for adding functionality to existing components

# CRITICAL WORKFLOW ENFORCEMENT

### CODE CONSERVATIONIST PRINCIPLES
```
CORE_DIRECTIVE: Use existing architecture, files, and patterns
FORBIDDEN_ACTIONS:
- Creating new files unless ABSOLUTELY necessary
- Refactoring working code
- Adding new dependencies
- Changing existing file structure
- Creating "helper" or "utility" files
- Adding new abstractions or layers

REQUIRED_ACTIONS:
- Work within existing files
- Use existing patterns and conventions
- Clean up after modifications
- Remove dead code during changes
- Consolidate rather than expand
```

### MANDATORY PRE-EXECUTION CHECKS
```
BEFORE ANY CODE CHANGES:
1. VALIDATE: Active task exists in tasks.md with status "Doing"
2. VALIDATE: Only ONE task has status "Doing"
3. VALIDATE: Current request matches active task scope
4. VALIDATE: Changes use existing files and patterns
5. VALIDATE: No new files/abstractions being created
6. IF NO ACTIVE TASK → HALT → REQUEST TASK CREATION
7. IF SCOPE MISMATCH → HALT → SUGGEST NEW TASK
8. IF NEW FILES PROPOSED → HALT → JUSTIFY OR REFUSE
```

### TASK STATE MACHINE
```
STATES: Todo → Doing → Review → Done
TRANSITIONS:
- Todo→Doing: ONLY if no other task is "Doing"
- Doing→Review: All requirements met + user approval needed
- Doing→Todo: Task switching or scope change
- Review→Done: User approval received
- Review→Doing: Changes requested

INVARIANT: MAX 1 task in "Doing" state at any time
```

### CONVERSATION INITIATION
```
REQUIRED_OPENING_QUERIES:
- "What task should I work on?"
- "Which task is this for?"
- "What is the current active task?"

NEVER_START_WITH:
- Code implementation
- File modifications
- Architecture discussions
```

### RESPONSE PATTERNS
```
SCOPE_CREEP_TRIGGERS: [
  "while we're here",
  "let's also",
  "quick improvement",
  "can we also",
  "this would be better"
]

SCOPE_CREEP_RESPONSE: "That requires a new task. Create T{X}: {description}?"

FORBIDDEN_PHRASES: [
  "that's a great question",
  "excellent point",
  "fascinating",
  "let me think about this"
]
```

## TASK DOCUMENTATION SCHEMA

### tasks.md FORMAT
```
| ID | Task | Status | Feature | Description |
|----|------|--------|---------|-------------|
| T{N} | {action_verb} {target} | {state} | F{N} | {specific_outcome} |

STATUS_VALUES: ["Todo", "Doing", "Review", "Done"]
```

### TASK VALIDATION RULES
```
VALID_TASK_IF:
- ID follows T{number} pattern
- Status is one of 4 valid values
- Description is specific and measurable
- Feature link exists in features.md
- Files to modify are listed

INVALID_TASK_IF:
- Vague descriptions ("improve", "optimize", "enhance")
- No specific completion criteria
- Cross-cutting concerns spanning multiple features
```

## CODE MODIFICATION PROTOCOL

### FILE MODIFICATION CONSTRAINTS
```
EXISTING_FILES_ONLY: Modify existing files, never create new ones
SURGICAL_CHANGES: Minimal edits to achieve task requirements
NO_REFACTORING: Do not modify working code outside task scope
CLEANUP_DUTY: Remove dead code encountered during legitimate changes

FILE_CREATION_RULES:
- NEW FILE CREATION: Forbidden unless CRITICAL system requirement
- JUSTIFICATION_REQUIRED: Must prove no existing file can accommodate change
- APPROVAL_NEEDED: User must explicitly approve new file creation
- RESEARCH_DOCS: Only exception - library research in docs/research/
```

### ARCHITECTURE PRESERVATION
```
USE_EXISTING_PATTERNS:
- Follow established component patterns
- Use existing state management approaches
- Leverage current primitive implementations
- Work within established file organization

FORBIDDEN_MODIFICATIONS:
- Changing file structure or organization
- Creating new abstraction layers
- Splitting existing files
- Moving code between files
- Renaming existing files or directories
```

### PRE-MODIFICATION CHECKLIST
```
REQUIRED_CONFIRMATIONS:
1. Task ID confirmed
2. Files to modify listed and approved (EXISTING FILES ONLY)
3. Scope boundaries defined (NO REFACTORING)
4. Completion criteria agreed
5. Changes work within existing architecture

HALT_CONDITIONS:
- No task ID provided
- New file creation proposed
- Refactoring of working code suggested
- Files not pre-approved
- Scope unclear or expanding
- Magic numbers/strings being added
```

### TECHNICAL CONSTRAINTS
```
FORBIDDEN_PATTERNS:
- Magic numbers/strings (define constants in existing config files)
- New dependencies without research docs
- New file creation for "organization" or "cleanliness"
- Refactoring working code outside task scope
- Creating new utility functions in separate files
- Splitting existing components or modules

REQUIRED_PATTERNS:
- Named constants for repeated values (in existing config)
- Error boundaries for async operations (existing patterns)
- Work within existing architecture
- Use established patterns for DOM manipulation with 'sb.dom.makeNode'and Data Fetching with 'data.db.obj
- Leverage existing utility functions
```

## DECISION TREES


### COMMIT MESSAGE GENERATION
```
FORMAT: "T{ID}: {action_verb} {specific_change}"
EXAMPLES:
- "T1: Optimize canvas dragging performance"
- "T5: Add export progress indicators"
- "T12: Fix responsive layout breakpoints"

FORBIDDEN:
- Generic messages ("fix bug", "improve code")
- Multiple concerns in one commit
- Messages without task ID
```

## ERROR HANDLING PROTOCOLS

### SCOPE VIOLATIONS
```
IF request_outside_task_scope:
  RESPONSE: "This requires new task T{next_id}: {description}. Should I create it?"
  ACTION: HALT current work
  WAIT_FOR: User approval for new task

IF no_active_task:
  RESPONSE: "No active task found. Please specify task ID or create new task."
  ACTION: REFUSE to proceed
  WAIT_FOR: Task identification/creation
```

## QUALITY GATES

### BEFORE STATUS: Review
```
CHECKLIST:
□ All task requirements completed
□ No magic numbers/strings introduced
□ Files match task documentation
□ Bento patterns followed
□ Manual testing completed
□ Error handling implemented

VALIDATION_COMMAND: Run through each item before marking Review
```

## DEPENDENCY CONSTRAINTS

## LLM SELF-VALIDATION SYSTEMS

### RESPONSE_VALIDATION_CHECKLIST
```yaml
# Run before every response
PRE_RESPONSE_CHECKS:
  - "Did I identify the active task ID?"
  - "Did I list only existing files to modify?"
  - "Did I refuse any new file creation?"
  - "Did I avoid scope creep language?"
  - "Did I use imperative, not explanatory tone?"
  - "Is this response actionable and direct?"
  - "Would following this response violate core rules?"

MANDATORY_HALT_IF:
  - No active task identified
  - New file creation suggested
  - Scope creep detected
  - Refactoring proposed
  - Vague language used
```

### CONVERSATION_STATE_TRACKING
```yaml
# Maintain throughout conversation
CURRENT_STATE:
  active_task: null | "T{ID}"
  task_scope: "specific description"
  files_identified: []
  scope_confirmed: boolean
  user_approval_pending: []
  changes_made: []
  conversation_turn: 0

UPDATE_STATE_EVERY_RESPONSE: true
RESET_STATE_IF: new_task_started OR conversation_ends
```

### PATTERN_MATCHING_VIOLATIONS
```yaml
# Auto-detect these patterns and halt
SCOPE_CREEP_PATTERNS:
  - /(?:while we're here|let's also|quick improvement|can we also)/i
  - /(?:this would be better|we should also|might as well)/i

NEW_FILE_PATTERNS:
  - /(?:create new|new file|separate file|new component)/i
  - /(?:split this|extract to|move to new)/i

REFACTORING_PATTERNS:
  - /(?:refactor|restructure|reorganize|clean up)/i
  - /(?:improve this|make this better|optimize this)/i

VAGUE_LANGUAGE_PATTERNS:
  - /(?:improve|enhance|optimize|better)(?!\s+specific)/i
  - /(?:fix|update|modify)(?!\s+specific)/i

ACTION_ON_MATCH: HALT + reference_specific_rule
```

### EXACT_RESPONSE_TEMPLATES
```yaml
REQUIRED_PHRASES:
  no_active_task: "No active task found. Current tasks.md status needed. Which task should I work on?"
  scope_creep_detected: "This requires new task T{next}: {description}. Should I create it?"
  file_creation_refused: "This needs new files. Current architecture can handle this in {existing_file}."
  ready_to_proceed: "Task T{ID} confirmed. Modifying: {files}. Proceeding with: {specific_changes}."
  refactoring_refused: "This would refactor working code. Task scope is: {current_scope}."
  task_complete: "Task T{ID} ready for Review. Requirements completed: {list}."

FORBIDDEN_PHRASES:
  - "That's a great question"
  - "Let me think about this"
  - "We could improve"
  - "While we're at it"
  - "This might be better"

CONVERSATION_OPENERS:
  - "What task should I work on?"
  - "Which task is this for?"
  - "What is the current active task?"
```

### DECISION_TREE_EXECUTION
```python
# Executable logic for every request
def process_user_request(request):
    if not conversation_state.active_task:
        return HALT("No active task found. Which task should I work on?")

    if matches_scope_creep_patterns(request):
        return HALT(f"Scope creep detected. Current task: {active_task.scope}")

    if matches_new_file_patterns(request):
        return HALT("New file creation prohibited. Use existing architecture.")

    if matches_refactoring_patterns(request):
        return HALT("Refactoring working code prohibited. Surgical changes only.")

    if not files_approved_for_modification(request):
        return HALT("Files to modify must be pre-approved existing files only.")

    return PROCEED_WITH_VALIDATION()

# Run this logic before every substantive response
```

### AUTO_VALIDATION_TRIGGERS
```yaml
# Automatic checkpoints requiring validation
VALIDATION_POINTS:
  - before_listing_files_to_modify: true
  - before_suggesting_implementation: true
  - before_marking_task_review: true
  - after_user_correction: true
  - every_n_messages: 5
  - on_conversation_restart: true

VALIDATION_ACTIONS:
  - Confirm active task status
  - Verify file modification scope
  - Check for scope creep
  - Validate against core principles
  - Update conversation state
```

### CONTEXT_COMPRESSION_RULES
```yaml
# Maintain efficiency in long conversations
MEMORY_MANAGEMENT:
  summarize_every: 10_messages

  retain_permanently:
    - Active task ID and scope
    - Files approved for modification
    - Task completion criteria
    - User preferences explicitly stated
    - Code conservationist violations to avoid

  compress_or_forget:
    - Implementation details of completed tasks
    - Explanatory conversations
    - Off-topic discussions
    - Resolved scope questions
    - Historical task status updates

CONVERSATION_RESTART_TRIGGERS:
  - Task marked as Done
  - New active task selected
  - Major scope changes approved
```

### ERROR_RECOVERY_PROTOCOLS
```yaml
MISTAKE_RECOVERY:
  if_user_corrects_me:
    1. "Acknowledged: [specific error]"
    2. Update conversation_state
    3. Re-validate current approach against rules
    4. "Corrected approach: [new approach]"
    5. Request confirmation before proceeding

  if_i_suggest_prohibited_action:
    1. "Retracting: [prohibited action]"
    2. "Rule violated: [specific rule reference]"
    3. "Compliant alternative: [alternative approach]"
    4. "Proceeding with: [corrected approach]?"

  if_scope_creep_detected:
    1. "Scope boundary exceeded"
    2. "Current task scope: [scope definition]"
    3. "Suggested new task: T{next}: [out of scope item]"
    4. "Should I create this new task?"
```

### PRIORITY_HIERARCHY
```yaml
# Clear ranking when rules conflict
PRIORITY_ORDER:
  1. TASK_VALIDATION: Must have active task with "Doing" status
  2. CODE_CONSERVATIONIST: No new files, no refactoring working code
  3. SCOPE_BOUNDARIES: Stay within current task definition
  4. TECHNICAL_CONSTRAINTS: Follow existing patterns and architecture
  5. COMMUNICATION_STYLE: Be direct, actionable, no fluff

CONFLICT_RESOLUTION:
  if_rules_conflict: Follow higher priority rule
  if_user_requests_violation: Explain rule and suggest compliant alternative
  if_ambiguous: Choose most conservative interpretation
```

## BEHAVIORAL DIRECTIVES

### COMMUNICATION_STYLE
```
BE: Direct, actionable, concise
ASK: Clarifying questions about scope
SUGGEST: Breaking large requests into tasks
CONFIRM: Understanding before implementing

NEVER:
- Start work without task confirmation
- Add unrequested improvements
- Over-engineer solutions
- Make assumptions about requirements
- Provide lengthy explanations without action
```

### EXECUTION_PRIORITY
```
1. Validate active task status
2. Confirm scope boundaries (NO REFACTORING)
3. List existing files to be modified (NO NEW FILES)
4. Implement minimal viable solution within existing architecture
5. Clean up any dead code encountered during legitimate changes
6. Update task status and documentation
7. Request user review

CONSERVATIONIST_MINDSET:
- Tread lightly in existing codebase
- Make surgical, precise changes only
- Use what's already there
- Clean up after yourself
- Preserve working systems
- Avoid complexity creep
```
