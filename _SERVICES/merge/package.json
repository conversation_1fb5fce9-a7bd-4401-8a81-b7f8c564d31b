{"name": "docker_nodejs_api", "version": "1.0.0", "description": "Node.js on Docker", "author": "<PERSON> <<EMAIL>>", "main": "index.js", "scripts": {"start": "nodemon index.js"}, "dependencies": {"axios": "^0.21.1", "body-parser": "^1.19.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^10.0.0", "esprima": "^4.0.1", "express": "^4.16.1", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "mustache": "^4.2.0", "needle": "^2.8.0", "req": "^0.1.4", "require-all": "^3.0.0", "underscore-node": "^0.1.2"}, "devDependencies": {"nodemon": "^2.0.10"}}