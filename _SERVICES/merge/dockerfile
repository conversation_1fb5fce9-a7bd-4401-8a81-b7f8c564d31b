FROM 918760427934.dkr.ecr.us-east-2.amazonaws.com/node:14-alpine

# Create app directory
RUN mkdir -p /home/<USER>/app
WORKDIR /home/<USER>/app

#Install nodemon for hot reloading
RUN npm install nodemon -g

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
# where available (npm@5+)
COPY package*.json ./

RUN npm install
# If you are building your code for production
# RUN npm ci --only=production

# Copy app files
#COPY ./src /home/<USER>/app/

COPY . .

EXPOSE 8084
CMD [ "nodemon", "-L", "src/index.js" ]