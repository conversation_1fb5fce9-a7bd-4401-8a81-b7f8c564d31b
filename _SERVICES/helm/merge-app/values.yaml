imageName:
imageTag:

configmap: bentomergeservice
config:
  BEN<PERSON>_DATABASE_NAME:
  BENTO_DATABASE_PASSWORD:
  BENTO_DATABASE_PORT:
  BENTO_DATABASE_SSL_FLAG:
  BENTO_DATABASE_URL:
  BENTO_DATABASE_USER:
  BENTO_DOCS_DATABASE_NAME:
  BENTO_DOCUMENTS_URL:
  CURRENT_ENV:
  DATA_ENDPOINT:
  ICG_APIKEY:
  ICG_APIKEY_DREAM:
  ICG_GATEWAYLIVEMODE:
  ICG_SITEID:
  ICG_SITEID_DREAM:
  ICG_SITEKEY:
  ICG_SITEKEY_DREAM:
  MERGE_ENDPOINT:
  STRIPE_PK:
  STRIPE_SK:

service:
  portName: http
  port: 8084
  type: ClusterIP

ingress:
  issuer: letsencrypt-prod-merge
  issuerSecret: tls-secret-merge
  issuerEmail: <EMAIL>
