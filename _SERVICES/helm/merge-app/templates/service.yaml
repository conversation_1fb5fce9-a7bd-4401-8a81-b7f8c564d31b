kind: Service
apiVersion: v1
metadata:
  name: {{ .Chart.Name }}
  labels:
    app.kubernetes.io/instance: {{ .Chart.Name }}
    app.kubernetes.io/managed-by: <PERSON>lm
    app.kubernetes.io/name: {{ .Chart.Name }}
    app.kubernetes.io/version: {{ .Chart.Version }}
    helm.sh/chart: {{ .Chart.Name }}
spec:
  ports:
    - name: {{.Values.service.portName }}
      protocol: TCP
      port: {{ .Values.service.port }}
      targetPort: http
  selector:
    app.kubernetes.io/instance: {{ .Chart.Name }}
    app.kubernetes.io/name: {{ .Chart.Name }}
  type: {{ .Values.service.type }}
