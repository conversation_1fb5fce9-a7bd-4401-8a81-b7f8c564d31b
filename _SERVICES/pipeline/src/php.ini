[PHP]

upload_max_filesize = 50M

; Maximum size of POST data that P<PERSON> will accept.
; Its value may be 0 to disable the limit. It is ignored if POST data reading
; is disabled through enable_post_data_reading.
; http://php.net/post-max-size
post_max_size = 50M

; This sets the maximum amount of memory in bytes that a script is allowed to allocate.
; This helps prevent poorly written scripts for eating up all available memory on a server...
memory_limit = 512M
