-- Adminer 4.3.1 PostgreSQL dump

INSERT INTO "blueprints" ("id", "instance", "blueprint_type", "blueprint_name", "blueprint", "date_created") VALUES
(190,	'pagodadev',	'object',	'datadump                           ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "BEO": {"name": "BEO", "type": "string", "immutable": false}, "CPG": {"name": "CPG", "type": "string", "immutable": false}, "Tax": {"name": "Tax", "type": "string", "immutable": false}, "Beer": {"name": "Beer", "type": "string", "immutable": false}, "City": {"name": "City", "type": "string", "immutable": false}, "Cost": {"name": "Cost", "type": "string", "immutable": false}, "PO #": {"name": "PO #", "type": "string", "immutable": false}, "Paid": {"name": "Paid", "type": "string", "immutable": false}, "Room": {"name": "Room", "type": "string", "immutable": false}, "Wine": {"name": "Wine", "type": "string", "immutable": false}, "First": {"name": "First", "type": "string", "immutable": false}, "Labor": {"name": "Labor", "type": "string", "immutable": false}, "Month": {"name": "Month", "type": "string", "immutable": false}, "Other": {"name": "Other", "type": "string", "immutable": false}, "Theme": {"name": "Theme", "type": "string", "immutable": false}, "Third": {"name": "Third", "type": "string", "immutable": false}, "Total": {"name": "Total", "type": "string", "immutable": false}, "Actual": {"name": "Actual", "type": "string", "immutable": false}, "Booked": {"name": "Booked", "type": "string", "immutable": false}, "Closed": {"name": "Closed", "type": "string", "immutable": false}, "County": {"name": "County", "type": "string", "immutable": false}, "Liquor": {"name": "Liquor", "type": "string", "immutable": false}, "Postal": {"name": "Postal", "type": "string", "immutable": false}, "Profit": {"name": "Profit", "type": "string", "immutable": false}, "Second": {"name": "Second", "type": "string", "immutable": false}, "Status": {"name": "Status", "type": "string", "immutable": false}, "Address": {"name": "Address", "type": "string", "immutable": false}, "Balance": {"name": "Balance", "type": "string", "immutable": false}, "Country": {"name": "Country", "type": "string", "immutable": false}, "Event #": {"name": "Event #", "type": "string", "immutable": false}, "Folio #": {"name": "Folio #", "type": "string", "immutable": false}, "Invoice": {"name": "Invoice", "type": "string", "immutable": false}, "Members": {"name": "Members", "type": "string", "immutable": false}, "Planned": {"name": "Planned", "type": "int", "immutable": false}, "Revised": {"name": "Revised", "type": "string", "immutable": false}, "St/Prov": {"name": "St/Prov", "type": "string", "immutable": false}, "Year-Mo": {"name": "Year-Mo", "type": "string", "immutable": false}, "% Profit": {"name": "% Profit", "type": "string", "immutable": false}, "BPMTotal": {"name": "BPMTotal", "type": "string", "immutable": false}, "Canc Chg": {"name": "Canc Chg", "type": "string", "immutable": false}, "Category": {"name": "Category", "type": "string", "immutable": false}, "Contract": {"name": "Contract", "type": "string", "immutable": false}, "Definite": {"name": "Definite", "type": "string", "immutable": false}, "Discount": {"name": "Discount", "type": "string", "immutable": false}, "Exempt #": {"name": "Exempt #", "type": "string", "immutable": false}, "Room Chg": {"name": "Room Chg", "type": "string", "immutable": false}, "Subtotal": {"name": "Subtotal", "type": "string", "immutable": false}, "Tax Name": {"name": "Tax Name", "type": "string", "immutable": false}, "# Expires": {"name": "# Expires", "type": "string", "immutable": false}, "Account #": {"name": "Account #", "type": "string", "immutable": false}, "Booked By": {"name": "Booked By", "type": "string", "immutable": false}, "CPG (Sub)": {"name": "CPG (Sub)", "type": "string", "immutable": false}, "Canc Date": {"name": "Canc Date", "type": "string", "immutable": false}, "Equipment": {"name": "Equipment", "type": "string", "immutable": false}, "Event End": {"name": "Event End", "type": "string", "immutable": false}, "Loyalty #": {"name": "Loyalty #", "type": "string", "immutable": false}, "Operation": {"name": "Operation", "type": "string", "immutable": false}, "Reference": {"name": "Reference", "type": "string", "immutable": false}, "Sales Rep": {"name": "Sales Rep", "type": "string", "immutable": false}, "Site Last": {"name": "Site Last", "type": "string", "immutable": false}, "Telephone": {"name": "Telephone", "type": "int", "immutable": false}, "Account ID": {"name": "Account ID", "type": "string", "immutable": false}, "Event Date": {"name": "Event Date", "type": "string", "immutable": false}, "Guaranteed": {"name": "Guaranteed", "type": "int", "immutable": false}, "Party Name": {"name": "Party Name", "type": "string", "immutable": false}, "Pay Method": {"name": "Pay Method", "type": "string", "immutable": false}, "Site Email": {"name": "Site Email", "type": "string", "immutable": false}, "Site First": {"name": "Site First", "type": "string", "immutable": false}, "Site Title": {"name": "Site Title", "type": "string", "immutable": false}, "Tax Exempt": {"name": "Tax Exempt", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "Booked Last": {"name": "Booked Last", "type": "string", "immutable": false}, "Cancel Type": {"name": "Cancel Type", "type": "string", "immutable": false}, "Day Of Week": {"name": "Day Of Week", "type": "string", "immutable": false}, "Event Begin": {"name": "Event Begin", "type": "string", "immutable": false}, "Event Notes": {"name": "Event Notes", "type": "string", "immutable": false}, "Non-Members": {"name": "Non-Members", "type": "string", "immutable": false}, "Parking Lot": {"name": "Parking Lot", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "Booked First": {"name": "Booked First", "type": "string", "immutable": false}, "Client Email": {"name": "Client Email", "type": "string", "immutable": false}, "Delivery Chg": {"name": "Delivery Chg", "type": "string", "immutable": false}, "Deposit Date": {"name": "Deposit Date", "type": "string", "immutable": false}, "Next Deposit": {"name": "Next Deposit", "type": "string", "immutable": false}, "Site Contact": {"name": "Site Contact", "type": "string", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "Account Email": {"name": "Account Email", "type": "string", "immutable": false}, "Booking Email": {"name": "Booking Email", "type": "string", "immutable": false}, "Booking Title": {"name": "Booking Title", "type": "string", "immutable": false}, "Business Type": {"name": "Business Type", "type": "string", "immutable": false}, "Site Cellular": {"name": "Site Cellular", "type": "string", "immutable": false}, "Master Account": {"name": "Master Account", "type": "string", "immutable": false}, "Site Telephone": {"name": "Site Telephone", "type": "int", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "Address (Other)": {"name": "Address (Other)", "type": "string", "immutable": false}, "Booking Contact": {"name": "Booking Contact", "type": "string", "immutable": false}, "Credit Card Fee": {"name": "Credit Card Fee", "type": "string", "immutable": false}, "Sales Rep Email": {"name": "Sales Rep Email", "type": "string", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "Account Category": {"name": "Account Category", "type": "string", "immutable": false}, "Booking Cellular": {"name": "Booking Cellular", "type": "string", "immutable": false}, "Discount Comment": {"name": "Discount Comment", "type": "string", "immutable": false}, "Operational Cost": {"name": "Operational Cost", "type": "string", "immutable": false}, "Booking Telephone": {"name": "Booking Telephone", "type": "int", "immutable": false}, "Primary Site Name": {"name": "Primary Site Name", "type": "string", "immutable": false}, "Client/Organization": {"name": "Client/Organization", "type": "string", "immutable": false}, "Sales Rep Telephone": {"name": "Sales Rep Telephone", "type": "string", "immutable": false}, "Food and Non-Alcoholic Beverage": {"name": "Food and Non-Alcoholic Beverage", "type": "string", "immutable": false}}',	'2017-03-04 17:31:51.358489'),
(261,	'pagodadev',	'object',	'inventory_units',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "measurements": {"name": "Units", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "divisor": {"name": "Divisor", "type": "float", "immutable": false}, "multiplier": {"name": "Multiplier", "type": "float", "immutable": false}, "base_reference": {"name": "Base Unit", "type": "int", "immutable": false}}, "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.436148'),
(271,	'pagodadev',	'object',	'events',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "venue": {"name": "Venue", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "status": {"name": "Status", "type": "objectId", "immutable": false, "objectType": "event_status", "selectDisplay": "[name]"}, "company": {"name": "Company", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]"}, "vendors": {"name": "Vendors", "type": "objectId", "immutable": false, "objectType": "event_vendors", "selectDisplay": "[name]"}, "end_date": {"name": "Event End Date", "type": "date", "immutable": false}, "contracts": {"name": "Contracts", "type": "objectIds", "immutable": false, "objectType": "contracts", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "event_name": {"name": "Event Name", "type": "string", "immutable": false}, "event_type": {"name": "Event Type", "type": "objectId", "immutable": false, "objectType": "event_type_options", "selectDisplay": "[name]"}, "is_deleted": {"name": "Is Deleted", "type": "select", "options": ["No", "Yes"], "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Event Start Date", "type": "date", "immutable": false}, "tax_exempt": {"name": "Tax Exempt", "type": "boolean", "immutable": false}, "booked_date": {"name": "Booked Date", "type": "date", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "guest_count": {"name": "Guest Count", "type": "int", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "decor_budget": {"name": "Decor Budget", "type": "usd", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "service_style": {"name": "Service Style", "type": "objectId", "immutable": false, "objectType": "event_service_style", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "contract_status": {"name": "Contract Status", "type": "objectId", "immutable": true, "objectType": "event_contract_status", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "event_specialist": {"name": "Event Specialist", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "sales_specialist": {"name": "Sales Specialist", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "additional_contacts": {"name": "Additional Contacts", "type": "objectIds", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.448008'),
(185,	'pagodadev',	'object',	'client_hold                        ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "staff_id": {"name": "staff_id", "type": "int", "immutable": false}, "client_id": {"name": "client_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "hold_contact": {"name": "hold_contact", "type": "string", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "hold_release_date": {"name": "hold_release_date", "type": "string", "immutable": false}}',	'2017-03-04 17:31:51.350768'),
(196,	'pagodadev',	'object',	'event_state                        ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "blog": {"name": "blog", "type": "object", "immutable": false}, "linen": {"name": "linen", "type": "object", "immutable": false}, "av_notes": {"name": "av_notes", "type": "object", "immutable": false}, "av_sheet": {"name": "av_sheet", "type": "object", "immutable": false}, "event_id": {"name": "event_id", "type": "int", "immutable": false}, "staffing": {"name": "staffing", "type": "object", "immutable": false}, "bar_notes": {"name": "bar_notes", "type": "object", "immutable": false}, "floorplan": {"name": "floorplan", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "date_updated": {"name": "date_updated", "type": "string", "immutable": false}, "iec_contract": {"name": "iec_contract", "type": "object", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "tent_choices": {"name": "tent_choices", "type": "object", "immutable": false}, "final_payment": {"name": "final_payment", "type": "object", "immutable": false}, "catering_notes": {"name": "catering_notes", "type": "object", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "event_packlist": {"name": "event_packlist", "type": "object", "immutable": false}, "menu_finalized": {"name": "menu_finalized", "type": "object", "immutable": false}, "rain_floorplan": {"name": "rain_floorplan", "type": "object", "immutable": false}, "thank_you_note": {"name": "thank_you_note", "type": "object", "immutable": false}, "venue_contract": {"name": "venue_contract", "type": "object", "immutable": false}, "initial_payment": {"name": "initial_payment", "type": "object", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "ac_heat_selection": {"name": "ac_heat_selection", "type": "object", "immutable": false}, "catering_packlist": {"name": "catering_packlist", "type": "object", "immutable": false}, "prep_list_completed": {"name": "prep_list_completed", "type": "object", "immutable": false}, "transportation_notes": {"name": "transportation_notes", "type": "object", "immutable": false}, "initial_meeting_sheet": {"name": "initial_meeting_sheet", "type": "object", "immutable": false}}',	'2017-03-04 17:31:51.366513'),
(202,	'pagodadev',	'object',	'note_replies                       ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "note": {"name": "note", "type": "string", "immutable": false}, "author": {"name": "author", "type": "objectId", "immutable": false, "objectType": "staff"}, "parent": {"name": "parent", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.372758'),
(305,	'pagodadev',	'instance',	'instances',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "email": {"name": "Email Address", "type": "string", "immutable": false}, "fname": {"name": "First Name", "type": "string", "immutable": false}, "lname": {"name": "Last Name", "type": "string", "immutable": false}, "credit": {"name": "Credit", "type": "float", "immutable": false}, "parent": {"name": "Parent", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "db_post": {"name": "Database Post", "type": "string", "immutable": false}, "db_read": {"name": "Database Read", "type": "string", "immutable": false}, "enabled": {"name": "Enabled", "type": "select", "options": ["Disabled", "Enabled"], "immutable": false}, "sold_by": {"name": "Sold by", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]", "objectOverflow": true}, "version": {"name": "Version", "type": "string", "immutable": false}, "contract": {"name": "Contract", "type": "objectId", "immutable": false, "objectType": "contract", "selectDisplay": "[name]"}, "db_write": {"name": "Database Write", "type": "string", "immutable": false}, "flat_fee": {"name": "Flat fee", "type": "int", "immutable": false}, "instance": {"name": "Slug", "type": "string", "immutable": false}, "sms_from": {"name": "SMS From", "type": "string", "immutable": false}, "tax_rate": {"name": "Tax rate", "type": "objectId", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "emailFrom": {"name": "Email From", "type": "string", "immutable": false}, "temp_user": {"name": "Temp Root User", "type": "object", "immutable": false}, "components": {"name": "Components", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "files_read": {"name": "Files Read", "type": "string", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "systemName": {"name": "System Name", "type": "string", "immutable": false}, "twilio_sid": {"name": "Twilio SID", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "files_write": {"name": "Files Write", "type": "string", "immutable": false}, "is_template": {"name": "Is template?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "pageModules": {"name": "Page Modules", "type": "string", "immutable": false}, "permissions": {"name": "Permisions", "type": "string", "immutable": false}, "billing_plan": {"name": "Billing Plan", "type": "object", "immutable": false}, "billing_type": {"name": "Billing Type", "type": "string", "immutable": false}, "company_logo": {"name": "Company logo", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "name"}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "files_bucket": {"name": "Files Bucket", "type": "string", "immutable": false}, "files_delete": {"name": "Files Delete", "type": "string", "immutable": false}, "last_invoice": {"name": "Last invoice", "type": "objectId", "immutable": false, "objectType": "invoices", "selectDisplay": "[date_created]", "objectOverflow": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "mandrill_api": {"name": "Mandrill API Key", "type": "string", "immutable": false}, "moduleSource": {"name": "Module Source", "type": "string", "immutable": false}, "qty_of_users": {"name": "Qty of users", "type": "int", "immutable": false}, "twilio_token": {"name": "Twilio Token", "type": "string", "immutable": false}, "userSettings": {"name": "User Settings", "type": "string", "immutable": false}, "factorySource": {"name": "Factory Source", "type": "string", "immutable": false}, "flat_user_cap": {"name": "Flat user cap", "type": "int", "immutable": false}, "instance_type": {"name": "Instance type", "type": "int", "immutable": false}, "mailchimp_api": {"name": "MailChimp API Key", "type": "string", "immutable": false}, "searchObjects": {"name": "Search Objects", "type": "string", "immutable": false}, "settingSource": {"name": "Setting Source", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "per_user_price": {"name": "Per User Price", "type": "int", "immutable": false}, "price_per_user": {"name": "Price per user", "type": "int", "immutable": false}, "trial_end_date": {"name": "Trial end date", "type": "date", "immutable": false}, "componentSource": {"name": "Component Source", "type": "string", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "settingsModules": {"name": "Settings Modules", "type": "string", "immutable": false}, "settings_objects": {"name": "Settings Objects", "type": "string", "immutable": false}, "trial_start_date": {"name": "Trial start date", "type": "date", "immutable": false}, "last_billing_date": {"name": "Last billing date", "type": "date", "immutable": false}, "mailchimp_list_id": {"name": "MailChimp List ID", "type": "string", "immutable": false}, "stripe_account_id": {"name": "Stripe Account ID", "type": "string", "immutable": false}, "account_signup_code": {"name": "Account signup code", "type": "string", "immutable": false}, "quickbooks_realm_id": {"name": "QuickBooks Realm ID", "type": "string", "immutable": false}, "quickbooks_access_token": {"name": "QuickBooks Access Token", "type": "string", "immutable": false}, "quickbooks_refresh_token": {"name": "QuickBooks Refresh Token", "type": "string", "immutable": false}}',	'2017-06-11 14:20:44.48617'),
(42,	'pagodadev',	'object',	'_TestingOleBlue',	'{"updating": {"this": "thing"}}',	'2017-10-05 21:46:50.920443'),
(199,	'pagodadev',	'object',	'kitchen_staff_hours                ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "status": {"name": "status", "type": "int", "immutable": false}, "event_id": {"name": "event_id", "type": "int", "immutable": false}, "staff_id": {"name": "staff_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "shift_hours": {"name": "shift_hours", "type": "int", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.369741'),
(198,	'pagodadev',	'object',	'event_status                       ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "bar": {"name": "bar", "type": "int", "immutable": false}, "food": {"name": "food", "type": "int", "immutable": false}, "linen": {"name": "linen", "type": "int", "immutable": false}, "booked": {"name": "booked", "type": "int", "immutable": false}, "av_notes": {"name": "av_notes", "type": "int", "immutable": false}, "av_sheet": {"name": "av_sheet", "type": "int", "immutable": false}, "event_id": {"name": "event_id", "type": "int", "immutable": false}, "food_beo": {"name": "food_beo", "type": "int", "immutable": false}, "timeline": {"name": "timeline", "type": "int", "immutable": false}, "pack_list": {"name": "pack_list", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "floor_plan": {"name": "floor_plan", "type": "int", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "alcohol_beo": {"name": "alcohol_beo", "type": "int", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "guest_count": {"name": "guest_count", "type": "int", "immutable": false}, "menu_status": {"name": "menu_status", "type": "int", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "iec_contract": {"name": "iec_contract", "type": "int", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "entertainment": {"name": "entertainment", "type": "int", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "venue_contract": {"name": "venue_contract", "type": "int", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "catering_packlist": {"name": "catering_packlist", "type": "int", "immutable": false}, "lower_event_staff": {"name": "lower_event_staff", "type": "int", "immutable": false}, "catering_bar_notes": {"name": "catering_bar_notes", "type": "int", "immutable": false}, "catering_general_notes": {"name": "catering_general_notes", "type": "int", "immutable": false}, "catering_tasting_notes": {"name": "catering_tasting_notes", "type": "int", "immutable": false}, "hand_check_print_count": {"name": "hand_check_print_count", "type": "int", "immutable": false}, "lower_event_staff_note": {"name": "lower_event_staff_note", "type": "string", "immutable": false}, "food_and_beverage_diagram": {"name": "food_and_beverage_diagram", "type": "int", "immutable": false}, "lower_event_staff_requests": {"name": "lower_event_staff_requests", "type": "object", "immutable": false}}',	'2017-03-04 17:31:51.36863'),
(205,	'pagodadev',	'object',	'search_index                       ',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "measurements": {"name": "Units", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "divisor": {"name": "Divisor", "type": "float", "immutable": false}, "multiplier": {"name": "Multiplier", "type": "float", "immutable": false}, "base_reference": {"name": "Base Unit", "type": "int", "immutable": false}}, "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.375641'),
(243,	'pagodadev',	'object',	'inventory_items',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "cost": {"name": "Cost", "type": "usd", "immutable": false}, "units": {"name": "Units", "type": "int", "immutable": false}, "picture": {"name": "Picture", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "location": {"name": "Location", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "quantity": {"name": "Quantity", "type": "float", "immutable": false}, "shelf_tag": {"name": "Shelf Tag", "type": "string", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "vendor_id": {"name": "Vendor", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "exclusive_tax": {"name": "Exclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "inclusive_tax": {"name": "Inclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "received_date": {"name": "Received Date", "type": "date", "immutable": false}, "serial_number": {"name": "Serial Number", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "expiration_date": {"name": "Expiration Date", "type": "date", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "objectId", "immutable": false, "objectType": "inventory_groups", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectIds", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "initial_quantity": {"name": "Initial Quantity", "type": "float", "immutable": false}}',	'2017-03-04 17:31:51.417428'),
(211,	'pagodadev',	'object',	'venue_inventory                    ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "venue_id": {"name": "venue_id", "type": "int", "immutable": false}, "inventory": {"name": "inventory", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.382541'),
(222,	'pagodadev',	'object',	'staff_status',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.394711'),
(316,	'pagodadev',	'object',	'contract_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "notEmpty": true, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "updated_by": {"name": "Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "contract_object_type": {"name": "Type", "type": "select", "options": {"work_orders": "Work Orders"}, "immutable": false}}',	'2017-09-08 10:43:02.141192'),
(40,	'pagodadev',	'object',	'_Testing_BP',	'null',	'2017-10-05 21:46:50.920443'),
(216,	'pagodadev',	'object',	'form_submissions',	'{"id": {"name": "id", "type": "int", "immutable": true}, "email": {"name": "Email", "type": "string", "immutable": false}, "fName": {"name": "First Name", "type": "string", "immutable": false}, "lName": {"name": "Last Name", "type": "string", "immutable": false}, "phone": {"name": "Phone", "type": "string", "immutable": false}, "adults": {"name": "Number of Adults in Church", "type": "int", "immutable": false}, "twitter": {"name": "Twitter", "type": "string", "immutable": false}, "website": {"name": "Website", "type": "string", "immutable": false}, "facebook": {"name": "Facebook", "type": "string", "immutable": false}, "students": {"name": "Number of Students in Church", "type": "int", "immutable": false}, "userType": {"name": "User Type", "type": "string", "immutable": false}, "churchZip": {"name": "Church Zip", "type": "string", "immutable": false}, "churchCity": {"name": "Church City", "type": "string", "immutable": false}, "churchName": {"name": "Church Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "churchPhone": {"name": "Church Phone", "type": "string", "immutable": false}, "churchState": {"name": "Church State", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "shippingZip": {"name": "Shipping Zip", "type": "string", "immutable": false}, "bookQuantity": {"name": "Number of Books", "type": "int", "immutable": false}, "churchStreet": {"name": "Church Street", "type": "string", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "denomination": {"name": "Denomination", "type": "string", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "shippingCity": {"name": "Shipping City", "type": "string", "immutable": false}, "shippingState": {"name": "Shipping State", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "shippingStreet": {"name": "Shipping Street", "type": "string", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "confirmationCode": {"name": "Confirmation Code", "type": "string", "immutable": false}, "seniorPastorFName": {"name": "Senior First Name", "type": "string", "immutable": false}, "seniorPastorLName": {"name": "Senior Last Name", "type": "string", "immutable": false}}',	'2017-03-04 17:31:51.388219'),
(223,	'pagodadev',	'object',	'vendor_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "exclusive_tax": {"name": "Exclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name] - [rate]"}, "inclusive_tax": {"name": "Inclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name] - [rate]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-03-04 17:31:51.395656'),
(278,	'pagodadev',	'object',	'staff_rates',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "rate": {"name": "Monthly Rate", "type": "usd", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-09 12:55:50.035774'),
(231,	'pagodadev',	'object',	'event_contract_status',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.403667'),
(248,	'pagodadev',	'object',	'lead_source',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.422254'),
(41,	'pagodadev',	'object',	'_Testing_Blue2',	'null',	'2017-10-05 21:46:50.920443'),
(247,	'pagodadev',	'object',	'client_type',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "notEmpty": true, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.421331'),
(232,	'pagodadev',	'object',	'event_discount',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.405245'),
(233,	'pagodadev',	'object',	'event_service_style',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.406321'),
(221,	'pagodadev',	'object',	'survey_questions',	'{"id": {"name": "id", "type": "int", "immutable": true}, "text": {"name": "Text", "type": "string", "immutable": "false"}, "type": {"name": "type", "type": "select", "options": {"rate-us": "Rate Us", "endpoint": "Endpoint", "textarea": "Text-area", "multi-select": "Multi-select", "single-select": "Select"}, "immutable": false}, "answers": {"name": "Answers", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.393769'),
(229,	'pagodadev',	'object',	'receipt_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_accounts": {"name": "Chart Of Accounts", "type": "objectIds", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-03-04 17:31:51.401761'),
(273,	'pagodadev',	'object',	'contacts',	'{"id": {"name": "id", "type": "int", "immutable": true}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "contact_types", "selectDisplay": "[name]"}, "fname": {"name": "First Name", "type": "string", "notEmpty": true, "immutable": false}, "lname": {"name": "Last Name", "type": "string", "notEmpty": true, "immutable": false}, "state": {"name": "State", "type": "int", "immutable": false}, "company": {"name": "Company", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true}, "manager": {"name": "Manager", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "stripe_id": {"name": "Stripe ID", "type": "string", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "closing_date": {"name": "Closing Date", "type": "date", "immutable": false}, "contact_info": {"name": "Contact Info", "type": "objectIds", "immutable": true, "objectType": "contact_info", "selectDisplay": "[info]"}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "sales_person": {"name": "Sales Person", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "external_form": {"name": "External Form", "type": "objectId", "immutable": false, "objectType": "external_forms", "selectDisplay": "[name]"}, "quickbooks_id": {"name": "QuickBooks ID", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "follow_up_date": {"name": "Follow Up Date", "type": "date", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "potential_value": {"name": "Potential Value", "type": "int", "immutable": false}}',	'2017-09-29 15:49:04.315597'),
(237,	'pagodadev',	'object',	'survey_templates',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "name", "type": "string", "immutable": false}, "survey": {"name": "survey", "type": "object", "immutable": "false"}, "client_id": {"name": "client_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.410981'),
(246,	'pagodadev',	'object',	'client_status',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.420403'),
(188,	'pagodadev',	'object',	'initial_proposal_pricing           ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "note": {"name": "note", "type": "string", "immutable": false}, "type": {"name": "type", "type": "string", "immutable": false}, "author": {"name": "author", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "notify": {"name": "notify", "type": "objectIds", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "public": {"name": "public", "type": "int", "immutable": false}, "type_id": {"name": "type_id", "type": "int", "immutable": false}, "isDeleted": {"name": "isDeleted", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.355902'),
(34,	'pagodadev',	'object',	'inventory_recipe_categories',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "surcharges": {"name": "Surcharges", "type": "objectIds", "immutable": false, "objectType": "surcharges", "selectDisplay": "[name]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(283,	'pagodadev',	'object',	'menu_item_category',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "menu_item_category": {"name": "Menu Item Category", "type": "string", "immutable": false}}',	'2017-03-10 14:56:04.724732'),
(251,	'pagodadev',	'object',	'staff_base',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "zip": {"name": "Zip", "type": "string", "immutable": false}, "city": {"name": "City", "type": "string", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "state": {"name": "State", "type": "string", "immutable": false}, "street": {"name": "Street", "type": "string", "immutable": false}, "country": {"name": "Country", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.425857'),
(312,	'pagodadev',	'object',	'notes',	'{"id": {"name": "id", "type": "int", "immutable": true}, "note": {"name": "note", "type": "string", "immutable": false}, "type": {"name": "type", "type": "string", "immutable": false}, "author": {"name": "author", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "notify": {"name": "notify", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "public": {"name": "public", "type": "int", "immutable": false}, "type_id": {"name": "type_id", "type": "objectId", "immutable": false}, "log_data": {"name": "Log Data", "type": "object", "blueprint": {"type": {"name": "Type", "type": "select", "options": {"create": "Create", "delete": "Delete", "update": "Update", "state-change": "State Change"}, "immutable": true}, "details": {"name": "Details", "type": "string", "immutable": false}, "objectName": {"name": "Name", "type": "string", "immutable": false}}, "immutable": false}, "isDeleted": {"name": "isDeleted", "type": "int", "immutable": false}, "note_type": {"name": "Note Type", "type": "objectId", "immutable": false, "objectType": "note_types", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "page_params": {"name": "Page Params", "type": "string", "immutable": false}, "record_type": {"name": "Record Type", "type": "select", "options": {"log": "System Log", "comment": "User Comment"}, "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "activity_type": {"name": "Activity Type", "type": "select", "options": {"create": "Create", "delete": "Delete", "update": "Update"}, "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-07-13 09:19:25.189942'),
(314,	'pagodadev',	'object',	'routes',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "status": {"name": "status", "type": "select", "options": {"done": "Done", "open": "Open"}, "immutable": false}, "run_date": {"name": "Run Date", "type": "date", "immutable": false}, "isDeleted": {"name": "isDeleted", "type": "int", "immutable": false}, "waypoints": {"name": "Waypoints", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "assigned_to": {"name": "Assigned To", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "related_type": {"name": "Related Type", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-08-28 19:19:31.359777'),
(290,	'pagodadev',	'object',	'email_templates',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "body": {"name": "Body", "type": "string", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-04-07 12:25:53.256798'),
(267,	'pagodadev',	'object',	'user_settings',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "tables": {"name": "Tables", "type": "object", "immutable": true}, "userId": {"name": "User ID", "type": "objectId", "immutable": true, "object_type": "staff", "selectDisplay": "[fname] [lname]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.443227'),
(270,	'pagodadev',	'object',	'contact_info_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "notEmpty": true, "immutable": false}, "data_type": {"name": "Info Type", "type": "select", "options": {"date": "Date", "email": "Email", "other": "Other", "phone": "Phone", "address": "Address", "website": "Website/URL", "cellphone": "Cell Phone", "account_info": "Account Info"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_address": {"name": "Street Address?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.446767'),
(228,	'pagodadev',	'object',	'staff',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "dob": {"name": "Date of Birth", "type": "date", "immutable": false}, "pin": {"name": "PIN Number", "type": "string", "immutable": false}, "ssn": {"name": "Social Security #", "type": "string", "immutable": false}, "base": {"name": "Location", "type": "objectIds", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "user": {"name": "User Object", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "email": {"name": "Email", "type": "string", "immutable": false}, "fname": {"name": "First Name", "type": "string", "immutable": false}, "lname": {"name": "Last Name", "type": "string", "immutable": false}, "phone": {"name": "Phone", "type": "string", "immutable": false}, "parent": {"name": "Parent", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "status": {"name": "Employee Status", "type": "objectId", "immutable": false, "objectType": "staff_status", "selectDisplay": "[name]"}, "address": {"name": "Address", "type": "objectId", "immutable": false, "objectType": "contact_info", "selectDisplay": "[type]", "objectOverflow": true}, "enabled": {"name": "Enabled", "type": "int", "immutable": false}, "payroll": {"name": "Payroll", "type": "objectIds", "immutable": false, "objectType": "payroll", "selectDisplay": "[id]"}, "service": {"name": "Service", "type": "objectIds", "immutable": false, "objectType": "inventory_service", "selectDisplay": "[name]"}, "nickname": {"name": "Nickname", "type": "string", "immutable": false}, "password": {"name": "Password", "type": "string", "immutable": true}, "hire_date": {"name": "Date of Hire", "type": "date", "immutable": false}, "stripe_id": {"name": "Stripe ID", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "dependents": {"name": "Dependents", "type": "int", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "user_views": {"name": "User Views", "type": "objectIds", "immutable": true, "objectType": "user_views", "selectDisplay": "[name]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "garnishments": {"name": "Garnishment Amount", "type": "usd", "immutable": false}, "hours_worked": {"name": "Hours Worked", "type": "int", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "filing_status": {"name": "Filing Status", "type": "select", "options": {"single": "Single", "married": "Married", "single_rate": "Married (Single Rate)"}, "immutable": false}, "vacation_days": {"name": "Vacation Days", "type": "int", "immutable": false}, "daily_requests": {"name": "Daily Requests", "type": "int", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "work_week_start": {"name": "Work Week Start", "type": "select", "options": {"friday": "Friday", "monday": "Monday", "sunday": "Sunday", "tuesday": "Tuesday", "saturday": "Saturday", "thursday": "Thursday", "wednesday": "Wednesday"}, "immutable": false}, "company_hired_to": {"name": "Company Hired To", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true}, "termination_date": {"name": "Termination Date", "type": "date", "immutable": false}}',	'2017-03-04 17:31:51.400781'),
(57,	'pagodadev',	'object',	'beo_templates',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "categories": {"name": "Categories", "type": "objectIds", "immutable": false, "objectType": "inventory_billable_categories", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "breakout_into_sections": {"name": "Breakout into sections", "type": "int", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(262,	'pagodadev',	'object',	'inventory_billable_items',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "items": {"name": "Items", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": false}, "price": {"name": "Price Per Unit", "type": "usd", "immutable": false}, "units": {"name": "Units", "type": "int", "immutable": false}, "divisor": {"name": "Divisor", "type": "float", "immutable": false}, "multiplier": {"name": "Multiplier", "type": "float", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "objectId", "immutable": false}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.43724'),
(275,	'pagodadev',	'object',	'sms',	'{"id": {"name": "id", "type": "int", "immutable": true}, "type": {"name": "Type", "type": "string", "immutable": false}, "sentBy": {"name": "Sent By", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "sentTo": {"name": "Sent To", "type": "string", "immutable": false}, "typeId": {"name": "Type ID", "type": "int", "immutable": false}, "message": {"name": "Message", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.453016'),
(284,	'pagodadev',	'object',	'bank_account',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "bank_name": {"name": "Bank Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "account_number": {"name": "Account Number", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "routing_number": {"name": "Routing Number", "type": "string", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-03-10 14:56:25.78082'),
(269,	'pagodadev',	'object',	'inventory                          ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "cost": {"name": "Cost", "type": "usd", "immutable": false}, "type": {"name": "type", "type": "select", "options": {"bar": "Bar", "food": "Food", "decor": "Decor", "labor": "Labor", "rentals": "Rentals", "lighting": "Lighting", "security": "Security", "estimates": "Estimates", "venueFees": "Venue Fees", "descriptors": "Descriptors", "tentsHeatAir": "Tents/Heat/Air", "venueInventory": "Venue Inventory", "eventPlanningPackages": "Event Planning Packages"}, "immutable": false}, "price": {"name": "Price", "type": "usd", "immutable": false}, "active": {"name": "active", "type": "int", "immutable": true}, "details": {"name": "details", "type": "object", "immutable": false}, "item_id": {"name": "item_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.445549'),
(204,	'pagodadev',	'object',	'public_events                      ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "name", "type": "string", "immutable": false}, "notes": {"name": "notes", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "event_end_date": {"name": "event_end_date", "type": "string", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "event_start_date": {"name": "event_start_date", "type": "string", "immutable": false}}',	'2017-03-04 17:31:51.374648'),
(315,	'pagodadev',	'object',	'request_cancellations',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "reason": {"name": "Cancellation Reason", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-09-04 17:08:16.050012'),
(309,	'pagodadev',	'object',	'inventory_menu',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "date": {"name": "Date", "type": "date", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "select", "options": {"bid": "Bid", "quote": "Quote", "standard": "Standard Invoice", "event-menu": "Event Invoice", "bid-request": "Bid request"}, "immutable": false}, "venue": {"name": "Venue", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "active": {"name": "Active", "type": "select", "options": ["Yes", "No"], "immutable": false}, "vendor": {"name": "Vendor", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]"}, "budgets": {"name": "Budgets", "type": "list", "blueprint": {"type": {"name": "Budget Type", "type": "objectId", "immutable": false, "objectType": "menu_budget_types", "selectDisplay": "[name]"}, "amount": {"name": "Amount", "type": "usd", "immutable": false}, "description": {"name": "Description", "type": "string", "immutable": false}}, "immutable": false}, "related": {"name": "Related Object", "type": "int", "immutable": false}, "end_date": {"name": "End Date", "type": "date", "immutable": false}, "sections": {"name": "Sections", "type": "list", "blueprint": {"id": {"name": "id", "type": "int", "immutable": false}, "to": {"name": "End Time", "type": "date", "immutable": false}, "from": {"name": "Start Time", "type": "date", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "items": {"name": "Items", "type": "objectIds", "immutable": false, "objectType": "inventory_menu_line_items"}, "sortId": {"name": "Sort Id", "type": "int", "immutable": false}, "details": {"name": "Details", "type": "text", "immutable": false}}, "immutable": false}, "attachment": {"name": "Attachment", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "guest_count": {"name": "Guest Count", "type": "int", "immutable": false}, "date_created": {"name": "Last Updated", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "quote_status": {"name": "Quote status", "type": "select", "options": {"sent": "Sent", "draft": "Draft", "accepted": "Accepted", "declined": "Declined"}, "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "date_submitted": {"name": "Date submitted", "type": "date", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "bid_request_status": {"name": "Bid request status", "type": "select", "options": {"draft": "Draft", "closed": "Closed", "posted": "Posted", "cancelled": "Cancelled", "bid-selected": "Bid Selected"}, "immutable": false}}',	'2017-06-20 14:36:54.31303'),
(279,	'pagodadev',	'object',	'campaigns',	'{"id": {"name": "id", "type": "int", "immutable": true}, "file": {"name": "Files", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[file_name]"}, "name": {"name": "Campaign Name", "type": "string", "immutable": false}, "status": {"name": "Status", "type": "select", "options": {"active": "Active", "review": "Needs Review", "complete": "Complete", "scheduled": "Scheduled"}, "immutable": false}, "end_date": {"name": "End Date", "type": "date", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-10 12:59:55.696723'),
(50,	'pagodadev',	'object',	'discounts',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "item": {"name": "Item to apply to", "type": "int", "immutable": false}, "memo": {"name": "Memo", "type": "string", "immutable": false}, "menu": {"name": "Name", "type": "objectId", "immutable": false, "objectType": "inventory_menu", "selectDisplay": "[name]"}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "type", "type": "select", "options": {"amount_off": "Amount off", "percent_off": "Percent off", "replace_amount": "Replacement amount"}, "immutable": false}, "factor": {"name": "Factor", "type": "int", "immutable": false}, "apply_to": {"name": "apply_to", "type": "select", "options": {"category": "Category", "line_item": "Line Item", "workorder": "Workorder"}, "immutable": false}, "category": {"name": "Discount Category", "type": "objectId", "immutable": false, "objectType": "discount_categories"}, "categories": {"name": "Categories", "type": "objectIds", "immutable": false, "objectType": "inventory_billable_groups"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "after_taxes": {"name": "After taxes", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "copied_from": {"name": "Copied from", "type": "objectId", "immutable": true, "objectType": "discount_templates"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "discount_id": {"name": "discount_id", "type": "string", "immutable": true}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": true, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-10-05 21:46:50.920443'),
(255,	'pagodadev',	'object',	'event_staffing',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "staff": {"name": "Staff", "type": "object", "immutable": true}, "duties": {"name": "Duties", "type": "array", "immutable": true}, "approved": {"name": "Approved", "type": "int", "immutable": true}, "event_id": {"name": "Event", "type": "objectId", "immutable": true, "objectType": "events", "selectDisplay": "[event_name]"}, "confirmed": {"name": "Confirmed", "type": "array", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "assignments": {"name": "Assignments", "type": "string", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.429715'),
(287,	'pagodadev',	'object',	'cookies',	'{"id": {"name": "id", "type": "int", "immutable": true}, "uid": {"name": "uid", "type": "int", "immutable": false}, "token": {"name": "token", "type": "string", "immutable": false}, "series": {"name": "series", "type": "string", "immutable": false}, "user_type": {"name": "user_type", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "ip_address": {"name": "IP Address", "type": "string", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-27 15:48:51.596768'),
(260,	'pagodadev',	'object',	'invoice',	'{"id": {"name": "id", "type": "int", "immutable": true}, "memo": {"name": "Memo Line", "type": "string", "immutable": false}, "sent": {"name": "sent", "type": "string", "immutable": true}, "type": {"name": "Type", "type": "string", "immutable": false}, "items": {"name": "items", "type": "string", "immutable": true}, "amount": {"name": "Amount", "type": "usd", "immutable": false}, "locked": {"name": "locked", "type": "int", "immutable": true}, "dueDate": {"name": "Due Date", "type": "string", "immutable": false}, "eventId": {"name": "eventId", "type": "int", "immutable": true}, "approved": {"name": "approved", "type": "int", "immutable": true}, "payments": {"name": "payments", "type": "usd", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "oldDueDate": {"name": "oldDueDate", "type": "string", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.435041'),
(230,	'pagodadev',	'object',	'emails',	'{"id": {"name": "id", "type": "int", "immutable": true}, "to": {"name": "to", "type": "string", "immutable": false}, "from": {"name": "from", "type": "string", "immutable": false}, "read": {"name": "read", "type": "int", "immutable": false}, "sent": {"name": "sent", "type": "int", "immutable": false}, "type": {"name": "type", "type": "string", "immutable": false}, "opened": {"name": "opened", "type": "int", "immutable": false}, "bounced": {"name": "bounced", "type": "int", "immutable": false}, "clicked": {"name": "clicked", "type": "int", "immutable": false}, "message": {"name": "message", "type": "string", "immutable": false}, "subject": {"name": "subject", "type": "string", "immutable": false}, "type_id": {"name": "type_id", "type": "int", "immutable": false}, "thread_id": {"name": "thread_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "mandrill_id": {"name": "mandrill_id", "type": "string", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.402709'),
(35,	'pagodadev',	'object',	'inventory_recipe',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "uom": {"name": "Units of measure", "type": "object", "blueprint": {"config": {"name": "Configuration", "type": "object", "blueprint": {"weight": {"name": "Weight", "type": "int", "immutable": false}, "quantity": {"name": "Quantity", "type": "int", "immutable": false}, "dry_volume": {"name": "Dry volume", "type": "int", "immutable": false}, "liquid_volume": {"name": "Liquid volume", "type": "int", "immutable": false}, "volume_to_weight": {"name": "Volume to weight ratio", "type": "float", "immutable": false}}, "immutable": false}, "weight": {"name": "Weight", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "quantity": {"name": "Quantity", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "dry_volume": {"name": "Dry volume", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "liquid_volume": {"name": "Liquid volume", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}}, "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "image": {"name": "Image", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "file_name"}, "items": {"name": "Items", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": false}, "qty": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "name": {"name": "Name", "type": "string", "immutable": false}, "choices": {"name": "choices", "type": "list", "blueprint": {"qty": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "name": {"name": "Name", "type": "string", "immutable": false}, "selection": {"name": "Selection", "type": "object", "immutable": false}, "description": {"name": "Description", "type": "string", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "relatedObject", "immutable": false}, "additional_price": {"name": "Additional Price", "type": "usd", "immutable": false}}, "immutable": false}, "selection": {"name": "Selection", "type": "object", "immutable": false}, "max_selections": {"name": "Max Selections", "type": "int", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "relatedObject", "immutable": false}}, "immutable": false}, "category": {"name": "Category", "type": "objectId", "immutable": false, "objectType": "inventory_recipe_categories", "selectDisplay": "[name]"}, "selection": {"name": "Selection", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": false}, "item_yield": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "servings": {"name": "Servings", "type": "int", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(242,	'pagodadev',	'object',	'vendors',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "file": {"name": "File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[file_name]"}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectIds", "immutable": false, "objectType": "vendor_types", "selectDisplay": "[name]"}, "notes": {"name": "Notes", "type": "string", "immutable": false}, "markup": {"name": "Markup", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "invoice_amount": {"name": "Invoice Amount", "type": "usd", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.41642'),
(226,	'pagodadev',	'object',	'sms_message',	'{"id": {"name": "id", "type": "int", "immutable": true}, "to": {"name": "To", "type": "object", "immutable": true}, "sender": {"name": "Sender", "type": "int", "immutable": false}, "viewed": {"name": "Viewed", "type": "object", "immutable": true}, "message": {"name": "Message", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.398829'),
(39,	'pagodadev',	'object',	'company_logo',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_primary": {"name": "Primary", "type": "select", "options": {"no": "No", "yes": "Yes"}}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "company_logo": {"name": "Company logo", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_main_app_logo": {"name": "Main App Logo", "type": "select", "options": {"no": "No", "yes": "Yes"}}}',	'2017-10-05 21:46:50.920443'),
(298,	'pagodadev',	'object',	'incoming_form_source_vendor',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-06-03 12:12:12.775438'),
(318,	'pagodadev',	'object',	'note_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Category Name", "type": "string", "immutable": false}, "default": {"name": "Default", "type": "select", "options": {"no": "Not Default", "yes": "Default"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "updated_by": {"name": "Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "permissions": {"name": "Who can see these?", "type": "objectIds", "immutable": false, "objectType": "user_views", "selectDisplay": "[name]"}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-09-12 19:38:14.693358'),
(45,	'pagodadev',	'object',	'document_category',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(200,	'pagodadev',	'object',	'metrics                            ',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "file": {"name": "File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[file_name]"}, "type": {"name": "Type", "type": "objectId", "immutable": true, "objectType": "vendor_types", "selectDisplay": "[name]"}, "notes": {"name": "Notes", "type": "string", "immutable": false}, "event_id": {"name": "Event Id", "type": "objectId", "immutable": false, "objectType": "events"}, "vendor_id": {"name": "Vendor Id", "type": "objectId", "immutable": false, "objectType": "vendors", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "display_name": {"name": "Display Name", "type": "string", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "invoice_amount": {"name": "Invoice Amount", "type": "usd", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.37069'),
(239,	'pagodadev',	'object',	'fiscal_year',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "end": {"name": "End Date", "type": "date", "immutable": false, "displayFormat": "MMMM Do"}, "start": {"name": "Start Date", "type": "date", "immutable": false, "displayFormat": "MMMM Do"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.413172'),
(259,	'pagodadev',	'object',	'ingredients',	'{"id": {"name": "id", "type": "int", "immutable": true}, "cost": {"name": "Cost", "type": "usd", "immutable": false}, "type": {"name": "type", "type": "select", "options": {"bar": "Bar", "food": "Food", "decor": "Decor", "labor": "Labor", "rentals": "Rentals", "lighting": "Lighting", "security": "Security", "estimates": "Estimates", "venueFees": "Venue Fees", "descriptors": "Descriptors", "tentsHeatAir": "Tents/Heat/Air", "venueInventory": "Venue Inventory", "eventPlanningPackages": "Event Planning Packages"}, "immutable": false}, "price": {"name": "Price", "type": "usd", "immutable": false}, "active": {"name": "active", "type": "int", "immutable": true}, "details": {"name": "details", "type": "object", "immutable": false}, "item_id": {"name": "item_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.433856'),
(227,	'pagodadev',	'object',	'file_meta_data',	'{"id": {"name": "id", "type": "int", "immutable": true}, "oid": {"name": "oid", "type": "int", "immutable": false}, "parent": {"name": "parent", "type": "int", "immutable": false}, "oid_type": {"name": "oid_type", "type": "string", "immutable": false}, "file_name": {"name": "file_name", "type": "string", "immutable": false}, "file_type": {"name": "file_type", "type": "select", "options": {"other": "Other", "linens": "Linens", "av_sheet": "AV Request Sheet", "staffing": "Staffing", "floor-plan": "Floor Plan", "tour-notes": "Tour Notes", "client-files": "Client Files", "vendor-quotes": "Vendor Quotes", "50-iec-payment": "50% IEC Contract Payment", "initial_payment": "Initial Venue Payment", "rain_floor_plan": "Rain Floor Plan", "tax_exempt_form": "Tax exempt form", "final-iec-payment": "Final IEC Payment", "final-venue-payment": "Final Venue Payment", "signed-iic-contract": "Signed IEC Contract", "initial_meeting_sheet": "Initial Meeting Sheet", "event-planning-payment": "Event Planning Payment", "outside-vendor-payment": "Outside Vendor Payment", "signed-rental-contract": "Signed Rental Contract", "food-and-beverage-diagram": "Food & Beverage Diagram"}, "immutable": false}, "is_public": {"name": "is_public", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.399776'),
(317,	'pagodadev',	'object',	'menu_budget_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "categories": {"name": "Billable Categories Included", "type": "objectIds", "immutable": false, "objectType": "inventory_billable_categories", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "date_created": {"name": "Last Updated", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-09-12 07:35:35.29972'),
(319,	'pagodadev',	'object',	'packlist_checkpoints',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "order_id": {"name": "Order", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "checkpoints": {"name": "Checkpoints", "type": "list", "blueprint": {"uid": {"name": "Uid", "type": "int", "immutable": false}, "icon": {"name": "Icon", "type": "text", "immutable": false}, "name": {"name": "Name", "type": "text", "immutable": false}, "next": {"name": "Next states", "type": "object", "immutable": false}, "color": {"name": "Name", "type": "text", "immutable": false}, "previous": {"name": "Previous states", "type": "object", "immutable": false}, "isEntryPoint": {"name": "Is entry point", "type": "int", "immutable": false}}, "immutable": false}, "date_created": {"name": "Last Updated", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-09-18 21:02:44.290519'),
(209,	'pagodadev',	'object',	'restaurant_reports                 ',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "picture": {"name": "Picture", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "base_unit": {"name": "Base Measurement", "type": "objectId", "immutable": false, "objectType": "inventory_units", "selectDisplay": "[name]"}, "vendor_id": {"name": "Vendor", "type": "objectId", "immutable": false, "objectType": "vendors", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description/Model", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "measurements": {"name": "Units", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "is_base": {"name": "Is Base?", "type": "int", "immutable": true}, "multiplier": {"name": "Multiplier", "type": "float", "immutable": false}, "base_reference": {"name": "Base Unit", "type": "int", "immutable": false}}, "immutable": true}, "exclusive_tax": {"name": "Exclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "inclusive_tax": {"name": "Inclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectIds", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "inventory_categories": {"name": "Inventory Category", "type": "objectId", "immutable": false, "objectType": "inventory_categories", "selectDisplay": "[name]"}}',	'2017-03-04 17:31:51.379925'),
(31,	'pagodadev',	'object',	'payroll',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "rate": {"name": "Rate", "type": "usd", "immutable": false}, "staff": {"name": "Staff", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "service": {"name": "Service", "type": "objectId", "immutable": false, "objectType": "inventory_service", "selectDisplay": "[name]"}, "flat_rate": {"name": "Flat rate", "type": "usd", "immutable": false}, "pay_style": {"name": "Pay Style", "type": "select", "options": {"flat": "Flat", "hourly": "Hourly", "salary": "Salary", "flat_and_hourly": "Flat + Hourly"}, "immutable": false, "max_flat_hours": {"name": "Max Hours for Flat Rate", "type": "float", "immutable": false}}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname][lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "hourly_rate": {"name": "Hourly rate", "type": "usd", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "max_flat_hours": {"name": "Max flat hours", "type": "number", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname][lname]"}}',	'2017-10-05 21:46:50.920443'),
(291,	'pagodadev',	'object',	'wordlife_groups',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "send_time": {"name": "Daily Video Send Time", "type": "time", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "send_style": {"name": "Delivery Style", "type": "select", "options": {"sms": "Text Message", "email": "Email", "email-sms": "Email & Text Message"}, "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "group_owner": {"name": "Group Owner", "type": "objectId", "immutable": true, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-04-14 07:49:56.452899'),
(193,	'pagodadev',	'object',	'additional_contacts                ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "comm": {"name": "comm", "type": "int", "immutable": false}, "type": {"name": "type", "type": "string", "immutable": false}, "email": {"name": "email", "type": "string", "immutable": false}, "fname": {"name": "fname", "type": "string", "immutable": false}, "lname": {"name": "lname", "type": "string", "immutable": false}, "phone": {"name": "phone", "type": "string", "immutable": false}, "title": {"name": "title", "type": "string", "immutable": false}, "object_id": {"name": "object_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.363418'),
(217,	'pagodadev',	'object',	'event_staff_hours                  ',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "file": {"name": "File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[file_name]"}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectIds", "immutable": false, "objectType": "vendor_types", "selectDisplay": "[name]"}, "notes": {"name": "Notes", "type": "string", "immutable": false}, "markup": {"name": "Markup", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "invoice_amount": {"name": "Invoice Amount", "type": "usd", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.389613'),
(207,	'pagodadev',	'object',	'timeline_events                    ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "menu": {"name": "menu", "type": "object", "immutable": false}, "name": {"name": "name", "type": "string", "immutable": false}, "rooms": {"name": "rooms", "type": "object", "immutable": false}, "endTime": {"name": "endTime", "type": "string", "immutable": false}, "eventId": {"name": "eventId", "type": "int", "immutable": false}, "startTime": {"name": "startTime", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.37768'),
(250,	'pagodadev',	'object',	'clients',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "zip": {"name": "Zip", "type": "string", "immutable": false}, "city": {"name": "City", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectIds", "immutable": false, "objectType": "client_type", "selectDisplay": "[name]"}, "email": {"name": "Email", "type": "string", "immutable": false}, "fname": {"name": "First Name", "type": "string", "immutable": false}, "lname": {"name": "Last Name", "type": "string", "immutable": false}, "notes": {"name": "Notes", "type": "string", "immutable": false}, "phone": {"name": "Phone", "type": "string", "immutable": false}, "state": {"name": "State", "type": "string", "immutable": false}, "status": {"name": "Status", "type": "objectIds", "immutable": false, "objectType": "client_status", "selectDisplay": "[name]"}, "street": {"name": "Street", "type": "string", "immutable": false}, "company": {"name": "company", "type": "string", "immutable": false}, "country": {"name": "Country", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "guest_count": {"name": "Guest Count", "type": "string", "immutable": false}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "checks_allowed": {"name": "Checks Allowed", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "follow_up_date": {"name": "Follow Up Date", "type": "string", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "event_specialist": {"name": "Event Specialist", "type": "objectIds", "immutable": false, "objectType": "staff", "selectDisplay": "[fname][lname]"}, "sales_specialist": {"name": "Sales Specialist", "type": "objectIds", "immutable": false, "objectType": "staff", "selectDisplay": "[fname][lname]"}, "checks_allowed_note": {"name": "Checks Allowed Note", "type": "string", "immutable": false}, "display_date_created": {"name": "Display Date Created", "type": "string", "immutable": false}}',	'2017-03-04 17:31:51.424721'),
(56,	'pagodadev',	'object',	'payment_report',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "end_date": {"name": "End Date", "type": "date", "immutable": false}, "cycle_type": {"name": "Cycle Type", "type": "select", "options": {"weekly": "Weekly", "monthly": "Monthly", "bi_weekly": "Bi Weekly", "1st_and_15th": "1st and 15th", "15th_and_last_day": "15th and last day"}, "immutable": false}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "cycle_start": {"name": "Cycle Start", "type": "date", "immutable": false}, "total_price": {"name": "Total Price", "type": "usd", "immutable": false}, "fiscal_year_start": {"name": "Fiscal Year Start", "type": "date", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(195,	'pagodadev',	'object',	'event_staffing                     ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "staff": {"name": "staff", "type": "object", "immutable": false}, "duties": {"name": "duties", "type": "object", "immutable": false}, "approved": {"name": "approved", "type": "int", "immutable": false}, "event_id": {"name": "event_id", "type": "int", "immutable": false}, "confirmed": {"name": "confirmed", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "assignments": {"name": "assignments", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.365399'),
(197,	'pagodadev',	'object',	'event_wedding                      ',	'{"id": {"name": "id", "type": "int", "immutable": true}, "event_id": {"name": "event_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "bride_fname": {"name": "bride_fname", "type": "string", "immutable": false}, "bride_lname": {"name": "bride_lname", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "groom_fname": {"name": "groom_fname", "type": "string", "immutable": false}, "groom_lname": {"name": "groom_lname", "type": "string", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.367631'),
(254,	'pagodadev',	'object',	'holidays',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "month": {"name": "Month", "type": "multi-select", "options": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "immutable": false}, "style": {"name": "Style", "type": "select", "options": {"date": "Date", "day_of_week": "Day of Week"}, "immutable": false}, "endDate": {"name": "End Date", "type": "string", "immutable": false}, "frequency": {"name": "Frequency", "type": "select", "options": {"annual": "Annual", "weekly": "Weekly", "monthly": "Monthly", "one_time": "One Time"}, "immutable": false}, "startDate": {"name": "Start Date", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "daysOfWeek": {"name": "Days of the week", "type": "multi-select", "options": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.428726'),
(282,	'pagodadev',	'object',	'invoice_type',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "bank_account": {"name": "Bank Account", "type": "objectId", "immutable": false, "objectType": "bank_account", "selectDisplay": "[bank_name]"}, "company_logo": {"name": "Company logo", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "name"}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "invoice_type": {"name": "Name", "type": "string", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "stripe_payout_account": {"name": "Deposit Account", "type": "string", "immutable": false}}',	'2017-03-10 14:55:41.720645'),
(240,	'pagodadev',	'object',	'walkthroughs',	'{"id": {"name": "id", "type": "int", "immutable": true}, "zip": {"name": "zip", "type": "string", "immutable": false}, "city": {"name": "city", "type": "string", "immutable": false}, "state": {"name": "state", "type": "string", "immutable": false}, "venue": {"name": "venue", "type": "objectId", "immutable": false, "objectType": "staff_base"}, "street": {"name": "street", "type": "string", "immutable": false}, "client_id": {"name": "client_id", "type": "objectId", "immutable": false, "objectType": "clients"}, "meet_with": {"name": "meet_with", "type": "objectIds", "immutable": false, "objectType": "staff"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "isConfirmed": {"name": "isConfirmed", "type": "int", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "meeting_time": {"name": "meeting_time", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.414233'),
(310,	'pagodadev',	'object',	'item_reservation',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "item": {"name": "Item", "type": "int", "immutable": true}, "menu": {"name": "Menu", "type": "int", "immutable": false}, "type": {"name": "Type", "type": "select", "options": {"use": "Use", "rental": "Rental", "untracked": "Untracked"}, "immutable": false}, "items": {"name": "Items", "type": "list", "blueprint": {"qty": {"name": "Quantity (in base units)", "type": "float", "immutable": false}, "location": {"name": "Original location", "type": "int", "immutable": false}, "inventory_item": {"name": "Inventory Item", "type": "int", "immutable": false, "objectType": "inventory_items", "selectDisplay": "[received_date] - [serial_number]"}}, "immutable": false}, "units": {"name": "Units", "type": "int", "immutable": false}, "filled": {"name": "Filled", "type": "int", "immutable": false}, "stages": {"name": "Stages", "type": "object", "immutable": false}, "status": {"name": "Status", "type": "select", "options": {"at_venue": "At venue", "reserved": "Reserved", "in_transit": "In Transit", "could_not_reserve": "Could Not Reserve"}, "immutable": false}, "details": {"name": "Details", "type": "string", "immutable": false}, "related": {"name": "Related Object (event id)", "type": "relatedObject", "immutable": false}, "end_date": {"name": "End Date", "type": "date", "immutable": false}, "location": {"name": "Location", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "quantity": {"name": "Quantity", "type": "float", "immutable": false}, "is_active": {"name": "Is Active", "type": "int", "immutable": false}, "menu_item": {"name": "Menu Item", "type": "int", "immutable": false}, "picked_up": {"name": "Picked Up", "type": "boolean", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "ingredient": {"name": "Ingredient", "type": "int", "immutable": false}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "dropped_off": {"name": "Dropped Off", "type": "boolean", "immutable": false}, "date_created": {"name": "Last Updated", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "menu_section": {"name": "Menu Section", "type": "int", "immutable": false}, "picked_up_by": {"name": "Picked Up By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "date_reserved": {"name": "Date Reserved", "type": "date", "immutable": false}, "item_category": {"name": "Item Category", "type": "int", "immutable": false}, "dropped_off_by": {"name": "Dropped Off By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "picked_up_date": {"name": "Picked Up Timestamp", "type": "date", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "objectId", "immutable": false, "objectType": "inventory_groups", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "quantity_filled": {"name": "Quantity filled", "type": "float", "immutable": false}, "dropped_off_date": {"name": "Dropped Off Timestamp", "type": "date", "immutable": false}, "menu_item_choice": {"name": "Menu Item Choice", "type": "int", "immutable": false}, "current_checkpoint": {"name": "Current checkpoint", "type": "int", "immutable": false}, "inventory_item_ids": {"name": "Inventory Item Ids", "type": "object", "immutable": false}}',	'2017-06-20 14:37:30.079759'),
(218,	'pagodadev',	'object',	'personal_events                    ',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "cost": {"name": "Cost", "type": "usd", "immutable": false}, "units": {"name": "Units", "type": "int", "immutable": false}, "picture": {"name": "Picture", "type": "objectIds", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "location": {"name": "Location", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "quantity": {"name": "Quantity", "type": "int", "immutable": false}, "shelf_tag": {"name": "Shelf Tag", "type": "string", "immutable": false}, "vendor_id": {"name": "Vendor", "type": "objectId", "immutable": false, "objectType": "vendors", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "exclusive_tax": {"name": "Exclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "inclusive_tax": {"name": "Inclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "received_date": {"name": "Received Date", "type": "date", "immutable": false}, "serial_number": {"name": "Serial Number", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "expiration_date": {"name": "Expiration Date", "type": "date", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "objectId", "immutable": false, "objectType": "inventory_groups", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectIds", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-03-04 17:31:51.390577'),
(293,	'pagodadev',	'object',	'staff_schedules',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "event": {"name": "Event", "type": "objectId", "immutable": true, "objectType": "events", "selectDisplay": "[name]"}, "active": {"name": "Active", "type": "select", "options": ["Yes", "No"], "immutable": false}, "status": {"name": "Status", "type": "select", "options": {"late": "Late", "available": "Available", "completed": "Completed"}, "immutable": false}, "details": {"name": "Details", "type": "string", "immutable": false}, "location": {"name": "Location", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "recurring": {"name": "Recurring", "type": "select", "options": {"no": "No", "yes_forever": "Yes, forever", "yes_end_date": "Yes, with end date"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "is_template": {"name": "Template", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "page_params": {"name": "Page Params", "type": "string", "immutable": false}, "date_created": {"name": "date_created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "recurring_end_date": {"name": "Recurring End Date", "type": "date", "immutable": false}}',	'2017-05-10 10:38:47.67272'),
(59,	'pagodadev',	'object',	'task_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "states": {"name": "States", "type": "list", "blueprint": {"uid": {"name": "Uid", "type": "int", "immutable": false}, "icon": {"name": "Icon", "type": "text", "immutable": false}, "name": {"name": "Name", "type": "text", "immutable": false}, "next": {"name": "Next states", "type": "object", "immutable": false}, "type": {"name": "Type", "type": "text", "immutable": false}, "color": {"name": "Name", "type": "text", "immutable": false}, "previous": {"name": "Previous states", "type": "object", "immutable": false}, "isEntryPoint": {"name": "Is entry point", "type": "int", "immutable": false}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(297,	'pagodadev',	'object',	'incoming_form_sources',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "opens": {"name": "Opens", "type": "int", "immutable": false}, "vendor": {"name": "Source Vendor", "type": "objectId", "immutable": false, "objectType": "incoming_form_source_vendor", "selectDisplay": "[name]"}, "subject": {"name": "Subject Line", "type": "string", "immutable": false}, "redirect": {"name": "Redirect", "type": "string", "immutable": false}, "list_size": {"name": "List Size", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "email_html": {"name": "Email", "type": "string", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "banner_text": {"name": "Banner Text", "type": "string", "immutable": false}, "cookie_name": {"name": "Cookie Name", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "auto_approve": {"name": "Auto Approve", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "show_on_form": {"name": "Show on form?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "click_throughs": {"name": "Click Throughs", "type": "int", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-06-03 12:11:18.523772'),
(280,	'pagodadev',	'object',	'channels',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "cost": {"name": "Cost", "type": "usd", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "metrics": {"name": "Metrics", "type": "object", "immutable": false}, "payload": {"name": "Payload", "type": "object", "immutable": false}, "end_date": {"name": "End Date", "type": "date", "immutable": false}, "post_date": {"name": "Post Date", "type": "date", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "campaign_id": {"name": "Campaign Id", "type": "parentId", "immutable": false, "objectType": "campaign"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "channel_type": {"type": "select", "options": {"sms": "SMS Message", "email": "Email", "survey": "Survey", "twitter": "Twitter", "facebook": "Facebook", "instagram": "Instagram"}, "immutable": false, "objectType": "channel"}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-10 13:00:31.309376'),
(300,	'pagodadev',	'object',	'service_ticket_recommendations',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "recommendation": {"name": "Recommendation", "type": "string", "immutable": false}, "condition_codes": {"name": "Condition Codes", "type": "objectIds", "immutable": true, "objectType": "condition_codes", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-06-04 17:43:12.962503'),
(23,	'pagodadev',	'object',	'requests',	'{"id": {"name": "id", "type": "int", "immutable": true}, "zip": {"name": "Zip Code", "type": "string", "immutable": false}, "city": {"name": "City", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "project_types", "selectDisplay": "[name]"}, "books": {"name": "# of Books", "type": "int", "immutable": false}, "phone": {"name": "Phone", "type": "string", "immutable": false}, "state": {"name": "State", "type": "string", "immutable": false}, "adults": {"name": "Adults", "type": "int", "immutable": false}, "source": {"name": "Source", "type": "objectId", "immutable": false, "objectType": "incoming_form_sources", "selectDisplay": "[name]"}, "status": {"name": "Status", "type": "select", "options": ["Cancelled", "On Hold", "Awaiting Shipment", "Shipped"], "immutable": true}, "street": {"name": "Street", "type": "string", "immutable": false}, "carrier": {"name": "Carrier", "type": "string", "immutable": false}, "company": {"name": "Church", "type": "objectId", "immutable": true, "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true}, "contact": {"name": "Youth Leader", "type": "objectId", "immutable": true, "objectType": "contacts", "selectDisplay": "[fname] [lname]", "objectOverflow": true}, "country": {"name": "Country", "type": "select", "options": {"united_states": "United States"}, "immutable": false}, "manager": {"name": "Manager", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "shipped": {"name": "Shipped", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": true}, "students": {"name": "Students", "type": "int", "immutable": false}, "verified": {"name": "Verified?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "attention": {"name": "Attention", "type": "string", "immutable": false}, "form_data": {"name": "Form Data", "type": "objectId", "immutable": true, "objectType": "submitted_form", "selectDisplay": "", "objectOverflow": true}, "completion": {"name": "Completion", "type": "objectId", "immutable": true, "objectType": "request_completion_date", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "original_id": {"name": "Original ID", "type": "int", "immutable": true}, "book_version": {"name": "Book Version", "type": "select", "options": {"3010": "John", "3020": "Mark"}, "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "shipped_date": {"name": "Shipped Date", "type": "string", "immutable": true}, "business_name": {"name": "Business name", "type": "string", "immutable": false}, "delivery_date": {"name": "Delivery Date", "type": "date", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "delivery_status": {"name": "Delivery Status", "type": "select", "options": {"tracking": "Tracking", "delivered": "Delivered", "not_delivered": "Not Delivered"}, "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "tracking_number": {"name": "Tracking Number", "type": "string", "immutable": false}, "cancelled_reason": {"name": "Cancelled Reason", "type": "objectIds", "immutable": false, "objectType": "request_cancellations", "selectDisplay": "[reason]"}, "shipping_address": {"name": "Shipping Address", "type": "objectId", "immutable": false, "objectType": "contact_info", "selectDisplay": "[info]", "objectOverflow": true}, "tracking_numbers": {"name": "Tracking Numbers", "type": "object", "immutable": false}}',	'2017-12-19 21:53:07.348601'),
(311,	'pagodadev',	'object',	'csv_source',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-07-02 10:36:16.727101'),
(295,	'pagodadev',	'object',	'questions',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "ask": {"name": "Who do you want to ask?", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[lname], [fname]"}, "answer": {"name": "Answer", "type": "string", "immutable": true}, "status": {"name": "Status", "type": "select", "options": {"answered": "Answered", "not_answered": "Awaiting an Answer"}}, "actions": {"name": "Actions", "type": "list", "blueprint": {"button_color": {"name": "Button Color", "type": "string", "immutable": true}, "button_title": {"name": "Button Title", "type": "string", "immutable": true}, "callback_function": {"name": "Callback Function", "type": "string", "immutable": true}}}, "question": {"name": "Question", "type": "string", "immutable": false}, "questions": {"name": "Questions", "type": "objectIds", "immutable": true, "objectType": "questions", "selectDisplay": "[question]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "related_object": {"name": "Related Object", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "answer_needed_on": {"name": "date_created", "type": "date", "immutable": true}, "related_object_type": {"name": "Related Object Type", "type": "string", "immutable": true}}',	'2017-05-28 14:20:45.486972'),
(301,	'pagodadev',	'object',	'staff_paperwork',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "user": {"name": "User", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "staff": {"name": "Staff", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "fields": {"name": "Fields", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "document_file": {"name": "Document File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "document_type": {"name": "Document Type", "type": "objectId", "immutable": false, "objectType": "staff_paperwork_type", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "expiration_date": {"name": "Expiration Date", "type": "date", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-06-07 14:18:19.418828'),
(220,	'pagodadev',	'object',	'sys_docs',	'{"id": {"name": "id", "type": "int", "immutable": true}, "type": {"name": "Type", "type": "select", "options": {"ext": "Extension", "mod": "Module", "comp": "Component"}, "immutable": false}, "summary": {"name": "Summary", "type": "string", "immutable": false}, "sys_name": {"name": "System Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "notifications": {"name": "Notifications", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.392829'),
(308,	'pagodadev',	'object',	'payment_methods',	'{"id": {"name": "id", "type": "int", "immutable": true}, "token": {"name": "Payment Method Token", "type": "string", "immutable": false}, "default": {"name": "Default Payment Method", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "contact_id": {"name": "Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "method_type": {"name": "Payment Method Type", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-06-18 16:00:07.589299'),
(302,	'pagodadev',	'object',	'csv_upload',	'{"id": {"name": "id", "type": "int", "immutable": true}, "file": {"name": "File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "name": {"name": "Name", "type": "string", "immutable": false}, "rows": {"name": "Rows of Data", "type": "int", "immutable": false}, "source": {"name": "Source", "type": "objectId", "immutable": false, "objectType": "csv_source", "selectDisplay": "[name]"}, "status": {"name": "Status", "type": "select", "options": {"deleted": "Deleted", "started": "Started", "complete": "Complete", "not_started": "Not started"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "translation": {"name": "Translation", "type": "object", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "rows_transcribed": {"name": "Rows Transcribed", "type": "int", "immutable": false}, "rows_transcribed_and_related": {"name": "Rows Transcribed and Relations Set", "type": "int", "immutable": false}}',	'2017-06-08 15:07:27.945331'),
(215,	'pagodadev',	'object',	'system_tags',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "tag": {"name": "Tag", "type": "string", "immutable": false}, "type": {"name": "Object Type", "type": "string", "immutable": false}, "color": {"name": "Color", "type": "string", "immutable": false}, "objects": {"name": "Tagged Objects", "type": "object", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.387212'),
(203,	'pagodadev',	'object',	'payments',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "amount": {"name": "amount", "type": "usd", "immutable": false}, "details": {"name": "details", "type": "object", "immutable": false}, "invoice": {"name": "Invoice", "type": "objectId", "immutable": false, "objectType": "invoices", "selectDisplay": "[name]"}, "vendor_id": {"name": "Vendor Id", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "related_object": {"name": "Related Object", "type": "int", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "stripe_payment_id": {"name": "Stripe Payment ID", "type": "string", "immutable": true}}',	'2017-03-04 17:31:51.373708'),
(210,	'pagodadev',	'object',	'invoices',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "logo": {"name": "Logo", "type": "objectId", "immutable": false, "objectType": "company_logo", "selectDisplay": "[name]"}, "memo": {"name": "Memo", "type": "string", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "paid": {"name": "Paid", "type": "usd", "immutable": true}, "sent": {"name": "Sent", "type": "string", "immutable": true}, "type": {"name": "Type", "type": "string", "immutable": true}, "items": {"name": "Line Items", "type": "list", "blueprint": {"name": {"name": "Name", "type": "string", "immutable": false}, "amount": {"name": "Amount", "type": "usd", "immutable": false}, "quantity": {"name": "Quantity", "type": "int", "immutable": false}, "tax_rate": {"name": "Tax Rate", "type": "float", "immutable": false}}, "immutable": false}, "active": {"name": "Active", "type": "select", "options": ["Yes", "No"], "immutable": false}, "amount": {"name": "Amount", "type": "usd", "immutable": false}, "balance": {"name": "Balance", "type": "usd", "immutable": true}, "id_hash": {"name": "ID Hash", "type": "string", "immutable": true}, "sent_by": {"name": "Sent By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "sent_on": {"name": "Sent On", "type": "date", "immutable": true}, "type_id": {"name": "Invoice Type ID", "type": "objectId", "immutable": false, "objectType": "invoice_type", "selectDisplay": "[invoice_type]"}, "due_date": {"name": "Due Date", "type": "date", "immutable": false}, "payments": {"name": "Payments", "type": "objectIds", "immutable": true, "objectType": "payments", "selectDisplay": "[date_created] - [amount]"}, "tax_rate": {"name": "Tax Rate", "type": "float", "immutable": false}, "template": {"name": "Template", "type": "object", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "related_object": {"name": "Related Object", "type": "relatedObject", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "invoice_type_list": {"name": "Invoice Type", "type": "objectId", "immutable": false, "objectType": "invoice_type", "selectDisplay": "[invoice_type]"}}',	'2017-03-04 17:31:51.381255'),
(234,	'pagodadev',	'object',	'event_status',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.407398'),
(288,	'pagodadev',	'object',	'video_content',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "verse": {"name": "Verse", "type": "string", "immutable": false}, "video_id": {"name": "Video ID", "type": "string", "immutable": false}, "reference": {"name": "Reference", "type": "string", "immutable": false}, "view_date": {"name": "View Date", "type": "date", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "series_name": {"name": "Series Name", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-29 14:22:18.637334'),
(244,	'pagodadev',	'object',	'time_off_request',	'{"id": {"name": "id", "type": "int", "immutable": true}, "type": {"name": "Type", "type": "string", "immutable": false}, "reason": {"name": "Reason", "type": "string", "immutable": false}, "status": {"name": "Status", "type": "string", "immutable": false}, "endDate": {"name": "End Date", "type": "string", "immutable": false}, "staffId": {"name": "Staff ID", "type": "int", "immutable": true}, "startDate": {"name": "Start Date", "type": "string", "immutable": false}, "totalDays": {"name": "Total Days", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "statusChangedBy": {"name": "Updated By", "type": "int", "immutable": false}, "statusChangedOn": {"name": "Updated On", "type": "string", "immutable": false}}',	'2017-03-04 17:31:51.418464'),
(292,	'pagodadev',	'object',	'submitted_form',	'{"id": {"name": "id", "type": "int", "immutable": true}, "type": {"name": "Submission Type", "type": "string", "immutable": false}, "books": {"name": "Books", "type": "int", "immutable": false}, "email": {"name": "Email", "type": "string", "immutable": false}, "phone": {"name": "Phone", "type": "string", "immutable": false}, "students": {"name": "Students", "type": "int", "immutable": false}, "confirmed": {"name": "Confirmed", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "form_data": {"name": "Form Data", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "confirmation_code": {"name": "Confirmation Code", "type": "string", "immutable": false}}',	'2017-04-17 19:20:51.545577'),
(38,	'pagodadev',	'object',	'surcharges',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "note": {"name": "Note", "type": "string", "immutable": false}, "rate": {"name": "Surcharge Percentage", "type": "int", "immutable": false}, "default": {"name": "Default", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-10-05 21:46:50.920443'),
(252,	'pagodadev',	'object',	'permissions',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "edit": {"name": "Edit", "type": "object", "immutable": true}, "view": {"name": "View", "type": "object", "immutable": true}, "erase": {"name": "Erase", "type": "object", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "displayName": {"name": "Display Name", "type": "string", "immutable": true}, "staff_types": {"name": "Staff Types", "type": "objectIds", "immutable": true, "objectType": "staff_job_types", "selectDisplay": "[name]"}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "pageModuleId": {"name": "Page Module ID", "type": "string", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.426812'),
(187,	'pagodadev',	'object',	'youth_leaders',	'{"id": {"name": "id", "type": "int", "immutable": true}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "contact_types", "selectDisplay": "[name]"}, "fname": {"name": "First Name", "type": "string", "immutable": false}, "lname": {"name": "Last Name", "type": "string", "immutable": false}, "church": {"name": "Church", "type": "objectId", "immutable": false, "objectType": "churches", "selectDisplay": "[name]"}, "manager": {"name": "Manager", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "child_ids": {"name": "Children", "type": "string", "immutable": true}, "parent_id": {"name": "Parent", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "original_id": {"name": "Original ID", "type": "int", "immutable": true}, "contact_info": {"name": "Contact Info", "type": "objectIds", "immutable": true, "objectType": "contact_info", "selectDisplay": "[info]"}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.354586'),
(214,	'pagodadev',	'object',	'jobTypes                           ',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "file": {"name": "File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[file_name]"}, "name": {"name": "Name Of Expense", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "receipt_types", "selectDisplay": "[name]"}, "notes": {"name": "Notes", "type": "string", "immutable": false}, "amount": {"name": "Amount", "type": "usd", "immutable": false}, "status": {"name": "Status", "type": "select", "options": {"denied": "Denied", "approved": "Approved", "submitted": "Submitted"}, "immutable": true}, "staff_id": {"name": "Staff Id", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_id": {"name": "Object Id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "object_type": {"name": "Object Type", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "date_of_expense": {"name": "Date Of Expense", "type": "date", "immutable": false, "displayFormat": "l"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.386152'),
(265,	'pagodadev',	'object',	'receipts',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "file": {"name": "File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[file_name]"}, "name": {"name": "Name Of Expense", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "receipt_types", "selectDisplay": "[name]"}, "notes": {"name": "Notes", "type": "string", "immutable": false}, "amount": {"name": "Amount", "type": "usd", "immutable": false}, "status": {"name": "Status", "type": "select", "options": {"denied": "Denied", "approved": "Approved", "submitted": "Submitted"}, "immutable": true}, "staff_id": {"name": "Staff Id", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_id": {"name": "Object Id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "object_type": {"name": "Object Type", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "date_of_expense": {"name": "Date Of Expense", "type": "date", "immutable": false, "displayFormat": "l"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.440851'),
(257,	'pagodadev',	'object',	'event_vendor',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "file": {"name": "File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[file_name]"}, "type": {"name": "Type", "type": "objectId", "immutable": true, "objectType": "vendor_types", "selectDisplay": "[name]"}, "notes": {"name": "Notes", "type": "string", "immutable": false}, "event_id": {"name": "Event Id", "type": "objectId", "immutable": false, "objectType": "events"}, "vendor_id": {"name": "Vendor Id", "type": "objectId", "immutable": false, "objectType": "vendors", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "display_name": {"name": "Display Name", "type": "string", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "invoice_amount": {"name": "Invoice Amount", "type": "usd", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.431795'),
(256,	'pagodadev',	'object',	'inventory_billable_categories',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "tax_rates": {"name": "Tax Rates", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "surcharges": {"name": "Surcharges", "type": "objectIds", "immutable": false, "objectType": "surcharges", "selectDisplay": "[name]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": true, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "default_tax_rate": {"name": "Default Tax Rate", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "default_pricing_option": {"name": "Default pricing option", "type": "select", "options": {"price": "Price per unit", "price_per_hour": "Price per hour", "price_per_person": "Price per person", "price_per_hour_per_person": "Price per hour per person"}, "immutable": false}}',	'2017-03-04 17:31:51.43075'),
(7,	'pagodadev',	'object',	'tracker_item_type',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "datum_properties": {"name": "Datum properties", "type": "list", "blueprint": {"name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "string", "immutable": false}, "unit_id": {"name": "Unit", "type": "objectId", "immutable": false, "objectType": "inventory_units", "selectDisplay": "[name]"}}, "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(313,	'pagodadev',	'object',	'staff_paperwork_type',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "fields": {"name": "Extra Fields", "type": "list", "blueprint": {"name": {"name": "Name", "type": "string", "immutable": false}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "service_type": {"name": "Service Types", "type": "objectIds", "immutable": false, "objectType": "inventory_service", "selectDisplay": "[name]"}, "file_download": {"name": "File Download", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "need_exp_date": {"name": "Need Exp Date", "type": "select", "options": {"No": "No", "Yes": "Yes"}, "immutable": false}, "not_frequency": {"name": "Notification Frequency", "type": "int", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "need_file_upload": {"name": "Need Upload", "type": "select", "options": {"No": "No", "Yes": "Yes"}, "immutable": false}, "need_file_download": {"name": "Need Download", "type": "select", "options": {"No": "No", "Yes": "Yes"}, "immutable": false}}',	'2017-07-18 13:57:48.742427'),
(28,	'pagodadev',	'object',	'tasks2',	'{"id": {"name": "id", "type": "int", "immutable": true}, "oid": {"name": "oid", "type": "int", "immutable": false}, "title": {"name": "Title", "type": "string", "immutable": false}, "author": {"name": "author", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "status": {"name": "Status", "type": "int", "immutable": false}, "details": {"name": "Details", "type": "string", "immutable": false}, "assignee": {"name": "Assigned to", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "due_date": {"name": "Due Date", "type": "date", "immutable": false}, "task_type": {"name": "Type", "type": "select", "options": ["Task", "Meeting"], "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "object_type": {"name": "object_type", "type": "string", "immutable": false}, "page_params": {"name": "Page Params", "type": "string", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(268,	'pagodadev',	'object',	'contracts',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "sent": {"name": "Sent", "type": "int", "immutable": false}, "active": {"name": "Active", "type": "select", "options": ["Yes", "No"], "immutable": false}, "status": {"name": "Status", "type": "select", "options": ["Unsigned", "Out For Signature", "Signing In Process", "Signed"], "immutable": false}, "sent_by": {"name": "Sent By", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "sent_on": {"name": "Sent On", "type": "date", "immutable": false}, "signer_ip": {"name": "Signer IP", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": false}, "signatures": {"name": "Signature Files", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "html_string": {"name": "Contract", "type": "string", "immutable": false}, "object_type": {"name": "Object Type", "type": "string", "immutable": false}, "signer_name": {"name": "Signer Name", "type": "text", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "signer_email": {"name": "Signer Email", "type": "text", "immutable": true}, "signed_copies": {"name": "Signed Copies", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "contract_types": {"name": "Contract Type", "type": "objectId", "immutable": false, "objectType": "contract_types", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "related_object": {"name": "Related Object", "type": "relatedObject", "immutable": true}, "restore_object": {"name": "Restore Object", "type": "text", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.444338'),
(6,	'pagodadev',	'object',	'mandrill_webhooks',	'{"id": {"name": "id", "type": "int", "immutable": true}, "data": {"name": "data", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "webhook_json": {"name": "Webhook JSON", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(4,	'pagodadev',	'object',	'company_categories',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "notEmpty": true, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "available_types": {"name": "Available Types", "type": "objectIds", "immutable": false, "objectType": "contact_info_types", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-21 09:46:41.561105'),
(48,	'pagodadev',	'object',	'discount_categories',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "categories": {"name": "Categories", "type": "objectIds", "immutable": false, "objectType": "inventory_billable_groups"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": true, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-10-05 21:46:50.920443'),
(5,	'pagodadev',	'object',	'rules',	'null',	'2017-10-05 21:46:50.920443'),
(3,	'pagodadev',	'object',	'contract_system',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "request_email": {"name": "Signture Request Email Template", "type": "text", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "signature_disclaimer": {"name": "Signature Disclaimer", "type": "text", "immutable": false}, "request_email_subject": {"name": "Signture Request Email Subject Template", "type": "text", "immutable": false}}',	'2017-10-16 19:20:05.304778'),
(249,	'pagodadev',	'object',	'tax_rates',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "note": {"name": "Note", "type": "string", "immutable": false}, "rate": {"name": "Rate Percentage", "type": "int", "immutable": false}, "type": {"name": "Type", "type": "select", "options": {"exclusive": "Exclusive", "inclusive": "Inclusive"}, "immutable": false}, "default": {"name": "Default", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "tax_exempt": {"name": "Apply when contact is tax exempt?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-03-04 17:31:51.423191'),
(281,	'pagodadev',	'object',	'payment_schedule_template',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "templates": {"name": "Templates", "type": "list", "blueprint": {"name": {"name": "Name", "type": "string", "immutable": false}, "due_date": {"name": "Due Date", "type": "int", "immutable": false}, "flat_rate": {"name": "Flat Rate", "type": "usd", "immutable": false}, "before_after": {"name": "Before or After", "type": "select", "options": {"after": "After", "before": "Before"}, "immutable": false}, "invoice_type": {"name": "Invoice Type", "type": "objectId", "immutable": false, "objectType": "invoice_type", "selectDisplay": "[name]"}, "payment_type": {"name": "Payment Type", "type": "select", "options": {"flatRate": "Flat Rate", "percentOfTotal": "Percent of Total", "remainingBalance": "Remaining Balance"}, "immutable": false}, "percent_of_total": {"name": "Percent of Total", "type": "float", "immutable": false}, "before_after_type": {"name": "Before or After Type", "type": "select", "options": {"project": "Work Order/Project Date", "proposal": "Proposal Acceptance Date"}, "immutable": false}, "inventory_billable_categories": {"name": "Menu Item Category", "type": "objectIds", "immutable": false, "objectType": "inventory_billable_categories", "selectDisplay": "[name]"}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-10 14:55:01.8088'),
(253,	'pagodadev',	'object',	'staff_job_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "permissions": {"name": "View Permissions", "type": "objectIds", "immutable": false, "objectType": "permissions", "selectDisplay": "[displayName]"}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_accounts_options": {"name": "Chart Of Account", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name] - [account_id]"}}',	'2017-03-04 17:31:51.427758'),
(11,	'pagodadev',	'object',	'opportunity_status_type',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "color": {"name": "Color", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(8,	'pagodadev',	'object',	'tracker_datum',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "data": {"name": "Data", "type": "object", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "tracker_item_type", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(29,	'pagodadev',	'object',	'view_settings',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "user": {"name": "User", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "settings": {"name": "Settings", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(13,	'pagodadev',	'object',	'invoice_system',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "emails": {"name": "Emails", "type": "list", "blueprint": {"email": {"name": "Email Text", "type": "string", "immutable": false}, "active": {"name": "Active", "type": "string", "immutable": false}, "subject": {"name": "Subject", "type": "string", "immutable": false}, "email_type": {"name": "Email Type", "type": "string", "immutable": false}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "billing_address": {"name": "Billing Address", "type": "objectId", "immutable": true, "objectType": "contact_info", "selectDisplay": "[info]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(12,	'pagodadev',	'object',	'opportunity_type',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "contract_template": {"name": "Contract Template", "type": "objectId", "immutable": false, "objectType": "contract_types", "selectDisplay": "[name]"}}',	'2017-10-05 21:46:50.920443'),
(296,	'pagodadev',	'object',	'service_tickets',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "route": {"name": "Route", "type": "objectId", "immutable": false, "objectType": "routes", "selectDisplay": "[name]"}, "alerts": {"name": "Alerts", "type": "object", "immutable": false}, "status": {"name": "Status", "type": "select", "options": {"open": "Open", "paid": "Paid", "closing": "Closing", "servicing": "Servicing", "ready_to_service": "Ready To Service"}, "immutable": false}, "address": {"name": "Address", "type": "objectId", "immutable": false, "objectType": "contact_info", "selectDisplay": "[street] [city], [state] [zip]"}, "company": {"name": "Company", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]"}, "invoice": {"name": "Invoice", "type": "objectId", "immutable": false, "objectType": "invoices", "selectDisplay": "[id]"}, "contracts": {"name": "Contracts", "type": "objectIds", "immutable": true, "objectType": "object_contracts", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is Deleted", "type": "select", "options": ["No", "Yes"], "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "timer_start": {"name": "Timer Start", "type": "string", "immutable": false}, "timer_total": {"name": "Total TIme", "type": "int", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "service_date": {"name": "Service Date", "type": "date", "immutable": false}, "timer_running": {"name": "Timer Running", "type": "int", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "condition_codes": {"name": "Condition_codes", "type": "objectIds", "immutable": false, "objectType": "condition_codes", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "need_to_be_home": {"name": "Need to be home?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "sales_specialist": {"name": "Sales Specialist", "type": "objectIds", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "service_agreement": {"name": "Service Agreement", "type": "objectId", "immutable": false, "objectType": "service_agreements", "selectDisplay": ""}, "service_locations": {"name": "Service Locations", "type": "list", "blueprint": {"note": {"name": "Additional Note", "type": "string", "immutable": false}, "items": {"name": "Items", "type": "object", "immutable": false}, "strings": {"name": "Strings", "type": "object", "immutable": false}, "location": {"name": "Service Location", "type": "objectId", "immutable": false, "objectType": "pest_control_locations", "selectDisplay": "[name]"}, "chemicals": {"name": "Chemicals", "type": "objectIds", "immutable": false, "objectType": "pest_control_inventory", "selectDisplay": "[name]"}, "pesticide": {"name": "Pesticide", "type": "objectId", "immutable": false, "objectType": "billable_categories", "selectDisplay": "[name]"}, "sublocation": {"name": "Service Sub-Location", "type": "objectId", "immutable": false, "objectType": "pest_control_sub_locations", "selectDisplay": "[name]"}, "target_pest": {"name": "Target Pest", "type": "objectId", "immutable": false, "objectType": "target_pests", "selectDisplay": "[name]"}, "application_rate": {"name": "Application Rate", "type": "string", "immutable": false}, "application_methods": {"name": "Application Methods", "type": "object", "immutable": false}}}, "service_technician": {"name": "Service Technician", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[name] [lname]"}}',	'2017-06-01 12:50:43.064359'),
(14,	'pagodadev',	'object',	'target_pests',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Pest Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(299,	'pagodadev',	'object',	'condition_codes',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "recommendations": {"name": "Recommendations", "type": "objectIds", "immutable": false, "objectType": "service_ticket_recommendations", "selectDisplay": "[recommendation]"}}',	'2017-06-04 11:14:54.600947'),
(20,	'pagodadev',	'object',	'background_process',	'{"id": {"name": "id", "type": "int", "immutable": true}, "run": {"name": "Run", "type": "string", "immutable": false}, "type": {"name": "type", "type": "string", "immutable": false}, "status": {"name": "Status", "type": "select", "options": {"deleted": "Deleted", "started": "Started", "complete": "Complete", "not_started": "Not started"}}, "payload": {"name": "Payload", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(30,	'pagodadev',	'object',	'inventory_estimate_type',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "inventory_billable_category": {"name": "Product category", "type": "objectId", "immutable": false, "objectType": "inventory_billable_categories", "selectDisplay": "[name]"}}',	'2017-10-05 21:46:50.920443'),
(10,	'pagodadev',	'object',	'opportunities',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "value": {"name": "Value", "type": "int", "immutable": false}, "status": {"name": "Status", "type": "objectId", "immutable": false, "objectType": "opportunity_status_type"}, "contract": {"name": "Contract", "type": "objectId", "immutable": false, "objectType": "contracts"}, "end_date": {"name": "End Date", "type": "date", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "closing_date": {"name": "Closing Date", "type": "date", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "related_object": {"name": "Related Object", "type": "int", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "sales_specialist": {"name": "Sales Specialist", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(43,	'pagodadev',	'object',	'projects',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "project_types", "selectDisplay": "[name]"}, "state": {"name": "Status", "type": "int", "immutable": false}, "tools": {"name": "Tools", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": true}, "order": {"name": "Order", "type": "int", "immutable": false}, "added_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "added_on": {"name": "Date Created", "type": "string", "immutable": true}, "system_name": {"name": "System Name", "type": "string", "immutable": false}, "display_name": {"name": "Display Name", "type": "string", "immutable": false}, "is_archieved": {"name": "Is Archieved", "type": "int", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname][lname]"}, "allowed_users": {"name": "Allowed Users", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "potential_value": {"name": "Potential Value", "type": "int", "immutable": false}, "related_contacts": {"name": "Related contacts", "type": "objectIds", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname][lname]"}}, "immutable": false}, "users": {"name": "Users", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "status": {"name": "Status", "type": "select", "options": ["Proposal", "Active", "Accepted", "Signed", "Paid", "Complete"], "immutable": false, "objectType": "select"}, "end_date": {"name": "End date", "type": "date", "immutable": false}, "proposal": {"name": "Proposal", "type": "objectId", "immutable": true, "objectType": "proposals", "selectDisplay": "[name]"}, "locations": {"name": "Locations", "type": "objectIds", "immutable": false, "objectType": "staff_base", "selectDisplay": "[fname][lname]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname][lname]"}, "project_lead": {"name": "Project lead", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "allowed_users": {"name": "Allowed Users", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "potential_value": {"name": "Potential Value", "type": "int", "immutable": false}, "related_contacts": {"name": "Related contacts", "type": "objectIds", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname][lname]"}}',	'2017-10-05 21:46:50.920443'),
(264,	'pagodadev',	'object',	'time_entries',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "note": {"name": "Note", "type": "string", "immutable": false}, "tips": {"name": "Tips", "type": "usd", "immutable": false}, "shift": {"name": "Shift", "type": "objectId", "immutable": false, "objectType": "groups", "selectDisplay": "[id]"}, "staff": {"name": "Staff", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "service": {"name": "Service", "type": "objectId", "immutable": false, "objectType": "inventory_service", "selectDisplay": "[name]"}, "duration": {"name": "Duration", "type": "int", "immutable": false}, "end_date": {"name": "End Date", "type": "date", "immutable": false}, "location": {"name": "Location", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "flat_rate": {"name": "Flat Rate", "type": "usd", "immutable": false}, "is_salary": {"name": "Is salary", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "cycle_type": {"name": "Cycle Type", "type": "select", "options": {"weekly": "Weekly", "monthly": "Monthly", "bi_weekly": "Bi Weekly", "1st_and_15th": "1st and 15th", "15th_and_last_day": "15th and last day"}, "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "hourly_rate": {"name": "Hourly Rate", "type": "usd", "immutable": false}, "billing_type": {"name": "Billing Type", "type": "select", "options": {"flat": "Flat", "hourly": "Hourly", "salary": "Salary", "non_billable": "Non-Billable", "flat_and_hourly": "Flat + Hourly"}, "immutable": false}, "compensation": {"name": "Compensation", "type": "usd", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "can_be_edited": {"name": "Can be edited", "type": "boolean", "immutable": false}, "is_correction": {"name": "Is correction", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "max_flat_hours": {"name": "Max Hours for Flat Rate", "type": "float", "immutable": false}, "payment_report": {"name": "Payment Report", "type": "objectId", "immutable": false, "objectType": "payment_report", "selectDisplay": "[name]"}, "default_payroll": {"name": "Default Payroll", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.43975'),
(238,	'pagodadev',	'object',	'threads',	'{"id": {"name": "id", "type": "int", "immutable": true}, "type": {"name": "type", "type": "string", "immutable": false}, "listeners": {"name": "listeners", "type": "object", "immutable": false}, "object_id": {"name": "object_id", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "not_viewed": {"name": "Not Viewed", "type": "int", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "replied_to": {"name": "Replied to", "type": "int", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "object_type": {"name": "object_type", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.412109'),
(15,	'pagodadev',	'object',	'pest_control_locations',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Location Name (bedrooms, backyard, barn)", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "sublocations": {"name": "Sub Locations", "type": "objectIds", "immutable": false, "objectType": "pest_control_sub_locations", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(17,	'pagodadev',	'object',	'pest_control_sub_locations',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Location Name (sink, closet, floor boards...)", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(241,	'pagodadev',	'object',	'tasks',	'{"id": {"name": "id", "type": "int", "immutable": true}, "title": {"name": "Title", "type": "string", "immutable": false}, "author": {"name": "author", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "status": {"name": "Status", "type": "int", "immutable": false}, "details": {"name": "Details", "type": "string", "immutable": false}, "assignee": {"name": "Assigned to", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "due_date": {"name": "Due Date", "type": "date", "immutable": false}, "task_type": {"name": "Type", "type": "select", "options": {"task": "Task", "meeting": "Meeting"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "page_params": {"name": "Page Params", "type": "string", "immutable": false}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "related_object": {"name": "Object Id", "type": "objectId", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.415369'),
(18,	'pagodadev',	'object',	'pest_control_chemical_application_methods',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Application Method Name", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(16,	'pagodadev',	'object',	'pest_control_inventory',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Item Name", "type": "string", "immutable": false}, "price": {"name": "Price Per Unit", "type": "usd", "immutable": false}, "add_tax": {"name": "Add Tax", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "tax_rate": {"name": "Tax Rate", "type": "objectId", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "is_chemical": {"name": "Is this a chemical?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "objectId", "immutable": false, "objectType": "inventory_units", "selectDisplay": "[name]"}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "application_methods": {"name": "Application Methods", "type": "objectIds", "immutable": false, "objectType": "pest_control_chemical_application_methods", "selectDisplay": "[name]"}}',	'2017-10-05 21:46:50.920443'),
(19,	'pagodadev',	'object',	'lifebook_international_orders',	'{"id": {"name": "id", "type": "int", "immutable": true}, "amount": {"name": "Books", "type": "int", "immutable": false}, "status": {"name": "Status", "type": "select", "options": ["Not Delivered", "Delivered"], "immutable": false}, "contact": {"name": "notify", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "manager": {"name": "Manager", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "isDeleted": {"name": "isDeleted", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "order_date": {"name": "order_date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(304,	'pagodadev',	'object',	'service_agreement_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "price": {"name": "Price", "type": "usd", "immutable": false}, "contract": {"name": "Service Agreement Contract", "type": "objectId", "immutable": false, "objectType": "contracts", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is Deleted", "type": "select", "options": ["No", "Yes"], "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "estimated_service_time": {"name": "Estimated Service Time (minutes)", "type": "int", "immutable": false}, "payment_schedule_template": {"name": "Payment Schedule Template", "type": "objectId", "immutable": false, "objectType": "payment_schedule_template", "selectDisplay": "[name]"}}',	'2017-06-09 14:14:21.34569'),
(24,	'pagodadev',	'object',	'staff_availability_requests',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "staff": {"name": "Staff", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "reason": {"name": "Reason", "type": "string", "immutable": false}, "status": {"name": "Status", "type": "select", "options": {"denied": "Denied", "approved": "Approved", "processing": "Processing"}, "immutable": false}, "end_time": {"name": "End Time", "type": "date", "immutable": false}, "recurring": {"name": "Recurring", "type": "select", "options": {"no": "No", "yes": "Yes", "yes_end_date": "Yes, with end date"}, "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_time": {"name": "Start Time", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "request_type": {"name": "Request Type", "type": "select", "options": {"time_on": "Time On", "time_off": "Time Off"}, "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "related_object": {"name": "Object Id", "type": "objectId", "immutable": false, "objectType": "users"}, "used_sick_days": {"name": "Used vacation days", "type": "int", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "recurring_end_date": {"name": "Recurring End Date", "type": "date", "immutable": false}, "used_vacation_days": {"name": "Used vacation days", "type": "int", "immutable": false}, "recurring_week_days": {"name": "Recurring Week Days", "type": "select", "options": {"friday": "Friday", "monday": "Monday", "sunday": "Sunday", "tuesday": "Tuesday", "saturday": "Saturday", "thursday": "Thursday", "wednesday": "Wednesday"}, "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(21,	'pagodadev',	'object',	'life_book_system',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "new_order": {"name": "New Order Notification", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "new_web_form": {"name": "New Website Form Submission Notification", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(22,	'pagodadev',	'object',	'opportunity_goals',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Goal Type", "type": "select", "options": ["User", "Category"], "immutable": false}, "staff": {"name": "Staff", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "goal_value": {"name": "Goal Value", "type": "usd", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": false}, "time_frame": {"name": "Time Frame", "type": "select", "options": ["Weekly", "Monthly", "Quarterly", "Annually"], "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "total_count": {"name": "Total Count", "type": "int", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "opportunity_type": {"name": "Opportunity Type", "type": "objectIds", "immutable": false, "objectType": "opportunity_type", "selectDisplay": "[name]"}}',	'2017-10-05 21:46:50.920443'),
(208,	'pagodadev',	'object',	'object_tags',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "document": {"name": "Tags", "type": "objectIds", "immutable": true, "objectType": "system_tags", "selectDisplay": "[tag]"}, "object_id": {"name": "Object ID", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "object_type": {"name": "Object Type", "type": "string", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.378844'),
(272,	'pagodadev',	'object',	'contact_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "notEmpty": true, "immutable": false}, "states": {"name": "States", "type": "list", "blueprint": {"uid": {"name": "Uid", "type": "int", "immutable": false}, "icon": {"name": "Icon", "type": "text", "immutable": false}, "name": {"name": "Name", "type": "text", "immutable": false}, "next": {"name": "Next states", "type": "object", "immutable": false}, "color": {"name": "Name", "type": "text", "immutable": false}, "previous": {"name": "Previous states", "type": "object", "immutable": false}, "isEntryPoint": {"name": "Is entry point", "type": "int", "immutable": false}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "available_types": {"name": "Available Types", "type": "objectIds", "immutable": false, "objectType": "contact_info_types", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.449485'),
(274,	'pagodadev',	'object',	'inventory_groups',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "uom": {"name": "Units of measure", "type": "object", "blueprint": {"config": {"name": "Configuration", "type": "object", "blueprint": {"weight": {"name": "Weight", "type": "int", "immutable": false}, "quantity": {"name": "Quantity", "type": "int", "immutable": false}, "dry_volume": {"name": "Dry volume", "type": "int", "immutable": false}, "liquid_volume": {"name": "Liquid volume", "type": "int", "immutable": false}, "volume_to_weight": {"name": "Volume to weight ratio", "type": "float", "immutable": false}}, "immutable": false}, "weight": {"name": "Weight", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "quantity": {"name": "Quantity", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "dry_volume": {"name": "Dry volume", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "liquid_volume": {"name": "Liquid volume", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}}, "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "picture": {"name": "Picture", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "base_unit": {"name": "Base Measurement", "type": "objectId", "immutable": false, "objectType": "inventory_units", "selectDisplay": "[name]"}, "vendor_id": {"name": "Vendor", "type": "objectIds", "immutable": false, "objectType": "vendors", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "stock_type": {"name": "Is Perishable?", "type": "select", "options": {"untracked": "Do not track", "perishable": "Perishable items", "not_perishable": "Not perishable items"}, "immutable": false}, "surcharges": {"name": "Surcharges", "type": "objectIds", "immutable": false, "objectType": "surcharges", "selectDisplay": "[name]"}, "current_qty": {"name": "Current Quantity", "type": "float", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description/Model", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "measurements": {"name": "Units", "type": "int", "immutable": false}, "exclusive_tax": {"name": "Exclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "inclusive_tax": {"name": "Inclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "inventory_categories": {"name": "Inventory Category", "type": "objectId", "immutable": false, "objectType": "inventory_categories", "selectDisplay": "[name]"}}',	'2017-03-04 17:31:51.451721'),
(235,	'pagodadev',	'object',	'event_type_options',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "notEmpty": true, "immutable": false}, "guests": {"name": "Does this have guests?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "email_template_body": {"name": "Email Template Body", "type": "string", "immutable": true}, "email_template_subject": {"name": "Email Template Subject", "type": "string", "immutable": true}}',	'2017-03-04 17:31:51.408522'),
(27,	'pagodadev',	'object',	'external_forms',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "title": {"name": "Title", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "fieldOrder": {"name": "Field Order", "type": "object", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "send_email": {"name": "Send Email", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "create_task": {"name": "Info Type", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "fieldGroups": {"name": "Field Groups", "type": "object", "immutable": false}, "submissions": {"name": "Submissions", "type": "int", "immutable": false}, "contact_type": {"name": "Contact Type", "type": "objectId", "immutable": false, "objectType": "contact_types", "selectDisplay": "[name]"}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "submitButton": {"name": "Submit Button", "type": "object", "immutable": false}, "task_due_date": {"name": "Task Due Date", "type": "int", "immutable": false}, "usersToNotify": {"name": "Users To Notify", "type": "objectIds", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "send_email_body": {"name": "Send Email Body", "type": "string", "immutable": false}, "send_email_field": {"name": "Send Email Field", "type": "string", "immutable": false}, "send_email_subject": {"name": "Send Email Subject", "type": "string", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(303,	'pagodadev',	'object',	'service_agreements',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "route": {"name": "Route", "type": "objectId", "immutable": false, "objectType": "routes", "selectDisplay": "[name] [lname]"}, "status": {"name": "Status", "type": "select", "options": {"live": "Active", "expired": "Expired"}, "immutable": false}, "address": {"name": "Service Address", "type": "objectId", "immutable": false, "objectType": "contact_info", "selectDisplay": "[info]", "objectOverflow": true}, "company": {"name": "Company", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]"}, "contracts": {"name": "Contract", "type": "objectIds", "immutable": false, "objectType": "contracts", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is Deleted", "type": "select", "options": ["No", "Yes"], "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "service_day": {"name": "Service Days", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_service": {"name": "Last Service", "type": "objectId", "immutable": false, "objectType": "service_tickets", "selectDisplay": "[service_date]"}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "next_service": {"name": "Next Service", "type": "objectId", "immutable": false, "objectType": "service_tickets", "selectDisplay": "[service_date]"}, "service_time": {"name": "Service Time", "type": "text", "immutable": false}, "service_week": {"name": "Service Weeks", "type": "object", "immutable": false}, "discount_type": {"name": "Discount Type", "type": "select", "options": {"dollar": "Dollar", "percent": "Percent"}, "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_scheduled": {"name": "Last Scheduled", "type": "date", "immutable": false}, "related_object": {"name": "Related Object Type", "type": "int", "immutable": false}, "discount_amount": {"name": "Discount Amount", "type": "text", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "need_to_be_home": {"name": "Need to be home?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "sales_specialist": {"name": "Sales Specialist", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "last_service_date": {"name": "Last Service Date", "type": "date", "immutable": false}, "service_frequency": {"name": "Service frequency", "type": "select", "options": {"day": "Daily", "week": "Weekly", "year": "Annually", "month": "Monthly", "bi_year": "Bi-Annually", "quarter": "Quarterly", "bi_month": "Bi-Monthly"}, "immutable": false}, "service_technician": {"name": "Service Technician", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[name] [lname]"}, "service_agreement_type": {"name": "Service Agreement Type", "type": "objectId", "immutable": false, "objectType": "service_agreement_types", "selectDisplay": ""}}',	'2017-06-09 14:11:35.354357'),
(306,	'pagodadev',	'object',	'menu_template',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "select", "options": {"standard": "Standard Invoice", "event-menu": "Event Invoice"}, "immutable": false}, "sections": {"name": "Sections", "type": "list", "blueprint": {"id": {"name": "id", "type": "int", "immutable": false}, "to": {"name": "End Time", "type": "time", "immutable": false}, "from": {"name": "Start Time", "type": "time", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "items": {"name": "Items", "type": "list", "blueprint": {"id": {"name": "id", "type": "int", "immutable": false}, "qty": {"name": "Quantity", "type": "int", "immutable": false}, "item": {"name": "Item", "type": "objectId", "immutable": false, "objectType": "inventory_billable_groups", "selectDisplay": "[name]"}, "sortId": {"name": "Sort Id", "type": "int", "immutable": false}, "choices": {"name": "Choices", "type": "object", "blueprint": {"item": {"name": "Item", "type": "int", "immutable": false}, "choice": {"name": "Choice", "type": "object", "immutable": false}}, "immutable": false}}, "immutable": false}, "sortId": {"name": "Sort Id", "type": "int", "immutable": false}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "guest_count": {"name": "Guest Count", "type": "int", "immutable": false}, "date_created": {"name": "Last Updated", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-06-16 10:16:24.320627'),
(294,	'pagodadev',	'object',	'shifts',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "staff": {"name": "Staff", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[lname], [fname]"}, "state": {"name": "State", "type": "select", "options": {"notified": "Notified", "scheduled": "Scheduled", "unscheduled": "Unscheduled"}, "immutable": false}, "details": {"name": "Details", "type": "string", "immutable": false}, "service": {"name": "Service", "type": "objectId", "immutable": false, "objectType": "inventory_service", "selectDisplay": "[name]"}, "end_time": {"name": "End Time", "type": "date", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_time": {"name": "Start Time", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "date_created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "staff_schedules": {"name": "Staff Schedules", "type": "objectId", "immutable": false, "objectType": "staff_schedules", "selectDisplay": "[name]"}, "can_be_reassigned": {"name": "Can be reassigned", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}}',	'2017-05-10 10:39:30.711885'),
(225,	'pagodadev',	'object',	'user_views',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "User Type", "type": "select", "options": {"staff": "Staff", "client": "Client"}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "menu_items": {"name": "Menu Items", "type": "object", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "permissions": {"name": "Menu Permissions", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.397833'),
(52,	'pagodadev',	'object',	'group_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "states": {"name": "States", "type": "list", "blueprint": {"uid": {"name": "Uid", "type": "int", "immutable": false}, "icon": {"name": "Icon", "type": "text", "immutable": false}, "name": {"name": "Name", "type": "text", "immutable": false}, "next": {"name": "Next states", "type": "object", "immutable": false}, "color": {"name": "Name", "type": "text", "immutable": false}, "previous": {"name": "Previous states", "type": "object", "immutable": false}, "isEntryPoint": {"name": "Is entry point", "type": "int", "immutable": false}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(49,	'pagodadev',	'object',	'discount_templates',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "item": {"name": "Product ID to apply to", "type": "int", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "type", "type": "select", "options": {"amount_off": "Amount off", "percent_off": "Percent off", "replace_amount": "Replacement amount"}, "immutable": false}, "factor": {"name": "Factor", "type": "int", "immutable": false}, "apply_to": {"name": "apply_to", "type": "select", "options": {"category": "Category", "workorder": "Workorder"}, "immutable": false}, "category": {"name": "Discount Category", "type": "objectId", "immutable": false, "objectType": "discount_categories"}, "categories": {"name": "Categories", "type": "objectIds", "immutable": false, "objectType": "inventory_billable_groups"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "discount_id": {"name": "discount_id", "type": "string", "immutable": true}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": true, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-10-05 21:46:50.920443'),
(9,	'pagodadev',	'object',	'staff_availability',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "staff": {"name": "Staff", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "reason": {"name": "Reason", "type": "string", "immutable": false}, "end_time": {"name": "End Time", "type": "date", "immutable": false}, "recurring": {"name": "Recurring", "type": "select", "options": {"no": "No", "yes_forever": "Yes, forever", "yes_end_date": "Yes, with end date"}, "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_time": {"name": "Start Time", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "day_of_week": {"name": "Day Of Week", "type": "select", "options": {"Fri": "Fri", "Mon": "Mon", "Sat": "Sat", "Sun": "Sun", "Thu": "Thu", "Tue": "Tue", "Wed": "Wed"}, "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "request_type": {"name": "Request Type", "type": "select", "options": {"request_to_work": "Request To Work", "request_time_off": "Request Time Off"}, "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(26,	'pagodadev',	'object',	'chart_of_accounts_companies',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "child_ids": {"name": "Children", "type": "string", "immutable": true}, "parent_id": {"name": "Parent", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(286,	'pagodadev',	'object',	'inventory_service',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "rate": {"name": "Rate ($/hr)", "type": "usd", "immutable": false}, "price": {"name": "Price", "type": "usd", "immutable": false}, "min_hours": {"name": "Min Hours", "type": "float", "immutable": false}, "vendor_id": {"name": "Vendor", "type": "objectId", "immutable": false, "objectType": "vendors", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "permission": {"name": "Permission", "type": "objectId", "immutable": false, "objectType": "user_views", "selectDisplay": "[name]"}, "price_type": {"name": "Billing Type", "type": "select", "options": {"flat": "Flat", "hourly": "Hourly", "non_billable": "Non-Billable", "flat_and_hourly": "Flat + Hourly"}, "immutable": false}, "surcharges": {"name": "Surcharges", "type": "objectIds", "immutable": false, "objectType": "surcharges", "selectDisplay": "[name]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description/Model", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "can_be_billed": {"name": "Billing Type", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "exclusive_tax": {"name": "Exclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "inclusive_tax": {"name": "Inclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "max_flat_hours": {"name": "Max Hours for Flat Rate", "type": "float", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "manager_locations": {"name": "Manager Locations", "type": "objectIds", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "inventory_categories": {"name": "Inventory Category", "type": "objectId", "immutable": false, "objectType": "inventory_categories", "selectDisplay": "[name]"}}',	'2017-03-15 15:48:42.28155'),
(25,	'pagodadev',	'object',	'work_orders',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "event_type_options", "selectDisplay": "[name]"}, "price": {"name": "Price", "type": "int", "immutable": false}, "venue": {"name": "Venue", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "status": {"name": "Status", "type": "select", "options": ["Prospecting", "Approval", "Approved", "Proposal", "Approved", "Sent", "Accepted", "Signed", "Paid", "Complete"], "immutable": false, "objectType": "select"}, "balance": {"name": "Balance", "type": "int", "immutable": false}, "company": {"name": "Company", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]"}, "history": {"name": "History", "type": "object", "immutable": false}, "manager": {"name": "Manager", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "vendors": {"name": "Vendors", "type": "objectId", "immutable": false, "objectType": "event_vendors", "selectDisplay": "[name]"}, "contract": {"name": "Contracts", "type": "objectId", "immutable": false, "objectType": "contracts", "selectDisplay": "[name]"}, "end_date": {"name": "Date", "type": "date", "immutable": false}, "proposal": {"name": "Main Proposal", "type": "objectId", "immutable": false, "objectType": "proposals", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is Deleted", "type": "select", "options": ["No", "Yes"], "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Date", "type": "date", "immutable": false}, "amount_paid": {"name": "Amount Paid", "type": "int", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "guest_count": {"name": "Guest count", "type": "number", "immutable": false}, "is_template": {"name": "Is template?", "type": "int", "immutable": false}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "follow_up_date": {"name": "Follow-Up Date", "type": "date", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "potential_value": {"name": "Potential Value", "type": "int", "immutable": false}, "qualification_note": {"name": "Qualification Note", "type": "string", "immutable": false}, "additional_contacts": {"name": "Additional Contacts", "type": "objectIds", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "potential_closing_date": {"name": "Potential Closing Date", "type": "date", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(245,	'pagodadev',	'object',	'chart_of_accounts',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "note": {"name": "Note", "type": "string", "immutable": false}, "account_id": {"name": "Account ID", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "quickbooks_account_id": {"name": "QuickBooks Account Id", "type": "int", "immutable": false}, "chart_of_accounts_company": {"name": "Chart of accounts company", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts_companies", "selectDisplay": "[name]"}}',	'2017-03-04 17:31:51.419449'),
(266,	'pagodadev',	'object',	'companies',	'{"id": {"name": "id", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "contact_types", "selectDisplay": "[name]"}, "manager": {"name": "Manager", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "products": {"name": "Products", "type": "objectIds", "immutable": false, "objectType": "inventory_categories", "selectDisplay": "[name]"}, "child_ids": {"name": "Children", "type": "string", "immutable": true}, "is_vendor": {"name": "Is vendor?", "type": "int", "immutable": false}, "parent_id": {"name": "Parent", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "tax_exempt": {"name": "Tax Exempt", "type": "boolean", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "contact_info": {"name": "Contact Info", "type": "objectIds", "immutable": true, "objectType": "contact_info", "selectDisplay": "[info]"}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "markup_percent": {"name": "Markup Percent", "type": "float", "immutable": false}, "default_product": {"name": "Default Product", "type": "objectId", "immutable": false, "objectType": "inventory_categories", "selectDisplay": "[name]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "company_category": {"name": "Category", "type": "objectId", "immutable": false, "objectType": "company_categories", "selectDisplay": "[name]"}, "chart_of_accounts": {"name": "Chart of Accounts", "type": "objectIds", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-03-04 17:31:51.442101'),
(46,	'pagodadev',	'object',	'project_types',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "states": {"name": "States", "type": "list", "blueprint": {"uid": {"name": "Uid", "type": "int", "immutable": false}, "icon": {"name": "Icon", "type": "text", "immutable": false}, "name": {"name": "Name", "type": "text", "immutable": false}, "next": {"name": "Next states", "type": "object", "immutable": false}, "type": {"name": "Type", "type": "select", "options": {"done": "Done", "open": "Open", "onHold": "On Hold", "inReview": "In Review", "inProgress": "In Progress"}, "immutable": false}, "color": {"name": "Name", "type": "text", "immutable": false}, "previous": {"name": "Previous states", "type": "object", "immutable": false}, "isEntryPoint": {"name": "Is entry point", "type": "int", "immutable": false}}, "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(51,	'pagodadev',	'object',	'groups',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "project_types", "selectDisplay": "[name]"}, "user": {"name": "User", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "color": {"name": "Color", "type": "text", "immutable": false}, "cycle": {"name": "Cycle", "type": "select", "options": {"daily": "Daily", "weekly": "Weekly", "yearly": "Yearly", "monthly": "Monthly"}, "immutable": false}, "state": {"name": "Status", "type": "int", "immutable": false}, "tools": {"name": "Tools", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": true}, "tip": {"name": "Tip", "type": "string", "immutable": false}, "order": {"name": "Order", "type": "int", "immutable": false}, "added_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "added_on": {"name": "Date Created", "type": "string", "immutable": true}, "system_name": {"name": "System Name", "type": "string", "immutable": false}, "display_name": {"name": "Display Name", "type": "string", "immutable": false}, "is_archieved": {"name": "Is Archieved", "type": "int", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname][lname]"}, "allowed_users": {"name": "Allowed Users", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "potential_value": {"name": "Potential Value", "type": "int", "immutable": false}, "related_contacts": {"name": "Related contacts", "type": "objectIds", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname][lname]"}}, "immutable": false}, "users": {"name": "Users", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "parent": {"name": "Parent", "type": "objectId", "immutable": false, "objectType": "groups", "selectDisplay": "[name]"}, "status": {"name": "Status", "type": "select", "options": {"done": "Done", "paid": "Paid", "active": "Active", "signed": "Signed", "accepted": "Accepted", "complete": "Complete", "notified": "Notified", "proposal": "Proposal", "scheduled": "Scheduled", "unscheduled": "Unscheduled"}, "immutable": false, "objectType": "select"}, "details": {"name": "Details", "type": "string", "immutable": false}, "initial": {"name": "Initial", "type": "int", "immutable": false}, "end_date": {"name": "End date", "type": "date", "immutable": false}, "job_type": {"name": "Job type", "type": "objectId", "immutable": false, "objectType": "inventory_service", "selectDisplay": "name"}, "location": {"name": "Location", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[fname][lname]"}, "managers": {"name": "Managers", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "proposal": {"name": "Proposal", "type": "objectId", "immutable": true, "objectType": "proposals", "selectDisplay": "[name]"}, "is_active": {"name": "Active", "type": "select", "options": ["Yes", "No"], "immutable": false}, "locations": {"name": "Locations", "type": "objectIds", "immutable": false, "objectType": "staff_base", "selectDisplay": "[fname][lname]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "group_type": {"name": "Type", "type": "select", "options": ["Headquarters", "Team", "Project", "Task", "Schedule", "Shift"], "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "source_type": {"name": "Source Type", "type": "object", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "is_recurring": {"name": "Is recurring?", "type": "int", "immutable": false}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname][lname]"}, "project_lead": {"name": "Project lead", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "allowed_users": {"name": "Allowed Users", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "repeat_forever": {"name": "Should repeat forever", "type": "int", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "potential_value": {"name": "Potential Value", "type": "int", "immutable": false}, "repeat_end_date": {"name": "Repeat end date", "type": "date", "immutable": false}, "related_contacts": {"name": "Related contacts", "type": "objectIds", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname][lname]"}, "schedule_options": {"name": "Schedule optoins", "type": "objectIds", "immutable": false}, "can_be_reassigned": {"name": "Can Be Reassigned", "type": "select", "options": ["Yes", "No"], "immutable": false}, "reimburses_vacation_day": {"name": "Reimburses Vacation Day", "type": "int", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(53,	'pagodadev',	'object',	'time_off_vesting_schedule',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "max_qty": {"name": "Vacation Day Cap", "type": "float", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "copied_from": {"name": "Copied From", "type": "int", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "initial_qty": {"name": "Initial Quantity", "type": "float", "immutable": false}, "is_template": {"name": "Is template?", "type": "int", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "does_carry_over": {"name": "Do days carry over?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "vesting_start_date": {"name": "Vesting begins [x] after hire date", "type": "select", "options": {"year": "Year", "month": "1 Month", "2_months": "2 Months", "3_months": "3 Months", "4_months": "4 Months", "6_months": "6 Months", "immediately": "Immediately"}, "immutable": false}, "vesting_period_type": {"name": "Vesting Period", "type": "select", "options": {"year": "Year", "month": "Month", "2_months": "2 Months", "3_months": "3 Months", "4_months": "4 Months", "6_months": "6 Months"}, "immutable": false}, "qty_vested_per_period": {"name": "Days Vested Every Vesting Period", "type": "float", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(263,	'pagodadev',	'object',	'contact_info',	'{"id": {"name": "id", "type": "int", "immutable": true}, "zip": {"name": "Zip Code", "type": "string", "immutable": true}, "city": {"name": "City", "type": "string", "immutable": true}, "info": {"name": "Info", "type": "string", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "objectId", "immutable": false, "objectType": "contact_info_types", "selectDisplay": "[name]"}, "other": {"name": "Other", "type": "string", "immutable": false}, "state": {"name": "State", "type": "string", "immutable": true}, "title": {"name": "Title", "type": "string", "immutable": false}, "street": {"name": "Street", "type": "string", "immutable": true}, "country": {"name": "Country", "type": "string", "immutable": true}, "street2": {"name": "Apartment/Unit Number", "type": "string", "immutable": true}, "object_id": {"name": "Object ID", "type": "int", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_primary": {"name": "Primary", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "object_type": {"name": "Object Type", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-03-04 17:31:51.438337'),
(58,	'pagodadev',	'object',	'notification',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "icon": {"name": "Icon", "type": "string", "immutable": false}, "link": {"name": "Link", "type": "string", "immutable": true}, "type": {"name": "Notification Type", "type": "select", "options": {"general": "General", "mention": "Mention", "question": "Question", "reminder": "Reminder", "assignment": "Assignment"}, "immutable": false}, "user": {"name": "User", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "color": {"name": "Color", "type": "string", "immutable": false}, "title": {"name": "Title", "type": "string", "immutable": false}, "details": {"name": "Details", "type": "string", "immutable": false}, "producer": {"name": "Producer", "type": "relatedObject", "immutable": false}, "is_viewed": {"name": "Viewed", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is Deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "is_archived": {"name": "Is Archived?", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(33,	'pagodadev',	'object',	'inventory_billable_combinations',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "uom": {"name": "Units of measure", "type": "object", "blueprint": {"config": {"name": "Configuration", "type": "object", "blueprint": {"weight": {"name": "Weight", "type": "int", "immutable": false}, "quantity": {"name": "Quantity", "type": "int", "immutable": false}, "dry_volume": {"name": "Dry volume", "type": "int", "immutable": false}, "liquid_volume": {"name": "Liquid volume", "type": "int", "immutable": false}, "volume_to_weight": {"name": "Volume to weight ratio", "type": "float", "immutable": false}}, "immutable": false}, "weight": {"name": "Weight", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "quantity": {"name": "Quantity", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "dry_volume": {"name": "Dry volume", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "liquid_volume": {"name": "Liquid volume", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}}, "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "image": {"name": "Image", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "file_name"}, "items": {"name": "Items", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": false}, "qty": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "name": {"name": "Name", "type": "string", "immutable": false}, "units": {"name": "Units", "type": "int", "immutable": false}, "choices": {"name": "choices", "type": "list", "blueprint": {"qty": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "name": {"name": "Name", "type": "string", "immutable": false}, "units": {"name": "Units", "type": "int", "immutable": false}, "divisor": {"name": "Divisor", "type": "float", "immutable": false}, "multiplier": {"name": "Multiplier", "type": "float", "immutable": false}, "description": {"name": "Description", "type": "string", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "relatedObject", "immutable": false}, "additional_price": {"name": "Additional Price", "type": "usd", "immutable": false}}, "immutable": false}, "divisor": {"name": "Divisor", "type": "float", "immutable": false}, "multiplier": {"name": "Multiplier", "type": "float", "immutable": false}, "max_selections": {"name": "Max Selections", "type": "int", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "relatedObject", "immutable": false}}, "immutable": false}, "price": {"name": "Price Per Unit", "type": "usd", "immutable": false}, "units": {"name": "Units", "type": "objectId", "immutable": false, "objectType": "inventory_units", "selectDisplay": "[name]"}, "category": {"name": "Category", "type": "objectId", "immutable": false, "objectType": "inventory_billable_combination_categories", "selectDisplay": "[name]"}, "quantity": {"name": "Quantity", "type": "float", "immutable": false}, "selection": {"name": "Selection", "type": "object", "immutable": false}, "tax_rates": {"name": "Tax Rates", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": false}, "item_yield": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "servings": {"name": "Servings", "type": "int", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "price_per_hour": {"name": "Price Per Hour", "type": "usd", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": true, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "default_tax_rate": {"name": "Default Tax Rate", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "price_per_person": {"name": "Price Per Person", "type": "usd", "immutable": false}, "default_qty_option": {"name": "Quantity Style", "type": "select", "options": {"absolute": "Absolute", "per_hour": "Per hour", "per_guest": "Per guest", "per_hour_per_guest": "Per hour per guest"}, "immutable": false}, "default_pricing_option": {"name": "Pricing Style", "type": "select", "options": {"price": "Price", "price_per_hour": "Price per hour", "price_per_person": "Price per person", "price_per_hour_per_person": "Price per hour per person"}, "immutable": false}, "price_per_hour_per_person": {"name": "Price Per Hour Per Person", "type": "usd", "immutable": false}, "is_hidden_from_menu_selections": {"name": "Is hidden from menu selections?", "type": "int", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(285,	'pagodadev',	'object',	'inventory_service_categories',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "vendor_id": {"name": "Vendor", "type": "objectId", "immutable": false, "objectType": "vendors", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "surcharges": {"name": "Surcharges", "type": "objectIds", "immutable": false, "objectType": "surcharges", "selectDisplay": "[name]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "testing_time": {"name": "Testing Time", "type": "time", "immutable": false}, "exclusive_tax": {"name": "Exclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "inclusive_tax": {"name": "Inclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectIds", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}}',	'2017-03-15 15:48:19.958876'),
(276,	'pagodadev',	'object',	'inventory_billable_groups',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "uom": {"name": "Units of measure", "type": "object", "blueprint": {"config": {"name": "Configuration", "type": "object", "blueprint": {"weight": {"name": "Weight", "type": "int", "immutable": false}, "quantity": {"name": "Quantity", "type": "int", "immutable": false}, "dry_volume": {"name": "Dry volume", "type": "int", "immutable": false}, "liquid_volume": {"name": "Liquid volume", "type": "int", "immutable": false}, "volume_to_weight": {"name": "Volume to weight ratio", "type": "float", "immutable": false}}, "immutable": false}, "weight": {"name": "Weight", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "quantity": {"name": "Quantity", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "dry_volume": {"name": "Dry volume", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}, "liquid_volume": {"name": "Liquid volume", "type": "object", "blueprint": {"default_shipment_measurement": {"name": "Default shipment measurement", "type": "int", "immutable": false}}, "immutable": false}}, "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "image": {"name": "Image", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "file_name"}, "items": {"name": "Items", "type": "list", "blueprint": {"id": {"name": "ID", "type": "int", "immutable": false}, "qty": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "name": {"name": "Name", "type": "string", "immutable": false}, "units": {"name": "Units", "type": "int", "immutable": false}, "choices": {"name": "choices", "type": "list", "blueprint": {"qty": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "name": {"name": "Name", "type": "string", "immutable": false}, "units": {"name": "Units", "type": "int", "immutable": false}, "divisor": {"name": "Divisor", "type": "float", "immutable": false}, "multiplier": {"name": "Multiplier", "type": "float", "immutable": false}, "description": {"name": "Description", "type": "string", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "relatedObject", "immutable": false}, "additional_price": {"name": "Additional Price", "type": "usd", "immutable": false}}, "immutable": false}, "divisor": {"name": "Divisor", "type": "float", "immutable": false}, "multiplier": {"name": "Multiplier", "type": "float", "immutable": false}, "max_selections": {"name": "Max Selections", "type": "int", "immutable": false}, "inventory_group": {"name": "Inventory Group", "type": "relatedObject", "immutable": false}}, "immutable": false}, "price": {"name": "Price Per Unit", "type": "usd", "immutable": false}, "units": {"name": "Units", "type": "objectId", "immutable": false, "objectType": "inventory_units", "selectDisplay": "[name]"}, "category": {"name": "Category", "type": "objectId", "immutable": false, "objectType": "inventory_billable_categories", "selectDisplay": "[name]"}, "quantity": {"name": "Quantity", "type": "float", "immutable": false}, "selection": {"name": "Selection", "type": "object", "immutable": false}, "tax_rates": {"name": "Tax Rates", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": false}, "item_yield": {"quantity": {"name": "Quantity", "type": "float", "immutable": false}, "servings": {"name": "Servings", "type": "int", "immutable": false}, "unit_type": {"name": "Unit type", "type": "select", "options": {"weight": "Weight", "quantity": "Quantity", "dry_volume": "Dry volume", "liquid_volume": "Liquid volume"}, "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "surcharges": {"name": "Surcharges", "type": "objectIds", "immutable": false, "objectType": "surcharges", "selectDisplay": "[name]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_of_use": {"name": "Date of Use", "type": "date", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "price_per_hour": {"name": "Price Per Hour", "type": "usd", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": true, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "default_tax_rate": {"name": "Default Tax Rate", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "price_per_person": {"name": "Price Per Person", "type": "usd", "immutable": false}, "default_qty_option": {"name": "Quantity Style", "type": "select", "options": {"absolute": "Absolute", "per_hour": "Per hour", "per_guest": "Per guest", "per_hour_per_guest": "Per hour per guest"}, "immutable": false}, "default_pricing_option": {"name": "Pricing Style", "type": "select", "options": {"price": "Price", "price_per_hour": "Price per hour", "price_per_person": "Price per person", "price_per_hour_per_person": "Price per hour per person"}, "immutable": false}, "price_per_hour_per_person": {"name": "Price Per Hour Per Person", "type": "usd", "immutable": false}, "is_hidden_from_menu_selections": {"name": "Is hidden from menu selections?", "type": "int", "immutable": false}}',	'2017-03-04 17:31:51.454264'),
(47,	'pagodadev',	'object',	'proposals',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "menu": {"name": "Menu", "type": "objectId", "immutable": false, "objectType": "inventory_menu", "selectDisplay": ""}, "name": {"name": "Name", "type": "string", "immutable": false}, "venue": {"name": "Venue", "type": "objectId", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "status": {"name": "Status", "type": "select", "options": ["Editing", "Proposal", "Admin Review", "Approved", "Declined", "Active", "Client Review", "Accepted", "Signed", "Paid", "Complete"], "immutable": false, "objectType": "select"}, "manager": {"name": "Manager", "type": "objectId", "immutable": false, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "pricing": {"name": "Pricing", "type": "object", "immutable": false}, "vendors": {"name": "Vendors", "type": "objectId", "immutable": false, "objectType": "event_vendors", "selectDisplay": "[name]"}, "contract": {"name": "Contracts", "type": "objectId", "immutable": false, "objectType": "contracts", "selectDisplay": "[name]"}, "end_date": {"name": "End Date", "type": "date", "immutable": false}, "invoices": {"name": "Invoices", "type": "objectIds", "immutable": false, "objectType": "invoices", "selectDisplay": ""}, "schedule": {"name": "Schedule", "type": "objectId", "immutable": false, "objectType": "staff_schedules", "selectDisplay": ""}, "sections": {"name": "Sections", "type": "object", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is Deleted", "type": "select", "options": ["No", "Yes"], "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "start_date": {"name": "Start Date", "type": "date", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "is_template": {"name": "Is template?", "type": "int", "immutable": false}, "main_object": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "approval_notes": {"name": "Approval Notes", "type": "object", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(44,	'pagodadev',	'object',	'document',	'{"id": {"name": "Id", "type": "int", "immutable": true}, "body": {"name": "Body", "type": "string", "immutable": false}, "name": {"name": "Name", "type": "string", "immutable": false}, "category": {"name": "Document Category", "type": "objectId", "immutable": false, "objectType": "document_category", "selectDisplay": "[name]"}, "is_public": {"name": "Is Public?", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is Deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "share_link": {"name": "Share Link", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "file_upload": {"name": "File", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "is_archived": {"name": "Is Archived?", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "storage_type": {"name": "Storage Type", "type": "select", "options": {"box": "Box File", "other": "Other", "google": "Google", "dropbox": "Dropbox File", "onedrive": "Microsoft OneDrive"}, "immutable": false}, "tagged_users": {"name": "Tagged Users", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname][lname]"}, "document_type": {"name": "Document Type", "type": "select", "options": {"text": "Text", "share": "Shareable Link", "upload": "File Upload", "google_doc": "Google Doc", "custom-file": "Custom File", "google_other": "Google Other", "google_sheet": "Google Sheet", "google_slide": "Google Slide"}, "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "related_object": {"name": "Related Object Id", "type": "relatedObject", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}}',	'2017-10-05 21:46:50.920443'),
(32,	'pagodadev',	'object',	'inventory_billable_combination_categories',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "tax_rates": {"name": "Tax Rates", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "surcharges": {"name": "Surcharges", "type": "objectIds", "immutable": false, "objectType": "surcharges", "selectDisplay": "[name]"}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "Description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": true, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "default_tax_rate": {"name": "Default Tax Rate", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "default_pricing_option": {"name": "Default pricing option", "type": "select", "options": {"price": "Price per unit", "price_per_hour": "Price per hour", "price_per_person": "Price per person", "price_per_hour_per_person": "Price per hour per person"}, "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(37,	'pagodadev',	'object',	'inventory_menu_line_item',	'{"id": {"name": "id", "type": "int", "immutable": false}, "qty": {"name": "Quantity", "type": "int", "immutable": false}, "item": {"name": "Item", "type": "object", "immutable": false}, "menu": {"name": "Menu", "type": "int", "immutable": false}, "note": {"name": "Notes", "type": "string", "immutable": false}, "type": {"name": "Type", "type": "select", "options": {"item": "Item", "budget": "Budget", "estimate": "Estimate"}, "immutable": false}, "state": {"name": "state", "type": "select", "options": {"reserved": "Stock items reserved", "selections_open": "Choices are still open", "selections_completed": "All selections have been made"}, "immutable": false}, "sortId": {"name": "Sort Id", "type": "int", "immutable": false}, "vendor": {"name": "Vendor", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]"}, "choices": {"name": "Choices", "type": "object", "blueprint": {"item": {"name": "Item", "type": "int", "immutable": false}, "choice": {"name": "Choice", "type": "object", "immutable": false}}, "immutable": false}, "invoice": {"type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "product": {"name": "Product", "type": "int", "immutable": true}, "section": {"name": "Section", "type": "int", "immutable": false}, "qty_type": {"name": "Quantity Type", "type": "select", "options": {"absolute": "Total", "per_guest": "Per Guest", "guest_count": "Guest Count"}, "immutable": false}, "servings": {"name": "Servings", "type": "float", "immutable": false}, "tax_rates": {"name": "Tax Rates", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "unit_type": {"name": "Unit type", "type": "string", "immutable": false}, "price_type": {"name": "Price Type", "type": "select", "options": {"price": "Standard Pricing", "price_per_hour": "Per hour", "price_per_person": "Per person", "price_per_hour_per_person": "Per hour per person"}, "immutable": false}, "surcharges": {"name": "Surcharges", "type": "objectIds", "immutable": false, "objectType": "surcharges", "selectDisplay": "[name]"}, "yield_type": {"name": "Yield type", "type": "string", "immutable": false}, "date_of_use": {"name": "Date of Use", "type": "date", "immutable": false}, "measurement": {"name": "Measurement", "type": "int", "immutable": false}, "absolute_qty": {"name": "Absolute Quantity", "type": "int", "immutable": false}, "product_category": {"name": "Product Category", "type": "int", "immutable": false}, "item_reservations": {"name": "Reservations", "type": "objectIds", "immutable": false, "objectType": "item_reservation", "selectDisplay": "[date]"}, "override_tax_rates": {"name": "Override category default tax rates?", "type": "int", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(258,	'pagodadev',	'object',	'inventory_categories',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "name": {"name": "Name", "type": "string", "immutable": false}, "vendor_id": {"name": "Vendor", "type": "objectId", "immutable": false, "objectType": "vendors", "selectDisplay": "[name]"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "is_deleted": {"name": "Is deleted?", "type": "int", "immutable": true}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "description": {"name": "description", "type": "string", "immutable": false}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "exclusive_tax": {"name": "Exclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "inclusive_tax": {"name": "Inclusive Tax", "type": "objectIds", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "chart_of_account": {"name": "Chart of Account", "type": "objectId", "immutable": false, "objectType": "chart_of_accounts", "selectDisplay": "[name]"}, "test_private_val": {"name": "Test", "type": "string", "encrypt": true, "immutable": false}}',	'2017-03-04 17:31:51.432806'),
(55,	'pagodadev',	'object',	'instance_setup',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "email": {"name": "Email Address", "type": "string", "immutable": false}, "fname": {"name": "First Name", "type": "string", "immutable": false}, "lname": {"name": "Last Name", "type": "string", "immutable": false}, "credit": {"name": "Credit", "type": "float", "immutable": false}, "parent": {"name": "Parent", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "db_post": {"name": "Database Post", "type": "string", "immutable": false}, "db_read": {"name": "Database Read", "type": "string", "immutable": false}, "enabled": {"name": "Enabled", "type": "select", "options": ["Disabled", "Enabled"], "immutable": false}, "sold_by": {"name": "Sold by", "type": "objectId", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]", "objectOverflow": true}, "version": {"name": "Version", "type": "string", "immutable": false}, "contract": {"name": "Contract", "type": "objectId", "immutable": false, "objectType": "contract", "selectDisplay": "[name]"}, "db_write": {"name": "Database Write", "type": "string", "immutable": false}, "flat_fee": {"name": "Flat fee", "type": "int", "immutable": false}, "instance": {"name": "Slug", "type": "string", "immutable": false}, "sms_from": {"name": "SMS From", "type": "string", "immutable": false}, "tax_rate": {"name": "Tax rate", "type": "objectId", "immutable": false, "objectType": "tax_rates", "selectDisplay": "[name]"}, "emailFrom": {"name": "Email From", "type": "string", "immutable": false}, "temp_user": {"name": "Temp Root User", "type": "object", "immutable": false}, "components": {"name": "Components", "type": "string", "immutable": false}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "files_read": {"name": "Files Read", "type": "string", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "systemName": {"name": "System Name", "type": "string", "immutable": false}, "twilio_sid": {"name": "Twilio SID", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "files_write": {"name": "Files Write", "type": "string", "immutable": false}, "is_template": {"name": "Is template?", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "pageModules": {"name": "Page Modules", "type": "string", "immutable": false}, "permissions": {"name": "Permisions", "type": "string", "immutable": false}, "billing_plan": {"name": "Billing Plan", "type": "object", "immutable": false}, "billing_type": {"name": "Billing Type", "type": "string", "immutable": false}, "company_logo": {"name": "Company logo", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "name"}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "files_bucket": {"name": "Files Bucket", "type": "string", "immutable": false}, "files_delete": {"name": "Files Delete", "type": "string", "immutable": false}, "last_invoice": {"name": "Last invoice", "type": "objectId", "immutable": false, "objectType": "invoices", "selectDisplay": "[date_created]", "objectOverflow": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "main_contact": {"name": "Main Contact", "type": "objectId", "immutable": false, "objectType": "contacts", "selectDisplay": "[fname] [lname]"}, "mandrill_api": {"name": "Mandrill API Key", "type": "string", "immutable": false}, "moduleSource": {"name": "Module Source", "type": "string", "immutable": false}, "qty_of_users": {"name": "Qty of users", "type": "int", "immutable": false}, "twilio_token": {"name": "Twilio Token", "type": "string", "immutable": false}, "userSettings": {"name": "User Settings", "type": "string", "immutable": false}, "factorySource": {"name": "Factory Source", "type": "string", "immutable": false}, "flat_user_cap": {"name": "Flat user cap", "type": "int", "immutable": false}, "instance_type": {"name": "Instance type", "type": "int", "immutable": false}, "mailchimp_api": {"name": "MailChimp API Key", "type": "string", "immutable": false}, "searchObjects": {"name": "Search Objects", "type": "string", "immutable": false}, "settingSource": {"name": "Setting Source", "type": "string", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "per_user_price": {"name": "Per User Price", "type": "int", "immutable": false}, "price_per_user": {"name": "Price per user", "type": "int", "immutable": false}, "trial_end_date": {"name": "Trial end date", "type": "date", "immutable": false}, "componentSource": {"name": "Component Source", "type": "string", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "settingsModules": {"name": "Settings Modules", "type": "string", "immutable": false}, "settings_objects": {"name": "Settings Objects", "type": "string", "immutable": false}, "trial_start_date": {"name": "Trial start date", "type": "date", "immutable": false}, "last_billing_date": {"name": "Last billing date", "type": "date", "immutable": false}, "mailchimp_list_id": {"name": "MailChimp List ID", "type": "string", "immutable": false}, "stripe_account_id": {"name": "Stripe Account ID", "type": "string", "immutable": false}, "account_signup_code": {"name": "Account signup code", "type": "string", "immutable": false}, "quickbooks_realm_id": {"name": "QuickBooks Realm ID", "type": "string", "immutable": false}, "quickbooks_access_token": {"name": "QuickBooks Access Token", "type": "string", "immutable": false}, "quickbooks_refresh_token": {"name": "QuickBooks Refresh Token", "type": "string", "immutable": false}}',	'2017-10-05 21:46:50.920443'),
(289,	'pagodadev',	'object',	'users',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "dob": {"name": "Date of Birth", "type": "date", "immutable": false}, "pin": {"name": "Pin Number", "type": "string", "immutable": false}, "ssn": {"name": "Social Security #", "type": "string", "immutable": false}, "zip": {"name": "Zip", "type": "string", "immutable": false}, "base": {"name": "Location", "type": "objectIds", "immutable": false, "objectType": "staff_base", "selectDisplay": "[name]"}, "city": {"name": "City", "type": "string", "immutable": false}, "rate": {"name": "Rate", "type": "int", "immutable": false}, "type": {"name": "User Type", "type": "select", "options": {"admin": "Admin", "staff": "Staff", "contacts": "Client", "developer": "Developer"}, "immutable": false}, "color": {"name": "Color", "type": "text", "immutable": false}, "email": {"name": "Email", "type": "string", "immutable": false}, "fname": {"name": "First Name", "type": "string", "immutable": false}, "lname": {"name": "Last Name", "type": "string", "immutable": false}, "phone": {"name": "Phone", "type": "string", "immutable": false}, "state": {"name": "State", "type": "string", "immutable": false}, "parent": {"name": "Parent", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "status": {"name": "Employee Status", "type": "objectId", "immutable": false, "objectType": "staff_status", "selectDisplay": "[name]"}, "street": {"name": "Street", "type": "string", "immutable": false}, "address": {"name": "Address", "type": "objectId", "immutable": false, "objectType": "contact_info", "selectDisplay": "[type]", "objectOverflow": true}, "country": {"name": "Country", "type": "string", "immutable": false}, "enabled": {"name": "Enabled", "type": "int", "immutable": false}, "payroll": {"name": "Payroll", "type": "objectIds", "immutable": false, "objectType": "payroll", "selectDisplay": "[id]"}, "service": {"name": "Service", "type": "objectIds", "immutable": false, "objectType": "inventory_service", "selectDisplay": "[name]"}, "password": {"name": "Password", "type": "string", "immutable": false}, "profiles": {"name": "Profiles", "type": "objectIds", "immutable": false, "objectType": "user_views", "selectDisplay": "[name]"}, "hire_date": {"name": "Date of Hire", "type": "date", "immutable": false}, "nick_name": {"name": "Nick Name", "type": "string", "immutable": false}, "sick_days": {"name": "Sick Days", "type": "objectId", "immutable": false, "objectType": "time_off_vesting_schedule"}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "dependents": {"name": "Dependents", "type": "int", "immutable": false}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "user_views": {"name": "User Views", "type": "objectIds", "immutable": true, "objectType": "user_views", "selectDisplay": "[name]"}, "work_email": {"name": "Work Email", "type": "string", "immutable": false}, "work_phone": {"name": "Work Phone", "type": "string", "immutable": false}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "string", "immutable": true}, "garnishments": {"name": "Garnishment Amount", "type": "usd", "immutable": false}, "hours_worked": {"name": "Hours Worked", "type": "int", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "filing_status": {"name": "Filing Status", "type": "select", "options": {"single": "Single", "married": "Married", "single_rate": "Married (Single Rate)"}, "immutable": false}, "profile_image": {"name": "Profile Image", "type": "objectId", "immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]"}, "vacation_days": {"name": "Vacation Days", "type": "objectId", "immutable": false, "objectType": "time_off_vesting_schedule"}, "daily_requests": {"name": "Daily Requests", "type": "int", "immutable": true}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": false}, "related_object": {"name": "Related Object", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "staff", "selectDisplay": "[fname] [lname]"}, "work_week_start": {"name": "Work Week Start", "type": "select", "options": {"friday": "Friday", "monday": "Monday", "sunday": "Sunday", "tuesday": "Tuesday", "saturday": "Saturday", "thursday": "Thursday", "wednesday": "Wednesday"}, "immutable": false}, "company_hired_to": {"name": "Company Hired To", "type": "objectId", "immutable": false, "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true}, "termination_date": {"name": "Termination Date", "type": "date", "immutable": false}}',	'2017-03-04 17:31:51.443227'),
(36,	'pagodadev',	'object',	'workorder_system',	'{"id": {"name": "ID", "type": "int", "immutable": true}, "created_by": {"name": "Created By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "object_uid": {"name": "Object Id", "type": "int", "immutable": true}, "data_source": {"name": "Data Source", "type": "int", "immutable": true}, "date_created": {"name": "Date Created", "type": "date", "immutable": true}, "last_updated": {"name": "Last Updated", "type": "date", "immutable": true}, "request_email": {"name": "Signture Request Email Template", "type": "text", "immutable": false}, "data_source_id": {"name": "Data Source Id", "type": "int", "immutable": true}, "follow_up_time": {"name": "Follow Up Time", "type": "int", "immutable": false}, "follow_up_type": {"name": "Follow Up Type", "type": "text", "immutable": false}, "last_updated_by": {"name": "Last Updated By", "type": "objectId", "immutable": true, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "default_opp_note": {"name": "Default Opportunity Note", "type": "text", "immutable": false}, "require_approval": {"name": "Require Approval", "type": "select", "options": {"no": "No", "yes": "Yes"}, "immutable": false}, "approval_admin_users": {"name": "Approval Admin Users", "type": "objectIds", "immutable": false, "objectType": "users", "selectDisplay": "[fname] [lname]"}, "signature_disclaimer": {"name": "Signature Disclaimer", "type": "text", "immutable": false}, "request_email_subject": {"name": "Signture Request Email Subject Template", "type": "text", "immutable": false}}',	'2017-10-05 21:46:50.920443');

-- 2019-01-24 22:16:16.996918-05