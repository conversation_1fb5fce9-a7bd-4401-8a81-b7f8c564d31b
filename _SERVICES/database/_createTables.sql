CREATE TABLE objects (
	id  SERIAL PRIMARY KEY,
	instance TEXT,
	object_type TEXT,
	object_data JSONB,
	date_created TIMESTAMP DEFAULT NOW(),
	is_deleted BOOLEAN,
	tagged_with INTEGER[],
	shared_with INTEGER[],
	read_obj INTEGER[],
	write_obj INTEGER[],
	notify INTEGER[],
	is_task BOOLEAN
);

CREATE TABLE blueprints (
	id  SERIAL PRIMARY KEY,
	instance TEXT,
	blueprint_type TEXT,
	blueprint_name TEXT,
	blueprint JSONB,
	date_created TIMESTAMP DEFAULT NOW()
);