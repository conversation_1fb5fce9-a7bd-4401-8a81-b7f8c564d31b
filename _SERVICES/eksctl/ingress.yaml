apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    certmanager.k8s.io/issuer: "letsencrypt-prod"
    certmanager.k8s.io/acme-challenge-type: http01
    nginx.ingress.kubernetes.io/proxy-body-size: 20m
  name: bento
spec:
  rules:
    - host: "bento.infinityhospitality.net"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: bentoproduction
                port:
                  number: 80
    - host: "foundationgroup.bento.infinityhospitality.net"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: bentoproduction
                port:
                  number: 80
    - host: "merge.bento.infinityhospitality.net"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: bento-mergeservice
                port:
                  number: 80
  tls:
    - hosts:
        - "bento.infinityhospitality.net"
        - "merge.bento.infinityhospitality.net"
        - "foundationgroup.bento.infinityhospitality.net"
      secretName: tls-secret
