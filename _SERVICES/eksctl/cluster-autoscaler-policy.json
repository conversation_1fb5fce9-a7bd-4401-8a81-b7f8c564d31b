{"Version": "2012-10-17", "Statement": [{"Sid": "VisualEditor0", "Effect": "Allow", "Action": ["autoscaling:SetDesiredCapacity", "autoscaling:TerminateInstanceInAutoScalingGroup"], "Resource": "*", "Condition": {"StringEquals": {"aws:ResourceTag/k8s.io/cluster-autoscaler/bento": "owned"}}}, {"Sid": "VisualEditor1", "Effect": "Allow", "Action": ["autoscaling:DescribeAutoScalingInstances", "autoscaling:DescribeAutoScalingGroups", "ec2:DescribeLaunchTemplateVersions", "autoscaling:DescribeTags", "autoscaling:DescribeLaunchConfigurations"], "Resource": "*"}]}