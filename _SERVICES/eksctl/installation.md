helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx --namespace ingress-nginx --set controller.service.type=LoadBalancer --version 4.2.0 --create-namespace

kubectl get service ingress-nginx-controller --namespace=ingress-nginx
dev: a2514530746a44516a8f801c48286665-1954099921.us-east-2.elb.amazonaws.com
prod: a5e93be0f4d8c42f38b3234834769818-1360396574.us-east-2.elb.amazonaws.com

kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.9.1/cert-manager.crds.yaml
helm repo add jetstack https://charts.jetstack.io
helm repo update
helm install cert-manager jetstack/cert-manager --namespace cert-manager --create-namespace --version v1.9.1
kubectl get pods --namespace cert-manager

SETUP DNS

kubectl apply -f le.yaml

kubectl apply -f ingress.yaml
kubectl apply -f ../configmap/appconfig.yaml

<!-- kubectl apply -f ../service/bento-pdf-generation.yaml
kubectl apply -f ../service/bento-zip-generation.yaml -->

<!-- kubectl apply -f ../service/bento-merge-service.yaml -->

kubectl apply -f ../service/bentoproduction.yaml

kubectl create secret docker-registry dockerhub --docker-username= --docker-password= --docker-email=

<!-- kubectl apply -f ../deployment/bento-pdf-generation.yaml
kubectl apply -f ../deployment/bento-zip-generation.yaml -->

kubectl apply -f ../deployment/bentoproduction.yaml

kubectl apply -f ../hpa/bentoproduction.yaml
