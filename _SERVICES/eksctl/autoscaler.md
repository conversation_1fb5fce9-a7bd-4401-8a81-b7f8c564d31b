https://docs.aws.amazon.com/eks/latest/userguide/autoscaling.html

DEV

aws iam create-policy \
 --policy-name AmazonEKSClusterAutoscalerPolicyDev \
 --policy-document file://cluster-autoscaler-policy-dev.json

eksctl utils associate-iam-oidc-provider --region=us-east-2 --cluster=bento-dev --approve

eksctl create iamserviceaccount \
 --cluster=bento-dev \
 --namespace=kube-system \
 --name=cluster-autoscaler \
 --attach-policy-arn=arn:aws:iam::************:policy/AmazonEKSClusterAutoscalerPolicyDev \
 --override-existing-serviceaccounts \
 --approve

curl -o cluster-autoscaler-autodiscover.yaml https://raw.githubusercontent.com/kubernetes/autoscaler/master/cluster-autoscaler/cloudprovider/aws/examples/cluster-autoscaler-autodiscover.yaml

UPDATE CLUSTER NAME -- line 165

kubectl apply -f cluster-autoscaler-autodiscover-dev.yaml

kubectl annotate serviceaccount cluster-autoscaler \
 -n kube-system \
 eks.amazonaws.com/role-arn=arn:aws:iam::************:role/AmazonEKSClusterAutoscalerRoleDev

kubectl patch deployment cluster-autoscaler \
 -n kube-system \
 -p '{"spec":{"template":{"metadata":{"annotations":{"cluster-autoscaler.kubernetes.io/safe-to-evict": "false"}}}}}'

kubectl -n kube-system edit deployment.apps/cluster-autoscaler

        - --balance-similar-node-groups
        - --skip-nodes-with-system-pods=false

kubectl set image deployment cluster-autoscaler \
 -n kube-system \
 cluster-autoscaler=k8s.gcr.io/autoscaling/cluster-autoscaler:v1.22.3

kubectl -n kube-system logs -f deployment.apps/cluster-autoscaler

PROD

aws iam create-policy \
 --policy-name AmazonEKSClusterAutoscalerPolicy \
 --policy-document file://cluster-autoscaler-policy.json
eksctl utils associate-iam-oidc-provider --region=us-east-2 --cluster=bento --approve

eksctl create iamserviceaccount \
 --cluster=bento \
 --namespace=kube-system \
 --name=cluster-autoscaler \
 --attach-policy-arn=arn:aws:iam::************:policy/AmazonEKSClusterAutoscalerPolicy \
 --override-existing-serviceaccounts \
 --approve

kubectl apply -f cluster-autoscaler-autodiscover.yaml

kubectl patch deployment cluster-autoscaler \
 -n kube-system \
 -p '{"spec":{"template":{"metadata":{"annotations":{"cluster-autoscaler.kubernetes.io/safe-to-evict": "false"}}}}}'

kubectl -n kube-system edit deployment.apps/cluster-autoscaler

         - --balance-similar-node-groups
         - --skip-nodes-with-system-pods=false

kubectl set image deployment cluster-autoscaler \
 -n kube-system \
 cluster-autoscaler=k8s.gcr.io/autoscaling/cluster-autoscaler:v1.22.3

kubectl -n kube-system logs -f deployment.apps/cluster-autoscaler
