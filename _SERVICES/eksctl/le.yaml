---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    # The ACME server URL
    server: https://acme-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: <EMAIL>
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-prod
    # Enable the HTTP-01 challenge provider
    solvers:
      - http01:
          ingress:
            class: nginx
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: letsencrypt-prod
spec:
  secretName: tls-secret
  issuerRef:
    kind: Issuer
    name: letsencrypt-prod
  commonName: "bento.infinityhospitality.net"
  dnsNames:
    - "bento.infinityhospitality.net"
    - "merge.bento.infinityhospitality.net"
    - "foundationgroup.bento.infinityhospitality.net"
