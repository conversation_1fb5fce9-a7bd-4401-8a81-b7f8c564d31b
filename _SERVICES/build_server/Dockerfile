FROM php:7.3-apache

# Install composer
RUN cd /tmp && curl -sS https://getcomposer.org/installer | php
RUN cd /tmp && mv composer.phar /usr/local/bin/composer

COPY src/php.ini $PHP_INI_DIR/conf.d/

# Install PHP modules
RUN apt-get update && apt-get install -y \
		libpng-dev \
		libzip-dev \
		libpq-dev \
                git \
                unzip \
                zip \
                && docker-php-ext-install zip

RUN docker-php-ext-configure pgsql -with-pgsql=/usr/local/pgsql
       
RUN a2enmod rewrite
 
RUN docker-php-ext-configure mbstring
RUN docker-php-ext-install mbstring

RUN docker-php-ext-configure gd
RUN docker-php-ext-install gd

RUN docker-php-ext-install pdo pdo_pgsql pgsql