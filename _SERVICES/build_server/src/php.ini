[PHP]

upload_max_filesize = 150M

; Maximum size of POST data that <PERSON><PERSON> will accept.
; Its value may be 0 to disable the limit. It is ignored if POST data reading
; is disabled through enable_post_data_reading.
; http://php.net/post-max-size
post_max_size = 150M

; This sets the maximum amount of memory in bytes that a script is allowed to allocate.
; This helps prevent poorly written scripts for eating up all available memory on a server...
memory_limit = 512M

; This lets us send responses back in buffers without having to wait for the script to end
output_buffering = on

; Turns of gzip compression
zlib.output_compression=off

error_reporting = E_ALL & ~E_STRICT