version: 0.2
env:
  variables:
    AWS_DEFAULT_REGION: "us-east-2"
    AWS_ACCOUNT_ID: "************"
    AWS_CLUSTER_NAME: "bento-dev"
phases:
  install:
    commands:
      - aws --version
      - phpenv global 7.4
      - curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
      - chmod 700 get_helm.sh
      - echo "Installing Helm 3.14.x"
      - ./get_helm.sh --version v3.14.0
      - curl -o kubectl https://amazon-eks.s3.$AWS_DEFAULT_REGION.amazonaws.com/1.19.6/2021-01-05/bin/linux/amd64/kubectl
      - chmod +x ./kubectl
      - mkdir -p $HOME/bin && cp ./kubectl $HOME/bin/kubectl && export PATH=$PATH:$HOME/bin
      - echo 'export PATH=$PATH:$HOME/bin' >> ~/.bashrc
      - . ~/.bashrc
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/bento
      - REPOSITORY_URI_MERGE=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/bento-merge-service
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
      - aws eks --region $AWS_DEFAULT_REGION update-kubeconfig --name $AWS_CLUSTER_NAME
      - echo "Checking cluster access..."
      - chmod 600 /root/.kube/config
      - cat ~/.kube/config
      - kubectl get svc
      - echo "Verifying Helm installation..."
      - helm version
  build:
    commands:
      - echo "Checking Docker version..."
      - docker --version
      - echo Build started on `date`
      # Install Dependencies
      - cd ${CODEBUILD_SRC_DIR}
      - npm ci --cache .npm
      #Install Backend Dependencies dev4
      - cd ${CODEBUILD_SRC_DIR}/_SRC/pagoda
      - composer install --no-dev
      #Install Merge Dependencies
      - cd ${CODEBUILD_SRC_DIR}/_SRC/merge
      - npm ci --only=production --cache .npm
      - cd ${CODEBUILD_SRC_DIR}
      # -- Building Version Number
      - export COMMIT_SHORT=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c1-7)
      - export buildVersion=$COMMIT_SHORT
      - cd ${CODEBUILD_SRC_DIR}/_SRC/pagoda
      - echo "<?php const APP_BUILD = '${buildVersion}'; ?>" > BUILD_VERSION.php
      - cat BUILD_VERSION.php
      - echo "<?php const MERGE_ENDPOINT = '${MERGE_ENDPOINT}'; ?>" > BENTO_ENV.php
      - cat BENTO_ENV.php
      - cd ${CODEBUILD_SRC_DIR}/_SRC/notify/_factory
      - echo "var STRIPE_PK = '${STRIPE_PK}'; var CURRENT_ENV = '${CURRENT_ENV}'; var BITBUCKET_BUILD_NUMBER = '${buildVersion}'; var PAGODA_APP_VERSION = '${buildVersion}'; var PAGODA_APP_BUILD = '${buildVersion}';" > _version.js
      - cd ${CODEBUILD_SRC_DIR}/_SRC/merge/src
      - echo "var BITBUCKET_BUILD_NUMBER = '${buildVersion}'; var PAGODA_APP_VERSION = '${buildVersion}'; var PAGODA_APP_BUILD = '${buildVersion}'; module.exports = { BITBUCKET_BUILD_NUMBER, PAGODA_APP_VERSION, PAGODA_APP_BUILD };" > _version.js
      - cd ${CODEBUILD_SRC_DIR}
      - npm install gulp -g
      - gulp build-bento --buildNumber $buildVersion
      # -- Saving artifacts
      - echo "BUILD_VERSION=${buildVersion}" >> build.env

      - echo Building the Bento Docker image...
      - cd ${CODEBUILD_SRC_DIR}/_SERVICES/app
      - echo Building tagging $REPOSITORY_URI:$COMMIT_SHORT
      - docker build -t $REPOSITORY_URI:$COMMIT_SHORT .

      - echo Building the Bento Merge Service Docker image...
      - cd ${CODEBUILD_SRC_DIR}/_SERVICES/merge
      - echo Building tagging $REPOSITORY_URI_MERGE:$COMMIT_SHORT
      - docker build -t $REPOSITORY_URI_MERGE:$COMMIT_SHORT .
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:$COMMIT_SHORT
      - docker push $REPOSITORY_URI_MERGE:$COMMIT_SHORT
  post_build:
    commands:
      - echo Helm Upgrading Bento...
      - cd ${CODEBUILD_SRC_DIR}/_SERVICES/helm/bento-app
      - echo Installing Version $REPOSITORY_URI:$COMMIT_SHORT
      - >
        helm upgrade bentoproduction ./ --install
        --set config.BENTO_DATABASE_NAME=${BENTO_DATABASE_NAME}
        --set config.BENTO_DATABASE_PASSWORD=${BENTO_DATABASE_PASSWORD}
        --set config.BENTO_DATABASE_PORT=${BENTO_DATABASE_PORT}
        --set config.BENTO_DATABASE_SSL_FLAG=${BENTO_DATABASE_SSL_FLAG}
        --set config.BENTO_DATABASE_USER=${BENTO_DATABASE_USER}
        --set config.BENTO_DATABASE_URL=${BENTO_DATABASE_URL}
        --set config.BENTO_DOCS_DATABASE_NAME=${BENTO_DOCS_DATABASE_NAME}
        --set config.BENTO_DOCUMENTS_URL=${BENTO_DOCUMENTS_URL}
        --set config.DATA_ENDPOINT=${DATA_ENDPOINT}
        --set config.MERGE_ENDPOINT=${MERGE_ENDPOINT}
        --set config.ICG_APIKEY=${ICG_APIKEY}
        --set config.ICG_GATEWAYLIVEMODE=${ICG_GATEWAYLIVEMODE}
        --set config.ICG_SITEID=${ICG_SITEID}
        --set config.ICG_SITEKEY=${ICG_SITEKEY}
        --set config.STRIPE_PK=${STRIPE_PK}
        --set config.STRIPE_SK=${STRIPE_SK}
        --set config.FINIX_APK_TEST=${FINIX_APK_TEST}
        --set config.FINIX_APK_LIVE=${FINIX_APK_LIVE}
        --set config.CURRENT_ENV=${CURRENT_ENV}
        --set imageName=$REPOSITORY_URI
        --set imageTag=$COMMIT_SHORT
        --set config.ICG_SITEID_DREAM=${ICG_SITEID_DREAM}
        --set config.ICG_SITEKEY_DREAM=${ICG_SITEKEY_DREAM}
        --set config.ICG_APIKEY_DREAM=${ICG_APIKEY_DREAM}
        --set ingress.foundationGroup=${FOUNDATIONGROUP_HOST}
        --set resources.memory=1500Mi
        --set resources.cpu=1000m
      - echo Helm Upgrading Bento Merge Service...
      - cd ${CODEBUILD_SRC_DIR}/_SERVICES/helm/merge-app
      - echo Installing Version $REPOSITORY_URI_MERGE:$COMMIT_SHORT
      - >
        helm upgrade bento-merge-service ./ --install
        --set config.BENTO_DATABASE_NAME=${BENTO_DATABASE_NAME}
        --set config.BENTO_DATABASE_PASSWORD=${BENTO_DATABASE_PASSWORD}
        --set config.BENTO_DATABASE_PORT=${BENTO_DATABASE_PORT}
        --set config.BENTO_DATABASE_SSL_FLAG=${BENTO_DATABASE_SSL_FLAG}
        --set config.BENTO_DATABASE_USER=${BENTO_DATABASE_USER}
        --set config.BENTO_DATABASE_URL=${BENTO_DATABASE_URL}
        --set config.BENTO_DOCS_DATABASE_NAME=${BENTO_DOCS_DATABASE_NAME}
        --set config.BENTO_DOCUMENTS_URL=${BENTO_DOCUMENTS_URL}
        --set config.DATA_ENDPOINT=${DATA_ENDPOINT}
        --set config.MERGE_ENDPOINT=${MERGE_ENDPOINT}
        --set config.ICG_APIKEY=${ICG_APIKEY}
        --set config.ICG_GATEWAYLIVEMODE=${ICG_GATEWAYLIVEMODE}
        --set config.ICG_SITEID=${ICG_SITEID}
        --set config.ICG_SITEKEY=${ICG_SITEKEY}
        --set config.STRIPE_PK=${STRIPE_PK}
        --set config.STRIPE_SK=${STRIPE_SK}
        --set config.FINIX_APK_TEST=${FINIX_APK_TEST}
        --set config.FINIX_APK_LIVE=${FINIX_APK_LIVE}
        --set config.CURRENT_ENV=${CURRENT_ENV}
        --set imageName=$REPOSITORY_URI_MERGE
        --set imageTag=$COMMIT_SHORT
        --set config.ICG_SITEID_DREAM=${ICG_SITEID_DREAM}
        --set config.ICG_SITEKEY_DREAM=${ICG_SITEKEY_DREAM}
        --set config.ICG_APIKEY_DREAM=${ICG_APIKEY_DREAM}
        --set resources.memory=1000Mi
        --set resources.cpu=150m
