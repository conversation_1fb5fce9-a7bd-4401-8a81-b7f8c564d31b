_BUILD/
_SERVICES/app/src
_SERVICES/merge/src
_SERVICES/pdf_generation/src
_SRC/pagoda/vendor/
_SERVICES/test
_SERVICES/test/src/
_SERVICES/test/test/
_SERVICES/test/package-lock.json
_SERVICES/test/server.js
_SERVICES/test/test/_setup.spec.js
pagoda/app/
pagoda/_files/
pagoda/_coredev/vendor/
node_modules/
build/
pagoda/_coredev/composer.lock
pagoda/_coredev/tmp-pdf-generation/
semantic/
.nova/
.idea/

# Compiled source #
###################
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs and databases #
######################
*.log
*.sqlite

# OS generated files #
######################
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.appconfig*.yaml
*.patch
