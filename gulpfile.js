// plugins
var gulp = require('gulp');
//var args   = require('yargs').argv;
var runSequence = require('run-sequence');
var concat = require('gulp-concat');
var pump = require('pump');
var uglify = require('gulp-uglify-es').default;
var cleanCSS = require('gulp-clean-css');
var semanticWatch = require('./semantic/tasks/watch');
var exec = require('child_process').exec;
var mocha = require('gulp-mocha');
var toc = require('gulp-markdown-toc');
/*
var semanticCSS = require('./semantic/tasks/build/css');
var semanticJS = require('./semantic/tasks/build/javascript');
*/
var semanticBuild = require('./semantic/tasks/build');
var ts = require("gulp-typescript");
var tsProject = ts.createProject("tsconfig.json");
var del = require('del');

var versionNumber = 'dev';
var DBPATH = 'localhost';

require('dotenv').config();
var replace = require('gulp-string-replace');

// get package.json file
var pkg = require('./package.json');
var fs = require('fs');

// fetch command line arguments
const arg = (argList => {

	let arg = {}, a, opt, thisOpt, curOpt;
	for (a = 0; a < argList.length; a++) {

		thisOpt = argList[a].trim();
		opt = thisOpt.replace(/^\-+/, '');

		if (opt === thisOpt) {

			// argument value
			if (curOpt) arg[curOpt] = opt;
			curOpt = null;

		} else {

			// argument name
			curOpt = opt;
			arg[curOpt] = true;

		}

	}

 	return arg;

})(process.argv);

if (arg.buildNumber) {
	versionNumber = arg.buildNumber;
}

if (process.env.DB_PATH) {
	DBPATH = process.env.DB_PATH;
}

gulp.task('clean', function(done) {
	del(['./_BUILD/**', '!./_BUILD', './_SERVICES/app/src/**', '!./_SERVICES/app/src']);
	done();
});

gulp.task("typescript", function () {
    return tsProject.src()
        .pipe(tsProject())
        .js.pipe(gulp.dest("_BUILD/ts"));
});

gulp.task('productionCSS', function (cb) {
  pump([
      gulp.src([
	    './semantic/dist/semantic.min.css',
	    './node_modules/chart.js/dist/Chart.min.css',
	    './node_modules/medium-editor/dist/css/medium-editor.min.css',
	    './node_modules/medium-editor-tables/dist/css/medium-editor-tables.min.css',
		'./node_modules/jquery-contextmenu/dist/jquery.contextMenu.min.css',
		'./node_modules/sweetalert2/dist/sweetalert2.min.css',
    './node_modules/frappe-gantt/dist/frappe-gantt.css',
      	'./_SRC/notify/_css/*.css',
	  ]),
      concat('pagoda.min-'+versionNumber+'.css'),
      cleanCSS({debug: true}, (details) => {
	    console.log(`${details.name}: ${details.stats.originalSize}`);
	    console.log(`${details.name}: ${details.stats.minifiedSize}`);
	  }),
      gulp.dest('_SERVICES/app/src/css')
    ],
    cb
  );
});

gulp.task('developmentCSS', function (cb) {
  pump([
      gulp.src([
	    './semantic/dist/semantic.css',
	    './node_modules/chart.js/dist/Chart.css',
	    './node_modules/medium-editor/dist/css/medium-editor.css',
 	    './node_modules/medium-editor-tables/dist/css/medium-editor-tables.css',
		'./node_modules/jquery-contextmenu/dist/jquery.contextMenu.css',
		'./node_modules/sweetalert2/dist/sweetalert2.min.css',
    './node_modules/frappe-gantt/dist/frappe-gantt.css',
/*
 	    './node_modules/quill/dist/quill.core.css',
 	    './node_modules/quill/dist/quill.snow.css',
*/
     	'./_SRC/notify/_css/*.css',
	  ]),
      concat('pagoda.min-'+versionNumber+'.css'),
      gulp.dest('_SERVICES/app/src/css')
    ],
    cb
  );
});

// bento ENVIROMENT
gulp.task('environment-js', function (done) {

	del(['./_SRC/merge/src/modules/environment/**', '!./_SRC/merge/src/modules/environment']);

	   // Replace each placeholder with the correct value for the variable.
	   gulp.src('_SRC/merge/src/modules/environment.js')
		.pipe(replace('REPLACE_TEST_ENV', process.env.TEST_ENVIRONMENT))
		.pipe(replace('MERGE_END_POINT', process.env.MERGE_ENDPOINT))
		.pipe(replace('DATA_END_POINT', process.env.API_ENDPOINT))
	    .pipe(gulp.dest('_SRC/merge/src/modules/environment'));

		done();

		console.log("environment JS END");
});
gulp.task('build-docs', function (cb) {
	  pump([
		  gulp.src([
		  	'./_DOCS/_docsSetup.md',
		  	'./_DOCS/core-concepts.md',
			'./_DOCS/api.md',
			'./_DOCS/notify/notify.md',
			'./_DOCS/notify/**/*.md',
			'./_DOCS/pagoda/*.md',
			'./_DOCS/pagoda/rules/*.md'
		  ]),
		  concat('readme.md'),
		  toc(),
		  gulp.dest('./')
		],
		cb
	  );
	});

// bento production
gulp.task('productionEmbed', function (cb) {
	pump([
		gulp.src([
			'./node_modules/moment/moment.js',
			'./node_modules/moment-timezone/builds/moment-timezone-with-data.min.js',
			'_BUILD/ts/_factory/*.js',
			'_BUILD/ts/_factory/domTools/*.js',
			'_BUILD/ts/_factory/pagodaTemplates/*.js',
			'_BUILD/ts/_embedExtensions/*.js',
			'_BUILD/ts/_components/_tlb/requests.js'
		]),
		concat('embed.pagoda.min.js'),
		uglify(),
		gulp.dest('_SERVICES/app/src/js')
	],cb);
});

// bento development embed
gulp.task('developmentEmbed', function (cb) {
	pump([
		gulp.src([
			'./node_modules/moment/moment.js',
			'./node_modules/moment-timezone/builds/moment-timezone-with-data.min.js',
			'_BUILD/ts/_factory/*.js',
			'_BUILD/ts/_factory/domTools/*.js',
			'_BUILD/ts/_factory/pagodaTemplates/*.js',
			'_BUILD/ts/_embedExtensions/*.js',
			'_BUILD/ts/_components/_tlb/requests.js'
		]),
		concat('embed.pagoda.dev.min.js'),
		uglify(),
		gulp.dest('_SERVICES/app/src/js')
	],cb);
});

// bento production
gulp.task('productionJS', function (cb) {
  pump([
      gulp.src([
	    './node_modules/jquery/dist/jquery.min.js',
	    './node_modules/jquery-contextmenu/dist/jquery.contextMenu.min.js',
	    './node_modules/jquery-contextmenu/dist/jquery.ui.position.min.js',
	    './node_modules/jquery-csv/src/jquery.csv.min.js',
	    './node_modules/underscore/underscore-min.js',
	    './node_modules/crypto-js/crypto-js.js',
	    '_SRC/notify/_libraries/*.js',
	    './node_modules/html2canvas/dist/html2canvas.min.js',
	    './node_modules/medium-editor/dist/js/medium-editor.min.js',
	    './node_modules/medium-editor-tables/dist/js/medium-editor-tables.min.js',
	    './node_modules/moment/moment.js',
	    './node_modules/moment-timezone/builds/moment-timezone-with-data.min.js',
	    './node_modules/chart.js/dist/Chart.min.js',
	    './node_modules/chartjs-plugin-zoom/dist/chartjs-plugin-zoom.min.js',
      './node_modules/chartjs-gauge/dist/chartjs-gauge.min.js',
      './node_modules/snapsvg/dist/snap.svg-min.js',
      './node_modules/frappe-gantt/dist/frappe-gantt.min.js',
		'./node_modules/quill/dist/quill.min.js',
		'./node_modules/quill-magic-url/dist/index.js',
		'./node_modules/sweetalert2/dist/sweetalert2.min.js',
		'./node_modules/canvas2image/canvas2image.js',
	    './semantic/dist/semantic.min.js',
      	'_BUILD/ts/_factory/*.js',
      	'_BUILD/ts/_factory/domTools/*.js',
      	'_BUILD/ts/_factory/pagodaTemplates/*.js',
      	'_BUILD/ts/_extensions/*.js',
      	'_SRC/notify/_components/_core/tool.js',
      	'_BUILD/ts/_components/_projectManagement/*.js',
      	'!_BUILD/ts/_components/_core/_app.js',
      	'_BUILD/ts/_components/_core/*.js',
      	'_BUILD/ts/_components/_core/_fields/*.js',
      	'_BUILD/ts/_components/_core/_collections/*.js',
      	'_BUILD/ts/_components/_core/_actions/*.js',
      	'_BUILD/ts/_components/_core/_conditions/*.js',
      	'_BUILD/ts/_components/_cms/*.js',
      	'_BUILD/ts/_components/_bizdev/*.js',
      	'_BUILD/ts/_components/_staffing/*.js',
      	'_BUILD/ts/_components/inventory/*.js',
      	'_BUILD/ts/_components/_tlb/requests.js',
      	'_BUILD/ts/_components/_tlb/lifebookmetrics.js',
      	'_BUILD/ts/_components/_tlb/lifebooktgi.js',
		'_BUILD/ts/_components/_tlb/submittedForms.js',
		'_SRC/merge/src/modules/environment/environment.js'
	  ]),
      concat('bento.pagoda.min-'+versionNumber+'.js'),
      uglify(),
      gulp.dest('_SERVICES/app/src/js')
    ],
    cb
  );
});

gulp.task('dev-js', function (cb) {
	//  #1712: This is where the js code is being moved into the
	//  service directory. This is where the js code can be made
	//  available to the new merge service.
  pump([
      gulp.src([
        './node_modules/jquery/dist/jquery.js',
        './node_modules/jquery-contextmenu/dist/jquery.contextMenu.js',
	    './node_modules/jquery-contextmenu/dist/jquery.ui.position.js',
        './node_modules/jquery-csv/src/jquery.csv.js',
        './node_modules/underscore/underscore.js',
	    './node_modules/crypto-js/crypto-js.js',
	    '_SRC/notify/_libraries/*.js',
	    './node_modules/html2canvas/dist/html2canvas.js',
	    './node_modules/medium-editor/dist/js/medium-editor.js',
	    './node_modules/medium-editor-tables/dist/js/medium-editor-tables.js',
	    './node_modules/moment/moment.js',
	    './node_modules/moment-timezone/builds/moment-timezone-with-data.min.js',
        './node_modules/chart.js/dist/Chart.js',
        './node_modules/chartjs-plugin-zoom/dist/chartjs-plugin-zoom.js',
        './node_modules/chartjs-gauge/dist/chartjs-gauge.min.js',
        './node_modules/snapsvg/dist/snap.svg-min.js',
        './node_modules/frappe-gantt/dist/frappe-gantt.min.js',
		'./node_modules/quill/dist/quill.js',
		'./node_modules/quill-magic-url/dist/index.js',
		'./node_modules/sweetalert2/dist/sweetalert2.min.js',
	    './node_modules/canvas2image/canvas2image.js',
	    './semantic/dist/semantic.min.js',
      	'_SRC/notify/_factory/*.js',
      	'_SRC/notify/_factory/domTools/*.js',
      	'_SRC/notify/_factory/pagodaTemplates/*.js',
      	'_SRC/notify/_extensions/*.js',
      	'_SRC/notify/_components/_core/tool.js',
      	'_SRC/notify/_components/_projectManagement/*.js',
      	'!_SRC/notify/_components/_core/_app.js',
      	'_SRC/notify/_components/_core/*.js',
      	'_SRC/notify/_components/_core/_fields/*.js',
      	'_SRC/notify/_components/_core/_collections/*.js',
      	'_SRC/notify/_components/_core/_actions/*.js',
      	'_SRC/notify/_components/_core/_conditions/*.js',
      	'_SRC/notify/_components/_cms/*.js',
      	'_SRC/notify/_components/_bizdev/*.js',
      	'_SRC/notify/_components/_staffing/*.js',
      	'_SRC/notify/_components/inventory/*.js',
      	'_SRC/notify/_components/_tlb/requests.js',
      	'_SRC/notify/_components/_tlb/lifebookmetrics.js',
      	'_SRC/notify/_components/_tlb/lifebooktgi.js',
		'_SRC/notify/_components/_tlb/*.js',
		'_SRC/merge/src/modules/environment/environment.js'
	  ]),
      concat('bento.pagoda.min-'+versionNumber+'.js'),
      gulp.dest('_SERVICES/app/src/js')
    ],
    cb
  );

});


// 'test' task for endpoint tests
gulp.task('test', function (cb) {

	// Pass test scripts to the mocha
	// testing library
	gulp.src(
		'_SERVICES/test/test'
		, {read:false}
	).pipe(

		mocha({
			reporter: 	'nyan'
			, exit: 	true
			// , ui: 		'tdd'
		})

	// Supress error output
	).on(
		'error'
		, function () {}
	);
	cb();

});

// Spec
gulp.task('spec', function (cb) {

	// Pass test scripts to the mocha
	// testing library
	gulp.src(
		'_SERVICES/test/test'
		, {read:false}
	).pipe(

		mocha({
			reporter: 	'spec'
			, exit: 	true
			// , ui: 		'tdd'
		})

	// Supress error output
	).on(
		'error'
		, function () {}
	);
	cb();

});

// bin production
gulp.task('BINproductionJS', function (cb) {
	pump([
      	gulp.src([
			'_SRC/notify/_libraries/*.js',
			'./semantic/dist/semantic.min.js',
			'_BUILD/ts/_factory/*.js',
			'_BUILD/ts/_factory/domTools/*.js',
			'_BUILD/ts/_factory/pagodaTemplates/*.js',
			'_BUILD/ts/_extensions/*.js',
			'_BUILD/ts/_components/_core/*.js',
			'_BUILD/ts/_components/_core/_fields/*.js',
			'_BUILD/ts/_components/_core/_collections/*.js',
			'_BUILD/ts/_components/_core/_actions/*.js',
			'_BUILD/ts/_components/_core/_conditions/*.js',
			'_BUILD/ts/_components/_cms/*.js',
			'_BUILD/ts/_components/_bizdev/*.js',
			'_BUILD/ts/_components/_staffing/*.js',
			'_BUILD/ts/_components/inventory/*.js',
			'_BUILD/ts/_components/_events/*.js'
	  	]),
      	concat('bin.pagoda.min-'+versionNumber+'.js'),
      	uglify(),
      	gulp.dest('_SERVICES/app/src/js')
    ],cb);
});

// tlb production
gulp.task('TLBproductionJS', function (cb) {
	pump([
    	gulp.src([
			'_SRC/notify/_libraries/*.js',
			'./semantic/dist/semantic.min.js',
			'_BUILD/ts/_factory/*.js',
			'_BUILD/ts/_factory/domTools/*.js',
			'_BUILD/ts/_factory/pagodaTemplates/*.js',
			'_BUILD/ts/_extensions/*.js',
			'_BUILD/ts/_components/_core/*.js',
			'_BUILD/ts/_components/_core/_fields/*.js',
			'_BUILD/ts/_components/_core/_collections/*.js',
			'_BUILD/ts/_components/_core/_actions/*.js',
			'_BUILD/ts/_components/_core/_conditions/*.js',
			'_BUILD/ts/_components/_cms/*.js',
			'_BUILD/ts/_components/_tlb/*.js'
	  	]),
		concat('tlb.pagoda.min-'+versionNumber+'.js'),
		uglify(),
		gulp.dest('_SERVICES/app/src/js')
    ],cb);
});

gulp.task('setupSemantic', function(done){
	pump([
    	gulp.src([
      		'_SRC/semantic/site/**/*'
	  	]),
      	gulp.dest(
      		'semantic/src/site/'
      	)
    ],
    function(complete){
	    pump([
	    	gulp.src([
	      		'_SRC/semantic/themes/**/*'
			]),
	    	gulp.dest(
	      		'semantic/src/themes/'
	      	)
	    ],
	    function(){
			done();
	    }
    )}
  );
});

gulp.task('build-dev-js', gulp.series('dev-js', function(done){
	done();
}));

gulp.task('build-production-css', gulp.series('productionCSS', function(done){
	done();
}));

gulp.task('start-development', gulp.series('build-dev-js','build-production-css', function(done){
	done();
}));

gulp.task('moveIcons', function (cb) {
	pump([
    	gulp.src([
      		'semantic/src/themes/default/assets/fonts/**'
		]),
      	gulp.dest(
      		'_SERVICES/app/src/css/themes/default/assets/fonts/'
      	)
    ],cb);
});

gulp.task('moveFontAwesome', function (cb) {
	pump([
		gulp.src([
			'_SRC/notify/webfonts//*'
		]),
		gulp.dest(
			'_SERVICES/app/src/webfonts/'
		)
	],cb);
});

gulp.task('moveFonts', gulp.series('moveIcons', 'moveFontAwesome', function (cb) {
	pump([
    	gulp.src([
			'_SRC/notify/fonts//*'
	  	]),
      	gulp.dest(
			'_SERVICES/app/src/fonts/'
      	)
	],cb);
}));

gulp.task('move-login-portal', function(cb){
	pump([
		gulp.src([
			'_SRC/app/index.php'
		]),
		gulp.dest(
			'_SERVICES/app/src/'
		)
    ],cb);
});

gulp.task('moveAppFiles', function (cb) {
  	pump([
    	gulp.src([
      		'_SRC/app/**',
      		'_SRC/app/index.php',
      		'_SRC/app/.htaccess'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/app/'
      	)
    ],cb);
});

gulp.task('moveHelpers', function (cb) {
  	pump([
    	gulp.src([
      		'_SRC/notify/_helpers/**',
	  	]),
    	gulp.dest(
      		'_SERVICES/app/src/'
      	)
    ],cb);
});

gulp.task('moveAPIFiles', function (cb) {
  	pump([
    	gulp.src([
      		'_SRC/pagoda/**',
      		'!_SRC/pagoda/vendor'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/'
      	)
    ], cb);
});

gulp.task('moveMergeService', function (cb) {
	pump([
	  	gulp.src([
			'_SRC/merge/**',
		]),
		gulp.dest(
			'_SERVICES/merge/'
		),
  	], cb);
});

gulp.task('moveDatabaseMergeService', function (cb) {
	pump([
	  	gulp.src([
			'_SRC/notify/_extensions/_database.js',
			'_SRC/notify/_extensions/_urlData.js',
			'_SRC/notify/_extensions/files.js',
			'_SRC/notify/_factory/_sandbox.js',
			'_SRC/notify/_factory/_core.js'
		]),
		gulp.dest(
			'_SERVICES/merge/src/modules/'
		)
  	],cb);
});

gulp.task('moveFieldsMergeService', function (cb) {
  	pump([
		gulp.src([
			'_SRC/notify/_components/_core/_fields/**'
		]),
		gulp.dest(
			'_SERVICES/merge/src/modules/tags'
		)
	], cb);
});

gulp.task('moveCronFiles', function (cb) {
  	pump([
    	gulp.src([
      		'_SRC/pagoda/cron/*.php'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/cron/'
      	)
    ],cb);
});

gulp.task('moveRulesFiles', function (cb) {
	pump([
      	gulp.src([
      		'_SRC/pagoda/rules/**/*.php'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/rules/'
      	)
    ],cb);
});

gulp.task('moveServicesFiles', function (cb) {
	pump([
      	gulp.src([
      		'_SRC/pagoda/services/*.php'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/services/'
      	)
    ],cb);
});

gulp.task('moveViewsFiles', function (cb) {
	pump([
      	gulp.src([
      		'_SRC/pagoda/views/**/*'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/views/'
      	)
    ],cb);
});

gulp.task('moveIncomingFiles', function (cb) {
	pump([
	  gulp.src([
			'_SRC/pagoda/incoming/*.php'
		]),
		gulp.dest(
			'_SERVICES/app/src/api/incoming/'
		)
  ],cb);
});

gulp.task('moveCaldavFiles', function (cb) {
  	pump([
    	gulp.src([
      		'_SRC/pagoda/caldav/*.php'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/caldav/'
      	)
    ],cb);
});

gulp.task('moveAPIFilesWatch', function (cb) {
	pump([
    	gulp.src([
			'_SRC/pagoda/files/**'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/files/'
      	)
    ],cb);
	pump([
    	gulp.src([
			'_SRC/pagoda/objs/**/*.php'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/objs/'
      	)
    ],cb);
	pump([
    	gulp.src([
			'!_SRC/pagoda/vendor',
			'!_SRC/pagoda/lib',
			'!_SRC/pagoda/phpseclib',
			'!_SRC/pagoda/quickbooks',
			'_SRC/pagoda/custom_scripts/**',
			'_SRC/pagoda/*.php'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/'
      	)
    ],cb);
	pump([
    	gulp.src([
			'_SRC/pagoda/stylesheet.css'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/'
      	)
    ],cb);
	pump([
    	gulp.src([
			'_SRC/pagoda/stylesheet-erationalmarketing.css'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/'
      	)
    ],cb);
	pump([
    	gulp.src([
			'_SRC/pagoda/stylesheet-foundation_group.css'
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/'
      	)
    ],cb);
});

gulp.task('moveMergeServiceWatch', function (cb) {
	pump([
    	gulp.src([
			'_SRC/merge/src/**'
	  	]),
      	gulp.dest(
      		'_SERVICES/merge/src/'
      	)
    ],cb);
});

gulp.task('moveDatabaseMergeServiceWatch', function (cb) {
	pump([
    	gulp.src([
			'_SRC/notify/_extensions/_database.js',
			'_SRC/notify/_extensions/_urlData.js',
			'_SRC/notify/_extensions/files.js',
			'_SRC/notify/_factory/_sandbox.js',
			'_SRC/notify/_factory/_core.js'
	  	]),
      	gulp.dest(
      		'_SERVICES/merge/src/modules/'
      	)
    ],cb);
});

gulp.task('moveFieldsMergeServiceWatch', function (cb) {
	pump([
		gulp.src([
			'_SRC/notify/_components/_core/_fields/**'
		]),
		gulp.dest(
			'_SERVICES/merge/src/modules/tags'
		)
	], cb);
});

gulp.task('moveBlueprints', function (cb) {
  	pump([
    	gulp.src([
      		'_SRC/blueprints/*.json',
	  	]),
      	gulp.dest(
      		'_SERVICES/app/src/api/blueprints/'
      	)
    ],cb);
});

gulp.task('moveImages', function (cb) {
	pump([
    	gulp.src([
      		'_SRC/notify/_images/**/*',
	  	]),
      	gulp.dest(
			'_SERVICES/app/src/_images'
		)
    ],cb);
});

gulp.task('moveServiceWorkers', function (cb) {
  	pump([
    	gulp.src([
      		'_SRC/notify/_serviceWorkers/**/*',
	  	]),
      	gulp.dest('_SERVICES/app/src/')
    ],cb);
});

gulp.task('move-files', gulp.series('moveFonts', 'moveAPIFiles','moveMergeService', 'moveDatabaseMergeService', 'moveFieldsMergeService', 'moveAppFiles', 'moveHelpers', 'moveServiceWorkers', 'moveBlueprints', 'moveCronFiles', 'moveRulesFiles', 'moveServicesFiles', 'moveViewsFiles', 'moveCaldavFiles', function(cb){
	pump([
		gulp.src([
			'_SRC/app/fonts/**',
			'_SRC/app/notify/webfonts/**',
			'_SRC/app/favicons/**',
			'_SRC/app/index.php'
		]),
		gulp.dest(
			'_SERVICES/app/src/'
		)
    ],cb);
}));


gulp.task('build', gulp.series('setupSemantic','start-development', 'move-files', function(done){
	done();
}));

gulp.task('build-production-js', gulp.series('typescript', 'productionJS', 'productionEmbed', function(done){
	done();
}));

// Add development build task that includes the new developmentEmbed task
gulp.task('build-development-js', gulp.series('typescript', 'dev-js', 'developmentEmbed', function(done){
	done();
}));

gulp.task('start-production', gulp.series('build-production-js', 'build-production-css', function(done){
	done();
}));

gulp.task('start-development', gulp.series('build-development-js', 'developmentCSS', function(done){
	done();
}));

gulp.task('start-app-production', gulp.series('start-production', function(done){
	done();
}));

gulp.task('build-bento-app', gulp.series('start-app-production', function(done){
	done();
}));

gulp.task('watchui', semanticWatch);
gulp.task('buildui', semanticBuild);


gulp.task('build-bento', gulp.series('clean', 'setupSemantic', 'build-bento-app', 'move-files', function(done){
	done();
}));

gulp.task('default', gulp.series('clean', 'environment-js', 'start-development', 'setupSemantic', 'start-development', 'move-files', function(done){
	done();
}));

function watchFiles(){

    gulp.watch('_SRC/notify/_css/*.css', gulp.series('developmentCSS'));

    gulp.watch('_SRC/notify/fonts/**/*', gulp.series('moveFonts'));
	gulp.watch('_SRC/notify/fonts/**/*', gulp.series('moveIcons'));
	gulp.watch('_SRC/notify/webfonts/**/*', gulp.series('moveIcons'));
    gulp.watch('_SRC/notify/_images/**/*', gulp.series('moveImages'));

    gulp.watch('_SRC/notify/_factory/*.js', gulp.series('dev-js'));
    gulp.watch('_SRC/notify/_factory/domTools/*.js', gulp.series('dev-js'));
    gulp.watch('_SRC/notify/_factory/pagodaTemplates/*.js', gulp.series('dev-js'));
    gulp.watch('_SRC/notify/_extensions/*.js', gulp.series('dev-js'));
	gulp.watch('_SRC/notify/_components/**/*.js', gulp.series('dev-js'));
	gulp.watch('_SRC/test/**/*.js', gulp.series('dev-js'));

    gulp.watch('_SRC/notify/_serviceWorkers/**/*.js', gulp.series('moveServiceWorkers'));

    gulp.watch('_SRC/pagoda/*.php', gulp.series('moveAPIFilesWatch'));
	gulp.watch('_SRC/pagoda/**/**.php', gulp.series('moveIncomingFiles'));
    gulp.watch('_SRC/pagoda/objs/**/*.php', gulp.series('moveAPIFilesWatch'));
    gulp.watch('_SRC/pagoda/*.css', gulp.series('moveAPIFilesWatch'));
    gulp.watch('_SRC/pagoda/files/*.php', gulp.series('moveAPIFilesWatch'));
    gulp.watch('_SRC/pagoda/cron/*.php', gulp.series('moveCronFiles'));
	gulp.watch('_SRC/pagoda/rules/**/**.php', gulp.series('moveRulesFiles'));
	gulp.watch('_SRC/pagoda/services/**.php', gulp.series('moveServicesFiles'));
	gulp.watch('_SRC/pagoda/views/**/**.php', gulp.series('moveViewsFiles'));
    gulp.watch('_SRC/pagoda/caldav/*.php', gulp.series('moveCaldavFiles'));
    gulp.watch('_SRC/pagoda/custom_scripts/*.php', gulp.series('moveAPIFilesWatch'));
    gulp.watch('_SRC/blueprints/*.json', gulp.series('moveBlueprints'));
    gulp.watch('_SRC/app/index.php', gulp.series('moveAppFiles'));
    gulp.watch('_SRC/app/_go/*.php', gulp.series('moveAppFiles'));
	gulp.watch('_SRC/app/index.php', gulp.series('move-login-portal'));

	gulp.watch('_SRC/merge/**/**.js', gulp.series('moveMergeServiceWatch'));
	gulp.watch('_SRC/notify/_extensions/_database.js', gulp.series('moveDatabaseMergeServiceWatch'));
	gulp.watch('_SRC/notify/_extensions/_urlData.js', gulp.series('moveDatabaseMergeServiceWatch'));
	gulp.watch('_SRC/notify/_extensions/files.js', gulp.series('moveDatabaseMergeServiceWatch'));
	gulp.watch('_SRC/notify/_factory/_sandbox.js', gulp.series('moveDatabaseMergeServiceWatch'));
	gulp.watch('_SRC/notify/_factory/_core.js', gulp.series('moveDatabaseMergeServiceWatch'));
	gulp.watch('_SRC/notify/_components/_core/_fields/**', gulp.series('moveFieldsMergeServiceWatch'));

}

// development watch task
const watch = gulp.parallel(watchFiles);

exports.watch = watch;
/*
gulp.task('allProduction', ['moveFonts', 'moveIcons', 'moveImages', 'productionCSS', 'allProductionJS']);
gulp.task('bentoProduction', ['moveFonts', 'moveIcons', 'moveImages', 'productionCSS', 'productionJS']);
gulp.task('tlbProduction', ['moveFonts', 'moveIcons', 'moveImages', 'productionCSS', 'TLBproductionJS']);

gulp.task('tlb', ['moveFonts', 'moveIcons', 'moveImages', 'developmentCSS', 'TLBdevelopmentJS']);
*/
