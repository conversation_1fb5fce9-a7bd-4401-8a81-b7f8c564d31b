# API

## Services

### Registering a New Service

## Data Layer

### Basic Concepts

#### Selection Objects

Selection objects are used to tell the api exactly what properties (and child object data) to pull in in a given request. In the **sb.data.db.obj.where**, this can be added to the query directly at **where.childObjs**.

For example: f you are looking at **time_entries**, and want to pull in related users objects (at the **staff** property) and the profile image (at the **profile_image** property on the staff object), you can set the selection object to:

```
{
  staff: {
	profile_image: true
  }
}
```



**NOTE:** *Property definitions on all hard-coded object types in the system can be found in* ***_SRC/blueprints\*** *in json files.*

#### Where Objects

#### How do I filter a set where records are tagged with any tag in a given set, AND another specific tag?

To filter a set where records are tagged with any of a tag set [1, 2, 3] ***AND\*** contain another tag [4] tag, we can combine two **tagged_with** filters to build a where clause for our query like this:

```
where = {
  tagged_with: {
	type:     'any'
	, values: [1, 2, 3]
	, and:    {
	  tagged_with: [4]
	}
  }
};
```

### Methods

#### objs->create

`create (String $objectType = '', Array $objectData = null, ...)` 

The create method is used to establish a new record (of any type) in the database. 