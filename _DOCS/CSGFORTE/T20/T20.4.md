# T20.4: Document API Keys and Endpoints

**Task:** Document API keys and endpoints  
**Role:** Developer (D)  
**Status:** Dependent on T20.1-T20.3 completion  

## Overview
This document serves as the central repository for all CSG Forte credentials, endpoints, and configuration details needed for the Bento platform integration.

## ⚠️ SECURITY WARNING
**This file contains sensitive credentials. Ensure proper access controls are in place.**

---

## Sandbox Environment Credentials

### Organization & Location Details
```
Organization ID: org_499921
Location ID: loc_401809
Organization Name: Infinity Hospitality Group
Legal Name: Infinity Hospitality Group
Environment: Sandbox/Testing
Status: Live
Time Zone: Pacific Standard Time (PST)
Created: Jul 30, 2025
Primary Contact: <PERSON> (<EMAIL>)
Parent Organization: Test ISO ID 234894
```

### API Authentication Credentials
```
API Access ID: 8adfb585f7a7cde0168cbebc52ccc1b2
API Secure Key: 4bbb4b9fbc8e58d47eddeab8030d190d
Credential Name: "Bento Payment Integration"
Created Date: [COMPLETED T20]
Webhook URL: https://eon72wfrwg90ydj.m.pipedream.net
```

### Base URIs and Endpoints
```
Sandbox Base URI: https://sandbox.forte.net/api/v3
Production Base URI: https://api.forte.net/v3 (for future use)

Checkout v2 Sandbox: https://sandbox.forte.net/checkout/v2/js
Checkout v2 Production: https://checkout.forte.net/v2/js (for future use)
```

---

## Production Environment Credentials
**Note:** These will be obtained later during production deployment (T33)

### **Production Setup Process**
Unlike sandbox (self-service), production requires a formal merchant account with CSG Forte:

1. **Apply for Merchant Account**
   - Complete merchant application with business information (legal name, address, tax ID, bank account details)
   - Contact CSG Forte Sales Team or apply through merchant account process
   - Undergo underwriting and approval process (typically 48 hours or less)

2. **Receive Production Credentials**
   - CSG Forte will provide your production Organization ID and Location ID
   - **These will be different from sandbox credentials**
   - You get access to **production Dex portal** (separate from sandbox)

3. **Create Production API Credentials**
   - Sandbox credentials do NOT carry over to production
   - Use production Dex portal to generate new API Access ID and API Secure Key
   - Process is same as sandbox credential creation

4. **Update Integration Settings**
   - Change Base URI to: `https://api.forte.net/v3` (remove /sandbox)
   - Update all environment variables with production credentials

### **Production Credentials (To Be Obtained)**
```
Organization ID: [TO BE OBTAINED FROM PRODUCTION DEX]
Location ID: [TO BE OBTAINED FROM PRODUCTION DEX]
API Access ID: [TO BE CREATED IN PRODUCTION DEX]
API Secure Key: [TO BE CREATED IN PRODUCTION DEX]
```

### **Production Contact Information**
- **CSG Forte Sales:** Request merchant account application
- **Technical Support:** 888-235-4635 for setup assistance
- **Partner Support:** <EMAIL>

---

## Environment Variable Configuration

### For Local Development (localhost:8080)
```bash
# CSG Forte Sandbox
CSGFORTE_ENVIRONMENT=sandbox
CSGFORTE_BASE_URI=https://sandbox.forte.net/api/v3
CSGFORTE_ORG_ID=org_499921
CSGFORTE_LOCATION_ID=loc_401809
CSGFORTE_API_ACCESS_ID=8adfb585f7a7cde0168cbebc52ccc1b2
CSGFORTE_API_SECURE_KEY=4bbb4b9fbc8e58d47eddeab8030d190d

# Checkout v2 Integration
CSGFORTE_CHECKOUT_URL=https://sandbox.forte.net/checkout/v2/js

# Webhook for Testing
CSGFORTE_WEBHOOK_URL=https://eon72wfrwg90ydj.m.pipedream.net
```

### For Staging Environment (bento-dev.infinityhospitality.net)
```bash
# Same as sandbox initially
CSGFORTE_ENVIRONMENT=sandbox
CSGFORTE_BASE_URI=https://api.forte.net/v3/sandbox
# ... (same credentials as above)
```

### For Production Environment (bento.infinityhospitality.net)
```bash
# To be configured in T33 after merchant account approval
CSGFORTE_ENVIRONMENT=production
CSGFORTE_BASE_URI=https://api.forte.net/v3
CSGFORTE_ORG_ID=[FROM_PRODUCTION_DEX_AFTER_MERCHANT_APPROVAL]
CSGFORTE_LOCATION_ID=[FROM_PRODUCTION_DEX_AFTER_MERCHANT_APPROVAL]
CSGFORTE_API_ACCESS_ID=[CREATED_IN_PRODUCTION_DEX]
CSGFORTE_API_SECURE_KEY=[CREATED_IN_PRODUCTION_DEX]

# Production URLs
CSGFORTE_CHECKOUT_URL=https://checkout.forte.net/v2/js
CSGFORTE_WEBHOOK_URL=[PRODUCTION_WEBHOOK_URL]
```

### **Important Production Notes:**
- **Two Separate Dex Portals:** Sandbox Dex (current) vs Production Dex (after merchant approval)
- **All New Credentials:** Production credentials are completely different from sandbox
- **Merchant Account Required:** Must have approved merchant account before production access
- **No Credential Migration:** Sandbox credentials do not transfer to production

---

## API Integration Details

### Authentication Method
- **Type:** HMAC-SHA256 Signatures + Basic Auth
- **Header Requirements:**
  ```
  Authorization: Basic [base64(api_access_id:api_secure_key)]
  X-Forte-Auth-Organization-Id: [organization_id]
  Content-Type: application/json
  ```

### Signature Generation Algorithm
```
HMACSHA256(
  "api_access_id|method|version_number|total_amount|utc_time|order_number|customer_token|paymethod_token",
  "API Secure Key"
)
```

### Integration Method
- **Selected:** CSG Forte Checkout v2 (iFrame replacement)
- **Reason:** Best match for existing iCG integration pattern
- **Alternative:** Forte.js (not selected - too much customization)

---

## Key API Endpoints

### Core Transaction Endpoints
```
GET    /organizations/{org_id}
GET    /organizations/{org_id}/locations/{location_id}
POST   /organizations/{org_id}/locations/{location_id}/transactions
GET    /organizations/{org_id}/locations/{location_id}/transactions/{transaction_id}
```

### Customer & Payment Method Endpoints
```
POST   /organizations/{org_id}/locations/{location_id}/customers
GET    /organizations/{org_id}/locations/{location_id}/customers/{customer_token}
POST   /organizations/{org_id}/locations/{location_id}/customers/{customer_token}/paymethods
```

---

## Integration Architecture Notes

### Current iCG Integration Pattern (TO REPLACE)
- **File:** `contact-payment-sources.js`
- **Method:** iFrame-based payment capture
- **Response Handler:** `window.addEventListener('message', icgResponseHandler)`

### New CSG Forte Pattern (TO IMPLEMENT)
- **File:** Same - `contact-payment-sources.js`
- **Method:** Checkout v2 iFrame-based payment capture
- **Response Handler:** `window.addEventListener('message', csgForteResponseHandler)`

### Payment Object Mapping
```javascript
// Current iCG Payment Object Fields
icg_payment_id: [confirmation_number]
test_payment: [boolean]
manual_payment: false

// New CSG Forte Payment Object Fields (TO ADD)
csg_forte_payment_id: [transaction_id]
csg_forte_response: [full_response_object]
test_payment: [boolean] // reuse existing
manual_payment: false   // reuse existing
```

---

## Testing & Validation

### Test Cards (Sandbox)
```
Credit Card: **************** (Visa)
Expiration: Any future date
CVV: 123
Amount: Use various amounts for different test scenarios
```

### Test ACH (Sandbox)
```
Routing Number: ********* (Chase)
Account Number: **********
Account Type: Checking
```

---

## Support & Documentation Links

### Primary Resources
- **REST API Docs:** https://restdocs.forte.net/
- **Checkout v2 Docs:** https://iframe.icheckdev.com/CheckoutV2.html
- **Integration Support:** <EMAIL>
- **Technical Support:** 888-235-4635

### Migration Resources
- **iCG Replacement Guide:** https://support.forte.net/support/solutions/articles/***********-icg-iframe-replacement-options

---

## Completion Checklist
- [x] Organization ID documented from Dex: **org_499921**
- [x] Location ID documented from Dex: **loc_401809**
- [x] API Access ID documented from Dex Developer section
- [x] API Secure Key securely stored
- [x] Webhook URL documented for testing
- [ ] Environment variables configured for localhost:8080
- [x] Test API connectivity verified: **SUCCESSFUL**
- [x] Integration method (Checkout v2) confirmed
- [x] Documentation shared with development team

---

## Change Log
| Date | Change | Updated By |
|------|--------|------------|
| [DATE] | Initial documentation created | [DEVELOPER] |
| [DATE] | Sandbox credentials added | [DEVELOPER] |
| [DATE] | Production credentials added | [DEVELOPER] |

---

**Next Steps:** Proceed to T21 API Documentation Research with these credentials established.
