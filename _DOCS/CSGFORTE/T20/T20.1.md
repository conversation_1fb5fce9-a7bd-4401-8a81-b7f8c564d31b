# T20.1: CSG Forte Dex Platform Registration

**Task:** Register for Dex platform access  
**Role:** Developer (D)  
**Status:** Ready to Execute  

## Registration Process

### Step 1: Navigate to Registration Page
- **URL:** https://www.forte.net/test-account-setup
- **Required Information:**
  - First Name: `[Developer Name]`
  - Last Name: `[Developer Last Name]`
  - Company Name: `Infinity Hospitality` (confirmed)
  - Email Address: `[Primary Contact Email]`

### Step 2: Email Verification Process
**You will receive 3 emails after registration:**

1. **Email Verification** (from <EMAIL>)
   - ⏰ **CRITICAL:** Must verify within 24 hours
   - Click "Verify Email Address" button

2. **Dex Invitation** (from Forte)
   - ⏰ **CRITICAL:** Must accept within 7 days
   - Click "Accept Invitation" button

3. **Forte Sales Confirmation**
   - General welcome/confirmation email

### Step 3: Complete Dex Registration
- **SMS Verification Required:** Have mobile phone ready for SMS verification
- **Admin Access:** Registration provides Admin access to Dex organization
- **Account Type:** Sandbox/Test account (isolated from production)

## Success Criteria
- [ ] Registration form submitted successfully
- [ ] Email verification completed (within 24 hours)
- [ ] Dex invitation accepted (within 7 days)
- [ ] SMS verification completed
- [ ] Logged into Dex with Admin access

## Next Steps
After successful registration, proceed to:
- T20.2: Obtain developer credentials from Dex platform
- T20.3: Configure sandbox environment settings

## Support Contact
**If issues arise:** Contact Forte Technical Support at ************

## Notes
- Sandbox account is completely isolated from production environment
- Free testing account with no charges for development/testing
- Credentials created in sandbox are separate from production credentials
