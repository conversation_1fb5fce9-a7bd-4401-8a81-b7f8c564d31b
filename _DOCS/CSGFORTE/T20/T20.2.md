# T20.2: Obtain Developer Credentials

**Task:** Obtain developer credentials  
**Role:** Developer (D)  
**Status:** Dependent on T20.1 completion  

## Prerequisites
- ✅ T20.1: Dex platform registration completed
- ✅ Admin access to Dex organization established

## Credentials Required

### Primary Identifiers
1. **Organization ID**
   - **Location in Dex:** Profile icon (top right) → Organization
   - **Display:** Listed above organization name on Organization Detail page
   - **Usage:** Required in all API requests (X-Forte-Auth-Organization-Id header)

2. **Location ID** 
   - **Location in Dex:** Left menu → Location
   - **Display:** Listed above organization name on Location Detail page
   - **Usage:** Required for transaction processing endpoints

### API Authentication Credentials
3. **API Access ID**
   - **Location in Dex:** Left menu → Developer → [Create new credentials]
   - **Usage:** Primary authentication identifier

4. **API Secure Key**
   - **Location in Dex:** Left menu → <PERSON><PERSON><PERSON> → [Create new credentials]
   - **Usage:** Secret key for HMAC signature generation
   - **⚠️ SECURITY:** Store securely, never expose in client-side code

## Step-by-Step Process

### Step 1: Get Organization ID
1. Log into Dex platform
2. Click profile icon (top right corner)
3. Select "Organization"
4. Copy Organization ID from Organization Detail page
5. **Document:** Record in T20.4 documentation

### Step 2: Get Location ID
1. In Dex left menu, click "Location"
2. Copy Location ID from Location Detail page
3. **Document:** Record in T20.4 documentation

### Step 3: Create API Credentials
1. In Dex left menu, click "Developer"
2. Create new API credentials
3. Enter Name field: `"Bento Payment Integration"`
4. Select appropriate group/role
5. Generate API Access ID and API Secure Key
6. **⚠️ IMPORTANT:** Copy and store both values immediately
7. **Document:** Record in T20.4 documentation

## Security Requirements
- **API Secure Key:** Store in environment variables only
- **Never commit credentials to version control**
- **Sandbox vs Production:** These are sandbox credentials only

## Credential Format Examples
```
Organization ID: org_123456789
Location ID: loc_987654321
API Access ID: api_abcdefghijk123
API Secure Key: sk_1234567890abcdefghijklmnop
```

## Success Criteria
- [ ] Organization ID obtained and documented
- [ ] Location ID obtained and documented  
- [ ] API Access ID created and documented
- [ ] API Secure Key created and securely stored
- [ ] All credentials recorded in T20.4 documentation

## Next Steps
After obtaining credentials, proceed to:
- T20.3: Configure sandbox environment with these credentials
- T20.4: Complete documentation of all endpoints and keys

## Troubleshooting
- **Can't find Developer section:** Ensure you have Admin access to the organization
- **Credentials not generating:** Contact Forte <NAME_EMAIL>
- **Lost API Secure Key:** Regenerate new credentials (old ones will be invalidated)
