# T20.3: Configure Sandbox Environment

**Task:** Configure sandbox environment  
**Role:** Developer (D)  
**Status:** Dependent on T20.2 completion  

## Prerequisites
- ✅ T20.1: Dex platform registration completed
- ✅ T20.2: Developer credentials obtained
- ✅ Organization ID, Location ID, API Access ID, API Secure Key documented

## Environment Configuration

### Sandbox Environment Details
- **Sandbox Base URI:** `https://api.forte.net/v3/sandbox`
- **Environment Type:** Isolated testing environment
- **Data Isolation:** Completely separate from production
- **Credentials:** Sandbox-specific (do not carry over to production)

### Production Environment (For Reference Only)
- **Production Base URI:** `https://api.forte.net/v3`
- **Migration Note:** Credentials must be recreated for production

## Integration Method Selection

### Recommended: CSG Forte Checkout v2
**Chosen for iFrame replacement** - matches our current iCG integration pattern

#### Sandbox Configuration
```html
<!-- Add to <head> tag of payment forms -->
<script type="text/javascript" src="https://sandbox.forte.net/checkout/v2/js" data-rocket-defer defer></script>
```

#### Production Configuration (Future Use)
```html
<!-- Add to <head> tag of payment forms -->
<script type="text/javascript" src="https://checkout.forte.net/v2/js" data-rocket-defer defer></script>
```

### Alternative: Forte.js (Not Selected)
- **Sandbox URL:** `https://sandbox.forte.net/api/js/v1`
- **Use Case:** Maximum customization (not needed for our integration)

## Authentication Setup

### Hash Signature Generation
CSG Forte Checkout v2 uses HMAC-SHA256 signatures for authentication:

```
HMACSHA256("api_access_id|method|version_number|total_amount|utc_time|order_number|customer_token|paymethod_token", "API Secure Key")
```

### Required Headers for API Calls
```
Authorization: Basic [base64(api_access_id:api_secure_key)]
X-Forte-Auth-Organization-Id: [organization_id]
Content-Type: application/json
```

## Local Development Setup

### Environment Variables (localhost:8080)
```bash
# CSG Forte Sandbox Configuration
CSGFORTE_SANDBOX_BASE_URI=https://api.forte.net/v3/sandbox
CSGFORTE_SANDBOX_ORG_ID=[from T20.2]
CSGFORTE_SANDBOX_LOCATION_ID=[from T20.2]
CSGFORTE_SANDBOX_API_ACCESS_ID=[from T20.2]
CSGFORTE_SANDBOX_API_SECURE_KEY=[from T20.2]

# CSG Forte Checkout v2 Sandbox
CSGFORTE_CHECKOUT_SANDBOX_URL=https://sandbox.forte.net/checkout/v2/js
```

### rickyvoltz Instance Configuration
- **Environment:** localhost:8080
- **Instance Type:** rickyvoltz (testing environment)
- **Integration Point:** Existing payment portal in contact-payment-sources.js

## Verification Steps

### Step 1: Verify Existing Payment Functionality
Before CSG Forte integration:
- [ ] Test existing Stripe Credit Card processing
- [ ] Test existing iCheckGateway ACH processing  
- [ ] Confirm payment portal loads correctly
- [ ] Verify invoice reconciliation works

### Step 2: Test CSG Forte Sandbox Connectivity
```bash
# Test API connectivity
curl -X GET \
  https://api.forte.net/v3/sandbox/organizations/[org_id] \
  -H "Authorization: Basic [base64_credentials]" \
  -H "X-Forte-Auth-Organization-Id: [org_id]"
```

### Step 3: Validate Checkout v2 Integration
- [ ] Checkout v2 script loads without errors
- [ ] Authentication signatures generate correctly
- [ ] Test forms render properly
- [ ] Error handling works as expected

## Success Criteria
- [ ] Sandbox environment variables configured
- [ ] CSG Forte Checkout v2 script integration ready
- [ ] Authentication mechanism tested
- [ ] Existing payment functionality verified as working
- [ ] Connectivity to CSG Forte sandbox confirmed

## Next Steps
After sandbox configuration:
- T20.4: Document all API keys and endpoints
- T21.x: Begin detailed API documentation research
- T22.x: Set up local development environment

## Troubleshooting

### Common Issues
- **CORS Errors:** Ensure domain is properly configured for CSG Forte
- **Authentication Failures:** Verify API credentials are correctly formatted
- **Sandbox Timeouts:** Check CSG Forte sandbox status

### Support Resources
- **Technical Support:** 888-235-4635
- **Integration Support:** <EMAIL>
- **Documentation:** https://restdocs.forte.net/

## Security Notes
- **Sandbox Only:** These configurations are for testing only
- **No Real Money:** Sandbox transactions use test data only
- **Credential Isolation:** Sandbox credentials cannot access production
