# T21.1: Research Credit Card Processing APIs

**Task:** Research Credit Card processing APIs  
**Role:** Developer (D)  
**Status:** Ready to Execute  

## Prerequisites
- ✅ T20: CSG Forte Dex Platform Registration Complete
- ✅ API Credentials: 8adfb585f7a7cde0168cbebc52ccc1b2 / 4bbb4b9fbc8e58d47eddeab8030d190d

## Credit Card Processing Overview

### API Integration Method: CSG Forte Checkout v2
**Selected for iFrame replacement** - matches our current iCG integration pattern

### Key Credit Card API Endpoints

#### Transaction Creation
```
POST /organizations/{org_id}/locations/{location_id}/transactions
```

**Request Structure:**
```json
{
  "action": "sale",
  "authorization_amount": 10.00,
  "billing_address": {
    "first_name": "<PERSON>",
    "last_name": "Doe"
  },
  "card": {
    "card_type": "visa",
    "name_on_card": "<PERSON>",
    "card_number": "****************",
    "expire_month": 12,
    "expire_year": 2025,
    "card_verification_value": "123"
  }
}
```

#### Transaction Lookup
```
GET /organizations/{org_id}/locations/{location_id}/transactions/{transaction_id}
```

### Integration Pattern for Bento

#### Current Stripe Integration (TO REPLACE)
```javascript
// In contact-payment-sources.js
ui.buttons.makeNode("createCC", "button", {
  css: "pda-btnOutline-green",
  text: '<i class="fa fa-plus"></i> New Credit/Debit Card',
})
.notify("click", {
  type: "paymentMethodRun",
  data: {
    run: addCCtoStripeCustomer.bind(ui, contactId, stripeId, options),
  },
});
```

#### New CSG Forte CC Pattern (TO IMPLEMENT)
```javascript
// In contact-payment-sources.js
ui.buttons.makeNode("createCSGForteCC", "button", {
  css: "pda-btnOutline-green", 
  text: '<i class="fa fa-plus"></i> CSG Forte Credit Card',
})
.notify("click", {
  type: "paymentMethodRun",
  data: {
    run: addCCtoCSGForteCustomer.bind(ui, contactId, options),
  },
});
```

## Checkout v2 Implementation Details

### Script Integration
```html
<!-- Add to payment form <head> -->
<script type="text/javascript" src="https://sandbox.forte.net/checkout/v2/js"></script>
```

### Authentication Signature
```javascript
// HMAC-SHA256 signature generation
const signature = HMACSHA256(
  `${api_access_id}|${method}|${version}|${amount}|${utc_time}|${order_number}|${customer_token}|${paymethod_token}`,
  api_secure_key
);
```

### Response Handling Pattern
```javascript
// Similar to existing iCG pattern
window.addEventListener('message', csgForteResponseHandler);

function csgForteResponseHandler(e) {
  if (e.data.action == "dismissFrame") {
    return;
  } else {
    var csgForteResponse = e.data;
    if (typeof csgForteResponse != "undefined" && 
        csgForteResponse.response_type == "S" && // Success
        csgForteResponse.transaction_id != undefined) {
      
      // Process successful CC payment
      processCSGForteCCPayment(csgForteResponse);
    }
  }
}
```

## Payment Processing Flow

### Step 1: Customer Initiates CC Payment
- Customer clicks "CSG Forte Credit Card" button
- iFrame loads with CSG Forte Checkout v2 form

### Step 2: Payment Collection
- Customer enters credit card details in secure iFrame
- CSG Forte handles PCI compliance
- Payment processed through CSG Forte

### Step 3: Response Processing  
- Success response contains transaction_id
- Create payment object in Bento with CSG Forte metadata
- Update invoice balances
- Send email notifications

### Step 4: Payment Object Creation
```php
// In new CSGForteService.php (similar to iCheckGatewayService.php)
$paymentObject = $this->sb->pgObjects->create('payments', array(
    'main_object' => $proposal['id'],
    'amount' => $amount,
    'fee' => $transactionFee,
    'csg_forte_payment_id' => $csgForteResponse->transaction_id,
    'invoice' => $inv["id"],
    'test_payment' => $this->testKey,
    'manual_payment' => false,
    'owner' => $paymentOwnerId,
));
```

## Metadata Field Reuse Strategy

### Existing Stripe Metadata Fields (TO REUSE)
- Payment object structure
- Invoice reconciliation logic
- Email notification system
- Fee calculation patterns

### New CSG Forte Fields (TO ADD)
- `csg_forte_payment_id` (replaces `stripe_payment_id`)
- `csg_forte_response` (full response object)
- Transaction status tracking

## Testing Requirements

### Sandbox Test Cards
```
Visa: ****************
Mastercard: ****************
Amex: ***************
Discover: ****************

Expiration: Any future date
CVV: 123 (Visa/MC/Discover) or 1234 (Amex)
```

### Test Scenarios
- [ ] Successful CC payment processing
- [ ] Declined card handling
- [ ] Invalid card number handling
- [ ] Expired card handling
- [ ] Insufficient funds handling
- [ ] CVV mismatch handling

## Success Criteria
- [ ] CSG Forte CC API endpoints documented
- [ ] Integration pattern defined for Bento
- [ ] Response handling structure planned
- [ ] Payment object mapping completed
- [ ] Test scenarios identified
- [ ] Error handling patterns documented

## Next Steps
After CC API research completion:
- T21.2: Research ACH processing APIs
- T21.3: Research iFrame integration options (builds on this)
- T23.x: Begin CC button implementation

## Research Resources
- **Checkout v2 Docs:** https://iframe.icheckdev.com/CheckoutV2.html
- **REST API Docs:** https://restdocs.forte.net/
- **Integration Guide:** https://support.forte.net/support/solutions/articles/***********

## Notes
- CSG Forte Checkout v2 chosen over Forte.js for simplicity
- iFrame approach maintains PCI compliance
- Response patterns similar to existing iCG integration
- Metadata reuse strategy preserves existing business logic
