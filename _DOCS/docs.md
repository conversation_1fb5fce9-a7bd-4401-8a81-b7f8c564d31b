# Table of Contents

1. Core Concepts
   1. Fields
      1. Registering a Field Type
   2. Sets
      1. FAQs
         1. [How do I find subsets for a given set?](#How do I find subsets for a given set?)
   3. Workflows
   4. Rules
      1. Usage
         1. Running a Rule Flow
      2. Triggers
         1. [How do I create a new kind of trigger?](#How do I create a new kind of trigger?)
      3. Actions
         1. Registering an Action
      4. Conditions
         1. Registering a Condition
2. API
   1. Services
      1. Registering a New Service
   2. App/Sandbox Tools
   3. [Data Layer](#Data Layer)
      1. [Basic Concepts](#Basic Concepts)
         1. [Selection Objects](#Selection Objects)
         2. [Where Objects](#Where Objects)
            1. [How do I filter a set where records are tagged with any tag in a given set, AND another specific tag?](#How do I filter a set where records are tagged with any tag in a given set, AND another specific tag?)
      2. Methods
         1. [$objs->create ( String $objectType = '', Array $objectData = null, ... )](#objs->create)
         2. update()
         3. getById()
         4. where()
         5. delete()
3. Browser Application
   1. Sandbox



# Core Concepts

## Sets

### FAQs

#### How do I find subsets for a given set?

Subsets are related to their parent/root set by a pointer on the subset (**entity_type**) object at **_class**. So to gather all subsets of Set A, you can query the api like this:

```
sb.data.db.obj.getWhere(
  'entity_type'
  , {
    _class: setA.id
  }
  , function (subsets) { console.log('Here are the direct subsets of Set A: ', subsets) }
);
```

#### How do I create a new kind of trigger?

This will end up being a two-step process - 

1. First, we'll need to let the API know that this new trigger-type exists by adding it as an option to the relevant properties on the blueprints for actions/conditions.
2. Next, we'll need to establish a UI to let users configure action chains for the new trigger.

First, the api needs to know that the new trigger type exists. We can do this by adding it as options :

```
... "trigger": {
      "name": "Trigger"
      , "type": "select"
      , "immutable": false
      , "options": {
            "stateChange": "State Change"
            , "clientCommentPosted": "Client Comment Posted"
            //... Add the new option here
      }
} ...
```

- blueprint files in ***_SRC/blueprints/event_type.json and _SRC/blueprints/condition.json\***

Once this trigger type is established, the ***triggerActions\*** endpoint can be used. On the back-end, it can be used like this:

```
$app->triggerActions(
      $request = [
          'type' => String       // the type of trigger (the option you just added to the blueprints above)
          'context' => ObjectId  // the triggering context object
      ]
      $json = Int                // formatting the request - 0 for a php array, 1 for json
```

Next, we need to establish the UI for users to configure actions tied to the new trigger. Both the **event_type** and **condition** objects have the following properties that are relevant here: 

- **object**: 	*generally, a linking "type" object that lets the system know* 
- **trigger**: 	*the kind of trigger, stored from a selections list.*

```
sb.notify({
      type: 'view-actions-by-trigger'
      , data: {
            triggerType: String    // the type of trigger (the option you just added to the blueprints above)
            , context:   ObjectId  // the triggering context object
      }
});
```

# API

## Services

### Registering a New Service



## Data Layer

### Basic Concepts

#### Selection Objects

Selection objects are used to tell the api exactly what properties (and child object data) to pull in in a given request. In the **sb.data.db.obj.where**, this can be added to the query directly at **where.childObjs**.

For example: f you are looking at **time_entries**, and want to pull in related users objects (at the **staff** property) and the profile image (at the **profile_image** property on the staff object), you can set the selection object to:

```
{
  staff: {
    profile_image: true
  }
}
```



**NOTE:** *Property definitions on all hard-coded object types in the system can be found in* ***_SRC/blueprints\*** *in json files.*

#### Where Objects

#### How do I filter a set where records are tagged with any tag in a given set, AND another specific tag?

To filter a set where records are tagged with any of a tag set [1, 2, 3] ***AND\*** contain another tag [4] tag, we can combine two **tagged_with** filters to build a where clause for our query like this:

```
where = {
  tagged_with: {
    type:     'any'
    , values: [1, 2, 3]
    , and:    {
      tagged_with: [4]
    }
  }
};
```

### Methods

#### objs->create

`create (String $objectType = '', Array $objectData = null, ...)` 

The create method is used to establish a new record (of any type) in the database. 