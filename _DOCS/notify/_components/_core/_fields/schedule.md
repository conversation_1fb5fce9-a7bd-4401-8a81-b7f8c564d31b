### Schedule Field

#### Description of the Field

The schedule field allows users to schedule when single or recurring actions should occur.

**Behaviors:**

| Action | Behavior |
|-----------|-----------|

#### Developer Guide

**Field Type**: `schedule`

**Options:**

| Name | Type | Description |
|-----------| -----------| -----------|
| commitUpdates | `boolean` | Default: `true` When set to `false`, prevents running an update call within the field.|

**Data Model:**

The schedule field stores an array of field data.

So, for entities, you would see something like this:

_30: {
    quantityOfTime: 1, // related to 'unitOfTime', the amount of 'unitOfTime'
    unitOfTime: 'week', // options are 'hour', 'day', 'week', 'month', 'year'
    dayOfWeek: 'Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday' // a comma separated string
    dayOfMonth: '1', // numeric day of month 1-31
    monthOfYear: 'January', // single month name
    timeOfDay: '12:00 pm', // time of day
    startAfter: false, // boolean to toggle on the number of days after transitioning to the workflow state that the schedule should start
    startAfterQuantity: 1, // number of days after transitioning to the workflow state that the schedule should start
    endsAfter: false, // boolean to toggle on the number of times the schedule should run
    endsAfterQuantity: 1, // the number of times the schedule should run
    isScheduled: false // boolean to toggle the active state of the schedule
}

**Known Issues:**

* 

**Cleanup:**

* 

**Screenshots:**
