# Gifting With Valor
#### Bento Systems Documentation

## Table of Contents
1. [Definitions](#definitions)
2. [Setting Up A New Project](#setting-up-a-new-project)
3. [Creating A New Supplier and Product](#creating-a-new-supplier-and-product)
	
***

## Definitions

### Contact
A `Contact` is the individual/lead that requests a `Project`. A single `Contact` can request multiple `Projects` over time.

### Project
A `Project` is a requested by a `Contact` and contains all of the `Box` samples and the final `Box` selection. A `Project` also contains the Sales Agreement contract and the intake questionnaire information gathered during the initial interview with the `Contact`.

`Projects` have a status to indicate where they are in the current workflow.

### Box
A `Box` or series of `Boxes` can be created within a `Project`. Each `Box` contains all of the `Line Items` associated with the `Box`.

`Boxes` have a status to indicate whether or not they have been accepted or declined by the `Contact`. 

### Line Item

A `Line Item` is a single item within a `Box`. One or many `Line Items` can be added to a single `Box`. Each `Line Item` contains the pricing and quantity information needed to properly price the item.

The `Line Item` entry also allows you to link the `Line Item` to a `Product` within the system.

> Linking a `Line Item` to a `Product` will allow you to see all `Line Items` of a particular `Product` if desired.

### Product
Whereas a `Line Item` is a pricing record linked to a single `Product`, the `Product` is the item being sold by the `Supplier`. The separation between a `Line Item` and a `Product` allows you to sell a single `Product` at different prices, but still be able to produce a report that shows all the times (and at which price) a `Product` has been sold.

### Supplier
A `Supplier` is a vendor that sells one or more `Products` in a `Box`. A `Supplier` can have many `Products` linked to them.

### Tags
`Tags` are used to categorize information. The most common use case is to add a `Tag` for a product category directly to a `Product` entry. Doing so, will allow users to search and filter the `Product` list by product category(ies).

---

## Setting Up A New Project

1. Create a new `Contact` or select an existing `Contact` in the system.
2. Create a new `Project` from the `Contact` page. This `Project` will be automatically linked to the `Contact`.
3. On the `Project` screen, fill out the questionnaire details on the `Intake and Discovery` tab.
4. Once you have completed the `Intake and Discovery` tab, move the project to the `Building the Boxes` stage. This will create the first 3 samples boxes. Click the `Information` tab at the top of the `Project` screen to view the sample `Boxes` and due date information.
5. To edit a `Box`, click the box name to open the `Box`.
6. After opening the `Box`, you can add begin adding `Line Items` by clicking the green New button in the `Line Items` section of the `Box` view. Give the new `Line Item` a name and click Save.
7. Click the name of the `Line Item` to view the entire entry. On the `Line Item` screen you can edit the pricing information and link the `Line Item` to a `Product`.

## Creating A New Supplier and Product
1. Select the `Supplier` tab from the menu bar.
2. Click the New button on the right side of the screen. This will bring up the New Supplier Form. Fill in the details in the form. Details are saved automatically.
3. When you have finished, click the Arrow button at the top right of the form. This will take you to the new `Supplier` you just entered.
4. On the Supplier Page, scroll until you see the `Products` section. Click the New button and give the `Product` a name. Click Save.
5. Once saved, you can click the `Product` name in the table to open the Product Page.
6. One the Product Page, you can add more details to the `Product` by filling out the rest of the form.
7. Add `Tags` for product categorization to the `Product` at the top of the page. 
8. Once complete, the new `Product` will be available when creating `Line Items`.
9. If desired, you can go back to the Supplier Page to add another `Product` to the same `Supplier`.