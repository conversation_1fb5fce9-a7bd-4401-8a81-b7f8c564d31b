# Foundation Group

<!-- toc -->
## Table of Contents

1. [Client Organizations and Contacts](#client-organizations-and-contacts)
	1. [Creating a Client Organization](#creating-a-client-organization)
	2. [Core Demographics](#core-demographics)
	3. [Creating a Contact Within a Client Organization](#creating-a-contact-within-a-client-organization)
2. [Services](#services)
	1. [Core Services](#core-services)
	2. [Form 990](#form-990)
		1. [Intake](#990-intake)
		2. [Delivery Flow](#990-delivery-flow)
	3. [Form 990N](#form-990n)
	3. [Charitable Solicitations](#charitable-solicitations)
		1. [Charitable Solicitations Initial](#charitable-solicitations-initial)
		2. [Charitable Solicitations Renewal](#charitable-solicitations-renewal)
		3. [Adding Additional State Forms](#charitable-solicitations-additional-state-forms)
	4. [Formation](#formation)
		1. [Creating and Editing Form Fields](#creating-and-editing-form-fields)
		2. [Creating and Editing Tasks](#creating-and-editing-tasks)
	5. [Abatement](#abatement)
	6. [Services Roles](#roles)
		1. [Specialist](#specialist-role)
	7. [Service Tools](#service-tools)
		1. [Info Requests](#info-requests)
		2. [Client Reviews](#client-reviews)
		3. [Client Service](#client-services)
3. [Navigation](#navigation)
	1. [Navigating Between Teams](#navigating-between-teams)
4. [Client Portals](#client-portals)
	1. Current Services
	2. [History](#client-portals-history-page)
5. [Delivery](#delivery)
	1. [Delivery Documents](#delivery-documents)
6. [Recent Client Action Flow](#recent-client-action-flow)
7. [FAQs](#FAQs)

<!-- tocstop -->
<div style="page-break-after: always;"></div>

# Client Organizations and Contacts

## Creating a Client Organization

Since all [Services](#services) must be associated with Client Organizations, creating the Client Organization (and a Contact at the Organization) is the first step in the all of the service processes. Most Client Organizations and Contacts are set up automatically through an integration with Salesforce, however, they can be created manually. 

To Create Clients manually, first [navigate to](#Navigating Between Teams) **All Company Team** (this will ensure that the Client Organization you create is available to everyone -- users may not be able to access Client Organizations created elsewhere). Once within the **All Company Team**, you can click on the **Contacts** tool in the left nav, and should see a screen that looks something like this:

![](images/contacts-table.png)

Next, click the green **+ New** button in the top right of this page. This should bring up the **Create a Company** form, shown below. All information can be changed later. Note that the **Manager** field in this form corresponds to the **CSM**. out the form and click **Save and view company**.

![](images/create-company-form.png)

Once created, you'll be taken to the **Client Organization's Page**, shown below. On this page, you can view and edit information about the Organization, and see active and historical projects for the Client.

![](images/company-page.png)

## Core Demographics

The Core Demographics page within Organizations is the place to view and edit details specific to the company. There is a single Core Demographics page for each Client Organization, which is contains data which is referenced throughout intake forms which are auto-generated throughout [Services](#services).

Before setting up any Services for the Client, open the **Core Demographics** tool for the Organization and record all information available there. (If this client is auto-generated through the Salesforce integration, this information will be pre-populated and you will just want to verify its correctness.)

![](images/core-demo.png)


<div style="page-break-after: always;"></div>

## Creating a Contact Within a Client Organization

Next, set up a Contact at the Organization using the green **+ New** button on the right-hand side of the **Info** tab. This will bring up the form shown below:

![](images/create-contact-form.png)

Once saved, you will be brought to the new contact's page where you can view and edit information about that contact, shown below.

![](images/contact-page.png)

Note that the contact info added during the creation process can be found here as "Primary" contact info. This can be adjusted with the pencil edit icons here:

![](images/contact-page-contact-info.png)

Client Organizations can have many Contacts, so the process shown above to add contacts to the organization can be repeated as many times as is necessary. 

# Services

<div style="page-break-after: always;"></div>

## Core Services

The *Core Services* Set is used to create the various Services for a Client, as well as open up portal access for client users. Each Client Organization has a Core Services page, which contains a list of all of Core Services created for the organization over time, shown below:

![](images/core-services.png)

On the right-hand side, we can see a new core services record -- its name is defaulted to "Name of the company | Date the record was added". We can now fill out this form to record the kinds of services we wish to create for the Client Organization -- which (if any) 990, 1023, and Charitable Solicitaion services (as well as the applicable states for Charitable Solicitations). Once this form is completely filled out, we can transition it from **Needs Assessment** to **Active**, which will do two things:

1. Create the Services/Projects based off of the selections made in the form, 

2. and bring up a form to give the contact (set in the **Main Contact** field of the form) portal access, as well as send out a welcome email with their login credentials (shown below)

   ![](images/create-portal-form.png)

Back on the **Company** page, we will now see the new **Services** just created in the **Projects** tab.

Within each **Project**, fill out the **Client Service** page, then transition the project to **Intake** -- now, the projects will be visible within the **Client Portal**.

<div style="page-break-after: always;"></div>

## Form 990

### Upgrading and Downgrading between 990 and 990 EZ Services
- https://www.loom.com/share/a313a10214f6471090fd7ecc61c13ede

#### 990 Unassigned

All new **990** projects start here. Before transitioning the project to **Intake**, make sure that the following items are filled in:

- On the **Client Service** page, the: 
  - **Tax Year**
  - **State of Incorporation**
  - **Due Date**
  - **Fiscal Year End Date**
- On the **Project Role Sheet** page, the:
  - **Specialist**
  - **Reviewer**

At this point, you can transition your service to [Intake](#990-intake).

#### 990 Intake

When the service is first moved into *Intake*, you will be asked if you want to trigger the following actions, shown below:

![](images/990-intake-transition-with-actions.png)

Select **Transition** to ensure the following automations run:

- The *Create Client Portal* form (also in the [Core Services](#core-services) flow) will appear. At this point, it is likely that the **Main Contact** on the **Service** already has a client portal (set up during Core Services) and this form can be ignored (or if run you will just get a message letting you know they already have a portal). However, in the case that a **Client Organization** has multiple contacts, each acting as main contacts for the various services, the main contact may not yet have login access and it can be granted by submitting this email form.

- Upon transition to Intake, the **Intake Forms** are also generated, which vary depending on the type of 990.

At this point, the client can login to their portal and view their intake forms. When the client logs in, they should see the new service in their Home Screen, shown below:

![](images/990-in-portal-service-list.png)

Within the service, the client will now see their list of action items, like this:

![](images/990-portal-action-items-list.png)



When they have completed all intake forms, the client will see the following message in the top-right of their screen, 

![](images/portal-all-set-msg.png)

and the service will automatically transition to [Initial Info Review](#990-information-review).

#### 990 Information Review

At this point, the **Intake Forms** have been completed by the **Client** and the **Specialist** should review the forms. If more information is needed, the [Info Requests](#info-requests) tool can be used to create additional action items for the client and move the service to [More Info Requested](#990-more-info-requested).

Otherwise, the Specialist should move the service to [Work in Progress](#charitable-solicitations-initial-work-in-progress).

#### 990 Work in Progress

- In the [Client Portal](#client-portals) the service now appears as [Preparing](#portal-status-preparing). 

While in this status, the assigned [Specialist](#specialist-role) on the project should review the intake forms submitted by the client. When necessary, they can reach out for more information using the [Information Request](#information-requests) tool on the project, which will end up moving the service into [More Info Requested](#990-more-info-requested).

If the information provided in the intake forms is sufficient, the [Specialist](#specialist-role) can create a [Client Review](#client-reviews) in the service and upload the deliverable/review item there and manually move the project to [Final Review](#-review) for it to be reviewed by the [Reviewer](#reviewer-role). 

Next, see [Delivery](#990-delivery-flow)

#### 990 Final Review

See [Delivery](#990-delivery-flow)

#### 990 Package for Delivery

See [Delivery](#990-delivery-flow)

#### 990 Client Review

See [Delivery](#990-delivery-flow)

#### 990 Authorized

Services are moved into this cue when the client has authorized the review document and the [Client Service](#client-services) tool can be used to upload the final deliverables to the client and transition the service to [Complete](#990-complete).

Each service has a Client Service page, which can be used to upload the final deliverable items for the client and transition the project to its "done" state/cue. 

![](images/client-service-deliverables-table.png)

#### 990 Client Revision Request

Services are moved into this cue when the client has requested a revision in their review documents in the [Client Review Item](#client-reviews) flow. Specialists can filter their cues by status to see all services here, and when they are ready to begin the revision, should move the service into [Package for Delivery](#990-package-for-delivery).

#### 990 Complete

When a service has reached this status, it is considered "done". It will no longer appear in the **Home** or **Current Services** pages of the corresponding client's portal, and will move into the [History](#client-portals-history-page) page where the client can download the final deliverables.

In the **History** page, the client can see all completed services and download the final deliverable documents for them. The main **History** page is shown below:

![](images/portal-history.png)

After opening the individual services, you can download the deliverables here:

![](images/portal-deliverable-download.png)

#### 990 More Info Requested

Services in **More Info Requested** appear have an outstanding [Info Request](#info-requests) on the Client's plate. when the client completes the info request action item, the service automatically transitions back to [Information Review](#990-information-review).

#### 990 Hold

At any point, the service can be manually moved to this cue if the project can not yet be started.

<div style="page-break-after: always;"></div>

#### 990 Delivery Flow

Delivery for 990 begins in the **Work in Progress** stage of the project. Here, the Compliance Team can create a new review document in the **990 Client Review** tool within the projects.

![](images/990-client-review-page.png)

The **Client Review** goes through the following flow:
- ***Document Prep***, where the Compliance Team is building the documents. Moving into this state moves the project into **Client Review**.
- ***Internal Review***, where the documents are being reviewed by an FG team member. Moving into this state moves the project into **Client Review**.
- ***Awaiting Client Review***, where the documents are shared with the client. When the client has reviewed, uploaded their signed copy, and clicked **Authorize**, the Review Document and Project move to **Authorized**.

![](images/990-client-review-approve-btn.png)

- ***Authorized*** -- approved by the client! The project has also been moved to **Authorized**.
- ***Needs Adjustment*** -- the client has said something is off on the current document(s). The project moves to ??? and its time to review the change request notes from the client (stored on the Review Document). With the new info, the process should now be started over by creating a new Review Document within the project.

Once the 990 has been **Authorized**, the Foundation Group team awaits the Confirmation Code from the IRS. The 990 can no longer appears in the client's **Home** or **Current Services** pages, and instead appears in the **History Page**, where the final documents can be found.

When the Compliance Team receives the Confirmation Code, it should be recorded in the **Client Service** tool in the project in the *eFile Confirmation Code* field. Then, clicking the **Share Confirmation Code** button on the **Client Service** page bring up an email box to send the code with the client. This also moves the project to **Completed**.

![](images/990-send-confirmation-code-email.png)


## Form 990N

**990N** projects/services can be created using the [Core Services](#core-services) tool on the **Company**.

#### 990N Unassigned

All new **990N** projects start here. Before transitioning the project to **Intake**, make sure that the following items are filled in:

- On the **Client Service** page, the: 
  - **Tax Year**
  - **State of Incorporation**
  - **Due Date**
  - **Fiscal Year End Date**
- On the **Project Role Sheet** page, the:
  - **Specialist**
  - **Reviewer**

At this point, you can transition your service to [Intake](#990n-intake).

#### 990N Intake

When the service is first moved into *Intake*, you will be asked if you want to trigger the following actions, shown below:

![](images/990n-intake-transition-with-actions.png)

Select **Transition** to ensure the following automations run:

- The *Create Client Portal* form (also in the [Core Services](#core-services) flow) will appear. At this point, it is likely that the **Main Contact** on the **Service** already has a client portal (set up during Core Services) and this form can be ignored (or if run you will just get a message letting you know they already have a portal). However, in the case that a **Client Organization** has multiple contacts, each acting as main contacts for the various services, the main contact may not yet have login access and it can be granted by submitting this email form.

- Upon transition to Intake, the **01 Intake Questionnaire** form for the client is generated.

At this point, the client can login to their portal and view their intake forms. When the client logs in, they should see the new service in their Home Screen, shown below:

![](images/990n-in-portal-service-list.png)

Within the service, the client will now see their intake form, like this:

![](images/990n-portal-action-items-list.png)

When they have completed the intake forms, the client will see the following message in the top-right of their screen, 

![](images/portal-all-set-msg.png)

and the service will automatically transition to [Initial Info Review](#990n-information-review).

#### 990N Information Review

At this point, the **Intake Form** has been completed by the **Client** and the **Specialist** should review it. If more information is needed, the [Info Requests](#info-requests) tool can be used to create additional action items for the client and move the service to [More Info Requested](#990n-more-info-requested).

Otherwise, the Specialist should move the service to [Work in Progress](#990n-work-in-progress).

#### 990N Work in Progress

- In the [Client Portal](#client-portals) the service now appears as [Preparing](#portal-status-preparing). 

While in this status, the assigned [Specialist](#specialist-role) on the project should review the intake forms submitted by the client. When necessary, they can reach out for more information using the [Information Request](#information-requests) tool on the project, which will end up moving the service into [More Info Requested](#990n-more-info-requested).

If the information provided in the intake forms is sufficient, the [Specialist](#specialist-role) can navigate to the project's **Client Service** tool and upload the deliverable document.

![](images/990n-deliverable-completion.png)

Then, the **Specialist** can click the **Add to Portal History Page** button shown above, which will share this with the client in their portal and move the project/service to [Complete](#990n-complete). It will also bring up an email to send out to the client.

#### 990N Complete

When a service has reached this status, it is considered "done". It will no longer appear in the **Home** or **Current Services** pages of the corresponding client's portal, and will move into the [History](#client-portals-history-page) page where the client can download the final deliverables.

In the **History** page, the client can see all completed services and download the final deliverable documents for them. The main **History** page is shown below:

![](images/portal-history.png)

After opening the individual services, you can download the deliverables here:

![](images/portal-deliverable-download.png)

#### 990N More Info Requested

Services in **More Info Requested** appear have an outstanding [Info Request](#info-requests) on the Client's plate. when the client completes the info request action item, the service automatically transitions back to [Information Review](#990n-information-review).

<div style="page-break-after: always;"></div>

## Charitable Solicitations

Charitable Solicitations Services can be set up using the [Core Services](#core-services) tool in a [Client Organization](#client-organizations-and-contacts) like the other *Client Services* currently available.

There are two kinds of Charitable Solicitations Services:

1. [Charitable Solicitations Initial](#charitable-solicitations-initial), which are used for clients just getting started, 
2. and [Charitable Solicitaions Renewal](#charitable-solicitations-renewal), which are used for clients who have previously completed the *Charitable Solicitations Initial* process in a prior year.

If a *Charitable Soliciations* service (either *Initial* or *Renewal*) is selected in the *Core Services* form, a selection of US states the Client is registering in, shown below:

![](images/core-services-charSol-state-selections.png)

The selections made above will determine which Intake Forms (since some are state-specific) are generated for the project when the project is initially generated. In addition to state specific forms, both *Initial* and *Renewal* services will be generated with 3 Universal Intake forms and forms associated with the states selected above. These can be found in the **Client Action Items** tool within the project, shown below:

![](images/charSol-action-items.png)

### Charitable Solicitations Initial

#### Charitable Solicitations Initial | New

All new **Charitable Solicitations Initial** projects start here. Before transitioning the project to **Intake**, make sure that the following items are filled in:

- On the **Client Service** page, the: 
   - **Tax Year**
   - **State of Incorporation**
   - **Due Date**
   - **Fiscal Year End Date**
- On the **Project Role Sheet** page, the:
   - **Specialist**
   - **CS Reviewer**

At this point, you can transition your service to [Intake](#charitable-solicitations-initial-intake)

#### Charitable Solicitations Initial Intake

When the service is first moved into *Intake*, the *Create Client Portal* form (also in the [Core Services](#core-services) flow) will appear. At this point, it is likely that the **Main Contact** on the **Service** already has a client portal (set up during Core Services) and this form can be ignored (or if run you will just get a message letting you know they already have a portal). However, in the case that a **Client Organization** has multiple contacts, each acting as main contacts for the various services, the main contact may not yet have login access and it can be granted by submitting this email form.

At this point, the client can login to their portal and view their intake forms. When the client logs in, they should see the new service in their Home Screen, shown below:

![](images/portal-home-page-with-action-needed.png)

Within the service, the client will now see their list of action items, like this:

![](images/portal-intake-forms.png)



When they have completed all intake forms, the client will see the following message in the top-right of their screen, 

![](images/portal-all-set-msg.png)

and the service will automatically transition to [Initial Info Review](#charitable-solicitations-initial-info-review).

#### Charitable Solicitations Initial Info Review

At this point, the **Intake Forms** have been completed by the **Client** and the **Specialist** should review the forms. If more information is needed, the [Info Requests](#info-requests) tool can be used to create additional action items for the client and move the service to [More Info Requested](#charitable-solicitations-initial-more-info-requested).

Otherwise, the Specialist should move the service to [Work in Progress](#charitable-solicitations-initial-work-in-progress).

#### Charitable Solicitations Initial More Info Requested

Services in **More Info Requested** appear have an outstanding [Info Request](#info-requests) on the Client's plate. when the client completes the info request action item, the service automatically transitions back to [Initial Info Review](#charitable-solicitations-initial-info-review).

#### Charitable Solicitations Initial Work in Progress

- In the [Client Portal](#client-portals) the service now appears as [Preparing](#portal-status-preparing). 

While in this status, the assigned [Specialist](#specialist-role) on the project should review the intake forms submitted by the client. When necessary, they can reach out for more information using the [Information Request](#information-requests) tool on the project, which will end up moving the service into [More Info Requested](#charitable-solicitations-initial-more-info-requested).

If the information provided in the intake forms is sufficient, the [Specialist](#specialist-role) can create a [Client Review](#client-reviews) in the service and upload the deliverable/review item there and manually move the project to [Internal Review](#charitable-solicitations-initial-internal-review) for it to be reviewed by the [Reviewer](#reviewer-role).

#### Charitable Solicitations Initial Internal Review
- In the [Client Portal](#client-portals) the service now appears as [Preparing](#portal-status-preparing). 

While in this status, the [Reviewer](#reviewer-role) should:

- Review the deliverable item the [Specialist](#specialist-role) set up during [Work in Progress](#charitable-solicitations-initial-work-in-progress). 

If the deliverable is ready to pass on the the client, the [Specialist](#specialist-role) can move the [Client Review item](#client-reviews) to **Awaiting Client Review**, which will bring up the Send New Client Review email form, shown below, as well as automatically transition the project to [Sent to Client](#charitable-solicitations-initial-sent-to-client). 

![](images/client-review-new-review-item-email-form.png)

#### Charitable Solicitations Initial Sent to Client

At this point, the Service should now appear in both the [Home](#client-portal-home) and [Current Services](#client-portal-current-services) pages in the [Client Portal](#client-portals) in the **Action Needed** status. The [Client Review item](#client-reviews) created and reviewed in [Work in Progress](#charitable-solicitations-initial-work-in-progress) and [Internal Review](#charitable-solicitations-initial-internal-review) will now appear as an action item for the client within the service. There, they can download the document, leave notes, and pass the project back to the *Specialist* by completing the action item by either approving or requiring changes.

When the client completes this, the service automatically moves to [Approved Letter Received](#charitable-solicitations-initial-approved-letter-received).

#### Charitable Solicitations Initial Client Revision Request

Services are moved into this cue when the client has requested a revision in their review documents in the [Client Review Item](#client-reviews) flow. Specialists can filter their cues by status to see all services here, and when they are ready to begin the revision, should move the service into [Revision in Progress](#charitable-solicitations-initial-revision-in-progress).

#### Charitable Solicitations Initial Revision in Progress

Services in this cue are currently being worked on by a Specialist. While working on the revision, the Specialist should create a new [Client Review Item](#client-reviews) with the necessary adjustments, and use it to move the service back into [Sent to Client](#charitable-solicitations-initial-sent-to-client).

#### Charitable Solicitations Initial Approved Letter Received

When a service has reached this status, it is considered completed, and the [Client Service](#client-services) tool can be used to upload the final deliverables to the client. It will no longer appear in the **Home** or **Current Services** pages of the corresponding client's portal, and will move into the [History](#client-portals-history-page) page where the client can download the final deliverables.

#### Charitable Solicitations Initial Withdrawn

At any time, a service can be moved into **Withdrawn** to take the service out of the working cues. It will no longer be considered an active service.

#### Charitable Solicitations Initial Recent Client Action

*Coming soon..*

#### Charitable Solicitations Holds

Charitable Solicitations services will not enter [Intake](#charitable-solicitations-initial-intake) until certain conditions are met. In the following order, services will stay in these Hold Cues until their corresponding condition is met:

- **Hold State/Date of Inc.** until the *State of Incorporation* and *Date of Incorporation* values are set on the *Core Demographics* page for the client
- **Hold 1023 Application** until the client has completed their 1023 Application for that year
- **Hold IRS Determination Letter** until the client has completed their IRS Determination Letter for that year
- **Hold 990** until the client has completed their 990 service for that year

### Charitable Solicitations Renewal

The Renewal workflow is largely the same as the Initial flow, but more streamlined -- the key differences are:

- Universal Intake forms have their initial values carried over from the previous year's forms.
- There is only one relevant hold status, **Hold 990**.
- There is no client review flow, so **Sent to Client**, **Client Revision Request**, **Revision in Progress** are not applicable, and **Done** is the service's "done" cue instead of **Approval Letter Received**.
- There is no only one internal review cue, **Information Review**.

### Charitable Solicitations Additional State Forms

If not all states are known during the time the CharSol project is created, the **CharSol State Supplemental** tool can be used to add additional states. First, navigate to the **CharSol State Supplemental** within the project. There, you will see the **Supplemental State** field containing the checklist of states for the project. To add another state, click the check next to the desired state.

After processing, you will see this alert asking you to refresh the page:

![](images/char-sol-additional-forms-refresh-msg.png)

Click **Yes** to refresh the page, and any forms for the selected state will have been added to the action items list.

<div style="page-break-after: always;"></div>

## Formation

### Formation Workflow Overview

#### Formation New

All new **Formation** projects/services start here. Before transitioning the project to **Intake**, make sure that the following items are filled in:

- On the **Client Service** page, the: 
   - **Tax Year**
   - **State of Incorporation**
   - **Due Date**
   - **Fiscal Year End Date**
- On the **Project Role Sheet** page, the:
   - **Specialist**
   - **CS Reviewer**

At this point, you can transition your service to [Intake](#formation-incorporation-intake)

##### TODOS:
- [ ] Connect creation to Core Services
- [ ] Update the new portal email on Core Services
- [ ] We do not have the Client Service info at the get-go of this -- it is set up during Intake
- [ ] On the Project Role Sheet page, th gets assigned during Incorporation Intake Review
	- [ ] Specialist is assigned first time we go into Interview
- [ ] Share the action items list w/Dee and swap these out
- [ ] Auto-populate the name of the Appointments
- [ ] Duration should be defaulted to 90 mins
- [ ] Add Appointments tool across teams
- [ ] Roles & Role sheet info TBD from more info from FG
- Day 2:
	- [ ] Show attendees within the card in the calendar view

#### Formation Incorporation Intake

When the service is first moved into *Intake*, the *Create Client Portal* form (also in the [Core Services](#core-services) flow) will appear. At this point, it is likely that the **Main Contact** on the **Service** already has a client portal (set up during Core Services) and this form can be ignored (or if run you will just get a message letting you know they already have a portal). However, in the case that a **Client Organization** has multiple contacts, each acting as main contacts for the various services, the main contact may not yet have login access and it can be granted by submitting this email form.

At this point, the client can login to their portal and view their intake forms. When the client logs in, they should see the new service in their Home Screen, shown below:

![](images/portal-home-page-with-action-needed.png)

Within the service, the client will now see their list of action items, like this:

![](images/portal-intake-forms.png)

When they have completed all intake forms, the client will see the following message in the top-right of their screen, 

![](images/portal-all-set-msg.png)

and the service will automatically transition to [Initial Info Review](#formation-incorporation-intake-review).

#### Formation Incorporation Intake Review

During the Initial Review, the Specialist does the intake assessment. If all tasks are completed, they can manually move the service to [Incorporation Document Creation](#formation-incorporation-document-creation); otherwise, they should schedule an interview with the client using the [Info Requests](#info-requests) tool.

The interview process can be started using the **Appointments Tool**, shown below:

![](images/formation-appointments.png)

Appointments can be created using the green **+ New** button shown above. 

![](images/formation-single-appointment.png)

Once created, the type of appointment should be set. In this case, we should set the appointment to **Apointment - Initial Interview**. (see above image to adjust the type of appointment)

Then, information about the actual appointment should be recorded, and the Appointment can be moved to **Scheduled**. When the Interview moves to **Scheduled**, the project moves to [Interview](#formation-interview).

#### Formation Interview

When the service is in Interview, it means that the client interview has been scheduled or is currently in progress. 

When the interview takes place, the specialist should keep the **Interview** tool open to walk them through the pieces of information they need to capture and record the information requested there.

When the interview is completed, the **Interview Record** in the tool should be moved to **Completed**. Depending on the type of appointment, the service will move to the next appropriate status. 
- **Appointment - Initial Interviews** move the formation service to [Federal Exemption in Progress](#formation-federal-exemption-in-progress) upon completed.
- **Appointment - Incorporation Document Clarification** move the formation service to [Incorporation Document Creation](#formation-incorporation-document-creation) upon completed.
- **Appointment - Incorporation Document Clarification** move the formation service to [Federal Exemption Intake](#formation-federal-exemption-intake) upon completed.

##### TODOS:
- [ ] Emails:
  - [ ] #4b or #4c ES IV  - Is required
  - [ ] #4 ES IV for ECFC


#### Formation Incorporation Document Creation

If documents are not needed, the specialist should manually move the service to [Federal Exemption in Progress](#formation-federal-exemption-in-progress).

If clarification from the client is needed to complete the documents, you can use the **Appointments tool** to schedule an **Appointment - Incorporation Document Clarification**-type appointment. This will move the project to [Interview](#formation-interview).

The specialist can then create and upload the documents using the **Incorporation Document** tool, shown below. 

![](images/incorporation-doc-review.png)
When the document is ready, the specialist can move the document to **Client Review**, which will move the service back to **Incorporation Intake** 
- When the client reviews & approves the document in their portal, the project now moves to [Incorporation Review Appointment Needed](#formation-incorporation-review-appointment-needed).
##### TODOS:
- [ ] Emails:
	- [ ] #3c - Docs Created

#### Formation Incorporation Review Appointment Needed

In this status, the client has reviewed the **Incorporation Documents** and the **Appointments** tool should be used to schedule an **Incorporation Document Review Appointment**, in the same manner that it has been used to schedule appointments earlier in the workflow.
- When the Appointment is moved to **Scheduled**, the project moves to **Interview**.
- When the **Appointment** is completed, the project moves to [Federal Exemption in Progress](#formation-federal-exemption-intake).

#### Formation Federal Exemption in Progress
##### TODOS:
- [ ] #4d - Interview Complete
Dynamic list of tasks - 
goes every 2 weeks if no action
- [ ] If all action items for the client are completed, move to [Federal Exemption Intake](#formation-federal-exemption-intake).

#### Formation Federal Exemption Intake

When transitioned to this status, the Federal Exemption Intake tasks are generated. When the client completes all of these tasks, the service automatically moves into [TPS Review](#formation-tps-review).

##### TODOS:
- [ ] Create federal exemption intake client tasks

#### Formation TPS Review

In TPS (Tech Pre-Screen) Review, the TPS Review Checklist is generated and can be found in the **Tasks** tool. When the FG Team Member completes this task, the project moves to [Federal Exemption in Production](#formation-federal-exemption-in-production).

##### TODOS:
- [ ] Get Client Review states from Dee so we can map over to what the Client sees in the portal (differentiate between/show progress) 
- [ ] Template in the checklist and instructions for TPS Review Checklist
- [ ] Email: When the TPS Review Checklist is completed, #6 - Production PreScreen - 1 time email 
	Confirms all input received.  Sets
	expectation of completion by production
	team.  Ends previous auto send email 
- [ ] Day 2:
	- [ ] See the dates when project has transitioned in the portal

#### Formation Federal Exemption in Production

The specialist can now create and upload the documents using the **Federal Exemption** tool, shown below. 

![](images/fed-exemp-review.png)
When the document is ready, the specialist can move the document to **Internal Review**, which will move the service to [Internal Review](#formation-internal-review). 

#### Formation Internal Review

Here, the ES Reviews packet (the Federal Exemption Document created in the previous stage). If the document requires changes, the Tech Writer makes the necessary modifications. When the document is ready, it can be moved to Final Internal Review, which also moves the project to [Final Internal Review](#formation-final-interal-review).

#### Formation Final Internal Review

- During this stage, the SCR reviews the excemption document. If changes are required, they can add notes in the comments of the document and move it back into Interal Review (which will also bring the service/project back into [Internal Review](#formation-internal-revbiew)).
- If no more changes are required, the document can be moved to Client Review, which moves the service/project to [Client Review](#formation-client-review), where the document is visible in the Client's Action Items list for review.

##### TODOS:
	- [ ] And transition brings up an email notification to let the client know their packet is ready for review.

#### Formation Client Review

- Here, the documents appear for review in the client's portal in their action items list for the service, shown below.
	- They can either approve the document, which will move the service into [Not Downloaded](#formation-not-downloaded), 
	- or request changes, leaving notes in the **Change Request Notes** field, moving the service/project back into [Internal Review](#formation-internal-review).
##### TODOS:
- [ ] #7 - Exemption App Docs ready. Repeat every 2 weeks until client downloads See queue #1
- [ ] We can skip Not Downloaded and, when they download this, flag it so that we can check if they are allowed to Approve

#### Formation Not Downloaded
At this point, the final documents have been approved by the client, but the client has not yet downloaded their documents.
##### QUESTION:
- [ ] Is this status necessary? They have downloaded/viewed the document in the previous step already, so it seems like we can just move straight to **Formation Downloaded**.
##### TODOS:
- [ ] When the client has downloaded their documents, auto move to Formation Downloaded.

#### Formation Downloaded

- At this point, the client has downloaded their documents, but we have not yet received confirmation that they have submitted it to the IRS. 
- In the client portal, the service is now in Action Needed, and the client should see an action item to verify/report that they have submitted the documents to the IRS. When they have marked it as so in the action item, the service/project will move into [At IRS](#formation-at-irs).

##### TODOS:
- [ ] Email #7a - Downloaded but not submitted - 
Every 2 weeks until submitted

#### Formation At IRS
- New action item set for the exemption letter
	- [ ] When the client receives the exemption letter from the IRS, they should upload the letter to the action item and move it to "Exemption Letter Received", which will move the service/project into its "Done" status [IRS Approved](#formation-irs-approved).
##### TODOS:
- [ ] Email #7b - At IRS - reminder every 60 days

#### Formation IRS Inquiry
#### Formation IRS Approved

### Making Changes to Tasks/Forms

<https://www.loom.com/share/1ac15c6d0ebd47feabd61b495c6ca27c>

Video walkthrough of:
 - Creating and Editing Form Fields
 - Creating and Editing Tasks

***

### Creating and Editing Form Fields

You can create and edit form fields for the Formation service by following the steps listed below:

1. Navigate to the Headquarters (HQ).
2. Click `Sets` in the toolbar on the left of the screen.

![](images/hq-sets.png)

3. Search for `Formation Tasks` on the Set page.

![](images/set-search-formation-tasks.png)

4. Click on the form you would like to edit.

![](images/formation-set-selection.png)

5. Click to open one of the forms for this Set.
6. Edit the form fields as desired. **(Changes are saved automatically. There is no `Save` button.)**

![](images/formation-edit-a-set.png)

### Creating and Editing Tasks

1. Navigate to the Headquarters (HQ).
2. Click `Projects` in the toolbar on the left of the screen.

![](images/hq-projects.png)

3. Open the Project Type dropdown by clicking `All Types`.
4. Hover over the Project Type you would like to edit, and select `Edit Workflow` from the next popup menu that appears.

![](images/projects-edit-workflow.png)

5. This screen shows the workflow stages/steps for the Project Type you selected in the previous step. Click on the stage/step you would like to edit.
6. Select the `Actions` tab.

![](images/workflow-actions-tab.png)

7. Select an Action that has already been created to make changes.

	#### Creating a new Task
	
	8. Click the `New` button to begin.
	9. In the dropdown, select `Create Record(s)` as the action type.
	10. In the search box, search for `Formation Tasks`. Select the option that comes up.
	11. You can now give the Action a name and click the `New` button to create a new Task.
	12. The images below explain how to setup tasks.
	13. Once you have completed your updates, scroll down and click the `Save` button to save your work.

![](images/action-type.png)
<br />
![](images/new-action.png)
<br />
![](images/new-action-item.png)
<br />
![](images/new-action-item2.png)

<div style="page-break-after: always;"></div>

## Abatement
https://www.loom.com/share/995659a527b54f3d9525ba1ff29592fe

### Abatement Intake

All new **Abatement** projects start here. In Intake, the client task **Upload your IRS penalty letter (CP141L)** is generated and can be found in the client's portal.

At this point, the client can login to their portal and view their intake forms. When the client logs in, they should see the new service in their Home Screen, shown below:
![](images/abatement-in-portal-service-list.png)

Within the service, the client will now see their list of action items, like this:

![](images/abatement-portal-action-items-list.png)



When they have completed all intake forms, the client will see the following message in the top-right of their screen, 

![](images/portal-all-set-msg.png)

and the service will automatically transition to [Work in Progress](#abatement-work-in-progress).

### Abatement Work in Progress

- In the [Client Portal](#client-portals) the service now appears as [Preparing](#portal-status-preparing). 

While in this status, the assigned [Specialist](#specialist-role) on the project should review the IRS Penalty Letter uploaded by the client (found in the Client Action Items page for the project).

Then, they can create a review document for the client in the "Abatement Client Review" tool. A file can be uploaded there, and when ready, the Client Review should be moved to Awaiting Client Review, which will transition the project to **Client Signature Needed**.

### Abatement Client Signature Needed

Here, the project appears as **Action Needed** in the client portal. They can open the project, download their document, and upload their signed version, or submit change request notes.
![](images/abatement-review.png)

### Abatement Work Complete

Here, work is completed on this project and it can be found in the client's History page.

## Service Tools

### Info Requests

Info Requests can be created within Services to provide additional action items for the client. The Info Requests tool is essentially the same between the various services, and is shown below:

![](images/client-info-request.png)

- In **Preparing for Client**, the **Specialist** can customize the action item for the Client. 
  - The **Details** and **Reference Files** fields *above* the dividing line are for the action item to be customized for the client and can be edited by Foundation Group team members. For clients in the portal, these fields are view-only.
  - The **Answer** and **Attachment(s)** fields *below* the dividing line are for the client to answer the question/fulfill the request. 
- When the action item is ready for the client, the specialist should move the item to **More Info Requested**. This will also move the service to **More Info Requested**, and the service will appear in **Action Needed** in the portal, with the info request appearing in the action items list.

![](images/action-items-in-portal.png)

- When the client completes the info requests in the portal (the same way they complete intake form action items), the info request moves to **Submitted by Client** and the service moves to **Initial Info Review**. (Formation services move to [Incorporation Intake Review](#formation-incorporation-intake-review))

### Client Reviews

**Client Reviews** are similar to [Info Requests](#info-requests) -- they are a way of creating action items for the client. However, the review action items differ in that they offer **Approve/Request Change** options to the client, instead of just **Submit**.

The Client Reviews tool can be found in relevant services in the tools menu, shown below:

![](images/client-review-items.png)

- In **Preparing for Client**, the **Specialist** can customize the action item for the Client. 
  - The **Details** and **Reference Files** fields *above* the dividing line are for the action item to be customized for the client and can be edited by Foundation Group team members. For clients in the portal, these fields are view-only.
  - The **Answer** and **Attachment(s)** fields *below* the dividing line are for the client to answer the question/fulfill the request. 
- When the action item is ready for the client, the specialist should move the item to **Awaiting Client Review**. This will also move the service to **Sent to Client**, and the service will appear in **Action Needed** in the portal, with the client review action item appearing in the action items list, shown below:

![](images/client-review-approve-adjust.png)

In 990 services, the options are **Authorized** and **Needs Adjustment**:

- Clicking **Authorized** will complete the action item and move the service to [Authorized](#990-authorized).
- Clicking **Needs Adjustment** will complete the action item and move the service to [Client Revision Request](#990-client-revision-request)

In Charitable Solicitations services, the options are **Approved Letter Received** and **Needs Adjustment**:

- Clicking **Approved Letter Received** will complete the action item and move the service to [Approved Letter Received](#charitable-solicitations-initial-approved-letter-received).
- Clicking **Needs Adjustment** will complete the action item and move the service to [Client Revision Request](#charitable-solicitations-initial-client-revision-request)

### Client Services

Each service has a Client Service page, which can be used to upload the final deliverable items for the client and transition the project to its "done" state/cue. 

![](images/client-service-deliverables-table.png)

When **Add to Portal History page** is clicked:

- A form to send an email notification to the client is brought up, shown below:
  - ![](images/deliverables-email-send.png)
- and the project is transitioned to its "done" cue, and is now visible in the **History** page in the client portal.

<div style="page-break-after: always;"></div>

## Services Roles

### Specialist Role



# Navigation

<div style="page-break-after: always;"></div>

## Navigating Between Teams

From your **My Stuff** page (your homepage, which should load when you initially login), you can open all Teams you have access to through the **Teams** page shown below:

![](images/my-teams.png)

# Client Portals

<div style="page-break-after: always;"></div>

## Client Portals History Page

In the **History** page, the client can see all completed services and download the final deliverable documents for them. The main **History** page is shown below:

![](images/portal-history.png)

After opening the individual services, you can download the deliverables here:

![](images/portal-deliverable-download.png)

# Delivery

<div style="page-break-after: always;"></div>

## Delivery Documents

### What are they?
A delivery document is a single page view of a group of intake forms. These pages can also be exported as PDF's if needed. 

### How do I use them?
To add a delivery document to a project, first navigate to the homepage of a project, then click the documents tool on the left.

![](images/projectHome-documents.png)

If you see the document you want on the following screen, just click the title of the document to view it.

### Add a delivery document to a project
If you don't see the document you want, click the plus button to begin adding a new document. In the popup box that appears, you can search for the delivery document you would like to add. Once you find it, click the green "Use this template" button to add the delivery document to the current project.

![](images/documentsTool.png)

![](images/searchDocTemplates.png)

Once the page loads, you'll see the merged delivery document.

![](images/mergedDeliveryDoc.png)

> Load times for these documents are currently quite long depending on how much data is being merged into them. We are currently working on a dedicated service that will greatly reduce this load time.

### Export a delivery document as a PDF
If you would like to export this ad a PDF, click the options menu on the top right of the page. Then select the "View PDF" option.
> Depending on your browser, the download may be blocked. If this happens, just allow your browser to download files from Bento and repeat the export process outlined here.

![](images/viewPDF.png)

<div style="page-break-after: always;"></div>

# Client Portals

<div style="page-break-after: always;"></div>

# Recent Client Action Flow

Across all client services, the conversation flow between client users in the Client Portal and Foundation Group Team members is consistent.

If a client user has a question about an action item, they can reach out for clarification directly on the action item by scrolling down to the bottom of the action item's form and posting a comment, shown below.

![](images/client-action-item-post-comment.png)

Once the client posts a comment, the project will remain in its current state (most likely 'Intake') but appear in the **Recent Client Action** feed. 

![](images/recent-client-action.png)

From there, a Foundation Group team member can open up the **Work Log** tool within the project to see what the client has posted, and follow the link on the comment to the form in which they posted the comment.

![](images/work-log.png)

From there, the Foundation Group team member can reply to the client by posting a *public* comment. Once the public comment is posted, the client will receive a notification email with the content of the comment, and the project is automatically removed from the **Recent Client Action** feed.

![](images/post-public-comment-response.png)

# FAQs

Coming Soon...

