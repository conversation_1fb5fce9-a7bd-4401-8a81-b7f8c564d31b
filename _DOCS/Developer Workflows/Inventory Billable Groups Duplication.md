📄 Problem Statement
Problem Identification: A critical data integrity issue has been identified within our inventory_billable_combinations records. The duplication process for these records has been performing a shallow copy, rather than a deep, recursive copy of the entire record hierarchy. This has resulted in new top-level combinations referencing the original nested inventory_billable_groups and their subsequent choices, rather than new, independent copies of those groups.

Impact: This violation of referential integrity means that any modifications to a "duplicated" combination's sub-items directly alter the original source items. This fundamentally breaks the intended modularity of the data structure and can lead to unpredictable pricing, incorrect inventory management, and inconsistent menu offerings. For example, a "2025" combination's items are incorrectly linked to a "2023" source, leading to a disconnected data tree and inconsistent pricing.

🛠️ Proposed Solution: Deep Duplication Script
Our immediate solution is a robust, asynchronous JavaScript script designed to perform a comprehensive deep copy of a given record tree.

What it does:
The script is initiated with the ID of a single, top-level inventory_billable_combinations record. It then performs a recursive traversal of the entire record hierarchy.

How it works:

Queue Management: The script uses an internal idMap to track which original IDs have been duplicated and what their new IDs are. This prevents infinite loops in cases of circular references and ensures that a record is only duplicated once.

Recursive Duplication: For each record retrieved, the script first inspects its nested items and choices arrays. It recursively calls itself to duplicate any inventory_group records it finds within these arrays.

Dependency Resolution: The script utilizes an async/await pattern to ensure that the duplication of all child nodes is completed before the parent node is created. This guarantees that all internal pointers (inventory_group IDs) on the newly created parent record correctly reference the new child records.

Database Interaction: All database calls (getById, create) are wrapped in a setTimeout of 10ms. This prevents the server from being overloaded with concurrent requests and mitigates the risk of a 503 Service Unavailable error, making the script suitable for production environments.

Record Linking: Each newly created record will have its data_source_id field populated with the id of its original source record, creating a clear and traceable link for future analysis and updates.

This script effectively creates a parallel, independent copy of the original data tree, correcting the shallow copy issue.

⏭️ Next Phase: Updating Existing Records
The successful deep duplication of the original record is the first part of our two-phase solution. The second phase involves fixing the incorrectly duplicated records that currently exist.

Problem to solve: We have already identified the "shallow" copies (e.g., the record with ID 20456861). These records currently have a new top-level ID but still point to the old, pre-duplication inventory_group IDs.

Proposed approach: We need to develop a second script that will do the following:

Identify Target Records: Use a list of known "shallow" copy IDs (like 20456861) as a starting point.

Find the New Duplicates: For each shallow copy, the script needs to find its corresponding correct, deep-duplicated version. It can do this by using the data_source_id field. For example, if a shallow copy's data_source_id is 18240191, it should find the new, deep-duplicated version whose data_source_id is also 18240191.

Re-link the Records: Once the correct, deep-duplicated sub-items are identified, the script will update the inventory_group IDs on the shallow copy to point to the new, correct IDs. This will effectively detach the shallow copy from the old data tree and attach it to the new one, making it a fully functional, deep copy.

Final Cleanup: After a successful re-linking, the script can be modified to delete the incorrect, shallow-copied records if necessary, or simply be used to correct them
