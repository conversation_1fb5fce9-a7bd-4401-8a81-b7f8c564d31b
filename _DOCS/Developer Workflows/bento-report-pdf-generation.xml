<?xml version="1.0" encoding="UTF-8"?>
<BentoDevelopment>
  <Feature>
    <Name>Report PDF Generation</Name>
    <Description>Framework for generating PDF versions of reports in the Bento system</Description>
    <Components>
      <Component>
        <Name>formatReportAsHtml</Name>
        <Location>_SRC/notify/_components/inventory/endOfProjectReport.js</Location>
        <Description>
          Converts report data structure into HTML format suitable for PDF generation.
          This function serves as a template for how other reports can be formatted for PDF export.
        </Description>
        <Usage>
          <Code>
            var reportWithData = _.extend({}, report, { data: data });
            var html = formatReportAsHtml(reportWithData);
            sb.data.makePDF(html, 'D'); // 'D' for download, 'I' for inline viewing
          </Code>
        </Usage>
      </Component>
      <Component>
        <Name>sb.data.makePDF</Name>
        <Location>Backend PHP service</Location>
        <Description>
          Bento core service for generating PDFs from HTML content.
          Takes HTML content and generates a PDF file, with options for downloading or inline viewing.
        </Description>
        <Parameters>
          <Parameter>
            <Name>html</Name>
            <Type>string</Type>
            <Description>HTML content to be converted to PDF</Description>
          </Parameter>
          <Parameter>
            <Name>mode</Name>
            <Type>string</Type>
            <Description>
              Output mode for the PDF:
              - 'I': Display inline in browser
              - 'D': Force download
              - 'F': Save to file on server
              - 'S': Return as string
            </Description>
          </Parameter>
        </Parameters>
      </Component>
    </Components>
    <Implementation>
      <Step>
        <Description>Create a formatReportAsHtml function in your report component</Description>
        <Details>
          This function should convert your report's data structure into clean HTML that can be rendered as a PDF.
          Make sure to include all necessary styling inline or reference standard Bento styles.
        </Details>
      </Step>
      <Step>
        <Description>Add UI controls for PDF actions</Description>
        <Details>
          Add buttons or links that trigger PDF generation, with appropriate icons:
          - Print (for inline viewing): &lt;i class="print icon"&gt;&lt;/i&gt;
          - Download: &lt;i class="file pdf icon"&gt;&lt;/i&gt;
        </Details>
      </Step>
      <Step>
        <Description>Wire up event handlers</Description>
        <Details>
          Create click handlers that prepare the data and call the formatReportAsHtml function,
          then pass the result to sb.data.makePDF with the appropriate mode parameter.
        </Details>
      </Step>
    </Implementation>
    <BestPractices>
      <Practice>
        <Description>Handle data carefully</Description>
        <Details>
          Ensure that your formatReportAsHtml function handles all possible data scenarios, including:
          - Missing or null data
          - Empty collections
          - Unexpected data types
          This prevents PDF generation errors for edge cases.
        </Details>
      </Practice>
      <Practice>
        <Description>Use proper HTML structure</Description>
        <Details>
          Follow standard HTML practices for PDFs:
          - Use proper heading hierarchy (h1, h2, etc.)
          - Use tables for tabular data
          - Include margins and spacing for readability
          - Keep styling simple and printer-friendly
        </Details>
      </Practice>
      <Practice>
        <Description>Test with various data sets</Description>
        <Details>
          Test PDF generation with:
          - Minimal data
          - Maximum data (stress test)
          - Special characters
          - Different browsers
        </Details>
      </Practice>
    </BestPractices>
  </Feature>
</BentoDevelopment>