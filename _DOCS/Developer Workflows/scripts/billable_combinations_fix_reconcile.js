// This object must be provided by your environment.
// It should contain the methods: getById, create, update, getWhere

/**
 * A recursive function to traverse and replace old IDs with new IDs.
 * @param {object} record The record object to traverse.
 * @param {Map<number, number>} idMap A map of oldId -> newId relationships.
 */
function traverseAndReplace(record, idMap) {
    if (!record) return;

    if (record.inventory_group && idMap.has(record.inventory_group)) {
        console.log(`      Mapping old inventory_group ID ${record.inventory_group} to new ID ${idMap.get(record.inventory_group)}`);
        record.inventory_group = idMap.get(record.inventory_group);
    }

    // Recursively check nested items
    if (record.items && Array.isArray(record.items)) {
        for (const item of record.items) {
            traverseAndReplace(item, idMap);
        }
    }

    // Recursively check nested choices
    if (record.choices && Array.isArray(record.choices)) {
        for (const choice of record.choices) {
            traverseAndReplace(choice, idMap);
        }
    }

    // Recursively check nested selections
    if (record.selection && Array.isArray(record.selection)) {
        for (const selection of record.selection) {
            traverseAndReplace(selection, idMap);
        }
    }
}

/**
 * Main function to find and reconcile shallow copies with new deep-copied records.
 * This version starts with the original source ID and finds all shallow copies.
 * @param {number} sourceId The ID of the original record to reference.
 * @param {boolean} dryRun If true, only logs actions without making changes.
 */
async function reconcileRecords(sourceId, dryRun = true) {
    console.log(`Starting record reconciliation process in ${dryRun ? 'DRY RUN' : 'LIVE'} mode.`);

    // --- Phase 1: Finding Shallow Copies ---
    await new Promise(resolve => setTimeout(() => {
        console.log(`\n--- Phase 1: Finding Shallow Copies ---`);
        console.log(`> Fetching original source record with ID ${sourceId}`);
        databaseConnection.obj.getById('inventory_billable_combinations', sourceId, (originalSource) => {
            if (!originalSource) {
                console.error(`Error: Original source record with ID ${sourceId} not found. Aborting.`);
                resolve();
                return;
            }

            const shallowCopyName = originalSource.name + ' (*)';
            console.log(`> Searching for shallow copies with name "${shallowCopyName}"`);
            databaseConnection.obj.getWhere('inventory_billable_combinations', { name: shallowCopyName }, async (shallowCopies) => {
                if (shallowCopies.length === 0) {
                    console.warn(`Warning: No shallow copies found with name "${shallowCopyName}". Skipping reconciliation.`);
                    resolve();
                    return;
                }

                console.log(`> Found ${shallowCopies.length} shallow copies:`);
                const shallowCopyIds = shallowCopies.map(rec => rec.id);
                console.log(`   - Shallow Copy IDs: ${shallowCopyIds.join(', ')}`);

                // --- Phase 2: Building the ID Mapping ---
                console.log(`\n--- Phase 2: Building ID Mapping ---`);
                const idMap = new Map();
                const originalGroupIds = originalSource.items.map(item => item.inventory_group);
                console.log(`> Found original nested group IDs from source: ${originalGroupIds.join(', ')}`);

                for (const originalGroupId of originalGroupIds) {
                    await new Promise(resolveGroup => setTimeout(() => {
                        console.log(`> Searching for new deep-copied records with data_source_id = ${originalGroupId}`);
                        databaseConnection.obj.getWhere('inventory_billable_groups', { data_source_id: originalGroupId }, (newRecords) => {
                            if (newRecords.length > 0) {
                                // Assuming there's only one new deep copy per original source record
                                const newRec = newRecords[0];
                                idMap.set(originalGroupId, newRec.id);
                                console.log(`   - New record ID ${newRec.id} (Name: "${newRec.name}") links to old ID ${newRec.data_source_id}`);
                            } else {
                                console.warn(`Warning: No new deep-copied record found for original group ID ${originalGroupId}. This branch will be skipped.`);
                            }
                            resolveGroup();
                        });
                    }, 10));
                }

                // --- Phase 3: Updating the Shallow Copies ---
                console.log(`\n--- Phase 3: Updating Shallow Copies ---`);
                if (idMap.size > 0) {
                    // Take only the first shallow copy to reconcile, as requested
                    const shallowCopy = shallowCopies[0];
                    console.log(`> Reconciling only the first shallow copy found: ID ${shallowCopy.id}`);
                    const updatedRecord = _.clone(shallowCopy);
                    traverseAndReplace(updatedRecord, idMap);

                    // Set the visibility flag to 0 for the reconciled record
                    updatedRecord.is_hidden_from_menu_selections = 0;
                    console.log(`> Setting is_hidden_from_menu_selections to 0 for record ID ${shallowCopy.id}.`);

                    if (dryRun) {
                        console.log(`[DRY RUN] Would update record ID ${shallowCopy.id} with the following new structure:`);
                        console.log(JSON.stringify(updatedRecord, null, 2)); // Log the entire object
                    } else {
                        await new Promise(resolveUpdate => setTimeout(() => {
                            databaseConnection.obj.update(shallowCopy.object_bp_type, updatedRecord, (result) => {
                                console.log(`✅ Successfully updated shallow copy record ID ${result.id}.`);
                                resolveUpdate();
                            });
                        }, 10));
                    }
                } else {
                    console.warn(`Reconciliation skipped due to missing deep-copied records.`);
                }
                resolve();
            });
        });
    }, 10));

    console.log(`\nAll operations finished.`);
}

// =================================================================================================
// SCRIPT EXECUTION - SIMPLE VERSION
// =================================================================================================

// STEP 1: Find all shallow copies (records with (*) in name)
function fixAllShallowCopies(ids, dryRun = true) {
    console.log(`Finding all shallow copies to fix...`);

    // Get all records with (*) in the name - these are the shallow copies
    databaseConnection.obj.getById('', ids, function(allRecords) {
        const shallowCopies = allRecords.filter(record => record.name.includes('(*)'));
        console.log(`Found ${shallowCopies.length} shallow copies to fix`);

        // Build map of original -> duplicate for inventory_billable_groups
        databaseConnection.obj.getWhere('inventory_billable_groups', {}, function(allGroups) {
            const idMap = new Map();
            allGroups.forEach(group => {
                if (group.data_source_id) {
                    idMap.set(group.data_source_id, group.id);
                }
            });
            console.log(`Built mapping for ${idMap.size} group duplicates`);

            // Fix each shallow copy
            shallowCopies.forEach((shallowCopy, index) => {
                setTimeout(() => {
                    console.log(`\n--- Fixing ${index + 1}/${shallowCopies.length}: "${shallowCopy.name}" (ID: ${shallowCopy.id}) ---`);

                    const updatedRecord = _.clone(shallowCopy);
                    traverseAndReplace(updatedRecord, idMap);

                    if (dryRun) {
                        console.log(`[DRY RUN] Would update record ${shallowCopy.id}`);
                    } else {
                        databaseConnection.obj.update('inventory_billable_combinations', updatedRecord, function(result) {
                            console.log(`✅ Fixed record ${result.id}: "${result.name}"`);
                        });
                    }
                }, index * 200); // Delay between each fix
            });
        });
    });
}

const IDS = [];
// RUN THIS TO FIX ALL SHALLOW COPIES
const DRY_RUN = true;
fixAllShallowCopies(IDS, DRY_RUN);
