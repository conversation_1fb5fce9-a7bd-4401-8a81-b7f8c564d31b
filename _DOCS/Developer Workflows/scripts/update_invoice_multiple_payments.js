function findAndPrepareUpdates(invoices, paymentData, commitChanges = false) {
    const payments = paymentData.invoicePayments;
    const paymentMap = new Map(payments.map((p) => [p.id, p]));

    // Track which invoices reference each payment
    const paymentReferences = new Map();

    // Build the reference map
    invoices.forEach((invoice) => {
        if (!invoice.payments) return;
        invoice.payments.forEach((paymentId) => {
            if (!paymentReferences.has(paymentId)) {
                paymentReferences.set(paymentId, []);
            }
            paymentReferences.get(paymentId).push({
                invoiceId: invoice.id,
                invoiceAmount: invoice.amount,
            });
        });
    });

    // Find payments referenced by multiple invoices
    const duplicates = [];
    paymentReferences.forEach((references, paymentId) => {
        if (references.length > 1) {
            const payment = paymentMap.get(parseInt(paymentId));
            if (!payment) return;

            const correctInvoice = references.find(
                (ref) => ref.invoiceAmount === payment.amount
            );
            const incorrectInvoices = references.filter(
                (ref) => ref.invoiceAmount !== payment.amount
            );

            duplicates.push({
                paymentId,
                paymentAmount: payment.amount,
                correctInvoice: correctInvoice?.invoiceId,
                removeFrom: incorrectInvoices.map((inv) => inv.invoiceId),
            });
        }
    });

    if (duplicates.length === 0) {
        console.log("✅ No duplicate payment assignments found");
        return;
    }

    // Log findings
    console.group("🔍 Duplicate Payment Assignments");
    console.table(
        duplicates.map((d) => ({
            "Payment ID": d.paymentId,
            "Payment Amount": `$${d.paymentAmount / 100}`,
            "Belongs to Invoice": d.correctInvoice,
            "Remove from Invoices": d.removeFrom.join(", "),
        }))
    );
    console.groupEnd();

    // Get all invoice IDs that need updates
    const invoiceIdsToUpdate = [
        ...new Set(duplicates.flatMap((d) => d.removeFrom)),
    ];

    // Function to process the invoices
    const processInvoices = (invoices) => {
        console.log("Invoices to update:", invoices);
        invoices.forEach((invoice) => {
            const dupePayments = duplicates.filter((d) =>
                d.removeFrom.includes(invoice.id)
            );
            const paymentsToRemove = dupePayments.map((d) =>
                parseInt(d.paymentId)
            );
            const updatedPayments = invoice.payments.filter(
                (p) => !paymentsToRemove.includes(p)
            );

            console.log(`\nInvoice ${invoice.id}:`);
            console.log("Current payments:", invoice.payments);
            console.log("Updated payments:", updatedPayments);

            if (commitChanges) {
                console.log(`🔄 Updating invoice ${invoice.id}...`);
                databaseConnection.obj.update(
                    "invoices",
                    {
                        id: invoice.id,
                        payments: updatedPayments,
                    },
                    (result) =>
                        console.log(`✅ Updated invoice ${invoice.id}:`, result)
                );
            }
        });
    };

    // Fetch and process invoices
    databaseConnection.obj.getById(
        "invoices",
        invoiceIdsToUpdate,
        processInvoices
    );

    return duplicates;
}

// Usage:
// For dry run (just print):
const duplicates = findAndPrepareUpdates(temp1, temp2);

// To commit changes:
// const duplicates = findAndPrepareUpdates(temp1, temp2, true);
