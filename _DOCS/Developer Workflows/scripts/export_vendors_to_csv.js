function exportVendorsToCSV() {
  // Get all vendors
  databaseConnection.obj.getWhere('companies', {is_vendor: 1}, function(vendors) {
    console.log(`Processing ${vendors.length} vendors...`);

    // Create CSV header
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "ID,Vendor Name,Active?\n";

    // Process vendors in batches
    const batchSize = 100;
    let currentIndex = 0;

    function processBatch() {
      const endIndex = Math.min(currentIndex + batchSize, vendors.length);

      // Process current batch
      for (let i = currentIndex; i < endIndex; i++) {
        const vendor = vendors[i];
        // Escape quotes in company names
        const escapedName = vendor.name ? vendor.name.replace(/"/g, '""') : '';
        csvContent += `${vendor.id},"${escapedName}",\n`;
      }

      currentIndex = endIndex;

      // Show progress
      console.log(`Processed ${currentIndex}/${vendors.length} vendors`);

      // Continue with next batch or finish
      if (currentIndex < vendors.length) {
        setTimeout(processBatch, 0);
      } else {
        // All vendors processed, create download
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "vendors.csv");
        document.body.appendChild(link);

        // Trigger download
        link.click();

        // Clean up
        document.body.removeChild(link);

        console.log(`Exported ${vendors.length} vendors to CSV`);
      }
    }

    // Start processing
    processBatch();
  });
}

// Run the function
exportVendorsToCSV();
