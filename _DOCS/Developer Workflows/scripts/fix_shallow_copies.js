// SIMPLE SCRIPT TO FIX ALL SHALLOW COPIES

/**
 * Find or create a duplicate of an inventory_billable_group
 */
async function findOrCreateDuplicate(originalGroupId, duplicateCache, dryRun) {
    if (duplicateCache.has(originalGroupId)) {
        return duplicateCache.get(originalGroupId);
    }

    return new Promise(resolve => {
        databaseConnection.obj.getById('inventory_billable_groups', originalGroupId, function(originalGroup) {
            if (!originalGroup) {
                console.log(`    ⚠️  Group ${originalGroupId} not found, keeping original`);
                duplicateCache.set(originalGroupId, originalGroupId);
                resolve(originalGroupId);
                return;
            }

            const duplicateName = originalGroup.name + ' (*)';
            
            // Look for existing duplicate
            databaseConnection.obj.getWhere('inventory_billable_groups', {}, function(allGroups) {
                const existingDuplicate = allGroups.find(group => group.name === duplicateName);

                if (existingDuplicate) {
                    console.log(`    ✅ Found duplicate: ${originalGroupId} -> ${existingDuplicate.id} ("${existingDuplicate.name}")`);
                    duplicateCache.set(originalGroupId, existingDuplicate.id);
                    resolve(existingDuplicate.id);
                } else {
                    console.log(`    🔄 Creating duplicate for: "${originalGroup.name}"`);
                    
                    if (dryRun) {
                        const fakeId = `NEW_${originalGroupId}`;
                        console.log(`    [DRY RUN] Would create: ${originalGroupId} -> ${fakeId}`);
                        duplicateCache.set(originalGroupId, fakeId);
                        resolve(fakeId);
                    } else {
                        const newGroup = _.clone(originalGroup);
                        delete newGroup.id;
                        newGroup.name = duplicateName;
                        newGroup.data_source_id = originalGroupId;

                        databaseConnection.obj.create('inventory_billable_groups', newGroup, function(created) {
                            console.log(`    ✅ Created: ${originalGroupId} -> ${created.id} ("${created.name}")`);
                            duplicateCache.set(originalGroupId, created.id);
                            resolve(created.id);
                        });
                    }
                }
            });
        });
    });
}

/**
 * Recursively traverse and fix all inventory_group references
 */
async function traverseAndFix(node, duplicateCache, dryRun, path = '') {
    if (!node) return;

    // Fix inventory_group reference
    if (node.inventory_group) {
        const originalId = node.inventory_group;
        const newId = await findOrCreateDuplicate(originalId, duplicateCache, dryRun);
        if (newId !== originalId) {
            console.log(`    ${path}inventory_group: ${originalId} -> ${newId}`);
            node.inventory_group = newId;
        }
    }

    // Process nested arrays
    if (node.items && Array.isArray(node.items)) {
        for (let i = 0; i < node.items.length; i++) {
            await traverseAndFix(node.items[i], duplicateCache, dryRun, `${path}items[${i}].`);
        }
    }

    if (node.choices && Array.isArray(node.choices)) {
        for (let i = 0; i < node.choices.length; i++) {
            await traverseAndFix(node.choices[i], duplicateCache, dryRun, `${path}choices[${i}].`);
        }
    }
}

/**
 * Fix all shallow copies
 */
async function fixAllShallowCopies(dryRun = true) {
    console.log(`=== FIXING ALL SHALLOW COPIES ===`);
    
    return new Promise(resolve => {
        databaseConnection.obj.getWhere('inventory_billable_combinations', {}, async function(allRecords) {
            const shallowCopies = allRecords.filter(record => record.name.includes('(*)'));
            console.log(`Found ${shallowCopies.length} shallow copies to fix`);
            
            const duplicateCache = new Map();
            
            for (let i = 0; i < shallowCopies.length; i++) {
                const shallowCopy = shallowCopies[i];
                console.log(`\n--- ${i + 1}/${shallowCopies.length}: Fixing "${shallowCopy.name}" (ID: ${shallowCopy.id}) ---`);
                
                const updatedRecord = _.clone(shallowCopy);
                await traverseAndFix(updatedRecord, duplicateCache, dryRun);
                
                if (dryRun) {
                    console.log(`[DRY RUN] Would update record ${shallowCopy.id}`);
                } else {
                    await new Promise(resolveUpdate => {
                        databaseConnection.obj.update('inventory_billable_combinations', updatedRecord, function(result) {
                            console.log(`✅ Updated record ${result.id}: "${result.name}"`);
                            resolveUpdate();
                        });
                    });
                }
                
                // Small delay between records
                await new Promise(delay => setTimeout(delay, 200));
            }
            
            console.log(`\n=== ALL SHALLOW COPIES PROCESSED ===`);
            resolve();
        });
    });
}

// =================================================================================================
// EXECUTION
// =================================================================================================

const DRY_RUN = true;
fixAllShallowCopies(DRY_RUN);
