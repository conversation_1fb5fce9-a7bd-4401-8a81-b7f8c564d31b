// Paste this in console to start monitoring
let requests = [];
const originalXHR = window.XMLHttpRequest;

// Helper to recursively extract all IDs from an object
function extractAllIds(obj, ids = new Set()) {
    if (!obj || typeof obj !== 'object') return ids;
    if (Array.isArray(obj)) {
        obj.forEach(item => extractAllIds(item, ids));
        return ids;
    }
    for (const [key, value] of Object.entries(obj)) {
        if (key === 'id' && typeof value === 'number') ids.add(value);
        if (value && typeof value === 'object') extractAllIds(value, ids);
    }
    return ids;
}

// Function to fetch objects in batches
async function fetchObjectsBatch(ids, batchSize = 50) {
    const results = [];
    const errors = [];

    for (let i = 0; i < ids.length; i += batchSize) {
        const batchIds = ids.slice(i, i + batchSize);
        console.log(`Fetching batch ${i/batchSize + 1}/${Math.ceil(ids.length/batchSize)} (${batchIds.length} objects)`);

        try {
            await new Promise((resolve, reject) => {
                databaseConnection.obj.getById(
                    "",
                    batchIds,
                    function(response) {
                        if (response) {
                            const items = Array.isArray(response) ? response : [response];
                            results.push(...items.filter(x => x));
                            console.log(`Batch ${i/batchSize + 1} complete. Total: ${results.length}`);
                            resolve();
                        } else {
                            reject(new Error("No response"));
                        }
                    }
                );
            });
        } catch (err) {
            console.error(`Error fetching batch ${i/batchSize + 1}:`, err);
            errors.push(...batchIds);
        }

        await new Promise(resolve => setTimeout(resolve, 500));
    }

    return results;
}

// Function to save results
async function saveResults(results) {
    const blob = new Blob([JSON.stringify(results, null, 2)], {type: 'application/json'});
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = 'production_objects.json';
    a.click();
    URL.revokeObjectURL(a.href);
    console.log(`Downloaded ${results.length} objects to production_objects.json`);
}

// Override XHR to monitor requests
window.XMLHttpRequest = function() {
    const xhr = new originalXHR();
    const originalOpen = xhr.open;
    const originalSend = xhr.send;

    xhr.open = function() {
        this._url = arguments[1];
        originalOpen.apply(this, arguments);
    };

    xhr.send = function() {
        this.addEventListener('load', function() {
            try {
                const response = JSON.parse(this.responseText);
                const ids = extractAllIds(response);
                requests.push({url: this._url, ids: Array.from(ids), response});
            } catch (e) {}
        });
        originalSend.apply(this, arguments);
    };

    return xhr;
};

// Create finish button
const btn = document.createElement('button');
btn.textContent = 'Finish & Fetch Production';
btn.style.position = 'fixed';
btn.style.bottom = '20px';
btn.style.right = '20px';
btn.style.zIndex = '9999';
btn.style.padding = '10px';
btn.style.background = '#4CAF50';
btn.style.color = 'white';
btn.style.border = 'none';
btn.style.borderRadius = '4px';
btn.style.cursor = 'pointer';

// When clicked, process everything
btn.onclick = async () => {
    // Get unique IDs from all requests
    const allIds = new Set();
    requests.forEach(req => {
        if (req.response) {
            const ids = extractAllIds(req.response);
            ids.forEach(id => allIds.add(id));
        }
    });
    
    const idArray = Array.from(allIds).sort((a, b) => a - b);
    console.log(`Found ${idArray.length} unique IDs`);
    
    // Fetch and save
    const results = await fetchObjectsBatch(idArray);
    await saveResults(results);
    
    // Clean up
    btn.textContent = 'Done!';
    btn.style.background = '#666';
    btn.disabled = true;
};

document.body.appendChild(btn);
console.log('XHR Monitor started - click the green button when done');
