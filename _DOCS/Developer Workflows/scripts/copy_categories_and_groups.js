/**
 * Script to duplicate inventory billable categories and their groups
 * with modified surcharges
 *
 * @param {boolean} dryRun - If true, only logs changes without creating records
 * @param {number} limit - Limit processing to this many categories (for testing)
 * @param {number} startIndex - Skip this many categories before starting (default: 0)
 * @param {boolean} skipExisting - If true, skips categories that already have copies (default: true)
 */
function duplicateBillableCategories(dryRun = true, limit = 1, startIndex = 0, skipExisting = true) {
  console.log(`\n========== STARTING DUPLICATION PROCESS ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will create records)'}`);
  console.log(`Processing: ${limit} categories starting from index ${startIndex}`);
  console.log(`Skip existing: ${skipExisting ? 'YES (will skip categories with existing copies)' : 'NO (will create new copies regardless)'}`);
  console.log(`=================================================\n`);

  databaseConnection.obj.getAll('inventory_billable_categories', function(ib_cat) {
    console.log(`Found ${ib_cat.length} total billable categories in the database.`);

    // Filter out categories that have already been processed (contain "(*)" in name)
    const filteredCategories = ib_cat.filter(category => !category.name.includes('(*)'));
    console.log(`Found ${filteredCategories.length} original categories (${ib_cat.length - filteredCategories.length} are copies).`);

    // Apply start index and limit
    const categoriesToProcess = filteredCategories.slice(startIndex, startIndex + limit);

    if (categoriesToProcess.length === 0) {
      console.warn(`⚠️ WARNING: No categories to process with current settings. Check your startIndex (${startIndex}) and limit (${limit}).`);
      return;
    }

    console.log(`\nWill process ${categoriesToProcess.length} categories:`);
    categoriesToProcess.forEach((cat, idx) => {
      console.log(`  ${idx+1}. "${cat.name}" (ID: ${cat.id})`);
    });

    // Track statistics
    const stats = {
      categoriesProcessed: 0,
      categoriesSkipped: 0,
      categoriesCreated: 0,
      groupsProcessed: 0,
      groupsSkipped: 0,
      groupsCreated: 0
    };

    // Process categories sequentially to avoid overwhelming the server
    processNextCategory(categoriesToProcess, 0, dryRun, skipExisting, stats);
  });
}

/**
 * Process categories one at a time to avoid server connection issues
 */
function processNextCategory(categories, index, dryRun, skipExisting, stats) {
  if (index >= categories.length) {
    console.log(`\n========== DUPLICATION PROCESS COMPLETE ==========`);
    console.log(`Categories processed: ${stats.categoriesProcessed}`);
    console.log(`Categories skipped (already had copies): ${stats.categoriesSkipped}`);
    console.log(`Categories created: ${stats.categoriesCreated}`);
    console.log(`Groups processed: ${stats.groupsProcessed}`);
    console.log(`Groups skipped (already existed): ${stats.groupsSkipped}`);
    console.log(`Groups created: ${stats.groupsCreated}`);
    console.log(`=================================================\n`);
    return;
  }

  const ibc = categories[index];
  stats.categoriesProcessed++;

  console.log(`\n========== CATEGORY ${index+1}/${categories.length} ==========`);
  console.log(`Processing category: "${ibc.name}" (ID: ${ibc.id})`);

  // If skipExisting is true, check if this category already has a copy
  if (skipExisting) {
    const searchName = ibc.name + ' (*)';
    console.log(`Checking if category "${ibc.name}" already has copies...`);

    databaseConnection.obj.getWhere('inventory_billable_categories', {name: searchName}, function(existingCategories) {
      if (existingCategories && existingCategories.length > 0) {
        stats.categoriesSkipped++;
        console.warn(`⚠️ SKIPPING CATEGORY: "${ibc.name}" already has ${existingCategories.length} copies:`);

        // Log each existing copy
        existingCategories.forEach((cat, i) => {
          console.warn(`  Copy ${i+1}: "${cat.name}" (ID: ${cat.id})`);
        });

        // If we want to check for groups in the existing category
        if (existingCategories.length === 1) {
          console.log(`\nChecking for groups in the existing category copy...`);
          checkGroupsInExistingCategory(ibc.id, existingCategories[0].id, dryRun, skipExisting, stats, function() {
            // Continue with the next category
            processNextCategory(categories, index + 1, dryRun, skipExisting, stats);
          });
        } else {
          console.warn(`Multiple copies exist. Skipping group check to avoid confusion.`);
          // Continue with the next category
          processNextCategory(categories, index + 1, dryRun, skipExisting, stats);
        }
      } else {
        console.log(`No existing copies found for "${ibc.name}". Will create a new copy.`);
        // No copy exists, proceed with creation
        createCategory(ibc, dryRun, skipExisting, stats, function() {
          // Continue with the next category
          processNextCategory(categories, index + 1, dryRun, skipExisting, stats);
        });
      }
    });
  } else {
    console.log(`Skip existing check disabled. Will create a new copy regardless.`);
    // Skip checking, proceed with creation
    createCategory(ibc, dryRun, skipExisting, stats, function() {
      // Continue with the next category
      processNextCategory(categories, index + 1, dryRun, skipExisting, stats);
    });
  }
}

/**
 * Check for groups in an existing category copy
 */
function checkGroupsInExistingCategory(originalCategoryId, existingCopyCategoryId, dryRun, skipExisting, stats, callback) {
  // Get groups from original category
  databaseConnection.obj.getWhere('inventory_billable_groups', {category: originalCategoryId}, function(originalGroups) {
    console.log(`Found ${originalGroups.length} groups in the original category.`);
    stats.groupsProcessed += originalGroups.length;

    // Get groups from the existing copy category
    databaseConnection.obj.getWhere('inventory_billable_groups', {category: existingCopyCategoryId}, function(existingGroups) {
      console.log(`Found ${existingGroups.length} groups in the existing category copy.`);

      // Simple direct comparison - find originals that don't have copies
      const missingGroups = [];

      // For each original group, check if a corresponding copy exists
      originalGroups.forEach(originalGroup => {
        const expectedCopyName = originalGroup.name + ' (*)';
        const hasCopy = existingGroups.some(copyGroup => copyGroup.name === expectedCopyName);

        if (!hasCopy) {
          missingGroups.push(originalGroup);
        }
      });

      // Report and process missing groups
      if (missingGroups.length > 0) {
        console.log(`Found ${missingGroups.length} groups that need to be copied:`);
        missingGroups.forEach((group, i) => {
          console.log(`  ${i+1}. "${group.name}" (ID: ${group.id})`);
        });

        if (!dryRun) {
          console.log(`Creating these missing groups...`);
          processNextGroup(missingGroups, 0, existingCopyCategoryId, dryRun, stats, callback);
        } else {
          console.log(`[DRY RUN] Would create these ${missingGroups.length} missing groups.`);
          if (callback) callback();
        }
      } else {
        console.log(`All groups from the original category have copies in the existing category copy.`);
        if (callback) callback();
      }
    });
  });
}

/**
 * Create a new category based on an existing one
 */
function createCategory(ibc, dryRun, skipExisting, stats, callback) {
  var newIBC = _.clone(ibc);

  // Remove the id property as a new one will be assigned by the database
  delete newIBC.id;

  // Update name by appending "(*)""
  newIBC.name = ibc.name + ' (*)';

  // Remove surcharges with ID 1310026 if it exists
  if (newIBC.surcharges && Array.isArray(newIBC.surcharges)) {
    const originalLength = newIBC.surcharges.length;
    newIBC.surcharges = newIBC.surcharges.filter(id => id !== 1310026);
    console.log(`Modified surcharges: ${originalLength} → ${newIBC.surcharges.length} (Removed 1310026)`);
  } else {
    console.log(`No surcharges to modify`);
  }

  // Update date_created to current time
  newIBC.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new category: "${newIBC.name}"...`);
    databaseConnection.obj.create('inventory_billable_categories', newIBC, function(createdIBC) {
      stats.categoriesCreated++;
      console.log(`✅ CREATED: New category "${createdIBC.name}" (ID: ${createdIBC.id})`);

      // Now process the groups for this category
      processGroupsForCategory(ibc.id, createdIBC.id, dryRun, skipExisting, stats, callback);
    });
  } else {
    console.log(`[DRY RUN] Would create new category: "${newIBC.name}"`);
    // For dry run, still show what groups would be processed
    processGroupsForCategory(ibc.id, 'new-id-placeholder', dryRun, skipExisting, stats, callback);
  }
}

/**
 * Process all groups for a given category
 */
function processGroupsForCategory(originalCategoryId, newCategoryId, dryRun, skipExisting, stats, callback) {
  console.log(`\nProcessing groups for category ID ${originalCategoryId}...`);

  databaseConnection.obj.getWhere('inventory_billable_groups', {category: originalCategoryId}, function(ib_groups) {
    console.log(`Found ${ib_groups.length} groups in the original category.`);
    stats.groupsProcessed += ib_groups.length;

    // Process groups sequentially
    processNextGroup(ib_groups, 0, newCategoryId, dryRun, stats, callback);
  });
}

/**
 * Process groups one at a time to avoid server connection issues
 */
function processNextGroup(groups, index, newCategoryId, dryRun, stats, callback) {
  if (index >= groups.length) {
    console.log(`Completed processing all groups for this category.`);
    if (callback) callback();
    return;
  }

  const ibg = groups[index];
  const newGroupName = ibg.name + ' (*)';

  // Check if this group already exists in the new category (if not a dry run and not a placeholder ID)
  if (!dryRun && newCategoryId !== 'new-id-placeholder') {
    databaseConnection.obj.getWhere('inventory_billable_groups', {
      category: newCategoryId,
      name: newGroupName
    }, function(existingGroups) {
      if (existingGroups && existingGroups.length > 0) {
        stats.groupsSkipped++;
        console.log(`⚠️ SKIPPING GROUP: "${ibg.name}" already exists in the new category as "${newGroupName}" (ID: ${existingGroups[0].id})`);

        // Process the next group after a short delay
        setTimeout(function() {
          processNextGroup(groups, index + 1, newCategoryId, dryRun, stats, callback);
        }, 50);
      } else {
        // Group doesn't exist, create it
        createGroup(ibg, newCategoryId, dryRun, stats, function() {
          // Process the next group after a short delay
          setTimeout(function() {
            processNextGroup(groups, index + 1, newCategoryId, dryRun, stats, callback);
          }, 50);
        });
      }
    });
  } else {
    // For dry run or when using placeholder ID, just simulate creation
    createGroup(ibg, newCategoryId, dryRun, stats, function() {
      // Process the next group (no delay needed for dry run)
      processNextGroup(groups, index + 1, newCategoryId, dryRun, stats, callback);
    });
  }
}

/**
 * Create a new group based on an existing one
 */
function createGroup(ibg, newCategoryId, dryRun, stats, callback) {
  var newIBG = _.clone(ibg);

  // Remove the id property as a new one will be assigned by the database
  delete newIBG.id;

  // Update name by appending "(*)"
  newIBG.name = ibg.name + ' (*)';

  // Update category to point to the new category
  newIBG.category = newCategoryId;

  // Update date_created to current time
  newIBG.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new group: "${newIBG.name}"...`);
    databaseConnection.obj.create('inventory_billable_groups', newIBG, function(createdIBG) {
      stats.groupsCreated++;
      console.log(`✅ CREATED: New group "${createdIBG.name}" (ID: ${createdIBG.id})`);
      if (callback) callback();
    });
  } else {
    console.log(`[DRY RUN] Would create new group: "${newIBG.name}"`);
    if (callback) callback();
  }
}

/**
 * Create a new combination based on an existing one
 */
function createCombination(ibc, newCategoryId, dryRun, stats, callback) {
  var newIBC = _.clone(ibc);

  // Remove the id property as a new one will be assigned by the database
  delete newIBC.id;

  // Update name by appending "(*)"
  newIBC.name = ibc.name.trim() + ' (*)';

  // Update category to point to the new category
  newIBC.category = newCategoryId;

  // Update date_created to current time
  newIBC.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new combination: "${newIBC.name}"...`);
    databaseConnection.obj.create('inventory_billable_combinations', newIBC, function(createdIBC) {
      stats.combinationsCreated++;
      console.log(`✅ CREATED: New combination "${createdIBC.name}" (ID: ${createdIBC.id})`);
      if (callback) callback();
    });
  } else {
    console.log(`[DRY RUN] Would create new combination: "${newIBC.name}"`);
    if (callback) callback();
  }
}

/**
 * Script to duplicate inventory billable combination categories and their combinations
 * with modified surcharges
 *
 * @param {boolean} dryRun - If true, only logs changes without creating records
 * @param {number} limit - Limit processing to this many categories (for testing)
 * @param {number} startIndex - Skip this many categories before starting (default: 0)
 * @param {boolean} skipExisting - If true, skips categories that already have copies (default: true)
 */
function duplicateBillableCombinationCategories(dryRun = true, limit = 1, startIndex = 0, skipExisting = true) {
  console.log(`\n========== STARTING COMBINATION DUPLICATION PROCESS ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will create records)'}`);
  console.log(`Processing: ${limit} combination categories starting from index ${startIndex}`);
  console.log(`Skip existing: ${skipExisting ? 'YES (will skip categories with existing copies)' : 'NO (will create new copies regardless)'}`);
  console.log(`=================================================\n`);

  databaseConnection.obj.getAll('inventory_billable_combination_categories', function(ib_cat) {
    console.log(`Found ${ib_cat.length} total billable combination categories in the database.`);

    // Filter out categories that have already been processed (contain "(*)" in name)
    const filteredCategories = ib_cat.filter(category => !category.name.includes('(*)'));
    console.log(`Found ${filteredCategories.length} original combination categories (${ib_cat.length - filteredCategories.length} are copies).`);

    // Apply start index and limit
    const categoriesToProcess = filteredCategories.slice(startIndex, startIndex + limit);

    if (categoriesToProcess.length === 0) {
      console.warn(`⚠️ WARNING: No combination categories to process with current settings. Check your startIndex (${startIndex}) and limit (${limit}).`);
      return;
    }

    console.log(`\nWill process ${categoriesToProcess.length} combination categories:`);
    categoriesToProcess.forEach((cat, idx) => {
      console.log(`  ${idx+1}. "${cat.name}" (ID: ${cat.id})`);
    });

    // Track statistics
    const stats = {
      categoriesProcessed: 0,
      categoriesSkipped: 0,
      categoriesCreated: 0,
      combinationsProcessed: 0,
      combinationsSkipped: 0,
      combinationsCreated: 0
    };

    // Process categories sequentially to avoid overwhelming the server
    processNextCombinationCategory(categoriesToProcess, 0, dryRun, skipExisting, stats);
  });
}

/**
 * Process combination categories one at a time to avoid server connection issues
 */
function processNextCombinationCategory(categories, index, dryRun, skipExisting, stats) {
  if (index >= categories.length) {
    console.log(`\n========== COMBINATION DUPLICATION PROCESS COMPLETE ==========`);
    console.log(`Combination categories processed: ${stats.categoriesProcessed}`);
    console.log(`Combination categories skipped (already had copies): ${stats.categoriesSkipped}`);
    console.log(`Combination categories created: ${stats.categoriesCreated}`);
    console.log(`Combinations processed: ${stats.combinationsProcessed}`);
    console.log(`Combinations skipped (already existed): ${stats.combinationsSkipped}`);
    console.log(`Combinations created: ${stats.combinationsCreated}`);
    console.log(`=================================================\n`);
    return;
  }

  const ibc = categories[index];
  stats.categoriesProcessed++;

  console.log(`\n========== COMBINATION CATEGORY ${index+1}/${categories.length} ==========`);
  console.log(`Processing combination category: "${ibc.name}" (ID: ${ibc.id})`);

  // If skipExisting is true, check if this category already has a copy
  if (skipExisting) {
    const searchName = ibc.name.trim() + ' (*)';
    console.log(`Checking if combination category "${ibc.name}" already has copies...`);

    databaseConnection.obj.getWhere('inventory_billable_combination_categories', {name: searchName}, function(existingCategories) {
      if (existingCategories && existingCategories.length > 0) {
        stats.categoriesSkipped++;
        console.warn(`⚠️ SKIPPING COMBINATION CATEGORY: "${ibc.name}" already has ${existingCategories.length} copies:`);

        // Log each existing copy
        existingCategories.forEach((cat, i) => {
          console.warn(`  Copy ${i+1}: "${cat.name}" (ID: ${cat.id})`);
        });

        // If we want to check for combinations in the existing category
        if (existingCategories.length === 1) {
          console.log(`\nChecking for combinations in the existing category copy...`);
          checkCombinationsInExistingCategory(ibc.id, existingCategories[0].id, dryRun, skipExisting, stats, function() {
            // Continue with the next category
            processNextCombinationCategory(categories, index + 1, dryRun, skipExisting, stats);
          });
        } else {
          console.warn(`Multiple copies exist. Skipping combinations check to avoid confusion.`);
          // Continue with the next category
          processNextCombinationCategory(categories, index + 1, dryRun, skipExisting, stats);
        }
      } else {
        console.log(`No existing copies found for "${ibc.name}". Will create a new copy.`);
        // No copy exists, proceed with creation
        createCombinationCategory(ibc, dryRun, skipExisting, stats, function() {
          // Continue with the next category
          processNextCombinationCategory(categories, index + 1, dryRun, skipExisting, stats);
        });
      }
    });
  } else {
    console.log(`Skip existing check disabled. Will create a new copy regardless.`);
    // Skip checking, proceed with creation
    createCombinationCategory(ibc, dryRun, skipExisting, stats, function() {
      // Continue with the next category
      processNextCombinationCategory(categories, index + 1, dryRun, skipExisting, stats);
    });
  }
}

/**
 * Check for combinations in an existing category copy
 */
function checkCombinationsInExistingCategory(originalCategoryId, existingCopyCategoryId, dryRun, skipExisting, stats, callback) {
  // Get combinations from original category
  databaseConnection.obj.getWhere('inventory_billable_combinations', {category: originalCategoryId}, function(originalCombinations) {
    console.log(`Found ${originalCombinations.length} combinations in the original category.`);
    stats.combinationsProcessed += originalCombinations.length;

    // Get combinations from the existing copy category
    databaseConnection.obj.getWhere('inventory_billable_combinations', {category: existingCopyCategoryId}, function(existingCombinations) {
      console.log(`Found ${existingCombinations.length} combinations in the existing category copy.`);

      // Simple direct comparison - find originals that don't have copies
      const missingCombinations = [];

      // For each original combination, check if a corresponding copy exists
      originalCombinations.forEach(originalCombination => {
        const expectedCopyName = (originalCombination.name.trim() + ' (*)').trim();
        const hasCopy = existingCombinations.some(copyCombination =>
          copyCombination.name.trim() === expectedCopyName);

        if (!hasCopy) {
          missingCombinations.push(originalCombination);
        }
      });

      // Report and process missing combinations
      if (missingCombinations.length > 0) {
        console.log(`Found ${missingCombinations.length} combinations that need to be copied:`);
        missingCombinations.forEach((combination, i) => {
          console.log(`  ${i+1}. "${combination.name}" (ID: ${combination.id})`);
        });

        if (!dryRun) {
          console.log(`Creating these missing combinations...`);
          processNextCombination(missingCombinations, 0, existingCopyCategoryId, dryRun, stats, callback);
        } else {
          console.log(`[DRY RUN] Would create these ${missingCombinations.length} missing combinations.`);
          if (callback) callback();
        }
      } else {
        console.log(`All combinations from the original category have copies in the existing category copy.`);
        if (callback) callback();
      }
    });
  });
}

/**
 * Create a new combination category based on an existing one
 */
function createCombinationCategory(ibc, dryRun, skipExisting, stats, callback) {
  var newIBC = _.clone(ibc);

  // Remove the id property as a new one will be assigned by the database
  delete newIBC.id;

  // Update name by appending "(*)""
  newIBC.name = ibc.name.trim() + ' (*)';

  // Remove surcharges with ID 1310026 if it exists
  if (newIBC.surcharges && Array.isArray(newIBC.surcharges)) {
    const originalLength = newIBC.surcharges.length;
    newIBC.surcharges = newIBC.surcharges.filter(id => id !== 1310026);
    console.log(`Modified surcharges: ${originalLength} → ${newIBC.surcharges.length} (Removed 1310026)`);
  } else {
    console.log(`No surcharges to modify`);
  }

  // Update date_created to current time
  newIBC.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new combination category: "${newIBC.name}"...`);
    databaseConnection.obj.create('inventory_billable_combination_categories', newIBC, function(createdIBC) {
      stats.categoriesCreated++;
      console.log(`✅ CREATED: New combination category "${createdIBC.name}" (ID: ${createdIBC.id})`);

      // Now process the combinations for this category
      processCombinationsForCategory(ibc.id, createdIBC.id, dryRun, skipExisting, stats, callback);
    });
  } else {
    console.log(`[DRY RUN] Would create new combination category: "${newIBC.name}"`);
    // For dry run, still show what combinations would be processed
    processCombinationsForCategory(ibc.id, 'new-id-placeholder', dryRun, skipExisting, stats, callback);
  }
}

/**
 * Process all combinations for a given category
 */
function processCombinationsForCategory(originalCategoryId, newCategoryId, dryRun, skipExisting, stats, callback) {
  console.log(`\nProcessing combinations for category ID ${originalCategoryId}...`);

  databaseConnection.obj.getWhere('inventory_billable_combinations', {category: originalCategoryId}, function(ib_combinations) {
    console.log(`Found ${ib_combinations.length} combinations in the original category.`);
    stats.combinationsProcessed += ib_combinations.length;

    // Process combinations sequentially
    processNextCombination(ib_combinations, 0, newCategoryId, dryRun, stats, callback);
  });
}

/**
 * Process combinations one at a time to avoid server connection issues
 */
function processNextCombination(combinations, index, newCategoryId, dryRun, stats, callback) {
  if (index >= combinations.length) {
    console.log(`Completed processing all combinations for this category.`);
    if (callback) callback();
    return;
  }

  const ibc = combinations[index];
  const newCombinationName = ibc.name.trim() + ' (*)';

  // Check if this combination already exists in the new category (if not a dry run and not a placeholder ID)
  if (!dryRun && newCategoryId !== 'new-id-placeholder') {
    databaseConnection.obj.getWhere('inventory_billable_combinations', {
      category: newCategoryId,
      name: newCombinationName
    }, function(existingCombinations) {
      if (existingCombinations && existingCombinations.length > 0) {
        stats.combinationsSkipped++;
        console.log(`⚠️ SKIPPING COMBINATION: "${ibc.name}" already exists in the new category as "${newCombinationName}" (ID: ${existingCombinations[0].id})`);

        // Process the next combination after a short delay
        setTimeout(function() {
          processNextCombination(combinations, index + 1, newCategoryId, dryRun, stats, callback);
        }, 50);
      } else {
        // Combination doesn't exist, create it
        createCombination(ibc, newCategoryId, dryRun, stats, function() {
          // Process the next combination after a short delay
          setTimeout(function() {
            processNextCombination(combinations, index + 1, newCategoryId, dryRun, stats, callback);
          }, 50);
        });
      }
    });
  } else {
    // For dry run or when using placeholder ID, just simulate creation
    createCombination(ibc, newCategoryId, dryRun, stats, function() {
      // Process the next combination (no delay needed for dry run)
      processNextCombination(combinations, index + 1, newCategoryId, dryRun, stats, callback);
    });
  }
}

/**
 * Create a new combination based on an existing one
 */
function createCombination(ibc, newCategoryId, dryRun, stats, callback) {
  var newIBC = _.clone(ibc);

  // Remove the id property as a new one will be assigned by the database
  delete newIBC.id;

  // Update name by appending "(*)"
  newIBC.name = ibc.name.trim() + ' (*)';

  // Update category to point to the new category
  newIBC.category = newCategoryId;

  // Update date_created to current time
  newIBC.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new combination: "${newIBC.name}"...`);
    databaseConnection.obj.create('inventory_billable_combinations', newIBC, function(createdIBC) {
      stats.combinationsCreated++;
      console.log(`✅ CREATED: New combination "${createdIBC.name}" (ID: ${createdIBC.id})`);
      if (callback) callback();
    });
  } else {
    console.log(`[DRY RUN] Would create new combination: "${newIBC.name}"`);
    if (callback) callback();
  }
}
