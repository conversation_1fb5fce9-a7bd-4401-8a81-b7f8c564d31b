const RECORD_IDS_TO_DUPLICATE = [
    // Add your record IDs here
  8277779,
  8278061,
  8277129,
  8278187,
  8277268
];

function duplicateRecordsById(dryRun = true) {
  console.log(`Starting duplication...`);
  processRecordsSequentially(0, dryRun);
}

function processRecordsSequentially(index, dryRun) {
  if (index >= RECORD_IDS_TO_DUPLICATE.length) {
    console.log(`All records processed!`);
    return;
  }

  const recordId = RECORD_IDS_TO_DUPLICATE[index];
  console.log(`\n--- Processing record ${index + 1}/${RECORD_IDS_TO_DUPLICATE.length} ---`);

  databaseConnection.obj.getById('', recordId, function(record) {
    if (record) {
      console.log(`Found: "${record.name}" (ID: ${record.id}, Type: ${record.object_bp_type})`);

      // Duplicate the main record
      const newRecord = _.clone(record);
      delete newRecord.id;
      newRecord.name = record.name.replace('2023', '2025*');
      newRecord.data_source_id = record.id;

      if (!dryRun) {
        databaseConnection.obj.create(record.object_bp_type, newRecord, function(created) {
          console.log(`Created: "${created.name}" (ID: ${created.id})`);

          // Find related records
          const relatedType = record.object_bp_type === 'inventory_billable_categories'
            ? 'inventory_billable_groups'
            : 'inventory_billable_combinations';

          databaseConnection.obj.getWhere(relatedType, {category: record.id}, function(related) {
            console.log(`Found ${related.length} related records`);

            // Process related records one at a time with delays
            processRelatedRecordsSequentially(related, 0, created.id, relatedType, function() {
              // After all related records are done, move to next main record
              setTimeout(function() {
                processRecordsSequentially(index + 1, dryRun);
              }, 500); // 500ms delay between main records
            });
          });
        });
      } else {
        console.log(`[DRY RUN] Would create: "${newRecord.name}"`);

        // For dry run, also simulate finding related records
        const relatedType = record.object_bp_type === 'inventory_billable_categories'
          ? 'inventory_billable_groups'
          : 'inventory_billable_combinations';

        databaseConnection.obj.getWhere(relatedType, {category: record.id}, function(related) {
          console.log(`[DRY RUN] Found ${related.length} related records`);

          related.forEach(relatedRecord => {
            console.log(`  [DRY RUN] Would create related: "${relatedRecord.name}*"`);
          });

          // Move to next record after short delay
          setTimeout(function() {
            processRecordsSequentially(index + 1, dryRun);
          }, 100);
        });
      }
    } else {
      console.log(`Not found: ID ${recordId}`);
      // Move to next record
      setTimeout(function() {
        processRecordsSequentially(index + 1, dryRun);
      }, 100);
    }
  });
}

function processRelatedRecordsSequentially(relatedRecords, index, newCategoryId, relatedType, callback) {
  if (index >= relatedRecords.length) {
    console.log(`Completed all related records`);
    if (callback) callback();
    return;
  }

  const relatedRecord = relatedRecords[index];
  console.log(`  Creating related ${index + 1}/${relatedRecords.length}: "${relatedRecord.name}"`);

  const newRelated = _.clone(relatedRecord);
  delete newRelated.id;
  newRelated.name = relatedRecord.name + '*';
  newRelated.category = newCategoryId;
  newRelated.data_source_id = relatedRecord.id;

  databaseConnection.obj.create(relatedType, newRelated, function(createdRelated) {
    console.log(`    ✅ Created related: "${createdRelated.name}" (ID: ${createdRelated.id})`);

    // Wait before processing next related record
    setTimeout(function() {
      processRelatedRecordsSequentially(relatedRecords, index + 1, newCategoryId, relatedType, callback);
    }, 200); // 200ms delay between related records
  });
}
duplicateRecordsById(true);
