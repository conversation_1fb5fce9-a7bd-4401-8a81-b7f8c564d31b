// Function to fix vendors with missing product data
function fixVendorsWithMissingProducts(vendors) {
  console.log(`Processing ${vendors.length} vendors...`);

  // 1. Find all 0% Fee vendors
  const zeroFeeVendors = vendors.filter(vendor =>
    vendor.name && vendor.name.includes("(0% Fee)"));

  console.log(`Found ${zeroFeeVendors.length} vendors with (0% Fee) suffix`);

  // 2. Process each 0% Fee vendor
  const updates = zeroFeeVendors.map(zeroFeeVendor => {
    // Skip vendors that already have product data
    if (zeroFeeVendor.default_product &&
        zeroFeeVendor.products &&
        Array.isArray(zeroFeeVendor.products) &&
        zeroFeeVendor.products.length > 0) {
      console.log(`⏩ Skipping vendor "${zeroFeeVendor.name}" (ID: ${zeroFeeVendor.id}) - already has product data`);
      return null;
    }

    // 3. Find the original vendor (same name without the suffix)
    const originalName = zeroFeeVendor.name.replace(" (0% Fee)", "").trim();
    const originalVendor = vendors.find(vendor =>
      vendor.name === originalName &&
      vendor.default_product &&
      vendor.id !== zeroFeeVendor.id);

    if (!originalVendor) {
      console.warn(`⚠️ Could not find original vendor for "${zeroFeeVendor.name}" (ID: ${zeroFeeVendor.id})`);
      return null;
    }

    console.log(`Found match: "${zeroFeeVendor.name}" (ID: ${zeroFeeVendor.id}) -> "${originalVendor.name}" (ID: ${originalVendor.id})`);

    // Debug: Log the original vendor's products
    console.log(`Original vendor products:`, originalVendor.products);

    // Extract the default_product ID
    let defaultProductId = null;
    if (originalVendor.default_product) {
      // Handle both cases: when default_product is an object or when it's already an ID
      defaultProductId = typeof originalVendor.default_product === 'object' ?
                         originalVendor.default_product.id :
                         originalVendor.default_product;
    }

    // Extract product IDs
    let productIds = [];
    if (originalVendor.products && Array.isArray(originalVendor.products)) {
      productIds = originalVendor.products.map(product => {
        // Handle both cases: when product is an object or when it's already an ID
        return typeof product === 'object' ? product.id : product;
      });
    }

    // Debug: Log the extracted product IDs
    console.log(`Extracted product IDs:`, productIds);

    // 4. Create update object with products from original vendor
    const updateObj = {
      id: zeroFeeVendor.id,
      default_product: defaultProductId,
      products: productIds
    };

    // Debug: Log the final update object
    console.log(`Update object:`, updateObj);

    return updateObj;
  }).filter(update => update !== null);

  console.log(`Prepared ${updates.length} vendor updates`);

  // 5. Perform updates with delay between each
  let updateIndex = 0;
  const failedUpdates = [];

  function processNextUpdate() {
    if (updateIndex >= updates.length) {
      console.log(`Updates complete. ${updates.length - failedUpdates.length} successful, ${failedUpdates.length} failed.`);

      if (failedUpdates.length > 0) {
        console.log("Failed updates:", failedUpdates);
      }

      return;
    }

    const update = updates[updateIndex++];
    console.log(`Updating vendor ID ${update.id} with default_product: ${update.default_product}, products: [${update.products.join(', ')}]`);

    // Debug: Log the exact object being sent to the database
    console.log(`Sending to database:`, JSON.stringify(update));

    databaseConnection.obj.update(
      'companies',
      update,
      function(response) {
        if (response) {
          console.log(`✅ Updated vendor ID ${update.id}:`, response);
          // Debug: Check if products were saved in the response
          console.log(`Response products:`, response.products);
        } else {
          console.error(`❌ Failed to update vendor ID ${update.id}`);
          failedUpdates.push(update);
        }

        // Process next update after delay
        setTimeout(processNextUpdate, 100);
      }
    );
  }

  // Start processing updates
  processNextUpdate();

  return updates;
}

// Function to check which vendors still need updates
function checkRemainingVendorsToFix() {
  console.log("Checking for vendors that still need fixing...");

  databaseConnection.obj.getWhere(
    'companies',
    {
      is_vendor: 1
    },
    function(response) {
      const zeroFeeVendors = response.filter(vendor =>
        vendor.name && vendor.name.includes("(0% Fee)"));

      const vendorsNeedingFix = zeroFeeVendors.filter(vendor =>
        !vendor.default_product ||
        !vendor.products ||
        !Array.isArray(vendor.products) ||
        vendor.products.length === 0);

      console.log(`Found ${vendorsNeedingFix.length} out of ${zeroFeeVendors.length} (0% Fee) vendors still needing fixes`);

      if (vendorsNeedingFix.length > 0) {
        console.log("Vendors still needing fixes:", vendorsNeedingFix);
        window.vendorsToFix = response; // Store for easy access
        console.log("To fix these vendors, run: fixVendorsWithMissingProducts(window.vendorsToFix)");
      } else {
        console.log("All vendors appear to be fixed!");
      }
    }
  );
}

// Execute the function with the vendors from your query
// Assuming the vendors are stored in a variable called 'vendorsList'
const updates = fixVendorsWithMissingProducts(vendorsList);

// After updates complete, you can run:
// checkRemainingVendorsToFix();

// Function to diagnose vendor product data issues
function diagnoseVendorProducts() {
  console.log("Fetching all vendors...");

  databaseConnection.obj.getWhere(
    'companies',
    {
      is_vendor: 1
    },
    function(vendors) {
      console.log(`Found ${vendors.length} total vendors`);

      // Separate into original and 0% Fee vendors
      const originalVendors = vendors.filter(v => v.name && !v.name.includes("(0% Fee)"));
      const zeroFeeVendors = vendors.filter(v => v.name && v.name.includes("(0% Fee)"));

      console.log(`Found ${originalVendors.length} original vendors`);
      console.log(`Found ${zeroFeeVendors.length} 0% Fee vendors`);

      // Detailed analysis of each 0% Fee vendor
      console.log("\n=== DETAILED ANALYSIS OF 0% FEE VENDORS ===");

      const needsUpdate = [];

      zeroFeeVendors.forEach(zeroFeeVendor => {
        const originalName = zeroFeeVendor.name.replace(" (0% Fee)", "").trim();
        const originalVendor = originalVendors.find(v => v.name === originalName);

        if (!originalVendor) {
          console.log(`\n❌ "${zeroFeeVendor.name}" (ID: ${zeroFeeVendor.id}) - NO MATCHING ORIGINAL VENDOR FOUND`);
          return;
        }

        console.log(`\n🔍 ANALYZING: "${zeroFeeVendor.name}" (ID: ${zeroFeeVendor.id})`);
        console.log(`   ORIGINAL: "${originalVendor.name}" (ID: ${originalVendor.id})`);

        // Check default_product
        const zeroFeeDefaultProduct = zeroFeeVendor.default_product ?
          (typeof zeroFeeVendor.default_product === 'object' ?
            zeroFeeVendor.default_product.id : zeroFeeVendor.default_product) : null;

        const originalDefaultProduct = originalVendor.default_product ?
          (typeof originalVendor.default_product === 'object' ?
            originalVendor.default_product.id : originalVendor.default_product) : null;

        console.log(`   DEFAULT PRODUCT:`);
        console.log(`     Original: ${originalDefaultProduct}`);
        console.log(`     0% Fee: ${zeroFeeDefaultProduct}`);
        console.log(`     Match: ${zeroFeeDefaultProduct === originalDefaultProduct ? '✅ YES' : '❌ NO'}`);

        // Check products array
        const zeroFeeProducts = zeroFeeVendor.products && Array.isArray(zeroFeeVendor.products) ?
          zeroFeeVendor.products.map(p => typeof p === 'object' ? p.id : p) : [];

        const originalProducts = originalVendor.products && Array.isArray(originalVendor.products) ?
          originalVendor.products.map(p => typeof p === 'object' ? p.id : p) : [];

        // Check if arrays have the same elements (order doesn't matter)
        const productsMatch =
          zeroFeeProducts.length === originalProducts.length &&
          zeroFeeProducts.every(id => originalProducts.includes(id));

        console.log(`   PRODUCTS ARRAY:`);
        console.log(`     Original: [${originalProducts.join(', ')}]`);
        console.log(`     0% Fee: [${zeroFeeProducts.join(', ')}]`);
        console.log(`     Match: ${productsMatch ? '✅ YES' : '❌ NO'}`);
        console.log(`     Original Length: ${originalProducts.length}`);
        console.log(`     0% Fee Length: ${zeroFeeProducts.length}`);

        // Check if update is needed
        if (zeroFeeDefaultProduct !== originalDefaultProduct || !productsMatch) {
          console.log(`   ⚠️ NEEDS UPDATE`);

          needsUpdate.push({
            id: zeroFeeVendor.id,
            name: zeroFeeVendor.name,
            default_product: originalDefaultProduct,
            products: originalProducts
          });
        } else {
          console.log(`   ✅ DATA MATCHES - NO UPDATE NEEDED`);
        }
      });

      console.log(`\n=== SUMMARY ===`);
      console.log(`${needsUpdate.length} vendors need updates`);

      // Store for easy access
      window.vendorsNeedingUpdate = needsUpdate;

      if (needsUpdate.length > 0) {
        console.log("To update these vendors, run: updateVendorProducts()");
      }
    }
  );
}

// Function to update vendor products
function updateVendorProducts() {
  if (!window.vendorsNeedingUpdate || window.vendorsNeedingUpdate.length === 0) {
    console.error("No vendors need updating. Run diagnoseVendorProducts() first.");
    return;
  }

  const updates = window.vendorsNeedingUpdate;
  console.log(`Preparing to update ${updates.length} vendors...`);

  // Process updates sequentially
  let updateIndex = 0;
  const failedUpdates = [];

  function processNextUpdate() {
    if (updateIndex >= updates.length) {
      console.log(`Updates complete. ${updates.length - failedUpdates.length} successful, ${failedUpdates.length} failed.`);

      if (failedUpdates.length > 0) {
        console.log("Failed updates:", failedUpdates);
      }

      return;
    }

    const update = updates[updateIndex++];
    console.log(`\nUpdating vendor "${update.name}" (ID: ${update.id})`);
    console.log(`  default_product: ${update.default_product}`);
    console.log(`  products: [${update.products.join(', ')}]`);

    // Create a clean update object
    const updateObj = {
      id: update.id,
      default_product: update.default_product,
      products: update.products
    };

    console.log(`Sending to database:`, JSON.stringify(updateObj));

    databaseConnection.obj.update(
      'companies',
      updateObj,
      function(response) {
        if (response) {
          console.log(`✅ Updated vendor ID ${update.id}`);
          console.log(`  Response:`, response);
          console.log(`  Response products:`, response.products);

          // Verify the update was successful
          if (!response.products || response.products.length === 0) {
            console.error(`❌ Products array is empty in response!`);
          }
        } else {
          console.error(`❌ Failed to update vendor ID ${update.id}`);
          failedUpdates.push(update);
        }

        // Process next update after delay
        setTimeout(processNextUpdate, 500);
      }
    );
  }

  // Start processing updates
  processNextUpdate();
}

// Run the diagnostic
diagnoseVendorProducts();

// After diagnosis completes, you can run:
// updateVendorProducts();
