// Function to fetch objects in batches
async function fetchObjectsBatch(ids, batchSize = 50) {
    const results = [];
    const errors = [];

    // Process in batches
    for (let i = 0; i < ids.length; i += batchSize) {
        const batchIds = ids.slice(i, i + batchSize);
        console.log(
            `Fetching batch ${i / batchSize + 1}/${Math.ceil(
                ids.length / batchSize
            )} (${batchIds.length} objects)`
        );

        try {
            // Use promise to handle async
            await new Promise((resolve, reject) => {
                databaseConnection.obj.getById(
                    "",
                    batchIds,
                    function (response) {
                        if (response) {
                            const items = Array.isArray(response)
                                ? response
                                : [response];
                            results.push(...items.filter((x) => x));

                            // Log progress
                            console.log(
                                `Batch ${
                                    i / batchSize + 1
                                } complete. Total objects: ${results.length}`
                            );
                            resolve();
                        } else {
                            reject(new Error("No response"));
                        }
                    }
                );
            });
        } catch (err) {
            console.error(`Error fetching batch ${i / batchSize + 1}:`, err);
            errors.push(...batchIds);
        }

        // Small delay between batches
        await new Promise((resolve) => setTimeout(resolve, 500));
    }

    return results;
}

// Function to save results
function saveResults(results) {
    // Group objects by type for logging
    const byType = {};
    results.forEach((obj) => {
        const type = obj.object_bp_type || "unknown";
        if (!byType[type]) {
            byType[type] = [];
        }
        byType[type].push(obj);
    });

    // Save directly to file using server API - just the array
    sb.data.db.service('FileSystem', 'writeFile', {
        path: '/Users/<USER>/Infinity/bento/_DOCS/Developer Workflows/data/production_objects.json',
        content: JSON.stringify(results, null, 2)
    }, function(response) {
        if (response && response.success) {
            // Log detailed summary
            console.log('\nFetch Complete!');
            console.log('Total objects:', results.length);
            console.log('\nBy Type:');
            Object.entries(byType).forEach(([type, items]) => {
                console.log(`${type}: ${items.length} objects`);
            });
            console.log('\nSaved to: /Users/<USER>/Infinity/bento/_DOCS/Developer Workflows/data/production_objects.json');
        } else {
            console.error('Error saving file:', response);
        }
    });
}

// Main function to orchestrate the process
async function fetchProductionObjects(ids) {
    console.log(`Starting fetch for ${ids.length} objects...`);

    const results = await fetchObjectsBatch(ids);
    saveResults(results);
    return results;
}

// Usage instructions
console.log("To fetch objects:");
console.log("1. Load your IDs array");
console.log("2. Call: fetchProductionObjects(ids)");
