<?xml version="1.0" encoding="UTF-8"?>
<BentoPlatform>
  <CoreConcepts>
    <Concept>
      <Name>Factory and Module Registration</Name>
      <Description>
        The Factory is the central registry for all Bento modules. Components register themselves with the Factory to become available in the system.
      </Description>
      <Example>
        <![CDATA[
          Factory.register('componentName', function (sb) {
            // Module implementation
            
            return {
              // Public API
              init: function() {
                // Initialize the module
              }
            };
          });
        ]]>
      </Example>
      <Notes>
        <Note>Every module receives a sandbox (sb) parameter for controlled access to the core system</Note>
        <Note>Modules must return an object with at least an init() method</Note>
        <Note>Factory.register is the entry point for all components in the system</Note>
      </Notes>
    </Concept>
    
    <Concept>
      <Name>Sandbox (sb)</Name>
      <Description>
        The sandbox is a mediator that provides controlled access to the core system. It prevents modules from directly accessing other modules or system internals.
      </Description>
      <CommonMethods>
        <Method>
          <Name>sb.notify</Name>
          <Description>Publishes an event to the system's message bus</Description>
          <Signature>sb.notify({ type: 'event-name', data: {...} })</Signature>
          <Example>
            <![CDATA[
              sb.notify({
                type: 'register-application',
                data: { 
                  navigationItem: { 
                    id: 'componentId',
                    title: 'Component Title'
                    // Other properties
                  }
                }
              });
            ]]>
          </Example>
        </Method>
        <Method>
          <Name>sb.listen</Name>
          <Description>Subscribes to system events</Description>
          <Signature>sb.listen({ 'event-name': callbackFunction })</Signature>
          <Example>
            <![CDATA[
              var listeners = {
                'custom-event': this.handleCustomEvent,
                'another-event': this.handleAnotherEvent
              };
              
              sb.listen(listeners);
            ]]>
          </Example>
        </Method>
        <Method>
          <Name>sb.data</Name>
          <Description>Access to data operations and utilities</Description>
          <SubMethods>
            <SubMethod>
              <Name>sb.data.db.obj</Name>
              <Description>Database object operations</Description>
              <Operations>
                <Operation>getById(type, id, callback)</Operation>
                <Operation>getWhere(type, conditions, callback, fields)</Operation>
                <Operation>getAll(type, callback, fields)</Operation>
                <Operation>create(type, data, callback)</Operation>
                <Operation>update(type, id, data, callback)</Operation>
              </Operations>
            </SubMethod>
            <SubMethod>
              <Name>sb.data.url</Name>
              <Description>URL creation utilities</Description>
              <Operations>
                <Operation>createPageURL(type, params)</Operation>
              </Operations>
            </SubMethod>
            <SubMethod>
              <Name>sb.data.makePDF</Name>
              <Description>Generate PDFs from HTML</Description>
              <Signature>sb.data.makePDF(htmlContent, mode)</Signature>
              <Modes>
                <Mode>I: Display inline</Mode>
                <Mode>D: Download</Mode>
                <Mode>F: Save to server file</Mode>
                <Mode>S: Return as string</Mode>
              </Modes>
            </SubMethod>
          </SubMethods>
        </Method>
      </CommonMethods>
    </Concept>
    
    <Concept>
      <Name>DOM Tools</Name>
      <Description>
        Bento provides a DOM manipulation abstraction layer for creating and managing UI elements.
      </Description>
      <Example>
        <![CDATA[
          // Create a container
          ui.makeNode('container', 'div', {
            css: 'ui segment',
            text: 'Some content'
          });
          
          // Add a child element
          ui.container.makeNode('title', 'div', {
            css: 'ui header',
            tag: 'h2',
            text: 'Title Text'
          });
          
          // Apply changes
          ui.patch();
        ]]>
      </Example>
      <CommonOperations>
        <Operation>
          <Name>makeNode(id, type, options)</Name>
          <Description>Creates a new DOM node</Description>
          <Parameters>
            <Parameter>id: String identifier for the node</Parameter>
            <Parameter>type: Type of node ('div', 'lineBreak', custom types)</Parameter>
            <Parameter>options: Object with properties (css, tag, text, style, etc.)</Parameter>
          </Parameters>
        </Operation>
        <Operation>
          <Name>empty()</Name>
          <Description>Clears all content from a node</Description>
        </Operation>
        <Operation>
          <Name>patch()</Name>
          <Description>Applies all pending DOM changes</Description>
        </Operation>
      </CommonOperations>
      <SpecialNodeTypes>
        <NodeType>div: Standard container</NodeType>
        <NodeType>lineBreak: Creates spacing</NodeType>
        <NodeType>Custom factory types (defined in _factory/domTools)</NodeType>
      </SpecialNodeTypes>
    </Concept>
    
    <Concept>
      <Name>Event System</Name>
      <Description>
        Bento uses a publish/subscribe event system for communication between modules.
      </Description>
      <EventFlow>
        <Step>1. Modules register event listeners using sb.listen()</Step>
        <Step>2. Events are published using sb.notify()</Step>
        <Step>3. Event data is passed to registered handlers</Step>
      </EventFlow>
      <CommonEvents>
        <Event>
          <Name>register-application</Name>
          <Description>Registers a component in the navigation system</Description>
        </Event>
        <Event>
          <Name>register-report</Name>
          <Description>Registers a report in the reporting system</Description>
        </Event>
        <Event>
          <Name>show-collection</Name>
          <Description>Displays a collection of objects in a view</Description>
        </Event>
        <Event>
          <Name>click</Name>
          <Description>Handles click interactions on UI elements</Description>
        </Event>
      </CommonEvents>
    </Concept>
    
    <Concept>
      <Name>Component Structure</Name>
      <Description>
        Standard structure for Bento components with initialization and event handling.
      </Description>
      <Template>
        <![CDATA[
          Factory.register('componentName', function (sb) {
            
            // Private variables and functions
            var privateData = {};
            
            function privateFunction() {
              // Implementation
            }
            
            // View functions
            function renderView(ui, state, draw) {
              // UI rendering logic using DOM tools
              ui.makeNode(...);
              ui.patch();
            }
            
            // Data handling functions
            function loadData(callback) {
              sb.data.db.obj.getWhere(..., function(results) {
                // Process data
                callback(results);
              });
            }
            
            // Event handlers
            function handleEvent(data) {
              // Handle event data
            }
            
            // Public API
            return {
              // Event handlers exposed to the system
              handleExternalEvent: function(data) {
                // Implementation
              },
              
              // Initialization function (required)
              init: function() {
                // Register event listeners
                var listeners = {
                  'event-name': this.handleExternalEvent
                };
                
                sb.listen(listeners);
                
                // Register with navigation system
                sb.notify({
                  type: 'register-application',
                  data: {
                    navigationItem: {
                      // Configuration
                    }
                  }
                });
              }
            };
          });
        ]]>
      </Template>
    </Concept>
  </CoreConcepts>
  
  <CommonPatterns>
    <Pattern>
      <Name>Data Loading</Name>
      <Description>Standard pattern for loading data in Bento components</Description>
      <Example>
        <![CDATA[
          function loadData(state, onComplete) {
            // Show loading indicator
            loader(ui, 'Loading data...');
            
            // Get primary data
            sb.data.db.obj.getById('objectType', state.id, function(result) {
              
              // Update loading message
              loader(ui, 'Loading related data...');
              
              // Get related data
              sb.data.db.obj.getWhere('relatedType', { 
                parent: result.id 
              }, function(relatedData) {
                
                // Process data
                var processedData = {
                  main: result,
                  related: relatedData
                };
                
                // Pass processed data to callback
                onComplete(processedData);
              });
            });
          }
        ]]>
      </Example>
    </Pattern>
    
    <Pattern>
      <Name>View Rendering</Name>
      <Description>Pattern for rendering UI components</Description>
      <Example>
        <![CDATA[
          function renderView(ui, data) {
            // Clear existing content
            ui.empty();
            
            // Create container
            ui.makeNode('container', 'div', {
              css: 'ui segment'
            });
            
            // Add header
            ui.container.makeNode('header', 'div', {
              css: 'ui header',
              tag: 'h2',
              text: data.title
            });
            
            // Add content sections
            ui.container.makeNode('content', 'div', {
              css: 'ui basic segment'
            });
            
            // Render list items
            _.each(data.items, function(item) {
              ui.container.content.makeNode('item' + item.id, 'div', {
                css: 'ui item',
                text: item.name
              });
            });
            
            // Apply all DOM changes
            ui.patch();
          }
        ]]>
      </Example>
    </Pattern>
    
    <Pattern>
      <Name>Component Registration</Name>
      <Description>Registering a component with the navigation system</Description>
      <Example>
        <![CDATA[
          sb.notify({
            type: 'register-application',
            data: {
              navigationItem: {
                id: 'componentId',
                title: 'Component Title',
                icon: '<i class="fa fa-icon"></i>',
                views: [
                  {
                    id: 'mainView',
                    default: true,
                    type: 'custom',
                    title: 'Main View',
                    icon: '<i class="fa fa-view-icon"></i>',
                    dom: renderMainView
                  },
                  {
                    id: 'detailView',
                    type: 'object-view',
                    title: 'Detail View',
                    icon: 'detail',
                    dom: renderDetailView
                  }
                ]
              }
            }
          });
        ]]>
      </Example>
    </Pattern>
    
    <Pattern>
      <Name>Event Handling</Name>
      <Description>Standard pattern for handling events in Bento</Description>
      <Example>
        <![CDATA[
          // Register event listeners
          var listeners = {
            'custom-event': handleCustomEvent,
            'object-updated': handleObjectUpdated
          };
          
          sb.listen(listeners);
          
          // Event handler implementation
          function handleCustomEvent(data) {
            // Process event data
            var processedResult = processData(data);
            
            // Update UI or trigger another action
            updateUI(processedResult);
            
            // Optionally fire another event
            sb.notify({
              type: 'action-completed',
              data: {
                result: processedResult
              }
            });
          }
        ]]>
      </Example>
    </Pattern>
  </CommonPatterns>
  
  <CoreComponents>
    <Component>
      <Name>Notify System</Name>
      <Description>
        The central messaging system that enables communication between Bento components.
        It implements a publish/subscribe pattern.
      </Description>
      <Location>_SRC/notify/_factory/_core.js</Location>
      <Usage>
        <PublishEvent>
          <![CDATA[
            sb.notify({
              type: 'event-name',
              data: { /* Event data */ }
            });
          ]]>
        </PublishEvent>
        <SubscribeToEvent>
          <![CDATA[
            sb.listen({
              'event-name': eventHandler
            });
          ]]>
        </SubscribeToEvent>
      </Usage>
    </Component>
    
    <Component>
      <Name>Run Function</Name>
      <Description>
        A common pattern in Bento modules where a run function is used to execute an action
        or operation provided in an event. This pattern allows for dynamic behavior injection.
      </Description>
      <Example>
        <![CDATA[
          // In the module's public API
          run: function(data) {
            // Execute the function provided in the data
            if (data && typeof data.run === 'function') {
              data.run(data);
            }
          },
          
          // In the initialization
          init: function() {
            var listeners = {
              'component-run': this.run
            };
            
            sb.listen(listeners);
          }
          
          // Usage from another component
          ui.button.notify('click', {
            type: 'component-run',
            data: {
              run: function() {
                // Action to perform when clicked
                performAction();
              }
            }
          }, sb.moduleId);
        ]]>
      </Example>
    </Component>
    
    <Component>
      <Name>Bento DOM Tools</Name>
      <Description>
        A set of utilities for manipulating the DOM in a structured way.
        These tools provide an abstraction over direct DOM manipulation.
      </Description>
      <Location>_SRC/notify/_factory/domTools/</Location>
      <CoreFunctions>
        <Function>
          <Name>makeNode</Name>
          <Description>Creates a new DOM node with specified properties</Description>
          <Signature>parent.makeNode(id, type, options)</Signature>
          <Example>
            <![CDATA[
              ui.makeNode('container', 'div', {
                css: 'ui segment',
                text: 'Content goes here'
              });
            ]]>
          </Example>
        </Function>
        <Function>
          <Name>empty</Name>
          <Description>Removes all child nodes from an element</Description>
          <Example>ui.container.empty();</Example>
        </Function>
        <Function>
          <Name>patch</Name>
          <Description>Applies all pending DOM changes</Description>
          <Example>ui.patch();</Example>
        </Function>
      </CoreFunctions>
      <SpecialNodeTypes>
        <NodeType>
          <Name>div</Name>
          <Description>Standard container element</Description>
        </NodeType>
        <NodeType>
          <Name>lineBreak</Name>
          <Description>Creates vertical spacing</Description>
          <Example>ui.makeNode('br', 'lineBreak', { spaces: 2 });</Example>
        </NodeType>
        <NodeType>
          <Name>Custom factory types</Name>
          <Description>Additional node types defined in domTools directory</Description>
          <Examples>
            <Example>bsColumns: Bootstrap column layout</Example>
            <Example>bsTables: Bootstrap table</Example>
            <Example>charts: Data visualization</Example>
          </Examples>
        </NodeType>
      </SpecialNodeTypes>
    </Component>
    
    <Component>
      <Name>Database Access</Name>
      <Description>
        Utilities for accessing and manipulating data in the Bento system.
      </Description>
      <Location>_SRC/notify/_extensions/_database.js</Location>
      <CommonOperations>
        <Operation>
          <Name>getById</Name>
          <Description>Retrieves a single object by ID</Description>
          <Signature>sb.data.db.obj.getById(type, id, callback, fields)</Signature>
          <Example>
            <![CDATA[
              sb.data.db.obj.getById('companies', 123, function(company) {
                // Process company data
              }, {
                name: true,
                contact_info: {
                  email: true,
                  phone: true
                }
              });
            ]]>
          </Example>
        </Operation>
        <Operation>
          <Name>getWhere</Name>
          <Description>Retrieves objects matching specified conditions</Description>
          <Signature>sb.data.db.obj.getWhere(type, conditions, callback, fields)</Signature>
          <Example>
            <![CDATA[
              sb.data.db.obj.getWhere('invoices', {
                status: 'unpaid',
                due_date: {
                  lt: moment().format('YYYY-MM-DD')
                }
              }, function(overdue) {
                // Process overdue invoices
              });
            ]]>
          </Example>
        </Operation>
        <Operation>
          <Name>create</Name>
          <Description>Creates a new object</Description>
          <Signature>sb.data.db.obj.create(type, data, callback)</Signature>
        </Operation>
        <Operation>
          <Name>update</Name>
          <Description>Updates an existing object</Description>
          <Signature>sb.data.db.obj.update(type, id, data, callback)</Signature>
        </Operation>
      </CommonOperations>
    </Component>
  </CoreComponents>
  
  <BestPractices>
    <Practice>
      <Name>Module Structure</Name>
      <Description>Follow the standard module structure for consistency</Description>
      <Guidelines>
        <Guideline>Start with private variables and functions</Guideline>
        <Guideline>Group related functions (UI, data, events)</Guideline>
        <Guideline>Return public API with init function</Guideline>
        <Guideline>Register event listeners in init</Guideline>
      </Guidelines>
    </Practice>
    
    <Practice>
      <Name>Error Handling</Name>
      <Description>Implement proper error handling in async operations</Description>
      <Example>
        <![CDATA[
          sb.data.db.obj.getById('type', id, function(result) {
            if (!result) {
              // Handle missing data
              showError('Object not found');
              return;
            }
            
            // Process result
          });
        ]]>
      </Example>
    </Practice>
    
    <Practice>
      <Name>UI Updates</Name>
      <Description>Batch DOM updates and use patch() efficiently</Description>
      <Guidelines>
        <Guideline>Call ui.empty() before rebuilding a view</Guideline>
        <Guideline>Build the complete DOM structure before calling patch()</Guideline>
        <Guideline>Use meaningful node IDs for easier debugging</Guideline>
      </Guidelines>
    </Practice>
    
    <Practice>
      <Name>Performance</Name>
      <Description>Optimize for performance in large data operations</Description>
      <Guidelines>
        <Guideline>Request only the fields you need in database queries</Guideline>
        <Guideline>Use _.debounce for functions triggered by frequent events</Guideline>
        <Guideline>Cache results when appropriate</Guideline>
        <Guideline>Use pagination for large datasets</Guideline>
      </Guidelines>
    </Practice>
  </BestPractices>
</BentoPlatform>