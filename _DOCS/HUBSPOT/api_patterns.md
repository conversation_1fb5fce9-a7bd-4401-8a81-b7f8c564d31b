# Bento API Patterns for HubSpot Integration

## Overview

This document outlines the specific API patterns extracted from the existing Salesforce integration (`fg_salesforce.php`) that must be followed for the HubSpot webhook integration.

## BentoAPI Class Usage

### Initialization Pattern
```php
// FOR INFINITY INSTANCE - Values to be discovered in Task T1
$apiKey = 'infinity';
$apiToken = '[INFINITY_API_TOKEN]';  // Discovered from Task T1
$bento = new BentoAPI($apiKey, $apiToken);
```

### ⚠️ Reference Pattern (Foundation Group - DO NOT USE)
```php
// REFERENCE ONLY - Foundation Group pattern from fg_salesforce.php
$apiKey = 'foundation_group';
$apiToken = '8bff041c171a0b7d5f53f9ad26f26ce4b3c0adf32378c575a5e7a77311e26747a3acda5ae816a0708ae3472460117044fcaf2eca8943ed0b1ce99326ee282747';
$bento = new BentoAPI($apiKey, $apiToken);
```

## Search Patterns

### Search by External ID (Primary Pattern)
```php
// Search company by HubSpot Company ID
$organization = $bento->getWhere([
    'objectType' => 'companies',
    'queryObj' => [
        'data_source_hash' => $hubspot_company_id,
        'data_source' => crc32($hubspot_company_id)
    ]
])[0];
```

### Fallback Search by Name
```php
// If not found by ID, search by name
if (empty($organization)) {
    $organization = $bento->getWhere([
        'objectType' => 'companies',
        'queryObj' => [
            'name' => $company_name
        ]
    ])[0];
}
```

### Search Contacts within Company
```php
$contact_list = $bento->getWhere([
    'objectType' => 'contacts',
    'queryObj' => [
        'company' => $organization['id']
    ]
]);

// Find specific contact by name using Underscore.php
$contact = [];
if (!empty($contact_list)) {
    foreach($contact_list as $key => $val) {
        if ($val['name'] == $contact_name) {
            $contact = $contact_list[$key];
            break;
        }
    }
}
```

### Search Users (for assignment)
```php
// Search for salesperson
$salesperson = $bento->getWhere([
    'objectType' => 'users',
    'queryObj' => [
        'name' => $salesperson_name
    ]
]);

// Search for CSM
$csm = $bento->getWhere([
    'objectType' => 'users',
    'queryObj' => [
        'name' => $csm_name
    ]
]);
```

## Creation Patterns

### Company Creation
```php
// FOR INFINITY INSTANCE - Values to be discovered in Task T2
$organization_setup = [
    'objectType' => 'companies',
    'objectData' => [
        'type' => [INFINITY_COMPANY_TYPE_ID],     // Discovered from Task T2
        'name' => $company_name,
        'parent' => [INFINITY_HQ_ID],             // Discovered from Task T2
        'tagged_with' => [INFINITY_HQ_ID, INFINITY_TEAM_ID], // Discovered from Task T2
        'value' => intval(floatval($revenue) * 100), // Revenue in cents
        'manager' => $csm['0']['id'],
        'data_source_hash' => $hubspot_company_id,
        'data_source' => crc32($hubspot_company_id)
    ]
];

$org = $bento->create($organization_setup);
```

### ⚠️ Reference Pattern (Foundation Group - DO NOT USE)
```php
// REFERENCE ONLY - Foundation Group pattern from fg_salesforce.php
$organization_setup = [
    'objectType' => 'companies',
    'objectData' => [
        'type' => 1652262,        // Non-Profit Contact Type
        'name' => $company_name,
        'parent' => 1636580,      // FG HQ
        'tagged_with' => [1636580, 1763496], // FG HQ, All Company Team
        'value' => intval(floatval($revenue) * 100),
        'manager' => $csm['0']['id'],
        'data_source_hash' => $hubspot_company_id,
        'data_source' => crc32($hubspot_company_id)
    ]
];
```

### Contact Creation
```php
// FOR INFINITY INSTANCE - Values to be discovered in Task T2
$contact_setup = [
    'objectType' => 'contacts',
    'objectData' => [
        'type' => [INFINITY_CONTACT_TYPE_ID], // Discovered from Task T2
        'company' => $organization['id'],
        'parent' => $organization['id'],
        'sales_person' => $salesperson['0']['id'],
        'manager' => $csm['0']['id'],
        'tagged_with' => [
            [INFINITY_HQ_ID],     // Discovered from Task T2
            [INFINITY_TEAM_ID],   // Discovered from Task T2
            $organization['id'],  // Company
            $salesperson['0']['id'], // Salesperson
            $csm['0']['id']       // CSM
        ],
        'data_source_hash' => $hubspot_contact_id,
        'data_source' => crc32($hubspot_contact_id)
    ]
];

// Parse name if contains space
if (strpos($full_name, " ") !== false) {
    $name_parts = explode(" ", $full_name);
    $contact_setup['objectData']['name'] = $full_name;
    $contact_setup['objectData']['fname'] = $name_parts[0];
    $contact_setup['objectData']['lname'] = $name_parts[1];
}

$contact = $bento->create($contact_setup);
```

### ⚠️ Reference Pattern (Foundation Group - DO NOT USE)
```php
// REFERENCE ONLY - Foundation Group pattern from fg_salesforce.php
$contact_setup = [
    'objectType' => 'contacts',
    'objectData' => [
        'type' => 1652260,       // Contact type
        'company' => $organization['id'],
        'parent' => $organization['id'],
        'sales_person' => $salesperson['0']['id'],
        'manager' => $csm['0']['id'],
        'tagged_with' => [
            1636580,              // HQ
            1763496,              // All Company Team
            $organization['id'],  // Company
            $salesperson['0']['id'], // Salesperson
            $csm['0']['id']       // CSM
        ],
        'data_source_hash' => $hubspot_contact_id,
        'data_source' => crc32($hubspot_contact_id)
    ]
];
```

### Contact Info Creation (Email/Phone)
```php
$contactInfoObjs = [];

// Email contact info - FOR INFINITY INSTANCE
if (!empty($email)) {
    array_push($contactInfoObjs, [
        'objectType' => 'contact_info',
        'objectData' => [
            'object_id' => $contact['id'],
            'object_type' => 'contact_info',
            'name' => 'Email Address',
            'title' => 'Email Address',
            'info' => $email,
            'type' => [INFINITY_EMAIL_TYPE_ID],    // Discovered from Task T2
            'is_primary' => 'yes'
        ]
    ]);
}

// Phone contact info - FOR INFINITY INSTANCE
if (!empty($phone)) {
    array_push($contactInfoObjs, [
        'objectType' => 'contact_info',
        'objectData' => [
            'object_id' => $contact['id'],
            'object_type' => 'contact_info',
            'name' => 'Phone Number',
            'title' => 'Phone Number',
            'info' => $phone,
            'type' => [INFINITY_PHONE_TYPE_ID],    // Discovered from Task T2
            'is_primary' => 'yes'
        ]
    ]);
}

// Create contact info objects and link to contact
if (!empty($contactInfoObjs)) {
    $contactInfoIds = [];
    foreach($contactInfoObjs as $info) {
        $contactInfoIds[] = $bento->create($info)['id'];
    }

    // Update contact with contact info IDs
    $bento->update([
        'objectType' => 'contacts',
        'objectData' => [
            'id' => $contact['id'],
            'contact_info' => $contactInfoIds
        ]
    ]);
}
```

## Update Patterns

### Standard Update Pattern
```php
$bento->update([
    'objectType' => 'contacts',
    'objectData' => [
        'id' => $contact['id'],
        'field_name' => $new_value,
        'another_field' => $another_value
    ]
]);
```

## Blueprint Usage

### Getting Object Blueprints
```php
$core_blueprint = $bento->getBlueprint('#ml7laG'); // Core Demographic blueprint
$bp_options = $core_blueprint['_3']['options']['options'];

// Find option using Underscore.php
$accountType = __::find($bp_options, function($option) use($search_value) {
    return $option['name'] == $search_value && !$option['is_archived'];
});
```

## Logging & Notes Creation

### System Note Creation
```php
$noteBody = '<h5><strong>New HubSpot Submission:</strong></h5>';
$noteBody .= '<h5>Contact ID: ' . $hubspot_contact_id . '</h5>';
$noteBody .= '<h5>Email: ' . $email . '</h5>';
$noteBody .= '<h5>Timestamp: ' . date('d F Y, h:i:s A') . '</h5>';

$note = [
    'record_type' => 'log',
    'record_type_name' => 'System Log',
    'activity_type' => 'Create',
    'type_id' => $contact['id'],
    'tagged_with' => [1636580, 1763496, $organization['id'], $contact['id']],
    'note' => $noteBody,
    'author' => 0
];

$system_note = $bento->create([
    'objectType' => 'notes',
    'objectData' => $note
]);
```

## External Logging Pattern

### Pipedream Logging
```php
function httpPost($url, $data) {
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($curl);
    curl_close($curl);
    return $response;
}

// Log to Pipedream for development
$payload = [
    "data" => $response_data
];
httpPost("https://eoskrd6yth6uopj.m.pipedream.net", $payload);
```

## Error Handling Patterns

### Output Buffering for Logging
```php
ob_start();
echo "HubSpot processing start :><br>";
echo "<pre>HubSpot response « \n ", var_dump(json_encode($data, JSON_PRETTY_PRINT)), "</pre>\n";
echo "<: HubSpot processing end <br>";
$hubspotResp = ob_get_clean();
ob_end_flush();
```

### Response Codes
```php
// Success response
http_response_code(200);

// Error responses (use appropriate codes)
http_response_code(400); // Bad Request
http_response_code(401); // Unauthorized
http_response_code(500); // Internal Server Error
```

## Value Transformation Patterns

### Currency to Cents
```php
// Convert dollar amounts to cents (Bento stores as integers)
$value_in_cents = intval(floatval(strval($dollar_amount)) * 100);
```

### Date Formatting
```php
// Standard date format for Bento
$formatted_date = date('Y-m-d', strtotime($input_date));
```

### Address Object Pattern
```php
$address = [
    'add2' => '',
    'city' => $city,
    'country' => 'US',
    'street' => $street_address,
    'zip' => $zip_code,
    'state' => $state
];
```

## Functional Programming Patterns with Underscore.php

### Array Operations
```php
// Find items in arrays
$found_item = __::find($array, function($item) use($search_criteria) {
    return $item['property'] == $search_criteria && !$item['is_archived'];
});

// Filter arrays
$active_items = __::filter($items, function($item) {
    return !$item['is_archived'];
});

// Map transformations
$transformed = __::map($items, function($item) {
    return $item['name'];
});

// Group by property
$grouped = __::groupBy($items, function($item) {
    return $item['category'];
});
```

### Safe Property Access
```php
// Check if property exists before accessing
if (__::has($object, 'property_name')) {
    $value = $object['property_name'];
}

// Get values from objects
$names = __::pluck($objects, 'name');
```

## Data Source Hash Patterns

### External System Integration
```php
// For HubSpot Contact ID
$data_source_hash = $hubspot_contact_id;           // Store original ID
$data_source = crc32($hubspot_contact_id);         // Store CRC32 hash

// For HubSpot Company ID
$company_data_source_hash = $hubspot_company_id;
$company_data_source = crc32($hubspot_company_id);
```

### Search by Data Source
```php
// Primary search by data source
$existing_contact = $bento->getWhere([
    'objectType' => 'contacts',
    'queryObj' => [
        'data_source_hash' => $hubspot_contact_id,
        'data_source' => crc32($hubspot_contact_id)
    ]
])[0];
```

## Custom Object Type Patterns

### Object Type Reference
- Standard objects use string names: `'companies'`, `'contacts'`, `'users'`, `'notes'`
- Custom objects use hash IDs: `'#30N93o'`, `'#ml7laG'`, `'#3KelnN'`

### Tagged With Strategy
All objects should be tagged with:
1. `[INFINITY_HQ_ID]` - Infinity HQ (always) - **Discovered from Task T2**
2. `[INFINITY_TEAM_ID]` - Infinity Team (always) - **Discovered from Task T2**
3. Related object IDs (company, contact, user IDs as appropriate)

### ⚠️ Reference Values (Foundation Group - DO NOT USE)
- `1636580` - FG HQ (Foundation Group only)
- `1763496` - All Company Team (Foundation Group only)

This ensures proper relationship tracking and data visibility within the Bento system.
