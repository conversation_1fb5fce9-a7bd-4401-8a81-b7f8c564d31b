# HubSpot Webhook Integration - Implementation Guide

## 🚀 Quick Start

### File Location
```
/Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinity_hubspot_webhook.php
```

### Development URL
```
http://localhost:8080/custom_scripts/infinity_hubspot_webhook.php
```

### Production URLs
- **Development**: `https://bento-dev.infinityhospitality.net/custom_scripts/infinity_hubspot_webhook.php`
- **Production**: `https://bento.infinityhospitality.net/custom_scripts/infinity_hubspot_webhook.php`

## 🔧 Configuration To Be Discovered

### Infinity Bento API
- **Instance**: `infinity`
- **Token**: `[TO BE DISCOVERED - Task T1]`

### Object Type IDs (Infinity Specific)
```php
// TO BE DISCOVERED - Task T2
'companies' => [COMPANY_TYPE_ID],
'contacts' => [CONTACT_TYPE_ID],
'contact_info_email' => [EMAIL_TYPE_ID],
'contact_info_phone' => [PHONE_TYPE_ID],
'notes' => [NOTES_TYPE_ID]
```

### Tagged With Values
```php
// TO BE DISCOVERED - Task T2
'tagged_with' => [INFINITY_HQ_ID, INFINITY_TEAM_ID]
```

### ⚠️ Multi-Tenant Gotchas
- **Never hardcode Foundation Group values** (`foundation_group`, `1636580`, `1763496`)
- **Instance-specific discovery required** - Each tenant has unique IDs
- **Shared blueprints, unique data** - Same structure, different values

## 📋 HubSpot Webhook Flow

### Current Setup (Development)
```
HubSpot → Pipedream → Infinity Bento
          ↓
    https://eowsqc6detxmwqb.m.pipedream.net
          ↓
    localhost:8080/custom_scripts/infinity_hubspot_webhook.php
```

### Production Setup (Future)
```
HubSpot → Direct → Infinity Bento
          ↓
    https://bento.infinityhospitality.net/custom_scripts/infinity_hubspot_webhook.php
```

## 🔍 Payload Format Support

### Pipedream Passthrough Format
```json
{
  "event": {
    "body": {
      "dealname": "Contact Name",
      "email": "<EMAIL>",
      "firstname": "First",
      "lastname": "Last",
      "hubspot_owner_id": "79618815",
      "lead_source": "Source Type"
    }
  },
  "headers": {
    "x-hubspot-correlation-id": "unique-id"
  }
}
```

### Direct HubSpot Contact Webhook Format
```json
{
  "objectId": "12345",
  "properties": {
    "firstname": "First",
    "lastname": "Last",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "company": "Company Name",
    "associatedcompanyid": "67890"
  }
}
```

## 🛠️ Testing & Development

### Local Testing (Recommended)
1. **Start gulp watch**: Navigate to project root and run gulp watch process
2. **Test endpoint**: `curl -X POST http://localhost:8080/custom_scripts/infinity_hubspot_webhook.php -d '{"test":"data"}'`
3. **Live reload**: File changes automatically update via gulp process

### Debugging Features
- **Verbose logging**: All processing steps logged to Pipedream
- **Output buffering**: Complete request/response capture
- **Error notifications**: MailSpon integration for critical errors
- **System notes**: Audit trail in Bento with full payload details

### Pipedream Logging
Development logs sent to: `https://eoskrd6yth6uopj.m.pipedream.net`

### MailSpon Error Alerts
Critical errors sent to: `<EMAIL>`

## 📊 Data Processing Flow

### Contact Synchronization Steps

1. **Extract Contact Data**
   - Detect webhook format (Pipedream vs Direct)
   - Extract contact fields (name, email, phone, company)
   - Generate unique contact identifier

2. **Company Resolution**
   - Search by HubSpot Company ID (if available)
   - Fallback to company name matching
   - Create new company if not found

3. **Contact Deduplication**
   - Search by HubSpot Contact ID (`data_source_hash`)
   - Search by email within company scope
   - Search by name within company scope
   - Create new contact if not found

4. **Contact Info Management**
   - Create separate `contact_info` objects for email/phone
   - Link via `contact_info` array in contact update
   - Set primary flags appropriately

5. **System Logging**
   - Create detailed system note with all HubSpot data
   - Tag with relevant entities for tracking
   - Include timestamp and processing details

### Data Source Tracking Pattern
```php
$data_source_hash = $hubspot_contact_id;     // String
$data_source = crc32($hubspot_contact_id);   // Integer hash
```

## 🔐 Security Implementation

### CORS Headers (Required)
```php
header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
```

### Error Reporting (Production)
```php
error_reporting(E_ERROR | E_PARSE);
```

### Input Sanitization
- JSON payload validation
- Data type checking
- SQL injection prevention via BentoAPI
- XSS prevention in note content

## 🚨 Error Handling

### Exception Management
- Try/catch blocks around all major operations
- Detailed error logging with context
- Graceful degradation on partial failures
- HTTP status code responses (200/400/500)

### Monitoring & Alerts
- **Development**: Pipedream webhook logs
- **Critical Errors**: MailSpon email notifications
- **System Notes**: Bento audit trail
- **HTTP Responses**: JSON status with details

## 📈 Performance Considerations

### Optimization Features
- Single file implementation (no complex imports)
- Efficient API calls (minimal redundant requests)
- Contact deduplication to prevent duplicates
- Batch contact info creation

### Scalability Notes
- **Rate Limiting**: Consider implementing if high volume
- **Queue Processing**: Current implementation is synchronous
- **Database Transactions**: BentoAPI handles consistency
- **Caching**: No caching implemented (stateless design)

## 🔄 Deployment Process

### Development Workflow
1. **Edit Source**: `/Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinity_hubspot_webhook.php`
2. **Test Locally**: `localhost:8080` with gulp watch
3. **Deploy**: File changes automatically sync via build process

### Production Deployment
1. **Staging Test**: Deploy to `bento-dev.infinityhospitality.net`
2. **HubSpot Configuration**: Update webhook URL in HubSpot
3. **Production Deploy**: Push to `bento.infinityhospitality.net`

## ✅ Production Readiness Checklist

### Pre-Deployment
- [ ] File saved to correct location: `infinity_hubspot_webhook.php`
- [ ] Infinity API token verified and working
- [ ] Object type IDs confirmed for Infinity instance
- [ ] CORS headers properly configured
- [ ] Error handling comprehensive and tested
- [ ] Pipedream logging functional
- [ ] MailSpon error notifications working

### Test Command
```bash
curl -X POST http://localhost:8080/custom_scripts/infinity_hubspot_webhook.php \
  -H "Content-Type: application/json" \
  -d '{"event":{"body":{"email":"<EMAIL>","firstname":"Test","lastname":"User"}}}'
```
