# HubSpot Webhook Security Guidelines

## Overview

This document outlines security requirements and best practices for the HubSpot webhook integration, based on existing patterns and industry standards.

## HubSpot Webhook Verification

### Required Headers
HubSpot sends these security headers with each webhook:
- `X-HubSpot-Signature` - SHA256 HMAC signature
- `X-HubSpot-Signature-Version` - Signature version (currently v1)
- `X-HubSpot-Request-Timestamp` - Request timestamp for replay protection

### Signature Verification Implementation
```php
function verifyHubSpotSignature($payload, $signature, $timestamp, $clientSecret) {
    // 1. Verify timestamp to prevent replay attacks (within 5 minutes)
    $currentTime = time();
    if (abs($currentTime - intval($timestamp)) > 300) {
        return false;
    }
    
    // 2. Create source string for signature
    $sourceString = 'POST' . 'https://yourdomain.com/webhook/hubspot' . $payload . $timestamp;
    
    // 3. Generate expected signature
    $expectedSignature = 'sha256=' . hash_hmac('sha256', $sourceString, $clientSecret);
    
    // 4. Compare signatures securely
    return hash_equals($expectedSignature, $signature);
}

// Usage in webhook endpoint
$payload = file_get_contents("php://input");
$signature = $_SERVER['HTTP_X_HUBSPOT_SIGNATURE'] ?? '';
$timestamp = $_SERVER['HTTP_X_HUBSPOT_REQUEST_TIMESTAMP'] ?? '';
$clientSecret = 'your_hubspot_client_secret'; // Store securely

if (!verifyHubSpotSignature($payload, $signature, $timestamp, $clientSecret)) {
    http_response_code(401);
    exit('Unauthorized');
}
```

## Standard Security Headers (REQUIRED)

### CORS Configuration
```php
// Standard CORS headers from existing pattern
header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
```

### Content Type Validation
```php
// Ensure proper content type
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
if (strpos($contentType, 'application/json') === false) {
    http_response_code(400);
    exit('Invalid content type');
}
```

## Input Validation & Sanitization

### JSON Payload Validation
```php
function validateHubSpotPayload($data) {
    $required_fields = ['objectId', 'properties'];
    
    foreach ($required_fields as $field) {
        if (!isset($data[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    // Validate objectId is numeric
    if (!is_numeric($data['objectId'])) {
        throw new Exception("Invalid objectId format");
    }
    
    // Validate properties structure
    if (!is_array($data['properties'])) {
        throw new Exception("Invalid properties format");
    }
    
    return true;
}
```

### Data Sanitization
```php
function sanitizeContactData($data) {
    return [
        'objectId' => filter_var($data['objectId'], FILTER_SANITIZE_NUMBER_INT),
        'firstName' => htmlspecialchars(trim($data['properties']['firstname']['value'] ?? '')),
        'lastName' => htmlspecialchars(trim($data['properties']['lastname']['value'] ?? '')),
        'email' => filter_var($data['properties']['email']['value'] ?? '', FILTER_SANITIZE_EMAIL),
        'phone' => preg_replace('/[^0-9\-\(\)\+\s]/', '', $data['properties']['phone']['value'] ?? '')
    ];
}
```

## Rate Limiting

### Simple Rate Limiting Implementation
```php
function checkRateLimit($identifier, $maxRequests = 100, $timeWindow = 3600) {
    $cacheFile = "/tmp/rate_limit_{$identifier}";
    $currentTime = time();
    
    if (file_exists($cacheFile)) {
        $data = json_decode(file_get_contents($cacheFile), true);
        
        // Reset if time window expired
        if ($currentTime - $data['timestamp'] > $timeWindow) {
            $data = ['count' => 0, 'timestamp' => $currentTime];
        }
        
        // Check if limit exceeded
        if ($data['count'] >= $maxRequests) {
            return false;
        }
        
        $data['count']++;
    } else {
        $data = ['count' => 1, 'timestamp' => $currentTime];
    }
    
    file_put_contents($cacheFile, json_encode($data));
    return true;
}

// Usage
$clientIP = $_SERVER['REMOTE_ADDR'];
if (!checkRateLimit($clientIP)) {
    http_response_code(429);
    exit('Rate limit exceeded');
}
```

## Error Handling Security

### Secure Error Responses
```php
function secureErrorResponse($message, $statusCode = 500) {
    http_response_code($statusCode);
    
    // Log detailed error internally
    error_log("HubSpot Webhook Error: {$message}");
    
    // Return generic error to client
    $response = [
        'error' => 'Internal server error',
        'timestamp' => date('c')
    ];
    
    // Only include detailed message in development
    if (getenv('ENVIRONMENT') === 'development') {
        $response['details'] = $message;
    }
    
    echo json_encode($response);
    exit;
}
```

### Error Logging Pattern
```php
function logSecurityEvent($event, $data = []) {
    $logData = [
        'timestamp' => date('c'),
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'data' => $data
    ];
    
    // Log to file
    error_log(json_encode($logData), 3, '/var/log/hubspot_security.log');
    
    // Log to Pipedream for monitoring
    httpPost("https://eoskrd6yth6uopj.m.pipedream.net", [
        'type' => 'security_event',
        'data' => $logData
    ]);
}
```

## Environment Configuration

### Secure Configuration Management
```php
// Environment-based configuration
function getConfig($key) {
    $config = [
        'development' => [
            'hubspot_client_secret' => getenv('HUBSPOT_CLIENT_SECRET_DEV'),
            'debug_mode' => true,
            'pipedream_url' => 'https://eoskrd6yth6uopj.m.pipedream.net'
        ],
        'production' => [
            'hubspot_client_secret' => getenv('HUBSPOT_CLIENT_SECRET_PROD'),
            'debug_mode' => false,
            'pipedream_url' => null // Disable in production
        ]
    ];
    
    $env = getenv('ENVIRONMENT') ?: 'development';
    return $config[$env][$key] ?? null;
}
```

## IP Whitelist (Optional)

### HubSpot IP Range Validation
```php
function validateHubSpotIP($clientIP) {
    // HubSpot webhook IP ranges (update as needed)
    $hubspotRanges = [
        '***********/24',
        '***********/24',
        '************/24'
    ];
    
    foreach ($hubspotRanges as $range) {
        if (ipInRange($clientIP, $range)) {
            return true;
        }
    }
    
    return false;
}

function ipInRange($ip, $range) {
    list($subnet, $bits) = explode('/', $range);
    $ip = ip2long($ip);
    $subnet = ip2long($subnet);
    $mask = -1 << (32 - $bits);
    $subnet &= $mask;
    return ($ip & $mask) == $subnet;
}
```

## Secure Data Storage

### Sensitive Data Handling
```php
// Never log sensitive data
function sanitizeForLogging($data) {
    $sanitized = $data;
    
    // Remove or mask sensitive fields
    if (isset($sanitized['properties']['email'])) {
        $email = $sanitized['properties']['email']['value'];
        $sanitized['properties']['email']['value'] = maskEmail($email);
    }
    
    if (isset($sanitized['properties']['phone'])) {
        $phone = $sanitized['properties']['phone']['value'];
        $sanitized['properties']['phone']['value'] = maskPhone($phone);
    }
    
    return $sanitized;
}

function maskEmail($email) {
    $parts = explode('@', $email);
    if (count($parts) != 2) return $email;
    
    $username = $parts[0];
    $domain = $parts[1];
    
    $maskedUsername = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
    return $maskedUsername . '@' . $domain;
}

function maskPhone($phone) {
    $cleaned = preg_replace('/[^0-9]/', '', $phone);
    if (strlen($cleaned) >= 4) {
        return str_repeat('*', strlen($cleaned) - 4) . substr($cleaned, -4);
    }
    return str_repeat('*', strlen($cleaned));
}
```

## SSL/TLS Requirements

### HTTPS Enforcement
```php
function enforceHTTPS() {
    if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on') {
        http_response_code(400);
        exit('HTTPS required');
    }
}

// Call at the beginning of the webhook endpoint
enforceHTTPS();
```

## Monitoring & Alerting

### Security Metrics Tracking
```php
function trackSecurityMetrics($event, $success = true) {
    $metrics = [
        'timestamp' => time(),
        'event' => $event,
        'success' => $success,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'endpoint' => $_SERVER['REQUEST_URI']
    ];
    
    // Store metrics for analysis
    file_put_contents('/var/log/webhook_metrics.json', json_encode($metrics) . "\n", FILE_APPEND);
}

// Usage examples
trackSecurityMetrics('signature_verification', true);
trackSecurityMetrics('rate_limit_check', true);
trackSecurityMetrics('invalid_payload', false);
```

### Alert Thresholds
```php
function checkSecurityAlerts() {
    $alertThresholds = [
        'failed_signatures_per_hour' => 10,
        'rate_limit_hits_per_hour' => 50,
        'invalid_payloads_per_hour' => 20
    ];
    
    // Implement alerting logic based on metrics
    // Send to monitoring system or email alerts
}
```

## Security Checklist

### Pre-Deployment Security Review
- [ ] HubSpot signature verification implemented
- [ ] Request timestamp validation (replay protection)
- [ ] Input validation and sanitization
- [ ] Rate limiting configured
- [ ] HTTPS enforcement enabled
- [ ] Error handling doesn't expose sensitive data
- [ ] Logging excludes sensitive information
- [ ] Environment variables for secrets
- [ ] IP whitelist configured (if required)
- [ ] Security monitoring enabled

### Runtime Security Monitoring
- [ ] Failed authentication attempts
- [ ] Rate limit violations
- [ ] Invalid payload submissions
- [ ] Unusual traffic patterns
- [ ] Error rate monitoring
- [ ] Response time monitoring

This security framework ensures that the HubSpot webhook integration follows security best practices while maintaining compatibility with your existing system architecture.
