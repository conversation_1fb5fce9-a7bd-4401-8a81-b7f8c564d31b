***

### objs->getDataFromMap ($map = null, $contextObjId = null)

This method can be used to pull together data points related to the context object into key-value pairs matching the keys submitted in the **$map** argument..

**Method Definition**

```

$map = [
    'contactFName' =>   'main_contact.fname',
    'companyName' =>    'main_contact.company.name',
    'managers' =>       'managers'
];

$mappedData = $objs->getDataFromMap(
    $map, 
    $projectId
);

// $mappedData = [
//    'contactFName' =>   'Larry',
//    'companyName' =>    'Cool Company,
//    'managers' =>       [231242, 124124] // (user ids)
//];

```

**Parameters**

| Name | Type | Required | Description |
|-----------|-----------|-----------|-----------|
| map | `array` | yes | An array of property address strings, keyed at the requested response. |
| contextObjId | `int` | yes | The ID of the object you are using as root context. |
| return type | `array` | no (default: `[]`) | When set to false, the method will return a proper PHP array. Otherwise, it will return a JSON object. |