***

**Overview**
A query object represents a database query, and are used in api calls to set filters on the queries. In Javascript, they are Objects, and in Php, they are Arrays--but their structure is essentially the same


**Where They Are Used**
On the front end, these are used in all api queries that can be filtered in some way--getWhere, getSum, getGroupSum, etc. Our most commonly used call is:
sb.data.db.obj.getWhere(objectType, queryObject, callback)


**Structure**
In our query objects, the keys correspond to keys of properties in the set, and the most basic condition has the value to that key being a value to check equivalence against in the set at that property. So, to find only the contacts where the first:

    {
        fname: '<PERSON>'
    }

To find only the contacts where the first name is '<PERSON>' and the last name is '<PERSON><PERSON><PERSON>', we can say:

    {
        fname:   'Bob'
        lname: 'Loblaw'
    }


**Kinds of Conditions**
We have a small library of other conditions we can use to create more complex queries, each which can take in their own arguments:

***Tags & ObjectIds***
- **any (values)**
    - Where values is an array of ints (tags), will match if any tag in values is in an items tag set
- If no type is set, you just provide a value, like tagged_with: 231
    - to look for items tagged with 231
- You can also provide an array, tagged_with: [2312, 3212]
    - to look for items tagged with both 2312 and 3212

***General***
- **contains (value)**
    - Checks if a string or array contains the given value.
- **not_equal (value)**
    - Checks if a value is not equal to the given value.
- **not_set ()**
    - Checks if the value is not set on the object

***Arrays***
- **intersect (value)**
    - Checks if an array contains any of the same values as the array given in value.

***Numbers***
- **greater_than (value)**
- **less_than (value)**

***Dates***
- **between (start, end)**
    - Checks if a date is between start and end, where start and end are unix timestamps.
- **before (date)**
- **after (date)**
- **overlap (range, endField)**
    - Checks if a date range, starting at the keyed property and ending at endField overlaps with the range given in range, where range is an object with start and end properties in unix timestamps.

To use the conditions above in a query object, the value becomes an object where 'type' names the condition you want to use, and arguments to that specific condition check are included in that object. So, to find all contacts where the first name is 'Bob':

    {
        fname: 'Bob',
        date_created: {
            type: 'after',
            date: moment().startOf('week').unix()
        }
    }


***Nesting And/Or***
We can further refine our queries using the and and or keywords in the keys our queries. If we want to find all contacts that were created this week or whose first name contains the string 'Bo', we could say:

    where = {
        date_created: {
            type: 'after',
            date: moment().startOf('week').unix(),
            or: {
                fname: {
                    type: 'contains',
                    value: 'Bo'
                }
            }
        }
    }