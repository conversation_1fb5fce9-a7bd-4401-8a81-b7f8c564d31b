***

### getObjectsWhere()

This method can be used to query for one or more objects in the system with various filters and options.

**Method Definition**

```

$objects = $this->getObjectsWhere(
	'contacts', // object type
	[ 
		"lname" => "Smith"  // where object
	], 	
	true, // return type
	0 // child object/selection object depth
);

```

**Parameters**

| Name | Type | Required | Options | Description |
|-----------|-----------|-----------|-----------|-----------|
| object type | `string` or `array` | yes | `blueprint type`,<br />`any`,<br />`#`,<br />`array of strings` | The type of the object you are trying to retrieve.<br /><br />`blueprint type`: The blueprint type of the object(s) you are searching for. Only objects of this type will be returned.<br /><br />`any`: Disregards the object type of the return objects. Allows you to search across object types, including user-created Sets.<br /><br /> `#`: The same as setting this value to `any`, only it restricts the search to user-created Sets.<br /><br />`array of strings`: Setting this value to an array of the string values above, allows you to refine your search further. Setting this value to `['groups','#']` will return results of objects of the `group` type and user-defined Sets. |
| where object | `array` | yes |  | The is the object that specifies the filters and other query parameters to be used. |
| return type | `boolean` | no (default: `true`) | `true`,<br />`false` | When set to false, the method will return a proper PHP array. Otherwise, it will return a JSON object. |
| child object depth/selection object | `int` or `array` | no (default: `0`) |  | When greater than 0, the returned object(s) will contain their child objects up to the specified depth. Can also be a `selectionObject` array which allows you to specify the exact structure of the data that is returned. |

**The `where` object**

The example above will get all `contacts` with the last name of 'Smith'.

Example Response:
```

$objects = [
	[
		'id' => 1234,
		'fname' => 'Joe',
		'lname' => 'Smith',
		'object_bp_type' => 'contacts',
		...
	],
	[
		'id' => 2345,
		'fname' => 'Jane',
		'lname' => 'Smith',
		'object_bp_type' => 'contacts',
		...
	]
]

```

Get all objects tagged with a specific set of tags:
```

$objects = $this->getObjectsWhere(
	'any',
	[ 
		'tagged_with' => [
			'type' => 'any',
			'values' => [3456, 5678]
		]
	]
);

```