# Pagoda

Description of Pagoda.

## App API File

**Todos:**

- [ ] crud methods
	- [ ] where
	- [X] getById
	- [X] updateObject
	- [ ] Selection object
	- [ ] Child objects
- [ ] Sending emails

## Cron System

- [ ] _cron.php
- [ ] single cron file

## CRUD Operations

### The `objectType` Property

When performing simple queries, the `objectType` property is a string set to the `object_bp_type` of the object you are creating, getting, or updating. However, 

### Selection Objects

A `Selection Object` can be passed to many of the get functions that specifies the exact properties of an object or list of objects you would like returned. An example of this would be returning only the `lname` property of the `contacts` object being retrieved.

In most cases, the `Selection Object` can be passed in, in place of the `Child Object` property.

Example:
```
$selectionObject = [
	"lname" => false
];

$contact = $this->getObjectById(
	'contacts',
	1234,
	true,
	$selectionObject // <-- selection object here
);
```

This will result in only the `lname` property of the `contacts` object to be returned.

In addition to the usage above, you can also put `objectType` names (for Sets) into `Selection Objects` like property names, to pull in all records that are pointing to the root object in the **parent** property as if they were direct pointers:

```
$select = [
	'name' => true
		'<Set Name goes here>' => [
			'name' => true
		]
]
```

Or like this to pull in records in the specified set tagged with the root obj in the query:

```
$select = [
	'name' => true
		'#<Set Name goes here>' => [
			'name' => true
		]
]
```

You can further filter the nested calls with their own ‘where’ clauses, like this:

```
$select = [
	'name' => true,
	'#<Set Name goes here>' => [
		'name' => true,
		'where' => [
	    	... 
   		]
 	]
]
```

> Remember, these selection objects are read recursively (use responsibly).

### Child Objects

The `Child Objects` property is an int that when greater than 0, will return the requested object and all child objects up to the specified depth.

Example Request:

You would like to return a `Contact` and its related `Company` in a single call. Since this is a direct relationship, you can set the `Child Objects` property to 1 to achieve this. Once run, the method will return the requested `Contact` object along with all related objects up to a depth of 1, which includes the related `Company` object in this case.

```
$contact = $this->getObjectById(
	'contacts',
	1234,
	true,
	1 // <-- child object depth here
);
```

Response *with* `Child Object` depth:

```
$contact = [
	'fname' => 'John',
	'lname' => 'Doe',
	'company' => [    // <-- Contains the full company object.
		'name' => 'ABC Company'
	]
];
```

Response *without* `Child Object` depth:

```
$contact = [
	'fname' => 'John',
	'lname' => 'Doe',
	'company' => 1234567 // <-- Only the ID of the related company is returned.
];
```

## Actions/Triggers/Conditions

## Services
- What is a new service.
- How to create a new service.

### Single Service
- Example of a method.