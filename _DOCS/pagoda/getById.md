***

### getById()

This method can be used to get a single object with or without `Child Objects`.

**Method Definition**

```

$object = $this->getObjectById(
	'contacts', // object type
	1234, // id
	true // return type
);

```

**Parameters**

| Name | Type | Required | Description |
|-----------|-----------|-----------|-----------|
| object type | `string` | yes | The type of the object you are trying to retrieve. |
| id | `int` | yes | The ID of the object you are trying to retrieve. |
| return type | `boolean` | no (default: `true`) | When set to false, the method will return a proper PHP array. Otherwise, it will return a JSON object. |