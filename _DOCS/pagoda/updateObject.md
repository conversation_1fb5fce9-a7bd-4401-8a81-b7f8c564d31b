***

### updateObject()

This method will update a single object or an array of objects and return the result.

**Method Definition**

```

$object = $this->updateObject(
	[],
	'contacts',
	true,
	false
);

```

**Parameters**

| Name | Type | Required | Description |
|-----------|-----------|-----------|-----------|
| object(s) to update| `array` | yes | Object(s) you would like to update. This can be either a single object or a list of objects. Either way, you must include the `id` property for each record you are trying to update. |
| objectType | `string` | yes | The type of the object you are trying to update. If passing an array of objects, the `object_bp_type` must be set on each record you are trying to update. |
| return json | `boolean` | no (default: `true`) | When set to false, the method will return a proper PHP array. Otherwise, it will return a JSON object. |
| return child object depth | `int` | no (default: `0`) | When greater than 0, the returned object(s) will contain their child objects up to the specified depth. |