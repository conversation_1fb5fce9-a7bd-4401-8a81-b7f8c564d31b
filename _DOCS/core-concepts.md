# Core Concepts

## Sets

### FAQs

#### How do I find subsets for a given set?

Subsets are related to their parent/root set by a pointer on the subset (**entity_type**) object at **_class**. So to gather all subsets of Set A, you can query the api like this:

```
sb.data.db.obj.getWhere(
  'entity_type'
  , {
	_class: setA.id
  }
  , function (subsets) { console.log('Here are the direct subsets of Set A: ', subsets) }
);
```

#### How do I create a new kind of trigger?

This will end up being a two-step process - 

1. First, we'll need to let the API know that this new trigger-type exists by adding it as an option to the relevant properties on the blueprints for actions/conditions.
2. Next, we'll need to establish a UI to let users configure action chains for the new trigger.

First, the api needs to know that the new trigger type exists. We can do this by adding it as options :

```
... "trigger": {
	  "name": "Trigger"
	  , "type": "select"
	  , "immutable": false
	  , "options": {
			"stateChange": "State Change"
			, "clientCommentPosted": "Client Comment Posted"
			//... Add the new option here
	  }
} ...
```

- blueprint files in ***_SRC/blueprints/event_type.json and _SRC/blueprints/condition.json\***

Once this trigger type is established, the ***triggerActions\*** endpoint can be used. On the back-end, it can be used like this:

```
$app->triggerActions(
	  $request = [
		  'type' => String       // the type of trigger (the option you just added to the blueprints above)
		  'context' => ObjectId  // the triggering context object
	  ]
	  $json = Int                // formatting the request - 0 for a php array, 1 for json
```

Next, we need to establish the UI for users to configure actions tied to the new trigger. Both the **event_type** and **condition** objects have the following properties that are relevant here: 

- **object**: 	*generally, a linking "type" object that lets the system know* 
- **trigger**: 	*the kind of trigger, stored from a selections list.*

```
sb.notify({
	  type: 'view-actions-by-trigger'
	  , data: {
			triggerType: String    // the type of trigger (the option you just added to the blueprints above)
			, context:   ObjectId  // the triggering context object
	  }
});
```