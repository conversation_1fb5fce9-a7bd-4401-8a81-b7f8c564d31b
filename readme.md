# Bento Systems Documentation1. 

- [Core Concepts](#core-concepts)
  - [Sets](#sets)
    - [FAQs](#faqs)
      - [How do I find subsets for a given set?](#how-do-i-find-subsets-for-a-given-set)
      - [How do I create a new kind of trigger?](#how-do-i-create-a-new-kind-of-trigger)
- [API](#api)
  - [Services](#services)
    - [Registering a New Service](#registering-a-new-service)
  - [Data Layer](#data-layer)
    - [Basic Concepts](#basic-concepts)
      - [Selection Objects](#selection-objects)
      - [Where Objects](#where-objects)
      - [How do I filter a set where records are tagged with any tag in a given set, AND another specific tag?](#how-do-i-filter-a-set-where-records-are-tagged-with-any-tag-in-a-given-set-and-another-specific-tag)
    - [Methods](#methods)
      - [objs->create](#objs-create)
- [notify.js](#notifyjs)
  - [Fields](#fields)
    - [Registering a Field](#registering-a-field)
    - [Field Instantiation](#field-instantiation)
    - [Schedule Field](#schedule-field)
      - [Description of the Field](#description-of-the-field)
      - [Developer Guide](#developer-guide)
    - [Stripe Payment Sources Field](#stripe-payment-sources-field)
      - [Description of the Field](#description-of-the-field-1)
      - [Developer Guide](#developer-guide-1)
    - [Time Tracking Field](#time-tracking-field)
      - [Description of the Field](#description-of-the-field-2)
      - [Developer Guide](#developer-guide-2)
- [Pagoda](#pagoda)
  - [App API File](#app-api-file)
  - [Cron System](#cron-system)
  - [CRUD Operations](#crud-operations)
    - [The `objectType` Property](#the-objecttype-property)
    - [Selection Objects](#selection-objects-1)
    - [Child Objects](#child-objects)
  - [Actions/Triggers/Conditions](#actionstriggersconditions)
  - [Services](#services-1)
    - [Single Service](#single-service)
    - [getById()](#getbyid)
    - [getObjectsWhere()](#getobjectswhere)
    - [objs->getDataFromMap ($map = null, $contextObjId = null)](#objs-getdatafrommap-map--null-contextobjid--null)
    - [updateObject()](#updateobject)

<!-- tocstop -->

# Core Concepts

## Sets

### FAQs

#### How do I find subsets for a given set?

Subsets are related to their parent/root set by a pointer on the subset (**entity_type**) object at **\_class**. So to gather all subsets of Set A, you can query the api like this :

```
sb.data.db.obj.getWhere(
  'entity_type'
  , {
	_class: setA.id
  }
  , function (subsets) { console.log('Here are the direct subsets of Set A: ', subsets) }
);
```

#### How do I create a new kind of trigger?

This will end up being a two-step process -

1. First, we'll need to let the API know that this new trigger-type exists by adding it as an option to the relevant properties on the blueprints for actions/conditions.
2. Next, we'll need to establish a UI to let users configure action chains for the new trigger.

First, the api needs to know that the new trigger type exists. We can do this by adding it as options :

```
... "trigger": {
	  "name": "Trigger"
	  , "type": "select"
	  , "immutable": false
	  , "options": {
			"stateChange": "State Change"
			, "clientCommentPosted": "Client Comment Posted"
			//... Add the new option here
	  }
} ...
```

- blueprint files in **\*\_SRC/blueprints/event_type.json and \_SRC/blueprints/condition.json\***

Once this trigger type is established, the **\*triggerActions\*** endpoint can be used. On the back-end, it can be used like this:

```
$app->triggerActions(
	  $request = [
		  'type' => String       // the type of trigger (the option you just added to the blueprints above)
		  'context' => ObjectId  // the triggering context object
	  ]
	  $json = Int                // formatting the request - 0 for a php array, 1 for json
```

Next, we need to establish the UI for users to configure actions tied to the new trigger. Both the **event_type** and **condition** objects have the following properties that are relevant here:

- **object**: _generally, a linking "type" object that lets the system know_
- **trigger**: _the kind of trigger, stored from a selections list._

```
sb.notify({
	  type: 'view-actions-by-trigger'
	  , data: {
			triggerType: String    // the type of trigger (the option you just added to the blueprints above)
			, context:   ObjectId  // the triggering context object
	  }
});
```

# API

## Services

### Registering a New Service

## Data Layer

### Basic Concepts

#### Selection Objects

Selection objects are used to tell the api exactly what properties (and child object data) to pull in in a given request. In the **sb.data.db.obj.where**, this can be added to the query directly at **where.childObjs**.

For example: f you are looking at **time_entries**, and want to pull in related users objects (at the **staff** property) and the profile image (at the **profile_image** property on the staff object), you can set the selection object to:

```
{
  staff: {
	profile_image: true
  }
}
```

**NOTE:** _Property definitions on all hard-coded object types in the system can be found in_ **\*\_SRC/blueprints\*** _in json files._

#### Where Objects

#### How do I filter a set where records are tagged with any tag in a given set, AND another specific tag?

To filter a set where records are tagged with any of a tag set [1, 2, 3] **\*AND\*** contain another tag [4] tag, we can combine two **tagged_with** filters to build a where clause for our query like this:

```
where = {
  tagged_with: {
	type:     'any'
	, values: [1, 2, 3]
	, and:    {
	  tagged_with: [4]
	}
  }
};
```

### Methods

#### objs->create

`create (String $objectType = '', Array $objectData = null, ...)`

The create method is used to establish a new record (of any type) in the database.

# notify.js

## Fields

### Registering a Field

### Field Instantiation

```
sb.notify({
	type:'',
	data:{
		type:'field-type',
		options:{
			option1:'' // description of the option
		}
	}
})
```

### Schedule Field

#### Description of the Field

The schedule field allows users to schedule when single or recurring actions should occur.

**Behaviors:**

| Action | Behavior |
| ------ | -------- |

#### Developer Guide

**Field Type**: `schedule`

**Options:**

| Name          | Type      | Description                                                                            |
| ------------- | --------- | -------------------------------------------------------------------------------------- |
| commitUpdates | `boolean` | Default: `true` When set to `false`, prevents running an update call within the field. |

**Data Model:**

The schedule field stores an array of field data.

So, for entities, you would see something like this:

\_30: {
quantityOfTime: 1, // related to 'unitOfTime', the amount of 'unitOfTime'
unitOfTime: 'week', // options are 'hour', 'day', 'week', 'month', 'year'
dayOfWeek: 'Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday' // a comma separated string
dayOfMonth: '1', // numeric day of month 1-31
monthOfYear: 'January', // single month name
timeOfDay: '12:00 pm', // time of day
startAfter: false, // boolean to toggle on the number of days after transitioning to the workflow state that the schedule should start
startAfterQuantity: 1, // number of days after transitioning to the workflow state that the schedule should start
endsAfter: false, // boolean to toggle on the number of times the schedule should run
endsAfterQuantity: 1, // the number of times the schedule should run
isScheduled: false // boolean to toggle the active state of the schedule
}

**Known Issues:**

-

**Cleanup:**

-

**Screenshots:**

### Stripe Payment Sources Field

#### Description of the Field

Here is the description of the timer field and what it can do.

**Behaviors:**

| Action              | Behavior                                           |
| ------------------- | -------------------------------------------------- |
| Timer is turned on. | Timer in the sidebar is red.                       |
| Time is logged.     | Data is rolled up to a logged value on the record. |

#### Developer Guide

**Field Type**: `stripe-payment-source`

**Options:**

| Name | Type      | Description                                                                        |
| ---- | --------- | ---------------------------------------------------------------------------------- |
| mini | `boolean` | Default: `false` When set to `true`, displays a much smaller version of the field. |

**Data Model:**

Here is a description of the internal data model for this field.

**Known Issues:**

-

**Cleanup:**

-

**Screenshots:**

### Time Tracking Field

#### Description of the Field

Here is the description of the timer field and what it can do.

**Behaviors:**

| Action              | Behavior                                           |
| ------------------- | -------------------------------------------------- |
| Timer is turned on. | Timer in the sidebar is red.                       |
| Time is logged.     | Data is rolled up to a logged value on the record. |

#### Developer Guide

**Field Type**: `timeTracking`

**Options:**

| Name | Type      | Description                                                                        |
| ---- | --------- | ---------------------------------------------------------------------------------- |
| mini | `boolean` | Default: `false` When set to `true`, displays a much smaller version of the field. |

**Data Model:**

Here is a description of the internal data model for this field.

**Known Issues:**

-

**Cleanup:**

-

**Screenshots:**

# Pagoda

Description of Pagoda.

## App API File

**Todos:**

- [ ] crud methods
  - [ ] where
  - [x] getById
  - [x] updateObject
  - [ ] Selection object
  - [ ] Child objects
- [ ] Sending emails

## Cron System

- [ ] \_cron.php
- [ ] single cron file

## CRUD Operations

### The `objectType` Property

When performing simple queries, the `objectType` property is a string set to the `object_bp_type` of the object you are creating, getting, or updating. However,

### Selection Objects

A `Selection Object` can be passed to many of the get functions that specifies the exact properties of an object or list of objects you would like returned. An example of this would be returning only the `lname` property of the `contacts` object being retrieved.

In most cases, the `Selection Object` can be passed in, in place of the `Child Object` property.

Example:

```
$selectionObject = [
	"lname" => false
];

$contact = $this->getObjectById(
	'contacts',
	1234,
	true,
	$selectionObject // <-- selection object here
);
```

This will result in only the `lname` property of the `contacts` object to be returned.

In addition to the usage above, you can also put `objectType` names (for Sets) into `Selection Objects` like property names, to pull in all records that are pointing to the root object in the **parent** property as if they were direct pointers:

```
$select = [
	'name' => true
		'<Set Name goes here>' => [
			'name' => true
		]
]
```

Or like this to pull in records in the specified set tagged with the root obj in the query:

```
$select = [
	'name' => true
		'#<Set Name goes here>' => [
			'name' => true
		]
]
```

You can further filter the nested calls with their own ‘where’ clauses, like this:

```
$select = [
	'name' => true,
	'#<Set Name goes here>' => [
		'name' => true,
		'where' => [
	    	...
   		]
 	]
]
```

> Remember, these selection objects are read recursively (use responsibly).

### Child Objects

The `Child Objects` property is an int that when greater than 0, will return the requested object and all child objects up to the specified depth.

Example Request:

You would like to return a `Contact` and its related `Company` in a single call. Since this is a direct relationship, you can set the `Child Objects` property to 1 to achieve this. Once run, the method will return the requested `Contact` object along with all related objects up to a depth of 1, which includes the related `Company` object in this case.

```
$contact = $this->getObjectById(
	'contacts',
	1234,
	true,
	1 // <-- child object depth here
);
```

Response _with_ `Child Object` depth:

```
$contact = [
	'fname' => 'John',
	'lname' => 'Doe',
	'company' => [    // <-- Contains the full company object.
		'name' => 'ABC Company'
	]
];
```

Response _without_ `Child Object` depth:

```
$contact = [
	'fname' => 'John',
	'lname' => 'Doe',
	'company' => 1234567 // <-- Only the ID of the related company is returned.
];
```

## Actions/Triggers/Conditions

## Services

- What is a new service.
- How to create a new service.

### Single Service

- Example of a method.

---

### getById()

This method can be used to get a single object with or without `Child Objects`.

**Method Definition**

```

$object = $this->getObjectById(
	'contacts', // object type
	1234, // id
	true // return type
);

```

**Parameters**

| Name        | Type      | Required             | Description                                                                                            |
| ----------- | --------- | -------------------- | ------------------------------------------------------------------------------------------------------ |
| object type | `string`  | yes                  | The type of the object you are trying to retrieve.                                                     |
| id          | `int`     | yes                  | The ID of the object you are trying to retrieve.                                                       |
| return type | `boolean` | no (default: `true`) | When set to false, the method will return a proper PHP array. Otherwise, it will return a JSON object. |

---

### getObjectsWhere()

This method can be used to query for one or more objects in the system with various filters and options.

**Method Definition**

```

$objects = $this->getObjectsWhere(
	'contacts', // object type
	[
		"lname" => "Smith"  // where object
	],
	true, // return type
	0 // child object/selection object depth
);

```

**Parameters**

| Name                                | Type                | Required             | Options                                                         | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| ----------------------------------- | ------------------- | -------------------- | --------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| object type                         | `string` or `array` | yes                  | `blueprint type`,<br />`any`,<br />`#`,<br />`array of strings` | The type of the object you are trying to retrieve.<br /><br />`blueprint type`: The blueprint type of the object(s) you are searching for. Only objects of this type will be returned.<br /><br />`any`: Disregards the object type of the return objects. Allows you to search across object types, including user-created Sets.<br /><br /> `#`: The same as setting this value to `any`, only it restricts the search to user-created Sets.<br /><br />`array of strings`: Setting this value to an array of the string values above, allows you to refine your search further. Setting this value to `['groups','#']` will return results of objects of the `group` type and user-defined Sets. |
| where object                        | `array`             | yes                  |                                                                 | The is the object that specifies the filters and other query parameters to be used.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| return type                         | `boolean`           | no (default: `true`) | `true`,<br />`false`                                            | When set to false, the method will return a proper PHP array. Otherwise, it will return a JSON object.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| child object depth/selection object | `int` or `array`    | no (default: `0`)    |                                                                 | When greater than 0, the returned object(s) will contain their child objects up to the specified depth. Can also be a `selectionObject` array which allows you to specify the exact structure of the data that is returned.                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |

**The `where` object**

The example above will get all `contacts` with the last name of 'Smith'.

Example Response:

```

$objects = [
	[
		'id' => 1234,
		'fname' => 'Joe',
		'lname' => 'Smith',
		'object_bp_type' => 'contacts',
		...
	],
	[
		'id' => 2345,
		'fname' => 'Jane',
		'lname' => 'Smith',
		'object_bp_type' => 'contacts',
		...
	]
]

```

Get all objects tagged with a specific set of tags:

```

$objects = $this->getObjectsWhere(
	'any',
	[
		'tagged_with' => [
			'type' => 'any',
			'values' => [3456, 5678]
		]
	]
);

```

---

### objs->getDataFromMap ($map = null, $contextObjId = null)

This method can be used to pull together data points related to the context object into key-value pairs matching the keys submitted in the **$map** argument..

**Method Definition**

```

$map = [
    'contactFName' =>   'main_contact.fname',
    'companyName' =>    'main_contact.company.name',
    'managers' =>       'managers'
];

$mappedData = $objs->getDataFromMap(
    $map,
    $projectId
);

// $mappedData = [
//    'contactFName' =>   'Larry',
//    'companyName' =>    'Cool Company,
//    'managers' =>       [231242, 124124] // (user ids)
//];

```

**Parameters**

| Name         | Type    | Required           | Description                                                                                            |
| ------------ | ------- | ------------------ | ------------------------------------------------------------------------------------------------------ |
| map          | `array` | yes                | An array of property address strings, keyed at the requested response.                                 |
| contextObjId | `int`   | yes                | The ID of the object you are using as root context.                                                    |
| return type  | `array` | no (default: `[]`) | When set to false, the method will return a proper PHP array. Otherwise, it will return a JSON object. |

---

### updateObject()

This method will update a single object or an array of objects and return the result.

**Method Definition**

```

$object = $this->updateObject(
	[],
	'contacts',
	true,
	false
);

```

**Parameters**

| Name                      | Type      | Required             | Description                                                                                                                                                                           |
| ------------------------- | --------- | -------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| object(s) to update       | `array`   | yes                  | Object(s) you would like to update. This can be either a single object or a list of objects. Either way, you must include the `id` property for each record you are trying to update. |
| objectType                | `string`  | yes                  | The type of the object you are trying to update. If passing an array of objects, the `object_bp_type` must be set on each record you are trying to update.                            |
| return json               | `boolean` | no (default: `true`) | When set to false, the method will return a proper PHP array. Otherwise, it will return a JSON object.                                                                                |
| return child object depth | `int`     | no (default: `0`)    | When greater than 0, the returned object(s) will contain their child objects up to the specified depth.                                                                               |

## Republix
