{"name": "docker_web_app", "version": "1.0.0", "description": "Node.js on Docker", "author": "First Last <<EMAIL>>", "main": "server.js", "scripts": {"start": "node server.js", "test": "mocha"}, "dependencies": {"FormData": "^0.10.1", "ajax-request": "^1.2.3", "axios": "^0.20.0", "chai": "^4.2.0", "express": "^4.16.1", "form-data": "^3.0.0", "mocha": "^8.1.3", "moment": "^2.29.1", "to-formdata": "^1.0.2", "underscore": "^1.11.0", "underscore-node": "^0.1.2"}}