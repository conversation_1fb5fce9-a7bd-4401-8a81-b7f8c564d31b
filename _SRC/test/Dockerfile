FROM node:12

# Create app directory
RUN mkdir -p /usr/src/app
WORKDIR /usr/src/app

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
# where available (npm@5+)
COPY package*.json ./
CMD npm install

# If you are building your code for production
# RUN npm ci --only=production

# Bundle app source
COPY . .
# COPY ./src .

EXPOSE 8080
# CMD [ "node", "server.js" ]
CMD ["mocha", "test/sum.spec.js", "--reporter", "spec"]
