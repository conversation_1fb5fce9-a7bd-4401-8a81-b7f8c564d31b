var assert = require("assert");
var api = require("../src/_database").api;
var instanceKey = "TEST_ENVIRONMENT";

// Check if a test instance exists
describe("tests", function () {
  describe("checkTestEnvironment", function () {
    it("Passes the check", function (done) {
      api.controller("setTestEnvironment", {}, function (isSetup) {
        if (isSetup === true) {
          done();
        } else {
          done(
            new Error(
              "Test environment not set up: Run this in pgAdmin to set it up:\n\n" +
                'INSERT INTO "objects" ("id", "instance", "object_type", "object_data", "date_created", "is_deleted", "tagged_with", "read_obj", "write_obj", "notify") VALUES ' +
                '(920411,	\'dev_test_env\',	\'instances\',	\'{"id": 920411, "read": [], "write": [], "notify": [], "db_post": "https://pagoda.voltz.software/_repos/rickyvoltz/notify/pagoda/_coredev/_post.php?", "db_read": "https://pagoda.voltz.software/_repos/rickyvoltz/notify/pagoda/_coredev/_get.php?", "enabled": 1, "version": "bento", "db_write": "https://pagoda.voltz.software/_repos/rickyvoltz/notify/pagoda/_coredev/_get.php?", "instance": "dev_test_env", "sms_from": "6152050722", "emailFrom": "<EMAIL>", "components": "_app,_crud,_crudTable,payroll,login-component,inventory-units,companies,contact,_instances,_workflows,tasks2,notes2,rules,emails,_charts,payroll,timeClockReport,workorders,invoices,tlbSMS,paymentMethods,integrations,calendar,formBuilder,tags,staffList,staffDetails,_users,scheduling,notes,decisions,timeClock,contracts,inventory,csv-uploader,contactInfo", "created_by": 919898, "files_read": "https://pagoda.voltz.software/_repos/_production/pagoda/_coredev/files/get/?", "full_count": null, "object_uid": 0, "systemName": "Dev Test Env", "twilio_sid": "**********************************", "data_source": 0, "files_write": "https://pagoda.voltz.software/_repos/_production/notify/pagoda/_coredev/files/post/?", "pageModules": "", "permissions": "", "tagged_with": [], "company_logo": 1203364, "date_created": "2017-09-29 11:31:26.056276", "files_bucket": "https://pagoda.voltz.software/_repos/_production/pagoda/_files/_instances/", "files_delete": "https://pagoda.voltz.software/_repos/_production/notify/pagoda/_coredev/files/delete/?", "last_updated": "2019-01-19 16:32:13", "main_contact": 0, "mandrill_api": "**********************", "moduleSource": "https://pagoda.voltz.software/_repos/rickyvoltz/notify/notify/", "qty_of_users": -4, "twilio_token": "1bc16e9c21afe54bb3565a512b42beff", "userSettings": "", "factorySource": "https://pagoda.voltz.software/_repos/rickyvoltz/notify/notify/_factory/", "mailchimp_api": "************************************", "searchObjects": "", "settingSource": "https://pagoda.voltz.software/_repos/rickyvoltz/notify/notify/", "data_source_id": 0, "object_bp_type": "instances", "per_user_price": 0, "componentSource": "https://pagoda.voltz.software/_repos/rickyvoltz/notify/notify/", "last_updated_by": 920410, "settingsModules": "editBluprints", "settings_objects": "inventory_units,inventory_service,contact_types,contact_info_types", "mailchimp_list_id": "", "stripe_account_id": "acct_1De71rHacOS9Zsw2", "quickbooks_realm_id": "***************", "quickbooks_access_token": "", "quickbooks_refresh_token": ""}\',	\'2017-09-29 11:31:31.329414\', FALSE, NULL, NULL, NULL, NULL),' +
                '(920412,	\'dev_test_env\',	\'users\',	\'{"id": 920412, "dob": "", "password":"sha256:2000:hpurrY1EWL9IjQrVgwvy5YhlxoYsKMGS:2v1mUsU7HaMHz0R3q+h3K/4laPrw4m8M", "pin": "", "ssn": "", "zip": "37066", "base": [1194462], "city": "Gallatin", "read": [], "type": "admin", "color": "black", "email": "<EMAIL>", "fname": "Test", "lname": "User", "phone": "**********", "state": "TN", "write": [], "notify": [], "status": 920459, "street": "215 Higginson Pl S", "country": "United States", "enabled": 1, "service": [1425585], "instance": "dev_test_env", "profiles": [920460], "hire_date": "", "nick_name": "Ricky", "created_by": 919898, "dependents": 0, "full_count": null, "object_uid": 0, "data_source": 0, "tagged_with": [], "date_created": null, "garnishments": 0, "last_updated": "2019-01-09 17:37:30", "filing_status": "single", "profile_image": 1420003, "data_source_id": 0, "object_bp_type": "users", "related_object": 1, "last_updated_by": 920410, "work_week_start": "friday", "termination_date": ""}\',	\'2017-09-29 11:31:31.329414\', FALSE, NULL, NULL, NULL, NULL),' +
                '(920413,	\'dev_test_env\',	\'cookies\',	\'{"id": 920413, "uid": 920412, "type": 0, "token": "7406a2b8435761c32a926c96c9d806fbe3d1e9fe2af33812bff7bfae2547742da773eb37856843afeaef9095472791ba551874acdfcb2b0bd050a854c067cc86", "parent": 0, "series": "9e4f96d7bab022b5241e69bf39b633e23449f2f286218bcf3844b68b4c829385503b52a99ee3fa662848952e0f544703625f41f69b5efcb5cdbae597706e4660", "platform": "Test", "user_type": "", "created_by": 0, "ip_address": "Test", "object_uid": 984, "data_source": 0, "fingerprint": "50101575373690044309353736", "is_template": 0, "date_created": "2021-05-09 22:25:32.326795", "last_updated": "2021-05-09 22:25:32.326795", "data_source_id": 0, "object_bp_type": "cookies", "does_not_expire": 1, "last_updated_by": 0}\',	\'2017-09-29 11:31:31.329414\', FALSE, NULL, NULL, NULL, NULL);'
            )
          );
        }
      });
    });
  });
});
