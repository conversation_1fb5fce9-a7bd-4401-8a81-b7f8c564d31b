var api = require('../src/_database').api;
var _ = require('underscore-node');
var sortSetName = '#SortTest';

// Sort-relevant methods
describe('sort', function () {

    // Create a test set, if it does not exist
    function setTestSortSet (onComplete) {

        api.obj.getWhere(
            'entity_type'
            , {
                bp_name: sortSetName
            }
            , function (set) {

                if (!_.isEmpty(set[0])) {
                    
                    onComplete(set);

                } else {

                    api.obj.create(
                        'entity_type'
                        , {
                            'bp_name':      sortSetName
                            , 'blueprint':  []
                        }
                        , function (set) {

                            onComplete(set);

                        }
                    );

                }

            }
        );

    }

    // Sorting in correct order
    describe('objs->getAll', function () {
        it('Can sort by sortId value', function (done) {
            
            setTestSortSet(function (testSet) {

                // Create three records 
                api.obj.create(
                    sortSetName
                    , [{name: 'one'}, {name: 'two'}, {name: 'three'}]
                    , function () {

                        var page = {
                            count:      true,
                            page:       0,
                            pageLength: 5,
                            paged:      true,
                            sortCol:    'sortIndex',
                            sortDir:    'desc',
                            sortCast:   'string'
                        };

                        // Check the order of the new records on view
                        api.obj.getAll(
                            sortSetName
                            , function (response) {

                                var records = response.data;
                                var passed = true;
                                var sortIndex = records[0]._sortIndex;
                                var errMsg = '';

                                _.each(records, function (record) {

                                    if (typeof record._sortIndex !== 'number') {
                                        passed = false;
                                        errMsg = 'Sort index in response is not a number';
                                    } else if (record._sortIndex < sortIndex) {
                                        passed = false;
                                        errMsg = 'Not sorting correctly';
                                    }
                                    sortIndex = record._sortIndex;

                                });

                                // Clean up the data
                                api.obj.erase(
                                    sortSetName
                                    , _.pluck(records, 'id')
                                    , function (cleanedUp) {

                                        if (passed === true) {

                                            done();
        
                                        } else {
        // console.log(records);
                                            done(new Error(errMsg))
                                        
                                        }

                                    }
                                );

                            }
                            , {name: true}
                            , page
                        ); 

                    }
                );
                
            });

        });
    });

    // New records are created at front
    describe('objs->create', function () {
        it('New records are created with a proper sort index', function (done) {
            
            setTestSortSet(function (testSet) {

                // Create three records 
                api.obj.create(
                    sortSetName
                    , [{name: 'one'}]
                    , function () {

                        var page = {
                            count:      true,
                            page:       0,
                            pageLength: 4,
                            paged:      true,
                            sortCol:    'sortIndex',
                            sortDir:    'desc',
                            sortCast:   'string'
                        };
                        
                        // Check the order of the new records on view
                        api.obj.getWhere(
                            sortSetName
                            , {
                                paged: page
                                , select: {
                                    sortIndex:  true
                                    , name:     true
                                }
                            }
                            , function (response) {

                                var records = response.data;
                                var passed = false;
                                var errMsg = '';
                                // console.log(response);
                                
                                if (
                                    typeof records[0].sort_index === 'string'
                                    && records[0].sort_index.length > 10
                                ) {
                                    passed = true;
                                } else {
                                    passed = false;
                                    errMsg = 'Sort index is too short.';
                                }

                                // Clean up the data
                                // api.obj.erase(
                                //     sortSetName
                                //     , _.pluck(records, 'id')
                                //     , function (cleanedUp) {
                                        if (passed === true) {

                                            done();
        
                                        } else {
        
                                            done(new Error(errMsg))
                                        
                                        }

                                //     }
                                // );

                            }

                        ); 

                    }
                );
                
            });

        });

        it('New records are created at front of sort-order', function (done) {
            
            setTestSortSet(function (testSet) {

                // Create three records 
                api.obj.create(
                    sortSetName
                    , [{name: 'one'}, {name: 'two'}, {name: 'three'}]
                    , function () {
                        // console.log(created);
                        api.obj.create(
                            sortSetName
                            , {name: 'top'}
                            , function (lastCreated) {

                                var page = {
                                    count:      true,
                                    page:       0,
                                    pageLength: 4,
                                    paged:      true,
                                    sortCol:    'sortIndex',
                                    sortDir:    'desc',
                                    sortCast:   'string'
                                };

                                // Check the order of the new records on view
                                api.obj.getWhere(
                                    sortSetName
                                    , {
                                        paged: page
                                        , select: {
                                            sortIndex:  true
                                            , name:     true
                                        }
                                    }
                                    , function (response) {

                                        var records = response.data;
                                        var passed = false;
                                        var errMsg = '';
                                        // console.log(response);
                                        
                                        if (lastCreated.id === records[0].id) {
                                            passed = true;
                                        } else {
                                            passed = false;
                                            errMsg = 'Last created record is not at the top of the list.';
                                        }
        
                                        // Clean up the data
                                        // api.obj.erase(
                                        //     sortSetName
                                        //     , _.pluck(records, 'id')
                                        //     , function (cleanedUp) {
                                                if (passed === true) {
        
                                                    done();
                
                                                } else {
                
                                                    done(new Error(errMsg))
                                                
                                                }
        
                                        //     }
                                        // );
        
                                    }
        
                                ); 

                            }
                        );

                    }
                );
                
            });

        });
    });

    // Move sort order
    describe('moveRecord', function () {
        it('Can move a record between two records', function (done) {
            
            setTestSortSet(function (testSet) {

                // Create three records 
                api.obj.create(
                    sortSetName
                    , [{name: 'one'}, {name: 'two'}, {name: 'three'}]
                    , function () {

                        var page = {
                            count:      true,
                            page:       0,
                            pageLength: 3,
                            paged:      true,
                            sortCol:    'sortIndex',
                            sortDir:    'desc',
                            sortCast:   'string'
                        };

                        // Get current sort order
                        api.obj.getWhere(
                            sortSetName
                            , {
                                paged: page
                                , select: {
                                    sortIndex:  true
                                    , name:     true
                                }
                            }
                            , function (response) {

                                var records = response.data;
                                var newSecond = records[2];
                                var newThird = records[1];
                                api.obj.runSteps(
                                    {
                                        'moveSortOrder': {
                                            type:       'between'
                                            , before:   records[1].id
                                            , after:    records[0].id
                                        }
                                    }
                                    , newSecond.id
                                    , function (response) {
                                        // console.log(response);

                                         // Get current sort order
                                        api.obj.getWhere(
                                            sortSetName
                                            , {
                                                paged: page
                                                , select: {
                                                    sortIndex:  true
                                                    , name:     true
                                                }
                                            }
                                            , function (response) {
// console.log(response);
                                                var records = response.data;
                                                var passed = false;
                                                var errMsg = '';

                                                if (
                                                    records[1].id === newSecond.id
                                                    && records[2].id === newThird.id
                                                ) {
                                                    passed = true;
                                                } else {
                                                    passed = false;
                                                    errMsg = 'Record order not updated.';
                                                }

                                                // Clean up the data
                                                api.obj.erase(
                                                    sortSetName
                                                    , _.pluck(records, 'id')
                                                    , function (cleanedUp) {

                                                        if (passed === true) {

                                                            done();
                        
                                                        } else {
console.log(records);
                                                            done(new Error(errMsg))
                                                        
                                                        }

                                                    }
                                                );

                                            }
                                        );

                                    }
                                );

                            }

                        ); 

                    }
                );
                
            });

        });

        it('Can move to the top of the list', function (done) {
            
            setTestSortSet(function (testSet) {

                // Create three records 
                api.obj.create(
                    sortSetName
                    , [{name: 'one'}, {name: 'two'}, {name: 'three'}]
                    , function () {

                        var page = {
                            count:      true,
                            page:       0,
                            pageLength: 3,
                            paged:      true,
                            sortCol:    'sortIndex',
                            sortDir:    'desc',
                            sortCast:   'string'
                        };

                        // Get current sort order
                        api.obj.getWhere(
                            sortSetName
                            , {
                                paged: page
                                , select: {
                                    sortIndex:  true
                                    , name:     true
                                }
                            }
                            , function (response) {

                                var records = response.data;
                                var newFirst = records[2];
                                api.obj.runSteps(
                                    {
                                        'moveSortOrder': {
                                            type:       'to-top'
                                        }
                                    }
                                    , newFirst.id
                                    , function (response) {
                                        // console.log(response);

                                         // Get current sort order
                                        api.obj.getWhere(
                                            sortSetName
                                            , {
                                                paged: page
                                                , select: {
                                                    sortIndex:  true
                                                    , name:     true
                                                }
                                            }
                                            , function (response) {

                                                var records = response.data;
                                                var passed = false;
                                                var errMsg = '';
                                                // console.log(records);

                                                if (
                                                    records[0].id === newFirst.id
                                                ) {
                                                    passed = true;
                                                } else {
                                                    passed = false;
                                                    errMsg = 'Record order not updated.';
                                                }

                                                // Clean up the data
                                                api.obj.erase(
                                                    sortSetName
                                                    , _.pluck(records, 'id')
                                                    , function (cleanedUp) {

                                                        if (passed === true) {

                                                            done();
                        
                                                        } else {
// console.log(records);
                                                            done(new Error(errMsg))
                                                        
                                                        }

                                                    }
                                                );

                                            }
                                        );

                                    }
                                );

                            }

                        ); 

                    }
                );
                
            });

        });

        it('Can move to the bottom of the list', function (done) {
            
            setTestSortSet(function (testSet) {

                // Create three records 
                api.obj.create(
                    sortSetName
                    , [{name: 'one'}, {name: 'two'}, {name: 'three'}]
                    , function () {

                        var page = {
                            count:      true,
                            page:       0,
                            pageLength: 3,
                            paged:      true,
                            sortCol:    'sortIndex',
                            sortDir:    'asc',
                            sortCast:   'string'
                        };

                        // Get current sort order
                        api.obj.getWhere(
                            sortSetName
                            , {
                                paged: page
                                , select: {
                                    sortIndex:  true
                                    , name:     true
                                }
                            }
                            , function (response) {

                                var records = response.data;
                                var newFirst = records[0];
                                api.obj.runSteps(
                                    {
                                        'moveSortOrder': {
                                            type:       'to-bottom'
                                        }
                                    }
                                    , newFirst.id
                                    , function (response) {
                                        // console.log(response);

                                         // Get current sort order
                                        api.obj.getWhere(
                                            sortSetName
                                            , {
                                                paged: page
                                                , select: {
                                                    sortIndex:  true
                                                    , name:     true
                                                }
                                            }
                                            , function (response) {
// console.log(response);
                                                var records = response.data;
                                                var passed = false;
                                                var errMsg = '';

                                                if (
                                                    records[0].id === newFirst.id
                                                ) {
                                                    passed = true;
                                                } else {
                                                    passed = false;
                                                    errMsg = 'Record order not updated.';
                                                }

                                                // Clean up the data
                                                api.obj.erase(
                                                    sortSetName
                                                    , _.pluck(records, 'id')
                                                    , function (cleanedUp) {

                                                        if (passed === true) {

                                                            done();
                        
                                                        } else {
// console.log(records);
                                                            done(new Error(errMsg))
                                                        
                                                        }

                                                    }
                                                );

                                            }
                                        );

                                    }
                                );

                            }

                        ); 

                    }
                );
                
            });

        });
        
    });

});