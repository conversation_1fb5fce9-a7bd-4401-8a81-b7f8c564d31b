var api = require('../src/_database').api;

// Test basic create on groups
describe('obj', function () {
  describe('#create()', function () {
    it('Should create a new record', function (done) {

      var itemName = 'Test Group';
      api.obj.create('groups', {name: itemName}, function (r) {

        if (r && r.id) {
          done();
        } else {
          done(new Error('New record did not save/return'));
        }
        
      });
      
    });
    it('Should save with the given \'name\' value', function (done) {

      var itemName = 'Test Group';
      api.obj.create('groups', {name: itemName}, function (r) {

        if (r.name === itemName) {
          done();
        } else {
          done(new Error('Name property is not being set as requested'));
        }
        
      });
      
    });
  });

  describe('#getById()', function () {

    it('Should pull the record for the specified id', function (done) {

        api.obj.getById(
            'users'
            , 920412
            , function (userObj) {

                if (
                    userObj.id === 920412
                    && userObj.object_bp_type === 'users'
                ) {

                    done();

                } else {

                    done(new Error('New record did not save/return'));

                }

            }
        )

    });

});

});