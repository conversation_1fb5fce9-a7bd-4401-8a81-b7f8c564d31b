var editable_item = (function(){ 
	
	var data = {};
	
	function showDisplayState(node){
		
		if(node.hasOwnProperty('empty')){
			node.empty();
		}
		
		var	args = data[node.selector].args,
			object = data[node.selector].object,
			style = data[node.selector].args.style || '',
			css = data[node.selector].args.css || '';
		
		if(args.display){
			var displayText = args.display(object) || args.placeholder || '';
		}else{
			var displayText = data[node.selector].object[data[node.selector].prop] || data[node.selector].args.placeholder;
		}
		
		if(data[node.selector].args.hasOwnProperty('header') && data[node.selector].args.header){
			node.makeNode('h', 'headerText', {text: displayText, size: 'medium', style: style, css: 'pda-editable-item'});
		}else{
			node.makeNode('h', 'text', {text: displayText, css: 'pda-editable-item '+ css, style: 'font-size: large;'+ style});
		}
		
		node.h.listeners = [function(e){
			
			$(node.h.selector).on('click', function(e){
			
				var selector = '.'+ $(this).parent().attr('class');
				showEditState(data[selector].node, data[selector].object, data[selector].prop);
				data[selector].node.patch();
				
			});
			
		}];
		
	}
	
	function showEditState(node, object, prop){
		
		if(node.hasOwnProperty('empty')){
			node.empty();
		}
		
		var style = '';
		if(data[node.selector].args.header){
			style = 'font-size:x-large;';
		}else{
			style = '';
		}
		
		var	bottom = '16px',
			height = '12px;';
		if(data[node.selector].args.hasOwnProperty('header') && data[node.selector].args.header){
			bottom = '0px';
			height = '63px';
		}
		
		// input field
		var	inputArgs = _.clone(data[node.selector].args);
		inputArgs.name = 'menu_name';
		inputArgs.style = style +'margin:0px;';
		inputArgs.value = object[prop];
		inputArgs.noSpace = true;
		if(!inputArgs.hasOwnProperty('type')){ inputArgs.type = 'text'; }
		if(data[node.selector].args.hasOwnProperty('parseVal')){
			inputArgs.value = data[node.selector].args.parseVal(inputArgs.value);
		}
		
		var input = node.makeNode('l', 'column', {width: 12, style: 'line-height: '+ height +'; height: '+ height +'; display: inline-block; bottom: '+ bottom +'; position: relative;'})
			.makeNode('f', 'form', {
				menu_name: inputArgs
			}).menu_name;
		
		// focus cursor in field on patch/build
		if(!input.hasOwnProperty('listeners')){
			input.listeners = [];
		}
		input.listeners.push(function(selector){$(selector).focus();});
		
		// check button
// 		node.makeNode('r', 'column', {width: 2, style: 'line-height: '+ height +'; height: '+ height +'; display: inline-block; bottom:'+ bottom +'; position: relative;'})
// 			.makeNode('b', 'button', {text: '<i class="fa fa-check" aria-hidden="true"></i>', css: 'btn-success btn-block'});
		
		// listeners
		var listenerFunc = function(e){
			
			return function(e){

				if(e.type === 'click'){
					var selector = '.'+ $(this).parent().parent().attr('class');
				}else if(e.type === 'focusout'){
					var selector = '.'+ $(this).parent().parent().parent().attr('class');
				}else if(e.type === 'keypress'){
					if(e.which !== 13){
						return true;
					}
					var selector = '.'+ $(this).parent().parent().parent().attr('class');
				}
				
				data[selector].object[data[selector].prop] = data[selector].node.l.f.process().fields.menu_name.value || '';
				
				if(data[selector].args.process){
					data[selector].object[data[selector].prop] = data[selector].args.process(data[selector].object[data[selector].prop]);
				}
				
				showDisplayState(data[selector].node);
				data[selector].node.patch();
				
				if(data[selector].args.hasOwnProperty('onUpdate')){
					
					Factory.triggerEvent({
						type: data[selector].args.onUpdate,
						data: {
							object: data[selector].object
						}
					});
					
				}
				
			}
			
		};
		
		input.listeners.push(function(e){
			$(node.l.f.menu_name.selector).on('focusout keypress', listenerFunc(e));
		});
		
		/*
node.r.b.listeners = [function(e){
			$(node.r.b.selector).on('click', listenerFunc(e));
		}];
*/
		
	}
	
	return {
		
		make: function(selector, args, name){
			
			var node = {
				selector: selector +'-editable-item-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};
			
			data[node.selector] = {
				object: args.object,
				prop: args.prop,
				args: args,
				node: node
			};
			
			node.html = '<div class="'+ node.selector.replace('.', '') +'" style="'+ args.containerStyle +'"></div>';
			
			// if object contains the value, display the name
			if(!args.edit){
				
				showDisplayState(node);
			
			// if the menu doesn't have a name, show a text field where user can name it	
			}else{
				
				showEditState(node, data[node.selector].object, data[node.selector].prop);
				
			}
			
			return node;
			
		}

	}
	
})();