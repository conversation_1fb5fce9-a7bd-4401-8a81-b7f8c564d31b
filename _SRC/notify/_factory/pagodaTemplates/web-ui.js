var web_ui_dom_tool = (function(){
	
	return {
		
		make: function(selector, args, name){
			
			var	webUiNode = {
				
				selector: selector +'-pop-'+ name,
				html: '',
				makeNode: Sandbox.makeNode,
				patch: Sandbox.patchNode,
				appendNode: Sandbox.appendNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				properties: {}
				
			};
			
			// map old position classes over to new ones
			var position = 'top left';
			var classes = {
				'bottom-right':'right center',
				'right-bottom':'right center',
				'bottom':'bottom center',
				'left-bottom':'left center',
				'bottom-left':'left center',
				'left':'left center',
				'top-left':'left center',
				'left-top':'left center',
				'top':'top center',
				'right-top':'right center',
				'top-right':'right center',
				'right':'right center'
			};
			if(args.placement){
				_.each(classes, function(newClass, old){
					if(args.placement.indexOf(old) > -1){
						position = newClass;
					}
				});
			}else if(args.position){
				position = args.position;
			}
			
			var trigger = 'hover';
			if(args.trigger){
				trigger = args.trigger;
			}else if(args.on){
				trigger = args.on;
			}
			var style = '';
			if (args.style) {
				style = ' style="'+ args.style +'"';
			}

			webUiNode.html = '<div class="ui popup '+ webUiNode.selector.replace('.', '') +'"'+ style +'></div>';
				
			webUiNode.listeners = [function(parentSelector){
				
				webUiNode.properties.popover = $(parentSelector).popup({
					popup:$(this.selector),
					on:trigger,
					delay: {
					  show: 50,
					  hide: 50
					},
					observeChanges:true,
					hoverable:true,
// 					target:$(parentSelector),
					preserve:true,
					lastResort:true,
					position:position,
					transition:'scale' || args.transition,
					offset: 0 || args.offset
// 					boundary:'.main',
// 					scrollContext:'.main'
				});
				
			}.bind(webUiNode, args.parent.selector)];
			
			webUiNode.show = function(){ 
								
/*
				{
					title:'',
					content:buttonHTML,
					placement:'bottom-right',
					offsetLeft:100,
					offsetTop:10,
					position:'right',
					trigger:'manual',
					backdrop:false,
					closeable:false,
					animation:'pop'
				}
*/
				this.properties.popover.popup('show');
				
			};
			webUiNode.hide = function(){ 

				this.properties.popover.popup('hide'); 
				
			};
			
			return webUiNode;
			
			webUiNode.html = '<div style=""><div id="'+ selector.replace('.', '') +'-pop-content-'+ name +'" class="'+ webUiNode.selector.replace('.', '') +'"></div></div>';
			
			if(typeof args.delay === 'undefined'){
				args.delay = {};
			}
			if(typeof args.async === 'undefined'){
				args.async = {};
			}
			
			webUiNode.listeners = [function(){
				
				$(webUiNode.selector).parent().remove();
// 				console.log(webUiNode);
				var htmls = Sandbox.getNodeHTML(webUiNode);
				var html = htmls.open + htmls.close;
// 				console.log(html);
				webUiNode.properties.popover = $(args.parent.selector).webuiPopover({
				    placement:		args.placement || 'auto',//values: auto,top,right,bottom,left,top-right,top-left,bottom-right,bottom-left,auto-top,auto-right,auto-bottom,auto-left,horizontal,vertical
				    container: 		args.container || document.body,// The container in which the popover will be added (i.e. The parent scrolling area). May be a jquery object, a selector string or a HTML element. See https://jsfiddle.net/1x21rj9e/1/
				    width:			args.width || 'auto',//can be set with  number
				    height:			args.height || 'auto',//can be set with  number
				    trigger:		args.trigger || 'hover',//values:  click,hover,manual(handle events by your self),sticky(always show after popover is created);
				    selector:		args.selector || false,// jQuery selector, if a selector is provided, popover objects will be delegated to the specified. 
				    style:			args.style || '',// Not to be confused with inline `style=""`, adds a classname to the container for styling, prefixed by `webui-popover-`. Default '' (light theme), 'inverse' for dark theme
				    animation:		args.animation || null, //pop with animation,values: pop,fade (only take effect in the browser which support css3 transition)
				    delay: {//show and hide delay time of the popover, works only when trigger is 'hover',the value can be number or object
				        show: 		args.delay.show || null,
				        hide: 		args.delay.hide || 300
				    },
				    async: {
				        type:		args.async.type || 'GET', // ajax request method type, default is GET
				        before:		args.async.before || function(that, xhr, settings) {},//executed before ajax request
				        success:	args.async.success || function(that, data) {},//executed after successful ajax request
				        error: 		args.async.error || function(that, xhr, data) {} //executed after error ajax request
				    },
				    cache:			args.cache || true,//if cache is set to false,popover will destroy and recreate
				    multi:			args. multi || false,//allow other popovers in page at same time
				    arrow:			args.arrow || true,//show arrow or not
				    title:			args.title || '',//the popover title, if title is set to empty string,title bar will auto hide
				    content:		function(){
						return html;
					},//content of the popover,content can be function
				    closeable:		args.closeable || false,//display close button or not
				    direction:		args.direction || '', // direction of the popover content default is ltr ,values:'rtl';
				    padding:		args.padding || true,//content padding
				    type:			'html',//content type, values:'html','iframe','async'
// 				    url:			'#'+ selector.replace('.', '') +'-pop-content-'+ name,
				    backdrop:		args.backdrop || false,//if backdrop is set to true, popover will use backdrop on open
				    dismissible:	args.dismissible || true, // if popover can be dismissed by  outside click or escape key
				    autoHide:		args.audoHide || false, // automatic hide the popover by a specified timeout, the value must be false,or a number(1000 = 1s).
				    offsetTop:		args.offsetTop || 0,  // offset the top of the popover
				    offsetLeft:		args.offsetLeft || 0,  // offset the left of the popover
// 				    onShow: 		args.onShow || function($element) {}, // callback after show
					onShow:			function(callback){ 
										Sandbox.setChildNodeListeners(webUiNode);
										if(callback){ 
											callback();
										}
									}.bind({}, args.onShow),
				    onHide: 		args.onHide || function($element) {}, // callback after hide
				});
				
			}];
			
			webUiNode.show = function(){ 
				
				console.log(selector); 
				
/*
				{
					title:'',
					content:buttonHTML,
					placement:'bottom-right',
					offsetLeft:100,
					offsetTop:10,
					position:'right',
					trigger:'manual',
					backdrop:false,
					closeable:false,
					animation:'pop'
				}
*/
				this.properties.popover.webuiPopover('show');
				
			};
			webUiNode.hide = function(){ 

				this.properties.popover.webuiPopover('hide'); 
				
			};
			
			return webUiNode;
			
		}
		
	}
	
})();