var pda_loader = (function(){
	
	return {
		
		make: function(selector, args, name){
			
			var loader = {
				selector: selector +'-loader-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};
			
			if(!args.hasOwnProperty('size')){
				args.size = 'small';
			}
			
			var size_classes = {
				'x-small': 'pda-spinner-x-small',
				'small': 'pda-spinner',
				'medium': 'pda-spinner-medium',
				'large': 'pda-spinner-large',
				'x-large': 'pda-spinner-main'
			}
			
			loader.html = 
			
				'<div class="pda-spinner '+ size_classes[args.size] +' '+ loader.selector.replace('.', '') +' '+ args.css +'">'+
					'<div>'+
						'<div>'+
						'</div>'+
					'</div>'+
				'</div>';
			
			return loader;
			
		}
		
	}
	
})();