var pda_panel = (function(){
		
	function makePanel(panelType, selector, args){
		
		if( typeof panelType == undefined || panelType == '' ){
			panelType = 'default';
		}
		if( typeof args.style === undefined ){
			args.style = '';
		}
		
		var ret = {
			selector: selector + '-panel',
			html: '<div class="ui card panel-'+ panelType +' '+ selector.replace('.', '') +'-panel '+ args.css +'" style="'+ args.style +'"></div>',
			makeNode: Sandbox.makeNode,
			appendNode: Sandbox.appendNode,
			patch: Sandbox.patchNode
		};
		
		return ret;
		
	}
	
	function makeBody(content, selector, args){
		
		if(!args.hasOwnProperty('bodyStyle')){ args.bodyStyle = ''; }
		if(!args.hasOwnProperty('bodyCss')){ args.bodyCss = ''; }
		
		var ret = {
			selector: selector + '-panel-body',
			html: '<div class="pda-panelBody '+ selector.replace('.', '') +'-panel-body '+ args.bodyCss +'" style="overflow:auto;'+ args.bodyStyle +'">'+ content +'</div>',
			makeNode: Sandbox.makeNode,
			appendNode: Sandbox.appendNode,
			patch: Sandbox.patchNode,
			empty: Sandbox.emptyNode
		};
		
		return ret;
		
	}
	
	function makeHeader(content, selector, args){
		
		var ret = {
			selector: selector + '-panel-header',
			html: '<div class="pda-panelHeader '+ args.headerCss +'"><h3 class="panel-title '+ selector.replace('.', '') +'-panel-header">'+ content +'</h3></div>',
			makeNode: Sandbox.makeNode,
			appendNode: Sandbox.appendNode,
			patch: Sandbox.patchNode
		};
		
		return ret;
		
	}
		
	return {
		
		make: function(selector, panelObj, name, container){
			
			panelObj = Sandbox.verifyDomToolArgs(panelObj, this.setup());

			var panel = makePanel(panelObj.type, selector +'-'+ name, panelObj);
			
			panel.header = makeHeader(panelObj.header, selector +'-'+ name, panelObj);
			panel.body = makeBody(panelObj.body, selector +'-'+ name, panelObj);
			panel.makeNode = Sandbox.makeNode;
			panel.appendNode = Sandbox.appendNode;
			panel.notify = Sandbox.makeNotification;
			panel.notifications = {};
			
			if(container.hasOwnProperty('properties') && container.properties.hasOwnProperty('compInstanceId')){
				panel.header.properties = {
					compInstanceId: container.properties.compInstanceId
				};
				panel.body.properties = {
					compInstanceId: container.properties.compInstanceId
				};
			}
			
			return panel;
			
		},
		
		setup: function(){
			
			return {
				type: {
					argType: 'option',
					options: ['default', 'primary', 'success', 'info', 'warning', 'danger'],
					defaultValue: 'default',
					description: 'The coloring of the panel'
				},
				header: {
					argType: 'free-input',
					description: 'The text of the header',
					defaultValue: ''
				},
				body: {
					argType: 'free-input',
					description: 'What will go inside of the body of the panel',
					defaultValue: ''
				}, css: {
					argType: 'free-input',
					description: 'Extra css classnames to apply',
					defaultValue: ''
				}, headerCss: {
					argType: 'free-input',
					description: 'Extra css classnames to apply to the header',
					defaultValue: ''
				}, bodyCss: {
					argType: 'free-input',
					description: 'Extra css classnames to apply to the body',
					defaultValue: ''
				}
			};
			
		}
						
	}
	
})();
