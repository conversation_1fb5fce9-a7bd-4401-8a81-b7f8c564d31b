var pda_button_group = (function(){
	
	return {
		
		make: function(selector, args, name){
			
			args = Sandbox.verifyDomToolArgs(args, this.setup());
			
			var buttonGroupObj = {
				selector: selector +'-button-group-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};
			
			if(args.type == 'horizontal'){
				args.type = 'pda-btn-group-x';
			}else if(args.type == 'vertical'){
				args.type = '';
			}
			
			buttonGroupObj.html = '<div class="ui buttons stackable '+ args.type +' '+ buttonGroupObj.selector.replace('.', '') +' '+ args.css +'" role="group" aria-label=""></div>';
			
			return buttonGroupObj;
			
		},
		
		setup: function(){
			
			return {
				type: {
					argType: 'options',
					options: ['horizontal', 'vertical'],
					defaultValue: 'horizontal',
					description: 'Whether the button group is horizontally or vertically displayed'
				},  css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}

			}
		}	
	}
	
})();