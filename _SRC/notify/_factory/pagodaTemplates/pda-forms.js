var bsForms = (function(){
	
	// wysiwyg funcs
	function mentionDropdown(editor, selector, editorSelector, setup){
		
		var dropdown;
		var popSelector = 'asdlkfjasldfas';
		var state = 'CLOSED';
		var searchText = '';
		var lastPlace;
		var trigger = '@';
		var triggerText = '';
		var options = setup;
		
		function getPos(el) {

            el.focus()
            lastPlace = window.getSelection().getRangeAt(0)
            var range = lastPlace.cloneRange()
            
            return range.getClientRects();

	    }
		
	    function open(data, editable, hint){
		    
		    var pos = getPos(editable)[0];
		    searchText = '';

		    var justOpened = true;
		    
		    var content = [];
		    if(Array.isArray(hint.mentions) && hint.mentions.length > 0){
			   content = _.map(hint.mentions, function(mention){
				   
				   if(typeof mention === 'string'){
					   return {
						   title:mention
					   }
				   }
				   
			   });
		    }
			
// 			var height = $(editorSelector).height();
// 			var width = $(editorSelector).width();
			
// 			var offset = $(editorSelector).offset();
			var scrollOffset = $(window).scrollTop();

			$('body').append(
				'<div id="'+ popSelector +'" class="ui search secondary segment" style="position:absolute;left:calc('+pos.x +'px - 2em);top:calc('+ (+pos.y + scrollOffset) +'px - 2em);z-index:10000;">'+
					'<div class="ui transparent input">'+
						'<input id="wysiwyg_editor_mention_prompt" class="prompt" type="text" placeholder="">'+
					'</div>'+
					'<div class="results"></div>'+
				'</div>'
			);
			
			dropdown = $('#'+ popSelector).search({
			    source:content,
			    selectFirstResult:true,
			    searchOnFocus:true,
			    minCharacters:0,
			    onSelect:function(a){

				    var paste = a;
				    if(hint.content){
					    paste = hint.content(a.title);
				    }

				    close(paste);
				    
			    },
			    onSearchQuery:function(a){

				    if(_.isEmpty(a) && !justOpened){
					    close();
				    }
				    
			    },
			    searchDelay:0,
			    onResultsClose:function(){
				    close();
			    }
			});
			
			dropdown.search('show');
			dropdown.search('set active');
// 			dropdown.search('set value', '@');
						
			$('#wysiwyg_editor_mention_prompt').focus();
			
			state = 'OPEN';
			justOpened = false;
			
			return;
		    
	    }
	    
	    function close(paste){
		    
		    dropdown.search('destroy');
		    $('#'+ popSelector).remove();
		    state = 'CLOSED';
		    
		    var selection = window.getSelection();
			selection.removeAllRanges();
			selection.addRange(lastPlace.cloneRange());
			
			if (paste) {

				if (paste.indexOf('Document_Template.Name:') !== -1) {
					
					Swal.fire({
						icon: 'question',
						text: 'Would you like to process this merge tag so you can directly edit the text, or just show the merge tag so the data can be dynamically generated?',
						confirmButtonText: 'Process Merge Tag',
						denyButtonText: 'Show Merge Tag',
						showDenyButton: true,
						showCancelButton: false
					}).then((result) => {
						
						if (result.isConfirmed) {
							
							swal.disableButtons();

							databaseConnection.obj.runSteps(
								{
									'merge': {
										template: '{{' + paste + '}}'
									}
								},
								appConfig.state.pageObject.related_object.id,
								function (merged) {

									if (merged && merged['msg'] && merged['msg']['memo']) {
										var html_string = merged['msg']['memo'];
									}

									document.execCommand("delete");
									document.execCommand("delete");
									editor.pasteHTML(html_string);

								},
								true
							);

						} else {

							editor.pasteHTML(paste + '}} ');

						}
						
					});

				} else {

					editor.pasteHTML(paste + '}} ');

				}
				
			}
		    
	    }
	    
	    function shouldOpen(triggerText){
		    
		    var ret = false;
		    _.each(options.hint, function(hint){
			    
			    if(hint.hasOwnProperty('match')){
					
				    if( (new RegExp(hint.match)).test(triggerText) ){
					    ret = hint;
				    }
				    
			    }
			    
		    });
		    
		    return ret;
		    
	    }
	    
	    function update(data, editable){
		    
		    if(typeof data.data == 'string'){
			    searchText += data.data;
			    dropdown.dropdown('set text', '@'+ searchText);
/*
			    dropdown.dropdown('setup menu', {
				    values:[{
					    value:'1',
					    text:'@Josh Gantt',
					    name:'josh'
					}, {
						value:'2',
					    text:'@Peter Mikhail',
					    name:'pete'
					}]
			    });
*/
		    }else{
			    close();
		    }
		    
	    }
		
		return {
			
			update:function(data, editable){
				
				switch(state){
					
					case 'OPEN':
					
// 						update(data, editable);
					
					break;
					
					case 'CLOSED':
						
						if(data.data){
							triggerText += data.data;
						}
						
						var hint = shouldOpen(triggerText);
						if(hint){
							
							triggerText = '';
							open(data, editable, hint);
							
						}
						
					break;
					
				}
				
			}
			
		};
		
	}
	
	// general funcs
	function isDateInputSupported(){
		
		var elem = document.createElement('input');
		elem.setAttribute('type','date');
		elem.value = 'foo';
		return (elem.type == 'date' && elem.value != 'foo');
		
	}
	
	function buildCheckHTML(option, className, selectedValues, arg){
		
		// remove null values
		_.each(selectedValues, function(val, i){
			if(val === null){
				selectedValues.splice(i, 1);
			}
		});

		var optionHTML = '';
		var disabledClass = '';
		
		if (option.isImmutable) {
			disabledClass = ' disabled';
		}
		
		if(Array.isArray(selectedValues) && selectedValues.length > 0 && selectedValues[0].constructor === Object){
			selectedValues = _.pluck(selectedValues, 'id');
		}
		
		var checkedString = '';
		if(option.checked == true || _.contains(selectedValues, option.value) || _.contains(selectedValues, parseInt(option.value)) || typeof option.value === 'number' && _.contains(selectedValues, option.value.toString())){
			
			checkedString = 'checked';
			
			if (!arg.value) {
				arg.value = true;
			}
			
		}
		
		optionHTML += 
			'<div class="ui'+ disabledClass +' checkbox '+ className +'">'+
				'<input type="checkbox" name="'+ option.name +'" value="'+ option.value +'" '+ checkedString +'>'+
				'<label>'+ option.label +'</label>'+
			'</div>'+
			'<br />';

/*
			'<select class="ui fluid dropdown" multiple="">'+
				'<option value="'+ option.value +'" '+ checkedString +'>'+ option.name +'</option>'+
			'</select>'+
			'<br />';
*/
					
		return optionHTML;
		
	}
	
	function buildFieldHTML(arg, className){
		
		if(typeof arg.type === 'undefined') arg.type = 'text';
		if(typeof arg.value === 'undefined') arg.value = '';
// 		if(typeof arg.name === 'undefined') arg.name = arg.inputLabel.toLowerCase();
		if(typeof arg.label === 'undefined') arg.label = '';
		if(typeof arg.placeholder === 'undefined') arg.placeholder = '';
		if(typeof arg.active === 'undefined') arg.active = '';
		if(typeof arg.rows === 'undefined') arg.rows = 5;
		if(typeof arg.validation === 'undefined') arg.validation = '';
		if(typeof arg.isImmutable !== 'undefined' && arg.isImmutable == true){ arg.isImmutable = 'disabled="true"' }else{ arg.isImmutable = '' };
		if(typeof arg.css === 'undefined'){ arg.css = ''; }
		if(typeof arg.style === 'undefined') arg.style = '';
		if(typeof arg.helperText === 'undefined') arg.helperText = '';
		
		var fieldBreak = '';
		var fieldDisabled = '';
		var fieldCSS = arg.fieldCSS || '';
		var fieldTag = arg.fieldTag || 'div';
		if(arg.isImmutable){
			fieldCSS += ' disabled';
		}
		if(fieldCSS != ''){
			fieldBreak = '<div class="ui clearing hidden divider"><br /><br /></div>';
		}
		var fieldHTML = '<'+ fieldTag +' class="field '+ fieldCSS +'">';
		
		if(arg.disabled){
			fieldDisabled = ' disabled="disabled" ';
		}
		
		// get pre-made args for special field types
		switch(arg.type){
			
			case 'date':
// 			arg.type = 'text';
			break;
			
			case 'state':
				arg.options = [
					{
						"name": "N/A",
						"value": "n/a"
					},{
						"name": "United States",
						"value": "us",
						"disabled": true
					},
				    {
				        "name": "Alabama",
				        "value": "AL"
				    },
				    {
				        "name": "Alaska",
				        "value": "AK"
				    },
				    {
				        "name": "American Samoa",
				        "value": "AS"
				    },
				    {
				        "name": "Arizona",
				        "value": "AZ"
				    },
				    {
				        "name": "Arkansas",
				        "value": "AR"
				    },
				    {
				        "name": "California",
				        "value": "CA"
				    },
				    {
				        "name": "Colorado",
				        "value": "CO"
				    },
				    {
				        "name": "Connecticut",
				        "value": "CT"
				    },
				    {
				        "name": "Delaware",
				        "value": "DE"
				    },
				    {
				        "name": "District Of Columbia",
				        "value": "DC"
				    },
				    {
				        "name": "Federated States Of Micronesia",
				        "value": "FM"
				    },
				    {
				        "name": "Florida",
				        "value": "FL"
				    },
				    {
				        "name": "Georgia",
				        "value": "GA"
				    },
				    {
				        "name": "Guam",
				        "value": "GU"
				    },
				    {
				        "name": "Hawaii",
				        "value": "HI"
				    },
				    {
				        "name": "Idaho",
				        "value": "ID"
				    },
				    {
				        "name": "Illinois",
				        "value": "IL"
				    },
				    {
				        "name": "Indiana",
				        "value": "IN"
				    },
				    {
				        "name": "Iowa",
				        "value": "IA"
				    },
				    {
				        "name": "Kansas",
				        "value": "KS"
				    },
				    {
				        "name": "Kentucky",
				        "value": "KY"
				    },
				    {
				        "name": "Louisiana",
				        "value": "LA"
				    },
				    {
				        "name": "Maine",
				        "value": "ME"
				    },
				    {
				        "name": "Marshall Islands",
				        "value": "MH"
				    },
				    {
				        "name": "Maryland",
				        "value": "MD"
				    },
				    {
				        "name": "Massachusetts",
				        "value": "MA"
				    },
				    {
				        "name": "Michigan",
				        "value": "MI"
				    },
				    {
				        "name": "Minnesota",
				        "value": "MN"
				    },
				    {
				        "name": "Mississippi",
				        "value": "MS"
				    },
				    {
				        "name": "Missouri",
				        "value": "MO"
				    },
				    {
				        "name": "Montana",
				        "value": "MT"
				    },
				    {
				        "name": "Nebraska",
				        "value": "NE"
				    },
				    {
				        "name": "Nevada",
				        "value": "NV"
				    },
				    {
				        "name": "New Hampshire",
				        "value": "NH"
				    },
				    {
				        "name": "New Jersey",
				        "value": "NJ"
				    },
				    {
				        "name": "New Mexico",
				        "value": "NM"
				    },
				    {
				        "name": "New York",
				        "value": "NY"
				    },
				    {
				        "name": "North Carolina",
				        "value": "NC"
				    },
				    {
				        "name": "North Dakota",
				        "value": "ND"
				    },
				    {
				        "name": "Northern Mariana Islands",
				        "value": "MP"
				    },
				    {
				        "name": "Ohio",
				        "value": "OH"
				    },
				    {
				        "name": "Oklahoma",
				        "value": "OK"
				    },
				    {
				        "name": "Oregon",
				        "value": "OR"
				    },
				    {
				        "name": "Palau",
				        "value": "PW"
				    },
				    {
				        "name": "Pennsylvania",
				        "value": "PA"
				    },
				    {
				        "name": "Puerto Rico",
				        "value": "PR"
				    },
				    {
				        "name": "Rhode Island",
				        "value": "RI"
				    },
				    {
				        "name": "South Carolina",
				        "value": "SC"
				    },
				    {
				        "name": "South Dakota",
				        "value": "SD"
				    },
				    {
				        "name": "Tennessee",
				        "value": "TN"
				    },
				    {
				        "name": "Texas",
				        "value": "TX"
				    },
				    {
				        "name": "Utah",
				        "value": "UT"
				    },
				    {
				        "name": "Vermont",
				        "value": "VT"
				    },
				    {
				        "name": "Virgin Islands",
				        "value": "VI"
				    },
				    {
				        "name": "Virginia",
				        "value": "VA"
				    },
				    {
				        "name": "Washington",
				        "value": "WA"
				    },
				    {
				        "name": "West Virginia",
				        "value": "WV"
				    },
				    {
				        "name": "Wisconsin",
				        "value": "WI"
				    },
				    {
				        "name": "Wyoming",
				        "value": "WY"
				    },
				    {
						"name": "<strong>Canadian Provinces</strong>",
						"value": "ca",
						"disabled": true
					},
					{
						"name": "Alberta",
						"value": "AB"
					},
					{
						"name": "British Columbia",
						"value": "BC"
					},
					{
						"name": "Manitoba",
						"value": "MB"
					},
					{
						"name": "New Brunswick",
						"value": "NB"
					},
					{
						"name": "Newfoundland and Labrador",
						"value": "NL"
					},
					{
						"name": "Nova Scotia",
						"value": "NS"
					},
					{
						"name": "Northwest Territories",
						"value": "NT"
					},
					{
						"name": "Nunavut",
						"value": "NU"
					},
					{
						"name": "Ontario",
						"value": "ON"
					},
					{
						"name": "Ottawa",
						"value": "OTT"	
					},
					{
						"name": "Prince Edward Island",
						"value": "PE"
					},
					{
						"name": "Québec",
						"value": "QC"
					},
					{
						"name": "Saskatchewan",
						"value": "SK"
					},
					{
						"name": "Yukon",
						"value": "YT"
					}
				];
				arg.type = 'select';
				break;
			
			default:
				break;
		}
		
		if((arg.hasOwnProperty('label') && arg.label !== '') && (arg.type !== 'radio' && arg.type !== 'section' && arg.type !== 'hidden' && fieldCSS == '')){
			fieldHTML += '<label>'+ arg.label +'</label>'+fieldBreak;
		}

		if (!_.isEmpty(arg.helperText)) {
			fieldHTML += '<small class="text-muted">'+ arg.helperText +'</small>';
		}
		
		// get html
		switch(arg.type){
			
			case 'check':
			
				if (
					arg.hasOwnProperty('helperText')
					&& !_.isEmpty(arg.helperText)
				) {
					
					fieldHTML += '<br />';
					
				}
				
				_.each(arg.options, function(option){
					fieldHTML += buildCheckHTML(option, className, arg.value, arg);
				});
				
				break;
			
			case 'checkbox':
								
				fieldHTML = getMultiSelectHTML(arg, fieldHTML, className);			
				
				break;
				
			case 'color':
				fieldHTML += '<div class="'+ className +' input-group colorpicker-component '+ arg.css +'"><input '+ arg.isImmutable +' type="'+ arg.type +'" name="'+ arg.name +'" data-id="'+ arg.validation +'" value="'+ arg.value +'" data-label="'+ arg.label +'" placeholder="'+ arg.placeholder +'" '+ arg.active +' class="pda-form pda-form-fullWidth"><span class="input-group-addon"><i></i></span></div>';
				break;
			
			case 'date':
			case 'time':
			
				var useFieldType = 'date';
				
				if(isDateInputSupported()){
					useFieldType = 'text';
				}
				var valueString = '';
				var dateFormat = '';
				if(typeof arg.dateFormat === 'undefined'){
					dateFormat = 'M/D/YYYY, h:mm a';
				}else{
					dateFormat = arg.dateFormat;
				}

				if(moment.isMoment(arg.value)){
					
					var momentObj = moment(arg.value, dateFormat);
					
					// check for daylight savings time shift
					var dt = momentObj.format('YYYY-MM-DD HH:mm:ss');
					var nowTime = moment().format('YYYY-MM-DD HH:mm:ss');
					
					// If is daylight savings, and time selected outside of dst, 
					// shift an hour forward.
					if (
						!moment.tz(dt, moment.tz.guess()).isDST()
						&& moment.tz(nowTime, moment.tz.guess()).isDST()
					) {
						
						momentObj.add(1, 'hour');
						
					// If its not daylight savings, and time selected is in dst, 
					// shift back an hour.
					} else if (
						moment.tz(dt, moment.tz.guess()).isDST()
						&& !moment.tz(nowTime, moment.tz.guess()).isDST()
					) {
						
						momentObj.subtract(1, 'hour');
						
					}
					
					// Adjust to take into account the UTC offset
					var utcOffset = new Date().getTimezoneOffset() / 60;
					valueString = momentObj.add(utcOffset, 'hours').format(dateFormat);
					
				}
				
				var idName = '';
				if(arg.id){
					idName = arg.id;
				}

				fieldHTML += 

					'<div class="ui calendar" id="example1">'+
						'<div class="ui input left icon '+ className +' '+ arg.css +'">'+
							'<i class="calendar icon"></i>'+
							'<input type="text" value="'+ valueString +'" placeholder="'+ arg.placeholder +'"'+ fieldDisabled +' id="'+ idName +'" name="'+ arg.name +'" autocomplete="off">'+
						'</div>'+
					'</div>';

// 				fieldHTML += '<input '+ arg.isImmutable +' type="'+ useFieldType +'" name="'+ arg.name +'" data-id="'+ arg.validation +'" data-label="'+ arg.label +'" placeholder="'+ arg.placeholder +'" '+ arg.active +' class="ui calendar '+ className +' '+ arg.css +'">';
				break;
				
			case 'file-upload':
				fieldHTML += '<input id="'+ className +'" type="file" name="'+ arg.name +'" data-id="'+ arg.validation +'" value="'+ arg.value +'" placeholder="'+ arg.placeholder +'" '+ arg.active +' class=" pda-form pda-form-fullWidth '+ className +' '+ arg.css +'" style="'+ arg.style +'">';
				break;

			case 'file-upload-multiple':
				fieldHTML += '<input id="'+ className +'" type="file" name="'+ arg.name +'" data-id="'+ arg.validation +'" value="'+ arg.value +'" placeholder="'+ arg.placeholder +'" '+ arg.active +' class=" pda-form pda-form-fullWidth '+ className +' '+ arg.css +'" style="'+ arg.style +'" multiple>';
				break;
				
			case 'radio':
			
				fieldHTML = '<div class="'+ className +'">';
				
				if(arg.label && arg.label !== ''){
					fieldHTML += '<label>'+ arg.label +'</label>';
				}
				
				// stack radio buttons if vertical stacking is indicated by args
				var vertHTML = '';
				if(arg.style == 'vertical'){
					vertHTML = '<div class="clearfix"></div>';
				}
				
				_.each(arg.options, function(option){

					var radioCss = '',
						preselected = '';
						
					if(option.highlighted){
						radioCss = 'text-info bg-info';
					}else{
						radioCss = '';
					}
					if(arg.value == option.value || arg.value.constructor === Object && option.value == arg.value.id){
						preselected = 'checked="checked"';
					}
					
					if(option.selected == true){
						fieldHTML += '<label class="radio-inline '+ radioCss +' '+ arg.css +'"><input '+ preselected +' '+ arg.isImmutable +' type="radio" checked="checked" value="'+ option.value +'" name="'+ arg.name +'"> '+ option.name +'</label>'+ vertHTML;
					}else{
						fieldHTML += '<label class="radio-inline '+ radioCss +' '+ arg.css +'"><input '+ preselected +' '+ arg.isImmutable +' type="radio" value="'+ option.value +'" name="'+ arg.name +'"> '+ option.name +'</label>'+ vertHTML;
					}
					
				});
				
				fieldHTML += '</div>';
				
				break;
				
			case 'section':
				fieldHTML = '<br /><div class="ui card fluid '+ arg.css +'"><br /><div class="small-container"><h4 class="ui dividing header"><br />'+ arg.label +'</h4><div class="ui form '+ className +'"></div><br /><br /></div></div>';
				break;
			
			case 'string':
			case 'text':
			case 'usd':

				var inputList = '';
				if(typeof arg.inputList != 'undefined' ){
					
					inputList = '<ul class="hidden" id="'+ className +'-list">';
					_.each(arg.inputList, function(listItem){
						inputList += '<li>'+ listItem +'</li>';
					});
					inputList += '</ul>';
				}
				
				var inputTypeString = arg.type;
				
				if(arg.type == 'usd'){
					inputTypeString = 'text';
				}
				
				// Fixes double-quote issue
				if (arg.hasOwnProperty('value')) {
					if (arg.value && typeof arg.value === 'string') {
						arg.value = arg.value.replace(/"/g, '&quot;');
					}
				}
				
				fieldHTML += '<input '+ arg.isImmutable +' id="'+ className +'" type="'+ inputTypeString +'" name="'+ arg.name +'" data-id="'+ arg.validation +'" value="'+ arg.value +'" data-label="'+ arg.label +'" placeholder="'+ arg.placeholder +'" '+ arg.active +' class="pda-form pda-form-fullWidth '+ className +' '+ arg.css +'" style="'+ arg.style +'">'+ inputList;
				break;
				
			case 'textbox':

				if(arg.tinymce || arg.wysiwyg){
					
					if(arg.header === false){
						arg.header = '';
					}else{
						arg.header = '<div class="ui basic segment"><h4 class="ui header">'+ arg.label +'</h4></div>';
					}
					
					if(!arg.hasOwnProperty('parentCSS')){
						arg.parentCSS = 'ui padded basic segments';
					}
					
					if(!arg.hasOwnProperty('parentStyle')){
						arg.parentStyle = '';
					}
					
					if(!arg.hasOwnProperty('bodyColor')){
						arg.bodyColor = 'orange';
					}
					
					if(!arg.hasOwnProperty('childCSS')){
						arg.childCSS = 'ui '+ arg.bodyColor +' basic segment editable';
					}
					
					fieldHTML = '<div class="'+ arg.parentCSS +'" style="'+ arg.parentStyle +'">'+ arg.header +'<div class="'+ arg.childCSS +' '+ className +' '+ arg.css +'" style="'+ arg.style +'">'+ arg.value +'</div></div>';
					
				}else{
					fieldHTML += '<textarea '+ arg.isImmutable +' rows="'+ arg.rows +'" name="'+ arg.name +'" data-id="'+ arg.validation +'" data-label="'+ arg.label +'" placeholder="' + arg.placeholder + '" ' + arg.active +' class="pda-textarea pda-form-fullWidth '+ className +' '+ arg.css +'" style="'+ arg.style +'">'+ arg.value +'</textarea>';
				}

				break;	
			
			case 'select':

				var optionHTML = buildOptionHTML('None', '', false, '', {disabled:false});
					optionVal = '';

				if(typeof arg.value === 'undefined' || arg.value == null){
					arg.value = '';
				}else if(arg.value.constructor === Object){
					optionVal = arg.value.id;
				}else{
					optionVal = arg.value;
				}
				
				_.each(arg.options, function(option) {
					
					if (option.value == optionVal) {
						option.selected = true;
					}
					optionHTML += buildOptionHTML(option.name, option.value, option.selected, arg.value, option);
					
				});

				fieldHTML += '<select '+ arg.isImmutable +' name="'+ arg.name +'" data-id="'+ arg.validation +'" type="select" value="'+ optionVal +'" data-label="'+ arg.label +'" class="'+ className +' ui fluid search clearable selection dropdown ' + arg.css + '">'+ optionHTML +'</select>';
				
				break;
				
			case 'number':
			
				if(typeof arg.min === 'undefined'){
					arg.min = '';
				}else{
					arg.min = 'min="'+ arg.min +'"';
				}
				
				if(typeof arg.max === 'undefined'){
					arg.max = '';
				}else{
					arg.max = 'max="'+ arg.max +'"';
				}
				
				if(typeof arg.step === 'undefined'){
					arg.step = '';
				}else{
					arg.step = 'step="'+ arg.step +'"';
				}
				
				fieldHTML += '<input '+ arg.isImmutable +' class="pda-form pda-form-fullWidth '+ className +' '+ arg.css +'" data-id="'+ arg.validation +'" type="number" name="'+ arg.name +'" '+ arg.min +' '+ arg.max +' '+ arg.step +' value="'+ arg.value +'" style="'+ arg.style +'">';
				break;
				
			case 'hidden':
				fieldHTML = '<input type="'+ arg.type +'" name="'+ arg.name +'" value="'+ arg.value +'" data-label="'+ arg.label +'" class=" '+ className +' '+ arg.css +'">';
				break;
				
			default:
				
				var inputTypeString = arg.type;
				
				if(arg.type == 'usd'){
					inputTypeString = 'text';
				}
				
				fieldHTML += '<input '+ arg.isImmutable +' type="'+ inputTypeString +'" name="'+ arg.name +'" data-id="'+ arg.validation +'" value="'+ arg.value +'" data-label="'+ arg.label +'" placeholder="'+ arg.placeholder +'" '+ arg.active +' class="pda-form pda-form-fullWidth '+ className +' '+ arg.css +'" style="'+ arg.style +'">';
				break;

		}
		
		if(!arg.noSpace && arg.type !== 'hidden'){
			fieldHTML += '</'+ fieldTag +'>';
		}
		
		return fieldHTML;

	}
	
	function buildFormHTML(formClass){
		
		return '<form onsubmit="return false;" class="'+ formClass +'" enctype="multipart/form-data"></form>';
		
	}
	
	function buildListener(selector, arg, form, formSelector){

		var listeners = [];

		switch(arg.type){
			
			case 'check':
				listeners.push(function(){
					$(selector).checkbox({
						onChecked:function(){
							arg.value = true;
							
							if (typeof arg.onChange === 'function') {
								arg.onChange(true);
							}
							
						},
						onUnchecked:function(){
							arg.value = false;
							
							if (typeof arg.onChange === 'function') {
								arg.onChange(false);
							}
							
						}
					});
				});
				break;
			
			case 'checkbox':
				listeners.push(function(){
					
					// update select all button on change of current selections
					$(selector).dropdown({
						onChange:function(value, text, $selectedItem){
							
							if(arg.options.length == value.length){
								$(selector+'-selectAll').checkbox('check');
							}else{
								$(selector+'-selectAll').checkbox('uncheck');
							}
							
							if(arg.onChange){
								arg.onChange(value);
							}else if(arg.change){
								arg.change(value);
							}
															
						},
						onLabelCreate:arg.onLabelCreate
					});
					
					// when select all is checked, update selections list
					if(!(arg.selectAll == false)){
						
						var checked = false;

						if(arg.options.length == _.where(arg.options, {selected:true}).length || arg.options.length == _.where(arg.options, {checked:true}).length){						
							checked = true;
						}
						
						$(selector+'-selectAll').checkbox({
							onChecked:function(){
														
								$(selector).dropdown('set selected', _.map(arg.options, function(opt){
									return opt.value.toString();
								}));
								
							},
							onUnchecked:function(){

								if($(selector).dropdown('get value') && ($(selector).dropdown('get value').length == 0 || $(selector).dropdown('get value').length == arg.options.length)){
										
									$(selector).dropdown('clear');
									
								}
																				
							}
						});
						
						if(checked === true){
							$(selector+'-selectAll').checkbox('check');
						}
						
					}
				
				});
				break;
			
			case 'datetime':
			case 'date':
			
			listeners.push(function(selector){

				if(typeof arg.dateFormat === 'undefined'){
					dateTimeFormat = 'MM/DD/YYYY';
				}else{
					dateTimeFormat = arg.dateFormat;
				}

				// split date formats into date and time formats
				var dateFormat = '';
				var timeFormat = '';
				if(dateTimeFormat.indexOf('h')){
					dateFormat = dateTimeFormat.split('h')[0];
					timeFormat = 'h'+ dateTimeFormat.split('h')[1];
				}else if(dateTimeFormat.indexOf('H')){
					dateFormat = dateTimeFormat.split('H')[0];
					timeFormat = 'H'+ dateTimeFormat.split('H')[1];
				}else{
					dateFormat = dateTimeFormat;
				}

				if(arg.setup){
					var setup = arg.setup;
				}else{
					
					var setup = {
						formatter:{
							date: function(date, settings){

								var ret = moment(date, 'ddd MMM DD YYYY HH:mm:ss ZZ');
								return ret.format(dateFormat);
	
							}
						},
						onChange: function(date, text, mode){

							this.value = date;
							
						}.bind(arg)
					};
					
					var dateType = 'datetime';
					if(arg.dateType){
						setup.type = arg.dateType;
						
						if(setup.type === 'datetime'){
							timeFormat = '';
						}
						
					}else{
						setup.type = 'date';
					}

					if(arg.value){

						if(moment.isMoment(arg.value) && arg.value.isValid()){
							
							setup.initialDate = arg.value.toDate();
							
						}else if(typeof arg.value === 'string'){
							
							setup.initialDate = moment(arg.value).toDate();
							
						}
						
					}

					if(!_.isEmpty(timeFormat)){
						setup.formatter.time = function(date, settings, forCalendar){
							var ret = moment(date);
							return ret.format(timeFormat);
							
						}
					}
					
					if(arg.hasOwnProperty('min')){
						setup.minDate = arg.min.toDate();
					}
					if(arg.hasOwnProperty('max')){
						setup.maxDate = arg.max.toDate();
					}
					
				}

				arg.uiModule = $(selector).calendar(setup);

				if(setup.initialDate){
					arg.uiModule = $(selector).calendar('set date', setup.initialDate);
				}
				
			});
			break;
			
			case 'select':
			listeners.push(function(){

				if ((!arg.value || arg.value === 'null') && arg.options && arg.options.length > 0) {
					
					arg.value = _.findWhere(arg.options, {selected: true});
					
					if (arg.value) {
						arg.value = arg.value.value;
					} else {
						arg.value = _.filter(arg.options, function(option){ return (option.disabled !== true); })[0].value;
					}
					
				}

				if(typeof arg.change !== 'undefined'){
						
					$(selector).on('change', function(event){

						switch(typeof arg.change){
							
							case 'function':
									
									arg.value = $(selector).dropdown('get value');
									var newVal = arg.change(form, $(selector).dropdown('get value'));
									bsForms.make(formSelector, newVal);
																		
								break;
								
							default:
							
								Factory.triggerEvent(arg.change, arg.module);
							
						}					
						
					});
				
				}else{
					
					$(selector).dropdown({
						onChange: function(value, text, $choice){
							
							if ( !_.isEmpty(value) ) {
								arg.value = value;
							}
							
							if (typeof arg.onChange === 'function') {
								arg.onChange(value);
							}
														
						},
						value: arg.value
					});
					
				}
				
			});
			break;
			
			case 'time':
			listeners.push(function(){
				
				var defaultDate,
					dateFormat;
					
				if(typeof arg.value !== 'undefined'){
					defaultDate = arg.value;
				}else{
					defaultDate = moment().startOf('day').add(10, 'hours');
				}
				
				if(typeof arg.dateFormat === 'undefined'){
					dateFormat = 'h:mma';
				}else{
					dateFormat = arg.dateFormat;
				}

				var dp = $(selector).datetimepicker({
					defaultDate: defaultDate,
					format:  dateFormat,
					sideBySide: true
				});
				
				if(typeof arg.change !== 'undefined'){
					
					if(typeof arg.change !== 'undefined'){

						$(selector).on('dp.change', function(event){
	
							switch(typeof arg.change){
								
								case 'function':
									
										var newVal = arg.change(form, $(selector).val());
										
										bsForms.make(formSelector, newVal);
																			
									break;
									
								default:
								
									Factory.triggerEvent(arg.change, arg.module);
								
							}					
							
						});
					
					}
				}

			});
			break;
			
			case 'file-upload':
			
			listeners.push(function(){
				
				$(selector).on('change', function(e){
					
					var fileName = $(this).val().split('\\')[$(this).val().split('\\').length - 1].split('.')[0];
					
					$(selector +'-fileName').val(fileName);
					
				});
				
			});
			
			break;
			
			case 'text':
			
			if(typeof arg.inputList != 'undefined' || typeof arg.change !== 'undefined'){
				
				listeners.push(function(){
					
					if(typeof arg.inputList != 'undefined'){
						
						var input = document.getElementById(selector.replace('.', ''));
						
						new Awesomplete(input, {list: "#"+ selector.replace('.', '') +'-list'});
												
					}

					if(typeof arg.change !== 'undefined'){
						
						$(selector).on('change paste', function(event){

							switch(typeof arg.change){
								
								case 'function':
								
										var fieldContent;
										var eventArgs = [form, $(selector).val()];
										
										if(event.type == 'paste'){

											fieldContent = event.originalEvent.clipboardData.getData('text');
											eventArgs.push(fieldContent);
										}
									
										var newVal = arg.change.apply(null, eventArgs);
																													
									break;
									
								default:
								
									Factory.triggerEvent(arg.change, arg.module);
								
							}					
							
						});
					
					}
					
				});
			}
			break;
			
			case 'textbox':
			if ( arg.tinymce || arg.wysiwyg ) {
				
				listeners.push(function(selector) {
					
					// ================================== //
					// ===== COLOR PICKER EXTENSION ===== //
					// ================================== //

					var MediumEditorColorPicker = MediumEditor.extensions.button.extend({
					    name: "colorPicker",
					    action: "applyTextColor",
					    aria: "Color Picker",
					    init: function() {
					        
					        this.button = this.document.createElement('button');
					        this.button.classList.add('medium-editor-action');
					        this.button.classList.add('medium-editor-action-colorPicker');

					    }
					    
					});

					// ================================== //
					// ===== TEXT SIZE EXTENSION ===== //
					// ================================== //

					var MediumEditorFontSize = MediumEditor.extensions.button.extend({
					    name: "fontSize",
					    action: "applyFontSize",
					    aria: "Font Size",
					    init: function() {
					        
					        this.button = this.document.createElement('button');
					        this.button.classList.add('medium-editor-action');
					        this.button.classList.add('medium-editor-action-fontSize');

					    }
					    
					});
					
					// ================================== //
					// ================================== //

					var toolbar = !arg.wysiwyg.toolbar ? false : {
						buttons: [
							'bold', 'italic', 'underline', 'strikethrough', 'colorPicker', 
							'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
							'anchor', 'quote', 'justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull',
							'orderedlist', 'unorderedlist',
							'indent', 'outdent',
							'html',
							'removeFormat',
							'table'
						]
					}

					var editor = new MediumEditor(selector, {
						toolbar: toolbar,
						extensions: {
							table: new MediumEditorTable(),
							colorPicker: new MediumEditorColorPicker(),
							fontSize: new MediumEditorFontSize()
						},
						buttonLabels: 'fontawesome'
					});

					// Add Color Picker Extension
					editor.subscribe('showToolbar', function() {
						
						var ui = domTool.make('.medium-editor-action-colorPicker');
						        
						$(ui.selector).css('padding', '0px');
						
						var dropdown = ui.makeNode('dropdown', 'div', {
							css:'ui fluid dropdown',
							style: 'color: #ffffff !important; padding:15px;',
							text: '<i class="palette icon"></i>',
							listener:{
								type:'dropdown'
							}
						});

						var menu = dropdown.makeNode('menu', 'div', {
							css: 'menu'
						});
						
						var item = menu.makeNode('item', 'div', {
							css: 'item',
							style: 'padding:0 !important;'
						});
						
						var obj = {};

						Factory.triggerEvent({
							type: 'view-field',
							data: {
								type: 'color-picker',
								property: 'color',
								obj: obj,
								options: {
									defaultColor: 'none',
									allowAdditions: true,
									returnAsRGB: true,
									isOpen: false,
									saveSelection: true,
									edit: true,
									editing: true,
									commitUpdates: false,
									onUpdate(obj) {
										
										function restoreSelection(range) {
											if (range) {
												if (window.getSelection) {
													var sel = window.getSelection();
													sel.removeAllRanges();
													sel.addRange(range);
												} else if (document.selection && range.select) {
													range.select();
												}
											}
										}
										
										// Restore selection
										restoreSelection(obj.selection);
										
										// Colorize text
										if ( obj.color == 'none' ) {
											document.execCommand("removeFormat", false, "ForeColor");
										} else {
											document.execCommand('styleWithCSS', false, true);
											document.execCommand("ForeColor", false, obj.color);
										}
										
										// Hide dropdown
										$(dropdown.selector).dropdown('hide');

									}
								},
								ui: item.makeNode('field', 'div', {})
							}
						});

						ui.build();

					});

					// Adjust the paragraph icon
					$('.medium-editor-action-pre').html('<i class="paragraph icon"><i>');

					var mentionPop = null;
					if( arg.wysiwyg && arg.wysiwyg.hint ) {
						mentionPop = mentionDropdown(editor, selector.replace('.', '#') +'-dropdown', selector, arg.wysiwyg);
					}
					
					if (mentionPop) {

						// Listen for changes in content for the auto-suggestor and 
						// to pass updates upstream through the 'onChange' callback.
						editor.subscribe('editableInput', function(data, editable){
							
							mentionPop.update(data, editable);
							if (typeof arg.onChange === 'function') {

								arg.onChange(editor.getContent());
								
							}

						});

					}

					arg.editor = editor;

					// Adjust the size and color of the editor
					$('.medium-editor-toolbar').css({'max-width': '575px', 'background-color': '#2a2e37'});
										
				});
			}
			break;
			
			case 'color':
			listeners.push(function(){
				
				$(selector).colorpicker();
				
			});
			break;
			
			case 'usd':
			
			listeners.push(function(){
				
				if($(selector).length > 0){
					
					var currentVal = parseInt($(selector).val().replace(/[^0-9-]/g, '').replace(/(?!^)-/g, ''));

					if(isNaN(currentVal)){
						currentVal = 0;
					}
					
					currentVal = (currentVal/100).toFixed(2);
					currentVal = '$ '+ currentVal;
					$(selector).val(currentVal);
					
					$(selector).on('change input', function(e){

						if($(this).val() == '-'){
							
							//$(this).val(currentVal);
							
						}else{
							
							var currentVal = parseInt($(this).val().replace(/[^0-9-]/g, '').replace(/(?!^)-/g, ''));
							
							if(isNaN(currentVal)){
								currentVal = 0;
							}
							
							currentVal = (currentVal/100).toFixed(2);
							currentVal = '$ '+ currentVal;
							$(this).val(currentVal);
							
						}
						
					});
					
					// ! Updating USD form to include the Change Property functionality

					if(typeof arg.change !== 'undefined'){
						
						// Listen for the Enter key -- keyCode 13
						// Listen for when the user focuses out -- focusout
						
						$(selector).on('focusout', function(event){
							
							switch(typeof arg.change){
									
								case 'function':
									
										var newVal = arg.change(form, $(selector).val());
										
										bsForms.make(formSelector, newVal);
																			
									break;
									
								default:
								
									Factory.triggerEvent(arg.change, arg.module);
								
							}
							
						});
						
						$(selector).on('keypress', function(event){
							
							if(event.keyCode == 13){
								
								switch(typeof arg.change){
									
									case 'function':
										
											var newVal = arg.change(form, $(selector).val());
											
											bsForms.make(formSelector, newVal);
																				
										break;
										
									default:
									
										Factory.triggerEvent(arg.change, arg.module);
									
								}
								
							}
							
						});
					}
				
				}
				
			});
			
			break;
			
			default:
				
				listeners.push(function(){
					
					if(typeof arg.change !== 'undefined'){

						$(selector).on('change', function(event){
	
							switch(typeof arg.change){
								
								case 'function':

										var newVal = arg.change(form, $(selector).val());
										
										bsForms.make(formSelector, newVal);
																			
									break;
									
								default:
								
									Factory.triggerEvent(arg.change, arg.module);
								
							}					
							
						});
					
					}
				
				});
		}
		
		return listeners;
		
	}
	
	function buildOptionHTML(name, value, selected, selectedValue, option){
		
		var disabledText = '';
		if(option.disabled){
			disabledText = 'disabled';
		}
		
		if(selected === true || value === selectedValue){
			selected = 'selected';
		}
		
		if(typeof selected === 'undefined'){
			selected = '';
		}
		
		return '<option value="'+ value +'" '+ selected +' '+ disabledText +'>'+ name +'</option>';
		
	}
	
	function getMultiSelectHTML(arg, fieldHTML, className){
		
		var optionHTML = '';
				
		_.each(arg.options, function(option){

			if (
				option.selected === true 
				|| option.checked === true 
				|| _.contains(arg.value, option.value)
			) {
				option.selected = 'selected="true"';
			} else if (
				Array.isArray(arg.value) 
				&& arg.value.length > 0 
				&& arg.value[0] != null
				&& arg.value[0].constructor === Object
			) {
				
				if(_.contains(_.pluck(arg.value, 'id'), parseInt(option.value))){
					option.selected = 'selected="true"';
				}
				
			}else{
				option.selected = '';
			}
			
			optionHTML += '<option value="'+ option.value +'" '+ option.selected +'>'+ option.label +'</option>';
								
		});
		
		if(arg.selectAll == true || arg.selectAll == undefined){					
			fieldHTML += '<div class="filter"><div class="ui slider checkbox '+ className +'-selectAll"><input type="checkbox" name="selectAll"><label>Select All</label></div></div>';
		}
		var search = '';
		if(arg.search){
			search = ' search';
		}
						
		fieldHTML += '<select name="'+ arg.name +'" class="ui dropdown'+ search +' '+ className +' '+ arg.css +'" multiple="">'+ optionHTML +'</select>';
		
		return fieldHTML;
		
	}
	
	function updateField(node, newValue){
		
		var updatingValues = false;
		if(typeof newValue === 'object'){
			
			_.each(newValue, function(val, key){
				node.args[key] = val;
			});
			
		
		// if not an object, default to replace value contained in field
		}else{
			node.args.value = newValue;
			updatingValues = true;
		}
		
		if(newValue.type === 'checkbox' && !(node.args.selectAll)){

			if(newValue.options){

				node.args.options = newValue.options;

				$(node.selector).dropdown('change values', _.map(node.args.options, function(option){
					
					var name = option.label || option.name;
					
					return {
						value:option.value,
						name:name,
						text:name
					};
					
				}));
				
				return;
								
			}else if(updatingValues){
				
				var newVals = [];
				if(Array.isArray(node.args.value)){
					newVals = node.args.value;
				}else{
					_.each(node.args.options, function(option){
						if(option.checked){
							newVals.push(option.value);
						}
					});
				}
				$(node.selector).dropdown('set exactly', newVals);

			}
			
			node.html = buildFieldHTML(node.args, node.selector.replace('.', ''));

			// Added on 9/19/18
			$( node.selector ).first().prev("label").remove();
			$( node.selector ).first().prev(".filter").remove();
			$( node.selector ).first().next(".clearfix").remove();
			$( node.html ).insertAfter( node.selector );
			$( node.selector ).first().remove();
			
			var listeners = buildListener(node.selector, node.args);
			
			for(i in listeners){
				
				listeners[i](node.selector);
				
			}
			
		}else{
			
			node.html = buildFieldHTML(node.args, node.selector.replace('.', ''));
			
			$( node.selector ).first().prev("label").remove();
			
			// -- Added on 9/19/18 -- //
			$( node.selector ).first().prev(".filter").remove();
			$( node.selector ).first().prev("label").remove();
			// ---------------------- //
			
			$( node.selector ).first().next(".clearfix").remove();
			$( node.html ).insertAfter( node.selector );
			$( node.selector ).first().remove();

			var listeners = buildListener(node.selector, node.args);
			for(i in listeners){
				listeners[i](node.selector);
			}
			
			// if new options set and no new value explicitely set, set value
			// to first in the list
			if (
				node 
				&& node.args 
				&& node.args.value
				&& newValue
				&& Array.isArray(newValue.options)
				&& newValue.options.length > 0
				&& !newValue.value
			) {
				node.args.value = _.first(newValue.options).value;
			}
			
		}
		
	}
	
	function validateForm(formSelector, fields){
		
		var formData = {fields: {}},
			formCompleted = true,
			formValid = true;

		_.each(fields, function(field){

			if($(formSelector +' input[name='+ field.name +']').attr('type') == 'checkbox'){
				
				formData.fields[field.name] = {
					value: [],
					type: 'checkbox',
				};
				
				_.each(fields, function(checkboxField){
					
					if(checkboxField.name == field.name){
						
						formData.fields[field.name].value.push(checkboxField.value);

					}
									
				});
				
			}else if($(formSelector +' select[name='+ field.name +']').prop('type') == 'select-multiple'){
				
				if(!formData.fields[field.name]){
					
					formData.fields[field.name] = {};
					
					formData.fields[field.name].value = [];
					
				}
				
				formData.fields[field.name].value.push(field.value);
				
				formData.fields[field.name].type = 'checkbox';
				
			}else{

				formData.fields[field.name] = {
					value: field.value,
					type: $(formSelector +' input[name='+ field.name +']').attr('type')
				};
								
				switch(formData.fields[field.name].type){
					
					case 'checkbox':
						
						break;
					
					case 'email':
						if(formData.fields[field.name].value.indexOf('@') === -1){
							formData.fields[field.name].valid = false;
							formData.fields[field.name].errorMsg = "'"+ $(formSelector +' input[name='+ field.name +']').attr('data-label') +"'"+' must include \'@\'';
							formValid = false;
						}else{
							formData.fields[field.name].valid = true;
						}
						break;
						
					case 'number':
						if($(formSelector +' input[name='+ field.name +']').attr('data-id') == 'positive' && formData.fields[field.name].value <= 0){
							formData.fields[field.name].valid = false;
							formData.fields[field.name].errorMsg = "'"+ $(formSelector +' input[name='+ field.name +']').attr('data-label') +"'"+' must be positive';
							formValid = false;
						}else{
							formData.fields[field.name].valid = true;
						}
						break;
						
					case 'text':
						if($(formSelector +' input[name='+ field.name +']').attr('data-id') == 'positive-number' && formData.fields[field.name].value <= 0){
							formData.fields[field.name].valid = false;
							formData.fields[field.name].errorMsg = "'"+ $(formSelector +' input[name='+ field.name +']').attr('data-label') +"'"+' must be a positive number';
							formValid = false;
						}
						break;
						
					default:
						formData.fields[field.name].valid = true;
						break;
						
				}
				
			}
			
			// update if form is incomplete
			if(field.value == '' && $(formSelector +' input[name='+ field.name +']').attr('data-id') != 'unecessary-to-complete'){
				
				formCompleted = false;
			}
			
		});
		
		formData.completed = formCompleted;
		formData.valid = formValid;
		
		return formData;
		
	}
	
	return {
		
		make: function(selector, formArgs){
			
			formObj = {
				selector: selector +'-form',
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				process: this.processSelf
			};
			
			var formCSS = 'ui form';

			if(formArgs !== undefined && formArgs.hasOwnProperty('customCSS')){
				formCSS = formArgs.customCSS;
				delete formArgs.customCSS;
			}
			
			formObj.html = buildFormHTML(formCSS+' '+formObj.selector.replace('.', ''));
			
			// create generated field form args
			var tempArgs = {};
			_.each(formArgs, function(arg){
				
				tempArgs[arg.name] = arg;
				if(arg.type == 'file-upload'){
					
					// if objectType, objectId, and fileType are not provided, provide default values
					if(!arg.hasOwnProperty('objectType')){
						arg.objectType = 'root';
					}
					if(!arg.hasOwnProperty('objectId')){
						arg.objectId = 0;
					}
					if(!arg.hasOwnProperty('fileType')){
						arg.fileType = 'N/A';
					}
					if(!arg.hasOwnProperty('parent')){
						arg.parent = 0;
					}
					
					var	fileObjectTypeField = {
							type: 'hidden',
							name: arg.name +'-objectType',
							value: arg.objectType
						},
						fileObjectIdField = {
							type: 'hidden',
							name: arg.name +'-objectId',
							value: arg.objectId
						},
						fileTypeField = {
							type: 'hidden',
							name: arg.name +'-fileType',
							value: arg.fileType
						}, fileParentField = {
							type: 'hidden',
							name: arg.name +'-parent',
							value: arg.parent
						}, fileNameField = {
							type: 'hidden',
							name: arg.name +'-fileName',
							label: 'File Name'
						};
						
					if(arg.rename){
						fileNameField.type = 'text';
					}
					
					if(arg.fileIdPropertyName){
						/*
var fileIdPropertyField = {
							type: 'hidden',
							name: '',
							
						};
*/
					}
					
					if(Array.isArray(formArgs)){
						
						tempArgs.push(fileObjectTypeField);
						tempArgs.push(fileObjectIdField);
						tempArgs.push(fileObjectTypeField);
						tempArgs.push(fileParentField);
						tempArgs.push(fileNameField);
						
					}else{
						
						tempArgs[arg.name +'-objectType'] = fileObjectTypeField;
						tempArgs[arg.name +'-objectId'] = fileObjectIdField;
						tempArgs[arg.name +'-fileType'] = fileTypeField;
						tempArgs[arg.name +'-parent'] = fileParentField;
						tempArgs[arg.name +'-fileName'] = fileNameField;
						
					}
					
				}
				
			});
			
			formArgs = tempArgs;

			_.each(formArgs, function(arg){

				formObj[arg.name] = {
					selector: formObj.selector +'-'+ arg.name,
					html: buildFieldHTML(arg, formObj.selector.replace('.', '') +'-'+ arg.name),
					appendNode: Sandbox.appendNode,
					makeNode: Sandbox.makeNode,
					patch: Sandbox.patchNode,
					notify: Sandbox.makeNotification,
					notifications: {},
					listeners: buildListener(formObj.selector +'-'+ arg.name, arg, formArgs, selector),
					update: function(newValue){updateField(this, newValue);},
					args: arg,
					empty: Sandbox.emptyNode,
					addClass:function(className){ $(this.selector).addClass(className); },
					removeClass:function(className){ $(this.selector).removeClass(className); }
				};
				
				if(arg.type == 'section'){
					
					_.each(arg.fields, function(field){

						formObj[arg.name][field.name] = {
							selector: formObj[arg.name].selector +'-'+ field.name,
							html: buildFieldHTML(field, formObj[arg.name].selector.replace('.', '') +'-'+ field.name),
							appendNode: Sandbox.appendNode,
							makeNode: Sandbox.makeNode,
							patch: Sandbox.patchNode,
							notify: Sandbox.makeNotification,
							notifications: {},
							listeners: buildListener(formObj[arg.name].selector.replace('.', '') +'-'+ field.name, field, arg.fields, selector),
							update: function(newValue){updateField(this, newValue);},
							args: field,
							empty: Sandbox.emptyNode
						};
						
					});
					
				}
				
			});
			
			return formObj;
			
		},
		
		processSelf: function(){

			var ret = validateForm(this.selector, $(this.selector).serializeArray());

			_.each(this, function(field, key){
				
				if (
					typeof field === 'object' 
					&& field.hasOwnProperty('args')
				) {
					
					if (field.args.type === 'file-upload') {
	
						ret.fields[key] = {
							value: {
								fileData: $(field.selector)[0].files[0],
								fileName: ret.fields[key +'-fileName'].value,
								fileType: ret.fields[key +'-fileType'].value,
								objectType: ret.fields[key +'-objectType'].value,
								objectId: ret.fields[key +'-objectId'].value,
								parent: ret.fields[key +'-parent'].value
							}, 
							type: 'file-upload'
						};
						
						_.each($(field.selector), function(field){
							if(field.files.length > 0){
								ret.fields[key].value.fileData = field.files[0];
							}
						});
						
						delete ret.fields[key +'-fileName'];
						delete ret.fields[key +'-fileType'];
						delete ret.fields[key +'-objectType'];
						delete ret.fields[key +'-objectId'];
						delete ret.fields[key +'-parent'];
						
					} else if (field.args.type === 'file-upload-multiple') {

					} else if (
						field.args.type === 'textbox' && field.args.tinymce
						|| field.args.type === 'textbox' && field.args.wysiwyg
					) {
											
						ret.fields[key] = {};
						ret.fields[key].value = field.args.editor.getContent();
										
					// usd to cents
					}else if(field.args.type === 'usd'){
						
						ret.fields[key].value = parseInt(ret.fields[key].value.replace(/[^0-9-]/g, '').replace(/(?!^)-/g, ''));
					
					// get data from multi-select dropdown w/search on
					}else if(field.args.type === 'select'){
						
						if (field.args.hasOwnProperty('value')) {
							
							ret.fields[key] = {
								value:field.args.value
							};
							
						}
						
					// get data from multi-select dropdown w/search on
					}else if(field.args.type === 'checkbox'){
	
						ret.fields[key] = {
							value:$(field.selector).dropdown('get value')
						};
											
					}else if (field.args.type === 'check') {
						
						if (field.args.hasOwnProperty('value')) {
							
							ret.fields[key] = {
								value: field.args.value
							};
							
						}
						
					}else if (field.args.type === 'date') {
						
						if (field.args.hasOwnProperty('value')) {
							
							ret.fields[key] = {
								value: field.args.value
							};
							
						}
						
					}
					
				}
				
			}, this);

			return ret;		
				
		},
		
		process: function(formSelector){
			
			var ret = validateForm(formSelector, $(formSelector).serializeArray());
			
			return ret;			
			
		}
		
	}
	
})();