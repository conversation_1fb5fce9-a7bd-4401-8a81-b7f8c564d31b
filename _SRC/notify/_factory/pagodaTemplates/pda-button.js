var pda_button = (function(){
	
	function hideButton(){

		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		el.className = el.className + ' hidden';

	}
	
	function showButton(){
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		el.className = el.className.replace('hidden', '');
		
	}
	
	function resetState(){
		
		// reapply listeners
		var events = $._data($(this.selector).get(0), 'events');
		events.click = this.properties._tmp_click;
		
	}
	
	function updateText(newText, replace){
		
		if(replace === undefined){
			replace = true;
		}
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		if(el){
			
			el.innerHTML = newText;
			
			if(replace){
				this.properties.args.text = newText;
			}
			
		}
		
	}
	
	return {
		
		make: function(selector, buttonArgs, name){

			buttonArgs = Sandbox.verifyDomToolArgs(buttonArgs, this.setup());
			
			if(typeof buttonArgs.dataId === 'undefined') buttonArgs.dataId = 0;
			if(typeof buttonArgs.tag === 'undefined') buttonArgs.tag = '';
			if(typeof buttonArgs.size === 'undefined') buttonArgs.size = '';
			if(typeof buttonArgs.css === 'undefined') buttonArgs.css = '';
			if(typeof buttonArgs.style === 'undefined') buttonArgs.style = '';
			
			var buttonObj = {
				selector: selector +'-button-'+ name.replace(' ', ''),
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				reset: resetState,
				notifications: {},
				show: showButton,
				hide: hideButton,
// 				css: updateCss,
				text: updateText,
				properties: {
					args: buttonArgs
				}
			};
			
			// backwards compatability from old bootstrap classes
			var bootstrapToPagoda = {
				
				// size classes
				'btn-xs': 'mini',
				'btn-sm': 'small',
				'btn-lg': 'pda-btn-large',
				
				// color classes
				'btn-default': '',
				'btn-primary': 'pda-btnOutline-blue',
				'btn-success': 'pda-btnOutline-green',
				'btn-info': 'pda-btnOutline-primary',
				'btn-warning': 'pda-btnOutline-orange',
				'btn-danger': 'pda-btnOutline-red',
				'btn-link': 'pda-btnOutline-purple',
				
				// can't use pda-btn-primary right now, so this ugly switch
				'pda-pda-btnOutline-blue': 'pda-btn-primary',
				
				// helper classes
				'btn-block': 'pda-btn-fullWidth'
				
			};
			
			for(var bootstrapClass in bootstrapToPagoda){
				if(bootstrapToPagoda.hasOwnProperty(bootstrapClass)){
					
					if(buttonArgs.css.indexOf(bootstrapClass) > -1){
						buttonArgs.css = buttonArgs.css.split(bootstrapClass).join(bootstrapToPagoda[bootstrapClass]);
					}
					//console.log(buttonArgs.css);
				}
			}
			
			// colors
			var colors = [
				'primary', 'secondary', 'positive', 'negative', 'red', 'orange', 'yellow', 'olive', 'green', 'teal', 'blue', 'violet', 'purple', 'pink', 'brown', 'grey', 'black'
			];
			var btnColor = '';
			_.each(colors, function(color){
				if(buttonArgs.css.indexOf(color) > -1){
					btnColor = color;
				}
			});
			
			// sizes
			var sizes = {
				'pda-btn-x-small':'mini',
				'pda-btn-small':'tiny',
				'pda-btn-large':'large'
			};
			var size = 'small';
			_.each(sizes, function(size, old){
				if(buttonArgs.css.indexOf(old) > -1){
					btnColor = size;
				}
			});
			
			// styles
			var styles = [
				'basic',
				'inverted'
			];
			var style = '';
			if(buttonArgs.css.indexOf('Outline') > -1){
				style = 'basic';
			}
			
			var oldClasses = [
				'btn-xs',
				'btn-sm',
				'btn-lg',
				'pda-btn-large',
				'btn-default',
				'btn-primary',
				'pda-btnOutline-blue',
				'btn-success',
				'pda-btnOutline-green',
				'btn-info',
				'pda-btnOutline-primary',
				'btn-warning',
				'pda-btnOutline-orange',
				'btn-danger',
				'pda-btnOutline-red',
				'btn-link',
				'pda-btnOutline-purple',
				'pda-btnOutline-gray',
				'pda-btn-blue',
				'pda-transparent',
				'pda-btn-x-small',
				'pull-right',
				
				// can't use pda-btn-primary right now, so this ugly switch
				'pda-pda-btnOutline-blue',
				'pda-btn-primary',
				
				// helper classes
				'btn-block',
				'pda-btn-fullWidth'
			];
			
			_.each(oldClasses, function(oldClass){
				buttonArgs.css = buttonArgs.css.replace(oldClass, '');
			});
			
			var css = buttonArgs.css;
			
			buttonObj.html = '<button class="'+ size +' ui '+ style +' '+ btnColor +' '+ css +' button '+ buttonObj.selector.replace('.', '') +'" data-id="'+ buttonArgs.dataId +'" style="'+ buttonArgs.style +'" data-id2="'+ buttonArgs.dataId2 +'" data-id3="'+ buttonArgs.dataId3 +'">'+ buttonArgs.text +'</button>';
			
			return buttonObj;
					
		},
		
		setup: function(){
			
			return {
				
				type: {
					argType: 'option',
					options: ['default', 'primary', 'success', 'info', 'warning', 'danger', 'blue', 'primary', 'green', 'orange', 'red', 'purple'],
					defaultValue: 'default',
					description: 'The coloring of the button.'
				}, css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}, dataId: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the button here (can be used in notifications).'
				}, dataId2: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the button here (can be used in notifications).'
				}, dataId3: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the button here (can be used in notifications).'
				}, text: {
					argType: 'free-input',
					defaultValue: '',
					description: 'The text to be written within the button.'
				}
				
			}
			
		}, 
		
		setButtonLoadingGIF: function(){
			
			this.makeNode('loadingGIF', 'text', {text: '<img id="loading-img" style="display:block; margin: 0 auto; width: 30px;" src="../_images/loading.gif" >'});
			this.patch();
			
		}
	}
	
})();