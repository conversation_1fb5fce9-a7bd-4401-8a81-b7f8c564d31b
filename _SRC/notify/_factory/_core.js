var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Establish the module
	function _core(){};
	module.exports = _core;

	// Get dependencies
	var Sandbox = require('./_sandbox.js').sb;
	var _ = require('underscore-node');
	var moment = require('moment-timezone');

}

var appConfig = appConfig || '';

moment.tz.setDefault("Africa/Abidjan");

Math.trunc = Math.trunc || function(x) {
  var n = x - x%1;
  return n===0 && (x<0 || (x===0 && (1/x !== 1/0))) ? -0 : n;
};

if (!Array.prototype.filter)
  Array.prototype.filter = function(func, thisArg) {
    'use strict';
    if ( ! ((typeof func === 'Function' || typeof func === 'function') && this) )
        throw new TypeError();
    
    var len = this.length >>> 0,
        res = new Array(len), // preallocate array
        t = this, c = 0, i = -1;
    if (thisArg === undefined)
      while (++i !== len)
        // checks to see if the key was set
        if (i in this)
          if (func(t[i], i, t))
            res[c++] = t[i];
    else
      while (++i !== len)
        // checks to see if the key was set
        if (i in this)
          if (func.call(thisArg, t[i], i, t))
            res[c++] = t[i];
    
    res.length = c; // shrink down array to proper size
    return res;
  };
  
Number.isInteger = Number.isInteger || function(value) {
    return typeof value === "number" && 
           isFinite(value) && 
           Math.floor(value) === value;
};

if (typeof Object.assign != 'function') {
  Object.assign = function(target) {
    'use strict';
    if (target == null) {
      throw new TypeError('Cannot convert undefined or null to object');
    }

    target = Object(target);
    for (var index = 1; index < arguments.length; index++) {
      var source = arguments[index];
      if (source != null) {
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
    }
    return target;
  };
}
		
var Factory = (function(){
	
	var moduleData = {},
		debug = false,
		userSettings = Object.assign({}, appConfig.userSettings);
	
	var app = {
		
		components: {},
		instanceCount: 0
// 		extentions: {},
// 		modules: {},
// 		templates: {}
		
	};
	
	return {
		
		createComponentInstance: function(compId, parentModule){
				
// 			var instanceId = app.components[compId].instances.length;
			var instanceId = app.instanceCount;
			app.instanceCount++;

			if(app.components.hasOwnProperty(compId)){
				
				app.components[compId].instances[instanceId] = app.components[compId].component(Sandbox.create(this, compId, 'component', instanceId));
			
			}else{
				
				if(debug){
					console.log(compId +' does not exist');
				}
				
				// return object with empty notify/destroy methods so a page won't error out if component type doesn't exist
				return {
					notify: function(){},
					destroy: function(){}
				};
				
			}
			
			if(debug){
				console.log(parentModule +' has created an instance of the '+ compId +' component (id='+ instanceId +')');
			}
			
			return {
				
				baseComponentId: compId,
				instanceId: instanceId,
				parentModule: parentModule,
				
				notify: function(notificationObj){
					
					notificationObj.data.instanceId = this.instanceId;
					notificationObj.data.toCompInstance = true;
					Factory.triggerEvent(notificationObj, this.parentModule);
					
				}, 
				
				destroy: function(data){
					
					Sandbox.releaseComponentListeners(this.instanceId);
					
					if(debug){
						console.log(parentModule +' has destroyed its instance of the '+ compId +' component (id='+ instanceId +')');
					}
					
					if(app.components.hasOwnProperty(this.baseComponentId) && app.components[this.baseComponentId].instances[this.instanceId]){
						
						if(app.components[this.baseComponentId].instances[this.instanceId].hasOwnProperty('destroy')){
							app.components[this.baseComponentId].instances[this.instanceId].destroy(data);
						}
// 						app.components[this.baseComponentId].instances.splice(this.instanceId, 1);
						delete app.components[this.baseComponentId].instances[this.instanceId];
						
					}
					
				}
				
			};
			
		},
				
		debug: function(on){
			debug = on ? true : false;			
		},
		
		setModuleInitListeners: function (moduleId) {
			
			var mod = moduleData[moduleId];	
					
			if(mod){
				
				if(debug){
					console.log(moduleId +' is starting...');
				}
				
				mod.instance = mod.create(Sandbox.create(this, moduleId, 'module'));

				if (mod.instance) {
					if (mod.instance.hasOwnProperty('initListeners')) {
						if (typeof mod.instance.initListeners === 'function') {
							mod.instance.initListeners();
						}
					}
				}
			
			}
			
			
		},
		
		initComponent: function(compId){
			
			var comp = app.components[compId];	
					
			if(comp){
				
				if(debug){
					console.log('Component "'+ compId +'" is starting...');
				}

				comp.instance = comp.component(Sandbox.create(this, compId, 'component'));

				if (comp.instance) {
					if (comp.instance.hasOwnProperty('init')) {
						if (typeof comp.instance.init === 'function') {
							comp.instance.init();
						}
					}
				}	
			
			}
			
		},
		
		logState: function(ret){
			
			if(ret == true){
				return app;
			}else{
				console.log(app);
			}
			
		},
		
		register: function(moduleId, creator){

			if(debug){
				console.log('Module ' + moduleId +' has registered');
			}
						
			moduleData[moduleId] = { 
                create: creator, 
                instance: null 
            }; 
            
            this.registerComponent(moduleId, creator);
            
		},
		
		registerComponent: function(compId, component){
			
			if (debug) console.log('Component "'+ compId +'" has registered.');
			app.components[compId] = {
				component: component,
				events: {},
				instances: {}
			};
			
		},
		
		registerEvents: function (evts, mod) {
	
			if(debug){
				console.log(mod +' listening for:');
				console.log(evts);
			}
			
// 			if(moduleData[mod] && moduleData[mod].events){
				moduleData[mod].events = evts;
// 			}
			

		},
		
		registerComponentEvents: function(evts, comp, instanceId){
			
			if(debug){
/* 				console.log(mod +' listening for:'); */
				console.log(evts);
			}
			
			var functionName = '';
			for(var eventName in evts){
				
				// get function name
				var baseComponent = app.components[comp].component();
				for(var funcName in baseComponent){
					if(''+baseComponent[funcName] === ''+evts[eventName]){
						functionName = funcName;
					}
				}
				
				app.components[comp].events[eventName] = {
					
					name: functionName,
					run: function(data){
						
						if(data.hasOwnProperty('instanceId') && app.components[comp].instances[data.instanceId]){
							
							app.components[comp].instances[data.instanceId][this.name](data);
							
						}else{
							
							for(var instanceId in app.components[comp].instances){
								
								if(app.components[comp].instances.hasOwnProperty(instanceId)){

									app.components[comp].instances[instanceId][this.name](data);
									
								}
								
							}
							
						}
					
					}
					
				};
				
			}
			//console.log(app.components[comp]);
			return true;
			
		},
		
		removeEvents: function (evts, mod) { 

			var i = 0, evt;
			for( ; evt = evts[i++] ; ) { 
				delete mod.events[evt]; 
			} 

		},
		
		run: function(){
			this.startAll();
		},
		
		start: function(moduleId){
			
			var mod = moduleData[moduleId];	
					
			if(mod){
				
				if(debug){
					console.log(moduleId +' is starting...');
				}
				
				if (mod.instance) {
					if (mod.instance.hasOwnProperty('init')) {
						if (typeof mod.instance.init === 'function') {
							mod.instance.init();
						}
					}
				}
				
			}
			
		},
		
		startAll: function(){
			
			// Start components
			for(var compId in app.components){
				if(app.components.hasOwnProperty(compId)){
					this.initComponent(compId);
				}
			}
			
			// Start modules
			var moduleId;
			for(moduleId in moduleData){
				if(moduleData.hasOwnProperty(moduleId)){
					this.setModuleInitListeners(moduleId);
				}
			}
			for(moduleId in moduleData){
				if(moduleData.hasOwnProperty(moduleId)){
					this.start(moduleId);
				}
			}
			
		},
				
		stop: function(moduleId){
			var mod = moduleData[moduleId];
			if(mod && mod.instance){
				mod.instance.destroy();
				mod.instance = null;
			}
		},
		
		stopAll: function(){
			var moduleId;
			for(moduleId in moduleData){
				if(moduleData.hasOwnProperty(moduleId)){
					this.stop(moduleId);
				}
			}
		},
		 
		triggerEvent: function (evt, triggeringModule) {
			
			if(!evt.data){
				evt.data = {}
			}
			
			evt.data.moduleId = triggeringModule;
			
			if(debug){
				console.log(triggeringModule +' triggered:');
				console.log(evt);
			}

			var mod;
			var func;
			
			if(evt.data.toCompInstance){
				
				// trigger component events
				for(var compId in app.components){

					if(app.components.hasOwnProperty(compId)){
						
						if(app.components[compId].events.hasOwnProperty(evt.type)){
	
							app.components[compId].events[evt.type].run(evt.data);
							
						}
						
					}

				}
				
				return true;
				
			}
			
			for (mod in moduleData) { 
				
				if (moduleData.hasOwnProperty(mod)){ 
					mod = moduleData[mod]; 
					if (mod.events && mod.events[evt.type]) { 
// 						mod.events[evt.type](evt.data); 
						
						for (func in mod.instance){
							if(mod.instance[func] == mod.events[evt.type]){
								mod.instance[func](evt.data);
							}
						}
			
					} 
				} 
			}
			
			// trigger component events
			for(var compId in app.components){
				if(app.components.hasOwnProperty(compId)){
					
					if(app.components[compId].events.hasOwnProperty(evt.type)){

						app.components[compId].events[evt.type].run(evt.data);
						
					}
					
				}
			}
			
		},
		
		updateUserSettings: function(newSettings){
						
			appConfig.userSettings = newSettings;
			
			setTimeout(function(){
				
				if(userSettings != newSettings){
					
					appConfig.userSettings = newSettings;
					
					userSettings = newSettings;

					Factory.updateUserSettings(newSettings);
					
				}else{
					
					databaseConnection.obj.update('user_settings', newSettings, function(updatedSettings){
						
						appConfig.userSettings = updatedSettings;
						
						userSettings = updatedSettings;
						
					});

				}

			}, 500);		
			
		}
		
	};
	
}());

if (IN_NODE_ENV) {
	_core.Factory = Factory;
} 