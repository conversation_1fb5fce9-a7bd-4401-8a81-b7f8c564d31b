var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Establish the module
	function _sandbox(){};
	module.exports = _sandbox;

	// Get dependencies
	var _ = require('underscore-node');
	var moment = require('moment');
	var db = require('./_database.js').api;
	var databaseConnection = db;
	var urlData = require('./_urlData.js').urlData;
	var fileAPI = require('./files.js').fileAPI;

}

var MERGE_ENDPOINT = 'http://localhost:8084';
var appConfig = appConfig || '';
var document = document || '';
var window = window || '';
var windowWidth = window ? $(window).width() : 0;
var TimerState = {};

var Sandbox = (function(sandbox) {

	var	domTools = {};
	var domain = window ? window.location.host : MERGE_ENDPOINT;
	var dragData = {};
	var listenerData = {};
	var url = window ? window.location.protocol + "//" + window.location.host : MERGE_ENDPOINT;
	
	// For dev env 
	if (appConfig.instance === 'rickyvoltz') { domain = ''; }
	
	function buildCookieAPI(){
		
		var cookieData = {};
		
		cookieData.userId = getCookie('uid');
		cookieData.get = getCookie;
		cookieData.set = setCookie;
		
		return cookieData;
		
	}
	
	function buildCommunicationsTools(){
		
		var comms;
		
		if(typeof communications !== 'undefined'){
			comms = communications;
		}		
		
		return comms;
		
	}
	
	function buildDataTools(moduleId){
		
		Number.prototype.formatMoney = function(c, d, t){
	
			var n = this, 
			    c = isNaN(c = Math.abs(c)) ? 2 : c, 
			    d = d == undefined ? "." : d, 
			    t = t == undefined ? "," : t, 
			    s = n < 0 ? "-" : "", 
			    i = parseInt(n = Math.abs(+n || 0).toFixed(c)) + "", 
			    j = (j = i.length) > 3 ? j % 3 : 0;
			   
			return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
		 
		};
 
		String.prototype.replaceAll = function(target, replacement) {
		  return this.split(target).join(replacement);
		};
		
		var data = {};
				
		data.cookie = buildCookieAPI();
		
		data.search = {};
		data.search.newClient = function(){
			return new $.es.Client({
				host: 'elastic:<EMAIL>:9243'
			});
		};
		
		if(typeof databaseConnection !== 'undefined'){

			if (IN_NODE_ENV) {

				data.db = db;

			} else {

				data.db = $.extend(true, {}, databaseConnection);
			}

			data.db.setAppConfig(appConfig);

			data.db.obj.setModuleId(moduleId);
			
			data.db.spanURL = './../api/_getAdmin.php?do=';
					
		}
		
		if (typeof urlData !== 'undefined') {
			data.url = urlData;
		}
		if (typeof fileAPI !== 'undefined') {
			data.files = fileAPI;
		}
		if (typeof Countries !== 'undefined') {
			data.countries = Countries;
		}
		
		data.makePDF = function(html_string, view, options, callback) {

			// Change the border collapse from separate to collapse in order to work with mPDF
			html_string = html_string ? html_string.replace('border-collapse: separate;', 'border-collapse: collapse;') : '';
			
			var optionsTxt = '';
			if (options) {
				
				if (options.hasOwnProperty('footer') && options.footer == false) {
					optionsTxt = '&f=0';
				}
				
				if (options.orientation === 'landscape') {
					optionsTxt += '&o=l';
				}

				if (options.name) {
					optionsTxt += '&n=' + options.name.replace(new RegExp(' ', 'g'), '-');
				}
				
			}

			var urlMakePDF = url + '/api/_getAdmin.php?do=makePDF&api_webform=true&pagodaAPIKey=' + appConfig.instance + optionsTxt;

			var args = { 'html_string': html_string, 'view': view }

			if (view === 'F') {

				$.ajax({
					type: 'POST',
					url: urlMakePDF,
					data: args,
					success: function (filePath) {

						callback(filePath);
						
					},
					
					error: function (jqXHR, status, error) {
		
						alerts.alert('Error!', 'Something went wrong. Try again later.', 'error');
						
					}
					
				});	

			} else {

				var $form = $("#downloadForm");
			
				if ($form.length == 0) {
					$form = $("<form>").attr({ "target": "_blank", "id": "downloadForm", "method": "POST"}).hide();
					$("body").append($form);
				}

				$form.attr('action', urlMakePDF);
				
				$form.find("input").remove();

				for(var field in args){
					$form.append($("<input>").attr({"value":args[field], "name":field}));
				}

				$form.submit();

			}
		    
		}
		
		data.util = {
			
			calculateRemainingVacationDays: function(staffObj, timeOffObjs, fiscalYearStart, fiscalYearEnd, holidays){
		
				var approvedDaysOff = 0,
					workRequestDays = 0,
					daysOffArray = [],
					removeTimeOffDays = 0,
					removeWorkDays = 0;
					
				_.each(staffObj.availability, function(availability){
					
					if(daysOffArray.indexOf(availability.day) == -1){
						daysOffArray.push(availability.day);
					}
					
				});	
				
				_.each(holidays, function(holiday){
					
					if(holiday.frequency == 'weekly'){
						
						_.each(holiday.daysOfWeek, function(weekday){

							daysOffArray.push(moment(+weekday).format('dddd'));
							
						});
						
					}
					
				});	
		
				_.each(timeOffObjs, function(timeOffObj){
					
					var requestStart = moment(timeOffObj.startDate, 'YYYY-MM-DD'),
					    requestEnd = moment(timeOffObj.endDate, 'YYYY-MM-DD'),
					    dateRange = [],
					    day = requestStart,
					    holidayRange = [];
					    
					while (day <= requestEnd) {
					    dateRange.push(day.clone());
					    day = day.clone().add(1, 'd');
					} 
					
					_.each(holidays, function(holiday){
					
						if(holiday.frequency == 'annual' || holiday.frequency == 'one_time'){
							
							var holidayStart = moment(holiday.startDate, 'MM/DD/YYYY'),
								holidayEnd = moment(holiday.endDate, 'MM/DD/YYYY');

							while (holidayStart <= holidayEnd) {
							    holidayRange.push(holidayStart.clone());
							    holidayStart = holidayStart.clone().add(1, 'd');
							}	
							
						}
																		
					});					
					  
					if(moment(timeOffObj.endDate, 'YYYY-MM-DD').isBetween(fiscalYearStart, moment().subtract(1,'days'))){
					    
						if(timeOffObj.hasOwnProperty('totalDays')){
							
							if(timeOffObj.totalDays > 0){
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'time off'){
								
									approvedDaysOff += timeOffObj.totalDays
								
								}
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'work request'){
								
									workRequestDays += timeOffObj.totalDays
								
								}
								
							}else{
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'time off'){
							
									approvedDaysOff += requestEnd.add(1, 'days').diff(requestStart, 'days');
									
									_.each(dateRange, function(dateInRange){
										
										if(_.indexOf(daysOffArray, dateInRange.format('dddd')) > -1){
											
											removeTimeOffDays++;
											
										}
										
										_.each(holidayRange, function(holidayMoment){

											if(holidayMoment.isBetween(requestStart, requestEnd)){
												
												removeTimeOffDays++;
												
											}
											
										});
																				
									});
									
								}
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'work request'){
									
									workRequestDays += moment(timeOffObj.endDate, 'YYYY-MM-DD').add(1, 'days').diff(moment(timeOffObj.startDate, 'YYYY-MM-DD'), 'days');
									
									_.each(dateRange, function(dateInRange){
										
										if(_.indexOf(daysOffArray, dateInRange.format('dddd')) > -1){
											
											removeWorkDays++;
											
										}
																				
									});
									
								}
								
							}
							
						}
					
					}
					    						
				});
		
				return staffObj.vacation_days - (approvedDaysOff - removeTimeOffDays) + (workRequestDays - removeWorkDays);
						
			},
			
			calculateTotalVacationDays: function(staffObj, timeOffObjs, fiscalYearStart, fiscalYearEnd, holidays){

				var approvedDaysOff = 0,
					workRequestDays = 0,
					daysOffArray = [],
					removeTimeOffDays = 0,
					removeWorkDays = 0;
					
				_.each(staffObj.availability, function(availability){
					
					if(daysOffArray.indexOf(availability.day) == -1){
						daysOffArray.push(availability.day);
					}
					
				});
				
				_.each(holidays, function(holiday){
					
					if(holiday.frequency == 'weekly'){
						
						_.each(holiday.daysOfWeek, function(weekday){

							daysOffArray.push(moment(+weekday).format('dddd'));
							
						});
						
					}
					
				});	

				_.each(timeOffObjs, function(timeOffObj){
					
					var requestStart = moment(timeOffObj.startDate, 'YYYY-MM-DD'),
					    requestEnd = moment(timeOffObj.endDate, 'YYYY-MM-DD'),
					    dateRange = [],
					    day = requestStart,
					    holidayRange = [];
					    
					while (day <= requestEnd) {
					    dateRange.push(day.clone());
					    day = day.clone().add(1, 'd');
					} 
					
					_.each(holidays, function(holiday){
					
						if(holiday.frequency == 'annual' || holiday.frequency == 'one_time'){
							
							var holidayStart = moment(holiday.startDate, 'MM/DD/YYYY'),
								holidayEnd = moment(holiday.endDate, 'MM/DD/YYYY');
								
							while (holidayStart <= holidayEnd) {
							    holidayRange.push(holidayStart.clone());
							    holidayStart = holidayStart.clone().add(1, 'd');
							}	
							
						}
												
					});
					
					if(moment(timeOffObj.endDate, 'YYYY-MM-DD').isBetween(fiscalYearStart, fiscalYearEnd)){
					    
						if(timeOffObj.hasOwnProperty('totalDays')){
							
							if(timeOffObj.totalDays > 0){
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'time off'){
								
									approvedDaysOff += timeOffObj.totalDays
								
								}
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'work request'){
								
									workRequestDays += timeOffObj.totalDays
								
								}
								
							}else{
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'time off'){
							
									approvedDaysOff += requestEnd.add(1, 'days').diff(moment(timeOffObj.startDate, 'YYYY-MM-DD'), 'days');
									
									_.each(dateRange, function(dateInRange){

										if(_.indexOf(daysOffArray, dateInRange.format('dddd')) > -1){
											
											removeTimeOffDays++;
											
										}
																				
									});
									
									_.each(holidayRange, function(dateInRange){
										
										_.each(dateRange, function(requestRange){

											if(dateInRange.format('MM/DD/YYYY') === requestRange.format('MM/DD/YYYY')){
												
												removeTimeOffDays++;
												
											}
											
										});
																														
									});
									

								}
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'work request'){
									
									workRequestDays += requestEnd.add(1, 'days').diff(moment(timeOffObj.startDate, 'YYYY-MM-DD'), 'days');
									
									_.each(dateRange, function(dateInRange){
										
										if(_.indexOf(daysOffArray, dateInRange.format('dddd')) > -1){
											
											removeWorkDays++;
											
										}
																				
									});
									
								}
								
							}
							
						}
					
					}    
				
				});

				return (approvedDaysOff - removeTimeOffDays) + (workRequestDays - removeWorkDays);
						
			},
	
			calculateApprovedDays: function(staffObj, timeOffObjs, fiscalYearStart, fiscalYearEnd, holidays){
				
				var approvedDaysOff = 0,
					workRequestDays = 0,
					daysOffArray = [],
					removeTimeOffDays = 0,
					removeWorkDays = 0;
					
				_.each(staffObj.availability, function(availability){
					
					if(daysOffArray.indexOf(availability.day) == 0){
						daysOffArray.push(availability.day);
					}		
				});
				
				_.each(holidays, function(holiday){
					
					if(holiday.frequency == 'weekly'){
						
						_.each(holiday.daysOfWeek, function(weekday){

							daysOffArray.push(moment(+weekday).format('dddd'));
							
						});
						
					}
					
				});	
		
				_.each(timeOffObjs, function(timeOffObj){
					
					var requestStart = moment(timeOffObj.startDate, 'YYYY-MM-DD'),
					    requestEnd = moment(timeOffObj.endDate, 'YYYY-MM-DD'),
					    dateRange = [],
					    day = requestStart,
					    holidayRange = [];
					    
					while (day <= requestEnd) {
					    dateRange.push(day.clone());
					    day = day.clone().add(1, 'd');
					}
					
					_.each(holidays, function(holiday){
					
						if(holiday.frequency == 'annual' || holiday.frequency == 'one_time'){
							
							var holidayStart = moment(holiday.startDate, 'MM/DD/YYYY'),
								holidayEnd = moment(holiday.endDate, 'MM/DD/YYYY');
								
							while (holidayStart <= holidayEnd) {
							    holidayRange.push(holidayStart.clone());
							    holidayStart = holidayStart.clone().add(1, 'd');
							}	
							
						}
												
					});
					    
					if(moment(timeOffObj.endDate, 'YYYY-MM-DD').isBetween(fiscalYearStart, fiscalYearEnd)){
					    
						if(timeOffObj.hasOwnProperty('totalDays')){
							
							if(timeOffObj.totalDays > 0){
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'time off'){
								
									approvedDaysOff += timeOffObj.totalDays
								
								}
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'work request'){
								
									workRequestDays += timeOffObj.totalDays
								
								}
								
							}else{
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'time off'){
							
									approvedDaysOff += requestEnd.add(1, 'days').diff(moment(timeOffObj.startDate, 'YYYY-MM-DD'), 'days');
									
									_.each(dateRange, function(dateInRange){
										
										if(_.indexOf(daysOffArray, dateInRange.format('dddd')) > -1){
											
											removeTimeOffDays++;
											
										}
										
										_.each(holidayRange, function(holidayMoment){
											
											if(holidayMoment.isBetween(requestStart, requestEnd, null, '[]')){
												
												removeTimeOffDays++;
												
											}
											
										});
																				
									});
									
								}
								
								if(timeOffObj.status == 'approved' && timeOffObj.type == 'work request'){
									
									workRequestDays += requestEnd.add(1, 'days').diff(moment(timeOffObj.startDate, 'YYYY-MM-DD'), 'days');
									
									_.each(dateRange, function(dateInRange){
										
										if(_.indexOf(daysOffArray, dateInRange.format('dddd')) > -1){
											
											removeWorkDays++;
											
										}
																				
									});
									
								}
								
							}
							
						}
					
					}    
    					
				});
		
				return staffObj.vacation_days - (approvedDaysOff - removeTimeOffDays) + (workRequestDays - removeWorkDays);
						
			}
			
		}
		
		if(appConfig.instance == 'thelifebookau'){
			
			data.countryArray = [
				{ name: 'AUSTRALIA', value:'AU' }
			];
			
			data.stateArray = [
			    { name: 'Ashmore and Cartier Islands', value: 'ACI'},
			    { name: 'Australian Antarctic Territory', value: 'AAT'},
			    { name: 'Australian Capital Territory', value: 'ACT'},
			    { name: 'Christmas Island', value: 'CI'},
			    { name: 'Cocos (Keeling) Islands', value: 'KI'},
			    { name: 'Coral Sea Islands', value: 'CSI'},
			    { name: 'Heard Island and McDonald Islands', value: 'HIMI'},
			    { name: 'Jervis Bay Territory', value: 'JB'},
			    { name: 'New South Wales', value: 'NSW'},
			    { name: 'Norfolk Island', value: 'NI'},
			    { name: 'Northern Territory', value: 'NT'},
			    { name: 'Queensland', value: 'QLD'},
			    { name: 'South Australia', value: 'SA'},
			    { name: 'Tasmania', value: 'TAS'},
			    { name: 'Victoria', value: 'VIC'},
			    { name: 'Western Australia', value: 'WA'}
			];
			
		}else{
			
			data.countryArray = [
				{ name: 'UNITED STATES OF AMERICA', value:'USA' }
			];
			
			data.stateArray = [
			    { name: 'ALABAMA', value: 'AL'},
			    { name: 'ALASKA', value: 'AK'},
			    { name: 'AMERICAN SAMOA', value: 'AS'},
			    { name: 'ARIZONA', value: 'AZ'},
			    { name: 'ARKANSAS', value: 'AR'},
			    { name: 'CALIFORNIA', value: 'CA'},
			    { name: 'COLORADO', value: 'CO'},
			    { name: 'CONNECTICUT', value: 'CT'},
			    { name: 'DELAWARE', value: 'DE'},
			    { name: 'DISTRICT OF COLUMBIA', value: 'DC'},
			    { name: 'FEDERATED STATES OF MICRONESIA', value: 'FM'},
			    { name: 'FLORIDA', value: 'FL'},
			    { name: 'GEORGIA', value: 'GA'},
			    { name: 'GUAM', value: 'GU'},
			    { name: 'HAWAII', value: 'HI'},
			    { name: 'IDAHO', value: 'ID'},
			    { name: 'ILLINOIS', value: 'IL'},
			    { name: 'INDIANA', value: 'IN'},
			    { name: 'IOWA', value: 'IA'},
			    { name: 'KANSAS', value: 'KS'},
			    { name: 'KENTUCKY', value: 'KY'},
			    { name: 'LOUISIANA', value: 'LA'},
			    { name: 'MAINE', value: 'ME'},
			    { name: 'MARSHALL ISLANDS', value: 'MH'},
			    { name: 'MARYLAND', value: 'MD'},
			    { name: 'MASSACHUSETTS', value: 'MA'},
			    { name: 'MICHIGAN', value: 'MI'},
			    { name: 'MINNESOTA', value: 'MN'},
			    { name: 'MISSISSIPPI', value: 'MS'},
			    { name: 'MISSOURI', value: 'MO'},
			    { name: 'MONTANA', value: 'MT'},
			    { name: 'NEBRASKA', value: 'NE'},
			    { name: 'NEVADA', value: 'NV'},
			    { name: 'NEW HAMPSHIRE', value: 'NH'},
			    { name: 'NEW JERSEY', value: 'NJ'},
			    { name: 'NEW MEXICO', value: 'NM'},
			    { name: 'NEW YORK', value: 'NY'},
			    { name: 'NORTH CAROLINA', value: 'NC'},
			    { name: 'NORTH DAKOTA', value: 'ND'},
			    { name: 'NORTHERN MARIANA ISLANDS', value: 'MP'},
			    { name: 'OHIO', value: 'OH'},
			    { name: 'OKLAHOMA', value: 'OK'},
			    { name: 'OREGON', value: 'OR'},
			    { name: 'PENNSYLVANIA', value: 'PA'},
			    { name: 'PUERTO RICO', value: 'PR'},
			    { name: 'RHODE ISLAND', value: 'RI'},
			    { name: 'SOUTH CAROLINA', value: 'SC'},
			    { name: 'SOUTH DAKOTA', value: 'SD'},
			    { name: 'TENNESSEE', value: 'TN'},
			    { name: 'TEXAS', value: 'TX'},
			    { name: 'UTAH', value: 'UT'},
			    { name: 'VERMONT', value: 'VT'},
			    { name: 'VIRGIN ISLANDS', value: 'VI'},
			    { name: 'VIRGINIA', value: 'VA'},
			    { name: 'WASHINGTON', value: 'WA'},
			    { name: 'WEST VIRGINIA', value: 'WV'},
			    { name: 'WISCONSIN', value: 'WI'},
			    { name: 'WYOMING', value: 'WY' }
			];
			
		}
		
		if (typeof Uom !== 'undefined') {
			
			data.units = {
				uom: Uom
			};
			
		}
		
		return data;
		
	}
	
	function buildDOMTools(moduleId, compInstanceId){
		
		if(typeof domTool !== 'undefined'){
			domTools = domTool;
		}
		if(typeof compInstanceId !== 'undefined'){
			
			domTools.compInstanceId = compInstanceId;
			domTools.make = function(selector, moduleId, instanceId){
				
				if(instanceId !== undefined){
					compInstanceId = instanceId;
				}
				
				var baseClass = selector.replace('.', '');
			
				var domObj =  {
					
					selector: selector,
					html: '<div class="container col-sm-12 '+ baseClass +'"></div>',
					makeNode: Sandbox.makeNode,
					appendNode: Sandbox.appendNode,
					empty: Sandbox.emptyNode,
					
					currentObj: {},
					properties: {
						compInstanceId: compInstanceId
					},
						
					build: Sandbox.patchNode	
					
				}
								
				return domObj;
			};
			
		}
		
		domTools.container = createCSSSelector(moduleId);
		domTools.loadingGIF = '<img id="loading-img" style="display:block; margin: 0 auto; width: 30px;" src="../_images/loading.gif" >';
		
		domTools.formatPhone = function(text){
			return text.replace(/(\d\d\d)(\d\d\d)(\d\d\d\d)/, '($1) $2-$3');
		};
		
		domTools.formatNumber = function(x){
		    var parts = x.toString().split(".");
		    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
		    return parts.join(".");
		}
				
		if(typeof alerts !== 'undefined'){
			domTools.alerts = alerts;
		}else{
			alerts = {};
		}
		
		if(typeof bsButtons !== 'undefined'){
			domTools.button = bsButtons;
		}else{
			bsButtons = {};
		}
		
		if(typeof bsColumns !== 'undefined'){
			domTools.columns = bsColumns;
			domTools.column = bsColumns;
		}else{
			bsColumns = {};
		}
		
		if(typeof bsForms !== 'undefined'){
			domTools.form = bsForms;
		}else{
			bsForms = {};
		}
		
		if(typeof bsLabels !== 'undefined'){
			domTools.label = bsLabels;
		}else{
			bsLabels = {};
		}
		
		if(typeof bsLists !== 'undefined'){
			domTools.list = bsLists;
		}else{
			bsLists = {};
		}
		
		if(typeof bsListItems !== 'undefined'){
			domTools.listItem = bsListItems;
		}else{
			bsListItems = {};
		}
		
		if(typeof bsModal2 !== 'undefined'){
			domTools.modal = bsModal2;
		}else{
			bsModal2 = {};
		}
		
		if(typeof bsPanel !== 'undefined'){
			domTools.panel = bsPanel;
		}else{
			bsPanel = {};
		}
		
		if(typeof bsTable !== 'undefined'){
			domTools.table = bsTable;
		}else{
			bsTable = {};
		}
		
		if(typeof bsAlerts !== 'undefined'){
			domTools.loader = bsAlerts;
		}else{
			bsAlerts = {};
		}
		
		if(typeof bsText !== 'undefined'){
			domTools.text = bsText;
		}else{
			bsText = {};
		}
		
		if(typeof lineBreak !== 'undefined'){
			domTools.lineBreak = lineBreak;
		}else{
			lineBreak = {};
		}
		
		if(typeof headerText !== 'undefined'){
			domTools.headerText = headerText;
		}else{
			headerText = {};
		}
		
		if(typeof containers !== 'undefined'){
			domTools.container = containers;
		}else{
			containers = {};
		}
		
		if(typeof bsButtonGroup !== 'undefined'){
			domTools.buttonGroup = bsButtonGroup;
		}else{
			bsButtonGroup = {};
		}
		
		if(typeof listeners !== 'undefined'){
			domTools.listener = listeners;
		}else{
			listeners = {};
		}
		
		if(typeof svgPath !== 'undefined'){
			domTools.path = svgPath;
		}else{
			var svgPath = {};
		}
		if(typeof bsTabs !== 'undefined'){
			domTools.tabs = bsTabs;
		}else{
			bsTabs = {};
		}
		if(typeof anchors !== 'undefined'){
			domTools.anchor = anchors;
		}else{
			anchors = {};
		}
		if(typeof charts !== 'undefined'){
			domTools.chart = charts;
		}else{
			charts = {};
		}
		if(typeof tiles !== 'undefined'){
			domTools.tile = tiles;
		}else{
			tiles = {};
		}
		if(typeof fileDrop !== 'undefined'){
			domTools.fileDrop = fileDrop;
		}else{
			fileDrop = {};
		}
		if(typeof bsImages !== 'undefined'){
			domTools.image = bsImages;
		}else{
			bsImages = {};
		}
		if(typeof pda_panel !== 'undefined'){
			domTools.panel = pda_panel;
		}else{
			pda_panel = {};
		}
		if(typeof pda_button !== 'undefined'){
			domTools.button = pda_button;
		}else{
			pda_button = {};
		}
		if(typeof pda_button_group !== 'undefined'){
			domTools.buttonGroup = pda_button_group;
		}else{
			pda_button_group = {};
		}
		if(typeof pda_forms !== 'undefined'){
			domTools.form = pda_forms;
		}else{
			var pda_forms = {};
		}
		
		if(typeof web_ui_dom_tool !== 'undefined'){
			domTools.popover = web_ui_dom_tool;
		}else{
			web_ui_dom_tool = {};
		}
		
		if(typeof editable_item !== 'undefined'){
			domTools.editable = editable_item;
		}else{
			editable_item = {};
		}
		
		if(typeof pda_loader !== 'undefined'){
			domTools.loader = pda_loader;
		}else{
			pda_loader = {};
		}
		
		if(typeof div_ui_tool !== 'undefined'){
			domTools.div = div_ui_tool;
		}else{
			div_ui_tool = {};
		}
		
		if(typeof progress_ui_tool !== 'undefined'){
			domTools.progress = progress_ui_tool;
		}else{
			progress_ui_tool = {};
		}
		
		if(typeof accordion_ui_tool !== 'undefined'){
			domTools.accordion = accordion_ui_tool;
		}else{
			accordion_ui_tool = {};
		}
		
		return {
			
			// user properties
			moduleId: moduleId,
			instanceId: compInstanceId,
			
			// root methods
			add: 		domTools.add,
			build: 		domTools.build,
			get: 		domTools.get,
			
			make: function(selector, moduleId, instanceId){
				
				if(instanceId !== undefined){
					compInstanceId = instanceId;
				}

				var baseClass = selector.replace('.', '');
			
				var domObj =  {
					
					selector: selector,
					html: '<div class="container col-sm-12 '+ baseClass +'"></div>',
					makeNode: Sandbox.makeNode,
					appendNode: Sandbox.appendNode,
					empty: Sandbox.emptyNode,
					loading: Sandbox.loading,
					
					currentObj: {},
					properties: {
						compInstanceId: this.instanceId
					},
						
					build: Sandbox.patchNode	
					
				}
								
				return domObj;
			},
			
			loadingGIF: domTools.loadingGIF,
			patch:		domTools.patch,
			
			// misc tools
			container:	createCSSSelector(moduleId),
			loadingGIF:	'<img id="loading-img" style="display:block; margin: 0 auto; width: 30px;" src="../_images/loading.gif" >',
			formatPhone: function(text){
				return text.replace(/(\d\d\d)(\d\d\d)(\d\d\d\d)/, '($1) $2-$3');
			},
			formatNumber: function(x){
			    var parts = x.toString().split(".");
			    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
			    return parts.join(".");
			},
			
			// node types
			alerts: alerts,
			button: bsButtons,
			columns: bsColumns,
			column: bsColumns,
			form: bsForms,
			label: bsLabels,
			list: bsLists,
			listItem: bsListItems,
			modal: bsModal2,
			panel: bsPanel,
			table: bsTable,
			loader: bsAlerts,
			text: bsText,
			lineBreak: lineBreak,
			headerText: headerText,
			container: containers,
			buttonGroup: bsButtonGroup,
			listener: listeners,
			path: svgPath,
			tabs: bsTabs,
			anchor: anchors,
			chart: charts,
			tile: tiles,
			fileDrop: fileDrop,
			image: bsImages,
			panel: pda_panel,
			button: pda_button,
			buttonGroup: pda_button_group,
			form: pda_forms,
			popover: web_ui_dom_tool,
			editable: editable_item,
			loader: pda_loader,
			div: div_ui_tool,
			progress:progress_ui_tool,
			accordion:accordion_ui_tool,
			
			// bools
			isMobile: windowWidth <= 768 ? true : false,

			// colors
			legacycolors: domTools.legacycolors,
			colors: domTools.colors,
			colorsNameToHex: domTools.colorsNameToHex,
			colorsHexToName: domTools.colorsHexToName,
			colorsNameToRGB: domTools.colorsNameToRGB,
			colorsRGBToName: domTools.colorsRGBToName,
			colorsIsHexadecimal: domTools.colorsIsHexadecimal,
			colorsRGBToHex: domTools.colorsRGBToHex,
			icons: domTools.icons,
			iconsSemanticToFontAwesome: domTools.iconsSemanticToFontAwesome,

			// urls
			cleanURL: function(url) {

				if (typeof url !== 'string') {
					return '';
				}
				
				if ( url.substring(0, 8) === 'https://' || url.substring(0, 7) === 'http://' ) {
					return url;
				}
				
				return 'https://'+ url;
	
			},
			lastURLSegment: function(url) {
					
				var lastURLSegment = url.substr(url.lastIndexOf('/') + 1);
				lastURLSegment = lastURLSegment.split('-');
				lastURLSegment = lastURLSegment[lastURLSegment.length - 1];
				return lastURLSegment;
				
			},
			
			// dates
			utcOffset: new Date().getTimezoneOffset() / 60,

			// times
			timeToSeconds: function(time) {
				
			},

			// currency
			formatCurrency: function(value) {
				value = !value ? 0 : value;
				value = value / 100;
				value = value.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,");
				return '$'+ value;
			},

			// random string
			randomString: function(length, chars) {
				var result = '';
				for (var i = length; i > 0; --i) result += chars[Math.round(Math.random() * (chars.length - 1))];
				return result;
			},

			// remove script tags
			removeScriptTags: function(html_string) {
				if (html_string) {
					html_string = html_string.replace('<script', '<removed-by-bento');
					html_string = html_string.replace('script>', 'removed-by-bento>');
					html_string = html_string.replace('&lt;script', '&lt;removed-by-bento');
					html_string = html_string.replace('script&gt;', 'removed-by-bento&gt;');
				}
				return html_string;
			},

			// duration in seconds
			durationToSeconds: function(duration) {
				var time = duration.split(':');
				var hours = time[0] > 0 ? time[0] * 3600 : 0;
				var minutes = time[1] > 0 ? time[1] * 60 : 0;
				var seconds = time[2] > 0 ? time[2] : 0;
				return Math.round(parseInt(hours) + parseInt(minutes) + parseInt(seconds));
			},

			// duration in seconds
			secondsToDurationFormat: function(seconds) {

				var hours = Math.floor(seconds / 3600);
				var minutes = Math.floor((seconds / 60) - (hours * 60));

				if (hours > 0) {
					seconds = seconds - (hours * 3600);
				}

				if (minutes > 0) {
					seconds = seconds - (minutes * 60);
				}

				if (seconds < 0) {
					seconds = 0;
				}

				hours = hours < 10 ? '0' + hours : hours;
				minutes = minutes < 10 ? '0' + minutes : minutes;
				seconds = seconds < 10 ? '0' + seconds : seconds;

				return hours + ':' + minutes + ':' + seconds;
				
			},

			// duration formatted
			durationDisplay: function(obj, returnType) {

				var duration = {
					minutes: 0,
					hours: 0,
					duration_start: moment(obj.start_date),
					remainder: 0
				};

				if (_.isNumber(obj)) {
				
					duration.minutes = obj / 60;

				} else {

					if( obj.hasOwnProperty('end_date') && obj.end_date.length === 0 ) {
						
						duration.duration_end = moment();
						
					} else {
						
						duration.duration_end = moment(obj.start_date).add(obj.duration, 'seconds');
						
					}
			
					duration.minutes = duration.duration_end.diff(duration.duration_start, 'minutes');

				}
		
				if (returnType === undefined) {
					
					if(duration.minutes > 60) {
						
						duration.hours = duration.minutes / 60;
						duration.remainder = duration.minutes % 60;
					
						return Math.floor(duration.hours).toFixed(0) + 'hr ' + Math.floor(duration.remainder).toFixed(0) + 'min';
						
					} else {
						
						return Math.floor(duration.minutes).toFixed(0) + 'min';
						
					}
					
				} else {
					
					if (returnType === 'min') {
					
						return Math.floor(duration.minutes).toFixed(0);
						
					} else if (returnType === 'hr') {
						
						return Math.floor(duration.hours).toFixed(0);
						
					} else if (returnType === 'all') {
						
						duration.hours = duration.minutes / 60;
						duration.remainder = duration.minutes % 60;
						
						return {
							hours: Math.floor(duration.hours).toFixed(0),
							remainder: Math.floor(duration.remainder).toFixed(0)
						}
						
					}	
					
				}

			},

			// company profile image
			getCompanyAvatar: function(company) {

				data = buildDataTools(moduleId);

				var profileImgHtml = '';

				if (
					company.profile_image != null
					&& company.profile_image.hasOwnProperty('loc')
					&& company.profile_image.loc !== '//') 
				{

					profileImgHtml = '<img class="ui cirular avatar image" src="'+ data.files.getURL(company.profile_image) +'">';
					return profileImgHtml;

				} else {

					var initials = company.name.charAt(0).toUpperCase() + company.name.charAt(1).toUpperCase();
					var canvas = document.createElement('canvas');
					var size = 60;
					canvas.width = size;
					canvas.height = size;
					var context = canvas.getContext("2d");
					context.fillStyle = this.colorsNameToHex[company.color];
					context.fillRect(0, 0, canvas.width, canvas.height);
					context.font = Math.round(canvas.width/2)+"px Arial";
					context.textAlign = "center";
					context.fillStyle = "#FFF";
					context.fillText(initials, size / 2, size / 1.5);
					var profileImgUrl = canvas.toDataURL();
					canvas  = null;
					
					profileImgHtml = '<img class="ui cirular avatar image" src="'+ profileImgUrl +'">';
					return profileImgHtml;

				}

			},

			// user profile image
			getUserAvatar: function(user) {

				data = buildDataTools(moduleId);

				var profileImgHtml = '';

				if (
					user.profile_image != null
					&& user.profile_image.hasOwnProperty('loc')
					&& user.profile_image.loc !== '//') 
				{

					profileImgHtml = '<img class="ui cirular avatar image" src="'+ data.files.getURL(user.profile_image) +'">';
					return profileImgHtml;

				} else {

					var initials = user.fname.charAt(0).toUpperCase() + user.lname.charAt(0).toUpperCase();
					var canvas = document.createElement('canvas');
					var size = 60;
					canvas.width = size;
					canvas.height = size;
					var context = canvas.getContext("2d");
					context.fillStyle = this.colorsNameToHex[user.color];
					context.fillRect(0, 0, canvas.width, canvas.height);
					context.font = Math.round(canvas.width/2)+"px Arial";
					context.textAlign = "center";
					context.fillStyle = "#FFF";
					context.fillText(initials, size / 2, size / 1.5);
					var profileImgUrl = canvas.toDataURL();
					canvas  = null;
					
					profileImgHtml = '<img class="ui cirular avatar image" src="'+ profileImgUrl +'">';
					return profileImgHtml;

				}

			},

			// field icons
			getFieldIcon: function(blueprint, field, key) {
			
				var typeDef = _.findWhere(appConfig.Fields, {name: field.fieldType});
				var icon = '';
				
				if (typeDef) {
					
					icon = typeDef.icon;
					
					// icons that change with options
					if (
						!_.isEmpty(blueprint)
						&& !_.isEmpty(blueprint[key])
						&& blueprint[key].hasOwnProperty('options')
						&& typeof typeDef.getIcon === 'function'
					) {
						icon = typeDef.getIcon(blueprint[key].options);
					}
					
				} else {
	
					switch (field.objectType) {
						
						case 'contacts':
						case 'users':
							icon = 'users';
							break;
						
					}
					
				}

				// Parent fields
				if (_.isEmpty(icon) && key === 'parent') {
					icon = 'external square';
				}
				
				return icon;
				
			},

			blueprintPrettyName: function(blueprint) {
			
				blueprint = blueprint.replace(/_/g, ' ');
				blueprint = blueprint.replace(/(^\w{1})|(\s+\w{1})/g, letter => letter.toUpperCase());

				return blueprint;
				
			},

			blueprintNameFormatted: function(bpName) {

				if (bpName != 'time_entries') {
					bpName = !bpName.includes('#') ? '#' + bpName : bpName;
				}

				return bpName;

			},

			blueprintNameUnformatted: function(bpName) {

				if (bpName != 'time_entries') {
					bpName = bpName.includes('#') ? bpName.substr(1) : bpName;
				}

				return bpName;

			}
			
		};
		
	}
	
	function buildObjectsExtension(){
		
		var objectsTool;
		
		if(typeof objectsModule !== 'undefined'){
			objectsTool = objectsModule;
		}
		
		return objectsTool;
		
	}
	
	function buildSystemTools(Factory){
		
		sys = (function(){
			
			return {
				
				components: {},
				
				modules: {},
				
				nav: (function(){
					
					var navigationList = [];
			
					return {
						
						get: function(){
							
							return navigationList;
							
						},
						
						set: function(listArray){

							appConfig.pageModuleList = listArray;
													
							navigationList = listArray;
							
							return true;
							
						}
						
					}
					
				})(),
				
				state: Factory.logState(true)
				
			};		
			
		})();
		
		return sys;
		
	}
	
	function createCSSSelector(moduleId){		
		return '.'+ moduleId;
	}
	
	function getCookie(cname){
		var name = cname + "=";
	    var ca = document ? document.cookie.split(';') : '';
	    for(var i = 0; i <ca.length; i++) {
	        var c = ca[i];
	        while (c.charAt(0)==' ') {
	            c = c.substring(1);
	        }
	        if (c.indexOf(name) == 0) {
	            return c.substring(name.length,c.length);
	        }
	    }
	    return "";
	}
	
	function ignoreEvent(){
		
		var events = $._data($(this.selector).get(0), 'events');
		this.properties._tmp_click = events.click;
		events.click = null;
							
	}
	
	function setCookie(cname, cvalue, ctime) {

		if (document) {

			// Don't allow the browser to touch any cookies
			// that should be managed on the back-end.
			if (
				cname === 'p_uid'
				|| cname === 'p_user_type'
				|| cname === 'p_series'
				|| cname === 'p_token'
				|| cname === 'p_instance'
			) {
				return;
			}

			var d = new Date();
			var expires = "expires="+ d;
			if (ctime !== 'now') {
				if (ctime) {
					d.setTime(d.getTime() + ctime * 1000);
				} else {
					d.setTime(d.getTime() + (30*24*60*60*1000)); // expires in 30 days
				}
				expires = "expires="+ d.toUTCString();
			}
			document.cookie = cname + "=" + cvalue + ";" + expires + ";domain="+ domain +";path=/";

		}

	}
	
	function patchDomHTML(domObjectPiece, selector){

		var domHTML = '';
		
		if(domObjectPiece.constructor == Object && domObjectPiece.hasOwnProperty('selector')){
		
			$(selector).append(domObjectPiece.html);
			
		}else{
			
			return true;
			
		}
		
		_.each(domObjectPiece, function(domObjectInnerPiece){
			
			if(domObjectInnerPiece.constructor == Object && domObjectInnerPiece.hasOwnProperty('selector')){
				
				patchDomHTML(domObjectInnerPiece, domObjectPiece.selector);
				
			}
			
		});
				
	}
	
	function patchDomHTMLNew(domObjectPiece, parentSelector){
		
		function parseHTML(htmlString, selector){

			var ret = {
				open: '',
				close: ''
			};
			
			// open
			var splitAtSelector = htmlString.split(selector.replace('.', '') + ' '),
				add = ' ';
			if(splitAtSelector.length < 2){
				splitAtSelector = htmlString.split(selector.replace('.', '') + '"');
				add = '"';
			}
			if(splitAtSelector.length < 2){
				splitAtSelector = htmlString.split(selector.replace('.', ''));
				add = '';
			}

			if(splitAtSelector.length > 2){
				
				for(var i=0; i<splitAtSelector.length - 1; i++){
					
					ret.open = ret.open + splitAtSelector[i] + selector.replace('.', '') + add;
				}
				ret.open = ret.open + splitAtSelector[splitAtSelector.length - 1].split('>')[0] + '>';
				
			}else{
				
				ret.open = splitAtSelector[0] + selector.replace('.', '') + add + splitAtSelector[1].split('>')[0] + '>';
				
			}
					
			// close
			ret.close = htmlString.replace(ret.open, '');			
			return ret;
			
		}
		
		var	html = {
			open: '',
			close: ''
		};

		if(domObjectPiece !== undefined && domObjectPiece.constructor == Object && domObjectPiece.hasOwnProperty('selector')){

			html = parseHTML(domObjectPiece.html, domObjectPiece.selector);
			
		}else{
			
			return html;
			
		}

		_.each(domObjectPiece, function(domObjectInnerPiece){

			if(domObjectInnerPiece != null && domObjectInnerPiece.constructor == Object && domObjectInnerPiece.hasOwnProperty('selector')){
				
				var temp = patchDomHTMLNew(domObjectInnerPiece, domObjectPiece.selector);
				html.open += temp.open + temp.close;
				
			}
			
		});
		
		return html;
				
	}
		
	function setDomObjListeners(listeners){

		_.each(listeners, function(listener, listenerSelector){
			
			$(listenerSelector).off(listener.eventType);

			$(listenerSelector).on(listener.eventType, function(e){
				
				// get classnames of the clicked element
				var classes = $(this).attr("class").split(' ');
				
				// get data to particular element; i.e., underscore.js is amazing
				var data = _.find(listener.datas, function(submittedData){
					
					return _.contains(classes, submittedData.selector.replace('.', ''));
					
				});
				
				// specific listeners
				if(typeof data !== 'undefined' && typeof data.data !== 'undefined'){
					data = data.data;
					
				// group listeners
				}else{
					data = listener.datas[0].data;
				}
				
				if($(this).attr('data-id')){
					
					if(typeof data == 'undefined'){
						data = {};
					}
					
					data.sender = $(this).attr('data-id');
					data.dataId = $(this).attr('data-id');
				}
				if($(this).attr('data-id2')){
					
					if(typeof data == 'undefined'){
						data = {};
					}
					
					data.sender2 = $(this).attr('data-id2');
					data.dataId2 = $(this).attr('data-id2');
				}
				
				var notObj = {
					type: listener.type,
					data: data
				};
				
				if(data != undefined){
					data.evt = e;
				}
				
				Factory.triggerEvent({
					type: listener.type,
					data: data
				}, listener.moduleId);
				
			});
			
		});
		
	}
	
	function getDomObjListeners (
		domObjectPiece
		, selector
		, listenersList
		, compId
	) {
		
		var keys = Object.keys(domObjectPiece);
		var i = 0;
		var key = keys[i];
		var domObjectInnerPiece = domObjectPiece[key];
		
		while (domObjectInnerPiece) {
			
			if (
				domObjectInnerPiece != null 
				&& domObjectInnerPiece !== undefined 
				&& domObjectInnerPiece.constructor == Object 
				&& domObjectInnerPiece.hasOwnProperty('selector') 
				&& key != 'notifications' 
				&& key != 'listeners'
			) {

				getDomObjListeners(domObjectInnerPiece, domObjectPiece.selector, listenersList, compId);
				
			} else if (
				domObjectInnerPiece != null 
				&& domObjectInnerPiece !== undefined 
				&& domObjectInnerPiece.constructor == Object 
				&& key == 'notifications' 
				&& !_.isEmpty(domObjectInnerPiece)
			){

				_.each(domObjectInnerPiece, function(notification, notificationType){
					
					if (
						typeof notification !== 'undefined' 
						&& notification.hasOwnProperty('selector')
					) {

						if (listenersList.hasOwnProperty(notification.selector)) {
						
							// remove if specific listener data exists already
							listenersList[notification.selector].datas = _.filter(
								listenersList[notification.selector].datas,
								function(datum){
									return datum.selector !== domObjectPiece.selector;
								}
							);
						
							listenersList[notification.selector].datas.push({
								selector: domObjectPiece.selector, 
								data: notification.notification.data,
								compInstanceId: compId
							});
							
						} else {

							if (
								!_.isEmpty(notification)
								&& notification.type
								&& notification.notification
							) {

								listenersList[notification.selector] = {
									eventType: notification.type,
									type: notification.notification.type,
									moduleId: notification.moduleId,
									datas: []
								};
								listenersList[notification.selector].datas.push({
									selector: domObjectPiece.selector, 
									data: notification.notification.data,
									compInstanceId: compId
								});

							}
							
						}
						
					}
					
				});
					
			// sets any listeners that aren't notifications
			} else if (
				domObjectInnerPiece != null 
				&& domObjectInnerPiece !== undefined 
				&& domObjectInnerPiece.constructor == Array 
				&& key == 'listeners'
			){

				_.each(domObjectInnerPiece, function(listener){
				
					listener(domObjectPiece.selector);
					
				});
				
			}
			
			i++;
			key = keys[i];
			domObjectInnerPiece = domObjectPiece[key];
			
		}
		
		return listenersList;

	}
	
	function getDragItems(domObjectPiece, dragItems){
		
		if(domObjectPiece != null && domObjectPiece !== undefined && domObjectPiece.constructor != Object && !domObjectPiece.hasOwnProperty('selector')){
		
			return dragItems;
			
		}
		
		if(domObjectPiece.hasOwnProperty('properties') && domObjectPiece.properties.drag){
			dragItems[domObjectPiece.selector] = domObjectPiece.properties.drag;
		}
		
		_.each(domObjectPiece, function(domObjectInnerPiece, key){

			if(domObjectInnerPiece != null && domObjectInnerPiece.constructor == Object && domObjectInnerPiece.hasOwnProperty('selector') && key != 'notifications' && key != 'listeners'){

				getDragItems(domObjectInnerPiece, dragItems);
				
			}
			
		});
		
		return dragItems;
		
	}
	
	function setDragObj(selector){
		
		function checkSelector(classNames, items){
			
			var classList = classNames.split(' ');
			for(var i in classList){
				if(items.hasOwnProperty('.'+ classList[i].replace(' ', ''))){
					return '.'+ classList[i].replace(' ', '');
				}
			}
			
		}
		
		var	elements = [],
			currentElement = {};
			
		for(var key in dragData.items){
			
			currentElement = document.querySelector(key);
			
			if(currentElement !== null){
				
				if(dragData.items[key].moves){
					elements.push(currentElement.parentElement);
				}
				if(dragData.items[key].accepts){
					elements.push(currentElement);
				}
				
			}else{
				
				delete dragData.items[key];
				
			}
			
		}
		
		elements = _.uniq(elements);
		
		if(dragData.hasOwnProperty('dragula')){
			dragData.dragula.destroy();
		}

		dragData.dragula = dragula(elements, {
			
			accepts: function (el, target, source, sibling) {
				
				var	selector = checkSelector(target.className, dragData.items),
					elSelector = checkSelector(el.className, dragData.items),
					targetSelector = checkSelector(target.className, dragData.items);
					
				// check that you aren't trying to drop into itself
				if(targetSelector === undefined){
					return false;
				}
				if(targetSelector.indexOf(elSelector) > -1){
					return false;
				}
				
				if(typeof dragData.items[selector] === 'object' && dragData.items[selector].hasOwnProperty('accepts') && typeof dragData.items[selector].accepts === 'boolean'){
					
					return dragData.items[selector].accepts;
					
				}else if(typeof dragData.items[selector] === 'object' && dragData.items[selector].hasOwnProperty('accepts') && typeof dragData.items[selector].accepts === 'function'){

					return dragData.items[selector].accepts(dragData.items[selector].data, dragData.items[elSelector].data);
					
				}

				return false;
				
			}, 
			moves: function(el, source, handle, sibling){
				
				var	selector = checkSelector(el.className, dragData.items);
				if(typeof dragData.items[selector] === 'object' && dragData.items[selector].hasOwnProperty('moves') && typeof dragData.items[selector].moves === 'boolean'){
					
					$(el).css('opacity', '1');
// 					$(el).css('box-shadow', '0px 0px 12px 1px gray');
					return dragData.items[selector].moves;
					
				}else if(typeof dragData.items[selector] === 'object' && dragData.items[selector].hasOwnProperty('moves') && typeof dragData.items[selector].moves === 'function'){

					return dragData.items[selector].moves(dragData.items[selector].data);
					
				}
				
				return false;
				
			},
			
			drag: function(){				
				return false;
			},
			
			copy: function(el, source){
				
				var selector = checkSelector(el.className, dragData.items);
				if(typeof dragData.items[selector] === 'object' && dragData.items[selector].hasOwnProperty('copy') && typeof dragData.items[selector].copy === 'boolean'){
					
					return dragData.items[selector].copy;
					
				}else if(typeof dragData.items[selector] === 'object' && dragData.items[selector].hasOwnProperty('copy') && typeof dragData.items[selector].copy === 'function'){

					return dragData.items[selector].copy(dragData.items[selector].data);
					
				}
				
				return false;
				
			}

		});
		
		dragData.dragula.on('drop', function(el, target, source, sibling){

			if(target == null){
				return false;
			}
			var	selector = checkSelector(el.className, dragData.items),
				elSelector = checkSelector(target.className, dragData.items),
				prevSelector = '',
				nextSelector = '';
				
			if ($(el) && $(el).length > 0) {
				$(el).removeClass('drag-picked-up');
			}
			
			if (el.previousSibling) {
				prevSelector = checkSelector(el.previousSibling.className, dragData.items);
			}
			if (sibling) {
				nextSelector = checkSelector(sibling.className, dragData.items);
			}

			if(dragData.items[selector].hasOwnProperty('drop')){
				
				clearInterval(TimerState.dragInterval);
				
				var droppedData = {},
					inData = {},
					prevData = {},
					nextData = {};
					
				if(dragData.items[selector].hasOwnProperty('data')){
					droppedData = dragData.items[selector].data;
				}
				if(dragData.items[elSelector].hasOwnProperty('data')){
					inData = dragData.items[elSelector].data;
				}
				if(nextSelector !== '' && dragData.items.hasOwnProperty(nextSelector) && dragData.items[nextSelector].hasOwnProperty('data')){
					nextData = dragData.items[nextSelector].data;
				}
				if(prevSelector !== '' && dragData.items.hasOwnProperty(prevSelector) && dragData.items[prevSelector].hasOwnProperty('data')){
					prevData = dragData.items[prevSelector].data;
				}
				
				if(typeof dragData.items[selector].drop == 'function'){
					
					dragData.items[selector].drop(inData, droppedData);
					
				}else{

					var updatedList = [];
					_.each($(el).parent().children(), function(value, key) {
						if (_.isNumber(key)) {
							$(value).attr('data-sort', key);
							updatedList.push({
								id: $(value).attr('data-id'),
								sort: key
							});
						}
					});
					
					Factory.triggerEvent({
						type: dragData.items[selector].drop, 
						data: {
							dropped: 		droppedData,
							in: 			inData,
							prev: 			prevData,
							next: 			nextData,
							instanceId: 	droppedData.instanceId,
							updatedList:	updatedList
						}
					});
					
				}
				
			}
			
		});

		dragData.dragula.on('drag', function(el, container, source){
			
			Factory.dragInterval = setInterval(function(){

			    var zone   = 250;
			    var speed  = 0.3;
			    			    
			    if ($(el) && $(el).length > 0 && !$('html, body').is(':animated')) {
			        
			        var bottom = $(window).height() - zone;
			        var rightEdge = $('.boardActual').width();
			        
			        $(el).addClass('drag-picked-up');
			        
			        if((cursorX-zone) > rightEdge){

				       $('.boardActual').animate({scrollLeft:  $('.boardActual').scrollLeft() + ((cursorX - zone) * speed)},500);
			      
			        }
			      
			        else if((cursorX - zone) < 200){
				  
				        $('.boardActual').animate({scrollLeft:  $('.boardActual').scrollLeft() * speed},500);
			      
			        }
			      
			        else {
			      
			            $('.boardActual').finish();
			      
			        }
			        
			    }
			}, 20);
			
			$(document).mouseup(function(e){                
			    clearInterval(Factory.dragInterval); 
			});
			
			return false;
			
		});
		
		dragData.dragula.on('over', function(el, container, source){
			
			if(container == null){
				return false;
			}
			
// 			$(container).addClass('drag-over');
			
			var	selector = checkSelector(el.className, dragData.items),
				elSelector = checkSelector(container.className, dragData.items);
				
			if(dragData.items[selector].hasOwnProperty('over')){
				
				var droppedData = {},
					inData = {};
					
				if(dragData.items[selector].hasOwnProperty('data')){
					droppedData = dragData.items[selector].data;
				}
				if(dragData.items[elSelector].hasOwnProperty('data')){
					inData = dragData.items[elSelector].data;
				}
				
				Factory.triggerEvent({
					type: dragData.items[selector].over, 
					data: {
						item: droppedData,
						container: inData,
						instanceId: droppedData.instanceId
					}
				});
				
			}
			
		});
		
		dragData.dragula.on('out', function(el, container, source){
			
			if(container == null){
				return false;
			}
			
			$(container).removeClass('drag-over');
			
			var	selector = checkSelector(el.className, dragData.items),
				elSelector = checkSelector(container.className, dragData.items),
				nextSelector = '';
				
			if(container){
				nextSelector = checkSelector(container.className, dragData.items);
			}

			if(dragData.items[selector].hasOwnProperty('out')){
				
				var droppedData = {},
					inData = {};
					
				if(dragData.items[selector].hasOwnProperty('data')){
					droppedData = dragData.items[selector].data;
				}
				if(dragData.items[elSelector].hasOwnProperty('data')){
					inData = dragData.items[elSelector].data;
				}
				
				Factory.triggerEvent({
					type: dragData.items[selector].out, 
					data: {
						item: droppedData,
						container: inData,
						instanceId: droppedData.instanceId
					}
				});
				
			}
			
		});
		
		dragData.dragula.on('cancel', function(el, target, source, sibling){

			if(target == null){
				return false;
			}
			var	selector = checkSelector(el.className, dragData.items),
				elSelector = checkSelector(target.className, dragData.items),
				nextSelector = '';
				
			if ($(el) && $(el).length > 0) {
				$(el).removeClass('drag-picked-up');
			}
			
		});        
		
/*
		dragData.dragula.on('out', function(el, container, source){
			
			$(el).css('opacity', '1');
			$(el).css('box-shadow', '5px 10px 18px #888888');
			console.log(1406, el, $(el));
			
		});
*/
		
		/*
dragData.dragula.on('shadow', function(el, container, source){
			
// 			$(container).css('box-shadow', 'inset 0px 0px 24px 1px gray');
// 			$(container).css('background-color', 'gray');
			$(container).addClass('drag-over');
			
			$(el).css('opacity', '.8');
// 			$(el).css('background-color', 'gray');
			$(el).css('box-shadow', 'none');
			console.log(1406, el, $(el));
			
		});
*/
		
// 		console.log(dragData);
		
	}
	
	function updateCss(newClass, replace){
		
		if(!replace){
			replace = true;
		}
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		
		var remove_classes = this.properties.args.css.split(' ');
		
		for(i=0; i<remove_classes.length; i++){

			if(el && remove_classes[i] !== ''){
				el.classList.remove(remove_classes[i]);
			}
			
		}
		
		var add_classes = newClass.split(' ');
		for(i=0; i<add_classes.length; i++){
			
			if(el && add_classes[i] !== ''){
				el.classList.add(add_classes[i]);
			}
			
		}
		
		if(replace){
			this.properties.args.css = newClass;
		}
		
	}
	
	function updateStyle(newStyle){
		
		var elem = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		elem.setAttribute("style",newStyle);
		
	}
	
	function isGroupManager(userId, groupObj) {

		if (userId) {

			// Check appConfig for hq managers
			if (appConfig) {
				if ( appConfig.hasOwnProperty('headquarters') ) {
					if (appConfig.headquarters) {
						if ( appConfig.headquarters.hasOwnProperty('managers') ) {
							if (appConfig.headquarters.managers) {
								if (_.contains(appConfig.headquarters.managers, userId)) {
									return true;
								}
							}
						}
					}
				}
			}
			
			// Check groupObj managers list
			if (groupObj) {
				if ( groupObj.hasOwnProperty('managers') ) {
					if (groupObj.managers) {
						if ( _.chain(groupObj.managers).pluck('id').contains(userId).value() || groupObj.managers.includes(userId) ) {
							return true;
						}
					}
				}
				if ( groupObj.hasOwnProperty('user') ) {
					if (groupObj.user) {
						if ( groupObj.user === userId ) {
							return true;
						}
					}
				}
				// Check tagged_with value on a record
				if (
					typeof groupObj.object_bp_type === 'string'
					&& groupObj.object_bp_type.startsWith('#')
				) {
					if (
						Array.isArray(groupObj.tagged_with)
						&& _.contains(groupObj.tagged_with, userId)
					) {
						return true;
					}
				}

			}

		}
		
		return false;
		
	}

	function isGroupMember(userId, groupObj) {

		if(groupObj.allowed_users){
			
			if(groupObj.allowed_users.indexOf(userId) > -1){
				return true;
			}
			
		}
		
		if (_.findWhere(groupObj.tagged_with, {id: userId})) {
			return true;
		}
		
		if(groupObj.tagged_with){
			
			if(groupObj.tagged_with.indexOf(userId) > -1){
				return true;
			} 
			
		}
				
		if (isGroupManager(userId, groupObj)) {
			return true;
		}
		
		return false;
		
	}

	// Set timezone offset in cookie, so that the back-end can be aware of it
	setCookie('tz_off', new Date().getTimezoneOffset());
				
	return {
		
		appendNode: function(name, newNode){
			
			this[name] = newNode;
			
		},
		
		dragData: dragData,
		
		emptyNode: function(){
				
			for(var index in this){
				
				if(typeof this[index].html !== 'undefined'){
					
					delete this[index];
					
				}
				
			}
			
		},
		
		getNodeHTML: function(node){
			
			return patchDomHTMLNew(node);
			
		},
		
		setChildNodeListeners: function(node){
			
			var listeners = {};
			_.each(node, function(domObjectPiece){
				listeners = getDomObjListeners(domObjectPiece, domObjectPiece.selector, listeners);
			});
			setDomObjListeners(listeners);
			
		},
		
		patchNode: function(){
			
// var one = new Date();
			var selector = this.selector;
			$(selector).empty();
			
			_.each(this, function(domObjectPiece){
				
				// new way of placing dom on page
				if(1 === 1/* getCookie('uid') === '663' */){
					
					var html = patchDomHTMLNew(domObjectPiece, selector);
					$(selector).append(html.open + html.close);
				
				// old way, just in case
				}else{
					patchDomHTML(domObjectPiece, selector, true);
				}
				
			});
			
// 			var listeners = listenerData;
			var listeners = {};
			
			var compId = 'module';
			if(this.properties !== undefined && this.properties.hasOwnProperty('compInstanceId')){
				compId = this.properties.compInstanceId;
			}
// var two = new Date();
			_.each(this, function(domObjectPiece){
				listeners = getDomObjListeners(domObjectPiece, selector, listeners, compId);
			});
// var three = new Date();
			setDomObjListeners(listeners);
// var four = new Date();
			var dragItems = getDragItems(this, {});
			
			if(!_.isEmpty(dragItems)){
				
				if(!dragData.hasOwnProperty('items')){
					dragData.items = {};
				}
				
				for(var key in dragItems){
					dragData.items[key] = dragItems[key];
					if(!dragData.items[key].hasOwnProperty('data')){
						dragData.items[key].data = {};
					}
				}
				setDragObj();
				
			}
// var five = new Date();
// var tot = five - one;

// console.log(
// 	'patchNode::'+ this.selector +'\n'+
// 	'html:: 				'+ (two - one) +'ms ('+ ((two - one)/tot)*100 +'%)\n'+
// 	'getlisteners:: 		'+ (three - two) +'ms ('+ ((three - two)/tot)*100 +'%)\n'+
// 	'applylisteners:: 		'+ (four - three) +'ms ('+ ((four - three)/tot)*100 +'%)\n'+
// 	'drag:: 				'+ (five - four) +'ms ('+ ((five - four)/tot)*100 +'%)\n'+
// 	'TOTAL:: 				'+ tot +'ms\n'+
// 	'#LISTENERS::			'+ Object.keys(listeners).length
// );

			return true;
			
		},
		
		makeNode: function(name, tool, args){

			if(domTools[tool].make){
				
				this[name] = domTools[tool].make(this.selector, args, name, this);
				this[name].empty = Sandbox.emptyNode;
				this[name].ignore = ignoreEvent;
				this[name].css = updateCss;
				this[name].style = updateStyle;
				this[name].addClass = function(className){ $(this.selector).addClass(className); };
				this[name].removeClass = function(className){ $(this.selector).removeClass(className); };
				
				if(this.hasOwnProperty('properties') && this.properties.hasOwnProperty('compInstanceId')){
					
					if(!this[name].hasOwnProperty('properties')){
						
						this[name].properties = {
							compInstanceId: this.properties.compInstanceId,
							args: args
						};
						
					}else{
						
						this[name].properties.compInstanceId = this.properties.compInstanceId;
						this[name].properties.args = args;
						
					}
					
				}else{
					
					if(!this[name].hasOwnProperty('properties')){
						this[name].properties = {
							args: args
						};
					}
					
				}
				
				// draggability
				if(typeof args === 'object' && args.drag){
					this[name].properties.drag = args.drag;
				}
				if(typeof args === 'object' && args.drop){
					this[name].properties.drop = args.drop;
				}
				
			}
			
			return this[name];
			
		},
		
		makeNotification: function(type, notificationObj, moduleId, listenForChildNodes){
			
			args = {
				type: type,
				notification: notificationObj
			};

			var notificationSelector = '.'+ notificationObj.type +'-'+ type +'-'+ moduleId +'-'+ this.selector.replace('.', '');
			var newhtml;
			
			if(args.notification.data === null){
				args.notification.data = {};
			}
			
			if(this.hasOwnProperty('properties') && this.properties.hasOwnProperty('compInstanceId')){

				args.notification.data.instanceId = this.properties.compInstanceId;
				notificationSelector += '-'+ this.properties.compInstanceId;
			}
			
			if(listenForChildNodes){
				
				for(var key in this){ 
					
					if(this[key].constructor == Object && this[key].hasOwnProperty('html')){
						
						newhtml = this[key].html.split(this[key].selector.replace('.', ''))[0] + this[key].selector.replace('.', '') +' '+ notificationSelector.replace('.', '') +' '+ this[key].html.split(this[key].selector.replace('.', ''))[1];
						this[key].html = newhtml;

					}

				}
				
			}else{
				
				if(this.hasOwnProperty('html')){
					
					newhtml = this.html.split(this.selector.replace('.', ''))[0] + this.selector.replace('.', '') +' '+ notificationSelector.replace('.', '') +' '+ this.html.split(this.selector.replace('.', ''))[1];
					
					// if there are more splits
					if(this.html.split(this.selector.replace('.', '')).length > 2){
						for(i=2; i<this.html.split(this.selector.replace('.', '')).length; i++){
							newhtml += this.html.split(this.selector.replace('.', ''))[i];
						}
					}
					this.html = newhtml;
					
				}
				
			}
			
			if(!this.hasOwnProperty('notifications')){
				this.notifications = {};
			}
			this.notifications[type] = domTools.listener.make(notificationSelector, args, moduleId);

		},
		
		loading: function(loading) {
		
			if (loading === undefined) {
				loading = true;
			}
	
			if (loading) {

				$(this.selector).addClass('loading prevent-click disabled'); 
				$(this.selector).prop('disabled', true);
				
			} else {
				
				$(this.selector).removeClass('loading prevent-click disabled');
				$(this.selector).prop('disabled', false);
							
			}
			
		},

		releaseComponentListeners: function(compInstanceId){

			_.each(listenerData, function(listenerDatum, evt){
				
				listenerDatum.datas = _.filter(listenerDatum.datas, function(l){
					return l.compInstanceId !== compInstanceId;
				});
				
				if(listenerDatum.datas.length === 0){
					delete listenerData[evt];
				}
				
			});
			
		},
		
		create: function(Factory, moduleId, type, instanceId){

			var API = {};
			
			// use switch block to restrict modules access to sandbox tools
			switch(type){
				
				case 'component':
				
					return {
						
						comm: buildCommunicationsTools(),
						
						createComponent: function(compId){

							return Factory.createComponentInstance(compId, moduleId);
							
						},
						
						data: buildDataTools(moduleId),
						
						dom: buildDOMTools(moduleId, instanceId),						
						
						moduleId: moduleId,
						
						obj: buildObjectsExtension(),
						
						notify: function (evt) {

							if(!evt.hasOwnProperty('data') || evt.data === null){
								evt.data = {};
							}

							evt.data.instanceId = instanceId;
						    Factory.triggerEvent(evt, moduleId, instanceId);
						    
						}, 
						
						listen: function (evts) { 
						    Factory.registerComponentEvents(evts, moduleId);             
						},  
												
						ignore: function (evts) { 
						    Factory.removeEvents(evts, moduleId);           
						},
						
						instanceId: instanceId,
						
						sys: buildSystemTools(Factory),
						
						start: function(){
							Factory.start(moduleId);
						},
						
						stop: function(){
							Factory.stop(moduleId);
						},
						
						permissions: {
							isGroupManager: isGroupManager,
							isGroupMember: isGroupMember
						},

						url: url
									
					};
				
					break;
				
				default:
				
					return {

						bucket: 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/',
						
						comm: buildCommunicationsTools(),
						
						createComponent: function(compId){
							
							return Factory.createComponentInstance(compId, moduleId);
							
						},
						
						data: buildDataTools(moduleId),
						
						dom: buildDOMTools(moduleId),						
						
						moduleId: moduleId,
						
						obj: buildObjectsExtension(),
						
						notify: function (evt) { 
						    Factory.triggerEvent(evt, moduleId);
						}, 
						
						listen: function (evts) { 
						    Factory.registerEvents(evts, moduleId);             
						},  
												
						ignore: function (evts) { 
						    Factory.removeEvents(evts, moduleId);           
						},
						
						sys: buildSystemTools(Factory),
						
						start: function(){
							Factory.start(moduleId);
						},
						
						stop: function(){
							Factory.stop(moduleId);
						},
						
						permissions: {
							isGroupManager: isGroupManager,
							isGroupMember: isGroupMember
						},

						url: url
									
					};
				
			}
			
		},
		
		verifyDomToolArgs: function(args, defaultArgs){
			
			if(typeof args === 'undefined'){
				args = {};
			}
			
			_.each(defaultArgs, function(defaultArg, argName){
				
				if(defaultArg.argType == 'option'){

					if(_.indexOf(defaultArg.options, args[argName]) == -1){
						args[argName] = defaultArg.defaultValue;
					}
					
				}else if(defaultArg.argType == 'int'){
					
					if(typeof args[argName] === 'undefined'){
						
						args[argName] = defaultArg.defaultValue;
						
					}else if(args[argName] !== parseInt(args[argName], 10)){
						
						args[argName] = defaultArgs.defaultValue;
						
					}
					
				}
					
				if(typeof args[argName] === 'undefined'){
					args[argName] = defaultArg.defaultValue;
				}
					
				
			});
			
			return args;

		}
						
	};
	
}());

if (window) {

	/*
	A simple, lightweight jQuery plugin for creating sortable tables.
	https://github.com/kylefox/jquery-tablesort
	Version 0.0.11
	*/

	(function($) {
		$.tablesort = function ($table, settings) {
			var self = this;
			this.$table = $table;
			this.$thead = this.$table.find('thead');
			this.settings = $.extend({}, $.tablesort.defaults, settings);
			this.$sortCells = this.$thead.length > 0 ? this.$thead.find('th:not(.no-sort)') : this.$table.find('th:not(.no-sort)');
			this.$sortCells.on('click.tablesort', function() {
				self.sort($(this));
			});
			this.index = null;
			this.$th = null;
			this.direction = null;
		};

		$.tablesort.prototype = {

			sort: function(th, direction) {
				var start = new Date(),
					self = this,
					table = this.$table,
					rowsContainer = table.find('tbody').length > 0 ? table.find('tbody') : table,
					rows = rowsContainer.find('tr').has('td, th'),
					cells = rows.find(':nth-child(' + (th.index() + 1) + ')').filter('td, th'),
					sortBy = th.data().sortBy,
					sortedMap = [];

				var unsortedValues = cells.map(function(idx, cell) {
					if (sortBy)
						return (typeof sortBy === 'function') ? sortBy($(th), $(cell), self) : sortBy;
					return ($(this).data().sortValue != null ? $(this).data().sortValue : $(this).text());
				});
				if (unsortedValues.length === 0) return;

				//click on a different column
				if (this.index !== th.index()) {
					this.direction = 'asc';
					this.index = th.index();
				}
				else if (direction !== 'asc' && direction !== 'desc')
					this.direction = this.direction === 'asc' ? 'desc' : 'asc';
				else
					this.direction = direction;

				direction = this.direction == 'asc' ? 1 : -1;

				self.$table.trigger('tablesort:start', [self]);
				self.log("Sorting by " + this.index + ' ' + this.direction);

				// Try to force a browser redraw
				self.$table.css("display");
				// Run sorting asynchronously on a timeout to force browser redraw after
				// `tablesort:start` callback. Also avoids locking up the browser too much.
				setTimeout(function() {
					self.$sortCells.removeClass(self.settings.asc + ' ' + self.settings.desc);
					for (var i = 0, length = unsortedValues.length; i < length; i++)
					{
						sortedMap.push({
							index: i,
							cell: cells[i],
							row: rows[i],
							value: unsortedValues[i]
						});
					}

					sortedMap.sort(function(a, b) {
						return self.settings.compare(a.value, b.value) * direction;
					});

					$.each(sortedMap, function(i, entry) {
						rowsContainer.append(entry.row);
					});

					th.addClass(self.settings[self.direction]);

					self.log('Sort finished in ' + ((new Date()).getTime() - start.getTime()) + 'ms');
					self.$table.trigger('tablesort:complete', [self]);
					//Try to force a browser redraw
					self.$table.css("display");
				}, unsortedValues.length > 2000 ? 200 : 10);
			},

			log: function(msg) {
				if(($.tablesort.DEBUG || this.settings.debug) && console && console.log) {
					console.log('[tablesort] ' + msg);
				}
			},

			destroy: function() {
				this.$sortCells.off('click.tablesort');
				this.$table.data('tablesort', null);
				return null;
			}

		};

		$.tablesort.DEBUG = false;

		$.tablesort.defaults = {
			debug: $.tablesort.DEBUG,
			asc: 'sorted ascending',
			desc: 'sorted descending',
			compare: function(a, b) {
				if (a > b) {
					return 1;
				} else if (a < b) {
					return -1;
				} else {
					return 0;
				}
			}
		};

		$.fn.tablesort = function(settings) {
			var table, sortable, previous;
			return this.each(function() {
				table = $(this);
				previous = table.data('tablesort');
				if(previous) {
					previous.destroy();
				}
				table.data('tablesort', new $.tablesort(table, settings));
			});
		};

	})(window.Zepto || window.jQuery);

}

if (IN_NODE_ENV) {
	_sandbox.sb = Sandbox;
} 