var bsImages = (function(){
	
	return {
				
		make: function(selector, args, name){
			
			if(typeof args.style === 'undefined'){args.style = '';}
			if(typeof args.width !== 'undefined'){args.width = 'width="'+ args.width +'"';}else{ args.width = ''; }
			if(typeof args.height !== 'undefined'){args.height = 'height="'+ args.height +'"';}else{ args.height = ''; }
			
			var imageObj = {
				selector: selector +'-img-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};
			
			imageObj.html = '<img src="'+ args.url +'" class="'+ imageObj.selector.replace('.', '') +' '+ args.css +'" '+ args.width +' '+ args.height +' style="'+ args.style +'">';
			
			return imageObj;
			
		}
		
	}
	
})();