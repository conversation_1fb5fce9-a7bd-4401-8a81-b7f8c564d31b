var listeners = (function(){
	
	return {
				
		make: function(selector, args, moduleId){
			
			var ret = {
				selector: selector,
				type: args.type,
				notification: args.notification,
				moduleId: moduleId
			};
			
			return ret;			
		},
		
		setup: function(){
			
			return {
				
				type: {
					argType: 'option',
					options: ['click', 'change'],
					defaultValue: 'click',
					description: 'The event to listen for.'
				}, notification: {
					argType: 'object',
					defaultValue: '',
					description: 'The notification object.'
				}
			}
		}
						
	}
	
})();