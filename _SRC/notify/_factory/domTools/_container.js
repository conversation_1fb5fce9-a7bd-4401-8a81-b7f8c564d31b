var containers = (function(){
	
	function toggleContent(el){
		
		$(el).accordion('toggle', 0);
		
	}
	
	function hideContainer(){

		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		if(el){
			el.className = el.className + ' hidden';
		}

	}
	
	function showContainer(){
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		if(el){
			el.className = el.className.replace(/hidden/g, '');
		}
		
	}
	
	return {
				
		make: function(selector, args, name){
			
			args = Sandbox.verifyDomToolArgs(args, this.setup());

			var containerObj = {
				selector: selector +'-container-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				listeners: [],
				html: '',
				show: showContainer,
				hide: hideContainer
			};
			
			if(args.hasOwnProperty('collapse')){
				
				containerObj.toggle = toggleContent;
				containerObj.listeners.push(function(selector){
					
					$(selector.replace('.', '#') +'-toggle').accordion();
					
				});
				
			}
			
			var headerSize = 4;
			if(args.hasOwnProperty('headerSize')){
				
				switch(args.headerSize){
					
					case 'xx-small':
						headerSize = 5;
						break;
					
					case 'x-small':
						headerSize = 4;
						break;
					
					case 'small':
						headerSize = 3;
						break;
						
					case 'medium':
						headerSize = 2;
						break;
						
					case 'large':
						headerSize = 1;
						break;
					
				}
				
			}
			
			var hiddenString = '';
			var gridString = 'ui stackable grid ';
			
			if(args.hasOwnProperty('uiGrid')){
				if(args.uiGrid === false){
					gridString = '';
				}
			}
			
			if(args.hasOwnProperty('collapse') && args.collapse !== false){
				
				var subTitle = '',
					subTitleBreak = '';
				if(args.hasOwnProperty('subTitle')){
					subTitle = '<br />'+ args.subTitle +'';
					subTitleBreak = '<br />';
				}
				
				var activeText = '';
				var hiddenText = 'hidden';
				if(args.collapse === true || args.collapse === 'open'){
					activeText = 'active';
					hiddenText = '';
				}
				
				containerObj.html = 
					'<div id="'+ containerObj.selector.replace('.', '') +'-toggle" class="ui styled fluid accordion">'+
						'<div class="'+ activeText +' title">'+
							'<i class="dropdown icon"></i>'+
							args.title + subTitle +
						'</div>'+
						'<div class="'+ activeText +' content">'+
							'<div class="transition '+ hiddenText +' '+ containerObj.selector.replace('.', '') +'">'+
							'</div>'+
							'<br />'+
						'</div>'+
					'</div>'+
					'<div class="sixteen wide column noMobileHeight"><br /></div>';
				
				/*
var headerStyle = '';
				if(args.hasOwnProperty('headerStyle')){
					headerStyle = args.headerStyle;
				}
				
				var subTitle = '',
					subTitleBreak = '';
				if(args.hasOwnProperty('subTitle')){
					subTitle = '<br /><small><small>'+ args.subTitle +'</small></small>';
					subTitleBreak = '<br />';
				}
				
				containerObj.html = 
			
					'<div id="'+ containerObj.selector.replace('.', '') +'" style="cursor:pointer;'+ headerStyle +' padding-left:10px;"><h'+ headerSize +' class="pull-left">'+ args.title +''+ subTitle +'</h'+ headerSize +'>';
				
				if(args.collapse === true || args.collapse === 'open'){
					
					containerObj.html += '<button id="'+ containerObj.selector.replace('.', '') +'-btn" data-toggle="open" class="pull-right btn pda-btn pda-btnOutline pda-transparent"><i class="fa fa-chevron-down" aria-hidden="true"></i></button>';
					
				}else if(args.collapse === 'closed'){
					
					containerObj.html += '<button id="'+ containerObj.selector.replace('.', '') +'-btn" data-toggle="closed" class="pull-right btn pda-btn pda-btnOutline pda-transparent"><i class="fa fa-minus" aria-hidden="true"></i></button>';
					hiddenString = 'hidden';
					
				}
				
				containerObj.html +=
				
					'</div>'+ subTitleBreak +'<div class="sixteen wide column clearfix"><br /><hr style="border-top:1px solid gray;"></div>';
				
				if(args.collapse === 'closed'){
					
					containerObj.html +=
					
						'<div class="'+ gridString +'table-accordian-item '+ containerObj.selector.replace('.', '') +' '+ args.css +' '+ hiddenString +'" style="'+ args.style +'" data-id="'+ args.dataId +'" data-id2="'+ args.dataId2 +'" data-id3="'+ args.dataId3 +'"></div>';
					
				}else{
					
					containerObj.html +=
					
						'<div class="'+ gridString + containerObj.selector.replace('.', '') +' '+ args.css +' '+ hiddenString +'" style="'+ args.style +'" data-id="'+ args.dataId +'" data-id2="'+ args.dataId2 +'" data-id3="'+ args.dataId3 +'"></div>';
					
				}
*/
				
				
			}else{
				
				containerObj.html +=
					
					'<div class="'+ gridString + containerObj.selector.replace('.', '') +' '+ args.css +' '+ hiddenString +'" style="'+ args.style +'" data-id="'+ args.dataId +'" data-id2="'+ args.dataId2 +'" data-id3="'+ args.dataId3 +'"></div>';
				
			}			
						
			return containerObj;
			
		},
		
		setup: function(){
			
			return {
				
				css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}, dataId: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the button here (can be used in notifications).'
				}, dataId2: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the button here (can be used in notifications).'
				}, dataId3: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the button here (can be used in notifications).'
				}, style: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional styles here'
				}
				
			}
			
		}
		
	}
	
})();