var bsColumns = (function(){
	
	function adjustWidth(width){
		
		var widthClasses = [
			'',
			'one',
			'two',
			'three',
			'four',
			'five',
			'six',
			'seven',
			'eight',
			'nine',
			'ten',
			'eleven',
			'twelve',
			'thirteen',
			'fourteen',
			'fifteen',
			'sixteen'
		];
		
		var newClass = widthClasses[width];
		$(this.selector).removeClass(this.properties.lastWidth +' wide column').addClass(widthClasses[width] +' wide column');
		
	}
	
	function hideColumn(){

		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		el.className = el.className + ' hidden';

	}
	
	function showColumn(){
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		el.className = el.className.replace(/hidden/g, '');
		
	}
	
	return {
				
		make: function(selector, columnArgs, name){
			
/*
			var widthClasses = [
					'',
					'one wide column',
					'two wide column',
					'three wide column',
					'four wide column',
					'five wide column',
					'six wide column',
					'seven wide column',
					'eight wide column',
					'nine wide column',
					'ten wide column',
					'eleven wide column',
					'twelve wide column'
				];
*/

			var widthClasses = [
					'',
					'one wide column', // 1
					'two wide column', // 2
					'four wide column', // 3
					'six wide column', // 4
					'seven wide column', // 5
					'eight wide column', // 6
					'nine wide column', // 7
					'eleven wide column', // 8
					'twelve wide column', // 9
					'fourteen wide column', // 10
					'fifteen wide column', // 11
					'sixteen wide column' // 12
				];
				
			var wideClasses = [
				'',
				'one',// 1
				'two',// 2
				'three',// 3
				'four',// 4
				'five',// 5
				'six',// 6
				'seven',// 7
				'eight',// 8
				'nine',// 9
				'ten',// 10
				'eleven',// 11
				'twelve',// 12
				'thirteen',// 13
				'fourteen',// 14
				'fifteen',// 15
				'sixteen',// 16
			];

			
			if(typeof columnArgs.style === 'undefined'){columnArgs.style = '';}
			if(typeof columnArgs.dataId === 'undefined'){columnArgs.dataId = '';}
			columnArgs = Sandbox.verifyDomToolArgs(columnArgs, this.setup());
			
			var columnObj = {
				selector: selector +'-col-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				width: adjustWidth,
				show: showColumn,
				hide: hideColumn
			};
						
			//columnArgs.offset
			
			var columnCSS = widthClasses[columnArgs.width];
			if(columnArgs.w){
				columnCSS = wideClasses[columnArgs.w] +' wide column';
				columnObj.properties = {
					lastWidth: wideClasses[columnArgs.w]
				};
			}
			
			columnObj.html = '<div class="'+ columnCSS +' '+ columnArgs.css +' col-'+ columnArgs.type +' '+ columnObj.selector.replace('.', '') +'" style="'+ columnArgs.style +'" data-id="'+ columnArgs.dataId +'"></div>';
			
			//columnObj.html = '<div class="tile tile-w-'+ columnArgs.width +' '+ columnArgs.css +' col-'+ columnArgs.type +'-offset-'+ columnArgs.offset +' '+ columnObj.selector.replace('.', '') +'" style="'+ columnArgs.style +'" data-id="'+ columnArgs.dataId +'"></div>';
			
			return columnObj;
			
		},
		
		setup: function(){
			
			return {
				
				type: {
					argType: 'option',
					options: ['xs', 'sm', 'md', 'lg'],
					defaultValue: 'sm',
					description: 'The device type the column is meant for.'
				}, css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}, width: {
					argType: 'option',
					defaultValue: '12',
					options: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
					description: 'The width of the column, between 1 and 12, where 12 is full width.'
				}, offset: {
					argType: 'option',
					defaultValue: 0,
					options: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
					description: 'The left offset of the column, between 1 and 11. Leave blank for no offset.'
				}
				
			}
			
		}
	}
	
})();