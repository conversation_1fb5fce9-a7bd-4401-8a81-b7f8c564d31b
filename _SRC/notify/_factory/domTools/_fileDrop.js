var fileDrop = (function(){
	
	return {
		
		make: function(selector, args, name){
			
			if(typeof args.style === 'undefined'){args.style = '';}
			if(typeof args.css === 'undefined'){args.css = '';}
			if(typeof args.receiveFile === 'undefined'){args.receiveFile = function(){};}
			if(typeof args.dataId === 'undefined'){args.dataId = '';}
			if(typeof args.dataId2 === 'undefined'){args.dataId2 = '';}
			
			var fileDropObj = {
				selector: selector +'-file-drop-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				listeners: [
					
					function(){
						
						$(selector +'-file-drop-'+ name).on('drag dragstart dragend dragover dragenter dragleave drop', function(e){
							e.preventDefault();
							e.stopPropagation();
						})
							.on('dragover dragenter', function() {
								$(selector +'-file-drop-'+ name).addClass('bg-warning');
							})
							.on('dragleave dragend drop', function() {
								$(selector +'-file-drop-'+ name).removeClass('bg-warning');
							})
							.on('drop', function(e) {
								
								args.receiveFile(e.originalEvent.dataTransfer.files);
// 								droppedFiles = e.originalEvent.dataTransfer.files;
							});
						
					}
					
				]
			};
			
			fileDropObj.html = 
			
				'<form class="'+ fileDropObj.selector.replace('.', '') +' '+ args.css +'" style="'+ args.style +'" enctype="multipart/form-data" data-id="'+ args.dataId +'" data-id2="'+ args.dataId2 +'">'+
					'<input class="'+ fileDropObj.selector.replace('.', '') +'-box" type="file" style="display: none;" name="files[]" id="file"/>'+
				'</form>';
			
			return fileDropObj;
			
		}
		
	}
	
})();