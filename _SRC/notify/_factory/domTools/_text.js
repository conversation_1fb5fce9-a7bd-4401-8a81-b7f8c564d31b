var bsText = (function(){
	
	function updateText(newText, replace){
		
		if(replace === undefined){
			replace = true;
		}
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		el.innerHTML = newText;
		
		if(replace){
			this.text = newText;
		}
		
	}
	
	return {
		
		make: function(selector, args, name){
			
			// verify input arguments
			args = Sandbox.verifyDomToolArgs(args, this.setup());
			
			// create dom node object
			var text = {
				selector: selector +'-text-'+ name,
				text: args.text,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				updateText:updateText
			};
			
			if(typeof args.dataId === 'undefined') args.dataId = '';
			if(typeof args.dataId2 === 'undefined') args.dataId2 = '';

			// add create html
			
			if(args.hasOwnProperty('span')){
				text.html = '<span class="'+ text.selector.replace('.', '') +' '+ args.css +'">'+args.text+'</span>';				
			}else{
				text.html = '<p class="'+ text.selector.replace('.', '') +' '+ args.css +'" data-id="'+ args.dataId +'" data-id2="'+ args.dataId2 +'" style="'+ args.style +'">'+ args.text +'</p>';				
			}
									
			return text;
			
		},
		
		setup: function(){
			
			return {
				text: {
					argType: 'free-input',
					defaultValue: '',
					description: 'the text to be displayed'
				}, css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place any extra css class names for this element here'
				}, tag: {
					argType: 'free-input',
					defaultValue: 'hd',
					description: 'Place a unique tag here to distinguish this text from other text elements within the same container'
				}, dataId: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the text here (can be used in notifications).'
				}, style: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place any extra css for this element here'
				}

			};
			
		}

	}
	
})();