var headerText = (function(){
	
	function updateText(newText, replace){
		
		if(replace === undefined){
			replace = true;
		}
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		el.innerHTML = newText;
		
		if(replace){
			this.text = newText;
		}
		
	}
	
	return {
		
		make: function(selector, args){

			// verify input arguments
			args = Sandbox.verifyDomToolArgs(args, this.setup());
			
			// create dom node object
			var headerText = {
				selector: selector +'-break-'+ args.tag,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				updateText:updateText
			};
			
			// parse align argument into class
			switch(args.align){
				case 'left':
					args.align = 'pull-left';
					break;
				
				case 'center':
					args.align = 'text-center';
					break;
				
				case 'right':
					args.align = 'pull-right';
					break;
					
				case '':
					break;
			}

			// add html field
			switch(args.size){
				
				case 'xx-small':
					headerText.html = '<h5 class="ui header '+ headerText.selector.replace('.', '') +' '+ args.align +' '+ args.css +'" data-id="'+ args.dataId +'" style="'+ args.style +'">'+ args.text +'</h5>';
					break;
				case 'x-small':
					headerText.html = '<h4 class="ui header '+ headerText.selector.replace('.', '') +' '+ args.align +' '+ args.css +'" data-id="'+ args.dataId +'" style="'+ args.style +'">'+ args.text +'</h4>';
					break;
				case 'small':
					headerText.html = '<h3 class="ui header '+ headerText.selector.replace('.', '') +' '+ args.align +' '+ args.css +'" data-id="'+ args.dataId +'" style="'+ args.style +'">'+ args.text +'</h3>';
					break;
				case 'medium':
					headerText.html = '<h2 class="ui header '+ headerText.selector.replace('.', '') +' '+ args.align +' '+ args.css +'" data-id="'+ args.dataId +'" style="'+ args.style +'">'+ args.text +'</h2>';
					break;
				case 'large':
					headerText.html = '<h1 class="ui header '+ headerText.selector.replace('.', '') +' '+ args.align +' '+ args.css +'" data-id="'+ args.dataId +'" style="'+ args.style +'">'+ args.text +'</h1>';
					break;
				default: 
					break;
			}
			
			return headerText;
			
		},
		
		setup: function(){
			
			return {
				text: {
					argType: 'free-input',
					defaultValue: '',
					description: 'the text to be displayed'
				}, align: {
					argType: 'option',
					options: ['left', 'center', 'right', ''],
					defaultValue: '',
					description: 'The alignment of the text'
				}, css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place any extra css for this element here'
				}, dataId: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the button here (can be used in notifications).'
				}, tag: {
					argType: 'free-input',
					defaultValue: 'hd',
					description: 'Place a unique tag here to distinguish this header texts from other header texts within the same container'
				}, size: {
					argType: 'option',
					options: ['xx-small', 'x-small', 'small', 'medium', 'large'],
					defaultValue: 'medium',
					description: 'The size of the header text'
				}, style: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place any extra css for this element here'
				}

			};
			
		}

		
	}
	
})();