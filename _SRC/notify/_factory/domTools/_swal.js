var alerts = (function() {
	
	return {
		
		alert: function(text, subtext, icon, html, callback, allowOutSiteClick = false, showCloseButton = false, confirmButtonText = 'OK'){
			
			if( typeof icon === undefined ) icon = 'warning';
			if( typeof html === undefined ) html = false;
			
			Swal.fire({
				title:text,
				text:subtext,
				icon:icon,
				html:html,
				heightAuto: false,
				allowOutsideClick: allowOutSiteClick,
                showCloseButton: showCloseButton,
				confirmButtonText: confirmButtonText
			}).then((result) => {
				if (typeof callback === 'function') {
					callback(result.isConfirmed, result.value);
				}
			});
			
		},
		
		ask: function(setupOptions, callback){
			
			if ( typeof(setupOptions) === undefined || setupOptions == null ) {
				var setupOptions = {};				
			} 
			
			if(setupOptions.title === undefined) setupOptions.title = '';
			if(setupOptions.text === undefined) setupOptions.text = '';
			if(setupOptions.icon === undefined) setupOptions.icon = 'warning';
			if(setupOptions.html === undefined) setupOptions.html = false;
			if(setupOptions.showCancelButton === undefined) setupOptions.showCancelButton = true;
			if(setupOptions.showLoaderOnConfirm === undefined) setupOptions.showLoaderOnConfirm = false;
			
			if (setupOptions.primaryButtonText === undefined && setupOptions.input) {
				setupOptions.primaryButtonText = 'Save';
			} else if (setupOptions.primaryButtonText === undefined) {
				setupOptions.primaryButtonText = 'Yes';
			}

			if (setupOptions.cancelButtonText === undefined && setupOptions.input || setupOptions.cancelButtonText === undefined && setupOptions.primaryButtonText !== 'Yes') {
				setupOptions.cancelButtonText = 'Cancel';
			} else if (setupOptions.cancelButtonText === undefined) {
				setupOptions.cancelButtonText = 'No';
			}

			setupOptions.showSecondaryButton = false;
			if (setupOptions.secondaryButtonText) {
				setupOptions.showSecondaryButton = true;
			}

			Swal.fire({
				title: setupOptions.title,
				text: setupOptions.text,
				icon: setupOptions.icon,
				cancelButtonText: setupOptions.cancelButtonText,
				confirmButtonText: setupOptions.primaryButtonText,
				denyButtonText: setupOptions.secondaryButtonText,
				showDenyButton: setupOptions.showSecondaryButton,
				showCancelButton: setupOptions.showCancelButton,
				html: setupOptions.html,
				showLoaderOnConfirm: setupOptions.showLoaderOnConfirm,
				reverseButtons: true,
				heightAuto: false,
				allowOutsideClick: false,
				input: setupOptions.input,
				inputValidator: (value) => {
					return new Promise((resolve) => {
						if (_.isEmpty(value)) {
							resolve('Please enter a value.');
						} else {
							resolve();
						}
					})
				}
			}).then((result) => {
				if (callback) {
					callback(result.isConfirmed, result.isDenied, result.value);
				}
			});
			
		}
		
	}
	
})();
