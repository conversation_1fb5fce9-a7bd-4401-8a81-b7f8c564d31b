var bsLists = (function(){
	
	return {
				
		make: function(selector, listArgs, name){
			
			listArgs = Sandbox.verifyDomToolArgs(listArgs, this.setup());
			
			var ret = {
				selector: selector +'-list-'+ name,
				html: '<ul class="'+ selector.replace('.', '') +'-list-'+ name +' '+ listArgs.css +'" id="sidebar" role="navigation"></ul>',
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};
			
			return ret;			
		}, 
		
		setup: function(){
			
			return {
				
				css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}, name: {
					argType: 'free-input',
					defaultValue: '',
					description: 'The name of the list group.'
				}
				
			}
			
		}
						
	}
	
})();