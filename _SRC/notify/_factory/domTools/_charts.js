var charts = (function(){
	
	function getHTML(selector, args, name){
		
		var maxHeight = '100%';
		if(args.maxHeight){
			maxHeight = args.maxHeight;
		}
		
		return '<canvas class="'+ selector.replace('.', '') +'" id="'+ selector.replace('.', '') +'" width="100%" style="max-height:'+ maxHeight +'"></canvas>';
		
	}
	
	function getListener(selector, args, name){

		return [function(){
			
			// Container for pan options
			args.options.pan = {
				// Boolean to enable panning
				enabled: true,
	
				// Panning directions. Remove the appropriate direction to disable
				// Eg. 'y' would only allow panning in the y direction
				mode: 'xy',
				rangeMin: {
					// Format of min pan range depends on scale type
					x: null,
					y: null
				},
				rangeMax: {
					// Format of max pan range depends on scale type
					x: null,
					y: null
				},
				
				speed: 0.2,
				// Function called once panning is completed
				// Useful for dynamic data loading
// 				onPan: function({chart}) { console.log(`I was panned!!!`); }
			};
	
			// Container for zoom options
// 			args.options.zoom = {
// 				// Boolean to enable zooming
// 				enabled: true,
// 	
// 				// Zooming directions. Remove the appropriate direction to disable
// 				// Eg. 'y' would only allow zooming in the y direction
// 				mode: 'xy',
// 	
// 				rangeMin: {
// 					// Format of min zoom range depends on scale type
// 					x: null,
// 					y: null
// 				},
// 				rangeMax: {
// 					// Format of max zoom range depends on scale type
// 					x: null,
// 					y: null
// 				},
// 	
// 				// Speed of zoom via mouse wheel
// 				// (percentage of zoom on a wheel event)
// 				speed: 0.02,
// 	
// 				// Function called once zooming is completed
// 				// Useful for dynamic data loading
// // 				onZoom: function({chart}) { console.log(`I was zoomed!!!`); }
// 			};
			
			switch (args.type) {
				
				case 'bar':
					args.options.legend = {
			            display: false
			         };
					break;
					
				default:
					if (!args.options.hasOwnProperty('legend')) {
						
						args.options.legend = {
							position: 'bottom'
						} ;
						
					}
					break;
				
			}
			
			var ctx = $(selector).get(0).getContext("2d");

			if(args.data.hasOwnProperty('options')) {
				
				if(args.data.options.hasOwnProperty('zoom')) {
					args.options.zoom.enabled = args.data.options.zoom;
				}
				
			}

			var doughnutChart = new Chart(ctx, {
				type: args.type,
				data: args.data,
				options: args.options
			});

		}];
				
	}
	
	return {
		
		make: function(selector, args, name){
			
			var chartObj = {
					selector: selector +'-chart-'+ name,
					makeNode: Sandbox.makeNode,
					appendNode: Sandbox.appendNode,
					patch: Sandbox.patchNode,
					notify: Sandbox.makeNotification,
					loading: Sandbox.loading,
					notifications: {},
				};
			
			chartObj.html = getHTML(chartObj.selector, args, name);
			chartObj.listeners = getListener(chartObj.selector, args, name);
			
			return chartObj;
			
		}
		
	}
	
})();