var progress_ui_tool = (function(){
	
	return {
		
		make: function(selector, args, name){
			
			var divObj = {
				selector: selector +'-div-'+ name.replace(' ', ''),
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				listeners:[],
				properties:{},
				setPercent:function(newPercent){
					$(this.selector).progress('set percent', newPercent);
				}
			};
			
			var percentage = args.percent || 0;
			var label = args.label || '';
			var size = args.size || '';
			var color = args.color || '';
			
			if(percentage > 100){
				percentage = 100;
			}
			
			divObj.html = 
				
				'<div class="'+ divObj.selector.replace('.', '') +' ui '+ size +' '+ color +' progress" data-percent="'+ percentage +'" id="example1">'+
					'<div class="bar" style="min-width:0px;"></div>'+
					'<div class="label">'+ label +'</div>'+
				'</div>';
			
			divObj.listeners.push(function(selector){
				$(selector).progress({
				  percent: percentage
				});
			});
			
			return divObj;
			
		}
	}
	
})();
