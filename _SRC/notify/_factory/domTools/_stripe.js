var stripe = (function () {
  var publicKey = STRIPE_PK;

  return {
    createCustomer: function (email, token, callback) {
      controller(
        "createStripeCustomer&email=" + email + "&stripeToken=" + token,
        function (stripeCustomerId) {
          callback(stripeCustomerId);
        }
      );
    },

    createToken: function ($form, callback) {
        Stripe.setPublishableKey(publicKey);
        Stripe.card.createToken($form, function (status, response) {
          if (response.error) {
            callback(false);
          } else {
            // return the token from stripe
            callback(response.id);
          }
        });
    },

    createACHToken: async function (account) {
        var stripe = Stripe(publicKey);
    
        var stripeRespone = await stripe.createToken("bank_account", account);

        return stripeRespone;
    },

    updateCustomerCard: function (stripeId, token, callback) {
      controller(
        "updatePaymentInfo&stripe-id=" + stripeId + "&stripeToken=" + token,
        function (response) {
          callback(response);
        }
      );
    },
  };
})();
