var svgPath = (function(){
	
	return {
		
		make: function(selector, lineArgs, name){
			
			var lineObj = {
				selector: selector +'-line-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};
			
			lineArgs.height = lineArgs.drawTo.y - lineArgs.drawFrom.y;
			lineArgs.width = lineArgs.drawTo.x - lineArgs.drawFrom.x;
			
			line.html = '<svg class="'+ lineObj.selector.replace('.', '') +'" height="'+ lineArgs.height +'" width="'+ lineArgs.width +'"><line x1="'+ lineArgs.drawFrom.x +'" y1="'+ lineArgs.drawFrom.y +'" x2="'+ lineArgs.drawTo.x +'" y2="'+ lineArgs.drawTo.y +'" style="stroke:rgb(255,0,0);stroke-width:2" /></svg>';
			
			return lineObj;
			
		}
		
	}
	
})();