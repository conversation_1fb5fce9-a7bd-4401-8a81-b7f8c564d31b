var bsModal2 = (function(){
	
	var modalSelector = '';
	
	function makeModal(selector, args){
		
		var modal_size = '';
		
		if(args.size){
			modal_size = args.size;
		}else{
			modal_size = 'large';
		}
		
		if(typeof args.style === 'undefined'){ args.style = ''; }
		if(typeof args.headerStyle === 'undefined'){ args.headerStyle = ''; }

		args.fullScreen = false;

		var full = 'fullscreen';
		if (args.fullScreen === false) {
			full = '';
		}
		
		var modalHTML = 
			'<div id="" class="ui large longer '+ full +' modal '+ selector.replace('.', '') +'-'+ args.identity +'-modal">'+
						
				'<div class="content scrolling '+ selector.replace('.', '') +'-modal-content"></div>'+
				
			'</div>';
			
		if (args.type === 'basic') {
			
			modalHTML = 
				'<div id="" class="ui basic large modal '+ selector.replace('.', '') +'-'+ args.identity +'-modal">'+
									
					'<div class="content scrolling '+ selector.replace('.', '') +'-modal-content"></div>'+
					
				'</div>';
			
		}
		
		var ret = {
				showSelector: selector + '-'+ args.identity +'-modal',
				selector: selector + '-modal-content',
				html: modalHTML,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode
			};
		
		return ret;
		
	}
	
	function makeModalHeader(selector, args){
		
		if(typeof args.bodyStyle === 'undefined'){ args.bodyStyle = ''; }
		
		var modalHTML = '<div id="" class="header '+ selector.replace('.', '') +'-'+ args.identity +'-header" style="'+ args.bodyStyle +'"></div>'; //<div class="ui loading basic segment"></div></div>';
		
		var ret = {
			selector: selector + '-'+ args.identity +'-header',
			html: modalHTML,
			makeNode: Sandbox.makeNode,
			appendNode: Sandbox.appendNode,
			patch: Sandbox.patchNode,
			empty: Sandbox.emptyNode
		};
		
		return ret;
		
	}
	
	function makeModalBody(selector, args){
		
		if(typeof args.bodyStyle === 'undefined'){ args.bodyStyle = ''; }
		
		var modalHTML = '<div id="" class="modal-body '+ selector.replace('.', '') +'-'+ args.identity +'-modal-body" style="'+ args.bodyStyle +'"></div>'; //<div class="ui loading basic segment"></div></div>';
		
		var ret = {
			selector: selector + '-'+ args.identity +'-modal-body',
			html: modalHTML,
			makeNode: Sandbox.makeNode,
			appendNode: Sandbox.appendNode,
			patch: Sandbox.patchNode,
			empty: Sandbox.emptyNode
		};
		
		return ret;
		
	}
	
	function makeModalFooter(selector, args){
		
		if(typeof args.footerStyle === 'undefined'){ args.footerStyle = ''; }
		var modalHTML = '<div id="" class="actions '+ selector.replace('.', '') +'-modal-footer" style="'+ args.footerStyle +'"></div>';
		
		var ret = {
			selector: selector + '-modal-footer',
			html: modalHTML,
			makeNode: Sandbox.makeNode,
			appendNode: Sandbox.appendNode,
			loading: Sandbox.loading,
			patch: Sandbox.patchNode,
			empty: Sandbox.emptyNode
		};
		
		return ret;
		
	}
	
	return {
		
		selector: modalSelector,
		
		make: function(selector, modalObj, name, container){

			if(typeof modalObj.identity === 'undefined'){ modalObj.identity = Math.floor(Math.random() * 20000); }
			
			modalSelector = selector+'-modal-'+ modalObj.identity +'-'+ name;
			
			modalObj = Sandbox.verifyDomToolArgs(modalObj, this.setup());

			var modal = makeModal(selector, modalObj);
			
			modal.header = makeModalHeader(selector, modalObj);
			modal.body = makeModalBody(selector, modalObj);
			modal.footer = makeModalFooter(selector, modalObj);
			modal.makeNode = Sandbox.makeNode;
			modal.appendNode = Sandbox.appendNode;
			modal.patch = Sandbox.patchNode;
			modal.notify = Sandbox.makeNotification;
			modal.notifications = {};
			modal.hide = this.hide;
			modal.show = this.show;
			modal.isActive = this.isActive;
			
			if(container.hasOwnProperty('properties') && container.properties.hasOwnProperty('compInstanceId')){
				modal.body.properties = {
					compInstanceId: container.properties.compInstanceId
				};
				modal.footer.properties = {
					compInstanceId: container.properties.compInstanceId
				};
			}
			
			modal.listeners = [];
			
			var modalListenerArray = [];
			
			if(modalObj.onClose){
				modalListenerArray.push({
					name:'onHidden',
					func:function(){
						 modalObj.onClose();
					}
				});
				
			}
			
			if(modalObj.onShow){
				
				modalListenerArray.push({
					name:'onVisible',
					func:function(){
						 modalObj.onShow();
					}
				})

			}

			if(modalListenerArray.length > 0){
				
				var listenerSetup = {
					autofocus: false
				};
				
				_.each(modalListenerArray, function(listener){
					
					listenerSetup[listener.name] = listener.func;
					
				});

				modal.listeners.push(function(selector){
					
					var extra = $(modal.showSelector);
					for ( i=1; i < extra.length; i++ ) {
						extra[i].remove();
					}
					
					$(modal.showSelector).modal(listenerSetup);
					
				});
				
			}

			// remove old modals
			$(modal.selector).remove();
			
			return modal;
			
		},
		
		setup: function(){
			
			return {
				body: {
					argType: 'dom-object',
					defaultValue: '',
					description: 'DOM object to insert into the body of the modal'
				},
				footer: {
					argType: 'dom-object',
					description: 'DOM object to insert into the footer of the modal',
					defaultValue: ''
				}
			};
			
		},
		
		hide: function(callback){
			
			// This fix was applied here to fix semantic scrolling issue on mobile devices
			if( /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ) {
				
				if(!EventTarget.prototype.hasOwnProperty('addEventListenerBase')) {
					
					EventTarget.prototype['addEventListenerBase'] = EventTarget.prototype.addEventListener;
					
					EventTarget.prototype.addEventListener = function(type, listener){

						if(this !== document.querySelector('body') || type !== 'touchmove'){
							this.addEventListenerBase(type, listener);
						}
						
					};	
					
				}	
				
			}
	
			$(this.showSelector).modal('hide');
					
		},
				
		show: function(callback){

			// This fix was applied here to fix semantic scrolling issue on mobile devices
			if( /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ) {
				
				if(!EventTarget.prototype.hasOwnProperty('addEventListenerBase')) {
					
					EventTarget.prototype['addEventListenerBase'] = EventTarget.prototype.addEventListener;
					
					EventTarget.prototype.addEventListener = function(type, listener){

						if(this !== document.querySelector('body') || type !== 'touchmove'){
							this.addEventListenerBase(type, listener);
						}
						
					};	
					
				}	
				
			}
			
			$(this.showSelector).modal('show');
			
		},
		
		isActive: function(callback) {

			return $(this.showSelector).modal('is active');
			
		}
				
	}
	
}());