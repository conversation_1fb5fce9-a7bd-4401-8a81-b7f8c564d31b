var bsListItems = (function(){
	
	return {
				
		make: function(selector, listObj, name){
			
			listObj = Sandbox.verifyDomToolArgs(listObj, this.setup());
			
			var ret = {
				selector: selector+'-list-item-'+ name,
				html: '<li class="'+ selector.replace('.', '') +'-list-item-'+ name +' '+ listObj.css +'" data-id="'+ listObj.dataId +'">'+ listObj.content +'</li>',
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};
			
			return ret;
			
		}, 
		
		setup: function(){
			
			return {
				
				content: {
					argType: 'free-input',
					defaultValue: '',
					description: 'The text to be written within the list item.'
				}, css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}, dataId: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the list item here (can be used in notifications).'
				}
				
			}
			
		}
						
	}
	
})();