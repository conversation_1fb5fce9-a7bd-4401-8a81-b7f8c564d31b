var bsTable = (function(){
	
	function getListeners(selector, tableArgs){

		var listeners = [],
			tblSettings = [];
		
		if(tableArgs.dataTable){
			
			var tableSetup = {
					dom: 'ifpBtpi',
				    buttons: [
					    'csvHtml5',
				        'excel',
				        'print'
				    ],
					destroy: true, 
					order: [0, 'asc'],
					pageLength: 100,
					paging: true,
					columnDefs: [
						{
							bSortable: true,
							aTargets: ['_all']
						}
					]
				};

			if(typeof tableArgs.dataTable === 'object'){

				_.each(tableArgs.dataTable, function(value, key){

					tableSetup[key] = value;
					
				});
								
			}

			if(typeof tblSettings !== 'undefined'){
				
				tableSetup.colReorder = {
					order: []
				};
				
				// change visibility settings
/*
				_.each(tableSetup.columns, function(col, key){

					tableSetup.columns[key].visible = (_.where(tblSettings.colOrder, {name: col.title})[0].isVisible === 'true');
					tableSetup.columns[key].bVisible = (_.where(tblSettings.colOrder, {name: col.title})[0].isVisible === 'true');
					
					var match = 0;
					
					_.each(tableSetup.columns, function(col, innerKey){

						if(col.title === _.where(tblSettings.colOrder, {order: key.toString()})[0].name){
							
							match = innerKey;
							
						}						
						
					});
					
					tableSetup.colReorder.order.push( match );
					
				});			
*/
				
			}

			listeners.push(function(){
				
				var	table = $(document).ready(function(){
						
						function updateTableSettings(table, selector, currentSettings){

							if(!currentSettings.hasOwnProperty('userId')){
								
								currentSettings = {
									userId: +Sandbox.create(Factory, 'tablesDomTool', 'domTool', 0).data.cookie.userId
								};
								
							}
							
							if(Array.isArray(currentSettings.tables)){
								currentSettings.tables = {};
							}
														
							currentSettings.tables[selector.replace('.', '')] = {
								colOrder: []
							};	
							
							_.each(table.columns()[0], function(columnKey){
								
								currentSettings.tables[selector.replace('.', '')].colOrder.push({
									order: columnKey,
									name: table.column(columnKey).title(),
									isVisible: ''+table.column(columnKey).visible()+''
								});
								
							});

							return currentSettings;
							
						}
																		
						var tableInstance = $(selector).DataTable(tableSetup),
							userSettings = Object.assign({}, appConfig.userSettings);
						
						$('.dt-buttons').addClass("pull-right");
						
						tableInstance.on('column-reorder', function(e, settings, details){

							Factory.updateUserSettings( updateTableSettings(tableInstance, selector, userSettings) );

						});
					
						tableInstance.on( 'column-visibility.dt', function ( e, settings, column, state ) {
														
							Factory.updateUserSettings( updateTableSettings(tableInstance, selector, userSettings) );

						});

					});
				
			});
			
		}
		
		return listeners;
		
	}
	
	return {
		
		make: function(selector, tableArgs, name, parent){

			tableArgs = Sandbox.verifyDomToolArgs(tableArgs, this.setup());
			
			// create table object
			var tableObj = {
				selector: selector +'-table-'+ name,
				patch: Sandbox.patchNode,
				makeRow: this.makeRow,
				setupRow: this.setupRow,
				columns: []
			};
			
			var tableCss = 'ui selectable compact celled table';
			if(tableArgs.css){
				tableCss += ' '+ tableArgs.css;
			}
			if(tableArgs.tableCss){
				tableCss = tableArgs.tableCss;
			}
			
			if(tableArgs.clearCSS === true){

				// add html field
				tableObj.html = '<div style="overflow:auto;"><table class="'+ tableArgs.css +' '+ tableObj.selector.replace('.', '') +'"></table></div>';
				
			}else{
				
				// add html field
				tableObj.html = '<div style="overflow:auto;"><table class="'+ tableCss +' '+ tableObj.selector.replace('.', '') +'"></table></div>';

			}
						
			// create header node
			var headerCss = '';
			if(tableArgs.headerCss){
				headerCss = tableArgs.headerCss;
			}
			var headerStyle = '';
			if(tableArgs.headerStyle){
				headerStyle = ' style="'+ tableArgs.headerStyle +'"';
			}
			
			tableObj.header =  {};
			tableObj.header.selector = tableObj.selector +'-header';
			tableObj.header.html = '<thead class="'+ tableObj.header.selector.replace('.', '') +' '+ headerCss +'"'+ headerStyle +'></thead>';

			tableObj.header.row = {};
			tableObj.header.row.selector = tableObj.header.selector +'-row';
			tableObj.header.row.html = '<tr class="'+ tableObj.header.row.selector.replace('.', '') +'"></tr>';
			tableObj.header.row.makeNode = Sandbox.makeNode;
			tableObj.header.row.patch = Sandbox.patchNode;
			tableObj.header.row.empty = Sandbox.emptyNode;
			
			var i=0, headStyle = '';
						
			_.each(tableArgs.columns, function(columnDisplayName, columnName){

				tableObj.columns[i] = columnName;
				if(typeof tableArgs.headerStyles !== 'undefined' && typeof tableArgs.headerStyles[i] !== 'undefined'){
					headStyle = tableArgs.headerStyles[i];
				}else{
					headStyle = '';
				}
				
				if(typeof tableArgs.columnCSS !== 'undefined' && typeof tableArgs.columnCSS[i] !== 'undefined'){
					columnCSS = tableArgs.columnCSS[i];
				}else{
					columnCSS = '';
				}
				
				i++;
								
				tableObj.header.row[columnName] = {
					selector: tableObj.header.row.selector +'-col-'+ columnName
				};
				tableObj.header.row[columnName].html = '<th class="'+ tableObj.header.row[columnName].selector.replace('.', '') +' '+ columnCSS +'" style="'+ headStyle +'">'+ columnDisplayName +'</th>';
				tableObj.header.row[columnName].notify = Sandbox.makeNotification;
				tableObj.header.row[columnName].makeNode = Sandbox.makeNode;
				tableObj.header.row[columnName].patch = Sandbox.patchNode;
				tableObj.header.row[columnName].empty = Sandbox.emptyNode;

				if(parent.hasOwnProperty('properties')){

    				tableObj.header.row[columnName].properties = {
    					compInstanceId: parent.properties.compInstanceId
    				};
    			}
				
			});	
			
			// create body node
			tableObj.body = {
				selector: tableObj.selector +'-body',
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification
			};
			
			var style = '';
			if(tableArgs.style){
				style = 'style="'+ tableArgs.style +'"';
			}
			
			tableObj.body.html = '<tbody class="'+ tableObj.body.selector.replace('.', '') +'" '+ style +'></tbody>';
			
			// get listeners for the tableObj
			tableObj.listeners = getListeners(tableObj.selector, tableArgs);
			
			return tableObj;
			
		},
		
		setup: function(){
			
			return {
				
				css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}, tag: {
					argType: 'free-input',
					defaultValue: '',
					description: 'A unique tag to add to the table\'s selector.'
				}, columns: {
					argType: 'free-input',
					defaultValue: {},
					description: 'The columns of the table. (a display name keyed with the name to be used to access cells)'
				}
				
			}
			
		},
		
		setupRow: function(){
		
			return {
				css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}, dataId: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Row specific identifier to be passed through notify calls.'
				}
			}
			
		},
		
		makeRow: function(rowName, content, rowArgs){

// 			rowArgs = Sandbox.verifyDomToolArgs(rowArgs, this.setupRow());
			if(typeof rowArgs === 'undefined'){
				rowArgs = {};
			}
			if(typeof rowArgs.css === 'undefined'){
				rowArgs.css = '';
			}
			if(typeof rowArgs.dataId2 === 'undefined'){
				rowArgs.dataId2 = '';
			}
			
			this.body[rowName] = {};
			this.body[rowName].selector = this.body.selector +'-row-'+ rowName;
			this.body[rowName].html = '<tr class="'+ this.body[rowName].selector.replace('.', '') +' '+ rowArgs.css +'" data-id="'+ rowArgs.dataId +'" data-id2="'+ rowArgs.dataId2 +'" style="'+ rowArgs.style +'"></tr>';
			this.body[rowName].notify = Sandbox.makeNotification;
			this.body[rowName].makeNode = Sandbox.makeNode;
			this.body[rowName].empty = Sandbox.emptyNode;
			this.body[rowName].patch = Sandbox.patchNode;
			this.body[rowName].notifications = {};
			
			if(this.hasOwnProperty('properties')){
				this.body.properties = {
					compInstanceId: this.properties.compInstanceId
				};
				this.body[rowName].properties = {
					compInstanceId: this.properties.compInstanceId
				};
			}
			
			var columnName, cellStyle;
			
// 			_.each(content, function(cell){
			for(i = 0; i < this.columns.length; i++){

				columnName = this.columns[i];
				
				this.body[rowName][columnName] = {};
				this.body[rowName][columnName].selector = this.body[rowName].selector +'-col-'+ columnName;
				this.body[rowName][columnName].makeNode = Sandbox.makeNode;
				this.body[rowName][columnName].notify = Sandbox.makeNotification;
				this.body[rowName][columnName].empty = Sandbox.emptyNode;
				this.body[rowName][columnName].patch = Sandbox.patchNode;
				
				if(this.hasOwnProperty('properties')){
					this.body[rowName][columnName].properties = {
						compInstanceId: this.properties.compInstanceId
					};
				}
				
				if(typeof rowArgs.cellStyles !== 'undefined'){
					cellStyle = rowArgs.cellStyles[i];
				}else{
					cellStyle = '';
				}
				if(typeof rowArgs.dataId === 'undefined'){
					rowArgs.dataId = '';
				}
				if(typeof rowArgs.dataId2 === 'undefined'){
					rowArgs.dataId2 = '';
				}
				
				if(typeof content[i] !== 'undefined'){
					this.body[rowName][columnName].html = '<td class="'+ this.body[rowName][columnName].selector.replace('.', '') +' '+ rowArgs.css +'" style="'+ cellStyle +'" data-id="'+ rowArgs.dataId +'" data-id2="'+ rowArgs.dataId2 +'">'+ content[i] +'</td>';
				}else{
					this.body[rowName][columnName].html = '<td class="'+ this.body[rowName][columnName].selector.replace('.', '') +' '+ rowArgs.css +'" style="'+ cellStyle +'" data-id="'+ rowArgs.dataId +'" data-id2="'+ rowArgs.dataId2 +'"></td>';
				}
				
				
				
			}
// 			});
			
			
			return this.body[rowName];
			
		},
				
	}
	
})();

var bsTableRow = (function(){
	
	return {
		
		make: function(selector, rowArgs){
			
			rowArgs = Sandbox.verifyDomToolArgs(rowArgs, this.setup());
			
			var rowObj = {
				selector: selector +'-row-'+ rowArgs.tag,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};
			
			rowObj.html = '<tr class="'+ rowObj.selector.replace('.', '') +' '+ rowArgs.css +'" data-id="'+ rowArgs.dataId +'">';
			
			_.each(rowArgs.content, function(cell){
				
				switch(rowArgs.type){
					
					case 'header':
					rowObj.html += '<th>'+ cell +'</th>';
					break;
					
					case 'row':
					rowObj.html += '<td>'+ cell +'</td>';
					break;
					
					default:
					break;
					
				}
				
			});
			
			rowObj.html += '</tr>';
			
			return rowObj;
			
		},
		
		setup: function(){
			
			return {
				
				type: {
					argType: 'option',
					options: ['header', 'row'],
					defaultValue: 'row',
					description: 'Whether the row is a table header or regular row.'
				}, css: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional classes here.'
				}, dataId: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place additional data about the button here (can be used in notifications).'
				}, content: {
					argType: 'free-input',
					defaultValue: [],
					description: 'The content of the row formatted in a list, where each item in the list is what will be displayed in a cell.'
				}, tag: {
					argType: 'free-input',
					defaultValue: '',
					description: 'A unique tag to add to the row\'s selector.'
				}
				
			}
			
		}
		
	}
	
})();