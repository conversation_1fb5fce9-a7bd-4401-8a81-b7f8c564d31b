var div_ui_tool = (function(){
	
	
	function runSemanticBehavior(behaviorName, argumentOne, argumentTwo){
		
		$(this.selector)[this.properties.semanticModuleType](behaviorName, argumentOne, argumentTwo);
		
	}
	
	function getHTML(divObj, args){

		// generate div html
		var content = '';
		var css = '';
		var href = '';
		var style = '';
		var tag = 'div';
		var type = '';
		var src = '';
		var id = '';
		var dataAttr = '';
		var placebholder = '';
		var multiple = '';
		var value = '';
		var tabIndex = '';
		var checked = '';
		
		if(args.content){
			content = args.content;
		}else if(args.text){
			content = args.text;
		}
		if(args.checked){
			checked = ' checked=""';
		}
		if(args.css){
			css = args.css;
		}
		if(args.style){
			style = args.style;
		}
		if(args.tag){
			tag = args.tag;
		}
		if(args.src){
			src = ' src="'+ args.src +'"';
		}
		if(args.type){
			type = ' type="'+ args.type +'"';
		}
		if(args.href){
			href = 'href="'+ args.href +'" ';
			if(args.target){
				href += 'target="'+ args.target +'" ';
			}
		}
		if(args.id){
			id = args.id;
		}
		if(args.placeholder){
			placebholder = ' placeholder="'+ args.placeholder +'" ';
		}
		if(args.multiple){
			multiple = ' multiple="" ';
		}
		if(args.dataAttr){
			_.each(args.dataAttr, function(dataAttribute, key){
				
				if (
					typeof dataAttribute === 'object'
					&& dataAttribute.hasOwnProperty('name')
				) {
					
					dataAttr += ' data-'+dataAttribute.name+'="'+dataAttribute.value+'" ';
					
				} else {
					
					dataAttr += ' data-'+ key +'="'+ dataAttribute +'" ';
					
				}
				
			});
		}
		if(args.data){
			_.each(args.data, function(dataAttribute, key){
				
				if (
					typeof dataAttribute === 'object'
					&& dataAttribute.hasOwnProperty('name')
				) {
					
					dataAttr += ' '+dataAttribute.name+'='+dataAttribute.value+' ';
					
				} else {
					
					dataAttr += ' '+ key +'='+ dataAttribute +' ';
					
				}
				
			});
		}
		if (args.isTabbable) {
			tabIndex = ' tabindex="0"';
		}

		var closingTag = '';
		switch(tag){
			
			case 'input':
				if( args.hasOwnProperty('icon') ){
					closingTag += '<i class="'+ args.icon +' icon"></i>';
				}
				if(args.value){
					value = ' value="'+ args.value +'"';
				}
				break;
			
			case 'img':
				break;
			
			default:
				closingTag = '</'+ tag +'>';
				break;
			
		}
		
		var tooltip = '';
		if(args.tooltip){
			switch(typeof args.tooltip){
				
				case 'string':
				var tooltipPos = args.tooltipPos || 'top left';
				tooltip = ' data-tooltip="'+ args.tooltip +'" data-position="'+ tooltipPos +'" data-variation="inverted" data-inverted=""';
				break;
				
				case 'object':
				var tipTitle;

				if(args.tooltip.title){
					tipTitle = args.tooltip.title;
				} else {
					tipTitle = '';
				}
				
				tooltip = ' data-title="'+ tipTitle +'" data-content="'+ args.tooltip.text +'" data-position="'+ args.tooltip.position +'"';
				if(!args.tooltip.white){
					tooltip += 'data-variation="inverted" data-inverted=""';
				}
				break;
				
			}
		}

		divObj.html = '<'+ tag +' id="'+ id +'" class="'+ css +' '+ divObj.selector.replace('.', '') +'" '+ href +'style="'+ style +'" '+ placebholder + multiple + tooltip +''+ src +''+ type +''+ value +''+ checked +''+ dataAttr +''+ tabIndex +'>'+ content + closingTag;		
		
	}
	
	function registerWalkthroughStep (args, node) {
		
		if (!_.isEmpty(args.walkthrough)) {
			
			Factory.triggerEvent({
				type: 'register-walkthrough-step'
				, data: {
					walkthrough: 	args.walkthrough.name
					, name: 		args.walkthrough.step
					, selector:		node.selector
				}
			}, 'div-tool');
			
		}
		
	}
	
	function updateText(newText, replace){
		
		if(replace === undefined){
			replace = true;
		}
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		if(el){
			
			if(replace){

				this.properties.args.text = newText;
				
				getHTML(this, this.properties.args);
				
				this.patch();
				
			}else{

				el.innerHTML = newText;
				
			}
			
		}
		
	}
	
	return {
		
		make: function(selector, args, name){
			
			var divObj = {
				selector: selector +'-div-'+ name.replace(' ', ''),
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				listeners:[],
				properties:{},
				text:updateText,
				args:args
			};
			
			getHTML(divObj, args);	

			// add listener
			if(args.listener){
				
				divObj.properties.semanticModuleType = args.listener.type;
				divObj[args.listener.type] = runSemanticBehavior;
				
				switch(args.listener.type){
					
					case 'date':
						divObj.listeners.push(function(selector){
							
							$(selector)['calendar'](this);
							
						}.bind(args.listener));
						break;
					
					case 'search':
						var urlOpts = {};
						if (!_.isEmpty(args.urlOpts)) {
							urlOpts = args.urlOpts;
						}
						args.listener.apiSettings = {
							url: databaseConnection.obj.getSearchPath(
									args.listener.objectType
									, {}
									, {}
									, urlOpts
								)
							, mockResponseAsync: function(selection, where, settings, callback) {
								
								function GetCookieValue(name) {
								    var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
								    return found.length > 0 ? found[0].split("=")[1] : null;
								}
								
								var headers = {
									'bento-token': GetCookieValue('token')
								};
								if (appConfig.is_portal) {
									
									headers.portal = appConfig.state.portal;
									headers['bento-token'] = GetCookieValue('p_token');
									
								}
								
								$.ajax({
									type: 'post',
									url: settings.url,
									xhrFields: {
										withCredentials: true
									},
									crossDomain: true,
									data: {
										json: JSON.stringify({
											selection: 	selection
											, where: 	where
										})
									},
									success: function (response) {
						
										callback(response);
										
									},
									error: function (jqXHR, status, error) {
						
										
										
									},
									headers: headers
								});	
								
							}.bind({}, args.listener.selection, args.listener.where)
						};
						args.listener.fields = {
							results : 'results',
							title   : 'name'
// 							url     : 'html_url'
					    },
					    args.listener.minCharacters = 1;
					    args.listener.mockResponseAsync = function(settings, callback){
						    
// 						    console.log('responseAsync::', arguments);
						    
					    };
					    
					    if (args.listener.category) {
						    
						    args.listener.type = 'category';
						    
						    if (typeof args.listener.onResponse === 'function') {
							    
							    args.listener.apiSettings.onResponse = args.listener.onResponse;
							    
						    } else {
							    
							    args.listener.apiSettings.onResponse = function(raw){
									
								    var response = {
									    results : {}
								    };
								    
								    _.each(raw.results, function(item){
									    
									    if(!item[args.listener.category]){
										    return;
									    }
									    
									    if (response.results.hasOwnProperty(item[args.listener.category].id.toString())) {
										    response.results[item[args.listener.category].id.toString()].results.push(item);
									    }else{
										    response.results[item[args.listener.category].id.toString()]
										    	
										    	= {
											    	name : item[args.listener.category].name,
											    	results : [item]
										    	};
										    	
									    }
									    
								    });
								    
								    return response;
								    
							    };
							    
						    }
						    
					    } else {
						    
							args.listener.type = 'standard';
							
							if (typeof args.listener.onResponse === 'function') {
							    
								args.listener.apiSettings.onResponse = args.listener.onResponse;
							
							}
						    
					    }

						args.listener.onResultsOpen = function() {

							setTimeout(function() {
								$('.floater-container').css({'overflow':'visible'});
							}, 100);
	
							var element = $(this[0]);
	
							function setTagDropdownHeight(element) {
								var parentElement = element.parent();
								var dropdownHeight = $(window).height() - parentElement.offset().top - parentElement[0].offsetHeight - 25;
								dropdownHeight = (dropdownHeight < 450) ? dropdownHeight : 450;
								var rightOffset = args.autoRightOffset ? element.outerWidth() - ($(window).width() - (parentElement.offset().left + parentElement.outerWidth())) : 0;
								element.css({'max-height':dropdownHeight, 'left':'-'+rightOffset+'px'});
							}
	
							$(window).on('resize', function() {
								setTagDropdownHeight(element);
							});
	
							window.addEventListener('scroll', function() {
								setTagDropdownHeight(element);
							}, true);
	
							setTagDropdownHeight(element);
	
						}

						args.listener.onResultsClose = function() {

							setTimeout(function() {
								$('.floater-container').css({'overflow-y':'scroll', 'overflow-x':'auto'});
							}, 100);

						}
					    
					    divObj.listeners.push(function(selector){
							
							$(selector)['search'](this);
							
						}.bind(args.listener));
					    
						break;
					
					default:
					
						if(args.type){
							
							divObj.listeners.push(function(selector){
								
								$(selector)[args.type](this);
								
							}.bind(args.listener));
							
						}else{
							
							divObj.listeners.push(function(selector){
								
								$(selector)[this.type](this);
								
							}.bind(args.listener));
							
						}
						
						break;
					
				}
				
				
				
			}
			
			registerWalkthroughStep(args, divObj);
	
			return divObj;
			
		}
		
	}
	
})();
