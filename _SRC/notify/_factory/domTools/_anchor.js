var anchors = (function(){   
	
	function updateText(newText, replace){
		
		if(replace === undefined){
			replace = true;
		}
		
		var el = document.getElementsByClassName(this.selector.replace('.', ''))[0];
		el.innerHTML = newText;
		
		if(replace){
			this.text = newText;
		}
		
	}
	
	return {
		
		make: function(selector, anchorArgs, name){
			
			if(typeof anchorArgs.css === 'undefined') anchorArgs.css = '';
			if(typeof anchorArgs.text === 'undefined') anchorArgs.text = '';
			
			var anchorObj = {
				selector: selector +'-a-'+ name.replace('.', '').replace(' ', '').replace("'", '').replace(/\"/g,''),
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				text:updateText
			};
			
			var url = '';
			if(anchorArgs.hasOwnProperty('url')){
				url = anchorArgs.url;
			}else{
				url = urlData.createPageURL(anchorArgs.page, anchorArgs.pageParams);
			}
			
			var target = '';
			if(anchorArgs.newWindow){
				target = 'target="_blank"';
			}
			
			var style = '';
			if(anchorArgs.style){
				style = 'style="'+ anchorArgs.style +'"';
			}
			
			if(anchorArgs.hasOwnProperty('noLink')){
			
				target = '';
				url = '';
				
				anchorObj.html = '<a '+ target +' class="'+ anchorObj.selector.replace('.', '') +' '+ anchorArgs.css +'" " '+ style +'>'+ anchorArgs.text +'</a>';
			
			}else{
				
				anchorObj.html = '<a '+ target +' class="'+ anchorObj.selector.replace('.', '') +' '+ anchorArgs.css +'" href="'+ url +'" '+ style +'>'+ anchorArgs.text +'</a>';
								
			}

			return anchorObj;
			
		}
		
	}
	
})();