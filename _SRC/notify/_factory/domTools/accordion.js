var accordion_ui_tool = (function(){
	
	return {

		make: function(selector, args, name){

			var divObj = {
				selector: selector +'-div-'+ name.replace(' ', ''),
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				patch: Sandbox.patchNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {},
				listeners:[],
				properties:{
					bellowsCounter: 0
				},
				makeItem: this.makeItem
			},
			bellowHTML = '',
			accordionType = '';
			
			if(args.type)
				accordionType = args.type;
				
			if(args.fluid){
				accordionType = accordionType.concat(' fluid');
			}	

			if(args.inverted){
				accordionType = accordionType.concat(' inverted');
				divObj.html = 
					'<div class="ui inverted segment">'+
						'<div class="'+ divObj.selector.replace('.', '') +' ui '+ accordionType +' accordion">'+
							bellowHTML +
						'</div>'+
					'</div>';				
			}else{
				divObj.html = 
					'<div class="'+ divObj.selector.replace('.', '') +' ui '+ accordionType +' accordion">'+
						bellowHTML +
					'</div>'				
			}

			if(args.settings == undefined)
				args.settings = {};

			divObj.listeners.push(function(selector){
				$(selector).accordion(args.settings);
			});

			return divObj;
			
		},
		
		makeItem: function(name, args){
			
			var  bellowString = '',
				icon = args.icon || 'dropdown',
				title = args.title || '',
				active = '',
				itemObj = {
					selector: this.selector+'-'+name,
					makeNode: Sandbox.makeNode,
					appendNode: Sandbox.appendNode,
					patch: Sandbox.patchNode,
					notify: Sandbox.makeNotification,
					notifications: {},
					listeners:[],
					properties:{},
					toggle:function(index){
						
						$(this.selector).accordion('toggle', index);
						
					}.bind(this, this.properties.bellowsCounter),
					open:function(index){
						
						$(this.selector).accordion('open', index);
						
					}.bind(this, this.properties.bellowCounter),
					close:function(index){
						
						$(this.selector).accordion('close', index);
						
					}.bind(this, this.properties.bellowCounter)					
				};
				
			if(args.active)
				active = 'active';					
											
			itemObj.html = 
				'<div class="'+active+' title">'+
					'<i class="'+ icon +' icon"></i> '+ title +
				'</div>'+
				'<div class="'+active+' clearing content">'+
					'<div class="'+ itemObj.selector.replace('.', '')+'">'+
					'</div>'+
				'</div> ';
				
			this[name] = itemObj;
		
			this.properties.bellowsCounter++;
				
			return itemObj;
	
		}
		
	}
	
})();