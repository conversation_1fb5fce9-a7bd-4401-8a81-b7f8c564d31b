var lineBreak = (function(){
	
	return {
		
		make: function(selector, args, name){
			
			// verify input arguments
			args = Sandbox.verifyDomToolArgs(args, this.setup());
			
			// create dom node object
			var lineBreak = {
				selector: selector +'-break-'+ name,
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				notify: Sandbox.makeNotification,
				loading: Sandbox.loading,
				notifications: {}
			};

			
			// add spacing to html field
			lineBreak.html = '';
			for(i = 0; i < args.spaces; i++){
				lineBreak.html += '<div class="sixteen wide column '+ lineBreak.selector.replace('.', '') +'" style="'+ args.style +'"><br /></div>';
			}

			return lineBreak;
			
		},
		
		setup: function(){
			
			return {
				spaces: {
					argType: 'int',
					defaultValue: 1,
					description: 'The amount of spacing of the pagebreak'
				},
				tag: {
					argType: 'free-input',
					defaultValue: 'br',
					description: 'Place a unique tag here to distinguish this line break from other line breaks within the same container'
				},
				style: {
					argType: 'free-input',
					defaultValue: '',
					description: 'Place any straight css here to apply to element.'
				}
			};
			
		}

		
	}
	
})();