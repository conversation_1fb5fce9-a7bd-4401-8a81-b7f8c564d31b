var domTool = (function(){
	
	var domObject = {};
	
	function appendDOMObject(domPiece, newElement, containerClass){
		
		if(domPiece.constructor == Object){
			
			if(domPiece.containerClass == containerClass){
				
// 				return 
				
			}
			
			_.each(domPiece, function(domElement, elementContainerClass){
				
// 				if()
				
			});
			
		}else{
			
			return false;
			
		}
		
	}
	
	function buildDivHTML(div){
				
		switch(div.type){
			
			case 'container':
			return '<div class="'+ div.containerClass +'-container"></div>';
			break;
			
			case 'panel-container':
			return '<div class="panel panel-default '+ div.containerClass +'"></div>';
			break;
			
			case 'panel-header':
			return '<div class="panel-heading '+ div.containerClass +'"></div>';
			break;
			
			case 'panel-header-title':
			return '<h3 class="panel-title '+ div.containerClass +'">'+ div.text +'</h3>';
			break;
			
			case 'panel-body':
			return '<div class="panel-body '+ div.containerClass +'"></div>';
			break;
			
			case 'panel-footer':
			return '<div class="panel-footer '+ div.containerClass +'"></div>';
			break;
			
			case 'panel-footer-title':
			return '<h3 class="panel-title '+ div.containerClass +'">'+ div.text +'</div>';
			break;
			
			case 'loading-gif':
			return '<img id="loading-img" style="display:block; margin: 0 auto; width: 30px;" src="_images/loading.gif" >';
			break;
			
			default:
			break;
			
		}
				
	}
	
	function buildDomHTML(domObjectPiece, selector){

/* 		var domHTML = ''; */
		
		if(domObjectPiece.constructor == Object && domObjectPiece.hasOwnProperty('selector')){
		
			$(selector).append(domObjectPiece.html);
			
		}else{
			
			return true;
			
		}
		
		_.each(domObjectPiece, function(domObjectInnerPiece, key){
			
			if(domObjectInnerPiece.constructor == Object && domObjectInnerPiece.hasOwnProperty('selector') && key != 'notifications' && key != 'listeners'){
				
				buildDomHTML(domObjectInnerPiece, domObjectPiece.selector);
				
			}/*
else if(domObjectInnerPiece.constructor == Object && key == 'notifications'){

				_.each(domObjectInnerPiece, function(notification){

					$(notification.selector).off(notification.type);
					$(notification.selector).on(notification.type, function(e){
						
						notification.notification.data.event = e;
						notification.notification.data.sender = this;
						Factory.triggerEvent(notification.notification, notification.moduleId);
						
					});
					
				});
				
			}
*/
			
		});
				
	}

	function setDomObjListeners(listeners){

		_.each(listeners, function(listener, listenerSelector){
			
			$(listenerSelector).off(listener.eventType);
			$(listenerSelector).on(listener.eventType, function(e){
				
				// get classnames of the clicked element
				var classes = $(this).attr("class").split(' ');

				// get data to particular element; i.e., underscore.js is amazing
				var data = _.find(listener.datas, function(submittedData){
					
					return _.contains(classes, submittedData.selector.replace('.', ''));
					
				});
				
				// specific listeners
				if(typeof data !== 'undefined' && typeof data.data !== 'undefined'){
					data = data.data;
					
				// group listeners
				}else{
					data = listener.datas[0].data;
				}
				
				if($(this).attr('data-id')){
					
					if(typeof data == 'undefined'){
						data = {};
					}
					
					data.sender = $(this).attr('data-id');
					data.dataId = $(this).attr('data-id');
				}
				if($(this).attr('data-id2')){
					
					if(typeof data == 'undefined'){
						data = {};
					}
					
					data.sender2 = $(this).attr('data-id2');
					data.dataId2 = $(this).attr('data-id2');
				}
				
				var notObj = {
					type: listener.type,
					data: data
				};
				
				Factory.triggerEvent({
					type: listener.type,
					data: data
				}, listener.moduleId);
				
			});
			
		});
		
	}
	
	function getDomObjListeners(domObjectPiece, selector, listenersList){
				
		if(domObjectPiece.constructor != Object && !domObjectPiece.hasOwnProperty('selector')){
		
			return listenersList;
			
		}
		
		_.each(domObjectPiece, function(domObjectInnerPiece, key){

			if (domObjectInnerPiece.constructor == Object && domObjectInnerPiece.hasOwnProperty('selector') && key != 'notifications' && key != 'listeners') {

				getDomObjListeners(domObjectInnerPiece, domObjectPiece.selector, listenersList);
				
			} else if (domObjectInnerPiece.constructor == Object && key == 'notifications' && !_.isEmpty(domObjectInnerPiece)) {

				_.each(domObjectInnerPiece, function(notification, notificationType){
					
					if (typeof notification !== 'undefined' && notification.hasOwnProperty('selector')) {

						if (listenersList.hasOwnProperty(notification.selector)) {
						
							listenersList[notification.selector].datas.push({
								selector: domObjectPiece.selector, 
								data: notification.notification.data
							});
							
						} else {

							listenersList[notification.selector] = {
								eventType: notification.type,
								type: notification.notification.type,
								moduleId: notification.moduleId,
								datas: []
							};
							listenersList[notification.selector].datas.push({
								selector: domObjectPiece.selector, 
								data: notification.notification.data
							});
							
						}
						
					}
					
				});
					
			// sets any listeners that aren't notifications
			}else if(domObjectInnerPiece.constructor == Array && key == 'listeners'){

				_.each(domObjectInnerPiece, function(listener){
				
					listener();
				});
				
			}
			
		});
		
		return listenersList;

	}
	
	return {
		
		/*
domObject: (function(baseClass){
			
			function buildDomHTML(domObjectPiece, selector){
		
				var domHTML = '';
		
				if(domObjectPiece.constructor == Object && domObjectPiece.hasOwnProperty('containerClass')){
					
					$(selector).append(domObjectPiece.html);
					
				}else{
					
					return true;
					
				}
				
				_.each(domObjectPiece, function(domObjectInnerPiece){
					
					if(domObjectInnerPiece.constructor == Object && domObjectInnerPiece.hasOwnProperty('containerClass')){
						
						buildDomHTML(domObjectInnerPiece, domObjectPiece.selector);
						
					}
					
				});
						
			}
			
			return {
				
				domObject: {
						
						selector: '.' + baseClass,
						className: baseClass,
						html: '<div class="container col-sm-12 '+ baseClass +'"></div>',
						build: this.build
					
					},
				
				currentObj: {},
					
				build: function(){
					
					_.each(domObject, function(domObjectPiece){
				
						buildDomHTML(domObjectPiece, domObject.selector, true);
						
					});
					
					return true;
					
				}
				
			}
			
		})(),
*/
		
// 		domObject: domObject,
				
		add: function(newDomElements, containerClass){
			
			return appendDOMObject(domObject, newDomElements, containerClass);
			
// 			domObject[containerClass].push(newDomElements);
			
		},
		
		build: function(){

			_.each(domObject, function(domObjectPiece){
				
				buildDomHTML(domObjectPiece, domObject.selector, true);
				
			});
			
			return true;
		},
		
		get: function(){
			
			return domObject;
			
		},
		
		loadingGIF: function(baseClass){
			
			var loader = {
				
				container: '.' + baseClass + '-loader',
				containerClass: baseClass + '-loader',
				type: 'loading-gif'
				
			};
			
			return loader;
			
		},
		
		/*
make: function(baseClass){
			
			domObj = {
			
					selector: '.' + baseClass,
					className: baseClass,
					html: '<div class="container col-sm-12 '+ baseClass +'"></div>',
					build: this.build,
					
				
			};
			
			return domObj;
								
		},
*/
											
		make: function(selector, moduleId){

			var baseClass = selector.replace('.', '');
			
			return {
				
				selector: selector,
				html: '<div class="container col-sm-12 '+ baseClass +'"></div>',
				makeNode: Sandbox.makeNode,
				appendNode: Sandbox.appendNode,
				loading: Sandbox.loading,
				
				currentObj: {},
					
				build: Sandbox.patchNode	
				
			}
									
		},		
		
		patch: function(){
			
					
					
		},
		
		//legacyColors: ['#D34539', '#C82461', '#862CAC', '#5D3CB2', '#4652B0', '#5995EF', '#69BAD2', '#549487', '#73AE57', '#9CC155', '#F7E94F', '#EEBF2F', '#E69624', '#DE582B', '#9D9D9D', '#697C89'],
		legacycolors: {
			'#D34539': 'red',
			'#C82461': 'pink',
			'#862CAC': 'purple',
			'#5D3CB2': 'violet',
			'#4652B0': 'violet',
			'#5995EF': 'blue',
			'#69BAD2': 'teal',
			'#549487': 'olive',
			'#73AE57': 'green',
			'#9CC155': 'olive',
			'#F7E94F': 'yellow',
			'#EEBF2F': 'yellow', 
			'#E69624': 'orange',
			'#DE582B': 'orange',
			'#9D9D9D': 'grey', 
			'#697C89': 'grey'
		},

		colors: ['red', 'orange', 'yellow', 'olive', 'green', 'teal', 'blue', 'violet', 'purple', 'pink', 'brown', 'grey', 'black', 'white'],
		colorsNameToHex: {
			'red': '#FF2F00',
			'orange': '#FF8400',
			'yellow': '#F5D40F',
			'olive': '#B5CC18',
			'green': '#18CC54',
			'teal': '#00CAC0',
			'blue': '#027eff',
			'violet': '#7A52D1',
			'purple': '#B04ED1',
			'pink': '#FF379B',
			'brown': '#BD7C51',
			'grey': '#767676',
			'black': '#2a2e3a',
			'white': '#ffffff'
		},
		colorsHexToName: {
			'#FF2F00': 'red',
			'#FF8400': 'orange',
			'#F5D40F': 'yellow',
			'#B5CC18': 'olive',
			'#18CC54': 'green',
			'#00CAC0': 'teal',
			'#027eff': 'blue',
			'#7A52D1': 'violet',
			'#B04ED1': 'purple',
			'#FF379B': 'pink',
			'#BD7C51': 'brown',
			'#767676': 'grey',
			'#2a2e3a': 'black',
			'#ffffff': 'white'
		},
		colorsNameToRGB: {
			'red': 'rgb(219,40,40)',
			'orange': 'rgb(242,113,28)',
			'yellow': 'rgb(251,189,8)',
			'olive': 'rgb(181,204,24)',
			'green': 'rgb(33,186,69)',
			'teal': 'rgb(0,181,173)',
			'blue': 'rgb(33,133,208)',
			'violet': 'rgb(100,53,201)',
			'purple': 'rgb(163,51,200)',
			'pink': 'rgb(224,57,151)',
			'brown': 'rgb(165,103,63)',
			'grey': 'rgb(118,118,118)',
			'black': 'rgb(27,28,29)',
			'white': 'rgb(255,255,255)'
		},
		colorsRGBToName: {
			'rgb(219,40,40)': 'red',
			'rgb(242,113,28)': 'orange',
			'rgb(251,189,8)': 'yellow',
			'rgb(181,204,24)': 'olive',
			'rgb(33,186,69)': 'green',
			'rgb(0,181,173)': 'teal',
			'rgb(33,133,208)': 'blue',
			'rgb(100,53,201)': 'violet',
			'rgb(163,51,200)': 'purple',
			'rgb(224,57,151)': 'pink',
			'rgb(165,103,63)': 'brown',
			'rgb(118,118,118)': 'grey',
			'rgb(27,28,29)': 'black',
			'rgb(255,255,255)': 'white'
		},

		colorsIsHexadecimal: function(color) {
		
			// Test if the color is hexadecimal
			if ( /^#[0-9A-F]{6}$/i.test(color) ) { // this tests for #666666 (six characters)
				return true;
			} else if ( /^#([0-9A-F]{3}){1,2}$/i.test(color) ) { // this tests for #333 (three characters)
				return true;
			} else {
				return false;
			}
			
		},
			
		colorsRGBToHex: function(rgb) {
			
			// Turn "rgb(r,g,b)" into [r,g,b]
			rgb = rgb.substr(4).split(")")[0].split(', ');
			
			let r = (+rgb[0]).toString(16), g = (+rgb[1]).toString(16), b = (+rgb[2]).toString(16);
			
			if (r.length == 1) {
				r = "0" + r;
			}
			
			if (g.length == 1) {
				g = "0" + g;
			}
				
			if (b.length == 1) {
				b = "0" + b;
			}
			
			return "#" + r + g + b;
			
		},
		
		icons: [ // version 2.1.6
			{ name:  'search', content: '\\f002' },
			{ name:  'mail outline', content: '\\f003' },
			{ name:  'external', content: '\\f08e' },
			{ name:  'signal', content: '\\f012' },
			{ name:  'setting', content: '\\f013' },
			{ name:  'home', content: '\\f015' },
			{ name:  'inbox', content: '\\f01c' },
			{ name:  'browser', content: '\\f022' },
			{ name:  'tag', content: '\\f02b' },
			{ name:  'tags', content: '\\f02c' },
			{ name:  'calendar', content: '\\f073' },
			{ name:  'comment', content: '\\f075' },
			{ name:  'comments', content: '\\f086' },
			{ name:  'shop', content: '\\f07a' },
			{ name:  'privacy', content: '\\f084' },
			{ name:  'settings', content: '\\f085' },
			{ name:  'trophy', content: '\\f091' },
			{ name:  'payment', content: '\\f09d' },
			{ name:  'feed', content: '\\f09e' },
			{ name:  'alarm outline', content: '\\f0a2' },
			{ name:  'tasks', content: '\\f0ae' },
			{ name:  'cloud', content: '\\f0c2' },
			{ name:  'lab', content: '\\f0c3' },
			{ name:  'mail', content: '\\f0e0' },
			{ name:  'idea', content: '\\f0eb' },
			{ name:  'dashboard', content: '\\f0e4' },
			{ name:  'sitemap', content: '\\f0e8' },
			{ name:  'alarm', content: '\\f0f3' },
			{ name:  'terminal', content: '\\f120' },
			{ name:  'code', content: '\\f121' },
			{ name:  'protect', content: '\\f132' },
			{ name:  'calendar outline', content: '\\f133' },
			{ name:  'ticket', content: '\\f145' },
			{ name:  'external square', content: '\\f14c' },
			{ name:  'map', content: '\\f14e' },
			{ name:  'bug', content: '\\f188' },
			{ name:  'mail square', content: '\\f199' },
			{ name:  'history', content: '\\f1da' },
			{ name:  'options', content: '\\f1de' },
			{ name:  'comment outline', content: '\\f0e5' },
			{ name:  'comments outline', content: '\\f0e6' },
			{ name:  'text telephone', content: '\\f1e4' },
			{ name:  'find', content: '\\f1e5' },
			{ name:  'wifi', content: '\\f1eb' },
			{ name:  'alarm slash', content: '\\f1f6' },
			{ name:  'alarm slash outline', content: '\\f1f7' },
			{ name:  'copyright', content: '\\f1f9' },
			{ name:  'at', content: '\\f1fa' },
			{ name:  'eyedropper', content: '\\f1fb' },
			{ name:  'paint brush', content: '\\f1fc' },
			{ name:  'heartbeat', content: '\\f21e' },
			{ name:  'download', content: '\\f019' },
			{ name:  'repeat', content: '\\f01e' },
			{ name:  'refresh', content: '\\f021' },
			{ name:  'lock', content: '\\f023' },
			{ name:  'bookmark', content: '\\f02e' },
			{ name:  'print', content: '\\f02f' },
			{ name:  'write', content: '\\f040' },
			{ name:  'theme', content: '\\f043' },
			{ name:  'adjust', content: '\\f042' },
			{ name:  'edit', content: '\\f044' },
			{ name:  'external share', content: '\\f045' },
			{ name:  'ban', content: '\\f05e' },
			{ name:  'mail forward', content: '\\f064' },
			{ name:  'share', content: '\\f064' },
			{ name:  'expand', content: '\\f065' },
			{ name:  'compress', content: '\\f066' },
			{ name:  'unhide', content: '\\f06e' },
			{ name:  'hide', content: '\\f070' },
			{ name:  'random', content: '\\f074' },
			{ name:  'retweet', content: '\\f079' },
			{ name:  'sign out', content: '\\f08b' },
			{ name:  'pin', content: '\\f08d' },
			{ name:  'sign in', content: '\\f090' },
			{ name:  'upload', content: '\\f093' },
			{ name:  'call', content: '\\f095' },
			{ name:  'call square', content: '\\f098' },
			{ name:  'remove bookmark', content: '\\f097' },
			{ name:  'unlock', content: '\\f09c' },
			{ name:  'configure', content: '\\f0ad' },
			{ name:  'filter', content: '\\f0b0' },
			{ name:  'wizard', content: '\\f0d0' },
			{ name:  'undo', content: '\\f0e2' },
			{ name:  'exchange', content: '\\f0ec' },
			{ name:  'cloud download', content: '\\f0ed' },
			{ name:  'cloud upload', content: '\\f0ee' },
			{ name:  'reply', content: '\\f112' },
			{ name:  'reply all', content: '\\f122' },
			{ name:  'erase', content: '\\f12d' },
			{ name:  'unlock alternate', content: '\\f13e' },
			{ name:  'archive', content: '\\f187' },
			{ name:  'translate', content: '\\f1ab' },
			{ name:  'recycle', content: '\\f1b8' },
			{ name:  'send', content: '\\f1d8' },
			{ name:  'send outline', content: '\\f1d9' },
			{ name:  'share alternate', content: '\\f1e0' },
			{ name:  'share alternate square', content: '\\f1e1' },
			{ name:  'wait', content: '\\f017' },
			{ name:  'write square', content: '\\f14b' },
			{ name:  'share square', content: '\\f14d' },
			{ name:  'add to cart', content: '\\f217' },
			{ name:  'in cart', content: '\\f218' },
			{ name:  'add user', content: '\\f234' },
			{ name:  'remove user', content: '\\f235' },
			{ name:  'help circle', content: '\\f059' },
			{ name:  'info circle', content: '\\f05a' },
			{ name:  'warning', content: '\\f12a' },
			{ name:  'warning circle', content: '\\f06a' },
			{ name:  'warning sign', content: '\\f071' },
			{ name:  'help', content: '\\f128' },
			{ name:  'info', content: '\\f129' },
			{ name:  'announcement', content: '\\f0a1' },
			{ name:  'birthday', content: '\\f1fd' },
			{ name:  'users', content: '\\f0c0' },
			{ name:  'doctor', content: '\\f0f0' },
			{ name:  'child', content: '\\f1ae' },
			{ name:  'user', content: '\\f007' },
			{ name:  'handicap', content: '\\f193' },
			{ name:  'student', content: '\\f19d' },
			{ name:  'spy', content: '\\f21b' },
			{ name:  'female', content: '\\f182' },
			{ name:  'male', content: '\\f183' },
			{ name:  'woman', content: '\\f221' },
			{ name:  'man', content: '\\f222' },
			{ name:  'non binary transgender', content: '\\f223' },
			{ name:  'intergender', content: '\\f224' },
			{ name:  'transgender', content: '\\f225' },
			{ name:  'lesbian', content: '\\f226' },
			{ name:  'gay', content: '\\f227' },
			{ name:  'heterosexual', content: '\\f228' },
			{ name:  'other gender', content: '\\f229' },
			{ name:  'other gender vertical', content: '\\f22a' },
			{ name:  'other gender horizontal', content: '\\f22b' },
			{ name:  'neuter', content: '\\f22c' },
			{ name:  'grid layout', content: '\\f00a' },
			{ name:  'list layout', content: '\\f00b' },
			{ name:  'block layout', content: '\\f009' },
			{ name:  'zoom', content: '\\f00e' },
			{ name:  'zoom out', content: '\\f010' },
			{ name:  'resize vertical', content: '\\f07d' },
			{ name:  'resize horizontal', content: '\\f07e' },
			{ name:  'maximize', content: '\\f0b2' },
			{ name:  'crop', content: '\\f125' },
			{ name:  'cocktail', content: '\\f000' },
			{ name:  'road', content: '\\f018' },
			{ name:  'flag', content: '\\f024' },
			{ name:  'book', content: '\\f02d' },
			{ name:  'gift', content: '\\f06b' },
			{ name:  'leaf', content: '\\f06c' },
			{ name:  'fire', content: '\\f06d' },
			{ name:  'plane', content: '\\f072' },
			{ name:  'magnet', content: '\\f076' },
			{ name:  'legal', content: '\\f0e3' },
			{ name:  'lemon', content: '\\f094' },
			{ name:  'world', content: '\\f0ac' },
			{ name:  'travel', content: '\\f0b1' },
			{ name:  'shipping', content: '\\f0d1' },
			{ name:  'money', content: '\\f0d6' },
			{ name:  'lightning', content: '\\f0e7' },
			{ name:  'rain', content: '\\f0e9' },
			{ name:  'treatment', content: '\\f0f1' },
			{ name:  'suitcase', content: '\\f0f2' },
			{ name:  'bar', content: '\\f0fc' },
			{ name:  'flag outline', content: '\\f11d' },
			{ name:  'flag checkered', content: '\\f11e' },
			{ name:  'puzzle', content: '\\f12e' },
			{ name:  'fire extinguisher', content: '\\f134' },
			{ name:  'rocket', content: '\\f135' },
			{ name:  'anchor', content: '\\f13d' },
			{ name:  'bullseye', content: '\\f140' },
			{ name:  'sun', content: '\\f185' },
			{ name:  'moon', content: '\\f186' },
			{ name:  'fax', content: '\\f1ac' },
			{ name:  'life ring', content: '\\f1cd' },
			{ name:  'bomb', content: '\\f1e2' },
			{ name:  'soccer', content: '\\f1e3' },
			{ name:  'calculator', content: '\\f1ec' },
			{ name:  'diamond', content: '\\f219' },
			{ name:  'crosshairs', content: '\\f05b' },
			{ name:  'asterisk', content: '\\f069' },
			{ name:  'certificate', content: '\\f0a3' },
			{ name:  'circle', content: '\\f111' },
			{ name:  'quote left', content: '\\f10d' },
			{ name:  'quote right', content: '\\f10e' },
			{ name:  'ellipsis horizontal', content: '\\f141' },
			{ name:  'ellipsis vertical', content: '\\f142' },
			{ name:  'cube', content: '\\f1b2' },
			{ name:  'cubes', content: '\\f1b3' },
			{ name:  'circle notched', content: '\\f1ce' },
			{ name:  'circle thin', content: '\\f1db' },
			{ name:  'square outline', content: '\\f096' },
			{ name:  'square', content: '\\f0c8' },
			{ name:  'checkmark', content: '\\f00c' },
			{ name:  'remove', content: '\\f00d' },
			{ name:  'checkmark box', content: '\\f046' },
			{ name:  'move', content: '\\f047' },
			{ name:  'add circle', content: '\\f055' },
			{ name:  'minus circle', content: '\\f056' },
			{ name:  'remove circle', content: '\\f057' },
			{ name:  'check circle', content: '\\f058' },
			{ name:  'remove circle outline', content: '\\f05c' },
			{ name:  'check circle outline', content: '\\f05d' },
			{ name:  'plus', content: '\\f067' },
			{ name:  'minus', content: '\\f068' },
			{ name:  'add square', content: '\\f0fe' },
			{ name:  'radio', content: '\\f10c' },
			{ name:  'selected radio', content: '\\f192' },
			{ name:  'minus square', content: '\\f146' },
			{ name:  'minus square outline', content: '\\f147' },
			{ name:  'check square', content: '\\f14a' },
			{ name:  'plus square outline', content: '\\f196' },
			{ name:  'toggle off', content: '\\f204' },
			{ name:  'toggle on', content: '\\f205' },
			{ name:  'film', content: '\\f008' },
			{ name:  'sound', content: '\\f025' },
			{ name:  'photo', content: '\\f030' },
			{ name:  'bar chart', content: '\\f080' },
			{ name:  'camera retro', content: '\\f083' },
			{ name:  'newspaper', content: '\\f1ea' },
			{ name:  'area chart', content: '\\f1fe' },
			{ name:  'pie chart', content: '\\f200' },
			{ name:  'line chart', content: '\\f201' },
			{ name:  'arrow circle outline down', content: '\\f01a' },
			{ name:  'arrow circle outline up', content: '\\f01b' },
			{ name:  'chevron left', content: '\\f053' },
			{ name:  'chevron right', content: '\\f054' },
			{ name:  'arrow left', content: '\\f060' },
			{ name:  'arrow right', content: '\\f061' },
			{ name:  'arrow up', content: '\\f062' },
			{ name:  'arrow down', content: '\\f063' },
			{ name:  'chevron up', content: '\\f077' },
			{ name:  'chevron down', content: '\\f078' },
			{ name:  'pointing right', content: '\\f0a4' },
			{ name:  'pointing left', content: '\\f0a5' },
			{ name:  'pointing up', content: '\\f0a6' },
			{ name:  'pointing down', content: '\\f0a7' },
			{ name:  'arrow circle left', content: '\\f0a8' },
			{ name:  'arrow circle right', content: '\\f0a9' },
			{ name:  'arrow circle up', content: '\\f0aa' },
			{ name:  'arrow circle down', content: '\\f0ab' },
			{ name:  'caret down', content: '\\f0d7' },
			{ name:  'caret up', content: '\\f0d8' },
			{ name:  'caret left', content: '\\f0d9' },
			{ name:  'caret right', content: '\\f0da' },
			{ name:  'angle double left', content: '\\f100' },
			{ name:  'angle double right', content: '\\f101' },
			{ name:  'angle double up', content: '\\f102' },
			{ name:  'angle double down', content: '\\f103' },
			{ name:  'angle left', content: '\\f104' },
			{ name:  'angle right', content: '\\f105' },
			{ name:  'angle up', content: '\\f106' },
			{ name:  'angle down', content: '\\f107' },
			{ name:  'chevron circle left', content: '\\f137' },
			{ name:  'chevron circle right', content: '\\f138' },
			{ name:  'chevron circle up', content: '\\f139' },
			{ name:  'chevron circle down', content: '\\f13a' },
			{ name:  'toggle down', content: '\\f150' },
			{ name:  'toggle up', content: '\\f151' },
			{ name:  'toggle right', content: '\\f152' },
			{ name:  'long arrow down', content: '\\f175' },
			{ name:  'long arrow up', content: '\\f176' },
			{ name:  'long arrow left', content: '\\f177' },
			{ name:  'long arrow right', content: '\\f178' },
			{ name:  'arrow circle outline right', content: '\\f18e' },
			{ name:  'arrow circle outline left', content: '\\f190' },
			{ name:  'toggle left', content: '\\f191' },
			{ name:  'power', content: '\\f011' },
			{ name:  'trash', content: '\\f1f8' },
			{ name:  'trash outline', content: '\\f014' },
			{ name:  'disk outline', content: '\\f0a0' },
			{ name:  'desktop', content: '\\f108' },
			{ name:  'laptop', content: '\\f109' },
			{ name:  'tablet', content: '\\f10a' },
			{ name:  'mobile', content: '\\f10b' },
			{ name:  'game', content: '\\f11b' },
			{ name:  'keyboard', content: '\\f11c' },
			{ name:  'plug', content: '\\f1e6' },
			{ name:  'folder', content: '\\f07b' },
			{ name:  'folder open', content: '\\f07c' },
			{ name:  'level up', content: '\\f148' },
			{ name:  'level down', content: '\\f149' },
			{ name:  'file', content: '\\f15b' },
			{ name:  'file outline', content: '\\f016' },
			{ name:  'file text', content: '\\f15c' },
			{ name:  'file text outline', content: '\\f0f6' },
			{ name:  'folder outline', content: '\\f114' },
			{ name:  'folder open outline', content: '\\f115' },
			{ name:  'file pdf outline', content: '\\f1c1' },
			{ name:  'file word outline', content: '\\f1c2' },
			{ name:  'file excel outline', content: '\\f1c3' },
			{ name:  'file powerpoint outline', content: '\\f1c4' },
			{ name:  'file image outline', content: '\\f1c5' },
			{ name:  'file archive outline', content: '\\f1c6' },
			{ name:  'file audio outline', content: '\\f1c7' },
			{ name:  'file video outline', content: '\\f1c8' },
			{ name:  'file code outline', content: '\\f1c9' },
			{ name:  'barcode', content: '\\f02a' },
			{ name:  'qrcode', content: '\\f029' },
			{ name:  'fork', content: '\\f126' },
			{ name:  'html5', content: '\\f13b' },
			{ name:  'css3', content: '\\f13c' },
			{ name:  'rss', content: '\\f09e' },
			{ name:  'rss square', content: '\\f143' },
			{ name:  'openid', content: '\\f19b' },
			{ name:  'database', content: '\\f1c0' },
			{ name:  'server', content: '\\f233' },
			{ name:  'heart', content: '\\f004' },
			{ name:  'star', content: '\\f005' },
			{ name:  'empty star', content: '\\f006' },
			{ name:  'thumbs outline up', content: '\\f087' },
			{ name:  'thumbs outline down', content: '\\f088' },
			{ name:  'star half', content: '\\f089' },
			{ name:  'empty heart', content: '\\f08a' },
			{ name:  'smile', content: '\\f118' },
			{ name:  'frown', content: '\\f119' },
			{ name:  'meh', content: '\\f11a' },
			{ name:  'star half empty', content: '\\f123' },
			{ name:  'thumbs up', content: '\\f164' },
			{ name:  'thumbs down', content: '\\f165' },
			{ name:  'music', content: '\\f001' },
			{ name:  'video play outline', content: '\\f01d' },
			{ name:  'volume off', content: '\\f026' },
			{ name:  'volume down', content: '\\f027' },
			{ name:  'volume up', content: '\\f028' },
			{ name:  'record', content: '\\f03d' },
			{ name:  'step backward', content: '\\f048' },
			{ name:  'fast backward', content: '\\f049' },
			{ name:  'backward', content: '\\f04a' },
			{ name:  'play', content: '\\f04b' },
			{ name:  'pause', content: '\\f04c' },
			{ name:  'stop', content: '\\f04d' },
			{ name:  'forward', content: '\\f04e' },
			{ name:  'fast forward', content: '\\f050' },
			{ name:  'step forward', content: '\\f051' },
			{ name:  'eject', content: '\\f052' },
			{ name:  'unmute', content: '\\f130' },
			{ name:  'mute', content: '\\f131' },
			{ name:  'video play', content: '\\f144' },
			{ name:  'closed captioning', content: '\\f20a' },
			{ name:  'marker', content: '\\f041' },
			{ name:  'coffee', content: '\\f0f4' },
			{ name:  'food', content: '\\f0f5' },
			{ name:  'building outline', content: '\\f0f7' },
			{ name:  'hospital', content: '\\f0f8' },
			{ name:  'emergency', content: '\\f0f9' },
			{ name:  'first aid', content: '\\f0fa' },
			{ name:  'military', content: '\\f0fb' },
			{ name:  'h', content: '\\f0fd' },
			{ name:  'location arrow', content: '\\f124' },
			{ name:  'space shuttle', content: '\\f197' },
			{ name:  'university', content: '\\f19c' },
			{ name:  'building', content: '\\f1ad' },
			{ name:  'paw', content: '\\f1b0' },
			{ name:  'spoon', content: '\\f1b1' },
			{ name:  'car', content: '\\f1b9' },
			{ name:  'taxi', content: '\\f1ba' },
			{ name:  'tree', content: '\\f1bb' },
			{ name:  'bicycle', content: '\\f206' },
			{ name:  'bus', content: '\\f207' },
			{ name:  'ship', content: '\\f21a' },
			{ name:  'motorcycle', content: '\\f21c' },
			{ name:  'street view', content: '\\f21d' },
			{ name:  'hotel', content: '\\f236' },
			{ name:  'train', content: '\\f238' },
			{ name:  'subway', content: '\\f239' },
			{ name:  'table', content: '\\f0ce' },
			{ name:  'columns', content: '\\f0db' },
			{ name:  'sort', content: '\\f0dc' },
			{ name:  'sort ascending', content: '\\f0de' },
			{ name:  'sort descending', content: '\\f0dd' },
			{ name:  'sort alphabet ascending', content: '\\f15d' },
			{ name:  'sort alphabet descending', content: '\\f15e' },
			{ name:  'sort content ascending', content: '\\f160' },
			{ name:  'sort content descending', content: '\\f161' },
			{ name:  'sort numeric ascending', content: '\\f162' },
			{ name:  'sort numeric descending', content: '\\f163' },
			{ name:  'font', content: '\\f031' },
			{ name:  'bold', content: '\\f032' },
			{ name:  'italic', content: '\\f033' },
			{ name:  'text height', content: '\\f034' },
			{ name:  'text width', content: '\\f035' },
			{ name:  'align left', content: '\\f036' },
			{ name:  'align center', content: '\\f037' },
			{ name:  'align right', content: '\\f038' },
			{ name:  'align justify', content: '\\f039' },
			{ name:  'list', content: '\\f03a' },
			{ name:  'outdent', content: '\\f03b' },
			{ name:  'indent', content: '\\f03c' },
			{ name:  'linkify', content: '\\f0c1' },
			{ name:  'cut', content: '\\f0c4' },
			{ name:  'copy', content: '\\f0c5' },
			{ name:  'attach', content: '\\f0c6' },
			{ name:  'save', content: '\\f0c7' },
			{ name:  'content', content: '\\f0c9' },
			{ name:  'unordered list', content: '\\f0ca' },
			{ name:  'ordered list', content: '\\f0cb' },
			{ name:  'strikethrough', content: '\\f0cc' },
			{ name:  'underline', content: '\\f0cd' },
			{ name:  'paste', content: '\\f0ea' },
			{ name:  'unlink', content: '\\f127' },
			{ name:  'superscript', content: '\\f12b' },
			{ name:  'subscript', content: '\\f12c' },
			{ name:  'header', content: '\\f1dc' },
			{ name:  'paragraph', content: '\\f1dd' },
			{ name:  'euro', content: '\\f153' },
			{ name:  'pound', content: '\\f154' },
			{ name:  'dollar', content: '\\f155' },
			{ name:  'rupee', content: '\\f156' },
			{ name:  'yen', content: '\\f157' },
			{ name:  'ruble', content: '\\f158' },
			{ name:  'won', content: '\\f159' },
			{ name:  'lira', content: '\\f195' },
			{ name:  'shekel', content: '\\f20b' },
			{ name:  'paypal', content: '\\f1ed' },
			{ name:  'paypal card', content: '\\f1f4' },
			{ name:  'google wallet', content: '\\f1ee' },
			{ name:  'visa', content: '\\f1f0' },
			{ name:  'mastercard', content: '\\f1f1' },
			{ name:  'discover', content: '\\f1f2' },
			{ name:  'american express', content: '\\f1f3' },
			{ name:  'stripe', content: '\\f1f5' },
			{ name:  'twitter square', content: '\\f081' },
			{ name:  'facebook square', content: '\\f082' },
			{ name:  'linkedin square', content: '\\f08c' },
			{ name:  'github square', content: '\\f092' },
			{ name:  'twitter', content: '\\f099' },
			{ name:  'facebook', content: '\\f09a' },
			{ name:  'github', content: '\\f09b' },
			{ name:  'pinterest', content: '\\f0d2' },
			{ name:  'pinterest square', content: '\\f0d3' },
			{ name:  'google plus square', content: '\\f0d4' },
			{ name:  'google plus', content: '\\f0d5' },
			{ name:  'linkedin', content: '\\f0e1' },
			{ name:  'github alternate', content: '\\f113' },
			{ name:  'maxcdn', content: '\\f136' },
			{ name:  'bitcoin', content: '\\f15a' },
			{ name:  'youtube square', content: '\\f166' },
			{ name:  'youtube', content: '\\f167' },
			{ name:  'xing', content: '\\f168' },
			{ name:  'xing square', content: '\\f169' },
			{ name:  'youtube play', content: '\\f16a' },
			{ name:  'dropbox', content: '\\f16b' },
			{ name:  'stack overflow', content: '\\f16c' },
			{ name:  'instagram', content: '\\f16d' },
			{ name:  'flickr', content: '\\f16e' },
			{ name:  'adn', content: '\\f170' },
			{ name:  'bitbucket', content: '\\f171' },
			{ name:  'bitbucket square', content: '\\f172' },
			{ name:  'tumblr', content: '\\f173' },
			{ name:  'tumblr square', content: '\\f174' },
			{ name:  'apple', content: '\\f179' },
			{ name:  'windows', content: '\\f17a' },
			{ name:  'android', content: '\\f17b' },
			{ name:  'linux', content: '\\f17c' },
			{ name:  'dribbble', content: '\\f17d' },
			{ name:  'skype', content: '\\f17e' },
			{ name:  'foursquare', content: '\\f180' },
			{ name:  'trello', content: '\\f181' },
			{ name:  'gittip', content: '\\f184' },
			{ name:  'vk', content: '\\f189' },
			{ name:  'weibo', content: '\\f18a' },
			{ name:  'renren', content: '\\f18b' },
			{ name:  'pagelines', content: '\\f18c' },
			{ name:  'stack exchange', content: '\\f18d' },
			{ name:  'vimeo', content: '\\f194' },
			{ name:  'slack', content: '\\f198' },
			{ name:  'wordpress', content: '\\f19a' },
			{ name:  'yahoo', content: '\\f19e' },
			{ name:  'google', content: '\\f1a0' },
			{ name:  'reddit', content: '\\f1a1' },
			{ name:  'reddit square', content: '\\f1a2' },
			{ name:  'stumbleupon circle', content: '\\f1a3' },
			{ name:  'stumbleupon', content: '\\f1a4' },
			{ name:  'delicious', content: '\\f1a5' },
			{ name:  'digg', content: '\\f1a6' },
			{ name:  'pied piper', content: '\\f1a7' },
			{ name:  'pied piper alternate', content: '\\f1a8' },
			{ name:  'drupal', content: '\\f1a9' },
			{ name:  'joomla', content: '\\f1aa' },
			{ name:  'behance', content: '\\f1b4' },
			{ name:  'behance square', content: '\\f1b5' },
			{ name:  'steam', content: '\\f1b6' },
			{ name:  'steam square', content: '\\f1b7' },
			{ name:  'spotify', content: '\\f1bc' },
			{ name:  'deviantart', content: '\\f1bd' },
			{ name:  'soundcloud', content: '\\f1be' },
			{ name:  'vine', content: '\\f1ca' },
			{ name:  'codepen', content: '\\f1cb' },
			{ name:  'jsfiddle', content: '\\f1cc' },
			{ name:  'rebel', content: '\\f1d0' },
			{ name:  'empire', content: '\\f1d1' },
			{ name:  'git square', content: '\\f1d2' },
			{ name:  'git', content: '\\f1d3' },
			{ name:  'hacker news', content: '\\f1d4' },
			{ name:  'tencent weibo', content: '\\f1d5' },
			{ name:  'qq', content: '\\f1d6' },
			{ name:  'wechat', content: '\\f1d7' },
			{ name:  'slideshare', content: '\\f1e7' },
			{ name:  'twitch', content: '\\f1e8' },
			{ name:  'yelp', content: '\\f1e9' },
			{ name:  'lastfm', content: '\\f202' },
			{ name:  'lastfm square', content: '\\f203' },
			{ name:  'ioxhost', content: '\\f208' },
			{ name:  'angellist', content: '\\f209' },
			{ name:  'meanpath', content: '\\f20c' },
			{ name:  'buysellads', content: '\\f20d' },
			{ name:  'connectdevelop', content: '\\f20e' },
			{ name:  'dashcube', content: '\\f210' },
			{ name:  'forumbee', content: '\\f211' },
			{ name:  'leanpub', content: '\\f212' },
			{ name:  'sellsy', content: '\\f213' },
			{ name:  'shirtsinbulk', content: '\\f214' },
			{ name:  'simplybuilt', content: '\\f215' },
			{ name:  'skyatlas', content: '\\f216' },
			{ name:  'whatsapp', content: '\\f232' },
			{ name:  'viacoin', content: '\\f237' },
			{ name:  'medium', content: '\\f23a' },
			{ name:  'like', content: '\\f004' },
			{ name:  'favorite', content: '\\f005' },
			{ name:  'video', content: '\\f008' },
			{ name:  'check', content: '\\f00c' },
			{ name:  'close', content: '\\f00d' },
			{ name:  'cancel', content: '\\f00d' },
			{ name:  'delete', content: '\\f00d' },
			{ name:  'x', content: '\\f00d' },
			{ name:  'user times', content: '\\f235' },
			{ name:  'user close', content: '\\f235' },
			{ name:  'user cancel', content: '\\f235' },
			{ name:  'user delete', content: '\\f235' },
			{ name:  'user x', content: '\\f235' },
			{ name:  'zoom in', content: '\\f00e' },
			{ name:  'magnify', content: '\\f00e' },
			{ name:  'shutdown', content: '\\f011' },
			{ name:  'clock', content: '\\f017' },
			{ name:  'time', content: '\\f017' },
			{ name:  'play circle outline', content: '\\f01d' },
			{ name:  'headphone', content: '\\f025' },
			{ name:  'camera', content: '\\f030' },
			{ name:  'video camera', content: '\\f03d' },
			{ name:  'picture', content: '\\f03e' },
			{ name:  'pencil', content: '\\f040' },
			{ name:  'compose', content: '\\f040' },
			{ name:  'point', content: '\\f041' },
			{ name:  'tint', content: '\\f043' },
			{ name:  'signup', content: '\\f044' },
			{ name:  'plus circle', content: '\\f055' },
			{ name:  'dont', content: '\\f05e' },
			{ name:  'minimize', content: '\\f066' },
			{ name:  'add', content: '\\f067' },
			{ name:  'eye', content: '\\f06e' },
			{ name:  'attention', content: '\\f06a' },
			{ name:  'cart', content: '\\f07a' },
			{ name:  'shuffle', content: '\\f074' },
			{ name:  'talk', content: '\\f075' },
			{ name:  'chat', content: '\\f075' },
			{ name:  'shopping cart', content: '\\f07a' },
			{ name:  'bar graph', content: '\\f080' },
			{ name:  'area graph', content: '\\f1fe' },
			{ name:  'pie graph', content: '\\f200' },
			{ name:  'line graph', content: '\\f201' },
			{ name:  'key', content: '\\f084' },
			{ name:  'cogs', content: '\\f085' },
			{ name:  'discussions', content: '\\f086' },
			{ name:  'like outline', content: '\\f087' },
			{ name:  'dislike outline', content: '\\f088' },
			{ name:  'heart outline', content: '\\f08a' },
			{ name:  'log out', content: '\\f08b' },
			{ name:  'thumb tack', content: '\\f08d' },
			{ name:  'winner', content: '\\f091' },
			{ name:  'bookmark outline', content: '\\f097' },
			{ name:  'phone', content: '\\f095' },
			{ name:  'phone square', content: '\\f098' },
			{ name:  'credit card', content: '\\f09d' },
			{ name:  'hdd outline', content: '\\f0a0' },
			{ name:  'bullhorn', content: '\\f0a1' },
			{ name:  'bell', content: '\\f0f3' },
			{ name:  'bell outline', content: '\\f0a2' },
			{ name:  'bell slash', content: '\\f1f6' },
			{ name:  'bell slash outline', content: '\\f1f7' },
			{ name:  'hand outline right', content: '\\f0a4' },
			{ name:  'hand outline left', content: '\\f0a5' },
			{ name:  'hand outline up', content: '\\f0a6' },
			{ name:  'hand outline down', content: '\\f0a7' },
			{ name:  'globe', content: '\\f0ac' },
			{ name:  'wrench', content: '\\f0ad' },
			{ name:  'briefcase', content: '\\f0b1' },
			{ name:  'group', content: '\\f0c0' },
			{ name:  'flask', content: '\\f0c3' },
			{ name:  'sidebar', content: '\\f0c9' },
			{ name:  'bars', content: '\\f0c9' },
			{ name:  'list ul', content: '\\f0ca' },
			{ name:  'list ol', content: '\\f0cb' },
			{ name:  'numbered list', content: '\\f0cb' },
			{ name:  'magic', content: '\\f0d0' },
			{ name:  'truck', content: '\\f0d1' },
			{ name:  'currency', content: '\\f0d6' },
			{ name:  'triangle down', content: '\\f0d7' },
			{ name:  'dropdown', content: '\\f0d7' },
			{ name:  'triangle up', content: '\\f0d8' },
			{ name:  'triangle left', content: '\\f0d9' },
			{ name:  'triangle right', content: '\\f0da' },
			{ name:  'envelope', content: '\\f0e0' },
			{ name:  'conversation', content: '\\f0e6' },
			{ name:  'umbrella', content: '\\f0e9' },
			{ name:  'clipboard', content: '\\f0ea' },
			{ name:  'lightbulb', content: '\\f0eb' },
			{ name:  'ambulance', content: '\\f0f9' },
			{ name:  'medkit', content: '\\f0fa' },
			{ name:  'fighter jet', content: '\\f0fb' },
			{ name:  'beer', content: '\\f0fc' },
			{ name:  'plus square', content: '\\f0fe' },
			{ name:  'computer', content: '\\f108' },
			{ name:  'circle outline', content: '\\f10c' },
			{ name:  'intersex', content: '\\f10c' },
			{ name:  'asexual', content: '\\f10c' },
			{ name:  'spinner', content: '\\f110' },
			{ name:  'gamepad', content: '\\f11b' },
			{ name:  'star half full', content: '\\f123' },
			{ name:  'question', content: '\\f128' },
			{ name:  'eraser', content: '\\f12d' },
			{ name:  'microphone', content: '\\f130' },
			{ name:  'microphone slash', content: '\\f131' },
			{ name:  'shield', content: '\\f132' },
			{ name:  'target', content: '\\f140' },
			{ name:  'play circle', content: '\\f144' },
			{ name:  'pencil square', content: '\\f14b' },
			{ name:  'compass', content: '\\f14e' },
			{ name:  'amex', content: '\\f1f3' },
			{ name:  'eur', content: '\\f153' },
			{ name:  'gbp', content: '\\f154' },
			{ name:  'usd', content: '\\f155' },
			{ name:  'inr', content: '\\f156' },
			{ name:  'cny', content: '\\f157' },
			{ name:  'rmb', content: '\\f157' },
			{ name:  'jpy', content: '\\f157' },
			{ name:  'rouble', content: '\\f158' },
			{ name:  'rub', content: '\\f158' },
			{ name:  'krw', content: '\\f159' },
			{ name:  'btc', content: '\\f15a' },
			{ name:  'sheqel', content: '\\f20b' },
			{ name:  'ils', content: '\\f20b' },
			{ name:  'try', content: '\\f195' },
			{ name:  'zip', content: '\\f187' },
			{ name:  'dot circle outline', content: '\\f192' },
			{ name:  'sliders', content: '\\f1de' },
			{ name:  'wi-fi', content: '\\f1eb' },
			{ name:  'graduation', content: '\\f19d' },
			{ name:  'weixin', content: '\\f1d7' },
			{ name:  'binoculars', content: '\\f1e5' },
			{ name:  'gratipay', content: '\\f184' },
			{ name:  'genderless', content: '\\f1db' },
			{ name:  'teletype', content: '\\f1e4' },
			{ name:  'power cord', content: '\\f1e6' },
			{ name:  'tty', content: '\\f1e4' },
			{ name:  'cc', content: '\\f20a' },
			{ name:  'plus cart', content: '\\f217' },
			{ name:  'arrow down cart', content: '\\f218' },
			{ name:  'detective', content: '\\f21b' },
			{ name:  'venus', content: '\\f221' },
			{ name:  'mars', content: '\\f222' },
			{ name:  'mercury', content: '\\f223' },
			{ name:  'venus double', content: '\\f226' },
			{ name:  'female homosexual', content: '\\f226' },
			{ name:  'mars double', content: '\\f227' },
			{ name:  'male homosexual', content: '\\f227' },
			{ name:  'venus mars', content: '\\f228' },
			{ name:  'mars stroke', content: '\\f229' },
			{ name:  'mars alternate', content: '\\f229' },
			{ name:  'mars vertical', content: '\\f22a' },
			{ name:  'mars horizontal', content: '\\f22b' },
			{ name:  'mars stroke vertical', content: '\\f22a' },
			{ name:  'mars stroke horizontal', content: '\\f22b' },
			{ name:  'facebook official', content: '\\f230' },
			{ name:  'pinterest official', content: '\\f231' },
			{ name:  'bed', content: '\\f236' }
		],

		iconsSemanticToFontAwesome: function(icon, style) {

			var styleOverride = !_.isEmpty(style) ? style : '';

			switch(icon) {

				case 'add':
					icon = 'plus';
					style = 'fas';
					break;
				case 'add circle':
					icon = 'plus-circle';
					style = 'fas';
					break;
				case 'add square':
					icon = 'plus-square';
					style = 'fas';
					break;
				case 'area chart':
					icon = 'chart-area';
					style = 'fas';
					break;
				case 'check circle outline':
					icon = 'check-circle';
					style = 'far';
					break;
				case 'add to cart':
				case 'plus cart':
					icon = 'cart-plus';
					style = 'fas';
					break;
				case 'add user':
					icon = 'user-plus';
					style = 'fas';
					break;
				case 'alarm':
				case 'alarm slash':
					icon = 'alarm-clock';
					style = 'fas';
					break;
				case 'alarm outline':
				case 'alarm slash outline':
					icon = 'alarm-clock';
					style = 'far';
					break;
				case 'american express':
				case 'amex':
					icon = 'cc-amex';
					style = 'fas';
					break;
				case 'attach':
					icon = 'paperclip';
					style = 'fas';
					break;
				case 'archive':
				case 'red archive':
					icon = 'archive';
					style = 'fas';
					break;
				case 'address card outline':
					icon = 'address-card';
					style = 'fas';
					style = 'far';
					break;
				case 'announcement':
					icon = 'bullhorn';
					style = 'fas';
					break;
				case 'arrow circle outline down':
					icon = 'arrow-circle-down';
					style = 'far';
					break;
				case 'arrow circle outline up':
					icon = 'arrow-circle-up';
					style = 'far';
					break;
				case 'arrow circle outline left':
					icon = 'arrow-circle-left';
					style = 'far';
					break;
				case 'arrow circle outline right':
					icon = 'arrow-circle-right';
					style = 'far';
					break;
				case 'asexual':
					icon = 'genderless';
					style = 'fas';
					break;
				case 'bar':
					icon = 'beer';
					style = 'fas';
					break;
				case 'bar chart':
					icon = 'chart-bar';
					style = 'fas';
					break;
				case 'bell outline':
					icon = 'bell';
					style = 'fas';
					break;
				case 'bell slash outline':
					icon = 'bell-slash';
					style = 'far';
					break;
				case 'birthday':
					icon = 'birthday-cake';
					style = 'fas';
					break;
				case 'btc':
					icon = 'bitcoin';
					style = 'fas';
					break;
				case 'bookmark outline':
					icon = 'bookmark';
					style = 'far';
					break;
				case 'building outline':
					icon = 'building';
					style = 'far';
					break;
				case 'calendar alternate outline':
					icon = 'calendar-alt';
					style = 'far';
					break;
				case 'calendar outline':
					icon = 'calendar';
					style = 'far';
					break;
				case 'calendar plus outline':
					icon = 'calendar-plus';
					style = 'far';
					break;
				case 'call':
					icon = 'phone';
					style = 'fas';
					break;
				case 'call square':
					icon = 'phone-square';
					style = 'fas';
					break;
				case 'cart':
				case 'shop':
					icon = 'shopping-cart';
					style = 'fas';
					break;
				case 'cc':
					icon = 'closed-captioning';
					style = 'fas';
					break;
				case 'in cart':
				case 'arrow down cart':
					icon = 'cart-arrow-down';
					style = 'fas';
					break;
				case 'comment outline':
					icon = 'comment';
					style = 'far';
					break;
				case 'chat':
					icon = 'comment';
					style = 'fas';
					break;
				case 'content':
					icon = 'bars';
					style = 'fas';
					break;
				case 'conversation':
				case 'discussions':
					icon = 'comments';
					style = 'fas';
					break;
				case 'comments outline':
					icon = 'comments';
					style = 'far';
					break;
				case 'circle outline':
					icon = 'circle';
					style = 'far';
					break;
				case 'circle thin':
					icon = 'circle';
					style = 'fas';
					break;
				case 'check circle outline':
					icon = 'check-circle';
					style = 'far';
					break;
				case 'check square outline':
					icon = 'check-square';
					style = 'far';
					break;
				case 'checkmark':
					icon = 'check';
					style = 'fas';
					break;
				case 'checkmark box':
					icon = 'check-square';
					style = 'fas';
					break;
				case 'clock outline':
					icon = 'clock';
					style = 'far';
					break;
				case 'cog':
				case 'grey cog':
					icon = 'cog';
					style = 'fas';
					break;
				case 'copy outline':
					icon = 'copy';
					style = 'far';
					break;
				case 'linkify':
					icon = 'link';
					style = 'fas';
					break;
				case 'dashboard':
					icon = 'tachometer-alt';
					style = 'fas';
					break;
				case 'discover':
					icon = 'cc-discover';
					style = 'fas';
					break;
				case 'doctor':
					icon = 'user-md';
					style = 'fas';
					break;
				case 'dot circle outline':
					icon = 'dot-circle';
					style = 'far';
					break;
				case 'edit outline':
				case 'signup':
					icon = 'edit';
					style = 'far';
					break;
				case 'orange edit':
					icon = 'edit';
					style = 'fas';
					break;
				case 'eyedropper':
					icon = 'eye-dropper';
					style = 'fas';
					break;
				case 'ellipsis horizontal':
					icon = 'ellipsis-h';
					style = 'fas';
					break;
				case 'ellispsis vertical':
					icon = 'ellipsis-v';
					style = 'fas';
					break;
				case 'emergency':
					icon = 'ambulance';
					style = 'fas';
					break;
				case 'envelope open outline':
					icon = 'envelope-open';
					style = 'far';
					break;
				case 'euro':
				case 'eur':
					icon = 'euro-sign';
					style = 'fas';
					break;
				case 'external alternate':
					icon = 'external-link-alt';
					style = 'fas';
					break;
				case 'external square alternate':
					icon = 'external-link-square-alt';
					style = 'fas';
					break;
				case 'facebook official':
					icon = 'facebook-square';
					style = 'fas';
					break;
				case 'feed':
					icon = 'rss';
					style = 'fas';
					break;
				case 'first aid':
					icon = 'medkit';
					style = 'fas';
					break;
				case 'file pdf outline':
					icon = 'file-pdf';
					style = 'far';
					break;
				case 'file word outline':
					icon = 'file-word';
					style = 'far';
					break;
				case 'file excel outline':
					icon = 'file-excel';
					style = 'far';
					break;
				case 'file powerpoint outline':
					icon = 'file-powerpoint';
					style = 'far';
					break;
				case 'file image outline':
					icon = 'file-image';
					style = 'far';
					break;
				case 'file archive outline':
					icon = 'file-archive';
					style = 'far';
					break;
				case 'file audio outline':
					icon = 'file-audio';
					style = 'far';
					break;
				case 'file video outline':
					icon = 'file-video';
					style = 'far';
					break;
				case 'file code outline':
					icon = 'file-code';
					style = 'far';
					break;
				case 'file alternate':
					icon = 'file-alt';
					style = 'fas';
					break;
				case 'file alternate outline':
					icon = 'file-alt';
					style = 'far';
					break;
				case 'file outline':
					icon = 'file';
					style = 'far';
					break;
				case 'folder open outline':
					icon = 'folder-open';
					style = 'far';
					break;
				case 'file text':
					icon = 'file-alt';
					style = 'fas';
					break;
				case 'file text outline':
					icon = 'file-alt';
					style = 'far';
					break;
				case 'folder':
					icon = 'folder';
					style = 'far';
					break;
				case 'find':
					icon = 'binoculars';
					style = 'fas';
					break;
				case 'flag outlined':
					icon = 'flag';
					style = 'far';
					break;
				case 'food':
					icon = 'utensils';
					style = 'fas';
					break;
				case 'fork':
					icon = 'code-branch';
					style = 'fas';
					break;
				case 'game':
					icon = 'gamepad';
					style = 'fas';
					break;
				case 'github alternate':
					icon = 'github-alt';
					style = 'fas';
					break;
				case 'grey eye slash icon':
					icon = 'eye-slash';
					style = 'fas';
					break;
				case 'grid layout':
					icon = 'th';
					style = 'fas';
					break;
				case 'group':
					icon = 'users';
					style = 'fas';
					break;
				case 'google blue':
					icon = 'google';
					style = 'fas';
					break;
				case 'h':
					icon = 'h-square';
					style = 'fas';
					break;
				case 'hand outline right':
					icon = 'hand-point-right';
					style = 'far';
					break;
				case 'hand outline left':
					icon = 'hand-point-left';
					style = 'far';
					break;
				case 'hand outline up':
					icon = 'hand-point-up';
					style = 'far';
					break;
				case 'hand outline down':
					icon = 'hand-point-down';
					style = 'far';
					break;
				case 'handicap':
					icon = 'wheelchair';
					style = 'fas';
					break;
				case 'hide':
					icon = 'eye-slash';
					style = 'fas';
					break;
				case 'handshake outline':
					icon = 'handshake';
					style = 'far';
					break;
				case 'hdd outline':
					icon = 'hdd';
					style = 'far';
					break;
				case 'empty heart':
				case 'like':
					icon = 'heart';
					style = 'fas';
					break;
				case 'heart outline':
				case 'like outline':
					icon = 'heart';
					style = 'far';
					break;
				case 'help':
					icon = 'question';
					style = 'fas';
					break;
				case 'help circle':
					icon = 'question-circle';
					style = 'fas';
					break;
				case 'idea':
					icon = 'lightbulb';
					style = 'fas';
					break;
				case 'lab':
					icon = 'flask';
					style = 'fas';
					break;
				case 'legal':
					icon = 'gavel';
					style = 'fas';
					break;
				case 'lightning':
					icon = 'bolt';
					style = 'fas';
					break;
				case 'line chart':
					icon = 'chart-line';
					style = 'fas';
					break;
				case 'lira':
				case 'try':
					icon = 'line-sign';
					style = 'fas';
					break;
				case 'list alternate':
					icon = 'list-alt';
					style = 'fas';
					break;
				case 'list alternate outline':
					icon = 'list-alt';
					style = 'far';
					break;
				case 'list layout':
					icon = 'th-list';
					style = 'fas';
					break;
				case 'log out':
					icon = 'sign-out';
					style = 'fas';
					break;
				case 'block layout':
					icon = 'th-large';
					style = 'fas';
					break;
				case 'mail':
					icon = 'envelope';
					style = 'fas';
					break;
				case 'mail outline':
					icon = 'envelope';
					style = 'far';
					break;
				case 'mail forward':
					icon = 'share';
					style = 'fas';
					break;
				case 'man':
					icon = 'mars';
					break;
				case 'map marker alternate':
					icon = 'map-marker-alt';
					style = 'fas';
					break;
				case 'marker':
				case 'point':
					icon = 'map-marker';
					style = 'fas';
					break;
				case 'mastercard':
					icon = 'cc-mastercard';
					style = 'fas';
					break;
				case 'military':
					icon = 'fighter-jet';
					style = 'fas';
					break;
				case 'money':
				case 'currency':
					icon = 'money-bill-alt';
					style = 'fas';
					break;
				case 'move':
					icon = 'arrows-alt';
					style = 'fas';
					break;
				case 'mute':
					icon = 'microphone-slash';
					style = 'fas';
					break;
				case 'maximize':
					icon = 'expand-alt';
					style = 'fas';
					break;
				case 'minimize':
					icon = 'compress-alt';
					style = 'fas';
					break;
				case 'options':
				case 'sliders':
					icon = 'sliders-h';
					style = 'fas';
					break;
				case 'ordered list':
				case 'numbered list':
					icon = 'list-ol';
					style = 'fas';
					break;
				case 'payment':
					icon = 'credit-card';
					style = 'fas';
					break;
				case 'paypal card':
					icon = 'cc-paypal';
					style = 'fas';
					break;
				case 'picture':
					icon = 'image';
					style = 'fas';
					break;
				case 'pinterest official':
					icon = 'pinterest';
					style = 'fas';
					break;
				case 'pen icon':
					icon = 'pen';
					style = 'fas';
					break;
				case 'pencil':
				case 'orange pencil':
				case 'write':
				case 'compose':
					icon = 'pencil';
					style = 'fas';
					break;
				case 'pie chart':
					icon = 'chart-pie';
					style = 'fas';
					break;
				case 'photo':
					icon = 'camera';
					style = 'fas';
					break;
				case 'plus square outline':
					icon = 'plus-square';
					style = 'far';
					break;
				case 'pointing right':
					icon = 'hand-point-right';
					style = 'fas';
					break;
				case 'pointing left':
					icon = 'hand-point-left';
					style = 'fas';
					break;
				case 'pointing up':
					icon = 'hand-point-up';
					style = 'fas';
					break;
				case 'pointing down':
					icon = 'hand-point-down';
					style = 'fas';
					break;
				case 'pound':
				case 'gbp':
					icon = 'pound-sign';
					style = 'fas';
					break;
				case 'power':
				case 'shutdown':
					icon = 'power-off';
					style = 'fas';
					break;
				case 'power cord':
					icon = 'plug';
					style = 'fas';
					break;
				case 'privacy':
					icon = 'key';
					style = 'fas';
					break;
				case 'protect':
					icon = 'lock';
					style = 'fas';
					break;
				case 'puzzle':
					icon = 'puzzle-piece';
					style = 'fas';
					break;
				case 'radio':
				case 'selected radio':
					icon = 'dot-circle';
					style = 'fas';
					break;
				case 'rain':
					icon = 'umbrella';
					style = 'fas';
					break;
				case 'record':
				case 'video camera':
					icon = 'video';
					style = 'fas';
					break;
				case 'remove':
				case 'close':
				case 'cancel':
				case 'delete':
				case 'x':
					icon = 'times';
					style = 'fas';
					break;
				case 'remove bookmark':
					icon = 'bookmark';
					style = 'fas';
					break;
				case 'remove circle':
					icon = 'times-circle';
					style = 'fas';
					break;
				case 'remove circle outline':
					icon = 'times-circle';
					style = 'far';
					break;
				case 'remove user':
					icon = 'user-minus';
					style = 'fas';
					break;
				case 'resize vertical':
					icon = 'arrows-v';
					style = 'fas';
					break;
				case 'resize horizontal':
					icon = 'arrows-h'
					style = 'fas';
					break;
				case 'ruble':
				case 'rouble':
				case 'rub':
					icon = 'ruble-sign';
					style = 'fas';
					break;
				case 'rupee':
				case 'inr':
					icon = 'rupee-sign';
					style = 'fas';
					break;
				case 'send':
					icon = 'paper-plane';
					style = 'fas';
					break;
				case 'send outline':
					icon = 'paper-plane';
					style = 'far';
					break;
				case 'share alternate':
					icon = 'share-alt';
					style = 'fas';
					break;
				case 'share alternate square':
					icon = 'share-alt-square';
					style = 'fas';
					break;
				case 'shuffle':
					icon = 'random';
					style = 'fas';
					break;
				case 'setting':
					icon = 'cog';
					style = 'fas';
					break;
				case 'settings':
					icon = 'cogs';
					style = 'fas';
					break;
				case 'configure':
					icon = 'wrench';
					style = 'fas';
					break;
				case 'shekel':
				case 'sheqel':
				case 'ils':
					icon = 'shekel-sign';
					style = 'fas';
					break;
				case 'shipping':
					icon = 'truck';
					style = 'fas';
					break;
				case 'sliders horizontal':
					icon = 'sliders-h';
					style = 'fas';
					break;
				case 'soccer':
					icon = 'futbol';
					style = 'fas';
					break;
				case 'sort ascending':
					icon = 'sort-up';
					style = 'fas';
					break;
				case 'sort descending':
					icon = 'sort-down';
					style = 'fas';
					break;
				case 'sort alphabet ascending':
					icon = 'sort-alpha-down';
					style = 'fas';
					break;
				case 'sort alphabet descending':
					icon = 'sort-alpha-up';
					style = 'fas';
					break;
				case 'sort content ascending':
					icon = 'sort-amount-down';
					style = 'fas';
					break;
				case 'sort content descending':
					icon = 'sort-amount-up';
					style = 'fas';
					break;
				case 'sort numeric ascending':
					icon = 'sort-numeric-down';
					style = 'fas';
					break;
				case 'sort numeric descending':
					icon = 'sort-numeric-up';
					style = 'fas';
					break;
				case 'sound':
				case 'headphone':
					icon = 'headphones';
					style = 'fas';
					break;
				case 'spoon':
					icon = 'utensil-spoon';
					style = 'fas';
					break;
				case 'spy':
				case 'detective':
					icon = 'user-secret';
					style = 'fas';
					break;
				case 'star half empty':
				case 'star half full':
					icon = 'star-half';
					style = 'fas'
					break;
				case 'empty star':
				case 'favorite':
					icon = 'star';
					style = 'fas'
					break;
				case 'star outline':
					icon = 'star';
					style = 'far'
					break;
				case 'student':
				case 'graduation':
					icon = 'graduation-cap';
					style = 'fas';
					break;
				case 'sync alternate':
					icon = 'sync-alt';
					style = 'fas';
					break;
				case 'talk':
					icon = 'comment-alt';
					style = 'fas';
					break;
				case 'text telephone':
				case 'teletype':
					icon = 'tty';
					style = 'fas';
					break;
				case 'theme':
					icon = 'tint';
					style = 'fas';
					break;
				case 'thumb tack':
					icon = 'thumbtack';
					style = 'fas';
					break;
				case 'transgender':
				case 'intergender':
				case 'intersex':
					icon = 'transgender';
					style = 'fas';
					break;
				case 'non binary transgender':
					icon = 'mercury';
					style = 'fas';
					break;
				case 'gay':
				case 'male homosexual':
					icon = 'mars-double';
					style = 'fas';
					break;
				case 'lesbian':
				case 'female homosexual':
					icon = 'venus-double';
					style = 'fas';
					break;
				case 'heterosexual':
					icon = 'venus-mars';
					style = 'fas';
					break;
				case 'other gender':
				case 'mars alternate':
					icon = 'mars-stroke';
					style = 'fas';
					break;
				case 'other gender vertical':
				case 'mars vertical':
				case 'mars stroke vertical':
					icon = 'mars-stroke-v';
					style = 'fas';
					break;
				case 'other gender horizontal':
				case 'mars horizontal':
				case 'mars stroke horizontal':
					icon = 'mars-stroke-h';
					style = 'fas';
					break;
				case 'thumbs outline up':
					icon = 'thumbs-up';
					style = 'far';
					break;
				case 'thumbs outline down':
				case 'dislike':
					icon = 'thumbs-down';
					style = 'far';
					break;
				case 'toggle down':
					icon = 'caret-square-down';
					style = 'fas';
					break;
				case 'toggle up':
					icon = 'caret-square-up';
					style = 'fas';
					break;
				case 'toggle right':
					icon = 'caret-square-right';
					style = 'fas';
					break;
				case 'toggle left':
					icon = 'caret-square-left';
					style = 'fas';
					break;
				case 'translate':
					icon = 'language';
					style = 'fas';
					break;
				case 'trash outline':
					icon = 'trash';
					style = 'far';
					break;
				case 'treatment':
					icon = 'stethoscope';
					style = 'fas';
					break;
				case 'triangle up':
					icon = 'caret-up';
					style = 'fas';
					break;
				case 'triangle down':
					icon = 'caret-down';
					style = 'fas';
					break;
				case 'triangle left':
					icon = 'caret-left';
					style = 'fas';
					break;
				case 'triangle right':
				case 'dropdown':
					icon = 'caret-right';
					style = 'fas';
					break;
				case 'truck':
				case 'teal truck':
					icon = 'truck';
					style = 'fas';
					break;
				case 'travel':
					icon = 'briefcase';
					style = 'fas';
					break;
				case 'unlock alternate':
					icon = 'unlock';
					style = 'fas';
					break;
				case 'unhide':
					icon = 'eye';
					style = 'fas';
					break;
				case 'unmute':
					icon = 'microphone';
					style = 'fas';
					break;
				case 'unordered list':
					icon = 'list-ul';
					style = 'fas';
					break;
				case 'usd':
				case 'usd green':
				case 'dollar':
					icon = 'dollar-sign';
					style = 'fas';
					break;
				case 'user close':
				case 'user cancel':
				case 'user delete':
				case 'user x':
					icon = 'user-times';
					style = 'fas';
					break;
				case 'video play':
					icon = 'play-circle';
					style = 'fas';
					break;
				case 'video play outline':
				case 'play circle outline':
					icon = 'play-circle';
					style = 'far';
					break;
				case 'visa':
					icon = 'cc-visa';
					style = 'fas';
					break;
				case 'wait':
				case 'time':
					icon = 'clock';
					style = 'fas';
					break;
				case 'warning':
					icon = 'exclamation';
					style = 'fas';
					break;
				case 'warning circle':
				case 'attention':
					icon = 'exclamation-circle';
					style = 'fas';
					break;
				case 'warning sign':
					icon = 'exclamation-triangle';
					style = 'fas';
					break;
				case 'wi-fi':
					icon = 'wifi';
					style = 'fas';
					break;
				case 'winner':
					icon = 'trophy';
					style = 'fas';
					break;
				case 'wizard':
					icon = 'magic';
					style = 'fas';
					break;
				case 'woman':
					icon = 'venus';
					style = 'fas';
					break;
				case 'won':
				case 'krw':
					icon = 'won-sign';
					style = 'fas';
					break;
				case 'world':
					icon = 'globe';
					style = 'fas';
					break;
				case 'write square':
					icon = 'pencil-square';
					style = 'fas';
					break;
				case 'yen':
				case 'cyn':
				case 'rmb':
				case 'jpy':
					icon = 'yen-size';
					style = 'fas';
					break;
				case 'zoom':
				case 'zoom-in':
				case 'magnify':
					icon = 'search-plus';
					style = 'fas';
					break;
				case 'zip':
					icon = 'file-archive';
					style = 'fas';
					break;
				case 'zoom out':
					icon = 'search-minus';
					style = 'fas';
					break;
				default:
					icon = icon;
					style = 'fas';
					break;

			}

			style = !_.isEmpty(styleOverride) ? styleOverride : style;

			return style + ' fa-' + icon.replace(' ', '-');

		}
				
	}
	
})();