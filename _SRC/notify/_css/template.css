body {
/*   padding-top: 50px; */
}

del {
	background-color: #ebccd1;
}

ins {
	background-color: #d6e9c6;
}

.hide {
	display: none;
}

.global-search {
	margin-top: 12px;
}

.global-search .pda-form {
	margin-bottom: 0px;
}

.navbar-brand {
	padding-top: 24px;
	margin-bottom: 0px !important;
}

.text-bold {
	font-weight: bold;
}

.bg-black {
	background-color: black;
	color: white;
}

.bg-default {
	border: 1px dotted gray;
}

/*
.alert {
	position: fixed;
	bottom: 20px;
	right: 40px;
}
*/

.dataTables_length, .dt-button {
/*
	float: right;
	padding-left: 10px;
*/
}

.dataTables_info {
	
}

.dataTables_filter {
/* 	margin-top: 10px; */
}

.dt-info {
	float: left;
}

.dt-page-btns {
	float: right;
}

.dt-bottom-btns {
	margin-top: 10px;
}

.dt-paging {
	margin-bottom: 10px;
}

.dataTables_processing {
	line-height: 0px;
}

.diff td{
  vertical-align : top;
  white-space    : pre;
  white-space    : pre-wrap;
  font-family    : monospace;
}

.diffInserted {
	border:1px solid rgb(192,255,192);
	background:rgb(224,255,224);
}

.diffDeleted {
	border:1px solid rgb(255,192,192);
	background:rgb(255,224,224);
}


.row {
	margin-left: 0px;
	margin-right: 0px;
}

td {
	vertical-align: middle;
}

.starter-template {
  padding: 40px 15px;
  text-align: center;
}

.admin-container {
	width: 95%;
	margin: 0 auto;
	max-width: 950px;
}

.full-width-container {
	width: 95%;
	margin: 0 auto;
}

.client-container {
	max-width: 1150px;
	margin: 0 auto;
}

.small {
}

.container {
	width: 100% !important;
}

.navbar {
	min-height: 25px;
/* 	background-color: #EC1C24; */
	background-color: gray;
	border-radius: 0px;
}

.navbar-toggle {
	border-color: white;
}

.navbar-toggle .icon-bar {
	border-color: white;
	color: white;
}

.navbar-inverse .navbar-nav > li > a, .navbar-inverse .navbar-brand {
	color: white !important;
}

.navbar-nav > li > a, .navbar-brand {
	color: white;
}

.nav > li > a:hover, .nav > li > a:focus {
	background-color: red !important;
}

.navbar p {
	color: white !important;
	padding: 10px 0;
	margin: 0;
}

.gray-box {
	background-color: white;
	width: 100%;
	padding: 10px 10px;
	border: 2px solid lightgray;
}

.red-box {
	background-color: #ffecec;
	width: 100%;
	padding: 10px 10px;
	border: 2px solid #a80000;
	border-radius: 0px !important;
	color: white;
}

.green-box {
	background-color: #f4fff5;
	width: 100%;
	padding: 10px 10px;
	border: 2px solid #009722;
}

.orange-box {
	background-color: #fffbee;
	width: 100%;
	padding: 10px 10px;
	border: 2px solid #a87000;
}

ul#food-list, ul#bar-list {
	list-style: none !important;
}

a {
	color: inherit;
}

/*
h3 {
	font-weight: bold;
}

h1, h2, h4 {
	font-weight: normal;
}

h1 {
	color: #EC1C24;
}

h2 {
	text-transform: uppercase;
}
*/

#irg-logo {
	display: block;
	margin: 0 auto;
}

#defaultModal {
/* 	width: 700px !important; */
}

.modal-header {
	border-bottom: 0px;
}

.modal-body {
	padding: 5px 15px 15px 15px;
}

label {
	display: block;
}

.admin-header {
	
}

.client-header {
	
}

.client-payment-bar {
	border-top: 1px solid black;
	border-bottom: 1px solid black;
	margin: 10px 0;
}

.client-payment-bar p {
	margin: 0;
	padding: 5px 0;
	color: gray;
	font-size: 1.2em;
}

.loading-image {
	display: block;
	margin: 0 auto;
}

.fixed-height {
	height: 500px;
}

.note-replies {
	padding-top: 10px;
	padding-left: 40px;
}

#task-list p {
	margin-bottom: 0px;
}

.red-background {
	background-color: #EC1C24;
}

.table {
	margin-bottom: 0px;
}

/*
.table-btn {
	z-index: 10;
}
*/

.panel-purple {
	border-color: #edaaff;
}

.panel-purple > .panel-heading {
	background-color: #faebff;
	border-color: #edaaff;
	color: #9a68a7;
}


.popover {
	width: 270px;
}

.modal-body-touch {
    min-height: 100px;
    max-height: 300px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

body ::-webkit-scrollbar {
	width: 6px !important;
}

div.table-responsive {
	overflow-x: visible !important;
}

.availability-table-row {
	width: 100px;
}

@media (min-height: 600px) {
    .modal-body-touch {
        max-height: 500px;
    }
}

@media (min-height: 800px) {
    .modal-body-touch {
        max-height: 700px;
    }
}

@media (min-height: 1000px) {
    .modal-body-touch {
        max-height: 800px;
    }
}

@media (min-height: 1200px) {
    .modal-body-touch-body {
        max-height: 1000px;
    }
}