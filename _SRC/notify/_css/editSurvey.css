.question-container {
  max-width: 250px;
  margin-left: 50px;
  margin-right: 50px;
  margin-top: 25px;
  margin-bottom: 25px;
  float: left;
/*
  background-color: #2b2b2b;
  color: #f4f4f4;
*/
/*   border-radius: 5px; */
  padding: 10px;
/*
  padding-left: 50px;
  padding-right: 50px;
*/
/*   border-style: solid; */
/*   border-width: 2px; */
	box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
}

.question-complete {
	background-color: rgb(57,127,187);
}
.question-incomplete {
	background-color: rgb(236,161,66);
}

.question-list-item {
	background-color: #f5f5f5;
	margin: 0 auto;
}

.survey-container {
/* 	position: absolute; */
	z-index: 0;
/* 	overflow-y: scroll; */
}

.survey-paths-container {
	position: absolute;
	z-index: 1;
	background-color: none;
	background: rgba(54, 25, 25, 0);
	pointer-events: none
}

.survey-layer-container {
  float: right;

  position: relative;
  left: -50%;
  text-align: left;
}

.layer-even {
  float: right;

  position: relative;
  left: -45%;
  text-align: left;
}

.layer-odd {
  float: right;

  position: relative;
  left: -55%;
  text-align: left;
}

.survey-layer-container > .question-container {
  position: relative;
  left: 50%;
}

.question-text-display {
	background-color: #2b2b2b;
	color: #f4f4f4;
	border-radius: 5px;
	padding: 5px;
}

path:hover {
	width: 10;
	color: aqua;
}

/* question object answer buttons */
.question-answer-button {
	margin: 5px;
}