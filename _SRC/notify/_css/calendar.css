/********** CALENDAR CSS **********/

/* TOP MENU */

.pointer:hover {
	cursor: pointer !important;
}

.hover-underline:hover {
	text-decoration: underline !important;
}

.monthTable,
.monthTable > thead > tr > th {
	border: none !important;
}

.monthTable > thead > tr > th {
	text-align: center !important;
	padding: 0 0 10px 0 !important;
	background-color: transparent;
}

.ui.selectable.monthTable > tbody > tr:hover,
.ui.selectable.week-table-top > tbody > tr:hover,
.ui.selectable.week-table-bottom > tbody > tr:hover,
.ui.selectable.threeDay-table-top > tbody > tr:hover,
.ui.selectable.threeDay-table-bottom > tbody > tr:hover,
.ui.selectable.listViewTable > tbody > tr:hover,
.ui.selectable.day-table-top > tbody > tr:hover,
.ui.selectable.day-table-bottom > tbody > tr:hover {
	background-color: white !important;
}

.monthTable > tbody > tr > td {
/* 	border: .5px solid rgba(34, 36, 38, .1) !important; */
/* 	border: .1px solid rgb(245, 245, 245) !important; */
	width: 14.29% !important;
	padding: 0 !important;
	z-index: 1000;
}

.monthTable > tbody > tr > td:hover {
	background-color: #FCF8E3;
}

.month_cell {
	height: 100px !important;
}

.month_cell > div:nth-child(1) > div {
	padding: 5px;
}

.month-event {
	padding: 1px 5px !important;
	margin: 2px auto !important;
	width: 100%;
	white-space: nowrap !important;
	position: relative !important;
	overflow: visible !important;
}

.month-event > i {
	font-size: 7px;
	transform: translate(-50%, -50%);
}

.popup-cell {
	padding: 0 0 7px 0 !important;
	z-index: 2000 !important;
}

.today {
	background-color: #FCF8E3 !important;
}

.greyCell {
	background-color: rgb(249, 250, 251);
}

.week-table-top > thead > tr > th,
.week-table-bottom > tbody > tr > td {
	width: 13.29%;
}

.threeDay-table-top > thead > tr > th {
	width: 31%;
}

.week-table-top > thead > tr > th:nth-child(1),
.week-table-bottom > tbody > tr > td:nth-child(1),
.threeDay-table-top > thead > tr > th:nth-child(1),
.threeDay-table-bottom > tbody > tr > td:nth-child(1),
.day-table-top > thead > tr > th:nth-child(1), 
.day-table-bottom > tbody > tr > td:nth-child(1) {
	width: 7%;
}

.week-table-bottom > thead,
.threeDay-table-bottom > thead,
.day-table-bottom > thead {
	display: none !important;
}

.week-table-bottom > tbody > tr:nth-child(even) > td,
.threeDay-table-bottom > tbody > tr:nth-child(even) > td,
.day-table-bottom > tbody > tr:nth-child(even) > td {
	border-top: none !important;
}

.week-table-bottom > tbody > tr > td,
.threeDay-table-bottom > tbody > tr > td,
.day-table-bottom > tbody > tr > td {
	padding: 0 !important;
	height: 30px;
}

.week-table-bottom > tbody > tr > td:nth-child(1),
.threeDay-table-bottom > tbody > tr > td:nth-child(1),
.day-table-bottom > tbody > tr > td:nth-child(1) {
	border-top: none !important;
	text-align: center;
}

.week-table-bottom > tbody > tr > td:nth-child(1) > div:nth-child(1),
.threeDay-table-bottom > tbody > tr > td:nth-child(1) > div:nth-child(1),
.day-table-bottom > tbody > tr > td:nth-child(1) > div:nth-child(1) {
	margin-top: -25px;
}

.week-table-bottom > tbody > tr:nth-child(1) > td:nth-child(1) > div:nth-child(1),
.threeDay-table-bottom > tbody > tr:nth-child(1) > td:nth-child(1) > div:nth-child(1),
.day-table-bottom > tbody > tr:nth-child(1) > td:nth-child(1) > div:nth-child(1) {
	display: none;
}

.week-table-top > tbody > tr > td,
.threeDay-table-top > tbody > tr > td,
.day-table-top > tbody > tr > td {
	height: 50px !important;
	padding: 0 !important;
	vertical-align: top !important;
}

.week-table-top > tbody > tr > td:nth-child(1),
.threeDay-table-top > tbody > tr > td:nth-child(1),
.day-table-top > tbody > tr > td:nth-child(1) {
	vertical-align: middle !important;
}

.listViewTable {
	border: none !important;
}

.listViewTable > thead {
	display: none;
}

.listViewTable > tbody > tr > td {
	padding: 0;
	border: none !important;
}

.listViewTable > tbody > tr > td > p > span {
	font-weight: bold;
	margin: 0 10px;
}

.listViewTable > tbody > tr > td > p > span:nth-child(2) {
	float: right;
}

.all-day-week-event {
	width: 100% !important;
	padding: 3px !important;
	margin: 2px auto !important;
	position: relative !important;
	overflow: visible !important;
}

.table-event-box {
	padding: 0 !important;
	margin: 2px 0 !important;
	border: 0.5px solid lightgrey !important;
/* 	position: absolute !important; */
/* 	opacity: 0.6; */
}

.table-event-box > div {
	padding: 2px !important;
	margin: 0 !important;
}

.table-event-box > div:nth-child(2) {
	font-size: x-small !important;
}

.popup-cell-week {
	padding: 5px !important;
}

.scroll-wrapper {
	height: 75vh;
	overflow: scroll;
	margin: 2px 0 0 !important;
	padding: 0 !important;
	position: relative;
}

.scroll-wrapper::-webkit-scrollbar {
	display: none !important;
}
.calendar-area {
/* 	margin: -14px; */
	margin-right: -14px;
	margin-left: -14px;
	margin-bottom: -14px;
}
.segment > .calendar-area {
	margin: -14px;
}
.calendar-area table {
	border-radius: 0px !important;
	border: none !important;
}

.monthTable > thead > tr > th {
	border-left: none;
	border-bottom-width: 1px					!important;
    border-bottom-style: solid					!important;
    border-bottom-color: rgba(34, 36, 38, 0.1)	!important;
    border-top-width: 1px					!important;
    border-top-style: solid					!important;
    border-top-color: rgba(34, 36, 38, 0.1)	!important;
}

.monthTable > thead > tr > th:not(:first-child) {
    border-radius: 0px !important;
	border-left-width: 1px						!important;
    border-left-style: solid					!important;
    border-left-color: rgba(34, 36, 38, 0.1)	!important;
    
}
.weekTable > thead > tr > th {
    border-radius: 0px !important;
	border-top-width: 1px					!important;
    border-top-style: solid					!important;
    border-top-color: rgba(34, 36, 38, 0.1)	!important;
}