Factory.register('dataCRUD', function(sb) {
	
	var comps = {};
	
	function home_view(dom, state, draw) {
		
		dom.empty();
		
		dom.makeNode('wrapper', 'div', {});
		
		dom.wrapper.makeNode('head', 'div', {});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 2});
		dom.wrapper.makeNode('body', 'div', {});
		
		dom.wrapper.head.makeNode('title', 'div', {text: '<h2><i class="database icon"></i> DataCRUD <div class="ui sub header">For Devs</div></h2>', css: 'ui header'});
		
		dom.wrapper.body.makeNode('load_cont', 'div', {});
		dom.wrapper.body.load_cont.makeNode('loader', 'loader', {});
		dom.wrapper.body.load_cont.makeNode('load_text', 'div', {text: 'Fetching all blueprints ...', css: 'text-center'});
		
		sb.data.db.blueprint.getAll(function(bps) {
			
			var formObj = {
					bps: {
						name: 'bps',
						type: 'select',
						label: 'Select a blueprint type',
						options: []
					}
				};
				
			function process_form(form, load, after) {
				
				var formData = form.process().fields.bps.value;
				
				load();

				sb.data.db.obj.getBlueprint(formData, function(bp) {
						
					after(bp, formData);
					
				});
				
			}
			
			function build_blueprintTable(bp, name, dom, state, draw) {

				var visibleCols = {};
				
				_.each(bp, function(val, key) {
			
					visibleCols = _.extend(visibleCols, {[key]: val.name});
					
				});

				dom.empty();
				
				dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
				
				dom.wrapper.makeNode('head', 'div', {});
				dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
				dom.wrapper.makeNode('message', 'div', {css: 'ui message'});
				dom.wrapper.makeNode('body', 'div', {});
				dom.wrapper.makeNode('create', 'div', {});
				
				dom.wrapper.head.makeNode('title', 'div', {text: '<h2>Viewing all data for: '+ name +'</h2>'});
				
				dom.wrapper.message.makeNode('content', 'div', {tag: 'p', text: 'Click "Reload" after creating new objects to see them displayed in the crud table.'});
				
				if(draw !== undefined) {
					
					draw({
						dom: dom,
						after: function(dom) {
							
							dom.patch();
							
							comps.dataCRUD.notify({
								type: 'show-table',
								data: {
									domObj: dom.wrapper.body,
									objectType: name,
									childObjs: 1,
									visibleCols: visibleCols,
									cells: {},
									searchObjects: false,
									rowLink: {
										type: 'tab',
										header: function(obj) {
											return obj.id;
										},
										action: function(obj, dom, bp) {
					
											sb.obj.edit(dom, name, obj, {});
											
										}
									},
									rowSelection: true,
									multiSelectButtons: {
										erase: {
											name: '<i class="fa fa-trash-o"></i> Delete',
											css: 'pda-btn-red',
											domType: 'none',
											action: function(list) {
												
												var ids = _.pluck(list, 'id');
												
												sb.data.db.obj.erase(name, ids, function(deleted) {
													
													if(deleted) {
														
														comps.dataCRUD.notify({
															type: 'update-table',
															data: {}
														});
														
													}
													
												});
												
											}
										}
									},
									headerButtons: {
										create: {
											name: '<i class="fa fa-plus"></i> Create',
											css: 'pda-btn-green',
											domType: 'none',
											action: function() {
					
												sb.obj.create(dom.wrapper.create, name, {});
																										
											}
										},
										reload: {
											name: 'Reload',
											css: 'pda-btn-blue',
											action: function() {}
										}	
									},
									data: function(paged, callback) {
										
										sb.data.db.obj.getAll(name, function(ret) {
											
											callback(ret);
											
										}, 1, paged);
										
									}
								}
							});
							
						}
					});	
					
				}	 else {
					
					dom.patch();
					
					comps.dataCRUD.notify({
						type: 'show-table',
						data: {
							domObj: dom.wrapper.body,
							objectType: name,
							childObjs: 1,
							visibleCols: visibleCols,
							cells: {},
							searchObjects: false,
							rowLink: {
								type: 'tab',
								header: function(obj) {

									return obj.id;
									
								},
								action: function(obj, dom, bp) {
			
									sb.obj.edit(dom, name, obj, {});
									
								}
							},
							rowSelection: true,
							multiSelectButtons: {
								erase: {
									name: '<i class="fa fa-trash-o"></i> Delete',
									css: 'pda-btn-red',
									domType: 'none',
									action: function(list) {
										
										var ids = _.pluck(list, 'id');
										
										sb.data.db.obj.erase(name, ids, function(deleted) {
											
											if(deleted) {
												
												comps.dataCRUD.notify({
													type: 'update-table',
													data: {}
												});
												
											}
											
										});
										
									}
								}
							},
							headerButtons: {
								create: {
									name: '<i class="fa fa-plus"></i> Create',
									css: 'pda-btn-green',
									domType: 'none',
									action: function() {
			
										sb.obj.create(dom.wrapper.create, name, {});
																								
									}
								},
								reload: {
									name: 'Reload',
									css: 'pda-btn-blue',
									action: function() {}
								}	
							},
							data: function(paged, callback) {
								
								sb.data.db.obj.getAll(name, function(ret) {
									
									callback(ret);
									
								}, 1, paged);
								
							}
						}
					});
					
				}
				
			}
			
			if(bps) {
				
				_.each(bps, function(obj, key) {
					
					formObj.bps.options.push({
						name: obj.blueprint_name,
						value: obj.blueprint_name 
					});
					
				}, this);
				
				formObj.bps.options = _.sortBy(formObj.bps.options, function(obj) {
					return obj.name;
				});
				
				dom.wrapper.body.empty();
				
				dom.wrapper.body.makeNode('grid', 'div', {css: 'ui equal width grid'});
				
				dom.wrapper.body.grid.makeNode('col1', 'div', {css: 'column'});
				dom.wrapper.body.grid.makeNode('col2', 'div', {css: 'column'});
				dom.wrapper.body.grid.makeNode('col3', 'div', {css: 'column'});
				
				dom.wrapper.body.grid.col2.makeNode('form', 'form', formObj);
				
				dom.wrapper.body.grid.col2.makeNode('lb_1', 'lineBreak', {spaces: 1});
				
				dom.wrapper.body.grid.col2.makeNode('btnGrp', 'div', {});
				
				dom.wrapper.body.grid.col2.btnGrp.makeNode('next', 'div', {css: 'ui green button right floated', text: 'Next'}).notify('click', {
					type: 'dataCRUD-run',
					data: {
						run: function(data) {
							
							process_form(dom.wrapper.body.grid.col2.form, function() {
								
								dom.wrapper.body.empty();
								
								dom.wrapper.body.makeNode('load_cont', 'div', {});
								dom.wrapper.body.load_cont.makeNode('loader', 'loader', {});
								dom.wrapper.body.load_cont.makeNode('load_text', 'div', {text: 'Fetching blueprint data ...', css: 'text-center'});
								
								dom.wrapper.body.patch();
								
							}, function(bp, value) {

								if(bp && _.isObject(bp)) {
									
									if(!state.hasOwnProperty('bento')) {
										
										sb.notify({
											type: 'app-navigate-to',
											data: {
												itemId: 'dataCRUD',
												viewId: {
													id: 'single-' + value,
													title: value,
													icon: '<i class="fa fa-database"></i>',
													removable: true,
													dom: build_blueprintTable.bind(this, bp, value)
												}
											}
										});
										
									} else {
										
										build_blueprintTable(bp, value, dom, state, draw);
										
									}
									
								} else {
									
									sb.dom.alerts.alert(
										'This blueprint does not exist',
										'Can not find blueprint',
										'warning'
									);
									
									home_view(dom, state, draw);
									
								}
								
							});
							
						}
					}
				}, sb.moduleId);
				
				dom.wrapper.patch();
				
			}
			
		});
		
		if(draw !== undefined) {
			
			draw(false);
			dom.patch();
			
		} else {
			
			dom.patch();
			
		}
		
	}
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'dataCRUD',
						title: 'DataCRUD',
						icon: '<i class="fa fa-database"></i>',
						views: [
							{
								id: 'home-view',
								default: true,
								type: 'custom',
								title: 'Home',
								icon: '<i class="fa fa-home"></i>',
								dom: home_view
							}
						]
					}
				}
			});
			
			sb.listen({
				'dataCRUD-run': this.run,
				'dataCRUD-show': this.show
			});
			
			comps.dataCRUD = sb.createComponent('crud-table');
			
		},
		
		destroy: function() {
		
			_.each(comps, function(v) {
				v.destroy();
			}, this)
			
			comps = {};
			
		},
		
		run: function(data) {
			data.run(data);
		},
		
		show: function(data) {
			
			// Flags
			data.state.bento = true;
			
			home_view(data.domObj, data.state, undefined);
			
		}
		
	}
	
});