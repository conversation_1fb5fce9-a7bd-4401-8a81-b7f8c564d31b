Factory.registerComponent('tuts-walkthroughs-comp', function(sb){
	
	var	domObj = {},
		walkthroughs = [],
		clientId = 0,
		staff = [],
		bases = [];
	
	function prepareComponentBodySection(container, walkthroughsList){
		
		container.makeNode('walkthroughsList', 'container', {});
		
		_.each(_.sortBy(walkthroughsList, function(walkthrough){ return moment(walkthrough.meeting_time, 'l, LT').unix(); }), function(walkthrough){
			
			prepareWalkthroughDomObj(container.walkthroughsList, walkthrough);
			
		});
		
	}
	
	function prepareComponentTopSection(container){
		
		container.makeNode('newWalkthroughButton', 'button', {css: 'pda-btn-green pda-btn-pi pull-right', text: '<i class="fa fa-plus"></i>'})
			.notify('click', {
				type: 'new-walkthrough-button-clicked',
				data: {}
			}, sb.moduleId);
		
	}
	
	function prepareWalkthroughDomObj(container, walkthrough){
		
		container.makeNode('walkthrough-'+ walkthrough.id, 'column', {width: 10})
			.makeNode('line', 'text', {text: '<hr>'});
		
		container['walkthrough-'+ walkthrough.id]
			.makeNode('btns', 'buttonGroup', {css: 'pull-right'})
			.makeNode('edit', 'button', {css: 'pda-btnOutline-orange pda-transparent', text: '<i class="fa fa-pencil" aria-hidden="true"></i>'})
			.notify('click', {
				type: 'edit-walkthrough-button-clicked',
				data: {
					walkthrough: walkthrough
				}
			});
		
		container['walkthrough-'+ walkthrough.id].btns
			.makeNode('remove', 'button', {css: 'pda-btnOutline-red pda-transparent', text: '<i class="fa fa-times" aria-hidden="true"></i>'})
			.notify('click', {
				type: 'delete-walkthrough-button-clicked',
				data: {
					walkthrough: walkthrough
				}
			});
			
		container['walkthrough-'+ walkthrough.id].makeNode('title', 'headerText', {text: walkthrough.meeting_time +' at '+ _.where(bases, {id: walkthrough.venue})[0].name, size: 'x-small'});
		
		var meetWith = '';
		_.each(walkthrough.meet_with, function(attendee, i){
			
			if(i > 0){
				meetWith += ', ';
			}
			var staffObj = _.where(staff, {id: parseInt(attendee)})[0];
			meetWith += staffObj.fname +' '+ staffObj.lname;
			
		});
		
		container['walkthrough-'+ walkthrough.id].makeNode('invitees', 'text', {text: meetWith});
		
	}
	
	return {
		
		init: function(){
			
			sb.listen({
				'load-walkthroughs-list': this.load
			});
			
		},
		
		load: function(setup){
			
			sb.listen({
				'destroy-walkthroughs-list': this.destroy,
				'delete-walkthrough-button-clicked': this.deleteWalkthrough,
				'new-walkthrough-button-clicked': this.getEditWalkthroughModal,
				'edit-walkthrough-button-clicked': this.getEditWalkthroughModal,
				'save-walkthrough-button-clicked': this.saveWalkthrough
			});
			
			if(setup.hasOwnProperty('domObj')){
				
				domObj = sb.dom.make(setup.domObj.selector);
				walkthroughs = setup.walkthroughs;
				clientId = setup.clientId;
				staff = setup.staff;
				bases = setup.bases;
				
			}else{
				
				return false;
				
			}
			
			domObj.makeNode('top', 'container', {});
			prepareComponentTopSection(domObj.top);
			
			domObj.makeNode('body', 'container', {});
			prepareComponentBodySection(domObj.body, walkthroughs);
			
			domObj.makeNode('modal', 'modal', {});
			
			domObj.build();
			
		},
		
		destroy: function(){
			
			sb.listen({
				'load-walkthroughs-list': this.load
			});
			
			domObj = {};
			walkthroughs = [];
			clientId = 0;
			staff = [];
			bases = [];
			
		},
		
		deleteWalkthrough: function(data){
			
			sb.dom.alerts.ask({
				
				title: 'Are you sure?', 
				text: 'This can\'t be undone'
				
			}, function(response){
				
				sb.data.db.obj.erase('walkthroughs', data.walkthrough.id, function(response){
					
					if(response){
						
						sb.dom.alerts.alert('Gone!', 'Walkthrough has been deleted.', 'success');
						
						walkthroughs = _.reject(walkthroughs, function(walkthrough){ return walkthrough.id === data.walkthrough.id; });
						prepareComponentBodySection(domObj.body, walkthroughs);
						domObj.body.patch();
						
					}else{
						
						sb.dom.alerts.alert('Error!', 'Please refresh and try again.', 'error');
						
					}
					
				});
			
			}); 
			
		},
		
		getEditWalkthroughModal: function(data){
			
			var formArgs = {
				
					meeting_time: {
						name: 'meeting_time',
						type: 'date',
						label: 'Date & Time'
					},
					venue: {
						name: 'venue',
						type: 'select',
						label: 'Venue',
						options: []
					},
					meet_with: {
						name: 'meet_with',
						type: 'checkbox',
						label: 'Staff Attendies',
						options: []
					}
					
			};
			
			_.each(bases, function(base){
				
				formArgs.venue.options.push({
					name: base.name,
					value: base.id
				});
				
			});
			
			_.each(staff, function(staffMember){
				
				formArgs.meet_with.options.push({
					name: 'meet_with',
					label: staffMember.lname +', '+ staffMember.fname,
					value: staffMember.id
				});
				
			});
			
			if(data.hasOwnProperty('walkthrough')){
				
				formArgs.id = {
					name: 'id',
					type: 'hidden'
				};
				
				_.each(data.walkthrough, function(val, key){
					if(formArgs.hasOwnProperty(key)){
						formArgs[key].value = val;
					}
				})
				
			}
			
			domObj.modal.body.makeNode('form', 'form', formArgs);
			
			domObj.modal.body.makeNode('save', 'button', {text: 'Save'})
				.notify('click', {
					type: 'save-walkthrough-button-clicked',
					data: {
						form: domObj.modal.body.form,
						modal: domObj.modal
					}
				});
			
			domObj.modal.body.patch();
			domObj.modal.show();
			
		},
		
		saveWalkthrough: function(data){
			
			var	formData = data.form.process(),
				walkthroughObj = {
					client_id: clientId
				};
			
			_.each(formData.fields, function(fieldData, fieldKey){
				
				walkthroughObj[fieldKey] = fieldData.value;
				
			});
			
			if(walkthroughObj.hasOwnProperty('id')){
				
				sb.data.db.obj.update('walkthroughs', walkthroughObj, function(response){
				
					if(response){
						
						sb.dom.alerts.alert('Saved!', 'Walkthrough has been updated.', 'success');
						data.modal.hide();
						
						walkthroughs = _.reject(walkthroughs, function(walkthrough){ return walkthrough.id === response.id; });
						walkthroughs.push(response);
						prepareComponentBodySection(domObj.body, walkthroughs);
						domObj.body.patch();
						
					}else{
						
						sb.dom.alerts.alert('Error!', 'Please refresh and try again.', 'error');
						
					}
					
				});
				
			}else{
				
				sb.data.db.obj.create('walkthroughs', walkthroughObj, function(response){
					
					if(response){
						
						sb.dom.alerts.alert('Saved!', 'Walkthrough has been created.', 'success');
						data.modal.hide();
						
						walkthroughs.push(response);
						prepareComponentBodySection(domObj.body, walkthroughs);
						domObj.body.patch();
						
					}else{
						
						sb.dom.alerts.alert('Error!', 'Please refresh and try again.', 'error');
						
					}
					
				});
				
			}
			
		}
		
	}
	
});