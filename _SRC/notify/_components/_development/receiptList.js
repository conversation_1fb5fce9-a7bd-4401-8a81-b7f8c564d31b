Factory.registerComponent('receiptList', function(sb) {
	
	var domObj = {},
		receiptList = [],
		components = {},
		object_type = '',
		object_id = 0
		staffList = []
		receiptId = 0,
		receipt = {},
		modal = {};
		
	return {
		
		init: function() {
			
			sb.listen({
				
				'load-receipt-list': this.load,
				'load-receipt-view': this.singleReceiptView,
				'receipt-status-button-clicked': this.updateReceiptStatus,
				'process-receipt-status': this.processReceiptStatus, 
				'crud-view-receipts-object': this.view
				
			});
			
		},
		
		load: function(setup) {
console.log(setup);					
			if(setup.hasOwnProperty('domObj')) {
				
				domObj = sb.dom.make(setup.domObj.selector);
				
			}
			
			if(setup.hasOwnProperty('object_type')) {
				
				object_type = setup.object_type;
				
			}
			
			if(setup.hasOwnProperty('object_id')) {
				
				object_id = setup.object_id;
				
			}
			
			if(setup.hasOwnProperty('receiptList')) {
				
				receiptList = setup.receiptList;
				
			}
			
			domObj.makeNode('receiptlistTable', 'container', {});

			domObj.makeNode('modal', 'modal', {});
						
			domObj.build();  
						
			components.table = sb.createComponent('crud');
						
			components.table.notify({ 
					
				type: 'display-crud-table', 
				data: { 
					domObj: domObj.receiptlistTable, 
					objects: receiptList, 
					objectType: 'receipts',
					columns: {
						name: 'Receipt / Name',
						amount: {
							colName: 'Amount',
							rowSetup: function(receipt){ return '$ ' + (receipt.amount / 100).toFixed(2); }
						},
						status: {
							colName: 'Status',
							rowSetup: function(receipt, container){
								
								if(receipt.status == null || receipt.status == 'null') {
									
									container.makeNode('btn', 'button', {text: 'Needs Review', css: 'pda-btnOutline-red pda-center', dataId: receipt.id})
										.notify('click', {
											type: 'receipt-status-button-clicked',
											data: {
												container: container
											}
										}, sb.moduleId);
									
								} else if(receipt.status == 'Submitted') {
									
									container.makeNode('btn', 'button', {text: 'Submitted', css: 'pda-btnOutline-orange pda-center', dataId: receipt.id})
										.notify('click', {
											type: 'receipt-status-button-clicked',
											data: {
												container: container
											}
										}, sb.moduleId);
									
								} else if(receipt.status == 'Approved') {
									
									container.makeNode('btn', 'button', {text: 'Approved', css: 'pda-btnOutline-green pda-center', dataId: receipt.id})
										.notify('click', {
											type: 'receipt-status-button-clicked',
											data: {
												container: container
											}
										}, sb.moduleId);
									
								} else if(receipt.status == 'Denied') {
									
									container.makeNode('btn', 'button', {text: 'Denied', css: 'pda-Btn pda-center', dataId: receipt.id})
										.notify('click', {
											type: 'receipt-status-button-clicked',
											data: {
												container: container
											}
										}, sb.moduleId);
									
								}

								
							}
						}
					},
					buttons: {
						create: true,
						view: true,
						edit: true,
						erase: true
					}, 
					formFields: {
						name: {},
						type: {},
						notes: {
							type: 'textarea'
						}, 
						amount: {},
						object_id: {
							type: 'hidden',
							value: object_id
						},
						object_type: {
							type: 'hidden',
							value: object_type
						}, 
						date_of_expense: {},
						file: {},
						staff_id: {
							type: 'hidden',
							value: sb.data.cookie.userId
						},

					}
				}
					
			});
			
		},
		
		destroy: function() {
			
			domObj = {},
			receiptList = [],
			components = {},
			object_type = '',
			object_id = 0
			staffList = []
			receiptId = 0,
			receipt = {},
			modal = {};
		
		},
		
		processReceiptStatus: function(data) {
			
			domObj.modal.footer.makeNode('processStatus', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Updating...', css:'pda-btnOutline-green pda-center'});
			
			domObj.modal.footer.patch();
			
			var formData = data.form.process(),
				receipt = {
					status: formData.fields.Status.value,
					id: formData.fields.id.value
				};
			
			if(receipt.hasOwnProperty('status')) {
					
				sb.data.db.obj.update('receipts', receipt, function(response) {
										
					receiptList = _.reject(receiptList, function(item) {
				
						return item.id == response.id;
						
					});
					
					receiptList.push(response);
					
					if(components.hasOwnProperty('table')){
						
						components.table.notify({
							type: 'update-crud-table',
							data: response
						});
						
					}
										
					domObj.modal.footer.makeNode('processStatus', 'button', {text:'<i class="fa fa-check"></i> Updated', css:'pda-btnOutline-green pda-center'});
					
					domObj.modal.footer.patch();
					
					sb.notify({
						type: 'receipt-status-changed',
						data: {
							receipt: response,				
						}
					});
					
					setTimeout(function(){
						
						domObj.modal.hide();
						
					}, 800);
					
				});	
					
			}
			
			
		},
		
		singleReceiptView: function(data){
			
console.log(data);

			if (data.hasOwnProperty('domObj')){
				domObj = sb.dom.make(data.domObj.selector);
			}
			
			if(data.hasOwnProperty('receipt')){
				receipt = data.receipt;
			}
			
			if(data.hasOwnProperty('staffList')){
				staffList = data.staffList;
			}
			
			if(data.hasOwnProperty('receiptId')){
				receiptId = data.receiptId;
			}
			
			domObj.makeNode('modal', 'modal', {});			
			domObj.makeNode('title', 'headerText', {text: '<b>Receipt Title</b> : ' + receipt.name, css: 'pda-text-center', size: 'small'});
			domObj.makeNode('lb', 'lineBreak', {spaces: 1});
			domObj.makeNode('submitted', 'text', {text: 'Submitted by: ' + receipt.staff_id.fname + ' ' + receipt.staff_id.lname + ' on ' + moment(receipt.date_created).format("MM/DD/YY, h:mm a")});
			domObj.makeNode('notes', 'text', {text: 'Receipt Notes: ' + receipt.notes});
			domObj.makeNode('status', 'text', {text: '<b>Receipt Status - </b>' + receipt.status});
						
			domObj.makeNode('contOne', 'column', {width: 5});
			domObj.contOne.makeNode('buttonOne', 'button', {text: 'Change Status', css: 'pda-btnOutline-orange pda-align-right'})
				.notify('click', {
					type: 'receipt-status-button-clicked',
					data: {
						receipt: receipt,
						
					}
				}, sb.moduleId);
			domObj.makeNode('contTwo', 'column', {width: 5, offset: 2});
			domObj.contTwo.makeNode('buttonTwo', 'button', {text: 'View Attachment', css: 'pda-btnOutline-primary pda-align-left'})
				.notify('click', {
					type: '',
					data: {}
				}, sb.moduleId);
			domObj.makeNode('llb', 'lineBreak', {spaces: 1});	


			domObj.build();		
		},
		
		updateReceiptStatus: function(data){
console.log(data);			
			if(data.hasOwnProperty('receipt')){
				
				var receipt = data.receipt;
				
			}else {
				
				var receipt = _.where(receiptList, {id: parseInt(data.dataId)})[0];
					
			}
				
			
			domObj.modal.body.makeNode('title', 'headerText', {text:'<i class="fa fa-pencil-square-o" aria-hidden="true"></i> Change Receipt Status', css: 'pda-text-center', size:'small'});
			
			domObj.modal.body.makeNode('statusForm', 'form', {
				
				status: {
					type: 'select',
					name: 'Status',
					label: 'Select Status',
					options: [
					
						{
							name: 'Submitted',
							value: 'Submitted'
						},
						
						{
							name: 'Approved',
							value: 'Approved'
						},
						
						{
							name: 'Denied',
							value: 'Denied'
						}
					
					],
					
					value: receipt.status
				},
				
				id: {
					name: 'id',
					type: 'hidden',
					value: receipt.id
				}
				
			});
			
			domObj.modal.footer.makeNode('processStatus', 'button', {text:'Update Status', css:'pda-btnOutline-green pda-center'}).notify('click', {
				
					type: 'process-receipt-status',
					data: {
						form: domObj.modal.body.statusForm
					},
					
				}, sb.moduleId
			
			);
			
			domObj.modal.patch();
			
			domObj.modal.show();
			
		},
		
		view: function(receiptObj){
console.log('crud-view-receipts-object');
			sb.notify({
				type: 'app-change-page',
				data: {
					to: 'receipt',
					pageParams: {
						receiptId: receiptObj.id,
					}
				}
			});
			
		}
				
	}
	
});