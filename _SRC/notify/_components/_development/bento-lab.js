Factory.registerComponent('bento-lab-comp', function(sb){
	
	var ui = {},
		comps = {};
	
	function createView(ui, bp){
		
		ui.empty();
		
		ui.makeNode('title', 'headerText', {text:'What do you want to call this view?', size: 'small', css:'text-center'});
		
		ui.makeNode('br', 'lineBreak', {});
		
		ui.makeNode('main', 'column', {width:6, offset:3, css:'text-center'});
		ui.main.makeNode('form', 'form', {
			name: {
				name:'name',
				type:'text',
				css:'text-center'
			}
		});
		
		ui.main.makeNode('br2', 'lineBreak', {});
		ui.main.makeNode('btns', 'buttonGroup', {css:'pull-right'})
			.makeNode('create', 'button', {css:'text-center pda-btnOutline-green', text:'<i class="fa fa-check" aria-hidden="true"></i> create'})
			.notify('click', {
				type:'bento-lab-run',
				data: {
					run: function(){
						
						var viewObj = {
							name:'tst'
						};
						
						view_view(ui, viewObj);
					}
				}
			}, sb.moduleId);
		
		ui.patch();
		
	}
	
	function draw_table(){
		
		this.empty();
		
		this.makeNode('header', 'headerText', {text:'Data lab', size:'large'});
		
		this.makeNode('table', 'container', {});
		
		this.build();
		
		var sysSetup = {							
			domObj:this.table,
			tableTitle:'<i class="fa fa-flask" aria-hidden="true"></i> Number lab',
			objectType:'tracker_datum',
			searchObjects:[],					
			headerButtons:{
				create: {
					name: '<i class="fa fa-pencil-square-o" aria-hidden="true"></i> Log data',
					css: 'pda-btn-green',
					domType: 'full',
					action: logDatum
				},
				reload:{
					name:'Reload',
					css:'pda-btn-blue',
					action:function(){}
				}
			},
			rowSelection:true,
			visibleCols:{
				date_created:'Date logged'
			},
			home:{
				action:homeScreenView
			},
			settings:{
				action:[
					{
						object_type:'tracker_item_type',
						name:'Data Types',
						action:trackerDatumTable
					},
					{
						object_type:'inventory_units',
						name:'Units',
						action:function(dom){
							
							sb.notify({
								type:'edit-inventory-units-load',
								data:{
									domObj:dom
								}
							});
						
						}
					}
				]
			},
			cells: {},
			rowLink:{
				type:'tab',
				header:function(obj){
					return '[name of datum type]'+ obj.date_created;
				},
				action:singleDatumView
			},
			data:function(paged, callback){
				
				sb.data.db.obj.getAll('tracker_datum', function(ret){

					callback(ret);
					
				}, 1, paged);
				
			},
			rules: {
				triggers: [{
					name:'an event',
					object_type:'events',
					properties: [{
						label: 'guest count',
						propertyName:'guest_count'
					}]
				}],
				changeable:{
					status:{
						label:'create log'
					}
				}
			}
		}
		
		comps.table = sb.createComponent('crud-table');
		comps.table.notify({
			type:'show-table',
			data:sysSetup
		});
		
	}
	
	function logDatum(bp, container, bp2, object_type, type){
		
		var obj = {};
	
		function formNav(obj, stepData){

			this.empty();
			
			this.makeNode('title', 'headerText', {css:'text-center', text:stepData.title});
			
			this.makeNode('next', 'button', {text:stepData.btnText, css:'pull-right pda-btnOutline-green'})
				.notify('click', {
					type:'bento-lab-run',
					data:{
						run:stepData.process
					}
				}, sb.moduleId);
			
			this.patch();
			
		}
		
		function stepOne(obj){
			
			function processStep(data){
				
				var formData = this.process().fields;
				if(!formData.type.value || formData.type.value === ''){
					
					sb.dom.alerts.alert('Wait!', 'What are you logging?', 'error');
					return false;
					
				}
				
				obj.type = parseInt(formData.type.value);
				container.step.drawStepTwo(obj);
				
				
			}
			
			this.empty();
			
			var formArgs = {
				
				type:{
					name:'type',
					value:obj.type || '',
					type:'select',
					options:[]
				}
				
			};
			
			_.each(bp.type.options, function(name, val){
				
				formArgs.type.options.push({
					name:name,
					value:val
				});
				
			});
			
			this.makeNode('form', 'form', formArgs);
			
			this.patch();
			
			container.nav.draw(obj, {
				process:processStep.bind(this.form),
				btnText:'Next <i class="fa fa-arrow-right" aria-hidden="true"></i>',
				title:'What are you logging?'
			});
			
			
		}
		
		function stepTwo(obj){
			
			var datum_type = {};
			
			function processStep(){
				
				var formData = this.process().fields;				
				obj.data = {};
				
				_.each(formData, function(datum, key){
					
					obj.data[key] = {
						prop_id: parseInt(key),
						value: datum.value
					};
					
					if(_.findWhere(datum_type.datum_properties, {id:parseInt(key)}).type === 'amount'){
						obj.data[key].value = parseInt(obj.data[key].value);
					}
					
				});
				
				sb.data.db.obj.create('tracker_datum', obj, function(r){
					
					console.log(r);
					if(r){
						sb.dom.alerts.alert('Log recorded!', '', 'success');
					}
					
				});
				
			}
			
			this.empty();
			
			this.makeNode('loader', 'loader', {size:'large', css:'text-center'});
			
			this.patch();
			
			var ui = this;
			
			sb.data.db.obj.getWhere('tracker_item_type', {id:obj.type, childObjs:1}, function(datum_types){
				
				datum_type = datum_types[0];
				ui.empty();
				
				ui.makeNode('left', 'column', {width:4});
				ui.left.makeNode('title', 'headerText', {text:'Logging '+ datum_type.name});
				ui.left.makeNode('date', 'headerText', {text:moment().format('l'), size:'x-small'});
				
				ui.makeNode('right', 'column', {width: 8});
				
				var formArgs = [];
				
				_.each(datum_type.datum_properties, function(prop){
					
					var arg = {
						name:prop.id,
						label:prop.name,
						type:''
					};
					
					switch(prop.type){
						
						case 'note':
							arg.type = 'textbox';
							break;
						
						case 'amount':
							arg.type = 'number';
							arg.label += ' ('+ prop.unit_id.name +')';
							break;
						
					}
										
					formArgs.push({
						name:arg.name,
						label:arg.label,
						type:arg.type
					});
					
				});
				
				ui.right.makeNode('form', 'form', formArgs);
				
				ui.patch();
				
				container.nav.draw(obj, {
					process:processStep.bind(ui.right.form),
					btnText:'Create new log <i class="fa fa-check" aria-hidden="true"></i>',
					title:''
				});
				
			});
			
		}

		container.empty();
		
		container.makeNode('nav', 'column', {width:12});
		
		container.makeNode('step', 'column', {width:12});
		
		container.patch();
		
		container.nav.draw = formNav;
// 			container.nav.draw(obj);
		
		container.step.drawStepOne = stepOne;
		container.step.drawStepTwo = stepTwo;
		
		if(type !== undefined){
			
			obj.type = type;
			console.log(obj);
			container.step.drawStepTwo(obj);
			
		}else{
			
			container.step.drawStepOne(obj);
		
		}
		
	}
	
	function homeScreenView(container, bp){
		
		container.empty();
		
		function data_type_table(){
			
			this.makeNode('title', 'headerText', {text:'Data types', size:'small', css:'pull-left'});
			
			this.makeNode('new', 'button', {text:'<i class="fa fa-plus" aria-hidden="true"></i> new', css:'pda-btnOutline-green pda-btn-x-small pull-right', size:'x-small'})
				.notify('click', {
					type:'bento-lab-run',
					data:{
						run: function(){
							
							createDatumType(bp, container);
							
						}
					}
				}, sb.moduleId);
			
			this.makeNode('table', 'table', {
				
				css: 'table-condensed table-responsive',
				columns: {
					btns: ''
				}
				
			});
			
			this.patch();
			
			_.each(bp.type.options, function(name, id){
				
				this.table.makeRow('type-'+ id, ['']);
				this.table.body['type-'+ id].btns.makeNode('view', 'button', {text:name +' <i class="fa fa-arrow-right" aria-hidden="true"></i>', css:'pda-btnOutline-primary pda-btn-fullWidth'})
					.notify('click', {
						type:'bento-lab-run',
						data:{
							run: function(){
								
								container.logs_view(id);
								
							}.bind(this)
						}
					}, sb.moduleId);
				
			}, this);
			
			this.patch();
			
		}
		
		function view_type_table(){
			
			this.makeNode('title', 'headerText', {text:'Views', size:'small', css:'pull-left'});
			
			this.makeNode('new', 'button', {text:'<i class="fa fa-plus" aria-hidden="true"></i> new', css:'pda-btnOutline-green pda-btn-x-small pull-right', size:'x-small'})
				.notify('click', {
					type:'bento-lab-run',
					data:{
						run:function(){
							createView(container, bp);
						}
					}
				}, sb.moduleId);
				
			this.makeNode('table', 'table', {
				
				css: 'table-condensed table-responsive',
				columns: {
					btns: ''
				}
				
			});
			
			this.patch();
			
			_.each(bp.type.options, function(name, id){
				
				this.table.makeRow('type-'+ id, ['']);
				this.table.body['type-'+ id].btns.makeNode('view', 'button', {text: name +' <i class="fa fa-arrow-right" aria-hidden="true"></i>', css:'pda-btnOutline-primary pda-btn-fullWidth'})
					.notify('click', {
						type:'bento-lab-run',
						data:{
							run: function(){
								
								container.view_view(id);
								
							}.bind(this)
						}
					}, sb.moduleId);
				
			}, this);
			
			this.patch();
			
		}
		
		function view_logs(type){
			
			type = parseInt(type);
			
			var data = [],
				paged = {},
				datumType = {};
			
			function view_data(){
				
				if(!datumType.hasOwnProperty('views')){
					
					datumType.views = [];
					
				}
				
				this.empty();
				
				_.each(datumType.views, function(view, i){
					
					this.makeNode('view-'+ i, 'column', {width:view.width})
						.makeNode('c', 'container', {collapse:true, title:view.title});
					
				}, this);
				
				console.log(datumType);
				
				// new view button
				/*
this.makeNode('newView', 'button', {text:'<i class="fa fa-plus" aria-hidden="true"></i> new view', css:'pda-btn-green'})
					.notify('click', {
						type:'bento-lab-run',
						data:{
							run: function(){
								
								container.editView();
								
							}.bind(this)
						}
					}, sb.moduleId);
*/
				
				this.patch();
				
			}
			
			function view_datum(){}
			
			this.empty();
			
			// CONTAINER FOR CREATE NEW LOG FORM
			this.makeNode('createForm', 'column', {width:12})
				.makeNode('c', 'container', {collapse:true, title:'<i class="fa fa-plus" aria-hidden="true"></i> new log'});
			
			logDatum(bp, this.createForm.c, {}, 'tracker_datum', type);
			
			this.makeNode('break', 'lineBreak', {});
			
			// CONTAINER FOR TABLES
			this.makeNode('views', 'column', {width:12})
				.makeNode('loader', 'loader', {size:'large', css:'text-center'});
				
			this.makeNode('editViewContainer', 'column', {width:12});
			this.editView = logDatum.bind(bp, this, {}, 'tracker_datum', type);
			
			this.patch();
			
			var container = this;
			
			sb.data.db.obj.getWhere('tracker_item_type', {id: type, childObjs:1}, function(datum_type_list){
				
				datumType = datum_type_list;
				
				sb.data.db.obj.getWhere('tracker_datum', {type:type, childObjs:1}, function(dataList){
					
					data = dataList;
					container.view_data = view_data.bind(container.views);
					container.view_data();
					
				});
				
			});
			
		}
		
		function view_view(view_id){
			
			this.empty();
			
			this.makeNode('headerText', 'headerText', {text:'View view'});
			
			this.patch();
			
		}
		
		container.makeNode('dataTypes', 'column', {width:4, css:'pda-container'});
		container.makeNode('viewTypes', 'column', {width:8, css:'pda-container'});
		container.patch();
		
		container.viewDataTypes = data_type_table.bind(container.dataTypes);
		container.viewDataTypes();
		
		container.viewViewTypes = view_type_table.bind(container.viewTypes);
		container.viewViewTypes();
		
		container.logs_view = view_logs.bind(container);
		container.view_view = view_view.bind(container);
		
	}
	
	function createDatumType(bp, container){
			
		sb.data.db.obj.getAll('inventory_units', function(units){
			
			var obj = {};
		
			function formNav(obj, stepData){

				this.empty();
				
				this.makeNode('title', 'headerText', {css:'text-center', text:stepData.title});
				
				this.makeNode('next', 'button', {text:stepData.btnText, css:'pull-right pda-btnOutline-green'})
					.notify('click', {
						type:'bento-lab-run',
						data:{
							run:stepData.process
						}
					}, sb.moduleId);
				
				this.patch();
				
			}
			
			function stepOne(obj){
				
				function processStep(data){
					
					var formData = this.process().fields;
					if(!formData.name.value || formData.name.value === ''){
						
						sb.dom.alerts.alert('Wait!', 'Enter a name for what you want to track first.', 'error');
						return false;
						
					}
					
					obj.name = formData.name.value;
					container.step.drawStepTwo(obj);
					
					
				}
				
				this.empty();
				
				this.makeNode('form', 'form', {
					name:{
						name:'name',
						value:obj.name || '',
						placeholder:'something'
					}
				});
				
				this.patch();
				
				container.nav.draw(obj, {
					process:processStep.bind(this.form),
					btnText:'Next <i class="fa fa-arrow-right" aria-hidden="true"></i>',
					title:'What would you like to track?'
				});
				
				
			}
			
			function stepTwo(obj){
				
				function addProp(){
					
					this.process();
					
					obj.datum_properties.push({
						id:obj.datum_properties.length + 1,
						name:'',
						type:''
					});

					this.drawProperties();
					
				}
				
				function processProps(){
					
					_.each(obj.datum_properties, function(prop, i){
						
						prop.name = this.props['prop-'+ i].name.form.process().fields.name.value;
						prop.typeString = this.props['prop-'+ i].type.form.process().fields.type.value;
						prop.type = prop.typeString;
						if(prop.type.indexOf('amount-') !== -1){
							
							prop.unit_id = parseInt(prop.type.split('-')[1]);
							prop.type = 'amount';
							
						}
						
						console.log(prop);
						
					}, this);
					
				}
				
				function processStep(data){
					
					this.process();
					
					console.log(this);
					console.log(obj);
					
					sb.data.db.obj.create('tracker_item_type', obj, function(r){
						console.log(r);
					});
					
				}
				
				function drawProperties(obj){
					
					_.each(obj.datum_properties, function(prop, i){
						
						this.props.makeNode('prop-'+ i, 'container', {});
						this.props['prop-'+ i].setup = setupPropertyUI.bind(this.props['prop-'+ i], prop);
						this.props['prop-'+ i].setup();
						
					}, this);
					
					this.patch();
					
				}
				
				function setupPropertyUI(prop){
					
					var property_options = [];
					
					_.each(units, function(unit){
						
						property_options.push({
							value:'amount-'+ unit.id,
							name:'an amount of '+ unit.name.toUpperCase() +'(s)'
						});
						
					});
					
					property_options.push({
						value:'note',
						name:'a note'
					});
					property_options.push({
						value:'select',
						name:'a single selection from ...'
					});
					property_options.push({
						value:'multi-select',
						name:'a  selection from ...'
					});
					
					this.empty();
					
					// property name
					this.makeNode('name', 'column', {width:5})
						.makeNode('form', 'form', {
							name:{
								name:'name',
								type:'text',
								value:prop.name || ''
							}
						});
					
					// ", a "
					this.makeNode('a', 'column', {width:1})
						.makeNode('text', 'headerText', {size:'x-small', css:'text-center', text:', '});
					
					// type
					this.makeNode('type', 'column', {width:5})
						.makeNode('form', 'form', {
							type: {
								name:'type',
								type:'select',
								value:prop.typeString || '',
								options:property_options
							}
						});
					
					// "."
					this.makeNode('end', 'column', {width:1})
						.makeNode('text', 'headerText', {size:'x-small', css:'text-center', text:'.'});
					
					console.log(prop);
					
				}
				
				this.empty();
				
				if(!obj.hasOwnProperty('datum_properties')){
					obj.datum_properties = [{
						id:1,
						name:'',
						type:''
					}];
				}
				
				this.makeNode('props', 'container', {});
				
				this.drawProperties = drawProperties.bind(this, obj);
				this.process = processProps.bind(this);
				
				this.makeNode('break', 'lineBreak', {});
				
				this.makeNode('footer', 'container', {css:'text-center'})
					.makeNode('add', 'button', {text:'<i class="fa fa-plus" aria-hidden="true"></i> add', css:'pda-btnOutline-green pull-right'})
					.notify('click', {
						type:'bento-lab-run',
						data:{
							run:addProp.bind(this)
						}
					}, sb.moduleId);
				
				this.patch();
				
				this.drawProperties();
				
				container.nav.draw(obj, {

					btnText:'<i class="fa fa-check" aria-hidden="true"></i> Save',
					title:'A '+ obj.name +' consists of ...',
					process:processStep.bind(this)
					
				});
				
				
			}

			container.empty();
			
			container.makeNode('nav', 'column', {width:12});
			
			container.makeNode('step', 'column', {width:12});
			
			container.patch();
			
			container.nav.draw = formNav;
// 			container.nav.draw(obj);
			
			container.step.drawStepOne = stepOne;
			container.step.drawStepTwo = stepTwo;
			
			container.step.drawStepOne(obj);
			
		});
		
	}
	
	function trackerDatumTable(dom){
		
		// SETUP DOM
		dom.empty();
		
		dom.makeNode('table', 'container', {});
		
		dom.patch();
		
		// SETUP TABLE
		
		var tableSetup = {
			domObj:dom.table,
			tableTitle:'<i class="fa fa-flask" aria-hidden="true"></i> Number lab',
			objectType:'tracker_datum',
			searchObjects:false,
			navigation:false,
			headerButtons:{
				create: {
					name: '<i class="fa fa-plus"></i> New data type',
					css: 'pda-btn-green',
					domType: 'full',
					action: createDatumType
				},
				reload:{
					name:'Reload',
					css:'pda-btn-blue',
					action:function(){}
				}
			},
			rowSelection:false,
			rowLink:false,
			visibleCols:{
				name:'Name'
			},
			cells:{},
			data:function(paged, callback){
				
				sb.data.db.obj.getAll('tracker_item_type', function(ret){
	
					callback(ret);
					
				}, 1, paged);
				
			}
			
		};
		
		if(comps.hasOwnProperty('typeTable')){
			delete comps.typeTable;
		}
		
		comps.typeTable = sb.createComponent('crud-table');
		comps.typeTable.notify({
			type:'show-table',
			data:tableSetup
		});
		
	}
	
	function singleDatumView(){}
	
	function view_view(ui, view){
		
		function view_subview(subview, dataTypes){
			
			function table_view(subview){
				
				this.empty();
				
				this.makeNode('test','text', {text:'table view'});
				
				this.patch();
				
			}
			
			function feed_view(subview){
				
				console.log(subview);
				
				this.empty();
				
				if(!subview.hasOwnProperty('display_prop')){
					this.makeNode('message', 'headerText', {text:'no display property selected', size:'x-small', css:'text-center'});
				}
				
				this.makeNode('table', 'container', {});
				
				this.patch();
				
				if(comps.hasOwnProperty('subview-'+ subview.id)){
					
					comps['subview-'+ subview.id].destroy();
					delete comps['subview-'+ subview.id];
					
				}
				
				comps['subview-'+ subview.id] = sb.createComponent('crud-table');
				
				var tableSetup = {
					domObj:this.table,
					tableTitle:'<i class="fa fa-flask" aria-hidden="true"></i> Number lab',
					objectType:'tracker_datum',
					searchObjects:false,
					navigation:false,
					headerButtons:{},
					rowSelection:false,
					rowLink:false,
					visibleCols:{
						date_created:'',
						log:''
					},
					cells:{
						date_created: function(obj){
							
							return moment(obj.date_created, 'YYYY-MM-DD HH:mm:ss').format('l');
							
						},
						log: function(obj, container){
							
							container.makeNode('text', 'text', {text: '<span class="label label-info">'+ _.findWhere(obj.data, {prop_id:subview.display_prop.toString()}).value}) +'</span>';
							console.log(obj, container);
						}
					},
					data:function(paged, callback){
						
						sb.data.db.obj.getAll('tracker_datum', function(ret){
			
							callback(ret);
							
						}, 1, paged);
						
					}
					
				}
				
				comps['subview-'+ subview.id].notify({
					type:'show-table',
					data: tableSetup
				});
				
			}
			
			this.empty();
			this.makeNode('tile', 'column', {width:subview.width, height:subview.height, css:'pda-border pda-container pda-background-'+subview.color});
			
			this.tile.makeNode('container', 'container', {title:subview.title, collapse:true});

			if(subview.data_type === ''){
				
				this.tile.container.makeNode('message', 'headerText', {size:'x-small', text:'Select a data type', css:'text-center text-muted'});
				this.patch();
				return true;
				
			}

			switch(subview.type){
				
				case 'table':
					this.tile.container.refresh = table_view.bind(this.tile.container, subview);
					break;
					
				case 'feed':
					this.tile.container.refresh = feed_view.bind(this.tile.container, subview);
					break;
				
			}
			
			if(this.tile.container.hasOwnProperty('refresh')){
				
				this.tile.container.refresh();
				
			}
			
// 			this.tile.container.makeNode('table', );
			
			this.patch();
			
		}
		
		function edit_subview(viewObj){
			
			function leftColumn(viewObj){
				
				var left = this;
				
				sb.data.db.obj.getAll('tracker_item_type', function(data_types){
					
					var dataTypeOptions = [];
					_.each(data_types, function(data_type){
						
						dataTypeOptions.push({
							name:data_type.name,
							value:data_type.id
						});
						
					});
					
					left.makeNode('message', 'headerText', {text:'Data', size:'x-small', css:'text-center'});
					left.makeNode('form', 'form', {
						data_type:{
							name:'data_type',
							type:'select',
							label:'Data type',
							options: dataTypeOptions
						},
						type:{
							name:'type',
							type:'select',
							value:'table',
							label:'View data as ...',
							options: [{
								name:'a table',
								value:'table'
							}, {
								name:'a feed',
								value:'feed'
							}, {
								name:'a line-plot',
								value:'a line-plot'
							}, {
								name:'a sum',
								value:'sum'
							}]
						}
					});
					
					left.form.notify('change', {
						type:'bento-lab-run',
						data:{
							run:function(){
								
								var formData = left.form.process().fields;
								
								viewObj.data_type = parseInt(formData.data_type.value);
								viewObj.type = formData.type.value;
								
								ui.updateCenter(data_types);
								
								preview.view(viewObj, data_types);
								
							}
						}
					}, sb.moduleId);
					
					left.patch();
					
				}, 1);
				
			}
			
			function optionsForm(viewObj, data_types){
				
				var dataType = _.findWhere(data_types, {id:viewObj.data_type});
				
				function table_options(){
					
					this.empty();
					
					/*
_.each(dataType.datum_properties, function(prop){
						
						this.makeNode('btn-'+ prop.id, 'button', {});
						
					}, this);
*/
					
					this.patch();
					
				}
				
				function feed_options(){
					
					this.empty();
					
					var options = [];
					_.each(dataType.datum_properties, function(prop){
						
						options.push({
							name:prop.name,
							value:prop.id
						});
						
					});
					
					var form = this.makeNode('form', 'form', {
						display_prop:{
							name:'display_prop',
							type:'select',
							value:viewObj.display_prop,
							options:options,
							label:'What to view in the feed'
						}
					});
					
					this.form.notify('change', {
						type:'bento-lab-run',
						data:{
							run:function(){
								
								var formData = form.process().fields;
								
								viewObj.display_prop = parseInt(formData.display_prop.value);
								preview.view(viewObj, data_types);
								
							}
						}
					}, sb.moduleId);
					
					this.patch();
					
				}
				
				this.empty();
				
				// TITLE
				this.makeNode('title', 'headerText', {size:'x-small', css:'text-center', text:'Options'});
				this.makeNode('main', 'container', {});
				
				if(!viewObj.hasOwnProperty('data_type')){
					
					this.makeNode('message', 'text', {text:'No data type selected.', css:'text-center text-muted'});
					this.patch();
					return true;
					
				}else if(!viewObj.hasOwnProperty('type')){
					
					this.makeNode('message', 'text', {text:'No display type selected', css:'text-center text-muted'});
					this.patch();
					return true;
					
				}
				
				this.patch();
				
				switch(viewObj.type){
					
					case 'feed':
						this.view = feed_options.bind(this.main);
						break;
					
					case 'table':
						this.view = table_options.bind(this.main);
						break;
					
					default:
						this.view = function(){};
						break;
					
				}
				
				this.view();
				
			}
			
			function styleForm(viewObj){
				
				var ui = this;
				
				// TITLE
				this.makeNode('title', 'headerText', {size:'x-small', css:'text-center', text:'Style'});
				
				this.makeNode('form', 'form', {
					title:{
						name:'title',
						type:'text',
						label:'Title',
						value:viewObj.title
					},
					color: {
						name:'color',
						type:'select',
						label:'Tile color',
						value:viewObj.color,
						options:[{
							name:'None',
							value:''
						}, {
							name:'Light blue',
							value:'primary'
						}, {
							name:'Blue',
							value:'blue'
						}, {
							name:'Red',
							value:'red'
						}, {
							name:'Green',
							value:'green'
						}, {
							name:'Purple',
							value:'purple'
						}, {
							name:'Orange',
							value:'orange'
						}]
						
					},
					width:{
						name:'width',
						type:'select',
						label:'Tile width',
						value:viewObj.width,
						options:[
							{
								name:'tiny',
								value:2
							}, {
								name:'small',
								value:3
							}, {
								name:'medium',
								value:4
							}, {
								name:'large',
								value:6
							}, {
								name:'x-large',
								value:8
							}, {
								name:'almost full',
								value:10
							}, {
								name:'full',
								value:12
							}	
						],
						
					},
					height:{
						name:'height',
						type:'select',
						label:'Tile height',
						value:viewObj.height,
						options:[
							{
								name:'small',
								value:3
							}, {
								name:'medium',
								value:6
							}, {
								name:'large',
								value:8
							}, {
								name:'full',
								value:12
							}	
						],
						
					}
				});
				
				ui.form.notify('change', {
					type:'bento-lab-run',
					data:{
						run: function(){
							
							var formData = ui.form.process().fields;
							
							viewObj.title = formData.title.value;
							viewObj.width = parseInt(formData.width.value);
							viewObj.height = parseInt(formData.height.value);
							viewObj.color = formData.color.value;
							
							preview.view(viewObj);
							
						}
					}
				}, sb.moduleId);
				
				ui.patch();
				
			}
			
			var ui = this;
			
			var creating = false;
			if(viewObj === undefined){
				
				viewObj = {
					title:'new view',
					width:3,
					height:6,
					data_type:'',
					type:'table',
					color:'',
					id:1
				};
				creating = true;
				
			}
			
			this.empty();
			
			this.makeNode('main', 'column', {width:12, css:'pda-vertical-center'});
			
			this.makeNode('br', 'lineBreak', {});
			
			var preview = this.makeNode('preview', 'column', {width:12});
			
			// LEFT
			var left = this.main.makeNode('left', 'column', {width:4, css:'pda-container'});
			
			var center = this.main.makeNode('center', 'column', {width:4, css:'pda-container'});
			
			var right = this.main.makeNode('right', 'column', {width:4, css:'pda-container'});
			
			this.patch();
			
			// RIGHT
			preview.view = view_subview.bind(preview);
			preview.view(viewObj);
			
			this.updateLeft = leftColumn.bind(left, viewObj);			
			this.updateLeft();
			
			this.updateCenter = optionsForm.bind(center, viewObj);
			this.updateCenter();
			
			this.updateRight = styleForm.bind(right, viewObj);
			this.updateRight();
			
		}
		
		ui.empty();
		
		ui.makeNode('title', 'headerText', {size:'small', text: view.name});
		
		ui.makeNode('br', 'lineBreak', {});
		
		ui.makeNode('main', 'column', {width:12});
		ui.makeNode('edit', 'column', {width:12});
		
		ui.makeNode('new', 'button', {css: 'pda-btnOutline-green', size:'large', text:'<i class="fa fa-plus fa-2x" aria-hidden="true"></i>'})
			.notify('click', {
				type:'bento-lab-run',
				data:{
					run:function(){
						ui.new.hide();
						ui.viewEdit();
					}
				}
			}, sb.moduleId);
		
		ui.patch();
		
		ui.viewEdit = edit_subview.bind(ui.edit);
		
	}
	
	return {
		
		destroy:function(){
			
			ui = {};
			
			_.each(comps, function(comp){
				
				comp.destroy();
				
			});
			
			comps = {};
			
			sb.listen({
				'start-bento-lab':this.start
			});
			
		},
		
		init: function(){
			
			sb.listen({
				'start-bento-lab':this.start
			});
			
		},
		
		run:function(data){
			
			data.run(data);
			
		},
		
		start: function(setup){
			
			sb.listen({
				'destroy-bento-lab':this.destroy,
				'bento-lab-run':this.run
			});
			
			ui = sb.dom.make(setup.domObj.selector);
			
			ui.draw = draw_table;
			ui.draw();
			
		},
		
	}
	
});