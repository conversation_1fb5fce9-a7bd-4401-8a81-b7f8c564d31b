Factory.register('decisions', function(sb){
	
	var components = {},
		ui = {},
		questions = {},
		staff = [],
		viewDashboard = true,
		collapse = true,
		fullscreen = false,
		viewArchive = false,
		currentPage = 'dashboard',
		headerText = '<i class="fa fa-question" aria-hidden="true"></i> Decisions',
		headerSize = 'small',
		objectId = 0,
		objectType = '',
		createdBy = 0,
		askedTo = 0,
		postBox = {};
	
	function createUI(domObj){
		
		ui = sb.dom.make(domObj.selector);
		ui.makeNode('container', 'container', {title:headerText, css:'', collapse: collapse, headerSize:headerSize});
			
		ui.container.makeNode('main', 'column', {width: 12});
			
		ui.state = uiState;					
				
		ui.build();
		ui.state(ui.container.main);
				
		components.calendar = sb.createComponent('calendar');
		//components.notes = sb.createComponent('notes2');
		
	}
	
	function getData(askedBy, createdBy, objectId, callback){
		
		if(objectId == 0){
			
			sb.data.db.obj.getWhere('questions', {ask: +sb.data.cookie.userId, childObjs:1}, function(objs){
				
				sb.data.db.obj.getWhere('questions', {created_by: +sb.data.cookie.userId, childObjs:1}, function(createdByList){
					
					sb.data.db.obj.getAll('users', function(staffList){
					
						callback(objs.concat(createdByList), staffList);
					
					}, 1);
				
				});
								
			});
			
		}else{
			
			sb.data.db.obj.getWhere('questions', {related_object:objectId, childObjs:1}, function(objs){
				
				sb.data.db.obj.getAll('users', function(staffList){
											
					callback(objs, staffList);
				
				}, 1);
								
			});
			
		}
		
	}
	
	function uiState(dom){
		
		function askQuestion(){
			
			this.empty();
			
			this.makeNode('panel', 'container', {css:'pda-background-blue pda-container pda-Panel'}).makeNode('body', 'container', {});
			
			this.panel.body.makeNode('btns', 'buttonGroup', {css:'pull-right'});
			
			this.panel.body.btns.makeNode('back', 'button', {text:'<i class="fa fa-times"></i> Close', css:'pda-btn-red'}).notify('click', {
				type:'back-to-decision-list',
				data:{}
			}, sb.moduleId);
			this.panel.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading', css:'pda-btn-primary'});
			
			this.panel.body.makeNode('title', 'headerText', {text:'Ask someone a question', size:'small'});
			
			this.panel.body.makeNode('btnBreak', 'lineBreak', {});

			this.panel.body.makeNode('loading', 'headerText', {text:'<i class="fa fa-circle-o-notch fa-spin"></i>', css:'text-center'});
			
			this.patch();
			
			var dom = this;
			
			sb.data.db.obj.getAll('users', function(users){
				
				dom.panel.body.makeNode('cont', 'container', {css:''});
				
				dom.panel.body.cont.makeNode('form', 'form', {
					ask:{
						name:'ask',
						label:'Who do you want to ask?',
						type:'select',
						options:_.map(users, function(o){ return {name: o.fname+' '+o.lname, value:o.id}; })
					},
					question:{
						name:'question',
						label:'What do you want to ask?',
						type:'textbox',
						rows:5
					},
					answer_needed_on:{
						name:'answer_needed_on',
						label:'When do you need an answer?',
						type:'date',
						dateFormat:'M/DD/YYYY',
						value:moment().format('M/DD/YYYY')
					}
				});
				
				dom.panel.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
					type:'save-new-question',
					data:{
						uiState:saveNewQuestion.bind(dom)
					}
				}, sb.moduleId);				
				
				delete dom.panel.body.loading;
				
				dom.panel.body.patch();
				
			});
			
		}
		
		function headerBar(state){
			
			switch(state){
				
				case 'hide':
				case 'new':

					delete this.new;
														
					break;
				
				case 'show':
				
					this.makeNode('new', 'button', {text:'<i class="fa fa-plus"></i> Ask A New Question', css:'pda-btn-primary'}).notify('click', {
						type:'ask-a-new-question',
						data:{}
					}, sb.moduleId);
				
					break;
				
			}
						
			this.patch();
			
		}
		
		function loading(on){
			
			if(on){
				
				this.makeNode('loading', 'loader', {size:'large'});
				
			}else{
				
				delete this.loading;
				
			}
			
			this.patch();
			
		}
		
		function saveNewQuestion(data){
			
			var form = this.panel.body.cont.form.process();
			
			if(form.completed == false){
				
				sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
				return;
				
			}

			this.panel.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving', css:'pda-btn-primary'});
			
			this.panel.body.btns.patch();

			var question = {
					ask: +form.fields.ask.value,
					question:form.fields.question.value,
					answer_needed_on:form.fields.answer_needed_on.value,
					status:'not_answered',
					related_object:objectId,
					related_object_type:objectType,
					actions: [
						{
							button_title:'<i class="fa fa-check"></i> Yes',
							button_color:'btn-success',
							callback_function:function(obj, callback){
																
								callback('Yes');
								
							}
						},
						{
							button_title:'<i class="fa fa-times"></i> No',
							button_color:'btn-danger',
							callback_function:function(obj, callback){
																
								callback('No');
								
							}
						}
					]
				};
				
			_.each(question.actions, function(act){
				
				act.callback_function = act.callback_function.toString();
				
			});

			sb.data.db.obj.create('questions', question, function(created){
				
				questions.push(created);
				
				ui.state.header('show');
				ui.state.showList(questions);
				
			}, 1);				
			
		}
		
		function showList(objs){
			
			function buildList(objs){
				
				this.empty();
							
				if(objs.length == 0){
					
					this.makeNode('text', 'headerText', {css:'text-center text-success', size:'small', text:'<i class="fa fa-check"></i> No decisions to make'});
					
				}else{
					
					var args = {};
					
					var answered = _.where(objs, {status:'answered'}),
						notAnswered = _.where(objs, {status:'not_answered'});
						
					var questionObjs = notAnswered.concat(answered);
					
					//this.makeNode('listBreak1', 'lineBreak', {spaces:2});
								
					_.each(questionObjs, function(o){
											
						if(o.ask.id == askedTo && o.status != 'answered'){
							
							args.outlineColor = 'pda-background-orange';
							
						}else{
							
							args.outlineColor = 'text-muted pda-background-gray';
							
						}
						
						this.makeNode('cont-'+o.id, 'container', {dataId:o.id, css:'pda-Panel pda-container '+ args.outlineColor});
						
						var text = o.question;
						
						this['cont-'+o.id].makeNode('col1', 'column', {width:12});
						this['cont-'+o.id].makeNode('col2', 'column', {width:12});
							
						this['cont-'+o.id].col1.makeNode('text', 'headerText', {size:'x-small', text:text});
						
						if(moment(o.answer_needed_on)){

							var metaLine = 'Asked by '+ o.created_by.fname +' '+ o.created_by.lname + '. Answer needed by '+ moment(o.answer_needed_on).format('M/DD/YYYY') +'.';
							
							if(o.status == 'answered'){
		
								this['cont-'+o.id].col1.makeNode('answer', 'headerText', {size:'x-small', css:'text-italic', text:'Answer: '+ o.answer});
								
								metaLine = 'Asked by '+ o.created_by.fname +' '+ o.created_by.lname + '. Answered by '+ o.ask.fname +' '+ o.ask.lname +'.';
								
							}
							
							this['cont-'+o.id].col1.makeNode('askedBy', 'text', {css:'text-italic', text:metaLine});
													
						}
						
						this['cont-'+o.id].col2.makeNode('btns', 'buttonGroup', {css:'pull-left'});
						
						if(o.status == 'not_answered'){
							
							this['cont-'+o.id].col2.btns.makeNode('answer', 'button', {text:'<i class="fa fa-comments-o"></i> Respond', css:'pda-btn-blue'}).notify('click', {
								type:'decisions-ui-change',
								data:{
									uiState:startAnswer.bind(this['cont-'+o.id], o)
								}
							}, sb.moduleId);						
							
						}
											
						if(o.related_object && o.related_object_type){
							
							var viewData = {
									to:'',
									pageParams:{}
								};
							
							switch(o.related_object_type){
								
								case 'contacts':
								
									viewData.to = 'contact';
									viewData.pageParams.contactId = o.related_object;
								
									break;
								
								case 'staff':
								
									viewData.to = o.related_object_type;
									viewData.pageParams.staffId = o.related_object;
								
									break;
								
							}
							
							if(objectId == 0){
							
								if(o.status == 'not_answered'){
		
									this['cont-'+o.id].col2.btns.makeNode('viewRelated', 'button', {text:'<i class="fa fa-external-link"></i> View Related', css:'pda-btnOutline-blue'}).notify('click', {
										type: 'app-change-page',
										data: viewData
									}, sb.moduleId);
		
								}else{
									
									this['cont-'+o.id].col2.btns.makeNode('viewRelated', 'button', {text:'<i class="fa fa-external-link"></i> View Related', css:'pda-btnOutline-blue'}).notify('click', {
										type: 'app-change-page',
										data: viewData
									}, sb.moduleId);
		
								}
								
							}
							
							if(o.status == 'not_answered'){
							
								this['cont-'+o.id].col2.btns.makeNode('erase', 'button', {text:'<i class="fa fa-trash-o"></i> Delete', css:'pda-btn-red'}).notify('click', {
									type: 'decisions-ui-change',
									data: {
										uiState:erase.bind(this, o)
									}
								}, sb.moduleId);
								
								this['cont-'+o.id].col2.makeNode('btnsBreak', 'lineBreak', {});
								
								//this.makeNode('break-'+o.id, 'lineBreak', {});
								
							}
								
						}
										
					}, this);
					
				}
	
				this.patch();
				
			}

			function answer(obj, actionObj){

				var answerNotes = postBox.summernote('code');
											
				eval("var funcToRun = "+ actionObj.callback_function); 

				funcToRun(obj, function(answer){
					
					if(answerNotes != ''){
						answerNotes = ' - '+ answerNotes;
					}
					
					obj.answer = answer + answerNotes;
					obj.status = 'answered';
					obj.status_name = 'Answered';
					
					sb.data.db.obj.update('questions', obj, function(updated){
						
						questions = _.reject(questions, function(o){return o.id == obj.id;});
						
						questions.push(obj);
						
						sb.dom.alerts.alert('Success!', answer, 'success');
						
						ui.state.header('show');
						ui.state.showList(questions);
						
						sb.notify({
							type:'remaining-questions-count',
							data:{
								questions:questions
							}
						});
						
					});
					
				}, this);	

			}
			
			function erase(obj){
								
				sb.dom.alerts.ask({
					title: 'Are you sure?',
					text: ''
				}, function(resp){
					
					if(resp){
					
						swal.disableButtons();
						
						var objsToDelete = [
								{
									objectType:'decisions',
									id:obj.id
								}
							];
							
						function deleteObjects(allObjects, callback, count){
												
							if(!count){
								count = 0;
							}
							
							sb.data.db.obj.erase(allObjects[count].objectType, allObjects[count].id, function(done){
								
								count++;
								
								if(count == allObjects.length){
									
									callback(count);
									
								}else{
									
									deleteObjects(allObjects, callback, count);
									
								}
								
							});
							
						}
						
						deleteObjects(objsToDelete, function(deleted){
							
							sb.dom.alerts.alert('Success!', deleted +' object(s) deleted.', 'success');
							
							questions = _.reject(questions, function(o){ return o.id == obj.id; });
							
							sb.notify({
								type:'remaining-questions-count',
								data:{
									questions:questions
								}
							});

							ui.state.header('show');
							ui.state.showList(questions);
							
						});	
					
					}
					
				});
				
			}
			
			function startAnswer(obj){
								
				this.makeNode('cont', 'container', {css:'pda-container pda-Panel pda-background-blue'});
				
				this.cont.makeNode('response', 'container', {css:''});

				this.cont.response.makeNode('btns', 'buttonGroup', {css:'pull-right'});
				
				_.each(obj.actions, function(a){
					
					this.cont.response.btns.makeNode('action-'+ a.id, 'button', {text:a.button_title, css:a.button_color}).notify('click', {
						type:'decisions-run',
						data:{
							run:answer.bind(this, obj, a)
						}
					}, sb.moduleId);
					
				}, this);
				
				this.cont.response.btns.makeNode('close', 'button', {text:'<i class="fa fa-minus"></i>', css:'pda-btn-small pda-btn-orange'}).notify('click', {
					type:'back-to-decision-list',
					data:{}
				}, sb.moduleId);
				
				this.cont.response.makeNode('header', 'headerText', {text:'Type your response below.', size:'x-small'});
				
				this.cont.response.makeNode('btnsBreak', 'lineBreak', {});
				
				this.cont.response.makeNode('responseForm', 'text', {text:'<div id="response-box" class="response-box"></di>'});
				
				this.cont.makeNode('notes', 'container', {css:''});
				
				this.patch();
				
				postBox = $('#response-box').summernote({
					toolbar:[
						['style', ['bold', 'italic', 'underline', 'clear']],
						['misc', ['undo', 'redo']]
					],
					disableDragAndDrop: true,
					dialogsFade: true,
					height:150
				});
				
				postBox.summernote('fontSize', 20);
								
				this.patch();
								
			}
			
			function notes(obj){

				this.makeNode('notes', 'container', {css:'pda-container pda-Panel pda-background-blue'});
				
				this.notes.makeNode('notes', 'container', {css:''});
				
				this.patch();
				
				sb.notify({
					type: 'show-note-list-box',
					data: {
						domObj:this.notes.notes,
						objectIds:[obj.id],
						objectId:obj.id
					}
				});
				
			}
			
			var run = buildList.bind(this, objs);
						
			run();
			
		}

		dom.makeNode('header', 'buttonGroup', {css:'pull-right'});
		dom.makeNode('break1', 'lineBreak', {});
		dom.makeNode('list', 'container', {css:'pda-container'});
		
		this.state.askQuestion = askQuestion.bind(dom.list);
		this.state.header = headerBar.bind(dom.header);
		this.state.loading = loading.bind(dom);
		this.state.showList = showList.bind(dom.list);
		
	}
				
	return {
		
		init: function(){
			
			sb.listen({
				'ask-a-new-question':this.askANewQuestion,
				'back-to-decision-list':this.backToList,
				'decisions-run':this.run,
				'decisions-ui-change':this.uiChange,
				'save-new-question':this.saveQuestion,
				'show-decision-dashboard':this.showDashboard
			});
			
/*
			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'decisions',
						title:'Decisions',
						icon:'<i class="fa fa-question"></i>',
						views:[
							{
								id:'quickaction',
								type:'quickAction',
								default:true,
								icon:'<i class="fa fa-question fa-2x"></i> <span class="badge">0</span>',
								size:'large',
								dom:function(dom, state, draw){
									
									dom.makeNode('title', 'headerText', {text:'Decisions you need to make', size:'small'});
									
									dom.makeNode('panel', 'container', {}).makeNode('body', 'container', {});
																				
									//showList.call(dom, taskArray, true, true);
									
									dom.makeNode('btns', 'buttonGroup', {});
									
									dom.btns.makeNode('view', 'button', {text:'View All Tasks', css:'pda-btn-x-small pda-btn-blue'}).notify('click', {
										type:'app-navigate-to',
										data:{
											itemId:'tasks',
											viewId:'tasks'
										}
									}, sb.moduleId);
										
									draw(dom);
																				
								}
							}
						]
					}
				}	
			});
*/
			
		},
		
		destroy: function(){
			
			_.each(components, function(comp){
				comp.destroy();
			});
			
			domObj = {},
			components = {};
			
		},
				
		askANewQuestion: function(data){
			
			ui.state.askQuestion();
			ui.state.header('hide');
			
		},
		
		backToList: function(data){
			
			ui.state.header('show');
			ui.state.showList(questions);
			
		},
		
		run: function(data){ data.run(); },
		
		showDashboard: function(data){

			if(data.hasOwnProperty('objectId')){
				objectId = data.objectId;
				objectType = data.objectType;
			}
			
			if(data.hasOwnProperty('createdBy')){
				createdBy = data.createdBy;
			}
			
			if(data.hasOwnProperty('askedTo')){
				askedTo = data.askedTo;
			}
			
			if(data.hasOwnProperty('headerText')){
				headerText = data.headerText;
			}
			
			if(data.hasOwnProperty('headerSize')){
				headerSize = data.headerSize;
			}
			
			if(data.hasOwnProperty('collapse')){
				collapse = data.collapse;
			}
			
			createUI(data.domObj);

			ui.state.loading(true);
			
			getData(askedTo, createdBy, objectId, function(objs, staffList){
				
				questions = objs;
				staff = staffList;
				
				ui.state.loading(false);
				ui.state.header('show');
				ui.state.showList(objs);
				
			});
			
		},
				
		uiChange: function(data){ data.uiState(); }
									
	}
	
});

Factory.registerComponent('decisionsOld', function(sb){
	
	var domObj = {},
		components = {},
		ui = {},
		navbar = {},
		neededCol = {},
		awaitingCol = {},
		completeCol = {},
		calendarCol = {},
		questions = {},
		staff = [],
		viewDashboard = true,
		fullscreen = false,
		viewArchive = false,
		currentPage = 'dashboard',
		objectId = 0,
		objectType = '',
		createdBy = 0,
		askedTo = 0;
	
	function buildNavigation(){
		
		this.empty();
		
		this.makeNode('btns', 'buttonGroup', {css:'pull-left'});
		
		var dButtonState = 'pda-btnOutline-blue',
			abuttonState = 'pda-btnOutline-blue';
				
		this.btns.makeNode('dashboard', 'button', {css:dButtonState +' pda-btn-x-small', text:'<i class="fa fa-question"></i> All Questions'}).notify('click', {
			type:'decisions-nav-change',
			data:{
				change:'dashboard'
			}
		}, sb.moduleId);;
		
/*
		this.btns.makeNode('calendar', 'button', {css:'pda-btnOutline-blue pda-btn-x-small', text:'<i class="fa fa-calendar"></i> Calendar'}).notify('click', {
			type:'decisions-nav-change',
			data:{
				change:'calendar'
			}
		}, sb.moduleId);;
*/
		
		this.btns.makeNode('archive', 'button', {css:abuttonState +' pda-btn-x-small', text:'<i class="fa fa-thumbs-o-up"></i> Answered Questions'}).notify('click', {
			type:'decisions-nav-change',
			data:{
				change:'archive'
			}
		}, sb.moduleId);
		
		this.makeNode('navbreak', 'lineBreak', {spaces:1});
		
		this.patch();
		
	}
	
	function changeNav(page){
		
		this.empty();
		
		this.makeNode('btns', 'buttonGroup', {css:'pull-left'});
		
		var dButtonState = 'pda-btnOutline-blue',
			cButtonState = 'pda-btnOutline-blue',
			abuttonState = 'pda-btnOutline-blue';
		
		switch(page){
			
			case 'calendar':
			
				cButtonState = 'pda-btn-blue';
			
				break;
			
			case 'dashboard':
			
				dButtonState = 'pda-btn-blue';
				
				break;
				
			case 'archive':
			
				abuttonState = 'pda-btn-blue';
			
				break;	
			
		}
		
		this.btns.makeNode('dashboard', 'button', {css:dButtonState +' pda-btn-x-small', text:'<i class="fa fa-question"></i> All Questions'}).notify('click', {
			type:'decisions-nav-change',
			data:{
				change:'dashboard'
			}
		}, sb.moduleId);
		
/*
		this.btns.makeNode('calendar', 'button', {css:cButtonState +' pda-btn-x-small', text:'<i class="fa fa-calendar"></i> Calendar'}).notify('click', {
			type:'decisions-nav-change',
			data:{
				change:'calendar'
			}
		}, sb.moduleId);
*/
		
		this.btns.makeNode('archive', 'button', {css:abuttonState +' pda-btn-x-small', text:'<i class="fa fa-thumbs-o-up"></i> Answered Questions'}).notify('click', {
			type:'decisions-nav-change',
			data:{
				change:'archive'
			}
		}, sb.moduleId);
								
		this.makeNode('navbreak', 'lineBreak', {spaces:1});
		
		this.patch();

		switch(currentPage){
			
			case 'archive':
			
				if(currentPage != page){
					
					completeCol.hide();
					
				}
			
				break;
				
			case 'calendar':
				
				if(currentPage != page){
					
					calendarCol.hide();
					
				}
			
				break;	
			
			case 'dashboard':
								
				if(currentPage != page){
					
					neededCol.hide();
					awaitingCol.hide();
					
				}
				
				break; 
			
		}	
		
		switch(page){
			
			case 'archive':
			
				completeCol.show();
				completeCol.showPanel();
				
				completeCol.showList(
					_.where(questions, {status:'answered'}),
					{
						noText:'',
						outlineColor:'pda-panel-primary',
						strikethrough:true
					}
				);
			
				break;
				
			case 'calendar':
				
				calendarCol.show();
				calendarCol.showPanel();
			
				break;	
			
			case 'dashboard':
								
				neededCol.show();
				neededCol.showPanel();
				
				awaitingCol.show();
				awaitingCol.showPanel();
				
				neededCol.showList(
					_.where(questions, {status:'not_answered', ask: {id: +sb.data.cookie.userId}}),
					{
						noText:'<i class="fa fa-check-circle-o"></i> You\'re all caught up!',
						outlineColor:'pda-panel-red'
					}
				);

				awaitingCol.showList(
					_.reject(
					_.where(questions, {status:'not_answered'}),
					function(o){
						return o.ask.id == +sb.data.cookie.userId;
					}),
					{
						noText:'',
						outlineColor:'pda-panel-blue'
					}
				);
				
				break;	
			
		}
		
		currentPage = page;
		
	}
	
	function buildNeededCol(){
		
		this.empty();
		
		this.makeNode('panel', 'panel', {header:'<i class="fa fa-exclamation-triangle"></i> Needs Your Attention', css:'pda-panel-red'});
		
		this.panel.body.makeNode('fullscreen', 'button', {css:'pda-btn-x-small', text:'<i class="fa fa-expand"></i>'}).notify('click', {
			type:'view-questions-fullscreen',
			data:{
				fullscreen:fullscreen
			}
		}, sb.moduleId);
		
		this.panel.body.makeNode('list', 'container', {});
		
		//this.makeNode('colBreak', 'lineBreak', {spaces:1});
				
		this.patch();
		
	}
	
	function buildAwaitingCol(){
		
		this.empty();
		
		this.makeNode('panel', 'panel', {header:'<i class="fa fa-clock-o"></i> Related Questions', css:'pda-panel-blue'});
		
		this.panel.body.makeNode('btns', 'buttonGroup', {});
		
		this.panel.body.btns.makeNode('new', 'button', {css:'pda-btn-x-small pda-btnOutline-blue', text:'<i class="fa fa-plus"></i> Ask a question'}).notify('click', {
			type:'pose-a-question',
			data:{}
		}, sb.moduleId);
		
		this.panel.body.makeNode('list', 'container', {});
				
		this.patch();
		
	}
	
	function buildCompleteCol(){
		
		this.empty();
		
		this.makeNode('panel', 'panel', {header:'<i class="fa fa-history"></i> History', css:'pda-panel-primary'});
		
		this.panel.body.makeNode('list', 'container', {});
						
		this.patch();
		
	}
	
	function buildCalendarCol(){

		this.empty();
		
		this.makeNode('panel', 'panel', {header:'Decisions Calendar'});
		
		this.patch();
		
		var i = 0;
		var calQuestions = _.map(questions, function(q){
			
			i++;
			
			var desc = '',
				buttons = {},
				backgroundColor = 'pda-background-red';
				
			if(q.status == 'answered'){
				backgroundColor = 'pda-background-blue';
			}	
			
			desc = 'Asked by '+ q.created_by.fname +' '+ q.created_by.lname;
			
			var w = 0;
			_.each(q.actions, function(choice){
				
				buttons['btn-'+w] = {
					text:choice.button_title,
					css:choice.button_color,
					type:'action-button-clicked',
					data:{
						action:choice,
						question:q
					}
				};
								
				w++;
				
			}, this);
						
			return {
					id: i,
					name: q.question,
					startTime: moment(q.due_date).subtract(i, 'days'),
					endTime: moment(q.due_date).subtract(i, 'days'),
					duration: 1,
					description: desc,
					type: q.status_name,
					color: backgroundColor,
					buttons: buttons,
					group: q.status_name
				};
			
		}, i);
		
		components.calendar.notify({
			type: 'display-calendar',
			data: {
				domObj: this.panel.body,
				events: calQuestions,
				viewDate: moment(),
				viewType: 'month',  // could be changed to any of the other view types
				cellBtns: {
					add: {
						text: '<i class="fa fa-plus" aria-hidden="true"></i>',
						type: 'cell-btn-notify',
						css: '',
						data: {} // pass moment obj of a given day
					},
					erase: {
						text: '<i class="fa fa-minus" aria-hidden="true"></i>',
						type: 'cell-btn-test',
						css: '',
						data: {}
					}
				},
				calMenu: {
					list: {
						text: 'List',
						type: 'list',
						show: true
					},
					day: {
						text: 'Day',
						type: 'day',
						show: true
					},
					week: {
						text: 'Week',
						type: 'week',
						show: true
					},
					month: {
						text: 'Month',
						type: 'month',
						show: true
					}
				}
				
			}
			
		});
		
	}
	
	
	
		
	function buildListItems(objs, args){
		
		delete this.panel.body.loading;
		
		this.panel.body.list.empty();
		
		if(objs.length == 0){
			
			this.panel.body.list.makeNode('text', 'headerText', {css:'text-center', size:'x-small', text:args.noText});
			
		}else{
						
			_.each(objs, function(o){

				this.panel.body.list.makeNode('cont-'+o.id, 'container', {dataId:o.id, css:'pda-Panel pda-container '+ args.outlineColor});
				
				if(o.status == 'answered'){
										
					var text = '<span class="text-bold" style="text-decoration:line-through;">'+ o.question +'</span>';
					
					this.panel.body.list['cont-'+o.id].makeNode('text', 'headerText', {size:'xx-small', text:text, css:'text-muted'});
					
					this.panel.body.list['cont-'+o.id].makeNode('answer', 'label', {color:'primary', text:'Answered: '+ o.answer});

					this.panel.body.list['cont-'+o.id].makeNode('askedBy', 'text', {css:'text-right text-italic text-muted', text:'Asked by '+ o.created_by.fname +' '+ o.created_by.lname});
					
				}else{
					
					var text = o.question;
					
					this.panel.body.list['cont-'+o.id].makeNode('text', 'headerText', {size:'xx-small', text:text});
					
					if(moment(o.answer_needed_on)){

						this.panel.body.list['cont-'+o.id].makeNode('askedBy', 'text', {css:'text-right text-italic text-muted', text:'Asked by '+ o.created_by.fname +' '+ o.created_by.lname});
						
						this.panel.body.list['cont-'+o.id].makeNode('due', 'text', {css:'text-right text-italic text-muted', text:'Answer needed by '+ moment(o.answer_needed_on).format('M/DD/YYYY')});
						
					}
					
				}
								
				this.panel.body.list['cont-'+o.id].notify('click', {
					type:'view-decision',
					data:{
						object:o
					}
				}, sb.moduleId);
				
			}, this);
			
		}
		
		this.patch();
		
	}
	
	function buildListItemsFS(objs, args){
		
		delete this.panel.body.loading;
		
		this.panel.body.list.empty();
		
		if(objs.length == 0){
			
			this.panel.body.list.makeNode('text', 'headerText', {css:'text-center', size:'x-small', text:args.noText});
			
		}else{
						
			_.each(objs, function(o){

				this.panel.body.list.makeNode('cont-'+o.id, 'container', {dataId:o.id, css:'pda-Panel pda-container '+ args.outlineColor});
				
				if(o.status == 'answered'){
										
					var text = '<span class="text-bold" style="text-decoration:line-through;">'+ o.question +'</span>';
					
					this.panel.body.list['cont-'+o.id].makeNode('text', 'headerText', {size:'small', text:text});
					
					this.panel.body.list['cont-'+o.id].makeNode('answer', 'label', {color:'primary', text:'Answered: '+ o.answer});

					this.panel.body.list['cont-'+o.id].makeNode('askedBy', 'text', {css:'text-right text-italic text-muted', text:'Asked by '+ o.created_by.fname +' '+ o.created_by.lname});

				}else{
					
					var text = o.question;
					
					this.panel.body.list['cont-'+o.id].makeNode('text', 'headerText', {size:'small', text:text});
					
					if(moment(o.answer_needed_on)){

						this.panel.body.list['cont-'+o.id].makeNode('askedBy', 'text', {css:'text-right text-italic text-muted', text:'Asked by '+ o.created_by.fname +' '+ o.created_by.lname});

						this.panel.body.list['cont-'+o.id].makeNode('due', 'text', {css:'text-right text-italic text-muted', text:'Answer needed by '+ moment(o.answer_needed_on).format('M/DD/YYYY')});
						
					}
					
					this.panel.body.list['cont-'+o.id].makeNode('btns', 'buttonGroup', {});
			
					if(o.created_by.id == +sb.data.cookie.userId){
						
						this.panel.body.list['cont-'+o.id].btns.makeNode('delete', 'button', {css:'pda-btn-large btn-danger', text:'<i class="fa fa-times"></i> Delete'}).notify('click', {
							type:'delete-question-object',
							data:{
								object:o
							}
						}, sb.moduleId);
						
					}
					
					if(o.status == 'not_answered' && o.ask.id == +sb.data.cookie.userId){
					//if(1==1){	
						
						this.panel.body.list['cont-'+o.id].btns.makeNode('delete', 'button', {css:'pda-btn-large btn-danger', text:'<i class="fa fa-times"></i> Delete'}).notify('click', {
							type:'delete-question-object',
							data:{
								object:o
							}
						}, sb.moduleId);
						
						var i = 0;
						_.each(o.actions, function(choice){
							
							this.panel.body.list['cont-'+o.id].btns.makeNode('choice-'+i, 'button', {css:'pda-btn-large '+choice.button_color, text:choice.button_title}).notify('click', {
								type:'action-button-clicked',
								data:{
									action:choice,
									question:o
								}
							}, sb.moduleId);
							
							i++;
							
						}, this);
						
					}
					
				}
				
				this.panel.body.list.makeNode('break-'+o.id, 'lineBreak', {spaces:1});
				
			}, this);
			
		}
		
		this.patch();
		
	}
	
	
	
	
	function buildLoading(){
		
		this.panel.body.makeNode('loading', 'text', {text:sb.dom.loadingGIF});
		
		this.patch();
		
	}
	
	function createQuestionModal(){
		
		this.modals.create.body.makeNode('break', 'lineBreak', {spaces:1});
		
		this.modals.create.body.makeNode('form', 'form', {
			section:{
				type: 'section',
				name: 'section1',
				label: 'Your Question',
				fields:{
					question:{
						type:'textbox',
						name:'question',
						label:'',
						placeholder:'Type your question here...',
						rows:5
					}
				}
			},
			ask:{
				type:'select',
				label:'Who do you want to ask?',
				name:'ask',
				options:createStaffOptions()
			},
			date_answer_needed:{
				type:'date',
				label:'When do you need an answer?',
				name:'date_answer_needed',
				dateFormat:'M/DD/YYYY'
			}
		});
				
		this.modals.create.footer.makeNode('save', 'button', {css:'btn-success', text:'<i class="fa fa-floppy-o"></i> Save'}).notify('click', {
			type:'create-question-from-form',
			data:{
				form:this.modals.create.body.form,
				modal:this.modals.create
			}
		}, sb.moduleId);
				
		this.modals.create.patch();
		
		this.modals.create.show();
		
	}
	
	function createStaffOptions(){
		
		var ret = _.map(staff, function(v, k){
			
			return {
				name:v.fname +' '+ v.lname,
				value:v.id
			};
			
		});
				
		return ret;
		
	}
	
	function goFullscreen(data){

		if(data.fullscreen == true){
			
			awaitingCol.show();
		
			neededCol.width(6);
			
			this.panel.body.makeNode('fullscreen', 'button', {css:'pda-btn-x-small', text:'<i class="fa fa-expand"></i>'}).notify('click', {
				type:'view-questions-fullscreen',
				data:{
					fullscreen:false
				}
			}, sb.moduleId);
			
			neededCol.showList(
				_.where(questions, {status:'not_answered', ask: {id: +sb.data.cookie.userId}}),
				{
					noText:'<i class="fa fa-check-circle-o"></i> You\'re all caught up!',
					outlineColor:'pda-panel-red'
				}
			);
						
		}else{
			
			awaitingCol.hide();
			
			neededCol.width(12);
			
			this.panel.body.makeNode('fullscreen', 'button', {css:'pda-btn-x-small', text:'<i class="fa fa-minus"></i>'}).notify('click', {
				type:'view-questions-fullscreen',
				data:{
					fullscreen:true
				}
			}, sb.moduleId);
			
			neededCol.showListFS(
				_.where(questions, {status:'not_answered', ask: {id: +sb.data.cookie.userId}}),
				{
					noText:'<i class="fa fa-check-circle-o"></i> You\'re all caught up!',
					outlineColor:'pda-panel-red'
				}
			);
						
		}
		
		this.panel.body.patch();
		
	}
	
	function viewSingleDecision(obj){

		this.modals.view.body.empty();
			
		if(obj.status == 'answered'){
			
			this.modals.view.body.makeNode('answer', 'headerText', {size:'x-small', css:'text-center alert alert-info', text:'Answer: '+ obj.answer});
			
			this.modals.view.body.makeNode('text', 'headerText', {size:'small', text:obj.question});
			
		}else{
			
			this.modals.view.body.makeNode('text', 'headerText', {size:'small', text:obj.question});
			
			this.modals.view.body.makeNode('btns', 'buttonGroup', {});
			
			this.modals.view.body.makeNode('answerBreak', 'lineBreak', {spaces:1});
			
			this.modals.view.body.makeNode('answerDom', 'container', {css:'pda-container pda-background-blue'});
			
			if(obj.created_by.id == +sb.data.cookie.userId){
				
				this.modals.view.body.btns.makeNode('delete', 'button', {css:'btn-danger', text:'<i class="fa fa-times"></i> Delete'}).notify('click', {
					type:'delete-question-object',
					data:{
						object:obj,
						modal:this.modals.view
					}
				}, sb.moduleId);
				
			}
			
			if(obj.status == 'not_answered' && obj.ask.id == +sb.data.cookie.userId){
			//if(1==1){	
				
				this.modals.view.body.btns.makeNode('delete', 'button', {css:'btn-danger', text:'<i class="fa fa-times"></i> Delete'}).notify('click', {
					type:'delete-question-object',
					data:{
						object:obj,
						modal:this.modals.view
					}
				}, sb.moduleId);
				
				var i = 0;
				_.each(obj.actions, function(choice){
					
					this.modals.view.body.btns.makeNode('choice-'+i, 'button', {css:choice.button_color, text:choice.button_title}).notify('click', {
						type:'action-button-clicked',
						data:{
							action:choice,
							question:obj,
							domObj:this.modals.view.body.answerDom
						}
					}, sb.moduleId);
					
					i++;
					
				}, this);
				
			}
			
		}
		
		this.modals.view.body.makeNode('commentsBreak', 'lineBreak', {spaces:1});
		
		this.modals.view.body.makeNode('comments', 'container', {});
		
		this.modals.view.patch();
		
		this.modals.view.show();
		
		components.comments = sb.createComponent('notes2');
		components.comments.notify({
			type: 'show-note-list-box',
			data: {
				domObj:this.modals.view.body.comments,
				objectIds:[obj.id],
				objectId:obj.id
			}
		});
		
	}
	
	
	
	function getData(askedBy, createdBy, objectId, callback){
		
		if(objectId == 0){
			
			sb.data.db.obj.getWhere('questions', {ask: +sb.data.cookie.userId, childObjs:1}, function(objs){
				
				sb.data.db.obj.getWhere('questions', {created_by: +sb.data.cookie.userId, childObjs:1}, function(createdByList){
					
					sb.data.db.obj.getAll('users', function(staffList){
					
						callback(objs.concat(createdByList), staffList);
					
					}, 1);
				
				});
								
			});
			
		}else{
			
			sb.data.db.obj.getWhere('questions', {related_object:objectId, childObjs:1}, function(objs){
				
				sb.data.db.obj.getAll('users', function(staffList){
											
					callback(objs, staffList);
				
				}, 1);
								
			});
			
		}
		
	}
	
	
				
	return {
		
		init: function(){
			
			sb.listen({
				'action-button-clicked':this.actionButtonClicked,
				'create-question-from-form':this.createFromForm,
				'decisions-nav-change':this.navChange,
				'delete-question-object':this.deleteQuestion,
				'pose-a-question':this.createQuestion,
				'show-decision-dashboard':this.showDashboard,
				'update-question-columns':this.updateLists,
				'view-decision':this.viewSingle,
				'view-questions-fullscreen':this.viewFullscreen
			});
			
		},
		
		destroy: function(){
			
			_.each(components, function(comp){
				comp.destroy();
			});
			
			domObj = {},
			components = {};
			
		},
		
		actionButtonClicked: function(data){

			eval("var funcToRun = "+ data.action.callback_function); 

			funcToRun(data.question, data.domObj, function(answer){
				
				data.question.answer = answer;
				data.question.status = 'answered';
				data.question.status_name = 'Answered';
				
				sb.data.db.obj.update('questions', data.question, function(updated){
					
					questions = _.reject(questions, function(o){return o.id == data.question.id;});
					
					questions.push(data.question);
					
					sb.dom.alerts.alert('Success!', answer, 'success');
					
					sb.notify({
						type:'update-question-columns',
						data:{}
					});
					
				});
				
			});
								
		},
		
		createFromForm: function(data){
						
			data.modal.footer.makeNode('save', 'button', {css:'btn-success', text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving'});
			
			data.modal.footer.patch();
			
			var formData = data.form.process();			

			var newQuestion = {
					question:formData.fields.question.value,
					date_answer_needed:moment(formData.fields.date_answer_needed.value).format(),
					ask: formData.fields.ask.value,
					status:'not_answered',
					related_object:objectId,
					related_object_type:objectType,
					actions: [
						{
							button_title:'<i class="fa fa-check"></i> Yes',
							button_color:'btn-success',
							callback_function:function(obj, callback){
																
								sb.data.db.obj.update(obj.related_object_type, {id:obj.related_object, lname:'Doe Answered'}, function(done){

									callback('Yes');
									
								});
								
							}
						},
						{
							button_title:'<i class="fa fa-times"></i> No',
							button_color:'btn-danger',
							callback_function:function(obj, callback){
																
								callback('No');
								
							}
						}
					]
				};
				
			_.each(newQuestion.actions, function(act){
				
				act.callback_function = act.callback_function.toString();
				
			});

			sb.data.db.obj.create('questions', newQuestion, function(created){
				
				sb.data.db.obj.getById('questions', created.id, function(newObj){	
					
					questions.push(newObj);
					
					data.modal.hide();
					
					sb.notify({
						type:'update-question-columns',
						data:{}
					});
				
				}, 1);
				
			});	
			
		},

		createQuestion: function(data){
			
			ui.createNewQuestion();
			
		},
		
		deleteQuestion: function(data){
			
			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: ''
			}, function(resp){
				
				swal.disableButtons();
				
				if(resp){
					
					sb.data.db.obj.erase('questions', data.object.id, function(done){
						
						questions = _.reject(questions, function(o){return +o.id == +data.object.id;});
						
						if(data.hasOwnProperty('modal')){
							
							data.modal.hide();
							
						}
						
						sb.dom.alerts.alert('Deleted!', '', 'success');
						
						sb.notify({
							type:'update-question-columns',
							data:{}
						});
						
					});
					
				}
				
			});
			
		},
		
		navChange: function(data){
			
			navbar.updateNav(data.change);
			
		},
		
		showDashboard: function(data){

			if(data.hasOwnProperty('objectId')){
				objectId = data.objectId;
				objectType = data.objectType;
			}
			
			if(data.hasOwnProperty('createdBy')){
				createdBy = data.createdBy;
			}
			
			if(data.hasOwnProperty('askedTo')){
				askedTo = data.askedTo;
			}
						
			ui = sb.dom.make(data.domObj.selector);
			ui.makeNode('modals', 'container', {})
			ui.modals.makeNode('view', 'modal', {});
			ui.modals.makeNode('create', 'modal', {});
			
			ui.viewSingle = viewSingleDecision;
			ui.createNewQuestion = createQuestionModal;
			
			navbar = ui.makeNode('navigation', 'column', {width:12});
			navbar.showNav = buildNavigation;
			navbar.updateNav = changeNav;
			
			neededCol = ui.makeNode('neededCol', 'column', {width:6});
			neededCol.showPanel = buildNeededCol;
			neededCol.showLoading = buildLoading;
			neededCol.showList = buildListItems;
			neededCol.fullscreen = goFullscreen;
			neededCol.showListFS = buildListItemsFS;
			
			awaitingCol = ui.makeNode('awaitingCol', 'column', {width:6});
			awaitingCol.showPanel = buildAwaitingCol;
			awaitingCol.showLoading = buildLoading;
			awaitingCol.showList = buildListItems;
			
			completeCol = ui.makeNode('completeCol', 'column', {width:12});
			completeCol.showPanel = buildCompleteCol;
			completeCol.showLoading = buildLoading;
			completeCol.showList = buildListItems;
			
			calendarCol = ui.makeNode('calendarCol', 'column', {width:12});
			calendarCol.showPanel = buildCalendarCol;
			
			
			neededCol.showPanel();
			awaitingCol.showPanel();
			
			ui.build();
			
			navbar.showNav();
			
			neededCol.showLoading();
			awaitingCol.showLoading();
			
			components.calendar = sb.createComponent('calendar');
			
			getData(askedTo, createdBy, objectId, function(objs, staffList){
				
				questions = objs;
				staff = staffList;
				
				navbar.updateNav('dashboard');
				
			});
			
		},
		
		updateLists: function(data){
			
			getData(askedTo, createdBy, objectId, function(objs, staff){
				
				questions = objs;
				staff = staffList;
				
				navbar.updateNav('dashboard');
				
			});
			
		},
				
		viewFullscreen: function(data){

			neededCol.fullscreen(data);
			
		},
		
		viewSingle: function(data){

			ui.viewSingle( data.object );
			
		},
									
	}
	
});