Factory.register('styleGuide', function(sb) {
	
	var comps = {},
	
		// UI Objs
		ui = {
			sys_modal: {},
			sys_cont: {}
		},
		listeners = {};
					
	function singleView(dom, state, draw){

		dom.empty();
		
		dom.makeNode('header', 'div', {text:'Bento Style Guide', css:'ui huge header center aligned'});

		dom.makeNode('buttonTitle', 'div', {text:'Buttons', css:'ui large header'});		
		dom.makeNode('buttonInfo', 'div', {css:'ui message'});
		dom.buttonInfo.makeNode('message', 'div', {tag:'p', text:'Buttons are a simple stadarization point for the application. Using the same, standard button designs brings a more cohesive visually experiance for the user. Icons should not be used in buttons anymore either. They increase loading time and cause an \'icon heavy\' look for the user.'});
		
		dom.makeNode('buttons', 'div', {text:'Standard Button Group', css:'ui medium header'});		
		dom.makeNode('btns', 'div', {css:'ui buttons'});
		dom.btns.makeNode('save', 'div', {css:'ui green button', text:'Save'});
		dom.btns.makeNode('cancel', 'div', {css:'ui orange button', text:'Cancel'});
		dom.btns.makeNode('delete', 'div', {css:'ui red button', text:'Delete'});
		
		dom.makeNode('btnBreak', 'div', {text:'<br />'});

		dom.makeNode('viewButton', 'div', {text:'Standard View Button', css:'ui medium header'});
		dom.makeNode('view', 'div', {css:'ui blue button', text:'View'});
		
		
		
		draw(dom);
		
	}
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'styleGuide',
						title: 'Style Guide',
						icon: '<i class="fa fa-css3"></i>',
						views: [
							{
								id: 'custom-home',
								default: true,
								type: 'custom',
								title: 'Style Guide',
								icon: '<i class="fa fa-css3"></i>',
								dom: singleView
							}
						]
					}
				}
			});
			
			listeners = {
				'styleGuide-run': this.run
			};
			
			sb.listen(listeners);
			
			//comps.table = sb.createComponent('crud-table');
			
		},
		
		load: function(setup) {
			
			
		},
		
		destroy: function() {
			
			_.each(comps, function(v) {
				v.destroy();
			});
			
			comps = {};
			
		},
		
		// R
		run: function(data) {
			data.run(data);
		}
		
	}
	
});