Factory.register('listsTable', function(sb){
	
	function loadData(setup, callback){
		
		var where = setup.where;
		var selection = setup.dataSelection;
		var objectType = setup.objectType;
		
		// convert 'getAll' queries to 'getWhere' queries
		if(where == 'all'){
			where = {
				date_created:{
					type:'before',
					date:moment().add(1, 'day').unix()
				}
			}
		}
		
		// add selection object
		where.childObjs = selection;
		
		// add paging object
		where.paged = {
			count:true,
			page:0,
			pageLength:15,
			paged:true,
			sortCol:'date_created',
			sortDir:'desc',
			sortCast:'string'
		};
		
		// get blueprint
		sb.data.db.obj.getBlueprint(
			objectType,
			function(blueprint){
				
				// get data
				sb.data.db.obj.getWhere(
					objectType,
					where,
					function(data){
						
						sb.data.db.obj.getAll('system_tags', function(tags){
							
							// build return
							var ret = {
									blueprint:blueprint,
									data:data,
									tags:tags
								};
							
							callback(ret);
							
						});
						
					}
				);
				
			}
		);
		
	}
	
	function ui_buildTagFilterButton(dom, tags, mainDom){
				
		dom.makeNode('title', 'div', {css:'ui small header', text:'Filter by tags'});
		dom.makeNode('tags', 'div', {css:'ui action fluid input'});		
		dom.tags.makeNode('tags', 'div', {css:'ui fluid search dropdown', tag:'select', multiple:'multiple'});		
		dom.tags.makeNode('button', 'div', {css:'ui blue button', tag:'button', text:'Filter'})
			.notify('click', {
					type:'objects-run',
					data:{
						run:function(dom, mainDom){
							
							mainDom.top.css('ui middle attached secondary loading segment');
							mainDom.main.css('ui bottom attached loading segment');
							
							setTimeout(function(){
								
								mainDom.top.css('ui middle attached secondary segment');
								mainDom.main.css('ui bottom attached segment');
								
							}, 2000);
														
						}.bind({}, dom, mainDom)
					}
				}, sb.moduleId);		
		
		_.each(tags, function(tag){
			
			dom.tags.tags.makeNode('option-'+tag.id, 'div', {tag:'option', text:tag.tag, value:tag.id});			
			
		});
		
	}

	function ui_buildSearch(dom, data, mainDom){
				
		dom.makeNode('title', 'div', {css:'ui small header', text:'Search'});
		dom.makeNode('tags', 'div', {css:'ui left labeled action fluid input'});		
		
		dom.tags.makeNode('types', 'div', {css:'ui compact selection dropdown', tag:'select'});
		dom.tags.makeNode('search', 'div', {tag:'input', css:'blue', placeholder:'Search...'});		
		
		_.each(_.where(data.blueprint, {immutable:false}), function(field, key){
			
			dom.tags.types.makeNode('option-'+key, 'div', {tag:'option', text:field.name, value:key});			
			
		});		
		
		dom.tags.makeNode('button', 'div', {css:'ui button', text:'Search'})
			.notify('click', {
					type:'objects-run',
					data:{
						run:function(dom, mainDom){
							
							mainDom.top.css('ui middle attached secondary loading segment');
							mainDom.main.css('ui bottom attached loading segment');
							
							setTimeout(function(){
								
								mainDom.top.css('ui middle attached secondary segment');
								mainDom.main.css('ui bottom attached segment');
								
							}, 2000);
														
						}.bind({}, dom, mainDom)
					}
				}, sb.moduleId);		
				
	}

	function ui_buildTopButtons(dom, setup, data, mainDom){
		
		dom.makeNode('grid', 'div', {css:'ui grid'});
		dom.grid.makeNode('left', 'div', {css:'eight wide column'});
		dom.grid.makeNode('right', 'div', {css:'eight wide column'});
				
		dom.grid.left.makeNode('paging', 'div', {css:'ui mini pagination menu'});
		dom.grid.right.makeNode('views', 'div', {css:'ui right floated blue buttons'});
		
		var totalPages = data.data.recordsTotal / data.data.data.length;
		var currentPage = 1;
		var active = '';
console.log('totalPages',totalPages);

		// paging buttons
		while(currentPage <= (totalPages+1)){
			
			if(currentPage == 1){
				active = 'active ';
			}else{
				active = '';
			}
			
			dom.grid.left.paging.makeNode('page-'+currentPage, 'div', {css:active+'item', text:currentPage, tag:'a'})
				.notify('click', {
					type:'objects-run',
					data:{
						run:function(dom, page, mainDom){
							
							mainDom.main.css('ui bottom attached loading segment');
							dom.grid.left.paging['page-'+page].text('<div class="ui inline active mini loader"></div>');
							
							setTimeout(function(){
								
								mainDom.main.css('ui bottom attached segment');
								dom.grid.left.paging['page-'+page].text(page);
								
							}, 2000);
							
						}.bind({}, dom, currentPage, mainDom)
					}
				}, sb.moduleId);
			
			currentPage++;
			
		}
		
		// view buttons
		dom.grid.right.views.makeNode('table', 'div', {css:'ui button', text:'Table', tag:'button'})
			.notify('click', {
				type:'objects-run',
				data:{
					run:function(dom, mainDom, data, setup){
												
						ui_buildTable(mainDom.main, setup, data, function(done){});
												
					}.bind({}, dom, mainDom, data, setup)
				}
			}, sb.moduleId);
		
		dom.grid.right.views.makeNode('calendar', 'div', {css:'ui button', text:'Calendar', tag:'button'})
			.notify('click', {
				type:'objects-run',
				data:{
					run:function(dom, page, mainDom){
						
						mainDom.main.css('ui attached loading segment');
						mainDom.main.empty();
						
						setTimeout(function(){
								
							mainDom.main.css('ui attached segment');
							mainDom.main.patch();
							
						}, 2000);
												
					}.bind({}, dom, currentPage, mainDom)
				}
			}, sb.moduleId);
			
		dom.grid.right.views.makeNode('kanban', 'div', {css:'ui button', text:'KanBan', tag:'button'})
			.notify('click', {
				type:'objects-run',
				data:{
					run:function(dom, page, mainDom){
						
						mainDom.main.css('ui attached loading segment');
						
						setTimeout(function(){
								
							mainDom.main.css('ui attached segment');
							
						}, 2000);
												
					}.bind({}, dom, currentPage, mainDom)
				}
			}, sb.moduleId);
			
		dom.grid.right.views.makeNode('cards', 'div', {css:'ui button', text:'Cards', tag:'button'})
			.notify('click', {
				type:'objects-run',
				data:{
					run:function(dom, mainDom, data, setup){
												
						ui_buildCards(mainDom.main, setup, data, function(done){});
												
					}.bind({}, dom, mainDom, data, setup)
				}
			}, sb.moduleId);	
						
	}
		
	function ui_start(dom, setup){
		
		dom.makeNode('main', 'div', {css:'ui basic loading segment'});
		
		dom.main.makeNode('filter', 'div', {css:'ui top attached secondary segment'});

		dom.main.makeNode('search', 'div', {css:'ui middle attached secondary segment'});

		dom.main.makeNode('top', 'div', {css:'ui middle attached secondary segment'});

		dom.main.makeNode('main', 'div', {css:'ui bottom attached segment'})
			.makeNode('test', 'div', {text:'main'});
		
		//dom.main.makeNode('bottom', 'div', {css:'ui bottom attached secondary segment'});	
		
		dom.patch();
		
		loadData(setup, function(data){
						
console.log('data',data);
			
			ui_buildTagFilterButton(dom.main.filter, data.tags, dom.main);
			
			ui_buildTopButtons(dom.main.top, setup, data, dom.main);

			ui_buildSearch(dom.main.search, data, dom.main);
			
			//ui_buildTopButtons(dom.main.bottom, data, dom.main);
						
			dom.main.patch();
			
			dom.main.css('ui basic segment');
			
			$('.ui.dropdown').dropdown();
			
			ui_buildTable(dom.main.main, setup, data, function(done){
				
				
				
			});

/*
			ui_buildTable(dom.main.listCont, setup, data, function(done){
												
				ui_fillTable(dom.main.listCont, setup, data, function(done){
					
					dom.main.listCont.patch();
					
					dom.main.css('ui basic segment');
					
					$('.ui.dropdown').dropdown();
					
				});
				
			});	
*/
			
		});
					
	}
	
	function ui_buildCards(dom, setup, data, callback){
		
		dom.empty();
		
		if(data.data.data.length == 0){
			
			dom.makeNode('noItems', 'div', {css:'ui center aligned header', text:'No items yet'});
			
		}else{
						
			dom.makeNode('cards', 'div', {css:'ui four doubling cards'});
			
			_.each(data.data.data, function(datum){
				
				dom.cards.makeNode('card-'+datum.id, 'div', {css:'ui card'});
												
			});
			
		}
		
		dom.patch();
		
		if(data.data.data.length > 0){
			
			_.each(data.data.data, function(datum){
			
				setup.cardLayout(dom.cards['card-'+datum.id], datum);
			
			});
			
		}
					
		callback(true);
		
	}
	
	function ui_buildTable(dom, setup, data, callback){
		
		dom.empty();
		
		if(data.data.data.length == 0){
			
			dom.makeNode('noItems', 'div', {css:'ui center aligned header', text:'No items yet'});
			
		}else{
						
			dom.makeNode('table', 'div', {css:'ui striped single line selectable compact table', tag:'table'});
			dom.table.makeNode('header', 'div', {tag:'thead'})
				.makeNode('row', 'div', {tag:'tr'});
				
			dom.table.header.row.makeNode('rowBtns', 'div', {tag:'th', text:''});
							
			_.each(setup.columns, function(colName, colKey){
				
				dom.table.header.row.makeNode('row-'+colKey, 'div', {tag:'th', text:colName});
				
			});
			
			dom.table.makeNode('body', 'div', {tag:'tbody'});
			
			_.each(data.data.data, function(datum){
				
				dom.table.body.makeNode('row-'+datum.id, 'div', {tag:'tr'});
				
				dom.table.body['row-'+datum.id].makeNode('cell-btns', 'div', {tag:'td', css:'two wide'});
				
				_.each(setup.cells, function(cellObj, cellKey){
					
					dom.table.body['row-'+datum.id].makeNode('cell-'+cellKey, 'div', {tag:'td', css:''});
					
					cellObj.dom(dom.table.body['row-'+datum.id]['cell-'+cellKey], datum);
					
				});
								
			});
			
		}
		
		dom.patch();
					
		callback(true);
		
	}
	
	function ui_fillTable(dom, setup, data, callback){
		
		_.each(data.data.data, function(datum){
			
			dom.table.body['row-'+datum.id]['cell-btns'].makeNode('btns', 'div', {css:'ui mini compact buttons'});
			dom.table.body['row-'+datum.id]['cell-btns'].btns.makeNode('view', 'div', {css:'ui icon green button', text:'<i class="eye icon"></i>'});
			dom.table.body['row-'+datum.id]['cell-btns'].btns.makeNode('edit', 'div', {css:'ui icon orange button', text:'<i class="edit icon"></i>'});
			dom.table.body['row-'+datum.id]['cell-btns'].btns.makeNode('delete', 'div', {css:'ui icon red button', text:'<i class="trash icon"></i>'});
					
			_.each(setup.cells, function(cellObj, cellKey){
				
				cellObj.dom(
					dom.table.body['row-'+datum.id]['cell-'+cellKey],
					datum
				);
								
			});
							
		});
		
		callback(true);
		
	}
						
	return {
		
		init: function(){
									
			sb.listen({
				'objects-run':this.run,
				'show-objects-table':this.start
			});
			
		},
		
		destroy: function(){
			
			_.each(components, function(comp){
				comp.destroy();
			});
			
			ui = {};
		},
				
		run: function(data){

			data.run(data);
			
		},
		
		start: function(data){
			
			var dom = sb.dom.make(data.domObj.selector);
			dom.makeNode('cont', 'div', {});
			dom.build();
			
			ui_start(dom.cont, data.setup);
			
		}
											
	}
	
});