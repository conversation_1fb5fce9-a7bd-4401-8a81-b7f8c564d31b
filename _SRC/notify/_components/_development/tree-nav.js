Factory.registerComponent('tree-nav', function(sb){
	
	var ui = {},
		tree = {};
	
	function setup_ui(tree){
		
		// helper funcs
		
		
		// view methods
		function choice_ui(){
			
		}
		
		function tree_ui(tree){
			
		}
		console.log('setup');
		this.update = tree_ui.bind(this);
		this.update(tree);
		this.build();
		
	}
	
	return {
		
		init:function(){
			
			sb.listen({
				'new-tree-nav':this.create
			});
			
		},
		
		create:function(setup){
			
			sb.listen({
				'destroy-tree-nav':this.destroy
			});
			
			ui = sb.dom.make(setup.domObj.selector);
			tree = setup.tree;
			
			ui.setup = setup_ui.bind(ui);
			ui.setup(setup.tree);
			
		},
		
		destroy:function(){
			
			ui = {};
			tree = {};
			
		}
		
	}
	
});