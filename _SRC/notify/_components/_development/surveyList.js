Factory.registerComponent('survey-list', function(sb){
				
	return {
		
		destroy: function(){
			
			
		},
		
		init: function(){
			
			sb.listen({
				'survey-list-load': this.load,
				'load-survey': this.loadSurvey
			});
			
		},
		
		load: function(setup){
			console.log(setup);
			// clear display and place loading gif
			$(setup.domObj.selector).empty();
			$(setup.domObj.selector).append(sb.dom.loadingGIF);
			
			setup.surveyList = sb.dom.make(setup.domObj.selector);
			setup.surveyList.makeNode('header', 'column', {width: 12})
				.makeNode('new-button', 'button', {text: '+', css: 'pull-right btn-success pda-btn-pi'})
				.notify('click', {
					type: 'app-change-page',
					data: {
						to: 'edit-survey'
					}
				}, sb.moduleId);
			
			// create table
			setup.surveyList.makeNode('surveyList', 'table', {columns: {surveyName: 'Name', clientId: 'Client'}, css: 'table-condensed table-hover'});
			_.each(setup.allSurveys, function(survey){
				
				setup.surveyList.surveyList.makeRow(survey.surveyKey, [survey.surveyKey, survey.clientId], {css: 'edit-survey-listener', dataId: survey.surveyKey});
				
			});
			
			setup.surveyList.build();
			
			// temporary listener
			$('.edit-survey-listener').on('click', function(e){

				sb.notify({
					type: 'survey-selected-to-edit',
					data: { survey: _.where(setup.allSurveys, {surveyKey: $(this).attr('data-id')})[0] }
				});
				
			});
		
		},
		
		loadSurvey: function(data){
			
			if(data.surveyKey){
				
				sb.data.db.getSurvey(data.surveyKey, function(survey){
					
					console.log(survey);
					
				});
				
			}
			
		}
		
	}
	
});