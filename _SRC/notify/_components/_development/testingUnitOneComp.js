Factory.register('calendar-staging', function(sb) {
	
	var comps = {};
	
	function calendar(dom, state, draw) {
		
		dom.empty();
		
		dom.makeNode('wrapper', 'div', {});
		
		draw(dom);
		
		comps.calendar.notify({
			type: 'show-calendar',
			data: {
				domObj: dom.wrapper,
				events: function(callback, range) {
					
					var ret = [];
					
					sb.data.db.obj.getWhere('staff_schedules', {start_date: range, active: 'Yes', childObjs: 1}, function(data) {

						_.each(data, function(obj) {
            
				            ret.push({
				               id: obj.id,
				               name: obj.name,
				               startTime: moment(obj.start_date),
				               description: 'Your description goes here',
				               type: 'Staff Schedules',
				               color: 'green',
				               modal: {
					               btns: {
						               btn1: {
							               text: 'test',
							               css: 'red',
							               type: 'lab-run',
							               data: {
								               run: function(data) {}
							               }
						               },
						               btn2: {
							               text: 'test1',
							               css: 'green',
							               type: 'lab-run',
							               data: {
								               run: function(data) {}
							               }
						               }
					               }
				               }
				            });
				 
				        });
				        
				        sb.data.db.obj.getWhere('shifts', {start_time: range, childObjs: 1}, function(data) {

					        _.each(data, function(obj) {
            
					            ret.push({
					               id: obj.id,
					               name: obj.service.name,
					               startTime: moment(obj.start_time),
					               endTime: moment(obj.end_time),
					               description: 'Your description goes here',
					               type: 'Shifts',
					               color: 'red',
					               modal: true
					            });
					 
					        });

					        callback(ret);
					        
				        });
				         
					});
					
				},
				settings: {
					fullscreen: true
				}
			}
		});
		
	}
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'testing-lab',
						title: 'Testing Lab',
						icon: '<i class="fa fa-flask"></i>',
						views: [
							{
								id: 'lobby',
								type: 'custom',
								default: true,
								title: 'Pagoda Lab',
								icon: '<i class="fa fa-flask"></i>',
								dom: calendar
							}
						]
					}
				}
			});
			
			sb.listen({
				'lab-run': this.run,
			});
			
			comps.calendar = sb.createComponent('calendar');
			
		},
		
		destroy: function() {
			
			_.each(comps, function(comp) {
				comp.destroy();
			});
			
			comps = {};
			
		},
		
		run: function(data) {
			
			data.run(data);
			
		}
		
	}
	
});