Factory.registerComponent('rules', function(sb){
	
	var comps = {},
		rules = {},
		ui = {},
		triggers = [],
		options = {
			
			triggers_checks: {
				'select': [],
				'string': ['is']
			}
			
		},
		changeables = {};
	
	function create_rule_old(bp, dom){
		
		var rule = {};
		
		function basic_info_ui(state, rule){
			
			function process_basic_info(rule){
				
				// if a new rule-obj
				if(rule === undefined){
					rule = {};
				}
				
				var form_data = this.process().fields;
				
				rule.name = form_data.name.value;
				rule.description = form_data.description.value;
				rule.object_type = form_data.object_type.value;
				
				dom.refresh('edit-trigger', rule);
				
			}
			
			this.empty();
			
			if(state === 'edit-basic-info'){
				
				var form_args = {
					name: {
						name: 'name',
						type: 'text',
						value: '',
						label: 'Rule name'
					},
					description: {
						name: 'description',
						type: 'textbox',
						value: '',
						label: 'Description'
					},
					object_type: {
						name: 'object_type',
						type: 'select',
						label: 'Object type',
						options: []
					}
				};
				
				_.each(triggers, function(trigger){
					
					form_args.object_type.options.push({
						name: trigger,
						value: trigger
					});
					
				});
				
				this.makeNode('form', 'form', form_args);
				
				this.makeNode('btn', 'button', {text: '<i class="fa fa-chevron-right" aria-hidden="true"></i>', css: 'pull-right pda-btn-green'})
					.notify('click', {
						type: 'rules-comp-run',
						data: {
							run: process_basic_info.bind(this.form)
						}
					}, sb.moduleId);
				
			}else{
				
				this.makeNode('c', 'column', {width: 6, offset: 3});
				this.c.makeNode('name', 'headerText', {text: 'WHEN a(n) '+ rule.object_type +' is created/changed ...', css: 'text-center', size: 'small'});
				this.c.makeNode('description', 'text', {text: '<strong>'+ rule.name +'</strong> <i class="text-muted">'+ rule.description +'</i>', style: 'display:inline-block;'});
				
			}
			
			this.patch();
			
			if(_.isEmpty(rule)){
				this.width(12);
			}else{
				this.width(3);
			}
			
		}
		
		function trigger_ui(state, rule){
			
			function process_trigger_form(rule){
				
				rule.trigger = {
					
					check_property: this.l.form.process().fields.check_property.value,
					check_type: this.c.form.process().fields.check_type.value,
					check_value: this.r.form.process().fields.check_val.value,
					
				};
				
				console.log(rule);
				dom.refresh('edit-action', rule);
				
			}
			
			this.empty();
			
			if(state === 'edit-trigger'){
				
				var ui = this;
				
				sb.data.db.obj.getBlueprint(rule.object_type, function(bp){
					
					ui.makeNode('title', 'headerText', {text: 'IF', size: 'medium', css: 'text-center'});
					
					var form_args = {
						
						check_property: {
							name: 'check_property',
							type: 'select',
							label: '',
							value: '',
							options: [{
								name: 'Select a property',
								value: 0
							}]
						}
						
					}
					
					_.each(bp, function(property, key){
						
						form_args.check_property.options.push({
							name: property.name,
							value: key
						});
						
					});
					
					ui.makeNode('l', 'column', {width: 5, css: 'pda-container'})
						.makeNode('form', 'form', form_args);
					
					
					ui.makeNode('c', 'column', {width: 2, css: 'pda-container'})
						.makeNode('form', 'form', {
							
							check_type: {
								name: 'check_type',
								type: 'select',
								label: '',
								value: '',
								options: [{
									name: '=',
									value: 'equal_to'
								}, {
									name: '<',
									value: 'less_than'
								}, {
									name: '>',
									value: 'greater_than'
								}]
							}
							
						});
						
					ui.makeNode('r', 'column', {width: 5, css: 'pda-container'})
						.makeNode('form', 'form', {
							check_val: {
								name: 'check_val',
								type: 'text',
								label: '',
								value: ''
							}
						});
						
					ui.makeNode('next', 'button', {text: '<i class="fa fa-arrow-right" aria-hidden="true"></i> Next step', css: 'pda-btn-green pull-right'})
						.notify('click', {
							type: 'rules-comp-run',
							data: {
								run: process_trigger_form.bind(ui, rule)
							}
						}, sb.moduleId);
					
					ui.patch();
					
				});
				
			}else if(rule.hasOwnProperty('trigger')){
				
				this.makeNode('title', 'headerText', {text: 'IF', size: 'medium', css: 'text-center'});
				
				this.makeNode('l', 'column', {width: 5})
					.makeNode('property', 'text', {text: rule.trigger.check_property, css: 'pull-right'});
					
					
				var checkString = '';
				switch(rule.trigger.check_type){
					
					case 'equal_to':
						checkString = '=';
						break;
						
					case 'greater_than':
						checkString = '>';
						break;
						
					case 'less_than':
						checkString = '<';
						break;
						
				}
					
				this.makeNode('c', 'column', {width: 2})
					.makeNode('check_type', 'headerText', {text: checkString, size: 'small', css: 'text-center'});
				
				this.makeNode('r', 'column', {width: 5})
					.makeNode('check_val', 'text', {text: rule.trigger.check_value, css: 'pull-left'});
				
				this.patch();
				
			}
			
			if(state === 'edit-action'){
				this.width(3);
			}else if(rule.hasOwnProperty('trigger')){
				this.width(5);
			}else{
				this.width(12);
			}
			
		}
		
		function action_ui(state, rule){
			
			this.empty();
			if(state === 'edit-action'){
				
				// section title
				this.makeNode('title', 'headerText', {text: 'THEN', size: 'medium', css: 'text-center'});
				
				// action type form
				var action_type_form_args = {
					action_type: {
						name: 'action_type',
						type: 'select',
						label: '',
						value: '',
						options: [{
							name: 'Select an action',
							value: 0
						}, {
							name: 'Create new ...',
							value: 'create'
						}, {
							name: 'Set ...',
							value: 'update'
						}, {
							name: 'Send Email ...',
							value: 'send_email'
						}, {
							name: 'Send SMS ...',
							value: 'send_msg'
						}]
					}
				};
				
				this.makeNode('form', 'form', action_type_form_args);
				
				this.makeNode('add', 'button', {text: '<i class="fa fa-plus" aria-hidden="true"></i> new action', css: 'pda-btn-green'});
				
				this.patch();
				
			}else{
				
				this.patch();
				
			}
			
			if(state === 'edit-action'){
				this.width(7);
			}
			
		}
		
		function pointer_ui_1(state, rule){
						
			if(rule.hasOwnProperty('trigger') || state === 'edit-trigger'){
				
				this.makeNode('pointer', 'text', {text: '<i class="fa fa-arrow-right fa-2x" aria-hidden="true"></i>'});
				this.patch();
				
			}else{
				
				this.empty();
				this.patch();
				
			}
			
		}
		
		function pointer_ui_2(state, rule){
						
			if(rule.hasOwnProperty('action') || state === 'edit-action'){
				
				this.makeNode('pointer', 'text', {text: '<i class="fa fa-arrow-right fa-2x" aria-hidden="true"></i>'});
				this.patch();
				
			}else{
				
				this.empty();
				this.patch();
				
			}
			
		}
		
		function refresh_ui(state, rule){
			
			this.basicInfo.view(state, rule);
			this.main.pointer_1.view(state, rule);
			this.main.trigger.view(state, rule);
			this.main.pointer_2.view(state, rule);
			this.main.action.view(state, rule);
			
		}
		
		dom.empty();
		
		dom.makeNode('basicInfo', 'column', {width: 3});
		dom.basicInfo.view = basic_info_ui.bind(dom.basicInfo);
		
		dom.makeNode('main', 'column', {width: 9/* , css: 'pda-vertical-align' */});
		
		dom.main.makeNode('pointer_1', 'column', {width: 1, css: 'text-center'});
		dom.main.pointer_1.view = pointer_ui_1.bind(dom.main.pointer_1);
		
		dom.main.makeNode('trigger', 'column', {width: 5});
		dom.main.trigger.view = trigger_ui.bind(dom.main.trigger);
		
		dom.main.makeNode('pointer_2', 'column', {width: 1, css: 'text-center'});
		dom.main.pointer_2.view = pointer_ui_2.bind(dom.main.pointer_2);
		
		dom.main.makeNode('action', 'column', {width: 5, css: 'text-center'});
		dom.main.action.view = action_ui.bind(dom.main.action);
		
		dom.patch();
		
		var current_state = 'edit-basic-info';
		
		dom.basicInfo.view(current_state, rule);
		dom.main.trigger.view(current_state, rule);
		dom.main.pointer_1.view(current_state, rule);
		dom.main.pointer_2.view(current_state, rule);
		dom.main.action.view(current_state, rule);
		
		dom.refresh = refresh_ui.bind(dom);
		console.log(dom);
		
	}
	
	function create_rule(bp, dom){
		
		var blueprint = {};
		
		function update_btn(new_text, new_color){
			
			if(new_color){
				
				this.css(new_color);
				
			}
			
			if(new_text){
				
				this.text(new_text);
				
			}
			
			return true;
			
		}
		
		function subject_ui(rule){
			
			function update_object_type(trigger, property){
				
				sb.data.db.obj.getBlueprint(trigger.object_type, function(bp){
					
					blueprint = bp;
					
					rule.object_type = trigger.object_type;
					
					var dispString = trigger.name;
					
					if(property && property.hasOwnProperty('propertyName')){
						
						rule.property = property.propertyName;
						dispString += '\'s '+ property.label;
						
					}else{
						
						rule.property = false;
						
					}
					
					dom.updateSubjectBtn(dispString, 'pda-btn-large pda-btnOutline-primary');
					
					dom.main.subject_popover.hide();
					dom.main.check.check_popover.view(rule);
					
					dom.main.object_popover.view();
					
				});
								
			}
			
			this.empty();				
				
			this.makeNode('c', 'container', {css: 'pda-vertical-align'});
			
			_.each(triggers, function(trigger){
				
				var btnCss = 'pda-btn-fullWidth pda-btnOutline-primary';
				if(trigger.object_type === rule.object_type){
					btnCss = 'pda-btn-fullWidth pda-btn-primary';
				}
				
				this.makeNode('ot-'+ trigger.object_type, 'button', {text: '... '+ trigger.name +' ...', css:btnCss, style:'text-align:left;'})
					.notify('click', {
						type:'rules-comp-run',
						data:{
							run:update_object_type.bind(this, trigger)
						}
					}, sb.moduleId);
					
				_.each(trigger.properties, function(property){
					
					this.makeNode('ot-'+ trigger.object_type +'-'+ property.propertyName, 'button', {text: '... '+ trigger.name +'\'s '+ property.label +' ...', css:btnCss, style:'text-align:left;'})
						.notify('click', {
							type:'rules-comp-run',
							data:{
								run:update_object_type.bind(this, trigger, property)
							}
						}, sb.moduleId);
					
				}, this);
				
			}, this);
			
			this.patch();
			
		}
		
		function check_ui(rule){
			
			function update_check_type(check){
				
				rule.checkType = check.type;
				
				var btnColor = 'primary';
				switch(check.type){
					
					case 'created':
						btnColor = 'green';
						dom.main.check.edit.hide();
						break;
						
					case 'deleted':
						btnColor = 'red';
						dom.main.check.edit.hide();
						break;
						
					case 'equals':
						
						dom.main.check.edit.show();
						break;
						
					default:
						dom.main.check.edit.hide();
						break;
					
				}
				
				dom.updateCompareTypeBtn(check.label, 'pda-btn-large pda-btnOutline-'+ btnColor);
				
				dom.main.check.check_popover.hide();
				
			}
			
			this.empty();

			if(!rule.property){
				
				var checks = [{
					type: 'created',
					label: 'is created'
				}, {
					type: 'deleted',
					label: 'is deleted'
				}];
				
			}else{
				
				var checks = [{
					type: 'equals',
					label: 'is'
				}, {
					type: 'less_than',
					label: 'is less than'
				}, {
					type: 'greater_than',
					label: 'is greater than'
				}];
				
			}
				
			this.makeNode('c', 'container', {css: 'pda-vertical-align'});
			
			_.each(checks, function(check){
				
				var btnCss = 'pda-btn-fullWidth pda-btnOutline-primary';
				
				this.makeNode('ot-'+ check.type, 'button', {text: '... '+ check.label +' ...', css:btnCss, style:'text-align:left;'})
					.notify('click', {
						type:'rules-comp-run',
						data:{
							run:update_check_type.bind(this, check)
						}
					}, sb.moduleId);
				
			}, this);
			
			this.patch();
			
		}
		
		function action_ui(rule){
			
			function update_action_type(action){
				
				rule.actionType = action.type;
				
				if(action.hasOwnProperty('property')){
					rule.actionProperty = action.property;
				}
				
				var btnColor = 'primary';
				switch(action.type){
					
					case 'created':
						btnColor = 'green';
						break;
						
					case 'deleted':
						btnColor = 'red';
						break;
					
				}

				if(action.color){
					btnColor = action.color;
				}
				
				dom.updateActionTypeBtn(action.label, 'pda-btn-large pda-btnOutline-'+ btnColor);
				
				dom.main.action_popover.hide();
				
				switch(action.type){
					
					case 'change':
						dom.main.action.edit.hide();
						break;
						
					case 'create_note':
					case 'email':
					case 'sms':
						dom.main.action.edit.show();
						break;
					
				}
				
				dom.main.object_popover.view();
				
			}
			
			this.empty();
			
			var actions = [{
				type: 'create_note',
				label: 'create a note about',
				color:'green'
			}, {
				type: 'email',
				label: 'send an email to',
				color:'yellow'
			}, {
				type: 'sms',
				label: 'send an sms to',
				color:'yellow'
			}];

			_.each(changeables, function(changeable, propName){
				
				actions.push({
					type:'change-'+ propName,
					label:'change '+ changeable.label +' to',
					property:propName,
					color:'orange'
				});
				
			});
				
			this.makeNode('c', 'container', {css: 'pda-vertical-align'});
			
			_.each(actions, function(action){
				
				var btnCss = 'pda-btn-fullWidth pda-btnOutline-'+ action.color;
				
				this.makeNode('ot-'+ action.type, 'button', {text: '... '+ action.label +' ...', css:btnCss, style:'text-align:left;'})
					.notify('click', {
						type:'rules-comp-run',
						data:{
							run:update_action_type.bind(this, action)
						}
					}, sb.moduleId);
				
			}, this);
			
			this.patch();
			
		}
		
		function object_ui(rule){
			
			function get_options(rule, callback){

				if(rule.actionType === 'email' || rule.actionType === 'sms'){

					// get options of associated staff members/users
					var contactOptions = [];
					
					_.each(blueprint, function(property, property_key){

						if(property.type === 'objectId'){

							if(property.objectType === 'contacts'){
								
								contactOptions.push({
									type:property_key,
									label:property.name +'.'
								});
								
							}else if(property.objectType === 'staff'){
								
								contactOptions.push({
									type:property_key,
									label:property.name +'.'
								});
								
							}
							
						}
						
					});

					callback(contactOptions);
				
				}else if(rule.hasOwnProperty('actionProperty')){
					
					if(blueprint.hasOwnProperty(rule.actionProperty) && blueprint[rule.actionProperty].type === 'objectId' && blueprint[rule.actionProperty].noOptions === true){
						
						// allow for object searching
						
					}else if(blueprint.hasOwnProperty(rule.actionProperty) && blueprint[rule.actionProperty].type === 'objectId'){
						
						// get options from db
						sb.data.db.obj.getAll(blueprint[rule.actionProperty].objectType, function(option_data){
							
							var options = [];
							
							_.each(option_data, function(option_datum){
								
								options.push({
									type:option_datum.id,
									label:option_datum.name +'.'
								});
								
							});

							callback(options);
							
						});
						
						
					}else{
						
						callback();
						
					}
					
					
				}else{
					
					callback();
					
				}
				
			}
			
			function update_object_type(option){
				
				rule.indirectObjectType = option.type;
				
				dom.updateIndirectObjectTypeBtn(option.label, 'pda-btn-large pda-btnOutline-primary');
				
				dom.main.object_popover.hide();
				
/*
				switch(option.type){
					
					case 'change':
						dom.main.object.edit.hide();
						break;
						
					case 'create_note':
					case 'email':
					case 'sms':
						dom.main.action.edit.show();
						break;
					
				}
*/
				
			}
			
			this.empty();
			console.log(blueprint, rule);
			
			var ui = this;
			
			get_options(rule, function(option_data){
				
				var options = [];
				
				if(options === undefined){
					
					options = [{
						type: 'option_1',
						label: 'first option'
					}, {
						type: 'option_2',
						label: 'second option'
					}, {
						type: 'option_3',
						label: 'third option'
					}];
					
				}else{
					
					options = option_data;
					
				}
					
				ui.makeNode('c', 'container', {css: 'pda-vertical-align'});
				
				_.each(options, function(option){
					
					var btnCss = 'pda-btn-fullWidth pda-btnOutline-primary';
					
					ui.makeNode('ot-'+ option.type, 'button', {text: '... '+ option.label +'.', css:btnCss, style:'text-align:left;'})
						.notify('click', {
							type:'rules-comp-run',
							data:{
								run:update_object_type.bind(ui, option)
							}
						}, sb.moduleId);
					
				});
				
				ui.patch();
				
			});
			
		}
		
		var rule = {},
			css = 'display:inline-block;vertical-align:top;';
				
		dom.empty();
		
		dom.makeNode('main', 'container', {css: 'pda-container text-center'});
		
		// If 
		dom.main.makeNode('if', 'headerText', {text: 'If', style: css +'padding:0.25em;padding-left:0.5em;padding-right:0.5em;'});
		
		//[this], 
		dom.main.makeNode('subject', 'button', {text: 'something ', size:'large', style: css, css: 'pda-btn-large pda-btnOutline-gray'});
		
		dom.main.makeNode('subject_popover', 'popover', {parent:dom.main.subject, trigger:'click', animation:'pop', placement:'bottom'});
			
		dom.main.subject_popover.view = subject_ui.bind(dom.main.subject_popover, rule);
		dom.main.subject_popover.view();
		
		dom.main.makeNode('check', 'container', {style:css});
		dom.main.check.makeNode('check', 'button', {text: ' does something, ', size:'large', style: css, css: 'pda-btn-large pda-btnOutline-gray'});
		dom.main.check.makeNode('check_popover', 'popover', {parent:dom.main.check.check, trigger:'click', animation:'pop', placement:'bottom'});
			
		dom.main.check.check_popover.view = check_ui.bind(dom.main.check.check_popover, rule);
		dom.main.check.check_popover.view();
		
		dom.main.check.makeNode('edit', 'container', {css:'hidden', style:'display:inline-block;'})
			.makeNode('form', 'form', [{
				name:'value',
				type:'text',
				label:'',
				style:'vertical-align:top;text-align:center;font-size:20px;border:1px dashed black;border-radius:2px;padding: 6px 12px;height:100%;margin: 6px 3px;'
			}]);
		
		//then [that].
		dom.main.makeNode('then', 'headerText', {text:' then ', style: css +'padding:0.25em;padding-left:0.5em;padding-right:0.5em;'});
		
		//[that]
		dom.main.makeNode('action', 'container', {style: css});
		
		dom.main.action.makeNode('action', 'button', {text: ' do something to', size:'large', css: 'pda-btn-large pda-btnOutline-gray'});
		dom.main.makeNode('action_popover', 'popover', {parent:dom.main.action.action, trigger:'click', animation:'pop', placement:'bottom'});
		
		dom.main.action.makeNode('edit', 'button', {text: '<i class="fa fa-pencil-square-o" aria-hidden="true"></i>', size:'small', css: 'pda-center pda-btnOutline-purple pda-btn-pi hidden', style:'position:relative;'});
		
		// action popover
		dom.main.action.makeNode('pop', 'popover', {parent:dom.main.action.edit, trigger:'click', animation:'pop', placement:'bottom'})
			.makeNode('message', 'form', [{
				type:'textbox',
				placeholder:'Write your message here...',
				style:'margin:0px;padding:0px;'
			}]);
		
		dom.main.action_popover.view = action_ui.bind(dom.main.action_popover, rule);
		dom.main.action_popover.view();
			
		dom.main.makeNode('object', 'button', {text: 'something.', size:'large', style: css, css: 'pda-btn-large pda-btnOutline-gray'});
		dom.main.makeNode('object_popover', 'popover', {parent:dom.main.object, trigger:'click', animation:'pop', placement:'bottom'});
		
		dom.main.object_popover.view = object_ui.bind(dom.main.object_popover, rule);
		dom.main.object_popover.view();
			
		dom.updateSubjectBtn = update_btn.bind(dom.main.subject);
		dom.updateCompareTypeBtn = update_btn.bind(dom.main.check.check);
		dom.updateActionTypeBtn = update_btn.bind(dom.main.action.action);
		dom.updateIndirectObjectTypeBtn = update_btn.bind(dom.main.object);
		
		dom.patch();
		
	}
	
	function edit_rule(){}
	
	function erase_rule(){}
	
	function setup_ui(state){
		
		function displayRulesTable(dom){
					
			var rowLink = {
					type:'edit',
					header:function(obj){},
					action:edit_rule
				};
			
			comps.table.notify({
				type:'show-table',
				data:{							
					domObj:dom,
					tableTitle:'System Rules',
					navigation:false,
					borderColor:'pda-panel-primary',
					objectType:'system_rules',
					searchObjects:false,
					filters:false,
					download:false,
					headerButtons:{
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							
							// !TODO: run this through rules extension
							action:function(){}
						},
						create:{
							name:'<i class="fa fa-plus"></i> Create New',
							css:'pda-btn-green',
							domType:'full',
							action:create_rule
						}
					},
					calendar:false,
					rowSelection:true,
					rowLink:rowLink,
					multiSelectButtons:{
						erase:{
							name:'<i class="fa fa-trash-o"></i> Delete',
							css:'pda-btn-red',
							domType:'none',
							action:erase_rule
						}
					},
					visibleCols:{
						name: 'Name',
						description: 'Description'
					},
					searchObjects:false,
					dateRange:false,
					home:false,
					settings:false,
					cells: {},
					childObjs:0,
					
					//!TODO: run this through rules extension instead
					data:function(paged, callback){
						
						sb.data.db.obj.getAll('system_rules', function(ret){
	
							callback(ret);
							
						}, 1, paged);
						
					}
				}
			});
			
		}
		
		// place loader
		this.empty();
		this.makeNode('loader', 'loader', {size: 'medium'});
		this.build();
		
		// run table
		displayRulesTable(this);
		
	}
	
	return {
		
		init: function(){
			
			sb.listen({
				'show-rules-overview': this.showOverview,
				'show-rules-table': this.showTable
			});
			
		},
		
		run: function(data){ data.run(data); },
		
		showOverview: function(setup){},
		
		showTable: function(setup){
			
			sb.data.db.obj.getWhere('events', {
				guest_count: {
					type: 'less_than',
					value: 20
				}}, function(response){
				console.log(response);
			});
			
			sb.listen({
				'show-rules-overview': this.showOverview,
				'show-rules-table': this.showTable,
				'rules-comp-run': this.run
			});
			
			if(setup.triggers === undefined){
				
				return this.showOverview(setup);
				
			}
			
			triggers = setup.triggers;
			changeables = setup.changeable;
			
			comps.table = sb.createComponent('crud-table');
			
			ui = sb.dom.make(setup.domObj.selector);
			ui.empty();
			ui.build();
			
			ui.view = setup_ui;
			ui.view('table');
			
			console.log(setup);
			
		},
		
		destroy: function(){
			
			sb.listen({
				'show-rules-overview': this.showOverview,
				'show-rules-table': this.showTable
			});
			
			_.each(comps, function(comp){
				
				comp.destroy();
				
			});
			
			comps = {};
			rules = {};
			ui = {};
			
		}
		
	}
	
});