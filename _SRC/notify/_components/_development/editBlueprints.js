Factory.register('edit-blueprint-objects', function(sb){

	var	domObj = null,
		objectList = [],
		components = {},
		objectType = 'permissions',
		staff = {},
		staffList = [],
		permissions = [],
		currentUser = 0;
		
	function create_new_blueprint_form(){
		
		this.empty();
		
		this.makeNode('modal', 'modal', {css:'ui modal'});
		
		this.modal.body.makeNode('form', 'form', {
			object_name: {
				type:'text',
				name:'object_name',
				label:'Object name',
				placeholder:''
			},
			blueprint: {
				type: 'textbox',
				name: 'blueprint',
				label: 'Blueprint JSON',
				placeholder: '',
				rows: 35
			}
		});
		
		// create button
		this.modal.body.makeNode('createBtn', 'button', {text:'Create blueprint', css:'pull-right pda-btn-green'});
		this.modal.body.createBtn.notify('click', {
			type:'edit-blueprint-objects-run',
			data:{
				run:function(){

					var formData = this.modal.body.form.process().fields;
					
					if(_.isEmpty(formData.object_name.value)){
						
						sb.dom.alerts.alert('No object name!', '', 'error');
						return false;
						
					}else if(_.isEmpty(formData.blueprint.value)){
						
						sb.dom.alerts.alert('No blueprint!', '', 'error');
						return false;
						
					}
					
					var newBlueprint = JSON.parse(formData.blueprint.value);
					var blueprintName = formData.object_name.value;
					this.modal.body.createBtn.loading(true);
					this.modal.body.createBtn.css('pull-right');
					
					sb.data.db.blueprint.create({name:blueprintName}, newBlueprint, function(response){
						
						sb.dom.alerts.alert('Saved!', '', 'success');
						this.modal.hide();
						
					}.bind(this));	
					
				}.bind(this)
			}
		}, sb.moduleId);
		
		this.patch();
		
		this.modal.show();
		
	}

	return {

		init: function(){
			
			currentUser = +sb.data.cookie.get('uid');
			
			sb.listen({
				'edit-blueprint-objects-load': this.load,
				'edit-blueprint-object': this.editSingleUser,
				'update-blueprint-objects': this.update,
				'edit-blueprint-objects-run': this.run
			});

			if (appConfig.type === 'developer' || appConfig.instance === 'voltzsoftware') {
				
				sb.notify({
					type:'register-dev-tool',
					data: {
						section: 'Blueprints',
						displayName: 'Edit',
						page: 'edit-edit-blueprint-object',
						pageParams: {}
					}
				});
				
			}

		},

		load: function(setup){

			sb.listen({
				'edit-blueprint-objects-run':this.run,
				'change-blueprint-object': this.change,
				'edit-blueprint-objects': this.edit,
				'edit-staff-blueprint-objects': this.destroy,
				'edit-blueprint-object': this.editSingleUser,
				'update-blueprint-object': this.save,
				'update-blueprint-objects': this.update
			});

			domObj = sb.dom.make(setup.domObj.selector);
			domObj.makeNode('panel', 'panel', {header:'</br><div class="ui dividing header">Edit Blueprints</div></br>', css:'ui grid container'});
			domObj.panel.body.makeNode('loader', 'text', {text: sb.dom.loadingGIF});
			domObj.makeNode('modals', 'div', {css:'sixteen wide column'});
			
			// create new blueprint ui
			domObj.makeNode('createForm', 'container', {});
			domObj.createForm.view = create_new_blueprint_form.bind(domObj.createForm);
			
			domObj.panel.body.makeNode('createBtn', 'button', {text:'<i class="fa fa-plus" aria-hidden="true"></i> New Blueprint', css:'large ui right floated green button'})
				.notify('click', {
					type:'edit-blueprint-objects-run',
					data:{
						run:function(){
							
							console.log('test');

							this.createForm.view();
							
						}.bind(domObj)
					}
				}, sb.moduleId);
			
			domObj.build();

			sb.data.db.blueprint.getAll(function(blueprints){

				var count = 0;

				domObj.panel.body.makeNode('permissionsList', 'div', {name: 'ui list'});

				_.each(
					_.sortBy(blueprints, function(o) { return o.blueprint_name; })
					, function(item){

						domObj.panel.body.permissionsList.makeNode('item-'+item.id, 'listItem', {content:''+item.blueprint_name+' <small><i class="fa fa-pencil"></i></small>', css:'item'})
						.notify('click', {
							type:'edit-blueprint-object',
							data: {
								object: Object.assign({}, item),
								modalContainer: domObj.modals
							}
						}, sb.moduleId);
	
					});

				delete domObj.panel.body.loader;

				domObj.panel.patch();

			});

		},

		destroy: function(){

			$(domObj.selector).empty();
			domObj = null;
			objectList = [];
			components = ['crud'];
			objectType = 'permissions';
			staffList = [];
			permissions = [];

			sb.listen({
				'edit-blueprint-objects-load': this.load,
				'edit-blueprint-object': this.editSingleUser,
				'update-blueprint-objects': this.update
			});
			
		},
		
		run: function(data){ data.run(data); },

		change: function(editObj){

			// check if permissions have already been created for this system
			sb.data.db.obj.getAll('permissions', function(permissionsObjs){

				permissions = permissionsObjs;

				var permissionObj = _.where(permissions, {pageModuleId:editObj.object.module});

				if(permissionObj.length == 0){

					var newPermissionObj = {
							pageModuleId: editObj.object.module,
							displayName: editObj.object.displayName
					};

					sb.data.db.obj.create('permissions', newPermissionObj, function(permission){

						sb.notify({
							type:'edit-blueprint-object',
							data: {
								permission: permission,
								updating: false
							}
						});

					});

				}else{

					sb.notify({
						type:'edit-blueprint-object',
						data: {
							permission: permissionObj[0],
							updating: false
						}
					});

				}

			});

		},

		edit: function(editObj){

			//var permissionButtons = ['view', 'edit', 'erase'];
			var permissionButtons = ['view'],
				allButton = 'NO',
				allButtonColor = 'pda-btnOutline-red';

			if(editObj.updating == false){

				domObj.modals.makeNode('editModal', 'modal', {});

				domObj.modals.editModal.body.makeNode('header', 'headerText', {size: 'small', text:'Edit '+ editObj.permission.displayName +' System Permissions'});

				domObj.modals.editModal.body.makeNode('break', 'lineBreak', {spaces:1});

			}else{

				delete domObj.modals.editModal.body.table;

			}

			domObj.modals.editModal.body.makeNode('table', 'table',
				{
					css: 'table-hover table-condensed',
					columns: {
						name: 'Staff',
						view: 'View',
/*
						edit: 'Create<br />& Update',
						erase: 'Delete'
*/
					}
				});

			domObj.modals.editModal.body.table.makeRow(
				'allStaff',
				['<span class="text-bold">ALL STAFF</span>', '']
			);

			if(staffList.length == editObj.permission.view.length){

				allButton = 'YES';
				allButtonColor = 'pda-btnOutline-green';

			}

			domObj.modals.editModal.body.table.body.allStaff.view.makeNode('button', 'button', {text:allButton, css:'btn-default pda-btn-large pda-btn-fullWidth '+ allButtonColor}).notify('click', {
				type:'update-blueprint-object',
				data: {
					staff:'all',
					permission:editObj.permission,
					type:'view'
				}
			}, sb.moduleId);
			
			_.each(staffList, function(staff){
			//_.each(_.reject(staffList, function(staff){ return staff.id == currentUser; }), function(staff){

				domObj.modals.editModal.body.table.makeRow(
					'staff-'+staff.id,
					[staff.fname+' '+staff.lname, '']
				);

				_.each(permissionButtons,  function(btn){

					if( $.inArray(staff.id.toString(), editObj.permission[btn]) < 0){

						domObj.modals.editModal.body.table.body['staff-'+staff.id][btn].makeNode('button', 'button', {text:'No', css:'btn-default pda-btn-fullWidth'}).notify('click', {
							type:'update-blueprint-object',
							data: {
								staff:staff,
								permission:editObj.permission,
								type:btn
							}
						}, sb.moduleId);

					}else{

						domObj.modals.editModal.body.table.body['staff-'+staff.id][btn].makeNode('button', 'button', {text:'Yes', css:'btn-success pda-btn-fullWidth'}).notify('click', {
							type:'update-blueprint-object',
							data: {
								staff:staff,
								permission:editObj.permission,
								type:btn
							}
						}, sb.moduleId);

					}

				});

			});

			if(editObj.updating == false){

				domObj.modals.patch();

				domObj.modals.editModal.show();

			}else{

				domObj.modals.editModal.body.patch();

			}

		},

		editSingleUser: function(editObj){

			domObj = editObj.modalContainer;

			domObj.makeNode('permModal', 'modal', {});

			domObj.permModal.body.makeNode('header', 'headerText', {size: 'small', text:'Edit '+ editObj.object.blueprint_name +' Blueprint'});

			domObj.permModal.body.makeNode('break', 'lineBreak', {spaces:1});
			
			domObj.permModal.body.makeNode('form', 'form', {
				blueprint: {
					type: 'textbox',
					name: 'blueprint',
					label: 'Blueprint JSON',
					placeholder: '',
					value: JSON.stringify(editObj.object.blueprint, null, ' '),
					rows: 35
				}
			});
			
			domObj.permModal.footer.makeNode('save', 'button', {text:'Update'}).notify('click', {
				type: 'update-blueprint-object',
				data: {
					modal: domObj.permModal,
					form: domObj.permModal.body.form,
					item: editObj.object
				}
			}, sb.moduleId);

			domObj.patch();

			domObj.permModal.show();

		},
		
		save: function(saveObj){
			
			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: ''
			}, function(resp){
				
				if(resp){
					
					var newItem = saveObj.item,
						formObj = saveObj.form.process();
					
					newItem.blueprint = JSON.parse(formObj.fields.blueprint.value);
					
					sb.data.db.blueprint.update(newItem, function(response){
						
						sb.dom.alerts.alert('Saved!', '', 'success');
						
					});					
			
				}
				
			});
			
		},

		update: function(updateObj){

			if(updateObj.staff == 'all'){

				sb.dom.alerts.ask({
					title: 'Do you want to continue?',
					text: 'This will not affect your access to this page.'
				}, function(resp){

					if(resp){

						swal.disableButtons();

						if(staffList.length == updateObj.permission.view.length){

							updateObj.permission.view = [currentUser];

						}else{

							updateObj.permission.view = [];

							_.each(staffList, function(staff){

								updateObj.permission.view.push(staff.id);

							});

						}

						sb.data.db.obj.update('permissions', updateObj.permission, function(updatedPermission){

							sb.notify({
								type:'edit-blueprint-objects',
								data: {
									permission: updatedPermission,
									updating: true
								}
							});

							sb.dom.alerts.alert('Success!', '', 'success');

						});

					}

				});

			}else{

				if( $.inArray(updateObj.staff.id.toString(), updateObj.permission[updateObj.type]) < 0 ){

					updateObj.permission[updateObj.type].push(updateObj.staff.id.toString());

					switch(updateObj.type){

						case 'edit':
						case 'erase':

							if( $.inArray(updateObj.staff.id.toString(), updateObj.permission.view) < 0 ){

								updateObj.permission.view.push(updateObj.staff.id.toString());

							}

							break;

					}

				}else{

					updateObj.permission[updateObj.type] = _.reject(updateObj.permission[updateObj.type], function(staffId){return staffId == updateObj.staff.id});

					switch(updateObj.type){

						case 'view':

							if( $.inArray(updateObj.staff.id.toString(), updateObj.permission.edit) > -1 ){

								updateObj.permission.edit = _.reject(updateObj.permission.edit, function(staffId){return staffId == updateObj.staff.id});

							}

							if( $.inArray(updateObj.staff.id.toString(), updateObj.permission.erase) > -1 ){

								updateObj.permission.erase = _.reject(updateObj.permission.erase, function(staffId){return staffId == updateObj.staff.id});

							}

							break;

					}

				}

				sb.data.db.obj.update('permissions', updateObj.permission, function(updatedPermission){

					sb.notify({
						type:'edit-blueprint-objects',
						data: {
							permission: updatedPermission,
							updating: true
						}
					});

					sb.notify({
						type:'edit-blueprint-object',
						data: {
							permission: updatedPermission,
							updating: true
						}
					});

				});

			}

		}

	}

});
