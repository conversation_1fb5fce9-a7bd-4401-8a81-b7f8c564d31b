Factory.registerComponent('routes', function(sb){
	
	var ui = {},
		tableUI = {},
		components = {},
		objectType = '',
		innerDomView,
		homeScreenView = false;
		
	function buildUI(domObj){
		
		components.table = sb.createComponent('crud-table');
		
		ui = sb.dom.make(domObj.selector);
		
		tableUI = ui.makeNode('table', 'container', {});
		tableUI.state = tableState;
				
		ui.build();
		
		tableUI.state();
		
	}
	
	function combineRoutes(objs, dom, blueprint, saveChanges){
		
		if(saveChanges){
						
			var form = dom.body.cont.form.process();
			
			if(form.completed == false){
				sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
				return;
			}
			
			dom.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving', css:'pda-btn-primary'});
			dom.footer.btns.patch();
			
			var waypoints = [];
			
			_.each(objs, function(o){
				_.each(o.waypoints, function(w){
					waypoints.push(+w);
				});
			});

			waypoints = _.uniq(waypoints);

			var newObject = {
					name:form.fields.name.value,
					run_date:form.fields.run_date.value,
					assigned_to: +form.fields.assigned_to.value,
					waypoints:waypoints,
					related_type:objectType
				};
			
			sb.data.db.obj.create('routes', newObject, function(routeObj){
				
				eraseRoutes(objs, dom, true);
				
			});
						
		}else{
			
			dom.body.makeNode('loading', 'headerText', {text:'<i class="fa fa-circle-o-notch fa-spin"></i>', css:'text-center', size:'small'});			
								
			dom.body.patch();
			
			sb.data.db.obj.getAll('users', function(users){
				
				delete dom.body.loading;
				
				dom.body.makeNode('header', 'headerText', {text:'Combining '+ objs.length +' routes', size:'small'});
				
				dom.footer.makeNode('btns', 'buttonGroup', {});
				dom.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
					type:'routes-run',
					data:{run:combineRoutes.bind(this, objs, dom, blueprint, true)}
				}, sb.moduleId);
							
				dom.body.makeNode('cont', 'container', {css:'pda-container'});
				
				dom.body.cont.makeNode('form', 'form', {
					run_date:{
						type:'date',
						label:'New Run Date',
						name:'run_date',
						value:objs[0].run_date
					},
					name:{
						type:'text',
						label:'New Route Name',
						name:'name',
						value:objs[0].name
					},
					assigned_to:{
						type:'select',
						label:'Assigned To',
						name:'assigned_to',
						options:_.map(users, function(u){ var ret = {name:u.fname +' '+ u.lname, value:u.id}; if(u.id == objs[0].assigned_to){ ret.selected = true; } return ret; })
					}
				});
							
				dom.body.patch();
				dom.footer.patch();
				
			});
			
		}
		
	}
	
	function createRoute(blueprint, dom){
		
		dom.makeNode('panel', 'panel', {header:'<i class="fa fa-plus"></i> Create A Route', css:'pda-panel-primary'});
		
		dom.panel.body.makeNode('btns', 'buttonGroup', {css:'pull-right'});
		
		dom.panel.body.btns.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left"></i> Cancel', css:'pda-btn-red pda-btn-x-small'});
		
		dom.panel.body.btns.makeNode('create', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading', css:'pda-btn-primary pda-btn-x-small'});
		
		dom.patch();
		
		sb.data.db.obj.getAll('users', function(users){
			
			dom.panel.body.makeNode('contBreak', 'lineBreak', {});
		
			dom.panel.body.makeNode('cont', 'container', {css:'pda-container'});
			
			var formSetup = {
					name:{
						type:'text',
						name:'name',
						label:'Route Name'
					},
					users:{
						type:'checkbox',
						name:'users',
						label:'Assigned To',
						options:_.map(users, function(u){ return {name:'users', label:u.fname, value:u.id}; })
					},
					related_type:{
						type:'hidden',
						name:'related_type',
						value:objectType
					}
				};	
			
			dom.panel.body.btns.makeNode('create', 'button', {text:'<i class="fa fa-plus"></i> Save', css:'pda-btn-green pda-btn-x-small'}).notify('click', {
				type:'routes-run',
				data:{run:saveCreateForm.bind(dom)}
			}, sb.moduleId);
	
			dom.panel.body.cont.makeNode('form', 'form', formSetup);
			
			dom.panel.body.patch();
			
		});		
		
	}
	
	function editRoutes(objs, dom, blueprint, saveChanges){
		
		if(saveChanges){
						
			var forms = [],
				objectsToUpdate = [];
			
			_.each(objs, function(o, k){
				
				forms.push(
					{
						object:o.id,
						run_date:dom.panel.body.cont.table.body['row-'+o.id].run_date.form.process(),
						name:dom.panel.body.cont.table.body['row-'+o.id].name.form.process(),
						assigned_to:dom.panel.body.cont.table.body['row-'+o.id].users.form.process()
					}
				);
				
				if(forms[k].name.completed == false || forms[k].assigned_to.completed == false){
					sb.dom.alerts.alert('Error', 'Please fill out all form fields.', 'error');
					return;
				}
				
				objectsToUpdate.push({
					id:o.id,
					run_date:forms[k].run_date.fields.run_date.value,
					name:forms[k].name.fields.name.value,
					assigned_to: +forms[k].assigned_to.fields.users.value
				});
				
			});

			dom.panel.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving', css:'pda-btn-primary pda-btn-x-small'});
			
			dom.panel.body.btns.patch();
			
			sb.data.db.obj.update('routes', objectsToUpdate, function(updated){
				
				components.table.notify({
					type:'crud-table-back-to-table',
					data:{}
				});
				
				components.table.notify({
					type:'update-table',
					data:{}
				});
				
			});
						
		}else{
			
			dom.makeNode('panel', 'panel', {header:'Edit Routes', css:'pda-panel-primary'});
		
			dom.panel.body.makeNode('btns', 'buttonGroup', {css:'pull-right'});
			
			dom.panel.body.makeNode('btnsBreak', 'lineBreak', {});
			
			dom.panel.body.btns.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left"></i> Cancel', css:'pda-btn-red pda-btn-x-small'}).notify('click', {
				type:'crud-table-back-to-table',
				data:{}
			}, sb.moduleId);
			
			dom.panel.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading', css:'pda-btn-primary pda-btn-x-small'});
			
			dom.patch();
			
			sb.data.db.obj.getAll('users', function(users){
				
				dom.panel.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save Changes', css:'pda-btn-green pda-btn-x-small'}).notify('click', {
					type:'routes-run',
					data:{run:editRoutes.bind(this, objs, dom, blueprint, true)}
				}, sb.moduleId);
							
				dom.panel.body.makeNode('cont', 'container', {css:'pda-container'});
				
				dom.panel.body.cont.makeNode(
					'table',
					'table',
					{
						css: 'table-hover table-condensed',
						columns: {
							run_date: 'Run Date',
							name: 'Route Name',
							users: 'Assigned To'
						}
					}
				);
				
				_.each(objs, function(o){
					
					dom.panel.body.cont.table.makeRow(
						'row-'+o.id,
						[
							'',
							'',
							''
						]
					);
					
					var selectedUsers = _.pluck(o.users, 'id');
	
					var runDateFormSetup = {
							run_date:{
								name:'run_date',
								type:'date',
								label:'',
								dateFormat:'M/D/YYYY',
								value:moment(o.run_date).format('M/D/YYYY')
							}
						},
						nameFormSetup = {
							users:{
								name:'name',
								type:'text',
								label:'',
								value:o.name
							}
						},
						userFormSetup = {
							users:{
								name:'users',
								type:'select',
								label:'',
								options:_.map(users, function(u){ var ret = {name:u.fname +' '+ u.lname, value:u.id}; if(o.assigned_to.id == u.id){ ret.selected = true; } return ret; })
							}
						};

					dom.panel.body.cont.table.body['row-'+o.id].run_date.makeNode('form', 'form', runDateFormSetup);					
					dom.panel.body.cont.table.body['row-'+o.id].name.makeNode('form', 'form', nameFormSetup);
					dom.panel.body.cont.table.body['row-'+o.id].users.makeNode('form', 'form', userFormSetup);
					
				});
				
				dom.panel.body.patch();
				
			});
			
		}
		
	}
	
	function eraseRoutes(objs, dom, confirm){
		
		if(confirm === true){
			
			dom.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Deleting', css:'pda-btn-primary'});
			
			dom.footer.btns.patch();
			
			var objsToDelete = [];
			_.each(objs, function(o){
				
				objsToDelete.push({
					objectType:'routes',
					id:o.id
				});
				
			});
			
			function deleteObjects(allObjects, callback, count){
										
				if(!count){
					count = 0;
				}
				
				sb.data.db.obj.erase(allObjects[count].objectType, allObjects[count].id, function(done){
					
					count++;
					
					if(count == allObjects.length){
						
						callback(count);
						
					}else{
						
						deleteObjects(allObjects, callback, count);
						
					}
					
				});
				
			}
			
			deleteObjects(objsToDelete, function(deleted){

				dom.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Done', css:'pda-btn-green'});
				
				dom.footer.btns.patch();
				
				setTimeout(function(){
					
					dom.hide();
					
					components.table.notify({
						type:'update-table',
						data:{}
					});
					
				}, 1000);				
				
			});
			
		}else{
			
			
			dom.body.makeNode('header', 'headerText', {text:'Are you sure you want to delete '+ objs.length +' route(s)?', css:'text-center', size:'small'});		
		
			dom.footer.makeNode('btns', 'buttonGroup', {});
			
			dom.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Yes', css:'pda-btn-green'}).notify('click', {
				type:'routes-run',
				data:{
					run:eraseRoutes.bind(this, objs, dom, true)
				}
			}, sb.moduleId);
			dom.footer.btns.makeNode('no', 'button', {text:'<i class="fa fa-times"></i> No', css:'pda-btn-red'});
			
			dom.body.patch();
			dom.footer.patch();
			
		}
		
	}
	
	function saveCreateForm(){
		
		var form = this.panel.body.cont.form.process();
		
		if(form.completed == false || !form.fields.users){
			sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
			return;
		}
		
		this.panel.body.btns.makeNode('create', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving', css:'pda-btn-x-small pda-btn-primary'});
		this.panel.body.btns.patch();
		
		var newRoute = {
				related_type:form.fields.related_type.value,
				users:form.fields.users.value,
				name:form.fields.name.value
			};		
		
		sb.data.db.obj.create('routes', newRoute, function(routeObj){
			
			components.table.notify({
				type:'update-table',
				data:{}
			});
			
			components.table.notify({
				type:'crud-table-back-to-table',
				data:{}
			});
			
		}, 2);
		
	}
	
	function singleRouteView(obj, dom){
		
		function showMap(dom, route, waypoint){
			
console.log(dom, route, waypoint);
			
			dom.col2.panel.body.cont.makeNode('btns', 'buttonGroup', {css:''});
			
			dom.col2.panel.body.cont.btns.makeNode('phone', 'button', {text:'<i class="fa fa-mobile"></i> Send to phone', css:'pda-btn-x-small pda-btn-blue'});
			
			//dom.col2.panel.body.cont.makeNode('buttonBreak', 'lineBreak', {});
			
			dom.col2.panel.body.cont.makeNode('mapContainer', 'text', {text:'<div id="mapContainer" style="width:100%; height:500px;">Loading map...</div>'});
			
			dom.col2.panel.body.cont.patch();
			
			var geocoder = new google.maps.Geocoder(),
				addressString = waypoint.address.street +' '+ waypoint.address.city +', '+ waypoint.address.state +' '+ waypoint.address.zip,
				directionsDisplay = new google.maps.DirectionsRenderer();;
			
			geocoder.geocode({address:addressString}, function(results, status){
				
				var map = new google.maps.Map(document.getElementById('mapContainer'), {
						zoom: 12
					});
					
				map.setCenter(results[0].geometry.location);	
										
				var marker = new google.maps.Marker({
						position: results[0].geometry.location,
						map: map
					});
					
				google.maps.event.trigger(map, "resize");
				
			});
		
			
		}
				
		dom.makeNode('col1', 'column', {width:6}).makeNode('panel', 'container', {header:'', css:''}).makeNode('body', 'container', {}).makeNode('cont', 'container', {css:''}).makeNode('loading', 'text', {text:'<i class="fa fa-circle-o-notch fa-spin"></i>', css:'text-right'});

		dom.makeNode('col2', 'column', {width:6}).makeNode('panel', 'container', {header:'', css:''}).makeNode('body', 'container', {}).makeNode('cont', 'container', {css:'pda-container pda-panel-blue pda-Panel'});
				
		dom.patch();
		
		sb.data.db.obj.getWhere(obj.related_type, {
			id:{
				type:'or',
				values:_.map(obj.waypoints, function(p){ return +p; })
			},
			childObjs:2
		}, function(points){
			
			_.each(points, function(p){
				
				delete dom.col1.panel.body.cont.loading;
				
				dom.col1.panel.body.cont.makeNode('cont-'+p.id, 'container', {css:'pda-container pda-Panel pda-panel-blue'});
				
				dom.col1.panel.body.cont['cont-'+p.id].makeNode('btns', 'buttonGroup', {});
				
/*
				dom.col1.panel.body.cont['cont-'+p.id].btns.makeNode('map', 'button', {text:'<i class="fa fa-map"></i> Map', css:'pda-btn-x-small pda-btn-blue'}).notify('click', {
					type:'routes-run',
					data:{run:showMap.bind(this, dom, obj, p)}
				}, sb.moduleId);
*/
				
				innerDomView(obj, p, dom.col1.panel.body.cont['cont-'+p.id]);
								
			});			
			
			dom.col1.panel.body.cont.patch();
			
			dom.col2.panel.body.cont.makeNode('info', 'container', {});
			
			dom.col2.panel.body.cont.makeNode('mapContainer', 'text', {text:'<div id="mapContainer" style="width:100%; height:500px;">Loading map...</div>'});
			
			dom.col2.panel.body.cont.patch();
			
			var geocoder = new google.maps.Geocoder(),
				directionsDisplay = new google.maps.DirectionsRenderer(),
				directions = new google.maps.DirectionsService(),
				map = new google.maps.Map(document.getElementById('mapContainer'), {
					zoom: 12
				}),
				stops = [];
				
			function geocodeAddressStrings(strings, callback, count, ret){

				var addressString = strings[count].street +' '+ strings[count].city +' '+ strings[count].state +' '+ strings[count].zip;

				geocoder.geocode({address:addressString}, function(res, status){
					
					ret.push({
						location:res[0].geometry.location,
						stopover:true
					});
					
					count++;
					
					if(strings[count]){
						
						geocodeAddressStrings(strings, callback, count, ret);
						
					}else{
						
						callback(ret);
						
					}
					
				});
				
			}
			
			geocodeAddressStrings(_.pluck(points, 'address'), function(stops){

				directionsDisplay.setMap(map);
							
				google.maps.event.trigger(map, "resize");

				directions.route({
					travelMode:'DRIVING',
					origin:points[0].address.zip,
					waypoints:stops,
					destination: points[0].address.zip,
					optimizeWaypoints:true,
				}, function(result, status) {
console.log(result, status);					
					if(status == 'OK'){		
						
						var totalTime = _.reduce(result.routes[0].legs, function(total, l){
								return total + l.duration.value;
							}, 0);
						
						dom.col2.panel.body.cont.info.makeNode('title', 'headerText', {text:'Route Info', size:'small'});

						dom.col2.panel.body.cont.info.makeNode('stops', 'headerText', {text:'Total stops: '+ result.routes[0].legs.length, size:'x-small'});

						dom.col2.panel.body.cont.info.makeNode('stats', 'headerText', {
							text:'Total drive time: '+ (totalTime / 60).toFixed(2) + ' minutes',
							size:'x-small'});
							
						dom.col2.panel.body.cont.info.patch();	

						directionsDisplay.setDirections(result);
						
					}
				
				});			
				
			}, 0, []);
			
		});
		
	}
	
	function settingsView(){}
	
	function tableState(){
		
		function buildState(routeId){
						
			var crudSetup = {							
					domObj:this,
					tableTitle:'Routes',
					objectType:'routes',
					searchObjects:false,
					filters:false,
					download:false,
					headerButtons:{
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							action:function(){}
						}
					},
					rowSelection:true,
					rowLink:{
						type:'tab',
						header:function(obj){
							return obj.name;
						},
						action:singleRouteView
					},
					multiSelectButtons:{
						erase:{
							name:'<i class="fa fa-trash-o"></i> Delete',
							css:'pda-btn-red',
							domType:'modal',
							action:eraseRoutes
						},
						edit:{
							name:'<i class="fa fa-pencil"></i> Edit',
							css:'pda-btn-orange',
							domType:'full',
							action:editRoutes
						},
						combine:{
							name:'<i class="fa fa-compress"></i> Combine',
							css:'pda-btn-orange',
							domType:'modal',
							action:combineRoutes
						}
					},
					visibleCols:{
						run_date:'Run Date',
						name:'Name',
						totalWaypoints:'Total Waypoints',
						assigned_to:'Assigned To'
					},
					searchObjects:false,
					cells: {
						run_date:function(obj){
							return moment(obj.run_date).format('M/D/YYYY');
						},
						totalWaypoints:function(obj){
							return obj.waypoints.length;
						}
					},
					childObjs:2,
					data:function(paged, callback){
						
						sb.data.db.obj.getAll('routes', function(ret){
							
							callback(ret);
							
						}, 2, paged);
						
					}
				};
				
			if(routeId){
				crudSetup.objectId = routeId;
			}	
			
			if(homeScreenView){
				crudSetup.home = {
						action:homeScreenView,
						default:false
					};
			}
				
			components.table.notify({
				type: 'show-table',
				data: crudSetup
			});
	
			
		}
		
		function show(){}
		
		this.state.show = buildState.bind(this);
				
	}
				
	return {
		
		init: function(){
			
			sb.listen({
				'routes-run':this.run,
				'start-routes-component':this.start
			});
			
		},
		
		destroy: function(){
			
			_.each(components, function(comp){
				comp.destroy();
			});
			
			domObj = {},
			components = {};
			
			sb.listen({
				'start-routes-component':this.start
			});
			
		},
		
		run: function(data){ data.run(); },
		
		start: function(data){
			
			if(data.objectType){
				objectType = data.objectType;
			}
			
			if(data.singleView){
				innerDomView = data.singleView;
			}
			
			if(data.homeView){
				homeScreenView = data.homeView;
			}
									
			buildUI(data.domObj);
			
			tableUI.state.show(data.routeId);
			
		}
									
	}
	
});