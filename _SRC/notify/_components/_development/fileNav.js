Factory.registerComponent('file-nav', function(sb){
	
	var	domObj = {},
		files = [],
		fileTypes = {},
		objectType = '',
		state = {
			selection: [],
			modalOpen: false,
			searchPhrase: ''
		}, 
		objectData = {},
		dragSelectors = [],
		permissions = {
			edit: false
		},
		mode = 'default';
	
	function getCurrentDir(groupedFiles, selection, i){
		
		if(typeof i === 'undefined'){
			i = 0;
		}
		if(_.isEmpty(selection)){
			return groupedFiles;
		}
		
		var ret = groupedFiles[selection[i]];
		
		if(typeof selection[i + 1] !== 'undefined'){
			
			ret = getCurrentDir(ret.items, selection, i + 1);
			
		}
			
		return ret;
		
	}
	
	function groupFiles(fileList){
		
		var clone = _.clone(fileList);
		
		if(state.searchPhrase !== ''){
			
			clone = _.filter(clone, function(file){ 
				
				if(typeof file.file_name === 'undefined'){
					return false;
				}
				
				if(file.file_name.indexOf(state.searchPhrase) > -1 || file.oid_type.indexOf(state.searchPhrase) > -1){
					
					return true;
					
				}
				
				var dispString = '';
				switch(file.oid_type){
					
					case 'client':
					case 'clients':
					var client =  _.where(objectData.clients, {id: file.oid})[0];
					if(!_.isEmpty(client) && typeof client.fname !== 'undefined'){
						dispString = client.lname;
					}
					if(!_.isEmpty(client) && typeof client.lname !== 'undefined'){
						dispString += ', '+ client.fname;
					}
					break;
					
					case 'event':
					case 'events':
					if(typeof _.where(objectData.events, {id: file.oid})[0] !== 'undefined'){
						dispString = _.where(objectData.events, {id: file.oid})[0].event_name;
					}else{
						dispString = '';
					}
					break;
					
					case 'staff':
					case 'staff-member':
					var staffMember =  _.where(objectData.staff, {id: file.oid})[0];
					if(!_.isEmpty(staffMember) && typeof staffMember.fname !== 'undefined'){
						dispString = staffMember.lname;
					}
					if(!_.isEmpty(staffMember) && typeof staffMember.lname !== 'undefined'){
						dispString += ', '+ staffMember.fname;
					}
					break;
					
					default:
					dispString = file.oid_type + file.oid;
					break;
					
				}
				
				if(dispString.indexOf(state.searchPhrase) > -1){
					
					return true
					
				}
				
				return false;
				
			});
			
			_.each(clone, function(file){
			
				if(file.parent > 0){
					console.log(_.where(fileList, {id: file.parent})[0]);
					clone.push(_.where(fileList, {id: file.parent})[0]);
					
				}
				
			});
			
		}
		
		var withoutSubfiles = _.filter(clone, function(file){ return typeof file.parent === 'undefined' || file.parent < 1; });
		withoutSubfiles = _.filter(withoutSubfiles, function(file){ return file.oid_type !== 'root'; });
		var grouped = _.groupBy(withoutSubfiles, function(file){ return file.oid_type + file.oid; });
		var temp = [];
		
		_.each(grouped, function(group, key){
			
			group = _.sortBy(group, function(file){ 
				
				if(typeof file.file_name === 'undefined'){
					return '';
				}
				
				return file.file_name.toUpperCase(); 
				
			});
			
			if(!_.isEmpty(_.filter(group, function(item){ return item.oid > 0; }))){
				
				var dispString = '';
				if(!_.isEmpty(objectData)){
					console.log(objectData);
					switch(group[0].oid_type){
						
						case 'client':
						case 'clients':
						var client =  _.where(objectData.clients, {id: group[0].oid})[0];
						if(!_.isEmpty(client) && typeof client.fname !== 'undefined'){
							dispString = client.lname;
						}
						if(!_.isEmpty(client) && typeof client.lname !== 'undefined'){
							dispString += ', '+ client.fname;
						}
						break;
						
						case 'event':
						case 'events':
						dispString = _.where(objectData.events, {id: group[0].oid})[0].event_name;
						break;
						
						case 'staff':
						case 'staff-member':
						var staffMember =  _.where(objectData.staff, {id: group[0].oid})[0];
						if(!_.isEmpty(staffMember) && typeof staffMember.fname !== 'undefined'){
							dispString = staffMember.lname;
						}
						if(!_.isEmpty(staffMember) && typeof staffMember.lname !== 'undefined'){
							dispString += ', '+ staffMember.fname;
						}
						break;
						
						default:
						dispString = key;
						break;
						
					}
					
				}else{
					dispString = key;
				}
				
				temp.push({
					items: _.filter(group, function(item){ return item.oid > 0; }),
					file_name: dispString,
					oid_type: group[0].oid_type,
					oid: group[0].oid
				});
				
			}
			
			var rootItems = _.where(group, {oid: 0});
			_.each(rootItems, function(rootItem){
				
				temp.push(rootItem);
				
			});
			 
		});
		
		grouped = _.groupBy(temp, function(fileGroup){ return fileGroup.oid_type; });
		temp = [];
		_.each(grouped, function(group, key){
			
			group = _.sortBy(group, function(file){ return file.file_name.toUpperCase(); });
			temp.push({
				items: group,
				file_name: key,
				oid_type: group[0].oid_type,
				oid: 0
			});
			
		});
		
		_.each(_.filter(_.where(clone, {oid_type: 'root'}), function(file){ return file.parent < 1; }), function(rootItem){
// 			console.log(rootItem);
			temp.push(rootItem);
			
		});
		
		temp = _.sortBy(temp, function(file){ return file.file_name.toUpperCase(); });
		
		return temp;
		
	}
	
	function highlightSearchPhrase(string){
		
		if(state.searchPhrase !== ''){
			return string.split(state.searchPhrase).join('<strong>'+ state.searchPhrase +'</strong>');
		}else{
			return string;
		}
		
	}
	
	function makeDraggable(selectors){
		
		var temp = [];
		_.each(selectors, function(selector){
			temp.push( document.querySelector(selector));
		});
		
		var dragItems = dragula(temp, {
			
			accepts: function (el, target, source, sibling) {
				
				var temp = _.where(files, {id: parseInt($(target).attr("data-id"))})[0];
				return temp === undefined || temp.file_type === 'folder';
				
			}, 
			
			canMove: function(){
				
				return true;
				
			}, 
			
			moves: function(el, source, handle, sibling){
				
				var temp = _.where(files, {id: parseInt($(el).attr("data-id"))})[0];
				return parseInt($(el).attr("data-id")) > 0 && temp.file_type !== 'folder';
				
			}

		});
		
		dragItems.on('over', function(el, container, source){
			
			$(container).addClass('bg-warning');
			$('.gu-transit').css('display', 'none');
			
		});
		
		dragItems.on('out', function(el, container, source){
			
			$(container).removeClass('bg-warning');
			
		});
		
		dragItems.on('drop', function(el, target, source, sibling){

			var	temp = _.clone(_.where(files, {id: parseInt($(el).attr("data-id"))})[0]),
				to;
			
			if(parseInt($(target).attr("data-id"))){
				
				to = _.where(files, {id: parseInt($(target).attr("data-id"))})[0];
				
			}else if($(target).attr("data-id") === 'directory'){
				
				var tempSelection = _.clone(state.selection);
				
				tempSelection = tempSelection.slice(0, parseInt($(target).attr("data-id2")));
				
				var droppedOnDir = getCurrentDir(groupFiles(files), tempSelection);
				if(typeof droppedOnDir.oid === 'undefined'){
					
					to = {
						oid: 0,
						oid_type: 'root',
						id: 0
					};
					
				}else{
					
					to = {
						oid: droppedOnDir.oid,
						oid_type: droppedOnDir.oid_type
					};
					
					if(typeof droppedOnDir.id !== 'undefined'){
						to.id = droppedOnDir.id;
					}else{
						to.id = 0;
					}
					
				}
				
			}else{
				
				to = {
					id: 0,
					oid: parseInt($(target).attr("data-id2")),
					oid_type: $(target).attr("data-id")
				};
				
			}
				
			temp.parent = to.id;
			temp.oid = to.oid;
			temp.oid_type = to.oid_type;
			
			var current = _.where(files, {id: parseInt($(el).attr("data-id"))})[0];
			if(current.parent != temp.parent && temp.parent > 0 || current.parent != temp.parent && current.parent > 0 || current.oid != temp.oid || current.oid_type != temp.oid_type){
				
				sb.notify({
					type: 'file-nav-item-moved',
					data: {
						file: temp
					}
				});
				
			}else{
				
				refreshDom(domObj.main, groupFiles(files), state, files);
				
			}
				
		});
		
		return dragItems;
		
	}
	
	function prepareFileDetail(container, label, value){
		
		container.makeNode(label.split(' ').join('').replace(':', ''), 'container', {});
		
		container[label.split(' ').join('').replace(':', '')].makeNode('label', 'column', {width: 6, style: 'padding: 4px;'})
			.makeNode('label', 'text', {text: '<strong class="text-muted">'+ label +'</strong>', css: 'pull-right', style: 'padding: 0px; margin: 0px;'});
			
		container[label.split(' ').join('').replace(':', '')].makeNode('text', 'column', {width: 6, style: 'padding: 4px; word-wrap: break-word;'})
			.makeNode('text', 'text', {text: value, css: 'pull-left', style: 'padding: 0px; margin: 0px; word-wrap: break-word;'});
		
	}
	
	function prepareFilePreview(container, file){
		
		container.makeNode('preview', 'container', {style: 'white-space: normal; width: 300px; height: 500px; position: relative; display: inline-block; vertical-align:top; padding: 15px; overflow-y: scroll;'})
			.makeNode('break-1', 'lineBreak', {spaces: 1});
			
// 		container.preview.makeNode('glyph', 'headerText', {text: '<i class="glyphicon glyphicon-file"></i>'});
		
		container.preview.makeNode('view', 'image', {url: sb.data.files.getURL(file), css: '', style: 'border-radius:4px;', width: 250});

		container.preview.makeNode('title', 'headerText', {css: 'text-center', text: file.file_name, size: 'x-small', style: 'word-wrap: break-word;'});
		container.preview.makeNode('break-2', 'text', {text: '<hr>'});
		
		container.preview.makeNode('btns', 'container', {css: 'text-center'})
			.makeNode('open', 'headerText', {text: '<i class="fa fa-download"></i>', css: 'text-info', style: 'display: inline-block;', size: 'small'})
			.notify('click', {
				type: 'open-file-button-clicked',
				data: {
					file: file
				}
			}, sb.moduleId);
			
		/*
container.preview.btns.makeNode('space-1', 'text', {text: ' ', style: 'display: inline-block; width: 10px;'});
		
		container.preview.btns.makeNode('edit', 'headerText', {text: '<i class="glyphicon glyphicon-edit"></i>', css: 'text-warning', style: 'display: inline-block;', size: 'small'});
*/
		
		container.preview.btns.makeNode('space-2', 'text', {text: ' ', style: 'display: inline-block; width: 10px;'});
		
		container.preview.btns.makeNode('link', 'headerText', {text: '<i class="fa fa-link" aria-hidden="true"></i>', css: 'text-info', style: 'display: inline-block;', size: 'small'})
			.notify('click', {
				type: 'get-file-link-button-clicked',
				data: {
					file: file
				}
			}, sb.moduleId);
		
		container.preview.btns.makeNode('space-3', 'text', {text: ' ', style: 'display: inline-block; width: 10px;'});
			
		container.preview.btns.makeNode('delete', 'headerText', {text: '<i class="fa fa-times"></i>', css: 'text-danger', style: 'display: inline-block;', size: 'small'})
			.notify('click', {
				type: 'delete-file-button-clicked',
				data: {
					fileId: file.id
				}
			});
		
		prepareFileDetail(container.preview, 'Date Created:', file.date_created);
		prepareFileDetail(container.preview, 'Type:', file.file_type);
		prepareFileDetail(container.preview, 'Public:', file.is_public);
		
	}
	
	function prepareHeader(container){
		
		if(objectType !== ''){
			
			switch(objectType){
				case 'staff':
				case 'staff-member':
				case 'client':
				case 'clients':
				var title = '<i class="fa fa-folder-open-o fa-lg"></i> '+ objectData[objectType][0].lname +', '+ objectData[objectType][0].fname;
				break;
				
				case 'event':
				case 'events':
				var title = '<i class="fa fa-folder-open-o fa-lg"></i> '+ objectData[objectType][0].event_name;
				break;
				
				default:
				var title = '';
				break;
			}
			
		}else{
			var title = '<i class="fa fa-hdd-o fa-lg"></i> '+ appConfig.instance;
		}
		
		container.makeNode('title', 'column', {width: 12, css: 'text-center'})
			.makeNode('title', 'text', {text: title});
		
		container.makeNode('btns', 'column', {width: 9});
		
		if(permissions.edit){
			
			container.btns.makeNode('btns', 'buttonGroup')
				.makeNode('newFolder', 'button', {text: '<i class="fa fa-folder fa-lg"></i>'})
				.notify('click', {
					type: 'create-new-folder-button-clicked',
					data: {
						modal: domObj.main.modal,
						fileType: 'folder',
						from: 'header'
					}
				}, sb.moduleId);
			
			container.btns.btns.makeNode('uploadButton', 'button', {text: '<i class="fa fa-upload fa-lg"></i>'})
				.notify('click', {
					type: 'create-new-file-button-clicked',
					data: {
						modal: domObj.main.modal,
						fileType: 'file',
						from: 'header'
					}
				}, sb.moduleId);
			
		}
		
		container.makeNode('search', 'column', {width: 3})
			.makeNode('form', 'form', [{
				type: 'text',
				name: 'search',
				placeholder: 'Search...'
			}]);
		
	}
	
	function prepareNav(container, navFiles, displayState, level, modal, fileList){
		
		if(level === 0 && _.isEmpty(navFiles)){
			
			container.makeNode('err', 'text', {text: 'No files to display', css: 'text-center text-muted', style: 'padding: 50px;'});
			
		}else{
			
			prepareNavDir(container, navFiles, displayState, level, modal, fileList);
			
			if(typeof displayState[level] !== 'undefined' && typeof navFiles[displayState[level]] !== 'undefined' && typeof navFiles[displayState[level]].items !== 'undefined' && typeof navFiles[displayState[level]].id === 'undefined'){
				
				prepareNav(container, navFiles[displayState[level]].items, displayState, level + 1, modal, fileList);
				
			}else if(typeof displayState[level] !== 'undefined' && typeof navFiles[displayState[level]] !== 'undefined' && navFiles[displayState[level]].file_type === 'folder'){
				
				navFiles[displayState[level]].items = _.where(fileList, {parent: navFiles[displayState[level]].id});
				prepareNav(container, navFiles[displayState[level]].items, displayState, level + 1, modal, fileList);
				
			}else if(typeof displayState[level] !== 'undefined'){
				
				if(typeof navFiles[displayState[level]] !== 'undefined'){
					
					prepareFilePreview(container, navFiles[displayState[level]]);
					
				}
				
			}
			
		}
		
	}
	
	function prepareNavDir(container, directory, selected, level, modal){
		
		var divStyle = 'min-width: 250px; max-width: 300px; height: 500px; overflow-x: scroll; position: relative; display: inline-block; vertical-align:top; border-right: 1px solid rgb(204, 204, 204);';
		if(objectType !== '' && level < 2){
			divStyle += ' display: none;';
		}
		
		container.makeNode('dir-'+ level, 'fileDrop', {
			
			dataId: 'directory',
			dataId2: level,
			style: divStyle,
			
			receiveFile: function(file){

				sb.notify({
					type: 'file-dropped',
					data: {
						file: file,
						level: level,
						modal: modal
					}
				});
				
			}
			
			}).makeNode('disp', 'table', {
				css: 'table-condensed table-hover',
				columns: {
					'disp': ''
				}, style: 'border: none; z-index: 10;',
				headerStyles: ['border: none;']
			});
			
		dragSelectors.push(container['dir-'+ level].selector);
// 		dragSelectors.push(container['dir-'+ level].disp.body.selector);
		
		_.each(directory, function(item, key){
			
			if(Array.isArray(directory)){
				
				prepareNavItem(container['dir-'+ level].disp, item, item.file_name, selected, level, item.meta_type, key);
				
			}else{
				
				prepareNavItem(container['dir-'+ level].disp, item, key, selected, level, 'folder', key);
				
			}

		});
		
// 		container['dir-'+ level].makeNode('drop', 'container', {style: 'width: 100%; max-height: 100%; min-height: 100px; background-color: blue; bottom: 0;'});
		
	}
	
	function prepareNavFileItem(container, name, itemState, index, item){
		
		var wrap = ['', ''];
		if(item.loading && itemState !== 'selected'){
			wrap = ['<i class="fa fa-circle-o-notch fa-spin fa-fw pull-left text-muted"></i> ', ''];
		}else if(item.loading){
			wrap = ['<i class="fa fa-circle-o-notch fa-spin fa-fw pull-left"></i> ', ''];
		}else if(itemState === 'selected'){
			wrap = ['<i class="fa fa-file-o fa-lg"></i> ', ''];
		}else{
			wrap = ['<i class="fa fa-file fa-lg"></i> ', ''];
		}
		//use .info for upper selected, .primary for current selected
		if(itemState == 'selected'){
			container.makeRow(index, [wrap[0] + highlightSearchPhrase(name) + wrap[1]], {dataId: item.id, css: 'bg-primary', style: 'border: none;', cellStyles: ['border: none;']});
		}else if(itemState == 'selected-muted'){
			container.makeRow(index, [wrap[0] + highlightSearchPhrase(name) + wrap[1]], {dataId: item.id, css: 'bg-info', style: 'border: none;', cellStyles: ['border: none;']});
		}else{
			container.makeRow(index, [wrap[0] + highlightSearchPhrase(name) + wrap[1]], {dataId: item.id, css: '', cellStyles: ['border: none;']});
		}
		
		dragSelectors.push(container.body[index].selector);
		return container.body[index];
		
	}
	
	function prepareNavFolderItem(container, name, itemState, index, item){
		
		var wrap = ['', ''];
		if(item.loading && itemState !== 'selected'){
			wrap = ['<i class="fa fa-circle-o-notch fa-spin fa-fw pull-left text-muted"></i> ', ''];
		}else if(item.loading){
			wrap = ['<i class="fa fa-circle-o-notch fa-spin fa-fw pull-left"></i> ', ''];
		}else if(itemState === 'selected'){
			wrap = ['<i class="fa fa-folder-open fa-lg"></i> ', ''];
		}else if(itemState === 'selected-muted'){
			wrap = ['<i class="fa fa-folder-open-o fa-lg text-info"></i> ', ''];
		}else{
			wrap = ['<i class="fa fa-folder fa-lg text-primary"></i> ', ''];
		}
		
		var dataId, dataId2;
		if(typeof item.id !== 'undefined'){
			dataId = item.id;
			dataId2 = '';
		}else{
			dataId = item.oid_type;
			dataId2 = item.oid;
		}
		
		//use .info for upper selected, .primary for current selected
		if(itemState == 'selected'){
			container.makeRow(index, [wrap[0] + highlightSearchPhrase(name) + wrap[1] + '<i class="fa fa-angle-right fa-lg pull-right"></i>'], {dataId: dataId, dataId2: dataId2, css: 'bg-primary', cellStyles: ['border: none;']});
		}else if(itemState == 'selected-muted'){
			container.makeRow(index, [wrap[0] + highlightSearchPhrase(name) + wrap[1] + '<i class="fa fa-angle-right fa-lg pull-right"></i>'], {dataId: dataId, dataId2: dataId2, css: 'bg-info', cellStyles: ['border: none;']});
		}else{
			container.makeRow(index, [wrap[0] + highlightSearchPhrase(name) + wrap[1] + '<i class="fa fa-angle-right fa-lg pull-right"></i>'], {dataId: dataId, dataId2: dataId2, css: '', cellStyles: ['border: none;']});
		}
		
		dragSelectors.push(container.body[index].selector);
		return container.body[index];
		
	}
	
	function prepareNavItem(container, item, name, selected, level, type, index){
		
		var	itemState = 'not-selected',
			notificationType = 'file-nav-item-selected';
		
		if(selected[level] == index && level + 1 == selected.length){
			
			itemState = 'selected';
			
			if(permissions.edit){
				notificationType = 'file-nav-item-double-clicked';
				notificationEvent = 'dblclick';
			}
			if(mode === 'select'){
				notificationType = 'file-selected';
				notificationEvent = 'dblclick';
			}
			
		}else if(selected[level] == index){
			
			itemState = 'selected-muted';
			
		}
		
		if(typeof item.items !== 'undefined' || item.file_type === 'folder'){
			
			var itemDom = prepareNavFolderItem(container, name, itemState, index, item);
			
			itemDom.notify('click', {
				type: notificationType,
				data: {
					level: level,
					itemName: index,
					type: 'folder',
					item: itemDom,
					container: container,
					file: item
				}
			}, sb.moduleId);
			
		}else{
			
			var itemDom = prepareNavFileItem(container, name, itemState, index, item);
			
			itemDom.notify('click', {
				type: notificationType,
				data: {
					level: level,
					itemName: index,
					type: 'file',
					item: itemDom,
					container: container,
					file: item
				}
			}, sb.moduleId);
			
		}
		
	}
	
	function recordScrollPos(container, selection){
		
		var level = 0, ret = [];
		_.each(selection, function(){
			
			if(typeof container['dir-'+ level] !== 'undefined'){
				ret.push($(container['dir-'+ level].selector).scrollTop());
			}
			level++;
			
		});
		
		return ret;
		
	}
	
	function refreshDom(domObj, groupedFiles, state, fileList){
		
		var scrollPos = recordScrollPos(domObj.nav, state.selection);
		domObj.makeNode('nav', 'container', {width: 12, style: 'width: 100%; height: 500px; display: block; position: relative; overflow-x: auto; white-space: nowrap; border-left: 1px solid rgb(204, 204, 204); border-right: 1px solid rgb(204, 204, 204); border-bottom: 1px solid rgb(204, 204, 204);'});
		prepareNav(domObj.nav, groupedFiles, state.selection, 0, domObj.modal, fileList);
		domObj.nav.patch();
		
		$(domObj.nav.selector).scrollLeft(
			$(domObj.nav.selector).width()
		);
		
		_.each(scrollPos, function(pos, lev){
			
// 			console.log($(domObj.nav['dir-'+ lev].selector));
			if(typeof domObj.nav['dir-'+ lev] !== 'undefined'){
				$(domObj.nav['dir-'+ lev].selector).scrollTop(pos);
			}
			
		});
		
		if(permissions.edit){
			makeDraggable(dragSelectors);
		}
		
	}
			
	return {
		
		init: function(){
			
			sb.listen({
				'start-file-nav': this.start
			});
			
		},
		
		start: function(setup){
			
			sb.listen({
				'create-new-folder-button-clicked': this.getFileUploadForm,
				'create-new-file-button-clicked': this.getFileUploadForm,
				'delete-file-button-clicked': this.deleteFile,
				'get-file-link-button-clicked': this.getLinkModal,
				'start-file-nav': this.start,
				'stop-file-nav': this.destroy,
				'file-dropped': this.getFileUploadForm,
				'file-nav-item-selected': this.selectItem,
				'file-nav-item-moved': this.updateFile,
				'file-nav-item-double-clicked': this.editFileName,
				'nav-key-pressed': this.selectItem,
				'new-file-name-submitted': this.updateFile,
				'open-file-button-clicked': this.openFile,
				'upload-file-button-clicked': this.uploadFile
			});
			
			if(typeof setup.domObj !== 'undefined'){
				domObj = sb.dom.make(setup.domObj.selector);
			}else{
				return;
			}
			if(typeof setup.files !== 'undefined'){
				
				files = setup.files;
				fileTypes = setup.fileTypes;

				if(setup.hasOwnProperty('objectType')){
					
					objectType = setup.objectType;
					objectData[objectType] = [setup.objectData];
					state.selection = [0, 0];
					
				}else if(setup.hasOwnProperty('objectData')){
					
					objectData = {
						events: setup.events,
						clients: setup.clients,
						staff: setup.staff
					};
					
				}
				if(setup.hasOwnProperty('permissions')){
					permissions = setup.permissions;
				}
				
			}else{
				return;
			}
			if(setup.hasOwnProperty('selectMode')){
				mode = 'select';
			}
			
			domObj.makeNode('main', 'container', {style: 'border:1px solid rgb(245, 245, 245); border-radius:4px;'});
			domObj.main.makeNode('modal', 'modal', {});
			domObj.main.makeNode('header', 'container', {style: 'padding: 10px; background-color: rgb(245, 245, 245); width: 100%; display: block; position: relative; overflow-x: auto; white-space: nowrap;'});
			prepareHeader(domObj.main.header);
			
			var groupedFiles = groupFiles(files);

			domObj.main.makeNode('nav', 'container', {style: 'width: 100%; height: 500px; display: block; position: relative; overflow-x: auto; white-space: nowrap;'});
			
			prepareNav(domObj.main.nav, groupedFiles, state.selection, 0, domObj.main.modal, files);
				
			domObj.build();
			
			/*
$(document).keyup(function(e) {
			   
				if(!state.modalOpen){
			       
			       var key = e.which;
			       sb.notify({
				       type: 'nav-key-pressed',
				       data: {
					       key: key
				       }
			       });
			       
				}
				   
			});
		   
			$(document).keydown(function(e) {
			   
				if(!state.modalOpen){
			    	
			    	if(e.which === 37 || e.which === 38 || e.which === 39 || e.which === 40){
				    	
				    	e.preventDefault();
						return false;
				    	
			    	}
			   
				}
			   
			});
*/
			
			$('.modal').on("hidden.bs.modal", function (e) {
				
				state.modalOpen = false;
			
			});
			
			$(domObj.main.header.search.form.search.selector).on('input', function(e){
				
				state.searchPhrase = this.value;
				refreshDom(domObj.main, groupFiles(files), state);
				
			});
			
			$(domObj.main.header.search.form.search.selector).on('focus', function(e){
				
				state.modalOpen = true;
				
			});
			
			$(domObj.main.header.search.form.search.selector).on('focusout', function(e){
				
				state.modalOpen = false;
				
			});
// 			console.log(domObj);
			
			if(permissions.edit){
				makeDraggable(dragSelectors);
			}
			
		},
		
		destroy: function(){
			
// 			$(document).off('keyup');
		   
// 			$(document).off('keydown');
			
			$('.modal').off("hidden.bs.modal");
			
// 			$(domObj.main.header.search.form.search.selector).on('input');
			
// 			$(domObj.main.header.search.form.search.selector).on('focus');
			
// 			$(domObj.main.header.search.form.search.selector).on('focusout');
			
			sb.listen({
				'start-file-nav': this.start
			});
			domObj = {};
			files = [];
			fileTypes = {};
			objectType = '';
			state = {
				selection: [],
				modalOpen: false,
				searchPhrase: ''
			}; 
			objectData = {};
			dragSelectors = [];
			permissions = {
				edit: false
			};
			
		},
		
		deleteFile: function(data){
			
			sb.dom.alerts.ask({
				
				title: 'Are you sure?',
				text: 'This cannot be undone.'
				
			}, function(response){
				
				if(response){
					
					sb.data.files.delete(data.fileId, function(response){
						
						if(response){
							
							sb.dom.alerts.alert('Success!', 'File has been deleted.', 'success');
							files = _.without(files, _.where(files, {id: data.fileId})[0]);
							refreshDom(domObj.main, groupFiles(files), state);
							
						}
						
					});
					
				}
				
			});
				
		},
		
		editFileName: function(data){
			
			if(data.file.id > 0){
			
				data.item.empty();
				data.item.notifications = {};
				data.item.html = 
				
					'<form id="edit-nav-item-file-name-form" onsubmit="return false;">'+
						'<input class="form-control edit-nav-item-file-name-field" style="border-radius: 0px; border: none; background-color: rgb(218, 237, 246);" name="file-name" value="'+ data.file.file_name +'">'+
					'</form>';
					
				data.container.patch();
				state.modalOpen = true;
				
				$('.edit-nav-item-file-name-field').focus();
				$('.edit-nav-item-file-name-field').on('keypress focusout', function(event){
					
					if(event.which == 13 || event.type === 'focusout'){
						
						state.modalOpen = false;
						if(data.file.file_name !== event.currentTarget.value){
							
							sb.notify({
								type: 'new-file-name-submitted',
								data: {
									file: {
										id: data.file.id,
										file_name: event.currentTarget.value
									}
								}
							});
							
						}else{
							
							refreshDom(domObj.main, groupFiles(files), state);
							
						}
					
					}
					
				});
			
			}
			
		},
		
		getFileUploadForm: function(data){
			
			if(!permissions.edit){
				return false;
			}
			
			data.modal.body.empty();
			data.modal.footer.empty();
			
			if(data.hasOwnProperty('level') || data.from === 'header'){
				
				if(data.from === 'header'){
					data.level = state.selection.length;
				}
				
				var	temp = _.clone(state.selection),
					droppedOn = temp.splice(0, data.level),
					dir = getCurrentDir(groupFiles(files), droppedOn);
				
				if(dir != undefined){
					data.objectType = dir.oid_type;
					data.objectId = dir.oid;
				}else{
					data.objectType = 'root';
					data.objectId = 0;
				}
				
				if(dir.file_type === 'folder'){
					data.parent = dir.id;
				}else{
					data.parent = 0;
				}
				
			}
			
			var formParams = {
				fileName: {
					type: 'text',
					name: 'fileName',
					label: 'File Name'
				}, fileType: {
					type: 'select',
					name: 'fileType',
					label: 'Type',
					options: [{
						name: 'Please Select',
						value: 'N/A'
					}]
				}, isPublic: {
					type: 'checkbox',
					name: 'isPublic',
					options: [{
						label: 'Is Public?',
						name: 'isPublic',
						value: 1
					}]
				}, objectType: {
					type: 'hidden',
					name: 'objectType',
					value: data.objectType
				}, objectId: {
					type: 'hidden',
					name: 'objectId',
					value: data.objectId
				}, parent: {
					type: 'hidden',
					name: 'parent',
					value: data.parent
				}
			};
			
			_.each(fileTypes, function(val, key){
				formParams.fileType.options.push({
					name: val,
					value: key
				});
			});
			
			if(data.fileType === 'folder'){
				
				formParams.fileType = {
					type: 'hidden',
					name: 'fileType',
					value: 'folder'
				};
				var title = 'New Folder';
				
			}else{
				
				var title = 'Upload File';
				
			}
			
			var file;
			if(data.hasOwnProperty('file')){
				formParams.fileName.value = data.file[0].name;
				var file = data.file[0];
			}else if(data.fileType !== 'folder'){
				formParams.file = {
					type: 'file-upload',
					name: 'file',
					label: 'Select a file'
				};
			}
			
			data.modal.body.makeNode('title', 'headerText', {text: title, css: 'text-center text-primary'});
			data.modal.body.makeNode('form', 'form', formParams);
			data.modal.body.patch();
			
			data.modal.footer.makeNode('upload', 'button', {text: 'Save', css: 'btn-primary btn-block'})
				.notify('click', {
					type: 'upload-file-button-clicked',
					data: {
						file: file,
						form: data.modal.body.form,
						modal: data.modal
					}
				}, sb.moduleId);
				
			data.modal.footer.patch();
			
			data.modal.show();
			state.modalOpen = true;
			
		},
		
		getLinkModal: function(data){
			
			if(data.file){
				
				domObj.main.modal.body.empty();
				domObj.main.modal.footer.empty();
				
				domObj.main.modal.body.makeNode(
					'getLink',
					'form',
					[{
						type: 'text',
						label: 'Copy the link below',
						value: sb.data.files.getURL(data.file)
					}]
				);
				
				domObj.main.modal.patch();
				domObj.main.modal.show();
				
			}
			
		},
			
		openFile: function(data){
			
			if(data.hasOwnProperty('file')){
				
				sb.data.files.open(data.file);
				
			}
			
		},
		
		selectItem: function(data){
			
			var groupedFiles = groupFiles(files);
			
			if(typeof data.key !== 'undefined'){
				
				switch(data.key){
					
					// up
					case 38:
					if(state.selection[state.selection.length - 1] > 0){
						state.selection[state.selection.length - 1]--;
					}
					break;
					
					// down
					case 40:
					state.selection[state.selection.length - 1]++;
					break;
					
					// left
					case 37:
					if(objectType !== '' && state.selection.length > 2 || objectType === ''){
						state.selection.pop();
					}
					break;
					
					// right
					case 39:
					state.selection.push(0);
					break;
					
				}
				
			}else{
				
				if(objectType === '' || objectType !== '' && data.level > 1){
					
					state.selection[data.level] = data.itemName;
					state.selection.splice(data.level + 1);
					
				}
				
			}
			
			dragSelectors = [];
			refreshDom(domObj.main, groupedFiles, state, files);
			
		},
		
		updateFile: function(data){
			
			// MARK: ZACH CHECK IF FILE NAME IS USED IN DIR HERE TOO
			var	dummy = _.where(files, {id: data.file.id})[0],
				temp = _.filter(files, function(file){ return file.id !== data.file.id; });
				
			_.each(data.file, function(fileDatum, key){
				
				dummy[key] = fileDatum;
				
			});
			dummy.loading = true;
			
			temp.push(dummy);
			refreshDom(domObj.main, groupFiles(temp), state, temp);
			files = temp;
			
			sb.data.files.update({}, data.file, function(response){
				
				temp = _.without(temp, _.where(temp, {id: response.id})[0]);
				temp.push(response);
				refreshDom(domObj.main, groupFiles(temp), state, temp);
				files = temp;
				
				// if updated file type is a folder, update the locations of any child files
				if(response.file_type === 'folder'){
					
					var childFiles = _.where(files, {parent: response.id});
					_.each(childFiles, function(childFile){
						
						files[_.findIndex(files, {id: childFile.id})].loc = files[_.findIndex(files, {id: childFile.id})].loc.replace(dummy.loc, response.loc);
						
					});
					
				}
				
			});
			
		},
		
		uploadFile: function(data){
			
			console.log(data.form.process());
// 			return true;
			if(!permissions.edit){
				return false;
			}
			
			var currentDir = getCurrentDir(groupFiles(files), state.selection);
			var	formData = data.form.process();
			var temp = _.clone(files);
			
			if(!data.file && formData.fields.fileType.value !== 'folder'){
				data.file = $(data.form.file.selector)[0].files[0];
			}
			
			if(typeof formData.fields.fileName !== 'undefined' && typeof _.where(currentDir.items, {file_name: formData.fields.fileName.value})[0] !== 'undefined'){
				
				sb.dom.alerts.alert('Sorry!', 'A file with that name already exists in this directory--please rename your file.', 'warning');
				return false;
				
			}else if(formData.fields.fileName.value === '' && formData.fields.fileType.value !== 'folder' && typeof _.where(currentDir.items, {file_name: data.file.name})[0] !== 'undefined' || formData.fields.fileName.value === '' && formData.fields.fileType.value !== 'folder' && typeof _.where(currentDir.items, {file_name: data.file.name.split('.')[0]})[0] !== 'undefined'){
				
				sb.dom.alerts.alert('Sorry!', 'A file with that name already exists in this directory--please rename your file.', 'warning');
				return false;
				
			}
			
			data.modal.hide();
			
			temp.push({
				id: 0,
				oid: parseInt(formData.fields.objectId.value),
				oid_type: formData.fields.objectType.value,
				file_name: formData.fields.fileName.value,
				file_type: formData.fields.fileType.value,
				parent: parseInt(formData.fields.parent.value),
				is_public: 0,
				date_created: moment().format('YYYY-MM-dd H:m:s'),
				loading: true
			});

			refreshDom(domObj.main, groupFiles(temp), state, temp);
			files = temp;
			
			var fileMetaData = {
				fileType: formData.fields.fileType.value,
				fileName: formData.fields.fileName.value,
				objectType: formData.fields.objectType.value,
				objectId: parseInt(formData.fields.objectId.value),
				parent: parseInt(formData.fields.parent.value)
			};
			
			if(formData.fields.hasOwnProperty('isPublic')){
				fileMetaData.isPublic = 1;
			}else{
				fileMetaData.isPublic = 0;
			}
			
			console.log(fileMetaData);
			
			if(fileMetaData.objectType === ''){
				fileMetaData.objectType = 'root';
			}
			if(isNaN(fileMetaData.objectId)){
				fileMetaData.objectId = 0;
			}

			sb.data.files.upload(data.file, fileMetaData, function(response){
				
				temp = _.without(temp, _.where(temp, {
					oid: parseInt(formData.fields.objectId.value),
					oid_type: formData.fields.objectType.value,
					file_name: formData.fields.fileName.value,
					file_type: formData.fields.fileType.value,
					parent: parseInt(formData.fields.parent.value),
					loading: true
				})[0]);
				
				if(response){
					
					temp.push(response);
					refreshDom(domObj.main, groupFiles(temp), state, temp);
					files = temp;
					
				}else{
					
					sb.dom.alerts.alert('Sorry!', 'An error occurred--please refresh and try again.', 'error');
					
				}
				
			});
			
		}
		
	}
	
});