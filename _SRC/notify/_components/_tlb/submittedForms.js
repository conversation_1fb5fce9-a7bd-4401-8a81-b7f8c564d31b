Factory.register('submittedForms', function(sb){
	
	var domObj = {},
		components = {};
				
	return {
		
		init: function(){
			
			sb.listen({
				'confirm-submitted-form':this.confirm,
				'confirm-submitted-forms':this.confirmMulti,
				'submittedForms-run':this.run,
				'view-submitted-form':this.view,
				'view-submitted-forms-table':this.showTable
			});
			
/*
			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'submittedForms',
						title:'Submitted Forms',
						icon:'<i class="fa fa-users"></i>',
						views:[
							{
								id:'table',
								type:'table',
								title:'Request Form Submissions',
								icon:'<i class="fa fa-th-list"></i>',
								default:true,
								setup:{
									objectType:'submitted_form',
									childObjs:2,
									searchObjects:[
										{
											name:'Verification Code',
											value:'confirmation_code'
										},
										{
											name:'Email Address',
											value:'email'
										},
										{
											name:'Cell Phone',
											value:'phone'
										}
										
									],
									filters:false,
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										}
									},
									multiSelectButtons:{
										confirm:{
											name:'<i class="fa fa-times"></i> Confirm',
											css:'pda-btn-green',
											domType:'modal',
											action:function(selectedObjs, dom){
												
												dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
												
												dom.body.patch();
												
												setTimeout(function(){
																						
													dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Confirm '+ selectedObjs.length + ' request(s)?'});
																								
													dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
														type:'confirm-submitted-forms',
														data:{
															modal:dom,
															objects:selectedObjs
														}
													}, sb.moduleId);
																						
													dom.body.patch();
													dom.footer.patch();
																								
												}, 1000);
												
											}
										},
									},
									rowSelection:true,
									rowLink:false,
									visibleCols:{
										date_created:'Date Created',
										confirmed:'Confirmed',
										source:'Source',
										confirmation_code:'Code',
										books:'Books',
										phone:'Phone',
										email:'Email'
									},
									cells: {
										date_created: function(obj){
											return moment(obj.date_created).format('M/D/YYYY hh:mm a');
										},
										source: function(obj){
											return obj.form_data.request_source;
										},
										books: function(obj){
											return obj.form_data.amount_of_books;
										}
									},
									data: function(paged, callback){
																	
										sb.data.db.obj.getAll('submitted_form', function(objs){
																					
											callback(objs);
											
										}, 1, paged);
									
									}
								}
							},
							{
								id:'preorder',
								type:'table',
								title:'Preorders',
								icon:'<i class="fa fa-th-list"></i>',
								setup:{
									objectType:'submitted_form',
									childObjs:2,
									searchObjects:[
										{
											name:'Verification Code',
											value:'confirmation_code'
										},
										{
											name:'Email Address',
											value:'email'
										},
										{
											name:'Cell Phone',
											value:'phone'
										}
										
									],
									filters:false,
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										}
									},
									multiSelectButtons:{
										confirm:{
											name:'<i class="fa fa-times"></i> Confirm',
											css:'pda-btn-green',
											domType:'modal',
											action:function(selectedObjs, dom){
												
												dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
												
												dom.body.patch();
												
												setTimeout(function(){
																						
													dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Confirm '+ selectedObjs.length + ' request(s)?'});
																								
													dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
														type:'confirm-submitted-forms',
														data:{
															modal:dom,
															objects:selectedObjs
														}
													}, sb.moduleId);
																						
													dom.body.patch();
													dom.footer.patch();
																								
												}, 1000);
												
											}
										},
									},
									rowSelection:true,
									rowLink:false,
									visibleCols:{
										date_created:'Date Created',
										confirmed:'Confirmed',
										source:'Source',
										confirmation_code:'Code',
										books:'Books',
										phone:'Phone',
										email:'Email'
									},
									cells: {
										date_created: function(obj){
											return moment(obj.date_created).format('M/D/YYYY hh:mm a');
										},
										source: function(obj){
											return obj.form_data.request_source;
										},
										books: function(obj){
											return obj.form_data.amount_of_books;
										}
									},
									data: function(paged, callback){
																	
										sb.data.db.obj.getWhere('submitted_form', {confirmed:'yes', type:'preorder', paged:paged, childObjs:1}, function(objs){
																					
											callback(objs);
											
										}, 1, paged);
									
									}
								}
							},
							{
								id:'preorderMetrics',
								type:'custom',
								title:'Preorder Stats',
								icon:'<i class="fa fa-th-list"></i>',
								dom:function(dom, state, draw){
									
									var variables = {
											bps:0,
											totalSize:0
										};
									
									function calculateOrderValues(dom, callback){
										
										function calculate(paged, callback, ret){
											
											if(!ret){
												ret = {
													affected:0,
													newTotal:0
												};
											}
											
											sb.data.db.obj.getWhere('submitted_form', {confirmed:'yes', type:'preorder', paged:paged}, function(data){
console.log('calc preorders', data);												
												if(data.constructor === Array){
console.log('callback', data.constructor === Array);												
													callback(ret);
												
												}else{
													
													if(data.data.length > 0){
													
														_.each(data.data, function(req){
															
															ret.newTotal += req.books;
															
															if(variables.bps > 0){
																
																var reqBPS = req.books / req.students;
console.log('calculated bps', reqBPS);
console.log('variable bps', variables.bps);																	
																if(reqBPS > variables.bps){
																	
																	var reductionAmount = 100;
																	
																	while(reqBPS > variables.bps){
																		
																		reductionAmount += 100;
																		
																		req.books -= reductionAmount;
																		
																		reqBPS = req.books / req.students;
																		
																	}
																	
																	ret.affected++;
																	ret.newTotal -= reductionAmount;
																	
																}
																
															}
															
															if(variables.totalSize > 0){
																
																if(req.books > variables.totalSize){
																																	
																	ret.newTotal -= (req.books - variables.totalSize);
																	ret.affected++;
																	
																}
																
															}
															
														});
														
														paged.page = paged.page + paged.pageLength;
														
														calculate(paged, callback, ret);
														
													}else{
														
														callback(ret);
														
													}
													
												}
												
											});
											
										}
										
										var paged = {
												count:true,
												page:0,
												pageLength:50,
												paged:true,
												sortCol:'date_created',
												sortDir:'desc',
												sortCast:'string'
											};
										
										sb.data.db.obj.getWhere('submitted_form', {type:'preorder', paged:paged, confirmed:'yes'}, function(data){
																						
											delete dom.loader;
											
											dom.makeNode('total', 'text', {text:'Total Requests: '+ data.recordsTotal, size:'small'});
											
											dom.makeNode('loading', 'text', {text:'Calculating new preorder totals...'});
											
											dom.makeNode('loader', 'loader', {css:'pull-left'});
											
											dom.patch();
											
											calculate(paged, function(done){
												
												delete dom.loading;
												delete dom.loader;
												
												dom.makeNode('affected', 'text', {text:'Affected orders: '+ done.affected});
												dom.makeNode('newTotal', 'text', {text:'New total: '+ done.newTotal});
												
												dom.patch();
												
												callback(done);
												
											});
											
										});
										
									}
									
									dom.makeNode('modals', 'container', {});
									
									dom.makeNode('btns', 'buttonGroup', {css:'pull-right'});
									
									dom.makeNode('title', 'headerText', {text:'Preorder Stats'});
									
									dom.makeNode('titleBreak', 'lineBreak', {});
									
									dom.makeNode('stats', 'container', {css:'text-center'});
																		
									dom.stats.makeNode('stat1', 'column', {width:4, css:''})
										.makeNode('loader', 'loader', {});

									dom.stats.makeNode('stat2', 'column', {width:4, css:''})
										.makeNode('loader', 'loader', {});									

									dom.stats.makeNode('stat3', 'column', {width:4, css:''})
										.makeNode('loader', 'loader', {});									
									
									draw({
										dom:dom,
										after:function(dom){
											
											sb.data.db.obj.getSum('submitted_form', 'books', {
												groupBy:'year',
												type:'preorder',
												confirmed:'yes',
												dateRange:{
													start: moment('2018-05-31 11:59:59', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS'),
													end: moment('2019-06-01 01:00:01', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS')
												}
											}, function(totalBookData){
											
												delete dom.stats.stat1.loader;
												delete dom.stats.stat2.loader;
												delete dom.stats.stat3.loader;

												dom.stats.stat1.makeNode('value', 'headerText', {text:totalBookData[0].grouped_total, size:'small'});
												dom.stats.stat1.makeNode('label', 'headerText', {text:'Preorders', size:'x-small'});
												
												dom.stats.stat2.makeNode('value', 'headerText', {text:totalBookData[0].sum, size:'small'});
												dom.stats.stat2.makeNode('label', 'headerText', {text:'Books', size:'x-small'});
												
												dom.stats.stat3.makeNode('value', 'headerText', {text:Math.round(totalBookData[0].sum / totalBookData[0].grouped_total), size:'small'});
												dom.stats.stat3.makeNode('label', 'headerText', {text:'Books Per Order', size:'x-small'});
												
												dom.stats.makeNode('btnBreak', 'lineBreak', {spaces:2});
												
												dom.btns.makeNode('bps', 'button', {text:'Modify BPS Breakpoint', css:''})
													.notify('click', {
														type:'submittedForms-run',
														data:{
															run:function(dom){
																
																dom.modals.makeNode('modal', 'modal', {});
																
																dom.modals.modal.body.makeNode('form', 'form', {
																	breakpoint:{
																		name:'breakpoint',
																		label:'Enter Breakpoint Value',
																		type:'number',
																		value:0
																	}
																});
																
																dom.modals.modal.body.makeNode('save', 'button', {text:'Save and calculate', css:'pda-btn-green'})
																	.notify('click', {
																		type:'submittedForms-run',
																		data:{
																			run:function(dom){
																				
																				dom.modals.modal.footer.makeNode('loader', 'loader', {});
																				
																				dom.modals.modal.footer.patch();
																				
																				variables.bps = dom.modals.modal.body.form.process().fields.breakpoint.value;

																				calculateOrderValues(dom.modals.modal.footer, function(done){
																																										
console.log('done', done, totalBookData[0].sum);
																					dom.modals.modal.footer.makeNode('diff', 'text', {text:'Difference: <span class="">('+ (totalBookData[0].sum - done.newTotal) +')</span>'});
																					
																					dom.modals.modal.footer.patch();
																					
																				});
																				
																			}.bind({}, dom)
																		}
																	}, sb.moduleId);
																
																dom.modals.patch();
																
																dom.modals.modal.show();
																
															}.bind({}, dom)
														}
													}, sb.moduleId);;
												
												dom.btns.makeNode('submit', 'button', {text:'Submit Preorders', css:'pda-btn-green'})
													.notify('click', {
														type:'submittedForms-run',
														data:{
															run:function(){
																
																sb.dom.alerts.ask({
																	title: 'Are you sure?',
																	text: ''
																}, function(resp){
																	
																	if(resp){
																	
																		sb.dom.alerts.alert('nice!', 'you did it!', 'success');
																		console.log('you did it!');
																	
																	}
																	
																});
																
															}.bind()
														}
													}, sb.moduleId);
																							
												dom.patch();
												
											});
											
										}
									});
																		
								}
							}
						]
					}
				}
			});
*/
			
			if(appConfig.instance == 'thelifebook' || appConfig.instance == 'rickyvoltz'){
				
				sb.notify({
					type:'register-application',
					data:{
						navigationItem:{
							id:'submittedForms',
							title:'Submitted Forms',
							icon:'<i class="fa fa-book"></i>',
							views:[
								{
									id:'submittedForms',
									type:'hqTool',
									name:'Submitted Forms',
									tip:'View and manage your all requests.',
									icon: {
										type: 'book',
										color: 'blue'
									},
									default:true,
									settings: [
										{
											object_type:'incoming_form_source_vendor',
											name:'1. Form Source Vendors'
										},
										{
											object_type:'incoming_form_sources',
											name:'2. Form Sources'
										}
									],
									mainViews:[
										{
											dom:function(dom, state, draw, mainDom){
	
												sb.notify({
													type:'show-collection',
													data:{
														actions:{
															convert: {
																id: 'convert',
																icon: 'check',
																color: 'green',
																action: function(data, ui, onComplete){																	
																	
																	var selectedObjs = [];
																	
																	if(_.isArray(data)){
																		selectedObjs = data;
																	}else{
																		selectedObjs.push(data);
																	}
																	
																	ui.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Confirm '+ selectedObjs.length + ' request(s)?'});
																													
																	ui.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
																		type:'confirm-submitted-forms',
																		data:{
																			modal:dom,
																			objects:selectedObjs,
																			callback:onComplete
																		}
																	}, sb.moduleId);
																										
																	ui.patch();
																
																},
																title:'Convert to Request',
																headerAction: true,
																singleAction: true
															}
														},
														domObj:dom,
														layer: 'hq',
														fields:{
															date_created:{
																title:'Requested On'
															},
															confirmed:{
																title:'Confirmed'
															},
															source:{
																title:'Source',
																view:function(ui, obj){
																	
																	var sourceString = '';
																	if(obj.form_data){
																		sourceString = obj.form_data.request_source;
																	}
																	
																	ui.makeNode('text', 'div', {text:sourceString});
																	
																	return ui;
																	
																}
															},
															confirmation_code:{
																title:'Confirmation Code',
																isSearchable:true
															},
															books:{
																title:'Books',
																view:function(ui, obj){
																	
																	var bookString = '';
																	if(obj.form_data){
																		bookString = obj.form_data.amount_of_books;
																	}
																	
																	ui.makeNode('text', 'div', {text:bookString});
																	
																	return ui;
																	
																}
															},
															email:{
																title:'Email',
																isSearchable:true
															},
															phone:{
																title:'Phone',
																isSearchable:true
															}
														},
														fullView:{
															type:'teamTool',
															id:'contactTool'
														},
														groupings:{
															type:'Type'
														},
														objectType:'submitted_form',
														singleView:{
															view:function(ui, obj, draw){
																
																var state = {
																	object:obj,
																	edit:false,
																	onSave:draw,
																	onDelete:function(){
	
																		sb.notify({
																			type:'app-navigate-to',
																			data:{
																				type:'UP'
																			}
																		});
																		
																	}
																};														
																
																//singleState(obj, ui, state, draw);
															},
															select:3
														},
														state:state,
														templates: false,
														where:{
															childObjs:2
														}
													}
												});
																						
											}
										}
									]
								}
							]
						}
					}
				});			
				
			}
			
		},
		
		destroy: function(){
			
			_.each(components, function(comp){
				comp.destroy();
			});
			
			domObj = {},
			components = {};
			
		},
		
		confirm: function(data){
			
			data.object.confirmed = 'yes';
			
			sb.data.db.obj.update('submitted_forms', {id:data.object.id, confirmed:'yes'}, function(formObj){

				components.req = sb.createComponent('requestsComponent');
				components.req.notify({
					type:'create-new-request-from-form',
					data:{
						form:formObj
					}
				});
				
			});
			
		},
		
		confirmMulti: function(data){
			
			var count = 0;
			
			components.req = sb.createComponent('requestsComponent');
			
			_.each(data.objects, function(obj){
				
				obj.confirmed = 'yes';
			
				sb.data.db.obj.update('submitted_forms', {id:obj.id, confirmed:'yes'}, function(formObj){
					
					count++;
					
					components.req.notify({
							type:'create-new-request-from-form',
							data:{
								form:formObj
							}
						});
					
				});
				
				if(count == data.objects.length && data.callback){
					data.callback(true);
				}
				
			});
			
		},
		
		run: function(data){
			
			data.run();
			
		},
		
		showTable: function(data){
			
			if(_.isEmpty(domObj)){
				domObj = sb.dom.make(data.domObj.selector);
			}
			
			domObj.makeNode('tableContainer', 'container', {});
			domObj.makeNode('modals', 'container', {});
			
			domObj.build();
			
			components.table = sb.createComponent('crud-table');
			components.table.notify({
				type: 'show-table',
				data: {							
					domObj:domObj.tableContainer,
					objectType:'submitted_form',
					childObjs:2,
					searchObjects:[
						{
							name:'Verification Code',
							value:'confirmation_code'
						},
						{
							name:'Email Address',
							value:'email'
						},
						{
							name:'Cell Phone',
							value:'phone'
						}
						
					],
					filters:false,
					headerButtons:{
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							action:function(){}
						}
/*
						checkConfirm:{
							name:'<i class="fa fa-times"></i> Check Confirmation Status',
							css:'pda-btn-green',
							domType:'full',
							action:function(selectedObjs, dom){
								
								dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
								
								dom.body.patch();
								
								sb.data.db.obj.getAll('submitted_form', function(forms){
									
									dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Confirm '+ forms.length + ' request(s)?'});
																		
									dom.body.patch();
									
									function check(objs, callback, count, total){
										
										if(!count){
											count = 0;
										}
										
										if(!total){
											total = 0;
										}
										
										sb.data.db.obj.getWhere('contact_info', {info:objs[count].email}, function(ret){
											
											count++;
											
											if(ret.length > 0){
												total++;
											}
											
											if(objs[count]){
console.log(count, total);												
												check(objs, callback, count, total);
												
											}else{
												
												callback(total);
												
											}
											
										});
										
									}
									
									check(forms, function(created){
										
console.log(created);										
										
									});
									
									
									
								});
																										
							}
						}
*/
					},
					multiSelectButtons:{
						confirm:{
							name:'<i class="fa fa-times"></i> Confirm',
							css:'pda-btn-green',
							domType:'modal',
							action:function(selectedObjs, dom){
								
								dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
								
								dom.body.patch();
								
								setTimeout(function(){
																		
									dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Confirm '+ selectedObjs.length + ' request(s)?'});
																				
									dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
										type:'confirm-submitted-forms',
										data:{
											modal:dom,
											objects:selectedObjs
										}
									}, sb.moduleId);
																		
									dom.body.patch();
									dom.footer.patch();
																				
								}, 1000);
								
							}
						},
					},
					rowSelection:true,
					rowLink:false,
					visibleCols:{
						date_created:'Date Created',
						confirmed:'Confirmed',
						source:'Source',
						confirmation_code:'Code',
						books:'Books',
						phone:'Phone',
						email:'Email'
					},
					cells: {
						date_created: function(obj){
							return moment(obj.date_created).format('M/D/YYYY hh:mm a');
						},
						source: function(obj){
							return obj.form_data.request_source;
						},
						books: function(obj){
							return obj.form_data.amount_of_books;
						}
					},
					data: function(paged, callback){
													
						sb.data.db.obj.getAll('submitted_form', function(objs){
																	
							callback(objs);
							
						}, 1, paged);
					
					},
					
				}
			});
			
/*
			components.table = sb.createComponent('crudPaged');
			components.table.notify({
				type: 'display-crud-table-paged',
				data: {
					domObj: domObj.tableContainer,
					objectType:'submitted_form',
					visibleCols: ['date_created', 'email', 'phone', 'confirmation_code', 'confirmed'],
					data: function(callback, paged){
													
						sb.data.db.obj.getAll('submitted_form', function(objs){
																	
							callback(objs);
							
						}, 1, paged);
					
					},
					cells: {
						date_created:function(obj){
							return moment(obj.date_created).format('M/D/YYYY hh:mm a');
						}
					},
					cols: {
						object_uid:'ID',
						date_created:'Date Created'
					},
					buttons: {
						create: false,
						view:{
							type:'view-submitted-form',
							data:{}
						},
						create_request: function(obj){
							
							return {
									type:'confirm-submitted-form',
									data:{}
								};
							
						},
						edit: false,
						erase: true,
						duplicate:false,
						reload:true
					}
				}
			});
*/
			
		},
		
		view: function(data){
						
			domObj.modals.makeNode('modal', 'modal', {});
			
			domObj.modals.modal.body.makeNode('header', 'headerText', {text:'Submitted Form', size:'small'});
			
			_.each(data.object.form_data, function(v, k){
				
				domObj.modals.modal.body.makeNode('info-'+k, 'text', {text:k.toUpperCase() +': '+ v});
				
			});
			
			domObj.modals.patch();
			domObj.modals.modal.show();
			
		}
									
	}
	
});