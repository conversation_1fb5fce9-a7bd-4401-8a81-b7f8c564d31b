Factory.register("tlbSMS", function (sb) {
  var comps = {};

  function testSettings(state, callback) {
    if (!state.appSettings.apps.tlbSMS.tgiThanks) {
      state.appSettings.apps.tlbSMS = {};
      state.appSettings.apps.tlbSMS.tgiThanks = {
        currentOffset: 0,
      };
      state.appSettings.save(function (done) {
        callback(state);
      });
    } else {
      callback(state);
    }
  }

  function stories(dom, state, draw) {
    function sendTGIThanks(dom, state, callback, sent) {
      function sendSMS(dom, offset, total, totalSent, callback, timing, count) {
        if (!timing) {
          timing = {
            total: offset,
            totalSeconds: 0,
            avg: 0,
          };
        }

        if (!count) {
          count = 0;
        }

        /*
				if(offset == 1){
					throw new Error();
				}
*/

        var startTimer = moment();
        console.log("current count:", count);
        if (offset >= total || count == 200) {
          var percentComplete = 95;

          dom.makeNode("progress", "text", {
            text:
              '<div class="progress">' +
              '<div class="progress-bar progress-bar-info progress-bar-stripe active" role="progressbar" aria-valuenow="' +
              Math.round(percentComplete) +
              '" aria-valuemin="0" aria-valuemax="100" style="width: ' +
              Math.round(percentComplete) +
              '%;">' +
              "" +
              Math.round(percentComplete) +
              "%" +
              "</div>" +
              "</div>",
          });

          dom.patch();

          callback(true);
        } else {
          sb.data.db.obj.getWhere(
            "requests",
            {
              shipped: "yes",
              date_created: {
                type: "between",
                start: moment("2018-01-01", "YYYY-MM-DD").unix(),
                end: moment("2018-03-01").unix(),
              },
              paged: {
                count: true,
                page: offset,
                pageLength: 1,
                paged: true,
                sortCol: "date_created",
                sortDir: "desc",
                sortCast: "string",
              },
            },
            function (reqs) {
              sb.data.db.obj.getWhere(
                "contact_info",
                {
                  object_id: reqs.data[0].contact,
                  is_primary: "yes",
                  childObjs: {
                    info: {},
                    type: {
                      data_type: {},
                    },
                  },
                },
                function (info) {
                  var phone = "";
                  var email = "";

                  _.each(info, function (i) {
                    console.log(i);
                    if (i.type.id == 49) {
                      phone = i.info;
                    }

                    if (i.type.data_type == "email") {
                      email = i.info;
                    }
                  });

                  var smsObj = {
                    to: phone,
                    message:
                      "Hey, this is The Life Book – will you do us a quick favor & share how God used The Life Book to impact lives? Just reply to this text. Thanks!",
                    from: "+16155024897",
                  };

                  sb.comm.sendSMS(smsObj, function (done) {
                    sb.data.db.controller(
                      "mailchimpSendAPIEmail",
                      {
                        email: email,
                        workflowId: "76f057a66d",
                        emailId: "ddfdce533b",
                      },
                      function (done) {
                        totalSent = totalSent + 1;

                        var percentComplete = (totalSent / total) * 100;

                        dom.makeNode("progress", "text", {
                          text:
                            '<div class="progress">' +
                            '<div class="progress-bar progress-bar-info progress-bar-stripe active" role="progressbar" aria-valuenow="' +
                            Math.round(percentComplete) +
                            '" aria-valuemin="0" aria-valuemax="100" style="width: ' +
                            Math.round(percentComplete) +
                            '%;">' +
                            "" +
                            Math.round(percentComplete) +
                            "%" +
                            "</div>" +
                            "</div>",
                        });

                        dom.makeNode("sent", "headerText", {
                          text: "Total sent: " + totalSent,
                          size: "small",
                        });

                        dom.patch();

                        setTimeout(function () {
                          offset = offset + 1;

                          state.appSettings.apps.tlbSMS.tgiThanks.currentOffset =
                            offset;

                          state.appSettings.save(function () {
                            timing.total++;
                            count++;

                            timing.totalSeconds += +moment().diff(
                              startTimer,
                              "s"
                            );
                            timing.avg = (
                              timing.totalSeconds / timing.total
                            ).toFixed(2);

                            var totalTime = (timing.totalSeconds / 60).toFixed(
                              3
                            );

                            if (totalTime > 0.5) {
                              totalTime = (timing.totalSeconds / 60).toFixed(1);
                            }

                            dom.makeNode("avg", "headerText", {
                              text:
                                "Averge send time: " + timing.avg + " seconds",
                              size: "x-small",
                            });
                            dom.makeNode("estRun", "headerText", {
                              text:
                                "Est. run time: " +
                                (timing.avg * total) / 60 +
                                " minutes",
                              size: "x-small",
                            });
                            dom.makeNode("estTimeToComp", "headerText", {
                              text:
                                "Est. time to complete: " +
                                (timing.avg * (total - timing.total)).toFixed(
                                  2
                                ) +
                                " seconds",
                              size: "x-small",
                            });

                            dom.patch();

                            return sendSMS(
                              dom,
                              offset,
                              total,
                              totalSent,
                              callback,
                              timing,
                              count
                            );
                          });
                        }, 1000);
                      }
                    );
                  });
                }
              );
            }
          );
        }
      }

      dom.css("pda-background-gray pda-container");

      dom.empty();

      dom.makeNode("state", "headerText", {
        text: "Preparing...",
        size: "small",
      });

      dom.patch();

      sb.data.db.obj.getWhere(
        "requests",
        {
          shipped: "yes",
          date_created: {
            type: "between",
            start: moment("2018-01-01", "YYYY-MM-DD").unix(),
            end: moment("2018-03-01").unix(),
          },
          paged: {
            count: true,
            page: +state.appSettings.apps.tlbSMS.tgiThanks.currentOffset,
            pageLength: 1,
            paged: true,
            sortCol: "date_created",
            sortDir: "desc",
            sortCast: "string",
          },
        },
        function (reqs) {
          dom.makeNode("state", "headerText", {
            text: "Total list size: " + reqs.recordsTotal,
            size: "small",
          });

          var percentComplete = (sent / reqs.recordsTotal) * 100;

          dom.makeNode("progress", "text", {
            text:
              '<div class="progress">' +
              '<div class="progress-bar" role="progressbar" aria-valuenow="' +
              percentComplete +
              '" aria-valuemin="0" aria-valuemax="100" style="width: ' +
              percentComplete +
              '%;">' +
              "" +
              percentComplete +
              "%" +
              "</div>" +
              "</div>",
          });

          dom.patch();

          sendSMS(
            dom,
            +state.appSettings.apps.tlbSMS.tgiThanks.currentOffset,
            reqs.recordsTotal,
            sent,
            function (done) {
              dom.makeNode("progress", "text", {
                text:
                  '<div class="progress">' +
                  '<div class="progress-bar progress-bar-success progress-bar-stripe" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%;">' +
                  "100%" +
                  "</div>" +
                  "</div>",
              });

              dom.patch();

              callback(true);
            }
          );
        }
      );
    }

    testSettings(state, function (newState) {
      var sent = +state.appSettings.apps.tlbSMS.tgiThanks.currentOffset;

      state = newState;

      var settings = state.appSettings.apps.tlbSMS.tgiThanks;

      dom.empty();

      dom.makeNode("modals", "container", {});

      dom.makeNode("title", "headerText", { text: "Stories Campaign" });

      dom.makeNode("titleBreak", "lineBreak", {});

      dom.makeNode("offset", "headerText", {
        text: "Current Offset: " + settings.currentOffset,
        size: "x-small",
      });

      dom
        .makeNode("changeOffset", "button", {
          text: '<i class="fa fa-pencil"></i> Change Offset',
          css: "pda-btn-orange",
        })
        .notify(
          "click",
          {
            type: "tlbSMS-run",
            data: {
              run: function (dom, state, draw) {
                dom.modals.makeNode("modal", "modal", {
                  onShow: function () {
                    var modal = dom.modals.modal;

                    modal.body.makeNode("title", "headerText", {
                      text: "Change the current offset",
                      size: "small",
                    });

                    modal.body.makeNode("form", "form", {
                      offset: {
                        name: "offset",
                        label: "New Offset:",
                        type: "number",
                        value:
                          state.appSettings.apps.tlbSMS.tgiThanks.currentOffset,
                      },
                    });

                    modal.footer
                      .makeNode("save", "button", {
                        text: '<i class="fa fa-check"></i> Save',
                        css: "pda-btn-green",
                      })
                      .notify(
                        "click",
                        {
                          type: "tlbSMS-run",
                          data: {
                            run: function (dom, state, draw) {
                              modal.footer.save.loading();

                              var newOffset =
                                +modal.body.form.process().fields.offset.value;

                              state.appSettings.apps.tlbSMS.tgiThanks.currentOffset =
                                newOffset;

                              state.appSettings.save(function (done) {
                                modal.hide();
                              });
                            }.bind({}, dom, state, draw),
                          },
                        },
                        sb.moduleId
                      );

                    modal.body.patch();
                    modal.footer.patch();
                  },
                  onClose: function () {
                    sb.notify({
                      type: "app-navigate-to",
                      data: {
                        itemId: "tlbSMS",
                        viewId: "tgi-thanks",
                      },
                    });
                  },
                });

                dom.modals.patch();

                dom.modals.modal.show();
              }.bind({}, dom, state, draw),
            },
          },
          sb.moduleId
        );

      dom.makeNode("formBreak", "lineBreak", { spaces: 2 });

      dom
        .makeNode("send", "button", {
          text: '<i class="fa fa-envelope"></i> Send the campaign',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "tlbSMS-run",
            data: {
              run: function (dom, state, draw) {
                sb.dom.alerts.ask(
                  {
                    text: "Are you sure?",
                    title: "",
                  },
                  function (resp) {
                    if (resp) {
                      swal.close();

                      sendTGIThanks(
                        dom.sending,
                        state,
                        function (done) {
                          sb.dom.alerts.alert("Done sending!", "", "success");
                        },
                        sent
                      );
                    }
                  }
                );
              }.bind({}, dom, state, draw),
            },
          },
          sb.moduleId
        );

      dom.makeNode("sendingBreak", "lineBreak", { spaces: 2 });

      dom.makeNode("sending", "container", { css: "" });

      dom.patch();

      draw(false);
    });
  }

  function tgiThanks(dom, state, draw) {
    function sendTGIThanks(dom, state, callback, sent) {
      function sendSMS(dom, offset, total, totalSent, callback, timing) {
        if (!timing) {
          timing = {
            total: offset,
            totalSeconds: 0,
            avg: 0,
          };
        }

        /*
				if(offset == 1){
					throw new Error();
				}
*/

        var startTimer = moment();

        if (offset >= total) {
          var percentComplete = 95;

          dom.makeNode("progress", "text", {
            text:
              '<div class="progress">' +
              '<div class="progress-bar progress-bar-info progress-bar-stripe active" role="progressbar" aria-valuenow="' +
              Math.round(percentComplete) +
              '" aria-valuemin="0" aria-valuemax="100" style="width: ' +
              Math.round(percentComplete) +
              '%;">' +
              "" +
              Math.round(percentComplete) +
              "%" +
              "</div>" +
              "</div>",
          });

          dom.patch();

          callback(true);
        } else {
          sb.data.db.obj.getWhere(
            "requests",
            {
              shipped: "yes",
              date_created: {
                type: "between",
                start: moment("2018-01-01", "YYYY-MM-DD").unix(),
                end: moment().unix(),
              },
              paged: {
                count: true,
                page: offset,
                pageLength: 1,
                paged: true,
                sortCol: "date_created",
                sortDir: "asc",
                sortCast: "string",
              },
            },
            function (reqs) {
              sb.data.db.obj.getWhere(
                "contact_info",
                {
                  object_id: reqs.data[0].contact,
                  is_primary: "yes",
                  childObjs: {
                    info: {},
                    type: {
                      data_type: {},
                    },
                  },
                },
                function (info) {
                  var phone = "";
                  var email = "";

                  _.each(info, function (i) {
                    console.log(i);
                    if (i.type.id == 49) {
                      phone = i.info;
                    }

                    if (i.type.data_type == "email") {
                      email = i.info;
                    }
                  });

                  var smsObj = {
                    to: phone,
                    message:
                      "Hey, this is The Life Book. Would you help us tell the Gideons Thank You for funding free Life Books? Just reply with a short thank you text.",
                  };

                  sb.comm.sendSMS(smsObj, function (done) {
                    sb.data.db.controller(
                      "mailchimpSendAPIEmail",
                      {
                        email: email,
                        workflowId: "5dc351fd16",
                        emailId: "22f203d0b9",
                      },
                      function (done) {
                        totalSent = totalSent + 1;

                        var percentComplete = (totalSent / total) * 100;

                        dom.makeNode("progress", "text", {
                          text:
                            '<div class="progress">' +
                            '<div class="progress-bar progress-bar-info progress-bar-stripe active" role="progressbar" aria-valuenow="' +
                            Math.round(percentComplete) +
                            '" aria-valuemin="0" aria-valuemax="100" style="width: ' +
                            Math.round(percentComplete) +
                            '%;">' +
                            "" +
                            Math.round(percentComplete) +
                            "%" +
                            "</div>" +
                            "</div>",
                        });

                        dom.makeNode("sent", "headerText", {
                          text: "Total sent: " + totalSent,
                          size: "small",
                        });

                        dom.patch();

                        setTimeout(function () {
                          offset = offset + 1;

                          state.appSettings.apps.tlbSMS.tgiThanks.currentOffset =
                            offset;

                          state.appSettings.save(function () {
                            timing.total++;

                            timing.totalSeconds += +moment().diff(
                              startTimer,
                              "s"
                            );
                            timing.avg = (
                              timing.totalSeconds / timing.total
                            ).toFixed(2);

                            var totalTime = (timing.totalSeconds / 60).toFixed(
                              3
                            );

                            if (totalTime > 0.5) {
                              totalTime = (timing.totalSeconds / 60).toFixed(1);
                            }

                            dom.makeNode("avg", "headerText", {
                              text:
                                "Averge send time: " + timing.avg + " seconds",
                              size: "x-small",
                            });
                            dom.makeNode("estRun", "headerText", {
                              text:
                                "Est. run time: " +
                                (timing.avg * total) / 60 +
                                " minutes",
                              size: "x-small",
                            });
                            dom.makeNode("estTimeToComp", "headerText", {
                              text:
                                "Est. time to complete: " +
                                (timing.avg * (total - timing.total)).toFixed(
                                  2
                                ) +
                                " seconds",
                              size: "x-small",
                            });

                            dom.patch();

                            return sendSMS(
                              dom,
                              offset,
                              total,
                              totalSent,
                              callback,
                              timing
                            );
                          });
                        }, 1000);
                      }
                    );
                  });
                }
              );
            }
          );
        }
      }

      dom.css("pda-background-gray pda-container");

      dom.empty();

      dom.makeNode("state", "headerText", {
        text: "Preparing...",
        size: "small",
      });

      dom.patch();

      sb.data.db.obj.getWhere(
        "requests",
        {
          shipped: "yes",
          date_created: {
            type: "between",
            start: moment("2018-01-01", "YYYY-MM-DD").unix(),
            end: moment().unix(),
          },
          paged: {
            count: true,
            page: +state.appSettings.apps.tlbSMS.tgiThanks.currentOffset,
            pageLength: 1,
            paged: true,
            sortCol: "date_created",
            sortDir: "asc",
            sortCast: "string",
          },
        },
        function (reqs) {
          dom.makeNode("state", "headerText", {
            text: "Total list size: " + reqs.recordsTotal,
            size: "small",
          });

          var percentComplete = (sent / reqs.recordsTotal) * 100;

          dom.makeNode("progress", "text", {
            text:
              '<div class="progress">' +
              '<div class="progress-bar" role="progressbar" aria-valuenow="' +
              percentComplete +
              '" aria-valuemin="0" aria-valuemax="100" style="width: ' +
              percentComplete +
              '%;">' +
              "" +
              percentComplete +
              "%" +
              "</div>" +
              "</div>",
          });

          dom.patch();

          sendSMS(
            dom,
            +state.appSettings.apps.tlbSMS.tgiThanks.currentOffset,
            reqs.recordsTotal,
            sent,
            function (done) {
              dom.makeNode("progress", "text", {
                text:
                  '<div class="progress">' +
                  '<div class="progress-bar progress-bar-success progress-bar-stripe" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%;">' +
                  "100%" +
                  "</div>" +
                  "</div>",
              });

              dom.patch();

              callback(true);
            }
          );
        }
      );
    }

    testSettings(state, function (newState) {
      var sent = +state.appSettings.apps.tlbSMS.tgiThanks.currentOffset;

      state = newState;

      var settings = state.appSettings.apps.tlbSMS.tgiThanks;

      dom.empty();

      dom.makeNode("modals", "container", {});

      dom.makeNode("title", "headerText", { text: "Gideon Thanks Campaign" });

      dom.makeNode("titleBreak", "lineBreak", {});

      dom.makeNode("offset", "headerText", {
        text: "Current Offset: " + settings.currentOffset,
        size: "x-small",
      });

      dom
        .makeNode("changeOffset", "button", {
          text: '<i class="fa fa-pencil"></i> Change Offset',
          css: "pda-btn-orange",
        })
        .notify(
          "click",
          {
            type: "tlbSMS-run",
            data: {
              run: function (dom, state, draw) {
                dom.modals.makeNode("modal", "modal", {
                  onShow: function () {
                    var modal = dom.modals.modal;

                    modal.body.makeNode("title", "headerText", {
                      text: "Change the current offset",
                      size: "small",
                    });

                    modal.body.makeNode("form", "form", {
                      offset: {
                        name: "offset",
                        label: "New Offset:",
                        type: "number",
                        value:
                          state.appSettings.apps.tlbSMS.tgiThanks.currentOffset,
                      },
                    });

                    modal.footer
                      .makeNode("save", "button", {
                        text: '<i class="fa fa-check"></i> Save',
                        css: "pda-btn-green",
                      })
                      .notify(
                        "click",
                        {
                          type: "tlbSMS-run",
                          data: {
                            run: function (dom, state, draw) {
                              modal.footer.save.loading();

                              var newOffset =
                                +modal.body.form.process().fields.offset.value;

                              state.appSettings.apps.tlbSMS.tgiThanks.currentOffset =
                                newOffset;

                              state.appSettings.save(function (done) {
                                modal.hide();
                              });
                            }.bind({}, dom, state, draw),
                          },
                        },
                        sb.moduleId
                      );

                    modal.body.patch();
                    modal.footer.patch();
                  },
                  onClose: function () {
                    sb.notify({
                      type: "app-navigate-to",
                      data: {
                        itemId: "tlbSMS",
                        viewId: "tgi-thanks",
                      },
                    });
                  },
                });

                dom.modals.patch();

                dom.modals.modal.show();
              }.bind({}, dom, state, draw),
            },
          },
          sb.moduleId
        );

      dom.makeNode("formBreak", "lineBreak", { spaces: 2 });

      dom
        .makeNode("send", "button", {
          text: '<i class="fa fa-envelope"></i> Send the campaign',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "tlbSMS-run",
            data: {
              run: function (dom, state, draw) {
                sb.dom.alerts.ask(
                  {
                    text: "Are you sure?",
                    title: "",
                  },
                  function (resp) {
                    if (resp) {
                      swal.close();

                      sendTGIThanks(
                        dom.sending,
                        state,
                        function (done) {
                          sb.dom.alerts.alert("Done sending!", "", "success");
                        },
                        sent
                      );
                    }
                  }
                );
              }.bind({}, dom, state, draw),
            },
          },
          sb.moduleId
        );

      dom.makeNode("sendingBreak", "lineBreak", { spaces: 2 });

      dom.makeNode("sending", "container", { css: "" });

      dom.patch();

      draw(false);
    });
  }

  return {
    init: function () {
      sb.listen({
        "tlbSMS-run": this.run,
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "tlbSMS",
            title: "SMS Campaigns",
            icon: '<i class="fa fa-comments"></i>',
            views: [
              {
                id: "tgi-thanks",
                type: "custom",
                default: true,
                title: "Gideon Thanks",
                icon: '<i class="fa fa-usd"></i>',
                dom: tgiThanks,
              },
              {
                id: "stories",
                type: "custom",
                default: true,
                title: "Stories",
                icon: '<i class="fa fa-usd"></i>',
                dom: stories,
              },
            ],
          },
        },
      });
    },

    run: function (data) {
      data.run(data);
    },
  };
});
