Factory.register('lifebookReport', function(sb){
	
	function getSumData(sumField, dateRange, callback, groupOn){
		
		var where = {
				status:'Shipped',
				groupBy:'year',
				dateRange:{
					start: dateRange.start.format('YYYY-MM-DD HH:mm:ss.SS'),
					end: dateRange.end.format('YYYY-MM-DD HH:mm:ss.SS')
				}
			};
			
		if(groupOn){
			
			where.groupOn = groupOn;
			
		}	
		
		sb.data.db.obj.getSum('requests', sumField, where, function(results){
			
			var ret = {
					count:0,
					average:0,
					sum:0
				},
				count = 0;
			
			_.each(results, function(row){
				
				count++;
				
				ret.count += row.grouped_total;
				ret.average = (ret.average + +row.avg) / count;
				ret.sum += row.sum;
				
			});
			
			callback(ret);
			
		});		
		
	}
	
	function printTopReport(dom, fields, dateRange, callback, count, retStack){
		
		if(!count){
			count = 0;
			retStack = [];
		}

		if(fields[count]){
			
			getSumData(fields[count].field, dateRange, function(data){
				
				dom.makeNode(fields[count].field+'-break', 'lineBreak', {});

				dom.makeNode(fields[count].field+'-title', 'headerText', {text:fields[count].title, size:'small'});
				
				dom.makeNode(fields[count].field+'-cont', 'container', {css:'pda-background-gray pda-container text-center'});
								
				dom[fields[count].field+'-cont'].makeNode('total', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(data.sum) +'<br /><small>Total</small>'});
				dom[fields[count].field+'-cont'].makeNode('average', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(Math.round(data.average, 2)) +'<br /><small>Average</small>'});
				dom[fields[count].field+'-cont'].makeNode('count', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(data.count) +'<br /><small>Total Count</small>'});
				
				dom[fields[count].field+'-cont'].makeNode(fields[count].field+'-break', 'lineBreak', {});
				
				dom.patch();			
				
				retStack.push({
					type:fields[count].field,
					data:data
				});
				
				count++;
				printTopReport(dom, fields, dateRange, callback, count, retStack);
				
			});
			
		}else{
			
			callback(retStack);
			
		}
		
	}
	
	return {
		
		init: function(data){
			
			if(appConfig.instance == 'thelifebook'){
				
				sb.notify({
					type:'register-application',
					data:{
						navigationItem:{
							id:'rerequests',
							title:'Re-Registrations',
							icon:'<i class="fa fa-book"></i>',
							type:'subnav',
							parent:'lifebook-metrics',
							views:[
								{
									id:'reregReport',
									type:'custom',
									title:'Re-registrations',
									icon:'<i class="fa fa-book"></i>',
									default:true,
									dom:function(dom, state, draw){
										
										dom.makeNode('selectDate', 'headerText', {text:'Select a date range', size:''});
										
										dom.makeNode('date', 'form', {date:{type:'text',name:'date', label:''}});									
	
										dom.makeNode('dateBreak', 'lineBreak', {});
					
										dom.makeNode('results', 'headerText', {text:'Results', size:''});
										
										dom.makeNode('cont', 'container', {});
										
										draw({
											dom:dom,
											after:function(dom){
												
												var dateRangeDom = dom.date.date.selector;
												
												$(dateRangeDom).daterangepicker({
												    "showDropdowns": false,
												    "ranges": {
												        "Today": [moment().startOf('day'),moment().endOf('day')],
												        "Yesterday": [moment().subtract(1, 'day').startOf('day'), moment().subtract(1, 'day').endOf('day')],
												        "This Season": [moment('2017-06-01').startOf('day').startOf('day'),moment('2018-05-31').endOf('day')],
														"Last Season": [moment('2016-06-01').startOf('day'),moment('2017-05-31').endOf('day')],
												        "Last 7 Days": [moment().subtract(7, 'day').startOf('day'), moment().endOf('day')],
												        "Last 30 Days": [moment().subtract(30, 'day').startOf('day'), moment().endOf('day')],
												        "This Month": [moment().startOf('month').startOf('day'), moment().endOf('month').endOf('day')],
												        "This Year": [moment().month(0).startOf('month').startOf('day'),moment().endOf('day')],
												        "Last Month": [moment().subtract(1, 'month').startOf('month').startOf('day'),moment().subtract(1, 'month').endOf('month').endOf('day')],
												        "Last Year": [moment().subtract(1, 'year').month(0).startOf('month').startOf('day'),moment().subtract(1, 'year').month(11).endOf('month').endOf('day')]
												    },
												    "alwaysShowCalendars": true,
												    "startDate":moment().subtract(30, 'day').startOf('day'),
												    "endDate":moment().endOf('day'),
													"opens":"right"
												}, function(start, end, label) {
													$(dateRangeDom +' span').html(start.format('MM-DD-YYYY') + ' - ' + end.format('MM-DD-YYYY'));
												});
												
												$(dateRangeDom).on('apply.daterangepicker', function(ev, picker){
													
													var fields = [
															{
																field:'books',
																title:'Book Stats'
															},
															{
																field:'adults',
																title:'Adult Stats'
															},
															{
																field:'students',
																title:'Student Stats'
															}
														];
													
													var dateRange = {start:picker.startDate, end:picker.endDate};
														
													printTopReport(dom.cont, fields, dateRange, function(data){
														
	console.log(data);
														sb.data.db.obj.getSum('requests', 'books', {
															status:'Shipped',
															groupBy:'year',
															groupOn:'book_version',
															dateRange:{
																start: dateRange.start.format('YYYY-MM-DD HH:mm:ss.SS'),
																end: dateRange.end.format('YYYY-MM-DD HH:mm:ss.SS')
															}
														}, function(results){
															
															var john = _.find(results, {grouped:'3010'}),
																other = _.find(results, {grouped:null}),
																mark = _.find(results, {grouped:'3020'});
	console.log(results, other);														
															dom.cont.makeNode('versionJohnBreak', 'lineBreak', {});								
															dom.cont.makeNode('versionJohnTitle', 'headerText', {text:'John Stats', size:'small'});
															dom.cont.makeNode('versionJohnCont', 'container', {css:'pda-container pda-background-gray text-center'});
															
															var johnTotal = 0,
																johnSum = 0,
																johnCount = 0,
																average = 1;
															
															if(john){
																
																johnTotal = john.grouped_total;
																johnSum = john.sum;
																johnAvg = +john.avg;
																
																average = 2;
															
															}
															
															if(other){
																	
																johnTotal += other.grouped_total;
																johnSum += other.sum;
																
																if(average > 1){
																	
																	johnAvg = (johnAvg + +other.avg) / average;
																	
																}else{
																	
																	johnAvg = +other.avg;
																	
																}
																
															}
	
															dom.cont.versionJohnCont.makeNode('johnSum', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(johnSum) +'<br /><small>Total</small>'});
															dom.cont.versionJohnCont.makeNode('johnAvg', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(Math.round(johnAvg)) +'<br /><small>Average</small>'});
															dom.cont.versionJohnCont.makeNode('johnTotal', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(johnTotal) +'<br /><small>Total Count</small>'});
															
	
															dom.cont.makeNode('versionMarkBreak', 'lineBreak', {});								
															dom.cont.makeNode('versionMarkTitle', 'headerText', {text:'Mark Stats', size:'small'});
															dom.cont.makeNode('versionMarkCont', 'container', {css:'pda-container pda-background-gray text-center'});
															
															var markTotal = 0,
																markSum = 0,
																markAvg = 0;
															
															if(mark){
																
																markTotal = mark.grouped_total;
																markSum = mark.sum;
																markAvg = mark.avg;
															
															}
	
															dom.cont.versionMarkCont.makeNode('MarkSum', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(markSum) +'<br /><small>Total</small>'});
															dom.cont.versionMarkCont.makeNode('MarkAvg', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(Math.round(markAvg)) +'<br /><small>Average</small>'});
															dom.cont.versionMarkCont.makeNode('MarkTotal', 'column', {width:4}).makeNode('cont', 'container', {}).makeNode('stat', 'headerText', {text:sb.dom.formatNumber(markTotal) +'<br /><small>Total Count</small>'});
	
															dom.cont.patch();
															
														});
														
													});	
												
												});
												
											}
										});
										
									}
								}
							]
						}
					}
				});
			
			}
			
		},
		
		start: function(){},
		
		run: function(data){ data.run(); }
		
	}
	
});