Factory.register('tgi-church-report', function(sb){

	var domObj = {},
		components = {};

	return {
		
		init: function(){
			
			if(appConfig.instance == 'thelifebook'){

				sb.listen({
					'tgi-church-report-load':this.load,
					'view-church-report-month':this.loadMonth,
					'tgi-church-report-destroy':this.destroy
				});
				
				sb.notify({
					type:'register-application',
					data:{
						navigationItem:{
							id:'church-report',
							title:'Church Report',
							icon:'<i class="fa fa-bar-chart"></i>',
							views:[
								{
									id:'create',
									type:'custom',
									title:'Report Dashboard',
									icon:'<i class="fa fa-bar-chart"></i>',
									default:true,
									dom:function(dom, state, draw){
										
										dom.makeNode('panel', 'container', {css:'pda-container'}).makeNode('body', 'container', {}).makeNode('title', 'headerText', {text:'Choose a month'});
												
										var years = [2018, 2017, 2016, 2015];
										
										_.each(years, function(year){
											
											dom.panel.body.makeNode('cont-'+year, 'container', {css:'pda-container'});
											
											dom.panel.body.makeNode('cont-break-'+year, 'container', {css:'pda-panel-primary'});
											
											dom.panel.body['cont-'+year].makeNode('year', 'headerText', {text:year, size:'small'});
											
											var month = 0,
												momObj = moment(year, 'YYYY');
											
											dom.panel.body['cont-'+year].makeNode('btns', 'buttonGroup', {css:'pull-left'});
											
											while(month < 12){
												
												var add = 1;
												
												if(month == 0){
													add = 0;
												}
												
												var printMoment = momObj.add(add, 'months');
												
												if(printMoment < moment().subtract(1, 'month')){
			
													dom.panel.body['cont-'+year].btns.makeNode('month-'+month, 'button', {text:printMoment.format('MMM'), css:'pda-btn-primary'}).notify('click', {
														type:'view-church-report-month',
														data:{
															date:printMoment.clone()
														}
													}, sb.moduleId);
													
												}
																					
												month++;
												
											}			
											
										});
										
										draw(dom);
												
									}
								}
							]
						}
					}
				});
			
			}

		},

		load: function(setup){
			
			if(setup.hasOwnProperty('domObj')){
				
				domObj = sb.dom.make(setup.domObj.selector)

			}
			
			//domObj.makeNode('panel', 'panel', {header:'Life Book Report', css:'pda-panel-blue'});
			domObj.makeNode('panel', 'container', {}).makeNode('body', 'container', {});
			
			domObj.build();
			
			components.table = sb.createComponent('crud-table');
			components.table.notify({
				type: 'show-table',
				data: {							
					domObj:domObj.panel.body,
					objectType:'requests',
					tableTitle:'Life Book Church Report',
					childObjs:2,
					searchObjects:[
						{
							name:'Request #',
							value:'object_uid'
						}
					],
					filters:false,
					download:function(obj){
							
						var churchName = 'None given';
						
						var objectUID = 0;
						
						if(obj.original_id)	{
							objectUID = obj.original_id;
						}else{
							objectUID = obj.object_uid;
						}
						
						if(obj.company){
							churchName = obj.company.name;
						}
						
						var street = '',
							city = '',
							state = '',
							zip = '';
						
						//street
						if(obj.contact){
								
							if(obj.contact.contact_info){
								
								var ret = '';
								_.each(obj.contact.contact_info, function(c){
									
									if(c){
										
										if(c.type == 53){
										
											ret = c.street;
											
										}
										
									}else{
										
										street = obj.street;
										
									}
									
																		
								});
								
								street = ret;
								
							}else{
							
								street = obj.street;
								
							}
															
						}else{
							
							street = obj.street;
							
						}
						
						// city
						if(obj.contact){
								
							if(obj.contact.contact_info){
								
								var ret = '';
								_.each(obj.contact.contact_info, function(c){
									
									if(c){
										
										if(c.type == 53){
										
											ret = c.city;
											
										}
										
									}else{
										
										city = obj.city;
										
									}
									
								});
								
								city = ret;
								
							}else{
							
								city = obj.city;
								
							}
															
						}else{
							
							city = obj.city;
							
						}
						
						// state
						if(obj.contact){
								
							if(obj.contact.contact_info){
								
								var ret = '';
								_.each(obj.contact.contact_info, function(c){
									
									if(c){
										
										if(c.type == 53){
										
											ret = c.state;
											
										}
										
									}else{
										
										state = obj.state;
										
									}
									
								});
								
								state = ret;
								
							}else{
							
								state = obj.state;
								
							}
															
						}else{
							
							state = obj.state;
							
						}	
						
						// zip
						if(obj.contact){
								
							if(obj.contact.contact_info){
								
								var ret = '';
								_.each(obj.contact.contact_info, function(c){
									
									if(c){
										
										if(c.type == 53){
										
											ret = c.zip;
											
										}
										
									}else{
										
										zip = obj.zip;
										
									}
									
								});
								
								zip = ret;
								
							}else{
							
								zip = obj.zip;
								
							}
															
						}else{
							
							zip = obj.zip;
							
						}	
						
						return [
							{
								name:'Shipped Date',
								value:moment(obj.shipped_date).format('M/D/YYYY'),
							},
							{
								name:'Order ID',
								value:objectUID,
							},
							{
								name:'Church Name',
								value:churchName
							},
							{
								name:'Street',
								value:street,
							},
							{
								name:'City',
								value:city,
							},
							{
								name:'State',
								value:state
							},
							{
								name:'Zip',
								value:zip
							},
							{
								name:'Books',
								value:obj.books
							}];
						
					},
					headerButtons:{},
					home:{
						header:'',
						action:function(dom){
							
							//dom.makeNode('panel', 'panel', {header:'Choose a month', css:'pda-panel-primary'});
							dom.makeNode('panel', 'container', {css:'pda-container'}).makeNode('body', 'container', {}).makeNode('title', 'headerText', {text:'Choose a month'});
							
							var years = [2017, 2016, 2015];
							
							_.each(years, function(year){
								
								dom.panel.body.makeNode('cont-'+year, 'container', {css:'pda-container'});
								
								dom.panel.body.makeNode('cont-break-'+year, 'container', {css:'pda-panel-primary'});
								
								dom.panel.body['cont-'+year].makeNode('year', 'headerText', {text:year, size:'small'});
								
								var month = 0,
									momObj = moment(year, 'YYYY');
								
								dom.panel.body['cont-'+year].makeNode('btns', 'buttonGroup', {css:'pull-left'});
								
								while(month < 12){
									
									var add = 1;
									
									if(month == 0){
										add = 0;
									}
									
									var printMoment = momObj.add(add, 'months');
									
									if(printMoment < moment().subtract(1, 'month')){

										dom.panel.body['cont-'+year].btns.makeNode('month-'+month, 'button', {text:printMoment.format('MMM'), css:'pda-btn-primary'}).notify('click', {
											type:'view-church-report-month',
											data:{
												date:printMoment.clone()
											}
										}, sb.moduleId);
										
									}
																		
									month++;
									
								}			
								
							});
							
							dom.patch();
							
						}
					},
					multiSelectButtons:{},
					rowSelection:false,
					rowLink:false,
					visibleCols:{
						shipped_date:'Date Created',
						object_uid:'Order #',
						business_name:'Church Name',
						street:'Street',
						city:'City',
						state:'State',
						zip:'Zip',
						books:'Order Quantity',
					},
					cells:{
						shipped_date:function(obj){
							return moment(obj.shipped_date).format('M-D-YYYY h:mm a');
						},
						object_uid:function(obj){
							
							if(obj.original_id)	{
								return obj.original_id;
							}else{
								return obj.object_uid;
							}
							
						},
						status: function(obj){

							switch(obj.status){
								
								case 'Cancelled':
								
									return '<span class="label label-danger">'+ obj.status +'</span>';
								
									break;

								case 'On Hold':
								
									return '<span class="label label-warning">'+ obj.status +'</span>';
								
									break;
									
								case 'Awaiting Shipment':
								
									return '<span class="label label-success">'+ obj.status +'</span>';
								
									break;
									
								case 'Shipped':
								
									return '<span class="label label-success">'+ obj.status +'</span>';
								
									break;		
								
							}
						
						},
						business_name: function(obj){
console.log(obj.company);							
							if(obj.company){
								
								if(obj.company.name){
									
									return obj.company.name;
									
								}else{
								
									return 'Manual order';
									
								}
							
							}else{
								
								return 'Manual order';
								
							}
							
						},
						street:function(obj){

							if(obj.contact){
								
								if(obj.contact.contact_info){
									
									var ret = '';
									_.each(obj.contact.contact_info, function(c){
										
										if(c){
											
											if(c.type == 53){
											
												ret = c.street;
												
											}
											
										}
										
									});
									
									return ret;
									
								}else{
								
									return obj.street;
									
								}
																
							}else{
								
								return obj.street;
								
							}
							
						},
						city:function(obj){

							if(obj.contact){
								
								if(obj.contact.contact_info){
									
									var ret = '';
									_.each(obj.contact.contact_info, function(c){
										
										if(c){
											
											if(c.type == 53){
											
												ret = c.city;
												
											}
											
										}
										
									});
									
									return ret;
									
								}else{
								
									return obj.city;
									
								}
																
							}else{
								
								return obj.city;
								
							}
							
						},
						state:function(obj){

							if(obj.contact){
								
								if(obj.contact.contact_info){
									
									var ret = '';
									_.each(obj.contact.contact_info, function(c){
										
										if(c){
											
											if(c.type == 53){
											
												ret = c.state;
												
											}	
											
										}
										
									});
									
									return ret;
									
								}else{
								
									return obj.state;
									
								}
																
							}else{
								
								return obj.state;
								
							}
							
						},
						zip:function(obj){

							if(obj.contact){
								
								if(obj.contact.contact_info){
									
									var ret = '';
									_.each(obj.contact.contact_info, function(c){
										
										if(c){
											
											if(c.type == 53){
											
												ret = c.zip;
												
											}	

										}
										
									});
									
									return ret;
									
								}else{
								
									return obj.zip;
									
								}
																
							}else{
								
								return obj.zip;
								
							}
							
						}
					},
					data:function(paged, callback){
						
						sb.data.db.obj.getWhere('requests', {
							shipped_date:{
								type:'between',
								start:moment('2017-07-01').unix(),
								end:moment('2017-07-31').unix()
							},
							status:'Shipped',
							paged:paged,
							childObjs:2
						}, function(requests){

							callback(requests);
							
						});
						
					}
					
				}
			});
			
		},
		
		loadMonth: function(data){
			
			var start = data.date.clone().unix(),
				end = data.date.add(1, 'month').clone().unix();
			
			sb.notify({
				type:'app-navigate-to',
				data:{
					itemId:'church-report',
					viewId:{
						id:'table-'+data.date.format('M'),
						type:'table',
						title:data.date.format('MMMM YYYY'),
						icon:'<i class="fa fa-calendar"></i>',
						setup:{
							objectType:'requests',
							tableTitle:'Life Book Church Report',
							childObjs:2,
							searchObjects:[
								{
									name:'Request #',
									value:'object_uid'
								}
							],
							filters:false,
							download:function(obj){
									
								var churchName = 'None given';
								
								var objectUID = 0;
								
								if(obj.original_id)	{
									objectUID = obj.original_id;
								}else{
									objectUID = obj.object_uid;
								}
								
								if(obj.company){
									churchName = obj.company.name;
								}
								
								var street = '',
									city = '',
									state = '',
									zip = '';
								
								//street
								if(obj.contact){
										
									if(obj.contact.contact_info){
										
										var ret = '';
										_.each(obj.contact.contact_info, function(c){
											
											if(c){
												
												if(c.type == 53){
												
													ret = c.street;
													
												}
												
											}else{
												
												street = obj.street;
												
											}
											
																				
										});
										
										street = ret;
										
									}else{
									
										street = obj.street;
										
									}
																	
								}else{
									
									street = obj.street;
									
								}
								
								// city
								if(obj.contact){
										
									if(obj.contact.contact_info){
										
										var ret = '';
										_.each(obj.contact.contact_info, function(c){
											
											if(c){
												
												if(c.type == 53){
												
													ret = c.city;
													
												}
												
											}else{
												
												city = obj.city;
												
											}
											
										});
										
										city = ret;
										
									}else{
									
										city = obj.city;
										
									}
																	
								}else{
									
									city = obj.city;
									
								}
								
								// state
								if(obj.contact){
										
									if(obj.contact.contact_info){
										
										var ret = '';
										_.each(obj.contact.contact_info, function(c){
											
											if(c){
												
												if(c.type == 53){
												
													ret = c.state;
													
												}
												
											}else{
												
												state = obj.state;
												
											}
											
										});
										
										state = ret;
										
									}else{
									
										state = obj.state;
										
									}
																	
								}else{
									
									state = obj.state;
									
								}	
								
								// zip
								if(obj.contact){
										
									if(obj.contact.contact_info){
										
										var ret = '';
										_.each(obj.contact.contact_info, function(c){
											
											if(c){
												
												if(c.type == 53){
												
													ret = c.zip;
													
												}
												
											}else{
												
												zip = obj.zip;
												
											}
											
										});
										
										zip = ret;
										
									}else{
									
										zip = obj.zip;
										
									}
																	
								}else{
									
									zip = obj.zip;
									
								}	
								
								return [
									{
										name:'Shipped Date',
										value:moment(obj.shipped_date).format('M/D/YYYY'),
									},
									{
										name:'Order ID',
										value:objectUID,
									},
									{
										name:'Church Name',
										value:churchName
									},
									{
										name:'Street',
										value:street,
									},
									{
										name:'City',
										value:city,
									},
									{
										name:'State',
										value:state
									},
									{
										name:'Zip',
										value:zip
									},
									{
										name:'Books',
										value:obj.books
									}];
								
							},
							headerButtons:{},
							multiSelectButtons:{},
							rowSelection:false,
							rowLink:false,
							visibleCols:{
								shipped_date:'Date Created',
								object_uid:'Order #',
								business_name:'Church Name',
								street:'Street',
								city:'City',
								state:'State',
								zip:'Zip',
								books:'Order Quantity',
							},
							cells:{
								shipped_date:function(obj){
									return moment(obj.shipped_date).format('M-D-YYYY h:mm a');
								},
								object_uid:function(obj){
									
									if(obj.original_id)	{
										return obj.original_id;
									}else{
										return obj.object_uid;
									}
									
								},
								status: function(obj){
		
									switch(obj.status){
										
										case 'Cancelled':
										
											return '<span class="label label-danger">'+ obj.status +'</span>';
										
											break;
		
										case 'On Hold':
										
											return '<span class="label label-warning">'+ obj.status +'</span>';
										
											break;
											
										case 'Awaiting Shipment':
										
											return '<span class="label label-success">'+ obj.status +'</span>';
										
											break;
											
										case 'Shipped':
										
											return '<span class="label label-success">'+ obj.status +'</span>';
										
											break;		
										
									}
								
								},
								business_name: function(obj){

									if(obj.company){
										
										if(obj.company.name){
											
											return obj.company.name;
											
										}else{
										
											return 'STAFF ORDER';
											
										}
									
									}else{
										
										return 'STAFF ORDER';
										
									}
									
								},
								street:function(obj){
		
									if(obj.contact){
										
										if(obj.contact.contact_info){
											
											var ret = '';
											_.each(obj.contact.contact_info, function(c){
												
												if(c){
													
													if(c.type == 53){
													
														ret = c.street;
														
													}
													
												}
												
											});
											
											return ret;
											
										}else{
										
											return obj.street;
											
										}
																		
									}else{
										
										return obj.street;
										
									}
									
								},
								city:function(obj){
		
									if(obj.contact){
										
										if(obj.contact.contact_info){
											
											var ret = '';
											_.each(obj.contact.contact_info, function(c){
												
												if(c){
													
													if(c.type == 53){
													
														ret = c.city;
														
													}
													
												}
												
											});
											
											return ret;
											
										}else{
										
											return obj.city;
											
										}
																		
									}else{
										
										return obj.city;
										
									}
									
								},
								state:function(obj){
		
									if(obj.contact){
										
										if(obj.contact.contact_info){
											
											var ret = '';
											_.each(obj.contact.contact_info, function(c){
												
												if(c){
													
													if(c.type == 53){
													
														ret = c.state;
														
													}	
													
												}
												
											});
											
											return ret;
											
										}else{
										
											return obj.state;
											
										}
																		
									}else{
										
										return obj.state;
										
									}
									
								},
								zip:function(obj){
		
									if(obj.contact){
										
										if(obj.contact.contact_info){
											
											var ret = '';
											_.each(obj.contact.contact_info, function(c){
												
												if(c){
													
													if(c.type == 53){
													
														ret = c.zip;
														
													}	
		
												}
												
											});
											
											return ret;
											
										}else{
										
											return obj.zip;
											
										}
																		
									}else{
										
										return obj.zip;
										
									}
									
								}
							},
							data:function(paged, callback){
								
								sb.data.db.obj.getWhere('requests', {
									shipped_date:{
										type:'between',
										start:start,
										end:end
									},
									status:'Shipped',
									paged:paged,
									childObjs:2
								}, function(requests){
		
									callback(requests);
									
								});
								
							}
						},
						removable:true
					}
				}
			});	
			
		},
		
		destroy: function(){
		
			_.each(components, function(comp){
				
				comp.destroy();
				
			});
						
			domObj = {};
			components = {};

		}
	}

});
