Factory.register('tgi-report', function(sb){

	var domObj = {},
		components = {};

	return {
		
		init: function(){
			
			sb.listen({
				'tgi-report-load':this.load,
				'view-report-month':this.loadMonth,
				'view-report-month-internal':this.loadMonthInternal,
				'tgi-report-destroy':this.destroy
			});
			
			components.table = sb.createComponent('crud-table');
			
			if(
				appConfig.instance == 'thelifebook'
				||
				appConfig.instance == 'rickyvoltz'
			){
				
				sb.notify({
					type:'register-application',
					data:{
						navigationItem:{
							id:'gideon-report',
							title:'Request Report',
							icon:'<i class="fa fa-bar-chart"></i>',
							views:[
								{
									id:'create',
									type:'custom',
									title:'Report Dashboard',
									icon:'<i class="fa fa-bar-chart"></i>',
									default:true,
									dom:function(dom, state, draw){
										
										dom.makeNode('panel', 'container', {css:''}).makeNode('body', 'container', {}).makeNode('title', 'headerText', {text:'Choose a month'});
	
										var years = [2020, 2019, 2018, 2017, 2016, 2015];
										
										_.each(years, function(year){
											
											dom.panel.body.makeNode('cont-'+year, 'container', {css:'pda-container'});
											
											dom.panel.body.makeNode('cont-break-'+year, 'container', {css:'pda-panel-primary'});
											
											dom.panel.body['cont-'+year].makeNode('year', 'headerText', {text:year, size:'small'});
											
											var month = 0,
												momObj = moment(year, 'YYYY');
											
											dom.panel.body['cont-'+year].makeNode('btns', 'buttonGroup', {css:'pull-left'});
											
											while(month < 12){
												
												var add = 1;
												
												if(month == 0){
													add = 0;
												}
												
												var printMoment = momObj.add(add, 'months');
												
												if(printMoment < moment().subtract(1, 'month')){
			
													dom.panel.body['cont-'+year].btns.makeNode('month-'+month, 'button', {text:printMoment.format('MMM'), css:'pda-btn-primary'}).notify('click', {
														type:'view-report-month',
														data:{
															date:printMoment.clone()
														}
													}, sb.moduleId);
													
												}
																					
												month++;
												
											}			
											
										});
										
										//dom.patch();
										
										draw(dom);
										
									}
								},
								{
									id:'internal-report',
									type:'custom',
									title:'Internal Report Dashboard',
									icon:'<i class="fa fa-bar-chart"></i>',
									default:true,
									dom:function(dom, state, draw){
										
										dom.makeNode('panel', 'container', {css:''}).makeNode('body', 'container', {}).makeNode('title', 'headerText', {text:'Choose a month'});
	
										var years = [2020, 2019, 2018, 2017, 2016, 2015];
										
										_.each(years, function(year){
											
											dom.panel.body.makeNode('cont-'+year, 'container', {css:'pda-container'});
											
											dom.panel.body.makeNode('cont-break-'+year, 'container', {css:'pda-panel-primary'});
											
											dom.panel.body['cont-'+year].makeNode('year', 'headerText', {text:year, size:'small'});
											
											var month = 0,
												momObj = moment(year, 'YYYY');
											
											dom.panel.body['cont-'+year].makeNode('btns', 'buttonGroup', {css:'pull-left'});
											
											while(month < 12){
												
												var add = 1;
												
												if(month == 0){
													add = 0;
												}
												
												var printMoment = momObj.add(add, 'months');
												
												if(printMoment < moment().subtract(1, 'month')){
			
													dom.panel.body['cont-'+year].btns.makeNode('month-'+month, 'button', {text:printMoment.format('MMM'), css:'pda-btn-primary'}).notify('click', {
														type:'view-report-month-internal',
														data:{
															date:printMoment.clone()
														}
													}, sb.moduleId);
													
												}
																					
												month++;
												
											}			
											
										});
										
										//dom.patch();
										
										draw(dom);
										
									}
								},
								{
									id:'gideonReport',
									type:'hqTool',
									name:'TGI Request Report',
									tip:'TGI Request report.',
									icon: {
										type: 'chart line',
										color: 'blue'
									},
									default:true,
									mainViews:[
										{
											dom:function(dom, state, draw, mainDom){
											
												dom.makeNode('panel', 'div', {css:''}).makeNode('body', 'div', {css:'ui clearing segment'}).makeNode('title', 'headerText', {text:'Choose a month'});
												
												dom.makeNode('results', 'div', {css:'ui clearing segment'})
													.makeNode('title', 'div', {css:'ui header', text:'Choose a month above. Report will load here.'});
												
												var years = [2020, 2019, 2018, 2017, 2016, 2015];
												
												_.each(years, function(year){
													
													dom.panel.body.makeNode('cont-'+year, 'div', {css:'ui basic segment'});
													
													dom.panel.body.makeNode('cont-break-'+year, 'div', {text:'<br />'});
													
													dom.panel.body['cont-'+year].makeNode('year', 'headerText', {text:year, size:'small'});
													
													var month = 0,
														momObj = moment(year, 'YYYY');
													
													dom.panel.body['cont-'+year].makeNode('btns', 'buttonGroup', {css:'pull-left'});
																									
													while(month < 12){
														
														var add = 1;
														
														if(month == 0){
															add = 0;
														}
														
														var printMoment = momObj.add(add, 'months');
														
														if(printMoment < moment().subtract(1, 'month')){
					
															dom.panel.body['cont-'+year].btns.makeNode('month-'+month, 'button', {text:printMoment.format('MMM'), css:'pda-btn-primary'}).notify('click', {
																type:'view-report-month',
																data:{
																	dom:dom.results,
																	date:printMoment.clone()
																}
															}, sb.moduleId);
															
														}
																							
														month++;
														
													}			
													
												});
																							
												draw(dom);																					
	
											}
										}
									]
								},
								{
									id:'internalReport',
									type:'hqTool',
									name:'Youth Leader Report',
									tip:'Youth Leader report.',
									icon: {
										type: 'chart line',
										color: 'blue'
									},
									default:true,
									mainViews:[
										{
											dom:function(dom, state, draw, mainDom){
											
												dom.makeNode('panel', 'div', {css:''}).makeNode('body', 'div', {css:'ui clearing segment'}).makeNode('title', 'headerText', {text:'Choose a month'});
												
												dom.makeNode('results', 'div', {css:'ui clearing segment'})
													.makeNode('title', 'div', {css:'ui header', text:'Choose a month above. Report will load here.'});
												
												var years = [2020, 2019, 2018, 2017, 2016, 2015];
												
												_.each(years, function(year){
													
													dom.panel.body.makeNode('cont-'+year, 'div', {css:'ui basic segment'});
													
													dom.panel.body.makeNode('cont-break-'+year, 'div', {text:'<br />'});
													
													dom.panel.body['cont-'+year].makeNode('year', 'headerText', {text:year, size:'small'});
													
													var month = 0,
														momObj = moment(year, 'YYYY');
													
													dom.panel.body['cont-'+year].makeNode('btns', 'buttonGroup', {css:'pull-left'});
																									
													while(month < 12){
														
														var add = 1;
														
														if(month == 0){
															add = 0;
														}
														
														var printMoment = momObj.add(add, 'months');
														
														if(printMoment < moment().subtract(1, 'month')){
					
															dom.panel.body['cont-'+year].btns.makeNode('month-'+month, 'button', {text:printMoment.format('MMM'), css:'pda-btn-primary'}).notify('click', {
																type:'view-report-month-internal',
																data:{
																	dom:dom.results,
																	date:printMoment.clone()
																}
															}, sb.moduleId);
															
														}
																							
														month++;
														
													}			
													
												});
																							
												draw(dom);																					
	
											}
										}
									]
								},
								{
									id:'gideonReport',
									type:'teamTool',
									name:'TGI Request Report',
									tip:'TGI Request report.',
									icon: {
										type: 'chart line',
										color: 'blue'
									},
									default:true,
									mainViews:[
										{
											dom:function(dom, state, draw, mainDom){
											
												dom.makeNode('panel', 'div', {css:''}).makeNode('body', 'div', {css:'ui clearing segment'}).makeNode('title', 'headerText', {text:'Choose a month'});
												
												dom.makeNode('results', 'div', {css:'ui clearing segment'})
													.makeNode('title', 'div', {css:'ui header', text:'Choose a month above. Report will load here.'});
												
												var years = [2020, 2019, 2018, 2017, 2016, 2015];
												
												_.each(years, function(year){
													
													dom.panel.body.makeNode('cont-'+year, 'div', {css:'ui basic segment'});
													
													dom.panel.body.makeNode('cont-break-'+year, 'div', {text:'<br />'});
													
													dom.panel.body['cont-'+year].makeNode('year', 'headerText', {text:year, size:'small'});
													
													var month = 0,
														momObj = moment(year, 'YYYY');
													
													dom.panel.body['cont-'+year].makeNode('btns', 'buttonGroup', {css:'pull-left'});
																									
													while(month < 12){
														
														var add = 1;
														
														if(month == 0){
															add = 0;
														}
														
														var printMoment = momObj.add(add, 'months');
														
														if(printMoment < moment().subtract(1, 'month')){
					
															dom.panel.body['cont-'+year].btns.makeNode('month-'+month, 'button', {text:printMoment.format('MMM'), css:'pda-btn-primary'}).notify('click', {
																type:'view-report-month',
																data:{
																	dom:dom.results,
																	date:printMoment.clone()
																}
															}, sb.moduleId);
															
														}
																							
														month++;
														
													}			
													
												});
																							
												draw(dom);																					
	
											}
										}
									]
								}
							]
						}
					}
				});
				
			}

		},

		load: function(setup){
			
			if(setup.hasOwnProperty('domObj')){
				
				domObj = sb.dom.make(setup.domObj.selector)

			}
			
			//domObj.makeNode('panel', 'panel', {header:'Life Book Report', css:'pda-panel-blue'});
			domObj.makeNode('panel', 'container', {}).makeNode('body', 'container', {});
			
			domObj.build();
			
			components.table = sb.createComponent('crud-table');
			components.table.notify({
				type: 'show-table',
				data: {							
					domObj:domObj.panel.body,
					objectType:'requests',
					tableTitle:'The Life Book Monthly Order Report',
					maxPageButtons:6,
					searchObjects:[
						{
							name:'Request #',
							value:'object_uid'
						}
						
					],
					filters:false,
					download:function(obj){
							
						var churchName = 'None given';
						
						var objectUID = 0;
						
						if(obj.original_id)	{
							objectUID = obj.original_id;
						}else{
							objectUID = obj.object_uid;
						}
						
						if(obj.company){
							churchName = obj.company.name;
						}
						
						return [
							{
								name:'Shipped Date',
								value:moment(obj.shipped_date).format('M/D/YYYY'),
							},
							{
								name:'Order ID',
								value:objectUID,
							},
							{
								name:'Church Name',
								value:churchName
							},
							{
								name:'Street',
								value:obj.street,
							},
							{
								name:'City',
								value:obj.city,
							},
							{
								name:'State',
								value:obj.state
							},
							{
								name:'Zip',
								value:obj.zip
							},
							{
								name:'Books',
								value:obj.books
							}];
						
					},
					headerButtons:{},
					home:{
						header:'',
						action:function(dom){
							
							//dom.makeNode('panel', 'panel', {header:'Life Book Report', css:'pda-panel-primary'});
							dom.makeNode('panel', 'container', {css:'pda-container'}).makeNode('body', 'container', {}).makeNode('title', 'headerText', {text:'Choose a month'});

							var years = [2017, 2016, 2015];
							
							_.each(years, function(year){
								
								dom.panel.body.makeNode('cont-'+year, 'container', {css:'pda-container'});
								
								dom.panel.body.makeNode('cont-break-'+year, 'container', {css:'pda-panel-primary'});
								
								dom.panel.body['cont-'+year].makeNode('year', 'headerText', {text:year, size:'small'});
								
								var month = 0,
									momObj = moment(year, 'YYYY');
								
								dom.panel.body['cont-'+year].makeNode('btns', 'buttonGroup', {css:'pull-left'});
								
								while(month < 12){
									
									var add = 1;
									
									if(month == 0){
										add = 0;
									}
									
									var printMoment = momObj.add(add, 'months');
									
									if(printMoment < moment().subtract(1, 'month')){

										dom.panel.body['cont-'+year].btns.makeNode('month-'+month, 'button', {text:printMoment.format('MMM'), css:'pda-btn-primary'}).notify('click', {
											type:'view-report-month',
											data:{
												date:printMoment.clone()
											}
										}, sb.moduleId);
										
									}
																		
									month++;
									
								}			
								
							});
							
							dom.patch();
							
						}
					},
					multiSelectButtons:{},
					rowSelection:false,
					rowLink:false,
					visibleCols:{
						shipped_date:'Date Created',
						object_uid:'Order #',
						business_name:'Church Name',
						street:'Street',
						city:'City',
						state:'State',
						zip:'Zip',
						books:'Order Quantity',
					},
					cells:{
						shipped_date:function(obj){
							return moment(obj.shipped_date).format('M-D-YYYY h:mm a');
						},
						object_uid:function(obj){
							
							if(obj.original_id)	{
								return obj.original_id;
							}else{
								return obj.object_uid;
							}
							
						},
						status: function(obj){

							switch(obj.status){
								
								case 'Cancelled':
								
									return '<span class="label label-danger">'+ obj.status +'</span>';
								
									break;

								case 'On Hold':
								
									return '<span class="label label-warning">'+ obj.status +'</span>';
								
									break;
									
								case 'Awaiting Shipment':
								
									return '<span class="label label-success">'+ obj.status +'</span>';
								
									break;
									
								case 'Shipped':
								
									return '<span class="label label-success">'+ obj.status +'</span>';
								
									break;		
								
							}
						
						},
						business_name: function(obj){
							
							if(obj.company){
								
								if(obj.company.name){
									
									return obj.company.name;
									
								}
							
							}
							
						},
						street:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('street')){
									
									return obj.shipping_address.street;
							
								}else{
								
									return obj.street;
									
								}
								
							}else{
								
								return obj.street;
								
							}
							
						},
						city:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('city')){
									
									return obj.shipping_address.city;
							
								}else{
								
									return obj.city;
									
								}
								
							}else{
								
								return obj.city;
								
							}
							
						},
						state:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('state')){
									
									return obj.shipping_address.state;
							
								}else{
								
									return obj.state;
									
								}
								
							}else{
								
								return obj.state;
								
							}
							
						},
						zip:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('zip')){
									
									return obj.shipping_address.zip;
							
								}else{
								
									return obj.zip;
									
								}
								
							}else{
								
								return obj.zip;
								
							}
							
						}
					},
					data:function(paged, callback){
						
						sb.data.db.obj.getWhere('requests', {
							shipped_date:{
								type:'between',
								start:moment('2017-07-01').unix(),
								end:moment('2017-07-31').unix()
							},
							status:'Shipped',
							paged:paged,
							childObjs:1
						}, function(requests){

							callback(requests);
							
						});
						
					}
					
				}
			});
			
		},
		
		loadMonth: function(data){
			
			var modifiedDate = data.date.startOf('day').clone();
			
			var start = modifiedDate.startOf('day').clone().unix(),
				end = modifiedDate.add(1, 'month').clone().unix();
				
			var setup = {
					domObj:data.dom,
					objectType:'requests',
					tableTitle:false,
					childObjs:1,
					searchObjects:[
						{
							name:'Request #',
							value:'object_uid'
						}
						
					],
					filters:false,
					download:function(obj){
							
						var churchName = 'STAFF ORDER';
						
						var objectUID = 0;
						
						if(obj.original_id)	{
							objectUID = obj.original_id;
						}else{
							objectUID = obj.object_uid;
						}
						
						if(obj.company){
							churchName = obj.company.name;
						}
						
						return [
							{
								name:'Shipped Date',
								value:moment(obj.shipped_date).format('M/D/YYYY'),
							},
							{
								name:'Order ID',
								value:objectUID,
							},
							{
								name:'Church Name',
								value:churchName
							},
							{
								name:'Street',
								value:obj.street,
							},
							{
								name:'City',
								value:obj.city,
							},
							{
								name:'State',
								value:obj.state
							},
							{
								name:'Zip',
								value:obj.zip
							},
							{
								name:'Books',
								value:obj.books
							}];
						
					},
					headerButtons:{},
					multiSelectButtons:{},
					rowSelection:false,
					rowLink:false,
					visibleCols:{
						shipped_date:'Date Created',
						object_uid:'Order #',
						business_name:'Church Name',
						street:'Street',
						city:'City',
						state:'State',
						zip:'Zip',
						books:'Order Quantity',
					},
					cells:{
						shipped_date:function(obj){
							return moment(obj.shipped_date).format('M-D-YYYY h:mm a');
						},
						object_uid:function(obj){
							
							if(obj.original_id)	{
								return obj.original_id;
							}else{
								return obj.object_uid;
							}
							
						},
						status: function(obj){

							switch(obj.status){
								
								case 'Cancelled':
								
									return '<span class="label label-danger">'+ obj.status +'</span>';
								
									break;

								case 'On Hold':
								
									return '<span class="label label-warning">'+ obj.status +'</span>';
								
									break;
									
								case 'Awaiting Shipment':
								
									return '<span class="label label-success">'+ obj.status +'</span>';
								
									break;
									
								case 'Shipped':
								
									return '<span class="label label-success">'+ obj.status +'</span>';
								
									break;		
								
							}
						
						},
						business_name: function(obj){
							
							if(obj.company){
								
								if(obj.company.name){
									
									return obj.company.name;
									
								}else{
									
									return 'STAFF ORDER';
									
								}
							
							}else{
								
								return 'STAFF ORDER';
								
							}
							
						},
						street:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('street')){
									
									return obj.shipping_address.street;
							
								}else{
								
									return obj.street;
									
								}
								
							}else{
								
								return obj.street;
								
							}
							
						},
						city:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('city')){
									
									return obj.shipping_address.city;
							
								}else{
								
									return obj.city;
									
								}
								
							}else{
								
								return obj.city;
								
							}
							
						},
						state:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('state')){
									
									return obj.shipping_address.state;
							
								}else{
								
									return obj.state;
									
								}
								
							}else{
								
								return obj.state;
								
							}
							
						},
						zip:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('zip')){
									
									return obj.shipping_address.zip;
							
								}else{
								
									return obj.zip;
									
								}
								
							}else{
								
								return obj.zip;
								
							}
							
						}
					},
					data:function(paged, callback){
				
						sb.data.db.obj.getWhere('requests', {
							shipped_date:{
								type:'between',
								start:start,
								end:end
							},
							status:'Shipped',
							paged:paged,
							childObjs:1
						}, function(requests){
							
							requests.data = _.sortBy(requests.data, 'shipped_date')
							
							callback(requests);
							
						});
						
					}
				};
			
			components.table.notify({
				type: 'show-table',
				data: setup	
			});
						
		},
		
		loadMonthInternal: function(data){
			
			var modifiedDate = data.date.startOf('day').clone();
			
			var start = modifiedDate.startOf('day').clone().unix(),
				end = modifiedDate.add(1, 'month').clone().unix();
				
			var setup = {
					domObj:data.dom,
					objectType:'requests',
					tableTitle:false,
					childObjs:1,
					searchObjects:[
						{
							name:'Request #',
							value:'object_uid'
						}
						
					],
					filters:false,
					download:function(obj){
							
						var churchName = 'STAFF ORDER';
						
						var objectUID = 0;
						
						if(obj.original_id)	{
							objectUID = obj.original_id;
						}else{
							objectUID = obj.object_uid;
						}
						
						if(obj.company){
							churchName = obj.company.name;
						}
						
						return [
							{
								name:'Shipped Date',
								value:moment(obj.shipped_date).format('M/D/YYYY'),
							},
							{
								name:'Order ID',
								value:objectUID,
							},
							{
								name:'Church Name',
								value:churchName
							},
							{
								name:'First Name',
								value:obj.fname,
							},
							{
								name:'Last Name',
								value:obj.lname,
							},
							{
								name:'Cell Phone',
								value:obj.phone,
							},
							{
								name:'Street',
								value:obj.street,
							},
							{
								name:'City',
								value:obj.city,
							},
							{
								name:'State',
								value:obj.state
							},
							{
								name:'Zip',
								value:obj.zip
							},
							{
								name:'Books',
								value:obj.books
							}];
						
					},
					headerButtons:{},
					multiSelectButtons:{},
					rowSelection:false,
					rowLink:false,
					visibleCols:{
						shipped_date:'Date Created',
						object_uid:'Order #',
						business_name:'Church Name',
						fname:'First Name',
						lname:'Last Name',
						cellphone:'Cell Phone',
						street:'Street',
						city:'City',
						state:'State',
						zip:'Zip',
						books:'Order Quantity',
					},
					cells:{
						shipped_date:function(obj){
							return moment(obj.shipped_date).format('M-D-YYYY h:mm a');
						},
						object_uid:function(obj){
							
							if(obj.original_id)	{
								return obj.original_id;
							}else{
								return obj.object_uid;
							}
							
						},
						fname:function(obj){
							
							if(obj.contact)	{
								return obj.contact.fname;
							}else{
								return '<i>no name</i>';
							}
							
						},
						lname:function(obj){
							
							if(obj.contact)	{
								return obj.contact.lname;
							}else{
								return '<i>no name</i>';
							}
							
						},
						cellphone:function(obj){
							
							if(obj.phone)	{
								return obj.phone;
							}else{
								return '<i>no phone</i>';
							}
							
						},
						status: function(obj){

							switch(obj.status){
								
								case 'Cancelled':
								
									return '<span class="label label-danger">'+ obj.status +'</span>';
								
									break;

								case 'On Hold':
								
									return '<span class="label label-warning">'+ obj.status +'</span>';
								
									break;
									
								case 'Awaiting Shipment':
								
									return '<span class="label label-success">'+ obj.status +'</span>';
								
									break;
									
								case 'Shipped':
								
									return '<span class="label label-success">'+ obj.status +'</span>';
								
									break;		
								
							}
						
						},
						business_name: function(obj){
							
							if(obj.company){
								
								if(obj.company.name){
									
									return obj.company.name;
									
								}else{
									
									return 'STAFF ORDER';
									
								}
							
							}else{
								
								return 'STAFF ORDER';
								
							}
							
						},
						street:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('street')){
									
									return obj.shipping_address.street;
							
								}else{
								
									return obj.street;
									
								}
								
							}else{
								
								return obj.street;
								
							}
							
						},
						city:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('city')){
									
									return obj.shipping_address.city;
							
								}else{
								
									return obj.city;
									
								}
								
							}else{
								
								return obj.city;
								
							}
							
						},
						state:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('state')){
									
									return obj.shipping_address.state;
							
								}else{
								
									return obj.state;
									
								}
								
							}else{
								
								return obj.state;
								
							}
							
						},
						zip:function(obj){
							
							if(obj.shipping_address){
								
								if(obj.hasOwnProperty('zip')){
									
									return obj.shipping_address.zip;
							
								}else{
								
									return obj.zip;
									
								}
								
							}else{
								
								return obj.zip;
								
							}
							
						}
					},
					data:function(paged, callback){
				
						sb.data.db.obj.getWhere('requests', {
							shipped_date:{
								type:'between',
								start:start,
								end:end
							},
							status:'Shipped',
							paged:paged,
							childObjs:1
						}, function(requests){
							
							requests.data = _.sortBy(requests.data, 'shipped_date')

							callback(requests);
							
						});
						
					}
				};
			
			components.table.notify({
				type: 'show-table',
				data: setup	
			});
						
		},
		
		destroy: function(){
		
			_.each(components, function(comp){
				
				comp.destroy();
				
			});
						
			domObj = {};
			components = {};

		}
	}

});