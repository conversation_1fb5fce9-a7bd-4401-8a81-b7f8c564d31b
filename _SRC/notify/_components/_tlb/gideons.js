Factory.register('gideons', function(sb) {
	
	var comps = {};
	var listeners = {};
	
	function addNewSeed(dom, state, callback){
		
		function makeRecommendation(dom, state, callback){
			
			dom.save.loading();
			
			setInterval(function(){
				
				dom.empty();
				
				dom.makeNode('title', 'div', {text:'Send Email Reminder to the Youth Leader', css:'ui huge header'});
				dom.makeNode('message', 'div', {css:'ui grey message'})
					.makeNode('header', 'div', {text:'Copy the email below and send it to the Youth Leader. We will let you know when they request Life Books. Please personalize the email as much as possible.', css:'header'});
				
				dom.makeNode('template', 'div', {css:'ui raised basic segment'});
				
				dom.template.makeNode('header', 'div', {text:'Thank You for Your Ministry', css:'ui small header'});
				dom.template.makeNode('body', 'div', {text:'Hi (Insert Youth Leader Name),'+
'<br /><br />'+
'Thank you for your ministry to teenagers. I greatly appreciate all you do for <PERSON> and His Kingdom.'+
'<br /><br />'+
'Please know that I’ll be praying for you and your ministry. Feel free to email me with any special prayer requests.'+
'<br /><br /> '+
'I also wanted to remind you that Life Books are completely free for your teenagers to give to their classmates and peers. You can get yours at thelifebook.com'+
'<br /><br />'+
'Thank you again for your faithfulness. May God bless you and your ministry.'+
'<br /><br />'+
'(Add Your Name and Contact Information)', tag:'p'});
				
				dom.makeNode('save', 'div', {text:'Done', css:'ui green button', tag:'button'}).notify('click', {
					type:'gideons-run',
					data:{
						run:saveForm.bind({}, dom, state, callback)
					}
				}, sb.moduleId);
				
				dom.patch();
				
			}, 1000);
			
		}
		
		function saveForm(dom, state, callback){
			
			dom.save.loading();
			
			setInterval(function(){
				
				callback(true);
				
			}, 1000);
			
		}
		
		var formObj = {
				fname:{
					name:'fname',
					label:'Youth Leader\'s First Name',
					type:'text',
					placeholder:'John'
				},
				lname:{
					name:'lname',
					label:'Youth Leader\'s Last Name',
					type:'text',
					placeholder:'Doe'
				},
				email:{
					name:'email',
					label:'Youth Leader\'s Email Address',
					type:'text',
					placeholder:'<EMAIL>'
				},
				church_name:{
					name:'church_name',
					label:'Church Name',
					type:'text',
					placeholder:'First Baptist of Anywhere, USA'
				},
				church_phone:{
					name:'church_phone',
					label:'Phone Number',
					type:'text',
					placeholder:'1234567890'
				},
				is_cell:{
					name:'is_cell',
					label:'This is a cell phone',
					type:'select',
					options:[
						{
							value:0,
							name:'No'
						},
						{
							value:1,
							name:'Yes'
						}
					]
				}
			};
				
		dom.makeNode('title', 'div', {text:'Add New Seed', css:'ui huge header'});
		dom.makeNode('message', 'div', {css:'ui grey message'})
			.makeNode('header', 'div', {text:'Fill out this form to see if any Youth Leader\'s you have spoken to have ordered Life Books.', css:'header'});
		
		dom.makeNode('form', 'form', formObj);		

		dom.makeNode('break', 'div', {text:'<br />'});		
		
		dom.makeNode('save', 'div', {text:'Next step', css:'ui green button'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:makeRecommendation.bind({}, dom, state, callback)
				}
			}, sb.moduleId);
				
	}

	function campOrders(dom, state, draw){
		
		dom.empty();
		
		dom.makeNode('seedTracker', 'div', {css:'ui clearing segment'})
			.makeNode('new', 'div', {text:'Order Camp Life Books', css:'ui right floated green button'})
				.notify('click', {type:'gideons-run', data:{run:function(dom, state, draw, total){
					
					if(total < 200){
						
						dom.modals.makeNode('newSeed', 'modal', {
							onShow:function(){
								
								addNewSeed(dom.modals.newSeed.body, state, function(savedSeed){
									
									dom.modals.newSeed.hide();
									
								});
								
								dom.modals.newSeed.body.patch();
								
							}
						});
						
						dom.modals.patch();
						dom.modals.newSeed.show();
						
					}else{
						
						sb.dom.alerts.alert('', 'Your camp has already ordered '+ total +' Life Books this year', 'error');
						return;
						
					}
					
				}.bind({}, dom, state, draw, 200)}}, sb.moduleId);
		dom.seedTracker.makeNode('title', 'div', {text:'Your Camp\'s Life Book Orders', css:'ui left floated large header'});
		dom.seedTracker.makeNode('break', 'div', {text:'<br /><br />'});
		dom.seedTracker.makeNode('message', 'div', {css:'ui grey message'})
			.makeNode('header', 'div', {text:'Camps may order Life Books for to share with Pastors and Youth Leaders one-on-one and for PAB events. Camps are limited to 200 Life Books per fiscal year.', css:'header'});
		dom.seedTracker.makeNode('break2', 'div', {text:'<hr><br />'});
		
		dom.seedTracker.makeNode('counter', 'div', {css:'ui small header', text:'200 Life Book ordered for your camp this year.'});
		
		dom.seedTracker.makeNode('table', 'table', {
			clearCSS:true,
			css:'ui table',
			columns:{
				name:'Ordered By',
				phone:'Phone Number',
				date:'Order Date',
				books:'Amount'
			}
		});
		
		dom.seedTracker.table.makeRow(
			'row1',
			[
				'John Doe',
				'(*************',
				moment().format('M/D/YYYY'),
				100
			]
		);

		dom.seedTracker.table.makeRow(
			'row2',
			[
				'Bill Smith',
				'(*************',
				moment().format('M/D/YYYY'),
				100
			]
		);
		
		dom.makeNode('modals', 'div', {});
				
		dom.patch();
		
	}
	
	function homeScreen(dom, state, draw){
		
		window.scrollTo(0,0);
		
		dom.empty();
		
		sb.notify({
			type:'app-remove-all-navigation',
			data:{}
		});
		
		ui_appHeader(dom, false, false, state, draw);


/*
		dom.makeNode('title1', 'div', {text:'Helping Members use The Life Book', css:'ui huge header', tag:'p'});
		dom.makeNode('title2', 'div', {text:'To Strive side by side for the faith of the Gospel With', css:'ui huge header', tag:'p'});
		dom.makeNode('title3', 'div', {text:'Pastors, Youth Leaders, & Students', css:'ui huge header', tag:'p'});
*/

/*
		dom.makeNode('colsmessage', 'div', {css:'ui centered stackable grid'})
			.makeNode('col', 'div', {css:'twelve wide center aligned column'});
		dom.colsmessage.col.makeNode('message', 'div', {css:'ui grey center aligned message'})
			.makeNode('header', 'div', {text:'There are churches in your area using The Life Book.', css:'center aligned header'});
		dom.colsmessage.col.message.makeNode('btn', 'div', {css:'ui grey button', text:'See the churches'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){
	
						findOpenDoors(dom, state, draw);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);

		dom.makeNode('messageBreak', 'div', {text:'<br />'});
*/
		
		//dom.makeNode('banner', 'div', {tag:'img', src:'https://thelifebook.com/images/gideon-app/Banner.png', css:'ui large centered image'});
		dom.makeNode('subtitle', 'div', {css:'ui center aligned large header', text:'Helping Members use The Life Book<br />to strive side by side for the faith of the Gospel<br />with church Pastors and Youth Leaders'});

		dom.makeNode('cols', 'div', {css:'ui centered stackable grid'})
			.makeNode('col', 'div', {css:'sixteen wide column center aligned'});

		dom.cols.makeNode('col1', 'div', {css:'four wide column'});
		dom.cols.makeNode('col2', 'div', {css:'four wide column'});
		dom.cols.makeNode('col3', 'div', {css:'four wide column'});
									
		var segDom1 = dom.cols.col1;
		var segDom2 = dom.cols.col2;
		var segDom3 = dom.cols.col3;
					
		var segDom = dom.cols.col;
			
		segDom1.makeNode('seg1', 'div', {css:'ui red clearing segment'})
			.makeNode('grid', 'div', {css:'ui stackable grid center aligned'});
		segDom1.seg1.grid.makeNode('vidCol', 'div', {css:'sixteen wide column'});
		segDom1.seg1.grid.makeNode('infoCol', 'div', {css:'sixteen wide column centered center aligned'});
		segDom1.seg1.grid.vidCol.makeNode('embed', 'div', {css:'ui left floated small embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/Open1.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});
		segDom1.seg1.grid.infoCol.makeNode('header', 'div', {text:'Open the door', css:'ui large header center aligned', tag:'span'});
		segDom1.seg1.grid.infoCol.makeNode('title', 'div', {text:'Use The Life Book to initiate church relationships', css:'ui header', tag:'p'});
		segDom1.seg1.grid.infoCol.makeNode('btn', 'div', {text:'Get started', css:'ui grey button', tag:'button', style:'background-color:#414042 !important;'});


		//segDom.makeNode('seg1break', 'div', {text:'<br />'})


		segDom2.makeNode('seg2', 'div', {css:'ui red clearing segment'})
			.makeNode('grid', 'div', {css:'ui stackable grid'});
		segDom2.seg2.grid.makeNode('vidCol', 'div', {css:'sixteen wide column'});
		segDom2.seg2.grid.makeNode('infoCol', 'div', {css:'sixteen wide column center aligned'});
		segDom2.seg2.grid.vidCol.makeNode('embed', 'div', {css:'ui left floated small embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/Find1.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});
		segDom2.seg2.grid.infoCol.makeNode('header', 'div', {text:'Find open doors', css:'ui large header', tag:'span'});
		segDom2.seg2.grid.infoCol.makeNode('title', 'div', {text:'See the churches in your camp area using The Life Book', css:'ui header', tag:'p'});
		segDom2.seg2.grid.infoCol.makeNode('btn', 'div', {text:'See the list', css:'ui grey button', tag:'button', style:'background-color:#414042 !important;'});
					
					
		//segDom.makeNode('seg2break', 'div', {text:'<br />'})
											
		
		segDom3.makeNode('seg3', 'div', {css:'ui red clearing segment'})
			.makeNode('grid', 'div', {css:'ui stackable grid'});
		segDom3.seg3.grid.makeNode('vidCol', 'div', {css:'sixteen wide column'});
		segDom3.seg3.grid.makeNode('infoCol', 'div', {css:'sixteen wide column center aligned'});
		segDom3.seg3.grid.vidCol.makeNode('embed', 'div', {css:'ui left floated small embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/Nurture1.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});
		segDom3.seg3.grid.infoCol.makeNode('header', 'div', {text:'Nurture open doors', css:'ui large header', tag:'span'});
		segDom3.seg3.grid.infoCol.makeNode('title', 'div', {text:'Build relationships with churches using The Life Book', css:'ui header', tag:'p'});
		segDom3.seg3.grid.infoCol.makeNode('btn', 'div', {text:'Get resources', css:'ui grey button', tag:'button', style:'background-color:#414042 !important;'});

	
											
		//segDom3.makeNode('finalBreak', 'div', {text:'<br /><br />'})


		dom.makeNode('finalBreak', 'div', {text:'<br /><br />'});

		dom.makeNode('modals', 'div', {});
		
		segDom1.seg1.grid.infoCol.btn.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){
	
						openDoors(dom, state, draw);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
					
		segDom2.seg2.grid.infoCol.btn.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){
	
						findOpenDoors(dom, state, draw);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
			
		segDom3.seg3.grid.infoCol.btn.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){
	
						nurtureOpenDoors(dom, state, draw);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);		
		
		draw({
			dom:dom,
			after:function(dom){

				$('.ui .embed').embed();
				
			}
		});
		
	}
	
	function openDoors(dom, state, draw){
		
		window.scrollTo(0,0);
		
		dom.empty();

		ui_appHeader(dom, true, 'Open Doors', state, draw);
		
		dom.makeNode('modals', 'div', {text:'<br />'});
													
		dom.makeNode('cols', 'div', {css:'ui stackable grid'});
		
		dom.cols.makeNode('left', 'div', {css:'four wide column'});
		dom.cols.makeNode('right', 'div', {css:'twelve wide column'});

		dom.cols.left.makeNode('vid', 'div', {css:'ui clearing segment'});
		dom.cols.left.vid.makeNode('title', 'div', {css:'ui small header', text:'About Open Doors'});
		dom.cols.left.vid.makeNode('embed', 'div', {css:'ui small embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/Open1.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});
				
		dom.cols.left.makeNode('resources', 'div', {css:'ui clearing basic segment'});
		//dom.cols.left.resources.makeNode('title', 'div', {text:'Resources', css:'ui left floated large header'});
		//dom.cols.left.resources.makeNode('break', 'div', {text:'<br /><br /><hr><br />'});
		
		dom.cols.left.resources.makeNode('menu', 'div', {css:'ui fluid secondary vertical pointing large menu'});
		dom.cols.left.resources.menu.makeNode('item1', 'div', {css:'active item', text:'Making the connection', tag:'a'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){

						dom.cols.left.resources.menu.item1.css('active item');						
						dom.cols.left.resources.menu.item2.css('item');
						dom.cols.left.resources.menu.item3.css('item');
						
						openingTips(dom.cols.right, state, draw, true);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
		dom.cols.left.resources.menu.makeNode('item2', 'div', {css:'item', text:'SeedTracker', tag:'a'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){

						dom.cols.left.resources.menu.item1.css('item');						
						dom.cols.left.resources.menu.item2.css('active item');
						dom.cols.left.resources.menu.item3.css('item');
						
						seedTracker(dom.cols.right, state, draw);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
		dom.cols.left.resources.menu.makeNode('item3', 'div', {css:'item', text:'Camp Orders', tag:'a'})
		.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){

						dom.cols.left.resources.menu.item1.css('item');						
						dom.cols.left.resources.menu.item2.css('item');
						dom.cols.left.resources.menu.item3.css('active item');
						
						campOrders(dom.cols.right, state, draw);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
				
						
		dom.cols.left.makeNode('finalBreak', 'div', {text:'<br /><br />'});
		
		openingTips(dom.cols.right, state, draw);
		
		//seedTracker(dom.cols.right, state, draw);
		
		draw({
			dom:dom,
			after:function(dom){
				$('.ui .embed').embed();
			}
		});
		
	}

	function orderLifeBooks(dom, state, callback){
				
		function saveForm(dom, state, callback){
			
			dom.save.loading();
			
			setInterval(function(){
				
				callback(true);
				
			}, 1000);
			
		}
		
		var formObj = {
				fname:{
					name:'fname',
					label:'First Name',
					type:'text',
					placeholder:appConfig.user.fname,
					active:'disabled'
				},
				lname:{
					name:'lname',
					label:'Last Name',
					type:'text',
					placeholder:appConfig.user.lname,
					active:'disabled'
				},
				camp_number:{
					name:'camp_number',
					label:'Camp Number',
					type:'text',
					placeholder:123456,
					active:'disabled'
				},
				email:{
					name:'email',
					label:'Email Address',
					type:'text',
					placeholder:appConfig.user.email,
					active:'disabled'
				},
				street:{
					name:'street',
					label:'Street',
					type:'text',
					placeholder:appConfig.user.street,
					active:'disabled'
				},
				city:{
					name:'city',
					label:'City',
					type:'text',
					placeholder:appConfig.user.city,
					active:'disabled'
				},
				state:{
					name:'state',
					label:'State',
					type:'text',
					placeholder:appConfig.user.state,
					active:'disabled'
				},
				zip:{
					name:'zip',
					label:'Zip Code',
					type:'text',
					placeholder:appConfig.user.zip,
					active:'disabled'
				}
			};
		
		dom.empty();
				
		dom.makeNode('title', 'div', {text:'Order Life Books', css:'ui huge header'});
		
		dom.makeNode('form', 'form', formObj);		

		dom.makeNode('break', 'div', {text:'<br />'});		
		
		dom.makeNode('save', 'div', {text:'Place the order', css:'ui green button'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:saveForm.bind({}, dom, state, callback)
				}
			}, sb.moduleId);
			
		dom.patch();
		
	}
	
	function findOpenDoors(dom, state, draw){
		
		dom.empty();

		ui_appHeader(dom, true, 'Find Open Doors', state, draw);

		dom.makeNode('modals', 'div', {text:'<br />'});
															
		dom.makeNode('cols', 'div', {css:'ui stackable grid'});
		
		dom.cols.makeNode('left', 'div', {css:'sixteen wide column'})
			.makeNode('seg', 'div', {css:'ui clearing segment'}).makeNode('new', 'div', {text:'Download Current List', css:'ui right floated grey button', style:'background-color:#414042 !important;'})
				.notify('click', {type:'gideons-run', data:{run:function(dom, state, draw){
					
					dom.cols.left.seg.new.loading();
					
					setTimeout(function(){
						
						dom.cols.left.seg.new.loading(false);						
						
					}, 2000);
					
				}.bind({}, dom, state, draw)}}, sb.moduleId);
		dom.cols.left.seg.makeNode('title', 'div', {text:'See the churches in your camp area using The Life Book', css:'ui left floated large header'});
		dom.cols.left.seg.makeNode('break1', 'div', {text:'<br /><br />'});
		dom.cols.left.seg.makeNode('message', 'div', {css:'ui grey message'})
			.makeNode('header', 'div', {text:'The order date is not the date Life Books were received by the church leader. Please be aware that churches use varied timelines for distribution. Some order Life Books many months ahead of a distribution, others distribute them throughout the whole school year while others may hand them out in a semester or a week. Please do not attempt to manage the Youth Leader or his timeline. He will appreciate it greatly if you allow him to do what he believes works best for his teens and his ministry.'});
		//dom.cols.left.seg.makeNode('break', 'div', {text:'<br /><br /><hr><br />'});
		
		dom.cols.left.seg.makeNode('btns', 'div', {css:'ui buttons'});
		//dom.cols.left.seg.makeNode('btnsBreak', 'div', {text:'<br />'});
		
		dom.cols.left.seg.btns.makeNode('year', 'div', {css:'ui dropdown search button'})
		dom.cols.left.seg.btns.year.makeNode('default', 'div', {tag:'span', css:'text', text:'2018-2019 Fiscal Year <i class="caret down icon"></i>'});
		dom.cols.left.seg.btns.year.makeNode('menu', 'div', {css:'menu'});

/*
		dom.cols.left.seg.btns.makeNode('month', 'div', {css:'ui dropdown search button'})
		dom.cols.left.seg.btns.month.makeNode('default', 'div', {tag:'span', css:'text', text:'Select a month'});
		dom.cols.left.seg.btns.month.makeNode('menu', 'div', {css:'menu'});
*/
		
		dom.cols.left.seg.btns.makeNode('go', 'div', {css:'ui green button', text:'Load church list'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){
						
						dom.cols.left.seg.btns.go.css('ui green button').loading();
						dom.cols.left.seg.tableCont.css('ui loading segment');
						
						setTimeout(function(){
						
							dom.cols.left.seg.btns.go.loading(false);
							dom.cols.left.seg.tableCont.removeClass('loading');					
							
						}, 2000);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
		
		dom.cols.left.seg.btns.year.menu.makeNode('default', 'div', {css:'active item', text:'2018-2019 Fiscal Year'});
		dom.cols.left.seg.btns.year.menu.makeNode('default-1', 'div', {css:'item', text:'2017-2018 Fiscal Year'});
		dom.cols.left.seg.btns.year.menu.makeNode('default-2', 'div', {css:'item', text:'2016-2017 Fiscal Year'});
		dom.cols.left.seg.btns.year.menu.makeNode('default-3', 'div', {css:'item', text:'2015-2016 Fiscal Year'});
		
		var years = [];
		var months = [];
		
		var currentYear = 2019;
		var currentMonth = 0;
		while(currentYear <= +moment().format('YYYY')){
			
			years.push(currentYear);
			
			//dom.cols.left.seg.btns.year.menu.makeNode('item-'+currentYear, 'div', {css:'item', text:currentYear});
			
			currentYear++;
			
		}
/*
		while(currentMonth <= 11){
			
			months.push(currentMonth);
			
			dom.cols.left.seg.btns.month.menu.makeNode('item-'+currentMonth, 'div', {css:'item', text:moment().month(currentMonth).format('MMMM')});
			
			currentMonth++;
			
		}
*/
//console.log(years);

		dom.cols.left.seg.makeNode('tableCont', 'div', {css:'ui basic segment'});
						
		dom.cols.left.seg.tableCont.makeNode('table', 'table', {
			clearCSS:true,
			css:'ui table',
			columns:{
				church:'Church name',
				street:'Street',
				city:'City',
				state:'State',
				date:'Date Ordered',
				camps:'Potential Camp Area'
			}
		});
		
		dom.cols.left.seg.tableCont.table.makeRow(
			'row1',
			['Test church name', '123 Easy Street', 'Hendersonville', 'TN', moment().format('M/D/YYYY'), '<div class="ui green label">Your camp</div>']
		);
		dom.cols.left.seg.tableCont.table.makeRow(
			'row2',
			['Test church name', '123 Easy Street', 'Gallatin', 'TN', moment().format('M/D/YYYY'), '<div class="ui orange label">Your camp, U12345, U22345</div>']
		);
		dom.cols.left.seg.tableCont.table.makeRow(
			'row3',
			['Test church name', '123 Easy Street', 'Nashville', 'TN', moment().format('M/D/YYYY'), '<div class="ui orange label">Your camp, U32345</div>']
		);
		dom.cols.left.seg.tableCont.table.makeRow(
			'row4',
			['Test church name', '123 Easy Street', 'Madison', 'TN', moment().format('M/D/YYYY'), '<div class="ui green label">Your camp</div>']
		);
		
		draw({
			dom:dom,
			after:function(dom){
				
				$('.ui .dropdown').dropdown();
				
			}
		});
		
	}

	function lifebookStories(dom, state, draw, patch){
		
		window.scrollTo(0,0);
		
		dom.empty();
		
		dom.makeNode('cont', 'div', {css:'ui basic segment'});
		
		var cont = dom.cont;
			
		//cont.makeNode('ask', 'div', {css:'ui right floated grey button', tag:'a', text:'Ask a question', target:'_blank', href:'http://questions.thelifebook.com/'});
		cont.makeNode('title', 'div', {text:'Life Book Stories', css:'ui left floated large header'});
		cont.makeNode('break', 'div', {text:'<br /><br />'});
		cont.makeNode('message', 'div', {css:'ui grey message'})
			.makeNode('header', 'div', {text:'"See how God is using The Life Book."', css:'header'});
		cont.makeNode('break2', 'div', {text:'<hr>'});
		
		cont.makeNode('menu', 'div', {css:'ui top attached tabular menu'});
		cont.makeNode('studentsCont', 'div', {css:'ui bottom attached active tab segment', dataAttr:[{name:'tab', value:'students'}]});
		cont.makeNode('youthleadersCont', 'div', {css:'ui bottom attached tab segment', dataAttr:[{name:'tab', value:'youthleaders'}]});
		
		cont.menu.makeNode('students', 'div', {css:'active item story', text:'Student Stories', dataAttr:[{name:'tab', value:'students'}], tag:'a'});
		cont.menu.makeNode('youthleaders', 'div', {css:'item story', text:'Youth Leader Stories', dataAttr:[{name:'tab', value:'youthleaders'}], tag:'a'});
		cont.menu.makeNode('share', 'div', {css:'item'}).makeNode('btn', 'div', {css:'ui green button', text:'Share a Story'});
		
		var studentCards = cont.studentsCont.makeNode('cards', 'div', {css:'ui three doubling cards'});
		var ylCards = cont.youthleadersCont.makeNode('cards', 'div', {css:'ui three doubling cards'});
		
		var testCards = 5;
		var count = 0;
		
		while(count < testCards){
			
			studentCards.makeNode('card'+count, 'div', {css:'ui card'});
			studentCards['card'+count].makeNode('content', 'div', {css:'content'});
			studentCards['card'+count].content.makeNode('title', 'div', {text:'Story #1', css:'header'});

			ylCards.makeNode('card'+count, 'div', {css:'ui card'});
			ylCards['card'+count].makeNode('content', 'div', {css:'content'});
			ylCards['card'+count].content.makeNode('title', 'div', {text:'Story #1', css:'header'});
			
			count++;
			
		}
		
		dom.makeNode('modals', 'div', {});
		
		cont.menu.share.notify('click', {
			type:'gideons-run',
			data:{
				run:function(dom, state, draw){
					
					dom.modals.makeNode('share', 'modal', {
						onShow:function(){
							
							dom.modals.share.body.makeNode('title', 'div', {css:'ui huge header', text:'Share your story'});

							dom.modals.share.body.makeNode('form', 'form', {
								story:{
									name:'story',
									label:'',
									type:'textbox',
									placeholder:'Type your story here.',
									rows:15
								}
							});
							
							dom.modals.share.body.makeNode('break', 'div', {text:'<br />'});
							
							dom.modals.share.body.makeNode('save', 'div', {text:'Share Story', css:'ui green button'})
								.notify('click', {
									type:'gideons-run',
									data:{
										run:function(dom){
											
											dom.modals.share.body.save.loading();
											
											setTimeout(function(){
												
												dom.modals.share.hide();
												
											}, 2000);
											
										}.bind({}, dom)
									}
								}, sb.moduleId);
							
							dom.modals.share.body.patch();
														
						}
					});
					
					dom.modals.patch();
					dom.modals.share.show();
					
				}.bind({}, dom, state, draw)
			}
		}, sb.moduleId);
						
		if(patch){
			dom.patch();
			$('.ui .embed').embed();
			$(cont.menu.selector +' .item').tab();
		}
		
	}
	
	function encourageYouthLeaders(dom, state, draw, patch){
		
		window.scrollTo(0,0);
		
		dom.empty();
		
		dom.makeNode('cont', 'div', {css:'ui basic segment'});
		
		var cont = dom.cont;
			
		cont.makeNode('title', 'div', {text:'Encourage Youth Leaders', css:'ui left floated large header'});
		cont.makeNode('break', 'div', {text:'<br /><br />'});
		cont.makeNode('message', 'div', {css:'ui grey message'})
			.makeNode('header', 'div', {text:'"striving side by side for the faith of the Gospel with churches"', css:'header'});
		cont.makeNode('break2', 'div', {text:'<br />'});
		
		var cards = cont.makeNode('cards', 'div', {css:''});
		
		
		cards.makeNode('tip1', 'div', {css:'ui stackable grid'});
		cards.tip1.makeNode('video', 'div', {css:'six wide column'});
		cards.tip1.makeNode('content', 'div', {css:'ten wide column'});
		cards.tip1.content.makeNode('title', 'div', {text:'<b>Encourage Youth Leaders</b>', css:''});
		cards.tip1.content.makeNode('body', 'div', {text:'Pray for them<br />Ask for prayer requests<br />Speak well of them to the pastor<br />Speak well of them in the community<br />Send note and/or card'});
		cards.tip1.content.makeNode('text', 'div', {text:'Easy Ways to Encourage Youth Leaders', css:'ui large header'});
		cards.tip1.video.makeNode('embed', 'div', {css:'ui embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/Encourage.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});
		
		dom.makeNode('finalBreak', 'div', {text:'<br />'});
		
		if(patch){
			dom.patch();
			$('.ui .embed').embed();
		}
		
	}
	
	function openingTips(dom, state, draw, patch){
		
		window.scrollTo(0,0);
		
		dom.empty();
		
		dom.makeNode('cont', 'div', {css:'ui basic segment'});
		
		var cont = dom.cont;
			
		cont.makeNode('ask', 'div', {css:'ui right floated grey button', tag:'a', style:'background-color:#414042 !important;', text:'Ask a question', target:'_blank', href:'http://questions.thelifebook.com/'});
		cont.makeNode('title', 'div', {text:'Making the connection', css:'ui left floated large header'});
		cont.makeNode('break', 'div', {text:'<br /><br />'});
		cont.makeNode('message', 'div', {css:'ui grey message'})
			.makeNode('header', 'div', {text:'"striving side by side for the faith of the Gospel with churches"', css:'header'});
		cont.makeNode('break2', 'div', {text:'<hr>'});
		
		var cards = cont.makeNode('cards', 'div', {css:''});
		
		
		cards.makeNode('tip1', 'div', {css:'ui stackable grid segment'});
		cards.tip1.makeNode('video', 'div', {css:'six wide column'});
		cards.tip1.makeNode('content', 'div', {css:'ten wide column'});
		cards.tip1.content.makeNode('title', 'div', {text:'<b>How to share The Life Book with a Pastor</b>', css:''});
		cards.tip1.content.makeNode('body', 'div', {text:'The Life Book is free to churches<br />Over 2.6 Million teens have handed out over 34 Million to their peers<br />Simple way for every teen to be involved in evangelism<br />Everything they need to know is at thelifebook.com'});
		cards.tip1.content.makeNode('text', 'div', {text:'Connect with the Pastor', css:'ui large header'});
		cards.tip1.video.makeNode('embed', 'div', {css:'ui embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/SharePastor.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});

		cards.makeNode('tip2', 'div', {css:'ui stackable grid segment'});
		cards.tip2.makeNode('video', 'div', {css:'six wide column'});
		cards.tip2.makeNode('content', 'div', {css:'ten wide column'});
		cards.tip2.content.makeNode('header', 'div', {text:'<b>How to share The Life Book with a Youth Leader</b>', css:''});
		cards.tip2.content.makeNode('body', 'div', {text:'Youth leaders are often more accessible<br />Don’t try to be cool or waste their time<br />No need to say much because they’ll go right to the website<br />Don’t hint that you’ll help them'});
		cards.tip2.content.makeNode('text', 'div', {text:'Connect with the Youth Leader', css:'ui large header'});
		cards.tip2.video.makeNode('embed', 'div', {css:'ui embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/ShareYL.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});

		cards.makeNode('tip3', 'div', {css:'ui stackable grid segment'});
		cards.tip3.makeNode('video', 'div', {css:'six wide column'});
		cards.tip3.makeNode('content', 'div', {css:'ten wide column'});
		cards.tip3.content.makeNode('header', 'div', {text:'How They See You<br />The Heart of a Youth Leader<br />The Maverick Mindset<br />Appreciate and Encourage'});
		cards.tip3.content.makeNode('text', 'div', {text:'<b>Understanding Why Youth Leaders are Different</b>', css:'ui large header'});
		cards.tip3.video.makeNode('embed', 'div', {css:'ui embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/Different.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});
		
		dom.makeNode('finalBreak', 'div', {text:'<br />'});
		
		if(patch){
			dom.patch();
			$('.ui .embed').embed();
		}
		
	}
	
	function seedTracker(dom, state, draw){
		
		dom.empty();
		
		dom.makeNode('seedTracker', 'div', {css:'ui clearing segment'})
			.makeNode('new', 'div', {text:'Add New Seed', css:'ui right floated green button'})
				.notify('click', {type:'gideons-run', data:{run:function(dom, state, draw){
					
					dom.modals.makeNode('newSeed', 'modal', {
						onShow:function(){
							
							addNewSeed(dom.modals.newSeed.body, state, function(savedSeed){
								
								dom.modals.newSeed.hide();
								
							});
							
							dom.modals.newSeed.body.patch();
							
						}
					});
					
					dom.modals.patch();
					dom.modals.newSeed.show();
					
				}.bind({}, dom, state, draw)}}, sb.moduleId);
		dom.seedTracker.makeNode('title', 'div', {text:'SeedTracker', css:'ui left floated large header'});
		dom.seedTracker.makeNode('break', 'div', {text:'<br /><br />'});
		dom.seedTracker.makeNode('message', 'div', {css:'ui grey message'})
			.makeNode('header', 'div', {text:'Here are Youth Leaders you have connected with about The Life Book. Click on \'Add New Seed\' to add more.', css:'header'});
		dom.seedTracker.makeNode('break2', 'div', {text:'<hr><br />'});
		
		dom.seedTracker.makeNode('table', 'table', {
			clearCSS:true,
			css:'ui table',
			columns:{
				name:'Name',
				church:'Church Name',
				email:'Email Address',
				church_phone:'Phone Number',
				planted:'Planted On Date',
				books:'LB Order Date'
			}
		});
		
		dom.seedTracker.table.makeRow(
			'row1',
			[
				'John Doe',
				'First Baptist',
				'<EMAIL>',
				'(*************',
				moment().format('M/D/YYYY')
			]
		);

		dom.seedTracker.table.makeRow(
			'row2',
			[
				'John Doe',
				'First Baptist',
				'<EMAIL>',
				'(*************',
				moment().format('M/D/YYYY')
			]
		);

		dom.seedTracker.table.makeRow(
			'row3',
			[
				'John Doe',
				'First Baptist',
				'<EMAIL>',
				'(*************',
				moment().format('M/D/YYYY'),
				'<i class="icon checkmark"></i> 300 ordered on '+ moment().format('M/D/YYYY')
			],
			{
				css:'positive'
			}
		);
		
		dom.makeNode('modals', 'div', {});
				
		dom.patch();
		
	}
	
	function sendACard(dom, state, draw, patch){
		
		window.scrollTo(0,0);
		
		dom.empty();
		
		dom.makeNode('cont', 'div', {css:'ui basic segment'});
		
		var cont = dom.cont;
			
		cont.makeNode('title', 'div', {text:'Send Gideon Card', css:'ui left floated large header'});
		cont.makeNode('break', 'div', {text:'<br /><br />'});
		cont.makeNode('message', 'div', {css:'ui grey message'})
			.makeNode('header', 'div', {text:'"Sending them a Gideon Card is a great way to let youth leaders know you care while also introducing them to the greater Gideon ministry."', css:'header'});
		//cont.makeNode('break2', 'div', {text:'<br />'});
		
		var cards = cont.makeNode('cards', 'div', {css:'ui one cards'});
		
		cards.makeNode('card1', 'div', {css:'ui raised card'});
		cards.card1.makeNode('image', 'div', {tag:'a', href:'https://www.sendtheword.org/', target:'_blank', css:'image'}).makeNode('image', 'div', {css:'', src:'https://thelifebook.com/images/gideon-app/sharetheword.png', tag:'img'});
		cards.card1.makeNode('button', 'div', {css:'ui bottom attached blue button', tag:'a', href:'https://www.sendtheword.org/', target:'_blank', text:'Go now <i class="right arrow icon"></i>'});

		
		if(patch){
			dom.patch();
			$('.ui .embed').embed();
		}
		
	}
		
	function nurtureOpenDoors(dom, state, draw){
		
		window.scrollTo(0,0);
		
		dom.empty();

		ui_appHeader(dom, true, 'Nuture Open Doors', state, draw);
		
		dom.makeNode('modals', 'div', {text:'<br />'});
													
		dom.makeNode('cols', 'div', {css:'ui stackable grid'});
		
		dom.cols.makeNode('left', 'div', {css:'four wide column'});
		dom.cols.makeNode('right', 'div', {css:'twelve wide column'});

		dom.cols.left.makeNode('vid', 'div', {css:'ui clearing segment'});
		dom.cols.left.vid.makeNode('title', 'div', {css:'ui small header', text:'About Nurturing Open Doors'});
		dom.cols.left.vid.makeNode('embed', 'div', {css:'ui small embed', dataAttr:[{name:'placeholder', value:'https://thelifebook.com/images/gideon-app/Nurture1.png'}, {name:'source', value:'vimeo'}, {name:'id', value:225419060}]});
				
		dom.cols.left.makeNode('resources', 'div', {css:'ui clearing basic segment'});
		
		dom.cols.left.resources.makeNode('menu', 'div', {css:'ui fluid secondary vertical pointing large menu'});
		dom.cols.left.resources.menu.makeNode('item1', 'div', {css:'active item', text:'Life Book Stories', tag:'a'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){

						dom.cols.left.resources.menu.item1.css('active item');						
						dom.cols.left.resources.menu.item2.css('item');
						dom.cols.left.resources.menu.item3.css('item');
						
						lifebookStories(dom.cols.right, state, draw, true);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
		dom.cols.left.resources.menu.makeNode('item2', 'div', {css:'item', text:'Encourage Youth Leaders', tag:'a'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){

						dom.cols.left.resources.menu.item1.css('item');						
						dom.cols.left.resources.menu.item2.css('active item');
						dom.cols.left.resources.menu.item3.css('item');
						
						encourageYouthLeaders(dom.cols.right, state, draw, true);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
		dom.cols.left.resources.menu.makeNode('item3', 'div', {css:'item', text:'Send Gideon Card', tag:'a'})
		.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){

						dom.cols.left.resources.menu.item1.css('item');						
						dom.cols.left.resources.menu.item2.css('item');
						dom.cols.left.resources.menu.item3.css('active item');
						
						sendACard(dom.cols.right, state, draw, true);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
				
						
		dom.cols.left.makeNode('finalBreak', 'div', {text:'<br /><br />'});
				
		draw({
			dom:dom,
			after:function(dom){
				
				$('.ui .embed').embed();
				
				lifebookStories(dom.cols.right, state, draw, true);
				
				//$('.item .story').tab();
			}
		});
		
	}
	
	function ui_appHeader(dom, backButton, title, state, draw){
		
		var titleString = '<img class="ui middle aligned left floated image" src="https://thelifebook.com/images/TLB-Logo1.png"> ';
		if(title){
			titleString = '<img class="ui middle aligned tiny image" src="https://thelifebook.com/images/TLB-Logo1.png">  <small><small>'+ title +'</small></small>';
		}
		
		dom.makeNode('navHeader', 'div', {css:'ui grey inverted segment', style:'background-color:#414042 !important;'});

		dom.navHeader.makeNode('logo', 'div', {src:'https://thelifebook.com/images/TLB-Logo1.png', tag:'img', css:'ui middle aligned left floated image'});
		dom.navHeader.makeNode('btns', 'div', {css:'ui buttons right floated'})

		if(backButton){
			
			dom.navHeader.btns.makeNode('back', 'div', {text:'Back', css:'ui yellow button'})
				.notify('click', {
					type:'app-navigate-to',
					data:{
						itemId:'openDoors',
						viewId:'open-door'
					}
				}, sb.moduleId);
			
		}

		dom.navHeader.btns.makeNode('see', 'div', {text:'See Life Book Churches In Your Area', css:'ui red button'})
			.notify('click', {
				type:'gideons-run',
				data:{
					run:function(dom, state, draw){
	
						findOpenDoors(dom, state, draw);
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
		dom.navHeader.makeNode('break', 'div', {text:'<br /><br />'});
		
		dom.navHeader.logo.notify('click', {
			type:'gideons-run',
			data:{
				run:function(dom, state, draw){
					
					homeScreen(dom, state, draw);
					
				}.bind({}, dom, state, draw)
			}
		}, sb.moduleId);
															
		//dom.makeNode('logobreak', 'div', {css:'ui divider'});
		
	}
				
	return {
		
		init: function() {
			
			// open the door
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'openDoors',
						title: 'Open Doors',
						icon: '<i class="fa fa-lightbulb-o"></i>',
						views: [
							{
								id:'open-door',
								type:'custom',
								title:'Open Doors',
								icon:'<i class="fa fa-lightbulb-o"></i>',
								default:true,
								dom:function(dom, state, draw){
																		
									homeScreen(dom, state, draw);
								
								}
							}
						]
					}
				}
			});
			
			// see the door
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'findOpenDoors',
						title: 'See Open Doors',
						hidden:true,
						icon: '<i class="fa fa-lightbulb-o"></i>',
						views: [
							{
								id:'tracker',
								type:'custom',
								title:'See Open Doors',
								icon:'<i class="fa fa-lightbulb-o"></i>',
								default:true,
								dom:function(dom, state, draw){
									
									findOpenDoors(dom, state, draw);
									
								}
							}
						]
					}
				}
			});
			
			// nurture the door
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'nurture-door',
						title: 'Nurture Open Doors',
						icon: '<i class="fa fa-lightbulb-o"></i>',
						views: [
							{
								id:'nurture-door',
								type:'custom',
								title:'Nurture Open Doors',
								icon:'<i class="fa fa-lightbulb-o"></i>',
								default:true,
								dom:function(dom, state, draw){
									draw(dom);
								}
							}
						]
					}
				}
			});
			
			listeners = {
				'gideons-run': this.run,
				'gideons-contact-view': this.singleContactView
			};
			
			sb.listen(listeners);
			
			//comps.projects = sb.createComponent('crud-table');
			
		},
		
		load: function(setup) {
			
			
		},
		
		destroy: function() {
			
			_.each(comps, function(v) {
				v.destroy();
			});
			
			comps = {};
			
		},
						
		// R
		run: function(data) {
			data.run(data);
		}
		
	}
	
});