Factory.register('metrics', function(sb){
	
	var comps = {},
		domObj = {},
		ui = {},
		navUI = {},
		booksUI = {},
		reqUI = {},
		startRange = moment('2022-06-01').startOf('day'),
		endRange = moment('2023-05-31').endOf('day'),
		groupBy = 'day',
		dateDiff = 0,
		incomingSources = [],
		metrics = {
			confirmedFormSubmissions:{
				name:'Requests Received',
				value:'',
				type:'getCountWhere',
				objectType:'requests',
				where:{}
			},
			approvedRequests:{
				name:'Approved Requests',
				value:'',
				type:'getCountWhere',
				objectType:'requests',
				where:{
					status:'Awaiting Shipment'
				}
			},
			approvedBooks:{
				name:'Approved Books',
				value:'',
				type:'getSum',
				objectType:'requests',
				field:'books',
				where:{
					groupBy:'day'
				}
			},
			approvalRate:{
				name:'Approval Rate',
				value:'',
				type:'',
				objectType:'submitted_form',
				where:{}
			},
			totalStudents:{
				name:'Total Students',
				value:'',
				type:'getSum',
				objectType:'requests',
				field:'students',
				where:{
					status:'Awaiting Shipment',
					groupBy:'day'
				}
			},
			booksPerStudent:{
				name:'Books Per Student',
				value:'',
				type:'',
				objectType:'requests',
				where:{}
			},
			booksPerRequest:{
				name:'Average Books Per Request',
				value:'',
				type:'',
				objectType:'requests',
				where:{}
			}
		},
		data = [];
						
	function buildNavUI(){
		
		function loading(on){
			
			if(on){
				
				this.makeNode('text', 'headerText', {size:'', css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i><br />Loading Metrics'});
				//this.makeNode('loading');
				
			}else{
				
				this.empty();
				
			}
			
			this.patch();
			
		}
		
		function showNav(){
			
			function time_range_ui (ui) {

				var placeholder = 'All time';

// 					var timeRangeOptionsReports = [
				var timeRangeOptions = [
					{
						name: 'This Season',
						value: 'this_season',
						start: moment('2022-06-01').startOf('day'),
						end: moment('2023-05-31').endOf('day'),
						selected: true
					},
					{
						name: '2021-2022',
						value: '2021_season',
						start: moment('2021-06-01').startOf('day'),
						end: moment('2022-05-31').endOf('day'),
						selected: false
					},
					{
						name: '2020-2021',
						value: '2020_season',
						start: moment('2020-06-01').startOf('day'),
						end: moment('2021-05-31').endOf('day'),
						selected: false
					},
					{
						name: '2019-2020 Season',
						value: '2019_season',
						start: moment('2019-06-01').startOf('day'),
						end: moment('2020-05-31').endOf('day'),
						selected: false
					},
					{
						name: '2018-2019 Season',
						value: '2018_season',
						start: moment('2018-06-01').startOf('day'),
						end: moment('2019-05-31').endOf('day'),
						selected: false
					},
					{
						name: '2017-2018 Season',
						value: '2017_season',
						start: moment('2017-06-01').startOf('day'),
						end: moment('2018-05-31').endOf('day'),
						selected: false
					},
					{
						name: 'Today',
						value: 'today',
						start: moment().startOf('day'),
						end: moment().endOf('day'),
						selected: false
					}
					, {
						name: 'This week',
						value: 'this_week',
						start: moment().startOf('week'),
						end: moment().endOf('week'),
						selected: false
					}
					, {
						name:'Last 30 Days'
						, value:'last_30'
						, start: moment().subtract(30,'days').startOf('day')
						, end: moment().endOf('day')
						, selected: false
					}
					, {
						name: 'This month'
						, value: 'this_month'
						, start: moment().startOf('month')
						, end: moment().endOf('month')
						, selected: false
					}
					, {
						name: 'This year'
						, value: 'this_year'
						, start: moment().startOf('year')
						, end: moment().endOf('year')
						, selected: false
					}
					, {
						name: 'Range'
						, value: 'range'
						, selected: false
					}
				];

				function date_range_filter_ui (ui) {

					function updateRange (type, value) {
						
						if(type == 'start'){
							startRange = value;
						}
						
						if(type == 'end'){
							endRange = value;
						}
						
						sb.notify({
							type:'change-metrics-view',
							data:{
								startRange:startRange,
								endRange:endRange
							}
						});

					}

					ui.makeNode('start', 'div', {
						text:
							'<div class="field">'+
								'<div class="ui calendar" id="rangestart">'+
									'<div class="ui input left icon">'+
										'<i class="calendar icon"></i>'+
										'<input type="text" placeholder="Start" value="'+ startRange.format('MM/DD/YYYY') +'">'+
									'</div>'+
								'</div>'+
							'</div>'
						, style: 'display:inline-block;'
					});

					ui.makeNode('to', 'div', {
						text:
							'<div class="field">'+
								' to '+
							'</div>'
						, style: 'display:inline-block;'
					});

					ui.makeNode('end', 'div', {
						text:
							'<div class="field">'+
								'<div class="ui calendar" id="rangeend">'+
									'<div class="ui input left icon">'+
										'<i class="calendar icon"></i>'+
										'<input type="text" placeholder="End" value="'+ endRange.format('MM/DD/YYYY') +'">'+
									'</div>'+
								'</div>'+
							'</div>'
						, style: 'display:inline-block;'
					}).listeners.push(

						function (selector) {

							$('#rangestart').calendar({
							  type: 'date'
							  , endCalendar: $('#rangeend')
							  , onChange: function (type, date, text, mode) {
							  	
							  		date = moment(date);
							  	
								  updateRange(type, date);

							  }.bind({}, 'start')
							});
							$('#rangeend').calendar({
							  type: 'date'
							  , startCalendar: $('#rangestart')
							  , onChange: function (type, date, text, mode) {
							  	
							  		date = moment(date);
							  		
								  updateRange(type, date);

							  }.bind({}, 'end')
							});

						}

					);

				}

				var selectedRange = 'this_season';

				if (
					_.findWhere(timeRangeOptions, {value: selectedRange})
				) {
					placeholder = _.findWhere(timeRangeOptions, {value: selectedRange}).name;
					_.findWhere(timeRangeOptions, {value:selectedRange}).selected = true;
				}
				
				ui.makeNode('time_range2','form',{
					timeRange:{
						name:'timeRange',
						label:'Time Range',
						type:'select',
						options:timeRangeOptions,
						change:function(form, selected){
							
							console.log('changed', selected);
							
							if(selected){

								if (selected === 'range') {

									date_range_filter_ui(ui.range);
									ui.range.patch();

								} else {

									ui.range.empty();
									ui.range.patch();
									
									sb.notify({
										type:'change-metrics-view',
										data:{
											startRange:_.findWhere(timeRangeOptions, {value:selected}).start,
											endRange:_.findWhere(timeRangeOptions, {value:selected}).end
										}
									});

								}

							}
							
						}
					}
				});

// 				ui.makeNode('time_range', 'div', {
// 					text:'<i class="clock icon"></i><span class="text">'+ placeholder +'</span><i class="dropdown icon"></i>',
// 					css:'ui dropdown dateSelect',
// 					listener:{
// 						type:		'dropdown',
// 						values:		timeRangeOptions,
// 						placeholder: placeholder,
// 						onChange:	function(value, text){
// console.log('test');
// 							if(value){
// 
// 								if (value === 'range') {
// 
// 									date_range_filter_ui(ui.range);
// 									ui.range.patch();
// 
// 								} else {
// 
// 									ui.range.empty();
// 									ui.range.patch();
// 
// 								}
// 
// 							}
// 
// 						}
// 					}
// 				});

				ui.makeNode('range', 'div', {});

/*
				if (selectedRange === 'range') {

					date_range_filter_ui(ui.range);

				}
*/

			}
			
			function updateDateRange(start, end, label) {
				$('#reportrange span').html(' '+ start.format('MM-DD-YYYY') + ' - ' + end.format('MM-DD-YYYY') +' ');
			}
			
			this.makeNode('title', 'div', {css:'ui large header', text:'Select a date range'});
			this.makeNode('date', 'div', {style:''});
/*
			this.date.makeNode('icon', 'div', {tag:'i', css:'fa fa-calendar'});
			this.date.makeNode('span', 'div', {tag:'span', text:''});
			this.date.makeNode('caret', 'div', {tag:'i', css:'fa fa-caret-down'});
*/
			
			time_range_ui (this.date, {});
			
			this.patch();
			
			// $('.dropdown .dateSelect')
			// .dropdown({
			// 	onChange: function(value, text, $selectedItem) {
			// 		console.log('changed', value);
			// 	}
			// });
			// 
			
			//var dateRangeDom = this.date.selector;
			
/*
			$(this.date.selector).daterangepicker({
			    "startDate":startRange,
			    "endDate":endRange,
				"opens":"right",
				"format":"MM-DD-YYYY",
				"showDropdowns":true,
			    "ranges": {
			        "Today": [moment().startOf('day'),moment().endOf('day')],
			        "Yesterday": [moment().subtract(1, 'day').startOf('day'), moment().subtract(1, 'day').endOf('day')],
			        "This Season": [moment('2019-06-01').startOf('day').startOf('day'),moment('2020-05-31').endOf('day')],
			        "2018-19 Season": [moment('2018-06-01').startOf('day').startOf('day'),moment('2019-05-31').endOf('day')],
					"2017-18 Season": [moment('2017-06-01').startOf('day'),moment('2018-05-31').endOf('day')],
					"2016-17 Season": [moment('2016-06-01').startOf('day'),moment('2017-05-31').endOf('day')],
			        "Last 7 Days": [moment().subtract(7, 'day').startOf('day'), moment().endOf('day')],
			        "Last 30 Days": [moment().subtract(30, 'day').startOf('day'), moment().endOf('day')],
			        "This Month": [moment().startOf('month').startOf('day'), moment().endOf('month').endOf('day')],
			        "This Year": [moment().month(0).startOf('month').startOf('day'),moment().endOf('day')],
			        "Last Month": [moment().subtract(1, 'month').startOf('month').startOf('day'),moment().subtract(1, 'month').endOf('month').endOf('day')],
			        "Last Year": [moment().subtract(1, 'year').month(0).startOf('month').startOf('day'),moment().subtract(1, 'year').month(11).endOf('month').endOf('day')]
			    }
			}, updateDateRange);
			
			updateDateRange(startRange, endRange, '');
*/
			
/*
			$(this.date.selector).on('apply.daterangepicker', function(ev, picker) {

				sb.notify({
					type:'change-metrics-view',
					data:{
						startRange:picker.startDate,
						endRange:picker.endDate
					}
				});
				
			});
*/
			
		}
		
		this.body.makeNode('date', 'column', {width:6});
		this.body.makeNode('loading', 'column', {width:6});
		
		this.body.patch();
				
		this.state.show = showNav.bind(this.body.date);
		this.state.loading = loading.bind(this.body.loading);
		
	}
	
	function buildPanelUI(){
		
		function calcChart(metrics, start, end, sumOn){
						
			var labelArr = [],
				count = 0,
				dateDiff = end.diff(start, groupBy);
			
			while(count < dateDiff){

				if(count == 0){
					labelArr.push(start.format('MM-DD-YYYY'));
				}else{
					labelArr.push(start.add(1, groupBy).format('MM-DD-YYYY'));
				}
				
				count++;
				
			}
						
			var datasets = {
					awaiting: _.map(labelArr, function(d){ var r = {date:d,sum:0}; return r; }),
					cancelled: _.map(labelArr, function(d){ var r = {date:d,sum:0}; return r; }),
					shipped: _.map(labelArr, function(d){ var r = {date:d,sum:0}; return r; }),
					hold: _.map(labelArr, function(d){ var r = {date:d,sum:0}; return r; }),
				};
				
			_.each(datasets, function(points, type){
				
				_.each(points, function(p){
					
					_.each(metrics, function(o,name){
						
						if(name == type){
							
							_.each(o, function(metric){
	
								if(
									moment(metric.grouped, 'MM-DD-YYYY').isBetween(moment(p.date, 'MM-DD-YYYY'), moment(p.date, 'MM-DD-YYYY').add(1, groupBy).endOf(groupBy))
								){
									p.sum += metric[sumOn];
								}
								
							});
							
						}
						
					});

					
				});							
				
			});	
														
			var awaiting = _.pluck(datasets.awaiting, 'sum'),
				shipped = _.pluck(datasets.shipped, 'sum');			
			
			var totalDataset = _.reduceRight(awaiting, function(a,b){ return a.concat(b + shipped[a.length]); }, []);
			
			var labels = _.map(labelArr, function(l){ return moment(l, 'MM-DD-YYYY').format('M-D-YYYY') });
			
			return {
				datasets:{
					datasets:datasets,
					totalDataset:totalDataset
				},
				labels:labels
			};

			
		}
		
		function calcGroupBy(start, end){
			
			var ret = 'day';
			
/*
			if(end.diff(start, 'days') <= 2){
				ret = 'hour';
			}
*/
			
			if(end.diff(start, 'days') > 31){
				ret = 'week';
			}
			
			if(end.diff(start, 'days') > 365){
				ret = 'month';
			}
			
			return ret;
			
		}
		
		function buildChart(metrics, start, end, sources){
			
			this.makeNode('booksLine', 'div', {css:'ui fluid segment'});
			this.booksLine.makeNode('title', 'div', {text:'Books Line Chart', css:'ui large header'});
			
			this.makeNode('reqsLine', 'div', {css:'ui fluid segment'});
			this.reqsLine.makeNode('title', 'div', {text:'Requests Line Chart', css:'ui large header'});
			
			this.makeNode('sourcesPie', 'div', {css:'ui grid'})
				.makeNode('col', 'column', {w:8})
					.makeNode('body', 'div', {css:'ui fluid segment'})
						.makeNode('title', 'div', {text:'Requests Sources Pie Chart', css:'ui large header'});
			
			this.makeNode('tableBreak', 'div', {text:'<br />'});
			
			this.makeNode('sourceTable', 'div', {css:'ui fluid segment'});
			this.sourceTable.makeNode('title', 'div', {text:'Submitted Form Source Breakdown', css:'ui large header'});
			
			this.patch();
			
//console.log(metrics, incomingSources);			
			
			/*
console.log(metrics.sources.slice(0,10));
			
			metrics.sources = metrics.sources.slice(0,10);
*/
			
			booksChartData = calcChart(metrics, start.clone(), end.clone(), 'sum');
			
			this.sourceTable.makeNode(
				'table',
				'table',
				{
					css: 'table-hover table-condensed',
					columns: {
						campaign: 'Campaign',
						requests: 'Requests',
						books: 'Books',
						bps:'Books Per Request',
						btns: ''
					}
				}
			);
			
			_.each(_.sortBy(metrics.sources, 'grouped_total').reverse(), function(source, k){
//console.log(_.where(incomingSources, {name:source.grouped}));
				
				if(1==1){
				//if(_.where(incomingSources, {name:source.grouped}).length > 0){
					
					if(1==1){
					//if(_.where(incomingSources, {name:source.grouped})[0].vendor.name == 'Facebook'){
						
						var vendor = 'Internal';
						if(_.where(incomingSources, {name:source.grouped}).length > 0){
							vendor = _.where(incomingSources, {name:source.grouped})[0].vendor.name;
						}
						
						this.sourceTable.table.makeRow(
							'row-'+k,
							[source.grouped, source.grouped_total, source.sum, Math.round(source.avg), '']
						);
						
					}
					
					
					
				}

				
				
			}, this);
			
			this.booksLine.makeNode('chart', 'chart', {
				type:'line',
				data:{
			        labels: booksChartData.labels,
			        datasets: [{
			            label: 'Cancelled Books',
			            data: _.pluck(booksChartData.datasets.datasets.cancelled, 'sum'),
			            borderWidth: 1,
			            borderColor: 'rgba(209,33,33,1)',
			            backgroundColor: 'rgba(209,33,33,1)'
			        },
			        {
			            label: 'On Hold',
			            data: _.pluck(booksChartData.datasets.datasets.hold, 'sum'),
			            borderWidth: 1,
			            borderColor: 'rgba(289,189,33,1)',
			            backgroundColor: 'rgba(289,189,33,1)'
			        },
			        {
			            label: 'Approved/Shipped',
			            data: booksChartData.datasets.totalDataset,
			            borderWidth: 1,
			            borderColor: 'rgba(51,209,33,1)',
			            backgroundColor: 'rgba(51,209,33,1)'
			        }]
			    },
			    options:{
				    scales: {
				        yAxes: [{
				            ticks: {
				                beginAtZero: true
				            },
				            stacked:true
				        }],
				        xAxes: [{
					        stacked:true
				        }]
				    }
				}
			});

			reqsChartData = calcChart(metrics, start.clone(), end.clone(), 'grouped_total');
			
			this.reqsLine.makeNode('chart', 'chart', {
				type:'line',
				data:{
			        labels: reqsChartData.labels,
			        datasets: [{
			            label: 'Cancelled Books',
			            data: _.pluck(reqsChartData.datasets.datasets.cancelled, 'sum'),
			            borderWidth: 1,
			            borderColor: 'rgba(209,33,33,1)',
			            backgroundColor: 'rgba(209,33,33,1)'
			        },
			        {
			            label: 'On Hold',
			            data: _.pluck(reqsChartData.datasets.datasets.hold, 'sum'),
			            borderWidth: 1,
			            borderColor: 'rgba(289,189,33,1)',
			            backgroundColor: 'rgba(289,189,33,1)'
			        },
			        {
			            label: 'Approved/Shipped',
			            data: reqsChartData.datasets.totalDataset,
			            borderWidth: 1,
			            borderColor: 'rgba(51,209,33,1)',
			            backgroundColor: 'rgba(51,209,33,1)'
			        }]
			    },
			    options:{
				    scales: {
				        yAxes: [{
				            ticks: {
				                beginAtZero: true
				            },
				            stacked:true
				        }],
				        xAxes: [{
					        stacked:true
				        }]
				    }	
				}
			});
			
			var colors = [];
			
			_.each(_.pluck(metrics.sources, 'grouped'), function(o){
				
				colors.push(getRandomColor());
				
			});
			
			metrics.sources = _.sortBy(metrics.sources, function(o) { return o.sum; });

			this.sourcesPie.col.body.makeNode('chart', 'chart', {
				type:'pie',
				data:{
			        labels: _.pluck(metrics.sources, 'grouped'),
			        datasets: [{
			            data: _.pluck(metrics.sources, 'sum'),
			            backgroundColor:colors
			        }]
			    },
			    options:{}
			});
			
			this.patch();
				
			}
					
		function getData(start, end, callback){
						
			groupBy = calcGroupBy(start, end);
			
			var dateDiff = end.diff(start, groupBy);
							
			var length = Object.keys(metrics).length,
				count = 0,
				start = start.clone(),
				end = end.clone(),
				allData = {},
				allStudentData = {};
				dom = this;
				
			sb.data.db.obj.getSum('requests', 'books', {
				status:'Shipped',
				groupBy:'year',
				dateRange:{
					start: moment('2022-05-31 11:59:59', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS'),
					end: moment('2023-06-01 01:00:01', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS')
				}
			}, function(totalBookData){
				
				sb.data.db.obj.getSum('requests', 'students', {
					status:'Shipped',
					groupBy:'year',
					dateRange:{
						start: moment('2022-05-31 11:59:59', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS'),
						end: moment('2023-06-01 01:00:01', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS')
					}
				}, function(totalStudentData){
								
					//var totalBooksFromOldSystem = 33493084;
					//var totalStudentsFromOldSystem = 2500293;
					// var totalBooksFromOldSystem = 38648884;
					// var totalStudentsFromOldSystem = 2874279;
					var totalBooksFromOldSystem = 49529284; 
					var totalStudentsFromOldSystem = 3665049;
					var runningTotalBooks = 0;
					var runningTotalStudents = 0;
											
					runningTotalBooks = totalBooksFromOldSystem;
					runningTotalStudents = totalStudentsFromOldSystem;
					
					_.each(totalBookData, function(totalData){
						
						runningTotalBooks += totalData.sum; 
						
					});	
					
					_.each(totalStudentData, function(totalData){
						
						runningTotalStudents += totalData.sum; 
						
					});	
											
					// top row
					dom.one.runningTotalBooks.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber(runningTotalBooks)});
					dom.one.runningTotalStudents.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber(runningTotalStudents)});
					
					dom.one.runningTotalBooks.patch();

					sb.data.db.obj.getSum('requests', 'books', {
						status:'Cancelled',
						groupBy:groupBy,
						dateRange:{
							start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
							end:end.format('YYYY-MM-DD HH:mm:ss.SS')
						}
					}, function(data){
						
						allData.cancelled = _.map(data, function(m,k){
							m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
							return m;
						});
						
						sb.data.db.obj.getSum('requests', 'books', {
							status:'On Hold',
							groupBy:groupBy,
							dateRange:{
								start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
								end:end.format('YYYY-MM-DD HH:mm:ss.SS')
							}
						}, function(data){
							
							allData.hold = _.map(data, function(m,k){
								m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
								return m;
							});
							
							sb.data.db.obj.getSum('requests', 'books', {
								status:'Awaiting Shipment',
								groupBy:groupBy,
								dateRange:{
									start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
									end:end.format('YYYY-MM-DD HH:mm:ss.SS')
								}
							}, function(data){
								
								allData.awaiting = _.map(data, function(m,k){
									m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
									return m;
								});
								
								sb.data.db.obj.getSum('requests', 'books', {
									status:'Shipped',
									groupBy:groupBy,
									dateRange:{
										start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
										end:end.format('YYYY-MM-DD HH:mm:ss.SS')
									}
								}, function(data){
									
									allData.shipped = _.map(data, function(m,k){
										m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
										return m;
									});
		
									var totals = {
											books:{
												total: 0,
												cancelled:_.reduce(allData.cancelled, function(memo, o){ return o.sum + memo; }, 0),
												onHold:_.reduce(allData.hold, function(memo, o){ return o.sum + memo; }, 0),
												shipped:_.reduce(allData.shipped, function(memo, o){ return o.sum + memo; }, 0),
												approved:_.reduce(allData.awaiting, function(memo, o){ return o.sum + memo; }, 0),
											},
											reqs:{
												total: 0,
												cancelled:_.reduce(allData.cancelled, function(memo, o){ return o.grouped_total + memo; }, 0),
												onHold:_.reduce(allData.hold, function(memo, o){ return o.grouped_total + memo; }, 0),
												shipped:_.reduce(allData.shipped, function(memo, o){ return o.grouped_total + memo; }, 0),
												approved:_.reduce(allData.awaiting, function(memo, o){ return o.grouped_total + memo; }, 0),
											}
										};
										
									_.each(totals, function(o, k){
		
										totals[k].total = 	_.reduce(totals[k], function(memo, o){ return o + memo; }, 0)		
																		
									});
		
									// we have everything to print book stats
									
									// top row
									dom.two.totalBooks.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber((totals.books.shipped + totals.books.approved))});
									dom.two.totalRequests.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber((totals.reqs.shipped + totals.reqs.approved))});
									dom.two.bps.makeNode('value', 'div', {css:'value', text: ((totals.books.shipped + totals.books.approved) / (totals.reqs.shipped + totals.reqs.approved)).toFixed(2) + '<br /><span class="recent-bps"><small><small><i class="fa fa-circle-o-notch fa-spin"></i></small></small></span>'});
									
									// second row
									dom.three.booksOnHold.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber(totals.books.onHold)});
									dom.three.booksCancelled.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber(totals.books.cancelled)});
									dom.three.booksApproved.makeNode('value', 'div', {css:'value', text: sb.dom.formatNumber((totals.books.shipped + totals.books.approved))});
									dom.three.booksApprovalRate.makeNode('value', 'div', {css:'value', text: ( (((totals.books.shipped + totals.books.approved) / (totals.books.cancelled + totals.books.shipped + totals.books.approved)) * 100)).toFixed(2) + '%' });
		
									// third row
									dom.four.requestsOnHold.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber(totals.reqs.onHold)});
									dom.four.requestsCancelled.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber(totals.reqs.cancelled)});
									dom.four.requestsApproved.makeNode('value', 'div', {css:'value', text: sb.dom.formatNumber((totals.reqs.shipped + totals.reqs.approved))});
									dom.four.requestApprovalRate.makeNode('value', 'div', {css:'value', text: ( (((totals.reqs.shipped + totals.reqs.approved) / (totals.reqs.cancelled + totals.reqs.shipped + totals.reqs.approved)) * 100)).toFixed(2) + '%' });
							
									dom.patch();
																
									sb.data.db.obj.getAll('incoming_form_sources', function(sources){
										
										incomingSources = sources;
										
										var gideonTotal = 0,
											gideonPercent = 0;
										
										sb.data.db.obj.getSum('requests', 'books', {
											groupBy:groupBy,
											status:'Shipped',
											groupOn:'source',
											dateRange:{
												start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
												end:end.format('YYYY-MM-DD HH:mm:ss.SS')
											}
										}, function(requestsBySource){
											
											var sourceData = _.map(requestsBySource, function(m,k){
												
												var source = _.where(sources, {id: +m.grouped}),
													sourceName = 'Internal';

												if(source.length > 0){
													
													if(source[0].name){
														
														if(source[0].name == 'Gideon'){

															gideonTotal = m.sum;
														}
														
														m.grouped = _.where(sources, {id: +m.grouped})[0].name;
													}else{
														m.grouped = sourceName;
													}
													
												}
								
												return m;
											});

											if(gideonTotal > 0){
												
												gideonPercent = (gideonTotal / (totals.books.shipped + totals.books.approved) ) * 100;
												
											}
											
											dom.two.gideonSeeded.makeNode('value', 'div', {css:'value', text: gideonPercent.toFixed(2) + '%<br /><small><small>Total Books: '+ sb.dom.formatNumber(gideonTotal) +'</smalll></small>' });
											
											dom.two.gideonSeeded.patch();
											
										});
										
										sb.data.db.obj.getSum('submitted_form', 'books', {
											groupBy:groupBy,
											confirmed:'yes',
											groupOn:{
												prop:'form_data',
												childProp:'request_source'
											},
											dateRange:{
												start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
												end:end.format('YYYY-MM-DD HH:mm:ss.SS')
											}
										}, function(data){

											allData.sources = _.map(data, function(m,k){
												
												var source = _.where(sources, {id: +m.grouped}),
													sourceName = 'Internal';

												if(source.length > 0){
													
													if(source[0].name){
																												
														m.grouped = _.where(sources, {id: +m.grouped})[0].name;
													}else{
														m.grouped = sourceName;
													}
													
												}
								
												return m;
											});
											
											callback(allData);
											
											getRecentBPS(moment().subtract(14, 'days'), moment().subtract(7, 'days'), function(done){

												//callback(allData);
												
											});
											
										});
										
									}, 1);
									
								});
								
							});				
							
						});
						
					});
					
					sb.data.db.obj.getSum('requests', 'students', {
						status:'Cancelled',
						groupBy:groupBy,
						dateRange:{
							start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
							end:end.format('YYYY-MM-DD HH:mm:ss.SS')
						}
					}, function(studentData){
						
						allStudentData.cancelled = _.map(studentData, function(m,k){
							m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
							return m;
						});
						
						sb.data.db.obj.getSum('requests', 'students', {
							status:'On Hold',
							groupBy:groupBy,
							dateRange:{
								start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
								end:end.format('YYYY-MM-DD HH:mm:ss.SS')
							}
						}, function(studentData){
							
							allStudentData.hold = _.map(studentData, function(m,k){
								m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
								return m;
							});
							
							sb.data.db.obj.getSum('requests', 'students', {
								status:'Awaiting Shipment',
								groupBy:groupBy,
								dateRange:{
									start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
									end:end.format('YYYY-MM-DD HH:mm:ss.SS')
								}
							}, function(studentData){
								
								allStudentData.awaiting = _.map(studentData, function(m,k){
									m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
									return m;
								});
								
								sb.data.db.obj.getSum('requests', 'students', {
									status:'Shipped',
									groupBy:groupBy,
									dateRange:{
										start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
										end:end.format('YYYY-MM-DD HH:mm:ss.SS')
									}
								}, function(studentData){
									
									allStudentData.shipped = _.map(studentData, function(m,k){
										m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
										return m;
									});

									var studentTotals = {
											books:{
												total: 0,
												cancelled:_.reduce(allStudentData.cancelled, function(memo, o){ return o.sum + memo; }, 0),
												onHold:_.reduce(allStudentData.hold, function(memo, o){ return o.sum + memo; }, 0),
												shipped:_.reduce(allStudentData.shipped, function(memo, o){ return o.sum + memo; }, 0),
												approved:_.reduce(allStudentData.awaiting, function(memo, o){ return o.sum + memo; }, 0),
											},
											reqs:{
												total: 0,
												cancelled:_.reduce(allStudentData.cancelled, function(memo, o){ return o.grouped_total + memo; }, 0),
												onHold:_.reduce(allStudentData.hold, function(memo, o){ return o.grouped_total + memo; }, 0),
												shipped:_.reduce(allStudentData.shipped, function(memo, o){ return o.grouped_total + memo; }, 0),
												approved:_.reduce(allStudentData.awaiting, function(memo, o){ return o.grouped_total + memo; }, 0),
											}
										};
										
									_.each(studentTotals, function(o, k){
		
										studentTotals[k].total = 	_.reduce(studentTotals[k], function(memo, o){ return o + memo; }, 0)		
																		
									});
		
		
									// fifth row
									dom.five.requestsOnHold.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber(studentTotals.books.onHold)});
									dom.five.requestsCancelled.makeNode('value', 'div', {css:'value', text:sb.dom.formatNumber(studentTotals.books.cancelled)});
									dom.five.requestsApproved.makeNode('value', 'div', {css:'value', text: sb.dom.formatNumber((studentTotals.books.shipped + studentTotals.books.approved))});
									dom.five.requestApprovalRate.makeNode('value', 'div', {css:'value', text: ( (((studentTotals.books.shipped + studentTotals.books.approved) / (studentTotals.books.cancelled + studentTotals.books.shipped + studentTotals.books.approved)) * 100)).toFixed(2) + '%' });
							
									dom.patch();
																
								
									
								});
								
							});				
							
						});
						
					});
				
				});
							
			});
			
		}
		
		function getRecentBPS(start, end, callback){
						
			groupBy = calcGroupBy(start, end);
			
			var dateDiff = end.diff(start, groupBy);
							
			var length = Object.keys(metrics).length,
				count = 0,
				start = start.clone(),
				end = end.clone(),
				allData = {},
				dom = this;
				
			sb.data.db.obj.getSum('requests', 'books', {
				status:'Shipped',
				groupBy:'year',
				dateRange:{
					start: moment('2022-05-31 11:59:59', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS'),
					end: moment('2023-06-01 01:00:01', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS')
				}
			}, function(totalBookData){
				
				sb.data.db.obj.getSum('requests', 'students', {
					status:'Shipped',
					groupBy:'year',
					dateRange:{
						start: moment('2022-05-31 11:59:59', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS'),
						end: moment('2023-06-01 01:00:01', 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD HH:mm:ss.SS')
					}
				}, function(totalStudentData){
								
					var totalBooksFromOldSystem = 49529284,
						totalStudentsFromOldSystem = 3665049,
						runningTotalBooks = 0,
						runningTotalStudents = 0;
						
					if(totalBookData[0]){
						
						runningTotalBooks = totalBooksFromOldSystem + totalBookData[0].sum;
						runningTotalStudents = totalStudentsFromOldSystem + totalStudentData[0].sum;						
						
					}	
											
					sb.data.db.obj.getSum('requests', 'books', {
						status:'Cancelled',
						groupBy:groupBy,
						dateRange:{
							start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
							end:end.format('YYYY-MM-DD HH:mm:ss.SS')
						}
					}, function(data){
						
						allData.cancelled = _.map(data, function(m,k){
							m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
							return m;
						});
						
						sb.data.db.obj.getSum('requests', 'books', {
							status:'On Hold',
							groupBy:groupBy,
							dateRange:{
								start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
								end:end.format('YYYY-MM-DD HH:mm:ss.SS')
							}
						}, function(data){
							
							allData.hold = _.map(data, function(m,k){
								m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
								return m;
							});
							
							sb.data.db.obj.getSum('requests', 'books', {
								status:'Awaiting Shipment',
								groupBy:groupBy,
								dateRange:{
									start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
									end:end.format('YYYY-MM-DD HH:mm:ss.SS')
								}
							}, function(data){
								
								allData.awaiting = _.map(data, function(m,k){
									m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
									return m;
								});
								
								sb.data.db.obj.getSum('requests', 'books', {
									status:'Shipped',
									groupBy:groupBy,
									dateRange:{
										start: start.format('YYYY-MM-DD HH:mm:ss.SS'),
										end:end.format('YYYY-MM-DD HH:mm:ss.SS')
									}
								}, function(data){
									
									allData.shipped = _.map(data, function(m,k){
										m.grouped = moment(m.grouped, 'YYYY-MM-DD HH:mm:ss.SS').format('MM-DD-YYYY');
										return m;
									});
		
									var totals = {
											books:{
												total: 0,
												cancelled:_.reduce(allData.cancelled, function(memo, o){ return o.sum + memo; }, 0),
												onHold:_.reduce(allData.hold, function(memo, o){ return o.sum + memo; }, 0),
												shipped:_.reduce(allData.shipped, function(memo, o){ return o.sum + memo; }, 0),
												approved:_.reduce(allData.awaiting, function(memo, o){ return o.sum + memo; }, 0),
											},
											reqs:{
												total: 0,
												cancelled:_.reduce(allData.cancelled, function(memo, o){ return o.grouped_total + memo; }, 0),
												onHold:_.reduce(allData.hold, function(memo, o){ return o.grouped_total + memo; }, 0),
												shipped:_.reduce(allData.shipped, function(memo, o){ return o.grouped_total + memo; }, 0),
												approved:_.reduce(allData.awaiting, function(memo, o){ return o.grouped_total + memo; }, 0),
											}
										};
										
									_.each(totals, function(o, k){
		
										totals[k].total = 	_.reduce(totals[k], function(memo, o){ return o + memo; }, 0)		
																		
									});
		
									$('.recent-bps').html('<small><small>(Recently '+ ((totals.books.shipped + totals.books.approved) / (totals.reqs.shipped + totals.reqs.approved)).toFixed(2) +')</small></small>');
									
									callback(true);
																																		
								});
								
							});				
							
						});
						
					});
				
				});
							
			});
			
		}
		
		function getRandomColor(){
		    
		    var letters = '0123456789ABCDEF',
		    	color = '#';
		    
		    for(var i = 0; i < 6; i++) {
		    
		        color += letters[Math.floor(Math.random() * 16)];
		    
		    }
		    
		    return color;
		}
				
		function showBoxes(){
			
			// total row
			//this.makeNode('runningTotal', 'column', {width:6}).makeNode('total', 'panel', {header:'Alltime Total Shipped Books'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
			
			this.makeNode('one', 'div', {css:'two ui statistics'});
			this.one.makeNode('runningTotalBooks', 'div', {css:'ui statistic'});
			this.one.runningTotalBooks.makeNode('label', 'div', {text:'All-time Total Books', css:'label'});
			this.one.runningTotalBooks.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});
			
			this.one.makeNode('runningTotalStudents', 'div', {css:'statistic'})
			this.one.runningTotalStudents.makeNode('label', 'div', {text:'All-time Total Students', css:'label'});
			this.one.runningTotalStudents.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});
			
						
			// running total row
/*
			this.makeNode('runningTotalBooks', 'column', {width:6}).makeNode('total', 'panel', {header:'All-time Total Books'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});

			this.makeNode('runningTotalStudents', 'column', {width:6}).makeNode('total', 'panel', {header:'All-time Total Students'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
*/

			// top row
			this.makeNode('twoBreak', 'div', {text:'<br /><br /><br />'});
			this.makeNode('two', 'div', {css:'four ui tiny statistics'});
			
			this.two.makeNode('totalBooks', 'div', {css:'ui statistic'});
			this.two.totalBooks.makeNode('label', 'div', {text:'Total Approved Books', css:'label'});
			this.two.totalBooks.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.two.makeNode('totalRequests', 'div', {css:'ui statistic'});
			this.two.totalRequests.makeNode('label', 'div', {text:'Total Approved Requests', css:'label'});
			this.two.totalRequests.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.two.makeNode('bps', 'div', {css:'ui statistic'});
			this.two.bps.makeNode('label', 'div', {text:'Books Per Saturation', css:'label'});
			this.two.bps.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.two.makeNode('gideonSeeded', 'div', {css:'ui statistic'});
			this.two.gideonSeeded.makeNode('label', 'div', {text:'Gideon Seeded %', css:'label'});
			this.two.gideonSeeded.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

/*
			this.makeNode('totalBooks', 'column', {width:3}).makeNode('total', 'panel', {header:'Total Approved Books'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});

			this.makeNode('totalRequests', 'column', {width:3}).makeNode('total', 'panel', {header:'Total Approved Requests'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
			
			this.makeNode('bps', 'column', {width:3}).makeNode('total', 'panel', {header:'Books Per Saturation'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});

			this.makeNode('gideonSeeded', 'column', {width:3}).makeNode('total', 'panel', {header:'Gideon Seeded %'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
*/
			
			// second row
			this.makeNode('threeBreak', 'div', {text:'<br /><br /><br />'});
			this.makeNode('three', 'div', {css:'four ui tiny statistics'});

			this.three.makeNode('booksOnHold', 'div', {css:'ui statistic'});
			this.three.booksOnHold.makeNode('label', 'div', {text:'Books On Hold', css:'label'});
			this.three.booksOnHold.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.three.makeNode('booksCancelled', 'div', {css:'ui statistic'});
			this.three.booksCancelled.makeNode('label', 'div', {text:'Books Cancelled', css:'label'});
			this.three.booksCancelled.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.three.makeNode('booksApproved', 'div', {css:'ui statistic'});
			this.three.booksApproved.makeNode('label', 'div', {text:'Books Approved & Shipped', css:'label'});
			this.three.booksApproved.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.three.makeNode('booksApprovalRate', 'div', {css:'ui statistic'});
			this.three.booksApprovalRate.makeNode('label', 'div', {text:'Books Approval Rate', css:'label'});
			this.three.booksApprovalRate.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});



/*
			this.makeNode('booksOnHold', 'column', {width:3}).makeNode('total', 'panel', {header:'Books On Hold'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});

			this.makeNode('booksCancelled', 'column', {width:3}).makeNode('total', 'panel', {header:'Books Cancelled'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
			
			this.makeNode('booksApproved', 'column', {width:3}).makeNode('total', 'panel', {header:'Books Approved & Shipped'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});

			this.makeNode('booksApprovalRate', 'column', {width:3}).makeNode('total', 'panel', {header:'Books Approval Rate'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
*/

			// third row
			this.makeNode('fourBreak', 'div', {text:'<br /><br /><br />'});
			this.makeNode('four', 'div', {css:'four ui tiny statistics'});

			this.four.makeNode('requestsOnHold', 'div', {css:'ui statistic'});
			this.four.requestsOnHold.makeNode('label', 'div', {text:'Requests On Hold', css:'label'});
			this.four.requestsOnHold.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.four.makeNode('requestsCancelled', 'div', {css:'ui statistic'});
			this.four.requestsCancelled.makeNode('label', 'div', {text:'Requests Cancelled', css:'label'});
			this.four.requestsCancelled.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.four.makeNode('requestsApproved', 'div', {css:'ui statistic'});
			this.four.requestsApproved.makeNode('label', 'div', {text:'Requests Approved & Shipped', css:'label'});
			this.four.requestsApproved.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.four.makeNode('requestApprovalRate', 'div', {css:'ui statistic'});
			this.four.requestApprovalRate.makeNode('label', 'div', {text:'Requests Approval Rate', css:'label'});
			this.four.requestApprovalRate.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.makeNode('fiveBreak', 'div', {text:'<br /><br /><br />'});
			this.makeNode('five', 'div', {css:'four ui tiny statistics'});

			this.five.makeNode('requestsOnHold', 'div', {css:'ui statistic'});
			this.five.requestsOnHold.makeNode('label', 'div', {text:'Students On Hold', css:'label'});
			this.five.requestsOnHold.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.five.makeNode('requestsCancelled', 'div', {css:'ui statistic'});
			this.five.requestsCancelled.makeNode('label', 'div', {text:'Students Cancelled', css:'label'});
			this.five.requestsCancelled.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.five.makeNode('requestsApproved', 'div', {css:'ui statistic'});
			this.five.requestsApproved.makeNode('label', 'div', {text:'Students Approved & Shipped', css:'label'});
			this.five.requestsApproved.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});

			this.five.makeNode('requestApprovalRate', 'div', {css:'ui statistic'});
			this.five.requestApprovalRate.makeNode('label', 'div', {text:'Students Approval Rate', css:'label'});
			this.five.requestApprovalRate.makeNode('value', 'div', {css:'value', text:'<i class="fa fa-refresh fa-spin"></i>'});



/*
			this.makeNode('requestsOnHold', 'column', {width:3}).makeNode('total', 'panel', {header:'Requests On Hold'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});

			this.makeNode('requestsCancelled', 'column', {width:3}).makeNode('total', 'panel', {header:'Requests Cancelled'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
			
			this.makeNode('requestsApproved', 'column', {width:3}).makeNode('total', 'panel', {header:'Requests Approved & Shipped'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});

			this.makeNode('requestApprovalRate', 'column', {width:3}).makeNode('total', 'panel', {header:'Requests Approval Rate'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
*/

/*
			this.makeNode('requestsCancelled', 'column', {width:3}).makeNode('total', 'panel', {header:'Requests Cancelled'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
			
			this.makeNode('requestsApproved', 'column', {width:3}).makeNode('total', 'panel', {header:'Requests Approved & Shipped'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});

			this.makeNode('requestApprovalRate', 'column', {width:3}).makeNode('total', 'panel', {header:'Requests Approval Rate'}).body.makeNode('text', 'headerText', {css:'text-center', text:'<i class="fa fa-refresh fa-spin fa-2x"></i>'});
*/

			this.patch();
			
		}
				
		this.body.makeNode('loading', 'column', {columns:12});
		
		this.body.makeNode('boxes', 'column', {columns:12});
		
		this.body.makeNode('charts', 'column', {columns:12});
		
		this.patch();
		
		this.state.layout = showBoxes.bind(this.body.boxes);
		this.state.update = getData.bind(this.body.boxes);
		this.state.charts = buildChart.bind(this.body.charts);
		
	}
	
	return {
		
		init: function(){
			
			sb.listen({
				'change-metrics-view': this.changeView,
				'metrics-load': this.load,
				'metrics-destroy': this.destroy
			});

			sb.notify({
				type: 'side-nav-register-page',
				data: {
					pageName: '<i class="fa fa-line-chart"></i> Metrics',
					pageModuleId: sb.moduleId
				}
			});
			
			if(
				appConfig.instance == 'thelifebook'
				||
				appConfig.instance == 'rickyvoltz'
			){
				
				sb.notify({
					type:'register-application',
					data:{
						navigationItem:{
							id:'lifebook-metrics',
							title:'Metrics',
							icon:'<i class="fa fa-bar-chart"></i>',
							views:[
								{
									id:'create',
									type:'custom',
									title:'Report Dashboard',
									icon:'<i class="fa fa-bar-chart"></i>',
									default:true,
									dom:function(dom, state, draw){
										
										draw(false);
										
										startRange = moment('2022-06-01').startOf('day');
										endRange = moment('2023-05-31').endOf('day');
																			
										ui = sb.dom.make(dom.selector);
										
										ui.makeNode('navigation', 'div', {css:'ui basic segment'}).makeNode('body', 'div', {css:'ui grid'});
										ui.makeNode('break', 'div', {text:'<br />'});
										ui.makeNode('books', 'div', {css:'pda-container pda-Panel pda-background-gray pda-panel-gray'}).makeNode('body', 'div', {css:'ui grid'});
										
										navUI = ui.navigation;
										navUI.state = buildNavUI;
										
										booksUI = ui.books;
										booksUI.state = buildPanelUI;
																
										ui.build();
										
										navUI.state();
										navUI.state.show();
										
										booksUI.state();
										
										navUI.state.loading(true);
							
										booksUI.state.layout();
										
										booksUI.state.update(startRange.clone(), endRange.clone(), function(allData){
							
											// stats are on the screen now
											booksUI.state.charts(allData, startRange.clone(), endRange.clone());
											
											navUI.state.loading(false);
																						
										});
										
									}
								},
								{
									id:'metricsTool',
									type:'hqTool',
									name:'Metrics',
									tip:'Request Metrics.',
									icon: {
										type: 'chart line',
										color: 'blue'
									},
									default:true,
									mainViews:[
										{
											dom:function(dom, state, draw, mainDom){
											
												startRange = moment('2022-06-01').startOf('day');
												endRange = moment('2023-05-31').endOf('day');
																					
												ui = sb.dom.make(dom.selector);
												
												ui.makeNode('navigation', 'div', {css:'ui basic segment'}).makeNode('body', 'div', {css:'ui grid'});
												ui.makeNode('break', 'div', {text:'<br />'});
												ui.makeNode('books', 'div', {css:'pda-container pda-Panel pda-background-gray pda-panel-gray'}).makeNode('body', 'div', {css:'ui grid'});
												
												navUI = ui.navigation;
												navUI.state = buildNavUI;
												
												booksUI = ui.books;
												booksUI.state = buildPanelUI;
																		
												ui.build();
												
												navUI.state();
												navUI.state.show();
												
												booksUI.state();
												
												navUI.state.loading(true);
									
												booksUI.state.layout();
												
												booksUI.state.update(startRange.clone(), endRange.clone(), function(allData){
									
													// stats are on the screen now
													booksUI.state.charts(allData, startRange.clone(), endRange.clone());
													
													navUI.state.loading(false);
																								
												});
																						
											}
										}
									]
								},
								{
									id:'metricsTool',
									type:'teamTool',
									name:'Metrics',
									tip:'Request Metrics.',
									icon: {
										type: 'chart line',
										color: 'blue'
									},
									default:true,
									mainViews:[
										{
											dom:function(dom, state, draw, mainDom){
											
												startRange = moment('2022-06-01').startOf('day');
												endRange = moment('2023-05-31').endOf('day');
																					
												ui = sb.dom.make(dom.selector);
												
												ui.makeNode('navigation', 'div', {css:'ui basic segment'}).makeNode('body', 'div', {css:'ui grid'});
												ui.makeNode('break', 'div', {text:'<br />'});
												ui.makeNode('books', 'div', {css:'pda-container pda-Panel pda-background-gray pda-panel-gray'}).makeNode('body', 'div', {css:'ui grid'});
												
												navUI = ui.navigation;
												navUI.state = buildNavUI;
												
												booksUI = ui.books;
												booksUI.state = buildPanelUI;
																		
												ui.build();
												
												navUI.state();
												navUI.state.show();
												
												booksUI.state();
												
												navUI.state.loading(true);
									
												booksUI.state.layout();
												
												booksUI.state.update(startRange.clone(), endRange.clone(), function(allData){
									
													// stats are on the screen now
													booksUI.state.charts(allData, startRange.clone(), endRange.clone());
													
													navUI.state.loading(false);
																								
												});
																						
											}
										}
									]
								}
							]
						}
					}
				});
	

			}
						
		},
		
		load: function(setup){
			
			startRange = moment('2022-06-01').startOf('day');
			endRange = moment('2023-05-31').endOf('day');
			
			domObj = sb.dom.make(setup.domObj.selector);
			
			ui = sb.dom.make(setup.domObj.selector);
			
			navUI = ui.makeNode('navigation', 'panel', {header:'Setup', css:'pda-panel-blue'});
			navUI.state = buildNavUI;
			
			booksUI = ui.makeNode('books', 'panel', {header:'Stats', css:'pda-panel-blue'});
			booksUI.state = buildPanelUI;
									
			ui.build();
			
			navUI.state();
			navUI.state.show();
			
			booksUI.state();
			
			navUI.state.loading(true);

			booksUI.state.layout();
			
			booksUI.state.update(startRange.clone(), endRange.clone(), function(allData){

				// stats are on the screen now
/*
				booksUI.state.charts(allData, startRange.clone(), endRange.clone());
				
				navUI.state.loading(false);
*/
				
			});
									
		},
		
		destroy: function(){
			
			_.each(comps, function(comp){
				comp.destroy();
			});
			
			comps = {},
			domObj = {};
		
		},
		
		changeView: function(data){
console.log('data',data);			
			navUI.state.loading(true);

			startRange = data.startRange.clone();
			endRange = data.endRange.clone()
console.log('startRange',startRange,endRange);
			booksUI.state.update(startRange.clone(), endRange.clone(), function(allData){

				// stats are on the screen now
				booksUI.state.charts(allData, startRange.clone(), endRange.clone());
				
				navUI.state.loading(false);
				
			});
			
		}
		
	}
	
});