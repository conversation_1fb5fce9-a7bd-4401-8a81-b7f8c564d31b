Factory.register('lifebookinternational-component', function(sb){
	
	var ui = {},
		components = {};
	
	function getSettings(callback, childObjs){
		
		if(!childObjs){
			childObjs = 0;
		}
			
		sb.data.db.obj.getAll('life_book_system', function(settings){
			
			if(settings.length > 0){
				
				callback(settings[0]);
				
			}else{
				
				sb.data.db.obj.create('life_book_system', {new_web_form:[], new_order:[]}, function(settings){
					
					callback(settings);
					
				}, childObjs);
				
			}			
			
		}, childObjs);
		
	}
	
	function homeView(dom, bp, obj, stats){
		
		var startRange = moment().startOf('month'),
			endRange = moment().endOf('month');
			
		statsView.call(dom, startRange, endRange);
		
	}
	
	function notificationSettings(dom, bp, skipLoader){
		
		if(!skipLoader){
			
			dom.empty();
		
			dom.makeNode('loader', 'loader', {});
			
			dom.patch();
			
		}
		
		getSettings(function(settings){

			sb.data.db.obj.getAll('users', function(users){
				
				dom.makeNode('title', 'headerText', {text:'Notification Settings', size:'small'});
				
				dom.makeNode('break', 'lineBreak', {});
				
				var formObj = {
						new_web_form:{
							name:'new_web_form',
							label:'Who will be notified when a website form is filled out?',
							type:'checkbox',
							options:_.map(users, function(u){
								
								var ret = {
										name:'new_web_form',
										value:u.id,
										label:u.fname +' '+ u.lname
									};
								
								if(settings.new_web_form.indexOf(u.id) > -1){
									ret.checked = true;
								}
								
								return ret;
							})
						},
						new_order:{
							name:'new_order',
							label:'Who will be notified when Life Books are dropped off?',
							type:'checkbox',
							options:_.map(users, function(u){
								var ret = {
										name:'new_order',
										value:u.id,
										label:u.fname +' '+ u.lname
									};
								
								if(settings.new_order.indexOf(u.id) > -1){
									ret.checked = true;
								}
								
								return ret;
							})
						}
					};
					
				dom.makeNode('form', 'form', formObj);
				
				dom.makeNode('formBreak', 'lineBreak', {});
				
				dom.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Update', css:'pda-btn-green'}).notify('click', {
					type:'lifebookinternational-run',
					data:{
						run:function(settings){
							
							var dom = this,
								formObj = dom.form.process();

							dom.save.loading();
							
							settings.new_web_form = formObj.fields.new_web_form.value;
							settings.new_order = formObj.fields.new_order.value;
							
							sb.data.db.obj.update('life_book_system', settings, function(updatedSettings){
								
								notificationSettings.call(dom, dom, bp, true);
								
							});
							
						}.bind(dom, settings)
					}
				}, sb.moduleId);
					
				delete dom.loader;
				
				dom.patch();
				
			});
			
			
		});
				
	}
			
	function singleView(contactObj){
		
		var contact = {},
			listDom = {},
			createDom = {};
		
		function buildList(keepScreen){
			
			var dom = this;			
			
			if(!keepScreen){
				
				dom.empty();
							
			}
			
			if(keepScreen){
				
				//dom.makeNode('loader', 'loader', {});
				
			}
			
			dom.patch();

			sb.data.db.obj.getWhere('lifebook_international_orders', {contact:contact.id, childObjs:2}, function(orders){
				
				dom.empty();
				
				if(orders.length == 0){
					
					dom.makeNode('title', 'headerText', {text:'No Life Book orders', size:'x-small'});
					
				}else{
					
					var totalBooks = _.reduce(_.pluck(orders, 'amount'), function(memo, num){ return memo + num; }, 0);
					
					dom.makeNode('title', 'headerText', {text: orders.length + ' order(s). ' + totalBooks +' total books.', size:'x-small'});
					
					dom.makeNode('table', 'table', {columns:{
						amount:'Amount',
						status:'Order Status',
						date_created:'Ordered On',
						created_by:'Ordered By',
						manager:'Point Person',
						btns:'Actions'
					}});
					
					_.each(orders, function(o){
						
						var orderStatus = '';
						
						if(o.status == 'Delivered'){
							
							orderStatus = '<span class="label label-success">Delivered</span>';
							
						}else{
							
							orderStatus = '<span class="label label-warning">Not Delivered</span>';
							
						}
						
						dom.table.makeRow('row-'+o.id, [
							o.amount +' books',
							orderStatus,
							moment(o.order_date, 'YYYY-MM-DD HH:mm').fromNow(),
							o.created_by.fname +' '+ o.created_by.lname,
							o.manager.fname +' '+ o.manager.lname,
							''
						]);
												
						if(o.status == 'Delivered'){
							
							dom.table.body['row-'+o.id].btns.makeNode('status', 'button', {text:'<i class="fa fa-truck"></i> Change Status', css:'pda-btn-primary'}).notify('click', {
								type:'lifebookinternational-run',
								data:{
									run:function(dom, obj){
										
										sb.dom.alerts.ask({
											title: 'Change status to Not Delivered?',
											text: ''
										}, function(resp){
											
											if(resp){
												
												swal.disableButtons();
												
												sb.data.db.obj.update('lifebook_international_orders', {id:obj.id, status:'Not Delivered'}, function(updated){
																		
													var notesToCreate = {
															type_id: updated.contact,
															type: 'contacts',
															note: 'Order #'+ updated.object_uid +' has been marked as Not Delivered.',
															note_type:1153211,
															author: sb.data.cookie.get('uid'),
															notifyUsers: []
														};
		
													sb.data.db.obj.create('notes', notesToCreate, function(newNote){
														
														sb.notify({
															type:'update-contact-note-feed',
															data:{
																object:newNote
															}
														});
														
														swal.close();
														
														buildList.call(dom, true);
														
													});
													
												});
												
											}
										
										});
										
									}.bind({}, dom, o)
								}
							}, sb.moduleId);
							
						}else{
							
							dom.table.body['row-'+o.id].btns.makeNode('status', 'button', {text:'<i class="fa fa-truck"></i> Change Status', css:'pda-btn-primary'}).notify('click', {
								type:'lifebookinternational-run',
								data:{
									run:function(dom, obj){
										
										sb.dom.alerts.ask({
											title: 'Change status to Delivered?',
											text: ''
										}, function(resp){
											
											if(resp){
												
												swal.disableButtons();
												
												sb.data.db.obj.update('lifebook_international_orders', {id:obj.id, status:'Delivered'}, function(updated){
																		
													var notesToCreate = {
															type_id: updated.contact,
															type: 'contacts',
															note: 'Order #'+ updated.object_uid +' has been marked as Delivered.',
															note_type:1153211,
															author: sb.data.cookie.get('uid'),
															notifyUsers: []
														};
		
													sb.data.db.obj.create('notes', notesToCreate, function(newNote){
														
														sb.notify({
															type:'update-contact-note-feed',
															data:{
																object:newNote
															}
														});
														
														swal.close();
														
														buildList.call(dom, true);
														
													});
													
												});
												
											}
										
										});
										
									}.bind({}, dom, o)
								}
							}, sb.moduleId);
							
						}
						
						dom.table.body['row-'+o.id].btns.makeNode('edit', 'button', {text:'<i class="fa fa-pencil"></i> Edit', css:'pda-btn-orange'}).notify('click', {
							type:'lifebookinternational-run',
							data:{
								run:editOrder.bind(dom, o, dom.table.body['row-'+o.id].btns.edit)
							}
						}, sb.moduleId);
						
						dom.table.body['row-'+o.id].btns.makeNode('delete', 'button', {text:'<i class="fa fa-trash-o"></i> Delete', css:'pda-btn-red'}).notify('click', {
							type:'lifebookinternational-run',
							data:{
								run:deleteOrder.bind(dom, o)
							}
						}, sb.moduleId);
						
					});
					
				}			
				
				dom.patch();
				
			});
			
		}
		
		function createNew(button){
			
			var dom = this;
			
			if(button){
				button.loading();
			}
			
			sb.data.db.obj.getAll('users', function(users){
				
				dom.empty();

				dom.makeNode('firstBreak', 'lineBreak', {});
				
				dom.makeNode('cont', 'container', {css:'ui fluid card green'});
				
				dom.cont.makeNode('cont', 'container', {css:'content', uiGrid:false});
								
				dom.cont.cont.makeNode('title', 'div', {text:'New Order', css:'header'});
				
				dom.cont.cont.makeNode('form', 'form', {
					amount:{
						name:'amount',
						type:'number',
						label:'Amount of Life Books'
					},
					date_ordered:{
						name:'date_ordered',
						type:'date',
						label:'Date Ordered',
						dateFormat:'M/D/YYYY',
						value:moment().format('M/D/YYYY')
					},
					delivery_date:{
						name:'delivery_date',
						type:'date',
						label:'Desired Delivery Date (will be the due date of the task created for this order)',
						dateFormat:'M/D/YYYY',
						value:moment().format('M/D/YYYY')
					},
					manager:{
						name:'manager',
						type:'select',
						label:'Assign to',
						options:_.map(users, function(u){
							var ret = {
									value:u.id,
									name:u.fname +' '+ u.lname
								};
							if(u.id == sb.data.cookie.userId){
								ret.selected = true;
							}
							return ret;
						})
					}
				});

				dom.cont.cont.makeNode('btnBreak', 'lineBreak', {});
								
				dom.cont.cont.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
					type:'lifebookinternational-run',
					data:{
						run:function(){
							
							var dom = this,
								formInfo = dom.cont.cont.form.process();
								
							if(formInfo.completed == false){
								sb.dom.alerts.alert('', 'Please fill out the entire form', 'error');
								return;
							}	
							
							dom.cont.cont.save.loading();
							
							var newOrder = {
									amount:+formInfo.fields.amount.value,
									contact:contact.id,
									order_date:formInfo.fields.date_ordered.value,
									manager:+formInfo.fields.manager.value
								};
								
							sb.data.db.obj.create('lifebook_international_orders', newOrder, function(newOrderObj){
								
								getSettings(function(settings){
																		
									var emailObj = {
											to: _.pluck(settings.new_order, 'email'),
											from: appConfig.emailFrom,
											subject: 'New Life Book order has been placed',
											mergevars: {
												TITLE: 'New Life Book order has been placed',
												BODY: 'A new Life Book order for '+ newOrderObj.amount +' books has been placed for '+ newOrderObj.contact.company.name +' by '+ newOrderObj.manager.fname +' '+ newOrderObj.manager.lname +' on '+ moment(newOrderObj.order_date).format('M/D/YYYY h:mm a') +'.',
												BUTTON: 'View Online',
												BUTTON_LINK:'https://pagoda.voltz.software/app/'+ appConfig.instance +'/contact?&contactId='+ newOrderObj.contact.id,
												newThread:true
											}, emailtags: [
												''
											],
											type: 'contacts',
											typeId: contact.id,
										};								

									sb.comm.sendEmail(emailObj, function(response){
										
										var taskObj = {
												object_type: 'contacts',
												oid: newOrderObj.contact.id,
												title: 'New Life Book order',
												details: 'Deliver Life Book order',
												author: +sb.data.cookie.userId,
												status: 0,
												task_type:'task',
												related_object:newOrderObj.contact.id,
												assignee: [newOrderObj.manager.id],
												due_date: moment(formInfo.fields.delivery_date.value, 'M/D/YYYY').format('M/D/YYYY h:mm a'),
												page_params: window.location.href.substr(window.location.href.indexOf("#"))
											};
											
										sb.data.db.obj.create('tasks', taskObj, function(done){
											
											var notesToCreate = {
													type_id: newOrderObj.contact.id,
													type: 'contacts',
													note: 'Order #'+ newOrderObj.object_uid +' has been created.',
													note_type:1153211,
													author: sb.data.cookie.get('uid'),
													notifyUsers: []
												};

											sb.data.db.obj.create('notes', notesToCreate, function(newNote){
												
												sb.notify({
													type:'update-contact-note-feed',
													data:{
														object:newNote
													}
												});
												
												dom.empty();
												dom.patch();
												
												sb.notify({
													type:'update-contact-task-list',
													data:{}
												});
												
												buildList.call(listDom, true);
												
											});
											
										});	
										
									});
									
								}, 1);
								
							}, 2);	
														
						}.bind(dom)
					}
				}, sb.moduleId);
				
				dom.cont.cont.makeNode('cancel', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btnOutline-red'}).notify('click', {
					type:'lifebookinternational-run',
					data:{
						run:function(){
							this.empty();
							this.patch();
						}.bind(dom)
					}
				}, sb.moduleId);

				//dom.cont.cont.makeNode('final', 'lineBreak', {});
				
				dom.patch();
				
				if(button){
					button.loading(false);
				}
				
			});
			
		}
		
		function editOrder(order, button){
			
			var dom = this;
			
			if(button){
				button.loading();
			}
			
			sb.data.db.obj.getAll('users', function(users){
				
				dom.empty();
				
				dom.makeNode('cont', 'container', {css:'ui fluid card orange'});
			
				dom.cont.makeNode('cont', 'container', {css:'content', uiGrid:false});
				
				dom.cont.cont.makeNode('title', 'headerText', {text:'Editing Order', size:'small'});
				
				dom.cont.cont.makeNode('form', 'form', {
					amount:{
						name:'amount',
						type:'number',
						label:'Amount of Life Books',
						value:order.amount
					},
					date_ordered:{
						name:'date_ordered',
						type:'date',
						label:'Date Ordered',
						dateFormat:'M/D/YYYY',
						value:moment(order.order_date).format('M/D/YYYY')
					},
					manager:{
						name:'manager',
						type:'select',
						label:'Assign to',
						options:_.map(users, function(u){
							var ret = {
									value:u.id,
									name:u.fname +' '+ u.lname
								};
							if(u.id == order.manager.id){
								ret.selected = true;
							}
							return ret;
						})
					}
				});
				
				dom.cont.cont.makeNode('btns', 'buttonGroup', {css:''});
				
				dom.cont.cont.makeNode('btnBreak', 'lineBreak', {});
				
				dom.cont.cont.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
					type:'lifebookinternational-run',
					data:{
						run:function(order){
							
							var dom = this,
								formInfo = dom.cont.cont.form.process();
								
							if(formInfo.completed == false){
								sb.dom.alerts.alert('', 'Please fill out the entire form', 'error');
								return;
							}	
							
							dom.cont.cont.save.loading();
							
							var newOrder = {
									id:order.id,
									amount:+formInfo.fields.amount.value,
									order_date:formInfo.fields.date_ordered.value,
									manager:+formInfo.fields.manager.value
								};
								
							sb.data.db.obj.update('lifebook_international_orders', newOrder, function(newOrderObj){
																							
								buildList.call(dom, true);
								
							});	
														
						}.bind(dom, order)
					}
				}, sb.moduleId);
				
				dom.cont.cont.makeNode('cancel', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btnOutline-red'}).notify('click', {
					type:'lifebookinternational-run',
					data:{
						run:function(){
							
							buildList.call(dom, true);
						
						}.bind(dom)
					}
				}, sb.moduleId);
				
				dom.patch();
				
				if(button){
					button.loading(false);
				}
				
			});
			
		}
		
		function deleteOrder(order){
			
			var dom = this;
			
			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: ''
			}, function(resp){
				
				if(resp){
					
					swal.disableButtons();
					
					sb.data.db.obj.erase('lifebook_international_orders', order.id, function(deleted){
						
						buildList.call(listDom, true);
						
						swal.close();
						
					});
					
				}
				
			});
			
		}
		
		function view(){
			
			var dom = this;
			
			dom.makeNode('btns', 'buttonGroup', {css:''});
			
			createDom = dom.makeNode('newCont', 'container', {uiGrid:false});
			
			dom.makeNode('createBreak', 'lineBreak', {});
			
			listDom = dom.makeNode('list', 'container', {uiGrid:false});
			
			dom.btns.makeNode('new', 'button', {text:'<i class="fa fa-plus"></i> New Order', css:'pda-btnOutline-green'}).notify('click', {
				type:'lifebookinternational-run',
				data:{
					run:createNew.bind(createDom, dom.btns.new)
				}
			}, sb.moduleId);
			
			buildList.call(listDom);
			
			dom.patch();
			
		}
		
		contact = contactObj;
		
		view.call(this);
		
	}
	
	function statsView(startRange, endRange){
		
		var objectTypeReport = 'lifebook_international_orders',
			groupOn = 'manager',
			dateField = 'order_date',
			sum = 'amount';
		
		function buildHomePageInner(data){
			
			function calculateChartData(data){
				
				var ret = {
						labels:[],
						total:[]
					},
					diff = data.end.diff(data.start, 'days'),
					workingDate = data.start.clone(),
					dateFormat = 'M/D/YYYY';
									
				while(workingDate.endOf(data.groupBy) <= data.end){
					
					ret.labels.push(workingDate.format(dateFormat));
					
					var found = false;
					
					_.each(data.total, function(p){
						
						if(moment(p.grouped, 'YYYY-MM-DD H:mm:ss-SS').format(dateFormat) == workingDate.format(dateFormat)){
							ret.total.push(p.grouped_total);
							found = true;
						}
						
					});
					
					if(found === false){
						ret.total.push(0);
					}
										
					workingDate.add(1, data.groupBy);
					
				}
				
				return ret;
				
			}
			
			var totalOrders = 0,
				totalBooks = 0;
			_.each(data.total, function(o){
				totalOrders += o.grouped_total;
				totalBooks += o.sum;
			});
			
			this.makeNode('break', 'lineBreak', {});
			
			this.makeNode('topCont', 'container', {css:'pda-container pda-Panel pda-panel-gray'});
			
			this.topCont.makeNode('col0', 'column', {width:2}).makeNode('text', 'headerText', {text:(totalOrders) +'<br /><small>Total Orders</small><br /><br />'+ (totalBooks) +'<br /><small>Total Books</small>', css:'text-center'});			
			this.topCont.makeNode('col2', 'column', {width:5});
			this.topCont.makeNode('col3', 'column', {width:5});
			
			this.makeNode('break1', 'lineBreak', {spaces:1});	
						
			var chartData = calculateChartData(data);
			
			this.makeNode('chartCont', 'container', {css:'pda-container pda-Panel pda-panel-gray'});
			
			this.chartCont.makeNode('title', 'headerText', {text:'Orders By Gideon Over Time', size:'x-small', css:'text-center'});
			
			this.chartCont.makeNode('chartCont', 'container', {css:'chartHeight'});
			
			this.chartCont.chartCont.makeNode('chart', 'chart', {
				type:'bar',
				data:{
			        labels: chartData.labels,
			        datasets: [
				        {
				            label: 'Total',
				            data: chartData.total,
				            borderColor: 'rgba(66,180,113,1)',
				            backgroundColor: 'rgba(66,180,113,1)'
				        }
			        ]
			    },
			    options:{
					responsive: true,
			        maintainAspectRatio: false,
				    scales: {
				        yAxes: [{
				            ticks: {
				                beginAtZero: true
				            },
				            stacked:true
				        }],
				        xAxes: [{
					        stacked:true
				        }]
				    }
				}
			});
		
			this.topCont.col2.makeNode('title', 'headerText', {text:'Orders By Gideon', size:'x-small', css:'text-center'});
			this.topCont.col2.makeNode('chart', 'chart', {
				type:'pie',
				data:{
			        labels: _.map(data.byStaff, function(o){ return _.where(data.staffList, {id:+o.grouped})[0].fname +' '+ _.where(data.staffList, {id:+o.grouped})[0].lname; }),
			        datasets: [{
			            data: _.map(data.byStaff, function(o){ return o.grouped_total; }),
			            backgroundColor:['rgba(235,132,15,1)', 'rgba(66,180,113,1)']
			        }]
			    },
			    options:{
				    responsive: true,
			        maintainAspectRatio: false,
			    }
			});
			
			this.topCont.col3.makeNode('title', 'headerText', {text:'Books By Gideon', size:'x-small', css:'text-center'});
			this.topCont.col3.makeNode('chart', 'chart', {
				type:'pie',
				data:{
			        labels: _.map(data.byStaff, function(o){ return _.where(data.staffList, {id:+o.grouped})[0].fname +' '+ _.where(data.staffList, {id:+o.grouped})[0].lname; }),
			        datasets: [{
			            data: _.map(data.byStaff, function(o){ return o.sum; }),
			            backgroundColor:['rgba(235,132,15,1)', 'rgba(66,180,113,1)']
			        }]
			    },
			    options:{
				    responsive: true,
			        maintainAspectRatio: false,
			    }
			});
						
			this.makeNode('break2', 'lineBreak', {spaces:1});
			
			this.makeNode('tableCont', 'column', {width:12}).makeNode('col', 'container', {css:'pda-container pda-Panel pda-panel-gray'});
			
			this.tableCont.col.makeNode('title', 'headerText', {text:'Orders By Gideon', size:'x-small', css:'text-center'});
			this.tableCont.col.makeNode('break', 'lineBreak', {});
			
			this.tableCont.col.makeNode(
				'staffTable',
				'table',
				{
					css: 'table-hover table-condensed',
					columns: {
						id: 'Gideon',
						total: 'Total Orders',
						btns: ''
					}
				}
			);

			_.each(data.staffList, function(s){
				
				var total = 0,
					paid = 0,
					open = 0;
				
				_.each(data.byStaff, function(d){
					
					if(s.id == +d.grouped){
						total += d.grouped_total;						
					}
					
				});
				
				_.each(data.byStaffTotal, function(d){
					
					if(s.id == +d.grouped){
						paid += d.grouped_total;
					}
					
				});
								
				this.tableCont.col.staffTable.makeRow(
					'row-'+ s.id,
					[
						'<span class="text-bold">'+ s.fname +' '+ s.lname +'</span>',
						'<span class="text-bold">'+ total +'</span>',
						''
					]
				);
				
			}, this);
			
/*
			_.each(data.byStaff, function(s){
				
				var staff = _.where(data.staffList, {id:+s.grouped})[0];

				this.tableCont.col.staffTable.makeRow(
					'row-'+s.sum,
					[staff.fname +' '+ staff.lname, s.grouped_total, '', '', '']
				);
				
			}, this);
*/
			
			this.makeNode('finalBreak', 'lineBreak', {spaces:1});
			
			this.patch();
			
		}

		function getDashboardData(startRange, endRange, callback){
			
			var data = {
					fetched:false,
					fetched_on:moment(),
					start:startRange,
					end:endRange,
					open:{},
					paid:{},
					groupBy:'day'
				},
				groupBy = 'day';
				
			if(endRange.diff(startRange, 'days') > 60){
				
				groupBy = 'week';
				
			}
			
			if(endRange.diff(startRange, 'days') > 365){
				
				groupBy = 'month';
				
			}	
				
			if(data.fetched_on > moment().subtract(5, 'minutes') || data.fetched === false){
				
				sb.data.db.obj.getSum(objectTypeReport, sum, {
					groupBy:groupBy,
					dateField:dateField,
					dateRange:{
						start: startRange.format('YYYY-MM-DD HH:mm:ss.SS'),
						end: endRange.format('YYYY-MM-DD HH:mm:ss.SS')
					}
				}, function(totalOrders){
					
					sb.data.db.obj.getGroupSum(objectTypeReport, sum, {
						groupOn:groupOn,
						dateField:dateField,
						dateRange:{
							start: startRange.format('YYYY-MM-DD HH:mm:ss.SS'),
							end: endRange.format('YYYY-MM-DD HH:mm:ss.SS')
						}
					}, function(staffTotal){

						sb.data.db.obj.getAll('users', function(allStaff){
							
							data.fetched = true;
							data.fetched_on = moment();
							data.total = totalOrders;
							data.byStaff = staffTotal;
							data.staffList = allStaff;
							data.groupBy = groupBy;
		
							callback(data);
							
						});
						
					});
					
				});
				
			}else{
				
				data.groupBy = groupBy;
				
				callback(data);
				
			}
			
		}
		
		var domObj = this;

		getDashboardData(startRange, endRange, function(data){

			delete domObj.loader;	
			
			domObj.makeNode('titleCont', 'container', {}).makeNode('title', 'headerText', {text:'All orders between '+ data.start.format('M/D/YYYY') +' and '+ data.end.format('M/D/YYYY') + ' <small>click to change</small>', size:'small'});
			
			domObj.makeNode('cont', 'container', {}).makeNode('coldate', 'column', {width:12});
			
			domObj.cont.makeNode('loading', 'container', {});
			
			//dom.cont.makeNode('break2', 'lineBreak', {spaces:1});
			
			domObj.cont.makeNode('cont', 'container', {});
			
			domObj.patch();
			
			buildHomePageInner.call(domObj.cont.cont, data);
							
			var dateRangeDom = domObj.titleCont.selector;
			
			$(dateRangeDom).daterangepicker({
			    "showDropdowns": false,
			    "ranges": {
			        "Today": [moment().startOf('day'),moment().endOf('day')],
			        "Yesterday": [moment().subtract(1, 'day').startOf('day'), moment().subtract(1, 'day').endOf('day')],
			        "Last 7 Days": [moment().subtract(7, 'day').startOf('day'), moment().endOf('day')],
			        "Last 30 Days": [moment().subtract(30, 'day').startOf('day'), moment().endOf('day')],
			        "This Month": [moment().startOf('month').startOf('day'), moment().endOf('month').endOf('day')],
			        "This Year": [moment().month(0).startOf('month').startOf('day'),moment().endOf('day')],
			        "Last Month": [moment().subtract(1, 'month').startOf('month').startOf('day'),moment().subtract(1, 'month').endOf('month').endOf('day')],
			        "Last Year": [moment().subtract(1, 'year').month(0).startOf('month').startOf('day'),moment().subtract(1, 'year').month(11).endOf('month').endOf('day')]
			    },
			    "alwaysShowCalendars": true,
			    "startDate":startRange,
			    "endDate":endRange,
				"opens":"right"
			}, function(start, end, label) {
				$(dateRangeDom +' span').html(start.format('MM-DD-YYYY') + ' - ' + end.format('MM-DD-YYYY'));
			});
			
			$(dateRangeDom).on('apply.daterangepicker', function(ev, picker) {
				
				domObj.cont.loading.makeNode('loader', 'loader', {});
				
				domObj.cont.loading.patch();
									
				getDashboardData(picker.startDate, picker.endDate, function(data){
					
					delete domObj.cont.loading.loader;
					
					domObj.titleCont.makeNode('title', 'headerText', {text:'All tickets between '+ data.start.format('M/D/YYYY') +' and '+ data.end.format('M/D/YYYY') + ' <small>click to change</small>', size:'small'});
					
					domObj.titleCont.patch();
					domObj.cont.loading.patch();
					
					buildHomePageInner.call(domObj.cont.cont, data);
				
				});
				
			});
			
		});
		
	}
	
	function systemView(){
		
		function build(){
			
			var dom = this;
			
			components.table = sb.createComponent('crud-table');
			
			var crudSetup = {							
					domObj:dom,
					objectType:'lifebook_international_orders',
					childObjs:2,
					tableTitle:'Life Book Orders',
					navigation:true,
					searchObjects:false,
					settings:{
						action:[
							{
								object_type:'life_book_system',
								name:'Notification Settings',
								action:notificationSettings
							}
						]
					},					
					filters:false,
					download:false,
					headerButtons:{
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							action:function(){}
						}
					},
					rowSelection:true,
					multiSelectButtons:{
						change:{
							name:'<i class="fa fa-retweet"></i> Change Point Person',
							css:'pda-btn-orange',
							domType:'modal',
							action:function(selectedObjs, modal){
								
								modal.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
								
								modal.body.patch();
								
								sb.data.db.obj.getAll('users', function(users){
									
									modal.body.makeNode('title', 'headerText', {text:'Change point person on '+ selectedObjs.length + ' order(s).', size:'small'});
									
									modal.body.makeNode('titleBreak', 'lineBreak', {});
									
									modal.body.makeNode('form', 'form', {
										manager:{
											name:'manager',
											type:'select',
											label:'New Point Person',
											options:_.map(users, function(u){
												return {
													value:u.id,
													name:u.fname +' '+ u.lname
												};
											})
										}
									});
									
									modal.footer.makeNode('btns', 'buttonGroup', {});
									
									modal.footer.btns.makeNode('yes', 'button', {css:'pda-btn-green', text:'<i class="fa fa-check"></i> Save Changes'}).notify('click', {
										type:'lifebookinternational-run',
										data:{
											run:function(selectedObjs){
												
												var modal = this,
													newManager = +modal.body.form.process().fields.manager.value;
												
												modal.footer.btns.yes.loading();
												
												var newObjs = _.map(selectedObjs, function(o){
														o.manager = newManager;
														return o;												
													});
												
												sb.data.db.obj.update('lifebook_international_orders', newObjs, function(updated){
													
													modal.hide(function(){
														
														components.table.notify({
															type:'update-table',
															data:{}
														});
														
													});
													
												});
												
											}.bind(modal, selectedObjs)
										}
									}, sb.moduleId);
									
									modal.footer.btns.makeNode('cancel', 'button', {css:'pda-btnOutline-red', text:'<i class="fa fa-times"></i> Cancel'}).notify('click', {
										type:'change-from-table',
										data:{
											type:'approve',
											modal:dom,
											objects:selectedObjs
										}
									}, sb.moduleId);
									
									delete modal.body.text;
									modal.body.patch();
									modal.footer.patch();
									
								});
																
							}
						},
						erase:{
							name:'<i class="fa fa-times"></i> Delete',
							css:'pda-btn-red',
							domType:'modal',
							action:function(selectedObjs, dom){
								
								dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
								
								dom.body.patch();
								
								setTimeout(function(){
																		
									dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Delete '+ selectedObjs.length + ' contact(s)?'});
																				
									dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes, delete them'}).notify('click', {
										type:'change-from-table',
										data:{
											type:'approve',
											modal:dom,
											objects:selectedObjs
										}
									}, sb.moduleId);
									
									dom.body.patch();
									dom.footer.patch();
									
									crudModal = dom;
									
								}, 1000);
								
							}
						}
					},
					home:{
						action:homeView
					},
					rowLink:{
						type:'notify',
						action:{
							type:'lifebookinternational-view-contact',
							data:{}
						}
					},
					visibleCols:{
						amount:'Amount of Books',
						manager:'Assigned To',
						contact:'Church Contact',
						church:'Church Name',
						order_date:'Date Ordered'
					},
					cells: {
						amount:function(obj){
							return obj.amount + ' books';
						},
						order_date:function(obj){
							return moment(obj.order_date).format('M/D/YYYY');
						},
						church:function(obj){
							return obj.contact.company.name;
						}
					},
					data:function(paged, callback){
						
						sb.data.db.obj.getAll('lifebook_international_orders', function(ret){

							callback(ret);
							
						}, 2, paged);
						
					}
				};
				
			components.table.notify({
				type: 'show-table',
				data: crudSetup
			});	
			
		}
		
		build.call(this);
		
	}
				
	return {
		
		init: function(){
			
			sb.listen({
				'lifebookinternational-show-single-object':this.showSingleView,
				'lifebookinternational-show-system-view':this.showSystemView,
				'lifebookinternational-destroy':this.destroy,
				'lifebookinternational-run':this.run,
				'lifebookinternational-view-contact':this.goToContact
			});
			
			if(
				appConfig.instance == 'thelifebookno' 
				|| appConfig.instance == 'thelifebookau' 
				|| appConfig.instance == 'rickyvoltz'
			) {
			
				sb.notify({
					type: 'register-application',
					data: {
						navigationItem: {
							id: 'tlbOrders',
							title: 'TLB Orders',
							icon: '<i class="fa fa-book"></i>',
							views: [
								{
									id:'tlbOrdersTool',
									type:'tool',
									name:'TLB Orders',
									tip:'',
									icon: {
										type: 'book',
										color: 'blue'
									},
									availableToEntities: true,
									hiddenFromProjects: true,
									mainViews:[
										{
											dom: function (dom, state, draw, mainDom) {
												
												sb.data.db.obj.getById('', state.entity, function(obj) {
													
													sb.notify({
														type: 'lifebookinternational-show-single-object',
														data: {
															dom:dom,
															contact:obj
														}
													});
													
												}, 1);
												
											}
										}
									],
									boxViews:[]								
								}
							]
						}
					}
				});
			}
			
		},
		
		destroy: function(){
			
			sb.listen({
				'lifebookinternational-show-single-object':this.showSingleView,
				'lifebookinternational-show-system-view':this.showSystemView,
				'lifebookinternational-destroy':this.destroy,
				'lifebookinternational-run':this.run,
				'lifebookinternational-view-contact':this.goToContact
			});
			
			_.each(components, function(comp){
				comp.destroy();
			});
			
			ui = {};
			components = {};
			
		},
		
		run: function(data){data.run()},
		
		goToContact: function(data){
			
			sb.notify({
				type:'side-nav-change-page',
				data:{
					pageModuleId:'contact',
					dataId:'contact',
					pageParams:{
						contactId:data.object.contact.id
					}
				}
			});		
			
		},
		
		showSingleView: function(data){
			
			ui = sb.dom.make(data.dom.selector);
			
			ui.makeNode('cont', 'container', {uiGrid:false});
			
			ui.build();
			
			singleView.call(ui.cont, data.contact);
			
		},
		
		showSystemView: function(data){
			
			ui = sb.dom.make(data.dom.selector);
			
			ui.makeNode('cont', 'container', {});
			
			ui.build();
			
			systemView.call(ui.cont);
			
		}
									
	}
	
});