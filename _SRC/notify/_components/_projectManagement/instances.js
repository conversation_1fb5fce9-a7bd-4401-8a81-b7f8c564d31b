Factory.register("instances", function (sb) {
  var adminURL = "../../api/",
    ui = {},
    mainUI = {},
    components = {},
    blueprint = {};

  function makeid() {
    var text = "";
    var possible =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    for (var i = 0; i < 6; i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));

    return text;
  }

  function deleteCookie(cname) {
    var d = new Date(); //Create an date object
    d.setTime(d.getTime() - 1000 * 60 * 60 * 24); //Set the time to the past. 1000 milliseonds = 1 second
    var expires = "expires=" + d.toGMTString(); //Compose the expirartion date
    window.document.cookie = cname + "=" + "; " + expires; //Set the cookie with name and the expiration date
  }

  function buildMainUI() {
    function getData(callback) {}

    function main() {
      var dom = this;

      sb.data.db.controller(
        "getInstanceBlueprint",
        { objectType: "instances" },
        function (bp) {
          blueprint = bp;

          var crudSetup = {
            domObj: dom,
            objectType: "instances",
            tableTitle: {
              title: '<i class="fa fa-snowflake-o"></i> Instances',
              size: "large",
            },
            home: false,
            searchObjects: false,
            filters: false,
            download: false,
            settings: {
              action: [
                {
                  object_type: "blueprints",
                  name: "Blueprints",
                  action: function (dom) {
                    sb.notify({
                      type: "edit-blueprint-objects-load",
                      data: {
                        domObj: dom,
                      },
                    });
                  },
                },
              ],
            },
            headerButtons: {
              reload: {
                name: "Reload",
                css: "pda-btn-blue",
                action: function () {},
              },
            },
            calendar: false,
            rowSelection: true,
            multiSelectButtons: {},
            rowLink: {
              type: "tab",
              header: function (obj) {
                return obj.systemName;
              },
              action: singleView,
            },
            visibleCols: {
              systemName: "System Name",
              enabled: "Enabled",
            },
            dateRange: "start_date",
            cells: {
              systemName: function (obj) {
                return obj.systemName;
              },
              enabled: function (obj) {
                return obj.enabled;
              },
            },
            childObjs: 1,
            data: function (paged, callback) {
              sb.data.db.controller(
                "getAllInstances&pagodaAPIKey=" + appConfig.instance,
                { objectType: "instances", childObjs: 1, paged: paged },
                function (objs) {
                  callback(objs);
                },
                adminURL + "_getAdmin.php?do="
              );
            },
          };

          components.table.notify({
            type: "show-table",
            data: crudSetup,
          });
        },
        adminURL + "_getAdmin.php?do="
      );
    }

    this.state.show = main.bind(this);
  }

  function createUI(selector) {
    components.table = sb.createComponent("crud-table");
    components.blueprints = sb.createComponent("blueprintBuilder");

    ui = sb.dom.make(selector);

    // !TODO: REMOVE TESTING
    /*
		ui.makeNode('test', 'container', {})
			.makeNode('btn', 'button', {text:'testing copy settings objs'})
			.notify('click', {
				type:'instances-run',
				data:{
					run: function(){

						sb.data.db.controller('copyInstanceSettingsObjects&pagodaAPIKey='+ 'zachvoltz', {fromInstance:'rickyvoltz', toInstance:'zachvoltz'}, function(updatedObj){
							
							
							
										
						}, 'https://pagoda.voltz.software/_repos/zachvoltz/pagoda/_coredev/_getAdmin.php?do=');	
						
					}
				}
			}, sb.moduleId);
*/

    // TESTING

    mainUI = ui.makeNode("container", "container", {});
    mainUI.state = buildMainUI;

    ui.build();
  }

  function loginToInstance(obj) {
    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "",
      },
      function (resp) {
        if (resp) {
          swal.disableButtons();

          sb.data.db.obj.getById(
            "users",
            +sb.data.cookie.get("uid"),
            function (user) {
              sb.data.db.controller(
                "getUserAccountsNew&api_webform=1",
                { email: user.email, multi: true },
                function (accounts) {
                  var hasAccount = _.where(accounts, {
                    instance: obj.instance,
                  });

                  if (hasAccount.length > 0) {
                    sb.data.db.controller(
                      "deleteCookie&api_webform=1",
                      {
                        series: sb.data.cookie.get("series"),
                        token: sb.data.cookie.get("token"),
                      },
                      function (deleted) {
                        if (deleted) {
                          sb.data.cookie.set("type", "");
                          sb.data.cookie.set("appNav", "");

                          sb.data.db.controller(
                            "createCookie&api_webform=1&pagodaAPIKey=" +
                              obj.instance,
                            { staffId: hasAccount[0].id, login: true },
                            function (cookie) {
                              sb.data.cookie.set("type", "Admin");

                              //window.open("/app/"+obj.instance,"_self");
                            },
                            adminURL + "_getAdmin.php?do="
                          );
                        }
                      },
                      adminURL + "_getAdmin.php?do="
                    );
                  } else {
                    user.type = "Admin";
                    user.user_type = "Admin";
                    delete user.id;
                    delete user.instance;

                    sb.data.db.controller(
                      "createNewObject&api_webform=1&pagodaAPIKey=" +
                        obj.instance,
                      { objectType: "users", objectData: user },
                      function (newUser) {
                        sb.data.db.controller(
                          "deleteCookie&api_webform=1",
                          {
                            series: sb.data.cookie.get("series"),
                            token: sb.data.cookie.get("token"),
                          },
                          function (deleted) {
                            if (deleted) {
                              sb.data.cookie.set("type", "");
                              sb.data.cookie.set("appNav", "");

                              sb.data.db.controller(
                                "createCookie&api_webform=1&pagodaAPIKey=" +
                                  obj.instance,
                                { staffId: newUser.id, login: true },
                                function (cookie) {
                                  sb.data.cookie.set("type", "Admin");

                                  //window.open("/app/"+obj.instance,"_self");
                                }
                              );
                            }
                          },
                          adminURL + "_getAdmin.php?do="
                        );
                      },
                      adminURL + "_getAdmin.php?do="
                    );
                  }
                },
                adminURL + "_getAdmin.php?do="
              );
            }
          );
        }
      }
    );
  }

  function singleObjectView(dom, contact) {
    function editInstanceSetup(dom, obj) {
      dom.makeNode("codeModal", "modal", {
        onShow: function (dom) {
          var formArgs = {
            /*
							instance:{
								name:'instance',
								type:'text',
								placeholder:'All lowercase & one word',
								value:contact.fname.toLowerCase()+contact.lname.toLowerCase(),
								label:'Application Name'
							},
*/
            fname: {
              name: "fname",
              type: "text",
              value: contact.fname,
              label: "First Name",
            },
            lname: {
              name: "lname",
              type: "text",
              value: contact.lname,
              label: "Last Name",
            },
            email: {
              name: "email",
              type: "text",
              value: email,
              label: "Email address",
            },
            flat_fee: {
              name: "flat_fee",
              type: "usd",
              value: 0,
              label: "Flat fee",
            },
            flat_user_cap: {
              name: "flat_user_cap",
              type: "number",
              value: 0,
              label: "Users included in flat rate",
            },
            price_per_user: {
              name: "price_per_user",
              type: "usd",
              value: 2000,
              label: "Price per user thereafter",
            },
            trial_start_date: {
              name: "trial_start_date",
              type: "date",
              value: moment(),
              label: "Trial period start date",
            },
            trial_end_date: {
              name: "trial_end_date",
              type: "date",
              value: moment().add(1, "months"),
              label: "Trial period end date",
            },
            version: {
              name: "version",
              type: "select",
              label: "Instance type",
              options: [
                {
                  name: "Bento",
                  value: "bento",
                  selected: true,
                },
              ],
            },
            main_contact: {
              name: "main_contact",
              type: "hidden",
              value: contact.id,
            },
          };

          if (obj) {
            dom.codeModal.body.makeNode("title", "div", {
              css: "ui huge header",
              text: "Edit this instance",
            });

            _.each(formArgs, function (v, k) {
              v.value = obj[k];
            });
          } else {
            dom.codeModal.body.makeNode("title", "div", {
              css: "ui huge header",
              text: "Create a new instance code",
            });
          }

          dom.codeModal.body.makeNode("form", "form", formArgs);

          dom.codeModal.body.makeNode("formBreak", "div", { text: "<br />" });

          var saveButtonText = "Generate a new code";
          if (obj) {
            saveButtonText = "Save details";
          }

          dom.codeModal.body
            .makeNode("save", "div", {
              text: saveButtonText,
              tag: "button",
              css: "ui green button",
            })
            .notify(
              "click",
              {
                type: "instances-run",
                data: {
                  run: function (dom, obj) {
                    var createUpdate = "create";
                    if (obj) {
                      createUpdate = "update";
                    }

                    dom.codeModal.body.save.loading();

                    var setupCode = makeid().toUpperCase();

                    sb.data.db.obj.getAll("instances", function (instances) {
                      var formInfo = dom.codeModal.body.form.process();
                      var instanceSetupObj = {
                        account_signup_code: setupCode,
                      };

                      _.each(instances[0], function (v, k) {
                        instanceSetupObj[k] = v;
                      });

                      _.each(formInfo.fields, function (fieldObj, fieldName) {
                        instanceSetupObj[fieldName] = fieldObj.value;
                      });

                      if (createUpdate === "update") {
                        instanceSetupObj.id = obj.id;
                      }

                      sb.data.db.obj[createUpdate](
                        "instance_setup",
                        instanceSetupObj,
                        function (created) {
                          if (createUpdate === "update") {
                            dom.codeModal.hide();

                            singleObjectView(dom, contact);
                          } else {
                            dom.codeModal.body.empty();

                            dom.codeModal.body.makeNode("title", "div", {
                              text: "Your new setup code",
                              css: "ui huge header",
                            });

                            dom.codeModal.body.makeNode("code", "div", {
                              css: "ui big teal floating icon message",
                            });
                            dom.codeModal.body.code.makeNode("icon", "div", {
                              tag: "i",
                              css: "barcode icon",
                            });
                            dom.codeModal.body.code.makeNode("code", "div", {
                              text:
                                setupCode.substr(0, 3) +
                                " - " +
                                setupCode.substr(3, 3),
                              css: "ui huge header",
                            });

                            dom.codeModal.body.makeNode("seg", "div", {
                              css: "ui teal segment",
                            });
                            dom.codeModal.body.seg.makeNode("form", "form", {
                              to: {
                                name: "to",
                                type: "text",
                                label: "To",
                                value: email,
                              },
                              subject: {
                                name: "subject",
                                type: "text",
                                label: "Subject",
                                value: "Here is your Bento Systems setup code!",
                              },
                              body: {
                                name: "body",
                                type: "textbox",
                                label: "Message",
                                rows: 5,
                                value:
                                  "Go to https://bento.infinityhospitality.net/getstarted and enter the code below to start using Bento.\n\nHere's your code: " +
                                  setupCode.substr(0, 3) +
                                  " - " +
                                  setupCode.substr(3, 3),
                              },
                            });
                            dom.codeModal.body.seg.makeNode(
                              "formBreak",
                              "div",
                              { text: "<br />" }
                            );
                            dom.codeModal.body.seg
                              .makeNode("send", "div", {
                                tag: "button",
                                css: "ui green button",
                                text: "Send email",
                              })
                              .notify(
                                "click",
                                {
                                  type: "instances-run",
                                  data: {
                                    run: function (dom) {
                                      dom.codeModal.body.seg.send.loading();

                                      var formData =
                                        dom.codeModal.body.seg.form.process()
                                          .fields;

                                      var emailObj = {
                                        to: formData.to.value,
                                        from: "",
                                        subject: formData.subject.value,
                                        mergevars: {
                                          TITLE: formData.subject.value,
                                          BODY: formData.body.value,
                                          BUTTON: "Get Started",
                                          BUTTON_LINK:
                                            "https://bento.infinityhospitality.net/getstarted",
                                        },
                                        emailtags: ["new account code"],
                                      };

                                      sb.comm.sendEmail(
                                        emailObj,
                                        function (response) {
                                          setTimeout(function () {
                                            sb.dom.alerts.alert(
                                              "Email sent!",
                                              "",
                                              "success"
                                            );

                                            dom.codeModal.hide();

                                            singleObjectView(dom, contact);
                                          }, 0);
                                        }
                                      );
                                    }.bind({}, dom),
                                  },
                                },
                                sb.moduleId
                              );
                            dom.codeModal.body.seg
                              .makeNode("close", "div", {
                                tag: "button",
                                css: "ui blue button",
                                text: "Close and don't send email",
                              })
                              .notify(
                                "click",
                                {
                                  type: "instances-run",
                                  data: {
                                    run: function (dom) {
                                      dom.codeModal.hide();

                                      singleObjectView(dom, contact);
                                    }.bind({}, dom),
                                  },
                                },
                                sb.moduleId
                              );

                            dom.codeModal.body.patch();
                          }
                        },
                        adminURL + "_getAdmin.php?do="
                      );
                    });
                  }.bind({}, dom, obj),
                },
              },
              sb.moduleId
            );

          dom.codeModal.body.patch();
        }.bind({}, dom),
      });

      dom.patch();

      dom.codeModal.show();
    }

    var email = false;

    if (contact.contact_info) {
      _.each(contact.contact_info, function (info) {
        switch (info.type.data_type) {
          case "email":
            if (info.is_primary == "yes") {
              email = info.info;
            }

            break;
        }
      });
    }

    if (email === false) {
      dom.empty();

      dom.makeNode("emailwarning", "text", {
        text: "Please add an email address to this contact to continue.",
        css: "text-center",
      });

      dom.patch();
    } else {
      dom.empty();

      dom.makeNode("loading", "text", {
        text: "Loading instances...",
        css: "text-center",
      });
      dom.makeNode("loader", "loader", {});

      dom.patch();

      sb.data.db.controller(
        "getInstancesBy&api_webform=true&pagodaAPIKey=" +
          contact.fname.toLowerCase() +
          contact.lname.toLowerCase(),
        {
          objectType: "instance_setup",
          queryObj: { main_contact: contact.id },
          getChildObjs: 1,
        },
        function (instanceSetupObjs) {
          sb.data.db.controller(
            "getInstancesBy&api_webform=true&pagodaAPIKey=" +
              appConfig.instance,
            {
              objectType: "instances",
              queryObj: { main_contact: contact.id },
              getChildObjs: 1,
            },
            function (instances) {
              dom.empty();

              if (instanceSetupObjs.length == 0 && instances.length == 0) {
                dom
                  .makeNode("code", "button", {
                    text: "Generate a new code",
                    css: "ui green button",
                  })
                  .notify(
                    "click",
                    {
                      type: "instances-run",
                      data: {
                        run: function (dom) {
                          editInstanceSetup(dom, null);
                        }.bind({}, dom),
                      },
                    },
                    sb.moduleId
                  );

                dom.patch();
              } else {
                dom.makeNode("table", "table", {
                  columns: {
                    name: "Instance Name",
                    plan: "Plan Level",
                    soldBy: "Setup By",
                    version: "App Version",
                    actions: "",
                  },
                });

                var availableInstances = instanceSetupObjs;
                if (instances.length > 0) {
                  availableInstances = instances;
                }

                _.each(availableInstances, function (instance) {
                  var createdBy = "N/A";
                  if (instance.created_by) {
                    createdBy =
                      instance.created_by.fname +
                      " " +
                      instance.created_by.lname;
                  }

                  var appVersion = "bento";
                  if (instance.version) {
                    appVersion = instance.version;
                  }

                  switch (instance.object_bp_type) {
                    case "instances":
                      dom.table.makeRow(
                        "row-" + instance.id,
                        [
                          instance.instance,
                          "$" +
                            (instance.flat_fee / 100).formatMoney() +
                            "/month for " +
                            instance.flat_user_cap +
                            " users. Then $" +
                            (instance.price_per_user / 100).formatMoney() +
                            "/user thereafter.",
                          createdBy,
                          appVersion.toUpperCase(),
                          "",
                        ],
                        {
                          css: "positive",
                        }
                      );

                      dom.table.body["row-" + instance.id].actions.makeNode(
                        "view",
                        "div",
                        {
                          text: '<i class="fa fa-eye"></i> View',
                          css: "ui mini green button",
                        }
                      );
                      dom.table.body["row-" + instance.id].actions.makeNode(
                        "edit",
                        "div",
                        {
                          text: '<i class="fa fa-eye"></i> Edit',
                          css: "ui mini orange button",
                        }
                      );

                      dom.table.body["row-" + instance.id].actions.view.notify(
                        "click",
                        {
                          type: "instances-run",
                          data: {
                            run: function (instance) {
                              loginToInstance(instance);
                            }.bind({}, instance),
                          },
                        },
                        sb.moduleId
                      );

                      dom.table.body["row-" + instance.id].actions.edit.notify(
                        "click",
                        {
                          type: "instances-run",
                          data: {
                            run: function (dom, instance) {
                              singleView(instance, dom, {}, function (done) {});
                            }.bind({}, dom, instance),
                          },
                        },
                        sb.moduleId
                      );

                      break;

                    case "instance_setup":
                      dom.table.makeRow(
                        "row-" + instance.id,
                        [
                          "Signup Code: <b>" +
                            instance.account_signup_code.substr(0, 3) +
                            " - " +
                            instance.account_signup_code.substr(3, 3) +
                            "</b>",
                          "$" +
                            (instance.flat_fee / 100).formatMoney() +
                            "/month for " +
                            instance.flat_user_cap +
                            " users. Then $" +
                            (instance.price_per_user / 100).formatMoney() +
                            "/user thereafter.",
                          createdBy,
                          appVersion.toUpperCase(),
                          "",
                        ],
                        {
                          css: "warning",
                        }
                      );

                      dom.table.body["row-" + instance.id].actions.makeNode(
                        "view",
                        "div",
                        {
                          text: '<i class="fa fa-trash"></i> Delete',
                          css: "ui mini red button",
                        }
                      );
                      dom.table.body["row-" + instance.id].actions.view.notify(
                        "click",
                        {
                          type: "instances-run",
                          data: {
                            run: function (dom, obj) {
                              sb.dom.alerts.ask(
                                {
                                  title: "Are you sure?",
                                  text: "You will have to generate a new code for this person to create an account.",
                                },
                                function (resp) {
                                  if (resp) {
                                    swal.disableButtons();

                                    sb.data.db.obj.erase(
                                      "instance_setup",
                                      obj.id,
                                      function (deleted) {
                                        swal.close();

                                        singleObjectView(dom, contact);
                                      }
                                    );
                                  }
                                }
                              );
                            }.bind({}, dom, instance),
                          },
                        },
                        sb.moduleId
                      );

                      dom.table.body["row-" + instance.id].actions.makeNode(
                        "edit",
                        "div",
                        {
                          text: '<i class="fa fa-pencil"></i> Edit',
                          css: "ui mini orange button",
                        }
                      );
                      dom.table.body["row-" + instance.id].actions.edit.notify(
                        "click",
                        {
                          type: "instances-run",
                          data: {
                            run: function (obj) {
                              editInstanceSetup(dom, obj);
                            }.bind({}, instance),
                          },
                        },
                        sb.moduleId
                      );

                      dom.table.body["row-" + instance.id].actions.makeNode(
                        "activate",
                        "div",
                        {
                          text: '<i class="fa fa-check"></i> Activate',
                          css: "ui mini green button",
                        }
                      );
                      dom.table.body[
                        "row-" + instance.id
                      ].actions.activate.notify(
                        "click",
                        {
                          type: "instances-run",
                          data: {
                            run: function (dom, obj) {
                              dom.makeNode("activate", "modal", {
                                onShow: function () {
                                  var modal = dom.activate.body;

                                  modal.empty();

                                  modal.makeNode("title", "div", {
                                    css: "ui huge header",
                                    text: "Choose a name for this application",
                                  });

                                  modal.makeNode("form", "form", {
                                    instance: {
                                      name: "instance",
                                      type: "text",
                                      value: contact.company.name.toLowerCase(),
                                      label: "",
                                    },
                                  });

                                  modal.makeNode("formBreak", "div", {
                                    text: "<br />",
                                  });

                                  modal
                                    .makeNode("save", "div", {
                                      css: "ui green button",
                                      text: "Activate",
                                    })
                                    .notify(
                                      "click",
                                      {
                                        type: "instances-run",
                                        data: {
                                          run: function (modal, dom, obj) {
                                            var instanceName =
                                              modal.form.process().fields
                                                .instance.value;

                                            if (!instanceName) {
                                              sb.dom.alerts.alert(
                                                "Error",
                                                "Every application needs a name.",
                                                "error"
                                              );
                                              return;
                                            }

                                            obj.instance = instanceName;
                                            obj.main_contact = contact.id;

                                            sb.dom.alerts.ask(
                                              {
                                                title: "Are you sure?",
                                                text: "This will activate the account and begin the free trial.",
                                              },
                                              function (resp) {
                                                if (resp) {
                                                  modal.save.loading();
                                                  swal.disableButtons();

                                                  sb.data.db.obj.getById(
                                                    "users",
                                                    +sb.data.cookie.get("uid"),
                                                    function (currentUser) {
                                                      delete currentUser.date_created;

                                                      sb.data.db.controller(
                                                        "saveNewInstanceObject&api_webform=true&pagodaAPIKey=" +
                                                          obj.instance,
                                                        {
                                                          instanceObj: obj,
                                                          user: currentUser,
                                                        },
                                                        function (newInstance) {
                                                          // instance object and files created
                                                          // begin copying objects if object types have been selected
                                                          sb.dom.alerts.alert(
                                                            "Success!",
                                                            "",
                                                            "success"
                                                          );
                                                          dom.activate.hide();

                                                          singleObjectView(
                                                            dom,
                                                            contact
                                                          );
                                                        },
                                                        adminURL +
                                                          "_getAdmin.php?do="
                                                      );
                                                    }
                                                  );
                                                }
                                              }
                                            );
                                          }.bind({}, modal, dom, obj),
                                        },
                                      },
                                      sb.moduleId
                                    );

                                  modal.patch();
                                },
                              });

                              dom.patch();

                              dom.activate.show();
                            }.bind({}, dom, instance),
                          },
                        },
                        sb.moduleId
                      );

                      break;
                  }
                });

                dom.patch();
              }
            },
            adminURL + "_getAdmin.php?do="
          );
        },
        adminURL + "_getAdmin.php?do="
      );
    }
  }

  function singleView(obj, dom, something, draw) {
    var components = [],
      pageModules = [],
      settingsModules = [];

    function addNewComponent(obj) {
      this.newCol.makeNode("cont", "container", {
        css: "pda-container pda-Panel pda-panel-green",
      });

      this.newCol.cont.makeNode("title", "headerText", {
        text: "New Component",
        size: "x-small",
      });

      this.newCol.cont.makeNode("titleBreak", "lineBreak", {});

      this.newCol.cont.makeNode("form", "form", {
        pageModule: {
          name: "pageModule",
          type: "string",
          label: "Component File Name",
        },
      });

      this.newCol.cont
        .makeNode("save", "button", {
          text: '<i class="fa fa-plus"></i> Add Component',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                var moduleName = this.newCol.cont.form.process();

                if (moduleName.completed == false) {
                  sb.dom.alerts.alert(
                    "Please provide a file name",
                    "",
                    "error"
                  );
                  return;
                }

                this.newCol.cont.save.loading();

                components.splice(0, 0, moduleName.fields.pageModule.value);

                printModuleList(this, components);

                this.newCol.empty();
                this.newCol.patch();
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );

      this.newCol.makeNode("break", "lineBreak", {});

      this.newCol.patch();
    }

    function addNewModule(obj) {
      this.newCol.makeNode("cont", "container", {
        css: "pda-container pda-Panel pda-panel-green",
      });

      this.newCol.cont.makeNode("title", "headerText", {
        text: "New Page Module",
        size: "x-small",
      });

      this.newCol.cont.makeNode("titleBreak", "lineBreak", {});

      this.newCol.cont.makeNode("form", "form", {
        pageModule: {
          name: "pageModule",
          type: "string",
          label: "Page Module File Name",
        },
      });

      this.newCol.cont
        .makeNode("save", "button", {
          text: '<i class="fa fa-plus"></i> Add Module',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                var moduleName = this.newCol.cont.form.process();

                if (moduleName.completed == false) {
                  sb.dom.alerts.alert(
                    "Please provide a file name",
                    "",
                    "error"
                  );
                  return;
                }

                this.newCol.cont.save.loading();

                pageModules.splice(0, 0, moduleName.fields.pageModule.value);

                printModuleList(this, pageModules);

                this.newCol.empty();
                this.newCol.patch();
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );

      this.newCol.makeNode("break", "lineBreak", {});

      this.newCol.patch();
    }

    function chooseComponents(obj) {
      var form1 = this.col1.cont.form.process(),
        form2 = this.col2.cont.form.process();

      /*
			if(form1.completed == false || form2.completed == false){
				sb.dom.alerts.alert('All fields required', 'Please fill out all form fields.', 'error');
				return;
			}
*/

      var newInstanceObj = {};

      _.each(form1.fields, function (f, name) {
        newInstanceObj[name] = f.value;
      });

      _.each(form2.fields, function (f, name) {
        newInstanceObj[name] = f.value;
      });

      this.btns.save.loading();
      this.btns.back.css("pda-btn-disabled");

      var modForm = {
          modules: {
            type: "checkbox",
            label: "Modules to include",
            name: "modules",
            options: [],
          },
        },
        compForm = {
          components: {
            type: "checkbox",
            label: "Components to include",
            name: "components",
            options: [],
          },
        },
        settingsForm = {
          settings: {
            type: "checkbox",
            label: "Settings to include",
            name: "settings",
            options: [],
          },
        };

      _.each(pageModules, function (mod) {
        modForm.modules.options.push({
          name: "module",
          label: mod,
          value: mod,
          checked: true,
        });
      });

      _.each(components, function (comp) {
        compForm.components.options.push({
          name: "component",
          label: comp,
          value: comp,
          checked: true,
        });
      });

      _.each(settingsModules, function (set) {
        settingsForm.settings.options.push({
          name: "setting",
          label: set,
          value: set,
          checked: true,
        });
      });

      this.makeNode("title", "headerText", {
        text: "Modules, Components & Settings",
      });

      this.makeNode("col1", "column", { width: 4 })
        .makeNode("cont", "container", { css: "pda-container" })
        .makeNode("form", "form", modForm);
      this.makeNode("col2", "column", { width: 4 })
        .makeNode("cont", "container", { css: "pda-container" })
        .makeNode("form", "form", compForm);
      this.makeNode("col3", "column", { width: 4 })
        .makeNode("cont", "container", { css: "pda-container" })
        .makeNode("form", "form", settingsForm);

      delete this.btns.back;

      this.btns
        .makeNode("save", "button", {
          text: 'Next Step <i class="fa fa-arrow-right"></i>',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: copyObjects.bind(this, obj, newInstanceObj),
            },
          },
          sb.moduleId
        );

      this.patch();
    }

    function copyObjects(obj, newInstanceObj) {
      var form1 = this.col1.cont.form.process(),
        form2 = this.col2.cont.form.process(),
        form3 = this.col3.cont.form.process();

      if (
        form1.completed == false ||
        form2.completed == false ||
        form3.completed == false
      ) {
        sb.dom.alerts.alert(
          "Error",
          "Please choose at least 1 module, component and setting to include in the new instance.",
          "error"
        );
        return;
      }

      /*
			newInstanceObj.pageModules = form1.fields.module.value.toString();
			newInstanceObj.components = form2.fields.component.value.toString();
			newInstanceObj.settingsModules = form3.fields.setting.value.toString();
*/

      delete this.btns.save;

      this.empty();

      this.makeNode("btns", "buttonGroup", { css: "pull-right" });

      this.makeNode("title", "headerText", { text: "Copy Objects" });

      this.btns.makeNode("save", "button", {
        text: '<i class="fa fa-circle-o-notch fa-spin"></i> Loading',
        css: "pda-btn-primary",
      });

      this.patch();

      var dom = this;

      sb.data.db.blueprint.getAll(function (bps) {
        var objectsForm = {
          objects: {
            type: "checkbox",
            name: "objects",
            label: "Choose the object types to copy to the new instance",
            options: [],
          },
        };

        bps = _.sortBy(bps, "blueprint_name");

        _.each(bps, function (o) {
          objectsForm.objects.options.push({
            name: "object",
            label: o.blueprint_name,
            value: o.blueprint_name,
          });
        });

        dom.makeNode("cont", "container", { css: "pda-container" });

        dom.cont.makeNode("form", "form", objectsForm);

        dom.btns
          .makeNode("save", "button", {
            text: 'Create Instance <i class="fa fa-arrow-right"></i>',
            css: "pda-btn-green",
          })
          .notify(
            "click",
            {
              type: "instances-run",
              data: {
                run: function (obj, newInstanceObj) {
                  var objectsToCopy = [];

                  if (this.cont.form.process().completed) {
                    objectsToCopy = _.map(
                      this.cont.form.process().fields.objects.value,
                      function (o) {
                        return o.trim();
                      }
                    );
                  }

                  this.btns.save.loading();

                  readyToCreateInstance(
                    this,
                    obj,
                    newInstanceObj,
                    objectsToCopy
                  );
                }.bind(dom, obj, newInstanceObj),
              },
            },
            sb.moduleId
          );

        dom.patch();
      });
    }

    function duplicateInstance(obj) {
      this.empty();

      this.makeNode("btns", "buttonGroup", { css: "pull-right" });

      this.makeNode("title", "headerText", {
        text: "Duplicating " + obj.systemName + " instance",
      });

      var formObj1 = {
          section1: {
            type: "section",
            name: "names",
            label: "Instance Name",
            fields: {
              systemName: {
                name: "systemName",
                label: "System Name (top left)",
                type: "text",
                value: obj.systemName + " copy",
              },
              instance: {
                name: "instance",
                label: "Instance Name (internal use - no spaces, lowercase)",
                type: "text",
                value: obj.instance + "copy",
              },
            },
          },
          section2: {
            type: "section",
            name: "sources",
            label: "Framework File Paths",
            fields: {
              settingSource: {
                name: "settingSource",
                label: "Settings Module File Path",
                type: "text",
                value: obj.settingSource,
              },
              componentSource: {
                name: "componentSource",
                label: "Component File Path",
                type: "text",
                value: obj.componentSource,
              },
              moduleSource: {
                name: "moduleSource",
                label: "Page Module File Path",
                type: "text",
                value: obj.moduleSource,
              },
              factorySource: {
                name: "factorySource",
                label: "Factory File Path",
                type: "text",
                value: obj.factorySource,
              },
            },
          },
          section3: {
            type: "section",
            name: "database",
            label: "Database Paths",
            fields: {
              db_read: {
                name: "db_read",
                label: "Database Read Path",
                type: "text",
                value: obj.db_read,
              },
              db_write: {
                name: "db_write",
                label: "Database Write Path",
                type: "text",
                value: obj.db_write,
              },
              db_post: {
                name: "db_post",
                label: "Database POST Path",
                type: "text",
                value: obj.db_post,
              },
            },
          },
        },
        formObj2 = {
          section1: {
            type: "section",
            name: "mandrill",
            label: "Mandrill Setup",
            fields: {
              emailFrom: {
                name: "emailFrom",
                label: "Email From Address",
                type: "text",
                value: obj.emailFrom,
              },
              mandrill_api: {
                name: "mandrill_api",
                label: "Mandrill API Key",
                type: "text",
                value: obj.mandrill_api,
              },
            },
          },
          section2: {
            type: "section",
            name: "twilio",
            label: "Twilio Setup",
            fields: {
              sms_from: {
                name: "sms_from",
                label: "SMS From Number",
                type: "text",
                value: obj.sms_from,
              },
              twilio_sid: {
                name: "twilio_sid",
                label: "Twilio SID",
                type: "text",
                value: obj.twilio_sid,
              },
              twilio_token: {
                name: "twilio_token",
                label: "Twilio Token",
                type: "text",
                value: obj.twilio_token,
              },
            },
          },
          section3: {
            type: "section",
            name: "files",
            label: "File System Setup",
            fields: {
              files_bucket: {
                name: "files_bucket",
                label: "Files Bucket",
                type: "text",
                value: obj.files_bucket,
              },
              files_delete: {
                name: "files_delete",
                label: "Files Delete API Path",
                type: "text",
                value: obj.files_delete,
              },
              files_read: {
                name: "files_read",
                label: "Files Read API Path",
                type: "text",
                value: obj.files_read,
              },
              files_write: {
                name: "files_write",
                label: "Files Write API Path",
                type: "text",
                value: obj.files_write,
              },
            },
          },
        };

      this.makeNode("titleBreak", "lineBreak", {});

      this.makeNode("col1", "column", { width: 6 });
      this.col1.makeNode("title", "headerText", {
        text: "Instance Details",
        size: "x-small",
      });
      this.col1.makeNode("cont", "container", { css: "pda-container" });

      this.makeNode("col2", "column", { width: 6 });
      this.col2.makeNode("title", "headerText", {
        text: "Other Details",
        size: "x-small",
      });
      this.col2.makeNode("cont", "container", { css: "pda-container" });

      this.col1.cont.makeNode("form", "form", formObj1);
      this.col2.cont.makeNode("form", "form", formObj2);

      this.btns
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Back',
          css: "pda-btn-orange",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                singleView(obj, this);
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );
      this.btns
        .makeNode("save", "button", {
          text: 'Next Step <i class="fa fa-arrow-right"></i>',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: chooseComponents.bind(this, obj),
            },
          },
          sb.moduleId
        );

      this.patch();
    }

    function editBillingInfo(obj) {
      var ui = this,
        taxRates = [];

      // utility funcs
      function getOptionsData(callback) {
        if (_.isEmpty(taxRates)) {
          sb.data.db.obj.getAll("tax_rates", function (data) {
            taxRates = data;
            callback();
          });
        }
      }

      function processUpdates() {
        var billingInfo = this.columns.r.seg.form.process().fields;
        var btn = this.h.btns.save;

        var saveObj = {
          id: obj.id,
          main_contact: obj.main_contact.id,
          credit: parseInt(billingInfo.credit.value),
          flat_fee: parseInt(billingInfo.flat_fee.value),
          flat_user_cap: parseInt(billingInfo.flat_user_cap.value),
          // 					last_invoice:,
          price_per_user: parseInt(billingInfo.price_per_user.value),
          qty_of_users: parseInt(billingInfo.qty_of_users.value),
          trial_end_date: billingInfo.trial_end_date.value,
          trial_start_date: billingInfo.trial_start_date.value,
          is_template: billingInfo.is_template.value,
        };

        sb.dom.alerts.ask(
          {
            title: "Are you sure?",
            text: "This will save your changes and apply them to the instance immediatly.",
          },
          function (resp) {
            if (resp) {
              swal.disableButtons();

              btn.loading(true);
              sb.data.db.controller(
                "updateObject&pagodaAPIKey=" + obj.instance,
                { objectType: "instances", objectData: saveObj },
                function (updatedObj) {
                  if (updatedObj.instance == appConfig.instance) {
                    window.location.reload();
                  } else {
                    btn.loading(false);
                    updatedObj.main_contact = obj.main_contact;
                    sb.dom.alerts.alert("Success", "", "success");
                    singleView(updatedObj, dom);
                  }
                },
                adminURL + "_getAdmin.php?do="
              );
            }
          }
        );
      }

      function updatePrimaryContact(contact) {
        obj.main_contact = contact;
        ui.columns.l.primaryContact.disp.view(obj.main_contact, true);
      }

      // views
      function contactOptionsView(contacts, patch) {}

      function primaryContactView(contact, patch, skip) {
        this.empty();

        if (skip || contact === undefined || _.isEmpty(contact)) {
          this.makeNode("main", "div", { css: "" });
          this.patch();

          var searchTableSetup = {
            domObj: this.main,
            tableTitle: "Main contact search",
            objectType: "contacts",
            searchObjects: [
              {
                name: "First name",
                value: "fname",
              },
              {
                name: "Last name",
                value: "lname",
              },
            ],
            navigation: false,
            headerButtons: {
              reload: {
                name: "Reload",
                css: "pda-btn-blue",
                action: function () {},
              },
            },
            data: function (page, callback) {
              sb.data.db.obj.getAll(
                "contacts",
                function (data) {
                  callback(data);
                },
                0,
                page
              );
            },
            visibleCols: {
              disp: "",
              button: "",
            },
            cells: {
              disp: function (obj, container) {
                if (obj) {
                  container.makeNode("title", "div", {
                    text: obj.fname + " " + obj.lname,
                    size: "ui header",
                  });
                }
              },
              button: function (obj, container) {
                container
                  .makeNode("btn", "div", {
                    css: "ui mini blue button",
                    tag: "button",
                    text: "Make account owner",
                  })
                  .notify(
                    "click",
                    {
                      type: "instances-run",
                      data: {
                        run: updatePrimaryContact.bind({}, obj),
                      },
                    },
                    sb.moduleId
                  );
              },
            },
            rowLink: false,
            rowSelection: false,
            multiSelectButtons: {},
            rowStyle: "padding:0px;",
            cellStyles: ["padding:0px;"],
          };

          if (components.hasOwnProperty("mainContactSearch")) {
            components.mainContactSearch.destroy();
          }

          components.mainContactSearch = sb.createComponent("crud-table");
          components.mainContactSearch.notify({
            type: "show-table",
            data: searchTableSetup,
          });

          this.makeNode("message", "headerText", {
            text: "No contact specified",
            css: "text-center text-muted",
            size: "x-small",
          });
        } else {
          this.makeNode("name", "div", {
            css: "ui large header",
            text: contact.fname + " " + contact.lname,
          });

          this.makeNode("change", "div", {
            tag: "button",
            css: "ui orange button",
            text: "Change account owner",
          });

          this.change.notify(
            "click",
            {
              type: "instances-run",
              data: {
                run: function (dom, contact, patch) {
                  primaryContactView.call(dom, contact, patch, true);
                }.bind({}, this, contact, patch),
              },
            },
            sb.moduleId
          );
        }

        if (patch) {
          this.patch();
        }
      }

      function adminUsersView(users, patch) {
        this.empty();

        if (patch) {
          this.patch();
        }
      }

      getOptionsData(function () {
        ui.empty();

        // header
        ui.makeNode("h", "div", { css: "" });
        ui.makeNode("hBreak", "div", { text: "<br />" });

        ui.h.makeNode("title", "div", {
          css: "ui huge header",
          text: "Edit billing info",
        });

        ui.h.makeNode("btns", "div", { css: "ui buttons" });

        ui.h.btns
          .makeNode("back", "button", {
            text: '<i class="fa fa-arrow-left"></i> Back',
            css: "pda-btn-orange",
          })
          .notify(
            "click",
            {
              type: "instances-run",
              data: {
                run: function (obj) {
                  singleView(obj, ui);
                }.bind(ui, obj),
              },
            },
            sb.moduleId
          );

        ui.h.btns.makeNode("save", "button", {
          text: '<i class="fa fa-floppy-o"></i> Save Changes',
          css: "pda-btn-green",
        });

        // form
        var formArgs = {
          qty_of_users: {
            name: "qty_of_users",
            type: "number",
            value: obj.qty_of_users,
            label: "Number of users",
          },
          // search field for ui
          // 				primary_contact:{},
          flat_fee: {
            name: "flat_fee",
            type: "usd",
            value: obj.flat_fee,
            label: "Flat fee",
          },
          flat_user_cap: {
            name: "flat_user_cap",
            type: "number",
            value: obj.flat_user_cap,
            label: "Free user cap",
          },
          price_per_user: {
            name: "price_per_user",
            type: "usd",
            value: obj.price_per_user,
            label: "Price per user",
          },
          trial_start_date: {
            name: "trial_start_date",
            type: "date",
            value: obj.trial_start_date,
            label: "Trial period start date",
          },
          trial_end_date: {
            name: "trial_end_date",
            type: "date",
            value: obj.trial_end_date,
            label: "Trial period end date",
          },
          /*
					instance_type:{
						name:'instance_type',
						type:'select',
						value:obj.instance_type,
						label:'Instance type',
						options:[]
					},
	*/
          // % amount of deduction off next payment
          credit: {
            name: "credit",
            type: "number",
            value: obj.credit,
            label: "Credit (%)",
          },
          /*
	last_invoice:{
						name:'last_invoice',
						type:'date',
						value:obj.last_invoice,
						label:'Last invoice'
					},
	*/
          tax_rate: {
            name: "tax_rate",
            type: "select",
            value: obj.tax_rate || 0,
            label: "Tax rate",
            options: [],
          },
          is_template: {
            name: "is_template",
            type: "select",
            value: obj.is_template || "no",
            label: "Is this a template?",
            options: [
              {
                name: "No",
                value: "no",
              },
              {
                name: "Yes",
                value: "yes",
              },
            ],
          },
          // do this w/ search field too
          // 				admin_users:{}
        };

        _.each(taxRates, function (rate) {
          formArgs.tax_rate.options.push({
            value: rate.id,
            name: rate.name,
          });
        });

        ui.makeNode("columns", "div", { css: "ui grid" });
        var uiCol = ui.columns;

        // main contact
        uiCol.makeNode("l", "div", { css: "twelve wide column" });

        uiCol.l
          .makeNode("primaryContact", "div", { css: "ui basic segment" })
          .makeNode("title", "div", {
            text: "Main contact",
            css: "ui header",
          });

        uiCol.l.primaryContact.makeNode("disp", "div", {});
        uiCol.l.primaryContact.disp.view = primaryContactView;
        uiCol.l.primaryContact.disp.view(obj.main_contact);

        uiCol.l.primaryContact.makeNode("options", "div", {});
        uiCol.l.primaryContact.options.view = contactOptionsView;

        // admin users
        /*
	ui.l.makeNode('users', 'container', {})
					.makeNode('title', 'headerText', {
						text:'Admin users',
						size:'x-small'
					});
	*/

        /*
	ui.l.users.makeNode('disp', 'container', {});
				ui.l.users.disp.view = adminUsersView;
				ui.l.users.disp.view(obj.adminUsersView);
	*/

        uiCol
          .makeNode("r", "div", { css: "four wide column" })
          .makeNode("seg", "div", { css: "ui orange segment" });
        uiCol.r.seg.makeNode("title", "div", {
          css: "ui header",
          text: "Billing Info",
        });
        uiCol.r.seg.makeNode("form", "form", formArgs);

        // save listener
        ui.process = processUpdates;
        ui.h.btns.save.notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function () {
                ui.process();
              }.bind(ui),
            },
          },
          sb.moduleId
        );

        ui.patch();
      });
    }

    function editComponents(obj) {
      this.empty();

      this.makeNode("btns", "buttonGroup", { css: "pull-right" });
      this.btns
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Back',
          css: "pda-btn-orange",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                singleView(obj, this);
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );
      this.btns
        .makeNode("new", "button", {
          text: '<i class="fa fa-plus"></i> New Component',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: addNewComponent.bind(this, obj),
            },
          },
          sb.moduleId
        );
      this.btns
        .makeNode("save", "button", {
          text: '<i class="fa fa-floppy-o"></i> Save Changes',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: saveComponents.bind(this, obj),
            },
          },
          sb.moduleId
        );

      this.makeNode("title", "headerText", { text: "Edit Components" });
      this.makeNode("titleBreak", "lineBreak", {});

      this.makeNode("newCol", "column", { width: 8, offset: 2 });

      this.makeNode("col1", "column", { width: 8, offset: 2 });

      this.patch();

      printComponentList(this, components);
    }

    function editInstance(obj) {
      this.empty();

      var applicationVersions = [
        {
          value: "bento",
          name: "Bento",
        },
        {
          value: "bin",
          name: "BIN",
        },
        {
          value: "tlb",
          name: "The Life Book",
        },
        {
          value: "capd",
          name: "Captin D's",
        },
      ];

      if (obj.version) {
        applicationVersions = _.map(applicationVersions, function (v) {
          if (v.value == obj.version) {
            v.selected = true;
          }

          return v;
        });
      }

      var formObj1 = {
        systemName: {
          name: "systemName",
          label: "System Name (top left)",
          type: "text",
          value: obj.systemName,
        },
        version: {
          name: "version",
          label: "Instance Type",
          type: "select",
          options: applicationVersions,
        },
        settingSource: {
          name: "settingSource",
          label: "Settings Module File Path",
          type: "text",
          value: obj.settingSource,
        },
        componentSource: {
          name: "componentSource",
          label: "Component File Path",
          type: "text",
          value: obj.componentSource,
        },
        moduleSource: {
          name: "moduleSource",
          label: "Page Module File Path",
          type: "text",
          value: obj.moduleSource,
        },
        factorySource: {
          name: "factorySource",
          label: "Factory File Path",
          type: "text",
          value: obj.factorySource,
        },
        db_read: {
          name: "db_read",
          label: "Database Read Path",
          type: "text",
          value: obj.db_read,
        },
        db_write: {
          name: "db_write",
          label: "Database Write Path",
          type: "text",
          value: obj.db_write,
        },
        db_post: {
          name: "db_post",
          label: "Database POST Path",
          type: "text",
          value: obj.db_post,
        },
        mailchimp_api: {
          name: "mailchimp_api",
          label: "MailChimp API Key",
          type: "text",
          value: obj.mailchimp_api,
        },
        emailFrom: {
          name: "emailFrom",
          label: "Email From Address",
          type: "text",
          value: obj.emailFrom,
        },
        mandrill_api: {
          name: "mandrill_api",
          label: "Mandrill API Key",
          type: "text",
          value: obj.mandrill_api,
        },
        sms_from: {
          name: "sms_from",
          label: "SMS From Number",
          type: "text",
          value: obj.sms_from,
        },
        twilio_sid: {
          name: "twilio_sid",
          label: "Twilio SID",
          type: "text",
          value: obj.twilio_sid,
        },
        twilio_token: {
          name: "twilio_token",
          label: "Twilio Token",
          type: "text",
          value: obj.twilio_token,
        },
        files_bucket: {
          name: "files_bucket",
          label: "Files Bucket",
          type: "text",
          value: obj.files_bucket,
        },
        files_delete: {
          name: "files_delete",
          label: "Files Delete API Path",
          type: "text",
          value: obj.files_delete,
        },
        files_read: {
          name: "files_read",
          label: "Files Read API Path",
          type: "text",
          value: obj.files_read,
        },
        files_write: {
          name: "files_write",
          label: "Files Write API Path",
          type: "text",
          value: obj.files_write,
        },
      };

      this.makeNode("title", "div", {
        css: "ui huge header",
        text: "Editing " + obj.systemName,
      });

      this.makeNode("btns", "buttonGroup", { css: "" });

      this.makeNode("col1", "div", { css: "ui orange clearing segment" })
        .makeNode("cont", "div", { css: "" })
        .makeNode("form", "form", formObj1);

      this.btns
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Go Back',
          css: "pda-btn-orange",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                singleView(obj, this);
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );

      this.btns
        .makeNode("save", "button", {
          text: '<i class="fa fa-floppy-o"></i> Save Changes',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                var dom = this;

                sb.dom.alerts.ask(
                  {
                    title: "Are you sure?",
                    text: "The changes will be applied immediately.",
                  },
                  function (resp) {
                    if (resp) {
                      swal.disableButtons();

                      var form1 = dom.col1.cont.form.process();

                      /*
								if(form1.completed == false || form2.completed == false){
									sb.dom.alerts.alert('Error', 'All fields are required.', 'error');
									return;
								}
*/

                      var instanceObj = {
                        id: obj.id,
                      };

                      _.each(form1.fields, function (field, name) {
                        instanceObj[name] = field.value;
                      });

                      /*
								_.each(form2.fields, function(field, name){
									
									instanceObj[name] = field.value;
									
								});
*/
                      sb.data.db.controller(
                        "updateObject&pagodaAPIKey=" + obj.instance,
                        { objectType: "instances", objectData: instanceObj },
                        function (updatedObj) {
                          sb.dom.alerts.alert("Success!", "", "success");

                          singleView(updatedObj, dom);
                        },
                        adminURL + "_getAdmin.php?do="
                      );
                    }
                  }
                );
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );

      this.patch();
    }

    function editPageModules(obj) {
      this.empty();

      this.makeNode("btns", "buttonGroup", { css: "pull-right" });
      this.btns
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Back',
          css: "pda-btn-orange",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                singleView(obj, this);
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );
      this.btns
        .makeNode("new", "button", {
          text: '<i class="fa fa-plus"></i> New Module',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: addNewModule.bind(this, obj),
            },
          },
          sb.moduleId
        );
      this.btns
        .makeNode("save", "button", {
          text: '<i class="fa fa-floppy-o"></i> Save Changes',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: savePageModules.bind(this, obj),
            },
          },
          sb.moduleId
        );

      this.makeNode("title", "headerText", { text: "Edit Page Modules" });
      this.makeNode("titleBreak", "lineBreak", {});

      this.makeNode("newCol", "column", { width: 8, offset: 2 });

      this.makeNode("col1", "column", { width: 8, offset: 2 });

      this.patch();

      printModuleList(this, pageModules);
    }

    function editSettingsTypes(obj) {
      this.empty();

      // buttons
      this.makeNode("btns", "buttonGroup", { css: "pull-right" })
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Go Back',
          css: "pda-btn-orange",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                singleView(obj, this);
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );

      // title
      this.makeNode("title", "headerText", {
        text: "Editing " + obj.systemName,
      });

      // form
      var formArgs = {
        settingsList: {
          name: "settingsList",
          label: "Settings objects (seperate by commas)",
          type: "textbox",
          rows: 5,
          value: obj.settings_objects || "",
        },
      };

      this.makeNode("form", "form", formArgs);

      this.btns
        .makeNode("save", "button", {
          text: '<i class="fa fa-floppy-o"></i> Save Changes',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (obj) {
                var dom = this;

                sb.dom.alerts.ask(
                  {
                    title: "Are you sure?",
                    text: "The changes will be applied immediately.",
                  },
                  function (resp) {
                    if (resp) {
                      swal.disableButtons();

                      var formData = dom.form.process();

                      var instanceObj = {
                        id: obj.id,
                        settings_objects: formData.fields.settingsList.value,
                      };

                      sb.data.db.controller(
                        "updateObject&pagodaAPIKey=" + obj.instance,
                        { objectType: "instances", objectData: instanceObj },
                        function (updatedObj) {
                          sb.dom.alerts.alert("Success!", "", "success");

                          singleView(updatedObj, dom);
                        },
                        adminURL + "_getAdmin.php?do="
                      );
                    }
                  }
                );
              }.bind(this, obj),
            },
          },
          sb.moduleId
        );

      this.patch();
    }

    function loginToInstance(obj) {
      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();

            sb.data.db.obj.getById(
              "users",
              +sb.data.cookie.userId,
              function (user) {
                sb.data.db.controller(
                  "getUserAccountsNew",
                  { email: user.email, multi: true },
                  function (accounts) {
                    var hasAccount = _.where(accounts, {
                      instance: obj.instance,
                    });

                    if (hasAccount.length > 0) {
                      sb.data.db.controller(
                        "deleteCookie",
                        {
                          series: sb.data.cookie.get("series"),
                          token: sb.data.cookie.get("token"),
                        },
                        function (deleted) {
                          if (deleted) {
                            sb.data.db.controller(
                              "createCookie&pagodaAPIKey=" + obj.instance,
                              { staffId: hasAccount[0].id, login: true },
                              function (newCookie) {
                                window.open("/app/" + obj.instance, "_self");
                              }
                            );
                          }
                        },
                        adminURL + "_getAdmin.php?do="
                      );
                    } else {
                      user.instance = obj.instance;
                      delete user.id;

                      sb.data.db.controller(
                        "createNewObject&pagodaAPIKey=" + obj.instance,
                        { objectType: "users", objectData: user },
                        function (newUser) {
                          sb.data.db.controller(
                            "deleteCookie",
                            {
                              series: sb.data.cookie.get("series"),
                              token: sb.data.cookie.get("token"),
                            },
                            function (deleted) {
                              if (deleted) {
                                sb.data.db.controller(
                                  "createCookie&pagodaAPIKey=" + obj.instance,
                                  { staffId: newUser.id, login: true },
                                  function (newCookie) {
                                    window.open(
                                      "/app/" + obj.instance,
                                      "_self"
                                    );
                                  }
                                );
                              }
                            },
                            adminURL + "_getAdmin.php?do="
                          );
                        },
                        adminURL + "_getAdmin.php?do="
                      );
                    }
                  }
                );
              }
            );
          }
        }
      );
    }

    function movePageModule(pageModule, currentPosition, direction) {
      function move(currentArray, old_index, new_index) {
        if (new_index >= currentArray.length) {
          var k = new_index - currentArray.length;
          while (k-- + 1) {
            currentArray.push(undefined);
          }
        }
        currentArray.splice(new_index, 0, currentArray.splice(old_index, 1)[0]);
        return currentArray; // for testing purposes
      }

      if (currentPosition == 0 && direction == "up") {
        return;
      }

      if (currentPosition == pageModules.length && direction == "down") {
        return;
      }

      var newPosition = 0;

      if (direction == "down") {
        newPosition = currentPosition + 1;
      } else {
        newPosition = currentPosition - 1;
      }

      move(pageModules, currentPosition, newPosition);

      printModuleList(this, pageModules);
    }

    function printComponentList(dom, components) {
      dom.col1.empty();

      _.each(components, function (p, k) {
        dom.col1.makeNode("module-" + k, "container", {
          css: "pda-container pda-background-gray pda-Panel",
        });

        dom.col1["module-" + k].makeNode("name", "headerText", {
          text: p,
          size: "small",
          css: "pull-left",
        });

        dom.col1["module-" + k].makeNode("btns", "buttonGroup", {
          css: "pull-right",
        });

        dom.col1["module-" + k].btns
          .makeNode("delete", "button", {
            text: '<i class="fa fa-trash-o"></i>',
            css: "pda-btn-red",
          })
          .notify(
            "click",
            {
              type: "instances-run",
              data: {
                run: removeComponent.bind(dom, p),
              },
            },
            sb.moduleId
          );

        dom.col1.makeNode("break-" + k, "lineBreak", {});
      });

      dom.col1.patch();
    }

    function printModuleList(dom, pageModules) {
      dom.col1.empty();

      _.each(pageModules, function (p, k) {
        dom.col1.makeNode("module-" + k, "container", {
          css: "pda-container pda-background-gray pda-Panel",
        });

        dom.col1["module-" + k].makeNode("name", "headerText", {
          text: p,
          size: "small",
          css: "pull-left",
        });

        dom.col1["module-" + k].makeNode("btns", "buttonGroup", {
          css: "pull-right",
        });

        dom.col1["module-" + k].btns
          .makeNode("delete", "button", {
            text: '<i class="fa fa-trash-o"></i>',
            css: "pda-btn-red",
          })
          .notify(
            "click",
            {
              type: "instances-run",
              data: {
                run: removePageModule.bind(dom, p),
              },
            },
            sb.moduleId
          );
        dom.col1["module-" + k].btns
          .makeNode("up", "button", {
            text: '<i class="fa fa-arrow-up"></i>',
            css: "pda-btn-blue",
          })
          .notify(
            "click",
            {
              type: "instances-run",
              data: {
                run: movePageModule.bind(dom, p, k, "up"),
              },
            },
            sb.moduleId
          );
        dom.col1["module-" + k].btns
          .makeNode("down", "button", {
            text: '<i class="fa fa-arrow-down"></i>',
            css: "pda-btn-blue",
          })
          .notify(
            "click",
            {
              type: "instances-run",
              data: {
                run: movePageModule.bind(dom, p, k, "down"),
              },
            },
            sb.moduleId
          );

        dom.col1.makeNode("break-" + k, "lineBreak", {});
      });

      dom.col1.patch();
    }

    function readyToCreateInstance(dom, obj, newInstanceObj, objectsToCopy) {
      newInstanceObj.copyFrom = obj.instance;
      newInstanceObj.enabled = 1;

      sb.data.db.controller(
        "saveNewInstanceObject&pagodaAPIKey=" + newInstanceObj.instance,
        newInstanceObj,
        function (newInstance) {
          // instance object and files created
          // begin copying objects if object types have been selected

          singleView(newInstance, dom);
        },
        adminURL + "_getAdmin.php?do="
      );
    }

    function removeComponent(component) {
      var dom = this;

      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "Your changes will not be saved immediatly. Please click the save button in the top righ to save your changes.",
          closeOnConfirm: true,
        },
        function (resp) {
          if (resp) {
            components = _.reject(components, function (p) {
              return p == component;
            });

            printComponentList(dom, components);
          }
        }
      );
    }

    function removePageModule(pageModule) {
      var dom = this;

      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "Your changes will not be saved immediatly. Please click the save button in the top righ to save your changes.",
          closeOnConfirm: true,
        },
        function (resp) {
          if (resp) {
            pageModules = _.reject(pageModules, function (p) {
              return p == pageModule;
            });

            printModuleList(dom, pageModules);
          }
        }
      );
    }

    function saveComponents(obj) {
      var dom = this;

      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "This will save your changes and apply them to the instance immediatly.",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();

            var instanceObj = {
              id: obj.id,
              components: components.toString(),
            };

            sb.data.db.controller(
              "updateObject&pagodaAPIKey=" + obj.instance,
              { objectType: "instances", objectData: instanceObj },
              function (updatedObj) {
                if (updatedObj.instance == appConfig.instance) {
                  window.location.reload();
                } else {
                  sb.dom.alerts.alert("Success", "", "success");
                  singleView(updatedObj, dom);
                }
              },
              adminURL + "_getAdmin.php?do="
            );
          }
        }
      );
    }

    function savePageModules(obj) {
      var dom = this;

      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "This will save your changes and apply them to the instance immediatly.",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();

            var instanceObj = {
              id: obj.id,
              pageModules: pageModules.toString(),
            };

            sb.data.db.controller(
              "updateObject&pagodaAPIKey=" + obj.instance,
              { objectType: "instances", objectData: instanceObj },
              function (updatedObj) {
                if (updatedObj.instance == appConfig.instance) {
                  window.location.reload();
                } else {
                  sb.dom.alerts.alert("Success", "", "success");
                  singleView(updatedObj, dom);
                }
              },
              adminURL + "_getAdmin.php?do="
            );
          }
        }
      );
    }

    dom.empty();

    dom.makeNode("title", "div", {
      text: obj.systemName,
      css: "ui huge header",
    });

    dom.makeNode("btns", "buttonGroup", { css: "" });

    //dom.btns.makeNode('edit', 'button', {text:'<i class="fa fa-pencil"></i> Edit', css:'pda-btn-orange'});

    dom.btns
      .makeNode("editI", "button", {
        text: '<i class="fa fa-pencil"></i> Edit Details',
        css: "pda-btn-orange",
      })
      .notify(
        "click",
        {
          type: "instances-run",
          data: {
            run: editInstance.bind(dom, obj),
          },
        },
        sb.moduleId
      );
    /*
		dom.btns2.makeNode('editP', 'button', {text:'<i class="fa fa-pencil"></i> Edit Modules', css:'pda-btn-orange'}).notify('click', {
			type:'instances-run',
			data:{
				run:editPageModules.bind(dom, obj)
			}
		}, sb.moduleId);
		dom.btns2.makeNode('editM', 'button', {text:'<i class="fa fa-pencil"></i> Edit Components', css:'pda-btn-orange'}).notify('click', {
			type:'instances-run',
			data:{
				run:editComponents.bind(dom, obj)
			}
		}, sb.moduleId);
*/
    /*
		dom.btns2.makeNode('editS', 'button', {text:'<i class="fa fa-pencil"></i> Edit Settings Objs', css:'pda-btn-orange'}).notify('click', {
			type:'instances-run',
			data:{
				run:editSettingsTypes.bind(dom, obj)
			}
		}, sb.moduleId);
*/
    dom.btns
      .makeNode("editB", "button", {
        text: '<i class="fa fa-pencil"></i> Edit billing info',
        css: "pda-btn-orange",
      })
      .notify(
        "click",
        {
          type: "instances-run",
          data: {
            run: editBillingInfo.bind(dom, obj),
          },
        },
        sb.moduleId
      );

    // fill w/empty data if undefined to stop from erroring out
    if (!obj.billing_plan) {
      obj.billing_plan = {
        minUsers: 1,
        maxUsers: 1,
        userPrice: 0,
      };
    }
    if (!obj.main_contact) {
      obj.main_contact = {};
    }
    if (!obj.sold_by) {
      obj.sold_by = {};
    }

    dom.makeNode("colBreak", "lineBreak", {});

    dom
      .makeNode("col1", "column", { width: 12 })
      .makeNode("cont", "container", {
        css: "pda-container",
        collapse: true,
        title: "Info",
      });

    //dom.makeNode('col1Break', 'lineBreak', {spaces:2});

    dom
      .makeNode("col3", "column", { width: 12 })
      .makeNode("cont", "container", {
        collapse: true,
        title: "Technical Details",
        css: "pda-container",
      });

    var mainContact = '<i class="text-muted">Not set</i>';
    if (obj.main_contact) {
      if (obj.main_contact.fname) {
        mainContact = obj.main_contact.fname + " " + obj.main_contact.lname;
      }
    }

    var billingPlan = '<i class="text-muted">Not set</i>';
    if (obj.billing_plan) {
      var userPrice = 0;
      var maxUsers = "Infinite";
      if (obj.billing_plan.userPrice) {
        userPrice = obj.billing_plan.userPrice;
      }
      if (obj.billing_plan.maxUsers > 0) {
        maxUsers = obj.billing_plan.maxUsers;
      }

      if (maxUsers == "Infinite") {
        billingPlan = "$" + userPrice + ".00 per month";
      } else {
        billingPlan =
          "Up to " +
          obj.billing_plan.maxUsers +
          " users @ $" +
          maxUsers * userPrice +
          ".00 per month";
      }
    }

    var soldBy = '<i class="text-muted">Not set</i>';
    if (obj.sold_by) {
      if (obj.sold_by.fname) {
        soldBy = obj.sold_by.fname + " " + obj.sold_by.lname;
      }
    }

    dom.col1.cont.makeNode("mainContact", "headerText", {
      size: "x-small",
      text: "<small>Main Contact:</small> " + mainContact,
    });
    dom.col1.cont.makeNode("freeTrial", "headerText", {
      size: "x-small",
      text:
        "<small>Free Trial:</small> " +
        moment(obj.trial_start_date).format("M/D/YYYY") +
        " - " +
        moment(obj.trial_end_date).format("M/D/YYYY"),
    });
    dom.col1.cont.makeNode("plan", "headerText", {
      size: "x-small",
      text: "<small>Billing Plan:</small> " + billingPlan,
    });
    dom.col1.cont.makeNode("soldBy", "headerText", {
      size: "x-small",
      text: "<small>Sold By:</small>  " + soldBy,
    });

    dom.col3.cont.makeNode("db", "headerText", {
      text: "Database",
      size: "small",
    });
    dom.col3.cont.makeNode("read", "headerText", {
      text: "Read: " + obj.db_read,
      size: "x-small",
    });
    dom.col3.cont.makeNode("write", "headerText", {
      text: "Write: " + obj.db_write,
      size: "x-small",
    });
    dom.col3.cont.makeNode("post", "headerText", {
      text: "Post: " + obj.db_post,
      size: "x-small",
    });
    dom.col3.cont.makeNode("dbBreak", "lineBreak", {});

    dom.col3.cont.makeNode("sources", "headerText", {
      text: "Sources",
      size: "small",
    });
    dom.col3.cont.makeNode("components", "headerText", {
      text: "Component Source: " + obj.componentSource,
      size: "x-small",
    });
    dom.col3.cont.makeNode("factory", "headerText", {
      text: "Factory Source: " + obj.factorySource,
      size: "x-small",
    });
    dom.col3.cont.makeNode("module", "headerText", {
      text: "Module Source: " + obj.moduleSource,
      size: "x-small",
    });
    dom.col3.cont.makeNode("settings", "headerText", {
      text: "Settings Source: " + obj.settingsSource,
      size: "x-small",
    });
    dom.col3.cont.makeNode("sourcesBreak", "lineBreak", {});

    dom.col3.cont.makeNode("mailchimp", "headerText", {
      text: "MailChimp",
      size: "small",
    });
    dom.col3.cont.makeNode("mailchimpapi", "headerText", {
      text: "API: " + obj.mailchimp_api,
      size: "x-small",
    });
    dom.col3.cont.makeNode("mailchimpBreak", "lineBreak", {});

    dom.col3.cont.makeNode("mandrill", "headerText", {
      text: "Mandrill",
      size: "small",
    });
    dom.col3.cont.makeNode("api", "headerText", {
      text: "API: " + obj.mandrill_api,
      size: "x-small",
    });
    dom.col3.cont.makeNode("emailFrom", "headerText", {
      text: "Email From: " + obj.emailFrom,
      size: "x-small",
    });
    dom.col3.cont.makeNode("mandrillBreak", "lineBreak", {});

    dom.col3.cont.makeNode("twilio", "headerText", {
      text: "Twilio",
      size: "small",
    });
    dom.col3.cont.makeNode("twilio_sid", "headerText", {
      text: "SID: " + obj.twilio_sid,
      size: "x-small",
    });
    dom.col3.cont.makeNode("twilio_token", "headerText", {
      text: "Token: " + obj.twilio_token,
      size: "x-small",
    });
    dom.col3.cont.makeNode("sms_from", "headerText", {
      text: "From Number: " + obj.sms_from,
      size: "x-small",
    });
    dom.col3.cont.makeNode("twilioBreak", "lineBreak", {});

    dom.col3.cont.makeNode("files", "headerText", {
      text: "Files",
      size: "small",
    });
    dom.col3.cont.makeNode("bucket", "headerText", {
      text: "Bucket: " + obj.files_bucket,
      size: "x-small",
    });
    dom.col3.cont.makeNode("files_delete", "headerText", {
      text: "Delete: " + obj.files_delete,
      size: "x-small",
    });
    dom.col3.cont.makeNode("files_read", "headerText", {
      text: "Read: " + obj.files_read,
      size: "x-small",
    });
    dom.col3.cont.makeNode("files_write", "headerText", {
      text: "Write: " + obj.files_write,
      size: "x-small",
    });
    dom.col3.cont.makeNode("twilioBreak", "lineBreak", {});

    dom.btns
      .makeNode("duplicate", "button", {
        text: '<i class="fa fa-clone"></i> Duplicate',
        css: "pda-btn-primary",
      })
      .notify(
        "click",
        {
          type: "instances-run",
          data: {
            run: duplicateInstance.bind(dom, obj),
          },
        },
        sb.moduleId
      );
    dom.btns
      .makeNode("login", "button", {
        text: '<i class="fa fa-sign-in"></i> Login To Instance',
        css: "pda-btn-primary",
      })
      .notify(
        "click",
        {
          type: "instances-run",
          data: {
            run: loginToInstance.bind(dom, obj),
          },
        },
        sb.moduleId
      );

    dom.patch();

    if (_.isFunction(draw)) {
      draw(false);
    }
  }

  function salesDashboard(ui, state, draw) {
    // view cache
    var instances = [],
      comm_schedule = {
        first_month_perc: 0.5,
        ongoing_perc: 0.03,
        ongoing_start_month: 3,
        ongoing_num_months: 12,
      };

    // utitlity funcs
    function get_data(callback) {
      sb.data.db.controller(
        "getInstancesBy&api_webform=true",
        {
          objectType: "instances",
          queryObj: { sold_by: parseInt(sb.data.cookie.userId) },
          getChildObjs: 1,
        },
        function (data) {
          instances = data;
          callback();
        },
        adminURL + "_getAdmin.php?do="
      );
    }

    function getMonthlyCommission(instances, month) {
      // !TESTING
      /*
instances = [{
				flat_fee:32000,
				date_created:'2017-12-13 13:32:34.607442',
				price_per_user:2500,
				trial_start_date:'2017-12-13 13:32:34.607442',
				trial_end_date:'2017-22-13 13:32:34.607442',
				flat_user_cap:10,
				qty_of_users:12
			}, {
				flat_fee:32000,
				date_created:'2018-01-13 13:32:34.607442',
				price_per_user:2500,
				trial_start_date:'2018-01-13 13:32:34.607442',
				trial_end_date:'2018-02-13 13:32:34.607442',
				flat_user_cap:10,
				qty_of_users:12
			}, {
				flat_fee:32000,
				date_created:'2018-01-13 13:32:34.607442',
				price_per_user:2500,
				trial_start_date:'2018-01-13 13:32:34.607442',
				trial_end_date:'2018-02-13 13:32:34.607442',
				flat_user_cap:10,
				qty_of_users:12
			}, {
				flat_fee:32000,
				date_created:'2018-01-13 13:32:34.607442',
				price_per_user:2500,
				trial_start_date:'2017-11-13 13:32:34.607442',
				trial_end_date:'2017-12-13 13:32:34.607442',
				flat_user_cap:10,
				qty_of_users:12
			}, {
				flat_fee:32000,
				date_created:'2017-01-13 13:32:34.607442',
				price_per_user:2500,
				trial_start_date:'2017-01-13 13:32:34.607442',
				trial_end_date:'2017-02-13 13:32:34.607442',
				flat_user_cap:10,
				qty_of_users:12
			}];
*/

      var comm = {
        total: 0,
        from_base: 0,
        from_previous: 0,
        sales: 0,
        monthName: month.format("MMMM"),
        month: month.clone(),
      };

      _.each(instances, function (instance) {
        var instanceDate = moment(instance.date_created, "YYYY-MM-DD hh:mm:ss");
        var diff = month.diff(instanceDate, "months");

        if (month.isSame(instanceDate, "month")) {
          if (instance.billing_plan.hasOwnProperty("userPrice")) {
            comm.from_base +=
              +instance.billing_plan.userPrice *
              +instance.billing_plan.maxUsers *
              100;
          }

          comm.sales++;
        } else if (
          diff > comm_schedule.ongoing_start_month &&
          diff <
            comm_schedule.ongoing_num_months + comm_schedule.ongoing_start_month
        ) {
          if (instance.billing_plan.hasOwnProperty("userPrice")) {
            comm.from_previous +=
              +instance.billing_plan.userPrice *
              +instance.billing_plan.maxUsers *
              100;
          }
        }
      });

      comm.total = +comm.from_base + comm.from_previous;

      return comm;
    }

    // views
    function salesSummaryView(instances) {
      var comm = getMonthlyCommission(instances, moment()),
        lastMonthComm = getMonthlyCommission(
          instances,
          moment().subtract(1, "month")
        );

      // this month's commission
      this.makeNode("comm", "column", { width: 4 });

      this.comm.makeNode("comm", "headerText", {
        css: "text-center",
        text: '<i class="fa fa-usd"></i>' + (comm.total / 100).toFixed(2),
      });

      this.comm.makeNode("label", "headerText", {
        size: "x-small",
        css: "text-center text-muted",
        text: "my commission",
      });

      // num sales
      this.makeNode("sales", "column", { width: 4 });

      this.sales.makeNode("val", "headerText", {
        css: "text-center",
        text: comm.sales,
      });

      this.sales.makeNode("label", "headerText", {
        size: "x-small",
        css: "text-center text-muted",
        text: "sales",
      });

      // last month comp
      var ratio = moment().date() / moment().daysInMonth(),
        compare_to_last =
          (comm.total - ratio * lastMonthComm.total) /
          (ratio * lastMonthComm.total);

      var arrowColor = "red",
        arrowDir = "down";
      if (compare_to_last > 0) {
        arrowColor = "primary";
        arrowDir = "up";
      }

      if (compare_to_last !== Infinity) {
        this.makeNode("comp", "column", { width: 4 });

        this.comp.makeNode("val", "headerText", {
          css: "text-center",
          text:
            '<i class="fa fa-arrow-' +
            arrowDir +
            " pda-" +
            arrowColor +
            '"></i> ' +
            compare_to_last.toFixed(1) +
            "%",
        });

        this.comp.makeNode("label", "headerText", {
          size: "x-small",
          css: "text-center text-muted",
          text: "from last month",
        });
      }
    }

    function commissionBreakdownView(instances) {
      var comms = [];
      for (var i = -3; i < 7; i++) {
        comms.push(getMonthlyCommission(instances, moment().add(i, "month")));
      }

      var chartSetup = {
        type: "line",
        data: {
          labels: _.pluck(comms, "monthName"),
          datasets: [
            {
              label: "My commission",
              lineTension: 0,
              data: _.map(comms, function (comm) {
                return {
                  y: (comm.total / 100).toFixed(2),
                  x: comm.month,
                };
              }),
              backgroundColor: "#0EABA7",
              borderColor: "#0EABA7",
            },
          ],
          fill: true,
        },
        options: {
          bezierCurve: false,
          scales: {
            xAxes: [
              {
                type: "time",
                time: {},
              },
            ],
            yAxes: [
              {
                stacked: true,
                ticks: {
                  beginAtZero: true,
                  callback: function (tick, index, ticks) {
                    return (
                      "$" +
                      tick.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                    );
                  },
                },
              },
            ],
          },
          tooltips: {
            callbacks: {
              title: function (tooltipItem) {
                return tooltipItem[0].xLabel.format("MMMM, YYYY");
              },
              label: function (tooltipItem) {
                return (
                  "$" +
                  tooltipItem.yLabel
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                );
              },
            },
          },
          title: "My commission",
        },
      };

      this.makeNode("chart", "chart", chartSetup);
    }

    function instancesListView(instances) {
      this.makeNode("list", "table", {
        css: "table-hover table-condensed",
        columns: {
          view: "",
          name: "Name",
          sellDate: "Sold on",
          enabled: "Enabled?",
        },
      });

      _.each(
        instances,
        function (instance) {
          var enabled = "No";
          if (instance.enabled === 1) {
            enabled = "Yes";
          }

          var freeTrailEndDate = moment(
              instance.trial_end_date,
              "YYYY-MM-DD hh:mm:ss"
            ),
            trialString = "";

          if (moment().isAfter(freeTrailEndDate)) {
            trialString =
              '<br /><i class="text-muted">Free trial ended ' +
              freeTrailEndDate.format("l") +
              "</i>";
          } else {
            trialString =
              '<br /><i class="pda-primary">Free trial until ' +
              freeTrailEndDate.format("l") +
              "</i>";
          }

          this.list.makeRow(instance.instance, [
            "",
            instance.systemName + " (" + instance.instance + ")",
            moment(instance.date_created, "YYYY-MM-DD hh:mm:ss").format("l") +
              trialString,
            enabled,
          ]);

          this.list.body[instance.instance].view
            .makeNode("btn", "button", {
              text: '<i class="fa fa-eye fa-2x"></i>',
              css: "pda-transparent",
            })
            .notify(
              "click",
              {
                type: "app-navigate-to",
                data: {
                  itemId: "instances",
                  viewId: {
                    id: "single-" + instance.id,
                    type: "table-single-item",
                    title: instance.systemName,
                    icon: '<i class="fa fa-th"></i>',
                    setup: {
                      objectType: "instances",
                    },
                    rowObj: instance,
                    removable: true,
                    parent: "sales-dashboard",
                  },
                },
              },
              sb.moduleId
            );
        },
        this
      );
    }

    if (!appConfig.user.parent) {
      // setup
      get_data(function () {
        ui.makeNode("button", "button", {
          text: '<i class="fa fa-users"></i> Manage Team',
          css: "pda-btn-primary pull-right",
        }).notify(
          "click",
          {
            type: "app-navigate-to",
            data: {
              itemId: "instances",
              viewId: {
                id: "sales-team",
                type: "custom",
                title: "My team",
                icon: '<i class="fa fa-users"></i>',
                dom: salesTeam,
                removable: true,
              },
            },
          },
          sb.moduleId
        );

        ui.makeNode("title", "headerText", {
          size: "small",
          text: moment().format("MMMM, YYYY") + "<hr>",
        });

        ui.makeNode("l", "column", { width: 7 });
        ui.l.view = salesSummaryView;
        ui.l.view(instances);

        ui.makeNode("r", "column", { width: 5 });
        ui.r.view = commissionBreakdownView;
        ui.r.view(instances);

        ui.makeNode("u", "column", { width: 12 });
        ui.u.view = instancesListView;
        ui.u.view(instances);

        draw(ui);
      });
    }
  }

  function salesTeam(dom, state, draw) {
    function createNewTeamMember(selected, dom) {
      dom.body.empty();

      dom.body.makeNode("title", "headerText", {
        text: '<i class="fa fa-user"></i> Add a team member',
        size: "small",
      });

      dom.body.makeNode("break", "lineBreak", {});

      dom.body.makeNode("form", "form", {
        fname: {
          name: "fname",
          label: "First Name",
          type: "text",
        },
        lname: {
          name: "lname",
          label: "Last Name",
          type: "text",
        },
        email: {
          name: "email",
          label: "Email",
          type: "text",
        },
        phone: {
          name: "phone",
          label: "Phone",
          type: "text",
        },
      });

      dom.footer
        .makeNode("button", "button", {
          text: '<i class="fa fa-check"></i> Save',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function (dom) {
                var formData = dom.body.form.process().fields;

                if (dom.body.form.process().completed == false) {
                  sb.dom.alerts.alert(
                    "Error",
                    "All fields are required.",
                    "error"
                  );
                  return;
                }

                dom.footer.button.loading();

                var staffObj = {
                  service: [1216945],
                  base: [1216943],
                  hire_date: moment().unix(),
                  parent: +sb.data.cookie.userId,
                };
                var userObj = {
                  enabled: 1,
                  jobType: [1216945],
                  type: "staff",
                  parent: +sb.data.cookie.userId,
                };

                _.each(formData, function (fieldObj, fieldName) {
                  staffObj[fieldName] = fieldObj.value;
                  userObj[fieldName] = fieldObj.value;
                });

                sb.data.db.createNewPassword(function (passwordObj) {
                  userObj.password = passwordObj.pwdHash;

                  sb.data.db.obj.create(
                    "staff",
                    staffObj,
                    function (newStaffObj) {
                      userObj.related_object = newStaffObj.id;

                      sb.data.db.obj.create(
                        "users",
                        userObj,
                        function (newUserObj) {
                          newStaffObj.user = newUserObj.id;

                          sb.data.db.obj.update(
                            "staff",
                            newStaffObj,
                            function (updatedStaffObj) {
                              var emailObj = {
                                to: newUserObj.email,
                                from: "",
                                subject: "New User Account",
                                mergevars: {
                                  TITLE:
                                    "New user account for " +
                                    appConfig.instance,
                                  BODY:
                                    "You can login to your account by clicking the login button below.<br /><br />Your login email: " +
                                    newUserObj.email +
                                    "<br />Your password: " +
                                    passwordObj.pwd,
                                  BUTTON: "Log In",
                                  BUTTON_LINK: "https://bento.infinityhospitality.net/",
                                },
                                emailtags: ["password reset email"],
                              };

                              sb.comm.sendEmail(emailObj, function (response) {
                                dom.hide();

                                components.table.notify({
                                  type: "update-table",
                                  data: {},
                                });
                              });
                            }
                          );
                        }
                      );
                    }
                  );
                });
              }.bind({}, dom),
            },
          },
          sb.moduleId
        );

      dom.body.patch();
      dom.footer.patch();
    }

    dom.makeNode("title", "headerText", {
      text: '<i class="fa fa-users"></i> My Team',
    });

    dom.makeNode("break", "lineBreak", { spaces: 2 });

    dom.makeNode("tableCont", "container", {});

    draw(dom);

    draw({
      dom: dom,
      after: function (dom) {
        var crudSetup = {
          domObj: dom.tableCont,
          searchObjects: false,
          settings: false,
          filters: false,
          download: false,
          title: false,
          /* 						navigation:false, */
          container: false,
          headerButtons: {
            reload: {
              name: "Reload",
              css: "pda-btn-blue",
              action: function () {},
            },
            create: {
              name: '<i class="fa fa-plus"></i> Create Team Member',
              css: "pda-btn-green",
              domType: "modal",
              action: createNewTeamMember,
            },
          },
          multiSelectButtons: {
            erase: {
              name: '<i class="fa fa-times"></i> Delete',
              css: "pda-btn-red",
              domType: "erase",
              action: "erase",
            },
          },
          home: false,
          rowSelection: true,
          rowLink: {
            type: "tab",
            header: function (obj) {
              return obj.fname + " " + obj.lname;
            },
            action: function () {},
          },
          visibleCols: {
            fname: "First Name",
            lname: "Last Name",
            email: "Email Address",
            phone: "Phone Number",
            date_created: "Date Created",
          },
          cells: {
            date_created: function (obj) {
              return moment(obj).fromNow();
            },
          },
          objectType: "users",
          childObjs: 0,
          data: function (paged, callback) {
            sb.data.db.obj.getWhere(
              "users",
              {
                parent: +sb.data.cookie.userId,
                paged: paged,
              },
              function (data) {
                callback(data);
              }
            );
          },
        };

        components.table.notify({
          type: "show-table",
          data: crudSetup,
        });
      },
    });
  }

  // component views
  function addInstanceToContact(ui, state, draw) {
    var newInstance = {},
      options = {
        templates: [],
      },
      // must be included in all instances
      coreComps = [
        "_app",
        "_charts",
        "_crudTable",
        "_users",
        "calendar",
        "companies",
        "contact",
        "contactInfo",
        "csv-uploader",
        "decisions",
        "emails",
        "rules",
        "tags",
        "tasks",
      ],
      billingDefaults = {
        flat_fee: 35000,
        flat_user_cap: 10,
        price_per_user: 2500,
        trial_start_date: moment(),
        trial_end_date: moment().add(14, "days"),
        credit: 0,
      },
      plans = [
        {
          minUsers: 1,
          maxUsers: 5,
          userPrice: 35,
          monthlyPrice: 175,
        },
        {
          minUsers: 6,
          maxUsers: 10,
          userPrice: 34,
          monthlyPrice: 340,
        },
        {
          minUsers: 11,
          maxUsers: 15,
          userPrice: 33,
          monthlyPrice: 505,
        },
        {
          minUsers: 16,
          maxUsers: 20,
          userPrice: 32,
          monthlyPrice: 620,
        },
        {
          minUsers: 21,
          maxUsers: 25,
          userPrice: 31,
          monthlyPrice: 700,
        },
        {
          minUsers: 26,
          maxUsers: 35,
          userPrice: 30,
          monthlyPrice: 840,
        },
        {
          minUsers: 36,
          maxUsers: 45,
          userPrice: 29,
          monthlyPrice: 900,
        },
        {
          minUsers: 46,
          maxUsers: 55,
          userPrice: 28,
          monthlyPrice: 840,
        },
      ];

    // utitlity functions
    function getFormOptions(callback) {
      sb.data.db.controller(
        "getInstancesBy&api_webform=true",
        { objectType: "instances", queryObj: { is_template: "yes" } },
        function (data) {
          options.templates = data;

          sb.data.db.obj.getAll("tax_rates", function (taxRates) {
            options.taxRates = taxRates;
            callback();
          });
        },
        adminURL + "_getAdmin.php?do="
      );
    }

    // form steps
    function instanceTypeForm() {
      function processStep() {
        var formData = this.form.process().fields,
          container = this,
          btn = this.header.btns.next;

        if (
          formData.instance_name.value
            .replace(/\s+/g, "_")
            .replace(/[^a-zA-Z0-9 -]/g, "")
            .toLowerCase() !== formData.instance_name.value
        ) {
          sb.dom.alerts.alert(
            "Instance name cannot have uppercase characters or spaces",
            "No spaces, uppercase or special characters",
            "error"
          );

          return false;
        }

        btn.loading(true);

        sb.data.db.controller(
          "getInstancesBy&api_webform=true",
          {
            objectType: "instances",
            queryObj: { instance: formData.instance_name.value },
          },
          function (conflictInstances) {
            var takenNames = _.pluck(conflictInstances, "instance");
            if (_.contains(takenNames, formData.instance_name.value)) {
              sb.dom.alerts.alert(
                '"' + formData.instance_name.value + '" is already being used',
                "Try another name",
                "error"
              );

              btn.loading(false);
              return false;
            } else {
              newInstance.instance = formData.instance_name.value;
              newInstance.systemName = formData.system_name.value;
              //newInstance.instance_type = parseInt(formData.template.value);

              ui.stepThree();
              //ui.stepTwo();
            }
          },
          adminURL + "_getAdmin.php?do="
        );
      }

      this.empty();

      // header
      this.makeNode("header", "container", {}).makeNode("btns", "buttonGroup", {
        css: "pull-right",
      });

      this.header.btns
        .makeNode("back", "button", {
          text: '<i class="fa fa-times"></i> Cancel',
          css: "pda-btnOutline-red",
        })
        .notify(
          "click",
          {
            type: "app-navigate-back",
            data: {},
          },
          sb.moduleId
        );

      this.header.btns.makeNode("next", "button", {
        text: 'Next <i class="fa fa-arrow-right"></i>',
        css: "pda-btn-primary",
      });

      this.makeNode("title", "headerText", {
        text:
          "Create new instance for " +
          state.contact.fname +
          " " +
          state.contact.lname +
          " <small>(" +
          state.contact.company.name +
          ")</small>",
        size: "small",
      });

      this.makeNode("linebreak", "lineBreak", {});

      // form
      var formSetup = {
        system_name: {
          name: "system_name",
          type: "text",
          label: "System name (what the users will see in the system)",
          value: newInstance.systemName || state.contact.company.name || "",
        },
        instance_name: {
          name: "instance_name",
          type: "text",
          label:
            "Instance name (will be used for the system's url--all lowercase; no spaces;)",
          value:
            newInstance.instance ||
            state.contact.company.name
              .replace(/\s+/g, "_")
              .replace(/[^a-zA-Z0-9 -]/g, "")
              .toLowerCase() ||
            "",
        },
      };

      _.each(options.templates, function (template) {
        formSetup.template.options.push({
          value: template.id,
          name: template.instance,
        });
      });

      this.makeNode("form", "form", formSetup);

      this.process = processStep;

      this.header.btns.next.notify(
        "click",
        {
          type: "instances-run",
          data: {
            run: function () {
              this.process();
            }.bind(this),
          },
        },
        sb.moduleId
      );

      this.patch();
    }

    function customizeComponentsForm() {
      function processStep() {
        var comps = this.form.process().fields.components.value;

        comps = coreComps.concat(comps);
        newInstance.components = comps.toString();
        ui.stepThree();
      }

      this.empty();

      // header
      this.makeNode("header", "container", {}).makeNode("btns", "buttonGroup", {
        css: "pull-right",
      });

      this.header.btns
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Back',
          css: "pda-btn-primary",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function () {
                ui.stepOne();
                ui.patch();
              },
            },
          },
          sb.moduleId
        );

      this.header.btns.makeNode("next", "button", {
        text: '<i class="fa fa-arrow-right"></i> Next',
        css: "pda-btn-primary",
      });

      this.makeNode("title", "headerText", {
        text: "Select systems",
        size: "small",
      });

      // form
      var template = _.findWhere(options.templates, {
          id: newInstance.instance_type,
        }),
        compsList = newInstance.components || template.components;

      compsList = compsList.split(",");

      var formSetup = {
        components: {
          name: "components",
          type: "checkbox",
          label: "Systems included",
          value: compsList,
          options: [],
        },
      };

      var compOptions = template.components.split(",");

      compOptions = _.chain(compOptions)
        // don't show core comps
        .filter(function (comp) {
          return !_.contains(coreComps, comp);
        })
        // sort alphabetically
        .sortBy(function (a) {
          return a;
        })
        .value();

      _.each(compOptions, function (comp) {
        formSetup.components.options.push({
          name: "components",
          value: comp,
          label: comp,
        });
      });

      this.makeNode("form", "form", formSetup);

      this.process = processStep;

      this.header.btns.next.notify(
        "click",
        {
          type: "instances-run",
          data: {
            run: function () {
              this.process();
            }.bind(this),
          },
        },
        sb.moduleId
      );

      this.patch();
    }

    function billingInformation() {
      function processStep(planType, planObj) {
        var formData = this.col2.cont.plan1.form.process().fields;

        var rootUserData = this.col1.cont.form.process();

        if (rootUserData.completed == false) {
          sb.dom.alerts.alert(
            "All fields required for Admin User",
            "",
            "error"
          );
          return;
        }

        /*
				if(parseInt(formData.flat_fee.value) < 0){
					sb.dom.alerts.alert('Flat fee/month must be non-negative', '', 'error');
					return false;
				}
				if(parseInt(formData.flat_user_cap.value) < 0){
					sb.dom.alerts.alert('Free user cap must be non-negative', '', 'error');
					return false;
				}
				if(parseInt(formData.price_per_user.value) < 0){
					sb.dom.alerts.alert('Price per user must be non-negative', '', 'error');
					return false;
				}
				if(parseFloat(formData.credit.value) < 0){
					sb.dom.alerts.alert('Credit percentage must be non-negative', '', 'error');
					return false;
				}
				if(parseFloat(formData.credit.value) > 100){
					sb.dom.alerts.alert('Credit percentage must be less than 100', '', 'error');
					return false;
				}
*/

        var selectedTemplate = _.findWhere(options.templates, {
          id: newInstance.instance_type,
        });

        var createObj = {
          // basic info
          instance: newInstance.instance,
          components: newInstance.components,
          systemName: newInstance.systemName,
          pageModules: "",
          settingsModules: "",
          enabled: 1,
          emailFrom: newInstance.instance + "@reply.voltz.software",
          main_contact: state.contact.id,
          sold_by: parseInt(sb.data.cookie.userId),
          copyFrom: "bento-cmd",

          // temp root user
          temp_user: {
            fname: rootUserData.fields.fname.value,
            lname: rootUserData.fields.lname.value,
            email: rootUserData.fields.email.value,
            phone: rootUserData.fields.phone.value,
          },

          // billing plan
          billing_type: planType,
          billing_plan: {},

          // billing info
          instance_type: newInstance.instance_type,
          credit: parseFloat(formData.credit.value),
          flat_fee: parseInt(formData.flat_fee.value),
          flat_user_cap: parseInt(formData.flat_user_cap.value),
          price_per_user: parseInt(formData.price_per_user.value),
          tax_rate: parseInt(formData.tax_rate.value),
          trial_end_date: formData.trial_end_date.value,
          trial_start_date: formData.trial_start_date.value,

          // sources
          componentSource:
            "https://bento.infinityhospitality.net/_repos/_production/notify/notify/",
          factorySource:
            "https://bento.infinityhospitality.net/_repos/_production/notify/notify/_factory/",
          moduleSource:
            "https://bento.infinityhospitality.net/_repos/_production/notify/notify/",
          settingSource:
            "https://bento.infinityhospitality.net/_repos/_production/notify/notify/",

          //copyFrom: selectedTemplate.instance,

          // db endpoints
          db_post:
            "https://bento.infinityhospitality.net/_repos/_production/notify/pagoda/_coredev/_post.php?",
          db_read:
            "https://bento.infinityhospitality.net/_repos/_production/notify/pagoda/_coredev/_get.php?",
          db_write:
            "https://bento.infinityhospitality.net/_repos/_production/notify/pagoda/_coredev/_get.php?",

          // files endpoints
          files_bucket:
            "https://bento.infinityhospitality.net/_repos/_production/pagoda/_files/_instances/",
          files_delete:
            "https://bento.infinityhospitality.net/_repos/_production/pagoda/_coredev/files/delete/?",
          files_read:
            "https://bento.infinityhospitality.net/_repos/_production/pagoda/_coredev/files/get/?",
          files_write:
            "https://bento.infinityhospitality.net/_repos/_production/pagoda/_coredev/files/post/?",

          // external apis
          mandrill_api: "**********************",
          sms_from: "6152050722",
          twilio_sid: "**********************************",
          twilio_token: "1bc16e9c21afe54bb3565a512b42beff",
        };

        if (planType == "package") {
          createObj.billing_plan = planObj;
        }

        sb.data.db.controller(
          "getStripeCustomer",
          { stripeId: state.contact.stripe_id },
          function (customer) {
            //if(1==2){
            if (!customer) {
              //btn.loading(false);

              sb.dom.alerts.alert("No credit card info", "", "error");

              return;
            } else {
              ui.stepFour(createObj);
            }
          }
        );
      }

      this.empty();

      // header
      this.makeNode("header", "container", {}).makeNode("btns", "buttonGroup", {
        css: "pull-left",
      });

      this.header
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Back',
          css: "pda-btn-primary pull-left",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function () {
                ui.stepOne();
              },
            },
          },
          sb.moduleId
        );

      this.makeNode("colBreak", "lineBreak", {});

      this.makeNode("col1", "column", { width: 4 }).makeNode(
        "cont",
        "container",
        { css: "pda-container" }
      );

      this.makeNode("col2", "column", { width: 8 }).makeNode(
        "cont",
        "container",
        { css: "pda-container pda-border-left" }
      );

      this.col1.cont.makeNode("infoTitle", "headerText", {
        text: "Admin user information",
        size: "small",
      });

      this.col1.cont.makeNode("subtitle", "headerText", {
        text: '<small><i class="fa fa-asterisk"></i> The contract will be sent to this user and they will be the first user/staff member in this system.</small>',
        size: "x-small",
      });

      var formSetup = {
        fname: {
          name: "fname",
          type: "text",
          value: state.contact.fname || "",
          label: "First Name",
        },
        lname: {
          name: "lname",
          type: "text",
          value: state.contact.lname || "",
          label: "Last Name",
        },
        email: {
          name: "email",
          type: "text",
          value: "",
          label: "Email Address",
        },
        phone: {
          name: "phone",
          type: "text",
          value: "",
          label: "Cell Phone",
        },
      };

      _.each(state.contact.contact_info, function (info) {
        if (info.is_primary == "yes") {
          switch (info.type.data_type) {
            case "email":
              formSetup.email.value = info.info;

              break;

            case "cellphone":
              formSetup.phone.value = info.info;

              break;
          }
        }
      });

      this.col1.cont.makeNode("form", "form", formSetup);

      this.col1.cont.makeNode("titleBreak", "lineBreak", {});

      this.col1.cont.makeNode("creditTitle", "headerText", {
        text: "Payment information",
        size: "small",
      });

      this.col1.cont.makeNode("creditSubtitle", "headerText", {
        text: '<small><i class="fa fa-asterisk"></i> The payment method won\'t be charged until the day after the free trial end date on the right.</small>',
        size: "x-small",
      });

      this.col1.cont.makeNode("paymentCont", "container", {});

      this.col2.cont.makeNode("title", "headerText", {
        text: "Choose a payment plan",
        size: "small",
      });

      this.col2.cont.makeNode("titleBreak", "lineBreak", {});

      this.col2.cont.makeNode("plan1", "container", {
        css: "pda-container pda-Panel pda-background-gray",
      });

      this.col2.cont.makeNode("planBreak", "lineBreak", {});

      this.col2.cont.makeNode("plans", "container", {});
      this.process = processStep;
      _.each(
        plans,
        function (plan) {
          this.col2.cont.plans.makeNode("plan-" + plan.maxUsers, "container", {
            css: "pda-container pda-Panel pda-background-gray",
          });
          this.col2.cont.plans["plan-" + plan.maxUsers]
            .makeNode("select", "button", {
              text: "Select",
              css: "pda-btn-green pull-right",
            })
            .notify(
              "click",
              {
                type: "instances-run",
                data: {
                  run: function () {
                    this.process("package", plan);
                  }.bind(this),
                },
              },
              sb.moduleId
            );
          this.col2.cont.plans["plan-" + plan.maxUsers].makeNode(
            "text",
            "headerText",
            {
              text:
                "$" +
                plan.maxUsers * plan.userPrice +
                " per month for up to " +
                plan.maxUsers +
                " users",
              size: "x-small",
            }
          );

          this.col2.cont.plans.makeNode("break-" + plan.maxUsers, "lineBreak", {
            spaces: 1,
          });
        },
        this
      );

      // form
      var formSetup = {
        flat_fee: {
          name: "flat_fee",
          type: "hidden",
          value: newInstance.flat_fee || billingDefaults.flat_fee,
          label: "Flat fee/month",
        },
        flat_user_cap: {
          name: "flat_user_cap",
          type: "hidden",
          value: newInstance.flat_user_cap || billingDefaults.flat_user_cap,
          label: "Free user cap",
        },
        price_per_user: {
          name: "price_per_user",
          type: "hidden",
          value: newInstance.price_per_user || billingDefaults.price_per_user,
          label: "Price per user/month",
        },
        trial_start_date: {
          name: "trial_start_date",
          type: "date",
          value:
            newInstance.trial_start_date || billingDefaults.trial_start_date,
          label: "Trial period start date",
          dateFormat: "M/DD/YYYY",
        },
        trial_end_date: {
          name: "trial_end_date",
          type: "date",
          value: newInstance.trial_end_date || billingDefaults.trial_end_date,
          label: "Trial period end date",
          dateFormat: "M/DD/YYYY",
        },
        credit: {
          name: "credit",
          type: "hidden",
          value: newInstance.credit || billingDefaults.credit,
          label: "Credit (% off of next payment)",
        },
        tax_rate: {
          name: "tax_rate",
          type: "hidden",
          value: newInstance.tax_rate || 0,
          label: "Tax rate",
          options: [],
        },
      };

      _.each(options.taxRates, function (rate) {
        formSetup.tax_rate.options.push({
          value: rate.id,
          name: rate.name,
        });
      });

      this.col2.cont.plan1.makeNode("form", "form", formSetup);

      this.patch();

      sb.notify({
        type: "show-paymentMethod-button",
        data: {
          domObj: this.col1.cont.paymentCont,
          objectId: state.contact.id,
          compact: true,
        },
      });
    }

    function contractSetup(instanceToCreate) {
      function processStep(createObj, contractObj) {
        createObj.contract = contractObj.id;

        sb.data.db.controller(
          "saveNewInstanceObject&pagodaAPIKey=" + createObj.instance,
          createObj,
          function (created) {
            var emailObj = {
              to: created.temp_user.email,
              from: "",
              subject: "Welcome to Bento - Get Started",
              mergevars: {
                TITLE: "Welcome to Bento",
                BODY:
                  created.temp_user.fname +
                  ",<br /><br />" +
                  "Ready to make your business more efficient? Click below to get started setting up your new Bento Application." +
                  "<br /><br />" +
                  '<a href="https://bento.infinityhospitality.net/app/setup?iid=' +
                  created.id +
                  "&i=" +
                  created.instance +
                  '" target="_blank">Click here to get started</a>' +
                  "<br />",
              },
              emailtags: ["Instance setup email"],
            };

            // instance object and files created
            // begin copying objects if object types have been selected

            sb.comm.sendEmail(emailObj, function (sent) {
              sb.dom.alerts.alert(
                "Success!",
                "The application has been setup. A setup email has been sent to the Admin user.",
                "success"
              );

              singleView(created, ui);
            });
          },
          adminURL + "_getAdmin.php?do="
        );
      }

      this.empty();

      // header
      this.makeNode("header", "container", {}).makeNode("btns", "buttonGroup", {
        css: "pull-right",
      });

      this.header
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Back',
          css: "pda-btn-primary pull-left",
        })
        .notify(
          "click",
          {
            type: "instances-run",
            data: {
              run: function () {
                ui.stepThree();
              },
            },
          },
          sb.moduleId
        );

      this.header.btns.makeNode("create", "button", {
        text: 'Finish Setup <i class="fa fa-check"></i>',
        css: "pda-btn-green",
      });

      this.makeNode("colBreak", "lineBreak", {});

      this.makeNode("col2", "column", { width: 12 }).makeNode(
        "cont",
        "container",
        { css: "" }
      );

      this.col2.cont.makeNode("contracts", "container", {});

      // on click
      this.process = processStep;
      this.header.btns.create.notify(
        "click",
        {
          type: "instances-run",
          data: {
            run: function (instanceToCreate) {
              var dom = this;

              sb.dom.alerts.ask(
                {
                  title: "Are you sure?",
                  text: "This can take a few minutes to complete.",
                },
                function (resp) {
                  if (resp) {
                    swal.disableButtons();

                    dom.header.btns.create.loading();

                    sb.data.db.obj.getWhere(
                      "contracts",
                      { main_contact: state.contact.id },
                      function (contractObj) {
                        if (contractObj.length == 0) {
                          sb.dom.alerts.alert(
                            "Please choose a contract",
                            "",
                            "error"
                          );

                          dom.header.btns.create.loading(false);

                          return;
                        }

                        processStep(instanceToCreate, contractObj);
                      }
                    );
                  }
                }
              );
            }.bind(this, instanceToCreate),
          },
        },
        sb.moduleId
      );

      var contractId;

      var dom = this;

      sb.data.db.obj.getWhere(
        "contracts",
        { main_contact: state.contact.id },
        function (ret) {
          dom.patch();

          if (ret.length > 0) {
            contractId = ret[0].id;
          }

          sb.notify({
            type: "view-all-contracts",
            data: {
              objectId: state.contact.id,
              contactId: state.contact.id,
              contractId: contractId,
              type: "contacts",
              domObj: dom.col2.cont.contracts,
              objectView: dom.col2.cont.contracts,
              templateButton: "Proposal",
              draw: function (done) {
                done.dom.patch();

                done.after(done.dom);
              },
              onUpdate: function (contract) {
                instanceToCreate.contract = contract;
              },
              afterCreate: function (newContract, callback) {
                instanceToCreate.contract = newContract;

                callback(newContract);
              },
            },
          });
        }
      );
    }

    ui.makeNode("panel", "container", { css: "pda-container" });

    ui.stepOne = instanceTypeForm.bind(ui.panel);
    ui.stepTwo = customizeComponentsForm.bind(ui.panel);
    ui.stepThree = billingInformation.bind(ui.panel);
    ui.stepFour = contractSetup.bind(ui.panel);

    ui.stepOne();

    draw(ui);
  }

  function display_loader(ui, text) {
    ui.makeNode("loaderWrap", "div", {
      css: "text-center",
    });
    ui.loaderWrap.makeNode("loader", "loader", {});
    ui.loaderWrap.makeNode("loaderText", "div", {
      text: text,
    });
  }

  // Data CRUD
  function dataCRUD(ui) {
    var CRUDSetup = {};

    function load_blueprints(callback) {
      sb.data.db.blueprint.getAll(function (blueprints) {
        callback(blueprints);
      });
    }

    function display_CRUDCollections(ui, setup) {
      setup.actions = {};

      ui.empty();

      ui.makeNode("wrap", "div", {});

      setup.domObj = ui.wrap;

      ui.patch();

      sb.notify({
        type: "show-collection",
        data: setup,
      });
    }

    function select_bp(ui, bps) {
      var formObj = {
        bps: {
          name: "bp",
          type: "select",
          options: [],
          label: "Select a blueprint",
        },
      };

      function processBP(form, before, after) {
        var formData = form.process().fields;
        var bpObj = {};

        before();

        bpObj.name = formData.bp.value;

        CRUDSetup.objectType = bpObj.name;

        bpObj.object = _.findWhere(bps, {
          blueprint_name: bpObj.name,
        }).blueprint;

        after(bpObj);
      }

      function detect_bpOptions(ui, selectedBp) {
        var selectProps = [];
        var fields = [];

        CRUDSetup.fields = {};

        _.each(selectedBp.object, function (prop, propName) {
          if (prop.type === "select") {
            selectProps.push({
              name: propName,
              label: prop.name + " (" + propName + ")",
              options: prop.options,
            });
          }

          if (prop.immutable === false) {
            fields.push({
              name: propName,
              title: prop.name,
            });
          }
        });

        var nameField = _.reject(fields, function (o) {
          return o.name !== "name";
        });

        fields = _.reject(fields, function (o) {
          return o.name === "name";
        });

        if (!_.isEmpty(nameField)) {
          _.each(nameField, function (o) {
            fields.unshift({
              name: o.name,
              title: o.title,
            });
          });
        }

        _.each(fields, function (o) {
          CRUDSetup.fields[o.name] = {
            title: o.title,
            view: function (ui, obj) {
              ui.makeNode("v", "div", {
                text: obj[this.name],
              });
            },
          };
        });

        selectProps.push({
          name: "is_template",
          label: "is_template",
          options: [0, 1],
        });

        build_bpOptions(ui, selectProps);
      }

      function build_bpOptions(ui, bpOptions) {
        var formObj = {};

        function process(form, callback) {
          var formData = form.process().fields;
          var values = {};

          _.each(formData, function (field, name) {
            if (field.value !== null) {
              if (field.value !== "null") {
                values[name] = field.value;
              }
            }
          });

          callback(values);
        }

        _.each(bpOptions, function (obj, i) {
          formObj[obj.name] = {
            name: obj.name,
            type: "select",
            label: obj.label,
            options: [],
            value: null,
          };

          if (Array.isArray(obj.options)) {
            _.each(obj.options, function (option, i) {
              formObj[obj.name].options.push({
                name: option,
                value: option,
              });
            });
          } else {
            _.each(obj.options, function (val, key) {
              formObj[obj.name].options.push({
                name: val,
                value: key,
              });
            });
          }

          formObj[obj.name].options.unshift({
            name: null,
            value: null,
          });
        });

        ui.empty();

        ui.makeNode("wrap", "div", {});

        ui.wrap.makeNode("btnGrp", "div", {
          css: "ui right floated buttons",
        });

        ui.wrap.btnGrp
          .makeNode("back", "div", {
            text: "Back",
            css: "ui red button",
          })
          .notify(
            "click",
            {
              type: [sb.moduleId + "-run"],
              data: {
                run: function (data) {
                  select_bp(ui, bps);
                },
              },
            },
            sb.moduleId
          );

        ui.wrap.btnGrp.makeNode("or", "div", {
          css: "or",
        });

        ui.wrap.btnGrp
          .makeNode("next", "div", {
            text: "Next",
            css: "ui green button",
          })
          .notify(
            "click",
            {
              type: [sb.moduleId + "-run"],
              data: {
                run: function (data) {
                  process(ui.wrap.form, function (setup) {
                    CRUDSetup.where = setup;

                    display_CRUDCollections(ui, CRUDSetup);
                  });
                },
              },
            },
            sb.moduleId
          );

        ui.wrap.makeNode("lb_1", "lineBreak", { spaces: 2 });

        ui.wrap.makeNode("form", "form", formObj);

        ui.patch();
      }

      _.each(bps, function (bp, i) {
        formObj.bps.options.push({
          name: bp.blueprint_name,
          value: bp.blueprint_name,
        });
      });

      ui.empty();

      ui.makeNode("wrap", "div", {});

      ui.wrap.makeNode("form", "form", formObj);

      ui.wrap.makeNode("btnGrp", "div", {
        css: "ui right floated buttons",
      });

      ui.wrap.btnGrp
        .makeNode("next", "div", {
          text: "Next",
          css: "ui green button",
        })
        .notify(
          "click",
          {
            type: [sb.moduleId + "-run"],
            data: {
              run: function (data) {
                processBP(
                  ui.wrap.form,
                  function () {},
                  function (bpObj) {
                    detect_bpOptions(ui, bpObj);
                  }
                );
              },
            },
          },
          sb.moduleId
        );

      ui.wrap.makeNode("lb_1", "lineBreak", { spaces: 2 });

      ui.patch();
    }

    ui.empty();

    ui.makeNode("wrap", "div", {});

    display_loader(ui, "Fetching blueprints...");

    ui.patch();

    load_blueprints(function (bps) {
      select_bp(ui, bps);
    });
  }

  return {
    init: function () {
      if (
        appConfig.instance == "rickyvoltz" ||
        appConfig.instance == "zachvoltz" ||
        appConfig.instance == "joshgantt" ||
        appConfig.instance == "petermikhail" ||
        appConfig.instance == "voltzsoftware"
      ) {
        if (sb.sys.state.components.instances) {
          components.table = sb.createComponent("crud-table");

          sb.listen({
            "stop-instances-component": this.stop,
            "instances-run": this.run,
            "instances-single-object-view": this.singleObjectView,
            "add-instance-to-contact": this.addInstanceToContact,
            "single-instance-view": this.viewSingleInstance,
          });

          sb.notify({
            type: "register-application",
            data: {
              navigationItem: {
                id: "instances",
                title: "Instances",
                icon: '<i class="fa fa-usd"></i>',
                views: [
                  {
                    id: "table",
                    default: true,
                    type: "table",
                    title: "All Instances",
                    icon: '<i class="fa fa-th-list"></i>',
                    setup: {
                      objectType: "instances",
                      tableTitle: {
                        title: '<i class="fa fa-snowflake-o"></i> Instances',
                        size: "large",
                      },
                      home: false,
                      searchObjects: false,
                      filters: false,
                      download: false,
                      settings: false,
                      headerButtons: {
                        reload: {
                          name: "Reload",
                          css: "pda-btn-blue",
                          action: function () {},
                        },
                      },
                      calendar: false,
                      rowSelection: true,
                      multiSelectButtons: {},
                      rowLink: {
                        type: "tab",
                        header: function (obj) {
                          return obj.systemName;
                        },
                        action: singleView,
                      },
                      visibleCols: {
                        systemName: "System Name",
                        enabled: "Enabled",
                      },
                      dateRange: "start_date",
                      cells: {
                        systemName: function (obj) {
                          return obj.systemName;
                        },
                        enabled: function (obj) {
                          return obj.enabled;
                        },
                      },
                      childObjs: 1,
                      singleDataCall: function (objId, callback) {
                        sb.data.db.controller(
                          "getInstancesBy&api_webform=true&pagodaAPIKey=" +
                            appConfig.instance,
                          {
                            objectType: "instances",
                            queryObj: { id: objId },
                            getChildObjs: 2,
                          },
                          function (obj) {
                            callback(obj[0]);
                          },
                          adminURL + "_getAdmin.php?do="
                        );
                      },
                      paged: {
                        sortCol: "systemName",
                        sortDir: "asc",
                      },
                      data: function (paged, callback) {
                        sb.data.db.controller(
                          "getAllInstances&api_webform=true&pagodaAPIKey=" +
                            appConfig.instance,
                          {
                            objectType: "instances",
                            getChildObjs: 2,
                            paged: paged,
                          },
                          function (objs) {
                            callback(objs);
                          },
                          adminURL + "_getAdmin.php?do="
                        );
                      },
                    },
                  },
                  {
                    id: "sales-dashboard",
                    type: "custom",
                    title: "My sales",
                    icon: '<i class="fa fa-usd"></i>',
                    dom: salesDashboard,
                    setup: {
                      rowLink: {
                        type: "tab",
                        header: function (obj) {
                          return obj.systemName;
                        },
                        action: singleView,
                      },
                    },
                  },
                  {
                    id: "blueprints",
                    type: "settings",
                    title: "Blueprints",
                    icon: '<i class="fa fa-cogs"></i>',
                    setup: [
                      {
                        object_type: "blueprints",
                        name: "All Blueprints",
                        action: function (dom) {
                          sb.notify({
                            type: "edit-blueprint-objects-load",
                            data: {
                              domObj: dom,
                            },
                          });
                        },
                      },
                    ],
                  },
                  {
                    id: "dev",
                    type: "hqTool",
                    name: "Dev",
                    tip: "For developers.",
                    icon: {
                      type: "lightbulb",
                      color: "blue",
                    },
                    default: true,
                    settings: [
                      {
                        object_type: "",
                        name: "dataCRUD",
                        action: function (ui, bp, state) {
                          dataCRUD(ui);
                        },
                      },
                    ],
                    mainViews: [
                      {
                        dom: function (dom, state, draw) {
                          draw({
                            dom: dom,
                            after: function (dom) {
                              sb.notify({
                                type: "edit-blueprint-objects-load",
                                data: {
                                  domObj: dom,
                                },
                              });
                            },
                          });
                        },
                      },
                    ],
                  },
                ],
              },
            },
          });
        }
      }
    },

    destroy: function () {
      _.each(components, function (comp) {
        comp.destroy();
      });

      (domObj = {}), (components = {});
    },

    run: function (data) {
      data.run();
    },

    start: function (data) {
      sb.listen({
        "stop-instances-component": this.stop,
        "instances-run": this.run,
      });

      createUI(data.domObj.selector);

      mainUI.state();
      mainUI.state.show();
    },

    // component views
    addInstanceToContact: function (setup) {
      addInstanceToContact(setup.ui, setup.state, setup.draw);
    },

    singleObjectView: function (setup) {
      singleObjectView(setup.dom, setup.contactObj);
    },

    viewSingleInstance: function (setup) {
      singleView(setup.state, setup.ui, {}, setup.draw);
    },
  };
});
