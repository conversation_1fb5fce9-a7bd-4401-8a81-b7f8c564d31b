Factory.register("headquarters", function (sb) {
  var comps = {},
    // UI Objs
    ui = {
      sys_modal: {},
      sys_cont: {},
    },
    listeners = {},
    statsStartDate = moment("2017-01-01", "YYYY-MM-DD"),
    statsEndDate = moment(),
    appSettings = {};
  var defaultCollectionsView = "list";
  var refreshProjectDashboard = function () {};
  var currentProj = 0;
  var comps = {};

  // navigation
  function breadcrumbsUI(ui, state, draw, mainDom, tool) {
    if (tool) {
      state.layer = "tools";
    }

    function getLayerCss(state, layer) {
      if (state.layer == layer) {
        return "section ui tiny simple header";
      } else {
        return "section hover-underline pointer ui tiny";
      }
    }

    ui.makeNode("nav", "div", { css: "ui breadcrumb" });
    ui.makeNode("navBreak", "div", { text: "<br />" });

    // this menu should be made recursive by nested projects/organizations
    // each layer is a node in our graph data, for our reporting system

    // MY (teams and projects) / ALL (teams and projects I have access to) / MY VS. REPORT / MY TEAMS/DEPARTMENTS / BOOKMARKS / NETWORK / SETTINGS

    /*
		ui.nav.makeNode('myOrAll', 'div', {css:'section hover-underline pointer', text:'All projects'}).notify('click', {
			type:'projects-run',
			data:{
				run:singleView.bind({}, state.project, ui, state, draw)
			}
		}, sb.moduleId);
*/

    // 		ui.nav.makeNode('div-1', 'div', {tag:'i', css:'right angle icon divider'});

    // TYPES / TYPE REPORT / TYPE VS. TYPE REPORT / TYPE COMPOSITION VIEWS::CALENDAR, TEAM/PROJECT RELATION BUBBLE GRAPH / SETTINGS

    // 		var layerCss = getLayerCss(state, 'project-type');

    /*
		ui.nav.makeNode('type', 'div', {css:layerCss, text:state.project.type.name}).notify('click', {
			type:'projects-run',
			data:{
				run:function(){
					state.layer = 'project-type';
					ProjectTypeView(ui, state, state.project.type);
				}
			}
		}, sb.moduleId);
*/

    // 		ui.nav.makeNode('div-3', 'div', {tag:'i', css:'right angle icon divider'});

    // MY PROJECT / PROJECT TEAM VIEW
    switch (state.layer) {
      case "headquarters":
      case "team":
      case "project":
        layerCss = getLayerCss(state, "headquarters");

        ui.nav
          .makeNode("headquarters", "div", {
            css: layerCss,
            text: '<i class="ui building icon"></i>' + state.headquarters.name,
          })
          .notify(
            "click",
            {
              type: "headquarters-run",
              data: {
                run: function () {
                  state.layer = "headquarters";
                  headquartersView(mainDom, state, draw);
                },
              },
            },
            sb.moduleId
          );

        break;

      case "project":
      case "tools":
        layerCss = getLayerCss(state, "project");

        ui.nav
          .makeNode("headquarters", "div", {
            css: layerCss,
            text: '<i class="ui building icon"></i>' + state.headquarters.name,
          })
          .notify(
            "click",
            {
              type: "headquarters-run",
              data: {
                run: function () {
                  state.layer = "headquarters";
                  headquartersView(mainDom, state, draw);
                },
              },
            },
            sb.moduleId
          );

        ui.nav.makeNode("div-3", "div", {
          tag: "i",
          css: "right angle icon divider",
        });

        ui.nav
          .makeNode("project", "div", {
            css: layerCss,
            text:
              '<i class="ui project diagram icon"></i>' + state.project.name,
          })
          .notify(
            "click",
            {
              type: "headquarters-run",
              data: {
                run: function () {
                  state.layer = "project";
                  projectView.call({}, state.project, mainDom, state, draw);
                },
              },
            },
            sb.moduleId
          );

        break;
    }

    switch (state.layer) {
      case "team":
        layerCss = getLayerCss(state, "team");

        ui.nav.makeNode("div-6", "div", {
          tag: "i",
          css: "right angle icon divider",
        });
        ui.nav.makeNode("team", "div", {
          css: layerCss + " dropdown item ",
          text: '<i class="ui users icon"></i>' + state.team.name,
        });

        break;

      case "project":
        layerCss = getLayerCss(state, "project");

        ui.nav.makeNode("div-7", "div", {
          tag: "i",
          css: "right angle icon divider",
        });
        ui.nav.makeNode("project", "div", {
          css: layerCss + " dropdown item ",
          text: '<i class="ui project diagram icon"></i>' + state.project.name,
        });

        break;

      case "tools":
        layerCss = getLayerCss(state, "tools");

        ui.nav.makeNode("div-5", "div", {
          tag: "i",
          css: "right angle icon divider",
        });
        ui.nav
          .makeNode("tool", "div", {
            css: layerCss + " dropdown item ",
            text: tool.display_name,
          })
          .makeNode("menu", "div", { css: "menu" });

        _.chain(state.project.tools)
          .sortBy(function (tool) {
            return tool.display_name;
          })
          .each(function (tool, i) {
            ui.nav.tool.menu
              .makeNode("tool-" + i, "div", {
                text: tool.display_name,
                css: "item",
              })
              .notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function () {
                      state.layer = "tools";
                      loadProjectTool(mainDom, state, draw, tool);
                    },
                  },
                },
                sb.moduleId
              );
          });

        break;
    }
  }

  // headquarters functions
  function headquartersView(dom, state, draw, callback) {
    var obj = state.headquarters;
    var mainDom = {};

    function editHQTool(dom, state, draw, tool, callback) {
      dom.modals.makeNode("edit", "modal", {
        onShow: function () {
          sb.data.db.obj.getWhere("users", { enabled: 1 }, function (users) {
            var modal = dom.modals.edit.body;
            var formObj = {
              display_name: {
                name: "display_name",
                label: "Section Name",
                type: "text",
                value: tool.display_name,
                fieldCSS: "field ui massive fluid input",
              },
              tip: {
                name: "tip",
                label: "Section tip",
                type: "textbox",
                rows: 5,
                value: tool.tip,
                fieldCSS: "field ui big fluid input",
              },
            };

            modal.makeNode("title", "div", {
              text: "Edit the " + tool.display_name + " section",
              css: "ui huge header",
            });

            modal.makeNode("form", "form", formObj);

            modal.makeNode("titleBreak", "div", { text: "<br />" });

            modal
              .makeNode("save", "div", {
                text: "Save changes",
                css: "ui green button",
              })
              .notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function (dom, state, draw, tool) {
                      var formData = dom.modals.edit.body.form.process();

                      if (!formData.fields.display_name) {
                        sb.dom.alerts.alert(
                          "",
                          "The section needs a name.",
                          "error"
                        );
                        return;
                      }

                      if (formData.fields.display_name.value == "") {
                        sb.dom.alerts.alert(
                          "",
                          "The section needs a name.",
                          "error"
                        );
                        return;
                      }

                      dom.modals.edit.body.save.loading();

                      _.findWhere(state.headquarters.tools, {
                        system_name: tool.system_name,
                      }).display_name = formData.fields.display_name.value;
                      _.findWhere(state.headquarters.tools, {
                        system_name: tool.system_name,
                      }).tip = formData.fields.tip.value;

                      sb.data.db.obj.update(
                        "groups",
                        {
                          id: state.headquarters.id,
                          tools: state.headquarters.tools,
                        },
                        function (updated) {
                          state.headquarters = updated;

                          dom.modals.edit.hide();

                          callback(state.headquarters);
                        },
                        3
                      );
                    }.bind({}, dom, state, draw, tool),
                  },
                },
                sb.moduleId
              );

            modal.patch();
          });
        },
      });

      dom.modals.patch();
      dom.modals.edit.show();
    }

    /*
	// !TODO var teamToolList set to only display team tool. Uncomment to use all tools on group obj
*/

    mainDom = dom;

    dom.empty();

    sb.data.db.obj.getById("groups", state.headquarters.id, function (obj) {
      sb.data.db.obj.getById("users", +sb.data.cookie.userId, function (user) {
        sb.data.db.obj.getWhere(
          "company_logo",
          { is_primary: "yes", childObjs: 1 },
          function (logo) {
            var hqTools = _.reject(appConfig.hqTools, function (o) {
              return o.display === false;
            });
            var isManager = false;
            var managerIds = obj.managers;

            if (managerIds.indexOf(+sb.data.cookie.get("uid")) > -1) {
              isManager = true;
            } else {
              if (user.type) {
                if (
                  user.type === "admin" ||
                  user.type === "Admin" ||
                  user.type === "developer"
                ) {
                  isManager = true;
                }
              }
            }

            state.layer = "headquarters";
            state.logo = logo;

            dom.makeNode("content", "div", {
              css: "middle aligned content",
            });

            dom.content.makeNode("extraCont", "div", {
              css: "ui basic segment",
              style: "padding:0px; margin-bottom:0px !important;",
            });

            dom.content.makeNode("tools", "div", {});

            dom.content.extraCont.makeNode("help", "div", {
              tooltip: {
                title: "Headquarters",
                text: "This is your organization as a whole. You can see every Contact, Team, Project and anything else your team has created. Click to learn more.",
                position: "left center",
              },
              listener: {
                type: "popup",
                hoverable: true,
              },
              text: '<a href="https://voltzsoftware.atlassian.net/servicedesk/customer/kb/view/273186817" target="_blank"><i class="question blue icon"></i></a>',
              style: "float:right;",
              css: "ui right floated simple orange icon",
            });

            if (isManager === true) {
              // edit dropdown button
              dom.content.extraCont.makeNode("edit", "div", {
                text: '<i class="edit orange icon"></i>',
                style: "float:right; margin-right:15px;",
                css: "ui right floated simple dropdown icon",
                walkthrough: {
                  name: "welcome",
                  step: "03-hq-options",
                },
              });

              dom.content.extraCont.edit.makeNode("menu", "div", {
                css: "left menu",
              });
              dom.content.extraCont.edit.menu.makeNode("name", "div", {
                css: "item",
                text: '<i class="info circle teal icon"></i> Change HQ name',
              });
              dom.content.extraCont.edit.menu.makeNode("edit", "div", {
                css: "item",
                text: '<i class="unlock alternate blue icon"></i> HQ Access',
              });
              //dom.content.extraCont.edit.menu.makeNode('tools', 'div', {css:'item', text:'<i class="wrench grey icon"></i> Manage tools'});

              dom.content.extraCont.edit.menu.name.notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function (obj, dom, state, draw) {
                      editHeadquartersName(
                        obj,
                        dom,
                        state,
                        draw,
                        function (done) {
                          headquartersView(dom, state, draw);
                        }
                      );
                    }.bind({}, obj, dom, state, draw),
                  },
                },
                sb.moduleId
              );

              dom.content.extraCont.edit.menu.edit.notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function (obj, dom, state, draw) {
                      editHeadquartersDetails(
                        obj,
                        dom,
                        state,
                        draw,
                        function (done) {
                          headquartersView(dom, state, draw);
                        }
                      );
                    }.bind({}, obj, dom, state, draw),
                  },
                },
                sb.moduleId
              );
            }

            dom.content.extraCont.makeNode("content", "div", {
              css: "content",
            });
            dom.content.extraCont.content.makeNode("title", "div", {
              text: obj.name,
              css: "ui huge header",
              style: "margin-bottom:0px;",
            });
            dom.content.extraCont.content.makeNode("details", "div", {
              text: obj.details,
              css: "ui header",
              style: "font-weight:normal; margin-top:0px;",
            });

            // TOOLS/SECTIONS

            ///filtering objects tools and filtered against current hqtools
            obj.tools = _.filter(obj.tools, function (t) {
              if (_.findWhere(hqTools, { id: t.system_name })) return t;
            });

            build_ToolsDashboard(dom.content.tools, {
              toolList: obj.tools,
              isManager: isManager,
              systemTools: hqTools,
              state: state,
              draw: draw,
              mainDom: dom,
              layer: "headquarters",
            });

            dom.makeNode("modals", "div", {});

            draw({
              dom: dom,
              after: function (dom) {
                //ui_memberList(dom.content.extraCont.content.extra, obj, null, true);

                $(".ui.search").search({
                  minCharacters: 0,
                  source: _.map(
                    hqTools,
                    function (tool) {
                      return {
                        title: tool.name,
                        description: tool.tip,
                        url: sb.data.url.createPageURL("hqTool", {
                          tool: tool.id,
                        }),
                      };
                    },
                    []
                  ),
                });

                if (isManager === true) {
                  $(".editTool").on("click", function (e) {
                    //e.stopPropagation();

                    e.preventDefault();

                    editHQTool(
                      dom,
                      state,
                      draw,
                      _.where(obj.tools, { system_name: this.id })[0],
                      function (done) {
                        headquartersView(dom, state, draw, function (done) {});
                      }
                    );
                  });
                }

                $(".fullScreenTool").on("click", function (e) {
                  e.stopPropagation();
                });

                var teamTool = _.find(hqTools, { id: "teamTool" });
                var projectTool = _.find(hqTools, { id: "projectTool" });

                var noteIds = [];

                /*
							sb.notify({
								type: 'show-note-activity-feed',
								data: {
									collapse:false,
									domObj:dom.sections.activitySection.content.noteContainer.col,
									objectIds:state.headquarters.tagged_with,
									objectId:state.headquarters.id,
									state:state
								}
							});
*/

                if (callback) {
                  callback(true);
                }

                // !WALK start-walkthrough

                if ($(window).width() > 768) {
                  sb.notify({
                    type: "start-walkthrough",
                    data: {
                      user: user,
                      walkthrough: "welcome",
                      steps: [
                        {
                          name: "01-intro",
                          popover: {
                            title: "Hi " + user.fname + "!",
                            description:
                              "Welcome to Bento. Before you get started, we'd like to introduce you to some " +
                              "features that will make running and growing <strong>" +
                              appConfig.headquarters.name +
                              " </strong> much easier. </br>" +
                              "Click 'Next' and we'll get started. </br> " +
                              '<span style="font-size:12px;color:grey;">(You can also use the keyboard arrow buttons)</span>',
                            position: "bottom-right",
                          },
                        },
                        {
                          name: "02-headquarters",
                          popover: {
                            title:
                              "This is the 'Headquarters' for " +
                              appConfig.headquarters.name,
                            description:
                              "Headquarters represents your business or organization as a whole. " +
                              "You'll have a high level overview of <strong>who</strong> is working on <strong>what</strong> " +
                              "and the current state of everything going on inside of " +
                              appConfig.headquarters.name +
                              ".",
                            position: "right",
                          },
                        },
                        {
                          name: "03-hq-options",
                          popover: {
                            title: "Headquarter Options.",
                            description:
                              "At any time you can change the name of your Headquarters and also " +
                              "select which teammates have direct access to <strong>" +
                              appConfig.headquarters.name +
                              "</strong>. You have " +
                              "complete control & flexibility to structure your organization exactly the way you want.",
                            position: "left",
                          },
                        },
                        {
                          name: "04-hq-tools",
                          popover: {
                            title: "Headquarters' Tools",
                            description:
                              "This is the tool section of your <strong>" +
                              appConfig.headquarters.name +
                              "</strong> Headquarters. All of the tools that " +
                              "you need to run your organization and " +
                              "streamline the way you and your team work, are all right here.",
                            position: "left",
                          },
                        },
                        {
                          name: "05-flex-tools",
                          popover: {
                            title: "Flexibility right out of the box.",
                            description:
                              "You can pick and choose which of these tools to make available to you and your team. Clicking on this " +
                              "will open a new window and you can select which tools you want based on your organization's needs.",
                            position: "top",
                          },
                        },
                        {
                          name: "06-hq-projects",
                          popover: {
                            title: "Stay organized with Projects",
                            description:
                              "Keep your team and your work focused with Projects.  " +
                              "Projects are flexible enough to fit into the way you already operate. " +
                              "And with the addition of our integrated <strong>Workflows</strong>, " +
                              "we've simplified the process of keeping track of the progress for each project inside of " +
                              "<strong>" +
                              appConfig.headquarters.name +
                              "</strong>.",
                            position: "top",
                          },
                        },
                        {
                          name: "07-hq-teams",
                          popover: {
                            title: "Organize your people with Teams",
                            description:
                              "No matter the size of your organization, our Team Tool will enable " +
                              "you to easily group people according to the work you need to get done. " +
                              "And it's never been easier to stay on task because we've allowed <strong>Teams</strong> to create and manage " +
                              "their <strong>own</strong> Teams & Projects.",
                            position: "top",
                          },
                        },
                        {
                          name: "08-outro",
                          popover: {
                            title: "Now, it's your turn " + user.fname + ".",
                            description:
                              "We've added a sample project for you to help get you started. " +
                              "Click around. Explore your new " +
                              appConfig.headquarters.name +
                              " Headquarters. " +
                              "Tell us what you think. If you have specific questions " +
                              "about the app we can also assist you through our <strong>? Help Desk</strong>. </br> Thanks! ",
                            position: "top",
                          },
                        },
                      ],
                    },
                  });
                }
              },
            });
          }
        );
      });
    });
  }

  function editHeadquartersDetails(obj, dom, state, draw, callback) {
    function saveAccess(dom, state, draw, obj, callback) {
      sb.data.db.obj.update(
        "groups",
        obj,
        function (updatedProject) {
          callback(updatedProject);
        },
        2
      );
    }

    dom.makeNode("modal", "modal", {
      onShow: function () {
        sb.data.db.obj.getAll(
          "users",
          function (users) {
            var modal = dom.modal.body;
            var formObj = {
              managers: {
                name: "managers",
                label: "Who are the Point People of this headquarters?",
                type: "checkbox",
                options: [],
              },
              salesmanagers: {
                name: "salesmanagers",
                label: "Who are the Point People of this headquarters?",
                type: "checkbox",
                options: [],
              },
            };

            users = _.sortBy(users, function (user) {
              return user.fname;
            });

            _.each(users, function (user) {
              formObj.managers.options.push({
                name: user.fname + " " + user.lname,
                label: user.fname + " " + user.lname,
                value: user.id,
              });

              formObj.salesmanagers.options.push({
                name: user.fname + " " + user.lname,
                label: user.fname + " " + user.lname,
                value: user.id,
              });
            });

            if (obj.managers.length > 0) {
              formObj.managers.value = obj.managers;
            } else {
              formObj.managers.value = +sb.data.cookie.userId;
            }

            modal.empty();

            modal.makeNode("title", "div", {
              text: "Edit the Point People",
              css: "ui huge header",
            });

            modal.makeNode("divider", "div", { css: "ui divider" });

            modal.makeNode("form", "form", formObj);

            modal.makeNode("formBreak", "div", { text: "<br />" });

            modal.makeNode("btns", "div", { css: "ui buttons" });

            modal.btns
              .makeNode("save", "button", {
                css: "pda-btn-green",
                text: "Save changes",
              })
              .notify(
                "click",
                {
                  type: "projects-run",
                  data: {
                    run: function (obj, dom, state, draw) {
                      var formInfo = modal.form.process();

                      if (formInfo.fields.managers.value === null) {
                        sb.dom.alerts.alert(
                          "",
                          "Please choose at least one manager.",
                          "error"
                        );
                        return;
                      }

                      dom.modal.body.btns.save.loading();

                      var projectObj = {
                        id: obj.id,
                      };

                      if (formInfo.fields.managers) {
                        if (formInfo.fields.managers.value.length == 0) {
                          projectObj.managers = [];
                        } else {
                          projectObj.managers = formInfo.fields.managers.value;
                        }
                      } else {
                        projectObj.managers = [];
                      }

                      if (formInfo.fields.allowed_users) {
                        if (formInfo.fields.allowed_users.value.length == 0) {
                          projectObj.allowed_users = [];
                        } else {
                          projectObj.allowed_users =
                            formInfo.fields.allowed_users.value;
                        }
                      } else {
                        projectObj.allowed_users = [];
                      }

                      saveAccess(
                        dom,
                        state,
                        draw,
                        projectObj,
                        function (updatedObj) {
                          dom.modal.hide();

                          callback(updatedObj);
                        }
                      );
                    }.bind({}, obj, dom, state, draw),
                  },
                },
                sb.moduleId
              );

            modal.btns
              .makeNode("close", "button", {
                css: "pda-btn-yellow",
                text: "Cancel",
              })
              .notify(
                "click",
                {
                  type: "projects-run",
                  data: {
                    run: function (dom) {
                      dom.modal.hide();
                    }.bind({}, dom),
                  },
                },
                sb.moduleId
              );

            modal.patch();
          },
          {
            fname: true,
            lname: true,
          }
        );
      },
    });

    dom.patch();
    dom.modal.show();
  }

  function editHeadquartersName(obj, dom, state, draw, callback) {
    function saveHQName(dom, state, draw, obj, callback) {
      sb.data.db.obj.update("groups", obj, function (updatedProject) {
        callback(updatedProject);
      });
    }

    dom.makeNode("modal", "modal", {
      onShow: function () {
        var modal = dom.modal.body;
        var formObj = {
          name: {
            name: "name",
            label: "Organization Name",
            type: "text",
            value: obj.name,
            fieldCSS: "ui massive fluid input",
          },
          details: {
            name: "details",
            label: "Details",
            type: "textbox",
            rows: 5,
            value: obj.details,
            fieldCSS: "ui big fluid input",
            placeholder: "HQ details",
          },
        };

        modal.empty();

        modal.makeNode("title", "div", {
          text: "Change the Headquarters name and details",
          css: "ui huge header",
        });

        modal.makeNode("form", "form", formObj);

        modal.makeNode("formBreak", "div", { text: "<br />" });

        modal.makeNode("btns", "div", { css: "ui buttons" });

        modal.btns
          .makeNode("save", "button", {
            css: "pda-btn-green",
            text: "Save changes",
          })
          .notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function (obj, dom, state, draw) {
                  var formInfo = modal.form.process();

                  if (formInfo.fields.name.value == "") {
                    sb.dom.alerts.alert(
                      "",
                      "The Headquarters needs a name.",
                      "error"
                    );
                    return;
                  }

                  dom.modal.body.btns.save.loading();

                  var projectObj = {
                    name: formInfo.fields.name.value,
                    details: formInfo.fields.details.value,
                    id: obj.id,
                  };

                  saveHQName(
                    dom,
                    state,
                    draw,
                    projectObj,
                    function (updatedObj) {
                      dom.modal.hide();

                      callback(updatedObj);
                    }
                  );
                }.bind({}, obj, dom, state, draw),
              },
            },
            sb.moduleId
          );

        modal.btns
          .makeNode("close", "button", {
            css: "pda-btn-yellow",
            text: "Cancel",
          })
          .notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function (dom) {
                  dom.modal.hide();
                }.bind({}, dom),
              },
            },
            sb.moduleId
          );

        modal.patch();
      },
    });

    dom.patch();
    dom.modal.show();
  }

  function manageHQTools(dom, state, draw) {
    dom.makeNode("modal", "modal", {
      onShow: function () {
        dom.modal.body.empty();

        dom.modal.body.makeNode("btns", "div", {
          css: "ui right floated buttons",
        });
        dom.modal.body.btns
          .makeNode("close", "div", { css: "ui blue button", text: "Done" })
          .notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function (dom) {
                  dom.modal.hide();
                }.bind({}, dom),
              },
            },
            sb.moduleId
          );

        dom.modal.body.makeNode("title", "div", {
          css: "ui left floated huge header",
          text: "HQ Tools",
        });

        dom.modal.body.makeNode("titleBreak", "div", { text: "<br /><br />" });

        dom.modal.body.makeNode("divider", "div", { css: "ui divider" });

        dom.modal.body.makeNode("tools", "div", {
          css: "ui big divided items",
        });

        var tools = _.reject(appConfig.hqTools, function (tool) {
          if (tool.mainViews === false) {
            return true;
          }
          if (tool.id === "teamTool") {
            return true;
          }
          if (tool.id === "projectTool") {
            return true;
          }
          if (tool.display === false) {
            return true;
          }
        });

        _.each(tools, function (tool) {
          dom.modal.body.tools
            .makeNode("tool-" + tool.id, "div", { css: "item" })
            .makeNode("content", "div", { css: "content" });

          var addButtonColor = "green";
          var addButtonText = "Add tool";
          var HQTool = _.findWhere(state.headquarters.tools, {
            system_name: tool.id,
            is_archieved: 0,
          });

          if (HQTool) {
            addButtonColor = "red";
            addButtonText = "Remove tool";
          }

          dom.modal.body.tools["tool-" + tool.id].content.makeNode(
            "select",
            "button",
            {
              tag: "button",
              css: "ui right floated " + addButtonColor + " mini button",
              text: addButtonText,
            }
          );

          var requiresToolsString = "";
          var requiresStringInner = "";

          _.each(appConfig.hqTools, function (pTool) {
            var count = 0;

            if (pTool.required) {
              if (pTool.required.indexOf(tool.id) > -1 && tool.id != pTool.id) {
                if (count > 0) {
                  requiresStringInner += ", ";
                }

                requiresStringInner += pTool.name;

                count++;
              }

              if (requiresStringInner != "") {
                requiresToolsString =
                  ' <div class="ui left pointing mini blue label">Required by ' +
                  requiresStringInner +
                  "</div>";
              }
            }
          });

          var requiredToolsString = "";

          if (tool.required) {
            requiredToolsString =
              ' <div class="ui left pointing mini blue label">Requires ';
            _.each(tool.required, function (reqToolId, count) {
              if (count > 0) {
                requiredToolsString += ", ";
              }

              requiredToolsString += _.findWhere(appConfig.hqTools, {
                id: reqToolId,
              }).name;
            });

            requiredToolsString += "</div>";
          }

          dom.modal.body.tools["tool-" + tool.id].content.makeNode(
            "header",
            "div",
            {
              css: "header",
              text:
                '<i class="circular ' +
                tool.icon.color +
                " " +
                tool.icon.type +
                ' icon"></i> ' +
                tool.name +
                requiresToolsString +
                requiredToolsString,
            }
          );
          dom.modal.body.tools["tool-" + tool.id].content.makeNode(
            "meta",
            "div",
            { css: "description", text: tool.tip }
          );

          dom.modal.body.tools["tool-" + tool.id].content.select.notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function (dom, state, draw, tool) {
                  dom.modal.body.tools[
                    "tool-" + tool.id
                  ].content.select.loading();

                  sb.data.db.obj.getById(
                    "groups",
                    state.headquarters.id,
                    function (HQ) {
                      state.headquarters.tools = HQ.tools;

                      var HQTool = _.findWhere(state.headquarters.tools, {
                        system_name: tool.id,
                      });

                      if (HQTool) {
                        if (HQTool.is_archieved == 1) {
                          // turn the tool on
                          _.findWhere(state.headquarters.tools, {
                            system_name: tool.id,
                          }).is_archieved = 0;

                          if (tool.required) {
                            var pass = true;

                            _.each(tool.required, function (toolName) {
                              var requiredTool = _.findWhere(
                                appConfig.hqTools,
                                { id: toolName }
                              );

                              if (requiredTool) {
                                var toolOnProject = _.findWhere(
                                  state.headquarters.tools,
                                  { system_name: toolName }
                                );

                                if (toolOnProject) {
                                  _.findWhere(state.headquarters.tools, {
                                    system_name: toolName,
                                  }).is_archieved = 0;
                                } else {
                                  state.headquarters.tools.push({
                                    allowed_users: [],
                                    system_name: requiredTool.id,
                                    display_name: requiredTool.name,
                                    is_archieved: 0,
                                    order: state.project.tools.length,
                                    added_by: sb.data.cookie.userId,
                                    added_on: moment(),
                                    settings: {},
                                    box_color: "",
                                  });
                                }
                              } else {
                                pass = false;
                              }
                            });

                            if (pass === false) {
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.loading(false);
                              sb.dom.alerts.alert(
                                "Error",
                                "A required tool is not available",
                                "error"
                              );
                              return;
                            }
                          }

                          sb.data.db.obj.update(
                            "groups",
                            state.headquarters,
                            function (updated) {
                              _.each(updated.tools, function (projectTool) {
                                if (projectTool.is_archieved == 0) {
                                  dom.modal.body.tools[
                                    "tool-" + tool.id
                                  ].content.select.loading(false);
                                  dom.modal.body.tools[
                                    "tool-" + tool.id
                                  ].content.select.text("Remove tool");
                                }
                              });

                              state.headquarters = updated;

                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.loading(false);
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.text("Remove tool");
                            },
                            1
                          );
                        } else {
                          var requiredByAnotherTool = false;

                          _.each(appConfig.hqTools, function (systemTool) {
                            if (systemTool.required) {
                              if (systemTool.required.indexOf(tool.id) > -1) {
                                requiredByAnotherTool = _.findWhere(
                                  state.headquarters.tools,
                                  { system_name: systemTool.id }
                                );
                              }
                            }
                          });

                          if (requiredByAnotherTool) {
                            if (requiredByAnotherTool.is_archieved == 0) {
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.loading(false);
                              sb.dom.alerts.alert(
                                "",
                                "This tool is required by " +
                                  requiredByAnotherTool.display_name +
                                  ". Please remove that tool first.",
                                "error"
                              );
                              return;
                            }
                          }

                          // turn the tool off
                          _.findWhere(state.headquarters.tools, {
                            system_name: tool.id,
                          }).is_archieved = 1;

                          sb.data.db.obj.update(
                            "groups",
                            {
                              id: state.headquarters.id,
                              tools: state.headquarters.tools,
                            },
                            function (updated) {
                              state.headquarters = updated;

                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.loading(false);
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.css("");
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.css(
                                "ui right floated green mini button"
                              );
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.text("Add tool");
                            },
                            1
                          );
                        }
                      } else {
                        // add the tool
                        var toolObj = {
                          allowed_users: [],
                          system_name: tool.id,
                          display_name: tool.name,
                          is_archieved: 0,
                          order: state.headquarters.tools.length,
                          added_by: sb.data.cookie.userId,
                          added_on: moment(),
                          settings: {},
                          box_color: "",
                        };

                        state.headquarters.tools.push(toolObj);

                        if (tool.required) {
                          var requiredTools = [];

                          _.each(tool.required, function (toolName) {
                            var requiredTool = _.findWhere(appConfig.hqTools, {
                              id: toolName,
                            });

                            requiredTools.push({
                              allowed_users: [],
                              system_name: requiredTool.id,
                              display_name: requiredTool.name,
                              is_archieved: 0,
                              order: state.headquarters.tools.length,
                              added_by: sb.data.cookie.userId,
                              added_on: moment(),
                              settings: {},
                              box_color: "",
                            });
                          });
                        }

                        sb.data.db.obj.update(
                          "groups",
                          state.headquarters,
                          function (updated) {
                            state.headquarters = updated;

                            dom.modal.body.tools[
                              "tool-" + tool.id
                            ].content.select.loading(false);
                            dom.modal.body.tools[
                              "tool-" + tool.id
                            ].content.select.css("");

                            _.each(updated.tools, function (projectTool) {
                              if (projectTool.is_archieved == 0) {
                                dom.modal.body.tools[
                                  "tool-" + tool.id
                                ].content.select.loading(false);
                                dom.modal.body.tools[
                                  "tool-" + tool.id
                                ].content.select.text("Remove tool");
                              }
                            });
                          },
                          2
                        );
                      }
                    },
                    {
                      tools: {},
                    }
                  );
                }.bind({}, dom, state, draw, tool),
              },
            },
            sb.moduleId
          );
        });

        dom.modal.body.patch();
      },
      onClose: function () {
        sb.data.db.obj.getById(
          "groups",
          state.headquarters.id,
          function (currentProject) {
            state.headquarters = currentProject;

            headquartersView(dom, state, draw, function (done) {});
          },
          1
        );
      },
    });

    draw(dom);

    dom.modal.show();
  }

  function setupProjectTypes(callback) {
    sb.data.db.obj.getAll("project_types", function (projectTypes) {
      if (projectTypes.length > 0) {
        callback(projectTypes[0]);
      }
    });
  }

  function startHQ(hq, dom, state, draw) {
    state.headquarters = hq;

    dom.makeNode("nav", "div", {});
    dom.makeNode("cont", "div", { css: "" });
    //dom.makeNode('finalBreak', 'div', {text:'<br />'});

    draw({
      dom: dom,
      after: function (dom) {
        ui_topNav(dom.nav, dom.cont, state, draw, "headquarters");
      },
    });
  }

  // admin functions
  function adminView(dom, state, draw, callback) {
    var hqTools = _.reject(appConfig.hqTools, function (obj) {
      if (obj.settings === false) {
        return true;
      }

      if (!obj.settings) {
        return true;
      } else {
        if (obj.settings.length == 0) {
          return true;
        }
      }
    });

    function displaySetting(dom, obj, state, menuItems) {
      // adjust the top menu
      state["item-" + obj.object_type].css("active item");

      _.each(
        _.reject(menuItems, function (item) {
          return item.object_type == obj.object_type;
        }),
        function (item) {
          state["item-" + item.object_type].css("item");
        }
      );

      // load the setting menu

      sb.data.db.obj.getBlueprint(obj.object_type, function (bp) {
        if (obj.action) {
          dom.empty();

          if (obj.tip) {
            dom.makeNode("message", "div", {
              text: obj.tip,
              css: "ui big blue message",
            });
          }
          dom
            .makeNode("table", "container", { uiGrid: false })
            .makeNode("loading", "loader", {});
          dom.patch();

          obj.action(dom.table, bp, {
            appSettings: appSettings,
            object_type: obj.object_type,
          });
        } else {
          var visibleCols = {};

          _.each(bp, function (fieldObj, fieldName) {
            if (fieldObj.immutable == false && fieldName !== "is_template") {
              visibleCols[fieldName] = fieldObj.name;
            }
          });

          var rowLink = {
            type: "edit",
            header: function (obj) {},
            action: "edit",
          };

          if (obj.formObjs) {
            rowLink.formObjs = obj.formObjs;
          }

          dom.empty();

          /*
					dom.makeNode('titleBreak', 'div', {text:''});
					dom.makeNode('title', 'div', {css:'ui header', text:obj.name});
*/
          dom
            .makeNode("table", "container", { uiGrid: false })
            .makeNode("loading", "loader", {});
          dom.patch();

          comps.table.notify({
            type: "show-table",
            data: {
              domObj: dom.table,
              tableTitle: obj.name,
              settingsTable: true,
              navigation: false,
              objectType: obj.object_type,
              searchObjects: false,
              filters: false,
              download: false,
              headerButtons: {
                reload: {
                  name: "Reload",
                  css: "pda-btn-blue",
                  action: function () {},
                },
                create: {
                  name: '<i class="fa fa-plus"></i> Create New',
                  css: "pda-btn-green",
                  domType: "full",
                  action: "create",
                },
              },
              calendar: false,
              rowSelection: true,
              rowLink: rowLink,
              multiSelectButtons: {
                erase: {
                  name: '<i class="fa fa-trash-o"></i> Delete',
                  css: "pda-btn-red",
                  domType: "default",
                  action: "erase",
                },
              },
              visibleCols: visibleCols,
              searchObjects: false,
              dateRange: false,
              home: false,
              settings: false,
              cells: {},
              childObjs: 1,
              data: function (paged, callback) {
                sb.data.db.obj.getAll(
                  obj.object_type,
                  function (ret) {
                    callback(ret);
                  },
                  1,
                  paged
                );
              },
            },
          });
        }
      });
    }

    function displaySettingMenu(menuDom, viewerDom, tool) {
      // adjust the side menu
      menuDom["item-" + tool.id].css("active item");

      _.each(
        _.reject(hqTools, function (obj) {
          return obj.id == tool.id;
        }),
        function (obj) {
          if (obj.settings !== false) {
            menuDom["item-" + obj.id].css("item");
          }
        }
      );

      // load the setting menu
      viewerDom.empty();

      viewerDom.makeNode("menu", "div", {
        css: "ui stackable secondary blue menu",
        style: "flex-wrap: wrap;",
      });
      viewerDom.makeNode("cont", "div", { css: "ui basic segment" });

      _.each(tool.settings, function (setting) {
        viewerDom.menu.makeNode("item-" + setting.object_type, "div", {
          text: setting.name,
          tag: "a",
          css: "item",
        });

        viewerDom.menu["item-" + setting.object_type].notify("click", {
          type: "headquarters-run",
          data: {
            run: function (viewerDom, tool, menuDom, settings) {
              displaySetting(viewerDom, setting, menuDom, settings);
            }.bind({}, viewerDom.cont, tool, viewerDom.menu, tool.settings),
          },
        });
      });

      viewerDom.patch();

      displaySetting(
        viewerDom.cont,
        tool.settings[0],
        viewerDom.menu,
        tool.settings
      );
    }

    dom.empty();

    dom.makeNode("cont", "div", { css: "ui stackable grid" });
    dom.cont.makeNode("col1", "div", { css: "two wide column" });
    dom.cont.makeNode("col2", "div", { css: "fourteen wide column" });

    dom.cont.col1.makeNode("menu", "div", {
      css: "ui labeled icon blue tiny vertical fluid menu",
    });

    var firstTool;

    _.each(hqTools, function (tool) {
      if (!firstTool) {
        firstTool = tool;
      }

      dom.cont.col1.menu
        .makeNode("item-" + tool.id, "div", {
          css: "item",
          tag: "a",
          text: tool.name,
        })
        .makeNode("icon", "div", {
          tag: "i",
          css: tool.icon.color + " " + tool.icon.type + " icon",
        });
      dom.cont.col1.menu["item-" + tool.id].notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (menuDom, viewerDom, tool) {
              displaySettingMenu(menuDom, viewerDom, tool);
            }.bind({}, dom.cont.col1.menu, dom.cont.col2, tool),
          },
        },
        sb.moduleId
      );
    });

    //dom.makeNode('finalBreak', 'div', {text:'<br /><br />'});

    dom.patch();

    displaySettingMenu(dom.cont.col1.menu, dom.cont.col2, firstTool);

    callback(true);
  }

  // profile functions
  function myTeams_collections(dom, state, options) {
    var where = {
      group_type: "Team",
      childObjs: {
        name: true,
        details: true,
        group_type: true,
        parent: true,
      },
    };

    // For teams collections for other users
    if (state && state.pageObject && state.pageObject.id) {
      where.tagged_with = [state.pageObject.id];
    }

    // For my stuff only
    if (!_.isEmpty(options) && options.isMyStuff) {
      where.tagged_with = [+sb.data.cookie.get("uid")];
    }

    defaultCollectionsView = "cards";

    if (options != undefined && options.userId) {
      where.tagged_with = [options.userId];
    }

    sb.notify({
      type: "show-collection",
      data: {
        actions: {
          create: function (ui, newObj, onComplete) {
            createNewTeam(ui, state, newObj, function (newTeam) {
              if (newTeam) {
                sb.notify({
                  type: "app-navigate-to",
                  data: {
                    itemId: "groups",
                    viewId: {
                      viewState: {
                        obj: newTeam,
                      },
                    },
                  },
                });
              }
            });
          },
          view: false,
        },
        domObj: dom,
        fields: {
          name: {
            title: "Name",
            type: "title",
          },
          parent: {
            title: "Parent",
            type: "parent",
          },
          details: {
            title: "Description",
            type: "detail",
          },
        },
        fullView: {
          id: "teamTool",
          type: "hqTool",
        },
        groupings: false,
        objectType: "groups",
        singleView: {
          view: function (ui, obj, draw) {
            state.team = obj;
            teamView(ui, state, draw);
          },
          select: 2,
        },
        selectedView: defaultCollectionsView,
        state: state,
        menu: {
          subviews: {
            cards: true,
            list: true,
            table: true,
            calendar: false,
          },
        },
        subviews: {
          cards: true,
          list: false,
          table: false,
          calendar: false,
          /*
					list: {
nestChildren: true
						, options: {
							where: {
								tagged_with:[+sb.data.cookie.get('uid')],
								group_type:'Team',
								parent: {
									type: 	'not_set',
									or: 	state.headquarters.id
								},
								childObjs:{
									name:true,
									details:true,
									group_type:true,
									parent:true
								}
							}
						}
					}
*/
        },
        sortCol: "name",
        sortDir: "asc",
        where: where,
      },
    });
  }

  function myProjects_collections(dom, state, draw, mainDom, options) {
    var defaultView = "table";

    if (sb.dom.isMobile) {
      defaultView = "list";
    }

    var where = {
      tagged_with: [+sb.data.cookie.get("uid")],
      group_type: "Project",
      childObjs: {
        name: true,
        state: true,
        state_updated_on: true,
        group_type: "id",
        type: {
          name: true,
          states: true,
        },
        main_contact: {
          fname: true,
          lname: true,
          company: {
            name: true,
          },
        },
        managers: {
          fname: true,
          lname: true,
          profile_image: true,
        },
        start_date: true,
        end_date: true,
        invoice_value: true,
        invoice_value_no_tax: true,
      },
    };

    if (sb.dom.isMobile) {
      defaultCollectionsView = "cards";
    }

    if (options != undefined && options.userId) {
      where.tagged_with = options.userId;
    }

    var config = {};
    var urlParams = checkURL();
    if (urlParams.type) {
      config = {
        type: urlParams.type,
      };
    }

    if (urlParams.state) {
      config.state = urlParams.state;
    }

    if (urlParams.c) {
      config.category = parseInt(urlParams.c);
      where.category = parseInt(urlParams.c);
    }

    var collectionsSetup = {
      actions: {
        create: function (ui, newObj, onComplete, bp, setup) {
          createNewProject(
            ui,
            state,
            newObj,
            function (newProject) {
              if (newProject) {
                sb.notify({
                  type: "app-navigate-to",
                  data: {
                    itemId: "groups",
                    viewId: {
                      viewState: {
                        obj: newProject,
                      },
                    },
                  },
                });
              }
            },
            setup
          );
        },
        view: false,
        copy: false,
        calEvents: {
          singleAction: false,
          title: "Copy calendar link",
          icon: "calendar",
          color: "blue",
          domType: "custom",
          ui: false,
          headerAction: function (selection, ui, shouldUpdate, options) {
            // !1891: Reference for caldav integration link
            // [] You will want to add an additional request var with the entity_type

            var dummy = document.createElement("input");
            var loc =
              sb.url +
              "/api/caldav/getEvents.php?i=" +
              appConfig.instance +
              "&id=" +
              +sb.data.cookie.get("uid");

            document.body.appendChild(dummy);
            dummy.value = loc;
            dummy.select();
            document.execCommand("copy");
            document.body.removeChild(dummy);

            sb.notify({
              type: "display-alert",
              data: {
                header: "Link copied",
                body: "Calendar subscription link has been copied to clipboard.",
                color: "green",
              },
            });
          },
        },
      },
      config: config,
      domObj: dom,
      fields: {
        name: {
          title: "Name",
          type: "title",
        },
        client: {
          title: "Client",
          type: "edge",
          view: function (ui, obj) {
            if (obj.main_contact) {
              ui.makeNode("c", "div", {
                tag: "a",
                text:
                  '<i class="user icon"></i>' +
                  obj.main_contact.fname +
                  " " +
                  obj.main_contact.lname,
                href: sb.data.url.createPageURL("object-view", {
                  id: obj.main_contact.id,
                  name: obj.main_contact.fname + " " + obj.main_contact.lname,
                  type: "contacts",
                }),
              });

              if (obj.main_contact.company) {
                ui.makeNode("co", "div", {
                  tag: "a",
                  text:
                    ' at <i class="building icon"></i>' +
                    obj.main_contact.company.name,
                  href: sb.data.url.createPageURL("object-view", {
                    id: obj.main_contact.company.id,
                    name: obj.main_contact.company.name,
                    type: "companies",
                  }),
                });
              }
            } else {
              ui.makeNode("c", "div", {
                text: '<i class="text-muted">Not set</i>',
              });
            }
          },
        },
        type: {
          title: "Type",
          type: "type",
        },
        state: {
          title: "Status",
          type: "state",
        },
        priority: {
          title: "Priority",
          type: "priority",
        },
        start_date: {
          title: "Start Date",
          type: "date",
          end: "end_date",
        },
        end_date: {
          title: "End Date",
          type: "date",
          hideInMini: true,
          //is_due_date: true
        },
        managers: {
          title: "Managers",
          type: "users",
          parseUpdates: function (updates) {
            updates.managers = [updates.managers];
            var leadsList = updates.managers;
            updates.users = leadsList;
            updates.tagged_with = _.chain(leadsList)
              .union(updates.tagged_with)
              .uniq()
              .value();

            updates.notify = updates.tagged_with;
            return updates;
          },
          options: {
            mini: true,
          },
        },
        invoice_value: {
          title: "Value",
          type: "usd",
          view: function (ui, obj) {
            ui.makeNode("value", "div", {
              text: "$" + (obj.invoice_value / 100).formatMoney(),
            });

            ui.patch();
          },
        },
      },
      fullView: {
        type: "hqTool",
        id: "projectTool",
      },
      groupings: {
        type: "Type",
        state: "Status",
        by_range: true,
      },
      objectType: "groups",
      selectedView: defaultView,
      subviews: {
        list: {
          groupBy: {
            defaultTo: "type",
          },
        },
        board: {
          groupBy: {
            defaultTo: "state",
          },
        },
      },
      state: state,
      sortCol: "name",
      sortDir: "asc",
      where: where,
    };

    // Don't show the create button for Foundation Group (all of their
    // projects should be created using automated actions)
    if (appConfig.instance === "foundationg_group") {
      collectionsSetup.actions.create = false;
    }

    sb.notify({
      type: "show-collection",
      data: collectionsSetup,
    });
  }

  function profileView(dom, state, draw, callback, viewType, isMyStuff) {
    var myContacts = "My Contacts";
    var instance = check_instance();

    function check_instance() {
      if (appConfig.instance === "thelifebook") {
        return {
          myContacts: "My Churches",
        };
      } else {
        return false;
      }
    }

    if (sb.dom.isMobile) {
      if (instance !== false) {
        myContacts = instance.myContacts;
      }
    }

    sb.data.db.obj.getById(
      "users",
      +sb.data.cookie.get("uid"),
      function (user) {
        sb.data.db.obj.getWhere(
          "groups",
          { group_type: "Headquarters" },
          function (hqs) {
            sb.data.db.obj.getWhere(
              "company_logo",
              { is_primary: "yes", childObjs: { company_logo: true } },
              function (logo) {
                var hq = hqs[0];
                var isManager = false;
                var managerIds = _.pluck(hq.managers, "id");

                managerIds = [];

                if (managerIds.indexOf(+sb.data.cookie.get("uid")) > -1) {
                  isManager = true;
                } else {
                  if (user && user.type) {
                    if (
                      user.type === "admin" ||
                      user.type === "Admin" ||
                      user.type === "developer"
                    ) {
                      isManager = true;
                    }
                  }
                }

                dom.makeNode("modal", "modal", { css: "ui modal" });

                dom.makeNode("cont", "div", {
                  css: "ui centered grid mobile tablet only",
                });

                dom.cont
                  .makeNode("logo1", "div", {
                    css: "computer only four wide column center aligned",
                  })
                  .makeNode("seg", "div", { css: "center aligned" });
                dom.cont
                  .makeNode("logo2", "div", {
                    css: "large screen only three wide column center aligned",
                  })
                  .makeNode("seg", "div", { css: "center aligned" });
                dom.cont
                  .makeNode("logo3", "div", {
                    css: "widescreen only four wide column center aligned",
                  })
                  .makeNode("seg", "div", { css: "center aligned" });

                dom.cont.makeNode("details", "div", {
                  css: "three wide computer eight wide mobile column",
                });
                dom.cont.makeNode("profile", "div", {
                  css: "three wide computer eight wide mobile column",
                });
                dom.cont.details.makeNode("link", "div", {
                  tag: "a",
                  href: window.location.href.split("#")[0] + "#hq",
                });

                dom.cont.details.link.makeNode("seg", "div", {
                  css: "ui padded segment",
                  style: "height: 100% !important;",
                });

                dom.cont.details.link.seg.makeNode("name", "div", {
                  css: "ui header",
                  text: hq.name + " HQ",
                });
                dom.cont.details.link.seg.makeNode("details", "div", {
                  css: "",
                  text: hq.details,
                });

                // logo
                if (logo.length > 0) {
                  dom.cont.logo1.seg.makeNode("img", "div", {
                    tag: "img",
                    css: "ui fluid image",
                    src: sb.data.files.getURL(logo[0].company_logo),
                  });
                  dom.cont.logo2.seg.makeNode("img", "div", {
                    tag: "img",
                    css: "ui fluid image",
                    src: sb.data.files.getURL(logo[0].company_logo),
                  });
                  dom.cont.logo3.seg.makeNode("img", "div", {
                    tag: "img",
                    css: "ui fluid image",
                    src: sb.data.files.getURL(logo[0].company_logo),
                  });
                } else {
                  if (isManager) {
                    dom.cont.logo1.seg.makeNode("logoBreak", "div", {
                      text: "<br /><br /><br />",
                    });
                    dom.cont.logo1.seg.makeNode("logo", "div", {
                      tag: "a",
                      href: window.location.href.split("#")[0] + "#admin",
                      css: "ui mini button",
                      text: "Add your company logo",
                    });
                    dom.cont.logo2.seg.makeNode("logoBreak", "div", {
                      text: "<br /><br /><br />",
                    });
                    dom.cont.logo2.seg.makeNode("logo", "div", {
                      tag: "a",
                      href: window.location.href.split("#")[0] + "#admin",
                      css: "ui mini button",
                      text: "Add your company logo",
                    });
                    dom.cont.logo3.seg.makeNode("logoBreak", "div", {
                      text: "<br /><br /><br />",
                    });
                    dom.cont.logo3.seg.makeNode("logo", "div", {
                      tag: "a",
                      href: window.location.href.split("#")[0] + "#admin",
                      css: "ui mini button",
                      text: "Add your company logo",
                    });
                  } else {
                    delete dom.cont.logo;
                  }
                }

                dom.cont.profile.makeNode("top", "div", { css: "" });

                dom.cont.profile.top.makeNode("imageWrap", "div", {
                  css: "ui left floated avatar image",
                  style: "margin:0 0.25em!important;",
                });

                if (
                  user &&
                  user.profile_image &&
                  user.profile_image.loc !== "" &&
                  user.profile_image.loc !== "//"
                ) {
                  dom.cont.profile.top.imageWrap
                    .makeNode("image", "image", {
                      url: sb.data.files.getURL(user.profile_image),
                      style: "cursor:pointer!important;",
                    })
                    .notify("click", {
                      type: "userDashboard-run",
                      data: {
                        run: function () {
                          uploadProfileImage.call(
                            this,
                            user,
                            dom.modal,
                            state,
                            draw
                          );
                        }.bind(dom, user, dom.modal, state),
                      },
                    });
                } else {
                  dom.cont.profile.top.imageWrap
                    .makeNode("image", "image", {
                      url: "https://placeimg.com/250/250/tech",
                      style: "cursor:pointer!important;",
                    })
                    .notify("click", {
                      type: "userDashboard-run",
                      data: {
                        run: function () {
                          uploadProfileImage.call(
                            this,
                            user,
                            dom.modal,
                            state,
                            draw
                          );
                        }.bind(dom, user, dom.modal, state),
                      },
                    });
                }

                if (user) {
                  dom.cont.profile.top.makeNode("name", "div", {
                    text: "<b>" + user.fname + " " + user.lname + "</b>",
                    css: "",
                  });

                  dom.cont.profile.top.makeNode("editProfile", "div", {
                    tag: "a",
                    text: "<small><u>Edit Profile</u></small>",
                    style: "color:grey;cursor:pointer!important;",
                    href: window.location.href.split("#")[0] + "#myaccount",
                  });
                  /*
								.notify('click', {
									type:'headquarters-run',
									data:{
										run:function(){
											comps.editProfileView.notify({
												type:'edit-account',
												data:{
													domObj:dom.modal,
													userObj:user,
													modal:dom.modal
												}
											});
										}
									}
								}, sb.moduleId);
	*/

                  dom.cont.profile.top.makeNode("linkbreak1", "div", {
                    text: "<br />",
                  });

                  if (appConfig.instance !== "erationalmarketing") {
                    dom.cont.profile.top
                      .makeNode("tasks_wrap", "div", {
                        style: "margin-bottom: 5px;",
                      })
                      .makeNode("tasks", "div", {
                        tag: "a",
                        text: '<i class="tasks icon"></i> My Tasks',
                        href: window.location.href.split("#")[0] + "#mytasks",
                        css: "link",
                      });
                  }

                  dom.cont.profile.top
                    .makeNode("contacts_wrap", "div", {
                      style: "margin-bottom: 5px;",
                    })
                    .makeNode("contacts", "div", {
                      tag: "a",
                      text: '<i class="address card icon"></i> ' + myContacts,
                      href: window.location.href.split("#")[0] + "#mycrm",
                      css: "link",
                    });

                  dom.cont.profile.top
                    .makeNode("docs_wrap", "div", {})
                    .makeNode("docs", "div", {
                      tag: "a",
                      text: '<i class="copy icon"></i> My Documents',
                      href: window.location.href.split("#")[0] + "#mydocs",
                      css: "link",
                    });
                }

                if (sb.dom.isMobile) {
                  dom.makeNode("topBreak", "div", {
                    css: "ui horizontal divider",
                  });
                  dom.makeNode("teamsCont", "container", {
                    collapse: "closed",
                    title: "Teams",
                  });
                  dom.makeNode("projectsCont", "container", {
                    collapse: true,
                    title: "Projects",
                  });
                }

                draw({
                  dom: dom,
                  after: function (dom) {
                    var options = {
                      isMyStuff: isMyStuff,
                    };

                    if (sb.dom.isMobile) {
                      if (
                        state.profileSetup === "projects" ||
                        state.profileSetup === "projects_teams"
                      ) {
                        // projects
                        myProjects_collections(dom.projectsCont, state);
                      }

                      if (
                        state.profileSetup === "teams" ||
                        state.profileSetup === "projects_teams"
                      ) {
                        // teams
                        myTeams_collections(dom.teamsCont, state, options);
                      }
                    } else {
                      if (viewType) {
                        myProjects_collections(dom, state);
                      } else {
                        myTeams_collections(dom, state, options);
                      }
                    }

                    if (callback) {
                      callback(true);
                    }
                  },
                });
              }
            );
          }
        );
      },
      {
        profile_image: true,
      }
    );
  }

  // team functions
  function teamView(dom, state, draw) {
    function editTeamTool(dom, state, draw, tool, callback) {
      dom.modals.makeNode("edit", "modal", {
        onShow: function () {
          sb.data.db.obj.getWhere("users", { enabled: 1 }, function (users) {
            var modal = dom.modals.edit.body;
            var formObj = {
              display_name: {
                name: "display_name",
                label: "Section Name",
                type: "text",
                value: tool.display_name,
              },
              tip: {
                name: "tip",
                label: "Description & Details",
                type: "textbox",
                rows: 5,
                value: tool.tip,
              },
            };

            modal.makeNode("title", "div", {
              text: "Edit " + tool.display_name,
              css: "ui huge header",
            });

            modal.makeNode("form", "form", formObj);

            modal.makeNode("titleBreak", "div", { text: "<br />" });

            modal
              .makeNode("save", "div", {
                text: "Save changes",
                css: "ui green button",
              })
              .notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function (dom, state, draw, tool) {
                      var formData = dom.modals.edit.body.form.process();

                      if (!formData.fields.display_name) {
                        sb.dom.alerts.alert(
                          "",
                          "The tool needs a name.",
                          "error"
                        );
                        return;
                      }

                      if (formData.fields.display_name.value == "") {
                        sb.dom.alerts.alert(
                          "",
                          "The tool needs a name.",
                          "error"
                        );
                        return;
                      }

                      dom.modals.edit.body.save.loading();

                      _.findWhere(state.team.tools, {
                        system_name: tool.system_name,
                      }).display_name = formData.fields.display_name.value;
                      _.findWhere(state.team.tools, {
                        system_name: tool.system_name,
                      }).tip = formData.fields.tip.value;

                      sb.data.db.obj.update(
                        "groups",
                        { id: state.team.id, tools: state.team.tools },
                        function (updated) {
                          state.team = updated;

                          dom.modals.edit.hide();

                          callback(state.team);
                        },
                        3
                      );
                    }.bind({}, dom, state, draw, tool),
                  },
                },
                sb.moduleId
              );

            modal.patch();
          });
        },
      });

      dom.modals.patch();
      dom.modals.edit.show();
    }

    dom.empty();

    dom.makeNode("modal", "modal", { css: "ui modal" });

    sb.data.db.obj.getById(
      "users",
      +sb.data.cookie.userId,
      function (user) {
        state.layer = "team";

        var obj = state.pageObject;
        var teamToolList = state.team.tools;
        var isManager = false;
        var managerIds = _.pluck(obj.managers, "id");

        var teamTools = _.reject(appConfig.teamTools, function (o) {
          return o.display === false;
        });

        if (managerIds.indexOf(+sb.data.cookie.get("uid")) > -1) {
          isManager = true;
        } else {
          if (user.type) {
            if (user.type === "admin" || user.type === "Admin") {
              isManager = true;
            }
          }
        }

        $("html,body").scrollTop(0);

        teamToolList = _.filter(teamToolList, function (t) {
          if (_.findWhere(teamTools, { id: t.system_name })) return t;
        });

        dom.makeNode("tools", "div", {});

        build_ToolsDashboard(dom.tools, {
          toolList: teamToolList,
          isManager: isManager,
          systemTools: teamTools,
          state: state,
          draw: draw,
          mainDom: dom,
          layer: "team",
        });

        // activity feed
        //dom.makeNode('titleBreak', 'div', {css:'ui clearing divider'});

        var style = sb.dom.isMobile ? "" : "margin:5px 15px 15px 15px";
        dom
          .makeNode("noteContainer", "div", {
            css: "ui one column centered grid",
          })
          .makeNode("col", "div", {
            css: "sixteen wide column round-border",
            style: style,
          });

        dom.makeNode("notesBreak", "div", { text: "<br /><br />" });

        dom.makeNode("modals", "div", {});

        draw({
          dom: dom,
          after: function (dom) {
            if (isManager === true) {
              $(".editTool").on("click", function (e) {
                e.preventDefault();
                //e.stopPropagation();

                editTeamTool(
                  dom,
                  state,
                  draw,
                  _.where(obj.tools, { system_name: this.id })[0],
                  function (done) {
                    teamView(dom, state, draw, function (done) {});
                  }
                );
              });
            }

            sb.notify({
              type: "show-note-list-box",
              data: {
                domObj: dom.noteContainer.col,
                objectIds: [state.team.id],
                objectId: state.team.id,
                collapse: "open",
                activityFeed: true,
              },
            });

            $(".fullScreenTool").on("click", function (e) {
              e.stopPropagation();
            });
          },
        });
      },
      { type: true }
    );
  }

  function addTeamSection(team, tool, onComplete) {
    sb.data.db.obj.getById(
      "groups",
      team.id,
      function (currentProject) {
        team.tools = currentProject.tools;

        var projectTool = _.findWhere(team.tools, { system_name: tool.id });

        if (projectTool) {
          if (projectTool.is_archieved == 1) {
            // turn the tool on
            _.findWhere(team.tools, { system_name: tool.id }).is_archieved = 0;

            if (tool.required) {
              var pass = true;

              _.each(tool.required, function (toolName) {
                var requiredTool = _.findWhere(appConfig.teamTools, {
                  id: toolName,
                });

                if (requiredTool) {
                  var toolOnProject = _.findWhere(team.tools, {
                    system_name: toolName,
                  });

                  if (toolOnProject) {
                    _.findWhere(team.tools, {
                      system_name: toolName,
                    }).is_archieved = 0;
                  } else {
                    team.tools.push({
                      allowed_users: [],
                      system_name: requiredTool.id,
                      display_name: requiredTool.name,
                      is_archieved: 0,
                      order: team.tools.length,
                      added_by: sb.data.cookie.userId,
                      added_on: moment(),
                      settings: {},
                      box_color: "",
                    });
                  }
                } else {
                  pass = false;
                }
              });

              if (pass === false) {
                sb.dom.alerts.alert(
                  "Error",
                  "A required tool is not available",
                  "error"
                );
                onComplete(false);
                return;
              }
            }

            sb.data.db.obj.update(
              "groups",
              team,
              function (updated) {
                onComplete(updated);
              },
              2
            );
          } else {
            var requiredByAnotherTool = false;
            _.each(appConfig.teamTools, function (systemTool) {
              if (systemTool.required) {
                if (systemTool.required.indexOf(tool.id) > -1) {
                  requiredByAnotherTool = _.findWhere(team.tools, {
                    system_name: systemTool.id,
                  });
                }
              }
            });

            if (requiredByAnotherTool) {
              if (requiredByAnotherTool.is_archieved == 0) {
                sb.dom.alerts.alert(
                  "",
                  "This tool is required by " +
                    requiredByAnotherTool.display_name +
                    ". Please remove that tool first.",
                  "error"
                );
                return;
              }
            }

            // turn the tool off
            _.findWhere(team.tools, { system_name: tool.id }).is_archieved = 1;

            sb.data.db.obj.update(
              "groups",
              { id: team.id, tools: team.tools },
              function (updated) {
                onComplete(updated);
              },
              2
            );
          }
        } else {
          // add the tool
          var toolObj = {
            allowed_users: [],
            system_name: tool.id,
            display_name: tool.name,
            is_archieved: 0,
            order: team.tools.length,
            added_by: sb.data.cookie.userId,
            added_on: moment(),
            settings: {},
            box_color: "",
          };

          team.tools.push(toolObj);

          if (tool.required) {
            var requiredTools = [];

            _.each(tool.required, function (toolName) {
              var requiredTool = _.findWhere(appConfig.teamTools, {
                id: toolName,
              });

              requiredTools.push({
                allowed_users: [],
                system_name: requiredTool.id,
                display_name: requiredTool.name,
                is_archieved: 0,
                order: team.tools.length,
                added_by: sb.data.cookie.userId,
                added_on: moment(),
                settings: {},
                box_color: "",
              });
            });
          }

          sb.data.db.obj.update(
            "groups",
            team,
            function (updated) {
              onComplete(updated);
            },
            2
          );
        }
      },
      {
        tools: {},
      }
    );
  }

  function createNewTeam(ui, state, defaultObject, callback) {
    function pointPeople(ui, teamObj) {
      sb.data.db.obj.getAll("users", function (users) {
        var selectableUsers = _.reject(users, function (user) {
          return teamObj.allowed_users.indexOf(user.id) == -1;
        });

        var formObj = {
          users: {
            name: "users",
            label: "",
            type: "checkbox",
            fieldCSS: "ui massive fluid input",
            options: _.map(selectableUsers, function (user) {
              var obj = {
                name: "users",
                label: user.fname + " " + user.lname,
                value: user.id,
              };

              if (user.id == +sb.data.cookie.userId) {
                obj.selected = true;
                obj.checked = true;
              }

              return obj;
            }),
          },
        };

        //formObj.users.value = [+sb.data.cookie.userId];

        ui.empty();

        ui.makeNode("title", "div", {
          text: "Who are the point people?",
          css: "ui huge header",
        });
        ui.makeNode("help", "div", {
          text: 'These users will have access to <span class="ui big orange label"><b>EDIT</b></span> this team. You will be able to change this list after the team has been created.',
          css: "",
          style: "font-size:1.2em;",
        });

        ui.makeNode("form", "form", formObj);

        ui.makeNode("formBreak", "div", { text: "<br />" });

        ui.makeNode("back", "div", {
          css: "ui blue huge button",
          text: "Back",
        }).notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (ui, teamObj) {
                teamMembers(ui, teamObj);
              }.bind({}, ui, teamObj),
            },
          },
          sb.moduleId
        );

        ui.makeNode("next", "div", {
          css: "ui green huge button",
          text: "Next step",
        }).notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (ui, teamObj) {
                teamObj.managers = _.map(
                  ui.form.process().fields.users.value,
                  function (user) {
                    return +user;
                  }
                );

                teamTools(ui, teamObj);
              }.bind({}, ui, teamObj),
            },
          },
          sb.moduleId
        );

        ui.patch();
      });
    }

    function teamName(ui, teamObj) {
      var formObj = {
        name: {
          name: "name",
          label: "",
          type: "text",
          placeholder: "Sales Team",
          fieldCSS: "ui massive fluid input",
        },
        description: {
          name: "description",
          label: "Enter a short description for this team",
          type: "text",
          placeholder: "Making it rain!",
        },
      };

      if (teamObj) {
        formObj.name.value = teamObj.name;
        formObj.description.value = teamObj.description;
      }

      ui.empty();

      ui.makeNode("title", "div", {
        text: "Give the team a name",
        css: "ui huge header",
      });

      ui.makeNode("form", "form", formObj);

      ui.makeNode("formBreak", "div", { text: "<br />" });

      ui.makeNode("next", "div", {
        css: "ui green huge button",
        text: "Next step",
      }).notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (ui) {
              if (ui.form.process().fields.name.value == "") {
                sb.dom.alerts.alert("Every team needs a name", "", "error");
                return;
              }

              ui.next.loading();

              if (!defaultObject && !teamObj) {
                var teamObj = {};
              } else {
                var teamObj = defaultObject;
              }

              teamObj.group_type = "Team";
              teamObj.name = ui.form.process().fields.name.value;
              teamObj.details = ui.form.process().fields.description.value;

              teamTools(ui, teamObj);
              //teamMembers(ui, teamObj);
            }.bind({}, ui),
          },
        },
        sb.moduleId
      );

      ui.patch();
    }

    function teamMembers(ui, teamObj) {
      sb.data.db.obj.getAll("users", function (users) {
        var formObj = {
          users: {
            name: "users",
            label: "",
            type: "checkbox",
            fieldCSS: "ui massive fluid input",
            options: _.map(users, function (user) {
              return {
                name: "users",
                label: user.fname + " " + user.lname,
                value: user.id,
              };
            }),
          },
        };

        formObj.users.value = [+sb.data.cookie.userId];

        if (teamObj) {
          if (teamObj.allowed_users) {
            formObj.users.value = teamObj.allowed_users.concat(
              formObj.users.value
            );
          }
        }

        ui.empty();

        ui.makeNode("title", "div", {
          text: "Who is a part of this team?",
          css: "ui huge header",
        });
        ui.makeNode("help", "div", {
          text: 'These users will have access to <span class="ui big teal label"><b>VIEW</b></span> this team. You will be able to change this list after the team has been created.',
          css: "",
          style: "font-size:1.2em;",
        });

        ui.makeNode("form", "form", formObj);

        ui.makeNode("formBreak", "div", { text: "<br />" });

        ui.makeNode("back", "div", {
          css: "ui blue huge button",
          text: "Back",
        }).notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (ui, teamObj) {
                teamName(ui, teamObj);
              }.bind({}, ui, teamObj),
            },
          },
          sb.moduleId
        );

        ui.makeNode("next", "div", {
          css: "ui green huge button",
          text: "Next step",
        }).notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (ui, teamObj) {
                teamObj.allowed_users = _.map(
                  ui.form.process().fields.users.value,
                  function (user) {
                    return +user;
                  }
                );

                pointPeople(ui, teamObj);
              }.bind({}, ui, teamObj),
            },
          },
          sb.moduleId
        );

        ui.patch();
      });
    }

    function teamTools(ui, teamObj) {
      ui.empty();

      ui.makeNode("title", "div", {
        text: "Choose the tools",
        css: "ui huge header",
      });

      ui.makeNode("items", "div", { css: "ui items" });

      var dom = ui;

      _.each(appConfig.teamTools, function (tool) {
        ui.makeNode("tool" + tool.id, "div", {
          css: "ui slider checkbox tool" + tool.id,
        });
        ui["tool" + tool.id].makeNode("input", "div", {
          tag: "input",
          type: "checkbox",
          name: tool.id,
        });
        ui["tool" + tool.id].makeNode("label", "div", {
          tag: "label",
          css: "ui header",
          text:
            '<i class="' +
            tool.icon.type +
            ' icon"></i><div class="content">' +
            tool.name +
            "<br /><small>" +
            tool.tip +
            "</small></div>",
        });

        ui.makeNode("break" + tool.id, "div", { css: "ui divider" });
      });

      ui.makeNode("formBreak", "div", { text: "<br />" });

      ui.makeNode("back", "div", {
        css: "ui blue huge button",
        text: "Back",
      }).notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (ui, teamObj) {
              teamName(ui, teamObj);
            }.bind({}, ui, teamObj),
          },
        },
        sb.moduleId
      );

      ui.makeNode("next", "div", {
        css: "ui green huge button",
        text: "Create the team",
      }).notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (ui, teamObj) {
              ui.next.loading();

              var toolStates = [];
              var toolOrder = 0;

              _.each(appConfig.teamTools, function (tool) {
                toolStates.push({
                  tool: tool.id,
                  name: tool.name,
                  checked: $(".ui.checkbox.tool" + tool.id).checkbox(
                    "is checked"
                  ),
                });
              });

              teamObj.tools = [];

              _.each(toolStates, function (tool) {
                if (tool.checked === true) {
                  teamObj.tools.push({
                    allowed_users: teamObj.allowed_users,
                    system_name: tool.tool,
                    display_name: tool.name,
                    is_archieved: 0,
                    order: toolOrder,
                    added_by: +sb.data.cookie.userId,
                    added_on: moment(),
                    settings: {},
                    box_color: "",
                  });

                  toolOrder++;
                }
              });

              if (state.pageObject) {
                teamObj.parent = state.pageObject.id;
                teamObj.managers = state.pageObject.managers;
                teamObj.tagged_with = _.union(
                  state.pageObject.tagged_with,
                  teamObj.tagged_with,
                  [state.pageObject.id],
                  state.pageObject.managers
                );
              }

              teamObj.managers = [+sb.data.cookie.userId];

              sb.data.db.obj.create(
                "groups",
                teamObj,
                function (newTeam) {
                  callback(newTeam);
                },
                2
              );
            }.bind({}, ui, teamObj),
          },
        },
        sb.moduleId
      );

      ui.patch();

      $(".ui.checkbox").checkbox();
      _.each(appConfig.teamTools, function (tool) {
        switch (tool.id) {
          case "contactTool":
          case "projectTool":
          case "teamTool":
            $(".ui.checkbox.tool" + tool.id).checkbox("set checked");

            break;
        }
      });
    }

    ui.patch();

    teamName(ui);
  }

  function manageTeamTools(dom, state, draw) {
    dom.makeNode("modal", "modal", {
      onShow: function () {
        dom.modal.body.empty();

        dom.modal.body.makeNode("btns", "div", {
          css: "ui right floated buttons",
        });
        dom.modal.body.btns
          .makeNode("close", "div", { css: "ui blue button", text: "Done" })
          .notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function (dom) {
                  dom.modal.hide();
                }.bind({}, dom),
              },
            },
            sb.moduleId
          );

        dom.modal.body.makeNode("title", "div", {
          css: "ui left floated huge header",
          text: "Team Tools",
        });

        dom.modal.body.makeNode("titleBreak", "div", { text: "<br /><br />" });

        dom.modal.body.makeNode("divider", "div", { css: "ui divider" });

        dom.modal.body.makeNode("tools", "div", {
          css: "ui big divided items",
        });

        _.each(appConfig.teamTools, function (tool) {
          dom.modal.body.tools
            .makeNode("tool-" + tool.id, "div", { css: "item" })
            .makeNode("content", "div", { css: "content" });

          var addButtonColor = "green";
          var addButtonText = "Add tool";
          var projectTool = _.findWhere(state.team.tools, {
            system_name: tool.id,
            is_archieved: 0,
          });
          if (projectTool) {
            addButtonColor = "red";
            addButtonText = "Remove tool";
          }

          dom.modal.body.tools["tool-" + tool.id].content.makeNode(
            "select",
            "button",
            {
              tag: "button",
              css: "ui right floated " + addButtonColor + " mini button",
              text: addButtonText,
            }
          );

          var requiresToolsString = "";
          var requiresStringInner = "";
          _.each(appConfig.teamTools, function (pTool) {
            var count = 0;

            if (pTool.required) {
              if (pTool.required.indexOf(tool.id) > -1 && tool.id != pTool.id) {
                if (count > 0) {
                  requiresStringInner += ", ";
                }

                requiresStringInner += pTool.name;

                count++;
              }

              if (requiresStringInner != "") {
                requiresToolsString =
                  ' <div class="ui left pointing mini blue label">Required by ' +
                  requiresStringInner +
                  "</div>";
              }
            }
          });

          var requiredToolsString = "";
          if (tool.required) {
            requiredToolsString =
              ' <div class="ui left pointing mini blue label">Requires ';
            _.each(tool.required, function (reqToolId, count) {
              if (count > 0) {
                requiredToolsString += ", ";
              }

              requiredToolsString += _.findWhere(appConfig.teamTools, {
                id: reqToolId,
              }).name;
            });

            requiredToolsString += "</div>";
          }

          dom.modal.body.tools["tool-" + tool.id].content.makeNode(
            "header",
            "div",
            {
              css: "header",
              text:
                '<i class="circular ' +
                tool.icon.color +
                " " +
                tool.icon.type +
                ' icon"></i> ' +
                tool.name +
                requiresToolsString +
                requiredToolsString,
            }
          );
          dom.modal.body.tools["tool-" + tool.id].content.makeNode(
            "meta",
            "div",
            { css: "description", text: tool.tip }
          );

          dom.modal.body.tools["tool-" + tool.id].content.select.notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function (dom, state, draw, tool) {
                  dom.modal.body.tools[
                    "tool-" + tool.id
                  ].content.select.loading();

                  sb.data.db.obj.getById(
                    "groups",
                    state.team.id,
                    function (currentProject) {
                      state.team.tools = currentProject.tools;

                      var projectTool = _.findWhere(state.team.tools, {
                        system_name: tool.id,
                      });

                      if (projectTool) {
                        if (projectTool.is_archieved == 1) {
                          // turn the tool on
                          _.findWhere(state.team.tools, {
                            system_name: tool.id,
                          }).is_archieved = 0;

                          if (tool.required) {
                            var pass = true;

                            _.each(tool.required, function (toolName) {
                              var requiredTool = _.findWhere(
                                appConfig.teamTools,
                                { id: toolName }
                              );

                              if (requiredTool) {
                                var toolOnProject = _.findWhere(
                                  state.team.tools,
                                  { system_name: toolName }
                                );

                                if (toolOnProject) {
                                  _.findWhere(state.team.tools, {
                                    system_name: toolName,
                                  }).is_archieved = 0;
                                } else {
                                  state.team.tools.push({
                                    allowed_users: [],
                                    system_name: requiredTool.id,
                                    display_name: requiredTool.name,
                                    is_archieved: 0,
                                    order: state.team.tools.length,
                                    added_by: sb.data.cookie.userId,
                                    added_on: moment(),
                                    settings: {},
                                    box_color: "",
                                  });
                                }
                              } else {
                                pass = false;
                              }
                            });

                            if (pass === false) {
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.loading(false);
                              sb.dom.alerts.alert(
                                "Error",
                                "A required tool is not available",
                                "error"
                              );
                              return;
                            }
                          }

                          sb.data.db.obj.update(
                            "groups",
                            state.team,
                            function (updated) {
                              _.each(updated.tools, function (projectTool) {
                                if (projectTool.is_archieved == 0) {
                                  dom.modal.body.tools[
                                    "tool-" + tool.id
                                  ].content.select.loading(false);
                                  dom.modal.body.tools[
                                    "tool-" + tool.id
                                  ].content.select.text("Remove tool");
                                }
                              });

                              state.team = updated;

                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.loading(false);
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.text("Remove tool");
                            },
                            2
                          );
                        } else {
                          var requiredByAnotherTool = false;
                          _.each(appConfig.teamTools, function (systemTool) {
                            if (systemTool.required) {
                              if (systemTool.required.indexOf(tool.id) > -1) {
                                requiredByAnotherTool = _.findWhere(
                                  state.team.tools,
                                  { system_name: systemTool.id }
                                );
                              }
                            }
                          });

                          if (requiredByAnotherTool) {
                            if (requiredByAnotherTool.is_archieved == 0) {
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.loading(false);
                              sb.dom.alerts.alert(
                                "",
                                "This tool is required by " +
                                  requiredByAnotherTool.display_name +
                                  ". Please remove that tool first.",
                                "error"
                              );
                              return;
                            }
                          }

                          // turn the tool off
                          _.findWhere(state.team.tools, {
                            system_name: tool.id,
                          }).is_archieved = 1;

                          sb.data.db.obj.update(
                            "groups",
                            { id: state.team.id, tools: state.team.tools },
                            function (updated) {
                              state.team = updated;

                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.loading(false);
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.css("");
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.css(
                                "ui right floated green mini button"
                              );
                              dom.modal.body.tools[
                                "tool-" + tool.id
                              ].content.select.text("Add tool");
                            },
                            2
                          );
                        }
                      } else {
                        // add the tool
                        var toolObj = {
                          allowed_users: [],
                          system_name: tool.id,
                          display_name: tool.name,
                          is_archieved: 0,
                          order: state.team.tools.length,
                          added_by: sb.data.cookie.userId,
                          added_on: moment(),
                          settings: {},
                          box_color: "",
                        };

                        state.team.tools.push(toolObj);

                        if (tool.required) {
                          var requiredTools = [];

                          _.each(tool.required, function (toolName) {
                            var requiredTool = _.findWhere(
                              appConfig.teamTools,
                              { id: toolName }
                            );

                            requiredTools.push({
                              allowed_users: [],
                              system_name: requiredTool.id,
                              display_name: requiredTool.name,
                              is_archieved: 0,
                              order: state.team.tools.length,
                              added_by: sb.data.cookie.userId,
                              added_on: moment(),
                              settings: {},
                              box_color: "",
                            });
                          });
                        }

                        sb.data.db.obj.update(
                          "groups",
                          state.team,
                          function (updated) {
                            state.team = updated;

                            dom.modal.body.tools[
                              "tool-" + tool.id
                            ].content.select.loading(false);
                            dom.modal.body.tools[
                              "tool-" + tool.id
                            ].content.select.css("");

                            _.each(updated.tools, function (projectTool) {
                              if (projectTool.is_archieved == 0) {
                                dom.modal.body.tools[
                                  "tool-" + tool.id
                                ].content.select.loading(false);
                                dom.modal.body.tools[
                                  "tool-" + tool.id
                                ].content.select.text("Remove tool");
                              }
                            });
                          },
                          2
                        );
                      }
                    },
                    {
                      tools: {},
                    }
                  );
                }.bind({}, dom, state, draw, tool),
              },
            },
            sb.moduleId
          );
        });

        dom.modal.body.patch();
      },
      onClose: function () {
        sb.data.db.obj.getById(
          "groups",
          state.team.id,
          function (currentProject) {
            state.team = currentProject;

            teamView(dom, state, draw, function (done) {});
          },
          2
        );
      },
    });

    draw(dom);

    dom.modal.show();
  }

  // project functions
  function projectView(obj, dom, state, draw) {
    var isManager = _.chain(obj.managers)
      .pluck("id")
      .contains(+sb.data.cookie.userId)
      .value();

    var write = true;

    if (!state.hasOwnProperty("components")) {
      state.components = {};
    }

    state.layer = "project";

    proposalSetup(obj, function (project) {
      state.project = project;

      $("html,body").scrollTop(0);

      dom.makeNode("tagsContainer", "div", {
        css: "round-border",
        style:
          "background-color: white !important; margin-top:15px; padding:15px;",
      });

      dom.tagsContainer.makeNode("header", "div", {
        css: "ui grey sub header",
        text: '<i class="tags icon"></i> Tags',
      });

      dom.tagsContainer.makeNode("tags", "div", {});

      dom.makeNode("lb_2", "lineBreak", { spaces: 1 });
      dom.makeNode("dashboard", "div", {
        css: "",
      });

      // activity feed
      //dom.makeNode('titleBreak', 'div', {css:'ui clearing divider'});

      var style = sb.dom.isMobile ? "" : "margin:5px 15px 15px 15px";

      if (appConfig.instance !== "foundation_group") {
        dom
          .makeNode("noteContainer", "div", {
            css: "ui one column centered grid",
          })
          .makeNode("col", "div", {
            css: "sixteen wide column round-border",
            style: style,
          });

        dom.makeNode("notesBreak", "div", { text: "<br /><br />" });
      }

      state.project.tools = _.chain(state.project.tools)
        .filter(function (tool) {
          return tool.is_archieved === 0;
        })
        .sortBy(function (tool) {
          return tool.order;
        })
        .value();

      draw({
        dom: dom,
        after: function (dom) {
          if (!appConfig.is_portal) {
            sb.notify({
              type: "view-field",
              data: {
                type: "tags",
                property: "tagged_with",
                obj: project,
                options: {
                  filter: false,
                  build: true,
                  canEdit: sb.permissions.isGroupManager(
                    +sb.data.cookie.userId,
                    project
                  ),
                },
                ui: dom.tagsContainer.tags,
              },
            });
          }

          sb.notify({
            type: "show-dashboard",
            data: {
              dom: dom.dashboard,
              state: state,
              draw: draw,
            },
          });

          currentProj = state.project.id;

          var noteIds = [state.project.id];
          if (state.project.main_contact) {
            noteIds.push(state.project.main_contact.id);
          }

          if (state.components.tags) {
            state.components.tags.destroy();
          }

          if (appConfig.instance !== "foundation_group") {
            sb.notify({
              type: "show-note-list-box",
              data: {
                domObj: dom.noteContainer.col,
                objectIds: [state.project.id],
                objectId: state.project.id,
                collapse: "open",
                activityFeed: true,
              },
            });
          }
        },
      });
    });
  }

  // New Version
  function createNewProject(ui, state, defaultObject, callback, setup) {
    // Initialize variables
    defaultObject.start_date = "";
    defaultObject.end_date = "";

    function createNewBlankProject(ui, state, obj, callback) {
      ui.footer.newProject.loading();

      var form = ui.form;

      var projectNameInputSelector =
        form.projectNameContainer.projectNameInputContainer.selector;
      var projectStartDateInputSelector =
        form.projectStartDateContainer.projectStartDateInputContainer.selector +
        " input";
      var projectEndDateInputSelector =
        form.projectEndDateContainer.projectEndDateInputContainer.selector +
        " input";

      $(projectNameInputSelector).removeClass("required");
      $(projectStartDateInputSelector).removeClass("required");
      $(projectEndDateInputSelector).removeClass("required");

      var projectName = obj.name;
      var projectIsOngoing = obj.is_ongoing;
      var projectStartDate = !projectIsOngoing ? obj.start_date : "";
      var projectEndDate = !projectIsOngoing ? obj.end_date : "";

      if (
        _.isEmpty(projectName) ||
        (_.isEmpty($(projectStartDateInputSelector).val()) &&
          !projectIsOngoing) ||
        (_.isEmpty($(projectEndDateInputSelector).val()) && !projectIsOngoing)
      ) {
        sb.dom.alerts.alert(
          "Uh-oh",
          "Please fill out all required fields.",
          "error"
        );

        if (_.isEmpty(projectName)) {
          $(projectNameInputSelector).addClass("required");
        }

        if (
          _.isEmpty($(projectStartDateInputSelector).val()) &&
          !projectIsOngoing
        ) {
          $(projectStartDateInputSelector).addClass("required");
        }

        if (
          _.isEmpty($(projectEndDateInputSelector).val()) &&
          !projectIsOngoing
        ) {
          $(projectEndDateInputSelector).addClass("required");
        }

        ui.footer.newProject.loading(false);

        return;
      }

      if (!projectIsOngoing) {
        var projectStartDateMoment = moment(
          projectStartDate,
          "YYYY-MM-DD HH:mm:ss"
        );
        var projectEndDateMoment = moment(
          projectEndDate,
          "YYYY-MM-DD HH:mm:ss"
        );
        var isValidDate = projectStartDateMoment.isBefore(projectEndDateMoment);

        if (!isValidDate) {
          sb.dom.alerts.alert(
            "Uh-oh",
            "Please make sure the start date is before the end date.",
            "error"
          );
          $(projectStartDateInputSelector).addClass("required");
          $(projectEndDateInputSelector).addClass("required");

          ui.footer.newProject.loading(false);

          return;
        }
      }

      var defaultTools = _.where(appConfig.projectTools, { default: true });

      obj.tools = [];

      _.each(defaultTools, function (tool, i) {
        obj.tools.push({
          allowed_users: obj.allowed_users,
          system_name: tool.id,
          display_name: tool.name,
          is_archieved: 0,
          order: i,
          added_by: +sb.data.cookie.userId,
          added_on: moment(),
          settings: {},
          box_color: "",
        });
      });

      if (state.pageObject) {
        obj.tagged_with = _.union(
          state.pageObject.tagged_with,
          obj.tagged_with,
          [state.pageObject.id]
        );
      }

      if (state.pageObject.hasOwnProperty("company")) {
        obj.parent = state.pageObject.company.id;
        obj.tagged_with.push(state.pageObject.company.id);
      } else if (state.hasOwnProperty("layer")) {
        if (state.layer === "team") {
          obj.parent = state.team.id;
        } else if (state.layer === "object") {
          obj.parent = state.pageObject.id;
        }
      } else if (state.hasOwnProperty("entity")) {
        if (state.entity) {
          obj.parent = state.entity;
        }
      } else {
        obj.parent = state.id;
      }

      // Set the main contact
      if (state.pageObjectType === "contacts") {
        obj.main_contact = state.id;
      }

      obj.name = projectName;
      obj.start_date = projectStartDate;
      obj.end_date = projectEndDate;
      obj.group_type = "Project";
      obj.managers = [+sb.data.cookie.userId];
      obj.is_ongoing = projectIsOngoing;

      sb.data.db.obj.create(
        "groups",
        obj,
        function (newProject) {
          callback(newProject);
        },
        2
      );
    }

    function searchForProjectTemplate(ui, state, obj, draw) {
      var form = ui.form;
      form.inputContainer.btn.loading();
      ui.results.loading();

      var term = $(form.inputContainer.input.selector).val();
      var cachedResults = [];

      if (term.length < 3 || term == "") {
        ui.results.loading(false);
        ui.form.inputContainer.btn.loading(false);
        ui.results.empty();
        ui.results.patch();
        return;
      }

      var collectionsSetup = {
        actions: {},
        domObj: ui.results,
        fields: {
          name: {
            title: "Template Name",
            view: function (ui, template) {
              ui.makeNode("row-" + template.id, "div", {
                style: "font-weight:bold",
                text: template.name,
              });
            },
          },
          select: {
            title: "-",
            view: function (ui, template) {
              ui.makeNode("row-" + template.id, "div", {});
              ui["row-" + template.id]
                .makeNode("select", "div", {
                  text: '<i class="fa fa-plus"></i> Use This Template',
                  css: "ui basic green button",
                  style: "float:right;",
                })
                .notify(
                  "click",
                  {
                    type: "headquarters-run",
                    data: {
                      run: function () {
                        ui["row-" + template.id].select.loading();

                        var projectNameInputSelector =
                          form.projectNameContainer.projectNameInputContainer
                            .selector;
                        var projectStartDateInputSelector =
                          form.projectStartDateContainer
                            .projectStartDateInputContainer.selector + " input";
                        var projectEndDateInputSelector =
                          form.projectEndDateContainer
                            .projectEndDateInputContainer.selector + " input";

                        $(projectNameInputSelector).removeClass("required");
                        $(projectStartDateInputSelector).removeClass(
                          "required"
                        );
                        $(projectEndDateInputSelector).removeClass("required");

                        var projectName = obj.name;
                        var projectIsOngoing = obj.is_ongoing;
                        var projectStartDate = !projectIsOngoing
                          ? obj.start_date
                          : "";
                        var projectEndDate = !projectIsOngoing
                          ? obj.end_date
                          : "";

                        if (
                          _.isEmpty(projectName) ||
                          (_.isEmpty($(projectStartDateInputSelector).val()) &&
                            !projectIsOngoing) ||
                          (_.isEmpty($(projectEndDateInputSelector).val()) &&
                            !projectIsOngoing)
                        ) {
                          sb.dom.alerts.alert(
                            "Uh-oh",
                            "Please fill out all required fields.",
                            "error"
                          );

                          if (_.isEmpty(projectName)) {
                            $(projectNameInputSelector).addClass("required");
                          }

                          if (
                            _.isEmpty($(projectStartDateInputSelector).val()) &&
                            !projectIsOngoing
                          ) {
                            $(projectStartDateInputSelector).addClass(
                              "required"
                            );
                          }

                          if (
                            _.isEmpty($(projectEndDateInputSelector).val()) &&
                            !projectIsOngoing
                          ) {
                            $(projectEndDateInputSelector).addClass("required");
                          }

                          ui["row-" + template.id].select.loading(false);

                          return;
                        }

                        if (!projectIsOngoing) {
                          var projectStartDateMoment = moment(
                            projectStartDate,
                            "YYYY-MM-DD HH:mm:ss"
                          );
                          var projectEndDateMoment = moment(
                            projectEndDate,
                            "YYYY-MM-DD HH:mm:ss"
                          );
                          var isValidDate =
                            projectStartDateMoment.isBefore(
                              projectEndDateMoment
                            );

                          if (!isValidDate) {
                            sb.dom.alerts.alert(
                              "Uh-oh",
                              "Please make sure the start date is before the end date.",
                              "error"
                            );
                            $(projectStartDateInputSelector).addClass(
                              "required"
                            );
                            $(projectEndDateInputSelector).addClass("required");

                            ui["row-" + template.id].select.loading(false);

                            return;
                          }
                        }

                        _.each(template.tools, function (tool, i) {
                          template.tools[i].added_by = +sb.data.cookie.userId;
                          template.tools[i].added_on = moment();
                        });

                        if (state.pageObject) {
                          obj.tagged_with = _.union(
                            state.pageObject.tagged_with,
                            obj.tagged_with,
                            [state.pageObject.id]
                          );
                        }

                        if (state.pageObject.hasOwnProperty("company")) {
                          obj.parent = state.pageObject.company.id;
                          obj.tagged_with.push(state.pageObject.company.id);
                        } else if (state.hasOwnProperty("layer")) {
                          if (state.layer === "team") {
                            obj.parent = state.team.id;
                            obj.tagged_with.push(state.team.id);
                          } else if (state.layer === "object") {
                            obj.parent = state.pageObject.id;
                            obj.tagged_with.push(state.pageObject.id);
                          }
                        } else if (state.hasOwnProperty("entity")) {
                          if (state.entity) {
                            obj.parent = state.entity;
                          }
                        } else {
                          obj.parent = state.id;
                        }

                        var newProject = {
                          name: projectName,
                          start_date: projectStartDate,
                          end_date: projectEndDate,
                          tagged_with: obj.tagged_with,
                          managers: [+sb.data.cookie.userId],
                          parent: obj.parent,
                          group_type: "Project",
                          is_ongoing: obj.is_ongoing,
                          // tools: template.tools
                        };

                        if (
                          defaultObject &&
                          defaultObject.hasOwnProperty("main_contact") &&
                          defaultObject.main_contact
                        ) {
                          newProject.main_contact = defaultObject.main_contact;
                        } else {
                          if (
                            state.hasOwnProperty("object") &&
                            state.object.object_bp_type === "contacts"
                          ) {
                            newProject.main_contact = state.object.id;
                          }
                        }

                        sb.data.db.obj.createFromTemplate(
                          template.id,
                          function (response) {
                            callback(response);
                          },
                          1,
                          newProject
                        );
                      }.bind(ui, template, state),
                    },
                  },
                  sb.moduleId
                );
            },
          },
        },
        objectType: "groups",
        selectedView: "table",
        subviews: {
          table: {
            hideSelectionBoxes: true,
            hideRowActions: true,
          },
        },
        onBoxview: true,
        state: state,
        sortCol: "name",
        sortDir: "asc",
        menu: false,
        submenu: false,
        tags: false,
        pageLength: 5,
        where: {
          group_type: "Project",
          name: {
            type: "contains",
            value: term,
          },
          is_template: 1,
          is_active: {
            type: "not_equal",
            value: "No",
          },
          childObjs: {
            name: true,
          },
        },
      };

      sb.notify({
        type: "show-collection",
        data: collectionsSetup,
      });

      ui.results.patch();
      ui.results.loading(false);
      ui.form.inputContainer.btn.loading(false);
    }

    var draw = function () {};

    var modal = ui;

    modal.makeNode("body", "div", {
      css: "ui stackable grid",
    });

    var headerText = state.is_template ? "New Project Template" : "New Project";
    modal.body.makeNode("header", "div", {
      css: "ui stackable grid",
      style: "padding-bottom:0 !important;",
    });
    modal.body.header.makeNode("left", "div", {
      css: "sixteen wide column",
      style: "padding:0 !important;",
      text: '<span class="ui large header">' + headerText + "</span>",
    });

    modal.body.makeNode("form", "div", {
      css: "ui stackable grid sixteen wide column",
      style: "padding:0 !important",
    });

    modal.body.form.makeNode("projectNameContainer", "div", {
      css: "sixteen wide column",
      style: "padding-bottom:0 !important;",
    });
    modal.body.form.projectNameContainer.makeNode("projectNameLabel", "div", {
      tag: "label",
      text: "Project Name (required)",
    });
    var projectNameInputContainer =
      modal.body.form.projectNameContainer.makeNode(
        "projectNameInputContainer",
        "div",
        {
          css: "round-border",
          style: "padding:1em;",
        }
      );
    sb.notify({
      type: "view-field",
      data: {
        type: "plain-text",
        property: "name",
        ui: projectNameInputContainer,
        obj: defaultObject,
        options: {
          edit: true,
          editing: true,
          dateType: "datetime",
          size: "huge",
          onUpdate: function (val) {
            defaultObject.name = val;
          },
        },
      },
    });

    modal.body.form.makeNode("projectStartDateContainer", "div", {
      css: "eight wide column ui calendar",
    });
    modal.body.form.projectStartDateContainer.makeNode(
      "projectStartDateLabel",
      "div",
      {
        tag: "label",
        text: "Start Date (required)",
      }
    );
    var projectStartDateInputContainer =
      modal.body.form.projectStartDateContainer.makeNode(
        "projectStartDateInputContainer",
        "div",
        {}
      );
    sb.notify({
      type: "view-field",
      data: {
        type: "date",
        property: "start_date",
        ui: projectStartDateInputContainer,
        obj: defaultObject,
        options: {
          edit: true,
          editing: true,
          dateType: "datetime",
          size: "huge",
          onUpdate: function (val) {
            defaultObject.start_date = val;
          },
        },
      },
    });

    modal.body.form.makeNode("projectEndDateContainer", "div", {
      css: "eight wide column ui calendar",
    });
    modal.body.form.projectEndDateContainer.makeNode(
      "projectEndDateLabel",
      "div",
      {
        tag: "label",
        text: "End Date (required)",
      }
    );
    var projectEndDateInputContainer =
      modal.body.form.projectEndDateContainer.makeNode(
        "projectEndDateInputContainer",
        "div",
        {}
      );
    sb.notify({
      type: "view-field",
      data: {
        type: "date",
        property: "end_date",
        ui: projectEndDateInputContainer,
        obj: defaultObject,
        options: {
          edit: true,
          editing: true,
          size: "huge",
          onUpdate: function (val) {
            defaultObject.end_date = val;
          },
        },
      },
    });

    modal.body.form.makeNode("projectOngoingContainer", "div", {
      css: "sixteen wide column",
    });
    var projectOngoingCheckboxContainer =
      modal.body.form.projectOngoingContainer.makeNode(
        "projectOngoingCheckboxContainer",
        "div",
        {}
      );
    projectOngoingCheckboxContainer.makeNode("label", "div", {
      text: "<strong>This project is ongoing and does not have set dates</strong>",
    });
    sb.notify({
      type: "view-field",
      data: {
        type: "toggle",
        property: "is_ongoing",
        ui: projectOngoingCheckboxContainer.makeNode("toggle", "div", {}),
        obj: defaultObject,
        options: {
          edit: true,
          editing: true,
          size: "huge",
          onUpdate: function (val) {
            // Set value
            defaultObject.is_ongoing = val;

            // Hide / Show Date fields
            if (val) {
              $(modal.body.form.projectStartDateContainer.selector).hide(
                "slow"
              );
              $(modal.body.form.projectEndDateContainer.selector).hide("slow");
            } else {
              $(modal.body.form.projectStartDateContainer.selector).show(
                "slow"
              );
              $(modal.body.form.projectEndDateContainer.selector).show("slow");
            }
          },
        },
      },
    });

    modal.body.form.makeNode("label", "div", {
      tag: "label",
      text: "Search for Project Templates",
    });
    modal.body.form.makeNode("inputContainer", "div", {
      css: "ui huge fluid action input",
    });
    modal.body.form.inputContainer
      .makeNode("input", "div", {
        tag: "input",
        type: "text",
        placeholder: "Search...",
      })
      .notify(
        "change",
        {
          type: "headquarters-run",
          data: {
            run: function (ui, state, defaultObject, draw) {
              setTimeout(function () {
                searchForProjectTemplate.call(
                  {},
                  ui,
                  state,
                  defaultObject,
                  draw
                );
              }, 750);
            }.bind({}, modal.body, state, defaultObject, draw),
          },
        },
        sb.moduleId
      );

    modal.body.form.inputContainer
      .makeNode("btn", "div", {
        text: "Search",
        css: "ui button",
      })
      .notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (ui, state, defaultObject, draw) {
              searchForProjectTemplate.bind({}, ui, state, defaultObject, draw);
            }.bind({}, modal.body, state, defaultObject, draw),
          },
        },
        sb.moduleId
      );

    modal.body.makeNode("results", "div", {
      css: "sixteen wide column",
      style: "margin-top:0px !important; padding:0 !important;",
    });

    modal.body.makeNode("footer", "div", {
      css: "sixteen wide column",
    });
    var newProjectText = state.is_template
      ? 'or ... Create Project Template from Scratch <i class="fa fa-arrow-right"></i>'
      : 'or ... Create Project without Template <i class="fa fa-arrow-right"></i>';
    modal.body.footer
      .makeNode("newProject", "div", {
        text: newProjectText,
        css: "ui large green button",
        style:
          "margin-top:15px !important; margin-right:0 !important; float:right;",
      })
      .notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function () {
              createNewBlankProject(modal.body, state, defaultObject, callback);
            },
          },
        },
        sb.moduleId
      );

    ui.patch();

    modal.show();

    $(projectStartDateInputContainer.selector + " input").val("");
    $(projectEndDateInputContainer.selector + " input").val("");
  }

  function loadProjectTool(dom, state, draw, tool) {
    dom.empty();

    dom.makeNode("topCont", "div", { css: "" });

    breadcrumbsUI(dom.topCont, state, draw, dom, tool);

    //dom.topCont.makeNode('btns', 'div', {css:'ui mini buttons'});

    //dom.topCont.makeNode('header', 'div', {text:state.project.name, css:'ui header'});

    /*
		dom.topCont.btnsCont.btns.makeNode('edit', 'div', {css:'ui blue button', text:'Edit project details'})
			.notify('click', {
				type:'projects-run',
				data:{
					run:editProjectDetails.bind({}, state.project, dom, state, draw, function(updated){
						state.project = updated;
						loadProjectTool(dom, state, draw, tool);
					})
				}
			}, sb.moduleId);
*/
    /*
		dom.topCont.btnsCont.btns.makeNode('back', 'div', {css:'ui blue button', text:'Back'}).notify('click', {
			type:'projects-run',
			data:{
				run:projectView.bind({}, state.project, dom, state, draw)
			}
		}, sb.moduleId);
*/

    //dom.makeNode('toolBreak', 'div', {text:'<br />'});

    dom.makeNode("modals", "div", {});

    dom.makeNode("tool", "div", { css: "" });

    dom.tool.makeNode("title", "div", {
      css: "ui huge header",
      text: tool.display_name,
    });

    dom.tool.makeNode("btns", "div", { css: "ui mini buttons" });

    dom.tool.btns
      .makeNode("back", "div", { css: "ui blue mini button", text: "Back" })
      .notify(
        "click",
        {
          type: "projects-run",
          data: {
            run: projectView.bind({}, state.project, dom, state, draw),
          },
        },
        sb.moduleId
      );

    dom.tool.btns
      .makeNode("settings", "div", {
        css: "ui yellow mini button",
        text: "Edit Tool Settings",
      })
      .notify(
        "click",
        {
          type: "projects-run",
          data: {
            run: editTool.bind({}, dom.modals, state, draw, tool, dom),
          },
        },
        sb.moduleId
      );

    //dom.tool.makeNode('titleBreak', 'div', {text:'<br /><br />'});

    dom.tool.makeNode("divider", "div", { css: "ui clearing divider" });

    //dom.tool.makeNode('toolContBreak', 'div', {text:'<br />'});

    dom.tool.makeNode("tabs", "div", {});

    dom.tool.makeNode("toolCont", "div", { css: "ui basic loading segment" });

    dom.makeNode("pageBreak", "div", { text: "<br /><br />" });

    var style = sb.dom.isMobile ? "" : "margin:5px 15px 15px 15px";
    dom
      .makeNode("noteContainer", "div", { css: "ui one column centered grid" })
      .makeNode("col", "div", {
        css: "sixteen wide column round-border",
        style: style,
      });

    dom.makeNode("notesBreak", "div", { text: "<br /><br />" });

    draw({
      dom: dom,
      after: function (dom) {
        dom.tool.makeNode("toolCont", "div", { css: "ui basic segment" });

        dom.tool.patch();

        tool.mainViews = _.findWhere(appConfig.projectTools, {
          id: tool.system_name,
        }).mainViews;

        if (typeof tool.mainViews[0].dom != "function") {
          dom.tool.empty();

          dom.tool.makeNode("error", "div", {
            text: "Tool box main object is not an object.",
            css: "ui red header",
          });

          dom.tool.patch();
        } else {
          if (tool.mainViews.length > 1) {
            dom.tool.tabs.makeNode("tabMenu", "div", {
              css: "ui top attached tabular menu",
            });

            _.each(tool.mainViews, function (view, count) {
              var active = "";
              var name = "View needs a name";
              if (count == 0) {
                active = "active";
              }

              if (view.name) {
                name = view.name;
              }

              dom.tool.tabs.tabMenu.makeNode("item-" + count, "div", {
                text: name,
                tag: "a",
                css: active + " item",
                dataAttr: [{ name: "tab", value: "view-" + count }],
              });

              dom.tool.tabs
                .makeNode("tool-" + count, "div", {
                  css: "ui " + active + " tab bottom attached segment",
                  dataAttr: [{ name: "tab", value: "view-" + count }],
                })
                .makeNode("cont", "div", {});
            });

            dom.tool.tabs.patch();

            $(".menu .item").tab();

            _.each(tool.mainViews, function (view, count) {
              view.dom(
                dom.tool.tabs["tool-" + count].cont,
                state,
                function (domObj) {
                  if (domObj.patch) {
                    domObj.patch();
                  } else {
                    domObj.dom.patch();

                    domObj.after(domObj.dom);
                  }
                }
              );
            });
          } else {
            tool.mainViews[0].dom(dom.tool.toolCont, state, function (domObj) {
              if (domObj.patch) {
                domObj.patch();
              } else {
                domObj.dom.patch();

                domObj.after(domObj.dom);
              }
            });
          }
        }
      },
    });
  }

  function proposalSetup(project, callback) {
    if (project.proposal) {
      callback(project);
    } else {
      project.main_object = project.id;

      sb.data.db.obj.create(
        "proposals",
        project,
        function (newProp) {
          sb.data.db.obj.update(
            "groups",
            { id: project.id, proposal: newProp.id },
            function (updatedProject) {
              callback(updatedProject);
            },
            1
          );
        },
        {
          id: true,
        }
      );
    }
  }

  function projectContactTool(dom, state, draw, project, options) {
    var collectionsSetup = {
      domObj: dom,
      actions: {
        create: function (ui, newObj, onComplete, bp, setup) {
          newObj.parent = state.contact;
          newObj.main_contact = state.contact;

          createNewProject(
            ui,
            state,
            newObj,
            function (newProject) {
              if (newProject) {
                sb.notify({
                  type: "app-navigate-to",
                  data: {
                    itemId: "groups",
                    viewId: {
                      viewState: {
                        obj: newProject,
                      },
                    },
                  },
                });
              }
            },
            setup
          );
        },
        removeFromProject: {
          icon: "times",
          title: "Remove from project",
          color: "red",
          domType: "none",
          action: function (obj, state, shouldUpdate) {
            obj.main_contact = 0;

            sb.data.db.obj.update(
              "groups",
              obj,
              function (updated) {
                if (project !== undefined && obj.id === project.id) {
                  sb.notify({
                    type: "app-navigate-to",
                    data: {
                      type: "UP",
                    },
                  });
                } else {
                  shouldUpdate(true);
                }
              },
              1
            );
          },
        },
      },
      fields: {
        name: {
          title: "Name",
          type: "title",
          isSearchable: true,
        },
        state: {
          title: "Status",
          type: "state",
        },
        priority: {
          title: "Priority",
          type: "priority",
        },
        start_date: {
          title: "Start Date",
          view: function (ui, obj) {
            var text = "Not set";

            if (obj.start_date) {
              text = moment(obj.start_date)
                .local()
                .format("M/D/YYYY h:mm:ss a");
            }

            if (ui) {
              ui.makeNode("div", "div", { text: "<nobr>" + text + "</nobr>" });
            }
          },
        },
        end_date: {
          title: "End Date",
          view: function (ui, obj) {
            var text = "Not set";

            if (obj.end_date) {
              text = moment(obj.end_date).local().format("M/D/YYYY h:mm:ss a");
            }

            if (ui) {
              ui.makeNode("div", "div", { text: "<nobr>" + text + "</nobr>" });
            }
          },
        },
        invoice_value: {
          title: "Value",
          view: function (ui, obj) {
            var text = 0;

            if (obj.invoice_value) {
              text = obj.invoice_value / 100;
            }

            if (ui) {
              ui.makeNode("div", "div", {
                text: "<nobr>$" + text.formatMoney() + "</nobr>",
              });
            }
          },
        },
        last_updated: {
          title: "Last updated",
          type: "date",
          view: function (ui, o) {
            ui.makeNode("date", "div", {
              text: moment(o.last_updated).fromNow(),
            });
          },
        },
        managers: {
          title: "Managers",
          type: "users",
          parseUpdates: function (updates) {
            updates.managers = [updates.managers];
            var leadsList = updates.managers;
            updates.users = leadsList;
            updates.tagged_with = _.chain(leadsList)
              .union(updates.tagged_with)
              .uniq()
              .value();

            updates.notify = updates.tagged_with;
            return updates;
          },

          options: {
            mini: true,
          },
        },
      },
      size: "small",
      singleView: {
        action: function (obj) {
          sb.notify({
            type: "app-navigate-to",
            data: {
              type: "object",
              object: obj,
            },
          });
        },
        useCache: true,
        view: function (dom, obj, onComplete, refresh, options, viewOptions) {
          sb.notify({
            type: "app-navigate-to",
            data: {
              type: "object",
              object: obj,
            },
          });
        },
      },
      groupings: false,
      subviews: {
        list: {
          groupBy: {
            defaultTo: "type",
          },
        },
        board: {
          groupBy: {
            defaultTo: "state",
          },
        },
        chart: {
          range: {
            defaultTo: "this_month",
            not: ["all_time"],
          },
          defaults: {
            type: "pie",
          },
          line: false,
        },
      },
      objectType: "groups",
      where: {
        group_type: "Project",
        childObjs: {
          name: true,
          main_contact: true,
          description: true,
          state: true,
          priority: true,
          group_type: true,
          type: {
            name: true,
            states: true,
          },
          managers: {
            fname: true,
            lname: true,
            profile_image: true,
          },
          salesmanagers: {
            fname: true,
            lname: true,
            profile_image: true,
          },
          invoice_value: true,
          tools: true,
          start_date: true,
          end_date: true,
          last_updated: true,
        },
      },
    };

    // Get the company object
    var companyObj = appConfig.breadcrumbs[appConfig.breadcrumbs.length - 2];

    // Setup the collections options
    if (options !== undefined) {
      if (options.hasOwnProperty("collections")) {
        _.each(options.collections, function (propVal, propKey) {
          collectionsSetup[propKey] = propVal;
        });
      }
    }

    // Initialize variables
    var mainContactId = false;

    switch (state.pageObjectType) {
      case "companies":
        // console.log('Case Met 1')
        // Case 1
        // For the Projects tab on a Company

        collectionsSetup.where.tagged_with = [state.pageObject.id];

        break;

      case "contacts":
        // console.log('Case Met 2')
        // Case 2
        // For the Projects tab on a Contact

        mainContactId = state.pageObject.id;

        collectionsSetup.where.tagged_with = {
          type: "any",
          values: [mainContactId],
          or: {
            main_contact: mainContactId,
          },
        };

        break;
    }

    if (state.project) {
      if (state.hasOwnProperty("object")) {
        if (state.project.id === state.id) {
          // console.log('Case Met 3')
          // Case 3
          // For within the Point of Contact tool (Project tab on a Contact)

          mainContactId = state.project.main_contact.id;

          collectionsSetup.where.tagged_with = {
            type: "any",
            values: [mainContactId],
            or: {
              main_contact: mainContactId,
            },
          };
        }
      } else {
        switch (state.pageObjectType) {
          case "groups":
            // console.log('Case Met 4')
            // Case 4
            // For nested projects within projects

            collectionsSetup.where.tagged_with = [state.project.id];

            collectionsSetup.where.id = {
              type: "not_equal",
              value: state.project.id,
            };

            break;
        }
      }
    }

    if (state.tool) {
      if (state.tool.hasOwnProperty("type")) {
        if (state.tool.type) {
          if (state.tool.type === "projectTool") {
            if (state.parent) {
              if (state.parent.hasOwnProperty("name")) {
                if (state.parent.name) {
                  if (state.parent.object_bp_type === "contact_types") {
                    // console.log('Case Met 5')
                    // Case 5
                    // For project tool on contacts

                    mainContactId = state.entity;

                    collectionsSetup.where.tagged_with = {
                      type: "any",
                      values: [mainContactId],
                      or: {
                        main_contact: mainContactId,
                      },
                    };
                  } else if (
                    state.parent.object_bp_type === "company_categories"
                  ) {
                    if (companyObj.pageObjectType === "companies") {
                      // console.log('Case Met 6')
                      // Case 6
                      // For project tool on companies

                      collectionsSetup.where.tagged_with = [
                        companyObj.objectId,
                      ];
                    }
                  }
                }
              }
            } else if (state.entity) {
              // console.log('Case Met 7')
              // Case 7
              // For project tool on contacts (in some random cases)

              mainContactId = state.entity;

              collectionsSetup.where.tagged_with = {
                type: "any",
                values: [mainContactId],
                or: {
                  main_contact: mainContactId,
                },
              };
            }
          }
        }
      }
    }

    // In the projects tool within records, make sure we are filtering by the
    // object.
    if (
      _.isEmpty(collectionsSetup.where.tagged_with) &&
      typeof state.entity === "number"
    ) {
      collectionsSetup.where.tagged_with = [state.entity];
    }

    // Build the collection
    sb.notify({
      type: "show-collection",
      data: collectionsSetup,
    });
  }

  // settings views
  function projectTypesView(dom, loadSingle) {
    function editView(dom, type) {
      var create = !type.hasOwnProperty("id");
      var selectedType = !_.isEmpty(type.type) ? type.type : "project";
      // details form
      var formArgs = {
        name: {
          name: "name",
          type: "text",
          label: "Name",
          value: type.name || "",
          placeholder: "Design, contract, etc.",
        },
        type: {
          name: "type",
          type: "check",
          label:
            'Are these events <div style="display:inline-block;" data-tooltip="Events have head counts, which can be used when planning inventory." data-inverted=""><i class="blue circle question icon"></i></div>',
          options: [
            {
              name: "type",
              label: "Yes",
              value: "event",
              checked: type.type === "event",
            },
          ],
          onChange: function (checked) {
            if (checked) {
              selectedType = "event";
            } else {
              selectedType = "project";
            }
          },
        },
        onFirstSignature: {
          name: "onFirstSignature",
          label: "When the first contract is signed, transition to:",
          type: "select",
          value: type.onFirstSignature || 0,
          options: _.map(type.states, function (state) {
            return {
              value: state.uid,
              name: state.name,
            };
          }),
        },
        onFirstFullPayment: {
          name: "onFirstFullPayment",
          label: "When the first payment is made in full, transition to:",
          type: "select",
          value: type.onFirstFullPayment || 0,
          options: _.map(type.states, function (state) {
            return {
              value: state.uid,
              name: state.name,
            };
          }),
        },
      };

      function saveProjectType(dom, projectType, callback) {
        var formData = dom.detailsForm.process().fields;

        if (projectType.hasOwnProperty("id")) {
          sb.data.db.obj.update(
            "project_types",
            {
              id: projectType.id,
              states: projectType.states,
              name: formData.name.value,
              type: selectedType,
              onFirstSignature: parseInt(formData.onFirstSignature.value),
              onFirstFullPayment: parseInt(formData.onFirstFullPayment.value),
            },
            function (response) {
              callback(response);
            }
          );
        } else {
          sb.data.db.obj.create(
            "project_types",
            {
              states: projectType.states,
              name: formData.name.value,
              type: selectedType,
              onFirstSignature: parseInt(formData.onFirstSignature.value),
              onFirstFullPayment: parseInt(formData.onFirstFullPayment.value),
            },
            function (response) {
              callback(response);
            }
          );
        }
      }

      // default values
      if (create) {
        type.states = [
          {
            uid: 1,
            name: "Backlog",
            icon: "",
            previous: [],
            next: [2],
            isEntryPoint: 1,
          },
          {
            uid: 2,
            name: "In progress",
            icon: "",
            previous: [1],
            next: [3],
          },
          {
            uid: 3,
            name: "Done",
            icon: "",
            previous: [2],
            next: [],
          },
        ];
      }

      dom.empty();

      dom.makeNode("detailsForm", "form", formArgs);

      // states
      dom.makeNode("br1", "div", { text: "<br />" });
      dom.makeNode("states", "div", { css: "text-center" });

      sb.notify({
        type: "show-workflow-states-view",
        data: {
          dom: dom.states,
          state: {
            header:
              'Project flow<div class="sub header">Define this project type\'s workflow in your system.</div>',
            object: type,
          },
        },
      });

      // buttons
      dom.makeNode("br2", "div", { text: "<br />" });
      dom
        .makeNode("btns", "div", {
          css: "ui buttons",
        })
        .makeNode("save", "button", {
          css: "ui green button",
          text: "Save",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function () {
                dom.btns.save.loading();
                saveProjectType(dom, type, function (response) {
                  if (response) {
                    sb.dom.alerts.alert("Saved!", "", "success");

                    if (loadSingle.hasOwnProperty("object_bp_type")) {
                      sb.notify({
                        type: "app-navigate-to",
                        data: { type: "UP" },
                      });
                    } else {
                      projectTypesView(dom, loadSingle);
                    }
                  } else {
                    sb.dom.alerts.alert(
                      "Oops!",
                      "Something went wrong -- refresh and try again.",
                      "error"
                    );

                    dom.btns.save.loading(false);
                  }
                });
              },
            },
          },
          sb.moduleId
        );

      dom.btns
        .makeNode("back", "div", {
          css: "ui orange button",
          text: "Cancel",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function () {
                if (loadSingle.hasOwnProperty("object_bp_type")) {
                  sb.notify({ type: "app-navigate-to", data: { type: "UP" } });
                } else {
                  projectTypesView(dom, loadSingle);
                }
              },
            },
          },
          sb.moduleId
        );

      if (!create) {
        dom.btns
          .makeNode("delete", "button", {
            css: "ui red button",
            text: "Delete",
          })
          .notify(
            "click",
            {
              type: "headquarters-run",
              data: {
                run: function () {
                  //!TODO: check if projects are using this type->only archive if they are
                  sb.dom.alerts.ask(
                    {
                      title: "Are you sure?",
                      text: "",
                    },
                    function (resp) {
                      if (resp) {
                        dom.btns.delete.loading(true);
                        sb.data.db.obj.erase(
                          "project_types",
                          type.id,
                          function (response) {
                            if (response) {
                              sb.dom.alerts.alert("Deleted!", "", "success");
                              projectTypesView(dom, loadSingle);
                            } else {
                              sb.dom.alerts.alert(
                                "Oops!",
                                "An error occurred -- please refresh and try again.",
                                "error"
                              );
                            }
                          }
                        );
                      }
                    }
                  );
                },
              },
            },
            sb.moduleId
          );
      }

      dom.patch();
    }

    if (loadSingle.object_bp_type) {
      editView(dom, loadSingle);
    } else {
      sb.data.db.obj.getAll("project_types", function (project_types) {
        if (loadSingle.object_bp_type) {
          editView(dom, project_types[0]);
        } else {
          dom.empty();

          dom
            .makeNode("btns", "div", {
              css: "ui right floated buttons",
            })
            .makeNode("create", "div", {
              tag: "button",
              css: "ui green button",
              text: "New project type",
            })
            .notify(
              "click",
              {
                type: "headquarters-run",
                data: {
                  run: function () {
                    editView(dom, {});
                  },
                },
              },
              sb.moduleId
            );

          // table
          dom.makeNode("br-before-table", "div", { text: "<br /><br />" });
          dom
            .makeNode("table", "div", { tag: "table", css: "ui basic table" })
            .makeNode("thead", "div", { tag: "thead" });
          dom.table.thead
            .makeNode("tr", "div", { tag: "tr" })
            .makeNode("name", "div", { tag: "th", text: "Name" });
          dom.table.thead.tr.makeNode("btns", "div", { tag: "th" });
          dom.table.makeNode("body", "div", { tag: "tbody" });

          _.each(project_types, function (project_type) {
            // type name
            dom.table.body
              .makeNode("type-" + project_type.id, "div", { tag: "tr" })
              .makeNode("name", "div", { tag: "td", text: project_type.name });

            // type link
            dom.table.body["type-" + project_type.id]
              .makeNode("btns", "div", { tag: "td" })
              .makeNode("open", "div", {
                tag: "button",
                css: "ui tiny yellow button",
                text: "Edit",
              })
              .notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function (typeObj) {
                      editView(dom, typeObj);
                    }.bind({}, project_type),
                  },
                },
                sb.moduleId
              );
          });

          dom.patch();
        }
      });
    }
  }

  function toolSettingsView(dom) {
    comps.settings = sb.createComponent("crud-table");

    function defaultSettingsView(dom, settings, tool) {
      dom.css("ui grid");

      dom.makeNode("settingsNav", "column", { w: 16 });
      dom.settingsNav.makeNode("title", "div", {
        css: "ui header",
        text: tool.name + " Settings",
      });
      dom.settingsNav.makeNode("cont", "container", {
        css: "ui secondary pointing menu",
      });
      dom
        .makeNode("settings", "column", { w: 16 })
        .makeNode("cont", "div", { css: "" });

      dom.settings.cont.makeNode("startText", "headerText", {
        text: "<br />Please choose a setting to edit",
        size: "xx-small",
        css: "text-center",
      });

      _.each(_.sortBy(settings, "name"), function (obj) {
        dom.settingsNav.cont
          .makeNode("nav-" + obj.object_type, "div", {
            text: obj.name,
            css: "ui item",
          })
          .notify(
            "click",
            {
              type: "crud-table-run",
              data: {
                run: displaySetting.bind(this, dom, obj, settings),
              },
            },
            sb.moduleId
          );
      });

      dom.patch();

      displaySetting(dom, _.sortBy(settings, "name")[0], settings);
    }

    function displaySetting(dom, obj, settings) {
      delete dom.settings.cont.startText;

      sb.data.db.obj.getBlueprint(obj.object_type, function (bp) {
        if (obj.action) {
          dom.settings.cont.patch();

          obj.action(dom.settings.cont, bp, settings);
        } else {
          var visibleCols = {};

          _.each(bp, function (fieldObj, fieldName) {
            if (fieldObj.immutable == false) {
              visibleCols[fieldName] = fieldObj.name;
            }
          });

          var rowLink = {
            type: "edit",
            header: function (obj) {},
            action: "edit",
          };

          if (obj.formObjs) {
            rowLink.formObjs = obj.formObjs;
          }

          comps.settings.notify({
            type: "show-table",
            data: {
              domObj: dom.settings.cont,
              tableTitle: obj.name,
              settingsTable: true,
              navigation: false,
              objectType: obj.object_type,
              searchObjects: false,
              filters: false,
              download: false,
              headerButtons: {
                reload: {
                  name: "Reload",
                  css: "pda-btn-blue",
                  action: function () {},
                },
                create: {
                  name: '<i class="fa fa-plus"></i> Create New',
                  css: "pda-btn-green",
                  domType: "full",
                  action: "create",
                },
              },
              calendar: false,
              rowSelection: true,
              rowLink: rowLink,
              multiSelectButtons: {
                erase: {
                  name: '<i class="fa fa-trash-o"></i> Delete',
                  css: "pda-btn-red",
                  domType: "default",
                  action: "erase",
                },
              },
              visibleCols: visibleCols,
              searchObjects: false,
              dateRange: false,
              home: false,
              settings: false,
              cells: {},
              childObjs: 1,
              data: function (paged, callback) {
                sb.data.db.obj.getAll(
                  obj.object_type,
                  function (ret) {
                    callback(ret);
                  },
                  1,
                  paged
                );
              },
            },
          });
        }
      });
    }

    dom.empty();

    dom
      .makeNode("cont", "div", { css: "ui basic segment" })
      .makeNode("cols", "div", { css: "ui divided items" });

    _.each(appConfig.projectTools, function (tool) {
      if (tool.settings) {
        var content = dom.cont.cols
          .makeNode("tool-" + tool.id, "div", { css: "item" })
          .makeNode("segment", "div", { css: "content" });

        content
          .makeNode("button", "div", {
            css: "ui right floated yellow button",
            text: "Edit",
          })
          .notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function () {
                  dom.empty();

                  defaultSettingsView(dom, tool.settings, tool);
                },
              },
            },
            sb.moduleId
          );

        content.makeNode("header", "div", {
          css: "header",
          text:
            '<i class="circular ' +
            tool.icon.color +
            " " +
            tool.icon.type +
            ' icon"></i> ' +
            tool.name,
        });
      }
    });

    dom.patch();
  }

  function categoriesSettings(ui, obj) {
    function createType(ui, tempObj, onComplete) {
      var formSetup = {
        name: {
          name: "name",
          type: "text",
          placeholder: "Category",
        },
      };

      function displayMsg(ui, msgHeader, msgbody, msgType, show) {
        var isShown = "hidden";

        if (show === true) {
          isShown = "visible";
        } else {
          isShown = "hidden";
        }

        ui.empty();

        ui.makeNode("msg", "div", {
          css: "ui " + msgType + " " + isShown + " message",
        });

        ui.msg.makeNode("header", "div", {
          css: "header",
          text: msgHeader,
        });

        ui.msg.makeNode("body", "div", {
          tag: "p",
          text: msgbody,
        });

        ui.patch();
      }

      function processForm(form, beforeProcess, afterProcess) {
        var formData = form.process().fields;
        var newObj = {};

        function validateForm(data, callback) {
          if (data.name.value === "") {
            callback(false);
          } else {
            callback(true);
          }
        }

        validateForm(formData, function (res) {
          if (res) {
            beforeProcess();

            newObj.name = formData.name.value;

            sb.data.db.obj.create("categories", newObj, function (created) {
              afterProcess(created);
            });
          } else {
            displayMsg(
              ui.wrap.msgArea,
              "Form is incomplete",
              "You must enter a schedule type to continue",
              "warning",
              true
            );
          }
        });
      }

      ui.empty();

      ui.makeNode("wrap", "div", {});

      ui.wrap.makeNode("head", "div", {});
      ui.wrap.makeNode("lb_1", "lineBreak", { spaces: 1 });
      ui.wrap.makeNode("msgArea", "div", {});
      ui.wrap.makeNode("body", "div", {});

      ui.wrap.head.makeNode("grid", "div", {
        css: "ui grid",
      });

      ui.wrap.head.grid.makeNode("col1", "div", {
        css: "eight wide column",
      });
      ui.wrap.head.grid.makeNode("col2", "div", {
        css: "eight wide column",
      });

      ui.wrap.head.grid.col1.makeNode("title", "div", {
        tag: "h2",
        css: "ui header",
        text: "Create a new category",
      });

      ui.wrap.head.grid.col2.makeNode("btnGrp", "div", {});

      ui.wrap.head.grid.col2.btnGrp
        .makeNode("save", "div", {
          css: "ui green button right floated",
          text: "Save",
        })
        .notify(
          "click",
          {
            type: "staffComponent-run",
            data: {
              run: function (data) {
                processForm(
                  ui.wrap.body.form,
                  function () {
                    ui.wrap.head.grid.col2.btnGrp.save.loading();

                    ui.wrap.body.empty();
                    ui.wrap.body.makeNode("seg", "div", {
                      css: "ui basic loading segment",
                    });
                    ui.wrap.body.seg.makeNode("lb", "lineBreak", { spaces: 1 });
                    ui.wrap.body.makeNode("loadingText", "div", {
                      text: "Processing form...",
                      css: "text-center",
                    });
                    ui.wrap.body.patch();
                  },
                  function (res) {
                    if (res) {
                      onComplete(res);
                    }
                  }
                );
              },
            },
          },
          sb.moduleId
        );

      ui.wrap.body.makeNode("form", "form", formSetup);

      ui.patch();
    }

    ui.empty();
    ui.makeNode("wrap", "div", {});
    ui.patch();

    sb.notify({
      type: "show-collection",
      data: {
        actions: {
          create: function (ui, tempObj, onComplete) {
            createType(ui, tempObj, onComplete);
          },
          view: false,
          navigateTo: false,
          copy: false,
        },
        domObj: ui.wrap,
        fields: {
          name: {
            title: "Name",
            type: "title",
            options: {
              link: false,
            },
          },
        },
        menu: {
          subviews: {
            table: true,
          },
        },
        groupings: {},
        objectType: "categories",
        where: {
          childObjs: {
            name: true,
          },
        },
      },
    });
  }

  // helper functions

  function build_ToolsDashboard(dom, setup) {
    // setup.toolList
    // setup.isManager
    // setup.systemTools
    // setup.state
    // setup.draw
    // setup.mainDom
    // setup.layer

    var toolList = setup.toolList;
    var isManager = setup.isManager;
    var systemTools = setup.systemTools;
    var openContainer = "collapse";
    var iconMenuClass = "icon";
    var itemMargin = "margin: 5px 2%;";
    var state = setup.state;
    var isMobile = false;
    var active = "";
    var mainDom = setup.mainDom;
    var draw = setup.draw;
    var layer = setup.layer;
    var systemToolRef = {};
    var pageObject = {};
    var hqManager = true;

    function build_toolView(ui, state, tool, systemTool) {
      var href = "";
      var fieldClass = "";

      function build_toolName(ui, state, systemTool) {
        var edit = false;

        if (hqManager) {
          edit = true;
        } else {
          edit = false;
        }

        sb.notify({
          type: "view-field",
          data: {
            type: "title",
            property: "display_name",
            obj: systemTool,
            options: {
              tag: "h2",
              edit: edit,
              update: function (obj, fieldName, value, callback) {
                _.findWhere(pageObject.tools, {
                  system_name: obj.system_name,
                }).display_name = value;

                sb.data.db.obj.update(
                  "groups",
                  {
                    id: pageObject.id,
                    tools: pageObject.tools,
                  },
                  function (updated) {
                    var updatedTool = _.findWhere(updated.tools, {
                      system_name: obj.system_name,
                    });

                    callback(updatedTool);

                    build_toolButton(updatedTool, tool, value);

                    $(dom.wrapper.menu["item" + tool.id].selector).html(
                      '<i class="large ' +
                        tool.icon.color +
                        " " +
                        tool.icon.type +
                        ' icon"></i>' +
                        value
                    );
                  },
                  1
                );
              },
            },
            ui: ui,
          },
        });

        ui.patch();
      }

      function build_toolTip(ui, state, systemTool) {
        var edit = false;

        if (hqManager) {
          edit = true;
        } else {
          edit = false;
        }

        sb.notify({
          type: "view-field",
          data: {
            type: "detail",
            property: "tip",
            obj: systemTool,
            ui: ui,
            options: {
              edit: edit,
              update: function (obj, fieldName, value, callback) {
                var updatedTool = (_.findWhere(pageObject.tools, {
                  system_name: obj.system_name,
                }).tip = value);

                sb.data.db.obj.update(
                  "groups",
                  { id: pageObject.id, tools: pageObject.tools },
                  function (updated) {
                    callback(
                      _.findWhere(updated.tools, {
                        system_name: obj.system_name,
                      })
                    );
                  },
                  1
                );
              },
            },
          },
        });

        ui.patch();
      }

      if (layer === "headquarters") {
        href = sb.data.url.createPageURL("hqTool", {
          tool: systemTool.system_name,
        });
      } else if (layer === "team") {
        href = sb.data.url.createPageURL("teamTool", {
          tool: systemTool.system_name,
        });
      }

      if (hqManager) {
        fieldClass = "field";
      } else {
        fieldClass = "";
      }

      ui.empty();

      ui.makeNode("divider1", "div", {
        css: "ui divider",
      });

      ui.makeNode("head", "div", {
        css: "ui grid",
      });
      ui.makeNode("lb_1", "lineBreak", { spaces: 1 });
      ui.makeNode("body", "div", {
        walkthrough: {
          name: "welcome",
          step: "08-outro",
        },
      });

      ui.head.makeNode("col1", "div", {
        css: "fourteen wide column",
      });
      ui.head.makeNode("col2", "div", {
        css: "two wide column",
      });

      ui.head.col1.makeNode("toolName", "div", {
        css: fieldClass,
      });

      ui.head.col1.makeNode("tip", "div", {
        css: fieldClass,
      });

      build_toolName(ui.head.col1.toolName, state, systemTool);
      build_toolTip(ui.head.col1.tip, state, systemTool);

      ui.head.col2.makeNode("fullscreen", "div", {
        tag: "a",
        css: "ui icon button right floated",
        style: "background: none !important; color: silver !important;",
        text: '<i class="large expand arrows alternate icon"></i>',
        href: href,
        tooltip: {
          title: "Expand",
          text: "Click to Expand to Full Window View",
          position: "left center",
        },
        listener: {
          type: "popup",
          hoverable: true,
        },
      });

      ui.head.patch();

      tool.mainViews[0].dom(ui.body, state, function (toolDom) {
        if (toolDom.dom) {
          toolDom.dom.patch();

          toolDom.after(toolDom.dom);
        } else if (toolDom !== false) {
          if (toolDom.build) {
            toolDom.build();
          } else {
            toolDom.patch();
          }
        }
      });
    }

    function build_toolButton(sysTool, tool, toolTitle) {
      var name = "";
      var href = "";

      if (toolTitle === undefined) {
        name = tool.display_name;
      } else {
        name = toolTitle;
      }

      if (isMobile) {
        if (layer === "headquarters") {
          href = sb.data.url.createPageURL("hqTool", {
            tool: tool.system_name,
          });
        } else if (layer === "team") {
          href = sb.data.url.createPageURL("teamTool", {
            tool: tool.system_name,
          });
        }
      } else {
        href = "";
      }

      var btnOptions = {
        tag: "a",
        css: "toolMenuItem item " + active,
        style: "width: 12.5%; " + itemMargin,
        text:
          '<i class="large ' +
          tool.icon.color +
          " " +
          tool.icon.type +
          ' icon"></i>' +
          name,
        tooltip: {
          title: name,
          text: tool.tip.replace(/(<([^>]+)>)/gi, ""),
          position: "bottom center",
        },
        href: href,
        listener: {
          type: "popup",
          hoverable: true,
        },
      };

      if (sysTool.id == "projectTool") {
        btnOptions.walkthrough = {
          name: "welcome",
          step: "06-hq-projects",
        };
      }

      if (sysTool.id == "teamTool") {
        btnOptions.walkthrough = {
          name: "welcome",
          step: "07-hq-teams",
        };
      }

      dom.wrapper.menu.makeNode("item" + sysTool.id, "div", btnOptions).notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (data) {
              $(".toolMenuItem").removeClass("active");
              $(".toolMenuItem").css("color", "black");

              $(data.selector).addClass("active");
              $(data.selector).css("color", "#027eff");

              if (isMobile === false) {
                build_toolView(dom.wrapper.toolView, state, sysTool, tool);
              }
            },
            selector: dom.wrapper.menu["item" + sysTool.id].selector,
          },
        },
        sb.moduleId
      );
    }

    if (layer === "headquarters") {
      systemToolRef = appConfig.hqTools;
      pageObject = state.headquarters;
    } else if (layer === "team") {
      systemToolRef = appConfig.teamTools;
      pageObject = state.team;

      hqManager = _.contains(
        state.headquarters.managers,
        +sb.data.cookie.get("uid")
      );
    }

    if ($(window).width() <= 768) {
      isMobile = true;
      iconMenuClass = "icon";
      itemMargin = "margin-left: 6%;";
    } else {
      isMobile = false;
      itemMargin = "margin: 5px 2%;";
    }

    dom.makeNode("lb_1", "lineBreak", { spaces: 1 });

    dom.makeNode("wrapper", "div", {});

    dom.wrapper.makeNode("menu", "div", {
      css: "ui " + iconMenuClass + " labeled secondary menu",
      style:
        "display: flex !important; flex-direction: row !important; flex-wrap: wrap !important; margin-left: 0 !important; margin-right: 0 !important;",
      walkthrough: {
        name: "welcome",
        step: "04-hq-tools",
      },
    });

    dom.wrapper.makeNode("toolView", "div", {});

    if (!_.isEmpty(toolList)) {
      _.each(_.sortBy(toolList, "display_name"), function (tool) {
        _.map(systemTools, function (sysTool) {
          var allowed = [];
          var canViewTool = false;

          if (tool.allowed_users) {
            allowed = _.pluck(tool.allowed_users, "id");
          }

          if (allowed.length == 0) {
            allowed.push(+sb.data.cookie.get("uid"));
          }

          if (
            isManager === true ||
            allowed.indexOf(+sb.data.cookie.get("uid")) > -1
          ) {
            canViewTool = true;
          }

          if (sysTool.id == tool.system_name && canViewTool) {
            if (!tool.tip) {
              tool.tip = _.findWhere(systemToolRef, {
                id: tool.system_name,
              }).tip;
            }

            tool.newButton = _.findWhere(systemToolRef, {
              id: tool.system_name,
            }).newButton;
            tool.icon = _.findWhere(systemToolRef, {
              id: tool.system_name,
            }).icon;

            if (tool.is_archieved != true) {
              if (tool.system_name === "projectTool") {
                if (isMobile !== true) {
                  active = "active";
                }
              } else {
                active = "";
              }

              build_toolButton(sysTool, tool);
            }
          }
        });
      });
    }

    if (_.findWhere(toolList, { is_archieved: 0 })) {
      // If project tool is not included --> find the next selected tool and display it
      var toolDefault = _.findWhere(toolList, { system_name: "projectTool" });

      if (!_.isUndefined(toolDefault) && toolDefault.is_archieved === 1) {
        toolList = _.reject(toolList, function (o) {
          return (
            o.system_name === "contactTool" ||
            o.system_name === "clientsTool" ||
            o.system_name === "cms"
          );
        });

        var objTool = _.find(toolList, function (o) {
          if (o.is_archieved !== 1) {
            return o;
          }
        });

        var sysTool = _.findWhere(systemTools, { id: objTool.system_name });
        var href = "";
        var activeClass = "";

        if (isMobile) {
          activeClass = "";

          if (layer === "headquarters") {
            href = sb.data.url.createPageURL("hqTool", {
              tool: objTool.system_name,
            });
          } else if (layer === "team") {
            href = sb.data.url.createPageURL("teamTool", {
              tool: objTool.system_name,
            });
          }
        } else {
          activeClass = "active";

          if (!_.isUndefined(objTool))
            build_toolView(dom.wrapper.toolView, state, sysTool, objTool);
        }

        dom.wrapper.menu
          .makeNode("item" + sysTool.id, "div", {
            tag: "a",
            css: "toolMenuItem item " + activeClass,
            style: "width: 12.5%; " + itemMargin,
            text:
              '<i class="large ' +
              sysTool.icon.color +
              " " +
              sysTool.icon.type +
              ' icon"></i>' +
              sysTool.name,
            tooltip: {
              title: sysTool.name,
              text: objTool.tip.replace(/(<([^>]+)>)/gi, ""),
              position: "bottom center",
            },
            href: href,
            listener: {
              type: "popup",
              hoverable: true,
            },
          })
          .notify(
            "click",
            {
              type: "headquarters-run",
              data: {
                run: function (data) {
                  $(".toolMenuItem").removeClass("active");
                  $(".toolMenuItem").css("color", "black");

                  $(data.selector).addClass("active");
                  $(data.selector).css("color", "#027eff");

                  if (isMobile === false) {
                    build_toolView(
                      dom.wrapper.toolView,
                      state,
                      sysTool,
                      objTool
                    );
                  }
                },
                selector: dom.wrapper.menu["item" + sysTool.id].selector,
              },
            },
            sb.moduleId
          );
      } else {
        if (isMobile === false) {
          if (!_.isUndefined(toolDefault))
            build_toolView(
              dom.wrapper.toolView,
              state,
              _.findWhere(systemTools, { id: "projectTool" }),
              toolDefault
            );
        }
      }
    }

    if (toolList.length) {
      if (hqManager) {
        switch (pageObject.group_type) {
          case "Team":
            break;
            if (
              !sb.permissions.isGroupManager(+sb.data.cookie.userId, pageObject)
            ) {
              break;
            }

          default:
            dom.wrapper.menu
              .makeNode("itemAdd", "div", {
                tag: "a",
                css: "item",
                style: "width: 12.5%; " + itemMargin,
                text: '<i class="large disabled grey cog icon"></i> Manage Apps',
                walkthrough: {
                  name: "welcome",
                  step: "05-flex-tools",
                },
              })
              .notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function (data) {
                      if (layer === "headquarters") {
                        manageHQTools(mainDom, state, draw);
                      } else if (layer === "team") {
                        manageTeamTools(mainDom, state, draw);
                      }
                    },
                  },
                },
                sb.moduleId
              );
            break;
        }
      }
    }
  }

  function ui_topNav(dom, domCont, state, draw, groupType) {
    /*
var totalButtons = 'three';
		if(state[groupType].managers.indexOf(+sb.data.cookie.userId) > -1){
			totalButtons = 'four';
		}

		dom.makeNode('navCol', 'div', {css:'ui centered stackable grid'})
			.makeNode('nav', 'div', {css:'five wide column'});

		dom.navCol.nav.makeNode('navigation', 'div', {css:' ui teal fluid '+ totalButtons +' item secondary pointing tiny menu'});

		dom.navCol.nav.navigation.makeNode('home', 'div', {text:'HQ', css:'active item', tag:'a'})
			.makeNode('icon', 'div', {css:'building icon', tag:'i'});
		dom.navCol.nav.navigation.makeNode('dashboard', 'div', {text:'My Stuff', css:'item', tag:'a'})
			.makeNode('icon', 'div', {css:'compass outline icon', tag:'i'});
		dom.navCol.nav.navigation.makeNode('profile', 'div', {text:'Me', css:'item', tag:'a'})
			.makeNode('icon', 'div', {css:'user outline icon', tag:'i'});

		if(totalButtons == 'four'){

			dom.navCol.nav.navigation.makeNode('admin', 'div', {text:'Admin', css:'item', tag:'a'})
				.makeNode('icon', 'div', {css:'cogs icon', tag:'i'});

		}

		dom.navCol.nav.navigation.home.notify('click', {
			type:'headquarters-run',
			data:{
				run:function(domCont){

					domCont.css('ui loading basic segment');

					dom.navCol.nav.navigation.home.css('active item');
					dom.navCol.nav.navigation.dashboard.css('item');
					dom.navCol.nav.navigation.profile.css('item');

					if(totalButtons == 'four')
						dom.navCol.nav.navigation.admin.css('item');

					ui_topNav(dom, domCont, state, draw, groupType);

				}.bind({}, domCont)
			}
		}, sb.moduleId);

		dom.navCol.nav.navigation.dashboard.notify('click', {
			type:'headquarters-run',
			data:{
				run:function(dom, domCont, state, draw, groupType){

					dom.navCol.nav.navigation.home.css('item');
					dom.navCol.nav.navigation.dashboard.css('active item');
					dom.navCol.nav.navigation.profile.css('item');

					if(totalButtons == 'four')
						dom.navCol.nav.navigation.admin.css('item');

					domCont.css('ui loading basic segment');

					dashboardView(domCont, state, draw, function(done){

						domCont.css('ui basic segment');

					});

				}.bind({}, dom, domCont, state, draw, groupType)
			}
		}, sb.moduleId);

		dom.navCol.nav.navigation.profile.notify('click', {
			type:'headquarters-run',
			data:{
				run:function(dom, domCont, state, draw, groupType){

					dom.navCol.nav.navigation.home.css('item');
					dom.navCol.nav.navigation.dashboard.css('item');
					dom.navCol.nav.navigation.profile.css('active item');

					if(totalButtons == 'four')
						dom.navCol.nav.navigation.admin.css('item');

					domCont.css('ui loading basic segment');

					profileView(domCont, state, draw, function(done){

						domCont.css('ui basic segment');

					});

				}.bind({}, dom, domCont, state, draw, groupType)
			}
		}, sb.moduleId);

		if(totalButtons == 'four'){

			dom.navCol.nav.navigation.admin.notify('click', {
				type:'headquarters-run',
				data:{
					run:function(dom, domCont, state, draw, groupType){

						dom.navCol.nav.navigation.home.css('item');
						dom.navCol.nav.navigation.dashboard.css('item');
						dom.navCol.nav.navigation.profile.css('item');
						dom.navCol.nav.navigation.admin.css('active item');

						domCont.css('ui loading basic segment');

						adminView(domCont, state, draw, function(done){

							domCont.css('ui basic segment');

						});

					}.bind({}, dom, domCont, state, draw, groupType)
				}
			}, sb.moduleId);

		}
*/

    dom.patch();

    switch (groupType) {
      case "headquarters":
        headquartersView(domCont, state, draw, function (done) {
          //domCont.css('');
        });

        break;
    }
  }

  function ui_moveGroup(dom, obj, state, onComplete) {
    function form_ui(dom, obj) {
      function processForm(form, callback, selectedParent) {
        var relationProperty = "parent";
        if (state.hasOwnProperty("parentProperty")) {
          relationProperty = state.parentProperty;
        }

        sb.data.db.obj.getWhere(
          "groups",
          { parent: state.currentGroupObject.id, childObjs: 2 },
          function (nestedGroups) {
            var queryObject = {
              id: selectedParent,
              childObjs: 2,
            };
            if (queryObject.id == 0) {
              queryObject = {
                parent: 0,
                childObjs: 2,
              };
            }

            sb.data.db.obj.getWhere(
              "groups",
              queryObject,
              function (newGroupLocation) {
                var updatedObject = [
                  {
                    id: obj.id,
                    name: obj.name,
                    object_bp_type: obj.object_bp_type,
                    [relationProperty]: selectedParent,
                    tagged_with: newGroupLocation[0].tagged_with,
                  },
                ];
                var child = _.findWhere(nestedGroups, {
                  id: updatedObject[0].parent,
                });

                if (child != undefined) {
                  updatedObject.unshift({
                    id: child.id,
                    name: child.name,
                    object_bp_type: child.object_bp_type,
                    parent: state.currentGroupObject.parent,
                    tagged_with: state.currentGroupObject.tagged_with,
                  });
                }

                updatedObject[0].tagged_with.push(newGroupLocation[0].id);

                callback(updatedObject, newGroupLocation);
              }
            );
          }
        );
      }

      function moveGroup(updatedGroupObject, newGroupLocation) {
        function update_data(callback) {
          sb.data.db.obj.update(
            "groups",
            updatedGroupObject,
            function (response) {
              callback(response);
            }
          );
        }

        function navigateToNewSpot(response) {
          if (typeof onComplete === "function") {
            if (response.length === 2) {
              onComplete(response[1], response[0]);
            } else if (response.length === 1) {
              onComplete(response[0], newGroupLocation);
            }
          }

          sb.notify({
            type: "app-navigate-to",
            data: {
              type: "UP",
            },
          });

          //!TODO: work out navigation to new place in system
          return;
          switch (obj.group_type) {
            case "Team":
              state.team = _.last(response);

              sb.notify({
                type: "app-navigate-to",
                data: {
                  itemId: "groups",
                  viewId: {
                    viewState: {
                      obj: state.team,
                    },
                  },
                },
              });

              break;

            case "Project":
              state.project = _.last(response);
              state.color = "gray";

              sb.notify({
                type: "app-navigate-to",
                data: {
                  itemId: "groups",
                  viewId: {
                    viewState: {
                      obj: state.project,
                    },
                  },
                },
              });

              break;
          }
        }

        var toolType = "teamTool";
        var needsTool = false;

        if (state.tool) {
          var projectTool = _.findWhere(newGroupLocation[0].tools, {
            system_name: state.tool,
          });

          toolType = state.tool;

          if (projectTool == undefined) {
            needsTool = true;
          } else {
            projectTool.is_archieved = 0;
            updatedGroupObject.unshift({
              id: newGroupLocation[0].id,
              tools: newGroupLocation[0].tools,
            });
          }
        } else if (obj.group_type == "Project") {
          var projectTool = _.findWhere(newGroupLocation[0].tools, {
            system_name: "projectTool",
          });

          toolType = "projectTool";

          if (projectTool == undefined) {
            needsTool = true;
          } else {
            projectTool.is_archieved = 0;
            updatedGroupObject.unshift(newGroupLocation[0]);
          }
        } else {
          var teamTool = _.findWhere(newGroupLocation[0].tools, {
            system_name: "teamTool",
          });

          if (teamTool == undefined) {
            needsTool = true;
          } else {
            teamTool.is_archieved = 0;
            updatedGroupObject.unshift(newGroupLocation[0]);
          }
        }

        if (needsTool) {
          addTeamSection(
            newGroupLocation[0],
            _.findWhere(appConfig.teamTools, { id: toolType }),
            function (newTeam) {
              update_data(navigateToNewSpot);
            }
          );
        } else {
          update_data(navigateToNewSpot);
        }

        state.currentGroupObject = {};
      }

      var left = dom
        .makeNode("wrapper", "div", {
          css: "ui stackable grid container",
          style: "min-height:500px;",
        })
        .makeNode("colOne", "div", {
          css: "ui five wide column",
        });

      var right = dom.wrapper.makeNode("colTwo", "div", {
        css: "ui eleven wide column",
      });

      var selectedParent;

      left.makeNode("tool", "div", { css: "ui card" });
      left.tool.makeNode("body", "div", { css: "content ui aligned" });
      left.tool.body.makeNode("seg", "div", { css: "ui basic yellow segment" });
      left.tool.body.seg.makeNode("header", "div", {
        css: "ui small header",
        text: obj.name,
      });
      left.tool.body.seg.makeNode("details", "div", {
        css: "description",
        text: obj.details,
      });

      right.makeNode("seg", "div", {
        css: "ui basic segment",
        style: "padding-top:0!important;",
      });
      right.seg.makeNode("header", "div", {
        css: "ui small dividing header",
        text: "Select a new location to move <b>" + obj.name + "</b> to.",
      });
      right.seg.makeNode("break", "div", { text: "<br />" });
      right.seg.makeNode("formCont", "div", { css: "ui stackable grid" });

      right.seg.formCont.makeNode("formCol", "div", {
        css: "ui thirteen wide column",
      });
      right.seg.formCont.makeNode("btnCol", "div", {
        css: "ui three wide column",
      });

      right.seg.formCont.btnCol.makeNode("btnGroup", "div", { css: "" });
      // 			right.seg.formCont.formCol.makeNode('form', 'form', formObj);
      right.seg.formCont.formCol.makeNode("moveToSelect", "div", {
        css: "ui fluid search item",
        text:
          '<div class="ui icon fluid input">' +
          '<input class="prompt" type="text" placeholder="Search..">' +
          '<i class="search icon"></i>' +
          "</div>" +
          '<div class="results"></div>',
        listener: {
          type: "search",
          objectType: "groups",
          onSelect: function (result, response) {
            if (!_.isEmpty(result)) {
              selectedParent = result.id;
            }
          },
          category: "group_type",
          onResponse: function (raw) {
            var response = {
              results: {},
            };

            _.each(raw.results, function (item) {
              if (
                (item.group_type !== "Headquarters" &&
                  item.group_type !== "Team" &&
                  item.group_type !== "Project") ||
                (obj.group_type === "Team" && item.group_type === "Project")
              ) {
                return;
              } else {
                if (response.results.hasOwnProperty(item.group_type)) {
                  response.results[item.group_type].results.push(item);
                } else {
                  response.results[item.group_type] = {
                    name: item.group_type,
                    results: [item],
                  };
                }
              }
            });

            return response;
          },
        },
      });

      right.seg.formCont.btnCol.btnGroup
        .makeNode("moveGroup", "button", {
          css: "ui large blue circular icon button",
          text: '<i class="arrow right icon"></i>',
        })
        .notify("click", {
          type: "headquarters-run",
          data: {
            run: function (dom, obj, state) {
              right.seg.formCont.btnCol.btnGroup.moveGroup.loading();
              processForm(
                dom.wrapper.colTwo.seg.formCont.formCol.form,
                moveGroup,
                selectedParent
              );
            }.bind({}, dom, obj, state),
          },
        });

      dom.patch();
    }

    if (!state.hasOwnProperty("currentGroupObject")) {
      state.currentGroupObject = {};

      if (obj.parent != null) {
        state.currentGroupObject.parent = obj.parent.id;
      } else {
        state.currentGroupObject.parent = 0;
      }

      state.currentGroupObject.name = obj.name;
    } else if (
      !state.hasOwnProperty("currentGroupObject") &&
      (obj.parent == null || obj.parent == 0) &&
      state.headquarters
    ) {
      state.currentGroupObject.parent = state.headquarters.id;
      state.currentGroupObject.name = state.headquarters.name;
    } else {
      state.currentGroupObject = {};
    }

    var queryObj = {
      parent: 0,
      group_type: "Team",
      childObjs: 2,
    };

    state.currentGroupObject = {
      id: obj.id,
    };

    if (obj.parent == null || obj.parent == 0) {
      state.currentGroupObject.parent = 0;
    } else {
      state.currentGroupObject.parent = obj.parent.id;
    }

    dom.makeNode("loader", "div", { css: "ui basic loading segment" });
    dom.patch();

    dom.empty();

    form_ui(dom, obj);
  }
  function ui_scheduleEmails(dom, obj, state, onComplete) {
    function actionList_ui(ui, obj) {
      // Header
      ui.makeNode("c", "div", { css: "ui basic segment" });

      ui.c.makeNode("name", "div", {
        text:
          '<h2 class="ui header">' +
          '<i class="envelope icon"></i>' +
          '<div class="content">' +
          "Schedule Emails" +
          "<div class=\"sub header\">Create and schedule 'Send Email' Actions to Team Members or Contacts</div>" +
          "</div>" +
          "</h2>",
      });

      ui.c.makeNode("lb", "lineBreak", { spaces: 2 });

      ui.c.makeNode("a-h", "div", {
        tag: "h4",
        css: "ui header",
        text: '<i class="ui grey bolt icon"></i> Actions that run when scheduled',
      });
      ui.c.makeNode("actions", "div", { text: "actions container" });

      ui.patch();

      sb.notify({
        type: "view-actions",
        data: {
          ui: ui.c.actions,
          state: {
            object: obj,
            state: {
              id: 1,
              uid: 1,
              allowAllTransitions: null,
              color: null,
              icon: "envelope",
              isEntryPoint: 1,
              message: null,
              name: "Schedule Email",
              next: [],
              previous: [],
              requiresConfirmation: null,
              shouldTransitionOnTaskComplete: null,
              tags: null,
              type: null,
              type_name: null,
            },
            options: {
              actionOptions: {
                include: ["sendEmail"],
              },
              createOptions: {
                trigger: "spaceSchedule",
              },
              toggleOptionsForm: {
                bcc: false,
                cc: false,
                sendDocumentAsLink: false,
                sendTo: false,
                _availableToUser: false,
                _requiresInput: false,
                startAfterQuantity: false,
                endsAfterQuantity: false,
              },
            },
          },
        },
      });

      ui.patch();
    }

    dom.makeNode("loader", "div", { css: "ui basic loading segment" });
    dom.patch();
    dom.empty();

    actionList_ui(dom, obj);
  }

  function checkSettingsObject(callback) {
    sb.data.db.obj.getAll("workorder_system", function (settingsObj) {
      if (settingsObj.length == 0) {
        var settingsObj = {
          signature_disclaimer: defaultDisclaimer,
          request_email: defaultRequestEmail,
          request_email_subject: defaultRequestEmailSubject,
          default_opp_note: defaultOppNoteText,
          follow_up_time: 1,
          follow_up_type: "days",
        };

        sb.data.db.obj.create(
          "workorder_system",
          settingsObj,
          function (created) {
            callback(created);
          }
        );
      } else {
        callback(settingsObj[0]);
      }
    });
  }

  function contactView(dom, state, draw) {
    var opportunityList = state.data;

    dom.empty();
    dom.makeNode("btnGroup", "div", {});
    newProjectBtn(dom.btnGroup, state.contact);

    dom.makeNode("tileColumn", "column", { width: 12 });

    if (_.isEmpty(opportunityList)) {
      dom.tileColumn.makeNode("tableBreak", "lineBreak", {});

      dom.tileColumn.makeNode("header", "text", {
        text: "No Work Orders. Create the first one.",
        size: "small",
        css: "pda-text-center",
      });
    } else {
      dom.tileColumn.makeNode("tableBreak", "lineBreak", {});

      dom.tileColumn.makeNode("woTable", "table", {
        columns: {
          name: "Project Name",
          last_updated: "State",
          view: "",
        },
      });

      _.each(
        opportunityList,
        function (o, i) {
          this.woTable.makeRow("row-" + i, [
            o.name,
            moment(o.last_updated).format("MMMM Do YYYY, h:mm:ss a") +
              " <small>" +
              moment(o.last_updated).fromNow() +
              "</small>",
            "",
          ]);

          this.woTable.body["row-" + i].view.makeNode("view", "button", {
            text: '<i class="fa fa-eye"></i> View',
            css: "",
          });

          this.woTable.body["row-" + i].view.view.notify(
            "click",
            {
              type: "app-navigate-to",
              data: {
                itemId: "contacts",
                viewId: {
                  id: "project-" + o.id,
                  type: "custom",
                  title: o.name,
                  icon: "",
                  removable: true,
                  dom: function (dom, state, draw) {
                    singleView(state.obj, dom, state, function (dom) {
                      draw(dom);
                    });
                  },
                  viewState: {
                    obj: o,
                  },
                },
              },
            },
            sb.moduleId
          );
        },
        dom.tileColumn
      );
    }

    draw({
      dom: dom,
    });
  }

  function editTool(dom, state, draw, tool, mainDom) {
    function saveDetails(dom, state, draw, callback) {
      sb.data.db.obj.update(
        "projects",
        { id: state.project.id, tools: state.project.tools },
        function (updated) {
          callback(updated);
        },
        2
      );
    }

    dom.makeNode("modal", "modal", {
      onShow: function () {
        sb.data.db.obj.getAll("users", function (users) {
          var modal = dom.modal.body;
          var formObj = {
            name: {
              name: "name",
              label: "Tool Name",
              type: "text",
              value: tool.display_name,
            },
            managers: {
              name: "managers",
              label: "Who can use this tool?",
              type: "checkbox",
              options: [],
            },
          };

          if (tool.allowed_users.length == 0) {
            formObj.managers.options = _.map(users, function (user) {
              return {
                name: "managers",
                label: user.fname + " " + user.lname,
                value: user.id,
                checked: true,
              };
            });
          } else {
            formObj.managers.options = _.map(users, function (user) {
              if (_.findWhere(tool.allowed_users, { id: user.id })) {
                return {
                  name: "managers",
                  label: user.fname + " " + user.lname,
                  value: user.id,
                  checked: true,
                };
              } else {
                return {
                  name: "managers",
                  label: user.fname + " " + user.lname,
                  value: user.id,
                };
              }
            });
          }

          delete modal.loading;

          modal.makeNode("title", "div", {
            text: "Edit tool settings",
            css: "ui huge header",
          });

          modal.makeNode("divider", "div", { css: "ui divider" });

          modal.makeNode("form", "form", formObj);

          modal.makeNode("formBreak", "div", { text: "<br />" });

          modal.makeNode("btns", "div", { css: "ui buttons" });

          modal.btns
            .makeNode("save", "button", {
              css: "pda-btn-green",
              text: "Save changes",
            })
            .notify(
              "click",
              {
                type: "projects-run",
                data: {
                  run: function (dom, state, draw, users) {
                    var formInfo = dom.modal.body.form.process();

                    if (formInfo.completed == false) {
                      sb.dom.alerts.alert(
                        "Problem",
                        "Please fill out the entire form.",
                        "error"
                      );
                      return;
                    }

                    dom.modal.body.btns.save.loading();

                    _.findWhere(state.project.tools, {
                      system_name: tool.system_name,
                    }).display_name = formInfo.fields.name.value;

                    // if the tool should be visible to everyone, save a blank array instead of links to every user object in the system
                    if (formInfo.fields.managers.value.length == users.length) {
                      _.findWhere(state.project.tools, {
                        system_name: tool.system_name,
                      }).allowed_users = [];
                    }

                    saveDetails(dom, state, draw, function (updatedObj) {
                      dom.modal.hide();

                      state.project = updatedObj;

                      loadProjectTool(
                        mainDom,
                        state,
                        draw,
                        _.findWhere(state.project.tools, {
                          system_name: tool.system_name,
                        })
                      );
                    });
                  }.bind({}, dom, state, draw, users),
                },
              },
              sb.moduleId
            );

          modal.btns
            .makeNode("close", "button", {
              css: "pda-btn-yellow",
              text: "Cancel",
            })
            .notify(
              "click",
              {
                type: "projects-run",
                data: {
                  run: function (dom) {
                    dom.modal.hide();
                  }.bind({}, dom),
                },
              },
              sb.moduleId
            );

          modal.patch();
        });
      },
      /*
			onClose:function(){

				loadProjectTool(mainDom, state, draw, tool);

			}
*/
    });

    draw({
      dom: dom,
      after: function (dom) {
        dom.modal.show();
      },
    });
  }

  function erasegroups() {}

  function eraseProjects() {}

  function newProjectBtn(ui, objectId) {
    ui.makeNode("newProject", "div", {
      css: "ui green button",
      text: "New Project",
      tag: "button",
    }).notify(
      "click",
      {
        type: "projects-run",
        data: {
          run: createNewProject.bind({}, ui, objectId),
        },
      },
      sb.moduleId
    );
  }

  function requireProposalApproval(dom) {
    checkSettingsObject(function (settings) {
      sb.data.db.obj.getAll("users", function (users) {
        var formObj = {
          require: {
            name: "require",
            label: "Require approval?",
            type: "select",
            options: [
              {
                value: "no",
                name: "No",
              },
              {
                value: "yes",
                name: "Yes",
              },
            ],
          },
          users: {
            name: "users",
            label: "Who should be notified for approval requests?",
            type: "checkbox",
            options: [],
          },
        };

        formObj.users.options = _.map(
          users,
          function (user) {
            return {
              name: "users",
              label: user.fname + " " + user.lname,
              value: user.id,
            };
          },
          []
        );

        if (settings.require_approval) {
          formObj.require.value = settings.require_approval;
        }

        if (settings.approval_admin_users) {
          formObj.users.value = settings.approval_admin_users;
        }

        dom.empty();

        dom.makeNode("help", "div", {
          text: "Setting this to '<b>Yes</b>' will require all proposals on a Workorder be approved before sending to the client.",
        });

        dom.makeNode("helpbreak", "div", { text: "<br />" });

        dom.makeNode("form", "form", formObj);

        dom.makeNode("formBreak", "div", { text: "<br />" });

        dom
          .makeNode("save", "div", { text: "Save", css: "ui green button" })
          .notify(
            "click",
            {
              type: "headquarters-run",
              data: {
                run: function (dom) {
                  dom.save.loading();

                  var requireApproval = dom.form.process().fields.require.value;
                  var usersToNotify = dom.form.process().fields.users.value;

                  checkSettingsObject(function (settings) {
                    settings.require_approval = requireApproval;
                    settings.approval_admin_users = usersToNotify;

                    sb.data.db.obj.update(
                      "workorder_system",
                      {
                        id: settings.id,
                        require_approval: requireApproval,
                        approval_admin_users: usersToNotify,
                      },
                      function (updated) {
                        dom.save.loading(false);
                        dom.save.text("Saved!");

                        setTimeout(function () {
                          dom.save.loading(false);
                          dom.save.text("Save");
                        }, 1500);
                      }
                    );
                  });
                }.bind({}, dom),
              },
            },
            sb.moduleId
          );

        dom.patch();
      });
    });
  }

  var persistentUsers = null;
  var persistentServices = null;

  function requireInvoiceVendorApproval(dom) {
    let queryRoleServicesUsers = [
      {
        responseName: "role",
        table: "role_groups",
        query: { role_type: "VENDOR_APPROVER" },
        childObjs: 1,
      },
      { responseName: "users", table: "users", query: null, childObjs: 0 },
      {
        responseName: "services",
        table: "groups",
        query: { group_type: "JobType" },
        childObjs: 0,
      },
    ];

    let queryRolesOnly = [
      {
        responseName: "role",
        table: "role_groups",
        query: { role_type: "VENDOR_APPROVER" },
        childObjs: 1,
      },
    ];

    var query =
      persistentUsers === null || persistentServices === null
        ? queryRoleServicesUsers
        : queryRolesOnly;

    sb.data.db.service("DataRepository", "get", query, function (response) {
      persistentUsers = response.users ? response.users : persistentUsers;
      persistentServices = response.services
        ? response.services
        : persistentServices;

      if (response.role[0]) {
        var selectedUsers = response.role[0]["approval_users"]
          ? _.pluck(response.role[0]["approval_users"], "id")
          : [];
        var selectedServices = response.role[0]["approval_jobs"]
          ? _.pluck(response.role[0]["approval_jobs"], "job_type")
          : [];
      }
      var formObj = {
        users: {
          name: "users",
          label: "Who should be notified for approval requests?",
          type: "checkbox",
          options: [],
        },
        services: {
          name: "services",
          label: "What Jobs should be notified for approval requests?",
          type: "checkbox",
          options: [],
        },
      };

      formObj.users.options = _.map(
        persistentUsers,
        function (user) {
          return {
            name: "users",
            label: user.fname + " " + user.lname,
            value: user.id,
            checked: _.contains(selectedUsers, user.id),
          };
        },
        []
      );

      formObj.services.options = _.map(
        persistentServices,
        function (service) {
          return {
            name: "services",
            label: service.name,
            value: service.id,
            checked: _.contains(selectedServices, service.job_type),
          };
        },
        []
      );

      dom.empty();

      dom.makeNode("help", "div", {
        text: "Select Users And/or Jobs who will approve vendors on invoices",
      });

      dom.makeNode("helpbreak", "div", { text: "<br />" });

      dom.makeNode("form", "form", formObj);

      dom.makeNode("formBreak", "div", { text: "<br />" });

      dom
        .makeNode("save", "div", { text: "Save", css: "ui green button" })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom) {
                dom.save.loading();

                var usersToNotify = dom.form.process().fields.users.value;
                var servicesToNotify = dom.form.process().fields.services.value;

                sb.data.db.service(
                  "RoleService",
                  "updateRole",
                  {
                    roleType: "VENDOR_APPROVER",
                    requireApproval: "yes",
                    users: usersToNotify,
                    services: servicesToNotify,
                  },
                  function (response) {
                    dom.save.loading(false);
                    dom.save.text("Saved!");
                    sb.dom.alerts.alert(response.message, "", response.status);
                    requireInvoiceVendorApproval(dom);
                  }
                );
              }.bind({}, dom),
            },
          },
          sb.moduleId
        );

      dom.patch();
    });
  }

  function build_loader(ui, text) {
    ui.makeNode("loadingWrap", "div", {});
    ui.loadingWrap.makeNode("loader", "loader", {});
    ui.loadingWrap.makeNode("loaderText", "div", {
      text: text,
      css: "text-center",
    });

    /*
ui.makeNode('loadingWrap', 'div', {
			css: 'text-center'
		});

		ui.loadingWrap.makeNode('textLoader', 'div', {
			css: 'ui active inline text loader',
			text: text
		});
*/
  }

  // collections

  function checkURL() {
    var url = window.location.href.split("!");
    var params = {};

    _.each(url, function (urlpart, i) {
      if (i !== 0) {
        urlpart = urlpart.split(":");

        _.each(urlpart, function (v, k) {
          params[urlpart[0]] = urlpart[1];
        });
      }
    });

    if (params.where)
      params.where = JSON.parse(decodeURIComponent(params.where));

    return params;
  }

  function return_singleProjectHeader(state, options) {
    // Set variables
    var isPublic = false;
    var stateUI = {};
    var canEdit = sb.permissions.isGroupManager(
      +sb.data.cookie.userId,
      state.pageObject
    );
    var isEvent =
      state.project.type && state.project.type.type === "event" ? true : false;
    var cachedProject = _.clone(state.project);

    var managersLabel = "Managers";
    var salesPersonLabel = "Sales Managers";

    if (
      options.headerOptions &&
      ((options.headerOptions || {}).manager || {}).label &&
      typeof options.headerOptions.manager.label == "string"
    ) {
      managersLabel = options.headerOptions.manager.label;
    }

    // Set column widths (for dynamic columns)
    var row2_col0_width = isEvent ? 2 : 2;
    var row2_col1_width = isEvent ? 2 : 2;
    var row2_col2_width = isEvent ? 3 : 4;
    var row2_col3_width = isEvent ? 2 : 3;
    var row2_col4_width = isEvent ? 3 : 3;
    var row2_col5_width = isEvent ? 2 : 0;
    var row2_col6_width = isEvent ? 2 : 2;

    // Set workflow object
    var workflowRow = {
      type: "column",
      w: 16,
      style: "margin-bottom:15px !important;",
      content: {
        stateTrackerContainer: {
          type: "view",
          stateTracker: true,
          fieldName: "type",
          view: function (ui, obj, opts) {
            var options = {};

            if (opts !== undefined) {
              options = opts;
            }

            ui.makeNode("stateWrap", "div", {
              style:
                "padding: 0px; margin: 0px; border-radius: 0px; min-height: 2em;",
            });

            sb.notify({
              type: "view-field",
              data: {
                type: "state",
                property: "state",
                ui: ui.stateWrap,
                obj: obj,
                options: {
                  stateTracker: true,
                  fluid: true,
                  edit: !appConfig.is_portal,
                },
              },
            });
          },
        },
      },
    };

    var headerSetup = {
      headerContainer: {
        type: "div",
        content: {
          css: "ui stackable grid",

          // State tracker
          row1: workflowRow,

          row2: {
            type: "column",
            w: 16,
            content: {
              row2_grid: {
                type: "div",
                content: {
                  css: "ui stackable grid",
                  row2_col0: {
                    type: "column",
                    w: row2_col0_width,
                    style: "padding:0 !important",
                    content: {
                      row2_col0_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid center aligned",

                          // UID
                          uid: {
                            type: "column",
                            w: 16,
                            css: "round-border-on-child-field mobile-padding-top-5 mobile-padding-bottom-30 mobile-padding-right-0",
                            style:
                              "padding-top:0; padding-left:0 !important; padding-bottom:0px;",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="hashtag icon"></i> ID',
                                  css: "ui grey sub header",
                                },
                              },
                              priorityBtn: {
                                type: "div",
                                css: "field round-border-on-child-field mobile-padding-top-5 mobile-padding-bottom-30 mobile-padding-right-0",
                                content: {
                                  css: "edge-field",
                                  text: state.project.object_uid,
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row2_col1: {
                    type: "column",
                    w: row2_col1_width,
                    style: "padding:0 !important",
                    content: {
                      row2_col1_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid center aligned",

                          // Priority
                          priorityContainer: {
                            type: "column",
                            w: 16,
                            css: "round-border-on-child-field mobile-padding-top-5 mobile-padding-bottom-30 mobile-padding-right-0",
                            style:
                              "padding-top:0; padding-left:0 !important; padding-bottom:0px;",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="exclamation triangle icon"></i> Priority',
                                  css: "ui grey sub header",
                                },
                              },
                              priorityBtn: {
                                type: "priority",
                                fieldName: "priority",
                                edit: canEdit,
                                requireChangeNote: true,
                                onUpdate: function (updObj) {
                                  projectView(
                                    updObj,
                                    options.dom,
                                    state,
                                    options.draw
                                  );
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row2_col6: {
                    type: "column",
                    w: row2_col6_width,
                    style: "padding:0 !important",
                    content: {
                      row2_col6_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid center aligned",

                          // Client Priority
                          clientPriorityContainer: {
                            type: "column",
                            w: 16,
                            css: "round-border-on-child-field mobile-padding-top-5 mobile-padding-bottom-30 mobile-padding-right-0",
                            style:
                              "padding-top:0; padding-left:0 !important; padding-bottom:0px;",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="exclamation triangle icon"></i> Client Priority',
                                  css: "ui grey sub header",
                                },
                              },
                              priorityBtn: {
                                type: "priority",
                                fieldName: "client_priority",
                                edit: canEdit,
                                requireChangeNote: true,
                                onUpdate: function (updObj) {
                                  projectView(
                                    updObj,
                                    options.dom,
                                    state,
                                    options.draw
                                  );
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row2_col2: {
                    type: "column",
                    w: row2_col2_width,
                    style: "padding:0 !important",
                    content: {
                      row2_col2_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid center aligned",

                          // Type
                          typeContainer: {
                            type: "column",
                            w: 16,
                            css: "dropdown-round-border mobile-padding-bottom-30 mobile-padding-right-0",
                            style:
                              "padding-top:0; padding-left:0 !important; padding-bottom:0px;",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="folder open icon"></i> Type',
                                  css: "ui grey sub header",
                                },
                              },
                              typeBtn: {
                                type: "type",
                                fieldName: "type",
                                edit: canEdit,
                                onUpdate: function (updObj) {
                                  projectView(
                                    updObj,
                                    options.dom,
                                    state,
                                    options.draw
                                  );
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row2_col3: {
                    type: "column",
                    w: row2_col3_width,
                    style: "padding:0 !important",
                    content: {
                      row2_col3_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid center aligned",

                          // Category
                          categoryContainer: {
                            type: "column",
                            w: 16,
                            style:
                              "padding-top:0 !important; padding-left:0 !important; padding-bottom:0px;",
                            css: "dropdown-round-border mobile-padding-bottom-30 mobile-padding-right-0",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="project diagram icon"></i> Category',
                                  css: "ui grey sub header",
                                },
                              },
                              category: {
                                type: "type",
                                fieldName: "category",
                                useCategory: true,
                                edit: canEdit,
                                onUpdate: function (projectObj) {
                                  if (projectObj.category === null) {
                                    projectObj.category = 0;
                                  }

                                  sb.data.db.obj.runSteps(
                                    {
                                      updateSchedule: {
                                        project_id: projectObj.id,
                                        property: "category",
                                        value: projectObj.category,
                                      },
                                    },
                                    projectObj.id,
                                    function (response) {}
                                  );
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row2_col4: {
                    type: "column",
                    w: row2_col4_width,
                    style: "padding:0 !important",
                    content: {
                      row2_col4_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid center aligned",

                          // Invoice Total
                          invoiceTotalContainer: {
                            type: "column",
                            w: 16,
                            css: "mobile-padding-bottom-15",
                            style:
                              "padding-top:0 !important; padding-left:0 !important; padding-right:0 !important; padding-bottom:0px;",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="file invoice icon"></i> Invoice Total',
                                  css: "ui grey sub header",
                                },
                              },
                              invoiceTotal: {
                                type: "view",
                                view: function (ui, obj, state) {
                                  ui.makeNode("l", "div", {
                                    css: "ui loading spinner notched circle icon",
                                    tag: "i",
                                  });
                                  ui.patch();

                                  sb.notify({
                                    type: "get-updated-menu-pricing-breakout",
                                    data: {
                                      obj: state.pageObject,
                                      onComplete: function (breakout) {
                                        ui.empty();
                                        ui.makeNode("p", "div", {
                                          css: "field",
                                          style:
                                            "border:1px solid #ebebeb; padding:8px; cursor:not-allowed !important;",
                                          text:
                                            "<strong>$ " +
                                            (
                                              breakout.total / 100
                                            ).formatMoney() +
                                            "</strong>",
                                        });
                                        ui.patch();

                                        if (
                                          state.pageObject.invoice_value !==
                                          breakout.total
                                        ) {
                                          sb.data.db.obj.update(
                                            "groups",
                                            {
                                              id: state.pageObject.id,
                                              invoice_value: breakout.total,
                                            },
                                            function (r) {}
                                          );
                                        }
                                      },
                                    },
                                  });
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row2_col5: {
                    type: "column",
                    w: row2_col5_width,
                    style: "padding:0 !important",
                    content: {
                      row2_col5_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid center aligned",

                          // Head Count
                          headCountContainer: {
                            type: "column",
                            w: 16,
                            css: "mobile-padding-top-15 mobile-padding-bottom-15 mobile-padding-left-0",
                            style:
                              "padding-top:0; padding-left:15px; padding-right:0 !important; padding-bottom:0px;",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="users icon"></i> Head Count',
                                  css: "ui grey sub header",
                                },
                              },
                              headCountFieldContainer: {
                                type: "div",
                                content: {
                                  css: "round-border",
                                  style: "padding: 10px 10px 8px 10px",
                                  headCount: {
                                    type: "view",
                                    view: function (ui, obj, state) {
                                      sb.notify({
                                        type: "view-field",
                                        data: {
                                          type: "quantity",
                                          property: "head_count",
                                          ui: ui,
                                          obj: obj,
                                          options: {
                                            edit: true,
                                            commitUpdates: true,
                                            onUpdate: function (obj) {
                                              // get active menu and update guest count
                                              if (
                                                obj.proposal &&
                                                Number.isInteger(
                                                  obj.proposal.menu
                                                )
                                              ) {
                                                var updates = [
                                                  {
                                                    id: obj.id,
                                                    head_count: obj.head_count,
                                                    object_bp_type: "groups",
                                                  },
                                                  {
                                                    id: obj.proposal.menu,
                                                    guest_count: obj.head_count,
                                                    object_bp_type:
                                                      "inventory_menu",
                                                  },
                                                ];

                                                sb.data.db.obj.update(
                                                  "proposals",
                                                  updates,
                                                  function (response) {
                                                    if (response) {
                                                      sb.data.db.controller(
                                                        "setMenuReservations",
                                                        {
                                                          menuId:
                                                            obj.proposal.menu,
                                                        },
                                                        function (response) {
                                                          //onComplete(obj);
                                                        }
                                                      );
                                                    }
                                                  }
                                                );
                                              }
                                            },
                                          },
                                        },
                                      });
                                    },
                                  },
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },

          row3: {
            type: "column",
            w: 16,
            style: "padding:15px 0 0 0 !important",
            content: {
              row3_grid: {
                type: "div",
                content: {
                  css: "ui stackable grid",
                  row3_col1: {
                    type: "column",
                    w: 7,
                    css: "mobile-margin-bottom-15",
                    content: {
                      row2_col3_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid center aligned",

                          // Start Date
                          startDateContainer: {
                            type: "column",
                            w: 8,
                            css: "mobile-padding-bottom-30 mobile-padding-right-0",
                            style:
                              "padding-top:0 !important; padding-left:0 !important; padding-bottom:0px;",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="calendar day icon"></i> Start Date',
                                  css: "ui grey sub header",
                                },
                              },
                              startDate: {
                                type: "date",
                                style:
                                  "padding:8px !important; border:1px solid #ebebeb; border-radius:0.375rem;",
                                fieldName: "start_date",
                                edit: canEdit,
                                update: function (
                                  obj,
                                  [fieldName],
                                  newVal,
                                  onComplete
                                ) {
                                  sb.notify({
                                    type: "update-invoices",
                                    data: {
                                      dom: false,
                                      project: cachedProject,
                                      startDate: newVal,
                                      callback: function (response) {
                                        // Update state
                                        cachedProject.start_date = newVal;

                                        // Callback
                                        onComplete;
                                      },
                                    },
                                  });
                                },
                              },
                            },
                          },

                          // End Date
                          endDateContainer: {
                            type: "column",
                            w: 8,
                            css: "mobile-padding-bottom-10",
                            style:
                              "padding-top:0 !important; padding-left:0 !important; padding-right:0 !important; padding-bottom:0px;",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="calendar day icon"></i> End Date',
                                  css: "ui grey sub header",
                                },
                              },
                              endDate: {
                                type: "date",
                                style:
                                  "padding:8px; border:1px solid #ebebeb; border-radius:0.375rem;",
                                fieldName: "end_date",
                                edit: canEdit,
                                update: function (
                                  obj,
                                  [fieldName],
                                  newVal,
                                  onComplete
                                ) {
                                  sb.notify({
                                    type: "update-invoices",
                                    data: {
                                      dom: false,
                                      project: cachedProject,
                                      endDate: newVal,
                                      callback: function (response) {
                                        // Update state
                                        cachedProject.end_date = newVal;

                                        // Callback
                                        onComplete;
                                      },
                                    },
                                  });
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row3_col2: {
                    type: "column",
                    w: 9,
                    css: "mobile-padding-left-0",
                    style:
                      "padding-top:0 !important; padding-bottom:0 !important;",
                    content: {
                      row2_col3_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid",

                          // Time Tracking
                          timeTrackingContainer: {
                            type: "column",
                            w: 16,
                            css: "round-border",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="ui stopwatch icon"></i> Time Tracking',
                                  css: "ui grey sub header",
                                },
                              },
                              timeTracking: {
                                type: "view",
                                view: function (ui, obj, options) {
                                  options.showProgressBar = true;

                                  ui.makeNode("seg-" + obj.id, "div", {
                                    css: "ui basic segment",
                                    style: "box-shadow: none; padding:0px;",
                                  });

                                  sb.notify({
                                    type: "view-field",
                                    data: {
                                      type: "timeTracking",
                                      property: "time_logged",
                                      ui: ui["seg-" + obj.id],
                                      obj: obj,
                                      options: options,
                                    },
                                  });
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },

          row4: {
            type: "column",
            w: 16,
            style:
              "margin-left:12px !important; margin-right:12px !important; padding:15px 0 0 0 !important;",
            content: {
              row4_grid: {
                type: "div",
                content: {
                  css: "ui stackable grid",
                  row4_col1: {
                    type: "column",
                    w: 7,
                    style: "padding:0 !important;",
                    content: {
                      row4_col1_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid",

                          // Details container
                          detailsContainer: {
                            type: "column",
                            w: 16,
                            css: "round-border mobile-margin-bottom-15",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="info circle icon"></i> Details',
                                  css: "ui grey sub header",
                                },
                              },
                              details: {
                                type: "detail",
                                fieldName: "description",
                                edit: canEdit,
                                editing: canEdit,
                                onUpdate: function (obj, value) {
                                  sb.data.db.obj.runSteps(
                                    {
                                      updateSchedule: {
                                        project_id: obj.id,
                                        property: "parent_description",
                                        value: value,
                                      },
                                    },
                                    obj.id,
                                    function (response) {}
                                  );
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row4_col2: {
                    type: "column",
                    w: 3,
                    css: "mobile-padding-left-0",
                    style:
                      "padding-top:0 !important; padding-bottom:0 !important; padding-right:0 !important;",
                    content: {
                      row4_col2_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid",

                          // Managers container
                          managersContainer: {
                            type: "column",
                            w: 16,
                            css: "round-border mobile-margin-bottom-15",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text:
                                    '<i class="users icon"></i> ' +
                                    managersLabel,
                                  css: "ui grey sub header",
                                  style: "margin-bottom:8px !important;",
                                },
                              },
                              managers: {
                                type: "users",
                                fieldName: "managers",
                                edit: canEdit,
                                multi: true,
                                shouldTag: true,
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row4_col3: {
                    type: "column",
                    w: 3,
                    css: "mobile-padding-left-0",
                    style:
                      "padding-top:0 !important; padding-bottom:0 !important; padding-right:0 !important;",
                    content: {
                      row4_col3_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid",

                          // Sales Managers container
                          managersContainer: {
                            type: "column",
                            w: 16,
                            css: "round-border mobile-margin-bottom-15",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text:
                                    '<i class="users icon"></i> ' +
                                    salesPersonLabel,
                                  css: "ui grey sub header",
                                  style: "margin-bottom:8px !important;",
                                },
                              },
                              salesmanagers: {
                                type: "users",
                                fieldName: "sales_managers",
                                edit: canEdit,
                                multi: true,
                                shouldTag: true,
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  row4_col4: {
                    type: "column",
                    w: 3,
                    css: "mobile-padding-left-0",
                    style:
                      "padding-top:0 !important; padding-bottom:0 !important; padding-right:0 !important;",
                    content: {
                      row4_col4_grid: {
                        type: "div",
                        content: {
                          css: "ui stackable grid",

                          // Locations container
                          locationsContainer: {
                            type: "column",
                            w: 16,
                            css: "round-border",
                            content: {
                              label: {
                                type: "div",
                                content: {
                                  text: '<i class="location arrow icon"></i> Locations',
                                  css: "ui grey sub header",
                                  style: "margin-bottom:8px !important;",
                                },
                              },
                              locations: {
                                type: "locations",
                                fieldName: "locations",
                                edit: canEdit,
                                multi: true,
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    };

    if ( appConfig.instance == 'foundation_group') {
        delete headerSetup.headerContainer.content['row2'].content['row2_grid'].content['row2_col0'];
        delete headerSetup.headerContainer.content['row2'].content['row2_grid'].content['row2_col1'];
        delete headerSetup.headerContainer.content['row2'].content['row2_grid'].content['row2_col4'];
        delete headerSetup.headerContainer.content['row2'].content['row2_grid'].content['row2_col6'];

        headerSetup.headerContainer.content['row2'].content['row2_grid'].content['row2_col7'] = {
            type: "column",
            w: 9,
            css: "mobile-padding-left-0",
            style:
              "padding-top:0 !important; padding-bottom:0 !important;",
            content: {
              row2_col3_grid: {
                type: "div",
                content: {
                  css: "ui stackable grid",

                  // Time Tracking
                  timeTrackingContainer: {
                    type: "column",
                    w: 16,
                    css: "round-border",
                    content: {
                      label: {
                        type: "div",
                        content: {
                          text: '<i class="ui stopwatch icon"></i> Time Tracking',
                          css: "ui grey sub header",
                        },
                      },
                      timeTracking: {
                        type: "view",
                        view: function (ui, obj, options) {
                          options.showProgressBar = true;

                          ui.makeNode("seg-" + obj.id, "div", {
                            css: "ui basic segment",
                            style: "box-shadow: none; padding:0px;",
                          });

                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "timeTracking",
                              property: "time_logged",
                              ui: ui["seg-" + obj.id],
                              obj: obj,
                              options: options,
                            },
                          });
                        },
                      },
                    },
                  },
                },
              },
            },
          };

        /// Start Date & End Date
        delete headerSetup.headerContainer.content['row3'].content['row3_grid'];


        //project types with renew options
        //var allowedProjectTypes = [9234871]; //projectType allow renew
        //var fieldNameRenew = 'repeat_forever'; //fieldName save renew value

        delete headerSetup.headerContainer.content['row4'].content['row4_grid'].content['row4_col1'];


      /*if(state.project && state.project.type && allowedProjectTypes.includes(state.project.type.id)){
        //option for renew
        headerSetup.headerContainer.content['row4'].content['row4_grid'].content['row4_col1'] = {
          type: "column",
          w: 7,
          style: "padding:0 !important;",
          content: {
            row4_col1_grid: {
              type: "div",
              content: {
                css: "ui stackable grid",

                // Details container
                detailsContainer: {
                  type: "column",
                  w: 16,
                  css: "round-border mobile-margin-bottom-15",
                  content: {
                    label: {
                      type: "div",
                      content: {
                        text: '<i class="info circle icon"></i> Auto-Renewal',
                        css: "ui grey sub header",
                      },
                    },
                    repeat_forever: {
                      type: "view",
                      view: function (ui, obj) {

                        sb.notify({
                          type: "view-field",
                          data: {
                            type: "toggle",
                            property: "repeat_forever",
                            ui: ui.makeNode("repforever-" + obj.id, "div", {}),
                            obj: {
                              id: obj.id,
                              repeat_forever: obj.repeat_forever == 1 ? true : false
                            },
                            options: {
                              commitUpdates: true,
                              edit: true,
                              editing: true,
                              size: "huge",
                              onUpdate: function (val) {
                                // Set value
                                obj.repeat_forever = val ? 1 : 0;
                              },
                            },
                          },
                        });

                      },
                    },
                  },
                },
              },
            },
          },
        };
      } else {
        delete headerSetup.headerContainer.content['row4'].content['row4_grid'].content['row4_col1'];
      }*/
        ///Details


        ///Managers
        delete headerSetup.headerContainer.content['row4'].content['row4_grid'].content['row4_col2'];
        /// Salesperson
        delete headerSetup.headerContainer.content['row4'].content['row4_grid'].content['row4_col3'];
        ///Locations
        delete headerSetup.headerContainer.content['row4'].content['row4_grid'].content['row4_col4'];
    }

    if (!isEvent) {
      delete headerSetup.headerContainer.content.row2.content.row2_grid.content
        .row2_col5;
    }

    //Limits Sales manager Box to just the Infinity Instance or rickyvoltz in dev environment - UM
    // if (
    //   appConfig.instance != "rickyvoltz" &&
    //   appConfig.instance != "infinity" &&
    //   appConfig.instance != "dreamcatering"
    // ) {
    //   delete headerSetup.headerContainer.content.row4.content.row4_grid.content
    //     .row4_col3;
    //   headerSetup.headerContainer.content.row4.content.row4_grid.content.row4_col1.w = 10;
    // }

    return headerSetup;
  }

  function build_projects_collection(dom, state, draw, mainDom, settings) {
    var selectedView = "table";
    var fieldsSetup = {
        managers: {
          title: "Managers",
          type: "users",
          parseUpdates: function (updates) {
            updates.managers = [updates.managers];
            var leadsList = updates.managers;
            updates.users = leadsList;
            updates.tagged_with = _.chain(leadsList)
              .union(updates.tagged_with)
              .uniq()
              .value();

            updates.notify = updates.tagged_with;
            return updates;
          },
          options: {
            mini: true,
          },
        },
        name: {
          title: "Name",
          type: "title",
        },
        client: {
          title: "Client",
          type: "edge",
          view: function (ui, obj) {
            if (obj.main_contact) {
              var ret = "";

              if (ui) {
                ui.makeNode("c", "div", {
                  tag: "a",
                  text:
                    '<div class="ui header mini">' +
                    obj.main_contact.fname +
                    " " +
                    obj.main_contact.lname +
                    "</div>",
                  href: sb.data.url.createPageURL("object-view", {
                    id: obj.main_contact.id,
                    name: obj.main_contact.fname + " " + obj.main_contact.lname,
                    type: "contacts",
                  }),
                });
              } else {
                ret = obj.main_contact.fname + " " + obj.main_contact.lname;
              }

              if (obj.main_contact.company) {
                if (ui) {
                  ui.makeNode("co", "div", {
                    tag: "a",
                    css: "link truncate",
                    style:
                      "display:block; font-size:12px; padding:0 !important;",
                    text:
                      "<nobr> at " + obj.main_contact.company.name + "</nobr>",
                    href: sb.data.url.createPageURL("object-view", {
                      id: obj.main_contact.company.id,
                      name: obj.main_contact.company.name,
                      type: "companies",
                    }),
                  });
                } else {
                  ret += " at " + obj.main_contact.company.name;
                }
              }

              if (!ui) {
                return ret;
              }
            } else {
              if (ui) {
                ui.makeNode("c", "div", {
                  text: '<i class="text-muted">Not set</i>',
                });
              } else {
                return "Not set";
              }
            }
          },
        },
        state: {
          title: "Status",
          type: "state",
        },
        start_date: {
          title: "Start Date",
          type: "date",
          end: "end_date",
          rangeOver: true,
        },
        end_date: {
          title: "End Date",
          type: "date",
          hideInMini: true,
          //is_due_date: true
        },
        invoice_value: {
          title: "Value",
          type: "usd",
          view: function (ui, obj) {
            if (ui) {
              ui.makeNode("value", "div", {
                text: "$" + (obj.invoice_value / 100).formatMoney(),
              });

              ui.patch();
            } else {
              return "$" + (obj.invoice_value / 100).formatMoney();
            }
          },
        },
        type: {
          title: "Type",
          type: "type",
          view: function (ui, obj) {
            if (obj.type) {
              var ret = "";

              if (ui) {
                ui.makeNode("c", "div", {
                  text:
                    '<div class="ui header mini">' + obj.type.name + "</div>",
                });
              } else {
                ret = obj.type.name;
              }

              if (!ui) {
                return ret;
              }
            } else {
              if (ui) {
                ui.makeNode("c", "div", {
                  text: '<i class="text-muted">Not set</i>',
                });
              } else {
                return "Not set";
              }
            }
          },
        }
      };
    var whereSetup = {
        group_type: "Project",
        childObjs: {
          name: true,
          state: true,
          state_updated_on: true,
          group_type: "id",
          type: {
            name: true,
            states: true,
          },
          main_contact: {
            fname: true,
            lname: true,
            company: {
              name: true,
            },
          },
          managers: {
            fname: true,
            lname: true,
            profile_image: true,
          },
          start_date: true,
          end_date: true,
          invoice_value: true,
          invoice_value_no_tax: true,
        },
      };

    //   if (appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz'){

    //     fieldsSetup = {
    //         name: {
    //           title: "Name",
    //           type: "title",
    //         },
    //         type: {
    //             title: "Type",
    //             type: "type",
    //             view: function (ui, obj) {
    //               if (obj.type) {
    //                 var ret = "";

    //                 if (ui) {
    //                   ui.makeNode("c", "div", {
    //                     text:
    //                       '<div class="ui header mini">' + obj.type.name + "</div>",
    //                   });
    //                 } else {
    //                   ret = obj.type.name;
    //                 }

    //                 if (!ui) {
    //                   return ret;
    //                 }
    //               } else {
    //                 if (ui) {
    //                   ui.makeNode("c", "div", {
    //                     text: '<i class="text-muted">Not set</i>',
    //                   });
    //                 } else {
    //                   return "Not set";
    //                 }
    //               }
    //             },
    //           },
    //         state: {
    //             title: "Status",
    //             type: "state",
    //           },
    //         client: {
    //           title: "Client",
    //           type: "edge",
    //           view: function (ui, obj) {
    //             if (obj.main_contact) {
    //               var ret = "";

    //               if (ui) {
    //                 ui.makeNode("c", "div", {
    //                   tag: "a",
    //                   text:
    //                     '<div class="ui header mini">' +
    //                     obj.main_contact.fname +
    //                     " " +
    //                     obj.main_contact.lname +
    //                     "</div>",
    //                   href: sb.data.url.createPageURL("object-view", {
    //                     id: obj.main_contact.id,
    //                     name: obj.main_contact.fname + " " + obj.main_contact.lname,
    //                     type: "contacts",
    //                   }),
    //                 });
    //               } else {
    //                 ret = obj.main_contact.fname + " " + obj.main_contact.lname;
    //               }

    //               if (obj.main_contact.company) {
    //                 if (ui) {
    //                   ui.makeNode("co", "div", {
    //                     tag: "a",
    //                     css: "link truncate",
    //                     style:
    //                       "display:block; font-size:12px; padding:0 !important;",
    //                     text:
    //                       "<nobr> at " + obj.main_contact.company.name + "</nobr>",
    //                     href: sb.data.url.createPageURL("object-view", {
    //                       id: obj.main_contact.company.id,
    //                       name: obj.main_contact.company.name,
    //                       type: "companies",
    //                     }),
    //                   });
    //                 } else {
    //                   ret += " at " + obj.main_contact.company.name;
    //                 }
    //               }

    //               if (!ui) {
    //                 return ret;
    //               }
    //             } else {
    //               if (ui) {
    //                 ui.makeNode("c", "div", {
    //                   text: '<i class="text-muted">Not set</i>',
    //                 });
    //               } else {
    //                 return "Not set";
    //               }
    //             }
    //           },
    //         },
    //         client_service: {
    //           title: "Client Service Tax Year",
    //           type: "edge",
    //           view: function (ui, obj) {

    //             if ( !_.isUndefined(obj['#HDIjlE']) ) {

    //                 if ( (obj['#HDIjlE'][0] || {})['_4']) {

    //                     console.log(obj['#HDIjlE'][0]);
    //                     if (ui) {
    //                         ui.makeNode("c", "div", {
    //                             text: obj['#HDIjlE'][0]['_4']
    //                           });
    //                     } else {
    //                         return obj['#HDIjlE'][0]['_4'];
    //                     }
    //                 }

    //             }
    //             return;



    //             if (obj.main_contact) {
    //               var ret = "";

    //               if (ui) {
    //                 ui.makeNode("c", "div", {
    //                   tag: "a",
    //                   text:
    //                     '<div class="ui header mini">' +
    //                     obj.main_contact.fname +
    //                     " " +
    //                     obj.main_contact.lname +
    //                     "</div>",
    //                   href: sb.data.url.createPageURL("object-view", {
    //                     id: obj.main_contact.id,
    //                     name: obj.main_contact.fname + " " + obj.main_contact.lname,
    //                     type: "contacts",
    //                   }),
    //                 });
    //               } else {
    //                 ret = obj.main_contact.fname + " " + obj.main_contact.lname;
    //               }

    //               if (obj.main_contact.company) {
    //                 if (ui) {
    //                   ui.makeNode("co", "div", {
    //                     tag: "a",
    //                     css: "link truncate",
    //                     style:
    //                       "display:block; font-size:12px; padding:0 !important;",
    //                     text:
    //                       "<nobr> at " + obj.main_contact.company.name + "</nobr>",
    //                     href: sb.data.url.createPageURL("object-view", {
    //                       id: obj.main_contact.company.id,
    //                       name: obj.main_contact.company.name,
    //                       type: "companies",
    //                     }),
    //                   });
    //                 } else {
    //                   ret += " at " + obj.main_contact.company.name;
    //                 }
    //               }

    //               if (!ui) {
    //                 return ret;
    //               }
    //             } else {
    //               if (ui) {
    //                 ui.makeNode("c", "div", {
    //                   text: '<i class="text-muted">Not set</i>',
    //                 });
    //               } else {
    //                 return "Not set";
    //               }
    //             }
    //           },
    //         },

    //       };

    //       whereSetup = {
    //         group_type: "Project",
    //         childObjs: {
    //           name: true,
    //           state: true,
    //           state_updated_on: true,
    //           group_type: true,
    //           type: {
    //             name: true,
    //             states: true,
    //           },
    //           main_contact: {
    //             fname: true,
    //             lname: true,
    //             company: {
    //               name: true,
    //             },
    //           },
    //           managers: {
    //             fname: true,
    //             lname: true,
    //             profile_image: true,
    //           }
    //         }
    //       };
    //   }

    var collectionsSetup = {
      actions: {
        create: function (ui, newObj, onComplete, bp, setup) {
          state.id = state.headquarters.id;
          newObj.parent = state.headquarters.id;
          newObj.onComplete = onComplete;

          createNewProject(
            ui,
            state,
            newObj,
            function (newProject) {
              if (newProject) {
                sb.notify({
                  type: "app-navigate-to",
                  data: {
                    itemId: "groups",
                    viewId: {
                      viewState: {
                        obj: newProject,
                      },
                    },
                  },
                });
              }
            },
            setup
          );
        },
        view: false,
        copy: false,
        calEvents: {
          singleAction: false,
          title: "Copy calendar link",
          icon: "calendar",
          color: "blue",
          domType: "custom",
          ui: false,
          headerAction: function (selection, ui, shouldUpdate, options) {
            var dummy = document.createElement("input");
            var loc = "#";

            // Adding logic for calendar subscription link
            if (state.hasOwnProperty("layer")) {
              if (state.layer === "hq") {
                loc =
                  sb.url + "/api/caldav/getEvents.php?i=" + appConfig.instance;
              } else if (state.layer === "team") {
                loc =
                  sb.url +
                  "/api/caldav/getEvents.php?i=" +
                  appConfig.instance +
                  "&id=" +
                  state.team.id +
                  "";
              }
            }

            document.body.appendChild(dummy);
            dummy.value = loc;
            dummy.select();
            document.execCommand("copy");
            document.body.removeChild(dummy);

            sb.notify({
              type: "display-alert",
              data: {
                header: "Link copied",
                body: "Calendar subscription link has been copied to clipboard.",
                color: "green",
              },
            });
          },
        },
      },
      domObj: dom,
      fields: fieldsSetup,
      fullView: {
        type: "hqTool",
        id: "projectTool",
      },
      groupings: {
        type: "Type",
        state: "Status",
        managers: "Owner",
        by_range: true,
      },
      objectType: "groups",
      rangeOver: "start_date",
      singleView: {
        view: function (dom, obj, onComplete) {
          state.fromCollection = true;
          projectView(obj, dom, state, onComplete);
        },
      },
      size: "small",
      selectedView: selectedView,
      subviews: {
        list: {
          groupBy: {
            defaultTo: "type",
          },
        },
        board: {
          groupBy: {
            defaultTo: "state",
          },
        },
        calendar: {
          hideUnscheduled: true,
        },
        table: {
          groupBy: {
            defaultTo: "type",
          },
        },
      },
      state: state,
      sortCol: "name",
      sortDir: "asc",
      counter: true,
      where: whereSetup,
      showPaging: settings.showPaging,
      counter: settings.counter,
    };

    // if (appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz') {
    //     collectionsSetup.query = function(data, callback, query, subview, range, types) {

    //         var dbCall = "addClientService";
    //         sb.data.db.service("ProjectService", dbCall, {data}, function (res) {

    //             data.data = res.data;
    //             data.recordsFiltered = res.recordsFiltered;
    //             data.recordsTotal = res.recordsTotal;

    //             callback(data);

    //         });
    //     };
    // }

    function checkSettingsObj(settings, setup) {
      // Can be any type of setup object that settings vars can modify

      if (settings.hasOwnProperty("menu")) {
        setup.menu = settings.menu;

        if (setup.menu === false) {
          setup.subviews = {};
        }
      }

      if (settings.hasOwnProperty("chart")) {
        setup.subviews.chart = settings.chart;
      }

      if (settings.hasOwnProperty("search")) {
        setup.search = settings.search;
      }

      if (settings.selectedView) {
        setup.selectedView = settings.selectedView;
      }

      if (settings.hasOwnProperty("filters")) {
        setup.filters = settings.filters;
      }

      if (settings.hasOwnProperty("showDateRange")) {
        setup.showDateRange = settings.showDateRange;
      }

      if (settings.hasOwnProperty("where")) {
        _.each(settings.where, function (v, k) {
          setup.where[k] = v;
        });
      }

      if (settings.hasOwnProperty("parseData")) {
        setup.parseData = settings.parseData;
      }

      if (settings.hasOwnProperty("fields")) {
        if (settings.fields.hasOwnProperty("invoice_value_no_taxes")) {
          delete setup.fields.invoice_value;

          setup.fields.invoice_value_no_taxes =
            settings.fields.invoice_value_no_taxes;
        } else {
          setup.fields = settings.fields;
        }
      }

      if (settings.hasOwnProperty("actions")) {
        setup.actions = settings.actions;
      }

      return setup;
    }

    if (
      appConfig.instance === "foundation_group"
      // || appConfig.instance === 'rickyvoltz'
    ) {
      // Additional properties to gather in data call
      collectionsSetup.where.childObjs.last_updated = true;

      // Adjust fields for Foundation Group collections
      collectionsSetup.fields = {

        // parent: {
        //   title: "Team",
        //   type: "parent",
        //   shouldShow: function (obj) {
        //     if (obj.parent.id !== state.headquarters.id) {
        //       return true;
        //     } else {
        //       return false;
        //     }
        //   },
        //   view: function (ui, obj) {
        //     if (!ui) {
        //       if (obj.parent) {
        //         var ret = "";

        //         if (ui) {
        //           ui.makeNode("c", "div", {
        //             tag: "a",
        //             text:
        //               '<div class="ui header mini">' +
        //               obj.parent.name +
        //               "</div>",
        //             href: sb.data.url.createPageURL("object-view", {
        //               id: obj.parent.name,
        //               name: obj.parent.name,
        //               type: obj.object_bp_type,
        //             }),
        //           });
        //         } else {
        //           ret = obj.parent.name;
        //         }

        //         if (!ui) {
        //           return ret;
        //         }
        //       } else {
        //         return "";
        //       }
        //     }
        //   },
        // },
        client: {
          title: "Organization",
          type: "edge",
          view: function (ui, obj) {

            if (obj.main_contact) {
              var ret = "";

              if (obj.main_contact.company) {
                if (ui) {
                  ui.makeNode("co", "div", {
                    tag: "a",
                    css: "link truncate",
                    style:
                      "display:block; padding:0 !important;",
                    text: "<nobr>" + obj.main_contact.company.name + "</nobr><br><span style=\"font-size:12px;\">" + obj.main_contact.name+ "</span>",
                    href: sb.data.url.createPageURL("object-view", {
                      id: obj.main_contact.company.id,
                      name: obj.main_contact.company.name,
                      type: "companies",
                    }),
                  });
                } else {
                  ret += obj.main_contact.company.name + " - " +obj.main_contact.name;
                }
              }

              if (!ui) {
                return ret;
              }
            } else {
              if (ui) {
                ui.makeNode("c", "div", {
                  text: '<i class="text-muted">Not set</i>',
                });
              } else {
                return "Not set";
              }
            }
          },
        },
        name: {
            title: "Name",
            type: "title",
          },
        type: {
          title: "Type",
          type: "type",
          view: function (ui, obj) {
            if (obj.type) {
              var ret = "";

              if (ui) {
                ui.makeNode("c", "div", {
                  text:
                    '<div>' + obj.type.name + "</div>",
                });
              } else {
                ret = obj.type.name;
              }

              if (!ui) {
                return ret;
              }
            } else {
              if (ui) {
                ui.makeNode("c", "div", {
                  text: '<i class="text-muted">Not set</i>',
                });
              } else {
                return "Not set";
              }
            }
          },
        },
        state: {
          title: "Status",
          type: "state",
        },
        dateOfInc: {
          title: "Date of Inc.",
          type: "date",
          view: function (ui, obj) {

            if ( obj.dateOfInc ) {
              if(ui && typeof ui.makeNode === 'function') {
                ui.makeNode("date", "div", {
                  css: 'ui muted label',
                  text: moment(obj.dateOfInc)
                    .local()
                    .format("MMM Do YYYY"),
                });
              }
            } else {
              if(ui && typeof ui.makeNode === 'function') {
                ui.makeNode("date", "div", {
                  text: 'Not set on Core Demo',
                  css: 'text-muted',
                });
              }
            }
          }
        },
        dateIntakeComplete: {
          title: "Date Intake Complete",
          type: "date",
          end: "end_date",
          rangeOver: true,
        },
        managers: {
          title: "Assigned",
          type: "users",
          hideInMini: true,
          //is_due_date: true
        },
        fiscalYearEndDate: {
          title: "Fiscal Year End Date",
          type: "date",
          hideInMini: true,
          //is_due_date: true
        },
        dueDate: {
          title: "Due Date",
          type: "date",
          hideInMini: true,
          //is_due_date: true
        },
        last_updated: {
          title: "Date of Last Activity",
          type: "date",
          hideInMini: true,
        },
      };

      collectionsSetup.query = function (request, onComplete) {
        if (request.hasOwnProperty("select")) {
          request.childObjs = request.select;
          delete request.select;
        }

        if (typeof request.childObjs === "undefined") {
          childObjs = 0;
        } else {
          childObjs = request.childObjs;
          delete request.childObjs;
        }

        var getJust = {};
        if (request.hasOwnProperty("getJust")) {
          getJust = request.getJust;
          delete request.getJust;
        }

        childObjs.type = {
            name: true,
            states: true
        };

        sb.data.db.service(
          "FGProjectsService",
          "getCollectionData",
          {
            objectType: "groups",
            queryObj: request,
            getChildObjs: childObjs,
            getJust: getJust,
          },
          function (response) {

            onComplete(response);
          }
        );
      };
    }

    // Don't show the create button for Foundation Group (all of their
    // projects should be created using automated actions)
    if (
      appConfig.instance === "foundation_group" &&
      appConfig.user.type !== "admin" &&
      appConfig.user.type !== "developer"
    ) {
      collectionsSetup.actions.create = false;
    }

    var urlParams = checkURL();

    if (sb.dom.isMobile) {
      selectedView = "list";
    }

    collectionsSetup = checkSettingsObj(settings, collectionsSetup);

    if (state.pageObject.group_type === "Headquarters") {
      collectionsSetup.layer = "hq";
    }

    var config = {};

    if (urlParams.type) {
      config = {
        type: urlParams.type,
      };
    }

    if (urlParams.state) {
      config.state = urlParams.state;
    }

    if (urlParams.where) {
      _.mapObject(urlParams.where, function (v, k) {
        collectionsSetup.where[k] = v;
      });
    }

    if (!_.isEmpty(config)) {
      collectionsSetup.config = config;
    }

    collectionsSetup.subviews.chart = settings.chart;

    collectionsSetup.actions.downloadCSV = true;

    if (settings && settings.onBoxview) {
      collectionsSetup.onBoxview = settings.onBoxview;
    }

    if (
      !_.isEmpty(state._additionalTags) &&
      Array.isArray(state._additionalTags)
    ) {
      collectionsSetup.selectedTags = state._additionalTags;
    }

    sb.notify({
      type: "show-collection",
      data: collectionsSetup,
    });
  }

  function showBookedEventsChart(ui, list, options, legend) {
    var chartDom = ui.chart;

    function draw_chart(ui) {
      get_raw_event_data(function (response) {
        if (_.isEmpty(response)) {
          msg =
            "No data in this range to display. Try broadening the selected time range/filters.";

          ui.empty();
          ui.makeNode("msg", "div", {
            css: "ui message",
            text: '<i class="ui exclamation icon"></i>' + msg,
          });
          ui.patch();
        } else {
          // variables
          var datesWithNoTaxValues = [];
          var eventData = [];
          var chartData = [];
          var totalEvents = 0;
          var totalValue = 0;
          var objType = "items";

          console.log('Raw event before processing:', response);

          // set UTC start datetime to local for each event & get needed props for each event:
          datesWithNoTaxValues = response.reduce(function (newArray, event) {
            if (event["start_date"] != "") {
              event["start_date"] = moment
                .utc(event["start_date"])
                .local()
                .format("YYYY-MM-DD");

              var invoiceValueNoTaxes = 0;

              if (
                !isNaN(event["invoice_value_no_taxes"]) &&
                event["invoice_value_no_taxes"] != undefined
              ) {
                invoiceValueNoTaxes = event["invoice_value_no_taxes"];
              }

              newArray.push({
                x: event["start_date"],
                y: invoiceValueNoTaxes,
                venueName: event["venue_name"],
                venueFee: event["venue_fee"],
                id: event["id"],
                name: event["name"]
              });
            }

            return newArray;
          }, []);

          // group events by start date, sum invoice values without tax:
          datesWithNoTaxValues.reduce(function (newArray, obj) {
            if (!newArray[obj.x]) {
              newArray[obj.x] = { x: obj.x, y: 0, itemCount: 0, events: [] };
              eventData.push(newArray[obj.x]);
            }

            if (newArray[obj.x].y != undefined) {
              newArray[obj.x].y += obj.y;
              newArray[obj.x].itemCount++;
            }

            if (newArray[obj.x].y > 0){
              newArray[obj.x].venueName = obj.venueName;
              newArray[obj.x].venueFee = obj.venueFee;
              newArray[obj.x].otherFees = !isNaN(obj.venueFee) && newArray[obj.x].y > obj.venueFee ? newArray[obj.x].y - obj.venueFee : "N/A";
              // Track this event
              newArray[obj.x].events = newArray[obj.x].events || [];
              newArray[obj.x].events.push({
                id: obj.id,
                name: obj.name,
                venueFee: obj.venueFee,
                otherFees: !isNaN(obj.venueFee) && obj.y > obj.venueFee ? obj.y - obj.venueFee : "N/A",
                value: obj.y
              });
            }

            return newArray;
          }, {});

          // filter data by start and end date:
          var startDate = moment(options.query.range.start).format(
            "YYYY-MM-DD"
          );
          var endDate = moment(options.query.range.end).format("YYYY-MM-DD");

          eventData = eventData.filter((group) => {
            var date = moment(group.x);

            if (
              (date.isSame(startDate) || date.isAfter(startDate)) &&
              (date.isSame(endDate) || date.isBefore(endDate))
            ) {
              return true;
            } else {
              return false;
            }
          });

          if (_.isEmpty(eventData)) {
            msg =
              "No data in this range to display. Try broadening the selected time range/filters.";

            ui.empty();
            ui.makeNode("msg", "div", {
              css: "ui message",
              text: '<i class="ui exclamation icon"></i>' + msg,
            });
            ui.patch();

            return;
          }

          if (eventData.find((obj) => obj["x"] == startDate) == undefined) {
            eventData.push({ x: startDate, y: 0, itemCount: 0 });
          }

          if (eventData.find((obj) => obj["x"] == endDate) == undefined) {
            eventData.push({ x: endDate, y: 0, itemCount: 0 });
          }

          // calculate total number of events and total value:
          eventData.map((obj) => {
            if (obj.itemCount) {
              totalEvents += obj.itemCount;
              totalValue += obj.y / 100;
            }
          });

          // get array of days for chart:
          var chartData = [
            {
              x: startDate,
              y: 0,
              itemCount: 0,
            },
            {
              x: endDate,
              y: 0,
              itemCount: 0,
            },
          ];

          var currDate = moment(startDate);
          var lastDate = moment(endDate);

          while (currDate.add(1, "days").diff(lastDate) < 0) {
            chartData.splice(chartData.length - 1, 0, {
              x: currDate.clone().format("YYYY-MM-DD"),
              y: 0,
              itemCount: 0,
            });
          }

          // build final data array for chart:
          _.each(eventData, function (edObj) {
            var index = chartData.findIndex(
              (cdObj) => cdObj["x"] == edObj["x"]
            );
            chartData[index] = edObj;
          });

          chartData = _.sortBy(chartData, "x");
          var labels = _.pluck(chartData, "x");

          var chartSetup = {
            type: "bar",
            data: {
              labels: labels,
              datasets: [
                {
                  label: "Value vs Start Date",
                  backgroundColor: "rgba(119, 114, 255)",
                  data: chartData,
                },
              ],
            },
            options: {
              zoom: {
                enabled: false,
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    var label =
                      data.datasets[tooltipItem.datasetIndex].label || "";

                    if (label) {
                      label += ": ";
                    }
                    label += "$ " + (tooltipItem.yLabel / 100).formatMoney();
                    return label;
                  },
                },
              },
              scales: {
                xAxes: [
                  {
                    ticks: {
                      callback: function (value) {
                        return moment(value).format("MMM-D");
                      },
                    },
                    labelOffset: 0,
                  },
                ],
                yAxes: [
                  {
                    ticks: {
                      callback: function (value) {
                        return "$" + (value / 100).formatMoney();
                      },
                      beginAtZero: false,
                    },
                  },
                ],
              },
            },
          };

          ui.makeNode("chart", "chart", chartSetup);

          // Table
          console.log('Data being passed to draw_table:', {
            chartSetup: chartSetup,
            chartData: chartData,
            rawData: response
          });

          draw_table(
            ui.makeNode("table", "div", { css: "ui padded basic segment" }),
            chartSetup,
            chartData,
            objType
          );

          metricsUI = chartDom.metricsHolder.makeNode("metrics", "div", {});
          metricsUI.empty();
          metricsUI.makeNode("cont", "div", {
            css: "ui raised padded segment",
          });
          metricsUI.cont.makeNode("statistics", "div", {
            css: "ui small statistics",
          });
          metricsUI.cont.statistics.makeNode("statistic1", "div", {
            css: "statistic",
          });
          metricsUI.cont.statistics.makeNode("statistic2", "div", {
            css: "statistic",
          });
          metricsUI.cont.statistics.statistic1.makeNode("val1", "div", {
            css: "value",
            text: totalEvents,
          });
          metricsUI.cont.statistics.statistic1.makeNode("label1", "div", {
            css: "label",
            text: objType,
          });
          metricsUI.cont.statistics.statistic2.makeNode("val2", "div", {
            css: "value",
            text: "$" + totalValue.formatMoney(),
          });
          metricsUI.cont.statistics.statistic2.makeNode("label2", "div", {
            css: "label",
            text: "Total Value",
          });
          chartDom.patch();

          function draw_table(ui, chartSetup, rawData, typeLabel) {
            console.log('draw_table arguments', arguments);
            ui.makeNode("t", "div", { tag: "table", css: "ui striped table" });
            ui.t.makeNode("thead", "div", { tag: "thead" });
            ui.t.thead.makeNode("tr", "div", { tag: "tr" });
            ui.t.thead.tr.makeNode("date", "div", { tag: "th", text: "Date" });
            ui.t.thead.tr.makeNode("venueName", "div", {tag: "th", text: "Venue Name"});
            ui.t.thead.tr.makeNode("venueFee", "div", {tag: "th", text: "Venue Fee"});
            ui.t.thead.tr.makeNode("otherFees", "div", {tag: "th", text: "Other Fees"});
            ui.t.thead.tr.makeNode("value", "div", {
              tag: "th",
              text: "Total Paid",
            });
            ui.t.thead.tr.makeNode("total", "div", {
              tag: "th",
              text: "Total",
            });
            ui.t.makeNode("tbody", "div", { tag: "tbody" });

            _.each(chartSetup.data.datasets[0].data, function (row, i) {
              if (!row.y) {
                return;
              }

              // Venue Row
              ui.t.tbody.makeNode("r" + i, "div", { tag: "tr", css: "venue-row" });

              // Date
              var dateTxt = '<strong>' + moment(row.x).format("MMM DD, YYYY") + '</strong>';
              ui.t.tbody["r" + i].makeNode("d", "div", {
                tag: "td",
                text: dateTxt,
              });

              // Venue Name
              ui.t.tbody["r" + i].makeNode("vn", "div", {
                tag: "td",
                text: row.venueName ? "<strong>" + row.venueName + "</strong>" : "<strong>N/A</strong>",
              });

              // VenueFee
              var venueFeeTxt = row.venueFee !== "N/A" && row.venueFee !== 0 ? `$${(row.venueFee/100).formatMoney()}` : "N/A";
              ui.t.tbody["r" + i].makeNode("vf", "div", {
                tag: "td",
                text: '<strong>' + venueFeeTxt + '</strong>',
              });

              // OtherFees
              var otherFeesTxt = row.otherFees !== "N/A" && row.otherFees !== 0 ? `$${(row.otherFees/100).formatMoney()}` : "N/A";
              ui.t.tbody["r" + i].makeNode("of", "div", {
                tag: "td",
                text: '<strong>' + otherFeesTxt + '</strong>',
              });

              // Value
              var valueTxt = "$" + (row.y / 100).formatMoney();
              ui.t.tbody["r" + i].makeNode("v", "div", {
                tag: "td",
                text: '<strong>' + valueTxt + '</strong>',
              });

              // Count
              ui.t.tbody["r" + i].makeNode("c", "div", {
                tag: "td",
                text: '<strong>' + rawData[i].itemCount + " " + typeLabel + '</strong>',
              });
              console.log('row.events && row.events.length > 0', row.events && row.events.length > 0);
              // Event Rows
              if (row.events && row.events.length > 0) {
                console.log("Processing row:", row);

                row.events.forEach((event, eventIndex) => {

                  console.log('Processing event:', event);

                  const eventRowId = `r${i}_e${eventIndex}`;
                  ui.t.tbody.makeNode(eventRowId, "div", { tag: "tr", css: "event-row" });

                  // Date (same as venue)
                  ui.t.tbody[eventRowId].makeNode("d", "div", {
                    tag: "td",
                    text: '',
                  });

                  // Event Name with Link
                  ui.t.tbody[eventRowId].makeNode("vn", "div", {
                      tag: "td",
                      text: `&nbsp;&nbsp;└ &nbsp;&nbsp;<a href="${sb.data.url.createPageURL(
                          "object",
                          {
                              id: event.id,
                              name: event.name,
                              type: "project",
                          }
                      )}">${
                          event.name
                      } &nbsp;&nbsp;<i class="external alternate tiny icon"></i></a>`,
                  });

                  // Event Fees (if available)
                  ui.t.tbody[eventRowId].makeNode("vf", "div", {
                    tag: "td",
                    text: "-",
                  });

                  ui.t.tbody[eventRowId].makeNode("of", "div", {
                    tag: "td",
                    text: "-",
                  });

                  ui.t.tbody[eventRowId].makeNode("v", "div", {
                      tag: "td",
                      text: event.value
                          ? `${(event.value / 100).formatMoney()}`
                          : "-",
                  });

                  // Count (1 event)
                  ui.t.tbody[eventRowId].makeNode("c", "div", {
                      tag: "td",
                      text: "",
                  });
                });
              }
            });

            ui.t.makeNode("tfoot", "div", { tag: "tfoot" });
            ui.t.tfoot.makeNode("tr", "div", { tag: "tr" });
            ui.t.tfoot.tr.makeNode("date", "div", { tag: "th", text: "" });
            ui.t.tfoot.tr.makeNode("venueName", "div", { tag: "th", text: "" });
            ui.t.tfoot.tr.makeNode("venueFee", "div", { tag: "th", text: "" });
            ui.t.tfoot.tr.makeNode("otherFees", "div", { tag: "th", text: "" });
            ui.t.tfoot.tr.makeNode("value", "div", {
              tag: "th",
              text: "$" + totalValue.formatMoney(),
            });
            ui.t.tfoot.tr.makeNode("total", "div", {
              tag: "th",
              text: totalEvents + " " + typeLabel,
            });
          }
        }
      }, options);
    }

    function get_raw_event_data(callback, options) {
      console.log('get_raw_event_data called with options:', options);

      var get_raw_query = {
          group_type: "Project",
          type: 1625566,
          state: { type: "or", values: [1, 2, 3, 4, 5, 9] },
          is_template: 0
      };

      // Add date range filter if provided
      if (options?.query?.range) {
          // Convert moment objects to UTC formatted date strings
          const startDate = moment.utc(options.query.range.start).format('YYYY-MM-DD HH:mm:ss');
          const endDate = moment.utc(options.query.range.end).format('YYYY-MM-DD HH:mm:ss');

          console.log('Formatted UTC date range:', {
            start: startDate,
            end: endDate
          });

          // Use the 'between' type query format expected by the PHP backend
          get_raw_query.start_date = {
              type: 'between',
              start: startDate,
              end: endDate
          };

          // Log the full query structure
          console.log('Query structure:', JSON.stringify(get_raw_query, null, 2));
      } else {
          console.log('No date range found in options');
      }

      console.log('Final query:', get_raw_query);
      sb.data.db.obj.getWhere(
        "groups",
        get_raw_query,
        function (data) {
          //sb.data.db.obj.getWhere('groups', {group_type: "Project", is_template: 0, childObjs: 0}, function(data){
console.log('Raw event data:', data);
          sb.data.db.service(
            "ProjectService",
            "subtractTaxesFromProjectInvoice",
            data,
            function (modifiedProjects) {
              callback(modifiedProjects);
            }
          );
        }
      );
    }

    chartDom.makeNode("metricsHolder", "div", {
      css: "ui sixteen wide column",
    });
    chartDom.makeNode("chartCont", "div", { css: "ui basic padded segment" });

    draw_chart(chartDom.chartCont.makeNode("chart", "div", {}));

    if (legend.hasOwnProperty("settings")) {
      $(legend.settings.selector).addClass("hidden");
    } else {
      delete ui.right;
    }
  }

  // box views

  function relatedProjectsBoxView(dom, state, draw) {
    var where = {
      group_type: "Project",
      is_template: {
        type: "not_equal",
        value: 1,
      },
    };

    if (state.myStuff) {
      where.tagged_with = +state.myStuff.user;
    } else if (
      !_.isEmpty(state.pageObject) &&
      state.pageObject.group_type !== "Headquarters"
    ) {
      where.tagged_with = state.pageObject.id;
    }

    sb.data.db.obj.getCounts("groups", undefined, where, function (response) {
      var count = 0;
      if (
        !_.isEmpty(response) &&
        !_.isEmpty(response[0]) &&
        response[0].count
      ) {
        count = response[0].count;
      }

      dom.makeNode("stats", "div", { css: "ui horizontal statistics" });
      dom.stats.makeNode("today", "div", { css: "ui one statistic" });
      dom.stats.today.makeNode("value", "div", {
        css: "value",
        text: count,
      });
      dom.stats.today.makeNode("label", "div", {
        css: "label",
        text: "Related projects",
      });

      draw(dom);
    });
  }

  function teamsOverviewBoxView(dom, state, draw) {
    var objectIds = [+sb.data.cookie.get("uid")];
    var where = {
      group_type: "Team",
    };

    if (state.myStuff) {
      where.tagged_with = +state.myStuff.user;
    } else if (
      !_.isEmpty(state.pageObject) &&
      state.pageObject.group_type !== "Headquarters"
    ) {
      where.tagged_with = state.pageObject.id;
    }

    sb.data.db.obj.getCounts("groups", undefined, where, function (response) {
      var count = 0;
      if (
        !_.isEmpty(response) &&
        !_.isEmpty(response[0]) &&
        response[0].count
      ) {
        count = response[0].count;
      }

      dom.makeNode("stats", "div", { css: "ui horizontal statistics" });

      dom.stats.makeNode("today", "div", { css: "ui statistic" });
      dom.stats.today.makeNode("value", "div", { css: "value", text: count });
      dom.stats.today.makeNode("label", "div", {
        css: "label",
        text: "Related teams",
      });

      draw(dom);
    });
  }

  function viewGroupsByType(dom, state, draw, options) {
    var linkedTool = {
      name: "projectTool",
      layer: "hqTool",
    };
    if (state.myStuff) {
      linkedTool.name = "myprojects";
      linkedTool.layer = "myStuffTool";
    }
    if (state.team) {
      linkedTool.name = "projectTool";
      linkedTool.layer = "teamTool";
    }

    if (!state.boxView.settings.options) {
      viewGroupsByTypeSettings(
        dom,
        state.boxView.settings,
        function (saved) {
          state.boxView.updateSettings(saved, function (done) {
            state.boxView.settings = saved;

            viewGroupsByType(dom, state, draw, options);
          });
        },
        options
      );
    } else {
      var objId = state.boxView.settings.options.type;

      sb.data.db.obj.getById("groups", objId, function (currentType) {
        var groupSumSetup = {
          group_type: options.group_type,
          groupOn: "state",
          is_template: 0,
          dateRange: {
            start: moment("20111031", "YYYYMMDD").format(
              "YYYY-MM-DD HH:mm:ss.SS"
            ),
            end: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss.SS"),
          },
          type: objId,
        };

        if (state.team || state.project) {
          groupSumSetup.tagged_with = [state.pageObject.id];
        } else if (state.myStuff) {
          groupSumSetup.tagged_with = [state.user];
        }

        sb.data.db.obj.getGroupSum(
          "groups",
          "id",
          groupSumSetup,
          function (databaseData) {
            dom.empty();

            // Download as csv btn
            dom
              .makeNode("download", "div", {
                tag: "button",
                css: "ui blue icon mini button pull-right",
                text: '<i class="download icon"></i>',
              })
              .notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function () {
                      var csvContent =
                        "data:text/csv;charset=utf-8," + csvString;
                      var encodedUri = encodeURI(csvContent);
                      var link = document.createElement("a");
                      link.setAttribute("href", encodedUri);
                      link.setAttribute("download", "download.csv");
                      document.body.appendChild(link); // Required for FF
                      link.click();
                      return;
                    },
                  },
                },
                sb.moduleId
              );

            dom.makeNode("table", "div", {
              tag: "table",
              css: "ui very basic celled table",
            });

            dom.table.makeNode("thead", "div", {
              tag: "thead",
            });
            dom.table.makeNode("tbody", "div", {
              tag: "tbody",
            });

            dom.table.thead.makeNode("tr", "div", {
              tag: "tr",
            });

            dom.table.thead.tr.makeNode("th1", "div", {
              tag: "th",
              css: "text-muted",
              text: "Status",
              style: "font-weight: normal;",
            });

            dom.table.thead.tr.makeNode("th2", "div", {
              tag: "th",
              css: "text-muted",
              text: "# of projects",
              style: "font-weight: normal;",
            });

            var state = _.findWhere(currentType.states, { isEntryPoint: 1 });
            var i = 0;
            var csvString = "Status, # of Projects\n";

            while (state) {
              dom.table.tbody.makeNode("tr-" + i, "div", {
                tag: "tr",
              });

              dom.table.tbody["tr-" + i].makeNode("td1", "div", {
                tag: "td",
              });

              dom.table.tbody["tr-" + i].td1.makeNode("link", "div", {
                tag: "a",
                css: "ui header",
                text: state.name,
                href: sb.data.url.createPageURL(linkedTool.layer, {
                  name: currentType.name + " | " + state.name,
                  tool: linkedTool.name,
                  toolId: linkedTool.name,
                  params: {
                    type: objId,
                    state: state.id,
                  },
                }),
              });

              var group = _.filter(databaseData, function (group) {
                return parseInt(group.grouped) === state.uid;
              });
              var total = 0;

              if (!_.isEmpty(group)) {
                total = group[0].grouped_total;
              } else {
                total = "0";
              }

              csvString += state.name + "," + total + "\n";

              dom.table.tbody["tr-" + i].makeNode("td2", "div", {
                tag: "td",
                text: total.toString(),
              });

              state = _.findWhere(currentType.states, {
                uid: parseInt(state.next[0]),
              });
              i++;
            }

            dom.patch();
          }
        );
      });
    }
  }

  function viewGroupsByTypeSettings(ui, settings, onComplete, options) {
    // options.type_bp

    var type = options.type_bp;
    var objectName = options.objectName;

    sb.data.db.obj.getAll(type, function (Types) {
      var currentSelection;

      if (settings.options) {
        currentSelection = +settings.options.types;
      }
      var mappedObjs = _.map(Types, function (element, index, list) {
        var selected = false;

        if (element.id === currentSelection) {
          selected = true;
        } else {
          selected = false;
        }

        return {
          name: element.name,
          value: element.id,
          selected: selected,
        };
      });
      ui.empty();

      var formArgs = [
        {
          name: "type",
          type: "select",
          label: "Select a " + objectName + " Type",
          options: mappedObjs,
        },
      ];

      ui.makeNode("form", "form", formArgs);

      ui.makeNode("break", "div", { text: "<br />" });

      ui.makeNode("buttons", "div", { css: "ui mini buttons" });

      ui.buttons
        .makeNode("save", "div", {
          text: "Save",
          css: "ui green button",
        })
        .notify(
          "click",
          {
            type: [sb.moduleId + "-run"],
            data: {
              run: function (ui) {
                ui.buttons.save.loading();
                ui.buttons.cancel.loading();

                var selectedObj = parseInt(ui.form.process().fields.type.value);

                settings.options = {
                  type: selectedObj,
                };

                onComplete(settings);
              }.bind({}, ui),
            },
          },
          sb.moduleId
        );

      ui.buttons.makeNode("or", "div", { css: "or" });

      ui.buttons
        .makeNode("cancel", "div", {
          text: "Cancel",
          css: "ui button",
        })
        .notify(
          "click",
          {
            type: "tasks-run",
            data: {
              run: function (ui) {
                ui.buttons.cancel.loading();
                ui.buttons.save.loading();

                onComplete(settings);
              }.bind({}, ui),
            },
          },
          sb.moduleId
        );

      ui.patch();
    });
  }

  function viewGroupsByTypePieChart(dom, state, draw) {
    if (!state.boxView.settings.options) {
      viewGroupsByTypeSettings(
        dom,
        state.boxView.settings,
        function (saved) {
          state.boxView.updateSettings(saved, function (done) {
            state.boxView.settings = saved;

            viewGroupsByTypePieChart(dom, state, draw);
          });
        },
        {
          type_bp: "project_types",
          objectName: "project",
          object_bp_type: "groups",
          group_type: "Project",
        }
      );
    } else {
      var objId = state.boxView.settings.options.type;

      sb.data.db.obj.getById("project_types", objId, function (type) {
        var groupSumSetup = {
          group_type: "Project",
          groupOn: "state",
          is_template: 0,
          dateRange: {
            start: moment("20111031", "YYYYMMDD").format(
              "YYYY-MM-DD HH:mm:ss.SS"
            ),
            end: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss.SS"),
          },
          type: objId,
        };

        if (state.team || state.project) {
          groupSumSetup.parent = state.pageObject.id;
        } else if (state.myStuff) {
          groupSumSetup.tagged_with = [state.user];
        }

        sb.data.db.obj.getGroupSum(
          "groups",
          "id",
          groupSumSetup,
          function (databaseData) {
            var groupedTaskTotal = [];

            var taskLabels = [];

            var taskColors = [];

            _.each(type.states, function (value, key) {
              taskLabels.push(value.name);
              if (value.color) {
                taskColors.push(value.color);
              } else {
                taskColors.push("grey");
              }

              var unassignedStateCounts = {};

              var additionalCounts = 0;

              if (value.isEntryPoint) {
                unassignedStateCounts = _.findWhere(databaseData, {
                  grouped: "0",
                });
                if (unassignedStateCounts) {
                  additionalCounts += unassignedStateCounts.grouped_total;
                }
              }

              var matchedData = _.findWhere(databaseData, {
                grouped: value.id.toString(),
              });
              if (matchedData) {
                groupedTaskTotal.push(
                  matchedData.grouped_total + additionalCounts
                );
              } else {
                groupedTaskTotal.push(0 + additionalCounts);
              }
            });

            var colors = [];
            _.each(taskColors, function (color) {
              if (sb.dom.colorsNameToHex[color]) {
                colors.push(sb.dom.colorsNameToHex[color]);
              } else {
                colors.push(color);
              }
            });

            var data = {
              labels: taskLabels,
              datasets: [
                {
                  label: "Projects",
                  data: groupedTaskTotal,
                  backgroundColor: colors,
                  borderColor: colors,
                },
              ],
            };

            var options = {
              title: {
                display: false,
                position: "top",
                text: "Projects",
                fontSize: 22,
                fontColor: "red",
              },
              legend: {
                display: false,
                position: "right",
              },
            };

            dom.empty();

            dom.makeNode("pieChart", "chart", {
              type: "pie",
              data: data,
              options: options,
            });

            dom.patch();
          }
        );
      });
    }
  }

  function forms_ui(modal, entity) {

    modal.body.makeNode("div", "div", {});

    modal.body.div.makeNode("loader", "div", {
      css: "ui active inverted dimmer",
      text: '<div class="ui text loader"> Loading </div>'
    });

    modal.body.patch();

    sb.notify(
        {
          type: "view-entity",
          data: {
            id: entity.id,
            ui: modal.body.div,
            options: {
              hideComments: true,
              //toNextInList: viewOptions.toNextInList,
              viewStyle: "form",
              hideTags: true,
              editBlueprint: false,
            },
            onComplete: function () {},
          },
        },
        sb.moduleId
    );

    modal.show();
  }

  return {
    init: function () {
      var is_portal = false;

      if (appConfig.is_portal) is_portal = !!appConfig.is_portal;

      var homeViewsSetup = [
        {
          id: "myprojects",
          type: "myStuff",
          name: "Projects",
          title: "Projects",
          icon: {
            type: "project diagram",
            color: "blue",
          },
          tip: "Organize your work.",
          default: true,
          dom: function (dom, state, draw) {
            ui.sys_cont = dom;
            state.profileSetup = "projects";

            dom.makeNode("title", "div", {
              text: "Projects",
              css: "ui huge header",
            });
            dom.makeNode("cont", "div", { css: "ui loading basic segment" });

            draw({
              dom: dom,
              after: function (dom) {
                profileView(
                  dom.cont,
                  state,
                  draw,
                  function (done) {
                    dom.cont.css("ui basic segment");
                  },
                  "projects"
                );
              },
            });
          },
          boxViews: [
            {
              id: "relatedProjects",
              width: "four",
              title: "Projects",
              dom: relatedProjectsBoxView,
            },
            // 'Projects in Role'
            {
              id: "projectsInRole",
              /* default: true, */
              width: "sixteen",
              title: "Role Sheet Assignments",
              collections: {
                hideDateRange: true,
                selectedView: "table",
                actions: {
                  forcedMenu: true,
                },
                filters: true,
                advancedFilters: {
                  role: true,
                  disabledStates: true,
                },
                fields: {
                  name: {
                    title: "Name",
                    type: "title",
                    isSearchable: true,
                  },
                  client: {
                    title: "Organization",
                    type: "edge",
                    view: function (ui, obj) {
                      if (obj.main_contact) {
                        var ret = "";

                        if (obj.main_contact.company) {
                          if (ui) {
                            ui.makeNode("co", "div", {
                              tag: "a",
                              css: "link truncate",
                              style:
                                "display:block; font-size:16px; padding:0 !important;",
                              text:
                                "<nobr>" +
                                obj.main_contact.company.name +
                                "</nobr>",
                              href: sb.data.url.createPageURL("object-view", {
                                id: obj.main_contact.company.id,
                                name: obj.main_contact.company.name,
                                type: "companies",
                              }),
                            });
                          } else {
                            ret += " at " + obj.main_contact.company.name;
                          }
                        }

                        if (!ui) {
                          return ret;
                        }
                      } else {
                        if (ui) {
                          ui.makeNode("c", "div", {
                            text: '<i class="text-muted">Not set</i>',
                          });
                        } else {
                          return "Not set";
                        }
                      }
                    },
                  },
                  state: {
                    title: "Status",
                    type: "state",
                  },
                  type: {
                    title: "Type",
                    type: "type",
                  },
                  category: {
                    title: "Category",
                    type: "category",
                  },
                  date_created: {
                    title: "Created",
                    type: "title",
                    view: function (ui, obj) {
                      ui.makeNode("date", "div", {
                        css: "date",
                        text: `Created ${moment(obj.date_created)
                          .local()
                          .fromNow()}</span>`,
                      });
                    },
                  },
                  fiscalYearEndDate: {
                    title: "Fiscal Year End Date (on Client Service)",
                    type: "date",
                    hideInMini: true,
                    //is_due_date: true
                  },
                  dueDate: {
                    title: "Due Date (on Client Service)",
                    type: "date",
                    hideInMini: true,
                    //is_due_date: true
                  },
                  last_updated: {
                    title: "Date of Last Activity",
                    type: "date",
                    hideInMini: true,
                  },
                  //note: {
                  //	title: 'Notes',
                  //	type: 'string',
                  //}
                },
                query: function (request, onComplete) {
                  if (request.hasOwnProperty("select")) {
                    request.childObjs = request.select;
                    delete request.select;
                  }

                  if (typeof request.childObjs === "undefined") {
                    childObjs = 0;
                  } else {
                    childObjs = request.childObjs;
                    delete request.childObjs;
                  }

                  var getJust = {};
                  if (request.hasOwnProperty("getJust")) {
                    getJust = request.getJust;
                    delete request.getJust;
                  }

                  sb.data.db.service(
                    "FGProjectsByRole",
                    "getCollectionData",
                    {
                      objectType: "groups",
                      queryObj: request,
                      getChildObjs: childObjs,
                      getJust: getJust,
                    },
                    function (response) {
                      onComplete(response);
                    }
                  );
                },
                pageLength: 10,
                objectType: "groups",
                menu: {
                  actions: true,
                  subviews: {
                    list: true,
                    table: true,
                  },
                },
                where: function (state) {
                  var tags = [parseInt(sb.data.cookie.userId)];
                  if (
                    state.boxView &&
                    state.boxView.settings &&
                    state.boxView.settings.options &&
                    Array.isArray(state.boxView.settings.options.additionalTags)
                  ) {
                    _.each(
                      state.boxView.settings.options.additionalTags,
                      function (t) {
                        tags.push(parseInt(t));
                      }
                    );
                  }
                  return {
                    group_type: "Project",
                    tagged_with: tags,
                    childObjs: {
                      name: true,
                      group_type: true,
                      state: true,
                      type: true,
                      category: {
                        name: true,
                      },
                      main_contact: {
                        fname: true,
                        lname: true,
                        company: { name: true },
                      },
                      managers: {
                        fname: true,
                        lname: true,
                        profile_image: true,
                      },
                      start_date: true,
                      end_date: true,
                      invoice_value: true,
                      invoice_value_no_tax: true,
                      last_updated: true,
                    },
                  };
                },
                subviews: {
                  list: {},
                  table: {},
                },
              },
              settingsView: function (ui, settings, onComplete) {
                var selectedTags = [];
                if (
                  settings &&
                  settings.options &&
                  Array.isArray(settings.options.additionalTags)
                ) {
                  _.each(settings.options.additionalTags, function (t) {
                    selectedTags.push(parseInt(t));
                  });
                }

                comps.bvSettingsTags = sb.createComponent("tags");

                ui.makeNode("label", "div", {
                  text: "<h5>Additional tag(s)</h5>",
                });
                ui.makeNode("tags", "div", {});
                ui.makeNode("br2", "lineBreak", {});

                // Save btn
                ui.makeNode("save", "div", {
                  text: "Save",
                  css: "ui green button",
                }).notify(
                  "click",
                  {
                    type: [sb.moduleId + "-run"],
                    data: {
                      run: function (ui) {
                        ui.save.loading();

                        if (_.isEmpty(settings.options)) {
                          settings.options = {};
                        }

                        settings.options.additionalTags = selectedTags;
                        onComplete(settings);
                      }.bind({}, ui),
                    },
                  },
                  sb.moduleId
                );

                ui.patch();

                comps.bvSettingsTags.notify({
                  type: "object-tag-view",
                  data: {
                    domObj: ui.tags,
                    objectType: "",
                    tags: selectedTags,
                    onChange: function (response) {
                      selectedTags = _.pluck(response, "id");
                    },
                    canEdit: true,
                  },
                });
              },
            },
            // 'Recent Projects'
            {
              id: "recentProjects",
              /* default: true, */
              width: "sixteen",
              title: "Recent Projects",
              collections: {
                selectedView: "list",
                actions: {},
                fields: {
                  name: {
                    title: "Name",
                    type: "title",
                    isSearchable: true,
                  },
                  date_created: {
                    title: "Created",
                    type: "title",
                    view: function (ui, obj) {
                      ui.makeNode("date", "div", {
                        css: "date",
                        text: `Created ${moment(obj.date_created)
                          .local()
                          .fromNow()}</span>`,
                      });
                    },
                  },
                  state: {
                    title: "Status",
                    type: "state",
                  },
                  type: {
                    title: "Type",
                    type: "type",
                  },
                },
                pageLength: 10,
                objectType: "groups",
                where: function (state) {
                  var tags = [parseInt(sb.data.cookie.userId)];
                  if (
                    state.boxView &&
                    state.boxView.settings &&
                    state.boxView.settings.options &&
                    Array.isArray(state.boxView.settings.options.additionalTags)
                  ) {
                    _.each(
                      state.boxView.settings.options.additionalTags,
                      function (t) {
                        tags.push(parseInt(t));
                      }
                    );
                  }

                  return {
                    group_type: "Project",
                    tagged_with: tags,
                    childObjs: {
                      name: true,
                      group_type: true,
                      state: true,
                      type: true,
                    },
                  };
                },
                subviews: {
                  list: {
                    hideTimeRangeFilter: true,
                  },
                },
              },
              settingsView: function (ui, settings, onComplete) {
                var selectedTags = [];
                if (
                  settings &&
                  settings.options &&
                  Array.isArray(settings.options.additionalTags)
                ) {
                  _.each(settings.options.additionalTags, function (t) {
                    selectedTags.push(parseInt(t));
                  });
                }

                comps.bvSettingsTags = sb.createComponent("tags");

                ui.makeNode("label", "div", {
                  text: "<h5>Additional tag(s)</h5>",
                });
                ui.makeNode("tags", "div", {});
                ui.makeNode("br2", "lineBreak", {});

                // Save btn
                ui.makeNode("save", "div", {
                  text: "Save",
                  css: "ui green button",
                }).notify(
                  "click",
                  {
                    type: [sb.moduleId + "-run"],
                    data: {
                      run: function (ui) {
                        ui.save.loading();

                        if (_.isEmpty(settings.options)) {
                          settings.options = {};
                        }

                        settings.options.additionalTags = selectedTags;
                        onComplete(settings);
                      }.bind({}, ui),
                    },
                  },
                  sb.moduleId
                );

                ui.patch();

                comps.bvSettingsTags.notify({
                  type: "object-tag-view",
                  data: {
                    domObj: ui.tags,
                    objectType: "",
                    tags: selectedTags,
                    onChange: function (response) {
                      selectedTags = _.pluck(response, "id");
                    },
                    canEdit: true,
                  },
                });
              },
            },

            // 'Action Items RCA'
            {
              id: "rcaForms",
              /* default: true, */
              width: "sixteen",
              title: "Action Items RCA",
              collections: {
                selectedView: "list",
                actions: {},
                fields: {
                  name: {
                    title: "Name",
                    type: "title",
                    isSearchable: true,
                    view: function (ui, obj) {

                      ui.makeNode('co', 'div', {text: obj.name, css:'ui link truncate'}).notify('click', {
                        type:'companyComponent-run',
                        data:{
                          run: function(){

                            ui.makeNode('code', 'modal', { css: "ui modal" });

                            ui.code.body.makeNode("menu", "div", {
                              css: "ui secondary right floated menu",
                              style: "margin:0 !important;",
                            });

                            var closeLinkSetup = {
                              css: "circular icon button item",
                              text: '<i class="close icon"></i>',
                              tag: "a",
                            };

                            ui.code.body.menu.makeNode("close", "div", closeLinkSetup).notify(
                                "click",
                                {
                                  type: "collections-run",
                                  data: {
                                    run: function (item) {
                                      ui.code.hide();
                                    }.bind({}, ui),
                                  },
                                },
                                sb.moduleId
                            );

                            ui.patch();

                            forms_ui(ui.code, obj);

                          }.bind(ui)
                        }
                      }, sb.moduleId);
                    }
                  },
                  status: {
                    title: "Status",
                    type: "status",
                  },
                  last_updated: {
                    title: 'Last updated',
                    type: "date"
                  }
                },
                pageLength: 10,
                objectType: "any",
                parseData: function(data, callback, query){
                  var recentClientActivity = 8220204; //prod: 8220204

                  sb.data.db.obj.getWhere('any', {
                    tagged_with: {
                      type: 'any',
                      values: [recentClientActivity]
                    },
                    childObjs: {
                      name: true,
                      last_updated: true,
                      status: true
                    },
                    paged: query.paged
                  }, function(objs){

                  var dataObj = objs.data;

                  var submittedBtn = "<div id=\"\" class=\"ui item green button  main-div-bottomGrid-div-col3-div-center-div-seg2-div-Body-div-Body-div-container-div-col-div-g-0-div-container-div-items-div-cont-8012097-div-body-div-f-div-btns-8012097-div-state-div-menu-div-btn-2 workflows-run-click-state-field-main-div-bottomGrid-div-col3-div-center-div-seg2-div-Body-div-Body-div-container-div-col-div-g-0-div-container-div-items-div-cont-8012097-div-body-div-f-div-btns-8012097-div-state-div-menu-div-btn-2-undefined \" style=\"border-radius:0px !important;border:none !important;\"> <nobr><i class=\"check circle outline icon\"></i> Submitted </nobr></div>";
                  var submitBtn = "<div id=\"\" class=\"ui blue dropdown fluid mini button main-div-bottomGrid-div-col3-div-center-div-seg2-div-Body-div-Body-div-container-div-col-div-g-0-div-container-div-items-div-cont-8012174-div-body-div-f-div-btns-8012174-div-state \" style=\"border-radius:0px !important;border:none !important;\"> <nobr><i class=\"arrow right icon\"></i> Submit </nobr></div>";

                   dataObj = _.map(dataObj, function(data){
                      data.status = (data.status === 'done') ? submittedBtn : submitBtn;
                      return data;
                    });


                    dataObj = _.filter(dataObj, function(data){
                      if(data.object_bp_type != 'notes' && data.object_bp_type != 'notification'){
                        return true;
                      }
                      return false;
                    });



                    data.data = dataObj;
                    callback(data);

                  }, 1);
                },
                where: function (state) {
                  var recentClientActivity = 8220204; //prod: 8220204
                  return {
                    tagged_with: {
                      type: 'any',
                        values: [recentClientActivity]
                    },
                    childObjs: {
                      name: true,
                      last_updated: true,
                      status: true
                    },
                  };
                },
                subviews: {
                  list: {
                    hideTimeRangeFilter: true,
                  },
                },
              },
            },

            // Projects by state table
            {
              id: "projectsByState",
              width: "eight",
              title: "Projects by Status Table",
              dom: function (dom, state, draw) {
                viewGroupsByType(dom, state, draw, {
                  type_bp: "project_types",
                  objectName: "project",
                  object_bp_type: "groups",
                  group_type: "Project",
                });
              },
              settingsView: function (ui, settings, onComplete) {
                viewGroupsByTypeSettings(ui, settings, onComplete, {
                  type_bp: "project_types",
                  objectName: "project",
                  object_bp_type: "groups",
                  group_type: "Project",
                });
              },
              link: function (state) {
                var linkedTool = {
                  name: "projectTool",
                  layer: "hqTool",
                };
                if (state.myStuff) {
                  linkedTool.name = "myprojects";
                  linkedTool.layer = "myStuffTool";
                }
                if (state.team) {
                  linkedTool.name = "projectTool";
                  linkedTool.layer = "teamTool";
                }

                if (
                  !state ||
                  !state.boxView ||
                  !state.boxView.settings ||
                  !state.boxView.settings.options ||
                  !state.boxView.settings.options.type
                ) {
                  return sb.data.url.createPageURL(linkedTool.layer, {
                    tool: linkedTool.name,
                  });
                }

                var objId = state.boxView.settings.options.type;

                return sb.data.url.createPageURL(linkedTool.layer, {
                  tool: linkedTool.name,
                  params: {
                    type: objId,
                  },
                });
              },
            },
            // Projects by state pie chart
            {
              id: "projectsGroupedByState",
              width: "eight",
              title: "Projects by Status Pie Chart",
              dom: viewGroupsByTypePieChart,
              settingsView: function (ui, settings, onComplete) {
                viewGroupsByTypeSettings(ui, settings, onComplete, {
                  type_bp: "project_types",
                  objectName: "project",
                  object_bp_type: "groups",
                  group_type: "Project",
                });
              },
            },
            // Project Status by Type
            {
              id: "projectStatusByType",
              width: "eight",
              title: "Project Status by Type",
              dom: function (ui, state, draw) {
                var userID = "";
                if (state.myStuff) {
                  userID = state.myStuff.user;
                } else {
                  userID = state.pageObject.id;
                }

                sb.data.db.obj.getAll("project_types", function (types) {
                  var typeIds = _.pluck(types, "id");

                  sb.data.db.obj.getGroupSum(
                    "groups",
                    "id",
                    {
                      group_type: "Project",
                      groupOn: "type",
                      type: {
                        type: "or",
                        values: typeIds,
                      },
                      is_template: {
                        type: "not_equal",
                        value: 1,
                      },
                      dateRange: {
                        start: moment("20111031", "YYYYMMDD").format(
                          "YYYY-MM-DD HH:mm:ss.SS"
                        ),
                        end: moment()
                          .add(1, "year")
                          .format("YYYY-MM-DD HH:mm:ss.SS"),
                      },
                      tagged_with: [userID],
                    },
                    function (allCounts) {
                      sb.data.db.obj.getGroupSum(
                        "groups",
                        "id",
                        {
                          group_type: "Project",
                          groupOn: "type",
                          type: {
                            type: "or",
                            values: typeIds,
                          },
                          is_template: {
                            type: "not_equal",
                            value: 1,
                          },
                          dateRange: {
                            start: moment("20111031", "YYYYMMDD").format(
                              "YYYY-MM-DD HH:mm:ss.SS"
                            ),
                            end: moment()
                              .add(1, "year")
                              .format("YYYY-MM-DD HH:mm:ss.SS"),
                          },
                          tagged_with: [userID],
                          status: {
                            type: "not_equal",
                            value: "done",
                          },
                        },
                        function (openCounts) {
                          ui.empty();

                          ui.makeNode("t", "div", {
                            tag: "table",
                            css: "ui compact very basic celled table",
                          });
                          ui.t.makeNode("b", "div", { tag: "tbody" });

                          _.each(allCounts, function (c) {
                            var type = _.findWhere(types, {
                              id: parseInt(c.grouped),
                            });

                            if (type) {
                              var link = sb.data.url.createPageURL(
                                "myStuffTool",
                                {
                                  tool: "myprojects",
                                  params: {
                                    type: type.id,
                                  },
                                }
                              );

                              ui.t.b
                                .makeNode("r-" + c.grouped, "div", {
                                  tag: "tr",
                                })
                                .makeNode("l", "div", {
                                  tag: "td",
                                  text:
                                    '<a href="' +
                                    link +
                                    '">' +
                                    type.name +
                                    "</a>",
                                });

                              var statusLabel =
                                '<div class="ui green label">Complete</div>';
                              if (
                                _.findWhere(openCounts, { grouped: c.grouped })
                              ) {
                                statusLabel =
                                  '<div class="ui yellow label">Action Needed</div>';
                              }

                              ui.t.b["r-" + c.grouped].makeNode(
                                "status",
                                "div",
                                {
                                  tag: "td",
                                  text: statusLabel,
                                }
                              );
                            }
                          });

                          draw(ui);
                        }
                      );
                    }
                  );
                });
              },
            },
            // Project Status by Type
            {
              id: "projectStatusByCat",
              width: "eight",
              title: "Project Status by Category",
              dom: function (ui, state, draw) {
                var userID = "";
                if (state.myStuff) {
                  userID = state.myStuff.user;
                } else {
                  userID = state.pageObject.id;
                }

                sb.data.db.obj.getAll("categories", function (types) {
                  var typeIds = _.pluck(types, "id");

                  sb.data.db.obj.getGroupSum(
                    "groups",
                    "id",
                    {
                      group_type: "Project",
                      groupOn: "category",
                      category: {
                        type: "or",
                        values: typeIds,
                      },
                      is_template: {
                        type: "not_equal",
                        value: 1,
                      },
                      dateRange: {
                        start: moment("20111031", "YYYYMMDD").format(
                          "YYYY-MM-DD HH:mm:ss.SS"
                        ),
                        end: moment()
                          .add(1, "year")
                          .format("YYYY-MM-DD HH:mm:ss.SS"),
                      },
                      tagged_with: [userID],
                    },
                    function (allCounts) {
                      sb.data.db.obj.getGroupSum(
                        "groups",
                        "id",
                        {
                          group_type: "Project",
                          groupOn: "category",
                          category: {
                            type: "or",
                            values: typeIds,
                          },
                          is_template: {
                            type: "not_equal",
                            value: 1,
                          },
                          dateRange: {
                            start: moment("20111031", "YYYYMMDD").format(
                              "YYYY-MM-DD HH:mm:ss.SS"
                            ),
                            end: moment()
                              .add(1, "year")
                              .format("YYYY-MM-DD HH:mm:ss.SS"),
                          },
                          tagged_with: [userID],
                          status: {
                            type: "not_equal",
                            value: "done",
                          },
                        },
                        function (openCounts) {
                          ui.empty();

                          ui.makeNode("t", "div", {
                            tag: "table",
                            css: "ui compact very basic celled table",
                          });
                          ui.t.makeNode("b", "div", { tag: "tbody" });

                          _.each(allCounts, function (c) {
                            var type = _.findWhere(types, {
                              id: parseInt(c.grouped),
                            });

                            if (type) {
                              var link = sb.data.url.createPageURL(
                                "myStuffTool",
                                {
                                  tool: "myprojects",
                                  params: {
                                    c: type.id,
                                  },
                                }
                              );

                              ui.t.b
                                .makeNode("r-" + c.grouped, "div", {
                                  tag: "tr",
                                })
                                .makeNode("l", "div", {
                                  tag: "td",
                                  text:
                                    '<a href="' +
                                    link +
                                    '">' +
                                    type.name +
                                    "</a>",
                                });

                              var statusLabel =
                                '<div class="ui green label">Complete</div>';
                              if (
                                _.findWhere(openCounts, { grouped: c.grouped })
                              ) {
                                statusLabel =
                                  '<div class="ui yellow label">Action Needed</div>';
                              }

                              ui.t.b["r-" + c.grouped].makeNode(
                                "status",
                                "div",
                                {
                                  tag: "td",
                                  text: statusLabel,
                                }
                              );
                            }
                          });

                          draw(ui);
                        }
                      );
                    }
                  );
                });
              },
            },
          ],
        },
        {
          id: "my-projects",
          type: "nav-item",
          name: "Projects",
          title: "Projects",
          icon: {
            type: "folder open",
            color: "blue",
          },
          /* default:true, */
          dom: function (dom, state, draw) {
            ui.sys_cont = dom;
            state.profileSetup = "projects";

            dom.makeNode("title", "div", {
              text: "Projects",
              css: "ui huge header",
            });
            dom.makeNode("cont", "div", { css: "ui loading basic segment" });

            draw({
              dom: dom,
              after: function (dom) {
                profileView(
                  dom.cont,
                  state,
                  draw,
                  function (done) {
                    dom.cont.css("ui basic segment");
                  },
                  "projects"
                );
              },
            });
          },
        },
        {
          id: "myteams",
          type: "myStuff",
          name: "Teams",
          title: "Teams",
          icon: {
            type: "users",
            color: "blue",
          },
          // default:true,
          dom: function (dom, state, draw) {
            ui.sys_cont = dom;

            dom.makeNode("title", "div", {
              css: "ui huge header",
              text: "My Teams",
            });
            dom.makeNode("cont", "div", { css: "ui basic loading segment" });

            draw({
              dom: dom,
              after: function (dom) {
                state.profileSetup = "teams";
                profileView(
                  dom.cont,
                  state,
                  draw,
                  function (done) {
                    dom.cont.css("ui basic segment");
                  },
                  "",
                  true
                );
              },
            });
          },
          boxViews: [
            {
              id: "teamOverview",
              width: "four",
              title: "Team Total",
              dom: teamsOverviewBoxView,
            },
            // Teams List
            {
              id: "teamsList",
              width: "sixteen",
              title: "Teams",
              position: 0,
              collections: {
                selectedView: "list",
                actions: {},
                fields: {
                  name: {
                    title: "Name",
                    type: "title",
                    isSearchable: true,
                  },
                },
                pageLength: 10,
                objectType: "groups",
                emptyMessage: "No teams created recently",
                where: function (state) {
                  return {
                    group_type: "Team",
                    tagged_with: [sb.data.cookie.userId],
                    childObjs: {
                      name: true,
                      group_type: true,
                    },
                  };
                },
                groupings: {},
                subviews: {
                  list: {
                    hideTimeRangeFilter: true,
                  },
                },
              },
            },
          ],
        },
        {
          id: "my-teams",
          type: "nav-item",
          name: "Teams",
          title: "Teams",
          icon: {
            type: "users",
            color: "blue",
          },
          /* default:true, */
          dom: function (dom, state, draw) {
            ui.sys_cont = dom;

            dom.makeNode("title", "div", {
              css: "ui huge header",
              text: "Teams",
            });
            dom.makeNode("cont", "div", { css: "" });

            draw({
              dom: dom,
              after: function (dom) {
                state.profileSetup = "teams";
                profileView(dom.cont, state, draw, function (done) {
                  //domCont.css('ui basic segment');
                });
              },
            });
          },
        },
      ];

      var teamSingleViewMenu = {
        referral_form: {
          action: function (obj, dom, state, onComplete) {
            window.open(
              "https://bento.infinityhospitality.net/partners?&partner=" + obj.id,
              "_blank"
            );
          },
          icon: "grey cog",
          title: "Referral Form",
        },
        manage_tools: {
          action: function (obj, dom, state, onComplete) {
            manageTeamTools(
              dom,
              state,
              function (dom) {
                if (dom.patch) {
                  dom.patch();
                } else if (dom.dom) {
                  dom.dom.patch();
                  dom.after(dom.dom);
                }
              },
              onComplete
            );
          },
          icon: "grey cog",
          title: "Manage Apps",
        },
        move: {
          icon: "teal truck",
          title: "Move Team",
          action: function (obj, dom, state, draw) {
            ui_moveGroup(dom, obj, state);
          },
        },
        archive: true,
        scheduleEmails: {
          icon: "grey envelope",
          title: "Schedule Emails",
          action: function (obj, dom, state, draw) {
            ui_scheduleEmails(dom, obj, state);
          },
        },
      };

      if (appConfig.instance != "voltzsoftware") {
        delete teamSingleViewMenu.referral_form;
      }

      if (sb.dom.isMobile) {
        homeViewsSetup[0].id = "home";
        homeViewsSetup[0].title = "Home";
        homeViewsSetup[0].dom = function (dom, state, draw) {
          ui.sys_cont = dom;

          state.profileSetup = "projects_teams";
          profileView(dom, state, draw, function (done) {
            //domCont.css('ui basic segment');
          });
        };

        delete homeViewsSetup[1];
      }

      // Project tool registrations
      sb.notify({
        type: "register-tool",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "projectTools",
            title: "Projects",
            icon: '<i class="fa fa-project-diagram"></i>',
            views: [
              // Main project tool
              {
                id: "projectTool",
                //type:'hqTool',
                layers: ["hq", "team"],
                name: "Projects",
                tip: "Organize your work.",
                icon: {
                  type: "project diagram",
                  color: "blue",
                },
                /* default:true, */
                settings: [
                  {
                    object_type: "project_types",
                    name: "Types and Workflows",
                    action: projectTypesView,
                  },
                  {
                    name: "Proposal Approval Settings",
                    object_type: "proposals",
                    action: requireProposalApproval,
                  },
                  {
                    name: "Vendor Approval Settings",
                    object_type: "vendors",
                    action: requireInvoiceVendorApproval,
                  },
                  {
                    object_type: "categories",
                    name: "Categories",
                    action: categoriesSettings,
                  },
                ],
                newButton: {
                  text: "New Project",
                  action: createNewProject,
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom) {
                      build_projects_collection(dom, state, draw, mainDom, {});
                    },
                  },
                ],
                boxViews: [
                  {
                    id: "relatedProjects",
                    width: "four",
                    title: "Projects",
                    dom: relatedProjectsBoxView,
                  },
                  // Projects in Role
                  {
                    id: "projectsInRole",
                    // default: true,
                    width: "sixteen",
                    title: "Projects in Role",
                    collections: {
                      selectedView: "table",
                      actions: {},
                      fields: {
                        name: {
                          title: "Name",
                          type: "title",
                          isSearchable: true,
                        },
                        company: {
                          title: "Company",
                          type: "link",
                          view: function (ui, obj) {
                            if (
                              obj.main_contact === null ||
                              obj.main_contact === false
                            ) {
                              ui.makeNode("none", "div", {
                                text: "N/A",
                              });
                            } else {
                              ui.makeNode("link", "div", {
                                tag: "a",
                                css: "link",
                                text:
                                  '<i class="linkify icon"></i> ' +
                                  obj.main_contact.company.name,
                                href: sb.data.url.createPageURL("object-view", {
                                  id: obj.main_contact.company.id,
                                  name: obj.main_contact.company.name,
                                  type: "companies",
                                }),
                              });
                            }
                          },
                        },
                        last_updated: {
                          title: "Last Updated",
                          type: "title",
                          view: function (ui, obj) {
                            ui.makeNode("date", "div", {
                              css: "date",
                              text: `Last updated ${moment(obj.last_updated)
                                .local()
                                .fromNow()}</span>`,
                            });
                          },
                        },
                        state: {
                          title: "Status",
                          type: "state",
                          edit: !is_portal,
                        },
                        type: {
                          title: "Type",
                          type: "type",
                        },
                      },
                      pageLength: 10,
                      objectType: "groups",
                      where: {
                        group_type: "Project",
                        childObjs: {
                          last_updated: true,
                          name: true,
                          group_type: true,
                          state: true,
                          type: true,
                          main_contact: {
                            company: true,
                          },
                        },
                      },
                      subviews: {
                        table: {
                          hideTimeRangeFilter: true,
                        },
                      },
                      canEdit: false,
                    },
                  },
                  // Recent Projects
                  {
                    id: "recentProjects",
                    // default: true,
                    width: "sixteen",
                    title: "Recent Projects",
                    collections: {
                      selectedView: "table",
                      actions: {},
                      fields: {
                        name: {
                          title: "Name",
                          type: "title",
                          isSearchable: true,
                        },
                        company: {
                          title: "Company",
                          type: "link",
                          view: function (ui, obj) {
                            if (
                              obj.main_contact === null ||
                              obj.main_contact === false
                            ) {
                              ui.makeNode("none", "div", {
                                text: "N/A",
                              });
                            } else {
                              ui.makeNode("link", "div", {
                                tag: "a",
                                css: "link",
                                text:
                                  '<i class="linkify icon"></i> ' +
                                  obj.main_contact.company.name,
                                href: sb.data.url.createPageURL("object-view", {
                                  id: obj.main_contact.company.id,
                                  name: obj.main_contact.company.name,
                                  type: "companies",
                                }),
                              });
                            }
                          },
                        },
                        last_updated: {
                          title: "Last Updated",
                          type: "title",
                          view: function (ui, obj) {
                            ui.makeNode("date", "div", {
                              css: "date",
                              text: `Last updated ${moment(obj.last_updated)
                                .local()
                                .fromNow()}</span>`,
                            });
                          },
                        },
                        state: {
                          title: "Status",
                          type: "state",
                          edit: !is_portal,
                        },
                        type: {
                          title: "Type",
                          type: "type",
                        },
                      },
                      pageLength: 10,
                      objectType: "groups",
                      where: {
                        group_type: "Project",
                        childObjs: {
                          last_updated: true,
                          name: true,
                          group_type: true,
                          state: true,
                          type: true,
                          main_contact: {
                            company: true,
                          },
                        },
                      },
                      subviews: {
                        table: {
                          hideTimeRangeFilter: true,
                        },
                      },
                      canEdit: false,
                    },
                  },
                  // Upcoming Projects
                  {
                    id: "upcomingProjects",
                    width: "sixteen",
                    title: "Upcoming Projects",
                    collections: {
                      selectedView: "list",
                      actions: {},
                      fields: {
                        name: {
                          title: "Name",
                          type: "title",
                          isSearchable: true,
                        },
                        end_date: {
                          title: "Due ",
                          type: "title",
                          //is_due_date: true,
                          view: function (ui, obj) {
                            var end_date = "";

                            if (obj.end_date !== "") {
                              end_date =
                                "Due " + moment(obj.end_date).local().fromNow();
                            }

                            ui.makeNode("date", "div", {
                              css: "date",
                              text: end_date,
                            });
                          },
                        },
                        state: {
                          title: "Status",
                          type: "state",
                        },
                        type: {
                          title: "Type",
                          type: "type",
                        },
                      },
                      pageLength: 10,
                      objectType: "groups",
                      where: {
                        group_type: "Project",
                        end_date: {
                          type: "between",
                          start: moment().unix(),
                          end: moment().add(7, "days").unix(),
                        },
                        childObjs: {
                          name: true,
                          group_type: true,
                          state: true,
                          type: true,
                          end_date: true,
                        },
                      },
                      subviews: {
                        list: {
                          hideTimeRangeFilter: true,
                        },
                      },
                    },
                  },
                  // Projects by Type
                  {
                    id: "projectsByType",
                    width: "eight",
                    title: "Projects by Type",
                    collections: {
                      fields: {
                        name: {
                          title: "Name",
                          type: "title",
                          link: function (o) {
                            return sb.data.url.createPageURL("hqTool", {
                              tool: "projectTool",
                              params: {
                                type: o.id,
                              },
                            });
                          },
                        },
                        projects: {
                          title: "# of projects",
                          view: function (ui, obj) {
                            ui.makeNode("total", "div", {
                              text: obj.projectsTotal || "0",
                              css: "text-center",
                            });

                            ui.patch();
                          },
                        },
                        tags: {
                          view: function () {
                            return false;
                          },
                        },
                        select: {
                          view: function () {
                            return false;
                          },
                        },
                      },
                      showPaging: true,
                      selectedView: "table",
                      pageLength: 10,
                      objectType: "project_types",
                      emptyMessage: "No project types available",
                      subviews: {
                        table: {
                          style: "padding: 0 !important",
                          hideTimeRangeFilter: true,
                        },
                      },
                      parseData: function (
                        data,
                        callback,
                        query,
                        subview,
                        range,
                        types
                      ) {
                        var typeIds = _.pluck(data.data, "id");

                        sb.data.db.obj.getGroupSum(
                          "groups",
                          "id",
                          {
                            group_type: "Project",
                            groupOn: "type",
                            type: {
                              type: "or",
                              values: typeIds,
                            },
                            is_template: 0,
                            dateRange: {
                              start: moment("20111031", "YYYYMMDD").format(
                                "YYYY-MM-DD HH:mm:ss.SS"
                              ),
                              end: moment()
                                .add(1, "year")
                                .format("YYYY-MM-DD HH:mm:ss.SS"),
                            },
                          },
                          function (metrics) {
                            _.each(metrics, function (metric) {
                              var type = _.findWhere(data.data, {
                                id: parseInt(metric["0"]),
                              });

                              type.projectsTotal = metric.grouped_total;
                            });

                            data.data = _.sortBy(data.data, "name");

                            callback(data);
                          }
                        );
                      },
                      where: {
                        childObjs: {
                          name: true,
                        },
                      },
                    },
                  },
                  // Projects by state table
                  {
                    id: "projectsByState",
                    width: "eight",
                    title: "Projects by Status Table",
                    dom: function (dom, state, draw) {
                      viewGroupsByType(dom, state, draw, {
                        type_bp: "project_types",
                        objectName: "project",
                        object_bp_type: "groups",
                        group_type: "Project",
                      });
                    },
                    settingsView: function (ui, settings, onComplete) {
                      viewGroupsByTypeSettings(ui, settings, onComplete, {
                        type_bp: "project_types",
                        objectName: "project",
                        object_bp_type: "groups",
                        group_type: "Project",
                      });
                    },
                    link: function (state) {
                      var linkedTool = {
                        name: "projectTool",
                        layer: "hqTool",
                      };
                      if (state.myStuff) {
                        linkedTool.name = "myprojects";
                        linkedTool.layer = "myStuffTool";
                      }
                      if (state.team) {
                        linkedTool.name = "projectTool";
                        linkedTool.layer = "teamTool";
                      }

                      if (
                        !state ||
                        !state.boxView ||
                        !state.boxView.settings ||
                        !state.boxView.settings.options ||
                        !state.boxView.settings.options.type
                      ) {
                        return sb.data.url.createPageURL(linkedTool.layer, {
                          tool: linkedTool.name,
                        });
                      }

                      var objId = state.boxView.settings.options.type;

                      return sb.data.url.createPageURL(linkedTool.layer, {
                        tool: linkedTool.name,
                        params: {
                          type: objId,
                        },
                      });
                    },
                  },
                  // Projects by state pie chart
                  {
                    id: "projectsGroupedByState",
                    width: "eight",
                    title: "Projects by Status Pie Chart",
                    dom: viewGroupsByTypePieChart,
                    settingsView: function (ui, settings, onComplete) {
                      viewGroupsByTypeSettings(ui, settings, onComplete, {
                        type_bp: "project_types",
                        objectName: "project",
                        object_bp_type: "groups",
                        group_type: "Project",
                      });
                    },
                  },
                  // Project calendar
                  {
                    id: "projectsCalendar",
                    width: "sixteen",
                    title: "Projects Calendar",
                    dom: function (ui, state, draw) {
                      if (comps.hasOwnProperty("projectsCalendar")) {
                        comps.projectsCalendar.destroy();
                      }

                      comps.projectsCalendar = sb.createComponent("calendar");

                      comps.projectsCalendar.notify({
                        type: "show-calendar",
                        data: {
                          domObj: ui,
                          events: function (callback, range) {
                            var ret = [];
                            var firstListIds = [];

                            delete range.type;

                            sb.data.db.obj.getWhere(
                              "groups",
                              {
                                group_type: "Project",
                                start_date: {
                                  type: "overlap",
                                  endField: "end_date",
                                  range: range,
                                },
                                childObjs: {
                                  name: true,
                                  description: true,
                                  start_date: true,
                                  end_date: true,
                                },
                              },
                              function (projects) {
                                _.each(projects, function (proj) {
                                  proj.start_date = moment(
                                    proj.start_date
                                  ).subtract(sb.dom.utcOffset, "hours");
                                  proj.end_date = moment(
                                    proj.end_date
                                  ).subtract(sb.dom.utcOffset, "hours");

                                  var endDate = proj.end_date;

                                  if (!endDate.isValid()) {
                                    endDate = proj.start_date.add(1, "hour");
                                  }

                                  ret.push({
                                    id: proj.id,
                                    name: proj.name,
                                    objectType: "project",
                                    startTime: proj.start_date,
                                    endTime: endDate,
                                    description: proj.description,
                                    color: proj.color || "blue",
                                  });

                                  firstListIds.push(proj.id);
                                });

                                range.type = "between";

                                sb.data.db.obj.getWhere(
                                  "groups",
                                  {
                                    group_type: "Project",
                                    start_date: range,
                                    childObjs: {
                                      name: true,
                                      description: true,
                                      start_date: true,
                                      end_date: true,
                                    },
                                  },
                                  function (projects2) {
                                    _.each(projects, function (proj) {
                                      if (!_.contains(firstListIds, proj.id)) {
                                        proj.start_date = moment(
                                          proj.start_date
                                        ).subtract(sb.dom.utcOffset, "hours");
                                        proj.end_date = moment(
                                          proj.end_date
                                        ).subtract(sb.dom.utcOffset, "hours");

                                        var endDate = proj.end_date;

                                        if (!endDate.isValid()) {
                                          endDate = proj.start_date.add(
                                            1,
                                            "hour"
                                          );
                                        }

                                        ret.push({
                                          id: proj.id,
                                          name: proj.name,
                                          objectType: "project",
                                          startTime: proj.start_date,
                                          endTime: endDate,
                                          description: proj.description,
                                          color: proj.color || "blue",
                                        });
                                      }
                                    });

                                    callback(ret);
                                  }
                                );
                              }
                            );
                          },
                          views: {
                            month: {
                              show: false,
                            },
                            week: {
                              body: false,
                              header: {
                                singleDayEvents: true,
                              },
                              text: "Projects",
                            },
                            threeDay: {
                              show: false,
                            },
                            day: {
                              show: false,
                            },
                            list: {
                              show: false,
                            },
                          },
                          viewType: "week",
                        },
                      });

                      draw(ui);
                    },
                  },
                ],
              }, // Project queue tool
              {
                id: "projectCueTool",
                //type:'hqTool',
                layers: ["hq", "team"],
                name: "Project Queue",
                tip: "Organize your work.",
                allowMulti: true,
                icon: {
                  type: "exclamation",
                  color: "red",
                },
                /* default:	false, */
                settings: [],
                newButton: {
                  text: "New Project",
                  action: createNewProject,
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom) {
                      if (
                        state._config &&
                        state._config &&
                        state._config.settings &&
                        Array.isArray(state._config.settings.tags)
                      ) {
                        state._additionalTags = [];
                        _.each(state._config.settings.tags, function (tag) {
                          state._additionalTags.push(parseInt(tag));
                        });

                        state._additionalTags = _.uniq(state._additionalTags);
                      }

                      build_projects_collection(dom, state, draw, mainDom, {});
                    },
                  },
                ],
                boxViews: [],
                allowMulti: true,
                options: {
                  name: {
                    type: "title",
                    title: "Name",
                    defaultValue: "Project Cue",
                  },
                  tags: {
                    type: "tags",
                    title: "Tag(s)",
                    defaultValue: [],
                  },
                },
              }, // Projects object view
              {
                id: "project-obj",
                type: "object-view",
                title: "Project",
                icon: "tools",
                dom: function (dom, state, draw) {
                  var headerOptions = {};
                  var foundationGroup = false;

                  if (appConfig.instance == "foundation_group")
                    foundationGroup = true;

                  if (foundationGroup)
                    headerOptions.manager = { label: "Specialist" };

                  state.project = state.pageObject;

                  sb.notify({
                    type: "view-page",
                    data: {
                      ui: dom,
                      onDraw: draw,
                      page: return_singleProjectHeader(state, {
                        draw: draw,
                        dom: dom,
                        headerOptions,
                      }),
                      state: {
                        pageObject: state.project,
                      },
                    },
                  });

                  projectView(state.project, dom, state, draw);
                },
                menu: function (state, draw, layer) {
                  try {
                    if (state) {
                      sb.notify({
                        type: "get-dashboard-menu",
                        data: {
                          draw: draw,
                          group: state.project,
                          layer: layer,
                        },
                      });
                    }
                  } catch (e) {
                    console.log(e);
                  }
                },
                actions: {
                  archive: true,
                  toggleFollow: true,
                  move: true,
                  toggleTemplate: true,
                  copy: true,
                },
                header: {
                  name: true,
                },
              }, // Project object tool
              {
                id: "projectTool",
                type: "tool",
                availableToEntities: true,
                name: "Projects",
                tip: "Organize your work.",
                icon: {
                  type: "project diagram",
                  color: "blue",
                },
                default: true,
                settings: false,
                newButton: {
                  text: "New Project",
                  action: createNewProject,
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom, options) {
                      var data = {
                        domObj: dom,
                        objectId: state.entity,
                        draw: draw,
                      };
                      var dom = data.domObj,
                        pagedObj = {
                          page: 0,
                          pageLength: 6,
                          paged: true,
                          sortCast: "string",
                          sortCol: "date_created",
                          sortDir: "desc",
                          objectType: "work_orders",
                        };

                      build_loader(dom, "Building tool...");

                      dom.makeNode("lb_1", "lineBreak", { spaces: 1 });

                      dom.patch();

                      sb.data.db.obj.getWhere(
                        "groups",
                        {
                          main_contact: data.objectId,
                          group_type: "Project",
                          childObjs: {
                            name: true,
                            description: true,
                            state: true,
                            priority: true,
                            group_type: true,
                            type: {
                              name: true,
                              states: true,
                            },
                            managers: {
                              fname: true,
                              lname: true,
                              profile_image: true,
                            },
                            salesmanagers: {
                              fname: true,
                              lname: true,
                              profile_image: true,
                            },
                            tools: true,
                            start_date: true,
                            end_date: true,
                          },
                        },
                        function (resp) {
                          if (state.project) {
                            if (_.isEmpty(options)) {
                              options = {};
                            }
                            options.projectsInProjects = true;
                            projectContactTool(
                              dom,
                              state,
                              data.draw,
                              state.project,
                              options
                            );
                          } else {
                            projectContactTool(
                              dom,
                              state,
                              data.draw,
                              undefined,
                              options
                            );
                          }
                        }
                      );
                    },
                  },
                ],
              },
              {
                id: "projectTool",
                type: "objectTool",
                name: "Projects",
                tip: "Organize your work.",
                icon: {
                  type: "project diagram",
                  color: "blue",
                },
                default: true,
                settings: false,
                newButton: {
                  text: "New Project",
                  action: createNewProject,
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom, options) {
                      var data = {
                        domObj: dom,
                        objectId: state.entity,
                        draw: draw,
                      };
                      var dom = data.domObj,
                        pagedObj = {
                          page: 0,
                          pageLength: 6,
                          paged: true,
                          sortCast: "string",
                          sortCol: "date_created",
                          sortDir: "desc",
                          objectType: "work_orders",
                        };

                      if (options && options.hasOwnProperty("parent")) {
                        data.objectId = options.parent;
                      }

                      build_loader(dom, "Building tool...");

                      dom.makeNode("lb_1", "lineBreak", { spaces: 1 });

                      dom.patch();

                      sb.data.db.obj.getWhere(
                        "groups",
                        {
                          main_contact: data.objectId,
                          group_type: "Project",
                          childObjs: {
                            name: true,
                            description: true,
                            state: true,
                            priority: true,
                            group_type: true,
                            type: {
                              name: true,
                              states: true,
                            },
                            managers: {
                              fname: true,
                              lname: true,
                              profile_image: true,
                            },
                            salesmanagers: {
                              fname: true,
                              lname: true,
                              profile_image: true,
                            },
                            tools: true,
                            start_date: true,
                            end_date: true,
                          },
                        },
                        function (resp) {
                          if (state.project) {
                            projectContactTool(
                              dom,
                              state,
                              data.draw,
                              state.project,
                              options
                            );
                          } else {
                            projectContactTool(
                              dom,
                              state,
                              data.draw,
                              undefined,
                              options
                            );
                          }
                        }
                      );
                    },
                  },
                ],
              }, //  project workload report
              {
                id: "projectWorkloadReport",
                type: "custom",
                title: "Projects",
                icon: {
                  type: "file alternate",
                  color: "olive",
                },
                dom: function (dom, state, draw, mainDom) {
                  build_projects_collection(dom, state, draw, mainDom, {
                    selectedView: "chart",
                  });
                },
              }, // project by value report
              {
                id: "projectByValueReport",
                type: "custom",
                title: "Projects by value",
                icon: {
                  type: "file alternate",
                  color: "olive",
                },
                dom: function (dom, state, draw, mainDom) {
                  build_projects_collection(dom, state, draw, mainDom, {
                    selectedView: "chart",
                    menu: false,
                    search: false,
                    chart: {
                      range: {
                        defaultTo: "range",
                        not: ["all_time"],
                      },
                      defaults: {
                        type: "bar_byValue",
                      },
                      line: false,
                      bar: false,
                      pie: false,
                    },
                    onBoxview: true,
                  });
                },
              }, // Booked Events Report
              {
                id: "bookedEventsReport",
                type: "custom",
                title: "Booked Events",
                icon: {
                  type: "file alternate",
                  color: "olive",
                },
                dom: function (dom, state, draw, mainDom) {
                  build_projects_collection(dom, state, draw, mainDom, {
                    filters: false,
                    showPaging: false,
                    counter: false,
                    showDateRange: true,
                    selectedView: "chart",
                    menu: false,
                    search: false,
                    chart: {
                      range: {
                        defaultTo: "range",
                        not: ["all_time"],
                      },
                      defaults: {
                        type: "custom",
                        chartDisplay: showBookedEventsChart,
                      },
                      line: false,
                      bar: false,
                      pie: false,
                    },
                    onBoxview: false,
                    fields: {
                      invoice_value_no_taxes: {
                        title: "Value",
                        type: "usd",
                        view: function (ui, obj) {
                          if (ui) {
                            ui.makeNode("value", "div", {
                              text:
                                "$" +
                                (
                                  obj.invoice_value_no_taxes / 100
                                ).formatMoney(),
                            });

                            ui.patch();
                          } else {
                            return (
                              "$" +
                              (obj.invoice_value_no_taxes / 100).formatMoney()
                            );
                          }
                        },
                      },
                    },
                    /*
, where: {
												type: 1625566
												, state: {
													type: 'or'
													, values: [2, 3]
												}
											}
*/
                  });
                },
              }, // project by total value paid report registration
              {
                id: "projectByTotalValuePaidReport",
                type: "custom",
                title: "Total Value Paid (Projects)",
                icon: {
                  type: "file alternate",
                  color: "olive",
                },
                dom: function (dom, state, draw, mainDom) {
                  build_projects_collection(dom, state, draw, mainDom, {
                    fields: {
                      name: {
                        title: "Name",
                        type: "title",
                      },
                      invoice_value: {
                        title: "Value",
                        type: "usd",
                        view: function (ui, obj) {
                          if (ui) {
                            ui.makeNode("total", "div", {
                              text:
                                "$" + (obj.invoice_value / 100).formatMoney(2),
                            });
                          } else {
                            return (obj.invoice_value / 100).formatMoney(2);
                          }
                        },
                      },
                      totalPayments: {
                        title: "Total Paid",
                        type: "usd",
                        view: function (ui, obj) {
                          if (ui) {
                            ui.makeNode("total", "div", {
                              text:
                                "$" + (obj.totalPayments / 100).formatMoney(2),
                            });
                          } else {
                            return (obj.totalPayments / 100).formatMoney(2);
                          }
                        },
                      },
                    },
                    selectedView: "table",
                    menu: {
                      subviews: false,
                    },
                    actions: {
                      downloadCSV: true,
                      create: false,
                      copy: false,
                      archive: false,
                    },
                    parseData: function (data, callback) {
                      console.log(" parseData Reports page data", data);
                      // Tried using endpoint but kept failing on csv download

                      /*
sb.data.db.service("ProjectService", "getPaymentsTotal", data.data, function(response) {

													data.data = response;

													callback(data);

												});
*/
                      var proposalIds = [];

                      _.each(data.data, function (obj) {
                        if (obj && obj.proposal && typeof obj.proposal === 'object') {
                          if (obj.proposal.hasOwnProperty('id')) {
                            proposalIds.push(obj.proposal.id);
                          }
                        }
                      });
                      console.log(" proposalIds", proposalIds);
                      sb.data.db.obj.getWhere(
                        "payments",
                        {
                          main_object: {
                            type: "or",
                            values: proposalIds,
                          },
                          childObjs: 0,
                        },
                        function (payments) {
                          _.each(data.data, function (obj, i) {
                            var relatedPayments = [];
                            var totalPaid = 0;

                            obj.totalPayments = 0;

                            if (obj.proposal.hasOwnProperty("id")) {
                              relatedPayments = _.where(payments, {
                                main_object: obj.proposal.id,
                              });

                              _.each(relatedPayments, function (paymentObj) {
                                totalPaid += paymentObj.amount;
                              });

                              obj.totalPayments = totalPaid;
                            }
                          });

                          callback(data);
                        }
                      );
                    },
                    where: {
                      type: 1625566,
                      state: {
                        type: "or",
                        values: [2, 3],
                      },
                      childObjs: {
                        name: true,
                        invoice_value: true,
                        invoice_value_no_taxes: true,
                        proposal: {
                          id: true
                        }
                      }
                    },
                  });
                },
              },
            ],
          },
        },
      });

      // Team tool registrations
      sb.notify({
        type: "register-tool",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "teamTools",
            title: "Teams",
            icon: '<i class="fa fa-users"></i>',
            views: [
              // Teams Main tool
              {
                id: "teamTool",
                //type:'hqTool',
                layers: ["hq", "team"],
                name: "Teams",
                tip: "Organize people into groups and teams, then create projects and tasks.",
                icon: {
                  type: "users",
                  color: "violet",
                },
                default: true,
                settings: false,
                newButton: {
                  text: "New Team",
                  action: createNewTeam,
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom) {
                      var where = {
                        group_type: "Team",
                        childObjs: {
                          name: true,
                          details: true,
                          group_type: true,
                          parent: {
                            name: true,
                            group_type: true,
                          },
                        },
                      };
                      var mainObj = {};

                      if (state.layer === "team") {
                        where.tagged_with = state.where.tagged_with;
                        mainObj = state.team;
                      } else {
                        mainObj = state.headquarters;
                      }

                      sb.notify({
                        type: "show-collection",
                        data: {
                          actions: {
                            create: function (ui, newObj, onComplete) {
                              newObj.parent = state.headquarters.id;

                              createNewTeam(
                                ui,
                                state,
                                newObj,
                                function (newTeam) {
                                  if (newTeam)
                                    sb.notify({
                                      type: "app-navigate-to",
                                      data: {
                                        itemId: "groups",
                                        viewId: {
                                          viewState: {
                                            obj: newTeam,
                                          },
                                        },
                                      },
                                    });
                                }
                              );
                            },
                            view: false,
                          },
                          domObj: dom,
                          fields: {
                            name: {
                              title: "Name",
                              type: "title",
                            },
                            parent: {
                              title: "Parent",
                              type: "parent",
                            },
                            details: {
                              title: "Details",
                              type: "detail",
                            },
                            // revisit this later to visually differentiate between pointPeeps & members
                            /*
														managers:{
															title:'Point People',
															view:function(domObj, object){

																state.labelSize = 'small';

																ui_pointPeople(domObj, object, state.logo, state);

															}
														},
														*/
                            /*
														allowed_users:{
															title:'Members',
															view:function(domObj, object){

																ui_memberList(domObj, object, 'small');

															}
														},
*/
                            /*
														enabledTools:{
															title:'Enabled Tools',
															view:function(domObj, object){

																domObj.makeNode('toolList', 'div', {css:'ui small basic labels'});

																_.each(object.tools, function(tool){

																	var toolIcon = 'users icon';

																	if(tool.display_name == 'Projects'){
																		toolIcon = 'lightbulb icon';
																	}

																	domObj.toolList.makeNode('tool'+ tool.id, 'div', {css:'ui label'});
																	domObj.toolList['tool'+ tool.id].makeNode('icon', 'div', {text:'<i class="'+ toolIcon +'"></i> '+ tool.display_name});

																});

																domObj.patch();

															}
														},
*/
                            /*
														date_created:{ // start_date value is currently "" on all teams; using date_created for now
															title:'Date Created',
															view:function(domObj, object){

																domObj.makeNode('start_date', 'div', {text:moment(object.date_created).format('M/D/YYYY')});
																domObj.patch();

															},
															isHidden:true
														}
*/
                          },
                          fullView: {
                            id: "teamTool",
                            type: "hqTool",
                          },
                          groupings: false,
                          objectType: "groups",
                          //size:'mini',
                          //layer: 'hq',
                          singleView: {
                            view: function (ui, obj, draw) {
                              state.team = obj;
                              teamView(ui, state, draw);
                            },
                            select: 2,
                          },
                          selectedView: "cards",
                          state: state,
                          sortCol: "name",
                          sortDir: "asc",
                          menu: {
                            subviews: {
                              cards: true,
                              list: true,
                              table: true,
                              calendar: false,
                            },
                          },
                          subviews: {
                            board: false,
                            calendar: false,
                            table: false,
                            list: {
                              nestChildren: true,
                              options: {
                                where: {
                                  group_type: "Team",
                                  parent: {
                                    type: "not_set",
                                    or: mainObj.id,
                                  },
                                  childObjs: {
                                    name: true,
                                    details: true,
                                    group_type: true,
                                  },
                                },
                              },
                            },
                          },
                          where: where,
                        },
                      });
                    },
                  },
                ],
                boxViews: [
                  {
                    id: "teamOverview",
                    width: "four",
                    title: "Team Total",
                    dom: teamsOverviewBoxView,
                  },
                  {
                    id: "teamsList",
                    width: "eight",
                    title: "Teams",
                    collections: {
                      selectedView: "list",
                      actions: {},
                      fields: {
                        name: {
                          title: "Name",
                          type: "title",
                          isSearchable: true,
                        },
                      },
                      pageLength: 10,
                      objectType: "groups",
                      where: {
                        group_type: "Team",
                        childObjs: {
                          name: true,
                          group_type: true,
                        },
                      },
                      subviews: {
                        list: {
                          hideTimeRangeFilter: true,
                        },
                      },
                    },
                  },
                ],
              }, // Teams object view
              {
                id: "team-obj",
                type: "object-view",
                title: "Team",
                icon: "building",
                dom: function (dom, state, draw) {
                  dom.makeNode("dash", "div", {});

                  // activity feed
                  dom.makeNode("titleBreak", "div", {
                    css: "ui clearing divider",
                  });

                  var style = sb.dom.isMobile
                    ? ""
                    : "margin:5px 15px 15px 15px";
                  dom
                    .makeNode("noteContainer", "div", {
                      css: "ui one column centered grid",
                    })
                    .makeNode("col", "div", {
                      css: "sixteen wide column round-border",
                      style: style,
                    });

                  dom.makeNode("notesBreak", "div", { text: "<br /><br />" });
                  dom.patch();

                  sb.notify({
                    type: "show-dashboard",
                    data: {
                      dom: dom.dash,
                      state: state,
                      draw: draw,
                    },
                  });

                  sb.notify({
                    type: "show-note-list-box",
                    data: {
                      domObj: dom.noteContainer.col,
                      objectIds: [state.team.id],
                      objectId: state.team.id,
                      collapse: "open",
                      activityFeed: true,
                    },
                  });
                },
                header: {
                  name: true,
                  subHeader: function (object) {
                    return object.details;
                  },
                  user: {
                    field: "managers",
                    edit: true,
                    multi: true,
                    label: "Managers",
                  },
                  tags: true,
                  detail: true,
                  menu: teamSingleViewMenu,
                  select: {
                    name: true,
                    description: true,
                    group_type: true,
                    type: {
                      name: true,
                    },
                    managers: {
                      fname: true,
                      lname: true,
                      profile_image: {
                        file_name: true,
                        file_type: true,
                        name: true,
                        loc: true,
                        oid_type: true,
                        oid: "id",
                      },
                    },
                    salesmanagers: {
                      fname: true,
                      lname: true,
                      profile_image: true,
                    },
                    allowed_users: {
                      fname: true,
                      lname: true,
                    },
                    tools: true,
                    parent: true,
                    selectionObj: true,
                  },
                  canEdit: sb.permissions.isGroupManager,
                },
                menu: function (state, draw, layer) {
                  if (state) {
                    sb.notify({
                      type: "get-dashboard-menu",
                      data: {
                        draw: draw,
                        group: state.team,
                        layer: layer,
                      },
                    });
                  }
                },
              },
            ],
          },
        },
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "headquarters",
            title: "Headquarters",
            icon: '<i class="fa fa-building"></i>',
            views: [
              {
                id: "headquarters",
                type: "object-view",
                title: "Headquarters",
                icon: '<i class="fa fa-building"></i>',
                default: true,
                header: {
                  name: true,
                  subHeader: function (object) {
                    return object.details;
                  },
                  user: {
                    field: "managers",
                    edit: true,
                    multi: true,
                    label: "Managers",
                  },
                  tags: false,
                  menu: false,
                  select: {
                    name: true,
                    type: {
                      name: true,
                    },
                    managers: {
                      fname: true,
                      lname: true,
                      profile_image: {
                        file_name: true,
                        file_type: true,
                        name: true,
                        loc: true,
                        oid_type: true,
                        oid: "id",
                      },
                    },
                    salesmanagers: {
                      fname: true,
                      lname: true,
                    },
                    allowed_users: {
                      fname: true,
                      lname: true,
                    },
                    selectionObj: true,
                  },
                  canEdit: true,
                },
                dom: function (dom, state, draw) {
                  /*
									ui.sys_cont = dom;

									dom.makeNode('nav', 'div', {});
									var adminCount = 0;
									if (
										state.headquarters
										&& Array.isArray(state.headquarters.managers)
									) {
										adminCount = state.headquarters.managers.length;
									}

									dom.nav.makeNode('edit', 'div', {
										css: 'ui blue right floated mini button'
										, text: '<i class="users icon"></i> ('+ adminCount.toString() +') HQ Members'
										, tag:'button'
									}).notify('click', {
										type:'headquarters-run',
										data:{
											run:function(obj, dom, state, draw){

												editHeadquartersDetails(obj, dom, state, draw, function(done){

													// refresh page
													window.location.reload();

												});

											}.bind({}, state.headquarters, dom, state, draw)
										}
									}, sb.moduleId);
									dom.makeNode('br', 'div', {text:'<br /><div class="ui clearing divider"></div<br />'});

									dom.makeNode('cont', 'div', {css:''});

									dom.patch();
*/

                  sb.notify({
                    type: "show-dashboard",
                    data: {
                      dom: dom,
                      state: state,
                      draw: draw,
                    },
                  });
                },
                menu: function (state, draw, layer) {
                  if (state) {
                    sb.notify({
                      type: "get-dashboard-menu",
                      data: {
                        draw: draw,
                        group: state.headquarters,
                        layer: layer,
                      },
                    });
                  }
                },
              },
              // HQ Object view
              {
                id: "hq-obj",
                type: "object-view",
                title: "HQ",
                icon: "building",
                dom: function (dom, state, draw) {
                  sb.notify({
                    type: "show-dashboard",
                    data: {
                      dom: dom,
                      state: state,
                      draw: draw,
                    },
                  });
                },
                header: {
                  name: true,
                  subTitle: function (object) {
                    return object.details;
                  },
                  user: {
                    field: "managers",
                    edit: true,
                    multi: true,
                    label: "Managers",
                  },
                  tags: true,
                  menu: {
                    manage_tools: {
                      action: function (obj, dom, state, onComplete) {
                        manageTeamTools(
                          dom,
                          state,
                          function (dom) {
                            if (dom.patch) {
                              dom.patch();
                            } else if (dom.dom) {
                              dom.dom.patch();
                              dom.after(dom.dom);
                            }
                          },
                          onComplete
                        );
                      },
                      icon: "grey cog",
                      title: "Manage Apps",
                    },
                    move: {
                      icon: "teal truck",
                      title: "Move Team",
                      action: function (obj, dom, state, draw) {
                        ui_moveGroup(dom, obj, state);
                      },
                    },
                    archive: true,
                  },
                  select: {
                    name: true,
                    type: {
                      name: true,
                    },
                    managers: {
                      fname: true,
                      lname: true,
                      profile_image: {
                        file_name: true,
                        file_type: true,
                        name: true,
                        oid_type: true,
                        oid: "id",
                        loc: true,
                      },
                    },
                    salesmanagers: {
                      fname: true,
                      lname: true,
                    },
                    allowed_users: {
                      fname: true,
                      lname: true,
                    },
                  },
                  canEdit: sb.permissions.isGroupManager,
                },
              },
              // Project types object view
              {
                id: "project_types-obj",
                type: "object-view",
                title: "Project Types Edit",
                icon: "tools",
                edit: true,
                dom: function (dom, state, draw) {},
              },
              // Teams table
              {
                id: "teams",
                type: "table",
                title: "Teams",
                icon: '<i class="fa fa-users"></i>',
                setup: {
                  objectType: "groups",
                  tableTitle: '<i class="fa fa-users"></i> Teams',
                  childObjs: 2,
                  searchObjects: [
                    {
                      name: "Name",
                      value: "name",
                    },
                  ],
                  filters: false,
                  headerButtons: {
                    reload: {
                      name: "Reload",
                      css: "pda-btn-blue",
                      action: function () {},
                    },
                  },
                  rowSelection: true,
                  multiSelectButtons: {
                    erase: {
                      name: '<i class="fa fa-times"></i> Delete',
                      css: "pda-btn-red",
                      domType: "modal",
                      action: erasegroups,
                    },
                  },
                  rowLink: {
                    type: "tab",
                    header: function (obj) {
                      return obj.name;
                    },
                    action: teamView,
                  },
                  visibleCols: {
                    state: "Status",
                    type: "Type",
                    name: "Name",
                    contact: "Contact Person",
                    start_date: "Start Date",
                    end_date: "End Date",
                    date_created: "Date Created",
                    description: "Description",
                  },
                  cells: {
                    description: function (obj) {
                      if (obj.description) {
                        return obj.description;
                      } else {
                        return "No description";
                      }
                    },
                    end_date: function (obj) {
                      if (obj.end_date) {
                        return obj.end_date;
                      } else {
                        return "No end date";
                      }
                    },
                    start_date: function (obj) {
                      if (obj.start_date) {
                        return obj.start_date;
                      } else {
                        return "No start date";
                      }
                    },
                    date_created: function (obj) {
                      if (obj.date_created) {
                        return moment(obj.date_created).format("M/D/YYYY");
                      } else {
                        return "No start date";
                      }
                    },
                    state: function (obj, ui) {
                      ui.properties.compInstanceId = sb.instanceId;
                      // 											stateBtn(ui, obj, obj.type.states, 'state', undefined, true);
                    },
                    contact: function (obj) {
                      if (obj.main_contact) {
                        var contactString =
                          obj.main_contact.fname + " " + obj.main_contact.lname;

                        if (obj.main_contact.company) {
                          contactString +=
                            "<br /><small>from</small> " +
                            obj.main_contact.company.name;
                        }

                        return contactString;
                      } else {
                        return "No contact";
                      }
                    },
                  },
                  paged: {
                    sortCol: "name",
                    sortDir: "asc",
                  },
                  data: function (paged, callback) {
                    sb.data.db.obj.getAll(
                      "groups",
                      function (ret) {
                        callback(ret);
                      },
                      {
                        name: true,
                        type: true,
                        state: true,
                        priority: true,
                      },
                      paged
                    );
                  },
                },
              },
              // BIN projects table
              {
                id: "projects",
                type: "table",
                title: "Projects",
                icon: '<i class="fa fa-project-diagram"></i>',
                setup: {
                  objectType: "groups",
                  tableTitle: '<i class="fa project-diagram"></i> Projects',
                  childObjs: 2,
                  searchObjects: [
                    {
                      name: "Name",
                      value: "name",
                    },
                  ],
                  filters: false,
                  headerButtons: {
                    reload: {
                      name: "Reload",
                      css: "pda-btn-blue",
                      action: function () {},
                    },
                  },
                  rowSelection: true,
                  multiSelectButtons: {
                    erase: {
                      name: '<i class="fa fa-times"></i> Delete',
                      css: "pda-btn-red",
                      domType: "modal",
                      action: eraseProjects,
                    },
                  },
                  rowLink: {
                    type: "tab",
                    header: function (obj) {
                      return obj.name;
                    },
                    action: projectView,
                  },
                  visibleCols: {
                    state: "Status",
                    type: "Type",
                    name: "Name",
                    contact: "Contact Person",
                    start_date: "Start Date",
                    end_date: "End Date",
                    date_created: "Date Created",
                    description: "Description",
                  },
                  cells: {
                    description: function (obj) {
                      if (obj.description) {
                        return obj.description;
                      } else {
                        return "No description";
                      }
                    },
                    end_date: function (obj) {
                      if (obj.end_date) {
                        return obj.end_date;
                      } else {
                        return "No end date";
                      }
                    },
                    start_date: function (obj) {
                      if (obj.start_date) {
                        return obj.start_date;
                      } else {
                        return "No start date";
                      }
                    },
                    date_created: function (obj) {
                      if (obj.date_created) {
                        return moment(obj.date_created).format("M/D/YYYY");
                      } else {
                        return "No start date";
                      }
                    },
                    state: function (obj, ui) {
                      ui.properties.compInstanceId = sb.instanceId;
                      // 											stateBtn(ui, obj, obj.type.states, 'state', undefined, true);
                    },
                    contact: function (obj) {
                      if (obj.main_contact) {
                        var contactString =
                          obj.main_contact.fname + " " + obj.main_contact.lname;

                        if (obj.main_contact.company) {
                          contactString +=
                            "<br /><small>from</small> " +
                            obj.main_contact.company.name;
                        }

                        return contactString;
                      } else {
                        return "No contact";
                      }
                    },
                  },
                  paged: {
                    sortCol: "name",
                    sortDir: "asc",
                  },
                  data: function (paged, callback) {
                    sb.data.db.obj.getWhere(
                      "groups",
                      {
                        group_type: "Project",
                        childObjs: {
                          name: true,
                          type: true,
                          state: true,
                          priority: true,
                        },
                        paged: paged,
                      },
                      function (ret) {
                        callback(ret);
                      }
                    );
                  },
                },
              },
              // settings
              {
                id: "settings",
                type: "settings",
                title: "Settings",
                icon: "",
                setup: [
                  {
                    object_type: "group_types",
                    name: "1. Group Types",
                    action: projectTypesView,
                  },
                  {
                    object_type: "project_types",
                    name: "2. Project Types",
                    action: projectTypesView,
                  },
                  {
                    object_type: "project_tools",
                    name: "3. Tool Settings",
                    action: toolSettingsView,
                  },
                ],
              },
            ],
          },
        },
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "my-stuff",
            title: "My Stuff",
            icon: '<i class="ui compass outline icon"></i>',
            views: [
              {
                id: "my-stuff",
                type: "custom",
                title: "My Stuff",
                icon: '<i class="compass outline icon"></i>',
                default: true,
                header: false,
                dom: function (dom, state, draw) {
                  ui.sys_cont = dom;

                  function setupMyStuff(userId, callback) {
                    sb.data.db.obj.getWhere(
                      "groups",
                      {
                        user: +sb.data.cookie.get("uid"),
                        group_type: "MyStuff",
                      },
                      function (myStuffGroup) {
                        if (Array.isArray(myStuffGroup)) {
                          // !TODO - Remove block (added 09/10/20)
                          if (myStuffGroup.length > 1) {
                            var sorted_byLastUpdated = _.sortBy(
                              myStuffGroup,
                              "last_updated"
                            );
                            var mostUpToDate =
                              sorted_byLastUpdated[
                                sorted_byLastUpdated.length - 1
                              ];
                            var extraObjIds = [];

                            _.each(sorted_byLastUpdated, function (o) {
                              if (o.id !== mostUpToDate.id) {
                                extraObjIds.push(o.id);
                              }
                            });

                            sb.data.db.obj.erase(
                              "groups",
                              extraObjIds,
                              function (resp) {},
                              {
                                name: true,
                              }
                            );
                          }

                          if (myStuffGroup[0]) {
                            callback(myStuffGroup[0]);
                          } else {
                            var tools = [];

                            _.each(
                              appConfig.myStuffTools,
                              function (tool, toolOrder) {
                                switch (tool.id) {
                                  case "myprojects":
                                  case "messageBoardTool":
                                  case "crmTool":
                                  case "myteams":
                                  case "taskTool":
                                    tools.push({
                                      allowed_users: [userId],
                                      system_name: tool.id,
                                      display_name: tool.name,
                                      is_archieved: 0,
                                      order: toolOrder,
                                      added_by: userId,
                                      added_on: moment(),
                                      settings: {},
                                      box_color: tool.icon.color,
                                    });

                                    break;
                                }
                              }
                            );

                            sb.data.db.obj.create(
                              "groups",
                              {
                                group_type: "MyStuff",
                                user: appConfig.user.id,
                                name:
                                  appConfig.user.fname +
                                  " " +
                                  appConfig.user.lname,
                                tools: tools,
                              },
                              function (myStuffGroup) {
                                callback(myStuffGroup);
                              }
                            );
                          }
                        } else {
                          location.reload();
                        }
                      }
                    );
                  }

                  dom.makeNode("cont", "div", {
                    css: "ui loading basic segment",
                  });

                  draw({
                    dom: dom,
                    after: function (dom) {
                      setupMyStuff(
                        +sb.data.cookie.get("uid"),
                        function (myStuffGroup) {
                          state.pageObject = myStuffGroup;
                          state.myStuff = myStuffGroup;

                          dom.cont.css("ui basic segment");

                          sb.notify({
                            type: "show-dashboard",
                            data: {
                              dom: dom.cont,
                              state: state,
                              draw: draw,
                            },
                          });
                        }
                      );
                    },
                  });
                },
                menu: function (state, draw, layer) {
                  sb.data.db.obj.getWhere(
                    "groups",
                    {
                      user: +sb.data.cookie.get("uid"),
                      group_type: "MyStuff",
                    },
                    function (myStuffGroup) {
                      if (myStuffGroup) {
                        sb.notify({
                          type: "get-dashboard-menu",
                          data: {
                            draw: draw,
                            group: myStuffGroup[0],
                            layer: layer,
                          },
                        });
                      }
                    }
                  );
                },
              },
            ],
          },
        },
      });

      // HOME
      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "home",
            title: "Home",
            icon: "home",
            views: homeViewsSetup,
          },
        },
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "admin",
            title: "Admin",
            icon: '<i class="ui cogs outline icon"></i>',
            views: [
              {
                id: "admin",
                type: "custom",
                title: "Admin",
                icon: '<i class="compass outline icon"></i>',
                default: true,
                dom: function (dom, state, draw) {
                  ui.sys_cont = dom;

                  appSettings = state.appSettings;

                  dom.makeNode("title", "div", {
                    text: "System Settings",
                    css: "ui huge header",
                  });
                  dom.makeNode("cont", "div", {
                    css: "ui basic loading segment",
                  });

                  draw({
                    dom: dom,
                    after: function (dom) {
                      adminView(dom.cont, state, draw, function (done) {
                        dom.cont.css("ui basic segment");
                      });
                    },
                  });
                },
              },
            ],
          },
        },
      });

      // reports
      /*
sb.notify({
				type:'register-report',
				data:{
					id:'projectWorkloadReport',
					icon:'chart bar',
					header:'Projects',
					subheader:'Projects by assignee and status.',
					type:'Workload'
				}
			});
*/

      listeners = {
        // internal events
        "headquarters-run": this.run,
        "headquarters-contact-view": this.singleContactView,
        "headquarters-tool-dropped": this.projectToolDropped,

        // external events
        "get-move-object-form": this.getMoveObjectForm,
        "create-new-project": this.createNewProject,
        "build-teams-collection": this.buildTeamsCollection,
        "build-projects-collection": this.buildProjectsCollection,

        // refreshing the page
        "field-updated": this.refreshProjectDashboard,
        "page-changed": this.clearState,
      };

      sb.listen(listeners);

      //comps.projects = sb.createComponent('crud-table');
      comps.table = sb.createComponent("crud-table");
      comps.tags = sb.createComponent("tags");
      comps.editProfileView = sb.createComponent("userComponent");
    },

    load: function (setup) {},

    createNewProject: function (setup) {
      createNewProject(
        setup.ui,
        setup.state,
        setup.obj,
        setup.callback,
        setup.setup
      );
    },

    buildProjectsCollection: function (setup) {
      myProjects_collections(
        setup.dom,
        setup.state,
        setup.draw,
        setup.mainDom,
        setup.options
      );
    },

    buildTeamsCollection: function (setup) {
      myTeams_collections(setup.dom, setup.state, setup.options);
    },

    destroy: function () {
      _.each(comps, function (v) {
        v.destroy();
      });

      comps = {};
    },

    // C
    clearState: function () {
      currentProj = 0;
      refreshProjectDashboard = function () {};
    },

    // G
    getMoveObjectForm: function (data) {
      ui_moveGroup(data.dom, data.object, data.state, data.onComplete);
    },

    // P
    projectToolDropped: function (data) {
      var toolIn = data.dropped.tool;
      var toolOut = data.in.tool;
      var project = data.dropped.project;
      var dom = data.dropped.dom;
      var state = data.dropped.state;
      var draw = data.dropped.draw;
      var index = data.in.index;

      toolOut.order = toolIn.order;

      toolIn.order = index;

      sb.data.db.obj.update("projects", project, function (updated) {
        if (!updated) {
          throw "Tool order did not update";
        } else {
          setTimeout(function () {
            singleView(project, dom, state, draw);
          }, 0.01);
        }
      });
    },

    // R
    run: function (data) {
      data.run(data);
    },

    refreshProjectDashboard: function (data) {
      if (
        data.obj &&
        data.obj.id === currentProj &&
        data.property === "state"
      ) {
        refreshProjectDashboard();
      }
    },

    // S
    singleContactView: function (data) {
      var dom = data.domObj,
        pagedObj = {
          page: 0,
          pageLength: 6,
          paged: true,
          sortCast: "string",
          sortCol: "date_created",
          sortDir: "desc",
          objectType: "work_orders",
        };

      sb.data.db.obj.getWhere(
        "groups",
        { main_contact: data.objectId, childObjs: 2, group_type: "Project" },
        function (resp) {
          sb.data.db.obj.getWhere(
            "groups",
            {
              related_contacts: { type: "contains", value: data.objectId },
              group_type: "Project",
              childObjs: 2,
            },
            function (addObjs) {
              contactView(
                dom,
                {
                  data: resp.concat(addObjs),
                  contact: data.objectId,
                },
                data.draw
              );
            }
          );
        }
      );
    },
  };
});
