Factory.registerComponent('staff-paperwork', function(sb){
	
	 var ui = {},
		modal = {},	 
		list = {},
	     details = {},
	     windowView = {},

	     hrDetails = {},
	     hrDocs = {},
	     hrDec = {},
	     hrCom = {},
	     cardBody = {},
	     
		components = {},
		updStaffList = {},
		selectedStaffObj = {},
		navButtons = [],
		navSelectedBtn = '',
		hrToggle = {
			details: true,
			documents: true,
			decisions: true,
			comments: true
		},
		
		fields = [],
		docTypes = [],
		fileDataObj = {},
	
		dbc = (function(){
			
			var getPaperwork = function(callback){
									
				sb.data.db.obj.getAll('staff_paperwork_type', function(docs){

					callback(docs);
				});		
	
			};
			
			return {
				
				paperwork: getPaperwork,
			}
		
			
		})();
		
	function navBarButtons(navCont, navButtons, selectedBtn){
				
		navCont.makeNode('btnCol', 'column', {width: 12});

		navCont.btnCol.makeNode('navBtnGroup', 'buttonGroup', {});
		
		var buttonCSS;
		
		_.each(navButtons, function(btn, i){

			if(btn.name == selectedBtn){
		
				buttonCSS = 'pda-btn-blue pda-align-left pda-btn-x-small'
							
			} else {
				
				buttonCSS = 'pda-btnOutline-blue pda-align-left pda-btn-x-small'
			}
			
			if(btn.hasOwnProperty('css')){
				
				buttonCSS += ' ' + btn.css;
			}

			navCont.btnCol.navBtnGroup.makeNode('Btn-' + btn.name, 'button', {css: buttonCSS, text: btn.displayText}).notify('click', btn.notificationObj, sb.moduleId);			
			
		}, this);
	
	} /// *** navBarButtons takes in cont, array of buttons, and selected to highlight css
	
	function listCo(staff){
		
		function staffList(){
			
			this.empty();

			var staffList = _.clone(staff);	
			
			this.makeNode('dashboardPanel', 'panel', {css: 'pda-panel-blue', header: '<i class="fa fa-user-circle-o" aria-hidden="true"></i> Staff List'});
			dashPan = this.dashboardPanel.body.makeNode('contentCont', 'container', {});
			
			this.dashboardPanel.body.patch();
			
			sb.data.db.obj.getAll('staff_base', function(baseObj){

				sb.data.db.obj.getAll('inventory_service', function(jobType){

					components.staffList = sb.createComponent('crud-table');
					
					components.staffList.notify({		
						type: 'show-table',
						data: { 
							domObj: dashPan,
							objectType:'staff',
							maxPageButtons: 6,
							searchObjects: [
								{
									name: 'First Name',
									value: 'fname'
								},
								{
									name: 'Last Name',
									value: 'lname'
								}
							],						
							headerButtons: {
								calendar: {
									name: '<i class="fa fa-plus-circle" aria-hidden="true"></i> Calendar View',
									css: 'pda-btn-blue',
									domType: 'full',
									action: {
										type: 'staff-list-calendar-view',
										data: {}
										
									}
								}
							},
							rowSelection: false,
							rowLink: {
								type: 'notify',
								action: {
									type: 'staff-view-button-clicked',
									data: {}
								}
							},
							visibleCols: {
								name: 'Name',
								docStatus: 'Status'
							},
							cells: {
								name: function(obj){ 
	
									return '<strong>' + obj.fname + ' ' + obj.lname + '</strong></br> <span class="small">Hire Date: </span>' + moment(obj.date_created).format('MM/DD/YY');
	
								},
								docStatus: function(obj){
									
									var label;
										
									var updBase = _.map(baseObj, function(item, i, list){

									     var baseId = item;
									     var matchedBase;
									     
									     const object = _.findWhere(baseObj, {id: +baseId});
									     
									     if(object){ matchedBase = object; } else { return item; }
										     
									     return matchedBase;
									     
									});

									var updJobTypes = _.map(jobType, function(job){
			
										var jobId = job;
										var matchedJob;
									
										const object = _.findWhere(jobType, {id: +jobId});
			
										if ( object ) { matchedJob = object; } else { return job; }
										
										return matchedJob;
										
									});

									obj.base = updBase;
									obj.service = updJobTypes;
									
									var labelColor = '<label class="label label-danger">';
									var docTotal = _.size(docTypes);
									var numberMissing = docTotal - (obj.staff_paperwork.length);	
									var labelInnerText = ' Missing ' + numberMissing + '(' + docTotal + ')' ;
							
									if(numberMissing < 1){
										
										labelColor = '<label class="label label-success">';
										labelInnerText = 'All Documents';
									}											
									
									label = '<label>' + labelColor + labelInnerText + '</label>';											
									
									return label
									
								}
								
							},
							data: function(paged, callback){
								
								// get all staff
								sb.data.db.obj.getAll('staff', function(staff){

									// empty array for building the staff list with the staff paperwork obejcts
									var ret = [];
									
									// get staff paperwork objects that have the staff ids of the staff members in the staff var
									sb.data.db.obj.getWhere('staff_paperwork', {
										staff:
											{
												type:'or',
												values:_.pluck(staff.data, 'id')
											}
									}, function(docs){
										
										if(docs.length > 0){
											
											// if there are docs for the staff members, add them to the staff object in a new prop called staff_paperwork
											
											ret = _.map(staff.data, function(s){

												s.staff_paperwork = _.where(docs, {staff:s.id});
												
												return s;
												
											});
											
											// ret contains a list of staff objects with the staff_paperwork prop
											
											staff.data = ret;
												
										}else{
											
											// if there aren't any docs for the staff members, create the prop as an empty array
											
											ret = _.map(staff.data, function(s){

												s.staff_paperwork = [];
												
												return s;
												
											});

											staff.data = ret;
											
										}

										callback(staff);
							 		
							 		});
										
								}, 1, paged);
							}	
						} 
					}); 	
					
				});
				
			});			
		}
					
		staffList.bind(this)();
		
		
	} /// *** listCo builds out left list column
	
	function detailsCo(staffObj, buttons, selectedBtn, toggle, fileData){

		function toggleCSSshowHide(show, data){

			if ( show ){

				this.card.cardHeader.btnCol.makeNode('toggleBtn', 'button', {dataId: data.dataId, dataId2: data.dataId2, css: 'pda-btnOutline-blue pda-transparent pull-right', text: '<i class="fa fa-caret-square-o-left fa-lg" aria-hidden="true"></i>'}).notify('click', {
					type: 'hr-nav-button-toggled',
					data: {}
				});
										
				this.card.cardHeader.btnCol.patch();
				
				this.card.cardBody.hide();
							
			} else {
 				
				this.card.cardHeader.btnCol.makeNode('toggleBtn', 'button', {dataId: data.dataId, dataId2: data.dataId2, css: 'pda-btnOutline-blue pda-transparent pull-right', text: '<i class="fa fa-caret-square-o-down fa-lg" aria-hidden="true"></i>'}).notify('click', {
					type: 'hr-nav-button-toggled',
					data: {}
				});						

								
				this.card.cardHeader.btnCol.patch();
					
				this.card.cardBody.show();
				
				
			}
		}
		function panelPrep(staff, buttons, selectedBtn){ /// panelPrep laysout header and buttons, creates windowView object
				
				if ( !_.isEmpty(staff) ) {
					
					var headerText = '<i class="fa fa-user-circle" aria-hidden="true"></i> ' +  staffObj.fname + ' ' + staffObj.lname, 
					    btnCSS = 'pda-color-default';
					
				} else { 
					
					headerText = '<i class="fa fa-user-circle-o" aria-hidden="true"></i> Select a Staff Member',
					btnCSS = 'pda-color-muted';
				}	
					
				this.makeNode('detailsPan', 'panel', {css: 'pda-panel-blue' , header: 'Dashboard View'});
						
				this.detailsPan.body.makeNode('lb', 'lineBreak', {spaces: 1}); 
		
				this.detailsPan.body.makeNode('headerText', 'headerText', {text: headerText, css: btnCSS});
								
				buttonNav = this.detailsPan.body.makeNode('buttonNavCont', 'container', {});
					
				navBarButtons.bind(this)(buttonNav, buttons, selectedBtn);
					
				this.detailsPan.body.makeNode('sp', 'lineBreak', {spaces: 1});
					
				windowView = this.detailsPan.body.makeNode('windowCont', 'container', {css: 'pda-container', style: 'background-color:rgb(245, 245, 245);border:1px solid rgb(221,221,221)'});	
				
			}		
		function cardPrep(name, toggle){ /// builds 'card' container that consists of header with title and toggle button

			if(toggle == true){
				
				this.makeNode('card', 'container', {css: 'pda-Panel pda-container'});
				this.card.makeNode('cardHeader', 'container', {});
				this.card.cardHeader.makeNode('textCol', 'column', {width: 6});
				this.card.cardHeader.textCol.makeNode('details-' + name, 'text', {size:'xx-small', text: '<span class="small">' + name.toUpperCase() + '</span>', css: 'text-muted pda-align-left'});	
				this.card.cardHeader.makeNode('btnCol', 'column', {width: 6});
				this.card.cardHeader.btnCol.makeNode('toggleBtn', 'button', {dataId: name, dataId2: selectedBtn, css: 'pda-btnOutline-blue pda-transparent pull-right', text: '<i class="fa fa-caret-square-o-down fa-lg" aria-hidden="true"></i>'}).notify('click', {
					type: 'hr-nav-button-toggled',
					data: {}
				});
				
				this.card.makeNode('cardBody', 'column', {width: 12});		
	
				
			} else {
				
				this.makeNode('card', 'container', {css: 'pda-Panel pda-container'});
				this.card.makeNode('cardHeader', 'container', {});
				this.card.cardHeader.makeNode('textCol', 'column', {width: 6});
				this.card.cardHeader.textCol.makeNode('details-' + name, 'text', {size:'xx-small', text: '<span class="small">' + name.toUpperCase() + '</span>', css: 'text-muted pda-align-left'});	
				this.card.cardHeader.makeNode('btnCol', 'column', {width: 6});
				this.card.cardHeader.btnCol.makeNode('toggleBtn', 'button', {dataId: name, dataId2: selectedBtn, css: 'pda-btnOutline-blue pda-transparent pull-right', text: '<i class="fa fa-caret-square-o-left fa-lg" aria-hidden="true"></i>'}).notify('click', {
					type: 'hr-nav-button-toggled',
					data: {}
				});
				
				this.card.makeNode('cardBody', 'column', {width: 12, css: 'hidden'});		
					
			}


						
		}
	
		function hrDisplayPrep(staff, hrToggle){ /// default view, laysout details, documents, decisions, comments
	
			function detailsColumn(staffObj, toggle){////DETAILS PANEL VIEW

				this.empty();/////DETAILS 'HEADER' VIEW
				
				cardPrep.bind(this)('details', toggle.details); 
		
				//////DETAILS BODY VIEW (TOGGLE)
				cardBody = this.card.cardBody;
				var left = cardBody.makeNode('leftCol', 'column', {width: 6});
				var right = cardBody.makeNode('rightCol', 'column', {width: 6});				
				left.makeNode('email', 'headerText', {size:'xx-small', text:'<span class="small">Email:</span> '+staffObj.email});
				left.makeNode('phone', 'headerText', {size:'xx-small', text:'<span class="small">Phone:</span> '+sb.dom.formatPhone(staffObj.phone)});
				left.makeNode('id', 'headerText', {size:'xx-small', text:'<span class="small">ID:</span> '+staffObj.id});
				left.makeNode('pin', 'headerText', {size:'xx-small', text:'<span class="small">PIN:</span> '+staffObj.pin});
				left.makeNode('status', 'headerText', {size:'xx-small', text:'<span class="small">Status:</span> '+staffObj.status.name});
				left.makeNode('vacation', 'headerText', {size:'xx-small', text:'<span class="small">Vacation Days:</span> '+staffObj.vacation_days});
				left.makeNode('sp', 'lineBreak', {spaces: 1});
				left.makeNode('jobs', 'headerText', {size:'xx-small', text:'<span class="small">Services:</span>'});
				
				_.each(staffObj.service, function(job){
					
					this.makeNode('job-'+job.id, 'headerText', {size:'xx-small', text:'<span class="small"><i class="fa fa-circle"></i></span> '+job.name});
					
				}, left);
								
				right.makeNode('locations', 'headerText', {size:'xx-small', text:'<span class="small">Locations:</span>'});
				
				_.each(staffObj.base, function(base){
					
					this.makeNode('base-'+base.id, 'headerText', {size:'xx-small', text:
						'<address>'+
							'<strong>'+ base.name +'</strong><br>'+
							base.street +'<br>'+
							base.city +', '+ base.state +' '+ base.zip +'<br>'+
							base.country +
						'</address>'
					});
					
				}, right);
				
				this.toggle = toggleCSSshowHide;
				this.cardPrepare = cardPrep;		
				this.patch();
						
			}
			function documentsColumn(staffObj, toggle){ ////DOCUMENTS PANEL
																										
				this.empty();//////PANEL 'HEADER' VIEW
				
				cardPrep.bind(this)('documents', toggle.documents);

				////////PANEL BODY VIEW (TOGGLE)
				cardBody = this.card.cardBody;		

				cardBody.makeNode('documentsCont', 'column', {width: 12});
				var matchedDocument = staffObj.staff_paperwork[0];
				
				_.each(docTypes, function(doc, i){

					var panelColor = 'pda-Panel pda-panel-red', 
					    panelBackground = 'background-color:rgb(247, 242, 242)',
					    panelButton = '<i class="fa fa-upload" aria-hidden="true"></i> Upload',
					    notificationType = 'upload-new-document-button-clicked',
					    deleteBtnCSS = 'pda-btn-x-small pda-transparent pda-align-right hidden',
					    viewBtnCSS = 'pda-btn-x-small pda-transparent pda-center';

					var matchedDocument = _.find(staffObj.staff_paperwork, function(match){

						return match.document_type === doc.id;
						
					});

					if (matchedDocument){ 
							
						panelColor = 'pda-Panel pda-panel-green',
						panelBackground = 'background-color: rgb(245,245,245)',
						panelButton = '<i class="fa fa-eye fa-lg" aria-hidden="true"></i> View',
						notificationType = 'view-document-card-clicked',
						deleteBtnCSS = 'pda-btn-x-small pda-transparent pda-align-right',
						viewBtnCSS = 'pda-btn-x-small pda-transparent pda-align-left';
																	
					} 
			
					this.makeNode('p-' + doc.id, 'column', {width: 3});
					
					this['p-' + doc.id].makeNode('colOne', 'container', {css: panelColor, style: panelBackground });
					this['p-' + doc.id].colOne.makeNode('delCont', 'column', {width: 12});
	
				
					this['p-' + doc.id].colOne.makeNode('textCont', 'container', {css: 'pda-text-center'});
					this['p-' + doc.id].colOne.textCont.makeNode('name', 'headerText', {size:'xx-small', text: '<span class="small">Document:</span>'});
					this['p-' + doc.id].colOne.textCont.makeNode('title', 'headerText', {size:'xx-small', text: doc.name });
					this['p-' + doc.id].colOne.textCont.makeNode('expDate', 'headerText', {style: 'height: 1px;'});

								
					if (matchedDocument && matchedDocument.need_expiration_date == 'yes') {

						this['p-' + doc.id].colOne.textCont.makeNode('expDate', 'headerText', {size:'xx-small', text: '<span class="small">Expires - ' + moment(matchedDocument.expiration_date).format("MM/DD/YY") + '</span>', css: 'pda-text-center', style: 'height: 10px;'});

					} 					

		
					this['p-' + doc.id].colOne.makeNode('viewContainer', 'container', {css: 'pda-center', style: 'border-top: 1px solid rgb(241,241,241)'});
					
					this['p-' + doc.id].colOne.viewContainer.makeNode('btn', 'button', {css: viewBtnCSS, size: 'xx-small', text: panelButton}).notify('click', {
						type: notificationType,
						data: {
							staffObj: staffObj,
							modal: modal,
							docType: doc
						}
					}, sb.moduleId);
					
					this['p-' + doc.id].colOne.viewContainer.makeNode('deleteBtn', 'button', {css: deleteBtnCSS, size: 'xx-small', text: '<i class="fa fa-trash-o fa-lg" aria-hidden="true"></i>'}).notify('click', {
						type: 'remove-document-card-clicked',
						data: {
							staffObj: staffObj,
							document: matchedDocument,
							modal: modal,
							docType: doc
						}
					}, sb.moduleId);

				}, cardBody.documentsCont);
			
				this.toggle = toggleCSSshowHide;
			
				this.patch();
				
			}
			function decisionsColumn(staff, toggle){

				this.empty();

				this.cardPrep = cardPrep;
				this.cardPrep('decisions', toggle.decisions);
				cardBody = this.card.cardBody;				
				cardBody.makeNode('decCont', 'container', {});
				
				this.toggle = toggleCSSshowHide;
				
				this.patch();
				
				components.decisions = sb.createComponent('decisions');
				components.decisions.notify({
					type:'show-decision-dashboard',
					data:{
						domObj: this.card.cardBody.decCont,
						createdBy: sb.data.cookie.userId,
						askedTo: sb.data.cookie.userId,
						objectId: staff.id,
						objectType: 'staff'
					}
				});
			}
			function commentsColumn(staff, toggle){
				
				this.empty();

				cardPrep.bind(this)('comments', toggle.comments);
				cardBody = this.card.cardBody;		
			
				cardBody.makeNode('commCont', 'container', {});
				this.toggle = toggleCSSshowHide;
				
				this.patch();
				
				//components.notes = sb.createComponent('notes2');
				sb.notify({
					type: 'show-note-list-box',
					data: {
						domObj: this.card.cardBody.commCont,
						objectIds:[staff.id],
						objectId:staff.id
					}
				});				
		
/*
				components.tasks = sb.createComponent('tasks2');
				components.tasks.notify({
					type: 'show-task-list',
					data: {
						domObj: cardBody.commCont,
						objectIds:[staff.id],
						objectId:staff.id,
						compact:true
					}
				});
*/				

			}
			this.empty();

			if ( !_.isEmpty(staffObj) ) {	
					
				hrDetails = this.makeNode('hrInfoCol', 'container', {});
				hrDetails.prep = detailsColumn;
				
				hrDocs = this.makeNode('hrDocsCol', 'container', {});
				hrDocs.prep = documentsColumn;
										
				hrDec = this.makeNode('hrDecCol', 'container', {});
				hrDec.prep = decisionsColumn;
				
				hrCom = this.makeNode('hrCommCol', 'container', {});
				hrCom.prep = commentsColumn;
				
			this.patch();

				hrDetails.prep.bind(hrDetails)(staff, hrToggle);			
				hrDocs.prep.bind(hrDocs)(staff, hrToggle);
				hrDec.prep.bind(hrDec)(staff, hrToggle);
				hrCom.prep.bind(hrCom)(staff, hrToggle);		

			} else { 
					
				this.makeNode('sp', 'lineBreak', {spaces: 15}); 
			
			}
						 		
		}
		
		function availDisplayPrep(){
			
			this.empty();
			
			this.makeNode('test', 'text', {text: 'availability window'});
			this.makeNode('availCont', 'panel', {css: 'pda-background-blue'});
/*
			
			components.scheduling = sb.createComponent('scheduling');
			components.scheduling.notify({
				type: 'show-scheduling',
				data: {
					domObj: this.availCont.body
				}
			});
*/
			
			this.patch();
		}
		
		function payrollDisplayPrep(){
			
			this.empty();
			
			this.makeNode('test', 'headerText', {text: 'payroll display window'});
			
			this.patch();
		}
		
		function filesDisplayPrep(staffObj, data){

			this.empty();
			
			this.makeNode('fileDisplay', 'panel', {header: 'files display test'});
			
			this.patch();
				
/*
				components.fileNav = sb.createComponent('file-nav');
				components.fileNav.notify({
					type: 'start-file-nav',
					data: {
						domObj: this.fileDisplay.body,
						files: data.fileData,
						fileTypes: data.fileTypes,
						objectData: staffObj,
						permissions: {
							edit: true
						}
					}
				});	
*/
				
				
				components.fileNav = sb.createComponent('file-nav');
				components.fileNav.notify({
					type: 'start-file-nav',
					data: {
						domObj: domObj.eventPanel.body.detailsColumn.column.toolTabs.tabs.files,
						files: dbData.files,
						fileTypes: dbData.fileTypes,
						objectData: dbData.allStaff,
						permissions: {
							edit: true
						}
					}
				});
			
			
		}
		
		function accountDisplayPrep(staffObj){
			
			this.empty();
			
			accCont = this.makeNode('account', 'container', {css: 'pda-container'});
							
			components.users = sb.createComponent('userComponent');

			components.users.notify({
				type:'show-user-account-info',
				data:{
					domObj: accCont,
					userObj:{
						fname:staffObj.fname,
						lname:staffObj.lname,
						email:staffObj.email,
						type:'staff',
						related:staffObj.id,
						enabled:1,
						phone:staffObj.phone
					},
					singleView: true
				}
			});			
			
			
			
			this.patch();
		}

		this.empty();
						
		panelPrep.bind(this)(staffObj, buttons, selectedBtn); /// window header with staff name and nav buttons
		
		this.patch();
						
			switch(selectedBtn){ /// views created based on nav selection
				
				case 'availability':
				
				availDisplayPrep.bind(windowView)();
	
				break;
				
				case 'payroll':
				
				payrollDisplayPrep.bind(windowView)();
	
				break;
				
				case 'files':
				
				filesDisplayPrep.bind(windowView)(staffObj, fileData);
	
				break;
				
				case 'account':
				
				accountDisplayPrep.bind(windowView)(staffObj);
				
				break;	
				
				default:
				
				hrDisplayPrep.bind(windowView)(staffObj, toggle);
				
				break;
				
			}
				
	} /// *** detailsCo responsible for all of views on right column
	
	function settingsPrepare(){
		
		function preparePermissions(){
			
			if(components.hasOwnProperty('permissionsTable')){
				components.permissionsTable.destroy();
			}			
			
			this.makeNode('permCont', 'container', {css: 'pda-container'});
							
			components.permissionsTable = sb.createComponent('crudPaged');
			components.permissionsTable.notify({
				type: 'display-crud-table-paged',
				data: {
					buttons: {
						create: true,
						view: false,
						edit: true,
						erase: true,
					},
					cells: {},
					data: function(callback, paged){
						
						sb.data.db.obj.getAll('staff_job_types', function(objs){
								
							callback(objs);
							
						}, 1, paged);
						
					},
					domObj: this.permCont,
					objectType:'staff_job_types',
					visibleCols: ['name']
				}
			});					
						
		}
		
		function prepareViews(){
			
			
			if(components.hasOwnProperty('viewsTable')){
				components.viewsTable.destroy();
			}			
			
			this.makeNode('viewsCont', 'container', {css: 'pda-container'});
							
			components.viewsTable = sb.createComponent('crudPaged');
			components.viewsTable.notify({
				type: 'display-crud-table-paged',
				data: {
					buttons: {
						create: true,
						view: false,
						edit: true,
						erase: true,
					},
					cells: {},
					data: function(callback, paged){
						
						sb.data.db.obj.getAll('user_views', function(objs){
								
							callback(objs);
							
						}, 1, paged);
						
					},
					domObj: this.viewsCont,
					objectType:'user_views',
					visibleCols: ['name']
				}
			});
				
		}
		
		function prepareStaffStatus(){
			
			
			if(components.hasOwnProperty('statusTable')){
				components.statusTable.destroy();
			}			
			
			this.makeNode('statusCont', 'container', {css: 'pda-container'});
							
			components.statusTable = sb.createComponent('crudPaged');
			components.statusTable.notify({
				type: 'display-crud-table-paged',
				data: {
					buttons: {
						create: true,
						view: false,
						edit: true,
						erase: true,
					},
					cells: {},
					data: function(callback, paged){
						
						sb.data.db.obj.getAll('staff_status', function(objs){
								
							callback(objs);
							
						}, 1, paged);
						
					},
					domObj: this.statusCont,
					objectType:'staff_status',
					visibleCols: ['name']
				}
			});			
			
		}
		
		function prepareJobTypes(){
			
			if(components.hasOwnProperty('jobTypesTable')){
				components.jobTypesTable.destroy();
			}			
			
			this.makeNode('jobTypesCont', 'container', {css: 'pda-container'});
							
			components.jobTypesTable = sb.createComponent('crudPaged');
			components.jobTypesTable.notify({
				type: 'display-crud-table-paged',
				data: {
					buttons: {
						create: true,
						view: false,
						edit: true,
						erase: true,
					},
					cells: {},
					data: function(callback, paged){
						
						sb.data.db.obj.getAll('staff_job_types', function(objs){
								
							callback(objs);
							
						}, 1, paged);
						
					},
					domObj: this.jobTypesCont,
					objectType:'staff_job_types',
					visibleCols: ['name']
				}
			});
		}
		
		function prepareDocumentTypes(){
			
			if(components.hasOwnProperty('docTypesTable')){
				
				components.docTypesTable.destroy();
			}
			
			this.makeNode('docTypesCont', 'container', {css: 'pda-container'});
			
			components.docTypesTable = sb.createComponent('crudPaged');
			components.docTypesTable.notify({
				type: 'display-crud-table-paged',
				data: {
					buttons: {
						create: {
							type: 'create-new-document-type-button-clicked',
							data: {}
						},
						view: false,
						edit: true,
						erase: true,
					},
					cells: {},
					data: function(callback, paged){
						
						sb.data.db.obj.getAll('staff_paperwork_type', function(objs){

							callback(objs);
							
						}, 1, paged);
						
					},
					domObj: this.docTypesCont,
					objectType:'staff_paperwork_type',
					visibleCols: ['id', 'name']
				}
			});
			
		}
		
		this.empty();
		
		this.makeNode('setCont', 'container', {css: 'pda-container'});
		
		setDoc = this.setCont.makeNode('documentPanel', 'panel', {header: 'Document Types', css: 'pda-panel-blue'});
		this.setCont.makeNode('sp0', 'lineBreak', {spaces: 1});
		setDoc.prep = prepareDocumentTypes;
		
		setPer = this.setCont.makeNode('permCol', 'panel', {header: 'Permissions', css: 'pda-panel-blue'});
		this.setCont.makeNode('sp1', 'lineBreak', {spaces: 1});
		setPer.prep = preparePermissions;
		
		setView = this.setCont.makeNode('viewsPanel', 'panel', {header: 'Views', css: 'pda-panel-blue'});
		this.setCont.makeNode('sp2', 'lineBreak', {spaces: 1});
		setView.prep = prepareViews;
		
		setStat = this.setCont.makeNode('staffStatPanel', 'panel', {header: 'Staff Status', css: 'pda-panel-blue'});
		this.setCont.makeNode('sp3', 'lineBreak', {spaces: 1});
		setStat.prep = prepareStaffStatus;
		
		setJob = this.setCont.makeNode('jobTypesPanel', 'panel', {header: 'Job Types', css: 'pda-panel-blue'});
		setJob.prep = prepareJobTypes;
		
		setDoc.prep();
		setPer.prep();
		setView.prep();
		setStat.prep();
		setJob.prep();
		
		this.patch();

	} ///***settingsPrepare loads new page with settings options within this component
	
	function calendarPrepare(){
		
		var docObjects = [];
		
		_.each(updStaffList, function(staff){

			_.map(staff.staff_paperwork, function(doc){

				if(doc.need_expiration_date == 'yes'){
					
					docObjects.push(
					
						{
							id: doc.id,
							name: staff.fname + ' ' + staff.lname + '</br>' + doc.name,
							startTime: moment(doc.expiration_date),
							endTime: moment(doc.expiration_date).add(12, "hour"),
							description: doc.description,
							type: 'Document Expires',
							color: 'pda-background-red',
							buttons: {
								view: {
									text: 'View Staff Member',
									css: 'pda-btn-green',
									type: 'staff-paperwork-dashboard',
									data: {
										staff: staff
									}
								}
							},
							group: 'contacts'
						}
					);
						
				}
					
			});
			
		});	

		this.empty();
		
		this.makeNode('calendarPanel', 'panel', {css: 'pda-panel-blue', header: 'Document Expiration Dates'});
		
		this.patch();
		
			components.calendar = sb.createComponent('calendar');
			
			components.calendar.notify({
				type: 'display-calendar',
				data: {
					domObj: this.calendarPanel.body,
					events: docObjects,
					viewDate: moment(),
					viewType: 'month',  // could be changed to any of the other view types
					cellBtns: {
						add: {
							text: '<i class="fa fa-plus" aria-hidden="true"></i>',
							type: 'cell-btn-notify',
							css: '',
							data: {} // pass moment obj of a given day
						},
						erase: {
							text: '<i class="fa fa-minus" aria-hidden="true"></i>',
							type: 'cell-btn-test',
							css: '',
							data: {}
						}
					},
 					//calSize: 'pda-cal-small', //[USE THIS PROPERTY TO DISPLAY A SMALLER MONTH VIEW]
					calMenu: {
						list: {
							text: 'List',
							type: 'list',
							show: true
						},
						day: {
							text: 'Day',
							type: 'day',
							show: true
						},
						threeDay: {
							text: '3 Days',
							type: 'threeDays',
							show: true	
						},
						week: {
							text: 'Week',
							type: 'week',
							show: true
						},
						month: {
							text: 'Month',
							type: 'month',
							show: true
						}
					}
					
				}	
			});
			
			
	
	}
	
	function appendDocTypeForm(modDom, modForm, fields){
		
		modDom.empty(); 

		_.each(fields, function(field, i){
			
		    modDom.makeNode('fieldCol-'+i, 'column', {width: 12, dataId: i});
		    modDom['fieldCol-' + i].makeNode('deleteBtn', 'button', {css: 'pda-transparent pda-align-right', text: 'X'}).notify('click', {
			    type: 'update-doc-type-delete-field',
			    data: {
				    fieldIndex: i,
				    removeField: true,
				    container: modDom,
				    form: modForm
			    }
		    }, sb.moduleId);			    
		    modDom['fieldCol-' + i].makeNode('form', 'form', [field]);
			
		});	
		
		modDom.patch();
			
	}

					
	return {
		
		init: function(){
			
			sb.listen({
				'staff-paperwork-dashboard': this.load,
				'staff-paperwork-settings': this.settings,
				'staff-view-button-clicked': this.staffView,
				'staff-list-calendar-view': this.calendarView,
				'nav-button-clicked': this.navButtonClicked,
				'hr-nav-button-toggled': this.hrNavButtonToggled,
				'create-new-document-type-button-clicked': this.createNewDocumentType,
				'create-new-document': this.createNewDocument,
				'upload-new-document-button-clicked': this.uploadNewDocumentClick,
				'process-upload-document-button-clicked': this.processUploadDocumentClick,
				'update-doc-type-add-field': this.updateDocFormAddField,
				'update-doc-type-delete-field': this.updateDocFormDeleteField,
				'update-doc-type-create-form': this.updateDocForm,
				'update-doc-type-create-form-download': this.updateDocFormDownload,
				'save-new-doc-type-button-clicked': this.saveDocForm,
				'remove-document-card-clicked': this.removeDocument,
				'view-document-card-clicked': this.viewDocument,
				"staff_paperwork-object-created": this.updateNewObject,
				'hr-external-link-button-clicked': this.fromStaffTableView
				
			});
			
		},
		
		load: function(setup){ ///layout 3 seperate UI elements into system

			if(!setup.hasOwnProperty('domObj')){ ui.empty(); } else { ui = sb.dom.make(setup.domObj.selector) }
			
			modal = ui.makeNode('modal', 'modal', {});
			ui.makeNode('uiNavCol', 'column', {width: 12});
			ui.uiNavCol.makeNode('load', 'button', {dataId:'settings', css:'pda-btnOutline-blue pda-btn-large pda-transparent pull-right', text:'<i class="fa fa-cogs"></i>'}).notify('click', {
				type:'staff-paperwork-settings',
				data:{
					page:'settings',
					object: true,
					staff: selectedStaffObj
				}
			}, sb.moduleId);			
						
			navButtons = [{
				name: 'hr',
				displayText: '<i class="fa fa-users" aria-hidden="true"></i> HR',
				notificationObj: {
					type: 'nav-button-clicked',
					data: {
						selected: 'hr',
					}
				}
			},{
				name: 'availability',
				displayText: '<i class="fa fa-calendar" aria-hidden="true"></i> Availability',
				notificationObj: {
					type: 'nav-button-clicked',
					data: {
						selected: 'availability'
					}
				}
			},{
				name: 'payroll',
				displayText: '<i class="fa fa-money" aria-hidden="true"></i> Payroll',
				notificationObj: {
					type: 'nav-button-clicked',
					data: {
						selected: 'payroll'
					}
				}
			},{
				name: 'files',
				displayText: '<i class="fa fa-files-o" aria-hidden="true"></i> Files',
				notificationObj: {
					type: 'nav-button-clicked',
					data: {
						selected: 'files'
					}
				}
			},{
				name: 'account',
				displayText: 'Account',
				notificationObj: {
					type: 'nav-button-clicked',
					data: {
						selected: 'account',
					}
				}
			}];
						
			list = ui.makeNode('dashCol', 'column', {width: 4});		
			list.prepare = listCo;
			
			details = ui.makeNode('sideCol', 'column', {width: 8});
			details.prepare = detailsCo;

			if(setup.hasOwnProperty('staff')){

				selectedStaffObj = setup.staff;
								
				list.prepare(updStaffList);
				
				details.prepare(selectedStaffObj, navButtons, 'hr', hrToggle);
				
				ui.build();

				
				
			} else {

				list.prepare();
				
				details.prepare(selectedStaffObj, navButtons, 'hr', hrToggle);
							
				ui.build();	

				dbc.paperwork(function(docs){ 
	
					docTypes = docs; 
					
				});				
				
			}
							
		}, /// *** invokes private listCo and detailsCo
		
		destroy: function(){
			
			_.each(components, function(comp){ comp.destroy(); });
			
			ui = {},
			modal = {},	 
			list = {},
		     details = {},
		     windowView = {},
	
		     hrDetails = {},
		     hrDocs = {},
		     hrDec = {},
		     hrCom = {},
		     cardBody = {},
		     
			components = {},
			updStaffList = {},
			selectedStaffObj = {},
			navButtons = [],
			navSelectedBtn = '',
			hrToggle = {
				details: true,
				documents: true,
				decisions: true,
				comments: true
			},
			
			fields = [],
			docTypes = [],
			fileDataObj = {};
		
		},
		
		staffView: function(data){

			if (data.hasOwnProperty('object')){

			     selectedStaffObj = data.object,
     		     navSelectedBtn = 'hr';
     		
			}

			details.prepare(selectedStaffObj, navButtons, navSelectedBtn, hrToggle, fileDataObj);

							     
	     }, /// *** loads details view object with staff info
		
		navButtonClicked: function(data){
			
			navSelectedBtn = data.selected;
						
			details.prepare(selectedStaffObj, navButtons, navSelectedBtn, hrToggle);
					
		}, /// *** handles navigation with details view object
		
		hrNavButtonToggled: function(data){

			if(data.hasOwnProperty('dataId2')){
				navSelectedBtn = data.dataId2;
			}
			
			switch (data.dataId){
				
				case 'details':

					if (hrToggle.details == true) {
						
						hrDetails.toggle(true, data);
						
					} else {
						
						hrDetails.toggle(false, data);
																	
					}

					hrToggle.details = !hrToggle.details;						
				break;
				
				case 'documents':
									
					if (hrToggle.documents == true) {

						hrDocs.toggle(true, data);
										
					} else {
						
						hrDocs.toggle(false, data);				
						
					}
					
					hrToggle.documents = !hrToggle.documents;
										
					break;
					
				case 'decisions':
				
					if (hrToggle.decisions == true) {
						
						hrDec.toggle(true, data);
						
					} else {
						
						hrDec.toggle(false, data);
						
					}
					
					hrToggle.decisions = !hrToggle.decisions;
					
					break;
					
				case 'comments':
				
					if (hrToggle.comments == true) {
						
						hrCom.toggle(true, data);
						
					} else {
						
						hrCom.toggle(false, data);
						
					}
					
					hrToggle.comments = !hrToggle.comments;
					
					break;
			}

		}, /// *** handles navigation within hrDisplayPrep private fn
		
		uploadNewDocumentClick: function(data){

			if( data.hasOwnProperty('modal') ) { modal = data.modal; }
			
			var formFields = {
				
				name: {
					type: 'text',
					name: 'name',
					label: 'Name',
					value: data.docType.name
				},			
				document_type: {
					name: 'document_type',
					value: data.docType.id,
					type: 'hidden'
				},
				description: {
					type: 'text',
					name: 'desc',
					label: 'Description',
					value: data.staffObj.lname + ' ' +  data.docType.name + ' Upload'
				},
				staff: {
					name: 'staff',
					value: data.staffObj.id,
					type: 'hidden'
				}
			};
			

			if (data.docType.hasOwnProperty('need_file') && data.docType.need_file == 'yes') {

				formFields.document_file = {
					type: 'file-upload',
					label: 'Select a File',
					name: 'document_file'
				}

			}

			if ( data.docType.hasOwnProperty('need_expiration_date') && data.docType.need_expiration_date == 'yes') {
				
				formFields.expiration_date = {
					type: 'date',
					label: 'Expiration Date',
					name: 'expiration_date'	
				}
				
			}			
			
			_.each(data.docType.fields, function(field){

				formFields['detail-' + field.id] = {
					type: 'text',
					label: field.name,
					name: 'detail-' + field.id
				}
								
			});
						
			modal.body.makeNode('header', 'headerText', {text: 'Upload Document', size: 'small', css: 'text-center'});
			
			modal.body.makeNode('uploadForm', 'form', formFields);
			
			modal.footer.makeNode('upldadBtn', 'button', {css: 'btn-block btn-success', text: 'Upload'}).notify('click', {
				type: 'process-upload-document-button-clicked',
				data: {
					form: modal.body.uploadForm
				}
			}, sb.moduleId);
			
			modal.body.patch();
			modal.footer.patch();
			
			modal.show();
			

		},
		
		processUploadDocumentClick: function(data){

			var formData = data.form.process();
			    staffPaperworkObj = {};
			    
			_.each(formData.fields, function(fieldData, fieldKey){
				
				staffPaperworkObj[fieldKey] = fieldData.value;
				
			}); 
			
			sb.data.db.obj.create('staff_paperwork', staffPaperworkObj, function(docData){

				if(docData){
					
					sb.dom.alerts.alert('Saved!', 'You have uploaded a new document', 'success');
					modal.hide();
										
					selectedStaffObj.staff_paperwork.push(docData);

					_.map(updStaffList, function(staff){
						
						if(staff.id == selectedStaffObj.id){

							staff.staff_paperwork = selectedStaffObj.staff_paperwork;
						}
						
					});			
					
					list.prepare(updStaffList);
					
					hrDocs.prep.bind(hrDocs)(selectedStaffObj, hrToggle);					
					
				}else{
					
					sb.dom.alerts.alert('Error!', 'Please refresh and try again.', 'error');
					
				}
				
			});			   
			
		},
		
		createNewDocument: function(data){

			sb.obj.create(data.modal, 'staff_paperwork');
			
			
		},
		
		createNewDocumentType: function(data){
			
			modal.body.makeNode('header', 'headerText', {size: 'small', css: 'text-center', text: 'Create New Document Type'});
			
			var formArgs = {
				
					name: {
						name: 'name',
						type: 'text',
						label: 'Name'
					},
					description: {
						name: 'description',
						type: 'text',
						label: 'Description'
					},
					service_type: {
						name: 'service_type',
						type: 'checkbox',
						label: 'Service Type',
						options: []
					},
					need_file_download: {
						name: 'need_file_download',
						type: 'select',
						label: 'Need to Provide Document to Download?',
						options: [
							{
								name: 'Yes',
								label: 'Yes',
								value: 'Yes'
							},
							{
								name: 'No',
								label: 'No',
								value: 'No'
							}	
						]
					},
					file_download: {
						type: 'file-upload',
						label: 'Select a File for Staff to Download',
						name: 'file_download'
					},
					need_file_upload: {
						name: 'need_file_upload',
						type: 'select',
						label: 'Require a Document upload from Staff?',
						options: [
							{
								name: 'Yes',
								label: 'Yes',
								value: 'Yes'
							},
							{
								name: 'No',
								label: 'No',
								value: 'No'
							}
						]
					},
					need_exp_date: {
						name: 'need_exp_date',
						type: 'select',
						label: 'Does this Document require an expiration date?',
						options: [
							{
								name: 'No',
								label: 'No',
								value: 'No'
							},
							{
								name: 'Yes',
								label: 'Yes',
								value: 'Yes'
							}							
						]
					},
					not_frequency: {
						name: 'not_frequency',
						type: 'hidden',
						label: 'How many days to alert before expiration date?'
					}
										
			};
			
			sb.data.db.obj.getAll('inventory_service', function(services){

				_.each(services, function(serv){

					formArgs.service_type.options.push({
						name: 'service_type',
						value: serv.id,
						label: serv.name
						
				});	
				
				
				if(data.object){
					formArgs.name.value = data.object.name;
					
					formArgs.id = {
						name: 'id',
						type: 'hidden',
						value: data.object.id
					}
				}			
				
				modal.body.makeNode('form', 'form', formArgs);
				
				modal.body.makeNode('optionsCont', 'container', {css: ''});
				
				modal.body.makeNode('addBtn', 'button', {css: 'pda-transparent pda-btn-x-small', text: '<i class="fa fa-plus fa-lg" aria-hidden="true"></i> Add Options Field'}).notify('click', {
					type: 'update-doc-type-add-field',
					data: {
						form: modal.body.form,
						container: modal.body.optionsCont,
						addNewField: true
					}
				}, sb.moduleId);
				
	
				modal.body.form.need_file_download.notify('change', {
					type: 'update-doc-type-create-form-download',
					data: {
						form: modal.body.form,
						modal: modal,
						file_download: true
					}
				}, sb.moduleId);
				
				modal.body.form.need_exp_date.notify('change', {
					type: 'update-doc-type-create-form-download',
					data: {
						form: modal.body.form,
						modal: modal,
						not_frequency: true
					}
				}, sb.moduleId);				
				
				modal.footer.makeNode('save', 'button', {text: 'Save', css: 'btn-block btn-success'})
					.notify('click', {
						type: 'save-new-doc-type-button-clicked',
						data: {
							form: modal.body.form,
							container: modal.body.optionsCont,
							footer: modal.footer,
							modal: modal
						}
					});
				
					
				modal.body.patch();
				modal.footer.patch();
				modal.show();		
					
				});				
				
			});
		
		},
		
		updateDocFormAddField: function(data){

			_.each(fields, function(field, i){
				
				var formData = data.container['fieldCol-' + i].form.process().fields;
				
				field.value = formData.field.value;

			});
			
			var modDom = data.container,
			    modForm = data.form;
			    
 			if ( data.hasOwnProperty('addNewField') && data.addNewField ) {
				
				fields.push({
					
						name: 'field',
						type: 'text',
						label: 'Name',						
				});
				
			}
			
			appendDocTypeForm(modDom, modForm, fields); 
			 
		},
		
		updateDocFormDeleteField: function(data){
			
			var modDom = data.container,
			    modForm = data.form;

			_.each(fields, function(field, i){
				
				var formData = data.container['fieldCol-' + i].form.process().fields;
				
				field.value = formData.field.value;

			});
			
			if(data.hasOwnProperty('removeField') && data.removeField == true){
				fields.splice(data.fieldIndex, 1);
							
			} 
			
			appendDocTypeForm(modDom, modForm, fields);
			    					
		},
		
		updateDocForm: function(data){
console.log(data);			

			_.each(fields, function(field, i){
				
				var formData = data.container['fieldCol-' + i].form.process().fields;
				
				field.value = formData.field.value;
				
			});
						
			var modDom = data.container,
			    modForm = data.form;	
		    
			
		    appendDocTypeForm(modDom, modForm, fields);
			
		    modDom.empty();
		    		    
			_.each(fields, function(field, i){
				
			    modDom.makeNode('fieldCol-'+i, 'column', {width: 12});
			    modDom['fieldCol-' + i].makeNode('deleteBtn', 'button', {css: 'pda-transparent pda-align-right', text: 'X'}).notify('click', {
				    type: 'update-doc-type-delete-field',
				    data: {
					    fieldIndex: i,
					    removeField: true,
					    container: data.container
				    }
			    }, sb.moduleId);			    
			    modDom['fieldCol-' + i].makeNode('form', 'form', [field]);
				
			});
			
		    modDom.patch();	    
			
		},
		
		updateDocFormDownload: function(data){

			var formData = data.form.process().fields;
			
			function downloadField(){
				
				if(formData.need_file_download.value == 'Yes'){
				
					data.form.file_download.update({
							type: 'file-upload'
					});
									
				} else {
					
					data.form.file_download.update({
						type: 'hidden'
					});
				}
				
			}
			
			function frequencyField(){

				if(formData.need_exp_date.value == 'Yes'){
					
					data.form.not_frequency.update({
						type: 'int'
					});
				} else {
					
					data.form.not_frequency.update({
						type: 'hidden'
					});
				}
			}
			
				if(data.file_download){
	
					downloadField();
					
				} else if(data.not_frequency){
	
					frequencyField();
				}				
			
		},
		
		saveDocForm: function(data){

			var formData = data.form.process(),
			    docTypeObj = {};
console.log(formData);
			_.each(formData.fields, function(fieldData, fieldKey){
				
				docTypeObj[fieldKey] = fieldData.value;
				
			});	
			
			_.each(docTypeObj.service_type, function(type, i){
				
				docTypeObj.service_type[i] = parseInt(type);
				
			});
			
			docTypeObj.fields = [];
			
			_.each(fields, function(field, i){
				
				var formData = data.container['fieldCol-' + i].form.process().fields;
				
				docTypeObj.fields.push({
					
					name: formData.field.value
					
				});
				
			});
					
			sb.data.db.obj.create('staff_paperwork_type', docTypeObj, function(response){
				
				if (response) {
					
					sb.dom.alerts.alert('Saved!', 'You created a new document type', 'success');
					modal.hide();	
					
					settingsUI.prepare();
					
					ui.build();				
					
				}	
				
				
			});
			
		},

	     viewDocument: function(data){

	     }, ///*** hrDisplayPrep --> documentsColumn

	     removeDocument: function(data){
console.log(data);
			sb.dom.alerts.ask({
				title: 'Are you positive',
				text: 'that you want to delete this document?'
			}, function(response){
								
				swal.disableButtons();
				
				if(response){

					sb.data.db.obj.erase('staff_paperwork', data.document.id, function(done){

						sb.dom.alerts.alert('Deleted!', '', 'success');
						
						var updDocList = _.reject(selectedStaffObj.staff_paperwork, function(paperworkDoc){

							return paperworkDoc.id === data.document.id
							
						});

						selectedStaffObj.staff_paperwork = updDocList;
						
						_.map(updStaffList, function(staff){
							
							if(staff.id == selectedStaffObj.id){
	
								staff.staff_paperwork = updDocList;
							}
							
						});

						list.prepare(updStaffList);
						
						hrDocs.prep.bind(hrDocs)(selectedStaffObj, hrToggle);
																			
					});

				}
				
			});
					     
	     }, ///*** hrDisplayPrep --> documentsColumn
			
		settings: function(setup){

			ui.empty();
			
			modal = ui.makeNode('modal', 'modal', {});
			
			ui.makeNode('settings', 'column', {width: 12, css: 'pda-container'});			
			
			ui.settings.makeNode('back', 'button', {css:'pda-btnOutline-blue pda-btn-small', text:'<i class="fa fa-arrow-circle-left fa-lg" aria-hidden="true"></i> Back to Staff View'}).notify('click', {
				type:'staff-paperwork-dashboard',
				data:{}
			}, sb.moduleId);			
			

			settingsUI = ui.settings.makeNode('settingsPanel', 'column', {width: 12});
			settingsUI.prepare = settingsPrepare;
			
			settingsUI.prepare();
			
			ui.build();
			

		}, /// *** clears out list and details view and loads settings page
		
		calendarView: function(setup){

			ui.empty();
			
			ui.makeNode('calendarCol', 'column', {width: 12, css: 'pda-container'});
	
			ui.calendarCol.makeNode('back', 'button', {css:'pda-btnOutline-blue pda-btn-small', text:'<i class="fa fa-arrow-circle-left fa-lg" aria-hidden="true"></i> Back to Staff View'}).notify('click', {
				type:'staff-paperwork-dashboard',
				data:{}
			}, sb.moduleId);
			
			calendarUI = ui.calendarCol.makeNode('calendarPanel', 'column', {width: 12});
			calendarUI.prepare = calendarPrepare;

			ui.build();			

			calendarUI.prepare();
			
			
		},
				
		updateNewObject: function(data){
	
			selectedStaffObj.staff_paperwork.push(data);


			var staffList = _.reject(updStaffList, function(staff){
				
				return staff.id == selectedStaffObj.id;
			});

			staffList.push(selectedStaffObj);
			
			list.prepare(staffList);
			
			hrDocs.prep.bind(hrDocs)(selectedStaffObj, hrToggle);

		},
		
		fromStaffTableView: function(data){
console.log(data);			
		}		
   
	}     
	
});