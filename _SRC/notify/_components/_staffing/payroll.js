Factory.register('payroll', function(sb) {
    
    var comps = {};
	
	function getCurrentDaysOffLeft(staff, callback){
		
		function get_used_days_off(staff, callback){
			
			var history = {
				daysTaken:[],
				extraShifts:[]
			};
			var vacationDaysStatus = moment().format();
			if(staff.vacation_days){
				vacationDaysStatus = staff.vacation_days.last_status.as_of;
			}
			
			sb.data.db.obj.getWhere('staff_availability_requests', {
				status:'approved',
				recurring:'no',
				start_time:{
					type:'after',
					value:moment(vacationDaysStatus).format('X')
				},
				related_object:staff.id
			}, function(usedDays){
				
				history.daysTaken = usedDays;
				
				sb.data.db.obj.getWhere('groups', {
// 					type:'shift',
					reimburses_vacation_day:1,
					start_date:{
						type:'after',
						value:moment(vacationDaysStatus).format('X')
					},
					user:staff.id
				}, function(shifts){

					history.extraShifts = shifts;
					callback(history);
					
				});
				
			});
			
		}
		
		function processDaysLeft(schedule, start, end, shifts, daysTaken, dayOffType){
			
			// set to last known qty
			var daysOff = schedule.last_status.days_left;
			
			// get months / vesting period
			var monthsPerPeriod;
			switch(schedule.vesting_period_type){
				
				case 'month':
				monthsPerPeriod = 1;
				break;
				
				case '2_months':
				monthsPerPeriod = 2;
				break;
				
				case '3_months':
				monthsPerPeriod = 3;
				break;
				
				case '4_months':
				monthsPerPeriod = 4;
				break;
				
				case '6_months':
				monthsPerPeriod = 6;
				break;
				
				case 'year':
				monthsPerPeriod = 12;
				break;
				
			}
			
			
			
			// run groupBy on shifts/requests -> group by 
			var groupedShifts = _.groupBy(shifts, function(shift){
				
				var monthsSinceLastUpdate = 
				
					Math.floor(-moment.duration(start.diff(shift.start_date, 'YYYY-MM-DD HH:mm:ss.SS'))
						.asMonths());

				return (monthsSinceLastUpdate/monthsPerPeriod).toString();
				
			});			
			var groupedDaysTaken = _.groupBy(daysTaken, function(dayOff){
				
				var monthsSinceLastUpdate = 
				
					Math.floor(-moment.duration(start.diff(dayOff.start_time, 'YYYY-MM-DD HH:mm:ss.SS'))
						.asMonths());

				return (monthsSinceLastUpdate/monthsPerPeriod).toString();
				
			});

			// get vesting periods since last update to data
			var vestingPeriodsSinceLastUpdate = 
			
					Math.floor(-moment.duration(start.diff(end))
						.asMonths());
			
			// vest periods one at a time, factoring in used days and 
			// reimbursed (from shifts taken on days regularly schedule 
			// to be off)
			for ( i = 0; i < vestingPeriodsSinceLastUpdate + 1; i++ ) {
				
				daysOff += schedule.qty_vested_per_period;
				
				// if in a new year, if days are not set to rollover, 
				// reset them to initial value
				if(schedule.does_carry_over === 'yes' && i*monthsPerPeriod === 12){
					
					daysOff = schedule.initial_qty;
					
				}
				
				// enforce vacation day cap
				if(daysOff > schedule.max_qty){
					daysOff = schedule.max_qty;
				}

				if( groupedDaysTaken[i.toString()] ) {
					
					daysOff -= _.reduce(groupedDaysTaken[i], function(memo, daysTaken){
						
						if(daysTaken['used_'+ dayOffType]){
							return memo + daysTaken['used_'+ dayOffType];
						}else{
							return memo;
						}
						
					}, 0)
					
				}
				
				if( groupedShifts[i.toString()] ) {
					
					daysOff += groupedShifts[i.toString()].length;
					
				}
				
			}
// 			console.log('daysOff', daysOff);
			
			return daysOff;
			
		}
		
		//!	WORKING HERE:
		// 	in order for this to work, something like:
		//
		// 		{
		// 			days_left:[INT]
		// 			as_of:[DATE]
		// 		}
		//
		//	needs to be updated and used as a spot to calculate 
		//	from. Otherwise, the cap will not have an effect.
		
		
		
		if( !_.isEmpty(staff.vacation_days) && _.isEmpty(staff.vacation_days.last_status) ){
			
			staff.vacation_days.last_status = {
				days_left:staff.vacation_days.initial_qty,
				as_of:staff.hire_date
			};
			
		}
		
		if( !_.isEmpty(staff.sick_days) && _.isEmpty(staff.sick_days.last_status) ){
			
			staff.sick_days.last_status = {
				days_left:staff.vacation_days.initial_qty,
				as_of:staff.hire_date
			};
			
		}
		
		get_used_days_off(staff, function(history) {
			
			var daysLeft = {
				vacation_days:0,
				sick_days:0
			};

			if( !_.isEmpty(staff.vacation_days) ) {
				
				daysLeft.vacation_days = processDaysLeft(
					staff.vacation_days, 
					moment(staff.vacation_days.last_status.as_of, 'YYYY-MM-DD HH:mm:ss.SS'), 
					moment(), 
					history.extraShifts, 
					history.daysTaken,
					'vacation_days'
				);
				
			}else{
				daysLeft.vacation_days = '<i class="text-muted">Not set</i>';
			}
			
			if( !_.isEmpty(staff.sick_days) ) {
				
				daysLeft.sick_days = processDaysLeft(
					staff.sick_days, 
					moment(staff.sick_days.last_status.as_of, 'YYYY-MM-DD HH:mm:ss.SS'), 
					moment(), 
					[], 
					[],
					'sick_days'
				);
				
			}else{
				daysLeft.sick_days = '<i class="text-muted">Not set</i>';
			}
			
// 		console.log('daysLeft->', daysLeft);
			callback(daysLeft);
			
		});
		
	}
	
	function build_jobTypes(dom, staff) {
			
		function build_payrollObj(dom, service, payroll, staff, type) {
			
			var formArgs = {
					pay_style: {
						name: 'pay_style',
						type: 'select',
						label: 'Pay Style',
						options: [],
						value: '',
						change: function(form, value) {		

							if(value === 'flat_and_hourly') {
								
								dom.wrapper.body.form.hourly_rate.update({
									type: 'usd'
								});
								
								dom.wrapper.body.form.flat_rate.update({
									type: 'usd'
								});
								
								dom.wrapper.body.form.max_flat_hours.update({
									type: 'number'
								});
								
								dom.wrapper.body.form.rate.update({
									type: 'hidden'
								});
								
							} else {
								
								dom.wrapper.body.form.hourly_rate.update({
									type: 'hidden'
								});
								
								dom.wrapper.body.form.flat_rate.update({
									type: 'hidden'
								});
								
								dom.wrapper.body.form.max_flat_hours.update({
									type: 'hidden'
								});
								
								dom.wrapper.body.form.rate.update({
									type: 'usd'
								});
								
							}
							
						}
					},
					rate: {
						name: 'rate',
						type: 'usd',
						label: 'Rate',
						value: ''
					},
					hourly_rate: {
						name: 'hourly_rate',
						type: 'hidden',
						label: 'Hourly rate',
						value: ''	
					},
					flat_rate: {
						name: 'flat_rate',
						type: 'hidden',
						label: 'Flat Rate',
						value: ''	
					},
					max_flat_hours: {
						name: 'max_flat_hours',
						type: 'hidden',
						label: 'Maximum hours of flat rate time before hourly billing starts',
						value: 0
					}
				};
			var payroll_settings_cache = {};
			
			if(payroll !== undefined) {
				
				payroll_settings_cache.pay_style = payroll.pay_style;
				payroll_settings_cache.flat_rate = payroll.flat_rate;
				payroll_settings_cache.hourly_rate = payroll.hourly_rate;
				payroll_settings_cache.max_flat_hours = payroll.max_flat_hours;
				payroll_settings_cache.rate = payroll.rate;	
				
			}
			
			function process_payroll(form, staff, payroll, service, load, after, type) {
				
				var formData = form.process().fields;
				
				function compare_payrollData(setup) {
					
					// setup.cachedPayroll --> old payroll settings
					// setup.formData --> data entered by the user in the form (may or may not be different from old payroll settings)
					// setup.method --> 'create' or 'update'
					// setup.service --> job type for related to payroll obj
					
					var jobType = setup.service;
					var billing_type = formData.pay_style.value;
					var rate = formData.rate.value;
					var hourly_rate = formData.hourly_rate.value;
					var flat_rate = formData.flat_rate.value;
					var max_flat_hours = formData.max_flat_hours.value;
					
					function display_jobType(type) {
						
						switch(type) {
							
							case 'hourly':
								return 'Hourly';
								break;
								
							case 'flat':
								return 'Flat';
								break;
								
							case 'salary':
								return 'Salary';
								break;
								
							case 'flat_and_hourly':
								return 'Flat and hourly';
								break;
							
							default:
								break;
							
						}
						
					}
					
					if(setup.method === 'create') {
						
						switch(billing_type) {
							
							case 'hourly':
							
								billing_type = 'Hourly';
							
								return '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ billing_type +'</div><div><strong>Rate:</strong> $'+ (parseInt(rate)/100).formatMoney() +' per hour</div>';
								break;
								
							case 'flat':
							
								billing_type = 'Flat';
								
								return '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ billing_type +'</div><div><strong>Rate:</strong> $'+ (parseInt(flat_rate)/100).formatMoney() +' flat fee</div>';
								break;
								
							case 'salary':
							
								billing_type = 'Salary';
								
								return '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ billing_type +'</div><div><strong>Rate:</strong> $'+ (parseInt(rate)/100).formatMoney() +' per year</div>';
								break;
								
							case 'flat_and_hourly':
							
								billing_type = 'Flat and hourly';
								
								return '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ billing_type +'</div><div><strong>Rate:</strong> $'+ (parseInt(flat_rate)/100).formatMoney() +' flat fee ($'+ (parseInt(hourly_rate)/100).formatMoney() +' per hour after '+ max_flat_hours +' hours)</div>';
								break;
								
							default:
								return;
						}
						
					} else if(setup.method === 'update') {
						
						// Checking if billing type changed
						if(setup.cachedPayroll.pay_style !== billing_type) { 
							
							var text = '<div><i>Billing type</i> for '+ jobType.name +' was updated from <strong>'+ display_jobType(setup.cachedPayroll.pay_style) +'</strong> to <strong>'+ display_jobType(billing_type) +'</strong></div>';
							
							switch(billing_type) {
							
								case 'hourly':
								
									return text + '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ display_jobType(billing_type) +'</div><div><strong>Rate:</strong> $'+ (parseInt(rate)/100).formatMoney() +' per hour</div>';
									break;
									
								case 'flat':
									
									return text + '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ display_jobType(billing_type) +'</div><div><strong>Rate:</strong> $'+ (parseInt(flat_rate)/100).formatMoney() +' flat fee</div>';
									break;
									
								case 'salary':
									
									return text + '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ display_jobType(billing_type) +'</div><div><strong>Rate:</strong> $'+ (parseInt(rate)/100).formatMoney() +' per year</div>';
									break;
									
								case 'flat_and_hourly':
									
									return text + '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ display_jobType(billing_type) +'</div><div><strong>Rate:</strong> $'+ (parseInt(flat_rate)/100).formatMoney() +' flat fee ($'+ (parseInt(hourly_rate)/100).formatMoney() +' per hour after '+ max_flat_hours +' hours)</div>';
									break;
									
								default:
									return;
							}
							
						} else {
							
							switch(billing_type) {
							
								case 'hourly':

									var changed_rate = '';

									if(parseInt(setup.cachedPayroll.rate) !== parseInt(rate)) {
										changed_rate = '<div><i>Hourly rate</i> was changed from <strong>$' + parseInt(setup.cachedPayroll.rate/100).formatMoney() + '</strong> to <strong>$'+ (parseInt(rate)/100).formatMoney() +'</strong></div>';
									} else {
										changed_rate = 'No changes were made';	
									} 
								
									return changed_rate + '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ display_jobType(billing_type) +'</div><div><strong>Rate:</strong> $'+ (parseInt(rate)/100).formatMoney() +' per hour</div>';
									break;
									
								case 'flat':
								
									var changed_rate = '';
								
									if(parseInt(setup.cachedPayroll.rate) !== parseInt(rate)) {
										changed_rate = '<div><i>Flat rate</i> was changed from <strong>$' + parseInt(setup.cachedPayroll.rate/100).formatMoney() + '</strong> to <strong>$'+ parseInt(rate/100).formatMoney() +'</strong></div>';
									} else {
										changed_rate = 'No changes were made';
									}
									
									return changed_rate + '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ display_jobType(billing_type) +'</div><div><strong>Rate:</strong> $'+ (parseInt(flat_rate)/100).formatMoney() +' flat fee</div>';
									break;
									
								case 'salary':
								
									var changed_rate = '';
								
									if(parseInt(setup.cachedPayroll.rate) !== parseInt(rate)) {
										changed_rate = '<div><i>Salary rate</i> was changed from <strong>$' + parseInt(setup.cachedPayroll.rate/100).formatMoney() + '</strong> to <strong>$'+ parseInt(rate/100).formatMoney() +'</strong></div>';
									} else {
										changed_rate = 'No changes were made';
									}
									
									return changed_rate + '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ display_jobType(billing_type) +'</div><div><strong>Rate:</strong> $'+ (parseInt(rate)/100).formatMoney() +' per year</div>';
									break;
									
								case 'flat_and_hourly':
								
									var changed_rate = '';
									
									if(parseInt(setup.cachedPayroll.flat_rate) !== parseInt(flat_rate) && parseInt(setup.cachedPayroll.hourly_rate) !== parseInt(hourly_rate)) {
										
										changed_rate = '<div><i>Flat rate</i> was changed from <strong>$'+ parseInt(setup.cachedPayroll.flat_rate/100).formatMoney() +'</strong> to <strong>$'+ parseInt(flat_rate/100).formatMoney() +'</strong></div>' +
													   '<div><i>Hourly rate</i> was changed from <strong>$'+ parseInt(setup.cachedPayroll.hourly_rate/100).formatMoney() +'</strong> to <strong>$'+ parseInt(hourly_rate/100).formatMoney() +'</strong> </div>';
										
									} else if(parseInt(setup.cachedPayroll.flat_rate) !== parseInt(flat_rate) && parseInt(setup.cachedPayroll.hourly_rate) === parseInt(hourly_rate)) {
										
										changed_rate = '<div><i>Flat rate</i> was changed from <strong>$'+ parseInt(setup.cachedPayroll.flat_rate/100).formatMoney() +'</strong> to <strong>$'+ parseInt(flat_rate/100).formatMoney() +'</strong></div>';
										
									} else if(parseInt(setup.cachedPayroll.flat_rate) === parseInt(flat_rate) && parseInt(setup.cachedPayroll.hourly_rate) !== parseInt(hourly_rate)) {
										
										changed_rate = '<div><i>Hourly rate</i> was changed from <strong>$'+ parseInt(setup.cachedPayroll.hourly_rate/100).formatMoney() +'</strong> to <strong>$'+ parseInt(hourly_rate/100).formatMoney() +'</strong> </div>';
										
									}
									
									return changed_rate + '<div><strong>Job Type:</strong> '+ jobType.name +'</div><div><strong>Billing Type:</strong> '+ display_jobType(billing_type) +'</div><div><strong>Rate:</strong> $'+ (parseInt(flat_rate)/100).formatMoney() +' flat fee ($'+ (parseInt(hourly_rate)/100).formatMoney() +' per hour after '+ max_flat_hours +' hours)</div>';
									break;
									
								default:
									return '';
							}
							
						}
						
					}
					
					
				}
				
				load();

				if(type.value === 'create') {
					
					var payroll = {};
					
					payroll.pay_style = formData.pay_style.value;
					payroll.rate = formData.rate.value;
					payroll.staff = staff.id;
					payroll.service = service.id;
					payroll.hourly_rate = formData.hourly_rate.value;
					payroll.flat_rate = formData.flat_rate.value;
					payroll.max_flat_hours = formData.max_flat_hours.value;
					
				} else if(type.value === 'update') {
					
					payroll.pay_style = formData.pay_style.value;
					payroll.rate = formData.rate.value;
					payroll.hourly_rate = formData.hourly_rate.value;
					payroll.flat_rate = formData.flat_rate.value;
					payroll.max_flat_hours = formData.max_flat_hours.value.toString();
					
				}

				sb.data.db.obj[type.value]('payroll', payroll, function(newPayroll) {
					
					if(newPayroll) {
						
						var noteObj = {
								type_id: newPayroll.staff.id,
								type: 'payroll',
								note: '',
								record_type: 'log',
								author: sb.data.cookie.get('uid'),
								notifyUsers: [],
								log_data: {
									type: type.value,
									objectName: newPayroll.service.name,
									details: ''
								}
							};
							
						if(type.value === 'create') {
							
							noteObj.log_data.details = compare_payrollData({
								cachedPayroll: payroll_settings_cache,
								formData: formData,
								method: 'create',
								service: service	
							});
							
							noteObj.note = '<div>New payroll information was created for <strong>' + newPayroll.staff.fname + ' ' + newPayroll.staff.lname + '</strong></div></br>' + noteObj.log_data.details;
							
						} else if(type.value === 'update') {
							
							noteObj.log_data.details = compare_payrollData({
								cachedPayroll: payroll_settings_cache,
								formData: formData,
								method: 'update',
								staff: newPayroll.staff,
								service: service	
							});

							noteObj.note = '<div>Payroll information was updated for <strong>' + newPayroll.staff.fname + ' ' + newPayroll.staff.lname + '</strong></div></br>' + noteObj.log_data.details;
							
						}
						
						sb.data.db.obj.create('notes', noteObj, function(newNote){
							
							after(newPayroll);	
							
						});
						
					}
					
				}, 1);
				
			}
			
			dom.empty();
			
			dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
			
			dom.wrapper.makeNode('head', 'div', {});
			dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
			dom.wrapper.makeNode('body', 'div', {});
			
			dom.wrapper.head.makeNode('cols', 'div', {css: 'ui equal width grid'});
			
			dom.wrapper.head.cols.makeNode('col1', 'div', {css: 'column'});
			dom.wrapper.head.cols.makeNode('col2', 'div', {css: 'column'});
			
			dom.wrapper.head.cols.col1.makeNode('title', 'div', {tag: 'h3', text: `${type.name} payroll for ${service.name}`});
			
			dom.wrapper.head.cols.col2.makeNode('btnGrp', 'div', {});
			
			dom.wrapper.head.cols.col2.btnGrp.makeNode('save_btn', 'div', {text: 'Save', css: 'ui green button right floated'}).notify('click', {
				type: 'payroll-run',
				data: {
					run: function(data) {
						
						process_payroll(dom.wrapper.body.form, staff, payroll, service, function() {
							
							display_loader({
								mainDom: dom.wrapper,
								text: 'Processing payroll ...'
							});
							
						}, function() {
							
							build_jobTypes(dom, staff);
							
						}, type);
						
					}
				}
			}, sb.moduleId);
			
			dom.wrapper.head.cols.col2.btnGrp.makeNode('back_btn', 'div', {text: 'Back', css: 'ui red button right floated'}).notify('click', {
				type: 'payroll-run',
				data: {
					run: function(data) { build_jobTypes(dom, staff); }
				}
			}, sb.moduleId);
			
			display_loader({
				mainDom: dom.wrapper.body,
				text: 'Fetching payroll blueprints ...'
			});

			sb.data.db.obj.getBlueprint('payroll', function(bp) {
				
				if(bp) {
					
					_.each(bp.pay_style.options, function(value, key) {

						formArgs.pay_style.options.push({
							name: value,
							value: key
						});
						
					}, this);
					
					if(type.value === 'update') {
						
						formArgs.pay_style.value = payroll.pay_style;
						
						formArgs.rate.value = payroll.rate;
						formArgs.hourly_rate.value = payroll.hourly_rate;
						formArgs.flat_rate.value = payroll.flat_rate;
						formArgs.max_flat_hours.value = payroll.max_flat_hours;
						
						if(payroll.pay_style === 'flat_and_hourly') {
							
							formArgs.rate.type = 'hidden';
							formArgs.flat_rate.type = 'usd';
							formArgs.hourly_rate.type = 'usd';
							formArgs.max_flat_hours.type = 'number';
							
						}
						
					}
					
					dom.wrapper.body.empty();
				
					dom.wrapper.body.makeNode('form', 'form', formArgs);
					
					dom.wrapper.body.patch();	
					
				}
				
			});
			
			dom.patch();
			
		}
		
		function get_serviceObjById(id, callback) {
			
			sb.data.db.obj.getWhere('inventory_service', {id: id, childObjs: 1}, function(obj) {

				callback(obj[0]);
				
			});
			
		}
		
		function build_payrollTable(payrollArr, serviceObj) {
			
			var payrollObj = _.find(payrollArr, function(obj) {
					return obj.service.id === serviceObj.id;
				});
				
			if(payrollObj) {
				
				if(payrollObj.pay_style !== 'flat_and_hourly') {

					dom.wrapper.table.makeRow('row-' + serviceObj.id, [payrollObj.service.name, payrollObj.pay_style_name, '$' + (payrollObj.rate/100).formatMoney(), '']);
					
				} else {
					
					dom.wrapper.table.makeRow('row-' + serviceObj.id, [payrollObj.service.name, payrollObj.pay_style_name, '$' + (payrollObj.flat_rate/100).formatMoney() + ' flat fee ($'+ (payrollObj.hourly_rate/100).formatMoney() +' per hour after '+ payrollObj.max_flat_hours +' hours)', '']);
					
				}
				
				dom.wrapper.table.body['row-' + serviceObj.id].btns.makeNode('update', 'div', {text: 'Update', css: 'ui tiny orange button'}).notify('click', {
					type: 'payroll-run',
					data: {
						run: function(data) {
							
							build_payrollObj(dom, serviceObj, payrollObj, staff, {
								name: 'Update',
								value: 'update'
							});
							
						}
					}
				}, sb.moduleId);

				
			} else {
				
				dom.wrapper.table.makeRow('row-' + serviceObj.id, [serviceObj.name, '<em class="text-muted">Not Set</em>', '<em class="text-muted">Not Set</em>', '']);
				
				dom.wrapper.table.body['row-' + serviceObj.id].btns.makeNode('create', 'div', {text: 'Create', css: 'ui tiny green button'}).notify('click', {
					type: 'payroll-run',
					data: {
						run: function(data) {
							
							build_payrollObj(dom, serviceObj, payrollObj, staff, {
								name: 'Create',
								value: 'create'
							});
							
						}
					}
				}, sb.moduleId);
				
			}
			
			
		}
		
		dom.empty();
		
		dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
		
		display_loader({
			mainDom: dom.wrapper,
			text: 'Fetching job types ...'
		});
		
		dom.patch();

		sb.data.db.obj.getWhere('payroll', {staff: staff.id, childObjs: 1}, function(payrollArr) {

			if(payrollArr) {
				
				dom.wrapper.empty();
				
				dom.wrapper.makeNode('table', 'table', {
					columns: {
						name: 'Job Type',
						pay_style: 'Pay Style',
						rate: 'Rate',
						btns: ''
					}
				});
				
				if(!_.isEmpty(staff.service)) {
					
					staff.service = _.sortBy(staff.service, function(obj) {
						return obj.name;
					});	
	
					_.each(staff.service, function(serviceObj) {
	
						if(typeof serviceObj === 'number') {
							
							get_serviceObjById(parseInt(serviceObj), function(obj) {
								
								serviceObj = obj;
								
								build_payrollTable(payrollArr, serviceObj);
								
								dom.wrapper.patch();
								
							});
							
						} else {
							
							build_payrollTable(payrollArr, serviceObj);
							
						}
						
					}, this);
					
				} else {
					
					dom.wrapper.makeNode('none', 'div', {
						text: 'There are no payroll settings found for this staff memebr',
						css: 'text-center ui header'
					});	
					
				}
				
				dom.wrapper.patch();
				
			}
			
		});
		
	}
	
	function singleStaffPayroll_ui(dom, staff) {

		var UI_CACHE = {
				mainDom: dom,
				payrollDetails: {}
			};
		
		function build_payrollDetails(dom, staff) {

			function check_address(dom, staff) {

				var addressText = '';

				if(staff.address) {
					
					if(_.isObject(staff.address)) {
						
						if(staff.address.street.length !== 0 && staff.address.city.length !== 0 && staff.address.zip.length !== 0) {
							
							addressText = `<h3>${staff.address.street} ${staff.address.street2} </br> ${staff.address.city}, ${staff.address.state} ${staff.address.zip} </br> ${staff.address.country}</h3>`;	
							
						} else {
							
							addressText = '<h3>N/A</h3>';
							
						}
						
					} else if(_.isNumber(staff.address)) {
						
						sb.data.db.obj.getWhere('contact_info', {childObjs: 1, object_id: staff.id}, function(addressArr) {
							
							if(!_.isEmpty(addressArr)) {
								
								var addressObj = addressArr[0];
								
								if(addressObj.street.length !== 0 && addressObj.city.length !== 0 && addressObj.zip.length !== 0) {
									
									dom.makeNode('address', 'div', {text: `<h3>${addressObj.street} ${addressObj.street2} </br> ${addressObj.city}, ${addressObj.state} ${addressObj.zip} </br> ${addressObj.country}</h3>`});
							
									dom.patch();
									
								} else {
									
									addressText = '<h3>N/A</h3>';
									
								}
								
							} else {
								
								addressText = '<h3>N/A</h3>';
								
							}
							
						});
						
					} else {
						
						addressText = '<h3>N/A</h3>';
						
					}	
					
				} else {
						
					addressText = '<h3>N/A</h3>';
					
				}
				
				dom.makeNode('address', 'div', {text: addressText});
				
				dom.patch();
				
			}
			
			function check_dob(staff) {
				
				if(staff.dob !== '') {
					return moment(staff.dob).format('MM/DD/YYYY');
				} else {
					return 'N/A';
				}
				
			}
			
			function check_pin(staff) {
				
				if(staff.pin !== '') {
					return staff.pin;
				} else {
					return 'N/A';
				}
				
			}
			
			function check_ssn(staff) {
				
				var ssn = [staff.ssn.slice(0, 3), staff.ssn.slice(3, 5), staff.ssn.slice(5, 9)].join(' - ');
				
				if(staff.ssn !== '') {
					return ssn;
				} else {
					return 'N/A';
				}
				
			}
			
			function check_startOfWeek(staff) {
				
				if(staff.work_week_start_name) {
					return staff.work_week_start_name;
				} else {
					return 'N/A';
				}
				
			}
			
			function setup_payrollDetails(dom, staff) {

				function build_mainSetup(dom, staff) {

					display_loader({
						mainDom: dom,
						text: 'Fetching data ...'
					});

					sb.data.db.obj.getBlueprint('staff', function(bp) {

						if(bp) {
							
							var formArgs1 = {
									dob: {
									name: 'dob',
									type: 'date',
									label: 'Date Of Birth',
									value: moment(staff.dob),
									dateType:'date'
								},
								ssn: {
									name: 'ssn',
									type: 'text',
									label: 'SSN # <small> <em>(Do not include dashes)</em> </small>',
									value: staff.ssn
								},
								filing_status: {
									name: 'filing_status',
									type: 'select',
									label: 'W4 Filing Status',
									options: [],
									value: staff.filing_status
								},
								dependents: {
									name: 'dependents',
									type: 'number',
									label: 'Dependents',
									value: staff.dependents
								}
							};
							var formArgs2 = {
									street: {
										name: 'street',
										type: 'text',
										label: 'Street Address',
										value: ''
									},
									unit: {
										name: 'unit',
										type: 'text',
										label: 'Unit/Apt #',
										placeholder: '(If applicable)',
										value: ''
									},
									city: {
										name: 'city',
										type: 'text',
										label: 'City',
										value: ''
									},
									state: {
										name: 'state',
										type: 'state',
										label: 'State/Province',
										options: [],
										value: ''
									},
									zip: {
										name: 'zip',
										type: 'text',
										label: 'Zip',
										value: ''
									},
									country: {
										name: 'country',
										type: 'text',
										label: 'Country',
										options: [],
										value: ''
									}
								};
							var formArgs3 = {
									pin: {
										name: 'pin',
										type: 'hidden',
										label: 'Time Clock PIN'
									},
									work_week_start: {
										name: 'work_week_start',
										type: 'select',
										label: 'Work Week Start',
										options: [],
										value: staff.work_week_start
									},
									overlap: {
										name: 'overlap'
										, type: 'select'
										, label: 'Can be assigned to overlapping shifts?'
										, options: [
											{
												name: 'No'
												, value: 0
											}
											, {
												name: 'Yes'
												, value: 1
											}
										]
										, value: staff.overlap
									}
								};
							
							function generateStaffPIN(callback) {
				
								var randomNum = generateRandomPIN(),
									resList = {};
									
								function generateRandomPIN() {
										
										var random = Math.floor(Math.random() * 1000000),
											numLength = 0,
											numLengthDiff = 0;
											
										random = random.toString();
										numLength = random.length;
										
										if(numLength < 6) {
											
											numLengthDiff = 6 - numLength;
								
											for(var i = 1; i <= numLengthDiff; i++) {
												
												random = '0' + random;
												
											}
											
										}
										
										return random;
										
									}
									
								sb.data.db.obj.getAll('staff', function(staffObjs) {
				
									resList = _.find(staffObjs, function(staff) {
										return staff.pin === randomNum;
									});
									
									if(resList === undefined) {
										
										callback(randomNum);
										
									} else {
										
										generateStaffPIN(callback);
										
									}
									
								});	
								
							}
							
							function process_payrollDetails(formsObj, staff, load, after) {

								var formData1 = formsObj.form1.process().fields;
								var formData2 = formsObj.form2.process().fields;
								var formData3 = formsObj.form3.process().fields;

								// CHECKS
								if( (formData3.pin.value.length < 6 || formData3.pin.value.length > 6) && formData3.pin.value.length !== 0) {
									
									sb.dom.alerts.alert(
										'Error',
										'You entered an invalid PIN. Your PIN should contain 6 characters only.',
										'error'
									);
									
									return;
									
								}
								
								load();
							
								sb.data.db.obj.getAll('users', function(staffObjs) {

									var foundPin = undefined;

									foundPin = _.find(staffObjs, function(staffObj) {
										
										if(staffObj.id !== staff.id && staffObj.pin && typeof staffObj.pin === 'string' && staffObj.pin.length !== 0 && staffObj.pin === formData3.pin.value) {
											return staffObj;
										}
										
									});

									if(foundPin === undefined) {
										
										staff.dob = formData1.dob.value;
										staff.ssn = formData1.ssn.value;
										staff.filing_status = formData1.filing_status.value;
										staff.dependents = formData1.dependents.value;
										
										staff.pin = formData3.pin.value;
										staff.work_week_start = formData3.work_week_start.value;
										staff.overlap = parseInt(formData3.overlap.value);
										
										sb.data.db.obj.getWhere('contact_info', {object_id: staff.id, childObjs: 1}, function(info) {

											if(!_.isEmpty(info)) {
												
												info[0].street = formData2.street.value;
												info[0].street2 = formData2.unit.value;
												info[0].city = formData2.city.value;
												info[0].state = formData2.state.value;
												info[0].zip = formData2.zip.value;
												info[0].country = formData2.country.value;
												
												sb.data.db.obj.update('contact_info', info[0], function(updatedAddress) {
												
													if(updatedAddress) {
														
														sb.data.db.obj.update('users', staff, function(updatedStaff) {

															if(updatedStaff) {
																
																after(updatedStaff);
																
															}
															
														}, 1);
														
													}
													
												}, 0);
												
											} else {
												
												var addressObj = {
														street: formData2.street.value,
														street2: formData2.unit.value,
														city: formData2.city.value,
														state: formData2.state.value,
														zip: formData2.zip.value,
														country: formData2.country.value,
														object_id: staff.id,
														is_primary: 'yes'
													};
												
												sb.data.db.obj.create('contact_info', addressObj, function(newObj) {
													
													if(newObj) {
														
														staff.address = newObj.id;
														
														sb.data.db.obj.update('users', staff, function(updatedStaff) {
															
															if(updatedStaff) {
																
																after(updatedStaff);
																
															}
															
														}, 1);	
														
													}
													
												}, 1);
												
											} 
											
										});
										
									} else {
										
										sb.dom.alerts.alert(
											'This Time Clock PIN already exits',
											'Please try a different number',
											'warning'
										);
										
										build_mainSetup(dom, staff);
										
										return;
										
									}
									
								}, {
									pin: true
								});	
									
							}
							
							if(staff.address) {
								
								formArgs2.street.value = staff.address.street;
								formArgs2.unit.value = staff.address.street2;
								formArgs2.city.value = staff.address.city;
								formArgs2.state.value = staff.address.state;
								formArgs2.zip.value = staff.address.zip;
								formArgs2.country.value = staff.address.country;
								
							}
							
							_.each(bp.filing_status.options, function(val, key) {
								
								formArgs1.filing_status.options.push({
									name: val,
									value: key
								});
								
							}, this);

							var sorter = {
								  // "sunday": 0, // << if sunday is first day of week
								  "monday": 1,
								  "tuesday": 2,
								  "wednesday": 3,
								  "thursday": 4,
								  "friday": 5,
								  "saturday": 6,
								  "sunday": 7
								}
							
							var daysArray = [bp.work_week_start.options];	
							
							var daysOfWeek = {
									monday:'Monday',
									tuesday:'Tuesday',
									wednesday:'Wednesday',
									thursday:'Thursday',
									friday:'Friday',
									saturday:'Saturday',
									sunday:'Sunday'
								};

							_.each(daysOfWeek, function(val, key) {

								formArgs3.work_week_start.options.push({
									name: val,
									value: key
								});
								
							}, this);
							
							dom.empty();
							
							dom.makeNode('head', 'div', {});
							dom.makeNode('lb_1', 'lineBreak', {spaces: 1});
							dom.makeNode('body', 'div', {});
							dom.makeNode('lb_2', 'lineBreak', {spaces: 1});
							
							dom.head.makeNode('cols', 'div', {css: 'ui equal width grid'});
							dom.body.makeNode('message', 'div', {text: '<p><strong>Note: </strong>street address, city and zip fields must be filled for address to be valid.</p>', css: 'ui message'});
							dom.body.makeNode('cols', 'div', {css: 'ui equal width grid'});
							
							dom.head.cols.makeNode('col1', 'div', {css: 'column'});
							dom.head.cols.makeNode('col2', 'div', {css: 'column'});
							
							dom.head.cols.col1.makeNode('title', 'div', {tag: 'h3', text: 'Edit Payroll Details'});
							
							dom.head.cols.col2.makeNode('btnGrp', 'div', {});
							
							dom.head.cols.col2.btnGrp.makeNode('save_btn', 'div', {text: 'Save', css: 'ui green button right floated'}).notify('click', {
								type: 'payroll-run',
								data: {
									run: function(data) { 
										
										process_payrollDetails({
											form1: dom.body.cols.col1.form,
											form2: dom.body.cols.col2.form,
											form3: dom.body.cols.col3.form
										}, staff, function() {
											
											display_loader({
												mainDom: dom.body,
												text: 'Updating payroll details ...'
											});
											
										}, function(updated) {

											build_payrollDetails(UI_CACHE.payrollDetails, updated);
											
										}); 
										
									}
								}
							}, sb.moduleId);
							
							dom.head.cols.col2.btnGrp.makeNode('back_btn', 'div', {text: 'Back', css: 'ui red button right floated'}).notify('click', {
								type: 'payroll-run',
								data: {
									run: function(data) { build_payrollDetails(UI_CACHE.payrollDetails, staff); }
								}
							}, sb.moduleId);
							
							dom.body.cols.makeNode('col1', 'div', {css: 'column'});
							dom.body.cols.makeNode('col2', 'div', {css: 'column'});
							dom.body.cols.makeNode('col3', 'div', {css: 'column'});
							
							dom.body.cols.col1.makeNode('form', 'form', formArgs1);
							dom.body.cols.col2.makeNode('form', 'form', formArgs2);
							dom.body.cols.col3.makeNode('generate_wrap', 'div', {});
							dom.body.cols.col3.makeNode('form', 'form', formArgs3);

							if( !staff.hasOwnProperty('pin') || staff.pin.length === 0 ) {
								
								dom.body.cols.col3.generate_wrap.makeNode('generate', 'div', {text: 'Time Clock PIN', css: 'text-bold'});
								
								dom.body.cols.col3.generate_wrap.makeNode('pin_btn', 'button', {text: 'Generate Time Clock PIN #', css: 'ui large green fluid button'}).notify('click', {
									type: 'payroll-run',
									data: {
										run: function(data) {
											
											display_loader({
												mainDom: dom.body.cols.col3.generate_wrap,
												text: 'Generating PIN ...'
											});
											
											generateStaffPIN(function(pin) {
												
												delete dom.body.cols.col3.generate_wrap;
												
												dom.body.cols.col3.form.pin.update({
													type: 'text',
													value: pin
												});
												
												dom.body.cols.col3.patch();
												
											});
											
										}
									}
								}, sb.moduleId);
								
								dom.body.cols.col3.generate_wrap.makeNode('lb_1', 'lineBreak', {spaces: 1});
								
							} else {

								dom.body.cols.col3.form.pin.update({
									type: 'text',
									value: staff.pin
								});
								
								dom.body.cols.col3.patch();
								
							} 

							dom.patch();
							
						}
						
					});
					
				}
				
				function build_setupMissingAddress(dom, staff) {
					
					var formArgs_1 = {
							street: {
								name: 'street',
								type: 'text',
								placeholder: 'Street Address',
								validation: true
							},
							unit: {
								name: 'unit',
								type: 'text',
								placeholder: 'Unit/Apt Number (if applicable)'
							},
							city: {
								name: 'city',
								type: 'text',
								placeholder: 'City'
							}
						};
					var formArgs_2 = {
							state: {
								name: 'state',
								type: 'state',
								placeholder: 'State/Province',
								options: []
							},
							zip: {
								name: 'zip',
								type: 'text',
								placeholder: 'Zip'
							},
							country: {
								name: 'country',
								type: 'text',
								placeholder: 'Country'
							}
						};
					
					function process_form(formsObj, staff, load, after) {
						
						var formData1 = formsObj.form1.process().fields;
						var formData2 = formsObj.form2.process().fields;
						var addressObj = {};
						
						function check_forms(data1, data2) {

							if(data1.street.value.length === 0 || data1.city.value.length === 0 || data2.zip.value.length === 0) {
								
								return false;
								
							} else {
								
								return true;
								
							}
							
						}
						
						// CHECKS
						if(!check_forms(formData1, formData2)) {
							
							sb.dom.alerts.alert(
								'Incomplete form',
								'Please fill out all required fields',
								'error'
							);
							
							return;
							
						}
						
						load();
						
						addressObj.street = formData1.street.value;
						addressObj.street2 = formData1.unit.value;
						addressObj.city = formData1.city.value;
						
						addressObj.state = formData2.state.value;
						addressObj.zip = formData2.zip.value;
						addressObj.country = formData2.country.value;

						addressObj.object_id = staff.id;
						addressObj.is_primary = 'yes';
						
						sb.data.db.obj.create('contact_info', addressObj, function(newObj) {
							
							staff.address = newObj.id;
							
							sb.data.db.obj.update('users', staff, function(updatedStaff) {
								
								if(updatedStaff) {
									
									after(updatedStaff);
									
								}
								
							}, 1);
							
						}, {
							id: true
						});

					}
										
					dom.empty();
					
					dom.makeNode('message', 'div', {css: 'ui attached negative message'});
					dom.makeNode('form_wrap', 'div', {css: 'ui attached fluid segment'});
					
					dom.message.makeNode('header', 'div', {text: 'This staff member is missing an address. Add an address or click "Continue Anyway" to proceed.', css: 'header'});
					
					dom.form_wrap.makeNode('cols', 'div', {css: 'ui equal width grid'});
					dom.form_wrap.makeNode('lb_1', 'lineBreak', {spaces: 1});
					dom.form_wrap.makeNode('btnGrp', 'div', {});
					
					dom.form_wrap.cols.makeNode('col1', 'div', {css: 'column'});
					dom.form_wrap.cols.makeNode('col2', 'div', {css: 'column'});
					
					dom.form_wrap.cols.col1.makeNode('form', 'form', formArgs_1);
					
					dom.form_wrap.cols.col2.makeNode('form', 'form', formArgs_2);
					
					dom.form_wrap.btnGrp.makeNode('continue_btn', 'div', {text: 'Continue Anyway', css: 'ui teal button right floated'}).notify('click', {
						type: 'payroll-run',
						data: {
							run: function(data) { build_mainSetup(dom, staff); }
						}
					}, sb.moduleId);
					
					dom.form_wrap.btnGrp.makeNode('save_btn', 'div', {text: 'Save', css: 'ui green button right floated'}).notify('click', {
						type: 'payroll-run',
						data: {
							run: function(data) { process_form({
								form1: dom.form_wrap.cols.col1.form,
								form2: dom.form_wrap.cols.col2.form
							}, staff, function() {
								
								display_loader({
									mainDom: dom,
									text: 'Updating address for ' + staff.fname + ' ...' 
								});
								
							}, function(updated) {
								
								build_mainSetup(dom, updated);
								
							}); }
						}
					}, sb.moduleId);
					
					dom.form_wrap.btnGrp.makeNode('back_btn', 'div', {text: 'Back', css: 'ui red button right floated'}).notify('click', {
						type: 'payroll-run',
						data: {
							run: function(data) { build_payrollDetails(UI_CACHE.mainDom.wrapper.payroll_details, staff); }
						}
					}, sb.moduleId);
					
					dom.form_wrap.btnGrp.makeNode('clr', 'div', {css: 'clear'});
					
					dom.patch();
					
				}
				
				dom.empty();
				
				dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
				
				if(!staff.address) {
					
					build_setupMissingAddress(dom.wrapper, staff);
					
				} else {

					build_mainSetup(dom.wrapper, staff);	
					
				}
				
				dom.patch();
				
			} 

			var filing_status = 'N/A';

			if(staff.filing_status_name) {
				filing_status = staff.filing_status_name;
			} else {
				filing_status = 'N/A';
			}
			
			if(!staff.hasOwnProperty('ssn')){
				staff.ssn = '';
			}
			
			dom.empty();
			
			dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
			
			dom.wrapper.makeNode('head', 'div', {});
			dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
			dom.wrapper.makeNode('body', 'div', {css: 'clear'});
			dom.wrapper.makeNode('lb_2', 'lineBreak', {spaces: 1});
			
			dom.wrapper.head.makeNode('btnGrp', 'div', {});
			
			dom.wrapper.head.btnGrp.makeNode('setup_btn', 'div', {text: 'Setup Payroll Details', css: 'ui green button'}).notify('click', {
				type: 'staffComponent-run',
				data: {
					run: function() { setup_payrollDetails(dom.wrapper, staff); }
				}
				
			}, sb.moduleId);
			
			dom.wrapper.body.makeNode('cols', 'div', {css: 'ui equal width grid'});
			
			dom.wrapper.body.cols.makeNode('col1', 'div', {css: 'column'});
			dom.wrapper.body.cols.makeNode('col2', 'div', {css: 'column'});
			dom.wrapper.body.cols.makeNode('col3', 'div', {css: 'column'});
			
			// COL1 - DATE OF BIRTH, SSN, FILLING STATUS, DEPENDENTS
			dom.wrapper.body.cols.col1.makeNode('dob', 'div', {text: `<h3><small class="text-muted">Date of Birth</small> ${check_dob(staff)}</h3>`});
			dom.wrapper.body.cols.col1.makeNode('lb_1', 'lineBreak', {spaces: 1});
			dom.wrapper.body.cols.col1.makeNode('ssn', 'div', {text: `<h3><small class="text-muted">SSN # </small> ${check_ssn(staff)}</h3>`});
			dom.wrapper.body.cols.col1.makeNode('lb_2', 'lineBreak', {spaces: 1});
			dom.wrapper.body.cols.col1.makeNode('filing_status', 'div', {text: `<h3><small class="text-muted">Filling Status</small> ${filing_status}</h3>`});
			dom.wrapper.body.cols.col1.makeNode('lb_3', 'lineBreak', {spaces: 1});
			dom.wrapper.body.cols.col1.makeNode('dependents', 'div', {text: `<h3><small class="text-muted">Dependents</small> ${staff.dependents || 'N/A'}</h3>`});

			// COL2 - ADDRESS
			dom.wrapper.body.cols.col2.makeNode('address_label', 'div', {text: '<small class="text-muted">Address</small>', tag: 'h3', css: 'ui header'});
			
			check_address(dom.wrapper.body.cols.col2, staff);
			
			dom.makeNode('modal', 'modal', {size:'small'});
			dom.wrapper.body.cols.col3.makeNode('timeOff', 'div', {})
				.makeNode('loader', 'loader', {});
				
			getCurrentDaysOffLeft(staff, function(daysOffLeft) {
				
				dom.wrapper.body.cols.col3.timeOff.empty();
				
				var vacationDayDispText = 'N/A';
				if( staff.vacation_days && staff.vacation_days.id ){
					vacationDayDispText = daysOffLeft.vacation_days +' / '+ staff.vacation_days.max_qty;
				}
				
				// COL3 - PIN, VACATION, WORK WEEK START
				dom.wrapper.body.cols.col3.timeOff.makeNode('pin', 'div', {text: `<h3><small class="text-muted">Time Clock PIN #</small> ${check_pin(staff) || 'N/A'}</h3>`});
				dom.wrapper.body.cols.col3.timeOff.makeNode('lb_1', 'lineBreak', {spaces: 1});
				dom.wrapper.body.cols.col3.timeOff.makeNode('vacation', 'div', {text: `<h3><small class="text-muted">Vacation Days</small> ${ vacationDayDispText }</h3>`})
					.makeNode('edit', 'div', {
						css:'ui basic mini orange icon right floated button', 
						text:'<i class="pencil icon"></i>'
					}).notify('click', {
						type:'payroll-run',
						data:{
							run:function(){
								
								dom.modal.show();
								editDaysOffScheduleModal(dom.modal, staff, function(response){
									
									staff.vacation_days = response;								
									build_payrollDetails(dom, staff);
									
								}, 'vacation_days');
								
							}
						}
					}, sb.moduleId);
					
				var sickDayDisText = 'N/A';
				if( staff.sick_days && staff.sick_days.id ){
					sickDayDisText = daysOffLeft.sick_days +' / '+ staff.sick_days.max_qty;
				}
					
				dom.wrapper.body.cols.col3.timeOff.makeNode('lb_2', 'lineBreak', {spaces: 1});
				dom.wrapper.body.cols.col3.timeOff.makeNode('sickDays', 'div', {text: `<h3><small class="text-muted">Sick Days</small> ${ sickDayDisText }</h3>`})
					.makeNode('edit', 'div', {
						css:'ui basic mini orange icon right floated button', 
						text:'<i class="pencil icon"></i>'
					}).notify('click', {
						type:'payroll-run',
						data:{
							run:function(){
								
								dom.modal.show();
								editDaysOffScheduleModal(dom.modal, staff, function(response){
									
									staff.sick_days = response;
									build_payrollDetails(dom, staff)
									
								}, 'sick_days');
								
							}
						}
					}, sb.moduleId);
					
				dom.wrapper.body.cols.col3.patch();
				
			});
			
			dom.wrapper.body.cols.col3.makeNode('lb_3', 'lineBreak', {spaces: 1});
			dom.wrapper.body.cols.col3.makeNode('start', 'div', {text: '<h3><small class="text-muted">Work Week Start</small> '+ check_startOfWeek(staff) + '</h3>'});

			var overlapText = '';
			
			if(staff.overlap === 0
				|| staff.overlap === null
				|| staff.overlap === undefined) {
					
					overlapText = 'No';
					
				} else if(staff.overlap === 1) {
					
					overlapText = 'Yes';
					
				}

			dom.wrapper.body.cols.col3.makeNode('overlap', 'div', {text: '<h3><small class="text-muted">Can be assigned to overlapping shifts? </small> '+ overlapText + '</h3>'});
			
			dom.patch();
			
		}
		
		function editDaysOffScheduleModal(modal, user, onComplete, scheduleType){
			
			function process_form(form){
				
				var formData = form.process().fields;
				var ret = {
					name: user.fname +' '+ user.lname +'\'s vacation day vesting schedule',
					description: formData.description.value,
					initial_qty: parseInt(formData.initial_qty.value),
					max_qty: parseInt(formData.max_qty.value),
					vesting_start_date: formData.vesting_start_date.value,
					vesting_period_type: formData.vesting_period_type.value,
					qty_vested_per_period: parseInt(formData.qty_vested_per_period.value),
					is_template:0
				};
				
				if(copiedFrom){
					ret.copied_from = copiedFrom;
				}
				
				if(formData.does_carry_over && _.contains(formData.does_carry_over.value, 'yes')){
					ret.does_carry_over = 'yes';
				}else{
					ret.does_carry_over = 'no';
				}
				
				// validate
				if(isNaN(ret.initial_qty) || ret.initial_qty < 0){
					
					sb.dom.alerts.alert('Incomplete', 'Initial Quantity must be a non-negative number.', 'warning');
					return false;
					
				}
				if(isNaN(ret.max_qty) || ret.max_qty < 0){
					
					sb.dom.alerts.alert('Incomplete', 'Quantity Cap must be a non-negative number.', 'warning');
					return false;
					
				}
				if(isNaN(ret.qty_vested_per_period) || ret.qty_vested_per_period < 0){
					
					sb.dom.alerts.alert('Incomplete', 'Days vested per period must be a non-negative number.', 'warning');
					return false;
					
				}
				
				return ret;
				
			}
			
			var copiedFrom;
			
			modal.body.empty();
			modal.body.makeNode('loader', 'loader', {});
			modal.body.patch();
			
			modal.footer.empty();
			modal.footer.patch();
			
			sb.data.db.obj.getWhere('time_off_vesting_schedule', {
				is_template:1
			}, function(templates){
				
				sb.data.db.obj.getBlueprint('time_off_vesting_schedule', function(bp){
					
					modal.body.empty();
					
					// form title
					modal.body.makeNode('title', 'div', {text:user.fname +'\'s vacation time schedule', tag:'h3', css:'ui header'});
					
					// template selection
					modal.body.makeNode('templateSelection', 'div', {css:'ui fluid buttons'});
					
					modal.body.templateSelection.makeNode('dropdown', 'div', {
							css:'ui floating dropdown labeled search icon button',
							listener:{
								type:'dropdown',
								values:_.map(templates, function(template){
									return {
										name:template.name,
										value:template.id
									};
								}),
								placeholder:'Select a template'
							}
						})
						.makeNode('icon', 'div', {tag:'i', css:'caret down icon'});
						
						
					modal.body.templateSelection.dropdown.makeNode('text', 'div', {css:'default text', tag:'span', text:'Select a template'});
					
					modal.body.templateSelection.makeNode('apply', 'div', {
						text:'Apply template',
						css:'ui primary button'
					}).notify('click', {
						type:'payroll-run',
						data:{
							run:function(){
								
								var selected = parseInt($(modal.body.templateSelection.dropdown.selector).dropdown('get value'));
								var templateToApply = _.findWhere(templates, {id:selected});
								copiedFrom = selected;

								if(templateToApply){
									
									modal.body.form.initial_qty.update({value:templateToApply.initial_qty});
									modal.body.form.max_qty.update({value:templateToApply.max_qty});
									modal.body.form.vesting_start_date.update({value:templateToApply.vesting_start_date});
									modal.body.form.vesting_period_type.update({value:templateToApply.vesting_period_type});
									modal.body.form.qty_vested_per_period.update({value:templateToApply.qty_vested_per_period});
									modal.body.form.does_carry_over.update({value:[templateToApply.does_carry_over]});
									
								}
								
							}
						}
					}, sb.moduleId);
					
					modal.body.makeNode('br', 'div', {text:'<br />'});
					
					// form
					var formSetup = {
						description:{
							name:'description',
							type:'textbox',
							label:'Notes'
						},
						initial_qty:{
							name:'initial_qty',
							type:'number',
							label:'Initial Quantity'
						},
						max_qty:{
							name:'max_qty',
							type:'number',
							label:'Quantity Cap'
						},
						vesting_start_date:{
							name:'vesting_start_date',
							type:'select',
							label:'Vesting begins after..',
							options:_.map(bp.vesting_start_date.options, function(name, value){
								
								return {
									name:name,
									value:value
								}
								
							})
						},
						vesting_period_type:{
							name:'vesting_period_type',
							type:'select',
							label:'Vesting period',
							options:_.map(bp.vesting_period_type.options, function(name, value){
								
								return {
									name:name,
									value:value
								}
								
							})
						},
						qty_vested_per_period:{
							name:'qty_vested_per_period',
							type:'number',
							label:'Days vested per period'
						},
						does_carry_over:{
							name:'does_carry_over',
							type:'check',
							label:'Do vacation days carry over?',
							options:[{
								name:'does_carry_over',
								value:'yes',
								label:'Yes'
							}]
						}
					};
					
					if(user[scheduleType] && user[scheduleType].id){
						
						_.each(formSetup, function(field, propertyKey){

							if(user[scheduleType][propertyKey]){
								
								if(field.type === 'check'){
									formSetup[propertyKey].value = [user[scheduleType][propertyKey]];
								}else{
									formSetup[propertyKey].value = user[scheduleType][propertyKey];
								}
								
							}
							
						});
						
					}
					
					modal.body.makeNode('form', 'form', formSetup);
					
					// actions
					modal.footer.makeNode('update', 'div', {
						text:'Save',
						tag:'button',
						css:'ui small right floated teal button'
					}).notify('click', {
						type:'payroll-run',
						data:{
							run:function(){
								
								var updatedObj = process_form(modal.body.form);
								
								if(updatedObj){
									
									modal.footer.update.loading();
									
									if(user[scheduleType] && user[scheduleType].id){
										
										updatedObj.id = user[scheduleType].id;
										sb.data.db.obj.update('time_off_vesting_schedule', updatedObj, function(response){
											
											if(response){
												
												modal.hide();
												onComplete(response);
												
											}else{
												
												sb.dom.alerts.alert('Something went wrong!', 'Refresh the page and try again.', 'error');

												modal.footer.update.loading(false);
												
											}
											
										});
										
									}else{
										
										// link user to schedule
										sb.data.db.obj.create('time_off_vesting_schedule', updatedObj, function(response){
											
											if(response){
												
												var updatedObj = {
													id:user.id
												};
												updatedObj[scheduleType] = response.id;
												
												sb.data.db.obj.update('users', updatedObj, function(response){
													
													if(response){
														
														modal.hide();
														onComplete(response);
														
													}else{
														
														sb.dom.alerts.alert('Something went wrong!', 'Refresh the page and try again.', 'error');
														
													}
													
												});
												
											}else{
												
												sb.dom.alerts.alert('Something went wrong!', 'Refresh the page and try again.', 'error');
												
											}
											
										});
										
									}
									
								}
								
							}
						}
					}, sb.moduleId);
					
					// patch
					modal.body.patch();
					modal.footer.patch();
					
				}, true);
				
			});
			
		}
		
		dom.empty();
		dom.makeNode('loader', 'loader', {});
		dom.patch();
		
		dom.empty();
	
		dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
		
		UI_CACHE.payrollDetails = dom.wrapper.makeNode('payroll_details', 'container', {title: 'Payroll Details', collapse: 'open'});
		dom.wrapper.makeNode('job_types', 'container', {title: 'Job Types', collapse: 'open'});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});

		build_payrollDetails(dom.wrapper.payroll_details, staff);
		build_jobTypes(dom.wrapper.job_types, staff);
		
		dom.patch();
		
	}
	
	function payrollCycleSettings_ui(dom, state) {
		
		var form_args = {
				cycle_type: {
					name: 'cycle_type',
					type: 'select',
					label: 'Cycle Type',
					options: [
						{
							name: 'Not selected',
							value: 0
						},
						{
							name: 'Weekly',
							value: 'weekly'
						},
						{
							name: 'Bi weekly',
							value: 'bi_weekly'
						},
						{
							name: 'Monthly',
							value: 'monthly'
						},
						{
							name: 'Semi-Monthly (1st and 15th)',
							value: '1st_and_15th'
						},
						{
							name: 'Semi-Monthly (15th and last day of month)',
							value: '15th_and_last_day'
						}
					],
					change: function(formArgs, value) {
						
						checkCycleStart_options(value, dom.wrapper.body.form);
						
						display_cycleStartDate(value, dom.wrapper.body.form);
						
					}
				},
				cycle_start: {
					name: 'cycle_start',
					type: 'hidden',
					label: 'Cycle Start',
					options: [],
					dateFormat:'M/D/YYYY'
				},
				cycle_start_date: {
					name: 'cycle_start_date',
					type: 'hidden',
					label: 'Cycle Start Date (Make sure cycle start date matches cycle start)',
					placeholder: 'Not Set'
				}
			};
			
		function checkCycleStart_options(value, form) {

			var arr = [];
		
			if(value === 'weekly' || value === 'bi_weekly') {
				
				arr.push(
					{
						name: 'Monday',
						value: 'monday'
					},
					{
						name: 'Tuesday',
						value: 'tuesday'
					},
					{
						name: 'Wednesday',
						value: 'wednesday'
					},
					{
						name: 'Thursday',
						value: 'thursday'
					},
					{
						name: 'Friday',
						value: 'friday'
					},
					{
						name: 'Saturday',
						value: 'saturday'
					},
					{
						name: 'Sunday',
						value: 'sunday'
					}
				);
						
				form.cycle_start.update({
					type: 'select',
					options: arr
				});
				
			} 
			else if(value === 'monthly') {
			
				arr.push(
					{
						name: 'January',
						value: 'january'
					},
					{
						name: 'February',
						value: 'february'
					},
					{
						name: 'March',
						value: 'march'
					},
					{
						name: 'April',
						value: 'april'
					},
					{
						name: 'May',
						value: 'may'
					},
					{
						name: 'June',
						value: 'june'
					},
					{
						name: 'July',
						value: 'july'
					},
					{
						name: 'August',
						value: 'august'
					},
					{
						name: 'September',
						value: 'september'
					},
					{
						name: 'November',
						value: 'november'
					},
					{
						name: 'December',
						value: 'december'
					}
				);
				
				form.cycle_start.update({
					type: 'select',
					options: arr
				});
				
			} else if(value === '1st_and_15th') {
			
				arr.push(
					{
						name: '1st',
						value: '1st'
					},
					{
						name: '15th',
						value: '15th'
					}
				);
				
				form.cycle_start.update({
					type: 'select',
					options: arr
				});
				
			} else if(value === '15th_and_last_day') {
			
				arr.push(
					{
						name: '15th',
						value: '15th'
					},
					{
						name: 'Last day of month',
						value: 'last_day_of_month'
					}
				);
				
				form.cycle_start.update({
					type: 'select',
					options: arr
				});
			
			} else {
				
				form.cycle_start.update({
					type: 'hidden'
				});
				
			}
			
			
		}
		
		function display_cycleStartDate(value, form) {

			if(parseInt(value) !== 0) {
				
				form.cycle_start_date.update({
					type: 'date',
					dateType: 'date',
					dateFormat: 'dddd MMMM Do',
					value: moment()
				});
				
			} else {
				
				form.cycle_start_date.update({
					type: 'hidden'
				});
		
			}
						
		}
		
		function process_formData(form) {
		
			var formData = form.process();

			if(formData.fields.cycle_start_date.value !== '0') {
				
				sb.dom.alerts.ask({
					title: 'Are you sure?',
					text: 'Update payroll cycle?'
				}, function(res) {
					
					if(res) {
		
						if(formData.fields.cycle_type.value !== '') {
							
							state.appSettings.apps.payroll.payroll_cycle = {
								type: formData.fields.cycle_type.value,
								start: formData.fields.cycle_start.value,
								start_date: moment(formData.fields.cycle_start_date.value, 'dddd MMMM Do')
							};
		
						} else {
							
							delete state.appSettings.apps.payroll.payroll_cycle;
							
						}
						
						state.appSettings.save(function(done) {
							
							if(done) {
								
								sb.dom.alerts.alert('Updated Payroll Cycle', '', 'success');
								
							}
							
						});	
		
					}
					
				});	
				
			} else {
				
				sb.dom.alerts.alert(
					'Error',
					'Date field can not be empty. Please select a cycle start date.',
					'error'
				);
				
				return;
				
			}
			
		}
			
		if(state.appSettings.apps.payroll.hasOwnProperty('payroll_cycle')) {
	
			form_args.cycle_type.value = state.appSettings.apps.payroll.payroll_cycle.type;
			
			form_args.cycle_start.type = 'select';
			form_args.cycle_start.value = state.appSettings.apps.payroll.payroll_cycle.start;
			
			form_args.cycle_start_date.type = 'date';
			form_args.cycle_start_date.dateType = 'date';
			form_args.cycle_start_date.dateFormat = 'dddd MMMM Do';
			form_args.cycle_start_date.value = moment(state.appSettings.apps.payroll.payroll_cycle.start_date);
			
		}
		
		dom.empty();
		
		dom.makeNode('wrapper', 'div', {css: 'ui raised blue segment'});
		
		dom.wrapper.makeNode('head', 'div', {css: 'ui grid'});
		dom.wrapper.makeNode('lb_1', 'div', {spaces: 1});
		dom.wrapper.makeNode('body', 'div', {});
		
		dom.wrapper.head.makeNode('btnGrp', 'div', {css: 'sixteen wide column'});
		
		dom.wrapper.head.btnGrp.makeNode('save_btn', 'div', {text: 'Save', css: 'ui green button right floated'}).notify('click', {
			type: 'payroll-run',
			data: {
				run: function(data) {
					
					process_formData(dom.wrapper.body.form);
					
				}
			}
		}, sb.moduleId);
		
		dom.wrapper.body.makeNode('form', 'form', form_args);
		
		form_args.cycle_start.options = checkCycleStart_options(form_args.cycle_type.value, dom.wrapper.body.form);
		
		dom.patch();
		
	}
	
	function processSchedulingRequest_ui(modal, request) {
		
		
		
	}
	
	function fiscalYearSettings_ui(dom, state) {

		var fiscalYear_value = '',
			fiscalYear_placeholder = '';
		
		if(state.appSettings.apps.payroll.hasOwnProperty('fiscal_year_start')) {
				
			fiscalYear_value = state.appSettings.apps.payroll.fiscal_year_start;
			fiscalYear_placeholder = '';	
			
		} else {
			
			fiscalYear_value = '';
			fiscalYear_placeholder = 'Not set';
			
		}

		dom.empty();
		dom.makeNode('cont', 'div', {css: 'ui raised blue segment'});
		dom.cont.makeNode('wrapper', 'div', {css: 'ui grid'});
		
		dom.cont.wrapper.makeNode('head', 'div', {css:'centered row'});
		dom.cont.wrapper.head.makeNode('col', 'div', {css: 'fifteen wide column'});
		dom.cont.wrapper.makeNode('body', 'div', {css: 'centered row'});
		dom.cont.wrapper.body.makeNode('cont', 'div', {css: 'fifteen wide column'});
		dom.cont.wrapper.makeNode('sp', 'lineBreak', {spaces: 1});
		dom.cont.wrapper.head.col.makeNode('save_btn', 'div', {text: '<i class="fa fa-floppy-o"></i> Save', css: 'ui green button right floated'}).notify('click', {
			type: 'payroll-run',
			data: {
				run: function(data) {
					
					var formData = dom.cont.wrapper.body.cont.form.process();

					if(formData.fields.fiscal_year_start.value !== "") {
						
						state.appSettings.apps.payroll.fiscal_year_start = moment(formData.fields.fiscal_year_start.value, 'dddd MMMM Do').toDate(); 
						
					} else {
						
						delete state.appSettings.apps.payroll.fiscal_year_start;
						
					}

					sb.dom.alerts.ask({
						title: 'Are you sure?',
						text: 'Update fiscal year start?'
					}, function(res) {
						
						if(res) {
							
							state.appSettings.save(function(done) { 
						
								if(done) {
									
									sb.dom.alerts.alert('Updated Fiscal Year Start', '', 'success');
									
								}
								
							});
							
						}
						
					});
					
				}
			}
		}, sb.moduleId);

		dom.cont.wrapper.body.cont.makeNode('form', 'form', {
			fiscal_year: {
				name: 'fiscal_year_start',
				type: 'date',
				dateType:'date',
				label: 'Fiscal Year Start',
				dateFormat: 'dddd MMMM Do',
				placeholder: fiscalYear_placeholder,
				value: fiscalYear_value,
				placeholder: 'Not Set'
			}
		});
		
		dom.patch();
		
	}
	
	function display_loader(setup) {
				
		// SETUP { 
			// mainDom: dom to display loader,
			// domList: array of dom nodes to empty if needed (optional),
			// empty: boolean, if it exists all dom nodes in domList will be emptied and patched (optional),
			// text: text that will be displayed with loader
		//}
		
		if(setup.hasOwnProperty('domList') && setup.hasOwnProperty('empty')) {
			
			_.each(setup.domList, function(dom) {
				dom.empty();
				dom.patch();
			}, this);
			
		}
		
		setup.mainDom.empty();
		
		setup.mainDom.makeNode('cont', 'div', {});
		
		setup.mainDom.cont.makeNode('loader', 'loader', {});
		setup.mainDom.cont.makeNode('load_text', 'div', {text: setup.text, css: 'text-center'});
		
		setup.mainDom.patch();
		
	}
	
	function build_payrollReport(dom, state, draw, report) {

		var DATA_CACHE = {
				grouped_entryList: [],
				entries: [],
				total_payroll: 0,
				locationId: 0,
				showSalaryEntries: false
			};
	    var UI_CACHE = {
	        	mainDom: {},
	        	modal: {},
	        	filter: {}
	    	};
	    
	    function display_payrollCycleTyle(type, callback) {
		    
		    switch(type) {
				    
			    case 'weekly':
			    case 'Weekly':
			    
			    	callback('Weekly');
			    	
			    	break;
			    	
			    case 'bi_weekly':
			    case 'Bi Weekly':
			    	
			    	callback('Bi Weekly');
			    	
			    	break;
			    	
			    case 'monthly':
			    case 'Monthly':
			    	
			    	callback('Monthly');
			    	
			    	break;
			    	
			    case '1st_and_15th':
			    case '1st and 15th':
			    	
			    	callback('1st and 15th');
			    	
			    	break;
			    	
			    case '15th_and_last_day':
			    case '15th and last day':
			    	
			    	callback('15th and last day');
			    	
			    	break;
			    	
			    default:
			    	callback('Not Set');
			    
		    }
		    
	    }
	    	
	    function build_payrollSettingsInfo(dom, setup) {
		    
		    // setup.state

		    // Check if there are payroll cycle settings available
		    if(setup.state.appSettings.apps.payroll.payroll_cycle) {
			    
			    // If true, display settings
			    
			    var settings = {};
			    
			    // Checks

			    // Check if payroll settings have a fiscal year start
			    if(setup.state.appSettings.apps.payroll.fiscal_year_start) {
				    
				    // If true, add to settings obj
				    settings.fiscal_year_start = moment(setup.state.appSettings.apps.payroll.fiscal_year_start).format('dddd MMMM Do, YYYY');
				    
			    } else {
				    
				    // If false, display text
				    settings.fiscal_year_start = 'Not Set';
				    
			    }
			    
			    display_payrollCycleTyle(setup.state.appSettings.apps.payroll.payroll_cycle.type, function(type) {
				    
				    settings.cycle_type = type;
				    
			    });
			    
			    settings.cycle_start = moment(setup.state.appSettings.apps.payroll.payroll_cycle.start_date).format('dddd MMMM Do');
			    
			    dom.makeNode('grid', 'div', {css: 'ui equal width grid'});
			    
			    // Grid level
			    dom.grid.makeNode('col1', 'div', {
					css: 'column text-center' 
			    });
			    dom.grid.makeNode('col2', 'div', {
					css: 'column text-center' 
			    });
			    dom.grid.makeNode('col3', 'div', {
					css: 'column text-center' 
			    });
			    
			    // Cols level
			    dom.grid.col1.makeNode('title', 'div', {
					tag: 'h3',
					text: 'Fiscal Year Start' 
			    });
			    dom.grid.col1.makeNode('val', 'div', {
				    css: 'text-muted',
				    text: '<h4>'+ settings.fiscal_year_start +'</h4>'
			    });
			    
			    dom.grid.col2.makeNode('title', 'div', {
				    tag: 'h3',
				    text: 'Cycle Type'
			    });
			    dom.grid.col2.makeNode('val', 'div', {
				    css: 'text-muted',
				    text: '<h4>'+ settings.cycle_type +'</h4>'
			    });
			    
			    dom.grid.col3.makeNode('titls', 'div', {
				    tag: 'h3',
				    text: 'Cycle Start'
			    });
			    dom.grid.col3.makeNode('val', 'div', {
				    css: 'text-muted',
				    text: '<h4>'+ settings.cycle_start +'</h4>'
			    });
			    
		    } else {
			    
			    // If false, allow user to go to payroll settings
			    
			    dom.makeNode('msg', 'div', {
				    css: 'ui message' 
			    });
			    
			    dom.msg.makeNode('header', 'div', {
				   	text: '<div class="header"><i class="exclamation icon"></i>Missing settings</div> <p>There are no payroll cycle settings set. Please set payroll cycle settings then return here to view payroll reports.</p>' 
			    });
			    
			    dom.msg.makeNode('lb_1', 'lineBreak', {
				   	spaces: 1 
			    });
			    
			    dom.msg.makeNode('btnGrp', 'div', {
				    css: 'text-center'
			    });
			    
			    // !TODO Re-route to settings view
			    dom.msg.btnGrp.makeNode('settings', 'div', {
					text: 'Go to settings <i class="arrow right icon"></i>',
					css: 'ui blue button' 
			    });
			    
		    }
		    
	    }
	    
	    function build_payPeriodForm(dom, setup) {
		    
		    // setup.state
		    
		    function display_payperiodRanges(setup) {
			    
			    // setup.cycle_type
			    
			    var payroll_periodList = [];
			    var payroll_startDate = moment(setup.state.appSettings.apps.payroll.payroll_cycle.start_date);
			    var funcs = {
				    	_weekly: calc_weekly,
				    	_bi_weekly: calc_bi_weekly,
				    	_monthly: calc_monthly,
				    	_1st_and_15th: calc_1st_and_15th,
				    	_15th_and_last_day: calc_15th_and_last_day
			    	};
			    
			    function calc_weekly() {

	                for(var i = 0; i <= 48; i++) {
	                    
	                    if(payroll_startDate.unix() <= moment().unix()) {
		                    
		                	var start = payroll_startDate;
		                    var end = payroll_startDate.clone().add(1, 'week');
		                    
		                    payroll_periodList.push({
		                    	name: start.clone().format('MM/DD/YY') + ' - ' + end.clone().format('MM/DD/YY'),
		                    	value: start.clone()
		                	});
		                	
		                	payroll_startDate.add(1, 'week').add(1, 'day');   
		                    
	                    }
	                    
	                }
	                
	                payroll_periodList[payroll_periodList.length - 1].name += ' (Current Pay Period)';
	                
	                return payroll_periodList;
	                
	            }
			    
			    function calc_bi_weekly() {
		           
		           	for(var i = 0; i <= 48; i++) {
	    	
					    if(i % 2 === 0 && (payroll_startDate.unix() <= moment().unix()) ) {
	                    	
	                    	var start = payroll_startDate;
	                    	var end = payroll_startDate.clone().add(2, 'week');
	                    	
	                    	payroll_periodList.push({
	                        	name: start.clone().format('MM/DD/YY') + ' - ' + end.clone().format('MM/DD/YY'),
	                        	value: start.clone()
	                    	});
	                    	                    	
	                    	payroll_startDate.add(2, 'week').add(1, 'day');
	                    	
	                	}
	        
	            	}
	            	
	            	payroll_periodList[payroll_periodList.length - 1].name += ' (Current Pay Period)';
	            	
	            	return payroll_periodList;
			            
	            }
			    
			    function calc_monthly() {
	            
		            for(var i = 0; i <= 13; i++) {
	        	
	                	if(payroll_startDate.unix() <= moment().unix()) {
	                    	
	                    	var start = payroll_startDate;
	                    	var end = payroll_startDate.clone().add(1, 'month').subtract(1, 'day');
	                    	
	                    	payroll_periodList.push({
	                        	name: start.clone().format('MM/DD/YY') + ' - ' + end.clone().format('MM/DD/YY'),
	                        	value: start.clone()
	                    	});
	                    	
	                    	payroll_startDate.add(1, 'month');
	                    	
	                	}
	        
	            	}
	    
	            	payroll_periodList[payroll_periodList.length - 1].name += ' (Current Pay Period)';
	            	
	            	return payroll_periodList;
		            
	            }
			    
			    function calc_1st_and_15th() {

		            for(var i = 0; i <= 12; i++) {
			            
			            if(payroll_startDate.unix() <= moment().unix()) {
			            
				            var start = payroll_startDate.clone().date(2);
		                    var end = payroll_startDate.clone().date(15);
				            
				            payroll_periodList.push({
					            name: start.clone().format('MM/DD/YY') + ' - ' + end.clone().format('MM/DD/YY'),
					            value: start.clone()
				            });
				            
				            start = payroll_startDate.clone().date(16);
		                    end = payroll_startDate.clone().add(1, 'month').startOf('month');
				            
				            payroll_periodList.push({
					            name: start.clone().format('MM/DD/YY') + ' - ' + end.clone().format('MM/DD/YY'),
					            value: start.clone()
				            });
				            
				            payroll_startDate.add(1, 'month');
				        
				        }
			            
		            }
		            
		            payroll_periodList[payroll_periodList.length - 1].name += ' (Current Pay Period)';
		            
		            return payroll_periodList;
		            
	            }
			    
			    function calc_15th_and_last_day() {
	            
		            for(var i = 0; i <= 12; i++) {
			            
			            if(payroll_startDate.unix() <= moment().unix()) {
			            
				            var start = payroll_startDate.clone().date(1);
		                    var end = payroll_startDate.clone().date(15);
				            
				            payroll_periodList.push({
					            name: start.clone().format('MM/DD/YY') + ' - ' + end.clone().format('MM/DD/YY'),
					            value: start.clone()
				            });
				            
				            start = payroll_startDate.clone().date(16);
		                    end = payroll_startDate.clone().endOf('month');
				            
				            payroll_periodList.push({
					            name: start.clone().format('MM/DD/YY') + ' - ' + end.clone().format('MM/DD/YY'),
					            value: start.clone()
				            });
				            
				            payroll_startDate.add(1, 'month');
				        
				        }
			            
		            }
		            
		            payroll_periodList[payroll_periodList.length - 1].name += ' (Current Pay Period)';
		            
		            return payroll_periodList;
		            
	            }

				// Call appropriate function 
				return funcs['_'+setup.state.appSettings.apps.payroll.payroll_cycle.type](); // This code generates function
			    
		    }
		    
		    // Check if there are payroll cycle settings available
		    if(state.appSettings.apps.payroll.payroll_cycle) {
			    
			    var formObj = {
					    payrollPeriod: {
	                        name: 'payroll_period',
	                	    type: 'select',
	                	    label: 'Select payroll period',
	                	    options: display_payperiodRanges({
		                	    state: state
	                	    })
	            	    }
				    };
				    
				formObj.payrollPeriod.value = formObj.payrollPeriod.options[formObj.payrollPeriod.options.length - 1].value;

			    // If true, build pay-period form
			    dom.makeNode('form', 'form', formObj);
			    
			    dom.makeNode('lb_1', 'lineBreak', {
				   	spaces: 1 
			    });
			    
			    dom.makeNode('btnGrp', 'div', {});
			    
			    dom.btnGrp.makeNode('report', 'div', {
					text: 'View Payperiod Report <i class="arrow right icon"></i>',
					css: 'ui blue button right floated' 
			    }).notify('click', {
				    type: 'payroll-run',
				    data: {
					    run: function(data) {
						    
						    var formData = data.form.process().fields;

						    build_payperiodReport(data.mainDom, {
							    state: data.state,
							    payperiod_start: moment(parseInt(formData.payroll_period.value)).unix()
						    });
						    
					    },
					    mainDom: UI_CACHE.mainDom,
					    form: dom.form,
					    state: state
				    }
			    }, sb.moduleId);
			    
			    dom.makeNode('lb_2', 'lineBreak', {
				   	spaces: 2 
			    });
			    
		    } else {
			    
			    // If false, return
			    return;
			    
		    }
		    
	    }
	    
	    function build_payperiodReport(dom, setup) {
		    
		    // setup.state
		    // setup.payperiod_start -> unix value
		    // setup.report

		    var payperiodObj = {
			    	start: setup.payperiod_start,
			    	end: 0,
			    	fiscal_year_start: setup.state.appSettings.apps.payroll.fiscal_year_start,
			    	cycle_type: '',
			    	cycle_start: setup.state.appSettings.apps.payroll.payroll_cycle.start_date
		    	};
		    	
		    function display_payperiodEndDate(startDate, cycle_type) {
			    
			    switch(cycle_type) {
					
					case 'Weekly':
						
						return moment.unix(payperiodObj.start).clone().add(1, 'week');
						
						break;
						
					case 'Bi Weekly':
						
						return moment.unix(payperiodObj.start).clone().add(2, 'week');
					
						break;
						
					case 'Monthly':
						
						return moment.unix(payperiodObj.start).clone().add(1, 'month').subtract(1, 'day');
						
						break;
						
					case '1st and 15th':
						
						if(moment.unix(payperiodObj.start).date() === 2) {
							
							return moment.unix(payperiodObj.start).clone().add(13, 'day');
							
						} else {
							
							return moment.unix(payperiodObj.start).clone().endOf('month').add(1, 'day');
							
						}
						
						break;
						
					case '15th and last day':
						
						if(moment.unix(payperiodObj.start).date() === 1) {
							
							return moment.unix(payperiodObj.start).clone().add(14, 'day');
							
						} else {
							
							return moment.unix(payperiodObj.start).clone().endOf('month');
							
						}
						
						break;
					
				}
			    
		    }
		    
		    function build_reportHeader(dom, setup) {
			    
			    // setup.payperiodObj
			    // setup.state
			    // setup.loading --> bool: buttons will display in loading state
			    // setup.btns --> bool: show or hide buttons
			    // setup.report
			    
			    var configured_settings = {
				    	_setting1: false, // Breakdown by job types
				    	_setting2: false  // Condensed view of report
			    	};
			    var view_title = '';
			    
			    function build_reportSettings(dom, setup) {
				    
				    // setup.configured_settings
				    // setup.payperiodObj
				    // setup.report
				    // setup.state
				    
				    function build_setting1_btn(dom, setup) {
					    
					    // setup.configured_settings
					    // setup.report
					    // setup.state
					    
					    if(setup.configured_settings._setting1) {
					    
						    dom.makeNode('setting_1', 'div', {
							    text: '<i class="toggle on big icon"></i> <span>Remove breakdown by job types</span>'
						    }).notify('click', {
							    type: 'payroll-run',
							    data: {
								    run: function(data) {
									    
									    configured_settings._setting1 = false;
									    total_report_payroll = 0;
									    
									    var settingSetup = {};
									    
									    settingSetup.configured_settings = configured_settings;
									    settingSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    settingSetup.report = setup.report;
										    
									    }
									    
									    build_setting1_btn(dom, settingSetup);
									    
									    var crudSetup = {};
									    
									    crudSetup.payperiodObj = setup.payperiodObj;
									    crudSetup.jobTypes = false;
									    crudSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    crudSetup.report = setup.report;
										    
									    }
									    
									    call_reportsCrud(crudSetup);
									    									    
								    }
							    }
						    }, sb.moduleId);
						    
					    } else {
						    
						    dom.makeNode('setting_1', 'div', {
							    text: '<i class="toggle off big icon"></i> <span>Include breakdown by job types (not available on compact view)</span>'
						    }).notify('click', {
							    type: 'payroll-run',
							    data: {
								    run: function(data) {
									    
									    configured_settings._setting1 = true;
									    
									    var settingSetup = {};
									    
									    settingSetup.configured_settings = configured_settings;
									    settingSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    settingSetup.report = setup.report;
										    
									    }
									    
									    build_setting1_btn(dom, settingSetup);
									    
									    var crudSetup = {};
									    
									    crudSetup.payperiodObj = setup.payperiodObj;
									    crudSetup.jobTypes = true;
									    crudSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    crudSetup.report = setup.report;
										    
									    }
									    
									    call_reportsCrud(crudSetup);
									    
								    }
							    }
						    }, sb.moduleId);
						    
					    }
					    
					    dom.patch();
					    
				    }
				    
				    function build_setting2_btn(dom, setup) {

					    // setup.configured_settings
					    // setup.payperiodObj
					    // setup.report
					    // setup.state
					    
					    if(setup.configured_settings._setting2) {
					    
						    dom.makeNode('setting_2', 'div', {
							    text: '<i class="toggle on big icon"></i> <span>Switch to regular payperiod report</span>'
						    }).notify('click', {
							    type: 'payroll-run',
							    data: {
								    run: function(data) {
									    
									    configured_settings._setting2 = false;
									    total_report_payroll = 0;
									    
									    var settingSetup = {};
									    
									    settingSetup.configured_settings = configured_settings;
									    settingSetup.payperiodObj = setup.payperiodObj;
									    settingSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    settingSetup.report = setup.report;
										    
									    }
									    
									    build_setting2_btn(dom, settingSetup);
									    
									    var crudSetup = {};
									    
									    crudSetup.payperiodObj = setup.payperiodObj;
									    crudSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    crudSetup.report = setup.report;
										    
									    }
									    
									    call_reportsCrud(crudSetup);
									    
								    }
							    }
						    }, sb.moduleId);
						    
					    } else {
						    
						    dom.makeNode('setting_2', 'div', {
							    text: '<i class="toggle off big icon"></i> <span>Switch to compact payperiod report</span>'
						    }).notify('click', {
							    type: 'payroll-run',
							    data: {
								    run: function(data) {
									    
									    configured_settings._setting2 = true;
									    
									    var settingSetup = {};
									    
									    settingSetup.configured_settings = configured_settings;
									    settingSetup.payperiodObj = setup.payperiodObj;
									    settingSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    settingSetup.report = setup.report;
										    
									    }
									    
									    build_setting2_btn(dom, settingSetup);
									    
									    var crudSetup = {};
									    
									    crudSetup.payperiodObj = setup.payperiodObj;
									    crudSetup.compact = true;
									    crudSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    crudSetup.report = setup.report;
										    
									    }
									    
									    call_reportsCrud(crudSetup);
									    
								    }
							    }
						    }, sb.moduleId);
						    
					    }
					    
					    dom.patch();
					    
				    }
				    
				    dom.empty();
				    
				    dom.makeNode('wrapper', 'div', {});
				    
				    dom.wrapper.makeNode('head', 'div', {});
				    dom.wrapper.makeNode('lb_1', 'lineBreak', {
					    spaces: 1
				    });
				    dom.wrapper.makeNode('body', 'div', {});
				    
				    dom.wrapper.head.makeNode('title', 'div', {
					   	text: 'Configure Report',
					   	css: 'ui huge header' 
				    });
				    
				    var setting_1_setup = {};
				    
				    setting_1_setup.configured_settings = setup.configured_settings;
				    setting_1_setup.state = setup.state;
				    
				    if(setup.hasOwnProperty('report')) {
					    
					    setting_1_setup.report = setup.report;
					    
				    }
				    
				    build_setting1_btn(dom.wrapper.body, setting_1_setup);
				    
				    var setting_2_setup = {};
				    
				    setting_2_setup.configured_settings = setup.configured_settings;
				    setting_2_setup.payperiodObj = setup.payperiodObj;
				    setting_2_setup.state = setup.state;
				    
				    if(setup.hasOwnProperty('report')) {
					    
					    setting_2_setup.report = setup.report;
					    
				    }
				    
				    build_setting2_btn(dom.wrapper.body, setting_2_setup);
				    
				    dom.wrapper.body.makeNode('btnGrp', 'div', {});
				    
				    dom.wrapper.body.btnGrp.makeNode('lb_1', 'lineBreak', {
					    spaces: 1
				    });
				    
				    dom.wrapper.body.btnGrp.makeNode('close', 'div', {
					   	text: 'Close',
					   	css: 'ui red button right floated' 
				    }).notify('click', {
					    type: 'payroll-run',
					    data: {
						    run: function(data) {
							    
							    data.modal.hide();
							    
						    },
						    modal: UI_CACHE.modal
					    }
				    }, sb.moduleId);
				    
				    dom.patch();
				    
			    }
			    
			    function complete_payroll(dom, setup) {
				    
				    // setup.entries
				    // setup.payperiodObj
				    // setup.total_payroll
				    // setup.state

					var newObj = {};
					var total_overtime_price = calc_overTime({
							entries: setup.entries
						});
					
					dom.empty();					
						
					display_loader({
						mainDom: dom,
						text: 'Generating payment report data...'
					});

					newObj.name = 'Payment Report for ' + moment.unix(setup.payperiodObj.start).format('M/D/YYYY') + ' to ' + setup.payperiodObj.end.format('M/D/YYYY');
				    newObj.start_date = moment.unix(setup.payperiodObj.start).startOf('day');
				    newObj.end_date = moment(setup.payperiodObj.end).endOf('day');
				    newObj.cycle_type = setup.state.appSettings.apps.payroll.payroll_cycle.type;
				    newObj.cycle_start = setup.payperiodObj.cycle_start;
				    newObj.fiscal_year_start = setup.payperiodObj.fiscal_year_start;
				    
				   	sb.data.db.obj.create('payment_report', newObj, function(newReport) {

						var total_report_price = 0;

						display_loader({
							mainDom: dom,
							text: 'Updating time entries...'
						});
						
						_.each(setup.entries, function(entry) {
						
							entry.payment_report = newReport.id;
							entry.is_editable = false;
							
							if(entry.is_salary !== 'yes') { // Regular entries
								
								entry.cycle_type = '';
								
								if(entry.hasOwnProperty('payrollObj')) {
									
									total_report_price += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
									entry.compensation = check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
									entry.default_payroll = 'no';
									entry.billing_type = entry.payrollObj.pay_style;
									
									if(entry.billing_type === 'hourly') {
										
										entry.hourly_rate = entry.payrollObj.rate;
										
									} else if(entry.billing_type === 'flat_and_hourly') {
										
										entry.hourly_rate = entry.payrollObj.rate;
										entry.flat_rate = entry.payrollObj.rate;
										entry.max_flat_hours = entry.payrollObj.max_flat_hours;
										
									} else { // This is for 'flat' billing type
										
										entry.flat_rate = entry.payrollObj.rate;
										
									}
									
								} else {
									
									total_report_price += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
									entry.compensation = check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
									entry.default_payroll = 'yes';
									entry.billing_type = entry.service.price_type;
									
									if(entry.billing_type === 'hourly' || entry.billing_type === '1') {
										
										entry.billing_type = 'hourly';
										entry.hourly_rate = entry.service.rate;
										
									} else if(entry.billing_type === 'flat_and_hourly' || entry.billing_type === '3') {
										
										entry.billing_type = 'flat_and_hourly';
										
										entry.hourly_rate = entry.service.rate;
										entry.flat_rate = entry.service.price;
										entry.max_flat_hours = entry.service.max_flat_hours;
										
									} else if(entry.billing_type === 'flat' || entry.billing_type === '0') {
										
										entry.billing_type = 'flat';
										
										entry.flat_rate = entry.service.price;
										
									} else {
										
										entry.billing_type = 'non-billable';
										
									}
									
								}
									
							} else { // Salary entries
								
								entry.cycle_type = setup.state.appSettings.apps.payroll.payroll_cycle.type;
								
								total_report_price += calc_yearlySalary({
									entry: entry,
									payperiodObj: payperiodObj
								});
								
								entry.compensation = calc_yearlySalary({ // This will set the total compensation for a given payperiod
									entry: entry,
									payperiodObj: payperiodObj
								});
								
								entry.billing_type = 'salary';
								entry.default_payroll = 'no';
								
								
							}
							
							total_report_price = parseInt(total_report_price);
							
						});

						newReport.total_price = total_report_price;
						
						display_loader({
							mainDom: dom,
							text: 'Updating payment report data...'
						});

						sb.data.db.obj.update('payment_report', newReport, function(res) {
						
							display_loader({
								mainDom: dom,
								text: 'Updating time entry data...'
							});
							
							sb.data.db.obj.update('time_entries', setup.entries, function(res) {
							
								sb.notify({						// NAVIGATING TO PAYMENT REPORT SINGLE VIEW
									type: 'app-navigate-to',
									data: {
										itemId: 'payroll',
										viewId: {
											dom: function(report, dom, state, draw) {
	
												var payperiodObj = {
														start: moment(report.start_date),
														end: moment(report.end_date).clone().subtract(1, 'day'),
														type: report.cycle_type,
														cycle_start: report.cycle_start,
														fiscal_year_start: report.fiscal_year_start
													};
												
												dom.empty();
												
												dom.makeNode('wrapper', 'div', {});

												build_payrollReport(dom.wrapper, setup.state, draw, report);
												
												draw(false);
												dom.patch();
												
											},
											icon: '<i class="file alternate outline icon"></i>',
											id: 'single-' + newReport.id,
											parent: 'payment-report',
											removable: true,
											rowObj: newReport,
											title: newReport.name,
											type: 'table-single-item',
											viewState: setup.state
										}
									}
								});
								
							}, 1);
							
						}, 1);
						
					}, 1); 
				    
			    }
			    
			    if(setup.hasOwnProperty('report')) {
				    
				    view_title = 'Payment Report';
				    
			    } else {
				    
				    view_title = 'Selected Payperiod';
				    
			    }
			    
			    dom.makeNode('grid', 'div', {
					css: 'ui grid' 
			    });
			    
			    dom.grid.makeNode('col1', 'div', {
				   	css: 'six wide column' 
			    });
			    dom.grid.makeNode('col2', 'div', {
				   	css: 'ten wide column' 
			    });
			    
			    dom.grid.col1.makeNode('title', 'div', {
				   	text: '<h1>'+ view_title +' <div class="ui sub header">'+ moment.unix(setup.payperiodObj.start).format('MMMM Do YYYY') + ' - ' + setup.payperiodObj.end.format('MMMM Do YYYY') +'</div></h1>',
				   	css: 'ui header' 
			    });
			    
			    dom.grid.col2.makeNode('btnGrp', 'div', {});
			    
			    if(!setup.hasOwnProperty('btns') || setup.btns === true) {
				    
					if(setup.hasOwnProperty('loading') && setup.loading === true) {
				    
					    dom.grid.col2.btnGrp.makeNode('back', 'div', {
						   	text: 'loading',
						   	css: 'ui red button right floated loading' 
					    });
					    
					    dom.grid.col2.btnGrp.makeNode('view_settings', 'div', {
						    text: 'loading',
						    css: 'ui blue button right floated loading'
					    });
					    
					    dom.grid.col2.btnGrp.makeNode('complete', 'div', {
						    text: 'loading',
						    css: 'ui green button right floated loading'
					    });
					    
				    } else {
					    
					    if(!setup.hasOwnProperty('report')) {
						    
							// Back to main view
						    dom.grid.col2.btnGrp.makeNode('back', 'div', {
							   	text: 'Back',
							   	css: 'ui red button right floated' 
						    }).notify('click', {
							    type: 'payroll-run',
							    data: {
								    run: function(data) {
										
										init(data.mainDom, data.state);
										    
								    },
								    mainDom: UI_CACHE.mainDom,
								    state: setup.state
							    }
						    }, sb.moduleId);    
						    
					    }
					    
					    dom.grid.col2.btnGrp.makeNode('view_settings', 'div', {
						    text: 'Report Settings',
						    css: 'ui blue button right floated'
					    }).notify('click', {
						    type: 'payroll-run',
						    data: {
							    run: function(data) {
								    
								    data.modal.listeners[0] = function() {
									    
									    var settingsSetup = {};
									    
									    settingsSetup.configured_settings = configured_settings;
									    settingsSetup.payperiodObj = setup.payperiodObj;
									    settingsSetup.state = setup.state;
									    
									    if(setup.hasOwnProperty('report')) {
										    
										    settingsSetup.report = setup.report;
										    
									    }
									    
									    build_reportSettings(data.modal.body, settingsSetup);
									    
									    data.modal.show();
									    
								    }
								    
								    data.modal.listeners[0]();
								    
							    },
							    modal: UI_CACHE.modal
						    }
					    }, sb.moduleId);
					    
					    if(!setup.hasOwnProperty('report')) {
						    
							// Generate final payroll report
						    dom.grid.col2.btnGrp.makeNode('complete', 'div', {
							    text: 'Complete Payroll',
							    css: 'ui green button right floated'
						    }).notify('click', {
							    type: 'payroll-run',
							    data: {
								    run: function(data) {
									    
									    sb.dom.alerts.ask({
											title: 'Are you sure?',
											text: 'You will not be able to edit this report or its associated entries.'
										}, function(resp) {
											
											if(resp) {
												
												swal.close();
												
												complete_payroll(UI_CACHE.mainDom, {
													entries: DATA_CACHE.entries,
													payperiodObj: payperiodObj,
													total_payroll: DATA_CACHE.total_payroll,
													state: setup.state
												});	
												
											}
											
										});
									    
								    }
							    }
						    }, sb.moduleId);    
						    
					    }   
					    
				    }   
				    
			    }
			    
			    dom.patch();
			    
		    }
		    
		    function calc_overTime(setup) {
				    
			    // setup.entries

			    switch(payperiodObj.cycle_type) {
				    
				    case 'Weekly':
				    	
				    	var week_total_hrs = 0;
				    	var week_total_pay = 0;
				    	var hourly_entries = [];
				    	var week_final = 0;
				    	
				    	_.each(setup.entries, function(entry) {
					    	
					    	if(entry.is_salary !== 'yes') {
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									if(entry.payrollObj.pay_style === 'hourly') {
										
										hourly_entries.push(entry);
										
									}
									
								} else {
									
									if(entry.service.price_type === '1') {
										
										hourly_entries.push(entry);
										
									}
									
								}
						    	
					    	}
					    	
				    	});
				    	
				    	_.each(hourly_entries, function(entry) {
					    	
					    	if(moment(entry.start_date).unix() < moment.unix(payperiodObj.start).clone().add(1, 'week').unix()) {
						    	
						    	week_total_hrs += parseInt(entry.duration);
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									week_total_pay += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									week_total_pay += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
						    	
					    	}
					    	
					    	week_total_pay = parseInt(week_total_pay);
					    	
				    	});
				    	
				    	if( (week_total_hrs/60) > 40 ) {
					    	
					    	var week_rate = 0;
					    	var diff = (week_total_hrs/60) - 40;
					    	
					    	week_rate = week_total_pay / (week_total_hrs/60);
					    	
					    	week_rate = week_rate / 2;
					    	
					    	week_final = week_rate * diff;
					    	
				    	}
				    	
				    	return week_final;
				    	
				    	break;
				    	
				    case '15th and last day':
				    	
				    	var week_1_hrs = 0;
				    	var week_2_hrs = 0;
				    	var week_1_pay = 0;
				    	var week_2_pay = 0;
				    	var hourly_entries = [];
				    	var week_1_final = 0;
				    	var week_2_final = 0;
				    	
				    	_.each(setup.entries, function(entry) {
					    	
					    	if(entry.is_salary !== 'yes') {
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									if(entry.payrollObj.pay_style === 'hourly') {
										
										hourly_entries.push(entry);
										
									}
									
								} else {
									
									if(entry.service.price_type === '1') {
										
										hourly_entries.push(entry);
										
									}
									
								}
						    	
					    	}
					    	
				    	});
				    	
				    	_.each(hourly_entries, function(entry) {
					    	
					    	// Check if payperiod starts on 15th
					    	if(moment.unix(payperiodObj.start).startOf('day').unix() === moment.unix(payperiodObj.start).date(15).startOf('day').unix()) {
						    	
						    	// If true, check for entries between 15th and 21st
						    	if( (moment.unix(payperiodObj.start).startOf('day').unix() < moment(entry.start_date).unix()) && (moment.unix(payperiodObj.start).clone().date(21).endOf('day').unix() > moment(entry.start_date).unix()) ) {
							    	
							    	week_1_hrs += parseInt(entry.duration);
									
									if(entry.hasOwnProperty('payrollObj')) {
						
										week_1_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										week_1_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}
							    	
						    	} else { // If not, check for entries between 22nd and last day of month
							    	
							    	week_2_hrs += parseInt(entry.duration);
									
									if(entry.hasOwnProperty('payrollObj')) {
						
										week_2_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										week_2_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}
							    	
						    	}
						    	
					    	} else { // If not, check for entries between 1st and 15th
						    	
						    	// If true, check for entries between 1st and 7th
						    	if( (moment.unix(payperiodObj.start).clone().date(1).startOf('day').unix() < moment(entry.start_date).unix()) && (moment.unix(payperiodObj.start).clone().date(7).endOf('day').unix() > moment(entry.start_date).unix()) ) {
							    	
							    	week_1_hrs += parseInt(entry.duration);
							    	
							    	if(entry.hasOwnProperty('payrollObj')) {
						
										week_1_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										week_1_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}
							    	
						    	} else { // If not, check for entries between 8th and 14th
							    	
							    	week_2_hrs += parseInt(entry.duration);
							    	
							    	if(entry.hasOwnProperty('payrollObj')) {
						
										week_2_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										week_2_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}
							    	
						    	}
						    	
					    	}
					    	
				    	});
				    	
				    	if( (week_1_hrs/60) > 40 ) {
					    	
					    	var week_1_rate = 0;
					    	var diff = (week_1_hrs/60) - 40;
					    	
					    	week_1_rate = week_1_pay / (week_1_hrs/60);
					    	
					    	week_1_rate = week_1_rate / 2;
					    	
					    	week_1_final = week_1_rate * diff;
					    	
				    	}
				    	
				    	if( (week_2_hrs/60) > 40 ) {
					    	
					    	var week_2_rate = 0;
					    	var diff = (week_2_hrs/60) - 40;
					    	
					    	week_2_rate = week_2_pay / (week_2_hrs/60);
					    	
					    	week_2_rate = week_2_rate / 2;
					    	
					    	week_2_final = week_2_rate * diff;
					    	
				    	}
				    	
				    	return (week_1_final + week_2_final);
				    	
				    	break;
				    	
				    case '1st and 15th':
				    	
				    	var week_1_hrs = 0;
				    	var week_2_hrs = 0;
				    	var week_1_pay = 0;
				    	var week_2_pay = 0;
				    	var hourly_entries = [];
				    	var week_1_final = 0;
				    	var week_2_final = 0;

				    	_.each(setup.entries, function(entry) {
					    	
					    	if(entry.is_salary !== 'yes') {
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									if(entry.payrollObj.pay_style === 'hourly') {
										
										hourly_entries.push(entry);
										
									}
									
								} else {
									
									if(entry.service.price_type === '1') {
										
										hourly_entries.push(entry);
										
									}
									
								}
						    	
					    	}
					    	
				    	});
				    	
				    	_.each(hourly_entries, function(entry) {
					    	
					    	// Check if the payperiod starts on the 2nd
					    	if(moment.unix(payperiodObj.start).startOf('day').unix() === moment.unix(payperiodObj.start).date(2).startOf('day').unix()) {
								
								// If true, check for entries between 2nd and 8th
								if( (moment.unix(payperiodObj.start).startOf('day').unix() < moment(entry.start_date).unix()) && (moment.unix(payperiodObj.start).clone().date(8).endOf('day').unix() > moment(entry.start_date).unix()) ) {
									
									week_1_hrs += parseInt(entry.duration);
									
									if(entry.hasOwnProperty('payrollObj')) {
						
										week_1_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										week_1_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}

								} else { // If not, check for entries between 9th and 15th
									
									week_2_hrs += parseInt(entry.duration);
									
									if(entry.hasOwnProperty('payrollObj')) {
						
										week_2_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										week_2_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}

								}

					    	} else { // If not, check for entries between 15th and 1st of next month
						    	
						    	// Check for entries between 15th and 21st
						    	if( (moment.unix(payperiodObj.start).clone().add(7, 'days').startOf('day').unix() < moment(entry.start_date).unix()) && (moment.unix(payperiodObj.start).clone().date(21).endOf('day').unix() > moment(entry.start_date).unix()) ) {
									
									week_1_hrs += parseInt(entry.duration);
									
									if(entry.hasOwnProperty('payrollObj')) {
						
										week_1_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										week_1_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}
									
								} else { // If not, check for entries between 22nd and 1st of next month
									
									week_2_hrs += parseInt(entry.duration);
									
									if(entry.hasOwnProperty('payrollObj')) {
						
										week_2_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										week_2_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}
									
								}
						    	
					    	}	
					    	
				    	});
				    	
				    	if( (week_1_hrs/60) > 40 ) {
					    	
					    	var week_1_rate = 0;
					    	var diff = (week_1_hrs/60) - 40;
					    	
					    	week_1_rate = week_1_pay / (week_1_hrs/60);
					    	
					    	week_1_rate = week_1_rate / 2;
					    	
					    	week_1_final = week_1_rate * diff;
					    	
				    	}
				    	
				    	if( (week_2_hrs/60) > 40 ) {
					    	
					    	var week_2_rate = 0;
					    	var diff = (week_2_hrs/60) - 40;
					    	
					    	week_2_rate = week_2_pay / (week_2_hrs/60);
					    	
					    	week_2_rate = week_2_rate / 2;
					    	
					    	week_2_final = week_2_rate * diff;
					    	
				    	}
				    	
				    	return (week_1_final + week_2_final);
				    	
				    	break;
				    
				    case 'Bi Weekly':

				    	var week_one_total = 0;
				    	var week_two_total = 0;
				    	var hourly_entries = [];
				    	var total_pay_week_1 = 0;
				    	var total_pay_week_2 = 0;
				    	var week_one_final = 0;
				    	var week_two_final = 0;
				    	
				    	_.each(setup.entries, function(entry) {
					    	
					    	if(entry.is_salary !== 'yes') {
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									if(entry.payrollObj.pay_style === 'hourly') {
										
										hourly_entries.push(entry);
										
									}
									
								} else {
									
									if(entry.service.price_type === '1') {
										
										hourly_entries.push(entry);
										
									}
									
								}	
						    	
					    	}
					    	
				    	});

				    	_.each(hourly_entries, function(entry) {
					    	
					    	if(moment(entry.start_date).unix() < moment.unix(payperiodObj.start).clone().add(1, 'week').unix()) {
						    	
								week_one_total += parseInt(entry.duration);
								
								if(entry.hasOwnProperty('payrollObj')) {
						
									total_pay_week_1 += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									total_pay_week_1 += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
						    	
					    	} else {
						    	
						    	week_two_total += parseInt(entry.duration);
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									total_pay_week_2 += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									total_pay_week_2 += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
						    	
					    	}
					    	
					    	total_pay_week_1 = parseInt(total_pay_week_1);
					    	total_pay_week_2 = parseInt(total_pay_week_2);
					    	
				    	});
				    	
				    	if( (week_one_total/60) > 40 ) {
					    	
					    	var week_one_rate = 0;
					    	var diff = (week_one_total/60) - 40;
					    	
					    	week_one_rate = total_pay_week_1 / (week_one_total/60);
					    	
					    	week_one_rate = week_one_rate / 2;
					    	
					    	week_one_final = week_one_rate * diff;
					    	
				    	}
				    	
				    	if( (week_two_total/60) > 40 ) {
					    	
					    	var week_two_rate = 0;
					    	var diff = (week_two_total/60) - 40;
					    	
					    	week_two_rate = total_pay_week_2 / (week_two_total/60);
					    	
					    	week_two_rate = week_two_rate / 2;
					    	
					    	week_two_final = week_two_rate * diff;
					    	
				    	}

				    	return (week_one_final + week_two_final);

				    	break;
				    	
				    case 'Monthly':
				    	
				    	var week_1_hrs = 0;
				    	var week_1_pay = 0;
				    	var week_2_hrs = 0;
				    	var week_2_pay = 0;
				    	var week_3_hrs = 0;
				    	var week_3_pay = 0;
				    	var week_4_hrs = 0;
				    	var week_4_pay = 0;
				    	var hourly_entries = [];
				    	var week_1_final = 0;
				    	var week_2_final = 0;
				    	var week_3_final = 0;
				    	var week_4_final = 0;
				    	
				    	_.each(setup.entries, function(entry) {
					    	
					    	if(entry.is_salary !== 'yes') {
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									if(entry.payrollObj.pay_style === 'hourly') {
										
										hourly_entries.push(entry);
										
									}
									
								} else {
									
									if(entry.service.price_type === '1') {
										
										hourly_entries.push(entry);
										
									}
									
								}
						    	
					    	}
					    	
				    	});
				    	
				    	_.each(hourly_entries, function(entry) {
					    	
					    	if(moment(entry.start_date).unix() < moment.unix(payperiodObj.start).clone().add(1, 'week').unix()) {
						    	
						    	week_1_hrs += parseInt(entry.duration);
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									week_1_pay += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									week_1_pay += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
						    	
					    	} else if(moment(entry.start_date).unix() < moment.unix(payperiodObj.start).clone().add(2, 'week').unix()) {
						    	
						    	week_2_hrs += parseInt(entry.duration);
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									week_2_pay += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									week_2_pay += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
						    	
					    	} else if(moment(entry.start_date).unix() < moment.unix(payperiodObj.start).clone().add(3, 'week').unix()) {
						    	
						    	week_3_hrs += parseInt(entry.duration);
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									week_3_pay += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									week_3_pay += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
						    	
					    	} else {
						    	
						    	week_4_hrs += parseInt(entry.duration);
						    	
						    	if(entry.hasOwnProperty('payrollObj')) {
						
									week_4_pay += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									week_4_pay += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
						    	
					    	}
					    	
				    	});
				    	
				    	if( (week_1_hrs/60) > 40) {
					    	
					    	var week_1_rate = 0;
					    	var diff = (week_1_hrs/60) - 40;
					    	
					    	week_1_rate = week_1_pay / (week_1_hrs/60);
					    	
					    	week_1_rate = week_1_rate / 2;
					    	
					    	week_1_final = week_1_rate * diff;
					    	
				    	}
				    	
				    	if( (week_2_hrs/60) > 40) {
					    	
					    	var week_2_rate = 0;
					    	var diff = (week_2_hrs/60) - 40;
					    	
					    	week_2_rate = week_2_pay / (week_2_hrs/60);
					    	
					    	week_2_rate = week_2_rate / 2;
					    	
					    	week_2_final = week_2_rate * diff;
					    	
				    	}
				    	
				    	if( (week_3_hrs/60) > 40) {
					    	
					    	var week_3_rate = 0;
					    	var diff = (week_3_hrs/60) - 40;
					    	
					    	week_3_rate = week_3_pay / (week_3_hrs/60);
					    	
					    	week_3_rate = week_3_rate / 2;
					    	
					    	week_3_final = week_3_rate * diff;
					    	
				    	}
				    	
				    	if( (week_4_hrs/60) > 40) {
					    	
					    	var week_4_rate = 0;
					    	var diff = (week_4_hrs/60) - 40;
					    	
					    	week_4_rate = week_4_pay / (week_4_hrs/60);
					    	
					    	week_4_rate = week_4_rate / 2;
					    	
					    	week_4_final = week_4_rate * diff;
					    	
				    	}
				    	
				    	return (week_1_final + week_2_final + week_3_final + week_4_final);
				    	
				    	break;
				    
			    }
			    
		    }
		    
		    function build_reportInfo(dom, setup) {
			    
			    // setup.payperiodObj
			    
			    var fiscal_year_start = 'Not Set';

			    function display_totalPayroll(setup) {

				    // setup.totalPayroll
				    // setup.report

				    dom.seg.grid.col4.val.empty();
				    
				    // Check if there is a total being passed to this function
				    if(setup.hasOwnProperty('totalPayroll')) {
					    
					    // If true, display it
					    dom.seg.grid.col4.val.makeNode('total', 'div', {
						    text: '<h4>$'+ (setup.totalPayroll/100).formatMoney() +'</h4>',
						    css: 'text-muted'
					    });
					    
				    } else if(setup.hasOwnProperty('report')) {
					    
					    // If there is a payment report, then grap the total payroll amount on the payment report obj
					    dom.seg.grid.col4.val.makeNode('total', 'div', {
						    text: '<h4>$'+ (setup.report.total_price/100).formatMoney() +'</h4>',
						    css: 'text-muted'
					    });
					    
				    } else {
					    
					    // If false, display loader
					    display_loader({
							mainDom: dom.seg.grid.col4.val,
							text: 'Calculating total payroll...' 
					    });
					    
				    }
				    
				    dom.seg.grid.col4.val.patch();
				    
			    }
			    
			    if(setup.payperiodObj.fiscal_year_start !== 'Not Set') {
				    fiscal_year_start = moment(setup.payperiodObj.fiscal_year_start).format('dddd MMMM Do, YYYY');
			    }
			    
				dom.empty();
				
				dom.makeNode('seg', 'div', {
					css: 'ui basic segment'
				});
				
				dom.seg.makeNode('grid', 'div', {
					css: 'ui equal width grid'
				});
				
				// Grid level
				dom.seg.grid.makeNode('col1', 'div', {
					css: 'column text-center'
				});
				dom.seg.grid.makeNode('col2', 'div', {
					css: 'column text-center'
				});
				dom.seg.grid.makeNode('col3', 'div', {
					css: 'column text-center'
				});
				dom.seg.grid.makeNode('col4', 'div', {
					css: 'column text-center'
				});
				
				// Cols level
				dom.seg.grid.col1.makeNode('title', 'div', {
					tag: 'h3',
					text: 'Fiscal Year Start'
				});
				
				dom.seg.grid.col1.makeNode('val', 'div', {
					text: '<h4>'+ fiscal_year_start +'</h4>',
					css: 'text-muted'
				});
				
				dom.seg.grid.col2.makeNode('title', 'div', {
					tag: 'h3',
					text: 'Cycle Type'
				});
				
				dom.seg.grid.col2.makeNode('val', 'div', {
					text: '<h4>'+ setup.payperiodObj.cycle_type +'</h4>',
					css: 'text-muted'
				});
				
				dom.seg.grid.col3.makeNode('title', 'div', {
					tag: 'h3',
					text: 'Cycle Start'
				});
				
				dom.seg.grid.col3.makeNode('val', 'div', {
					text: '<h4>'+ moment(setup.payperiodObj.cycle_start).format('dddd MMMM Do, YYYY') +'</h4>',
					css: 'text-muted'
				});
				
				dom.seg.grid.col4.makeNode('title', 'div', {
					tag: 'h3',
					text: 'Total Payroll'
				});
				
				dom.seg.grid.col4.makeNode('val', 'div', {});
				
				display_totalPayroll({});
				
				dom.patch();
				
				// Methods
				
				build_reportInfo.display_totalPayroll = display_totalPayroll;
			    
		    }
		    
		    function check_billingType(setup) {

			    // setup.payroll --> this is a payroll obj if applicable
			    // setup.service --> this is a service obj if no payroll obj found
			    // setup.entry --> time entry associated

				if(setup.hasOwnProperty('payroll')) {
					
					switch(setup.payroll.pay_style) {
					
						case 'flat':
							
							return {
								display: '$' + (setup.payroll.rate/100).formatMoney() + ' flat fee',
								total: setup.payroll.rate
							};
							
							break;
							
						case 'hourly':

							return {
								display: '$' + (setup.payroll.rate/100).formatMoney() + ' per hour',
								total: (setup.payroll.rate * (setup.entry.duration/60))
							};
							
							break;
							
						case 'flat_and_hourly':
						
							var total = 0;

							if( (setup.payroll.max_flat_hours / 60) > setup.entry.duration ) {
								
								var temp_diff = (setup.payroll.max_flat_hours / 60) - setup.entry.duration;
								
								temp_diff = temp_diff / 60;
								
								total = (setup.payroll.price + (temp_diff * setup.payroll.rate));
								
							} else {
								
								total = setup.payroll.price;
								
							}
						
							return {
								display: '$' + (setup.payroll.flat_rate/100).formatMoney() + ' flat fee ('+ (setup.payroll.hourly_rate/100).formatMoney() +' per hour after '+ setup.payroll.max_flat_hours +' hours)',
								total: total
							};
							
							break;
							
						default: 
						
							return {
								display: 'Not specified',
								total: 0	
							};
						
					}	
					
				} else {
					
					switch(setup.service.price_type) {
						
						case '0':
						case 'flat':
						
							return {
								display: '$' + (setup.service.price/100).formatMoney() + ' flat fee',
								total: setup.service.price
							};
						
							break;
							
						case '1':
						case 'hourly':
						
							return {
								display: '$' + (setup.service.rate/100).formatMoney() + ' per hour',
								total: ((setup.entry.duration/60) * setup.service.rate)
							};
						
							break;
							
						case '3':
						case 'flat_and_hourly':
						
							var total = 0;
						
							if( (setup.service.max_flat_hours / 60) > setup.entry.duration ) {
								
								var temp_diff = (setup.service.max_flat_hours / 60) - setup.entry.duration;
								
								temp_diff = temp_diff / 60;
								
								total = (setup.service.price + (temp_diff * setup.service.rate));
								
							} else {
								
								total = setup.service.price;
								
							}
						
							return {
								display: '$' + (setup.service.price/100).formatMoney() + ' flat fee ($' + (setup.service.rate/100).formatMoney() + ' per hour after ' + setup.service.max_flat_hours + ' hours)',
								total: total
							};
						
							break;
							
						default: 
						
							return {
								display: 'Non-billable',
								total: '$' + 0	
							};
						
					}
					
				}
		        
	        }
		    
		    function calc_yearlySalary(setup) {

			    // setup.entry --> this is a salary entry not a regular entry
			    // setup.payperiodObj
			    
			    var total = 0;
			    
			    switch(setup.payperiodObj.cycle_type) {
									
					case 'Bi Weekly':
					case '1st and 15th':
					case '15th and last day':
						
						return total = (setup.entry.compensation / 24);
						
						break;
						
					case 'Weekly':
						
						return total = (setup.entry.compensation / 48);
						
						break;
						
					case 'Monthly':
						
						return total = (setup.entry.compensation / 12);
						
						break;
					
				}
			    
		    }
		    
		    function build_reportTable(dom, setup) {

			    // setup.entries --> List of time entries passed in by the crud table
			    // setup.compact --> will morph view into a more compact form
			    // setup.jobTypes --> will reveal job type breakdowns
			    // setup.report
			    // setup.state
			    
			    var total_report_payroll = 0;

			    function generate_salaryEntries(setup, callback) {

				    // setup.entries --> optional
				    // setup.payrollObjs
				    // setup.report
				    // setup.state

				    var queryObj = {
						    childObjs: 1,
						    start_date: {
	                            type: 'between', 
	                            start: moment.unix(payperiodObj.start).startOf('day').unix(), 
	                            end: payperiodObj.end.endOf('day').unix()
	                        } 
						};
					var locationQuery = [];
					
					if(DATA_CACHE.locationId !== 0) {
						
						queryObj.location = DATA_CACHE.locationId;
						
					}
					
					if(DATA_CACHE.showSalaryEntries === true) {
						
						delete queryObj.location;
						
						if(setup.hasOwnProperty('report')) {
							queryObj.is_salary = 'yes';
						}
						
					} 

					// Check if no report
					if(!setup.hasOwnProperty('report')) {
						
						// Load initial set of entries
					    sb.data.db.obj.getWhere('time_entries', queryObj, function(entries) {
						    
						    // Remove all salary entries from database if any
						    var salary_entries_found = [];
						    var salary_entries_ids = [];
						    
						    salary_entries_found = _.where(entries, {is_salary: 'yes'});

						    if(!_.isEmpty(salary_entries_found)) {
							    
							    salary_entries_ids = _.pluck(salary_entries_found, 'id');
							    
						    }
	
						    sb.data.db.obj.erase('time_entries', salary_entries_ids, function(response) {
								
							    // Re-load entries from the database and re-generate salary entries
							    sb.data.db.obj.getWhere('time_entries', {
								    childObjs: 1,
									start_date: {
									    type: 'between', 
									    start: moment.unix(payperiodObj.start).startOf('day').unix(), 
									    end: payperiodObj.end.endOf('day').unix()
									},
									location: {
									    type: 'contains',
									    values: locationQuery
									}
							    }, function(entries) {
	
									var salary_payrollObjs = _.filter(setup.payrollObjs, function(o) {
								    	return o.pay_style === 'salary';
							    	});
								    var salary_entries_cache = [];
								    var temp_entries_cache = [];
	
									// Loop over salary payroll objs and generate temporary entries
								    _.each(salary_payrollObjs, function(payroll) {
									   	
									   	temp_entries_cache.push(
											{
												tips: 0,
												shift: 0,
												staff: payroll.staff,
												service: payroll.service,
												duration: 1,
												start_date: moment.unix(payperiodObj.start),
												end_date: moment(payperiodObj.end).clone().subtract(1, 'day'),
												location: 0,
												compensation: payroll.rate,
												is_salary: 'yes'
											}	
										);
									   	 
								    });
			
								    _.each(temp_entries_cache, function(tempEntry) {
								   	
									   	// Test entries array to see if salary entry already exists
										var test = _.find(entries, function(o) {
												return ( (o.service.id === tempEntry.service.id) && (o.staff.id === tempEntry.staff.id) && (o.is_salary === 'yes'));                                                   
											});
			
										if(test === undefined) {
											
											salary_entries_cache.push(tempEntry);
											
										}
									   	 
								    });
								    
								    if(!_.isEmpty(salary_entries_cache)) {
								    
									    sb.data.db.obj.create('time_entries', salary_entries_cache, function(newEntries) {
											
											var final_entry_list = [];
											
											final_entry_list = _.union(entries, newEntries);
											
											// This code block will link a payroll obj with an entry to be used throughout the table
											_.each(final_entry_list, function(entry) {
												
												_.each(setup.payrollObjs, function(payroll) {
													
													if(entry.service.id === payroll.service.id && entry.staff.id === payroll.staff.id) {
														
														entry.payrollObj = payroll;
														
													}
													
												});
												
											});
											
											if(DATA_CACHE.showSalaryEntries === true) {
							
												final_entry_list = _.where(final_entry_list, {is_salary: 'yes'});
												
											}
											
											callback(final_entry_list);
											
										}, 1);
									    
								    } else {
									    
									    _.each(setup.entries, function(entry) {
												
											_.each(setup.payrollObjs, function(payroll) {
												
												if(entry.service.id === payroll.service.id && entry.staff.id === payroll.staff.id) {
													
													entry.payrollObj = payroll;
													
												}
												
											});
											
										});
				
									    callback(setup.entries);
									    
								    }    
							    
							    });
							    
						    });
							
						});	
						
					} else {
						
						sb.data.db.obj.getWhere('time_entries', queryObj, function(entries) {

							entries = _.filter(entries, function(o) {
								
								if(o.is_salary === 'yes') {
									
									if(o.cycle_type === report.cycle_type) {
										return o;
									}
									
								} else {
									return o;
								} 
								
							});

							callback(entries);
							
						});
						
					}
									    
			    }
			    
			    function build_biWeekly_breakdown(setup) {
				    
				    // setup.entries --> list of entries
				    // setup.id --> staff id
				    // setup.compact
				    // setup.report
				    
				    var week_1_total_hrs = 0;
				    var week_2_total_hrs = 0;
				    var total_duration = 0;
				    var total_tips = 0;
				    var total_pay = 0;
					
					if(!setup.hasOwnProperty('compact') || setup.compact === false) {
						
						dom.wrapper.table.makeRow('bodyRowA'+setup.id, [], {style: 'background-color: #f4efef;'});
						dom.wrapper.table.makeRow('bodyRowB'+setup.id, [], {style: 'background-color: #f4efef;'});
						dom.wrapper.table.makeRow('bodyRowC'+setup.id, [], {style: 'background-color: #f4efef;'});
						
						dom.wrapper.table.body['bodyRowA'+setup.id].end_time.makeNode('cont', 'div', {text: '<strong>Week 1 Total</strong>', css: 'pull-right'});
						dom.wrapper.table.body['bodyRowB'+setup.id].end_time.makeNode('cont', 'div', {text: '<strong>Week 2 Total</strong>', css: 'pull-right'});
						dom.wrapper.table.body['bodyRowC'+setup.id].end_time.makeNode('cont', 'div', {text: '<strong>Total</strong>', css: 'pull-right'});	
						
					}
		    
				    _.each(setup.entries, function(entry) {
					    
					    if(!setup.hasOwnProperty('report')) {
						    
							if(entry.is_salary !== 'yes') {
							
								if(moment(entry.start_date).isBetween(moment.unix(payperiodObj.start), moment(payperiodObj.start).clone().add(6, 'day'))) {
									
									week_1_total_hrs += entry.duration;
									
								} else {
									
									week_2_total_hrs += entry.duration;
									
								}
								
								total_tips += entry.tips;
								
								if(entry.hasOwnProperty('payrollObj')) {
									
									total_pay += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									total_pay += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
								
							} else {
								
								total_pay = calc_yearlySalary({
									entry: entry,
									payperiodObj: payperiodObj
								});
	
							}
						    
					    } else {
						    
						    if(entry.is_salary !== 'yes') {
							 
								if(moment(entry.start_date).isBetween(moment.unix(payperiodObj.start), moment(payperiodObj.start).clone().add(6, 'day'))) {
							    
								    week_1_total_hrs += entry.duration;
								    
							    } else {
								    
								    week_2_total_hrs += entry.duration;
								    
							    }
							    
							    total_tips += entry.tips;    
							    
						    }
						    
						    total_pay += entry.compensation;
						    
					    }
					    
					    total_pay = parseInt(total_pay);     
					    
				    });
				    
				    if(!setup.hasOwnProperty('compact') || setup.compact === false) {
					    
						// Duration
					    dom.wrapper.table.body['bodyRowA'+setup.id].duration.makeNode('cont', 'div', {text: (week_1_total_hrs/60).toFixed(2).toString(), css: 'text-center'});
					    dom.wrapper.table.body['bodyRowB'+setup.id].duration.makeNode('cont', 'div', {text: (week_2_total_hrs/60).toFixed(2).toString(), css: 'text-center'});
					    dom.wrapper.table.body['bodyRowC'+setup.id].duration.makeNode('cont', 'div', {text: ((week_1_total_hrs + week_2_total_hrs)/60).toFixed(2).toString(), css: 'text-center'});
					    
					    // Tips
					    dom.wrapper.table.body['bodyRowC'+setup.id].tips.makeNode('cont', 'div', {text: '$' + (total_tips/100).formatMoney(), css: 'text-center'});
						
						// Overtime
						dom.wrapper.table.body['bodyRowC'+setup.id].overtime.makeNode('cont', 'div', {
							text: '$' + (calc_overTime({
							    entries: setup.entries
						    })/100).formatMoney(), 
							css: 'text-center'
						});
						
						// Compensation
						dom.wrapper.table.body['bodyRowC'+setup.id].total.makeNode('cont', 'div', {text: '$' + (total_pay/100).formatMoney(), css: 'text-center'});    
					    
				    }
				    
				    total_report_payroll += calc_overTime({
					    entries: setup.entries
				    });
				    
				    total_report_payroll += parseInt(total_pay);
				    
			    }
			    
			    function build_weekly_breakdown(setup) {
				    
				    // setup.entries --> list of entries
				    // setup.id --> staff id
				    // setup.report
				    
				    var total_pay = 0;
					var tip_total = 0;
					var total_duration = 0;
					
					if(!setup.hasOwnProperty('compact') || setup.compact === false) {
				    
				    	dom.wrapper.table.makeRow('bodyRowA'+setup.id, [], {style: 'background-color: #f4efef;'});
					
						dom.wrapper.table.body['bodyRowA'+setup.id].end_time.makeNode('cont', 'div', {text: '<strong>Total</strong>', css: 'pull-right'});
					
					}
					
					_.each(setup.entries, function(entry) {
						
						if(!setup.hasOwnProperty('report')) {
							
							if(entry.is_salary !== 'yes') {
							
								total_duration += entry.duration;
								tip_total += entry.tips;
								
								if(entry.hasOwnProperty('payrollObj')) {
									
									total_pay += check_billingType({
										entry: entry,
										payroll: entry.payrollObj
									}).total;
									
								} else {
									
									total_pay += check_billingType({
										entry: entry,
										service: entry.service
									}).total;
									
								}
									
							} else {
								
								total_pay += calc_yearlySalary({
									entry: entry,
									payperiodObj: payperiodObj
								});
								
							}	
							
						} else {
							
							if(entry.is_salary !== 'yes') {
								
								total_duration += entry.duration;
								tip_total += entry.tips;
								
							}
							
							total_pay += entry.compensation;
							
						}
						
						total_pay = parseInt(total_pay);

					});
					
					if(!setup.hasOwnProperty('compact') || setup.compact === false) {
						
						// Duration
						dom.wrapper.table.body['bodyRowA'+setup.id].duration.makeNode('cont', 'div', {text: (total_duration/60).toFixed(2), css: 'text-center'});
					    
					    // Tips
					    dom.wrapper.table.body['bodyRowA'+setup.id].tips.makeNode('cont', 'div', {text: '$' + (tip_total/100).formatMoney(), css: 'text-center'});
					    
					    // Overtime
						dom.wrapper.table.body['bodyRowA'+setup.id].overtime.makeNode('cont', 'div', {
							text: '$' + (calc_overTime({
							    entries: setup.entries
						    })/100).formatMoney(), 
							css: 'text-center'
						});
					    
					    // Total
					    dom.wrapper.table.body['bodyRowA'+setup.id].total.makeNode('cont', 'div', {text: '$' + (total_pay/100).formatMoney(), css: 'text-center'});
						
						
					}
				    
				    total_report_payroll += parseInt(total_pay);
				    
			    }
			    
			    function build_jobTypeBreakDown(setup) {
				    
				    // setup.entries
				    // setup.id
				    // setup.report
				    
				    var grouped_entries = _.groupBy(setup.entries, function(o) {
						return o.service.name;
				    });

					_.each(grouped_entries, function(entries, serviceName) {
						
						var total_duration = 0;
						var total_tips = 0;
						var total_pay = 0;
						
						_.each(entries, function(entry) {
							
							if(!setup.hasOwnProperty('report')) {
								
								if(entry.is_salary !== 'yes') {
								
									total_duration += entry.duration;
									total_tips += entry.tips;
									
									if(entry.hasOwnProperty('payrollObj')) {
									
										total_pay += check_billingType({
											entry: entry,
											payroll: entry.payrollObj
										}).total;
										
									} else {
										
										total_pay += check_billingType({
											entry: entry,
											service: entry.service
										}).total;
										
									}	
									
								} else {
									
									total_pay += calc_yearlySalary({
										entry: entry,
										payperiodObj: payperiodObj
									});
								}	
								
							} else {
								
								if(entry.is_salary !== 'yes') {
									
									total_duration += entry.duration;
									total_tips += entry.tips;
									
								}
								
								total_pay += entry.compensation;
								
							}
							
							total_pay = parseInt(total_pay);
							
						});
						
						dom.wrapper.table.makeRow('jobTypeRow'+setup.id+serviceName, [], {style: 'background-color: lightgrey;'});
						
						// Job type name
						dom.wrapper.table.body['jobTypeRow'+setup.id+serviceName].end_time.makeNode('cont', 'div', {
							text: '<i>'+serviceName+'</i>',
							css: 'pull-right'
						});
						
						// Job type duration
						dom.wrapper.table.body['jobTypeRow'+setup.id+serviceName].duration.makeNode('cont', 'div', {
							text: (total_duration/60).toFixed(2),
							css: 'text-center'
						});
						
						// Job type tips
						dom.wrapper.table.body['jobTypeRow'+setup.id+serviceName].tips.makeNode('cont', 'div', {
							text: '$' + (total_tips/100).formatMoney(),
							css: 'text-center'
						});
						
						// Job type total
						dom.wrapper.table.body['jobTypeRow'+setup.id+serviceName].total.makeNode('cont', 'div', {
							text: '$' + (total_pay/100).formatMoney(),
							css: 'text-center'
						});
						
					});
				    
			    }
			    
			    function update_payrollObjs(modalDom, entry) {

					modalDom.body.empty();
					
					modalDom.body.makeNode('head', 'div', {css: 'ui grid'});
					modalDom.body.makeNode('lb_1', 'lineBreak', {spaces: 1});
					modalDom.body.makeNode('body', 'div', {});
					
					modalDom.body.head.makeNode('col1', 'div', {css: 'ui ten wide column'});
					modalDom.body.head.makeNode('col2', 'div', {css: 'ui six wide column'});
					
					modalDom.body.head.col1.makeNode('title', 'div', {text: '<h2>Edit Payroll<div class="ui sub header">For '+ entry.staff.fname + ' ' + entry.staff.lname +'</div></h2>', css: 'ui header'});
					modalDom.body.head.col2.makeNode('btn', 'div', {text: 'Close and update table data', css: 'ui green button right floated'}).notify('click', {
						type: 'payroll-run',
						data: {
							run: function(data) {
								
								update_payrollReportTable();
								
								modalDom.hide();
								
							}
						}
					}, sb.moduleId);
					
					modalDom.body.patch();
	
					build_jobTypes(modalDom.body.body, entry.staff);
					
					modalDom.show();
					
				}
			    
			    function build_updatePayroll_btn(setup) {
				    
				    // setup.entry
				    // setup.id
				    
				    dom.wrapper.table.body['bodyRow'+setup.entry.id+setup.id].actions.cont.makeNode('update', 'div', {
						text: '<span data-tooltip="Edit Rate" data-inverted=""><i class="dollar sign icon"></i></span>',
						css: 'ui green button'
					}).notify('click', {
						type: 'payroll-run',
						data: {
							run: function(data) {
								
								data.modal.listeners[0] = function() {
									
									update_payrollObjs(data.modal, setup.entry);
									
								};
								
								data.modal.listeners[0]();	
								
							},
							modal: UI_CACHE.modal
						}
					}, sb.moduleId);
				    
			    }
			    
			    function build_updateEntry_btn(setup) {
				    
				    // setup.entry
				    // setup.id
				    
				    dom.wrapper.table.body['bodyRow'+setup.entry.id+setup.id].actions.cont.makeNode('edit', 'div', {
					    text: '<span data-tooltip="Edit Entry" data-inverted=""><i class="edit icon"></i></span>',
					    css: 'ui orange button'
				    }).notify('click', {
					    type: 'payroll-run',
					    data: {
						    run: function(data) {
							    
							    data.modal.listeners[0] = function() {
									
									build_editEntry(data.modal, setup.entry);
									
								};
								
								data.modal.listeners[0]();
							    
						    },
						    modal: UI_CACHE.modal
					    }
				    }, sb.moduleId);
				    
			    }
			    
			    function delete_entry(entry, after) {
			                
			        sb.data.db.obj.erase('time_entries', entry.id, function(res) {
				       
				        if(res) {
					       
					    	after();  
					       
				        } else {
					        
					        throw 'Error: can not delete this object';
					        
				        }
				        
			        });
		            
	            }
			    
			    function build_editEntry(modalDom, entry) {

		            var formArgs = {
			            	location: {
				            	name: 'location',
				            	type: 'select',
				            	label: 'Location',
				            	options: [],
				            	value: 0
			            	},
			            	job_type: {
				            	name: 'job_type',
				            	type: 'select',
				            	label: 'Job Type',
				            	options: [],
				            	value: 0
			            	},
			            	start_date: {
				            	name: 'start_date',
				            	type: 'date',
				            	dateType: 'datetime',
				            	label: 'Start Time',
				            	value: moment(entry.start_date).format('M/D/YYYY h:m A')
			            	},
			            	end_date: {
				            	name: 'end_date',
				            	type: 'date',
				            	dateType: 'datetime',
				            	label: 'End Time',
				            	value: moment(entry.end_date).format('M/D/YYYY h:m A')
			            	},
			            	tips: {
				            	name: 'tips',
				            	type: 'usd',
				            	label: 'Tips',
				            	value: entry.tips
			            	}
		            	};
		            
		            function process_entryForm(form, entry, load, after) {
			            
			            var formData = form.process().fields;
	
			            if((formData.start_date.value.length === 0) || ((moment.unix(payperiodObj.start).startOf('day').unix() > moment(formData.start_date.value).unix()) || (payperiodObj.end.endOf('day').unix() < moment(formData.start_date.value).unix()))) {
	
				            sb.dom.alerts.alert(
								'Date fields can not be empty or outside of payperiod range.',
								'',
								'error'
							);
							
				            return;
				            
			            } else if((formData.end_date.value.length === 0) || ((moment.unix(payperiodObj.start).startOf('day').unix() > moment(formData.end_date.value).unix()) || (payperiodObj.end.endOf('day').unix() < moment(formData.end_date.value).unix()))) {
				            
				            sb.dom.alerts.alert(
								'Date fields can not be empty or outside of payperiod range.',
								'',
								'error'
							);
							
							return;
				            
			            }
			            
			            load();
	
			            entry.location = formData.location.value;
			            entry.service = formData.job_type.value;
			            entry.tips = formData.tips.value;
			            entry.start_date = formData.start_date.value;
			            entry.end_date = formData.end_date.value;
			            entry.duration = moment(formData.end_date.value).diff(moment(formData.start_date.value), 'minutes');
			            
			            if(entry.duration === 0) {
				            
				            entry.duration = 1;
				            
			            }
			            
			            sb.data.db.obj.update('time_entries', entry, function(updated) {
				            
				            after(updated);
				            
			            }, 1);
			            
		            }
		            
		            modalDom.body.empty();
					
					modalDom.body.makeNode('head', 'div', {css: 'ui grid'});
					modalDom.body.makeNode('lb_1', 'lineBreak', {spaces: 1});
					modalDom.body.makeNode('body', 'div', {css: 'ui basic segment'});
					
					modalDom.body.head.makeNode('col1', 'div', {css: 'ten wide column'});
					modalDom.body.head.makeNode('col2', 'div', {css: 'six wide column'});
					
					modalDom.body.head.col1.makeNode('title', 'div', {text: '<h2><i class="edit icon"></i> Edit time entry <div class="ui sub header">For '+ entry.staff.fname + ' ' + entry.staff.lname +'</div></h2>', css: 'ui header'});
					
					modalDom.body.head.col2.makeNode('btnGrp', 'div', {});
					
					modalDom.body.head.col2.btnGrp.makeNode('close', 'div', {css: 'ui small red inverted button right floated', text: 'Close'}).notify('click', {
						type: 'payroll-run',
						data: {
							run: function(data) {
								
								modalDom.hide();
								
							}
						}
					}, sb.moduleId);
					
					modalDom.body.head.col2.btnGrp.makeNode('delete', 'div', {css: 'ui small red button right floated loading', text: 'loading'});
					modalDom.body.head.col2.btnGrp.makeNode('save', 'div', {css: 'ui small green button right floated loading', text: 'loading'});
						
					display_loader({
						mainDom: modalDom.body.body,
						text: 'Fetching location data ...'
					});
					
					modalDom.body.body.patch();
	
					sb.data.db.obj.getWhere('staff_base', {id: {
						type: 'or',
						values: entry.staff.base
					}, childObjs: 1}, function(locations) {
	
						_.each(locations, function(location) {
							
							formArgs.location.options.push({
								name: location.name,
								value: location.id
							});
							
						});
						
						formArgs.location.options = _.sortBy(formArgs.location.options, function(obj) {
							return obj.name;
						});
						
						formArgs.location.value = entry.location.id;
						
						modalDom.body.body.empty();
					
						display_loader({
							mainDom: modalDom.body.body,
							text: 'Fetching job type data ...'
						});
						
						modalDom.body.body.patch();
						
						sb.data.db.obj.getWhere('inventory_service', {id: {
							type: 'or',
							values: entry.staff.service
						}, childObjs: 1}, function(jobTypes) {
							
							_.each(jobTypes, function(jobType) {
							
								formArgs.job_type.options.push({
									name: jobType.name,
									value: jobType.id
								});
								
							});
							
							formArgs.job_type.options = _.sortBy(formArgs.job_type.options, function(obj) {
								return obj.name;
							});
							
							formArgs.job_type.value = entry.service.id;
							
							modalDom.body.head.col2.btnGrp.makeNode('delete', 'div', {css: 'ui small red button right floated', text: 'Delete'}).notify('click', {
								type: 'payroll-run',
								data: {
									run: function(data) {
										
										modalDom.body.empty();
										
										modalDom.body.makeNode('message', 'div', {text: '<h2>Are you sure?<div class="ui sub header">Delete this time entry?</div></h2>', css: 'ui header text-center'});
										modalDom.body.makeNode('btnGrp', 'div', {css: 'text-center'});
										
										modalDom.body.btnGrp.makeNode('yes', 'div', {text: 'Yes', css: 'ui small green button'}).notify('click', {
											type: 'payroll-run',
											data: {
												run: function(data) {
													
													modalDom.body.empty();
													
													display_loader({
														mainDom: modalDom.body,
														text: 'Deleting time entry ...'
													});
													
													modalDom.body.patch();
													
													delete_entry(entry, function() {
														
														update_payrollReportTable();
														
														modalDom.hide();
														
													});
													
												}
											}
										}, sb.moduleId);
										
										modalDom.body.btnGrp.makeNode('no', 'div', {text: 'No', css: 'ui small red button'}).notify('click', {
											type: 'payroll-run',
											data: {
												run: function(data) {
													
													build_editEntry(modalDom, entry);
													
												}
											}
										}, sb.moduleId);
										
										modalDom.body.patch();
										
									}
								}
							}, sb.moduleId);
							
							modalDom.body.head.col2.btnGrp.makeNode('save', 'div', {css: 'ui small green button right floated', text: 'Save'}).notify('click', {
								type: 'payroll-run',
								data: {
									run: function(data) {
										
										process_entryForm(modalDom.body.body.form, entry, function() {
											
											modalDom.body.head.col2.btnGrp.makeNode('close', 'div', {css: 'ui small red inverted button right floated loading', text: 'Close'});
											modalDom.body.head.col2.btnGrp.makeNode('delete', 'div', {css: 'ui small red button right floated loading', text: 'loading'});
											modalDom.body.head.col2.btnGrp.makeNode('save', 'div', {css: 'ui small green button right floated loading', text: 'loading'});
											
											modalDom.body.body.empty();
											
											display_loader({
												mainDom: modalDom.body.body,
												text: 'Updating entry data ...'
											});
											
											modalDom.body.head.patch();
											modalDom.body.body.patch();
											
										}, function(updated) {
											
											update_payrollReportTable();
											
											modalDom.hide();
											
										});
										
									}
								}
							}, sb.moduleId);
							
							modalDom.body.body.empty();
							
							modalDom.body.body.makeNode('form', 'form', formArgs);
							
							modalDom.patch();
							
						});
						
					});
					              
	                modalDom.patch();
	                modalDom.show();
		            
	            }
			    
			    function build_viewEntry_btn(setup) {
				    
				    // setup.entry
				    // setup.id
				    
				    dom.wrapper.table.body['bodyRow'+setup.entry.id+setup.id].actions.cont.makeNode('view', 'div', {
					    text: '<span data-tooltip="View Entry" data-inverted=""><i class="eye icon"></i></span>',
					    css: 'ui blue button'
				    }).notify('click', {
					    type: 'payroll-run',
					    data: {
						    run: function(data) {
							    
							    data.modal.listeners[0] = function() {
								    
								    data.modal.body.empty();
								    data.modal.show();
									
									sb.notify({
										type: 'timeClockReport-start-single-entry',
										data: {
											domObj: data.modal.body,
											entry: setup.entry,
											onUpdate: update_payrollReportTable,
											onDelete: update_payrollReportTable
										}
									});
									
								};
								
								data.modal.listeners[0]();
							    
						    },
						    modal: UI_CACHE.modal
					    }
				    }, sb.moduleId);
				    
			    }
			    
			    function build_correctionEntry_btn(setup) {
				    
				    // setup.entry
				    // setup.id
				    // setup.report
				    
				    // Correct entry
					dom.wrapper.table.body['bodyRow'+setup.entry.id+setup.id].actions.cont.makeNode('correct', 'div', {
						text: '<span data-tooltip="Create a correction entry" data-inverted=""><i class="eraser icon"></i></span>',
						css: 'ui green button inverted'
					}).notify('click', {
						type: 'payroll-run',
						data: {
							run: function(data) {
								
								data.modal.listeners[0] = function() {
									
									var build_correctedEntry_setup = {
											entry: setup.entry
										};
									
									if(setup.hasOwnProperty('report')) {
										build_correctedEntry_setup.report = setup.report;
									}
								    
								    build_correctedEntry(data.modal, build_correctedEntry_setup);
								    
								    data.modal.show();
									
																		
								};
								
								data.modal.listeners[0]();	
								
							},
							modal: UI_CACHE.modal
						}
					}, sb.moduleId);

			    }
			    
			    function build_correctionNote(modalDom, setup) {
				    
				    // setup.entry

				    modalDom.body.empty();
				    
				    modalDom.body.makeNode('wrapper', 'div', {});
				    
				    modalDom.body.wrapper.makeNode('head', 'div', {});
				    modalDom.body.wrapper.makeNode('lb_1', 'lineBreak', {
					    spaces: 1
				    });
				    modalDom.body.wrapper.makeNode('body', 'div', {});
				    
				    modalDom.body.wrapper.head.makeNode('title', 'div', {
					   text: '<h1>'+ setup.entry.staff.fname + ' ' + setup.entry.staff.lname + ' <small class="text-muted">for</small> ' + setup.entry.service.name + ' <small class="text-muted">at</small> ' + setup.entry.location.name + '<div class="ui sub header">Correction entry note</div></h1>',
					   css: 'ui header' 
				    });
				    
				    modalDom.body.wrapper.body.makeNode('note', 'div', {
					   text: setup.entry.note,
					   css: 'ui message' 
				    });
				    
				    modalDom.body.wrapper.body.makeNode('btnGrp', 'div', {});
				    
				    modalDom.body.wrapper.body.btnGrp.makeNode('close', 'div', {
						text: 'Close',
						css: 'ui red button right floated' 
				    }).notify('click', {
					    type: 'payroll-run',
					    data: {
						    run: function(data) {
							    
							    modalDom.hide();
							    
						    }
					    }
				    }, sb.moduleId);
				    
				    modalDom.patch();
				    modalDom.show();
				    
			    }
			    
			    function display_entryPayroll(setup) {
												
					// This function is used on entries related to a payment report object only
					// setup.entry
					
					switch(setup.entry.billing_type) {
						
						case 'hourly':
							
							return {
								text: '$' + (setup.entry.hourly_rate/100).formatMoney() + ' per hour',
								value: (setup.entry.compensation/100).formatMoney()
							};
								
							break;
							
						case 'flat':
							
							return {
								text: '$' + (setup.entry.flat_rate/100).formatMoney() + ' flat fee',
								value: (setup.entry.compensation/100).formatMoney()
							};
							
							break;
							
						case 'flat_and_hourly':
							
							return {
								text: '$' + (setup.entry.flat_rate/100).formatMoney()	+ ' flat fee ('+ (setup.entry.hourly_rate/100).formatMoney() +' per hour after '+ setup.entry.max_flat_hours +' hours)',
								value: (setup.entry.compensation/100).formatMoney()
							};
							
							break;
							
						case 'non-billable':
							
							return {
								text: 'Non-billable',
								value: 0	
							};
							
							break;
							
						default:
							return {
								text: '',
								value: 0
							};
						
					}
					
				}
			    
			    function return_entryBillingType(setup) {
				    
				    // setup.entry
				    
				    var entry = setup.entry;
				    
				    if(entry.hasOwnProperty('payrollObj')) {
					    
					    return entry.payrollObj.pay_style;
					    
				    } else {
					    
					    switch(entry.service.price_type) {
						    
						    case '0':
						    case 'flat':
						    	
						    	return 'flat';
						    	
						    	break;
						    	
						    case '1':
						    case 'hourly':
						    	
						    	return 'hourly';
						    	
						    	break;
						    	
						    case '2':
						    case 'non-billable':
						    	
						    	return 'non-billable';
						    	
						    	break;
						    	
						    case '3':
						    case 'flat_and_hourly':
						    	
						    	return 'flat_and_hourly';
						    	
						    	break;
						    	
						    default:
						    	return;
						    
					    }
					    
				    }
				    
			    }
			    
			    display_loader({
					mainDom: dom,
					text: 'Fetching existing payroll data...'
			    });
			    
			    sb.data.db.obj.getAll('payroll', function(payrollObjs) {
				    
				    var generate_salaryEntries_setup = {
					    	entries: setup.entries,
							payrollObjs: payrollObjs,
							state: setup.state
				    	};
				    
				    if(setup.hasOwnProperty('report')) {
					    generate_salaryEntries_setup.report = setup.report;
				    }

					// This function will generate salary entries and save them to the database if they do not already exist
					generate_salaryEntries(generate_salaryEntries_setup, function(entries) {

						dom.empty();
					
						dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
						
						dom.wrapper.makeNode('table', 'table', {
							tableCss: 'ui selectable celled table',
							columns: {
								employee: 'Employee',
								location: 'Location',
								job_type: 'Job Type',
								start_time: 'Start Time',
								end_time: 'End Time',
								duration: 'Duration',
								rate: 'Rate',
								tips: 'Tips',
								overtime: 'Overtime',
								total: 'Total',
								actions: ''
							}
						});

						// Check if time entries list is empty
						if(!_.isEmpty(entries)) {
							
							DATA_CACHE.entries = entries;
							
							// If false, begin by grouping entries list
							entries = _.groupBy(entries, function(o) {
								return o.staff.id;	
							});
							
							DATA_CACHE.grouped_entryList = entries;
							
							// Loop over grouped entries grouped object
							_.each(entries, function(entriesList, id) {
								
								_.each(entriesList, function(entry, index) {

									dom.wrapper.table.makeRow('bodyRow'+entry.id+id, [], {});
									
									// Staff Name
									if(!setup.hasOwnProperty('compact') || setup.compact === false) {
										
										dom.wrapper.table.body['bodyRow'+entry.id+id].employee.makeNode('cont', 'div', {
											text: entry.staff.fname + ' ' + entry.staff.lname,
											css: 'text-center'
										});	
										
									} else {
										
										if(index === 0) {
											
											dom.wrapper.table.body['bodyRow'+entry.id+id].employee.makeNode('cont', 'div', {
												text: '<strong>'+entry.staff.fname + ' ' + entry.staff.lname+'</strong>',
												css: 'text-center'
											});
											
										}
										
									}	
									
									// Location Name
									if(entry.location !== null) {
										
										dom.wrapper.table.body['bodyRow'+entry.id+id].location.makeNode('cont', 'div', {
											text: entry.location.name,
											css: 'text-center'
										});	
										
									} else {
										
										dom.wrapper.table.body['bodyRow'+entry.id+id].location.makeNode('cont', 'div', {
											text: '---',
											css: 'text-center'
										});	
										
									}
									
									// Job Type Name
									dom.wrapper.table.body['bodyRow'+entry.id+id].job_type.makeNode('cont', 'div', {
										text: entry.service.name,
										css: 'text-center'
									});
									
									// Start time
									dom.wrapper.table.body['bodyRow'+entry.id+id].start_time.makeNode('cont', 'div', {
										text: moment(entry.start_date).format('M/D/YYYY h:mm A'),
										css: 'text-center'
									});
									
									// End time
									dom.wrapper.table.body['bodyRow'+entry.id+id].end_time.makeNode('cont', 'div', {
										text: moment(entry.end_date).format('M/D/YYYY h:mm A'),
										css: 'text-center'
									});
									
									// Actions
									dom.wrapper.table.body['bodyRow'+entry.id+id].actions.makeNode('cont', 'div', {
										css: 'ui small icon buttons'
									});
									
									if(entry.is_salary === 'yes') {
										
										// Duration
										dom.wrapper.table.body['bodyRow'+entry.id+id].duration.makeNode('cont', 'div', {
											text: '---',
											css: 'text-center'
										});
										
										// Rate
										dom.wrapper.table.body['bodyRow'+entry.id+id].rate.makeNode('cont', 'div', {
											text: '<div class="ui olive label">Salary</div>',
											css: 'text-center'
										});
										
										// Tips
										dom.wrapper.table.body['bodyRow'+entry.id+id].tips.makeNode('cont', 'div', {
											text: '---',
											css: 'text-center'
										});
										
										// Overtime
										dom.wrapper.table.body['bodyRow'+entry.id+id].overtime.makeNode('cont', 'div', {
											text: '---',
											css: 'text-center'
										});
										
									} else {
										
										// Duration
										dom.wrapper.table.body['bodyRow'+entry.id+id].duration.makeNode('cont', 'div', {
											text: parseFloat((entry.duration / 60)).toFixed(2),
											css: 'text-center'
										});
										
										// Tips
										dom.wrapper.table.body['bodyRow'+entry.id+id].tips.makeNode('cont', 'div', {
											text: '$' + (entry.tips/100).formatMoney(),
											css: 'text-center'
										});
										
									}
									
									// Check for a payment report
									if(!setup.hasOwnProperty('report')) {
										
										// Check to see if entry is a salary entry
										if(entry.is_salary === 'yes') {
											
											// Total
											dom.wrapper.table.body['bodyRow'+entry.id+id].total.makeNode('cont', 'div', {
												text: '$' + (calc_yearlySalary({
													entry: entry,
													payperiodObj: payperiodObj
												})/100).formatMoney(),
												css: 'text-center'
											});
											
											if(entry.hasOwnProperty('payrollObj')) {
												
												build_updatePayroll_btn({
													entry: entry,
													id: id
												});	
												
											}
											
										} else {
											
											// Rate
											// Check to find entries with a payroll obj attached
											if(entry.hasOwnProperty('payrollObj')) {
												
												// If true, use it to display rate
												dom.wrapper.table.body['bodyRow'+entry.id+id].rate.makeNode('cont', 'div', {
													text: check_billingType({
														payroll: entry.payrollObj,
														entry: entry
													}).display,
													css: 'text-center'
												});
												
												// Total
												dom.wrapper.table.body['bodyRow'+entry.id+id].total.makeNode('cont', 'div', {
													text: '$' + (check_billingType({
														payroll: entry.payrollObj,
														entry: entry
													}).total/100).formatMoney(),
													css: 'text-center'
												});
												
												build_updatePayroll_btn({
													entry: entry,
													id: id
												});
												
											} else {
												
												// If false, use default service payroll
												dom.wrapper.table.body['bodyRow'+entry.id+id].rate.makeNode('cont', 'div', {
													text: check_billingType({
														service: entry.service,
														entry: entry
													}).display + '</br><div class="ui mini teal label">Default</div>',
													css: 'text-center'
												});
												
												// Total
												dom.wrapper.table.body['bodyRow'+entry.id+id].total.makeNode('cont', 'div', {
													text: '$' + (check_billingType({
														service: entry.service,
														entry: entry
													}).total/100).formatMoney(),
													css: 'text-center'
												});
												
											} 
												
											if(entry.is_correction !== 'yes') {
												
												build_updateEntry_btn({
													entry: entry,
													id: id
												});
												
												build_viewEntry_btn({
													entry: entry,
													id: id
												});
												
												// Correction entry
												if(return_entryBillingType({entry: entry}) === 'hourly') {
												
													build_correctionEntry_btn({
														entry: entry,
														id: id
													});
													
												}
												
											}		
											
										}
										
									} else {
										
										// Check to see if entry is a salary entry
										if(entry.is_salary === 'yes') {
											
											// Total
											dom.wrapper.table.body['bodyRow'+entry.id+id].total.makeNode('cont', 'div', {
												text: '$' + (entry.compensation/100).formatMoney(),
												css: 'text-center'
											});
											
										} else {
											
											if(entry.default_payroll === 'yes') {
												
												// Rate
												dom.wrapper.table.body['bodyRow'+entry.id+id].rate.makeNode('cont', 'div', {
													text: display_entryPayroll({
														entry: entry
													}).text + '</br><div class="ui mini teal label">Default</div>',
													css: 'text-center'
												});
												
											} else {
												
												// Rate
												dom.wrapper.table.body['bodyRow'+entry.id+id].rate.makeNode('cont', 'div', {
													text: display_entryPayroll({
														entry: entry
													}).text,
													css: 'text-center'
												});
												
											}
											
											if(entry.shift !== null) {
												
												build_viewEntry_btn({
													entry: entry,
													id: id
												});
												
											}
											
											if(entry.is_correction !== 'yes' && entry.billing_type === 'hourly') {
													
												build_correctionEntry_btn({
													entry: entry,
													id: id,
													report: setup.report
												});	
												
											}
											
											// Total
											dom.wrapper.table.body['bodyRow'+entry.id+id].total.makeNode('cont', 'div', {
												text: '$' + (entry.compensation/100).formatMoney(),
												css: 'text-center'
											});
											
										}
										
									}
									
									// Checking for correction entries
									if(entry.is_correction === 'yes') {
										
										dom.wrapper.table.body['bodyRow'+entry.id+id].employee.makeNode('cont', 'div', {
											text: entry.staff.fname + ' ' + entry.staff.lname + '</br><div class="ui mini red label">Correction</div>',
											css: 'text-center'
										});
										
										if(entry.hasOwnProperty('payrollObj') && !setup.hasOwnProperty('report')) {
										
											build_updatePayroll_btn({
												entry: entry,
												id: id
											});	
											
										}
										
										dom.wrapper.table.body['bodyRow'+entry.id+id].actions.cont.makeNode('delete', 'div', {
											text: '<span data-tooltip="Delete Entry" data-inverted=""><i class="trash icon"></i></span>',
											css: 'ui red button'
										}).notify('click', {
											type: 'payroll-run',
											data: {
												run: function(data) {
													
													delete_entry(data.entry, function() {
														
														update_payrollReportTable();
														
													});
													
												},
												entry: entry
											}
										}, sb.moduleId);
										
										if(!_.isEmpty(entry.note)) {
											
											dom.wrapper.table.body['bodyRow'+entry.id+id].actions.cont.makeNode('note', 'div', {
												text: '<span data-tooltip="Correction note" data-inverted=""><i class="sticky note outline icon"></i></span>',
												css: 'ui olive button'
											}).notify('click', {
												type: 'payroll-run',
												data: {
													run: function(data) {
														
														data.modal.listeners[0] = function() {
									
															build_correctionNote(data.modal, {
																entry: data.entry
															});
															
														};
														
														data.modal.listeners[0]();
														
													},
													entry: entry,
													modal: UI_CACHE.modal
												}
											}, sb.moduleId);	
											
										}
										
									}
									
									if( (index + 1) === entriesList.length) {
										
										if(setup.hasOwnProperty('jobTypes') && setup.jobTypes === true) {
											
											build_jobTypeBreakDown({
												entries: entriesList,
												id: id
											});
											
										}
										
										// Totals breakdown 
										switch(payperiodObj.cycle_type) {
											
											case 'Weekly':
											case '1st and 15th':
											case '15th and last day':
											case 'Monthly':
											
												var obj = {
														entries: entriesList,
														id: id
													};
												
												if(setup.hasOwnProperty('compact')) {
													
													obj.compact = setup.compact;
													
												}
												
												if(setup.hasOwnProperty('report')) {
													
													obj.report = setup.report;
													
												}
											
												build_weekly_breakdown(obj);
																							
												break;
												
											case 'Bi Weekly':
											
												var obj = {
														entries: entriesList,
														id: id
													};
												
												if(setup.hasOwnProperty('compact')) {
													
													obj.compact = setup.compact;
													
												}
												
												if(setup.hasOwnProperty('report')) {
													
													obj.report = setup.report;
													
												}
											
												build_biWeekly_breakdown(obj);

												break;
											
										}	
										
									}
										
								});
								
							});
							
						} else {
							
							// If true, display text
							
							dom.wrapper.makeNode('lb_1', 'lineBreak', {
								spaces: 1
							});
							
							dom.wrapper.makeNode('no_data', 'div', {
								text: '<h3>No Data</h3>',
								css: 'text-center'
							});
							
						}
						
						var reportInfo_setup = {};

						if(setup.hasOwnProperty('report')) {
							
							reportInfo_setup.report = report;
							
						} else {
							
							reportInfo_setup.totalPayroll = total_report_payroll;
							
						}
						
						build_reportInfo.display_totalPayroll(reportInfo_setup);
						
						dom.patch();

						DATA_CACHE.total_payroll = total_report_payroll;
						
					});
				    
			    }, 1);
			    
		    }
		    
		    function build_correctedEntry(dom, setup) {
			    
			    // dom --> modal dom, this function builds in the modal body
			    // setup.entry --> the entry that needs to be corrected (needs to be a true time clock entry)
			    // setup.report
			    
			    var formArgs = {
				    	staff: {
					    	name: 'staff',
					    	type: 'select',
					    	label: 'Staff',
					    	options: [],
					    	value: setup.entry.staff.id
					    	/*
change: function(formArgs, value) {

						    	if(value !== '0') {
							    	
							    	get_locations(value, function(locations) {
								    	
								    	var staff_locations = [];
								    	
								    	_.each(locations, function(location) {
									    	
									    	staff_locations.push({
										    	name: location.name,
										    	value: location.id
									    	});
									    	
								    	});
							    	
								    	dom.body.wrapper.body.form.locations.update({
											type: 'select',
											options: staff_locations
										});
										
										get_jobTypes(value, function(jobs) {
											
											var staff_jobTypes = [];
											
											_.each(jobs, function(job) {
												
												staff_jobTypes.push({
													name: job.name,
													value: job.id
												});
												
											});
											
											dom.body.wrapper.body.form.job_types.update({
												type: 'select',
												options: staff_jobTypes
											});
											
											dom.body.wrapper.body.form.duration.update({
												type: 'number'
											});
											
											dom.body.wrapper.body.form.note.update({
												type: 'textbox'
											});
											
											dom.body.wrapper.body.btnGrp.makeNode('create', 'div', {
											    text: 'Create',
											    css: 'ui green button right floated'
										    }).notify('click', {
											    type: 'payroll-run',
											    data: {
												    run: function(data) {
													    
													    process_form(data.form, function() {
														    
														    data.dom.body.wrapper.body.empty();
														    
														    display_loader({
															   mainDom: data.dom.body.wrapper.body,
															   text: 'Processing form data...' 
														    });
														    
													    }, function(entry) {

														    update_payrollReportTable();
														    
														    dom.hide();
														    
													    });
													    
												    },
												    form: dom.body.wrapper.body.form,
												    dom: dom
											    }
										    }, sb.moduleId);
										    
										    dom.body.wrapper.body.btnGrp.patch();
											
										});
								    	
							    	});	
							    	
						    	} else {
							    	
							    	dom.body.wrapper.body.form.locations.update({
										type: 'hidden',
										options: []
									});
									
									dom.body.wrapper.body.form.job_types.update({
										type: 'hidden',
										options: []
									});
									
									dom.body.wrapper.body.form.duration.update({
										type: 'hidden'
									});
									
									dom.body.wrapper.body.form.note.update({
										type: 'hidden'
									});
									
									delete dom.body.wrapper.body.btnGrp.create;
									
									dom.body.wrapper.body.btnGrp.patch();
							    	
						    	}
						    	
					    	}
*/
				    	},
				    	locations: {
					    	name: 'locations',
					    	type: 'hidden',
					    	label: 'Locations',
					    	options: [],
					    	value: setup.entry.location.id
				    	},
				    	job_types: {
					    	name: 'job_types',
					    	type: 'select',
					    	label: 'Job Types',
					    	options: [],
					    	value: setup.entry.service.id
				    	},
				    	start_date: {
					    	name: 'start_date',
					    	type: 'date',
					    	dateType: 'datetime',
					    	label: 'Start Date',
					    	value: moment(setup.entry.start_date)
				    	},
				    	end_date: {
					    	name: 'end_date',
					    	type: 'date',
					    	dateType: 'datetime',
					    	label: 'End Date',
					    	value: moment(setup.entry.end_date)
				    	},
				    	note: {
					    	name: 'note',
					    	type: 'textbox',
					    	label: 'Reason for correction',
					    	placeholder: '(optional)',
					    	value: ''
				    	}
			    	};
			    
			    // Deprecated	
			    function get_locations(staffId, callback) {
				    
				    sb.data.db.obj.getWhere('users', {id: parseInt(staffId), childObjs: 1}, function(staffList) {
					    
					    var locations = [];
					    
					    _.each(staffList[0].locations, function(location) {

						    locations.push(location.id);
						    
					    });
					    
					    sb.data.db.obj.getWhere('staff_base', {id: {
						    type: 'contains',
						    values: locations
					    }, childObjs: 1}, function(locationsList) {
						    
						    callback(locationsList);
						    
					    });
					    
				    });
				
				    
			    }
			    
			    function get_jobTypes(staffId, callback) {
				    
				    sb.data.db.obj.getWhere('users', {id: parseInt(staffId), childObjs: 1}, function(staffList) {
					    
					    var jobTypes = [];
					    
					    _.each(staffList[0].service, function(job) {

						    jobTypes.push(job.id);
						    
					    });
					    
					    sb.data.db.obj.getWhere('inventory_service', {id: {
						    type: 'contains',
						    values: jobTypes
					    }, childObjs: 1}, function(jobList) {
						    
						    callback(jobList);
						    
					    });
					    
				    });
				    
			    }
			    
			    function process_form(form, loading, after) {

				    var formData = form.process().fields;
				    var newObj = {};
				    var duration = 1;
				    
				    loading();
				    
				    newObj.staff = parseInt(formData.staff.value);
				    newObj.location = parseInt(formData.locations.value);
				    newObj.service = parseInt(formData.job_types.value);
				    newObj.duration = moment(formData.end_date.value).diff(moment(formData.start_date.value), 'minutes');
				    newObj.note = formData.note.value;
				    newObj.is_correction = 'yes';
				    newObj.billing_type = 'hourly';
				    
				    // If a payment report exists
				    if(setup.hasOwnProperty('report')) {

						newObj.hourly_rate = setup.entry.hourly_rate;
						newObj.default_payroll = setup.entry.default_payroll;
						newObj.compensation = +((newObj.duration/60) * setup.entry.hourly_rate); 

				    } else { 

						if(setup.entry.hasOwnProperty('payrollObj')) {
					    
							newObj.hourly_rate = setup.entry.payrollObj.hourly_rate;
							newObj.default_payroll = 'no';
							
							newObj.compensation = check_billingType({
								entry: setup.entry,
								payroll: setup.entry.payrollObj
							}).total;    
						    
					    } else {
						    
						    newObj.hourly_rate = setup.entry.service.rate;
						    newObj.default_payroll = 'yes';
						    
						    newObj.compensation = check_billingType({
								entry: setup.entry,
								service: setup.entry.service
							}).total;        
						    
					    }   
					    
				    }

				    newObj.start_date = formData.start_date.value;
				    newObj.end_date = formData.end_date.value;

					sb.data.db.obj.create('time_entries', newObj, function(newEntry) {

						after(newEntry);
						
					}, 1);
				    
			    }
			    
			    dom.body.empty();
			    
			    dom.body.makeNode('wrapper', 'div', {});
			    
			    dom.body.wrapper.makeNode('head', 'div', {});
			    dom.body.wrapper.makeNode('lb_1', 'lineBreak', {
				    spaces: 1
			    });
			    dom.body.wrapper.makeNode('body', 'div', {});
			    
			    dom.body.wrapper.head.makeNode('title', 'div', {
				    text: '<h1>Create a time entry <div class="ui sub header">You can create new entries to make corrections to payroll report</div></h1>',
				    css: 'ui header'
			    });
			    
			    display_loader({
					mainDom: dom.body.wrapper.body,
					text: 'Loading staff members...' 
			    });
			    
			    //sb.data.db.obj.getAll('users', function(usersList) {
					   
				   formArgs.staff.options.push({
					  name: setup.entry.staff.fname + ' ' + setup.entry.staff.lname,
					  value: setup.entry.staff.id 
				   });
				    
				    get_jobTypes(setup.entry.staff.id, function(jobTypes) {
					    
					    _.each(jobTypes, function(job) {
					   
						   formArgs.job_types.options.push({
							  name: job.name,
							  value: job.id 
						   });
						    
					    });
					    
					    formArgs.job_types.options = _.sortBy(formArgs.job_types.options, function(o) {
						   return o.name; 
					    });
					    
						dom.body.wrapper.body.empty();
				    
					    dom.body.wrapper.body.makeNode('form', 'form', formArgs);
					    dom.body.wrapper.body.makeNode('lb_1', 'lineBreak', {
						   spaces: 1 
					    });
					    dom.body.wrapper.body.makeNode('btnGrp', 'div', {});
					    
					    dom.body.wrapper.body.btnGrp.makeNode('create', 'div', {
						    text: 'Create',
						    css: 'ui green button right floated'
					    }).notify('click', {
						    type: 'payroll-run',
						    data: {
							    run: function(data) {
								    
								    process_form(data.form, function() {
									    
									    data.dom.body.wrapper.body.empty();
									    
									    display_loader({
										   mainDom: data.dom.body.wrapper.body,
										   text: 'Processing form data...' 
									    });
									    
								    }, function(entry) {

									    update_payrollReportTable();
									    
									    dom.hide();
									    
								    });
								    
							    },
							    form: dom.body.wrapper.body.form,
							    dom: dom
						    }
					    }, sb.moduleId);
					    
					    dom.body.wrapper.body.btnGrp.makeNode('close', 'div', {
						    text: 'Close',
						    css: 'ui red button right floated'
					    }).notify('click', {
						    type: 'payroll-run',
						    data: {
							    run: function(data) {
								    
								    dom.hide();
								    
							    }
						    }
					    }, sb.moduleId);
					    
					    dom.body.wrapper.body.patch(); 
					    
				    });
				    
			    //}, 1);
			    
			    dom.patch();
			    
		    }
		    
		    function call_reportsCrud(setup) {
			    
			    // setup.report --> payment report
			    // setup.payperiodObj --> payperiod info
			    // setup.compact --> switch between regular and compact views
			    // setup.jobTypes --> show job type breakdowns
			    // setup.state
			    
				comps.payroll_report.notify({
	                type: 'show-table',
	                data: {
	                    domObj: dom.wrapper.body,
	                    objectType: 'time_entries',
	                    childObjs: 1,
	                    navigation: false,
	                    rowSelection: false,
	                    rowLink: false,
	                    searchObjects: false,
	                    headerButtons: {
	                        reload: {
	                            name: 'Reload',
	                            css: 'pda-btn-blue',
	                            action: function() {}
	                        }
	                        /*
newEntry: {
								name: '<i class="fa fa-plus"></i> Create Entry',
								css: 'ui green button',
								domType: 'modal',
								action: function(bp, dom) {
									
									build_correctedEntry(dom, {});
									
								}
							}
*/
	                    },
	                    visibleCols: {},
	                    cells: {},
	                    custom: {
		                    dom: function(dom, entryList) {
			                    
			                    var obj = {};
			                    
			                    obj.entries = entryList;
			                    obj.state = setup.state;
			                    
			                    if(setup.hasOwnProperty('compact')) {
				                    obj.compact = setup.compact;
			                    }
			                    if(setup.hasOwnProperty('jobTypes')) {
				                    obj.jobTypes = setup.jobTypes;
			                    }
			                    if(setup.hasOwnProperty('payperiodObj')) {
				                    obj.payperiodObj = setup.payperiodObj;
			                    }
			                    if(setup.hasOwnProperty('report')) {
				                    obj.report = setup.report;
			                    }
			                    
			                    build_reportTable(dom, obj);
								
								dom.patch();
								
		                    }
	                    },
	                    filters: function(callback) {
		                    
		                    sb.data.db.obj.getAll('time_entries', function(entries) {
			                    
			                    var filterTypes = [];
			                    
			                    _.each(entries, function(entry){
														
									filterTypes.push({
										name: 'name',
										label: entry.staff.fname + ' ' + entry.staff.lname,
										value: entry.id,
										checked: true
									});
									
								});
								
								callback({
									type:{
										name: 'Staff',
										type: 'checkbox',
										field: 'name',
										options: filterTypes
									}
								});
			                    
		                    }, 1);
		                    
	                    },
	                    download: function(obj) {
		                    
		                    var location = '';
							
							_.each(DATA_CACHE.grouped_entryList, function(entryArr, staffId) {
								
								if(obj.staff.id = staffId) {
									
									_.each(entryArr, function(entry) {
										
										if(obj.id === entry.id) {
											
											obj = entry;
											
										}
										
									});
									
								}
								
							});
							
							if(obj.location !== null) {
								location = obj.location.name;
							} else {
								location = 'N/A';
							}
							
		                    return [
			                	{
				                	name: 'Employee',
				                	value: obj.staff.fname + ' ' + obj.staff.lname
			                	},
			                	{
				                	name: 'Location',
				                	value: location
			                	},
			                	{
				                	name: 'Job Type',
				                	value: obj.service.name
			                	},
			                	{
				                	name: 'Start Time',
				                	value: moment(obj.start_date).format('M/D/YYYY h:mm A')
			                	},
			                	{
				                	name: 'End Time',
				                	value: moment(obj.end_date).format('M/D/YYYY h:mm A')
			                	},
			                	{
				                	name: 'Duration (In minutes)',
				                	value: obj.duration
			                	},
			                	{
				                	name: 'Tips',
				                	value: '$' + (obj.tips/100).formatMoney()
			                	},
			                	{
				                	name: 'Compensation',
				                	value: '$' + (obj.compensation/100).formatMoney()
			                	}   
		                    ];
		                    
	                    },
	                    data: function(paged, callback) {
		                    
		                    var queryObj = {
		                            start_date: {
		                                type: 'between', 
		                                start: moment.unix(payperiodObj.start).startOf('day').unix(), 
		                                end: payperiodObj.end.endOf('day').unix()
		                            }, 
		                            paged: paged, 
		                            childObjs: 1
		                        };
		                        
		                    if(setup.hasOwnProperty('report')) {

			                    queryObj = {
		                            start_date: {
		                                type: 'between', 
		                                start: moment.unix(payperiodObj.start).startOf('day').unix(), 
		                                end: payperiodObj.end.endOf('day').unix()
		                            },
		                            payment_report: setup.report.id, 
		                            paged: paged, 
		                            childObjs: 1
		                        };
			                    
		                    }
	
	                        sb.data.db.obj.getWhere('time_entries', queryObj, function(ret) {
		                        	    
								callback(ret);     
									    	                            
	                        });
	                       
	                    }
	                }
	            });
			    
		    }
		    
		    function build_locationsFilter(dom) {
		            
	            display_loader({
		            mainDom: dom,
		            text: 'Fetching all staff locations ...'
	            });
	            
	            sb.data.db.obj.getAll('staff_base', function(staffBases) {
					
					var locations = [];
					
					_.each(staffBases, function(base) {
						
						locations.push({
							name: base.name,
							value: base.id
						})
						
					});
					
					locations.push({
						name: 'All Locations',
						value: 0
					});
					
					locations = _.sortBy(locations, function(o) {
						return o.name;
					});
					
		            dom.empty();
		            
		            dom.makeNode('col1', 'div', {css: 'ten wide column'});
					dom.makeNode('col2', 'div', {css: 'three wide column'});
					dom.makeNode('col3', 'div', {css: 'three wide column'});
		            
		            dom.col1.makeNode('form', 'form', {
			            locations: {
				            name: 'locations',
				            type: 'select',
				            options: locations
			            }
		            });
		            
		            dom.col2.makeNode('filter', 'div', {
			            text: '<i class="filter icon"></i> Filter by location', 
			            css: 'ui fluid teal button'
			        }).notify('click', {
			            type: 'payroll-run',
			            data: {
				            run: function(data) {
					            
					            var formData = dom.col1.form.process().fields;
					            
					            DATA_CACHE.locationId = parseInt(formData.locations.value);
					            DATA_CACHE.showSalaryEntries = false;

					            update_payrollReportTable(formData.locations.value);
					            
				            }
			            }
		            }, sb.moduleId);
		            
		            dom.col3.makeNode('salary_filter', 'div', {
			        	text: '<i class="dollar icon"></i> View only salary entries',
			        	css: 'ui fluid olive button'    
		            }).notify('click', {
			            type: 'payroll-run',
			            data: {
				            run: function(data) {
					            
					            DATA_CACHE.showSalaryEntries = true;
					            
					        	update_payrollReportTable();
					            
				            }
			            }
		            }, sb.moduleId);
		            
		            dom.patch();
		            
	            }, 1);
	            
            }
		    
		    function update_payrollReportTable(locationId) {

				comps.payroll_report.notify({
					type: 'update-table-data',
					data: {
						data: function(paged, callback) {
							
							var query = {};
				
							query = {
		                        start_date: {
		                            type: 'between', 
		                            start: moment.unix(payperiodObj.start).startOf('day').unix(), 
		                            end: payperiodObj.end.endOf('day').unix()
		                        },
		                        /*
duration: {
		                            type: 'greater_than',
		                            value: 0
		                        },
*/
		                        paged: paged, 
		                        childObjs: 1
		                    };
		                    
		                    if(locationId !== undefined) {
			                    query.location = parseInt(locationId);
		                    }
							
							sb.data.db.obj.getWhere('time_entries', query, function(ret) {

	                            callback(ret);
	                            
	                        });
							
						}
					}
				});
				
			}
		    
		    sb.notify({
				type: 'app-collapse-main-nav',
				data: {
					navState: 'hide'
				}	
			});
		    
		    if(setup.state.appSettings.apps.payroll.fiscal_year_start === undefined) {
				
				payperiodObj.fiscal_year_start = 'Not Set';
				
			}
			
			if(setup.hasOwnProperty('report')) {
				
				payperiodObj.start = moment(report.start_date).unix();
			    payperiodObj.fiscal_year_start = report.fiscal_year_start;
				
				display_payrollCycleTyle(report.cycle_type, function(type) {
				    
					payperiodObj.cycle_type = type;
					
					payperiodObj.end = display_payperiodEndDate(payperiodObj.start, type);    
				    
			    });
				
			} else {
				
				display_payrollCycleTyle(setup.state.appSettings.apps.payroll.payroll_cycle.type, function(type) {
				    
					payperiodObj.cycle_type = type;
					
					payperiodObj.end = display_payperiodEndDate(payperiodObj.start, type);    
				    
			    });	
				
			}

		    dom.empty();
		    
		    UI_CACHE.modal = dom.makeNode('modal', 'modal', {
			    onShow: function() {},
			    onClose: function() {}
		    });
		    
		    dom.makeNode('wrapper', 'div', {});
		    
		    dom.wrapper.makeNode('head', 'div', {});
		    dom.wrapper.makeNode('lb_1', 'lineBreak', {
			    spaces: 1
		    });
		    dom.wrapper.makeNode('info', 'div', {});
		    dom.wrapper.makeNode('lb_2', 'lineBreak', {
			    spaces: 1
		    });
		    dom.wrapper.makeNode('filter', 'div', {css: 'ui grid'});
		    dom.wrapper.makeNode('lb_3', 'lineBreak', {
			    spaces: 1
		    });
		    dom.wrapper.makeNode('body', 'div', {});
		    
		    UI_CACHE.filter = dom.wrapper.filter;
		    
		    // Report header
		    build_reportHeader(dom.wrapper.head, {
			    payperiodObj: payperiodObj,
			    state: setup.state,
			    loading: true
		    });
		    
		    display_loader({
				mainDom: dom.wrapper.body,
				text: 'Checking for existing report for this payperiod...' 
		    });
		    
		    if(!setup.hasOwnProperty('report')) {
			    
			    // Fetching any payment reports related to this payperiod
			    sb.data.db.obj.getWhere('payment_report', {
				    name: 'Payment Report for ' + moment.unix(payperiodObj.start).format('M/D/YYYY') + ' to ' + payperiodObj.end.format('M/D/YYYY'),
				    cycle_type: payperiodObj.type, 
					childObjs: 1
		        }, function(reports) {

			        dom.wrapper.body.empty();
			        
			        // Checking if reports list is empty
					if(_.isEmpty(reports)) {
						
						// If true, build payperiod report view
						build_reportHeader(dom.wrapper.head, {
						    payperiodObj: payperiodObj,
						    state: setup.state
					    });
						
						// Report Info
					    build_reportInfo(dom.wrapper.info, {
							payperiodObj: payperiodObj 
					    });
					    
					    build_locationsFilter(dom.wrapper.filter);
					    
					    call_reportsCrud({
						    payperiodObj: payperiodObj,
						    state: setup.state 
					    });
						
					} else {
						
						// If false, allow user to navigate to payment report view
						
						build_reportHeader(dom.wrapper.head, {
						    payperiodObj: payperiodObj,
						    state: setup.state,
						    btns: false
					    });
					    
					    dom.wrapper.info.makeNode('msg', 'div', {
						    css: 'ui message',
						    text: '<div class="header">Payment Report found!</div><p>This payperiod already has a payment report.</p>'
					    });
					    
					    dom.wrapper.info.makeNode('btnGrp', 'div', {
						    css: 'text-center'
					    });
					    
					    dom.wrapper.info.btnGrp.makeNode('back', 'div', {
						    text: 'Back',
						    css: 'ui red button'
					    }).notify('click', {
						    type: 'payroll-run',
						    data: {
							    run: function(data) {
								    
								    init(data.mainDom, data.state);
								    
							    },
							    mainDom: UI_CACHE.mainDom,
							    state: setup.state
						    }
					    }, sb.moduleId);
					    
					    dom.wrapper.info.btnGrp.makeNode('report', 'div', {
						    text: 'Go to payment report <i class="right arrow icon"></i>',
						    css: 'ui blue button'
					    }).notify('click', {
						    type: 'payroll-run',
						    data: {
							    run: function(data) {
								    
								    sb.notify({						// NAVIGATING TO PAYMENT REPORT SINGLE VIEW
										type: 'app-navigate-to',
										data: {
											itemId: 'payroll',
											viewId: {
												dom: function(report, dom, state, draw) {
		
													var payperiodObj = {
															start: moment(report.start_date),
															end: moment(report.end_date).clone().subtract(1, 'day'),
															type: report.cycle_type,
															cycle_start: report.cycle_start,
															fiscal_year_start: report.fiscal_year_start
														};
													
													dom.empty();
													
													dom.makeNode('wrapper', 'div', {});
		
													build_payrollReport(dom.wrapper, state, draw, report);
													
													draw(false);
													dom.patch();
													
												},
												icon: '<i class="file alternate outline icon"></i>',
												id: 'single-' + reports[0].id,
												parent: 'payment-report',
												removable: true,
												rowObj: reports[0],
												title: reports[0].name,
												type: 'table-single-item',
												viewState: setup.state
											}
										}
									});
								    
							    }
						    }
					    }, sb.moduleId);
						
					}
					
					dom.wrapper.patch();
			        
		        });
			    
		    } else {
			    
			    build_reportHeader(dom.wrapper.head, {
				    payperiodObj: payperiodObj,
				    state: setup.state,
				    report: setup.report
			    });
				
				// Report Info
			    build_reportInfo(dom.wrapper.info, {
					payperiodObj: payperiodObj 
			    });
			    
			    build_locationsFilter(dom.wrapper.filter);
			    
			    call_reportsCrud({
				    payperiodObj: payperiodObj,
				    report: setup.report,
				    state: setup.state 
			    });
			    
		    }
		    
		    dom.patch();
		    
	    }
	    
	    function init(dom, state) {
		    
		    dom.empty();
        
	        dom.makeNode('wrapper', 'div', {css: 'ui raised basic segment'});
	        
	        dom.wrapper.makeNode('head', 'div', {});
	        dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
	        dom.wrapper.makeNode('body', 'div', {});
	        
	        dom.wrapper.head.makeNode('title', 'div', {
		        text: '<i class="file alternate outline icon"></i> Payroll Report', 
		        css: 'ui huge header'
		    });

		    if(state.appSettings.apps.payroll.payroll_cycle.type !== '0') {
			    
				build_payrollSettingsInfo(dom.wrapper.head, {
			        state: state
		        });
		        
		        build_payPeriodForm(dom.wrapper.body, {
			        state: state
		        });   
			    
		    } else {
			    
			    dom.wrapper.body.makeNode('msg', 'div', {css: 'ui message'});
			    dom.wrapper.body.makeNode('lb_1', 'lineBreak', {
				    spaces: 1
			    });
			    dom.wrapper.body.makeNode('btnGrp', 'div', {
				    css: 'text-center'
			    });
			    
			    dom.wrapper.body.msg.makeNode('header', 'div', {
				    text: '<i class="exclamation icon"></i> Payroll Settings Not Set <p>Please set up payroll settings to view payroll reports.</p>',
				    css: 'header'
			    });
			    
			    dom.wrapper.body.btnGrp.makeNode('to_settings', 'div', {
					text: 'Go to settings <i class="arrow right icon"></i>',
					css: 'ui blue button' 
			    }).notify('click', {
				    type: 'payroll-run',
				    data: {
					    run: function(data) {
						    
							sb.notify({						
								type: 'app-navigate-to',
								data: {
									itemId: 'payroll',
									viewId: {
										id: 'table',
										type: 'settings',
										title: 'Settings',
										icon: '<i class="fa fa-cog"></i>',
										default: true,
										setup: [
											{
												object_type: 'payroll_cycle',
												name: 'Payroll Cycle',
												action: function(dom, bp, state) {
			
													payrollCycleSettings_ui(dom, state);
													
												}
											},
											{
												object_type: 'fiscal_year_start',
												name: 'Fiscal Year Start',
												action: function(dom, bp, state) {
													
													fiscalYearSettings_ui(dom, state);
													
												}
											}
										]
									}
								}
							});    
						    
					    }
				    }
			    }, sb.moduleId);
			    
		    }
	        
	        draw(false);
	        dom.patch();
		    
	    }
        
        UI_CACHE.mainDom = dom;
        
        if(report === undefined) {
	        
	    	init(dom, state);   
	        
        } else {
	        
	        build_payperiodReport(dom, {
		        state: state,
		        payperiod_start: moment(report.start_date).unix(),
		        report: report
	        });
	        
        }
        
        return {
	        build_payperiodReport: build_payperiodReport
        }
    	
	}
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'payroll',
						title: 'Payroll',
						icon: '<i class="fa fa-money"></i>',
						views: [
							{
								id:'schedule-requests-table',
								type:'table',
								title:'Schedule Requests',
								icon:'<i class="fa fa-th-list"></i>',
								default:true,
								setup:{
									objectType:'staff_availability_requests',
									childObjs:1,
									tableTitle: '<i class="calendar icon"></i> Schedule Requests',
									searchObjects:[],
									filters:function(callback){
										//!TODO: fix this filter
										callback({
											status:{
												name: 'Status',
												type: 'select',
												field: 'status',
												options:[
													{
														name:'Processing',
														label: 'processing',
														value:'processing'
													}, {
														name:'Approved',
														label: 'approved',
														value:'approved'
													},
													{
														name:'Denied',
														label: 'denied',
														value:'denied'
													}
												]
											}
										});
										
									},
									download:false,
									headerButtons:{},
									multiSelectButtons:{},
									calendar:{},
									rowSelection:false,
/*
									rowLink:{
										type:'tab',
										header:function(obj){

											return obj.fname +' '+ obj.lname;
										},
										action: singleStaffView
									},
*/
									visibleCols:{
										timePeriod:'Time Period',
										request_from:'From',
										request_type: 'Type',
										reason:'Reason',
										remaining_time_off:'Remaining time off',
										actions:''
									},
									cells: {
										reason:function(obj){

											if(_.isEmpty(obj.reason)){
												return '<i>None provided</i>';
											}else{
												return obj.reason;
											}
											
										},
										request_from:function(obj){

											return obj.staff.fname +' '+ obj.staff.lname;
											
										},
										timePeriod:function(obj){

											switch(obj.recurring) {
												
												case 'no':
													
													return  '<strong>'+ moment(obj.start_time).format('ddd, MMM Do YYYY h:mm A') +'</strong> to </br> <strong>'+ moment(obj.end_time).format('h:mm A') +'</strong>';
													
													break;
													
												case 'yes':
													
													var week_days = JSON.parse(obj.recurring_week_days);
													
													if(week_days.length === 1) {
														
														return '<strong>'+ moment(obj.start_time).format('ddd, MMM Do YYYY h:mm A') +'</strong> to </br> <strong>'+ moment(obj.end_time).format('h:mm A') +'</strong> </br> Recurring on <strong>'+ week_days[0] +'s</strong>';	
														
													} else {
														
														var string = '<strong>'+ moment(obj.start_time).format('ddd, MMM Do YYYY h:mm A') +'</strong> to </br> <strong>'+ moment(obj.end_time).format('h:mm A') +'</strong> </br> Recurring on'; 
														
														for(var i = 0; i < week_days.length; i++) {
															
															if((week_days.length - 1) !== i) {
																
																string += ' <strong>'+ week_days[i] +'s,</strong>';
																
															} else {
																
																string += ' <strong>'+ week_days[i] +'s</strong>';		
																
															}
															
														}
														
														return string;
														
													}
													
													
													break;
													
												default:
													return;
												
											}
											
										},
										actions:function(obj, cell){
											
											cell.properties.compInstanceId = sb.instanceId;
											
											switch(obj.status){
												
												case 'approved':
												
													cell.makeNode('approved', 'div', {
														css:'ui green disabled button',
														text:'<i class="check icon"></i> Approved'
													});
												
												break;
												
												default:
												
													cell.makeNode('actions', 'div', {
														css:'ui buttons'
													}).makeNode('approve', 'div', {
														css:'ui green button',
														text:'Approve'
													});
													
													cell.actions.makeNode('or', 'div', {css:'or'});
													
													cell.actions.makeNode('decline', 'div', {
														css:'ui red button',
														text:'Decline'
													});
												
												break;
												
											}
											
										},
										remaining_time_off:function(obj){
											
											var displayString = '';
											
											if(obj.staff.vacation_days && obj.staff.vacation_days.id){
												displayString += getCurrentDaysOffLeft(obj.staff, 'vacation_days') +'/'+ obj.staff.vacation_days.max_qty +' vacation days';
											}else{
												displayString += '<i>Vacation days not set</i>';
											}
											
											displayString += '<br />';
											if(obj.staff.sick_days && obj.staff.sick_days.id){
												displayString += getCurrentDaysOffLeft(obj.staff, 'sick_days') +'/'+ obj.staff.sick_days.max_qty +' sick days';
											}else{
												displayString += '<i>Sick days not set</i>';
											}
											
											return displayString;
											
										},
										request_type: function(obj) {

											return obj.request_type_name;
											
										}
									},
									data: function(page, callback){
										
										//!TODO: filter by status
										sb.data.db.obj.getWhere('staff_availability_requests', {
											paged:page,
// 											status:'',
											childObjs:{
												start_time:true,
												end_time:true,
												status:true,
												reason:true,
												recurring: true,
												recurring_end_date: true,
												recurring_week_days: true,
												request_type: true,
												staff:{
													fname:true,
													lname:true,
													vacation_days:true,
													sick_days:true
												}
											}
										}, function(data){
											
											callback(data);
											
										});
										
									}
								}
							},
							{
    							id: 'payroll-report',
    							type: 'custom',
    							title: 'Payroll Report',
    							icon: '<i class="file alternate outline icon"></i>',
    							dom: build_payrollReport
							},
							{
								id: 'payment-report',
								type: 'table',
								title: 'Payment Reports',
								icon:'<i class="file alternate outline icon"></i>',
								setup: {
									tableTitle:'<i class="file alternate outline icon"></i> Payment Reports',
									objectType: 'payment_report',
									searchObjects: false,
									childObjs: 1,
									rowSelection: false,
									multiSelectButtons: {},
									headerButtons: {
										reload: {
											name: 'Reload',
											css: 'pda-btn-blue',
											action: function() {}
										}
									},
									visibleCols: {
										name: 'Name',
										cycle_type: 'Cycle Type',
										total_price: 'Total Price'
									},
									cells: {
										cycle_type: function(obj) {
											return obj.cycle_type_name;
										},
										total_price: function(obj) {
											return '$' + (obj.total_price/100).formatMoney();
										}
									},
									rowLink: {
										type: 'tab',
										header: function(obj) {
											return obj.name;
										},
										icon: '<i class="file alternate outline icon"></i>',
										action: function(report, dom, state, draw) {

											var payperiodObj = {
													start: moment(report.start_date),
													end: moment(report.end_date).clone().subtract(1, 'day'),
													type: report.cycle_type,
													cycle_start: report.cycle_start,
													fiscal_year_start: report.fiscal_year_start
												};
											
											dom.empty();
											
											dom.makeNode('wrapper', 'div', {});

											build_payrollReport(dom.wrapper, state, draw, report);
											
											draw(false);
											dom.patch();
											
										}
									},
									data: function(paged, callback) {
										
										sb.data.db.obj.getAll('payment_report', function(reports) {
											
											callback(reports);
											
										}, 1, paged);
										
									}
								}	
							},
							{
								id: 'table',
								type: 'settings',
								title: 'Settings',
								icon: '<i class="fa fa-cog"></i>',
								default: true,
								setup: [
									{
										object_type: 'payroll_cycle',
										name: 'Payroll Cycle',
										action: function(dom, bp, state) {

											payrollCycleSettings_ui(dom, state);
											
										}
									},
									{
										object_type: 'fiscal_year_start',
										name: 'Fiscal Year Start',
										action: function(dom, bp, state) {
											
											fiscalYearSettings_ui(dom, state);
											
										}
									}
								]
							}
						]
					}
				}
			});
						
			sb.listen({
				'payroll-single-staff-start': this.start,
				'payroll-run': this.run
			});
			
			comps.payroll_report = sb.createComponent('crud-table');
			
		},
		
		destroy: function() {
    		
    		_.each(comps, function(comp) {
        		comp.destroy();
    		});
    		
    		comps = {};
    		
		},
		
		run: function(data) {
			
			data.run(data);
			
		},
		
		start: function(setup) {

			singleStaffPayroll_ui(setup.domObj, setup.staffObj);
			
		}
		
	}
	
});