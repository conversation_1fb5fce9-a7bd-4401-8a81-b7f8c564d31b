Factory.register('dashboard', function(sb) {
	
	function display_dashboardView(dom, state, draw) {

		function build_shiftDetails(dom, obj, draw, onComplete) {

			var obj_location = '';
			var end_date = '';
			var end_time = moment(obj.end_date).format('h:mm A');
			
			function build_requestTimeOff(dom, obj) {
				
				var formArgs = {
						request_type: {
							name: 'request_type',
							type: 'hidden',
							label: 'Request Type',
							value: 'time_off'
						},
						start_time: {
							name: 'start_time',
							type: 'date',
							label: 'Start Time',
							dateType: 'datetime',
							value: moment(obj.parent.start_date)
						},
						end_time: {
							name: 'end_time',
							type: 'date',
							label: 'End Time',
							dateType: 'datetime',
							value: moment(obj.parent.start_date)
						},
						recurring: {
							name: 'recurring',
							type: 'select',
							label: 'Recurring? (day of week)',
							options: [
								{
									name: 'No',
									value: 'no'
								},
								{
									name: 'Yes',
									value: 'yes'
								},
								{
									name: 'Yes, with end date',
									value: 'yes_with_end_date'
								}
							]
						},
						reason: {
							name: 'reason',
							type: 'textbox',
							label: 'Reason',
							placeholder: 'Reason for submitting this request (optional)'
						}
					};
				
				function process_requestOff(form, load, after) {
					
					var formData = form.process().fields;
					var newObj = {};
					
					load();
					
					newObj.request_type = formData.request_type.value;
					newObj.start_time = formData.start_time.value;
					newObj.end_time = formData.end_time.value;
					newObj.recurring = formData.recurring.value;
					newObj.reason = formData.reason.value;
					newObj.related_object = sb.data.cookie.userId;
					newObj.status = 'processing';
					
					sb.data.db.obj.create('staff_availability_requests', newObj, function(res) {
							
						after(res);
						
					});
					
				}
				
				dom.empty();
						
				dom.makeNode('wrapper', 'div', {});
				
				dom.wrapper.makeNode('head', 'div', {css: 'ui grid'});
				dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
				dom.wrapper.makeNode('body', 'div', {});
				
				dom.wrapper.head.makeNode('col1', 'div', {css: 'ui ten wide column'});
				dom.wrapper.head.makeNode('col2', 'div', {css: 'ui six wide column'});
				
				dom.wrapper.head.col1.makeNode('title', 'div', {
					text: '<h1 class="ui header">Request Time Off <div class="ui sub header">'+ moment(obj.start_date).format('dddd, MMMM Do YYYY') +'</div></h1>'
				});
				
				dom.wrapper.head.col2.makeNode('btnGrp', 'div', {});
				
				dom.wrapper.head.col2.btnGrp.makeNode('submit', 'div', {
					text: 'Submit',
					css: 'ui green button right floated'
				}).notify('click', {
					type: 'dashboard-run',
					data: {
						run: function(data) {

							process_requestOff(data.dom.wrapper.body.seg.form, function() {
								
								data.dom.empty();
								
								display_loader(data.dom, 'Creating request ...');
								
								data.dom.patch();
								
							}, function(req) {
								
								onComplete(true);
								
							});
							
						},
						dom: dom
					}
				}, sb.moduleId);
				
				dom.wrapper.head.col2.btnGrp.makeNode('back', 'div', {
					text: 'Back',
					css: 'ui red button right floated'
				}).notify('click', {
					type: 'dashboard-run',
					data: {
						run: function(data) {
							
							build_shiftDetails(dom, obj, draw);
							
						}
					}
				}, sb.moduleId);
				
				dom.wrapper.body.makeNode('seg', 'div', {css: 'ui basic segment'});
				
				dom.wrapper.body.seg.makeNode('form', 'form', formArgs);
				
				dom.patch();
				
			}
			
			// CHECKS
			
			if(typeof obj.parent.location === 'number') { // Checking if parent object (scehdule) has a location selected
				
				obj_location = obj.parent.location;
				
			} else {
				
				obj_location = 0;
				
			}
			
			if(moment(obj.end_date).endOf('day').unix() !== moment(obj.parent.start_date).endOf('day').unix()) { // Checking to see if the end time of obj (shift) falls on the same date as its parent obj (schedule)
				
				end_date = moment(obj.end_date).format('M/D/YY'); // If false, save the shift end date
				
			} else {
				
				end_date = ''; // If true, do not save shift end date
				
			}
			
			sb.data.db.obj.getById('staff_base', obj_location, function(location) {
				
				if(location !== null) {
					
					obj_location = location.name;	
					
				} else {
					
					obj_location = 'Not selected';
					
				}
				
				dom.empty();
									
				dom.makeNode('wrapper', 'div', {});
				
				dom.wrapper.makeNode('head', 'div', {css: 'ui equal width grid'});
				dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
				dom.wrapper.makeNode('body', 'div', {css: 'ui equal width grid'});
				
				dom.wrapper.head.makeNode('col1', 'div', {css: 'ui column'});
				dom.wrapper.head.makeNode('col2', 'div', {css: 'ui column'});
				
				dom.wrapper.head.col1.makeNode('title', 'div', {text: '<h1 class="ui header">'+ obj.name +'<div class="ui sub header">'+ obj.parent.name +'</div></h1>'});
				dom.wrapper.head.col2.makeNode('btnGrp', 'div', {});
				
				dom.wrapper.head.col2.btnGrp.makeNode('btn', 'div', {text: 'Request Time Off', css: 'ui green button right floated'}).notify('click', {
					type: 'dashboard-run',
					data: {
						run: function(data) {
							
							build_requestTimeOff(data.dom, data.obj);
							
						},
						dom: dom,
						obj: obj
					}
				}, sb.moduleId);
				
				dom.wrapper.body.makeNode('col1', 'div', {css: 'ui column'});
				dom.wrapper.body.makeNode('col2', 'div', {css: 'ui column'});
				dom.wrapper.body.makeNode('col3', 'div', {css: 'ui column'});
				
				// ----- COLUMN 1
				
				dom.wrapper.body.col1.makeNode('loc_key', 'div', {text: 'Location', css: 'text-muted'});
				dom.wrapper.body.col1.makeNode('loc_val', 'div', {text: '<h3>'+ obj_location +'</h3>'});
				
				dom.wrapper.body.col1.makeNode('lb_1', 'lineBreak', {spaces: 1});
				
				dom.wrapper.body.col1.makeNode('time_key', 'div', {text: 'Time', css: 'text-muted'});
				dom.wrapper.body.col1.makeNode('time_val', 'div', {
					text: '<h3>'+ moment(obj.start_date).format('M/D/YY') + ' <i>' + moment(obj.start_date).format('h:mm A') + '</i> to '+ end_date + ' <i>' + end_time +'</i></h3>'
				});
				
				dom.wrapper.body.col1.makeNode('lb_2', 'lineBreak', {spaces: 1});
				
				dom.wrapper.body.col1.makeNode('managers_key', 'div', {text: 'Managers', css: 'text-muted'});
				
				if(_.isEmpty(obj.parent.managers)) { // Checking if managers list is empty on the parent obj (schedule)
					
					// If true, display text
					
					dom.wrapper.body.col1.makeNode('managers_val', 'div', {text: '<h3>No managers selected</h3>'}); 
					
				} else {
					
					// If false, loop over manager list and display names
					
					_.each(obj.parent.managers, function(manager, i) {
						
						dom.wrapper.body.col1.makeNode('managers_val' + manager.id, 'div', {
							text: '<h4>'+ manager.fname + ' ' + manager.lname +'</h4>'
						}); 
						
					}); 
					
				}
				
				// ----- COLUMN 2
				
				dom.wrapper.body.col2.makeNode('coWorkers_key', 'div', {text: 'Co-workers', css: 'text-muted'});
				dom.wrapper.body.col2.makeNode('loader_cont', 'div', {});
				
				display_loader(dom.wrapper.body.col2.loader_cont, 'Fetching scheduled staff ...');
				
				sb.data.db.obj.getWhere('groups', { // Loading all shifts related to parent obj (schedule)
					group_type: 'Shift', 
					parent: obj.parent.id,
					childObjs: 1
				}, function(shiftList) {
					
					var staffList = []; 
					
					delete dom.wrapper.body.col2.loader_cont;
					
					shiftList = _.filter(shiftList, function(shift) { // Filtering for shifts that have staff scheduled
						return shift.user !== null;
					});
					
					if(!_.isEmpty(shiftList)) { // Checking if filtered list is empty
						
						// If false, loop over shifts and display staff names
						_.each(shiftList, function(shift, i) {
	
							if(parseInt(sb.data.cookie.userId) !== shift.user.id) { // Excluding the name of the user using this view
								
								staffList.push(shift.user); // Collecting all staff members with a different id than current user
									
							} 
							
						});
						
						if(_.isEmpty(staffList)) { // Checking if staff list is empty
							
							// If true, display text
							dom.wrapper.body.col2.makeNode('coWorkers_val', 'div', {text: '<h3>There are no other staff members scheduled</h3>'});
							
						} else {
							
							// If false, loop over staff list
							_.each(staffList, function(staff, i) {
								
								dom.wrapper.body.col2.makeNode('coWorkers_val'+staff.id, 'div', {text: '<h3>'+ staff.fname + ' ' + staff.lname +'</h3>'});
								
							});
							
						}
						
					}
					
					dom.wrapper.body.col2.patch();
					
				});
				
				// ----- COLUMN 3
				
				dom.wrapper.body.col3.makeNode('notes_key', 'div', {text: 'Shift Notes', css: 'text-muted'});
				
				if(_.isEmpty(obj.details)) { // Checking if shift notes are empty
					
					// If true, display text
					dom.wrapper.body.col3.makeNode('notes_val', 'div', {text: '<h3>No shift notes found</h3>'});
					
				} else {
					
					// If false, display notes
					dom.wrapper.body.col3.makeNode('notes_val', 'div', {text: '<h3>'+ obj.details +'</h3>'});
					
				}
				
				dom.patch();
				
			}, 1);
			
		}
		
		dom.empty();
		
		dom.makeNode('wrapper', 'div', {});
		
		if(draw) {
			
			draw({
				dom: dom,
				after: function(dom) {
					
					dom.patch();
					
					sb.notify({
						type: 'show-collection',
						data: {
							domObj: dom.wrapper,
							selectedView: 'calendar',
							objectType: 'groups',
							fields: {
								name: {
									title: 'Name'
								},
								details: {
									title: 'Details'
								}
							},
							groupings: {
								name: 'Name'	
							},
							singleView: {
								view: build_shiftDetails
							},
							state: state,
							where: {
								group_type: 'Shift',
								childObjs: 1,
								user: {
									type: 'contains',
									value: sb.data.cookie.userId
								},
								start_date: {
									type: 'between',
									start: moment().startOf('year').unix(),
									end: moment().endOf('year').unix()
								}
							}
						}
					});
					
				}
			})
			
		} else {
			
			dom.patch();
			
			sb.notify({
				type: 'show-collection',
				data: {
					domObj: dom.wrapper,
					selectedView: 'calendar',
					objectType: 'groups',
					fields: {
						name: {
							title: 'Name'
						},
						details: {
							title: 'Details'
						}
					},
					groupings: {
						name: 'Name'	
					},
					singleView: {
						view: build_shiftDetails
					},
					state: state,
					where: {
						group_type: 'Shift',
						childObjs: 1
					}
				}
			});
				
		}
		
	}
	
	function display_loader(dom, text) {
			
		dom.makeNode('load_cont', 'div', {});
		dom.load_cont.makeNode('loader', 'loader', {size: 'medium'});
		dom.load_cont.makeNode('load_text', 'div', {text: text, css: 'ui sub header center aligned'});
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'dashboard',
						title: 'Dashboard',
						icon: '<i class="fa fa-dashboard"></i> ',
						views: [
							{
								id: 'main',
								default: true,
								type: 'custom',
								title: 'Dashboard',
								icon: '<i class="fa fa-dashboard"></i> ',
								dom: display_dashboardView
							}
						]
					}
				}
			});
			
			sb.listen({
				'dashboard-run':this.run
			});
			
		},
		
		run: function(data) {
			data.run(data);
		}
		
	}
	
});