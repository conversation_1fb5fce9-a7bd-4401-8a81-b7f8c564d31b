Factory.register('job-types', function(sb){
	
	// comp views
	function jobType_collection(dom, state, draw, mainDom, options){

		var collSetup = {
				actions: {
					create: function (ui, state, onComplete, bp) {

						state.action = {
							onSave:function(res){

								window.location.href = sb.data.url.createPageURL(
									'object', 
									{
										type:'inventory_service', 
										id:res.inventory_service.id,
										name:res.inventory_service.name
									}
								);									
															
							}
						};						
						
						jobType_form(ui, state, onComplete, bp);
						
						return;
					}
				},
				domObj: dom,
				fields: {
					name: {
						title: 'Name'
						, type: 'title'
					},
				},
				useCache: true,
				groupings: {},
				objectType: 'inventory_service',
				selectedView: 'list',
				selectedMobileView: 'list',
				state: state,
				tool: options.tool,
				where: {
					childObjs: {
						name: true
					}
				}
			};

		sb.notify({
			type: 'show-collection'
			, data: collSetup
		});	
			
	}
	
	function jobType_form(dom, state, draw, bp) {

		var obj = state.pageObject || null;

		var formObj1 = {
				name: {
					name: 	'name'
					, type: 	'text'
					, label:	'Name'
				}
				, description: {
					type: 	'textbox'
					, name: 	'description'
					, label: 	'Description'
				}
				, permission: {
					type: 	'select'
					, name: 	'permission'
					, label:  'User Permissions'
					, options:[]
				}
				, manager_locations:{
					type:	'checkbox'
					, name:	'manager_locations'
					, label:	'Location Manager: <small>Allows Time Clock Admin Privileges</small>'
					, options:[]
				}
				, can_be_billed: {
				    type: 	'select'
				    , name: 	'can_be_billed'
				    , label: 	'Can be billed?'
				    , options: [
	   				    {
	       				    name: 	'Yes'
	       				    , value: 	'yes'
	   				    }
	   				    , {
	       				    name: 	'No'
	       				    , value: 	'no'
	   				    }
				    ]	
				}
				, inventory_billable_categories: {
					type: 'select'
					, name: 'inventory_billable_categories'
					, label: 'Category'
					, options: []
					, value: (obj !== null && obj.inventory_billable_categories) ? obj.inventory_billable_categories.id : 0
				}
				, price_type: {
					type: 		'select'
					, name: 		'price_type'
					, label: 		'Billing Type'
					, options: 	[]
					, onChange: 	switch_formFields
				}
			};
		var priceFormObj = {
				price: {
					type: 	'usd'
					, name: 	'price'
					, label: 	'Flat Fee Amount'
					, value: (obj !== null) ? obj.price : 0
				},
			};
		var rateFormObj = {
				rate: {
					type: 	'usd'
					, name:	'rate'
					, label:	'Rate'
					, value: (obj !== null) ? obj.rate : 0
				}
			};
		var min_hoursFormObj = {
				min_hours: {
					type: 	'number'
					, name: 	'min_hours'
					, label: 	'Minimum Hours'
					, value: (obj !== null) ? obj.min_hours : 0
				}
			};
		var max_flat_hoursFormObj = {
				max_flat_hours: {
					type: 	'number'
					, name: 'max_flat_hours'
					, label:  'Maximum hours of flat rate time before hourly billing starts'
					, value: (obj !== null) ? obj.max_flat_hours : 0
				}
			};
		var formObj2 = {
				/*
surcharges: {
					type: 	'checkbox'
					, name: 	'surcharges'
					, label:  'Surcharges'
					, options:[]
				},
*/
				vendor: {
					type: 	'select'
					, name:   'vendor'
					, label:  'Vendor (for Contract Labor)'
					, options:[]
				}
			};
		/*
var formObj3 = {
				chart_of_account_company: {
					name: 	'chart_of_account_company'
					, type: 	'select'
					, label:  'Chart Of Account Company'
					, options:[]
					, onChange: chartOfAccountUpdateField.bind(this, dom)
				}
			};
		var formObj4 = {
				chart_of_account: {
					name: 	'chart_of_account'
					, type: 	'select'
					, label:  'Chart Of Account'
					, options:[]
				},
				inclusive_tax: {
					type: 	'checkbox'
					, name: 	'inclusive_tax'
					, label: 	'Inclusive Tax'
					, options:[]
				},
				exclusive_tax: {
					type: 	'checkbox'
					, name: 	'exclusive_tax'
					, label: 	'Exclusive Tax'
					, options:[]
				}
			};
*/

		if (obj) {
			
			// If editing, don't show the name value (this is editable
			// from the page already, and updates the associated group 
			// object there)
			
			if (obj.id) {
				formObj1.name.type = 'hidden';
			} 
			
			formObj1.name.value = obj.name;
			formObj1.description.value = obj.description;
			
			formObj1.permission.value = obj.permission;
			formObj1.manager_locations.value = obj.manager_locations;
			formObj1.can_be_billed.value = obj.can_be_billed;
			formObj1.price_type.value = obj.price_type;
			//formObj2.surcharges.value = obj.surcharges;
			/*
formObj4.inclusive_tax.value = obj.inclusive_tax;
			formObj4.exclusive_tax.value = obj.exclusive_tax;
*/

			/*
if(obj.chart_of_account != null && obj.chart_of_account !== false){
				
				formObj3.chart_of_account_company.value = obj.chart_of_account.chart_of_accounts_company;
				formObj4.chart_of_account.value = obj.chart_of_account.id;
			}
*/		
			
		}

		function save_jobType(form, dom, obj, permissions) {

			var formData1 	  = form.form1.process().fields;
			var priceFormData = form.priceWrap.priceForm.process().fields;
			var rateFormData  = form.rateWrap.rateForm.process().fields;
			var min_hoursData = form.min_hoursWrap.min_hoursForm.process().fields;
			var max_hoursData = form.max_flat_hoursWrap.max_flat_hoursForm.process().fields;
			var formData2 	  = form.form2.process().fields;
			/*
var formData3 	  = form.form3Wrap.form3.process().fields;
			var formData4     = form.form4Wrap.form4.process().fields;
*/

			var jobTypeObj = {};
			var create = 'create';
			var jobType_payroll_cache = {};
			
			function display_jobTypeRate(setup) {
	
				// setup.obj --> job type object
	
				if(typeof setup.obj.price_type === 'number') {
					setup.obj.price_type = setup.obj.price_type.toString();
				}
				
				switch(setup.obj.price_type) {
					
					case '0':
					case 'flat':
					
						return {
							text: '$' + (setup.obj.price/100).formatMoney() + ' flat fee',
							rate: (setup.obj.price/100).formatMoney()
						};
					
						break;
						
					case '1':
					case 'hourly':
						
						return {
							text: '$' + (setup.obj.rate/100).formatMoney() + ' per hour',
							rate: (setup.obj.rate/100).formatMoney()	
						};
						
						break;
						
					case '2':
					case 'non-billable':
						
						return {
							text: '',
							rate: 0	
						};
						
						break;
						
					case '3':
					case 'flat_and_hourly':
					
						return {
							text: '$' + (setup.obj.price/100).formatMoney() + ' flat fee ($'+ (setup.obj.rate/100).formatMoney() +' per hour after '+ setup.obj.max_flat_hours +' hours)',
							rate: (setup.obj.rate/100).formatMoney(),
							price: 	(setup.obj.price/100).formatMoney()
						};
					
						break;
						
					default:
						return;
					
				}
				
			}
			
			function display_billingTypeName(billing_type) {
				
				switch(billing_type) {
					
					case '0':
					case 'flat':
						
						return 'Flat';
						
						break;
						
					case '1':
					case 'hourly':
						
						return 'Hourly';
						
						break;
						
					case '2':
					case 'non-billable':
						
						return 'Non-billable';
						
						break;
						
					case '3':
					case 'flat_and_hourly':
						
						return 'Flat and hourly';
						
						break;
					
				}
				
			}
			
			function compare_payrollData(setup) {
				
				// setup.jobType --> job type object
				// setup.cached_payrollData
				
				var jobType = setup.jobType;
				var cached_data = setup.cached_payrollData;
				
				if(jobType.price_type !== cached_data.price_type) {
					
					var changed = '<div><i>Billing type</i> was changed from <strong>'+ display_billingTypeName(cached_data.price_type) +'</strong> to <strong>'+ display_billingTypeName(jobType.price_type) +'</strong></div>';
					
					switch(jobType.price_type) {
						
						case '0':
						case 'flat':
						
							return {
								text: changed + '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>'+
									  '<div><strong>Rate: </strong>$'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee</div>'
							};
						
							break;
							
						case '1':
						case 'hourly':
						
							return {
								text: changed + '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>'+
									  '<div><strong>Rate: </strong>$'+ (parseInt(jobType.rate)/100).formatMoney() +' per hour</div>'
							};
						
							break;
							
						case '2':
						case 'non-billable':
						
							return {
								text: changed + '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>'
							};
						
							break;
							
						case '3':
						case 'flat_and_hourly':
						
							return {
								text: changed + '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>'+
									  '<div><strong>Rate: </strong>$'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee ($'+ (parseInt(jobType.rate)/100).formatMoney() +' per hour after '+ jobType.max_flat_hours +' hours)</div>'
							};
							
							break;
						
					}
					
				} else {
					
					var data = '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>';
					
					switch(jobType.price_type) {
						
						case '0':
						case 'flat':
						
							var price_change = '';
						
							if(parseInt(jobType.price) !== parseInt(cached_data.price)) {
								
								price_change = '<div><i>Flat rate</i> was changed from $'+ (parseInt(cached_data.price)/100).formatMoney() +' flat fee to $'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee</div>' + data;
								
								return {
									text: price_change
								};
								
							} else {
								
								return {
									text: 'No changes were made'
								};
							}
						
							break;
							
						case '1':
						case 'hourly':
						
							var rate_change = '';
	
							if(parseInt(jobType.rate) !== parseInt(cached_data.rate)) {
								
								rate_change = '<div><i>Hourly rate</i> was changed from $'+ (parseInt(cached_data.rate)/100).formatMoney() +' per hour to $'+ (parseInt(jobType.rate)/100).formatMoney() +' per hour</div>' + data;
								
								return {
									text: rate_change
								};
								
							} else {
								
								return {
									text: 'No changes were made'
								};
								
							}
						
							break;
							
						case '2':
						case 'non-billable':
						
							return {
								text: data
							};
						
							break;
							
						case '3':
						case 'flat_and_hourly':
						
							var rate_change = '';
							var price_change = '';
						
							if(parseInt(jobType.price) !== parseInt(cached_data.price) && parseInt(jobType.rate) !== parseInt(cached_data.rate)) {
								
								price_change = '<div><i>Flat rate</i> was changed from $'+ (parseInt(cached_data.price)/100).formatMoney() +' flat fee to $'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee</div>';
								rate_change = '<div><i>Hourly rate</i> was changed from $'+ (parseInt(cached_data.price)/100).formatMoney() +' per hour to $'+ (parseInt(jobType.price)/100).formatMoney() +' per hour</div>';
								
								return {
									text: price_change + rate_change + data
								};
								
							} else if(parseInt(jobType.price) !== parseInt(cached_data.price) && parseInt(jobType.rate) === parseInt(cached_data.rate)) {
								
								price_change = '<div><i>Flat rate</i> was changed from $'+ (parseInt(cached_data.price)/100).formatMoney() +' flat fee to $'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee</div>';
								
								return {
									text: price_change + data
								};
								
							} else if(parseInt(jobType.price) === parseInt(cached_data.price) && parseInt(jobType.rate) !== parseInt(cached_data.rate)) {
								
								rate_change = '<div><i>Hourly rate</i> was changed from $'+ (parseInt(cached_data.rate)/100).formatMoney() +' per hour to $'+ (parseInt(jobType.rate)/100).formatMoney() +' per hour</div>';
								
								return {
									text: rate_change + data
								};
								
							} else {
								
								return {
									text: 'No changes were made'
								};
								
							}
							
							break;
						
					}
					
				}
				
			}
	
			if(!formData1.permission && permissions.length > 0){
				sb.dom.alerts.alert('Warning', 'Please create a User Permission before trying to save.', 'warning');
				return;
			}
	
			if(formData1.name.value === '') {
				
				sb.dom.alerts.alert(
					'Error',
					'Name field can not empty. Please name the job type to create it.',
					'error'
				);
				
				return;
				
			} else {

				jobTypeObj.name             = formData1.name.value;
				jobTypeObj.description      = formData1.description.value;
				jobTypeObj.vendor_id        = +formData2.vendor.value;
				/* jobTypeObj.chart_of_account = +formData4.chart_of_account.value; */
				jobTypeObj.price_type       = +formData1.price_type.value;
				jobTypeObj.inventory_billable_categories = +formData1.inventory_billable_categories.value;
				
				if(formData1.manager_locations) {
					
					jobTypeObj.manager_locations = 
						_.map(formData1.manager_locations.value, function(val){
							return +val;
						});
						
				} else {
					
					jobTypeObj.manager_locations = [];
					
				}
				
				jobTypeObj.can_be_billed = formData1.can_be_billed.value;
	
				if(formData1.permission)
					jobTypeObj.permission = +formData1.permission.value;
			
/*
				if(formData2.surcharges) {
					
					jobTypeObj.surcharges = 
						_.map(formData2.surcharges.value, function(val){ return +val; });
						
				} else {
					
					jobTypeObj.surcharges = [];
					
				}
*/
				
				/*
if(formData4.inclusive_tax) {
					
					jobTypeObj.inclusive_tax = +formData4.inclusive_tax.value;
					
				} else {
					
					jobTypeObj.inclusive_tax = [];
					
				}
*/
				
				/*
if(formData4.exclusive_tax) {
					
					jobTypeObj.exclusive_tax = +formData4.exclusive_tax.value;
					
				} else {
					
					jobTypeObj.exclusive_tax = [];
					
				}
*/

				switch(+jobTypeObj.price_type) {
				
					case 0:
					//case 'flat':

						jobTypeObj.price = priceFormData.price.value;
					
						break;
						
					case 1:		
					//case 'flat_and_hourly':
					
						jobTypeObj.price = priceFormData.price.value;
						jobTypeObj.rate = rateFormData.rate.value;
						jobTypeObj.min_hours = +min_hoursData.min_hours.value;
						jobTypeObj.max_flat_hours = +max_hoursData.max_flat_hours.value;
					
						break;
					
					case 2:		
					//case 'hourly':
						
						jobTypeObj.rate = rateFormData.rate.value;
						jobTypeObj.min_hours = +min_hoursData.min_hours.value;
					
						break;
						
					case 3:
					//case 'non-billable':
					
						break;	
					
					default:
						return;
					
				}

				if(obj !== null && obj !== false) {
					
					create = 'update';
					
					jobTypeObj.id = obj.id;
					
					jobType_payroll_cache.price_type = obj.price_type;
					jobType_payroll_cache.rate = obj.rate;
					jobType_payroll_cache.price = obj.price;
					jobType_payroll_cache.max_flat_hours = obj.max_flat_hours;
					
				}

				sb.data.db.obj[create]('inventory_service', jobTypeObj, function(newJobType) {

					if(newJobType) {

						var groupObject = {
								name:		newJobType.name
								, description: newJobType.description
							};
	
						var noteObj = {
								type_id: newJobType.id,
								type: 'inventory_service',
								note: '',
								record_type: 'log',
								author: sb.data.cookie.get('uid'),
								notifyUsers: [],
								log_data: {
									type: create,
									objectName: newJobType.name,
									details: ''
								}
							};
												
						if (create == 'create') {
							
							groupObject.job_type = newJobType.id;
							groupObject.group_type = 'JobType';
							groupObject.managers = appConfig.headquarters.managers;
							groupObject.parent = appConfig.headquarters.id;
							groupObject.tools = appConfig.jobTypeTools;

/*
							noteObj.log_data.details = '<div><strong>Billing Type:</strong> '+ display_billingTypeName(newJobType.price_type) +'</div>'+
													   '<div><strong>Rate:</strong> '+ display_jobTypeRate({
														   obj: newJobType
													   }).text +'</div>';
*/
								
							noteObj.note = '<div>A new job type: <strong>'+ newJobType.name +'</strong> has been created</div></br>' + noteObj.log_data.details;	
																				
						} else if(create === 'update') {

							groupObject.id = newJobType.group_object;
								
/*
							noteObj.log_data.details = compare_payrollData({
								jobType: newJobType,
								cached_payrollData: jobType_payroll_cache
							}).text;
*/
							
							noteObj.note = '<div>Job type: <strong>' + newJobType.name + '</strong> has been updated<div></br>' + noteObj.log_data.details;
								
						}

						setTimeout(function(){

							sb.data.db.obj[create](
								'groups'
								, groupObject
								, function(jtGroupObj){ 

									if (create == 'create') {
										
										sb.data.db.obj.update('inventory_service'
											, {
												id:			 newJobType.id
												, group_object: jtGroupObj.id
											}, function(res){
												
												setTimeout(function(){
													
													if (state.hasOwnProperty('action') && typeof state.action.onSave == 'function') {
														state.action.onSave(
															{
																inventory_service:res
																, group:jtGroupObj
															}
														);
													}
													
												}, 500);

												 return res; 
											}
										);
										
									}
																	
									return true; 
								
								}	
							);
							
							sb.data.db.obj.create('notes', noteObj, function(newNote) { return; });
												
						}, 100);		
						
						$('.ui.modal').modal('hide');
													
					}
				
				}, 1);	
			}		
		}	
			
		function switch_formFields(value) {

			switch(value) {
				
				case '0':
				case 'flat':
				
					$(dom.wrapper.body.cont.priceWrap.selector).removeClass('hidden');
					$(dom.wrapper.body.cont.rateWrap.selector).addClass('hidden');
					$(dom.wrapper.body.cont.min_hoursWrap.selector).addClass('hidden');
					$(dom.wrapper.body.cont.max_flat_hoursWrap.selector).addClass('hidden');
					//$(dom.wrapper.body.cont.form3Wrap.selector).removeClass('hidden');
					
					break;
					
				case '1':	
				case 'flat_and_hourly':
				
					$(dom.wrapper.body.cont.priceWrap.selector).removeClass('hidden');
					$(dom.wrapper.body.cont.rateWrap.selector).removeClass('hidden');
					$(dom.wrapper.body.cont.min_hoursWrap.selector).removeClass('hidden');
					$(dom.wrapper.body.cont.max_flat_hoursWrap.selector).removeClass('hidden');
					//$(dom.wrapper.body.cont.form3Wrap.selector).removeClass('hidden');
				
					break;
					
				case '2':	
				case 'hourly':
				
					$(dom.wrapper.body.cont.priceWrap.selector).addClass('hidden');
					$(dom.wrapper.body.cont.rateWrap.selector).removeClass('hidden');
					$(dom.wrapper.body.cont.min_hoursWrap.selector).removeClass('hidden');
					$(dom.wrapper.body.cont.max_flat_hoursWrap.selector).addClass('hidden');
					//$(dom.wrapper.body.cont.form3Wrap.selector).removeClass('hidden');
					
					break;
					
				case '3':	
				case 'non_billable':
				
					$(dom.wrapper.body.cont.priceWrap.selector).addClass('hidden');
					$(dom.wrapper.body.cont.rateWrap.selector).addClass('hidden');
					$(dom.wrapper.body.cont.min_hoursWrap.selector).addClass('hidden');
					$(dom.wrapper.body.cont.max_flat_hoursWrap.selector).addClass('hidden');
					//$(dom.wrapper.body.cont.form3Wrap.selector).addClass('hidden');
					
					break;
				
			}
			
		}
		
		function chartOfAccountUpdateField(dom, value) {

			sb.data.db.obj.getWhere('chart_of_accounts', {
				chart_of_accounts_company: +value
			}, function(accounts) {
				
				formObj4.chart_of_account.options =  _.map(accounts, function(company) {
						
						return {
							name: company.name,
							value: company.id
						};
						
					});
				
				build_CoA(dom.wrapper.body.cont);
			
				dom.wrapper.body.cont.form4Wrap.patch();	
				
			});
			
		}
		
		function build_CoA(ui) {

			if(obj !== null && obj.chart_of_account != null && obj.chart_of_account !== false){
				
				formObj4.chart_of_account.value = obj.chart_of_account.id;
				
			} else {
				
				if(!_.isEmpty(formObj4.chart_of_account.options)) {
					
					formObj4.chart_of_account.value = formObj4.chart_of_account.options[0].value;
						
				}
				
			}
										
			ui.makeNode('form4Wrap', 'div', {
				css: ''
			}).makeNode('form4', 'form', formObj4);				
										
		}

		dom.empty();
		
		dom.makeNode('wrapper', 'div', {css: 'ui grid'});
		
		dom.wrapper.makeNode('head', 'div', {css:'centered row'});
		dom.wrapper.makeNode('body', 'div', {css: 'centered row'});
		dom.wrapper.head.makeNode('col1', 'div', {css:'fifteen wide column'});
		dom.wrapper.head.col1.makeNode('title'
			, 'headerText'
			, {
				text: (obj) ? '<i class="wrench icon"></i> Edit ' + obj.name : '<i class="wrench icon"></i> Create a New Job Type'
			}
		);
		dom.wrapper.makeNode('btns', 'div', {css: 'centered row'});
		dom.wrapper.btns.makeNode('cont', 'div', {css: 'fifteen wide column'});
		dom.wrapper.btns.cont.makeNode('btnGroup', 'div', {css:''});
	
		dom.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div'
			, {
				text: '<i class="fa fa-check"></i> Save'
				, css: 'ui right floated button disabled'
			}
		);			
		
		if (obj) {

			dom.wrapper.btns.cont.btnGroup.makeNode('erase_btn', 'div'
				, {
					text: '<i class="archive icon"></i> Archive'
					, css: 'ui right floated basic button'
					, style: 'border:none !important;'
				}
			).notify('click', {
				type: 'job-types-run',
				data: {
					run: function(obj) {

						sb.dom.alerts.ask({
							title: 'Are you sure?',
							text: 'Archive this Job Type?'
						}, function(resp){
						
							if(resp){

								swal.close();
						
								sb.data.db.obj.erase('groups', obj.id, function(res){

									$('.ui.modal').modal('hide');

									setTimeout(function(){
										
										sb.data.db.obj.getById('inventory_service'
											, obj.job_type
											, function(relatedService){

												if (relatedService) {
													
													sb.data.db.obj.erase('', relatedService.id, function(res){ 

														return res; 
													
													});
													
												}
												
												sb.notify({
													type: 'app-navigate-to',
													data: {
														type: 'UP'
													}
												});
												
											}
										);											
										
									}, 1000);																											
									
								});

							}
							
						});
						
					}.bind({}, obj)
				}
			}, sb.moduleId);	
			
		}
		
		dom.wrapper.btns.cont.btnGroup.makeNode('back_btn', 'div'
			, {
				text: '<i class="fa fa-arrow-left"></i> Back'
				, css: 'ui right floated basic button'
			}
		).notify('click', {
			type: 'job-types-run'
			, data: {
				run: function() {

					return $('.ui.modal').modal('hide');
										
				}
			}
		}, sb.moduleId);				 
		
		dom.wrapper.body.makeNode('load_cont', 'div', {css: 'fifteen wide column centered'});
		dom.wrapper.body.load_cont.makeNode('loader', 'loader', {size: 'large'});
		dom.wrapper.body.makeNode('cont', 'div', {css: 'fifteen wide column centered'});

		chartOfAccountSetup(function(coaInfo) {

			///formsetup		
			sb.data.db.obj.getWhere('companies', {is_vendor: 1, childObjs: 1}, function(companies) {
									
				sb.data.db.obj.getAll('tax_rates', function(taxes) {
												
					sb.data.db.obj.getAll('surcharges', function(surcharges) {
														
						sb.data.db.obj.getAll('inventory_billable_categories', function(categories) {
							
							formObj1.inventory_billable_categories.options = _.map(_.sortBy(categories, 'name'), function(category) {
								
								return {
									name: category.name,
									value: category.id
								}
								
							});
																
							sb.data.db.obj.getAll('user_views', function(permissions) {
																		
								sb.data.db.obj.getAll('staff_base', function(locations){

									if(_.isEmpty(permissions)){
										
										delete formObj1.permission;
																					
									}else{
										
										formObj1.permission.options = _.map(_.sortBy(permissions, 'name'), function(permission) {
										
											return {
												name: permission.name,
												value: permission.id
											};
											
										});
										
									}

									formObj1.manager_locations.options = _.map(_.sortBy(locations, 'name'), function(location){
										return {
											name: 'manager_locations',
											value: location.id,
											label: location.name
										};
									});

									
									formObj1.price_type.options = _.map(_.sortBy(bp.price_type.options, 'price_type'), function(field, fieldName) {

										return {
											name: field,
											value: fieldName
										};
										
									});

									
									/*
if(!_.isEmpty(surcharges)) {
										
										formObj2.surcharges.options = _.map(_.sortBy(surcharges, 'name'), function(tax) {
										
											return {
												name: 'surcharges',
												value: tax.id,
												label: tax.name
											};
											
										});
										
									} else {
										
										delete formObj2.surcharges;
										
									}
*/
									
									formObj2.vendor.options = _.map(_.sortBy(companies, 'name'), function(company) {
										
										return {
											name: company.name,
											value: company.id
										};
										
									});
									formObj2.vendor.options.unshift({
										name: 'N/A',
										value: 0
									});
	
									/*
formObj3.chart_of_account_company.options = _.map(_.sortBy(coaInfo.companies, 'name'), function(company) {
										
										return {
											name: company.name,
											value: company.id
										};
										
									});
*/

									/*
formObj4.chart_of_account.options = _.map(_.sortBy(coaInfo.accounts, 'name'), function(company) {

										return {
											name:company.name,
											value:company.id
										};
										
									});
*/
									
									/*
formObj4.inclusive_tax.options = _.map(_.sortBy(taxes, 'name'), function(tax) {
										
										return {
											name: 'inclusive_tax',
											value: tax.id,
											label: tax.name
										};
										
									});
									
									formObj4.exclusive_tax.options = _.map(_.sortBy(taxes, 'name'), function(tax) {
										
										return {
											name: 'exclusive_tax',
											value: tax.id,
											label: tax.name
										};
										
									});
*/      	
									
									dom.wrapper.body.load_cont.empty();
																	
									dom.wrapper.body.cont.makeNode('form1', 'form', formObj1);
									dom.wrapper.body.cont.makeNode('priceWrap', 'div', {
										css: ''
									}).makeNode('priceForm', 'form', priceFormObj);
									dom.wrapper.body.cont.makeNode('rateWrap', 'div', {
										css: 'hidden'
									}).makeNode('rateForm', 'form', rateFormObj);
									dom.wrapper.body.cont.makeNode('min_hoursWrap', 'div', {
										css: 'hidden'
									}).makeNode('min_hoursForm', 'form', min_hoursFormObj);
									dom.wrapper.body.cont.makeNode('max_flat_hoursWrap', 'div', {
										css: 'hidden'
									}).makeNode('max_flat_hoursForm', 'form', max_flat_hoursFormObj);
									dom.wrapper.body.cont.makeNode('form2', 'form', formObj2);
									/*
dom.wrapper.body.cont.makeNode('form3Wrap', 'div', {
										css: ''
									}).makeNode('form3', 'form', formObj3);
*/
									
									//build_CoA(dom.wrapper.body.cont);
										
									dom.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {text: '<i class="fa fa-check"></i> Save', css: 'ui right floated green button'}).notify('click', {
										type: 'job-types-run',
										data: {
											run: function() {
												
												save_jobType(dom.wrapper.body.cont, dom, obj, permissions);
												
											}
										}
									}, sb.moduleId);									  
									
									dom.wrapper.patch();
									
									/*
var CoAValue = 0;
									
									if(obj !== null) {
										CoAValue = obj.chart_of_account.chart_of_accounts_company;
									} else {
										CoAValue = formObj3.chart_of_account_company.options[0].value;
									}

									chartOfAccountUpdateField(dom, CoAValue);
*/
									
									if(obj !== null) {
										
										switch(+obj.price_type) {
												
											case 1: // flat + hourly
												
												$(dom.wrapper.body.cont.rateWrap.selector).removeClass('hidden');
												$(dom.wrapper.body.cont.min_hoursWrap.selector).removeClass('hidden');
												$(dom.wrapper.body.cont.max_flat_hoursWrap.selector).removeClass('hidden');
												break;
												
											case 2: // hourly
												
												$(dom.wrapper.body.cont.priceWrap.selector).addClass('hidden');
												$(dom.wrapper.body.cont.rateWrap.selector).removeClass('hidden');
												$(dom.wrapper.body.cont.min_hoursWrap.selector).removeClass('hidden');
												break;
												
											case 3: // non-billable
												
												$(dom.wrapper.body.cont.priceWrap.selector).addClass('hidden');
												$(dom.wrapper.body.cont.form3Wrap.selector).addClass('hidden');
												break;
											
										}
										
									}
									
								}, 1);
								
							}, 1);
							
						}, 1);
						
					}, 1);
					
				}, 1);
			
			});
			
		});
			
		dom.patch();
		
	}
	
	function jobType_view(dom, state, draw){

		sb.notify({
			type:'show-dashboard',
			data:{
				dom:dom,
				state:state,
				draw:draw
			}
		});	
		
	}
	
	// comp util
	function chartOfAccountSetup(callback){
		
		function getCompanies(callback){
			
			sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

				if (coaComps.length > 0) {
					
					callback(coaComps);
					
				} else {
					
					sb.data.db.obj.create('chart_of_accounts_companies', {
						name:'Default Chart of Account company'
					}, function(newCoaCompany){
	
						callback(newCoaCompany);
						
					});					
					
				}				
				
			});
			
		}
		
		function getAccounts(coaCompanyId, callback){

			sb.data.db.obj.getWhere('chart_of_accounts'
				, {
					chart_of_accounts_company: +coaCompanyId
				}
				, function(accounts){

					if (accounts.length === 0) {
						
						sb.data.db.obj.create('chart_of_accounts'
							, {
								chart_of_accounts_company:	coaCompanyId
								, name:					'Default chart of account'
								, account_id:				'1'
								, note:					'Default chart of account generated by the system.'
							}
							, function(accounts){
							
								callback(accounts);
							
							}
						);
						
					} else {
						
						callback(accounts);
						
					}
				
				}
			);
			
		}
				
		getCompanies(function(coaComps){

			getAccounts(coaComps[0].id, function(accounts){

				callback(
					{
						companies:	 coaComps
						, accounts:	 accounts
					}
				);
									
			});	
			
		});
		
	}
				
	function jobTypeGroupStatus(state, cb, options){

		var updState = _.clone(state);
				
		function jobTypeGroupGenerate(jobType, cb){

			var savedGroup = {
					name:		jobType.name
					, description:	jobType.description
					, group_type: 	'JobType'
					, job_type:	jobType.id
					, managers:	appConfig.headquarters.managers
					, parent:		appConfig.headquarters.id
					, tools: 		[]
					, tagged_with: []										
				};
				
			if (options) {
				savedGroup.tagged_with = options.tagged_with
			}	

			sb.data.db.obj.create('groups'
				, savedGroup
				, cb
			);
			
			return;
			
		}		

		sb.data.db.obj.getWhere(
			'groups'
			, {
				group_type:'JobType'
				, job_type: updState.id
			}
			, function(groupObject){

				if (groupObject.length !== 0) {
								
					updState.id = groupObject[0].id;
					updState.pageObject = groupObject[0];
					updState.pageObjectType = 'groups';			

					cb(updState);
				
				} else {
					
					jobTypeGroupGenerate(state.pageObject, function(group){

						updState.id = group.id;
						updState.pageObject = group;
						updState.pageObjectType = 'groups';

						cb(updState);						
						
					});				
					
				}
				
				return;	
							
			}
		);
		
		return;
		
	}

	function addUserToGroup (user, groupObj) {

		var upd_tw = _.clone(groupObj.tagged_with) || [];
		
		upd_tw.push(user);
		
		upd_tw = _.uniq(upd_tw);
		
		sb.data.db.obj.update('groups', {id:groupObj.id, tagged_with:upd_tw}, function(res){
// 			console.log('res-----', res, res.tagged_with);
		});
	}
	
	function removeUserFromGroup (user, groupObj) {

		var upd_tw = _.reject(groupObj.tagged_with, function(tag){
			return tag == user;
		});

		sb.data.db.obj.update('groups', {id:groupObj.id, tagged_with:upd_tw}, function(res){
/* 			console.log('res-----', res, res.tagged_with); */
		});		
		
	}
	
	function updateJobTypeGroup (cached, servList, userId) {

		cached = _.pluck(cached, 'id');
		
		function addToGroup(stateObject){

			jobTypeGroupStatus(stateObject, function(group){

				addUserToGroup(userId, group.pageObject);			
				
			}, {tagged_with:userId});
			
		}

		function removeFromGroup(stateObject){

			jobTypeGroupStatus(stateObject, function(group){

				removeUserFromGroup(userId, group.pageObject);
						
			});
			
		}
		
		function getServiceById(id, cb){
			
			sb.data.db.obj.getWhere(
				'inventory_service'
				, {
					id: id
					, childObjs:{
						name:true
						, description:true
					}
				}	
				, cb
			);
			
			return;
			
		}
		
		if (!_.isEmpty(servList)){

			_.map(servList, function(ls){

				var add = _.indexOf(cached, ls);

				if (add < 0){
					
					getServiceById(ls, function(service){

						addToGroup(
							{
								id:service[0].id
								, pageObject:service[0]
							}	
						);
						
					}); 
				}	

			});
			
			_.map(cached, function(cs){
				
				var remove = _.indexOf(servList, cs);
				
				if (remove < 0){
					
					getServiceById(cs, function(service){

						removeFromGroup(
							{
								id:service[0].id
								, pageObject:service[0]
							}
						);	
						
					});
				}									
					
			});

		} else {

			_.each(cached, function(cs){
				
				getServiceById(cs, function(service){

					removeFromGroup(
						{
							id:service[0].id
							, pageObject:service[0]
						}
					);	
					
				});
				
			});
			
		}

	}
				
	return {

		init: function(){

			sb.notify({
				type:'register-application'
				, data:{
					navigationItem:{
						moduleId: sb.moduleId
						, id:'jobTypes'
						, title:'Job Types'
						, icon:'<i class="wrench icon"></i>'
						, views:[
							// HQ collections tool StaffList
							{
								id:'jobTypeTool'
								, adminOnly: true
								, type:'hqTool'
								, name:'Job Types'
								, tip:'Define your team\'s Roles & Responsibilities.'
								, icon: {
									type: 'wrench'
									, color: 'blue'
								}
								, settings:[]
								, default:true
								, mainViews:[
									{
										dom:function(dom, state, draw, mainDom){

											jobType_collection(dom, state, draw, mainDom, {});
																						
										}
									}
								]
							}
							///HQ single object view
							, {
								id:'inventory_service-obj'
								, type:'object-view'
								, title:'Job Types'
								, icon:'wrench'
								, select: {
									name:		true
								}
								, header:{
									title:{
										options: {
											editing:true
											, onUpdate:function(newName, obj){

												sb.data.db.obj.update('inventory_service', {
													id: 	obj.job_type.id
													, name:	newName
												}, function(updatedService){

													return updatedService;
													
												});
												return;
																								
											}
										}
									}
									, detail:{
										options:{
											onUpdate:function(editedGroup){

												sb.data.db.obj.getWhere('inventory_service', {id: editedGroup.job_type.id}, function(service){

													sb.data.db.obj.update('inventory_service', {id: service[0].id, description:editedGroup.description}, function(updatedService){

														return updatedService;
													});
													
												});											
											}	
										}
									}
									, menu:{
										archive:true
										, billing: {
											icon:'orange pencil'
											, title:'Billing Options'
											, action: function(obj, dom, state, draw){

												sb.data.db.obj.getBlueprint('inventory_service', function(bp){
													
													state.action = {
														onSave:function(newObj){
							
															window.location.href = sb.data.url.createPageURL(
																'object', 
																{
																	type:'inventory_service', 
																	id:newObj.id,
																	name:newObj.name
																}
															);
															
														}
													};													
																								
													jobType_form(dom, state, draw, bp);
													
												});
												
											}
										}
									}
									, select:{
										name:true
										, type:{
											name:true
										}
										, managers:{
											fname:true
											, lname:true
										}
										, allowed_users:{
											fname:true
											, lname:true
										}
									}
									, canEdit: true
								}
								, beforeLoad: function(state, draw){

										sb.data.db.obj.getById('groups', state.id, function(jtGroup){
											
											if (!state.pageObject) {
												state.pageObject = jtGroup;
											}
											
											jobTypeGroupStatus(state, draw);
											
										});			
								}
								, dom: jobType_view
								, menu: function (state, draw, layer) {

									if (state) {
										sb.notify({
											type: 	'get-dashboard-menu'
											, data:	{
												draw: 	draw
												, group: 	state.pageObject
												, layer: 	layer
											}
										});	
									}									
																										
								}
							}
							///MyStuff JobTypes
							, {
								id:		'myJobTypes'
								, type: 	'myStuff'
								, name: 	'Roles & Responsibilities'
								, title: 	'Job Types'
								, tip: 	'My Roles & Responsibilities'
								, icon: {
									type: 	'wrench'
									, color: 	'blue'
								}
								, default: true
								, settings: 	[]
								, mainViews: 	[
									{
										dom: function(dom, state, draw){

											var collSetup = {
													actions: {
														create:false
													},
													domObj: dom,
													fields: {
														name: {
															title: 'Name'
															, type: 'title'
														},
													},
													useCache: true,
													groupings: {},
													objectType: 'inventory_service',
													selectedView: 'list',
													selectedMobileView: 'list',
													state: state,
													parseData:function(data, cb){

														sb.data.db.obj.getById(''
															, +sb.data.cookie.userId	
															, function(user){
																
																var myJobTypes = [];
	
				
																_.filter(data.data, function(serv){
																	
																	if ( _.contains(user.service, serv.id) ) {
																		myJobTypes.push(serv);																	
																	}
																	 
																	return;  
																	
																});
																
																data.data = myJobTypes;
															
															cb(data);
															
														});
																													
													}, 
													where: {
														childObjs: {
															name: true
														}
													}
												};

											sb.notify({
												type: 'show-collection'
												, data: collSetup
											});											

										}	
									}
								]
								, boxViews:	[]
							}							
						]
					}
				}
			});
					
			sb.listen({
				'update-job-type-group': 	this.updateJobTypeGroup
				, 'job-types-run': 				this.run
			});
			
		}
		
		, updateJobTypeGroup: function(data){

			updateJobTypeGroup(data.cachedServices, data.serviceList, data.userId);
			
		}
	
		, run: function(data){ data.run(data); }
		
	}
		
});