Factory.register('timeClockReport', function(sb) {
	
	function display_duration(timeEntry, returnType) {
		
		var duration = {
				minutes: 0,
				hours: 0,
				duration_start: moment(timeEntry.start_date),
				remainder: 0
			};
			
		if(timeEntry.end_date.length === 0) {
			
			duration.duration_end = moment();
			
		} else {
			
			duration.duration_end = moment(timeEntry.end_date);
			
		}
		
		duration.minutes = duration.duration_end.diff(duration.duration_start, 'minutes');

		if(returnType === undefined) {
			
			if(duration.minutes > 60) {
				
				duration.hours = duration.minutes / 60;
				duration.remainder = duration.minutes % 60;
			
				return Math.floor(duration.hours) + ' hour(s) ' + duration.remainder + ' minute(s)';
				
			} else {
				
				return duration.minutes + ' minute(s)';
				
			}
			
		} else {
			
			if(returnType === 'min') {
			
				return duration.minutes;
				
			} else if(returnType === 'hr') {
				
				return Math.floor(duration.hours);
				
			} else if(returnType === 'all') {
				
				duration.hours = duration.minutes / 60;
				duration.remainder = duration.minutes % 60;
				
				return {
					hours: Math.floor(duration.hours),
					remainder: duration.remainder
				}
				
			}	
			
		}
		
	}
	
	function display_loader(setup) {
				
		// SETUP { 
			// mainDom: dom to display loader,
			// domList: array of dom nodes to empty if needed (optional),
			// empty: boolean, if it exists all dom nodes in domList will be emptied and patched (optional),
			// text: text that will be displayed with loader
		//}
		
		if(setup.hasOwnProperty('domList') && setup.hasOwnProperty('empty')) {
			
			_.each(setup.domList, function(dom) {
				dom.empty();
				dom.patch();
			}, this);
			
		}
		
		setup.mainDom.empty();
		
		setup.mainDom.makeNode('cont', 'div', {});
		
		setup.mainDom.cont.makeNode('loader', 'loader', {});
		setup.mainDom.cont.makeNode('load_text', 'div', {text: setup.text, css: 'text-center'});
		
		setup.mainDom.patch();
		
	}
	
	function build_singleEntry(entry, dom, state, draw) {

		var UI_CACHE = {
				mainDom: {}
			};
		var shiftDuration = moment(entry.shift.end_date).diff(entry.shift.start_date, 'minutes');
		var shiftDuration_text = ''; 
		var timeOver = {
				minutes: display_duration(entry, 'min') - shiftDuration,
				hours: this.minutes / 60,
				remainder: shiftDuration % 60
			};
		var timeOver_text = '';
		
		if(shiftDuration < 60) {
			
			shiftDuration_text = `${shiftDuration} minutes`;
			
		} else {
			
			shiftDuration_text = `${Math.floor(shiftDuration / 60)} hours ${shiftDuration % 60} minutes`;
			
		}
		
		if(timeOver.minutes > 60) {
			
			timeOver_text = `${timeOver.hours} hours ${timeOver.remainder} minutes`;
			
		} else {
			
			timeOver_text = `${timeOver.minutes} minutes`;
			
		}

		function build_timeLine(dom, entry) {
			
			var isDisabled = '';
			var end_date = '';
			
			dom.empty();
			
			dom.makeNode('steps', 'div', {css: 'ui fluid steps'});
			
			// STEP 1
			dom.steps.makeNode('step1', 'div', {css: 'step'});
			
			dom.steps.step1.makeNode('icon', 'div', {tag: 'i', css: 'hourglass start icon'});
			dom.steps.step1.makeNode('content', 'div', {css: 'content'});
			
			dom.steps.step1.content.makeNode('title', 'div', {text: 'Shift Start', css: 'title'});
			dom.steps.step1.content.makeNode('description', 'div', {text: `${moment(entry.shift.start_date).format('MMM Do, YYYY h:mm A')}`, css: 'description'});
			
			// STEP 2
			dom.steps.makeNode('step2', 'div', {css: 'step'});
			
			dom.steps.step2.makeNode('icon', 'div', {tag: 'i', css: 'sign in alternate icon'});
			dom.steps.step2.makeNode('content', 'div', {css: 'content'});
			
			dom.steps.step2.content.makeNode('title', 'div', {text: 'Clocked In', css: 'title'});
			dom.steps.step2.content.makeNode('description', 'div', {text: `${moment(entry.start_date).format('MMM Do, YYYY h:mm A')}`, css: 'description'});
			
			// STEP 3
			dom.steps.makeNode('step3', 'div', {css: 'step'});
			
			dom.steps.step3.makeNode('icon', 'div', {tag: 'i', css: 'hourglass end icon'});
			dom.steps.step3.makeNode('content', 'div', {css: 'content'});
			
			dom.steps.step3.content.makeNode('title', 'div', {text: 'Shift Ends', css: 'title'});
			dom.steps.step3.content.makeNode('description', 'div', {text: `${moment(entry.shift.end_date).format('MMM Do, YYYY h:mm A')}`, css: 'description'});
			
			// STEP 4 
			
			if(entry.end_date.length === 0) {
				isDisabled = 'disabled';
				end_date = 'No clock out time';
			} else {
				isDisabled = '';
				end_date = `${moment(entry.end_date).format('MMM Do, YYYY h:mm A')}`;
			}
			
			dom.steps.makeNode('step4', 'div', {css: 'step ' + isDisabled});
			
			dom.steps.step4.makeNode('icon', 'div', {tag: 'i', css: 'sign out alternate icon'});
			dom.steps.step4.makeNode('content', 'div', {css: 'content'});
			
			dom.steps.step4.content.makeNode('title', 'div', {text: 'Clocked Out', css: 'title'});
			dom.steps.step4.content.makeNode('description', 'div', {text: end_date, css: 'description'});
			
			dom.patch();
			
		}
		
		function edit_timeEntry(dom, entry) {
			
			var formArgs = {
					clock_in: {
						name: 'clock_in',
						type: 'date',
						dateType: 'datetime',
						label: 'Clock In',
						value: moment(entry.start_date),
						placeholder: 'No clock in time'
					},
					clock_out: {
						name: 'clock_out',
						type: 'date',
						dateType: 'datetime',
						label: 'Clock Out',
						value: moment(entry.end_date),
						placeholder: 'No clock out time'
					},
					tips: {
						name: 'tips',
						type: 'usd',
						label: 'Tips',
						value: entry.tips
					}
				};
			
			function process_editEntry(form, entry, load, after) {
				
				var formData = form.process().fields;
				
				load();
				
				entry.start_date = formData.clock_in.value;
				entry.end_date = formData.clock_out.value;
				entry.tips = formData.tips.value;
				
				sb.data.db.obj.update('time_entries', entry, function(updated) {
					
					if(updated) {
						after(updated);
					}
					
				}, 1);
				
			}
			
			dom.empty();
			
			dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
			
			dom.wrapper.makeNode('head', 'div', {css: 'ui equal width grid'});
			dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
			dom.wrapper.makeNode('body', 'div', {});
			
			dom.wrapper.head.makeNode('col1', 'div', {css: 'column'});
			dom.wrapper.head.makeNode('col2', 'div', {css: 'column'});
			
			dom.wrapper.head.col1.makeNode('title', 'div', {text: `<h2 class="ui header">Edit Time Entry <div class="ui sub header">${entry.staff.fname} ${entry.staff.lname} <small>for</small> ${entry.service.name} <small>at</small> ${entry.location.name}</div></h2>`});
			
			dom.wrapper.head.col2.makeNode('btnGrp', 'div', {});
			
			dom.wrapper.head.col2.btnGrp.makeNode('save_btn', 'div', {text: 'Save', css: 'ui green button right floated'}).notify('click', {
				type: 'timeClockReport-run',
				data: {
					run: function(data) {
						
						process_editEntry(dom.wrapper.body.form_wrap.form, entry, function() {
							
							dom.wrapper.head.col2.btnGrp.makeNode('save_btn', 'div', {text: 'loading', css: 'ui green button right floated loading'});
						
							dom.wrapper.head.col2.btnGrp.makeNode('delete_btn', 'div', {text: 'loading', css: 'ui red button right floated loading'});
							
							dom.wrapper.head.col2.btnGrp.patch();
							
							display_loader({
								mainDom: dom.wrapper.body,
								text: 'Updating entry ...'
							});
							
						}, function(updated) {
							
							build_singleEntry(updated, dom, state, draw);
							
							if(entry.hasOwnProperty('onUpdate')) {
								
								entry.onUpdate();
								
							}
							
						});
						
					}
				}
			}, sb.moduleId);
			
			dom.wrapper.head.col2.btnGrp.makeNode('delete_btn', 'div', {text: 'Delete', css: 'ui red button right floated'}).notify('click', {
				type: 'timeClockReport-run',
				data: {
					run: function(data) {
						
						delete_timeEntry([entry.id], function() {
							
							dom.wrapper.head.col2.btnGrp.makeNode('save_btn', 'div', {text: 'loading', css: 'ui green button right floated loading'});
						
							dom.wrapper.head.col2.btnGrp.makeNode('delete_btn', 'div', {text: 'loading', css: 'ui red button right floated loading'});
							
							dom.wrapper.head.col2.btnGrp.patch();
							
							display_loader({
								mainDom: dom.wrapper.body,
								text: 'Deleting entry ...'
							});
							
						}, function(ret) {
							
							sb.notify({
								type:'app-remove-main-navigation-item',
								data:{
									itemId: 'timeClockReport',
									viewId: 'single-' + entry.id
								}
							});
							
							sb.notify({
								type: 'app-navigate-to',
								data: {
									itemId: 'timeClockReport',
									viewId: 'table',
									redraw: true 
								}
							});
							
							if(entry.hasOwnProperty('onDelete')) {
								
								entry.onDelete();
								
							}
							
						});	
						
					}
				}
			}, sb.moduleId);
			
			dom.wrapper.head.col2.btnGrp.makeNode('back_btn', 'div', {text: 'Back', css: 'ui red button right floated inverted'}).notify('click', {
				type: 'timeClockReport-run',
				data: {
					run: function(data) {
						
						build_singleEntry(entry, dom, state, draw);
						
					}
				}
			}, sb.moduleId);
			
			dom.wrapper.body.makeNode('form_wrap', 'div', {css: 'ui basic segment'});
			
			dom.wrapper.body.form_wrap.makeNode('form', 'form', formArgs);
			
			dom.patch();
			
		}

		UI_CACHE.mainDom = dom;
		
		dom.empty();
		
		dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
		
		dom.wrapper.makeNode('head', 'div', {css: 'ui equal width grid'});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
		dom.wrapper.makeNode('body', 'div', {});
		
		dom.wrapper.head.makeNode('col1', 'div', {css: 'column'});
		dom.wrapper.head.makeNode('col2', 'div', {css: 'column'});
		
		dom.wrapper.head.col1.makeNode('title', 'div', {text: `${entry.staff.fname} ${entry.staff.lname} <small class="text-muted">for</small> ${entry.service.name} <small class="text-muted">at</small> ${entry.location.name}`, tag: 'h2'});

		if(entry.payment_report === null || entry.payment_report === false) {
			
			dom.wrapper.head.col2.makeNode('btnGrp', 'div', {});
		
			dom.wrapper.head.col2.btnGrp.makeNode('edit_btn', 'div', {text: 'Edit', css: 'ui button orange right floated'}).notify('click', {
				type: 'timeClockReport-run',
				data: {
					run: function(data) {
						
						edit_timeEntry(dom, entry);
						
					}
				}
			}, sb.moduleId);	
			
		}
		
		dom.wrapper.body.makeNode('timeLine_wrap', 'div', {css: 'ui raised basic segment'});
		dom.wrapper.body.makeNode('details_wrap', 'div', {css: 'ui equal width grid'});
		dom.wrapper.body.makeNode('notes_wrap', 'div', {css: 'ui raised basic segment'});
		
		build_timeLine(dom.wrapper.body.timeLine_wrap, entry);
		
		dom.wrapper.body.details_wrap.makeNode('col1', 'div', {css: 'column'});
		dom.wrapper.body.details_wrap.makeNode('col2', 'div', {css: 'column'});
		dom.wrapper.body.details_wrap.makeNode('col3', 'div', {css: 'column'});
		dom.wrapper.body.details_wrap.makeNode('col4', 'div', {css: 'column'});
		dom.wrapper.body.details_wrap.makeNode('col5', 'div', {css: 'column'});
		
		// COL1
		dom.wrapper.body.details_wrap.col1.makeNode('segment', 'div', {css: 'ui raised basic segment text-center'});
		
		dom.wrapper.body.details_wrap.col1.segment.makeNode('label', 'div', {text: 'Duration', css: 'text-muted'});
		dom.wrapper.body.details_wrap.col1.segment.makeNode('val', 'div', {text: display_duration(entry, undefined), css: 'text-bold'});
		
		// COL2
		dom.wrapper.body.details_wrap.col2.makeNode('segment', 'div', {css: 'ui raised basic segment text-center'});
		
		dom.wrapper.body.details_wrap.col2.segment.makeNode('label', 'div', {text: 'Shift Duration', css: 'text-muted'});
		dom.wrapper.body.details_wrap.col2.segment.makeNode('val', 'div', {text: shiftDuration_text, css: 'text-bold'});
		
		// COL3
		dom.wrapper.body.details_wrap.col3.makeNode('segment', 'div', {css: 'ui raised basic segment text-center'});
		
		dom.wrapper.body.details_wrap.col3.segment.makeNode('label', 'div', {text: 'Time Over/Under', css: 'text-muted'});
		dom.wrapper.body.details_wrap.col3.segment.makeNode('val', 'div', {text: timeOver_text, css: 'text-bold'});
		
		// COL4
		dom.wrapper.body.details_wrap.col4.makeNode('segment', 'div', {css: 'ui raised basic segment text-center'});
		
		dom.wrapper.body.details_wrap.col4.segment.makeNode('label', 'div', {text: 'Tips Collected', css: 'text-muted'});
		dom.wrapper.body.details_wrap.col4.segment.makeNode('val', 'div', {text: `$ ${(entry.tips/100).formatMoney(2)}`, css: 'text-bold'});
		
		// COL5
		dom.wrapper.body.details_wrap.col5.makeNode('segment', 'div', {css: 'ui raised basic segment text-center'});
		
		dom.wrapper.body.details_wrap.col5.segment.makeNode('label', 'div', {text: 'Compensation', css: 'text-muted'});
		dom.wrapper.body.details_wrap.col5.segment.makeNode('val', 'div', {text: 'Coming soon', css: 'text-bold'});
		
		if(draw !== undefined) {
			
			draw({
				dom: dom,
				after: function(dom) {
					
					sb.notify({
						type: 'show-note-list-box',
						data: {
							domObj: dom.wrapper.body.notes_wrap,
							objectIds: [entry.id],
							objectId: entry.id,
							collapse: 'open'
						}
					});
					
				}
			});	
			
		} else {
			
			dom.patch();
			
			sb.notify({
				type: 'show-note-list-box',
				data: {
					domObj: dom.wrapper.body.notes_wrap,
					objectIds: [entry.id],
					objectId: entry.id,
					collapse: 'open'
				}
			});
			
		}
		
	}
	
	function delete_timeEntry(entryArr, load, after) {

		var noteIds = [];
		
		if(load !== undefined) {
			load();	
		}
		
		sb.data.db.obj.erase('time_entries', entryArr, function(ret) {
			
			if(ret) {
				
				sb.data.db.obj.getWhere('notes', {type_id: {type: 'or', values: entryArr}}, function(notesArr) {
					
					_.each(notesArr, function(note) {
						noteIds.push(note.id);
					}, this);
					
					sb.data.db.obj.erase('notes', noteIds, function(ret2) {
						
						if(ret2) {
							
							if(after !== undefined) {
								after(ret2);	
							}
							
						}
						
					});
					
				});
				
			}
			
		});
		
	}
			
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'timeClockReport',
						title: 'Time Clock Report',
						icon: '<i class="fa fa-clock-o" aria-hidden="true"></i>',
						views: [
							{
								id: 'table',
								type: 'table',
								default: true,
								icon: '<i class="fa fa-clock"></i>',
								title: 'All Time Clock Entries',
								setup: {
									tableTitle:'<i class="fa fa-clock"></i> All Time Entries',
									objectType: 'time_entries',
									childObjs: 1,
									searchObjects: [
										{
											name: 'Location Name',
											join: 'location',
											value: 'name'
										},
										{
											name: 'Employee First Name',
											join: 'users',
											value: 'fname'
										},
										{
											name: 'Employee Last Name',
											join: 'users',
											value: 'lname'
										}
									],
									rowSelection: true,
									multiSelectButtons: {
										erase: {
											name: '<i class="fa fa-trash-o"></i> Delete',
											css: 'pda-btn-red',
											domType: 'none',
											action: function(entryArr) {
												
												var noteIds = [];
												var entryIds = _.pluck(entryArr, 'id');
												
												sb.data.db.obj.erase('time_entries', entryIds, function(ret) {
			
													if(ret) {
														
														sb.data.db.obj.getWhere('notes', {type_id: {type: 'or', values: entryIds}}, function(notesArr) {
															
															_.each(notesArr, function(note) {
																noteIds.push(note.id);
															}, this);
															
															sb.data.db.obj.erase('notes', noteIds, function(ret2) {
																
																if(ret2) {
																	
																	sb.notify({
																		type: 'app-navigate-to',
																		data: {
																			itemId: 'timeClockReport',
																			viewId: 'table',
																			redraw: true 
																		}
																	});
																	
																}
																
															});
															
														});
														
													}
													
												});
												
											}
										}
									},
									headerButtons: {
										reload: {
											name: 'Reload',
											css: 'pda-btn-blue',
											action: function() {}
										}
									},
									download: function(obj) {
										
										var duration = 0,
											durationType = '';
										
										if(obj.duration > 60) {
											
											duration = obj.duration / 60;
											durationType = ' Hour(s)';
											
										} else {
											
											duration = obj.duration;
											durationType = ' Minute(s)';
											
										} 
										
										return [
											{
												name: 'Location',
												value: obj.location.name	
											},
											{
												name: 'Employee',
												value: obj.staff.fname + ' ' + obj.staff.lname
											},
											{
												name: 'Start Date',
												value: moment(obj.start_date).subtract(5, 'hour').format('dddd, MMMM Do, YYYY, hh:mm A')
											},
											{
												name: 'End Date',
												value: moment(obj.end_date).subtract(5, 'hour').format('dddd, MMMM Do, YYYY, hh:mm A')
											},
											{
												name: 'Duration',
												value: duration + durationType
											}
										]
										
									},
									visibleCols: {
										location: 'Location',
										staff: 'Employee',
										start_date: 'Start',
										end_date: 'End',
										duration: 'Duration',
										compensation: 'Compensation'
									},
									cells: {
										start_date: function(obj) {
											return '<div>' + moment(obj.start_date).format('ddd, MMM Do, YYYY h:mm A') + '</div>';
										},
										end_date: function(obj) {

											if(obj.duration === 0) {
												
												return '<span class="pda-color-red">No clock out time</span>';
												
											} else {
												
												return '<div>' + moment(obj.end_date).format('ddd, MMM Do, YYYY h:mm A') + '</div>';	
												
											}
											
										},
										duration: function(entry, dom) {
											
											if(entry.end_date.length === 0) {
												dom.makeNode('duration', 'div', {text: 'N/A'});	
											} else {
												dom.makeNode('duration', 'div', {text: display_duration(entry, undefined)});	
											}
											
										},
										compensation: function(obj) {
											return 'Coming soon';
										}
									},
									rowLink: {
										type: 'tab',
										header: function(obj) {
											return obj.staff.fname + ' ' + obj.staff.lname + ' - ' + obj.service.name;
										},
										action: build_singleEntry
									},
									data: function(paged, callback) {
										
										sb.data.db.obj.getAll('time_entries', function(ret) {
											
											ret.data = _.filter(ret.data, function(obj) {
												return obj.is_salary === null;
											});
											
											callback(ret);
											
										}, 1, paged);
										
									}
								}
							}
						]
					}
				}
			});
			
			sb.listen({
				'timeClockReport-run': this.run,
				'timeClockReport-start-single-entry': this.singleEntry
			});
			
		},
		
		destroy: function() {},
		
		run: function(data) {
			
			data.run(data);
			
		},
		
		singleEntry: function(data) {
			
			if(!data.entry) {
				
				throw 'Must inject a time entry object';
				return;
				
			}
			
			if(!data.domObj) {
				
				throw 'Must inject a dom object';
				return;
				
			}
			
			if(data.hasOwnProperty('onUpdate')) {
				
				data.entry.onUpdate = data.onUpdate;
				
			}
			
			if(data.hasOwnProperty('onDelete')) {
				
				data.entry.onDelete = data.onDelete;
				
			}
			
			build_singleEntry(data.entry, data.domObj, {}, undefined);
			
		}
		
	}
	
});