Factory.registerComponent('staffAvailability', function(sb){
	
	var comps = {},
		domObj = {},
		fiscalYearStart = moment('2016-12-01', 'YYYY-MM-DD'),
		fiscalYearEnd =  moment('2017-11-30', 'YYYY-MM-DD'),
		holidays = [],
		staffList = [],
		timeOffRequests = [];
	
	return {
		
		init: function(){
			
			sb.listen({
				'start-staffAvailability-component':this.start
			});
			
		},
		
		start: function(){
			
			sb.listen({
				'change-staff-vacation-days': this.changeVacation,
				'display-staff-availability-table':this.displayAvailabilityTable,
				'rebuild-staff-availability-page': this.rebuild,
				'save-staff-vac-days': this.saveStaffVac,
				'stop-staffAvailability-component':this.destroy
			});
			
		},
		
		destroy: function(){},
		
		changeVacation: function(setup){

			domObj.modals.makeNode(
				'editVacModal', 
				'modal', 
				{}
			).body.makeNode(
				'title',
				'headerText',
				{
					size: 'small',
					text: setup.staff.fname + " " + setup.staff.lname
				}
			
			);
			
			domObj.modals.editVacModal.body.makeNode(
				'editVacForm',
				'form',
				{
					vacationDays: {
						type: 'number',
						name: 'vacDays',
						label: 'Vacation Days',
						value: setup.staff.vacation_days
					}
				}	
			);
			
			domObj.modals.editVacModal.footer.makeNode(
				'updateBtn',
				'button',
				{
					text: 'Update',
					css: 'btn-success'
				}
			).notify(
				'click',
				{
					type: 'save-staff-vac-days',
					data: {
						staff: setup.staff,
						form: domObj.modals.editVacModal.body.editVacForm,
						allStaff: setup.allStaff
					}
				}
			);
			
			domObj.modals.patch();
			domObj.modals.editVacModal.show();
		
		},
		
		displayAvailabilityTable: function(setup){
			
/*
			setup = {
				domObj: {},
				staffList: [],
				holidays: [],
				timeOffRequests: []
			}
*/
			
			staffList = setup.staffList;
			holidays = setup.holidays;
			timeOffRequests = setup.timeOffRequests;
			
			domObj = sb.dom.make(setup.domObj.selector);
			
			domObj.makeNode('tableBreak', 'lineBreak', {spaces:1});
			
			domObj.makeNode(
				'availTable',
				'table',
				{
					css: 'table-hover table-condensed',
					columns: {
						name: 'Name',								
						allottedDays: 'Allotted Vacation Days',
						remainingDays: 'Remaining Days',
						approvedDays: 'Approved Days',
						btns: ''
					},
					dataTable: true
				}
			);
			
			_.each(_.reject(staffList, function(obj){return obj.vacation_days == 0}), function(staff){
				
				var approvedDaysOff = sb.data.util.calculateApprovedDays(staff, _.where(timeOffRequests, {staffId: staff.id}), fiscalYearStart, fiscalYearEnd, holidays),
					remainingDays = sb.data.util.calculateRemainingVacationDays(staff, _.where(timeOffRequests, {staffId: staff.id}), fiscalYearStart, fiscalYearEnd, holidays);
				
			
				domObj.availTable.makeRow(
					staff.id,
					[
						staff.fname+' '+staff.lname,
						staff.vacation_days,
						remainingDays,
						approvedDaysOff,
						''
					]
				);
				
				domObj.availTable.body[staff.id].btns.makeNode('buttonGroup', 'buttonGroup', {});
								
				domObj.availTable.body[staff.id].btns.buttonGroup.makeNode('vacaBtn', 'button', {text:'Edit Vacation Days', css:'btn-warning'}).notify('click', {
					type:'change-staff-vacation-days',
					data:{
						staff: staff,
						allStaff: staffList
					}
				}, sb.moduleId);
				
				domObj.availTable.body[staff.id].btns.buttonGroup.makeNode('availBtn', 'button', {text:'Edit Availability', css:'btn-warning'}).notify('click', {
					type:'change-staff-availability',
					data:{
						staff: staff,
						backButton: {
							type: 'rebuild-staff-availability-page',
							data: {
								allStaff: staffList
							}
						}
					}
				}, sb.moduleId);
								
			});
			
			domObj.makeNode('modals', 'container', {});
			
			domObj.build();
						
		},
		
		rebuild: function(setup){
			
			if(setup.hasOwnProperty('staffList')){
				staffList = setup.staffList;
			}
			
			if(setup.hasOwnProperty('holidays')){
				holidays = setup.holidays;
			}
			
			if(setup.hasOwnProperty('timeOffRequests')){
				timeOffRequests = setup.timeOffRequests;
			}
			
		},
		
		saveStaffVac: function(saveObj){
		
			var formData = saveObj.form.process();	

			saveObj.staff.vacation_days = +formData.fields.vacDays.value;	
			
			sb.data.db.obj.update('staff', saveObj.staff, function(updatedStaffObj){
				
				staffList = _.reject(staffList, function(staffObj){
					
					return staffObj.id == updatedStaffObj.id
				});
				
				staffList.push(updatedStaffObj);
				
				domObj.modals.editVacModal.hide();
				
				sb.notify({
					type: 'rebuild-staff-availability-page',
					data: {}
				});
				
				sb.dom.alerts.alert('Success!', '', 'success');
				
				
			});		
			
		}
		
	}
	
});