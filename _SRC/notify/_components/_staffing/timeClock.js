Factory.register('timeClock', function(sb) {
	
	var Setup = {};

	var UI_CACHE = {
		field: {},
		navbar_badge: {},
		modal: {}
	};

	var activeTimersList = [];
	var badgeInt = 0;

	var DATA_CACHE = {
		locationManager: false,
		managerOverride: false
	};

	var DefaultDuration = 0;

	var TimeEntriesBP = {
		id: true,
		name: true,
		object_uid: true,
		start_date: true,
		end_date: true,
		duration: true,
		note: true,
		tagged_with: true,
		shift: true,
		staff: {
			fname: true,
			lname: true,
			profile_image: true
		},
		parent: true,
		field_name: true
	}

	var TimeEntriesUpdateBP = {
		start_date: true,
		end_date: true,
		duration: true,
		note: true,
		tagged_with: true
	}

	var UpdateCollectionCallback = function(updated) {

		if (updated.hasOwnProperty('deleted') && updated.deleted) {

			Setup.removeFromCollections(updated.time_entry);

		} else {
			
			Setup.updateCollections(updated.time_entry, TimeEntriesUpdateBP);

		}

	}

	function resetModalCallbacks() {

		$(UI_CACHE.modal.showSelector).modal({
			'onHidden': function() {}
		})

	}

	function getPropertyNames(obj, state) {

        var propertyNames = {
			timeLoggedProperty: 'time_logged'
			, timeEstimateProperty: 'time_estimate'
            , rateValueProperty: 'rate'
			, runningProperty: 'running'
		}

		if (state && state.fieldName && obj.object_bp_type !== 'groups') {
			propertyNames.timeLoggedProperty = state.fieldName;
			propertyNames.timeEstimateProperty = state.fieldName +'_est';
            propertyNames.rateValueProperty = state.fieldName + '_rate';
			propertyNames.runningProperty = state.fieldName + '_running';
		}

        return propertyNames;

	}

	function clearIntervalObjects(timeEntryIds) {

		function clearIntervalObject(timeEntryId) {

			var intObjs = _.where(activeTimersList, {entryId: timeEntryId});
				
			_.each(intObjs, function (intervalObject) {
				clearInterval(intervalObject.intervalId);
			});
			
			activeTimersList = _.reject(activeTimersList, {entryId: timeEntryId});

		}

		if (Array.isArray(timeEntryIds)) {

			_.each(timeEntryIds, function(timeEntryId) {

				clearIntervalObject(timeEntryId);

			});

		} else {

			var timeEntryId = timeEntryIds;

			clearIntervalObject(timeEntryId);

		}

	}

	function getTimeEntryLink(dom, obj) {

		var link = '';
		if (obj.shift && obj.shift.id) {
			
			var shiftType = obj.shift.object_bp_type;
			if (shiftType === 'groups') {
				shiftType = obj.shift.group_type;
			}
			
			link = sb.data.url.createPageURL(
				'object',
				{
					id: 			obj.shift.id,
					type: 			shiftType.toLowerCase(),
					object_type: 	obj.shift.object_bp_type,
					name: 			obj.shift.name
				}
			);
			
		}

		if (link) {

			dom.makeNode('viewBtn-' + obj.id, 'div', {
				text: 'View', 
				css: 'ui tiny fluid basic light-grey button',
				href: link,
				tag: 'a'
			}).notify('click', {
				type: 'timeClock-run',
				data: {
					run: function () {

						UI_CACHE.modal.hide();

					}

				}

			});

		}

	}

	function displayRunningTimeBadge(ui, obj, state, options) {

		options = !_.isEmpty(options) ? options : {};

		var text = !_.isEmpty(options.text) && obj.end_date ? options.text : '<div class="ui fluid rounded grey label" style="text-align:center;"><i class="ui check icon" style="color:#ffffff !important; margin-right:5px !important;"></i> Logged</div>';
		
		if (!obj.end_date) {
			text = '<div class="ui fluid rounded red label" style="cursor:pointer; text-align:center;"><i class="ui stopwatch icon" style="color:#ffffff !important; margin-right:5px !important;"></i> Stop</div>';
		}

		if (ui) {

			var node = ui.makeNode('row-' + obj.oid, 'div', {
				text: text
			});

			if (!obj.end_date && (obj.staff.id === +sb.data.cookie.userId)) {

				node.notify('click', {
					type: 'timeClock-run',
					data: {
						run: function () {

							var parentObj = obj.shift;
							var timerState = {
								fieldName: obj.field_name,
								time_entry: [
									obj
								]
							}
							var timerOptions = {
								track: true,
								create: false,
								goBack: false,
								edit: false,
								inCollection: true,
								bypassSubmitTimeEntryPrompt: false,
								callback: options.callback
							}

							if (options.inModal) {
								timerOptions.goBack = true;
							}

							toggleClickEvent(UI_CACHE.modal.body.form, parentObj, timerState, timerOptions, function(updated) {

								if (options.callback) {
									options.callback(updated);
								}

							});

						}
					}
				});

			}

		} else {
			
			return text;

		}

	}

	function getCollectionsSetup(ui, state, options) {

		options = !_.isEmpty(options) ? options : {};

		ui.empty();

		ui.makeNode('grid', 'div', {
			css: 'ui grid'
		});

		var title = state.hasOwnProperty('myStuff') || appConfig.breadcrumbs[appConfig.breadcrumbs.length - 1].title == 'Time Sheet' ? 'Time Sheet' : 'Time Log';

		ui.grid.makeNode('headerLeft', 'div', {
			css: 'ui eight wide column',
			style: 'padding-top:0 !important; padding-bottom:5px !important;',
			text: '<span class="ui huge header">' + title + '</span>'
		});

		ui.grid.makeNode('headerRight', 'div', {
			css: 'ui eight wide column',
			style: 'padding-top:0 !important; padding-bottom:5px !important;',
		});

		if (options.timeSheetLink) {

			ui.grid.headerRight.makeNode('timeSheetLink', 'div', {
				css: 'ui small basic light-grey button',
				style: 'float:right; top:0;',
				text: 'View Time Sheet',
				tag: 'a',
				href: window.location.href.split('#')[0] + '#mystuff&1=mst-mytimelog'
			});

		}

		if (options.startTimeTrackingLink) {

			ui.grid.headerRight.makeNode('startTimeTrackingLink', 'div', {
				css: 'ui small green button',
				style: 'float:right; top:0;',
				text: 'Start Running Timer'
			}).notify('click', {
				type: 'timeClock-run',
				data: {
					run: function () {

						ui.grid.headerRight.startTimeTrackingLink.loading();

						if (typeof options.startTimeTrackingLink === 'function') {
							options.startTimeTrackingLink();
						}

					}
				}
			});

			// ui.grid.headerRight.makeNode('stopAllRunningTimers', 'div', {
			// 	css: 'ui small red button',
			// 	style: 'float:right; top:0;',
			// 	text: 'Stop All Running Timers'
			// }).notify('click', {
			// 	type: 'timeClock-run',
			// 	data: {
			// 		run: function () {

			// 			ui.grid.headerRight.stopAllRunningTimers.loading();

			// 			sb.data.db.obj.getWhere('time_entries', {
			// 				staff: parseInt(sb.data.cookie.get('uid')),
			// 				duration: {
			// 					type: 'not_set'
			// 				}
			// 			}, function (time_entries) {

			// 				var actionSetup = {
			// 					object: appConfig.state.pageObject,
			// 					state: appConfig.state,
			// 					options: {}
			// 				};
			
			// 				var actionOptions = {
			// 					selection: _.pluck(time_entries, 'id')
			// 				};
							
			// 				stopAllRunningTimers(actionSetup, actionOptions, function (updated) {

			// 					if (typeof options.startTimeTrackingLink === 'function') {
			// 						options.startTimeTrackingLink();
			// 					}

			// 				});

			// 			});

			// 		}
			// 	}
			// });

		}

		ui.grid.makeNode('column', 'div', {
			css: 'ui sixteen wide column',
			style: 'padding-top:0 !important; padding-bottom:5px !important;'
		});

		ui.grid.column.makeNode('grid', 'div', {
			css: 'ui grid'
		});

		var collectionsSetup = {
			domObj: ui.grid.column.grid,
			state: state,
			objectType: 'time_entries',
			actions: {
				navigateTo: false,
				copy: false,
				comments: false,
				downloadCSV: true,
				download: false,
				archive: {
					id: 'archive',
					name: 'Archive',
					action: function(selection, onComplete) {
		
						var actionSetup = {
							object: appConfig.state.pageObject,
							state: appConfig.state,
							options: {}
						};
		
						var actionOptions = {
							selection: selection
						};
					
						deleteTimerAction(actionSetup, actionOptions, function(updated) {
		
							if (typeof onComplete === 'function') {
								onComplete(updated);
							}
		
						});
		
					}
		
				}
			},
			fields: {
				id: {
					title: 'ID',
					view: function (ui, obj) {
	
						var objectUID = '#' + obj.object_uid;
	
						if (ui) {
	
							ui.makeNode('row-' + obj.oid, 'div', {
								text: objectUID
							});
	
						} else {
	
							return objectUID;
	
						}
	
					}
				},
				user: {
					title: 'User',
					view: function (ui, obj) {
	
						if (ui) {
	
							sb.notify ({
								type: 'view-field',
								data: {
									type: 'users',
									property: 'staff',
									obj: obj,
									options: {
										edit: false,
										editing: false
									},
									ui: ui
								}
							});
	
						} else {
							
							if (obj && !_.isEmpty(obj.staff)) {
								return obj.staff.name;
							} else {
								return '--';
							}
	
						}
	
					}
				},
				shift: {
					title: 'Task',
					isPrimary: true
					, view: function(ui, obj){
						
						if (!obj.shift) {
							obj.shift = {};
						}
						obj.shift.object_uid = obj.object_uid;
						
						if (ui) {
	
							var shiftType = obj.shift.object_bp_type;
							if (shiftType === 'groups') {
								shiftType = obj.shift.group_type;
							}
							if (shiftType) {
								shiftType = shiftType.toLowerCase();
							}
	
							ui.makeNode('data','div',{
								text:obj.shift.name,
								tag:'a',
								css:'ui small header',
								href:sb.data.url.createPageURL(
									'object',
									{
										id: 			obj.shift.id,
										type: 			shiftType,
										object_type: 	obj.shift.object_bp_type,
										name: 			obj.shift.name
									}
								)
							}).notify('click', {
								type: 'timeClock-run',
								data: {
									run: function () {
				
										UI_CACHE.modal.hide();
				
									}
				
								}
				
							});
				
																
						} else {
							
							if (!_.isEmpty(obj.shift) && !_.isEmpty(obj.shift.name)) {
								return obj.shift.name;
							} else {
								return 'Not set';
							}
							
						}
						
					
					}
					, charLimit: 20
					, isSearchable: true
				},
				start_date: {
					title: 'Start',
					view: function (ui, obj) {
	
						var startDate = moment(obj.start_date).local().format('MMM D, h:mma');
	
						if (ui) {
	
							ui.makeNode('row-' + obj.oid, 'div', {
								text: startDate
							});
	
						} else {
	
							return startDate;
	
						}
	
					}
				},
				end_date: {
					title: 'End',
					view: function (ui, obj) {
	
						var endDate = obj.duration ? moment(obj.start_date).add(obj.duration, 'seconds').local().format('MMM D, h:mma') : '-';

						if (ui) {
	
							ui.makeNode('row-' + obj.oid, 'div', {
								text: endDate
							});
	
						} else {
	
							return endDate;
	
						}
	
					}
				},
				duration: {
					title: 'Duration',
					view: function (ui, obj) {
	
						var duration = sb.dom.durationDisplay(obj);
	
						if (ui) {
	
							ui.makeNode('row-' + obj.oid, 'div', {
								text: duration
								// text: parseSeconds(obj.duration).string
							});
	
						} else {
	
							return duration;
	
						}
	
					}
				},
				minutes:{
					title: 'Total Minutes',
					view: function(ui, obj) {
	
						var totalMinutes = Math.round(parseInt(sb.dom.durationDisplay(obj, 'min'))).toString();
	
						if (ui) {
	
							ui.makeNode('info','div',{
								text: totalMinutes
							});
						
						} else {
	
							return totalMinutes;
	
						}
						
					}
				},
				status: {
					title: 'Status',
					view: function (ui, obj) {
	
						displayRunningTimeBadge(ui, obj, state, options);
	
					}
				},
				note: {
					title: 'Note',
					type: 'detail',
					edit: true,
					charLimit: 20
				},
				buttons: {
					title: '',
					view: function (ui, obj) {
		
						if (obj.end_date && (obj.staff.id === +sb.data.cookie.userId)) {
		
							ui.makeNode('editBtn-' + obj.id, 'div', {
								text:'Edit', 
								css:'ui tiny fluid basic light-grey button'
							}).notify('click', {
								type:'timeClock-run',
								data:{
									run:function() {
		
										options.track = false;
										options.create = false;
										options.goBack = false;
										options.edit = true;
										options.inCollection = false;
										options.bypassSubmitTimeEntryPrompt = false;
										options.selectedEntry = _.clone(obj);

										if (options.inModal) {
											options.goBack = true;
										}

										viewEditFormModal(ui, obj, state, options, function(updated) {
		
											if (updated) {
		
												if (updated.hasOwnProperty('updated') && updated.updated) {
		
													Setup.updateCollections(updated.time_entry, TimeEntriesUpdateBP);
		
												}
		
											}
							
										});
		
									}
								}
							});
		
						}
		
					}
				}
			},
			singleView:{
				view: function(ui, obj, onReady, onComplete){
					if (obj.shift && obj.shift.id) {
	
						sb.notify({
							type: 'view-entity'
							, data: {
								ui: ui
								, id: obj.shift.id
							}
						});
	
					} else {
	
						ui.makeNode('msg', 'div', {
							css: 'ui warning icon message'
							, text: '<i class="ui exclamation icon"></i><div class="header">Looks like the item this was logged against has been erased.</div>'
						});
	
						// If the timer is still running, let the user delete it
						// (if its not running, we'll need to allow upstream updates 
						// to let the user delete w/out causing issues).
						if (
							!_.isEmpty(obj.start_date)
							&& _.isEmpty(obj.end_date)
						) {
	
							ui.makeNode('archive', 'div', {
								tag: 'button'
								, css: 'ui red fluid icon button'
								, text: '<i class="trash icon"></i> Archive this time entry'
							}).notify('click', {
								type: 	'timeClock-run'
								, data: {
									run: function () {
	
										sb.dom.alerts.ask(
											{
												title:  'Are you sure?'
												, text: 'This cannot be undone.' 
											}
											, function (resp) {
				
												if (resp) {
		
													swal.disableButtons();
													
													// Delete the time entry
													sb.data.db.obj.erase(
														'time_entries'
														, obj.id
														, function () {
	
															swal.close();
															
															if (typeof onComplete === 'function') {
																onComplete(true);
															}
	
														}
													);
													
												}
				
											}
										);
	
									}
								}
							}, sb.moduleId);
							
						}
						ui.patch();
	
					}
	
					
				}
				, link: function (n) {
					
					var link = '';
					if (n.shift && n.shift.id) {
						
						link = sb.data.url.createPageURL(
							'object',
							{
								id: 			n.shift.id,
								type: 			n.shift.object_bp_type,
								object_type: 	n.shift.object_bp_type,
								name: 			n.shift.name
							}
						);
						
					}
					
					return link;
					
				}																											
			},
			metrics: {
				sum: {
					title: 'Total',
					fields: {
						shift: function(ui, getData, test, data){
							
							if (!_.isUndefined(ui) && !_.isNull(ui) && data) {
								
								ui.empty();
								ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>'+ data.length +' total entries</b>'});
								ui.patch();	
																			
							}	
	
							if (data) {
								return data.length +' total entries';
							}		
							
						},
						minutes:function(ui, getData, test, data) {
	
							var totalAmount = 0;
							
							if (_.isArray(data)) {
							
								_.each(data, function(inv){										
	
									totalAmount += inv.duration;
									
								});
								
							} else {
								
								totalAmount = data;
								
							}	
	
							
							if (!_.isUndefined(ui) && !_.isNull(ui)) {
								
								ui.empty();
								ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>'+ (totalAmount/60).toFixed(0) +'</b>'});
								ui.patch();	
																			
							}	
								
							
							return (totalAmount/60).toFixed(0);							
					
						},
						duration: function(ui, getData, test, data) {
	
							var totalAmount = 0;
							
							if (_.isArray(data)) {
							
								_.each(data, function(inv){										
	
									totalAmount += inv.duration;
									
								});
								
							} else {
								
								totalAmount = data;
								
							}	
	
							var hours = 0;
							totalAmount = totalAmount / 60;
							while(totalAmount > 60){
								hours++;
								totalAmount = totalAmount - 60;
							}
							
							if(!_.isUndefined(ui) && !_.isNull(ui)){
								
								ui.empty();
								ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>'+ hours +' hrs '+ totalAmount.toFixed(0) +' minutes</b>'});
								ui.patch();	
																			
							}	
								
							
							return '<b>'+ hours +' hrs '+ totalAmount.toFixed(0) +' minutes</b>';							
					
						}
					}
				}
			},
			data: {
				add: function(callback) {
						
					Setup.addToCollections = callback;
						
				},
				remove: function(callback) {
					
					Setup.removeFromCollections = callback;
					
				},
				update: function(callback) {
	
					Setup.updateCollections = callback;
	
				}
			},
			onBoxview: true,
			selectedView: 'table',
			menu: {
				subviews: false
			},
			sortCol: 'start_date',
			templates: false,				
			groupings: {
				staff: 'Staff',
				service: 'Service',
				shift: 'Shift'
			},
			rangeOver: 'start_date',
			where: {
				childObjs: TimeEntriesBP
			}
		};

		return collectionsSetup;

	}
		
	// TIMECLOCK KEYPADUI		
	function build_keypad(dom, state, options, onComplete) {
// !(1) Build Keypad ///		

		var formArgs = {
				pin: {
					name: 'pin',
					type: 'text',
					placeholder: 'Enter your six digit PIN',
					css: 'timeClock-form'
				}
			};
		var keypad_layout = [
				{
					row: ['Back']
				},
				{
					row: [1, 2, 3]
				},
				{
					row: [4, 5, 6]
				},
				{
					row: [7, 8, 9]
				},
				{
					row: ['Clear', '0', 'Enter']
				}
			];
		var key_color = '';
		var key_text = '';
				
		function process_keyPress(formDom, key_value, load, after) {
// !(2) Process_keyPress ///
			var formData = formDom.form.process().fields;
			
			// CHECKS
			if(formData.pin.value.length === 6 && typeof key_value === 'number') {
				
				sb.dom.alerts.alert(
					'Error',
					'PIN can not be more or less than six digits',
					'error'
				);
				
				return;
				
			}

			if(key_value === '0') {
				key_value = 0;
			}
			
			if(typeof key_value === 'number') {
				
				formDom.makeNode('form', 'form', {
					pin: {
						name: 'pin',
						type: 'text',
						placeholder: 'Enter your six digit PIN',
						css: 'timeClock-form',
						value: formData.pin.value + key_value
					}
				});
				
			} else {
				
				switch(key_value) {
					
					case 'Back':
						
						formDom.makeNode('form', 'form', {
							pin: {
								name: 'pin',
								type: 'text',
								placeholder: 'Enter your six digit PIN',
								css: 'timeClock-form',
								value: formData.pin.value.substring(0, formData.pin.value.length - 1)
							}
						});
						
						break;
						
					case 'Clear':
					
						formDom.makeNode('form', 'form', {
							pin: {
								name: 'pin',
								type: 'text',
								placeholder: 'Enter your six digit PIN',
								css: 'timeClock-form',
								value: ''
							}
						});
						
						break;
						
					case 'Enter':
						
						if(formData.pin.value.length < 6) {
							
							sb.dom.alerts.alert(
								'Error',
								'PIN can not be more or less than six digits',
								'error'
							);
							
							return;
							
						}

						load();
						
						get_users(formData.pin.value, after);
						
						break;
						
					default:
					
						return;
					
				}
				
			}
			
			formDom.patch();
			
		}
		
		dom.makeNode('wrapper', 'div', {css: 'ui padded grid'});
		
		dom.wrapper.makeNode('col1', 'div', {css: 'four wide column'});
		dom.wrapper.makeNode('col2', 'div', {css: 'eight wide column'});
		dom.wrapper.makeNode('col3', 'div', {css: 'four wide column'});
		
		dom.wrapper.col2.makeNode('keypad_wrap', 'div', {css: 'keypad'});
		
		dom.wrapper.col2.keypad_wrap.makeNode('top', 'div', {css: 'ui stackable grid basic segment'});
		dom.wrapper.col2.keypad_wrap.makeNode('bottom', 'div', {});
		dom.wrapper.col2.keypad_wrap.makeNode('lb_1', 'lineBreak', {spaces: 1});
		dom.wrapper.col2.keypad_wrap.makeNode('forgot_wrap', 'div', {css: 'text-center'});
		dom.wrapper.col2.keypad_wrap.makeNode('lb_2', 'lineBreak', {spaces: 1});
		
		dom.wrapper.col2.keypad_wrap.top.makeNode('col1', 'div', {css: 'six wide column'});
		dom.wrapper.col2.keypad_wrap.top.makeNode('col2', 'div', {css: 'ten wide column'});
		
		// COL1
		dom.wrapper.col2.keypad_wrap.top.col1.makeNode('time_wrap', 'div', {css: 'text-center'});
		dom.wrapper.col2.keypad_wrap.top.col1.makeNode('date_wrap', 'div', {css: 'text-center'});
		
		dom.wrapper.col2.keypad_wrap.top.col1.time_wrap.makeNode('time', 'div', {text: '<h2>'+ moment().format('h:mm:ss A') +'</h2>'});
		dom.wrapper.col2.keypad_wrap.top.col1.date_wrap.makeNode('date', 'div', {text: '<h3>'+ moment().format('ddd MMMM Do, YYYY') +'</h3>', css: 'text-muted'});
		
		setInterval(function(timerNode) {
		
			timerNode.makeNode('time', 'div', {text: '<h2>'+ moment().add(1, 'second').local().format('h:mm:ss A') +'</h2>'});
			timerNode.patch();
			
		}.bind(this, dom.wrapper.col2.keypad_wrap.top.col1.time_wrap), 1000);

		// COL2
		dom.wrapper.col2.keypad_wrap.top.col2.makeNode('form', 'form', formArgs);
		
		_.each(keypad_layout, function(rowObj, row_i) {
			
			dom.wrapper.col2.keypad_wrap.bottom.makeNode('row-'+row_i, 'div', {css: 'ui equal width grid basic segment'});

			_.each(rowObj.row, function(keyNum, key_i) {

				dom.wrapper.col2.keypad_wrap.bottom['row-'+row_i].makeNode('col-'+key_i, 'container', {uiGrid: false, css: 'column'});
				
				key_text = keyNum;
				
				if(keyNum === 'Clear') {
					
					key_color = 'red';
					
				} else if(keyNum === 'Enter') {
					
					key_color = 'green';
					
				} else if(keyNum === 'Back') {
					
					key_color = 'grey';
					
				} else {
					
					key_color = 'secondary';
					
				}
				
				dom.wrapper.col2.keypad_wrap.bottom['row-'+row_i]['col-'+key_i].makeNode('key', 'div', {text: key_text, css: 'fluid ui massive '+ key_color +' button keypad-btn'}).notify('click', {
					type: 'timeClock-run',
					data: {
						run: function(data) {
							
// !(4) Route User clock_staff ///
															
							process_keyPress(
								dom.wrapper.col2.keypad_wrap.top.col2
								, keyNum
								, function() { return; }
								, function(foundStaff) {

									if(!_.isEmpty(foundStaff)) {
										
										clock_staff( foundStaff );
										
									} else {
										
										sb.dom.alerts.alert(
											'Error',
											'Not found. Please try again.',
											'error'
										);
									
										build_keypad(dom, state, options, onComplete); 
																													
										dom.patch();
											
									}									
								}
							);	
							
						}
					}
				}, sb.moduleId);
				
			}, this);
			
		}, this);
		
		dom.patch();
		
	}
	
	// TIMECLOCK PAGEUI LAYOUT
	function build_singleTimeClock(dom, state, draw) {
		
		DATA_CACHE.hq = state.hq;
		DATA_CACHE.team = state.team;
		DATA_CACHE.location = state.location;
				
		dom.empty();
		
		UI_CACHE.modal = dom.makeNode('modal', 'modal', {
			onClose: function() {
				
				DATA_CACHE.manager = {};
				DATA_CACHE.staffObj = {};
				DATA_CACHE.locationManager = false;
				DATA_CACHE.managerOverride = false;

			}
		});
		UI_CACHE.modal.body.makeNode('list', 'div', {});
		UI_CACHE.modal.body.makeNode('form', 'div', {});
		
		dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
		
		dom.wrapper.makeNode('head', 'div', {css: 'ui padded grid'});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 2});
		dom.wrapper.makeNode('body', 'div', {});
		dom.wrapper.makeNode('lb_2', 'lineBreak', {spaces: 2});
		
		dom.wrapper.head.makeNode('col1', 'column', {w: 12});
		dom.wrapper.head.makeNode('col2', 'column', {w: 4});
		
		dom.wrapper.head.col1.makeNode('title', 'div', {text: '<i class="fa fa-stopwatch-o"></i> Employee Time Clock <span class="text-muted"><small>'+ DATA_CACHE.location.name +'</small></span>', css: 'ui header', tag: 'h1'});
		
		dom.wrapper.head.col2.makeNode('btnGrp', 'div', {});
		
		dom.wrapper.head.col2.btnGrp.makeNode('exit_btn', 'div'
			, {
				text: '<i class="fa fa-sign-out"></i> Exit'
				, css: 'ui red basic button right floated'
			}
		).notify('click', {
			type: 'logout-user',
			data: {}
		}, sb.moduleId);
				
		draw({
			dom: dom,
			after: function(dom) {

				build_keypad(
					dom.wrapper.body
					, state
					, {}
					, function(){}
				);				
								
			}
		});	
		
	}

	// TIMECLOCK UTIL 
	function clock_staff( staff ) {
// !(5) clock_staff		

		var dom = UI_CACHE.modal;
		
		function process_clockOut(tipForm, timeEntry, load, after) {
// !(7A) process_clockout							

			var formData = tipForm.process().fields;
			
			load();

			timeEntry.tips = formData.tip.value;
			timeEntry.end_date = moment().format('YYYY-MM-DD HH:mm:ss');

			timeEntry.duration = moment(timeEntry.end_date).diff(moment(timeEntry.start_date), 'minutes');
			
			if(timeEntry.duration === 0) {
				
				timeEntry.duration += 1;
				
			}
			
			sb.data.db.obj.update('time_entries', timeEntry, function(res) {
			
				if(res) {
					
					after();
					
				}
				
			});
			
		}
		
		dom.body.empty();
		
		dom.body.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
		
		dom.body.wrapper.makeNode('head', 'div', {css: 'ui grid'});
		dom.body.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 2});
		dom.body.wrapper.makeNode('body', 'div', {});
		
		dom.body.wrapper.head.makeNode('col1', 'div', {css: 'ten wide column'});
		dom.body.wrapper.head.makeNode('col2', 'div', {css: 'six wide column'});
		
		dom.body.wrapper.head.col1.makeNode('title', 'div', {text: 'Hello, ' + staff.fname +' '+ staff.lname, tag: 'h2'});
		
		dom.body.wrapper.head.col2.makeNode('btnGrp', 'div', {});
		
		dom.body.wrapper.head.col2.btnGrp.makeNode('clock_btn', 'div', {text: 'loading', css: 'ui tiny button right floated loading'});
		
		dom.body.wrapper.head.col2.btnGrp.makeNode('close_btn', 'div', {text: '<i class="fa fa-times"></i> Close', css: 'ui tiny red button inverted right floated'}).notify('click', {
			type: 'timeClock-run',
			data: {
				run: function(data) {

					dom.hide();
					
					DATA_CACHE.managerOverride = false;
					DATA_CACHE.locationManager = false;
					DATA_CACHE.staffObj = {};
					DATA_CACHE.admin = {};

					
				}
			}
		}, sb.moduleId);
		
		display_loader({mainDom:dom.body.wrapper.body, text:'Fetching time entries for this location ...'});

		get_timeEntries(DATA_CACHE.location, staff, function(timeEntry) {

			if(timeEntry && !_.isEmpty(timeEntry)  && DATA_CACHE.managerOverride != true) { // IF CLOCKED IN
				
				var duration = sb.dom.durationDisplay(timeEntry, undefined);
				
				dom.body.wrapper.body.empty();
				
				dom.body.wrapper.body.makeNode('wrap', 'div', {css: 'ui equal width grid'});
				
				dom.body.wrapper.body.wrap.makeNode('col1', 'div', {css: 'column'});
				dom.body.wrapper.body.wrap.makeNode('col2', 'div', {css: 'column'});
				dom.body.wrapper.body.wrap.makeNode('col3', 'div', {css: 'column'});
				
				dom.body.wrapper.body.wrap.col1.makeNode('status_wrap', 'div', {});
				dom.body.wrapper.body.wrap.col1.makeNode('lb_1', 'lineBreak', {spaces: 1});
				dom.body.wrapper.body.wrap.col1.makeNode('shiftType_wrap', 'div', {});
				
				dom.body.wrapper.body.wrap.col2.makeNode('time_wrap', 'div', {});
				dom.body.wrapper.body.wrap.col2.makeNode('lb_1', 'lineBreak', {spaces: 1});
				dom.body.wrapper.body.wrap.col2.makeNode('duration_wrap', 'div', {});
				
				dom.body.wrapper.body.wrap.col3.makeNode('shiftEnd_wrap', 'div', {});
				dom.body.wrapper.body.wrap.col3.makeNode('lb_1', 'lineBreak', {spaces: 1});
				dom.body.wrapper.body.wrap.col3.makeNode('tip_wrap', 'div', {});
				
				dom.body.wrapper.body.wrap.col1.status_wrap.makeNode('status_title', 'div', {text: '<h3>Status</h3>', css: 'text-center text-muted'});
				dom.body.wrapper.body.wrap.col1.status_wrap.makeNode('status', 'div', {text: '<div class="ui green label">Clocked In</div>', css: 'text-center'});
				
				dom.body.wrapper.body.wrap.col1.shiftType_wrap.makeNode('type_title', 'div', {text: '<h3>Shift Type</h3>', css: 'text-center text-muted'});
				dom.body.wrapper.body.wrap.col1.shiftType_wrap.makeNode('type', 'div', {text: '<h3>'+ timeEntry.service.name +'</h3>', css: 'text-center'});
				
				dom.body.wrapper.body.wrap.col2.time_wrap.makeNode('time_title', 'div', {text: '<h3>Clocked In</h3>', css: 'text-center text-muted'});
				dom.body.wrapper.body.wrap.col2.time_wrap.makeNode('time', 'div', {text: '<h3>'+ moment(timeEntry.start_date).format('MM/DD/YY - h:mm A') +'</h3>', css: 'text-center'});
				
				dom.body.wrapper.body.wrap.col2.duration_wrap.makeNode('duration_title', 'div', {text: '<h3>Duration</h3>', css: 'text-center text-muted'});
				dom.body.wrapper.body.wrap.col2.duration_wrap.makeNode('duration', 'div', {text: '<h3>'+ duration +'</h3>', css: 'text-center'});
				
				dom.body.wrapper.body.wrap.col3.shiftEnd_wrap.makeNode('end_title', 'div', {text: '<h3>Shift Ends</h3>', css: 'text-center text-muted'});
				dom.body.wrapper.body.wrap.col3.shiftEnd_wrap.makeNode('end', 'div', {text: '<h3>'+ moment(timeEntry.shift.end_time).format('h:mm A') +'</h3>', css: 'text-center'});
				
				dom.body.wrapper.body.wrap.col3.tip_wrap.makeNode('tip_form', 'form', {
					tip: {
						name: 'tip',
						type: 'usd',
						css: 'text-center',
						label: 'Please enter tip amount'
					}
				});
				
				dom.body.wrapper.body.patch();
				
				dom.body.wrapper.head.col2.btnGrp.makeNode('clock_btn', 'div', {text: '<i class="fa fa-stopwatch-o"></i> Clock Out', css: 'ui tiny green button right floated'}).notify('click', {
					type: 'timeClock-run',
					data: {
						run: function(data) {

							process_clockOut(dom.body.wrapper.body.wrap.col3.tip_wrap.tip_form, timeEntry, function() {
								
								dom.body.wrapper.body.empty();
								
								display_loader({mainDom:dom.body.wrapper.body, text:'Updating time entry ...'});
								
								dom.body.wrapper.body.patch();
								
							}, function() {
								
								dom.hide();
								
								sb.dom.alerts.alert(
									'You are clocked out.',
									'',
									'success'
								);
								
							});
							
						}
					}
				}, sb.moduleId);
				
				dom.body.wrapper.head.col2.btnGrp.patch();
				
			} else { // IF CLOCKED OUT
				
				/*
dom.body.wrapper.body.empty();
				
				display_loader({mainDom:dom.body.wrapper.body, text:'Fetching staff schedules for this location ...'});
				
				dom.body.wrapper.body.patch();
*/

				//get_staffSchedules(DATA_CACHE.location, function(schedules) {

					//if(!_.isEmpty(schedules)) {
						
						dom.body.wrapper.body.empty();
				
						display_loader({mainDom: dom.body.wrapper.body, text: 'Fetching shifts...'});
						
						dom.body.wrapper.body.patch();
						
						//get_shifts(schedules, staff, DATA_CACHE, function(shiftList) {

						get_shifts([], staff, DATA_CACHE, function(shiftList) {
						
							/*
if(typeof shiftList === 'object') {
								
								shiftList = _.filter(shiftList, function(obj) {
									
									if(moment().unix() < moment(obj.end_date).unix()) {
										return obj;
									}
									
								});
								
							}
*/

							if(!_.isEmpty(shiftList)) {
								
								var selectedShift = {};
								var body = dom.body.wrapper.body;
									
								dom.body.wrapper.head.col2.btnGrp.makeNode('clock_btn', 'div', {text: '<i class="fa fa-stopwatch-o"></i> Clock In', css: 'ui tiny button right floated'}).notify('click', {
									type: 'timeClock-run',
									data: {
										run: function(data) {
											
											if(_.isEmpty(selectedShift)) {
												
												sb.dom.alerts.alert(
													'Error',
													'Please select a shift to work',
													'error'
												);
												
												return;
												
											}
											
											/*
if(DATA_CACHE.managerOverride) {
												
												state.user = state.staffObj;
												
											} else {
												
												state.user = state.manager;
												
											}
*/

											create_timeEntry.call(DATA_CACHE.staffObj, selectedShift, function() {
												
												dom.body.empty();
												
												display_loader({ mainDom:dom.body, text:'Creating time entry ...'});
												
												dom.body.patch();
												
											}, function() {
										
												sb.dom.alerts.alert(
													'Done!',
													'Have a good shift, '+ staff.fname +'!',
													'success'
												);
												
												dom.hide();
												
												DATA_CACHE.staffObj = {};
												DATA_CACHE.manager = {};
												DATA_CACHE.locationManager = false;
												DATA_CACHE.managerOverride = false;
												
											});
											
										}
									}
								}, sb.moduleId);
								
								dom.body.wrapper.head.col2.btnGrp.patch();
								
								body.empty();
								
								body.makeNode('status_wrap', 'div', {});
								
								body.status_wrap.makeNode('status_title', 'div', {text: '<h2>Status</h2>', css: 'text-center text-muted'});
								body.status_wrap.makeNode('status', 'div', {text: '<div class="ui orange label">Not Clocked In</div>', css: 'text-center'});
								
								if(DATA_CACHE.staffObj && DATA_CACHE.locationManager === true) {
								
									body.makeNode('available', 'div', {text: 'All shifts', tag: 'h3'});
								
								} else {
									
									body.makeNode('available', 'div', {text: 'Available shifts', tag: 'h3'});	
									
								}
								
								body.makeNode('cards_wrap', 'div', {css: 'ui raised cards'})
								
								_.each(shiftList, function(shift) {
									
									var shiftAssignee = 'Not Assigned';
								
									if(shift.user != null && shift.user != undefined){
										
										if(shift.user.hasOwnProperty('fname') && shift.user.hasOwnProperty('lname')){
											
											shiftAssignee = shift.user.fname +' '+ shift.user.lname;
											
										}
										
									}
									
									body.cards_wrap.makeNode('card-'+shift.id, 'div', {css: 'card'});
									
									body.cards_wrap['card-'+shift.id].makeNode('card_content', 'div', {css: 'content'});
									
									body.cards_wrap['card-'+shift.id].card_content.makeNode('title', 'div', {text: shift.job_type.name, css: 'center aligned header'});
									
									body.cards_wrap['card-'+shift.id].card_content.makeNode('lb_1', 'lineBreak', {spaces: 1});
									
									body.cards_wrap['card-'+shift.id].card_content.makeNode('assignee_header', 'div', {text: '<h4><i class="fa fa-user"></i> Assignee</h4>', css: 'text-center text-muted'});
									body.cards_wrap['card-'+shift.id].card_content.makeNode('assignee', 'div', {text: '<h4>'+ shiftAssignee +'</h4>', css: 'text-center', style:'margin-bottom:0.5em!important;'});
									
									body.cards_wrap['card-'+shift.id].card_content.makeNode('time_title', 'div', {text: '<h4><i class="fa fa-calendar"></i> Time</h4>', css: 'text-center text-muted'});
									body.cards_wrap['card-'+shift.id].card_content.makeNode('date', 'div', {text: '<h4>'+ moment(shift.start_date).format('dddd MMMM Do, YYYY') +'</h4>', css: 'text-center'});
									body.cards_wrap['card-'+shift.id].card_content.makeNode('time', 'div', {text: '<h4>'+ moment(shift.start_date).format('h:mm A') + ' - ' + moment(shift.end_date).format('h:mm A') +'</h4>', css: 'text-center'});
									
									body.cards_wrap['card-'+shift.id].card_content.makeNode('lb_2', 'lineBreak', {spaces: 1});
									
									body.cards_wrap['card-'+shift.id].card_content.makeNode('details_title', 'div', {text: '<h4><i class="fa fa-sticky-note"></i> Shift Details</h4>', css: 'text-center text-muted'});
									
									if(shift.description.length > 0) {
										
										body.cards_wrap['card-'+shift.id].card_content.makeNode('lb_3', 'lineBreak', {spaces: 1});
										
										body.cards_wrap['card-'+shift.id].card_content.makeNode('details', 'div', {text: shift.description});
										
									} else {
										
										body.cards_wrap['card-'+shift.id].card_content.makeNode('details', 'div', {text: '<h4>No details</h4>', css: 'text-center'});	
										
									}
									
									body.cards_wrap['card-'+shift.id].makeNode('btn', 'div', {text: 'Select to work', css: 'ui bottom attached button'}).notify('click', {
										type: 'timeClock-run',
										data: {
											run: function(data) {
												
												if(body.cards_wrap['card-'+shift.id].btn.selected === false) {
													
													selectedShift = shift;
													
													body.cards_wrap['card-'+shift.id].btn.selected = true;
													
													$(body.cards_wrap['card-'+shift.id].btn.selector).text('Selected');
													$(body.cards_wrap['card-'+shift.id].btn.selector).addClass('positive');
													$(dom.body.wrapper.head.col2.btnGrp.clock_btn.selector).addClass('green');
													
												} else {
													
													selectedShiftId = {};
													
													body.cards_wrap['card-'+shift.id].btn.selected = false;
													
													$(body.cards_wrap['card-'+shift.id].btn.selector).text('Select to work');
													$(body.cards_wrap['card-'+shift.id].btn.selector).removeClass('positive');
													$(dom.body.wrapper.head.col2.btnGrp.clock_btn.selector).removeClass('green');
													
												}

												if(shiftList.length > 1) {
													
													_.each(shiftList, function(obj) {
													
														if(obj.id !== data.id) {
															
															body.cards_wrap['card-'+obj.id].btn.selected = false;
															body.cards_wrap['card-'+obj.id].btn.text('Select to work');
															$(body.cards_wrap['card-'+obj.id].btn.selector).removeClass('positive');
															
														}
														
													}, this);
													
												} 
												
											},
											id: shift.id
										}
									}, sb.moduleId);
									
									body.cards_wrap['card-'+shift.id].btn.selected = false;
									
								}, this);
								
								body.patch();
					
							} else {
										
								dom.body.wrapper.body.empty();
					
								dom.body.wrapper.body.makeNode('no_scheds', 'div', {text: '<i class="fa fa-info-circle"></i> Sorry, you have not been scheduled for any shifts for this location at this time.', css: 'ui message'});
																
								dom.body.wrapper.head.col2.btnGrp.makeNode('clock_btn', 'div', {css:'tiny ui inverted right floated '+ state.managerButtonColor +' button', text: state.lockIcon +' Manage Shifts'}).notify('click', {
									type:'timeClock-run',
									data:{
										
										run:function(){
											
											state.managerOverride = true;
											
											dom.body.wrapper.head.empty();
											dom.body.wrapper.body.empty();
											
											if(state.locationManager === true) {
												
												state.staffObj = state.manager;
												
												clock_staff(state.manager, state);
												
											} else {
											
												build_keypad(dom.body, state);
												
											}
											
											dom.body.patch();
											dom.show();
											
										}
										
									}
								});
								
								dom.body.wrapper.head.col2.btnGrp.patch();						
								dom.body.wrapper.body.patch();
								
							}
							
						});
						
					/*
} else {
						
						delete dom.body.wrapper.head.col2.btnGrp.clock_btn;
						
						dom.body.wrapper.body.empty();
						
						dom.body.wrapper.body.makeNode('no_scheds', 'div', {text: '<i class="fa fa-info-circle"></i> Sorry, there are no schedules found for this location at this time.', css: 'ui message'});
						
						dom.body.wrapper.head.col2.btnGrp.patch();						
						dom.body.wrapper.body.patch();
						
					
*/
//}
					
				//});
				
			}
			
		});
		
		dom.body.patch();
		dom.show();
		
	}
	function create_timeEntry(shift, load, after) {
// !@TODO refac for time entry action		
		var obj = {};
		var	unscheduledUser = this;
		
		load();
		
		//obj.staff = shift.user.id;
		obj.shift = shift.id;
		obj.service  = shift.job_type.id;
		obj.start_date = moment().format('YYYY-MM-DD HH:mm:ss');
		obj.duration = 0;
		obj.location = DATA_CACHE.location.id;
		
		if(shift.user != null && !this.hasOwnProperty('id')){
			
			obj.staff = shift.user.id;
			
			sb.data.db.obj.create('time_entries', obj, function(ret) {
				
				after(ret);
				
			}, 2);
			
		}else{
			
			obj.staff = this.id;
			
			sb.data.db.obj.update('groups', {id:shift.id, user:unscheduledUser.id}, function(response){
				
				sb.data.db.obj.create('time_entries', obj, function(ret) {
					
					after(ret);
					
				}, 2);
				
			}, 2);
			
		}
		
	}
	function display_loader(setup) {
				
		// SETUP { 
			// mainDom: dom to display loader,
			// empty: boolean, always set to true unless it is turned off (optional),
			// patch: boolean, always set to true unless it is turned off (optional),
			// text: text that will be displayed with loader
		//}
		
		if(setup.empty !== false) {
			setup.mainDom.empty();	
		}
		
		setup.mainDom.makeNode('cont', 'div', {});
		
		setup.mainDom.cont.makeNode('loader', 'loader', {});
		setup.mainDom.cont.makeNode('load_text', 'div', {text: setup.text, css: 'text-center'});
		
		if(setup.patch !== false) {
			setup.mainDom.patch();	
		}
		
	}	
	function get_users( pin, callback ) {
// !(3) get users staff, manager, admin		

		if( DATA_CACHE.managerOverride === false ) {
			
			DATA_CACHE.staffObj = {};
			DATA_CACHE.admin = {};
			DATA_CACHE.locationManager = false;
			
		}

		sb.data.db.controller(
			'getObjectsWhere&api_webform=true&pagodaAPIKey='+ appConfig.instance
			, {
				queryObj:{
					pin:pin
				}
				, instance:appConfig.instance
				, objectType:'users'
			}				
			, function(usersResp){

				if (usersResp[0] && usersResp[0].hasOwnProperty('id')) 
					validateUser(usersResp[0]);

				callback( usersResp[0] );	
							
			}
		);	
		
	}
	
	function getBreakoutSums (entries, onComplete) {
		
		var tags = [];
		_.each(entries, function (entry) {
			
			tags = tags.concat(entry.tagged_with);
			
		});
		tags = _.uniq(tags);
		
		if (!_.isEmpty(tags)) {
			
			sb.data.db.obj.getById(
				''
				, tags
				, function (tags) {
					
					var grouped = _.groupBy(tags, function (tag) {
						
						if (tag.object_bp_type === 'groups') {
							
							return tag.group_type;
							
						} else {
							
							return tag.object_bp_type;
							
						}
						
					});
					
					_.each(grouped, function (tags, key) {
						
						switch (key) {
							
							case 'JobType':
							case 'Team':
							case 'system_tags':
							case 'users':
								_.each(tags, function (tag) {
									
									switch (tag.object_bp_type) {
										
										case 'users':
											tag.name = tag.fname +' '+ tag.lname;
											tag.icon = 'user';
											break;
										
										case 'system_tags':
											tag.name = tag.tag;
											tag.icon = 'hashtag';
											break;
										
										case 'groups':
											switch (tag.group_type) {
												case 'JobType':
													tag.icon = 'wrench';
													break;
												
												case 'Team':
													tag.icon = 'users';
													break;
											}
											break;
										
									}
									
									tag._time_logged = _.reduce(
										entries
										, function (memo, entry) {
											
											if (
												_.contains(entry.tagged_with, tag.id)
												&& entry.duration
											) {
												memo += entry.duration;
											}
											return memo;
											
										}
										, 0
									);
									
								});
								
								grouped[key] = _.sortBy(tags, function (t) { return -t._time_logged; });
								break;
							
							default:
								delete grouped[key];
								break;
							
						}
						
					});
					
					onComplete(grouped);
					
				}
				, {
					name: true
					, color: true
				}
			);
			var breakout = {};
			
			
		} else {
			
			onComplete({});
			
		}
		
	}
	
	function get_timeEntries(location, staff, callback) {

// !(6) get_timeEntries		
		var start = moment().startOf('day').subtract(6, 'hours').unix();
		var end = moment().endOf('day').add(6, 'hours').unix();
		var queryObj = {
				location: location.id,
				//duration: 0,
				start_date: {
					type: 'between',
					start: start,
					end: end
				},
				instance: appConfig.instance
				, childObjs: 1	
		    };

		if(staff) {
			queryObj.staff = staff.id;
		}

		sb.data.db.controller(
			'getObjectsWhere&api_webform=true&pagodaAPIKey='+ appConfig.instance
			, {
				queryObj:queryObj
				, instance:appConfig.instance
				, objectType:'time_entries'
			}				
			, function(timeEntriesRes){

				if(!_.isEmpty(timeEntriesRes)) {
					
					if(staff) {
						callback(timeEntriesRes[0]);
					} else {
						callback(timeEntriesRes);
					}
					
				} else {
					
					callback(false);
					
				}
			
			}
		);		
		
	}
	
	function get_staffSchedules(location, callback) {
// !(7B) get_staffSchedules
		var start = moment().startOf('day').unix();
		var end = moment().endOf('day').unix();		

		/*
sb.data.db.obj.getWhere('groups', {
			group_type: 'Schedule'
			, location: location.id
			, start_date: {
				type: 	'between'
				, start: 	start
				, end: 	end
			}
			, childObjs: {
				name: true
				, start_date: true
			}
		}, function(schedules) {
		
			callback(schedules);	
		
		});
*/
		
		sb.data.db.controller(
			'getObjectsWhere&api_webform=true&pagodaAPIKey='+ appConfig.instance
			, {
				queryObj:{
					group_type: 	'Schedule'
					, start_date: {
						type: 	'between'
						, start: 	start
						, end: 	end
					}
				}
				, getChildObjs: {
					id: 			true
					, name: 		true
					, start_date: 	true
					, job_type: 	true
				}						
				, instance: appConfig.instance
				, objectType: 'groups'
			}				
			, function(schedules){

				callback(schedules);	
					
			}
			, sb.url + '/api/_getAdmin.php?do='
		);	
		
	}
	
	function get_shifts(schedules, staff, state, callback) {

// !(8) get_shifts
		
		// Using state to switch from getting a single user's shifts to getting all shifts

		var start = moment().utc().local().startOf('day').unix();
		var end = moment().utc().local().endOf('day').unix();
	
		var queryObject = {
				queryObj:{
					group_type: 	'Shift'
					, user: 		staff.id
					, start_date: {
						type: 	'between'
						, start: 	start
						, end: 	end
					}
				}
				, getChildObjs: {
					id: 			true
					, name: 		true
					, start_date: 	true
					, end_date: 	true
					, job_type: 	true
					, user: 		true
					, category: 	true
					, is_active: 	true
					, status: 	true
					, description: true
				}						
				, instance:appConfig.instance
				, objectType:'groups'			
			};
			
		if(!state.managerOverride){
			queryObject.user = staff.id;
		}

		sb.data.db.controller(
			'getObjectsWhere&api_webform=true&pagodaAPIKey='+ appConfig.instance
			, queryObject				
			, function(shifts){

				callback(shifts);
							
			}
			, sb.url + '/api/_getAdmin.php?do='
		);		
		
	}
	
	function validateUser ( user ) {

		var isAdmin = _.contains(DATA_CACHE.hq.managers, user.id);
		var isLocationManager = _.contains(DATA_CACHE.team.managers, user.id)

		if ( isAdmin || isLocationManager ) {
			
			DATA_CACHE.admin = ( isAdmin ) ? user : false;
			DATA_CACHE.locationManager = ( isLocationManager ) ? user : false;
			DATA_CACHE.managerOverride = true;
			
		} else {
			
			DATA_CACHE.staffObj = user;
		}
	
		return user;
	}
	
/*
	state {
		toggle:true
		, progress:true
	}
	
	options:{
		toggle:{
			runningTime:true
		}
	}
*/

	///ENTRY - time tracking navbar view
	function navbarDisplay(ui, entries) {
		
		UI_CACHE.navbar_badge = ui;
		
		UI_CACHE.navbar_badge.refresh = navbarBadgeDisplay.bind(null, UI_CACHE.navbar_badge);
		
		if (_.isEmpty(UI_CACHE.modal)) {
			UI_CACHE.modal = ui.makeNode('modal', 'modal', {});
			UI_CACHE.modal.body.makeNode('list', 'div', {});
			UI_CACHE.modal.body.makeNode('form', 'div', {});
		}
		
		UI_CACHE.navbar_badge.refresh(entries);
						
		ui.patch();		
				
	}
	
	function navbarBadgeDisplay(ui, list) {
		
		if (list) {
			
			var isRunning = false;
			var iconText = '<i class="large stopwatch icon" style="margin:0 !important;"><div style="top:2em !important; left:85% !important;"></div></i>';
			
			isRunning = _.find(list, function(entry) {
	
				if (entry.end_date == '' && entry.duration == 0)
					return entry;				
					
			});

			if (isRunning) {
				iconText = '<i class="large stopwatch red icon animated flash slow" style="margin:0 !important;"><div style="top:2em !important; left:85% !important;"></div></i>';
			}

			$(ui.selector).off().on('click', function(event) {
				viewTimerNavbarModal(UI_CACHE.modal);
				UI_CACHE.modal.show();
			});

			ui.makeNode('icon', 'div', {
				text:     iconText,
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data: {
					run: function(data) {

					}
				}
			}, sb.moduleId);	

			if (isRunning) {

				if (badgeInt == 0) {
				
					badgeInt = setInterval(function(badgeIcon){
	
						$(badgeIcon.selector).toggleClass('animated flash slow');					
						
					}, 60000, ui.icon);						
					
				}		
		
			} else {
				
				clearInterval(badgeInt);
				badgeInt = 0;
			}

		} else {

			ui.makeNode('icon', 'div', {
				tag:	  	  'i' 
				, css: 'large stopwatch icon'
				, style: 'margin:0 !important'
				, listener: {
					type:	  'popup'
					, inline:   false
					, on:	  'click'
					, position: 'bottom center'
					, closable: true
					, offset:   -170
				}
			});
			
		}
			
		ui.icon.notify('click', {
			type:   'timeClock-run'
			, data: {
				run: function () {}
			}
		}, sb.moduleId);
		
		ui.patch();
		
	}
	
	function viewTimerNavbarModal(ui) {

		var modal = ui;
		ui = modal.body.list;

		$(modal.body.form.selector).hide();
		$(modal.body.list.selector).show();

		modal.body.form.empty();
		modal.body.list.empty();

		var collectionsSetup = getCollectionsSetup(ui, appConfig.user, {
			inModal: true,
			timeSheetLink: true,
			callback: UpdateCollectionCallback
		});
		delete collectionsSetup.fields.user;
		collectionsSetup.pageLength = 10;
		collectionsSetup.where.staff = +sb.data.cookie.userId;
		collectionsSetup.where.tagged_with = +sb.data.cookie.userId;
	
		sb.notify({
			type: 'show-collection',
			data: collectionsSetup
		});

		ui.patch();
		
	}

	function viewRunningTimersModal(ui, fieldName, callback) {

		var modal = ui;
		ui = modal.body.list;

		$(modal.body.form.selector).hide();
		$(modal.body.list.selector).show();

		modal.body.form.empty();
		modal.body.list.empty();

		var collectionsSetup = getCollectionsSetup(ui, appConfig.user, {
			inModal: true,
			startTimeTrackingLink: callback,
			callback: function(updated) {

				Setup.removeFromCollections(updated.time_entry);

				if ( $(ui.selector + ' table > tbody > tr').length === 0 ) {
					callback();
				}
		
			}

		});
		delete collectionsSetup.fields.user;
		collectionsSetup.actions = false;
		collectionsSetup.pageLength = 10;
		collectionsSetup.where.staff = +sb.data.cookie.userId;
		collectionsSetup.where.tagged_with = +sb.data.cookie.userId;
		collectionsSetup.where.duration = {
			type: 'not_set'
		}
		collectionsSetup.where.field_name = {
			type: 'not_equal',
			value: fieldName
		}
	
		sb.notify({
			type: 'show-collection',
			data: collectionsSetup
		});

		ui.patch();
		
	}
	
	//ENTRY - time tracking field view
	function fieldView(ui, obj, state, opt) {
		
		if (appConfig.instance !== obj.instance) {
			opt.edit = false;
		}
		
		var gridOptions = {
			css: (opt.compact) ? 'ui center aligned two column grid' : 'ui grid'
		};
		
		ui.empty();
		
		var containerCss = '';
		var containerStyle = '';

		if (obj.group_type === 'Project') {
			state.fieldName = 'time_logged';
		}
		
		if (state && state.fieldName && !opt.mini) {
			containerCss = 'edge-field';
			containerStyle = 'padding-top:8px;padding-bottom:8px;';
		}
		
		ui.makeNode('c', 'div', {
			css: containerCss,
			style: containerStyle
		}).makeNode('grid' , 'div',
			gridOptions
		);
		
		UI_CACHE.field = ui.c;

		if (_.isEmpty(UI_CACHE.modal)) {
			UI_CACHE.modal = ui.makeNode('modal', 'modal', {});
			UI_CACHE.modal.body.makeNode('list', 'div', {});
			UI_CACHE.modal.body.makeNode('form', 'div', {});
		}

		ui.patch();
		
		state.ui = ui;

		if ((opt.inCollection && !opt.mini) || opt.isMetric || opt.edit == false) {

			plainTextMode(ui.c, obj, state, opt);

		} else {
		
			if (state.toggleDisplay) {
				toggleMode(ui.c, obj, state, opt);
			}

		}
				
	}

	function plainTextMode(ui, obj, state, opt) {

		if (opt === undefined) {
			opt = {};
		}

		var css = '';
		
		var timeLoggedProperty = getPropertyNames(obj, state).timeLoggedProperty;
		var timeEstimateProperty = getPropertyNames(obj, state).timeEstimateProperty;

		if (opt.isMetric) {
			timeLoggedProperty = 'logged'
			timeEstimateProperty = 'est'
		}
			
		var timeColor = 'text-muted';
		if (
			obj[timeEstimateProperty] > 0
			&& obj[timeLoggedProperty] > obj[timeEstimateProperty]
		) {
			timeColor = 'ui red tiny red header';
		}
		
		var logText = '<span class="'+ timeColor +'">' + formatTimeDisplay(obj[timeLoggedProperty]) + '</span>';
		var estText = '';
		
		if (obj[timeEstimateProperty] > 0) {
			estText = '<span class="text-muted"> / '+ formatTimeDisplay(obj[timeEstimateProperty]) + '</span>';
		}
						
		ui.makeNode('disp' , 'div', {
			css: css,
			text: logText + estText
		});

		ui.patch();

	}
	
	function progressMode(ui, obj, state, opt) {

        function init(ui, listener, state) {
			
            function viewEditEstimatePopup(popup, ui, obj, state, options, position) {

				$(document).ready(function() {

					position = !_.isEmpty(position) ? position : 'bottom left';

					$(ui.selector).popup({
						on: 'click',
						popup: $(popup.selector),
						position: position,
						forcePosition: true,
						exclusive: true,
						onShow: function() {

							var timeLoggedProperty = getPropertyNames(obj, state).timeLoggedProperty;
							var timeEstimateProperty = getPropertyNames(obj, state).timeEstimateProperty;

							var timeEstimateDuration = 0;

							popup.empty();

							popup.makeNode('header', 'div', {
								style: 'padding-top:0 !important; margin-bottom:5px !important; text-align:left;',
								text: 'Estimated Time',
								tag: 'label'
							});
							
							sb.notify({
								type: 'view-field',
								data: {
									type: 'duration',
									property: timeEstimateProperty,
									ui: popup.makeNode('duration', 'div', {
										css: 'ui field field-value round-border', 
										style: 'height:38px; min-width:100px;'
									}),
									obj: obj,
									options: {
										edit: true,
										type: 'picker',
										style: 'width:100% !important;',
										formatAsSeconds: true,
										onUpdate: function(val) {

                                            timeEstimateDuration = val;
										}
									}
								}
							});
		
							// Save
							popup.makeNode('saveBtn', 'div', {
								text: 'Save', 
								css: 'ui tiny fluid green button',
								style: 'margin-top:8px;'
							}).notify('click', {
								type: 'timeClock-run',
								data: {
									run: function () {
				
										popup.saveBtn.loading();
																
										var actionSetup = {
											object: obj,
											state: state,
											options: options
										}
																
										var actionOptions = {
											time_estimate: timeEstimateDuration,
											tagged_with: [obj.id, +sb.data.cookie.userId]
										};	
										
										if (obj.parent) {
											if (obj.parent.hasOwnProperty('id')) {
												actionOptions.tagged_with.push(obj.parent.id);
											} else {
												actionOptions.tagged_with.push(obj.parent);
											}
										}
										
										actionOptions.tagged_with = _.uniq(actionOptions.tagged_with);	
										
										timeTrackAction(actionSetup, actionOptions, function (updated) {

                                            popup.saveBtn.loading(false);
											
											if (typeof onComplete === 'function') {
												callback(updated);
											}
																		
										});
		
									}
								}
							});

							popup.patch();

							setTimeout(function() {
								$(popup.duration.selector + ' input').focus();
							}, 100);

						}
					});

				});

			}
            
			function viewEditRatePopup(popup, ui, obj, state, options, position) {

                $(document).ready(function() {

					position = !_.isEmpty(position) ? position : 'bottom left';

					$(ui.selector).popup({
						on: 'click',
						popup: $(popup.selector),
						position: position,
						forcePosition: true,
						exclusive: true,
						onShow: function() {
							
                            var rateValueProperty = getPropertyNames(obj, state).rateValueProperty;

                            var timeRateValue = 0;

                            popup.empty();

							popup.makeNode('header', 'div', {
								style: 'padding-top:0 !important; margin-bottom:5px !important; text-align:left;',
								text: 'Rate Value',
								tag: 'label'
							});
							
							sb.notify({
								type: 'view-field',
								data: {
									type: 'currency',
									property: rateValueProperty,
									ui: popup.makeNode('ratevalue', 'div', {
										css: 'ui field field-value', 
										style: 'height:38px; min-width:171px;'
									}),
									obj: obj,
									options: {
                                        edit: true,
                                        editing: true,
                                        style: 'width:100% !important;',
                                        onChange: function(val) {
                                            timeRateValue = val;
										}
									}
								}
							});
		
							// Save
							popup.makeNode('saveBtn', 'div', {
								text: 'Save', 
								css: 'ui tiny fluid green button',
								style: 'margin-top:8px;'
							}).notify('click', {
								type: 'timeClock-run',
								data: {
									run: function () {
 
                                        popup.saveBtn.loading();

										var actionSetup = {
											object: obj,
											state: state,
											options: options
										}

										var actionOptions = {
											rate: timeRateValue,
											tagged_with: [obj.id, +sb.data.cookie.userId]
										};	
										
										if (obj.parent) {
											if (obj.parent.hasOwnProperty('id')) {
												actionOptions.tagged_with.push(obj.parent.id);
											} else {
												actionOptions.tagged_with.push(obj.parent);
											}
										}
							
										actionOptions.tagged_with = _.uniq(actionOptions.tagged_with);	
		
										timeTrackAction(actionSetup, actionOptions, function (updated) {

											popup.saveBtn.loading(false);

											$(ui.selector).popup('hide');
											
											if (typeof onComplete === 'function') {
												callback(updated);
											}
																		
										});
											
									}
								}
							});

							popup.patch();

                            setTimeout(function() {
								$(popup.ratevalue.selector + ' active').focus();
							}, 100);

						}
					});

				});

			}

			var timeLoggedProperty = getPropertyNames(obj, state).timeLoggedProperty;
			var timeEstimateProperty = getPropertyNames(obj, state).timeEstimateProperty;
            var rateValueProperty = getPropertyNames(obj, state).rateValueProperty;
					
			var isRunning = state.hasOwnProperty('time_entry') && state.time_entry.length > 0 ? 1 : 0;
			
			var timerRightSectionCSS = isRunning && sb.dom.isMobile ? 'ten wide column' : '';
			
			if (!opt.mini) {

				var style = sb.dom.isMobile ? 'padding-left:0; padding-right:0; cursor:pointer; padding-top:0; padding-bottom:0;' : 'cursor:pointer; padding-top:0; padding-bottom:0; flex:1 1 auto; flex-direction:column;';
				ui.makeNode('mode' , 'div', {
					css: timerRightSectionCSS,
					style: style
				});
				
				ui.mode.makeNode('grid' , 'div', {
					css: 'ui grid'
				});

			}
			
			// Show progress bar
			if (opt.showProgressBar) {

				var style = sb.dom.isMobile ? 'padding-left:0; padding-right:0; padding-bottom:3px' : 'padding-top:0; padding-bottom:0px';
				ui.mode.grid.makeNode('containerTop' , 'div', {
					css: 'sixteen wide column',
					style: style
				});

				var percent = calcPerc(obj, state);
				
				if (percent > 100) {
					percent = 100;
				}

				var css = '';
				if (percent > 0 && percent < 100) {
					css = 'warning';
				} else if (percent == 100) {
					css = 'success';
				}
				ui.mode.grid.containerTop.makeNode('progress', 'div', {
					css: 'ui light-grey progress ' + css,
					style: 'margin-bottom:2px !important;',
					tooltip: 'Click to view, edit, & add time logs',
					tooltipPos: 'top center',
					data: {
						'data-percent': percent
					}
				});
				
				ui.mode.grid.containerTop.progress.makeNode('bar', 'div', {
					css: 'bar',
					style: 'width:' + percent + '%;'
				});
				
				ui.mode.grid.containerTop.progress.bar.makeNode('progress', 'div', {
					css: 'progress',
					text: percent + '%'
				});

				var style = sb.dom.isMobile ? 'padding-left:0px; padding-top:0px; padding-bottom:0px;' : 'padding-top:0px; padding-bottom:0px;';
				ui.mode.grid.makeNode('containerBottom' , 'div', {
					css: 'sixteen wide column',
					style: style
				});

				var logText = (opt.compact)
					? '<small><span class="">' + formatTimeDisplay(obj[timeLoggedProperty]) + '</span></small>'
					: '<span style="display:block; float:left;"><span style="color:#848484;">Logged:</span> <strong>' + formatTimeDisplay(obj[timeLoggedProperty]) + '</strong></span><span style="display:block; float:left; color:#848484; font-size:12px; margin-left:5px;"><i class="ui pencil icon" style="margin-right:0;"></i></span><div style="clear:both"></div>';

				var estText = (opt.compact) 
					? '<small><span class="text-muted">'+ formatTimeDisplay(obj[timeEstimateProperty]) + '</span></small>'
					: '<span style="display:block; float:left;"><span style="color:#848484;">Est:</span> <strong>' + formatTimeDisplay(obj[timeEstimateProperty]) + '</strong></span><span style="display:block; float:left; color:#848484; font-size:12px; margin-left:5px;"><i class="ui pencil icon" style="margin-right:0;"></i></span><div style="clear:both"></div>';
				
				var rateText = (opt.compact) 
					? '<small><span class="text-muted">'+ sb.dom.formatCurrency(obj[rateValueProperty]) + '</span></small>'
					: '<span style="display:block; float:left;"><span style="color:#848484;">Rate:</span> <strong>' + sb.dom.formatCurrency(obj[rateValueProperty]) + '</strong></span><span style="display:block; float:left; color:#848484; font-size:12px; margin-left:5px;"><i class="ui pencil icon" style="margin-right:0;"></i></span><div style="clear:both"></div>';

				ui.mode.grid.containerBottom.makeNode('ratio' , 'div', {
					css: 'text',
					style: 'padding-right:5px; text-align:right;',
				});

				ui.mode.grid.containerBottom.ratio.makeNode('logged', 'div', {
					style: 'float:left;',
					text: logText
				});

				ui.mode.grid.containerBottom.ratio.makeNode('rateContainer', 'div', {
					style: 'float:right;'
				});
				ui.mode.grid.containerBottom.ratio.makeNode('pipCont', 'div', {
					style: 'float:right;'
				});
				ui.mode.grid.containerBottom.ratio.makeNode('estimatedContainer', 'div', {
					style: 'float:right;'
				});

				ui.mode.grid.containerBottom.ratio.rateContainer.makeNode('rateTime', 'div', {
					text: rateText
				});
				ui.mode.grid.containerBottom.ratio.pipCont.makeNode('pip', 'div', {
					text: '|'
                    , style: 'color:#bbbbbb; margin-left:10px; margin-right:10px;'
				});
				ui.mode.grid.containerBottom.ratio.estimatedContainer.makeNode('estimatedTime', 'div', {
					text: estText
				});

				ui.mode.grid.containerBottom.ratio.rateContainer.makeNode('ratePopup', 'div', {
					css: 'ui popup'
				});
				ui.mode.grid.containerBottom.ratio.estimatedContainer.makeNode('estimatedPopup', 'div', {
					css: 'ui popup'
				});

				viewEditRatePopup(ui.mode.grid.containerBottom.ratio.rateContainer.ratePopup, ui.mode.grid.containerBottom.ratio.rateContainer.rateTime, obj, state, opt, 'bottom right');
				viewEditEstimatePopup(ui.mode.grid.containerBottom.ratio.estimatedContainer.estimatedPopup, ui.mode.grid.containerBottom.ratio.estimatedContainer.estimatedTime, obj, state, opt, 'bottom right');

				if (obj.id && listener) {

					viewEditLogsModal(ui.mode.grid.containerTop.progress, obj, state, opt);

					viewEditLogsModal(ui.mode.grid.containerBottom.ratio.logged, obj, state, opt);

				}

			} else if (!opt.mini) {

				var style = sb.dom.isMobile ? 'padding-left:0px; padding-top:0px; padding-bottom:0px;' : 'padding-top:5px; padding-bottom:0px;';
				ui.mode.grid.makeNode('container' , 'div', {
					css: 'sixteen wide column',
					style: style
				});

				var loggedTime = obj[timeLoggedProperty] ? formatTimeDisplay(obj[timeLoggedProperty]) : 0;

				ui.mode.grid.container.makeNode('loggedContainer' , 'div', {
					style: 'float:left;'
				});

				ui.mode.grid.container.loggedContainer.makeNode('loggedTime' , 'div', {
					style: 'font-size:16px; font-weight:bold;',
					text: '<span style="display:block; float:left;">' + loggedTime + '</span><span style="display:block; float:left; color:#848484; font-size:12px; margin-left:5px;"><i class="ui pencil icon" style="margin-right:0;"></i></span><div style="clear:both;"></div>'
				});

				ui.mode.grid.container.loggedContainer.makeNode('toggedLabel' , 'div', {
					style: 'color:#848484; font-size:9px; line-height:10px; text-transform:uppercase;',
					text: 'Logged'
				});

				if (obj.id && listener) {
					viewEditLogsModal(ui.mode.grid.container.loggedContainer, obj, state, opt);
				}

				if (opt.showEstimatedTime) {
                    
                    var estimatedTime = obj[timeEstimateProperty] ? formatTimeDisplay(obj[timeEstimateProperty]) : 0;
                    
					ui.mode.grid.container.makeNode('estimatedSlash' , 'div', {
                        style: 'float:left; color:#bbbbbb; margin-left:5px; margin-right:5px;',
						text: '/'
					});
                    
					ui.mode.grid.container.makeNode('estimatedContainer' , 'div', {
                        style: 'float:left;'
					});
                    
					ui.mode.grid.container.estimatedContainer.makeNode('estimatedTime' , 'div', {
                        style: 'font-size:16px;',
						text: '<span style="display:block; float:left;">' + estimatedTime + '</span><span style="display:block; float:left; color:#848484; font-size:12px; margin-left:5px;"><i class="ui pencil icon" style="margin-right:0;"></i></span><div style="clear:both;"></div>'
					});
                    
					ui.mode.grid.container.estimatedContainer.makeNode('estimatedLabel' , 'div', {
                        style: 'color:#848484; font-size:9px; line-height:10px; text-transform:uppercase;',
						text: 'Estimated'
					});
                    
					ui.mode.grid.container.estimatedContainer.makeNode('estimatedPopup', 'div', {
                        css: 'ui popup'
					});
                    
					viewEditEstimatePopup(ui.mode.grid.container.estimatedContainer.estimatedPopup, ui.mode.grid.container.estimatedContainer.estimatedTime, obj, state, opt, 'bottom left');
					viewEditEstimatePopup(ui.mode.grid.container.estimatedContainer.estimatedPopup, ui.mode.grid.container.estimatedContainer.estimatedLabel, obj, state, opt, 'bottom left');
					
				}
                
                if (opt.showRateValue) {
                  
                    ui.mode.grid.container.makeNode('ratePip' , 'div', {
                        style: 'float:left; color:#bbbbbb; margin-left:10px; margin-right:10px;',
						text: '|'
					});
                    
					ui.mode.grid.container.makeNode('rateContainer' , 'div', {
                        style: 'float:left;'
					});
                    
					ui.mode.grid.container.rateContainer.makeNode('rateValue' , 'div', {
                        style: 'font-size:16px;',
						text: '<span style="display:block; float:left;">' + sb.dom.formatCurrency(obj[rateValueProperty]) + '</span><span style="display:block; float:left; color:#848484; font-size:12px; margin-left:5px;"><i class="ui pencil icon" style="margin-right:0;"></i></span><div style="clear:both;"></div>'
					});
                    
					ui.mode.grid.container.rateContainer.makeNode('rateLabel' , 'div', {
                        style: 'color:#848484; font-size:9px; line-height:10px; text-transform:uppercase;',
						text: 'Rate'
					});
                    
					ui.mode.grid.container.rateContainer.makeNode('ratePopup', 'div', {
                        css: 'ui popup'
					});
                    
					viewEditRatePopup(ui.mode.grid.container.rateContainer.ratePopup, ui.mode.grid.container.rateContainer.rateValue, obj, state, opt, 'bottom left');
					viewEditRatePopup(ui.mode.grid.container.rateContainer.ratePopup, ui.mode.grid.container.rateContainer.rateLabel, obj, state, opt, 'bottom left');
                }
                
			}	
			
		}
		
		init(ui.grid, true, state);
		
		ui.patch();

	}

	function viewEditLogsModal(ui, obj, state, options) {

		ui.notify('click', {
			type:   'timeClock-run'
			, data: {
				run: function() {

					$(UI_CACHE.modal.body.form.selector).hide();
					$(UI_CACHE.modal.body.list.selector).show();

					UI_CACHE.modal.body.form.empty();
					UI_CACHE.modal.body.list.empty();
									
					viewEditLogsList(UI_CACHE.modal.body.list, obj, state, options);

					UI_CACHE.modal.show();
					
				}
			}
		});

	}
	
	function viewEditLogsList(ui, obj, state, options) {

		ui.empty();

		var collectionsSetup = getCollectionsSetup(ui, state, {
			inModal: true,
			callback: UpdateCollectionCallback
		});
		delete collectionsSetup.fields.shift;
		collectionsSetup.pageLength = 10;
		collectionsSetup.where.shift = obj.id;
		collectionsSetup.where.field_name = options.fieldName;
		collectionsSetup.actions.create = {
			type: 'function', 
			action: function() {

				options.track = false;
				options.create = true;
				options.goBack = true;
				options.edit = false;
				options.inCollection = false;
 				options.bypassSubmitTimeEntryPrompt = false;

				toggleClickEvent(UI_CACHE.modal.body.form, obj, state, options, function(updated) {

					if (updated) {

						if ( ( updated.hasOwnProperty('created') && updated.created ) ||
							 ( updated.hasOwnProperty('updated') && updated.updated )	
						) {

							Setup.addToCollections(updated.time_entry);

						}

					}
					
				});

			}

		};
		collectionsSetup.actions.archive = {
			id: 'archive',
			name: 'Archive',
			action: function(selection, onComplete) {

				var actionSetup = {
					object: obj,
					state: state,
					options: {}
				};

				var actionOptions = {
					selection: selection
				};
			
				deleteTimerAction(actionSetup, actionOptions, function(updated) {

					if (typeof onComplete === 'function') {
						onComplete(updated);
					}

				});

			}

		};
	
		sb.notify({
			type: 'show-collection',
			data: collectionsSetup
		});

		ui.patch();
				
	}

	function toggleClickEvent(ui, obj, state, options, onComplete) {
			
		var now = moment();

		var actionSetup = {
			object:	obj,
			state: state,
			options: options
		};

		if (options.create && !options.track) {
			options.duration = DefaultDuration;
			options.start_date = moment().format('YYYY-MM-DD HH:mm:ss'),
			options.end_date = moment().format('YYYY-MM-DD HH:mm:ss')
		}

		var fieldName = 'time_logged';
		if (state.fieldName) {
			options.fieldName = state.fieldName;
			fieldName = state.fieldName;
		}

		if (state.time_entry.length > 0 && !options.create) {

			stopRunningTime(ui, obj, state, state.time_entry[0], now, function(updated) {

				if (typeof onComplete === 'function') {
					onComplete(updated);
				} else {
					toggleMode(ui, updated, state, options);
					ui.patch();
				}

			}, options);				
			
		} else {

			function continueStartingNewTimer() {

				options.tagged_with = [obj.id, +sb.data.cookie.userId];

				if (obj.parent) {
					if (obj.parent.hasOwnProperty('id')) {
						options.tagged_with.push(obj.parent.id);
					} else {
						options.tagged_with.push(obj.parent);
					}
				}

				options.tagged_with = _.uniq(options.tagged_with); 	

				if (options.create) {

					options.selectedEntry = {
						start_date: now,
						duration: 0,
						shift: obj,
						tagged_with: options.tagged_with
					};

					if (!options.goBack) {

						$(UI_CACHE.modal.showSelector).modal({
							'onHidden': function() {
				
								toggleMode(ui, obj, state, options);
								ui.patch();

								resetModalCallbacks();

							}
						});

					}

					viewEditFormModal(ui, obj, state, options, function(updated) {

						if (!options.goBack) {

							UI_CACHE.modal.hide();
							toggleMode(ui, updated, state, options);
							ui.patch();

						}

						if (typeof onComplete === 'function') {
							onComplete(updated);
						}

					});

				} else {

					timeTrackAction(actionSetup, options, function (updated) {

						sb.data.db.obj.getWhere('time_entries', {
							shift: 			obj.id
							, field_name: 	fieldName
							, staff: 		parseInt(sb.data.cookie.get('uid'))
							, duration: {
								type: 'not_set'
							}
						}, function (time_entries) {

							state.time_entry = time_entries;
							toggleMode(ui, updated, state, options);
							ui.patch();
							
						});
						
					});	
					
				}

			}

			if (!options.create) {

				sb.data.db.obj.getWhere('time_entries', {
					staff: parseInt(sb.data.cookie.get('uid')),
					duration: {
						type: 'not_set'
					},
					childObjs: 1
				}, function (time_entries) {

					if (time_entries.length > 0) {

						var title = time_entries.length === 1 ? 'You have 1 timer already running!' : 'You have ' + time_entries.length + ' timers already running!';
						var text = time_entries.length === 1 ? 'Would you like to log the currently running timer BEFORE starting this new timer?' : 'Would you like to view these running timers (and optionally log them) BEFORE starting this new timer?';
						
						sb.dom.alerts.ask(
							{
								title: title,
								text: text,
								icon: 'warning',
								primaryButtonText: 'Yes',
								secondaryButtonText: 'No',
								cancelButtonText: 'Cancel'
							}
							, function (yes, no) {
				
								if (yes) {

									if (time_entries.length > 1) {

										viewRunningTimersModal(UI_CACHE.modal, fieldName, function() {
											UI_CACHE.modal.hide();
										});

										$(UI_CACHE.modal.showSelector).modal({
											'onHidden': function() {
												continueStartingNewTimer();
												resetModalCallbacks();
											}
										});

										UI_CACHE.modal.show();

									} else {

										var now = moment();

										var parentObj = time_entries[0].shift;
										var timerState = {
											fieldName: time_entries[0].field_name,
											time_entry: [
												time_entries[0]
											]
										}
										var timerOptions = {
											track: true,
											create: false,
											goBack: false,
											edit: false,
											inCollection: false,
											bypassSubmitTimeEntryPrompt: true
										}

										$(UI_CACHE.modal.showSelector).modal({
											'onHidden': function() {
												continueStartingNewTimer();
												resetModalCallbacks();
											}
										});

										stopRunningTime(ui, parentObj, timerState, time_entries[0], now, function(updated) {

											continueStartingNewTimer();

										}, timerOptions);

									}

								} else if (no) {

									continueStartingNewTimer();

								} else {

									toggleMode(ui, obj, state, options);
									ui.patch();

								}

							}

						);

					} else {

						continueStartingNewTimer();

					}

				});		

			} else {

				continueStartingNewTimer();

			}
			
		}
						
	};

	function runningTimeMode(ui, obj, entry, opt) {
		
		var intervalObj = {
			entryId: entry.id,
			fieldId: opt._fieldId,
			intervalId: setInterval(function(ui, entry) {

				var now = moment();
				var startDate = moment(entry.start_date, 'YYYY-MM-DD HH:mm:ss ZZ');
				var newElapsedTime = now.clone().diff(startDate, 'seconds');
			
				$(ui.selector).text(sb.dom.secondsToDurationFormat(newElapsedTime)).removeClass('loading');	
				
			}, 1000, ui, entry)
		};
		
		activeTimersList.push(intervalObj);
		
	}

	function toggleMode(ui, obj, state, opt) {

		var timeLoggedProperty = getPropertyNames(obj, state).timeLoggedProperty;
		var timeEstimateProperty = getPropertyNames(obj, state).timeEstimateProperty;
		var rateValueProperty = getPropertyNames(obj, state).rateValueProperty;

		var tooltip = 'Logged: ' + formatTimeDisplay(obj[timeLoggedProperty]);
		if (opt.showEstimatedTime) {
			tooltip += ' / Est: ' + formatTimeDisplay(obj[timeEstimateProperty]);
		}
		if (opt.showRateValue) {
			tooltip += ' | Rate: ' + sb.dom.formatCurrency(obj[rateValueProperty]);
		}
		
		var isRunning = state.hasOwnProperty('time_entry') && state.time_entry.length > 0 ? 1 : 0;
		
		var timerLeftSectionCSS = isRunning && sb.dom.isMobile ? 'six wide column' : '';
		
		var style = sb.dom.isMobile ? 'padding-left:0; padding-bottom:0;' : 'padding-top:0; padding-bottom:0;';
		
		if (opt.mini) {
			style = style + 'min-height:28px; max-height:28px;';
		} else {
			style = style + 'min-height:40px; max-height:40px; width:130px; padding-right:0 !important;';
		}

		ui.grid.makeNode('cont' , 'div', {
			css: timerLeftSectionCSS,
			style: style			
		});

		var groupBtnsCSS = opt.mini ? '' : 'fluid';
		var singleBtnCSS = opt.mini ? 'transparent' : '';
		var text = opt.mini ? '' : ' Track';
		var border = opt.border == false ? 'transparent' : 'rounded';
		var stopBtnBackgroundColor = opt.background == false ? '' : 'red';
		var stopBtnIconColor = opt.background == false ? 'red' : '';
		var setup = {};
		var createBtn = '';

		style = '';

		if (!isRunning) {

			style = opt.mini ? 'padding:0.5em !important; min-height:28px; max-height:28px;' : 'min-height:40px; max-height:40px;';
			if (opt.mini && !opt.hasOwnProperty('border')) {
				style += 'min-width:45px; max-width:45px;';
			}
			ui.grid.cont.makeNode('buttons', 'div', {
				css: 'ui basic ' + border + ' buttons ' + groupBtnsCSS,
				style: style
			});

			style = opt.mini ? 'padding:0.5em !important; padding-left:10px !important; padding-right:3px !important;' : 'padding-left:5px !important; padding-right:5px !important;';
			setup = {
				css: 'ui basic button ' + singleBtnCSS,
				style: style,
				text: '<i class="far fa-stopwatch" style="margin:0 !important;"></i>'	+ text
			}
			if (opt.mini) {
				setup.tooltip = tooltip;
				setup.tooltipPos = 'top right';
				setup.listener = {
					type: 'popup',
					hoverable: true
				}
			}
			ui.grid.cont.buttons.makeNode('track', 'div', setup);

			if (!opt.mini) {

				createBtn = ui.grid.cont.buttons.makeNode('create', 'div', {
					css: 'ui basic button ' + singleBtnCSS,
					style: 'padding-left:0px !important; padding-right:0px !important;',
					text: '<i class="ui plus icon"></i>'
				});

			} else {

				style = opt.mini && opt.border != false ? 'width:15px !important;' : 'width:auto !important;';
				ui.grid.cont.buttons.makeNode('more', 'div', {
					css: 'ui vertical menu',
					style: 'margin:0 !important; min-height:28px; max-height:28px; background:none !important;' + style
				});

				ui.grid.cont.buttons.more.makeNode('dropdown', 'div', {
					css: 'ui simple dropdown icon button ' + singleBtnCSS,
					style: 'padding: 7px 0px !important;' + style,
					text: '<i class="ui grey dropdown icon"></i>'
				});

				ui.grid.cont.buttons.more.dropdown.makeNode('menu', 'div', {
					css: 'left menu'
				});

				createBtn = ui.grid.cont.buttons.more.dropdown.menu.makeNode('create', 'div', {
					css: 'item',
					text: '<i class="ui plus icon"></i> Manual Time Entry'
				});

				ui.grid.cont.buttons.more.dropdown.menu.makeNode('list', 'div', {
					css: 'item',
					text: '<i class="ui list icon"></i> View Time Entries'
				});

				viewEditLogsModal(ui.grid.cont.buttons.more.dropdown.menu.list, obj, state, opt);

			}

		} else {

			// ui.grid.cont.makeNode('buttons', 'div', {
			// 	css: 'ui basic fluid buttons',
			// 	style: 'height:40px;'
			// });
			// ui.grid.cont.buttons.makeNode('stop', 'div', {
			// 	css: 'ui basic button',
			// 	text: '<i class="far fa-stopwatch red"></i>'
			// });
			// ui.grid.cont.buttons.makeNode('pause', 'div', {
			// 	css: 'ui basic button',
			// 	text: '<i class="ui pause icon"></i>'
			// });

			style = opt.mini ? 'padding:0.5em !important; min-height:28px !important; max-height:28px !important;' : 'min-height:40px; max-height:40px;';
			if (opt.mini && !opt.hasOwnProperty('border')) {
				style += 'min-width:45px; max-width:45px;';
			}
			ui.grid.cont.makeNode('buttons', 'div', {
				css: 'ui basic ' + border + ' buttons ' + groupBtnsCSS,
				style: style
			});

			style = opt.mini ? 'padding:0.5em !important; padding-left:5px !important; padding-right:0px !important; min-height:28px !important; max-height:28px !important;' : 'padding-left:5px !important; padding-right:5px !important;';
			if (opt.mini && !opt.hasOwnProperty('border')) {
				style += 'padding-left:0px !important;'
			}
			setup = {
				css: 'ui ' + stopBtnBackgroundColor + ' button running',
				style: style,
				text: '<i class="far fa-spinner-third fa-spin"></i>'
			}
			if (opt.mini) {
				setup.text = '<i class="fas fa-stopwatch ' + stopBtnIconColor +'"></i>';
				setup.tooltip = tooltip;
				setup.tooltipPos = 'top right';
				setup.listener = {
					type: 'popup',
					hoverable: true
				}
			}
			ui.grid.cont.buttons.makeNode('track', 'div', setup);

			if (!opt.mini) {
				runningTimeMode(ui.grid.cont.buttons.track, obj, state.time_entry[0], opt);
			}
			
		}

		progressMode(ui, obj, state, opt);
			
		ui.grid.cont.buttons.track.notify('click', {
			type: 'timeClock-run',
			data: {
				run: function (ui, object, state, options) {

					$(ui.grid.cont.buttons.track.selector).html('<i class="far fa-spinner-third fa-spin"></i>');
					$(ui.grid.cont.buttons.track.selector).addClass('spinning');
					
					if (!isRunning) {
						$(createBtn.selector).remove();
					}

					options.track = true;
					options.create = false;
					options.goBack = false;
					options.edit = false;
					options.inCollection = false;
					options.bypassSubmitTimeEntryPrompt = false;

					toggleClickEvent(ui, object, state, options);
					
				}.bind(null, ui, obj, state, opt)
			}
		}, sb.moduleId);
		
		if (!isRunning) {

			createBtn.notify('click', {
				type: 'timeClock-run',
				data: {
					run: function (ui, object, state, options) {

						$(createBtn.selector).html('<i class="far fa-spinner-third fa-spin"></i>');
						$(createBtn.selector).addClass('spinning');

						$(ui.grid.cont.buttons.track.selector).remove();

						options.track = false;
						options.create = true;
						options.goBack = false;
						options.edit = false;
						options.inCollection = false;
						options.bypassSubmitTimeEntryPrompt = false;
						
						toggleClickEvent(ui, object, state, options);
						
					}.bind(null, ui, obj, state, opt)
				}
			}, sb.moduleId);

		}

		ui.patch();
					
	}

	///util
	function checkEntryStatus (groupObj, state, onComplete) {

		var where = {
			staff: +sb.data.cookie.userId,
			duration: {
				type:'not_set'
			}	
		};
		
		if (state && state.fieldName) {
			where.field_name = state.fieldName;
		}

		if (groupObj) {
			if (groupObj.id) {
				where.shift = groupObj.id;
			} else {
				onComplete({});
				return;
			}
		}

		sb.data.db.obj.getWhere(
			'time_entries'
			, where
			, onComplete
		);
		
		return;
		
	}	
	
	function getTimeEntries(page, onComplete) {

		var whereOpt = {
				staff:	   +sb.data.cookie.userId
				, childObjs: {
					start_date: true
					, end_date: true
					, staff:	  {
						fname: true
						, lname: true
						, profile_pic: true
					}
					, shift:    true
					, duration: true
					, object_uid: true
				}
			};
			
		if (page) {

			whereOpt = page;

		}	

		sb.data.db.obj.getWhere('time_entries'
			, whereOpt
			, onComplete
		);
		
		return;
		
	}
		
	function calcPerc(obj, state) {

		var percentage;
		
		var timeLoggedProperty = getPropertyNames(obj, state).timeLoggedProperty;
		var timeEstimateProperty = getPropertyNames(obj, state).timeEstimateProperty;
		
		var quotient = obj[timeLoggedProperty] / obj[timeEstimateProperty];

		if (_.isNaN(quotient)) {

			percentage = 0;

		} else {
		
			if (quotient > 1) {

				percentage = 101;

			} else if ( quotient == 1) {
				
				percentage = 100;
				
			} else {
				
				percentage = quotient * 100;
				
			}

		}
		
		return Math.round(percentage);

	}	
	
	function formatTimeDisplay (input, options) {

		var input = input || 0;
		var defaultTimes = [ {h:3600}, {m:60}, {s:1} ];
		var formatted = '';
	
		if (options) {
	
			_.map(defaultTimes, function(t){
	
				var timeKey = _.keys(t);
	
				if (options.hasOwnProperty(timeKey)) {
	
					t[timeKey] = options[timeKey];
				}
	
			});
	
		}
	
		if (input >= 60) {

			var input = input;

			var reducedTime = _.reduce(defaultTimes, function(memo, range, index) {
		
				var key = _.keys(range);
		
				if (input / range[key] >= 1) {
		
					var whole = Math.floor( input / range[key] );
		
					memo[key] = whole;
		
					input = input % range[key];
		
				}
		
				return memo;
		
			}, {});
	
			_.mapObject(reducedTime, function(v, k, i) {			
				formatted += v + k + ' ';
			});

			if (formatted == '') {
				formatted = 0;
			}
			
		} else {

			formatted = (input == 0) ? 0 : input + 's';		
				
		}

		return formatted;
	}		
	
	function timeTrackAction(setup, options, onComplete, hideModal) {

        options.timeLoggedProperty = getPropertyNames(setup.object, setup.state).timeLoggedProperty;
		options.timeEstimateProperty = getPropertyNames(setup.object, setup.state).timeEstimateProperty;
		options.rateValueProperty = getPropertyNames(setup.object, setup.state).rateValueProperty;
		options.runningProperty = getPropertyNames(setup.object, setup.state).runningProperty;
		options.shift = setup.object.id

        sb.data.db.obj.runSteps(
		  	{
				timer: {
					options: options
				}
		  	},
			setup.object.id,
			function (response) {
                
				if (response.hasOwnProperty('time_entry')) {
					if (response.time_entry.hasOwnProperty('id')) {
						if (response.time_entry.id) {
							clearIntervalObjects(response.time_entry.id);
						}
					}
				}

				function updateField() {

					sb.notify({
						type: 'timelog-field-view-update',
						data: {
							object: setup.object,
							state: setup.state,
							options: setup.options
						}
					});

				}

				if (hideModal) {

					$(UI_CACHE.modal.showSelector).modal({
						'onHidden': function() {
							
							updateField();

							if (typeof onComplete === 'function') {
								onComplete(response);
							}

							resetModalCallbacks();

						}
					});

					UI_CACHE.modal.hide();

				} else {

					updateField();

					if (typeof onComplete === 'function') {
						onComplete(response);
					}

				}
				
			}
		  
		);
		
	}

	function deleteTimerAction(setup, options, onComplete) {
		
		sb.data.db.obj.runSteps(
		  	{
				timer: {
					options: options,
					delete: true
				}
		  	},
		  	setup.object.id,
		  	function (response) {

				sb.notify({
					type:   'timelog-field-view-update',
					data: {
						object: setup.object,
						state: setup.state,
						options: setup.options
					}
				});

				clearIntervalObjects(options.selection);

				var body = !Array.isArray(options.selection) ? 'Time entry discarded successfully!' : 'Time entries discarded successfully!' ;

				sb.notify({
					type: 'display-alert',
					data: {
						header: 'Discarded',
						body: body,
						color: 'green'
					}
				});
			
				if (onComplete && typeof onComplete == 'function') {
					onComplete(response);
				}
			
		  	}
		  
		);
		
	}

	function viewEditFormModal(ui, obj, state, options, onComplete) {

		if (_.isEmpty(UI_CACHE.modal)) {
			UI_CACHE.modal = ui.makeNode('modal', 'modal', {});
			UI_CACHE.modal.body.makeNode('list', 'div', {});
			UI_CACHE.modal.body.makeNode('form', 'div', {});
		}

		$(UI_CACHE.modal.body.form.selector).show();
		$(UI_CACHE.modal.body.list.selector).hide();

		UI_CACHE.modal.body.form.empty();
		UI_CACHE.modal.body.list.empty();

		viewEditForm(UI_CACHE.modal.body.form, obj, state, options, function(updated) {

			$(UI_CACHE.modal.body.form.selector).hide();
			$(UI_CACHE.modal.body.list.selector).show();

			UI_CACHE.modal.body.form.empty();

			if (typeof onComplete === 'function') {
				onComplete(updated);
			}

		});

		if ((options.create || options.track || options.edit) && !options.goBack) {
			UI_CACHE.modal.show();
		}

	}
	
	function stopRunningTime(ui, obj, state, time_entry, now, onComplete, options) {
		
		function submitTimeEntry(onComplete) {

			options.selectedEntry = time_entry;
					
			viewEditFormModal(ui, obj, state, options, function(updated) {

				if (typeof onComplete === 'function') {
					onComplete(updated);
				}

			});
			
		}

		if (options.bypassSubmitTimeEntryPrompt) {

			submitTimeEntry(onComplete);

		} else {

			sb.dom.alerts.ask(
				{
					title: 'Discard or Log Timer?',
					text: 'Would you like to discard or log this running timer?',
					icon: 'question',
					primaryButtonText: 'Log Time',
					secondaryButtonText: 'Discard Time',
					cancelButtonText: 'Nevermind'
				}
				, function (yes, no) {

					swal.close();

					if (yes) {

						submitTimeEntry(onComplete);
						
					} else if (no) {

						var actionSetup = {
							object: obj,
							state: state,
							options: {}
						};
		
						var actionOptions = {
							selection: time_entry.id
						};
					
						deleteTimerAction(actionSetup, actionOptions, function(updated) {

							state.time_entry = [];
		
							if (typeof onComplete === 'function') {
								onComplete(updated);
							}
		
						});

					} else {

						toggleMode(ui, obj, state, options);
						ui.patch();

					}

				}
			);

		}
									
	}
	
	var view = {
		segment: {
			type: 'div'
			, css: 'ui stackable grid container'
			, content: {
				css: 'ui row'
				, top: {
					type:'column'
					, w: 16
					, css: 'centered'
					, style: 'margin-bottom:20px;'
					, content: {
						id: {
							type: 'title'
							, fieldName: 'name'
							, edit: false
						}
						, divide: {
							type:'div'
							, css: 'ui divider'
						}						
					}
				}
				, left: {
					type: 'column'
					, w: 8
					, css: ''
					, content: {
						shift: {
							type:'view'
							, view: function(ui, obj, opt){

								var gridCss = 'ui stackable grid';
								var col1Css = 'four wide column';
								var col2Css = 'twelve wide column';
																
								ui.makeNode('duration', 'div', {
									css: 'field'
								});
								
								ui.duration.makeNode('grid', 'div', {css:gridCss});
								
								ui.duration.grid.makeNode('col1', 'div', {
									css: col1Css
								});
								ui.duration.grid.makeNode('col2', 'div', {
									css: col2Css
								});

								ui.duration.grid.col1.makeNode('assignee_label', 'div'
									, {
										text: (obj.shift.group_type) ? obj.shift.group_type :  'Logged on: '
										, css: 'ui mini grey sub header'
										, tag: 'h5'
										, style: 'font-weight:4 !important;line-height:2em;color:rgb(175,175,175) !important;'
									}
								);
								
								ui.duration.grid.col2.makeNode('durval' , 'div'
									, {
										css: ''
										, text: '<strong>' + obj.shift.name + '</strong>'
									}
								);
								
								ui.patch();															
							}
						}
						, start_date: {
							type: 'date'
							, fieldName: 'start_date'
							, label: 'Start Time'
							, edit: true
							, dateType: 'datetime'
						}
						, end_date: {
							type: 'date'
							, fieldName: 'end_date'
							, label: 'End Time'
							, edit: true
							, dateType: 'datetime'
						}
						, duration: {
							type: 'view'
							, fieldName: 'duration'
							, label: 'Duration'
							, view: function(ui, obj, options){

								var gridCss = 'ui stackable grid';
								var col1Css = 'four wide column';
								var col2Css = 'twelve wide column';
								
								ui.makeNode('duration', 'div', {
									css: 'field'
								});
								
								ui.duration.makeNode('grid', 'div', {css:gridCss});
								
								ui.duration.grid.makeNode('col1', 'div', {
									css: col1Css
								});
								ui.duration.grid.makeNode('col2', 'div', {
									css: col2Css
								});

								ui.duration.grid.col1.makeNode('assignee_label', 'div'
									, {
										text: 'Duration:'
										, css: 'ui mini grey sub header'
										, tag: 'h5'
										, style: 'font-weight:4 !important;line-height:2em;color:rgb(175,175,175) !important;'
									}
								);
								
								ui.duration.grid.col2.makeNode('durval' , 'div'
									, {
										css: ''
										, text: formatTimeDisplay(obj.duration)
									}
								);
								
								ui.patch();								
							}
						}													
					}
				}
				, right: {
					type: 'column'
					, w: 8
					, css: ''
					, content: {
						note: {
							type:'detail'
							, fieldName: 'note'
							, label: 'Notes'
							, edit: true
							, commitUpdates: true
						}
						, created_by: {
							type: 'users'
							, fieldName: 'created_by'
							, label: 'Created By'
						}					
						, date_created: {
							type: 'date'
							, fieldName: 'date_created'
							, edit: false
							, label: 'Created'
						}	
						, last_updated: {
							type: 'date'
							, fieldName: 'last_updated'
							, edit: false
							, label: 'Updated'
						}
					}
				}
				, bottom: {
					type: 'column'
					, w: 8
					, css: 'centered'
					, content: {
						comments: {
							type: 'view'
							, view: function (ui, obj, options) {

								sb.notify({
									type: 'show-note-list-box'
									, data: {
										domObj: ui
										, objectIds: [obj.id]
										, objectId: obj.id
										, collapse: 'open'
									}
								});

							}
						}
					}
				}
			}
		}
	};	

	function viewEditForm(ui, obj, state, options, onComplete) {

		function continueViewEditForm() {

			var mode = 'tracking';
			var clonedEntry = JSON.parse(JSON.stringify(options.selectedEntry));

			var startDateLockedStatus = false;
			var endDateLockedStatus = true;
			var durationLockedStatus = false;
			var lastUpdatedField = 'start_date';
			var lastLockedField = 'end_date';

			function updateFields(ui, updatedField, updatedValue, options) {

				var startDate = moment.isMoment(options.selectedEntry.start_date) ? options.selectedEntry.start_date : moment(options.selectedEntry.start_date, 'YYYY-MM-DD HH:mm:ss ZZ');
				var endDate = moment.isMoment(options.selectedEntry.end_date) ? options.selectedEntry.end_date : moment(options.selectedEntry.end_date, 'YYYY-MM-DD HH:mm:ss ZZ');
				var duration = options.selectedEntry.duration;

				lastUpdatedField = updatedField;

				var newVal = '';

				$(ui.grid.errorContainer.selector).hide();
				$(ui.grid.errorContainer.errorMessage.selector).html('');
				
				if (startDateLockedStatus == true) {

					// Changing end date should only adjust duration (start date + end date)
					if (updatedField == 'end_date') {
						updatedValue = moment(updatedValue, 'YYYY-MM-DD HH:mm:ss ZZ');
						newVal = updatedValue.clone().diff(startDate, 'seconds');
						if (Math.sign(newVal) == 1) {
							options.selectedEntry.duration = newVal;
							$(ui.grid.durationContainer.grid.left.duration.selector + ' input').val(sb.dom.secondsToDurationFormat(options.selectedEntry.duration));
						} else {
							options.selectedEntry.duration = 0;
							$(ui.grid.durationContainer.grid.left.duration.selector + ' input').val(sb.dom.secondsToDurationFormat(options.selectedEntry.duration));
							$(ui.grid.errorContainer.selector).show();
							$(ui.grid.errorContainer.errorMessage.selector).html('Please make sure the end date is later than the start date.');
						}
					}
					
					// Changing duration should only adjust end date (start date + duration)
					if (updatedField == 'duration') {
						options.selectedEntry.end_date = startDate.clone().add(updatedValue, 'seconds');
						$(ui.grid.endDateContainer.grid.left.endDate.txt.selector).html('<nobr>' + options.selectedEntry.end_date.local().format('l, h:mma') + '</nobr>');
					}

				} else if (endDateLockedStatus == true) {

					// Changing start date should only adjust duration (start date + end date)
					if (updatedField == 'start_date') {
						updatedValue = moment(updatedValue, 'YYYY-MM-DD HH:mm:ss ZZ');
						newVal = endDate.clone().diff(updatedValue, 'seconds');
						if (Math.sign(newVal) == 1) {
							options.selectedEntry.duration = newVal;
							$(ui.grid.durationContainer.grid.left.duration.selector + ' input').val(sb.dom.secondsToDurationFormat(options.selectedEntry.duration));
						} else {
							options.selectedEntry.duration = 0;
							$(ui.grid.durationContainer.grid.left.duration.selector + ' input').val(sb.dom.secondsToDurationFormat(options.selectedEntry.duration));
							$(ui.grid.errorContainer.selector).show();
							$(ui.grid.errorContainer.errorMessage.selector).html('Please make sure the start date is earlier than the end date.');
						}
					}

					// Changing duration should adjust start date (end date + duration)
					if (updatedField == 'duration') {
						options.selectedEntry.start_date = endDate.clone().subtract(updatedValue, 'seconds');
						$(ui.grid.startDateContainer.grid.left.startDate.txt.selector).html('<nobr>' + options.selectedEntry.start_date.local().format('l, h:mma') + '</nobr>');
					}

				} else if (durationLockedStatus == true) {

					// Changing start date should only adjust end date (start date + duration)
					if (updatedField == 'start_date') {
						updatedValue = moment(updatedValue, 'YYYY-MM-DD HH:mm:ss ZZ');
						options.selectedEntry.end_date = updatedValue.clone().add(duration, 'seconds');
						$(ui.grid.endDateContainer.grid.left.endDate.txt.selector).html('<nobr>' + options.selectedEntry.end_date.local().format('l, h:mma') + '</nobr>');
					}

					// Changing end date should only adjust start date (end date + duration)
					if (updatedField == 'end_date') {
						updatedValue = moment(updatedValue, 'YYYY-MM-DD HH:mm:ss ZZ');
						options.selectedEntry.start_date = updatedValue.clone().subtract(duration, 'seconds');
						$(ui.grid.startDateContainer.grid.left.startDate.txt.selector).html('<nobr>' + options.selectedEntry.start_date.local().format('l, h:mma') + '</nobr>');
					}

				}
				
			}

			function updateFieldLocks(field) {

				var startDateLockUI = ui.grid.startDateContainer.grid.right.lock;
				var endDateLockUI = ui.grid.endDateContainer.grid.right.lock;
				var durationLockUI = ui.grid.durationContainer.grid.right.lock;

				lastLockedField = field;

				if (field == 'start_date') {

					startDateLockedStatus = true;
					endDateLockedStatus = false;
					durationLockedStatus = false;

					buildStartDateField(ui, false);
					buildEndDateField(ui, true);
					buildDurationField(ui, true);
					buildNoteField(ui);

					ui.patch();

					$(startDateLockUI.selector).html('<i class="fas fa-lock"></i>').css({'cursor':'not-allowed'});
					$(endDateLockUI.selector).html('<i class="far fa-lock-open"></i>').css({'cursor':'pointer'});
					$(durationLockUI.selector).html('<i class="far fa-lock-open"></i>').css({'cursor':'pointer'});

				} else if (field == 'end_date') {

					startDateLockedStatus = false;
					endDateLockedStatus = true;
					durationLockedStatus = false;

					buildStartDateField(ui, true);
					buildEndDateField(ui, false);
					buildDurationField(ui, true);
					buildNoteField(ui);

					ui.patch();

					$(startDateLockUI.selector).html('<i class="far fa-lock-open"></i>').css({'cursor':'pointer'});
					$(endDateLockUI.selector).html('<i class="fas fa-lock"></i>').css({'cursor':'not-allowed'});
					$(durationLockUI.selector).html('<i class="far fa-lock-open"></i>').css({'cursor':'pointer'});

				} else if (field == 'duration') {

					startDateLockedStatus = false;
					endDateLockedStatus = false;
					durationLockedStatus = true;

					buildStartDateField(ui, true);
					buildEndDateField(ui, true);
					buildDurationField(ui, false);
					buildNoteField(ui);

					ui.patch();

					$(startDateLockUI.selector).html('<i class="far fa-lock-open"></i>').css({'cursor':'pointer'});
					$(endDateLockUI.selector).html('<i class="far fa-lock-open"></i>').css({'cursor':'pointer'});
					$(durationLockUI.selector).html('<i class="fas fa-lock"></i>').css({'cursor':'not-allowed'});

				}

				if (!options.create && !options.edit) {

					$(ui.grid.right.grid.container.runningTimeBtnContainer.selector).hide();
					$(ui.grid.right.grid.container.manualModeBtnContainer.selector).hide();
					$(ui.grid.right.grid.container.runningModeBtnContainer.selector).show();

				}

				$(ui.grid.startDateContainer.selector).show();
				$(ui.grid.endDateContainer.selector).show();
				$(ui.grid.durationContainer.selector).show();

			}

			function buildStartDateField(ui, canEdit) {

				sb.notify({
					type: 'view-field',
					data: {
						type: 'date',
						property: 'start_date',
						ui: ui.grid.startDateContainer.grid.left.makeNode('startDate', 'div', {
							css: 'ui field round-border', 
							style: 'padding:9px 10px; height:38px; min-width:200px; margin-bottom:1em;'
						}),
						obj: options.selectedEntry,
						options: {
							edit: canEdit,
							showRelative: false,
							onUpdate: function(val) {
		
								options.selectedEntry.start_date = val;
		
								updateFields(ui, 'start_date', val, options);
		
							}
						}
					}
				});

			}

			function buildEndDateField(ui, canEdit) {

				sb.notify({
					type: 'view-field',
					data: {
						type: 'date',
						property: 'end_date',
						ui: ui.grid.endDateContainer.grid.left.makeNode('endDate', 'div', {
							css: 'ui field round-border', 
							style: 'padding:9px 10px; height:38px; min-width:200px;'
						}),
						obj: options.selectedEntry,
						options: {
							edit: canEdit,
							showRelative: false,
							onUpdate: function(val) {
		
								options.selectedEntry.end_date = val;
		
								updateFields(ui, 'end_date', val, options);
		
							}
						},
						id: 'timerEndDate'
					}
				});

			}

			function buildDurationField(ui, canEdit) {

				sb.notify({
					type: 'view-field',
					data: {
						type: 'duration',
						property: 'duration',
						ui: ui.grid.durationContainer.grid.left.makeNode('duration', 'div', {
							css: 'ui field round-border', 
							style: 'padding:9px 10px; height:38px;'
						}),
						obj: options.selectedEntry,
						options: {
							edit: canEdit,
							type: 'picker',
							style: 'width:100% !important;',
							formatAsSeconds: true,
							onUpdate: function(val) {
		
								options.selectedEntry.duration = val;
				
								updateFields(ui, 'duration', val, options);	
		
							}
						}
					}
				});

			}

			function buildNoteField(ui) {

				sb.notify({
					type: 'view-field',
					data: {
						type: 'detail',
						property: 'note',
						ui: ui.grid.noteContainer.makeNode('note', 'div', {
							css: 'ui field round-border', 
							style: 'height:38px; min-height:150px; min-width:200px;'
						}),
						obj: options.selectedEntry,
						options: {
							edit: true,
							editing: true,
							commitUpdates: false,
							onUpdate: function(val) {
								if (val.hasOwnProperty('note')) {
									if (val.note) {
										options.selectedEntry.note = val.note;
									}
								}
							}
						}
					}
				});

			}

			function showManualModeFields() {

				$(ui.grid.startDateContainer.selector).show();
				$(ui.grid.endDateContainer.selector).show();
				$(ui.grid.durationContainer.selector).show();

			}

			function hideManualModelFields() {

				$(ui.grid.startDateContainer.selector).hide();
				$(ui.grid.endDateContainer.selector).hide();
				$(ui.grid.durationContainer.selector).hide();

			}

			function switchToManualMode() {

				mode = 'manual';

				buildStartDateField(ui, !startDateLockedStatus);
				buildEndDateField(ui, !endDateLockedStatus);
				buildDurationField(ui, !durationLockedStatus);

				ui.patch();

				if (!options.create && !options.edit) {

					$(ui.grid.right.grid.container.runningTimeBtnContainer.selector).hide();
					$(ui.grid.right.grid.container.manualModeBtnContainer.selector).hide();
					$(ui.grid.right.grid.container.runningModeBtnContainer.selector).show();

				}

				showManualModeFields();

				updateFields(ui, lastUpdatedField, options.selectedEntry[lastUpdatedField], options);
				updateFieldLocks(lastLockedField);

			}

			function switchToTrackingMode() {

				mode = 'tracking';

				if (!options.create && !options.edit) {

					$(ui.grid.right.grid.container.runningTimeBtnContainer.selector).show();
					$(ui.grid.right.grid.container.manualModeBtnContainer.selector).show();
					$(ui.grid.right.grid.container.runningModeBtnContainer.selector).hide();

				}

				hideManualModelFields();

			}

			ui.empty();

			var timeLoggedProperty = getPropertyNames(obj, state).timeLoggedProperty;
			var timeEstimateProperty = getPropertyNames(obj, state).timeEstimateProperty;

			if (!obj[timeEstimateProperty]) {
				obj[timeEstimateProperty] = 0;
			}	
					
			switch (obj.object_bp_type) {
				
				case 'groups':
					if (obj.start_date) {
						startDateVal = moment(obj.start_date).local().format('MM/DD/YYYY hh:mm a');
					}
					break;
				
			}

			ui.makeNode('grid', 'div', {
				css: 'ui stackable grid'
			});

			var headerText = '';
			if (options.selectedEntry) {

				if (options.track) {
					headerText = 'Edit Tracked Time';
				} else if (options.create) {
					headerText = 'New Time Entry';
				} else if (options.edit) {
					headerText = 'Edit Time Entry #' + options.selectedEntry.object_uid;
				}

				if (!options.selectedEntry.end_date) {
					options.selectedEntry.end_date = moment();
				}

			}

			ui.grid.makeNode('left', 'div', {
				css: 'ui eight wide column',
				style: 'padding-top:0 !important;'
			});

			ui.grid.left.makeNode('grid', 'div', {
				css: 'grid'
			});

			ui.grid.makeNode('right', 'div', {
				css: 'ui eight wide column',
				style: 'padding-top:0 !important;'
			});

			ui.grid.right.makeNode('grid', 'div', {
				css: 'grid'
			});

			ui.grid.left.grid.makeNode('header', 'div', {
				css: 'ui sixteen wide column',
				style: 'padding-top:0 !important; padding-bottom:5px !important;',
				text: '<span class="ui huge header">' + headerText + '</span>'
			});

			ui.grid.left.grid.makeNode('subheader', 'div', {
				css: 'ui sixteen wide column',
				style: 'padding-top:0 !important;',
				text: '<span class="ui small blue header"><span style="font-weight:400;">in</span> ' + options.selectedEntry.shift.name + '</span>'
			});

			ui.grid.right.grid.makeNode('container', 'div', {
				css: 'ui sixteen wide column'
			});

			if (!options.create && !options.edit) {

				ui.grid.right.grid.container.makeNode('runningTimeBtnContainer', 'div', {
					css: 'ui basic rounded buttons right floated'
				});

				var runningTimeBtnUI = ui.grid.right.grid.container.runningTimeBtnContainer.makeNode('runningTimeBtn', 'div', {
					css: 'ui red button running',
					style: 'cursor:default !important; min-width:95px !important;',
					text: '<i class="far fa-spinner-third fa-spin"></i>'
				});

				ui.grid.right.grid.container.makeNode('manualModeBtnContainer', 'div', {
					css: 'ui basic rounded buttons right floated transparent'
				});

				ui.grid.right.grid.container.manualModeBtnContainer.makeNode('manualModeBtn', 'div', {
					css: 'ui basic button',
					style: 'margin-right:5px !important;',
					text: '<i class="far fa-pencil-alt" style="margin-right:5px !important;"></i> Switch to Manual Mode'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function() {

							switchToManualMode();

						}
					}
				});

				ui.grid.right.grid.container.makeNode('runningModeBtnContainer', 'div', {
					css: 'ui basic rounded buttons right floated transparent',
					style: 'display:none;'
				});

				ui.grid.right.grid.container.runningModeBtnContainer.makeNode('runningModeBtn', 'div', {
					css: 'ui basic button',
					style: 'margin-right:5px !important;',
					text: '<i class="far fa-stopwatch" style="margin-right:5px !important;"></i> Switch to Tracking Mode'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function() {

							switchToTrackingMode();

						}
					}
				});

				runningTimeMode(runningTimeBtnUI, obj, options.selectedEntry, options);

			}

			ui.grid.makeNode('errorContainer', 'div', {
				css: 'ui sixteen wide column',
				style: 'padding-top:0 !important; display:none;'
			});

			ui.grid.errorContainer.makeNode('errorMessage', 'div', {
				css: 'ui red message'
			});

			// Start Date
			ui.grid.makeNode('startDateContainer', 'div', {
				css: 'ui five wide column',
				style: 'padding-top:0 !important; padding-bottom:0 !important; display:none;'
			});
			ui.grid.startDateContainer.makeNode('startDateLabel', 'div', {
				text: 'Start Date (required)',
				tag: 'label'
			});
			ui.grid.startDateContainer.makeNode('grid', 'div', {
				css: 'ui grid'
			});
			ui.grid.startDateContainer.grid.makeNode('left', 'div', {
				css: 'fifteen wide column',
				style: 'padding-right:5px; padding-top:0 !important; padding-bottom:0 !important;'
			});

			ui.grid.startDateContainer.grid.makeNode('right', 'div', {
				css: 'one wide column',
				style: 'padding-top:8px; padding-left:0; padding-bottom:0 !important;'
			});
			ui.grid.startDateContainer.grid.right.makeNode('lock', 'div', {
				style: 'cursor:pointer;',
				text: startDateLockedStatus ? '<i class="fas fa-lock"></i>' : '<i class="far fa-lock-open"></i>'
			}).notify('click', {
				type: 'timeClock-run',
				data: {
					run: function () {

						updateFieldLocks('start_date');

					}
				}
			});

			// End Date
			ui.grid.makeNode('endDateContainer', 'div', {
				css: 'ui five wide column',
				style: 'padding-top:0 !important; padding-bottom:0 !important; display:none;'
			});
			ui.grid.endDateContainer.makeNode('endDateLabel', 'div', {
				text: 'End Date',
				tag: 'label'
			});
			ui.grid.endDateContainer.makeNode('grid', 'div', {
				css: 'ui grid'
			});
			ui.grid.endDateContainer.grid.makeNode('left', 'div', {
				css: 'fifteen wide column',
				style: 'padding-right:5px; padding-top:0 !important; padding-bottom:0 !important;'
			});

			ui.grid.endDateContainer.grid.makeNode('right', 'div', {
				css: 'one wide column',
				style: 'padding-top:8px; padding-left:0; padding-bottom:0 !important;'
			});
			ui.grid.endDateContainer.grid.right.makeNode('lock', 'div', {
				style: 'cursor:pointer;',
				text: endDateLockedStatus ? '<i class="fas fa-lock"></i>' : '<i class="far fa-lock-open"></i>'
			}).notify('click', {
				type: 'timeClock-run',
				data: {
					run: function () {
						
						updateFieldLocks('end_date');
						
					}
				}
			});

			// Duration
			ui.grid.makeNode('durationContainer', 'div', {
				css: 'ui six wide column',
				style: 'padding-top:0 !important; padding-bottom:0 !important; display:none;'
			});
			ui.grid.durationContainer.makeNode('durationLabel', 'div', {
				text: 'Duration (required)',
				tag: 'label'
			});
			ui.grid.durationContainer.makeNode('grid', 'div', {
				css: 'ui grid'
			});
			ui.grid.durationContainer.grid.makeNode('left', 'div', {
				css: 'fifteen wide column',
				style: 'padding-right:5px; padding-top:0 !important; padding-bottom:0 !important;'
			});

			ui.grid.durationContainer.grid.makeNode('right', 'div', {
				css: 'one wide column',
				style: 'padding-top:8px; padding-left:0; padding-bottom:0 !important;'
			});
			ui.grid.durationContainer.grid.right.makeNode('lock', 'div', {
				style: 'cursor:pointer;',
				text: durationLockedStatus ? '<i class="fas fa-lock"></i>' : '<i class="far fa-lock-open"></i>'
			}).notify('click', {
				type: 'timeClock-run',
				data: {
					run: function () {

						updateFieldLocks('duration');
						
					}
				}
			});

			// Notes
			ui.grid.makeNode('noteContainer', 'div', {
				css: 'ui sixteen wide column',
				style: 'padding-top:0 !important;'
			});
			ui.grid.noteContainer.makeNode('durationLabel', 'div', {
				text: 'Note',
				tag: 'label'
			});

			buildNoteField(ui);

			// Tags
			ui.grid.makeNode('tagsContainer', 'div', {
				css: 'ui sixteen wide column',
				style: 'padding-top:0px !important;'
			});
			ui.grid.tagsContainer.makeNode('tagsLabel', 'div', {
				text: 'Tags',
				tag: 'label'
			});
			ui.grid.tagsContainer.makeNode('tags', 'div', {});

			// Buttons
			ui.grid.makeNode('btnContainer', 'div', {
				css: 'ui sixteen wide column',
				style: 'padding-bottom:0 !important; text-align:right;'
			});

			// Cancel
			ui.grid.btnContainer.makeNode('cancelBtn', 'div', {
				text: 'Cancel', 
				css: 'ui large light-grey basic button',
				style: 'margin-top:1em;'
			}).notify('click', {
				type: 'timeClock-run',
				data: {
					run: function () {

						ui.grid.btnContainer.cancelBtn.loading();

						if (!options.goBack) {

							$(UI_CACHE.modal.showSelector).modal({
								'onHidden': function() {
		
									if (typeof onComplete === 'function') {
										onComplete(obj);
									}

									resetModalCallbacks();
		
								}
							});

							UI_CACHE.modal.hide();

						} else {

							if (typeof onComplete === 'function') {
								onComplete();
							}

						}
						
					}
				}
			});

			// Save
			ui.grid.btnContainer.makeNode('saveBtn', 'div', {
				text: '<i class="stopwatch icon"></i> Save Time Entry', 
				css: 'ui large green button',
				style: 'margin-top:1em;'
			}).notify('click', {
				type: 'timeClock-run',
				data: {
					run: function () {

						if (mode == 'tracking') {
							options.selectedEntry.start_date = moment(clonedEntry.start_date, 'YYYY-MM-DD HH:mm:ss ZZ');
							options.selectedEntry.end_date = moment();
							options.selectedEntry.duration = moment().clone().diff(clonedEntry.start_date, 'seconds');
						}

						// Validate start date
						if (_.isEmpty(options.selectedEntry.start_date)) {
							sb.dom.alerts.alert('Missing info!', 'Please add a start date.', 'warning');
							return;
						}

						// Validate end date
						if (_.isEmpty(options.selectedEntry.end_date)) {
							sb.dom.alerts.alert('Missing info!', 'Please add an end date.', 'warning');
							return;
						}

						// Validate duration
						if (!options.selectedEntry.duration) {
							sb.dom.alerts.alert('Missing info!', 'Please enter a duration.', 'warning');
							return;
						}
		
						ui.grid.btnContainer.saveBtn.loading();
															
						var actionSetup = {
							object: obj,
							state: state,
							options: options
						}
		
						var actionOptions = {
							start_date:	  	options.selectedEntry.start_date,
							end_date:	  	options.selectedEntry.end_date,
							duration:	  	options.selectedEntry.duration,
							note:		  	options.selectedEntry.note,
							tagged_with:   	options.selectedEntry.tagged_with,
							entry:			options.selectedEntry.id,
							mode:			mode
						};

						if ((obj || {}).hasOwnProperty('shift')) {
							actionSetup.object = obj.shift;
						}
						
						if (obj.parent) {
							if (obj.parent.hasOwnProperty('id')) {
								actionOptions.tagged_with.push(obj.parent.id);
							} else {
								actionOptions.tagged_with.push(obj.parent);
							}
						}
		
						actionOptions.tagged_with = _.uniq(actionOptions.tagged_with);

						var hideModal = options.goBack ? false : true;
								
						timeTrackAction(actionSetup, actionOptions, function (updated) {

							state.time_entry = [];

							sb.notify({
								type: 'display-alert',
								data: {
									header: 'Saved',
									body: 'Time entry saved successfully!',
									color: 'green'
								}
							});

							if (typeof onComplete === 'function') {
								onComplete(updated);
							}

						}, hideModal);
							
					}
				}
			}, sb.moduleId);

			ui.patch();

			if (options.create || options.edit) {

				switchToManualMode();	

			} else {

				switchToTrackingMode();

			}

			var tags = options.selectedEntry.tagged_with;
			var selectedTags = _.clone(options.selectedEntry.tagged_with);

			var bpName = obj.hasOwnProperty('shift') ? obj.shift.object_bp_type.substr(1) : obj.object_bp_type.substr(1);
			sb.data.db.obj.getBlueprint(bpName, function(blueprint) {

				fieldOptions = {}
				if (state.fieldName) {
					fieldOptions = blueprint.blueprint[state.fieldName].options;
				} else if (obj.field_name) {
					fieldOptions = blueprint.blueprint[obj.field_name].options;
				}

				var jobTags = !options.jobs ?  fieldOptions.jobs : options.jobs;
				var otherTags = !options.otherTags ? fieldOptions.otherTags : options.otherTags;
				var selectableTags = _.union(jobTags, otherTags);

				_.each(jobTags, function(tag) {
					tags.push(tag);
				});

				_.each(otherTags, function(tag) {
					tags.push(tag);
				});
				
				// Tags
				sb.notify ({
					type: 'view-field',
					data: {
						type: 'tags',
						property: 'tagged_with',
						obj: options.selectedEntry,
						options: {
							build: false,
							tagList: tags,
							selectedTags: selectedTags,
							selectableTags: selectableTags,
							canEdit: true,
							deselectedTagsAreToggleable: true,
							onChange: function(response, updatedTag, action) {

								$('#loader').fadeOut();

								// Just get the ids
								options.selectedEntry.tagged_with = _.each(options.selectedEntry.tagged_with, function(tag) {
									tag = tag.id;
								});

								// Used when adding new tags to the list
								if (action === 'add') {
									if (updatedTag && updatedTag.id) {
										options.selectedEntry.tagged_with.push(updatedTag.id);
									}
								}

								// Used when removing tags from the list
								if (action === 'remove') {
									if (updatedTag && updatedTag.id) {
										options.selectedEntry.tagged_with = _.reject(options.selectedEntry.tagged_with, function(tag) {
											return updatedTag.id === tag;
										});
									}
								}

								// Add selected tags
								_.each(response.selectedTags, function(tag) {
									options.selectedEntry.tagged_with.push(tag);
								});

								// Removed deselected tags
								options.selectedEntry.tagged_with = _.reject(options.selectedEntry.tagged_with, function(tag) {
									return _.contains(response.deselectedTags, tag);
								});

								// Ensure uniqueness
								options.selectedEntry.tagged_with = _.uniq(options.selectedEntry.tagged_with);
			
							}
						},
						ui: ui.grid.tagsContainer.tags,
					}
				});

				options.selectedEntry.tagged_with = selectedTags;

			}, false, true);

		}

		if (!options.selectedEntry.shift) {

			sb.data.db.obj.getById('time_entry', options.selectedEntry.id, function(time_entry) {

				options.selectedEntry = time_entry;

				continueViewEditForm();

			}, {
				shift: true
			});

		} else {

			continueViewEditForm();

		}		

	}
	
	function build_collection(ui, state, options) {

		sb.data.db.obj.getById(
			''
			, options.tagged_with[0]
			, function (obj) {

				var collectionsSetup = getCollectionsSetup(ui, state, {
					callback: UpdateCollectionCallback
				});
		
				switch (options.collection) {
					
					case 'project' :
						collectionsSetup.selectedTags = state.project.id
						break;
						
					case 'team' :
						collectionsSetup.selectedTags =  state.team.id;
						 break;	
					
					case 'my-timelog' :
						collectionsSetup.where.staff = +sb.data.cookie.userId;
						delete collectionsSetup.fields.user;
						break;
						
					default:
		
				}
		
				if (state.pageObject.group_type === 'Headquarters') {
					collectionsSetup.layer = 'hq';
				}
		
				if (options.tagged_with) {
					collectionsSetup.where.tagged_with = options.tagged_with; 		
				}

				if (obj && obj.object_bp_type === 'users') {
					delete collectionsSetup.where.tagged_with;
					collectionsSetup.where.staff = obj.id;
				}
		
				sb.notify({
					type: 'show-collection',
					data: collectionsSetup
				});
				
				ui.patch();

			}
		);

	}
	
	function build_collection_report ( ui, state, draw, mainDom, options) {

		var reportSelection = options.defaultVal || '';	

		var set_config = {
			companies: {
				is_vendor: 0
				, childObjs:{
					name:		true
					, tagged_with: true
				}
			}
			, contacts: {
				childObjs: {
					tagged_with:	true
					, fname: 		true
					, lname: 		true
					, type: 		true
					, company: 	true
					, state: 		true
				}
			}
			, projects: {
				childObjs: {
					name:			true
					, group_type: 		'Projects'
					, state:			true
					, state_updated_on: true
					, type:{
						name:		true
						, states:		true
					}
				}
			}
			, team: {
				group_type: 		'Team'
				, childObjs: {
					name: 		true
					, details:	true
					, parent:		true					
				}
			}
			, users: {
				childObjs: {
					fname:		true
					, lname:		true
					, service:{
						name:	true
					}					
				}
			}
			, system_tags: {
				childObjs: {
					tag: true
				}
			}
		};
		
		function parseReportSelection (config, selection, options) {

			var selectionKey = '';
			var configBlueprint = '';

			options.where = config[selection];			

			switch ( selection ) {
				
				case 'projects':
				case 'team':
				
					configBlueprint = 'groups';

				break;
												
				default:

					configBlueprint = selectionKey = selection;
								
				break;
				
			}

			options.objectType = configBlueprint;

		}			

		var collectionsSetup = {
			actions:		{
				create: false
				, view: false
				, copy: false
				, downloadCSV:true
				, navigateTo: false
				, archive: false
			}
			, domObj:		ui
			, fields:		{
				name: {
					title:'Name'
					, view: function(ui, obj){
						
						if(ui){
							
							ui.makeNode('data','div'
								, {
									text:obj.name

									, tag: 'a'
									, href: sb.data.url.createPageURL(
										'custom'
										, {
											name: 	obj.name + ' Reporting'
											, id: 	'timeTrackingApp'
											, params: {
												t: 'time_entries'
												, obj: obj.id
												, name: obj.name
												, tagged_with: obj.id
											}
										}
									) 
								}
							)					
							
						}else{
							
							return obj.name;
							
						}
						
					}
				}
				, minutes:{
					title:'Total Minutes'
					, type: 'text'
					, view: function(ui, obj){

						var totalMinutes = Math.round(obj.time_logged/60).toString();

						if (ui) {
							ui.makeNode('info','div',{css:'right aligned', text:totalMinutes});
						} else {
							return totalMinutes;
						}
						
					}
				}
			}
			, singleView:{
				view: function(ui, obj, onComplete){

					var opt = {
						tagged_with: [obj.id]
					};
					
					build_collection(ui, state, draw, mainDom, opt)
					
				}
				, link: function (n) {
					
					var link = '';
					if (n.shift && n.shift.id) {
						
						link = sb.data.url.createPageURL(
							'object',
							{
								id: 			n.shift.id,
								type: 			n.shift.object_bp_type,
								object_type: 	n.shift.object_bp_type,
								name: 			n.shift.name
							}
						);
						
					}
					
					return link;
					
				}																											
			}
			, templates: false					
			, metrics:{
				sum:{
					title:'Total',
					fields:{
						minutes:function(ui, getData, test, data){

							var totalAmount = 0;
							
							if(_.isArray(data)){
							
								_.each(data, function(space){										

									totalAmount += space.time_logged;
									
								});
								
							}
							
							if(!_.isUndefined(ui) && !_.isNull(ui)){
								
								ui.empty();
								ui.makeNode('total', 'div', {css: 'right aligned', text:'<b>'+ (totalAmount/60).toFixed(0) +'</b>'});
								ui.patch();	
																			
							}	
								
							
							return (totalAmount/60).toFixed(0);							
					
						},
/*
						duration:function(ui, getData, test, data){

							var totalAmount = 0;
							
							if(_.isArray(data)){
							
								_.each(data, function(inv){										

									totalAmount += inv.duration;
									
								});
								
							}else{
								
								totalAmount = data;
								
							}	

							var hours = 0;
							totalAmount = totalAmount / 60;
							while(totalAmount > 60){
								hours++;
								totalAmount = totalAmount - 60;
							}
							
							if(!_.isUndefined(ui) && !_.isNull(ui)){
								
								ui.empty();
								ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>'+ hours +' hrs '+ totalAmount.toFixed(0) +' minutes</b>'});
								ui.patch();	
																			
							}	
								
							
							return '<b>'+ hours +' hrs '+ totalAmount.toFixed(0) +' minutes</b>';							
					
						}
*/
					}
				}
			}
			, hideDateRange: true
			, parseData: function (data, callback, query, subview, range, types, options) {

				var groupSumSetup = {
					groupOn: 	'tagged_with',
					dateRange: {
						//!TODO: get working with user-selection
						start: 	moment("20111031", "YYYYMMDD").format('YYYY-MM-DD HH:mm:ss.SS'),
						end: 	moment().add(1, 'year').format('YYYY-MM-DD HH:mm:ss.SS')
					}
					, tagged_with: {
						type: 		'any'
						, values: 	_.pluck(data.data, 'id')
					}
				};
				
				sb.data.db.obj.getGroupSum(
					'time_entries'
					, 'duration'
					, groupSumSetup,
					function (sumData) {

						_.each(data.data, function (space) {

							var timeLogged = _.findWhere(sumData, {
								grouped: space.id
							});

							if (timeLogged && timeLogged.sum) {
								
								space.time_logged = timeLogged.sum;
								
							} else {
								
								space.time_logged = 0;
								
							}
							
						});

						callback(data);
						
					}
				);
							                
			}					
			, subviews: {
				table: {
					hideSelectionBoxes:false
					, hideRowActions:  false
				}
				, timeRangeOptions:{
					reports:true
					, hideRowActions:  true							
				}
				
			}			
			, objectType: reportSelection
			, state: state
			, where: {}
			, filterBy: {					
				tags: {
					title: 			'Tag Types'
					, defaultText: 	'Tag Types'
					, defaultValue: 	reportSelection
					, getOptions: 	function (callback) {

						var filterOptions = [
							{
								name: 	'Company'
								, id: 	'companies'
							}
							, {
								name: 	'Contacts'
								, id: 	'contacts'
							}
							, {
								name: 	'Projects'
								, id: 	'projects'
							}
							, {
								name: 	'Team'
								, id: 	'team'
							}
							, {
								name: 	'Users'
								, id: 	'users'
							}
							, {
								name: 	'Tags'
								, id: 	'system_tags'
							}
						];

						callback( filterOptions );

					}
					, parseSelection: parseReportSelection.bind(null, set_config)
				}						
			}
		};
				
		switch (options.collection) {
			
			case 'project' :
				where.shift = state.project.id;
				break;
				
			case 'team' :
			 	where.shift = state.team.id;
			 	break;	
			
			case 'my-timelog' :
				where.staff = +sb.data.cookie.get('uid');
				delete where.childObjs.staff;
				delete collectionsSetup.fields.user;
				break;

		}
		
		collectionsSetup.where = set_config[reportSelection];

		if(state.pageObject.group_type === 'Headquarters') {
			collectionsSetup.layer = 'hq';
		}		

		sb.notify(
			{
				type:	'show-collection'
				, data: 	collectionsSetup
			}
		);		
		
	}
	
	function build_activityBoxView (dom, state, draw, list){

		var sortedResp = _.sortBy(list, 'date_created');
	
		if(list.length == 0){
																
			draw(false);
																
		} else {
			
			var domObj = dom;
			
			domObj.makeNode('feed', 'div', {css: 'ui small feed'});
			domObj.feed.makeNode('fhead', 'div', {tag:'h4', css: 'ui dividing header', text:'Recent Activity'});
			
			_.each(sortedResp.reverse(), function(m, i){
	
				if(i > 2) 
					return
				
				var dom = this;
				
				dom.feed.makeNode('ev-'+m.id, 'div', {css: 'event'});
			
				if(_.isObject(m.staff) && _.isObject(m.staff.profile_image)){
					
					if(m.created_by.profile_image.loc != "//" && m.created_by.profile_image !== null && m.created_by.profile_image !== undefined){
						dom.feed['ev-'+m.id].makeNode('label-'+m.id, 'div', {css: 'label'}).makeNode('ava-'+m.id, 'image', {url:sb.data.files.getURL(m.created_by.profile_image)});
					}else{
						dom.feed['ev-'+m.id].makeNode('label-'+m.id, 'div', {css: 'label'}).makeNode('ava-'+m.id, 'div', {tag:'i', css: 'user circle icon'});	
					}
					
				}
	
				dom.feed['ev-'+m.id].makeNode('content-'+m.id, 'div', {css: 'content'});
				dom.feed['ev-'+m.id]['content-'+m.id].makeNode('summ-'+m.id, 'div', {css: 'summary', text:'<i class="stopwatch icon"></i> Time logged: ' + formatTimeDisplay(m.duration)+ '<div class="date">'+ moment(m.date_created).local().fromNow() +'</div>'});
				dom.feed['ev-'+m.id]['content-'+m.id].makeNode('meta-'+m.id, 'div', {css: 'meta'});
				dom.feed['ev-'+m.id]['content-'+m.id]['meta-'+m.id].makeNode('time', 'div', {css:'like', text:m.shift.name });
				
			}, domObj);
			
			draw(dom);
																
		}
		
		build_activityBoxView.refresh = build_activityBoxView.bind(null, dom, state, draw);
		
	}
	
	function registerMergeTags () {
		
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'myDailyLog'
				, tag: 		'My Daily Log'
				, data: 	function (obj, callback) {
					
					var start = moment().local().startOf('day').unix();
					var end = moment().local().endOf('day').unix();
					var queryObj = {
						start_date: {
							type: 		'after'
							, date: 	start
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, shift: {
								name: true
							}
							, note: true
						}
						, staff: +sb.data.cookie.userId
				    };
					
					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						callback(list);
						
					});
					
				}
				, parse: 	function (logs) {
					
					var html = '';
					
					_.chain(logs)
						.sortBy(
							function (log) {
								
								return moment(log.start_date).unix();
								
							}
						).each(function (log) {
							
							var noteTxt = '';
							if (!_.isEmpty(log.note)) {
								noteTxt = ', <i>'+ log.note +'</i>';
							}
							
							html += 
									moment(log.start_date).local().format('h:mma') +
									' - '+ moment(log.end_date).local().format('h:mma') +
									': '+ log.shift.name +
									noteTxt +
									'<br />';
							
						});
					
					return html;

				}
			}
		});
		
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'myDailyHours'
				, tag: 		'My Hours Today'
				, data: 	function (obj, callback, opts) {
					
					var start = moment().local().startOf('day').unix();
					var queryObj = {
						start_date: {
							type: 		'after'
							, date: 	start
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, shift: {
								name: true
							}
							, note: true
							, duration: true
						}
						, staff: +sb.data.cookie.userId
					};
					
					if (opts && opts.mergeVars && opts.mergeVars.today && moment.isMoment(opts.mergeVars.today)) {
						queryObj.start_date = {
							type: 		'between'
							, start: 	opts.mergeVars.today.clone().local().startOf('day').unix()
							, end: 		opts.mergeVars.today.clone().local().endOf('day').unix()
						};
					}

					// To allow references on the related entity in the query 
					// when the context is a document
					if (
						obj 
						&& obj.object_bp_type === 'contracts' 
						&& !_.isEmpty(obj.related_object)
					) {
						obj.state = {
							entity: obj.related_object
						};
					}

					// If a date field is passed in in opts, use that as the 
					// date to query on
					if (
						Array.isArray(opts)
						&& typeof opts[0] === 'string'
						&& obj && obj.state && obj.state.entity
					) {

						var type = _.findWhere(
							appConfig.Types
							, {
								bp_name: obj.state.entity.object_bp_type.substring(1)
							}
						);
						var dateFieldKey = false;

						_.each(type.blueprint, function (field, key) {
							if (field.name === opts[0]) {
								dateFieldKey = key;
							}
						});

						var dateFieldVal = moment(obj.state.entity[dateFieldKey]);
						if (moment().isValid(dateFieldVal)) {

							queryObj.start_date = {
								type: 		'between'
								, start: 	dateFieldVal.clone().local().startOf('day').unix()
								, end: 		dateFieldVal.clone().local().endOf('day').unix()
							};

						}

						// Use the parent, not the cookie, since this can be kept up to day now
						if (
							!_.isEmpty(obj.state.entity)
							&& !_.isEmpty(obj.state.entity.parent)
							&& typeof obj.state.entity.parent.id === 'number'
						) {
							queryObj.staff = obj.state.entity.parent.id;
						}

					}

					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						callback(list);
						
					});
					
				}
				, parse: 	function (logs) {
					
					var html = '';
					var totalDur = 0;
					var tasks = _.chain(logs)
									.pluck('shift')
									 .uniq(false, function (task) {
										 
										 if (task) {
											 return task.id; 
										 } else {
											 return false;
										 }
										 
									 }).value();
					
					html += '<ul>';
					
					_.each(tasks, function (task) {
						
						if (!task) {
							return;
						}
						
						html += '<li>';
						
						var duration = 0;
						var name = task.name;
						var notes = '';
						
						_.each(logs, function (log) {
							
							if (log && log.shift && log.shift.id === task.id) {
								
								duration += parseInt(log.duration)*1000;
								totalDur += parseInt(log.duration)*1000;
								
								if (!_.isEmpty(log.note)) {
									notes += '<li>'+ log.note +'</li>'
								}
								
							}
							
						});
						
						if (!_.isEmpty(notes)) {
							notes = '<ul>'+ notes +'</ul>'
						}
						
						var tempTime = moment.duration(duration);
						var durTxt = tempTime.hours() +'h '+ tempTime.minutes() +'m';
						
						html += '<strong>'+ durTxt +'</strong>, '+ name + notes;
						html += '</li>';
						
					});
					
					var tempTime = moment.duration(totalDur);
					var durTxt = tempTime.hours() +'h '+ tempTime.minutes() +'m';
					
					html += '</ul>';
					html = '<h3>'+ durTxt +' logged today</h3>'+ html;
					
					return html;

				}
			}
		});
		
		// "{{This Month}}"
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'thisMonth'
				, tag: 		'This Month'
				, data: 	function (obj, callback) {
					
					callback(false);
					
				}
				, parse: 	function (logs) {
					
					return moment().format('MMMM, YYYY');

				}
			}
		});
		
		// "{{Hours This Month}}", time logged in project for the current month,
		// grouped by task
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'hrsThisMonth'
				, tag: 		'Hours This Month'
				, data: 	function (obj, callback) {
					
					var spaceId = 0;
					if (obj.related_object && obj.related_object.id) {
						spaceId = obj.related_object.id;
					} else if (obj.space && obj.space.id) {
						spaceId = obj.space.id
					}
					
					var start = moment().local().startOf('month').unix();
					var end = moment().local().endOf('month').unix();
					var queryObj = {
						start_date: {
							type: 		'after'
							, date: 	start
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, shift: {
								name: true
							}
							, note: true
							, duration: true
						}
						, tagged_with: spaceId
						, _dont_force_portal_user_tag: true
				    };
					
					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						callback(list);
						
					});
					
				}
				, parse: 	function (logs) {
					
					var html = '';
					var totalDur = 0;
					var tasks = _.chain(logs)
									.pluck('shift')
									 .uniq(false, function (task) {
										 
										 if (task) {
											 return task.id; 
										 } else {
											 return false;
										 }
										 
									 }).value();
					
					html += '<ul>';
					
					_.each(tasks, function (task) {
						
						if (!task) {
							return;
						}
						
						html += '<li>';
						
						var duration = 0;
						var name = task.name;
						var notes = '';
						
						_.each(logs, function (log) {
							
							if (log && log.shift && log.shift.id === task.id) {
								
								duration += parseInt(log.duration)*1000;
								totalDur += parseInt(log.duration)*1000;
								
								if (!_.isEmpty(log.note)) {
									notes += '<li>'+ log.note +'</li>'
								}
								
							}
							
						});
						
						if (!_.isEmpty(notes)) {
							notes = '<ul>'+ notes +'</ul>'
						}
						
						var tempTime = moment.duration(duration);
						var days = tempTime.days();
						var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';
						
						html += '<strong>'+ durTxt +'</strong>, '+ name + notes;
						html += '</li>';
						
					});
					
					var tempTime = moment.duration(totalDur);
					var days = tempTime.days();
					var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';
					
					html += '</ul>';
					html = '<h3>'+ durTxt +' logged this month</h3>'+ html;
					
					return html;

				}
			}
		});
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'hrsThisWeek'
				, tag: 		'Hours This Week'
				, data: 	function (obj, callback) {
					
					var spaceId = 0;
					if (obj.related_object && obj.related_object.id) {
						spaceId = obj.related_object.id;
					} else if (obj.space && obj.space.id) {
						spaceId = obj.space.id
					}
					var start = moment().local().startOf('week').unix();
					var end = 	moment().local().endOf('week').unix();
					var queryObj = {
						start_date: {
							type: 		'between'
							, start: 	start
							, end: 		end
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, shift: {
								name: true
							}
							, note: true
							, duration: true
						}
						, tagged_with: spaceId
						, _dont_force_portal_user_tag: true
				    };
					
					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						callback(list);
						
					});
					
				}
				, parse: 	function (logs) {
					
					var html = '';
					var totalDur = 0;
					var tasks = _.chain(logs)
									.pluck('shift')
									 .uniq(false, function (task) {
										 
										 if (task) {
											 return task.id; 
										 } else {
											 return false;
										 }
										 
									 }).value();
					
					html += '<ul>';
					
					_.each(tasks, function (task) {
						
						if (!task) {
							return;
						}
						
						html += '<li>';
						
						var duration = 0;
						var name = task.name;
						var notes = '';
						
						_.each(logs, function (log) {
							
							if (log && log.shift && log.shift.id === task.id) {
								
								duration += parseInt(log.duration)*1000;
								totalDur += parseInt(log.duration)*1000;
								
								if (!_.isEmpty(log.note)) {
									notes += '<li>'+ log.note +'</li>'
								}
								
							}
							
						});
						
						if (!_.isEmpty(notes)) {
							notes = '<ul>'+ notes +'</ul>'
						}
						
						var tempTime = moment.duration(duration);
						var days = tempTime.days();
						var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';
						
						html += '<strong>'+ durTxt +'</strong>, '+ name + notes;
						html += '</li>';
						
					});
					
					var tempTime = moment.duration(totalDur);
					var days = tempTime.days();
					var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';
					
					html += '</ul>';
					html = '<h3>'+ durTxt +' logged this week</h3>'+ html;
					
					return html;

				}
			}
		});
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'hrsThisWeekReport'
				, tag: 		'Hours This Week by Team Member'
				, data: 	function (obj, callback, opts) {
					
					var start = moment().local().startOf('week').unix();
					var end = 	moment().local().endOf('week').unix();
					var queryObj = {
						start_date: {
							type: 		'between'
							, start: 	start
							, end: 		end
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, shift: {
								name: 	true
							}
							, note: 	true
							, duration: true
							, staff: 	'id'
						}
						, _dont_force_portal_user_tag: true
				    };

					// If a date field is passed in in opts, use that as the 
					// date to query on
					if (
						Array.isArray(opts)
						&& typeof opts[0] === 'string'
						&& obj && obj.state && obj.state.entity
					) {

						var type = _.findWhere(
							appConfig.Types
							, {
								bp_name: obj.state.entity.object_bp_type.substring(1)
							}
						);
						var dateFieldKey = false;

						_.each(type.blueprint, function (field, key) {
							if (field.name === opts[0]) {
								dateFieldKey = key;
							}
						});

						var dateFieldVal = moment(obj.state.entity[dateFieldKey]);
						if (moment().isValid(dateFieldVal)) {

							queryObj.start_date = {
								type: 		'between'
								, start: 	dateFieldVal.clone().local().startOf('week').unix()
								, end: 		dateFieldVal.clone().local().endOf('week').unix()
							};

						}

						// Use the parent, not the cookie, since this can be kept up to day now
						if (
							!_.isEmpty(obj.state.entity)
							&& !_.isEmpty(obj.state.entity.parent)
							&& typeof obj.state.entity.parent.id === 'number'
						) {
							queryObj.staff = obj.state.entity.parent.id;
						}

					}
					
					// Get time entries
					sb.data.db.obj.getWhere('time_entries', queryObj, function(entries) {
						
						// Get staff members
						sb.data.db.obj.getById('users', _.chain(entries).pluck('staff').compact().uniq().value(), function (staff) {

							callback({
								entries: entries
								, staff: staff
							});

						});
						
					});
					
				}
				, parse: 	function (data) {

					var html = '<table class="ui very basic simple celled table"><tbody>';
					
					_.each(data.staff, function (staffMember) {

						var duration = 0;
						var memerLogs = _.where(data.entries, {staff: staffMember.id});
						_.each(memerLogs, function (log) {
							
							// if (log && log.shift && log.shift.id === task.id) {
								
								duration += parseInt(log.duration)*1000;
								// totalDur += parseInt(log.duration)*1000;
								
							// }
							
						});

						var tempTime = moment.duration(duration);
						var days = tempTime.days();
						var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';

						html += '<tr>'+
									'<td>'+ staffMember.name +'</td><td>'+ durTxt +'</td>'+
								'</tr>';

					});

					html += '</tbody></table>';
					
					return html;

				}
			}
		});
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'hrsLastWeek'
				, tag: 		'Hours Last Week'
				, data: 	function (obj, callback) {
					
					var spaceId = 0;
					if (obj.related_object && obj.related_object.id) {
						spaceId = obj.related_object.id;
					} else if (obj.space && obj.space.id) {
						spaceId = obj.space.id
					}
					var start = moment().local().startOf('week').subtract(1, 'week').unix();
					var end = 	moment().local().endOf('week').subtract(1, 'week').unix();
					var queryObj = {
						start_date: {
							type: 		'between'
							, start: 	start
							, end: 		end
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, shift: {
								name: true
							}
							, note: true
							, duration: true
						}
						, tagged_with: spaceId
						, _dont_force_portal_user_tag: true
				    };
					
					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						callback(list);
						
					});
					
				}
				, parse: 	function (logs) {
					
					var html = '';
					var totalDur = 0;
					var tasks = _.chain(logs)
									.pluck('shift')
									 .uniq(false, function (task) {
										 
										 if (task) {
											 return task.id; 
										 } else {
											 return false;
										 }
										 
									 }).value();
					
					html += '<ul>';
					
					_.each(tasks, function (task) {
						
						if (!task) {
							return;
						}
						
						html += '<li>';
						
						var duration = 0;
						var name = task.name;
						var notes = '';
						
						_.each(logs, function (log) {
							
							if (log && log.shift && log.shift.id === task.id) {
								
								duration += parseInt(log.duration)*1000;
								totalDur += parseInt(log.duration)*1000;
								
								if (!_.isEmpty(log.note)) {
									notes += '<li>'+ log.note +'</li>'
								}
								
							}
							
						});
						
						if (!_.isEmpty(notes)) {
							notes = '<ul>'+ notes +'</ul>'
						}
						
						var tempTime = moment.duration(duration);
						var days = tempTime.days();
						var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';
						
						html += '<strong>'+ durTxt +'</strong>, '+ name + notes;
						html += '</li>';
						
					});
					
					var tempTime = moment.duration(totalDur);
					var days = tempTime.days();
					var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';
					
					html += '</ul>';
					html = '<h3>'+ durTxt +' logged this week</h3>'+ html;
					
					return html;

				}
			}
		});
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'hrsLastMonth'
				, tag: 		'Hours Last Month'
				, data: 	function (obj, callback) {
					
					var start = moment().local().subtract(1, 'month').startOf('month').unix();
					var end = 	moment().local().subtract(1, 'month').endOf('month').unix();
					var queryObj = {
						start_date: {
							type: 		'between'
							, start: 	start
							, end: 		end
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, shift: {
								name: true
							}
							, note: true
							, duration: true
						}
						, tagged_with: obj.related_object.id
						, _dont_force_portal_user_tag: true
				    };
					
					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						callback(list);
						
					});
					
				}
				, parse: 	function (logs) {
					
					var html = '';
					var totalDur = 0;
					var tasks = _.chain(logs)
									.pluck('shift')
									 .uniq(false, function (task) {
										 
										 if (task) {
											 return task.id; 
										 } else {
											 return false;
										 }
										 
									 }).value();
					
					html += '<ul>';
					
					_.each(tasks, function (task) {
						
						if (!task) {
							return;
						}
						
						html += '<li>';
						
						var duration = 0;
						var name = task.name;
						var notes = '';
						
						_.each(logs, function (log) {
							
							if (log && log.shift && log.shift.id === task.id) {
								
								duration += parseInt(log.duration)*1000;
								totalDur += parseInt(log.duration)*1000;
								
								if (!_.isEmpty(log.note)) {
									notes += '<li>'+ log.note +'</li>'
								}
								
							}
							
						});
						
						if (!_.isEmpty(notes)) {
							notes = '<ul>'+ notes +'</ul>'
						}
						
						var tempTime = moment.duration(duration);
						var days = tempTime.days();
						var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';
						
						html += '<strong>'+ durTxt +'</strong>, '+ name + notes;
						html += '</li>';
						
					});
					
					var tempTime = moment.duration(totalDur);
					var days = tempTime.days();
					var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';
					
					html += '</ul>';
					html = '<h3>'+ durTxt +' logged this month</h3>'+ html;
					
					return html;

				}
			}
		});
		
		// "{{Hours This Month By Tags}}", time logged in project for the current month,
		// grouped by task
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'hrsThisMonthByTag'
				, tag: 		'Hours This Month By Tag'
				, data: 	function (obj, callback) {
					
					var spaceId = 0;
					if (obj.related_object && obj.related_object.id) {
						spaceId = obj.related_object.id;
					} else if (obj.space && obj.space.id) {
						spaceId = obj.space.id
					}
					
					var start = moment().local().startOf('month').unix();
					var end = moment().local().endOf('month').unix();
					var queryObj = {
						start_date: {
							type: 		'after'
							, date: 	start
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, tagged_with: true
							, duration: true
						}
						, tagged_with: spaceId
						, _dont_force_portal_user_tag: true
				    };
					
					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						var tags = [];
						_.each(list, function (entry) { tags.concat(list.tagged_with); });
						tags = _.uniq(tags, list);
						
						getBreakoutSums(list, callback);
						
					});
					
				}
				, parse: 	function (breakout) {
					
					function showTagList (list) {
						
						var html = '';
						_.each(list, function (tag) {
							
							html += '<li>'+ tag.name +', '+ formatTimeDisplay(tag._time_logged) +'</li>';
							
						});
						
						return html;
						
					}
					
					var html = '';
					
					if (breakout.JobType) {
						
						html += 
							'<h3>Jobs</h3>'+
							'<ul>'+
								showTagList(breakout.JobType) +
							'</ul>';
						
					}
					if (breakout.Team) {
						
						html += 
							'<h3>Teams</h3>'+
							'<ul>'+
								showTagList(breakout.Team) +
							'</ul>';
						
					}
					if (breakout.users) {
						
						html += 
							'<h3>Team Members</h3>'+
							'<ul>'+
								showTagList(breakout.users) +
							'</ul>';
						
					}
					if (breakout.system_tags) {
						
						html += 
							'<h3>#Tags</h3>'+
							'<ul>'+
								showTagList(breakout.system_tags) +
							'</ul>';
						
					}
					
					return html;

				}
			}
		});
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'hrsThisWeekByTag'
				, tag: 		'Hours This Week By Tag'
				, data: 	function (obj, callback) {
					
					var spaceId = 0;
					if (obj.related_object && obj.related_object.id) {
						spaceId = obj.related_object.id;
					} else if (obj.space && obj.space.id) {
						spaceId = obj.space.id
					}
					
					var start = moment().local().startOf('week').unix();
					var end = moment().local().endOf('week').unix();
					var queryObj = {
						start_date: {
							type: 		'between'
							, start: 	start
							, end: 		end
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, tagged_with: true
							, duration: true
						}
						, tagged_with: spaceId
						, _dont_force_portal_user_tag: true
				    };
					
					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						var tags = [];
						_.each(list, function (entry) { tags.concat(list.tagged_with); });
						tags = _.uniq(tags, list);
						
						getBreakoutSums(list, callback);
						
					});
					
				}
				, parse: 	function (breakout) {
					
					function showTagList (list) {
						
						var html = '';
						_.each(list, function (tag) {
							
							html += '<li>'+ tag.name +', '+ formatTimeDisplay(tag._time_logged) +'</li>';
							
						});
						
						return html;
						
					}
					
					var html = '';
					
					if (breakout.JobType) {
						
						html += 
							'<h3>Jobs</h3>'+
							'<ul>'+
								showTagList(breakout.JobType) +
							'</ul>';
						
					}
					if (breakout.Team) {
						
						html += 
							'<h3>Teams</h3>'+
							'<ul>'+
								showTagList(breakout.Team) +
							'</ul>';
						
					}
					if (breakout.users) {
						
						html += 
							'<h3>Team Members</h3>'+
							'<ul>'+
								showTagList(breakout.users) +
							'</ul>';
						
					}
					if (breakout.system_tags) {
						
						html += 
							'<h3>#Tags</h3>'+
							'<ul>'+
								showTagList(breakout.system_tags) +
							'</ul>';
						
					}
					
					return html;

				}
			}
		});

		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'hrsLastWeekByTag'
				, tag: 		'Hours Last Week By Tag'
				, data: 	function (obj, callback) {
					
					var spaceId = 0;
					if (obj.related_object && obj.related_object.id) {
						spaceId = obj.related_object.id;
					} else if (obj.space && obj.space.id) {
						spaceId = obj.space.id
					}
					
					var start = moment().local().startOf('week').subtract(1, 'week').unix();
					var end = moment().local().endOf('week').subtract(1, 'week').unix();
					var queryObj = {
						start_date: {
							type: 		'between'
							, start: 	start
							, end: 		end
						}
						, childObjs: {
							start_date: true
							, end_date: true
							, tagged_with: true
							, duration: true
						}
						, tagged_with: spaceId
						, _dont_force_portal_user_tag: true
				    };
					
					sb.data.db.obj.getWhere('time_entries', queryObj, function(list) {
						
						var tags = [];
						_.each(list, function (entry) { tags.concat(list.tagged_with); });
						tags = _.uniq(tags, list);
						
						getBreakoutSums(list, callback);
						
					});
					
				}
				, parse: 	function (breakout) {
					
					function showTagList (list) {
						
						var html = '';
						_.each(list, function (tag) {
							
							html += '<li>'+ tag.name +', '+ formatTimeDisplay(tag._time_logged) +'</li>';
							
						});
						
						return html;
						
					}
					
					var html = '';
					
					if (breakout.JobType) {
						
						html += 
							'<h3>Jobs</h3>'+
							'<ul>'+
								showTagList(breakout.JobType) +
							'</ul>';
						
					}
					if (breakout.Team) {
						
						html += 
							'<h3>Teams</h3>'+
							'<ul>'+
								showTagList(breakout.Team) +
							'</ul>';
						
					}
					if (breakout.users) {
						
						html += 
							'<h3>Team Members</h3>'+
							'<ul>'+
								showTagList(breakout.users) +
							'</ul>';
						
					}
					if (breakout.system_tags) {
						
						html += 
							'<h3>#Tags</h3>'+
							'<ul>'+
								showTagList(breakout.system_tags) +
							'</ul>';
						
					}
					
					return html;

				}
			}
		});
		
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'activityLog'
				, tag: 		'Activity Today'
				, data: 	function (obj, callback, opts) {

					var start = moment().local().startOf('day').unix();
					var end = moment().local().endOf('day').unix();
					var queryObj = {
						date_created: {
							type: 		'between'
							, start: 	start
							, end: 		end
						}
						, childObjs:{
							date_created: true,
							note_type:{
								name:true
							},
							note:true,
							type:true,
							type_id:{
								name:true
							}
						}
						, created_by: +sb.data.cookie.userId
					};

					// To allow references on the related entity in the query 
					// when the context is a document
					if (
						obj 
						&& obj.object_bp_type === 'contracts' 
						&& !_.isEmpty(obj.related_object)
					) {
						obj.state = {
							entity: obj.related_object
						};
					}

					// If a date field is passed in in opts, use that as the 
					// date to query on
					if (
						Array.isArray(opts)
						&& typeof opts[0] === 'string'
						&& obj && obj.state && obj.state.entity
					) {

						var type = _.findWhere(
							appConfig.Types
							, {
								bp_name: obj.state.entity.object_bp_type.substring(1)
							}
						);
						var dateFieldKey = false;

						_.each(type.blueprint, function (field, key) {
							if (field.name === opts[0]) {
								dateFieldKey = key;
							}
						});

						var dateFieldVal = moment(obj.state.entity[dateFieldKey]);
						if (moment().isValid(dateFieldVal)) {

							queryObj.date_created = {
								type: 		'between'
								, start: 	dateFieldVal.clone().local().startOf('day').unix()
								, end: 		dateFieldVal.clone().local().endOf('day').unix()
							};

						}

						// Use the parent, not the cookie, since this can be kept up to day now
						if (
							!_.isEmpty(obj.state.entity)
							&& !_.isEmpty(obj.state.entity.parent)
							&& typeof obj.state.entity.parent.id === 'number'
						) {
							queryObj.created_by = obj.state.entity.parent.id;
						}

					}

					if (opts && opts.mergeVars && opts.mergeVars.today && moment.isMoment(opts.mergeVars.today)) {
						queryObj.date_created = {
							type: 		'between'
							, start: 	opts.mergeVars.today.clone().local().startOf('day').unix()
							, end: 		opts.mergeVars.today.clone().local().endOf('day').unix()
						};
					}

					sb.data.db.obj.getWhere('notes', queryObj, function(list) {

						callback(list);
						
					});
					
				}
				, parse: 	function (logs) {
					
					var html = '<ul>';
					
					_.chain(logs)
						.sortBy(
							function (log) {
								
								return moment(log.date_created).unix();
								
							}
						).each(function (log) {
							
							var objName = '--';
							if (!_.isEmpty(log.type_id)) {
								objName = log.type_id.name;
							}
							
							html += '<li><i>'+ moment(log.date_created).local().format('h:mma') +'</i>, in <strong>'+ objName +'</strong>';
							html += '<ul><li>'+ log.note +'</li></ul>';
							html += '</li>';
							
						});
					
					html += '</ul>';
					
					return html;

				}
			}
		});
		
	}
	
	return {
		
		init: function () {

			sb.notify({
				type: 'register-application'
				, data: {
					navigationItem: {
						moduleId: sb.moduleId
						, id: 	'timeTracking'
						, title:	'Time Entries'
						, icon: 	'<i class="stopwatch icon"></i>'
						, menuItemView: function(ui, options) {

							if (_.isEmpty(UI_CACHE.modal)) {
								UI_CACHE.modal = options.ui.makeNode('modal', 'modal', {});
								UI_CACHE.modal.body.makeNode('list', 'div', {});
								UI_CACHE.modal.body.makeNode('form', 'div', {});
							}

							setTimeout(function() {
					
								getTimeEntries({
									staff:	   +sb.data.cookie.userId,
									duration: {
										type: 'not_set'
									},
									childObjs: {
										start_date: true,
										end_date: true,
										staff:	  {
											fname: true,
											lname: true,
											profile_pic: true,
										},
										shift: true,
										duration: true,
										object_uid: true
									}
								}, navbarDisplay.bind(null, ui));
								
							}, 0.1);

						}
						, views: 	[
							///MYSTUFF timeTracking
							{
								id:'mytimelog'
								, type:'myStuff'
								, name:'Time Sheet'
								, title: 'Time Sheet'
								, tip: 'Log & Record Time on Tasks'
								, icon: {
									type:'stopwatch'
									, color: 'red'
								}
								, default: true
								, settings: false
								, mainViews: [
									{
										dom: function(dom, state) {

											build_collection(dom, state, {
												collection:'my-timelog'
                                                , tagged_with: [+sb.data.cookie.userId]
											});										
											
										}
									}
								]
								, boxViews: []
							}
							/// HEADQUARTERS timeTrackingApp							
 							, {
								id: 			'timeTrackingApp'
								, type: 		'hqTool'
								, name: 		'Time Sheet'
								, tip: 		'Keep track of time entries logged across all Teams.'
								, icon: 		{
									type:	'stopwatch'
									, color: 	'red'
								}
								, default: 	true
								, settings: 	false
								, mainViews: [
									{
										dom:function (dom, state, draw, mainDom) {	

											build_collection_report (
												dom
												, state
												, draw
												, mainDom
												, {
													collection:	'hq'
													, defaultVal: 	'companies'
												}
											);

										}	
									}
								]
							}
 							/// HEADQUARTERS timeTrackingApp Report View
							, {
								id: 	'timeTrackingApp',
								type: 	'custom',
								name: 	'Report Page',
								title: 	'Report Page',
								tip: 	'Reporting for Time Entries',
								settings: false,
								mainViews: [{
									dom: function (dom, state, options) {

										var options = {};
										
										if ( 
											state.hasOwnProperty('params') 
										     && !!((state.params || {}).tagged_with)
										) {
											options.tagged_with = [parseInt(state.params.tagged_with)];
										}
										
										build_collection(dom, state, options);

									}
								}],
								boxViews: []
							} 														
							/// TEAM timeTrackingApp							
 							, {
								id: 			'timeTrackingApp'
								, type: 		'teamTool'
								, name: 		'Time Sheet'
								, tip: 		'Keep track of your Team\'s time entries.'
								, icon: 		{
									type:	'stopwatch'
									, color: 	'red'
								}
								, default: 	true
								, settings: 	false
								, mainViews: [
									{
										dom:function (dom, state) {	

											build_collection(dom, state, {
												collection: 'team'
											});

										}	
									}
								]
							}
							/// PROJECT timeTrackingApp 
							, {
								id: 			'timeTrackingApp'
								, type: 		'tool'
								, name: 		'Time Sheet'
								, tip: 		'Keep track of your Project\'s time entries.'
								, icon: 		{
									type:	'stopwatch'
									, color: 	'red'
								}
								, default: 	true
								, settings: 	false
								, mainViews: [
									{
										dom:function (dom, state) {	

											build_collection(dom, state, {
												collection:'project'
											});
										}	
									}
								]
								, boxViews:[
									{
										id:'timeTrackingApp'
										, width: 'six'
										, title: 'Time Sheet'
										, dom:function(dom, state, draw){
							
											var whereOpt = {
												tagged_with: [state.project.id]
												, childObjs: {
													start_date: true
													, end_date: true
													, staff:    true
													, shift:    true
													, duration: true
													, tagged_with: true
												}
											};

											getTimeEntries(whereOpt, build_activityBoxView.bind(null, dom, state, draw));
												
										}
									}
								]
							}
							/// OBJECT view time entries
							, {
								id:'time_entries-obj',
								type:'object-view',
								title:'Time Sheet',
								icon: 'stopwatch',
								default:true,
								settings:false,
								view:view
							},
							/// TEAM TOOL timeClock (Keypad)
							, {
								id: 			'timeClockApp'
								, type: 		'teamTool'
								, name: 		'Time Clock'
								, tip: 		'Keep track of your Team\'s time.'
								, icon: 		{
									type:	'stopwatch'
									, color: 	'orange'
								}
								, default: 	true
								, settings: 	false
								, mainViews: [
									{
										dom:function (dom, state, draw, mainDom) {

											sb.data.db.obj.getAll('staff_base', function(locs){

												if (locs.length == 0) {
											
													dom.makeNode('noloc' , 'div', { css: 'ui header', text:'Navigate to Team Members tab in Settings and add a location'});
													dom.makeNode('sett' , 'div'
														, { 
															css: 'ui blue link item'
															, tag: 'a' 
															, text: 'Go to settings'
															, href: sb.url + '/app/' + appConfig.instance + '#settings'
														}
													);
													
												} else {
													
													var locationId = locs[0].id;
											
													var formArgs = {
														location: {
															name: 'location',
															label: 'Select a location',
															type: 'select',
															options: _.map(locs, function(l){ return {name:l.name, value:l.id}; }),
															change: function(form, selected){
											
																locationId = selected;															
															}
														}
													};
													
													dom.makeNode('form' , 'form', formArgs);
													
													dom.makeNode('launch' , 'div'
														, {
															text:'<i class="stopwatch icon"></i> Launch Time Clock Keypad'
															, css:'ui blue link item'
															, tag:'a'
														}
													).notify('click'
														, {
															type: 'timeClock-run'
															, data: {
																run: function(){
																	window.location.href = sb.url + '/app/timeclock#?&i=' + appConfig.instance + '&hq=' + appConfig.headquarters.id + '&t=' + state.id + '&l=' + locationId;																
																}
															}
														}
													);																								
																										
												}	
																								
												dom.patch();	
											
											});
										
										}	
									}
								]
							}
						]
					}
				}
			});

			var toolRegistrationsSetup = [
				{
					id:'timesheetReport'
					, layers: ['hq', 'team', 'project', 'myStuff']
					, name:'Time Sheet Report'
					, tip:'View Recorded Time Entries across Tags'
					, icon:{
						type:'stopwatch'
						, color:'green'
					}
					, default:true
					, settings:[]
					, mainViews:[
						{
							dom:function(dom, state, draw, mainDom, setup){

								build_collection_report (
									dom
									, state
									, draw
									, mainDom
									, {
										collection:	state.layer
										, defaultVal: 	'companies'
									}
								);
															
							}
						}
					]
					, boxViews:[]
				} 				
			];
			
			sb.notify(
				{
					type: 'register-tool'
					, data: {
						navigationItem: {
							moduleId: 	sb.moduleId
							, instanceId: 	sb.instanceId
							, id:		'reportTimeSheet'
							, title:		'reportTimeSheet'
							, icon:		'<i class="stopwatch icon"></i>'
							, views: 		toolRegistrationsSetup
						}
					}
				}
			);			
			
			sb.listen({
				'timeClock-run': 	    		 	this.run
				, 'timeTracking-field-view': 		this.fieldView
				, 'timelog-field-view-update': 		this.update
				, 'display-keypad': 				this.displayKeypad
				, 'destroy-timer-field-view': 		this.destroySingleFieldView
				, 'start-keypad-portal':			this.startKeypadPortal
			});
			
			registerMergeTags();
			
		}

		, destroySingleFieldView: function (data) {
			
			var toDestroy = _.where(activeTimersList, {
				fieldId: data._fieldId
			});
			
			_.each(toDestroy, function (intervalObj) {
				
				clearInterval(intervalObj.intervalId);
				
			});
			
			activeTimersList = _.reject(activeTimersList, {fieldId: data._fieldId});
			
		}
		
		, displayKeypad: function (setup) {

			if(setup.hasOwnProperty('dom') && setup.hasOwnProperty('obj')) {
			
				build_keypad (setup.dom, setup.obj, setup.options, setup.onComplete);
				
			}
			
			return;
						
		}
		
		, fieldView: function (setup) {

			function continueFieldView(obj) {

				setup.obj = obj;

				var runningProperty = getPropertyNames(setup.obj, setup.state).runningProperty;
				var runningEntry = _.findWhere(setup.obj[runningProperty], {userId: parseInt(sb.data.cookie.get('uid'))});

				setup.state.time_entry = [];

				if (!_.isEmpty(runningEntry)) {
					setup.state.time_entry.push(runningEntry);
				}

				fieldView(setup.domObj, setup.obj, setup.state, setup.options);

			}

			if (setup.hasOwnProperty('domObj') && setup.hasOwnProperty('obj')) {
				
				setup.options.fieldName = setup.state.fieldName;
				setup.options.domObj = setup.domObj;
					
				if (setup.state.toggleDisplay){
					
					if (setup.hasOwnProperty('openTimeEntries')) {
						
						setup.state.time_entry = _.findWhere(setup.openTimeEntries, {shift: setup.obj.id});
				
						fieldView(setup.domObj, setup.obj, setup.state, setup.options);
						
						return;
						
					}

					if (!setup.options.inCollection) {

						sb.data.db.obj.getById('', setup.obj.id, function(response) {
							
							continueFieldView(response)
							
						});

					} else {

						continueFieldView(setup.obj)

					}

					return;
						
				} else {
					
					sb.data.db.obj.getById('', setup.obj.id, function(res){
						
						setup.obj = res;

						fieldView(setup.domObj, setup.obj, setup.state, setup.options);
						
					});
					
				}		
					
			}
			
		}
		
		, run: function (data) { data.run(data); }
		
		, startKeypadPortal: function(setup){

			var instance = sb.data.url.getParams().i;
			var hq = parseInt( sb.data.url.getParams().hq );
			var team = parseInt( sb.data.url.getParams().t );
			var location = parseInt( sb.data.url.getParams().l );
			
			function getSetupData ( callback ) {
				
				var data = {};
	
				sb.data.db.controller(
					'getObjectsWhere&pagodaAPIKey='+ instance
					, {
						queryObj:{
							instance:instance
						}
						, instance:instance
						, objectType:'instances'
					}				
					, function(instanceRes){

						appConfig = instanceRes[0];

						sb.data.db.controller(
							'getObjectById&api_webform=true&pagodaAPIKey='+ instance
							, { value:hq, type:'groups' }
							, function(headquarters){

								sb.data.db.controller(
									'getObjectById&api_webform=true&pagodaAPIKey='+ instance
									, { value:team, type:'groups' }
									, function(team){

										sb.data.db.controller(
											'getObjectById&api_webform=true&pagodaAPIKey='+ instance
											, { value:location, type:'groups' }
											, function(location){

												data.hq = headquarters;
												data.team = team;
												data.location = location;
												
												callback(data);
								
											}
											, sb.url + '/api/_getAdmin.php?do='
										);	
									}
									, sb.url + '/api/_getAdmin.php?do='
								);
							}
							, sb.url + '/api/_getAdmin.php?do='
						);
					}
					, sb.url + '/api/_getAdmin.php?do='
				);	
			}
			
			dom = sb.dom.make('.main');

			dom.makeNode('menu', 'div', {css:'ui huge inverted stackable menu'});

			dom.makeNode('cont', 'div', {css:''});
			
			dom.cont.makeNode('loader' , 'div', { css: 'ui basic very padded loading segment' });

			dom.build();

			sb.data.db.setAPIPath('../../api/_getAdmin.php');
			
			getSetupData(
				function(data){
				
					var stateObj = {
						hq: 			data.hq
						, team:		data.team
						, location:	data.location
					};	

					build_singleTimeClock(
						dom.cont
						, stateObj
						, function(draw){
	
							if ( draw.dom ){
								
								draw.dom.patch();
								
								if (draw.after && typeof draw.after == 'function')
									draw.after(draw.dom);						
							}					
						}
					);
								
				}
			);	
		}
		
		, update: function (data) {

            getTimeEntries(null, function(response) {

                navbarDisplay(UI_CACHE.navbar_badge, response);
				
				if (data.object.group_type == 'Project') {

					if (build_activityBoxView.hasOwnProperty('refresh')) {
						build_activityBoxView.refresh(response);
					}				
					
				}		
				
			});
			
		}
				
	}
	
});