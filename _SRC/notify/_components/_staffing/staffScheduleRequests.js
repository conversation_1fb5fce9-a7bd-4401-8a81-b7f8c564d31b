/*
Factory.register('staffScheduleRequests', function(sb) {
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'staffScheduleRequests',
						title: 'Schedule Requests',
						icon: '<i class="fa fa-users"></i>',
						views: [
							{
								id: 'table',
								default: true,
								type: 'custom',
								title: 'Schedule Requests',
								icon:'<i class="fa fa-th-list"></i>',
								dom: function(dom, state, draw) {
									
									sb.notify({
										type: 'start-schedule_requests',
										data: {
											domObj: dom,
											draw: draw
										}
									});
									
								}
							}
						]
					}
				}
			});
			
		},
		
		destroy: function() {}
		
	}
	
});
*/