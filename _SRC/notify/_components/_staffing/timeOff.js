Factory.register("timeOff", function (sb) {
  var adminRequestTableDom = null,
    requestTableDom = {},
    modalDom = null,
    staff = [],
    timeOffObjs = [],
    fiscalYearEnd = null,
    fiscalYearStart = null;

  function buildAdminTimeOffTable(tableDom, timeOffObjs) {
    tableDom.makeNode("timeOffTable", "table", {
      css: "table-hover table-condensed",
      columns: {
        startDate: "Start Date",
        endDate: "End Date",
        type: "Type",
        status: "Status",
        staff: "Staff Member",
        reason: "Reason",
        btns: "",
      },
      dataTable: true,
    });

    _.each(timeOffObjs, function (timeOffObj) {
      var statusDetails = "";

      switch (timeOffObj.status) {
        case "approved":
          var rowCSS = "bg-success";
          statusDetails =
            "<br /><small>On: " +
            moment(timeOffObj.status.statusChangedOn).format("YYYY-MM-DD") +
            " by " +
            _.where(staff, { id: timeOffObj.statusChangedBy })[0].fname +
            " " +
            _.where(staff, { id: timeOffObj.statusChangedBy })[0].lname +
            "</small>";
          break;

        case "denied":
          var rowCSS = "bg-danger";
          statusDetails =
            "<br /><small>On: " +
            moment(timeOffObj.status.statusChangedOn).format("YYYY-MM-DD") +
            " by " +
            _.where(staff, { id: timeOffObj.statusChangedBy })[0].fname +
            " " +
            _.where(staff, { id: timeOffObj.statusChangedBy })[0].lname +
            "</small>";
          break;

        default:
          var rowCSS = "";
      }

      tableDom.timeOffTable.makeRow(
        timeOffObj.id,
        [
          timeOffObj.startDate,
          timeOffObj.endDate,
          timeOffObj.type.toUpperCase(),
          timeOffObj.status.toUpperCase() + statusDetails,
          _.where(staff, { id: timeOffObj.staffId })[0].fname +
            " " +
            _.where(staff, { id: timeOffObj.staffId })[0].lname,
          timeOffObj.reason,
        ],
        { css: rowCSS }
      );

      if (
        moment(timeOffObj.startDate).add(1, "day").isAfter(moment(), "days")
      ) {
        tableDom.timeOffTable.body[timeOffObj.id].btns.makeNode(
          "buttonGroup",
          "buttonGroup",
          {}
        );

        tableDom.timeOffTable.body[timeOffObj.id].btns.buttonGroup
          .makeNode("delete", "button", {
            text: '<i class="glyphicon glyphicon-remove"></i>',
            css: "btn-danger",
          })
          .notify(
            "click",
            {
              type: "delete-time-off-request",
              data: {
                timeOffObj: timeOffObj,
                adminView: true,
              },
            },
            sb.moduleId
          );

        tableDom.timeOffTable.body[timeOffObj.id].btns.buttonGroup
          .makeNode("deny", "button", {
            text: '<i class="glyphicon glyphicon-minus"></i>',
            css: "btn-warning",
          })
          .notify(
            "click",
            {
              type: "update-time-off-request",
              data: {
                timeOffObj: timeOffObj,
                status: "denied",
                adminView: true,
              },
            },
            sb.moduleId
          );

        tableDom.timeOffTable.body[timeOffObj.id].btns.buttonGroup
          .makeNode("approve", "button", {
            text: '<i class="glyphicon glyphicon-ok"></i>',
            css: "btn-success",
          })
          .notify(
            "click",
            {
              type: "update-time-off-request",
              data: {
                timeOffObj: timeOffObj,
                status: "approved",
                adminView: true,
              },
            },
            sb.moduleId
          );
      }
    });

    return tableDom.timeOffTable;
  }

  function buildTimeOffTable(tableDom, timeOffObjs) {
    tableDom.makeNode("timeOffTable", "table", {
      css: "table-hover table-condensed",
      columns: {
        type: "Type",
        date: "Date",
        status: "Status",
        btns: "",
      },
      dataTable: true,
    });

    _.each(timeOffObjs, function (timeOffObj) {
      switch (timeOffObj.status) {
        case "approved":
          var rowCSS = "bg-success";
          break;

        case "denied":
          var rowCSS = "bg-danger";
          break;

        default:
          var rowCSS = "";
      }

      tableDom.timeOffTable.makeRow(
        timeOffObj.id,
        [
          timeOffObj.type.toUpperCase(),
          '<span class="text-bold">From ' +
            moment(timeOffObj.startDate, "YYYY-MM-DD").format(
              "ddd, MMM Do YYYY"
            ) +
            " to " +
            moment(timeOffObj.endDate, "YYYY-MM-DD").format(
              "ddd, MMM Do YYYY"
            ) +
            "</span><br />Reason: " +
            timeOffObj.reason,
          timeOffObj.status.toUpperCase(),
        ],
        { css: rowCSS }
      );

      if (moment(timeOffObj.startDate, "YYYY-MM-DD") > moment()) {
        tableDom.timeOffTable.body[timeOffObj.id].btns
          .makeNode("delete", "button", {
            text: '<i class="glyphicon glyphicon-remove"></i>',
            css: "btn-danger",
          })
          .notify(
            "click",
            {
              type: "delete-time-off-request",
              data: {
                timeOffObj: timeOffObj,
                adminView: false,
              },
            },
            sb.moduleId
          );
      }
    });

    return tableDom.timeOffTable;
  }

  return {
    init: function () {
      sb.listen({
        "start-timeOff": this.start,
      });
    },

    start: function (setupObj) {
      sb.listen({
        "delete-time-off-request": this.deleteTimeOffRequest,
        "show-admin-request-table": this.showAdminTable,
        "show-request-table": this.showTable,
        "show-request-time-off-form": this.showRequestTimeOffForm,
        "show-request-to-work-form": this.showRequestToWorkForm,
        "show-time-off-request-list-modal": this.showListModal,
        "stop-timeOff": this.stop,
        "submit-time-off-request": this.submitTimeOffRequest,
        "time-off-request-object-changed": this.rebuildTable,
        "update-time-off-request": this.updateObject,
      });
    },

    stop: function () {
      sb.listen({
        "start-timeOff": this.start,
      });

      adminRequestTableDom = null;
      requestTableDom = {};
      staff = [];
      timeOffObjs = [];
    },

    deleteTimeOffRequest: function (deleteObj) {
      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "This cannot be undone.",
        },
        function (resp) {
          if (resp) {
            sb.data.db.obj.erase(
              "time_off_request",
              deleteObj.timeOffObj.id,
              function (response) {
                sb.dom.alerts.alert("Success!", "", "success");

                sb.notify({
                  type: "time-off-request-object-changed",
                  data: {
                    object: deleteObj.timeOffObj,
                    type: "erase",
                    adminView: deleteObj.adminView,
                  },
                });
              }
            );
          }
        }
      );
    },

    rebuildTable: function (setupObj) {
      if (setupObj.type == "new") {
        timeOffObjs.push(setupObj.object);
      }

      if (setupObj.type == "erase") {
        timeOffObjs = _.reject(timeOffObjs, function (timeOffObj) {
          return timeOffObj.id == setupObj.object.id;
        });
      }

      if (setupObj.adminView) {
        sb.notify({
          type: "show-admin-request-table",
          data: {
            requests: timeOffObjs,
          },
        });
      } else {
        sb.notify({
          type: "show-request-table",
          data: {
            requests: timeOffObjs,
          },
        });
      }
    },

    showAdminTable: function (setupObj) {
      fiscalYearStart = setupObj.fiscalYearStart;
      fiscalYearEnd = setupObj.fiscalYearEnd;

      if (staff.length == 0) {
        staff = setupObj.staff;
      }

      if (adminRequestTableDom == null) {
        adminRequestTableDom = sb.dom.make(setupObj.domObj.selector);
      }

      timeOffObjs = setupObj.requests;

      adminRequestTableDom.makeNode("lineBreak", "lineBreak", { spaces: 1 });

      adminRequestTableDom.makeNode("tableContainer", "container", {});

      adminRequestTableDom.tableContainer = buildAdminTimeOffTable(
        adminRequestTableDom.tableContainer,
        timeOffObjs
      );

      adminRequestTableDom.build();
    },

    showTable: function (setupObj) {
      if (staff.length == 0) {
        staff = setupObj.staff;
      }

      if (adminRequestTableDom == null) {
        adminRequestTableDom = sb.dom.make(setupObj.domObj.selector);
      }

      timeOffObjs = setupObj.requests;

      adminRequestTableDom.makeNode("tableContainer", "container", {});

      adminRequestTableDom.tableContainer = buildTimeOffTable(
        adminRequestTableDom.tableContainer,
        timeOffObjs
      );

      adminRequestTableDom.build();
    },

    showListModal: function (setupObj) {
      var domObj = sb.dom.make(setupObj.domObj.selector);

      domObj.makeNode("modalContainer", "container", {});

      domObj.modalContainer.makeNode("listModal", "modal", {});

      domObj.modalContainer.listModal.body.makeNode(
        "tableHeader",
        "headerText",
        { text: "Your requests this cycle<br />", size: "small" }
      );

      domObj.modalContainer.listModal.body.makeNode(
        "tableContainer",
        "container",
        {}
      );

      domObj.modalContainer.listModal.body.tableContainer = buildTimeOffTable(
        domObj.modalContainer.listModal.body.tableContainer,
        setupObj.timeOffObjs
      );

      domObj.build();

      domObj.modalContainer.listModal.show();

      requestTableDom = domObj.modalContainer.listModal.body.tableContainer;
    },

    showRequestTimeOffForm: function (setupObj) {
      timeOffObjs = setupObj.timeOffObjs;

      fiscalYearEnd = setupObj.fiscalYearEnd;
      fiscalYearStart = setupObj.fiscalYearStart;

      var remainingVacationDays = sb.data.util.calculateRemainingVacationDays(
        setupObj.staff,
        timeOffObjs,
        fiscalYearStart,
        fiscalYearEnd
      );

      modalDom = sb.dom.make(setupObj.domObj.selector);

      var formObj = {
        section1: {
          type: "section",
          name: "section",
          label: "",
          fields: {
            startDate: {
              type: "date",
              name: "startDate",
              label: "From:",
            },
            endDate: {
              type: "date",
              name: "endDate",
              label: "To:",
            },
            notes: {
              type: "textbox",
              name: "reason",
              label: "Request Reason",
              placeholder: "Type here...",
              rows: 6,
            },
            requestType: {
              type: "hidden",
              name: "requestType",
              value: "time off",
            },
          },
        },
      };

      modalDom.makeNode("timeOffModalContainer", "container", {});

      modalDom.timeOffModalContainer.makeNode("modal", "modal", {});

      modalDom.timeOffModalContainer.modal.body.makeNode(
        "formHeader",
        "headerText",
        { text: "<br />Request Time Off Form<br />", size: "small" }
      );
      modalDom.timeOffModalContainer.modal.body.makeNode(
        "form",
        "form",
        formObj
      );
      modalDom.timeOffModalContainer.modal.body.form.section
        .makeNode("button", "button", { text: "Submit", css: "btn-success" })
        .notify(
          "click",
          {
            type: "submit-time-off-request",
            data: {
              staff: setupObj.staff,
              form: modalDom.timeOffModalContainer.modal.body.form,
            },
          },
          sb.moduleId
        );

      modalDom.build();

      modalDom.timeOffModalContainer.modal.show();

      var disabledVacDays = [];

      _.each(setupObj.staff.availablility, function (vac) {
        disabledVacDays.push(+moment(vac.day, "dddd").format("d"));
      });

      $(
        modalDom.timeOffModalContainer.modal.body.form.section.startDate
          .selector
      )
        .datetimepicker({
          format: "YYYY-MM-DD",
          daysOfWeekDisabled: disabledVacDays,
          minDate: moment(),
        })
        .on("dp.change", function (changeObj) {
          $(
            modalDom.timeOffModalContainer.modal.body.form.section.endDate
              .selector
          ).val(changeObj.date.format("YYYY-MM-DD"));
        });

      $(
        modalDom.timeOffModalContainer.modal.body.form.section.endDate.selector
      ).datetimepicker({
        format: "YYYY-MM-DD",
        daysOfWeekDisabled: disabledVacDays,
        minDate: moment(),
      });
    },

    showRequestToWorkForm: function (setupObj) {
      timeOffObjs = setupObj.timeOffObjs;

      var remainingVacationDays = sb.data.util.calculateRemainingVacationDays(
        setupObj.staff,
        timeOffObjs,
        fiscalYearStart,
        fiscalYearEnd
      );

      modalDom = sb.dom.make(setupObj.domObj.selector);

      var formObj = {
        section1: {
          type: "section",
          name: "section",
          label: "",
          fields: {
            startDate: {
              type: "date",
              name: "startDate",
              label: "From:",
            },
            endDate: {
              type: "date",
              name: "endDate",
              label: "To:",
            },
            notes: {
              type: "textbox",
              name: "reason",
              label: "Request Reason",
              placeholder: "Type here...",
              rows: 6,
            },
            requestType: {
              type: "hidden",
              name: "requestType",
              value: "work request",
            },
          },
        },
      };

      modalDom.makeNode("timeOffModalContainer", "container", {});

      modalDom.timeOffModalContainer.makeNode("modal", "modal", {});

      modalDom.timeOffModalContainer.modal.body.makeNode(
        "formHeader",
        "headerText",
        { text: "<br />Request to work form<br />", size: "small" }
      );
      modalDom.timeOffModalContainer.modal.body.makeNode(
        "form",
        "form",
        formObj
      );
      modalDom.timeOffModalContainer.modal.body.form.section
        .makeNode("button", "button", { text: "Submit", css: "btn-success" })
        .notify(
          "click",
          {
            type: "submit-time-off-request",
            data: {
              staff: setupObj.staff,
              form: modalDom.timeOffModalContainer.modal.body.form,
            },
          },
          sb.moduleId
        );

      modalDom.build();

      modalDom.timeOffModalContainer.modal.show();

      var disabledDays = [];

      _.each(setupObj.staff.availability, function (avail) {
        disabledDays.push(+moment(avail.day, "dddd").format("d"));
      });

      $(
        modalDom.timeOffModalContainer.modal.body.form.section.startDate
          .selector
      )
        .datetimepicker({
          format: "YYYY-MM-DD",
          //daysOfWeekDisabled: disabledDays,
          minDate: moment(),
        })
        .on("dp.change", function (changeObj) {
          $(
            modalDom.timeOffModalContainer.modal.body.form.section.endDate
              .selector
          ).val(changeObj.date.format("YYYY-MM-DD"));
        });

      $(
        modalDom.timeOffModalContainer.modal.body.form.section.endDate.selector
      ).datetimepicker({
        format: "YYYY-MM-DD",
        //daysOfWeekDisabled: disabledDays,
        minDate: moment(),
      });
    },

    submitTimeOffRequest: function (setupObj) {
      if (formObj.completed == false) {
        sb.dom.alerts.alert(
          "Form is not complete",
          "Please fill out the entire form.",
          "warning"
        );
      } else {
        sb.dom.alerts.ask(
          {
            title: "Are you sure?",
            text: "",
          },
          function (resp) {
            if (resp) {
              swal.disableButtons();

              var formObj = setupObj.form.process();

              setupObj.form.patch();

              var timeOffObj = {
                startDate: formObj.fields.startDate.value,
                endDate: formObj.fields.endDate.value,
                reason: formObj.fields.reason.value,
                staffId: setupObj.staff.id,
                status: "submitted",
                statusChangedBy: 0,
                statusChangedOn: null,
                type: formObj.fields.requestType.value,
              };

              sb.data.db.obj.create(
                "time_off_request",
                timeOffObj,
                function (newObj) {
                  if (newObj.hasOwnProperty("id")) {
                    var emailObj = {
                      to: ["<EMAIL>"],
                      from: "Pagoda Dev",
                      subject: "Schedule Request",
                      mergevars: {
                        TITLE: "Schedule Request",
                        BODY:
                          setupObj.staff.fname +
                          " " +
                          setupObj.staff.lname +
                          " has requested " +
                          timeOffObj.type +
                          " from " +
                          timeOffObj.startDate +
                          " to " +
                          timeOffObj.endDate +
                          ".<br /><br />Reason: " +
                          timeOffObj.reason,
                        BUTTON: "Log in to review",
                        BUTTON_LINK:
                          "https://staff.infinityhospitality.net/app/#time-off-requests-admin",
                      },
                      emailtags: ["Schedule Request Email"],
                    };

                    sb.comm.sendEmail(emailObj, function (response) {
                      modalDom.timeOffModalContainer.modal.hide();

                      sb.dom.alerts.alert(
                        "Success!",
                        "Your time off request was submitted successfully!",
                        "success"
                      );

                      sb.notify({
                        type: "time-off-request-object-changed",
                        data: {
                          object: newObj,
                          type: "new",
                        },
                      });
                    });
                  } else {
                    sb.dom.alerts.alert(
                      "Our bad",
                      "There was a problem submitting your request.",
                      "error"
                    );
                  }
                }
              );
            }
          }
        );
      }
    },

    updateObject: function (updateObj) {
      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();

            updateObj.timeOffObj.status = updateObj.status;
            updateObj.timeOffObj.statusChangedBy = +sb.data.cookie.get("uid");
            updateObj.timeOffObj.statusChangedOn = moment().format();

            sb.data.db.obj.getById(
              "staff",
              updateObj.timeOffObj.staffId,
              function (staffObj) {
                sb.data.db.obj.getAll("holidays", function (holidayObjs) {
                  console.log(holidayObjs);
                  updateObj.timeOffObj.totalDays = 0;

                  updateObj.timeOffObj.totalDays =
                    sb.data.util.calculateTotalVacationDays(
                      staffObj,
                      [updateObj.timeOffObj],
                      fiscalYearStart,
                      fiscalYearEnd,
                      holidayObjs
                    );

                  sb.data.db.obj.update(
                    "time_off_request",
                    updateObj.timeOffObj,
                    function (response) {
                      _.each(timeOffObjs, function (timeOff, timeOffKey) {
                        if (timeOff.id == updateObj.timeOffObj.id) {
                          timeOffObjs[timeOffKey].status = updateObj.status;
                          timeOffObjs[timeOffKey].statusChangedBy =
                            +sb.data.cookie.get("uid");
                          timeOffObjs[timeOffKey].statusChangedOn =
                            moment().format();
                        }
                      });

                      var emailObj = {
                        to: staffObj.email,
                        subject: "Schedule request " + response.status,
                        mergevars: {
                          TITLE: "Schedule request " + response.status,
                          BODY:
                            "Your schedule request from " +
                            response.startDate +
                            " to " +
                            response.endDate +
                            " has been " +
                            response.status +
                            ".",
                          BUTTON: "View Online",
                          BUTTON_LINK:
                            "https://staff.infinityhospitality.net/app/#time-off-requests",
                        },
                        emailtags: ["Schedule Request Email"],
                      };

                      sb.comm.sendEmail(emailObj, function (response) {
                        sb.dom.alerts.alert("Success!", "", "success");

                        sb.notify({
                          type: "show-admin-request-table",
                          data: {
                            requests: timeOffObjs,
                          },
                        });
                      });
                    }
                  );
                });
              }
            );
          }
        }
      );
    },
  };
});
