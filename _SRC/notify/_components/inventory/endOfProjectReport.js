Factory.register('endOfProjectReport', function (sb) {

	// Data

	// Views

	function projectList (ui, state, draw) {
//
		sb.notify({
			type:'show-collection',
			data:{
				actions:{
					create:	false
					, view:	false
				},
// 				config: config,
				domObj:ui,
				fields:{
					name:{
						title:'Name',
						type:'title',
						uid: true
					}
					, start_date:{
						title:'Start Date',
						type: 'date',
						end: 'end_date'
					}
					, locations: {
						title: 'Location(s)'
						, type: 'locations'
					}
					, invoice_value: {
						title: 'Amount'
						, type: 'currency'
					}
				},
				fullView:{
					type:'hqTool',
					id:'projectTool'
				},
				objectType:'groups',
				onBoxview: true,
				selectedView:'table',
				subviews: {
					table: true,
					list: false,
					board: false,
					calendar: false,
					chart: false
				},
				menu: false,
				state:state,
				sortCol:'name',
				sortDir:'asc',
				parseData: function (data, onComplete) {

					var projIds = _.pluck(data.data, 'id');
					sb.data.db.obj.getWhere(
						'inventory_menu_pricing_breakdown'
						, {
							space: {
								type: 		'or'
								, values: 	projIds
							}
						}
						, function (pricingBreakdowns) {

							_.each(data.data, function (proj) {

								var pricingBreakdown = _.findWhere(
									pricingBreakdowns
									, {
										space: proj.id
									}
								);

								if (pricingBreakdown) {

									proj.link = sb.data.url.createPageURL(
										'object',
										{
											type: 	'inventory_menu_pricing_breakdown',
											id: 	pricingBreakdown.id,
											name: 	proj.name
										}
									);

								} else {

									proj.link = '';

								}

							});

							onComplete(data);

						}
						, {
							name: 		true
							, space: 	0
						}
					);

				},
				where:{
					group_type: 'Project'
					, status: 'done'
					, childObjs: {
						name: true
						, start_date: true
						, invoice_value: true
						, locations: true
					}
					, is_template: 0
				}
			}
		});

	}

	function viewPricingBreakdownReport (ui, state, draw) {

		var data = {
			accounts: 		[]
			, companies: 	[]
		};

		// Data

		function getStartupData (state, onComplete) {

			loader(ui, 'fetching event data...');

			// Get tde space, w/child objs
			sb.data.db.obj.getById('groups', state.pageObject.space.id, function (space) {

				loader(ui, 'fetching company data...');

				// Get vendors
				sb.data.db.obj.getWhere('companies', {is_vendor: 1, select: {name: true}}, function (vendors) {

					loader(ui, 'fetching chart of account data...');

					// Get chart of accts
					sb.data.db.obj.getAll(
						'chart_of_accounts'
						, function (coas) {

							sb.data.db.obj.getAll('chart_of_accounts_companies', function (cos) {

								loader(ui, 'fetching invoice data...');

								sb.data.db.obj.getWhere(
									'invoices'
									, {
										related_object: space.proposal.id
									}
									, function (payments) {

										state.pageObject.space = space;

										data.vendors = vendors;
										data.accounts = coas;
										data.companies = cos;
										data.payments = payments;

										onComplete(state);

									}
									, {
										name: 		true
										, due_date: true
										, memo: 	true
										, paid: 	true
										, balance: 	true
									}
								);

							}, {
								name: true
							}, undefined, 'any');

						}, {
							name: true
							, chart_of_accounts_company: {
								name: true
							}
							, account_id: true
						}
						, undefined
						, 'any'
					);

				});

			}, {
				name: true
				, object_uid: true
				, start_date: true
				, locations: {
					name: true
				}
				, managers: {
					fname: 		true
					, lname: 	true
				}
			});

		}

		// View

		function draw(ui, report, data) {

			var coaList = []; //all accounts

			_.each(report.breakdown.chartOfAccts, function(price, coaId) {

				coaList.push({
					id: parseInt(coaId)
					, price: price
				});

			});

			_.each(report.breakdown.vendorChartOfAccts, function(accountData, vendorId) {

				_.each(accountData, function (price, accountId) {

					coaList.push({
						id: _.findWhere(data.accounts, {id: parseInt(accountId)}).id
						, price: price
					});

				});

				// var account = _.findWhere(coaList, {id: parseInt(Object.keys(accountData)[0])});
				//
				// if (account) {
				//
				// 	account.price += accountData[Object.keys(accountData)[0]];
				//
				// } else {
				//
				// 	coaList.push({
				// 		id: parseInt(Object.keys(accountData)[0])
				// 		, price: accountData[Object.keys(accountData)[0]]
				// 	});
				//
				// }

			});

			_.each(coaList, function(coa, i) {

				var account = _.findWhere(data.accounts, {id: coa.id});

				if (account) {
					coa.account = account;
				}

			});

			function showEventInfo (ui, report, data) {

				// Title
				ui.makeNode('h', 'div', {css: 'ui header', tag: 'h2', text: 'Event Info'});

				// Setup table
				ui.makeNode('t', 'div', {
					tag: 'table'
					, css: 'ui compact very basic celled table'
				});
				ui.t.makeNode('b', 'div', {tag: 'tbody'});

				// Parse for disp
				var dateTxt = 	!report.space.start_date ? '<i class="text-muted">Not set</i>' : moment(report.space.start_date).format('MM/DD/YYYY');
				var locTxt = 		'<i class="text-muted">Not set</i>';
				var managerTxt = 	'<i class="text-muted">Not set</i>';

				if (!_.isEmpty(report.space.managers)) {

					managerTxt = '';
					_.each(report.space.managers, function (man, i) {

						if (i > 0) { managerTxt += ', '; }

						managerTxt += man.fname +' '+ man.lname;

					});

				}
				if (!_.isEmpty(report.space.locations)) {

					locTxt = '';
					_.each(report.space.locations, function (loc, i) {

						if (i > 0) { locTxt += ', '; }

						locTxt += loc.name;

					});

				}

				ui.t.b.makeNode('no', 'div', {
					tag: 'tr'
					, text: '<td>Number</td><td>#'+ report.space.object_uid +'</td>'
				});
				ui.t.b.makeNode('name', 'div', {
					tag: 'tr'
					, text: '<td>Name</td><td>'+ report.space.name +'</td>'
				});
				ui.t.b.makeNode('date', 'div', {
					tag: 'tr'
					, text: '<td>Date</td><td>'+ dateTxt +'</td>'
				});
				ui.t.b.makeNode('loc', 'div', {
					tag: 'tr'
					, text: '<td>Location(s)</td><td>'+ locTxt +'</td>'
				});
				ui.t.b.makeNode('manager', 'div', {
					tag: 'tr'
					, text: '<td>Manager(s)</td><td>'+ managerTxt +'</td>'
				});

			}

			function showChartOfAccts (ui, report, data) {

				var coaTot = 0;
				var groubedCoaList = _.groupBy(coaList, function(coa) {
					return coa.account.chart_of_accounts_company.id;
				});

				// Title
				ui.makeNode('h', 'div', {css: 'ui header', tag: 'h2', text: 'Chart of Accounts'});

				// Setup table
				ui.makeNode('t', 'div', {
					tag: 'table'
					, css: 'ui compact very basic celled table'
				});

				ui.t.makeNode('b', 'div', {tag: 'tbody'});

				_.each(groubedCoaList, function(coaCompanyList, coaCompanyId) {

					_.each(coaCompanyList, function(coa, i) {

						// Label the company
						ui.t.b.makeNode('companyName'+ coaCompanyId, 'div', {
							tag: 'tr'
							, text: '<td><strong>'+ coa.account.chart_of_accounts_company.name +'</strong></td><td style="background-color: lightgrey;"></td>'
						});

						ui.t.b.makeNode('coaName'+ coa.id, 'div', {
							tag: 'tr'
							, text: '<td><small>'+ coa.account.account_id +'</small> - '+ coa.account.name +'</td><td>$'+ (coa.price/100).formatMoney(2) +'</td>'
						});

						coaTot += coa.price;

					});

				});

				ui.t.b.makeNode('tot', 'div', {
					tag: 'tr'
					, text: '<td><strong>Total <i>(without inclusive taxes)</i></strong></td><td>$'+ (coaTot/100).formatMoney(2) +'</td>'
				});

			}

			function showVendorChartOfAccts (ui, report, data) {

				var vendorChartOfAcctsBreakdown = report.breakdown.vendorChartOfAccts;
				var accountsList = [];
				var coaTot = 0;

				_.each(vendorChartOfAcctsBreakdown, function(coaData, vendorId) {

					accountsList.push(coaData);

				});

				// Title
				ui.makeNode('h', 'div', {css: 'ui header', tag: 'h2', text: 'Vendors'});

				// Setup table
				ui.makeNode('t', 'div', {
					tag: 'table'
					, css: 'ui compact very basic celled table'
				});

				ui.t.makeNode('b', 'div', {tag: 'tbody'});

				_.each(vendorChartOfAcctsBreakdown, function(accountData, vendorId) {

					var accountObjId = parseInt(Object.keys(accountData)[0]);
					var accountVal = accountData[Object.keys(accountData)[0]];
					var account = _.findWhere(data.accounts, {id: accountObjId});

					if (account) {

						ui.t.b.makeNode('a'+ account.id, 'div', {
							tag: 'tr'
							, text: '<td>'+ account.account_id +'<h4> '+ account.name +'</h4></td><td>$'+ (accountVal/100).formatMoney(2) +'</td>'

						});

					}

					coaTot += accountVal;

				});

				ui.t.b.makeNode('tot', 'div', {
					tag: 'tr'
					, text: '<td><strong>Total</strong></td><td>$'+ (coaTot/100).formatMoney(2) +'</td>'
				});

				return coaTot;

			}

			function showPaymentSchedule (ui, report, data) {

				// Title
				ui.makeNode('h', 'div', {css: 'ui header', tag: 'h2', text: 'Payment Schedule'});

				var paidTot = 0;
				var invTot = 0;
				data.payments = _.sortBy(
					data.payments
					, function (pay) {

						return moment(pay.due_date).format('X');

					}
				);

				// Setup table
				ui.makeNode('t', 'div', {
					tag: 'table'
					, css: 'ui compact very basic celled table'
				});
				ui.t.makeNode('h', 'div', {tag: 'thead'}).makeNode('h', 'div', {
					tag: 'tr'
					, text: '<th>Schedule</th><th>Status</th><th>Paid</th><th>Invoiced</th>'
				});
				ui.t.makeNode('b', 'div', {tag: 'tbody'});

				_.each(data.payments, function (payment) {

					// Total for tde company
					ui.t.b.makeNode('p'+ payment.id, 'div', {
						tag: 	'tr'
						, text: '<td>('+ moment(payment.due_date).format('MM/DD/YYYY') +') '+ payment.name +'</td>'+
								'<td>$'+ (payment.paid/100).formatMoney(2) +'</td>'+
								'<td>$'+ (payment.amount/100).formatMoney(2) +'</td>'
					});

					paidTot += payment.paid;
					invTot += payment.amount;

				});

				// Total
				ui.t.b.makeNode('tot', 'div', {
					tag: 'tr'
					, text: '<td><strong>Total</strong></td>'+
							'<td>$'+ (paidTot/100).formatMoney(2) +'</td>'+
							'<td>$'+ (invTot/100).formatMoney(2) +'</td>'
				});

			}

			ui.empty();

			// Add action buttons at the top
			ui.makeNode('actions', 'div', {
				css: 'ui right aligned basic segment clearfix'
			});

			// Print button
			ui.actions.makeNode('printBtn', 'div', {
				css: 'ui blue labeled icon button',
				text: '<i class="print icon"></i>Print Report'
			});

			// Download button
			ui.actions.makeNode('downloadBtn', 'div', {
				css: 'ui grey labeled icon button',
				text: '<i class="file pdf icon"></i>Download Report'
			});

			// Add click handlers
			ui.actions.printBtn.notify('click', {
				type: 'endOfProjectReport-run',
				data: {
					run: function() {
						// Include both report and data in a merged object
						var reportWithData = _.extend({}, report, { data: data });
						var html = formatReportAsHtml(reportWithData);
						sb.data.makePDF(html, 'I');
					}
				}
			}, sb.moduleId);

			ui.actions.downloadBtn.notify('click', {
				type: 'endOfProjectReport-run',
				data: {
					run: function() {
						// Include both report and data in a merged object
						var reportWithData = _.extend({}, report, { data: data });
						var html = formatReportAsHtml(reportWithData);
						sb.data.makePDF(html, 'D');
					}
				}
			}, sb.moduleId);

			// Event info
			showEventInfo(ui.makeNode('info', 'div', {}), report, data);

			ui.makeNode('br1', 'lineBreak', {spaces: 2});

			// Chart of accts info
			showChartOfAccts(
				ui.makeNode('coas', 'div', {})
				, report
				, data
			);

			ui.makeNode('br2', 'lineBreak', {spaces: 2});

			// Vendor chart of accts info
			/*
showVendorChartOfAccts(
				ui.makeNode('v-coas', 'div', {})
				, report
				, data
			);
*/

			// Payment Schedule
			showPaymentSchedule(
				ui.makeNode('pay', 'div', {})
				, report
				, data
			);

			ui.patch();

		}

		getStartupData(state, function (state) {

			draw(ui, state.pageObject, data);

		});

	}

	function loader(ui, text) {

		ui.empty();

		ui.makeNode('loadingSeg', 'div', {
			css: 'ui basic segment'
			, style: 'height: 100px;'
			, text: '<p></p>'
		});

		ui.loadingSeg.makeNode('dimmer', 'div', {
			css: 'ui active inverted dimmer'
		});

		ui.loadingSeg.dimmer.makeNode('textLoader', 'div', {
			css: 'ui text loader'
			, text: text
		});

		ui.patch();

	}

	function formatReportAsHtml(report) {
		var html = '';

		// Title
		html += '<h1 style="font-family: Arial, sans-serif; font-size: 20px; text-align: center; margin-bottom: 20px;">End of Project Report</h1>';

		// Event Info
		html += '<h2 style="font-family: Arial, sans-serif; font-size: 16px; margin-top: 20px; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">Event Info</h2>';
		html += '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; font-family: Arial, sans-serif;">';
		html += '<tbody>';

		// Parse for display
		var dateTxt = !report.space.start_date ? 'Not set' : moment(report.space.start_date).format('MM/DD/YYYY');
		var locTxt = 'Not set';
		var managerTxt = 'Not set';

		if (!_.isEmpty(report.space.managers)) {
			managerTxt = '';
			_.each(report.space.managers, function (man, i) {
				if (i > 0) { managerTxt += ', '; }
				managerTxt += man.fname +' '+ man.lname;
			});
		}

		if (!_.isEmpty(report.space.locations)) {
			locTxt = '';
			_.each(report.space.locations, function (loc, i) {
				if (i > 0) { locTxt += ', '; }
				locTxt += loc.name;
			});
		}

		// Original layout for Event Info section
		html += '<tr><td style="width: 25%; padding: 8px;">Number</td><td style="width: 75%; padding: 8px;">#'+ report.space.object_uid +'</td></tr>';
		html += '<tr><td style="width: 25%; padding: 8px;">Name</td><td style="width: 75%; padding: 8px;">'+ report.space.name +'</td></tr>';
		html += '<tr><td style="width: 25%; padding: 8px;">Date</td><td style="width: 75%; padding: 8px;">'+ dateTxt +'</td></tr>';
		html += '<tr><td style="width: 25%; padding: 8px;">Location(s)</td><td style="width: 75%; padding: 8px;">'+ locTxt +'</td></tr>';
		html += '<tr><td style="width: 25%; padding: 8px;">Manager(s)</td><td style="width: 75%; padding: 8px;">'+ managerTxt +'</td></tr>';
		html += '</tbody></table>';

		// Chart of Accounts
		if (report.breakdown && (report.breakdown.chartOfAccts || report.breakdown.vendorChartOfAccts)) {
			html += '<h2 style="font-family: Arial, sans-serif; font-size: 16px; margin-top: 20px; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">Chart of Accounts</h2>';
			html += '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; font-family: Arial, sans-serif;">';
			html += '<thead><tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Account</th><th style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Amount</th></tr></thead>';
			html += '<tbody>';

			var coaTot = 0;

			// Add chart of accounts entries if available
			if (report.breakdown.chartOfAccts) {
				_.each(report.breakdown.chartOfAccts, function(price, coaId) {
					// Get account details for better reporting
					var account = _.findWhere(report.data.accounts, {id: parseInt(coaId)});
					var accountLabel = "Account #" + coaId;
					var accountCompany = "";

					// Add account details if available
					if (account) {
						if (account.account_id && account.name) {
							accountLabel = account.account_id + " - " + account.name;
						} else if (account.name) {
							accountLabel = account.name;
						} else if (account.account_id) {
							accountLabel = "Account #" + account.account_id;
						}

						// Add company info if available
						if (account.chart_of_accounts_company && account.chart_of_accounts_company.name) {
							accountCompany = " (" + account.chart_of_accounts_company.name + ")";
						}
					}

					// Add the account row first
					html += '<tr><td style="text-align: left; padding: 8px;">'+ accountLabel + accountCompany +'</td><td style="text-align: right; padding: 8px;">$'+ (price/100).formatMoney(2) +'</td></tr>';

					// If category exists, add it as a full row
					if (account && account.category) {
						html += '<tr><td colspan="2" style="text-align: left; padding: 8px; padding-left: 20px;"><small>Category: ' + account.category + '</small></td></tr>';
					}

					// If account type exists, add it as a full row
					if (account && account.account_type) {
						html += '<tr><td colspan="2" style="text-align: left; padding: 8px; padding-left: 20px;"><small>Type: ' + account.account_type + '</small></td></tr>';
					}
					coaTot += price;
				});
			}

			// Add vendor chart of accounts if available
			if (report.breakdown.vendorChartOfAccts) {
				_.each(report.breakdown.vendorChartOfAccts, function(accountData, vendorId) {
					// Get the vendor name and contact information
					var vendorObj = _.findWhere(report.data.vendors, {id: parseInt(vendorId)});
					var vendorName = vendorObj ? vendorObj.name : "Vendor " + vendorId;
					var vendorInfo = "";

					// Add vendor contact info if available
					if (vendorObj && vendorObj.contact_info) {
						if (vendorObj.contact_info.email) {
							vendorInfo += " (Email: " + vendorObj.contact_info.email + ")";
						}
						if (vendorObj.contact_info.phone) {
							vendorInfo += " (Phone: " + vendorObj.contact_info.phone + ")";
						}
					}

					// Display vendor section
					html += '<tr><td colspan="2" style="text-align: left; padding: 8px; background-color: #f8f8f8; font-weight: bold;">'+ vendorName +
						(vendorInfo ? '<br><small>' + vendorInfo + '</small>' : '') + '</td></tr>';

					// Process vendor accounts
					_.each(accountData, function(price, accountId) {
						// Get account details
						var account = _.findWhere(report.data.accounts, {id: parseInt(accountId)});
						var accountLabel = "Account #" + accountId;
						var accountCompany = "";

						// Add account details if available
						if (account) {
							if (account.account_id && account.name) {
								accountLabel = account.account_id + " - " + account.name;
							} else if (account.name) {
								accountLabel = account.name;
							} else if (account.account_id) {
								accountLabel = "Account #" + account.account_id;
							}

							// Add company info if available
							if (account.chart_of_accounts_company && account.chart_of_accounts_company.name) {
								accountCompany = " (" + account.chart_of_accounts_company.name + ")";
							}
						}

						// Add the account row first
						html += '<tr><td style="text-align: left; padding: 8px; padding-left: 20px;">'+ accountLabel + accountCompany +'</td><td style="text-align: right; padding: 8px;">$'+ (price/100).formatMoney(2) +'</td></tr>';

						// If category exists, add it as a full row
						if (account && account.category) {
							html += '<tr><td colspan="2" style="text-align: left; padding: 8px; padding-left: 40px;"><small>Category: ' + account.category + '</small></td></tr>';
						}

						// If account type exists, add it as a full row
						if (account && account.account_type) {
							html += '<tr><td colspan="2" style="text-align: left; padding: 8px; padding-left: 40px;"><small>Type: ' + account.account_type + '</small></td></tr>';
						}
						coaTot += price;
					});
				});
			}

			html += '<tr><td style="text-align: left; padding: 8px; border-top: 2px solid #ddd; font-weight: bold;">Total (without inclusive taxes)</td><td style="text-align: right; padding: 8px; border-top: 2px solid #ddd; font-weight: bold;">$'+ (coaTot/100).formatMoney(2) +'</td></tr>';
			html += '</tbody></table>';
		}

		// Payment Schedule
		// Always show Payment Schedule section
		html += '<h2 style="font-family: Arial, sans-serif; font-size: 16px; margin-top: 20px; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">Payment Schedule</h2>';
		html += '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; font-family: Arial, sans-serif;">';
		html += '<thead><tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Schedule</th><th style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Paid</th><th style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Invoiced</th></tr></thead>';
		html += '<tbody>';

		var paidTot = 0;
		var invTot = 0;

		// Make sure to include payments if available
		var paymentsList = [];

		// For the PDF generation from the buttons, payments may be in data property
		if (report.data && report.data.payments && report.data.payments.length > 0) {
			paymentsList = _.sortBy(
				report.data.payments,
				function (pay) {
					return moment(pay.due_date).format('X');
				}
			);
		}
		// Existing check for payments directly on the report object
		else if (report.payments && report.payments.length > 0) {
			paymentsList = _.sortBy(
				report.payments,
				function (pay) {
					return moment(pay.due_date).format('X');
				}
			);
		}

		_.each(paymentsList, function (payment) {
			// Add memo information to the payment description if available
			var description = payment.name || '';
			if (payment.memo) {
				description += ' - ' + payment.memo;
			}

			// No status column as requested

			html += '<tr>'+
				'<td style="text-align: left; padding: 8px;">('+ moment(payment.due_date).format('MM/DD/YYYY') +') '+ description +'</td>'+
				'<td style="text-align: right; padding: 8px;">$'+ (payment.paid/100).formatMoney(2) +'</td>'+
				'<td style="text-align: right; padding: 8px;">$'+ (payment.amount/100).formatMoney(2) +'</td>'+
				'</tr>';

			paidTot += payment.paid;
			invTot += payment.amount;
		});

		html += '<tr>'+
			'<td style="text-align: left; padding: 8px; border-top: 2px solid #ddd; font-weight: bold;">Total</td>'+
			'<td style="text-align: right; padding: 8px; border-top: 2px solid #ddd; font-weight: bold;">$'+ (paidTot/100).formatMoney(2) +'</td>'+
			'<td style="text-align: right; padding: 8px; border-top: 2px solid #ddd; font-weight: bold;">$'+ (invTot/100).formatMoney(2) +'</td></tr>';
		html += '</tbody></table>';

		return html;
	}

	return {

		// Simple run function that passes data to the run function provided in the event
		run: function (data) {
			data.run(data);
		},

		init: function () {

			// Register event listeners
			var listeners = {
				'endOfProjectReport-run': this.run
			};

			sb.listen(listeners);

			sb.notify({
				type:'register-application',
				data:{
					navigationItem: {
						id: 	'endOfProjectReport',
						title: 	'End of Project Report',
						icon: 	'<i class="fa fa-table"></i>',
						views:[
							// Overview of all completed projects
							{
								id: 		'endOfProjectReport'
								, default: 	true
								, type: 	'custom'
								, title: 	'End of Project Report'
								, icon: 	'<i class="fa fa-exclamation"></i>'
								, dom: 		projectList
							}
							// End of proj report, w/pricing breakdown/chart of accts
							, {
								id: 		'inventory_menu_pricing_breakdown-obj'
								, type: 	'object-view'
								, title: 	'Pricing Breakdown'
								, icon: 	'box'
								, dom: 		viewPricingBreakdownReport
							}
						]
					}
				}
			});

			sb.notify({
				type: 'register-report',
				data: {
					id:				'endOfProjectReport'
					, icon:			'table'
					, header: 		'End of Project Report'
					, subheader: 	'Chart of accounts, vendors, payment schedule breakdown for a project.'
					, type: 		'Accounting'
				}
			});

		}

	};

});
