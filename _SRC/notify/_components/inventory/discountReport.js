Factory.register('discount-report', function (sb) {
	
	var grouped = {};

	function producePDF (range) {
		
		var where = {
			event_date: {
				type: 'between'
			}
			, is_template: 0
			, childObjs: {
				breakdown: 	true
				, client: 	true
				, event_date: 	true
				, space: {
					name: 			true
					, start_date: 	true
					, head_count: 	true
				}
				, total: 		true
			}
		};
		
		if (
			_.isObject(range)
			&& range.start
		) {
			
			where.event_date.start = range.start.clone().startOf().format('X');
			where.event_date.end = range.end.clone().add(1, 'day').startOf().format('X');
			
		} else {
			
			switch (range) {
	
				///default case options
				case 'today':
					where.event_date.type = 'before';
					where.event_date.date = moment().endOf('day').format('X');
					break;
	
				case 'this_week':
					where.event_date.type = 'before';
					where.event_date.date = moment().endOf('week').format('X');
					break;
	
				case 'past_3_months':
					where.event_date.start = moment().startOf('month').subtract(3, 'months').format('X');
					where.event_date.end = moment().endOf('month').format('X');
					break;
	
				case 'next_3_months':
					where.event_date.type = 'before';
					where.event_date.date = moment().endOf('month').add(3, 'months').format('X');
					break;
	
				///report case options
				case 'last_30':
					where.event_date.start = moment().subtract(30, 'days').format('X');
					where.event_date.end = moment().format('X');
					break;
	
				case 'this_month':
					where.event_date.start = moment().startOf('month').format('X');
					where.event_date.end = moment().endOf('month').format('X');
					break;
	
				case 'this_quarter':
					where.event_date.start = moment().startOf('quarter').format('X');
					where.event_date.end = moment().endOf('quarter').format('X');
					break;
	
				case 'this_year':
					where.event_date.start = moment().startOf('year').format('X');
					where.event_date.end = moment().endOf('year').format('X');
					break;
	
				case 'last_month':
					where.event_date.start = moment().subtract(1, 'month').startOf('month').format('X');
					where.event_date.end = moment().subtract(1, 'month').endOf('month').format('X');
					break;
	
				case 'last_quarter':
					where.event_date.start = moment().subtract(1, 'quarter').startOf('quarter').format('X');
					where.event_date.end = moment().subtract(1, 'quarter').endOf('quarter').format('X');
					break;
	
				case 'last_year':
					where.event_date.start = moment().subtract(1, 'year').startOf('year').format('X');
					where.event_date.end = moment().subtract(1, 'year').endOf('year').format('X');
					break;
	
				default:
					delete where.event_date;
					break;
	
			}
			
		}
		
		sb.data.db.obj.getWhere(
			'inventory_menu_pricing_breakdown'
			, where
			, function (records) {
				
				records = _.sortBy(records, function (r) {
					
					return moment(r).format('X');
					
				});
				
				var html = '';
				var tots = {
					categories: {}
					, surcharges: {}
					, taxes: {}
					, subtotal: 0
					, total: 0
					, head_count: 0
				};
				
				// Display date range
				if (where.event_date) {
					
					switch (where.event_date.type) {
						
						case 'before':
							html += '<h3>Before '+ moment.unix(where.event_date.date).format('M/D/YYYY') +'</h3>';
							break;
							
						case 'between':
							html += '<h3>From '+ moment.unix(where.event_date.start).format('M/D/YYYY') +' to '+ moment.unix(where.event_date.end).subtract(1, 'day').format('M/D/YYYY') +'</h3>';
							break;
						
					}
					
				}
				
				
				html += '<table>';
				html += '<thead><tr>'+
							'<th>Client</th>'+
							'<th>Project</th>'+
							'<th>Head Count</th>'+
							'<th>Event Date</th>';
				
				if (grouped.inventory_billable_categories) {
					
					_.each(grouped.inventory_billable_categories, function (cat) {
						
						tots.categories[cat.id.toString()] = 0;
						html += '<th>'+ cat.name +'</th>';
						
					});
					
				}
				
				html += '<th>Subtotal</th>';
				
				if (grouped.surcharges) {
					
					_.each(grouped.surcharges, function (cat) {
						
						tots.surcharges[cat.id.toString()] = 0;
						html += '<th>'+ cat.name +'</th>';
						
					});
					
				}
				
				if (grouped.tax_rates) {
					
					_.each(grouped.tax_rates, function (cat) {
						
						tots.taxes[cat.id.toString()] = 0;
						html += '<th>'+ cat.name +'</th>';
						
					});
					
				}
				
				html += 	'<th>Total</th>'+
						'</tr></thead>';
				
				_.each(records, function (record) {
					
					var dateTxt = '<i class="text-muted">Not set</i>';
					var headCount = '0';
					if (moment(record.event_date).isValid()) {
						dateTxt = moment(record.event_date).format('M/D/YYYY');
					}
					if (record && record.space && record.space.head_count) {
						headCount = record.space.head_count.toString();
						tots.head_count += record.space.head_count;
					}
					var clientName = '<i class="text-muted">Not set</i>';
					if (record.client) {
						clientName = record.client.name;
					}
					var spaceName = '<i class="text-muted">Not set</i>';
					if (record.space) {
						spaceName = record.space.name;
					}
					
					html += '<tr>'+
								'<td>'+ clientName +'</td>'+
								'<td>'+ spaceName +'</td>'+
								'<td>'+ headCount +'</td>'+
								'<td>'+ dateTxt +'</td>';
					
					if (grouped.inventory_billable_categories) {
						
						_.each(grouped.inventory_billable_categories, function (cat) {
							
							var cost = 0;
							if (record.breakdown) {
								
								if (
									record.breakdown.categories
									&& record.breakdown.categories[cat.id.toString()]
								) {
									cost += parseInt(record.breakdown.categories[cat.id.toString()]);
								}
								
								if (
									record.breakdown.labor
									&& record.breakdown.labor[cat.id.toString()]
								) {
									cost += parseInt(record.breakdown.labor[cat.id.toString()]);
								}
								
								tots.categories[cat.id.toString()] += cost;
								html += '<td>$ '+ (cost/100).formatMoney(2) +'</td>';
								
							} else {
								
								html += '<td>$ 0.00</td>';
								
							}
							
						});
						
					}
					
					tots.subtotal += parseInt(record.breakdown.subTotal);
					html += '<td>$ '+ (record.breakdown.subTotal/100).formatMoney(2) +'</td>';
					
					if (grouped.surcharges) {
						
						_.each(grouped.surcharges, function (cat) {
							
							if (
								record.breakdown
								&& record.breakdown.surcharges
								&& record.breakdown.surcharges[cat.id.toString()]
							) {
								
								tots.surcharges[cat.id.toString()] += parseInt(record.breakdown.surcharges[cat.id.toString()]);
								html += '<td>$ '+ (record.breakdown.surcharges[cat.id.toString()]/100).formatMoney(2) +'</td>';
								
							} else {
								html += '<td>$ 0.00</td>';
							}
							
						});
						
					}
					
					if (grouped.tax_rates) {
						
						_.each(grouped.tax_rates, function (cat) {
							
							if (
								record.breakdown
								&& record.breakdown.taxes
								&& record.breakdown.taxes[cat.id.toString()]
							) {
								
								tots.taxes[cat.id.toString()] += parseInt(record.breakdown.taxes[cat.id.toString()]);
								html += '<td>$ '+ (record.breakdown.taxes[cat.id.toString()]/100).formatMoney(2) +'</td>';
								
							} else {
								html += '<td>$ 0.00</td>';
							}
							
						});
						
					}
					
					tots.total += parseInt(record.breakdown.total);
					html += 	'<td>$ '+ (record.total/100).formatMoney(2) +'</td>'+
							'</tr>';
					
				});
				
				// Add totals
				html += '<tr>'+
							'<th>Total Count '+ records.length +'</th>'+
							'<th></th>'+
							'<th>'+ tots.head_count +'</th>'+
							'<th></th>';
				
				if (grouped.inventory_billable_categories) {
					
					_.each(grouped.inventory_billable_categories, function (cat) {
						
						html += '<th>$ '+ (tots.categories[cat.id.toString()]/100).formatMoney(2) +'</th>';
						
					});
					
				}
				
				html += '<th>$ '+ (tots.subtotal/100).formatMoney(2) +'</th>';
				
				if (grouped.surcharges) {
					
					_.each(grouped.surcharges, function (cat) {
						
						html += '<th>$ '+ (tots.surcharges[cat.id.toString()]/100).formatMoney(2) +'</th>';
						
					});
					
				}
				
				if (grouped.tax_rates) {
					
					_.each(grouped.tax_rates, function (cat) {
						
						html += '<th>$ '+ (tots.taxes[cat.id.toString()]/100).formatMoney(2) +'</th>';
						
					});
					
				}
				
				html += 	'<th>$ '+ (tots.total/100).formatMoney(2) +'</th>'+
						'</tr>';
				
				
				html += '</table>';
				
				sb.data.makePDF(html, true, {
					orientation: 'landscape'
				});
				
			}
		);
		
	}
	
	function getFactorText(factor, type_name, discountValue) {
		if (type_name === 'Percent off') {
			
			return (factor/10000) + '% off ($' + (discountValue/100).formatMoney() + ')';
			
		} else if (type_name === 'Amount off' || type_name === 'Replacement amount') {
			
			return '$' + (factor/100) + ' off';
			
		} 
	}

	function display_discountsTable(ui, obj, discounts, refs){

		var discountsRef = discounts
		.filter(discount => refs.filter(ref => ref.discountId === discount.id)?.length)
		?.map(discount => {
			let ref = refs.filter(ref => ref.discountId === discount.id);
			let factorText = getFactorText(discount.factor, discount.type_name, ref[0]?.discountValue);
			let categoriesText = discount.categories?.map(cat => cat.name)?.join(',');

			return {
				id: discount.id,
				name: discount.name,
				memo: discount.memo,
				type: discount.type_name,
				value: factorText,
				categories: categoriesText
			}
		});
		
		ui.makeNode('wrapper', 'div', {});

		ui.wrapper.makeNode('modal', 'modal', {
			onShow: function() {

				var discountsSetup = {
					domObj: ui.wrapper.modal.body
					, fields: {
						name: {
							title: 'Discount Name',
							type: 'text'
						}
						, memo: {
							title: 'Memo',
							type: 'text'
						}
						, type: {
							title: 'Type',
							type: 'text'
						}
						, value: {
							title: 'Value',
							type: 'text'
						}
						, categories: {
							title: 'Categories',
							type: 'text'
						}
					}
					, objectType: 'discounts'
					, actions: {}
					, selectedView: 'table'
					, menu: {
						subviews: false
					}
					, subviews: {
						table: {
							hideSelectionBoxes:true
						}
					}
					, data: {
						obj: discountsRef
					}
				};

				sb.notify({
					type:'show-collection',
					data: discountsSetup
				});
			}
		});
				
		ui.patch();
		ui.wrapper.modal.show();

	};
	
	function viewReport (ui, state, draw) {
		
		sb.notify({
            type: 'show-collection',
            data: {
                domObj: 	ui,
                state: 		state,
                objectType: 'groups',
                singleView: false,
                actions: {
                    downloadCSV: true
                    , create: false
					, copy: false
					, archive: false
                },
                fields: {
                    name: {
	                    title: 'Name'
	                    , type: 'title'
                    }
                    , locations: {
	                    title: 'Locations'
	                    , type: 'locations'
                    }
                    , type: {
						title: 'Type'
						, type: 'type'
					}
                    , start_date: {
	                    title: 'Event Date'
	                    , type: 'date'
	                    , rangeOver:true
                    }
                    , value: {
						title: 'Total Value (before discounts)',
						type: 'usd',
						view: function(ui, obj) {
							if (obj.value > 0) {
								ui.makeNode('value', 'div', {
									text: '$' + (obj.value/100).formatMoney()
								});
								
								ui.patch();
							}
						}
					}
                },
                metrics: {
					sum: {
						title:		'Total'
						, fields:	{
							invoice_value: function(ui, calculated, rawValue, data) {
								
								var totalAmount = 0;
										
								if(_.isArray(data)){
								
									_.each(data, function(inv){										
									
										totalAmount += inv.invoice_value;
										
									});
									
								}else{
									
									totalAmount = data;
									
								}	

								
								if(!_.isUndefined(ui) && !_.isNull(ui)){
									
									ui.empty();
									ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>$'+ (totalAmount/100).formatMoney() +'</b>'});
									ui.patch();	
																				
								}	
									
								
								return '$'+ (totalAmount/100).formatMoney();
								
							}
							, value: function(ui, calculated, rawValue, data) {
								
								var totalAmount = 0;
										
								if(_.isArray(data)){
								
									_.each(data, function(inv){										
									
										totalAmount += inv.total_value;
										
									});
									
								}else{
									
									totalAmount = data;
									
								}	

								
								if(!_.isUndefined(ui) && !_.isNull(ui)){
									
									ui.empty();
									ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>$'+ (totalAmount/100).formatMoney() +'</b>'});
									ui.patch();	
																				
								}	
									
								
								return '$'+ (totalAmount/100).formatMoney();
								
							}
						}
					}
				},
                parseData: function (data, callback, query, subview, range, types, options) {

					var projects = data.data;
					var projectIds = projects.map((project) => project.id);

					sb.data.db.obj.getWhere('inventory_menu_pricing_breakdown', {
							space: {
								type: 'or'
								, values: projectIds
							}
							, parent: 0
							, childObjs: 1
						}
						, (breakdowns) => {

							var discountIds = [];
							var discountRefs = [];
							var projectsWithDiscounts = [];
							
							breakdowns = _.filter(breakdowns, function(o) {
								return o.space !== false;
							});

							_.each(breakdowns, function(breakdown) {
								
								if (
									breakdown.breakdown.hasOwnProperty('discounts')
									&& !_.isEmpty(breakdown.breakdown.discounts)
								) {

									projectsWithDiscounts.push(breakdown.space);
								}

								_.each(breakdown.breakdown.discounts, function(discountValue, discountId) {
									
									discountIds.push(parseInt(discountId));
									
									discountRefs.push({
										discountId: parseInt(discountId)
										, discountValue: discountValue
										, space: breakdown.space.id
									});
									
								});

								discountIds = _.uniq(discountIds); 
								
							});

							var discountsInfo = [];

							sb.data.db.obj.getWhere("discounts", {
								id: {
									type: 'or'
									, values: discountIds
								}
								, childObjs: 1
							}, function(discounts) {

								var discountFields = {};
								
								_.each(discounts, function(discount, i) {
									
									discountsInfo.push(discount);
									
									options.fields.value.view = function(ui, obj) {
										
										obj.total_value = obj.invoice_value;
											
										_.each(discountRefs, function(discountRef, i) {
									
											if (
												(obj.id === discountRef.space)
											) {
												
												obj.total_value += Math.abs(discountRef.discountValue);
												
												ui.makeNode('value', 'div', {
													text: '$' + (obj.total_value/100).formatMoney()
												});	

												obj.diff = obj.total_value - obj.invoice_value;	
												
											} 
											
										});
										
										ui.patch();
										
									};
									
								});
								
								options.fields.discounts = {

									title: 'Discount Info',

									type: 'plain-text',

									view: function(ui, obj) {

										var thisDiscountsRefs = discountRefs.filter(ref => ref.space === obj.id); 

										var thisDiscounts = discountsInfo.filter(discount => thisDiscountsRefs.find(ref => ref.discountId === discount.id));
										
										if (obj.diff !== undefined && !_.isEmpty(thisDiscounts)) {

											ui.makeNode('value', 'div', {

												text: '$' + (obj.diff/100).formatMoney()

												, css:'ui fluid teal button'

												, tag:'button'

											}).notify('click', {

												type:'inventory-comp-run',

												data:{

													run:function(data){

														display_discountsTable(data.ui, data.obj, thisDiscounts, thisDiscountsRefs);

													}

													, ui: ui

													, obj: obj

												}

											});	
											
										} else {
											
											ui.makeNode('value', 'div', {
												text: ''
											});
											
										}
										
										ui.patch();
										
									}
								};

								options.fields.invoice_value = {
									title: 'Total Value (after discounts)',
									type: 'usd',
									view: function(ui, obj) {
										
										ui.makeNode('value', 'div', {
											text: '$' + (obj.invoice_value/100).formatMoney()
										});
										
										ui.patch();
										
									}
								};
								
								options.fields.percentOff = {
									title: 'Total percent off total value',
									type: 'plain-text',
									view: function(ui, obj) {

										if (obj.diff !== undefined) {
											
											var totalValue = obj.total_value/100;
											var diff = obj.diff/100;
											
											ui.makeNode('value', 'div', {
												text: (diff/totalValue) * 100 + '%'
											});	
											
										} else {
											
											ui.makeNode('value', 'div', {
												text: '0%'
											});
											
										}
										
										ui.patch();
										
									}
								};

								data.data = projectsWithDiscounts;

								callback(data); 
							
							});
							
						}, {
							space: true
							, breakdown: true
						}
					);
	                
                },
                groupings: {
					type:'Type'
				},
                rangeOver: 'start_date',
                selectedView: 'table',
                menu: {
	                subviews: false
                },
                where: {
	                group_type: 'Project'
	                , childObjs: {
		                name: true,
						group_type: true,
						type:{
							name: true,
							states: true
						},
						start_date: true,
						invoice_value: true,
						locations: true,
						proposal: true
	                }
				}
            }
        });
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type:'register-application',
				data:{
					navigationItem: {
						id: 	'discountReport',
						title: 	'Discount Report',
						icon: 	'<i class="fa fa-table"></i>',
						views:[
							{
								id: 		'discountReport'
								, default: 	true
								, type: 	'custom'
								, title: 	'Discount Report'
								, icon: 	'<i class="fa fa-exclamation"></i>'
								, dom: 		viewReport
							}
						]
					}
				}
			});
			
			sb.notify({
				type: 'register-report',
				data: {
					id:				'discountReport'
					, icon:			'table'
					, header: 		'Discount'
					, subheader: 	'Discounts across all projects'
					, type: 		'Accounting'
				}
			});
			
		}
		
	}
	
});