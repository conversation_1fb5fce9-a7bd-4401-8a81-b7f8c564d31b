Factory.register('pricing-breakout-report', function (sb) {
	
	var grouped = {};
	
	function producePDF (range) {
		
		var where = {
			event_date: {
				type: 'between'
			}
			, is_template: 0
			, childObjs: {
				breakdown: 	true
				, client: 	true
				, event_date: 	true
				, space: {
					name: 			true
					, start_date: 	true
					, head_count: 	true
				}
				, total: 		true
			}
		};
		
		if (
			_.isObject(range)
			&& range.start
		) {
			
			where.event_date.start = range.start.clone().startOf().format('X');
			where.event_date.end = range.end.clone().add(1, 'day').startOf().format('X');
			
		} else {
			
			switch (range) {
	
				///default case options
				case 'today':
					where.event_date.type = 'before';
					where.event_date.date = moment().endOf('day').format('X');
					break;
	
				case 'this_week':
					where.event_date.type = 'before';
					where.event_date.date = moment().endOf('week').format('X');
					break;
	
				case 'past_3_months':
					where.event_date.start = moment().startOf('month').subtract(3, 'months').format('X');
					where.event_date.end = moment().endOf('month').format('X');
					break;
	
				case 'next_3_months':
					where.event_date.type = 'before';
					where.event_date.date = moment().endOf('month').add(3, 'months').format('X');
					break;
	
				///report case options
				case 'last_30':
					where.event_date.start = moment().subtract(30, 'days').format('X');
					where.event_date.end = moment().format('X');
					break;
	
				case 'this_month':
					where.event_date.start = moment().startOf('month').format('X');
					where.event_date.end = moment().endOf('month').format('X');
					break;
	
				case 'this_quarter':
					where.event_date.start = moment().startOf('quarter').format('X');
					where.event_date.end = moment().endOf('quarter').format('X');
					break;
	
				case 'this_year':
					where.event_date.start = moment().startOf('year').format('X');
					where.event_date.end = moment().endOf('year').format('X');
					break;
	
				case 'last_month':
					where.event_date.start = moment().subtract(1, 'month').startOf('month').format('X');
					where.event_date.end = moment().subtract(1, 'month').endOf('month').format('X');
					break;
	
				case 'last_quarter':
					where.event_date.start = moment().subtract(1, 'quarter').startOf('quarter').format('X');
					where.event_date.end = moment().subtract(1, 'quarter').endOf('quarter').format('X');
					break;
	
				case 'last_year':
					where.event_date.start = moment().subtract(1, 'year').startOf('year').format('X');
					where.event_date.end = moment().subtract(1, 'year').endOf('year').format('X');
					break;
	
				default:
					delete where.event_date;
					break;
	
			}
			
		}
		
		sb.data.db.obj.getWhere(
			'inventory_menu_pricing_breakdown'
			, where
			, function (records) {
				
				records = _.sortBy(records, function (r) {
					
					return moment(r).format('X');
					
				});
				
				var html = '';
				var tots = {
					categories: {}
					, surcharges: {}
					, taxes: {}
					, subtotal: 0
					, total: 0
					, head_count: 0
				};
				
				// Display date range
				if (where.event_date) {
					
					switch (where.event_date.type) {
						
						case 'before':
							html += '<h3>Before '+ moment.unix(where.event_date.date).format('M/D/YYYY') +'</h3>';
							break;
							
						case 'between':
							html += '<h3>From '+ moment.unix(where.event_date.start).format('M/D/YYYY') +' to '+ moment.unix(where.event_date.end).subtract(1, 'day').format('M/D/YYYY') +'</h3>';
							break;
						
					}
					
				}
				
				
				html += '<table>';
				html += '<thead><tr>'+
							'<th>Client</th>'+
							'<th>Project</th>'+
							'<th>Head Count</th>'+
							'<th>Event Date</th>';
				
				if (grouped.inventory_billable_categories) {
					
					_.each(grouped.inventory_billable_categories, function (cat) {
						
						tots.categories[cat.id.toString()] = 0;
						html += '<th>'+ cat.name +'</th>';
						
					});
					
				}
				
				html += '<th>Subtotal</th>';
				
				if (grouped.surcharges) {
					
					_.each(grouped.surcharges, function (cat) {
						
						tots.surcharges[cat.id.toString()] = 0;
						html += '<th>'+ cat.name +'</th>';
						
					});
					
				}
				
				if (grouped.tax_rates) {
					
					_.each(grouped.tax_rates, function (cat) {
						
						tots.taxes[cat.id.toString()] = 0;
						html += '<th>'+ cat.name +'</th>';
						
					});
					
				}
				
				html += 	'<th>Total</th>'+
						'</tr></thead>';
				
				_.each(records, function (record) {
					
					var dateTxt = '<i class="text-muted">Not set</i>';
					var headCount = '0';
					if (moment(record.event_date).isValid()) {
						dateTxt = moment(record.event_date).format('M/D/YYYY');
					}
					if (record && record.space && record.space.head_count) {
						headCount = record.space.head_count.toString();
						tots.head_count += record.space.head_count;
					}
					var clientName = '<i class="text-muted">Not set</i>';
					if (record.client) {
						clientName = record.client.name;
					}
					var spaceName = '<i class="text-muted">Not set</i>';
					if (record.space) {
						spaceName = record.space.name;
					}
					
					html += '<tr>'+
								'<td>'+ clientName +'</td>'+
								'<td>'+ spaceName +'</td>'+
								'<td>'+ headCount +'</td>'+
								'<td>'+ dateTxt +'</td>';
					
					if (grouped.inventory_billable_categories) {
						
						_.each(grouped.inventory_billable_categories, function (cat) {
							
							var cost = 0;
							if (record.breakdown) {
								
								if (
									record.breakdown.categories
									&& record.breakdown.categories[cat.id.toString()]
								) {
									cost += parseInt(record.breakdown.categories[cat.id.toString()]);
								}
								
								if (
									record.breakdown.labor
									&& record.breakdown.labor[cat.id.toString()]
								) {
									cost += parseInt(record.breakdown.labor[cat.id.toString()]);
								}
								
								tots.categories[cat.id.toString()] += cost;
								html += '<td>$ '+ (cost/100).formatMoney(2) +'</td>';
								
							} else {
								
								html += '<td>$ 0.00</td>';
								
							}
							
						});
						
					}
					
					tots.subtotal += parseInt(record.breakdown.subTotal);
					html += '<td>$ '+ (record.breakdown.subTotal/100).formatMoney(2) +'</td>';
					
					if (grouped.surcharges) {
						
						_.each(grouped.surcharges, function (cat) {
							
							if (
								record.breakdown
								&& record.breakdown.surcharges
								&& record.breakdown.surcharges[cat.id.toString()]
							) {
								
								tots.surcharges[cat.id.toString()] += parseInt(record.breakdown.surcharges[cat.id.toString()]);
								html += '<td>$ '+ (record.breakdown.surcharges[cat.id.toString()]/100).formatMoney(2) +'</td>';
								
							} else {
								html += '<td>$ 0.00</td>';
							}
							
						});
						
					}
					
					if (grouped.tax_rates) {
						
						_.each(grouped.tax_rates, function (cat) {
							
							if (
								record.breakdown
								&& record.breakdown.taxes
								&& record.breakdown.taxes[cat.id.toString()]
							) {
								
								tots.taxes[cat.id.toString()] += parseInt(record.breakdown.taxes[cat.id.toString()]);
								html += '<td>$ '+ (record.breakdown.taxes[cat.id.toString()]/100).formatMoney(2) +'</td>';
								
							} else {
								html += '<td>$ 0.00</td>';
							}
							
						});
						
					}
					
					tots.total += parseInt(record.breakdown.total);
					html += 	'<td>$ '+ (record.total/100).formatMoney(2) +'</td>'+
							'</tr>';
					
				});
				
				// Add totals
				html += '<tr>'+
							'<th>Total Count '+ records.length +'</th>'+
							'<th></th>'+
							'<th>'+ tots.head_count +'</th>'+
							'<th></th>';
				
				if (grouped.inventory_billable_categories) {
					
					_.each(grouped.inventory_billable_categories, function (cat) {
						
						html += '<th>$ '+ (tots.categories[cat.id.toString()]/100).formatMoney(2) +'</th>';
						
					});
					
				}
				
				html += '<th>$ '+ (tots.subtotal/100).formatMoney(2) +'</th>';
				
				if (grouped.surcharges) {
					
					_.each(grouped.surcharges, function (cat) {
						
						html += '<th>$ '+ (tots.surcharges[cat.id.toString()]/100).formatMoney(2) +'</th>';
						
					});
					
				}
				
				if (grouped.tax_rates) {
					
					_.each(grouped.tax_rates, function (cat) {
						
						html += '<th>$ '+ (tots.taxes[cat.id.toString()]/100).formatMoney(2) +'</th>';
						
					});
					
				}
				
				html += 	'<th>$ '+ (tots.total/100).formatMoney(2) +'</th>'+
						'</tr>';
				
				
				html += '</table>';
				
				sb.data.makePDF(html, 'I', {
					orientation: 'landscape'
				});
				
			}
		);
		
	}
	
	function viewReport (ui, state, draw) {

		function setFields (cats, options) {

			function sumVal (data, field, type) {
				
				var sum = 0;
				if (_.isArray(data)) {
				
					_.each(data, function(r){										
						
						switch (type) {
							
							case 'total':
								sum += r[field];
								break;
							
							case 'subTotal':
								if (
									r.breakdown
									&& r.breakdown
								) {
									sum += parseInt(r.breakdown[type]);
								}
								break;
							
							default:
								if (
									r.breakdown
									&& r.breakdown[type]
									&& r.breakdown[type][field]
								) {
									
									sum += parseInt(r.breakdown[type][field]);
									
								}
								break;
							
						}
						
					});
					
				} else {
					
					sum = data;
					
				}
				
				if(sum == NaN){
					sum = 0;
				}
				
				return sum;
				
			}

			function sumUi (ui, sum, rawValue, data){
				
				var totalAmount = sumVal(
					data
					, this.field
					, this.type
				);
				
				if(!_.isUndefined(ui) && !_.isNull(ui)){
					
					ui.empty();
					ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
					ui.patch();	
																
				}
				
				return (totalAmount/100).formatMoney();							
		
			}
			
			var fields = {
                client: {
					title: 'Client'
					, type: 'link'
					, view: function(ui, obj) {

						if(obj.client === null) {
							
							if ( ui ) {
								
								ui.makeNode('none', 'div', {
									text: 'N/A'
								});	
															
							} else {
										
								return 'N/A';
								
							}
							
						} else {
							
							if (ui) {
								
								ui.makeNode('link', 'div', {
									tag: 'a'
									, css: 'link'
									, style: 'white-space: nowrap;'
									, text: '<i class="linkify icon"></i> ' + obj.client.name
									, href: sb.data.url.createPageURL(
												'object-view', 
												{
													id: 		obj.client.id
													, name: 	obj.client.name
													, type: 	'companies'
												}
											)
								});
								
							} else {
								
								return obj.client.name;
								
							}
							
						}
						
					}	
				}
				, project: {
					title: 'Project'
					, type: 'link'
					, view: function(ui, obj) {

						if (obj.space === null || !obj.space) {
							
							ui.makeNode('none', 'div', {
								text: 'N/A'
							});
							
						} else {
							
							if (ui) {
								
								ui.makeNode('link', 'div', {
									tag: 'a'
									, css: 'link'
									, style: 'white-space: nowrap;'
									, text: '<i class="linkify icon"></i> ' + obj.space.name
									, href: sb.data.url.createPageURL(
												'object-view', 
												{
													id: 		obj.space.id
													, name: 	obj.space.name
													, type: 	'groups'
													, group_type: 'Project'
												}
											)
								});
								
							} else {
								
								return obj.space.name;
								
							}
							
						}
						
					}	
				}
				, head_count: {
					title: 'Head Count'
					, view: function(ui, obj) {
						
						if (obj.space === null && ui) {
							
							ui.makeNode('none', 'div', {
								text: 'N/A'
								, css: 'pull-right'
							});
							
						} else {
							
							var count = obj.space.head_count;
							if (!count) {
								count = 0;
							}
							
							if (ui) {
								
								ui.makeNode('c', 'div', {
									text: 	count.toString()
									, css: 	'pull-right'
								});
								
							} else {
								
								return count;
								
							}
							
						}
						
						return count;
						
					}	
				}
				, locations: {
					title: 'Location(s)'
					, type: 'locations'
				}
				, event_date: {
					title: 'Event Date'
					, type: 'date'
					, view: function (ui, obj) {

						var txt = ' - ';
						
						if (!_.isEmpty(obj.event_date))
							txt = moment(obj.event_date).format('M/D/YYYY');
						
						if (ui) {
							
							ui.makeNode('d', 'div', {
								text: 		txt
								, css: 	'pull-right'
								, style: 	'white-space: nowrap;'
							});
							
						}
						
						return txt;
						
					}
					, rangeOver: true
				}
            };
	            options.metrics.sum.fields = {
/*
		            client: function(ui, sum, raw, data){ 
						if (!_.isUndefined(ui) && !_.isNull(ui)) {
						
							ui.empty();
							ui.makeNode('client', 'div', {css: '', text:''});
							ui.patch();	
														
						}			            

			            }
		            , project: function(ui, sum, raw, data){
						if (!_.isUndefined(ui) && !_.isNull(ui)) {
						
							ui.empty();
							ui.makeNode('project', 'div', {css: '', text:''});
							ui.patch();	
														
						}			            
		            }
*/ 
	            };
	            options.metrics.sum.fields.head_count = function (ui, sum, raw, data) {
		            
		            var totHeadCount = 0;
		            _.each(data, function (rec) {
			            
			            if (rec && rec.space && rec.space.head_count) {
				            totHeadCount += rec.space.head_count;
			            }
			            
		            });
		            
		            if (!_.isUndefined(ui) && !_.isNull(ui)) {
						
						ui.empty();
						ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>'+ totHeadCount.toString() + '</strong>'});
						ui.patch();	
																	
					}
					
					return totHeadCount;	
		            
	            };
			
			grouped = _.groupBy(
                cats
                , function (cat) {
	                
	                return cat.object_bp_type;
	                
                }
            );
            
            // Line items
            if (grouped.inventory_billable_categories) {
                
                _.each(grouped.inventory_billable_categories, function (cat) {
					
					fields['c'+ cat.id] = {
						title: 	cat.name
						, view: function (ui, obj) {
							
							var amt = 0;
							if (
								obj.breakdown
								&& obj.breakdown.categories
								&& obj.breakdown.categories[this.catId.toString()]
							) {
								
								amt = parseInt(obj.breakdown.categories[this.catId.toString()]);
								
							}
							if (
								obj.breakdown
								&& obj.breakdown.labor
								&& obj.breakdown.labor[this.catId.toString()]
							) {
								
								amt = parseInt(obj.breakdown.labor[this.catId.toString()]);
								
							}
							
							if (ui) {
								
								ui.makeNode(
									'amt'
									, 'div'
									, {
										text: '$ '+ (amt/100).toFixed(2)
										, css: 'pull-right'
										, style: 'white-space: nowrap;'
									}
								);
								
							}
							
							return (amt/100).toFixed(2);
							
						}.bind({
							catId: cat.id
						})
					};
					
					options.metrics.sum.fields['c'+ cat.id] = sumUi.bind({
						field: 	cat.id.toString()
						, type: 'categories'
					});
					
                });
                
            }
            
            // Subtotal
            fields.subTotal = {
	            title: 'Subtotal'
	            , view: function (ui, obj) {
		            
		            var amt = 0;
					if (obj.breakdown.subTotal) {
						
						amt = parseInt(obj.breakdown.subTotal);
						
					}
					
					if (ui) {
						
						ui.makeNode(
							'amt'
							, 'div'
							, {
								text: '$ '+ (amt/100).toFixed(2)
								, css: 'pull-right'
								, style: 'white-space: nowrap;'
							}
						);
						
					}
					
					return (amt/100).toFixed(2);
		            
	            }
            };
            options.metrics.sum.fields.subTotal = sumUi.bind({field:'subTotal', type:'subTotal'});
            
            // Surcharges
            if (grouped.surcharges) {
                
                _.each(grouped.surcharges, function (cat) {
					
					fields['c'+ cat.id] = {
						title: 	cat.name
						, view: function (ui, obj) {
							
							var amt = 0;
							if (
								obj.breakdown
								&& obj.breakdown.surcharges
								&& obj.breakdown.surcharges[this.catId.toString()]
							) {
								
								amt = parseInt(obj.breakdown.surcharges[this.catId.toString()]);
								
							}
							
							if (ui) {
								
								ui.makeNode(
									'amt'
									, 'div'
									, {
										text: '$ '+ (amt/100).toFixed(2)
										, css: 'pull-right'
										, style: 'white-space: nowrap;'
									}
								);
								
							}
							
							return (amt/100).toFixed(2);
							
						}.bind({
							catId: cat.id
						})
					};
					
					options.metrics.sum.fields['c'+ cat.id] = sumUi.bind({
						field: 	cat.id.toString()
						, type: 'surcharges'
					});
					
                });
                
            }
			
			// Taxes
			if (grouped.tax_rates) {

                _.each(grouped.tax_rates, function (cat) {

					var colTitle = '';
						
					function taxRateTitle (taxRate){
						
						var name = taxRate.name;
						var rateInfo = '';
						
						if ( taxRate.rate )
							rateInfo = ' (' + (taxRate.rate/100) + '%)';

						if ( taxRate.state ) 
							rateInfo = ' (' + (taxRate.rate/100) + '% ' + taxRate.state + ')';
						
						if ( !_.isEmpty(rateInfo) ){ 
							return name + rateInfo;
						} else {
							return name;
						}
						
					}
						
					colTitle = taxRateTitle(cat);	
					
					fields['c'+ cat.id] = {
						title:  colTitle
						, view: function (ui, obj) {
							
							var amt = 0;
							if (
								obj.breakdown
								&& obj.breakdown.taxes
								&& obj.breakdown.taxes[this.catId.toString()]
							) {
								
								amt = parseInt(obj.breakdown.taxes[this.catId.toString()]);
								
							}
							
							if (ui) {
								
								ui.makeNode(
									'amt'
									, 'div'
									, {
										text: '$ '+ (amt/100).toFixed(2)
										, css: 'pull-right'
										, style: 'white-space: nowrap;'
									}
								);
								
							}
							
							return (amt/100).toFixed(2);
							
							
						}.bind({
							catId: cat.id
							, name: cat.name
						})
					};
					options.metrics.sum.fields['c'+ cat.id] = sumUi.bind({
						field: 	cat.id.toString()
						, type: 'taxes'
					});
					
                });
                
            }
            
			fields.tax_exempt = {
			  title: 'Tax Exempt'
			  , view: function(ui, obj){

					var tax_exempt = '';
					
					if ( 
						obj.client 
						&& obj.client.hasOwnProperty('tax_exempt')
						&& obj.client.tax_exempt == 1 
					) {

						tax_exempt = 'TE';
						
					}
	
					if (!_.isUndefined(ui) && !_.isNull(ui)) {
					
						ui.empty();
						ui.makeNode('total', 'div'
							, {
								css: 'ui small gray text pull-right'
								, style: 'white-space: nowrap;'
								, text:'<strong>'+ tax_exempt + '</strong>'
							}
						);
						ui.patch();	
														
					}
				
					return tax_exempt;
			  
			  }
		  };
            
            // Total
            fields.total = {
	            title: 'Total'
	            , view: function (ui, obj) {
		            
		            var amt = 0;
					if (obj.total) {
						
						amt = parseInt(obj.total);
						
					}
					
					if (ui) {
						
						ui.makeNode(
							'amt'
							, 'div'
							, {
								text: '$ '+ (amt/100).toFixed(2)
								, css: 'pull-right'
								, style: 'white-space: nowrap;'
							}
						);
						
					}
					
					
					return (amt/100).toFixed(2);
		            
	            }
            };
            options.metrics.sum.fields.total = sumUi.bind({field:'total', type:'total'});

			return fields;
			
		}

		sb.notify({
            type: 'show-collection',
            data: {
                domObj: 	ui,
                state: 		state,
                objectType: 'inventory_menu_pricing_breakdown',
                singleView: false,
                actions: {
                    view: 		false,
                    create: 	false,
                    template: 	false,
                    pdfDownload: {
	                    id: 		'pdfDownload'
	                    , name: 	'Download PDF'
	                    , title: 	'Download PDF'
	                    , icon: 	'file'
	                    , color: 	'blue'
	                    , ui: 		false
	                    , headerAction: 	function (selected, state, onComplete, query) {
		                    
		                    producePDF(query.range);
							
	                    }
	                    , requireSelection: false
                    }, 
                    downloadCSV: true
                },
                fields: {
                    client: {
						title: 'Client'
						, view: function () {
							
						}
					}
					, project: {
						title: 'Project'
						, view: function () {
							
						}
					}
					, event_date: {
						title: 'Event Date'
						, type: 'date'
					}
                },
                metrics:{
					sum:{
						title:		'Total'
						, fields:	{}
					}
				},
                parseData: function (data, callback, query, subview, range, types, options) {

	                var catsToGet = [];
	                var uncategorized = false;
	                
	                _.each(data.data, function (record) {

						_.each(record.breakdown.categories, function (cat, catId) {
						
							catsToGet.push(
								parseInt(catId)
							);
							
						});
						
						_.each(record.breakdown.labor, function (cat, catId) {
						
							catsToGet.push(
								parseInt(catId)
							);
							
						});
						
						_.each(record.breakdown.surcharges, function (cat, catId) {
							
							catsToGet.push(
								parseInt(catId)
							);
							
						});
						
						_.each(record.breakdown.taxes, function (cat, catId) {
							
							catsToGet.push(
								parseInt(catId)
							);
							
						});
						
						if (record.space !== false || record.space !== null) {
							
							record.locations = record.space.locations;
							
						}	
		                
	                });
	                
	                ///filter results that do not have a space
	                data.data = _.filter(data.data, function(rec){
		                
		                	if ( rec.space !== false && rec.space != null ){
			                	return rec;
		                	}
	                });
	                
	                uncategorized = _.contains(catsToGet, NaN);

	                catsToGet = _.chain(catsToGet)
	                				.uniq()
	                				 .compact()
	                				  .value();

	                sb.data.db.obj.getById(
		                ''
		                , catsToGet
		                , function (cats) {

			                if (uncategorized) {
				                
				                cats.push({
					                object_bp_type: 'inventory_billable_categories'
					                , name: 		'Uncategorized'
					                , id: 			'false'
				                });
				                
			                }
			                
			                options.fields = setFields(cats, options);

							callback(data);
			                
		                }
		                , {
			                name: true
		                }
		                , true
	                );
	                
                },
                selectedView: 'table',
                sortCol: 'event_date',
                menu: {
	                subviews: false
                },
                where: {
	                related_object:state.objectId,
	                is_template: 0,
					childObjs: {
						breakdown: 	true
						, client: 	true
						, event_date: 	true
						, space: {
							name: 			true
							, start_date: 	true
							, head_count: 	true
							, locations: true
						}
						, total: 		true
						, tax_exempt: 	true
					}
				}
            }
        });
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type:'register-application',
				data:{
					navigationItem: {
						id: 	'menuPricingBreakdownReport',
						title: 	'Pricing Breakout Report',
						icon: 	'<i class="fa fa-table"></i>',
						views:[
							{
								id: 		'menuPricingBreakdownReport'
								, default: 	true
								, type: 	'custom'
								, title: 	'Menu Pricing Breakdown Report'
								, icon: 	'<i class="fa fa-exclamation"></i>'
								, dom: 		viewReport
							}
						]
					}
				}
			});
			
			sb.notify({
				type: 'register-report',
				data: {
					id:				'menuPricingBreakdownReport'
					, icon:			'table'
					, header: 		'Pricing Breakdown'
					, subheader: 	'Pricing breakdown across all projects'
					, type: 		'Accounting'
				}
			});
			
		}
		
	}
	
});