Factory.register('inv-packlist', function (sb) {
	
	var units_of_measure = {};
	if (sb) {
		units_of_measure = sb.data.units.uom;
	}
	
	function getData (obj, callback) {
		
		var menuId = false;
		if (
			obj
			&& obj.related_object
			&& obj.related_object.proposal
			&& obj.related_object.proposal.menu
		) {
			menuId = obj.related_object.proposal.menu;
		}
		
		if (menuId) {
			
			sb.data.db.obj.getWhere(
				'item_reservation'
				, {
					menu: menuId
					, childObjs: {
						menu_item: 			0
						, status: 			true
						, quantity: 			true
						, quantity_filled: 	true
						, filled: 			true
						, inventory_group: 	{
							name: 			true
						}
						, units: 			true
						, unit_type: 		true
					}
				}
				, function (reservations) {
					
					callback(reservations);
					
				}
			);
			
		} else {
			
			callback();
			
		}
		
	}
	
	function generateHtml (reservations, objectData) {
		function getQtyDisplay(item){
	
			var ret = item.quantity.toString() +' ';
			
			if(item.unitType === 'quantity' && item.measurement === 0){
				ret += item.name;
				return ret;
			}
	
			var measurementName = '';
	
			if(item.measurement === 0){
				measurementName = units_of_measure.uscu[item.unitType].base.abbr;
			} else if (units_of_measure.uscu[item.unitType]) {
				measurementName = _.findWhere(units_of_measure.uscu[item.unitType].measurements, {id:item.measurement}).abbr;
			} else if (item.unitType === 'servings') {
				measurementName = 'servings';
			}
			ret += measurementName +' of '+ item.name;
	
			return ret;
			
		}
		
		function getItemDescriptionHtml (item) {
			
			var txt = 'x'+ item.absolute_qty +' '+ item.item.name +' ';
			if (
				item.item
				&& !_.isEmpty(item.item.description)
			) {
				
				txt += '<i>'+ item.item.description +'</i>';
				
			}
			
			return txt;
			
		}
		
		function getItemNoteHtml (item) {

			var note = '';

			if (item.hasOwnProperty('item')) {

				if (
					!_.isEmpty(item.item.note)
					&& _.isEmpty(item.note)
				) {

					note = item.item.note

				} else {

					note = item.note;

				}

			} else {

				if (!_.isEmpty(item.note)) {

					note = item.note;

				}

			}

			return note;
			
		}
		
		var html = '';
		
		_.each(
			objectData.pricedMenu.sections
			, function (section) {

				function getMenuSectionTitleHtml (section) {
					
					var html = '<div style="border: 1px solid lightgray; padding: 15px;">' +
						'<h3 style="font-weight:bold; padding-bottom: 15px;">'+ section.name;
					if(section.hasOwnProperty('details')){
						html +=
						'<h4 style="font-weight:bold; text-align:left; color:red; padding-bottom: 15px;">'
						+ section.details
					}

					if (
						moment(section.from).isValid()
						&& moment(section.to).isValid()
					) {
						
						html += ', '+ moment(section.from).format('h:mma') +' - '+ moment(section.to).format('h:mma');
						
					}
					
					html += '</h3>';
					
					return html;
					
				}

				
				html += getMenuSectionTitleHtml(section);
				html += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">'+
							'<thead>'+
								'<tr style="font-size:12px;font-weight:bold; background-color: #F2F3F4;">'+
									'<th style="padding:5px; color:#B9BEC4; text-align:left;">Item</th>'+
									'<th style="padding:5px; color:#B9BEC4; text-align:left; width:15%;">Note</th>'+
									'<th style="padding:5px; color:#B9BEC4; text-align:right;">Status</th>'+
								'<tr>'+
							'</thead>'+
						'<tbody>';
				
				_.each(section.items, function (item,index) {
					var rowStyle ="";
					if(index %2 ===1){
						rowStyle=" style=\"background-color: #F2F3F4;\"";
					}
					console.log('item::', item);
					html += 
							'<tr'+ rowStyle + '>'+
								'<td style="padding:10px 0px;">'+ getItemDescriptionHtml(item) +'</td>'+
								'<td style="padding:10px 0px; text-align:left; color:red;">'+ getItemNoteHtml(item) +'</td>'+
								'<td></td>'+
							'</tr>';
					
					var res = _.where(
						reservations
						, {
							menu_item: item.id
						}
					);
					
					if (!_.isEmpty(res)) {
						
						console.log(
							'res::', res
						);
						// Display reservations, grouped by stock item, in case
						// there are more than one of the same type.
						_.chain(res)
							.groupBy(
								function (res) {
									
									return res.inventory_group.id;
									
								}
							)
							.each(
								function (r) {
									
									var qtyDisp = getQtyDisplay({
										name: 			r[0].inventory_group.name,
										unitType: 		r[0].unit_type,
										measurement: 	parseInt(r[0].units), // Reservation units are always in base, so doesn't matter
										quantity: 		_.reduce(
															r
															, function (memo, reservation) {
																return memo + +reservation.quantity;
															}
															, 0
														)
									});
									
									var statusTxt = 'Reserved';
									_.each(r, function (reservation) {
										
										if (!reservation.filled) {
											statusTxt = 'Not reserved';
										}
										
									});
									
									html += 
										'<tr>'+
											'<td>⟶ 	x'+ qtyDisp +'</td>'+
											'<td></td>'+
											'<td>'+ statusTxt +'</td>'+
										'</tr>';
									
								}
							);
						
					}
					
				});
				
				html += '</tbody></table></div></br>';
				
			}
		);
		
		return html;
		
	}
	
	// Merge tags
	function registerMergeTags () {
		
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'packlist'
				, tag: 		'Packlist'
				, data: 	getData
				, parse: 	generateHtml
			}
		});
		
	}
	
	return {
		
		init: function () {
			
			registerMergeTags();
			
		}
		
	};
	
});