Factory.register('inv-beo', function (sb) {
	
	var units_of_measure = {};
	
	if (sb) {
		units_of_measure = sb.data.units.uom;
	}
	
	// Items list
	
	function getData (obj, callback, opts) {

		var menuId = false;
		
		if (
			obj
			&& obj.related_object
			&& obj.related_object.proposal
			&& obj.related_object.proposal.menu
		) {
			menuId = obj.related_object.proposal.menu;
		}

		if (menuId) {
			
			sb.data.db.obj.getWhere(
				'inventory_menu_line_item'
				, {
					menu: menuId
				}
				, function (reservations) {
                    var controllerCall = 'generateBEOMergeTag';

                    if ( opts.controllerCall && opts.controllerCall == 'generateInfinityBEOMergeTag')
                        controllerCall = opts.controllerCall;
                    if ( appConfig.instance === 'dreamcatering' )
                        controllerCall = 'generateDreamBEOMergeTag'; 

                    sb.data.db.controller(controllerCall, {
						menuId: menuId
						, options: opts || []
						, setup: {
							border: true
							, sectionNames: true
							, internalNotes: true
							, tz: Intl.DateTimeFormat().resolvedOptions().timeZone
							, choiceItems: true
						}
					}, function(mergeTagHTML) {
						
						callback({
							data: reservations
							, mergeTagHTML: mergeTagHTML
						})
						
					});
					
				}
			);
			
		} else {
			
			callback();
			
		}
		
	}
	
	function generateHtml (items, objectData, opts, callback) {

		function getQtyDisplay(item, menu){
	
			var qtyText = item.absolute_qty;

			if (!_.isEmpty(item.yield_type) && item.yield_type !== 'servings' && item.measurement > 0) {
				
				var measurementObj = _.findWhere(units_of_measure['uscu'][item.yield_type].measurements, {id:item.measurement});
				qtyText = item.absolute_qty/to_base_units(item.yield_type, item.measurement, 1) + measurementObj.abbr;

			}
			
			return qtyText;
			
		}
		
		function getItemDescriptionHtml (item) {

			var txt = item.item.name +' ';
			if (
				item.item
				&& !_.isEmpty(item.item.description)
			) {
				
				txt += '</br><span class="text-muted">'+ item.item.description +'</span>';
				
			}
			
			txt += generateChoiceItemsListHTML(item);
			
			return txt;
			
		}
		
		function getItemNoteHtml (item) {
			
			var txt = '';
			if (
				item.item
				&& item.item.note
			) {
				txt = item.item.note;
			}
			return txt;
			
		}
		
		function old_layout() {
			
			html += '<div style="border: 1px solid lightgray; padding: 15px;">';
		
			html += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">'+ 
						'<thead>'+
							'<tr style="text-align:left; font-size:12px;font-weight:bold; background-color: #F2F3F4; color:#B9BEC4;">'+
								'<th style="padding:5px; color:#B9BEC4; width: 30%;">Item</th>'+
								'<th style="padding:5px; color:#B9BEC4; width: 30%;">Start</th>'+
								'<th style="padding:5px; color:#B9BEC4; width: 30%;">End</th>'+
								'<th style="padding:5px; color:#B9BEC4; width: 30%;">Notes</th>'+
								'<th style="padding:5px; color:#B9BEC4; width: 30%;">Qty</th>'+
							'<tr>'+
						'</thead>'+
						'<tbody>';
			
			var cats = [];
			
			_.each(opts, function (opts) {
				cats.push(parseInt(opts));
			});
			
			_.each(items, function (item, i) {
	
				var catId = item.item.category;
				var rowBackgroundColor = '';
				
				if (i % 2 === 1) {
	
					rowBackgroundColor = '#F2F3F4';
					
				}
				
				if (catId.id) {
					catId = catId.id;
				}
				
				catId = parseInt(catId);
				
				// Check for product category
				if (
					_.contains(cats, catId)
					|| _.isEmpty(cats)
				) {
					
					var section = _.findWhere(objectData.menu.sections, {id: item.section});
					var start = '';
					var end = '';
					
					if (section) {
						
						if (moment(section.from).isValid()) {
							
							start = moment(section.from).local().format('h:mm a');
							
						}
						
						if (moment(section.to).isValid()) {
							
							end = moment(section.to).local().format('h:mm a');
							
						}
						
					}
					
					html += '<tr style="background-color: '+ rowBackgroundColor +'">'+
								'<td style="padding: 10px 0px;">'+ getItemDescriptionHtml(item) +'</td>'+
								'<td style="padding: 10px 0px;">'+ start +'</td>'+
								'<td style="padding: 10px 0px;">'+ end +'</td>'+
								'<td style="padding: 10px 0px;">'+ getItemNoteHtml(item) +'</td>'+
								'<td style="padding: 10px 0px;">'+ getQtyDisplay(item, objectData.menu) +'</td>'+
							'</tr>';
					
				}
				
			});
			
			html += '</tbody></table></div>';
			
		}
		
		function backend_layout(cb) {
			
			/*
sb.data.db.service('BeoService', 'generateBEOMergeTag', {
				menuId: objectData.menu.id
				, setup: {
					border: true
					, sectionNames: true
					, internalNotes: true
				}
			}, function(mergeTagHTML) {
				
				cb(mergeTagHTML);
				
			});
*/
			
		}
		
		function new_layout() {

			var orderedSections = _.sortBy(
					objectData.menu.sections
					, 'sortId'
				);
			
			html += '<div style="border: 1px solid lightgray; padding: 15px;">';
			
			_.each(orderedSections, function(section, i) {
				
				if (!_.isEmpty(section.items)) {
					
					var sectionItems = section.items;
					
					// Redundant 
					if (!_.isEmpty(sectionItems)) {

						var sectionTime = '';
						var internalNotes = section.details;
						var cats = [];
			
						_.each(opts, function (opts) {
							cats.push(parseInt(opts));
						});
						
						if(section.from){
							sectionTime = ', ' + moment(section.from).local().format('M/D/YYYY h:mm a') +' ';
						}
						
						if(section.to){
							sectionTime += '- '+ moment(section.to).local().format('M/D/YYYY h:mm a');
						}
						
						html += '<h3 style="font-weight:bold;">'+ section.name +' '+ sectionTime +'</h3>';

						if (internalNotes) {
							
							html += '<div class="text-muted" style="margin-top: 5px;">'+ internalNotes +'</div>';	
							
						}
						
						html += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse; margin: 15px 0;">'+ 
									'<thead>'+
										'<tr style="text-align:left; font-size:12px;font-weight:bold; background-color: #F2F3F4; color:#B9BEC4;">'+
											'<th style="padding:5px; color:#B9BEC4; width: 30%;">Item</th>'+
											//'<th style="padding:5px; color:#B9BEC4; width: 30%;">Start</th>'+
											//'<th style="padding:5px; color:#B9BEC4; width: 30%;">End</th>'+
											'<th style="padding:5px; color:#B9BEC4; width: 50%;">Notes</th>'+
											'<th style="padding:5px; color:#B9BEC4; width: 20%;">Qty</th>'+
										'<tr>'+
									'</thead>';
						
						_.each(sectionItems, function (item, i) {
				
							var catId = item.item.category;
							var rowBackgroundColor = '';
							
							if (i % 2 === 1) {
				
								rowBackgroundColor = '#F2F3F4';
								
							}
							
							if (catId.id) {
								catId = catId.id;
							}
							
							catId = parseInt(catId);
							
							// Check for product category
							if (
								_.contains(cats, catId)
								|| _.isEmpty(cats)
							) {
								
								var start = '';
								var end = '';
								
								if (section) {
									
									if (moment(section.from).isValid()) {
										
										start = moment(section.from).local().format('h:mm a');
										
									}
									
									if (moment(section.to).isValid()) {
										
										end = moment(section.to).local().format('h:mm a');
										
									}
									
								}
								
								html += '<tbody><tr style="background-color: '+ rowBackgroundColor +'">'+
											'<td style="padding: 10px 0px;">'+ getItemDescriptionHtml(item) +'</td>'+
											//'<td style="padding: 10px 0px;">'+ start +'</td>'+
											//'<td style="padding: 10px 0px;">'+ end +'</td>'+
											'<td style="padding: 10px 0px;">'+ getItemNoteHtml(item) +'</td>'+
											'<td style="padding: 10px 0px;">'+ getQtyDisplay(item, objectData.menu) +'</td>'+
										'</tr></tbody>';
								
							}
							
						});
						
						html += '</table>'
						
					}
					
				}
				
			});
			
			html += '</div>';
			
		}
		
		var html = '';

		new_layout();
		
		//backend_layout(function(generatedMergeTagHTML) {

			//callback(generatedMergeTagHTML);
			
		//});
		
		return html;
		
	}
	
	function generateChoiceItemsListHTML(item) {

		var HTML = '';
		
		if (!_.isEmpty(item.item.ingredient_names)) {
			
			// HTML += '<div class="text-muted">';
		
				/*
HTML += '</br>';
			
				HTML += '<p>Choices:</p>';
*/
			
				HTML += '<ul style="padding-left: 20px !important;">';
				
				_.each(item.item.ingredient_names, function(choiceItem, i) {
				
					HTML += '<li>'+ choiceItem +'</li>';
				
				});
			
				HTML += '</ul>';
				
			// HTML += '</div>';	
			
		}

		return HTML;
		
	}

	function generateDreamsChoiceItemsListHTML(item, _fullitems) {

		if(item && item.item && item.item.id) {
			var HTML = '';
			var fullItem = _.find(_fullitems, {id: item.item.id});

			if (!!fullItem) {

				var full_items = fullItem['items'];

				//TODO -- verify this color
				var lineItemType = item['type'];

				if (lineItemType === 0) {
					var choicesHaveBeenSelected = false;


					if (item['choices'] && item['choices'][0] && item['choices'][0]['choice'] && item['choices'][0]['choice'].length > 0) {
						choicesHaveBeenSelected = true;
						if (choicesHaveBeenSelected) {
							//have choices
							_.forEach(item['choices'][0], function (choiceSelection, i) {
								if (choiceSelection && choiceSelection.length > 0) {
									///each selection can have its own items of choice selections
									_.forEach(choiceSelection, function (choice, j) {
										if (choice['choice'] && choice['choice'].length > 0) {
											_.forEach(choice['choice'], function (selectedChoice, k) {
												if (selectedChoice > 0) {

													//TODO -- here fullItem check
													var componentInformation = _.find(full_items, function (compItem) {
														return choice['item'] == compItem['id'];
													});

													if(!componentInformation){
														return;
													}

													var choiceInformation = _.find(componentInformation['choices'], function (chItem) {
														return chItem['id'] == selectedChoice;
													});

													var choiceSelectionName = '';

                                                    if ( choiceInformation && choiceInformation['inventory_group'] ) {

                                                        choiceSelectionName = choiceInformation['inventory_group']['name'];

                                                    }

													var choiceSelection_beo_servingstyle = '-';
													var choiceSelection_beo_qty = '-';
													var choiceSelection_beo_note = '';

													//TODO -- here fullItem check (converter in item)
													if (item['beo_ingredients']) {

														var choiceSelection_beoIngredients = _.find(item['beo_ingredients'], function (ingr) {

															return choiceInformation && choiceInformation['inventory_group'] && choiceInformation['inventory_group']['name'].includes(ingr['name']);
														});

														if (choiceSelection_beoIngredients && choiceSelection_beoIngredients['beo_servingstyle']) {
															choiceSelection_beo_servingstyle = choiceSelection_beoIngredients['beo_servingstyle'] || "";
														}
														if (choiceSelection_beoIngredients && choiceSelection_beoIngredients['beo_qty']) {
															choiceSelection_beo_qty = choiceSelection_beoIngredients['beo_qty'] || "";
														}
														if (choiceSelection_beoIngredients && choiceSelection_beoIngredients['beo_note']) {
															choiceSelection_beo_note = choiceSelection_beoIngredients['beo_note'] || "";
														}

														HTML += '<tr>';
														HTML += '<td>&nbsp;&nbsp; - ' + choiceSelectionName + '</td>';
														HTML += '<td style="color: black; text-align:right;"> </td>';
														HTML += '<td style="text-align: right;"> </td>';
														HTML += '</tr>';


														if (choiceSelection_beo_note.length > 0) {
															HTML += '<tr>';
															HTML += '<td colspan="5"  style="color: red;">&nbsp;&nbsp;&nbsp;' + choiceSelection_beo_note.replace(/(<([^>]+)>)/gi, "") + '</td>';
															HTML += '</tr>';
														}

													}

												}
											});
										}
									});
								}
							})

						} else {

							//TODO -- here fullItem['beo_ingredients']
							//not have choices
							if (item['item']['items'] && _.empty(item['beo_ingredients'])) {
								_.forEach(full_items, function (item, j) {

									var componentItem = item['inventory_group'];

									var component_itemNameDisplay = componentItem['name'] || "";

									HTML += '<tr>';
									HTML += '<td  colspan="5">&nbsp;&nbsp; - ' + component_itemNameDisplay + '</td>';
									HTML += '</tr>';

									/// NESTED COMPONENT ITEM ALSO HAS COMPONENT ITEMS
									if (componentItem['items'].length > 0) {
										_.forEach(componentItem['items'], function (it, k) {


											var componentNestedName = it['inventory_group']['name'] || "";

											HTML += '<tr>';
											HTML += '<td colspan="5">&nbsp;&nbsp;&nbsp;&nbsp; -- ' + componentNestedName + '</td>';
											HTML += '</tr>';

											///CHECK TO SEE IF COMPONENET ITEM OF THE ABOVE NESTED, HAS COMPONENTS
											if (it['inventory_group']['items'] && it['inventory_group']['items'].length > 0) {
												_.forEach(it['inventory_group']['items'], function (litem, l) {

													var componentOfNestedCompName = litem['inventory_group']['name'] || "";

													HTML += '<tr>';
													HTML += '<td colspan="5">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; --- ' + componentOfNestedCompName + '</td>';
													HTML += '</tr>';
												});

											}

										});
									}

								});

							}


						}
					} 

                    if (fullItem.items && fullItem.items.length > 0) {

                        _.each(fullItem.items, function(it){

                            if (it.inventory_group && _.isEmpty(it.choices)) {

                                HTML += '<tr>';
                                HTML += '<td>&nbsp;&nbsp; - ' + it.inventory_group.name + '</td>';
                                HTML += '</tr>';

                            }

                        });
                    }

				} else if (lineItemType === 'item') {
					//no rendering the description for this case
					//console.log('-- ' + [item['item']['name']] + ' rendered custom item...');
					//custom item
				}
			}
		}

		return (HTML || '');

	}
	
	// Menu sections
	
	function generateSectionsHtml (data, objectData) {
		
		// This function is used to generate html for PROPOSAL.INVOICE merge tag
		// and menu sections merge tag.
		
		var html = '';
		
		if (
			objectData.menu
			&& objectData.menu.sections
		) {
			
			html = '<div style="border: 1px solid lightgray; padding: 15px;">';
			
			html += '<table class="medium-editor-table" style="width:100%;">'+ 
							'<colgroup>'+
								'<col span="1" style="width: 60%;">'+
								'<col span="1" style="width: 20%;">'+
								'<col span="1" style="width: 20%;">'+
							'</colgroup>'+
							'<thead>'+
								'<tr>'+
									'<th align="left">Name</th>'+
									'<th>Start</th>'+
									'<th>End</th>'+
								'<tr>'+
							'</thead>'+
						'<tbody>';
			
			var rowColor = '';
			
			var orderedSections = _.sortBy(
				objectData.menu.sections
				, 'sortId'
			);
			
			var shownSections = _.reject(orderedSections, function(section){
				return section.hidden_on_invoice == 'yes';
			});
			
			_.each(shownSections, function (section, i) {
				
				var start = '';
				var end = '';
				var details = '';
				
				if (section) {
					
					if (moment(section.from).isValid()) {
						
						start = moment(section.from).local().format('h:mm a');
						
					}
					
					if (moment(section.to).isValid()) {
						
						end = moment(section.to).local().format('h:mm a');
						
					}
					
				}
				
				html += 
						'<tr data-id="'+ i +'" style="background-color:'+ rowColor +'; padding:5px;">'+
							'<td data-id="'+ section.name +'" style="padding:5px;">'+ section.name +'</td>'+
							'<td data-id="'+ i +'-s" style="text-align: center;padding:5px;">'+ start +'</td>'+
							'<td data-id="'+ i +'-s" style="text-align: center;padding:5px;">'+ end +'</td>'+							
						'</tr>';
						
				if(rowColor == ''){
					rowColor = '#F2F3F4';
				}else{
					rowColor = '';
				}
				
			});
			
			html += '</table></div>';
			
		}
		
		return html;
		
	}
	
	// Menu comments
	
	function getComments (obj, callback) {
		
		var menuId = false;
		if (
			obj
			&& obj.related_object
			&& obj.related_object.proposal
			&& obj.related_object.proposal.menu
		) {
			menuId = obj.related_object.proposal.menu;
		}
		
		if (menuId) {
			
			sb.data.db.obj.getWhere(
				'notes'
				, {
					type_id: menuId
					// product_category: 
				}
				, function (comments) {
					
					callback(comments);
					
				}
			);
			
		} else {
			
			callback();
			
		}
		
	}
	
	function generateCommentsHtml (comments, objectData) {
		
		var html = '<table class="medium-editor-table" style="width:100%;">'+ 
							'<thead>'+
								'<tr>'+
									'<th></th>'+ 
								'<tr>'+
							'</thead>'+
						'<tbody>';
		
		function getCommentHtml (comment) {
			
			var ret = '';
			ret = comment.note;
			return ret;
			
		}
		
		_.each(comments, function (comment) {
			
			html += 
					'<tr>'+
						'<td>'+ getCommentHtml(comment) +'</td>'+
					'</tr>';
			
		});
		
		html += '</table>';
		
		return html;
		
		_.each(
			objectData.pricedMenu.sections
			, function (section) {
				
				function getMenuSectionTitleHtml (section) {
					
					var html = '<h3>'+ section.name;
					if (
						moment(section.from).isValid()
						&& moment(section.to).isValid()
					) {
						
						html += ', '+ moment(section.from).local().format('h:mma') +' - '+ moment(section.to).local().format('h:mma');
						
					}
					
					html += '</h3>';
					
					return html;
					
				}
				
				html += getMenuSectionTitleHtml(section);
				html += '<table class="medium-editor-table" style="width:100%;">'+ 
							'<thead>'+
								'<tr>'+
									'<th>Item</th>'+
									'<th>Note</th>'+
									'<th>Status</th>'+
								'<tr>'+
							'</thead>'+
						'<tbody>';
				
				_.each(section.items, function (item) {
					
					html += 
							'<tr>'+
								'<td>'+ getItemDescriptionHtml(item) +'</td>'+
								'<td>'+ getItemNoteHtml(item) +'</td>'+
								'<td></td>'+
							'</tr>';
					
					var res = _.where(
						reservations
						, {
							menu_item: item.id
						}
					);
					
					if (!_.isEmpty(res)) {
						
						// Display reservations, grouped by stock item, in case
						// there are more than one of the same type.
						_.chain(res)
							.groupBy(
								function (res) {
									
									return res.inventory_group.id;
									
								}
							)
							.each(
								function (r) {
									
									var qtyDisp = getQtyDisplay({
										name: 			r[0].inventory_group.name,
										unitType: 		r[0].unit_type,
										measurement: 	parseInt(r[0].units), // Reservation units are always in base, so doesn't matter
										quantity: 		_.reduce(
															r
															, function (memo, reservation) {
																return memo + +reservation.quantity;
															}
															, 0
														)
									});
									
									var statusTxt = 'Reserved';
									_.each(r, function (reservation) {
										
										if (!reservation.filled) {
											statusTxt = 'Not reserved';
										}
										
									});
									
									html += 
										'<tr>'+
											'<td>⟶ 	x'+ qtyDisp +'</td>'+
											'<td></td>'+
											'<td>'+ statusTxt +'</td>'+
										'</tr>';
									
								}
							);
						
					}
					
				});
				
				html += '</tbody></table>';
				
			}
		);
		
		return html;
		
	}
	
	// Merge tags
	
	function registerMergeTags () {

		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'menu-beo'
				, tag: 		'Menu BEO'
				, data: 	getData
				//, parse: generateHtml
				, parse: function(data) {

					return data.mergeTagHTML;					
					
				}
			}
		});


        sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'infinity-menu-beo'
				, tag: 		'Infinity Menu BEO'
				, data: 	function(obj, cb, opts){
                    opts.controllerCall = 'generateInfinityBEOMergeTag'
                    getData(obj, cb, opts);
                }
				//, parse: generateHtml
				, parse: function(data) {

					return data.mergeTagHTML;					
					
				}
			}
		});

		
		// Menu sections
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'menu-sections'
				, tag: 		'Menu Sections'
				, data: 	function (obj, callback) {
					
					callback(obj);
					
				}
				, parse: 	generateSectionsHtml
			}
		});
		
		// Menu comments
		sb.notify({
			type: 		'register-merge-tag'
			, data: 	{
				name: 		'menu-comments'
				, tag: 		'Menu Comments'
				, data: 	getComments
				, parse: 	generateCommentsHtml
			}
		});
		
	}
	
	return {
		
		displayChoiceItems: function(data) {
			var html = '';
			
			if (!data.hasOwnProperty('item')) {
				return console.log('%c ** No item found. **', 'color: red;');
			} else {
				
				if (!data.item.hasOwnProperty('id')) {
					return console.log('%c ** Not a valid item. Missing id. **', 'color: red;');
				}
				
			}

			if(appConfig.instance === 'dreamcatering' || appConfig.instance == 'rickyvoltz'){
				html += '<table>' + generateDreamsChoiceItemsListHTML(data.item, data.fullitems) + '</table>';
			} else {
				html += generateChoiceItemsListHTML(data.item);
			}

			if (!data.hasOwnProperty('cb')) {
				return console.log('%c ** A callback function needs to be provided. **', 'color: red;');
			} else {

				data.cb(html);

			}
			
		}
		
		, init: function () {
			
			registerMergeTags();
			
			sb.listen({
				'display-choice-items': this.displayChoiceItems
			});
			
		}
		
	};
	
});
