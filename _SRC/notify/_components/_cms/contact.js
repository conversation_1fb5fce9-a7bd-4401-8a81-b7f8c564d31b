Factory.register('contacts', function(sb){
	
	var ui = {},
		tableUI = {},
		components = {},
		singleComponents = {},
		contactInfoTypes = [],
		compact = false,
		companyId = 0,
		setupData = {
			tableTitle:{
				title:'<i class="fa fa-users"></i> Contacts',
				size:'large'
			},
			homeView:false,
			navigation:false
		},
		tableFilters = {
			manager:'all',
			status:'all'
		},
		onMobile = false;
		
	// check contact settings
	function setupContactSystem(callback){

		function createClientTypes(infoTypes, callback){
			
			sb.data.db.obj.getAll('company_categories', function(clientTypes){

				if(clientTypes.length == 0){
					
					var newClientTypes = [
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Client'
							},
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Vendor'
							}
						];
					
					sb.data.db.obj.create(
						'company_categories',
						newClientTypes,
						function(created){
							
							callback(created);
							
						}
					);
					
				}else{
					
					callback(clientTypes);
					
				}
				
			});
			
		}
		
		function createContactTypes(infoTypes, callback){
			
			sb.data.db.obj.getAll('contact_types', function(contactTypes){

				if(contactTypes.length == 0){
					
					sb.data.db.obj.create(
						'contact_types',
						{
							available_types:_.pluck(infoTypes, 'id'),
							name:'Contact',
							states:[
								{
									color:'green',
									icon:'check',
									isEntryPoint:1,
									name:'Active',
									next:["2"],
									previous:[],
									uid:1
								},
								{
									color:'grey',
									icon:'ban',
									name:'Inactive',
									next:[],
									previous:["1"],
									uid:2
								}
							]
						},
						function(created){
							
							callback(created);
							
						}
					);
					
				}else{
					
					callback(contactTypes);
					
				}
				
			});
			
		}
		
		function createInfoTypes(callback){
			
			sb.data.db.obj.getAll('contact_info_types', function(infoTypes){
				
				if(infoTypes.length == 0){
					
					//create the default info types
					var newInfoTypes = [
							{
								data_type:'email',
								is_address:'',
								name:'Email',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'phone',
								is_address:'',
								name:'Phone',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'address',
								is_address:true,
								name:'Default Address',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'website',
								is_address:'',
								name:'Website URL',
								object_bp_type:'"contact_info_types"'
							}
						];
						
					sb.data.db.obj.create('contact_info_types', newInfoTypes, function(created){
						
						callback(created);
						
					});
					
				}else{
					
					callback(infoTypes);
					
				}
				
			});
			
		}
		
		createInfoTypes(function(infoTypes){
			
			createContactTypes(infoTypes, function(contactTypes){
				
				createClientTypes(infoTypes, function(clientTypes){
					
					callback(true);
					
				});
								
			});
			
		});
				
	}
	
	
	// creates all of the initial dom objects and components, regardless of the view instatiation	
	function createUI(domObj){

		components.table = sb.createComponent('crud-table');
		//components.tags = sb.createComponent('tags');
		//components.notes = sb.createComponent('notes2');
		//components.tasks = sb.createComponent('tasks2');
		components.contactInfo = sb.createComponent('contact_info');
		//components.payments = sb.createComponent('paymentMethods');
		//components.clients = sb.createComponent('companyComponent');
		components.invoices = sb.createComponent('invoicesComponent');
		
		if(appConfig.instance == 'thelifebook'){
			//components.requests = sb.createComponent('requestsComponent');
		}

		if(appConfig.instance == 'thelifebookno' || appConfig.instance == 'thelifebookau'){
			components.lifebookinternational = sb.createComponent('lifebookinternational-component');
		}
		
		if(sb.sys.state.components.serviceAgreements){
			components.sa = sb.createComponent('serviceAgreements');
		}
		
		if(sb.sys.state.components.eventsComponent){
			//components.events = sb.createComponent('eventsComponent');
		}
		//components.tags = sb.createComponent('tags');
		if(sb.sys.state.components.tags){
			components.tags = sb.createComponent('tags');
		}
		
		ui = sb.dom.make(domObj.selector);
			
		tableUI = ui.makeNode('table', 'column', {width:12});
		tableUI.state = tableState;
		
		ui.build();
		
	}
	
	function createContactState(dom, state, draw){

		function form_ui(ui, state, draw){

			ui.makeNode('panel', 'div', {css:''})
				.makeNode('title', 'div', {size: 'small', text: 'Create A Contact', css:'ui huge header'});
			
			ui.panel.makeNode('break', 'lineBreak', {});
			
			ui.panel.makeNode('body', 'div', {});
			
			setupContactSystem(function(done){
				
				sb.data.db.obj.getAll('users', function(users){
					sb.data.db.obj.getAll('contact_types', function(types){

						var formObj = {
								fname:{
									type:'text',
									name:'fname',
									label:'First Name'
								},
								lname:{
									type:'text',
									name:'lname',
									label:'Last Name'
								},
								type:{
									type:'select',
									name:'type',
									label:'Type',
									options:[]
								},
								manager:{
									type:'select',
									name:'manager',
									label:'Manager',
									options:[
										{
											value:0,
											name:'Please Select'
										}
									]
								}
							};
						
						_.each(types, function(t){
							
							formObj.type.options.push({
								value:t.id,
								name:t.name
							});
							
						});
							
						_.each(users, function(u){
							
							formObj.manager.options.push({
								value:u.id,
								name:u.fname +' '+ u.lname
							});
							
						});	
						
						ui.panel.body.makeNode('cont', 'div', {css:'pda-container'});
						
						ui.panel.body.cont.makeNode('form', 'form', formObj);
	
						ui.panel.body.cont.makeNode('formBreak', 'div', {text:'<br />'});
	
						ui.panel.makeNode('header', 'div', {})
							.makeNode('btns', 'div', {css:'ui buttons'});
						
						ui.panel.header.btns.makeNode('save', 'button', {css:'pda-btn-green', text:'Save and continue'}).notify('click', {
							type:'create-contact-from-form',
							data:{
								form:ui.panel.body.cont.form,
								dom:ui,
								state:state
							}
						}, sb.moduleId);	
						
						ui.patch();
																			
					});
				});
				
			});
			
		}
				
		dom.empty();
		
		dom.makeNode('message', 'div', {css:'ui icon massive message'});
		dom.message.makeNode('icon', 'div', {tag:'i', css:'exclamation circle icon'});
		dom.message.makeNode('content', 'div', {css:'content'});
		dom.message.content.makeNode('text1', 'div', {text:'You can create a new contact from within a company.', css:'header'});
		dom.message.content.makeNode('text2', 'div', {
			text:'Go to your Companies',
			css:'ui teal button',
			tag:'a',
			href:window.location.href.split('#')[0] +'#mycompanies'
		});

		dom.patch();
								
	}
		
	function eraseContacts(objs, dom){

		function performDelete(dom, objs, contacts, deleteContacts){
			
			function deleteObjs(objectType, objs, callback){
				
				sb.data.db.obj.erase(objectType, _.pluck(objs, 'id'), function(response){
					
					callback(response);
				
				});
				
			}
			
			dom.footer.btns.empty();
			
			dom.footer.btns.makeNode('finishing', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Finishing up', css:'pda-btn-primary'});
			
			dom.footer.btns.patch();
			
			deleteObjs('contacts', objs, function(done){
				
				dom.hide();
				
				sb.notify({
					type:'app-navigate-to',
					data:{
						itemId:'contacts',
						viewId:'table'
					}
				});
				
			});
			
		}
		
		dom.body = sb.dom.make(dom.body.selector),
		dom.footer = sb.dom.make(dom.footer.selector);
		
		dom.body.makeNode('title', 'headerText', {text:'Delete '+ objs.length +' contacts?', size:'small'});		
		
		dom.footer.makeNode('btns', 'buttonGroup', {});
		
		dom.footer.btns.makeNode('yes', 'button', {text:'<i class="fa fa-check"></i> Yes', css:'pda-btn-green'}).notify('click', {
			type:'companyComponent-run',
			data:{
				run:function(dom, objs){
					
					dom.footer.btns.yes.text('<i class="fa fa-circle-o-notch fa-spin"></i> Loading');
					dom.footer.btns.yes.css('pda-btn-primary');
					dom.footer.btns.no.css('pda-btn-disabled');
					
					performDelete(dom, objs, [], false);
					
				}.bind(this, dom, objs)
			}
		}, sb.moduleId);
		dom.footer.btns.makeNode('no', 'button', {text:'<i class="fa fa-times"></i> No', css:'pda-btn-red'});
		
		dom.body.build();
		dom.footer.build();
		
	}
	
	function homeState(dom, statsPage){
		
		function initialView(dom){

			dom.makeNode('col1', 'column', {width: 12});
			//dom.col1.makeNode('title', 'headerText', {css:'', text:'Contacts Dashboard', size:''});
			dom.col1.makeNode('btns', 'buttonGroup', {css:'pull-left'});
			dom.col1.makeNode('break', 'lineBreak', {});

			dom.col1.makeNode('col1', 'column', {width:12});				
			dom.col1.col1.makeNode('cont', 'container', {css:'pda-container'});
			
			dom.col1.btns.makeNode('viewContacts', 'button', {text:'<i class="fa fa-users"></i> All Contacts', css:'pda-btn-blue'}).notify('click', {
				type:'contactComponent-run',
				data:{
					run:function(tableUI){
						
						components.table.notify({
							type:'crud-table-back-to-table',
							data:{}
						});
																											
					}.bind(this, tableUI)
				}
			}, sb.moduleId);
			
			dom.col1.btns.makeNode('viewClients', 'button', {text:'<i class="fa fa-building-o"></i> All Clients', css:'pda-btn-blue'}).notify('click', {
				type:'contactComponent-run',
				data:{
					run: function(){
						
						sb.notify({
							type:'app-change-page',
							data:{
								to:'companies'
							}
						});

					}.bind(dom)
				}
			}, sb.moduleId);
			
			dom.col1.btns.makeNode('create', 'button', {css: 'pda-btn-green', text: '<i class="fa fa-plus"></i> Create Contact'}).notify('click', {
				type: 'contactComponent-run',
				data: {
					run: function(){
						
						createContactState({}, this);
						
					}.bind(dom)
				}
			});
			
/*
			dom.col1.btns.makeNode('createClient', 'button', {css: 'pda-btn-green', text: '<i class="fa fa-plus"></i> Create Client'}).notify('click', {
				type: 'contactComponent-run',
				data: {
					run: function(){
						
						sb.notify({
							type:'app-change-page',
							data:{
								to:'companies'
							}
						});

					}
				}
			});
*/
			
			dom.col1.btns.makeNode('upload', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-upload" aria-hidden="true"></i> Upload contacts csv'})
				.notify('click', {
					type:'contactComponent-run',
					data: {
						run: function(){
							
							uploadCsvState(this);
							
						}.bind(dom)
					}
				}, sb.moduleId);

/*
			dom.col1.btns.makeNode('viewStats', 'button', {text:'<i class="fa fa-bar-chart"></i> Stats', css:'pda-btn-primary'}).notify('click', {
				type:'contactComponent-run',
				data:{
					run:homeState.bind(this, dom, true)
				}
			}, sb.moduleId);
*/
			
			dom.col1.col1.cont.makeNode('tableTitle', 'headerText', {css:'text-left', text:'New Contacts', size:'x-small'});
			dom.col1.col1.cont.makeNode('info', 'container', {});
			dom.col1.col1.cont.info.makeNode('loading', 'text', {text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
						
			dom.patch();
			
			sb.data.db.obj.getAll('contacts', function(con){

				delete dom.col1.col1.cont.info.loading;

				dom.col1.col1.cont.info.makeNode('table', 'table', {
					css: 'table-hover table-condensed table-responsive',
					columns: {
						btns: '',
						fname: 'First Name',
						lname: 'Last Name',
						type: 'Type',
						company: 'Company'
					}
				});	
				
				_.each(con.data, function(contact){

					var contactCompInfo;
					
					if (contact.company != null){
						
						contactCompInfo = contact.company.name;
						
					} else {
						
						contactCompInfo = '<strong><em>No Company Data on File</em></strong>';
					}
					
					dom.col1.col1.cont.info.table.makeRow(
						'row-' + contact.id, 
						['', contact.fname, contact.lname, contact.type.name, contactCompInfo]);
						
					dom.col1.col1.cont.info.table.body['row-' + contact.id].btns.makeNode('button', 'button', {css: 'pda-transparent', text: '<p class="text-center"><i class="text-center fa fa-eye fa-2x"></i></p>'}).notify('click', {
						type: 'contactComponent-run',
						data: {
							run: function(contact){
					
								components.table.notify({
									type:'crud-table-row-link-clicked',
									data:{
										object: contact,
										type: 'tab'
									}
								});
																													
							}.bind(this, contact)
						}
					});
					
				});			

				dom.col1.col1.cont.info.patch();

			}, 2, {
				page:0,
				sortCol:'date_created',
				sortDir:'desc',
				pageLength:10
			});
		}
		
		function statsView(dom){
			
			dom.contacts.empty();

			dom.contacts.makeNode('stats', 'container', {css:'pda-container pda-Panel pda-panel-blue'});

			dom.contacts.stats.makeNode('btns', 'buttonGroup', {css:'pull-right'});
			dom.contacts.stats.btns.makeNode('back', 'button', {text:'Close <i class="fa fa-times"></i>', css:'pda-btn-x-small pda-btn-red'}).notify('click', {
				type:'eventComponent-run',
				data:{
					run:homeState.bind(this, dom, false)
				}
			}, sb.moduleId);
			
			dom.contacts.stats.makeNode('header', 'headerText', {text:'Stats'});
		}
				
		if (statsPage === true) { 
			
			statsView(dom);
			
		} else {
			
			initialView(dom);
		}		
													
	}
	
	function searchForAContact(dom, state, draw){

		function performSearch(term, callback){
			
			sb.data.db.obj.getWhere('contacts', {
				fname:{
					type:'contains',
					value:term
				},
				childObjs:2
			}, function(fnameResults){
				
				sb.data.db.obj.getWhere('contacts', {
					lname:{
						type:'contains',
						value:term
					},
					childObjs:2
				}, function(lnameResults){
					
					var results = fnameResults.concat(lnameResults);
										
					callback(_.uniq(results, function(contact){
						return contact.id;
					}));	
				
				});			
				
			});
			
		}
		
		dom.empty();
		
		dom.makeNode('title', 'div', {text:'Search for a contact', css:'ui large header'});
		
		dom.makeNode('form', 'div', {css:'ui huge fluid action input'});
		
		dom.form.makeNode('input', 'div', {tag:'input', type:'text'});		
		dom.form.makeNode('btn', 'div', {text:'Search', css:'ui green button'})
			.notify('click', {
				type:'contactComponent-run',
				data:{
					run:function(dom, state, draw){
						
						dom.form.btn.loading();
						dom.results.css('ui basic loading segment');
						
						var term = $(dom.form.input.selector).val();
												
						if(term.length < 3){
							sb.dom.alerts.alert('', 'Type more to begin search.', 'error');
							return;
						}
						
						performSearch(term, function(results){
							
							dom.results.makeNode('count', 'div', {text:results.length + ' total results', css:'ui small header'});
							
							dom.results.makeNode('table', 'table', {
								clearCSS:true,
								css:'ui stackable table',
								columns:{
									select:'',
									name:'Name'
								},
								columnCSS:['two wide', 'fourteen wide']
							});
							
							_.each(results, function(contact){
								
								var company = '';
								if(contact.company){
									company = '<small> at '+contact.company.name+'</small>';
								}
								
								dom.results.table.makeRow(
									'row-'+contact.id,
									[
										'',
										'<h2>'+contact.fname +' '+ contact.lname + company+'</h2>'
									]
								);
								
								dom.results.table.body['row-'+contact.id].select.makeNode('select', 'div', {text:'Select', css:'ui green button'});
								
								dom.results.table.body['row-'+contact.id].select.select.notify('click', {
									type:'contactComponent-run',
									data:{
										run:function(dom, state, draw, contact){
											
											dom.results.table.body['row-'+contact.id].select.select.loading();
											
											sb.data.db.obj.update('groups', {id:state.project.id, main_contact:contact.id}, function(project){
												
												singleState(contact, dom, state, draw);
												
											});
											
										}.bind({}, dom, state, draw, contact)
									}
								}, sb.moduleId);
								
							});
							
							dom.results.patch();
							dom.results.css('ui basic segment');
							dom.form.btn.loading(false);					
							
						});
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
		
		dom.makeNode('formBreak', 'div', {text:'<br />'});	
		dom.makeNode('results', 'div', {css:'ui basic segment'});
		
		draw(dom);
		
	}
	
	function singleState(obj, dom, state, draw){

		function editContact(dom){

			state.editing = true;
			var onBack = function(){
				
				sb.notify({
					type: 'app-redraw',
					data: {}
				}, sb.moduleId);
				
			};
			
			function update(data, company){

				data.dom.btns.save.loading();
							
				var form = data.form.process().fields,
					formIsComplete = data.form.process();

				if(formIsComplete.completed === false){
					
					data.dom.btns.save.loading(false);
					
					sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
					
					return
					
				}
				
				var contact = {
						id: +form.id.value,
						fname:form.fname.value,
						lname:form.lname.value,
						manager: +form.manager.value,
						sales_person: +form.sales_person.value,
						type: +form.type.value
					},
					contactInfo = [];
				var updCompany;	
	
				if(data.contactInfoForm){
				
					contactInfoForm = data.contactInfoForm.process().fields;
	
					_.each(data.object.contact_info, function(info){
						
						if(info){
							
							if(info.street){
								
								var formValues = data.addressForms['cont-'+info.id].cont.form.process().fields
	
								contactInfo.push({
									id:info.id,
									info:formValues['street-'+info.id].value,
									street:formValues['street-'+info.id].value,
									city:formValues['city-'+info.id].value,
									state:formValues['state-'+info.id].value,
									zip:formValues['zip-'+info.id].value,
									country:formValues['country-'+info.id].value
								});
								
							}else{
								
								if(contactInfoForm['info-'+info.id]){
									contactInfo.push({
										id:info.id,
										info:contactInfoForm['info-'+info.id].value
									});	
								}
								
							}
							
						}
						
					});
					
				}

				if(!_.isNull(company) && !_.isUndefined(company)){

					updCompany = company;

					if(!(company.name === form.company.value)){
						updCompany.name = form.company.value;
					}
					
					contact.company = updCompany;
					
				}
			
				sb.data.db.obj.update('contact_info', contactInfo, function(updatedInfo){	
					
					sb.data.db.obj.update('companies', updCompany, function(updatedCompany){
					
						sb.data.db.obj.update('contacts', contact, function(updated){
	
							updated.contact_info = updatedInfo;
							updated.company = updatedCompany;
							
							obj = updated;
							
							if(data.state){
								data.state.object = updated;
							}
							
							//if(+sb.data.cookie.userId == 11){
							if(appConfig.instance == 'thelifebook'){
								
								var mailchimpMember = {
										listId:'5983ed1a31',
										member:{
											email_address:'',
											status:'subscribed',
											merge_fields:{
												FNAME:obj.fname,
												LNAME:obj.lname,
												SHIPPING:{},
												PHONE:'',
												CHURCHPHON:'',
												CHURCHADDR:{},
												ADULTS:0,
												STUDENTS:0,
												REQUESTS:1,
												TYPE:obj.type.name,
												DENOM:'',
												ZIP:'',
												MANAGER:obj.manager.fname +' '+ obj.manager.lname,
												RECENT:'',
												RECENTSIZE:0
											}
										}
									};
									
								_.each(obj.contact_info, function(info){
									
									if(info.is_primary == 'yes'){
										
										switch(info.type.data_type){
											
											case 'other':
												
												switch(info.title){
													
													case 'Adults':
														
														mailchimpMember.member.merge_fields.ADULTS = +info.info;
														
														break;
														
													case 'Students':
														
														mailchimpMember.member.merge_fields.STUDENTS = +info.info;
														
														break;
														
													case 'Denomination':
														
														mailchimpMember.member.merge_fields.DENOM = info.info;
														
														break;		
													
												}
											
												break;
																						
											case 'email':
											
												mailchimpMember.member.email_address = info.info;
											
												break;
																																
											case 'address':
												
												if(info.name == 'Shipping Address'){
													
													//orderObj.order.customer.merge_fields.SHIPPING = info.street +', '+ info.city +', '+ info.state +' '+ info.zip;
													mailchimpMember.member.merge_fields.SHIPPING.addr1 = info.street;
													mailchimpMember.member.merge_fields.SHIPPING.addr2 = '';
													mailchimpMember.member.merge_fields.SHIPPING.city = info.city;
													mailchimpMember.member.merge_fields.SHIPPING.state = info.state;
													mailchimpMember.member.merge_fields.SHIPPING.zip = info.zip;
													
													
													mailchimpMember.member.merge_fields.ZIP = info.zip;
												}
												
												if(info.name == 'Church Address'){
													
													//orderObj.order.customer.merge_fields.CHURCHADDR = info.street +', '+ info.city +', '+ info.state +' '+ info.zip;
													mailchimpMember.member.merge_fields.CHURCHADDR.addr1 = info.street;
													mailchimpMember.member.merge_fields.CHURCHADDR.addr2 = '';
													mailchimpMember.member.merge_fields.CHURCHADDR.city = info.city;
													mailchimpMember.member.merge_fields.CHURCHADDR.state = info.state;
													mailchimpMember.member.merge_fields.CHURCHADDR.zip = info.zip;
													
												}
											
												break;	
												
											case 'phone':
												
												if(info.name == 'Cell Phone'){
													
													mailchimpMember.member.merge_fields.PHONE = info.info;
													
												}
												
												if(info.name == 'Church Phone'){
													
													mailchimpMember.member.merge_fields.CHURCHPHON = info.info;
													
												}
											
												break;		
											
										}
									
									}
									
								});	
		
								sb.data.db.controller('updateMailchimpListMember', mailchimpMember, function(done){
									
									if(data.draw){
										singleState(updated, data.dom, data.state, data.draw);
									}else{
										sb.notify({
											type:'app-redraw',
											data:{}
										});
									}			
									
								});
								
							}else{
								
								if(data.state.hasOwnProperty('appSettings') && data.state.appSettings.apps.integrations.mailchimp){
									
									if(data.state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){
									
										sb.data.db.controller('updateSingleContactWithMailchimp', {id:updated.id}, function(done){
											
											if(done.status == 404){
																							
												if(data.draw){
													singleState(updated, data.dom, data.state, data.draw);
												}else{
													sb.notify({
														type:'app-redraw',
														data:{}
													});
												}
												
											}else{
												
												if(data.draw){
													singleState(updated, data.dom, data.state, data.draw);
												}else{
													sb.notify({
														type:'app-redraw',
														data:{}
													});
												}
												
											}
											
										});
									
									}else{
										
										if(data.draw){
											singleState(updated, data.dom, data.state, data.draw);
										}else{
											sb.notify({
												type:'app-redraw',
												data:{}
											});
										}
										
									}
									
								}else{
		
									if(data.state.hasOwnProperty('onSave')){
	
										data.state.object = updated;
										
										data.state.onSave.call(null, updated, data.dom, data.state, data.draw);
										
									} else {
																
										sb.notify({
											type:'app-redraw',
											data:{}
										});
									}
											
								}
								
							}
													
						}, 2);
					
						
					});
									
				}, 2);			
			}			
			
			if(state.hasOwnProperty('onBack')){
				state.editing = false;
				onBack = state.onBack.bind(null, obj, dom, state, draw);
			}	
			
			dom.empty();

			dom.makeNode('header', 'div', {text: 'Editing '+ state.object.fname +' '+ state.object.lname, css:'ui huge header'});

			dom.makeNode('btns', 'div', {css:'ui buttons'}).makeNode('back', 'button', {css:'pda-btnOutline-red', text:'<i class="fa fa-times"></i> Close'}).notify('click', {
				type:'contactComponent-run',
				data:{
					run:onBack
				}
			}, sb.moduleId);
			
			dom.makeNode('btnBreak', 'div', {text:'<br />'})
			
			dom.makeNode('cols', 'div', {css:'ui stackable grid'});
			
			dom.cols.makeNode('col1', 'column', {w:8});
			dom.cols.makeNode('col2', 'column', {w:8});
			dom.cols.makeNode('col3', 'column', {w:16});
			
			dom.cols.col1.makeNode('header', 'div', {text:'Basic Info', css:'ui medium header'});
			dom.cols.col1.makeNode('cont', 'div', {css:'ui orange fluid card'})
				.makeNode('cont', 'div', {css:'content'});

			dom.cols.col2.makeNode('header', 'div', {text:'Contact Info', css:'ui medium header'});
			dom.cols.col2.makeNode('cont', 'div', {css:'ui orange fluid card'})
				.makeNode('cont', 'div', {css:'content'});
				
			dom.cols.col3.makeNode('header', 'div', {text:'Address Info', css:'ui medium header'});
			dom.cols.col3.makeNode('col', 'div', {css:'ui stackable fluid cards'});
									
			dom.makeNode('endBreak', 'div', {text:'<br /><br />'});
																	
			dom.patch();
			
			sb.data.db.obj.getAll('users', function(users){

				sb.data.db.obj.getAll('contact_types', function(types){	

					sb.data.db.obj.getWhere('contact_info', {object_id:state.object.id, childObjs:3}, function(contactInfo){

						state.object.contact_info = contactInfo;
						
						var managerId = 0;
						var salesId = 0;
						var typeId = 0;
						var selectedCompany;
						var companyName = '';			

						
						if(state.object.manager){
							managerId = state.object.manager.id;
						}
						
						if(state.object.sales_person){
							salesId = state.object.sales_person.id;
						}
						
						if(state.object.type){
							typeId = state.object.type.id;
						}

						if(state.object.company){
							selectedCompany = state.object.company;
							if(selectedCompany.hasOwnProperty('name')){
								companyName = selectedCompany.name;
							}
						}
											
						var formObj = {
								fname:{
									type:'text',
									name:'fname',
									label:'First Name',
									value:state.object.fname
								},
								lname:{
									type:'text',
									name:'lname',
									label:'Last Name',
									value:state.object.lname
								},
								type:{
									type:'select',
									name:'type',
									label:'Type',
									options:[],
									value:typeId
								},
								sales_person:{
									type:'select',
									name:'sales_person',
									label:'Sales Person',
									options:[],
									value:salesId
								},
								manager:{
									type:'select',
									name:'manager',
									label:'Manager',
									options:[],
									value:managerId
								},
								company:{
									type:'text',
									name:'company',
									label:'Company',
									value:companyName
								},
								id:{
									type:'hidden',
									name:'id',
									value:state.object.id
								}
							},
							newManagerOptions = [];

						formObj.sales_person.options.push({
							value:0,
							name:'Select One'
						});	
							
						_.each(types, function(t){

							if (t) {
								
								var add = {
										value:t.id,
										name:t.name
									};
																	
								formObj.type.options.push(add);
								
							}
							
						});
						
						var adding = [];
						var salesManagers = [];
						
						_.each(users, function(u){
							
							var add = {
									value:u.id,
									name:u.fname +' '+ u.lname
								},
								salesManager = {
									value:u.id,
									name:u.fname +' '+ u.lname
								};

							if(add.name == 'undefined' || add.name == 'undefined undefined' || add.name == undefined || add.name == null){
								
								//add.name = 'Not Selected';
								
							}
	
							if(state.object.manager){
							
/*
								if(u.id == state.object.manager.id){

									add.selected = true;
									
								}
*/
								
/*
								if(appConfig.instance == 'thelifebook'){
									
									if(u.id == state.object.manager.related_object.id){

										add.selected = true;
										
									}
									
								}
*/
							
							}
	
							if(state.object.sales_person){
							
								if(u.id == state.object.sales_person.id){

									salesManager.selected = true;
									
								}
							
							}
							
							adding.push(add);
							salesManagers.push(salesManager);
							
						});

						formObj.manager.options = formObj.manager.options.concat(adding);
						formObj.sales_person.options = formObj.sales_person.options.concat(salesManagers);

						if(_.find(formObj.manager.options, {name: 'Not Selected'})){
							
							newManagerOptions = _.chain(formObj.manager.options)
								.filter(function(opt){return opt.name != 'Not Selected'})
								.sortBy('name')
								.value();
							
							newManagerOptions.unshift(_.find(formObj.manager.options, {name:'Not Selected'}));
														
						}else{
							
							newManagerOptions = _.sortBy(formObj.manager.options, 'name');
														
						}
												
						formObj.manager.options = newManagerOptions;
																		
						if(state.object.contact_info){
							
							var infoForm = {},
								addressForms = [];
							
							_.each(state.object.contact_info, function(o){
	
								if(o){
									
									if(o.street){
										
										var stateVal = _.findWhere(sb.data.stateArray, {name:o.state});
										if(!stateVal){
											stateVal = _.findWhere(sb.data.stateArray, {value:o.state});
										}
										
										if(!stateVal){
											stateVal = sb.data.stateArray[0];
										}
										
										addressForms.push(
											{
												infoId:o.id,
												info:o,
												form:{
													street:{
														type:'text',
														name:'street-'+o.id,
														label:'Street',
														value:o.street
													},
													street2:{
														type:'text',
														name:'street2-'+o.id,
														label:'Apartment/Unit Number',
														value:o.street2
													},
													city:{
														type:'text',
														name:'city-'+o.id,
														label:'City',
														value:o.city
													},
													state:{
														type:'state',
														name:'state-'+o.id,
														label:'State',
														value:stateVal.value
													},
													zip:{
														type:'text',
														name:'zip-'+o.id,
														label:'Zip',
														value:o.zip
													},
													country:{
														type:'text',
														name:'country-'+o.id,
														label:'Country',
														value:o.country
													}
													
												}
											}
										);
										
									}else{
	
										if(o.type){
											
											if(o.is_primary == 'yes'){
												var primary = ' (primary)';
											}else{
												var primary = '';
											}
																						
											infoForm['info-'+o.id] = {
												type:'text',
												name:'info-'+o.id,
												label:o.type.name + primary,
												value:o.info
												
											};
										}												
										
									}
									
								}			
								
							});
							
						}				
																			
						dom.cols.col1.cont.cont.makeNode('form', 'form', formObj);
						
						if(state.object.contact_info){
							if(state.object.contact_info.length > 0){
								dom.cols.col2.cont.cont.makeNode('form', 'form', infoForm);
							}else{
								dom.cols.col2.cont.cont.makeNode('noInfo', 'div', {css:'ui small header', text:'No contact info'});
							}
						}else{
							dom.cols.col2.cont.cont.makeNode('noInfo', 'div', {css:'ui small header', text:'No contact info'});
						}
						
						if(addressForms.length > 0){
							
							_.each(addressForms, function(form){
								
								var primaryText = '';
								if(form.info.is_primary == 'yes'){
									primaryText = 'Primary ';
								}
								
								dom.cols.col3.col.makeNode('cont-'+form.info.id, 'div', {css:'ui orange fluid card'})
										.makeNode('cont', 'div', {css:'content'});

								dom.cols.col3.col['cont-'+form.info.id].cont.makeNode('header-'+form.infoId, 'div', {css:'ui small header', text:primaryText+form.info.type.name});
								
								dom.cols.col3.col['cont-'+form.info.id].cont.makeNode('form', 'form', form.form);
								
							});
							
						}else{
							
							dom.cols.col3.col.makeNode('cont', 'div', {css:'ui orange fluid card'})
								.makeNode('cont', 'div', {css:'content'})
									.makeNode('noInfo', 'div', {text:'No address info', css:'ui small header'});
							
						}
																		
						dom.cols.col1.cont.cont.form.makeNode('changeCompany', 'div', {
							css: 'ui fluid search item field',
							text:
								/*'<label>Company</label>'+ */
								'<div class="ui icon fluid input">'+
									'<input class="prompt" type="text" placeholder="Select New Company" style="border-radius:.28571429rem;">'+
									'<i class="search icon"></i>'+
								'</div>'+
								'<div class="results"></div>',
							listener:{
								type: 'search',
								objectType: 'companies',
								onSelect: function(result, response){

									if (!_.isEmpty(result)) {

										selectedCompany = result;
										dom.cols.col1.cont.cont.form.company.update({value:result.name});
										
									}
									
								},
								category: 'type',
								selection:{
									name:true,
									type:{
										name:true
									}
								},
								onResponse: function(raw){

								    var response = {
									    results : {}
								    };
								    
								    _.each(raw.results, function(item){

								    		///null undefined check for company catagory
										if(!_.isNull(item.type) && !_.isUndefined(item.type)){
										
											///push to results group else create new
										    if ( response.results.hasOwnProperty(item.type.id) ) {
											
											    response.results[item.type.id].results.push(item);
										    
										    }else{
											    
											    response.results[item.type.id]
											    	
											    	= {
												    	name : item.type.name,
												    	results : [item]
											    	};
											    	
										    }										
											
										} else {
											
											///company does not have a type. push to none else create
										    if ( response.results.hasOwnProperty('none') ) {
											
											    response.results['none'].results.push(item);
										    
										    }else{
											    
											    response.results['none']
											    	
											    	= {
												    	name : '&#160&#160',
												    	results : [item]
											    	};
											    	
										    }
											
										}
																	    
								    });

								    return response;
								    
							    }
							}
						});
	
						dom.btns.makeNode('save', 'button', {css:'pda-btn-green', text:'<i class="fa fa-check"></i> Save'}).notify('click', {
							type:'contactComponent-run',
							data:{
								run:function(){
									
									update({
										form:dom.cols.col1.cont.cont.form,
										contactInfoForm:dom.cols.col2.cont.cont.form,
										addressForms:dom.cols.col3.col,
										object:state.object,
										dom:dom,
										state:state,
										draw:draw
									}, selectedCompany)
																		
								}
							}						
							
						}, sb.moduleId);	
																		
						dom.patch();
						
					});
				
				}, {
					name:true
				});
			
			}, {
				fname:true, 
				lname:true
			});
			
		}
		
		var typeString = '';
		var companyString = '';
		var denominationString = '';
		var managerString = '';
		var salespersonString = '';
		var locationLine = '';
		var editButton = true;
		var googleQuery = {
			fname:obj.fname,
			lname:obj.lname,
			company:'',
			location:''
		};
		var collapse = true;
		
		if($(window).width() <= 768){
			collapse = 'closed';
		}

		state.editing = false;
		var object = state.object = obj;

		if(state.hasOwnProperty('edit'))
			editButton = state.edit;
		
		if(obj.type){
			if(obj.type.hasOwnProperty('name')){
				
				typeString = obj.type.name;
			
			}
		}
		
		var locations = [],
			denomination = [];

		if(obj.company){

			if(obj.company.hasOwnProperty('name')){
				
				companyString = obj.company.name;
				
				googleQuery.company = obj.company.name;
								
				if(obj.contact_info){
					
					var info = obj.contact_info.filter(function( element ) {
						   return element != null;
						});
					                        
                        _.each(info, function(i){
                            
                            if(i.type.id == 53 || i.type.data_type == 'address'){
                                locations.push(i);
                            }
                            
                            if(i.type.data_type == 'address' && appConfig.instance != 'thelifebook'){
                                locations.push(i);
                            }
                            
                        });
 
                        locations = locations.sort(function(a,b) { 
                                return b.id - a.id;
                            });
                                            
                        if(locations.length > 0){
                            locationLine = '<small>, '+ locations[0].city +', '+ locations[0].state +'</small>';
                            googleQuery.location = ' '+ locations[0].city +', '+ locations[0].state;
                        }    
                        
                        
                    // does the contact have a primary address?
                    
                    // does the contact have a primary denomination?
                    _.each(info, function(i){
                            
                            if(i.type.id == 1354){
                                denomination.push(i);
                            }
                            
                        });
                        
                    denomination = denomination.sort(function(a,b) { 
                        return b.id - a.id;
                    });	
					
				}
				
			}
			
			if(obj.company.hasOwnProperty('tax_exempt') && obj.company.tax_exempt == true){
												
				dom.panel.body.name.makeNode('taxlabel', 'label', {text: 'Tax Exempt', color: 'gray'});
				
			}
		
		}

		if(obj.sales_person){
			if(obj.sales_person.hasOwnProperty('fname')){
				salespersonString = '<br /><small><small>Sales Person:</small> '+ obj.sales_person.fname +' '+ obj.sales_person.lname +'</small>';
			}
		}
		
		if(obj.manager){
			if(obj.manager.hasOwnProperty('fname')){
				managerString = obj.manager.fname +' '+ obj.manager.lname;
			}
		}

		if(denomination.length > 0){
			
			denominationString = '<br /><small><small>Denomination</small> '+ denomination[0].info +'</small>';
						
		}
		
		dom.empty();

		dom.makeNode('alertContainer', 'column', {css:''});
		
		dom.makeNode('cont', 'div', {css:'ui stackable grid'});
		
		dom.cont.makeNode('name', 'div', {css:'sixteen wide column'});
		dom.cont.makeNode('left', 'div', {css:'four wide column'});
		dom.cont.makeNode('right', 'div', {css:'twelve wide column'});
		
		dom.cont.name.makeNode('btnCont', 'div', {css:'right floated'});
		
		dom.cont.name.makeNode('name', 'div', {
			css: 'mobilePadding',
			text: '<h1>' + obj.fname +' '+ obj.lname + '</h1>'
		});
		
		dom.cont.name.makeNode('company', 'div', {
			css: 'mobilePadding link',
			text: '<a><h3>' + typeString + ' <small>at</small> ' + companyString + locationLine + salespersonString + '</h3></a>'
		}).notify('click', {
			type: 'contactComponent-run',
			data: {
				run: function(data) {
					
					sb.notify({
						type:'app-navigate-to',
						data:{
							itemId: 'companies',
							viewId: {
								id: 'single-' + obj.company.id,
								type: 'table-single-item',
								parent: 'table',
								title: obj.company.name,
								rowObj: obj.company,
								icon: '<i class="building icon"></i>',
								removable: true,
								viewState: state
							}
						}
					});
					
				}
			}
		}, sb.moduleId);
		
		dom.cont.name.makeNode('manager', 'div', {
			css: 'mobilePadding',
			text: '<h3><small>Manager:</small> ' + managerString + denominationString + '</h3>'
		});
		
		dom.cont.name.makeNode('lb1', 'lineBreak', {
			spaces: 1
		});
		
		dom.cont.name.makeNode('stateBtn', 'div', {css: 'basic clearing segment mobilePadding'});
		
		dom.cont.name.makeNode('sp', 'lineBreak', {});
		
		dom.cont.name.makeNode('tags', 'div', {css:'basic clearing segment mobilePadding'});
		
		if(editButton){
					
			dom.cont.name.btnCont.makeNode('edit', 'div', {
				text:'<i class="large ellipsis horizontal circular blue icon"></i>', 
				style:'float:right;', 
				css:'ui right floated simple dropdown blue icon'
			}).makeNode('menu', 'div', {css:'left menu'});
			
			dom.cont.name.btnCont.edit.menu.makeNode('edit', 'div', {css:'item', text:'<i class="edit orange icon"></i> Edit Contact Details'}).notify('click', {
				type:'contactComponent-run',
				data:{
					run:editContact.bind(null, dom, state, draw)
				}
			}, sb.moduleId);
			
			if(state.project){
			
				dom.cont.name.btnCont.edit.menu.makeNode('change', 'div', {css:'item', text:'<i class="user times orange icon"></i> Remove from this project'}).notify('click', {
					type:'contactComponent-run',
					data:{
						run: function(data) { 
							
							var project = state.project;
							
							project.main_contact = 0;
							
							sb.data.db.obj.update('groups', project, function(updated) {
								
								sb.notify({
									type:'app-navigate-to',
									data:{
										type:'UP'
									}
								});
								
							}, 1);
							
						} 
					}
				}, sb.moduleId);
				
			}
			
			if(obj.hasOwnProperty('company') && obj.company != null){
								
				dom.cont.name.btnCont.edit.menu.makeNode('view', 'div', {css: 'item', text:'<i class="eye teal icon"></i> View Company'}).notify('click', {
					type: 'contactComponent-run',
					data: {
						run:function(){
							
							sb.notify({
								type:'app-navigate-to',
								data:{
									itemId:'companies',
									viewId:{
										viewState:{
											obj: obj.company
										}
									}
								}
							});						
							
						}
					}
				}, sb.moduleId);
				
			}

			dom.cont.name.btnCont.edit.menu.makeNode('search', 'div', {css:'item', text:'<i class="google blue icon"></i> Google Search'}).notify('click', {
				type:'search-in-google',
				data:{
					link:`http://www.google.com/search?q=${googleQuery.fname} ${googleQuery.lname} ${googleQuery.company} ${googleQuery.location}`
				}
			}, sb.moduleId);
				
			dom.cont.name.btnCont.edit.menu.makeNode('delete', 'div', {css:'item', text:'<i class="trash red icon"></i> Delete Contact'}).notify('click', {
				type:'delete-contact-object',
				data:{
					object:obj,
					state:{
						onDelete:function(){

							sb.notify({
								type:'app-navigate-to',
								data:{
									type:'UP'
								}
							});							
							
						}
					}
				}
			}, sb.moduleId);
			
		}
		
		
		dom.cont.left.makeNode('tasks', 'div', {});
		dom.cont.left.makeNode('paymentInfo', 'div', {});
		dom.cont.left.makeNode('contactInfo', 'div', {});
		
		//Projects	
		if(sb.sys.state.components.projects){
			dom.cont.right.makeNode('projects', 'div', {uiGrid:false});
			dom.cont.right.projects.makeNode('tableCont', 'container', {title:'<i class="fa fa-breifcase"></i> Projects', collapse:collapse, css:'pda-container'});
			//dom.cont.right.projects.makeNode('projectsBreak', 'lineBreak', {spaces:1});
		}
		
		if(
			appConfig.instance != 'thelifebookau' &&
			appConfig.instance != 'thelifebookno'
		){
			
			if(
				appConfig.instance == 'rickyvoltz' ||
				appConfig.instance == 'voltzsoftware'
			){
				
				if(sb.sys.state.components.instances){
			
					dom.cont.right.makeNode('instances', 'div', {width:12});
					dom.cont.right.instances.makeNode('tableCont', 'container', {title:'<i class="fa fa-th"></i> Account', collapse:collapse, css:''});
					dom.cont.right.instances.tableCont.makeNode('cont', 'column', {width:12});
					//dom.cont.right.makeNode('instancesBreak', 'lineBreak', {spaces:1});
					
				}
				
			}
				
			if(sb.sys.state.components.workorders){
				dom.cont.right.makeNode('workorders', 'div', {uiGrid:false});
				dom.cont.right.workorders.makeNode('tableCont', 'container', {title:'<i class="fa fa-files-o"></i> Work Orders', collapse:collapse, css:'pda-container'});
				//dom.cont.right.makeNode('workordersBreak', 'lineBreak', {spaces:1});
			}

			if(
				sb.sys.state.components.inventory &&
				(
					appConfig.instance == 'zachvoltz' ||
					appConfig.instance == 'infinity' ||
					appConfig.instance == 'dreamcatering' ||
					appConfig.instance == 'mcvh' ||
					appConfig.instance == 'infinityproduction' ||
					appConfig.instance == 'nashvilleeventbartending'
				)
			){
				dom.cont.right.makeNode('discounts', 'div', {uiGrid:false});
				dom.cont.right.discounts.makeNode('tableCont', 'container', {title:'<i class="edit outline icon"></i> Discounts', collapse:collapse, css:'pda-container'});
				//dom.cont.right.makeNode('discountsBreak', 'lineBreak', {spaces:1});
			}
			
			if(sb.sys.state.components.serviceAgreements){
				dom.cont.right.makeNode('serviceAgreements', 'div', {uiGrid:false});
				dom.cont.right.serviceAgreements.makeNode('tableCont', 'container', {title:'<i class="fa fa-files-o"></i> Service Agreements', collapse:'closed', css:'pda-container'});
				//dom.cont.right.makeNode('serviceAgreementsBreak', 'lineBreak', {spaces:1});
			}
			
		}
		
		if(appConfig.instance == 'thelifebook' || appConfig.instance == 'rickyvoltz'){
			dom.cont.right.makeNode('requestsComponent', 'div', {uiGrid:false});
			//dom.cont.right.makeNode('requestsComponentBreak', 'lineBreak', {spaces:1});
		}
		
		if(appConfig.instance == 'thelifebookno' || appConfig.instance == 'thelifebookau' || appConfig.instance == 'rickyvoltz'){
			dom.cont.right.makeNode('lifebookinternational', 'div', {uiGrid:false});
			dom.cont.right.lifebookinternational.makeNode('tableCont', 'container', {title:'<i class="fa fa-book"></i> Life Book Orders', collapse:collapse});
			//dom.cont.right.makeNode('lifebookinternationalBreak', 'lineBreak', {spaces:1});
		}
		
		dom.cont.right.makeNode('notes', 'div', {});
											
		if(obj.type){
			
			if(obj.type.available_types != null){
			
				obj.type.available_types = obj.type.available_types.filter(function(n){ return n != undefined });
				
			}

		}

		draw({
			dom:dom,
			after:function(dom){

				if(object.type && object.type.hasOwnProperty('states')){

					sb.notify({
						type: 'view-field',
						data: {
							type:'state',
							property:'states',
							ui: dom.cont.name.stateBtn,
							obj: object,
							options: {
								size: 'mini'
							}
						}
					});

				}														
				
				if (obj.type) {
					
					components.contactInfo.notify({
						type:'start-contact_info-component',
						data:{
							domObj:dom.cont.left.contactInfo,
							collapse:collapse
						}
					});
					
					var contactInfoCompSetup = {
							contactType:obj.type,
							contactInfoTypes:obj.type.available_types,
							info:obj.contact_info
						};

					components.contactInfo.notify({
						type:'show-contact-info-column',
						data:{
							contactTypeId:obj.type.id,
							contactId:obj.id,
							contactType:'contacts',
							compData:contactInfoCompSetup,
							collapse:collapse,
							handleModal:true
						}
					});	
					
				} else {
					
					dom.cont.left.contactInfo.makeNode('msg', 'div', {
						css:'ui warning message'
						, text: 'Select a type for this contact to add contact info.'
					});
					
					dom.cont.left.contactInfo.patch();
					
				}
				
				
				sb.notify({
					type: 'show-task-list',
					data: {
						domObj: dom.cont.left.tasks,
						objectIds: [obj.id],
						objectId: obj.id,
						collapse: collapse,
						compact: true,
						draw: function(done){
							
							done.patch();
							
						}
					}
				});
				
				if(
					appConfig.instance == 'rickyvoltz' ||
					appConfig.instance == 'voltzsoftware'
				){
					
					if(sb.sys.state.components.instances){
						
						var instancesDom = dom.cont.right.instances.tableCont.cont;
					
						sb.notify({
							type:'instances-single-object-view',
							data:{
								dom:instancesDom,
								contactObj:obj
							}
						});	

					}
									
				}
				
				if(
					appConfig.instance != 'thelifebookau' &&
					appConfig.instance != 'thelifebookno'
				){
										
					if(sb.sys.state.components.projects){
						
						sb.notify({
							type: 'projects-contact-view',
							data: {
								domObj: dom.cont.right.projects.tableCont,
								objectId: obj.id,
								draw:function(projects){
									projects.dom.patch();
								}
							}
						});
						
					}
					
					if(sb.sys.state.components.workorders){
						sb.notify({
							type: 'workorders-object-view',
							data: {
								domObj: dom.cont.right.workorders.tableCont,
								contact: obj,
								draw:function(workflows){
									workflows.dom.patch();
								}
							}
						});
					}
					
					sb.notify({
						type: 'show-paymentMethod-button',
						data: {
							domObj:dom.cont.left.paymentInfo,
							objectId:obj.id,
							compact: true,
							collapse:'closed'
						}
					});
					
				}
																			
				
				
				if(components.notes){
					components.notes.destroy();
					delete components.notes;
				}
				
				sb.notify({
					type: 'show-note-list-box',
					data: {
						domObj:dom.cont.right.notes,
						objectId:obj.id,
						collapse:true,
						state:state
					}
				});		
				
				if(appConfig.instance == 'thelifebook' || appConfig.instance == 'rickyvoltz'){
					
					dom.alertContainer.makeNode('cont', 'div', {});
					dom.alertContainer.makeNode('break2', 'div', {text:'<br />'});
					
					dom.alertContainer.patch();
					
					sb.notify({
						type: 'view-requests-box-list',
						data: {
							dom:dom.cont.right.requestsComponent,
							object:obj,
							alertDom:dom.alertContainer.cont,
							collapse:collapse
						}
					});
				
				}
				
				if(appConfig.instance == 'thelifebookno' || appConfig.instance == 'thelifebookau' || appConfig.instance == 'rickyvoltz'){
					sb.notify({
						type: 'lifebookinternational-show-single-object',
						data: {
							dom:dom.cont.right.lifebookinternational.tableCont,
							contact:obj
						}
					});
				}
															
				if(sb.sys.state.components.tags){
		
					components.tags.notify({
						type: 'object-tag-view',
						data: {
							domObj: dom.cont.name.tags,
							objectType: 'contacts',
							objectId: obj.id
						}
					});	
					
				}
				
				if(
					sb.sys.state.components.inventory &&
					(
						appConfig.instance == 'zachvoltz' ||
						appConfig.instance == 'infinity' ||
						appConfig.instance == 'dreamcatering' ||
						appConfig.instance == 'mcvh' ||
						appConfig.instance == 'infinityproduction' ||
						appConfig.instance == 'nashvilleeventbartending'
					)
				){
					
					if( components.discountsTable ){
						components.discountsTable.destroy();
					}
					
					components.discountsTable = sb.createComponent('crud-table');
					components.discountsTable.notify({
						type:'show-table',
						data:{							
							domObj:dom.cont.right.discounts.tableCont,
							tableTitle:'Discounts',
							objectType:'discounts',
							searchObjects:false,
							navigation:false,					
							headerButtons:{
								create: {
									name:'<i class="plus icon"></i> Add',
									css:'ui basic green button',
									domType:'modal',
									action:function(bp, modal){
										sb.notify({
											type:'get-edit-discount-modal',
											data:{
												modal:modal,
												applyTo:obj,
												onComplete:function(response){
													
													components.discountsTable.notify({
														type:'update-table',
														data:{}
													});
													
												}
											}
										});
									}
								},
								reload:{
									name:'Reload',
									css:'pda-btn-blue',
									action:function(){}
								}
							},
							data:function(page, callback){
								
								page.sortCol = 'name';
								page.sortDir = 'asc';
								sb.data.db.obj.getWhere('discounts', {menu:obj.id, paged:page, childObjs:2}, function(data){
									
									callback(data);
									
								});
								
							},
							visibleCols:{
								btns:'',
								name:'Name',
								description:'Description',
								factor:'Discount'
							},
							cells:{
								btns:function(obj, cell){
									
									cell.properties.compInstanceId = sb.instanceId;
									cell.makeNode('editBtn', 'div', {
										text:'Edit',
										css:'ui tiny orange button'
									}).notify('click', {
										type:'inventory-comp-run',
										data:{
											run:function(obj){
			
												//editModal.show();
												editDiscountForm(editModal, viewSettings.category, obj, function(response){
			
													comps.stockGroups.notify({
														type:'update-table',
														data:{}
													});
													
												});
												
											}.bind({}, obj)
										}
									}, sb.moduleId);
									
								},
								factor:function(obj){
									switch(obj.type){
										
										case 'percent_off':
										return (obj.factor/100)/100 +'% off';
										break;
										
										case 'amount_off':
										return '$'+ (obj.factor/100).toFixed(2) +' off';
										break;
										
										case 'replace_amount':
										return 'override price to $'+ (obj.factor/100).toFixed(2);
										break;
										
									}
								}
							},
							rowLink:false,
							rowSelection:true,
							multiSelectButtons:{
								erase:{
									name:'<i class="fa fa-trash-o"></i> Remove',
									css:'pda-btn-red',
									domType:'none',
									action:function(one, two, three){

									}
								}
							}
						}
					});
					
				}									
			}

		});	
				
	}
			
	function tableState(){
			
		function buildTable(objectId){

			var dom = this;

			var crudSetup = {							
					domObj:dom,
					objectType:'contacts',
					childObjs:2,
					tags:function(dom, obj){
												
						dom.makeNode('name', 'headerText', {text:obj.fname, size:'small'});
						
						return dom;						
						
					},
					tableTitle:setupData.tableTitle,
					navigation:setupData.navigation,
					searchObjects:[
						{
							name:'First Name',
							value:'fname'
						},
						{
							name:'Last Name',
							value:'lname'
						},
						{
							name:'Contact Info',
							value:'info',
							join:'contact_info'
						}						
					],
					settings:{
						action:[
							{
								object_type:'contact_types',
								name:'Contact Types'
							},
							{
								object_type:'contact_info_types',
								name:'Contact Info Types'
							},
							{
								object_type:'staff_base',
								name:'Locations'
							}
						]
					},					
					filters:function(callback){
						
						sb.data.db.obj.getAll('contact_types', function(types){
		
							sb.data.db.obj.getAll('contact_info_types', function(contactInfoTypesObjs){
							
								contactInfoTypes = contactInfoTypesObjs;
			
								var filterTypes = [];
							
								_.each(types, function(t){
									
									filterTypes.push({
										name:'type',
										label:t.name,
										value:t.id,
										checked:true
									});
									
								});

								callback({
									type:{
										name:'Contact Types',
										type:'checkbox',
										field:'type',
										options:filterTypes
									}
								});
						
							}, 1);
		
						});
						
					},
					download:function(obj){
							
						var emailAddress = 'None given';
						
						if(obj.contact_info){
							
							_.each(obj.contact_info, function(o){
								
								if(o){
									
									if(o.type){
									
										if(o.type.data_type == 'email' && o.is_primary == 'yes'){
											
											emailAddress = o.info;
											
										}
	
										if(o.type == 48){
	
											emailAddress = o.info
																
										}
										
									}
									
								}
								
							});
							
						}
						
						return [
							{
								name:'Date Created',
								value:moment(obj.date_created).format('M/D/YYYY'),
							},
							{
								name:'First Name',
								value:obj.fname,
							},
							{
								name:'Last Name',
								value:obj.lname
							},
							{
								name:'Email',
								value:emailAddress,
							}];
						
					},
					headerButtons:{
						create:{
							name:'<i class="fa fa-plus"></i> Create Contact',
							css:'pda-btn-green',
							domType:'full',
							action:createContactState
						},
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							action:function(){}
						}
					},
					rowSelection:false,
					multiSelectButtons:{
						erase:{
							name:'<i class="fa fa-times"></i> Delete',
							css:'pda-btn-red',
							domType:'modal',
							action:function(selectedObjs, dom){
								
								dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
								
								dom.body.patch();
								
								setTimeout(function(){
																		
									dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Delete '+ selectedObjs.length + ' contact(s)?'});
																				
									dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes, delete them'}).notify('click', {
										type:'change-from-table',
										data:{
											type:'approve',
											modal:dom,
											objects:selectedObjs
										}
									}, sb.moduleId);
									
									dom.body.patch();
									dom.footer.patch();
									
									crudModal = dom;
									
								}, 1000);
								
							}
						}
					},
					home:{
						header:'Contacts',
						action:homeState
					},
					rowLink:{
						type:'tab',
						header:function(obj){
							return obj.fname +' '+ obj.lname;
						},
						action:singleState
					},
					visibleCols:{
						fname:'First Name',
						lname:'Last Name',
						type:'Type',
						company:'Company',
						email:'Email',
						phone:'Phone',
						address:'Address',
						date_created:'Date Created'
					},
					cells: {
						date_created:function(obj){
							return moment(obj.date_created).format('M/D/YYYY h:mm a');
						},
						name:function(obj){
							return obj.fname +' '+ obj.lname;
						},
						type:function(obj){
							if(obj.type){
								return obj.type.name;
							}else{
								return 'Type not selected';
							}
						},
						company:function(obj){
							if(obj.company){
								return obj.company.name;
							}else{
								return 'Company not selected';
							}
						},
						address:function(obj){
							
							var ret = '',
								count = 0;
								
							_.each(obj.contact_info, function(o){
								
								if(o){
									
									if(o.type){
									
										if(o.type.data_type == 'address'){
											
											var streetTwo = '';
											
											if(o.hasOwnProperty('street2') && o.street2 != ''){
												streetTwo = ' '+o.street2;
											}
	
											if(count == 0){
												
												ret += o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
												
												count++;
												
											}else{
												
												ret += '<br />'+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
												
											}
											
										}
	
										if(o.type == 52 || o.type == 53){
											
											var streetTwo = '';
											
											if(o.hasOwnProperty('street2') && o.street2 != ''){
												streetTwo = ' '+o.street2;
											}
	
											if(count == 0){
												
												ret += o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
												
												count++;
												
											}else{
												
												ret += '<br />'+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
												
											}
																
										}
										
									}
									
								}
								
							});
							
							return ret;
							
						},
						adults:function(obj){
							
							var ret = '',
								count = 0;
								
							_.each(obj.contact_info, function(o){
								
								if(o){
								
									if(o.type){
										
										if(o.type.data_type == 'other'){
											
											if(count == 0){
												
												ret += o.info;
												
												count++;
												
											}else{
												
												ret += ', '+ o.info;
												
											}
											
										}
	
										if(o.type == 65){
	
											if(count == 0){
												
												ret += o.info;
												
												count++;
												
											}else{
												
												ret += ', '+ o.info;
												
											}
																
										}
										
									}
									
								}
								
							});
							
							return ret;
							
						},
						email:function(obj){
							
							var ret = '',
								count = 0;
								
							_.each(obj.contact_info, function(o){
								
								if(o){
									
									if(o.type){
									
										if(o.type.data_type == 'email'){
											
											if(count == 0){
												
												ret += o.info;
												
												count++;
												
											}else{
												
												ret += '<br />'+ o.info;
												
											}
											
										}
	
										if(o.type == 48){
	
											if(count == 0){
												
												ret += o.info;
												
												count++;
												
											}else{
												
												ret += '<br />'+ o.info;
												
											}
																
										}
										
									}
									
								}
								
							});
							
							return ret;
							
						},
						phone:function(obj){
							
							var ret = '',
								count = 0;
								
							_.each(obj.contact_info, function(o){
								
								if(o){
									
									if(o.type){
									
										if(o.type.data_type == 'phone'){
											
											if(count == 0){
												
												ret += sb.dom.formatPhone(o.info);
												
												count++;
												
											}else{
												
												ret += '<br />'+ sb.dom.formatPhone(o.info);
												
											}
											
										}
	
										if(o.type == 49 || o.type == 60){
	
											if(count == 0){
												
												ret += sb.dom.formatPhone(o.info);
												
												count++;
												
											}else{
												
												ret += '<br />'+ sb.dom.formatPhone(o.info);
												
											}
																
										}
										
									}
									
								}
								
							});
							
							return ret;
						
						},
						students:function(obj){
							
							var ret = '',
								count = 0;
								
							_.each(obj.contact_info, function(o){
								
								if(o){
									
									if(o.type){
									
										if(o.type.data_type == 'other'){
											
											if(count == 0){
												
												ret += o.info;
												
												count++;
												
											}else{
												
												ret += ', '+ o.info;
												
											}
											
										}
	
										if(o.type == 64){
	
											if(count == 0){
												
												ret += o.info;
												
												count++;
												
											}else{
												
												ret += ', '+ o.info;
												
											}
																
										}
										
									}
									
								}
								
							});
							
							return ret;
							
						},
					},
					data:function(paged, callback){
						
						sb.data.db.obj.getAll('contacts', function(ret){
			
							callback(ret);
							
						}, 2, paged);
						
					}
				};
				
			if(objectId){

				crudSetup.objectId = +objectId;
			
			}

			if(companyId){
				
				crudSetup.searchObjects = false;
				crudSetup.filters = false;
				crudSetup.download = false;
				crudSetup.home = false;
				crudSetup.settings = false;
				
				crudSetup.data = function(paged, callback){
					
					sb.data.db.obj.getWhere('contacts', {
						company:companyId,
						childObjs:2,
						paged:paged
					}, function(ret){
												
						callback(ret);
						
					});
					
				};
				
			}
			
			if(setupData.homeView === false){
				crudSetup.homeView = false;
			}
			
			if(compact){
				
				crudSetup.download = false;
				crudSetup.home = false;
				crudSetup.settings = false;
								
			}
					
			components.table.notify({
				type: 'show-table',
				data: crudSetup
			});
								
		}
		
		this.state.show = buildTable.bind(this);
		
	}
	
	function uploadCsvState(ui, state, draw, onComplete){

		function obj_mapper_view(map){
			
			// initial state
			if(map === undefined || _.isEmpty(map)){
			
				this.empty();
				
				this.makeNode('message', 'text', {text:'Please choose a file to upload', css:'text-center text-muted'});
				
				this.patch();
			
			// updates to map obj
			}else{
				
			}
			
		}
		
		ui.empty();
		
/*
		var left = ui.makeNode('left', 'column', {width:4});
		var right = ui.makeNode('right', 'column', {width:8});
		
		// LEFT COLUMN
		left.makeNode('title', 'headerText', {text:'Upload csv file'});
		left.makeNode('form', 'form', {
			
			file: {
				name:'file',
				type:'file-upload',
				label:'Select a file'
			}
			
		});
		
		// RIGHT COLUMN
		right.update = obj_mapper_view;
		right.update();
*/
		
		ui.patch();
		
		draw({
			dom:ui,
			after:function(ui){
				
				if(components.hasOwnProperty('csvUploader')){
					components.csvUploader.destroy();
				}
				
				components.csvUploader = sb.createComponent('csv-uploader');
				components.csvUploader.notify({
					type:'show-csv-contact-uploader',
					data:{
						domObj:ui,
						object_type:'contacts',
						onComplete: onComplete
					}
				});
				
			}
		});
		
	}
	
	function contactTypeSettings(dom, bp){

		function contactTypeEditView(dom, setup){			

			var create = _.isEmpty(setup.object);
			var object = setup.object;

			var formArgs = {
					name:{
						name:'name',
						type:'text',
						label:'Name',
						value:object.name || '',
						placeholder:'New, Opportunity, etc.'					
					},
					formsof:{
						name:'formsof',
						type:'checkbox',
						label:'Forms of Contact',
						placeholder:'Select which forms of contact that apply..',
						options:[],
						value: []
					}
				};
				
			var defaultStates = [
					{
						uid:1,
						name:'New',
						icon:'star outline',
						color:'grey',
						previous:[],
						next:[2],
						isEntryPoint:1
					},
					{
						uid:2,
						name:'Active',
						icon:'star',
						color:'grey',
						previous:[1],
						next:[]
					}
				];

			function processContactTypeForm(form, updObj, cb){

				var ret = {
					dbCall:'create',
					status:{
						type:'success',
						message:'You have created a new type of Contact'
					},
					saveObj:updObj
				};
				
				var formData = form.detailsForm.process().fields;
				
				if(updObj.hasOwnProperty('id'))
					ret.dbCall = 'update';

				if(_.isEmpty(formData.name.value)){
					ret.status.type = 'warning';
					ret.status.message = 'Please enter a name for this contact type';
				}

				if(_.isArray(formData.formsof.value)){
					
					ret.saveObj.available_types = _.map(formData.formsof.value, function(val){
						
						return parseInt(val);
												
					});
										
				} else {
					
					ret.saveObj.available_types = [];
				}
				
				ret.saveObj.name = formData.name.value;

				return cb(ret);

			}

			if(!_.isEmpty(setup.info_types)){
				
				formArgs.formsof.options = _.map(setup.info_types, function(itype){

					var ret = {name:itype.name, value:itype.id, label:itype.name}	
			
					if(_.findWhere(object.available_types, {id:itype.id}))
						ret.selected = true;
					
					if(create === true) {
						
						formArgs.formsof.value.push(itype.id);		
						
					}
			
					return ret;
					
				});
				
				if(create === false) {
						
					_.each(object.available_types, function(type) {
						
						formArgs.formsof.value.push(type.id);		
						
					});
					
				}
									
			}	
			
			///default values 

			if(create || _.isEmpty(object.states) || !_.has(object, 'states')){
		
				object.states = defaultStates;
				
			}

			dom.empty();

			dom.makeNode('detailsForm', 'form', formArgs);
			dom.makeNode('br1', 'div', {text:'<br />'});
			dom.makeNode('states', 'div', {css: 'text-center'});
			
			sb.notify({
				type: 'show-workflow-states-view',
				data: {
					dom:dom.states,
					state:{
						header:'Contact management flow<div class="sub header">Define this contact type\'s workflow in your system.</div>',
						object:object
					}	
				}
			});			
			
			dom.makeNode('br2', 'div', {text:'<br />'});
			dom.makeNode('btns', 'div', {css: 'ui buttons'});
			dom.btns.makeNode('back', 'div', {css: 'ui button', text:'Cancel'}).notify('click', {
				type: 'contactComponent-run',
				data: {
					run:contactTypeSettings.bind(null, dom, setup)
				}
			}, sb.moduleId);
			
			if(!create)
				dom.btns.makeNode('delete', 'div', {css: 'ui red button', text:'Delete'}).notify('click', {
					type: 'contactComponent-run',
					data: {
						run:function(dom, obj){
							
							dom.btns.delete.css('ui red button loading');
							
							sb.data.db.obj.erase('contact_types', obj.id, function(resp){
								
								if(resp){

									contactTypeSettings(dom, bp);

								}else{
									
									dom.btns.delete.loading(false);
									
									sb.dom.alerts.alert('Oops!', 'An error occurred -- please refresh and try again.', 'error');
								}								
								
							});
							
						}.bind(null, dom, object)
					}
				}, sb.moduleId);
			
			dom.btns.makeNode('save', 'div', {css: 'ui green button', text:'Save'}).notify('click', {
				type: 'contactComponent-run',
				data: {
					run:function(dom, typeObj){
						
						dom.btns.save.css('ui green button loading');
						processContactTypeForm(dom, typeObj, function(output){

							if(output.status.type == 'warning'){
								
								setTimeout(function(){
									
									dom.btns.save.loading(false);

									sb.dom.alerts.alert('Warning', output.status.message, output.status.type);
									
								}, 300);
	
							}else{

								sb.data.db.obj[output.dbCall]('contact_types', output.saveObj, function(updated){
									
									setup.object = updated;
									
									contactTypeSettings(dom, bp);
																			
								});	
																
							}							
							
						});
												
					}.bind({}, dom, object)
				}
			}, sb.moduleId);
			
			dom.patch();
			
			return;	
			
		}
		
		setupContactSystem(function(done){
			
			sb.data.db.obj.getAll('contact_types', function(contact_types){

				sb.data.db.obj.getAll('contact_info_types', function(info_types){
	
					dom.empty();
				
					dom.makeNode('btns', 'div', {
						css:'ui right floated buttons'
					}).makeNode('create', 'div', {
						tag:'button',
						css:'ui green button',
						text:'New Contact Type'
					}).notify('click', {
						type:'contactComponent-run',
						data:{
							run:contactTypeEditView.bind(null, dom, {
								blueprint:bp,
								object:{},
								info_types:info_types
							})
						}
					}, sb.moduleId);
					
					// table
					dom.makeNode('br-before-table', 'div', {text:'<br /><br />'});
					dom.makeNode('table', 'div', {tag:'table', css:'ui basic table'}).makeNode('thead', 'div', {tag:'thead'});
					
					dom.table.thead.makeNode('tr', 'div', {tag:'tr'})
						.makeNode('name', 'div', {tag:'th', text:'Name'});
										
					dom.table.thead.tr.makeNode('btns', 'div', {tag:'th', text:'Buttons'});
					dom.table.makeNode('body', 'div', {tag:'tbody'});
					
					_.each(contact_types, function(type){
	
						dom.table.body.makeNode('type-'+type.id, 'div', {tag:'tr'})
							.makeNode('name', 'div', {tag:'td', text:type.name});
											
						dom.table.body['type-'+type.id].makeNode('btns', 'div', {tag:'td'})
							.makeNode('edit', 'div', {tag:'button', css:'ui tiny yellow button', text:'Edit'})
							.notify('click', {
								type: 'contactComponent-run',
								data: {
									run:contactTypeEditView.bind(null, dom, {
										blueprint:bp,
										object:type,
										info_types:info_types
									})
								}
							}, sb.moduleId);	
						
					});
					
					dom.patch();			
					
				});
									
			}, 1);
			
		});
				
	}
	
	function getContactData(contact, callback){
		
		var ret = {};
		
		if(contact){
			
			sb.data.db.obj.getWhere('companies', {
				id:contact.company, 
				childObjs:{
					name:true
				}
			}, function(comp){
				
				sb.data.db.obj.getWhere('contact_info', {
					object_id:contact.id,
					childObjs:{
						info:true,
						city:true,
						state:true,
						zip:true,
						type:{
							name:true,
							data_type:true
						}
					}
				}, function(contactInfo){
					
					sb.data.db.obj.getWhere('users', {
						id:contact.manager, 
						childObjs:{
							fname:true, 
							lname:true
						}
					}, function(man){

						ret.company = comp[0];
						ret.info = contactInfo; 
						ret.manager = man[0];
							
						callback(ret);
						
					});
										
				});	
				
			});
			
		}
		
		return;		
	}
	
	function getObjectPageParams(obj){
		
		var objType = obj.object_bp_type;
		var objName = obj.name;
		
		switch(objType){
			
			case 'contacts':
			case 'users':
			
			objName = obj.fname +' '+ obj.lname;
			
			break;
			
			case 'tasks':
			
			objName = obj.title;
			
			break;
				
		}
			
		return sb.data.url.createPageURL(
				'object', 
				{
					type:objType, 
					id:obj.id,
					name:objName
				}
			);
			
	}
				
	return {
		
		init: function(){
			
			var fields = {
					fname:{
						title:'First Name',
						type:'title',
						isSearchable:true
					},
					lname:{
						title:'Last Name',
						type:'title',
						isSearchable:true
					},
					type:{
						title:'Type',
						type:'type'
					},
					state:{
						title:'State',
						type:'state'
					},
					company:{
						title:'Company'
					},
					email:{
						title:'Email',
						view:function(c, obj){
							
							var infoToShow = _.filter(obj.contact_info, function(info){
								
								if(info.type && info.type.data_type){
									return info.type.data_type === 'email';
								}
								
								return false;
								
							})[0];
							
							if(infoToShow) {
								c.makeNode('t', 'div', {text:infoToShow.info});
							} else {
								return false;
							}
							
						},
						type:'detail'
					},
					phone:{
						title:'Phone',
						view:function(c, obj){
							
							var infoToShow = _.filter(obj.contact_info, function(info){
								
								if(info.type && info.type.data_type){
									return (info.type.data_type === 'cellphone' || info.type.data_type === 'phone');
								}
								
								return false;
								
							})[0];
							
							if(infoToShow) {
								c.makeNode('t', 'div', {text:infoToShow.info});
							} else {
								return false;
							}
							
						},
						type:'detail'
					},
					address:{
						title:'Address',
						view:function(c, obj){
							
							var infoToShow = _.filter(obj.contact_info, function(info){
									
								if(info.type && info.type.data_type){
									return info.type.data_type === 'address';
								}
								
								return '';
								
							})[0];

							var addressObject = {
								address: infoToShow
							}
							
							sb.notify({
								type: 'view-field'
								, data: {
									type: 'address'
									, property: 'address'
									, obj: addressObject
									, isMetric: true
									, options: {
										edit: false
									}
									, ui: c.makeNode('address', 'div', {})
								}
							});
							
						},
						type:'detail'
					},
					date_created:{
						title:'Created On'
					}
				};
				
			if($(window).width() <= 768) {
				
				onMobile = true;
												
				fields = _.omit(fields, ['fname', 'lname']);
				fields = _.extend({
					fname: {
						title: 'Name',
						type: 'title',
						isSearchable: true,
						view: function(ui, obj) {

							ui.makeNode('t', 'div', {
								tag: 'a',
								text: obj.fname + ' ' + obj.lname,
								css: 'ui header',
								style: 'text-decoration:underline;',
								href: getObjectPageParams(obj)
							});
							
						}
					}
				}, fields);
					
			}
			
			components.table = sb.createComponent('crud-table');
			components.tags = sb.createComponent('tags');
			components.contactInfo = sb.createComponent('contact_info');
			
			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'contacts',
						title:'Contacts',
						icon:'<i class="fa fa-users"></i>',
						views:[
							{
								id:'table',
								display:false,
								type:'table',
								title:'All Contacts',
								icon:'<i class="fa fa-th-list"></i>',
								pageChange:function(state, changePage){

									if(state.rowObj){

										if(state.editing){
											
											sb.dom.alerts.ask({
												text: 'You haven\'t saved your changes. Are you sure you want to leave this page?',
												title: ''
											}, function(resp){

												if(resp){
													
													state.editing = false;
													
													swal.close();

													sb.notify(changePage);
										
												}
												
											});
											
											return false;
											
										}
										
									}else{
										
										return true;
										
									}									
													
									
								},
								setup:{
									objectType:'contacts',
									tableTitle:'<i class="fa fa-users"></i> Contacts',
									childObjs:2,
									searchObjects:[
										{
											name:'First Name',
											value:'fname'
										},
										{
											name:'Last Name',
											value:'lname'
										},
										{
											name:'Contact Info',
											value:'info',
											join:'contact_info'
										}						
									],
									filters:function(callback){
										
										sb.data.db.obj.getAll('contact_types', function(types){
						
											sb.data.db.obj.getAll('contact_info_types', function(contactInfoTypesObjs){
											
												contactInfoTypes = contactInfoTypesObjs;
							
												var filterTypes = [];
											
												_.each(types, function(t){
													
													filterTypes.push({
														name:'type',
														label:t.name,
														value:t.id,
														checked:true
													});
													
												});
										
												callback({
													type:{
														name:'Contact Types',
														type:'checkbox',
														field:'type',
														options:filterTypes
													}
												});
										
											}, 1);
						
										});
										
									},
									download:function(obj){
											
										var emailAddress = 'None given';
										
										if(obj.contact_info){
											
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'email' && o.is_primary == 'yes'){
															
															emailAddress = o.info;
															
														}
					
														if(o.type == 48){
					
															emailAddress = o.info
																				
														}
														
													}
													
												}
												
											});
											
										}
										
										return [
											{
												name:'Date Created',
												value:moment(obj.date_created).format('M/D/YYYY'),
											},
											{
												name:'First Name',
												value:obj.fname,
											},
											{
												name:'Last Name',
												value:obj.lname
											},
											{
												name:'Email',
												value:emailAddress,
											}];
										
									},
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										},
										upload:{
											name:'<i class="fa fa-upload"></i> Upload Contact File',
											css:'pda-btn-blue',
											domType:'none',
											action:function(){
												
												sb.notify({
													type:'app-navigate-to',
													data:{
														itemId:'self',
														viewId:{
															id:'upload',
															type:'custom',
															title:'Upload Contacts',
															icon:'<i class="fa fa-upload"></i>',
															dom:uploadCsvState,
															removable:true
														}
													}
												});
																																			
											}
										}
									},
									rowSelection:true,
									multiSelectButtons:{
										erase:{
											name:'<i class="fa fa-times"></i> Delete',
											css:'pda-btn-red',
											domType:'modal',
											action:eraseContacts
										}
									},
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.fname +' '+ obj.lname;
										},
										action:singleState
									},
									visibleCols:{
										fname:'First Name',
										lname:'Last Name',
										type:'Type',
										company:'Company',
										email:'Email',
										phone:'Phone',
										address:'Address',
										date_created:'Date Created'
									},
									cells: {
										date_created:function(obj){
											return moment(obj.date_created).format('M/D/YYYY h:mm a');
										},
										name:function(obj){
											return obj.fname +' '+ obj.lname;
										},
										type:function(obj){
											if(obj.type){
												return obj.type.name;
											}else{
												return 'Type not selected';
											}
										},
										company:function(obj){
											if(obj.company){
												return obj.company.name;
											}else{
												return 'Company not selected';
											}
										},
										address:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'address'){

															var streetTwo = '';
											
															if(o.hasOwnProperty('street2') && o.street2 != ''){
																streetTwo = ' '+o.street2;
															}
					
															if(count == 0){
																
																ret += o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																
																count++;
																
															}else{
																
																ret += '<br />'+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																
															}
															
														}
					
														if(o.type == 52 || o.type == 53){
															
															var streetTwo = '';
															
															if(o.hasOwnProperty('street2') && o.street2 != ''){
																streetTwo = ' '+o.street2;
															}

					
															if(count == 0){
																
																ret += o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																
																count++;
																
															}else{
																
																ret += '<br />'+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
											
										},
										adults:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
												
													if(o.type){
														
														if(o.type.data_type == 'other'){
															
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += ', '+ o.info;
																
															}
															
														}
					
														if(o.type == 65){
					
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += ', '+ o.info;
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
											
										},
										email:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'email'){
															
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += '<br />'+ o.info;
																
															}
															
														}
					
														if(o.type == 48){
					
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += '<br />'+ o.info;
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
											
										},
										phone:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'phone'){
															
															if(count == 0){
																
																ret += sb.dom.formatPhone(o.info);
																
																count++;
																
															}else{
																
																ret += '<br />'+ sb.dom.formatPhone(o.info);
																
															}
															
														}
					
														if(o.type == 49 || o.type == 60){
					
															if(count == 0){
																
																ret += sb.dom.formatPhone(o.info);
																
																count++;
																
															}else{
																
																ret += '<br />'+ sb.dom.formatPhone(o.info);
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
										
										},
										students:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'other'){
															
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += ', '+ o.info;
																
															}
															
														}
					
														if(o.type == 64){
					
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += ', '+ o.info;
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
											
										},
									},
									paged:{
										sortCol:'fname',
										sortDir:'asc'
									},
									data:function(paged, callback){
																				
										sb.data.db.obj.getAll('contacts', function(ret){

											callback(ret);
											
										}, {
											fname:true,
											lname:true,
											type:{
												name:true
											},
											company:{
												name:true
											},
											contact_info:{
												info:true,
												street:true,
												city:true,
												state:true,
												zip:true,
												type:{
													data_type:true
												}
											}
										}, paged);
										
									}
								}								
							},
							{
								id:'contacts-collection',
								type:'custom',
								title:'All Contacts',
								icon:'<i class="fa fa-th-list"></i>',
								isCollection:true,
								default:true,
								dom:function(dom, state, draw){

									draw({
										dom:dom,
										after:function(dom){

											sb.notify({
												type:'show-collection',
												data:{
													actions:{
														upload: {
															id: 'upload',
															icon: 'upload',
															color: 'purple',
															action: function(data, ui, onComplete){
// 			console.log('upload::', arguments);															
																uploadCsvState(ui, {}, function(ui){
																	
// 																	console.log('draw::', ui);
																	ui.dom.patch();
																	ui.after(ui.dom);
																	
																}, function(){
																	
																	onComplete(true);
																	
																});
															
															},
															title:'Upload CSV',
															headerAction: true,
															singleAction: false
														}
													},
													domObj:dom,
													fields:{
														fname:{
															title:'First Name',
															type:'title'
														},
														lname:{
															title:'Last Name',
															type:'title'														
														},
														type:{
															title:'Type',
															type:'type',
															view:function(domObj, object){

																var textString = 'Not selected';
																
																if(object.type){
																	
																	if(object.type.hasOwnProperty('name')){
																		textString = object.type.name;
																	}
																
																	domObj.makeNode('text', 'div', {text:'<div class="ui label">'+ textString +'</div>'});															
																
																}
																														
															}
														},
														email:{
															title:'Email', 
															view:function(domObj, object){

																var textString = '';
																
																if(!_.isEmpty(object.contact_info)){

																	_.filter(object.contact_info, function(info){

																		if(info.title == 'Email')
																			textString = info.info;
																		
																	}).title;
																	
																}

																domObj.makeNode('text', 'div', {text:textString});						
															}
														},
														phone:{
															title:'Phone',
															view:function(domObj, object){
																var textString = '';
																
																if(!_.isEmpty(object.contact_info)){

																	_.filter(object.contact_info, function(info){

																		if(info.title == 'Cell Phone')
																			textString = info.info;
																		
																	}).title;
																	
																}

																domObj.makeNode('text', 'div', {text:textString});																
															}
														},
														address:{
															title:'Address',
															view:function(dom, obj){
																
																var infoToShow = _.filter(obj.contact_info, function(info){
									
																	if(info.type && info.type.data_type){
																		return info.type.data_type === 'address';
																	}
																	
																	return '';
																	
																})[0];
								
																var addressObject = {
																	address: infoToShow
																}
																
																sb.notify({
																	type: 'view-field'
																	, data: {
																		type: 'address'
																		, property: 'address'
																		, obj: addressObject
																		, isMetric: true
																		, options: {
																			edit: false
																		}
																		, ui: c.makeNode('address', 'div', {})
																	}
																});

															}
														},
														manager:{
															title:'Manager',
															view:function(domObj, object){
																var textString = '';
																if(object.manager){
																	var textString = object.manager.fname +' '+ object.manager.lname;
																}
																domObj.makeNode('text', 'div', {text:textString});
															}															
														},
														company:{
															title:'Company',
															type:'type'														
														},																												
													},
													fullView:{
														type:'object-view',
														id:'contacts-obj'
													},
													groupings:{
														type:'Type'
													},
													objectType:'contacts',
													selectedView:'table',
													state:state,
													where:{
														childObjs:{
															fname:true,
															lname:true,
															contact_info:true,
															type:true,
															manager:true,
															company:{
																name:true
															}
														}
													}
												}
											});
																				
										}
									});
									
								}
							},
							{
								id:'sales-table',
								display:false,
								type:'table',
								title:'My Contacts',
								icon:'<i class="fa fa-th-list"></i>',
								pageChange:function(state, changePage){

									if(state.rowObj){

										if(state.editing){
											
											sb.dom.alerts.ask({
												text: 'You haven\'t saved your changes. Are you sure you want to leave this page?',
												title: ''
											}, function(resp){

												if(resp){
													
													state.editing = false;
													
													swal.close();

													sb.notify(changePage);
										
												}
												
											});
											
											return false;
											
										}
										
									}else{
										
										return true;
										
									}									
													
									
								},
								setup:{
									objectType:'contacts',
									tableTitle:'<i class="fa fa-users"></i> Contacts',
									childObjs:2,
									searchObjects:[
										{
											name:'First Name',
											value:'fname'
										},
										{
											name:'Last Name',
											value:'lname'
										},
										{
											name:'Contact Info',
											value:'info',
											join:'contact_info'
										}						
									],
									filters:function(callback){
										
										sb.data.db.obj.getAll('contact_types', function(types){
						
											sb.data.db.obj.getAll('contact_info_types', function(contactInfoTypesObjs){
											
												contactInfoTypes = contactInfoTypesObjs;
							
												var filterTypes = [];
											
												_.each(types, function(t){
													
													filterTypes.push({
														name:'type',
														label:t.name,
														value:t.id,
														checked:true
													});
													
												});
										
												callback({
													type:{
														name:'Contact Types',
														type:'checkbox',
														field:'type',
														options:filterTypes
													}
												});
										
											}, 1);
						
										});
										
									},
									download:function(obj){
											
										var emailAddress = 'None given';
										
										if(obj.contact_info){
											
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'email' && o.is_primary == 'yes'){
															
															emailAddress = o.info;
															
														}
					
														if(o.type == 48){
					
															emailAddress = o.info
																				
														}
														
													}
													
												}
												
											});
											
										}
										
										return [
											{
												name:'Date Created',
												value:moment(obj.date_created).format('M/D/YYYY'),
											},
											{
												name:'First Name',
												value:obj.fname,
											},
											{
												name:'Last Name',
												value:obj.lname
											},
											{
												name:'Email',
												value:emailAddress,
											}];
										
									},
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										},
										upload:{
											name:'<i class="fa fa-upload"></i> Upload Contact File',
											css:'pda-btn-blue',
											domType:'none',
											action:function(){
												
												sb.notify({
													type:'app-navigate-to',
													data:{
														itemId:'self',
														viewId:{
															id:'upload',
															type:'custom',
															title:'Upload Contacts',
															icon:'<i class="fa fa-upload"></i>',
															dom:uploadCsvState,
															removable:true
														}
													}
												});
																																			
											}
										}
									},
									rowSelection:true,
									multiSelectButtons:{
										erase:{
											name:'<i class="fa fa-times"></i> Delete',
											css:'pda-btn-red',
											domType:'modal',
											action:eraseContacts
										}
									},
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.fname +' '+ obj.lname;
										},
										action:singleState
									},
									visibleCols:{
										fname:'First Name',
										lname:'Last Name',
										type:'Type',
										company:'Company',
										email:'Email',
										phone:'Phone',
										address:'Address',
										date_created:'Date Created'
									},
									cells: {
										date_created:function(obj){
											return moment(obj.date_created).format('M/D/YYYY h:mm a');
										},
										name:function(obj){
											return obj.fname +' '+ obj.lname;
										},
										type:function(obj){
											if(obj.type){
												return obj.type.name;
											}else{
												return 'Type not selected';
											}
										},
										company:function(obj){
											if(obj.company){
												return obj.company.name;
											}else{
												return 'Company not selected';
											}
										},
										address:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'address'){

															var streetTwo = '';
											
															if(o.hasOwnProperty('street2') && o.street2 != ''){
																streetTwo = ' '+o.street2;
															}
					
															if(count == 0){
																
																ret += o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																
																count++;
																
															}else{
																
																ret += '<br />'+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																
															}
															
														}
					
														if(o.type == 52 || o.type == 53){
															
															var streetTwo = '';
															
															if(o.hasOwnProperty('street2') && o.street2 != ''){
																streetTwo = ' '+o.street2;
															}

					
															if(count == 0){
																
																ret += o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																
																count++;
																
															}else{
																
																ret += '<br />'+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
											
										},
										adults:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
												
													if(o.type){
														
														if(o.type.data_type == 'other'){
															
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += ', '+ o.info;
																
															}
															
														}
					
														if(o.type == 65){
					
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += ', '+ o.info;
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
											
										},
										email:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'email'){
															
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += '<br />'+ o.info;
																
															}
															
														}
					
														if(o.type == 48){
					
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += '<br />'+ o.info;
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
											
										},
										phone:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'phone'){
															
															if(count == 0){
																
																ret += sb.dom.formatPhone(o.info);
																
																count++;
																
															}else{
																
																ret += '<br />'+ sb.dom.formatPhone(o.info);
																
															}
															
														}
					
														if(o.type == 49 || o.type == 60){
					
															if(count == 0){
																
																ret += sb.dom.formatPhone(o.info);
																
																count++;
																
															}else{
																
																ret += '<br />'+ sb.dom.formatPhone(o.info);
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
										
										},
										students:function(obj){
											
											var ret = '',
												count = 0;
												
											_.each(obj.contact_info, function(o){
												
												if(o){
													
													if(o.type){
													
														if(o.type.data_type == 'other'){
															
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += ', '+ o.info;
																
															}
															
														}
					
														if(o.type == 64){
					
															if(count == 0){
																
																ret += o.info;
																
																count++;
																
															}else{
																
																ret += ', '+ o.info;
																
															}
																				
														}
														
													}
													
												}
												
											});
											
											return ret;
											
										},
									},
									paged:{
										sortCol:'fname',
										sortDir:'asc'
									},
									data:function(paged, callback){

										sb.data.db.obj.getWhere('contacts', {
											manager:+sb.data.cookie.userId,
											paged:paged,
											childObjs:{
												fname:true,
												lname:true,
												type:{
													name:true
												},
												company:{
													name:true
												},
												contact_info:{
													info:true,
													street:true,
													city:true,
													state:true,
													zip:true,
													type:{
														data_type:true
													}
												}
											}
										}, function(ret){
							
											callback(ret);
											
										});
										
									}
								}								
							},
							// Sales Table 
							{
								id:'salestablenew',
								type:'custom',
								title:'My Contacts',
								icon:'<i class="fa fa-th-list"></i>',
								default:true,
								dom:function(dom, state, draw){

									draw({
										dom:dom,
										after:function(dom){

											sb.notify({
												type:'show-collection',
												data:{
													actions:{
														upload: {
															id: 'upload',
															icon: 'upload',
															color: 'purple',
															action: function(data, ui, onComplete){
// 			console.log('upload::', arguments);															
																uploadCsvState(ui, {}, function(ui){
																	
// 																	console.log('draw::', ui);
																	ui.dom.patch();
																	ui.after(ui.dom);
																	
																}, function(){
																	
																	onComplete(true);
																	
																});
															
															},
															title:'Upload CSV',
															headerAction: true,
															singleAction: false
														}
													},
													domObj:dom,
													fields:{
														fname:{
															title:'First Name',
															type:'title'
														},
														lname:{
															title:'Last Name',
															type:'title'														
														},
														type:{
															title:'Type',
															type:'type',
															view:function(domObj, object){

																var textString = 'Not selected';
																
																if(object.type){
																	
																	if(object.type.hasOwnProperty('name')){
																		textString = object.type.name;
																	}
																
																	domObj.makeNode('text', 'div', {text:'<div class="ui label">'+ textString +'</div>'});															
																
																}
																														
															}
														},
														email:{
															title:'Email', 
															view:function(domObj, object){

																var textString = '';
																
																if(!_.isEmpty(object.contact_info)){

																	_.filter(object.contact_info, function(info){

																		if(info.title == 'Email')
																			textString = info.info;
																		
																	}).title;
																	
																}

																domObj.makeNode('text', 'div', {text:textString});						
															}
														},
														phone:{
															title:'Phone',
															view:function(domObj, object){
																var textString = '';
																
																if(!_.isEmpty(object.contact_info)){

																	_.filter(object.contact_info, function(info){

																		if(info.title == 'Cell Phone')
																			textString = info.info;
																		
																	}).title;
																	
																}

																domObj.makeNode('text', 'div', {text:textString});																
															}
														},
														address:{
															title:'Address',
															view:function(domObj, object){
																
																var infoToShow = _.filter(obj.contact_info, function(info){
									
																	if(info.type && info.type.data_type){
																		return info.type.data_type === 'address';
																	}
																	
																	return '';
																	
																})[0];
								
																var addressObject = {
																	address: infoToShow
																}
																
																sb.notify({
																	type: 'view-field'
																	, data: {
																		type: 'address'
																		, property: 'address'
																		, obj: addressObject
																		, isMetric: true
																		, options: {
																			edit: false
																		}
																		, ui: c.makeNode('address', 'div', {})
																	}
																});

															}
														},
														manager:{
															title:'Manager',
															view:function(domObj, object){

																var textString = '<span class="text-muted"><em>Not selected</em></span>';
																if(object.manager){
																	var textString = object.manager.fname +' '+ object.manager.lname;
																}
																domObj.makeNode('text', 'div', {text:textString});
															}															
														},
														company:{
															title:'Company',
															type:'type'														
														},																												
													},
													fullView:{
														type:'object-view',
														id:'contacts-obj'
													},
													groupings:{
														type:'Type'
													},
													objectType:'contacts',
													selectedView:'table',
													state:state,
													where:{
														manager:+sb.data.cookie.userId,
														childObjs:{
															fname:true,
															lname:true,
															contact_info:true,
															manager:true,
															type:{
																name:true
															},
															company:{
																name:true
															}
														}
													}
												}
											});
																				
										}
									});
									
								}
							},
							// Tags							
							{
								id:'tags',
								type:'tags', // table, calendar, tags, custom, quickaction
								title:'Tags',
								color:'red',
								icon:'<i class="fa fa-tags"></i>',
								setup:{
									type:'contacts',
									childObjs:2,
									resultList:function(dom, obj){
										
										var typeString = '<i>No type selected</i>',
											companyString = '<i>No client</i>';
										
										if(obj.type){
											typeString = obj.type.name;
										}
																
										if(obj.company){
											companyString = obj.company.name;
										}						
																
										dom.makeNode('cont', 'div', {css:'content'});
										
										dom.cont.makeNode('name', 'div', {css:'ui header', text:`<i class="fa fa-user-o"></i> ${obj.fname} ${obj.lname}`});
										
										dom.cont.makeNode('comp', 'div', {css:'meta', text:`<span class="small">Company:</span> ${companyString}`});
										dom.cont.makeNode('type', 'div', {css: '', tag:'p', text:`<span class="small">Type:</span> ${obj.type.name}`});
										dom.makeNode('btn', 'div', {css: 'ui bottom attached button', text: `View ${obj.fname}`});
										
										dom.btn.notify('click', {
											type:'app-navigate-to',
											data:{
												itemId:'contacts',
												viewId:{
													id:'single-'+obj.id,
													type:'table-single-item',
													title:obj.fname +' '+ obj.lname,
													icon:'<i class="fa fa-user"></i>',
													setup:{
														objectType:'contacts'
													},
													dom:singleState,
													rowObj:obj,
													removable:true,
													parent:'table'
												}
											}
										}, sb.moduleId);
										
										return dom;						
										
									}
								}
							},
							// New Contact
							{
								id:'newContact',
								type:'modal',
								title:'Create Contact',
								icon:'<i class="fa fa-plus"></i>',
								color:'green',
								modal:function(modal, state, draw){
									
									modal.body.empty();
									modal.footer.empty();
									
									modal.footer.makeNode('btns', 'buttonGroup', {});
																	
									modal.footer.makeNode('button', 'button', {text:'<i class="fa fa-times"></i> Close', css:'pda-btnOutline-red pda-btn-x-small'})
										.notify('click', {
											type:'contactComponent-run',
											data:{
												run:function(){
													
													this.hide();
													
												}.bind(modal)
											}
										}, sb.moduleId);
									
									modal.body.makeNode('break', 'lineBreak', {});
									
									modal.body.makeNode('header', 'headerText', {text:'You can create a new contact under a client.<br />How would you like to start?', css:'text-center', size:'x-small'});
									
									modal.body.makeNode('btns', 'buttonGroup', {css:'fluid'});
									
									modal.body.btns.makeNode('search', 'button', {text:'Search for a client', css:'pda-btn-primary pda-btn-x-large'})
										.notify('click', {
											type:'contactComponent-run',
											data:{
												run:function(){
													
													this.hide();
													
													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'companies',
															viewId:'table'
														}
													});
													
												}.bind(modal)
											}
										}, sb.moduleId);
										
									modal.body.btns.makeNode('or', 'div', {css:'or'});
									
									modal.body.btns.makeNode('create', 'button', {text:'Create a new client', css:'pda-btn-primary'})
										.notify('click', {
											type:'contactComponent-run',
											data:{
												run:function(){
													
													this.hide();
													
													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'companies',
															viewId:'create'
														}
													});
													
												}.bind(modal)
											}
										}, sb.moduleId);
									
									draw(modal);
																		
/*
									dom.makeNode('break', 'lineBreak', {});
									
									dom.makeNode('header', 'headerText', {text:'You can create a new contact under a client.<br />How would you like to start?', css:'text-center', size:'x-small'});
									
									dom.makeNode('btns', 'buttonGroup', {});
									
									dom.btns.makeNode('search', 'button', {text:'Search for a client', css:'pda-btn-primary pda-btn-x-large'})
										.notify('click', {
											type:'app-navigate-to',
											data:{
												itemId:'companies',
												viewId:'table'
											}
										}, sb.moduleId);
									
									dom.btns.makeNode('create', 'button', {text:'Create a new client', css:'pda-btn-primary'})
										.notify('click', {
											type:'app-navigate-to',
											data:{
												itemId:'companies',
												viewId:'create'
											}
										}, sb.moduleId);
									
									draw(dom);
*/
																											
									//createContactState([], dom, draw);
									
								}	
							},
							// Settings
							{
								id:'settings',
								type:'settings',
								title:'Settings',
								icon:'<i class="fa fa-cog"></i>',
								setup:[
									{
										object_type:'contact_types',
										name:'1. Contact Types'
									},
									{
										object_type:'contact_info_types',
										name:'2. Contact Info'
									}
								]
							}

						]
					}
				}
			});
			
			if(appConfig.instance == 'thelifebook' || appConfig.instance == 'rickyvoltz'){
				
				sb.notify({
					type:'register-application',
					data:{
						navigationItem:{
							id:'requests',
							title:'Requests',
							icon:'<i class="fa fa-book"></i>',
							views:[
								{
									id:'table',
									default:true,
									type:'table',
									title:'All Requests',
									icon:'<i class="fa fa-th-list"></i>',
									setup:{
										objectType:'requests',
										tableTitle:'<i class="fa fa-book"></i> Requests',
										childObjs:3,
										searchObjects:[
											{
												name:'First Name',
												value:'fname',
												join:'contact'
											},
											{
												name:'Last Name',
												value:'lname',
												join:'contact'
											},
											{
												name:'Church Name',
												value:'name',
												join:'company'
											},
											{
												name:'Shipping Street',
												value:'street'
											},
											{
												name:'Shipping City',
												value:'city'
											},
											{
												name:'Shipping State',
												value:'state'
											},
											{
												name:'Confirmation Code',
												value:'confirmation_code',
												join:'form_data'
											},
											{
												name:'New Request # (6xxxxx)',
												value:'object_uid'
											},
											{
												name:'Old Request # (5xxxxx)',
												value:'original_id'
											}
											
										],
										filters:function(callback){
						
											sb.data.db.obj.getWhere('users', {
												fname:{
													type:'or',
													values:['Joe', 'Tim', 'Rusty', 'Jeff']
												}
											}, function(users){
											
												var filters = {
														manager:{
															name:'Manager',
															type:'checkbox',
															all:true,
															field:'manager',
															options:[]
														},
														status:{
															name:'Status',
															type:'checkbox',
															all:true,
															field:'status',
															options:[
																{
																	name:'status',
																	label:'Shipped',
																	value:'Shipped',
																	checked:true
																},
																{
																	name:'status',
																	label:'Awaiting Shipment',
																	value:'Awaiting Shipment',
																	checked:true
																},
																{
																	name:'status',
																	label:'On Hold',
																	value:'On Hold',
																	checked:true
																},
																{
																	name:'status',
																	label:'Cancelled',
																	value:'Cancelled',
																	checked:true
																}
															]
														}
													};
													
												_.each(users, function(u){
												
													filters.manager.options.push({
														name:'manager',
														label:u.fname +' '+ u.lname,
														value:u.id,
														checked:true
													});
													
												});	
												
												callback(filters);
											
											});
											
										},
										headerButtons:{
											reload:{
												name:'Reload',
												css:'pda-btn-blue',
												action:function(){}
											}
										},
										rowSelection: true,
										multiSelectButtons:{
											cancel:{
												name:'<i class="fa fa-times"></i> Cancel',
												css:'pda-btn-red',
												domType:'modal',
												action:function(selectedObjs, dom){
													
													dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
													
													dom.body.patch();
													
													setTimeout(function(){
														
														var approvedObjs = _.reject(selectedObjs, {status:'Shipped'});
														approvedObjs = _.reject(selectedObjs, {status:'Cancelled'});
														
														dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Cancel '+ approvedObjs.length + ' request(s)?'});
																									
														dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
															type:'change-from-table',
															data:{
																type:'cancel',
																modal:dom,
																objects:approvedObjs
															}
														}, sb.moduleId);
														
														dom.footer.btns.makeNode('no', 'button', {css:'pda-btn-large pda-btn-red', text:'<i class="fa fa-times"></i> No'}).notify('click', {
															type:'change-from-table',
															data:{
																type:'close',
																modal:dom
															}
														}, sb.moduleId);
														
														dom.body.patch();
														dom.footer.patch();
																									
													}, 1000);
													
												}
											},
											hold:{
												name:'<i class="fa fa-pause"></i> Hold',
												css:'pda-btn-orange',
												domType:'modal',
												action:function(selectedObjs, dom){
													
													dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
													
													dom.body.patch();
													
													setTimeout(function(){
														
														var approvedObjs = _.reject(selectedObjs, {status:'Shipped'});
														approvedObjs = _.reject(selectedObjs, {status:'On Hold'});
														
														dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Hold '+ approvedObjs.length + ' request(s)?'});
																									
														dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
															type:'change-from-table',
															data:{
																type:'hold',
																modal:dom,
																objects:approvedObjs
															}
														}, sb.moduleId);
														
														dom.footer.btns.makeNode('no', 'button', {css:'pda-btn-large pda-btn-red', text:'<i class="fa fa-times"></i> No'}).notify('click', {
															type:'change-from-table',
															data:{
																type:'close',
																modal:dom
															}
														}, sb.moduleId);
														
														dom.body.patch();
														dom.footer.patch();
														
														crudModal = dom;
														
													}, 1000);
													
												}
											},
											approve:{
												name:'<i class="fa fa-check"></i> Approve',
												css:'pda-btn-green',
												domType:'modal',
												action:function(selectedObjs, dom){
													
													dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
													
													dom.body.patch();
													
													setTimeout(function(){
														
														var approvedObjs = _.reject(selectedObjs, {status:'Shipped'});
														approvedObjs = _.reject(selectedObjs, {status:'Awaiting Shipment'});
														
														dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Approve '+ approvedObjs.length + ' request(s)?'});
																									
														dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
															type:'change-from-table',
															data:{
																type:'approve',
																modal:dom,
																objects:approvedObjs
															}
														}, sb.moduleId);
														
														dom.footer.btns.makeNode('no', 'button', {css:'pda-btn-large pda-btn-red', text:'<i class="fa fa-times"></i> No'}).notify('click', {
															type:'change-from-table',
															data:{
																type:'close',
																modal:dom
															}
														}, sb.moduleId);
														
														dom.body.patch();
														dom.footer.patch();
														
														crudModal = dom;
														
													}, 1000);
													
												}
											}
										},
										rowLink:{
											type:'tab',
											header:function(obj){
												return obj.contact.fname +' '+ obj.contact.lname;
											},
											action:function(obj, dom, state, draw){
												
												singleState(obj.contact, dom, state, draw);
												
											}
										},
										visibleCols:{
											date_created:'Date Created',
											status:'Status',
											books:'Books',
											attention:'Attention',
											role:'Type',
											manager:'Manager',
											business_name:'Church Name',
											address:'Addresses',
											email:'Email',
											phone:'Phone',
											students:'Students',
											adults:'Adults',
											object_uid:'Request #'
										},
										cells: {
											phone:function(obj){
											
												var ret = '',
													count = 0;
												
												if(!obj.contact_obj){
													obj.contact_obj = {
														contact_info:[]
													};
												}
												
												if(obj.contact){
												
													_.each(obj.contact_obj.contact_info, function(o){
					
														if(o){
					
															if(o.type){
					
																if(o.type.data_type == 'phone'){
																	
																	if(count == 0){
																		
																		ret += o.title +': '+ sb.dom.formatPhone(o.info);
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.title +': '+ sb.dom.formatPhone(o.info);
																		
																	}
																	
																}
							
																if(o.type == 49 || o.type == 60){
							
																	if(count == 0){
																		
																		ret += o.title +': '+ sb.dom.formatPhone(o.info);
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.title +': '+ sb.dom.formatPhone(o.info);
																		
																	}
																						
																}
																
															}
															
														}
														
													});
													
													return ret;
												
												}
											
											},
											email:function(obj){
											
												var ret = '',
													count = 0;
												
												if(obj.contact){
												
													_.each(obj.contact.contact_info, function(o){
					
														if(o){
					
															if(o.type){
					
																if(o.type.data_type == 'email'){
																	
																	if(count == 0){
																		
																		ret += o.title +': '+ o.info;
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.title +': '+ o.info;
																		
																	}
																	
																}
							
																if(o.type == 48){
							
																	if(count == 0){
																		
																		ret += o.title +': '+ o.info;
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.title +': '+ o.info;
																		
																	}
																						
																}
																
															}
															
														}
														
													});
													
													return ret;
												
												}
											
											},
											address:function(obj){
											
												var ret = '',
													count = 0;
												
												if(obj.contact){									
													
													_.each(obj.contact.contact_info, function(o){

														if(o){
					
															if(o.type){
					
																if(o.type.data_type == 'address'){
																	
																	var streetTwo = '';
											
																	if(o.hasOwnProperty('street2') && o.street2 != ''){
																		streetTwo = ' '+o.street2;
																	}
							
																	if(count == 0){
																		
																		ret += o.title +': '+ o.street +', '+o.city+', '+o.state+' '+ o.zip;
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+o.title +': '+ o.street +', '+o.city+', '+o.state+' '+ o.zip;
																		
																	}
																	
																}
							
																if(o.type == 52 || o.type == 53){
																	
																	var streetTwo = '';
																	
																	if(o.hasOwnProperty('street2') && o.street2 != ''){
																		streetTwo = ' '+o.street2;
																	}

							
																	if(count == 0){
																		
																		ret += o.title +': '+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+o.title +': '+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																		
																	}
																						
																}
																
															}
															
														}
														
													});
												
												}
												
												return ret;
											
											},
											business_name: function(obj){
												if(obj.company){
													return obj.company.name;
												}
											},
											shipping: function(obj){
												return obj.street+'<br />'+obj.city+', '+obj.state+' '+obj.zip;
											},
											status: function(obj){
					
												switch(obj.status){
													
													case 'Cancelled':
													
														return '<div class="fluid ui mini red button">'+ obj.status +'</div>';
													
														break;
					
													case 'On Hold':
													
														return '<div class="fluid ui mini orange button">'+ obj.status +'</div>';
													
														break;
														
													case 'Awaiting Shipment':
													
														return '<div class="fluid ui mini green button">'+ obj.status +'</div>';
													
														break;
														
													case 'Shipped':
													
														return '<div class="fluid ui mini green button">'+ obj.status +'</div>';
													
														break;		
													
												}
											
											},
											shipped: function(obj){
												if(obj.shipped == 1){
													return 'Shipped';
												}else{
													return 'Not Shipped';
												}
											},
											date_created: function(obj){
												return moment(obj.date_created).format('M/D/YYYY hh:mm a');
											},
											shipped_date: function(obj){
												return moment(obj.shipped_date).fromNow();
											},
											role: function(obj){
					
												if(obj){
					
													if(obj.contact){
														if(obj.contact.type){
															return obj.contact.type.name;
														}
													}
												}
											},
											manager: function(obj){
												
												if(obj.contact){
													if(obj.contact.manager){
														return obj.contact.manager.fname +' '+ obj.contact.manager.lname;
													}
												}
												
												return 'Not selected';
												
											}
										},
										data:function(paged, callback){
											
											if(tableFilters.status == 'all'){
												
												sb.data.db.obj.getAll('requests', function(requests){
													
													_.each(requests.data, function(r){
																							
														if(r.contact){
															r.contact_obj = r.contact;
														}									
														
													});
													
													if(tableFilters.manager !== 'all'){
														
														var newData = [];
															
														_.each(requests.data, function(o){
															
															if(o.contact.manager.id == +tableFilters.manager){
																
																newData.push(o);
																
															}
															
														});
														
														requests.data = newData;
														requests.recordsFiltered = newData.length;
					
													}	
																														
													callback(requests);
													
												}, {
													books:true,
													students:true,
													adults:true,
													attention:true,
													object_uid:true,
													contact:{
														fname:true,
														lname:true,
														contact_info:{
															type:{
																data_type:true
															},
															info:true,
															title:true,
															street:true,
															city:true,
															state:true,
															zip:true
														},
														type:{
															name:true
														},
														manager:{
															fname:true,
															lname:true
														} 
													},
													company:{
														name:true
													},
													street:true,
													city:true,
													state:true,
													zip:true,
													status:true,
													shipped:true,
													shipped_date:true
												}, paged);
												
											}else{
					
												sb.data.db.obj.getWhere('requests', {status:tableFilters.status, childObjs:{
													books:true,
													students:true,
													adults:true,
													attention:true,
													object_uid:true,
													contact:{
														fname:true,
														lname:true,
														contact_info:{
															type:{
																data_type:true
															},
															info:true,
															title:true,
															street:true,
															city:true,
															state:true,
															zip:true
														},
														type:{
															name:true
														},
														manager:{
															fname:true,
															lname:true
														} 
													},
													company:{
														name:true
													},
													street:true,
													city:true,
													state:true,
													zip:true,
													status:true,
													shipped:true,
													shipped_date:true
												}, paged:paged}, function(requests){
													
													_.each(requests.data, function(r){
																							
														if(r.contact){
															r.contact_obj = r.contact;
														}
														
													});
													
													if(tableFilters.manager !== 'all'){
														
														var newData = [];
															
														_.each(requests.data, function(o){
															
															if(o.contact.manager.id == +tableFilters.manager){
																
																newData.push(o);
																
															}
															
														});
														
														requests.data = newData;
														requests.recordsFiltered = newData.length;	
					
													}	
																														
													callback(requests);
													
												});
												
											}
											
										}
									}								
								},
								{
									id:'sm-table',
									default:true,
									type:'table',
									title:'Supervisor Dashboard',
									icon:'<i class="fa fa-th-list"></i>',
									setup:{
										objectType:'requests',
										tableTitle:'<i class="fa fa-book"></i> Requests',
										childObjs:3,
										searchObjects:[
											{
												name:'First Name',
												value:'fname',
												join:'contact'
											},
											{
												name:'Last Name',
												value:'lname',
												join:'contact'
											},
											{
												name:'Church Name',
												value:'name',
												join:'company'
											},
											{
												name:'Shipping Street',
												value:'street'
											},
											{
												name:'Confirmation Code',
												value:'confirmation_code',
												join:'form_data'
											},
											{
												name:'New Request # (6xxxxx)',
												value:'object_uid'
											},
											{
												name:'Old Request # (5xxxxx)',
												value:'original_id'
											}
											
										],
										headerButtons:{
											reload:{
												name:'Reload',
												css:'pda-btn-blue',
												action:function(){}
											}
										},
										rowSelection: false,
										multiSelectButtons:false,
										rowLink:{
											type:'tab',
											header:function(obj){
												return obj.contact.fname +' '+ obj.contact.lname;
											},
											action:function(obj, dom, state, draw){
												
												singleState(obj.contact, dom, state, draw);
												
											}
										},
										visibleCols:{
											date_created:'Date Created',
											status:'Status',
											books:'Books',
											attention:'Attention',
											role:'Type',
											business_name:'Church Name',
											address:'Addresses',
											email:'Email',
											phone:'Phone',
											students:'Students',
											adults:'Adults',
											object_uid:'Request #'
										},
										cells: {
											phone:function(obj){
											
												var ret = '',
													count = 0;
												
												if(!obj.contact_obj){
													obj.contact_obj = {
														contact_info:[]
													};
												}
												
												if(obj.contact){
												
													_.each(obj.contact_obj.contact_info, function(o){
					
														if(o){
					
															if(o.type){
					
																if(o.type.data_type == 'phone'){
																	
																	if(count == 0){
																		
																		ret += o.title +': '+ sb.dom.formatPhone(o.info);
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.title +': '+ sb.dom.formatPhone(o.info);
																		
																	}
																	
																}
							
																if(o.type == 49 || o.type == 60){
							
																	if(count == 0){
																		
																		ret += o.title +': '+ sb.dom.formatPhone(o.info);
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.title +': '+ sb.dom.formatPhone(o.info);
																		
																	}
																						
																}
																
															}
															
														}
														
													});
													
													return ret;
												
												}
											
											},
											email:function(obj){
											
												var ret = '',
													count = 0;
												
												if(obj.contact){
												
													_.each(obj.contact.contact_info, function(o){
					
														if(o){
					
															if(o.type){
					
																if(o.type.data_type == 'email'){
																	
																	if(count == 0){
																		
																		ret += o.title +': '+ o.info;
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.title +': '+ o.info;
																		
																	}
																	
																}
							
																if(o.type == 48){
							
																	if(count == 0){
																		
																		ret += o.title +': '+ o.info;
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.title +': '+ o.info;
																		
																	}
																						
																}
																
															}
															
														}
														
													});
													
													return ret;
												
												}
											
											},
											address:function(obj){
											
												var ret = '',
													count = 0;
												
												if(obj.contact){									
													
													_.each(obj.contact.contact_info, function(o){
					
														if(o){
					
															if(o.type){
					
																if(o.type.data_type == 'address'){
																	
																	var streetTwo = '';
											
																	if(o.hasOwnProperty('street2') && o.street2 != ''){
																		streetTwo = ' '+o.street2;
																	}
							
																	if(count == 0){
																		
																		ret += o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																		
																	}
																	
																}
							
																if(o.type == 52 || o.type == 53){
																	
																	var streetTwo = '';
																	
																	if(o.hasOwnProperty('street2') && o.street2 != ''){
																		streetTwo = ' '+o.street2;
																	}

							
																	if(count == 0){
																		
																		ret += o.title +': '+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																		
																		count++;
																		
																	}else{
																		
																		ret += '<br />'+o.title +': '+ o.street + streetTwo +', '+o.city+', '+o.state+' '+ o.zip;
																		
																	}
																						
																}
																
															}
															
														}
														
													});
													
													return ret;
												
												}
											
											},
											business_name: function(obj){
												if(obj.company){
													return obj.company.name;
												}
											},
											shipping: function(obj){
												return obj.street+'<br />'+obj.city+', '+obj.state+' '+obj.zip;
											},
											status: function(obj){
					
												switch(obj.status){
													
													case 'Cancelled':
													
														return '<span class="label label-danger">'+ obj.status +'</span>';
													
														break;
					
													case 'On Hold':
													
														return '<span class="label label-warning">'+ obj.status +'</span>';
													
														break;
														
													case 'Awaiting Shipment':
													
														return '<span class="label label-success">'+ obj.status +'</span>';
													
														break;
														
													case 'Shipped':
													
														return '<span class="label label-success">'+ obj.status +'</span>';
													
														break;		
													
												}
											
											},
											shipped: function(obj){
												if(obj.shipped == 1){
													return 'Shipped';
												}else{
													return 'Not Shipped';
												}
											},
											date_created: function(obj){
												return moment(obj.date_created).format('M/D/YYYY hh:mm a');
											},
											shipped_date: function(obj){
												return moment(obj.shipped_date).fromNow();
											},
											role: function(obj){
					
												if(obj){
					
													if(obj.contact){
														if(obj.contact.type){
															return obj.contact.type.name;
														}
													}
												}
											},
											manager: function(obj){
												
												if(obj.contact){
													if(obj.contact.manager){
														return obj.contact.manager.fname +' '+ obj.contact.manager.lname;
													}
												}
												
												return 'Not selected';
												
											}
										},
										data:function(paged, callback){
																						
											sb.data.db.obj.getWhere('requests', {manager: +sb.data.cookie.userId, status:'On Hold', childObjs:{
													books:true,
													students:true,
													adults:true,
													attention:true,
													object_uid:true,
													contact:{
														fname:true,
														lname:true,
														contact_info:{
															type:{
																data_type:true
															},
															info:true,
															title:true,
															street:true,
															city:true,
															state:true,
															zip:true
														},
														type:{
															name:true
														},
														manager:{
															fname:true,
															lname:true
														} 
													},
													company:{
														name:true
													},
													street:true,
													city:true,
													state:true,
													zip:true,
													status:true,
													shipped:true,
													shipped_date:true
												}, paged:paged}, function(requests){
													
												_.each(requests.data, function(r){
																						
													if(r.contact){
														r.contact_obj = r.contact;
													}									
													
												});

/*
												requests.data = _.reject(requests.data, function(obj){ return obj.contact.manager.id != +sb.data.cookie.userId; });
												requests.recordsFiltered = requests.data.length;
												requests.recordsTotal = requests.data.length;
*/
																													
												callback(requests);
												
											});
											
										}
									}								
								},
								{
									id:'shipmentNotes',
									type:'custom',
									title:'Shipping Notes',
									icon:'<i class="fa fa-comment"></i>',
									dom:function(dom, state, draw){
										
										draw(false);
										
										dom.patch();
										
										sb.notify({
											type:'show-note-list-global',
											data:{
												domObj:dom,
												tableTitle:'<i class="fa fa-comment"></i> Shipping Notes',
												noteTypes:[1153600]
											}
										});
										
									}
								},
								{
									id:'settings',
									type:'settings',
									title:'Settings',
									icon:'<i class="fa fa-cog"></i>',
									setup:[
										{
											object_type:'request_cancellations',
											name:'Cancellation Reasons'
										},
										{
											object_type:'incoming_form_source_vendor',
											name:'Advertising Vendors'
										},
										{
											object_type:'incoming_form_sources',
											name:'Advertising Sources',
											action:function(domObj){
				
												var comp = {};
												
												comp.settings = sb.createComponent('crud-table');
				
												comp.settings.notify({
													type: 'display-crud-table-paged',
													data: {
														datatable:{
															order: [1, 'desc']
														},
														buttons: {
															create: true,
															view: false,
															edit: true,
															erase: true,
															colVis:false,
															duplicate:true
														},
														cells: {
															destination_url:function(obj){
																return 'https://thelifebook.com/request?tlbsource='+obj.cookie_name;
															},
															date_created:function(obj){
																return moment(obj.date_created).format('M/D/YYYY h:MM a');
															}
														},
														cols: {
															destination_url:'Destination URL',
															date_created:'Date Created'
														},
														data: function(callback, paged){
															
															sb.data.db.obj.getAll('incoming_form_sources', function(objs){
																	
																callback(objs);
																
															}, 1, paged);
															
														},
														domObj: domObj,
														objectType:'incoming_form_sources',
														visibleCols: ['date_created', 'name', 'destination_url', 'redirect', 'banner_text']
													}
												});
											
											}
										}
									]
								},
								{
									id:'tags',
									type:'tags', // table, calendar, tags, custom, quickaction
									title:'Tags',
									icon:'<i class="fa fa-tags"></i>',
									setup:{
										type:'contacts',
										childObjs:2,
										resultList:function(dom, obj){
											
											var typeString = '<i>No type selected</i>',
												companyString = '<i>No client</i>';
											
											if(obj.type){
												typeString = obj.type.name;
											}
																	
											if(obj.company){
												companyString = obj.company.name;
											}						
																	
											dom.makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-gray pda-background-gray'});
											
											dom.cont.makeNode('name', 'headerText', {text:obj.fname +' '+ obj.lname +' <i class="fa fa-eye fa-2x pull-right"></i>', size:'x-small'});
											
											dom.cont.makeNode('type', 'text', {text:'Type: '+ typeString +'<br />Company: '+ companyString});
											
											dom.makeNode('break', 'lineBreak', {});
											
											dom.cont.notify('click', {
												type:'app-navigate-to',
												data:{
													itemId:'contacts',
													viewId:{
														id:'single-'+obj.id,
														type:'table-single-item',
														title:obj.fname +' '+ obj.lname,
														icon:'<i class="fa fa-user"></i>',
														setup:{
															objectType:'contacts'
														},
														dom:singleState,
														rowObj:obj,
														removable:true,
														parent:'table'
													}
												}
											}, sb.moduleId);
											
											return dom;						
											
										}
									}
								}
							]
						}
					}
				});
				
			}
			
			if(appConfig.instance == 'rickyvoltz' || appConfig.instance == 'thelifebookau'){
				
				sb.notify({
					type:'register-application',
					data:{
						navigationItem:{
							id:'int-requests',
							title:'Life Book Orders',
							icon:'<i class="fa fa-book"></i>',
							views:[
								{
									id:'table',
									default:true,
									type:'table',
									title:'All Orders',
									icon:'<i class="fa fa-th-list"></i>',
									setup:{
										objectType:'lifebook_international_orders',
										tableTitle:'<i class="fa fa-book"></i> Requests',
										childObjs:3,
										searchObjects:[
											{
												name:'First Name',
												value:'fname',
												join:'contact'
											},
											{
												name:'Last Name',
												value:'lname',
												join:'contact'
											}
											
										],
										filters:function(callback){
						
											sb.data.db.obj.getAll('users', function(users){
											
												var filters = {
														manager:{
															name:'Manager',
															type:'checkbox',
															all:true,
															field:'contact.manager',
															options:[]
														}
													};
													
												_.each(users, function(u){
												
													filters.manager.options.push({
														name:'manager',
														label:u.fname +' '+ u.lname,
														value:u.id,
														checked:true
													});
													
												});	
												
												callback(filters);
											
											});
											
										},
										headerButtons:{
											reload:{
												name:'Reload',
												css:'pda-btn-blue',
												action:function(){}
											}
										},
										rowSelection: true,
										multiSelectButtons:{
											fulfill:{
												name:'<i class="fa fa-check"></i> Mark as fulfilled',
												css:'pda-btn-green',
												domType:'modal',
												action:function(selectedObjs, dom){
													
													dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
													
													dom.body.patch();
													
													setTimeout(function(){
														
														var approvedObjs = _.reject(selectedObjs, {status:'Delivered'});
																												
														dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Mark '+ approvedObjs.length + ' order(s) as fulfilled?'});
																									
														dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
															type:'contactComponent-run',
															data:{
																run:function(dom, objects){
																	
																	dom.footer.btns.yes.loading();								
																																		
																	objsToUpdate = _.map(objects, function(obj){
																		
																		obj.status = 'Delivered';
																		
																		return obj;
																		
																	}, []);
																																		
																	sb.data.db.obj.update('lifebook_international_orders', objsToUpdate, function(updated){
																		
																		var notesToCreate = _.map(updated, function(obj){
																			
																			return {
																				type_id: obj.contact,
																				type: 'contacts',
																				note: 'Order #'+ obj.object_uid +' has been marked as Delivered.',
																				note_type:1153211,
																				author: sb.data.cookie.get('uid'),
																				notifyUsers: []
																			};
																			
																		}, []);

																		sb.data.db.obj.create('notes', notesToCreate, function(newNote){
																		
																			dom.hide();
																			
																			sb.notify({
																				type:'app-redraw',
																				data:{}
																			});
																			
																		});
																		
																	});
																																		
																}.bind({}, dom, approvedObjs)
															}
														}, sb.moduleId);
														
														dom.footer.btns.makeNode('no', 'button', {css:'pda-btn-red', text:'<i class="fa fa-times"></i> No'}).notify('click', {
															type:'change-from-table',
															data:{
																type:'close',
																modal:dom
															}
														}, sb.moduleId);
														
														dom.body.patch();
														dom.footer.patch();
																									
													}, 1000);
													
												}
											},
											notDelivered:{
												name:'<i class="fa fa-times"></i> Mark as not delivered',
												css:'pda-btn-orange',
												domType:'modal',
												action:function(selectedObjs, dom){
													
													dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
													
													dom.body.patch();
													
													setTimeout(function(){
														
														var approvedObjs = _.where(selectedObjs, {status:'Delivered'});
																												
														dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Mark '+ approvedObjs.length + ' order(s) as not fulfilled?'});
																									
														dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-green', text:'<i class="fa fa-check"></i> Yes'}).notify('click', {
															type:'contactComponent-run',
															data:{
																run:function(dom, objects){
																	
																	dom.footer.btns.yes.loading();								
																																		
																	objsToUpdate = _.map(objects, function(obj){
																		
																		obj.status = 'Not Delivered';
																		
																		return obj;
																		
																	}, []);
																																		
																	sb.data.db.obj.update('lifebook_international_orders', objsToUpdate, function(updated){
																		
																		var notesToCreate = _.map(updated, function(obj){
																			
																			return {
																				type_id: obj.contact,
																				type: 'contacts',
																				note: 'Order #'+ obj.object_uid +' has been marked as Not Delivered.',
																				note_type:1153211,
																				author: sb.data.cookie.get('uid'),
																				notifyUsers: []
																			};
																			
																		}, []);

																		sb.data.db.obj.create('notes', notesToCreate, function(newNote){
																		
																			dom.hide();
																			
																			sb.notify({
																				type:'app-redraw',
																				data:{}
																			});
																			
																		});
																		
																	});
																																		
																}.bind({}, dom, approvedObjs)
															}
														}, sb.moduleId);
														
														dom.footer.btns.makeNode('no', 'button', {css:'pda-btn-red', text:'<i class="fa fa-times"></i> No'}).notify('click', {
															type:'change-from-table',
															data:{
																type:'close',
																modal:dom
															}
														}, sb.moduleId);
														
														dom.body.patch();
														dom.footer.patch();
																									
													}, 1000);
													
												}
											}
										},
										rowLink:{
											type:'tab',
											header:function(obj){
												return obj.contact.fname +' '+ obj.contact.lname;
											},
											action:function(obj, dom, state, draw){
												
												singleState(obj.contact, dom, state, draw);
												
											}
										},
										visibleCols:{
											name:'Contact Name',
											amount:'Books Ordered',
											status:'Status',
											manager:'Point Person',
											order_date:'Date Ordered',
											created_by:'Ordered By'
											},
										cells: {
											name:function(obj){
												return obj.contact.fname +' '+ obj.contact.lname;
											},
											order_date:function(obj){
												return moment(obj.date_created).format('M/D/YYYY');
											},
											created_by:function(obj){
												return obj.created_by.fname +' '+ obj.created_by.lname;
											},
											manager:function(obj){
												return obj.manager.fname +' '+ obj.manager.lname;
											},
											amount:function(obj){
												return '<p class="center text-center">'+obj.amount+'</p>';
											},
											status:function(obj){
												
												if(obj.status){
													
													if(obj.status == 'Delivered'){
														
														return '<span class="label label-success">Delivered</span>';
														
													}else{
														
														return '<span class="label label-warning">Not Delivered</span>';
														
													}
													
												}else{
													
													return '<span class="label label-warning">Not Delivered</span>';
													
												}
												
											}
										},
										data:function(paged, callback){
											
											sb.data.db.obj.getAll('lifebook_international_orders', function(requests){

												callback(requests);
												
											}, 1, paged);
											
										}
									}								
								},
								{
									id:'tags',
									type:'tags', // table, calendar, tags, custom, quickaction
									title:'Tags',
									icon:'<i class="fa fa-tags"></i>',
									setup:{
										type:'contacts',
										childObjs:2,
										resultList:function(dom, obj){
											
											var typeString = '<i>No type selected</i>',
												companyString = '<i>No client</i>';
											
											if(obj.type){
												typeString = obj.type.name;
											}
																	
											if(obj.company){
												companyString = obj.company.name;
											}						
																	
											dom.makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-gray pda-background-gray'});
											
											dom.cont.makeNode('name', 'headerText', {text:obj.fname +' '+ obj.lname +' <i class="fa fa-eye fa-2x pull-right"></i>', size:'x-small'});
											
											dom.cont.makeNode('type', 'text', {text:'Type: '+ typeString +'<br />Company: '+ companyString});
											
											dom.makeNode('break', 'lineBreak', {});
											
											dom.cont.notify('click', {
												type:'app-navigate-to',
												data:{
													itemId:'contacts',
													viewId:{
														id:'single-'+obj.id,
														type:'table-single-item',
														title:obj.fname +' '+ obj.lname,
														icon:'<i class="fa fa-user"></i>',
														setup:{
															objectType:'contacts'
														},
														dom:singleState,
														rowObj:obj,
														removable:true,
														parent:'table'
													}
												}
											}, sb.moduleId);
											
											return dom;						
											
										}
									}
								},
								{
									id:'settings',
									type:'settings',
									title:'Settings',
									icon:'<i class="fa fa-cog"></i>',
									setup:[
										{
											object_type:'life_book_system',
											name:'1. System Settings'
										}
									]
								}
							]
						}
					}
				});
				
			}
			
			sb.listen({
				'add-contact-info-to-contact':this.addContactInfo,
				'contactComponent-run': this.run,
				'contact-object-updated':this.contactUpdated,
				'create-company-from-form':this.createCompany,
				'create-contact':this.createContact,
				'create-contact-from-form':this.create,
				'delete-contact-object':this.erase,
				'new-request-created':this.requestsUpdated,
				'requests-object-updated':this.requestsUpdated,
				'search-in-google':this.searchInGoogle,
				'update-contact-note-feed':this.updateNotes,
				'update-contact-task-list':this.updateTasks,
				'view-contacts-table':this.viewTable,
				'view-single-contact':this.viewSingle
			});
			
		},
		
		destroy: function(){
			
			_.each(components, function(comp){
				comp.destroy();
			});
			
			ui = {};
			tableUI = {};
			components = {};
			singleComponents = {};
			contactInfoTypes = [];
		},
		
		addContactInfo: function(data){
			
			if(!data.create){
				
				sb.notify({
					type:'add-contact-info-to-contact',
					data:data
				});
				
			}else{
				
				data.dom.body.cont.btns.makeNode('save', 'button', {css:'pda-btn-primary', text:'Saving <i class="fa fa-circle-o-notch fa-spin"></i>'});
				data.dom.body.cont.btns.makeNode('skip', 'button', {css:'pda-btn-blue', text:'Skip this step <i class="fa fa-arrow-right"></i>'});
				
				data.dom.body.cont.btns.patch();
				
				var form = data.form.process().fields;
				
				var company = {
						name:form.name.value,
						manager:form.manager.value
					};				
				
			}
									
		},
		
		contactUpdated: function(data){
			
			if(appConfig.instance == 'thelifebook'){
								
				sb.data.db.obj.getById('contacts', data.contactId, function(obj){
					
					var mailchimpMember = {
							listId:'5983ed1a31',
							member:{
								email_address:'',
								status:'subscribed',
								merge_fields:{
									FNAME:obj.fname,
									LNAME:obj.lname,
									SHIPPING:{},
									SHIPPED:'No',
									APPROVED:'No',
									PHONE:'',
									CHURCHPHON:'',
									CHURCHADDR:{},
									ADULTS:0,
									STUDENTS:0,
									REQUESTS:1,
									TYPE:obj.type.name,
									DENOM:'',
									ZIP:'',
									MANAGER:obj.manager.fname +' '+ obj.manager.lname,
									RECENT:'',
									RECENTSIZE:0
								}
							}
						};

					_.each(obj.contact_info, function(info){

						if(info.is_primary == 'yes'){
							
							switch(info.type.data_type){
								
								case 'other':
									
									switch(info.title){
										
										case 'Adults':
											
											mailchimpMember.member.merge_fields.ADULTS = +info.info;
											
											break;
											
										case 'Students':
											
											mailchimpMember.member.merge_fields.STUDENTS = +info.info;
											
											break;
											
										case 'Denomination':
											
											mailchimpMember.member.merge_fields.DENOM = info.info;
											
											break;		
										
									}
								
									break;
																			
								case 'email':
								
									mailchimpMember.member.email_address = info.info;
								
									break;
																													
								case 'address':
									
									if(info.name == 'Shipping Address'){
										
										//orderObj.order.customer.merge_fields.SHIPPING = info.street +', '+ info.city +', '+ info.state +' '+ info.zip;
										mailchimpMember.member.merge_fields.SHIPPING.addr1 = info.street;
										mailchimpMember.member.merge_fields.SHIPPING.addr2 = '';
										mailchimpMember.member.merge_fields.SHIPPING.city = info.city;
										mailchimpMember.member.merge_fields.SHIPPING.state = info.state;
										mailchimpMember.member.merge_fields.SHIPPING.zip = info.zip;
										
										
										mailchimpMember.member.merge_fields.ZIP = info.zip;
									}
									
									if(info.name == 'Church Address'){
										
										//orderObj.order.customer.merge_fields.CHURCHADDR = info.street +', '+ info.city +', '+ info.state +' '+ info.zip;
										mailchimpMember.member.merge_fields.CHURCHADDR.addr1 = info.street;
										mailchimpMember.member.merge_fields.CHURCHADDR.addr2 = '';
										mailchimpMember.member.merge_fields.CHURCHADDR.city = info.city;
										mailchimpMember.member.merge_fields.CHURCHADDR.state = info.state;
										mailchimpMember.member.merge_fields.CHURCHADDR.zip = info.zip;
										
									}
								
									break;	
									
								case 'phone':
									
									if(info.name == 'Cell Phone'){
										
										mailchimpMember.member.merge_fields.PHONE = info.info;
										
									}
									
									if(info.name == 'Church Phone'){
										
										mailchimpMember.member.merge_fields.CHURCHPHON = info.info;
										
									}
								
									break;		
								
							}
						
						}
						
					});	

					sb.data.db.controller('updateMailchimpListMember', mailchimpMember, function(done){

						data.modal.hide();
						
						if(data.alert){
							swal.close();
						}
						
					});
					
				}, 3);
				
			}else{

				data.modal.hide();
				
				if(data.alert){
					swal.close();
				}
			
			}
			
		},
		
		// create a new contact
		create: function(data){

			if(data.form.process().completed == false){
				
				sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
				
				return;
				
			}
						
			data.dom.panel.header.btns.makeNode('save', 'button', {css:'pda-btn-primary', text:'Saving <i class="fa fa-circle-o-notch fa-spin"></i>'});
			
			data.dom.panel.header.btns.patch();
			
			var form = data.form.process().fields;
			
			var contact = {
					fname:form.fname.value,
					lname:form.lname.value,
					manager:form.manager.value,
					type:form.type.value,
					company:companyId
				};
				
			if(data.state && data.state.tagged_with){
				
				contact.tagged_with = data.state.tagged_with;
				
			}
							
			sb.data.db.obj.create('contacts', contact, function(created){

				data.dom.panel.header.empty();
				data.dom.panel.header.patch();
				
				if(companyId > 0){
					
					data.dom.panel.body.cont.makeNode('btns', 'buttonGroup', {});
					
					data.dom.panel.body.cont.patch();
					
					sb.notify({
						type:'create-company-from-form',
						data:{
							form:data.dom.panel.body.cont.form,
							dom:data.dom,
							state:data.state,
							contact:created,
							create:false
						}
					});
					
				}else{
					
					data.dom.panel.body.cont.makeNode('header', 'headerText', {text:'Add a company', size:'small'});
				
					data.dom.panel.body.cont.makeNode('form', 'form', {
						name:{
							type:'text',
							name:'name',
							label:'Company Name'
						},
						manager:{
							type:'hidden',
							name:'manager',
							value:contact.manager
						}
					});
					
					data.dom.panel.body.cont.makeNode('btns', 'buttonGroup', {});
					
					data.dom.panel.body.cont.btns.makeNode('save', 'button', {css:'pda-btn-green', text:'Save and continue <i class="fa fa-floppy-o"></i>'}).notify('click', {
						type:'create-company-from-form',
						data:{
							form:data.dom.panel.body.cont.form,
							dom:data.dom,
							contact:created,
							state:data.state,
							create:true
						}
					}, sb.moduleId);
					data.dom.panel.body.cont.btns.makeNode('skip', 'button', {css:'pda-btn-blue', text:'Skip this step <i class="fa fa-arrow-right"></i>'}).notify('click', {
						type:'create-company-from-form',
						data:{
							form:data.dom.panel.body.cont.form,
							dom:data.dom,
							contact:created,
							state:data.state,
							create:false
						}
					}, sb.moduleId);
					
					data.dom.panel.body.cont.patch();
					
				}
				
			}, 1);
						
		},
		
		createContact: function(data){
						
			createContactState([], data.dom);
			
		},

		// create a new company
		createCompany: function(data){

			var onSave = function(contact){

				sb.notify({
					type:'app-navigate-to',
					data:{
						itemId:'contacts',
						viewId:{
							id:'single-'+contact.id,
							type:'table-single-item',
							title:contact.fname +' '+ contact.lname,
							icon:'<i class="fa fa-user"></i>',
							setup:{
								objectType:'contacts'
							},
							dom:singleState,
							rowObj:contact,
							removable:true,
							parent:'table'
						}
					}
				}, sb.moduleId);

				return;
				
			};
			
			if(data.hasOwnProperty('state')){
			
				if(data.state.onSave && typeof data.state.onSave == 'function')
					onSave = data.state.onSave;
				
			}
						
			data.dom.panel.body.cont.btns.makeNode('save', 'button', {css:'pda-btn-primary', text:'Saving <i class="fa fa-circle-o-notch fa-spin"></i>'});
			data.dom.panel.body.cont.btns.makeNode('skip', 'button', {css:'pda-btn-blue', text:'Skip this step <i class="fa fa-arrow-right"></i>'});
			
			data.dom.panel.body.cont.btns.patch();
			
			if(companyId > 0){
				
				var company = {
						name:'',
						manager:''
					};
				
			}else{
				
				var form = data.form.process().fields;
				
				var company = {
						name:form.name.value,
						manager:form.manager.value
					};
					
			}

			if(data.state && data.state.tagged_with){
				
				company.tagged_with = data.state.tagged_with;
				
			}
								
			function createOrSkip(company, contact, create, callback){
				
				if(create){
					
					// create the company
					// update the contact
					sb.data.db.obj.create('companies', company, function(created){

						sb.data.db.obj.update('contacts', {id:contact.id, company:created.id}, function(updatedContact){
						
							callback(updatedContact);
						
						}, 1);
					
					}, 1);
					
				}else{
					
					callback(contact);
					
				}
				
			}
			
			createOrSkip(company, data.contact, data.create, function(contact){
				
				data.dom.panel.body.cont.empty();
				
				data.dom.panel.body.cont.makeNode('header', 'headerText', {text:'Add some contact information', size:'small'});
				
				data.dom.panel.body.cont.makeNode('info', 'container', {});
				
				data.dom.panel.body.cont.makeNode('btns', 'buttonGroup', {});
				
				data.dom.panel.body.cont.btns.makeNode('save', 'button', {css:'pda-btn-green', text:'Save and finish <i class="fa fa-check"></i>'}).notify('click', {
					type: 'contactComponent-run',
					data: {
						run:onSave.bind(null, contact)
					}
				}, sb.moduleId);

				data.dom.panel.body.cont.btns.makeNode('skip', 'button', {css:'pda-btn-blue', text:'Skip and finish <i class="fa fa-arrow-right"></i>'}).notify('click', {
					type: 'contactComponent-run',
					data: {
						run:onSave.bind(null, contact)
					}
				}, sb.moduleId);
								
				data.dom.panel.body.cont.patch();
				
				components.contactInfo.notify({
					type:'start-contact_info-component',
					data:{
						domObj:data.dom.panel.body.cont.info
					}
				});
				components.contactInfo.notify({
					type:'show-contact-info-column',
					data:{
						contactTypeId:data.contact.type.id,
						contactId:data.contact.id
					}
				});
				
			}, 1000);
									
		},
		
		// erase a single contact
		erase: function(data){
			
			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: ''
			}, function(resp){
				
				if(resp){
				
					swal.disableButtons();
					
					// get all contact info, tasks, and notes
					sb.data.db.obj.getWhere('contact_info', {object_id:data.object.id}, function(contactInfo){
						
						var objsToDelete = [
								{
									objectType:'contacts',
									id:data.object.id
								}
							];
						
						_.each(contactInfo, function(o){
							
							objsToDelete.push(
								{
									objectType:'contact_info',
									id:o.id
								}
							);
							
						});
						
						
						
						sb.data.db.obj.getWhere('tasks', {object_id:data.object.id}, function(tasks){
													
							_.each(tasks, function(o){
							
								objsToDelete.push(
									{
										objectType:'tasks',
										id:o.id
									}
								);
								
							});
							
							sb.data.db.obj.getWhere('notes', {object_id:data.object.id}, function(notes){
													
								_.each(notes, function(o){
							
									objsToDelete.push(
										{
											objectType:'notes',
											id:o.id
										}
									);
									
								});
																
								sb.dom.alerts.ask({
									title: objsToDelete.length + ' object(s) to delete. Continue?',
									text: 'Nothing has been deleted yet. You can still cancel by selecting \'No\''
								}, function(resp){
									
									if(resp){
										
										swal.disableButtons();
										
										function deleteObjects(allObjects, callback, count){
											
											if(!count){
												count = 0;
											}
											
											sb.data.db.obj.erase(allObjects[count].objectType, allObjects[count].id, function(done){
												
												count++;
												
												if(count == allObjects.length){
													
													callback(count);
													
												}else{
													
													deleteObjects(allObjects, callback, count);
													
												}
												
											});
											
										}
										
										deleteObjects(objsToDelete, function(deleted){
											
											sb.dom.alerts.alert('Success!', deleted +' object(s) deleted.', 'success');
											
											if(+sb.data.cookie.userId == 11){
												
												var emailAddress = '';
												_.each(obj.contact_info, function(info){
								
													if(info.is_primary == 'yes'){
														
														switch(info.type.data_type){
																										
															case 'email':
															
																emailAddress = info.info;
															
																break;
															
														}
													
													}
													
												});
												
												sb.data.db.controller('updateMailchimpListMember', {listId:'5983ed1a31', member:{email_address:emailAddress}}, function(done){
								
													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'contacts',
															viewId:'table'
														}
													});				
													
												});
												
											}else{
												
												if(data.hasOwnProperty('state') && typeof data.state.onDelete == 'function'){
													
													data.state.onDelete();
													
												} else {
												
													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'contacts',
															viewId:'table'
														}
													});
												
												}
												
											}
											
										});
										
									}	
								
								});
								
							});
							
						});
						
					});
				
				}
				
			});
						
		},
		
		// handle requests object updates
		requestsUpdated: function(data){
			
			tableUI.state.show(data.object.contact.id);
			
		},
		
		run: function(data){

			data.run(data);
			
		},
		
		// opens the search link in a new page
		searchInGoogle: function(data){
			
			window.open(data.link, '_blank');
			
		},
		
		// update a single contact
		update: function(data){

			data.dom.btns.save.loading();
						
			var form = data.form.process().fields,
				formIsComplete = data.form.process();
				
			if(formIsComplete.completed === false){
				
				data.dom.body.btns.save.loading(false);
				
				sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
				
				return
				
			}
			
			var contact = {
					id: +form.id.value,
					fname:form.fname.value,
					lname:form.lname.value,
					manager: +form.manager.value,
					sales_person: +form.sales_person.value,
					type: +form.type.value
				},
				contactInfo = [];

			if(data.contactInfoForm){
			
				contactInfoForm = data.contactInfoForm.process().fields;

				_.each(data.object.contact_info, function(info){
					
					if(info){
						
						if(info.street){
							
							var formValues = data.addressForms['cont-'+info.id].cont.form.process().fields

							contactInfo.push({
								id:info.id,
								info:formValues['street-'+info.id].value,
								street:formValues['street-'+info.id].value,
								city:formValues['city-'+info.id].value,
								state:formValues['state-'+info.id].value,
								zip:formValues['zip-'+info.id].value,
								country:formValues['country-'+info.id].value
							});
							
						}else{
							
							if(contactInfoForm['info-'+info.id]){
								contactInfo.push({
									id:info.id,
									info:contactInfoForm['info-'+info.id].value
								});	
							}
							
						}
						
					}
					
				});
				
			}	

			sb.data.db.obj.update('contact_info', contactInfo, function(updatedInfo){	
				
				sb.data.db.obj.update('contacts', contact, function(updated){

					updated.contact_info = updatedInfo;
					
					obj = updated;
					
					if(data.state){
						data.state.object = updated;
					}
					
					//if(+sb.data.cookie.userId == 11){
					if(appConfig.instance == 'thelifebook'){
						
						var mailchimpMember = {
								listId:'5983ed1a31',
								member:{
									email_address:'',
									status:'subscribed',
									merge_fields:{
										FNAME:obj.fname,
										LNAME:obj.lname,
										SHIPPING:{},
										PHONE:'',
										CHURCHPHON:'',
										CHURCHADDR:{},
										ADULTS:0,
										STUDENTS:0,
										REQUESTS:1,
										TYPE:obj.type.name,
										DENOM:'',
										ZIP:'',
										MANAGER:obj.manager.fname +' '+ obj.manager.lname,
										RECENT:'',
										RECENTSIZE:0
									}
								}
							};
							
						_.each(obj.contact_info, function(info){
							
							if(info.is_primary == 'yes'){
								
								switch(info.type.data_type){
									
									case 'other':
										
										switch(info.title){
											
											case 'Adults':
												
												mailchimpMember.member.merge_fields.ADULTS = +info.info;
												
												break;
												
											case 'Students':
												
												mailchimpMember.member.merge_fields.STUDENTS = +info.info;
												
												break;
												
											case 'Denomination':
												
												mailchimpMember.member.merge_fields.DENOM = info.info;
												
												break;		
											
										}
									
										break;
																				
									case 'email':
									
										mailchimpMember.member.email_address = info.info;
									
										break;
																														
									case 'address':
										
										if(info.name == 'Shipping Address'){
											
											//orderObj.order.customer.merge_fields.SHIPPING = info.street +', '+ info.city +', '+ info.state +' '+ info.zip;
											mailchimpMember.member.merge_fields.SHIPPING.addr1 = info.street;
											mailchimpMember.member.merge_fields.SHIPPING.addr2 = '';
											mailchimpMember.member.merge_fields.SHIPPING.city = info.city;
											mailchimpMember.member.merge_fields.SHIPPING.state = info.state;
											mailchimpMember.member.merge_fields.SHIPPING.zip = info.zip;
											
											
											mailchimpMember.member.merge_fields.ZIP = info.zip;
										}
										
										if(info.name == 'Church Address'){
											
											//orderObj.order.customer.merge_fields.CHURCHADDR = info.street +', '+ info.city +', '+ info.state +' '+ info.zip;
											mailchimpMember.member.merge_fields.CHURCHADDR.addr1 = info.street;
											mailchimpMember.member.merge_fields.CHURCHADDR.addr2 = '';
											mailchimpMember.member.merge_fields.CHURCHADDR.city = info.city;
											mailchimpMember.member.merge_fields.CHURCHADDR.state = info.state;
											mailchimpMember.member.merge_fields.CHURCHADDR.zip = info.zip;
											
										}
									
										break;	
										
									case 'phone':
										
										if(info.name == 'Cell Phone'){
											
											mailchimpMember.member.merge_fields.PHONE = info.info;
											
										}
										
										if(info.name == 'Church Phone'){
											
											mailchimpMember.member.merge_fields.CHURCHPHON = info.info;
											
										}
									
										break;		
									
								}
							
							}
							
						});	

						sb.data.db.controller('updateMailchimpListMember', mailchimpMember, function(done){
							
							sb.notify({
								type:'app-redraw',
								data:{}
							});				
							
						});

						
					}else{
						
						if(data.state.hasOwnProperty('appSettings') && data.state.appSettings.apps.integrations.mailchimp){
							
							if(data.state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){
							
								sb.data.db.controller('updateSingleContactWithMailchimp', {id:updated.id}, function(done){
									
									if(done.status == 404){
																					
										if(data.draw){
											singleState(updated, data.dom, data.state, data.draw);
										}else{
											sb.notify({
												type:'app-redraw',
												data:{}
											});
										}
										
									}else{
										
										if(data.draw){
											singleState(updated, data.dom, data.state, data.draw);
										}else{
											sb.notify({
												type:'app-redraw',
												data:{}
											});
										}
										
									}
									
								});
							
							}else{
								
								if(data.draw){
									singleState(updated, data.dom, data.state, data.draw);
								}else{
									sb.notify({
										type:'app-redraw',
										data:{}
									});
								}
								
							}
							
						}else{

							if(data.state.hasOwnProperty('onSave')){

								data.state.object = updated;
								
								data.state.onSave.call(null, updated, data.dom, data.state, data.draw);
								
							} else {
														
								sb.notify({
									type:'app-redraw',
									data:{}
								});
							}
									
						}
						
/*
console.log('mailchimp settings', data.state.appSettings.apps.integrations.mailchimp);							
console.log('state', data.state.appSettings.apps.integrations.mailchimp.pushToMaps[updated.type.id]);							
*/
						
						
						
					}
											
				}, 2);
			
			}, 2);
						
		},
		
		// update single contact note feed
		updateNotes: function(data){
			
			sb.notify({
				type:'update-note-list',
				data:data
			});
			
		},
		
		// update single contact task feed
		updateTasks: function(data){
			
			sb.notify({
				type:'update-task-list',
				data:{}
			});
			
		},
		
		updateRequests: function(data){
			
			components.requests.notify({
				type: 'update-box-view',
				data: {}
			});
			
		},
		
		// view the contacts table
		viewTable: function(data){
			
			if(data.companyId){
				companyId = data.companyId;
			}
			
			if(data.hasOwnProperty('tableTitle')){
				setupData.tableTitle = data.tableTitle;
			}
			
			if(data.hasOwnProperty('homeView')){
				setupData.homeView = data.homeView;
			}
			
			if(data.hasOwnProperty('compact')){
				compact = true;
			}
			
			if(data.hasOwnProperty('navigation')){
				setupData.navigation = data.navigation;
			}
			
			createUI(data.domObj);
			
			tableUI.state();
			tableUI.state.show();

		},
		
		// view a single contact
		viewSingle: function(data){
			
/*
			sb.data.db.obj.getById('contacts', data.objectId, function(obj){
				
				singleState(obj, data.domObj, {}, function(done){
				
					done.dom.patch();
					done.after(done.dom);
					
				});
				
			}, 3);
*/
			
		}
									
	}
	
});