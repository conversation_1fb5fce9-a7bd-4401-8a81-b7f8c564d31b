Factory.register('csv-uploader', function(sb){
	
	// ui pieces
	var	ui 				= {},
		header_ui 		= {},
		files_ui		= {},
		translator_ui	= {},
		modal_ui		= {},
		
		// components
		comps 			= {},
		
		// view state
		viewState		= {
			view: 'list'
		},
		
		// csv file data
		csvData			= [],
		
		// translation
		uploadObj = {},
		translation		= {
			objectTypes: [],
			refObjs: [],
			snippets: {}
		},
		
		// object blueprints
		blueprints		= {},
		bp = {},
		
		// new objects
		objects = {},
		
		batchSize = 100;
	
	// utility funcs
	function csvToObjs(csv){
		
		var ret 	= [],
			props 	= csv[0];
			
		for(i in props){
			
			// if there is another property of the same name, make this one unique
			if(_.indexOf(props, props[i]) < i){
				props[i] = props[i] +'-'+ i;
			}
			
		}
			
		for(i in csv){
			if(i > 0){
				
				ret.push(_.object(props, csv[i]));
				
			}
		}

		return ret;
		
	}
	
	// object funcs
	function createObjs(data, metaData, view, callback, i){
		
		if(i === undefined){
			i = 0;
		}
		
		uploadObj.rows_transcribed = i;
		
		var batchSize = 250;
		var batch = data.slice(i, i + batchSize);
		
		// testing--db.obj.create call goes here
		var blueprint = _.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0];
		
		if(_.isEmpty(batch)){
			
			sb.data.db.obj.update('csv_upload', {id: uploadObj.id, translation: translation, rows: csvData.length, rows_transcribed: data.length}, function(response){
			
				uploadObj.rows_transcribed = data.length;
				view.update(data.length, data.length);
				callback(true);
				header_ui.view(viewState, uploadObj);
				
			});
			
		}else{
			
			sb.data.db.obj.create(blueprint.blueprint_name, batch, function(response){
				
				view.update(i, data.length);
				createObjs(data, metaData, view, callback, i + batchSize);
			
			});
			
		}
		
	}
	
	function updateObjs(data, metaData, view, dbObjs, referenceObjects, callback, i){
		
		if(i === undefined){
			i = 0;
		}
		
		uploadObj.rows_transcribed_and_related = i;
		
		var batchSize = 250;
		var batch = data.slice(i, i + batchSize);
		
		var blueprint = _.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0];
		
		// use ids from db data
		_.each(batch, function(obj){
			obj.id = _.where(dbObjs, {data_source_id: obj.data_source_id})[0].id;
			obj.object_bp_type = blueprint.blueprint_name;
		});
		
		if(_.isEmpty(batch)){
			
			sb.data.db.obj.update('csv_upload', {id: uploadObj.id, translation: translation, rows: csvData.length, rows_transcribed: uploadObj.rows_transcribed, rows_transcribed_and_related: data.length}, function(response){
			
				uploadObj.rows_transcribed_and_related = data.length;
				view.update(data.length, data.length);
				callback(true);
				header_ui.view(viewState, uploadObj);
				
			});
						
		}else{
			
			sb.data.db.obj.update(blueprint.blueprint_name, batch, function(response){
			// temporary works without batch updates
// 			sb.data.db.obj.update(blueprint.blueprint_name, batch[0], function(response){
				
				view.update(i, data.length);
				updateObjs(data, metaData, view, dbObjs, referenceObjects, callback, i + batchSize);
					
			});
			
		}
		
	}
	
	function updateObjs2(csvData, blueprint, translation, objectType, source, view, next, i){

		function getReferenceObjs(sourceId, calls, next, i, db){
			
			if(db === undefined){
				db = {};
				i = 0;
			}
			
			if(calls[i].skip){
				
				if(calls.length > i + 1){
						
						getReferenceObjs(sourceId, calls, next, i + 1, db);
						
					}else{
						
						next(db);
						
					}
				
			}else if(calls[i].ref_on_other === 'yes'){
				
				sb.data.db.obj.getWhere(calls[i].objectType, {data_source: sourceId, [calls[i].csvField]: {type: 'or', values: calls[i].values}}, function(data){
					
					db[calls[i].objectType] = data;
					if(calls.length > i + 1){
						
						getReferenceObjs(sourceId, calls, next, i + 1, db);
						
					}else{
						
						next(db);
						
					}
					
				});
				
			}else{
				
				sb.data.db.obj.getWhere(calls[i].objectType, {data_source: sourceId, data_source_id: {type: 'or', values: calls[i].values}}, function(data){
					
					db[calls[i].objectType] = data;
					if(calls.length > i + 1){
						
						getReferenceObjs(sourceId, calls, next, i + 1, db);
						
					}else{
						
						next(db);
						
					}
					
				});
				
			}
			
		}
		
// 		var batchSize = 100;
		uploadObj.rows_transcribed_and_related = i;

// 		var batchSize = 2;
		if(i === undefined){
			i = 0;
		}
		
		// get objects to update
		sb.data.db.obj.getWhere(objectType, {
			
			// just the data from this data source
			data_source: source,
			
			// sort by date_created, offset should be batchsize, and page should be i
			paged: {
				pageLength: batchSize,
				sortCol: 'date_created',
				sortDir: 'asc',
				page: i
			},
			
			// get just the data i need
			getJust: ['id', 'data_source_id']
			
		}, function(data){
			
			var batch = data.data;

			// get ids to call objs for their new reference ids
			var dbCallData = [];
			_.each(translation.snippets, function(snippet, propName){
				
				if(snippet.use_refs === 'yes'){
					
					var callData = {
						objectType: _.where(blueprints, {id: parseInt(snippet.object_type)})[0].blueprint_name,
						fieldType: blueprint.blueprint[propName].type,
						values: [],
						property: propName,
						csvField: snippet.field_to_use,
						ref_on_other: snippet.ref_on_other,
						ref_on_self: snippet.on_my_obj_field,
						switch_on_obj_type: snippet.switch_on_obj_type
					};
					
					if(snippet.switch_on_obj_type === 'yes'){
						callData.skip = true;
					}
					
					_.each(batch, function(obj){

						if(snippet.ref_on_other === 'yes'){
							
							callData.values.push(obj[callData.ref_on_self]);
							
						}else{
							
							if(snippet.switch_on_obj_type === 'yes'){
								
								var csvRow = _.where(csvData, {id: obj.data_source_id.toString()})[0];
								if(csvRow !== undefined){
									
									var value = '',
										data = csvRow;
										
									eval(snippet.code);
									
									callData.values.push({
										object_type: value,
										object_id: parseInt(csvRow[snippet.field_to_use]),
										csv_id: csvRow.id
									});
								}
								
							}else{
								
								var csvRow = _.where(csvData, {id: obj.data_source_id.toString()})[0];
								if(csvRow !== undefined){
									callData.values.push(parseInt(csvRow[snippet.field_to_use]));
								}

							}
														
						}
						
					});
					
					callData.values = _.uniq(callData.values);
					dbCallData.push(callData);
					
				}
				
			});
			
			_.each(dbCallData, function(callData, i){
				
				if(callData.switch_on_obj_type === 'yes'){
					
					var newCallData = {},
						newCallDataCsvIds = {};
					
					_.each(callData.values, function(val, j){

						if(newCallData.hasOwnProperty(val.object_type)){
							newCallData[val.object_type].push(val.object_id);
							newCallDataCsvIds[val.object_type].push(val.csv_id);
						}else{
							newCallData[val.object_type] = [val.object_id];
							newCallDataCsvIds[val.object_type] = [val.csv_id];
						}
						
					});
					
					_.each(newCallData, function(newCallIds, objectType){
						
						dbCallData.push({
							objectType: 		objectType,
							fieldType: 			callData.fieldType,
							values: 			_.uniq(newCallIds),
							property: 			callData.property,
							csvField: 			callData.csvField,
							ref_on_other: 		callData.ref_on_other,
							ref_on_self: 		callData.ref_on_self,
							switch_on_obj_type:	callData.switch_on_obj_type,
							csv_ids:			_.uniq(newCallDataCsvIds[objectType])
						});
						
					});
					
				}
				
			});
			
			// get reference objects
			getReferenceObjs(source, dbCallData, function(db){
				
				// create relationships
				_.each(dbCallData, function(call){
					
					if(!call.hasOwnProperty('skip')){
						
						_.each(batch, function(obj){

							if(call.fieldType === 'objectId' || call.fieldType === 'int'){
								
								if(call.ref_on_other === 'yes'){
									
									var data_to_use = _.where(db[call.objectType], {[call.csvField]: obj[call.ref_on_self]});
									if(!_.isEmpty(data_to_use) && data_to_use[0].hasOwnProperty('id')){
										obj[call.property] = data_to_use[0].id;
									}
									
								}else{
									
									var csvRow = _.where(csvData, {id: obj.data_source_id.toString()})[0];
									if(csvRow !== undefined){
										
										// if csv_fields array exists and this csvRow item is not on it
										if(call.hasOwnProperty('csv_ids') && !_.contains(call.csv_ids, csvRow.id)){
											// don't do anything to this batch item
										}else{
											
											var data_to_use = _.where(db[call.objectType], {data_source_id: parseInt(csvRow[call.csvField])});
											if(!_.isEmpty(data_to_use) && data_to_use[0].hasOwnProperty('id')){
												obj[call.property] = data_to_use[0].id;
											}
											
										}
										
									}
									
								}
															
							}else if(call.fieldType === 'objectIds'){
								
								if(call.ref_on_other === 'yes'){
									
									var data_to_use = _.where(db[call.objectType], {[call.csvField]: obj[call.ref_on_self]});
									if(!_.isEmpty(data_to_use) && data_to_use[0].hasOwnProperty('id')){
										obj[call.property] = _.pluck(data_to_use, 'id');
									}
									
								}else{
									
									// if csv_fields array exists and this csvRow item is not on it
									if(call.hasOwnProperty('csv_ids') && !_.contains(call.csv_ids, csvRow.id)){
										// don't do anything to this batch item
										return false;
									}else{
										
										var csvRow = _.where(csvData, {id: obj.data_source_id.toString()})[0];
										obj[call.property] = _.pluck(_.where(db[call.objectType], {data_source_id: parseInt(csvRow[call.csvField])}), 'id');
										
									}
										
								}
															
							}
							
						});
						
					}
					
				});
				
// 				console.log(batch);
// 				return true;
				
// 				console.log(batch);
// 				return true;
				// 229735 (darth), 229732 luke, 229733 leia, 229734 han 234294 (han's email obj id)
					
				// update data, and move to next step
				if(_.isEmpty(batch)){

					sb.data.db.obj.update('csv_upload', {id: uploadObj.id, translation: translation, rows: csvData.length, rows_transcribed: uploadObj.rows_transcribed, rows_transcribed_and_related: uploadObj.rows_transcribed_and_related}, function(response){
					
// 						uploadObj.rows_transcribed_and_related = data.length;
						view.update(data.length, data.length);
						next(true);
						header_ui.view(viewState, uploadObj);
						
					});
								
				}else{
					
					sb.data.db.obj.update(objectType, batch, function(response){
						
						view.update(i, data.length);
						updateObjs2(csvData, blueprint, translation, objectType, source, view, next, i + batchSize);
							
					});
					
				}
				
			});
							
		});
		
	}
	
	function rowsToObjs(csvData, translation, source, db){
		
		var objects = {};
		
		_.each(translation.objectTypes, function(type){
			
			objects[_.where(blueprints, {id: parseInt(type.id)})[0].blueprint_name] = [];
			
		});
		
		// for each item in csv
		_.each(csvData, function(datum, i){
			
			// for each object type in csv
			_.each(objects, function(objList, objectType){
				
				// new object to be created from csv row
				var newObj = {
					data_source_id: parseInt(datum.id),
					data_source: source
				};
				
				// for each snippet in translation
				_.each(translation.snippets, function(snippet, propName){

					if(db === undefined){
						
						if(!snippet.use_refs || snippet.use_refs === 'no'){
						
							var	data = datum,
								value;
	
							eval(snippet.code);
							
							newObj[propName] = value;
							
						}
						
					}else{
						
						var data = datum,
							value;

						eval(snippet.code);
						
						newObj[propName] = value;
						
					}
					
				});
				
				objList.push(newObj);
				
			});
			
		});
		
		return objects;
		
	}
	
	function getReferenceData(objTypes, blueprints, sourceId, callback, i, db){
		
		if(db === undefined){
			db = {};
			i = 0;
		}
		
		var objectType = _.where(blueprints, {id: parseInt(objTypes[i].id)})[0].blueprint_name;

		sb.data.db.obj.getWhere(objectType, {data_source: sourceId}, function(data){
			
			db[objectType] = data;
			if(objTypes.length > i + 1){
				
				getReferenceData(objTypes, blueprints, sourceId, callback, i + 1, db);
				
			}else{
				
				callback(db);
				
			}
			
		});
		
	}
	
	// views
	function show_header_ui(state, uploadObj){

		// empty
		this.empty();
		
		// left section
		this.makeNode('l', 'column', {width: 4})
			.makeNode('btns', 'buttonGroup', {css: 'pull-left'})
				.makeNode('files-toggle', 'button', {text: '<i class="fa fa-files-o" aria-hidden="true"></i> All CSVs'})
				.notify('click', {
					type: 'show-uploads-list-btn-clicked',
					data: {}
				}, sb.moduleId);
				
		// right section
		this.makeNode('r', 'column', {width: 8})
			.makeNode('btns', 'buttonGroup', {css: 'pull-right'});
			
		if(state.view === 'single'){
			
			// create objects button
			var css = '',
				btnText = '<i class="fa fa-exchange" aria-hidden="true"></i> Convert data ('+ uploadObj.rows_transcribed +'/'+ csvData.length+')';
			if(csvData.length === uploadObj.rows_transcribed){
				css = 'pda-btn-disabled';
				btnText = '<i class="fa fa-check" aria-hidden="true"></i> ('+ uploadObj.rows_transcribed +' objects created)';
			}
			this.r.btns.makeNode('upload-btn', 'button', {text: btnText, css: 'pda-btnOutline-blue '+ css})
				.notify('click', {
					type: 'upload-data-btn-clicked',
					data: {}
				}, sb.moduleId);
			
			css = '';
			btnText = '<i class="fa fa-link" aria-hidden="true"></i> Set data links ('+ uploadObj.rows_transcribed_and_related +'/'+ csvData.length +')';
			if(uploadObj.rows_transcribed_and_related === uploadObj.rows){
				css = 'pda-btn-disabled';
				btnText = '<i class="fa fa-check" aria-hidden="true"></i> ('+ uploadObj.rows_transcribed_and_related +' objects up to date)';
			}
				
			// update references button
			this.r.btns.makeNode('update-links-btn', 'button', {text: btnText, css: 'pda-btnOutline-blue'})
				.notify('click', {
						type: 'update-data-refs-btn-clicked',
						data: {}
					}, sb.moduleId);
				
			this.r.btns.makeNode('save-btn', 'button', {text: '<i class="fa fa-floppy-o" aria-hidden="true"></i> Save Changes', css: 'pda-btnOutline-green'})
				.notify('click', {
					type: 'save-csv-translation-btn-clicked',
					data: {}
				}, sb.moduleId);
			
		}else if(state.view === 'list'){
			
			this.r.btns.makeNode('show-settings-btn', 'button', {text: '<i class="fa fa-cogs fa-2x" aria-hidden="true"></i>', css: 'pda-transparent'})
				.notify('click', {
					type: 'show-settings-btn-clicked',
					data: {}
				}, sb.moduleId);
			
		}
			
		// draw to dom
		this.patch();
		
	}
	
	function show_files_ui(comps){
		
		this.empty();
		
		if(comps.hasOwnProperty('filesTable')){
			comps.filesTable.destroy();
		}
		
		// update dom
		this.makeNode('filesTable', 'container', {});
		this.patch();
		
		// start crud comp
		comps.filesTable = sb.createComponent('crudPaged');
		comps.filesTable.notify({
			type: 'display-crud-table-paged',
			data: {
				buttons: {
					create: true,
					view: true,
					edit: false,
					erase: true
				},
				cells: {
					rows: function(obj){ return obj.rows_transcribed +' / '+ obj.rows; }
				},
				data: function(callback, page){
					sb.data.db.obj.getAll('csv_upload', function(ret){
													
						callback(ret);
						
					}, 1, page);						
										
				},
				domObj: this.filesTable,
				headerButtons:[],
				objectType:'csv_upload',
				visibleCols: ['name', 'source', 'rows', 'date_created'],
				formFields: {
					name: {},
					file: {},
					source: {},
					rows: {
						type: 'hidden',
						value: 0
					},
					rows_transcribed: {
						type: 'hidden',
						value: 0
					},
					rows_transcribed_and_related: {
						type: 'hidden',
						value: 0
					}
				}
			}
		});
		
	}
	
	function show_settings_ui(comps){
		
		this.show();
		
		if(!comps.hasOwnProperty('settings')){
			
			this.empty();
			this.makeNode('t', 'headerText', {text: 'Data Sources', css: 'text-center', size: 'small'});
			this.makeNode('table-container', 'column', {width: 12});
			this.patch();
			
			comps.settings = sb.createComponent('crudPaged');
			comps.settings.notify({
				type: 'display-crud-table-paged',
				data: {
					buttons: {
						create: true,
						view: false,
						edit: false,
						erase: false
					},
					cells: {},
					data: function(callback, page){
						
						sb.data.db.obj.getAll('csv_source', function(ret){
														
							callback(ret);
							
						}, 1, page);						
											
					},
					domObj: this['table-container'],
					headerButtons:[],
					objectType:'csv_source',
					visibleCols: ['name', 'date_created'],
					formFields: {
						name: {}
					}
				}
			});
			
		}
		
	}
	
	function show_translator_ui(csv, selectedProperty, translation, blueprints){
		
		function show_object_properties(blueprint, translation){
			
			this.translation.empty();

			this.translation.makeNode('title', 'headerText', {text: blueprint.blueprint_name, size: 'x-small', css: 'text-center'});

			// show properties
			_.each(blueprint.blueprint, function(prop, key){
				
				// highlight if there is an applicable translation
				var containerClass = '',
					style = 'border:1px solid white;';
				if(translation.snippets.hasOwnProperty(key)){
					containerClass = 'pda-panel-primary';
					style = '';
				}
				
				// don't show generated system properties
				if(
					
					key !== 'id' && 
// 					key !== 'date_created' && 
					key !== 'created_by' &&
					key !== 'last_updated' && 
					key !== 'last_updated_by' &&
					key !== 'data_source' && 
					key !== 'data_source_id'
					 
				){
					
					this.translation.makeNode('col-'+ key, 'column', {width: 3})
						.makeNode('prop-'+ key, 'container', {css: 'pda-Panel pda-container '+ containerClass, style: 'padding:5px;'+ style})
						.notify('click', {
							type: 'new-prop-translation-btn-clicked',
							data: {
								prop: key
							}
						});
				
					this.translation['col-'+ key]['prop-'+ key].makeNode('text', 'text', {text: key, css: 'text-center'});
					
				}
				
			}, this);
			
			this.translation.patch();
			
		}
		
		this.empty();
		
		this.makeNode('objs', 'column', {width: 6})
			.makeNode('title', 'headerText', {text: 'Object Type', size: 'x-small', css: 'text-center'});
		
		this.objs.makeNode('items', 'buttonGroup', {});
		_.each(translation.objectTypes, function(objType){
			
			this.objs.items.makeNode('obj-'+ objType.id, 'button', {text: _.where(blueprints, {id: parseInt(objType.id)})[0].blueprint_name});
			
		}, this);
		
		this.makeNode('ref-objs', 'column', {width: 6})
			.makeNode('title', 'headerText', {text: 'Reference Objects', size: 'x-small', css: 'text-center'});
		
		_.each(translation.refObjs, function(objType){
			this['ref-objs'].makeNode('obj-'+ objType.id, 'column', {width: 3})
				.makeNode('text', 'text', {text: _.where(blueprints, {id: parseInt(objType.id)})[0].blueprint_name});
		}, this);
		
		this.makeNode('br', 'lineBreak', {spaces: 1});
		
		this['ref-objs'].makeNode('new-btn', 'button', {text: '<i class="fa fa-plus" aria-hidden="true"></i>', css: 'pda-btnOutline-green'})
			.notify('click', {
				type: 'get-add-obj-type-form-btn-clicked',
				data: {
					add_to: 'refObjs'
				}
			}, sb.moduleId);
		
		if(_.isEmpty(translation.objectTypes)){
			
			this.objs.items.makeNode('add-obj-type-btn', 'button', {text: '<i class="fa fa-plus" aria-hidden="true"></i> object type', css: 'pda-btnOutline-green'})
				.notify('click', {
					type: 'get-add-obj-type-form-btn-clicked',
					data: {
						add_to: 'objectTypes'
					}
				}, sb.moduleId);
			
		}
		
		this.makeNode('csv', 'column', {width: 6})
			.makeNode('title', 'headerText', {text: 'Csv Columns', size: 'x-small', css: 'text-center'});
		
		this.makeNode('translation', 'column', {width: 6});
				
		if(!_.isEmpty(csv)){
			
			_.each(_.keys(csv[0]), function(key, i){
				
				var containerClass = '',
					style = 'border:1px solid white;';
				
				this.csv.makeNode('col-'+ key, 'column', {width: 3})
					.makeNode('prop-'+ key, 'container', {css: 'pda-Panel pda-container '+ containerClass, style: 'padding:5px;'+ style});
				
				this.csv['col-'+ key]['prop-'+ key].makeNode('text', 'text', {text: key, css: 'text-center'});
				
			}, this);
			
		}
		
		this.patch();
		
		this.object_view = show_object_properties;
		
		if(!_.isEmpty(translation.objectTypes)){

			this.object_view(_.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0], translation);
			
		}
		
	}
	
	function view_modal_ui(type, blueprints, settings){
		
		this.body.empty();
		this.footer.empty();
		
		switch(type){
			
			case 'add-prop-translation':
				
				this.body.makeNode('header', 'headerText', {text: settings.prop, css: 'text-center'});
				
				// get data to pre-fill form
				var translation_code = '',
					use_refs = '',
					ref_obj_type = 0,
					ref_obj_from_field = '',
					ref_on_other = '',
					on_my_obj_field = '',
					switch_on_obj_type = '';
					
				if(settings.translation.hasOwnProperty('snippets') && settings.translation.snippets.hasOwnProperty(settings.prop)){
					translation_code = settings.translation.snippets[settings.prop].code || '';
					use_refs = settings.translation.snippets[settings.prop].use_refs || '';
					ref_obj_type = settings.translation.snippets[settings.prop].object_type || 0;
					ref_obj_from_field = settings.translation.snippets[settings.prop].field_to_use || '';
					ref_on_other = settings.translation.snippets[settings.prop].ref_on_other || '';
					on_my_obj_field = settings.translation.snippets[settings.prop].on_my_obj_field || '';
					switch_on_obj_type = settings.translation.snippets[settings.prop].switch_on_obj_type || '';
				}

				var objTypeOptions = [];
				_.each(translation.refObjs, function(objType){
					objTypeOptions.push({
						value: objType.id,
						name: _.where(blueprints, {id: parseInt(objType.id)})[0].blueprint_name
					});
				});
				
				var csvPropOptions = [],
					on_my_obj_field_options = [];
					
				_.each(bp, function(field, key){
					on_my_obj_field_options.push({
						name: key,
						value: key
					});
				});
					
				if(ref_on_other === 'yes'){
					
					var this_bp = _.where(blueprints, {id: parseInt(ref_obj_type)})[0];
					
					_.each(this_bp.blueprint, function(field, key){
						
						csvPropOptions.push({
							name: key,
							value: key
						});
						
					});
					
				}else{
					
					_.each(settings.csv[0], function(v, key){
						
						csvPropOptions.push({
							name: key,
							value: key
						});
						
					});
					
				}
				
				// hide fields that aren't needed
				var translationFieldType = 'textbox',
					refObjFieldType = 'select',
					switch_on_obj_type_field_type = 'checkbox',
					code_snippet_label = 'Translation Code (set <i>value</i> to new object property value)';
					
				if(use_refs){
					translationFieldType = 'hidden';
				}else{
					refObjFieldType = 'hidden';
					switch_on_obj_type_field_type = 'hidden';
				}
				
				if(switch_on_obj_type === 'yes'){
					translationFieldType = 'textbox';
					code_snippet_label = 'Translation Code (set <i>value</i> to reference object type)';
				}

				var formArgs = {
					prop_name: {
						name: 'prop_name',
						type: 'hidden',
						label: 'Object Property Name',
						options: [],
						value: settings.prop
					},
					translation: {
						name: 'translation',
						type: translationFieldType,
						label: code_snippet_label,
						style: 'background-color: black;color:white;box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);border:2px solid #eb840f;',
						css: 'pda-Panel pda-container pda-panel-orange',
						value: translation_code
					},
					use_refs: {
						name: 'use_refs',
						type: 'checkbox',
						options: [{
							name: 'use_refs',
							label: 'Reference to other object data?',
							value: 'yes'
						}],
						value: [use_refs]
					},
					ref_on_other: {
						name: 'ref_on_other',
						type: 'checkbox',
						options: [{
							name: 'ref_on_other',
							label: 'Reference already on the other obj?',
							value: 'yes'
						}],
						value: [ref_on_other]
					},
					ref_obj_type: {
						label: 'Reference to what object type?',
						name: 'ref_obj_type',
						type: refObjFieldType,
						options: objTypeOptions,
						value: ref_obj_type
					},
					ref_obj_from_field: {
						label: 'Use what field?',
						name: 'ref_obj_from_field',
						type: refObjFieldType,
						options: csvPropOptions,
						value: ref_obj_from_field
					},
					on_my_obj_field: {
						label: 'Field on my object?',
						name: 'on_my_obj_field',
						type: refObjFieldType,
						options: on_my_obj_field_options
					},
					switch_on_obj_type: {
						label: 'Varying object type?',
						name: 'switch_on_obj_type',
						type: switch_on_obj_type_field_type,
						options: [{
							name: 'switch_on_obj_type',
							label: 'Yes',
							value: 'yes'
						}],
						value: [switch_on_obj_type]
					}
				};

				var blueprint = _.where(blueprints, {id: parseInt(settings.translation.objectTypes[0].id)})[0],
					propOptions = [];
				
				_.each(blueprint.blueprint, function(prop, propKey){
					
					// ignore system generated properties
					if(
					
						propKey !== 'id' && 
// 						propKey !== 'date_created' && 
						propKey !== 'created_by' &&
						propKey !== 'last_updated' && 
						propKey !== 'last_updated_by' &&
						propKey !== 'data_source' && 
						propKey !== 'data_source_id'
						 
					){
						propOptions.push({
							name: prop.name,
							value: propKey
						});
					}
					
				});
				
				// show property info
				this.body.makeNode('propInfo', 'column', {width: 12})
					.makeNode('l', 'column', {width: 6});
				this.body.propInfo.makeNode('r', 'column', {width: 6});
				
				// property type
				this.body.propInfo.l.makeNode('type', 'text', {text: '<strong>Data Type:</strong>', css: 'pull-right'});
				this.body.propInfo.r.makeNode('type', 'text', {text: blueprint.blueprint[settings.prop].type});
				this.body.propInfo.l.makeNode('br', 'lineBreak', {});
				this.body.propInfo.l.makeNode('br', 'lineBreak', {});
				
				formArgs.prop_name.options = propOptions;
				
				this.body.makeNode('pre-form-br', 'lineBreak', {});
				this.body.makeNode('form', 'form', formArgs);
				this.body.form.use_refs.notify('change', {
					type: 'toggle-use-refs-in-edit-modal',
					data: {
						form: this.body.form
					}
				}, sb.moduleId);
				
				this.body.form.ref_on_other.notify('change', {
					type: 'toggle-ref-on-other',
					data: {
						form: this.body.form
					}
				}, sb.moduleId);
				
				this.body.form.ref_obj_type.notify('change', {
					type: 'toggle-ref-on-other',
					data: {
						form: this.body.form
					}
				}, sb.moduleId);
				
				this.body.form.switch_on_obj_type.notify('change', {
					type: 'toggle-use-refs-in-edit-modal',
					data: {
						form: this.body.form
					}
				}, sb.moduleId);
				
				// property options
				if(bp[settings.prop].type === 'select' || bp[settings.prop].type === 'multi-select' || bp[settings.prop].type === 'objectId' || bp[settings.prop].type === 'objectIds'){

					// object type if value is an id reference
					if(blueprint.blueprint[settings.prop].type === 'objectId' || blueprint.blueprint[settings.prop].type === 'objectIds'){
						
						this.body.propInfo.l.makeNode('objType', 'text', {text: '<strong>Object Type:</strong>', css: 'pull-right'});
						this.body.propInfo.r.makeNode('objType', 'text', {text: blueprint.blueprint[settings.prop].objectType});
						this.body.propInfo.l.makeNode('br2', 'lineBreak', {});
						this.body.propInfo.l.makeNode('br2', 'lineBreak', {});
						
					}
					
					this.body.propInfo.l.makeNode('options', 'text', {text: '<strong>Options:</strong>', css: 'pull-right'});
					this.body.propInfo.r.makeNode('options', 'buttonGroup', {});
					
					if(_.size(bp[settings.prop].options) < 20){
						
						_.each(bp[settings.prop].options, function(name, val){
						
							this.body.propInfo.r.options.makeNode('btn-'+ val, 'button', {text: name})
								.notify('click', {
									type: 'use-ref-obj-prop-option-btn-clicked',
									data: {
										insertString: val,
										form: this.body.form
									}
								}, sb.moduleId);
							
						}, this);
						
					}
					
				}
						
				// show usable csv columns
				this.body.makeNode('csv-btns', 'buttonGroup', {width: 12});
				_.each(settings.csv[0], function(v, key){
										
					this.body['csv-btns'].makeNode('btn-'+ key, 'button', {text: key})
						.notify('click', {
							type: 'use-csv-data-col-val-btn-clicked',
							data: {
								form: this.body.form,
								insertString: 'data.'+ key
							}
						}, sb.moduleId);
					
				}, this);
				
				this.footer.makeNode('testBtn', 'button', {text: '<i class="fa fa-exchange" aria-hidden="true"></i> test in console', css: 'pda-btnOutline-purple'})
					.notify('click', {
						type: 'test-property-translation-btn-clicked',
						data: {
							form: this.body.form
						}
					}, sb.moduleId);
					
				this.footer.makeNode('addTransBtn', 'button', {text: '<i class="fa fa-exchange" aria-hidden"true"></i> add', css: 'pda-btnOutline-green'})
					.notify('click', {
						type: 'add-translation-snippet-btn-clicked',
						data: {
							form: this.body.form
						}
					}, sb.moduleId);
					
				this.body.patch();
				this.footer.patch();
			
			break;
			
			case 'add-object-type':
				
				this.body.makeNode('header', 'headerText', {text: 'Add Object Type', css: 'text-center'});
				
				var formArgs = {
					obj_type: {
						name: 'obj_type',
						type: 'select',
						label: 'Object Type',
						options: []
					}
				};
				
				_.each(blueprints, function(blueprint, i){
					
					if(_.isEmpty(_.where(translation.objectTypes, {id: blueprint.id}))){
						
						formArgs.obj_type.options.push({
							name: blueprint.blueprint_name,
							value: blueprint.id
						});

					}
										
				});
				
				this.body.makeNode('form', 'form', formArgs);
				
				this.footer.makeNode('addBtn', 'button', {text: '<i class="fa fa-plus" aria-hidden="true"></i> obj type', css: 'pda-btnOutline-green pull-right'})
					.notify('click', {
						type: 'add-object-type-btn-clicked',
						data: {
							form: this.body.form,
							modal: this,
							add_to: settings.add_to
						}
					}, sb.moduleId);
					
				this.body.patch();
				this.footer.patch();
				
			break;
			
			case 'create-objs':
				
				this.body.makeNode('title', 'headerText', {text: 'Creating Objects...', css: 'text-center', size: 'small'});
				
			case 'update-objs':
			
				if(!this.body.hasOwnProperty('title')){
					this.body.makeNode('title', 'headerText', {text: 'Updating Objects...', css: 'text-center', size: 'small'});
				}
				
				this.body.makeNode('c', 'column', {width: 12, css: 'text-center'})
					.makeNode('progress', 'headerText', {text: settings.done +' / '+ settings.total});
				
				this.body.patch();
				
				this.show();
				
				// method to update the creation status
				this.body.c.update = function(created, total){
					
					this.makeNode('progress', 'headerText', {text: created +' / '+ total});
					this.patch();
					
				};
				
				return this.body.c;
			
			break;
			
		}
		
	}
	
	// contacts uploader view
	function contacts_uploader(onComplete){
		
		// !TODO: make contacts uploader 4 width
		this.empty();
		
		var dataMap = {},
			modal = this.makeNode('modal', 'modal', {}),
			fieldSelectOptions = [],
			fileData = [],
			fileBlob = {},
			filePost = {};
		var upload = {};
		
		function getContactInfoTypeFormArgs(type){
			
			var formSetup = {
				is_primary:{
					label:'Primary',
					name:'is_primary',
					type:'select',
					options:[{
						name:'Yes',
						value:'yes'
					},{
						name:'No',
						value:'no'
					}]
				}, 
				data_type: {
					name:'data_type',
					type:'hidden',
					value:type.data_type
				}
			};
			
			switch(type.data_type){
				
				case 'address':
				
					formSetup.street = {
							type:'select',
							name:'street',
							label:'Street',
							options:fieldSelectOptions
						};
						
					formSetup.city = {
							type:'select',
							name:'city',
							label:'City',
							options:fieldSelectOptions
						};
						
					formSetup.state = {
							type:'select',
							name:'state',
							label:'State',
							options:fieldSelectOptions
						};	
						
					formSetup.zip = {
							type:'select',
							name:'zip',
							label:'Zip Code',
							options:fieldSelectOptions
						};
						
					formSetup.country = {
							type:'select',
							name:'country',
							label:'Country',
							options:fieldSelectOptions
						};			
				
					break;
				
				default:
									
					formSetup.contact_type = {
							type:'select',
							name:'info',
							label:type.name,
							options:fieldSelectOptions
						};
				
			}
			
			return formSetup;
			
		}
		
		// STEP ONE
		function upload_form(){
			
			function process_step(){
				
				var formData = this.main.form.process().fields;
				filePost = formData.file.value;
				
				var file_data = filePost.fileData;
				fileBlob = file_data;
				
				if(file_data === undefined){
					
					sb.dom.alerts.alert('Please select a file to upload!', '', 'error');
					
					return false;
					
				}else if(file_data.type !== 'text/csv'){
					
					sb.dom.alerts.alert('Not a csv!', '', 'error');
					
					return false;
					
				}else if(formData.type.value === '0'){
					
					sb.dom.alerts.alert('Please select a type for the new contacts.', '', 'error');
					return false;
					
				}
				
				dataMap.type = parseInt(formData.type.value);
				
				this.empty();
				
				this.makeNode('loader', 'loader', {size:'large', css:'text-center'});
				this.patch();
				
				this.view = data_map;
				this.view();
				
			}
			
			this.empty();
			
			// top menu
			this.makeNode(
				'menu'
				, 'div'
				, {
					css: 'ui secondary borderless horizontal fluid menu'
				}
			);
			
			// top menu / next btn
			this.menu.makeNode(
				'next'
				, 'div', {
					tag: 	'button'
					, text:	'Map my data <i class="arrow right icon"></i>'
					, css:	'ui right floated button item'
				}
			).notify('click', {
				type:'csv-uploader-run',
				data:{
					run: function(){

						this.process();
						
					}.bind(this)
				}
			});
			
			this.process = process_step;
			
			// title
			this.makeNode('title', 'div', {
				tag: 	'h1'
				, text:	'Upload your contacts as a csv file'+
						'<div class="sub header">'+
							'Upload your data as a csv file, and select what type of contact you would like to map your data to.'+
						'</div>'
				, css:	'ui header'
			});
			
			
			this.makeNode('break', 'lineBreak', {});
			
			var ui = this;
			
			sb.data.db.obj.getBlueprint('contacts', function(bp){
			
				var formArgs = {
					file: {
						name:'file',
						type:'file-upload'
					},
					type:{
						label:'Type',
						type:'select',
						options:[{
							name:'Select a contact type',
							value:0
						}],
						name:'type',
						value:dataMap.type || 0
					}
				};
				
				_.each(bp.type.options, function(name, val){
					
					formArgs.type.options.push({
						name:	name,
						value:	parseInt(val)
					});
					
				});
				
				// form
				ui.makeNode('main', 'column', {width:5});
				ui.main.makeNode('form', 'form', formArgs);
				ui.patch();
				
			}, true);
			
		}
		
		// STEP TWO
		function data_map(){
			
			function process_step(){
				
				// LEFT
				var formData = this.main.left.form.process().fields;
				
				// check if form is incomplete
				if(formData.f_name.value === '0'){
					
					sb.dom.alerts.alert('Please select a value set for the contacts\' first name.', '', 'error');
					return false;
					
				}else if(formData.l_name.value === '0'){
					
					sb.dom.alerts.alert('Please select a value set for the contacts\' last name.', '', 'error');
					return false;
					
				}
				
				// use form data
				dataMap.fname = formData.f_name.value;
				dataMap.lname = formData.l_name.value;
				dataMap.company = formData.company.value;
				dataMap.manager = 0;
				dataMap.companyCategory = parseInt(formData.company_category.value);
				
				// place loader spinner
				this.empty();
				this.makeNode('loader', 'loader', {size:'large', css:'text-center'});
				this.patch();
				
				// next step
// 				this.view = contact_info;
// 				this.view();
				
				// RIGHT
				this.view = preview_contacts;
				this.view();
				
			}
			
			function go_back(){
					
				var ui = this;
				sb.dom.alerts.ask(
				
					{
						title:'Are you sure?',
						text:'You will lose any progress you have made mapping over the csv file currently loaded.',
						closeOnConfirm:true
					}, function(resp){
						
						if(resp){
							
							fileData = [];
							fieldSelectOptions = [];
							dataMap = {};
							ui.view = upload_form;
							ui.view();
							
						}
						
					}
					
				);
				
			}
			
			// LEFT
			function initial_map(){
				
				function process_step(){
					
					var formData = this.form.process().fields;
					
					// check if form is incomplete
					//!TODO: turn these checks back on
					if(formData.f_name.value === '0'){
						
						sb.dom.alerts.alert('Please select a value set for the contacts\' first name.', '', 'error');
						return false;
						
					}else if(formData.l_name.value === '0'){
						
						sb.dom.alerts.alert('Please select a value set for the contacts\' last name.', '', 'error');
						return false;
						
					}
					
					// use form data
					dataMap.fname = formData.f_name.value;
					dataMap.lname = formData.l_name.value;
					dataMap.company = formData.company.value;
					dataMap.manager = 0;
					dataMap.companyCategory = parseInt(formData.company_category.value);
					
					// place loader spinner
					this.empty();
					this.makeNode('loader', 'loader', {size:'large', css:'text-center'});
					this.patch();
					
					// next step
					this.view = contact_info;
					this.view();
					
				}
				
				function go_back(){
					
					var ui = this;
					sb.dom.alerts.ask(
					
						{
							title:'Are you sure?',
							text:'You will lose any progress you have made mapping over the csv file currently loaded.',
							closeOnConfirm:true
						}, function(resp){
							
							if(resp){
								
								fileData = [];
								fieldSelectOptions = [];
								dataMap = {};
								console.log(this);
								ui.view = upload_form;
								ui.view();
								
							}
							
						}
						
					);
					
				}
				
				var ui = this;
					
				sb.data.files.read(fileBlob, function(parsed_file){
					
					fileData = parsed_file;
					
					sb.data.db.obj.getAll('company_categories', function(company_categories){
						
						ui.empty();
						ui.previousStep = go_back;
						
						// back button
						/*
ui.makeNode('back', 'button', {css:'pull-left pda-btn-primary', text:'<i class="fa fa-arrow-left" aria-hidden="true"></i> Back'})
							.notify('click', {
								type:'csv-uploader-run',
								data:{
									
									run: function(){
										
										ui.previousStep();
										
									}
									
								}
							}, sb.moduleId);
						
						// next button
						ui.makeNode('next', 'button', {css:'pull-right pda-btn-primary', text:'Next <i class="fa fa-arrow-right" aria-hidden="true"></i>'});
						
						ui.process = process_step;
						
						ui.next.notify('click', {
							type:'csv-uploader-run',
							data:{
								run: function(){
			
									ui.process();
									
								}.bind(this)
							}
						});
						
						// step title
						ui.makeNode('title', 'headerText', {text:'Map your data', css:'text-center'});
						ui.makeNode('description', 'text', {text:'Now let\'s match the columns in your uploaded file to your contact\'s info.', css:'text-muted'});
						
						ui.makeNode('br', 'lineBreak', {});
						
*/
						var selectOptions = [{
							name:'Select a field from your csv file',
							value:0
						}];
						
						_.each(fileData[0], function(val, key){
							
							var temp = {
								name:key.toUpperCase() +' -- ',
								value:key
							};
							
							for(i=0; i<3; i++){
								if(fileData[i] !== undefined){
									temp.name += fileData[i][key];
									if(i < 2){
										temp.name += ', ';
									}
								}
							}
							
							selectOptions.push(temp);
							
						});
						
						fieldSelectOptions = selectOptions;
						var companyCategoryOptions = [{
							name:'Select a company type',
							value:0
						}];
						
						_.each(company_categories, function(cat){
							
							companyCategoryOptions.push({
								value:cat.id,
								name:cat.name
							});
							
						});
	
						var formArgs = {
							f_name:{
								label:'First name',
								type:'select',
								options:selectOptions,
								name:'f_name',
								value:dataMap.fname || ''
							},
							l_name:{
								label:'Last name',
								type:'select',
								options:selectOptions,
								name:'l_name',
								value:dataMap.lname || ''
							},
							company:{
								label:'Company',
								type:'select',
								options:selectOptions,
								name:'company',
								value:dataMap.company || ''
							},
							company_category:{
								name:'company_category',
								label:'Company type',
								type:'select',
								options:companyCategoryOptions,
								value:dataMap.companyCategory || ''
							}
						};
						
						ui.makeNode('form', 'form', formArgs);
						ui.makeNode('div', 'div', {css: 'ui clearing divider'});
						ui.patch();
						
					}, true);
					
				});
				
			}
			
			// RIGHT
			function contact_info(){
				
				var ui = this,
					contactTypeObj = {};
				
				function go_back(){
					
					ui.view = initial_map;
					ui.view();
					
				}
				
				function toNextStep(){
					
					this.view = preview_contacts;
					this.view();
					
				}
				
				function displayContactInfo(contact_type){
					
					ui = this;
					ui.empty();
					
					/*
ui.previousStep = go_back;
					
					ui.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left" aria-hidden="true"></i> Back', css:'pull-left pda-btn-primary'})
						.notify('click', {
							type:'csv-uploader-run',
							data:{
								run: function(){
									
									ui.previousStep();
									
								}
							}
						}, sb.moduleId);
					
					ui.makeNode('next', 'button', {text:'Preview <i class="fa fa-arrow-right" aria-hidden="true"></i>', css:'pull-right pda-btn-primary'})
						.notify('click', {
							type:'csv-uploader-run',
							data:{
								run: function(){
									
									toNextStep.bind(ui)();
									
								}
							}
						}, sb.moduleId);
					
					ui.makeNode('title', 'headerText', {text:'Map contact info', css:'text-center'});
					
					ui.makeNode('br', 'lineBreak', {});
*/

					_.each(contact_type.available_types, function(type){
						
						if(type !== null){
							
							ui.makeNode('type-'+ type.id, 'column', {width:4});
							ui['type-'+ type.id].makeNode('title', 'text', {text:'<strong>'+ type.name +'</strong>'});
							
							var items = _.where(dataMap.available_types, {type:type.id});
							_.each(items, function(item){
								
								ui['type-'+ type.id].makeNode('disp-'+ item.info, 'container', {});
								if(item.is_primary === 'yes'){
									ui['type-'+ type.id]['disp-'+ item.info].makeNode('isPrimary', 'text', {text:'<i class="fa fa-check" aria-hidden="true"></i> PRIMARY', style:'display:inline-block;'});
								}else{
									ui['type-'+ type.id]['disp-'+ item.info].makeNode('isPrimary', 'text', {text:'<i class="fa fa-minus" aria-hidden="true"></i> SUPPLEMENTARY', style:'display:inline-block;'});
								}
								
								switch(item.data_type){
									
									case 'address':
										ui['type-'+ type.id]['disp-'+ item.info].makeNode('street', 'text', {text:_.findWhere(fieldSelectOptions, {value:item.street}).name +' <small>(street)</small>'});
										ui['type-'+ type.id]['disp-'+ item.info].makeNode('city', 'text', {text:_.findWhere(fieldSelectOptions, {value:item.city}).name +' <small>(city)</small>'});
										ui['type-'+ type.id]['disp-'+ item.info].makeNode('state', 'text', {text:_.findWhere(fieldSelectOptions, {value:item.state}).name +' <small>(state)</small>'});
										ui['type-'+ type.id]['disp-'+ item.info].makeNode('zip', 'text', {text:_.findWhere(fieldSelectOptions, {value:item.zip}).name +' <small>(zip)</small>'});
										ui['type-'+ type.id]['disp-'+ item.info].makeNode('country', 'text', {text:_.findWhere(fieldSelectOptions, {value:item.country}).name +' <small>(country)</small>'});
										break;
									
									default:
										ui['type-'+ type.id]['disp-'+ item.info].makeNode('disp-'+ item.info, 'text', {text:_.findWhere(fieldSelectOptions, {value:item.info}).name});
										break;
									
								}
								
							});
							
							ui['type-'+ type.id].makeNode('add', 'button', {text:'<i class="fa fa-plus" aria-hidden="true"></i>', css:'pda-btnOutline-blue pda-btn-x-small'})
								.notify('click', {
									type:'csv-uploader-run',
									data:{
										run:function(){
											
											ui.view = contactInfoTypeForm;
											ui.view(type);
											ui.show();
											
										}.bind(ui, type)
									}
								}, sb.moduleId);
							
						}
						
					});
					
					ui.patch();
					
				}
				
				function processContactInfoTypeForm(){
					
					var formData = this.form.process().fields;
					
					var contactInfoObj = {};
					switch(formData.data_type.value){
						
						case 'address':
							contactInfoObj.street = formData.street.value;
							contactInfoObj.city = formData.city.value;
							contactInfoObj.state = formData.state.value;
							contactInfoObj.zip = formData.zip.value;
							contactInfoObj.country = formData.country.value;
							break;
							
						default:
							contactInfoObj.info = formData.info.value;
							break;
						
					}
					
					contactInfoObj.type = parseInt(formData.contactInfoType.value);
					contactInfoObj.data_type = formData.data_type.value;
					contactInfoObj.is_primary = formData.is_primary.value;
					contactInfoObj.typeObj = _.findWhere(contactTypeObj.available_types, {id:contactInfoObj.type});
					
					// set the rest of these types to not primary
					if(contactInfoObj.is_primary === 'yes'){
						
						var sameTypes = _.where(dataMap.available_types, {type:contactInfoObj.type});
						_.each(sameTypes, function(toUpd){
							
							toUpd.is_primary = 'no';
							
						});
						
					}
					
					if(!dataMap.hasOwnProperty('available_types')){
						dataMap.available_types = [];
					}
					
					dataMap.available_types.push(contactInfoObj);
					
					ui.refresh();
					
				}
				
				function contactInfoTypeForm(type){
					
					this.empty();
					
					var formArgs = getContactInfoTypeFormArgs(type);
					formArgs.contactInfoType = {
						name:'contactInfoType',
						type:'hidden',
						value:type.id
					};
					formArgs.data_type = {
						name:'data_type',
						type:'hidden',
						value:type.data_type
					};
					
					this.makeNode('form', 'form', formArgs);
					this.process = processContactInfoTypeForm;
					
					var ui = this;
					
					this.makeNode('add', 'button', {text:'<i class="fa fa-check" aria-hidden="true"></i> Okay', css:'pda-btnOutline-primary'})
						.notify('click', {
							type:'csv-uploader-run',
							data: {
								run:function(){
	
									ui.process();
									
								}
							}
						}, sb.moduleId);
						
					this.patch();
					
				}
				
				// get contact info type
				sb.data.db.obj.getById('contact_types', dataMap.type, function(contact_type){
					
					contactTypeObj = contact_type;
					ui.refresh = displayContactInfo.bind(ui, contact_type);
					ui.refresh();
					
				}, 1);
				
			}
			
			this.empty();
			
			var ui = this;
			
			// nav btns
			this.makeNode('header', 'div', {css: 'ui secondary borderless fluid horizontal menu'});
			
			this.previousStep = go_back;
			this.nextStep = process_step;
			
			this.header.makeNode('back', 'button', {
				css:		'ui left floated button item'
				, text:	'<i class="arrow left icon"></i> Back'
			}).notify('click', {
					type:'csv-uploader-run',
					data:{
						
						run: function(){
							
							ui.previousStep();
							
						}
						
					}
				}, sb.moduleId);
				
			this.header.makeNode('next', 'button', {
				css:		'ui right floated button item'
				, text:	'Preview <i class="arrow right icon"></i>'
			}).notify('click', {
					type:'csv-uploader-run',
					data:{
						
						run: function(){
							
							ui.nextStep();
							
						}
						
					}
				}, sb.moduleId);
			
			// title and text
			this.makeNode('title', 'div', {
				tag: 	'h1'
				, text:	'Map your contact info'+
						'<div class="sub header">'+
							'Match the columns in your uploaded file to your contact\'s info.'+
						'</div>'
				, css:	'ui header'
			});
			
			this.makeNode('break', 'lineBreak', {});
			this.makeNode('main', 'div', {css: 'ui fluid container'});
			
			this.main.makeNode('left', 'div', {});
			
			this.main.makeNode('right', 'div', {});
			
			this.main.left.view = initial_map;
			this.main.right.view = contact_info;
			
			this.patch();
			
			this.main.left.view();
			this.main.right.view();
			
		}
		
		// STEP THREE
		function preview_contacts(){
			
			var ui = this;
			var uploadObj = {
				tagged_with:[]
			};
			
			function go_back(){
				
				ui.view = contact_info;
				ui.view();
				
			}
			
			function completeSetup(){
				
				var post = {
					file:filePost,
					translation:dataMap,
					rows_transcribed:0,
					rows_transcribed_and_related:0,
					status:'not_ready'
				};
				
				sb.data.db.obj.create('csv_upload', post, function(response){
							
					// RIGHT
					ui.view = selectTags;
					ui.view(response);
												
					/*
sb.notify({
						type:'side-nav-change-page',
						data:{
							dataId:'contact'
						}
					});
*/
					
				});
				
			}
			
			// !TODO: use this cols obj to general extra cols for contactInfo data they have added
			var visibleCols = {
					fname:'First Name',
					lname:'Last Name',
					company:'Company'
				},
				cells = {
					company:function(obj){
						return obj.company;
					}
				};
			
			// !TODO: display multiple datas of the same type in the same column, highlighting the primary datum
			if(dataMap.hasOwnProperty('available_types') && !_.isEmpty(dataMap.available_types)){

				_.each(dataMap.available_types, function(extraCol, i){
					
					switch(extraCol.data_type){
						
						case 'address':
							visibleCols[extraCol.typeObj.name] = extraCol.typeObj.name;
							cells[extraCol.typeObj.name] = function(type, obj){
								
								var address = _.findWhere(obj.available_types, {type:type});
								
								return address.street +' '+ address.city +', '+ address.state +' '+ address.zip +' '+ address.country;
								
							}.bind({}, parseInt(extraCol.type))
							break;
							
						default:
							visibleCols[extraCol.typeObj.name] = extraCol.typeObj.name;
							cells[extraCol.typeObj.name] = function(type, obj){

								return _.findWhere(obj.available_types, {type:type}).info;
								
							}.bind({}, parseInt(extraCol.type))
// 							cols[]
							break;
						
					}
					
				});
				
			}
			
			function pageData(page, callback){
				
				var ret = {
					data:[]
				};
				
				var count = 0;
				for(i = page.page; i < page.page +page.pageLength; i++){
					
					if(fileData[i] !== undefined){
						
						var temp = {
							id:i,
							fname:fileData[i][dataMap.fname],
							lname:fileData[i][dataMap.lname],
							company:fileData[i][dataMap.company],
							available_types:[]
						}
						_.each(dataMap.available_types, function(contact_info){
							
							switch(contact_info.data_type){
								
								case 'address':
									temp.available_types.push({
										type:contact_info.type,
										is_primary:contact_info.is_primary,
										data_type:contact_info.data_type,
										street:fileData[i][contact_info.street],
										city:fileData[i][contact_info.city],
										state:fileData[i][contact_info.state],
										zip:fileData[i][contact_info.zip],
										country:fileData[i][contact_info.country]
									});
									break;
								
								default:
									temp.available_types.push({
										type:contact_info.type,
										is_primary:contact_info.is_primary,
										data_type:contact_info.data_type,
										info:fileData[i][contact_info.info]
									});
									break;
								
							}
							
						});
						
						ret.data.push(temp);
						count++;
						
					}
					
				}
				ret.recordsTotal = count;
				
				console.log(ret);
				callback(ret);
				
			}
			
			this.empty();
			
			this.previousStep = go_back;
			this.completeSetup = completeSetup;
			
			this.makeNode('menu', 'div', {css: 'ui secondary borderless fluid horizontal menu'})
				.makeNode('back', 'div', {
					tag: 	'ui left floated button item'
					, text:	'<i class="left arrow icon"></i> Back'
			}).notify('click', {
					type:'csv-uploader-run',
					data:{
						run: function(){
							ui.previousStep();
						}
					}
				}, sb.moduleId);
			
			this.menu.makeNode('next', 'button', {
				text:	'Select tags <i class="ui right arrow icon"></i>'
				, css:	'ui right floated button item'
			}).notify('click', {
					type:'csv-uploader-run',
					data: {
						run:function(){
							ui.completeSetup();
						}
					}
				}, sb.moduleId);
			
			this.makeNode('title', 'div', {
				tag: 	'h1'
				, text:	'Preview contacts'+
						'<div class="sub header">'+
							'Check that the data looks correct.'+
						'</div>'
				, css:	'ui header'
			});
			
			this.makeNode('br', 'lineBreak', {});			
			this.makeNode('preview', 'div', {css: 'ui fluid container'});
			
			this.patch();
			
			var pageLength = 10;
			
			var tableSetup = {							
				domObj:this.preview,
				objectType:'contacts',
				childObjs:1,
				tableTitle:'Contacts preview',
				navigation:false,
				searchObjects:false,
				settings:false,					
				filters:false,
				download:false,
				headerButtons:{},
				rowSelection:false,
				multiSelectButtons:false,
				home:false,
				rowLink:false,
				visibleCols:visibleCols,
				cells: cells,
				data:pageData
			};
				
			if(comps.hasOwnProperty('tags')){
				
				comps.tags.destroy();
				delete comps.tags;
				
			}
				
			if(comps.hasOwnProperty('previewTable')){
				
				comps.previewTable.destroy();
				delete comps.previewTable;
				
			}
			
			comps.previewTable = sb.createComponent('crud-table');
			comps.previewTable.notify({
				type: 'show-table',
				data: tableSetup
			});
			
/*
			for(i=0; i<pageLength; i++){
				
				if(i < fileData.length){
					
					console.log(fileData[i]);
					
				}
				
			}
			
			if(fileData.length > pageLength){
				
				// !TODO: next page btn
				
			}
*/
			
		}
		
		function selectTags(uploadObj){
			
			function completeSetup(){
				
				sb.dom.alerts.ask({
					
					title:'Are you sure?',
					text:'',
					showLoaderOnConfirm:true
					
				}, function(resp){
					
					if(resp){
						
						var post = {
							id: 		uploadObj.id
							, status:	'not_started'
						};
						
						sb.data.db.obj.update('csv_upload', post, function(response){
							
							sb.dom.alerts.alert(
								'Uploaded!'
								, 'Your csv data will be uploaded now -- we\'ll send you an email when the upload is complete.'
								, 'success'
							);
							onComplete();
							
						});
						
					}
					
				});
				
			}
			
			this.empty();
			
			// tags selection
			this.makeNode('tagstitle', 'div', {
				tag:		'h1'
				, text: 	'What should these contacts be tagged with?'
				, css:	'ui header'
			});
			this.makeNode('break', 'lineBreak', {});
			this.makeNode('tags', 'div', {});
			this.makeNode('break2', 'lineBreak', {});
			
			this.makeNode('startUploadBtn', 'div', {
				tag:'button',
				css:'ui right floated blue button',
				text:'Start upload'
			});
			
			this.makeNode('startUploadBtn', 'button', {
				text:'<i class="ui check icon"></i> Start upload'
				, css:'ui primary fluid button'
			}).notify('click', {
					type:'csv-uploader-run',
					data: {
						run:function(){
							completeSetup();
						}
					}
				}, sb.moduleId);
			
			this.patch();
			
			comps.tags = sb.createComponent('tags');
			comps.tags.notify({
				type: 'object-tag-view',
				data: {
					domObj: 		this.tags,
					objectType: 	'csv_upload',
					objectId: 	uploadObj.id
				}
			});
			
		}
		
		// main container
		this.makeNode('main', 'container', {});
		
		this.build();
		
		this.main.view = upload_form;
		this.main.view();
		
	}
	
	return {
		
		init: function(){
			
			// set initial lister for module startup
			sb.listen({
				'show-csv-uploader': this.load,
				'show-csv-contact-uploader': this.contactsUploader
			});
			
		},
		
		load: function(setup){
			
			/*
sb.data.db.obj.getAll('contacts', function(response){
				console.log(response);
			}, 1);
*/
			
			// set listeners for module while active
			sb.listen({
				
				'add-object-type-btn-clicked': this.addObjectType,
				
				'add-translation-snippet-btn-clicked': this.addTranslationSnippet,
				
				'get-add-obj-type-form-btn-clicked': this.getAddObjTypeForm,
				
				'csv-uploader-destroy': this.destroy,
				
				'crud-view-csv_upload-object': this.showTranslator,
				
				'csv-prop-btn-clicked': this.showProperty,
				
				'new-prop-translation-btn-clicked': this.showNewPropertyTranslationModal,
				
				'save-csv-translation-btn-clicked': this.saveTranslation,
				
				'show-uploads-list-btn-clicked': this.showUploadList,
				
				'show-settings-btn-clicked': this.showSettingsView,
				
				'translation-obj-type-selected': this.updateObjectPropertiesSelection,
				
				'update-data-refs-btn-clicked': this.updateObjectReferences2,
				
				'upload-data-btn-clicked': this.createObjects,
				
				'use-csv-data-col-val-btn-clicked': this.addStringToCodeTextbox,
				'use-ref-obj-prop-option-btn-clicked': this.addStringToCodeTextbox,
				
				'test-property-translation-btn-clicked': this.testPropertyTranslation,
				
				'toggle-ref-on-other': this.toggleRefOnOther,
				'toggle-use-refs-in-edit-modal': this.toggleUseRefsInForm
				
			});
			
			// accept dom reference -- if none provided, return false
			if(setup.hasOwnProperty('domObj')){ ui = sb.dom.make(setup.domObj.selector); }else{ return false; }
			
			ui.makeNode('p', 'panel', {header: '<i class="fa fa-files-o" aria-hidden="true"></i> CSV Reader'});
			
			// divvy up ui into sections
			header_ui = ui.p.body.makeNode('h', 'column', {width: 12});
			header_ui.view = show_header_ui;
			
			files_ui = ui.p.body.makeNode('l', 'column', {width: 12});
			files_ui.view = show_files_ui;
			
			translator_ui = ui.p.body.makeNode('r', 'column', {width: 12, css: 'hidden'});
			translator_ui.view = show_translator_ui;
			
			settings_ui = ui.p.body.makeNode('settings', 'column', {width: 12, css: 'hidden'});
			settings_ui.view = show_settings_ui;
			
			modal_ui = ui.makeNode('modal', 'modal', {});
			modal_ui.view = view_modal_ui;
			
			ui.build();
			
			// start up ui sections
			header_ui.view(viewState, {});
			files_ui.view(comps);
			translator_ui.view(csvData, '', translation, blueprints);
			
			sb.data.db.blueprint.getAll(function(bps){
				
				blueprints = bps;
				
			});
			
			/*
sb.data.db.obj.getSum('csv_upload', 'rows', {source: 229497}, function(response){
				console.log(response);
			});
*/
			
		},
		
		addStringToCodeTextbox: function(data){
			
			var textarea = $(data.form.translation.selector);
			var i = textarea.prop("selectionStart");

			var formData = data.form.process().fields.translation.value;
			
			data.form.translation.update(formData.slice(0, i + 1) + data.insertString + formData.slice(i - 1) );
// 			textarea.focus();
//             textarea.setSelectionRange(i + ('data.'+ data.col).length, i + ('data.'+ data.col).length);
// 			var range = textarea.createTextRange();
//             range.move('character', i + ('data.'+ data.col).length);
//             range.select();
			
		},
		
		addTranslationSnippet: function(data){
			
			// function data
			var	formData = data.form.process().fields,
				blueprint = _.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0],
				property = formData.prop_name.value,
				translation_code = formData.translation.value;

			if(_.isEmpty(translation.snippets)){
				translation.snippets = {};
			}

			// add new snippet to translation object
			translation.snippets[property] = {code: translation_code};
			
			// set whether this references other objects (flag to get these values in second db pass)
			if(formData.hasOwnProperty('use_refs')){
				
				translation.snippets[property].use_refs = 'yes';
				translation.snippets[property].object_type = parseInt(formData.ref_obj_type.value);
				translation.snippets[property].field_to_use = formData.ref_obj_from_field.value;
				
				if(formData.hasOwnProperty('ref_on_other')){
					translation.snippets[property].ref_on_other = 'yes';
					translation.snippets[property].on_my_obj_field = formData.on_my_obj_field.value;
					
				}else{
					translation.snippets[property].ref_on_other = 'no';
				}
				
				if(formData.hasOwnProperty('switch_on_obj_type')){
					translation.snippets[property].switch_on_obj_type = 'yes';
				}else{
					translation.snippets[property].switch_on_obj_type = 'no';
				}
				
			}else{
				
				translation.snippets[property].use_refs = 'no';
				
			}

			// get and log objects
// 			console.log(rowsToObjs(csvData, translation, uploadObj.source.id));
			
			// update translation display
			translator_ui.view(csvData, '', translation, blueprints);
			console.log(translation);
			
		},
		
		addObjectType: function(data){
			
			var bpId = parseInt(data.form.process().fields.obj_type.value);
			
			if(!translation.hasOwnProperty(data.add_to)){
				translation[data.add_to] = [];
			}
			
			translation[data.add_to].push({
				id: bpId
			});

			translator_ui.view(csvData, '', translation, blueprints);
			
			if(data.hasOwnProperty('modal')){
				data.modal.hide();
			}
			
			// get blueprint for current object type
			if(data.add_to === 'objectTypes'){
				if(!_.isEmpty(translation.objectTypes)){
				
					var currentBp = _.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0];
					sb.data.db.obj.getBlueprint(currentBp.blueprint_name, function(resp){
						
						bp = resp;
						
// 					}, true);
					}, false);
					
				}
			}
			
		},
		
		contactsUploader: function(setup){
			
			sb.listen({
				'csv-uploader-run':this.run,
				'csv-uploader-destroy': this.destroy
			});
			
			ui = sb.dom.make(setup.domObj.selector);
			ui.view = contacts_uploader;
			
			ui.view(setup.onComplete);
			
		},
		
		createObjects: function(){
			
			// get objects to create
			var rows = rowsToObjs(csvData, translation, uploadObj.source.id);
			rows = rows[_.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0].blueprint_name];
			
			// bring up status modal
			var status_ui = modal_ui.view('create-objs', blueprints, {created: 0, total: rows.length});
			createObjs(rows, uploadObj, status_ui, function(response){
				
				if(response){
					sb.dom.alerts.alert('Success!', 'Objects Created', 'success');
				}
				
			}, uploadObj.rows_transcribed);
			
		},
		
		getAddObjTypeForm: function(data){
			
			modal_ui.view('add-object-type', blueprints, data);
			modal_ui.show();
			
		},
		
		objectsUploader: function(setup){
			
			console.log(setup);
			
		},
		
		run: function(data){
			
			data.run(data);
			
		},
		
		saveTranslation: function(){
			
			sb.data.db.obj.update('csv_upload', {id: uploadObj.id, translation: translation, rows: csvData.length, rows_transcribed: uploadObj.rows_transcribed, rows_transcribed_and_related: uploadObj.rows_transcribed_and_related}, function(response){
				
				if(response){
					sb.dom.alerts.alert('Saved!', 'Updates have been saved.', 'success');
				}else{
					sb.dom.alerts.alert('Oops!', 'There was a problem saving -- please refresh and try again.', 'error');
				}
				
			});
			
		},
		
		showNewPropertyTranslationModal: function(data){
			
			modal_ui.view('add-prop-translation', blueprints, {prop: data.prop, csv: csvData, translation: translation});
			modal_ui.show();
			
		},
		
		showProperty: function(data){
			
			translator_ui.view(csvData, data.prop);
			translator_ui.translation_view(data.prop);
			
		},
		
		showTranslator: function(upload){
			
			uploadObj = upload;
			translation = upload.translation;
			
			if(_.isEmpty(translation)){
				translation = {};
			}
			
			if(_.isEmpty(translation.snippets)){
				translation.snippets = {};
			}
			
			sb.data.files.read(upload.file, function(fileData){

// 				csvData = csvToObjs(fileData);
				csvData = fileData;
				
				viewState.view = 'single';
				header_ui.view(viewState, uploadObj);
				
				files_ui.hide();
				
				translator_ui.view(csvData, '', translation, blueprints);
				translator_ui.show();
				
			});
			
			if(!_.isEmpty(translation.objectTypes)){
				
				var currentBp = _.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0];
				sb.data.db.obj.getBlueprint(currentBp.blueprint_name, function(resp){
					
					bp = resp;
					
// 				}, true);
				}, false);
				
			}
			
		},
		
		showUploadList: function(){
			
			viewState.view = 'list';
			header_ui.view(viewState, {});
			
			settings_ui.hide();
			files_ui.show();
			
		},
		
		updateObjectPropertiesSelection: function(data){
			
			var bpId = parseInt(data.form.process().fields.obj_type.value);
			
			var bp = _.where(blueprints, {id: bpId})[0],
				propOptions = [];
			
			_.each(bp.blueprint, function(prop, propKey){
				propOptions.push({
					name: prop.name,
					value: propKey
				});
			});
			
			data.form.prop_name.update({
				type: 'select',
				options: propOptions
			});
			
			data.form.translation.update({
				type: 'textbox'
			});
			
		},
		
		showSettingsView: function(){
			
			viewState.view = 'settings';
			
			files_ui.hide();
			translator_ui.hide();
			settings_ui.view(comps);
			
		},
		
		// !TODO: get reference data and log with test if needed
		testPropertyTranslation: function(data){
			
			// function data
			var	formData = data.form.process().fields,
				blueprint = _.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0],
				property = formData.prop_name.value,
				translation_code = formData.translation.value;

			if(_.isEmpty(translation.snippets)){
				translation.snippets = {};
			}

			// add new snippet to translation object
			translation.snippets[property] = {code: translation_code};
			
			// set whether this references other objects (flag to get these values in second db pass)
			if(formData.hasOwnProperty('use_refs')){
				translation.snippets[property].use_refs = 'yes';
				translation.snippets[property].object_type = parseInt(formData.ref_obj_type.value);
				translation.snippets[property].field_to_use = formData.ref_obj_from_field.value;
			}else{
				translation.snippets[property].use_refs = 'no';
			}

			// get and log objects
			console.log(rowsToObjs(csvData, translation, uploadObj.source.id));
			
			// update translation display
			translator_ui.view(csvData, '', translation, blueprints);
			
		},
		
		toggleUseRefsInForm: function(data){
			
			var formData = data.form.process().fields;
			if(formData.hasOwnProperty('use_refs')){
				
				if(!formData.hasOwnProperty('switch_on_obj_type')){
					
					data.form.translation.update({
						type: 'hidden',
						label: 'Translation Code (set <i>value</i> to new object property value)'
					});
					
				}else{
					
					data.form.translation.update({
						type: 'textbox',
						label: 'Translation Code (set <i>value</i> to reference object type)'
					});
					
				}
				
				data.form.ref_obj_type.update({
					type: 'select'
				});
				
				var csvPropOptions = [];
				_.each(csvData[0], function(v, key){
					
					csvPropOptions.push({
						name: key,
						value: key
					});
					
				});
				
				data.form.ref_obj_from_field.update({
					type: 'select',
					options: csvPropOptions
				});
				
			}else{
				
				data.form.translation.update({
					type: 'textbox'
				});
				
				data.form.ref_obj_type.update({
					type: 'hidden'
				});
				
				data.form.ref_obj_from_field.update({
					type: 'hidden'
				});
				
			}
			
		},
		
		toggleRefOnOther: function(data){
			
			var formData = data.form.process().fields;
			if(formData.hasOwnProperty('ref_on_other')){
				
				var	options = [],
					bp = _.where(blueprints, {id: parseInt(formData.ref_obj_type.value)})[0];
				
				_.each(bp.blueprint, function(field, key){
					
					options.push({
						name: key,
						value: key
					});
					
				});
				
				data.form.ref_obj_from_field.update({
					options: options
				});
				
				data.form.on_my_obj_field.update({type: 'select'});
				
			}else{
				
				var csvPropOptions = [];
				_.each(csvData[0], function(v, key){
					
					csvPropOptions.push({
						name: key,
						value: key
					});
					
				});
				
				data.form.ref_obj_from_field.update({
					options: csvPropOptions
				});
				
				data.form.on_my_obj_field.update({type: 'hidden'});
				
			}
			
		},
		
		// !TODO: batch these database calls
		updateObjectReferences: function(){
			
			// bring up status modal
			var status_ui = modal_ui.view('update-objs', blueprints, {done: 0, total: 0});
			
			var blueprint = _.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0];
			
			// get objects to update
			sb.data.db.obj.getWhere(blueprint.blueprint_name, {data_source: uploadObj.source.id}, function(objs){
				
				// get reference objects
				getReferenceData(translation.refObjs, blueprints, uploadObj.source.id, function(ref_data){
					
					// get objects to create
					var rows = rowsToObjs(csvData, translation, uploadObj.source.id, ref_data);
					rows = rows[_.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0].blueprint_name];

					updateObjs(rows, uploadObj, status_ui, objs, ref_data, function(response){
						
						if(response){
							sb.dom.alerts.alert('Success!', 'Objects Created', 'success');
						}
						
					});
	
						
				});
				
			});
			
		},
		
		updateObjectReferences2: function(){
			
			// bring up status modal
			var status_ui = modal_ui.view('update-objs', blueprints, {done: 0, total: 0});
			
			var blueprint = _.where(blueprints, {id: parseInt(translation.objectTypes[0].id)})[0];
			
			// get new data for updates
			updateObjs2(csvData, blueprint, translation, blueprint.blueprint_name, uploadObj.source.id, status_ui, function(response){
				
				if(response){
					
					sb.dom.alerts.alert('Success!', 'Objects Updated!', 'success');
					
				}
			
// 			});
			}, uploadObj.rows_transcribed_and_related);
			
		},
		
		destroy: function(){
			
			// destroy/clear child components
			_.each(comps, function(comp){
				comp.destroy();
			});
			comps = {};
			
			// set initial lister for module re-startup
			sb.listen({
				'show-csv-uploader': this.load
			});
			
		}
		
	}
	
});