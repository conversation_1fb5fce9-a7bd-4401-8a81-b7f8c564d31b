Factory.register('tasks', function (sb) {

	var cachedSetup = {};
	var comps = {};
	var UI_CACHE = {
		modal: {},
		qaDom: {}
	};
	var qa_taskStatus = 0;
	var detailsCol_isOpen = false;
	var defaultCollectionsView = 'list';
	var onMobile = true;

	//boxviews//
	function viewTasksPieChart(dom, state, draw) {

		return;

		if(!state.boxView.settings.options){
			
			viewTasksPieChartSettings(dom, state.boxView.settings, function(saved){
								
				state.boxView.updateSettings(saved, function(done){
					
					state.boxView.settings = saved;
					
					viewTasksPieChart(dom, state, draw);
					
				});
				
			});
			
		} else {
			   
			var taskId = state.boxView.settings.options.taskType;

			sb.data.db.obj.getById('task_type', taskId, function (taskType) {

				var groupSumSetup = {
						group_type: 'Task',
						groupOn: 'state',
						is_template: 0,
						dateRange: {
							start: moment("20111031", "YYYYMMDD").format('YYYY-MM-DD HH:mm:ss.SS'),
							end: moment().add(1, 'year').format('YYYY-MM-DD HH:mm:ss.SS')
						},
						type: taskId
					};

				if(state.hasOwnProperty('layer')) {
					
					if(state.layer === 'team' || state.layer === 'project') {
						
						groupSumSetup.parent = state.pageObject.id;
						
					} else if(state.layer === 'myStuff') {
						
						groupSumSetup.parent = [+sb.data.cookie.userId];
						
					}
					
				}

				sb.data.db.obj.getGroupSum('groups', 'id', groupSumSetup,
					function (databaseData) {

						var groupedTaskTotal = [];
	
						var taskLabels = [];
	
						var taskColors = [];
	
						_.each(taskType.states, function (value, key) {
							taskLabels.push(value.name);
							if (value.color) {
								taskColors.push(value.color);
							} else {
								taskColors.push('grey');
							}
	
							var unassignedStateCounts = {};
	
							var additionalCounts = 0;
	
							if (value.isEntryPoint) {
								unassignedStateCounts = _.findWhere(databaseData, {
									grouped: "0"
								})
								if (unassignedStateCounts) {
									additionalCounts += unassignedStateCounts.grouped_total;
								}
							}
	
							var matchedData = _.findWhere(databaseData, {
								grouped: value.id.toString()
							})
							if (matchedData) {
								groupedTaskTotal.push(matchedData.grouped_total + additionalCounts)
							} else {
								groupedTaskTotal.push(0 + additionalCounts)
							}
						});
	
						var data = {
							labels: taskLabels,
							datasets: [{
								label: 'Tasks',
								data: groupedTaskTotal,
								backgroundColor: taskColors,
								borderColor: taskColors
							}]
						}
	
						var options = {
							title: {
								display: false,
								position: 'top',
								text: 'Tasks',
								fontSize: 22,
								fontColor: 'red'
							},
							legend: {
								display: false,
								position: 'right'
							}
						}
						
						dom.empty();
						
						dom.makeNode('pieChart', 'chart', {
							type: 'pie',
							data: data,
							options: options
						});
						
						dom.patch();
						
					});
	
			});
			
		}

	}

	function viewTasksPieChartSettings(ui, settings, onComplete) {

		sb.data.db.obj.getAll(
			'task_types',
			function (taskTypes) {
				
				var currentSelection;
				if(settings.options){
					currentSelection = +settings.options.taskType;
				}
				var mappedTasks = _.map(taskTypes, function (element, index, list) {
					
					var selected = false;
					if(element.id === currentSelection){
						selected = true;
					}else{
						selected = false;
					}
					
					return {
						name: element.name,
						value: element.id,
						selected: selected
					};
				});
				ui.empty();

				var formArgs = [{
					name: 'taskType',
					type: 'select',
					label: 'Select a Task Type',
					options: mappedTasks
				}];

				ui.makeNode(
					'form', 'form', formArgs
				);
				
				ui.makeNode('break', 'div', {text:'<br />'});
				
				ui.makeNode('buttons','div', {css:'ui mini buttons'});

				ui.buttons.makeNode(
					'save', 'div', {
						text: 'Save',
						css: 'ui green button'
					}
				).notify('click', {
					type: 'tasks-run',
					data: {
						run: function (ui) {
							
							ui.buttons.save.loading();
							ui.buttons.cancel.loading();
								
							var selectedTask = parseInt(ui.form.process().fields.taskType.value);
							
							settings.options = {
								taskType: selectedTask
							};
							
							onComplete(settings);
						
						}.bind({}, ui)
					}
				}, sb.moduleId);
				
				ui.buttons.makeNode('or', 'div', {css:'or'});
				
				ui.buttons.makeNode(
					'cancel', 'div', {
						text: 'Cancel',
						css: 'ui button'
					}
				).notify('click', {
					type: 'tasks-run',
					data: {
						run: function (ui) {

							ui.buttons.cancel.loading();
							ui.buttons.save.loading();
							
							onComplete(settings);
						
						}.bind({}, ui)
					}
				}, sb.moduleId);

				ui.patch();

			}
		);


	}
	
	function viewTasksByStateTable(dom, state, draw) {
		
		if(!state.boxView.settings.options){
			
			viewTasksPieChartSettings(dom, state.boxView.settings, function(saved){
								
				state.boxView.updateSettings(saved, function(done){
					
					state.boxView.settings = saved;
					
					viewTasksByStateTable(dom, state, draw);
					
				});
				
			});
			
		} else {
			
			var taskId = state.boxView.settings.options.taskType;

			sb.data.db.obj.getById('task_type', taskId, function (taskType) {

				var groupSumSetup = {
						group_type: 'Task',
						groupOn: 'state',
						is_template: 0,
						dateRange: {
							start: moment("20111031", "YYYYMMDD").format('YYYY-MM-DD HH:mm:ss.SS'),
							end: moment().add(1, 'year').format('YYYY-MM-DD HH:mm:ss.SS')
						},
						type: taskId
					};

				if(state.team || state.project) {
					
					groupSumSetup.parent = state.pageObject.id;
					
				} else if(state.myStuff) {
					
					groupSumSetup.tagged_with = [state.user];
					
				}

				sb.data.db.obj.getGroupSum('groups', 'id', groupSumSetup, function (databaseData) {

					dom.empty();
					
					dom.makeNode('table', 'div', {
						tag: 'table',
						css: 'ui very basic celled table'
					});
					
					dom.table.makeNode('thead', 'div', {
						tag: 'thead'
					});
					dom.table.makeNode('tbody', 'div', {
						tag: 'tbody'
					});
					
					dom.table.thead.makeNode('tr', 'div', {
						tag: 'tr'
					});
					
					dom.table.thead.tr.makeNode('th1', 'div', {
						tag: 'th',
						css: 'text-muted',
						text: 'Status',
						style: 'font-weight: normal;'
					});
					
					dom.table.thead.tr.makeNode('th2', 'div', {
						tag: 'th',
						css: 'text-muted',
						text: '# of tasks',
						style: 'font-weight: normal;'
					});
					
					_.each(taskType.states, function(state, i) {
						
						dom.table.tbody.makeNode('tr-'+i, 'div', {
							tag: 'tr'
						});
						
						dom.table.tbody['tr-'+i].makeNode('td1', 'div', {
							tag: 'td'
						});
						
						dom.table.tbody['tr-'+i].td1.makeNode('link', 'div', {
							tag: 'a',
							css: 'ui header',
							text: state.name,
							href: sb.data.url.createPageURL(
								  	'hqTool', 
										{
											tool: 'taskTool',
											params: {
												type: taskId,
												state: state.id
											}
										}
									)
						});

						var group = _.filter(databaseData, function(group) {
							return parseInt(group.grouped) === (state.uid);
						});
						var total = 0;

						if(!_.isEmpty(group)) {
							total = group[0].grouped_total;
						} else {
							total = '0';
						}
						
						dom.table.tbody['tr-'+i].makeNode('td2', 'div', {
							tag: 'td',
							text: total.toString()
						});
						
					});
										
					dom.patch();
					
				});
	
			});
			
		}
		
	}
	
	function viewTasksOverview (dom, state, draw) {
		
		var where = {
			group_type: 'Task',
			end_date: {
				type: 'between',
				start: moment().startOf('year').format('X'),
				end: moment().endOf('day').format('X')
			},
			status: {
				type: 'not_equal',
				value: 'done'
			},
			is_template: {
				type: 'not_equal',
				value: 1
			}
		};
		
		if (state.myStuff) {
			where.tagged_with = +state.myStuff.user;
		} else if (
			!_.isEmpty(state.pageObject)
			&& state.pageObject.group_type !== 'Headquarters'
		) {
			where.tagged_with = state.pageObject.id;
		}
		
		sb.data.db.obj.getCounts(
			'groups'
			, undefined
			, where
			, function (ret) {
				
				where = {
					group_type: 'Task',
					status: {
						type: 'not_equal',
						value: 'done'
					},
					is_template: {
						type: 'not_equal',
						value: 1
					}
				};
				
				if (state.myStuff) {
					where.tagged_with = +state.myStuff.user;
				} else if (
					!_.isEmpty(state.pageObject)
					&& state.pageObject.group_type !== 'Headquarters'
				) {
					where.tagged_with = state.pageObject.id;
				}
				
				sb.data.db.obj.getCounts(
					'groups'
					, undefined
					, where
					, function (total) {
						
						var totalTasks = '0';
						var totalColor = 'green';
						var incompleteTasks = '0';
						var incompleteColor = 'green';

						if (
							!_.isEmpty(ret)
							&& ret[0]
							&& ret[0].count
						) {

							if (ret[0].count > 0) {
								incompleteTasks = ret[0].count;
								incompleteColor = 'orange';
							}

						}

						if (
							!_.isEmpty(total)
							&& total[0]
							&& total[0].count
						) {

							if (total[0].count > 0) {
								totalTasks = total[0].count;
								totalColor = 'orange';
							}

						}

						dom.makeNode('stats', 'div', {
							css: 'ui mini horizontal statistics'
						});

						dom.stats.makeNode('incomplete', 'div', {
							css: 'ui ' + incompleteColor + ' statistic'
						});
						dom.stats.incomplete.makeNode('value', 'div', {
							css: 'value',
							text: incompleteTasks
						});
						dom.stats.incomplete.makeNode('label', 'div', {
							css: 'label',
							text: 'Due today'
						});

						dom.stats.makeNode('total', 'div', {
							css: 'ui statistic'
						});
						dom.stats.total.makeNode('value', 'div', {
							css: 'value',
							text: totalTasks.toString()
						});
						dom.stats.total.makeNode('label', 'div', {
							css: 'label',
							text: 'Total incomplete'
						});

						draw(dom);

					}
				);
				
			}
		
		);
		
	}

	// checks if a task type has been created and creates the first one if not
	function checkTaskStates(callback) {

		sb.data.db.obj.getAll('task_types', function (taskTypes) {

			if (taskTypes.length == 0) {

				var newTaskType = {
					name: 'Standard Task',
					states: [{
						uid: 1,
						name: 'Assigned',
						icon: '',
						previous: [],
						next: [2],
						isEntryPoint: 1
					}, {
						uid: 2,
						name: 'In progress',
						icon: '',
						previous: [1],
						next: [3],
						color: 'blue'
					}, {
						uid: 3,
						name: 'Done',
						icon: '',
						previous: [2],
						next: [],
						color: 'green',
						type: 'done'
					}]
				};

				sb.data.db.obj.create('task_types', newTaskType, function (taskType) {

					callback(taskType);

				});

			} else {

				callback(taskTypes[0]);

			}

		});

	}

	function build_singleTaskTile(dom, obj, compact, qaDom, closeQA) {

		var due_date = '';
		var cached_task = {};

		if (!_.isEmpty(obj.end_date)) {

			due_date = '<i class="fa fa-calendar"></i> ' + moment(obj.end_date).format('MMMM, Do YYYY');

		} else {

			due_date = '<span class="text-muted text-italic"><i class="fa fa-calendar"></i> No due date</span>';

		}

		get_follower_tasks(obj, function (obj) {

			dom.empty();

			dom.makeNode('head', 'container', {
				uiGrid: true
			});
			dom.makeNode('lb_1', 'lineBreak', {
				spaces: 1
			});
			dom.makeNode('body', 'container', {
				uiGrid: false
			});

			if (!_.isEmpty(obj.related_object) && obj.page_params !== window.location.href.substr(window.location.href.indexOf("#")) && _.isEmpty(cachedSetup)) {

				dom.head.makeNode('col1', 'column', {
					w: 14
				});
				dom.head.makeNode('col2', 'column', {
					w: 2
				});

			} else {

				dom.head.makeNode('col1', 'column', {
					w: 16
				});

			}

			dom.head.col1.makeNode('title', 'div', {
				text: '<a><h3>' + obj.name + ' <small class="text-muted">' + obj.type.name + '</small><div class="ui sub header ' + display_dateColor(obj) + '">' + due_date + '</div></h3></a>',
				css: 'ui header'
			}).notify('click', {
				type: 'tasks-run',
				data: {
					run: function (data) {

						build_taskDetails(UI_CACHE.modal, obj, qaDom);

					}
				}
			}, sb.moduleId);

			if (!_.isEmpty(obj.related_object) && obj.page_params !== window.location.href.substr(window.location.href.indexOf("#")) && _.isEmpty(cachedSetup)) {

				dom.head.col2.makeNode('link_icon', 'div', {
					text: '<a href="https:// window.location.hostname}/app/' + appConfig.instance + '/' + obj.page_params + '"><i class="fa fa-external-link"></i></a>',
					tooltip: {
						text: 'Go to related view',
						position: 'left center'
					},
					css: 'pull-right'
				}).notify('click', {
					type: 'tasks-run',
					data: {
						run: function (data) {

							if (closeQA !== undefined) {
								closeQA();
							}

						}
					}
				}, sb.moduleId);

			}

			dom.body.makeNode('stateBtn', 'div', {});

			if (compact === undefined || compact === false) {

				dom.body.makeNode('lb', 'lineBreak', {
					spaces: 1
				});

				dom.body.makeNode('assignee_wrap', 'div', {
					css: 'ui labels'
				});

				if (!_.isEmpty(obj.managers)) {

					_.each(obj.managers, function (manager) {

						dom.body.assignee_wrap.makeNode('label' + manager.id, 'div', {
							text: '<i class="user circle icon"></i>' + manager.fname + ' ' + manager.lname,
							css: 'ui label'
						});

					}, this);

				} else {

					dom.body.assignee_wrap.makeNode('none', 'div', {
						text: '<i class="user circle icon"></i> No one assigned',
						css: 'text-muted'
					});

				}

			}

			dom.patch();

			if (obj.type && obj.type.hasOwnProperty('states')) {

				sb.notify({
					type: 'view-field',
					data: {
						type:'state',
						property:'states',
						ui: dom.body.stateBtn,
						obj: obj,
						onUpdate: function (updated) {

							build_singleTaskTile(dom, updated, compact, qaDom, closeQA);

							if (dom.hasOwnProperty('onSave')) {

								dom.onSave();

							}

						}
					}
				});

			}

		});

	}

	function build_singleTask_list(dom, obj, col1, col2) {

		// THIS FUNCTION WILL BUILD TASKS IN LIST FORMAT AS SEEN IN JIRA

		var task_title = '';

		if (obj.name.length > 40) {
			task_title = obj.name.substring(0, 40) + ' ...';
		} else {
			task_title = obj.name;
		}

		dom.empty();

		dom.makeNode('grid', 'div', {
			css: 'ui grid'
		});

		dom.grid.makeNode('col1', 'div', {
			css: 'ui six wide column'
		});
		dom.grid.makeNode('col2', 'div', {
			css: 'ui six wide column'
		});
		dom.grid.makeNode('col3', 'div', {
			css: 'ui four wide column text-right'
		});

		dom.grid.col1.makeNode('title', 'div', {
			text: '<h4>' + task_title + '</h4>',
			css: 'hover-underline pointer'
		}).notify('click', {
			type: 'tasks-run',
			data: {
				run: function (data) {

					$('.taskBox').css('background-color', 'white');

					$(dom.selector).css('background-color', '#eff4ff');

					$(col1.selector).removeClass('sixteen wide');
					$(col1.selector).addClass('seven wide');

					$(col2.selector).addClass('nine wide');

					detailsCol_isOpen = true;

					build_details(col2, col1, obj, dom, detailsCol_isOpen);

				}.bind(dom)
			}
		}, sb.moduleId);

		dom.patch();

		if (obj.type && obj.type.hasOwnProperty('states')) {

			sb.notify({
				type: 'view-field',
				data: {
					type:'state',
					property:'states',
					ui: dom.grid.col2,
					obj: obj,
					options: {
						size: 'mini'
					},
					onUpdate: function (updated) {

						if (display_taskList.load && display_taskList.load.hasOwnProperty('update'))
							display_taskList.load.update(updated);

						if (detailsCol_isOpen) {
							$(dom.selector).css('background-color', '#eff4ff');
						}

						build_details(col2, col1, updated, dom, detailsCol_isOpen);

						update_tasksBadge();

					}
				}
			});

		}

	}

	function build_taskDetails(modalDom, task, qaDom, settings) {

		var dom = {};
		var wrapper_class = '';
		var mobile_style = '';

		if ($(window).width() <= 768) {
			mobile_style = 'margin-left: 0px !important; margin-right: 0px !important;';
		} else {
			mobile_style = '';
		}

		function build_head(ui, task) {

			function build_title(ui, task) {

				ui.makeNode('title', 'div', {
					text: '<h2>' + task.name + '</h2>',
					css: 'ui header'
				});

			}

			function build_menu(ui, task) {

				ui.makeNode('btnGrp', 'div', {});

				ui.btnGrp.makeNode('edit_btn', 'div', {
					text: '<i class="fa fa-pencil"></i>',
					css: 'ui orange tiny button right floated',
					tooltip: {
						text: 'Edit',
						position: 'bottom center'
					}
				}).notify('click', {
					type: 'tasks-run',
					data: {
						run: function (data) {

							edit_task(dom, task, qaDom);

						}
					}
				}, sb.moduleId);

				ui.btnGrp.makeNode('delete_btn', 'div', {
					text: '<i class="fa fa-trash"></i>',
					css: 'ui red tiny button right floated',
					tooltip: {
						text: 'Delete',
						position: 'bottom center'
					}
				}).notify('click', {
					type: 'tasks-run',
					data: {
						run: function () {

							delete_task(task, function () {}, function () {

								if (comps.hasOwnProperty('tasksList') && sb.data.url.get().page === 'initTasks') {

									update_tableData();

								}

								if (qaDom !== undefined) {

									build_quickActionTasks(qaDom, {}, undefined);

								}

								if (dom.hasOwnProperty('hide')) {

									display_taskList(cachedSetup);

								} else {

									sb.notify({
										type: 'app-navigate-to',
										data: {
											type: 'UP'
										}
									});

								}

								update_tasksBadge();

								if (qaDom === undefined && modalDom.hasOwnProperty('hide')) {

									modalDom.hide();

								}

							});

						}
					}
				}, sb.moduleId);

			}

			ui.makeNode('col1', 'div', {
				css: 'nine wide column'
			});
			ui.makeNode('col2', 'div', {
				css: 'seven wide column'
			});

			build_title(ui.col1, task);

			build_menu(ui.col2, task);

		}

		function build_body(ui, task) {

			function build_date(ui, task) {

				var due_date = '';

				if (!_.isEmpty(task.end_date)) {
					due_date = moment(task.end_date).format('MMMM, Do YYYY');
				} else {
					due_date = '<span class="text-muted"> Add a due date</span>';
				}

				ui.makeNode('date_grid', 'div', {
					css: 'ui stackable grid',
					style: mobile_style
				});

				ui.date_grid.makeNode('col1', 'div', {
					css: 'four wide column'
				});
				ui.date_grid.makeNode('col2', 'div', {
					css: 'twelve wide column'
				});

				ui.date_grid.col1.makeNode('date_label', 'div', {
					text: 'Due Date:',
					css: 'text-muted'
				});

				ui.date_grid.col2.makeNode('date', 'div', {
					text: due_date
				});

			}

			function build_details(ui, task) {

				ui.makeNode('detailsWrap', 'div', {});

				ui.detailsWrap.makeNode('title', 'text', {
					text: 'Description',
					css: 'text-muted'
				});

				if (task.description !== '') {

					ui.detailsWrap.makeNode('details', 'div', {
						text: task.description,
						css: 'ui basic segment',
						style: 'word-break: break-all;'
					});

				} else {

					ui.detailsWrap.makeNode('details', 'div', {
						text: '<p class="text-muted text-center">Add a description</p>',
						css: 'ui basic segment'
					});

				}

			}

			function build_reporter(ui, task) {

				var reporterImg = '';

				if (task.created_by == null) {
					return;
				}

				if (task.created_by.profile_image !== 0) {

					if (task.created_by.profile_image.loc !== '//') {
						reporterImg = '<img src="' + sb.data.files.getURL(task.created_by.profile_image) + '">';
					} else {
						reporterImg = '<i class="user circle icon"></i>';
					}

				} else {

					reporterImg = '<i class="user circle icon"></i>';

				}

				ui.makeNode('reporter_grid', 'div', {
					css: 'ui stackable grid',
					style: mobile_style
				});

				ui.reporter_grid.makeNode('col1', 'div', {
					css: 'four wide column'
				});
				ui.reporter_grid.makeNode('col2', 'div', {
					css: 'twelve wide column'
				});

				ui.reporter_grid.col1.makeNode('reporter_label', 'div', {
					text: 'Reporter:',
					css: 'text-muted'
				});

				ui.reporter_grid.col2.makeNode('reporter', 'div', {
					text: reporterImg + ' ' + task.created_by.fname + ' ' + task.created_by.lname,
					css: 'ui image label'
				});

			}

			function build_assignee(ui, task) {

				ui.makeNode('assignee_grid', 'div', {
					css: 'ui stackable grid',
					style: mobile_style
				});

				ui.assignee_grid.makeNode('col1', 'div', {
					css: 'four wide column'
				});
				ui.assignee_grid.makeNode('col2', 'div', {
					css: 'twelve wide column'
				});

				ui.assignee_grid.col1.makeNode('assignee_label', 'div', {
					text: 'Assignee(s):',
					css: 'text-muted'
				});

				if (!_.isEmpty(task.managers)) {

					_.each(task.managers, function (assignee) {

						var profileImg = '';

						if (assignee.profile_image !== 0) {

							if (assignee.profile_image.loc !== '//') {
								profileImg = '<img src="' + sb.data.files.getURL(assignee.profile_image) + '">';
							} else {
								profileImg = '<i class="user circle icon"></i>';
							}

						} else {

							profileImg = '<i class="user circle icon"></i>';

						}

						ui.assignee_grid.col2.makeNode('label' + assignee.id, 'div', {
							text: profileImg + ' ' + assignee.fname + ' ' + assignee.lname,
							css: 'ui image label',
							style: 'margin-bottom: 4px;'
						});

					}, this);

				} else {

					ui.assignee_grid.col2.makeNode('none', 'div', {
						text: '<i class="user circle icon"></i> No one assigned',
						css: 'text-muted'
					});

				}



			}

			function build_createdOn(ui, task) {

				ui.makeNode('created_grid', 'div', {
					css: 'ui stackable grid',
					style: mobile_style
				});

				ui.created_grid.makeNode('col1', 'div', {
					css: 'four wide column'
				});
				ui.created_grid.makeNode('col2', 'div', {
					css: 'twelve wide column'
				});

				ui.created_grid.col1.makeNode('created_label', 'div', {
					text: 'Created:',
					css: 'text-muted'
				});

				ui.created_grid.col2.makeNode('created', 'div', {
					text: moment(task.date_created).from(moment())
				});

			}

			function build_updatedOn(ui, task) {

				ui.makeNode('updated_grid', 'div', {
					css: 'ui stackable grid',
					style: mobile_style
				});

				ui.updated_grid.makeNode('col1', 'div', {
					css: 'four wide column'
				});
				ui.updated_grid.makeNode('col2', 'div', {
					css: 'twelve wide column'
				});

				ui.updated_grid.col1.makeNode('updated_label', 'div', {
					text: 'Updated:',
					css: 'text-muted'
				});

				ui.updated_grid.col2.makeNode('updated', 'div', {
					text: moment(task.last_updated).from(moment())
				});

			}

			ui.makeNode('col1', 'div', {
				css: 'nine wide column'
			});
			ui.makeNode('col2', 'div', {
				css: 'seven wide column'
			});

			ui.col1.makeNode('stateBtn', 'div', {});

			ui.col1.makeNode('lb_1', 'lineBreak', {
				spaces: 1
			});

			// Col1

			// Col1 -- Date
			build_date(ui.col1, task);

			ui.col1.makeNode('lb_2', 'lineBreak', {
				spaces: 1
			});

			// Col1 -- Description
			build_details(ui.col1, task);

			// Col2

			// Col2 -- Reporter
			build_reporter(ui.col2, task);

			ui.col2.makeNode('lb_1', 'lineBreak', {
				spaces: 1
			});

			// Col2 -- Assignees
			build_assignee(ui.col2, task);

			ui.col2.makeNode('lb_2', 'lineBreak', {
				spaces: 1
			});

			// Col2 -- Create On
			build_createdOn(ui.col2, task);

			ui.col2.makeNode('lb_3', 'lineBreak', {
				spaces: 1
			});

			// Col2 -- Updated On
			build_updatedOn(ui.col2, task);

		}

		if (_.isEmpty(settings)) {

			settings = {
				inModal: false
			};

		}

		if (qaDom === undefined || qaDom === false) {

			dom = modalDom;

		} else {

			dom = qaDom;

		}

		if (!modalDom.hasOwnProperty('show') && !settings.inModal) {

			wrapper_class = 'ui basic segment';

		}

		get_follower_tasks(task, function (task) {

			dom.empty();

			dom.makeNode('wrapper', 'div', {
				css: 'animated fadeIn ' + wrapper_class
			});

			dom.wrapper.makeNode('head', 'div', {
				css: 'ui grid'
			});
			dom.wrapper.makeNode('lb_1', 'lineBreak', {
				spaces: 1
			});
			dom.wrapper.makeNode('body', 'div', {
				css: 'ui stackable grid',
				style: mobile_style
			});
			dom.wrapper.makeNode('lb_2', 'lineBreak', {
				spaces: 3
			});
			dom.makeNode('notesWrap', 'div', {});

			// Head
			build_head(dom.wrapper.head, task);

			// Body
			build_body(dom.wrapper.body, task);

			dom.patch();

			if (qaDom === undefined || qaDom === false) {

				if (modalDom.hasOwnProperty('isActive') && modalDom.isActive() === false) {

					modalDom.show();

				}

			}

			sb.notify({
				type: 'show-note-list-box',
				data: {
					domObj: dom.notesWrap,
					objectIds: [task.id],
					objectId: task.id,
					collapse: 'open'
				}
			});

			if (task.type && task.type.hasOwnProperty('states')) {

				sb.notify({
					type: 'view-field',
					data: {
						type:'state',
						property:'states',
						ui: dom.wrapper.body.col1.stateBtn,
						obj: task,
						options: {
							size: 'mini'
						},
						onUpdate: function (updated) {
	
							if (modalDom.hasOwnProperty('isActive') && modalDom.isActive() === true) {
	
								display_taskList(cachedSetup);
	
							}
	
							build_taskDetails(modalDom, updated, qaDom);
	
						}
					}
				});

			}

		});

	}

	function build_quickActionTasks(dom, state, draw) {

		var task_states = {
			0: 'Assigned',
			1: 'In Progress',
			2: 'Completed'
		};

		function load(status) {

			sb.data.db.obj.getWhere('tasks', {
				assignee: {
					type: 'contains',
					value: sb.data.cookie.userId
				},
				status: status,
				childObjs: 1
			}, function (tasksList) {

				dom.wrapper.body.empty();

				dom.wrapper.head.col2.btnGrp.makeNode('create_btn', 'div', {
					text: '<i class="fa fa-plus"></i> New Task',
					css: 'ui tiny green button right floated'
				}).notify('click', {
					type: 'tasks-run',
					data: {
						run: function (data) {

							create_newTask(dom.wrapper, function () {

								build_quickActionTasks(dom, state, draw);

							}, undefined, dom);

						}
					}
				}, sb.moduleId);

				if (!_.isEmpty(tasksList)) {

					_.each(tasksList, function (task) {

						dom.wrapper.body.makeNode('taskBox-' + task.id, 'div', {
							css: 'ui basic segment'
						});

						build_singleTaskTile(dom.wrapper.body['taskBox-' + task.id], task, true, dom.wrapper, function () {

							dom.closeView();

						});

					}, this);

				} else {

					dom.wrapper.body.makeNode('no_tasks', 'div', {
						text: 'Empty',
						css: 'text-center'
					});

				}

				dom.wrapper.head.patch();
				dom.wrapper.body.patch();

			});

		}

		UI_CACHE.qaDom = dom;

		dom.empty();

		dom.makeNode('wrapper', 'container', {
			uiGrid: false
		});

		dom.wrapper.makeNode('head', 'container', {
			uiGrid: true
		});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {
			spaces: 1
		});
		dom.wrapper.makeNode('menu', 'div', {});
		dom.wrapper.makeNode('lb_2', 'lineBreak', {
			spaces: 1
		});
		dom.wrapper.makeNode('body', 'container', {
			uiGrid: false
		});

		dom.wrapper.head.makeNode('col1', 'column', {
			w: 8
		});
		dom.wrapper.head.makeNode('col2', 'column', {
			w: 8
		});

		dom.wrapper.head.col1.makeNode('title', 'div', {
			tag: 'h2',
			text: '<i class="fa fa-tasks"></i> Task List'
		});

		dom.wrapper.head.col2.makeNode('btnGrp', 'div', {});

		dom.wrapper.head.col2.btnGrp.makeNode('create_btn', 'div', {
			text: 'loading',
			css: 'ui tiny green button right floated loading'
		});

		dom.wrapper.menu.makeNode('btnGrp', 'div', {
			css: 'ui three buttons'
		});

		_.each(task_states, function (name, num) {

			var isActive = '';
			var selected = false;

			if (parseInt(num) === qa_taskStatus) {
				isActive = 'active';
			} else {
				isActive = '';
			}

			dom.wrapper.menu.btnGrp.makeNode('btn-' + num, 'div', {
				text: name,
				css: 'ui button stateBtn ' + isActive
			}).notify('click', {
				type: 'tasks-run',
				data: {
					run: function (data) {

						qa_taskStatus = +num;

						$('.stateBtn').removeClass('active');
						$(data.selector).addClass('active');

						dom.wrapper.body.empty();

						display_loader(dom.wrapper.body, 'Fetching ' + name + ' tasks ...');

						dom.wrapper.body.patch();

						load(qa_taskStatus);

					},
					selector: dom.wrapper.menu.btnGrp['btn-' + num].selector
				}
			}, sb.moduleId);

		}, this);

		display_loader(dom.wrapper.body, 'Fetching assigned tasks ...');

		load(qa_taskStatus);

		if (draw !== undefined) {

			draw({
				dom: dom,
				after: function (dom) {

					dom.patch();

				}
			});

		} else {

			dom.patch();

		}

		// ATTACH METHODS
		build_quickActionTasks.load = load;

	}

	function create_newTask(dom, onCreate, typeId, qaDom, newObj) {

		var formArgs = {
				name: {
					name: 'name',
					type: 'text',
					label: 'Task Title',
					placeholder: 'Create a budget report'
				},
				state: {
					name: 'state',
					type: 'hidden',
					options: []
				},
				end_date: {
					name: 'end_date',
					type: 'date',
					label: 'Due Date',
					placeholder: moment().endOf('month').format('MMMM Do YYYY')
				},
				type: {
					name: 'type',
					type: 'select',
					label: 'Task Type',
					options: []
				},
				managers: {
					name: 'managers',
					type: 'checkbox',
					label: 'Assignee(s)',
					options: []
				},
				description: {
					name: 'description',
					type: 'textbox',
					rows: 4,
					label: 'Description'
				},
				is_recurring: {
					name: 'is_recurring',
					type: 'check',
					label: 'Recurring?',
					options: [{
						name: 'is_recurring',
						label: 'Yes',
						value: 'yes'
					}],
					onChange: function (checked) {
						toggleRecurringForm(dom, checked);
					}
				}
			};

		var cycleOptions = [moment().day()];

		function toggleRecurringForm(ui, checked) {

			ui.wrapper.body.formWrap.recurringSched.makeNode(
				'form',
				'form', {
					cycle: {
						name: 'cycle',
						type: 'select',
						label: 'Repeat',
						value: 'weekly',
						options: [{
								name: 'Daily',
								value: 'daily'
							}, {
								name: 'Weekly',
								value: 'weekly'
							}
							/*
							, {
															name:'Montly',
															value:'monthly'
														}, {
															name:'Yearly',
															value:'yearly'
														}
							*/
						],
						change: function (form, value) {

							switch (value) {

								case 'weekly':
									$(ui.wrapper.body.formWrap.recurringSched.form.cycle_options.selector).parent().removeClass('hidden');
									break;

								default:
									$(ui.wrapper.body.formWrap.recurringSched.form.cycle_options.selector).parent().addClass('hidden');
									break;

							}

						}
					},
					cycle_options: {
						name: 'cycle_options',
						type: 'checkbox',
						label: 'Days of week',
						options: [{
							name: 'cycle_options',
							value: 1,
							label: 'Mon'
						}, {
							name: 'cycle_options',
							value: 2,
							label: 'Tue'
						}, {
							name: 'cycle_options',
							value: 3,
							label: 'Wed'
						}, {
							name: 'cycle_options',
							value: 4,
							label: 'Thur'
						}, {
							name: 'cycle_options',
							value: 5,
							label: 'Fri'
						}, {
							name: 'cycle_options',
							value: 6,
							label: 'Sat'
						}, {
							name: 'cycle_options',
							value: 0,
							label: 'Sun'
						}],
						value: cycleOptions,
						onChange: function (val) {
							cycleOptions = val;
						}
					},
					repeat_forever: {
						name: 'repeat_forever',
						type: 'check',
						label: '',
						options: [{
							name: 'repeat_forever',
							value: 'yes',
							label: '<strong>Repeat forever?</strong>'
						}],
						onChange: function (checked) {

							if (checked) {
								$(ui.wrapper.body.formWrap.recurringSched.form.repeat_end_date.selector)
									.parent().parent().addClass('hidden');
							} else {
								$(ui.wrapper.body.formWrap.recurringSched.form.repeat_end_date.selector)
									.parent().parent().removeClass('hidden');
							}

						}
					},
					repeat_end_date: {
						name: 'repeat_end_date',
						type: 'date',
						label: 'End on',
						placeholder: moment().endOf('month').format('MMMM Do YYYY')
					}
				}
			);

			ui.wrapper.body.formWrap.recurringSched.patch();

			if (checked) {

				ui.wrapper.body.formWrap.recurringSched.removeClass('hidden');

			} else {

				ui.wrapper.body.formWrap.recurringSched.addClass('hidden');

			}

		}

		function process_createTask(form, schedForm, loading, after, users) {

			var formData = form.process().fields;
			var obj = {};
			var assignees = [];

			// CHECKS
			if (formData.name.value === '') {

				sb.dom.alerts.alert(
					'Incomplete Form',
					'Please enter a title.',
					'error'
				);

				return;

			}

			loading();

			obj.name = formData.name.value;
			obj.start_date = formData.end_date.value; // for calendar display
			obj.end_date = formData.end_date.value;
			obj.description = formData.description.value;
			obj.group_type = 'Task';
			obj.type = parseInt(formData.type.value);

			if (typeId !== undefined) {
				obj.parent = parseInt(typeId);
				obj.related_object = parseInt(typeId);
				obj.page_params = window.location.href.substr(window.location.href.indexOf("#"));
			}

			if (formData.hasOwnProperty('managers') && !_.isEmpty(formData.managers.value)) {

				if (typeof formData.managers.value === 'string') {

					assignees.push(+formData.managers.value);

				} else {

					_.each(formData.managers.value, function (id) {

						assignees.push(+id);

					}, this);

				}

			}

			// recurring tasks properties
			obj.is_recurring = false;
			if (
				formData.hasOwnProperty('is_recurring') &&
				formData.is_recurring.value
			) {

				if (!obj.end_date) {

					sb.dom.alerts.alert(
						'Incomplete Form',
						'Recurring tasks need a due date specified',
						'error'
					);
					return false;

				}

				obj.is_recurring = 1;

			}

			var schedInput = schedForm.process().fields;

			obj.cycle = schedInput.cycle.value;
			obj.repeat_forever = false;

			if (
				schedInput.hasOwnProperty('repeat_forever') &&
				schedInput.repeat_forever.value
			) {

				obj.repeat_forever = true;

			} else if (obj.is_recurring) {

				obj.repeat_end_date = schedInput.repeat_end_date.value;

				if (!obj.repeat_end_date) {

					sb.dom.alerts.alert(
						'Incomplete Form',
						'Recurring tasks not set to repeat forever need an end date.',
						'error'
					);
					return false;

				}

			}

			obj.schedule_options = cycleOptions;
			obj.managers = assignees;
			obj.tagged_with = _.union(assignees);

			if (newObj && newObj.tagged_with) {
				obj.tagged_with = obj.tagged_with.concat(newObj.tagged_with);
			}

			obj.tagged_with = _.uniq(obj.tagged_with);

			// if description is empty, place some markup for an empty checklist
			if (_.isEmpty(obj.description)) {
				obj.description = '<p><strong>Todos:</strong></p><ul data-checked="false"><li><br></li></ul>';
			}

			checkTaskStates(function (taskState) {

				if (newObj) {

					if (newObj.hasOwnProperty('parent')) {
						
						if (
							newObj.parent !== undefined
							&& newObj.parent.hasOwnProperty('id')
						) {
							obj.parent = newObj.parent.id;
							obj.tagged_with.push(newObj.parent.id);	
						}
					}

					if (newObj.hasOwnProperty('state')) {
						obj.state = newObj.state;
					}
					
					if(newObj.hasOwnProperty('is_template') && newObj.is_template === 1) {
						obj.is_template = 1;
					}

				}

				// turn notifications on for users
				obj.notify = obj.tagged_with;

				sb.data.db.obj.create('groups', obj, function (newTaskObj) {

					if (newTaskObj.managers.length > 0) {

						var assignedByString = '';

						if (newTaskObj && newTaskObj.created_by) {

							assignedByString = newTaskObj.created_by.fname + ' ' + newTaskObj.created_by.lname;

						}

						var notificationEvt = { // This event is for assigned users
							title: assignedByString + ' assigned you ' + newTaskObj.name,
							details: newTaskObj.description,
							producer: newTaskObj.id,
							color: 'orange',
							icon: 'clipboard check',
							type: 'assignment',
							link: sb.data.url.createPageURL(
								'object', {
									id: newTaskObj.id,
									type: 'task',
									name: newTaskObj.name
								}
							),
							notify: assignees

						}

						sb.data.db.obj.notify(notificationEvt, function (res) {});

					}

					var noteObj = {
							type_id: newTaskObj,
							type: 'task',
							note: 'Task created by ' + assignedByString + '.',
							record_type: 'log',
							author: sb.data.cookie.get('uid'),
							notifyUsers: [],
							notification: {
								producer: newTaskObj.id
							},
							log_data: {
								type: 'create',
								objectName: newTaskObj.name,
								details: newTaskObj.description
							},
							icon: {
								icon: 'plus',
								color: 'green'
							}
						};

					sb.data.db.obj.postComment(noteObj, function (newNote) {

						update_tasksBadge();
						after(newTaskObj);

					});

				}, 1);

			});

		}

		if (qaDom !== undefined) {

			dom.empty();

		}

		//dom.empty();

		dom.makeNode('wrapper', 'div', {});

		dom.wrapper.makeNode('head', 'div', {});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {
			spaces: 1
		});
		dom.wrapper.makeNode('body', 'div', {});
		dom.wrapper.makeNode('lb_2', 'lineBreak', {
			spaces: 1
		});
		dom.wrapper.makeNode('btnGrp', 'div', {});

		dom.wrapper.head.makeNode('title', 'div', {
			text: 'Create New Task',
			tag: 'h1',
			css: 'text-center'
		});

		dom.wrapper.btnGrp.makeNode('save_btn', 'div', {
			text: 'loading',
			css: 'ui green button right floated loading'
		});

		//display_loader(dom.wrapper.body, 'Fetching task types ...');

		dom.patch();

		sb.data.db.obj.getAll('task_types', function (types) {

			types = _.sortBy(types, function (o) {
				return o.name;
			});

			_.each(types, function (typeObj) {

				formArgs.type.options.push({
					name: typeObj.name,
					value: typeObj.id
				});

				_.each(typeObj.states, function (state) {

					formArgs.state.options.push({
						name: state.name,
						value: state.id
					});

				});

				if (typeObj.states[0]) {
					formArgs.state.value = typeObj.states[0].id;
				}

			});

			if (newObj && newObj.type && parseInt(newObj.type) > 0) {
				formArgs.type.value = newObj.type;
			}

			dom.wrapper.body.empty();

			display_loader(dom.wrapper.body, 'Fetching users list...');

			dom.wrapper.body.patch();

			sb.data.db.obj.getWhere('users', {
				enabled: 1
			}, function (users) {

				if (users) {

					users = _.sortBy(users, function (obj) {
						return obj.fname;
					});

					_.each(users, function (userObj, i) {

						var assignee = {
								name: 'user',
								label: userObj.fname + ' ' + userObj.lname,
								value: userObj.id
							};

						if (userObj.id === +sb.data.cookie.get('uid')) {
							assignee.checked = true;
						}

						formArgs.managers.options.push(assignee);

					}, this);

					dom.wrapper.body.empty();

					dom.wrapper.body.makeNode('formWrap', 'div', {});
					dom.wrapper.body.formWrap.makeNode('form', 'form', formArgs);
					dom.wrapper.body.formWrap.makeNode('br', 'div', {
						text: '<br />'
					});
					dom.wrapper.body.formWrap.makeNode('recurringSched', 'div', {});

					dom.wrapper.btnGrp.makeNode('save_btn', 'div', {
						text: '<i class="fa fa-floppy-o"></i> Save',
						css: 'ui green button right floated'
					}).notify('click', {
						type: 'tasks-run',
						data: {
							run: function (data, users, dom) {

								if (dom.wrapper.body.formWrap) {

									process_createTask(

										dom.wrapper.body.formWrap.form,
										dom.wrapper.body.formWrap.recurringSched.form,

										function () {

											dom.wrapper.body.empty();

											display_loader(dom.wrapper.body, 'Creating new task ...');

											dom.wrapper.btnGrp.makeNode('save_btn', 'div', {
												text: 'loading',
												css: 'ui green button right floated loading'
											});

											dom.wrapper.body.patch();
											dom.wrapper.btnGrp.patch();

										},
										function (newTask) {

											// if (ga) {
											// 	ga('send', {
											// 		hitType: 'event',
											// 		eventCategory: 'Tasks',
											// 		eventAction: 'Create'
											// 	});
											// }

											if (qaDom === undefined || qaDom === false) {

												onCreate(newTask);

												dom.hide();

											} else {

												onCreate(newTask);
											}

										},
										users
									);
								}


							}.bind({}, {}, users, dom)
						}
					}, sb.moduleId);

					dom.wrapper.body.patch();
					dom.wrapper.btnGrp.patch();

					toggleRecurringForm(dom, false);

				}

			}, 1);

		}, 1);

		if (dom.show) {
			dom.show();
		}

	}

	function delete_task(task, loading, after) {

		var notesIds = [];

		sb.dom.alerts.ask({
			title: 'Are you sure?',
			text: 'You want to delete this task and its related notes?'
		}, function (resp) {

			if (resp) {

				if (loading !== undefined) {
					loading();
				}

				swal.disableButtons();

				sb.data.db.obj.erase('groups', [task.id], function (ret) {

					if (ret) {

						sb.data.db.obj.getWhere('notes', {
							type_id: task.id,
							childObjs: {
								id: true
							}
						}, function (notes) {

							if (!_.isEmpty(notes)) {

								_.each(notes, function (task) {

									notesIds.push(task.id);

								}, this);

								sb.data.db.obj.erase('notes', notesIds, function (ret) {

									swal.close();

									if (after !== undefined) {
										after();
									}

								});

							} else {

								swal.close();

								if (after !== undefined) {
									after();
								}

							}

						});

					}

				});

			}

		});

	}

	function display_dateColor(task) {

		if (task.due_date !== '') {

			if (moment(task.due_date).unix() < moment().unix()) {
				return 'red';
			}

		}

	}

	function display_loader(dom, text) {

		dom.makeNode('load_cont', 'div', {});
		dom.load_cont.makeNode('loader', 'loader', {
			size: 'medium'
		});
		dom.load_cont.makeNode('load_text', 'div', {
			text: text,
			css: 'ui sub header center aligned'
		});

	}

	function display_taskList(setup) {

		var dom = setup.domObj;
		var collapse = '';
		var compact = false;
		var viewType = 'tile';

		function load() {

			var where = {
				group_type: 'Task',
				parent: setup.objectId,
				childObjs: {
					created_by: {
						fname: true,
						lname: true,
						profile_image: true
					},
					managers: {
						fname: true,
						lname: true,
						profile_image: true
					},
					users: {
						fname: true,
						lname: true,
						profile_image: true
					},
					description: true,
					end_date: true,
					type: {
						name: true,
						states: true
					},
					state: true,
					name: true
				}
			};

			var tasks = [];

			function update_singleTask_listView(task) {

				// THIS IS ONLY FOR THE LIST VIEW

				dom.wrapper.body.grid.col1.segs.makeNode('box' + task.id, 'div', {
					css: 'ui ' + display_taskStatus(task.status).color + ' segment taskBox'
				});

				dom.wrapper.body.grid.col1.segs.patch();

				build_singleTask_list(dom.wrapper.body.grid.col1.segs['box' + task.id], task, dom.wrapper.body.grid.col1, dom.wrapper.body.grid.col2);

			}

			/*
						if(setup.objectIds){
							where.related_object.values = where.related_object.values.concat(setup.objectIds);
						}
			*/

			sb.data.db.obj.getWhere('groups', where, function (tasksList) {

				tasks = tasksList;

				dom.wrapper.body.empty();

				if (viewType === 'list') {

					detailsCol_isOpen = false;

					dom.wrapper.body.makeNode('grid', 'div', {
						css: 'ui grid'
					});

					dom.wrapper.body.grid.makeNode('col1', 'div', {
						css: 'ui sixteen wide column hide-scrollbar',
						style: ''
					});
					dom.wrapper.body.grid.makeNode('col2', 'div', {
						css: 'ui column hide-scrollbar',
						style: ''
					});

					dom.wrapper.body.grid.col1.makeNode('segs', 'div', {
						css: 'ui basic segments'
					});

				}

				dom.wrapper.head.btnGrp.makeNode('new_task', 'div', {
					text: '<i class="fa fa-plus"></i> New Task',
					css: 'ui small green button'
				}).notify('click', {
					type: 'tasks-run',
					data: {
						run: function (data) {

							create_newTask(UI_CACHE.modal, function () {

								display_taskList(setup);

							}, setup.objectId);

						}
					}
				}, sb.moduleId);

				//tasksList = _.filter(tasksList, function(obj) { return obj.status === 0 || obj.status === 1; });

				if (!_.isEmpty(tasksList)) {

					_.each(tasksList, function (task) {

						if (viewType === 'tile') {

							dom.wrapper.body.makeNode('box' + task.id, 'div', {
								css: 'ui basic segment'
							});

							build_singleTaskTile(dom.wrapper.body['box' + task.id], task, compact, undefined);

						} else if (viewType === 'list') {

							dom.wrapper.body.grid.col1.segs.makeNode('box' + task.id, 'div', {
								css: 'ui ' + display_taskStatus(task.status).color + ' segment taskBox'
							});

							build_singleTask_list(dom.wrapper.body.grid.col1.segs['box' + task.id], task, dom.wrapper.body.grid.col1, dom.wrapper.body.grid.col2);

						}

					}, this);

				} else {

					if (viewType === 'list') {
						delete dom.wrapper.body.grid.col1.segs;
					}

					dom.wrapper.body.makeNode('no_tasks', 'div', {
						tag: 'p',
						text: 'Empty',
						css: 'text-center text-muted'
					});

				}

				dom.wrapper.patch();

			});

			// METHODS
			load.update = update_singleTask_listView;

		}

		if (setup.hasOwnProperty('collapse')) {
			collapse = setup.collapse;
		}

		if (setup.hasOwnProperty('compact')) {
			compact = setup.compact;
		}

		if (setup.hasOwnProperty('viewType')) {
			viewType = setup.viewType;
		}

		cachedSetup = setup;

		dom.empty();

		if (_.isEmpty(UI_CACHE.modal)) {

			UI_CACHE.modal = dom.makeNode('modal', 'modal', {});

		} else {

			if (UI_CACHE.modal.isActive()) {

				UI_CACHE.modal.show();

			}

		}

		if (setup.collapse) {
			dom.makeNode('wrapper', 'container', {
				title: '<i class="fa fa-tasks"></i> Tasks',
				collapse: collapse
			});
		} else {
			dom.makeNode('wrapper', 'div', {});
		}

		dom.wrapper.makeNode('head', 'div', {});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {
			spaces: 1
		});
		dom.wrapper.makeNode('body', 'div', {});
		dom.wrapper.makeNode('lb_2', 'lineBreak', {
			spaces: 1
		});

		dom.wrapper.head.makeNode('btnGrp', 'div', {
			css: 'ui center aligned container'
		});

		dom.wrapper.head.btnGrp.makeNode('new_task', 'div', {
			text: 'loading',
			css: 'ui small green button loading'
		});

		display_loader(dom.wrapper.body, 'Fetching tasks ...');

		load();

		dom.patch();

		// ATTACH METHODS
		display_taskList.load = load;

	}

	function display_taskStatus(status) {

		switch (status) {

			case 0:
				return {
					name: 'Assigned',
						color: 'orange'
				};
				break;

			case 1:
				return {
					name: 'In Progress',
						color: 'green'
				};
				break;

			case 2:
				return {
					name: 'Completed',
						color: 'grey'
				};
				break;

			default:
				return '';

		}

	}

	function edit_task(modalDom, task, qaDom) {

		var formArgs = {
			name: {
				name: 'name',
				type: 'text',
				label: 'Task Title',
				placeholder: 'Create a budget report',
				value: task.name
			},
			end_date: {
				name: 'end_date',
				type: 'date',
				label: 'Due Date',
				placeholder: moment().endOf('month').format('MMMM Do YYYY'),
				value: moment(task.end_date)
			},
			state: {
				name: 'state',
				type: 'select',
				label: 'Task State',
				options: [],
				value: task.state
			},
			type: {
				name: 'type',
				type: 'hidden',
				label: 'Task Type',
				options: [],
				value: task.type
			},
			managers: {
				name: 'managers',
				type: 'checkbox',
				label: 'Assignee(s)',
				options: [],
				value: []
			},
			description: {
				name: 'description',
				type: 'textbox',
				rows: 4,
				label: 'Description',
				value: task.description
			}
		};
		var userIds = [];
		var dom = {};

		function process_editForm(form, task, loading, after) {

			var formData = form.process().fields;
			var leadsList = [];
			var assigneeList = [];
			var obj = {};

			if (formData.name.value === '') {

				sb.dom.alerts.alert(
					'Incomplete Form',
					'Please enter a title.',
					'error'
				);

				return;

			}

			if (loading !== undefined) {
				loading();
			}

			obj.id = task.id;
			obj.name = formData.name.value;
			obj.state = formData.state.value;
			obj.type = formData.type.value;
			obj.description = formData.description.value;
			obj.group_type = 'Task';

			if (formData.end_date.value === undefined) {

				obj.end_date = '';

			} else {

				obj.end_date = formData.end_date.value;

			}

			if (formData.hasOwnProperty('managers') && !_.isEmpty(formData.managers.value)) {

				if (typeof formData.managers.value === 'string') {

					leadsList.push(parseInt(formData.managers.value));

				} else {

					_.each(formData.managers.value, function (id) {

						leadsList.push(parseInt(id));

					}, this);

				}

			}

			if (formData.hasOwnProperty('users') && !_.isEmpty(formData.users.value)) {

				if (typeof formData.users.value === 'string') {

					assigneeList.push(parseInt(formData.users.value));

				} else {

					_.each(formData.users.value, function (id) {

						assigneeList.push(parseInt(id));

					}, this);

				}

			}

			obj.managers = leadsList;
			obj.users = assigneeList;

			obj.tagged_with = _.chain(leadsList).union(assigneeList).union(task.tagged_with).uniq().value();
			obj.notify = obj.tagged_with;

			sb.data.db.obj.update('groups', obj, function (newTaskObj) {

				if (after !== undefined) {

					after(newTaskObj);

				}

			}, 2);

		}

		if (!_.isEmpty(task.end_date)) {

			formArgs.end_date.value = moment(task.end_date);

		}

		if (qaDom === undefined || qaDom === false) {

			dom = modalDom;

		} else {

			dom = qaDom;

		}

		dom.empty();

		dom.makeNode('wrapper', 'div', {
			css: 'animated fadeIn'
		});

		dom.wrapper.makeNode('head', 'container', {
			uiGrid: true
		});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {
			spaces: 1
		});
		dom.wrapper.makeNode('body', 'container', {
			uiGrid: false
		});

		dom.wrapper.head.makeNode('col1', 'column', {
			w: 10
		});
		dom.wrapper.head.makeNode('col2', 'column', {
			w: 6
		});

		dom.wrapper.head.col1.makeNode('title', 'div', {
			text: '<h2><i class="fa fa-pencil"></i> Edit Task</h2>'
		});

		dom.wrapper.head.col2.makeNode('btnGrp', 'div', {});

		dom.wrapper.head.col2.btnGrp.makeNode('back_btn', 'div', {
			text: '<i class="fa fa-arrow-right"></i>',
			css: 'ui tiny inverted red button right floated',
			tooltip: {
				text: 'Back',
				postion: 'left center'
			}
		}).notify('click', {
			type: 'tasks-run',
			data: {
				run: function (data) {

					build_taskDetails(dom, task, qaDom);

				}
			}
		}, sb.moduleId);

		dom.wrapper.head.col2.btnGrp.makeNode('save_btn', 'div', {
			text: 'loading',
			css: 'ui tiny green button right floated loading'
		});

		/*
dom.wrapper.body.empty();
		
		display_loader(dom.wrapper.body, 'Fetching task types ...');
		
		dom.wrapper.body.patch();
*/

		sb.data.db.obj.getAll('task_types', function (types) {

			types = _.sortBy(types, function (o) {
				return o.name;
			});

			_.each(types, function (typeObj) {

				formArgs.type.options.push({
					name: typeObj.name,
					value: typeObj.id
				});

				_.each(typeObj.states, function (state) {

					formArgs.state.options.push({
						name: state.name,
						value: state.id
					});

				});

			});

			dom.wrapper.body.empty();

			display_loader(dom.wrapper.body, 'Fetching users list ...');

			dom.wrapper.body.patch();

			sb.data.db.obj.getWhere('users', {
				enabled: 1
			}, function (users) {

				var userOptions = [];
				var managerIds = [];
				var assigneeIds = [];

				if (users) {

					_.each(users, function (user) {

						userOptions.push({
							name: 'user',
							label: user.fname + ' ' + user.lname,
							value: user.id
						});

						task.tagged_with = _.without(task.tagged_with, user.id);

					}, this);

					formArgs.managers.options = userOptions;

					if (!_.isEmpty(task.managers)) {

						_.each(task.managers, function (manager) {

							managerIds.push(manager.id);

						});

					}

					formArgs.managers.value = managerIds;

					dom.wrapper.body.empty();

					dom.wrapper.body.makeNode('formWrap', 'div', {
						css: 'ui basic segment'
					});

					dom.wrapper.body.formWrap.makeNode('form', 'form', formArgs);

					dom.wrapper.head.col2.btnGrp.makeNode('save_btn', 'div', {
						text: '<i class="fa fa-floppy-o"></i>',
						css: 'ui tiny green button right floated',
						tooltip: {
							text: 'Save',
							position: 'left center'
						}
					}).notify('click', {
						type: 'tasks-run',
						data: {
							run: function (data) {

								process_editForm(dom.wrapper.body.formWrap.form, task, function () {

									dom.wrapper.head.col2.btnGrp.makeNode('save_btn', 'div', {
										text: 'loading',
										css: 'ui tiny green button right floated loading'
									});

									dom.wrapper.body.empty();

									display_loader(dom.wrapper.body, 'Updating task ...');

									dom.wrapper.body.patch();
									dom.wrapper.head.patch();

								}, function (updated) {

									if (task.page_params === window.location.href.substr(window.location.href.indexOf("#"))) {

										display_taskList.load();

									}

									if (comps.hasOwnProperty('tasksList') && sb.data.url.get().page === 'initTasks') {

										update_tableData();

									}

									build_taskDetails(dom, updated, qaDom);

									if (dom.hasOwnProperty('show')) {

										display_taskList(cachedSetup);

									}

								});

							}
						}
					}, sb.moduleId);

					dom.wrapper.patch();

				}

			}, 1);

		}, 1);

		dom.patch();

	}

	function get_follower_tasks(obj, callback) {

		if (obj.is_recurring) {

			sb.data.db.obj.getWhere(
				'groups', {
					initial: obj.id,
					end_date: {
						type: 'between',
						start: moment().startOf('week').format('X'),
						end: moment().endOf('week').format('X')
					}
				},
				function (recurring) {

					obj.followers = recurring;

					// 					console.log('with followers', obj);

					callback(obj);

				}
			);

		} else {

			callback(obj);

		}

	}

	function update_tableData() {

		comps.tasksList.notify({
			type: 'update-table-data',
			data: {
				data: function (paged, callback) {

					sb.data.db.obj.getAll('tasks', function (ret) {

						callback(ret);

					}, {
						id: true,
						status: true,
						title: true,
						author: true,
						details: true,
						assignee: {
							fname: true,
							lname: true
						},
						due_date: true,
						task_type: true,
						date_created: true,
						related_object: true,
						page_params: true
					}, paged);

				}
			}
		});

	}

	function update_tasksBadge() {

		sb.data.db.obj.getWhere('tasks', {
			assignee: {
				type: 'contains',
				value: sb.data.cookie.userId
			},
			childObjs: {
				status: true
			}
		}, function (tasksList) {

			var list = _.filter(tasksList, function (obj) {
				return obj.status !== 2;
			});
			var assignedList = _.filter(tasksList, function (obj) {
				return obj.status === 0;
			});
			var inProgressList = _.filter(tasksList, function (obj) {
				return obj.status === 1;
			});
			var labelColor = '';

			if (!_.isEmpty(list)) {
				labelColor = 'red';
			} else {
				labelColor = '';
			}

			sb.notify({
				type: 'app-update-quickAction',
				data: {
					itemId: 'tasks',
					viewObj: {
						id: 'qa-tasks',
						icon: '<span data-tooltip="' + assignedList.length + ' Assigned - ' + inProgressList.length + ' Completed"><i class="fa fa-tasks"></i> <span class="ui tiny ' + labelColor + ' label">' + list.length + '</span></span>',
						css: ''
					}
				}
			});

		});

	}

	function update_task(task, after) {

		sb.data.db.obj.update('groups', task, function (updatedTask) {

			if (after !== undefined) {

				after(updatedTask);

			}

		}, 1);

	}

	function parse_collections(data, callback, query, subview, range, types) {

		function get_followers(recurring, range, onComplete) {

			sb.data.db.obj.getWhere(
				'groups', {
					initial: {
						type: 'or',
						values: _.pluck(recurring, 'id')
					},
					end_date: range
				},
				function (followers) {

					//					console.log('followers = ', followers);

					onComplete(followers);

				}
			);

		}

		/*
				function merge_followers (recurring, followers) {
					
					_.each(recurring, function(recurring){
						
						recurring.followers = _.findWhere(followers, {
							initial: recurring.id
						});
						
					});
					
					return recurring;
					
				}
		*/

		if (_.isEmpty(data)) {
			data = {
				data: []
			};
		}

		switch (subview.name) {

			// in board, just show today's task for recurring tasks
			case 'board':

				var recurring = _.where(data.data, {
					is_recurring: 1
				});
				data.data = _.filter(data.data, function (task) {
					return task.is_recurring != 1;
				});

				get_followers(recurring, range, function (followers) {

					// if follower exists for today, merge it in,
					// 		if not, create 'ghost' follower task in
					// 		today's place
					var today = moment().startOf('day');
					_.each(recurring, function (recurringTask) {

						// weekly schedules should show the next upcoming,
						// 		which may not be today
						var currentDay = today.clone();
						var todayDayOfWeek = today.day();

						if (recurringTask.cycle === 'weekly') {

							// if today is not the schedule..
							if (!_.contains(recurringTask.schedule_options, currentDay.day())) {

								// ..find the next closest day..

								if (_.max(recurringTask.schedule_options) > currentDay.day()) {

									var tmp = _.filter(
										recurringTask.schedule_options,
										function (dayOfWeek) {

											//											console.log('test', dayOfWeek, currentDay.day())

											return dayOfWeek > currentDay.day();

										}
									);

									currentDay = currentDay.startOf('week');
									currentDay.add(
										tmp[0],
										'day'
									);

								} else if (!_.isEmpty(recurringTask.schedule_options)) {

									currentDay = currentDay.startOf('week');
									currentDay = currentDay.add(1, 'week');
									currentDay.add(
										recurringTask.schedule_options[0],
										'day'
									);

								} else {

									return;

								}

							}

						}

						var todaysTask = _.find(followers, function (follower) {

							return (
								(follower.initial === recurringTask.id) &&
								(
									moment(follower.end_date, 'YYYY-MM-DD HH:mm:ss')
									.isSame(currentDay, 'day')
								)
							);

						});

						if (!todaysTask) {

							// create 'ghost' task
							todaysTask = _.clone(recurringTask);
							todaysTask.initial = recurringTask.id;
							todaysTask.is_recurring = 0;
							todaysTask.start_date = currentDay.format('YYYY-MM-DD HH:mm:ss');
							todaysTask.end_date = currentDay.format('YYYY-MM-DD HH:mm:ss');
							todaysTask.is_ghost = true;

						}

						if (typeof todaysTask.type === 'number') {

							todaysTask.type = _.findWhere(types, {
								id: todaysTask.type
							});

						}

						data.data.push(todaysTask);

					});

					// 					console.log('followers = ', followers);

					// 					console.log('data = ', data);

					callback(data);

				});
				break;

				// in calendar, unroll recurring to all upcoming tasks
			case 'calendar':

				delete query.end_date;
				query.is_recurring = 1;

				sb.data.db.obj.getWhere('groups', query, function (recurring) {

					get_followers(recurring.data, range, function (followers) {

						// merging in follower tasks
						_.each(followers, function (follower) {

							data.data.push(follower);

						});

						data.data = _.filter(data.data, function (task) {
							return task.is_recurring !== 1;
						});


						// unfolding 'ghost' tasks
						_.each(recurring.data, function (recurringTask) {

							var current = moment.unix(range.start);
							var end = moment(recurringTask.repeat_end_date, 'YYYY-MM-DD HH:mm:ss');
							var shouldContinue = true;

							if (current.isBefore(moment(recurringTask.end_date, 'YYYY-MM-DD HH:mm:ss'))) {
								current = moment(recurringTask.end_date, 'YYYY-MM-DD HH:mm:ss');
							}
							if (recurringTask.repeat_forever || end.isAfter(moment.unix(range.end))) {
								end = moment.unix(range.end);
							}

							while (shouldContinue) {

								var ghostTask;

								// increment date
								switch (recurringTask.cycle) {

									case 'daily':
										current = current.startOf('day');
										// Do not greate 'ghost' tasks for 
										// tasks that already exist.
										if (_.find(data.data, function (task) {

												if (
													task.initial === recurringTask.id &&
													moment(task.end_date, 'YYYY-MM-DD HH:mm:ss').isSame(current, 'day')
												) {

													return true;
												}

												return false;

											})) {
											current.add(1, 'day');
											break;
										} else {

											// create 'ghost' task
											ghostTask = _.clone(recurringTask);
											ghostTask.initial = recurringTask.id;
											ghostTask.is_recurring = 0;
											ghostTask.start_date = current.format('YYYY-MM-DD HH:mm:ss');
											ghostTask.end_date = current.format('YYYY-MM-DD HH:mm:ss');
											ghostTask.is_ghost = true;

											data.data.push(ghostTask);

											current.add(1, 'day');

										}
										break;

									case 'weekly':
										current = current.startOf('week');
										_.each([0, 1, 2, 3, 4, 5, 6], function (val) {

											if (_.contains(recurringTask.schedule_options, current.day())) {

												// Do not greate 'ghost' tasks for 
												// tasks that already exist.
												if (_.find(data.data, function (task) {

														if (
															task.initial === recurringTask.id &&
															moment(task.end_date, 'YYYY-MM-DD HH:mm:ss').isSame(current, 'day')
														) {
															return true;
														}

														return false;

													})) {
													current.add(1, 'day');
													return;
												}

												// create 'ghost' task
												var ghostTask = _.clone(recurringTask);
												ghostTask.initial = recurringTask.id;
												ghostTask.is_recurring = 0;
												ghostTask.start_date = current.format('YYYY-MM-DD HH:mm:ss');
												ghostTask.end_date = current.format('YYYY-MM-DD HH:mm:ss');
												ghostTask.is_ghost = true;

												data.data.push(ghostTask);

											}

											current.add(1, 'day');

										});
										break;

									default:
										current.add(1, 'year');
										break;

								}

								if (current.isSame(end, 'day') || current.isAfter(end)) {
									shouldContinue = false;
								}

							}

						});

						//!TODO: temp fix--
						var rangeStart = moment.unix(range.start);
						var rangeEnd = moment.unix(range.end);
						data.data = _.filter(data.data, function (item) {

							return moment(item.end_date, 'YYYY-MM-DD HH:mm:ss').isBetween(rangeStart, rangeEnd);

						});

						// add color
						_.each(data.data, function (evt) {

							if (typeof evt.type === 'number') {
								evt.type = _.findWhere(types, {
									id: evt.type
								});
							}

							var currentState = _.findWhere(evt.type.states, {
								uid: evt.state
							});
							if (currentState && !_.isEmpty(currentState.color)) {
								evt.color = currentState.color;
							} else {
								evt.color = 'grey';
							}

						});

						callback(data);

					});

				});
				break;

			case 'list':
			case 'table':

				var toGet = _.chain(data.data)
					.pluck('id')
					.flatten()
					.uniq()
					.value();

				query.initial = {
					type: 'or',
					values: toGet
				};

				query.end_date = {
					type: 'between',
					start: moment().startOf('week').format('X'),
					end: moment().endOf('week').format('X')
				};

				delete query.paged;
				query.childObjs = {
					end_date: true,
					group_type: true,
					initial: true,
					name: true,
					state: true,
					status: true,
					parent: {
						name: true,
						fname: true,
						lname: true
					}
				};

				// get follower tasks and merge in
				sb.data.db.obj.getWhere('groups', query, function (followerTasks) {

					_.each(data.data, function (task) {

						task.followers = _.where(followerTasks, {
							initial: task.id
						});

					});

					callback(data);

				});

				break;

			default:
				callback(data);
				break;

		}

	}

	// Views

	var view = {
		segment: {
			type: 'div',
			css: 'ui stackable grid container',
			content: {
				css: 'ui row',
				left: {
					type: 'column',
					w: 10,
					css: '',
					content: {
						name: {
							type: 'title',
							fieldName: 'name',
							edit: true,
							tag: 'h1',
							parent: 'parent',
							uid: true
						},
						tags: {
							type: 'view',
							view: function (ui, obj, options) {

								ui.makeNode('c', 'div', {
									css: 'field'
								});
								ui.patch();

								var tagsComp = sb.createComponent('tags');
								tagsComp.notify({
									type: 'object-tag-view',
									data: {
										domObj: ui.c,
										objectType: obj.object_bp_type,
										objectId: obj.id
									}
								});

							}
						},
						state: {
							type: 'state',
							fieldName: 'state',
							edit: true
						},
						time_tracking: {
							type: 'timeTracking',
							label: 'Time Tracking'
						},
						end_date: {
							type: 'date',
							fieldName: 'end_date',
							label: 'Due',
							edit: true,
							dateType: 'datetime'
						},
						descriptionTitle: {
							type: 'div',
							tag: 'h5'
								// 								, text: 'DETAILS:'
								,
							css: 'ui mini grey sub header'
							// 								, style: 'font-weight:1 !important;line-height:2em;color:rgb(15,15,15) !important;'
						},
						descriptionCont: {
							type: 'div'
								// 								, style: 'border:1px dotted lightgrey;'
								,
							content: {
								description: {
									type: 'detail',
									fieldName: 'description',
									edit: true
									// 										, style:'border:1px dotted lightgrey;'
								}
							}
						}
					}
				},
				right: {
					type: 'column',
					w: 6,
					css: '',
					content: {
						type: {
							type: 'type',
							fieldName: 'type',
							label: 'Type',
							edit: true,
							objectType: 'task_types'
						},
						priority: {
							type: 'priority',
							fieldName: 'priority',
							label: 'Priority',
							edit: true
						},
						created_by: {
							type: 'users',
							fieldName: 'created_by'
								// 														, edit: false
								// 														, title: 'Reporter'
								,
							label: 'Reporter'
						},
						managers: {
							type: 'users',
							multi: true,
							edit: true,
							fieldName: 'managers',
							label: 'Assignee(s)'
								// !In the future, this will live on the back-end.
								// !TODO: 	for now, this callback should run in the fields
								// 			component, not in the user-field comp
								,
							parseUpdates: function (updates) {

								var leadsList = updates.managers;
								
								updates.users = leadsList;
								
								updates.tagged_with = _.chain(leadsList)
									.union(updates.tagged_with)
									.uniq()
									.value();

								updates.notify = updates.tagged_with;

								return updates;

							}
						},
						date_created: {
							type: 'date',
							fieldName: 'date_created',
							edit: false,
							label: 'Created'
						},
						last_updated: {
							type: 'date',
							fieldName: 'last_updated',
							edit: false,
							label: 'Updated'
						},
						attachments: {
							type: 'attachments',
							fieldName: 'attachments',
							edit: true,
							label: 'Attachments',
							labelStyle: 'stacked'
						}
					}
				},
				bottom: {
					type: 'column',
					w: 8,
					css: 'centered',
					content: {
						comments: {
							type: 'view',
							view: function (ui, obj, options) {

								sb.notify({
									type: 'show-note-list-box',
									data: {
										domObj: ui,
										objectIds: [obj.id],
										objectId: obj.id,
										collapse: 'open'
									}
								});

							}
						}
					}
				}
			}
		}
	};

	function build_collection(dom, state, draw, mainDom, options) {

		var backlog = true;
		var menu = true;
		var tagged_with = [];
		var selectedView = defaultCollectionsView;
		var where = {
				group_type: 'Task',
				initial: {
					type: 'not_set'
				},
				status: {
					type: 'not_equal',
					value: 'done'
				},
				childObjs: {
					comment_count: true,
					managers: {
						fname: true,
						lname: true,
						profile_image: true
					},
					description: true,
					start_date: true,
					end_date: true,
					time_logged: true,
					time_estimate: true,
					type: {
						name: true,
						states: true
					},
					state: true,
					name: true,
					group_type: true,
					is_recurring: true,
					cycle: true,
					schedule_options: 'id',
					repeat_forever: true,
					repeat_end_date: true,
					object_uid: true,
					parent: {
						name: true,
						group_type: true,
						fname: true,
						lname: true
					},
					priority: true,
					is_template: true
				}
			};
		var urlParams = checkURL();

		function checkURL() {
			
			var url = window.location.href.split('!');
			var params = {};
			
			_.each(url, function(urlpart, i) {

				if(i !== 0) {
					
					urlpart = urlpart.split(':');
					
					_.each(urlpart, function(v, k) {
						
						params[urlpart[0]] = urlpart[1];
						
					});
					
				}
				
			});

			return params;
			
		}

		if (options.tagged_with) {
			
			where.tagged_with = options.tagged_with;
			
		} 

		switch (options.collection) {

			case 'myStuff':

				where.managers = {
					type: 'contains',
					value: +sb.data.cookie.get('uid')
				};

				break;

		}

		if(state.where !== undefined) {
				
			_.each(state.where, function(propVal, prop) {
				
				where[prop] = propVal;
				
			});

		} 

		if (options.selectedView) {
			selectedView = options.selectedView;
		}

		if (options.userId) {

			where.managers = {
				type: 'contains',
				value: options.userId
			};

		}
		
		if (options.parent) {
			where.parent = options.parent;
		}

		if (options.mini) {
			menu = false;
			backlog = false;
		}
		
		if (
			state.hasOwnProperty('entity')
			&& typeof state.entity === 'number'
		) {

			where.tagged_with = [state.entity];
			
		}

		checkTaskStates(function (taskState) {

			var collSetup = {
					actions: {
						create: function (ui, newObj, onComplete) {
							
							if (
								options.collection !== undefined
							) {
								
								if (
									options.collection === 'project'
									|| options.collection === 'team'
								) {
									
									newObj.parent = state[options.collection];
									
								} else if (
									options.collection === 'object'
								) {

									newObj.parent = state.pageObject;	
									
								}	
								
							} else {
								
								if (
									state.hasOwnProperty('entity')
									&& typeof state.entity === 'number'
								) {

									newObj.parent = {
										id: state.entity	
									};

									if (
										state.hasOwnProperty('tool')
									) {
										
										newObj.tagged_with = _.filter(newObj.tagged_with, function(id) {
											return id !== state.tool.id;
										});
										
									}
									
								} else {
									
									newObj.parent = state.pageObject;	
									
								}
								
							}

							create_newTask(ui, onComplete, 0, {}, newObj);
	
						},
						view: true
					},
					domObj: dom,
					fields: {
						name: {
							title: 'Name',
							type: 'title',
							counter: true,
							uid: true,
							isSearchable: true
						},
						parent: {
							title: 'Parent',
							type: 'parent',
							shouldShow: function (obj) {
	
								if (obj.parent.id !== appConfig.headquarters.id) {
	
									return true;
	
								} else {
	
									return false;
	
								}
	
							}
						},
						description: {
							title: 'Details',
							type: 'detail'
						},
						managers: {
							title: 'Assignee(s)',
							type: 'users',
							parseUpdates: function (updates) {
	
								var leadsList = updates.managers;
								updates.users = leadsList;
								updates.tagged_with = _.chain(leadsList)
									.union(updates.tagged_with)
									.uniq()
									.value();
	
								updates.notify = updates.tagged_with;
								return updates;
	
							}
						},
						state: {
							title: 'Status',
							type: 'state',
							is_recurring: true
						},
						end_date: {
							title: 'Due on',
							type: 'date',
							rangeOver: true
						},
						type: {
							title: 'Type',
							type: 'type'
						},
						priority: {
							title: 'Priority',
							type: 'priority'
						}
					},
					singleView: {
						action: function (task) {
	
							setupTask(task, function (task) {
	
								sb.notify({
									type: 'app-navigate-to',
									data: {
										type: 'object',
										object: task
									}
								});
	
							}, 1);
	
						},
						useCache: true,
						view: function (dom, task, onComplete, refresh, options, viewOptions) {
	
							var compact = false;
							if (viewOptions && viewOptions.compact) {
								compact = true;
							}
	
							setupTask(task, function (task) {
	
								sb.notify({
									type: 'view-page',
									data: {
										ui: dom,
										onDraw: onComplete,
										page: view,
										onDraw: draw,
										state: {
											pageObject: task
										},
										options: {
											compact: compact
										}
									}
								});
	
							}, 1);
	
						}
					},
					filterBy: {
						status: {
							title: 			'Status'
							, defaultText: 	'All'
							, defaultValue: 	'open'
							, getOptions: 	function (callback) {
								
								callback([
									{
										name: 	'Open'
										, id: 	'open'
									}
									, {
										name: 	'Done'
										, id: 	'done'
									}
								]);
								
							}
							, parseSelection: function (selection, options) {
								
								switch (selection) {
									
									case 'done':
										options.where.status = 'done';
										break;
									
									case 'open':
										options.where.status = {
											type: 		'not_equal'
											, value: 	'done'
										};
										break;
										
									default:
										delete options.where.status;
										break;
									
								}
								
							}
						}
					},
					fullView: {
						type: 'object-view',
						id: 'tasks-obj'
					},
					groupings: {
						by_range: true,
						state: 'Status',
						managers: 'Assignee(s)',
						parent: 'About'
					},
					menu: menu,
					objectType: 'groups',
					parseData: parse_collections,
					selectedView: selectedView,
					selectedMobileView: 'list',
					state: state,
					tool: options.tool,
					subviews: {
						table: {
							range: {
								defaultTo: 'all_time'
							}
						},
						board: {
							range: {
								defaultTo: 'next_3_months',
								not: ['all_time']
							},
							groupBy: {
								defaultTo: 'state'
							}
						},
						list: {
							range: {
								defaultTo: 'next_3_months',
								not: ['all_time']
							},
							groupBy: {
								defaultTo: 'by_range'
							},
							backlog: backlog
						}
					},
					where: where
				};
			var config = {};

			if (options.hasOwnProperty('menu')) {
				collSetup.menu = options.menu;
			}

			if(options.collection === 'all-tasks') {
				
				collSetup.layer = 'hq';
				
			}
			
			if(options.hasOwnProperty('collectionsSetup')) {
				
				_.each(options.collectionsSetup, function(propVal, propKey) {
					
					collSetup[propKey] = propVal;
					
				});
				
			}
			
			if(urlParams.type) {
				
				config.type = urlParams.type;
				
			}
			
			if(urlParams.state) {
				
				config.state = urlParams.state;
				
			}

			if(!_.isEmpty(config)) {
				collSetup.config = config;
			}

			sb.notify({
				type: 'show-collection',
				data: collSetup
			});

		});

	}

	function setupTask(task, onComplete, childObjs) {

		// check if task is real
		if (task.is_ghost) {

			// check if task has been created
			sb.data.db.obj.getWhere(
				'groups', {
					group_type: 'Task',
					initial: task.id,
					end_date: {
						type: 'between',
						start: moment(task.end_date, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('X'),
						end: moment(task.end_date, 'YYYY-MM-DD HH:mm:ss').endOf('day').format('X')
					},
					childObjs: childObjs
				},
				function (data) {

					// if created, go to task page
					if (!_.isEmpty(data)) {

						onComplete(data[0]);

					} else { // create and go to task page

						var newTask = _.clone(task);

						delete newTask.id;
						delete newTask.cycle;
						newTask.is_recurring = false;
						delete newTask.schedule_options;
						delete newTask.repeat_forever;
						delete newTask.repeat_end_date;

						newTask.initial = task.id;

						sb.data.db.obj.create(
							'groups',
							newTask,
							function (created) {

								onComplete(created);

							},
							childObjs
						);

					}


				}
			);

		} else {

			// go to task page
			sb.data.db.obj.getById(
				'groups',
				task.id,
				function (task) {

					onComplete(task);

				},
				childObjs
			);


		}

	}

	return {

		init: function () {

			if (appConfig.instance !== 'erationalmarketing') { 

				if ($(window).width() <= 768) {
					
					defaultCollectionsView = 'cards';
					onMobile = true;
					
				}

				sb.listen({
					'show-task-list': this.displayList,
					'tasks-run': this.run,
					'kanban-task-dropped': this.taskDropped,
					'tasks-build-collection': this.buildCollection
				});
				
				var toolRegistrationsSetup = [
						{
							id: 'taskTool',
							name: 'Tasks',
							tip: 'Organize work.',
							icon: {
								type: 'tasks',
								color: 'green'
							},
							/* default: true, */
							settings: false,
							layers: ['hq', 'team', 'project', 'myStuff', 'object'],
							mainViews: [{
								dom: function (dom, state, draw, mainDom) {

									var collectionType = 'all-tasks';

									if (
										state.hasOwnProperty('layer')
									) {
										
										if (
											state.layer === 'object'
										) {
											
											collectionType = 'object';
											
										} else if (
											state.layer === 'team'
										) {
											
											collectionType = 'team';
											
										} else if (
											state.layer === 'project'
										) {
											
											collectionType = 'project';
											
										} else if (
											state.layer === 'myStuff'
										) {
											
											collectionType = 'myStuff';
											
										}
										
									}

									build_collection(
										dom, state, draw, mainDom, {
											collection: collectionType
										}
									);
			
								}
							}],
							boxViews: [
								{
									id: 'taskOverview',
									width: 'four',
									title: 'Task Overview',
									settingsView: function (dom, settings, done) {
			
										dom.makeNode('close', 'div', {
												css: 'ui mini button center aligned',
												text: 'Save'
											})
											.notify('click', {
												type: 'dashboard-tool-run',
												data: {
													run: function () {
			
														dom.close.loading();
			
														done(settings);
			
													}
												}
											});
			
										dom.patch();
			
									},
									dom: viewTasksOverview
								},
								// Tasks grouped by state -- pie chart
								{
									id: 'tasksGroupedByState',
									width: 'eight',
									title: 'Tasks by Status Pie Chart',
									dom: viewTasksPieChart,
									settingsView: viewTasksPieChartSettings
								},
								// Recent Tasks
								{
									id: 'recentTasks',
									width: 'sixteen',
									title: 'Recent Tasks',
									collections: {
										fields: {
											created_by: {
												title: 'Created By',
												type: 'title',
												view: function(ui, obj) {
													
													ui.makeNode('feed', 'div', {
														css: 'ui small feed'
													});
													
													ui.feed.makeNode('ev-' + obj.id, 'div', {
														css: 'event'
													});
			
													if(obj.created_by.profile_image.loc != "//" && obj.created_by.profile_image !== null && obj.created_by.profile_image !== undefined) {
														
														ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {
															css: 'label'
														}).makeNode('ava-'+obj.id, 'image', {
															url: sb.data.files.getURL(obj.created_by.profile_image)
														});
														
													} else {
														
														ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {css: 'label'}).makeNode('ava-'+obj.id, 'div', {tag:'i', css: 'user circle icon'});
															
													}
													
												}
											},
											name: {
												title: 'Name',
												type: 'title',
												isSearchable: true												
											},
											parent: {
												title: 'Parent',
												type: 'parent',
												shouldShow: function (obj) {
						
													return true;
						
												}
											},
											end_date: {
												title: 'Due ',
												type: 'title',
												view: function(ui, obj) {
													
													var end_date = '';
													
													if(obj.end_date !== '') {
														
														end_date = 'Due ' + moment(obj.end_date).local().fromNow();
														
													} 
													
													ui.makeNode('date', 'div', {
														css: 'date', 
														text: end_date
													});
													
												}
											},
											state: {
												title: 'Status',
												type: 'state'
											},
											type: {
												title: 'Type',
												type: 'type'
											}
										},
										groupings: {
											by_range: true,
											state: 'Status',
											managers: 'Assignee(s)',
											parent: 'About',
											type: 'Type'
										},
										actions: {},
										selectedView: 'list',
										objectType: 'groups',
										emptyMessage: 'No recent tasks',
										subviews: {
											list: {
												hideTimeRangeFilter: true,
												groupBy: {
													defaultTo: 'type'
												}
											}
										},
										where: {
											date_created: {
												type: 'between',
												start: moment().startOf('month').format('X'),
												end: moment().endOf('month').format('X')
											},
											group_type: 'Task',
											status: {
												type: 'not_equal',
												value: 'done'
											},
											childObjs: {
												group_type: true,
												name: true,
												parent: {
													name: true,
													fname: true,
													lname: true,
													group_type: true
												},
												created_by: {
													profile_image: true
												},
												end_date: true,
												state: true,
												type: true
											}
										}
									}
								},
								// Upcoming Tasks
								{
									id: 'upcomingTasks',
									/* default: true, */
									width: 'sixteen',
									title: 'Upcoming Tasks',
									collections: {
										fields: {
											created_by: {
												title: 'Created By',
												type: 'title',
												view: function(ui, obj) {
													
													ui.makeNode('feed', 'div', {
														css: 'ui small feed'
													});
													
													ui.feed.makeNode('ev-' + obj.id, 'div', {
														css: 'event'
													});
			
													if(obj.created_by.profile_image.loc != "//" && obj.created_by.profile_image !== null && obj.created_by.profile_image !== undefined) {
														
														ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {
															css: 'label'
														}).makeNode('ava-'+obj.id, 'image', {
															url: sb.data.files.getURL(obj.created_by.profile_image)
														});
														
													} else {
														
														ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {css: 'label'}).makeNode('ava-'+obj.id, 'div', {tag:'i', css: 'user circle icon'});
															
													}
													
												}
											},
											name: {
												title: 'Name',
												type: 'title',
												isSearchable: true												
											},
											end_date: {
												title: 'Due ',
												type: 'title',
												view: function(ui, obj) {
													
													var end_date = '';
													
													if(obj.end_date !== '') {
														
														end_date = 'Due ' + moment(obj.end_date).local().fromNow();
														
													} 
													
													ui.makeNode('date', 'div', {
														css: 'date', 
														text: end_date
													});
													
												}
											},
											state: {
												title: 'Status',
												type: 'state'
											},
											type: {
												title: 'Type',
												type: 'type'
											}
										},
										groupings: {
											by_range: true,
											state: 'Status',
											managers: 'Assignee(s)',
											parent: 'About',
											type: 'Type'
										},
										actions: {},
										selectedView: 'list',
										objectType: 'groups',
										emptyMessage: 'No upcoming tasks',
										subviews: {
											list: {
												hideTimeRangeFilter: true,
												groupBy: {
													defaultTo: 'type'
												}
											}
										},
										where: {
											group_type: 'Task',
											status: {
												type: 'not_equal',
												value: 'done'
											},
											end_date: {
												type: 'between',
												start: moment().unix(),
												end: moment().add(7, 'days').unix()
											},
											childObjs: {
												group_type: true,
												name: true,
												created_by: {
													profile_image: true
												},
												end_date: true,
												state: true,
												type: true
											}
										}
									}
								},
								// Tasks by type -- table
								{
									id: 'tasksByType',
									width: 'eight',
									title: 'Tasks by Type',
									collections: {
										fields: {
											name: {
												title: 'Name',
												type: 'title',
												isSearchable: true,
												link: function(o) {
													
													return sb.data.url.createPageURL(
														'hqTool', 
														{
															tool: 'taskTool',
															params: {
																type: o.id
															}
														}
													);
													
												}
											},
											tasks: {
												title: '# of tasks',
												view: function(ui, obj) {
			
													ui.makeNode('total', 'div', {
														text: obj.tasksTotal || '0',
														css: 'text-center'
													});
													
													ui.patch();
													
												}	
											},
											tags: {
												view: function() {
													return false;
												}
											},
											select: {
												view: function() {
													return false;
												}
											}
										},
										showPaging: true,
										selectedView: 'table',
										pageLength: 10,
										objectType: 'task_types',
										emptyMessage: 'No task types available',
										subviews: {
											table: {
												style: 'padding: 0 !important',
												hideTimeRangeFilter: true
											}
										},
										parseData: function(data, callback, query, subview, range, types) {
			
											var typeIds = _.pluck(data.data, 'id');
			
											sb.data.db.obj.getGroupSum('groups', 'id', {
												group_type: 'Task',
												groupOn: 'type',
												type: {
													type: 'or',
													values: typeIds
												},
												is_template: 0,
												dateRange: {
													start: moment("20111031", "YYYYMMDD").format('YYYY-MM-DD HH:mm:ss.SS'),
													end: moment().add(1, 'year').format('YYYY-MM-DD HH:mm:ss.SS')
												}
											}, function(metrics) {
			
												_.each(metrics, function(metric) {
													
													var type = _.findWhere(data.data, {id: parseInt(metric['0'])});
													
													type.tasksTotal = metric.grouped_total;
													
												});
												
												data.data = _.sortBy(data.data, 'name');
												
												callback(data);
												
											});
												
										},
										where: {
											childObjs: {
												name: true,
												states: true
											}
										}
									}
								},
								// Tasks by state -- table
								{
									id: 'tasksByState',
									width: 'eight',
									title: 'Tasks by Status Table',
									dom: viewTasksByStateTable,
									settingsView: viewTasksPieChartSettings
								},
								// Tasks calendar
								{
									id: 'tasksCalendar'
									, width: 'sixteen'
									, title: 'Tasks Calendar'
									, dom: function(ui, state, draw) {
										
										if(appConfig.instance === 'rickyvoltz' || 
											appConfig.instance === 'voltzsoftware') {
												
												if(comps.hasOwnProperty('availability')) {
													comps.availability.destroy();
												}
												
												comps.availability = sb.createComponent('calendar');
												
												comps.availability.notify({
													type: 'show-calendar',
													data: {
														domObj: ui,
														events: function(callback, range) {
															
															callback([]);
															
														},
														views: {
															month: {
																show: true
															},
															week: {
																body: false
															},
															threeDay: {
																show: false
															},
															day: {
																show: false
															},
															list: {
																show: false
															}
														},
														viewType: 'week',
														scheduling: true
													}
												});
												
											} else {
												
												ui.makeNode('note', 'div', {
													text: 'Coming soon...'
													, css: 'text-center text-bold'
												});	
												
											}
										
										draw(ui);
										
									}
								}
							]
						}
						// obj view
						, {
							id: 'task-obj',
							type: 'object-view',
							title: 'Task',
							icon: {
								type: 'check',
								color: 'green'
							},
							/*
															dom:function(dom, state, draw){
										console.log('test',view);						
																view.segment.content.left.w = 10;
																view.segment.content.right.w = 6;
																view.segment.content.bottom.w = 8;
																
																sb.data.db.obj.getById('groups', state.id, function(obj){
																	
																	build_taskDetails(dom, obj, undefined);
																	
																}, 2);
																
															},
							*/
							view: view,
							select: {
								name: true,
								state: true,
								description: true,
								end_date: true,
								start_date: true,
								time_logged: true,
								time_estimate: true,
								date_created: true,
								last_updated: true,
								selectionObj: true,
								managers: {
									fname: true,
									lname: true,
									profile_image: true
								},
								created_by: {
									fname: true,
									lname: true,
									profile_image: true
								},
								type: {
									name: true,
									states: true
								},
								group_type: true,
								object_uid: true,
								parent: {
									name: true,
									group_type: true,
									fname: true,
									lname: true
								},
								priority: true,
								tagged_with: true
							}
						}
						// Workload Report
						, {
							id: 'taskWorkloadReport',
							type: 'custom',
							title: 'Tasks',
							icon: {
								type: 'file alternate',
								color: 'olive'
							},
							dom: function (dom, state, draw, mainDom) {
			
								build_collection(
									dom, state, draw, mainDom, {
										collection: 'all-tasks',
										selectedView: 'chart'
									}
								);
			
							}
						}
						// Entity Tool
						, {
							id: 'tasks',
							type: 'tool',
							name: 'Tasks',
							title: 'Tasks',
							tip: 'Organize work.',
							icon: {
								type: 'tasks',
								color: 'green'
							},
							default: true,
							settings: false,
							availableToEntities: true,
							hiddenFromProjects: true,
							mainViews: [{
								dom: function (dom, state, draw, mainDom) {

									build_collection(
										dom, state, draw, mainDom, {
											parent: state.entity
											/*
tagged_with: {
												type: 'contains'
												, value: [state.entity]
											},
											tool: state.tool.id,
											menu: false
*/
										}
									);
			
								}
							}],
							boxViews: []
						}
					];
					
				if(onMobile) {
					
					toolRegistrationsSetup.push({
						id:'mytasks',
						type:'nav-item',
						name: 'Tasks',
						title: 'Tasks',
						tip:'Organize work.',
						icon: {
							type: 'tasks',
							color: 'green'
						},
						default:true,
						settings:false,
						mainViews:[
							{
								dom:function(dom, state, draw, mainDom){
									
									build_collection(
										dom, state, draw, mainDom, {
											collection: 'my-tasks'
										}
									);
								
								}
							}
						]
					});
					
				} else {
					
					toolRegistrationsSetup = _.reject(toolRegistrationsSetup, function(reg) {
						return reg.type === 'nav-item';
					});
					
				}

				sb.notify({
					type: 'register-tool',
					data: {
						navigationItem: {
							moduleId: sb.moduleId,
							instanceId: sb.instanceId,
							id: 'tasks',
							title: 'Tasks',
							icon: '<i class="fa fa-tasks"></i>',
							afterLoad: function (state) {

								checkTaskStates(function (taskState) {

									sb.data.db.obj.getWhere('tasks', {
										assignee: {
											type: 'contains',
											value: sb.data.cookie.userId
										},
										childObjs: {
											status: true
										}
									}, function (tasksList) {

										var list = _.filter(tasksList, function (obj) {
											return obj.status !== 2;
										});
										var assignedList = _.filter(tasksList, function (obj) {
											return obj.status === 0;
										});
										var inProgressList = _.filter(tasksList, function (obj) {
											return obj.status === 1;
										});
										var labelColor = '';

										if (!_.isEmpty(list)) {
											labelColor = 'red';
										} else {
											labelColor = '';
										}

										sb.notify({
											type: 'app-update-quickAction',
											data: {
												itemId: 'tasks',
												viewObj: {
													id: 'qa-tasks',
													icon: '<span data-tooltip="' + assignedList.length + ' Assigned - ' + inProgressList.length + ' Completed"><i class="fa fa-tasks"></i> <span class="ui ' + labelColor + ' tiny label">' + list.length + '</span></span>',
													css: ''
												}
											}
										});

									});

								});

							},
							views: toolRegistrationsSetup
						}
					}
				});
				
				// Task Work Load Report
				sb.notify({
					type: 'register-report',
					data: {
						id: 'taskWorkloadReport',
						icon: 'chart bar',
						header: 'Tasks',
						subheader: 'Tasks by team, assignee, and status.',
						type: 'Workload'
					}
				});
				
				// Project Work Load Report
				sb.notify({
					type: 'register-report',
					data: {
						id: 'projectWorkloadReport',
						icon: 'chart bar',
						header: 'Projects',
						subheader: 'Projects by assignee and status.',
						type: 'Workload'
					}
				});
				
				// Projects by value Report
				sb.notify({
					type:'register-report',
					data:{
						id:'projectByValueReport',
						icon:'chart bar',
						header:'Projects By Value',
						subheader:'Projects by date and value.',
						type:'Accounting'
					}
				});

				// Booked Events Report
				sb.notify({
					type:'register-report',
					data:{
						id:'bookedEventsReport',
						icon:'chart bar',
						header:'Booked Events',
						subheader:'Projects by date and value (Excluding taxes).',
						type:'Accounting'
					}
				});
				
				// project by total value paid report registration
				sb.notify({
					type:'register-report',
					data:{
						id: 'projectByTotalValuePaidReport',
						icon: 'chart bar',
						header: 'Projects (total value paid)',
						subheader: 'Projects by total value paid.',
						type: 'Accounting'
					}
				});
				
				sb.notify ({
					type: 'register-action'
					, data: {
						name: 		'createTasks'
						, title:	 	'Add task list'
						, icon: 		'tasks'
						, color: 	'green'
						, action: 	function () {}
						, options: {
							tasks: {
								name: 			'Task(s)'
								, type: 			'collection'
								, objectType: 	'groups'
								, group_type: 	'Task'
								, create: 		function (create) {
									
									var newObj = {
										name: 			'Action item'
										, group_type: 	'Task'
									};
									create(newObj);
			
								}
								, view: function (dom, task, onComplete, refresh, options, viewOptions) {
			
									var compact = true;
			
									setupTask(task, function (task) {
			
										sb.notify({
											type: 'view-page',
											data: {
												ui: dom,
												onDraw: function () {},
												page: view,
												state: {
													pageObject: task
												},
												options: {
													compact: compact
												}
											}
										});
			
									}, 1);
			
								}
							}
						}
					}
				});

			}

		},

		buildCollection: function (setup) {

			build_collection(setup.dom, setup.state, setup.draw, setup.mainDom, setup.options);

		},

		destroy: function () {

			_.each(comps, function (comp) {
				comp.destroy();
			});

			comps = {};

		},

		displayList: function (setup) {

			cachedSetup = setup;

			display_taskList(setup);

		},

		run: function (data) {

			data.run(data);

		},

		taskDropped: function (data) {

			var taskObj = data.dropped.obj;
			var groupType = data.in.group;

			taskObj.status = groupType;

			update_task(taskObj, function () {

				if (comps.hasOwnProperty('tasksList') && sb.data.url.get().page === 'initTasks') {

					update_tableData();

				}

				update_tasksBadge();

			});

		}

	}

});
