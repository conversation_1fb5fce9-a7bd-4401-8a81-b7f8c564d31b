Factory.registerComponent('tags', function(sb){
	
	var  ui = {},
		setupOptions = {
			resultList:function(dom, obj){ return dom; },
			childObjs:0
		},
		modal = {},
		
		systemTC = {},
		sysTagSearchForm = {},
		
		otv_tagCont = {},
		otv_newTag_popUI = {},
		otv_popForm = {},
		
		otv_sysTag_popUI = {},
		     		
		sortedListObject = {},
		filteredSortedListObject = {},
		
		objectTagList = [],
		selectedSysTags = [],
		newTagObject = {
			color: '',
			icon: '',
			name: '',
			type: '',
			objectId: '',
			filter: false
		};
		
	var tagsCache = [];
	var setupCache = {};    
	var Data = {};

	var canEdit = true;
	var canSearch = false;
	var style = '';
			
	function loadTaggedObjects(selectedTags, callback) {

		var tagIds = _.pluck(selectedTags, 'id');
		var requestObj = {
			tagIds:tagIds
		};
			
		if (newTagObject !== '') {
			
			requestObj.objectType = newTagObject.type;
		}

		if (!_.isEmpty(selectedTags)) {

			sb.data.db.obj.getWhere(newTagObject.type, {tagged_with:tagIds, childObjs:setupOptions.childObjs}, function(resp) {
				
				callback(resp);
			
			});	
			
		} else { 

			return; 

		}		
					
	}

	function getAllSystemTags(callback) {

		sb.data.db.obj.getAll('system_tags', function(resp){

			callback(resp);	

		}, {
			name:			true,
			tag:			true,
			fname:			true,
			lname:			true,
			color:			true,
			icon:			true,
			profile_image:	true,
			object_bp_type:	true,
			group_type:		true,
			selectionObj: 	true
		});
		
		return true;
	}
	
	function getObjectTagList(callback, tags) {

		if (_.isEmpty(tags)) {
						
			sb.data.db.obj.getById(newTagObject.objectType, newTagObject.objectId, function(obj) {

				var tagIds = [];
				if (obj) {
					
					if (obj.tagged_with) {
						tagIds = _.union(tagIds, obj.tagged_with);
					}
					if (obj.shared_with) {
						tagIds = _.union(tagIds, obj.shared_with);
					}
					
				}
				
				if (_.isEmpty(tagIds)) {
					
					callback(tagIds);
					
				} else {

					getSystemTags(tagIds, function(tags) {

						callback(tags);

					});
					
				}
				
			}, {
				tagged_with: 		true
				, shared_with: 		true
				, name: 			true
				, selectionObj: 	true
				, is_template:		true
			});
			
		} else {
			
			var tagIds = tags;
			if (Array.isArray(tags) && tags[0] && tags[0].id) {
				tagIds = _.pluck(tags, 'id');
			}

			getSystemTags(tagIds, function(tags) {

				callback(tags);

			});
			
		}
		
		return true;
		
	}
	
	function saveNewTagObject(tag, callback, data) {

		function getCurrentTags(response, data, callback) {

			if (data.onChange) {
				
				if (!Array.isArray(objectTagList)) {
					objectTagList = [];
				}
				
				if (response.length > 1) {
					_.each(response, function(tag) {
						objectTagList.push(tag);
					});
				} else {
					objectTagList.push(response);
				}
				
				otv_tagCont.view(objectTagList, response);
				
				callback(true);
				
				data.onChange(objectTagList, response, 'add');
				
			} else {
				
				getSystemTags(response, function(resp) {
					
					objectTagList = resp;
	
					var newTag = _.findWhere(objectTagList, {tag: addTagObj.tag});
	
					if (newTag == undefined) { 
						newTag = _.findWhere(objectTagList, {id: addTagObj.tagId});
					}

					objectTagList = _.union(objectTagList, tagsCache);
					if (newTag && newTag) {
						otv_tagCont.view(objectTagList, newTag.id);
					}
					
					callback(true);
					
					if(newTag.object_bp_type == 'users' && newTag.id != sb.data.cookie.userId){
					
						sb.data.db.obj.getById('', addTagObj.typeId, function(producer){
		
							sb.data.db.obj.getById('', sb.data.cookie.userId, function(current){
								
								function notEvtInfo(user, prod){
									var ret = {};
									
									ret.title = user.fname +' '+ user.lname +' tagged you';
	
									switch(prod.object_bp_type){
										
										case 'groups':
										ret.details = 'You have been added to the <strong>'+ producer.name +'</strong> '+ prod.group_type;									
										break;
										
										case 'document':
										ret.details = 'You have been added to the <strong>'+ producer.name +'</strong> '+ prod.object_bp_type;
										break;
										
										case 'contacts':
										case 'users':
										
										ret.details = user.fname +' shared '+ prod.fname +' '+ prod.lname +'\'s profile with you';
										break;
										
										default:
										ret.details = 'You have been added to <strong>'+ producer.name +'</strong>';									
									}
									
									return ret;
								}
								
								var evtInfo = notEvtInfo(current, producer);
		
								var notificationEvt = {
										title:evtInfo.title,
										details:evtInfo.details,
										producer:producer.id,
										color:'olive',
										icon:'hashtag',
										type: 'general',
										link:getObjectPageParams(producer), 
										notify:[newTag.id],
										notifySet: [newTag.id]
									}						

								sb.data.db.obj.notify(notificationEvt, function(res){});
								
							}, {
								fname:true,
								lname:true
							});
		
						}, {
							name:true
						});
						
					}
	
				});
				
			}
			
		}

		var addTagObj = {
			tag: tag.name || tag.tag,
			tagId: tag.id,
			color: tag.color,
			icon: tag.icon || 'hashtag',
			type: tag.type || newTagObject.type,
			typeId: tag.objectId || newTagObject.objectId
		};
			
		if (
			data
			&& data.objectType
		) {
			addTagObj.objectType = data.objectType;
		}

		if (addTagObj.tagId === null) {
			addTagObj.color = '#9D9D9D';
		}

		if (typeof data.onChange === 'function') {
			addTagObj.typeId = 0;
		}
		sb.data.db.controller('addTagToObject', addTagObj, function(response) {

			var updatedTags;

			if (!response.hasOwnProperty('id')) {
				
				updatedTags = _.union(response.tagged_with, response.shared_with);
				
			} else {
				
				updatedTags = response;
				
			}

			getCurrentTags(updatedTags, data, callback);
	
		});
		
	}
	
	function removeTagObject(tag, data) {

		$('#loader').fadeIn();

		if (data.objectType && data.objectId) {

			sb.data.db.obj.getById(data.objectType, data.objectId, function(object) {

				if (object.related_object === tag.id) {

					$('#loader').fadeOut();

					sb.dom.alerts.ask({
						icon: 'error',
						title: 'Unable to Remove',
						text: 'Removing this tag will remove this object from its parent.',
						primaryButtonText: 'Ok',
						cancelButtonText: 'Close'
					}, function(resp) {
						swal.close();
					});
	
					return;

				} else {

					removeTag();

				}

			});

		} else {

			removeTag();

		}

		function removeTag() {

			var removeTagObj = {
				tagId: 		tag.id
				, type: 	newTagObject.type
				, typeId: 	newTagObject.objectId
				, tagType: 	tag.object_bp_type
				, objectType: data.objectType
			};
			
			if(this.hasOwnProperty('hide')) {
				
				this.hide();	
				
			} else {
				
				modal.hide();
				
			}
			
			if (typeof data.onChange === 'function') {
				
				objectTagList = _.reject(objectTagList, function(obj){
				
					return obj.id == tag.id
					
				});
				
				otv_tagCont.remove(tag);		
					
				setTimeout(function(){
									
					otv_tagCont.view(objectTagList, canEdit, false);

					$('#loader').fadeOut();
					
				}, 100)
				
				data.onChange(objectTagList, tag, 'remove');
				
			} else {
				
				sb.data.db.controller('removeTagFromObject', removeTagObj, function(response) {
					
					if (response) {

						objectTagList = _.reject(objectTagList, function(obj) {
							return obj.id == tag.id
						});
						
						otv_tagCont.remove(tag);		
					
						setTimeout(function() {
											
							otv_tagCont.view(objectTagList, canEdit, false);

							$('#loader').fadeOut();
							
						}, 100);

						if (
							typeof newTagObject.filter === 'object'
							&& newTagObject.filter.hasOwnProperty('run')
						) {

							newTagObject.filter.run({
								deselectedTags: [tag.id]
							});

						}
						
					} else {

						$('#loader').fadeOut();
						
						sb.dom.alerts.alert('Error', 'There was a problem removing the tag--please refresh and try again.', 'error');
						
					}
					
				});	
			
			}

		}
		
	}
	
	function updateSystemTag(tag) {

		if(tag && tag != null){
			
			$('#loader').fadeIn();

			sb.data.db.obj.update('system_tags', tag, function(resp) {

				var i = _.findIndex(objectTagList, function(tag) {
					return tag.id == resp.id;
				});

				if (tag.hasOwnProperty('sortOrder')) {
					resp.sortOrder = tag.sortOrder;
				}
				
				objectTagList[i] = resp;
								
				otv_tagCont.view(objectTagList, canEdit, false);	

				$('#loader').fadeOut();
								
			}.bind(this));				
			
		}

	}
	
	function sortAlpha(tagArray) {

		var ret = {}

		tagArray.sort(function (a, b) {
				
			if (typeof a.tag !== 'string') {
				
				if (typeof a.name === 'string') {
					a.tag = a.name;
				}
				
			}
			
			return a.tag.toLowerCase().localeCompare(b.tag.toLowerCase());
		    	
		});

		_.each(tagArray, function(tag){

			var ucTag = tag.tag.toUpperCase();

			var group = ucTag.slice(0, 1);

			if(ret[group] === undefined){
				ret[group] = [];
			}

			ret[group].push(tag);

		});

		return ret;
	}

	function getTagDisplayData(tag, inSearch) {
		
		var tagName = tag.tag || 'Untitled';
		var tagColor = tag.color || 'grey';
		var tagIcon = tag.icon || 'hashtag';
		var category = {};
		var categoryName = '';

		if (tag.hasOwnProperty('name')) {
			tagName = tag.name;
		}

		tag.description = '';

		// Get legacy colors (this was done because old tags used hex colors)	
		var legacyColors = sb.dom.legacycolors;
		if (legacyColors.hasOwnProperty(tag.color)) {
			tagColor = legacyColors[tag.color];
		}

		if (
			tag.object_bp_type 
			&& tag.object_bp_type.substring(0, 1) === '#'
		) {
			
			var type = _.findWhere(appConfig.Types, {bp_name: tag.object_bp_type.substring(1)});
			if (type) {
				if (!type.icon) {
					type.icon = 'tags';
				}
				tagName = '<i class="ui ' + tagColor + ' ' + type.icon + ' icon"></i> '+ tagName;
				categoryName = '<i class="ui ' + tagColor + ' ' + type.icon + ' icon"></i>'+ type.name;
			} else {
				return;
			}
			
		} else {

			switch (tag.object_bp_type) {

				case 'companies':
					var company = tag;
					company.color = tagColor;
					tagName = sb.dom.getCompanyAvatar(company) + '<span>' + company.name + '</span>';
					tag.group_type = 'companies';
					categoryName = '<i class="ui yellow users icon"></i> Companies';
					break;

				case 'contacts':
					var contact = tag;
					tagName = '<i class="ui ' + tagColor + ' user icon"></i> ' + contact.fname + ' ' + contact.lname;
					tag.group_type = 'contacts';
					categoryName = '<i class="ui yellow user icon"></i> Contacts';
					break;
				
				case 'groups':
					if ( tag.group_type == 'JobType') {
						tagName = '<i class="ui ' + tagColor + ' wrench icon"></i> ' + tagName;
						categoryName = '<i class="ui grey wrench icon"></i> Job Types';
					} else if ( tag.group_type == 'Project' ) {
						tagName = '<i class="ui ' + tagColor + ' project diagram icon"></i> ' + tagName;
						categoryName = '<i class="ui blue project diagram icon"></i> Projects';
					} else {
						tagName = '<i class="ui ' + tagColor + ' users icon"></i> ' + tagName;
						categoryName = '<i class="ui grey users icon"></i> Teams';
					}
					break;
															
				case 'system_tags':
					tagName = `<i class="ui ${tagColor} ${tagIcon} icon"></i> ${tagName}`;
					tag.group_type = 'system_tag';
					categoryName = '<i class="ui grey hashtag icon"></i> Tags';
					break;
					
				case 'users':
					var user = tag;
					user.color = tagColor;
					tagName = sb.dom.getUserAvatar(user) + '<span>' + user.fname + ' ' + user.lname + '</span>';	
					tag.group_type = 'user';
					categoryName = '<i class="ui grey user icon"></i> Team Members';
					break;
					
				default:
					categoryName = '<i class="ui green plus icon"></i> New';
					break;
				
			}

		}

		if (inSearch) {
			if (tag.id != null) {
				tagName = '<div class="ui '+ tagColor + ' label tag ' + tag.object_bp_type + '">'+ tagName +'</div>';
			}
		}	

		// Update data
		tag.name = tagName;
		tag.color = tagColor;
		tag.icon = tagIcon;
		tag.hasDisplayData = true;
		category.name = categoryName;

		return {
			tag: tag,
			category: category
		};

	}

	function tagBuilder(tag, action, search, remove, canEdit) {

		if (!tag.hasDisplayData) {
			var tagDisplayData = getTagDisplayData(tag, false);
			tag = tagDisplayData.tag;
		}

		var tagColor = tag.color;
		var tagIcon = tag.icon || '';
		var tagName = tag.name;
		var tagSelectionClass = '';
		
		// Show whether or not the tag is selected
		if (tag.isSelectable && tag.isSelected === false) {
			tagSelectionClass = 'deselected';
		}
		
		if (remove) {
			tagName = tag.tag + '&emsp; <span class="xx-small"><i class="fa fa-times"></i></span>';
		}
		
		if (search) {
			
			this.makeNode('btn-' + tag.id, 'div', {
				css:'ui small '+ tag.color +' ' + tagIcon + ' label ' + tagSelectionClass + ' ' + tag.object_bp_type
				, text: tagName
				, data: {
					'data-id': tag.id
				}
			}).notify('click', {
				type: 'tagsComp-run',
				data: {
					run: action.bind(this, tag)
				}
			}, sb.moduleId);

		 
		} else {
			
			if ( (
					canEdit === false || 
					( 
						Data.hasOwnProperty('options') &&
						( Data.options.allTagsAreToggleable || Data.options.deselectedTagsAreToggleable )
					)
				) 
				&& tag.hasOwnProperty('isSelectable') 
			) {
				
				if (
					( 
						( typeof newTagObject.filter === 'object'
						&& newTagObject.filter.hasOwnProperty('run') )
					||
						( typeof Data.onChange === 'function' )
					)
					&& tag.isSelectable
				) {
					
					this.makeNode('btn-' + tag.id, 'div', {css: 'ui label tag ' + tagColor + ' ' + tagSelectionClass + ' ' + tag.object_bp_type, text: tagName, data:{'data-id':tag.id}});
					this['btn-' + tag.id].listeners.push(function(selector) {
					
						$(selector).on('click', function() {

							var tags = [];
							var selectedTags = [];
							var deselectedTags = [];
							var shouldDeselect = true;

							var parentClass = '.' + $(this).parent()[0].className.replace(/ /g, '.');
							if (Data.options.minTagSelection) {
								if ($(parentClass).children('.deselected').length === $(parentClass).children('.tag').length - Data.options.minTagSelection) {
									if (parseInt($(parentClass).children('.tag:not(.deselected)').attr('data-id')) === tag.id) {
										shouldDeselect = false;
									}
								}
							}
							
							if (shouldDeselect) {

								$('#loader').fadeIn();

								// TODO: This currently only works with a maxTagSelection of 1
								if (Data.options.maxTagSelection) {
									deselectedTags.push(parseInt($(parentClass).children('.tag:not(.deselected)').attr('data-id')));
									$(parentClass).children('.tag:not(.deselected)').addClass('deselected');
								}

								if($(selector).hasClass('deselected') === false) {
									
									$(selector).addClass('deselected');

									deselectedTags.push(tag.id);

									tag.isSelected = false;
									
								} else {
									
									$(selector).removeClass('deselected');

									selectedTags.push(tag.id);

									tag.isSelected = true;
									
								}

								tagsCache = _.each(tagsCache, function(cachedTag) {
									if (cachedTag.id == tag.id) {
										cachedTag = tag;
									}
								});

								tags = {
									selectedTags: selectedTags,
									deselectedTags: deselectedTags
								}
	
								if (newTagObject.hasOwnProperty('filter')) {
									if (newTagObject.filter) {
										if (newTagObject.filter.hasOwnProperty('run')) {
											if (typeof newTagObject.filter.run === 'function') {
												newTagObject.filter.run(tags);
											}
										}
									}
								}

								if (typeof Data.onChange === 'function') {
									Data.onChange(tags);
								}

							}
							
						});
						
					});

					
				} else {
					
					this.makeNode('btn-' + tag.id, 'div', {css: 'ui label tag '+ tagColor + ' ' + tagSelectionClass + ' ' + tag.object_bp_type, text: tagName, data:{'data-id':tag.id}});
					
				}
				
			} else {
				
				this.makeNode('btn-' + tag.id, 'div', {css: 'ui label tag '+ tagColor + ' ' + tagSelectionClass + ' ' + tag.object_bp_type, text: tagName, data:{'data-id':tag.id}});

				if (appConfig.instance == tag.instance) {

					otv_sysTag_popUI = this.makeNode('btnPop-' + tag.id, 'popover', {
						parent: this['btn-' + tag.id], 
						trigger: 'click', 
						animation: 'pop', 
						placement: 'bottom',
						style:'padding:0px;border:0px;border-radius:4px;'
					});
					
					if(sb.dom.isMobile) {
						
						this['btn-' + tag.id].notify('click', {
							type: 'tagsComp-run',
							data: {
								run: function(data) {

									otv_sysTag_popUI = modal.body;

									otv_sysTag_popUI.view = action.bind(otv_sysTag_popUI, tag);
							
									otv_sysTag_popUI.view();
									
									modal.show();
									
								}
							}
						}, sb.moduleId);
						
					} else {
						
						otv_sysTag_popUI = this.makeNode('btnPop-' + tag.id, 'popover', {
							parent: this['btn-' + tag.id], 
							trigger: 'click', 
							animation: 'pop', 
							placement: 'bottom',
							style:'padding:0px;border:0px;border-radius:4px;'
						});
						
						otv_sysTag_popUI.listeners.push(function(selector){
							$(selector).css('width', '130px');
						});
						
						otv_sysTag_popUI.view = action.bind(otv_sysTag_popUI, tag);
					
						otv_sysTag_popUI.view();
						
					}

				}
				
			}						
			
		}
		
		this.patch();
		
	}
		
	function appNavUILayout(data) {

		var  filteredList = {};
	
		function sysTagSearch(form){

			var formData = form.process().fields.search.value,
			    pagedObj = {  
			      "page":0,
			      "pageLength":10,
			      "paged":true,
			      "sortCol":"tag",
			      "sortDir":"asc",
			      "sortCast":"string"
			   };
			    			        
			this.empty();
			
			if(formData){			

				if(formData.length > 1){

					var filteredResults = _.filter(_.values(filteredSortedListObject)[0], function(tagObj){

						return _.contains(tagObj.tag, formData)			
						
					});

					var updFilteredSortedListObj = _.map(filteredSortedListObject, function(val, key){
						
						var updObj = {};
						updObj[key] = this;
	
						return updObj			
						
					}, filteredResults);
					
					displayAlphSysTagList.call(this, updFilteredSortedListObj[0]);
			
				} else {

					filteredSortedListObject = _.pick(sortedListObject, formData.toUpperCase());
					
					displayAlphSysTagList.call(this, filteredSortedListObject);
					
				}

			} else {

				this.empty();
				this.patch();
				systemTC.view.call(this, sortedListObject);

			}
						   			
		}
		
		function displayAlphSysTagList(tagsObj){

			_.map(tagsObj, function(tags, groups){

				this.makeNode(`${groups}-header`, 'headerText', {css: 'ui dividing header', text: `<strong><em>${groups}</em></strong>`});
				var groupCont = this.makeNode(`${groups}-col`, 'column', {css:'ui large labels'});	
				
				_.each(tags, function(to) {
					
					tagBuilder.call(this, to, function(t) {

						var res = [];
						res.push(t);
				
						filteredList.update(res);
						
					}, true);
					
				}, groupCont);
																	
			}, this);
			
			this.patch();
		}
		
		function tagSearchUI(cont){

			var tagSearchForm = {
				search: {
					type: 'text',
					name: 'search',
					placeholder: 'Search for a Tag...',
					style: 'margin: auto; display: block; font-size: 15px;'	
				}				
			};
			
			getAllSystemTags(function(tagResults){
								
				cont = this;
			
				cont.empty();
				
				sysTagSearchForm = cont.makeNode('tagSearchForm', 'form', tagSearchForm);

				systemTC = cont.makeNode('stContainer', 'div', {css:'ui basic segment'});
				systemTC.view = displayAlphSysTagList.bind(systemTC);
				
				sysTagSearchForm.notify('keyup', {
					type: 'tagsComp-run',
					data: {
						run: function(){
							
							setTimeout(function(){
								
								sysTagSearch.call(systemTC, sysTagSearchForm);
								
							}, 250);
						}
					}
				}, sb.moduleId);
				
				cont.patch();
				
				sortedListObject = sortAlpha(tagResults);

				systemTC.view(sortedListObject);
				
			}.bind(cont));			

		}
		
		function filteredResultsUI(tags){

			function removeSelectedTag(tag){
			
				var  updSelectedSysTags = _.reject(selectedSysTags, function(sysTag){

						return sysTag.id == tag.id; 
						
					});

				selectedSysTags = _.unique(updSelectedSysTags);
				
				filteredList.update(selectedSysTags);
				
					
			}
			
			this.empty();
			
			this.makeNode('ftags', 'div', {css:'ui container'});
			this.makeNode('sp', 'div', {css:'ui section divider'});	
			this.makeNode('ftcont', 'div', {css:'ui three stackable cards'});
			
			_.each(tags, function(t, i){
				
				tagBuilder.call(this, t, removeSelectedTag, true, true);
				
			}, this.ftags);	
			
			
			loadTaggedObjects(tags, function(resp){

				selectedTaggedObjs = resp;
				
				if(_.isEmpty(resp)){
					
					this.makeNode('break', 'lineBreak', {spaces:1});
					this.makeNode('message', 'div', {css: 'ui padded basic segment'});
					this.message.makeNode('noRes', 'text', {text: 'There are no results available'});
					
				} else {
					
					_.each(resp, function(obj){
										
						setupOptions.resultList(this.makeNode('result-'+obj.id, 'div', {css: 'ui card'}), obj);
						
					}, this);		
					
				}
											
				this.patch();	

			}.bind(this.ftcont));
			
			this.patch();
	
		}
		
		this.makeNode('ts', 'div', {css:'six wide column'});
		this.ts.makeNode('tscol', 'container', {collapse: true, title: 'Tags'});	

		this.makeNode('fr', 'div', {css:'ten wide column'});
		filteredList = this.fr.makeNode('frcol', 'container', {collapse: true, title: 'List Results'});
		filteredList.update = filteredResultsUI.bind(this.fr.frcol);

		this.patch();
		
		tagSearchUI(this.ts.tscol);
		
		filteredList.update(selectedSysTags);
				
	}
	
	function tagsUILayout(tags, data) {

		Data = data;

		function sys_tagPopUI(tag){
			
			var dom = this;

			function sys_tagPopUI_colorPalette(tagObj) {
				
				function updateTagColor(color) {

					tagObj.color = color;
					
					updateSystemTag.call(this, tagObj);
					
				};

				this.empty();
				
				this.makeNode('grid', 'div', {css: 'ui centered grid'});
				this.grid.makeNode('column', 'div', {css: 'centered row'});
				this.grid.column.makeNode('btns', 'div', {css: 'buttons', style: 'width:85px;'});											
								
				paletteMaker.call(this.grid.column.btns, updateTagColor.bind(dom), tagObj, this);
				
				this.patch();
				
			}

			function sys_tagPopUI_editTagName(tag){
	
				function toggleEditTagButton(show, patch){

					if(show){
						
						this.popover.buttonCont.tagCont.makeNode('editBtn', 'div', {css:'ui mini teal circular icon right floated button', text: '<i class="check icon"></i>'});
						this.popover.buttonCont.tagCont.editBtn.notify('click', {
							type: 'tagsComp-run',
							data: {
								run: pop_editFormProcess.bind(this, tag, updateSystemTag.bind(this))
							}
						}, sb.moduleId);
						
						this.popover.buttonCont.tagCont.makeNode('back', 'div', {
							css: 'ui mini circular icon basic button',
							text: '<i class="left arrow icon"></i>'
						}).notify('click', {
							type: 'tagsComp-run',
							data: {
								run: function(data) {
									
									sys_tagPopUI.call(data.dom, tag);
									
								},
								dom: this
							}
						}, sb.moduleId);
						
						if(patch){
							this.popover.buttonCont.tagCont.patch();
						}
						
					}else{
	
						if(this.popover.buttonCont.tagCont.editBtn){
							
							delete this.popover.buttonCont.tagCont.editBtn;
							
							if(patch){
								this.popover.buttonCont.tagCont.patch();
							}
							
						}
						
					}
					
				}
				
				function toggleTagAlert(show, patch){
					if(show){
						this.popover.resultsCont.makeNode('added', 'headerText', {size: 'xx-small', text: '<span class="small">Tag has already been created</span>'});
						if(patch){
							this.popover.resultsCont.patch();
						}
					}else{
						if(this.popover.resultsCont.added){
							delete this.popover.resultsCont.added;
							if(patch){
								this.popover.resultsCont.patch();
							}
						}
					}
				}
	
				function pop_editFormSearch(form){
	
					var formData = form.process().fields.tag.value,
					    pagedObj = {  
					      "page":0,
					      "pageLength":5,
					      "paged":true,
					      "sortCol":"tag",
					      "sortDir":"asc",
					      "sortCast":"string"
					   };
	
					this.popover.resultsCont.empty();
	
					this.showEditBtn(true);
					
					if(formData){
						
						if(formData == tag.tag){

							this.showAlert(false, true);
							this.showEditBtn(true, true)
							
						}else if(_.pluck(objectTagList, 'tag').indexOf(formData) != -1){
	
							this.showEditBtn(false, true);
							this.showAlert(true, true);						
		
						}else{
							
							if(this.popover.resultsCont.added){
								
								this.showAlert(false, true);
								
							}
							
							this.showEditBtn(true, true);
							
							sb.data.db.obj.getWhere('system_tags', {tag: {type: 'contains', value: formData}, paged: pagedObj}, function(resp){
	
								if(!_.isEmpty(resp.data)){


									if(_.pluck(resp.data, 'tag').indexOf(formData) == -1){

										this.showEditBtn(true, true);

									}else{
				
										this.showEditBtn(false, true);
										this.showAlert(true, true);											
													
									}
		
								}
													
							}.bind(this));	
						}
																				
					} else {
	
						if(this.popover.resultsCont.added){
	
							this.showAlert(false, true);
							this.showEditBtn(true, true);
	
						}
					}
								
				}
				
				function pop_editFormProcess(tag, callback){

					var formData = this.popover.editRow.formCont.form.process();

					if(formData.completed == true){
						
						tag.tag = formData.fields.tag.value; 

						callback(tag);
						
					} 					
					
				}
			
				var editFormObj = {
						tag: {
							type: 'text',
							name: 'tag',
							placeholder: tag.tag,
							style: 'margin: auto; display: block; font-size: 15px;'	
						}				
					};
				
				var editPopoverUI = this;

				this.empty();
				
				this.makeNode('popover', 'div', {css:'ui grid'});
				this.popover.makeNode('editRow', 'div', {css: 'row'});
				this.popover.editRow.makeNode('formCont', 'div', {css:'sixteen wide column'});
				
				var otv_editPopForm = this.popover.editRow.formCont.makeNode('form', 'form', editFormObj);
				
				this.popover.makeNode('buttonCont', 'div', {css:'row'});
				this.popover.buttonCont.makeNode('tagCont', 'div', {css: 'column'});
								
				this.showEditBtn = toggleEditTagButton.bind(this);
				this.showAlert = toggleTagAlert.bind(this);
				this.showEditBtn(true);
				
				this.popover.makeNode('resultsCont', 'div', {css:'ui labels'});

				otv_editPopForm.notify('keyup', {
					type: 'tagsComp-run',
					data: {
						run: function(){
							
							setTimeout(function(){
	
								pop_editFormSearch.call(editPopoverUI, otv_editPopForm);
								
							}, 500);
						}
					}
				}, sb.moduleId);
				
				this.patch();			
			
			}
			
			if($(window).width() <= 768){
				onMobile = true;
			}

			dom.empty();
			
			dom.makeNode('wrap', 'div', {});
			
			if(sb.dom.isMobile) {
				
				dom.wrap.makeNode('btns', 'div', {css: 'ui mini stackable icon buttons'});	
				
			} else {
				
				dom.wrap.makeNode('btns', 'div', {css: 'fluid ui vertical menu', style:'border-radius:4px;'});
				
			}

			dom.wrap.makeNode('btns', 'div', {css: 'fluid ui vertical menu', style:'border-radius:4px;'});

			dom.wrap.btns.makeNode('deleteBtn', 'div', {
				tag: 'a',
				css: 'ui red item',
				text: '<i class="red trash alternate icon"></i> Delete'
			});
				
			if (tag.object_bp_type === 'system_tags') {
				
				dom.wrap.btns.makeNode('editBtn', 'div', {
					tag: 'a',
					css: 'ui yellow item',
					text: '<i class="edit yellow icon"></i> Edit'
				});
				
			}
			
			var colorBtnColor = 'grey';
			
			if (!_.isEmpty(tag.color) && tag.color.charAt(0) !== '#') {
				colorBtnColor = tag.color;
			}

			dom.wrap.btns.makeNode('colorBtn', 'div', {
				tag: 'a',
				css: 'ui '+ colorBtnColor +' item',
				text: '<i class="eye '+ colorBtnColor +' dropper icon"></i> Color'
			});
			
			dom.wrap.btns.deleteBtn;
			dom.wrap.btns.deleteBtn.listeners.push(
				function (selector) {
					
					$(selector).on(
						'click'
						, removeTagObject.bind(dom, tag, data)
					);
					
				}
			);
			
			if (tag.object_bp_type === 'system_tags') {
				
				dom.wrap.btns.editBtn.listeners.push(
					function (selector) {
						
						$(selector).on(
							'click'
							, sys_tagPopUI_editTagName.bind(dom, tag)
						);
						
					}
				);
				
			}
			
			dom.wrap.btns.colorBtn.listeners.push(
				function (selector) {
					
					$(selector).on(
						'click'
						, sys_tagPopUI_colorPalette.bind(dom, tag)
					);
					
				}
			);
			
			if (
				(
					tag.object_bp_type === 'users'
					&& _.contains(appConfig.headquarters.managers, parseInt(sb.data.cookie.userId))
				)
				|| (
					tag.object_bp_type === 'groups'
					&& _.contains(tag.tagged_with, parseInt(sb.data.cookie.userId))
				)
			) {
				
				var tagType = 'users';
				var tagName = tag.fname +' '+ tag.lname;
				
				switch (tag.group_type) {
					
					case 'Team':
						tagType = 'team';
						tagName = tag.name;
						break;
					
					case 'Project':
						tagType = 'project';
						tagName = tag.name;
						break;
					
				}
				
				dom.wrap.btns.makeNode('open', 'div', {
					tag:'a',
					css: 'ui blue item', 
					text:'<i class="ui external alternate icon"></i> Go to',
					href:sb.data.url.createPageURL('object-view', {
						type: tagType,
						id: tag.id,
						name: tagName
					})
				});
				
			}

			dom.patch();

		}

		function paletteMaker(action, tag, popoverUI) {

			var colors = sb.dom.colors;		
						
			_.each(colors, function(color, i) {	
				
				this.makeNode('color-' + i, 'div', {
					css:'item',
					style:'margin-right:5px; display:inline-block; cursor:pointer;',
					text:'<div class="ui '+ color +' empty circular label" value="'+ color +'"></div>',
				}).notify('click', {
					type: 'tagsComp-run',
					data: {
						run: action.bind(this, color)
					}
				}, sb.moduleId);								
																
			}, this);
			
			this.makeNode('lb_1', 'div', {});
			
			this.makeNode('back', 'div', {
				text: '<i class="ui left arrow icon"></i>',
				css: 'ui mini circular icon basic button',
				style: 'margin-top:5px'
			}).notify('click', {
				type: 'tagsComp-run',
				data: {
					run: function(data) {
						
						sys_tagPopUI.call(popoverUI, tag);
												
					}
				}
			}, sb.moduleId);

			return true;			
			
		}
		
		function pop_uiColorPalette() { 

			function saveNewTag(color) {

				newTagObject.color = color;
			
				saveNewTagObject(newTagObject, function() {
					
					otv_newTag_popUI.hide();
					otv_newTag_popUI.view();
					
				});
				
				newTagObject.name = '';
				newTagObject.color = '';

			};

			this.empty();
			this.makeNode('newtagCont', 'div', {css: 'ui grid'});
			this.newtagCont.makeNode('cont', 'div', {css: 'sixteen wide center aligned column'});
			this.newtagCont.cont.makeNode('newTag', 'div', {css: 'ui basic label', text: newTagObject.name + '&emsp; <span class="xx-small"><i class="fa fa-times"></i></span>'}).notify('click', {
				type: 'tagsComp-run',
				data: {
					run: function(){

						otv_newTag_popUI.view();
						
					}
				}
			}, sb.moduleId);

			this.newtagCont.makeNode('paletteBtns', 'div', {css: 'sixteen wide center aligned column'});
			this.newtagCont.paletteBtns.makeNode('header', 'div', {css: 'ui hidden divider'});
			this.newtagCont.paletteBtns.makeNode('btns', 'div', {css: 'buttons'});
					
			paletteMaker.call(this, saveNewTag);

			this.patch();

		}
		
		function pop_formProcess(form, callback){

			var formData = form.process();

			if(formData.completed == true){
				
				newTagObject.name = formData.fields.tag.value; 

				callback();
				
			} else { 
				
				sb.dom.alerts.alert('Warning', 'Please give your tag a name', 'warning');
				return false;
			}
		
		}
			
		function pop_ui(){

			function toggleAddTagButton(show, patch){

				if(show){
					
					this.popover.buttonCont.tagCont.makeNode('newTagBtn', 'div', {css: 'ui basic mini fluid green button', text: '<i class="fa fa-plus"></i> Add Tag'});
					this.popover.buttonCont.tagCont.newTagBtn.notify('click', {
						type: 'tagsComp-run',
						data: {
							run: pop_formProcess.bind({}, otv_popForm, pop_uiColorPalette.bind(otv_newTag_popUI))
						}
					}, sb.moduleId);
					
					if(patch){
						this.popover.buttonCont.tagCont.patch();
					}
					
				}else{

					if(this.popover.buttonCont.tagCont.newTagBtn){
						
						delete this.popover.buttonCont.tagCont.newTagBtn;
						
						if(patch){
							this.popover.buttonCont.tagCont.patch();
						}
						
					}
					
				}
				
			}
			
			function toggleTagAlert(show, patch){  
				if(show){
					this.popover.resultsCont.makeNode('added', 'headerText', {size: 'xx-small', text: '<span class="small">Tag has already been added</span>'});
					if(patch){
						this.popover.resultsCont.patch();
					}
				}else{
					if(this.popover.resultsCont.added){
						delete this.popover.resultsCont.added;
						if(patch){
							this.popover.resultsCont.patch();
						}
					}
				}
			}

			function pop_formTagSearch(form){

				var formData = form.process().fields.tag.value,
				    pagedObj = {  
				      "page":0,
				      "pageLength":5,
				      "paged":true,
				      "sortCol":"tag",
				      "sortDir":"asc",
				      "sortCast":"string"
				   };
				    
				this.popover.resultsCont.empty();

				this.showAddBtn(true);
				
				if(formData){
				
					if(_.pluck(objectTagList, 'tag').indexOf(formData) != -1){

						this.showAddBtn(false, true);
						this.showAlert(true, true);						

					}else{
						
						if(this.popover.resultsCont.added){
							this.showAlert(false, true);
						}
						this.showAddBtn(true, true);
						
						var tmp = this;

						if ( Factory.logState(true).components.hasOwnProperty('breadcrumbs') ) {
							
							switch (formData.charAt(0)) {
								
								case 't':
									sb.data.db.obj.getWhere('groups', 
									
										{
											name: {
												type: 'contains', 
												value: formData.substr(1)
											},
											group_type:'Team',
											paged: pagedObj
										}, 
										
										function(resp){
														
											if(!_.isEmpty(resp.data)) {
				
												if(_.pluck(resp.data, 'tag').indexOf(formData) == -1) {
													this.showAddBtn(true, true);
													
												}else{
													this.showAddBtn(false, true);			
												}
				
												_.each(resp.data, function(obj, i) {
				
													var hasTag = _.findWhere(objectTagList, {id: obj.id});
				
													if(typeof hasTag == 'undefined'){
														
														tagBuilder.call(this, obj, function(tagObj) {
				
															saveNewTagObject(tagObj, function() {
																
																otv_newTag_popUI.hide();
																otv_newTag_popUI.view();
																
															});
											
														}.bind(otv_newTag_popUI), true);
																								
													}												
					
												}, this.popover.resultsCont);								
					
											}
									
										}.bind(tmp)
										
									);
									break;
									
								case '@':
									sb.data.db.obj.getWhere('users', 
									
										{
											fname: {
												type: 'contains', 
												value: formData.substr(1)
											},
											paged: pagedObj
										}, 
										
										function(resp){
														
											if(!_.isEmpty(resp.data)){
				
												if(_.pluck(resp.data, 'tag').indexOf(formData) == -1){
													this.showAddBtn(true, true);
													
												}else{
													this.showAddBtn(false, true);			
												}
				
												_.each(resp.data, function(obj, i){
				
													var hasTag = _.findWhere(objectTagList, {id: obj.id});
				
													if(typeof hasTag == 'undefined'){
														
														tagBuilder.call(this, obj, function(tagObj) {
				
															saveNewTagObject(tagObj, function(){
																
																otv_newTag_popUI.hide();
																otv_newTag_popUI.view();
																
															});
											
														}.bind(otv_newTag_popUI), true);
																								
													}												
					
												}, this.popover.resultsCont);								
					
											}
									
										}.bind(tmp)
										
									);
									break;
									
								case '#':
									sb.data.db.obj.getWhere('system_tags', {tag: {type: 'contains', value: formData.substr(1)}, paged: pagedObj}, function(resp){
		
										if(!_.isEmpty(resp.data)){
			
											if(_.pluck(resp.data, 'tag').indexOf(formData) == -1){
												this.showAddBtn(true, true);
												
											}else{
												this.showAddBtn(false, true);			
											}
			
											_.each(resp.data, function(obj, i){
			
												var hasTag = _.findWhere(objectTagList, {id: obj.id});
			
												if(typeof hasTag == 'undefined'){
													
													tagBuilder.call(this, obj, function(tagObj) {
														
														saveNewTagObject(tagObj, function(){
															
															otv_newTag_popUI.hide();
															otv_newTag_popUI.view();
															
														});
										
													}.bind(otv_newTag_popUI), true);
																							
												}												
				
											}, this.popover.resultsCont);								
				
										}
																					
									}.bind(tmp));
									break;
								
							}
							
						} else {
							
							sb.data.db.obj.getWhere('system_tags', {tag: {type: 'contains', value: formData}, paged: pagedObj}, function(resp){
								
								if(!_.isEmpty(resp.data)){
	
									if(_.pluck(resp.data, 'tag').indexOf(formData) == -1){
										this.showAddBtn(true, true);
										
									}else{
										this.showAddBtn(false, true);			
									}
	
									_.each(resp.data, function(obj, i){
	
										var hasTag = _.findWhere(objectTagList, {id: obj.id});
	
										if(typeof hasTag == 'undefined'){
											
											tagBuilder.call(this, obj, function(tagObj) {
	
												saveNewTagObject(tagObj, function(){
													
													otv_newTag_popUI.hide();
													otv_newTag_popUI.view();
													
												});
								
											}.bind(otv_newTag_popUI), true);
																					
										}												
		
									}, this.popover.resultsCont);								
		
								}
																			
							}.bind(tmp));
							
						}
						
					}
																			
				} else {

					this.popover.resultsCont.empty();
					this.popover.resultsCont.patch();
					
					if(this.popover.resultsCont.added){

						this.showAlert(false, true);
						this.showAddBtn(true, true);

					}
				}
							
			}
						
			var formObj = {
					tag: {
						type: 'text',
						name: 'tag',
						placeholder: 'Tag Name...',
						style: 'margin: auto; display: block; font-size: 15px;'	
					}				
				};
				
			var dom = this;
				
			dom.empty();
			dom.makeNode('popover', 'div', {css:'ui grid'});
			dom.popover.makeNode('searchRow', 'div', {css: 'row'});
			
			if ( Factory.logState(true).components.hasOwnProperty('breadcrumbs') ) {
				
				dom.popover.searchRow.makeNode('msg', 'div', {
					text:
						'<strong>t</strong> for teams,<br />'+
						'<strong>@</strong> for a user,<br />'+
						'<strong>#</strong> for a hashtag.<br />',
					style:'padding:10px;'
				});
				
			}
			
			dom.popover.searchRow.makeNode('cont', 'div', {css: 'sixteen wide column'});
			
			otv_popForm = dom.popover.searchRow.cont.makeNode('form', 'form', formObj);
				
			dom.popover.makeNode('buttonCont', 'div', {css: 'row'});
			dom.popover.buttonCont.makeNode('tagCont', 'div', {css: 'column'});
			
			dom.showAddBtn = toggleAddTagButton.bind(dom);
			dom.showAlert = toggleTagAlert.bind(dom);
			dom.showAddBtn(true);
			
			dom.popover.makeNode('resultsCont', 'div', {css: 'ui labels'});
			
			otv_popForm.notify('keyup', {
				type: 'tagsComp-run',
				data: {
					run: function(){
						
						setTimeout(function(){

							pop_formTagSearch.call(dom, otv_popForm);
							
						}, 300);
					}
				}
			}, sb.moduleId);			
						
			dom.patch();			
		
		}
		
		function tags_ui(tags, canEdit, sortTags) {

			this.empty();
			sortTags = (sortTags === false) ? false : true;

			if (!_.isEmpty(tags)) {

				if (sortTags) {
				
					objectTagList = tags.sort(function (a, b) {
							
						function getTagTxt(tag) {

							if (_.isArray(tag) || _.isObject(tag)) {

								switch (tag.object_bp_type) {
								
									case 'contacts':
									case 'users':
										return tag.fname +' '+ tag.lname;
										
									case 'system_tags':
										return tag.tag;
										
									case 'groups':
									default:
										return tag.name;
									
								}

							} else {

								return tag;

							}	
							
						}
						
						if (a.object_bp_type === b.object_bp_type) {
							
							return getTagTxt(a).toLowerCase().localeCompare(getTagTxt(b).toLowerCase());
							
						} else {

							if (
								(
									a.object_bp_type == 'groups'
									&& b.object_bp_type == 'users'
								)
								|| (
									a.object_bp_type == 'groups'
									&& b.object_bp_type == 'system_tags'
								)
								|| (
									a.object_bp_type == 'users'
									&& b.object_bp_type == 'system_tags'
								)
							) {
								
								return -1;
								
							} else {
								
								return 1;
								
							}
							
						}
												
					});

				}
				
			}

			_.each(objectTagList, function (tag) {

				if (!tag.hasOwnProperty('sortOrder')) {

					switch (tag.object_bp_type) {

						case 'companies':
							tag.sortOrder = 6;
							break;

						case 'contacts':
							tag.sortOrder = 7;
							break;
						
						case 'groups':
							tag.sortOrder = 5;
							break;
																	
						case 'system_tags':
							tag.sortOrder = 9;
							break;
							
						case 'users':
							tag.sortOrder = 4;
							break;
							
						default:
							tag.sortOrder = 8;
							break;
						
					}

					// Change order if tag is selected
					if (tag.isSelected) {
						tag.sortOrder = 3;
					}


					if ( appConfig.state.pageObject ) {
						
						// Change order if tag is object
						if (tag.id === appConfig.state.pageObject.id) {
							tag.sortOrder = 0;
						}
	
						// Change order if on My Stuff and tag is the same as user
						if (appConfig.state.layer === 'myStuff') {
							if (tag.id === parseInt(sb.data.cookie.get('uid'))) {
								tag.sortOrder = 1;
							}
						}
	
						// Change order if tag is the objects parent
						if (appConfig.state.pageObject.parent) {
							if (_.isNumber(appConfig.state.pageObject.parent)) {
								if (tag.id === appConfig.state.pageObject.parent) {
									tag.sortOrder = 2;
								}
							} else if (appConfig.state.pageObject.parent.hasOwnProperty('id')) {
								if (appConfig.state.pageObject.parent.id) {
									if (tag.id === appConfig.state.pageObject.parent.id) {
										tag.sortOrder = 2;
									}
								}
							}
						}
	
						// Change order if tag is the objects related object
						if (appConfig.state.pageObject.related_object) {
							if (_.isNumber(appConfig.state.pageObject.related_object)) {
								if (tag.id === appConfig.state.pageObject.related_object) {
									tag.sortOrder = 2;
								}
							} else if (appConfig.state.pageObject.related_object.hasOwnProperty('id')) {
								if (appConfig.state.pageObject.related_object.id) {
									if (tag.id === appConfig.state.pageObject.related_object.id) {
										tag.sortOrder = 2;
									}
								}
							}
						}
												
					}
					

					// Keep deselected tags at the front of the list
					if (Data.hasOwnProperty('options')) {
						if (Data.options) {
							if (!Data.options.deselectedTagsAreToggleable) {
								if (Data.options.hasOwnProperty('selectedTags')) {
									if (Data.options.selectedTags) {
										if (_.contains(Data.options.selectedTags, tag.id)) {
											tag.sortOrder = 0;
										}
									}
								}
							}
						}
					}

					if (Data.hasOwnProperty('options')) {
						if (Data.options) {
							if (Data.options.deselectedTagsAreToggleable && tag.hasOwnProperty('isSelected')) {
								if (tag.isSelected) {
									tag.sortOrder = 999;
								} else {
									tag.sortOrder = 9999;
								}
							}
						}
					}

				}

			});

			// Sort the tags by the ones that are selected first
			if (sortTags) {
				objectTagList = _.sortBy(objectTagList, 'sortOrder');
			}

			_.each(objectTagList, function(tag) {

				tagBuilder.call(this, tag, sys_tagPopUI, false, false, canEdit);
				
			}, this);
			
			function displayTagSearchNode(ui, options) {

				// Hide search if max tags are added
				if (Data.hasOwnProperty('options')) {
					if (Data.options) {
						if (Data.options.hasOwnProperty('maxTagAdditions')) {
							if (Data.options.maxTagAdditions > 0) {
								if (objectTagList.length === Data.options.maxTagAdditions) {
									return false;
								}
							}
						}
					}
				}

				var text = sb.dom.isMobile ? '<div class="ui transparent input" style="display:block; width:100%;"><input class="prompt" type="text" placeholder="+ add tag"></div><div class="results"></div>' : '<div class="ui transparent input"><input class="prompt" type="text" placeholder="+"></div><div class="results"></div>';
	
				if (options.canSearch) {
					text = '<div class="ui left icon transparent input"><input class="prompt" type="text" placeholder="Search tags.."><i class="search icon"></i></div><div class="results"></div>';
				}
	
				var searchNode = ui.makeNode('add', 'div', {
					css: 'ui category search',
					style: 'display:inline-block; vertical-align:top; margin-top:8px; margin-left:5px;' + options.style,
					text: text,
					autoRightOffset: true,
					listener:{
						type: 			'search',
						objectType: 	'Tag',
						onSelect: function(result, response) {
	
							if (!_.isEmpty(result)) {
								
								searchNode.loading();
	
								if (options.canSearch) {

									searchNode.search('set value', '');

									$('#loader').fadeIn();
	
									sb.data.db.obj.getById(result.object_bp_type, result.id, function(tag) {
										
										setupCache.onChange = function(tagsList) {}
										
										var tagIds = _.pluck(tagsCache, 'id');
										
										if(_.contains(tagIds, tag.id)) {
											delete tag.isSelectable;	
										} else {
											tag.isSelectable = true;
										}
										
										tagsCache.push(tag);
										
										tags_ui.call(otv_tagCont, tagsCache, canEdit);
										
										tag.isSelected = true;
	
										var tags = [];
										var selectedTags = [];
										var deselectedTags = [];
	
										selectedTags.push(tag.id);
	
										// TODO: This currently only works with a maxTagSelection of 1
										var parentClass = '.' + $(searchNode.selector).prev()[0].className.replace(/ /g, '.');
										if (Data.options.maxTagSelection) {
											deselectedTags.push(parseInt($(parentClass).children('.tag:not(.deselected)').attr('data-id')));
											$(parentClass).children('.tag').each(function() {
												if (tag.id !== parseInt($(this).attr('data-id'))) {
													$(this).addClass('deselected');
												}
											});
										}
	
										tags = {
											selectedTags: selectedTags,
											deselectedTags: deselectedTags
										}
										
										newTagObject.filter.run(tags);
																			
									}, 1);
	
								} else {

									$('#loader').fadeIn();

									sb.data.db.obj.getById(result.object_bp_type, result.id, function(tag) {
								
										saveNewTagObject(
											{
												id: 			result.id
												, objectId: 	data.objectId
												, tag: 			result.tag
												, name: 		result.name
												, color: 		'#C82461'
												, type: 		result.object_bp_type
											} 
											, function(response) {
												
												searchNode.search('set value', '');

												$(searchNode.selector + ' > .input > input').focus();

												$('#loader').fadeOut();
											
											}
											, data
										);

									});
	
								}
								
							}
							
						},
						category: 		'group_type',
						onResponse: 	function(raw) {
	
							var response = {
								results : {}
							};
	
							var searchString = $(searchNode.selector).search('get value');
	
							// add placeholder for creation of system tags
							if (
								!_.contains( 
									_.pluck(raw.results, 'tag')
									, searchString 
								)
								&&
								searchString.charAt(0) !== ':'
								&&
								searchString.charAt(0) !== '@'
								&&
								searchString.charAt(0) !== '#'
							) {
							   
								response.results['undefined-undefined'] = {
									name: 		'<i class=\"ui green plus icon\"></i> New'
									, results: 	[{
										id: 		null
										, name: 	searchString
										, tag: 		searchString
										, color: 	'grey'
									}]
								};
								
							}

							// Remove tags that are already selected
							raw.results = _.filter(raw.results, function(tag) {
								return !_.contains(_.pluck(objectTagList, 'id'), tag.id);
							});
							
							// Put Companies, Contacts, and Teammates first
							response.results['companies-companies'] = {
								name: 		'<i class=\"ui yellow users icon\"></i> Companies'
								, results: 	[]
							};
							response.results['contacts-contacts'] = {
								name: 		'<i class=\"ui yellow user icon\"></i> Contacts'
								, results: 	[]
							};
							response.results['users-user'] = {
								name: 		'<i class=\"ui teal user icon\"></i> Team Members'
								, results: 	[]
							};
	
							_.each(raw.results.reverse(), function(tag) {

								var tagDisplayData = getTagDisplayData(tag, true);

								tag = tagDisplayData.tag;
								var categoryName = tagDisplayData.category.name
	
								if (
									response.results[tag.object_bp_type + '-' + tag.group_type]
								) {
	
									response.results[tag.object_bp_type + '-' + tag.group_type].results.push(tag);							
									
								} else {
									
									response.results[tag.object_bp_type + '-' + tag.group_type] = {
										name : categoryName,
										results : [tag]
									};
									
								} 
								
							});

							// Alphabetize results
							_.each(response.results, function (resultCat) {
								resultCat.results = _.sortBy(resultCat.results, 'name');
							});

							return response;
							
						}
					}
				});

				return searchNode;
	
			}
	
			if (canEdit || canSearch) {
	
				displayTagSearchNode(this, {
					canEdit: canEdit,
					canSearch: canSearch,
					style: style
				});
				
			}

			this.patch();

		}

		function tags_ui_remove(tag){

			this['btn-' + tag.id].addClass('animated zoomOut');
			
		}
		
		if (data) {

			if (data.options) {
				
				if (data.options.hasOwnProperty('canSearch')) {
				
					canSearch = data.options.canSearch;
					
				}

				if (data.options.hasOwnProperty('canEdit')) {
					
					canEdit = data.options.canEdit;
					
				}
				
				if (data.options.hasOwnProperty('style')) {
					
					style = data.options.style;
					
				}
				
			}
			
		}
		
		modal = this.makeNode('modal', 'modal', {});
		
		this.makeNode('tags', 'div', {style:'display:inline-block;'});

		otv_tagCont = this.makeNode('btnGroup', 'div', {css:'ui labels', style:'display:inline-block;'});
		otv_tagCont.view = tags_ui.bind(otv_tagCont);
		otv_tagCont.remove = tags_ui_remove.bind(otv_tagCont);
		
		otv_tagCont.makeNode('loader', 'loader', {});

		this.patch();

		function updateTagView(tags) {
			objectTagList = tags;
			otv_tagCont.view(objectTagList, canEdit);
		}	

		if (typeof data.onChange === 'function') {
			
			updateTagView(tags);

			data.onChange(objectTagList);
			
		} else {

			if (_.isEmpty(tags)) {

				getObjectTagList(function(tags){
					
					updateTagView(tags);
		
				}, tags);

			} else {

				updateTagView(tags);

			}

		}
										
	}
	
	function getObjectPageParams(item) {

		var objType = item.object_bp_type;
		if(objType === 'groups'){
			objType = item.group_type.toLowerCase();
		}
		
		var objName = item.name;
		switch(objType){
			
			case 'contacts':
			case 'users':
			objName = item.fname +' '+ item.lname;
			break;
			
			case 'tasks':
			objName = item.title;
			break;
				
			}
			
			return sb.data.url.createPageURL(
					'object', 
					{
						type:objType, 
						id:item.id,
						name:objName
					}
				);
		
	}

	function getSystemTags(tags, callback) {

		sb.data.db.obj.getById('system_tags', tags, function (tags) {

			callback(tags)

		}, {
			name:			true,
			tag:			true,
			fname:			true,
			lname:			true,
			color:			true,
			icon:			true,
			profile_image:	true,
			object_bp_type:	true,
			group_type:		true,
			selectionObj: 	true
		});

	}
	
	return {
		
		init: function(){

			sb.listen({
				'app-nav-tag-view': this.appNavTagView,
				'object-tag-view': this.objectTagView
			});
			
		},
	
		appNavTagView: function(data){

			sb.listen({
				'tagsComp-run': this.run,
				'tags-destroy': this.destroy
			});
			
			if(data.hasOwnProperty('domContainer')){
				ui = sb.dom.make(data.domContainer.selector);
			}
			
			if(data.hasOwnProperty('resultList')){
				
				setupOptions.resultList = data.resultList;
				
			}
			
			if(data.hasOwnProperty('childObjs')){
				
				setupOptions.childObjs = data.childObjs;
				
			}
			
			if(data.hasOwnProperty('type')){
				newTagObject.type = data.type;
			}
			
			ui.makeNode('break', 'lineBreak', {});
			
			ui.makeNode('cont', 'container', {});
			
			appNavUILayout.call(ui.cont, setupOptions);

			ui.build();
					
		},
		
		objectTagView: function(data) {
			
			var build = true;

			sb.listen({
				'tagsComp-run': this.run,
				'tags-destroy': this.destroy
			});	

			if(data.hasOwnProperty('domObj')){
				
				if(data.hasOwnProperty('options') 
					&& data.options.hasOwnProperty('build')
					&& data.options.build === false) {
						
						ui = data.domObj;
						
						build = false;
						
					} else {
						
						ui = sb.dom.make(data.domObj.selector);
						
					}

					
				if(
					data.options
					&& data.options.hasOwnProperty('filter')
					&& typeof data.options.filter === 'object'
					&& data.options.filter.hasOwnProperty('run')) {
						
					newTagObject.filter = {};
					newTagObject.filter.run = data.options.filter.run;
					
				}

			}
			
			if(data.hasOwnProperty('objectType')){
				newTagObject.type = data.objectType;
			}
			
			if(data.hasOwnProperty('objectId')){
				newTagObject.objectId = data.objectId;
			}

			var tags = [];
			if (Array.isArray(data.tags)) {

				tags = data.tags;

				if (
					_.isEmpty(tags) 
					|| typeof tags[0] === 'number'
				) {

					getSystemTags(tags, function(loadedTags) {

						_.each(loadedTags, function (tag) {

							if (
								data.options
								&& data.options.allTagsAreToggleable
							) {
								tag.isSelectable = true;
								tag.isSelected = _.contains(data.options.selectedTags, tag.id);
							}

							if (
								data.options
								&& data.options.deselectedTagsAreToggleable
							) {
								tag.isSelectable = _.contains(data.options.selectableTags, tag.id);
								tag.isSelected = _.contains(data.options.selectedTags, tag.id);
							}

							if (!tag.isSelectable) {
								delete tag.isSelectable;
							}

						});

						ui.makeNode('div', 'div', {});
						ui.view = tagsUILayout.bind(ui.div, loadedTags, data);
						
						if (build !== false) {
							ui.build();	
						}			
				
						ui.view();	
														
						tagsCache = loadedTags;
						if (tagsCache === null) {
							tagsCache = [];
						}
						tagsUICache = ui;
						setupCache = data;

						if (typeof ui.patch === 'function') {
							ui.patch();
						}

					});	
					
				} else {

					if (newTagObject.filter === false) {
						
						data.domObj.makeNode('div', 'div', {});
						data.domObj.view = tagsUILayout.bind(data.domObj.div, tags, data);
						data.domObj.view();	
						
					}
					
					tagsUICache = ui;
					setupCache = data;

					if (typeof data.domObj.patch === 'function') {
						data.domObj.patch();
					}
					
				}
				
			} else {

				ui.makeNode('div', 'div', {});
				ui.view = tagsUILayout.bind(ui.div, tags, data);
				
				if (build !== false) {
					ui.build();		
				} 
		
				ui.view();
				
			}

		},
		
		run: function(data){ data.run(data); }
		
	}

});