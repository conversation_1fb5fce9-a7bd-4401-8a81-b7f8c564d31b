Factory.register('companyComponent', function(sb){
	
	var components = {},
		companyId = 0,
		contactId = 0,
		ui = {},
		tableUI = {},
		taxPop = {},
		taxToggle = {
			false: {
				label: 'Change Status',
				btnText: '<i class="fa fa-toggle-off fa-2x" aria-hidden="true"></i>'
			},
			true: {
				label: 'Tax Exempt',
				btnText: '<i class="fa fa-toggle-on fa-2x" aria-hidden="true"></i>'
			},
			selected: false,
		},
		taxDocument = {
			false: {
				label: 'Require Documentation',
				btnText: '<i class="fa fa-toggle-off fa-2x" aria-hidden="true"></i>'
			},
			true: {
				label: 'Select File for upload',
				btnText: '<i class="fa fa-toggle-on fa-2x" aria-hidden="true"></i>'
			},
			selected: false,
			docOnFile: null		
		},
		navigation = true;
		
	_.mixin({
		
		trx: function(x){
			
			function exist(x){ return x != null }
		
			return (x !== false) && exist(x)
			
		}
	});
	
	// check client and contact settings
	function setupContactSystem(callback){

		function createClientTypes(infoTypes, callback){
			
			sb.data.db.obj.getAll('company_categories', function(clientTypes){

				if(clientTypes.length == 0){
					
					var newClientTypes = [
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Company'
							},
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Non-profit'
							},
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Vendor'
							},
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Other'
							}
						];
					
					sb.data.db.obj.create(
						'company_categories',
						newClientTypes,
						function(created){
							
							callback(created);
							
						}
					);
					
				}else{
					
					callback(clientTypes);
					
				}
				
			});
			
		}
		
		function createContactTypes(infoTypes, callback){
			
			sb.data.db.obj.getAll('contact_types', function(contactTypes){

				if(contactTypes.length == 0){
					
					sb.data.db.obj.create(
						'contact_types',
						{
							available_types:_.pluck(infoTypes, 'id'),
							name:'Contact',
							states:[
								{
									color:'green',
									icon:'check',
									isEntryPoint:1,
									name:'Active',
									next:["2"],
									previous:[],
									uid:1
								},
								{
									color:'grey',
									icon:'ban',
									name:'Inactive',
									next:[],
									previous:["1"],
									uid:2
								}
							]
						},
						function(created){
							
							callback(created);
							
						}
					);
					
				}else{
					
					callback(contactTypes);
					
				}
				
			});
			
		}
		
		function createInfoTypes(callback){
			
			sb.data.db.obj.getAll('contact_info_types', function(infoTypes){
				
				if(infoTypes.length == 0){
					
					//create the default info types
					var newInfoTypes = [
							{
								data_type:'email',
								is_address:'',
								name:'Email',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'phone',
								is_address:'',
								name:'Phone',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'address',
								is_address:true,
								name:'Default Address',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'website',
								is_address:'',
								name:'Website URL',
								object_bp_type:'"contact_info_types"'
							}
						];
						
					sb.data.db.obj.create('contact_info_types', newInfoTypes, function(created){
						
						callback(created);
						
					});
					
				}else{
					
					callback(infoTypes);
					
				}
				
			});
			
		}
		
		createInfoTypes(function(infoTypes){
			
			createContactTypes(infoTypes, function(contactTypes){
				
				createClientTypes(infoTypes, function(clientTypes){
					
					callback(true);
					
				});
								
			});
			
		});
				
	}
	
	function createCompany(dom, state, draw, onComplete){

		function addContactsToCompany(dom, toAdd, company){

			dom.panel.body.addCont.cont.btns.save.text('<i class="fa fa-circle-o-notch fa-spin"></i> Saving');			
			dom.panel.body.addCont.cont.btns.save.css('pda-btn-primary');
			
			dom.panel.body.addCont.cont.btns.next.css('pda-btn-disabled');

			var objsToUpdate = [];

			_.each(toAdd, function(o){
				
				objsToUpdate.push({
					id:o.id,
					company:company.id
				});
				
			});
						
			sb.data.db.obj.update('contacts', objsToUpdate, function(done){
				
				sb.notify({
					type:'app-navigate-to',
					data:{
						itemId:'clients',
						viewId:'table'
					}
				});				
				
			});			
			
		}
		
		function chooseContact(dom, company){
			
			dom.empty();
			
			dom.makeNode('panel', 'panel', {header:'Choose a Contact', css:'pda-panel-primary'});
			
			dom.panel.body.makeNode('cont', 'column', {width:7}).makeNode('cont', 'container', {css:'pda-container'});
			dom.panel.body.makeNode('addCont', 'column', {width:5}).makeNode('cont', 'container', {css:'pda-container pda-background-gray'});
			
			dom.panel.body.cont.cont.makeNode('col1', 'column', {width:2});
			dom.panel.body.cont.cont.makeNode('col2', 'column', {width:10});

			dom.panel.body.cont.makeNode('results', 'column', {width:12});
			
			dom.panel.body.cont.cont.col1.makeNode('form', 'form', {
				searchField:{
					name:'searchField',
					label:'Field',
					type:'select',
					options:[
						{
							value:'fname',
							name:'First Name'
						},
						{
							value:'lname',
							name:'Last Name'
						}
					]
				}
			});
			
			dom.panel.body.cont.cont.col2.makeNode('form', 'form', {
				searchTerm:{
					name:'searchTerm',
					label:'Search for a contact to add',
					type:'string'
				}
			});
			
			dom.panel.body.cont.cont.col2.makeNode('btns', 'buttonGroup', {css:'pull-right'});
			
			dom.panel.body.cont.cont.col2.btns.makeNode('search', 'button', {text:'<i class="fa fa-search"></i> Search', css:'pda-btn-green'}).notify('click', {
				type:'companyComponent-run',
				data:{
					run:contactSearch.bind(this, dom, company)
				}
			}, sb.moduleId);
			
			dom.panel.body.addCont.cont.makeNode('title', 'headerText', {text:'Company Contacts', size:'x-small', css:'text-center'});
			
			dom.panel.body.addCont.cont.makeNode('contacts', 'container', {});
			
			dom.panel.body.addCont.cont.makeNode('btns', 'buttonGroup', {});
			
			dom.panel.body.addCont.cont.btns.makeNode('next', 'button', {text:'Skip & Continue <i class="fa fa-arrow-right"></i>', css:'pda-btn-primary'}).notify('click', {
				type:'app-navigate-to',
				data:{
					itemId:'clients',
					viewId:'table'
				}
			}, sb.moduleId);
			
			dom.patch();
			
			$(dom.panel.body.cont.cont.col2.form.selector).keypress(function(e) {
			    if(e.which == 13) {
			        
			        contactSearch(dom, company);
			        
			    }
			});
			
		}
		
		function contactSearch(dom, company){
			
			dom.panel.body.cont.cont.col2.btns.search.css('pda-btn-primary');
			dom.panel.body.cont.cont.col2.btns.search.text('<i class="fa fa-circle-o-notch fa-spin"></i> Searching');
			
			dom.panel.body.cont.results.empty();
			dom.panel.body.cont.results.patch();
			
			var searchTerm = dom.panel.body.cont.cont.col2.form.process().fields.searchTerm.value,
				searchField = dom.panel.body.cont.cont.col1.form.process().fields.searchField.value;
				
			var where = {};
			
			where[searchField] = {
					type:'contains',
					value:searchTerm
				};
				
			where.childObjs = 1;		
			
			sb.data.db.obj.getWhere('contacts', where, function(contacts){
				
				dom.panel.body.cont.cont.col2.btns.search.css('pda-btn-green');
				dom.panel.body.cont.cont.col2.btns.search.text('<i class="fa fa-search"></i> Search');
				
				var companyContacts = [];

				dom.panel.body.cont.results.makeNode('break', 'lineBreak', {});
				
				_.each(contacts, function(contact){
										
					dom.panel.body.cont.results.makeNode('contact-'+contact.id, 'container', {css:'pda-container pda-Panel pda-panel-blue'});
					
					dom.panel.body.cont.results['contact-'+contact.id].makeNode('col1', 'column', {width:9});
					dom.panel.body.cont.results['contact-'+contact.id].makeNode('col2', 'column', {width:3});
					
					dom.panel.body.cont.results['contact-'+contact.id].col1.makeNode('name', 'headerText', {text:contact.fname +' '+ contact.lname, size:'small', css:'text-left'});
					dom.panel.body.cont.results['contact-'+contact.id].col1.makeNode('type', 'headerText', {text:'Type: '+ contact.type.name, size:'x-small', css:'text-left'});
					
					dom.panel.body.cont.results['contact-'+contact.id].col2.makeNode('select', 'button', {text:'Add <i class="fa fa-arrow-right"></i>', css:'pda-btn-fullWidth pda-btn-green'}).notify('click', {
						type:'companyComponent-run',
						data:{
							run:function(dom, obj){
								
								companyContacts.push(obj);
								
								dom.panel.body.addCont.cont.makeNode('contact-'+obj.id, 'container', {css:'pda-container pda-Panel pda-panel-blue'});

								dom.panel.body.addCont.cont['contact-'+obj.id].makeNode('remove', 'button', {text:'<i class="fa fa-times"></i>', css:'pda-btn-red pull-right'}).notify('click', {
									type:'companyComponent-run',
									data:{
										run:function(dom, obj){
											
											delete dom.panel.body.addCont.cont['contact-'+obj.id];
											
											dom.panel.body.addCont.cont.patch();
											
										}.bind(this, dom, obj)
									}
								}, sb.moduleId);
								
								dom.panel.body.addCont.cont['contact-'+obj.id].makeNode('name', 'headerText', {text:obj.fname +' '+ obj.lname, size:'xx-small'});
																								
								dom.panel.body.addCont.cont.btns.makeNode('save', 'button', {text:'Save & Continue <i class="fa fa-check"></i>', css:'pda-btn-green'}).notify('click', {
									type:'companyComponent-run',
									data:{
										run:addContactsToCompany.bind(this, dom, companyContacts, company)
									}
								}, sb.moduleId);

								dom.panel.body.addCont.cont.patch();
								
							}.bind(this, dom, contact)
						}
					}, sb.moduleId);
					
				});	
				
				dom.panel.body.cont.results.patch();		
				
			});
			
		}
		
		function finishCreateFlow(dom){
			
			sb.notify({
				type:'app-navigate-to',
				data:{
					itemId:'clients',
					viewId:'table'
				}
			});
			
		}
		
		function init(dom, state, draw){

			dom.empty();
			
/*
			dom.cont.makeNode('headerCont', 'headerText', {css:'ui dividing header', text:'Create a '+ connection});
			dom.cont.makeNode('body', 'div', {css:'small-container'});
			
			dom.cont.headerCont.makeNode('btns', 'buttonGroup', {css:'small ui buttons pull-right'});
					
			dom.cont.headerCont.btns.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left"></i> Back', css:'small ui red button'}).notify('click', {
				type:'companyComponent-run',
				data:{
					run:function(){
						
						sb.notify({
							type:'app-navigate-back',
							data:{}
						});
						
					}
				}
			}, sb.moduleId);
			dom.cont.headerCont.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading', css:'small ui primary green button'});
*/
			
			dom.makeNode('sp', 'lineBreak', {});
			dom.makeNode('body', 'div', {css: 'small-container'});
					
			dom.body.makeNode('btns', 'buttonGroup', {css:'ui buttons pull-right'});
			
			if(!state.hqTool){
				dom.body.btns.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left"></i> Back', css:'ui red button'}).notify('click', {
					type:'companyComponent-run',
					data:{
						run:function(){
							
							sb.notify({
								type:'app-navigate-back',
								data:{}
							});
							
						}
					}
				}, sb.moduleId);				
			}

			dom.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading', css:'ui primary green button'});				
			dom.body.makeNode('title', 'headerText', {text:'Create a Company'});
			dom.body.makeNode('btnsp', 'lineBreak', {spaces: 1});
			
			dom.body.makeNode('cont', 'container', {}).makeNode('formCol', 'column', {w:16});
			
			setupContactSystem(function(systemSetup){
				
				sb.data.db.obj.getAll('users', function(users){
								
					sb.data.db.obj.getAll('inventory_billable_categories', function(products) {
						
						var sortedUsers = _.filter(users, function(user){ return user.hasOwnProperty('fname') && user.enabled == 1; }).sort(function (a, b) {
		
						    	if(a.fname){
									return a.fname.toLowerCase().localeCompare(b.fname.toLowerCase());
								}else{
									return 'z';
								}
								
						});

						sb.data.db.obj.getBlueprint('companies', function(bp){
							
							var userId = sb.data.cookie.get('uid');
		
							var typeOptions = [];
							
							_.each(bp.company_category.options, function(name, val){
								
								typeOptions.push({
									name:name,
									value:val
								});
								
							});

							var formObj = {
									name:{
										name:'name',
										label:'Name',
										type:'text',
										css:'ui input focus'
									},
									companyCategory:{
										name:'companyCategory',
										label:'Type',
										type:'select',
										options:_.sortBy(typeOptions, 'name'),
										value: 0
									},
									is_vendor:{
										label:'Is this company a vendor?',
										name:'is_vendor',
										type:'select',
										options:[
											{
												name:'No',
												value:0
											},
											{
												name:'Yes',
												value:1
											}
										],
										value: 0,
										onChange:function(value){
											
											if(parseInt(value) === 1) {
												
												dom.body.cont.formCol.form.markup_percent.update({
													type: 'text'
												});
												
												dom.body.cont.formCol.form.default_product.update({
													type: 'select'
												});
												
												dom.body.cont.formCol.form.products.update({
													type: 'checkbox'
												});
												
											} else {
												
												dom.body.cont.formCol.form.markup_percent.update({
													type: 'hidden'
												});
												
												dom.body.cont.formCol.form.default_product.update({
													type: 'hidden'
												});
												
												dom.body.cont.formCol.form.products.update({
													type: 'hidden'
												});
												
											}
											
										}
									},
									markup_percent:{
										name:'markup_percent',
										label:'Markup Percentage',
										type:'hidden',
										value: 0
									},
									default_product: {
										name: 'default_product',
										type: 'hidden',
										label: 'Default Product Category',
										options: _.chain(products).map(function(o) {
											return {
												name: o.name,
												value: o.id	
											};
										}).sortBy(function(o) { 
											return o.name; 
										}).value()
									},
									products: {
										name: 'products',
										type: 'hidden',
										label: 'Other Product Categories',
										options: _.chain(products).map(function(o) {
											return {
												name: o.name,
												label: o.name,
												value: o.id	
											};
										}).sortBy(function(o) { 
											return o.name; 
										}).value()
									},
									manager:{
										name:'manager',
										label:'Manager',
										type:'select',
										options:_.map(sortedUsers, function(u){ 
											
											return {value:u.id, name:u.fname +' '+ u.lname}; 
											
										})						
									}
								};
		
							if(_(userId).trx()){
								
								formObj.manager.value = userId;
							}
							
							formObj.default_product.options.unshift({
								name: 'None',
								value: 0
							});
							
							formObj.companyCategory.value = formObj.companyCategory.options[0].value;
												
							dom.body.cont.formCol.makeNode('form', 'form', formObj);
			
							dom.body.btns.makeNode('save', 'button', {css:'small ui green button', text:'<i class="fa fa-check"></i> Save'}).notify('click', {
								type:'companyComponent-run',
								data:{
									run:function(){

										saveNewCompany.call(this, dom, state, draw);
										
									}
								}
							}, sb.moduleId);
												
							//dom.patch();
							
							draw(dom);
							
						}, 1);
					
					}, {
						name: true
					});
					
				}, 
				{
					enabled:true,
					fname:true,
					lname:true
				});

			});
									
		}
		
		function saveNewCompany(dom, state, draw){

			var formInfo = dom.body.cont.formCol.form.process();

			function validate_form(formInfo, callback) {
				
				if(formInfo.fields.name.value === '') {
					
					sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
					
					callback(false);
					
				} else if(parseInt(formInfo.fields.is_vendor.value) === 1 && _.isEmpty(formInfo.fields.markup_percent.value)) {
					
					sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
					
					callback(false);
					
				} else {
					
					callback(true);
					
				}
				
			}
			
			validate_form(formInfo, function(res) {

				if(res === false) {
					
					return;
					
				} else {
					
					var company = {
							name:formInfo.fields.name.value,
							manager: +formInfo.fields.manager.value,
							type: +formInfo.fields.companyCategory.value,
							is_vendor: +formInfo.fields.is_vendor.value
						},
						itemId = 'companies';

					if(formInfo.fields.is_vendor.value == 1){
						
						itemId = 'vendors';
						
						company.markup_percent = formInfo.fields.markup_percent.value;
						company.default_product = +formInfo.fields.default_product.value;
						company.products = formInfo.fields.products.value;
						
					};

					if(state.hasOwnProperty('tagged_with')) {
						
						company.tagged_with = state.tagged_with;
						
					}
		
					sb.data.db.obj.create('companies', company, function(newCompany){

						sb.notify({
							type:'app-navigate-to',
							data:{
								itemId:'companies',
								viewId:{
									id:'single-'+newCompany.id,
									type:'table-single-item',
									title:newCompany.name,
									icon:'<i class="fa fa-user"></i>',
									setup:{
										objectType:'companies'
									},
									rowObj:newCompany,
									removable:true,
									parent:'table'
								}
							}
						});
						
					}, 1);
					
				} 
				
			});
			
		}
				
		init(dom, state, draw);
		
	}	
	
	function editCompany(obj, dom, state, draw){

		function saveChanges(dom){
			
			var form = dom.cont.c.form.process();
			var is_vendor = +form.fields.is_vendor.value;
			var itemId = '';
			var company = {
					id:obj.id,
					name:form.fields.name.value,
					manager: +form.fields.manager.value,
					type:+form.fields.type.value,
					is_vendor: +form.fields.is_vendor.value,
					default_product: +form.fields.default_product.value,
					products: form.fields.products.value
				};

			var onSave = function(updatedObj){

				if(updatedObj.is_vendor != obj.is_vendor){

					sb.notify({
						type:'app-remove-main-navigation-item',
						data:{
							itemId:'companies',
							viewId:'single-'+updatedObj.id
						}
					}, sb.moduleId);
				
					sb.notify({
						type:'app-navigate-to',
						data:{
							itemId:itemId,
							viewId:{
								id:'single-'+updatedObj.id,
								type:'table-single-item',
								title:updatedObj.name,
								icon:'<i class="fa fa-user"></i>',
								setup:{
									objectType:'companies'
								},
								dom:singleState,
								rowObj:updatedObj,
								removable:true,
								parent:'table'
							}
						}
					});
					
				}else{

					sb.notify({
						type:'app-redraw',
						data:{}
					});
				
				}				
				
			};
	
			if(form.completed == false && is_vendor == 1 || _.isEmpty(form.fields.name.value)){
				
				sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
				
				dom.body.btns.back.css('pda-btnOutline-red');	
				dom.body.btns.save.text('<i class="fa fa-check"></i> Save Changes');
				
				return;
			
			}

			if(((state || {}).onEdit || {}).action){

				if(state.onEdit.action.hasOwnProperty('onSave') && typeof state.onEdit.action.onSave == 'function'){

					onSave = function(updObj){
					
						state.onEdit.action.onSave(updObj, dom, state, draw);
						
						if(state.hasOwnProperty('onComplete')) {
							
							state.onComplete(updObj);	
							
						}
						
					}	
					
				}				
			}
	
			switch(company.is_vendor){
				
				case 0:
					itemId = 'companies';
					//company.markup_percent = +obj.markup_percent;
					company.markup_percent = +form.fields.markup_percent.value;
					
					break;
				
				case 1:
					itemId = 'vendors';
					company.markup_percent = +form.fields.markup_percent.value;
					
					break;
				
			}
			
			if(form.fields.products.value === null) {
				
				company.products = 0;
				
			}

			sb.data.db.obj.update('companies', company, function(res){

				dom.body.btns.save.css('pda-btn-green');
				dom.body.btns.save.text('<i class="fa fa-check"></i> Saved');

				onSave(res);
				
			}, 1);				
			
		}

		dom.empty();
		
		dom.makeNode('sp', 'lineBreak', {});
		dom.makeNode('body', 'div', {css: 'small-container'});
				
		dom.body.makeNode('btns', 'buttonGroup', {css:'pull-right'});
		
		if(appConfig.version === 'bin') {
			
			dom.body.btns.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left"></i> Back', css:'pda-btnOutline-red'}).notify('click', {
				type:'companyComponent-run',
				data:{
					run:singleState.bind(this, obj, dom, state, draw)
				}
			}, sb.mobuldeId);	
			
		}

		dom.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading', css:'pda-btn-primary'});				
		dom.body.makeNode('title', 'headerText', {text:'Edit '+ obj.name});
		dom.body.makeNode('btnsp', 'lineBreak', {spaces: 1});
		
		dom.makeNode('cont', 'container', {}).makeNode('c', 'column', {w:16});

		dom.patch();

		sb.data.db.obj.getAll('users', function(users){
			
			var sortedUsers = _.filter(users, function(user){ return user.hasOwnProperty('fname'); }).sort(function (a, b) {

			    	if(a.fname){
						return a.fname.toLowerCase().localeCompare(b.fname.toLowerCase());
					}else{
						return 'z';
					}
					
				});
			
			sb.data.db.obj.getAll('company_categories', function(types){
				
				sb.data.db.obj.getAll('inventory_billable_categories', function(products) {
					
					// Creating the Edit Form's Object
					var formObj = {
							name:{
								name:'name',
								label:'Name',
								type:'text',
								value:obj.name
							},
							type:{
								name:'type',
								label:'Type',
								type:'select',
								options:_.map(_.sortBy(types, 'name'), function(u){
									
									var ret = {
											value:u.id,
											name:u.name
										};
									
									if(obj.type){
										if(obj.type.id == u.id){
											ret.selected = true;
										}	
									}
									
									return ret;
									
								}, [])
							},
							is_vendor:{
								name:'is_vendor',
								label:'Is this one of your vendors?',
								type:'select',
								options:[{
									name:'No',
									value:0
								},{
									name:'Yes',
									value:1
								}],
								value:obj.is_vendor,
								onChange: function(value) {
	
									if(parseInt(value) === 0) {
										
										dom.cont.c.form.markup_percent.update({
											type: 'hidden'
										});
										
										dom.cont.c.form.default_product.update({
											type: 'hidden'
										});
										
										dom.cont.c.form.products.update({
											type: 'hidden'
										});
										
									} else if(parseInt(value) === 1) {
										
										dom.cont.c.form.markup_percent.update({
											type: 'text'
										});
										
										dom.cont.c.form.default_product.update({
											type: 'select'
										});
										
										dom.cont.c.form.products.update({
											type: 'checkbox'
										});
										
									}
									
								}
							},
							markup_percent:{
								name:'markup_percent',
								type:'hidden',
								label:'Markup Percentage',
								value:obj.markup_percent
							},
							default_product: {
								name: 'default_product',
								type: 'hidden',
								label: 'Default Product Category',
								options: _.chain(products).map(function(o) {
									return {
										name: o.name,
										value: o.id	
									};
								}).sortBy(function(o) { 
									return o.name; 
								}).value()							},
							products: {
								name: 'products',
								type: 'hidden',
								label: 'Other Product Categories',
								options: _.chain(products).map(function(o) {
									return {
										name: o.name,
										label: o.name,
										value: o.id	
									};
								}).sortBy(function(o) { 
									return o.name; 
								}).value()
							},
							manager:{
								name:'manager',
								label:'Manager',
								type:'select',
								options:_.map(sortedUsers, function(u){
									
									var ret = {
											value:u.id,
											name:u.fname +' '+ u.lname
										};
									
									if(_(obj.manager).trx()){
										if(obj.manager.id == u.id){
											ret.selected = true;
										}
									}		
									
									return ret;
									
								}, [])
							}
						};
						
					if(obj.type !== null && obj.type.hasOwnProperty('id')) {
						formObj.type.value = obj.type.id;
					}
					
					if(obj.manager !== null && obj.manager.hasOwnProperty('id')) {
						formObj.manager.value = obj.manager.id;
					}
					
					// If editing a Vendor, change markup percent type to 'text'
					if(obj.is_vendor === 1){
						formObj.markup_percent.type = 'text';
						formObj.default_product.type = 'select';
						
						if(_.isObject(obj.default_product)) {
							formObj.default_product.value = obj.default_product.id;	
						}
						
						formObj.products.type = 'checkbox';
						formObj.products.value = obj.products;
					}
					
					formObj.default_product.options.unshift({
						name: 'None',
						value: 0
					});
									
					dom.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
						type:'companyComponent-run',
						data:{
							run:saveChanges.bind(null, dom)
						}
					}, sb.mobuldeId);
	
					// Edit Form
					dom.cont.c.makeNode('form', 'form', formObj);
					
					dom.cont.c.form.is_vendor.notify('change', {
						type:'companyComponent-run',
						data:{
							run:function(){
								
								var formData = this.process().fields;
								
								if(formData.hasOwnProperty('markup_percent') && formData.hasOwnProperty('is_vendor')){
									
									switch(formData.is_vendor.value){
										
										case '1':
										
											this.markup_percent.update({
													type:'text',
													value:obj.markup_percent
												});
											
											break;
											
										case '0':
										
											this.markup_percent.update({
													type:'hidden'
												});
											
											break;
										
									}
									
								}
								
								return;
															
							}.bind(dom.cont.c.form, obj)
						}
					});
	
					delete dom.cont.c.loader;
									
					dom.patch();
					
				}, {
					name: true
				});
				
			});
			
		});
				
	}
	
	function eraseCompanies(objs, dom, state){

		function performDelete(dom, objs, contacts, deleteContacts){

			var onDelete = function(){

				sb.notify({
					type:'app-navigate-to',
					data:{
						itemId:'companies',
						viewId:'table'
					}
				});
							
			}
			
			if(state.hasOwnProperty('onDelete') && typeof state.onDelete == 'function'){

				onDelete = state.onDelete;
					
			}
					
			function deleteObjs(objectType, objs, callback, count){
				
				if(!count){
					count = 0;
				}
				
				if(objs[count]){
					
					sb.data.db.obj.erase(objectType, objs[count].id, function(deleted){
						
						count++;
						
						deleteObjs(objectType, objs, callback, count);
						
					});
					
				}else{
					
					callback(true);
					
				}
				
			}
			
			dom.footer.btns.empty();
			
			dom.footer.btns.makeNode('finishing', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Finishing up', css:'pda-btn-primary'});
			
			dom.footer.btns.patch();
			
			deleteObjs('companies', objs, function(done){
				
				if(deleteContacts){
					
					deleteObjs('contacts', contacts, function(done){
						
						dom.hide();
												
						onDelete();
						
					});
					
				}else{
					
					var contactsToUpdate = _.map(contacts, function(c){
						
						return {
								id:c.id,
								company:0
							};
						
					}, []);
					
					sb.data.db.obj.update('contacts', contactsToUpdate, function(done){
						
						dom.hide();
						
						onDelete();
												
					});
					
				}
				
			});
			
		}
		
		dom.body = sb.dom.make(dom.body.selector),
		dom.footer = sb.dom.make(dom.footer.selector);
		
		dom.body.makeNode('title', 'headerText', {text:'Delete '+ objs.length +' companies?', size:'small'});		
		
		dom.footer.makeNode('btns', 'buttonGroup', {});
		
		dom.footer.btns.makeNode('yes', 'button', {text:'<i class="fa fa-check"></i> Yes', css:'pda-btn-green'}).notify('click', {
			type:'companyComponent-run',
			data:{
				run:function(dom, objs){
					
					dom.footer.btns.yes.text('<i class="fa fa-circle-o-notch fa-spin"></i> Loading');
					dom.footer.btns.yes.css('pda-btn-primary');
					dom.footer.btns.no.css('pda-btn-disabled');
					
					sb.data.db.obj.getWhere('contacts', {
						company:{
							type:'or',
							values:_.pluck(objs, 'id')
						}
					}, function(contacts){
						
						delete dom.body.loading;
						
						dom.body.makeNode('title', 'headerText', {text:'Delete '+ contacts.length +' contacts, or just unlink them from the companies?', size:'small'});
						
						dom.footer.btns.makeNode('yes', 'button', {css:'pda-btn-orange', text:'<i class="fa fa-trash-o"></i> Delete the contacts'}).notify('click', {
							type:'companyComponent-run',
							data:{
								run:performDelete.bind(this, dom, objs, contacts, true)
							}
						}, sb.moduleId);
						dom.footer.btns.makeNode('no', 'button', {css:'pda-btn-orange', text:'<i class="fa fa-check"></i> Don\'t delete the contacts'}).notify('click', {
							type:'companyComponent-run',
							data:{
								run:performDelete.bind(this, dom, objs, contacts, false)
							}
						}, sb.moduleId);
						
						dom.body.build();
						dom.footer.build();
						
					});
					
				}.bind(this, dom, objs)
			}
		}, sb.moduleId);
		dom.footer.btns.makeNode('no', 'button', {text:'<i class="fa fa-times"></i> No', css:'pda-btn-red'});
		
		dom.body.build();
		dom.footer.build();
		
	}
	
	function homeState(dom, statsPage, state){

		function initialView(dom){

			var headerText = 'Recently Added Clients';
			
			if(state && state.is_vendor)
				headerText = 'Recently Added Vendors';

			dom.makeNode('col1', 'div', {css:'twelve wide column'});
			dom.col1.makeNode('btns', 'div', {css:'ui buttons'});
			dom.col1.makeNode('break', 'lineBreak', {});
			
/*
			dom.col1.btns.makeNode('viewContacts', 'div', {text:'<i class="fa fa-users"></i> All Contacts', css:'ui button blue'}).notify('click', {
				type:'companyComponent-run',
				data:{
					run:function(tableUI){
						
						sb.notify({
							type:'app-change-page',
							data:{
								to:'contact'
							}
						});
																											
					}.bind(dom, tableUI)
				}
			}, sb.moduleId);
*/
			
			dom.col1.btns.makeNode('viewClients', 'div', {text:'<i class="fa fa-building-o"></i> All Companies', css:'ui button blue'}).notify('click', {
				type:'companyComponent-run',
				data:{
					run: function(){
						
						components.table.notify({
							type:'crud-table-back-to-table',
							data:{}
						});

					}.bind(dom)
				}
			}, sb.moduleId);
			
			dom.col1.btns.makeNode('createClient', 'div', {css: 'ui button green', text: '<i class="fa fa-plus"></i> Create New'}).notify('click', {
				type: 'companyComponent-run',
				data: {
					run: createCompany.bind(this, [], dom, statsPage)
				}
			});
			
			dom.col1.btns.makeNode('upload', 'div', {css:'ui button teal', text:'<i class="fa fa-upload" aria-hidden="true"></i> Upload contacts csv'})
				.notify('click', {
					type:'contactComponent-run',
					data: {
						run: function(){
							
							uploadCsvState(this);
							
						}.bind(dom)
					}
				}, sb.moduleId);	
				
			
			dom.col1.makeNode('info', 'div', {css:'container'});				
			
			dom.col1.makeNode('table', 'div', {css:'ui raised padded container'});
			dom.col1.table.makeNode('tableTitle', 'div', {css:'ui header', tag:'h1', text:headerText});
			dom.col1.table.makeNode('loading', 'div', {css:'ui active inline centered loader'});
			
			
			dom.patch();
			
			sb.data.db.obj.getAll('companies', function(com){
					
				delete dom.col1.table.loading;
			
				dom.col1.table.makeNode('table', 'table', {
					css: 'table-hover table-condensed table-responsive',
					columns: {
						btns: '',
						name: 'Name',
						manager: 'Manager',
						date_created: 'Date Created'
					}
				});
				
				_.each(com.data, function(company){

					var managerInfo;
					
					if (company.manager != null){
						
						managerInfo = company.manager.fname + ' ' + company.manager.lname;
						
					} else {
						
						managerInfo = '<strong><em>Manager info not on file</em></strong>';
					}

					dom.col1.table.table.makeRow(
						'row-' + company.id, ['', company.name, managerInfo, moment(company.date_created).format('MM/DD/YY')]
					);
					
					dom.col1.table.table.body['row-' + company.id].btns.makeNode('btn', 'button', {css: 'pda-transparent', text: '<p class="text-center"><i class="text-center fa fa-eye fa-2x"></i></p>'}).notify('click', {
						type: 'contactComponent-run',
						data: {
							run: function(){
								
								components.table.notify({
									type:'crud-table-row-link-clicked',
									data:{
										object: company,
										type: 'tab'
									}
								});
								
							}.bind(this)
						}
					});
									
				});
				
				dom.col1.table.patch();
				
			}, 2, {
				page:0,
				sortCol:'date_created',
				sortDir:'desc',
				pageLength:10
			});
		}
		
		function statsView(dom){
			
			dom.contacts.empty();

			dom.contacts.makeNode('stats', 'container', {css:'pda-container pda-Panel pda-panel-blue'});

			dom.contacts.stats.makeNode('btns', 'buttonGroup', {css:'pull-right'});
			dom.contacts.stats.btns.makeNode('back', 'button', {text:'Close <i class="fa fa-times"></i>', css:'pda-btn-x-small pda-btn-red'}).notify('click', {
				type:'eventComponent-run',
				data:{
					run:homeState.bind(this, dom, false)
				}
			}, sb.moduleId);
			
			dom.contacts.stats.makeNode('header', 'headerText', {text:'Stats'});
		}
				
		if (statsPage === true) { 
			
			statsView(dom);
			
		} else {
			
			initialView(dom);
		}		
													
	}
	
	function taxExempt(obj, dom, onComplete){

		function fileCheck(obj, callback){

			sb.data.files.getWhere({oid: obj.id, file_type: "tax_exempt_form" }, function(document){

				if(_(document).trx() && !_.isEmpty(document)){
					
					taxDocument.docOnFile = document[0], taxDocument.selected = true;
					 
				} else {
					
					taxDocument.docOnFile = null, taxDocument.selected = false;
				}			

				callback();
											
			});
					
		}
		
		function processFileUpload(callback){

			var formData = null;
			
			if(this.body.header.modform.fileseg.hasOwnProperty('form')){
				formData = this.body.header.modform.fileseg.form.process().fields;
			}
						
			var  fileMetaData = {
					fileType: "tax_exempt_form",
					fileName: formData.file.value.fileName,
					objectType: "companies",
					objectId: parseInt(obj.id),
					parent: 0

			    };		
			    
			function validateForm(obj){

				if(obj && obj.file.value.fileData != undefined){
					
					return true;
					
				} else {
					
					formData = null;
					
					return false 
				}
			}    

			var validated = validateForm(formData);

			if(validated){
								
				this.footer.empty();
				this.footer.makeNode('loader', 'loader', {size: 'small'});
				this.footer.patch();

				sb.data.files.upload(formData.file.value.fileData, fileMetaData, function(response){
					
					formData = null;

					if(response){
						
						callback();
						
					}else{
						
						sb.dom.alerts.alert('Sorry!', 'An error occurred--please refresh and try again.', 'error');
						
					}
					
				});
				

			} else { 
				
				sb.dom.alerts.alert('Error', 'Please select a file to upload', 'error'); 
				
				formData = null;
				
				return false;
			}
						
		}
		
		function removeDocument(obj, callback){

			sb.dom.alerts.ask({
				
				title: 'Are you sure?',
				text: 'This cannot be undone.'
				
			}, function(resp){

				if(resp){
					
					sb.data.files.getWhere({oid: obj.id, file_type: "tax_exempt_form" }, function(document){

						sb.data.files.delete(document[0].id, function(response){
							
							

							callback(obj, {
								title: 'File Removed',
								text: 'This company is no longer tax exempt' 
							});
							
						});						
					});
					
				}
			});

		}
		
		function updateExemptStatus(obj, alert){
			
			var updObj = _.clone(obj),
			    aTitle = 'Status Changed',
			    aText = 'You have successfully changed the tax exempt status';

			updObj.tax_exempt = taxToggle.selected;			    

			if(this.body.header.modform.fileseg.hasOwnProperty('form')){

				processFileUpload.call(this, function(){

// 					this.modal.hide();
		
					sb.data.db.obj.update('companies', updObj, function(resp){
						
						if(onComplete){
							onComplete(resp);
							return;
						}
						
						sb.dom.alerts.alert(aTitle, aText, 'success');
						
						sb.notify({
							type:'app-redraw',
							data:{}
						});
						
						taxToggle.selected = false,
						taxDocument.selected = false,
						taxDocument.docOnFile = null;
						
					}.bind(this), 2);	
				
				}.bind(this));
				
			} else {
				
// 				this.modal.hide();
	
				sb.data.db.obj.update('companies', updObj, function(resp){
					
					if(onComplete){
						onComplete(resp);
						return;
					}
					
					sb.dom.alerts.alert(aTitle, aText, 'success');
					
					sb.notify({
						type:'app-redraw',
						data:{}
					});
					
					taxToggle.selected = false,
					taxDocument.selected = false,
					taxDocument.docOnFile = null;
					
				}.bind(this), 2);	
				
			}
		
		}
				
		function exemptUI(){
			
			function exemptStatusToggle(){
				
				var b = _.propertyOf(taxToggle)(taxToggle.selected);

				this.makeNode('labl', 'div', {css: 'ui right floated header', tag: 'h3', text: b.btnText}).notify('click', {
					type: 'companyComponent-run',
					data: {
						run: function(){
							
							if(_(taxDocument.docOnFile).trx() && taxDocument.selected == true){

								sb.dom.alerts.alert('Document Uploaded', 'Please remove document before changing status', 'warning');

							} else {
								
								taxToggle.selected = !taxToggle.selected;

								exemptUI();
								
							}
						}
					}
				}, sb.moduleId);
				this.makeNode('togg', 'div', {css: 'ui left floated header', tag: 'h3', text: b.label});				
												
			}
			
			function exemptFileUpload(){

				var b = _.propertyOf(taxDocument)(taxDocument.selected);
			
				var formArgs = {
					file_download: {
						type: 'file-upload',
						label: 'Select a File',
						name: 'file'
					}
				};
				
				function hasDoc(){

					this.makeNode('divider', 'div', {css: 'ui clearing divider'});
					this.makeNode('fhead', 'div', {css: 'ui left floated header', tag: 'h4'});
					this.fhead.makeNode('ficon', 'div', {css: 'file outline icon', tag: 'i'});
					this.fhead.makeNode('ftext', 'div', {css: 'content', text: `${taxDocument.docOnFile.file_name}`});
					
					this.makeNode('btnGroup', 'buttonGroup', {css: 'pull-right pda-vertical-align'});
					this.btnGroup.makeNode('downloadBtn', 'button', {css: 'ui button', text: '<i class="fa fa-download"> Download</i>'}).notify('click', {
						type: 'companyComponent-run',
						data: {
							run: function(){

								window.open(sb.bucket + appConfig.instance +'/'+ taxDocument.docOnFile.loc)
								// sb.data.files.open(taxDocument.docOnFile);
								
							}.bind(this)
						}
					}, sb.moduleId);
					
					this.btnGroup.makeNode('removeBtn', 'button', {css: 'ui red basic button', text: '<i class="fa fa-trash"></i> Remove'}).notify('click', {
						type: 'companyComponent-run',
						data: {
							run: function(){

								sb.data.files.delete(taxDocument.docOnFile.id, function(response){

									if(response){
										
										taxDocument.selected = false;
										taxDocument.docOnFile = null;
										
										exemptUI();
									}
								});										

							}
						}
					}, sb.moduleId);					
								
				}
				
				function doesnotHasDoc(){

					if(taxDocument.selected == true){
						
						this.makeNode('sp', 'lineBreak', {spaces: 2});					
						this.makeNode('form', 'form', formArgs);				
					}

				}
				
				this.makeNode('headr', 'div', {css: 'ui left floated header', text: b.label});
				this.makeNode('stattxt', 'div', {css: 'ui right floated header', tag: 'h3', text: b.btnText}).notify('click', {
					type: 'companyComponent-run',
					data: {
						run: function(){
	
							if(_(taxDocument.docOnFile).trx()){
								
								return true;
								
							} else {

								taxDocument.selected = !taxDocument.selected;
																
							}

							exemptUI();
							
						}
					}
				}, sb.moduleId);
				
				(_(taxDocument.docOnFile).trx()) ? hasDoc.call(this) : doesnotHasDoc.call(this)
								
			}
			
			dom.body.empty();
			dom.footer.empty();
			
			dom.body.makeNode('header', 'column', {w: 16, css: ''});

			dom.body.header.makeNode('modform', 'div', {css: 'ui form'});
			dom.body.header.modform.makeNode('modhead', 'div', {css: 'ui dividing header', text: 'Change Tax Exempt Status'});
			dom.body.header.modform.makeNode('header', 'div', {css: 'ui header'});
			dom.body.header.modform.header.makeNode('head', 'div', {css: 'content', text: `${obj.name}`});
			if ( obj.hasOwnProperty('type') ) {
				if ( obj.type ) {
					if ( obj.type.hasOwnProperty('name') ) {
						dom.body.header.modform.header.makeNode('subhead', 'div', {css: 'sub header', text: `${obj.type.name}`});
					}
				}	
			}
			dom.body.header.modform.makeNode('statseg', 'div', {css: 'ui clearing segment'});
			dom.body.header.modform.makeNode('fileseg', 'div', {css: 'ui clearing segment'});

			exemptStatusToggle.call(dom.body.header.modform.statseg);
			exemptFileUpload.call(dom.body.header.modform.fileseg);
			
			dom.footer.makeNode('saveBtn', 'button', {css: 'pda-btn-green pda-align-right', text: 'Save Changes'}).notify('click', {
				type: 'companyComponent-run',
				data: {
					run: updateExemptStatus.bind(dom, obj)
				}
			}, sb.moduleId);
						
			dom.patch();
			
		}

		(!obj.tax_exempt || obj.tax_exempt == false) ? taxToggle.selected = false : taxToggle.selected = true
		
		dom.empty();
		dom.makeNode('body', 'div', {});
		dom.makeNode('footer', 'div', {});
		
// 		dom.makeNode('modal', 'modal', {size:'small'});
				
		dom.patch();
		
// 		dom.modal.show();	

		fileCheck(obj, exemptUI);

	}
	
	function companyQuickView(dom, object){
		
		///label group
		dom.makeNode('labels', 'div', {css: 'ui blue labels'});	
		
		if(object.is_vendor === 1)
			dom.labels.makeNode('vendor', 'div', {css: 'ui label', text:'Vendor'});
			
		if(object.tax_exempt)	
			dom.labels.makeNode('tax', 'div', {css: 'ui label', text:'Tax Exempt'});

		if(object.is_vendor === 1 && object.markup_percent > 0)
			dom.labels.makeNode('markup', 'div', {css: 'ui label', text:`Mark Up - ${object.markup_percent} %`});	
					
		return dom;
		
	}
	
	function singleState(obj, dom, state, draw){

		function addContact(dom, state, draw, mainDom){
					
			sb.data.db.obj.getAll('contact_types', function(contactTypes){

				sb.data.db.obj.getWhere('users', 
					{
						enabled:1, 
						childObjs:{
							fname:true, 
							lname:true
						}
					}, function(users){
						
						var formObj = {
								fname:{
									name:'fname',
									label:'First Name',
									type:'text',
								},
								lname:{
									name:'lname',
									label:'Last Name',
									type:'text',
								},
								manager:{
									name:'manager',
									label:'Manager',
									type:'select',
									options: []
								},
								contactType:{
									name:'contactType',
									label:'Contact Type',
									type:'select',
									options: []
								}
							};						
						
						_.each(_.sortBy(users, 'fname'), function(user) {

							var userObj = {
								name: user.fname + ' ' + user.lname,
								value: user.id
							}
							
							if(user.id == +sb.data.cookie.userId){
								userObj.selected = true;
								formObj.manager.options.unshift(userObj);
							}else{
						
								formObj.manager.options.push(userObj);
							}
						});
												
						_.each(_.sortBy(contactTypes, 'name'), function(type) {
							
							formObj.contactType.options.push({
								name: type.name,
								value: type.id
							});
							
						});

						dom.empty();
						
						dom.makeNode('spacer', 'lineBreak', {});
						dom.makeNode('header', 'div', {css:'small-container'});
						dom.makeNode('body', 'container', {}).makeNode('formCol', 'column', {w:16});
						
						dom.header.makeNode('btns', 'buttonGroup', {css:'ui buttons pull-right'});
						
						dom.header.btns.makeNode('next', 'button', {css:'small ui green button', text:'Next Step <i class="fa fa-arrow-right"></i>'}).notify('click', {
							type:'companyComponent-run',
							data:{
								run:function(obj, state, draw){
									
									var formInfo = this.body.formCol.form.process();

									if(formInfo.completed == false){
										sb.dom.alerts.alert('', 'Please fill out the entire form', 'error');
										return;
									}

									this.header.btns.next.loading();
									
									var contactObj = {
											fname:formInfo.fields.fname.value,
											lname:formInfo.fields.lname.value,
											manager: +formInfo.fields.manager.value,
											type: +formInfo.fields.contactType.value,
											company:obj.id,
											tagged_with: [parseInt(sb.data.cookie.userId)]
										};

									var dom = this;

									sb.data.db.obj.create('contacts', contactObj, function(newContact){

										if(appConfig.version === 'bin') {

											$('.ui.modal').modal('hide all');
											
											sb.notify({
												type:'app-remove-main-navigation-item',
												data:{
													itemId:'companies',
													viewId:'add-contact-'+obj.id
												}
											});
											
											sb.notify({
												type:'app-navigate-to',
												data:{
													itemId:'contacts',
													viewId:{
														id:'single-'+newContact.id,
														type:'table-single-item',
														title:newContact.fname +' '+ newContact.lname,
														icon:'<i class="fa fa-user"></i>',
														setup:{
															objectType:'contacts'
														},
														rowObj:newContact,
														removable:true,
														parent:'table'
													}
												}
											});
										
										} else {

											state.onSave(newContact);
											
										}
										
									}, 2);
									
								}.bind(dom, obj, state, draw)
							}
						}, sb.moduleId);
						dom.header.makeNode('title', 'headerText', {text:'Add a contact to '+ obj.name});
						dom.header.makeNode('spacer', 'lineBreak', {});
						
						dom.body.formCol.makeNode('form', 'form', formObj);
						
						dom.patch();
											
						draw(dom);
			});
						
			}, 
			{
				name:true
			});
			
		}	
				
		function init(obj, dom, state, draw){

			var edit = (state.hasOwnProperty('edit')) ? state.edit : true;
			var taxBtnText = '<i class="fa fa-certificate"></i>  Change Tax Status';
			var taxBtnCSS = 'ui grey basic button';   
			var companyType = (!_.isEmpty(obj.type)) ? obj.type.name : '<i>Type not selected</i>';
			var manager = '<i>not selected</i>';			
			var vendorText = '';
			var contactInfoCollapse = true;
			
			if(obj.is_vendor === 1){
				vendorText = '<small><label class="label pda-label-primary">Vendor</label></small>';
			}
			
			if(obj.manager){
				manager = obj.manager.fname +' '+ obj.manager.lname;
			}						    
			    
			if(obj.hasOwnProperty('tax_exempt') && obj.tax_exempt == true){
				
				taxBtnText = '<i class="fa fa-check-circle-o fa-lg"></i>  Tax Exempt',
				taxBtnCSS = 'ui grey button';
			}    

			dom.empty();

			dom.makeNode('container', 'div', {css: 'ui stackable grid'});

/*
			if(
				appConfig.version != 'bento' &&
				appConfig.instance != 'rickyvoltz' &&
				appConfig.instance != 'zachvoltz' &&
				appConfig.instance != 'joshgantt' &&
				appConfig.instance != 'petermikhail'
			){
*/
			if(appConfig.version === 'bin') {

				dom.container.makeNode('alertContainer', 'div', {css: 'sixteen wide column'});			
				dom.container.makeNode('hleft', 'div', {css: 'eight wide column'});
	 			
				dom.container.hleft.makeNode('name', 'headerText', {text:`${obj.name}, <xx-small><i>${companyType}</i></xx-small>  ${vendorText}
					<br /><small>Manager: ${manager}, Created: ${moment(obj.date_created).fromNow()}</small>`});	
				if(obj.is_vendor === 1){
					dom.container.hleft.makeNode('markup', 'headerText', {text:`<small>Markup</small> <span class="pda-color-green">+${obj.markup_percent}%</span>`, size:`x-small`});
				}				
							
				dom.container.makeNode('hright', 'div', {css: 'eight wide column'});
				dom.container.hright.makeNode('btns', 'buttonGroup', {css:'two mini'});
				dom.container.hright.btns.makeNode('taxExempt', 'button', {text:taxBtnText, css: taxBtnCSS}).notify('click', {
					type: 'companyComponent-run',
					data: {
						run:taxExempt.bind(dom, obj, dom.container.alertContainer)
					}
				}, sb.moduleId);						
				dom.container.hright.btns.makeNode('edit', 'button', {text:'<i class="fa fa-pencil"></i> Edit / <i class="fa fa-trash-o"></i> Delete', css:'pda-btn-orange'}).notify('click', {
					type:'companyComponent-run',
					data:{
						run:editCompany.bind(this, obj, dom, {}, draw)
					}
				}, sb.moduleId);
			}
						
			dom.container.makeNode('headerCont', 'div', {css: 'sixteen wide column'});

			companyQuickView(dom.container.headerCont, obj);
			
			dom.container.makeNode('state', 'div', {css: 'ui container'});
			
			dom.container.makeNode('lcontent', 'div', {css: 'three wide column'});
			dom.container.lcontent.makeNode('cinfo', 'div', {css: 'column'});

			dom.container.makeNode('contacts', 'div', {css: 'thirteen wide column'});
			
			
			dom.container.makeNode('contactsList', 'div', {css: 'sixteen wide column'});
			
			dom.container.makeNode('rcontent', 'div', {css: 'sixteen wide column'});
			dom.container.rcontent.makeNode('notes', 'div', {css: ''});
		
			if(draw !== undefined) {
			
				draw({
					dom:dom,
					after:function(dom){

						if(obj.hasOwnProperty('state')){
							
							sb.notify({
								type: 'view-field',
								data: {
									type:'state',
									property:'state',
									ui: dom.container.state,
									obj: obj
								}
							});
						}
// !todo addContact								
						sb.notify({
							type:'show-collection',
							data:{
								actions:{
									create:function(ui, newObj, onCreate){

										newObj.onSave = onCreate;
														
										addContact(ui, newObj, draw, dom);
										
									}
								},
								domObj:dom.container.contacts,
								fields:{
									fname:{
										title:'First Name',
										type:'title',
										isSearchable:true
									},
									lname:{
										title:'Last Name',
										type:'title',
										isSearchable:true
									},
									type:{
										title:'Type',
										type:'type'
									},
									state:{
										title:'State',
										type:'state'
									},
									email:{
										title:'Email',
										view:function(c, obj){
											
											var infoToShow = _.filter(obj.contact_info, function(info){
												
												if(info.type && info.type.data_type){
													return info.type.data_type === 'email';
												}
												
												return false;
												
											})[0];
											
											if(infoToShow) {
												c.makeNode('t', 'div', {text:infoToShow.info});
											}
											
										},
										type:'detail'
									},
									phone:{
										title:'Phone',
										view:function(c, obj){
											
											var infoToShow = _.filter(obj.contact_info, function(info){
												
												if(info.type && info.type.data_type){
													return (info.type.data_type === 'cellphone' || info.type.data_type === 'phone');
												}
												
												return false;
												
											})[0];
											
											if(infoToShow) {
												c.makeNode('t', 'div', {text:infoToShow.info});
											}
											
										},
										type:'detail'
									},
									address:{
										title:'Address',
										view:function(c, obj){

											var infoToShow = _.filter(obj.contact_info, function(info){
									
												if(info.type && info.type.data_type){
													return info.type.data_type === 'address';
												}
												
												return '';
												
											})[0];
			
											var addressObject = {
												address: infoToShow
											}
											
											sb.notify({
												type: 'view-field'
												, data: {
													type: 'address'
													, property: 'address'
													, obj: addressObject
													, isMetric: true
													, options: {
														edit: false
													}
													, ui: c.makeNode('address', 'div', {})
												}
											});
											
										},
										type:'detail'
									}
								},
								groupings:{
									state:'State'
								},
								objectType:'contacts',
								pageLength:10,
								where:{
									company:obj.id,
									childObjs:{
										fname:true,
										lname:true,
										type:{
											name:true,
											states:true
										},
										company:{
											name:true
										},
										state:true,
										contact_info:{
											city:true,
											country:true,
											state:true,
											street:true,
											street2:true,
											zip:true,
											info:true,
											is_primary:true,
											name:true,
											title:true,
											type:1
										}
									}
								}
							}
						});
							
						components.info.notify({
							type:'start-contact_info-component',
							data:{
								domObj:dom.container.lcontent.cinfo
							}
						});
						
						var contactInfoCompSetup = {
								contactType:'companies',
								contactInfoTypes:[],
								info:[]
							};
						
						var typeId = 0;
						if(obj.type){
							typeId = obj.type.id;
						}
						
						components.info.notify({
							type:'show-contact-info-column',
							data:{
								contactTypeId:typeId,
								contactId:obj.id,
								contactType:'companies',
								compData:{},
								spread:4
							}
						});
						
						sb.notify({
							type: 'show-note-list-box',
							data: {
								domObj:dom.container.rcontent.notes,
								objectIds:[obj.id],
								objectId:obj.id
							}
						});
																		
					}
				});
				
			} else {

				dom.patch();
				
				if(obj.hasOwnProperty('state')){
					
					sb.notify({
						type: 'view-field',
						data: {
							type:'state',
							property:'state',
							ui: dom.container.state,
							obj: obj
						}
					});
					
				}
					
				components.info.notify({
					type:'start-contact_info-component',
					data:{
						domObj:dom.container.lcontent.cinfo
					}
				});
				
				var contactInfoCompSetup = {
						contactType:'companies',
						contactInfoTypes:[],
						info:[]
					};
				
				var typeId = 0;
				if(obj.type){
					typeId = obj.type.id;
				}
				
				components.info.notify({
					type:'show-contact-info-column',
					data:{
						contactTypeId:typeId,
						contactId:obj.id,
						contactType:'companies',
						compData:{},
						spread:4
					}
				});
				
				sb.notify({
					type: 'show-note-list-box',
					data: {
						domObj:dom.container.rcontent.notes,
						objectIds:[obj.id],
						objectId:obj.id
					}
				});
				
			}	
						
		}

		init(obj, dom, state, draw);
		
	}

	function uploadCsvState(ui){

		function obj_mapper_view(map){
			
			// initial state
			if(map === undefined || _.isEmpty(map)){
			
				this.empty();
				
				this.makeNode('message', 'text', {text:'Please choose a file to upload', css:'text-center text-muted'});
				
				this.patch();
			
			// updates to map obj
			}else{
				
			}
			
		}
		
		ui.empty();
		
/*
		var left = ui.makeNode('left', 'column', {width:4});
		var right = ui.makeNode('right', 'column', {width:8});
		
		// LEFT COLUMN
		left.makeNode('title', 'headerText', {text:'Upload csv file'});
		left.makeNode('form', 'form', {
			
			file: {
				name:'file',
				type:'file-upload',
				label:'Select a file'
			}
			
		});
		
		// RIGHT COLUMN
		right.update = obj_mapper_view;
		right.update();
*/
		
		ui.patch();
		
		if(components.hasOwnProperty('csvUploader')){
			components.csvUploader.destroy();
		}
		
		components.csvUploader = sb.createComponent('csv-uploader');
		components.csvUploader.notify({
			type:'show-csv-contact-uploader',
			data:{
				domObj:ui,
				object_type:'contacts'
			}
		});
		
	}
	
	function tagResultsList(dom, obj){

		var typeString = '<i>No type selected</i>',
			setupItemId,
			viewIdIcon;

		!!obj.is_vendor 
			? (setupItemId = 'vendors', viewIdIcon = '<i class="fa fa-truck"></i>') 
			: (setupItemId = 'companies', viewIdIcon = '<i class="fa fa-building"></i>');
		
		if(obj.type){
			typeString = obj.type.name;
		}
																								
		dom.makeNode('cont', 'div', {css:'content'});
		
		dom.cont.makeNode('name', 'div', {css:'ui header', text:`${viewIdIcon} ${obj.name}`});
		
		dom.cont.makeNode('type', 'div', {css:'meta', text:`<span class="small">Type:</span> ${obj.type.name}`});
		dom.cont.makeNode('manager', 'div', {css: '', tag:'p', text:`<span class="small">Manager:</span> ${obj.manager.fname} ${obj.manager.lname}`});
		
		dom.makeNode('btn', 'div', {css: 'ui bottom attached button', text:`View ${obj.name}`});
		dom.btn.notify('click', {
			type:'app-navigate-to',
			data:{
				itemId:setupItemId,
				viewId:{
					id:'single-'+obj.id,
					type:'table-single-item',
					title:obj.name,
					icon:viewIdIcon,
					setup:{
						objectType:'companies'
					},
					dom:singleState,
					rowObj:obj,
					removable:true,
					parent:'table'
				}
			}
		}, sb.moduleId);
		
		return dom;				
		
	}
			
	return {
				
		init: function(){

			components.table = sb.createComponent('crud-table');
			components.info = sb.createComponent('contact_info');
			
			if(sb.sys.state.components.tags){
				components.tags = sb.createComponent('tags');
			}

			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'companies',
						title:'Clients',
						icon:'<i class="fa fa-building"></i>',
						views:[
							{
								id:'table',
								display:false,
								//default: true,
								type:'table',
								title:'All Clients',
								icon:'<i class="fa fa-th-list"></i>',
								setup:{
									objectType:'companies',
									tableTitle:'<i class="fa fa-building"></i> Clients',
									childObjs:2,
									searchObjects:[
										{
											name:'Client Name',
											value:'name'
										}
									],
									filters:false,
									download:false,
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										}
									},
									rowSelection: false,
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.name;
										},
										action:function(obj, dom, state, draw){

											singleState(obj, dom, state, draw);
											
										}
										
									},
									visibleCols:{
										name:'Company Name',
										manager:'Manager',
										type:'Type',
										email:'<i class="fa fa-check"></i> Email',
										cellphone:'<i class="fa fa-check"></i> Phone',
										address:'<i class="fa fa-check"></i> Address',
										tax_exempt_status: 'Tax Status',
										date_created:'Date Created'
									},
									cells: {
										date_created:function(obj){
											return moment(obj.date_created).format('M/D/YYYY h:mm a');
										},
										manager: function(obj) {
											
											if(obj.manager !== null){
											
												return obj.manager.fname + ' ' + obj.manager.lname;
											
											}else{
												
												return 'Not selected';
												
											}
											
										},
										name:function(obj){
											return obj.name;
										},
										type: function(obj){
											if(obj.type){
												return obj.type.name;
											}else{
												return 'Type not selected';
											}
										},
										email:function(obj){
											if(obj.primaryInfo){
												if(obj.primaryInfo.email){
													return obj.primaryInfo.email.info;
												}else{
													return '';
												}
											}else{
												return '';
											}
										},
										cellphone:function(obj){
											if(obj.primaryInfo){
												if(obj.primaryInfo.cellphone){
													return obj.primaryInfo.cellphone.info;
												}else{
													return '';
												}
											}else{
												return '';
											}
										},
										address:function(obj){
											if(obj.primaryInfo){
												if(obj.primaryInfo.address){
													if(obj.primaryInfo.address.info){
														var clientAddress = obj.primaryInfo.address.info.split(',');
														return 	`${clientAddress[0]} <br />
																${clientAddress[1]}, ${clientAddress[2]}`;
													}else{
														return '';
													}
												}else{
													return '';
												}
											}else{
												return '';
											}
										},
										tax_exempt_status: function(obj, dom){
											
											var labelText = 'Not Tax Exempt',
												    labelColor = 'warning';
    
											if(obj.tax_exempt == true){
												
												labelText = 'Tax Exempt',
												labelColor = 'gray';
												
											}

											return dom.makeNode('label-'+obj.id, 'label', {text: labelText, color: labelColor});
																					
										}
										
									},
									paged:{
										sortCol:'name',
										sortDir:'asc'
									},
									data:function(paged, callback){
																				
										sb.data.db.obj.getWhere('companies', {is_vendor:0, childObjs:{
											date_created:true,
											manager:{
												fname:true,
												lname:true
											},
											name:true,
											type:{
												name:true
											},
											contact_info:{
												is_primary:true,
												info:true,
												type:{
													data_type:true
												}
											},
											tax_exempt:true,
											is_vendor:true,
											markup_percent:true
										}, paged:paged}, function(ret){
											
											_.each(ret.data, function(company){
											
												if(company.hasOwnProperty('contact_info') && company.contact_info !== null){
													
													var primaryInfos = _.where(company.contact_info, {is_primary: "yes"});
													
													company.primaryInfo = {};
													
													_.each(primaryInfos, function(info){
														
														if(info.type){
															if(info.type.data_type){
																if(info.type.data_type == "email"){
																	
																	company.primaryInfo.email = info;
																	
																}else if(info.type.data_type == "cellphone"){
																	
																	company.primaryInfo.cellphone = info;
																	
																}else if(info.type.data_type == "address"){
																	
																	company.primaryInfo.address = info;
																	
																}
															}
														}
													});
													
												}
											
											});
				
											callback(ret);
											
										});
										
									}
								}								
							},
							// Collections view
							{
								id: 'company-collection',
								type: 'custom',
								title: 'Clients',
								icon: '<i class="building icon"></i>',
								isCollection: true,
								default: true,
								dom: function(dom, state, draw) {
									
									draw({
										dom: dom,
										after: function(dom) {
											
											sb.notify({
												type: 'show-collection',
												data: {
													domObj: dom,
													actions: {
														view: true
														
													},
													fields: {
														name: {
															title: 'Name',
															type:'title'
														},
														manager: {
															title: 'Manager',
															view: function(dom, obj){

																var manager = 'No Manager Assigned';
																
																if(!_.isEmpty(obj.manager) && _.isObject(obj.manager)) {
																	
																	manager = obj.manager.fname + ' ' + obj.manager.lname;
																	
																}
																
																dom.makeNode('name', 'div', {text: manager});
															},
															type: 'description'
														},
														typeSelect: {
															title: 'Type',
															view: function(dom, obj){

																var type = 'No type selected';
																
																if(!_.isEmpty(obj.type) && _.isObject(obj.type)) {
																	
																	type = obj.type.name;
																
																	dom.makeNode('type', 'div', {css: 'ui label', text: type});
																}
															},
															type: 'type'													
														},
														email: {
															title: 'Email',
															view: function(dom, obj) {
																
																var email = {};
																var info = 'N/A';
																
																if(obj.hasOwnProperty('contact_info') && !_.isEmpty(obj.contact_info)) {
																	
																	email = _.filter(obj.contact_info, function(o) {
																		return o.type.data_type === 'email';
																	});
																	
																}
																
																if(!_.isEmpty(email) && email[0].hasOwnProperty('id')) {
																	
																	info = email[0].info;
																	
																} 

																dom.makeNode('email', 'div', {text: info});
																
															},
															type: 'description'	
														},
														phone: {
															title: 'Phone',
															view: function(dom, obj) {
																
																var phone = {};
																var info = 'N/A';
																
																if(obj.hasOwnProperty('contact_info') && !_.isEmpty(obj.contact_info)) {
																	
																	phone = _.filter(obj.contact_info, function(o) {
																		return o.type.data_type === 'phone';
																	});
																	
																}
																
																if(!_.isEmpty(phone) && phone[0].hasOwnProperty('id')) {
																	
																	info = phone[0].info;
																	
																} 

																dom.makeNode('phone', 'div', {text: info});
																
															},
															type: 'description'
														},
														address: {
															title: 'Address',
															view: function(c, obj) {

																var infoToShow = _.filter(obj.contact_info, function(info){
									
																	if(info.type && info.type.data_type){
																		return info.type.data_type === 'address';
																	}
																	
																	return '';
																	
																})[0];
								
																var addressObject = {
																	address: infoToShow
																}
																
																sb.notify({
																	type: 'view-field'
																	, data: {
																		type: 'address'
																		, property: 'address'
																		, obj: addressObject
																		, isMetric: true
																		, options: {
																			edit: false
																		}
																		, ui: c.makeNode('address', 'div', {})
																	}
																});
																
															},
															type: 'description'
														},
														tax_exempt: {
															title: 'Tax Status',
															view: function(dom, obj) {

																if(obj.tax_exempt === 'false' || obj.tax_exempt === null) {
																	
																	dom.makeNode('label', 'div', {
																		text: 'Not tax exempt', 
																		css: 'ui orange label'
																	});
																	
																} else if(obj.tax_exempt === 'true') {
																	
																	dom.makeNode('label', 'div', {
																		text: 'Tax exempt', 
																		css: 'ui grey label'
																	});
																	
																}
																 																
															},
															type: 'description'
														},
														date_created: {
															title: 'Created On',
															type: 'date'
														}
													},
													groupings: {
														type: 'Type'
													},
													singleView: {
														view: function(dom, obj, onComplete) {
															
															dom.makeNode('card', 'div', {css: 'ui fluid card'});
															dom.card.makeNode('content', 'div', {css: 'content'});
															dom.card.content.makeNode('header', 'div', {css: 'header', text:obj.name});
															dom.card.content.makeNode('hmeta', 'div', {css: 'meta', text:obj.type.name});
															
															dom.card.content.hmeta.makeNode('metatxt', 'div', {css:'right floated time', text:'<span>Created on:</span> '+ moment(obj.date_created).format('MM/DD/YY')});
																														
															dom.card.makeNode('con', 'div', {css: 'content'});
															dom.card.con.makeNode('subhead', 'div', {css: 'ui sub header', tag:'h4', text:'Manager'});
															dom.card.con.makeNode('label', 'div', {css: 'ui tiny lable', text:obj.manager.fname + obj.manager.lname});
															
															onComplete({
																dom: dom
															});
																														
														}
													},
													objectType: 'companies',
													where: {
														is_vendor: 0,
														childObjs: {
															date_created: true,
																manager: {
																	fname: true,
																	lname: true
																},
															name: true,
															type: {
																name: true
															},
															contact_info: {
																is_primary: true,
																info: true,
																type: {
																	data_type: true
																}
															},
															tax_exempt: true,
															is_vendor: true,
															markup_percent: true
														}
													}
												}
											});
											
										}
									});
									
								}	
							},
							{
								id:'tags',
								type:'tags',
								title:'Tags',
								icon:'<i class="fa fa-tags"></i>',
								color:'red',
								setup:{
									type:'companies',
									childObjs:2,
									resultList:tagResultsList
								}
							},
							{
								id:'create',
								type:'custom',
								title:'New Client',
								icon:'<i class="fa fa-plus"></i>',
								color:'green',
								dom:function(dom, state, draw){
									state.is_vendor = 0;
									createCompany(dom, state, draw, function(newCompany){

										sb.notify({
											type:'app-navigate-to',
											data:{
												itemId:'companies',
												viewId:{
													id:'single-'+newCompany.id,
													type:'table-single-item',
													title:newCompany.name,
													icon:'<i class="fa fa-user"></i>',
													setup:{
														objectType:'companies'
													},
													dom:singleState,
													rowObj:newCompany,
													removable:true,
													parent:'table'
												}
											}
										});
														
										createCompany(dom, state, draw);
										
										draw(dom);
										
									});
								}
							},
							{
								id:'settings',
								type:'settings',
								title:'Settings',
								icon:'<i class="fa fa-cog"></i>',
								setup:[
									{
										object_type:'contact_info_types',
										name:'1. Contact Info'
									},
									{
										object_type:'company_categories',
										name:'2. Client Types'
									}
								]
							}
						]
					}
				}
			});
			
			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'vendors',
						title:'Vendors',
						icon:'<i class="fa fa-truck"></i>',
						views:[
							{
								id:'table',
								default:true,
								type:'table',
								title:'All Vendors',
								icon:'<i class="fa fa-th-list"></i>',
								setup:{
									objectType:'companies',
									tableTitle:'<i class="fa fa-truck"></i> Vendors',
									childObjs:2,
									searchObjects:[
										{
											name:'Vendor Name',
											value:'name'
										}
									],
									filters:false,
									download:false,
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										}
									},
									rowSelection: false,
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.name;
										},
										action:singleState
									},
									visibleCols:{
										name:'Name',
										markup_percent:'Markup',
										type:'Type',
										date_created:'Date Created'
									},
									cells: {
										date_created:function(obj){
											return moment(obj.date_created).format('M/D/YYYY h:mm a');
										},
										name:function(obj){
											return obj.name;
										},
										type: function(obj) {

											if(obj.type !== null){
											
												return `${obj.type.name}`;
											
											}else{
												
												return 'Not selected';
												
											}
											
										},
										markup_percent:function(obj){
											
											if(obj.markup_percent !== undefined && obj.markup_percent !== `0` && obj.markup_percent !== null){
												return `<span class="pda-color-green">+${obj.markup_percent}%</span>`;
											}else{
												return `0%`;
											}
											
										}
									},
									data:function(paged, callback){
										
										sb.data.db.obj.getWhere('companies', {is_vendor: 1, childObjs: {
											date_created:true,
											manager:{
												fname:true,
												lname:true
											},
											name:true,
											type:{
												name:true
											},
											contact_info:{
												is_primary:true,
												info:true,
												type:{
													data_type:true
												}
											},
											tax_exempt:true,
											is_vendor:true,
											markup_percent:true
										}, paged: paged}, function(res){

											callback(res);
											
										});
										
									}
								}								
							},
							{
								id:'tags',
								type:'tags',
								title:'Tags',
								icon:'<i class="fa fa-tags"></i>',
								color:'red',
								setup:{
									type:'companies',
									childObjs:2,
									resultList:tagResultsList
								}
							},
							{
								id:'create-vendor',
								type:'custom',
								title:'New Vendor',
								icon:'<i class="fa fa-plus"></i>',
								color:'pda-btn-green',
								dom: function(dom, state, draw){
									state.is_vendor = 1;
									createCompany(dom, state, draw, function(newCompany){
			
										sb.notify({
											type:'app-navigate-to',
											data:{
												itemId:'vendors',
												viewId:{
													id:'single-'+newCompany.id,
													type:'table-single-item',
													title:newCompany.name,
													icon:'<i class="fa fa-user"></i>',
													setup:{
														objectType:'companies'
													},
													dom:singleState,
													rowObj:newCompany,
													removable:true,
													parent:'table'
												}
											}
										});
														
										createCompany(dom, state, draw);
										
										draw(dom);
										
									});
								}
							}
						]
					}
				}
			});

			sb.listen({
				'companyComponent-run':this.run
			});
			
		},
	
		run: function(data){data.run();}
					
	}
	
});