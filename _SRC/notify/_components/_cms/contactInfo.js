Factory.registerComponent("contact_info", function (sb) {
  var domObj = {},
    spread = 12,
    cachedData = {
      contactType: {},
      contactInfoTypes: {},
      info: [],
    },
    onScreen = false,
    components = {},
    ui = {},
    columnUI = {},
    modalUI = {},
    collapse = true,
    handleModal = false;

  function add(contactId, contactTypeId, infoType) {
    function isFirstOfItsType(type, otherTypes) {
      var ofSameType = _.filter(otherTypes, function (o) {
        return o.type.id === type.id;
      });

      if (ofSameType.length > 0) {
        return false;
      } else {
        return true;
      }
    }

    this.body.empty();
    this.footer.empty();

    this.body.makeNode("header", "headerText", {
      text: "Add New " + infoType.name,
    });

    this.body.makeNode("headerBreak", "lineBreak", { spaces: 1 });

    var infoObj = {};
    var formSetup = {
      is_primary: {
        type: "select",
        name: "is_primary",
        label: "Primary",
        options: [
          {
            value: "yes",
            name: "Yes",
            selected: true,
          },
          {
            value: "no",
            name: "No",
          },
        ],
      },
    };

    // if this is the first contact_info of its type on this obj, hide this choice and set to yes
    if (isFirstOfItsType(infoType, cachedData.info)) {
      formSetup.is_primary.type = "hidden";
      formSetup.is_primary.value = "yes";
    }

    switch (infoType.data_type) {
      case "address":
        break;

      case "account_info":
        formSetup.name = {
          type: "text",
          name: "name",
          label: "Account Name",
        };

        formSetup.info = {
          type: "text",
          name: "info",
          label: "Account Page",
        };

        formSetup.password = {
          type: "text",
          name: "password",
          label: "Password",
        };

        break;

      default:
        formSetup.contact_type = {
          type: "text",
          name: "info",
          label: infoType.name,
        };
    }

    if (infoType.data_type == "date") {
      formSetup.contact_type.type = "date";
      formSetup.contact_type.dateFormat = "M/D/YYYY";
    }

    this.body.makeNode("form", "form", formSetup);

    if (infoType.data_type === "address") {
      this.body.makeNode("br", "div", { text: "<br />" });
      sb.notify({
        type: "view-field",
        data: {
          type: "address",
          property: "address",
          obj: infoObj,
          isMetric: true,
          options: {
            edit: true,
            editing: true,
          },
          ui: this.body.makeNode("address", "div", {}),
        },
      });
    }

    this.footer.makeNode("btnsBreak", "lineBreak", { spaces: 1 });
    this.footer.makeNode("btns", "buttonGroup", { css: "" });
    this.footer.btns
      .makeNode("button", "button", {
        text: 'Save <i class="fa fa-floppy-o"></i>',
        css: "pda-btn-green",
      })
      .notify(
        "click",
        {
          type: "save-new-contact-info",
          data: {
            form: this.body.form,
            contactId: contactId,
            infoType: infoType,
            contactTypeId: contactTypeId,
            additionalInfo: infoObj,
          },
        },
        sb.moduleId
      );

    this.body.patch();
    this.footer.patch();
  }

  function addInfoType(dom, contactId, compData) {
    function addTypeToObject(infoType, contactId, compData) {
      this.body.table.body["row-" + infoType.id].btns.select.loading();

      var dom = this;

      sb.data.db.obj.getById("object", contactId, function (contactObj) {
        sb.data.db.obj.getById(
          "object",
          contactObj.type,
          function (contactType) {
            contactType.available_types.push(infoType.id);

            sb.data.db.obj.update(
              contactType.object_bp_type,
              {
                id: contactType.id,
                available_types: contactType.available_types,
              },
              function (done) {
                dom.hide();

                setTimeout(function () {
                  columnUI.ui(contactId, contactType.id);
                }, 500);
              }
            );
          }
        );
      });
    }

    this.body.empty();
    this.footer.empty();

    this.body.makeNode("header", "headerText", {
      text: "Add new info type",
      size: "small",
    });

    this.body.makeNode("headerBreak", "lineBreak", { spaces: 1 });

    this.body.makeNode("table", "table", {
      css: "table-hover table-condensed",
      columns: {
        name: "Choose a type to add",
        btns: "",
      },
    });

    _.each(
      compData.otherInfoTypes,
      function (iT) {
        this.body.table.makeRow("row-" + iT.id, ["", ""]);

        this.body.table.body["row-" + iT.id].name.makeNode(
          "name",
          "headerText",
          {
            text: iT.name + " <small>" + iT.data_type_name + "</small>",
            size: "x-small",
          }
        );

        this.body.table.body["row-" + iT.id].btns.makeNode("select", "button", {
          text: 'Select <i class="fa fa-arrow-right"></i>',
          css: "pda-btn-green",
        });

        this.body.table.body["row-" + iT.id].btns.select.notify(
          "click",
          {
            type: "contact-info-run",
            data: {
              run: addTypeToObject.bind(this, iT, contactId, compData),
            },
          },
          sb.moduleId
        );
      },
      this
    );

    this.footer.makeNode("btns", "buttonGroup", { css: "" });

    /*
		this.footer.btns.makeNode('add', 'button', {text:'<i class="fa fa-plus"></i> Create a new type', css:'pda-btn-green'}).notify('click', {
			type:'contact-info-run',
			data:{
				run:function(){
					
					this.hide();
					
					sb.notify({
						type:'app-navigate-to',
						data:{
							itemId:'contacts',
							viewId:'settings'
						}
					});
					
				}.bind(this)
			}
		}, sb.moduleId);
*/

    this.footer.btns
      .makeNode("cancel", "button", {
        text: '<i class="fa fa-times"></i> Close',
        css: "pda-btnOutline-red",
      })
      .notify(
        "click",
        {
          type: "contact-info-run",
          data: {
            run: function () {
              this.hide();
            }.bind(this),
          },
        },
        sb.moduleId
      );

    this.body.patch();
    this.footer.patch();

    //this.show();
  }

  function edit(contactId, contactTypeId, infoType, obj) {
    var dom = this;

    sb.data.db.obj.getWhere(
      "contact_info_options",
      { contact_info_type: infoType.id },
      function (options) {
        var infoObj = {};
        var formSetup = {
          is_primary: {
            type: "select",
            name: "is_primary",
            label: "Primary",
            options: [
              {
                value: "no",
                name: "No",
              },
            ],
          },
        };

        if (obj.is_primary == "yes") {
          formSetup.is_primary.options.push({
            value: "yes",
            name: "Yes",
            selected: true,
          });
        } else {
          formSetup.is_primary.options.push({
            value: "yes",
            name: "Yes",
            selected: false,
          });
        }

        switch (infoType.data_type) {
          case "address":
            break;

          case "account_info":
            formSetup.name = {
              type: "text",
              name: "name",
              label: "Account Name",
              value: obj.name,
            };

            formSetup.info = {
              type: "text",
              name: "info",
              label: "Account Page",
              value: obj.info,
            };

            formSetup.other = {
              type: "text",
              name: "other",
              label: "Password",
              value: obj.other,
            };
            break;

          default:
            if (infoType.is_select == "yes") {
              formSetup.info = {
                type: "select",
                name: "info",
                label: infoType.name,
                options: _.map(options, function (option) {
                  if (obj.info == option.name) {
                    return {
                      name: option.name,
                      value: option.name,
                      selected: true,
                    };
                  } else {
                    return {
                      name: option.name,
                      value: option.name,
                    };
                  }
                }),
              };
            } else {
              formSetup.contact_type = {
                type: "text",
                name: "info",
                label: infoType.name,
                value: obj.info,
              };
            }
        }

        dom.body.empty();

        dom.body.makeNode("header", "headerText", {
          text: "Edit " + infoType.name,
        });
        dom.body.makeNode("headerBreak", "lineBreak", { spaces: 1 });

        dom.body.makeNode("form", "form", formSetup);

        if (infoType.data_type === "address") {
          dom.body.makeNode("br", "div", { text: "<br />" });
          infoObj.address = {
            street: obj.street,
            add2: obj.street2,
            city: obj.city,
            state: obj.state,
            zip: obj.zip,
            country: obj.country,
          };

          sb.notify({
            type: "view-field",
            data: {
              type: "address",
              property: "address",
              obj: infoObj,
              isMetric: true,
              options: {
                edit: true,
                editing: true,
              },
              ui: dom.body.makeNode("address", "div", {}),
            },
          });
        }

        dom.footer.makeNode("break", "div", { text: "<br />" });

        // Action buttons
        dom.footer.makeNode("btns", "buttonGroup", { css: "pull-left" });

        dom.footer.btns
          .makeNode("button", "button", {
            text: 'Save <i class="fa fa-floppy-o"></i>',
            css: "pda-btn-green",
          })
          .notify(
            "click",
            {
              type: "update-contact-info",
              data: {
                form: dom.body.form,
                contactId: contactId,
                infoType: infoType,
                contactTypeId: contactTypeId,
                object: obj,
                additionalInfo: infoObj,
              },
            },
            sb.moduleId
          );

        dom.footer.btns
          .makeNode("close", "button", {
            text: 'Close <i class="fa fa-times"></i>',
            css: "pda-btnOutline-red",
          })
          .notify(
            "click",
            {
              type: "contact-info-run",
              data: {
                run: function () {
                  dom.hide();
                }.bind(dom),
              },
            },
            sb.moduleId
          );

        dom.footer.btns
          .makeNode("delete", "button", {
            css: "pda-btn-red",
            text: 'Delete <i class="fa fa-times"></i>',
          })
          .notify(
            "click",
            {
              type: "delete-contact-info",
              data: {
                form: dom.body.form,
                contactId: contactId,
                infoType: infoType,
                contactTypeId: contactTypeId,
                object: obj,
              },
            },
            sb.moduleId
          );

        dom.body.patch();
        dom.footer.patch();
      }
    );
  }

  function getSetupData(contactTypeId, contactId, supplied, callback) {
    var hitDB = false,
      needDb = false;

    if (supplied) {
      if (supplied.contactType) {
        cachedData.contactType = supplied.contactType;
      } else {
        hitDB = true;
      }

      if (supplied.contactInfoTypes) {
        cachedData.contactInfoTypes = supplied.contactInfoTypes;
      } else {
        hitDB = true;
      }

      if (supplied.info) {
        cachedData.info = supplied.info;
      } else {
        hitDB = true;
      }
    } else {
      hitDB = true;
    }

    if (hitDB) {
      needDb = true;
    }

    needDb = true;

    if (needDb) {
      var contact_info_types_queries = [
        {
          responseName: "allInfoTypes",
          table: "contact_info_types",
          query: {},
          childObjs: 1,
        },
        {
          responseName: "contactType",
          table: "contact_types",
          query: {
            id: parseInt(contactTypeId),
          },
          childObjs: 1,
        },
        {
          responseName: "companyType",
          table: "company_categories",
          query: {
            id: parseInt(contactTypeId),
          },
          childObjs: 1,
        },
      ];

      sb.data.db.service(
        "DataRepository",
        "get",
        contact_info_types_queries,
        function (contact_info_types_response) {
          var allInfoTypes = contact_info_types_response.allInfoTypes;
          var contactType;

          if (contact_info_types_response.contactType[0]) {
            contactType = contact_info_types_response.contactType[0];
          } else if (contact_info_types_response.companyType[0]) {
            contactType = contact_info_types_response.companyType[0];
          }

          if (contactType) {
            var contact_info_queries = [
              {
                responseName: "contactInfoTypes",
                table: "contact_info_types",
                query: {
                  id: {
                    type: "or",
                    values: _.pluck(contactType.available_types, "id"),
                  },
                },
                childObjs: 1,
              },
              {
                responseName: "info",
                table: "contact_info",
                query: {
                  object_id: parseInt(contactId),
                },
                childObjs: 1,
              },
            ];

            sb.data.db.service(
              "DataRepository",
              "get",
              contact_info_queries,
              function (contact_info_response) {
                var contactInfoTypes = contact_info_response.contactInfoTypes;
                var info = contact_info_response.info;
                var currentTypeIds = _.pluck(contactInfoTypes, "id");

                cachedData.contactType = contactType;
                cachedData.contactInfoTypes = contactInfoTypes;
                cachedData.info = info;
                cachedData.otherInfoTypes = _.reject(
                  allInfoTypes,
                  function (infoType) {
                    return currentTypeIds.indexOf(infoType.id) > -1;
                  }
                );

                callback(cachedData);
              }
            );
          } else {
            callback(false);
          }
        }
      );
    } else {
      callback(cachedData);
    }
  }

  function buildColumnUI(contactId, contactTypeId, suppliedData, updating) {
    if (!onScreen) {
      this.makeNode("panel", "container", {
        uiGrid: false,
        title: '<i class="fa fa-address-book-o"></i> Contact Info',
      }).makeNode("body", "container", { uiGrid: false });

      this.panel.body.makeNode("loading", "loader", {});

      onScreen = true;
    }

    this.patch();

    var dom = this;

    getSetupData(contactTypeId, contactId, suppliedData, function (compData) {
      if (compData === false) {
        delete dom.panel.body.loading;

        dom.panel.body.makeNode("noInfo", "headerText", {
          text: '<i class="fa fa-exclamation-circle"></i> Please choose a client type first',
          size: "x-small",
          css: "text-center",
        });

        dom.panel.body.patch();
      } else {
        var contactType = compData.contactType,
          contactInfoTypes = compData.contactInfoTypes,
          info = compData.info.filter(function (n) {
            return n != undefined;
          }),
          sortOrder = _.sortBy(contactInfoTypes, "name"),
          newInfoList = [];

        if (appConfig.instance == "thelifebook") {
          sortOrder = ["email", "cellphone", "phone", "address"];
        }

        delete dom.panel.body.loading;

        _.each(sortOrder, function (data_type) {
          _.each(contactInfoTypes, function (info) {
            if (
              info.data_type == data_type &&
              newInfoList.indexOf(info) == -1
            ) {
              newInfoList.push(info);
            }
          });
        });

        _.each(contactInfoTypes, function (info) {
          if (newInfoList.indexOf(info) == -1) {
            newInfoList.push(info);
          }
        });

        var count = 0;

        if (!_.isEmpty(newInfoList)) {
          _.each(
            newInfoList,
            function (infoType) {
              if (infoType) {
                dom.panel.body.makeNode("col-" + infoType.id, "column", {
                  width: spread,
                  css: "",
                });

                var infoList = _.where(info, { type: { id: infoType.id } });

                //var infoList = [];

                _.each(info, function (i) {
                  if (i.type.id == infoType.id) {
                    infoList.push(i);
                  }
                });

                // Find Where Solution
                var isPrimary = _.findWhere(infoList, { is_primary: "yes" });

                // Splicing isPrimary to the beginning of the array

                if (isPrimary) {
                  infoList.splice(0, 0, isPrimary);
                }

                dom.panel.body["col-" + infoType.id].makeNode("panel", "div", {
                  css: "ui stackable grid",
                  style:
                    "margin-top:10px !important; padding-top:20px; border-top:1px solid #ebebeb;",
                });

                dom.panel.body["col-" + infoType.id].panel.makeNode(
                  "headerLeft",
                  "div",
                  {
                    css: "fourteen wide column",
                    style: "padding:0 !important;",
                  }
                );

                dom.panel.body["col-" + infoType.id].panel.makeNode(
                  "headerRight",
                  "div",
                  {
                    css: "two wide column",
                    style: "padding:0 !important;",
                  }
                );

                dom.panel.body["col-" + infoType.id].panel.makeNode(
                  "content",
                  "div",
                  {
                    css: "sixteen wide column",
                    style: "margin-top:5px !important; padding:0 !important;",
                  }
                );

                dom.panel.body["col-" + infoType.id].panel.headerLeft.makeNode(
                  "header",
                  "headerText",
                  {
                    size: "xx-small",
                    text: infoType.name,
                  }
                );

                if (!_.isEmpty(infoList)) {
                  _.each(
                    infoList,
                    function (info) {
                      cssColor = "";
                      if (info.is_primary == "yes") {
                        cssColor = '<i class="fa fa-check"></i> ';
                      }

                      if (info.street != "") {
                        var addressObject = {
                          address: info,
                        };

                        dom.panel.body[
                          "col-" + infoType.id
                        ].panel.content.makeNode(
                          "youthleader-" + info.id,
                          "div",
                          {}
                        );

                        sb.notify({
                          type: "view-field",
                          data: {
                            type: "address",
                            property: "address",
                            obj: addressObject,
                            isMetric: true,
                            options: {
                              edit: false,
                              is_primary: info.is_primary,
                            },
                            ui: dom.panel.body[
                              "col-" + infoType.id
                            ].panel.content["youthleader-" + info.id].makeNode(
                              "address",
                              "div",
                              {
                                css: "pull-left",
                                style: "width:82%; margin-bottom:12px;",
                              }
                            ),
                          },
                        });

                        dom.panel.body["col-" + infoType.id].panel.content[
                          "youthleader-" + info.id
                        ]
                          .makeNode("map", "div", {
                            css: "pull-right",
                            style: "cursor:pointer!important;",
                            text: '<i class="fa fa-map"></i>',
                          })
                          .notify(
                            "click",
                            {
                              type: "view-on-google",
                              data: {
                                contactId: contactId,
                                infoType: infoType,
                                contactTypeId: contactTypeId,
                                object: info,
                              },
                            },
                            sb.moduleId
                          );

                        dom.panel.body["col-" + infoType.id].panel.content[
                          "youthleader-" + info.id
                        ]
                          .makeNode("edit", "div", {
                            css: "pull-right",
                            style:
                              "margin-right:0.5em!important; cursor:pointer!important;",
                            text: '<i class="fa fa-pencil"></i>',
                          })
                          .notify(
                            "click",
                            {
                              type: "edit-contact-info",
                              data: {
                                contactId: contactId,
                                infoType: infoType,
                                contactTypeId: contactTypeId,
                                object: info,
                              },
                            },
                            sb.moduleId
                          );
                      } else {
                        var infoText = info.info;

                        switch (info.type.data_type) {
                          case "account_info":
                            dom.panel.body[
                              "col-" + infoType.id
                            ].panel.content.makeNode(
                              "title-" + info.id,
                              "div",
                              {
                                text: info.name + "</a>",
                              }
                            );
                            dom.panel.body[
                              "col-" + infoType.id
                            ].panel.content.makeNode(
                              "youthleader-" + info.id,
                              "div",
                              {
                                text:
                                  "Account: " +
                                  info.info +
                                  "<br />Password: " +
                                  info.other +
                                  "</a>",
                              }
                            );
                            //this.panel.body['col-'+infoType.id].panel.makeNode('youthleader2-'+info.id, 'text', {text:'Password: '+ info.other});

                            dom.panel.body["col-" + infoType.id].panel.content[
                              "youthleader-" + info.id
                            ]
                              .makeNode("edit", "div", {
                                css: "pull-right",
                                text: '<i class="fa fa-pencil"></i>',
                                style:
                                  "margin-right:0.5em!important; cursor:pointer!important;",
                              })
                              .notify(
                                "click",
                                {
                                  type: "edit-contact-info",
                                  data: {
                                    contactId: contactId,
                                    infoType: infoType,
                                    contactTypeId: contactTypeId,
                                    object: info,
                                  },
                                },
                                sb.moduleId
                              );

                            dom.panel.body["col-" + infoType.id].panel.content[
                              "youthleader-" + info.id
                            ].makeNode("link", "div", {
                              css: "pull-right",
                              text:
                                ' <a href="' +
                                urlString +
                                '" target="_blank" style="text-decoration:underline;"><i class="fa fa-external-link"></i></a> ',
                            });

                            break;

                          case "website":
                            var urlString = "";
                            if (info.info.indexOf("http") > -1) {
                              urlString = info.info;
                            } else {
                              urlString = "http://" + info.info;
                            }

                            this.panel.body[
                              "col-" + infoType.id
                            ].panel.content.makeNode(
                              "youthleader-" + info.id,
                              "div",
                              {
                                style: "margin-bottom:5px",
                                text: cssColor + info.info + "</a>",
                              }
                            );

                            this.panel.body["col-" + infoType.id].panel.content[
                              "youthleader-" + info.id
                            ].makeNode("link", "div", {
                              css: "pull-right",
                              text:
                                ' <a href="' +
                                urlString +
                                '" target="_blank" style="text-decoration:underline;"><i class="fa fa-external-link"></i></a> ',
                            });

                            dom.panel.body["col-" + infoType.id].panel.content[
                              "youthleader-" + info.id
                            ]
                              .makeNode("edit", "div", {
                                css: "pull-right",
                                text: '<i class="fa fa-pencil"></i>',
                                style:
                                  "margin-right:0.5em!important; cursor:pointer!important;",
                              })
                              .notify(
                                "click",
                                {
                                  type: "edit-contact-info",
                                  data: {
                                    contactId: contactId,
                                    infoType: infoType,
                                    contactTypeId: contactTypeId,
                                    object: info,
                                  },
                                },
                                sb.moduleId
                              );

                            break;

                          default:
                            if (
                              info.type.data_type == "phone" ||
                              info.type.data_type == "cellphone"
                            ) {
                              infoText = sb.dom.formatPhone(info.info);
                            }

                            dom.panel.body["col-" + infoType.id].panel.content
                              .makeNode("text-" + info.id, "div", {
                                style: "margin-bottom:5px",
                                text: cssColor + infoText,
                              })
                              .makeNode("edit-" + info.id, "div", {
                                text: '<i class="fa fa-pencil"></i>',
                                css: "pull-right",
                                style:
                                  "margin:0 !important; cursor:pointer!important;",
                              })
                              .notify(
                                "click",
                                {
                                  type: "edit-contact-info",
                                  data: {
                                    contactId: contactId,
                                    infoType: infoType,
                                    contactTypeId: contactTypeId,
                                    object: info,
                                  },
                                },
                                sb.moduleId
                              );
                        }
                      }
                    },
                    dom
                  );
                } else {
                  dom.panel.body["col-" + infoType.id].panel.content.makeNode(
                    "title-" + info.id,
                    "text",
                    {
                      css: "field-placeholder",
                      text: "Empty",
                    }
                  );
                }

                dom.panel.body["col-" + infoType.id].panel.headerRight
                  .makeNode("button", "div", {
                    style:
                      "background-color: transparent; color: #18CC54; padding:0 !important; margin:0 !important; text-align:right;",
                    css: "fluid ui small icon link button",
                    text: '<i class="plus icon"></i>',
                  })
                  .notify(
                    "click",
                    {
                      type: "add-new-contact-info-button-click",
                      data: {
                        contactId: contactId,
                        infoType: infoType,
                        contactTypeId: contactTypeId,
                      },
                    },
                    sb.moduleId
                  );

                count++;

                delete dom.panel.body.loading;

                dom.panel.body.patch();
              }
            },
            dom
          );
        } else {
          dom.panel.body.makeNode("emptyMsg", "div", {
            css: "ui message",
            text: '<div class="header">No info types set on this contact type.</div><p>Go to <a href="https://bento.infinityhospitality.net/app/voltzsoftware#settings">Settings > Contacts > 2. Types of Contacts</a> to set the available types.</p>',
          });
        }

        dom.panel.body.patch();
      }
    });
  }

  function buildModalUI() {
    function add(contactId, contactTypeId, infoType) {
      function isFirstOfItsType(type, otherTypes) {
        var ofSameType = _.filter(otherTypes, function (o) {
          return o.type.id === type.id;
        });

        if (ofSameType.length > 0) {
          return false;
        } else {
          return true;
        }
      }

      this.body.empty();
      this.footer.empty();

      this.body.makeNode("header", "headerText", {
        text: "Add New " + infoType.name,
      });

      this.body.makeNode("headerBreak", "lineBreak", { spaces: 1 });

      var formSetup = {
        is_primary: {
          type: "select",
          name: "is_primary",
          label: "Primary",
          options: [
            {
              value: "yes",
              name: "Yes",
              selected: true,
            },
            {
              value: "no",
              name: "No",
            },
          ],
        },
      };

      // if this is the first contact_info of its type on this obj, hide this choice and set to yes
      if (isFirstOfItsType(infoType, cachedData.info)) {
        formSetup.is_primary.type = "hidden";
        formSetup.is_primary.value = "yes";
      }

      switch (infoType.data_type) {
        case "address":
          formSetup.street = {
            type: "text",
            name: "street",
            label: "Street",
          };

          formSetup.street2 = {
            type: "text",
            name: "street2",
            label: "Apartment/Unit Number",
          };

          formSetup.city = {
            type: "text",
            name: "city",
            label: "City",
          };

          formSetup.state = {
            type: "select",
            name: "state",
            label: "State",
            options: sb.data.stateArray,
          };

          formSetup.zip = {
            type: "text",
            name: "zip",
            label: "Postal/ZIP Code",
          };

          formSetup.country = {
            type: "select",
            name: "country",
            label: "Country",
            options: sb.data.countryArray,
          };

          break;

        case "account_info":
          formSetup.name = {
            type: "text",
            name: "name",
            label: "Account Name",
          };

          formSetup.info = {
            type: "text",
            name: "info",
            label: "Account Page",
          };

          formSetup.password = {
            type: "text",
            name: "password",
            label: "Password",
          };

          break;

        default:
          formSetup.contact_type = {
            type: "text",
            name: "info",
            label: infoType.name,
          };
      }

      if (infoType.data_type == "date") {
        formSetup.contact_type.type = "date";
        formSetup.contact_type.dateFormat = "M/D/YYYY";
      }

      this.body.makeNode("form", "form", formSetup);

      this.footer.makeNode("btnsBreak", "lineBreak", { spaces: 1 });

      this.footer.makeNode("btns", "buttonGroup", { css: "" });

      this.footer.btns
        .makeNode("button", "button", {
          text: 'Save <i class="fa fa-floppy-o"></i>',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "save-new-contact-info",
            data: {
              form: this.body.form,
              contactId: contactId,
              infoType: infoType,
              contactTypeId: contactTypeId,
            },
          },
          sb.moduleId
        );

      this.patch();

      this.show();
    }

    function addInfoType(dom, contactId, compData) {
      function addTypeToObject(infoType, contactId, compData) {
        this.body.table.body["row-" + infoType.id].btns.select.loading();

        var dom = this;

        sb.data.db.obj.getById("object", contactId, function (contactObj) {
          sb.data.db.obj.getById(
            "object",
            contactObj.type,
            function (contactType) {
              contactType.available_types.push(infoType.id);

              sb.data.db.obj.update(
                contactType.object_bp_type,
                {
                  id: contactType.id,
                  available_types: contactType.available_types,
                },
                function (done) {
                  columnUI.ui(contactId, contactType.id);

                  setTimeout(function () {
                    modalUI.state.add(contactId, contactType.id, infoType);
                  }, 500);
                }
              );
            }
          );
        });
      }

      this.body.empty();
      this.footer.empty();

      this.body.makeNode("header", "headerText", {
        text: "Add new info type",
        size: "small",
      });

      this.body.makeNode("headerBreak", "lineBreak", { spaces: 1 });

      this.body.makeNode("table", "table", {
        css: "table-hover table-condensed",
        columns: {
          name: "Choose a type to add",
          btns: "",
        },
      });

      _.each(
        compData.otherInfoTypes,
        function (iT) {
          this.body.table.makeRow("row-" + iT.id, ["", ""]);

          this.body.table.body["row-" + iT.id].name.makeNode(
            "name",
            "headerText",
            {
              text: iT.name + " <small>" + iT.data_type_name + "</small>",
              size: "x-small",
            }
          );

          this.body.table.body["row-" + iT.id].btns.makeNode(
            "select",
            "button",
            {
              text: 'Select <i class="fa fa-arrow-right"></i>',
              css: "pda-btn-green",
            }
          );

          this.body.table.body["row-" + iT.id].btns.select.notify(
            "click",
            {
              type: "contact-info-run",
              data: {
                run: addTypeToObject.bind(this, iT, contactId, compData),
              },
            },
            sb.moduleId
          );
        },
        this
      );

      this.footer.makeNode("btns", "buttonGroup", { css: "pull-left" });

      this.footer.btns
        .makeNode("add", "button", {
          text: '<i class="fa fa-plus"></i> Create a new type',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "contact-info-run",
            data: {
              run: function () {
                this.hide();

                sb.notify({
                  type: "app-navigate-to",
                  data: {
                    itemId: "contacts",
                    viewId: "settings",
                  },
                });
              }.bind(this),
            },
          },
          sb.moduleId
        );

      this.footer.btns
        .makeNode("cancel", "button", {
          text: '<i class="fa fa-times"></i> Close',
          css: "pda-btnOutline-red",
        })
        .notify(
          "click",
          {
            type: "contact-info-run",
            data: {
              run: function () {
                this.hide();
              }.bind(this),
            },
          },
          sb.moduleId
        );

      this.patch();

      this.show();
    }

    function edit(contactId, contactTypeId, infoType, obj) {
      this.body.empty();

      this.body.makeNode("header", "headerText", {
        text: "Edit " + infoType.name,
      });

      this.body.makeNode("headerBreak", "lineBreak", { spaces: 1 });

      var formSetup = {};

      var formSetup = {
        is_primary: {
          type: "select",
          name: "is_primary",
          label: "Primary",
          options: [
            {
              value: "no",
              name: "No",
            },
          ],
        },
      };

      if (obj.is_primary == "yes") {
        formSetup.is_primary.options.push({
          value: "yes",
          name: "Yes",
          selected: true,
        });
      } else {
        formSetup.is_primary.options.push({
          value: "yes",
          name: "Yes",
          selected: false,
        });
      }

      switch (infoType.data_type) {
        case "address":
          formSetup.street = {
            type: "text",
            name: "street",
            label: "Street",
            value: obj.street,
          };

          formSetup.street2 = {
            type: "text",
            name: "street2",
            label: "Apartment/Unit Number",
            value: obj.street2,
          };

          formSetup.city = {
            type: "text",
            name: "city",
            label: "City",
            value: obj.city,
          };

          formSetup.state = {
            type: "select",
            name: "state",
            label: "State",
            options: _.map(
              Object.assign({}, sb.data.stateArray),
              function (state) {
                if (state.value == obj.state) {
                  state.selected = true;
                } else {
                  state.selected = false;
                }

                return state;
              }
            ),
          };

          formSetup.zip = {
            type: "text",
            name: "zip",
            label: "Zip Code",
            value: obj.zip,
          };

          formSetup.country = {
            type: "select",
            name: "country",
            label: "Country",
            options: _.map(
              Object.assign({}, sb.data.countryArray),
              function (country) {
                if (country.value == obj.country) {
                  country.selected = true;
                } else {
                  country.selected = false;
                }

                return country;
              }
            ),
          };

          break;

        case "account_info":
          formSetup.name = {
            type: "text",
            name: "name",
            label: "Account Name",
            value: obj.name,
          };

          formSetup.info = {
            type: "text",
            name: "info",
            label: "Account Page",
            value: obj.info,
          };

          formSetup.other = {
            type: "text",
            name: "other",
            label: "Password",
            value: obj.other,
          };

          break;

        default:
          formSetup.contact_type = {
            type: "text",
            name: "info",
            label: infoType.name,
            value: obj.info,
          };
      }

      this.body.makeNode("form", "form", formSetup);

      this.footer.makeNode("btns", "buttonGroup", { css: "pull-left" });

      this.footer.btns
        .makeNode("button", "button", {
          text: 'Save <i class="fa fa-floppy-o"></i>',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "update-contact-info",
            data: {
              form: this.body.form,
              contactId: contactId,
              infoType: infoType,
              contactTypeId: contactTypeId,
              object: obj,
            },
          },
          sb.moduleId
        );

      this.footer.btns
        .makeNode("close", "button", {
          text: 'Close <i class="fa fa-times"></i>',
          css: "pda-btnOutline-red",
        })
        .notify(
          "click",
          {
            type: "contact-info-run",
            data: {
              run: function () {
                this.hide();
              }.bind(this),
            },
          },
          sb.moduleId
        );

      this.footer.btns
        .makeNode("delete", "button", {
          css: "pda-btn-red",
          text: 'Delete <i class="fa fa-times"></i>',
        })
        .notify(
          "click",
          {
            type: "delete-contact-info",
            data: {
              form: this.body.form,
              contactId: contactId,
              infoType: infoType,
              contactTypeId: contactTypeId,
              object: obj,
            },
          },
          sb.moduleId
        );

      this.patch();

      this.show();
    }

    function deleting() {
      this.footer.btns.makeNode("delete", "button", {
        css: "btn-danger",
        text: 'Deleting <i class="fa fa-circle-o-notch fa-spin"></i>',
      });

      this.footer.patch();
    }

    function loading() {
      this.footer.btns.makeNode("button", "button", {
        text: 'Saving <i class="fa fa-circle-o-notch fa-spin"></i>',
      });

      this.footer.patch();
    }

    this.state = {};

    this.state.add = add.bind(this);
    this.state.addType = addInfoType.bind(this);
    this.state.edit = edit.bind(this);
    this.state.deleting = deleting.bind(this);
    this.state.loading = loading.bind(this);
  }

  function processForm(formData, infoType, additionalInfo) {
    var newObj = {},
      streetTwo = "";

    newObj.is_primary = formData.fields.is_primary.value;

    switch (infoType.data_type) {
      case "address":
        newObj.street = additionalInfo.address.street;
        newObj.street2 = additionalInfo.address.add2;
        newObj.city = additionalInfo.address.city;
        newObj.state = additionalInfo.address.state;
        newObj.zip = additionalInfo.address.zip;
        newObj.country = additionalInfo.address.country;

        newObj.info =
          newObj.street +
          streetTwo +
          ", " +
          newObj.city +
          ", " +
          newObj.state +
          " " +
          newObj.zip +
          " " +
          newObj.country;

        // Validate completeness
        if (
          _.isEmpty(newObj.street) &&
          _.isEmpty(newObj.street2) &&
          _.isEmpty(newObj.city) &&
          _.isEmpty(newObj.state) &&
          _.isEmpty(newObj.zip)
        ) {
          sb.dom.alerts.alert(
            "Wait!",
            "Please input some info before saving.",
            "error"
          );
          return false;
        }

        break;

      case "account_info":
        newObj.info = formData.fields.info.value;
        newObj.other = formData.fields.password.value;
        newObj.name = formData.fields.name.value;
        if (_.isEmpty(newObj.info)) {
          sb.dom.alerts.alert(
            "Wait!",
            "Please input some info before saving.",
            "error"
          );
          return false;
        }
        break;

      default:
        newObj.info = formData.fields.info.value;

        if (_.isEmpty(newObj.info)) {
          sb.dom.alerts.alert(
            "Wait!",
            "Please input some info before saving.",
            "error"
          );
          return false;
        }
    }

    return newObj;
  }

  function passOffPrimary(oldPrimary, callback) {
    var typeId = oldPrimary.type.id;
    if (!typeId) {
      typeId = oldPrimary.type;
    }

    sb.data.db.obj.getWhere(
      "contact_info",
      { object_id: oldPrimary.object_id, type: typeId },
      function (transferPrimaryToList) {
        var notOldPrimaries = _.filter(transferPrimaryToList, function (o) {
          return o.id !== oldPrimary.id;
        });

        if (notOldPrimaries.length > 0) {
          var newPrimary = notOldPrimaries[0];
          newPrimary.is_primary = "yes";

          sb.data.db.obj.update(
            "contact_info",
            {
              id: newPrimary.id,
              is_primary: newPrimary.is_primary,
            },
            function (response) {
              callback(response);
            }
          );
        } else if (transferPrimaryToList.length > 0) {
          var newPrimary = transferPrimaryToList[0];
          newPrimary.is_primary = "yes";

          sb.data.db.obj.update(
            "contact_info",
            {
              id: newPrimary.id,
              is_primary: newPrimary.is_primary,
            },
            function (response) {
              callback(response);
            }
          );
        } else {
          callback();
        }
      }
    );
  }

  return {
    init: function () {
      sb.listen({
        "start-contact_info-component": this.start,
      });
    },

    start: function (data) {
      if (data.hasOwnProperty("collapse")) {
        collapse = data.collapse;
      }

      sb.listen({
        "add-new-contact-info-button-click": this.addNew,
        "contact-info-run": this.run,
        "delete-contact-info": this.deleteObject,
        "destroy-contact_info-component": this.destroy,
        "edit-contact-info": this.edit,
        "save-new-contact-info": this.save,
        "show-contact-info-column": this.showColumn,
        "update-contact-info": this.update,
        "view-on-google": this.viewOnGoogleMap,
      });

      ui = sb.dom.make(data.domObj.selector);

      columnUI = ui.makeNode("info", "column", { width: 12 });
      columnUI.ui = buildColumnUI;

      ui.build();
    },

    destroy: function () {
      sb.listen({
        "start-contact_info-component": this.start,
      });

      _.each(components, function (comp) {
        comp.destroy();
      });

      domObj = {};
      components = {};
    },

    addNew: function (data) {
      modalUI = ui
        .makeNode("modals", "container", { uiGrid: false })
        .makeNode("infoModal", "modal", {
          onShow: function () {
            modalUI.body.empty();
            modalUI.body.patch();
            add.call(
              ui.modals.infoModal,
              data.contactId,
              data.contactTypeId,
              data.infoType
            );
          },
        });

      ui.build();

      ui.modals.infoModal.show();
    },

    deleteObject: function (data) {
      function onComplete(response) {
        // update ui
        sb.notify({
          type: "contact-object-updated",
          data: {
            contactId: data.contactId,
            modal: modalUI,
            alert: true,
          },
        });
      }

      var wasPrimary = data.object.is_primary == "yes";

      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "",
        },
        function (resp) {
          swal.disableButtons();

          if (resp) {
            sb.data.db.obj.getById(
              "contacts",
              data.object.object_id,
              function (contact) {
                contact.contact_info = _.reject(
                  contact.contact_info,
                  function (o) {
                    return o == data.object.id;
                  }
                );

                sb.data.db.obj.update(
                  "contacts",
                  { id: contact.id, contact_info: contact.contact_info },
                  function (updated) {
                    sb.data.db.obj.erase(
                      "contact_info",
                      data.object.id,
                      function (done) {
                        if (!handleModal) {
                          modalUI.hide();
                          sb.dom.alerts.alert("Deleted", "", "success");
                        }

                        columnUI.ui(data.contactId, data.contactTypeId);

                        // if primary info obj was deleted,
                        if (wasPrimary) {
                          passOffPrimary(data.object, function (response) {
                            onComplete(response);
                          });
                        } else {
                          onComplete(done);
                        }
                      }
                    );
                  }
                );
              }
            );
          }
        }
      );
    },

    edit: function (data) {
      modalUI = ui
        .makeNode("modals", "container", { uiGrid: false })
        .makeNode("infoModal", "modal", {
          onShow: function () {
            modalUI.body.empty();
            modalUI.body.patch();
            edit.call(
              ui.modals.infoModal,
              data.contactId,
              data.contactTypeId,
              data.infoType,
              data.object
            );
          },
        });

      ui.build();

      ui.modals.infoModal.show();
    },

    run: function (data) {
      data.run();
    },

    save: function (data) {
      var formData = data.form.process();
      var newObj = processForm(formData, data.infoType, data.additionalInfo);

      if (newObj) {
        ui.modals.infoModal.footer.btns.makeNode("button", "button", {
          text: 'Saving <i class="fa fa-circle-o-notch fa-spin"></i>',
        });
        ui.modals.infoModal.footer.btns.patch();

        newObj.object_id = data.contactId;
        newObj.type = data.infoType.id;
        newObj.title = data.infoType.name;

        sb.data.db.obj.create("contact_info", newObj, function (done) {
          sb.data.db.obj.getById(
            "contacts",
            data.contactId,
            function (contact) {
              if (!contact.contact_info) {
                contact.contact_info = [];
              }

              contact.contact_info.push(done.id);

              sb.data.db.obj.update(
                "contacts",
                { id: contact.id, contact_info: contact.contact_info },
                function (updated) {
                  if (newObj.is_primary == "yes") {
                    sb.data.db.obj.getWhere(
                      "contact_info",
                      {
                        is_primary: "yes",
                        object_id: data.contactId,
                        type: data.infoType.id,
                      },
                      function (curPrimary) {
                        if (curPrimary.length > 0) {
                          var update = [];

                          _.each(curPrimary, function (o) {
                            if (o.id != done.id) {
                              update.push({
                                id: o.id,
                                is_primary: "no",
                              });
                            }
                          });

                          var total = update.length,
                            count = 0;

                          if (total == 0) {
                            if (!handleModal) {
                              modalUI.hide();
                            }

                            columnUI.ui(data.contactId, data.contactTypeId);

                            sb.notify({
                              type: "contact-object-updated",
                              data: {
                                contactId: data.contactId,
                                modal: modalUI,
                              },
                            });
                          }

                          _.each(update, function (u) {
                            sb.data.db.obj.update(
                              "contact_info",
                              u,
                              function (updated) {
                                count++;

                                if (i == count) {
                                  if (!handleModal) {
                                    modalUI.hide();
                                  }

                                  columnUI.ui(
                                    data.contactId,
                                    data.contactTypeId
                                  );

                                  sb.notify({
                                    type: "contact-object-updated",
                                    data: {
                                      contactId: data.contactId,
                                      modal: modalUI,
                                    },
                                  });
                                }
                              }
                            );
                          });
                        }
                      }
                    );
                  } else {
                    if (!handleModal) {
                      modalUI.hide();
                    }

                    columnUI.ui(data.contactId, data.contactTypeId);

                    sb.notify({
                      type: "contact-object-updated",
                      data: {
                        contactId: data.contactId,
                        modal: modalUI,
                      },
                    });
                  }
                }
              );
            }
          );
        });
      }
    },

    showColumn: function (data) {
      if (data.spread) {
        spread = data.spread;
      }

      if (data.handleModal) {
        handleModal = true;
      }

      onScreen = false;

      columnUI.ui(data.contactId, data.contactTypeId, data.compData);
    },

    update: function (data) {
      var formData = data.form.process();
      var newObj = processForm(formData, data.infoType, data.additionalInfo);

      if (newObj) {
        ui.modals.infoModal.footer.btns.makeNode("button", "button", {
          text: 'Saving <i class="fa fa-circle-o-notch fa-spin"></i>',
        });
        ui.modals.infoModal.footer.btns.patch();

        var wasPrimary = data.object.is_primary == "yes";

        newObj.id = data.object.id;
        newObj.object_id = data.contactId;
        newObj.type = data.infoType.id;

        cachedData = {
          contactType: {},
          contactInfoTypes: {},
          info: [],
        };

        sb.data.db.obj.update("contact_info", newObj, function (done) {
          if (newObj.is_primary == "yes") {
            sb.data.db.obj.getWhere(
              "contact_info",
              {
                is_primary: "yes",
                object_id: data.contactId,
                type: data.infoType.id,
              },
              function (curPrimary) {
                if (curPrimary.length > 0) {
                  var update = [];

                  _.each(curPrimary, function (o) {
                    if (o.id != data.object.id) {
                      update.push({
                        id: o.id,
                        is_primary: "no",
                      });
                    }
                  });

                  var total = update.length,
                    count = 0;

                  if (total == 0) {
                    if (!handleModal) {
                      modalUI.hide();
                    }

                    columnUI.ui(data.contactId, data.contactTypeId);

                    sb.notify({
                      type: "contact-object-updated",
                      data: {
                        contactId: data.contactId,
                        modal: modalUI,
                      },
                    });
                  }

                  _.each(update, function (u) {
                    sb.data.db.obj.update(
                      "contact_info",
                      u,
                      function (updated) {
                        count++;

                        if (i == count) {
                          if (!handleModal) {
                            modalUI.hide();
                          }

                          columnUI.ui(data.contactId, data.contactTypeId);

                          sb.notify({
                            type: "contact-object-updated",
                            data: {
                              contactId: data.contactId,
                              modal: modalUI,
                            },
                          });
                        }
                      }
                    );
                  });
                }
              }
            );
          } else if (newObj.is_primary == "no" && wasPrimary) {
            passOffPrimary(newObj, function (response) {
              columnUI.ui(data.contactId, data.contactTypeId);

              sb.notify({
                type: "contact-object-updated",
                data: {
                  contactId: data.contactId,
                  modal: modalUI,
                },
              });
            });
          } else {
            if (!handleModal) {
              modalUI.hide();
            }

            columnUI.ui(data.contactId, data.contactTypeId);

            sb.notify({
              type: "contact-object-updated",
              data: {
                contactId: data.contactId,
                modal: modalUI,
              },
            });
          }
        });
      }
    },

    viewOnGoogleMap: function (data) {
      var streetTwo = "";

      if (data.object.street2 != "") {
        streetTwo = " " + data.object.street2;
      }

      window.open(
        "https://maps.google.com/?q=" +
          data.object.street +
          streetTwo +
          ", " +
          data.object.city +
          ", " +
          data.object.state +
          " " +
          data.object.zip,
        "_blank"
      );
    },
  };
});
