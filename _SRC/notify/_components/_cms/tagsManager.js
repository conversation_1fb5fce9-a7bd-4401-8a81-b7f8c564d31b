Factory.register('tags-manager', function(sb){
	
    var onComplete;
	var sizes = ['tiny', 'small', 'medium', 'large', 'huge', 'massive']; // list of fomantic-UI sizes
	var TagsCollectionsSetup = {};

    function createNewTag(ui, state, newObject, callback){

		function createTag(tag, clearForm) {
			sb.data.db.obj.create('system_tags', tag, function(resp) {
				clearForm();
				callback(resp);
			});	
		}

		ui.makeNode('wrapper', 'div', {});

		ui.wrapper.makeNode('modal', 'modal', {

			onShow: function() {

				function updateTag(tag) {

					ui.optionsWrapper.empty();

					if (tag.icon) systemTag.icon = tag.icon;

					if (tag.color) systemTag.color = tag.color;

					ColorPalette(ui.optionsWrapper, systemTag, function(updatedTag) {

						if (updatedTag) updateTag(updatedTag);
						
					}, false);
					
					ui.optionsWrapper.patch();

				}

				var formObj = {
					tag:{
						name:'tag',
						label:'Tag Name',
						type:'text',
						fieldCSS:'ui massive fluid input'
					}
				};

				ui.makeNode('title', 'div', {text:'Create a new Tag', css:'ui huge header'});
				
				ui.makeNode('form', 'form', formObj);

				ui.form.patch();
				
				ui.makeNode('formBreak', 'div', {text:'<br />'});

				ui.makeNode('optionsWrapper', 'div', {});

				var systemTag = {
					tag:'',
					tagId:null,
					color: 'bento-blue',
					icon: 'tags',
					name: '',
					type: 'system_tags',
					object_bp_type:"system_tags",
					instance: "rickyvoltz"
				};

				updateTag(systemTag);

				ui.makeNode('submit', 'div', {css:'ui green big button', text:'Save'})
				.notify('click', {
					type:'formBuilder-run',
					data:{
						run:function(ui, obj, systemTag){

							systemTag.tag = ui.form.process().fields.tag.value;

							createTag(systemTag, () => { ui.hide(); });
							
						}.bind({}, ui, formObj, systemTag)
						
					}

				}, sb.moduleId);
			
				ui.patch();

			}

		});

		ui.patch();

		ui.wrapper.modal.show();

	}

    function createTagGrid(ui, obj, callback) {

        onComplete = callback;

		ui.empty();

		ui.makeNode('grid', 'div', {
			css: 'ui grid'
		});

		ui.grid.makeNode('col1', 'div', {
			css: 'two wide column'
		});
		ui.grid.makeNode('col2', 'div', {
			css: 'ten wide column'
		});

		// default color to bento blue for the background if one does not exist
		if (_.isEmpty(obj.color) || obj.color.charAt(0) === '#') {
			/* obj.color = 'bento-blue'; */
			obj.color = 'grey';
		}
		createIcon(ui.grid.col1, obj.icon, obj.color, 'circular', 'large');
		
		ui.grid.col2.makeNode('tagNameWrapper', 'div', {
			css: 'ui relaxed horizontal list'
		});

		ui.grid.col2.tagNameWrapper.makeNode('item1', 'div', {
			css: 'item'
		});
		ui.grid.col2.tagNameWrapper.item1.makeNode('tagname', 'div', {
			text: obj.tag || '',
			tag: 'span',
			css: 'ui large text text-bold'
		});

		if (!obj.is_deleted) {

			ui.grid.col2.tagNameWrapper.makeNode('item2', 'div', {
				css: 'item'
			});
			ui.grid.col2.tagNameWrapper.item2.makeNode('editBtn', 'div', {
				text: '(Edit)',
				tag: 'span',
				css: 'ui medium text link'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data: {
						run: function(data) {
							createTagSettings(ui, obj);
						}
					}
					, ui: ui
					, obj: obj
				}, sb.moduleId);
			
		}

		ui.patch();
	}

    function createTagSettings(ui, obj) {
		function createTagIconEdit(column) {

			function createClickableIcon(obj) {

				var list = column.grid.row.list;
				
				list.empty();

				list.makeNode('item1', 'div', {
					css: 'item',
					style: 'padding-right:0!important;padding-left: 10px;'
				});

				list.makeNode('item2', 'div', {
					css: 'item'
				});

				createIcon(list.item1, obj.icon, obj.color, 'circular');

				list.item1.iconwrapper.icon.notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function(data) {
							
							ui.modal.empty();
							
							ColorPalette(ui.modal, obj, function(updatedTag) {
	
								if (updatedTag) {

									createClickableIcon(updatedTag);

								}
								
							});
							
							ui.modal.show();
							
						},
						obj:obj,
						ui:ui
	
					}
	
				}, sb.moduleId);

				list.item2.makeNode('iconEditColor', 'div', {
					css: 'ui text link',
					text: '( Edit )'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
							run: function(data) {
								
								ui.modal.empty();
								
								ColorPalette(ui.modal, obj, function(updatedTag) {
									
									if (updatedTag) {

										createClickableIcon(updatedTag);
										
									}
									
								});
								
								ui.modal.show();
								
							},
							obj:obj,
							ui:ui

						}

					}, sb.moduleId);


				list.patch();

			}

			column.makeNode('grid', 'div', {
				css: 'ui center aligned grid'
			});
			column.grid.makeNode('row', 'div', {
				css: 'row'
			});
			column.grid.row.makeNode('list', 'div', {
				css: 'ui relaxed list'
			});
			
			createClickableIcon(obj);
			
			ui.patch();

		}

		function createTagNameEdit(column) {
			
			column.makeNode('tagEditWrapper', 'div', { css: 'ui form' });

			column.tagEditWrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
			column.tagEditWrapper.makeNode('row1', 'div', { css: 'row' });

			column.tagEditWrapper.makeNode('lb_2', 'lineBreak', {spaces: 2});
			column.tagEditWrapper.makeNode('row2', 'div', { css: 'row' });

			column.tagEditWrapper.makeNode('lb_3', 'lineBreak', {spaces: 1});
			column.tagEditWrapper.makeNode('row3', 'div', { css: 'row' });

			column.tagEditWrapper.makeNode('lb_4', 'lineBreak', {spaces: 1});
			column.tagEditWrapper.makeNode('row4', 'div', { css: 'row' });

			
			column.tagEditWrapper.row1.makeNode('tagNameHeader', 'div', {
				tag: 'h3',
				text: '<span class="ui large text">TAG SETTINGS</span>'
			});
			
			column.tagEditWrapper.row2.makeNode('tagNameInputLabel', 'div', {
				text: 'Tag Name (Click to edit name)'
			});
			
			var tagNameColumn = column.tagEditWrapper.row3;
			tagNameColumn.makeNode('tagNameWrapper', 'div', { css: 'field' });
			tagNameColumn.tagNameWrapper.makeNode('tagNameInput', 'div', {});
			
			sb.notify({
				type: 'view-field',
				data: {
					type: 'title',
					property: 'tag',
					obj: obj,
					ui: tagNameColumn.tagNameWrapper.tagNameInput,
					options: {
						fluid:true,
						edit: true
					}
				}
			});

			createConsolidateBtn(column.tagEditWrapper.row4);
			
		}

		function createTagBack(column) {
			
			column.makeNode('editwrapper', 'div', {
				css:'right floated column'
			});
			column.editwrapper.makeNode('closeBtn', 'div', {
				css: 'big ui button right floated',
				text: 'Close'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data: {
					run: function(data) {
						
						$(column.editwrapper.closeBtn.selector).addClass('loading');
						
						sb.data.db.obj.getById('', obj.id, function(resp) {
							
							obj = resp;
							
							createTagGrid(ui, resp);

							
						}, {
							color: true
						});

					}
				}
			}, sb.moduleId);
		}
		
		function createConsolidateBtn(column) {
			
			column.makeNode('consolidateWrapper', 'div', {
				css:'left floated column'
			});
			
			column.consolidateWrapper.makeNode( 'link', 'div', {
				tag: 'span',
				text: '<i class="plus icon"></i> Consolidate Tags',
				css: 'ui button'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data: {
					run: function() {
						
						createConsolidateModal(ui.modal, obj);
						
						ui.modal.show();
						
					}
				}
			}, sb.moduleId);
		}

		function createConsolidateModal(ui, obj) {

			function createSearchArea(ui, obj) {

				function updateSelectedTags() {
	
					var tagsList = [];
	
					selectedTagsList.forEach((tag) => {
						tagsList.push(tag.tag);
					});
					
					ui.tagsWrapper.tagsToDelete.empty();
	
					ui.tagsWrapper.tagsToDelete.makeNode('lb_1_delete', 'lineBreak', {spaces: 1});
	
					ui.tagsWrapper.tagsToDelete.makeNode('tagsContainer', 'div', {
						css: 'ui labels'
					});
	
					var tagsContainer = ui.tagsWrapper.tagsToDelete.tagsContainer;
					
					selectedTagsList.forEach((tag) => {
	
						var icon = tag.icon || 'hashtag';
						var color = tag.color || 'red';
						var iconText = `
							<i class="${icon} icon" style="margin-right:0"></i>
							${tag.tag}
							<i class="delete icon"></i>`;
						var cssText = `ui large ${color} label`;
	
						tagsContainer.makeNode('tag-'+tag.id, 'div', {
							text: iconText,
							css: cssText
						}).notify('click', {
							type: [sb.moduleId+'-run'],
							data: {
								run: function() {

									removeSelectedTag(tag.id);
	
								}
							}
						});
	
					});
	
					ui.tagsWrapper.tagsToDelete.patch();
	
				}
	
				function removeSelectedTag(id) {
	
					$('#loader').fadeIn();
	
					selectedTagsList = selectedTagsList.filter((tag) => tag.id !== id);
	
					updateSelectedTags();
	
					$('#loader').fadeOut();
	
				}
	
				ui.makeNode('add', 'div', {});
		
				ui.add.makeNode('text', 'div', {
					tag: 'span',
					text: '<h4>Add Tags </h4>'
				});
				
				ui.add.makeNode('lb_1_add', 'lineBreak', {spaces: 1});
		
				var text = `
					<div class="ui left icon input fluid" style="width:100%">
						<input class="prompt" type="text" placeholder="Search tags..">
						<i class="search icon"></i>
					</div>
					<div class="results"></div>`;
		
				var searchNode = ui.add.makeNode('add', 'div', {
					css: 'ui dropdown search',
					style: 'min-width:40%; padding-right:0px; border:1px solid #ebebeb; border-radius:0.375rem;',
					text: text,
					listener:{
						type: 			'search',
						objectType: 	'Tag',
						onResultsOpen: function() {
		
							setTimeout(function() {
								$('.floater-container').css({'overflow':'visible'});
							}, 100);
		
							var element = $(this[0]);
		
							function setTagDropdownHeight(element) {
								var parentElement = element.parent();
								var dropdownHeight = $(window).height() - parentElement.offset().top - parentElement[0].offsetHeight - 25;
								dropdownHeight = (dropdownHeight < 450) ? dropdownHeight : 450;
								var rightOffset = element.outerWidth() - ($(window).width() - (parentElement.offset().left + parentElement.outerWidth()));
								element.css({'max-height':dropdownHeight, 'left':'-'+rightOffset+'px'});
							}
		
							$(window).on('resize', function() {
								setTagDropdownHeight(element);
							});
		
							window.addEventListener('scroll', function() {
								setTagDropdownHeight(element);
							}, true);
		
							setTagDropdownHeight(element);
		
						},
						onResultsClose: function() {
		
							setTimeout(function() {
								$('.floater-container').css({'overflow-y':'scroll', 'overflow-x':'auto'});
							}, 100);
		
						},
						onSelect: function(result, response) {
		
							if (!_.isEmpty(result) && !selectedTagsList.find(tag=>tag.id===result.id)) {
		
								searchNode.loading();
			
								$('#loader').fadeIn();
		
								searchNode.search('set value', '');
								
								sb.data.db.obj.getById(result.object_bp_type, result.id, function(tag) {
		
									selectedTagsList.push(tag);
										
									updateSelectedTags();
		
									searchNode.loading(false);
		
									$('#loader').fadeOut();
									
								}, 1);
								
							}
							
						},
						category: 		'group_type',
						onResponse: 	function(raw) {
		
							var response = {
								results : {}
							};
		
							var searchString = $(searchNode.selector).search('get value');
		
							// add placeholder for creation of system tags
							if (
								!_.contains( 
									_.pluck(raw.results, 'tag')
									, searchString 
								)
								&&
								searchString.charAt(0) !== ':'
								&&
								searchString.charAt(0) !== '@'
								&&
								searchString.charAt(0) !== '#'
							) {
							   
							   raw.results.push({
									id: null,
									name: searchString,
									tag: searchString
								});
								
							}
		
							// Remove tags that are already selected and the primary tag
							raw.results = _.filter(raw.results, function(tag) {
								var contains = selectedTagsList.find(selected => selected.id === tag.id);
								return !contains && tag.id !== obj.id;
							});
							
							_.each(raw.results.reverse(), function(tag) {
		
								var tagDisplayData = getTagDisplayData(tag, true);
		
								tag = tagDisplayData.tag;
								var categoryName = tagDisplayData.category.name
		
								if (tag.object_bp_type ==="system_tags") {
		
									if (
										response.results[tag.object_bp_type + '-' + tag.group_type]
									) {
		
										response.results[tag.object_bp_type + '-' + tag.group_type].results.push(tag);							
										
									} else {
										
										response.results[tag.object_bp_type + '-' + tag.group_type] = {
											name : categoryName,
											results : [tag]
										};
										
									} 
								}
								
							});
							
							return response;
						}
					}
				});
		
				ui.makeNode('lb_1_2', 'lineBreak', {spaces: 1});
		
				ui.makeNode('warningWrapper', 'div', {
					css: 'row'
				});
		
				ui.warningWrapper.makeNode('warningP', 'div', {
					tag: 'p'
				});
		
				ui.warningWrapper.warningP.makeNode('warning', 'div', {
					tag: 'span',
					text: '<h3><strong>Warning!<br>The following tags will be permanently updated:</strong></h3>',
					css: 'ui text'
				});
		
				ui.makeNode('tagsWrapper', 'div', {
					css: 'row'
				});

				ui.makeNode('lb_1_3', 'lineBreak', {spaces: 1});

				ui.tagsWrapper.makeNode('tagsToDelete', 'div', {});
		
				ui.tagsWrapper.tagsToDelete.makeNode('text', 'div', {});

				ui.makeNode('lb_1_4', 'lineBreak', {spaces: 1});
		
				ui.patch();
		
			}
			
			var selectedTagsList = [];

			ui.empty();

			ui.makeNode('title', 'div', {text:'Tag Manager', css:'ui huge header'});

			ui.makeNode('lb_1', 'lineBreak', {spaces: 1});

			createSearchArea(ui, obj);

			ui.makeNode('updateTextWrapper', 'div', {
				css: 'row'
			});

			ui.makeNode('checkboxWrapper', 'div', {
				css: 'row '
			});

			ui.makeNode('lb_3', 'lineBreak', {spaces: 2});

			ui.checkboxWrapper.makeNode('checkboxDiv', 'div', { 
				css: 'row'
			});

			ui.checkboxWrapper.checkboxDiv.makeNode('checkboxTitleWrapper', 'div', {
				css: 'row'
			});

			ui.checkboxWrapper.checkboxDiv.checkboxTitleWrapper.makeNode('checkboxTitle', 'div', {
				type: 'span',
				css: 'ui text',
				text: '<h3><strong>Please choose a Consolidation Action:</strong></h3></span>'
			});

			ui.checkboxWrapper.checkboxDiv.makeNode('lb_1_checkbox', 'lineBreak', {spaces: 1, style: 'font-size: 8px'});

			ui.checkboxWrapper.checkboxDiv.makeNode('checkboxFormWrapper', 'div', {
				css: 'row'
			});

			var objColor = obj.color || 'grey';
			var objIcon = obj.icon || 'hashtag';

			var formArgs = {
				viewSwitch:{
					type:'radio',
					name:'viewSwitch',
					label:'',
					options:[{
						name:`<strong>Replace all selected tags with <span class="ui ${objColor} label"><i class="${objIcon} icon" style="margin-right:0"></i> ${obj.tag}</span></strong>`,
						value:'replace'
					}, {
						name:`<strong>Delete all selected tags and replace with <span class="ui ${objColor} label"><i class="${objIcon} icon" style="margin-right:0"></i> ${obj.tag}</span></strong>`,
						value:'delete'
					}]
				}
			};

			formArgs.viewSwitch.value = 'replace';
			
			ui.checkboxWrapper.checkboxDiv.checkboxFormWrapper.makeNode('form', 'form', formArgs);

			ui.makeNode('lb_5', 'lineBreak', {spaces: 1});

			ui.makeNode('btnsWrapper', 'div', {
				css: 'row'
			});

			ui.btnsWrapper.makeNode('btnsList', 'div', {
				css: 'ui relaxed horizontal list right floated content'
			});
			
			ui.btnsWrapper.btnsList.makeNode('btn_group', 'div', {
				css: 'item'
			});

			ui.btnsWrapper.btnsList.btn_group.makeNode('closeBtn', 'div', {
				tag: 'button',
				text: 'Close',
				css: 'ui button'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data:{
					run:function(ui){

						ui.hide();

					}.bind({}, ui)
				}
			}, sb.moduleId);

			ui.btnsWrapper.btnsList.btn_group.makeNode('consolidateBtn', 'div', {
				tag: 'button',
				text: 'Consolidate Tags',
				css: 'ui primary button'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data:{
					run:function(ui){
						
						$('#loader').fadeIn();

						var tagsToConsolidate = _.pluck(selectedTagsList, 'id');

						var deleteAllChoice = ui.checkboxWrapper.checkboxDiv.checkboxFormWrapper.form.process().fields.viewSwitch.value;

						var shouldDelete = deleteAllChoice === 'delete';

						var tagsData = {
							mainTag: obj.id,
							shouldDelete,
							tagsToConsolidate
						};

						sb.data.db.service("TagsService", 'consolidateTags', tagsData, function (response) {
	
							if (response) {
								
								TagsCollectionsSetup.refresh();	

								$('#loader').fadeOut();

								ui.hide();
								
								sb.dom.alerts.alert(response.message, response.message2, response.status);
								
							}

						});

					}.bind({}, ui)
				}
			}, sb.moduleId);
			
			ui.patch();

			ui.checkboxWrapper.checkboxDiv.checkboxFormWrapper.form.style('line-height:2.5');

			ui.checkboxWrapper.checkboxDiv.checkboxFormWrapper.form.patch();

		}

		ui.empty();
		
		ui.makeNode('modal', 'modal', {});
		
		// row 1
		ui.makeNode('tagsettingswrapper', 'div', {
			css: 'row'
		});

		ui.tagsettingswrapper.makeNode('tagsettings', 'div', {
			css: 'ui relaxed horizontal list'
		});

		// item 1
		ui.tagsettingswrapper.tagsettings.makeNode('iconwrapper', 'div', {
			css: 'item top aligned content',
			style: 'padding:0'
		});
		createTagIconEdit(ui.tagsettingswrapper.tagsettings.iconwrapper);

		// item 2
		ui.tagsettingswrapper.tagsettings.makeNode('namewrapper', 'div', {
			css: 'item top aligned content'
		});
		createTagNameEdit(ui.tagsettingswrapper.tagsettings.namewrapper);

		// row 2
		ui.makeNode('backwrapper', 'div', {
			css: 'row'
		});
		
		createTagBack(ui.backwrapper);

		ui.patch();
		
	}

    function ColorPalette(ui, tag, callback, save = true) { 

		function saveTagColor(color, tag, callback) {
		
			sb.data.db.obj.update('system_tags', {id: tag.id, color: color}, function(resp) {

				callback(resp);
				
			});

		};
		
		function paletteMaker(ui, tag, action) {

			var wrapper = ui.wrapper.colors;
			var colors = sb.dom.colors;
			var selectedIcon = '';		
						
			_.each(colors, function(color, i) {

				if (tag.color === color) {
					selectedIcon = '<i style="color: white !important;" class="checkmark icon"></i>';
				} else {
					selectedIcon = '';
				}
				
				wrapper.makeNode('color-' + i, 'div', {
					css:'item',
					style:'margin-right:5px; display:inline-block; cursor:pointer;',
					text:`<div class="ui huge ${color} empty label" value="${color}">${selectedIcon}</div>`,
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function() {
							
							wrapper.empty();
							
							loader(wrapper, 'Updating tag color...');
							
							wrapper.patch();

							tag.color = color;

							if (save) {
							
								saveTagColor(color, tag, function(res) {

									ui.empty();

									action(res);

									ColorPalette(ui, res, action, save);

									ui.patch();
									
								});

							} else {

								action(tag);

							}
							
						}
					}
				}, sb.moduleId);									
																
			});			
			
		}

		function iconMaker(ui, tag, action) {

			function updateIcon(icon) {

				tag.icon = icon;

				if (save) {
					
					ui.empty();
								
					action(tag);

					ColorPalette(ui, tag, action, save);

					ui.patch();

				} else {
					
					action(tag);

				}

			}

			var wrapper = ui.wrapper.icons;

			var fieldSetup = {
				type: 'icon',
				property: 'icon',
				obj: tag,
				options: {
					edit: true,
					editing: true,
					commitUpdates: true,
					onUpdate: function(res) {

						updateIcon(res);
						
					}
				},
				ui:wrapper
			};

			sb.notify ({
				type: 'view-field',
				data: fieldSetup
			});

		}
		
		ui.empty();
		
		ui.makeNode('wrapper', 'div', {});
		
		ui.wrapper.makeNode('title-color', 'div', {
			tag: 'h1'
			, text: 'Pick a color'
		});
		
		ui.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
		
		ui.wrapper.makeNode('colors', 'div', {
			css: 'text-center'
		});
		
		paletteMaker(ui, tag, callback);

		ui.wrapper.makeNode('lb_2', 'lineBreak', {spaces: 3});

		ui.wrapper.makeNode('title-icon', 'div', {
			tag: 'h1'
			, text: 'Pick an Icon'
		});
		
		ui.wrapper.makeNode('lb_3', 'lineBreak', {spaces: 1});
		
		ui.wrapper.makeNode('icons', 'div', {
			css: 'text-center'
		});

		iconMaker(ui, tag, callback);

		ui.wrapper.makeNode('lb_4', 'lineBreak', {spaces: 3});

		if (save) {
			
			ui.wrapper.makeNode('btnsWrapper', 'div', {
				css: 'row'
			});

			ui.wrapper.btnsWrapper.makeNode('btnsList', 'div', {
				css: 'ui relaxed horizontal list right floated content'
			});
			
			ui.wrapper.btnsWrapper.btnsList.makeNode('btn_group', 'div', {
				css: 'item'
			});

			ui.wrapper.btnsWrapper.btnsList.btn_group.makeNode('closeBtn', 'div', {
				tag: 'button',
				text: 'Close',
				css: 'ui button'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data:{
					run:function(ui){

						ui.hide();

						callback();

					}.bind({}, ui)
				}
			}, sb.moduleId);

		}
		
		ui.patch();

	}

    // shape needs to match a fomantic-UI shape
	function createIcon(wrapper, icon, color, shape, size = sizes[4]) {

		var iconWrapperCSS = `${shape || ''} ui icon button ${color || 'bento-blue'}`;
		var iconCSS = `icon ${icon || 'hashtag'} ${size}`;

		wrapper.makeNode('iconwrapper', 'div', {
			tag: 'button',
			css: iconWrapperCSS
		});
		
		wrapper.iconwrapper.makeNode('icon', 'div', {
			tag: 'i',
			style: 'color: white',
			css: iconCSS,
		});

	}

    function getTagDisplayData(tag, inSearch) {
		
		var tagName = tag.tag || 'Untitled';
		var tagColor = tag.color || 'grey';
		var tagIcon = tag.icon || 'hashtag';
		var category = {};
		var categoryName = '';

		if (tag.hasOwnProperty('name')) {
			tagName = tag.name;
		}

		tag.description = '';

		// Get legacy colors (this was done because old tags used hex colors)	
		var legacyColors = sb.dom.legacycolors;
		if (legacyColors.hasOwnProperty(tag.color)) {
			tagColor = legacyColors[tag.color];
		}

		if (
			tag.object_bp_type 
			&& tag.object_bp_type.substring(0, 1) === '#'
		) {
			
			var type = _.findWhere(appConfig.Types, {bp_name: tag.object_bp_type.substring(1)});
			if (type) {
				if (!type.icon) {
					type.icon = 'tags';
				}
				tagName = '<i class="ui ' + tagColor + ' ' + type.icon + ' icon"></i> '+ tagName;
				categoryName = '<i class="ui ' + tagColor + ' ' + type.icon + ' icon"></i>'+ type.name;
			} else {
				return;
			}
			
		} else {

			switch (tag.object_bp_type) {

				case 'companies':
					var company = tag;
					company.color = tagColor;
					tagName = sb.dom.getCompanyAvatar(company) + '<span>' + company.name + '</span>';
					tag.group_type = 'companies';
					categoryName = '<i class="ui yellow users icon"></i> Companies';
					break;

				case 'contacts':
					var contact = tag;
					tagName = '<i class="ui ' + tagColor + ' user icon"></i> ' + contact.fname + ' ' + contact.lname;
					tag.group_type = 'contacts';
					categoryName = '<i class="ui yellow user icon"></i> Contacts';
					break;
				
				case 'groups':
					if ( tag.group_type == 'JobType') {
						tagName = '<i class="ui ' + tagColor + ' wrench icon"></i> ' + tagName;
						categoryName = '<i class="ui grey wrench icon"></i> Job Types';
					} else if ( tag.group_type == 'Project' ) {
						tagName = '<i class="ui ' + tagColor + ' project diagram icon"></i> ' + tagName;
						categoryName = '<i class="ui blue project diagram icon"></i> Projects';
					} else {
						tagName = '<i class="ui ' + tagColor + ' users icon"></i> ' + tagName;
						categoryName = '<i class="ui grey users icon"></i> Teams';
					}
					break;
															
				case 'system_tags':
					tagName = `<i class="ui ${tagColor} ${tagIcon} icon"></i> ${tagName}`;
					tag.group_type = 'system_tag';
					categoryName = '<i class="ui grey hashtag icon"></i> Tags';
					break;
					
				case 'users':
					var user = tag;
					user.color = tagColor;
					tagName = sb.dom.getUserAvatar(user) + '<span>' + user.fname + ' ' + user.lname + '</span>';	
					tag.group_type = 'user';
					categoryName = '<i class="ui grey user icon"></i> Team Members';
					break;
					
				default:
					categoryName = '<i class="ui green plus icon"></i> New';
					break;
				
			}

		}

		if (inSearch) {
			if (tag.id != null) {
				tagName = '<div class="ui '+ tagColor + ' label tag ' + tag.object_bp_type + '">'+ tagName +'</div>';
			}
		}	

		// Update data
		tag.name = tagName;
		tag.color = tagColor;
		tag.icon = tagIcon;
		tag.hasDisplayData = true;
		category.name = categoryName;

		return {
			tag: tag,
			category: category
		};

	}

    function getAllSystemTags(callback) {

		sb.data.db.obj.getAll('system_tags', function(resp){

			callback(resp);	

		}, {
			name:			true,
			tag:			true,
			fname:			true,
			lname:			true,
			color:			true,
			icon:			true,
			profile_image:	true,
			object_bp_type:	true,
			group_type:		true,
			selectionObj: 	true
		});
		
		return true;

	}

    // UTIL
	
    function loader(ui, text) {
            
        ui.empty();
        
        ui.makeNode('loadingSeg', 'div', {
            css: 'ui basic segment'
        });
        
        ui.loadingSeg.makeNode('loader', 'div', {
            css: 'ui active inverted dimmer'
            , text: '<div class="ui large text loader">'+ text +'</div>'
        });
        
        ui.patch();
        
    }
    
    function buildTagsCollections(dom, state, draw, mainDom) {

	    dom.empty();
	    
	    dom.makeNode('titleWrap', 'div', {});
		dom.makeNode('lb_1', 'lineBreak', {spaces: 1});
		dom.makeNode('collectionsWrap', 'div', {});
		
		dom.titleWrap.makeNode('title', 'div', {
			tag: 'h1'
			, text: 'Tags Manager'
			, css: 'ui header'
		});
		
		dom.patch();
		
		sb.notify({
			type:'show-collection',
			data:{
				tags: false,
				filter: false,
				actions:{
					create:function(ui, newObj, onComplete){
						createNewTag(ui, state, newObj, function(newTag){
							onComplete(newTag);
						});
					},
					view:false,
					copy:true,
					columns:false,
					filter:false,
					batchTags:false,
					download: false,
					comments: false,
					navigateTo: false
				},
				domObj:dom.collectionsWrap,
				templates: false,
				fields:{
					tag:{
						title:'Tags',
						type:'title',
						isSearchable: true,
                        view: function(ui, obj, onComplete) {
							createTagGrid(ui, obj, onComplete);
						}
					},
				},
				objectType:'system_tags',
				state:state,
				selectedView:'table',
				onBoxview: true,
				menu: {
					subviews: false
				},
				subviews: false,
				bypassCache: true,
				hideTimeRangeFilter: true,
				where: {
					childObjs: {
						color:true,
						icon:true,
						id:true,
						object_uid:true,
						tag:true,
						type:true,
						is_deleted:true
					}
				}
			}
		});

		return {
			refresh: function() {
				
				buildTagsCollections(dom, state, draw, mainDom);
				
			}
		}
	    
    }

    // PUBLIC SCOPE
	return {
		
		init: function(){
			
			userId = sb.data.cookie.get('uid');
			
			sb.listen({
				'tags-manager-run': this.run,
			});

            var toolRegistrationsSetup = [
				// HQ Tool for Tags Manager
				{
					id: 'tagsManager',
					layers: ['hq'],
					name: 'Tags Manager',
					tip: 'Manage system tags.',
					icon: {
						type:'tags',
						color:'green'
					},
					dom: function(dom, state, draw, mainDom) {
						
						TagsCollectionsSetup = buildTagsCollections(dom, state, draw, mainDom);
							
					}
				}
			];

			sb.notify({
				type: 'register-tool',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'tagsComponent',
						title: 'Tags Component',
						icon: '<i class="ui tags icon"></i>',
						views: toolRegistrationsSetup
					}
				}
			});

		},
		
		run: function(data) { 
			
            data.run(data);

        },
		
	}
        
});