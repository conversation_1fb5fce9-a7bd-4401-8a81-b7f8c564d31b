Factory.register('crm', function(sb){
	
	var crm_cache = {
			domObj: {},
			state: {},
			draw: function() {}
		};
	var onMobile = false;
	var foundationGroup = false;
	
	function addContact(dom, state, mainDom, company){
				
		state.tabType = 'contacts';
		
		build_crm_createFlow(dom, state, {}, company);
		
	}
	
	function build_crm_createFlow(ui, state, templateObj, companyObj) {

		function contact_create_flow(ui, state){
			
			var selectedCompany;
			var selectedCompanyName;
			var selectedCompanyManagerName;
			var selectedManager;
			var selectedManagerName;
			var selectedSalesPerson;
			var selectedSalesPersonName;
			var fname;
			var lname;
			
			if(companyObj){
				selectedCompany = companyObj;
				selectedCompanyName = 'value="'+companyObj.name+'"';
			}
			
			if(templateObj){
				if(templateObj.fname){
					
					fname = templateObj.fname;
					lname = templateObj.lname;
					
					if(templateObj.manager){
						
						selectedManager = templateObj.manager;
						selectedManagerName = 'value="'+templateObj.manager.fname +' '+ templateObj.manager.lname+'"';
						
					}
					
					if(templateObj.sales_person){
						
						selectedSalesPerson = templateObj.sales_person;
						selectedSalesPersonName = 'value="'+templateObj.sales_person.fname +' '+ templateObj.sales_person.lname+'"';
						
					}
					
				}
			}

			var newContactAddressFormId = '';
			
			function displayContactInfoColumn(ui, contactInfoTypeIds){
				
				sb.data.db.obj.getWhere('contact_info_types', {
					id:{
						type:'or',
						values:contactInfoTypeIds
					}
				}, function(infoTypes){
					
					sb.data.db.obj.getWhere('contact_info_options', {
						contact_info_type:{
							type:'or',
							values:contactInfoTypeIds
						}
					}, function(contactInfoOptions){

						var addresses = [];
						var fields = _.map(infoTypes, function(type) {
							
							if(type.is_select == 'yes') {
								
								var selectOptions = _.where(contactInfoOptions, {contact_info_type:type.id});
								
								return {
									name:type.id,
									label:type.name,
									type:'select',
									options:_.map(selectOptions, function(option){
										
										return {
											name:option.name,
											label:option.id,
											value:option.name
										};
										
									})
								};
								
							}else{
								
								if(type.data_type == 'address'){
								
									addresses.push(type);
									
								}else{
									
									return {
										name:type.id,
										label:type.name
									};
		
								}
								
							}
							
							
							
						}, []);
						
						fields = _.compact(fields);

						ui.grid.col2.cont.empty();
						
						ui.grid.col2.cont.makeNode('seg','div',{css:'ui basic segment'});
											
						ui.grid.col2.cont.seg.makeNode('seg','div',{css:'ui tertiary segment'});
						ui.grid.col2.cont.seg.seg.makeNode('form1','form',fields);
						
						_.each(addresses, function(address){
							
							ui.grid.col2.cont.seg.makeNode('title'+address.id,'div',{css:'ui header', text:address.name});
							ui.grid.col2.cont.seg.makeNode('seg'+address.id,'div',{css:'ui tertiary segment'});
							ui.grid.col2.cont.seg['seg'+address.id].makeNode('form','form',{
								street:{
									name:'street',
									label:'Street',
									type:'text'
								},
								city:{
									name:'city',
									label:'City',
									type:'text'
								},
								state:{
									name:'state',
									label:'State',
									type:'select',
									options:sb.data.stateArray
								},
								zip:{
									name:'zip',
									label:'ZIP/Postal Code',
									type:'text'
								},
								country:{
									name:'country',
									label:'Country',
									type:'select',
									options:sb.data.countryArray
								}
							});

							newContactAddressFormId = address.id;
							
						});
						
						ui.grid.col2.cont.patch();
						ui.grid.col2.seg.loading(false);			
							
						
					});
					
				});
				
			}
									
			ui.makeNode('grid','div',{css:'ui centered grid'});
			
			ui.grid.makeNode('col','div',{css:'ui sixteen wide'})
				.makeNode('seg','div',{css:'ui basic loading segment'});
			
			ui.patch();
			
			sb.data.db.obj.getAll('contact_types',function(types){

				types = types.reverse();
			
				var typeOptions = _.map(types,function(type){
					
					return {
							name:type.name,
							label:type.name,
							value:type.id
						};
					
				}, []);
				
				ui.grid.empty();
				
				ui.grid.makeNode('col1','div',{css:'ui ten wide column'});
				ui.grid.makeNode('col2','div',{css:'ui six wide column'});
				
				ui.grid.col1.makeNode('seg','div',{css:'ui basic segment'});

				ui.grid.col1.seg.makeNode('header','div',{css:'ui huge header', text:'Basic Info'});
				ui.grid.col1.seg.makeNode('basicSeg','div',{css:'ui basic segment'});
				
				ui.grid.col1.seg.basicSeg.makeNode('basicForm','form',{
					fname:{
						name:'fname',
						type:'text',
						label:'First Name',
						value:fname
					},
					lname:{
						name:'lname',
						type:'text',
						label:'Last Name',
						value:lname
					},
					company:{
						type:'hidden',
						name:'company'
					},
					companyManager:{
						type:'hidden',
						name:'companyManager'
					},
				});
				
				ui.grid.col1.seg.basicSeg.basicForm.makeNode('changeCompany', 'div', {
					css: 'ui fluid search item field',
					text:
						'<label>Company</label>'+
						'<div class="ui icon fluid input">'+
							'<input id="company-name" class="prompt" '+ selectedCompanyName +' type="text" placeholder="Start typing to search or create a Company" style="border-radius:.28571429rem;">'+
							'<i class="search icon"></i>'+
						'</div>'+
						'<div class="results"></div>',
					listener:{
						type: 'search',
						objectType: 'companies',
						onSelect: function(result, response){
							if (!_.isEmpty(result)) {
								selectedCompany = result;
								ui.grid.col1.seg.basicSeg.basicForm.setCompanyManager.loading(true);
								$(".companyManagerDiv").show();
								
								if (result.id > 0) {
									
									sb.data.db.obj.getById('companies', result.id, function(company) {
										ui.grid.col1.seg.basicSeg.basicForm.companyManager.update({value:company.manager});
										
										if (company.manager > 0) {

											sb.data.db.obj.getById('users', company.manager, function(manager) {
												if (manager != null){
													selectedCompanyManagerName = manager.name;
													$("#company-manager-name").val(manager.name);
												}
												ui.grid.col1.seg.basicSeg.basicForm.setCompanyManager.loading(false);
											});
										} else {
											$("#company-manager-name").val("");
											ui.grid.col1.seg.basicSeg.basicForm.setCompanyManager.loading(false);
										}
									});
								} else {
									ui.grid.col1.seg.basicSeg.basicForm.setCompanyManager.loading(false);
								}

								ui.grid.col1.seg.basicSeg.basicForm.company.update({value:result.id});
							}
							
						},
						category: 'type',
						selection:{
							name:true,
							type:{
								name:true
							}
						},
						onResponse: function(raw){

						    var response = {
							    results : {
								    create:{
								    	name : '<i class="green plus icon"></i> Create',
								    	results : [{
									    	id:0,
									    	name:$('#company-name').val()
								    	}]
							    	}
							    }
						    };
						    
						    _.each(raw.results, function(item){

						    		///null undefined check for company catagory
								if(!_.isNull(item.type) && !_.isUndefined(item.type)){
								
									///push to results group else create new
								    if ( response.results.hasOwnProperty(item.type.id) ) {
									
									    response.results[item.type.id].results.push(item);
								    
								    }else{
									    
									    response.results[item.type.id]
									    	
									    	= {
										    	name : item.type.name,
										    	results : [item]
									    	};
									    	
								    }										
									
								} else {
									
									///company does not have a type. push to none else create
								    if ( response.results.hasOwnProperty('none') ) {
									
									    response.results['none'].results.push(item);
								    
								    }else{
									    
									    response.results['none']
									    	
									    	= {
										    	name : '&#160&#160',
										    	results : [item]
									    	};
									    	
								    }
									
								}
															    
						    });

						    return response;
						    
					    }
					}
				});

				ui.grid.col1.seg.basicSeg.basicForm.makeNode('setCompanyManager', 'div', {
					css: 'ui fluid search item field companyManagerDiv',
					text:
						'<label>Company Manager</label>'+
						'<div class="ui icon fluid input">'+
							'<input id="company-manager-name" class="prompt" '+ selectedCompanyManagerName +' type="text" placeholder="Start typing to search" style="border-radius:.28571429rem;">'+
							'<i class="search icon"></i>'+
						'</div>'+
						'<div class="results"></div>',
						listener:{
							type: 'search',
							objectType: 'users',
							onSelect: function(result, response){
								if (!_.isEmpty(result)) {
	
									selectedCompanyManagerName = result.name;
									ui.grid.col1.seg.basicSeg.basicForm.companyManager.update({value:result.id});
									
								}
								
							},
							category: 'type',
							selection:{
								name:true,
								type:{
									name:true
								}
							},
							onResponse: function(raw){
								var response = {
									results : {}
								};
								
								_.each(raw.results, function(item){
	
										///null undefined check for company catagory
									if(!_.isNull(item.type) && !_.isUndefined(item.type)){
									
										///push to results group else create new
										if ( response.results.hasOwnProperty(item.type.id) ) {
										
											response.results[item.type.id].results.push(item);
										
										}else{
											
											response.results[item.type.id]
												
												= {
													name : item.type.name,
													results : [item]
												};
												
										}										
										
									} else {
										
										///company does not have a type. push to none else create
										if ( response.results.hasOwnProperty('none') ) {
										
											response.results['none'].results.push(item);
										
										}else{
											
											response.results['none']
												
												= {
													name : '&#160&#160',
													results : [item]
												};
												
										}
										
									}
																	
								});
	
								return response;
								
							}
						}
				});
				
				ui.grid.col1.seg.basicSeg.basicForm.makeNode('break','div',{text:'<br />'});
				
				ui.grid.col1.seg.basicSeg.basicForm.makeNode('manager', 'div', {
					css: 'ui fluid search item field',
					text:
						'<label>Manager</label>'+
						'<div class="ui icon fluid input">'+
							'<input class="prompt" type="text" '+ selectedManagerName +' placeholder="Start typing to search" style="border-radius:.28571429rem;">'+
							'<i class="search icon"></i>'+
						'</div>'+
						'<div class="results"></div>',
					listener:{
						type: 'search',
						objectType: 'users',
						onSelect: function(result, response){

							if (!_.isEmpty(result)) {

								selectedManager = result;
								
							}
							
						},
						category: 'type',
						selection:{
							fname:true,
							lname:true
						},
						onResponse: function(raw){

						    var response = {
							    results : {}
						    };
						    
						    _.each(raw.results, function(item){

						    		///null undefined check for company catagory
								response.results[item.id] = {
							    	name : 'User',
							    	results : [item]
						    	};
															    
						    });

						    return response;
						    
					    }
					}
				});

				ui.grid.col1.seg.basicSeg.basicForm.makeNode('sales', 'div', {
					css: 'ui fluid search item field',
					text:
						'<label>Sales Person</label>'+
						'<div class="ui icon fluid input">'+
							'<input class="prompt" type="text" '+ selectedSalesPersonName +' placeholder="Start typing to search" style="border-radius:.28571429rem;">'+
							'<i class="search icon"></i>'+
						'</div>'+
						'<div class="results"></div>',
					listener:{
						type: 'search',
						objectType: 'users',
						onSelect: function(result, response){

							if (!_.isEmpty(result)) {

								selectedSalesPerson = result;
								
							}
							
						},
						category: 'type',
						selection:{
							fname:true,
							lname:true
						},
						onResponse: function(raw){

						    var response = {
							    results : {}
						    };
						    
						    _.each(raw.results, function(item){

						    		///null undefined check for company catagory
								response.results[item.id] = {
							    	name : 'User',
							    	results : [item]
						    	};
															    
						    });

						    return response;
						    
					    }
					}
				});
								
				ui.grid.col2.makeNode('seg','div',{css:'ui basic loading segment'});
				ui.grid.col2.makeNode('cont','div',{css:''});
				
				ui.grid.col2.seg.makeNode('header','div',{css:'ui huge header', text:'Contact Info'});
				ui.grid.col2.seg.makeNode('type','div',{css:'ui tertiary segment'})
					.makeNode('typeForm','form',{
						type:{
							name:'type',
							type:'select',
							label:'Contact Type',
							options:typeOptions,
							change:function(form, selected){
	
								ui.grid.col2.seg.loading();
								displayContactInfoColumn(ui, _.find(types, function(type){return type.id == +selected;}).available_types);
							
							}
						}
					});
				
				// buttons
				ui.makeNode('save','div',{css:'ui right floated green button', text:'Save'})
					.notify('click',{
						type:'crm-run',
						data:{
							run:function() {

								// Get basic data
								var basicData = ui.grid.col1.seg.basicSeg.basicForm.process().fields;
								
								basicData.type = ui.grid.col2.seg.type.typeForm.process().fields.type;

								// Get other contact info
								var other = ui.grid.col2.cont.seg.seg.form1.process().fields;

								// Get address info
								var address = [];
								
								if (newContactAddressFormId) {
									address = ui.grid.col2.cont.seg['seg'+newContactAddressFormId].form.process().fields;
									address.type = newContactAddressFormId;
								}

								var obj = {
										basicData: basicData,
										company: $('#company-name').val(),
										companyManager: basicData.companyManager.value,
										contactInfo: {
											other: other,
											address: address
										},
										manager: selectedManager,
										salesperson: selectedSalesPerson
									};
								
								if (basicData.fname.value == '' || basicData.lname.value == '') {
									sb.dom.alerts.alert('Please enter a full name for the contact.', '', 'error');
									return;
								}
								
								if (basicData.company.value == '') {
									
									if ($('#company-name').val() == '') {
										
										sb.dom.alerts.alert('Please select or create a company.', '', 'error');
										return;
									
									}
									
								}

								// Ask if the user wants to tag user on stuff
								var text = appConfig.insance === 'foundation_group' ? 'Would you like to tag this user on existing projects, action items, and core services within this company?' : 'Would you like to tag this user on existing projects?'
								sb.dom.alerts.ask({
									title: 'Tag?',
									text: text
								}, function(resp) {
									
									swal.disableButtons();

									var tagExistingProjects = false;
									if (resp) {
										tagExistingProjects = true;
									}

									ui.save.loading();

									sb.data.db.controller('createContact', {
										obj: obj,
										templateObj: templateObj,
										state: state,
										tagExistingProjects: tagExistingProjects
									}, function(data) {
										
										if (typeof state.onSave === 'function') {
											state.onSave(data);
										}

										if (state.onProject === true) {
											
											sb.notify({
												type: 'app-navigate-to',
												data: {
													type:'UP'
												}
											});													
										
										} else {
											
											window.location.href = sb.data.url.createPageURL(
												'object',
												{
													type:	'contacts',
													id:		data.id,
													name:	data.fname +' '+ data.lname
												}
											);
											
										}

									});

								});
																								
							}
						}
					},sb.moduleId);
				
				ui.makeNode('cancel','div',{css:'ui left floated red button', text:'Cancel'})
					.notify('click',{
						type:'crm-run',
						data:{
							run:function(){
								$('.ui.modal').modal('hide');
							}
						}
					},sb.moduleId);

				ui.patch();
				$(".companyManagerDiv").hide()
				
				if(companyObj){
					ui.grid.col1.seg.basicSeg.basicForm.company.update({value:selectedCompany.id});
				}
				
				displayContactInfoColumn(ui, types[0].available_types);
								
			});
			
		}
		
		ui.empty();
		
		ui.makeNode('wrapper', 'div', {});

		switch(state.tabType) {
			
			case 'companies':
							
				build_createCompanyFlow.call(ui.wrapper, ui.wrapper, state, undefined, templateObj);
				
				ui.patch();
				
				break;
				
			case 'contacts':
							
				ui.patch();
				
				contact_create_flow(ui.wrapper, state);
			
				break;
				
			default:
							
				ui.patch();
				
				contact_create_flow(ui.wrapper, state);
							
		}
		
	}
	
	function build_createCompanyFlow(ui, state, type, templateObj) {
			
		function saveNewCompany(form, state, callback) {

			var formInfo = form.process();
			var mainDom = this;

			function validate_form(formInfo, callback) {
				
				if(formInfo.fields.name.value === '') {
					
					sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
					
					callback(false);
					
				} else if(parseInt(formInfo.fields.is_vendor.value) === 1 && _.isEmpty(formInfo.fields.markup_percent.value)) {
					
					sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
					
					callback(false);
					
				} else {
					
					callback(true);
					
				}
				
			}
			
			validate_form(formInfo, function(res) {

				if(res === false) {
					
					return;
					
				} else {
					
					var company = {
							name:formInfo.fields.name.value,
							manager: +formInfo.fields.manager.value,
							type: +formInfo.fields.companyCategory.value,
							is_vendor: +formInfo.fields.is_vendor.value,
							tagged_with: []
						},
						itemId = 'companies';
						
					if ( !_.isUndefined(formInfo.fields.profile_image.value.fileData) ) 
						company.profile_image = formInfo.fields.profile_image.value;					
						

					if(formInfo.fields.is_vendor.value == 1){
						
						itemId = 'vendors';
						
						company.markup_percent = formInfo.fields.markup_percent.value;
						company.default_product = +formInfo.fields.default_product.value;
						company.products = formInfo.fields.products.value;
						
					};

					if(state.hasOwnProperty('tagged_with')) {
						
						company.tagged_with = state.tagged_with;
						
					}
					
					// DO NOT REMOVE -- this is needed for a specific client requirement 
					if (appConfig.instance === 'foundation_group') {
						
						if (Array.isArray(company.tagged_with)) {
							company.tagged_with.push(1763496);
						} else {
							company.tagged_with = [1763496];
						}
						
					}

					mainDom.empty();
					
					build_loader(mainDom, 'Creating new company...');
					
					mainDom.patch();							

					sb.data.db.obj.create('companies', company, function(newCompany){

						if(newCompany) {
							
							if(!callback) {
								
								window.location = sb.data.url.createPageURL('object', {
									id: newCompany.id, 
									type: newCompany.object_bp_type, 
									name: newCompany.name
								});
								
								templateObj.onSave(newCompany);	
								
							} else {
								
								callback(newCompany);
								
							}
							
						}
						
					}, 1);
					
				} 
				
			});
			
		}
		
		function build_companyContacts(ui, state, company) {
			
			var contacts_cache = [];
			var dom = {};
			
			function build_tableBody(newContact) {
				
				if(newContact) {
					
					contacts_cache.push(newContact);
					
				}
				
				table.tbody.empty();
				
				_.each(contacts_cache, function(o) {
					
					table.tbody.makeNode('row-'+o.id, 'div', {
						tag: 'tr'
					});
					
					table.tbody['row-'+o.id].makeNode('fname', 'div', {
						tag: 'td',
						text: o.fname
					});
					table.tbody['row-'+o.id].makeNode('lname', 'div', {
						tag: 'td',
						text: o.lname
					});
					table.tbody['row-'+o.id].makeNode('type', 'div', {
						tag: 'td',
						text: '<div class="ui label">'+ o.type.name +'</div>'
					});
					table.tbody['row-'+o.id].makeNode('manager', 'div', {
						tag: 'td',
						text: o.manager.fname + ' ' + o.manager.lname
					});
					table.tbody['row-'+o.id].makeNode('remove', 'div', {
						tag: 'td',
						style: 'width: 100px;'
					});
					
					table.tbody['row-'+o.id].remove.makeNode('btn', 'div', {
						text: 'Delete',
						css: 'ui mini red button'
					}).notify('click', {
						type: 'crm-run',
						data: {
							run: function(data) {
								
								contacts_cache = _.filter(contacts_cache, function(cached_o) {
									return cached_o.id !== o.id;
								});

								build_tableBody(undefined);
								
								sb.data.db.obj.erase('', o.id, function(res) {});
								
							}
						}
					}, sb.moduleId);
					
				});
				
				table.tbody.patch();
				
				build_addContact(ui.body.seg, state, company);
				
			}
			
			function build_addContact(ui, state, company) {
				
				function build_contactFormUI(ui, btnsUI) {
					
					ui.empty();
					
					ui.makeNode('head', 'div', {});
					ui.makeNode('lb_1', 'lineBreak', {spaces: 1});
					ui.makeNode('body', 'div', {});
					
					ui.head.makeNode('title', 'div', {text: '<i class="address book outline icon"></i> Create a Contact', css:'ui huge header'});
					
					build_loader(ui.body, 'Checking system setup...');
					ui.patch();
					
					setupContactSystem(function(done){
						
						build_loader(ui.body, 'Fetching users...');
						ui.patch();
						
						sb.data.db.obj.getAll('users', function(users){
							
							build_loader(ui.body, 'Fetching contact types...');
							ui.patch();
							
							sb.data.db.obj.getAll('contact_types', function(types){
		
								var formObj = {
										fname:{
											type:'text',
											name:'fname',
											label:'First Name'
										},
										lname:{
											type:'text',
											name:'lname',
											label:'Last Name'
										},
										manager:{
											type:'select',
											name:'manager',
											label:'Manager',
											options:[]
										},
										type:{
											type:'select',
											name:'type',
											label:'Contact Type',
											options:[]
										}
									};
								
								_.each(types, function(t){
									
									formObj.type.options.push({
										value:t.id,
										name:t.name
									});
									
								});
									
								_.each(users, function(u){
									
									formObj.manager.options.push({
										value:u.id,
										name:u.fname +' '+ u.lname
									});
									
								});	
								
								formObj.manager.value = sb.data.cookie.get('uid');
								
								ui.body.empty();
								
								ui.body.makeNode('form', 'form', formObj);
								
								ui.body.makeNode('lb_1', 'lineBreak', {spaces: 1});
								
								ui.body.makeNode('btns', 'div', {});
								
								btnsUI(ui.body.btns, ui.body.form);
								
								ui.patch();
																					
							});
						});
						
					});
					
				}
				
				function process_contactForm(form, state, company, loading, after) {
					
					var formData = form.process().fields;
					
					if(formData.fname.value === '' || formData.lname.value === '') {
						
						sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
						return;
						
					}
					
					loading();
					
					var contact = {
							fname: formData.fname.value,
							lname: formData.lname.value,
							manager: formData.manager.value,
							type: formData.type.value,
							company: company.id
						};
						
					if(state && state.tagged_with){
		
						contact.tagged_with = state.tagged_with;
						
					}

					sb.data.db.obj.create('contacts', contact, function(created){

						after(created);
						
					}, 1);
					
				}
				
				ui.empty();
				
				ui.makeNode('btnWrap', 'div', {
					css: 'text-center'
				});
				
				ui.btnWrap.makeNode('add', 'div', {
					text: '<i class="plus icon"></i>',
					css: 'ui circular icon green button',
				}).notify('click', {
					type: 'crm-run',
					data: {
						run: function(data) {
							
							build_contactFormUI(ui, function(ui, form) {
								
								ui.makeNode('save', 'div', {
									css: 'ui green button right floated',
									text: 'Save'
								}).notify('click', {
									type: 'crm-run',
									data: {
										run: function(data) {
											
											process_contactForm(form, state, company, function() {

												dom.body.seg.empty();

												build_loader(dom.body.seg, 'Creating new contact...');
												
												dom.body.seg.patch();
												
											}, function(created) {
												
												build_tableBody(created);
												
											});
											
										}
									}
								}, sb.moduleId);
								
							});
							
						}
					}
				}, sb.moduleId);
				
				ui.patch();
				
			}
			
			dom = ui;
			
			ui.empty();
			
			ui.makeNode('head', 'div', {
				css: 'ui grid'
			});
			ui.makeNode('lb_1', 'lineBreak', {spaces: 1});
			ui.makeNode('body', 'div', {});
			
			ui.head.makeNode('col1', 'div', {
				css: 'ui twelve wide column'
			});
			ui.head.makeNode('col2', 'div', {
				css: 'ui four wide column'
			});
			
			ui.head.col1.makeNode('title', 'div', {
				tag: 'h1',
				text: '<i class="plus icon"></i> Add contacts to ' + company.name,
				css: 'ui header'
			});
			
			ui.head.col2.makeNode('goTo_company', 'div', {
				text: 'Continue to ' + company.name,
				css: 'ui primary button right floated'
			}).notify('click', {
				type: 'crm-run',
				data: {
					run: function(data) {
						
						$('.ui.modal').modal('hide');
						
						window.location = sb.data.url.createPageURL('object', {
							id: company.id, 
							type: company.object_bp_type, 
							name: company.name
						});
						
					}
				}
			}, sb.moduleId);
			
			var table = ui.body.makeNode('table', 'div', {
				tag: 'table',
				css:'ui celled compact table'
			});
			
			ui.body.makeNode('seg', 'div', {
				css: 'ui basic segment'
			});
			
			build_addContact(ui.body.seg, state, company);
			
			table.makeNode('thead', 'div', {
				tag: 'thead'
			});
			table.makeNode('tbody', 'div', {
				tag: 'tbody'
			});
			
			table.thead.makeNode('tr', 'div', {
				tag: 'tr'
			});
			
			table.thead.tr.makeNode('fname', 'div', {
				tag: 'th',
				text: 'First Name'
			});
			table.thead.tr.makeNode('lname', 'div', {
				tag: 'th',
				text: 'Last Name'
			});
			table.thead.tr.makeNode('type', 'div', {
				tag: 'th',
				text: 'Type'
			});
			table.thead.tr.makeNode('manager', 'div', {
				tag: 'th',
				text: 'Manager'
			});
			table.thead.tr.makeNode('remove', 'div', {
				tag: 'th'
			});
			
			build_tableBody(undefined);
			
			ui.patch();
			
		}

		ui.empty();
		
		ui.makeNode('head', 'div', {
			css: 'ui grid'
		});
		ui.makeNode('lb_1', 'lineBreak', {spaces: 1});
		ui.makeNode('body', 'div', {});
		
		ui.head.makeNode('col1', 'div', {
			css: 'ui twelve wide column'
		});
		ui.head.makeNode('col2', 'div', {
			css: 'ui four wide column'
		});
		
		ui.head.col1.makeNode('title', 'div', {
			tag: 'h1',
			text: '<i class="building icon"></i> Create a Company',
			css: 'ui header'
		});

		if(state.tabType !== 'companies') {
			
			ui.head.col2.makeNode('back', 'div', {
				text: 'Back',
				css: 'ui red button right floated'
			}).notify('click', {
				type: 'crm-run',
				data: {
					run: function(data) {
						
						if(state.hasOwnProperty('btns')) {
							
							if(state.btns.hasOwnProperty('back')) {
								
								state.btns.back.run();
								
							} else {
								
								ui.empty();
						
								build_crm_createFlow(ui, state, templateObj);
								
								ui.patch();
								
							}
							
						} else {
							
							ui.empty();
						
							build_crm_createFlow(ui, state, templateObj);
							
							ui.patch();	
							
						}
						
					}
				}
			}, sb.moduleId);	
			
		}
		
		build_loader(ui.body, 'Checking system setup...');
		ui.body.patch();
		
		setupContactSystem(function(systemSetup) {
			
			build_loader(ui.body, 'Fetching active users...');
			ui.body.patch();
			
			sb.data.db.obj.getWhere('users', {enabled: 1, childObjs: {
				fname:true,
				lname:true
			}}, function(users) {
				
				build_loader(ui.body, 'Fetching product categories...');
				ui.body.patch();
				
				sb.data.db.obj.getAll('inventory_billable_categories', function(products) {
					
					sb.data.db.obj.getBlueprint('companies', function(bp) {
						
						sb.data.db.obj.getAll('company_categories', function (companyCategories) {

							var userId = sb.data.cookie.get('uid');
							var typeOptions = [];
							
							_.each(bp.type.options, function(name, val){
								
								typeOptions.push({
									name:name,
									value:val
								});
								
							});
							
							var formObj = {
									name: {
										name:'name',
										label:'Name',
										type:'text',
										css:'ui input focus'
									},
									file_upload: {
										type: 'file-upload'
										, label: '<i class="file image outline icon large"></i> Profile Image (Optional)'
										, name: 'profile_image'
									},										
									companyCategory: {
										name:'companyCategory',
										label:'Type',
										type:'select',
										options:_.sortBy(typeOptions, 'name'),
										value: 0
									},
									is_vendor: {
										label:'Is this company a vendor?',
										name:'is_vendor',
										type:'hidden',
										options:[
											{
												name:'No',
												value:0
											},
											{
												name:'Yes',
												value:1
											}
										],
										value: 0,
										onChange:function(value){
											
											if(parseInt(value) === 1) {
												
												ui.body.form.markup_percent.update({
													type: 'text'
												});
												
												ui.body.form.default_product.update({
													type: 'select'
												});
												
												ui.body.form.products.update({
													type: 'checkbox'
												});
												
											} else {
												
												ui.body.form.markup_percent.update({
													type: 'hidden'
												});
												
												ui.body.form.default_product.update({
													type: 'hidden'
												});
												
												ui.body.form.products.update({
													type: 'hidden'
												});
												
											}
											
										}
									},
									markup_percent: {
										name:'markup_percent',
										label:'Markup Percentage',
										type:'hidden',
										value: 0
									},
									default_product: {
										name: 'default_product',
										type: 'hidden',
										label: 'Default Product Category',
										options: _.chain(products).map(function(o) {
											return {
												name: o.name,
												value: o.id	
											};
										}).sortBy(function(o) { 
											return o.name; 
										}).value()
									},
									products: {
										name: 'products',
										type: 'hidden',
										label: 'Other Product Categories',
										options: _.chain(products).map(function(o) {
											return {
												name: o.name,
												label: o.name,
												value: o.id	
											};
										}).sortBy(function(o) { 
											return o.name; 
										}).value()
									},
									manager:{
										name:'manager',
										label:'Manager',
										type:'select',
										options:_.map(users, function(u){ 
											
											return {
												value: u.id, 
												name: u.fname +' '+ u.lname
											}; 
											
										})						
									}
								};

							formObj.companyCategory.options = _.map(companyCategories, function (cat) {
								return {
									name: 		cat.name
									, value: 	cat.id
								};
							});

							if(templateObj !== undefined) {
								
								if(templateObj.hasOwnProperty('is_vendor') 
									&& templateObj.is_vendor === 1) {
									
									formObj.is_vendor.value = 1;
									formObj.markup_percent.type = 'text';
									formObj.default_product.type = 'select';
									formObj.products.type = 'checkbox';
								
								} else {
									
									formObj.is_vendor.value = 0;
									
								}	
								
							}
							
							users = _.sortBy(users, function(o) {
								return o.fname;
							});
							
							formObj.manager.value = userId;
							
							formObj.default_product.options.unshift({
								name: 'None',
								value: 0
							});
							
							formObj.companyCategory.value = formObj.companyCategory.options[0].value;
							
							ui.body.empty();
							
							ui.body.makeNode('form', 'form', formObj);
							
							ui.body.makeNode('lb_1', 'lineBreak', {spaces: 1});
							
							ui.body.makeNode('btns', 'div', {
								css: 'ui buttons right floated'
							});
							
							if(state.hasOwnProperty('btns')) {
								
								if(state.btns.hasOwnProperty('save')) {
									
									ui.body.btns.makeNode('save', 'div', {
										tag: 'button',
										text: state.btns.save.text,
										css: 'ui green button'
									}).notify('click', {
										type: 'crm-run',
										data: {
											run: function(data) {
												
												saveNewCompany.call(ui.body, ui.body.form, state, function(newCompany) {
													
													state.btns.save.run(newCompany);
													
												});
												
											}
										}
									}, sb.moduleId);
									
								}
								
							} else {
								
								ui.body.btns.makeNode('save', 'div', {
									tag: 'button',
									text: 'Save and view company',
									css: 'ui green button'
								}).notify('click', {
									type: 'crm-run',
									data: {
										run: function(data) {
											
											saveNewCompany.call(ui.body, ui.body.form, state);
											
										}
									}
								}, sb.moduleId);
								
								if(state.tabType !== 'companies') {
									
									ui.body.btns.makeNode('or', 'div', {
										css: 'or'
									});
									
									ui.body.btns.makeNode('create_contacts', 'div', {
										tag: 'button',
										text: 'Save and create contacts',
										css: 'ui primary button'
									}).notify('click', {
										type: 'crm-run',
										data: {
											run: function(data) {
												
												saveNewCompany.call(ui.body, ui.body.form, state, function(newCompany) {
													
													build_companyContacts(ui, state, newCompany);
																										
												});
												
											}
										}
									}, sb.moduleId);	
									
								}	
								
							}
							
							ui.body.patch();

						}, {name: true});
						
					}, 1);
												
				}, {
					name: true
				});
				
			});
			
		});
		
		ui.patch();
		
	}
	
	function check_instance() {

		if(appConfig.instance == 'thelifebook') {
			
			return {
				tab1: 'Pastors',
				tab2: 'Churches',
				navTitle: 'Churches'
			}
			
		} else if( foundationGroup ) {
			
			return {
				tab1: 'Contacts',
				tab2: 'Organizations',
				navTitle: 'Organizations'
			}
			
		}  else {
			
			return false;
			
		}
		
	}
	
	function crm(ui, state, draw, onContact) {

		function return_queryObj(itemType, state) {

			var queryObj = {};

			if(itemType === 'companies') {
				
				queryObj = {
					childObjs: {
						name: true,
						manager: true,
						type:{
							name:true,
							states:true
						},
						profile_image:true,
						value: true						
					},
					is_vendor: 0				
				};

			} else if(itemType === 'contacts') {
				
				queryObj = {
					childObjs:{
						fname:true,
						lname:true,
						type:{
							name:true,
							states:true
						},
						company:{
							name:true
						},
						state:true,
						contact_info:{
							city: 		true,
							country: 	true,
							state: 		true,
							street: 	true,
							street2: 	true,
							zip: 		true,
							info: 		true,
							is_primary: true,
							name: 		true,
							title: 		true,
							type: 		{
								data_type: true
							}
						},
						value: true,
						lead_source: true
					}
	
				};

			} else if(itemType === 'vendors') {
				
				queryObj = {
					childObjs: {
						name: true,
						manager: true,
						type:{
							name:true,
							states:true
						},
						markup_percent:true,
						default_product:true						
					},
					is_vendor: 1
				};

			}

			switch(state.viewing) {
				
				case 'team':

					queryObj.tagged_with = [state.team.id];
				
					break;
					
				case 'mystuff':
					
					queryObj.tagged_with = [sb.data.cookie.get('uid')];
				
					break;
				
			}

			return queryObj;
			
		}

		function build_companiesCollections(ui, state, settings) {

			// settings.vendors -- 0 or 1
			
			var managerColTitle = 'Manager';
			
			if ( foundationGroup )
				managerColTitle = 'CSM'; 
			
			var itemType = 'companies';
			var collectionsSetup = {
					domObj:ui,
					actions:{
						create:function(dom, templateObj, onCreate){
	
							templateObj.is_vendor = 0;
							state.tabType = 'companies';
							
							if(settings.hasOwnProperty('vendors')) {
								templateObj.is_vendor = settings.vendors;
							}
							
							templateObj.onSave = onCreate;
							
							state.tagged_with = _.union(templateObj.tagged_with, state.tagged_with);
							
							build_crm_createFlow(dom, state, templateObj);
							
																													
						},
						view:false,
						downloadCSV:true
					},
					fields:{
						name:{
							title:'Name',
							view:function(c, obj){

								var text = '';
								
								if(obj.name)
									text = `${obj.name}`;
								
								if(c){
									c.makeNode('name', 'div', {
										text:text,
										tag:'a',
										css:'ui small header',
										href:sb.data.url.createPageURL('object', {
												id: 					obj.id
												, name: 				text
												, object_bp_type: 	'companies'
												, type: 				'companies'
											})
									});
								}else{
									return text;
								}
								
							},
							type:'title'
						},
						managerName:{
							title:managerColTitle,
							view:function(c, obj){

								var manager = 'No Manager Assigned';
								
								if ( foundationGroup )
									manager = 'No CSM Assigned'; 
								
								if(obj.manager)
									manager = `${obj.manager.fname} ${obj.manager.lname}`;
								
								if(c){
									c.makeNode('name', 'div', {text:manager});
								}else{
									return manager;
								}
								
							},
							type:'description'
						},
						type:{
							title:'Type',
							view:function(c, obj){

								var type = '';
								
								if(obj.type){
									
									type = `${obj.type.name}`;
									
									if(c){
										c.makeNode('type', 'div', {css: 'ui label', text:type});
									}else{
										return type;
									}
									
								}
							},
							type:'type'													
						},
						value:{
							title:'Value',
							view:function(c, obj) {

								var value = '';

								if (obj.value) {

									value = obj.value;

									if(c){

										sb.notify ({
											type: 'view-field',
											data: {
												type: 'currency',
												property: 'value',
												obj: obj,
												ui: c.makeNode('t', 'div', {}),
												options: {
													edit: false,
													editing: false
												}
											}
										});

									} else {

										c.makeNode('type', 'div', {css: 'ui label', text:'$0.00'});

									}

								} else {

									if (c) {
										c.makeNode('type', 'div', {css: 'ui label', text:'$0.00'});
									} else {
										return value;
									}

								}
								
							},
							type:'detail'
						},
						date_created:{
							title:'Created On',
							type:'date'
						}
					},
					groupings:{
						type:'Type'
					},
					metrics:{
						sum:{
							title:'Total',
							fields:{
								value:function(ui, getData, test, data){

									var totalValue = 0;
									
									if(_.isArray(data)){
									
										_.each(data, function(inv){										

											totalValue += inv.value;
											
										});
										
									} else {
										
										totalValue = data;
										
									}	
									
									if(!_.isUndefined(ui) && !_.isNull(ui)){
										
										ui.empty();
										ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>'+ sb.dom.formatCurrency(totalValue) +'</b>'});
										ui.patch();	
																					
									}	
									
									return '<b>'+ sb.dom.formatCurrency(totalValue) +'</b>';							
							
								}
							}
						}
					},
					singleView:{
						view:function(dom, obj, onComplete){
	
							dom.makeNode('card', 'div', {css: 'ui fluid card'});
							dom.card.makeNode('content', 'div', {css: 'content'});
							dom.card.content.makeNode('header', 'div', {css: 'header', text:obj.name});
							dom.card.content.makeNode('hmeta', 'div', {css: 'meta', text:obj.type.name});
							
							dom.card.content.hmeta.makeNode('metatxt', 'div', {css:'right floated time', text:'<span>Created on:</span> '+ moment(obj.date_created).format('MM/DD/YY')});
																						
							dom.card.makeNode('con', 'div', {css: 'content'});
							dom.card.con.makeNode('subhead', 'div', {css: 'ui sub header', tag:'h4', text:'Manager'});
							dom.card.con.makeNode('label', 'div', {css: 'ui tiny lable', text:obj.manager.fname + obj.manager.lname});
							
							onComplete({dom:dom});
							
						}																												
					},
					menu: {
						subviews: {
							list: true
							, board: true
							, table: true
							, calendar: false
							, cards: true
						}
					},
					objectType:'companies',
					templates: false,
					where: {}
				};

			if(state.hasOwnProperty('viewing')) {
				
				if(state.viewing === 'headquarter') {
					
					collectionsSetup.layer = 'hq';
					
				}
				
			}
			
			if(settings) {
				
				if(settings.vendors === 1) {
					
					delete collectionsSetup.fields.type;
					delete collectionsSetup.fields.date_created;
					
					itemType = 'vendors';
					
					collectionsSetup.fields.default_product = {
							title:'Default Product Category',
							view:function(dom, obj){
								
								var type = 'No category selected';

								if(obj.default_product){
																		
									if(dom){
										dom.makeNode('type', 'div', {css: '', text:obj.default_product.name});
									}else{
										return obj.default_product.name;
									}
									
								}else{
									
									if(dom){
										dom.makeNode('type', 'div', {css: '', text:type});
									}else{
										return type;
									}
									
								}
								
							}
						};
						
					collectionsSetup.fields.markup_percent = {
							title:'Markup %',
							view:function(c, obj){

								var type = 'No markup provided';

								if(obj.markup_percent){
																		
									if(c){
										c.makeNode('type', 'div', {css: '', text:obj.markup_percent+'%'});
									}else{
										return obj.markup_percent;
									}
									
								}
							}													
						};
						
					collectionsSetup.fields.date_created = {
							title:'Created On',
							type:'date'
						};
					
					collectionsSetup.where = return_queryObj(itemType, state);
					
				} else {
					
					itemType = 'companies';
					
					collectionsSetup.where = return_queryObj(itemType, state);
					
				}	
				
			}

			sb.notify({
				type: 'show-collection',
				data: collectionsSetup
			});
			
		}
		
		function build_contactCollections(ui, state) {

			var collectionsSetup = {
					domObj:			ui,
					actions:		{
						view:false,
						create:function(ui, templateObj, onCreate){

							templateObj.onSave = onCreate;
							state.tabType = 'contacts';
							state.tagged_with = _.union(templateObj.tagged_with, state.tagged_with);
 							
 							build_crm_createFlow(ui, state, templateObj);
							
						}
						, downloadCSV:true
						, upload_csv: {
							id: 		'upload_csv'
							, headerAction: true
							, color: 	'violet'
							, title: 	'Upload'
							, icon: 	'upload'
							, action: 	function (selected, ui, onComplete) {
								
								function uploadCsvState(ui, state, draw, onComplete){
							
									function obj_mapper_view(map){
										
										// initial state
										if(map === undefined || _.isEmpty(map)){
										
											this.empty();
											
											this.makeNode('message', 'text', {text:'Please choose a file to upload', css:'text-center text-muted'});
											
											this.patch();
										
										// updates to map obj
										}else{
											
										}
										
									}
									
									ui.empty();
									
							/*
									var left = ui.makeNode('left', 'column', {width:4});
									var right = ui.makeNode('right', 'column', {width:8});
									
									// LEFT COLUMN
									left.makeNode('title', 'headerText', {text:'Upload csv file'});
									left.makeNode('form', 'form', {
										
										file: {
											name:'file',
											type:'file-upload',
											label:'Select a file'
										}
										
									});
									
									// RIGHT COLUMN
									right.update = obj_mapper_view;
									right.update();
							*/
									
									ui.patch();
									
									
											
									sb.notify({
										type:'show-csv-contact-uploader',
										data:{
											domObj:ui,
											object_type:'contacts',
											onComplete: onComplete
										}
									});
									
								}
								
								uploadCsvState(ui, {}, function(){}, function(response){
									
									onComplete(true);
									
								});
								
							}
						}
						, comments: {
							icon: 'comment'
							, color: 'purple'
							, title: 'Comments'
						}
						, invoiceStatement: {
							title: 		'Account Statement'
							, icon: 	'usd green'
							, ui: 		false
							, action: 	function(obj, dom, state, draw){
																
								sb.data.db.obj.getWhere('invoices', {
									main_contact:obj.id,
									paged:{
										count:false,
										page:0,
										pageLength:1,
										paged:true,
										sortCol:'date_created',
										sortDir:'desc',
										sortCast:'string'
									}
								}, function(invoices){
		
									invoices.data[0].main_contact = obj;
									
									sb.notify({
										type: 'app-navigate-to',
										data: {
											type: 'object',
											object: invoices.data[0]
										}
									});
									
								});
								
								
		
		/*
								var googleQuery = {
									fname:o.fname,
									lname:o.lname,
									company:'',
									location:''
								};
								var locations = [];
		
								if(o.company && o.company.hasOwnProperty('name')){
									googleQuery.company = o.company.name;
								}
																
								if(o.contact_info){
									
									var info = o.contact_info.filter(function( element ) {
										   return element != null;
										});
									                        
				                        _.each(info, function(i){
				                            
				                            if(i.type.id == 53 || i.type.data_type == 'address'){
				                                locations.push(i);
				                            }
				                            
				                            if(i.type.data_type == 'address' && appConfig.instance != 'thelifebook'){
				                                locations.push(i);
				                            }
				                            
				                        });
				 
				                        locations = locations.sort(function(a,b) { 
				                                return b.id - a.id;
				                            });
				                                            
				                        if(locations.length > 0){
				                            googleQuery.location = ' '+ locations[0].city +', '+ locations[0].state;
				                        } 
			                         }  
								
								sb.notify({
									type:'search-in-google',
									data:{
										link:`http://www.google.com/search?q=${googleQuery.fname} ${googleQuery.lname} ${googleQuery.company} ${googleQuery.location}`
									}													
								});	
		*/
																			
							}
						}
					},
					fields:			{
						fname:{
							title:'First Name',
							type:'title',
							view:function(c, obj){

								var text = 'No First Name';
								var url = '';
								
								if(obj.fname){
									
									text = `${obj.fname}`;
																		
									if(c){
										c.makeNode('type', 'div', {
											css: 'ui small header',
											text:'<nobr>'+text+'</nobr>',
											tag:'a',
											href:sb.data.url.createPageURL('object', {
												id: 					obj.id
												, name: 				text
												, object_bp_type: 	'contacts'
												, type: 				'contacts'
											})
										});
									}else{
										return text;
									}
									
								}
							},
							isSearchable:true
						},
						lname:{
							title:'Last Name',
							type:'title',
							view:function(c, obj){

								var text = 'No Last Name';
								var url = '';
								
								if(obj.fname){
									
									text = `${obj.lname}`;
																		
									if(c){
										c.makeNode('type', 'div', {
											css: 'ui small header',
											text:'<nobr>'+text+'</nobr>',
											tag:'a',
											href:sb.data.url.createPageURL('object', {
												id: 					obj.id
												, name: 				text
												, object_bp_type: 	'contacts'
												, type: 				'contacts'
											})
										});
									}else{
										return text;
									}
									
								}
							},
							isSearchable:true
						},
						type:{
							title:'Type',
							view:function(c, obj){

								var type = '';
								
								if(obj.type){
									
									type = `${obj.type.name}`;
									
									if(c){
										c.makeNode('type', 'div', {css: 'ui label', text:type});
									}else{
										return type;
									}
									
								}
							},
							type:'type'													
						},
						state:{
							title:'State',
							type:'state'
						},
						company:{
							title:'Company'
							, view: function(c, obj){
								
								var text = 'No company';
								
								if(obj.company){
									text = obj.company.name;
								}
								
								if(c){
									c.makeNode('text', 'div', {text:'<nobr>'+text+'</nobr>'});
								}else{
									return text;
								}
								
							}
							, isSearchable:true
						},
						email:{
							title:'Email',
							view:function(c, obj){
								
								var infoToShow = _.filter(obj.contact_info, function(info){
											
									if(info.type && info.type.data_type){
										return (info.type.data_type === 'email' && info.is_primary === 'yes');
									}
									
									return false;
									
								})[0];

								if (_.isEmpty(infoToShow)) {
									infoToShow = _.filter(obj.contact_info, function(info){
									
										if(info.type && info.type.data_type){
											return info.type.data_type === 'email';
										}
										
										return false;
										
									})[0];
								}
								
								if(infoToShow) {
									
									if(c){
										c.makeNode('t', 'div', {text:infoToShow.info});
									}else{
										return infoToShow.info;
									}
								
								} else {
									return '';
								}
								
							},
							type:'detail'
						},
						phone:{
							title:'Phone',
							view:function(c, obj){
								
								var infoToShow = _.filter(obj.contact_info, function(info){
									
									if(info.type && info.type.data_type){
										return (info.type.data_type === 'cellphone' || info.type.data_type === 'phone');
									}
									
									return '';
									
								})[0];
								
								if(infoToShow) {
									
									if(c){
										c.makeNode('t', 'div', {text:infoToShow.info});
									}else{
										return infoToShow.info;
									}
									
								} else {
									return '';
								}
								
							},
							type:'detail'
						},
						address:{
							title:'Address',
							view:function(c, obj){
								
								var infoToShow = _.filter(obj.contact_info, function(info){
									
									if(info.type && info.type.data_type){
										return info.type.data_type === 'address';
									}
									
									return '';
									
								})[0];

								var addressObject = {
									address: infoToShow
								}
								
								if (c) {

									sb.notify({
										type: 'view-field'
										, data: {
											type: 'address'
											, property: 'address'
											, obj: addressObject
											, isMetric: true
											, options: {
												edit: false
											}
											, ui: c.makeNode('address', 'div', {})
										}
									});

								}
								
							},
							type:'detail'
						},
						// Lead Source Contact Infinity - Dreams - NLP
						lead_source: {
							title: 'Lead Source'
							, type: 'title'
							, isSearchable: true
						},
						value:{
							title:'Value',
							view:function(c, obj) {
								
								if(c){
									
									sb.notify ({
										type: 'view-field',
										data: {
											type: 'currency',
											property: 'value',
											obj: obj,
											ui: c.makeNode('t', 'div', {}),
											options: {
												edit: false,
												editing: false
											}
										}
									});
									
								}else{
									
									return (obj.value/100).formatMoney();
									
								}
								
							},
							type:'detail'
						},
						contact_info: {
							isSearchable:true
							, isHidden:true
						},
						date_created:{
							title:'Created On'
						}
					},
					groupings		:{
						state:'State'
					},
					metrics:{
						sum:{
							title:'Total',
							fields:{
								value:function(ui, getData, test, data){

									var totalValue = 0;
									
									if(_.isArray(data)){
									
										_.each(data, function(inv){										

											totalValue += inv.value;
											
										});
										
									} else {
										
										totalValue = data;
										
									}	
									
									if(!_.isUndefined(ui) && !_.isNull(ui)){
										
										ui.empty();
										ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>'+ sb.dom.formatCurrency(totalValue) +'</b>'});
										ui.patch();	
																					
									}	
									
									return '<b>'+ sb.dom.formatCurrency(totalValue) +'</b>';							
							
								}
							}
						}
					},
					objectType:		'contacts',
					singleView:		{
						view:function(ui, obj, draw){
							contactView(obj, ui, {}, draw);
						},
						select:3
					},
					subviews: {
						table: {},
						board: {
							range: {
								defaultTo: 'next_3_months',
								not: ['all_time']
							},
							groupBy: {
								defaultTo: 'state'
							}
						},
						list: {}
					},
					pageLength:		50,
					templates: 		false
					, menu: {
						subviews: {
							calendar: false
							, board: true
							, table: true
						}
					}
					, where: return_queryObj('contacts', state)
				};


			var urlParams = checkURL();
			var config = {};
			
			function checkURL() {
			
				var url = window.location.href.split('!');
				var params = {};
				
				_.each(url, function(urlpart, i) {
	
					if(i !== 0) {
						
						urlpart = urlpart.split(':');
						
						_.each(urlpart, function(v, k) {
							
							params[urlpart[0]] = urlpart[1];
							
						});
						
					}
					
				});
	
				return params;
				
			}
				
			if(state.hasOwnProperty('viewing')) {
				
				if(state.viewing === 'headquarter') {
					
					collectionsSetup.layer = 'hq';
					
				}
				
			}
			
			if(urlParams.type) {
				
				config.type = urlParams.type;
				
			}
			
			if(!_.isEmpty(config)) {
				collectionsSetup.config = config;
			}

			if (
				appConfig.instance === 'foundation_group'
				//|| appConfig.instance === 'rickyvoltz'
			) {
				//remove lead source from fields
				delete collectionsSetup.fields.lead_source;
			}

			sb.notify({
				type:'show-collection',
				data: collectionsSetup
			});				
			
		}
		
		function build_crm_tabs(ui, state, onContact) {

			var tab1 = 'Contacts'; // Churches
			var tab2 = 'Companies'; // Pastors
			var tab3 = 'Vendors';
			var instance = check_instance();
			var activeOnContact = '';
			var activeOnCompanies = '';

			if(onContact || onContact === undefined) {
				
				activeOnContact = 'active';
				
			} else {
				
				activeOnCompanies = 'active';
				
			}

			if(instance === false && !_.isObject(instance)) {
				
				tab1 = 'Contacts';
				tab2 = 'Companies';
				tab3 = 'Vendors';
				
			} else {
				
				tab1 = instance.tab1;
				tab2 = instance.tab2;
				tab3 = '';
				
			}
			
			var contactSetup = {
				node: 'contacts_item'
				, css: 'item '+ activeOnContact +' crmTab'
				, text: '<i class="address book outline icon"></i> ' + tab1
				, run: function(data) {
							
					$('.crmTab').removeClass('active');
					$('.crmTab').css('color', 'black');
					$(data.selector).addClass('active');
					$(data.selector).css('color', '#027eff');
					
					build_contactCollections(ui.wrapper.seg, state);
					
				}
			};
			var companySetup = {
				node: 'companies_item'
				, css: 'item '+ activeOnCompanies +' crmTab'
				, text: '<i class="building outline icon"></i> ' + tab2
				, run: function(data) {
							
					$('.crmTab').removeClass('active');
					$('.crmTab').css('color', 'black');
					$(data.selector).addClass('active');
					$(data.selector).css('color', '#027eff');
					
					build_companiesCollections(ui.wrapper.seg, state
						, {
							vendors: 0
						}
					);
					
				}
			};
			
			var firstTabSetup = contactSetup;
			var secondTabSetup = companySetup;

			if ( appConfig.instance == 'foundation_group' ) {
				firstTabSetup = companySetup;
				secondTabSetup = contactSetup;
			}

			ui.wrapper.menu.makeNode(firstTabSetup.node, 'div', {
				css: firstTabSetup.css,
				text: firstTabSetup.text,
				style: 'cursor: pointer;'
			});
			
			ui.wrapper.menu[firstTabSetup.node].notify('click', {
				type: 'crm-run',
				data: {
					run: firstTabSetup.run,
					selector: ui.wrapper.menu[firstTabSetup.node].selector 
				}
			}, sb.moduleId);
			
			ui.wrapper.menu.makeNode(secondTabSetup.node, 'div', {
				css: secondTabSetup.css,
				text: secondTabSetup.text,
				style: 'cursor: pointer;'
			});
			
			ui.wrapper.menu[secondTabSetup.node].notify('click', {
				type: 'crm-run',
				data: {
					run: secondTabSetup.run,
					selector: ui.wrapper.menu[secondTabSetup.node].selector 
				}
			}, sb.moduleId);

				
						
			if(tab3 !== '') {
				
				ui.wrapper.menu.makeNode('vendors_item', 'div', {
					css: 'item crmTab',
					text: '<i class="dolly icon"></i> ' + tab3,
					style: 'cursor: pointer;'
				}).notify('click', {
					type: 'crm-run',
					data: {
						run: function(data) {
							
							$('.crmTab').removeClass('active');
							$('.crmTab').css('color', 'black');
							$(data.selector).addClass('active');
							$(data.selector).css('color', '#027eff');
							
							build_companiesCollections(ui.wrapper.seg, state, {
								vendors: 1
							});
							
						},
						selector: ui.wrapper.menu.vendors_item.selector
					}
				}, sb.moduleId);
				
			}
			
		}
		
		if ( appConfig.instance == 'foundation_group' )
			foundationGroup = true;
			
		ui.empty();
		
		ui.makeNode('wrapper', 'div', {});
		
		//ui.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
		
		// crm tabs area
		ui.wrapper.makeNode('menu', 'div', {
			css: 'ui secondary stackable blue menu'
		});
		
		// Main crm ui body
		ui.wrapper.makeNode('seg', 'div', {
			css: 'ui bottom attached active tab basic segment'
		});

		// Controls crm tabs
		build_crm_tabs(ui, state, onContact);

		if(onContact || onContact === undefined) {
			
			setContactLeadSource(ui.wrapper.seg, function() {
				
				build_contactCollections(ui.wrapper.seg, state);
				
			});		
			
		} else {
			
			build_companiesCollections(ui.wrapper.seg, state, {});
			
		}
		
		ui.patch();
		
	}
	
	//contacts
	function contactView(obj, dom, state, draw){

		var denominationString = '';
		var salespersonString = '';
		var editButton = true;
		var collapse = 'closed';
		var contactInfoCollapse = true;
		var denomination = [];
		var object = state.object = obj;
		var toolCollapse = false;
		
		function editContact(obj, dom, state, draw){
			
			state.editing = true;
			var onBack = function(){
				
				sb.notify({
					type: 'app-redraw',
					data: {}
				}, sb.moduleId);
				
			};
			
			function update(data, company){
	
				data.dom.btns.save.loading();
							
				var form = data.form.process().fields,
					formIsComplete = data.form.process();
	
				if(formIsComplete.completed === false){
					
					data.dom.btns.save.loading(false);
					
					sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
					
					return
					
				}
				
				var contact = {
						id: +form.id.value,
						fname:form.fname.value,
						lname:form.lname.value,
						manager: +form.manager.value,
						sales_person: +form.sales_person.value,
						type: +form.type.value
					},
					contactInfo = [];
				var updCompany;	
	
				if(data.contactInfoForm){
				
					contactInfoForm = data.contactInfoForm.process().fields;
	
					_.each(data.object.contact_info, function(info){
						
						if(info){
							
							if(info.street){
								
								var formValues = data.addressForms['cont-'+info.id].cont.form.process().fields
	
								contactInfo.push({
									id:info.id,
									info:formValues['street-'+info.id].value,
									street:formValues['street-'+info.id].value,
									city:formValues['city-'+info.id].value,
									state:formValues['state-'+info.id].value,
									zip:formValues['zip-'+info.id].value,
									country:formValues['country-'+info.id].value
								});
								
							}else{
								
								if(contactInfoForm['info-'+info.id]){
									contactInfo.push({
										id:info.id,
										info:contactInfoForm['info-'+info.id].value
									});	
								}
								
							}
							
						}
						
					});
					
				}
	
				if(!_.isNull(company) && !_.isUndefined(company)){
	
					updCompany = company;
	
					if(!(company.name === form.company.value)){
						updCompany.name = form.company.value;
					}
					
					contact.company = updCompany;
					
				}
			
				sb.data.db.obj.update('contact_info', contactInfo, function(updatedInfo){	
					
					sb.data.db.obj.update('companies', updCompany, function(updatedCompany){
					
						sb.data.db.obj.update('contacts', contact, function(updated){
	
							updated.contact_info = updatedInfo;
							updated.company = updatedCompany;
							
							obj = updated;
							
							if(data.state){
								data.state.object = updated;
							}
							
							//if(+sb.data.cookie.userId == 11){
							if(appConfig.instance == 'thelifebook'){
								
								var mailchimpMember = {
										listId:'5983ed1a31',
										member:{
											email_address:'',
											status:'subscribed',
											merge_fields:{
												FNAME:obj.fname,
												LNAME:obj.lname,
												SHIPPING:{},
												PHONE:'',
												CHURCHPHON:'',
												CHURCHADDR:{},
												ADULTS:0,
												STUDENTS:0,
												REQUESTS:1,
												TYPE:obj.type.name,
												DENOM:'',
												ZIP:'',
												MANAGER:obj.manager.fname +' '+ obj.manager.lname,
												RECENT:'',
												RECENTSIZE:0
											}
										}
									};
									
								_.each(obj.contact_info, function(info){
									
									if(info.is_primary == 'yes'){
										
										switch(info.type.data_type){
											
											case 'other':
												
												switch(info.title){
													
													case 'Adults':
														
														mailchimpMember.member.merge_fields.ADULTS = +info.info;
														
														break;
														
													case 'Students':
														
														mailchimpMember.member.merge_fields.STUDENTS = +info.info;
														
														break;
														
													case 'Denomination':
														
														mailchimpMember.member.merge_fields.DENOM = info.info;
														
														break;		
													
												}
											
												break;
																						
											case 'email':
											
												mailchimpMember.member.email_address = info.info;
											
												break;
																																
											case 'address':
												
												if(info.name == 'Shipping Address'){
													
													//orderObj.order.customer.merge_fields.SHIPPING = info.street +', '+ info.city +', '+ info.state +' '+ info.zip;
													mailchimpMember.member.merge_fields.SHIPPING.addr1 = info.street;
													mailchimpMember.member.merge_fields.SHIPPING.addr2 = '';
													mailchimpMember.member.merge_fields.SHIPPING.city = info.city;
													mailchimpMember.member.merge_fields.SHIPPING.state = info.state;
													mailchimpMember.member.merge_fields.SHIPPING.zip = info.zip;
													
													
													mailchimpMember.member.merge_fields.ZIP = info.zip;
												}
												
												if(info.name == 'Church Address'){
													
													//orderObj.order.customer.merge_fields.CHURCHADDR = info.street +', '+ info.city +', '+ info.state +' '+ info.zip;
													mailchimpMember.member.merge_fields.CHURCHADDR.addr1 = info.street;
													mailchimpMember.member.merge_fields.CHURCHADDR.addr2 = '';
													mailchimpMember.member.merge_fields.CHURCHADDR.city = info.city;
													mailchimpMember.member.merge_fields.CHURCHADDR.state = info.state;
													mailchimpMember.member.merge_fields.CHURCHADDR.zip = info.zip;
													
												}
											
												break;	
												
											case 'phone':
												
												if(info.name == 'Cell Phone'){
													
													mailchimpMember.member.merge_fields.PHONE = info.info;
													
												}
												
												if(info.name == 'Church Phone'){
													
													mailchimpMember.member.merge_fields.CHURCHPHON = info.info;
													
												}
											
												break;		
											
										}
									
									}
									
								});	
		
								sb.data.db.controller('updateMailchimpListMember', mailchimpMember, function(done){
									
									if(data.draw){
										contactView(updated, data.dom, data.state, data.draw);
									}else{
										sb.notify({
											type:'app-redraw',
											data:{}
										});
									}			
									
								});
								
							}else{
								
								if(data.state.hasOwnProperty('appSettings') && data.state.appSettings.apps.integrations.mailchimp){
									
									if(data.state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){
									
										sb.data.db.controller('updateSingleContactWithMailchimp', {id:updated.id}, function(done){
											
											if(done.status == 404){
																							
												if(data.draw){
													contactView(updated, data.dom, data.state, data.draw);
												}else{
													sb.notify({
														type:'app-redraw',
														data:{}
													});
												}
												
											}else{
												
												if(data.draw){
													contactView(updated, data.dom, data.state, data.draw);
												}else{
													sb.notify({
														type:'app-redraw',
														data:{}
													});
												}
												
											}
											
										});
									
									}else{
										
										if(data.draw){
											contactView(updated, data.dom, data.state, data.draw);
										}else{
											sb.notify({
												type:'app-redraw',
												data:{}
											});
										}
										
									}
									
								}else{
		
									if(data.state.hasOwnProperty('onSave')){
	
										data.state.object = updated;
										
										data.state.onSave.call(null, updated, data.dom, data.state, data.draw);
										
									} else {
																
										sb.notify({
											type:'app-redraw',
											data:{}
										});
									}
											
								}
								
							}
													
						}, 2);
					
						
					});
									
				}, 2);			
			}			
			
			if(state.hasOwnProperty('onBack')){
				state.editing = false;
				onBack = state.onBack.bind(null, obj, dom, state, draw);
			}	
			
			dom.empty();
	
			dom.makeNode('header', 'div', {text: 'Editing '+ state.object.fname +' '+ state.object.lname, css:'ui huge header'});
	
			dom.makeNode('btns', 'div', {css:'ui buttons'}).makeNode('back', 'button', {css:'pda-btnOutline-red', text:'<i class="fa fa-times"></i> Close'}).notify('click', {
				type:'contactComponent-run',
				data:{
					run:onBack
				}
			}, sb.moduleId);
			
			dom.makeNode('btnBreak', 'div', {text:'<br />'})
			
			dom.makeNode('cols', 'div', {css:'ui stackable grid'});
			
			dom.cols.makeNode('col1', 'column', {w:8});
			dom.cols.makeNode('col2', 'column', {w:8});
			dom.cols.makeNode('col3', 'column', {w:16});
			
			dom.cols.col1.makeNode('header', 'div', {text:'Basic Info', css:'ui medium header'});
			dom.cols.col1.makeNode('cont', 'div', {css:'ui orange fluid card'})
				.makeNode('cont', 'div', {css:'content'});
	
			dom.cols.col2.makeNode('header', 'div', {text:'Contact Info', css:'ui medium header'});
			dom.cols.col2.makeNode('cont', 'div', {css:'ui orange fluid card'})
				.makeNode('cont', 'div', {css:'content'});
				
			dom.cols.col3.makeNode('header', 'div', {text:'Address Info', css:'ui medium header'});
			dom.cols.col3.makeNode('col', 'div', {css:'ui stackable fluid cards'});
									
			dom.makeNode('endBreak', 'div', {text:'<br /><br />'});
																	
			dom.patch();
			
			sb.data.db.obj.getAll('users', function(users){
	
				sb.data.db.obj.getAll('contact_types', function(types){	
	
					sb.data.db.obj.getWhere('contact_info', {object_id:state.object.id, childObjs:3}, function(contactInfo){
	
						state.object.contact_info = contactInfo;
						
						var managerId = 0;
						var salesId = 0;
						var typeId = 0;
						var selectedCompany;
						var companyName = '';			
	
						
						if(state.object.manager){
							managerId = state.object.manager.id;
						}
						
						if(state.object.sales_person){
							salesId = state.object.sales_person.id;
						}
						
						if(state.object.type){
							typeId = state.object.type.id;
						}
	
						if(state.object.company){
							selectedCompany = state.object.company;
							if(selectedCompany.hasOwnProperty('name')){
								companyName = selectedCompany.name;
							}
						}
											
						var formObj = {
								fname:{
									type:'text',
									name:'fname',
									label:'First Name',
									value:state.object.fname
								},
								lname:{
									type:'text',
									name:'lname',
									label:'Last Name',
									value:state.object.lname
								},
								type:{
									type:'select',
									name:'type',
									label:'Type',
									options:[],
									value:typeId
								},
								sales_person:{
									type:'select',
									name:'sales_person',
									label:'Sales Person',
									options:[],
									value:salesId
								},
								manager:{
									type:'select',
									name:'manager',
									label:'Manager',
									options:[],
									value:managerId
								},
								company:{
									type:'text',
									name:'company',
									label:'Company',
									value:companyName
								},
								id:{
									type:'hidden',
									name:'id',
									value:state.object.id
								}
							},
							newManagerOptions = [];
	
						formObj.sales_person.options.push({
							value:0,
							name:'Select One'
						});	
							
						_.each(types, function(t){
	
							if (t) {
								
								var add = {
										value:t.id,
										name:t.name
									};
																	
								formObj.type.options.push(add);
								
							}
							
						});
						
						var adding = [];
						var salesManagers = [];
						
						_.each(users, function(u){
							
							var add = {
									value:u.id,
									name:u.fname +' '+ u.lname
								},
								salesManager = {
									value:u.id,
									name:u.fname +' '+ u.lname
								};
	
							if(add.name == 'undefined' || add.name == 'undefined undefined' || add.name == undefined || add.name == null){
								
								//add.name = 'Not Selected';
								
							}
	
							if(state.object.manager){
							
	/*
								if(u.id == state.object.manager.id){
	
									add.selected = true;
									
								}
	*/
								
	/*
								if(appConfig.instance == 'thelifebook'){
									
									if(u.id == state.object.manager.related_object.id){
	
										add.selected = true;
										
									}
									
								}
	*/
							
							}
	
							if(state.object.sales_person){
							
								if(u.id == state.object.sales_person.id){
	
									salesManager.selected = true;
									
								}
							
							}
							
							adding.push(add);
							salesManagers.push(salesManager);
							
						});
	
						formObj.manager.options = formObj.manager.options.concat(adding);
						formObj.sales_person.options = formObj.sales_person.options.concat(salesManagers);
	
						if(_.find(formObj.manager.options, {name: 'Not Selected'})){
							
							newManagerOptions = _.chain(formObj.manager.options)
								.filter(function(opt){return opt.name != 'Not Selected'})
								.sortBy('name')
								.value();
							
							newManagerOptions.unshift(_.find(formObj.manager.options, {name:'Not Selected'}));
														
						}else{
							
							newManagerOptions = _.sortBy(formObj.manager.options, 'name');
														
						}
												
						formObj.manager.options = newManagerOptions;
																		
						if(state.object.contact_info){
							
							var infoForm = {},
								addressForms = [];
							
							_.each(state.object.contact_info, function(o){
	
								if(o){
									
									if(o.street){
										
										var stateVal = _.findWhere(sb.data.stateArray, {name:o.state});
										if(!stateVal){
											stateVal = _.findWhere(sb.data.stateArray, {value:o.state});
										}
										
										if(!stateVal){
											stateVal = sb.data.stateArray[0];
										}
										
										addressForms.push(
											{
												infoId:o.id,
												info:o,
												form:{
													street:{
														type:'text',
														name:'street-'+o.id,
														label:'Street',
														value:o.street
													},
													street2:{
														type:'text',
														name:'street2-'+o.id,
														label:'Apartment/Unit Number',
														value:o.street2
													},
													city:{
														type:'text',
														name:'city-'+o.id,
														label:'City',
														value:o.city
													},
													state:{
														type:'state',
														name:'state-'+o.id,
														label:'State',
														value:stateVal.value
													},
													zip:{
														type:'text',
														name:'zip-'+o.id,
														label:'Zip',
														value:o.zip
													},
													country:{
														type:'text',
														name:'country-'+o.id,
														label:'Country',
														value:o.country
													}
													
												}
											}
										);
										
									}else{
	
										if(o.type){
											
											if(o.is_primary == 'yes'){
												var primary = ' (primary)';
											}else{
												var primary = '';
											}
																						
											infoForm['info-'+o.id] = {
												type:'text',
												name:'info-'+o.id,
												label:o.type.name + primary,
												value:o.info
												
											};
										}												
										
									}
									
								}			
								
							});
							
						}				
																			
						dom.cols.col1.cont.cont.makeNode('form', 'form', formObj);
						
						if(state.object.contact_info){
							if(state.object.contact_info.length > 0){
								dom.cols.col2.cont.cont.makeNode('form', 'form', infoForm);
							}else{
								dom.cols.col2.cont.cont.makeNode('noInfo', 'div', {css:'ui small header', text:'No contact info'});
							}
						}else{
							dom.cols.col2.cont.cont.makeNode('noInfo', 'div', {css:'ui small header', text:'No contact info'});
						}
						
						if(addressForms.length > 0){
							
							_.each(addressForms, function(form){
								
								var primaryText = '';
								if(form.info.is_primary == 'yes'){
									primaryText = 'Primary ';
								}
								
								dom.cols.col3.col.makeNode('cont-'+form.info.id, 'div', {css:'ui orange fluid card'})
										.makeNode('cont', 'div', {css:'content'});
	
								dom.cols.col3.col['cont-'+form.info.id].cont.makeNode('header-'+form.infoId, 'div', {css:'ui small header', text:primaryText+form.info.type.name});
								
								dom.cols.col3.col['cont-'+form.info.id].cont.makeNode('form', 'form', form.form);
								
							});
							
						}else{
							
							dom.cols.col3.col.makeNode('cont', 'div', {css:'ui orange fluid card'})
								.makeNode('cont', 'div', {css:'content'})
									.makeNode('noInfo', 'div', {text:'No address info', css:'ui small header'});
							
						}
																		
						dom.cols.col1.cont.cont.form.makeNode('changeCompany', 'div', {
							css: 'ui fluid search item field',
							text:
								/*'<label>Company</label>'+ */
								'<div class="ui icon fluid input">'+
									'<input class="prompt" type="text" placeholder="Select New Company" style="border-radius:.28571429rem;">'+
									'<i class="search icon"></i>'+
								'</div>'+
								'<div class="results"></div>',
							listener:{
								type: 'search',
								objectType: 'companies',
								onSelect: function(result, response){
	
									if (!_.isEmpty(result)) {
	
										selectedCompany = result;
										dom.cols.col1.cont.cont.form.company.update({value:result.name});
										
									}
									
								},
								category: 'type',
								selection:{
									name:true,
									type:{
										name:true
									}
								},
								onResponse: function(raw){
	
								    var response = {
									    results : {}
								    };
								    
								    _.each(raw.results, function(item){
	
								    		///null undefined check for company catagory
										if(!_.isNull(item.type) && !_.isUndefined(item.type)){
										
											///push to results group else create new
										    if ( response.results.hasOwnProperty(item.type.id) ) {
											
											    response.results[item.type.id].results.push(item);
										    
										    }else{
											    
											    response.results[item.type.id]
											    	
											    	= {
												    	name : item.type.name,
												    	results : [item]
											    	};
											    	
										    }										
											
										} else {
											
											///company does not have a type. push to none else create
										    if ( response.results.hasOwnProperty('none') ) {
											
											    response.results['none'].results.push(item);
										    
										    }else{
											    
											    response.results['none']
											    	
											    	= {
												    	name : '&#160&#160',
												    	results : [item]
											    	};
											    	
										    }
											
										}
																	    
								    });
	
								    return response;
								    
							    }
							}
						});
	
						dom.btns.makeNode('save', 'button', {css:'pda-btn-green', text:'<i class="fa fa-check"></i> Save'}).notify('click', {
							type:'crm-run',
							data:{
								run:function(){
									
									update({
										form:dom.cols.col1.cont.cont.form,
										contactInfoForm:dom.cols.col2.cont.cont.form,
										addressForms:dom.cols.col3.col,
										object:state.object,
										dom:dom,
										state:state,
										draw:draw
									}, selectedCompany)
																		
								}
							}						
							
						}, sb.moduleId);	
																		
						dom.patch();
						
					});
				
				}, {
					name:true
				});
			
			}, {
				fname:true, 
				lname:true
			});
			
		}	
		function headerView(dom, pageObj, project){

			var objName = pageObj.name;
			if(pageObj.object_bp_type == 'contacts' || pageObj.object_bp_type == 'users'){
				objName = `${pageObj.fname} ${pageObj.lname}`;
			}
			var portalMenuOptions = {
				button: {
					style: '<i class="grey key icon"></i> Create Portal'
					, action: function(){

							sb.notify({
								type: 'get-sys-modal'
								, data: {
									callback: function (modal) {
																			
										modal.body.empty();
										modal.show();
										createClientPortal(
											pageObj
											, modal.body
											, state
											, function () {

												modal.hide();
												
											}
											, 'contact'
										);										
									
									},
									onClose: function () {

										setTimeout(function(){
											
											window.location.reload();
											
										}, 1000);
																		
										
									}
								}
							});										
					}
				}
			};

			if ( pageObj.token && pageObj.token.length > 0 ) {
				portalMenuOptions.button.style = '<i class="yellow key icon"></i> Revoke Portal'
			}

			var subHeader = pageObj.company.name;
			
			function manager_ui (ui, manager) {

				function staffPopup(dom, staff, isPointPerson){
					
					dom.makeNode('pop', 'modal', {
						size:'mini',
						onShow:function(){
							
							sb.data.db.obj.getById('users', staff.id, function(staff){
							
								if(staff.profile_image.id){
									
									dom.pop.body.makeNode('image', 'div', {tag:'img', css:'ui small circular centered image', src:sb.data.files.getURL(staff.profile_image)});
									
								}
								
								var nickName = '';
								if(staff.nick_name){
									nickName = '<br /><small><small><i>nick name: '+ staff.nick_name +'</i></small></small>';
								}
			
								dom.pop.body.makeNode('title', 'div', {text:staff.fname +' '+ staff.lname + nickName, css:'ui huge centered header'});
								
								if(isPointPerson === true){
									dom.pop.body.makeNode('pp', 'div', {style:'text-align:center;'})
										.makeNode('pointPerson', 'div', {text:'Point Person', css:'ui blue label'});						
								}
								
								dom.pop.body.makeNode('email', 'div', {text:staff.email, css:'ui centered header'});
								dom.pop.body.makeNode('phone', 'div', {text:sb.dom.formatPhone(staff.phone) , css:'ui centered header'});
								
								dom.pop.body.patch();
							
							}, 2);
							
						}
					});
					
					dom.patch();
					
					dom.pop.show();
					
				}
				
				ui.makeNode('man', 'div', {
					tag:'h5', 
					css: 'ui header', 
					style:'margin-top:0px;display:inline-block;cursor:pointer;'
				}).makeNode('icon', 'div', {
					tag:'i', 
					css: 'user circle icon'
				});
				
				ui.man.makeNode('content', 'div', {
					css: 'content', 
					text:`${manager.fname} ${manager.lname}<div class="sub header"> Manager</div>`
				}).notify('click', {
					type:'headquarters-run',
					data:{
						run:function(){
							staffPopup(ui.man, manager, true)
						}
					}
				}, sb.moduleId)
				
				return ui;
			}			
			
			dom.makeNode('seg', 'div', {css: 'ui basic segment'});

			dom.seg.makeNode('edit', 'div', {
				text:'<i class="ellipsis horizontal icon" style="font-size:1.5em !important;"></i>', 
				style:'float:right;', 
				css:'ui right floated simple dropdown icon'});
			dom.seg.edit.makeNode('menu', 'div', {css:'left menu'});		
			dom.seg.edit.menu.makeNode('change', 'div', {css:'item', text:'<i class="user times orange icon"></i> Remove from this project'}).notify('click', {
				type:'contactComponent-run',
				data:{
					run: function(data) { 

						var oldMainContact = project.main_contact;
						project.main_contact = 0;
						
						sb.data.db.obj.update('groups', {
							id: 			project.id, 
							main_contact: 	project.main_contact
						}, function(updated) {

							sb.data.db.obj.removeTag(
								project.id
								, oldMainContact.id
								, function (response) {

									sb.notify({
										type:'app-navigate-to',
										data:{
											type:'UP'
										}
									});

								}
							);
							
						}, 1);
						
					} 
				}
			}, sb.moduleId);
			
			dom.seg.edit.menu.makeNode('portal', 'div'
				, {
					css:'item'
					, text: portalMenuOptions.button.style
				}).notify('click', {
					type:'contactComponent-run',
					data:{
						run: portalMenuOptions.button.action
					}
				}, sb.moduleId);
						
			dom.seg.makeNode('header', 'div', {
				style: 'margin-bottom:14px;', 
				tag: 'h1', 
				css: 'ui header', 
				text: objName
			});

			if(pageObj.company){

				dom.seg.makeNode('subh', 'div', {
					tag:'h4',
					style:'display:block; margin:0 0 14px 0;',
					css: 'ui header', 
					text:'<a href="'+ sb.data.url.getObjectPageParams(obj.company) +'">'+ obj.company.name +'</a>'
				});

			}
			
			if(pageObj.manager){
				manager_ui(dom.seg, pageObj.manager);
			}
			
			dom.seg.makeNode('state', 'div', {});
			dom.seg.makeNode('div', 'div', {css:'ui clearing hidden divider'});
								
			dom.seg.makeNode('tags', 'div', {});
			dom.seg.makeNode('div', 'div', {css:'ui clearing hidden divider'});		
				
			return dom;
		}
		
		contactView.edit = editContact;
		
		if(onMobile){
			
			contactInfoCollapse = 'closed';
			
		} 

		state.editing = false;

		if(state.hasOwnProperty('edit'))
			editButton = state.edit;
					
		dom.empty();
		dom.makeNode('modal','modal',{css:'ui modal'});
		dom.patch();

		dom.makeNode('alertContainer', 'column', {css:''});
		
		if(state.hasOwnProperty('project')){

			if(state.project.main_contact){
				
				headerView(dom.alertContainer, object, state.project);
				
			} else {
				
				searchForAContact(dom.alertContainer, state, draw);
			}
		}
		
		dom.alertContainer.makeNode('labels', 'div', {css: 'ui blue labels'});			
		
		if(obj.sales_person){
			
			if(obj.sales_person.hasOwnProperty('fname')){
				
				salespersonString = '<br /><small><small>Sales Person:</small> '+ obj.sales_person.fname +' '+ obj.sales_person.lname +'</small>';
				
				dom.alertContainer.labels.makeNode('vendor', 'div', {css: 'ui label', text:salespersonString});				
				
			}			

		}

		if(obj.contact_info){

			var info = obj.contact_info.filter(function( element ) {
				   return element != null;
				});
			                        
               _.each(info, function(i){
                       
                  if(i.type.id == 1354 && i.object_id == obj.id)
                      denomination.push(i);
                       
               });

               if(!_.isEmpty(denomination)){

	               denomination = denomination.sort(function(a,b) { 
	                   return b.id - a.id;
	               });
	             
					denominationString = 'Denomination:'+ denomination[0].info +'</small>';

	               dom.alertContainer.labels.makeNode('denom', 'div', {css: 'ui label', text:denominationString});
	                
	          }     	
			
		}		
		
		// Portal dashboard setup
		dom.makeNode(
			'portal'
			, 'div'
			, {
				css: 'ui container'
			}
		).makeNode('s', 'div', {
			css: 'ui basic segment'
		});
		
		dom.makeNode(
			'p-br'
			, 'div'
			, {
				text: '<br />'
			}
		);
				
		dom.makeNode('grid', 'div', {
			css: 'ui stackable grid mobile reversed',
		});
		
		dom.grid.makeNode('left', 'div', {
			css: 'twelve wide column mobile-padding-bottom-2',
		});
		
		dom.grid.makeNode('right', 'div', {
			css: 'four wide column mobile-padding-bottom-2',
		});
		
		// Create tabs
		var style = sb.dom.isMobile ? 'flex-wrap: wrap;' : '';
		
		dom.grid.left.makeNode('tabs', 'div', {
			css: 'ui top attached tabular menu',
			style: style + 'flex-wrap: wrap;'
		});
		
		// Create tab
		dom.grid.left.makeNode('tab', 'div', {
			css:'ui bottom attached active tab segment',
		});
		
		// Status
		dom.grid.right.makeNode('statusContainer', 'div', {
			css: 'round-border',
			style: 'background-color: white !important; padding:1rem;'
		});
		dom.grid.right.statusContainer.makeNode('statusHeader', 'div', {
			css: 'ui grey sub header',
			style: 'margin-bottom:8px;',
			text: '<i class="question circle icon"></i> Status'
		});	
		dom.grid.right.statusContainer.makeNode('statusBody', 'div', {});
		
		sb.notify({
			type: 'view-field',
			data: {
				type:'state',
				property:'state',
				ui:dom.grid.right.statusContainer.statusBody,
				obj:obj,
				options:{
					fluid:true,
					edit:true
				}
			}
		});	
		
		// Type
		dom.grid.right.makeNode('typeContainer', 'div', {
			css: 'round-border',
			style: 'background-color: white !important; padding:1rem; margin-top:15px;'
		});
		dom.grid.right.typeContainer.makeNode('typeHeader', 'div', {
			css: 'ui grey sub header',
			style: 'margin-bottom:8px;',
			text: '<i class="folder open icon"></i> Type'
		});	
		dom.grid.right.typeContainer.makeNode('typeBody', 'div', {
			css: 'dropdown-round-border',
			style:'padding-top:0 !important; padding-left:0 !important; padding-bottom:0px !important;'
		});
		
		sb.notify({
			type: 'view-field',
			data: {
				type:'type',
				property:'type',
				ui: dom.grid.right.typeContainer.typeBody,
				obj:obj,
				options: {
					edit:true
				}
			}
		});
		
		// Managers
		dom.grid.right.makeNode('managersContainer', 'div', {
			css: 'round-border',
			style: 'background-color: white !important; padding:1rem; margin-top:15px;'
		});
		dom.grid.right.managersContainer.makeNode('managersHeader', 'div', {
			css: 'ui grey sub header',
			style: 'margin-bottom:8px;',
			text: '<i class="users icon"></i> Manager'
		});	
		dom.grid.right.managersContainer.makeNode('managersBody', 'div', {
			style: 'padding-bottom:5px;'
		});
		sb.notify({
			type: 'view-field',
			data: {
				type: 'users',
				property: 'manager',
				obj: obj,
				ui: dom.grid.right.managersContainer.managersBody,
				options: {
					edit:true,
					shouldTag:true
				}
			}
		});	
		
		// Tags
		dom.grid.right.makeNode('tagsContainer', 'div', {
			css: 'round-border',
			style: 'background-color: white !important; padding:1rem; margin-top:15px;'
		});
		
		dom.grid.right.tagsContainer.makeNode('header', 'div', {
			css: 'ui grey sub header',
			text: '<i class="tags icon"></i> Tags'
		});
		
		dom.grid.right.tagsContainer.makeNode('tags', 'div', {});
		
		// Contact info
		dom.grid.right.makeNode('contactInfoContainer', 'div', {
			css: 'round-border',
			style: 'background-color: white !important; padding:1rem; margin-top:15px;'
		});
		dom.grid.right.contactInfoContainer.makeNode('contactInfoHeader', 'div', {
			css: 'ui grey sub header',
			text: '<i class="id card icon"></i> Contact Info'
		});	
		dom.grid.right.contactInfoContainer.makeNode('contactInfoBody', 'div', {});

		// Value
		dom.grid.right.makeNode('valueContainer', 'div', {
			css: 'round-border',
			style: 'background-color: white !important; padding:1rem; margin-top:15px;'
		});
		dom.grid.right.valueContainer.makeNode('valueHeader', 'div', {
			css: 'ui grey sub header',
			text: '<i class="dollar usd icon"></i> Value'
		});	
		dom.grid.right.valueContainer.makeNode('valueBody', 'div', {});
		sb.notify ({
			type: 'view-field',
			data: {
				type: 'currency',
				property: 'value',
				obj: obj,
				ui: dom.grid.right.valueContainer.valueBody,
				options: {
					edit: true,
					editing: true,
					commitUpdates: true
				}
			}
		});
				
		// Removing un-necessary tools
		var contactDashboardTools = _.filter(appConfig.objectTools, function(o) {
			return o.type !== 'tool';
		});
		
		var activeTab = 'active';
		
		if(appConfig.instance == 'thelifebookno' || appConfig.instance == 'thelifebookau' || appConfig.instance == 'rickyvoltz'){
			
			activeTab = '';
			
			dom.grid.left.tabs.makeNode('lifebookinternational', 'div', {
				css: 'ui item active',
				text: '<i class="book icon"></i> Life Book Orders'
			}).notify('click', {
				type: 'crm-run',
				data:{
					run:function() {
						
						// Remove the active status from old tab
						$(dom.grid.left.tabs.selector + ' .item').removeClass('active');

						// Add the active status to the new tab
						$(dom.grid.left.tabs.lifebookinternational.selector).addClass('active');
						
						sb.notify({
							type: 'lifebookinternational-show-single-object',
							data: {
								dom:dom.grid.left.tab,
								contact:obj
							}
						});
						
					}
				}		
			});
			
		}
		
		if(appConfig.instance == 'rickyvoltz' || appConfig.instance == 'thelifebook'){
			
			var requestsTool = _.find(contactDashboardTools, function(tool){ return tool.id == 'requestsTool'; });

			contactDashboardTools = _.reject(contactDashboardTools, function(tool){ return tool.id == 'requestsTool'; });
			
			contactDashboardTools.unshift(requestsTool);

		}

		_.each(contactDashboardTools, function(toolObj) {

			// Create tab
			dom.grid.left.tabs.makeNode(toolObj.id, 'div', {
				css: 'ui item ' + activeTab,
				text: '<i class="' + toolObj.icon.color + ' ' + toolObj.icon.type + ' icon"></i>'+ toolObj.name
			}).notify('click', {
					type: 'crm-run',
					data:{
						run:function() {
							
							// Remove the active status from old tab
							$(dom.grid.left.tabs.selector + ' .item').removeClass('active');
	
							// Add the active status to the new tab
							$(dom.grid.left.tabs[toolObj.id].selector).addClass('active');

							toolObj.mainViews[0].dom(
								dom.grid.left.tab,
								state,
								function(dom){
									
									if (dom) {
		
										if(dom.dom){
											dom.dom.patch();
											if (dom.after) {
												dom.after(dom.dom);
											}
										} else {
											dom.patch();
										}
										
									}
									
								},
								dom.grid.left.tab,
								{
									parent: obj.id,
									mini: true
								}
							);
																
						}.bind({}, toolObj)
					}
				}
				, sb.moduleId
			);
			
			if ( activeTab ) {
				
				toolObj.mainViews[0].dom(
					dom.grid.left.tab,
					state,
					function(dom){
						
						if(dom){

							if(dom.dom){
								dom.dom.patch();
								if(dom.after){
									dom.after(dom.dom);
								}
							}else{
								dom.patch();
							}
							
						}
						
					},
					dom.grid.left.tab,
					{
						parent: obj.id
						, mini: true
					}
				);
				
			}
			
			// Make sure all other tools aren't marked active
			activeTab = '';
			
		});
				
		if(
			appConfig.instance != 'thelifebook' &&
			appConfig.instance != 'thelifebookau' &&
			appConfig.instance != 'thelifebookno'
		){
			
			if(
				appConfig.instance == 'rickyvoltz' ||
				appConfig.instance == 'voltzsoftware'
			){
				
				if(sb.sys.state.components.instances){
					
					dom.grid.left.tabs.makeNode('instances', 'div', {
						css: 'ui item',
						text: '<i class="th icon"></i> Account'
					}).notify('click', {
						type: 'crm-run',
						data:{
							run:function() {
								
								if(sb.sys.state.components.instances){
									
									// Remove the active status from old tab
									$(dom.grid.left.tabs.selector + ' .item').removeClass('active');
			
									// Add the active status to the new tab
									$(dom.grid.left.tabs.instances.selector).addClass('active');
					
									sb.notify({
										type:'instances-single-object-view',
										data:{
											dom:dom.grid.left.tab,
											contactObj:obj
										}
									});	
			
								}
								
							}
						}		
					});
					
				}
				
			}
				
			/*
if(
				(
					appConfig.instance == 'zachvoltz' ||
					appConfig.instance == 'rickyvoltz' ||
					appConfig.instance == 'infinity' ||
					appConfig.instance == 'dreamcatering' ||
					appConfig.instance == 'mcvh' ||
					appConfig.instance == 'infinityproduction' ||
					appConfig.instance == 'nashvilleeventbartending'
				)
				&& sb.sys.state.components
				&& sb.sys.state.components.inventory
			){
				dom.cont.right.makeNode('discounts', 'div', {uiGrid:false});
				dom.cont.right.discounts.makeNode('tableCont', 'container', {title:'<i class="edit outline icon"></i> Discounts', collapse:collapse, css:'pda-container'});
			}
*/
						
		}
		
		// Attachments
		dom.grid.left.tabs.makeNode('attachments', 'div', {
			css: 'ui item',
			text: '<i class="paperclip icon"></i> Attachments'
		}).notify('click', {
			type: 'crm-run',
			data:{
				run:function() {
					
					// Remove the active status from old tab
					$(dom.grid.left.tabs.selector + ' .item').removeClass('active');

					// Add the active status to the new tab
					$(dom.grid.left.tabs.attachments.selector).addClass('active');
					
					sb.notify({
						type: 'view-field',
						data: {
							type: 'attachments',
							fieldName: 'attachments',
							obj: obj,
							options: {
								edit: true
							},
							ui: dom.grid.left.tab
						}
					});
					
				}
			}
		});		

		// Payment Methods
		dom.grid.left.tabs.makeNode('paymentInfo', 'div', {
			css: 'ui item',
			text: '<i class="usd icon"></i> Payment Methods',
			data: {
				'data-tab': 'paymentInfo'
			}
		}).notify('click', {
			type: 'crm-run',
			data:{
				run:function() {
					
					// Remove the active status from old tab
					$(dom.grid.left.tabs.selector + ' .item').removeClass('active');

					// Add the active status to the new tab
					$(dom.grid.left.tabs.paymentInfo.selector).addClass('active');
					
					sb.notify({
						type: 'show-paymentMethod-button',
						data: {
							domObj:dom.grid.left.tab,
							objectId:obj.id,
							compact: true,
							collapse:'closed'
						}
					});
					
				}
			}		
		});	
		dom.grid.left.makeNode('notes', 'div', {});

		var style = sb.dom.isMobile ? '' : 'margin:5px 15px 15px 15px';
											
		if(obj.type){
			
			if(obj.type.available_types != null){
			
				obj.type.available_types = obj.type.available_types.filter(function(n){ return n != undefined });
				
			}

		}
		
		draw({
			dom:dom,
			after:function(dom){

				var components = {};
				
				if ( !appConfig.is_portal ) {
						
					sb.notify ({
						type: 'view-field', 
						data: {
							type: 'tags', 
							property: 'tagged_with', 
							obj: obj, 
							options: {
								filter: false, 
								build: true
							}, 
							ui: dom.grid.right.tagsContainer.tags
						}
					});
				
				}

				// Client portal
				sb.data.db.obj.getWhere(
					'portal_access_token'
					, {
						contact: 		object.id
						, is_active: 	true
					}
					, function (tokens) {

						if (!_.isEmpty(tokens)) {
							
							var token = tokens[0];
							
							dom.portal.empty();
							dom.portal.makeNode(
								's'
								, 'div'
								, {
									css: 'ui inverted yellow segment'
								}
							).makeNode(
								'link'
								, 'div'
								, {
									tag: 		'a'
									, text: 	'<i class="th icon"></i> View Dashboard'
									, href: 	 sb.data.url.createPageURL(
													'custom', 
													{
														id: 		'contactDashboard'
														, name: 	'Dashboard'
														, params: {
															t: token.id
														}
													}
												)
								}
							);
							dom.portal.patch();
							
						} else {
							
							if(dom.portal){
								$(dom.portal.selector).remove();
								$(dom['p-br'].selector).remove();
							}
							
						}
						
					}
				);

				if (obj.type) {

					if(state.hasOwnProperty('project')){

						sb.notify({
							type: 'view-field',
							data: {
								type:'state',
								property:'state',
								ui: dom.alertContainer.seg.state,
								obj: obj,
								options: {
									size: 'mini'
								}
							}
						});

					}

					sb.notify({
						type:'start-contact_info-component',
						data:{
							domObj:dom.grid.right.contactInfoContainer.contactInfoBody
						}
					});

					var contactInfoCompSetup = {
						contactType:obj.type,
						contactInfoTypes:obj.type.available_types,
						info:obj.contact_info
					};

					sb.notify({
						type:'show-contact-info-column',
						data:{
							contactTypeId:obj.type.id,
							contactId:obj.id,
							contactType:'contacts',
							compData:contactInfoCompSetup,
							collapse:contactInfoCollapse,
							handleModal:true
						}
					});

				} else {

					dom.contactInfo.makeNode('msg', 'div', {
						css:'ui warning message'
						, text: 'Select a type for this contact to add contact info.'
					});

					dom.contactInfo.patch();

				}

				sb.notify({
					type: 'show-note-list-box',
					data: {
						domObj:dom.grid.left.notes,
						objectId:obj.id,
						state:state
					}
				});
				
			}

		});	
				
	}
		
	///company
	function companyView(obj, dom, state, draw){

		function companyQuickView(dom, object){
			
			///label group
			dom.makeNode('labels', 'div', {css: 'ui blue labels'});	
			
			if(object.is_vendor === 1)
				dom.labels.makeNode('vendor', 'div', {css: 'ui label', text:'Vendor'});
				
			if(object.tax_exempt)	
				dom.labels.makeNode('tax', 'div', {css: 'ui label', text:'Tax Exempt'});
	
			if(object.is_vendor === 1 && object.markup_percent > 0)
				dom.labels.makeNode('markup', 'div', {css: 'ui label', text:`Mark Up ${object.markup_percent} %`});	
						
			return dom;
			
		}		
		
		function editCompany(obj, dom, state, draw){

			function saveChanges(dom){
				
				var form = dom.cont.c.form.process();
				var is_vendor = +form.fields.is_vendor.value;
				var itemId = '';
				var company = {
						id:obj.id,
						is_vendor: +form.fields.is_vendor.value,
						default_product: +form.fields.default_product.value,
						products: _.map(form.fields.products.value, function(i){ return parseInt(i); })
					};

				var onSave = function(updatedObj){

					if(updatedObj.is_vendor != obj.is_vendor){
	
						sb.notify({
							type:'app-remove-main-navigation-item',
							data:{
								itemId:'companies',
								viewId:'single-'+updatedObj.id
							}
						}, sb.moduleId);
					
						sb.notify({
							type:'app-navigate-to',
							data:{
								itemId:itemId,
								viewId:{
									id:'single-'+updatedObj.id,
									type:'table-single-item',
									title:updatedObj.name,
									icon:'<i class="fa fa-user"></i>',
									setup:{
										objectType:'companies'
									},
									dom:companyView,
									rowObj:updatedObj,
									removable:true,
									parent:'table'
								}
							}
						});
						
					}else{
	
						sb.notify({
							type:'app-redraw',
							data:{}
						});
					
					}				
					
				};

				if(((state || {}).onEdit || {}).action){
	
					if(state.onEdit.action.hasOwnProperty('onSave') && typeof state.onEdit.action.onSave == 'function'){
	
						onSave = function(updObj){
						
							state.onEdit.action.onSave(updObj, dom, state, draw);
							
							if(state.hasOwnProperty('onComplete')) {
						
								state.onComplete(updObj);	
								
							}
							
						}	
						
					}				
				}
		
				switch(company.is_vendor){
					
					case 0:
						itemId = 'companies';
						//company.markup_percent = +obj.markup_percent;
						company.markup_percent = 0;
						
						break;
					
					case 1:
						itemId = 'vendors';
						company.markup_percent = +form.fields.markup_percent.value;
						
						break;
					
				}

				if(form.fields.products.value === null) {
					
					company.products = 0;
					
				}
				
				if ( form.fields.profile_image.value.fileData !== undefined ) 
					company.profile_image = form.fields.profile_image.value;

				sb.data.db.obj.update('companies', company, function(res){

					dom.body.btns.save.css('pda-btn-green');
					dom.body.btns.save.text('<i class="fa fa-check"></i> Saved');
	
					onSave(res);
					
				}, 1);				
				
			}
	
			dom.empty();
			
			dom.makeNode('sp', 'lineBreak', {});
			dom.makeNode('body', 'div', {css: 'small-container'});
					
			dom.body.makeNode('btns', 'buttonGroup', {css:'pull-right'});
				
			dom.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading', css:'pda-btn-primary'});				
			dom.body.makeNode('title', 'headerText', {text:'Edit '+ obj.name});
			dom.body.makeNode('btnsp', 'lineBreak', {spaces: 1});
			
			dom.makeNode('cont', 'container', {}).makeNode('c', 'column', {w:16});
	
			dom.patch();
	
			sb.data.db.obj.getAll('inventory_billable_categories', function(products) {

				// Creating the Edit Form's Object
				var formObj = {
						file_upload: {
							type: 'file-upload'
							, label: '<i class="file image outline icon large"></i> Profile Image (Optional)'
							, name: 'profile_image'
							, value: ''
						},								
						is_vendor:{
							name:'is_vendor',
							label:'Is this one of your vendors?',
							type:'select',
							options:[{
								name:'No',
								value:0
							},{
								name:'Yes',
								value:1
							}],
							value:obj.is_vendor,
							onChange: function(value) {

								if(parseInt(value) === 0) {
									
									dom.cont.c.form.markup_percent.update({
										type: 'hidden'
									});
									
									dom.cont.c.form.default_product.update({
										type: 'hidden'
									});
									
									dom.cont.c.form.products.update({
										type: 'hidden'
									});
									
								} else if(parseInt(value) === 1) {
									
									dom.cont.c.form.markup_percent.update({
										type: 'text'
									});
									
									dom.cont.c.form.default_product.update({
										type: 'select'
									});
									
									dom.cont.c.form.products.update({
										type: 'checkbox'
									});
									
								}
								
							}
						},
						markup_percent:{
							name:'markup_percent',
							type:'hidden',
							label:'Markup Percentage',
							value:obj.markup_percent
						},
						default_product: {
							name: 'default_product',
							type: 'hidden',
							label: 'Default Product Category',
							options: _.chain(products).map(function(o) {
								return {
									name: o.name,
									value: o.id	
								};
							}).sortBy(function(o) { 
								return o.name; 
							}).value()							},
						products: {
							name: 'products',
							type: 'hidden',
							label: 'Other Product Categories',
							options: _.chain(products).map(function(o) {
								return {
									name: o.name,
									label: o.name,
									value: o.id	
								};
							}).sortBy(function(o) { 
								return o.name; 
							}).value()
						}
					};
				
				// If editing a Vendor, change markup percent type to 'text'
				if(obj.is_vendor === 1){
					formObj.markup_percent.type = 'text';
					formObj.default_product.type = 'select';
		
					if(_.isObject(obj.default_product)) {
						formObj.default_product.value = obj.default_product.id;	
					}
					
					formObj.products.type = 'checkbox';
					formObj.products.value = obj.products;
				}
				
				formObj.default_product.options.unshift({
					name: 'None',
					value: 0
				});

				delete dom.cont.c.loader;
								
				dom.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
					type:'companyComponent-run',
					data:{
						run:saveChanges.bind(null, dom)
					}
				}, sb.mobuldeId);

				// Edit Form
				dom.cont.c.makeNode('form', 'form', formObj);
				
				dom.cont.c.form.is_vendor.notify('change', {
					type:'companyComponent-run',
					data:{
						run:function(){
							
							var formData = this.process().fields;
							
							if(formData.hasOwnProperty('markup_percent') && formData.hasOwnProperty('is_vendor')){
								
								switch(formData.is_vendor.value){
									
									case '1':
									
										this.markup_percent.update({
												type:'text',
												value:obj.markup_percent
											});
										
										break;
										
									case '0':
									
										this.markup_percent.update({
												type:'hidden'
											});
										
										break;
									
								}
								
							}
							
							return;
														
						}.bind(dom.cont.c.form, obj)
					}
				});
								
				dom.patch();
				
			}, {
				name: true
			});
				
					
		}			
				
		function init(obj, dom, state, draw){

			var edit = (state.hasOwnProperty('edit')) ? state.edit : true;
			var taxBtnText = '<i class="fa fa-certificate"></i>  Change Tax Status';
			var taxBtnCSS = 'ui grey basic button';   
			var companyType = (!_.isEmpty(obj.type)) ? obj.type.name : '<i>Type not selected</i>';
			var manager = '<i>not selected</i>';			
			var vendorText = '';
			var contactInfoCollapse = true;

			// Client tools
			var projectTool = _.findWhere(appConfig.objectTools, {id: 'projectTool'});
			var taskTool = _.findWhere(appConfig.objectTools, {id: 'taskTool'});
			var attachementsTool = _.findWhere(appConfig.objectTools, {id: 'messageBoardTool'});
			var invoicesTool = _.findWhere(appConfig.objectTools, {id: 'invoicesTool'});
		
			function build_tabs(dom) {

				var tabs = {
						info: {
							title: '<i class="info icon"></i> Info',
							view: build_info
						},
						projects: {
							title: '<i class="'+ projectTool.icon.type + ' ' + projectTool.icon.color +' icon"></i>' + projectTool.name,
							view: build_projects
						}
/*
						invoices: {
							title: '<i class="'+ invoicesTool.icon.type + ' ' + invoicesTool.icon.color +' icon"></i> '+ invoicesTool.name,
							view: build_invoices
						}
*/
					};
					
				if(taskTool){
					tabs.tasks = {
						title: '<i class="'+ taskTool.icon.type + ' ' + taskTool.icon.color +' icon"></i>' + taskTool.name,
						view: build_tasks
					};
				}
				
				if (attachementsTool) {
					
					tabs.attachments = {
						title: '<i class="'+ attachementsTool.icon.type + ' ' + attachementsTool.icon.color +' icon"></i>' + attachementsTool.name,
						view: build_attachments
					};
					
				}

				if(appConfig.instance === 'foundation_group'){

					tabs.contacts = {
						title: '<i class="users icon"></i> Contacts',
						view: build_contacts
					}

				}
				
				dom.makeNode('menu', 'div', {
					css: 'ui secondary stackable blue huge menu'
				});
				
				_.each(tabs, function(tab, name) {
					
					var active = '';
					
					if(name === 'info') {
						
						active = 'active';
						
					} 
					
					dom.menu.makeNode(name, 'div', {
						css: active + ' link item menuItem',
						text: tab.title
					}).notify('click', {
						type: [sb.moduleId+'-run'],
						data: {
								run: function(data) {
									
									//sb.data.db.trackXhr(false);
									
									$('.menuItem').removeClass('active');
									$(dom.menu[name].selector).addClass('active');
									
									tab.view(dom.tabSeg);
									
								}
							}
						}, sb.moduleId);
					
				});
				
			}

			function build_contacts(dom) {

				dom.empty();

				dom.makeNode('grid', 'div', {
					css: 'ui stackable grid'
				});

				dom.grid.makeNode('contacts', 'div', {css: 'ui sixteen wide column'})
					.makeNode('cont', 'div', {css:''});

				build_loader(dom.grid.contacts.cont, 'Building tool...');

				dom.patch();

				setTimeout(function() {

					var contactInfoCompSetup = {
						contactType:'companies',
						contactInfoTypes:[],
						info:[]
					};

					var typeId = 0;
					if(obj.type){
						typeId = obj.type.id;
					}

					sb.notify({
						type:'show-contact-info-column',
						data:{
							contactTypeId:typeId,
							contactId:obj.id,
							contactType:'companies',
							compData:{}
						}
					});

					sb.notify({
						type:'show-collection',
						data:{
							actions:{
								create:function(ui, newObj, onCreate){

									newObj.onSave = onCreate;

									addContact(ui, newObj, dom, obj);

								}
							},
							domObj:dom.grid.contacts.cont,
							fields:{
								fname:{
									title:'First Name',
									type:'title',
									isSearchable:true
								},
								lname:{
									title:'Last Name',
									type:'title',
									isSearchable:true
								},
								type:{
									title:'Type',
									type:'type'
								},
								state:{
									title:'State',
									type:'state'
								},
								email:{
									title:'Email',
									view:function(c, obj){

										var infoToShow = _.filter(obj.contact_info, function(info){

											if(info.type && info.type.data_type){
												return (info.type.data_type === 'email' && info.is_primary === 'yes');
											}

											return false;

										})[0];

										if (_.isEmpty(infoToShow)) {
											infoToShow = _.filter(obj.contact_info, function(info){

												if(info.type && info.type.data_type){
													return info.type.data_type === 'email';
												}

												return false;

											})[0];
										}

										if(infoToShow) {
											c.makeNode('t', 'div', {text:infoToShow.info});
										}

									},
									type:'detail'
								},
								phone:{
									title:'Phone',
									view:function(c, obj){

										var infoToShow = _.filter(obj.contact_info, function(info){

											if(info.type && info.type.data_type){
												return (info.type.data_type === 'cellphone' || info.type.data_type === 'phone');
											}

											return false;

										})[0];

										if(infoToShow) {
											c.makeNode('t', 'div', {text:infoToShow.info});
										}

									},
									type:'detail'
								},
								address:{
									title:'Address',
									view:function(c, obj){

										var infoToShow = _.filter(obj.contact_info, function(info){

											if(info.type && info.type.data_type){
												return info.type.data_type === 'address';
											}

											return '';

										})[0];

										var addressObject = {
											address: infoToShow
										}

										if (c) {

											sb.notify({
												type: 'view-field'
												, data: {
													type: 'address'
													, property: 'address'
													, obj: addressObject
													, isMetric: true
													, options: {
														edit: false
													}
													, ui: c.makeNode('address', 'div', {})
												}
											});

										}

									},
									type:'detail'
								}
							},
							groupings:{
								state:'State'
							},
							menu: {
								subviews: {
									list: true
									, board: true
									, table: true
									, calendar: false
									, cards: true
								}
							},
							objectType:'contacts',
							pageLength:10,
							where:{
								company:obj.id,
								childObjs:{
									fname:true,
									lname:true,
									type:{
										name:true,
										states:true
									},
									company:{
										name:true
									},
									state:true,
									contact_info:{
										city:true,
										country:true,
										state:true,
										street:true,
										street2:true,
										zip:true,
										info:true,
										is_primary:true,
										name:true,
										title:true,
										type:1
									}
								}
							}
						}
					});

				}, 0.1);
			}
			
			function build_info(dom) {

				var layer = 'hq';
				
				dom.empty();

				dom.makeNode('grid', 'div', {
					css: 'ui stackable grid'
				});

				dom.grid.makeNode('lcontent', 'div', {css: 'three wide column'});
				dom.grid.lcontent.makeNode('cinfo', 'div', {css: ''});

				if (appConfig.instance === 'foundation_group') {
					//only was rendered for foundation group
					dom.grid.makeNode('projects', 'div', {css: 'thirteen wide column'})
						.makeNode('cont', 'div', {css:''});

					state.object = obj;
					projectTool.mainViews[0].dom(dom.grid.projects, state, draw, {}, {
						collections: {
							actions: {
								create: function(ui, newObj, onComplete, bp, setup) {

									state.id = state.id;
									newObj.parent = state.id;
									newObj.onComplete = onComplete;

									sb.notify({
										type:'create-new-project',
										data:{
											ui: ui,
											state: state,
											obj: newObj,
											callback: function(newProject) {

												if(newProject) {

													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'groups',
															viewId:{
																viewState:{
																	obj: newProject
																}
															}
														}
													});

												}

											},
											setup: setup
										}
									});

								},
							},
							layer: layer,
							objectType: 'groups',
							selectedView: 'list',
							fields: {
								name: {
									title: 'Name',
									type: 'title',
									isSearchable: true
								},
								last_updated: {
									title: 'Last updated',
									type: 'date',
									view: function(ui, o) {

										ui.makeNode('date', 'div', {
											text: moment(o.last_updated).fromNow() +'</small>'
										});

									}
								},
								type:{
									title:'Type',
									type:'type'
								},
								state:{
									title:'Status',
									type:'state'
								},
								main_contact: {
									title: 'Contact',
									type: 'user',
									isSearchable: true
								}
							},
							groupings: {
								type: 'Type',
								main_contact: 'main_contact'
							},
							subviews: {
								list: {
									groupBy: {
										defaultTo: 'main_contact'
									}
								}
							},
							where: {
								group_type: 'Project',
								tagged_with: [obj.id],
								childObjs: {
									name:true,
									state:true,
									group_type:true,
									main_contact: true,
									type:{
										name:true,
										states:true
									},
									last_updated: true
								}
							}
						}
					});
				} else {
					dom.grid.makeNode('contacts', 'div', {css: 'thirteen wide column'})
						.makeNode('cont', 'div', {css:''});
					build_loader(dom.grid.contacts.cont, 'Building tool...');
				}
				
				dom.patch();
				
				setTimeout(function() {
					
					sb.notify({
						type:'start-contact_info-component',
						data:{
							domObj:dom.grid.lcontent.cinfo
						}
					});
					
					var contactInfoCompSetup = {
							contactType:'companies',
							contactInfoTypes:[],
							info:[]
						};
					
					var typeId = 0;
					if(obj.type){
						typeId = obj.type.id;
					}
					
					sb.notify({
						type:'show-contact-info-column',
						data:{
							contactTypeId:typeId,
							contactId:obj.id,
							contactType:'companies',
							compData:{}
						}
					});
					
					sb.notify({
						type:'show-collection',
						data:{
							actions:{
								create:function(ui, newObj, onCreate){
	
									newObj.onSave = onCreate;

									addContact(ui, newObj, dom, obj);
									
								}
							},
							domObj:dom.grid.contacts.cont,
							fields:{
								fname:{
									title:'First Name',
									type:'title',
									isSearchable:true
								},
								lname:{
									title:'Last Name',
									type:'title',
									isSearchable:true
								},
								type:{
									title:'Type',
									type:'type'
								},
								state:{
									title:'State',
									type:'state'
								},
								email:{
									title:'Email',
									view:function(c, obj){
										
										var infoToShow = _.filter(obj.contact_info, function(info){
											
											if(info.type && info.type.data_type){
												return (info.type.data_type === 'email' && info.is_primary === 'yes');
											}
											
											return false;
											
										})[0];

										if (_.isEmpty(infoToShow)) {
											infoToShow = _.filter(obj.contact_info, function(info){
											
												if(info.type && info.type.data_type){
													return info.type.data_type === 'email';
												}
												
												return false;
												
											})[0];
										}
										
										if(infoToShow) {
											c.makeNode('t', 'div', {text:infoToShow.info});
										}
										
									},
									type:'detail'
								},
								phone:{
									title:'Phone',
									view:function(c, obj){
										
										var infoToShow = _.filter(obj.contact_info, function(info){
											
											if(info.type && info.type.data_type){
												return (info.type.data_type === 'cellphone' || info.type.data_type === 'phone');
											}
											
											return false;
											
										})[0];
										
										if(infoToShow) {
											c.makeNode('t', 'div', {text:infoToShow.info});
										}
										
									},
									type:'detail'
								},
								address:{
									title:'Address',
									view:function(c, obj){
	
										var infoToShow = _.filter(obj.contact_info, function(info){
									
											if(info.type && info.type.data_type){
												return info.type.data_type === 'address';
											}
											
											return '';
											
										})[0];
		
										var addressObject = {
											address: infoToShow
										}
										
										if (c) {

											sb.notify({
												type: 'view-field'
												, data: {
													type: 'address'
													, property: 'address'
													, obj: addressObject
													, isMetric: true
													, options: {
														edit: false
													}
													, ui: c.makeNode('address', 'div', {})
												}
											});

										}
										
									},
									type:'detail'
								}
							},
							groupings:{
								state:'State'
							},
							menu: {
								subviews: {
									list: true
									, board: true
									, table: true
									, calendar: false
									, cards: true
								}
							},
							objectType:'contacts',
							pageLength:10,
							where:{
								company:obj.id,
								childObjs:{
									fname:true,
									lname:true,
									type:{
										name:true,
										states:true
									},
									company:{
										name:true
									},
									state:true,
									contact_info:{
										city:true,
										country:true,
										state:true,
										street:true,
										street2:true,
										zip:true,
										info:true,
										is_primary:true,
										name:true,
										title:true,
										type:1
									}
								}
							}
						}
					});
					
				}, 0.1);
				
			}
			
			function build_projects(ui) {
				
				var layer = '';
				
				if(state.hasOwnProperty('team')) {
					
					layer = 'team';
					
				} else {
					
					layer = 'hq';
					
				}
				
				state.object = obj;
					
				projectTool.mainViews[0].dom(ui, state, draw, {}, {
					collections: {
						actions: {
							create: function(ui, newObj, onComplete, bp, setup) {

								state.id = state.id;
								newObj.parent = state.id;
								newObj.onComplete = onComplete;

								sb.notify({
									type:'create-new-project',
									data:{
										ui: ui,
										state: state,
										obj: newObj,
										callback: function(newProject) {
		
											if(newProject) {
												
												sb.notify({
													type:'app-navigate-to',
													data:{
														itemId:'groups',
														viewId:{
															viewState:{
																obj: newProject
															}
														}
													}
												});
												
											}
										
										},
										setup: setup
									}
								});
								
							},
						},
						layer: layer,
						objectType: 'groups',
						selectedView: 'list',
						fields: {
							name: {
								title: 'Name',
								type: 'title',
								isSearchable: true
							},
							last_updated: {
								title: 'Last updated',
								type: 'date',
								view: function(ui, o) {
		
									ui.makeNode('date', 'div', {
										text: moment(o.last_updated).fromNow() +'</small>'
									});
									
								}
							},
							type:{
								title:'Type',
								type:'type'
							},
							state:{
								title:'Status',
								type:'state'
							},
							main_contact: {
								title: 'Contact',
								type: 'user',
								isSearchable: true
							}
						},
						groupings: {
							type: 'Type',
							main_contact: 'main_contact'
						},
						subviews: {
							list: {
								groupBy: {
									defaultTo: 'main_contact'
								}
							}
						},
						where: {
							group_type: 'Project',
							tagged_with: [obj.id],
							childObjs: {
								name:true,
								state:true,
								group_type:true,
								main_contact: true,
								type:{
									name:true,
									states:true
								},
								last_updated: true
							}
						}
					}
				});
				
			}
			
			function build_tasks(ui) {
				
				var layer = '';
				
				if(state.hasOwnProperty('team')) {
					
					layer = 'team';
					
				} else {
					
					layer = 'hq';
					
				}
				
				taskTool.mainViews[0].dom(ui, state, draw, {}, {
					collectionsSetup: {
						actions: {},
						layer: layer,
						parseData: function(data, callback, query, subview, range, types) {
						
							sb.data.db.obj.getWhere('contacts', {
								company: obj.id,
								childObjs: {
									name: true
								}
							}, function(contactList) {
								
								var contactIds = _.pluck(contactList, 'id');
								var relatedTasks = [];

								_.each(data.data, function(task) {
									
									_.each(contactIds, function(id) {
										
										if(_.contains(task.tagged_with, id)) {
											
											relatedTasks.push(task);
											
										}
										
									});
									
								});
								
								data.data = relatedTasks;

								callback(data);
							
							});
						
						},
						where: {
							group_type: 'Task',
							childObjs: {
								name: true,
								tagged_with: true,
								managers: true,
								priority: true,
								group_type: true,
								type: {
									name: true,
									states: true
								},
								object_uid: true,
								parent: true,
								comment_count: true
							}
						}
					}
				});
				
			}
			
			function build_invoices(ui) {
				
				var layer = '';
				
				if(state.hasOwnProperty('team')) {
					
					layer = 'team';
					
				} else {
					
					layer = 'hq';
					
				}
				
				invoicesTool.mainViews[0].dom(ui, state, draw, {}, {
					collections: {
						layer: layer,
						parseData: function(data, callback, query, subview, range, types) {
							
							sb.data.db.obj.getWhere('contacts', {
								company: obj.id,
								childObjs: {
									name: true
								}
							}, function(contactList) {
								
								var contactIds = _.pluck(contactList, 'id');
								var invoices = _.filter(data.data, function(invoice) {
										
										if(invoice.main_contact !== null) {
											
											return _.contains(contactIds, invoice.main_contact.id);	
											
										}
										
									});

								data.data = invoices;

								callback(data);
								
							});
							
						}
					}
				});
				
			}
			
			function build_attachments(ui) {
				
				attachementsTool.mainViews[0].dom(ui, state, draw, {}, {});
				
			}
			
			if(obj.is_vendor === 1){
				vendorText = '<small><label class="label pda-label-primary">Vendor</label></small>';
			}
			
			if(obj.manager){
				manager = obj.manager.fname +' '+ obj.manager.lname;
			}						    
			    
			if(obj.hasOwnProperty('tax_exempt') && obj.tax_exempt == true){
				
				taxBtnText = '<i class="fa fa-check-circle-o fa-lg"></i>  Tax Exempt',
				taxBtnCSS = 'ui grey button';
			}    

			dom.empty();
			dom.makeNode('sp', 'lineBreak', {spaces: 1});
			dom.makeNode('container', 'div', {css: 'ui stackable grid'});

/*
			if(
				appConfig.version != 'bento' &&
				appConfig.instance != 'rickyvoltz' &&
				appConfig.instance != 'zachvoltz' &&
				appConfig.instance != 'joshgantt' &&
				appConfig.instance != 'petermikhail'
			){
*/
			if(appConfig.version === 'bin') {

				dom.container.makeNode('alertContainer', 'div', {css: 'sixteen wide column'});			
	
				if(obj.is_vendor === 1){
					dom.container.alertContainer.makeNode('markup', 'headerText', {text:`<small>Markup</small> <span class="pda-color-green">+${obj.markup_percent}%</span>`, size:`x-small`});
				}				
							
			}
						
			dom.container.makeNode('headerCont', 'div', {css: 'sixteen wide column'});

			companyQuickView(dom.container.headerCont, obj);
			
			build_tabs(dom.container);
			
			dom.container.makeNode('tabSeg', 'div', {
				css: 'ui bottom attached active tab segment'
			});
			
			build_info(dom.container.tabSeg);
			
			dom.container.makeNode('rcontent', 'div', {css: 'sixteen wide column'});
			dom.container.rcontent.makeNode('notes', 'div', {css: ''});
			
			if(draw !== undefined) {
			
				draw({
					dom:dom,
					after:function(dom){
						
						sb.notify({
							type: 'show-note-list-box',
							data: {
								domObj:dom.container.rcontent.notes,
								objectIds:[obj.id],
								objectId:obj.id
							}
						});
																		
					}
				});
				
			} else {

				dom.patch();
								
				sb.notify({
					type: 'show-note-list-box',
					data: {
						domObj:dom.container.rcontent.notes,
						objectIds:[obj.id],
						objectId:obj.id
					}
				});
				
			}	
						
		}

		init(obj, dom, state, draw);
		
		companyView.edit = editCompany;
		
	}	
	
	function createClientPortal (obj, dom, state, onComplete, type) {

		function submitForm (form, onComplete) {
			
			var formData = form.process().fields;
			var request = {
				name: 			formData.name.value
				, initialUser: 	formData.initialUser.value
			};
			
			sb.data.db.obj.runSteps(
				{
					createPortal: request
				}
				, obj.id
				, function (response) {
					
					onComplete(response);
					
				}
			);
			
		}
		
		// Loading gif
		dom.makeNode('loader', 'loader', {});
		dom.patch();

		sb.data.db.obj.getWhere(
			'portal_access_token'
			, {
				[type]: obj.id
			}
			, function (tokens) {

				function contactPortalForm (email) {
					
					function processForm (form, callback) {

						var formData = form.process().fields;
						var ret = {};
						ret.contact = obj.id;

						switch (formData.instance.value.split('-')[0]) {
							
							// Create in parent company portal
							case 'co':
								ret.company = parseInt(formData.instance.value.split('-')[1]);
								break;
							
							// Create new portal for parent company
							case 'new':
								break;
							
							// Create in another pre-existing instance
							case 'us':
								ret.user = parseInt(formData.instance.value.split('-')[1]);
								ret.instance = formData.instance.value.split('-')[2];
								break;
							
						}
						
						callback(ret);
						
					}
					
					// Confirm
					dom.empty();
					
					// Check for instance on parent company.
					sb.data.db.obj.getWhere(
						'portal_access_token'
						, {
							company: obj.company.id
						}, function (companyPortal) {

							// Check for other users that use this email address.
							sb.data.db.controller(
								'getAccounts'
								, {
									email: email
								}
								, function (otherAccounts) {

									var formArgs = {};
									
									// If other users exist, should be able to choose the 
									// instance to create the portal in for the user.
									
									// Email address to use
									dom.makeNode('email', 'div', {
										tag: 'h2'
										, css: 'ui header'
										, text: '<i class="grey envelope icon"></i> '+ email
									});
									
									// Instance selection
									formArgs = [
										{
											name: 		'instance'
											, type: 	'select'
											, options: 	[]
											, label: 	'Instance'
										}
									];
									
									if (!_.isEmpty(companyPortal)) {
										
										formArgs[0].options.push({
											name: 		obj.company.name
											, value: 	'co-'+ obj.company.id
										});
										
									}
									
									if (!_.isEmpty(otherAccounts)) {
										
										_.each(otherAccounts, function (acct) {
											
											formArgs[0].options.push({
												name: 		acct.instance.toUpperCase()
												, value: 	'us-'+ acct.id +'-'+ acct.instance
											});
											
										});
										
									}
									
									dom.makeNode(
										'form'
										, 'form'
										, formArgs
									);
									
									// Create btn
									dom.makeNode(
										'submit'
										, 'div'
										, {
											text: 'Create Portal'
											, css: 'ui teal fluid button'
										}
									).notify('click', {
										type: 'contactComponent-run'
										, data: {
											run: function () {
												
												dom.submit.loading();
												
												processForm(
													dom.form
													, function (formData) {

														sb.data.db.obj.runSteps(
															{
																createPortalUser: formData
															}
															, obj.id
															, function (response) {
																
																onComplete(response);
																
															}
														);
														
													}
												);
												
											}
										}
									}, sb.moduleId);
									
									dom.patch();
									
								}
							);
							
						}
					);
					
				}
				
				if (!_.isEmpty(tokens)) {

					var alertMessage = 'All contacts at this company will lose access to anything currently shared with them.';
					
					if ( type == 'contact')
						alertMessage = 'This contact will lose access to anything currently shared with them.'
					
					dom.empty();
					dom.makeNode(
						'msg'
						, 'div'
						, {
							css: 'ui warning message'
						}
					).makeNode(
						'c'
						, 'div'
						, {
							text: 	'<div class="header">Portal Access granted</div>'+
									'<p>Access granted '+ moment(tokens[0].date_created).format('MM/DD/YYYY') +'.</p>'
						}
					);
					
					dom.makeNode('revoke', 'div', {
						tag: 'button'
						, css: 'ui red button'
						, text: '<i class="trash icon"></i> Revoke access'
					}).notify('click', {
						type: 'contactComponent-run'
						, data: {
							run: function () {
								
								sb.dom.alerts.ask({
									title: 'Are you sure?'
									, text: alertMessage
								}, function (r) {
									
									if (r) {
										
										sb.data.db.obj.erase(
											'portal_access_token'
											, _.pluck(tokens, 'id')
											, function (r) {
												
												var	newComment = {
													type_id: 		obj.id,
													author: 		+sb.data.cookie.userId,
													notification:{
														producer: 	obj.id,
														link: 		sb.data.url.createPageURL('object', obj),
														notify: 	obj.notify
													},
													note: 			appConfig.user.fname +' '+ appConfig.user.lname +' revoked <strong>'+ obj.name +'\'s</strong> portal access.',
													note_type: 		0,
													edited: 		0,
													public: 		0
												};
										
												sb.data.db.obj.postComment(newComment, function(created){

													location.reload();

												}, 2);
												
											}
										);
										
									} else {
										
										swal.close();
										
									}
									
								});
								
							}
						}
					}, sb.moduleId);
					
					dom.patch();
					
				} else if (type === 'contact') {

					var email = false;
					
					sb.data.db.obj.getById('', obj.id, function(resp) {

						_.each(resp.contact_info, function (info) {

							if (info.type && info.type.data_type === 'email') {
								email = info.info;
							}
						});

						if (email) {
							
							contactPortalForm(email);
							
						} else {
							
							dom.empty();
							dom.makeNode(
								'msg'
								, 'div'
								, {
									css: 'ui warning message'
								}
							).makeNode(
								'c'
								, 'div'
								, {
									text: 	'<div class="header">Email Required</div>'+
											'<p>A valid email address for this user is required to create a portal.</p>'
								}
							);
							dom.patch();
							
						}
						
					}, 2);
					
				} else {

					sb.data.db.obj.getWhere(
						'contacts'
						, {
							company: obj.id
							, childObjs: {
								fname: 		true
								, lname: 	true
								, name: 		true
								, contact_info: {
									info: true
									, type: {
										data_type: true
									}
								}
							}
						}
						, function (contacts) {
							
							var contactsWithEmail = _.filter(
								contacts
								, function (contact) {
									
									var ret = false;
									_.each(contact.contact_info, function (info) {
										
										if (
											info
											&& info.type
											&& info.type.data_type === 'email'
										) {
											
											ret = true;
											
										}
										
									});
									
									return ret;
									
								}
							);
							dom.empty();
							
							if (_.isEmpty(contactsWithEmail)) {
								
								dom.makeNode(
									'msg'
									, 'div'
									, {
										css: 'ui warning message'
									}
								).makeNode(
									'c'
									, 'div'
									, {
										text: 	'<div class="header">Contact Required</div>'+
												'<p>At least one contact at this client (with a valid email address) is required to create a portal.</p>'
									}
								);
								dom.patch();
								
							} else {
								
								// Form header
								dom.makeNode(
									'h'
									, 'div'
									, {
										tag: 	'h1'
										, text: 	'Create Portal'
										, css: 	'ui header'
									}
								);
								
								var formArgs = {
									name: {
										name: 		'name'
										, label: 	'Company Name'
										, type: 		'text'
										, value: 	obj.name
									}
									, logo: {
										name: 		'logo'
										, label: 	'Company Logo (optional)'
										, type: 		'file-upload'
									}
									, initialUser: {
										name: 		'initialUser'
										, label: 	'Contact to give a user'
										, type: 		'select'
										, options: 	_.map(contactsWithEmail, function (c) {
											
											return {
												name: 		c.name
												, value: 	c.id
											};
											
										})
									}
								};
								
								dom.makeNode('form', 'form', formArgs);
								dom.makeNode('br', 'lineBreak', {});
								
								// Confirm
								dom.makeNode(
									'submit'
									, 'div'
									, {
										text: 'Create Portal'
										, css: 'ui teal fluid button'
									}
								).notify('click', {
									type: 'contactComponent-run'
									, data: {
										run: function () {
											
											dom.submit.loading();

											submitForm(
												dom.form
												, function (complete) {

													onComplete(true);
													
												}
											);
											
										}
									}
								}, sb.moduleId);
								
								dom.patch();
								
							}
							
						}
					);
					
				}
				
			}
		);
		
	}

	///settings
	function contactInfoTypes(dom, bp){

		function create_editForm(dom, info_type, callback) {
			
			var callType = 'update';
			
			if (info_type === null) {
				
				info_type = {};
				info_type.name = 'Create new info type';
				info_type.data_type = 'address';
				info_type.is_select = 'no';
				
				callType = 'create';
				
			} 
			
			var formObj = {
					name:{
						name:'name',
						label:'Name',
						type:'text',
						value:info_type.name
					},
					data_type:{
						name:'data_type',
						label:'Type',
						type:'select',
						options:_.map(bp.data_type.options, function(name, value){
	
							if(info_type.data_type == value){
								
								return {
									name:name,
									value:value,
									selected:true
								}
								
							}else{
								
								return {
									name:name,
									value:value
								}
								
							}
							
						})
					},
					is_select:{
						name:'is_select',
						label:'Is this a dropdown box?',
						type:'select',
						options:[
							{
								name:'No',
								value:'no'
							},
							{
								name:'Yes',
								value:'yes'
							}
						],
						onChange:function(value){
													
							if(value == 'yes'){
								
								sb.data.db.obj.update('contact_info_options', {id:info_type.id, is_select:'yes'}, function(updated){
									
									showDropDownOptions(dom, updated, function(done){
										
										editForm(dom, updated, function(){});
										
									});
									
								});							
								
							}else{
								
								sb.data.db.obj.update('contact_info_options', {id:info_type.id, is_select:'no'}, function(updated){
									
									dom.modal.body.optionsCont.empty();
									dom.modal.body.optionsCont.patch();
									$(dom.modal.body.optionsCont.selector).removeClass('ui secondary segment');
									
								});
								
							}
							
							
							
						}
					}
				};
			
			if(info_type.is_select == 'yes'){
				formObj.is_select.options[1].selected = true;
			}
			
			dom.modal.show();
									
			dom.modal.body.makeNode('title','div',{css:'ui header', text:info_type.name});

			dom.modal.body.makeNode('form','form',formObj);
			
			dom.modal.body.makeNode('optionsCont', 'div',{});
			
			dom.modal.body.makeNode('break', 'div',{text:'<br />'});
			
			dom.modal.body.makeNode('btns', 'div',{css:'ui buttons'});
			
			dom.modal.body.btns.makeNode('cancel', 'div',{css:'ui basic button', text:'Cancel'})
				.notify('click', {
					type:'crm-run',
					data:{
						run:function(){
							
							dom.modal.hide();
							
							callback(true);
							
						}
					}
				}, sb.moduleId);
			
			dom.modal.body.btns.makeNode('save', 'div',{css:'ui green button', text:'Save'})
				.notify('click', {
					type:'crm-run',
					data:{
						run:function(){
							
							dom.modal.body.btns.save.loading();
																				
							info_type.name = dom.modal.body.form.process().fields.name.value;
							info_type.data_type = dom.modal.body.form.process().fields.data_type.value;
							info_type.is_select = dom.modal.body.form.process().fields.is_select.value;

							sb.data.db.obj[callType]('contact_info_types', info_type, function(updated){
								
								dom.modal.hide();
								
								callback(true);
								
							});
							
						}
					}
				}, sb.moduleId);
				
			if(info_type.is_select == 'yes'){
			
				showDropDownOptions(dom, info_type, function(done){
					
					info_type.is_select = 'yes';
					
					create_editForm(dom, info_type, function(){});
					
				});
			
			}
			
			dom.modal.body.patch();
			
		}
		
		function showDropDownOptions(dom, info_type, callback){
			
			dom.modal.body.optionsCont.makeNode('name', 'div',{text:'<b>Dropdown options</b>'});

			sb.data.db.obj.getWhere('contact_info_options', {contact_info_type:info_type.id}, function(options){
				
				dom.modal.body.optionsCont.makeNode('table', 'div',{tag:'table', css:'ui striped table'});
				dom.modal.body.optionsCont.table.makeNode('thead', 'div',{tag:'thead'});
				dom.modal.body.optionsCont.table.thead.makeNode('tr', 'div',{tag:'tr', text:'<th>Option Name</th><th>Options</th>'});

				dom.modal.body.optionsCont.table.makeNode('tbody', 'div',{tag:'tbody'});
				
				_.each(options, function(option){
					
					dom.modal.body.optionsCont.table.tbody.makeNode('tr'+option.id, 'div',{tag:'tr'});
					dom.modal.body.optionsCont.table.tbody['tr'+option.id].makeNode('name', 'div',{text:option.name, tag:'td'});
					dom.modal.body.optionsCont.table.tbody['tr'+option.id].makeNode('btns', 'div',{tag:'td'});
					dom.modal.body.optionsCont.table.tbody['tr'+option.id].btns.makeNode('erase', 'div',{css:'ui mini red button', text:'Delete'})
						.notify('click', {
							type:'crm-run',
							data:{
								run:function(){
									
									dom.modal.body.optionsCont.table.tbody['tr'+option.id].btns.erase.loading();
									
									sb.data.db.obj.erase('contact_info_options', option.id, function(erased){
										
										showDropDownOptions(dom, info_type, function(){});
										
									});
									
								}
							}
						});
					
				});

				dom.modal.body.optionsCont.makeNode('create', 'div',{css:'ui mini basic green button', text:'New Option'})
					.notify('click', {
						type:'crm-run',
						data:{
							run:function(){
								
								dom.modal.body.optionsCont.table.tbody.makeNode('trnew', 'div',{tag:'tr'});
								
								dom.modal.body.optionsCont.table.tbody.trnew.makeNode('name', 'div',{tag:'td', text:'<div class="ui input"><input id="new-option" type="text" /></div>'});
								
								dom.modal.body.optionsCont.table.tbody.trnew.makeNode('btns', 'div',{tag:'td'});

								dom.modal.body.optionsCont.table.tbody.trnew.btns.makeNode('save', 'div',{css:'ui mini green button', text:'Save Option'})
									.notify('click', {
										type:'crm-run',
										data:{
											run:function(){
												
												dom.modal.body.optionsCont.table.tbody.trnew.btns.save.loading();
																																			
												sb.data.db.obj.create(
													'contact_info_options', 
													{
														name:$('#new-option').val(),
														contact_info_type:info_type.id
													},
													function(saved){
													
														showDropDownOptions(dom, info_type, function(){});
													
												});
												
											}
										}
									}, sb.moduleId);
								
								dom.modal.body.optionsCont.table.tbody.patch();
								
							}
						}
					}, sb.moduleId);
				
				dom.modal.body.optionsCont.patch();
				
				$(dom.modal.body.optionsCont.selector).addClass('ui tertiary segment');
				
			});
			
		}
		
		loader(dom, 'Fetching contact_info_types...');
		
		sb.data.db.obj.getAll('contact_info_types', function(info_types){
						
			dom.empty();
			
			dom.makeNode('createBtnWrap', 'div', {});
			
			dom.createBtnWrap.makeNode('createBtn', 'div', {
				text: 'Create'
				, css: 'ui right floated green button'
			}).notify('click', {
				type:'crm-run',
				data: {
						run: function(data) {
							
							create_editForm(dom, null, function(done){
										
								contactInfoTypes(dom, bp);
								
							});
							
						}
					}
				}, sb.moduleId);
			
			dom.makeNode('lb_1', 'lineBreak', {spaces: 2});
			
			dom.makeNode('table','div',{tag:'table', css:'ui celled table'});
			
			dom.table.makeNode('thead','div',{tag:'thead', css:''});

			dom.table.thead.makeNode('tr','div',{tag:'tr', css:''});
			
			dom.table.thead.tr.makeNode('name','div', {text:'<th>Name</th>'});
			
			dom.table.thead.tr.makeNode('type','div', {text:'<th>Type</th>'});
			
			dom.table.thead.tr.makeNode('options','div', {text:'<th>Options</th>'});

			dom.table.makeNode('tbody','div',{tag:'tbody', css:''});
			
			_.each(info_types, function(info_type){
				
				dom.table.tbody.makeNode('tr'+info_type.id,'div',{tag:'tr', css:''});
					
				dom.table.tbody['tr'+info_type.id].makeNode('name','div', {tag:'td', text:info_type.name});
				
				dom.table.tbody['tr'+info_type.id].makeNode('type','div', {tag:'td', text:info_type.data_type});
				
				dom.table.tbody['tr'+info_type.id].makeNode('options','div', {
					tag:'td'
					, css:''
					, style: 'width: 200px;'
				});
				
				dom.table.tbody['tr'+info_type.id].options.makeNode('btnGrp', 'div', {
					
				});
				
				dom.table.tbody['tr'+info_type.id].options.makeNode('edit','div',{css:'ui orange button', text:'Edit'})
					.notify('click', {
							type:'crm-run',
							data:{
								run:function(){
									
									create_editForm(dom, info_type, function(done){
										
										contactInfoTypes(dom, bp);
										
									});
									
								}
							}
						}, sb.moduleId);	
				
				dom.table.tbody['tr'+info_type.id].options.makeNode('delete','div',{css:'ui red button', text:'Delete'})
					.notify('click', {
						type:'crm-run',
						data: {
								run: function(data) {
									
									sb.dom.alerts.ask({
										title: 'Are you sure?'
										, text: 'You can not undo this action'
									}, function (r) {
										
										if (r) {
											
											sb.data.db.obj.erase('contact_info_types', info_type.id, function(erased) {
										
												if (erased) {
													
													contactInfoTypes(dom, bp);
													
												}
												
											});
											
										} else {
											
											swal.close();
											
										}
										
									});
									
								}
							}
						}, sb.moduleId);
				
			});
			
			dom.makeNode('modal','modal',{css:'ui modal'});
			
			dom.patch();
						
		});
		
	}
	
	function contactTypeSettings(dom, bp, options){

        function contactTypeEditView(dom, setup){			

            var create = _.isEmpty(setup.object);
			var object = setup.object;
            var db_objecttype = setup.db_objecttype;

			var formArgs = {
					name:{
						name:'name',
						type:'text',
						label:'Name',
						value:object.name || '',
						placeholder:'New, Opportunity, etc.'					
					},
					formsof:{
						name:'formsof',
						type:'checkbox',
						label:'Forms of Contact',
						placeholder:'Select which forms of contact that apply..',
						options:[],
						value: []
					}
				};
				
			var defaultStates = [
					{
						uid:1,
						name:'New',
						icon:'star outline',
						color:'grey',
						previous:[],
						next:[2],
						isEntryPoint:1
					},
					{
						uid:2,
						name:'Active',
						icon:'star',
						color:'grey',
						previous:[1],
						next:[]
					}
				];

			function processContactTypeForm(form, updObj, cb){

				var ret = {
					dbCall:'create',
					status:{
						type:'success',
						message:'You have created a new type of Contact'
					},
					saveObj:updObj
				};
				
				var formData = form.detailsForm.process().fields;
				
				if(updObj.hasOwnProperty('id'))
					ret.dbCall = 'update';

				if(_.isEmpty(formData.name.value)){
					ret.status.type = 'warning';
					ret.status.message = 'Please enter a name for this contact type';
				}

				if(_.isArray(formData.formsof.value)){
					
					ret.saveObj.available_types = _.map(formData.formsof.value, function(val){
						
						return parseInt(val);
												
					});
										
				} else {
					
					ret.saveObj.available_types = [];
				}
				
				ret.saveObj.name = formData.name.value;

				return cb(ret);

			}

			if(!_.isEmpty(setup.info_types)){
				
				formArgs.formsof.options = _.map(setup.info_types, function(itype){

					var ret = {name:itype.name, value:itype.id, label:itype.name}	
			
					if(_.findWhere(object.available_types, {id:itype.id}))
						ret.selected = true;
					
					if(create === true) {
						
						formArgs.formsof.value.push(itype.id);		
						
					}
			
					return ret;
					
				});
				
				if(create === false) {
						
					_.each(object.available_types, function(type) {
						
						formArgs.formsof.value.push(type.id);		
						
					});
					
				}
									
			}	
			
			///default values 

			if(create || _.isEmpty(object.states) || !_.has(object, 'states')){
		
				object.states = defaultStates;
				
			}

			dom.empty();

			dom.makeNode('detailsForm', 'form', formArgs);
			dom.makeNode('br1', 'div', {text:'<br />'});
			dom.makeNode('states', 'div', {css: 'text-center'});
			
			sb.notify({
				type: 'show-workflow-states-view',
				data: {
					dom:dom.states,
					state:{
						header:'Contact management flow<div class="sub header">Define this contact type\'s workflow in your system.</div>',
						object:object
					}	
				}
			});			
			
			dom.makeNode('br2', 'div', {text:'<br />'});
			dom.makeNode('btns', 'div', {css: 'ui buttons'});
			dom.btns.makeNode('back', 'div', {css: 'ui button', text:'Cancel'}).notify('click', {
				type: 'contactComponent-run',
				data: {
					run:contactTypeSettings.bind(null, dom, setup, options)
				}
			}, sb.moduleId);
			
			if(!create)
				dom.btns.makeNode('delete', 'div', {css: 'ui red button', text:'Delete'}).notify('click', {
					type: 'contactComponent-run',
					data: {
						run:function(dom, obj){
							
							dom.btns.delete.css('ui red button loading');
							
							sb.data.db.obj.erase(db_objecttype, obj.id, function(resp){

                                if(resp){

									contactTypeSettings(dom, bp, options);

								}else{
									
									dom.btns.delete.loading(false);
									
									sb.dom.alerts.alert('Oops!', 'An error occurred -- please refresh and try again.', 'error');
								}								
								
							});
							
						}.bind(null, dom, object)
					}
				}, sb.moduleId);
			
			dom.btns.makeNode('save', 'div', {css: 'ui green button', text:'Save'}).notify('click', {
				type: 'contactComponent-run',
				data: {
					run:function(dom, typeObj){
						
						dom.btns.save.css('ui green button loading');
						processContactTypeForm(dom, typeObj, function(output){

							if(output.status.type == 'warning'){
								
								setTimeout(function(){
									
									dom.btns.save.loading(false);

									sb.dom.alerts.alert('Warning', output.status.message, output.status.type);
									
								}, 300);
	
							}else{

								sb.data.db.obj[output.dbCall](db_objecttype, output.saveObj, function(updated){
									
									setup.object = updated;
									
									contactTypeSettings(dom, bp, options);
																			
								});	
																
							}							
							
						});
												
					}.bind({}, dom, object)
				}
			}, sb.moduleId);
			
			dom.patch();
			
			return;	
			
		}
		
		setupContactSystem(function(done){

            var db_objecttype = 'contact_types';
            if ( options && options.object_type ){
                db_objecttype = options.object_type;
            }
			
			sb.data.db.obj.getAll( db_objecttype, function(contact_types){

				sb.data.db.obj.getAll('contact_info_types', function(info_types){
	
					dom.empty();
				
					dom.makeNode('btns', 'div', {
						css:'ui right floated buttons'
					}).makeNode('create', 'div', {
						tag:'button',
						css:'ui green button',
						text:'New Contact Type'
					}).notify('click', {
						type:'contactComponent-run',
						data:{
							run:contactTypeEditView.bind(null, dom, {
								blueprint:bp,
								object:{},
								info_types:info_types
                                , db_objecttype: db_objecttype
							})
						}
					}, sb.moduleId);
					
					// table
					dom.makeNode('br-before-table', 'div', {text:'<br /><br />'});
					dom.makeNode('table', 'div', {tag:'table', css:'ui basic table'}).makeNode('thead', 'div', {tag:'thead'});
					
					dom.table.thead.makeNode('tr', 'div', {tag:'tr'})
						.makeNode('name', 'div', {tag:'th', text:'Name'});
										
					dom.table.thead.tr.makeNode('btns', 'div', {tag:'th', text:'Buttons'});
					dom.table.makeNode('body', 'div', {tag:'tbody'});
					
					_.each(contact_types, function(type){
	
						dom.table.body.makeNode('type-'+type.id, 'div', {tag:'tr'})
							.makeNode('name', 'div', {tag:'td', text:type.name});
											
						dom.table.body['type-'+type.id].makeNode('btns', 'div', {tag:'td'})
							.makeNode('edit', 'div', {tag:'button', css:'ui tiny yellow button', text:'Edit'})
							.notify('click', {
								type: 'contactComponent-run',
								data: {
									run:contactTypeEditView.bind(null, dom, {
										blueprint:bp,
										object:type,
										info_types:info_types
                                        , db_objecttype: db_objecttype
									})
								}
							}, sb.moduleId);	
						
					});
					
					dom.patch();			
					
				});
									
			}, 1);
			
		});
				
	}
	
	///utilfns
	function setupContactSystem(callback){

		function createClientTypes(infoTypes, callback){
			
			sb.data.db.obj.getAll('company_categories', function(clientTypes){

				if(clientTypes.length == 0){
					
					var newClientTypes = [
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Company'
							},
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Non-profit'
							},
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Vendor'
							},
							{
								available_types:_.pluck(infoTypes, 'id'),
								name:'Other'
							}
						];
					
					sb.data.db.obj.create(
						'company_categories',
						newClientTypes,
						function(created){
							
							callback(created);
							
						}
					);
					
				}else{
					
					callback(clientTypes);
					
				}
				
			});
			
		}
		
		function createContactTypes(infoTypes, callback){
			
			sb.data.db.obj.getAll('contact_types', function(contactTypes){

				if(contactTypes.length == 0){
					
					sb.data.db.obj.create(
						'contact_types',
						{
							available_types:_.pluck(infoTypes, 'id'),
							name:'Contact',
							states:[
								{
									color:'green',
									icon:'check',
									isEntryPoint:1,
									name:'Active',
									next:["2"],
									previous:[],
									uid:1
								},
								{
									color:'grey',
									icon:'ban',
									name:'Inactive',
									next:[],
									previous:["1"],
									uid:2
								}
							]
						},
						function(created){
							
							callback(created);
							
						}
					);
					
				}else{
					
					callback(contactTypes);
					
				}
				
			});
			
		}
		
		function createInfoTypes(callback){
			
			sb.data.db.obj.getAll('contact_info_types', function(infoTypes){
				
				if(infoTypes.length == 0){
					
					//create the default info types
					var newInfoTypes = [
							{
								data_type:'email',
								is_address:'',
								name:'Email',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'phone',
								is_address:'',
								name:'Phone',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'address',
								is_address:true,
								name:'Default Address',
								object_bp_type:'"contact_info_types"'
							},
							{
								data_type:'website',
								is_address:'',
								name:'Website URL',
								object_bp_type:'"contact_info_types"'
							}
						];
						
					sb.data.db.obj.create('contact_info_types', newInfoTypes, function(created){
						
						callback(created);
						
					});
					
				}else{
					
					callback(infoTypes);
					
				}
				
			});
			
		}
		
		createInfoTypes(function(infoTypes){
			
			createContactTypes(infoTypes, function(contactTypes){
				
				createClientTypes(infoTypes, function(clientTypes){
					
					callback(true);
					
				});
								
			});
			
		});
				
	}
	
	function pointOfContactCreateFlow(ui, state) {
		
		build_crm_createFlow(ui, state, {}, false);
		return;
		
		function selectACompany(ui) {
			
			var formArgs = {
					companies: {
						type: 'select'
						, name: 'companies'
						, label: 'Companies'
						, options: []
					}
				};
				
			function process_company(form, after) {
				
				var formData = form.process().fields;
				
				after(formData.companies.value);
									
			}
			
			ui.empty();
			
			ui.makeNode('wrapper', 'div', {});
			
			ui.wrapper.makeNode('head', 'div', {});
			ui.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
			ui.wrapper.makeNode('body', 'div', {});
			
			ui.wrapper.head.makeNode('title', 'div', {
				text: 'Select a company'
				, tag: 'h2'
				, css: 'ui header'
			});
			
			build_loader(ui.wrapper.body, 'Loading companies...');
			
			sb.data.db.obj.getWhere('companies', {
				childObjs: 1
			}, function(companies) {
				
				ui.wrapper.body.empty();
				
				if(!_.isEmpty(companies)) {
					
					_.each(companies, function(company) {
					
						formArgs.companies.options.push({
							name: company.name
							, value: company.id
						});
						
					});
					
					ui.wrapper.body.makeNode('form', 'form', formArgs);
				
					ui.wrapper.body.makeNode('lb_1', 'lineBreak', {spaces: 1});
					
					ui.wrapper.body.makeNode('btnGrp', 'div', {});
					
					ui.wrapper.body.btnGrp.makeNode('next', 'div', {
						css: 'ui green button right floated'
						, text: 'Next'
					}).notify('click', {
						type: [sb.moduleId+'-run'],
						data: {
								run: function(data) {
									
									ui.wrapper.body.btnGrp.next.loading();
									
									process_company(ui.wrapper.body.form, function(id) {
										
										listCompanyContacts(ui.wrapper, id);
										
									});
									
								}
							}
						}, sb.moduleId);
						
					ui.wrapper.body.btnGrp.makeNode('back', 'div', {
						css: 'ui red button left floated'
						, text: 'Back'
					}).notify('click', {
						type: [sb.moduleId+'-run'],
						data: {
								run: function(data) {
									
									pointOfContactCreateFlow(ui, state);
									
								}
							}
						}, sb.moduleId);		
					
				} else {
					
					ui.wrapper.body.makeNode('msg', 'div', {
						css: 'ui negative message'
					});
					
					ui.wrapper.body.msg.makeNode('header', 'div', {
						css: 'ui header'
						, text: 'No companies found'
					});
					
					ui.wrapper.body.msg.makeNode('content', 'div', {
						tag: 'p'
						, text: 'Click here to create a new company'
						, css: 'ui link'
					}).notify('click', {
						type: [sb.moduleId+'-run'],
						data: {
								run: function(data) {
									
									state.btns = {
										back: {
											text: 'Back'
											, run: function() {
												
												pointOfContactCreateFlow(ui, state);
												
											}
										}
										, save: {
											text: 'Save and create a contact'
											, run: function(company) {
												
												state.onSave = function(newContact) {
														
													sb.data.db.obj.update('groups', {
														id: state.project.id, 
														main_contact: newContact.id
													}, function(project){
					
														state.project = project;
														sb.notify({
															type: 'app-navigate-to'
															, data: {
																type: 'UP'
															}
														});
														
													});											
												}
												
												state.backBtn = function() {
													
													listCompanyContacts(ui, company.id);
													
												}
												
												state.saveBtnText = 'Set as point of contact';
												
												addContact(ui, state, {}, company);
												
											}
										}	
									};
									
									build_createCompanyFlow(ui, state, undefined, undefined);
									
								}
							}
						}, sb.moduleId);
																	
				}
				
				ui.wrapper.body.patch();
				
			});
			
			ui.patch();
			
		}
		
		function listCompanyContacts(ui, id) {
				
			// id is company id
			
			var formArgs = {
					contacts: {
						name: 'contacts'
						, label: 'Contacts'
						, type: 'select'
						, options: []
					}
				};
				
			function process_contact(form, after) {
				
				var formData = form.process().fields;
				
				after(formData.contacts.value);
				
			}
			
			ui.empty();
			
			ui.makeNode('wrapper', 'div', {});
			
			ui.wrapper.makeNode('head', 'div', {});
			ui.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
			ui.wrapper.makeNode('body', 'div', {});
			
			ui.wrapper.head.makeNode('title', 'div', {
				text: 'Select a contact or create a new contact'
				, css: 'ui header'
				, tag: 'h2'
			});
			
			build_loader(ui.wrapper.body, 'Loading contacts...');

			sb.data.db.obj.getWhere('contacts', {
				company: parseInt(id)
				, childObjs: 1
			}, function(contacts) {
			
				_.each(contacts, function(contact) {
					
					formArgs.contacts.options.push({
						name: contact.name
						, value: contact.id
					});
					
				});
				
				ui.wrapper.body.empty();
				
				ui.wrapper.body.makeNode('form', 'form', formArgs);
				
				ui.wrapper.body.makeNode('lb_1', 'lineBreak', {spaces: 1});
				
				ui.wrapper.body.makeNode('btnGrp', 'div', {});
				
				ui.wrapper.body.btnGrp.makeNode('next', 'div', {
					css: 'ui green button right floated'
					, text: 'Add as point of contact'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
							run: function(data) {
								
								ui.wrapper.body.btnGrp.next.loading();
								
								process_contact(ui.wrapper.body.form, function(id) {

									if(state.hasOwnProperty('onSave')) {
										
										var selected = _.findWhere(contacts, {id: id});
										
										state.onSave(selected);
										
									} else {

										sb.data.db.obj.update('groups', {
											id: state.project.id, 
											main_contact: parseInt(id)
										}, function(project){
		
											state.project = project;
											sb.notify({
												type: 'app-navigate-to'
												, data: {
													type: 'UP'
												}
											});
											
										});	
										
									}
									
								});
								
							}
						}
					}, sb.moduleId);
					
				ui.wrapper.body.btnGrp.makeNode('back', 'div', {
					css: 'ui red button left floated'
					, text: 'Back'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
							run: function(data) {
								
								selectACompany(ui);
								
							}
						}
					}, sb.moduleId);
					
				ui.wrapper.body.btnGrp.makeNode('createNew', 'div', {
					css: 'ui inverted green button left floated'
					, text: 'Create a contact'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
							run: function(data) {
								
								ui.wrapper.body.btnGrp.createNew.loading();
								
								if(!state.hasOwnProperty('onSave')) {
									
									state.onSave = function(newContact) {
									
										sb.data.db.obj.update('groups', {
											id: state.project.id, 
											main_contact: newContact.id
										}, function(project){
		
											state.project = project;
											sb.notify({
												type: 'app-navigate-to'
												, data: {
													type: 'UP'
												}
											});
											
										});											
									}	
									
								} 
								
								state.backBtn = function() {
									
									listCompanyContacts(ui, id);
									
								}
								
								state.saveBtnText = 'Set as point of contact';

								sb.data.db.obj.getById('companies', id, function(company) {
	
									addContact(ui, state, ui, company);
										
									
								}, {
									id: true
									, name: true
								});
								
							}
						}
					}, sb.moduleId);
				
				ui.wrapper.body.patch();
			
			});
			
			ui.patch();
			
		}
		
		ui.empty();
		
		ui.makeNode('message', 'div', {
			css:'ui icon massive message'
		});
		
		ui.message.makeNode('content', 'div', {
			css:'content'
		});
		
		ui.message.content.makeNode('header', 'div', {
			text:'You can create a new contact from within a company.', 
			css:'header'
		});
		
		ui.message.content.makeNode('seg', 'div', {css: 'ui placeholder segment'});
		ui.message.content.seg.makeNode('grid', 'div', {css: 'ui two column stackable center aligned grid'});
		ui.message.content.seg.grid.makeNode('vert', 'div', {css: 'ui vertical divider', text:'Or'});
		ui.message.content.seg.grid.makeNode('row', 'div', {css: 'middle aligned row'});
		
		ui.message.content.seg.grid.row.makeNode('col1', 'div', {css: 'column'});
		
		ui.message.content.seg.grid.row.col1.makeNode('header', 'div', {
			tag:'h5', 
			css: 'ui icon header', 
			text:'<i class="search icon"></i>'
		});
		ui.message.content.seg.grid.row.col1.makeNode('button', 'div', {
			css: 'ui primary button', 
			text:'Search for an existing company'
		}).notify('click', {
			type: 'crm-run',
			data: {
				run: function(data) {
					
					selectACompany(ui);
					
				}
			}
		}, sb.moduleId);

		ui.message.content.seg.grid.row.makeNode('col2', 'div', {
			css: 'column'});
		ui.message.content.seg.grid.row.col2.makeNode('header', 'div', {
			tag:'h5', 
			css: 'ui icon header', 
			text:'<i class="building icon"></i>'
		});
		ui.message.content.seg.grid.row.col2.makeNode('button', 'div', {
			css: 'ui positive button', 
			text:'Create a new company'
		}).notify('click', {
			type: 'crm-run',
			data: {
				run: function(data) {
					
					state.btns = {
						back: {
							text: 'Back'
							, run: function() {
								
								pointOfContactCreateFlow(ui, state);
								
							}
						}
						, save: {
							text: 'Save and create a contact'
							, run: function(company) {
								
								if(!state.hasOwnProperty('onSave')) {
									
									state.onSave = function(newContact) {
										
										sb.data.db.obj.update('groups', {
											id: state.project.id, 
											main_contact: newContact.id
										}, function(project){
		
											state.project = project;
											sb.notify({
												type: 'app-navigate-to'
												, data: {
													type: 'UP'
												}
											});
											
										});											
									}	
									
								}
								
								state.backBtn = function() {
									
									listCompanyContacts(ui, company.id);
									
								}
								
								state.saveBtnText = 'Set as point of contact';
								
								addContact(ui, state, {}, company);
								
							}
						}	
					};
					
					build_createCompanyFlow(ui, state, undefined, undefined);
					
				}
			}
		}, sb.moduleId);
		
		ui.patch();
		
	}
	
	function searchForAContact(dom, state, draw){

		function performSearch(term, callback){
			
			sb.data.db.obj.getWhere('contacts', {
				name:{
					type:'or',
					fields: ['fname', 'lname'],
					value:term
				},
				childObjs: {
					fname: 		true
					, lname: 	true
					, company: {
						name: 	true
					}
				}
			}, function(results){
				
				callback(results);
				
			});
			
		}
		
		function searchMainContact (dom, state, draw) {

			dom.form.btn.loading();
			dom.results.css('ui basic loading segment');
			
			var term = $(dom.form.input.selector).val();
			var cachedResults = [];

			if(term.length < 3 || term == ''){
				dom.results.css('ui basic segment');
				dom.form.btn.loading(false);
				dom.results.empty();
				dom.results.patch();
				return;
			}
			
			performSearch(term, function(results){

				cachedResults = results;
				
				dom.results.makeNode('count', 'div', {text:results.length + ' total results', css:'ui small header'});
				
				dom.results.makeNode('table', 'table', {
					clearCSS:true,
					css:'ui stackable table',
					columns:{
						select:'',
						name:'Name'
					},
					columnCSS:['two wide', 'fourteen wide']
				});
				
				_.each(results, function(contact){
					
					var company = '';
					if(contact.company){
						company = '<small> at '+contact.company.name+'</small>';
					}
					
					dom.results.table.makeRow(
						'row-'+contact.id,
						[
							'',
							'<h2>'+contact.fname +' '+ contact.lname + company+'</h2>'
						]
					);
					
					dom.results.table.body['row-'+contact.id].select.makeNode('select', 'div', {text:'Select', css:'ui green button'});
					
					dom.results.table.body['row-'+contact.id].select.select.notify('click', {
						type:'contactComponent-run',
						data:{
							run:function(dom, state, draw, contact){
								
								dom.results.table.body['row-'+contact.id].select.select.loading();

								var tags = state.project.tagged_with;
								tags.push(contact.id, contact.company.id);
								sb.data.db.obj.update('groups', {
									id:					state.project.id, 
									main_contact:		contact.id,
									tagged_with: 		tags
								}, function(project){

									state.project = project;
									sb.notify({
										type: 'app-navigate-to'
										, data: {
											type: 'UP'
										}
									});
									
								});
								
							}.bind({}, dom, state, draw, contact)
						}
					}, sb.moduleId);
					
				});
				
				dom.results.patch();
				dom.results.css('ui basic segment');
				dom.form.btn.loading(false);					
				
			});	
						
		}
		
		dom.empty();
		
		dom.makeNode('title', 'div', {text:'Search for a contact', css:'ui large header'});
		
		dom.makeNode('form', 'div', {css:'ui huge fluid action input'});
		
		var throttledMainContactSearch = _.throttle(
			searchMainContact
			, 1000
		);
		var form = dom.form.makeNode('input', 'div', {tag:'input', type:'text'})
			.notify('keyup', {
				type: 'contactComponent-run',
				data: {
					run: function(dom, state, draw){

						throttledMainContactSearch(dom, state, draw);
						
					}.bind({}, dom, state, draw) 
				}
			}, sb.moduleId);
			
		dom.form.makeNode('btn', 'div', {text:'Search', css:'ui green button'})
			.notify('click', {
				type:'contactComponent-run',
				data:{
					run:searchMainContact.bind({}, dom, state, draw)
				}
			}, sb.moduleId);
		
		dom.makeNode('formBreak', 'div', {text:'<br />'});
		dom.makeNode('createBtn', 'div', {
			text: '<i class="ui plus icon"></i> Create a new contact'
			, css: 'ui green button'
		}).notify('click', {
			type: [sb.moduleId+'-run'],
			data: {
					run: function(data) {
						
						dom.makeNode('modal', 'modal', {
							onShow: function() {
								
								state.onProject = true;

								pointOfContactCreateFlow(dom.modal.body, state);
								
								dom.modal.patch();
								
							}
						});
						
						dom.patch();
						dom.modal.show();
						
					}
				}
			}, sb.moduleId);
		dom.makeNode('results', 'div', {css:'ui basic segment'});
		
		draw(dom);
		
	}
	
	function taxExempt(obj, dom, onComplete){
		
		var taxToggle = {
			false: {
				label: 'Change Status',
				btnText: '<i class="fa fa-toggle-off fa-2x" aria-hidden="true"></i>'
			},
			true: {
				label: 'Tax Exempt',
				btnText: '<i class="fa fa-toggle-on fa-2x" aria-hidden="true"></i>'
			},
			selected: false,
		};
		var taxDocument = {
			false: {
				label: 'Require Documentation',
				btnText: '<i class="fa fa-toggle-off fa-2x" aria-hidden="true"></i>'
			},
			true: {
				label: 'Select File for upload',
				btnText: '<i class="fa fa-toggle-on fa-2x" aria-hidden="true"></i>'
			},
			selected: false,
			docOnFile: null		
		};		

		function fileCheck(obj, callback){

			sb.data.files.getWhere({oid: obj.id, file_type: "tax_exempt_form" }, function(document){

				if(_(document).trx() && !_.isEmpty(document)){
					
					taxDocument.docOnFile = document[0], taxDocument.selected = true;
					 
				} else {
					
					taxDocument.docOnFile = null, taxDocument.selected = false;
				}			

				callback();
											
			});
					
		}
		
		function processFileUpload(callback){

			var formData = null;
			
			if(this.body.header.modform.fileseg.hasOwnProperty('form')){
				formData = this.body.header.modform.fileseg.form.process().fields;
			}
						
			var  fileMetaData = {
					fileType: "tax_exempt_form",
					fileName: formData.file.value.fileName,
					objectType: "companies",
					objectId: parseInt(obj.id),
					parent: 0

			    };		
			    
			function validateForm(obj){

				if(obj && obj.file.value.fileData != undefined){
					
					return true;
					
				} else {
					
					formData = null;
					
					return false 
				}
			}    

			var validated = validateForm(formData);

			if(validated){
								
				this.footer.empty();
				this.footer.makeNode('loader', 'loader', {size: 'small'});
				this.footer.patch();

				sb.data.files.upload(formData.file.value.fileData, fileMetaData, function(response){
					
					formData = null;

					if(response){
						
						callback();
						
					}else{
						
						sb.dom.alerts.alert('Sorry!', 'An error occurred--please refresh and try again.', 'error');
						
					}
					
				});
				

			} else { 
				
				sb.dom.alerts.alert('Error', 'Please select a file to upload', 'error'); 
				
				formData = null;
				
				return false;
			}
						
		}
		
		function updateExemptStatus(obj, alert){
			
			var updObj = _.clone(obj),
			    aTitle = 'Status Changed',
			    aText = 'You have successfully changed the tax exempt status';

			updObj.tax_exempt = taxToggle.selected;			    

			if(this.body.header.modform.fileseg.hasOwnProperty('form')){

				processFileUpload.call(this, function(){

// 					this.modal.hide();
		
					sb.data.db.obj.update('companies', updObj, function(resp){
						
						if(onComplete){
							onComplete(resp);
							return;
						}
						
						sb.dom.alerts.alert(aTitle, aText, 'success');
						
						sb.notify({
							type:'app-redraw',
							data:{}
						});
						
						taxToggle.selected = false,
						taxDocument.selected = false,
						taxDocument.docOnFile = null;
						
					}.bind(this), 2);	
				
				}.bind(this));
				
			} else {
				
// 				this.modal.hide();
	
				sb.data.db.obj.update('companies', updObj, function(resp){
					
					if(onComplete){
						onComplete(resp);
						return;
					}
					
					sb.dom.alerts.alert(aTitle, aText, 'success');
					
					sb.notify({
						type:'app-redraw',
						data:{}
					});
					
					taxToggle.selected = false,
					taxDocument.selected = false,
					taxDocument.docOnFile = null;
					
				}.bind(this), 2);	
				
			}
		
		}
				
		function exemptUI(){
			
			function exemptStatusToggle(){
				
				var b = _.propertyOf(taxToggle)(taxToggle.selected);

				this.makeNode('labl', 'div', {css: 'ui right floated header', tag: 'h3', text: b.btnText}).notify('click', {
					type: 'companyComponent-run',
					data: {
						run: function(){
							
							if(_(taxDocument.docOnFile).trx() && taxDocument.selected == true){

								sb.dom.alerts.alert('Document Uploaded', 'Please remove document before changing status', 'warning');

							} else {
								
								taxToggle.selected = !taxToggle.selected;

								exemptUI();
								
							}
						}
					}
				}, sb.moduleId);
				this.makeNode('togg', 'div', {css: 'ui left floated header', tag: 'h3', text: b.label});				
												
			}
			
			function exemptFileUpload(){

				var b = _.propertyOf(taxDocument)(taxDocument.selected);
			
				var formArgs = {
					file_download: {
						type: 'file-upload',
						label: 'Select a File',
						name: 'file'
					}
				};
				
				function hasDoc(){

					this.makeNode('divider', 'div', {css: 'ui clearing divider'});
					this.makeNode('fhead', 'div', {css: 'ui left floated header', tag: 'h4'});
					this.fhead.makeNode('ficon', 'div', {css: 'file outline icon', tag: 'i'});
					this.fhead.makeNode('ftext', 'div', {css: 'content', text: `${taxDocument.docOnFile.file_name}`});
					
					this.makeNode('btnGroup', 'buttonGroup', {css: 'pull-right pda-vertical-align'});
					this.btnGroup.makeNode('downloadBtn', 'button', {css: 'ui button', text: '<i class="fa fa-download"> Download</i>'}).notify('click', {
						type: 'companyComponent-run',
						data: {
							run: function(){

								window.open(sb.bucket + appConfig.instance +'/'+ taxDocument.docOnFile.loc)
								// sb.data.files.open(taxDocument.docOnFile);
								
							}.bind(this)
						}
					}, sb.moduleId);
					
					this.btnGroup.makeNode('removeBtn', 'button', {css: 'ui red basic button', text: '<i class="fa fa-trash"></i> Remove'}).notify('click', {
						type: 'companyComponent-run',
						data: {
							run: function(){

								sb.data.files.delete(taxDocument.docOnFile.id, function(response){

									if(response){
										
										taxDocument.selected = false;
										taxDocument.docOnFile = null;
										
										exemptUI();
									}
								});										

							}
						}
					}, sb.moduleId);					
								
				}
				
				function doesnotHasDoc(){

					if(taxDocument.selected == true){
						
						this.makeNode('sp', 'lineBreak', {spaces: 2});					
						this.makeNode('form', 'form', formArgs);				
					}

				}
				
				this.makeNode('headr', 'div', {css: 'ui left floated header', text: b.label});
				this.makeNode('stattxt', 'div', {css: 'ui right floated header', tag: 'h3', text: b.btnText}).notify('click', {
					type: 'companyComponent-run',
					data: {
						run: function(){
	
							if(_(taxDocument.docOnFile).trx()){
								
								return true;
								
							} else {

								taxDocument.selected = !taxDocument.selected;
																
							}

							exemptUI();
							
						}
					}
				}, sb.moduleId);
				
				(_(taxDocument.docOnFile).trx()) ? hasDoc.call(this) : doesnotHasDoc.call(this)
								
			}
			
			dom.body.empty();
			dom.footer.empty();
			
			dom.body.makeNode('header', 'column', {w: 16, css: ''});

			dom.body.header.makeNode('modform', 'div', {css: 'ui form'});
			dom.body.header.modform.makeNode('modhead', 'div', {css: 'ui dividing header', text: 'Change Tax Exempt Status'});
			dom.body.header.modform.makeNode('header', 'div', {css: 'ui header'});
			dom.body.header.modform.header.makeNode('head', 'div', {css: 'content', text: `${obj.name}`});
			if ( obj.hasOwnProperty('type') ) {
				if ( obj.type ) {
					if ( obj.type.hasOwnProperty('name') ) {
						dom.body.header.modform.header.makeNode('subhead', 'div', {css: 'sub header', text: `${obj.type.name}`});
					}
				}	
			}
			dom.body.header.modform.makeNode('statseg', 'div', {css: 'ui clearing segment'});
			dom.body.header.modform.makeNode('fileseg', 'div', {css: 'ui clearing segment'});

			exemptStatusToggle.call(dom.body.header.modform.statseg);
			exemptFileUpload.call(dom.body.header.modform.fileseg);
			
			dom.footer.makeNode('saveBtn', 'button', {css: 'pda-btn-green pda-align-right', text: 'Save Changes'}).notify('click', {
				type: 'companyComponent-run',
				data: {
					run: updateExemptStatus.bind(dom, obj)
				}
			}, sb.moduleId);
						
			dom.patch();
			
		}

		(!obj.tax_exempt || obj.tax_exempt == false) ? taxToggle.selected = false : taxToggle.selected = true
		
		dom.empty();
		dom.makeNode('body', 'div', {});
		dom.makeNode('footer', 'div', {});
						
		dom.patch();
		
		fileCheck(obj, exemptUI);

	}
		
	function getContactData(contact, callback){

		var ret = {};
		var searchCo = false;
		var searchMan = false;
		
		if (contact.company)
			searchCo = !!contact.company;
			
		function getPortalToken(contact, search, callback){
			
			if ( search ) {

				sb.data.db.obj.getWhere(
					'portal_access_token'
					, {
						contact: 		contact.id
						, is_active: 	true
					}
					, function (tokens) {

						callback(tokens);
						
					}
				);				
				
				
			} else {
				callback(contact);
			}
			
			return;
			
		}	

		function getCompany (contact, search, callback) {
			
			if ( search ){
				
				sb.data.db.obj.getById('contacts', contact.id, function(res){
										
					callback(res);
					
				}, 1);
				
			} else {
				
				callback(contact);
				
			}
			
			return;
			
		}
		
		function getManager (contact, search, callback) {
			
			if ( search ) {
				
				sb.data.db.obj.getWhere('users', {
					id:contact.manager, 
					childObjs:{
						fname:true, 
						lname:true
					}
				}, function(man){

					callback (man);						
			
				});	
				
			} else {
				
				callback(contact);
				
			}		
			
		}

		if(contact){
			
			getPortalToken( contact, true, function(tokens){

				getCompany(contact, searchCo, function(contact){
	
					sb.data.db.obj.getWhere('contact_info', {
						object_id:contact.id,
						childObjs:{
							info:true,
							city:true,
							state:true,
							zip:true,
							type:{
								name:true,
								data_type:true
							}
						}
					}, function(contactInfo){
						
						if ( _.isNumber(contact.manager) )
							searchMan = true;
						
						getManager (contact, searchMan, function(contact){
	
							var contactTypeId = contact.type;
							if (typeof contact.type === 'object' && contact.type.id) {
								contactTypeId = contact.type.id;
							}
	
							sb.data.db.obj.getById('contact_types', contactTypeId, function(type){
								
								ret.company = contact.company;
								ret.info = contactInfo; 
								ret.manager = contact.manager;
								ret.contact_type = type;
								ret.token = tokens;

								callback(ret);					
							});								
						});
								
					});					
					
				});
				
			});
			
		}
		
		return;		
	}

	function instanceValidate(instance, option){}
	
	function getAddressTxt (o) {
		
		var addressTxt = '';
		var locations = [];
		
		if(o.contact_info){

			var info = o.contact_info.filter(function( element ) {
			   return element != null;
			});
		                        
	       _.each(info, function(i){

	           if(i.type.id == 53 && i.type.data_type == 'address' && i.object_id == o.id){
	               locations.push(i);
	           }
	           
	           if(i.type.data_type == 'address' && i.object_id == o.id && appConfig.instance != 'thelifebook'){
	               locations.push(i);
	           }
	           
	       });
	
	       locations = locations.sort(function(a,b) { 
	               return b.id - a.id;
	           });

	       if(locations.length > 0){
	           addressTxt = ' '+ locations[0].city +', '+ locations[0].state;
	       } 

		}
		
		if (_.isEmpty(addressTxt)) {
			return '';
		}
		
		if(o.object_bp_type == 'companies'){
		
			return addressTxt;
		
		}else{

			return ', '+ addressTxt;
		}	
		
	}
	
	function getGroupTxt (obj) {

		var txt = '';
		
		if (obj.company) {
			
			txt = '<a href="'+ sb.data.url.getObjectPageParams(obj.company) +'">'+ obj.company.name +'</a>';
			
		}
		
		return txt;
		
	}

	function build_loader(ui, text, style) {

		var wrapperStyle = '';

		if (style !== undefined) {
			wrapperStyle = style;
		}

		ui.makeNode('loadingSeg', 'div', {
			css: 'ui active centered inline loader'
		});

		ui.makeNode('loadWrap', 'div', {
			style: wrapperStyle
		});
		ui.loadWrap.makeNode('loadText', 'div', {
			text: text
			, css: 'text-center'
		});

	}
	
	function loader(ui, text) {
		
		ui.empty();
		
		ui.makeNode('loadingSeg', 'div', {
			css: 'ui active centered inline loader'
		});
		
		ui.patch();
		
	}
	
	function setContactLeadSource(ui, callback) {
		// Specific to Infinity-Dream Catering and NLP
		if ( 
			appConfig.instance === 'infinity'
			|| appConfig.instance === 'dreamcatering'
			|| appConfig.instance === 'nlp'
			|| appConfig.instance === 'rickyvoltz'
		) {
			
			ui.empty();
		
			build_loader(ui, 'Preparing data...please wait');
			
			ui.patch();

			sb.data.db.service("ContactService", "setLeadSource", {}, function () {
				ui.empty();
				ui.patch();
				
				callback(true);
				
			});
			
		} else {
			
			callback(true);
			
		}
		
	}
	
	//Boxviews
	function relatedProjects_boxview(ui, state, draw) {
		
		var project = state.project;

		if(project === undefined) {
			
			throw '(relatedProjects_boxview::line:5695) Project object undefined.';
			
		}
		
		if(project.main_contact === null) {
			
			ui.makeNode('msg', 'div', {
				css: 'ui message'
			});
			
			ui.msg.makeNode('header', 'div', {
				text: 'This project does not have a main contact.'
				, css: 'header'
			});
			
			ui.msg.makeNode('content', 'div', {
				tag: 'p'
				, text: 'Please add a main contact from the Point of Contact tool to see related projects.'
			});
			
		} else {
			
			var mainContact = project.main_contact;
			
			if(!mainContact.hasOwnProperty('id')) {
				
				throw '(relatedProjects_boxview::line:5721) Main contact is undefined.';
				
			}
			
			ui.makeNode('colWrapper', 'div', {});
			
			var collectionsSetup = {
					domObj: ui.colWrapper
					, objectType: 'groups'
					, actions: {}
					, fields:{
						name:{
							title:'Name'
						},
						description:{
							title:'Details',
							type:'description'
						},
						type:{
							title:'Type',
							type:'type'
						},
						state:{
							title:'Status',
							type:'state'
						},
						start_date:{
							title:'Start Date',
							type: 'date',
							end: 'end_date'
						},
						end_date:{
							title:'End Date',
							type: 'date',
							hideInMini: true
						}
					}
					, selectedView: 'list'
					, menu: false
					, subviews: {
						list: {
							groupBy: {
								defaultTo: 'type'
							}
						}
					}
					, groupings: {
						type:'Type',
						state:'Status',
						by_range: true
					}
					, state:state
					, where: {
						group_type: 'Project'
						, main_contact: mainContact.id
						, id: {
							type: 'not_equal'
							, value: project.id
						}
						, paged: {
							count:        true
							, page:       0
							, pageLength: 5
							, sortCol:    'date_created'
							, sortDir:    'desc'
						}
						, childObjs:{
							name:true,
							description:true,
							state:true,
							state_updated_on: true,
							group_type:true,
							type:{
								name:true,
								states:true
							},
							managers:{
								fname:true,
								lname:true,
								profile_image:true
							},
							tools:true,
							start_date:true,
							end_date:true
						} 
					}
				};
			
			sb.notify({
				type: 'show-collection'
				, data: collectionsSetup
			});
			
		}
		
		draw(ui);
		
	}	
	
	// Reports
	function salesLeadList(ui, state, draw, mainDom) {
		
		var ReportBody_UI = {};
		
		function formatPhoneNumber(string) {
			
			if (
				typeof string !== 'string'
			) {
				string = '';
			}

			var base = string.replace(/\D/g,'');
			var areaCode = base.substring(0, 3);
			var p1 = base.substring(3, 6);
			var p2 = base.substring(6, 10);
			var ret = '';
			
			if (!_.isEmpty(areaCode)) {
				ret = areaCode;
				
			}
			if (!_.isEmpty(p1)) {
				ret = '('+ ret +')';
				ret += ' '+ p1;
			}
			if (!_.isEmpty(p2)) {
				ret += '-'+ p2;
			}
			
			return ret;
			
		}
		
		function display_tabs(ui) {
			
			var tabs = {
					projects: {
						name: 'projects'
						, title: 'Projects'
						, icon: 'lightbulb'
					}
					, clients: {
						name: 'clients'
						, title: 'Clients'
						, icon: 'address book outline'
					}
				};
			var activeClass = '';
			
			function tabButtonClicked(data) {
				
				if ($(data.selector).hasClass('active') === false) {
								
					$('.reportTabs').removeClass('active');
					$(data.selector).addClass('active');
					
					switch(data.tabType) {
						
						case 'projects':
							display_ProjectReport(ReportBody_UI);
							break;
							
						case 'clients':
							display_ClientReport(ReportBody_UI);
							break;
						
					}	
					
				}
				
			}
				
			_.each(tabs, function(tab, name) {
				
				if (name === 'projects') {
					activeClass = 'active';
				} else {
					activeClass = '';
				}
				
				ui.makeNode('item-'+tab.name, 'div', {
					css: 'item reportTabs ' + activeClass,
					text: '<i class="'+ tab.icon +' icon"></i> ' + tab.title,
					style: 'cursor: pointer;'
				}).notify('click', {
					type: 'crm-run',
					data: {
						run: function(data) {
							
							tabButtonClicked(data);
							
						}
						, tabType: name
						, selector: ui['item-'+tab.name].selector
					}
				}, sb.moduleId);
				
			});
			
		}
		
		function display_ClientReport(ui) {
			
			function display_relatedProjects(ui, obj) {

				ui.makeNode('wrapper', 'div', {});

				ui.wrapper.makeNode('modal', 'modal', {
					onShow: function() {

						var collectionsSetup = {
								domObj: ui.wrapper.modal.body
								, fields: {
									state: {
					 					title:'Status'
					 					, view: function(dom, obj) {
	
						 					sb.notify({
												type: 'view-field',
												data: {
													type: 'state',
													property: 'state',
													ui: dom,
													obj: obj,
													options: {}
												}
											});
						 					
					 					}
				 					}
				 					, name: {
					 					title:'Name'
					 					, view: function(dom, obj) {
						 					
					 						sb.notify({
												type: 'view-field',
												data: {
													type: 'title',
													property: 'name',
													ui: dom,
													obj: obj,
													options: {}
												}
											});
											
										}
				 					}
				 					, managers: {
					 					title:'Managers'
					 					, view: function(dom, obj) {
						 					
					 						sb.notify({
												type: 'view-field',
												data: {
													type: 'users',
													property: 'managers',
													ui: dom,
													obj: obj,
													options: {}
												}
											});
						 					
					 					}
				 					}
				 					, start_date: {
					 					title:'Start Date'
					 					, view: function(dom, obj) {
						 					
						 					sb.notify({
												type: 'view-field',
												data: {
													type: 'date',
													property: 'start_date',
													ui: dom,
													obj: obj,
													options: {}
												}
											});
						 					
					 					}
				 					}
				 					, locations: {
					 					title:'Locations'
										, view: function(dom, obj) {
											
											sb.notify({
												type: 'view-field',
												data: {
													type: 'locations',
													property: 'locations',
													ui: dom,
													obj: obj,
													options: {}
												}
											});
											
										}
				 					}
				 					, head_count: {
					 					title: 'Guest Count'
					 					, type: 'quantity'
				 					}
				 					, invoice_value: {
					 					title: 'Value'
										, type: 'usd'
										, view: function(ui, obj) {
											
											ui.makeNode('value', 'div', {
												text: '$' + (obj.invoice_value/100).formatMoney()
											});
											
											ui.patch();
										}
				 					}
				 					, description: {
					 					title: 'Project Notes'
					 					, type: 'text'
				 					}
								}
								, objectType: 'groups'
								, actions: {}
								, selectedView: 'table'
								, menu: {
									subviews: false
								}
								, subviews: {
									table: {
										hideSelectionBoxes:true
									}
								}
								, where: {
									main_contact: obj.id
									, group_type: 'Project'
									, childObjs: {
										name:true
										, state:true
										, locations:{
											name:true
										}
										, main_contact: {
											fname: true
											, lname: true
											, company: {
												name: true
											}
										}
										, group_type:true
										, type:{
											name:true,
											states:true
										}
										, managers:{
											fname:true,
											lname:true,
											profile_image:true
										}
										, start_date:true
										, invoice_value:true
										, head_count: true
										, description: true
									}
								}
								
							};
						
						sb.notify({
							type:'show-collection',
							data: collectionsSetup
						});
						
					}
				});
				
				ui.patch();
				ui.wrapper.modal.show();
				
			}
			
			setContactLeadSource(ui, function() {

				var collectionsSetup = {
						domObj: ui
						, state: state
						, selectedView: 'table'
						, objectType: 'contacts'
						, actions: {
							downloadCSV: true
							, comments: true
							, copy: false
							, archive: false
						}
						, fields: {
							state: {
								title: 'Status'
								, view: function(dom, obj) {
									
									sb.notify({
										type: 'view-field',
										data: {
											type: 'state',
											property: 'state',
											ui: dom,
											obj: obj,
											options: {
												fluid: true,
												edit: true
											}
										}
									});
									
								}
							}
							, fname: {
								title: 'First Name',
								type: 'title',
								isSearchable: true
							}
							, lname: {
								title: 'Last Name'
								, type: 'title'
								, isSearchable: true
							}
							, company: {
								title: 'Company'
								, type: 'url'
								, isSearchable: true
							}
							, email: {
								title: 'Email',
								view: function(c, obj){
									
									if (obj.contact_info) {
										
										var infoToShow = _.filter(obj.contact_info, function(info){
											
											if(info.type && info.type.data_type){
												return (info.type.data_type === 'email' && info.is_primary === 'yes');
											}
											
											return false;
											
										})[0];
	
										if (_.isEmpty(infoToShow)) {
											infoToShow = _.filter(obj.contact_info, function(info){
											
												if(info.type && info.type.data_type){
													return info.type.data_type === 'email';
												}
												
												return false;
												
											})[0];
										}
										
										if(infoToShow) {
											
											c.makeNode('t', 'div', {text:infoToShow.info});
										
										} else {
											return '';
										}	
										
									}
									
								}
							}
							, phone: {
								title: 'Phone',
								view: function(c, obj){
	
									if (obj.contact_info) {
										
										var infoToShow = _.filter(obj.contact_info, function(info){
										
											if(
												info.type 
												&& info.type.data_type
												&& info.is_primary === 'yes'
											){
												return (info.type.data_type === 'cellphone' || info.type.data_type === 'phone');
											}
											
											return '';
											
										})[0];
	
										if(infoToShow) {
											
											c.makeNode('t', 'div', {text: formatPhoneNumber(infoToShow.info)});
											
										} else {
											return '';
										}	
										
									}
									
								}
							}
							, lead_source: {
								title: 'Lead Source'
								, type: 'title'
								, isSearchable: true
							}
							, notes: {
								title: 'Notes'
							}
							, date_created: {
								title:'Date Created'
								, type: 'date'
							}
							, manager: {
								title: 'Manager'
								, type: 'users'
							}
							, projects: {
								title: 'Projects'
								, view: function(ui, obj) {
	
									if (
										obj.projects !== undefined
										&& !_.isEmpty(obj.projects)
									) {
										
										ui.makeNode('btn', 'div', {
											css: 'ui fluid button'
											, text: '<i class="ui eye icon"></i> Related Projects'
										}).notify('click', {
											type: [sb.moduleId+'-run'],
											data: {
													run: function(data) {
		
														display_relatedProjects(data.ui, data.obj);
														
													}
													, ui: ui
													, obj: obj
												}
											}, sb.moduleId);	
										
									} else {
										
										ui.makeNode('noProjects', 'div', {
											text: 'No related projects'
											, css: 'text-center'
										});
										
									}
									
								}
							},
							last_updated: {
								title:'Last Updated'
								, type: 'hidden'
								, isHidden: true
							}
						}
						, menu: {
							subviews: false
						}
						, subviews: {
							table: {
								hideSelectionBoxes:true
							}
						}
						, parseData: function(data, callback) {
							
							var contacts = data.data;
							var leadIds = [];
							var leads = _.chain(contacts)
												.each(function(c) { leadIds.push(c.id); })
													.sortBy(function(c){ return c.last_updated; })
														.reverse()
															.value();
	
							sb.data.db.obj.getWhere('groups', {
								group_type: 'Project'
								, main_contact: {
									type: 'or'
									, values: leadIds
								}
								, childObjs: {
									name:true
									, main_contact: {
										fname: true
										, lname: true
										, company: {
											name: true
										}
									}
								}
							}, function(projects) {
		
								_.each(projects, function(p) {
									
									var lead = _.findWhere(leads, {id: p.main_contact.id});
									
									if (lead.hasOwnProperty('projects')) {
										
										p.groupObj = lead;
										
										lead.projects.push(p);
										
									} else {
										
										lead.projects = [];
										
										p.groupObj = lead;
										
										lead.projects.push(p);
										
									}
									
								});
							    
							    data.data = leads;
							
								return callback(data);
								
							});
							
						}
						, filterBy: {
							manager: {
								title: 'Manager'
								, type: 'user'
								, defaultText: 	'Any Manager'
								, getOptions: 	function (callback) {
									
									sb.data.db.obj.getWhere(
										'users'
										, {
											enabled: 1
											, childObjs: {
												id: true
												, fname: true
												, lname: true
											}
										}
										, function (opts) {
											
											callback(_.sortBy(opts, 'fname'), true);
											
										}
									);
									
								}
								, parseSelection: function (selection, options) {
	
									if (parseInt(selection) === 0) {
										delete options.where.manager;
										return;
									}
	
									options.where.manager = parseInt(selection);
																	
								}
							}
						}
						,sortCol: "last_updated"
						,sortDir: "desc"
						, where: {
							state: {
								type: 'or'
								, values: [1, 10, 6, 5]
							}
							, type: 1929600
							, childObjs: {
								fname: true
								, lname: true
								, type: {
									name: true
									, states: true
								}
								, company:{
									name: true
								}
								, state: true
								, manager: {
									fname: true
									, lname: true
									, profile_image: true
								}
								, contact_info: {
									info: true
									, is_primary: true
									, name: true
									, title: true
									, type: 1
								}
								, date_created: true
								, lead_source: true
								, last_updated: true
							}
						}
					};
				
				sb.notify({
					type:'show-collection',
					data: collectionsSetup
				});
				
			});
			
		}
		
		function display_ProjectReport(ui) {

            var stateVals = [0, 1, 2, 7];
            ///Infinity - "Event Management"
            var project_type = 1625566;

            if ( appConfig.instance == 'dreamcatering') {

                stateVals = [0, 1, 2, 7];
                ///Dream Catering - ""Event Management"
                project_type = 8248140;

            }

            if ( appConfig.instance == 'nlp') {

                stateVals = [0, 1, 2, 7];
                ///NLP - "Event Management"
                project_type = 8248747;

            } else if ( appConfig.instance == 'nlp') {
            
                stateVals = [1,2,3];
                project_type = 1766238;

            }

            var collectionsSetup = {
					domObj: ui
					, state: state
					, selectedView: 'table'
					, objectType: 'groups'
					, actions: {
						downloadCSV: true
					}
					, fields: {
						managers: {
							title:'Managers'
							, view: function(dom, obj) {
								
								sb.notify({
								   type: 'view-field',
								   data: {
									   type: 'users',
									   property: 'managers',
									   ui: dom,
									   obj: obj,
									   options: {}
								   }
							   });
								
							}
						}
						, name: {
							title:'Name'
							, type: 'title'
						}
	 					, state: {
		 					title:'Status'
							, type:'state'
	 					}
	 					, main_contact: {
		 					title: 'Point of Contact (PoC)'
		 					, type: 'url'
		 					, isSearchable: true
	 					}
	 					, poc_status: {
		 					title: 'PoC Status'
		 					, view: function(ui, obj) {
			 					
			 					if (obj.main_contact) {
				 					
				 					sb.notify({
										type: 'view-field',
										data: {
											type: 'state',
											property: 'state',
											ui: ui,
											obj: obj.main_contact,
											options: {
												fluid: true,
												edit: true
											}
										}
									});	
				 					
			 					}
			 					
		 					}
	 					}
	 					, phone: {
		 					title: 'Phone'
		 					, view: function(c, obj) {
			 					
			 					if (obj.main_contact) {
				 					
				 					var infoToShow = _.filter(obj.main_contact.contact_info, function(info){
									
										if(
											info.type 
											&& info.type.data_type
											&& info.is_primary === 'yes'
										){
											return (info.type.data_type === 'cellphone' || info.type.data_type === 'phone');
										}
										
										return '';
										
									})[0];
									
									if(infoToShow) {
										
										c.makeNode('t', 'div', {text: formatPhoneNumber(infoToShow.info)});
										
									} else {
										return '';
									}	
				 					
			 					}
			 					
		 					}
	 					}
	 					, email: {
		 					title: 'Email'
		 					, view: function(c, obj) {
			 					
			 					if (obj.main_contact) {
				 					
									var infoToShow = _.filter(obj.contact_info, function(info){
											
										if(info.type && info.type.data_type){
											return (info.type.data_type === 'email' && info.is_primary === 'yes');
										}
										
										return false;
										
									})[0];

									if (_.isEmpty(infoToShow)) {
										infoToShow = _.filter(obj.contact_info, function(info){
										
											if(info.type && info.type.data_type){
												return info.type.data_type === 'email';
											}
											
											return false;
											
										})[0];
									}
									
									if(infoToShow) {
										
										c.makeNode('t', 'div', {text:infoToShow.info});
									
									} else {
										return '';
									}	
				 					
			 					}
			 					
		 					}
	 					}
	 					, head_count: {
		 					title: 'Guest Count'
		 					, type: 'quantity'
	 					}
	 					, invoice_value: {
		 					title: 'Value'
							, type: 'usd'
							, view: function(ui, obj) {
								
								ui.makeNode('value', 'div', {
									text: '$' + (obj.invoice_value/100).formatMoney()
								});
								
								ui.patch();
							}
	 					}
	 					, description: {
		 					title: 'Project Notes'
		 					, type: 'text'
	 					}
					}
					, menu: {
						subviews: false
					}
					, subviews: {
						table: {
							hideSelectionBoxes:true
		 					, hideRowActions:  true
						}
					}
					, parseData: function(data, callback) {
						var contactStatesAvail = [4, 1, 5, 6];
						if ( appConfig.instance == 'dreamcatering') {
							data.data = data.data.filter(function (_obj) {
								if (_obj.main_contact && _obj.main_contact.state != undefined) {
									return contactStatesAvail.includes(_obj.main_contact.state);
								}
							});
							callback(data);
						}
						else{
							callback(data);
						}
					}
					, rangeOver: 'start_date'
					, counter: true
					, where: {
						state: {
							type: 'or'
							, values: stateVals
						}
						, type: project_type
						, group_type: 'Project'
						, childObjs: {
							state: true
							, group_type: true
							, name: true
							, main_contact: {
								fname: true
								, lname: true
								, contact_info: {
									info: true
									, is_primary: true
									, name: true
									, title: true
									, type: 1
								}
								, type: {
									name: true
									, states: true
								}
								, state: true
							}
							, managers: {
								fname: true
								, lname: true
								, profile_image: true
							}
							, head_count: true
							, invoice_value: true
							, description: true
							, type: {
								name: true
								, states: true
							}
						}
					}
				};

            sb.notify({
				type:'show-collection',
				data: collectionsSetup
			});
			
		}
		
		ui.empty();
		
		ui.makeNode('wrapper', 'div', {});
		
		ui.wrapper.makeNode('lb_1', 'div', {});
		
		ui.wrapper.makeNode('menu', 'div', {
			css: 'ui secondary stackable blue menu'
		});
		
		ReportBody_UI = ui.wrapper.makeNode('seg', 'div', {
			css: 'ui bottom attached active tab basic segment'
		});
		
		display_tabs(ui.wrapper.menu);
		
		display_ProjectReport(ReportBody_UI);
		
		ui.patch();
		
	}
	
	return {

		init:function(){
			
			var instance = check_instance();
			var contactsTitle = 'Contacts';
			var companyPageMenu = {
				edit: {
					action: function(obj, dom, state, onComplete) {

						state.onComplete = onComplete;
						
						companyView.edit(obj, dom, state, undefined);
						
					}
				}
				, followUnfollow:true
				, invoiceStatement: {
					title: 		'Account Statement'
					, icon: 	'usd green'
					, ui: 		false
					, action: 	function(obj, dom, state, draw){
						
						sb.data.db.obj.getWhere('invoices', {
							main_client:obj.id,
							paged:{
								count:false,
								page:0,
								pageLength:1,
								paged:true,
								sortCol:'date_created',
								sortDir:'desc',
								sortCast:'string'
							}
						}, function(invoices){
							
							if (!_.isEmpty(invoices)) {
								
								invoices.data[0].main_client = obj;
							
								sb.notify({
									type: 'app-navigate-to',
									data: {
										type: 'object',
										object: invoices.data[0]
									}
								});
								
							} else {
								
								sb.notify({
									type: 'display-alert',
									data: {
										header: 'No invoices found',
										body: 'There are no invoices related to this company.',
										color: 'red'
									}
								});
								
							}
							
						});
						
						

/*
						var googleQuery = {
							fname:o.fname,
							lname:o.lname,
							company:'',
							location:''
						};
						var locations = [];

						if(o.company && o.company.hasOwnProperty('name')){
							googleQuery.company = o.company.name;
						}
														
						if(o.contact_info){
							
							var info = o.contact_info.filter(function( element ) {
								   return element != null;
								});
							                        
		                        _.each(info, function(i){
		                            
		                            if(i.type.id == 53 || i.type.data_type == 'address'){
		                                locations.push(i);
		                            }
		                            
		                            if(i.type.data_type == 'address' && appConfig.instance != 'thelifebook'){
		                                locations.push(i);
		                            }
		                            
		                        });
		 
		                        locations = locations.sort(function(a,b) { 
		                                return b.id - a.id;
		                            });
		                                            
		                        if(locations.length > 0){
		                            googleQuery.location = ' '+ locations[0].city +', '+ locations[0].state;
		                        } 
	                         }  
						
						sb.notify({
							type:'search-in-google',
							data:{
								link:`http://www.google.com/search?q=${googleQuery.fname} ${googleQuery.lname} ${googleQuery.company} ${googleQuery.location}`
							}													
						});	
*/
																	
					}
				}
				, archive:true
				, taxExempt:{
					action:function(obj, dom, state, draw){

						taxExempt(obj, dom, function(response){
							draw(response);
						});
						
					}
					, title:'Change Tax Status'
					, icon:'certificate'
				}
			};
			var contactPageMenu = {
				edit:{
					action:function(o, dom, state, draw){

						contactView.edit(o, dom, state, draw);											
					}
				}
				, followUnfollow: 	true
				, google: 			{
					title: 		'Google Search'
					, icon: 	'google blue'
					, ui: 		false
					, action: 	function(o, dom, state, draw){

						var googleQuery = {
							fname:o.fname,
							lname:o.lname,
							company:'',
							location:''
						};
						var locations = [];

						if(o.company && o.company.hasOwnProperty('name')){
							googleQuery.company = o.company.name;
						}
														
						if(o.contact_info){
							
							var info = o.contact_info.filter(function( element ) {
								   return element != null;
								});
							                        
		                        _.each(info, function(i){
		                            
		                            if(i.type.id == 53 || i.type.data_type == 'address'){
		                                locations.push(i);
		                            }
		                            
		                            if(i.type.data_type == 'address' && appConfig.instance != 'thelifebook'){
		                                locations.push(i);
		                            }
		                            
		                        });
		 
		                        locations = locations.sort(function(a,b) { 
		                                return b.id - a.id;
		                            });
		                                            
		                        if(locations.length > 0){
		                            googleQuery.location = ' '+ locations[0].city +', '+ locations[0].state;
		                        } 
	                         }  
						
						sb.notify({
							type:'search-in-google',
							data:{
								link:`http://www.google.com/search?q=${googleQuery.fname} ${googleQuery.lname} ${googleQuery.company} ${googleQuery.location}`
							}													
						});	
																	
					}
				}
				, invoiceStatement: {
					title: 		'Account Statement'
					, icon: 	'usd green'
					, ui: 		false
					, action: 	function(obj, dom, state, draw){
						
						sb.data.db.obj.getWhere('invoices', {
							main_contact:obj.id,
							paged:{
								count:false,
								page:0,
								pageLength:1,
								paged:true,
								sortCol:'date_created',
								sortDir:'desc',
								sortCast:'string'
							}
						}, function(invoices){
							
							if (!_.isEmpty(invoices)) {
								
								invoices.data[0].main_contact = obj;
							
								sb.notify({
									type: 'app-navigate-to',
									data: {
										type: 'object',
										object: invoices.data[0]
									}
								});	
								
							} else {
								
								sb.notify({
									type: 'display-alert',
									data: {
										header: 'No invoices found',
										body: 'There are no invoices related to this contact.',
										color: 'red'
									}
								});
								
							}
							
						});
						
						

/*
						var googleQuery = {
							fname:o.fname,
							lname:o.lname,
							company:'',
							location:''
						};
						var locations = [];

						if(o.company && o.company.hasOwnProperty('name')){
							googleQuery.company = o.company.name;
						}
														
						if(o.contact_info){
							
							var info = o.contact_info.filter(function( element ) {
								   return element != null;
								});
							                        
		                        _.each(info, function(i){
		                            
		                            if(i.type.id == 53 || i.type.data_type == 'address'){
		                                locations.push(i);
		                            }
		                            
		                            if(i.type.data_type == 'address' && appConfig.instance != 'thelifebook'){
		                                locations.push(i);
		                            }
		                            
		                        });
		 
		                        locations = locations.sort(function(a,b) { 
		                                return b.id - a.id;
		                            });
		                                            
		                        if(locations.length > 0){
		                            googleQuery.location = ' '+ locations[0].city +', '+ locations[0].state;
		                        } 
	                         }  
						
						sb.notify({
							type:'search-in-google',
							data:{
								link:`http://www.google.com/search?q=${googleQuery.fname} ${googleQuery.lname} ${googleQuery.company} ${googleQuery.location}`
							}													
						});	
*/
																	
					}
				}
				, archive: 			true
			};
			
			if ( appConfig.instance == 'foundation_group' )
				foundationGroup = true;

			var headerManagerTitle = ( foundationGroup ) ? 'CSM' : 'Manager';
			
			companyPageMenu.makeInstance = {
				action: 		function (obj, modal, state, draw) {
					
					createClientPortal(
						obj
						, modal.body
						, state
						, function () {
							
							modal.hide();
							
						}
						, 'company'
					);
					
				}
				, adminOnly: true
				, title: 	'Portal Access'
				, icon: 		'key'
				, draw: 		function (obj, setBtn) {
					
					sb.data.db.obj.getWhere(
						'portal_access_token'
						, {
							company: obj.id
						}
						, function (tokens) {
							
							if (!_.isEmpty(tokens)) {
								
								setBtn('yellow key', 'Revoke Portal')
								
							} else {
								
								setBtn('grey key', 'Create Portal');
								
							}
							
						}
					);
					
				}
			};
			
			contactPageMenu.makeInstance = {
				action: 		function (obj, modal, state, draw) {
					
					createClientPortal(
						obj
						, modal.body
						, state
						, function () {
							
							modal.hide();
							
						}
						, 'contact'
					);
					
				}
				// , adminOnly: true
				, title: 	'Portal Access'
				, icon: 		'key'
				, draw: 		function (obj, setBtn) {
					
					sb.data.db.obj.getWhere(
						'portal_access_token'
						, {
							contact: 		obj.id
							, is_active: 	true
						}
						, function (tokens) {
							
							if (!_.isEmpty(tokens)) {
								
								setBtn('yellow key', 'Revoke Portal')
								
							} else {
								
								setBtn('grey key', 'Create Portal');
								
							}
							
						}
					);
					
				}
			};
			
			if(instance !== false) {
				
				contactsTitle = instance.navTitle;
				
			}
			
			if ($(window).width() <= 768) {
				
				onMobile = true;
				
			}

			sb.listen({
				'crm-run':this.run,
				'crm-show':this.show,
				'crm-init-create-flow': this.initCreateFlow,
				
				// External use
				'create-new-contact': this.createNewContact
			});
			
			var toolRegistrationsSetup = [
				// Main tool
				{
					id: 'crmTool'
					, name: contactsTitle
					, title: contactsTitle
					, layers: ['hq', 'team', 'myStuff']
					, tip: 'Manage Contacts & Companies'
					, icon:{
						type:'address book',
						color:'blue'
					}
					, default:true
					, settings: [
						{
							object_type:'contact_info_types',
							name:'1. Forms of Contact',
							action:contactInfoTypes
						},
						{
							object_type:'contact_types',
							name:'2. Types of Contacts',
							action:contactTypeSettings
						},
						{
							object_type:'company_categories',
							name:'3. Types of Companies/Vendors'
                            , action:contactTypeSettings
						}
					]
					, mainViews:[
						{
							dom:function(dom, state, draw){
								
								crm_cache.domObj = dom;
								crm_cache.state = state;
								crm_cache.draw = draw;

								if(state.layer === 'hq') {
									
									state.viewing = 'headquarter';
									
								} else if(state.layer === 'team') {
									
									state.viewing = 'team';
									state.tagged_with = state.where.tagged_with;

								} else if(state.layer === 'myStuff') {
									
									state.viewing = 'mystuff';
									state.tagged_with = state.where.tagged_with;
									
								}
								
								var onContact = true;
								
								if(appConfig.instance == 'foundation_group')
									onContact = false;
								
								crm(dom, state, draw, onContact);
								
							}
						}
					]
					, boxViews: [
						// Contacts overview
						{
							id:'contactsOverview',
							width: 'four',
							title: 'Contacts',
							dom:function(dom, state, draw){
								
								var where = {
										paged:{
											count: true,
											page: 0,
											pageLength: 5,
											sortCol: 'date_created',
											sortDir: 'desc'
										}
									};

								if(state.hasOwnProperty('where')
									&& state.where.hasOwnProperty('tagged_with')) {
									
									where.tagged_with = state.where.tagged_with;
									
								}

								sb.data.db.obj.getWhere('contacts', where, function(allTimeData) {
									
									where.date_created = {
										type:'between',
										start:moment().local().startOf('day').format('X'),
										end:moment().local().endOf('day').format('X')
									};
									
									where.childObjs = 2;
									
									sb.data.db.obj.getWhere('contacts', where, function(createdTodayData){

										var createdToday = '0';
										var allTime = '0';

										if(createdTodayData){
											if(createdTodayData.length != 0){
												createdToday = createdTodayData.recordsTotal;
											}
										}
										
										if(allTimeData){
											if(allTimeData.recordsTotal){
												allTime = allTimeData.recordsTotal;
											}
										}
										
										dom.makeNode('stats', 'div', {css:'ui mini horizontal statistics'});											

										dom.stats.makeNode('today', 'div', {css:'ui statistic'});
										dom.stats.today.makeNode('value', 'div', {css:'value', text:createdToday.toString()});
										dom.stats.today.makeNode('label', 'div', {css:'label', text:'Created today'});

										dom.stats.makeNode('total', 'div', {css:'ui statistic'});
										dom.stats.total.makeNode('value', 'div', {css:'value', text:allTime});
										dom.stats.total.makeNode('label', 'div', {css:'label', text:'Total'});
										
										draw(dom);
										
									});
									
								});											
																															
							}

						}
						// Recent Contacts
						, {
							id: 'recentContacts',
							width: 'sixteen',
							title:'Recent Contacts',
							collections: {
								selectedView: 'list',
								fields: {
									icon: {
										type: 'title',
										view: function(ui, obj) {
											
											ui.makeNode('label', 'div', {css: 'label'}).makeNode('ava', 'div', {tag:'i', css: 'user icon'});
											
										}
									},
									fname: {
										title: 'First Name',
										type: 'title',
										isSearchable: true												
									},
									lname: {
										title: 'Last Name',
										type: 'title',
										isSearchable: true
									},
									date_created: {
										title: 'Created',
										type: 'title',
										view: function(ui, obj) {
											
											ui.makeNode('date', 'div', {
												css: 'date', 
												text:`A ${obj.type.name} at ${obj.company.name}, <span class="text-muted">created ${moment(obj.date_created).local().fromNow()}</span>`
											});
											
										}
									},
									state: {
										title: 'Status',
										type: 'state'
									},
									type: {
										title: 'Type',
										type: 'type'
									}
								},
								actions: {},
								objectType: 'contacts',
								emptyMessage: 'No contacts created recently',
								where: {
									childObjs: {
										fname: true,
										lname: true,
										type: true,
										company: true,
										state: true,
										value: true
									}
								},
								subviews: {
									list: {
										hideTimeRangeFilter: true
									}
								}
							}
						}
						// Contacts by Type
						, {
							id: 'contactsByType',
							width: 'eight',
							title:'Contacts by Type',
							ignoreLayers: ['team', 'myStuff'],
							collections: {
								fields: {
									name: {
										title: 'Name',
										type: 'title',
										link: function(o) {
											
											return sb.data.url.createPageURL(
												'hqTool', 
												{
													tool: 'crmTool',
													params: {
														type: o.id
													}
												}
											);
											
										}
									},
									contacts: {
										title: '# of contacts',
										view: function(ui, obj) {
											
											ui.makeNode('total', 'div', {
												text: obj.contactsTotal || '0',
												css: 'text-center'
											});
											
											ui.patch();
											
										}	
									},
									tags: {
										view: function() {
											return false;
										}
									},
									select: {
										view: function() {
											return false;
										}
									}
								},
								showPaging: true,
								selectedView: 'table',
								objectType: 'contact_types',
								emptyMessage: 'No contact types available',
								subviews: {
									table: {
										style: 'padding: 0 !important',
										hideTimeRangeFilter: true
									}
								},
								parseData: function(data, callback, query, subview, range, types) {

									var typeIds = _.pluck(data.data, 'id');
									var state = appCofig = appConfig.state;
									var groupSumQuery = {
											groupOn: 'type',
											type: {
												type: 'or',
												values: typeIds
											},
											dateRange: {
												start: moment("20111031", "YYYYMMDD").format('YYYY-MM-DD HH:mm:ss.SS'),
												end: moment().add(1, 'year').format('YYYY-MM-DD HH:mm:ss.SS')
											}
										};


									if(state.hasOwnProperty('team')) {
										
										groupSumQuery.tagged_with = [state.team.id];
										
									} else if(state.hasOwnProperty('myStuff')) {
										
										groupSumQuery.tagged_with = [+sb.data.cookie.userId];
										
									}

									sb.data.db.obj.getGroupSum('contacts', 'id', groupSumQuery, function(metrics) {

										_.each(metrics, function(metric) {
											
											var type = _.findWhere(data.data, {id: parseInt(metric['0'])});
											
											type.contactsTotal = metric.grouped_total;
											
										});
										
										data.data = _.sortBy(data.data, 'name');
										
										callback(data);
										
									});
										
								},
								where: {
									childObjs: {
										name: true
									}
								}
							}
						}
					]
				},
				// Contact - single object view
				{
					id: 'contacts-obj',
					type: 'object-view',
					title: 'Contact',
					icon: 'address card outline',
					dom: function(dom, state, draw){

						//sb.data.db.obj.getById('contacts', state.id, function(obj){
							
							state.onDelete = function(){
								
								sb.notify({
									type:'app-navigate-to',
									data:{
										type:'UP'
									}
								});
								
							};
							
							state.onSave = state.onBack = contactView;

							contactView(state.pageObject, dom, state, draw);

						//}, 3);
					},
					header:{
						title:true,
						fixedTitle:function(ui, pageObj){

							var addressTxt = getAddressTxt(pageObj);
							
							ui.makeNode('sub', 'div', {
								tag: 'h4',
								css:'ui header'
								, style:'margin:0;' 
								, text:getGroupTxt(pageObj) + addressTxt
							});			
																
						}
						, tags:false
/*
						, type:{
							field:'type'
							, options:{
								edit:true
								, size:'huge'
								, color:'orange'
								, icon:'mail'
								, detail:'text here'
								, objectType: 'contact_types'
							}
						}
						, user:{
							field:'manager'
							, title:'Manager'
							, edit:true
						}
						, state:true
*/
						, menu:contactPageMenu
						, select:{
							fname:true
							, lname:true
							, manager:{
								fname:true
								, lname:true
							}
							, state:true
							, contact_info: {
								type: {
									data_type: true
								}
								, info: true
								, city: true
								, state: true
								, zip: true
								, street: true
								, object_id: true
							}
							, company: {
								name: true
							}
							, type: {
								name: true
								, states:true
							}
							, selectionObj: true
						}
					},
					menu: function (state, draw, layer) {

						try {
							
							sb.notify({
								type: 	'get-object-tools'
								, data:	{
									draw: 		draw
									, pageObject: 	state.pageObject
									, type:  state.pageObject.type
								}
							});
							
						} catch (e) {
							
							console.log(e);
							
						}
						
					}
				}
				// Company - single object view
				, {
					id:'companies-obj',
					type:'object-view',
					title:'Companies',
					icon:'building',
					dom:function(dom, state, draw){

						dom.makeNode('loader', 'div', {css: 'ui active centered inline loader'});
						dom.patch();

						delete dom.loader;

						state.onEdit = {
							action:{
								onSave:companyView
							}
						};
						
						state.onDelete = function(){
							
							sb.notify({
								type:'app-navigate-to',
								data:{
									type:'UP'
								}
							});
						
						}
						
						//sb.data.db.obj.getById('companies', state.pageObject.id, function(company){

							//state.pageObject = company;

							companyView(state.pageObject, dom, state, draw);																		
				
						//}, 1);
							
					},
					header:{
						title:true
						, subTitle:function(ui, pageObj){

							var addressTxt = getAddressTxt(pageObj);
																	
							ui.makeNode('sub', 'div', {
								css:'ui sub header'
								, style:'margin:0;' 
								, text:addressTxt
							});									
							
						}
						, tags:true
						, type:{
							field:'type'
							, options:{
								edit:true
							}
						}
						, user:{
							field:'manager'
							, title: headerManagerTitle
							, edit:false
						}
						, menu: companyPageMenu
						, select:{
							name:true
							, type:{
								name:true
							}
							, manager:{
								fname:true
								, lname:true
							}
							, contact_info: {
								type: {
									data_type: true
								}
								, info: true
								, city: true
								, state: true
								, zip: true
								, street: true
								, object_id: true
							}
							, profile_image: true
							, state:true
							, selectionObj: true
							, is_vendor: true
							, markup_percent: true
							, products: true
							, default_product: true
						}
					},
					menu: function (state, draw, layer) {

						try {
							
							sb.notify({
								type: 	'get-object-tools'
								, data:	{
									draw: 		draw
									, pageObject: 	state.pageObject
									, type:  state.pageObject.type
								}
							});
							
						} catch (e) {
							
							console.log(e);
							
						}
						
					}
				}
				// crm Project Tool
				, {
					id:'crmProject',
					type:'tool',
					name:'Point of Contact',
					tip:'Designate a point of contact for this project from your contacts list.',
					icon: {
						type: 'address book',
						color: 'violet'
					},
					default:true,
					settings:false,
					mainViews:[
						{
							dom:function(dom, state, draw){

								if(state.project.main_contact){
									
									getContactData(state.project.main_contact, function(contactData){

										state.project.main_contact.company = contactData.company;
										state.project.main_contact.contact_info = contactData.info;
										state.project.main_contact.manager = contactData.manager;
										state.project.main_contact.type = contactData.contact_type;
										state.project.main_contact.token = contactData.token;

										contactView(state.project.main_contact, dom, state, draw);											
										
									});
																					
								}else{
									
									searchForAContact(dom, state, draw);
									
								}						
								
							}
						}
					],
					boxViews:[
						// Point of contact tool
						{
							id: 'pointOfContact'
							, width: 'four'
							, title: 'Point of Contact'
							, dom:function(dom, state, draw){

								sb.data.db.obj.getWhere('groups', {
									id: state.project.id,
									group_type: 'Project',
									childObjs: {
										main_contact: {
											fname: true,
											lname: true,
											company: true,
											value: true
										}
									}
								}, function(projectArr) {
								
									var contact = projectArr[0].main_contact; 											

									if (contact) {

										getContactData(contact, function(contactData){

											// Overwrite variables
											contact.company = contactData.company;
											contact.contact_info = contactData.info;

											// Set variables
											var firstName = contact.fname ? contact.fname : '';
											var lastName = contact.lname ? contact.lname : ''
											var contactInfoText = firstName +' '+ lastName;
											
											dom.makeNode('name', 'div', {css: 'ui large header', text: contactInfoText, style:'margin:0;'});
											
											if(contactData.company){
												dom.makeNode('company', 'div', {css: 'meta', text:'<i class="building outline icon"></i>'+ contactData.company.name});
											}
																						
											dom.makeNode('desc', 'div', {css: 'description'}).makeNode('list', 'div', {css: 'ui list'});
											
											if(contactData.info){

												_.each(contactData.info, function(info, i){
												
													var infoText = '';
													
													function contactInfo(infoText){
														return this.makeNode('info-'+i, 'div', {css: 'item'})
															.makeNode('cont', 'div', {css: 'left floated content', text:infoText});									
													}

													if(info.info != ''){
														
														switch(info.type.data_type){
														
															case 'date':
															
																infoText = '<i class="calendar alternate icon"></i>'+ info.info;
																
																break;	
					
															case 'email':
															
																infoText = '<i class="mail icon"></i>'+ info.info;
																return contactInfo.call(this, infoText);	

																break;																	

															case 'other':
															
																infoText = '<i class="stick note outline icon"></i>'+ info.info;
																
																break;
																
															case 'phone':
															
																infoText = '<i class="phone icon"></i>'+ info.info;
																return contactInfo.call(this, infoText);
																
																break;
																
															case 'address':
															
																infoText = '<i class="marker icon"></i>'+ info.city +', '+ info.state;
																return contactInfo.call(this, infoText);
																
																break;
																
															case 'website':
															
																infoText = '<i class="linkify icon"></i>'+ info.info;
																return contactInfo.call(this, infoText);
																
																break;
																
															case 'cellphone':
															
																infoText = '<i class="mobile alternate icon"></i>'+ info.info;
																
																break;
																
															case 'account_info':
															
																infoText = '<i class="clipboard icon"></i>'+ info.info;
																
																break;
														}																																																																																																							
																												
													}														
																										
												}, dom.desc.list);
											
											}
											
											draw(dom);
	
										});																				
																							
									} else {
										
										draw(false);
										
									}
								
								});
							
							}
						}
						, {
							id: 'relatedProjects'
							, width: 'sixteen'
							, title: 'Related Projects'
							, dom: function(dom, state, draw) {
								
								relatedProjects_boxview(dom, state, draw);
								
							}
						}
					]
				}
			];
			
			var salesLeadsListReportSetup = {
					id: 'salesLeadsList',
					type: 'custom',
					title: 'Sales Leads List',
					icon:{
						type: 'funnel dollar',
						color: 'olive'
					},
					dom:function(ui, state, draw, mainDom) {
	
						salesLeadList(ui, state, draw, mainDom);
						
					}
				};

            if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'infinity'
                || appConfig.instance === 'dreamcatering'
				|| appConfig.instance === 'nlp'
			) {

                toolRegistrationsSetup.push(salesLeadsListReportSetup);

            }

			if (sb.dom.isMobile) { 
				
				toolRegistrationsSetup.push({
					id:'mycrm',
					type:'nav-item',
					name: contactsTitle,
					title: contactsTitle,
					tip:'Organize work.',
					icon: {
						type: 'address card outline',
						color: 'green'
					},
					default:true,
					settings:false,
					mainViews:[
						{
							dom:function(dom, state, draw, mainDom){
	
								crm_cache.domObj = dom;
								crm_cache.state = state;
								crm_cache.draw = draw;
								
								state.viewing = 'mystuff';
	
								crm(dom, state, draw, true);
							
							}
						}
					]
				});
				
			} else { 
				
				toolRegistrationsSetup = _.reject(toolRegistrationsSetup, function(reg) {
					return reg.type === 'nav-item';
				});
				
			}
					
			sb.notify({
				type: 'register-tool',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'crmComponent',
						title: 'crm',
						icon: '<i class="blue address book icon"></i>',
						views: toolRegistrationsSetup
					}
				}
			});
			
			if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'infinity'
                || appConfig.instance === 'dreamcatering'
				|| appConfig.instance === 'nlp'
			) {
				
				// Reports
				sb.notify({
					type:'register-report',
					data:{
						id: 'salesLeadsList',
						icon: 'funnel dollar',
						header: 'Sales Leads List',
						subheader: 'Sales leads and related projects.',
						type: 'Accounting'
					}
				});	
				
			}				
			
		},
		
		initCreateFlow: function(setup) {
			
			var state = appConfig.state;
			
			if(setup.hasOwnProperty('options')) {
				
				_.each(setup.options, function(option, name) {
					
					state[name] = option;
					
				});
				
			}

			pointOfContactCreateFlow(setup.domObj, state);
			
		},
		
		run:function(data){ data.run(data); },
		
		// External use
		createNewContact: function (data) {
			
			// Require a company
			if (!data.company) { return false; }
			
			sb.data.db.obj.getById('companies', data.company, function (company) {
				
				var newObj = data.seed;
				newObj.object_bp_type = 	'contacts';
				newObj.is_template = 		0;
				newObj.company = 			data.company;
				newObj.onSave = 			data.onSave || function () {};
				newObj.hiddenFields = ['manager'];
				
				addContact(
					data.ui
					, newObj
					, data.ui
					, company
				);
				
			});
			
		}
		
	};
	
});
