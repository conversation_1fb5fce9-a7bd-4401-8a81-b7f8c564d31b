Factory.registerComponent('calendar', function(sb) {
	
	var UI_CACHE = {
			domObj: {},
			head: {},
			body: {},
			modal: {}
		};
	var DATA_CACHE = {
			dataCall: function() {},
			events: []
		};
	var views = {
			month: {
				name: 'Month',
				show: true,
				body: true,
				action: build_month,
				cellButtons: false
			},
			week: {
				name: 'Week',
				body: true,
				show: true,
				action: build_wtd,
				header: {
					singleDayEvents: false
				}
				, text: 'All Day'
			},
			threeDay: {
				name: '3 Day',
				show: true,
				body: true,
				action: build_wtd
			},
			day: {
				name: 'Day',
				show: true,
				body: true,
				action: build_wtd
			},
			list: {
				name: 'List',
				show: true,
				body: true,
				action: build_list
			}
		};
	var viewDate = moment();
	var viewType = 'month';
	var settings = {
			fullscreen: false,
			monthBar: true,
			topbarSelect: true,
			filter: false
		};
	var activeView = function() {};
	var topbarSelect = false;
	var scheduling = false;
	
	// ********* CALENDAR MENU ************
	
	function build_menu(dom, date, viewing) {

		var hiddenMenu_state = {
				isHidden: true
			};
			
		function display_month(date) {
			
			dom.wrapper.cols.col2.cols.col2.makeNode('month', 'div', {text: date.format('MMMM YYYY'), css: 'ui header center aligned', tag: 'h2'});
			
			dom.wrapper.cols.col2.cols.col2.patch();
			
		}
		
		function build_viewsMenu(views, viewing) {

			var menuOptions = [];
			var currentView = '';

			_.each(views, function(viewObj, type) {
			
				if(viewObj.show === true) {
					
					menuOptions.push({
						name: viewObj.name,
						value: type
					});	
					
				}
				
			}, this);
			
			if(viewing === undefined) {
				
				currentView = views[Object.keys(views)[0]].name;
				
			} else {
				
				currentView = views[viewing].name;
				
			}

			dom.wrapper.cols.col3.makeNode('viewMenu', 'div', {css: 'fluid ui dropdown labeled search icon basic button', listener: {
				type: 'dropdown',
				values: menuOptions,
				placeholder: currentView,
				onChange: function(value) {
					
					if(!_.isEmpty(value)) {
						
						UI_CACHE.body.loading();

						viewType = value;

						currentView = views[value].name;

						$(dom.wrapper.cols.col3.viewMenu.span.selector).text(currentView);
						
						getEventList(DATA_CACHE.dataCall, viewDate, viewType, function(eventList) {
							
							DATA_CACHE.events = eventList;
							
							select_view(views, viewDate.clone(), eventList);
							
							UI_CACHE.body.loading(false);
							
							activeView(viewType, viewDate, eventList);
							
						});
							
					}
					
				}
			}}).makeNode('icon', 'div', {tag: 'i', css: 'calendar icon'});
			
			dom.wrapper.cols.col3.viewMenu.makeNode('span', 'div', {tag: 'span', text: currentView});
			
			dom.wrapper.cols.col3.patch();
			
		}
		
		function build_filter(events) {
			
			var formArgs = {
					types: {
						name: 'types',
						type: 'checkbox',
						options: [
							{
								name: 'test',
								label: 'Staff Schedules',
								value: 'test'
							},
							{
								name: 'test1',
								label: 'Tasks',
								value: 'test1'
							}
						]
					}
				};

			_.each(events, function(event) {
				
				//if(!_.contains(formArgs.types.options, event.type)) {
					
					/*
formArgs.types.options.push({
						name: 'test',
						label: 'Staff Schedules',
						value: 'test'
					});
*/	
					
				//}
				
			}, this);
			
			dom.wrapper.hiddenWrap.makeNode('form', 'form', formArgs);
			
		}

		dom.empty();
		
		dom.makeNode('wrapper', 'div', {css: 'animated fadeIn ui basic segment', style: 'z-index: 1000;'});
		
		dom.wrapper.makeNode('cols', 'div', {css: 'ui equal width grid'});
		
		dom.wrapper.cols.makeNode('col1', 'div', {css: 'column'});
		dom.wrapper.cols.makeNode('col2', 'div', {css: 'column'});
		dom.wrapper.cols.makeNode('col3', 'div', {css: 'middle aligned column'});
		
		// COL 1
		dom.wrapper.cols.col1.makeNode('cols', 'div', {css: 'ui equal width grid'});
		
		dom.wrapper.cols.col1.cols.makeNode('col1', 'div', {css: 'column'});
		dom.wrapper.cols.col1.cols.makeNode('col2', 'div', {css: 'ui column'});
		
		if(settings.filter) {
			
			dom.wrapper.cols.col1.cols.col1.makeNode('filter_btn', 'div', {css: 'ui labeled icon button', text: '<i class="filter icon"></i> Filter'}).notify('click', {
				type: 'calendar-run',
				data: {
					run: function(data) {
						
						if(hiddenMenu_state.isHidden === true) {
							
							hiddenMenu_state.isHidden = false;
							$(dom.wrapper.hiddenWrap.selector).removeClass('hidden');
								
						} else if(hiddenMenu_state.isHidden === false) {
							
							hiddenMenu_state.isHidden = true;
							$(dom.wrapper.hiddenWrap.selector).addClass('hidden');
							
						}
						
					}
				}
			}, sb.moduleId);	
			
		}
		
		dom.wrapper.cols.col1.cols.col2.makeNode('go_today', 'div', {text: 'Today', css: 'ui right floated basic button'}).notify('click', {
			type: 'calendar-run',
			data: {
				run: function(data) {
					
					UI_CACHE.body.loading();
					
					viewDate = moment();
					
					display_month(viewDate);
					
					getEventList(DATA_CACHE.dataCall, viewDate, viewType, function(eventList) {
						
						DATA_CACHE.events = eventList;
						
						select_view(views, viewDate, eventList);
						
						UI_CACHE.body.loading(false);
						
						activeView(viewType, viewDate, eventList);
						
					});
					
				},
				viewType: viewType
			}
		}, sb.moduleId);
		
		// COL 2
		dom.wrapper.cols.col2.makeNode('cols', 'div', {css: 'ui grid'});
		
		dom.wrapper.cols.col2.cols.makeNode('col1', 'div', {css: 'three wide column'});
		dom.wrapper.cols.col2.cols.makeNode('col2', 'div', {css: 'ten wide middle aligned column'});
		dom.wrapper.cols.col2.cols.makeNode('col3', 'div', {css: 'three wide column'});
		
		// LEFT ARROW
		dom.wrapper.cols.col2.cols.col1.makeNode('left', 'div', {
			tag: 'button',
			css: 'ui basic circular mini icon button', 
			text: '<i class="big angle left icon"></i>'
		}).notify('click', {
			type: 'calendar-run',
			data: {
				run: function(data) {
					
					UI_CACHE.body.loading();

					viewDate = viewDate.subtract(1, 'month');

					display_month(viewDate);
					
					getEventList(DATA_CACHE.dataCall, viewDate, viewType, function(eventList) {
						
						DATA_CACHE.events = eventList;
						
						select_view(views, viewDate, eventList);
						
						UI_CACHE.body.loading(false);
						
						activeView(viewType, viewDate, eventList);
						
					});
					
				}
			}
		}, sb.moduleId);
		
		// MONTH
		display_month(viewDate);
		
		// RIGHT ARROW
		dom.wrapper.cols.col2.cols.col3.makeNode(
			'right', 
			'div', 
			{
				tag: 'button', 
				css: 'ui basic circular mini icon button',
				text: '<i class="big angle right icon"></i>'
			}).notify('click', {
				type: 'calendar-run',
				data: {
					run: function(data) {
						
						UI_CACHE.body.loading();
						
						viewDate = viewDate.add(1, 'month');
						
						display_month(viewDate);
						
						getEventList(DATA_CACHE.dataCall, viewDate, viewType, function(eventList) {
							
							DATA_CACHE.events = eventList;
							
							select_view(views, viewDate, eventList);
							
							UI_CACHE.body.loading(false);
							
							activeView(viewType, viewDate, eventList);
							
						});
						
					}
				}
			}, sb.moduleId);
		
		// COL 3
		
		// VIEWS MENU

		if(settings.topbarSelect && topbarSelect === false) {
			build_viewsMenu(views, viewing);		
		} else {
			topbarSelect(dom.wrapper.cols.col3);
		}
		
		dom.wrapper.makeNode('hiddenWrap', 'div', {css: 'ui basic segment hidden'});

		build_filter(DATA_CACHE.events);
		
		dom.patch();
		
		// METHODS
		
		build_menu.display_month = display_month;
		build_menu.build_viewsMenu = build_viewsMenu;
		
	}
	
	// ********* CALENDAR VIEWS ***********
	
	function build_month(dom, date, events) {

		var weekDays = {
				Sun: '',
				Mon: '',
				Tue: '',
				Wed: '',
				Thu: '',
				Fri: '',
				Sat: ''
			};
		var monthRows = 5;
		var currentDay = firstDayOfMonthView(date);
		var cell = {};
		var recurringEvents = _.filter(events, function(obj) {
				
				if(obj.hasOwnProperty('recurring')) {
					
					return obj;
					
				}
				
			});

		function firstDayOfMonthView(date) {
		
			return date.clone().startOf('month').startOf('week');
			
		}
		
		function build_cell(dom, date, events) {

			var dayNum_css = '';
			var dayNum_text = '';
			var isToday = '';
			var duration = '';
//console.log('date:: ', date);
			function build_popUpCell(events) {
				
				dom.wrapper.empty();
									
				dom.wrapper.makeNode('raised', 'div', {css: 'ui raised basic segment popup-cell'});
				
				dom.wrapper.raised.makeNode('head', 'div', {});
				dom.wrapper.raised.makeNode('body', 'div', {css: 'clear'});
				
				dom.wrapper.raised.head.makeNode('col1', 'div', {css: 'pull-left'});
				dom.wrapper.raised.head.makeNode('col2', 'div', {css: 'pull-right right aligned pointer hover-underline'}).notify('click', {
					type: 'calendar-run',
					data: {
						run: function(data) {

							if(views.day.show === true) {
								
								viewType = 'day';
								viewDate = date;
								
								build_menu.display_month(viewDate);
								
								build_menu.build_viewsMenu(views, viewType);
								
								build_wtd(UI_CACHE.body, viewDate, DATA_CACHE.events);	
								
							}
							
						}
					}
				}, sb.moduleId);
				
				dom.wrapper.raised.head.col1.makeNode('close', 'div', {text: '<i class="times icon"></i>', css: 'ui mini icon button'}).notify('click', {
					type: 'calendar-run',
					data: {
						run: function(data) {
							
							dom.empty();
							
							build_cell(dom, date, DATA_CACHE.events);
							
							dom.patch();
							
						}
					}
				}, sb.moduleId);
				
				dom.wrapper.raised.head.col2.makeNode('day', 'div', {text: '<h3>'+ date.format('ddd') +'</h3>', css: 'text-muted'});
				dom.wrapper.raised.head.col2.makeNode('num', 'div', {text: '<h2>'+ date.format('D') +'</h2>', css: 'text-muted'});

				_.each(events, function(event, index) {
					
					if(!event.hasOwnProperty('endTime')) {
						
						dom.wrapper.raised.body.makeNode('box'+event.id, 'div', {text: event.name.slice(0, 20), css: 'month-event ui '+ event.color +' inverted basic segment pointer hover-underline'}).notify('click', {
							type: 'calendar-run',
							data: {
								run: function(data) { 
									
									build_modal(event, data.date); 
									
								},
								date: date.clone()
							}
						}, sb.moduleId);	
						
					} else {
						
						dom.wrapper.raised.body.makeNode('box'+event.id, 'div', {text: '<i class="fa fa-circle" style="color: '+ event.color +';"></i> <span class="text-bold">'+ event.startTime.format('h:mm A') +'</span> ' + event.name.slice(0, 15), css: 'month-event ui basic segment pointer hover-underline'}).notify('click', {
							type: 'calendar-run',
							data: {
								run: function(data) { 
									
									build_modal(event, data.date); 
									
								},
								date: date.clone()
							}
						}, sb.moduleId);               		
						
					}
					
				}, this);
				
				dom.wrapper.patch();
				
			}

			if( (moment().dayOfYear() === date.dayOfYear()) && (moment().year() === date.year()) ) {
				
				isToday = 'today';
				
			}
			
			if(viewDate.month() !== date.month()) {
				
				isToday = 'greyCell';
				
			}
			
			if(viewDate.get('M') === date.get('M')) {
				
				dayNum_css = 'pull-right pointer hover-underline';
				dayNum_text = date.format('D');
				
			} else {
				
				dayNum_css = 'pull-right text-muted pointer hover-underline';
				dayNum_text = date.format('MMM D');
				
			}
			
			dom.makeNode('wrapper', 'div', {
				css: 'month_cell ' + isToday
			});
			
			dom.wrapper.makeNode('head', 'div', {});
			dom.wrapper.makeNode('body', 'div', {css: 'clear'});
			
			dom.wrapper.head.makeNode('btns', 'div', {css: 'pull-left hidden'});
			
			dom.wrapper.head.makeNode('day', 'div', {text: dayNum_text, css: dayNum_css}).notify('click', {
				type: 'calendar-run',
				data: {
					run: function(data) {
						
						if(views.day.show === true) {
							
							viewType = 'day';
							viewDate = date;
							
							build_menu.display_month(viewDate);
							
							build_menu.build_viewsMenu(views, viewType);
	
							build_wtd(UI_CACHE.body, viewDate, DATA_CACHE.events);	
							
						}
						
					}
				}
			}, sb.moduleId);

			events = _.chain(events).filter(function(obj) {
				
				if(obj.endTime) {
					return ( date.clone().add(1, 'hour').isBetween(obj.startTime.clone().startOf('day'), obj.endTime.clone().endOf('day')) );	
				} else {
					return (obj.startTime.dayOfYear() === date.dayOfYear()) && (obj.startTime.year() === date.year());
				}
				
			}).sortBy(function(obj) {
				
				return obj.startTime;
				
			}).value();

			_.each(events, function(event, index) {
			
				if( (index < 2) ) {

					if(!event.hasOwnProperty('recurring')) {
						
						if(!event.hasOwnProperty('endTime')) {
						
							dom.wrapper.body.makeNode('box'+event.id, 'div', {text: event.name.slice(0, 6), css: 'month-event ui '+ event.color +' inverted basic segment pointer hover-underline'}).notify('click', {
								type: 'calendar-run',
								data: {
									run: function(data) {
			
										build_modal(event, date.date);
										
									},
									date: date.clone()
								}
							}, sb.moduleId);	
							
						} else {
	
							if( ((event.startTime.dayOfYear() !== date.dayOfYear()) 
								|| (event.startTime.year() !== date.year())) 
								&& (date.format('ddd') !== 'Sun') ) {
	
								dom.wrapper.body.makeNode('box'+event.id, 'div', {text: '<p style="visibility: hidden;">hidden</p>', css: 'month-event ui basic segment'});                  			
								
							} else {
	
								var daysLeft = 7 - date.weekday();
	
								duration = event.endTime.diff(date, 'days') + 1;
	
								if( daysLeft < duration) {
									
									duration = daysLeft;
								}
								
								dom.wrapper.body.makeNode('box'+event.id, 'div', {text: '<span class="text-bold">'+ event.startTime.subtract(sb.dom.utcOffset, 'hours').format('h:mm A') +'</span> ' + event.name.slice(0, 6), css: 'month-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline', style: 'width: '+ (101 * duration) +'%;'}).notify('click', {
									type: 'calendar-run',
									data: {
										run: function(data) {
				
											build_modal(event, data.date);
											
										},
										date: date.clone()
									}
								}, sb.moduleId);
								
							}                		
							
						}	
						
					}
					
				}
				
				if(index === 2) {
				
					dom.wrapper.body.makeNode('more', 'div', {text: '<i class="fa fa-plus"></i> ' + (events.length - 2) + ' more', css: 'text-center pointer hover-underline'}).notify('click', {
						type: 'calendar-run',
						data: {
							run: function(data) {
								
								build_popUpCell(events);
								
							}
						}
					}, sb.moduleId)
					
				}
				
			}, this);
			
			_.each(recurringEvents, function(event, index) {
				
				if( (index < 2) ) {
					
					if( (event.recurring === 'yes' || event.recurring === 'yes_with_end_date') && date.clone().isAfter(event.startTime) && (_.contains(event.recurring_week_days, date.clone().format('dddd').toLowerCase()) || event.recurring_week_days === null) ) {
					
						if(event.recurring_end_date === undefined) {
							
							dom.wrapper.body.makeNode('box'+event.id, 'div', {text: '<span class="text-bold">'+ event.startTime.subtract(sb.dom.utcOffset, 'hours').format('h:mm A') +'</span> ' + event.name.slice(0, 6), css: 'month-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline'}).notify('click', {
								type: 'calendar-run',
								data: {
									run: function(data) {
			
										build_modal(event, data.date);
										
									},
									date: date.clone()
								}
							}, sb.moduleId);	
							
						} else {
							
							if(date.clone().isBefore(event.recurring_end_date)) {
														
								dom.wrapper.body.makeNode('box'+event.id, 'div', {text: '<span class="text-bold">'+ event.startTime.subtract(sb.dom.utcOffset, 'hours').format('h:mm A') +'</span> ' + event.name.slice(0, 6), css: 'month-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline'}).notify('click', {
									type: 'calendar-run',
									data: {
										run: function(data) {
				
											build_modal(event, data.date);
											
										},
										date: date.clone()
									}
								}, sb.moduleId);
								
							}
							
						}
						
					} else {
						
						if( (date.clone().dayOfYear() === event.startTime.dayOfYear()) && (date.clone().year() === event.startTime.year()) ) {
							
							dom.wrapper.body.makeNode('box'+event.id, 'div', {text: '<span class="text-bold">'+ event.startTime.subtract(sb.dom.utcOffset, 'hours').format('h:mm A') +'</span> ' + event.name.slice(0, 6), css: 'month-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline'}).notify('click', {
								type: 'calendar-run',
								data: {
									run: function(data) {
			
										build_modal(event, data.date);
										
									},
									date: date.clone()
								}
							}, sb.moduleId);	
							
						}
						
					}
					
				}
				
			});
			
			if(views.month.cellButtons !== false && !_.isEmpty(views.month.cellButtons)) {
				
				_.each(views.month.cellButtons, function(buttonObj, name) {
					
					var text = '';
					var icon = '';
					var color = '';
					var tooltip = '';
					var action = function() {};
					var buttonSetup = {};
					
					// Conditions
					if(buttonObj.hasOwnProperty('text')) {
						text = buttonObj.text;
					}
					if(buttonObj.hasOwnProperty('icon')) {
						icon = buttonObj.icon;
					}
					if(buttonObj.hasOwnProperty('color')) {
						color = buttonObj.color;
					}
					
					buttonSetup = {
						text: '<i class="small '+ color + '  ' + icon +' icon"></i> ' + text,
						css: 'pull-left',
						style: 'cursor: pointer;'
					};
					
					if(buttonObj.hasOwnProperty('tooltip')) {
						buttonSetup.tooltip = buttonObj.tooltip;
						buttonSetup.listener = {
							type: 'popup',
							hoverable: true
						};
						
					}
					if(buttonObj.hasOwnProperty('action')) {
						action = buttonObj.action;
					}
					
					dom.wrapper.head.btns.makeNode('btn-'+name, 'div', buttonSetup).notify('click', {
						type: 'calendar-run',
						data: {
							run: function(data) {

								data.date.add(sb.dom.utcOffset, 'hours')
								action(data.dom, data.date);
								
							},
							date: 	date.clone(),
							dom: 	dom.wrapper
						}
					}, sb.moduleId);
					
				});
				
				dom.listeners = [function(selector) {
					
					$(selector).hover(function(e) {
						
						$(dom.wrapper.head.btns.selector).removeClass('hidden');
						
					});
					
					$(selector).on('mouseleave', function(e) {

						$(dom.wrapper.head.btns.selector).addClass('hidden');
						
					});
					
				}];
				
			}
			
		}

		dom.empty();
		
		dom.makeNode('wrapper', 'div', {});
		
		dom.wrapper.makeNode('monthViewTable', 'table', {
			columns: weekDays,
			css: 'monthTable'
		});
		
		_.each(weekDays, function(day, key) {
			
			dom.wrapper.monthViewTable.header.row[key].makeNode('node'+key, 'text', {text: key});
			
		}, this);
		
		if(date.startOf('month').format('ddd') === 'Fri' || date.startOf('month').format('ddd') === 'Sat') {
			monthRows = 6;
		}
		
		if(date.startOf('month').format('ddd') === 'Fri' && date.endOf('month').format('ddd') === 'Sat') {
			monthRows = 5;
		}
		
		for(var i = 0; i < monthRows; i++) {
			
			dom.wrapper.monthViewTable.makeRow(i, [], {});
			
			_.each(weekDays, function(weekDayVal, weekDayKey) {
				
				cell = dom.wrapper.monthViewTable.body[i.toString()][weekDayKey];

				build_cell(cell, currentDay.clone(), events);
				
				currentDay.add(1, 'days');
				
			}, this);
			
		}
		
		dom.patch();
		
	}
	
	function build_wtd(dom, date, events, count) { // week, three-day and day views

		if(count === undefined) { // temporary fix to prevent multiple calls
			
			count = 0;
			
		} else {
			
			if(count > 0) {
				return;
			}
			
		}

		var allDay_events = [];
		var regular_events = [];
		
		function build_topTable(dom, date, events) {

			var tableHeaderText = '';
			var	tableHeaderDate = '';
			var isToday = '';
			var currentDay = firstDayOfWeekView(date.clone());
			var weekDays = switch_tableCols(viewType);
			var activeDate = {};
			var tableCSS = '';
			var cellStyles = [];
			
			function build_allDayPopup(dom, currentDay, events) {

				dom.empty();
											
				dom.makeNode('raised', 'div', {css: 'ui raised basic segment popup-cell-week'});
				
				dom.raised.makeNode('head', 'div', {});
				dom.raised.makeNode('lb', 'lineBreak', {spaces: 2});
				dom.raised.makeNode('body', 'div', {css: 'clear'});
				
				dom.raised.head.makeNode('left', 'div', {css: 'pull-left'});
				dom.raised.head.makeNode('right', 'div', {css: 'pull-right'});
				
				dom.raised.head.left.makeNode('close', 'div', {text: '<i class="times icon"></i>', css: 'ui mini icon button'}).notify('click', {
					type: 'calendar-run',
					data: {
						run: function(data) {
							
							build_wtd(UI_CACHE.body, currentDay, DATA_CACHE.events);
							
						}
					}
				}, sb.moduleId);
				
				dom.raised.body.makeNode('wrapper', 'div', {});
	
				_.each(events, function(event, index) {
					
					if( check_eventDate(viewType, currentDay, event) ) {
						
						if(event.endTime) {
							
							dom.raised.body.makeNode('box-'+event.id, 'div', {css: 'all-day-week-event ui '+ event.color +' inverted basic segment pointer hover-underline', text: '<span style="text-bold">' + event.startTime.format('h:mm A') + '</span> ' + event.name}).notify('click', {
								type: 'calendar-run',
								data: {
									run: function(data) { 
										
										build_modal(event, data.date); 
										
									},
									date: date.clone()
								}
							}, sb.moduleId);	
							
						} else {
							
							dom.raised.body.makeNode('box-'+event.id, 'div', {css: 'all-day-week-event ui '+ event.color +' inverted basic segment pointer hover-underline', text: event.name}).notify('click', {
								type: 'calendar-run',
								data: {
									run: function(data) { 
										
										build_modal(event, data.date); 
										
									},
									date: date.clone()
								}
							}, sb.moduleId);		
							
						}
						
					}
					
				}, this);	
				
				dom.patch();
				
			}
			
			function check_tableHightlight(viewType, key) {
				
				if(viewType === 'week' || viewType === 'day') {
						
					if(activeDate.format('ddd') === 'Sun' || activeDate.format('ddd') === 'Sat') {
					
						dom.tableTop.body['1'][key].listeners = [
							function(selector) {
								$(selector).addClass('greyCell');
							}	
						];
						
					}
					
					if( (activeDate.dayOfYear() === moment().dayOfYear()) && (activeDate.year() === moment().year()) ) {
					
						dom.tableTop.body['1'][key].listeners = [
							function(selector) {
								$(selector).addClass('today');
							}	
						];
	
					}
					
				} else if(viewType === 'threeDay') {
					
					if(activeDate.clone().subtract(1, 'day').format('ddd') === 'Sun' || activeDate.clone().subtract(1, 'day').format('ddd') === 'Sat') {
					
						dom.tableTop.body['1'][key].listeners = [
							function(selector) {
								$(selector).addClass('greyCell');
							}	
						];
						
					}
					
					if( (activeDate.clone().subtract(1, 'day').dayOfYear() === moment().dayOfYear()) && (activeDate.clone().subtract(1, 'day').year() === moment().year()) ) {
					
						dom.tableTop.body['1'][key].listeners = [
							function(selector) {
								$(selector).addClass('today');
							}	
						];
	
					}
					
				}
				
			}
			
			function check_today(viewType, activeDate) {
				
				switch(viewType) {
					
					case 'week':
					case 'day':
					
						return (activeDate.dayOfYear() === moment().dayOfYear()) && (activeDate.year() === moment().year());
						
						break;
						
					case 'threeDay':
					
						return (activeDate.clone().subtract(1, 'day').dayOfYear() === moment().dayOfYear()) && (activeDate.clone().subtract(1, 'day').year() === moment().year());
					
						break;
					
				}
				
			}
			
			function check_eventDate(viewType, activeDate, event) {

				switch(viewType) {
					
					case 'week':
					case 'day':
						
						return ((activeDate.clone().dayOfYear() === event.startTime.dayOfYear()) 
								&& 
								(activeDate.clone().year() === event.startTime.year()));
						
						break;
						
					case 'threeDay':
					
						return (activeDate.clone().subtract(1, 'day').dayOfYear() === event.startTime.dayOfYear()) && (activeDate.clone().subtract(1, 'day').year() === event.startTime.year())
						
						break;
				}
				
			}
			
			function switch_tableCols(viewType) {
			
				switch(viewType) {
					
					case 'week':
					
						return {
							empty: '',
							Sun: '',
							Mon: '',
							Tue: '',
							Wed: '',
							Thu: '',
							Fri: '',
							Sat: ''
						};
						
						break;
						
					case 'threeDay':
					
						return {
							empty: '',
							prevDay: '',
							today: '',
							nextDay: ''
						};
						
						break;
						
					case 'day':
					
						return {
							empty: '',
							singleDay: ''
						};
						
						break;
						
					default:
						return;
					
				}
				
			}
			
			function check_draggable(event) {

				if(event.draggable) {
					
					return {
						moves: true,
						data: {
							type: event.draggable.data.type,
							obj: event.draggable.data.obj
						},
						drop: event.draggable.notify,
						accepts: false,
						copy: false
					};
					
				} else {
					
					return {};
					
				}
				
			}
			
			switch(viewType) {
				
				case 'week':
				
					activeDate = currentDay;
					tableCSS = 'week-table-top';
					cellStyles = [
						'position: absolute !important',
						'position: absolute !important',
						'position: absolute !important',
						'position: absolute !important',
						'position: absolute !important',
						'position: absolute !important',
						'position: absolute !important',
						'position: absolute !important'
					];
					
					break;
					
				case 'threeDay':
				
					activeDate = date;
					tableCSS = 'threeDay-table-top';
					cellStyles = [
						'position: absolute !important',
						'position: absolute !important',
						'position: absolute !important',
						'position: absolute !important'
					];
				
					break;
					
				case 'day':
				
					activeDate = date;
					tableCSS = 'day-table-top';
					cellStyles = [
						'position: absolute !important',
						'position: absolute !important'
					];
					
					break;
				
				default:
					return;	
				
			}

			dom.makeNode('tableTop', 'table', {
				columns: weekDays, 
				css: tableCSS +' weekTable',
				cellStyles: cellStyles
			});
			
			dom.tableTop.makeRow(1, [], {});
			
			_.each(weekDays, function(day, key) {
				
				var eventsCount = 0;
				
				if(key === 'empty') {
					
					tableHeaderText = '';
					tableHeaderDate = '';
					
				} else {
					
					switch(viewType) {
						
						case 'week':
						
							tableHeaderText = key;
							tableHeaderDate = activeDate.clone().format('D');
							
							break;
							
						case 'threeDay':
						
							tableHeaderText = activeDate.clone().subtract(1, 'day').format('ddd');
							tableHeaderDate = activeDate.clone().subtract(1, 'day').format('D');
							
							break;
						
						case 'day':
							
							tableHeaderText = activeDate.clone().format('ddd');
							tableHeaderDate = activeDate.clone().format('D');
							
							break;
						
					}
					
				}

				if(key !== 'empty') {
					
					dom.tableTop.header.row[key].makeNode('wrapper'+key, 'div', {
						css:isToday
					});

					dom.tableTop.header.row[key]['wrapper'+key].makeNode('cont-'+key, 'div', {
							css: 'pointer hover-underline'
						}).notify('click', {
							type: 'calendar-run',
							data: {
								run: function(data) {

									if(views.day.show === true) {
										
										if(viewType === 'threeDay') {
											viewDate = data.activeDate.subtract(1, 'day');
										} else {
											viewDate = data.activeDate;
										}
		
										viewType = 'day';
		
										build_menu.display_month(viewDate);
										
										build_menu.build_viewsMenu(views, viewType);
				
										build_wtd(UI_CACHE.body, viewDate, DATA_CACHE.events, count);
										
										count++; 	
										
									}
									
								},
								activeDate: activeDate.clone()
							}
						}, sb.moduleId);
					
					dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].makeNode('grid', 'div', {
						css: 'ui grid',
						style: 'margin: 0.1rem;'
					});
					
					dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].grid.makeNode('col1', 'div', {
						css: 'ten wide column',
						style: 'padding: 0px !important;'
					});
					
					if(views.week.cellButtons !== false && !_.isEmpty(views.week.cellButtons)) {
						
						dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].grid.col1.makeNode('btns', 'div', {
							css: 'pull-left hidden'
						});
						
						_.each(views.week.cellButtons, function(buttonObj, name) {
					
							var text = '';
							var icon = '';
							var color = '';
							var tooltip = '';
							var action = function() {};
							var buttonSetup = {};
							
							// Conditions
							if(buttonObj.hasOwnProperty('text')) {
								text = buttonObj.text;
							}
							if(buttonObj.hasOwnProperty('icon')) {
								icon = buttonObj.icon;
							}
							if(buttonObj.hasOwnProperty('color')) {
								color = buttonObj.color;
							}
							
							buttonSetup = {
								text: '<i class="small '+ color + '  ' + icon +' icon"></i> ' + text,
								css: 'pull-left',
								style: 'cursor: pointer;'
							};
							
							if(buttonObj.hasOwnProperty('tooltip')) {
								buttonSetup.tooltip = buttonObj.tooltip;
								buttonSetup.listener = {
									type: 'popup',
									hoverable: true
								};
								
							}
							if(buttonObj.hasOwnProperty('action')) {
								action = buttonObj.action;
							}
							
							dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].grid.col1.btns.makeNode('btn-'+name, 'div', buttonSetup).notify('click', {
								type: 'calendar-run',
								data: {
									run: function(data) {
										
										action(data.dom, data.date);
										
									},
									date: activeDate.clone(),
									dom: dom.tableTop.header.row[key]['wrapper'+key]
								}
							}, sb.moduleId);
							
						});
						
						dom.tableTop.header.row[key]['wrapper'+key].listeners = [function(selector) {
					
							$(selector).hover(function(e) {
								
								$(dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].grid.col1.btns.selector).removeClass('hidden');
								
							});
							
							$(selector).on('mouseleave', function(e) {
		
								$(dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].grid.col1.btns.selector).addClass('hidden');
								
							});
							
						}];
						
					}
					
					dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].grid.makeNode('col2', 'div', {
						css: 'six wide column',
						style: 'padding: 0px !important;'
					});
					
					dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].grid.col2.makeNode('dayName', 'div', {
						text: tableHeaderText,
						css: 'right aligned'
					});
					dom.tableTop.header.row[key]['wrapper'+key]['cont-'+key].grid.col2.makeNode('dayNum', 'div', {
						text: '<h2>'+ tableHeaderDate +'</h2>',
						css: 'right aligned'
					});
					
					dom.tableTop.header.row[key]['wrapper'+key].makeNode('clr', 'div', {css: 'clear'});
					
					check_tableHightlight(viewType, key);
					
					dom.tableTop.body['1'][key].makeNode('wrapper', 'div', {});

					if(scheduling === false) {

						_.each(events, function(event, index) {
							
							var eventURL = '';
							var linkedName = event.name;
							
							if(event.objectType){
										
								eventURL = sb.data.url.createPageURL('object-view', {
									id: 		event.id
									, name: 	event.name
									, type: 	event.objectType
									, startAt: 	null
								});
								
								linkedName = '<a href="'+ eventURL +'">' + event.name + '</a>';
							}

							if(event.startTime.isBefore(activeDate.clone().startOf('week').add(1, 'day'))) {
								
								if( (activeDate.clone().format('ddd') === 'Sun' 
									&& event.endTime.isAfter(activeDate.clone())) 
									|| event.endTime.isSame(activeDate.clone()) ) {
									
									dom.tableTop.body['1'][key].wrapper.makeNode('box-'+event.id, 'div', {
										css: 'all-day-week-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline' 
										, text: linkedName
										, style: 'width: ' + (100 + 1) + '% !important;'
										, drag: check_draggable(event)}).notify('click'
										, {
											type: 'calendar-run',
											data: {
												run: function(data) { 
												
													build_modal(event, data.date); 
												
												},
												date: date.clone()
											}
										}, sb.moduleId);
									
								} else {

									if( event.endTime.isAfter(activeDate.clone()) 
										|| event.endTime.isSame(activeDate.clone()) ) {
											
											dom.tableTop.body['1'][key].wrapper.makeNode('box-'+event.id, 'div', {
												css: 'all-day-week-event ui '+ event.color +' inverted tertiary basic segment'
												, style: 'height: 25px; width: ' + (100 + 1) + '% !important;'});
											
										} else {
											
											dom.tableTop.body['1'][key].wrapper.makeNode('box-'+event.id, 'div', {
												css: 'all-day-week-event ui '+ event.color +' inverted tertiary basic segment'
												, style: 'background-color: transparent; height: 25px; width: ' + (100 + 1) + '% !important;'});
											
										}
									
								}
								
							} else {

								if( check_eventDate(viewType, activeDate, event) 
									&& event.endTime.isAfter(activeDate.clone()) ) {

									dom.tableTop.body['1'][key].wrapper.makeNode('box-'+event.id, 'div', {
										css: 'all-day-week-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline' 
										, text: linkedName
										, style: 'width: 101% !important;'
										, drag: check_draggable(event)});
									
								} else {

									if( event.endTime.isSame(activeDate.clone()) ) {
										
										dom.tableTop.body['1'][key].wrapper.makeNode('box-'+event.id, 'div', {
											css: 'all-day-week-event ui '+ event.color +' inverted tertiary basic segment'
											, style: 'height: 25px; width: ' + (100 + 1) + '% !important;'
										});
										
									} else {
										
										if( (activeDate.clone().dayOfYear() === event.endTime.dayOfYear()) ) {
										
											dom.tableTop.body['1'][key].wrapper.makeNode('box-'+event.id, 'div', {
												css: 'all-day-week-event ui '+ event.color +' inverted tertiary basic segment'
												, style: 'height: 25px; width: ' + (100 + 1) + '% !important;'
											});
											
										} else {
											
											if( event.startTime.isBefore(activeDate.clone()) 
												&& event.endTime.isAfter(activeDate.clone()) ) {
													
												dom.tableTop.body['1'][key].wrapper.makeNode('box-'+event.id, 'div', {
													css: 'all-day-week-event ui '+ event.color +' inverted tertiary basic segment'
													, style: 'height: 25px; width: ' + (100 + 1) + '% !important;'
												});	
													
											} else {
															
												dom.tableTop.body['1'][key].wrapper.makeNode('box-'+event.id, 'div', {
													css: 'all-day-week-event ui '+ event.color +' inverted tertiary basic segment'
													, style: 'background-color: transparent; height: 25px; width: ' + (100 + 1) + '% !important;'
												});		
													
											}
											
										}	
										
									}
									
								}
								
							}
							
						}, this);	
						
					} else {

						_.each(events, function(list, time) {

							list = _.sortBy(list, function(event) {
								return event.startTime;
							});
							
							_.each(list, function(event, index) {

								if( ((event.startTime.dayOfYear() === activeDate.dayOfYear()) && (event.startTime.year() === activeDate.year())) ) {
									
									dom.tableTop.body['1'][key].wrapper.makeNode('box'+event.id, 'div', {text: '<span class="text-bold">'+ event.startTime.format('h:mm A') +'</span> ' + event.name.slice(0, 6), css: 'month-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline', drag: check_draggable(event)}).notify('click', {
										type: 'calendar-run',
										data: {
											run: function(data) { 
												
												build_modal(event, data.date); 
												
											},
											date: date.clone()
										}
									}, sb.moduleId);
									
								} else {
									
									if(event.hasOwnProperty('recurring')) {
										
										if( (event.recurring === 'yes' || event.recurring === 'yes_with_end_date') && activeDate.isAfter(event.startTime) && (_.contains(event.recurring_week_days, activeDate.format('dddd').toLowerCase()) || event.recurring_week_days === null) ) {
										
											if(event.recurring_end_date === undefined) {
												
												dom.tableTop.body['1'][key].wrapper.makeNode('box'+event.id, 'div', {text: '<span class="text-bold">'+ event.startTime.format('h:mm A') +'</span> ' + event.name.slice(0, 6), css: 'month-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline', drag: check_draggable(event)}).notify('click', {
													type: 'calendar-run',
													data: {
														run: function(data) { 
															
															build_modal(event, data.date); 
															
														},
														date: date.clone()
													}
												}, sb.moduleId);
												
											} else {
	
												if(activeDate.isBefore(event.recurring_end_date)) {
													
													dom.tableTop.body['1'][key].wrapper.makeNode('box'+event.id, 'div', {text: '<span class="text-bold">'+ event.startTime.format('h:mm A') +'</span> ' + event.name.slice(0, 6), css: 'month-event ui '+ event.color +' inverted tertiary basic segment pointer hover-underline', drag: check_draggable(event)}).notify('click', {
														type: 'calendar-run',
														data: {
															run: function(data) { 
																
																build_modal(event, data.date); 
																
															},
															date: date.clone()
														}
													}, sb.moduleId);
													
												}
												
											}
											
										} 	
										
									}
									
								}
								
							}, this);
							
						}, this);
						
					}
						
					activeDate.add(1, 'day');
					
				}
				
			}, this);
			
			if(scheduling === false) {
				
				dom.tableTop.body['1']['empty'].makeNode('allDay', 'div', {text: views.week.text, css: 'text-muted text-center'});	
				
			}
			
		}
		
		function build_bottomTable(dom, date, events) {

			var currentHour = firstHour(date.clone());
			var currentDay = firstDayOfWeekView(date.clone());
			var weekDays = switch_tableCols(viewType);
			var tableCSS = '';
				
			function switch_tableCols(viewType) {
				
				switch(viewType) {
					
					case 'week':
					
						return {
							empty: '',
							Sun: currentDay.clone(),
							Mon: currentDay.clone().add(1, 'day'),
							Tue: currentDay.clone().add(2, 'day'),
							Wed: currentDay.clone().add(3, 'day'),
							Thu: currentDay.clone().add(4, 'day'),
							Fri: currentDay.clone().add(5, 'day'),
							Sat: currentDay.clone().add(6, 'day')
						};
						
						break;
						
					case 'threeDay':
		
						date.subtract(3, 'day');
						
						return {
							empty: '',
							prevDay: date.clone().subtract(1, 'day'),
							today: date.clone(),
							nextDay: date.clone().add(1, 'day')
						}
						
						break;
						
					case 'day':
					
						date.subtract(1, 'day');
						
						return {
							empty: '',
							singleDay: date.clone()
						};
						
						return;
					
				}
				
			}
			
			switch(viewType) {
				
				case 'week':
				
					tableCSS = 'week-table-bottom';
					
					break;
					
				case 'threeDay':
				
					tableCSS = 'threeDay-table-bottom';
					
					break;
					
				case 'day':
					
					tableCSS = 'day-table-bottom';
					
					break;
				
			}

			dom.makeNode('table_wrap', 'div', {css: 'scroll-wrapper ui red basic segment'});
		
			dom.table_wrap.makeNode('tableBottom', 'table', {
				columns: weekDays, 
				css: tableCSS
			});
			
			for(var i = 0; i <= 47; i++) {
				
				dom.table_wrap.tableBottom.makeRow(i, [], {
					cellStyles: [
						'position: relative !important;',
						'position: relative !important;',
						'position: relative !important;',
						'position: relative !important;',
						'position: relative !important;',
						'position: relative !important;',
						'position: relative !important;',
						'position: relative !important;'
					]
				});
				
				_.each(weekDays, function(val, day) {
					
					if(day !== 'empty') {
						
						if( (day === 'Sun' || day === 'Sat') || (val.format('ddd') === 'Sun' || val.format('ddd') === 'Sat') ) {
												
							dom.table_wrap.tableBottom.body[i.toString()][day].listeners = [
								function(selector) {
									$(selector).addClass('greyCell');
								}
							];
							
						}
						
						if( val.format('ddd M/D') === moment().format('ddd M/D') ) {
						
							dom.table_wrap.tableBottom.body[i.toString()][day].listeners = [
								function(selector) {
									$(selector).addClass('today');
								}	
							];
		
						}	
						
					}
					
				}, this);
				
				if(i % 2 === 0) {
							
					dom.table_wrap.tableBottom.body[i.toString()]['empty'].makeNode('time', 'div', {text: currentHour.format('h a'), style: 'z-index: 30;'});
					
					currentHour.add(1, 'hour');
					
				}
				
			}
			
			build_event_wtd(dom.table_wrap.tableBottom.body['0'], events, weekDays);
			
		}
		
		allDay_events = _.filter(events, function(event) {
			
			return !event.hasOwnProperty('endTime') || ( event.endTime.diff(event.startTime, 'days') > 0);
					
		});

		regular_events = _.filter(events, function(event) {
			
			return event.hasOwnProperty('endTime') && ( event.endTime.diff(event.startTime, 'days') === 0);
			
		});
		
		regular_events = _.groupBy(regular_events, function(event) {
			//return event.startTime;
			
			var remainder = (event.startTime.minute() % 10);

			return moment(event.startTime).subtract(remainder, "minutes");
			
		});

		dom.empty();
		
		build_rangeSwitch(dom, date, viewType);
		
		dom.makeNode('wrapper', 'div', {});
		
		// TOP TABLE

		if(scheduling === false) {
			
			if(views.week.header.singleDayEvents === true) {
				
				build_topTable(dom.wrapper, date, events);
				
			} else {
				
				build_topTable(dom.wrapper, date, allDay_events);
				
			}
				
		} else {
			
			build_topTable(dom.wrapper, date, regular_events);
			
		}
		
		// BOTTOM TABLE

		if(views[viewType].body !== false) {
			
			build_bottomTable(dom.wrapper, date, regular_events);	
			
		}
		
		dom.patch();
		
	}
	
	function build_list(dom, date, events) {
		
		var groupedEvents = _.groupBy(events, function(event) {
			return event.startTime.clone().startOf('day').unix();
		});
		var eventTime = '';

		dom.empty();
		
		dom.makeNode('wrapper', 'div', {});
		
		if(_.isEmpty(events)) {
			
			dom.wrapper.makeNode('no_events', 'div', {tag: 'h2', text: 'No events', css: 'text-center text-muted'});
			
		} else {
			
			dom.wrapper.makeNode('listViewTable', 'table', {
				css: 'listViewTable',
				columns: {
					empty: ''
				}
			});
			
			_.each(groupedEvents, function(eventArr, timestamp) {
				
				if(moment().startOf('day').unix() === Number(timestamp)) {
					ifTodayHighlight = 'today';
				} else {
					ifTodayHighlight = '';
				}
				
				dom.wrapper.listViewTable.makeRow('day-' + timestamp, [], {css: ifTodayHighlight});
				
				dom.wrapper.listViewTable.body['day-' + timestamp]['empty'].makeNode('eventDate', 'div', {tag: 'p', text:'<span>'+ eventArr[0].startTime.format('dddd') +'</span> <span>'+ eventArr[0].startTime.format('MMMM D, YYYY') +'</span>'});
				
				_.each(eventArr, function(eventObj, index) {
					
					dom.wrapper.listViewTable.makeRow('event-' + timestamp + '-' + index, [], {});
					
					dom.wrapper.listViewTable.body['event-' + timestamp + '-' + index]['empty'].makeNode('raised', 'div', {css: 'ui basic segment pointer hover-underline', style: 'border:none;'}).notify('click', {
						type: 'calendar-run',
						data: {
							run: function(data) {

								build_modal(eventObj, data.date);
								
							},
							date: date.clone()
						}
					}, sb.moduleId);
					
					dom.wrapper.listViewTable.body['event-' + timestamp + '-' + index]['empty'].raised.makeNode('grid', 'div', {css: 'ui equal width grid'});
					
					dom.wrapper.listViewTable.body['event-' + timestamp + '-' + index]['empty'].raised.grid.makeNode('col1', 'div', {css: 'column'});
					dom.wrapper.listViewTable.body['event-' + timestamp + '-' + index]['empty'].raised.grid.makeNode('col2', 'div', {css: 'column'});
					
					dom.wrapper.listViewTable.body['event-' + timestamp + '-' + index]['empty'].raised.grid.col1.makeNode('name', 'div', {text: '<i class="small '+ eventObj.color +' circle icon"></i> ' + eventObj.name, css: 'text-bold'});
					
					if(!eventObj.hasOwnProperty('endTime')) {
						
						eventTime = 'All day';
						
					} else {
						
						if(moment(eventObj.startTime).isSame(eventObj.endTime)) {
							eventTime = eventObj.startTime.format('h:mm a') + ' - ' + eventObj.endTime.format('h:mm a');	
						} else {
							eventTime = eventObj.startTime.format('ddd, MMM Do h:mm a') + ' - ' + eventObj.endTime.format('ddd, MMM Do h:mm a');
						}
						
					}
					
					dom.wrapper.listViewTable.body['event-' + timestamp + '-' + index]['empty'].raised.grid.col2.makeNode('time', 'div', {text: eventTime});
					
				}, this);
				
			}, this);
			
		}
		
		dom.patch();
		
	}
	
	// ********* UTILITY FUNCTIONS *********
	
	function build_modal(event, date) {

		if(event.modal) {
			
			function build_defaultModal(event, btns) {
				
				var dom = UI_CACHE.modal;
				var eventTime = '';
				
				if(event.hasOwnProperty('endTime')) {
					
					if(moment(event.startTime).isSame(event.endTime)) {
						eventTime = event.startTime.format('h:mm a') + ' - ' + event.endTime.format('h:mm a');
					} else {
						eventTime = event.startTime.format('ddd, MMM Do h:mm a') + ' - ' + event.endTime.format('ddd, MMM Do h:mm a');
					}
					
				} else {
					eventTime = 'All day';
				}
				
				dom.empty();
				
				dom.makeNode('wrapper', 'div', {});
				
				dom.wrapper.makeNode('head', 'div', {});
				dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
				dom.wrapper.makeNode('body', 'div', {});
				
				dom.wrapper.head.makeNode('title', 'div', {text: '<h3>'+ event.name +'<div class="ui sub header">'+ eventTime +'</div></h3>', css: 'ui header'});
				dom.wrapper.head.makeNode('type', 'div', {text: event.type, css: 'ui '+ event.color +' label'});
				
				dom.wrapper.body.makeNode('desc', 'div', {text: event.description});
				
				if(btns) {
					
					dom.wrapper.body.makeNode('lb_2', 'lineBreak', {spaces: 1});
					
					dom.wrapper.body.makeNode('btnGroup', 'div', {css: 'fluid ui tiny compact buttons'});
					
					_.each(btns, function(v, i) {
					
						dom.wrapper.body.btnGroup.makeNode('btn-' + i, 'div', {text: v.text, css: 'ui '+ v.css +' button'}).notify('click', {
							type: v.type,
							data: v.data
						}, sb.moduleId);
						
					}, this);
					
				}
				
				dom.patch();
				dom.show();
				
			}
			
			if(_.isFunction(event.modal)) {
				
				event.modal(UI_CACHE.modal, event, date);
				
			} else if(_.isBoolean(event.modal) && event.modal === true) {
				
				build_defaultModal(event, undefined, date);
				
			} else if(_.isObject(event.modal)) {
				
				if(event.modal.btns) {
					
					build_defaultModal(event, event.modal.btns, date);
					
				}
				
			}
			
		}
		
	}
	
	function build_event_wtd(dom, events, weekDays) {

		var eventStartPosition = 0;
		var eventBoxHeight = 0;

		_.each(weekDays, function(dayDate, day) {
			
			_.each(events, function(list, time) {
			
				_.each(list, function(event, index) {
					
					if(day !== 'empty') {
						
						eventStartPosition = (Number(event.startTime.format('H')) * 60 + Number(event.startTime.format('m')));
						eventBoxHeight = (Number(event.endTime.format('H')) * 60 + Number(event.endTime.format('m'))) - eventStartPosition;
						
						if(dayDate.format('ddd M/D') === event.startTime.format('ddd M/D')) {

							dom[day].makeNode('box-'+event.id, 'div', {css: 'table-event-box ui tertiary '+ event.color +' inverted basic segment pointer hover-underline', style: 'top: '+ eventStartPosition +'px; height: ' + eventBoxHeight +'px; width: ' + (100/list.length) + '%; position: absolute !important; left: ' + (index * (100/list.length)) + '%; z-index: ' + eventStartPosition + ';'}).notify('click', {
								type: 'calendar-run',
								data: {
									run: function(data) {
										
										build_modal(event, data.date);
										
									},
									date: dayDate.clone()
								}
							}, sb.moduleId);                                                  
							
							dom[day]['box-'+event.id].makeNode('head', 'div', {css: 'ui inverted '+ event.color +' basic segment'});
							
							dom[day]['box-'+event.id].head.makeNode('name', 'div', {text: event.name});
							
							dom[day]['box-'+event.id].makeNode('time', 'div', {text: event.startTime.format('h:mm A') + ' - ' + event.endTime.format('h:mm A')});
							
						}
						
					}
					
				}, this);
				
			}, this);
			
		}, this);
		
	}
	
	function build_rangeSwitch(dom, date, viewType) {
		
		var rangeStart = '';
		var	rangeEnd = '';
		var	prev = '';
		var	next = '';
		
		function switch_range(date, viewType) {
			
			build_menu.display_month(date);
					
			switch(viewType) {
				
				case 'week':
				case 'threeDay':
				case 'day':
				
					UI_CACHE.body.loading();
				
					getEventList(DATA_CACHE.dataCall, date, viewType, function(eventList) {
					
						DATA_CACHE.events = eventList;
						
						build_wtd(dom, date, eventList);
						
						UI_CACHE.body.loading(false);
						
					});
				
					break;
				
			}
			
		}

		switch(viewType) {
			
			case 'week':
				
				rangeStart = date.clone().startOf('week').format('MMM Do');
				rangeEnd = ' - ' + date.clone().endOf('week').format('Do');
				prev = date.clone().subtract(1, 'week');
				next = date.clone().add(1, 'week');

				if(date.clone().startOf('week').format('MMM YYYY') !== date.clone().endOf('week').format('MMM YYYY')) {
					rangeEnd = ' - ' + date.clone().endOf('week').format('MMM Do');	
				}
				
				break;
				
			case 'threeDay':
			
				rangeStart = date.clone().subtract(1, 'day').format('MMM Do');
				rangeEnd = ' - ' + date.clone().add(1, 'day').format('Do');
				prev = date.clone().subtract(3, 'day');
				next = date.clone().add(3, 'day');
				
				if(date.clone().subtract(1, 'day').format('MMM YYYY') !== date.clone().add(1, 'day').format('MMM YYYY')) {
			
					rangeEnd = ' - ' + date.clone().add(1, 'day').format('MMM Do');
					
				}
				
				break;
				
			case 'day':
			
				rangeStart = date.clone().format('dddd, MMM Do');
				rangeEnd = '';
				prev = date.clone().subtract(1, 'day');
				next = date.clone().add(1, 'day');
				
				break;
			
		}
		
		dom.makeNode('grid', 'div', {css: 'ui equal width grid'});
		
		dom.grid.makeNode('col1', 'div', {css: 'column'});
		dom.grid.makeNode('col2', 'div', {css: 'column'});
		dom.grid.makeNode('col3', 'div', {css: 'column'});
		
		// COL 2
		dom.grid.col2.makeNode('grid', 'div', {css: 'ui grid'});
		
		dom.grid.col2.grid.makeNode('col1', 'div', {css: 'four wide column'});
		dom.grid.col2.grid.makeNode('col2', 'div', {css: 'eight wide middle aligned column text-center'});
		dom.grid.col2.grid.makeNode('col3', 'div', {css: 'four wide column'});
		dom.makeNode('br', 'div', {text:'<br />'});
		
		dom.grid.col2.grid.col1.makeNode('left', 'div', {text: '<i class="angle double left icon"></i>', css: 'ui right floated circular basic mini icon button'}).notify('click', {
			type: 'calendar-run',
			data: {
				run: function(data) {
					
					viewDate = prev;
					
					switch_range(viewDate, viewType);
					
					activeView(viewType, viewDate, DATA_CACHE.events);
					
				}
			}
		}, sb.moduleId);
		
		dom.grid.col2.grid.col2.makeNode('range', 'div', {text: '<h4>'+ rangeStart + rangeEnd +'</h4>'});
		
		dom.grid.col2.grid.col3.makeNode('right', 'div', {text: '<i class="angle double right icon"></i>', css: 'ui left floated circular basic mini icon button'}).notify('click', {
			type: 'calendar-run',
			data: {
				run: function(data) {
					
					viewDate = next;
					
					switch_range(viewDate, viewType);
					
					activeView(viewType, viewDate, DATA_CACHE.events);
					
				}
			}
		}, sb.moduleId);
		
	}
	
	function firstHour(date) {
		return date.startOf('day');
	}
	
	function firstDayOfWeekView(date) {
		return date.startOf('week');
	}
	
	function check_setup(setup) {

		// DOM-OBJ
		if(setup.domObj) {
			//UI_CACHE.domObj = sb.dom.make(setup.domObj.selector);
			
			setup.domObj.properties.compInstanceId = sb.instanceId;
			
			UI_CACHE.domObj = setup.domObj;
			
		} else {
			throw 'You need a domObj.';
		}
		
		// EVENTS
		if(setup.events) {
			DATA_CACHE.dataCall = setup.events;
		} else {
			throw 'Calendar has no events to display.';
		}
		
		// VIEWS
		if(setup.views) {
				
			_.each(views, function(viewObj, viewName) {
					
				_.each(setup.views, function(view, name) {
					
					if(setup.views.hasOwnProperty(viewName)) {
						
						_.each(view, function(val, prop) {
							
							if(viewName === name) {
								
								views[viewName][prop] = setup.views[viewName][prop];
								
							}
							
						}, this);
						
					} 
					
					if(!views.hasOwnProperty(name)) {
						
						views[name] = view;
						
					}
					
				}, this);
				
			}, this);
			
		}
		
		// VIEWDATE
		if(setup.viewDate) {
			viewDate = setup.viewDate;
		} else {
			viewDate = moment();
		}
		
		// VIEWTYPE
		if(setup.viewType) {
			viewType = setup.viewType;
		} else {
			viewType = Object.keys(views)[0];
		}
		
		// SETTINGS
		if(setup.settings) {
			
			_.each(setup.settings, function(val, name) {
				
				if(settings.hasOwnProperty(name)) {

					settings[name] = val;
					
				}
				
			}, this);
			
		} 
		
		// CURRENT VIEW
		if(setup.activeView) {
			activeView = setup.activeView;
		}
		
		// SCHEDULING
		if(setup.scheduling) {
			scheduling = setup.scheduling;
		}
		
		// TOP BAR SELECT MENU
		if(setup.topbarSelect) {
			topbarSelect = setup.topbarSelect;
		}  

	}
	
	function display_loader(setup) {
				
		// SETUP { 
			// mainDom: dom to display loader,
			// empty: boolean, always set to true unless it is turned off (optional),
			// patch: boolean, always set to true unless it is turned off (optional),
			// text: text that will be displayed with loader
		//}
		
		if(setup.empty !== false) {
			setup.mainDom.empty();	
		}
		
		setup.mainDom.makeNode('cont', 'div', {});
		
		setup.mainDom.cont.makeNode('loader', 'loader', {});
		setup.mainDom.cont.makeNode('load_text', 'div', {text: setup.text, css: 'text-center'});
		
		if(setup.patch !== false) {
			setup.mainDom.patch();	
		}
		
	}
	
	function select_view(views, date, events) {

		_.each(views, function(viewObj, type) {

			if(type === viewType) {
				
				if(viewObj.action && viewObj.show === true) {

					viewObj.action(UI_CACHE.body, date.clone(), events);
						
				}
				
			}
			
		}, this);
		
	}
	
	function getEventList(data, viewDateStart, viewTypeStart, callback) {
		
		switch(typeof data) {
			
			case 'function':
			
				var range = {
						type:'between'
					};
					
				switch(viewTypeStart) {
					
					case 'day':
					
						range.start = moment(viewDateStart).startOf('day').unix();
						range.end = moment(viewDateStart).endOf('day').unix();
						
						break;
						
					case 'month':
					
						range.start = moment(viewDateStart).startOf('month').unix();
						range.end = moment(viewDateStart).endOf('month').unix();
						
						break;
						
					case 'week':
					
						range.start = moment(viewDateStart).startOf('week').unix();
						range.end = moment(viewDateStart).endOf('week').unix();
						
						break;
						
					case 'list':
					
						range.start = moment(viewDateStart).startOf('month').unix();
						range.end = moment(viewDateStart).endOf('month').unix();
						
						break;
						
					case 'threeDays':

						range.start = moment(viewDateStart).subtract(1, 'day').startOf('day').unix();
						range.end = moment(viewDateStart).add(1, 'day').endOf('day').unix();
						
						break;
						
					default:
					
						range.start = moment(viewDateStart).startOf('month').unix();
						range.end = moment(viewDateStart).endOf('month').unix();				
					
				}
			
				data(function(eventList) {
										
					callback(eventList);
					
				}, range);
			
				break;
				
			default:
			
				callback(data);
			
		}
		
	}
	
	return {
		
		init: function() {
			
			sb.listen({
				'show-calendar': this.show,
				'update-calendar': this.update
			});
			
		},
		
		destroy: function() {
			
			var UI_CACHE = {};
			var DATA_CACHE = {};
			
		},
		
		run: function(data) {

    		if(data.instanceId === sb.instanceId) {
                data.run(data);	        		
    		}

		},
		
		show: function(setup) {
    		
    		if(setup.instanceId === sb.instanceId) {
        		
        	    sb.listen({
    				'calendar-run': this.run
    			});
    			
    			check_setup(setup);
    			
    			if(settings.fullscreen === true) {
    				
    				sb.notify({
    					type: 'app-collapse-main-nav',
    					data: {
    						navState: 'hide'
    					}	
    				});
    				
    			}
                
    			UI_CACHE.domObj.makeNode('wrapper', 'div', {css: 'animated fadeIn calendar-area'});
    			
    			UI_CACHE.modal = UI_CACHE.domObj.wrapper.makeNode('modal', 'modal', {size: 'mini'});
    			UI_CACHE.head = UI_CACHE.domObj.wrapper.makeNode('head', 'div', {});

    			UI_CACHE.body = UI_CACHE.domObj.wrapper.makeNode('body', 'div', {});
    
    			if(settings.monthBar) {
    				build_menu(UI_CACHE.head, viewDate, viewType);	
    			}
    			
    			UI_CACHE.body.loading();
    			
    			getEventList(DATA_CACHE.dataCall, viewDate, viewType, function(eventList) {
    
    				DATA_CACHE.events = eventList;
    				
    				select_view(views, viewDate, eventList);
    				
    				UI_CACHE.body.loading(false);
    
    				activeView(viewType, viewDate, eventList);
    				
    			});			
    			
    			UI_CACHE.domObj.patch();	
        		
    		}
			
		},
		
		update: function(data) {
    		
    		if(data.instanceId === sb.instanceId) {
        		
        	    if(data.hasOwnProperty('events')) {
    				DATA_CACHE.dataCall = data.events;
    			}
    			
    			UI_CACHE.body.loading();
    			
    			getEventList(DATA_CACHE.dataCall, viewDate, viewType, function(eventList) {
    						
    				DATA_CACHE.events = eventList;
    				
    				select_view(views, viewDate, eventList);
    				
    				UI_CACHE.body.loading(false);
    				
    				activeView(viewType, viewDate, eventList);
    				
    			});	
        		
    		}
			
		}
		
	}
	
});