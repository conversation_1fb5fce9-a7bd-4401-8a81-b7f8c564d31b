Factory.register("workOrdersPortal", function (sb) {
  var dom = {},
    comps = {},
    adminURL =
      "https://api.voltz.software/_repos/_production/notify/pagoda/_coredev/",
    instance = "",
    fullName = "",
    email = "",
    workorder = 0,
    invoices = [];

  function acceptProposal(obj, historyItem, callback) {
    var updates = [
      {
        id: obj.id,
        status: "Accepted",
        object_bp_type: "work_orders",
        price: _.reduce(
          historyItem.pricing,
          function (memo, price, catId) {
            return memo + +price;
          },
          0
        ),
        balance: _.reduce(
          historyItem.pricing,
          function (memo, price, catId) {
            return memo + +price;
          },
          0
        ),
        amount_paid: 0,
      },
      {
        id: historyItem.id,
        object_bp_type: "proposals",
        status: "Accepted",
      },
      {
        id: +historyItem.contract.id,
        active: "Yes",
        object_bp_type: "contracts",
      },
      {
        id: +historyItem.menu.id,
        active: "Yes",
        object_bp_type: "inventory_menu",
      },
    ];

    if (+historyItem.schedule > 1) {
      updates.push({
        id: +historyItem.schedule.id,
        active: "Yes",
        object_bp_type: "staff_schedules",
      });
    }

    sb.data.db.controller(
      "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
      {
        queryObj: {
          related: obj.id,
          active: "Yes",
        },
        instance: instance,
        objectType: "inventory_menu",
        getChildObjs: {
          id: true,
        },
      },
      function (activeMenus) {
        var activeMenu = activeMenus[0];

        _.map(activeMenus, function (activeMenu) {
          updates.push({
            id: activeMenu.id,
            active: "No",
            object_bp_type: "inventory_menu",
          });
        });

        sb.data.db.controller(
          "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
          {
            queryObj: {
              related_object: obj.id,
              active: "Yes",
            },
            instance: instance,
            objectType: "invoices",
            getChildObjs: {
              id: true,
            },
          },
          function (activeInvoices) {
            if (activeInvoices.length > 0) {
              updates = updates.concat(
                _.map(
                  activeInvoices,
                  function (inv) {
                    return {
                      id: inv.id,
                      active: "No",
                      object_bp_type: "invoices",
                    };
                  },
                  []
                )
              );
            }

            updates = updates.concat(
              _.map(
                historyItem.invoices,
                function (inv) {
                  return {
                    id: +inv,
                    active: "Yes",
                    object_bp_type: "invoices",
                  };
                },
                []
              )
            );

            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  event: obj.id,
                  active: "Yes",
                },
                instance: instance,
                objectType: "staff_schedules",
                getChildObjs: {
                  id: true,
                },
              },
              function (activeSchedules) {
                if (activeSchedules.length > 0) {
                  var activeSchedule = activeSchedules[0];

                  updates.push({
                    id: activeSchedule.id,
                    active: "No",
                    object_bp_type: "staff_schedules",
                  });
                }

                sb.data.db.controller(
                  "updateObject&pagodaAPIKey=" + instance,
                  {
                    objectType: "objects",
                    objectData: updates,
                  },
                  function (updated) {
                    callback(true);
                  }
                );
              }
            );
          }
        );
      }
    );
  }

  function buildInvoice(obj, edit, draw, domCache) {
    var dom = this,
      taxEditString = "",
      lineItemEditString = "",
      taxEditString = "";

    if (!dom.title) {
      dom.makeNode("loader", "loader", {});
      dom.patch();
    }

    var lineItems = "",
      subtotal = 0,
      taxRate = 0,
      tax = 0,
      total = 0,
      amountPaid = 0,
      totalDue = 0;

    if (obj.tax_rate) {
      taxRate = +obj.tax_rate;
    }

    _.each(obj.payments, function (p) {
      amountPaid += p.amount;
    });

    var clientAddress = "<i>No address given</i>",
      clientName = "<i>No client selected</i>";
    if (obj.main_client) {
      clientName = obj.main_contact.fname + " " + obj.main_contact.lname;

      if (obj.main_contact.company) {
        clientName =
          obj.main_contact.company.name +
          "<br />" +
          obj.main_contact.fname +
          " " +
          obj.main_contact.lname;
      }

      if (obj.main_contact.contact_info) {
        _.each(obj.main_contact.contact_info, function (info) {
          if (info.type.data_type == "address" && info.is_primary == "yes") {
            clientAddress =
              info.street +
              "<br />" +
              info.city +
              ", " +
              info.state +
              " " +
              info.zip;
          }
        });
      }
    }

    _.each(obj.items, function (item, k) {
      var itemSubtotal = item.amount * item.quantity,
        itemTotal = 0,
        itemTaxRate = 0,
        itemTax = 0;

      if (item.tax_rate) {
        itemTaxRate = item.tax_rate;
        itemTax = Math.round(item.amount * (itemTaxRate / 100));
      }

      itemTotal = itemSubtotal + itemTax;

      if (edit) {
        lineItemEditString =
          '<a data-id="' +
          k +
          '" class="remove-line-item"><i class="fa fa-trash-o" style="color:red; margin:0 5px 0px 0px;"></i></a> <a data-id="' +
          k +
          '" class="edit-line-item-' +
          obj.id +
          '"><i class="fa fa-pencil" style="color:orange; margin:0 10px;"></i></a> ';
        taxEditString =
          ' <a data-id="' +
          k +
          '" class="change-tax-rate-' +
          obj.id +
          '"><i class="fa fa-pencil" style="color:orange;"></i></a>';
      }

      lineItems +=
        '<tr class="bottom-border">  ' +
        '<td width="" class="edit-line-item-' +
        k +
        '">' +
        lineItemEditString +
        "" +
        item.name +
        "</td>  " +
        '<td width="" style="text-align: right;">$' +
        (item.amount / 100).formatMoney(2) +
        "</td>  " +
        '<td width="" style="text-align: right;">' +
        item.quantity +
        "</td>  " +
        '<td width="" style="text-align: right;" class="change-tax-rate-' +
        k +
        '" >$' +
        (itemTax / 100).formatMoney(2) +
        "" +
        taxEditString +
        "</td>  " +
        '<td width="" style="text-align: right;" class="lineTotal-' +
        k +
        '">$' +
        (itemTotal / 100).formatMoney(2) +
        "</td>  " +
        "</tr>";

      subtotal += itemTotal;
    });

    tax = subtotal * (+taxRate / 100);

    total = subtotal + tax;

    total = Math.round(total);

    totalDue = total - amountPaid;

    if (edit) {
      taxEditString =
        ' - <a style="color:blue;" class="tax-rate"><small>change</small></a>';
    }

    sb.data.db.obj.getAll("tax_rates", function (rates) {
      sb.data.db.obj.getAll(
        "invoice_system",
        function (settingsList) {
          getInstanceImage(appConfig, function (image) {
            var settings = settingsList[0],
              billingAddress = "<i>No billing address</i>";

            if (settings.billing_address) {
              billingAddress =
                settings.billing_address.street +
                "<br />" +
                settings.billing_address.city +
                ", " +
                settings.billing_address.state +
                " " +
                settings.billing_address.zip;
            }

            var paymentStubHTML = "<br /><br /><br /><br /><br /><br />";
            if (totalDue > 0) {
              paymentStubHTML =
                '   	<div style="border-bottom: 2px dotted #7e7e7e;"></div>  ' +
                "   	  " +
                "   	<br /><br />  " +
                "   	  " +
                '   	<table width="100%">  ' +
                "   		  " +
                "   		<tr>  " +
                "   			  " +
                "   			<td>  " +
                "   				  " +
                "   				<h3>PAYMENT STUB</h3>  " +
                "   				  " +
                "   				<br />  " +
                "   				  " +
                "   				<p>" +
                appConfig.systemName +
                "<br />" +
                billingAddress +
                "</p>  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   			<td>  " +
                "   				  " +
                '   				<div style="font-weight:bold;">  ' +
                '   					<p style="padding:10px;">To pay this invoice online, go to: <a href="https://bento.infinityhospitality.net/app/workorders/#?&i=' +
                appConfig.instance +
                "&wid=" +
                obj.related_object +
                '">LINK</a></p>' +
                "   				</div>  " +
                "   				  " +
                '   				<table width="100%">  ' +
                "   					  " +
                "   					<tr>  " +
                "     " +
                "   						<td>Client</td>  " +
                '   						<td style="text-align: right;">' +
                clientName +
                "</td>  " +
                "     " +
                "   					</tr>  " +
                "     " +
                '   					<tr class="bottom-border">  ' +
                "     " +
                "   						<td>Invoice #</td>  " +
                '   						<td style="text-align: right;">' +
                obj.object_uid +
                "</td>  " +
                "     " +
                "   					</tr>  " +
                "     " +
                '   					<tr class="bottom-border">  ' +
                "     " +
                "   						<td>Invoice Date</td>  " +
                '   						<td style="text-align: right;">' +
                moment(obj.due_date).format("M/D/YYYY") +
                "</td>  " +
                "     " +
                "   					</tr>  " +
                "   					  " +
                '   					<tr class="bottom-border">  ' +
                "     " +
                "   						<td>Balance Due</td>  " +
                '   						<td class="totalDue" style="text-align: right;">$' +
                (totalDue / 100).formatMoney(2) +
                "</td>  " +
                "     " +
                "   					</tr>  " +
                "   					  " +
                '   					<tr class="bottom-border">  ' +
                "     " +
                "   						<td>Amount Enclosed</td>  " +
                "   						<td></td>  " +
                "     " +
                "   					</tr>  " +
                "   					  " +
                "   				</table>  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                "   	</table>  " +
                "   	  " +
                "   	<br /><br />  ";
            } else {
              var paymentLineItemsHTML = "";

              _.each(obj.payments, function (payment) {
                paymentLineItemsHTML +=
                  '   					<tr style="border-top:1px dotted gray;">  ' +
                  "     " +
                  "   						<td>" +
                  moment(payment.date_created).format("M/D/YYYY h:mm a") +
                  "</td>  " +
                  '   						<td style="">$' +
                  (payment.amount / 100).formatMoney(2) +
                  "</td>  " +
                  "     " +
                  "   					</tr>  ";
              });

              paymentLineItemsHTML +=
                '   					<tr style="border-top:1px dotted gray;">  ' +
                "     " +
                '   						<td style="font-weight:bold;">TOTAL PAID</td>  ' +
                '   						<td style="font-weight:bold;">$' +
                (amountPaid / 100).formatMoney(2) +
                "</td>  " +
                "     " +
                "   					</tr>  ";

              paymentStubHTML =
                '<div style="border-bottom: 2px dotted #7e7e7e;"></div>  ' +
                '   	<table width="100%">  ' +
                "   		  " +
                "   		<tr>  " +
                "   			  " +
                "   			<td>  " +
                "   				  " +
                "   				<h3>PAYMENT HISTORY</h3>  " +
                "   				  " +
                "   				<br />  " +
                "   				  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   			<td>  " +
                '   				<table width="100%">  ' +
                "   					<tr>  " +
                "     " +
                '   						<td style="font-weight:bold;">Payment Date</td>  ' +
                '   						<td style="font-weight:bold;">Amount</td>  ' +
                "     " +
                "   					</tr>  " +
                "   					  " +
                paymentLineItemsHTML +
                "   					  " +
                "   				</table>  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                "   	</table>  " +
                "   	  " +
                "   	<br /><br />  ";
            }

            dom.empty();

            dom.makeNode("text", "text", {
              text:
                "" +
                '   <div id="invoice-container" style="width: 100%;">  ' +
                '		<table width="100%">' +
                "			<tr>" +
                '				<td width="50%">' +
                "   				<p>" +
                appConfig.systemName +
                "<br />" +
                billingAddress +
                "</p>  " +
                "				</td>" +
                '				<td width="50%" style="text-align:right;">' +
                ' 				  	<h2 style="text-align:right;">' +
                image +
                "</h2>  " +
                "				</td>" +
                "			</tr>" +
                "		</table>" +
                "   	  " +
                "   	<br /><br />" +
                "   	  " +
                '   	<table width="100%">  ' +
                "   		  " +
                "   		<tr>  " +
                "   			  " +
                '   			<td width="50%">  ' +
                "   				" +
                clientName +
                "<br />" +
                clientAddress +
                "   			</td>  " +
                "   			  " +
                '   			<td width="50%">  ' +
                "   				  " +
                '   				<table width="100%">  ' +
                "   					  " +
                "   					<tr>  " +
                "   						  " +
                '   						<td width="50%">Invoice #</td>  ' +
                '   						<td style="text-align: right;">' +
                obj.object_uid +
                "</td>  " +
                "   						  " +
                "   					</tr>  " +
                "     " +
                "   					<tr>  " +
                "   						  " +
                '   						<td width="50%">Invoice Date</td>  ' +
                '   						<td style="text-align: right;">' +
                moment(obj.due_date).format("M/D/YYYY") +
                "</td>  " +
                "   						  " +
                "   					</tr>  " +
                "     " +
                '   					<tr class="bold-table-row">  ' +
                "   						  " +
                '   						<td width="50%">Balance Due (USD)</td>  ' +
                '   						<td style="text-align: right;" class="totalDue">$' +
                (totalDue / 100).formatMoney(2) +
                "</td>  " +
                "   						  " +
                "   					</tr>  " +
                "   					  " +
                "   				</table>  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                "   	</table>  " +
                "   	  " +
                "   	<br /><br /><br /><br />" +
                "   	  " +
                '   	<table width="100%">  ' +
                "   					  " +
                '   		<tr class="bold-table-row">  ' +
                "   			  " +
                '   			<td width="35%">Line Item</td>  ' +
                '   			<td width="17.5%" style="text-align: right;">Unit Price</td>  ' +
                '   			<td width="15%" style="text-align: right;">Qty</td>  ' +
                '   			<td width="15%" style="text-align: right;">Tax</td>  ' +
                '   			<td width="17.5%" style="text-align: right;">Line Total</td>  ' +
                "   			  " +
                "   		</tr>  " +
                "     " +
                lineItems +
                "   		  " +
                '   		<tr style="font-weight: bold;">  ' +
                "   			  " +
                '   			<td width="50%"></td>  ' +
                '   			<td width="" style="text-align: right;">Subtotal</td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right;" class="subtotal">$' +
                (subtotal / 100).formatMoney(2) +
                "</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                '   		<tr style="font-weight: bold;">  ' +
                "   			  " +
                '   			<td width=""></td>  ' +
                '   			<td width="" style="text-align: right;">Amount Paid</td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right;" class="totalPaid">$' +
                (amountPaid / 100).formatMoney(2) +
                "</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                '   		<tr class="bold-table-row" style="font-weight:bold;">  ' +
                "   			  " +
                '   			<td width="" style="background-color: white; font-weight:bold;"></td>  ' +
                '   			<td width="" style="text-align: right;font-weight:bold;">Balance Due</td>  ' +
                '   			<td width="" style="text-align: right; font-weight:bold;"></td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right; font-weight:bold;" class="totalDue">$' +
                (totalDue / 100).formatMoney(2) +
                "</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                "   	</table>  " +
                "   	  " +
                "   	<br /><br />  " +
                "   	  " +
                '   	<p style="font-weight: bold;">Memo</p>  ' +
                "   	  " +
                "   	<p>" +
                obj.memo +
                "</p>  " +
                "   	  " +
                "   	<br /><br /><br /><br />" +
                "   	  " +
                paymentStubHTML +
                "   	  " +
                "  </div>  " +
                "",
            });

            if (draw) {
              draw({
                dom: dom,
                after: function (dom) {
                  dom.css("pda-container pda-Panel pda-panel-gray");

                  /*
sb.notify({
									type: 'show-note-list-box',
									data: {
										domObj:dom.col2.cont.notes,
										objectIds:[obj.id],
										objectId:obj.id,
										collapse:true
									}
								});
*/
                },
              });
            } else {
              dom.patch();
            }

            if (edit) {
              $(".change-tax-rate-" + obj.id).click(function () {
                var itemId = $(this).data("id"),
                  invoiceTotal = 0;

                $(".change-tax-rate-" + itemId).webuiPopover({
                  placement: "left", //values: auto,top,right,bottom,left,top-right,top-left,bottom-right,bottom-left,auto-top,auto-right,auto-bottom,auto-left,horizontal,vertical
                  trigger: "click", //values:  click,hover,manual(handle events by your self),sticky(always show after popover is created);
                  animation: "pop", //pop with animation,values: pop,fade (only take effect in the browser which support css3 transition)
                  width: 200,
                  title: "Tax Rates", //the popover title, if title is set to empty string,title bar will auto hide
                  content: function () {
                    var optionText = '<option value="0">No Tax</option>';

                    _.each(rates, function (r) {
                      if (r.rate == obj.items[itemId].tax_rate) {
                        optionText +=
                          '<option selected="selected" value="' +
                          r.id +
                          '">' +
                          r.name +
                          "</option>";
                      } else {
                        optionText +=
                          '<option value="' +
                          r.id +
                          '">' +
                          r.name +
                          "</option>";
                      }
                    });

                    var html =
                      '<form class="form"><select id="" class="tax-rate-form pda-form pda-form-select pda-form-fullWidth">' +
                      optionText +
                      "</select></form>";

                    return html;
                  }, //content of the popover,content can be function
                  closeable: true, //display close button or not
                  type: "html", //content type, values:'html','iframe','async'
                  dismissible: true, // if popover can be dismissed by  outside click or escape key
                  onShow: function () {
                    $(".tax-rate-form").on("change", function () {
                      if (+$(this).val() > 0) {
                        obj.items[itemId].tax_rate = _.where(rates, {
                          id: +$(this).val(),
                        })[0].rate;
                      } else {
                        obj.items[itemId].tax_rate = "0.00";
                      }

                      var newAmount = 0,
                        totalPaid = 0;

                      _.each(obj.items, function (i) {
                        if (i.tax_rate != "0.00") {
                          newAmount += i.amount + i.amount * (i.tax_rate / 100);
                        } else {
                          newAmount += i.amount;
                        }
                      });

                      _.each(obj.payments, function (p) {
                        totalPaid += p.amount;
                      });

                      obj.amount = newAmount;
                      obj.paid = totalPaid;
                      obj.balance = newAmount - totalPaid;

                      sb.data.db.obj.update(
                        "invoices",
                        obj,
                        function (updated) {
                          var noteObj = {
                            type_id: updated.id,
                            type: "invoices",
                            note:
                              "Tax rate changed for item " +
                              obj.items[itemId].name +
                              ".",
                            note_type: 0,
                            author: sb.data.cookie.get("uid"),
                            notifyUsers: [],
                          };

                          sb.data.db.obj.create(
                            "notes",
                            noteObj,
                            function (newNote) {
                              WebuiPopovers.hideAll();

                              buildInvoice.call(dom, updated, true);
                            }
                          );
                        },
                        2
                      );
                    });
                  },
                });
              });

              $(".edit-line-item-" + obj.id).click(function (e) {
                var itemId = $(this).data("id");

                $(".edit-line-item-" + itemId).webuiPopover({
                  placement: "bottom", //values: auto,top,right,bottom,left,top-right,top-left,bottom-right,bottom-left,auto-top,auto-right,auto-bottom,auto-left,horizontal,vertical
                  trigger: "click", //values:  click,hover,manual(handle events by your self),sticky(always show after popover is created);
                  animation: "pop", //pop with animation,values: pop,fade (only take effect in the browser which support css3 transition)
                  width: 375,
                  title: "Enter a new name",
                  content: function () {
                    return "";
                  },
                  closeable: true, //display close button or not
                  type: "html", //content type, values:'html','iframe','async'
                  dismissible: true, // if popover can be dismissed by  outside click or escape key
                  onShow: function () {
                    var popDom = sb.dom.make(".webui-popover-content");

                    popDom.makeNode("form", "form", {
                      itemName: {
                        name: "itemName",
                        label: "Item Name",
                        type: "text",
                        value: obj.items[itemId].name,
                      },
                    });

                    popDom.makeNode("button", "button", {
                      text: "Save",
                      css: "pda-btn-green pda-btn-x-small",
                    });

                    popDom.button.notify(
                      "click",
                      {
                        type: "invoicesRun",
                        data: {
                          run: function (obj, itemId) {
                            var popDom = this,
                              newName =
                                this.form.process().fields.itemName.value;

                            popDom.button.loading();

                            obj.items[itemId].name = newName;

                            sb.data.db.obj.update(
                              "invoices",
                              { id: obj.id, items: obj.items },
                              function (updatedInvoice) {
                                WebuiPopovers.hideAll();

                                buildInvoice.call(dom, updatedInvoice, true);
                              },
                              2
                            );
                          }.bind(popDom, obj, itemId),
                        },
                      },
                      sb.moduleId
                    );

                    popDom.build();
                  },
                });
              });

              $(".remove-line-item").click(function () {
                var event = this;

                sb.dom.alerts.ask(
                  {
                    title: "",
                    text: "Are you sure you want to remove this line item?",
                  },
                  function (resp) {
                    if (resp) {
                      swal.disableButtons();

                      var itemId = $(event).data("id"),
                        invoiceTotal = 0,
                        itemToRemove = obj.items[itemId];

                      obj.items = _.reject(obj.items, function (item, k) {
                        return k == itemId;
                      });

                      _.each(obj.items, function (i) {
                        if (i.tax_rate) {
                          if (i.tax_rate > 0) {
                            invoiceTotal +=
                              i.amount * (i.tax_rate / 100) + i.amount;
                          } else {
                            invoiceTotal += i.amount;
                          }
                        } else {
                          invoiceTotal += i.amount;
                        }
                      });

                      if (invoiceTotal == 0) {
                        invoiceTotal = "0";
                      }

                      obj.amount = Math.round(+invoiceTotal);
                      obj.balance = obj.amount - obj.paid;

                      sb.data.db.obj.update(
                        "invoices",
                        obj,
                        function (updatedObj) {
                          var noteObj = {
                            type_id: updatedObj.id,
                            type: "invoices",
                            note:
                              itemToRemove.name +
                              " removed for $" +
                              (itemToRemove.amount / 100).formatMoney(2) +
                              ".",
                            note_type: 0,
                            author: sb.data.cookie.get("uid"),
                            notifyUsers: [],
                          };

                          sb.data.db.obj.create(
                            "notes",
                            noteObj,
                            function (newNote) {
                              swal.close();

                              buildInvoice.call(dom, updatedObj, true);
                            }
                          );
                        },
                        4
                      );
                    }
                  }
                );
              });
            }
          });
        },
        1
      );
    });
  }

  function createMergedHTML(obj, objectType, callback) {
    //console.log('obj',obj);
    var relatedObject = 0;
    var relatedObjectType = "";
    if (obj.related_object) {
      relatedObject = obj.related_object.id;
      relatedObjectType = obj.related_object.object_bp_type;
    } else {
      relatedObject = obj.main_object.id;
      relatedObjectType = obj.main_object.object_bp_type;
    }

    sb.data.db.controller(
      "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
      {
        queryObj: {
          id: relatedObject,
        },
        instance: instance,
        objectType: relatedObjectType,
        getChildObjs: 1,
      },
      function (mergeObj) {
        //console.log(objectType, mergeObj);
        sb.data.db.controller(
          "getObjectBlueprint&api_webform=true&pagodaAPIKey=" + instance,
          {
            objectType: relatedObjectType,
          },
          function (mergeBP) {
            getAllBluprints(mergeBP, function (allBlueprints) {
              mergeObj = mergeObj[0];

              var htmlString = obj.html_string;

              if (mergeObj.status == "Signed") {
                var searchIndex = [
                  {
                    term: "{{PLEASE SIGN HERE}}",
                    value:
                      '<img width="300px" src="https://bento.infinityhospitality.net/_repos/_production/pagoda/_files/_instances/contracts/' +
                      mergeObj.contract.signatures.loc +
                      '"> - ' +
                      mergeObj.contract.signer_name +
                      " @ " +
                      moment(mergeObj.contract.signatures.date_created).format(
                        "M/DD/YYYY h:mm:ss a"
                      ) +
                      " - " +
                      mergeObj.contract.signer_ip,
                  },
                ];
              } else {
                var searchIndex = [
                  {
                    term: "{{PLEASE SIGN HERE}}",
                    value:
                      '<span class="signtaureLine" style="font-weight:bold;">PLEASE SIGN HERE</span>',
                  },
                ];
              }
              //console.log('mergeBP',mergeBP);
              _.each(mergeBP, function (bpObj, name) {
                searchIndex.push({
                  term: "{{" + objectType + "." + name + "}}",
                  value: mergeObj[name],
                });
              });

              _.each(allBlueprints, function (obj) {
                _.each(obj.blueprint, function (bpObj, name) {
                  if (mergeObj[obj.bp_name]) {
                    var searchValue = mergeObj[obj.bp_name][name];
                  } else {
                    var searchValue = "";
                  }

                  searchIndex.push({
                    term: "{{" + obj.bp_name + "." + name + "}}",
                    value: searchValue,
                  });
                });
              });
              //console.log('searchIndex',searchIndex);
              _.each(searchIndex, function (i) {
                htmlString = htmlString.replace(
                  new RegExp(i.term, "g"),
                  i.value
                );
              });

              callback(htmlString);
            });
          },
          adminURL + "_getAdmin.php?do="
        );
      },
      adminURL + "_getAdmin.php?do="
    );
  }

  function enterPayment(obj, mainDom) {
    function enterManualPayment(obj, total, mainDom) {
      var dom = this,
        formArgs = {
          type: {
            name: "type",
            type: "select",
            label: "Payment Type",
            options: [
              { name: "Check", value: "check" },
              { name: "Cash", value: "cash" },
              { name: "Other", value: "other" },
            ],
          },
          amount: {
            name: "amount",
            type: "usd",
            label: "Payment Amount",
          },
          payment_date: {
            name: "payment_date",
            type: "date",
            label: "Payment Date",
            dateFormat: "M/D/YYYY",
          },
          details: {
            name: "text",
            type: "textbox",
            label: "Details",
            rows: 10,
          },
        };

      dom.body.empty();

      dom.body.makeNode("cont", "container", { css: "pda-container" });

      dom.body.cont.makeNode("title", "headerText", {
        text: "Manual Payment",
        size: "small",
      });

      dom.body.cont.makeNode("titleBreak", "lineBreak", {});

      dom.body.cont.makeNode("form", "form", formArgs);

      dom.body.patch();

      dom.footer.makeNode("btns", "buttonGroup", {});

      dom.footer.btns
        .makeNode("save", "button", {
          text: '<i class="fa fa-check"></i> Save Payment',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "invoicesRun",
            data: {
              run: saveManualPayment.bind(mainDom, obj, total),
            },
          },
          sb.moduleId
        );

      dom.footer.btns
        .makeNode("cancel", "button", {
          text: '<i class="fa fa-times"></i> Cancel',
          css: "pda-btn-red",
        })
        .notify(
          "click",
          {
            type: "invoicesRun",
            data: {
              run: enterPayment.bind(mainDom, obj),
            },
          },
          sb.moduleId
        );

      dom.footer.patch();
    }

    function saveManualPayment(obj, total) {
      var dom = this,
        formData = dom.modals.modal.body.cont.form.process();

      if (formData.completed == false) {
        sb.dom.alerts.alert("", "Please fill out the whole form.", "error");
        return;
      }

      dom.modals.modal.footer.btns.save.loading();

      sb.data.db.controller(
        "getIPAddress&api_webform=true&pagodaAPIKey=" + appConfig.instance,
        {},
        function (ip) {
          var paymentObj = {
            amount: +formData.fields.amount.value,
            details: {
              payment_date: formData.fields.payment_date.value,
              type: formData.fields.type.value,
              notes: formData.fields.text.value,
              ip_address: ip,
            },
            vendor_id: formData.fields.type.value,
          };

          sb.data.db.obj.create("payments", paymentObj, function (newPayment) {
            if (!obj.payments) {
              obj.payments = [];
            }

            obj.paid += +formData.fields.amount.value;
            obj.payments.push(newPayment.id);
            obj.balance = obj.amount - obj.paid;

            sb.data.db.obj.update(
              "invoices",
              obj,
              function (updatedInvoice) {
                dom.modals.modal.hide(function () {
                  singleInvoice(updatedInvoice, dom);
                });
              },
              4
            );
          });
        },
        "https://bento.infinityhospitality.net/_repos/_production/notify/pagoda/_coredev/_getAdmin.php?do="
      );
    }

    var dom = this,
      total = 0,
      totalPaid = 0;

    _.each(obj.items, function (item) {
      if (item.tax_rate) {
        total += item.amount * (item.tax_rate / 100) + item.amount;
      } else {
        total += item.amount * item.quantity;
      }
    });

    _.each(obj.payments, function (item) {
      totalPaid += item.amount;
    });

    total = total - totalPaid;

    var contactId = 0;
    if (obj.main_contact) {
      contactId = obj.main_contact.id;
    }

    dom.modals.makeNode("modal", "modal", {});

    dom.modals.modal.body.makeNode("btns", "buttonGroup", {});

    dom.modals.modal.body.makeNode("payNow", "container", {});

    dom.modals.patch();

    dom.modals.modal.show(function () {
      sb.notify({
        type: "show-make-payment-button",
        data: {
          domObj: dom.modals.modal.body.payNow,
          payment: {
            customerId: contactId,
            invoiceId: obj.id,
            price: Math.trunc(total),
            admin: false,
          },
          buttonSetup: {
            text:
              '<i class="fa fa-credit-card"></i> Pay $' +
              (total / 100).formatMoney() +
              " now",
            css: "pda-btn-green pda-btn-large",
            skip: true,
            notification: "client-invoice-payment-completed",
            action: function (obj, callback) {
              var dom = this;

              callback(dom, obj);
            }.bind(dom, obj),
          },
        },
      });
    });
  }

  function getAllBluprints(mainBP, callback) {
    var bpsToGet = _.compact(
      _.map(mainBP, function (o, name) {
        if (o.type == "objectId" || o.type == "objectIds") {
          o.bp_name = name;

          return o;
        }
      })
    );

    function getBlueprints(list, callback, count, ret) {
      if (!count) {
        count = 0;
      }

      if (!ret) {
        ret = [];
      }

      if (list[count]) {
        sb.data.db.controller(
          "getObjectBlueprint&api_webform=true&pagodaAPIKey=" + instance,
          { objectType: list[count].objectType },
          function (bp) {
            ret.push({
              type: list[count].objectType,
              blueprint: bp,
              bp_name: list[count].bp_name,
            });

            count++;

            getBlueprints(list, callback, count, ret);
          },
          adminURL + "_getAdmin.php?do="
        );
      } else {
        callback(ret);
      }
    }

    getBlueprints(bpsToGet, function (allBlueprints) {
      callback(allBlueprints);
    });
  }

  function getInstanceImage(config, callback) {
    if (config.company_logo) {
      sb.data.db.controller(
        "getObjectById&api_webform=true&pagodaAPIKey=" + config.instance,
        { value: config.company_logo, type: "file_meta_data" },
        function (image) {
          callback(
            '<img width="150px"; src="' + sb.data.files.getURL(image) + '">'
          );
        },
        adminURL + "_getAdmin.php?do="
      );
    } else {
      callback(config.systemName);
    }
  }

  function invoiceView(dom, obj, callback) {
    var selectedInvoices = [];

    dom.makeNode("modals", "div", {});

    dom
      .makeNode("cont1", "div", { css: "ui one centered grid" })
      .makeNode("col", "column", { w: 16 });

    dom.cont1.col.makeNode("tableCont", "div", { css: "" });

    dom.cont1.col.tableCont.makeNode("btns", "buttonGroup", { css: "" });

    dom.cont1.col.tableCont.makeNode("tableBreak", "div", { text: "<br />" });

    dom.cont1.col.tableCont.makeNode("table", "table", {
      css: "table-hover table-condensed",
      columns: {
        select: "",
        id: "Invoice",
        due: "Amount Due",
        dueDate: "Due Date",
        btns: "",
      },
    });

    dom.cont1.col.tableCont.makeNode("invoiceBreak", "div", {
      text: "<br /><br />",
    });

    dom.cont1.col.tableCont.makeNode("invoiceCont", "container", { css: "" });

    dom.patch();

    sb.data.db.controller(
      "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
      {
        queryObj: {
          related_object: obj.proposal.id,
          active: "Yes",
        },
        instance: instance,
        objectType: "invoices",
      },
      function (invoicesList) {
        invoices = invoicesList;

        dom.cont1.col.tableCont.btns
          .makeNode("pay", "button", {
            text: '<i class="fa fa-usd"></i> Pay',
            css: "pda-btn-green",
          })
          .notify(
            "click",
            {
              type: "invoicesPay-run",
              data: {
                run: function (obj) {
                  if (selectedInvoices.length == 0) {
                    sb.dom.alerts.alert(
                      "",
                      "Please select some invoices",
                      "error"
                    );
                    return;
                  }

                  var dom = this,
                    total = _.chain(selectedInvoices)
                      .pluck("balance")
                      .reduce(function (memo, num) {
                        return memo + +num;
                      });

                  dom.modals.makeNode("pay", "modal", {
                    onShow: function () {
                      sb.notify({
                        type: "show-make-payment-button",
                        data: {
                          domObj: dom.modals.pay.body,
                          payment: {
                            customerId: obj.main_contact.id,
                            invoiceId: selectedInvoices,
                            price: Math.trunc(total),
                            admin: false,
                          },
                          buttonSetup: {
                            text:
                              '<i class="fa fa-credit-card"></i> Pay $' +
                              (total / 100).formatMoney() +
                              " now",
                            css: "pda-btn-green pda-btn-large",
                            skip: true,
                            notification: "client-invoice-payment-completed",
                            action: function (obj, selectedInvoices, callback) {
                              var dom = this;

                              callback(dom, obj, selectedInvoices);
                            }.bind(dom, obj, selectedInvoices),
                          },
                        },
                      });

                      dom.modals.pay.footer.makeNode("text", "headerText", {
                        text:
                          '<span class="small pda-color-red">Seperate invoices will be displayed as seperate charges on your bank statement.</span><br /><br />Total $' +
                          (total / 100).formatMoney(),
                        size: "x-small",
                        css: "text-center",
                      });

                      dom.modals.pay.footer.patch();
                    },
                  });

                  dom.modals.patch();

                  dom.modals.pay.show();
                }.bind(dom, obj),
              },
            },
            sb.moduleId
          );

        _.each(invoices, function (inv) {
          dom.cont1.col.tableCont.table.makeRow("inv-" + inv.id, [
            "",
            inv.name,
            "$" + (inv.balance / 100).formatMoney(),
            inv.due_date,
            "",
          ]);

          dom.cont1.col.tableCont.table.body["inv-" + inv.id].select
            .makeNode("select", "div", {
              text: '<i class="fa fa-square-o fa-2x"></i>',
              css: "ui center aligned",
            })
            .notify(
              "click",
              {
                type: "invoicesPay-run",
                data: {
                  run: function (inv) {
                    if (_.findWhere(selectedInvoices, { id: inv.id })) {
                      selectedInvoices = _.reject(
                        selectedInvoices,
                        function (obj) {
                          return obj.id == inv.id;
                        }
                      );

                      this.cont1.col.tableCont.table.body[
                        "inv-" + inv.id
                      ].select.select.text(
                        '<i class="fa fa-square-o fa-2x"></i>'
                      );
                    } else {
                      selectedInvoices.push(inv);

                      this.cont1.col.tableCont.table.body[
                        "inv-" + inv.id
                      ].select.select.text(
                        '<i class="fa fa-check-square-o fa-2x"></i>'
                      );
                    }
                  }.bind(dom, inv),
                },
              },
              sb.moduleId
            );

          dom.cont1.col.tableCont.table.body["inv-" + inv.id].btns.makeNode(
            "btns",
            "div",
            { css: "one ui fluid buttons" }
          );

          dom.cont1.col.tableCont.table.body["inv-" + inv.id].btns.btns
            .makeNode("download", "div", {
              tag: "button",
              text: '<i class="fa fa-eye"></i> View',
              css: "ui blue fluid button",
            })
            .notify(
              "click",
              {
                type: "invoicesPay-run",
                data: {
                  run: function (inv, dom) {
                    var button = this;

                    //button.loading();

                    buildInvoice.call(dom, inv, false, function (draw) {
                      draw.dom.patch();

                      draw.after(draw.dom);

                      //button.loading(false);
                    });
                  }.bind(
                    dom.cont1.col.tableCont.table.body["inv-" + inv.id].btns
                      .btns.download,
                    inv,
                    dom.cont1.col.tableCont.invoiceCont
                  ),
                },
              },
              sb.moduleId
            );
          //dom.table.body['inv-'+inv.id].btns.btns.makeNode('pay', 'button', {text:'<i class="fa fa-usd"></i> Pay', css:'pda-btn-green'});
        });

        delete dom.loading;

        //dom.makeNode('finalBreak', 'div', {text:'<br />'});

        dom.patch();

        callback(true);
      }
    );
  }

  function mergeString(contract, callback, signMergeString) {
    var relatedId = 0;
    if (contract.hasOwnProperty("related_object")) {
      if (contract.related_object.hasOwnProperty("id")) {
        relatedId = contract.related_object.id;
      } else {
        relatedId = contract.related_object;
      }
    }

    sb.data.db.controller(
      "getObjectById&pagodaAPIKey=" + instance,
      {
        value: relatedId,
        type: contract.contract_types.contract_object_type,
        childObjs: 1,
      },
      function (mergeObj) {
        sb.data.db.controller(
          "getObjectBlueprint&pagodaAPIKey=" + instance,
          { objectType: contract.contract_types.contract_object_type },
          function (mergeBP) {
            getAllBluprints(mergeBP, function (allBlueprints) {
              var searchIndex = [];

              /*
					if(signMergeString){
						var signStringToMerge = signMergeString;
					}else{
						var signStringToMerge = '<span id="signatureLine" class="text-warning signatureLine" style="font-weight:bold; font-size:22px;">SIGN HERE</span>';								
					}

					var searchIndex = [{
						term:'{{PLEASE SIGN HERE}}',
						value:signStringToMerge
					}];
*/

              _.each(mergeBP, function (bpObj, name) {
                searchIndex.push({
                  term:
                    "{{" +
                    contract.contract_types.contract_object_type +
                    "." +
                    name +
                    "}}",
                  value: mergeObj[name],
                });
              });

              _.each(allBlueprints, function (obj) {
                _.each(obj.blueprint, function (bpObj, name) {
                  if (mergeObj[obj.bp_name]) {
                    var searchValue = mergeObj[obj.bp_name][name];
                  } else {
                    var searchValue = "";
                  }

                  searchIndex.push({
                    term: "{{" + obj.bp_name + "." + name + "}}",
                    value: searchValue,
                  });
                });
              });

              var merged = contract.html_string;
              _.each(searchIndex, function (i) {
                merged = merged.replace(new RegExp(i.term, "g"), i.value);
              });

              contract.html_string_merged = merged;

              callback(contract.html_string_merged);
            });
          },
          adminURL + "_getAdmin.php?do="
        );
      },
      adminURL + "_getAdmin.php?do="
    );
  }

  function processWorkorderAcceptance(obj, prop, k, status, callback) {}

  function proposalView(dom, obj) {
    dom
      .makeNode("seg", "div", { css: "ui padded clearing segment" })
      .makeNode("loader", "div", { css: "ui active inverted dimmer" })
      .makeNode("loader", "div", {
        css: "ui text loader",
        text: "Loading proposals",
      });

    dom.seg.makeNode("title", "div", {
      text: "Your proposals",
      css: "ui huge header",
    });

    var cards = dom.seg.makeNode("row", "div", { css: "four ui fluid cards" });

    dom.patch();

    sb.data.db.controller(
      "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
      {
        queryObj: {
          main_object: obj.id,
        },
        instance: instance,
        objectType: "proposals",
        getChildObjs: 1,
      },
      function (proposals) {
        proposals = _.reject(proposals, function (prop) {
          return prop.name == "";
        });
        proposals = _.reject(proposals, function (prop) {
          return prop.status == "Proposal";
        });
        proposals = _.reject(proposals, function (prop) {
          return prop.status == "Active";
        });

        if (proposals.length > 0) {
          _.each(proposals, function (prop, k) {
            if (prop.name != "") {
              cards
                .makeNode("col-" + k, "div", { css: "card" })
                .makeNode("prop", "div", { css: "content" });

              cards["col-" + k].prop.makeNode("name", "div", {
                text: prop.name,
                css: "header",
              });
              cards["col-" + k].prop.makeNode("price", "div", {
                text:
                  "Price: $" +
                  (
                    _.reduce(
                      Object.values(prop.pricing),
                      function (price, memo) {
                        return +memo + +price;
                      },
                      0
                    ) / 100
                  ).formatMoney(),
                css: "description",
              });
              cards["col-" + k]
                .makeNode("button", "div", {
                  css: "ui bottom attached blue button",
                  text: "View Proposal",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: function (prop, k, obj) {
                        var dom = this;

                        _.each(obj.history, function (item, key) {
                          if (k != key) {
                            //dom.row['col-'+key].makeNode('prop', 'div', {css:'ui piled segment'});
                            //dom.row['col-'+key].prop.css('');
                          } else {
                            //dom.row['col-'+key].prop.css('pda-container pda-Panel pda-panel-gray pda-background-orange');
                          }
                        });

                        dom
                          .makeNode("propCont", "div", {
                            css: "ui basic segment",
                            style: "max-width:850px; margin:1.5em auto;",
                          })
                          .makeNode("cont", "div", { css: "small-container" });

                        dom.patch();

                        showProposal(
                          dom.propCont,
                          prop,
                          k,
                          obj,
                          function (done) {}
                        );
                      }.bind(dom, prop, k, obj),
                    },
                  },
                  sb.moduleId
                );
            }
          });
        } else {
          dom.seg.makeNode("title", "div", {
            css: "ui center aligned huge header",
            text: "Nothing to approve.",
          });
        }

        delete dom.seg.loader;

        dom.seg.patch();
      }
    );
  }

  function signContractView(dom, obj, categories) {
    function dataURItoBlob(dataURI) {
      // convert base64 to raw binary data held in a string
      // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
      var byteString = atob(dataURI.split(",")[1]);

      // separate out the mime component
      var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];

      // write the bytes of the string to an ArrayBuffer
      var ab = new ArrayBuffer(byteString.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }

      //Old Code
      //write the ArrayBuffer to a blob, and you're done
      //var bb = new BlobBuilder();
      //bb.append(ab);
      //return bb.getBlob(mimeString);

      //New Code
      return new Blob([ab], { type: mimeString });
    }

    function getConsent() {
      contractId = obj.contract.id;

      this.makeNode("consent", "div", {});

      this.consent.makeNode("break", "lineBreak", {});

      this.consent.makeNode("loading", "headerText", {
        text: "Loading consent form...",
        css: "text-center",
        size: "x-small",
      });

      this.consent.makeNode("loader", "loader", { size: "" });

      this.patch();

      var dom = this;

      sb.data.db.controller(
        "getObjectsWhere&pagodaAPIKey=" + instance,
        {
          queryObj: { main_object: +obj.id, status: "Accepted" },
          getChildObjs: 1,
          instance: instance,
          objectType: "proposals",
        },
        function (props) {
          console.log("props", props);
          contractId = props[0].contract.id;

          sb.data.db.controller(
            "getObjectById&pagodaAPIKey=" + instance,
            { value: +contractId, type: "contracts", childObjs: 4 },
            function (contract) {
              console.log("contract", contract);
              if (contract.status == "Signed") {
                mergeString(
                  contract,
                  function (merged) {
                    //dom.empty();

                    dom.makeNode("col", "column", { width: 6, offset: 3 });

                    dom.col.makeNode("cont", "div", {
                      css: "ui basic segment",
                    });

                    dom.col.cont.makeNode("title", "headerText", {
                      text: "Signing Complete!",
                      css: "text-center",
                    });

                    //dom.col.cont.makeNode('break1', 'lineBreak', {});

                    dom.col.cont.makeNode("download", "headerText", {
                      text: "Download a copy for your records.",
                      css: "text-center",
                      size: "small",
                    });

                    dom.col.cont.makeNode("btns", "buttonGroup", { css: "" });
                    dom.col.cont.btns
                      .makeNode("pdf", "button", {
                        text: '<i class="fa fa-download"></i> Download',
                        css: "pda-btn-blue",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function (contract) {
                              var dom = this;

                              sb.data.makePDF(merged, "D");
                            }.bind(dom, contract, merged),
                          },
                        },
                        sb.moduleId
                      );

                    dom.col.cont.makeNode("invoices", "headerText", {
                      text: "Invoices",
                      size: "x-small",
                    });

                    dom.patch();
                  },
                  '<img width="300px" src="https://bento.infinityhospitality.net/_repos/_production/pagoda/_files/_instances/contracts/' +
                    contract.signatures.loc +
                    '"> - ' +
                    fullName +
                    " @ " +
                    moment().format("M/DD/YYYY h:mm:ss a")
                );
              } else {
                sb.data.db.controller(
                  "getAll&pagodaAPIKey=" + instance,
                  { objectType: "contract_system" },
                  function (systemSettings) {
                    var systemSettings = systemSettings[0];

                    //delete dom.break;
                    delete dom.consent.loader;
                    delete dom.consent.loading;

                    dom.consent.makeNode("break", "div", {
                      text: "<br /><br />",
                    });

                    dom
                      .makeNode("col", "div", {
                        css: "ui one column centered grid",
                      })
                      .makeNode("cont", "column", { w: 8 });

                    dom.col.cont.makeNode("cont", "div", {
                      css: "ui basic segment",
                    });

                    dom.col.cont.cont.makeNode("title", "headerText", {
                      text: "Electronic Signature Disclaimer",
                    });

                    dom.col.cont.cont.makeNode("disclaimer", "text", {
                      text: systemSettings.signature_disclaimer,
                    });

                    //dom.col.cont.cont.makeNode('break', 'lineBreak', {});

                    dom.col.cont.cont.makeNode("form", "form", {
                      name: {
                        type: "text",
                        name: "name",
                        label: "Your Full Name",
                      },
                      email: {
                        type: "text",
                        name: "email",
                        label:
                          "Your Email Address (we will send a copy of the signed contract here)",
                      },
                    });

                    dom.col.cont.cont.makeNode("formBreak", "div", {
                      text: "<br />",
                    });

                    dom.col.cont.cont.makeNode("btns", "div", {
                      css: "ui buttons",
                    });

                    dom.col.cont.cont.btns
                      .makeNode("accept", "button", {
                        text: '<i class="fa fa-check"></i> Accept & Continue',
                        css: "pda-btn-green",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function () {
                              var formInfo = this.col.cont.cont.form.process();

                              if (formInfo.completed == false) {
                                sb.dom.alerts.alert(
                                  "Error",
                                  "Please enter your name.",
                                  "error"
                                );
                                return;
                              }

                              fullName = formInfo.fields.name.value;
                              email = formInfo.fields.email.value;

                              this.col.cont.cont.btns.accept.loading();

                              obj.contract.signer_email = email;
                              obj.contract.signer_name = fullName;

                              delete this.col;

                              viewContract(this, props[0]);
                            }.bind(dom),
                          },
                        },
                        sb.moduleId
                      );

                    dom.patch();
                  },
                  adminURL + "_getAdmin.php?do="
                );
              }
            },
            adminURL + "_getAdmin.php?do="
          );
        }
      );
    }

    function getSignature(contract) {
      var modal = this.modals.modal,
        dom = this;

      contract.related_object = obj;

      dom.modals.empty();

      dom.modals.patch();

      dom.modals.makeNode("signature", "modal", {
        onShow: function () {
          modal.footer.makeNode("btns", "buttonGroup", { css: "" });

          modal.body.makeNode("title", "div", {
            css: "ui huge header",
            text: "Create a signature",
          });

          modal.body.makeNode("cols", "div", { css: "ui grid" });

          modal.body.cols
            .makeNode("col1", "column", { width: 3 })
            .makeNode("cont", "container", { css: "pda-container" })
            .makeNode("buttonBreak", "lineBreak", {});
          modal.body.cols.makeNode("col2", "column", { width: 9 });

          modal.body.cols.col1.makeNode("choiceBreak1", "lineBreak", {});

          modal.body.cols.col1.cont.makeNode("btns", "buttonGroup", {});

          modal.body.cols.col1.cont.btns
            .makeNode("signature", "div", {
              tag: "button",
              text: "Signature",
              css: "ui fluid blue button",
            })
            .notify(
              "click",
              {
                type: "invoicesPay-run",
                data: {
                  run: function (btns, footer) {
                    this.empty();

                    this.makeNode("btns", "buttonGroup", { css: "pull-right" })
                      .makeNode("clear", "button", {
                        text: '<i class="fa fa-repeat"></i> Clear signature and start over',
                        css: "pda-btnOutline-orange",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function () {
                              signaturePad.clear();
                            },
                          },
                        },
                        sb.moduleId
                      );

                    this.makeNode("btnsBreak", "lineBreak", {});

                    this.makeNode("sign", "container", {
                      css: "pda-container pda-background-blue pda-Panel",
                      text: "",
                    });
                    this.sign.makeNode("helperText", "headerText", {
                      text: "Sign below",
                      css: "text-muted text-left",
                      size: "x-small",
                    });
                    this.sign.makeNode("signingCanvas", "text", {
                      text: '<canvas id="signature-pad" class="signature-pad" width=700 height=200></canvas>',
                    });
                    this.patch();

                    btns.signature.css("pda-btn-blue pda-btn-fullWidth");
                    btns.printed.css("pda-btnOutline-blue pda-btn-fullWidth");

                    var signaturePad = new SignaturePad(
                      document.getElementById("signature-pad"),
                      {
                        backgroundColor: "rgba(255, 255, 255, 0)",
                        penColor: "rgb(0, 76, 198)",
                      }
                    );

                    footer
                      .makeNode("save", "button", {
                        text: 'Save and continue <i class="fa fa-arrow-right"></i>',
                        css: "pda-btn-green",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function (dom, contract, signaturePad) {
                              this.footer.btns.save.loading();

                              if (signaturePad.isEmpty()) {
                                sb.dom.alerts.alert(
                                  "Error",
                                  "Please create a signature before continuing.",
                                  "error"
                                );
                                return;
                              }

                              saveSignature.call(
                                this,
                                dom,
                                contract,
                                signaturePad
                              );
                            }.bind(modal, dom, contract, signaturePad),
                          },
                        },
                        sb.moduleId
                      );

                    footer.patch();
                  }.bind(
                    modal.body.cols.col2,
                    modal.body.cols.col1.cont.btns,
                    modal.footer.btns
                  ),
                },
              },
              sb.moduleId
            );

          /*
					modal.body.col1.cont.btns.makeNode('printed', 'button', {text:'Printed', size:'x-small', css:'pda-btn-fullWidth pda-btnOutline-blue'}).notify('click', {
						type:'invoicesPay-run',
						data:{
							run:function(btns, footer){
		
								this.empty();
								
								this.makeNode('break', 'lineBreak', {});
								
								this.makeNode('cont', 'container', {css:'pda-container pda-Panel pda-background-blue'});
								this.cont.makeNode('helperText', 'headerText', {text:'Signing as: '+ fullName, size:'x-small', css:'text-muted'});
								this.cont.makeNode('signature', 'headerText', {text:fullName, size:'x-large', css:'text-center handwritten'});
								
								this.patch();
								
								footer.makeNode('save', 'button', {text:'Save and continue <i class="fa fa-arrow-right"></i>', css:'pda-btn-green'}).notify('click', {
									type:'invoicesPay-run',
									data:{
										run:function(dom, contract){
											
											this.footer.btns.save.loading();
											
											useTextSignature.call(this, dom, contract);
											
										}.bind(modal, dom, contract)
									}
								}, sb.moduleId);
								
								footer.patch();
								
								btns.signature.css('pda-btnOutline-blue pda-btn-fullWidth');
								btns.printed.css('pda-btn-blue pda-btn-fullWidth');
								
							}.bind(modal.body.col2, modal.body.col1.cont.btns, modal.footer.btns)
						}
					}, sb.moduleId);
		*/

          modal.body.cols.col2.makeNode("btns", "buttonGroup", {
            css: "pull-right",
          });
          modal.body.cols.col2.makeNode("btnsBreak", "lineBreak", {});

          modal.body.cols.col2.makeNode("sign", "container", {
            css: "pda-container pda-background-blue pda-Panel",
            text: "",
          });
          modal.body.cols.col2.sign.makeNode("helperText", "headerText", {
            text: "Sign below",
            css: "text-muted text-left",
            size: "x-small",
          });
          modal.body.cols.col2.sign.makeNode("signingCanvas", "div", {
            css: "ui tertiary inverted segment",
            text: '<canvas id="signature-pad" class="signature-pad" width=700 height=200></canvas>',
          });

          modal.body.makeNode("finalBreak", "lineBreak", {});

          modal.body.patch();
          modal.footer.patch();

          setTimeout(function () {
            var signaturePad = new SignaturePad(
              document.getElementById("signature-pad"),
              {
                backgroundColor: "rgba(255, 255, 255, 0)",
                penColor: "rgb(0, 76, 198)",
              }
            );

            modal.body.cols.col2.btns
              .makeNode("clear", "button", {
                text: '<i class="fa fa-repeat"></i> Clear signature and start over',
                css: "pda-btnOutline-orange",
              })
              .notify(
                "click",
                {
                  type: "invoicesPay-run",
                  data: {
                    run: function () {
                      signaturePad.clear();
                    },
                  },
                },
                sb.moduleId
              );

            modal.body.cols.col2.btns.patch();

            modal.footer.btns
              .makeNode("save", "button", {
                text: 'Save and continue <i class="fa fa-arrow-right"></i>',
                css: "pda-btn-green",
              })
              .notify(
                "click",
                {
                  type: "invoicesPay-run",
                  data: {
                    run: function (dom, contract, signaturePad) {
                      modal.footer.btns.save.loading();

                      if (signaturePad.isEmpty()) {
                        sb.dom.alerts.alert(
                          "Error",
                          "Please create a signature before continuing.",
                          "error"
                        );
                        return;
                      }

                      saveSignature.call(modal, dom, contract, signaturePad);
                    }.bind(modal, dom, contract, signaturePad),
                  },
                },
                sb.moduleId
              );

            modal.footer.btns.patch();
          }, 0);
        },
      });

      dom.modals.patch();

      dom.modals.signature.show();
    }

    function saveSignature(dom, contract, signaturePad) {
      var signature = signaturePad.toData(),
        imageBlob = dataURItoBlob(signaturePad.toDataURL()),
        fileMetaData = {
          fileType: "png",
          fileName: "contract-signature-" + contract.id,
          objectType: contract.contract_types.contract_object_type,
          objectId: contract.related_object,
          parent: contract.related_object,
          isPublic: 0,
        },
        modal = this,
        emails = {
          signer: contract.signer_email,
          manager: contract.related_object.manager.email,
        },
        args = {},
        emailBatch = [];

      if (fileMetaData.objectType === "") {
        fileMetaData.objectType = "root";
      }
      if (isNaN(fileMetaData.objectId)) {
        fileMetaData.objectId = 0;
      }

      appConfig.instance = contract.instance;

      _.each(
        emails,
        function (email, owner) {
          args[owner] = {
            to: email,
            from: "<EMAIL>",
            subject: "Proposal Signed",
            mergevars: {
              TITLE: "Proposal Signed",
              BODY:
                "Proposal for <strong>" +
                contract.related_object.name +
                "</strong> has been signed by <strong>" +
                contract.signer_name +
                "</strong>" +
                " on <strong>" +
                moment().format("MMMM Do, YYYY h:mm:ss A") +
                "</strong>",
            },
          };

          emailBatch.push(args[owner]);
        },
        this
      );

      sb.comm.sendEmail(emailBatch, function (ret) {
        if (ret) {
          sb.data.files.changePaths(contract.instance);

          sb.data.files.upload(imageBlob, fileMetaData, function (savedFile) {
            sb.data.db.controller(
              "updateObject&pagodaAPIKey=" + instance,
              {
                objectType: "contracts",
                objectData: { id: contract.id, signatures: savedFile.id },
              },
              function (updated) {
                sb.data.db.controller(
                  "getObjectById&pagodaAPIKey=" + instance,
                  { value: contract.id, type: "contracts", childObjs: 4 },
                  function (contract) {
                    viewProposalPDF(
                      obj.proposal,
                      categories,
                      obj.sections,
                      function (merged) {
                        contract.html_string_merged = merged;

                        dom.modals.signature.hide();

                        dom.cont.btns.pdf.css("pda-btn-disabled pda-btn-blue");
                        dom.cont.btns.sign.css(
                          "pda-btn-disabled pda-btn-green"
                        );
                        dom.cont.btns.sign.text(
                          '<i class="fa fa-pencil"></i> Signing'
                        );

                        contract.html_string_merged =
                          contract.html_string_merged.replace(
                            new RegExp("PLEASE SIGN HERE", "g"),
                            "{{PLEASE SIGN HERE}}"
                          );

                        scanDocumentForSignatureLines.call(this, dom, contract);
                      },
                      true
                    );
                  }
                );
              },
              adminURL + "_getAdmin.php?do="
            );
          });
        }
      });
    }

    function scanDocumentForSignatureLines(dom, contract, tour) {
      console.log("contract", contract);

      contract.html_string = contract.html_string_merged;
      contract.html_string_merged = contract.html_string_merged.replace(
        new RegExp("{{PLEASE SIGN HERE}}"),
        '<span id="signatureLine" class="text-warning signatureLine" style="font-weight:bold; font-size:18px;">SIGN HERE</span>'
      );

      dom.cont.contractContainer.makeNode("contract", "text", {
        text:
          '<div class="pda-container pda-Panel contractBox ui basic segment" style="max-width:850px; display:block; margin:0 auto;">' +
          contract.html_string_merged +
          "</div>",
      });
      dom.cont.contractContainer.patch();

      if ($(".signatureLine").get(0)) {
        $(".mainCanvas").animate(
          {
            scrollTop:
              $(".mainCanvas").scrollTop() -
              100 +
              $("#signatureLine").offset().top -
              $(".mainCanvas").offset().top,
          },
          {
            duration: 1000,
            specialEasing: {
              width: "linear",
              height: "easeOutBounce",
            },
            complete: function (e) {
              if (!tour) {
                var tour = {
                  id: "hello-hopscotch",
                  showNextButton: false,
                  smoothScroll: false,
                  steps: [
                    {
                      title: "Place Your Signature",
                      content:
                        '<button class="placeSignatureButton btn pda-btn-green pda-btn-full-width">PLACE YOUR SIGNATURE HERE</button>',
                      target: "signatureLine",
                      placement: "right",
                      onShow: function () {
                        $(".placeSignatureButton").on("click", function () {
                          $(".placeSignatureButton").html(
                            '<i class="fa fa-circle-o-notch fa-spin"></i> Placing Signature'
                          );

                          setTimeout(function () {
                            contract.html_string_merged =
                              contract.html_string.replace(
                                new RegExp("{{PLEASE SIGN HERE}}"),
                                '<img width="300px" src="https://bento.infinityhospitality.net/_repos/_production/pagoda/_files/_instances/workorders/' +
                                  contract.signatures.loc +
                                  '"> - ' +
                                  fullName +
                                  " @ " +
                                  moment().format("M/DD/YYYY h:mm:ss a")
                              );
                            contract.html_string = contract.html_string_merged;

                            dom.cont.contractContainer.makeNode(
                              "contract",
                              "text",
                              {
                                text:
                                  '<div class="pda-container pda-Panel" style="max-width:850px; display:block; margin:0 auto;">' +
                                  contract.html_string_merged +
                                  "</div>",
                              }
                            );

                            dom.cont.contractContainer.patch();

                            hopscotch.endTour();

                            scanDocumentForSignatureLines(dom, contract, tour);
                          }, 1000);
                        });
                      },
                    },
                  ],
                };
              }

              hopscotch.startTour(tour);
            },
          }
        );
      } else {
        setTimeout(function () {
          $(".mainCanvas").animate(
            {
              scrollTop: $(".contractBox").scrollTop(),
            },
            {
              duration: 1000,
              specialEasing: {
                width: "linear",
                height: "easeOutBounce",
              },
              complete: function (e) {
                sb.data.db.controller(
                  "getIPAddress&pagodaAPIKey=" + instance,
                  {},
                  function (ip) {
                    var updateContract = {
                      id: contract.id,
                      signer_ip: ip,
                      status: "Signed",
                      signer_name: fullName,
                      signer_email: email,
                    };

                    sb.data.db.controller(
                      "updateObject&pagodaAPIKey=" + instance,
                      { objectType: "contracts", objectData: updateContract },
                      function (updated) {
                        sb.data.db.controller(
                          "updateObject&pagodaAPIKey=" + instance,
                          {
                            objectType: "proposals",
                            objectData: {
                              id: contract.related_object.id,
                              status: "Signed",
                            },
                          },
                          function (updatedProposal) {
                            location.reload();
                          },
                          adminURL + "_getAdmin.php?do="
                        );
                      },
                      adminURL + "_getAdmin.php?do="
                    );
                  },
                  adminURL + "_getAdmin.php?do="
                );
              },
            }
          );
        }, 300);
      }
    }

    function useTextSignature(dom, contract, tour) {
      var imageBlob = "",
        signatureElement = document.getElementsByClassName(
          dom.modals.modal.body.col2.cont.signature.selector
        ),
        fileMetaData = {
          fileType: "png",
          fileName: "contract-signature-" + contract.id,
          objectType: contract.contract_types.contract_object_type,
          objectId: contract.related_object,
          parent: contract.related_object,
          isPublic: 0,
        },
        modal = this;

      if (fileMetaData.objectType === "") {
        fileMetaData.objectType = "root";
      }
      if (isNaN(fileMetaData.objectId)) {
        fileMetaData.objectId = 0;
      }

      appConfig.instance = contract.instance;

      sb.data.files.upload(imageBlob, fileMetaData, function (savedFile) {
        sb.data.db.controller(
          "updateObject&pagodaAPIKey=contracts",
          {
            objectType: "contracts",
            objectData: { id: contract.id, signatures: savedFile.id },
          },
          function (updated) {
            sb.data.db.controller(
              "getObjectById&pagodaAPIKey=" + instance,
              { value: contract.id, type: "contracts", childObjs: 4 },
              function (contract) {
                modal.hide(function (hidden) {
                  dom.cont.btns.pdf.css("pda-btn-disabled pda-btn-blue");
                  dom.cont.btns.sign.css("pda-btn-disabled pda-btn-green");
                  dom.cont.btns.sign.text(
                    '<i class="fa fa-pencil"></i> Signing'
                  );

                  mergeString(contract, function (merged) {
                    contract.html_string_merged = merged;

                    scanDocumentForSignatureLines.call(this, dom, contract);
                  });
                });
              }
            );
          },
          adminURL + "_getAdmin.php?do="
        );
      });
    }

    function viewContract(dom, obj) {
      sb.data.db.controller(
        "getObjectById&pagodaAPIKey=" + instance,
        { value: +obj.contract.id, type: "contracts", childObjs: 4 },
        function (contract) {
          viewProposalPDF(
            obj,
            categories,
            obj.sections,
            function (merged) {
              if (contract.status == "Signed") {
                dom.empty();

                dom.makeNode("col", "column", { width: 6, offset: 3 });

                dom.col.makeNode("cont", "container", {
                  css: "pda-container pda-Panel",
                });

                dom.col.cont.makeNode("title", "headerText", {
                  text: "Signing Complete!",
                  css: "text-center",
                });

                dom.col.cont.makeNode("break1", "lineBreak", {});

                dom.col.cont.makeNode("download", "headerText", {
                  text: "Download a copy for your records.",
                  css: "text-center",
                  size: "small",
                });

                dom.col.cont.makeNode("btns", "buttonGroup", { css: "" });
                dom.col.cont.btns
                  .makeNode("pdf", "button", {
                    text: '<i class="fa fa-download"></i> Download',
                    css: "pda-btn-blue",
                  })
                  .notify(
                    "click",
                    {
                      type: "invoicesPay-run",
                      data: {
                        run: function (contract) {
                          var dom = this;

                          sb.data.makePDF(merged, "D");
                        }.bind(dom, contract, merged),
                      },
                    },
                    sb.moduleId
                  );

                dom.patch();
              } else {
                sb.data.db.controller(
                  "updateObject&api_webform=true&pagodaAPIKey=" + instance,
                  {
                    objectType: "contracts",
                    objectData: {
                      id: contract.id,
                      status: "Signing In Progress",
                    },
                  },
                  function (statusChanged) {
                    contract.html_string_merged = merged;

                    dom.makeNode("cont", "div", { css: "" });

                    dom.cont.makeNode("btns", "div", {
                      css: "buttonGroupBar ui buttons",
                    });

                    dom.cont.btns
                      .makeNode("sign", "button", {
                        text: '<i class="fa fa-pencil"></i> Ready To Sign',
                        css: "pda-btn-green pda-btn-",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: getSignature.bind(dom, contract),
                          },
                        },
                        sb.moduleId
                      );

                    dom.cont.btns
                      .makeNode("pdf", "button", {
                        text: '<i class="fa fa-download"></i> Download PDF',
                        css: "pda-btn-blue pda-btn-",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function (contract) {
                              this.cont.btns.pdf.loading();

                              var dom = this;

                              mergeString(contract, function (merged) {
                                sb.data.makePDF(merged, "D");

                                dom.cont.btns.pdf.loading(false);
                              });
                            }.bind(dom, contract),
                          },
                        },
                        sb.moduleId
                      );

                    dom.cont
                      .makeNode("contractContainer", "div", {
                        css: "animated fadeIn",
                      })
                      .makeNode("contract", "div", {
                        css: "ui basic segment",
                        text: merged,
                        style:
                          "max-width:850px; display:block; margin:3em auto;",
                      });

                    dom.patch();
                  },
                  adminURL + "_getAdmin.php?do="
                );
              }
            },
            true
          );
        },
        adminURL + "_getAdmin.php?do="
      );
    }

    dom
      .makeNode("modals", "div", {})
      .makeNode("modal", "modal", { size: "large" });

    getConsent.call(dom);
  }

  function showInvoice(dom) {
    dom.makeNode("break", "lineBreak", {});

    dom.makeNode("text", "headerText", {
      text: "Loading the invoice",
      size: "small",
      css: "text-center",
    });
    dom.makeNode("loader", "loader", { size: "large" });

    dom.patch();

    instance = sb.data.url.getParams().i;
    contractId = sb.data.url.getParams().iid;

    appConfig.instance = instance;

    sb.data.db.controller(
      "getInstancesBy&api_webform=true&pagodaAPIKey=" + instance,
      { objectType: "instances", queryObj: { instance: instance } },
      function (instanceObj) {
        //console.log(instanceObj);
        //appConfig.systemName = instanceObj[0].systemName;

        appConfig = instanceObj[0];

        sb.data.db.controller(
          "getObjectById&api_webform=true&pagodaAPIKey=" + instance,
          { value: +contractId, type: "invoices", childObjs: 4 },
          function (invoice) {
            getInstanceImage(instanceObj[0], function (image) {
              console.log(image);
              dom.empty();

              dom.makeNode("modals", "container", {});

              dom
                .makeNode("col", "column", { width: 8, offset: 2 })
                .makeNode("cont", "container", {});

              dom.col.cont.makeNode("btnBreak1", "lineBreak", {});

              dom.col.cont.makeNode("btns", "buttonGroup", {});

              dom.col.cont.btns
                .makeNode("download", "button", {
                  text: '<i class="fa fa-download"></i> Download PDF',
                  css: "pda-btn-blue",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: viewPDF.bind(dom, invoice),
                    },
                  },
                  sb.moduleId
                );
              dom.col.cont.btns
                .makeNode("pay", "button", {
                  text: '<i class="fa fa-usd"></i> Make a payment',
                  css: "pda-btn-green",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: enterPayment.bind(dom, invoice),
                    },
                  },
                  sb.moduleId
                );

              dom.col.cont.makeNode("btnBreak", "lineBreak", {});

              dom.col.cont.makeNode("invoice", "container", {
                css: "pda-container pda-Panel pda-panel-gray pda-background-white",
              });

              dom.patch();

              buildInvoice.call(dom.col.cont.invoice, invoice, false, image);
            });
          },
          adminURL + "_getAdmin.php?do="
        );
      }
    );
  }

  function showProposal(dom, prop, key, obj, callback) {
    dom.empty();

    //dom.makeNode('propBreak', 'div', {text:'<br />'});

    dom.makeNode("loading", "div", { css: "ui active inverted dimmer" });
    dom.loading.makeNode("text", "text", {
      css: "ui text loader",
      text: "Loading proposal",
    });
    dom.makeNode("title", "div", {
      text: "Proposal: " + obj.name,
      css: "ui huge header",
    });
    dom.patch();

    sb.data.db.controller(
      "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
      {
        queryObj: {
          id: {
            type: "or",
            values: Object.keys(prop.pricing),
          },
        },
        instance: instance,
        objectType: "inventory_billable_categories",
        getChildObjs: 1,
      },
      function (categories) {
        //console.log('obj', obj);
        viewProposalPDF(
          prop,
          categories,
          prop.sections,
          function (contractHTML) {
            delete dom.loading;

            dom.makeNode("title", "div", {
              text: prop.name,
              css: "ui huge header",
            });

            dom.makeNode("btnsCont", "div", {
              css: "ui raised tertiary segment",
            });

            dom.btnsCont.makeNode("title", "div", {
              text: "What would you like to do?",
              css: "ui medium header",
            });

            dom.btnsCont.makeNode("btns", "div", { css: "three ui buttons" });

            dom.btnsCont.makeNode("notes", "div", { css: "" });

            dom.btnsCont.btns
              .makeNode("accept", "button", {
                text: '<i class="fa fa-check"></i> Accept',
                css: "pda-btn-green",
              })
              .notify(
                "click",
                {
                  type: "invoicesPay-run",
                  data: {
                    run: function () {
                      var dom = this;

                      sb.dom.alerts.ask(
                        {
                          title: "Accept or provide notes?",
                          text: "Accepting this proposal will let you move on to sign the contract and pay the invoices. Providing notes will alert the admin to come take a look. You won't be able to sign or pay until the admin reviews your notes.",
                          primaryButtonText: "Yes. Accept the proposal",
                          cancelButtonText: "No, don't accept the proposal.",
                        },
                        function (resp) {
                          if (resp) {
                            swal.disableButtons();

                            acceptProposal(obj, prop, function (done) {
                              location.reload();
                            });
                          }
                        }
                      );
                    }.bind(dom),
                  },
                },
                sb.moduleId
              );

            dom.btnsCont.btns
              .makeNode("notes", "button", {
                text: '<i class="fa fa-comment"></i> Add Notes',
                css: "pda-btn-blue",
              })
              .notify(
                "click",
                {
                  type: "invoicesPay-run",
                  data: {
                    run: function () {
                      var dom = this;

                      dom.btnsCont.notes.css("ui blue segment");

                      dom.btnsCont.notes.makeNode("title", "headerText", {
                        text: "Add your notes below.",
                        size: "small",
                      });

                      dom.btnsCont.notes.makeNode("form", "form", {
                        notes: {
                          name: "notes",
                          label: "",
                          type: "textbox",
                          rows: 5,
                        },
                      });

                      dom.btnsCont.notes.makeNode("break", "lineBreak", {
                        spaces: 1,
                      });

                      dom.btnsCont.notes
                        .makeNode("next", "button", {
                          text: 'Next Step <i class="fa fa-arrow-right"></i>',
                          css: "pda-btn-green",
                        })
                        .notify(
                          "click",
                          {
                            type: "invoicesPay-run",
                            data: {
                              run: function (prop, key, obj) {
                                var dom = this,
                                  clientNotes =
                                    dom.btnsCont.notes.form.process().fields
                                      .notes.value;

                                dom.btnsCont.notes.next.text(
                                  'Posting notes <i class="fa fa-circle-o-notch fa-spin"></i>'
                                );
                                dom.btnsCont.notes.next.css("pda-btn-primary");

                                var noteObj = {
                                  type_id: prop.contract,
                                  type: "contracts",
                                  note:
                                    "Client notes about proposal - <b>" +
                                    prop.name +
                                    "</b><br /><br />" +
                                    clientNotes,
                                  note_type: 0,
                                  author: 0,
                                  notifyUsers: [],
                                };

                                sb.data.db.controller(
                                  "createNewObject&api_webform=true&pagodaAPIKey=" +
                                    instance,
                                  { objectType: "notes", objectData: noteObj },
                                  function (newNote) {
                                    function sendManagerNotesEmail(
                                      obj,
                                      callback
                                    ) {
                                      var email = "";

                                      if (obj.manager) {
                                        if (obj.manager.related_object) {
                                          if (
                                            obj.manager.related_object.email
                                          ) {
                                            email =
                                              obj.manager.related_object.email;

                                            var emailObj = {
                                              newThread: true,
                                              to: email,
                                              from: appConfig.emailFrom,
                                              subject:
                                                obj.main_contact.fname +
                                                " " +
                                                obj.main_contact.lname +
                                                " posted notes about proposal " +
                                                prop.name,
                                              mergevars: {
                                                TITLE:
                                                  obj.main_contact.fname +
                                                  " " +
                                                  obj.main_contact.lname +
                                                  " posted notes about proposal " +
                                                  prop.name,
                                                BODY:
                                                  "Notes received from " +
                                                  obj.main_contact.fname +
                                                  " " +
                                                  obj.main_contact.lname +
                                                  " about proposal " +
                                                  prop.name +
                                                  ".<br /><br /><br /><br />" +
                                                  clientNotes +
                                                  '<br /><br /><a href="https://bento.infinityhospitality.net/app/' +
                                                  instance +
                                                  "/#workorders&page=workorders&view=table&single=" +
                                                  obj.id +
                                                  '">VIEW WORK ORDER</a>',
                                                BUTTON: "",
                                              },
                                              emailtags: [
                                                "Proposal client notes",
                                              ],
                                              type: "work_orders",
                                              typeId: obj.id,
                                            };

                                            sb.data.db.controller(
                                              "sendEmail&api_webform=true&pagodaAPIKey=" +
                                                instance,
                                              emailObj,
                                              function (response) {
                                                callback(true);
                                              },
                                              adminURL + "_getAdmin.php?do="
                                            );
                                          }
                                        }
                                      }

                                      callback(true);
                                    }

                                    sendManagerNotesEmail(obj, function (done) {
                                      dom.btnsCont.notes.empty();

                                      dom.btnsCont.notes.patch();

                                      dom.btnsCont.notes.css("");

                                      sb.dom.alerts.alert(
                                        "Got it!",
                                        "Your notes have been sent to the admin for review.",
                                        "success"
                                      );
                                    });
                                  }
                                );
                              }.bind(dom, prop, key, obj),
                            },
                          },
                          sb.moduleId
                        );

                      //dom.btnsCont.notes.makeNode('break', 'lineBreak', {spaces:2});

                      dom.btnsCont.notes.patch();
                    }.bind(dom),
                  },
                },
                sb.moduleId
              );

            /*
					dom.btnsCont.btns.makeNode('decline', 'button', {text:'<i class="fa fa-times"></i> Decline', css:'pda-btn-red'})
						.notify('click', {
							type:'invoicesPay-run',
							data:{
								run:function(){
									
									var dom = this;
									
									dom.btnsCont.notes.css('ui red segment');
									
									dom.btnsCont.notes.makeNode('title', 'headerText', {text:'Add your notes below.', size:'small'});
									
									dom.btnsCont.notes.makeNode('form', 'form', {
										notes:{
											name:'notes',
											label:'',
											type:'textbox',
											value:'Declined: ',
											rows:5
										}
									});
									
									dom.btnsCont.notes.makeNode('break', 'lineBreak', {spaces:1});
									
									dom.btnsCont.notes.makeNode('next', 'button', {text:'Next Step <i class="fa fa-arrow-right"></i>', css:'pda-btn-green'})
										.notify('click', {
											type:'invoicesPay-run',
											data:{
												run:function(prop, key, obj){
													
													var dom = this,
														clientNotes = dom.btnsCont.notes.form.process().fields.notes.value;
													
													dom.btnsCont.notes.next.text('Posting notes <i class="fa fa-circle-o-notch fa-spin"></i>');
													dom.btnsCont.notes.next.css('pda-btn-primary');
																						
													var noteObj = {
															type_id: prop.contract,
															type: 'contracts',
															note: 'Client notes about proposal - <b>'+ prop.name +'</b><br /><br /><b>DECLINED</b><br />'+ clientNotes,
															note_type:0,
															author: 0,
															notifyUsers: []
														};
													
													sb.data.db.controller('updateObject&pagodaAPIKey='+ instance, {
														objectType:'proposals',
														objectData:{
															id:prop.id,
															status:'Declined'
														}
													}, function(updated){
														
														sb.data.db.controller('createNewObject&api_webform=true&pagodaAPIKey='+ instance, {objectType:'notes', objectData:noteObj}, function(newNote){
														
															function sendManagerNotesEmail(obj, callback){
																
																var email = '';
																
																if(obj.manager){
																	if(obj.manager.related_object){
																		if(obj.manager.related_object.email){
																			
																			email = obj.manager.related_object.email;
																			
																			var emailObj = {
																					newThread:true,
																					to:email,
																					from: appConfig.emailFrom,
																					subject: obj.main_contact.fname +' '+ obj.main_contact.lname +' posted notes about proposal '+ prop.name,
																					mergevars: {
																						TITLE: obj.main_contact.fname +' '+ obj.main_contact.lname +' posted notes about proposal '+ prop.name,
																						BODY: 'Notes received from '+ obj.main_contact.fname +' '+ obj.main_contact.lname +' about proposal '+ prop.name +'.<br /><br /><b>DECLINED</b><br /><br />'+ clientNotes +'<br /><br /><a href="https://bento.infinityhospitality.net/app/'+ instance +'/#workorders&page=workorders&view=table&single='+ obj.id +'">VIEW WORK ORDER</a>',
																						BUTTON: ''
																					}, emailtags: [
																						'Proposal client notes'
																					],
																					type: 'work_orders',
																					typeId: obj.id,
																				};
																			
																			sb.data.db.controller('sendEmail&api_webform=true&pagodaAPIKey='+instance, emailObj, function(response){
																				
																				callback(true);
																				
																			}, adminURL+'_getAdmin.php?do=');
																																									
																		}
																	}
																}
																
																callback(true);
																
															}
															
															sendManagerNotesEmail(obj, function(done){
																
																dom.btnsCont.notes.empty();
															
																dom.btnsCont.notes.patch();
																
																dom.btnsCont.notes.css('');
																
																sb.dom.alerts.alert('Got it!', 'Your notes have been sent to the admin for review.', 'success');
																
															});
														
														});
														
													});
														
													
													
												}.bind(dom, prop, key, obj)
											}
										}, sb.moduleId);
									
									dom.btnsCont.notes.patch();
									
								}.bind(dom)
							}
						}, sb.moduleId);
*/

            dom.btnsCont.btns
              .makeNode("download", "button", {
                text: '<i class="fa fa-download"></i> Download A PDF',
                css: "pda-btn-blue",
              })
              .notify(
                "click",
                {
                  type: "invoicesPay-run",
                  data: {
                    run: function () {
                      var dom = this;

                      dom.btnsCont.btns.download.text(
                        '<i class="fa fa-circle-o-notch fa-spin"></i> Preparing PDF...'
                      );

                      viewProposalPDF(
                        prop,
                        categories,
                        prop.sections,
                        function (done) {
                          dom.btnsCont.btns.download.text(
                            '<i class="fa fa-download"></i> Download PDF'
                          );
                        }
                      );
                    }.bind(dom),
                  },
                },
                sb.moduleId
              );

            dom.makeNode("cont", "div", { css: "ui raised basic segment" });
            dom.cont.makeNode("text", "text", { text: contractHTML });

            dom.patch();

            callback(true);
          },
          true
        );
      }
    );
  }

  function showWorkorder(dom, workorder, callback) {
    //dom.makeNode('break', 'div', {text:'<br /><br />'});

    dom.makeNode("loading", "div", {});
    dom.loading.makeNode("text", "text", {
      text: "Loading...",
      css: "text-center",
    });
    dom.loading.makeNode("loader", "loader", {});

    dom.patch();

    sb.data.db.controller(
      "getObjectById&api_webform=true&pagodaAPIKey=" + instance,
      {
        value: +workorder,
        type: "work_orders",
        childObjs: 3,
      },
      function (obj) {
        delete dom.loading;

        dom.makeNode("mainBreak", "div", { text: "<br />" });
        dom
          .makeNode("cont", "div", {
            css: "ui basic segment",
            style: "margin:0 auto; max-width:1150px;",
          })
          .makeNode("steps", "div", { css: "ui three fluid ordered steps" });
        dom.makeNode("nav", "div", { css: "ui grid" });

        switch (obj.status) {
          case "Accepted":
            dom.cont.steps.makeNode("step1", "div", { css: "completed step" });
            dom.cont.steps.step1.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step1.content.makeNode("title", "div", {
              css: "title",
              text: "Review proposals",
            });
            dom.cont.steps.step1.content.makeNode("desc", "div", {
              css: "description",
              text: "Review and approve your proposals.",
            });

            dom.cont.steps.makeNode("step2", "div", { css: "active step" });
            dom.cont.steps.step2.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step2.content.makeNode("title", "div", {
              css: "title",
              text: "Sign contract",
            });
            dom.cont.steps.step2.content.makeNode("desc", "div", {
              css: "description",
              text: "Sign the contract for the approved proposal.",
            });

            dom.cont.steps.makeNode("step3", "div", { css: "step" });
            dom.cont.steps.step3.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step3.content.makeNode("title", "div", {
              css: "title",
              text: "Pay invoices",
            });
            dom.cont.steps.step3.content.makeNode("desc", "div", {
              css: "description",
              text: "Select and pay invoices.",
            });

            dom.patch();

            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  main_object: obj.id,
                  status: "Accepted",
                },
                instance: instance,
                objectType: "proposals",
                getChildObjs: 1,
              },
              function (accepted) {
                if (accepted.length > 0) {
                  obj.pricing = accepted[0].pricing;
                  obj.sections = accepted[0].sections;
                  obj.menu = accepted[0].menu.id;
                  obj.invoices = accepted[0].invoices;
                  obj.contract = accepted[0].contract;

                  sb.data.db.controller(
                    "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                    {
                      queryObj: {
                        id: {
                          type: "or",
                          values: Object.keys(obj.pricing),
                        },
                      },
                      instance: instance,
                      objectType: "inventory_billable_categories",
                      getChildObjs: 1,
                    },
                    function (categories) {
                      signContractView(dom, obj, categories);
                    }
                  );
                } else {
                  dom.cont.steps.makeNode("step1", "div", {
                    css: "completed step",
                  });
                  dom.cont.steps.step1.makeNode("content", "div", {
                    css: "content",
                  });
                  dom.cont.steps.step1.content.makeNode("title", "div", {
                    css: "title",
                    text: "Review proposals",
                  });
                  dom.cont.steps.step1.content.makeNode("desc", "div", {
                    css: "description",
                    text: "Review and approve your proposals.",
                  });

                  dom.cont.steps.makeNode("step2", "div", {
                    css: "completed step",
                  });
                  dom.cont.steps.step2.makeNode("content", "div", {
                    css: "content",
                  });
                  dom.cont.steps.step2.content.makeNode("title", "div", {
                    css: "title",
                    text: "Sign contract",
                  });
                  dom.cont.steps.step2.content.makeNode("desc", "div", {
                    css: "description",
                    text: "Sign the contract for the approved proposal.",
                  });

                  dom.cont.steps.makeNode("step3", "div", {
                    css: "active step",
                  });
                  dom.cont.steps.step3.makeNode("content", "div", {
                    css: "content",
                  });
                  dom.cont.steps.step3.content.makeNode("title", "div", {
                    css: "title",
                    text: "Pay invoices",
                  });
                  dom.cont.steps.step3.content.makeNode("desc", "div", {
                    css: "description",
                    text: "Select and pay invoices.",
                  });

                  dom.makeNode("contBreak", "div", {
                    text: "<br /><br /><br />",
                  });

                  dom.makeNode("grid", "div", {
                    css: "ui centered stackable grid container",
                  });
                  dom.grid
                    .makeNode("propCol", "div", { css: "four wide column" })
                    .makeNode("cont", "div", {
                      css: "ui active loading segment",
                    });
                  dom.grid
                    .makeNode("invoiceCol", "div", {
                      css: "twelve wide column",
                    })
                    .makeNode("cont", "div", {
                      css: "ui active loading segment",
                    });

                  dom.grid.propCol.cont.makeNode("title", "div", {
                    css: "ui header",
                    text: "Proposals",
                  });
                  dom.grid.propCol.cont.makeNode("titleBreak", "div", {
                    text: "<br />",
                  });
                  dom.grid.invoiceCol.cont.makeNode("title", "div", {
                    css: "ui header",
                    text: "Inoivce List",
                  });

                  dom.patch();

                  sb.data.db.controller(
                    "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                    {
                      queryObj: {
                        main_object: obj.id,
                        status: "Signed",
                      },
                      instance: instance,
                      objectType: "proposals",
                      getChildObjs: 1,
                    },
                    function (accepted) {
                      //console.log('accepted',accepted);
                      sb.data.db.controller(
                        "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                          instance,
                        {
                          queryObj: {
                            main_object: obj.id,
                            status: "Signed",
                          },
                          instance: instance,
                          objectType: "proposals",
                          getChildObjs: 1,
                        },
                        function (accepted) {
                          obj.proposal = accepted[0];

                          invoiceView(
                            dom.grid.invoiceCol.cont,
                            obj,
                            function (done) {
                              dom.grid.invoiceCol.cont.css("ui basic segment");
                            }
                          );

                          sb.data.db.controller(
                            "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                              instance,
                            {
                              queryObj: {
                                main_object: obj.id,
                                status: "Approved",
                              },
                              instance: instance,
                              objectType: "proposals",
                              getChildObjs: 1,
                            },
                            function (approved) {
                              var proposals = accepted.concat(approved);

                              smallProposalList(
                                dom.grid.propCol.cont,
                                proposals,
                                function (done) {
                                  dom.grid.propCol.cont.css("ui basic segment");
                                }
                              );
                            }
                          );
                        }
                      );
                    }
                  );
                }
              }
            );

            break;

          case "Paid":
            dom.nav
              .makeNode("col1", "column", { width: 4, css: "pda-center" })
              .makeNode("text", "button", {
                text: '1. Review Proposals <i class="fa fa-check"></i>',
                css: "pda-center pda-color-green pda-transparent pda-btn-large",
              });

            dom.nav
              .makeNode("col2", "column", { width: 4, css: "pda-center" })
              .makeNode("text", "button", {
                text: '2. Sign Contract <i class="fa fa-check"></i>',
                css: "pda-center pda-color-green pda-transparent pda-btn-large",
              });

            dom.nav
              .makeNode("col3", "column", { width: 4, css: "pda-center" })
              .makeNode("text", "button", {
                text: '3. Pay Invoices <i class="fa fa-check"></i>',
                css: "pda-center pda-color-green pda-transparent pda-btn-large",
              });

            dom.nav.makeNode("navBreak", "lineBreak", { spaces: 1 });

            dom.makeNode("break", "lineBreak", {});

            dom.makeNode("complete", "headerText", {
              text: "This proposal is complete.",
              css: "text-center",
            });

            dom.makeNode("contractBreak", "lineBreak", {});

            dom
              .makeNode("contract", "container", {
                css: "pda-container pda-Panel pda-panel-gray",
              })
              .makeNode("cont", "container", { css: "pda-container" });

            dom.contract.cont.makeNode("loading", "container", {});
            dom.contract.cont.loading.makeNode("text", "text", {
              text: "Loading completed proposal...",
              css: "text-center",
            });
            dom.contract.cont.loading.makeNode("loader", "loader", {});

            dom.patch();

            obj.pricing = _.find(obj.history, {
              contract: obj.contract.id.toString(),
            }).pricing;
            obj.sections = _.find(obj.history, {
              contract: obj.contract.id.toString(),
            }).sections;
            obj.menu = +_.find(obj.history, {
              contract: obj.contract.id.toString(),
            }).menu;
            obj.invoices = _.find(obj.history, {
              contract: obj.contract.id.toString(),
            }).invoices;

            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  id: {
                    type: "or",
                    values: Object.keys(obj.pricing),
                  },
                },
                instance: instance,
                objectType: "inventory_billable_categories",
                getChildObjs: 1,
              },
              function (categories) {
                obj.contract = obj.contract.id;

                viewProposalPDF(
                  obj,
                  categories,
                  obj.sections,
                  function (contractHtml) {
                    delete dom.contract.cont.loading;

                    dom.contract.cont.makeNode("btns", "buttonGroup", {
                      css: "pull-left",
                    });

                    dom.contract.cont.btns
                      .makeNode("download", "button", {
                        text: '<i class="fa fa-download"></i> Download a copy',
                        css: "pda-btn-primary",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function () {
                              var dom = this;

                              dom.contract.cont.btns.download.loading();

                              viewProposalPDF(
                                obj,
                                categories,
                                obj.sections,
                                function (done) {
                                  dom.contract.cont.btns.download.loading(
                                    false
                                  );
                                }
                              );
                            }.bind(dom),
                          },
                        },
                        sb.moduleId
                      );

                    dom.contract.cont.makeNode("btnBreak", "lineBreak", {});

                    dom.contract.cont.makeNode("text", "text", {
                      text: contractHtml,
                    });

                    dom.contract.cont.patch();
                  },
                  true
                );
              }
            );

            break;

          case "Signed":
            dom.cont.steps.makeNode("step1", "div", { css: "completed step" });
            dom.cont.steps.step1.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step1.content.makeNode("title", "div", {
              css: "title",
              text: "Review proposals",
            });
            dom.cont.steps.step1.content.makeNode("desc", "div", {
              css: "description",
              text: "Review and approve your proposals.",
            });

            dom.cont.steps.makeNode("step2", "div", { css: "completed step" });
            dom.cont.steps.step2.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step2.content.makeNode("title", "div", {
              css: "title",
              text: "Sign contract",
            });
            dom.cont.steps.step2.content.makeNode("desc", "div", {
              css: "description",
              text: "Sign the contract for the approved proposal.",
            });

            dom.cont.steps.makeNode("step3", "div", { css: "active step" });
            dom.cont.steps.step3.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step3.content.makeNode("title", "div", {
              css: "title",
              text: "Pay invoices",
            });
            dom.cont.steps.step3.content.makeNode("desc", "div", {
              css: "description",
              text: "Select and pay invoices.",
            });

            dom.makeNode("contBreak", "div", { text: "<br /><br />" });

            dom.makeNode("grid", "div", {
              css: "ui centered stackable grid container",
            });
            dom.grid
              .makeNode("propCol", "div", { css: "four wide column" })
              .makeNode("cont", "div", { css: "ui active loading segment" });
            dom.grid
              .makeNode("invoiceCol", "div", { css: "twelve wide column" })
              .makeNode("cont", "div", { css: "ui active loading segment" });

            dom.grid.propCol.cont.makeNode("title", "div", {
              css: "ui header",
              text: "Proposals",
            });
            dom.grid.propCol.cont.makeNode("titleBreak", "div", {
              text: "<br />",
            });
            dom.grid.invoiceCol.cont.makeNode("title", "div", {
              css: "ui header",
              text: "Inoivce List",
            });

            dom.patch();

            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  main_object: obj.id,
                  status: "Accepted",
                },
                instance: instance,
                objectType: "proposals",
                getChildObjs: 1,
              },
              function (accepted) {
                obj.proposal = accepted[0];

                invoiceView(dom.grid.invoiceCol.cont, obj, function (done) {
                  dom.grid.invoiceCol.cont.css("ui basic segment");
                });

                sb.data.db.controller(
                  "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                  {
                    queryObj: {
                      main_object: obj.id,
                      status: "Approved",
                    },
                    instance: instance,
                    objectType: "proposals",
                    getChildObjs: 1,
                  },
                  function (approved) {
                    var proposals = accepted.concat(approved);

                    smallProposalList(
                      dom.grid.propCol.cont,
                      proposals,
                      function (done) {
                        dom.grid.propCol.cont.css("ui basic segment");
                      }
                    );
                  }
                );
              }
            );

            break;

          default:
            dom.cont.steps.makeNode("step1", "div", { css: "active step" });
            dom.cont.steps.step1.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step1.content.makeNode("title", "div", {
              css: "title",
              text: "Review proposals",
            });
            dom.cont.steps.step1.content.makeNode("desc", "div", {
              css: "description",
              text: "Review and approve your proposals.",
            });

            dom.cont.steps.makeNode("step2", "div", { css: "step" });
            dom.cont.steps.step2.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step2.content.makeNode("title", "div", {
              css: "title",
              text: "Sign contract",
            });
            dom.cont.steps.step2.content.makeNode("desc", "div", {
              css: "description",
              text: "Sign the contract for the approved proposal.",
            });

            dom.cont.steps.makeNode("step3", "div", { css: "step" });
            dom.cont.steps.step3.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step3.content.makeNode("title", "div", {
              css: "title",
              text: "Pay invoices",
            });
            dom.cont.steps.step3.content.makeNode("desc", "div", {
              css: "description",
              text: "Select and pay invoices.",
            });

            dom.nav.makeNode("navBreak", "div", { text: "<br />" });

            proposalView(dom, obj);
        }

        callback(true);
      }
    );
  }

  function smallProposalList(dom, objs, callback) {
    dom.makeNode("cards", "div", { css: "ui cards" });

    _.each(_.sortBy(objs, "date_created").reverse(), function (obj) {
      var labelColor = "";
      switch (obj.status) {
        case "Accepted":
        case "Approved":
          labelColor = "basic green";
          break;

        case "Signed":
          labelColor = "green";

          dom
            .makeNode("card" + obj.id, "div", { css: "ui card" })
            .makeNode("content", "div", { css: "content" });

          dom["card" + obj.id].content.makeNode("name", "div", {
            css: "header",
            text: obj.name,
          });
          dom["card" + obj.id].content.makeNode("created", "div", {
            css: "description",
            text: "Created on " + moment(obj.date_created).format("M/D/YYYY"),
          });
          dom["card" + obj.id].content.makeNode("price", "div", {
            css: "description",
            text:
              "Price: $" +
              (
                _.reduce(
                  obj.pricing,
                  function (memo, price, cat) {
                    return memo + +price;
                  },
                  0
                ) / 100
              ).formatMoney(),
          });
          dom["card" + obj.id].content.makeNode("state", "div", {
            css: "ui " + labelColor + " fluid center aligned label",
            text: obj.status,
          });
          dom["card" + obj.id]
            .makeNode("bottom", "div", {
              css: "ui bottom attached button",
              text: "View PDF",
            })
            .notify(
              "click",
              {
                type: "invoicesPay-run",
                data: {
                  run: function (dom, obj) {
                    dom["card" + obj.id].bottom.loading();

                    sb.data.db.controller(
                      "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                        instance,
                      {
                        queryObj: {
                          id: {
                            type: "or",
                            values: Object.keys(obj.pricing),
                          },
                        },
                        instance: instance,
                        objectType: "inventory_billable_categories",
                        getChildObjs: 1,
                      },
                      function (categories) {
                        viewProposalPDF(
                          obj,
                          categories,
                          obj.sections,
                          function (done) {
                            dom["card" + obj.id].bottom.loading(false);
                          }
                        );
                      },
                      adminURL + "_getAdmin.php?do="
                    );
                  }.bind({}, dom, obj),
                },
              },
              sb.moduleId
            );

          break;
      }
    });

    dom.patch();

    callback(true);
  }

  function viewProposalPDF(obj, categories, sections, callback, htmlOnly) {
    function buildMenuString(menuObj, htmlString) {
      if (!htmlString) {
        htmlString = "";
      }

      //htmlString += '<br />';

      var total = 0,
        sectionTotal = 0;

      if (menuObj) {
        _.each(menuObj.sections, function (section) {
          htmlString +=
            '<p style="font-size:18px"><b>' + section.name + "</b></p>";

          htmlString +=
            '<table style="width:100%; border-collapse: collapse;">';
          htmlString +=
            '<tr style="border:1px solid gray; font-size:12px; font-weight:bold;"> <td style="padding:5px;">ITEM</td> <td style="padding:5px; text-align:right;">QUANTITY</td> <td style="padding:5px; text-align:right;">UNIT PRICE</td> <td style="padding:5px; text-align:right;">TOTAL</td> </tr>';

          sectionTotal = 0;

          _.each(section.items, function (item) {
            htmlString +=
              '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
              item.item.name +
              '</td> <td style="padding:5px; text-align:right;">' +
              item.qty +
              '</td> <td style="padding:5px; text-align:right;">$' +
              (item.item.price / 100).formatMoney() +
              '</td> <td style="padding:5px; text-align:right;">$' +
              ((item.item.price * item.qty) / 100).formatMoney() +
              "</td> </tr>";

            total += item.item.price * item.qty;
            sectionTotal += item.item.price * item.qty;
          });

          htmlString +=
            '<tr style="border:1px solid gray;"> <td style="padding:5px;"></td> <td style="padding:5px;"></td> <td style="padding:5px; font-weight:bold; text-align:right;">' +
            section.name +
            ' Total</td> <td style="padding:5px; text-align:right;">$' +
            (sectionTotal / 100).formatMoney() +
            "</td> </tr>";

          htmlString += "</table><br />";
        });

        htmlString += "<br />";

        htmlString += '<table style="width:100%;">';

        htmlString +=
          '<tr style=""> <td style="padding:5px; text-align:right; font-size:18px;"><b>Total $' +
          (total / 100).formatMoney() +
          "</b></td> </tr>";

        htmlString += "</table>";
      }

      return htmlString;
    }

    var fullHTMLString = "",
      totalPrice = 0;
    var contractId;
    var htmlString = "";
    if (obj.contract) {
      contractId = +obj.contract.id;
    }

    sb.data.db.controller(
      "getObjectById&api_webform=true&pagodaAPIKey=" + instance,
      {
        value: contractId,
        type: "contracts",
      },
      function (contract) {
        if (contract) {
          htmlString = contract.html_string;
        }

        sb.data.db.controller(
          "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
          {
            queryObj: {
              id: {
                type: "or",
                values: _.map(
                  _.pluck(obj.invoices, "id"),
                  function (inv) {
                    return +inv;
                  },
                  []
                ),
              },
            },
            instance: instance,
            objectType: "invoices",
            getChildObjs: 1,
          },
          function (invoices) {
            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  id: +obj.menu.id,
                },
                instance: instance,
                objectType: "inventory_menu",
                getChildObjs: 1,
              },
              function (menus) {
                //console.log('sections',sections);

                if (sections.contract == "Yes" && contract) {
                  //fullHTMLString += contractHTML;
                  fullHTMLString += htmlString;
                  fullHTMLString += "<br /><br /><br />";
                  //fullHTMLString += '<pagebreak />';
                }

                if (sections.itemList == "Yes") {
                  if (sections.contract == "Yes") {
                    fullHTMLString += "<pagebreak />";
                  }

                  //fullHTMLString += '<h3>Billable Items</h3>';
                  fullHTMLString += buildMenuString(menus[0]);
                  fullHTMLString += "<br />";
                  fullHTMLString += "{{PLEASE SIGN HERE}}";
                  fullHTMLString += "<br /><br />";
                }

                if (sections.pricingTable == "Yes") {
                  if (sections.itemList == "Yes") {
                    fullHTMLString += "<pagebreak />";
                  }

                  fullHTMLString += "<h3>Pricing</h3>";

                  fullHTMLString +=
                    '<table style="width:100%; border-collapse: collapse;">';

                  if (categories.length > 0) {
                    _.each(obj.pricing, function (price, cat) {
                      fullHTMLString +=
                        '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
                        _.find(categories, { id: +cat }).name +
                        '</td> <td style="padding:5px;">$' +
                        (price / 100).formatMoney() +
                        "</td> </tr>";

                      totalPrice += +price;
                    });
                  }

                  fullHTMLString +=
                    '<tr style="border:1px solid gray; font-weight:bold;"> <td style="padding:5px; font-weight:bold; font-size:18px;">TOTAL</td> <td style="padding:5px; font-weight:bold; font-size:16px;">$' +
                    (totalPrice / 100).formatMoney() +
                    "</td> </tr>";

                  fullHTMLString += "</table>";

                  fullHTMLString += "<br /><br />";
                  fullHTMLString += "{{PLEASE SIGN HERE}}";
                  fullHTMLString += "<br /><br /><br />";
                }

                if (sections.invoiceTable == "Yes") {
                  if (sections.pricingTable == "Yes") {
                    fullHTMLString += "<pagebreak />";
                  }

                  fullHTMLString += "<h3>Invoices</h3>";

                  fullHTMLString +=
                    '<table style="width:100%; border-collapse: collapse;">';

                  fullHTMLString +=
                    '<tr style="border:1px solid gray; font-size:12px; font-weight:bold;"> <td style="padding:5px;">INVOICE NAME</td> <td style="padding:5px; text-align:right;">DUE DATE</td> <td style="padding:5px; text-align:right;">BALANCE DUE</td> </tr>';

                  _.each(_.sortBy(invoices, "due_date"), function (inv) {
                    fullHTMLString +=
                      '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
                      inv.name +
                      '</td> <td style="padding:5px; text-align:right;">' +
                      moment(inv.due_date).format("M/D/YYYY") +
                      '</td> <td style="padding:5px; text-align:right;">$' +
                      (inv.balance / 100).formatMoney() +
                      "</td> </tr>";
                  });

                  fullHTMLString += "</table>";

                  fullHTMLString += "<br /><br />";
                  fullHTMLString += "{{PLEASE SIGN HERE}}";
                  fullHTMLString += "<br /><br /><br />";
                }

                fullHTMLString += "<br />";

                if (!contract) {
                  contract = {
                    html_string: "",
                  };
                }

                contract.html_string = fullHTMLString;
                obj.html_string = fullHTMLString;
                //console.log('contract obj', obj);
                createMergedHTML(
                  obj,
                  obj.object_bp_type,
                  function (contractHTML) {
                    if (htmlOnly === true) {
                      if (callback) {
                        callback(contractHTML);
                      }
                    } else {
                      sb.data.makePDF(contractHTML, "I");

                      if (callback) {
                        callback(contractHTML);
                      }
                    }
                  }
                );
              },
              adminURL + "_getAdmin.php?do="
            );
          },
          adminURL + "_getAdmin.php?do="
        );
      },
      adminURL + "_getAdmin.php?do="
    );
  }

  function viewPDF(obj) {
    sb.data.makePDF($(this.col.cont.invoice.selector).html(), "I");

    /*
		var dom = this;
		
		dom.col.cont.btns.download.loading();
		
		sb.data.db.obj.getById('invoices', obj.id, function(contract){
			
			dom.col.cont.btns.download.loading(false);
				
			sb.data.makePDF($(dom.col.cont.invoice.selector).html(), 'I');
			
		});	
*/
  }

  return {
    init: function () {
      instance = sb.data.url.getParams().i;
      workorder = sb.data.url.getParams().wid;
      appConfig.instance = instance;

      sb.listen({
        "invoicesPay-run": this.run,
        "client-invoice-payment-completed": this.paymentCompleted,
      });

      dom = sb.dom.make(".mainCanvas");

      dom
        .makeNode("cont", "div", { css: "ui" })
        .makeNode("cont", "div", { css: "" });

      dom.cont.cont.makeNode("modals", "div", {});

      dom.build();

      showWorkorder(dom.cont.cont, workorder, function () {});
    },

    paymentCompleted: function (data) {
      data[0].action(function (dom, obj, selectedInvoices) {
        var updates = [];

        _.each(data, function (datum) {
          var inv = _.findWhere(selectedInvoices, { id: datum.invoiceId }),
            totalPaid = datum.payment.amount;

          if (!inv.payments) {
            inv.payments = [];
          }

          if (!inv.payments.length) {
            inv.payments = [];
          }

          inv.payments.push(datum.payment.id);

          var newBalance = inv.balance - totalPaid;

          var newMemo =
            inv.memo +
            "<br />Payment for $" +
            (datum.payment.amount / 100).formatMoney(2) +
            " was made on " +
            moment().format("M/D/YYYY @ h:mm a") +
            ".";

          updates.push({
            id: inv.id,
            object_bp_type: "invoices",
            payments: inv.payments,
            paid: totalPaid,
            balance: newBalance,
            memo: newMemo,
            childObjs: 4,
          });
        });

        sb.data.db.controller(
          "updateObject&api_webform=true&pagodaAPIKey=invoices",
          { objectType: "invoices", objectData: updates },
          function (updatedInv) {
            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  related_object: obj.id,
                  active: "Yes",
                },
                instance: instance,
                objectType: "invoices",
              },
              function (invoicesList) {
                var workOrderBalance = _.chain(invoicesList)
                  .pluck("balance")
                  .reduce(function (memo, num) {
                    return memo + +num;
                  })
                  .value();

                var notesToCreate = [];

                _.each(updates, function (inv) {
                  notesToCreate.push({
                    type_id: inv.id,
                    type: "invoices",
                    note: inv.memo,
                    note_type: 0,
                    author: 0,
                    notifyUsers: [],
                  });
                });

                sb.data.db.controller(
                  "createNewObject&api_webform=true&pagodaAPIKey=" +
                    appConfig.instance,
                  { objectType: "notes", objectData: notesToCreate },
                  function (newNote) {
                    var objectId = obj.main_contact.id,
                      emailObjectType = "contacts",
                      emailAddress = "";

                    if (obj.main_contact) {
                      if (obj.main_contact.contact_info) {
                        var count = 0;
                        _.each(obj.main_contact.contact_info, function (i) {
                          if (
                            i.type.data_type == "email" &&
                            i.is_primary == "yes"
                          ) {
                            if (count > 0) {
                              emailAddress += ", " + i.info;
                            } else {
                              emailAddress += i.info;
                            }

                            count++;
                          }
                        });
                      }
                    }

                    var emailObj = {
                      newThread: true,
                      to: emailAddress,
                      from: appConfig.emailFrom,
                      subject: appConfig.systemName + " payment receipt",
                      mergevars: {
                        TITLE: appConfig.systemName + " payment receipt",
                        BODY:
                          "Thank you for your payment of $" +
                          (
                            _.chain(selectedInvoices)
                              .pluck("balance")
                              .reduce(function (memo, num) {
                                return memo + +num;
                              }) / 100
                          ).formatMoney(2) +
                          '<br /><br />Please click the link below to view and download a copy of the invoice for your records.<br /><br /><a href="https://bento.infinityhospitality.net/app/invoices/#?&i=' +
                          appConfig.instance +
                          "&wid=" +
                          obj.id +
                          '" target="_blank">https://bento.infinityhospitality.net/app/workorders/#?&i=' +
                          appConfig.instance +
                          "&wid=" +
                          obj.id +
                          "</a>",
                        BUTTON: "",
                      },
                      emailtags: ["Invoice Payment Receipt"],
                      type: emailObjectType,
                      typeId: objectId,
                    };

                    sb.data.db.controller(
                      "sendEmail&api_webform=true&pagodaAPIKey=" + instance,
                      emailObj,
                      function (response) {
                        if (workOrderBalance == 0) {
                          var workOrder = {
                            id: obj.id,
                            status: "Paid",
                          };

                          sb.data.db.controller(
                            "updateObject&api_webform=true&pagodaAPIKey=work_orders",
                            {
                              objectType: "work_orders",
                              objectData: workOrder,
                            },
                            function (updatedWorkOrder) {
                              dom.empty();

                              showWorkorder(dom, obj.id, function () {
                                sb.dom.alerts.alert(
                                  "Success",
                                  "Payment successful!",
                                  "success"
                                );
                              });
                            },
                            adminURL + "_getAdmin.php?do="
                          );
                        } else {
                          showWorkorder(dom, obj.id, function () {});

                          sb.dom.alerts.alert(
                            "Success",
                            "Payment successful!",
                            "success"
                          );
                        }
                      },
                      adminURL + "_getAdmin.php?do="
                    );
                  },
                  adminURL + "_getAdmin.php?do="
                );
              },
              adminURL + "_getAdmin.php?do="
            );
          },
          adminURL + "_getAdmin.php?do="
        );
      });
    },

    run: function (data) {
      data.run();
    },
  };
});
