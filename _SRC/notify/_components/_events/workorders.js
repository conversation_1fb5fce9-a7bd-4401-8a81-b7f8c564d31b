Factory.register('workorders', function(sb){
	
	var ui = {},
		tableUI = {},
		components = {},
		objectId = 0,
		workorderPrice = [],
		staffingPrice = [],
		defaultDisclaimer = 'The parties agree that this agreement may be electronically signed. The parties agree that the electronic signatures appearing on this agreement are the same as handwritten signatures for the purposes of validity, enforceability and admissibility.<br /><br />You may withdraw your consent to receive electronic documents, notices or disclosures at any time. In order to withdraw consent, you must notify the sending party that you wish to withdraw consent and request that your future documents, notices and disclosures be provided in paper format. To request paper copies of documents; withdraw consent to conduct business electronically and receive documents, notices or disclosures electronically; or withdraw consent to sign documents electronically, please contact the sending party by telephone, postal mail or email.',
// 		defaultOppNoteText = 'Here is your new Opportunity.<br /><span style="font-size:14px">You can use this area to type your notes and help you stay organized.</span><br /><br />1. How did you meet this contact?<br /><br /><br />2. What problems is this client having?<br /><br /><br />3. Who should be included in this deal?<br /><span style="font-size:14px; color:gray; background-color:lightgray;"><i> (@Team Member1, @Team Member2) </i></span>',
// 		defaultOppNoteText = '<p></p><h3 style="text-align: center; "><b>New Opportunity</b></h3><span style="font-size:14px"><div style="text-align: center;">You can use this area to type your notes and help you stay organized.</div><div style="text-align: center;"><br></div><div style="text-align: left;"><br></div></span><br><h4>1. How did you meet this contact?</h4><br><br><h4>2. What problems is this client having?</h4><br><br><h4>3. Who should be included in this deal?</h4><h6><i>(you can @mention team members to notify them of this work order via email)</i></h6><p></p>',
// 		defaultOppNoteText = '<p></p><h3 style="text-align: center; "><b>New Opportunity</b></h3><span style="font-size:14px"><div style="text-align: center;">You can use this area to type your notes and help you stay organized.</div><div style="text-align: center;"><br></div><div style="text-align: left;"><br></div></span><br><h4>1. How did you meet this contact?</h4><p open="" sans",="" arial,="" sans-serif;="" font-size:="" 14px;"="" style="margin-top: 0px; margin-bottom: 15px; color: rgb(51, 51, 51); font-size: 14px; padding: 0px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis at quam non ligula condimentum euismod. Morbi sagittis vehicula malesuada. Pellentesque in libero tortor. Sed luctus leo porta risus cursus, tempor blandit quam tincidunt. In egestas turpis libero, vitae facilisis ante varius id. Phasellus in ex blandit, feugiat quam ut, molestie risus. Maecenas auctor ipsum vitae semper finibus. Nullam eget mauris imperdiet, venenatis erat vel, scelerisque turpis. Aliquam convallis luctus fermentum. Lorem ipsum dolor sit amet, consectetur adipiscing elit. In et venenatis nulla, in consequat tellus. Nulla nec congue est.</p><p open="" sans",="" arial,="" sans-serif;="" font-size:="" 14px;"="" style="margin-top: 0px; margin-bottom: 15px; color: rgb(51, 51, 51); font-size: 14px; padding: 0px; text-align: justify;">Sed eu nunc mi. Morbi et ullamcorper sapien, cursus euismod magna. Nulla in ante eros. Phasellus luctus augue ac ex tempor, sit amet elementum tellus commodo. Phasellus quis lacus id quam iaculis blandit eu sed odio. Nam non tortor vitae lacus convallis tincidunt. Duis a orci dolor. Nulla vel ipsum lacinia, elementum dolor sit amet, placerat sapien. Vivamus congue velit id odio vulputate, ac scelerisque mauris bibendum. Vestibulum commodo justo vitae laoreet dictum. Praesent at imperdiet felis, eget sodales est. Vestibulum id facilisis magna, quis faucibus augue.</p><br><h4>2. What problems is this client having?</h4><p open="" sans",="" arial,="" sans-serif;="" font-size:="" 14px;"="" style="margin-top: 0px; margin-bottom: 15px; color: rgb(51, 51, 51); font-size: 14px; padding: 0px; text-align: justify;">Nulla pellentesque felis ac augue vehicula, at laoreet justo varius. Sed nisi dolor, ullamcorper sit amet porttitor ut, faucibus sit amet sapien. In a risus vel felis venenatis consectetur. Suspendisse egestas maximus mi non elementum. Cras pellentesque consectetur orci, non blandit nisl egestas condimentum. Proin finibus mi quis ligula rutrum, quis rhoncus nunc convallis. Integer dapibus ante a placerat ullamcorper. Sed ultricies feugiat magna, sed aliquet tortor tincidunt at. In nec eros est. Aenean mattis mauris eget elit lobortis, a tincidunt lectus tincidunt. Aliquam at hendrerit massa. Phasellus eu eros elit. Cras a nibh nec tellus convallis consectetur id vel nulla. Suspendisse potenti.</p><p open="" sans",="" arial,="" sans-serif;="" font-size:="" 14px;"="" style="margin-top: 0px; margin-bottom: 15px; color: rgb(51, 51, 51); font-size: 14px; padding: 0px; text-align: justify;">Vestibulum in fringilla turpis. Sed suscipit urna ex. Fusce pretium iaculis mollis. Duis lobortis a sapien a accumsan. Maecenas eu ex odio. Sed id diam ac leo feugiat maximus. In scelerisque gravida diam vitae mattis. Vivamus blandit facilisis suscipit. Mauris quam ex, pellentesque sed rutrum eget, venenatis et est. Quisque pellentesque porttitor elit nec semper.</p><p open="" sans",="" arial,="" sans-serif;="" font-size:="" 14px;"="" style="margin-top: 0px; margin-bottom: 15px; color: rgb(51, 51, 51); font-size: 14px; padding: 0px; text-align: justify;">In dictum, purus sit amet varius mattis, metus lacus aliquet erat, vel tincidunt sapien nulla quis ex. Quisque rhoncus, mauris non malesuada porttitor, augue ligula placerat enim, eu vestibulum mauris tellus vitae tortor. Donec eu sapien a urna feugiat interdum non at tortor. Pellentesque varius, arcu quis faucibus maximus, dolor magna ornare ex, at faucibus orci dui vel leo. Sed convallis elit eget nisl commodo, eu accumsan dui dapibus. Aliquam erat volutpat. Donec et arcu in neque tristique suscipit.</p><br><h4>3. Who should be included in this deal?</h4><h5>@staff-name, @staff-name, etc..</h5><h6><i><br></i></h6><h6><p open="" sans",="" arial,="" sans-serif;="" font-size:="" 14px;"="" style="margin-top: 0px; margin-bottom: 15px; color: rgb(51, 51, 51); font-size: 14px; padding: 0px;"><br></p><p open="" sans",="" arial,="" sans-serif;="" font-size:="" 14px;"="" style="margin-top: 0px; margin-bottom: 15px; color: rgb(51, 51, 51); font-size: 14px; padding: 0px; text-align: justify;"><br></p></h6><p></p>',
// 		defaultOppNoteText = `<p></p><h3 style="text-align: center; "><b style="font-weight:normal;" id="docs-internal-guid-fff1eb84-08aa-c089-3501-ccaf78e875df"><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-size:12pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;"><img title="horizontal line" src="https://lh6.googleusercontent.com/npWurXfxiudXHGTevKOpG9EryyV5P_zI9dJNXwi4iIOcW9-yxVK923NnH-PFbjCa-42lybrYMbOEopLISLQAvSFucA9jJNR1YAXdwqZhnCvAUfZv4EcEm7MqSHGui2cliMsPxI4j" width="624" height="8" style="border: none; transform: rotate(0.00rad); -webkit-transform: rotate(0.00rad);"></span></p><p dir="ltr" style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-size:30pt;font-family:'PT Sans Narrow';color:#695d46;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">New Opportunity</span></p><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size:13.999999999999998pt;font-family:'PT Sans Narrow';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Friday, 09.04.20XX</span></p><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-size:18pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">─</span></p></b></h3><h1 dir="ltr" style="line-height:1.56;margin-top:24pt;margin-bottom:0pt;"><span style="font-size:18pt;font-family:'PT Sans Narrow';color:#ff5e0e;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Team Members</span></h1><span style="font-size: 24px; text-align: center;"><ul style="margin-top: 0pt; margin-bottom: 0pt;"><li dir="ltr" style="list-style-type: disc; font-size: 11pt; color: rgb(105, 93, 70); background-color: transparent; font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline;"><p dir="ltr" style="margin-top: 6pt; margin-bottom: 0pt; text-align: left; line-height: 1.44;"><span style="font-family: &quot;Open Sans&quot;; white-space: pre-wrap;">@Harry Henderson, @Bugs Bunny, @Peter Pan</span></p></li></ul></span><h1 dir="ltr" style="line-height:1.56;margin-top:24pt;margin-bottom:0pt;"><span style="font-size:18pt;font-family:'PT Sans Narrow';color:#ff5e0e;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Agenda</span></h1><h2 dir="ltr" style="line-height:1.44;margin-top:6pt;margin-bottom:0pt;"><span style="font-size:16pt;font-family:'PT Sans Narrow';color:#008575;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Last Follow-up</span></h2><h3 style="text-align: center; "><b style="font-weight:normal;"><ol style="margin-top:0pt;margin-bottom:0pt;"><li dir="ltr" style="list-style-type:decimal;font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;"><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. </span></p></li></ol></b></h3><h2 dir="ltr" style="line-height:1.44;margin-top:6pt;margin-bottom:0pt;"><span style="font-size:16pt;font-family:'PT Sans Narrow';color:#008575;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">New Business</span></h2><h3 style="text-align: center; "><b style="font-weight:normal;"><ol style="margin-top:0pt;margin-bottom:0pt;" start="2"><li dir="ltr" style="list-style-type:decimal;font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;"><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. </span></p></li><li dir="ltr" style="list-style-type:decimal;font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;"><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Suspendisse scelerisque mi a mi. </span></p></li></ol></b></h3><h1 dir="ltr" style="line-height:1.56;margin-top:24pt;margin-bottom:0pt;"><span style="font-size:18pt;font-family:'PT Sans Narrow';color:#ff5e0e;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Notes</span></h1><h3 style="text-align: center; "><b style="font-weight:normal;"><ul style="margin-top:0pt;margin-bottom:0pt;"><li dir="ltr" style="list-style-type:disc;font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;"><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Lorem ipsum dolor sit amet</span><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;"> consectetuer adipiscing elit. </span></p></li><li dir="ltr" style="list-style-type:disc;font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;"><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Vestibulum ante</span><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;"> ipsum primis elementum, libero interdum auctor cursus, sapien enim dictum quam. </span></p></li><ul style="margin-top:0pt;margin-bottom:0pt;"><li dir="ltr" style="list-style-type:circle;font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;"><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Phasellus vehicula nonummy nunc.</span></p></li></ul></ul></b></h3><h1 dir="ltr" style="line-height:1.56;margin-top:24pt;margin-bottom:0pt;"><span style="font-size:18pt;font-family:'PT Sans Narrow';color:#ff5e0e;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Action Items</span></h1><h3 style="text-align: center; "><b style="font-weight:normal;"><ol style="margin-top:0pt;margin-bottom:0pt;"><li dir="ltr" style="list-style-type:decimal;font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;"><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size:11pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Lorem ipsum dolor sit amet consectetuer adipiscing elit. </span></p></li></ol></b></h3><h1 dir="ltr" style="line-height:1.56;margin-top:24pt;margin-bottom:0pt;"><span style="font-size:18pt;font-family:'PT Sans Narrow';color:#ff5e0e;background-color:transparent;font-weight:700;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;">Next Follow-Up Agenda</span></h1><h3 style="text-align: center; "><p dir="ltr" style="text-align: left; line-height: 1.44; margin-top: 6pt; margin-bottom: 0pt;"><span style="font-size: 11pt; font-family: " open="" sans";="" color:="" rgb(105,="" 93,="" 70);="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"="">Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</span></p></h3><p></p>`,
// 		defaultOppNoteText = `<p></p><h3 style="text-align: center; "><span style="" id="docs-internal-guid-fff1eb84-08aa-c089-3501-ccaf78e875df"><p dir="ltr" style="font-weight: bold; text-align: left; line-height: 1.44; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-size:12pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;"><img title="horizontal line" src="https://lh6.googleusercontent.com/npWurXfxiudXHGTevKOpG9EryyV5P_zI9dJNXwi4iIOcW9-yxVK923NnH-PFbjCa-42lybrYMbOEopLISLQAvSFucA9jJNR1YAXdwqZhnCvAUfZv4EcEm7MqSHGui2cliMsPxI4j" width="624" height="8" style="border: none; transform: rotate(0.00rad); -webkit-transform: rotate(0.00rad);"></span></p><h2 style="font-weight: bold; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(132, 99, 0);"><br></span></h2><h2 style="font-weight: bold; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(132, 99, 0);">New Opportunity</span></h2><h5 style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-weight: bold; color: rgb(99, 99, 99);">Friday, 07, 04, 1976</span></h5><h6 style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">––––</span></h6><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><br></p></span></h3><h3 style="text-align: center; "><span style=""><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);">Follow-Up Reminders</span></p><p></p><h5><ol><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Suspendisse scelerisque mi a mi.</span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Vestibulum ante ipsum primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</span></li></ol></h5><p></p><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);"><br></span></p><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);">Notes</span></p><h5><ul><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style="color: rgb(99, 99, 99);"><span style="font-size: 11pt; font-family: &quot;Open Sans&quot;; background-color: transparent; font-weight: 700; font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline; white-space: pre-wrap;">Lorem ipsum dolor sit amet</span><span style="font-size: 11pt; font-family: &quot;Open Sans&quot;; background-color: transparent; font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline; white-space: pre-wrap;"> consectetuer adipiscing elit.</span></span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style="color: rgb(99, 99, 99);"><span style="font-size: 11pt; font-family: &quot;Open Sans&quot;; background-color: transparent; font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline; white-space: pre-wrap; font-weight: bold;">Vestibulum ante ipsum </span><span style="font-size: 11pt; font-family: &quot;Open Sans&quot;; background-color: transparent; font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline; white-space: pre-wrap;">primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</span></span></li><ul><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style="color: rgb(99, 99, 99);"><span style="font-size: 11pt; font-family: &quot;Open Sans&quot;; background-color: transparent; font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline; white-space: pre-wrap;">Phasellus vehicula nonummy nunc.</span></span></li></ul></ul></h5></span></h3><h3 style="color: rgb(0, 0, 0); text-align: center;"><span id="docs-internal-guid-fff1eb84-08aa-c089-3501-ccaf78e875df"><p style="margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><span style="color: rgb(115, 165, 173);"><br></span></p><p style="margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><span style="color: rgb(115, 165, 173);">Attendees</span></p></span></h3><h5 style="line-height: 1.2; color: rgb(0, 0, 0); margin-top: 0pt; margin-bottom: 0pt;"><span style="font-weight: bold; color: rgb(99, 99, 99);">@Harry Henderson, @John Smith, @Jane Doe</span></h5><p></p>`,
// 		defaultOppNoteText = `<p></p><h3 style="text-align: center; "><span style="" id="docs-internal-guid-fff1eb84-08aa-c089-3501-ccaf78e875df"><p dir="ltr" style="font-weight: bold; text-align: left; line-height: 1.44; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-size:12pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;"><img title="horizontal line" src="https://lh6.googleusercontent.com/npWurXfxiudXHGTevKOpG9EryyV5P_zI9dJNXwi4iIOcW9-yxVK923NnH-PFbjCa-42lybrYMbOEopLISLQAvSFucA9jJNR1YAXdwqZhnCvAUfZv4EcEm7MqSHGui2cliMsPxI4j" width="624" height="8" style="border: none; transform: rotate(0.00rad); -webkit-transform: rotate(0.00rad);"></span></p><h2 style="font-weight: bold; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(132, 99, 0);">New Opportunity</span></h2><h5 style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-weight: bold; color: rgb(99, 99, 99);">Friday, 07, 04, 1976</span></h5><h6 style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">––––</span></h6><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><br></p></span></h3><h3 style="text-align: center; "><span style=""><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);">Follow-Up Agenda</span></p><p></p><h5><ol><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Suspendisse scelerisque mi a mi.</span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Vestibulum ante ipsum primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</span></li></ol></h5><p></p><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);"><br></span></p><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);">Notes</span></p><h5><ul><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style="color: rgb(99, 99, 99);"><span style="font-size: 11pt; font-family: " open="" sans";="" background-color:="" transparent;="" font-weight:="" 700;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"="">Lorem ipsum dolor sit amet</span><span style="font-size: 11pt; font-family: " open="" sans";="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"=""> consectetuer adipiscing elit.</span></span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style="color: rgb(99, 99, 99);"><span style="font-size: 11pt; font-family: " open="" sans";="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;="" font-weight:="" bold;"="">Vestibulum ante ipsum </span><span style="font-size: 11pt; font-family: " open="" sans";="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"="">primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</span></span></li><ul><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style="color: rgb(99, 99, 99);"><span style="font-size: 11pt; font-family: " open="" sans";="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"="">Phasellus vehicula nonummy nunc.</span></span></li></ul></ul></h5></span></h3><h3 style="text-align: center;"><p style="color: rgb(0, 0, 0); margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><span style="color: rgb(115, 165, 173);"><br></span></p><p style="margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><font color="#73a5ad">Future Follow-Up Reminders</font></p><p style="color: rgb(0, 0, 0);"></p><p style="color: rgb(0, 0, 0);"></p></h3><h5 style="box-sizing: border-box; font-family: Ubuntu; font-weight: 500; line-height: 1.1; color: inherit; margin-top: 8px !important; margin-bottom: 8px !important; font-size: 14px;"><ol style="box-sizing: border-box; margin-top: 0px; margin-bottom: 10px;"><li style="box-sizing: border-box; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="box-sizing: border-box; color: rgb(99, 99, 99);">Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</span></li><li style="box-sizing: border-box; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="box-sizing: border-box; color: rgb(99, 99, 99);">Suspendisse scelerisque mi a mi.</span></li><li style="box-sizing: border-box; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="box-sizing: border-box; color: rgb(99, 99, 99);">Vestibulum ante ipsum primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</span></li></ol></h5><h3 style="color: rgb(0, 0, 0); text-align: center;"><span id="docs-internal-guid-fff1eb84-08aa-c089-3501-ccaf78e875df"><p style="margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><span style="color: rgb(115, 165, 173);"><br></span></p><p style="margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><span style="color: rgb(115, 165, 173);">Team Members</span></p></span></h3><h5 style="line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-weight: bold; color: rgb(99, 99, 99);">@Harry Henderson, @John Smith, @Jane Doe</span></h5><p></p>`,
// 		defaultOppNoteText = `<p></p><h3 style="text-align: center; "><span style="" id="docs-internal-guid-fff1eb84-08aa-c089-3501-ccaf78e875df"><p dir="ltr" style="font-weight: bold; text-align: left; line-height: 1.44; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-size:12pt;font-family:'Open Sans';color:#695d46;background-color:transparent;font-weight:400;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;"><img title="horizontal line" src="https://lh6.googleusercontent.com/npWurXfxiudXHGTevKOpG9EryyV5P_zI9dJNXwi4iIOcW9-yxVK923NnH-PFbjCa-42lybrYMbOEopLISLQAvSFucA9jJNR1YAXdwqZhnCvAUfZv4EcEm7MqSHGui2cliMsPxI4j" width="624" height="8" style="border: none; transform: rotate(0.00rad); -webkit-transform: rotate(0.00rad);"></span></p><h2 style="font-weight: bold; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(132, 99, 0);">New Opportunity</span></h2><h5 style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="font-weight: bold; color: rgb(99, 99, 99);">Friday, 07, 04, 1976</span></h5><h6 style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">––––</span></h6><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><br></p></span></h3><h3 style="text-align: center; "><span style=""><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);">Follow-Up Agenda</span></p><p></p><h5><ol><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Suspendisse scelerisque mi a mi.</span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99);">Vestibulum ante ipsum primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</span></li></ol></h5><p></p><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);"><br></span></p><p style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(115, 165, 173);">Notes</span></p><h5><ul><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style="color: rgb(99, 99, 99);"><span style="font-size: 11pt;" open="" sans";="" background-color:="" transparent;="" font-weight:="" 700;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"="">Lorem ipsum dolor sit amet</span><span style="font-size: 11pt;" open="" sans";="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"=""> consectetuer adipiscing elit.</span></span></li><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style="color: rgb(99, 99, 99);"><span style="font-size: 11pt; font-family: " open="" sans";="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;="" font-weight:="" bold;"="">Vestibulum ante ipsum </span><span style="font-size: 11pt; font-family: " open="" sans";="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"="">primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</span></span></li><ul><li style="text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span id="docs-internal-guid-f8da19e6-1470-647a-a3e3-b9bbb091aad7" style=""><span style="font-size: 11pt; color: rgb(99, 99, 99);" open="" sans";="" background-color:="" transparent;="" font-variant-numeric:="" normal;="" font-variant-east-asian:="" vertical-align:="" baseline;="" white-space:="" pre-wrap;"="">Phasellus vehicula nonummy nunc.</span></span></li></ul></ul></h5></span></h3><h3 style="text-align: center;"><p style="color: rgb(0, 0, 0); margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><span style="color: rgb(115, 165, 173);"><br></span></p><p style="margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><font color="#73a5ad">Future Follow-Up Reminders</font></p><p style="color: rgb(0, 0, 0);"></p><p style="color: rgb(0, 0, 0);"></p></h3><h5 style="box-sizing: border-box; font-family: Ubuntu; font-weight: 500; line-height: 1.1; font-size: 14px; margin-top: 8px !important; margin-bottom: 8px !important;"><ol style="box-sizing: border-box; margin-top: 0px; margin-bottom: 10px;"><li style="box-sizing: border-box; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="box-sizing: border-box; color: rgb(99, 99, 99);">Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</span></li><li style="box-sizing: border-box; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="box-sizing: border-box; color: rgb(99, 99, 99);">Suspendisse scelerisque mi a mi.</span></li><li style="box-sizing: border-box; text-align: left; line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="box-sizing: border-box; color: rgb(99, 99, 99);">Vestibulum ante ipsum primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</span></li></ol></h5><h3 style="color: rgb(0, 0, 0); text-align: center;"><span id="docs-internal-guid-fff1eb84-08aa-c089-3501-ccaf78e875df"><p style="margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><span style="color: rgb(115, 165, 173);"><br></span></p><p style="margin-top: 0pt; margin-bottom: 0pt; text-align: left; line-height: 1.2;"><span style="color: rgb(115, 165, 173);">Team Members</span></p></span></h3><h5 style="line-height: 1.2; margin-top: 0pt; margin-bottom: 0pt;"><span style="color: rgb(99, 99, 99); font-weight: 700;">@Harry Henderson, @John Smith, @Jane Doe</span></h5><p></p>`,
		defaultOppNoteText = `<h1>New Opportunity</h1>
<h4>Date: ${moment().format('M/D/YYYY')}</h4>

<hr>

<h3>Follow-Up Agenda</h3>
<ol>
	<li>Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</li>
	<li>Suspendisse scelerisque mi a mi.</li>
	<li>Vestibulum ante ipsum primis elementum, libero interdum auctor cursus, sapien enim dictum quam.</li>
</ol>

<hr>

<h3>Notes</h3>
<p>Write some notes about the new opportynity.</p>

<hr>

<h3>Team Members</h3>
<p>@Harry Henderson, @John Smith, @Jane Doe</p>`,
		defaultRequestEmailSubject = 'You have new proposals to view',
		defaultRequestEmail = 'Please click below to view your proposals.',
		contact = {},
		showNavigation = true,
		showSettings = true,
		showCalendar = true,
		homeScreenDefault = true;

	function addWorkorderToContact(dom, state, draw){
		
		function chooseATemplate(dom, contact, newWorkOrder){
						
			dom.empty();
			
			dom.makeNode('panel', 'div', {css:''});
			
			dom.panel.makeNode('headerCol', 'column', {w:16});
			
			dom.panel.headerCol.makeNode('header', 'headerText', {text:'Select a template to apply', size:'small'});
			
			dom.panel.headerCol.makeNode('footerBtns', 'div', {css:'ui buttons'});
			
			dom.panel.headerCol.footerBtns.makeNode('skip', 'button', {text:'Skip this step <i class="fa fa-arrow-right"></i>', css:'pda-btn-orange'}).notify('click', {
				type:'workorders-run',
				data:{
					run:function(){
						
						this.panel.headerCol.footerBtns.skip.loading();

						sb.notify({
							type:'app-remove-main-navigation-item',
							data:{
								itemId:'contacts',
								viewId:'addWorkorder'
							}
						});
	
						sb.notify({
							type:'app-navigate-to',
							data:{
								itemId:'workorders',
								viewId:{
									id:'single-'+newWorkOrder.id,
									type:'table-single-item',
									title:newWorkOrder.name,
									icon:'<i class="fa fa-wrench"></i>',
									setup:{
										objectType:'work_orders'
									},
									rowObj:newWorkOrder,
									removable:true,
									parent:'table'
								}
							}
						});
												
					}.bind(dom, contact, newWorkOrder)
				}
			}, sb.moduleId);
			
			dom.panel.makeNode('contentCol', 'column', {w:16});
			
			dom.panel.contentCol.makeNode('contentContainer', 'div', {css:''});
			
			dom.panel.contentCol.contentContainer.makeNode('col1', 'column', {w:16});
			
			dom.panel.contentCol.contentContainer.col1.makeNode('loader', 'loader', {});
									
			dom.patch();
			
			components.templateSelectionTable.notify({
				type:'show-table',
				data:{
					domObj:dom.panel.contentCol.contentContainer,
					objectType:'proposals',
					childObjs:3,
					searchObjects:false,
					filters:false,
					download:false,
					navigation:false,
					headerButtons:{
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							action:function(){}
						}
					},
					settings:false,
					rowSelection:false,
					multiSelectButtons:false,
					rowLink:{
						type:'tab',
						header:function(obj){
							return obj.name;
						},
						icon:'check',
						action:function(obj){

							function createContractObject(obj, workOrder, callback, active, objId){
											
								var where = {related_object:obj.main_object, active:'Yes'};
								
								if(active){
									where.active = active;
								}
								
								if(objId){
									where.id = +objId;
								}
								
								sb.data.db.obj.getWhere('contracts', where, function(res){
									
									if(res.length > 0){
										
										// has a contract
										res[0].related_object = workOrder.id;
											
										sb.data.db.obj.create('contracts', res[0], function(newContract){
											
											callback(newContract);
											
										}, 3);
										
									}else{
										
										// no contract found
										callback(false);
										
									}
									
								});
								
							}
							
							function createMenuObject(obj, workOrder, callback, active, objId){
								
								function duplicateMenuItems(menu, stack, count, callback, ret){
									
									if(!ret){
										ret = [];
									}
									
									if(stack[count]){
										
										var itemsToCreate = [];
										
										_.each(stack[count].items, function(item){
											
											if(item != null && item.hasOwnProperty('menu')){
												
												item.menu = menu.id;
												itemsToCreate.push(item);
												
											}
											
										});
										
										sb.data.db.obj.create('inventory_menu_line_item', itemsToCreate, function(createdItems){
											
											stack[count].items = _.pluck(createdItems, 'id');
											
											ret.push(stack[count]);
											
											count++;
											
											return duplicateMenuItems(menu, stack, count, callback, ret);
											
										});
										
									}else{
										
										return callback(ret);
										
									}
									
								}
								
								var where = {related:obj.main_object, active:'Yes', childObjs:1};
								
								if(active){
									where.active = active;
								}
								
								if(objId){
									where.id = +objId;
								}
								
								if(active == 'No' && !objId){
									
									return callback(false);
	
								}
								
								sb.data.db.obj.getWhere('inventory_menu', where, function(res){
									
									if(res.length > 0){
										
										// has a menu
										res[0].related = workOrder.id;
										res[0].guest_count = workOrder.guest_count;
											
										sb.data.db.obj.create('inventory_menu', res[0], function(newMenu){
											
											duplicateMenuItems(newMenu, res[0].sections, 0, function(newSections){
													
												newMenu.sections = newSections;
												
												sb.data.db.obj.update('inventory_menu', newMenu, function(updatedMenu){
													
													callback(updatedMenu);
													
												});
													
											});	
																								
										}, 2);
										
									}else{
										
										// no menu found
										callback(false);
										
									}
									
								});
								
							}
							
							function createStaffScheduleObject(obj, workOrder, callback, active, objId){

								var where = {parent: workOrder.id, group_type: 'Schedule', is_active:'Yes'};
								
								if(active){
									where.is_active = active;
								}
								
								if(objId){
									where.id = +objId;
								}
								
								if(active == 'No' && !objId){
									
									return callback(false);
	
								}
								
								sb.data.db.obj.getWhere('groups', where, function(res){
									
									if(res.length > 0){
										
										// has a staff schedule
										res[0].parent = workOrder.id;
											
										sb.data.db.obj.create('groups', res[0], function(staffSchedule){
											
											callback(staffSchedule);
											
										}, 1);
										
									}else{
										
										// no staff schedule found
										callback(false);
										
									}
									
								});
								
							}
	
							function createInvoiceObjects(obj, workOrder, callback, active, objIds){
								
								var where = {related_object:obj.main_object, active:'Yes'};
								
								if(active){
									where.active = active;
								}
								
								if(objIds){
									where.id = {
										type:'or',
										values:_.map(objIds, function(o){ return +o; }, [])
									};
								}
								
								if(active == 'No' && !objIds){
									
									return callback(false);
	
								}
	
								sb.data.db.obj.getWhere('invoices', where, function(res){
									
									if(res.length > 0){
										
										// has invoices
										
										_.each(res, function(invoiceObj){
											
											invoiceObj.related_object = workOrder.id;
											
										});
											
										sb.data.db.obj.create('invoices', res, function(invoice){
											
											callback(invoice);
											
										}, 2);
										
									}else{
										
										// no invoices found
										callback(false);
										
									}
									
								});
								
							}
							
							function duplicateHistoryItems(workOrder, template, callback){
								
								function copyHistoryItems(stack, count, callback, ret){
									
									if(!ret){
										ret = [];
									}
									
									if(stack[count]){
										
										var item = stack[count],
											itemsToCreate = [];
										
										createContractObject(template, workOrder, function(newContract){
											
											createMenuObject(template, workOrder, function(newMenu){
												
												createStaffScheduleObject(template, workOrder, function(newSchedule){
													
													createInvoiceObjects(template, workOrder, function(newInvoices){
														
														var historyItem = item;
														
														historyItem.date = moment().format();
														historyItem.contract = newContract.id;
														historyItem.start_date = workOrder.start_date;
														historyItem.end_date = workOrder.end_date;
														
														if(newMenu){
															historyItem.menu = newMenu.id;
														}
														
														if(newSchedule){
															historyItem.schedule = newSchedule.id;
														}
														
														if(newInvoices){
															historyItem.invoices = _.pluck(newInvoices, 'id');
														}
														
														ret.push(historyItem);
														
														count++;
														
														return copyHistoryItems(stack, count, callback, ret);
														
													}, 'No', item.invoices);
													
												}, 'No', item.schedule)
												
											}, 'No', item.menu);
											
										}, 'No', item.contract);
	
										
									}else{
										
										return callback(ret);
										
									}
									
								}
	
								if(template.history.length > 0){
									
									copyHistoryItems(template.history, 0, function(newHistory){
	
										workOrder.history = newHistory;
										
										sb.data.db.obj.update('work_orders', workOrder, function(updatedWorkOrder){
											
											callback(updatedWorkOrder);
											
										}, 3);
																							
									});
									
								}else{
									
									callback(workOrder);
									
								}
								
							}
							
							// Create a Copy of the Active Contract
	
							createContractObject(obj, newWorkOrder, function(contractObj){
									
								// Create a Copy of the Active Menu and Appy Copied ID to the new Workorder
								createMenuObject(obj, newWorkOrder, function(menuObj){
									
									// Create a Copy of the Active Schedule and Appy Copied ID to the new Workorder
									createStaffScheduleObject(obj, newWorkOrder, function(staffSchedule){
										
										// Create a Copy of the Active Invoices and Appy Copied ID to the new Workorder
										createInvoiceObjects(obj, newWorkOrder, function(invoices){
											
											// Add the new contract to the new work order object		
											newWorkOrder.contract = contractObj.id;
											
											if(obj.hasOwnProperty('qualification_note')){
												
												newWorkOrder.qualification_note = obj.qualification_note;
												
											}
											
											checkSettingsObject(function(settings){
												
												if(settings.follow_up_type){
													
													newWorkOrder.follow_up_date = moment().add(settings.follow_up_time, settings.follow_up_type);
													newWorkOrder.potential_closing_date = newWorkOrder.follow_up_date;
												
												}
	
												// update the new work order object		
												sb.data.db.obj.update('work_orders', newWorkOrder, function(response){
													
													var proposalObject = {
															main_object:response.id,
															contract:contractObj.id,
															invoices:_.pluck(invoices, 'id'),
															name:obj.name,
															menu:menuObj.id,
															sections:obj.sections,
															pricing:obj.pricing,
															schedule:staffSchedule.id,
															start_date:response.start_date,
															end_date:response.end_date
														};
														
													sb.data.db.obj.create('proposals', proposalObject, function(newProposal){
														
														sb.notify({
															type:'app-remove-main-navigation-item',
															data:{
																itemId:'contacts',
																viewId:'addWorkorder'
															}
														});
														
														sb.notify({
															type:'app-navigate-to',
															data:{
																itemId:'workorders',
																viewId:{
																	id:'single-'+newWorkOrder.id,
																	type:'table-single-item',
																	title:newWorkOrder.name,
																	icon:'<i class="fa fa-wrench"></i>',
																	setup:{
																		objectType:'work_orders'
																	},
																	rowObj:newWorkOrder,
																	removable:true,
																	parent:'table'
																}
															}
														});
														
													});
													
												}, 3);
												
											});
													
										});
												
									});
											
								});
										
							});							
						}
					},
					visibleCols:{
						object_uid:'ID',
						name:'Name',
						venue:'Venue'
					},
					cells:{
						venue:function(obj){
							return obj.venue.name;
						}
					},
/*
					feedUI:{
						action:function(ui, obj){

							var templateObj = obj;
							
							ui = sb.dom.make(ui.selector);
							
							var templateContainer = ui.makeNode(`templateCont${obj.id}`, 'div', {css:'ui card'});
							
							templateContainer.makeNode('title', 'headerText', {text:obj.name, size:'small'});
							templateContainer.makeNode('details', 'container', {css:'pda-container'});
							
							if(obj.hasOwnProperty('contract')){
								
								if(obj.contract != null && obj.contract != undefined){
									
									templateContainer.details
										.makeNode('contract', 'div', {css:'content'})
											.makeNode('contract', 'div', {text:`<small>Contract: ${obj.contract.name}</small>`, css:''});
									
								}else{
									
									templateContainer.details
										.makeNode('contract', 'div', {css:'content'})
											.makeNode('contract', 'headerText', {text:`<small>Contract: Contract not available.</small>`, size:'x-small'});

									
								}
								
							}
							
							if(obj.hasOwnProperty('created_by')){
								if(obj.created_by.fname && obj.created_by.lname){
									
									var createdByName = `${obj.created_by.fname} ${obj.created_by.lname}`;
									
									templateContainer.details
										.makeNode('contract', 'div', {css:'content'})
											.makeNode('createdBy', 'headerText', {text:`<small>Created By: ${createdByName}</small>`, size:'x-small'});
									
								}
							}
							
							if(obj.hasOwnProperty('history')){
								if(obj.history.length > 0){
									
									templateContainer.details
										.makeNode('contract', 'div', {css:'content'})
											.makeNode('proposal', 'headerText', {text:`<small>Proposals: ${obj.history.length}</small>`, size:'x-small'});
								
									_.each(obj.history, function(historyObj){
										
										if(historyObj.hasOwnProperty('pricing')){
											
											if(historyObj.pricing.length > 0){
												
												templateContainer.details
													.makeNode('contract', 'div', {css:'content'})
														.makeNode('pricing', 'headerText', {text:`<small>Price Name: ${historyObj.pricing}, Price: ${historyObj.pricing} </small>`, size:'x-small'});
												
											}else{
												
												templateContainer.details
													.makeNode('contract', 'div', {css:'content'})
														.makeNode('pricing', 'headerText', {text:`<small>Pricing: Not yet available.</small>`, size:'x-small'});
												
											}
											
										}
										
									});
									
								}else{
									
									templateContainer.details
										.makeNode('contract', 'div', {css:'content'})
											.makeNode('proposal', 'headerText', {text:`<small>Proposals: 0</small>`, size:'x-small'});
									
									templateContainer.details
										.makeNode('contract', 'div', {css:'content'})
											.makeNode('pricing', 'headerText', {text:`<small>Pricing: Not yet available.</small>`, size:'x-small'});
									
								}
							}
							
							templateContainer.makeNode('btnGroup', 'div', {css:'ui buttons'});
														
							templateContainer.btnGroup.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Select and Apply', css:'pda-btn-green'}).notify('click', {
								type:'workorders-run',
								data:{
									run:function(newWorkOrder, templateObj){
																				
										templateContainer.btnGroup.save.loading();
										
										function createContractObject(obj, workOrder, callback, active, objId){
											
											var where = {related_object:obj.main_object, active:'Yes'};
											
											if(active){
												where.active = active;
											}
											
											if(objId){
												where.id = +objId;
											}
											
											sb.data.db.obj.getWhere('contracts', where, function(res){
												
												if(res.length > 0){
													
													// has a contract
													res[0].related_object = workOrder.id;
														
													sb.data.db.obj.create('contracts', res[0], function(newContract){
														
														callback(newContract);
														
													}, 3);
													
												}else{
													
													// no contract found
													callback(false);
													
												}
												
											});
											
										}
										
										function createMenuObject(obj, workOrder, callback, active, objId){
											
											function duplicateMenuItems(menu, stack, count, callback, ret){
												
												if(!ret){
													ret = [];
												}
												
												if(stack[count]){
													
													var itemsToCreate = [];
													
													_.each(stack[count].items, function(item){
														
														if(item != null && item.hasOwnProperty('menu')){
															
															item.menu = menu.id;
															itemsToCreate.push(item);
															
														}
														
													});
													
													sb.data.db.obj.create('inventory_menu_line_item', itemsToCreate, function(createdItems){
														
														stack[count].items = _.pluck(createdItems, 'id');
														
														ret.push(stack[count]);
														
														count++;
														
														return duplicateMenuItems(menu, stack, count, callback, ret);
														
													});
													
												}else{
													
													return callback(ret);
													
												}
												
											}
											
											var where = {related:obj.main_object, active:'Yes', childObjs:1};
											
											if(active){
												where.active = active;
											}
											
											if(objId){
												where.id = +objId;
											}
											
											if(active == 'No' && !objId){
												
												return callback(false);

											}
											
											sb.data.db.obj.getWhere('inventory_menu', where, function(res){
												
												if(res.length > 0){
													
													// has a menu
													res[0].related = workOrder.id;
													res[0].guest_count = workOrder.guest_count;
														
													sb.data.db.obj.create('inventory_menu', res[0], function(newMenu){
														
														duplicateMenuItems(newMenu, res[0].sections, 0, function(newSections){
																
															newMenu.sections = newSections;
															
															sb.data.db.obj.update('inventory_menu', newMenu, function(updatedMenu){
																
																callback(updatedMenu);
																
															});
																
														});	
																											
													}, 2);
													
												}else{
													
													// no menu found
													callback(false);
													
												}
												
											});
											
										}
										
										function createStaffScheduleObject(obj, workOrder, callback, active, objId){
											
											var where = {event:obj.main_object, active:'Yes'};
											
											if(active){
												where.active = active;
											}
											
											if(objId){
												where.id = +objId;
											}
											
											if(active == 'No' && !objId){
												
												return callback(false);

											}
											
											sb.data.db.obj.getWhere('staff_schedules', where, function(res){
												
												if(res.length > 0){
													
													// has a staff schedule
													res[0].event = workOrder.id;
														
													sb.data.db.obj.create('staff_schedules', res[0], function(staffSchedule){
														
														callback(staffSchedule);
														
													}, 2);
													
												}else{
													
													// no staff schedule found
													callback(false);
													
												}
												
											});
											
										}

										function createInvoiceObjects(obj, workOrder, callback, active, objIds){
											
											var where = {related_object:obj.main_object, active:'Yes'};
											
											if(active){
												where.active = active;
											}
											
											if(objIds){
												where.id = {
													type:'or',
													values:_.map(objIds, function(o){ return +o; }, [])
												};
											}
											
											if(active == 'No' && !objIds){
												
												return callback(false);

											}

											sb.data.db.obj.getWhere('invoices', where, function(res){
												
												if(res.length > 0){
													
													// has invoices
													
													_.each(res, function(invoiceObj){
														
														invoiceObj.related_object = workOrder.id;
														
													});
														
													sb.data.db.obj.create('invoices', res, function(invoice){
														
														callback(invoice);
														
													}, 2);
													
												}else{
													
													// no invoices found
													callback(false);
													
												}
												
											});
											
										}
										
										function duplicateHistoryItems(workOrder, template, callback){
											
											function copyHistoryItems(stack, count, callback, ret){
												
												if(!ret){
													ret = [];
												}
												
												if(stack[count]){
													
													var item = stack[count],
														itemsToCreate = [];
													
													createContractObject(template, workOrder, function(newContract){
														
														createMenuObject(template, workOrder, function(newMenu){
															
															createStaffScheduleObject(template, workOrder, function(newSchedule){
																
																createInvoiceObjects(template, workOrder, function(newInvoices){
																	
																	var historyItem = item;
																	
																	historyItem.date = moment().format();
																	historyItem.contract = newContract.id;
																	historyItem.start_date = workOrder.start_date;
																	historyItem.end_date = workOrder.end_date;
																	
																	if(newMenu){
																		historyItem.menu = newMenu.id;
																	}
																	
																	if(newSchedule){
																		historyItem.schedule = newSchedule.id;
																	}
																	
																	if(newInvoices){
																		historyItem.invoices = _.pluck(newInvoices, 'id');
																	}
																	
																	ret.push(historyItem);
																	
																	count++;
																	
																	return copyHistoryItems(stack, count, callback, ret);
																	
																}, 'No', item.invoices);
																
															}, 'No', item.schedule)
															
														}, 'No', item.menu);
														
													}, 'No', item.contract);

													
												}else{
													
													return callback(ret);
													
												}
												
											}

											if(template.history.length > 0){
												
												copyHistoryItems(template.history, 0, function(newHistory){

													workOrder.history = newHistory;
													
													sb.data.db.obj.update('work_orders', workOrder, function(updatedWorkOrder){
														
														callback(updatedWorkOrder);
														
													}, 3);
																										
												});
												
											}else{
												
												callback(workOrder);
												
											}
											
										}
										
										// Create a Copy of the Active Contract

										createContractObject(obj, newWorkOrder, function(contractObj){
												
											// Create a Copy of the Active Menu and Appy Copied ID to the new Workorder
											createMenuObject(obj, newWorkOrder, function(menuObj){
												
												// Create a Copy of the Active Schedule and Appy Copied ID to the new Workorder
												createStaffScheduleObject(obj, newWorkOrder, function(staffSchedule){
													
													// Create a Copy of the Active Invoices and Appy Copied ID to the new Workorder
													createInvoiceObjects(obj, newWorkOrder, function(invoices){
														
														// Add the new contract to the new work order object		
														newWorkOrder.contract = contractObj.id;
														
														if(templateObj.hasOwnProperty('qualification_note')){
															
															newWorkOrder.qualification_note = templateObj.qualification_note;
															
														}
														
														checkSettingsObject(function(settings){
															
															if(settings.follow_up_type){
																
																newWorkOrder.follow_up_date = moment().add(settings.follow_up_time, settings.follow_up_type);
																newWorkOrder.potential_closing_date = newEvent.follow_up_date;
															
															}

															// update the new work order object		
															sb.data.db.obj.update('work_orders', newWorkOrder, function(response){
																
																var proposalObject = {
																		main_object:response.id,
																		contract:contractObj.id,
																		invoices:_.pluck(invoices, 'id'),
																		name:obj.name,
																		menu:menuObj.id,
																		sections:obj.sections,
																		pricing:obj.pricing,
																		schedule:staffSchedule.id
																	};
																	
																sb.data.db.obj.create('proposals', proposalObject, function(newProposal){
																	
																	sb.notify({
																		type:'app-remove-main-navigation-item',
																		data:{
																			itemId:'contacts',
																			viewId:'addWorkorder'
																		}
																	});
																	
																	sb.notify({
																		type:'app-navigate-to',
																		data:{
																			itemId:'workorders',
																			viewId:{
																				id:'single-'+newWorkOrder.id,
																				type:'table-single-item',
																				title:newWorkOrder.name,
																				icon:'<i class="fa fa-wrench"></i>',
																				setup:{
																					objectType:'work_orders'
																				},
																				rowObj:newWorkOrder,
																				removable:true,
																				parent:'table'
																			}
																		}
																	});
																	
																});
																
															}, 3);
															
														});
																
													});
															
												});
														
											});
													
										});
										
									}.bind(contact, newWorkOrder, templateObj)
								}
							}, sb.moduleId);
								
							ui.build();
							
						}.bind(contact),
						width:4
					},
*/
					data:function(paged, callback){
																		
						sb.data.db.obj.getWhere('proposals', {is_template:1, childObjs:3, paged:paged}, function(ret){
							
							callback(ret);
							
						});
						
					}
				}
			});
			
		}
				
		function start(dom, state, draw){
			
			function toggleGuestCountField(infoForm, detailsForm, event_type_options){
				
				var selectedType = parseInt(infoForm.process().fields.type.value);
				var typeObj = _.findWhere(event_type_options, {id:selectedType});
				var currentGuestCount = parseInt(detailsForm.process().fields.guest_count.value);

				if(typeObj && typeObj.guests == 'yes'){
					detailsForm.guest_count.update({
						type:'number',
						value:currentGuestCount
					});
				}else{
					detailsForm.guest_count.update({
						type:'hidden',
						value:1
					});
				}
				
			}
			
			var contact = state.main_contact;

			dom.makeNode('panel', 'div', {css:'ui grid'});
			
			dom.panel.makeNode('headerCol', 'column', {w: 16});
			
			dom.panel.headerCol.makeNode('header', 'div', {text: 'Create A Workorder For '+ contact.fname +' '+ contact.lname, css: 'ui huge header'});
			
			//dom.panel.makeNode('headerBreak', 'lineBreak', {});
									
			var formDom = dom.panel;
			
			formDom.makeNode('col1', 'column', {w:8}).makeNode('cont', 'div', {css:''});
			formDom.makeNode('col2', 'column', {w:8}).makeNode('cont', 'div', {css:''});
									
			sb.data.db.obj.getBlueprint('work_orders', function(blueprint){

				sb.data.db.obj.getAll('users', function(users){
					
					sb.data.db.obj.getAll('event_type_options', function(event_type_options){
						
						sb.data.db.obj.getAll('staff_base', function(venues){
								
							getAdditionalContacts(contact.company, function(addContacts){
																										
								var defaultName = '';
								if(!_.isEmpty(contact)){
									defaultName = 'Workorder for '+ contact.fname +' '+ contact.lname;
								}
								
								// Form that includes Opportunity Details
								/*
								var infoForm = {
										name:{
											name:'name',
											label:'Name',
											type:'text',
											value: defaultName
										},
										manager:{
											name:'manager',
											label:'Manager',
											type:'select',
											options:_.map(_.sortBy(users, 'fname'), function(o){ return {value:o.id, name:o.fname +' '+ o.lname}; })
										},
										type:{
											name:'type',
											label:'Type',
											type:'select',
											options:_.map(_.sortBy(event_type_options, 'name'), function(o){ return {value:o.id, name:o.name}; })
										},
										start_date:{
											name:'start_date',
											label:'Start Date',
											type:'date',
											dateFormat:'M/D/YYYY h:mm a',
											value:moment().format('M/D/YYYY h:mm a')
										},
										follow_up_date:{
											name:'follow_up_date',
											label:'Follow-Up Reminder',
											type:'date',
											value:moment().format('M/D/YYYY h:mm a')
										}
									},
									detailsForm = {
										potential_closing_date:{
											name:'potential_closing_date',
											label:'Closing Date',
											type:'date',
											value:moment().format('M/D/YYYY h:mm a')
										},
										potential_value:{
											name:'potential_value',
											label:'Estimated Value',
											type:'int'
										},
										venue:{
											name:'venue',
											label:'Venue',
											type:'hidden',
											value:venues[0].id
										},
										main_contact:{
											name:'main_contact',
											label:'main_contact',
											type:'hidden',
											value:contact.id
										},
										additional_contacts:{
											name:'additional_contacts',
											label:'Additional Contacts',
											type:'checkbox',
											options:[]
										}
									};
*/
								
								var infoForm = {
									name:{
										name:'name',
										label:'Name',
										type:'text',
										value: defaultName
									},
									type:{
										name:'type',
										label:'Type',
										type:'select',
										options:_.map(_.sortBy(event_type_options, 'name'), function(o){ return {value:o.id, name:o.name}; })
									},
									manager:{
										name:'manager',
										label:'Manager',
										type:'select',
										options:_.map(_.sortBy(users, 'fname'), function(o){ return {value:o.id, name:o.fname +' '+ o.lname}; })
									}
								},
								detailsForm = {
									start_date:{
										name:'start_date',
										label:'Work Order Start Date',
										type:'date',
										dateFormat:'M/D/YYYY h:mm a',
										dateType:'datetime',
										value:moment().format('M/D/YYYY h:mm a')
									},
									end_date:{
										name:'end_date',
										label:'Work Order End Date',
										type:'date',
										dateFormat:'M/D/YYYY h:mm a',
										dateType:'datetime',
										value:moment().format('M/D/YYYY h:mm a')
									},
									potential_value:{
										type:'usd',
										name:'potential_value',
										label:'Estimated Value'
									},
									additional_contacts:{
										name:'additional_contacts',
										label:'Additional Contacts',
										type:'checkbox',
										options:[]
									},
									venue:{
										name:'venue',
										label:'Venue',
										type:'hidden',
										value:venues[0].id
									},
									main_contact:{
										name:'main_contact',
										label:'main_contact',
										type:'hidden',
										value:contact.id
									},
									guest_count:{
										name:'guest_count',
										label:'Guest count',
										type:'hidden',
										value:1
									}
								};
								
								_.map(
									_.reject(addContacts, function(user){
										return user.id == contact.id;
									}),
									function(o){
									
									detailsForm.additional_contacts.options.push({
										value:o.id,
										name:'additional_contacts',
										label:o.fname +' '+ o.lname
									});
									
								});	
																
								dom.panel.makeNode('btns', 'div', {css:'ui buttons'});
				
								dom.panel.btns.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left"></i> Back', css:'pda-btn-red'}).notify('click', {
									type:'app-navigate-back',
									data:{}
								}, sb.moduleId);
								
								dom.panel.btns.makeNode('save', 'button', {text:'Next <i class="fa fa-arrow-right"></i>', css:'pda-btn-green'}).notify('click', {
									type:'workorders-run',
									data:{
										run:function(){
																						
											var form1 = dom.panel.col1.cont.form.process(),
												form2 = dom.panel.col2.cont.form2.process(),
												templateObj = {};
								
											if(form1.completed == false || form2.completed == false){
												sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
												return;
											}
								
											dom.panel.btns.save.loading();
											
											var newEvent = {
												status:'Prospecting'
											};
											
											_.each(form1.fields, function(field, fieldName){
												
												newEvent[fieldName] = field.value;
												
											});
											
											_.each(form2.fields, function(field, fieldName){
												
												newEvent[fieldName] = field.value;
												
											});
											
											checkSettingsObject(function(settings){
															
												if(settings.follow_up_type){
													
													newEvent.follow_up_date = moment().add(settings.follow_up_time, settings.follow_up_type);
													newEvent.potential_closing_date = newEvent.follow_up_date;
													
												}
												
												sb.data.db.obj.create('work_orders', newEvent, function(event){
												
													sb.data.db.obj.getWhere('work_orders', {is_template:1, childObjs:3}, function(res){
														
														sb.notify({
															type:'app-remove-main-navigation-item',
															data:{
																itemId:'contacts',
																viewId:'addWorkorder'
															}
														});
														
														sb.notify({
															type:'app-navigate-to',
															data:{
																itemId:'workorders',
																viewId:{
																	id:'single-'+event.id,
																	type:'table-single-item',
																	title:event.name,
																	icon:'<i class="fa fa-wrench"></i>',
																	setup:{
																		objectType:'work_orders'
																	},
																	rowObj:event,
																	removable:true,
																	parent:'table'
																}
															}
														});
														
													});
													
												}, 3);
												
											});
																						
										}.bind(dom, contact)
									}
								}, sb.moduleId);
								
								formDom.col1.cont.makeNode('form', 'form', infoForm);
								formDom.col2.cont.makeNode('form2', 'form', detailsForm);
								
								// listener to toggle guest count field
								formDom.col1.cont.form.notify('change', {
										type:'workorders-run',
										data:{
											run:function(){
												toggleGuestCountField(formDom.col1.cont.form, formDom.col2.cont.form2, event_type_options);
											}
										}
									}, sb.moduleId);
								
								draw(dom);
								
								toggleGuestCountField(formDom.col1.cont.form, formDom.col2.cont.form2, event_type_options);
								
							});
							
						});
					
					});
					
				});
							
			});

			
		}
				
		start(dom, state, draw);
		
	}
	
	function buildHeaderNav(dom, status){
			
		var colors = {
				proposal:'orange',
				review:'muted',
				accepted:'muted',
				signed:'muted',
				paid:'muted',
				completed:'muted'
			},
			buttonText = {
				proposal:'Proposal',
				review:'Client Review',
				accepted:'Accepted',
				signed:'Signed',
				paid:'Paid',
				completed:'Complete'
			};

		switch(status){
			
			case 'Accepted':
				
				colors.proposal = 'green';
				colors.review = 'green';
				colors.accepted = 'green';
				colors.signed = 'orange';
				
				buttonText.proposal += ' <i class="fa fa-check"></i>';
				buttonText.review += ' <i class="fa fa-check"></i>';
				buttonText.accepted += ' <i class="fa fa-check"></i>';
				buttonText.signed = 'Waiting on signature <i class="fa fa-clock-o"></i>';
				
				break;
				
			case 'Paid':
				
				colors.proposal = 'green';
				colors.review = 'green';
				colors.accepted = 'green';
				colors.signed = 'green';
				colors.paid = 'green';
				colors.completed = 'green';
				
				buttonText.proposal += ' <i class="fa fa-check"></i>';
				buttonText.review += ' <i class="fa fa-check"></i>';
				buttonText.accepted += ' <i class="fa fa-check"></i>';
				buttonText.signed += ' <i class="fa fa-check"></i>';
				buttonText.paid += ' <i class="fa fa-check"></i>';
				buttonText.completed += ' <i class="fa fa-check"></i>';
				
				break;		
				
			case 'Sent':
				
				colors.proposal = 'green';
				colors.review = 'orange';
				
				buttonText.proposal += ' <i class="fa fa-check"></i>';
				buttonText.review += ' <i class="fa fa-clock-o"></i>';
				
				break;
				
			case 'Signed':
				
				colors.proposal = 'green';
				colors.review = 'green';
				colors.accepted = 'green';
				colors.signed = 'green';
				colors.paid = 'orange';
				
				buttonText.proposal += ' <i class="fa fa-check"></i>';
				buttonText.review += ' <i class="fa fa-check"></i>';
				buttonText.accepted += ' <i class="fa fa-check"></i>';
				buttonText.signed += ' <i class="fa fa-check"></i>';
				buttonText.paid = 'Waiting on payment <i class="fa fa-clock-o"></i>';
				
				break;		
				
			default:
			
					
			
		}
		
		dom.makeNode('col1', 'column', {width:2})
			.makeNode('text', 'headerText', {text:'1. '+buttonText.proposal, css:'pda-center pda-color-'+colors.proposal, size:'xx-small'});

		dom.makeNode('col2', 'column', {width:2})
			.makeNode('text', 'headerText', {text:'2. '+buttonText.review, css:'pda-center pda-color-'+colors.review, size:'xx-small'});
			
		dom.makeNode('col3', 'column', {width:2})
			.makeNode('text', 'headerText', {text:'3. '+buttonText.accepted, css:'pda-center pda-color-'+colors.accepted, size:'xx-small'});
			
/*
		dom.makeNode('col4', 'column', {width:2})
			.makeNode('text', 'headerText', {text:'4. '+buttonText.signed, css:'pda-center pda-color-'+colors.signed, size:'xx-small'});
*/
			
/*
		dom.makeNode('col5', 'column', {width:2})
			.makeNode('text', 'headerText', {text:'5. '+buttonText.paid, css:'pda-center pda-color-'+colors.paid, size:'xx-small'});			
*/

		dom.makeNode('col6', 'column', {width:2})
			.makeNode('text', 'headerText', {text:'6. '+buttonText.completed, css:'pda-center pda-color-'+colors.completed, size:'xx-small'});
		
		return dom;
	
	}
	
	function checkSettingsObject(callback){
			
		sb.data.db.obj.getAll('workorder_system', function(settingsObj){

			if(settingsObj.length == 0){
				
				var settingsObj = {
						signature_disclaimer:defaultDisclaimer,
						request_email:defaultRequestEmail,
						request_email_subject:defaultRequestEmailSubject,
						default_opp_note:defaultOppNoteText,
						follow_up_time:1,
						follow_up_type:'days'
					};

				sb.data.db.obj.create('workorder_system', settingsObj, function(created){
					
					callback(created);
					
				});	
					
			}else{
				
				callback(settingsObj[0]);
				
			}
		
		});
		
	}
	
	function contactView(dom, state, draw){
		
		var opportunityList = state.data;
		
		dom.empty();
		dom.makeNode('btnGroup', 'buttonGroup', {});
		dom.btnGroup.makeNode('addNewBtn', 'button', {style: '', css: 'pda-btnOutline-green', text: '<i class="fa fa-plus"></i> New Work Order'}).notify('click', {
			type: 'app-navigate-to',
			data: {
				itemId: 'contacts',
				viewId: {
					id: 'addWorkorder',
					type: 'custom',
					title: 'New Work Order',
					icon:'<i class="fa fa-plus"></i>',
					removable: true,
					color:'green',
					dom: addWorkorderToContact,
					viewState: {
						main_contact:state.contact
					}
				}
			}
		}, sb.moduleId);
		
		dom.makeNode('tileColumn', 'column', {width: 12});
				
		if(_.isEmpty(opportunityList)){
			
			dom.tileColumn.makeNode('tableBreak', 'lineBreak', {});

			dom.tileColumn.makeNode('header', 'text', {text: 'No Work Orders. Create the first one.', size: 'small', css: 'pda-text-center'});

		} else {
			
			dom.tileColumn.makeNode('tableBreak', 'lineBreak', {});
			
			dom.tileColumn.makeNode('woTable', 'table', {
				columns:{
					name:'Work Order Name',
					last_updated:'Last Updated',
					view:''
				}
			});

			_.each(opportunityList, function(o, i){
				
				this.woTable.makeRow('row-'+i, [
					o.name,
					moment(o.last_updated).format('MMMM Do YYYY, h:mm:ss a') +' <small>'+ moment(o.last_updated).fromNow() +'</small>',
					''
				]);
				
				this.woTable.body['row-' + i].view.makeNode('view', 'button', {text:'<i class="fa fa-eye"></i> View', css:''});

				this.woTable.body['row-' + i].view.view.notify('click', {
					type:'app-navigate-to',
					data:{
						itemId:'contacts',
						viewId:{
							id:'workorder-'+o.id,
							type:'custom',
							title:o.name,
							icon:'',
							removable:true,
							dom:function(dom, state, draw){

								singleWorkorderViewNew(state.obj, dom, state, function(dom){
									
									draw(dom);
									
								});
								
							},
							viewState:{
								obj:o
							}
						}
					}
				}, sb.moduleId);
				
			}, dom.tileColumn);		
		
		}
		
		draw({
			dom:dom
		});
				
	}
	
	function createMergedHTML(obj, objectType, callback){

		sb.data.db.obj.getWhere(objectType, {id:obj.related_object, childObjs:1}, function(mergeObj){

			sb.data.db.obj.getBlueprint(objectType, function(mergeBP){
			
				getAllBluprints(mergeBP, function(allBlueprints){
					
					var htmlString = obj.html_string;
								
					mergeObj = mergeObj[0];					

					if(obj.status == 'Signed'){
						
						var searchIndex = [{
							term:'{{PLEASE SIGN HERE}}',
							value:'<img width="300px" src="https://pagoda.voltz.software/_repos/_production/pagoda/_files/_instances/contracts/'+ obj.signatures.loc +'"> - '+ obj.signer_name +' @ ' + moment(obj.signatures.date_created).format('M/DD/YYYY h:mm:ss a') +' - '+ obj.signer_ip
						}];
						
					}else{
						
						var searchIndex = [{
							term:'{{PLEASE SIGN HERE}}',
							value:'<span class="signtaureLine" style="font-weight:bold;">PLEASE SIGN HERE</span>'
						}];
						
					}

					_.each(mergeBP, function(bpObj, name){

						searchIndex.push({
							term:'{{'+ objectType +'.'+ name +'}}',
							value:mergeObj[name]
						});
						
					});

					_.each(allBlueprints, function(obj){
						
						_.each(obj.blueprint, function(bpObj, name){
																										
							if(mergeObj[obj.bp_name]){
								var searchValue = mergeObj[obj.bp_name][name];
							}else{
								var searchValue = '';
							}
							
							searchIndex.push(
								{
									term:'{{'+obj.bp_name+'.'+name+'}}',
									value:searchValue
								}
							);
																			
						});
						
					});
												
					_.each(searchIndex, function(i){
						
						htmlString = htmlString.replace(new RegExp(i.term, "g"), i.value);
						
					});
					
					callback(htmlString);
									
				});
			
			});
									
		}, true);
		
	}
	
	function defaultOpportunityNote(dom){
		
		checkSettingsObject(function(settings){
			
			var noteString = defaultOppNoteText;
			if(settings.default_opp_note){
				noteString = settings.default_opp_note;
			}
			
			dom.empty();
					
			dom.makeNode('form', 'form', {
				message:{
					name:'message',
					label:'Enter the default note below',
					type:'textbox',
					rows:8,
					value:noteString,
					wysiwyg:true
				}
			});
			
			dom.makeNode('break', 'div', {text:'<br />'});
			
			dom.makeNode('save', 'button', {css:'pda-btn-green', text:'Save'})
				.notify('click', {
					type:'workorders-run',
					data:{
						run:function(dom){
							
							dom.save.loading();
							
							var formInfo = dom.form.process();
							
							checkSettingsObject(function(settings){
								
								settings.default_opp_note = formInfo.fields.message.value;
								
								sb.data.db.obj.update('workorder_system', settings, function(updated){
								
									sb.dom.alerts.alert('Saved!', '', 'success');
									
									dom.save.loading(false);
								
								});
								
							});
							
						}.bind({}, dom)
					}
				}, sb.moduleId);
			
			dom.patch();

			
		})
		
	}
	
	function defaultOpportunityFollowUpDate(dom){
		
		checkSettingsObject(function(settings){

			dom.empty();
			
			dom.makeNode('title', 'div', {text:'Set the default follow up date', css:'ui small header'});
			
			var formObj = {
					amountOfTime:{
						name:'amountOfTime',
						label:'Amount Of Time',
						type:'number',
						value:1
					},
					typeOfTime:{
						name:'typeOfTime',
						label:'',
						type:'select',
						options:[
							{
								name:'Hour(s)',
								value:'hours'
							},
							{
								name:'Day(s)',
								value:'days'
							},
							{
								name:'Week(s)',
								value:'weeks'
							}
						]
					}
				};
			
			if(settings.follow_up_type){
				
				_.each(formObj.typeOfTime.options, function(options){
					
					if(options.value === settings.follow_up_type){
						options.selected = true;
					}
					
				});
				
				formObj.amountOfTime.value = settings.follow_up_time;
				
			}
			
			dom.makeNode('form', 'form', formObj);
			
			dom.makeNode('break', 'div', {text:'<br />'});
			
			dom.makeNode('save', 'button', {text:'Save', css:'pda-btn-green'})
				.notify('click', {
					type:'workorders-run',
					data:{
						run:function(dom){
							
							var formInfo = dom.form.process();
							
							if(formInfo.completed == false){
								sb.dom.alerts.alert('', 'Please fill out the entire form', 'error');
								return;
							}
							
							dom.save.loading();
														
							settings.follow_up_time = formInfo.fields.amountOfTime.value;
							settings.follow_up_type = formInfo.fields.typeOfTime.value;
							
							sb.data.db.obj.update('workorder_system', settings, function(updated){
							
								sb.dom.alerts.alert('Success', '', 'success');
								
								dom.save.loading(false);
								
							});
														
						}.bind({}, dom)
					}
				}, sb.moduleId);
			
			dom.patch();
		
		});
		
	}
	
	function getAdditionalContacts(companyObj, callback){
			
		if(companyObj){
			
			sb.data.db.obj.getWhere('contacts', {company:companyObj.id}, function(addContacts){
				
				callback(addContacts);
				
			});
			
		}else{
			
			callback([]);
			
		}
		
	}
	
	function getAllBluprints(mainBP, callback){
			
		var bpsToGet = _.compact(_.map(mainBP, function(o, name){
			
				if(o.type == 'objectId' || o.type == 'objectIds'){
					
					o.bp_name = name;
					
					return o;
				
				}
				
			}));
			
		function getBlueprints(list, callback, count, ret){
			
			if(!count){
				count = 0;
			}
			
			if(!ret){
				ret = [];
			}
			
			if(list[count]){

				sb.data.db.obj.getBlueprint(list[count].objectType, function(bp){

					ret.push({
						type:list[count].objectType,
						blueprint:bp,
						bp_name:list[count].bp_name
					});
					
					count++;
					
					getBlueprints(list, callback, count, ret);
					
				});
				
			}else{
				
				callback(ret);
				
			}
			
		}

		getBlueprints(bpsToGet, function(allBlueprints){
			
			callback(allBlueprints);	
			
		});
					
	}
		
	function editEvent(dom, state, draw){

		var obj = state.object;
		
		dom.empty();
		//dom.makeNode('modal', 'modal', {});
		
		dom.makeNode('panel', 'container', {uiGrid:false});
		
		dom.panel.makeNode('btns', 'buttonGroup', {css:''});
		
		dom.panel.makeNode('btnsBreak', 'lineBreak', {});
		
		dom.panel.makeNode('body', 'container', {});
				
		dom.panel.btns.makeNode('back', 'button', {text:'<i class="fa fa-times"></i> Close', css:'pda-btnOutline-red'}).notify('click', {
			type:'app-navigate-to',
			data:{
				itemId:'workorders',
				viewId:'single-'+obj.id
			}	
		}, sb.moduleId);
				
		dom.panel.btns.makeNode('save', 'button', {text: '<i class="fa fa-circle-o-notch fa-spin"></i> Loading 1 of 6', css:'pda-btn-primary'});
		
		var formDom = dom.panel.body;
		
		formDom.makeNode('col1', 'column', {w:8});
		formDom.makeNode('col2', 'column', {w:8});

		formDom.col1.makeNode('header', 'headerText', {text:'Workorder Setup'});			
		formDom.col1.makeNode('cont', 'container', {uiGrid:false}).makeNode('loading', 'loader', {size:'large', css:'text-center'});
		
		formDom.col2.makeNode('header', 'headerText', {text:'Workorder Details'});
		formDom.col2.makeNode('cont', 'container', {uiGrid:false}).makeNode('loading', 'loader', {size:'large', css:'text-center'});
		
		//dom.patch();
		
		sb.data.db.obj.getBlueprint('events', function(blueprint){
						
			sb.data.db.obj.getAll('users', function(users){
											
				sb.data.db.obj.getAll('event_type_options', function(event_type_options){
									
					sb.data.db.obj.getAll('event_service_style', function(event_service_style){
						
						sb.data.db.obj.getAll('staff_base', function(venues){
								
							getAdditionalContacts(obj.main_contact.company, function(addContacts){
								
								var infoForm = {
										name:{
											name:'name',
											label:'Name',
											type:'text',
											value:obj.name
										},
										start_date:{
											name:'start_date',
											label:'Start Date',
											type:'date',
											value:obj.start_date
										},
										manager:{
											name:'manager',
											label:'Manager',
											type:'select',
											options: [
												{
													value: 0,
													name: 'Please Select'
												}
											]
										}
									},
									detailsForm = {
										type:{
											name:'type',
											label:'Type',
											type:'select',
											options:_.map(event_type_options, function(o){ return {value:o.id, name:o.name}; })
										},
										additional_contacts:{
											name:'additional_contacts',
											label:'Additional Contacts',
											type:'checkbox',
											options:[]
										}
									};

									_.map(users, function(o){

										var spRet = {value:o.id, name:o.fname +' '+ o.lname};
										var epRet = {value:o.id, name:o.fname +' '+ o.lname};
									 
										if(obj.manager != null && o.id === obj.manager.id){ 
											
											spRet.selected = true; } 
											
										infoForm.manager.options.push(spRet);
																				
									});
									
									var currentAddContacts = _.pluck(obj.additional_contacts, 'id');
									_.map(
										_.reject(addContacts, function(user){
											return user.id == obj.main_contact.id;
										}),
										function(o){
											
											if(currentAddContacts.indexOf(o.id) > -1){
												
												detailsForm.additional_contacts.options.push({
													value:o.id,
													name:'additional_contacts',
													label:o.fname +' '+ o.lname,
													checked:true
												});
												
											}else{
												
												detailsForm.additional_contacts.options.push({
													value:o.id,
													name:'additional_contacts',
													label:o.fname +' '+ o.lname
												});
												
											}
										
									});										
									
									delete dom.panel.body.col1.cont.loading;
									delete dom.panel.body.col2.cont.loading;
									
									dom.panel.btns.makeNode('delete', 'button', {text:'<i class="fa fa-trash-o"></i> Delete', css:'pda-btn-red'}).notify('click', {
										type: 'workorders-run',
										data: { run:eraseEvent.bind(dom, obj) }
									}, sb.moduleId);
									
									dom.panel.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
										type:'workorders-run',
										data:{
											run:saveEventChanges.bind(dom, obj)
										}
									}, sb.moduleId);
									
									formDom.col1.cont.makeNode('form', 'form', infoForm);
									formDom.col2.cont.makeNode('form', 'form', detailsForm);
										
								dom.patch();		
																
								draw(false);
								
							});		
			
						});
				
					});
				
				});
				
			});
						
		});

		
	}
	
	function editSystemSettings(dom){
		
		checkSettingsObject(function(settingsObj){
			
			dom.empty();
															
			dom.makeNode('col', 'column', {width:12});
			
			dom.makeNode('btns', 'buttonGroup', {css:''});
			
			dom.col.makeNode('form', 'form', {
				text:{
					name:'text',
					type:'textbox',
					label:'Disclaimer Text',
					value:settingsObj.signature_disclaimer,
					wysiwyg: {
						height: null,                 
						minHeight: 300,           
						maxHeight: null,            
						focus: true,
						toolbar: true
					}
				}
			});
			
			dom.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save Changes', css:'pda-btn-green'}).notify('click', {
				type:'contracts-run',
				data:{
					run:function(settingsObj){
						
						var dom = this,
							formInfo = dom.col.form.process();
						
						if(formInfo.completed == false){
							sb.dom.alerts.alert('Error', 'Disclaimer text is required.', 'error');
							return;
						}
						
						dom.btns.save.loading();
						
						settingsObj.signature_disclaimer = formInfo.fields.text.value;
						
						sb.data.db.obj.update('workorder_system', settingsObj, function(updated){
							
							dom.btns.save.loading(false);
							
							sb.dom.alerts.alert('Saved!', '', 'success');
							
							setTimeout(function(){
								
								editSystemSettings(dom);
								
							}, 1000);
							
						});
																			
					}.bind(dom, settingsObj)
				}
			}, sb.moduleId);
			
			dom.patch();			
			
		});		
		
	}
	
	function emailTemplateSettings(dom){
		
		checkSettingsObject(function(settingsObj){
			
			dom.empty();
		
			//dom.makeNode('title', 'headerText', {text:'5. Proposal Email Template', size:'small'});
			
			//dom.makeNode('titleBreak', 'lineBreak', {});
			
			dom.makeNode('form', 'form', {
				subject:{
					name:'subject',
					type:'text',
					label:'Subject',
					value:settingsObj.request_email_subject
				},
				text:{
					name:'text',
					type:'textbox',
					label:'Body',
					value:settingsObj.request_email,
					wysiwyg: {
						height: null,                 
						minHeight: 300,           
						maxHeight: null,            
						focus: true,
						toolbar: true
					}
				}
			});
			
			//dom.makeNode('formBreak', 'lineBreak', {});
			
			dom.makeNode('tags', 'div', {css:'ui info message'});
			dom.tags.makeNode('tagtitle', 'div', {text:'Use these merge tags in your email template above.', css:'ui header'});
			
			dom.makeNode('tagBreak', 'lineBreak', {});
			
			dom.tags.makeNode('fname', 'div', {css:'ui teal label', text:'*|first_name|*'});
			dom.tags.makeNode('lname', 'div', {css:'ui teal label', text:'*|last_name|*'});
			dom.tags.makeNode('woname', 'div', {css:'ui teal label', text:'*|work_order_name|*'});
			dom.tags.makeNode('venue', 'div', {css:'ui teal label', text:'*|venue|*'});
			dom.tags.makeNode('start', 'div', {css:'ui teal label', text:'*|start_date|*'});
			dom.tags.makeNode('managerfname', 'div', {css:'ui teal label', text:'*|manager_first_name|*'});
			dom.tags.makeNode('managerlname', 'div', {css:'ui teal label', text:'*|manager_last_name|*'});
			
			dom.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save Changes', css:'pda-btn-green pda-pull-left'}).notify('click', {
				type:'contracts-run',
				data:{
					run:function(settingsObj){
						
						var dom = this,
							formInfo = dom.form.process();
						
						if(formInfo.completed == false){
							sb.dom.alerts.alert('Error', 'Disclaimer text is required.', 'error');
							return;
						}
						
						dom.save.loading();
						
						settingsObj.request_email = formInfo.fields.text.value;
						settingsObj.request_email_subject = formInfo.fields.subject.value;
						
						sb.data.db.obj.update('workorder_system', settingsObj, function(updated){
							
							dom.save.loading(false);

							sb.dom.alerts.alert('Saved!', '', 'success');
							
							setTimeout(function(){
								
								emailTemplateSettings(dom);
								
							}, 1000);
							
						});
																			
					}.bind(dom, settingsObj)
				}
			}, sb.moduleId);
			
			dom.patch();
			
		});
		
	}
	
	function eraseEvent(obj){
			
		sb.dom.alerts.ask({
			title: 'Are you sure?',
			text: ''
		}, function(resp){
			
			if(resp){
				
				swal.disableButtons();
				
				var objsToDelete = [
							{
								objectType:'work_orders',
								id:obj.id
							}
						];
															
				function deleteObjects(allObjects, callback, count){
					
					if(!count){
						count = 0;
					}
					
					sb.data.db.obj.erase(allObjects[count].objectType, allObjects[count].id, function(done){
						
						count++;
						
						if(count == allObjects.length){
							
							callback(count);
							
						}else{
							
							deleteObjects(allObjects, callback, count);
							
						}
						
					});
					
				}
				
				sb.data.db.obj.getWhere('invoices', {related_object:obj.id, active:'Yes'}, function(invoices){
						
					sb.data.db.obj.getWhere('inventory_menu', {related:obj.id, active:'Yes'}, function(menus){
						
						sb.data.db.obj.getWhere('contracts', {related_object:obj.id, active:'Yes'}, function(contracts){
							
							sb.data.db.obj.getWhere('proposals', {main_object:obj.id}, function(props){
								
								_.each(props, function(prop){
								
									objsToDelete.push({
										objectType:'proposals',
										id:prop.id
									});
									
								});
								
								_.each(invoices, function(inv){
								
									objsToDelete.push({
										objectType:'invoices',
										id:inv.id
									});
									
								});
								
								_.each(menus, function(menu){
									
									objsToDelete.push({
										objectType:'inventory_menu',
										id:menu.id
									});
									
								});
								
								_.each(contracts, function(contract){
									
									objsToDelete.push({
										objectType:'contracts',
										id:contract.id
									});
									
								});
								
								deleteObjects(objsToDelete, function(deleted){
						
									sb.dom.alerts.alert('Success!', '', 'success');
									
									sb.notify({
										type:'app-remove-main-navigation-item',
										data:{
											itemId:'workorders',
											viewId:'single-'+obj.id
										}
									});
									
									sb.notify({
										type:'app-navigate-to',
										data:{
											itemId:'workorders',
											viewId:'table'
										}
									});
									
								});	
								
							});
						
						});
						
					});
					
				});	
				
			}
			
		});
		
	}
	


	function homeScreenView(dom, stats){

		function initialView(dom){

			// recently created events
			dom.routes.makeNode('col1', 'column', {width:12});	
			dom.routes.col1.makeNode('title', 'headerText', {css:'', text:'Events Dashboard', size:''});
			dom.routes.col1.makeNode('btns', 'buttonGroup', {css:'pull-left'});
			dom.routes.col1.makeNode('break', 'lineBreak', {});
			
			dom.routes.col1.makeNode('col1', 'column', {width:12});				
			dom.routes.col1.col1.makeNode('cont', 'container', {css:'pda-container'});
			
			dom.routes.col1.btns.makeNode('viewAgreements', 'button', {text:'<i class="fa fa-snowflake-o"></i> All Events', css:'pda-btn-blue'}).notify('click', {
				type:'eventComponent-run',
				data:{
					run:function(tableUI){
						
						components.table.notify({
							type:'crud-table-back-to-table',
							data:{}
						});
																											
					}.bind(this, tableUI)
				}
			}, sb.moduleId);

			dom.routes.col1.btns.makeNode('viewContacts', 'button', {text:'<i class="fa fa-users"></i> All Contacts', css:'pda-btn-blue'}).notify('click', {
				type:'eventComponent-run',
				data:{
					run:function(){
	
						/*
sb.notify({
							type:'app-navigate-to',
							data:{
								itemId:'contacts',
								viewId:{
									id:'single-'+obj.id,
									type:'table-single-item',
									title:obj.fname +' '+ obj.lname,
									icon:'<i class="fa fa-user"></i>',
									setup:{
										objectType:'contacts'
									},
									dom:singleState,
									rowObj:obj,
									removable:true,
									parent:'table'
								}
							}
						});
*/
											
					}.bind(this)
				}
			}, sb.moduleId);
			
			/*
dom.routes.col1.btns.makeNode('viewStats', 'button', {text:'<i class="fa fa-bar-chart"></i> Stats', css:'pda-btn-primary'}).notify('click', {
				type:'eventComponent-run',
				data:{
					run:homeScreenView.bind(this, dom, true)
				}
			}, sb.moduleId);
*/
						
			dom.routes.col1.col1.cont.makeNode('tableTitle', 'headerText', {css:'text-left', text:'5 Recently Created Events', size:'x-small'});
			dom.routes.col1.col1.cont.makeNode('info', 'container', {});
			dom.routes.col1.col1.cont.info.makeNode('loading', 'text', {text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
			
			// next events
			dom.routes.col1.makeNode('col2', 'column', {width:12})
				.makeNode('cont', 'container', {css:'pda-container'});
			
			dom.routes.col1.col2.cont.makeNode('tableTitle', 'headerText', {css:'text-left', text:'Next 5 Events', size:'x-small'});
			dom.routes.col1.col2.cont.makeNode('info', 'container', {});
			dom.routes.col1.col2.cont.info.makeNode('loading', 'text', {text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
			
/*
			dom.routes.col1.makeNode('lineBreak', 'lineBreak', {});
			dom.routes.col1.makeNode('contBreak', 'container', {css:'pda-panel-blue'});
*/
				
			dom.patch();
			
			sb.data.db.obj.getAll('events', function(objs){
				
				delete dom.routes.col1.col1.cont.info.loading;
				
				dom.routes.col1.col1.cont.info.makeNode('table', 'table', {
					css: 'table-hover table-condensed table-responsive',
					columns: {
						btns: '',
						date_created: 'Date Created',
						start_date: 'Start Date',
						event_name:'Event Name',
						service_style:'Service Style',
						venue:'Venue',
						main_contact: 'Contact',
						guest_count:'Guest Count'
					}
				});
				
				_.each(objs.data, function(a){

					var mainContact = 'No Contact on File';
					
					if (a.main_contact != null){
						
						mainContact = a.main_contact.fname +' '+ a.main_contact.lname;
						
					}

					dom.routes.col1.col1.cont.info.table.makeRow(
						'row-'+a.id,
						['', moment(a.date_created).format('M/D/YYYY h:mm a'),moment(a.start_date).format('M/D/YYYY h:mm a'), a.event_name, a.service_style.name +' - '+ a.event_type.name, a.venue.name, mainContact, a.guest_count]
					);

					dom.routes.col1.col1.cont.info.table.body['row-'+a.id].btns.makeNode('button', 'button', {text:'<p class="text-center"><i class="text-center fa fa-eye fa-2x"></i></p>', css:'pda-transparent', dataId: a.id}).notify('click', {
						type:'eventComponent-run',
						data:{
							run:function(a, data){																

								components.table.notify({
									type:'crud-table-row-link-clicked',
									data:{
										type:'tab',
										header: a.event_name,
										dataId: data.dataId,
										action: singleEventView,
										object: a
									}
								});					
														
							}.bind(this, a)
						}
					}, sb.moduleId);
					
				}, dom);
				
				dom.routes.col1.col1.cont.info.patch();
										
			}, 2, {
				page:0,
				sortCol:'date_created',
				sortDir:'desc',
				pageLength:5
			});
			
			sb.data.db.obj.getWhere('events', {
				status:236496, 
				childObjs:2, 
				start_date:{
					type:'between',
					start:moment().subtract(1, 'month').startOf('day').unix(),
					end:moment().endOf('year').unix()
				},
				paged:{
					page:0,
					sortCol:'start_date',
					sortDir:'asc',
					pageLength:5
				}
			}, function(objs){
	
				delete dom.routes.col1.col2.cont.info.loading;
				
				dom.routes.col1.col2.cont.info.makeNode('table', 'table', {
					css: 'table-hover table-condensed table-responsive',
					columns: {
						btns: '',
						start_date: 'Start Date',
						event_name:'Event Name',
						service_style:'Service Style',
						venue:'Venue',
						main_contact: 'Contact',
						guest_count:'Guest Count',
						event_specialist:'Event Specialist'					}
				});
				
				_.each(objs.data, function(a){
					
					dom.routes.col1.col2.cont.info.table.makeRow(
						'row-'+a.id,
						['', moment(a.start_date).format('M/D/YYYY h:mm a'), a.event_name, a.service_style.name +' - '+ a.event_type.name, a.venue.name, a.main_contact.fname +' '+ a.main_contact.lname, a.guest_count, a.event_specialist.fname +' '+ a.event_specialist.lname]
					);
					
					dom.routes.col1.col2.cont.info.table.body['row-'+a.id].btns.makeNode('button', 'button', {text:'<p class="text-center"><i class="text-center fa fa-eye fa-2x"></i></p>', css:'pda-transparent'}).notify('click', {
						type:'eventComponent-run',
						data:{
							run:function(tableUI, obj){
								
								components.table.notify({
									type:'crud-table-row-link-clicked',
									data:{
										type:'tab',
										object:obj
									}
								});
								
							}.bind(this, tableUI, a)
						}
					}, sb.moduleId);
					
				});
								
				dom.routes.col1.col2.cont.info.patch();
															
			});
				
			sb.data.db.obj.getWhere('routes', {run_date:{type:'between', start:moment().startOf('day').unix(), end:moment().endOf('day').unix()}, childObjs:2, paged:{
				page:0,
				sortCol:'run_date',
				pageLength:20
			}}, function(tickets){
				
							
			});
			
		}
		
		function statsView(dom){
			
			dom.routes.empty();
			
			dom.routes.makeNode('stats', 'container', {css:'pda-container pda-Panel pda-panel-blue pda-background-gray'});
			
			dom.routes.stats.makeNode('btns', 'buttonGroup', {css:'pull-right'});
			dom.routes.stats.btns.makeNode('back', 'button', {text:'Close <i class="fa fa-times"></i>', css:'pda-btn-red'}).notify('click', {
				type:'eventComponent-run',
				data:{
					run:homeScreenView.bind(this, dom, false)
				}
			}, sb.moduleId);
			
			dom.routes.stats.makeNode('header', 'headerText', {text:'Stats'});
			
			dom.routes.stats.makeNode('ticketStatus', 'headerText', {text:'Events', size:'small'});
			dom.routes.stats.makeNode('ticketStatusPie', 'column', {width:3}).makeNode('loading', 'text', {text:'<i class="fa fa-circle-o-notch fa-spin></i>'});
			dom.routes.stats.makeNode('servicing', 'column', {width:3}).makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-green'}).makeNode('title', 'headerText', {text:'<small>Being Serviced</small><br />', css:'text-center', size:''});			
			dom.routes.stats.makeNode('other', 'column', {width:3}).makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-orange'}).makeNode('title', 'headerText', {text:'<small>Other Tickets</small><br />', css:'text-center', size:''});			
			dom.routes.stats.makeNode('totalTickets', 'column', {width:3}).makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-blue'}).makeNode('title', 'headerText', {text:'<small>Total Tickets</small><br />', css:'text-center', size:''});			

			dom.routes.stats.makeNode('averageServiceTime', 'column', {width:3}).makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-blue'}).makeNode('title', 'headerText', {text:'<small>Avg Service Time</small><br />', css:'text-center', size:''});			
			dom.routes.stats.makeNode('serviceTime', 'column', {width:3}).makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-blue'}).makeNode('title', 'headerText', {text:'<small>Total Service Time</small><br />', css:'text-center', size:''});			
			dom.routes.stats.makeNode('break1', 'lineBreak', {});
			
			dom.routes.stats.makeNode('agreementStatus', 'headerText', {text:'Inventory', size:'small'});
			//dom.routes.stats.makeNode('agreementsPie', 'column', {width:3});
			dom.routes.stats.makeNode('totalAgreements', 'column', {width:4}).makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-blue'}).makeNode('title', 'headerText', {text:'<small>Total Agreements</small><br />', css:'text-center', size:''});			
			dom.routes.stats.makeNode('newAgreements', 'column', {width:4}).makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-blue'}).makeNode('title', 'headerText', {text:'<small>Created in the last 7 days</small><br />', css:'text-center', size:''});			
			dom.routes.stats.makeNode('otherAgreements', 'column', {width:4}).makeNode('cont', 'container', {css:'pda-container pda-Panel pda-panel-blue'}).makeNode('title', 'headerText', {text:'<small>Other Tickets</small><br />', css:'text-center', size:''});			

			dom.routes.patch();
											
			sb.data.db.obj.getSum('service_tickets', 'timer_total', {
				groupBy:'status',
				groupOn:'status',
				dateRange:{
					start:moment().startOf('year').format('YYYY-MM-DD HH:mm:ss.SS'),
					end:moment().endOf('day').format('YYYY-MM-DD HH:mm:ss.SS')
				}
			}, function(data){
				
				function getRandomColor(){
		    
				    var letters = '0123456789ABCDEF',
				    	color = '#';
				    
				    for(var i = 0; i < 6; i++) {
				    
				        color += letters[Math.floor(Math.random() * 16)];
				    
				    }
				    
				    return color;
				}
				
				var colors = [];
			
				_.each(data, function(o){
					
					colors.push(getRandomColor());
					
				});

				dom.routes.stats.servicing.cont.makeNode('title', 'headerText', {text:'<small>Being Serviced</small><br />'+ _.reduce(data, function(memo, o){ if(o.grouped == 'servicing'){return memo + o.grouped_total;}else{return memo;} }, 0), css:'text-center', size:''});			
				dom.routes.stats.other.cont.makeNode('title', 'headerText', {text:'<small>Other Tickets</small><br />'+ _.reduce(data, function(memo, o){ if(o.grouped != 'servicing'){return memo + o.grouped_total;}else{return memo;} }, 0), css:'text-center', size:''});			
				dom.routes.stats.totalTickets.cont.makeNode('title', 'headerText', {text:'<small>Total Tickets</small><br />'+ _.reduce(data, function(memo, o){ return memo + o.grouped_total; }, 0), css:'text-center', size:''});			

				dom.routes.stats.averageServiceTime.cont.makeNode('title', 'headerText', {text:'<small>Avg Service Time</small><br />'+ ((_.reduce(data, function(memo, o){ return memo + o.sum }, 0) / 60) / _.reduce(data, function(memo, o){ return memo + o.grouped_total }, 0)).toFixed(2), css:'text-center', size:''});			
				dom.routes.stats.serviceTime.cont.makeNode('title', 'headerText', {text:'<small>Total Service Time</small><br />'+ (_.reduce(data, function(memo, o){ return memo + o.sum }, 0) / 60) +'<small> minutes</small>', css:'text-center', size:''});			

				delete dom.routes.stats.ticketStatusPie.loading;
				dom.routes.stats.ticketStatusPie.makeNode('chart', 'chart', {
					type:'pie',
					data:{
				        labels: _.pluck(data, 'grouped'),
				        datasets: [{
				            data: _.pluck(data, 'grouped_total'),
				            backgroundColor:colors
				        }]
				    },
				    options:{}
				});
								
				dom.routes.stats.patch();

			});
							
		}
				
		dom.makeNode('routes', 'container', {});
		
		dom.patch();
		
		if(stats === true){
			
			statsView(dom);
			
		}else{
			
			initialView(dom);
			
		}
				
	}
	
	function packlistCheckpointsForm(ui){
		
		// !TODO: add loading gif while saving
		
		// get setting object if it exists
		sb.data.db.obj.getAll('packlist_checkpoints', function(packlist_checkpoints_list){
			
			sb.data.db.obj.getAll('inventory_service', function(inventory_services){
			
				// limit this to one object
				var	p_cs = packlist_checkpoints_list[0],
					services = inventory_services;
				
				if(p_cs === undefined){
					p_cs = {
						checkpoints: []
					};
				}
				
				function loading_state(is_loading){
					
					if(is_loading){
						
						this.header.makeNode('loader', 'loader', {css: 'pull-right', size: 'small'});
						
					}else{
						
						delete this.header.loader;
						
					}
					
					ui.header.patch();
					
				}
				
				function remove_checkpoint(p_cs, data){
					
					p_cs.checkpoints = _.filter(p_cs.checkpoints, function(cp){

						return data.checkpoint.sortId !== cp.sortId;
						
					});
					
					sb.dom.alerts.ask({
						
							title: 'Remove checkpoint?',
							text: '',
							closeOnConfirm: true,
						
						}, function(response){
							
							if(response){
								
								ui.loading(true);
								
								sb.data.db.obj.save([p_cs], function(response){
									
									ui.loading(false);
									ui.cps();
								
								});

							}
														
						}
						
					);
					
				}
				
				function save_packlist_checkpoints(p_cs, data){
					
					var formData = ui.editArea.form.process().fields;
					
					if(!formData.hasOwnProperty('managing_types')){
						
						sb.dom.alerts.alert('Incomplete', 'You need to select at least one managing staff type for this checkpoint to continue.', 'error');
						
						return false;
						
					}
					
					ui.editArea.save.loading(true);
					
					// editing
					if(data !== undefined && data.hasOwnProperty('checkpoint') && data.checkpoint !== undefined && data.checkpoint.hasOwnProperty('sortId')){

						var to_update = _.findWhere(p_cs.checkpoints, {sortId: parseInt(data.checkpoint.sortId)});

						to_update.name = formData.name.value;
						to_update.description = formData.description.value;
						to_update.managing_types = formData.managing_types.value;
					
					// creating new	
					}else{
						
						var new_checkpoint = {
						
							name: formData.name.value,
							description: formData.description.value,
							sortId: data.sortId,
							managing_types: formData.managing_types.value
							
						};
						
						var update_id = data.sortId;
		
						// update sort ids
						_.each(p_cs.checkpoints, function(cp){
							
							if(cp.sortId === update_id){
								cp.sortId++;
								update_id = cp.sortId;
							}
							
						});
						
						p_cs.checkpoints.push(new_checkpoint);

					}
										
					// remove initial create step button if it exists
					if(ui.checkpoints.hasOwnProperty('addBefore')){
						delete ui.checkpoints.addBefore;
					}
					
					// save
					p_cs.object_bp_type = 'packlist_checkpoints';
					ui.loading(true);

					sb.data.db.obj.save([p_cs], function(response){
						
						ui.loading(false);
						if(response){
							
							p_cs.id = response.id;
							
							ui.cps();

							ui.editArea.empty();
							ui.editArea.patch();
							
						}else{
							
							ui.cps();

							ui.editArea.empty();
							ui.editArea.patch();
							
							sb.data.db.alerts.alert('Error!', 'There was an error processing the request. Please refresh and try again.', 'error');
							
						}
						
					});
					
				}
				
				function new_checkpoint_form(p_cs, data){
					
					// toggle btn if it exists
					if(data.hasOwnProperty('btn')){
						data.btn.css('pda-btn-orange pda-btn-x-small pull-right');
					}
					
					var form_args = {
						name: {
							name: 'name',
							type: 'text',
							value: '',
							label: 'Name'
						},
						description: {
							name: 'description',
							type: 'textbox',
							value: '',
							label: 'Description'
						},
						managing_types: {
							name: 'managing_types',
							type: 'checkbox',
							value: '',
							label: 'Who can check this off?',
							options: []
						}
					};
					
					_.each(services, function(service){
						
						form_args.managing_types.options.push({
							name: 'managing_types',
							value: service.id,
							label: service.name
						});
						
					});
					
					// pre-fill form for editing
					if(data.hasOwnProperty('checkpoint')){
						
						form_args.name.value = data.checkpoint.name;
						form_args.description.value = data.checkpoint.description;
						form_args.managing_types.value = data.checkpoint.managing_types;
						
					}
					
					this.makeNode('form', 'form', form_args);
					this.makeNode('save', 'button', {
						text: '<i class="fa fa-check" aria-hidden="true"></i> Save', 
						css: 'pda-btn-green pda-btn-small pull-right'})
						.notify('click', {
							type: 'eventComponent-run',
							data: {
								run: save_packlist_checkpoints.bind({}, p_cs),
								sortId: data.sortId,
								checkpoint: data.checkpoint
							}
						});
					
					this.patch();
					
				}
				
				function draw_checkpoints(p_cs){

					var afterSortId = 0,
						firstSortId = _.min(_.pluck(p_cs.checkpoints, 'sortId')),
						lastSortId = _.max(_.pluck(p_cs.checkpoints, 'sortId'));
						
					var beforeString = '',
						afterString = '',
						btnBefore = false;
					
					this.checkpoints.empty();
					
					if(p_cs.checkpoints.length === 0){
						
						this.checkpoints.makeNode('btnBefore-'+ 0, 'column', {width: 1})
							.makeNode('add-before', 'button', {text: '<i class="fa fa-plus" aria-hidden="true"></i>', css: 'pda-btn-x-small pda-btn-green pda-center'})
							.notify('click', {
								type: 'eventComponent-run',
								data: {
									sortId: 0,
									run: new_checkpoint_form.bind(ui.editArea, p_cs)
								}
							}, sb.moduleId);
						
					}else{
						
						_.chain(p_cs.checkpoints)
							.sortBy(
								function(cp){
									return cp.sortId;
								}
							).each(function(checkpoint){
								
								if(checkpoint.sortId === firstSortId){
									beforeString = '';
									btnBefore = true;
								}else{
									beforeString = '<i class="fa fa-angle-double-right" aria-hidden="true"></i> ';
									btnBefore = false;
								}
								
								if(checkpoint.sortId < lastSortId){
									afterString = ' <i class="fa fa-angle-double-right" aria-hidden="true"></i>';
								}else{
									afterString = '';
								}
								
								if(btnBefore){
									
									this.checkpoints.makeNode('btnBefore-'+ checkpoint.sortId, 'column', {width: 1})
										.makeNode('add-before', 'button', {text: '<i class="fa fa-plus" aria-hidden="true"></i>', css: 'pda-btn-x-small pda-btn-green pda-center'})
										.notify('click', {
											type: 'eventComponent-run',
											data: {
												sortId: checkpoint.sortId,
												run: new_checkpoint_form.bind(ui.editArea, p_cs)
											}
										}, sb.moduleId);
									
								}
								
								// checkpoint container and display
								this.checkpoints.makeNode('cp-'+ checkpoint.sortId, 'column', {
										
										css: 'pda-Panel pda-panel-blue pda-container',
										width: 2
										
									});
								
								this.checkpoints.makeNode('btnAfter-'+ checkpoint.sortId, 'column', {width: 1})
									.makeNode('add-after', 'button', {text: '<i class="fa fa-plus" aria-hidden="true"></i>', css: 'pda-btn-x-small pda-btn-green pda-center'})
									.notify('click', {
										type: 'eventComponent-run',
										data: {
											sortId: checkpoint.sortId + 1,
											run: new_checkpoint_form.bind(ui.editArea, p_cs)
										}
									}, sb.moduleId);
								
								this.checkpoints['cp-'+ checkpoint.sortId].makeNode('name', 'headerText', {size: 'x-small', text: beforeString + checkpoint.name + afterString});
								
								// btns
								this.checkpoints['cp-'+ checkpoint.sortId].makeNode('btns', 'buttonGroup', {type: 'vertical', css: 'pull-right'});
								this.checkpoints['cp-'+ checkpoint.sortId].btns.makeNode('edit', 'button', {text: '<i class="fa fa-pencil" aria-hidden="true"></i>', css: 'pda-btnOutline-orange pda-transparent pda-btn-x-small'});
															
								this.checkpoints['cp-'+ checkpoint.sortId].btns.edit.notify('click', {
										type: 'eventComponent-run',
										data: {
											sortId: checkpoint.sortId,
											run: new_checkpoint_form.bind(ui.editArea, p_cs),
											checkpoint: checkpoint,
											btn: ui.checkpoints['cp-'+ checkpoint.sortId].edit
										}
									}, sb.moduleId);
									
								this.checkpoints['cp-'+ checkpoint.sortId].btns.makeNode('remove', 'button', {text: '<i class="fa fa-trash" aria-hidden="true"></i>', css: 'pda-btnOutline-red pda-transparent pda-btn-x-small'})
									.notify('click', {
										type: 'eventComponent-run',
										data: {
											checkpoint: checkpoint,
											run: remove_checkpoint.bind(ui, p_cs)
										}
									}, sb.moduleId);
									
								this.checkpoints['cp-'+ checkpoint.sortId].makeNode('description', 'text', {text: '<small><i class="text-muted"></i>'+ checkpoint.description +'</small>'});
								
								// managing types display
								var typesDisplayString = '',
									i = 0;
								_.each(checkpoint.managing_types, function(type){
									
									var typeObj = _.findWhere(services, {id: parseInt(type)});
									
									if(i > 0){
										typesDisplayString += ', ';
									}
									typesDisplayString += typeObj.name;
									i++;
									
								}, this);
								
								this.checkpoints['cp-'+ checkpoint.sortId].makeNode('managingTypes', 'text', {text: '<small><strong>'+ typesDisplayString +'</strong></small>'});
								
							}, this);
						
					}
					
					this.patch();
					
				}
				
				// clear loading gif from dom
				ui.empty();
				
				ui.loading = loading_state.bind(ui);
				
				// set up ui
				ui.makeNode('header', 'column', {width: 12, css: 'text-center'})
					.makeNode('h', 'headerText', {text: 'Current Packlist Checkpoints', size: 'x-small'});
				
				ui.makeNode('checkpoints', 'column', {width: 12});
				ui.makeNode('editArea', 'column', {width: 12});
				
				ui.cps = draw_checkpoints.bind(ui, p_cs);
				ui.patch();
				ui.cps();
				
			});
			
		}, 0);
		
	}
	
	function processUpdatedPricing(dom, obj, price, menuDom, priceDom){
		
		var total = 0;
		
		total = _.chain(price).reduce(function(memo, v, k){ return memo + v; }).value();
		
		priceDom.empty();
		
		priceDom.makeNode('header', 'div', {css:'description', text:'<b>Total Price</b>'});
		priceDom.makeNode('cont', 'div', {css:'ui huge fluid basic blue label', text:'$'+ (total/100).formatMoney()});
		priceDom.makeNode('meta', 'div', {css:'meta', text:'Estimated Value $' + (obj.potential_value/100).formatMoney(2) +'<br /><br />'});
		
		priceDom.makeNode('linkToCOAReport', 'div', {
			tag:'a',
			text:'Chart of Acct. <i class="external link icon"></i>',
			css:'ui link'
		}).notify('click', {
			type:'app-navigate-to',
			data:{
				itemId:'workorders',
				viewId:{
					id:'menu-'+ obj.id +'-coa-report',
					type:'notification',
					title:obj.name +' | Chart of accounts summary',
					icon:'<i class="fa fa-list-ul"></i>',
					removable:true,
					notification:function(dom, state, draw){
						
						sb.notify({
							type:'view-workorder-chart-of-accounts-report',
							data:{
								state:obj,
								dom:dom,
								draw:function(domCache){
									this(domCache)
								}.bind(draw)
							}
						});
						
					},
					viewState:{
						workorder:obj
					}
				}
			}
		}, sb.moduleId);
		
		priceDom.patch();	
		
		if(dom){
			
			sb.data.db.obj.update('proposals', {id:obj.proposal.id, pricing:price}, function(done){
					
				sb.notify({
					type: 'view-all-invoices',
					data: {
						domObj: dom,
						objectId: obj.proposal.id,
						contactId:obj.main_contact.id,
						objectType:obj.object_bp_type,
						dueDate: obj.proposal.event_start_date,
						price: price,
						balanceDom:priceDom,
						onUpdate:function(callback, invoices, deleteInvoices){
							
							if(deleteInvoices){
								var newInvoiceArray = [];
							}else{
								var newInvoiceArray = _.pluck(obj.proposal.invoices, 'id');
							}
							
							sb.data.db.obj.update('proposals', {id:obj.proposal.id, pricing:price, invoices:newInvoiceArray.concat(_.pluck(invoices, 'id'))}, function(done){
		
								if(obj.status == 'Paid'){
					
									sb.data.db.obj.update('work_orders', {id:obj.id, status:'Signed'}, function(updated){
											
										buildHeaderNav(menuDom, updated.status);
										
										menuDom.patch();
																	
										callback(true);
										
									});
										
								}else{
														
									callback(true);
									
								}
								
							});
							
						}
					}
				});
				
			});	
			
		}
		
	}
	
	function requireProposalApproval(dom){
		
		checkSettingsObject(function(settings){
			
			sb.data.db.obj.getAll('users', function(users){
console.log('settings',settings);				
				var formObj = {
						require:{
							name:'require',
							label:'Require approval?',
							type:'select',
							options:[
								{
									value:'no',
									name:'No'
								},
								{
									value:'yes',
									name:'Yes'
								}
							]
						},
						users:{
							name:'users',
							label:'Who should be notified for approval requests?',
							type:'checkbox',
							options:[]
						}
					};
				
				formObj.users.options = _.map(
					users,
					function(user){
						return {
							name:'users',
							label:user.fname +' '+ user.lname,
							value:user.id
						};
					}, []);
					
				if(settings.require_approval){
					formObj.require.value = settings.require_approval;
				}
				
				if(settings.approval_admin_users){
					formObj.users.value = settings.approval_admin_users;
				}
				
				dom.empty();
					
				dom.makeNode('help', 'div', {text:'Setting this to \'<b>Yes</b>\' will require all proposals on a Workorder be approved before sending to the client.'});
				
				dom.makeNode('helpbreak', 'div', {text:'<br />'});
					
				dom.makeNode('form', 'form', formObj);
					
				dom.makeNode('formBreak', 'div', {text:'<br />'});
		
				dom.makeNode('save', 'div', {text:'Save', css:'ui green button'})
					.notify('click', {
						type:'workorders-run',
						data:{
							run:function(dom){
								
								dom.save.loading();
								
								var requireApproval = dom.form.process().fields.require.value;
								var usersToNotify = dom.form.process().fields.users.value;
		
								checkSettingsObject(function(settings){
								
									settings.require_approval = requireApproval;
									settings.approval_admin_users = usersToNotify;
									
									sb.data.db.obj.update('workorder_system', {id:settings.id, require_approval:requireApproval, approval_admin_users:usersToNotify}, function(updated){
										
										dom.save.loading(false);
										dom.save.text('Saved!');
										
										setTimeout(function(){
											
											dom.save.loading(false);
											dom.save.text('Save');
											
										}, 1500);
										
									});							
									
								});
								
							}.bind({}, dom)
						}
					}, sb.moduleId);
				
				dom.patch();
				
			});
			
		});
				
	}
	
	function saveEventChanges(obj){	
		
		var form1 = this.panel.body.col1.cont.form.process(),
			form2 = this.panel.body.col2.cont.form.process(),
			newEvent = {
				id:obj.id
			};
			
		if(form1.completed == false || form2.completed == false){
			sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
			return;
		}
		
		this.panel.btns.save.loading();
			
		_.each(form1.fields, function(field, fieldName){
				
			newEvent[fieldName] = field.value;
			
		});
		
		_.each(form2.fields, function(field, fieldName){
			
			newEvent[fieldName] = field.value;
			
		});
		
		sb.data.db.obj.update('work_orders', newEvent, function(event){
			
			sb.notify({
				type: 'update-single-schedule',
				data: {
					parent: event,
					after: function() {
						
						sb.notify({
							type:'app-remove-main-navigation-item',
							data:{
								itemId:'contacts',
								viewId:'addWorkorder'
							}
						});
						
						sb.notify({
							type:'app-navigate-to',
							data:{
								itemId:'workorders',
								viewId:'single-'+event.id,
								redraw:true
							}
						});
						
					}
				}
			});			
			
		}, 2);			
		
	}
	
	function singleWorkorderViewNew(obj, dom, state, draw){
		
		function editWorkorder(dom, obj, users, button){

			button.loading();
			
			sb.data.db.obj.getAll('event_type_options', function(types){

				dom.makeNode('modal', 'modal', {
					onShow:function(){
						
						var formObj = {
								name:{
									type:'text',
									name:'name',
									label:'Work Order Name',
									value:obj.name
								},
								manager:{
									type:'select',
									name:'manager',
									label:'Manager',
									value:obj.manager.id,
									options:_.map(users, function(u){
										
										return {
												name:u.fname +' '+ u.lname,
												value:u.id
											};
										
									})
								},
								guest_count:{
									type:'number',
									name:'guest_count',
									label:'Guest Count',
									value:obj.guest_count
								},
								start_date:{
									type:'date',
									name:'start_date',
									label:'Start Date',
									value:obj.start_date,
									dateFormat:'M/D/YYYY h:mm a',
									dateType:'datetime'
								},
								end_date:{
									type:'date',
									name:'end_date',
									label:'End Date',
									value:obj.end_date,
									dateFormat:'M/D/YYYY h:mm a',
									dateType:'datetime'
								},
								followUp:{
									type:'date',
									name:'followUp',
									label:'Follow Up Date',
									value:obj.follow_up_date,
									dateFormat:'M/D/YYYY h:mm a',
									dateType:'datetime'
								},
								closing:{
									type:'date',
									name:'closing',
									label:'Closing Date',
									value:obj.potential_closing_date,
									dateFormat:'M/D/YYYY h:mm a',
									dateType:'datetime'
								},
								type:{
									type:'select',
									name:'type',
									label:'Type',
									value:obj.type.id,
									options:_.map(types, function(u){
										
										return {
												name:u.name,
												value:u.id
											};
										
									})
								},
								estimatedValue:{
									type:'usd',
									name:'estimatedValue',
									label:'Estimated Value',
									value:obj.potential_value
								}
							};
							
						dom.modal.body.makeNode('header', 'div', {text:'Edit the details', css:'ui huge header'});
						
						dom.modal.body.makeNode('form', 'form', formObj);
						
						dom.modal.body.makeNode('break', 'div', {text:'<br />'});
						
						dom.modal.body.makeNode('btns', 'div', {css:'ui buttons'});
						
						dom.modal.body.btns.makeNode('save', 'button', {text:'Save', css:'pda-btn-green'})
							.notify('click', {
								type:'workorders-run',
								data:{
									run:function(dom, obj){
										
										var formInfo = dom.modal.body.form.process();
										
										if(formInfo.completed == false){
											sb.dom.alerts.alert('All fields required.', 'Please fill out the entire form.', 'warning');
											return;
										}
										
										dom.modal.body.btns.save.loading();

										var saveObj = {
												id:obj.id,
												name:formInfo.fields.name.value,
												manager:formInfo.fields.manager.value,
												type:formInfo.fields.type.value,
												follow_up_date:formInfo.fields.followUp.value,
												potential_closing_date:formInfo.fields.closing.value,
												potential_value:formInfo.fields.estimatedValue.value,
												start_date:formInfo.fields.start_date.value,
												end_date:formInfo.fields.end_date.value,
												guest_count:formInfo.fields.guest_count.value,
												status:'Proposal'
											};
											
										sb.data.db.obj.update('work_orders', saveObj, function(savedObj){
											
											if(savedObj.proposal){
												
												sb.data.db.obj.update('proposals', {
													id:savedObj.proposal.id,
													start_date:savedObj.start_date,
													end_date:savedObj.end_date,
													status:'Proposal'
												}, function(updatedProposal){
																									
													if(updatedProposal.menu){
														
														sb.data.db.obj.update('inventory_menu', {
															id:updatedProposal.menu,
															start_date:savedObj.start_date,
															end_date:savedObj.end_date,
															date:savedObj.start_date
														}, function(menu){
															
															dom.modal.hide();
	
															if(savedObj.status == 'Prospecting'){
																opportunityView(dom, savedObj);
															}else{
																transferToWorkFlows(dom, savedObj);
															}
															
														});
														
													}else{
														
														dom.modal.hide();
	
														if(savedObj.status == 'Prospecting'){
															opportunityView(dom, savedObj);
														}else{
															transferToWorkFlows(dom, savedObj);
														}
														
													}
													
												});
												
											}else{
												
												dom.modal.hide();

												opportunityView(dom, savedObj);
												
											}
																						
										}, 3);
										
									}.bind({}, dom, obj)
								}
							}, sb.moduleId);
							
						dom.modal.body.btns.makeNode('close', 'button', {text:'Close', css:'pda-btn-orange'})
							.notify('click', {
								type:'workorders-run',
								data:{
									run:function(dom){
										
										dom.modal.hide();
										
									}.bind({}, dom)
								}
							}, sb.moduleId);
							
						dom.modal.body.btns.makeNode('delete', 'button', {text:'Delete', css:'pda-btn-red'}).notify('click', {
							type: 'workorders-run',
							data: { run:eraseEvent.bind(dom, obj) }
						}, sb.moduleId);	
						
						dom.modal.body.patch();
						
						button.loading(false);
						
					}
				});
				
				dom.patch();
				
				dom.modal.show();
				
			});
			
		}
		
		function opportunityView(dom, obj){
			
			dom.empty();
			
			var managerPop = {},
				typePop = {},
				followPop = {},
				closingPop = {},
				valuePop = {},
				taggedUsers = [],
				staff = [];
			
			checkSettingsObject(function(settings){
				
				sb.data.db.obj.getAll('event_type_options', function(event_type_options){
				
					sb.data.db.obj.getWhere('users', {enabled:1}, function(users){
						
						sb.data.db.obj.getWhere('staff', {id:{type:'contains', values:_.pluck(users, 'related_object')}, enabled:1}, function(staffList){
							
							_.each(users, function(user){
								
								if(_.pluck(users, 'id').indexOf(+sb.data.cookie.userId) > -1){
									
									staff.push(user);
									
								}
								
							});
							
							if(settings.default_opp_note){
								defaultOppNoteText = settings.default_opp_note;
							}
							
							if(obj.qualification_note){
								defaultOppNoteText = obj.qualification_note;
							}
							
							var opportunityDetails = {
									textbox:{
										name:'textbox',
										type:'textbox',
										label:'Qualification Details',
										value:defaultOppNoteText,
										wysiwyg:{
											hint:[{
												mentions: _.map(users, function(s){ return s.fname +' '+ s.lname }),
												match: /\B@(\w*)$/,
												search: function (keyword, callback) {
													
													var ret = _.map(this.mentions, function(item){
						
														if(item.search(keyword) > -1){
															return item;
														}
														
													});
													
													callback(_.compact(ret));
												
												},
												content: function (item) {
													taggedUsers.push(item);					
													return item;
												}
											}]
										}
									}
								};
							
							dom.makeNode('cols', 'div', {css:'ui stackable grid'});
							dom.makeNode('notesTasks', 'div', {css:'ui stackable grid'});
							
							dom.cols.makeNode('left', 'div', {css:'five wide column'})
								.makeNode('details', 'div', {css:'ui fluid'})
									.makeNode('content', 'div', {css:'content'});
							
							dom.cols.makeNode('right', 'div', {css:'eleven wide column'});
							
							dom.notesTasks.makeNode('tasks', 'div', {css:'five wide column'});
							dom.notesTasks.makeNode('notes', 'div', {css:'eleven wide column'});
							
							var leftCol = dom.cols.left.details.content;
							
							leftCol.makeNode('title', 'div', {css:'ui medium header', text:obj.name});
							
							dom.cols.right
								.makeNode('seperate', 'div', {})
									.makeNode('form', 'form', opportunityDetails);
							
							
							leftCol.makeNode('btns', 'div', {css:'three ui fluid buttons'});
							leftCol.btns.makeNode('edit', 'button', {text:'Edit', css:'pda-btn-orange'});
							leftCol.btns.makeNode('save', 'button', {text:'Save Notes', css:'pda-btn-green'});
							leftCol.btns.makeNode('next', 'button', {text:'Next Step', css:'pda-btn-blue'});
							
							leftCol.btns.edit.notify('click', {
								type:'workorders-run',
								data:{
									run:editWorkorder.bind({}, dom, obj, users, leftCol.btns.edit)
								}
							}, sb.moduleId);
										
							leftCol.btns.next.notify('click', {
								type:'workorders-run',
								data:{
									run:function(dom, obj, state, draw){
										
										dom.cols.left.details.content.btns.next.loading();
												
											var newObj = {
												id:obj.id,
												status:'Proposal',
												childObj:3
											};
											
											if(obj.hasOwnProperty('qualification_note')){
												
												newObj.qualification_note = dom.cols.right.seperate.form.process().fields.textbox.value;
												
											}
											
											sb.data.db.obj.update('work_orders', newObj, function(updated){
												
												var newNote = {},
													notifyEmails = [];
									
												newNote.notify = [];	
													
												_.each(taggedUsers, function(u){
														
													if(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].id != +sb.data.cookie.userId){
														
														newNote.notify.push(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].id);
													  
														notifyEmails.push(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].email);	
														
													}
																	
												});	
												
												if(newNote.notify.length > 0){
													
													sb.comm.sendEmail({
														to: notifyEmails,
														from: appConfig.emailFrom,
														subject: 'New Opportunity',
														mergevars: {
															TITLE: obj.name,
															BODY: 'Posted by '+ _.where(staff, {id: +sb.data.cookie.userId})[0].fname +' '+ _.where(staff, {id: +sb.data.cookie.userId})[0].lname +' at '+ moment().format('M/DD/YYYY h:mm a') +'.',
															BUTTON: 'View Online',
															BUTTON_LINK: window.location.href
														}, emailtags: [
															'New Opportunity'
														],
														type: 'notes',
														typeId: updated.id,
													}, function(sent){
														
														sb.dom.alerts.alert('Emails Sent!', 'All tagged users have been notified', 'success');
																		
													});
													
												}
												
												swal.close();
												
												transferToWorkFlows(dom, updated);
												
												components.workflows.notify({
												    type:'update-workflow-view',
												    data:{
												        object:updated
												    }
												});
												
											}, 3);
										
									}.bind({}, dom, obj, state, draw)
								}
							}, sb.moduleId);
							
							leftCol.btns.save.notify('click', {
								type:'workorders-run',
								data:{
									run:function(dom, obj){
										
										dom.cols.left.details.content.btns.save.loading();
		
										var qualNote = dom.cols.right.seperate.form.process().fields.textbox.value;
										
										sb.data.db.obj.update('work_orders', {id:obj.id, qualification_note:qualNote}, function(updated){
																
											var newNote = {},
												notifyEmails = [];
								
											newNote.notify = [];	
												
											_.each(taggedUsers, function(u){
													
												if(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].id != +sb.data.cookie.userId){
													
													newNote.notify.push(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].id);
												  
													notifyEmails.push(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].email);	
													
												}
																
											});	
											
											if(newNote.notify.length > 0){
												
												sb.comm.sendEmail({
													to: notifyEmails,
													from: appConfig.emailFrom,
													subject: 'New Opportunity',
													mergevars: {
														TITLE: obj.name,
														BODY: 'Posted by '+ _.where(staff, {id: +sb.data.cookie.userId})[0].fname +' '+ _.where(staff, {id: +sb.data.cookie.userId})[0].lname +' at '+ moment().format('M/DD/YYYY h:mm a') +'.',
														BUTTON: 'View Online',
														BUTTON_LINK: window.location.href
													}, emailtags: [
														'New Opportunity'
													],
													type: 'notes',
													typeId: updated.id,
												}, function(sent){
													
													sb.dom.alerts.alert('Emails Sent!', 'All tagged users have been notified', 'success');
																	
												});
												
											}
																			
											dom.cols.left.details.content.btns.save.loading(false);
											
											/*
		setTimeout(function(){
											
												opportunityView(dom, updated);	
												
											}, 1000);
		*/
											
										}, 3);
										
									}.bind({}, dom, obj)
								}
							}, sb.moduleId);
		
							leftCol.makeNode('break', 'div', {text:'<br />'});
							
							leftCol.makeNode('specs', 'div', {css:'ui items'});
							
							leftCol.specs.makeNode('contact', 'div', {css:'item'})
								.makeNode('content', 'div', {css:'content'});				
							leftCol.specs.contact.content.makeNode('header', 'div', {css:'description', text:'Main Contact'});
							leftCol.specs.contact.content.makeNode('meta', 'div', {css:'header', text:obj.main_contact.fname +' '+ obj.main_contact.lname +' <i class="fa fa-external-link"></i>'});
		
							leftCol.specs.makeNode('manager', 'div', {css:'item'})
								.makeNode('content', 'div', {css:'content'});
							leftCol.specs.manager.content.makeNode('header', 'div', {css:'description', text:'Manager'});
							leftCol.specs.manager.content.makeNode('meta', 'div', {css:'header', text:obj.manager.fname +' '+ obj.manager.lname});
							
							leftCol.specs.makeNode('type', 'div', {css:'item'})
								.makeNode('content', 'div', {css:'content'});
							leftCol.specs.type.content.makeNode('header', 'div', {css:'description', text:'Type'});
							leftCol.specs.type.content.makeNode('meta', 'div', {css:'header', text:obj.type.name});

							leftCol.specs.makeNode('guestCount', 'div', {css:'item'})
								.makeNode('content', 'div', {css:'content'});
							leftCol.specs.guestCount.content.makeNode('header', 'div', {css:'description', text:'Guest Count'});
							leftCol.specs.guestCount.content.makeNode('meta', 'div', {css:'header', text:obj.guest_count});
		
							leftCol.specs.makeNode('start', 'div', {css:'item'})
								.makeNode('content', 'div', {css:'content'});
							leftCol.specs.start.content.makeNode('header', 'div', {css:'description', text:'Event Date'});
							leftCol.specs.start.content.makeNode('meta', 'div', {css:'header', text:moment(obj.start_date).format('M/D/YYYY h:mm a') +' - '+ moment(obj.end_date).format('M/D/YYYY h:mm a')});
		
							leftCol.specs.makeNode('followup', 'div', {css:'item'})
								.makeNode('content', 'div', {css:'content'});
							leftCol.specs.followup.content.makeNode('header', 'div', {css:'description', text:'Follow Up Date'});
							leftCol.specs.followup.content.makeNode('meta', 'div', {css:'header', text:moment(obj.follow_up_date).format('M/D/YYYY h:mm a')});
		
							leftCol.specs.makeNode('closing', 'div', {css:'item'})
								.makeNode('content', 'div', {css:'content'});
							leftCol.specs.closing.content.makeNode('header', 'div', {css:'description', text:'Potential Closing Date'});
							leftCol.specs.closing.content.makeNode('meta', 'div', {css:'header', text:moment(obj.potential_closing_date).format('M/D/YYYY h:mm a')});
		
							leftCol.specs.makeNode('value', 'div', {css:'item'})
								.makeNode('content', 'div', {css:'content'});
							leftCol.specs.value.content.makeNode('header', 'div', {css:'description', text:'Estimated Value'});
							leftCol.specs.value.content.makeNode('meta', 'div', {css:'header', text:'$' + (obj.potential_value/100).formatMoney(2)});
							
							leftCol.specs.contact
								.notify('click', {
									type:'workorders-run',
									data:{
										run:function(dom, obj){
											
											sb.notify({
												type:'app-navigate-to',
												data:{
													itemId:'contacts',
													viewId:{
														id:'single-'+obj.main_contact.id,
														type:'table-single-item',
														title:obj.main_contact.fname +' '+ obj.main_contact.lname,
														icon:'<i class="fa fa-user"></i>',
														setup:{
															objectType:'contacts'
														},
														rowObj:obj.main_contact,
														removable:true,
														parent:'table',
														redraw:true
													}
												}
											});
											
										}.bind({}, dom, obj)
									}
								}, sb.moduleId);
												
							draw({
								dom:dom,
								after:function(){
									
									sb.notify({
										type: 'show-note-list-box',
										data: {
											domObj:dom.notesTasks.notes,
											objectIds:[obj.id].concat( _.chain(obj.history).pluck('contract').map(function(obj){ return +obj; }, []).value() ),
											objectId:obj.id
										}
									});	
												
									sb.notify({
										type: 'show-task-list',
										data: {
											domObj: dom.notesTasks.tasks,
											objectIds: [obj.id],
											objectId: obj.id,
											compact: true,
											collapse: true,
											draw: function(done){
												
												done.patch();
												
											}
										}
									});
									
								}
							});
							
						});
											
					});
						
				});
				
			});

		}

		function saveNewWorkorder(objectId){
			
			var form1 = this.panel.col1.cont.form.process(),
				form2 = this.panel.col1.cont.form2.process();

			if(form1.completed == false || form2.completed == false){
				sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
				return;
			}

			this.panel.btns.save.loading();
			
			var newEvent = {};
			
			_.each(form1.fields, function(field, fieldName){
				
				newEvent[fieldName] = field.value;
				
			});
			
			_.each(form2.fields, function(field, fieldName){
				
				newEvent[fieldName] = field.value;
				
			});

			sb.data.db.obj.create('work_orders', newEvent, function(event){
				
				sb.notify({
					type:'app-remove-main-navigation-item',
					data:{
						itemId:'contacts',
						viewId:'addWorkorder'
					}
				});
				
				sb.notify({
					type:'app-navigate-to',
					data:{
						itemId:'workorders',
						viewId:{
							id:'single-'+event.id,
							type:'table-single-item',
							title:event.name,
							icon:'<i class="fa fa-wrench"></i>',
							setup:{
								objectType:'work_orders'
							},
							rowObj:event,
							removable:true,
							parent:'table'
						}
					}
				});			
				
			}, 2);
			
		}
		
		function transferToWorkFlows(dom, obj){

			function setupProposal(obj, callback){
				
				if(obj.proposal){
					callback(obj);
				}else{
					obj.main_object = obj.id;
					obj.status = 'Editing';
					sb.data.db.obj.create('proposals', obj, function(newProp){
						sb.data.db.obj.update('work_orders', {id:obj.id, proposal:newProp.id}, function(workOrder){
							callback(workOrder);
						}, 3);
					});
				}
				
			}

			sb.data.db.obj.getAll('event_type_options', function(event_type_options){
				
				sb.data.db.obj.getAll('users', function(users){
					
					setupProposal(obj, function(obj){
						
						var toggleButton = 'times',
							toggleCSS = 'orange',
							templateText = 'Save as template?';
							
						if(obj.hasOwnProperty('is_template')){
							
							if(obj.is_template == 1){
								
								toggleButton = 'check',
								toggleCSS = 'green';
								templateText = '<i class="fa fa-check" aria-hidden="true"></i> Is a template';
								
							}
							
						}	
						
						dom.empty();
						
	
						dom.makeNode('cols', 'div', {css:'ui stackable grid'});
						dom.makeNode('notesTasks', 'div', {css:'ui stackable grid'});
						
						dom.cols.makeNode('left', 'div', {css:'sixteen wide column'})
							.makeNode('details', 'div', {css:'ui fluid'})
								.makeNode('content', 'div', {css:'content'});
						
						dom.cols.makeNode('right', 'div', {css:'sixteen wide column'});
						
						var leftCol = dom.cols.left.details.content;
						var rightCol = dom.cols.right;
	
						leftCol.makeNode('title', 'div', {css:'ui medium header', text:obj.name});
						var tagsContainer = leftCol.makeNode('tags', 'div', {});
	
						leftCol.makeNode('btns', 'div', {css:'ui buttons'});
						
						if(obj.status == 'Proposal'){
							
							leftCol.btns.makeNode('edit', 'button', {text:'Edit', css:'pda-btn-orange'});
							
						}
											
						//leftCol.btns.makeNode('save', 'button', {text:templateText, css:'pda-btn-'+ toggleCSS});
						
						if(obj.status == 'Proposal'){
							
							leftCol.btns.makeNode('back', 'button', {text:'Back', css:'pda-btn-blue'});
							
						}
											
						leftCol.makeNode('break', 'div', {text:'<br />'});
						
						leftCol.makeNode('cols', 'div', {css:'ui grid'});
						
						leftCol.cols.makeNode('price', 'div', {css:'ui two wide column'});
						leftCol.cols.makeNode('contact', 'div', {css:'ui two wide column'});
						leftCol.cols.makeNode('manager', 'div', {css:'ui two wide column'});
						leftCol.cols.makeNode('type', 'div', {css:'ui two wide column'});
						leftCol.cols.makeNode('start', 'div', {css:'ui three wide column'});
						leftCol.cols.makeNode('followup', 'div', {css:'ui two wide column'});
						leftCol.cols.makeNode('closing', 'div', {css:'ui two wide column'});
							
						leftCol.cols.price.makeNode('content', 'div', {css:'content'})
							.makeNode('desc', 'div', {css:'description'})
								.makeNode('loader', 'loader', {});
						
						leftCol.cols.contact.makeNode('header', 'div', {css:'ui small header', text:'Main Contact'});
						leftCol.cols.contact.makeNode('meta', 'div', {css:'', text:obj.main_contact.fname +' '+ obj.main_contact.lname +' <i class="fa fa-external-link"></i>'});
	
						leftCol.cols.manager.makeNode('header', 'div', {css:'ui small header', text:'Manager'});
						leftCol.cols.manager.makeNode('meta', 'div', {css:'', text:obj.manager.fname +' '+ obj.manager.lname});
						
						leftCol.cols.type.makeNode('header', 'div', {css:'ui small header', text:'Type'});
						leftCol.cols.type.makeNode('meta', 'div', {css:'', text:obj.type.name});

						leftCol.cols.type.makeNode('gcheader', 'div', {css:'', text:'Guest Count: '+ obj.guest_count});
						
						var endDateDisplay = '<i class ="text-muted">Not set</i>';
						if (obj.end_date) {
							endDateDisplay = moment(obj.end_date).format('M/D/YYYY h:mm a');
						}
						leftCol.cols.start.makeNode('header', 'div', {css:'ui small header', text:'Work Order Date'});
						leftCol.cols.start.makeNode('meta', 'div', {css:'', text:moment(obj.start_date).format('M/D/YYYY h:mm a') +'<br /><small>thru</small><br />'+ endDateDisplay});
						
						var followUpStartDateDisplay = '<i class ="text-muted">Not set</i>';
						if (obj.follow_up_date) {
							followUpStartDateDisplay = moment(obj.follow_up_date).format('M/D/YYYY h:mm a');
						}
						var followUpEndDateDisplay = '<i class ="text-muted">Not set</i>';
						if (obj.potential_closing_date) {
							followUpEndDateDisplay = moment(obj.potential_closing_date).format('M/D/YYYY h:mm a');
						}
						leftCol.cols.followup.makeNode('header', 'div', {css:'ui small header', text:'Follow Up Date'});
						leftCol.cols.followup.makeNode('meta', 'div', {css:'', text:followUpStartDateDisplay});
	
						leftCol.cols.closing.makeNode('header', 'div', {css:'ui small header', text:'Potential Closing Date'});
						leftCol.cols.closing.makeNode('meta', 'div', {css:'', text:followUpEndDateDisplay});
						
						//leftCol.makeNode('break', 'div', {text:'<br />'});
						
						leftCol.makeNode('tasksCont', 'div', {css:'ui grid'});
						leftCol.tasksCont.makeNode('tasks', 'div', {css:'ui five wide column'});
						leftCol.tasksCont.makeNode('note', 'div', {css:'ui eleven wide column'});
						
						leftCol.makeNode('tasks', 'div', {});
						
						leftCol.cols.contact
							.notify('click', {
								type:'workorders-run',
								data:{
									run:function(dom, obj){
										
										sb.notify({
											type:'app-navigate-to',
											data:{
												itemId:'contacts',
												viewId:{
													id:'single-'+obj.main_contact.id,
													type:'table-single-item',
													title:obj.main_contact.fname +' '+ obj.main_contact.lname,
													icon:'<i class="fa fa-user"></i>',
													setup:{
														objectType:'contacts'
													},
													rowObj:obj.main_contact,
													removable:true,
													parent:'table',
													redraw:true
												}
											}
										});
										
									}.bind({}, dom, obj)
								}
							}, sb.moduleId);
						
						if(obj.proposal.status == 'Proposal'){
							
							leftCol.btns.edit
								.notify('click', {
									type:'workorders-run',
									data:{
										run:editWorkorder.bind({}, dom, obj, users, leftCol.btns.edit)
									}
							}, sb.moduleId);
							
							leftCol.btns.back
								.notify('click', {
									type:'workorders-run',
									data:{
										run:function(dom, obj, state, draw){
											
											dom.cols.left.details.content.btns.back.loading();
													
												sb.data.db.obj.update('work_orders', {id:obj.id, status:'Prospecting', childObjs:3}, function(updated){
														
													opportunityView(dom, updated);
													
													components.workflows.notify({
													    type:'update-workflow-view',
													    data:{
													        object:updated
													    }
													});
													
												}, 3);
											
										}.bind({}, dom, obj, state, draw)
									}
							}, sb.moduleId);
						
						}
						
	/*
						leftCol.btns.save
							.notify('click', {
								type:'workorders-run',
								data:{
									run:function(){
										
										dom.cols.left.details.content.btns.save.loading();
										
										if(obj.hasOwnProperty('is_template')){
											
											switch(obj.is_template){
												
												case 1:
												
													obj.is_template = 0;
													break;
													
												case 0:
												
													obj.is_template = 1;
													break;
												
											}
											
										}else{
											
											obj.is_template = 1;
											
										}
										
										sb.data.db.obj.update('work_orders', obj, function(res){
											
											if(res){
												
												transferToWorkFlows(dom, obj);
												
											}else{
												
												sb.dom.alerts.alert('Oops, something went wrong.', 'Please refresh the page and try again.', 'error');
												
											}
											
										});
										
									}.bind(obj, dom, state, draw)
								}
							});
	*/
						rightCol.makeNode('estimates', 'div', {});
						rightCol.estimates.makeNode('cont', 'container', {title:'PROPOSALS', subTitle:'Bring your contract, pricing, schedules and invoices together.', collapse:true});
						rightCol.estimates.cont.makeNode('loading', 'container', {});
						rightCol.estimates.cont.loading.makeNode('text', 'text', {text:'Loading proposals...', css:'text-center'});
						//rightCol.estimates.cont.loading.makeNode('loader', 'loader', {});
						
						rightCol.makeNode('break5', 'lineBreak', {});

						leftCol.tasksCont.note.makeNode('qualificationDoc', 'div', {css:'clear'});
	
						//rightCol.makeNode('break0.5', 'lineBreak', {spaces:1});
						
						leftCol.tasksCont.note.qualificationDoc.makeNode('cont', 'container', {title:'Qualification Details', collapse:'closed'});
						
						if(obj.hasOwnProperty('qualification_note')){
								
							leftCol.tasksCont.note.qualificationDoc.cont.makeNode('col1', 'column', {width:9}).makeNode('cont', 'container', {css:'pda-container pda-align-right'});
							leftCol.tasksCont.note.qualificationDoc.cont.makeNode('col2', 'column', {width:3}).makeNode('cont', 'container', {css:'pda-container pda-align-left'});
							
							if(obj.qualification_note != '' && obj.qualification_note != undefined && obj.qualification_note != null){
								
								leftCol.tasksCont.note.qualificationDoc.cont.col1.cont.makeNode('qualificationDocument', 'text', {text:'<div class="ui fluid basic segment" style="max-width:70rem;">'+ obj.qualification_note +'</div>', css:''});
								
								leftCol.tasksCont.note.qualificationDoc.cont.col1.cont.makeNode('finalbreak', 'div', {text:'<br /><br />'});
								
/*
								rightCol.qualificationDoc.cont.col2.cont.makeNode('btnGroup', 'buttonGroup', {});
								
								rightCol.qualificationDoc.cont.col2.cont.btnGroup.makeNode('pdf', 'button', {text:'<i class="fa fa-file-pdf-o"></i> PDF', css:'pda-btn-blue'}).notify('click', {
									type: 'workorders-run',
									data: {
										run: function(){
											viewPDF(dom, obj)
										}.bind(dom, obj)
									}
								}, sb.moduleId);
								
								rightCol.qualificationDoc.cont.col2.cont.btnGroup.makeNode('editDoc', 'button', {text:'<i class="fa fa-pencil"></i> Edit', css:'pda-btn-orange'})
									.notify('click', {
										type:'workorders-run',
										data:{
											run:function(){
												
												var taggedUsers = [],
													staff = [];
												
												rightCol.qualificationDoc.empty();
												rightCol.qualificationDoc.makeNode('cont', 'container', {title:'Qualification Details', subTitle:'Review/Update the spec document that pertains to this opportunity.', collapse:'open'});
												
												rightCol.qualificationDoc.cont.makeNode('col1', 'column', {width:9}).makeNode('cont', 'container', {css:'pda-container pda-align-right', style:'max-width: 70rem !important;'});
												rightCol.qualificationDoc.cont.makeNode('col2', 'column', {width:3}).makeNode('cont', 'container', {css:'pda-container pda-align-left'});
												
												sb.data.db.obj.getWhere('users', {related_object:{type:'greater_than', value:0}}, function(users){
													
													sb.data.db.obj.getWhere('staff', {id:{type:'contains', values:_.pluck(users, 'related_object')}, enabled:1}, function(staffList){
																			
														staff = [];
														
														_.each(users, function(user){
															
															if(_.pluck(staffList, 'id').indexOf(user.related_object) > -1){
																
																staff.push(user);
																
															}
															
														});
														
														var opportunityDetails = {
															textbox:{
																name:'textbox',
																type:'textbox',
																label:'',
																value:obj.qualification_note,
																wysiwyg:{
																	minHeight: null,
																	maxHeight: 750,
																	dialogsInBody: true,																
																	toolbar:[
																		['style', ['style']],
																		['font', ['bold', 'italic', 'underline', 'clear']],
																		['fontsize', ['fontsize']],
																		['para', ['ul', 'ol', 'paragraph']],
																		['insert', ['picture']],
																		['misc', ['fullscreen', 'help']]
																	],
																	hint:[{
																		mentions: _.map(staff, function(s){ return s.fname +' '+ s.lname }),
																		match: /\B@(\w*)$/,
																		search: function (keyword, callback) {
																			
																			var ret = _.map(this.mentions, function(item){
												
																				if(item.search(keyword) > -1){
																					return item;
																				}
																				
																			});
																			
																			callback(_.compact(ret));
																		
																		},
																		content: function (item) {
																			taggedUsers.push(item);					
																			return '@' + item;
																		}
																	}]
																}
															}
														};
																
														rightCol.qualificationDoc.cont.col1.cont.makeNode('form', 'form', opportunityDetails);
														
														rightCol.qualificationDoc.cont.col2.cont.makeNode('btnGroup', 'buttonGroup', {});
														
														rightCol.qualificationDoc.cont.col2.cont.btnGroup.makeNode('cancel', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btnOutline-red'})
															.notify('click', {
																type:'workorders-run',
																data:{
																	run:function(){
																		transferToWorkFlows(dom, obj);
																	}.bind(dom, obj)
																}
															}, sb.moduleId);
															
														rightCol.qualificationDoc.cont.col2.cont.btnGroup.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'})
															.notify('click', {
																type:'workorders-run',
																data:{
																	run:function(dom, obj){
																		
																		dom.qualificationDoc.cont.col2.cont.btnGroup.save.loading();
																		
																		var qualNote = dom.qualificationDoc.cont.col1.cont.form.process().fields.textbox.value;
																		
																		sb.data.db.obj.update('work_orders', {id:obj.id, qualification_note:qualNote}, function(updated){
																								
																			var 	newNote = {},
																				notifyEmails = [];
																
																			newNote.notify = [];	
																				
																			_.each(taggedUsers, function(u){
																					
																				if(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].id != +sb.data.cookie.userId){
																					
																					newNote.notify.push(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].id);
																				  
																					notifyEmails.push(_.filter(staff, function(s){ return s.fname+' '+s.lname == u })[0].email);	
																					
																				}
																								
																			});	
																			
																			if(newNote.notify.length > 0){
																				
																				sb.comm.sendEmail({
																					to: notifyEmails,
																					from: appConfig.emailFrom,
																					subject: 'New Opportunity',
																					mergevars: {
																						TITLE: obj.name,
																						BODY: 'Posted by '+ _.where(staff, {id: +sb.data.cookie.userId})[0].fname +' '+ _.where(staff, {id: +sb.data.cookie.userId})[0].lname +' at '+ moment().format('M/DD/YYYY h:mm a') +'.',
																						BUTTON: 'View Online',
																						BUTTON_LINK: window.location.href
																					}, emailtags: [
																						'New Opportunity'
																					],
																					type: 'notes',
																					typeId: updated.id,
																				}, function(sent){
																					
																					sb.dom.alerts.alert('Emails Sent!', 'All tagged users have been notified', 'success');
																									
																				});
																				
																			}
																											
																			dom.qualificationDoc.cont.col2.cont.btnGroup.save.text('<i class="fa fa-check"></i> Saved!');
																			dom.qualificationDoc.cont.col2.cont.btnGroup.save.css('pda-btn-green');
																			
																			setTimeout(function(){
																			
																				transferToWorkFlows(dom, updated);	
																				
																			}, 1000);
																			
																		}, 3);
																		
																	}.bind({}, dom, obj)
																}
															}, sb.moduleId);
														
													});
													
												});
												
											}.bind(dom, obj)
										}
									});
*/
								
							}else{
								
								leftCol.tasksCont.note.qualificationDoc.cont.makeNode('qualificationDocument', 'text', {text:'<div style="max-width:65vw;" class="pda-Panel pda-panel-gray pda-background-white"> <large><b>No Qualification Note Found</b></large> <br /> Please make sure to save any changes made to the qualification document in the previous section.</div>', css:''});

								leftCol.tasksCont.note.qualificationDoc.cont.makeNode('finalbreak', 'div', {text:'<br /><br />'});
								
							}
							
						}

						if(obj.proposal.status != 'Editing' && obj.proposal.status != 'Proposal'){
							
							rightCol.makeNode('warning', 'div', {css:'ui large teal message', text:'This proposal has been approved by the admins. You can copy this proposal or create a new one.'});
							
						}else{
							
							rightCol.makeNode('contracts', 'div', {css:'clear'});
							rightCol.contracts.makeNode('cont', 'container', {title:'2. Contract', subTitle:'Edit the contract.', collapse:'closed'});
							rightCol.contracts.cont.makeNode('cont', 'div', {});
							rightCol.contracts.cont.cont.makeNode('text', 'text', {text:'Loading contract...', css:'text-center'});
										
							rightCol.makeNode('break1', 'lineBreak', {});
				
							rightCol.makeNode('items', 'div', {});
							rightCol.items.makeNode('cont', 'container', {title:'3. Billable Items', subTitle:'Add the things you want to charge for to the item list.', collapse:'closed'});
							rightCol.items.cont.makeNode('loading', 'container', {});
							rightCol.items.cont.loading.makeNode('text', 'text', {text:'Loading items...', css:'text-center'});
							rightCol.items.cont.loading.makeNode('loader', 'loader', {});
										
							rightCol.makeNode('break2', 'lineBreak', {});
				
							rightCol.makeNode('schedule', 'div', {});
							rightCol.schedule.makeNode('cont', 'container', {title:'4. Staff Schedule <small>(optional)</small>', subTitle:'Create and manage a schedule for your staff.', collapse:'closed'});
							rightCol.schedule.cont.makeNode('loading', 'container', {});
							rightCol.schedule.cont.loading.makeNode('text', 'text', {text:'Loading schedule...', css:'text-center'});
							rightCol.schedule.cont.loading.makeNode('loader', 'loader', {});	
		
							rightCol.makeNode('break3', 'lineBreak', {});
				
							rightCol.makeNode('invoices', 'div', {});
							rightCol.invoices.makeNode('cont', 'container', {title:'5. Invoices <small>(optional)</small>', subTitle:'Balance and manage the invoices.', collapse:'closed'});			
							rightCol.invoices.cont.makeNode('loading', 'div', {});
							rightCol.invoices.cont.loading.makeNode('text', 'text', {text:'Loading invoices...', css:'text-center'});
							rightCol.invoices.cont.loading.makeNode('loader', 'loader', {});
							
							rightCol.makeNode('break4', 'lineBreak', {});
							
						}
									
						rightCol.makeNode('bottomCont', 'column', {w:16})
							.makeNode('cont', 'container', {});
						
						rightCol.bottomCont.cont.makeNode('notesCol', 'column', {w:16}).makeNode('notes', 'container', {uiGrid:false});
						rightCol.bottomCont.cont.notesCol.notes.makeNode('cont', 'container', {uiGrid:false});
								
						var contractId;
						var menuId;
						var scheduleId;
						
						if(obj.proposal){
							
							if(obj.proposal.contract){
								contractId = obj.proposal.contract.id;
							}
							
							if(obj.proposal.menu){
								menuId = obj.proposal.menu.id;
							}
							
							if(obj.schedule){
								scheduleId = obj.schedule.id;
							}
							
						}
											
						dom.patch();
						
						draw({
							dom:dom,
							after:function(dom){
								
								var estimateView = {
										domObj:dom.cols.right.estimates.cont,
										object:obj,
										onUpdate:function(done){

											if(done === 'reload'){
												window.location.reload();
											}else{
												window.location.reload();
											}
			
										},
										doneCallback:function(doneType){
																						
											if(doneType !== true){
												
												window.location.reload();
											
											}
											
										}
									};
								
								function startEditSection(state){
									
									if(obj.proposal.status == 'Editing' || obj.proposal.status == 'Proposal'){
									
										sb.notify({
											type:'view-all-contracts',
											data:{
												objectId:obj.proposal.id,
												contactId:obj.main_contact.id,
												contractId:contractId,
												type:'work_orders',
												domObj:dom.cols.right.contracts.cont.cont,
												objectView:dom.cols.right.contracts.cont.cont,
												templateButton:obj.status,
												draw:function(done){
							
													done.dom.patch();
													
													done.after(done.dom);
							
												},
												onUpdate:function(contract){
																	
													obj.contract = contract;
													
													sb.data.db.obj.update('proposals', {id:obj.proposal.id, contract:contract.id}, function(updatedProp){
		
														sb.data.db.obj.getWhere(['inventory_billable_categories', 'inventory_billable_combination_categories', ''], {
															id:{
																type:'or',
																values:Object.keys(price)
															}
														}, function(categories){
																												
															sb.notify({
																type:'start-estimate-view',
																data:estimateView
															});
															
														});
														
													});
																	
												},
												afterCreate:function(newContract, callback){
																					
													obj.contract = newContract;
													
													sb.data.db.obj.update('proposals', {id:obj.proposal.id, contract:newContract.id}, function(done){
														
														sb.data.db.obj.getWhere(['inventory_billable_categories', 'inventory_billable_combination_categories', ''], {
															id:{
																type:'or',
																values:Object.keys(price)
															}
														}, function(categories){
															
															sb.data.db.obj.getWhere('groups', {group_type: 'Schedule', parent: obj.proposal.id, is_active: 'Yes'}, function(schedules){
																														
																sb.notify({
																	type:'start-estimate-view',
																	data:estimateView
																});
																
																callback(newContract);
																
															});
													
														});
														
													}, 2);
													
												}
											}
										});
			
										sb.notify({
											type: 'show-menu-builder',
											data: {
												domObj: dom.cols.right.items.cont,
												object: obj.proposal,
												menuId: menuId,
												invoicesDom: dom.cols.right.invoices.cont,
												pricing:function(updatedPrice){
							
													workorderPrice = updatedPrice;
													
													if(_.isEmpty(staffingPrice)){
														price = Object.assign(workorderPrice);
													}else{
														price = Object.assign(staffingPrice, workorderPrice);
													}
		
													processUpdatedPricing(dom.cols.right.invoices.cont, obj, price, dom.menu, dom.cols.left.details.content.cols.price.content);
													
													sb.data.db.obj.getWhere(['inventory_billable_categories', 'inventory_billable_combination_categories', ''], {
														id:{
															type:'or',
															values:Object.keys(price)
														}
													}, function(categories){
														
														sb.data.db.obj.getWhere('groups', {group_type: 'Schedule', parent: obj.id, is_active:'Yes'}, function(schedules){
															
															sb.notify({
																type:'start-estimate-view',
																data:estimateView
															});
																																										
															sb.notify({
																type: 'start-single-schedule',
																data: {
																	domObj: dom.cols.right.schedule.cont,
																	object: obj,
																	schedule: scheduleId,
																	pricing: function(updatedPrice) {
																																		
																		staffingPrice = updatedPrice;
																		
																		price = Object.assign(staffingPrice, workorderPrice);
																										
																		processUpdatedPricing(dom.cols.right.invoices.cont, obj, price, dom.menu, dom.cols.left.details.content.cols.price.content);
																		
																		sb.data.db.obj.getWhere(['inventory_billable_categories', 'inventory_billable_combination_categories', ''], {
																			id:{
																				type:'or',
																				values:Object.keys(price)
																			}
																		}, function(categories){
																			
																			sb.data.db.obj.getWhere('groups', {group_type: 'Schedule', parent: obj.id, is_active:'Yes'}, function(schedules){
																																		
																				sb.notify({
																					type:'start-estimate-view',
																					data:estimateView
																				});
																													
																			});
																			
																		});
																		
																	}
																}
															});
																								
														});
														
													});
																							
												},
												isInAccordion:true
											}
										});
										
										sb.data.db.obj.getWhere('proposals', {main_object:obj.id}, function(proposals){
											
											sb.notify({
												type: 'show-note-list-box',
												data: {
													domObj:dom.cols.right.bottomCont.cont.notesCol.notes.cont,
													objectIds:[obj.id].concat( _.pluck(proposals, 'id') ),
													objectId:obj.id
												}
											});							
											
										}, {
											id:true
										});
											
									}else{
										
										//processUpdatedPricing(false, obj, price, dom.menu, dom.cols.left.details.content.cols.price.content);
										
										sb.notify({
											type:'start-estimate-view',
											data:estimateView
										});
										
									}
									
									// tags comp
									if(sb.sys.state.components.tags){
										
										// remove old tags comp instance
										if(components.tags){
											components.tags.destroy();
											delete components.tags;
										}
										
										components.tags = sb.createComponent('tags');
										components.tags.notify({
											type: 'object-tag-view',
											data: {
												domObj: tagsContainer,
												objectType: 'work_orders',
												objectId: obj.id
											}
										});	
										
									}
														
									sb.notify({
										type: 'show-task-list',
										data: {
											domObj:dom.cols.left.details.content.tasksCont.tasks,
											objectIds:[obj.id],
											objectId:obj.id,
											compact:true,
											collapse:'collapse',
											boxColor: 'gray',
											draw:function(done){
												
												done.patch();
												
											}
										}
									});
									
								}
								
								startEditSection(state);
								
							}
						});
						
					});
					
				});
					
			});
			
		}
		
		function updateDate(obj, dom, state, draw, dateKey){
			
			var popDom = this,
				updateDateForm = {
					date:{
						name:'date',
						label:'Change Date:',
						type:'date',
						dateFormat:'M/D/YYYY h:mm a',
						value:moment(obj[dateKey]).format('M/D/YYYY h:mm a')
					}
				};

			popDom.empty();
			
			popDom.makeNode('col1', 'column', {width:12});
			popDom.makeNode('col2', 'column', {width:12});
			
			popDom.col1.makeNode('updateDateForm', 'form', updateDateForm);
			popDom.col1.makeNode('btnGroup', 'buttonGroup', {});
			
			popDom.col1.btnGroup.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pull-left pda-btn-x-small pda-btn-green'})
				.notify('click', {
					type:'workorders-run',
					data:{
						run:function(){
														
							var 	newObj = {},
								dateForm = popDom.col1.updateDateForm.process();
							
							popDom.col2.makeNode('loading', 'loader', {});
							
							popDom.patch();
							
							sb.data.db.obj.getById('work_orders', obj.id, function(res){
								
								newObj = _.clone(res);
									
								if(res){
									
									newObj[dateKey] = moment(dateForm.fields.date.value).format('YYYY-MM-DD HH:mm:ss');
																		
									sb.data.db.obj.getById('work_orders', obj.id, function(res){
										
										sb.data.db.obj.update('work_orders', newObj, function(res){
						
											if(res){
																																				
												WebuiPopovers.hideAll();
												
												opportunityView(dom, res);
												
											}else{
												
												sb.dom.alerts.alert('Oops, something went wrong.', 'Please refresh the page and try again.', 'error');
												
											}
											
										}, 3);
										
									});
									
								}else{
									
									sb.dom.alerts.alert('Error', 'Could not find existing workorder in database. Please try again.', 'error');
									
								}
								
							});
							
						}.bind(popDom, obj, dateKey)
					}
				}, sb.moduleId);
			
			popDom.patch();
			
		}
		
		function updateManager(obj, dom, state, draw, users, viewKey){
			
			var popDom = this,
				newManagerForm = {
					manager:{
						name:'manager',
						label:'Assign a new manager:',
						type:'select',
						change:function(form, selection){
														
							var newObj = _.clone(obj);
							
							popDom.col2.makeNode('loading', 'loader', {});
							
							popDom.patch();
							
							sb.data.db.obj.getById('users', +selection, function(res){
									
								if(res){
									
									newObj.manager = res;

									sb.data.db.obj.getById('work_orders', obj.id, function(res){
										
										sb.data.db.obj.update('work_orders', newObj, function(res){

											if(res){
																								
												WebuiPopovers.hideAll();
												
												if(viewKey == 'opportunity'){
													
													opportunityView(dom, res);
													
												}else{
												
													transferToWorkFlows(dom, res);
													
												}
												
											}else{
												
												sb.dom.alerts.alert('Oops, something went wrong.', 'Please refresh the page and try again.', 'error');
												
											}
											
										}, 3);
										
									});
									
								}else{
									
									sb.dom.alerts.alert('Error', 'Could not find user in database. Please try again.', 'error');
									
								}
							});
							
						},
						options:_.map(_.sortBy(users, 'fname'), function(user){var ret = {value:user.id, name:`${user.fname} ${user.lname}`}; if(obj.manager.id == user.id){ ret.selected = true; } return ret;})
					}
				};

			popDom.empty();
			
			popDom.makeNode('col1', 'column', {width:12});
			popDom.makeNode('col2', 'column', {width:12});
			
			popDom.col1.makeNode('typeForm', 'form', newManagerForm);
			
			popDom.patch();
			
		}
		
		function updatePotentialValue(obj, dom, state, draw){
			
			var popDom = this,
				updatePotentialValueForm = {
					usd:{
						name:'potential_value',
						label:'Update Estimated Value:',
						type:'usd',
						value:(obj.potential_value/100).formatMoney(2),
						change:function(form, selection){
														
							var 	newObj = {};
							
							popDom.col2.makeNode('loading', 'loader', {});
							
							popDom.patch();
							
							sb.data.db.obj.getById('work_orders', obj.id, function(res){
								
								newObj = _.clone(res);
									
								if(res){
									
									if(isNaN(selection)){
										
										var splitStrings = [];
										
										function splitString(stringToSplit, seperator){
											
											return stringToSplit.split(seperator);
											
										}
										
										splitStrings = splitString(selection, ' ');
										
										newObj.potential_value = ( +splitStrings[1] ) * 100;
										
									}else{
									
										newObj.potential_value = selection * 100;

									}
									
									newObj.status = 'Proposal';
																		
									sb.data.db.obj.getById('work_orders', obj.id, function(res){
										
										sb.data.db.obj.update('work_orders', newObj, function(res){
											
											if(res){
																																				
												WebuiPopovers.hideAll();
												
												opportunityView(dom, res);
												
											}else{
												
												sb.dom.alerts.alert('Oops, something went wrong.', 'Please refresh the page and try again.', 'error');
												
											}
											
										}, 3);
										
									});
									
								}else{
									
									sb.dom.alerts.alert('Error', 'Could not find existing workorder in database. Please try again.', 'error');
									
								}
							});
							
						}
					}
				};

			popDom.empty();
			
			popDom.makeNode('col1', 'column', {width:12});
			popDom.makeNode('col2', 'column', {width:12});
			
			popDom.col1.makeNode('updatePotentialValueForm', 'form', updatePotentialValueForm);
			
			popDom.patch();
			
		}
		
		function updateType(obj, dom, state, draw, event_type_options, viewKey){
			
			var popDom = this;
			
			var newTypeForm = {
					type:{
						name:'type',
						label:'Change Workorder Type:',
						type:'select',
						change:function(form, selection){
														
							var newObj = _.clone(obj);
							
							popDom.col2.makeNode('loading', 'loader', {});
							
							popDom.patch();
							
							sb.data.db.obj.getById('event_type_options', +selection, function(res){
									
								if(res){
									
									newObj.type = res;

									sb.data.db.obj.getById('work_orders', obj.id, function(res){
										
										sb.data.db.obj.update('work_orders', newObj, function(res){

											if(res){
												
												WebuiPopovers.hideAll();
												
												if(viewKey == 'opportunity'){
													
													opportunityView(dom, res);
													
												}else{
												
													transferToWorkFlows(dom, res);
													
												}
												
											}else{
												
												sb.dom.alerts.alert('Oops, something went wrong.', 'Please refresh the page and try again.', 'error');
												
											}
											
										}, 3);
										
									});
									
								}else{
									
									sb.dom.alerts.alert('Error', 'Could not find user in database. Please try again.', 'error');
									
								}
							});
							
						},
						options:_.map(_.sortBy(event_type_options, 'name'), function(o){ var ret = {value:o.id, name:o.name}; if(obj.type.id == o.id){ ret.selected = true; } return ret; })
					}
				};
			
			popDom.empty();
			
			popDom.makeNode('col1', 'column', {width:12});
			popDom.makeNode('col2', 'column', {width:12});
			
			popDom.col1.makeNode('typeForm', 'form', newTypeForm);
						
		}
		
		function viewPDF(dom, obj){

			dom.qualificationDoc.cont.col2.cont.btnGroup.pdf.loading();
			
			sb.data.makePDF(obj.qualification_note, 'D');
			
			dom.qualificationDoc.cont.col2.cont.btnGroup.pdf.loading(false);
					
		}
		
		function start(obj, dom, state, draw){

			var stages = [
					{
					    name:'Prospecting',
					    title:'Qualification',
					    subTitle:'Needs and data collection',
// 					    icon:'file alternate outline',
					    ui:opportunityView
					},{	
					    name:'Proposal',
					    title:'Proposal',
					    subTitle:'Send to client',
// 					    icon:'envelope',
					    ui:transferToWorkFlows
					}, {
					    name:'Sent',
					    title:'Client Review',
					    subTitle:'Proposals sent to client',
// 					    icon:'clipboard check',
					    ui:transferToWorkFlows
					}, {
					    name:'Accepted',
					    title:'Accepted',
					    subTitle:'Client has accepted a proposal',
// 					    icon:'thumbs up outline',
					    ui:transferToWorkFlows
					}, {
					    name:'Complete',
					    title:'Complete',
					    subTitle:'Invoices paid and event complete',
// 					    icon:'check circle',
					    ui:transferToWorkFlows
					}
				];
				
			if(obj.status == 'Approval'){
				stages[1].name = 'Approval';
				stages[1].title = 'Approval';
				stages[1].subTitle = 'Waiting for admin approval';
			}
			
			if(obj.status == 'Approved'){
				stages[1].name = 'Approved';
				stages[1].title = 'Approved';
				stages[1].subTitle = 'Ready to send';
			}
			
			dom.empty();
						
			dom.makeNode('mainCont', 'div', {css:'ui one column'});
			
			dom.patch();
			
			components.workflows.notify({
				type:'show-workflow-view',
				data:{
					ui:dom.mainCont,
					layout_ui:function(ui, obj, stageView){
			                      			                             	                     	                     
	                     ui.makeNode('cont', 'container', {})
	                     
	                     ui.cont.makeNode('r', 'column', {w:16});
	 
	                     ui.cont.makeNode('l', 'column', {w:16});

	                    // to show up when object is in that stage only
	                     stageView(ui.cont.r, obj);
	                             
	                },
	                objectType:'workorders',
	                object:obj,
	                stageProperty:'status',
	                stages:stages,
	                ordered:true
				}
			});

			draw(false);    
									
		}
		
		dom.empty();
		
		start(obj, dom, state, draw);
		
		return {
			refreshHeader:function(dom, status){
				
				buildHeaderNav(dom, status);
				
				dom.patch();
				
			}
		};
		
	}
		
	function startTableUI(){
	
		function buildTable(dom){

			function back(obj){

				components.table.notify({
					type:'crud-table-row-link-clicked',
					data:{
						dataId:obj.id,
						type:'tab',
						header: obj.event_name,
						action: singleEventView,
						object: obj
					}
				});
				
			}
			
			var crudSetup = {							
					domObj:dom,
					tableTitle:'<i class="fa fa-snowflake-o"></i> Events',
					objectType:'events',
					searchObjects:[
						{
							name:'Event Name',
							value:'event_name'
						},
						{
							name:'Event ID',
							value:'object_uid'
						}						
					],					
					filters:function(callback){
						
						sb.data.db.obj.getAll('users', function(users){
						
							var filters = {
									sales_specialist:{
										name:'Sales Specialist',
										type:'checkbox',
										all:true,
										field:'sales_specialist',
										options:[]
									},
									event_specialist:{
										name:'Event Specialist',
										type:'checkbox',
										all:true,
										field:'event_specialist',
										options:[]
									},
									status:{
										name:'Event Status',
										type:'checkbox',
										all:true,
										field:'status',
										options:[
											{
												name:'status',
												label:'Proposal',
												value:'proposal',
												checked:true
											},
											{
												name:'status',
												label:'Booked',
												value:'booked',
												checked:true
											},
											{
												name:'status',
												label:'On Hold',
												value:'On Hold',
												checked:true
											},
											{
												name:'status',
												label:'Cancelled',
												value:'Cancelled',
												checked:true
											}
										]
									}
								};
								
							_.each(users, function(u){
							
								filters.sales_specialist.options.push({
									name:'sales_specialist',
									label:u.fname +' '+ u.lname,
									value:u.id,
									checked:true
								});
								
								filters.event_specialist.options.push({
									name:'event_specialist',
									label:u.fname +' '+ u.lname,
									value:u.id,
									checked:true
								});
								
							});	
							
							callback(filters);
						
						});
						
					},
					download:true,
					headerButtons:{
						create: {
							name: '<i class="fa fa-plus"></i> Create Event',
							css: 'pda-btn-green',
							domType: 'full',
							action: createEvent
						},
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							action:function(){}
						}
					},
					calendar:{
						where:{},
						dateField:'start_date',
						prepare:function(obj){
							
							var  mainContact = obj.main_contact || 'No Main Contact Assigned',
								salesSpec = obj.sales_specialist || 'No Salses Specialist Selected',
								eventSpec = obj.event_specialist || 'No Event Specialist Selected';
													
							return {
								id: obj.id,
								name: obj.event_name,
								startTime: moment(obj.start_date),
								endTime: moment(obj.end_date),
								description:
									'<br /><br /><small>Venue:</small> '+ obj.venue.name
									+'<br /><small>Guest Count:</small> '+ obj.guest_count
									+'<br /><small>Main Contact:</small> '+ mainContact
									+'<br /><small>Sales Specialist:</small> '+ salesSpec
									+'<br /><small>Event Specialist:</small> '+ eventSpec,
								type: obj.event_type.name,
								color: 'pda-background-blue',
								buttons: {
									view: {
										text: 'View Event <i class="fa fa-external-link"></i>',
										css: 'pda-btn-blue pda-btn-x-small',
										type: 'eventComponent-run',
										data: {run:back.bind(this, obj)}
									}
								},
								group: 'events'
							};
	
						}
					},
					rowSelection:false,
					multiSelectButtons:{
						erase:{
							name:'<i class="fa fa-trash-o"></i> Delete',
							css:'pda-btn-red',
							domType:'modal',
							action:function(selectedObjs, dom){

								dom.body.makeNode('text', 'headerText', {css:'text-center', size:'small', text:'<i class="fa fa-circle-o-notch fa-spin"></i>'});
								
								dom.body.patch();
								
								setTimeout(function(){
																		
									dom.body.makeNode('text', 'headerText', {css:'text-center', size:'', text:'Delete '+ selectedObjs.length + ' events(s)?'});
																				
									dom.footer.makeNode('btns', 'buttonGroup', {}).makeNode('yes', 'button', {css:'pda-btn-large pda-btn-green', text:'<i class="fa fa-check"></i> Yes, delete them'}).notify('click', {
										type:'change-from-table',
										data:{
											type:'approve',
											modal:dom,
											objects:selectedObjs
										}
									}, sb.moduleId);
									
									dom.body.patch();
									dom.footer.patch();
									
									crudModal = dom;
									
								}, 1000);
								
							}
						}
					},
					visibleCols:{
						start_date:'Event Date',
						object_uid:'ID',
						event_name: 'Event Name',
						event_type:'Type',
						status:'Status',
						main_contact:'Main Contact',
						venue:'Venue',
						guest_count:'Guest Count',
						staff:'Specialists'
					},
					home:{
						action:homeScreenView,
						default: homeScreenDefault
					},
					settings:{
						action:[
							{
								object_type:'event_status',
								name:'Event Statuses'
							},
							{
								object_type:'event_contract_status',
								name:'Contract Statuses'
							},
							{
								object_type:'event_type_options',
								name:'Event Types'
							},
/*
							{
								object_type:'condition_codes',
								name:'Condition Codes',
								formObjs:{
									name:{},
									description:{
										type:'textbox',
										wysiwyg:{
											height:'300px'
										}
									},
									object_bp_type:{immutable:true},
								}
							},
*/
							{
								object_type:'staff_base',
								name:'Venues'
							},
							{
								object_type:'event_service_style',
								name:'Service Styles'
							},
							{
								object_type:'contracts',
								name:'Contracts',
								action:function(dom){
									
									components.contracts.notify({
										type:'view-all-contracts',
										data:{
											domObj:dom,
											type:'events',
											homeScreen:'hide'
										}
									});
								
								}
							},
							{
								object_type:'menu_budget_types',
								name:'Budget Types'
							}, 
							{
								object_type:'packlist_checkpoints',
								name:'Packlist Checkpoints',
								action:packlistCheckpointsForm
							}
						]
					},
					cells: {
						start_date:function(obj){							
							return moment(obj.start_date).format('M/D/YYYY h:mm a') +' - '+ moment(obj.end_date).format('h:mm a');
						},
						staff:function(obj){
							
							var eventSpecialist = 'Not selected',
								salesSpecialist = 'Not selected';
							
							if(obj.event_specialist){
								
								if(obj.event_specialist.fname){
									
									eventSpecialist = obj.event_specialist.fname +' '+ obj.event_specialist.lname;
									
								}
								
							}
							
							if(obj.sales_specialist){
								
								if(obj.sales_specialist.fname){
									
									salesSpecialist = obj.sales_specialist.fname +' '+ obj.sales_specialist.lname;
									
								}
								
							}
							
							return '<small>Sales Specialist:</small> '+ salesSpecialist +'<br /><small>Event Specialist:</small> '+ eventSpecialist;
							
						},
						event_type: function(obj){
							
							if(obj.event_type){
								return obj.event_type.name;
							}else{
								return 'Not selected';
							}
							
						}, 
						status: function(obj){

							if(obj.status){
								return obj.status;
							}else{
								return 'Not selected';
							}
							
						},
						main_contact: function(obj){
							
							var mainContact = 'No Contact on File';
							
							if(obj.main_contact != null){
								
								mainContact = obj.main_contact.name;
							}
							
							return mainContact;
						},
						venue: function(obj){
							
							if(obj.venue){
								return obj.venue.name;
							}else{
								return 'Not selected';
							}
					
						}
					},
					rowLink:{
						type:'tab',
						header:function(obj){
							return obj.event_name;
						},
						action:singleEventView
					},
					dateRange:'start_date',
					childObjs:2,
					data:function(paged, callback){
						
						sb.data.db.obj.getAll('events', function(ret){

							callback(ret);
							
						}, 2, paged);
						
					},
					rules: {
						triggers: [{
							name:'an event',
							object_type:'events',
							properties: [{
								label: 'guest count',
								propertyName:'guest_count'
							}]
						}],
						changeable:{
							status:{
								label:'event status'
							},
							venue:{
								label:'event venue'
							}
						}
					}
				}
				
			if(contact != null && contact.id > 0){
				
				crudSetup.objectId = objectId;
				
				crudSetup.home = false;
				crudSetup.homeScreenDefault = homeScreenDefault,
				crudSetup.settings = showSettings,
				crudSetup.calendar = showCalendar,
				crudSetup.navigation = showNavigation,
				crudSetup.searchObjects = false;
				
				crudSetup.headerButtons.create = {
					name:'<i class="fa fa-plus"></i> Create An Event',
					css:'pda-btn-green',
					domType:'full',
					action:createEvent
				};

				crudSetup.data = function(paged, callback){
					
					sb.data.db.obj.getWhere('events', {main_contact: contact.id, childObjs:2, paged:paged}, function(ret){
						
						callback(ret);
						
					});
					
				};

			}
			
			if(showSettings === false) {
				crudSetup.settings = false;
			}
			if(showCalendar === false) {
				crudSetup.calendar = false;
			}	
		
			if(components.table){

				components.table.notify({
					type: 'show-table',
					data: crudSetup
				});
				
			}

		}
				
		function update(objs, type){
			
			components.table.notify({
				type:'update-table',
				data:{
					type:type,
					objects:objs,
					redraw:true
				}
			});
			
		}
		
		this.state = {};
		this.state.update = update.bind(this);

		buildTable(this);
		
	}
	
	// Kanban View
	
	function build_singleWorkorder(dom, obj) {
				
		dom.makeNode('title', 'div', {
				text: 
					'<h4 class="ui header">'+
						obj.name +
						'<div class="sub header">'+ moment(obj.start_date).format('M/D/YYYY') +'</div>'+
					'</h4>',
			}).notify('click', {
			type: 'workorders-run',
			data: {
				run: function() {
					
					sb.notify({
						type: 'app-navigate-to',
						data: {
							itemId: 'workorders',
							viewId: {
								id: 'single-' + obj.id,
								rowObj: obj,
								type: 'table-single-item',
								dom: singleWorkorderViewNew,
								parent: 'table',
								removable: true,
								title: obj.name,
								icon: '<i class="fa fa-user"></i>',
								viewState: {}
							}
						}
					});
					
				}
			}
		}, sb.moduleId);
		
	}
	
	// Reports
	
	function chart_of_accounts_report_ui (ui, state, draw) {
		
		// data calls
		
		function get_setup_data(callback) {
			
			sb.data.db.obj.getAll('inventory_billable_categories', function(cats){
				
				Cache.categories = cats;
				
				sb.data.db.obj.getById('work_orders', state.id, function(workorder){
	
					Cache.workorder = workorder;
					
					sb.data.db.obj.getById('inventory_menu', workorder.proposal.menu, function(menu){
						
						Cache.menu = menu;
	// 					console.log('menu = ', menu);
						
						sb.notify({
							type:'get-menu-line-item-pricing',
							data:{
								menu:menu,
		// 						obj:obj,
								workorder:workorder,
								callback:function(pricedMenu){
									console.log('pricedMenu', pricedMenu);
								}
							}
						});
						
						callback();
						
					}, {
						sections:{
							items:2
						}
					});
					
				}, {
					name:true,
					proposal:{
						menu:true
					}
				});
				
			}/*
{
				name:true,
				
			}
*/);
			
		}
		
		function get_report_data() {
			
		}
		
		// utility funcs
		
		function getAmtTxt(obj){
			
			var style = 'ui small grey header';
			var amt = sum_account_amt(Cache.menu, obj);
			if(amt > 0){
				style = 'ui black header';
			}
			
			return '<h3 class="'+ style +'">$'+ (amt/100).toFixed(2) +'</h3>';
			
		}
		
		function getAcctIdTxt(obj){
			
			return '<div class="ui label">'+ obj.account_id +'</div>';
			
		}
		
		function getChartOfAcctNameTxt(obj){
			
			if(obj.chart_of_accounts_company != null){
				return '<strong>'+ obj.chart_of_accounts_company.name +' / '+ obj.name +'</strong>';
			}else{
				return '<strong>'+ obj.name +'</strong>';
			}
			
		}
		
		function get_report_html(menu) {
			
			var html = '<h1>'+ Cache.workorder.name +'</h1>'+
						'<h3>Chart of accounts report, '+ moment().format('l') +'</h3>'+
						'<table>'+
							'<thead>'+
								'<th>Account</th>'+
								'<th>Account ID</th>'+
								'<th>Amount</th>'
							'</thead>';
							
			_.each(Cache.chart_of_accounts.data, function(obj){
				
				html += '<tr>'+
							'<td>'+ getChartOfAcctNameTxt(obj) +'</td>'+
							'<td>'+ getAcctIdTxt(obj) +'</td>'+
							'<td>'+ getAmtTxt(obj) +'</td>'+
						'</tr>'
				
			});
			
			html += '</table>';
			
			return html;
			
		}
		
		function sum_account_amt(menu, account){
			
			return _.chain(menu.sections)
							.pluck('items')
							 .flatten()
							  .reduce(
									function(memo, item){
										
										var category = _.findWhere(Cache.categories, {id: parseInt(item.item.category)});

										if (
											(
												item.item 
												&& typeof item.item.chart_of_account === 'object'
												&& parseInt(item.item.chart_of_account.id) === account.id
											)
											|| (
												category
												&& category.chart_of_account === account.id
											)
										) {
											
											return memo + item.lineTotal;
											
										}
										
										return memo;
									}, 
									0
							  ).value();
			
		}
		
		// views
		
		function header_ui(ui) {
			
			ui.makeNode('pdfBtn', 'div', {
				tag:'button',
				css:'ui blue right floated button',
				text:'<i class="download icon"></i> Download'
			}).notify('click', {
				type:'workorders-run',
				data:{
					run:function(){
						sb.data.makePDF(get_report_html(Cache.menu), false);
					}
				}
			}, sb.moduleId);
			
			ui.makeNode(
				'title',
				'div',
				{
					css:'ui header',
					tag:'h1',
					text:Cache.workorder.name +'<div class="sub header">Chart of accounts report</div>'
				}
			);
			
			console.log('Cache = ', Cache);
			
		}
		
		function body_ui(ui) {
			
			if(components.coaReportTable){
				components.coaReportTable.destroy();
			}
			
			components.coaReportTable = sb.createComponent('crud-table');
			
			components.coaReportTable.notify({
				type:'show-table',
				data:{
					domObj:ui,
					objectType:'chart_of_accounts_companies',
					childObjs:1,
					searchObjects:false,
					filters:false,
					download:false,
					navigation:false,
					headerButtons:{
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							action:function(){}
						}
					},
					settings:false,
					rowSelection:false,
					rowLink:false,
					multiSelectButtons:false,
					visibleCols:{
						name:'Account',
						account_id:'Account ID',
						amount:'Amount'
					},
					cells:{
						name:function(obj){
							
							return getChartOfAcctNameTxt(obj);
							
						},
						account_id:function(obj){
							return getAcctIdTxt(obj);
						},
						amount:function(obj){
							
							return getAmtTxt(obj);
							
						}
					},
					data:function(page, callback){
						
						page.sortCol = 'chart_of_account_company',
						page.sortDir = 'asc';
						
						sb.data.db.obj.getAll('chart_of_accounts', function(chartOfAccounts){
							
							Cache.chart_of_accounts = chartOfAccounts;
							callback(chartOfAccounts);
							
						}, {
							account_id:true,
							name:true,
							chart_of_accounts_company:{
								name:true
							}
						}, page);
						
					}
				}
			});
			
		}
		
		// view state
		
		var ViewState = {};
		var Cache = {};
		
// 		console.log('chart_of_accounts_report_ui::', ui, state, draw);
		
		get_setup_data(function(){
			
			ui.empty();
			
			header_ui( ui.makeNode('header', 'div', {}) );
			body_ui( ui.makeNode('body', 'div', {}) );
			
			draw(ui);
			
		});
		
	}
				
	return {
		
		init: function(){
			
			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'workorders',
						title:'Workorders',
						icon:'<i class="fa fa-wrench"></i>',
						views:[
							{
								id:'table',
								display:false,
								type:'table',
								title:'All Workorders',
								icon:'<i class="fa fa-th-list"></i>',
								setup:{
									tableTitle:'<i class="fa fa-wrench"></i> All Workorders',
									objectType:'work_orders',
									searchObjects:[
										{
											name:'Name',
											value:'name'
										},
										{
											name:'ID',
											value:'object_uid'
										},
										{
											name:'Status',
											value:'status'
										}
									],
									filters:function(callback){
										
										callback({
											
											status:{
												
												name:'Workorder Status',
												type:'checkbox',
												field:'status',
												options:[
													
													{
														name:'status',
														label:'Proposal',
														value:'Proposal',
														checked:true
													},
													{
														name:'status',
														label:'Sent',
														value:'Sent',
														checked:true
													},
													{
														name:'status',
														label:'Accepted',
														value:'Accepted',
														checked:true
													},
													{
														name:'status',
														label:'Signed',
														value:'Signed',
														checked:true
													},
													{
														name:'status',
														label:'Paid',
														value:'Paid',
														checked:true
													},
													{
														name:'status',
														label:'Complete',
														value:'Complete',
														checked:true
													}
													
												]
												
											}
											
										});
										
									},
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										}
									},
									rowSelection:true,
									multiSelectButtons:{
										erase:{
											name:'<i class="fa fa-trash-o"></i> Delete',
											css:'pda-btn-red',
											domType:'modal',
											action:function(selectedObjs, dom){
												
												dom.body.makeNode('title', 'div', {text:'Delete '+ selectedObjs.length +' work orders?', css:'ui huge header'});
												
												dom.body.makeNode('btns', 'div', {css:'ui huge buttons'});
												
												dom.body.btns.makeNode('yes', 'div', {css:'ui green button', text:'Yes'})
													.notify('click', {
														type:'workorders-run',
														data:{
															run:function(){
																
																function deleteObjects(allObjects, callback, count){
					
																	if(!count){
																		count = 0;
																	}

																	sb.data.db.obj.erase(allObjects[count].objectType, allObjects[count].id, function(done){
																		
																		count++;
																		
																		if(count == allObjects.length){
																			
																			callback(count);
																			
																		}else{
																			
																			deleteObjects(allObjects, callback, count);
																			
																		}
																		
																	});
																	
																}
																
																function deleteWorkorders(stack, count, callback, objsToDelete){
																	
																	if(!objsToDelete){
																		objsToDelete = [];
																	}

																	if(stack[count]){
																		
																		var obj = stack[count];
																		
																		sb.data.db.obj.getWhere('invoices', {related_object:obj.id, active:'Yes'}, function(invoices){
																				
																			sb.data.db.obj.getWhere('inventory_menu', {related:obj.id, active:'Yes'}, function(menus){
																				
																				sb.data.db.obj.getWhere('contracts', {related_object:obj.id, active:'Yes'}, function(contracts){
																					
																					sb.data.db.obj.getWhere('proposals', {main_object:obj.id}, function(props){
																						
																						objsToDelete.push({
																							objectType:'work_orders',
																							id:obj.id
																						});
																						
																						_.each(props, function(prop){
																						
																							objsToDelete.push({
																								objectType:'proposals',
																								id:prop.id
																							});
																							
																						});
																						
																						_.each(invoices, function(inv){
																						
																							objsToDelete.push({
																								objectType:'invoices',
																								id:inv.id
																							});
																							
																						});
																						
																						_.each(menus, function(menu){
																							
																							objsToDelete.push({
																								objectType:'inventory_menu',
																								id:menu.id
																							});
																							
																						});
																						
																						_.each(contracts, function(contract){
																							
																							objsToDelete.push({
																								objectType:'contracts',
																								id:contract.id
																							});
																							
																						});
																						
																						count++;
																						
																						deleteWorkorders(stack, count, callback, objsToDelete);
																																												
																					});
																				
																				});
																				
																			});
																			
																		});	
																		
																	}else{
																		
																		callback(objsToDelete);
																		
																	}
																	
																}
																
																dom.body.btns.yes.loading();
																
																deleteWorkorders(selectedObjs, 0, function(objsToDelete){

																	deleteObjects(objsToDelete, function(done){
																		
																		dom.hide();
																		
																		sb.dom.alerts.alert('Done!', '', 'success');
																		
																	});
																	
																});
																
															}
														}
													}, sb.moduleId);
												dom.body.btns.makeNode('no', 'div', {css:'ui red button', text:'No'})
													.notify('click', {
														type:'workorders-run',
														data:{
															run:function(){
																
																dom.hide();
																
															}
														}
													}, sb.moduleId);
													
												dom.body.patch();
												
											}
										},
										edit:{
											name:'<i class="fa fa-plus"></i> Add Templates',
											css:'pda-btn-green',
											domType:'none',
											action:function(selectedObjs, dom){
												
												if(Array.isArray(selectedObjs) && selectedObjs.length > -1){
													
													_.each(selectedObjs, function(obj){
														
														if(obj.is_template != 1){
															
															obj.is_template = 1;
														
															sb.data.db.obj.update('work_orders', obj, function(response){
																
																if(!response){
																	
																	sb.dom.alerts.alert('Error', 'Oops. Something went wrong.', 'error');
																	
																}
																
															});
															
														}
														
													});
													
												}else{
													
													sb.dom.alerts.alert('Error', 'Oops. Something went wrong.', 'error');
													
												}
												
												sb.notify({
													type:'app-navigate-to',
													data:{
														itemId:'workorders',
														viewId:'templateTable',
														redraw:true
													}
												});
												
											}
										}
									},
									visibleCols:{
										start_date:'Date',
										object_uid:'ID',
										name: 'Name',
										type:'Type',
										status:'Status',
										main_contact:'Main Contact',
										manager:'Manager'
									},
									cells: {
										start_date:function(obj){
											return moment(obj.start_date).format('M/D/YYYY h:mm a');
										},
										manager:function(obj){
											
											var manager = 'Not selected';
																						
											if(obj.manager){
												
												if(obj.manager.fname){
													
													manager = obj.manager.fname +' '+ obj.manager.lname;
													
												}
												
											}
											
											return manager;
											
										},
										type: function(obj){
											
											if(obj.type){
												return obj.type.name;
											}else{
												return 'Not selected';
											}
											
										}, 
										status: function(obj){
											
											var css = 'label-warning';
											
											switch(obj.status){
												
												case 'Paid':
												
													css = 'label-success';
												
													break;
												
											}
											
											if(obj.status){
												return '<label class="label '+ css +'">'+ obj.status +'</label>';
											}else{
												return '<label class="label label-danger">Proposal</label>';
											}
											
										},
										main_contact: function(obj){
											
											var mainContact = 'No Contact on File';
											
											if(obj.main_contact != null){
												
												mainContact = obj.main_contact.fname +' '+ obj.main_contact.lname;
											}
											
											return mainContact;
										}
									},
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.name;
										},
										action:singleWorkorderViewNew
									},
									childObjs:3,
									data:function(paged, callback){
																				
										sb.data.db.obj.getAll('work_orders', function(ret){

											callback(ret);
											
										}, {
											date_created:true,
											start_date:true,
											object_uid:true,
											name:true,
											type:{
												name:true
											},
											status:{
												name:true
											},
											main_contact:{
												fname:true,
												lname:true
											},
											manager:{
												fname:true,
												lname:true
											},
											is_template:true
										}, paged);
										
									}
								}								
							},
							{
								id:'workordersnew',
								type:'custom',
								title:'Work Orders Table',
								icon:'<i class="fa fa-wrench"></i>',
								isCollection:true,
								default:true,
								dom:function(dom, state, draw){
									
									draw({
										dom:dom,
										after:function(dom){
											
											sb.notify({
												type:'show-collection',
												data:{
													actions:{
														view:true,
														fullscreen:{
															icon:'external square',
															color:'teal',
															title:'View',
															domType:'navigate',
															action:function(item, ui, onComplete){
																
																window.location.href = sb.data.url.createPageURL(
																	'object', 
																	{
																		type:'project', 
																		id:item.id,
																		name:item.name
																	}
																);
																
																															
															}
														}
													},
													domObj:dom,
													fields:{
														object_uid:{
															title:'ID'
														},
														name:{
															title:'Name'
														},
														venue:{
															title:'Venue',
															view:function(domObj, object){
																var textString = 'Not selected';
																if(object.venue){
																	var textString = object.venue.name;
																}
																domObj.makeNode('text', 'div', {text:textString});																
															}
														},
														guest_count:{
															title:'Guest Count'
														},
														type:{
															title:'Type',
															type:'type'
														},
														main_contact:{
															title:'Client'
														},
														status:{
															title:'Status',
															type:'status',
															view:function(domObj, object){
																
																var textString = 'Not selected';
																var textColor = '';
																switch(object.status){
																	
																	case 'Approved':
																		
																		textString = object.status;
																		textColor = 'basic green';
																		
																		break;
																		
																	case 'Proposal':
																		
																		textString = object.status;
																		textColor = 'basic orange';
																		
																		break;
																		
																	case 'Prospecting':
																		
																		textString = object.status;
																		textColor = 'basic yellow';
																		
																		break;
																		
																	case 'Sent':
																		
																		textString = object.status;
																		textColor = 'blue';
																		
																		break;
																	
																	case 'Paid':	
																	case 'Signed':
																		
																		textString = object.status;
																		textColor = 'green';
																		
																		break;				
																		
																	default:
																	
																		textString = object.status;
																	
																}
																
																domObj.makeNode('text', 'div', {text:'<div class="ui '+ textColor +' label">'+ textString +'</div>'});
																
															}
														},
														main_contact:{
															title:'Client',
															view:function(domObj, object){
																var textString = 'Not selected';
																if(object.main_contact){
																	var textString = object.main_contact.fname +' '+ object.main_contact.lname;
																}
																domObj.makeNode('text', 'div', {text:textString});																
															}
														},
														manager:{
															title:'Manager',
															view:function(domObj, object){
																var textString = 'Not selected';
																var textColor = 'basic yellow';
																if(object.manager){
																	textColor = 'basic blue';
																	textString = object.manager.fname +' '+ object.manager.lname;
																}
																domObj.makeNode('text', 'div', {text:'<div class="ui '+ textColor +' label">'+ textString +'</div>'});																
															}
														},
														start_date:{
															title:'Start Date',
															view:function(domObj, object){
																domObj.makeNode('text', 'div', {text:moment(object.start_date).format('M/D/YYYY h:mm a')});
															}
														},
														end_date:{
															title:'End Date',
															view:function(domObj, object){
																domObj.makeNode('text', 'div', {text:moment(object.end_date).format('M/D/YYYY h:mm a')});
															}
														},
														costPerGuest:{
															title:'Cost Per Guest',
															view:function(domObj, object){
																var textString = '$0';
																if(object.guest_count && object.price){
																	textString = '$'+ ((object.price / object.guest_count) / 100).formatMoney();
																}
																domObj.makeNode('text', 'div', {text:textString});
															}
														},
														price:{
															title:'Total Price',
															view:function(domObj, object){
																var textString = '$0';
																if(object.price){
																	textString = '$'+ ((object.price) / 100).formatMoney();
																}
																domObj.makeNode('text', 'div', {text:textString});
															}
														},
														paid:{
															title:'Total Paid',
															view:function(domObj, object){
																var textString = '$0';
																if(object.paid){
																	textString = '$'+ ((object.paid) / 100).formatMoney();
																}
																domObj.makeNode('text', 'div', {text:textString});
															}
														},
														balance:{
															title:'Balance',
															view:function(domObj, object){
																var textString = '$0';
																if(object.balance){
																	textString = '$'+ ((object.balance) / 100).formatMoney();
																}
																domObj.makeNode('text', 'div', {text:textString});
															}
														},
														last_updated:{
															title:'Last Updated',
															view:function(domObj, object){
																domObj.makeNode('text', 'div', {text:moment(object.last_updated).format('M/D/YYYY h:mm a')});
															}
														}
													},
													fullView:{
														type:'teamTool',
														id:'projectTool'
													},
													groupings:{
														type:'Type',
														manager:'Manager'
													},
													objectType:'work_orders',
													selectedView:'table',
													singleView:{
														view:function(ui, obj, draw){
															
															var state = {
																object:obj,
																edit:false,
																onSave:draw,
																onDelete:function(){

																	sb.notify({
																		type:'app-navigate-to',
																		data:{
																			type:'UP'
																		}
																	});
																	
																}
															};														
															
															ui.patch();
															
															singleWorkorderViewNew(obj, ui, state, draw);
															//singleState(obj, ui, state, draw);
														},
														select:3
													},
													state:state,
													where:{
														childObjs:1
														/*
childObjs:{
															date_created:true,
															start_date:true,
															end_date:true,
															object_uid:true,
															details:true,
															name:true,
															type:{
																name:true
															},
															status:{
																name:true
															},
															main_contact:{
																fname:true,
																lname:true
															},
															manager:{
																fname:true,
																lname:true
															},
															is_template:true
														}
*/
													}
												}
											});
											
											
										}
									});
																		
								}
							},
							{
								id:'templateTable',
								default:false,
								type:'table',
								title:'Templates',
								icon:'<i class="fa fa-paste"></i>',
								setup:{
									tableTitle:'Template Workorders',
									objectType:'proposals',
									searchObjects:[
										{
											name:'Name',
											value:'name'
										},
										{
											name:'ID',
											value:'object_uid'
										}						
									],					
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										}
									},
									rowSelection:true,
									multiSelectButtons:{
										edit:{
											name:'<i class="fa fa-trash-o"></i> Remove Templates',
											css:'pda-btn-red',
											domType:'none',
											action:function(selectedObjs, dom){
												
												if(Array.isArray(selectedObjs) && selectedObjs.length > -1){
													
													_.each(selectedObjs, function(obj){
														
														if(obj.is_template != 0){
															
															obj.is_template = 0;
														
															sb.data.db.obj.update('proposals', obj, function(response){
																
																if(!response){
																		
																	sb.dom.alerts.alert('Error', 'Oops. Something went wrong.', 'error');
																	
																}
																
															});
															
														}
														
													});
													
												}else{
													
													sb.dom.alerts.alert('Error', 'Oops. Something went wrong.', 'error');
													
												}
												
												sb.notify({
													type:'app-navigate-to',
													data:{
														itemId:'workorders',
														viewId:'templateTable',
														redraw:true
													}
												});
												
											}
										}
									},
									visibleCols:{
										object_uid:'ID',
										name: 'Name',
										type:'Type'
									},
									cells: {
										start_date:function(obj){
											return moment(obj.start_date).format('M/D/YYYY h:mm a');
										},
										manager:function(obj){
											
											var manager = 'Not selected';
																						
											if(obj.manager){
												
												if(obj.manager.fname){
													
													manager = obj.manager.fname +' '+ obj.manager.lname;
													
												}
												
											}
											
											return manager;
											
										},
										type: function(obj){
											
											if(obj.type){
												return obj.type.name;
											}else{
												return 'Not selected';
											}
											
										}, 
										status: function(obj){
											
											var css = 'label-warning';
											
											switch(obj.status){
												
												case 'Paid':
												
													css = 'label-success';
												
													break;
												
											}
											
											if(obj.status){
												return '<label class="label '+ css +'">'+ obj.status +'</label>';
											}else{
												return '<label class="label label-danger">Proposal</label>';
											}
											
										},
										main_contact: function(obj){
											
											var mainContact = 'No Contact on File';
											
											if(obj.main_contact != null){
												
												mainContact = obj.main_contact.fname +' '+ obj.main_contact.lname;
											}
											
											return mainContact;
										}
									},
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.name;
										},
										action:function(obj, dom, state, draw){
											
											singleWorkorderViewNew(obj.main_object, dom, state, draw)
											
										}
									},
									childObjs:3,
									data:function(paged, callback){
																		
										sb.data.db.obj.getAll('proposals', function(ret){
											
											var queryObj = {is_template:1};
											
											ret.data = _.where(ret.data, queryObj);
										
											callback(ret);
											
										}, 3, paged);
										
									}
								}								
							},
/*
							{
								id:'metrics',
								default:false,
								type:'custom',
								title:'Report',
								icon:'<i class="fa fa-paste"></i>',
								dom:function(dom, state, draw){
									
									sb.data.db.obj.getSum('work_orders', 'potential_value', {
										groupOn:'status',
										groupBy:'year',
										dateRange:{
											start: moment('2017-01-01', 'YYYY-MM-DD').format('YYYY-MM-DD HH:mm:ss.SS'),
											end: moment().format('YYYY-MM-DD HH:mm:ss.SS')
										}
									}, function(workorderData){
										
										function random_rgba(amount) {
											
											if(amount){
												
												var count = 0;
												var ret = [];
												
												while(count < amount){
													
													count++;
													
													var o = Math.round, r = Math.random, s = 255;
												    
												    ret.push('rgba(' + o(r()*s) + ',' + o(r()*s) + ',' + o(r()*s) + ',' + r().toFixed(1) + ')');
													
												}
												
												return ret;
												
											}else{
												
												var o = Math.round, r = Math.random, s = 255;
											    return 'rgba(' + o(r()*s) + ',' + o(r()*s) + ',' + o(r()*s) + ',' + r().toFixed(1) + ')';
												
											}
											
										}
										
										function updateDateRange(start, end, label) {
											$('#reportrange span').html(' '+ start.format('MM-DD-YYYY') + ' - ' + end.format('MM-DD-YYYY') +' ');
										}
										
										workorderData = _.reject(workorderData, function(group){
											
											return group.grouped == null;
											
										});
										
console.log('workorderData', workorderData, _.pluck(workorderData, 'grouped_total'));									
										dom.makeNode('report', 'div', {css:''});
										
										dom.makeNode('reportBreak', 'div', {text:'<br />'});
									
										var report = dom.report;
										
										//report.makeNode('title', 'div', {text:'Work Order Report', css:'ui huge header'});

										report.makeNode('date', 'div', {id:'reportrange', css:'reportrange', style:'background: #fff; cursor: pointer; padding: 5px 10px; border: 1px solid #ccc; width: 100%'});

										report.makeNode('statcont', 'div', {css:''});
										report.statcont.makeNode('statcontTitle', 'div', {css:'ui huge header', text:'Work Order Totals'});
										report.statcont.makeNode('stats', 'div', {css:'six tiny ui steps'});
										
										_.each(workorderData, function(status){
											
											report.statcont.stats.makeNode('stat-'+ status.grouped, 'div', {css:'ui step'})
												.makeNode('stat', 'div', {css:'ui blue statistic'});
											report.statcont.stats['stat-'+ status.grouped].stat.makeNode('label', 'div', {css:'label', text:status.grouped});
											report.statcont.stats['stat-'+ status.grouped].stat.makeNode('value', 'div', {css:'value', text:status.grouped_total});
																						
										});
										
										report.statcont.makeNode('reportBreak', 'div', {text:'<br />'});
				
										report.statcont.makeNode('valuestattitle', 'div', {css:'ui huge header', text:'Estimated Value'});
										report.statcont.makeNode('valuestat', 'div', {css:'six tiny ui steps'});
						
										_.each(workorderData, function(status){
											
											var value = 0;

											if(status.sum > 0){
												value = sb.dom.formatNumber(status.sum);
											}

											report.statcont.valuestat.makeNode('stat-'+ status.grouped, 'div', {css:'ui step'})
												.makeNode('stat', 'div', {css:'ui mini green statistic'});
											report.statcont.valuestat['stat-'+ status.grouped].stat.makeNode('label', 'div', {css:'label', text:status.grouped});
											report.statcont.valuestat['stat-'+ status.grouped].stat.makeNode('value', 'div', {css:'value', text:'$'+ value.toString()});
																						
										});
										
										report.statcont.makeNode('chartBreak', 'div', {text:'<br /><br /><br />'});
										
										report.statcont.makeNode('chartcont', 'div', {css:'ui grid'});
										
										report.statcont.chartcont.makeNode('bar', 'column', {w:10});
										report.statcont.chartcont.makeNode('pie', 'column', {w:6});
										
										report.statcont.chartcont.bar.makeNode('total', 'chart', {
											type:'bar',
											data:{
										        labels: _.pluck(workorderData, 'grouped'),
										        datasets: [{
										            label: 'Total Count',
										            data: _.pluck(workorderData, 'grouped_total'),
										            borderWidth: 1,
										            borderColor: random_rgba(workorderData.length),
										            backgroundColor: random_rgba(workorderData.length)
										        }]
										    },
										    options:{
											    scales: {
											        yAxes: [{
											            ticks: {
											                beginAtZero: true
											            },
											            stacked:true
											        }],
											        xAxes: [{
												        stacked:true
											        }]
											    }
											}
										});
										
										report.statcont.chartcont.pie.makeNode('chart', 'chart', {
											type:'pie',
											data:{
										        labels: _.pluck(workorderData, 'grouped'),
										        datasets: [{
										            label: 'Estimated Value',
										            data: _.pluck(workorderData, 'sum'),
										            borderWidth: 1,
										            borderColor: random_rgba(workorderData.length),
										            backgroundColor: random_rgba(workorderData.length)
										        }]
										    }
										});
										
										draw({
											dom:dom,
											after:function(dom){
												
												$(dom.report.date.selector).daterangepicker({
												    "startDate":moment().subtract(30, 'day').startOf('day'),
												    "endDate":moment().endOf('day'),
													"opens":"right",
													"format":"MM-DD-YYYY",
													"showDropdowns":true,
												    "ranges": {
												        "Today": [moment().startOf('day'),moment().endOf('day')],
												        "Yesterday": [moment().subtract(1, 'day').startOf('day'), moment().subtract(1, 'day').endOf('day')],
												        "Last 7 Days": [moment().subtract(7, 'day').startOf('day'), moment().endOf('day')],
												        "Last 30 Days": [moment().subtract(30, 'day').startOf('day'), moment().endOf('day')],
												        "This Month": [moment().startOf('month').startOf('day'), moment().endOf('month').endOf('day')],
												        "This Year": [moment().month(0).startOf('month').startOf('day'),moment().endOf('day')],
												        "Last Month": [moment().subtract(1, 'month').startOf('month').startOf('day'),moment().subtract(1, 'month').endOf('month').endOf('day')],
												        "Last Year": [moment().subtract(1, 'year').month(0).startOf('month').startOf('day'),moment().subtract(1, 'year').month(11).endOf('month').endOf('day')]
												    }
												}, updateDateRange);
												
												updateDateRange(moment().subtract(30, 'day').startOf('day'), moment().endOf('day'), '');
												
											}
										});
										
									});
									
								}								
							},							
*/
/*
							{
								id: 'workorders-kanban',
								type: 'kanban',
								title: 'Workorders Kanban',
								icon: '<i class="fa fa-columns"></i>',
								setup: {
									objectType: 'work_orders',
									childObjs: 1,
									data: function(paged, callback) {
										
										sb.data.db.obj.getAll('work_orders', function(ret) {

											callback(ret);
											
										}, 1, paged);
										
									},
									navigation: false,
									searchObjects: false,
									rowLink: false,
									kanban: {
										title: 'Workorders Kanban',
										groupBy: 'status',
										groups: [
											{
												name: 'Prospecting',
												value: 'Prospecting',
												color: ''
											},
											{
												name: 'Proposal',
												value: 'Proposal',
												color: 'orange'
											},
											{
												name: 'Sent',
												value: 'Sent',
												color: ''
											},
											{
												name: 'Accepted',
												value: 'Accepted',
												color: ''
											},
											{
												name: 'Signed',
												value: 'Signed',
												color: 'green'
											},
											{
												name: 'Paid',
												value: 'Paid',
												color: ''
											},
											{
												name: 'Complete',
												value: 'Complete',
												color: 'blue'
											}
										],
										cardUI: build_singleWorkorder
									}
								}
							},
*/
							{
								id:'settings',
								type:'settings',
								title:'Settings',
								icon:'<i class="fa fa-cog"></i>',
								setup:[
									{
										object_type:'event_type_options',
										name:'1. Work Order Types',
										formObjs:{
											name:{},
											guests:{},
											email_template_subject:{},
											email_template_body:{
												name:'email_template_body',
												label:'Email Template Body',
												type:'textbox'
											}
										}
									},
									{
										object_type: 'contract_types',
										name: '2. Contract Types'
									},
									{
										object_type: 'contract_templates',
										name: '3. Contract Templates',
										action:function(dom){
											
											dom.empty();
																						
											dom.makeNode('table', 'div', {});
											
											dom.patch();
											
											sb.notify({
												type:'view-contract-templates',
												data:{
													domObj:dom.table
												}
											});
											
										}
									},
									{
										name: '4. ESD <small>Elec. Signature Disclaimer</small>',
										object_type: 'contract_settings',
										action:editSystemSettings
									},
									{
										name:'5. Proposal Email Template',
										object_type:'contract_settingss',
										action:emailTemplateSettings
									},
									{
										name:'6. Default Opportunity Note',
										object_type:'test',
										action:defaultOpportunityNote
									},
									{
										name:'7. Default Opportunity Follow Up Date',
										object_type:'test1',
										action:defaultOpportunityFollowUpDate
									},
									{
										name:'8. Packlist checkpoints',
										object_type:'packlist_stages',
										action:function(dom){
											
											function get_data(callback){
												
												sb.data.db.obj.getAll('packlist_checkpoints', function(data){
													
													if (data) {
														callback(data[0]);
													} else {
														
														sb.data.db.obj.create('packlist_checkpoints', {}, function(created){
															callback(created);
														});
														
													}
													
												});
												
											}
											
											dom.empty();
											dom.makeNode('table', 'div', {});
											dom.patch();
											
											get_data(function(settingObj){
												
												sb.notify({
													type:'show-workflow-states-view',
													data:{
														dom:dom.table,
														state:{
															header: 'Packlist flow<div class="sub header">Define your packlist workflow.</div>',
															object: settingObj,
															stateProperty: 'checkpoints'
														}
													}
												});
												
												dom.makeNode('saveBtn', 'div', {
													tag:'button',
													css:'ui right floated blue button',
													text:'Save'
												}).notify('click', {
													type:'workorders-run',
													data:{
														run:function(){
															
															dom.saveBtn.loading();
															
															settingObj.checkpoints = settingObj.states;
															sb.data.db.obj.update('packlist_checkpoints', {
																id: settingObj.id,
																checkpoints: settingObj.checkpoints
															}, function(){
																
																dom.saveBtn.loading(false);
																
															});
															
														}
													}
												});
												
												dom.patch();
												
											});
											
										}
									},
									{
										name:'9. Require Proposal Approval',
										object_type:'proposals',
										action:requireProposalApproval
									}
								]
							},
							{
								id:'tags',
								type:'tags',
								title:'Tags',
								icon:'<i class="fa fa-tags"></i>',
								color:'red',
								setup:{
									type:['work_orders'],
									childObjs:1,
									resultList:function(dom, obj){

										var mainContact = 'No Contact Saved';
										
										if(obj.main_contact != null && obj.main_contact !== false)
											mainContact = obj.main_contact;

										dom.makeNode('content', 'div', {css: 'content'});
										
										if(obj.status != null && obj.status !== false)
											dom.content.makeNode('label', 'div', {css: 'ui top attached label', text:`${obj.status}`});
											
										dom.content.makeNode('header', 'div', {css:'ui header', text: `<i class="fa fa-th-list"></i> ${obj.name}`});
										
										if(obj.type){
											dom.content.makeNode('meta', 'div', {css:'meta', text:`<span class="small">Type:</span> ${obj.type.name}`});
										}
										
										dom.content.makeNode('contact', 'div', {css: '', tag:'p', text:`<span class="small">Main Contact:</span> ${mainContact.fname} ${mainContact.lname}`});

										dom.makeNode('btn', 'div', {css: 'ui bottom attached button', text:`View ${obj.name}`});									
										
										dom.btn.notify('click', {
											type: 'app-navigate-to',
											data: {
												itemId: 'workorders',
												viewId:{
													id:'single-'+obj.id,
													type:'table-single-item',
													title:obj.name,
													icon:'<i class="fa fa-th-list"></i>',
													setup:{
														objectType:'work_orders'
													},
													dom:singleWorkorderViewNew,
													rowObj:obj,
													removable:true,
													parent:'table'
												}
											}
										}, sb.moduleId);
										
										return dom;		
									}
								}
							},
							{
								id:'newWorkorder',
								type:'modal',
								title:'Create Work Order',
								icon:'<i class="fa fa-plus"></i>',
								color:'green',
								modal:function(modal, state, draw){
									
									modal.footer.makeNode('btns', 'buttonGroup', {});
																	
									modal.footer.makeNode('button', 'button', {text:'<i class="fa fa-times"></i> Close', css:'pda-btnOutline-red pda-btn-x-small'})
										.notify('click', {
											type:'workorders-run',
											data:{
												run:function(){
													
													this.hide();
													
												}.bind(modal)
											}
										}, sb.moduleId);
									
									modal.body.makeNode('break', 'lineBreak', {});
									
									modal.body.makeNode('header', 'headerText', {text:'You can create a new Work Order under a Contact.<br />How would you like to start?', css:'text-center', size:'x-small'});
									
									modal.body.makeNode('btns', 'buttonGroup', {});
									
									modal.body.btns.makeNode('search', 'button', {text:'Search for a Contact', css:'pda-btn-primary pda-btn-x-large'})
										.notify('click', {
											type:'workorders-run',
											data:{
												run:function(){
													
													this.hide();
													
													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'contacts',
															viewId:'table'
														}
													});
													
												}.bind(modal)
											}
										}, sb.moduleId);
									
									modal.body.btns.makeNode('create', 'button', {text:'Create a new Company/Client', css:'pda-btn-primary'})
										.notify('click', {
											type:'workorders-run',
											data:{
												run:function(){
													
													this.hide();
													
													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'companies',
															viewId:'create'
														}
													});
													
												}.bind(modal)
											}
										}, sb.moduleId);
									
									draw(modal);
																											
								}	
							},
							{
								id:'edit',
								type:'custom',
								title:'Edit',
								icon:'<i class="fa fa-pencil"></i>',
								display:false,
								dom:editEvent
							}
						]
					}
				}
			});
			
			components.table = sb.createComponent('crud-table');
			components.templateSelectionTable = sb.createComponent('crud-table');
// 			components.menuBuilder = sb.createComponent('inventory');
			components.tags = sb.createComponent('tags');
			//components.emails = sb.createComponent('emails');
			components.workflows = sb.createComponent('workflows');

			sb.listen({
				'workorders-run':this.run,
				'add-workorder-to-contact':this.addWorkorderToContact,
				'workorders-object-view':this.showObjectView,
				'view-workorder-chart-of-accounts-report':this.showWorkorderChartOfAccountsReport
			});
			
		},
				
		run: function(data){ data.run(data); },
		
		showObjectView: function(data){
			
			var dom = data.domObj,
				pagedObj = {
					page: 0,
					pageLength: 6,
					paged: true,
					sortCast: 'string',
					sortCol: 'date_created',
					sortDir: 'desc',
					objectType: 'work_orders'
				};
									
			sb.data.db.obj.getWhere('work_orders', {main_contact: data.contact.id, childObjs: 2},function(resp){
				
				sb.data.db.obj.getWhere('work_orders', {additional_contacts:{type:'contains', value:data.contact.id}, childObjs:2}, function(addObjs){
					
					contactView(dom, {
						data:resp.concat(addObjs),
						contact:data.contact
					}, data.draw);
					
				});
				
			});
						
		},
		
		start: function(data){
			
			components.table = sb.createComponent('crud-table');
// 			components.menuBuilder = sb.createComponent('inventory');
			//components.contracts = sb.createComponent('contractsComponent');
			//components.files = sb.createComponent('file-nav');
			components.tags = sb.createComponent('tags');
			
			if(data.objectId){
				objectId = data.objectId;
			}
			if(data.contact){
				contact = data.contact;
				objectId = data.contact.id;
			}
			if(data.settings === false) {
				showSettings = false;
			}
			if(data.calendar === false) {
				showCalendar = false;
			}
			
			if(data.hasOwnProperty('tableView')){
				
				showSettings = false,
				showCalendar = false,
				homeScreenDefault = false;
	
			}

			ui = sb.dom.make(data.domObj.selector);
			
			tableUI = ui.makeNode('table', 'container', {});
			tableUI.run = startTableUI;
			
			ui.build();
			
			tableUI.run();
			
		},
		
		addWorkorderToContact: function(data){

			addWorkorderToContactNew(data.dom, data.state, data.draw);		
			
		},
		
		showWorkorderChartOfAccountsReport: function(data){
			
			chart_of_accounts_report_ui (data.dom, data.state, data.draw);
			
		}
									
	}
	
});