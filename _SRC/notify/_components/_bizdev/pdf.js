Factory.register('pdf-tool', function(sb) {

	function build_toolCollections(dom, state, draw) {

		var collectionsSetup = {
			domObj: dom,
			state: state,
			objectType: 'pdfs',
			actions: {
				view: false,
				create: function(ui, obj_info, onComplete) {

					function getTemplateQuery(state, callback) {

						var where = {
							is_template: 1,
							childObjs: 1,
							active: {
								type: 'not_equal',
								value: 'No'
							},
						};
				
						if (typeof callback === 'function') {
							callback(where);
						} else {
							return where;
						}
				
					}

					function chooseATemplate(pdfs, dom, state, onCreate) {

						dom.makeNode('body', 'div', {});
				
						var modal = dom;
				
						var headerText = state.is_template ? 'New PDF Template' : 'New PDF';
						modal.body.makeNode('title', 'headerText', {text: headerText, size: 'large'});
				
						modal.body.makeNode('loading', 'div', {});
						modal.body.loading.makeNode('text', 'text', {text:'Loading templates...', css:'text-center'});
						modal.body.loading.makeNode('loader', 'loader', {});
				
						dom.patch();
						
						modal.body
							.makeNode('col', 'column', {w:16})
								.makeNode(
									'table',
									'table',
									{
										css: 'table-hover table-condensed',
										columns: {
											name: 'Template Name',
											btns: ''
										}
									}
								);
				
						_.each(pdfs, function(p){
				
							modal.body.col.table.makeRow('row-'+p.id, [p.name, '']);
							modal.body.col.table.body['row-'+p.id].btns.makeNode('use', 'button', {text:'<i class="fa fa-plus"></i> Use This Template', css:'pda-btn-green', style:'float:right;'}).notify('click', {
								type:'pdf-tool-run',
								data:{
									run:function(pdf, obj, data, dom) {
				
										this.body.col.table.body['row-'+pdf.id].btns.use.loading();
											
										obj.name = pdf.name + '-COPY';
										obj.active = 'Yes';
				
										sb.data.db.obj.erase('pdfs', obj.id, function(updated){

											var toCreate = _.clone(obj);
											toCreate.tagged_with = appConfig.state.layer === 'myStuff' ? [sb.data.cookie.get('uid')] : [appConfig.state.pageObject.id];
											toCreate.parent = appConfig.state.layer === 'myStuff' ? sb.data.cookie.get('uid') : appConfig.state.pageObject;
											delete toCreate.setTemplateProps;
											delete toCreate.options;

											// Check to see if there is a project upstream and tag with main contact
											if ( appConfig.state.pageObject.hasOwnProperty('group_type') ) {
												if (appConfig.state.pageObject.group_type === 'Project') {
													if ( appConfig.state.pageObject.hasOwnProperty('main_contact') ) {
														if (appConfig.state.pageObject.main_contact) {
															if ( appConfig.state.pageObject.main_contact.hasOwnProperty('id') ) {
																if (appConfig.state.pageObject.main_contact.id) {
																	toCreate.tagged_with.push(parseInt(appConfig.state.pageObject.main_contact.id))
																}
															}
															if ( appConfig.state.pageObject.main_contact.hasOwnProperty('company') ) {
																if (appConfig.state.pageObject.main_contact.company) {
																	toCreate.tagged_with.push(parseInt(appConfig.state.pageObject.main_contact.company))
																}
															}
														}
													}
												}
											}
											
											sb.data.db.obj.create('pdfs', toCreate, function(created){
				
												onCreate(created);
				
												var edit = created.is_template ? true : false;
												
												window.location.href = sb.data.url.createPageURL(
													'object', {
													type: 'pdfs',
													id: created.id,
													name: created.name
												});
				
											}, 2);
				
										});
				
									}.bind(modal, p, state, dom)
								}
							}, sb.moduleId);
				
						});
						
						modal.body.makeNode('col2', 'column', {w:16});
						var newPDFText = state.is_template ? 'Create Blank PDF Template <i class="fa fa-arrow-right"></i>' : 'Create Blank PDF <i class="fa fa-arrow-right"></i>';
						modal.body.col2.makeNode('newPDF', 'button', {text: newPDFText, css: 'pda-btn-green', style: 'margin-top:30px; float:right;'})
							.notify('click', {
								type:'pdf-tool-run',
								data:{
									run:function() {
				
										modal.body.col2.newPDF.loading();
				
										createNewBlankPDF(state);
				
									}
								}
							}, sb.moduleId);
				
				
						delete modal.body.loading;
				
						modal.body.patch();
				
						modal.show();
						
					}

					function createNewBlankPDF(state) {

						state.tagged_with = appConfig.state.layer === 'myStuff' ? [sb.data.cookie.get('uid')] : [appConfig.state.pageObject.id];
						state.parent = appConfig.state.layer === 'myStuff' ? sb.data.cookie.get('uid') : appConfig.state.pageObject;

						// Check to see if there is a project upstream and tag with main contact
						if ( appConfig.state.pageObject.hasOwnProperty('group_type') ) {
							if (appConfig.state.pageObject.group_type === 'Project') {
								if ( appConfig.state.pageObject.hasOwnProperty('main_contact') ) {
									if (appConfig.state.pageObject.main_contact) {
										if ( appConfig.state.pageObject.main_contact.hasOwnProperty('id') ) {
											if (appConfig.state.pageObject.main_contact.id) {
												state.tagged_with.push(parseInt(appConfig.state.pageObject.main_contact.id))
											}
										}
										if ( appConfig.state.pageObject.main_contact.hasOwnProperty('company') ) {
											if (appConfig.state.pageObject.main_contact.company) {
												state.tagged_with.push(parseInt(appConfig.state.pageObject.main_contact.company))
											}
										}
									}
								}
							}
						}

						sb.data.db.obj.create('pdfs', state, function(obj) {
							
							window.location.href = sb.data.url.createPageURL(
							  'object', {
								type: 'pdfs',
								id: obj.id,
								name: state.name
							});
				
						});
						
					}

					function createWorkflow(ui, state, obj_info, onComplete) {

						// Get contracts (checking here to see if we need to show the modal to choose a contract)
						sb.data.db.obj.getWhere('pdfs', getTemplateQuery(state), function(pdfs) {
				
							obj_info.name = obj_info.is_template ? 'Untitled PDF Template' : 'Untitled PDF';
				
							if ( !_.isEmpty(pdfs) ) {
						
								chooseATemplate(pdfs, ui, obj_info, onComplete);
								
							} else {
								
								createNewBlankPDF(obj_info);
							}
							
						});
				
					}

					createWorkflow(ui, state, obj_info, onComplete);

				}
			},
			fields: {
				name: {
					title: 'Name'
				},
				date_created: {
					title: 'Date Created',
					view: function(dom, obj) {

						dom.makeNode('date_created', 'div', {text: moment(obj.date_created).local().format('M/D/YYYY h:mm a')});

					}
				},
				last_updated: {
					title: 'Last Updated',
					view: function(dom, obj) {

						dom.makeNode('last_updated', 'div', {text: moment(obj.last_updated).local().format('M/D/YYYY h:mm a')});

					}
				}
			},
			where: {
				tagged_with: [state.pageObject.id],
				active: {
					type: 'not_equal',
					value: 'No'
				},
				childObjs: {
					name: true,
					date_created: true,
					last_updated: {
						fname: true,
						lname: true
					}
				}
			}
		};

		sb.notify({
            type: 'show-collection',
            data: collectionsSetup
        });

	}

	function singleView(ui, state, draw, mainDom) {

		var selectedPDFs = []; 

		var grid = ui.makeNode('grid', 'div', {
			css: 'ui stackable grid'
		});

		var header = grid.makeNode('header', 'div', {
			css: 'eight wide column',
		});

		sb.notify ({
			type: 'view-field'
			, data: {
				type: 'title'
				, property: 'name'
				, obj: state.pageObject
				, options: {
					edit: true,
					editing: true,
					fontSize: '2.5rem'
				}
				, ui: header.makeNode('title', 'div', {})
				, onUpdate: function (newVal) {
					
					if (!_.isEmpty(newVal)) {
						state.name = newVal;
					}
					
				}
			}
		});

		function getFileURL(file, options, callback) {

			var fileURL = '';
			if (file.loc) {
				fileURL = sb.bucket + appConfig.instance + '/' + file.loc;
			} else if (file.url) {
				fileURL = file.url;
			}

			var fileType = '';
			if (file.object_bp_type) {
				fileType = file.object_bp_type;
			} else if (file.type) {
				fileType = file.type;
			}

			if (fileType === 'contracts') {
				sb.data.makePDF(file.html_string, 'F', options, function(filePath) {
					fileURL = filePath;
				});
			}

			callback(fileURL);

		}

		function processPDF(obj, save, callback) {

			$('#loader').fadeIn();
			mergeBtn.loading();
			previewBtn.loading();

			var accessToken = '';
			var jobID = '';
			var mergePDFs = [];

			function mergePDF(fileName, fileURL, i, callback) {

				if (i === 0) {

					sb.data.db.controller('getEasyPDFCloudAccessToken', {}, function(response) {

						var parsedResponse = JSON.parse(response);
						accessToken = parsedResponse.access_token;

						sb.data.db.controller('createEasyPDFCloudJob', {
							accessToken: accessToken, 
							fileName: fileName, 
							fileURL: fileURL
						}, function(response) {
			
							var parsedResponse = JSON.parse(response);
							jobID = parsedResponse.jobID;

							callback();

						});
					
						
					}, sb.url + '/api/_getAdmin.php?do=');

				} else if (i > 0) {

					sb.data.db.controller('addFileToEasyPDFCloudJob', {
						accessToken: accessToken,
						jobID: jobID,
						fileName: fileName, 
						fileURL: fileURL
					}, function(response) {

						if (i === selectedPDFs.length - 1) {

							sb.data.db.controller('startEasyPDFCloudJob', {
								accessToken: accessToken, 
								jobID: jobID
							}, function(response) {
		
								sb.data.db.controller('waitOnEasyPDFCloudJob', {
									accessToken: accessToken, 
									jobID: jobID
								}, function(response) {
									
									sb.data.db.controller('downloadEasyPDFCloudFile', {
										accessToken: accessToken, 
										jobID: jobID,
										obj: obj,
										save: save
									}, function(response) {
		
										var parsedResponse = JSON.parse(response);
			
										sb.data.db.controller('deleteEasyPDFCloudJob', {
											accessToken: accessToken, 
											jobID: jobID
										}, function(response) {
				
										});

										$('#loader').fadeOut();
										mergeBtn.loading(false);
										previewBtn.loading(false);

										showViewFileButton(obj, actionBtnContainer);

										callback(parsedResponse);
			
									});
		
								});
		
							});

						} else {

							callback();

						}

					});

				}

			}

			var i = 0;
			function createPDFs(callback) {

				var fileName = 'combined' + i + '.pdf';

				var options = [];
				options.name = 'combined' + i;

				getFileURL(mergePDFs[i], options, function(fileURL) {

					mergePDF(fileName, fileURL, i, function(response) {
						if (i < mergePDFs.length - 1) {
							i++;
							createPDFs(callback);
						} else {
							callback(response);
						}
					});

				});

			}

			sb.data.db.obj.getById('', _.pluck(selectedPDFs, 'id'), function(queue) {
				
				_.each(queue, function(queueItem) {
				
					var mergeDocumentID = queueItem.id;
					var mergeDocumentName = queueItem.name;
					var mergeDocumentHTMLString = queueItem.html_string ? queueItem.html_string : '';
					var mergeDocumentURL = sb.bucket + appConfig.instance + '/' + queueItem.loc;
					var mergeDocumentType = queueItem.object_bp_type;

					mergePDFs.push({
						id: mergeDocumentID,
						name: mergeDocumentName,
						html_string: mergeDocumentHTMLString,
						url: mergeDocumentURL,
						type: mergeDocumentType
					});

				});

				createPDFs(function(response) {
					callback(response);
				});

			});

		}

		function showViewFileButton(obj, ui) {

			ui.makeNode('viewFile', 'button', {
				css: 'ui medium tertiary button',
				style: 'float:right;',
				text: '<i class="fas fa-external-link"></i> View PDF File'
			}).notify('click', {
				type:'pdf-tool-run',
				data:{
					run:function() {
	
						
	
					}
	
				}
			});

			ui.patch();

		}

		var actionBtnContainer = grid.makeNode('actionBtnContainer', 'div', {
			css: 'eight wide column',
		});

		actionBtnContainer.makeNode('mergeBtn', 'button', {
			css: 'ui medium green button',
			style: 'float:right;',
			text: 'Merge & Save'
		}).notify('click', {
			type:'pdf-tool-run',
			data:{
				run:function() {

					processPDF(state.pageObject, true, function(response) {

						// Show alert
						sb.notify({
							type: 'display-alert',
							data: {
								header: 'Merged & Saved',
								body: 'PDF saved to Files successfully!',
								color: 'green'
							}
						});

					});

				}

			}
		});

		actionBtnContainer.makeNode('previewBtn', 'button', {
			css: 'ui medium basic grey button',
			style: 'float:right;',
			text: 'Preview'
		}).notify('click', {
			type:'pdf-tool-run',
			data:{
				run:function() {

					processPDF(state.pageObject, false, function(response) {

						// Set variables
						var fileName = response.fileName;

						// View the file
						window.open(sb.url + '/api/' + fileName);

					});

				}
			}
		});

		var previewBtn = actionBtnContainer.previewBtn;
		var mergeBtn = actionBtnContainer.mergeBtn;

		var leftCol = grid.makeNode('leftCol', 'div', {
			css: 'eight wide column'
		});

		var rightCol = grid.makeNode('rightCol', 'div', {
			css: 'eight wide column'
		});

		var queueContainer = rightCol.makeNode('queueContainer', 'div', {
			css: 'round-border',
			style: 'padding:15px; min-height:937px !important;'
		})

		queueContainer.makeNode('queueTitle', 'div', {
			css: 'ui header',
			text: '<i class="fas fa-search grey icon"></i> Preview'
		});

		var queuePlaceholder = queueContainer.makeNode('queuePlaceholder', 'div', {
			css: 'ui placeholder segment',
			style: 'min-height:858px !important;',
			text: '<div class="ui icon header">No PDFs added, yet!</div>'
		});

		var queueContainerDropZone = queueContainer.makeNode('queueContainer', 'div', {
			drag: {
				moves: false,
				drop: 'queue-document-dropped',
				accepts: true,
				copy: false
			}
		});

		function pushSelectedPDFs(obj, callback) {
			selectedPDFs.push({
				id: obj.id,
				domID: obj.id + '-' + sb.dom.randomString(6, '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')
			});
			callback();
		}

		function updateQueue(obj) {
			var cleanedQueue = [];
			_.each(selectedPDFs, function(selectedPDF) {
				cleanedQueue.push({
					id: selectedPDF.id
				});
			});
			obj.queue = cleanedQueue;
			sb.data.db.obj.update('pdfs', obj, function(updated){}, 2);
		}

		function createQueueItems(obj, state, initialize) {

			queueContainerDropZone.empty();

			sb.data.db.obj.getById('', _.pluck(selectedPDFs, 'id'), function(queue) {

				var count = _.size(selectedPDFs);
				var i = 1;
				_.each(selectedPDFs, function(selectedPDF) {

					var queueItem = _.find(queue, function(queueItem) { return queueItem.id == selectedPDF.id });
					var queueDocumentID = queueItem.id
					var queueDocumentName = queueItem.name;
					var queueDocumentDomID = selectedPDF.domID;

					var display = (i == count) ? 'display:none !important;' : '';
					var queueDocumentDom = queueContainerDropZone.makeNode('queueDocument-' + queueDocumentDomID, 'div', {
						css: 'ui stackable grid',
						style: 'border:1px solid #ebebeb; border-radius:0.375em; margin-bottom:5px !important;' + display,
						data: {
							'data-id': queueDocumentID
						},
						drag: {
							moves: true,
							drop: function() {
								selectedPDFs = [];
								$(queueContainerDropZone.selector).children().each(function(i, elm) {
									selectedPDFs.push({
										id: $(this)[0].dataset.id
									});
								});
								updateQueue({id:state.id, queue:selectedPDFs});
								createQueue({id:state.id, queue:selectedPDFs});
							},
							accepts: false,
							copy:false,
							direction: 'vertical'
						}
					});
					queueDocumentDom.makeNode('left', 'div', {
						css: 'twelve wide column',
						text: '<span><i class="fas fa-bars grey icon"></i> ' + queueDocumentName + '</span>',
					});

					queueDocumentDom.makeNode('right', 'div', {
						css: 'four wide column'
					});
					queueDocumentDom.right.makeNode('remove', 'div', {
						text: '<span style="float:right; cursor:pointer;"><i class="fas fa-times red"></i> Remove</span>'
					}).notify('click', {
						type:'pdf-tool-run',
						data:{
							run:function() {

								selectedPDFs = _.reject(selectedPDFs, function(selectedPDF) {
									return selectedPDF.domID == queueDocumentDomID;
								});

								$(queueDocumentDom.selector).addClass('animated zoomOut', function() {});
								$(queueDocumentDom.selector).hide('slow', function() {});

								if (!_.size(selectedPDFs)) {
									$(queuePlaceholder.selector).fadeIn();
								}

								if (_.size(selectedPDFs) > 1) {
									$(mergeBtn.selector).prop('disabled', false).removeClass('half-opacity');
									$(previewBtn.selector).prop('disabled', false).removeClass('half-opacity');
								} else {
									$(mergeBtn.selector).prop('disabled', true).addClass('half-opacity');
									$(previewBtn.selector).prop('disabled', true).addClass('half-opacity');
								}

								updateQueue({id:state.id, queue:selectedPDFs});

							}
						}
					});
					queueDocumentDom.right.makeNode('preview', 'div', {
						text: '<span style="float:right; margin-right:15px; cursor:pointer;"><i class="fas fa-external-link"></i> Preview</span>'
					}).notify('click', {
						type:'pdf-tool-run',
						data:{
							run:function() {

								getFileURL(queueItem, {}, function(fileURL) {
									window.open(fileURL);
								});

							}
						}
					});

					queueContainerDropZone.patch();

					$(document).ready(function() {
						$(queuePlaceholder.selector).fadeOut();
						$(queueDocumentDom.selector).show();
					});

					if (i == count && !initialize) {
						$(queueDocumentDom.selector).show('slow', function() {});
					}

					i++;

				});

				$(document).ready(function() {
					if (_.size(selectedPDFs) > 1) {
						$(mergeBtn.selector).prop('disabled', false).removeClass('half-opacity');
						$(previewBtn.selector).prop('disabled', false).removeClass('half-opacity');
					} else {
						$(mergeBtn.selector).prop('disabled', true).addClass('half-opacity');
						$(previewBtn.selector).prop('disabled', true).addClass('half-opacity');
					}
				});

			});

		}

		function createQueue(obj) {
			var i = 0;
			_.each(obj.queue, function(queueItem) {
				pushSelectedPDFs(queueItem, function() {
					if (i === obj.queue.length - 1) {
						createQueueItems(queueItem, obj, true);
					}
					i++;
				});
			});
		}

		function showPreviewButton(ui, obj, state) {

			ui.makeNode('row-' + obj.id, 'div', {});
			ui['row-' + obj.id].makeNode('preview', 'div', {
				text:'<i class="fa fa-external-link"></i> Preview', 
				css:'ui small basic button',
				style:'float:right;'
			}).notify('click', {
				type:'pdf-tool-run',
				data:{
					run:function() {
						getFileURL(obj, {}, function(fileURL) {
							window.open(fileURL);
						});
					}
				}
			});

		}

		function showSelectButton(ui, obj, state) {

			ui.makeNode('row-' + obj.id, 'div', {});
			ui['row-' + obj.id].makeNode('select', 'div', {
				text:'<i class="fa fa-plus"></i> Select', 
				css:'ui small basic grey button',
				style:'float:right;'
			}).notify('click', {
				type:'pdf-tool-run',
				data:{
					run:function() {
						pushSelectedPDFs(obj, function() {
							updateQueue({id:state.id, queue:selectedPDFs});
							createQueueItems(obj, state);
						});
					}
				}
			});

		}

		var tags = [];
		if (state.pageObject.parent) {
			tags = tags.concat(state.pageObject.parent.id);
			if (state.pageObject.parent.main_contact) {
				if (Array.isArray(state.pageObject.parent.main_contact)) {
					tags = tags.concat(state.pageObject.parent.main_contact.id);
				} else {
					tags = tags.concat(state.pageObject.parent.main_contact);
				}
			}
		}
		tags = _.unique(tags);
		
		// var actionItemsContainer = leftCol.makeNode('actionItemsContainer', 'div', {
		// 	css: 'remove-segment-padding-and-margins',
		// 	style: 'margin-bottom:30px;'
		// });

		// actionItemsContainer.makeNode('actionItemsTitle', 'div', {
		// 	css: 'ui header',
		// 	text: '<i class="fas fa-list blue icon"></i> Action Items'
		// });

		// var actionItemsCollectionContainer = actionItemsContainer.makeNode('actionItemsCollectionContainer', 'div', {});

		// ui.patch();

		// var taggedWith = [];
		// if (state.pageObject.parent) {
		// 	taggedWith = taggedWith.concat(state.pageObject.parent.id);
		// }
		// taggedWith = _.unique(taggedWith);

		// var collectionsSetup = {
		// 	domObj: actionItemsCollectionContainer,
		// 	state: state,
		// 	objectType: 'document',
		// 	actions: {},
		// 	fields: {
		// 		name: {
		// 			title: 'Name',
		// 			view: function (ui, obj) {
		// 				ui.makeNode('row-' + obj.oid, 'div', {
		// 					style: 'font-weight:bold',
		// 					text: obj.name
		// 				});
		// 			}
		// 		},
		// 		preview: {
		// 			title: '-',
		// 			view: function(ui, obj) {

		// 				showPreviewButton(ui, obj, state);

		// 			}
		// 		},
		// 		select: {
		// 			title: '-',
		// 			view: function(ui, obj) {

		// 				showSelectButton(ui, obj, state);
			
		// 			}
		// 		}
		// 	},
		// 	onBoxview: true,
		// 	selectedView: 'table',
		// 	subviews: {
		// 		table: {
		// 			hideSelectionBoxes: true,
		// 			hideRowActions: true
		// 		}						
		// 	},
		// 	sortCol: 'name',
		// 	sortDir: 'asc',
		// 	menu: false,
		// 	submenu: false,
		// 	ignorePageFilter: true,
		// 	hiddenTags: taggedWith,
		// 	selectedTags: [4759703],
		// 	tags: [4759703, 4759702, 4759706],
		// 	allTagsAreToggleable: true,
		// 	canSearchForTags: false,
		// 	minTagSelection: 1,
		// 	maxTagSelection: 1,
		// 	pageLength: 5,
		// 	hidePagingAbove: true,
		// 	searchAboveCollection: true,
		// 	where: {
		// 		file_type: 'pdf',
		// 		active: {
		// 			type: 'not_equal',
		// 			value: 'No'
		// 		},
		// 		childObjs: {
		// 			name: true,
		// 			file_name: true,
		// 			loc: true
		// 		}
		// 	}
		// };
	
		// sb.notify({
		// 	type: 'show-collection',
		// 	data: collectionsSetup
		// });


		var filesContainer = leftCol.makeNode('filesContainer', 'div', {
			css: 'remove-segment-padding-and-margins',
			style: 'margin-bottom:30px;'
		});

		filesContainer.makeNode('filesTitle', 'div', {
			css: 'ui header',
			text: '<i class="fas fa-folder-open blue icon"></i> Organization Files'
		});

		var filesCollectionContainer = filesContainer.makeNode('filesCollectionContainer', 'div', {});

		ui.patch();

		var selectedTags = [state.pageObject.parent.id];

		var collectionsSetup = {
			domObj: filesCollectionContainer,
			state: state,
			objectType: 'document',
			actions: {},
			fields: {
				name: {
					title: 'Name',
					view: function (ui, obj) {
						ui.makeNode('row-' + obj.oid, 'div', {
							style: 'font-weight:bold',
							text: obj.name
						});
					}
				},
				preview: {
					title: '-',
					view: function(ui, obj) {

						showPreviewButton(ui, obj, state);

					}
				},
				select: {
					title: '-',
					view: function(ui, obj) {

						showSelectButton(ui, obj, state);
			
					}
				}
			},
			onBoxview: true,
			selectedView: 'table',
			subviews: {
				table: {
					hideSelectionBoxes: true,
					hideRowActions: true
				}						
			},
			sortCol: 'name',
			sortDir: 'asc',
			menu: false,
			submenu: false,
			ignorePageFilter: true,
			selectedTags: selectedTags,
			tags: tags,
			allTagsAreToggleable: true,
			minTagSelection: 1,
			maxTagSelection: 1,
			pageLength: 5,
			hidePagingAbove: true,
			searchAboveCollection: true,
			where: {
				file_type: 'pdf',
				active: {
					type: 'not_equal',
					value: 'No'
				},
				childObjs: {
					name: true,
					file_name: true,
					loc: true
				}
			}
		};
	
		sb.notify({
			type: 'show-collection',
			data: collectionsSetup
		});


		var documentsContainer = leftCol.makeNode('documentsContainer', 'div', {
			css: 'remove-segment-padding-and-margins',
			style: 'margin-bottom:30px;'
		});

		documentsContainer.makeNode('documentsTitle', 'div', {
			css: 'ui header',
			text: '<i class="fas fa-file yellow icon"></i> Organization Documents'
		});

		var documentsCollectionContainer = documentsContainer.makeNode('documentsCollectionContainer', 'div', {});

		ui.patch();

		var selectedTags = [state.pageObject.parent.id];

		var collectionsSetup = {
			domObj: documentsCollectionContainer,
			state: state,
			objectType: 'contracts',
			actions: {},
			fields: {
				name: {
					title: 'Name',
					view: function (ui, obj) {
						ui.makeNode('row-' + obj.id, 'div', {
							style: 'font-weight:bold',
							text: obj.name
						});
					}
				},
				preview: {
					title: '-',
					view: function(ui, obj) {

						showPreviewButton(ui, obj, state);

					}
				},
				select: {
					title: '-',
					view: function(ui, obj) {

						showSelectButton(ui, obj, state);

					}
				}
			},
			onBoxview: true,
			selectedView: 'table',
			subviews: {
				table: {
					hideSelectionBoxes: true,
					hideRowActions: true
				}						
			},
			sortCol: 'name',
			sortDir: 'asc',
			menu: false,
			submenu: false,
			ignorePageFilter: true,
			selectedTags: selectedTags,
			tags: tags,
			allTagsAreToggleable: true,
			minTagSelection: 1,
			maxTagSelection: 1,
			pageLength: 5,
			hidePagingAbove: true,
			searchAboveCollection: true,
			where: {
				active: {
					type: 'not_equal',
					value: 'No'
				},
				childObjs: {
					name: true,
					html_string: true
				}
			}
		};
	
		sb.notify({
			type: 'show-collection',
			data: collectionsSetup
		});

		createQueue(state.pageObject);

		ui.patch();

		$(mergeBtn.selector).prop('disabled', true).addClass('half-opacity');
		$(previewBtn.selector).prop('disabled', true).addClass('half-opacity');

		if (state.pageObject.related_file) {
			if (state.pageObject.related_file.loc) {
				showViewFileButton(state, actionBtnContainer);
			}
		}

	}

	return {

		// framework functions
		init: function(){

			sb.notify({
				type: 'register-tool',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'pdfTool',
						title: 'PDF Tool',
						icon: '<i class="fa fa-file-pdf-o"></i>',
						views: [
							// Main tool
							{
								id: 'pdfTool',
								layers: ['hq', 'team', 'project', 'myStuff', 'object'],
								availableToEntities: true,
								name: 'PDF Tool',
								tip:'Combine multiple PDFs into one.',
								icon: {
									type: 'file pdf',
									color: 'yellow'
								},
								mainViews: [
									{
										dom: function(ui, state, draw, mainDom) {
											build_toolCollections(ui, state, draw);
										}
									}
								],
								boxViews:[],
								settings: []
							}
							, // Single object view
							{
								id:'pdfs-obj',
								type:'object-view',
								title:'PDF Tool',
								icon:'tools',
								dom:function(ui, state, draw) {
									singleView(ui, state, draw, false);
									
								}
							}
						]
					}
				}
			});

			sb.listen({
				'pdf-tool-run': this.run,
				'queue-document-dropped': this.queueDocumentDropped
			});

		},

		run: function(data){
			data.run();
		},

		queueDocumentDropped: function(data) {
			console.log(data)
		}

	}

});