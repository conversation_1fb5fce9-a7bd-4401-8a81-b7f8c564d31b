Factory.register("scheduling", function (sb) {
  var comps = {};
  var UpdateShiftCollections = function () {};
  const SERVICE_NAME = "SchedulingService";

  // A
  function availability(dom, obj) {
    function staff_availability(dom, obj) {
      var UI_CACHE = {
        modal: {},
      };
      var calendarCellBtns = {
        timeOn: {
          icon: "calendar plus outline",
          color: "grey",
          tooltip: {
            text: "Enter time on",
            position: "bottom center",
          },
          action: function (dom, date) {
            dom.makeNode("modal", "modal", {
              onShow: function () {
                build_modal(dom.modal, date, obj, "timeOn");
              },
            });

            dom.patch();
            dom.modal.show();
          },
        },
        timeOff: {
          icon: "calendar minus",
          color: "grey",
          tooltip: {
            text: "Enter time off",
            position: "bottom center",
          },
          action: function (dom, date) {
            dom.makeNode("modal", "modal", {
              onShow: function () {
                build_modal(dom.modal, date, obj, "timeOff");
              },
            });

            dom.patch();
            dom.modal.show();
          },
        },
      };

      function build_modal(dom, date, obj, type) {
        var formArgs = {
          request_type: {
            name: "request_type",
            type: "hidden",
            label: "Request Type",
            options: [
              {
                name: "Time off",
                value: "time_off",
              },
              {
                name: "Time on",
                value: "time_on",
              },
            ],
          },
          start_time: {
            name: "start_time",
            type: "date",
            label: "Start Time",
            dateType: "datetime",
            value: moment(date).clone().add(sb.dom.utcOffset, "hours"),
          },
          end_time: {
            name: "end_time",
            type: "date",
            label: "End Time",
            dateType: "datetime",
            value: moment(date)
              .clone()
              .add(sb.dom.utcOffset + 6, "hours"),
          },
          recurring: {
            name: "recurring",
            type: "select",
            label: "Recurring?",
            options: [
              {
                name: "No",
                value: "no",
              },
              {
                name: "Yes",
                value: "yes",
              },
              {
                name: "Yes, with end date",
                value: "yes_with_end_date",
              },
            ],
            onChange: function (value) {
              if (value === "yes") {
                dom.body.body.form.recurring_week_days.update({
                  type: "checkbox",
                });

                dom.body.body.form.recurring_end_date.update({
                  type: "date",
                  dateType: "date",
                });
              } else if (value === "yes_with_end_date") {
                dom.body.body.form.recurring_end_date.update({
                  type: "date",
                  dateType: "date",
                });

                dom.body.body.form.recurring_week_days.update({
                  type: "checkbox",
                });
              } else {
                dom.body.body.form.recurring_week_days.update({
                  type: "hidden",
                });

                dom.body.body.form.recurring_end_date.update({
                  type: "hidden",
                });
              }
            },
          },
          recurring_week_days: {
            name: "recurring_week_days",
            type: "hidden",
            label: "Week Days",
            options: [
              {
                name: "monday",
                value: "monday",
                label: "Monday",
              },
              {
                name: "tuesday",
                value: "tuesday",
                label: "Tuesday",
              },
              {
                name: "wednesday",
                value: "wednesday",
                label: "Wednesday",
              },
              {
                name: "thursday",
                value: "thursday",
                label: "Thursday",
              },
              {
                name: "friday",
                value: "friday",
                label: "Friday",
              },
              {
                name: "saturday",
                value: "saturday",
                label: "Saturday",
              },
              {
                name: "sunday",
                value: "sunday",
                label: "Sunday",
              },
            ],
          },
          recurring_end_date: {
            name: "recurring_end_date",
            type: "hidden",
            dateType: "date",
            label: "Recurring End Date",
            placeholde: "Select end date for recurring availability.",
          },
          reason: {
            name: "reason",
            type: "textbox",
            label: "Reason",
            placeholder: "Reason for submitting this request (optional)",
          },
        };
        var formTitle = "";

        function process_request(form, obj, load, after) {
          var formData = form.process().fields;
          var newObj = {};

          newObj.request_type = formData.request_type.value;
          newObj.start_time = moment(formData.start_time.value).subtract(
            sb.dom.utcOffset,
            "hours"
          );
          newObj.end_time = moment(formData.end_time.value).subtract(
            sb.dom.utcOffset,
            "hours"
          );
          newObj.recurring = formData.recurring.value;
          newObj.reason = formData.reason.value;
          newObj.staff = obj.id;
          newObj.status = "approved";

          if (newObj.request_type === "time_on" && newObj.recurring === "no") {
            newObj.used_vacation_days =
              moment(newObj.end_time, "MM/DD/YYYY HH:mm:ss").diff(
                moment(newObj.start_time, "MM/DD/YYYY HH:mm:ss"),
                "days"
              ) + 1;
          } else {
            newObj.used_vacation_days = 0;
          }

          if (
            newObj.recurring === "yes" &&
            _.isEmpty(formData.recurring_week_days.value)
          ) {
            sb.dom.alerts.alert(
              "Error",
              "Select days for recurring option.",
              "error"
            );

            return;
          } else {
            newObj.recurring_week_days = formData.recurring_week_days.value;
          }

          if (
            newObj.recurring === "yes_with_end_date" &&
            formData.recurring_end_date.value === ""
          ) {
            sb.dom.alerts.alert(
              "Error",
              "Select an end date to recurring option.",
              "error"
            );

            return;
          } else {
            newObj.recurring_end_date = moment(
              formData.recurring_end_date.value
            );
          }

          load();

          sb.data.db.obj.create(
            "staff_availability_requests",
            newObj,
            function (res) {
              after(res);
            }
          );
        }

        if (type === "timeOn") {
          formTitle = "Request time to work";
          formArgs.request_type.value = "time_on";
        } else if (type === "timeOff") {
          formTitle = "Request time off";
          formArgs.request_type.value = "time_off";
        }

        dom.body.empty();

        dom.body.makeNode("head", "div", { css: "ui grid" });
        dom.body.makeNode("body", "div", { css: "ui basic segment" });
        dom.body.makeNode("btnGrp", "div", {});

        dom.body.head.makeNode("col1", "div", { css: "ten wide column" });
        dom.body.head.makeNode("col2", "div", { css: "six wide column" });

        dom.body.head.col1.makeNode("title", "div", {
          tag: "h2",
          text: '<i class="calendar icon"></i> ' + formTitle,
        });

        dom.body.head.col2.makeNode("date", "div", {
          text:
            "<h2>" +
            date.format("dddd") +
            '<div class="ui sub header">' +
            date.format("MMMM, Do YYYY") +
            "</div></h2>",
          css: "ui header right aligned",
        });

        dom.body.body.makeNode("form", "form", formArgs);

        dom.body.btnGrp
          .makeNode("submit", "div", {
            css: "ui green button right floated",
            text: "Submit",
          })
          .notify(
            "click",
            {
              type: "scheduling-run",
              data: {
                run: function (data) {
                  process_request(
                    dom.body.body.form,
                    obj,
                    function () {
                      dom.body.empty();

                      Loader(dom.body, "Creating request ...");

                      dom.body.patch();
                    },
                    function (request) {
                      comps.availability.notify({
                        type: "update-calendar",
                        data: {},
                      });

                      dom.hide();
                    }
                  );
                },
              },
            },
            sb.moduleId
          );

        dom.patch();
      }

      function deleteEvent(event, callback) {
        sb.data.db.obj.erase(
          "staff_availability_requests",
          event.id,
          function (response) {
            callback(response);
          }
        );
      }

      function eventModal(dom, event) {
        var eventTime = "";

        if (event.hasOwnProperty("endTime")) {
          if (moment(event.startTime).isSame(event.endTime)) {
            eventTime =
              event.startTime.format("h:mm a") +
              " - " +
              event.endTime.format("h:mm a");
          } else {
            eventTime =
              event.startTime.format("ddd, MMM Do h:mm a") +
              " - " +
              event.endTime.format("ddd, MMM Do h:mm a");
          }
        } else {
          eventTime = "All day";
        }

        dom.empty();

        dom.makeNode("wrapper", "div", {});

        dom.wrapper.makeNode("head", "div", { css: "ui grid" });
        dom.wrapper.makeNode("lb_1", "lineBreak", { space: 1 });
        dom.wrapper.makeNode("body", "div", {});

        dom.wrapper.head.makeNode("col1", "div", {
          css: "ui thirteen wide column",
        });
        dom.wrapper.head.makeNode("col2", "div", {
          css: "ui three wide column",
        });

        dom.wrapper.head.col1.makeNode("title", "div", {
          css: "ui header",
          text:
            "<h3>" +
            event.name +
            ' <div class="ui sub header">' +
            eventTime +
            "</div></h3>",
        });

        dom.wrapper.head.col2.makeNode("btnGrp", "div", {});

        dom.wrapper.head.col2.btnGrp
          .makeNode("delete_btn", "div", {
            css: "mini ui red button",
            text: '<i class="fa fa-trash"></i>',
          })
          .notify("click", {
            type: "scheduling-run",
            data: {
              run: function (data) {
                deleteEvent(event, function (res) {
                  //update calendar
                  comps.availability.notify({
                    type: "update-calendar",
                    data: {},
                  });

                  dom.hide();
                });
              },
            },
          });

        dom.wrapper.body.makeNode("label", "div", {
          css: "ui " + event.color + " label",
          text: event.type,
        });

        dom.wrapper.body.makeNode("lb_2", "lineBreak", { spaces: 1 });

        dom.wrapper.body.makeNode("description", "div", {
          text: event.description,
        });

        dom.patch();
        dom.show();
      }

      dom.empty();

      dom.makeNode("wrapper", "div", { css: "ui basic segment" });

      UI_CACHE.modal = dom.wrapper.makeNode("modal", "modal", {
        size: "small",
        onShow: function () {},
      });

      dom.wrapper.makeNode("calendarWrap", "div", {});
      dom.wrapper.makeNode("body", "div", {});

      dom.patch();

      comps.availability.notify({
        type: "show-calendar",
        data: {
          domObj: dom.wrapper.calendarWrap,
          events: function (callback, range) {
            var ret = [];

            sb.data.db.obj.getWhere(
              "staff_availability_requests",
              { staff: obj.id, childObjs: 1 },
              function (reqs) {
                _.each(reqs, function (req) {
                  var recurring_endDate = "";

                  if (_.isEmpty(req.recurring_end_date)) {
                    recurring_endDate = undefined;
                  } else {
                    recurring_endDate = moment(req.recurring_end_date);
                  }

                  ret.push({
                    id: req.id,
                    name: req.request_type_name,
                    startTime: moment(req.start_time),
                    endTime: moment(req.end_time),
                    description: req.reason,
                    type: "Availability",
                    color: "green",
                    recurring: req.recurring,
                    recurring_week_days: req.recurring_week_days,
                    recurring_end_date: recurring_endDate,
                    modal: eventModal,
                  });
                });

                callback(ret);
              }
            );
          },
          views: {
            month: {
              show: true,
              cellButtons: calendarCellBtns,
            },
            week: {
              body: false,
              cellButtons: calendarCellBtns,
            },
            threeDay: {
              show: false,
            },
            day: {
              show: false,
            },
            list: {
              show: false,
            },
          },
          viewType: "month",
          scheduling: true,
        },
      });
    }

    staff_availability(dom, obj);
  }

  // B
  function batch_editShifts(ui, shiftIds, callback) {
    Loader(ui, "Fetching shift data...");
    ui.patch();

    sb.data.db.obj.getWhere(
      "groups",
      {
        group_type: "Shift",
        id: {
          type: "or",
          values: shiftIds,
        },
        childObjs: {
          start_date: true,
          end_date: true,
          user: true,
          name: true,
          description: true,
        },
      },
      function (res) {
        var shifts = res;
        var unscheduledShifts = [];
        var scheduledShifts = [];

        _.each(shifts, function (s) {
          if (s.user !== null) {
            scheduledShifts.push(s);
          } else {
            unscheduledShifts.push(s);
          }
        });

        ui.empty();

        ui.makeNode("wrapper", "div", {});

        ui.wrapper.makeNode("head", "div", {});
        ui.wrapper.makeNode("lb_1", "lineBreak", { spaces: 1 });
        ui.wrapper.makeNode("body", "div", {});

        ui.wrapper.head.makeNode("grid", "div", {
          css: "ui two column grid",
        });

        ui.wrapper.head.grid
          .makeNode("col1", "div", {
            css: "column",
          })
          .makeNode("title", "div", {
            tag: "h1",
            text: "Edit shift(s)",
          });

        ui.wrapper.head.grid.makeNode("col2", "div", {
          css: "column",
        });

        ui.wrapper.head.grid.col2.makeNode("btnGrp", "div", {});
        ui.wrapper.head.grid.col2.btnGrp
          .makeNode("updateBtn", "div", {
            css: "ui right floated green button",
            text: "Update",
          })
          .notify(
            "click",
            {
              type: [sb.moduleId + "-run"],
              data: {
                run: function (data) {
                  if (!_.isEmpty(scheduledShifts)) {
                    sb.dom.alerts.ask(
                      {
                        title:
                          "Would you like to notify staff on scheduled shifts?",
                        text: "",
                        primaryButtonText: "Yes",
                        cancelButtonText: "No",
                      },
                      function (resp) {
                        swal.disableButtons();

                        if (resp) {
                          var selection = _.pluck(scheduledShifts, "id");

                          notify_multipleShiftStaff(selection, function (done) {
                            callback(true);

                            swal.close();
                          });
                        } else {
                          callback(true);

                          swal.close();
                        }
                      }
                    );
                  } else {
                    callback(true);
                  }
                },
              },
            },
            sb.moduleId
          );

        if (!_.isEmpty(unscheduledShifts)) {
          ui.wrapper.body.makeNode("unscheduled", "div", {
            tag: "h3",
            css: "ui horizontal divider",
            text: unscheduledShifts.length + " Unscheduled Shift(s)",
          });

          _.each(unscheduledShifts, function (s) {
            ui.wrapper.body.makeNode("shiftName" + s.id, "div", {
              text: "<strong>" + s.name + "</strong>",
            });

            // Date Area
            ui.wrapper.body.makeNode("grid" + s.id, "div", {
              css: "ui two column grid",
            });

            ui.wrapper.body["grid" + s.id].makeNode("col1", "div", {
              css: "column",
            });
            ui.wrapper.body["grid" + s.id].makeNode("col2", "div", {
              css: "column",
            });

            // Details Area
            ui.wrapper.body.makeNode("detailsLabel" + s.id, "div", {
              css: "text-muted",
              text: "Details: ",
            });
            ui.wrapper.body.makeNode("detailsWrapper" + s.id, "div", {});

            ui.wrapper.body.makeNode("lb" + s.id, "lineBreak", { spaces: 1 });

            s.start_date = moment(s.start_date)
              .clone()
              .add(sb.dom.utcOffset, "hours")
              .format("YYYY-MM-DD HH:mm:ss ZZ");
            s.end_date = moment(s.end_date)
              .clone()
              .add(sb.dom.utcOffset, "hours")
              .format("YYYY-MM-DD HH:mm:ss ZZ");

            sb.notify({
              type: "view-page",
              data: {
                ui: ui.wrapper.body["grid" + s.id].col1,
                onDraw: function () {},
                page: {
                  date: {
                    type: "date",
                    fieldName: "start_date",
                    label: "Start Date",
                    edit: sb.permissions.isGroupManager(
                      +sb.data.cookie.userId,
                      appConfig.headquarters
                    ),
                    dateType: "datetime",
                    removeUTC: true,
                  },
                },
                state: {
                  pageObject: s,
                },
              },
            });

            sb.notify({
              type: "view-page",
              data: {
                ui: ui.wrapper.body["grid" + s.id].col2,
                onDraw: function () {},
                page: {
                  date: {
                    type: "date",
                    fieldName: "end_date",
                    label: "End Date",
                    edit: sb.permissions.isGroupManager(
                      +sb.data.cookie.userId,
                      appConfig.headquarters
                    ),
                    dateType: "datetime",
                    removeUTC: true,
                  },
                },
                state: {
                  pageObject: s,
                },
              },
            });

            sb.notify({
              type: "view-field",
              data: {
                type: "detail",
                property: "description",
                obj: s,
                options: {
                  editing: sb.permissions.isGroupManager(
                    +sb.data.cookie.userId,
                    appConfig.headquarters
                  ),
                  placeholder: "Add shift details...",
                },
                ui: ui.wrapper.body["detailsWrapper" + s.id],
              },
            });
          });
        }

        if (!_.isEmpty(scheduledShifts)) {
          ui.wrapper.body.makeNode("scheduled", "div", {
            tag: "h3",
            css: "ui horizontal divider",
            text: scheduledShifts.length + " Scheduled Shift(s)",
          });

          _.each(scheduledShifts, function (s) {
            ui.wrapper.body.makeNode("shiftName" + s.id, "div", {
              text: "<strong>" + s.name + "</strong>",
            });
            ui.wrapper.body.makeNode("shiftStaff" + s.id, "div", {
              text:
                "<span>Staff: </span> <strong>" +
                s.user.fname +
                " " +
                s.user.lname +
                "</strong>",
            });

            // Date Area
            ui.wrapper.body.makeNode("grid" + s.id, "div", {
              css: "ui two column grid",
            });

            ui.wrapper.body["grid" + s.id].makeNode("col1", "div", {
              css: "column",
            });
            ui.wrapper.body["grid" + s.id].makeNode("col2", "div", {
              css: "column",
            });

            // Details Area
            ui.wrapper.body.makeNode("detailsLabel" + s.id, "div", {
              css: "text-muted",
              text: "Details: ",
            });
            ui.wrapper.body.makeNode("detailsWrapper" + s.id, "div", {});

            ui.wrapper.body.makeNode("lb" + s.id, "lineBreak", { spaces: 1 });

            s.start_date = moment(s.start_date)
              .clone()
              .add(sb.dom.utcOffset, "hours")
              .format("YYYY-MM-DD HH:mm:ss ZZ");
            s.end_date = moment(s.end_date)
              .clone()
              .add(sb.dom.utcOffset, "hours")
              .format("YYYY-MM-DD HH:mm:ss ZZ");

            sb.notify({
              type: "view-page",
              data: {
                ui: ui.wrapper.body["grid" + s.id].col1,
                onDraw: function () {},
                page: {
                  date: {
                    type: "date",
                    fieldName: "start_date",
                    label: "Start Date",
                    edit: sb.permissions.isGroupManager(
                      +sb.data.cookie.userId,
                      appConfig.headquarters
                    ),
                    dateType: "datetime",
                    removeUTC: true,
                  },
                },
                state: {
                  pageObject: s,
                },
              },
            });

            sb.notify({
              type: "view-page",
              data: {
                ui: ui.wrapper.body["grid" + s.id].col2,
                onDraw: function () {},
                page: {
                  date: {
                    type: "date",
                    fieldName: "end_date",
                    label: "End Date",
                    edit: sb.permissions.isGroupManager(
                      +sb.data.cookie.userId,
                      appConfig.headquarters
                    ),
                    dateType: "datetime",
                    removeUTC: true,
                  },
                },
                state: {
                  pageObject: s,
                },
              },
            });

            sb.notify({
              type: "view-field",
              data: {
                type: "detail",
                property: "description",
                obj: s,
                options: {
                  editing: sb.permissions.isGroupManager(
                    +sb.data.cookie.userId,
                    appConfig.headquarters
                  ),
                  placeholder: "Add shift details...",
                },
                ui: ui.wrapper.body["detailsWrapper" + s.id],
              },
            });
          });
        }

        ui.patch();
      },
      true
    );
  }

  function build_shiftCard(cardBodyUI, shiftObj, onComplete, viewLayout) {
    var minutes = moment(shiftObj.end_date).diff(
      moment(shiftObj.start_date),
      "minutes"
    );
    var statusUI = create_statusUI(shiftObj);
    var shiftPrice = price_singleShift(shiftObj);
    var shiftDuration = moment
      .duration(moment(shiftObj.end_date).diff(moment(shiftObj.start_date)))
      .as("hours");
    var shiftTime = "";
    var locationName = "";
    var isAvailableForPickup = {
      value: false,
      label: "Not available for pickup",
      color: "orange",
    };

    if (shiftObj.location) {
      locationName = shiftObj.location.name;
    }

    if (shiftObj.available_for_pick_up === "Yes") {
      isAvailableForPickup.value = true;
      isAvailableForPickup.label = "Available for pickup";
      isAvailableForPickup.color = "green";
    }

    cardBodyUI.empty();

    cardBodyUI.makeNode("shape", "div", {
      css: "ui shape shiftCardShape",
    });

    cardBodyUI.shape.makeNode("sides", "div", {
      css: "sides",
    });

    cardBodyUI.shape.sides.makeNode("side1", "div", {
      css: "active side",
    });
    cardBodyUI.shape.sides.makeNode("side2", "div", {
      css: "side",
    });

    if (viewLayout === "day") {
      shiftTime =
        '<span style="font-size: smaller; display: inline;" class="text-muted">' +
        moment(shiftObj.start_date).format("h:mma") +
        " - " +
        moment(shiftObj.end_date).format("h:mma") +
        "</span>";
    }

    cardBodyUI.shape.sides.side1.makeNode("desc", "div", {
      css: "description",
      text:
        '<h4 class="ui header">' +
        shiftObj.name +
        " " +
        shiftTime +
        '<div class="ui sub header">' +
        locationName +
        "</div></h4>",
    });

    if (viewLayout === "week" || viewLayout === undefined) {
      cardBodyUI.shape.sides.side1.makeNode("meta", "div", {
        css: "meta",
        text:
          moment(shiftObj.start_date).format("h:mma") +
          " - " +
          moment(shiftObj.end_date).format("h:mma"),
      });
    }

    if (shiftObj.user === null) {
      cardBodyUI.shape.sides.side1.makeNode("isAvailableForPickup", "div", {
        css: "ui mini " + isAvailableForPickup.color + " label",
        text: isAvailableForPickup.label,
      });
    }

    cardBodyUI.shape.sides.side2.makeNode("statusWrap", "div", {});
    cardBodyUI.shape.sides.side2.makeNode("durationWrap", "div", {
      style: "margin: 5px 0;",
    });
    cardBodyUI.shape.sides.side2.makeNode("costWrap", "div", {
      style: "margin: 5px 0;",
    });

    cardBodyUI.shape.sides.side2.statusWrap.makeNode("statusLabel", "div", {
      css: "ui " + statusUI.color + " label",
      text: statusUI.text,
    });
    cardBodyUI.shape.sides.side2.durationWrap.makeNode("duration", "div", {
      text:
        '<span><i class="clock icon"></i></span> ' + shiftDuration + " hour(s)",
      css: "meta",
    });

    if (
      !appConfig.state.hasOwnProperty("myStuff") &&
      !appConfig.state.myStuff
    ) {
      cardBodyUI.shape.sides.side2.costWrap.makeNode("cost", "div", {
        text: "$ " + (shiftPrice / 100).formatMoney(),
        css: "meta",
      });
    }

    cardBodyUI.patch();
  }

  function build_singleShiftView(ui, shift, setup, afterLoad) {
    // setup = {
    // patch: bool,
    // onModal: bool,
    // onClose: function
    // };

    var shiftStaffName = "";
    var shiftDuration = moment
      .duration(moment(shift.end_date).diff(moment(shift.start_date)))
      .as("hours");
    var shiftPrice = price_singleShift(shift);

    if (shift.user !== null) {
      shiftStaffName = shift.user.fname + " " + shift.user.lname;
    }

    ui.makeNode("wrapper", "div", {});

    ui.wrapper.makeNode("head", "div", {
      css: "ui equal width grid",
    });

    if (!sb.dom.isMobile) {
      ui.wrapper.makeNode("lb_1", "lineBreak", { spaces: 1 });
    }

    ui.wrapper.makeNode("body", "div", {});

    ui.wrapper.head.makeNode("col1", "div", {
      css: "column",
    });

    if (!sb.dom.isMobile) {
      ui.wrapper.head.makeNode("col2", "div", {
        css: "column",
      });
    }

    ui.wrapper.head.col1.makeNode("title", "div", {
      text:
        '<h1 class="ui header">' +
        shift.name +
        '<div class="ui sub header">' +
        shiftStaffName +
        "</div></h1>",
    });

    ui.wrapper.head.col1.makeNode("managersLabel", "div", {
      css: "ui sub header",
      text: "Manager(s)",
    });
    ui.wrapper.head.col1.makeNode("managers", "div", {});

    if (
      setup.hasOwnProperty("onModal") &&
      setup.onModal === true &&
      !sb.dom.isMobile
    ) {
      ui.wrapper.head.col2.makeNode("btnGrp", "div", {});

      ui.wrapper.head.col2.btnGrp
        .makeNode("close", "div", {
          text: "Close",
          css: "ui red button right floated",
        })
        .notify(
          "click",
          {
            type: [sb.moduleId + "-run"],
            data: {
              run: function (data) {
                setup.onClose();
              },
            },
          },
          sb.moduleId
        );

      ui.wrapper.head.col2.btnGrp.makeNode("fullview", "div", {
        text: '<i class="ui expand icon"></i>',
        css: "ui icon button right floated",
        tag: "a",
        href: sb.data.url.getObjectPageParams(shift, {}),
      });
    }

    ui.wrapper.body.makeNode("grid", "div", {
      css: "ui stackable equal width grid",
    });

    if (!sb.dom.isMobile) {
      ui.wrapper.body.makeNode("lb_1", "lineBreak", {
        spaces: 2,
      });
    }
    ui.wrapper.body.makeNode("project_details", "div", {});
    ui.wrapper.body.makeNode("schedule_details", "div", {});
    ui.wrapper.body.makeNode("details", "div", {});

    ui.wrapper.body.makeNode("lb_2", "lineBreak", {
      spaces: 1,
    });

    ui.wrapper.body.makeNode("comments", "div", {});

    ui.wrapper.body.grid.makeNode("col1", "div", {
      css: "column text-center",
    });
    ui.wrapper.body.grid.makeNode("col2", "div", {
      css: "column text-center",
    });
    ui.wrapper.body.grid.makeNode("col3", "div", {
      css: "column text-center",
    });
    ui.wrapper.body.grid.makeNode("col4", "div", {
      css: "column text-center",
    });

    if (
      !appConfig.state.hasOwnProperty("myStuff") &&
      !appConfig.state.myStuff
    ) {
      ui.wrapper.body.grid.makeNode("col5", "div", {
        css: "column text-center",
      });
    }

    ui.wrapper.body.grid.col1.makeNode("labelWrap", "div", {});

    ui.wrapper.body.grid.col1.labelWrap.makeNode("labelTitle", "div", {
      text: "Status",
      css: "text-muted",
    });
    ui.wrapper.body.grid.col1.labelWrap.makeNode("label", "div", {
      text: create_statusUI(shift).text,
      css: "ui " + create_statusUI(shift).color + " label",
    });

    ui.wrapper.body.grid.col2.makeNode("start_dateTitle", "div", {
      text: "Start Date",
      css: "text-muted",
    });
    ui.wrapper.body.grid.col2.makeNode("start_date", "div", {
      text:
        "<strong>" +
        moment(shift.start_date).format("ddd MMM Do, YYYY h:mm A") +
        "</strong>",
    });

    ui.wrapper.body.grid.col3.makeNode("end_dateTitle", "div", {
      text: "End Date",
      css: "text-muted",
    });
    ui.wrapper.body.grid.col3.makeNode("end_date", "div", {
      text:
        "<strong>" +
        moment(shift.end_date).format("ddd MMM Do, YYYY h:mm A") +
        "</strong>",
    });

    ui.wrapper.body.grid.col4.makeNode("duration_title", "div", {
      text: "Duration",
      css: "text-muted",
    });
    ui.wrapper.body.grid.col4.makeNode("duration", "div", {
      text: "<strong>" + shiftDuration + " hour(s)</strong>",
    });

    if (
      !appConfig.state.hasOwnProperty("myStuff") &&
      !appConfig.state.myStuff
    ) {
      ui.wrapper.body.grid.col5.makeNode("cost_title", "div", {
        text: "Cost",
        css: "text-muted",
      });

      ui.wrapper.body.grid.col5.makeNode("cost", "div", {
        text: "<strong>$ " + (shiftPrice / 100).formatMoney() + "</strong>",
      });
    }
    ui.wrapper.body.project_details.makeNode("Label", "div", {
      text: "Project Details:",
      css: "text-muted",
    });

    ui.wrapper.body.makeNode("lb_project", "lineBreak", { spaces: 3 });

    ui.wrapper.body.schedule_details.makeNode("Label", "div", {
      text: "Schedule Details:",
      css: "text-muted",
    });

    ui.wrapper.body.makeNode("lb_event", "lineBreak", { spaces: 3 });

    ui.wrapper.body.details.makeNode("Label", "div", {
      text: "Shift Details:",
      css: "text-muted",
    });
    ui.wrapper.body.details.makeNode("wrapper", "div", {});

    if (setup.hasOwnProperty("patch") && setup.patch === true) {
      ui.patch();
    }

    afterLoad(ui, shift, setup);
  }

  // C
  function checkURL() {
    var url = window.location.href.split("!");
    var params = {};

    _.each(url, function (urlpart, i) {
      if (i !== 0) {
        urlpart = urlpart.split(":");

        _.each(urlpart, function (v, k) {
          params[urlpart[0]] = urlpart[1];
        });
      }
    });

    return params;
  }

  function copy_shift(shift, beforeCopy, afterCopy) {
    beforeCopy();

    var newObj = {};

    _.each(shift, function (value, key) {
      if (key !== "id") {
        newObj[key] = value;
      }
    });

    sb.data.db.obj.create(
      "groups",
      newObj,
      function (newObj) {
        afterCopy(newObj);
      },
      1
    );
  }

  function create_schedule(ui, setup) {
    var form_args = {
      name: {
        name: "name",
        type: "text",
        label: "Schedule Name",
        placeholder: "Enter a schedule name",
      },
      start_date: {
        name: "start_date",
        type: "date",
        label: "Date",
        placeholder: "January 1st",
        /* value: moment().startOf('day') */
      },
      category: {
        name: "category",
        type: "hidden",
        label: "Category",
        options: [],
      },
      location: {
        name: "location",
        type: "select",
        label: "Location",
        options: [],
      },
      head_count: {
        name: "head_count",
        type: "hidden",
        label: "Guest Count",
        value: 0,
      },
      template: {
        name: "template",
        type: "hidden",
        label: "Template (Choose a template to create from)",
        options: [],
        value: 0,
      },
      description: {
        name: "description",
        type: "textbox",
        label: "Details",
        placeholder: "Any thoughts you would like included ...",
      },
    };
    var objTemplate = setup.objTemplate;
    var state = setup.state;
    var onCreate = setup.onCreate;
    var onClose = setup.onClose;

    function process(form, beforeCreate, afterCreate, ui) {
      var formData = form.process().fields;
      var newObj = {};

      function validate_form(formData, callback) {
        if (formData.name.value === "") {
          display_message(
            ui.wrapper.messageWrap,
            "Name field can not be empty"
          );

          callback(false);
        } else if (formData.start_date.value === "") {
          display_message(
            ui.wrapper.messageWrap,
            "Date field can not be empty"
          );

          callback(false);
        } else {
          callback(true);
        }
      }

      validate_form(formData, function (res) {
        if (res === false) {
          return;
        } else {
          beforeCreate();

          newObj.name = formData.name.value;
          newObj.start_date = formData.start_date.value;
          newObj.status = "Active";
          newObj.location = parseInt(formData.location.value);
          newObj.head_count = parseInt(formData.head_count.value);
          newObj.description = formData.description.value;
          newObj.is_active = "Yes";
          newObj.group_type = "Schedule";
          newObj.category = parseInt(formData.category.value);
          newObj._useTemplate = parseInt(formData.template.value);

          if (state.pageObject) {
            if (!_.isEmpty(state.pageObject.description)) {
              newObj.parent_description = state.pageObject.description;
            }
            newObj.managers = state.pageObject.managers;
            newObj.is_template = state.pageObject.is_template;
          }

          if (objTemplate === undefined) {
            newObj.tagged_with = state.project.tagged_with;
          } else {
            newObj.tagged_with = objTemplate.tagged_with;

            if (
              objTemplate.hasOwnProperty("is_template") &&
              objTemplate.is_template === 1
            ) {
              newObj.is_template = 1;
            }
          }

          if (state.hasOwnProperty("team")) {
            newObj.parent = state.team.id;
            newObj.tagged_with.push(state.team.id);
          }

          if (
            state.hasOwnProperty("project") &&
            !state.hasOwnProperty("proposal")
          ) {
            newObj.parent = state.object.id;
            newObj.tagged_with.push(state.object.id);
          } else if (
            state.hasOwnProperty("project") &&
            state.hasOwnProperty("proposal")
          ) {
            newObj.parent = state.proposal.id;
            newObj.tagged_with.push(state.proposal.id);
          }

          if (
            state.hasOwnProperty("project") &&
            !_.isEmpty(state.project.category)
          ) {
            newObj.category = parseInt(state.project.category.id);
          }
          sb.data.db.obj.create(
            "groups",
            newObj,
            function (obj) {
              if (afterCreate) {
                afterCreate(obj);
              }
            },
            1
          );
        }
      });
    }

    function display_message(ui, text) {
      ui.empty();

      ui.makeNode("message", "div", {
        css: "ui negative message",
      });

      ui.message.makeNode("header", "div", {
        css: "header",
        text: text,
      });

      ui.makeNode("lb", "lineBreak", { spaces: 1 });

      ui.patch();
    }

    function inherit_fromParent(state, form_args) {
      if (state.hasOwnProperty("project")) {
        if (!_.isEmpty(state.project.locations)) {
          form_args.location.options = [];

          _.each(state.project.locations, function (loc) {
            form_args.location.options.push({
              name: loc.name,
              value: loc.value,
            });
          });

          form_args.location.value = state.project.locations[0].id;
        }
      }

      return form_args;
    }

    if (state.pageObject) {
      form_args.name.value = state.pageObject.name + " Schedule";
    }

    ui.empty();

    ui.makeNode("wrapper", "div", {});

    ui.wrapper.makeNode("head", "div", {
      css: "ui grid",
    });
    ui.wrapper.makeNode("lb_1", "lineBreak", { spaces: 1 });
    ui.wrapper.makeNode("messageWrap", "div", {});
    ui.wrapper.makeNode("body", "div", {});

    ui.wrapper.head.makeNode("col1", "div", {
      css: "eight wide column",
    });
    ui.wrapper.head.makeNode("col2", "div", {
      css: "eight wide column",
    });

    ui.wrapper.head.col1.makeNode("title", "div", {
      tag: "h1",
      css: "ui header",
      text: "Create a schedule",
    });

    ui.wrapper.head.col2.makeNode("btnGrp", "div", {});

    ui.wrapper.head.col2.btnGrp.makeNode("close", "div", {
      css: "ui red button right floated loading",
    });
    ui.wrapper.head.col2.btnGrp.makeNode("save", "div", {
      css: "ui green button right floated loading",
    });

    Loader(ui.wrapper.body, "Loading staff locations...");

    ui.patch();

    sb.data.db.obj.getAll("staff_base", function (staffLocations) {
      Loader(ui.wrapper.body, "Loading schedule templates...");
      ui.wrapper.body.patch();

      sb.data.db.obj.getWhere(
        "groups",
        {
          is_template: 1,
          group_type: "Schedule",
          childObjs: {
            name: true,
          },
        },
        function (templates) {
          Loader(ui.wrapper.body, "Loading categories...");

          ui.wrapper.body.patch();

          sb.data.db.obj.getAll(
            "categories",
            function (project_types) {
              if (setup.objTemplate && setup.objTemplate.is_template === 1) {
                form_args.head_count.type = "number";
              }

              _.each(staffLocations, function (location) {
                form_args.location.options.push({
                  name: location.name,
                  value: location.id,
                });
              });

              form_args.location.options = _.sortBy(
                form_args.location.options,
                function (obj) {
                  return obj.name;
                }
              );

              if (!_.isEmpty(templates)) {
                _.each(templates, function (template) {
                  form_args.template.options.push({
                    name: template.name,
                    value: template.id,
                  });
                });

                form_args.template.options = _.sortBy(
                  form_args.template.options,
                  function (obj) {
                    return obj.name;
                  }
                );

                form_args.template.options.unshift({
                  name: "None",
                  value: 0,
                });

                form_args.template.type = "select";
              }

              if (!_.isEmpty(project_types)) {
                _.each(project_types, function (type) {
                  form_args.category.options.push({
                    name: type.name,
                    value: type.id,
                  });
                });

                form_args.category.options = _.sortBy(
                  form_args.category.options,
                  function (obj) {
                    return obj.name;
                  }
                );

                form_args.category.options.unshift({
                  name: "None",
                  value: 0,
                });

                form_args.category.type = "select";
              }

              form_args = inherit_fromParent(state, form_args);

              form_args.location.options.unshift({
                name: "None",
                value: 0,
              });

              ui.wrapper.head.col2.btnGrp.empty();

              ui.wrapper.head.col2.btnGrp
                .makeNode("close", "div", {
                  css: "ui red button right floated",
                  text: "Close",
                })
                .notify(
                  "click",
                  {
                    type: [sb.moduleId + "-run"],
                    data: {
                      run: function (data) {
                        onClose();
                      },
                    },
                  },
                  sb.moduleId
                );

              ui.wrapper.head.col2.btnGrp
                .makeNode("save", "div", {
                  css: "ui green button right floated",
                  text: "Save",
                })
                .notify(
                  "click",
                  {
                    type: [sb.moduleId + "-run"],
                    data: {
                      run: function (data) {
                        process(
                          ui.wrapper.body.form,
                          function () {
                            ui.empty();

                            Loader(ui, "Creating new schedule...");

                            ui.patch();
                          },
                          function (newObj) {
                            window.location.href = sb.data.url.createPageURL(
                              "object",
                              {
                                type: "schedule",
                                id: newObj.id,
                                name: newObj.name,
                              }
                            );
                          },
                          ui
                        );
                      },
                    },
                  },
                  sb.moduleId
                );

              ui.wrapper.head.col2.btnGrp.patch();

              ui.wrapper.body.empty();

              ui.wrapper.body.makeNode("form", "form", form_args);

              ui.wrapper.body.patch();
            },
            {
              name: true,
              states: true,
              type: true,
              template: true,
            }
          );
        }
      );
    });
  }

  function create_scheduleFromTemplate(template, proposal, callback) {
    sb.data.db.obj.getById(
      proposal.main_object.bp_object_type,
      proposal.main_object.id,
      function (resp) {
        if (resp.start_date === "") {
          sb.dom.alerts.alert(
            "No start date",
            "Please enter a start date for this project to apply a template.",
            "error"
          );
          $(".ui.modal").modal("hide");
          return;
        }

        var templateOptions = {
          start_date: resp.start_date,
          is_template: proposal.main_object.is_template,
          tagged_with: [resp.id],
          name: resp.name,
        };

        if (!_.isEmpty(resp.locations)) {
          templateOptions.location = resp.locations[0].id;
        }

        sb.data.db.obj.createFromTemplate(
          template.id,
          function (response) {
            callback(response);
          },
          1,
          templateOptions
        );

        /*
sb.data.db.obj.create(template.object_bp_type, obj, function(newSchedule){

				callback(newSchedule);

			}, 1);
*/
      },
      {
        start_date: true,
        locations: true,
        name: true,
      }
    );
  }

  function create_shift(schedule, setup, beforeCreate) {
    var ui = this;
    var formObj = {
      job_type: {
        name: "job_type",
        label: "Job Type",
        type: "select",
        options: [],
      },
      start_date: {
        name: "start_date",
        label: "Start Date",
        type: "date",
        dateType: "datetime",
        value: {},
      },
      end_date: {
        name: "end_date",
        label: "End Date",
        type: "date",
        dateType: "datetime",
        value: {},
      },
      location: {
        name: "location",
        type: "select",
        label: "Location",
        options: [],
      },
      can_be_reassigned: {
        name: "can_be_reassigned",
        type: "select",
        label: "Can be reassigned?",
        options: [
          {
            name: "No",
            value: "No",
          },
          {
            name: "Yes",
            value: "Yes",
          },
        ],
      },
      status: {
        name: "status",
        type: "hidden",
        value: "Unscheduled",
      },
      parent: {
        name: "parent",
        type: "hidden",
        value: 0, //schedule.id
      },
      amount: {
        name: "amount",
        label: "How many shifts do you need?",
        type: "number",
        value: 1,
      },
      description: {
        name: "description",
        label: "Details",
        type: "textbox",
        placeholder: "Any thoughts you would like included ...",
      },
    };
    var serviceFormObjs = [];
    var staff = undefined;
    var jobType = undefined;
    var setupDate = undefined;
    var subTitle = "";
    var utcOffset = new Date().getTimezoneOffset() / 60;
    var Team = setup.team;
    var HQ = setup.HQ;
    var user = setup.user;

    function process(form, beforeProcess, afterProcess) {
      var formData = form.process().fields;
      var shiftQuantity = parseInt(formData.amount.value);
      var shiftObj = {
        name: _.findWhere(serviceFormObjs, {
          value: parseInt(formData.job_type.value),
        }).name,
        user: null,
        status: formData.status.value,
        description: formData.description.value,
        job_type: formData.job_type.value,
        end_date: moment(formData.end_date.value).subtract(
          sb.dom.utcOffset,
          "hours"
        ),
        start_date: moment(formData.start_date.value).subtract(
          sb.dom.utcOffset,
          "hours"
        ),
        parent: formData.parent.value,
        can_be_reassigned: formData.can_be_reassigned.value,
        group_type: "Shift",
        tagged_with: [],
        available_for_pick_up: "No",
        location: parseInt(formData.location.value),
      };
      var objs = [];

      if (HQ !== undefined) {
        shiftObj.managers = HQ.managers;
      }

      if (schedule !== undefined) {
        shiftObj.tagged_with.push(schedule.id);

        shiftObj.managers = schedule.managers;

        shiftObj.location = schedule.location;

        shiftObj.is_template = schedule.is_template;

        if (schedule.location !== null) {
          shiftObj.location = schedule.location.id;
        }
      }

      if (Team !== undefined) {
        shiftObj.tagged_with.push(Team.id);

        shiftObj.managers = Team.managers;
      }

      beforeProcess();

      if (staff !== undefined && user === undefined) {
        shiftObj.user = staff.id;
      } else if (
        staff !== undefined &&
        setup.hasOwnProperty("obj") &&
        setup.obj !== undefined
      ) {
        shiftObj.user = staff.id;
      }

      beforeCreate(shiftObj, function (continu) {
        if (continu) {
          for (var i = 0; i < shiftQuantity; i++) {
            objs.push(shiftObj);
          }

          sb.data.db.obj.create(
            "groups",
            objs,
            function (newObjs) {
              afterProcess(newObjs);

              if (schedule !== undefined) {
                if (newObjs[0].user !== null && schedule.parent !== null) {
                  sb.data.db.obj.update(
                    "proposals",
                    { id: schedule.parent.id, schedule_template: 0 },
                    function (updatedProp) {}
                  );
                }
              }
            },
            {
              job_type: {
                id: true,
                name: true,
              },
              can_be_reassigned: true,
              parent: true,
              start_date: true,
              end_date: true,
              user: {
                fname: true,
                lname: true,
                phone: true,
                email: true,
              },
              details: true,
              status: true,
              available_for_pick_up: true,
            }
          );
        }
      });
    }

    if (setup.hasOwnProperty("obj") && setup.obj !== undefined) {
      if (setup.obj.object_bp_type === "users") {
        staff = setup.obj;

        subTitle =
          '<div class="sub header">For ' +
          staff.fname +
          " " +
          staff.lname +
          "</div>";
      } else if (setup.obj.object_bp_type === "inventory_service") {
        jobType = setup.obj;

        subTitle = '<div class="sub header">For ' + jobType.name + "</div>";
      }
    } else if (setup.hasOwnProperty("user") && setup.user !== undefined) {
      staff = user;

      subTitle =
        '<div class="sub header">For ' +
        staff.fname +
        " " +
        staff.lname +
        "</div>";
    }

    if (setup.hasOwnProperty("date")) {
      setupDate = moment(setup.date);

      formObj.start_date.value = setupDate
        .clone()
        .add(sb.dom.utcOffset, "hours");
      formObj.end_date.value = setupDate
        .clone()
        .add(sb.dom.utcOffset + 6, "hours");
    }

    if (schedule !== undefined) {
      formObj.parent.value = schedule.id;
    }

    ui.empty();

    ui.makeNode("wrapper", "div", {});

    ui.wrapper.makeNode("head", "div", {
      css: "ui grid",
    });
    ui.wrapper.makeNode("lb_1", "lineBreak", { spaces: 1 });
    ui.wrapper.makeNode("body", "div", {});

    ui.wrapper.head.makeNode("col1", "div", {
      css: "twelve wide column",
    });
    ui.wrapper.head.makeNode("col2", "div", {
      css: "four wide column",
    });

    ui.wrapper.head.col1.makeNode("title", "div", {
      css: "ui header",
      tag: "h1",
      text: "Create a shift " + subTitle,
    });

    ui.wrapper.head.col2.makeNode("btnGrp", "div", {});

    ui.wrapper.head.col2.btnGrp
      .makeNode("close", "div", {
        css: "ui red button right floated",
        text: "Close",
      })
      .notify(
        "click",
        {
          type: [sb.moduleId + "-run"],
          data: {
            run: function (data) {
              if (setup.hasOwnProperty("onComplete")) {
                setup.onComplete(false);
              }
            },
          },
        },
        sb.moduleId
      );

    ui.wrapper.head.col2.btnGrp.makeNode("save", "div", {
      css: "ui green button right floated loading",
      text: "Save",
    });

    Loader(ui.wrapper.body, "Fetching all job types...");

    sb.data.db.obj.getWhere(
      "inventory_service",
      {
        childObjs: {
          id: true,
          name: true,
          inventory_categories: {
            id: true,
            name: true,
          },
          can_be_billed: true,
        },
      },
      function (serviceList) {
        Loader(ui.wrapper.body, "Fetching all locations...");

        sb.data.db.obj.getAll(
          "staff_base",
          function (staffLocations) {
            _.each(staffLocations, function (location) {
              formObj.location.options.push({
                name: location.name,
                value: location.id,
              });
            });

            formObj.location.options = _.sortBy(
              formObj.location.options,
              function (obj) {
                return obj.name;
              }
            );

            ui.wrapper.head.col2.btnGrp
              .makeNode("save", "div", {
                css: "ui green button right floated",
                text: "Save",
              })
              .notify(
                "click",
                {
                  type: [sb.moduleId + "-run"],
                  data: {
                    run: function (data) {
                      process(
                        ui.wrapper.body.form,
                        function () {
                          ui.wrapper.head.col2.btnGrp.makeNode("save", "div", {
                            css: "ui green button right floated loading",
                            text: "Save",
                          });

                          ui.wrapper.head.col2.btnGrp.patch();

                          ui.wrapper.body.empty();

                          Loader(ui.wrapper.body, "Creating new shift...");

                          ui.wrapper.body.patch();
                        },
                        function (newObjs) {
                          if (setup.hasOwnProperty("onComplete")) {
                            setup.onComplete();
                          }
                        }
                      );
                    },
                  },
                },
                sb.moduleId
              );

            ui.wrapper.head.col2.btnGrp.patch();

            ui.wrapper.body.empty();

            if (!_.isEmpty(serviceList)) {
              _.each(serviceList, function (serviceObj) {
                serviceFormObjs.push({
                  name: serviceObj.name,
                  value: serviceObj.id,
                });
              });

              formObj.job_type.options = _.sortBy(serviceFormObjs, "name");

              if (jobType !== undefined) {
                formObj.job_type.options = [];

                formObj.job_type.options.push({
                  name: jobType.name,
                  value: jobType.id,
                });

                formObj.job_type.value = jobType.id;
              } else if (staff !== undefined) {
                formObj.job_type.options = [];

                _.each(staff.service, function (serviceObj) {
                  formObj.job_type.options.push({
                    name: serviceObj.name,
                    value: serviceObj.id,
                  });
                });
              }

              ui.wrapper.body.makeNode("form", "form", formObj);
            } else {
              // !TODO - create a way for users to create new job types

              delete ui.wrapper.head.col2.btnGrp.save;

              ui.wrapper.head.col2.btnGrp.patch();

              sb.data.db.obj.getWhere(
                "groups",
                {
                  group_type: "Headquarters",
                  childObjs: {
                    managers: true,
                  },
                },
                function (HQArr) {
                  var ManagerIds = _.pluck(HQArr[0].managers, "id");

                  ui.wrapper.body.makeNode("lb_1", "lineBreak", { spaces: 1 });

                  if (_.contains(ManagerIds, +sb.data.cookie.userId)) {
                    ui.wrapper.body.makeNode("note", "div", {
                      tag: "h2",
                      css: "ui header text-center",
                      text: "Looks like there are no job types available in the system. Please add jobs types in system settings to start scheduling staff.",
                    });

                    ui.wrapper.body.makeNode("msg", "div", {
                      css: "ui message",
                      style: "width: 400px; margin: 0 auto;",
                    });

                    ui.wrapper.body.msg.makeNode("bc", "div", {
                      css: "ui breadcrumb",
                    });

                    ui.wrapper.body.msg.bc.makeNode("sec1", "div", {
                      css: "section",
                      text: '<i class="cogs icon"></i> Settings',
                    });

                    ui.wrapper.body.msg.bc.makeNode("arrow1", "div", {
                      tag: "i",
                      css: "right arrow icon divider",
                    });

                    ui.wrapper.body.msg.bc.makeNode("sec2", "div", {
                      css: "section",
                      text: '<i class="teal users icon"></i> Team Members',
                    });

                    ui.wrapper.body.msg.bc.makeNode("arrow2", "div", {
                      tag: "i",
                      css: "right arrow icon divider",
                    });

                    ui.wrapper.body.msg.bc.makeNode("sec3", "div", {
                      css: "section",
                      text: "1. Job Types",
                    });
                  } else {
                    ui.wrapper.body.makeNode("note", "div", {
                      tag: "h2",
                      css: "ui header text-center",
                      text: "Looks like there are no job types available in the system. Please contact an admin.",
                    });
                  }

                  ui.wrapper.body.patch();
                }
              );
            }

            ui.wrapper.body.patch();
          },
          {
            name: true,
          }
        );
      }
    );

    ui.patch();
  }

  function create_statusUI(shiftObj) {
    if (shiftObj.status === "Unscheduled") {
      return {
        color: "orange",
        text: shiftObj.status,
      };
    } else if (shiftObj.status === "Scheduled") {
      return {
        color: "green",
        text: "Scheduled",
      };
    } else if (shiftObj.status === "Notified") {
      return {
        color: "grey",
        text: shiftObj.status,
      };
    }
  }

  // D
  function delete_schedule(scheduleId) {
    function run_delete(ids, callback) {
      sb.data.db.obj.erase("", ids, function (resp) {
        callback(resp);
      });
    }

    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "Would you like to archive schedule and its associated shifts?",
        primaryButtonText: "Archive schedule and associated shifts",
        cancelButtonText: "Archive schedule",
      },
      function (resp) {
        swal.disableButtons();

        if (resp) {
          // schedule and shifts

          sb.data.db.obj.getWhere(
            "groups",
            {
              group_type: "Shift",
              parent: scheduleId,
            },
            function (shifts) {
              var shiftIds = _.pluck(shifts, "id");

              shiftIds.push(scheduleId);

              run_delete(shiftIds, function (resp) {
                swal.close();

                sb.notify({
                  type: "app-navigate-to",
                  data: {
                    type: "UP",
                  },
                });
              });
            }
          );
        } else {
          // only schedule

          run_delete([scheduleId], function (resp) {
            swal.close();

            sb.notify({
              type: "app-navigate-to",
              data: {
                type: "UP",
              },
            });
          });
        }
      }
    );
  }

  function delete_scheduleAndShifts(setup, callback) {
    // setup => object
    // proposal => id
    // schedule => id

    var proposalId = 0;
    var scheduleId = 0;

    function get_schedule(proposalId, cb) {
      sb.data.db.obj.getWhere(
        "groups",
        {
          group_type: "Schedule",
          parent: +proposalId,
          childObjs: {
            name: true,
          },
        },
        function (schedArr) {
          cb(schedArr[0]);
        }
      );
    }

    function get_shifts(scheduleId, cb) {
      sb.data.db.obj.getWhere(
        "groups",
        {
          group_type: "Shift",
          parent: +scheduleId,
          childObjs: {
            name: true,
          },
        },
        function (shiftArr) {
          cb(shiftArr);
        }
      );
    }

    function delete_objs(ids, cb) {
      sb.data.db.obj.erase("groups", ids, function (resp) {
        cb(resp);
      });
    }

    if (setup.hasOwnProperty("proposal")) {
      proposalId = setup.proposal;
    } else if (setup.hasOwnProperty("schedule")) {
      scheduleId = setup.schedule;
    } else {
      return;
    }

    if (proposalId !== 0) {
      get_schedule(proposalId, function (scheduleObj) {
        get_shifts(scheduleObj.id, function (shiftArray) {
          var shiftIds = _.pluck(shiftArray, "id");

          delete_objs(shiftIds, function () {});
          delete_objs([scheduleObj.id], function () {});

          if (callback) {
            callback();
          }
        });
      });
    }
  }

  function delete_shift(shifts, beforeDelete, afterDelete) {
    if (beforeDelete !== undefined) {
      beforeDelete();
    }

    sb.data.db.obj.erase("groups", shifts, function () {
      if (afterDelete !== undefined) {
        afterDelete();
      }
    });
  }

  function display_collections(setup) {
    // This function will display shift collections and schedule collections in tabs

    var dom = setup.dom;
    var state = setup.state;
    var urlParams = checkURL();

    function build_tabs(ui, state, callback) {
      var tabs = {
        shifts: {
          name: "shifts",
          title: "Shifts",
          href: window.location.href + "-!viewType:shifts",
        },
        schedules: {
          name: "single_schedules",
          title: "Single Schedules",
          href: window.location.href + "-!viewType:schedules",
        },
      };

      if (state.hasOwnProperty("team")) {
        tabs.schedules.name = "team_schedules";
        tabs.schedules.title = state.team.name + " Schedules";

        tabs.shifts.name = "team_schedule";
        tabs.shifts.title = state.team.name + " Schedule Overview";
      } else {
        tabs.schedules.name = "all_schedules";
        tabs.schedules.title = "All Schedules";

        tabs.shifts.name = "schedule";
        tabs.shifts.title = "Schedule Overview";
      }

      ui.makeNode("menu", "div", {
        css: "ui secondary stackable blue menu",
      });
      ui.makeNode("seg", "div", {
        css: "ui bottom attached active tab basic segment",
      });

      _.each(tabs, function (tab, name) {
        var active = "";

        if (!_.isEmpty(urlParams) && urlParams.hasOwnProperty("viewType")) {
          if (urlParams.viewType === name) {
            active = "active";
          }
        } else {
          if (name === "shifts") {
            active = "active";
          }
        }

        ui.menu
          .makeNode(tab.name, "div", {
            css: "item schedulingTabs " + active,
            text: '<a href="' + tab.href + '">' + tab.title + "</a>",
            style: "cursor: pointer; color: #027eff;",
          })
          .notify(
            "click",
            {
              type: "crm-run",
              data: {
                run: function (data) {
                  $(".schedulingTabs").removeClass("active");
                  $(".schedulingTabs").css("color", "black");
                  $(data.selector).addClass("active");
                  $(data.selector).css("color", "#027eff");

                  ui.seg.empty();

                  callback(ui.seg, name);
                },
                selector: ui.menu[tab.name].selector,
              },
            },
            sb.moduleId
          );
      });

      if (!_.isEmpty(urlParams)) {
        if (urlParams.hasOwnProperty("viewType")) {
          if (urlParams.viewType === "shifts") {
            callback(ui.seg, "shifts");
          } else {
            callback(ui.seg, "schedules");
          }
        } else {
          callback(ui.seg, "shifts");
        }
      } else {
        callback(ui.seg, "shifts");
      }

      ui.patch();
    }

    build_tabs(dom, state, function (seg, type) {
      if (type === "schedules") {
        display_scheduleCollections(seg, {
          state: state,
        });
      } else {
        display_shiftCollections(seg, undefined, state);
      }
    });
  }

  function display_scheduleCollections(ui, setup) {
    var state = setup.state;
    var collectionsSetup = {
      domObj: ui,
      state: state,
      actions: {
        view: true,
        create: function (ui, objTemplate, onCreate) {
          create_schedule(ui, {
            objTemplate: objTemplate,
            onCreate: onCreate,
            onClose: function () {
              $(".ui.modal").modal("hide");
            },
            state: state,
          });
        },
        comments: {
          icon: "comment",
          color: "purple",
          title: "Comments",
        },
      },
      fields: {
        name: {
          title: "Name",
          type: "title",
        },
        /*
parent: {
						title: 'Parent',
						type: 'parent',
						shouldShow: true,
						use: function(schedule) {

							if (
								(schedule.parent.main_object !== undefined
								|| schedule.parent.main_object !== null)
								&& schedule.parent.main_object.hasOwnProperty('id')
							) {

								return schedule.parent.main_object;

							} else {

								return {};

							}

						}
					},
*/
        date: {
          title: "Date",
          view: function (dom, obj) {
            dom.makeNode("date", "div", {
              text: moment(obj.start_date).format("MMMM Do"),
            });
          },
        },
        category: {
          title: "Category",
          view: function (ui, obj) {
            if (obj.category !== null) {
              ui.makeNode("cat", "div", {
                text: obj.category.name,
                css: "ui label",
              });
            } else {
              ui.makeNode("cat", "div", {
                text: "N/A",
              });
            }
          },
        },
        guestCount: {
          title: "Guest Count",
          view: function (ui, obj) {
            ui.makeNode("headCount", "div", {
              text: obj.head_count.toString(),
            });
          },
        },
        details: {
          title: "Details",
          view: function (dom, obj) {
            if (obj.description !== "" && obj.description !== "<p><br></p>") {
              dom.makeNode("details", "div", { text: obj.description });
            } else {
              dom.makeNode("details", "div", { text: "N/A" });
            }
          },
        },
        /*
shifts: {
						title: 'Shifts',
						view: function(dom, obj) {

							Loader(dom, 'Fetching related shifts ...');

							sb.data.db.obj.getWhere('groups', {parent: obj.id, group_type: 'Shift', childObjs: {
								id: true
							}}, function(shifts) {

								dom.empty();

								dom.makeNode('shifts', 'div', {text: shifts.length + ' shifts'});

								dom.patch();

							});

						}
					}
*/
      },
      groupings: {
        status: "Status",
      },
      objectType: "groups",
      singleView: {
        view: function (
          dom,
          schedule,
          onComplete,
          refresh,
          options,
          viewOptions
        ) {
          sb.notify({
            type: "view-page",
            data: {
              ui: dom,
              onDraw: onComplete,
              page: return_singleScheduleViewObj(state),
              state: {
                pageObject: schedule,
              },
            },
          });
        },
      },
      fullView: {
        id: "schedule-obj",
        type: "object-view",
      },
      menu: {
        subviews: {
          table: true,
        },
      },
      where: {
        childObjs: {
          name: true,
          start_date: true,
          status: true,
          description: true,
          group_type: true,
          head_count: true,
          category: true,
          parent: {
            main_object: true,
          },
        },
        group_type: "Schedule",
      },
    };

    if (state.pageObject.group_type === "Headquarters") {
      collectionsSetup.layer = "hq";
    }

    sb.notify({
      type: "show-collection",
      data: collectionsSetup,
    });
  }

  function display_shiftCollections(ui, schedule, state) {
    var matrix_date = moment();
    var team = undefined;
    var HQ = undefined;
    var collectionsState = _.clone(state);
    var where = {
      childObjs: {
        fname: true,
        lname: true,
        service: true,
        overlap: true,
      },
    };
    var onMyStuff = false;
    var user = 0;
    var matrixActions = {
      create: function (ui, assignee, date, onComplete) {
        fetchUser(onMyStuff, function (user) {
          create_shift.call(
            ui,
            schedule,
            {
              obj: assignee,
              date: date,
              onComplete: onComplete,
              team: team,
              HQ: HQ,
              user: user,
            },
            function (preCreatedShift, cb) {
              if (assignee !== undefined) {
                if (
                  assignee.overlap === 0 ||
                  assignee.overlap === undefined ||
                  assignee.overlap === null
                ) {
                  sb.data.db.obj.runSteps(
                    {
                      _if: {
                        isAvailable: {
                          assignment: preCreatedShift,
                          date: date,
                          unsuccessfulMsg:
                            assignee.fname +
                            " is not available for this shift.",
                        },
                      },
                    },
                    assignee.id,
                    function (response) {
                      if (response._completed === true) {
                        cb(true);
                      } else {
                        cb(false);
                      }

                      onComplete();
                    },
                    true // Verbose mode, to receive full
                    // response from the api.
                  );
                } else if (assignee.overlap === 1) {
                  cb(true);
                  onComplete();
                }
              } else {
                cb(true);
                onComplete();
              }
            }
          );
        });
      },
    };

    var shiftActions = {
      open: {
        name: "Open",
        icon: "external alternate",
        title: "Open shift view",
        action: function (cardBodyUI, assignment, assignee, onComplete) {
          cardBodyUI.makeNode("modal", "modal", {});

          build_singleShiftView(
            cardBodyUI.modal.body,
            assignment.obj,
            {
              patch: false,
              onModal: true,
              onClose: function () {
                cardBodyUI.modal.hide();
              },
            },
            function (ui, shift, setup) {
              var edit = true;

              if (state.myStuff === true) {
                edit = false;
              }

              cardBodyUI.patch();

              if (setup.hasOwnProperty("onModal") && setup.onModal === true) {
                cardBodyUI.modal.show();
              }

              sb.notify({
                type: "view-field",
                data: {
                  type: "users",
                  property: "managers",
                  obj: shift,
                  options: {
                    edit: true,
                  },
                  ui: ui.wrapper.head.col1.managers,
                },
              });

              sb.notify({
                type: "view-field",
                data: {
                  type: "detail",
                  property: "parent_description",
                  obj: shift.parent,
                  options: {
                    edit: false,
                    editing: false,
                    placeholder: "Project details...",
                    commitUpdates: false,
                  },
                  ui: ui.wrapper.body.project_details,
                },
              });

              sb.notify({
                type: "view-field",
                data: {
                  type: "detail",
                  property: "description",
                  obj: shift.parent,
                  options: {
                    edit: false,
                    editing: false,
                    placeholder: "Event shift details...",
                    commitUpdates: false,
                  },
                  ui: ui.wrapper.body.schedule_details,
                },
              });

              sb.notify({
                type: "view-field",
                data: {
                  type: "detail",
                  property: "description",
                  obj: shift,
                  options: {
                    edit: edit,
                    editing: edit,
                    placeholder: "Add shift details...",
                    commitUpdates: edit,
                  },
                  ui: ui.wrapper.body.details,
                },
              });

              ui.wrapper.head.col1.managers.patch();
              ui.wrapper.body.project_details.patch();
              ui.wrapper.body.schedule_details.patch();
              ui.wrapper.body.details.patch();

              sb.notify({
                type: "show-note-list-box",
                data: {
                  collapse: false,
                  domObj: ui.wrapper.body.comments,
                  objectIds: [shift.id],
                  objectId: shift.id,
                },
              });
            }
          );
        },
        shouldShow: function (assignee, assignment, layout) {
          if (sb.dom.isMobile) {
            return false;
          }

          return true;
        },
      },
      edit: {
        name: "Edit",
        icon: "pen icon",
        title: "Edit shift",
        action: function (cardBodyUI, assignment, assignee, onComplete) {
          cardBodyUI.makeNode("modal", "modal", {});

          cardBodyUI.patch();

          batch_editShifts(
            cardBodyUI.modal.body,
            [assignment.id],
            function (done) {
              if (done) {
                onComplete();
                cardBodyUI.modal.hide();
              }
            }
          );

          cardBodyUI.modal.show();
        },
      },
      flip: {
        name: "Flip",
        icon: "sync alternate",
        title: "Flip for more info",
        shouldShow: function (assignee, assignment, layout) {
          if (layout === "day" || sb.dom.isMobile) {
            return false;
          } else {
            return true;
          }
        },
        action: function (cardBodyUI, assignment, assignee, onComplete) {
          $(cardBodyUI.shape.selector).shape("flip right");
        },
      },
      archive: {
        name: "Archive",
        icon: "trash",
        title: "Delete shift",
        action: function (cardBodyUI, assignment, assignee, onComplete) {
          sb.dom.alerts.ask(
            {
              title: "Are you sure?",
              text: "Delete this shift?",
            },
            function (resp) {
              if (resp) {
                swal.disableButtons();

                delete_shift(
                  [assignment.id],
                  function () {
                    cardBodyUI.empty();

                    cardBodyUI.makeNode("holder", "div", {
                      css: "ui placeholder",
                    });

                    cardBodyUI.holder.makeNode("square", "div", {
                      css: "image",
                    });

                    cardBodyUI.patch();
                  },
                  function () {
                    sb.dom.alerts.alert("Shift deleted", "", "success");

                    onComplete();
                  }
                );
              }
            }
          );
        },
        shouldShow: function (assignee, assignment, layout) {
          if (onMyStuff) {
            return false;
          } else {
            return true;
          }
        },
      },
      notify: {
        name: "Notify",
        icon: "bell",
        title: "Notify shift staff",
        shouldShow: function (assignee, assignment, layout) {
          if (onMyStuff) {
            return false;
          } else {
            if (assignee === undefined) {
              return false;
            } else {
              if (
                assignment.user !== null &&
                assignment.status !== "Notified"
              ) {
                return true;
              } else {
                return false;
              }
            }
          }
        },
        action: function (cardBodyUI, assignment, assignee, onComplete) {
          notify_shiftStaff(assignment.obj, function (resp) {
            if (resp) {
              onComplete();
            }
          });
        },
      },
      unassign: {
        name: "Unassign",
        title: "Unassign shift",
        icon: "user times",
        shouldShow: function (assignee, assignment, layout) {
          if (state.myStuff === true) {
            if (
              assignment.obj.can_be_reassigned === "Yes" &&
              assignee !== undefined
            ) {
              return true;
            } else {
              return false;
            }
          } else {
            if (assignee !== undefined) {
              return true;
            } else {
              return false;
            }
          }
        },
        action: function (cardBodyUI, assignment, assignee, onComplete) {
          var startTime = moment(assignment.startTime);

          if (onMyStuff) {
            if (startTime.isAfter(moment().add(12, "hours"))) {
              sb.dom.alerts.ask(
                {
                  title: "Are you sure?",
                  text: "Do you want to be unscheduled from this shift?",
                },
                function (resp) {
                  if (resp) {
                    swal.disableButtons();

                    update_shift(
                      assignment.obj,
                      {
                        user: 0,
                        status: "Unscheduled",
                      },
                      function () {},
                      function (newShift) {
                        onComplete({
                          closeModal: true, // This is a flag to close modal on mobile
                        });

                        sb.dom.alerts.alert(
                          "You were unscheduled from shift",
                          "",
                          "success"
                        );

                        notify_managersOfShiftChange(assignment.obj, false);
                        notify_staffOfAvailableShifts(assignment.obj);
                      }
                    );
                  }
                }
              );
            } else {
              sb.dom.alerts.alert(
                "You can not unassign shift",
                "Please contact a shift manager",
                "error"
              );
            }
          } else {
            notify_shiftStaff(
              assignment.obj,
              function (res) {
                onComplete();
              },
              "Unscheduled"
            );
          }
        },
      },
      pickUp: {
        name: "Pick up",
        title: "Pick up shift",
        icon: "calendar plus outline",
        shouldShow: function (assignee, assignment, layout) {
          if (assignee === undefined && sb.dom.isMobile) {
            return true;
          }

          return false;
        },
        action: function (modalFooterUI, assignment, assignee, onComplete) {
          if (onMyStuff && assignee === undefined) {
            modalFooterUI.btnGrp.pickUp.loading();

            sb.data.db.obj.getById(
              "users",
              +sb.data.cookie.userId,
              function (resp) {
                if (resp) {
                  modalFooterUI.btnGrp.pickUp.loading(false);

                  assignee = resp;

                  run_shiftRules(
                    assignment.obj,
                    assignee,
                    moment(assignment.startTime),
                    function (userObj) {
                      sb.data.db.obj.getById(
                        "groups",
                        assignment.obj.id,
                        function (shift) {
                          if (shift.user !== null) {
                            update_shift(
                              assignment.obj,
                              {
                                status: "Scheduled",
                              },
                              function () {},
                              function (updated) {
                                onComplete({
                                  closeModal: true,
                                });
                              }
                            );
                          }
                        },
                        1
                      );
                    }
                  );
                } else {
                  modalFooterUI.btnGrp.pickUp.loading(false);

                  console.log("Can not fetch user data");
                }
              },
              1
            );
          }
        },
      },
    };

    var matrixSetup = {
      actions: matrixActions,
      assignments: {
        query: function (callback, setup) {
          // setup can has a date and layout properties

          var matrix_assignmentQuerySetup = {
            group_type: "Shift",
            start_date: {
              type: "between",
              start: moment().unix(),
              end: moment().unix(),
            },
            childObjs: {
              name: true,
              start_date: true,
              end_date: true,
              user: true,
              job_type: true,
              parent: true,
              group_type: true,
              status: true,
              description: true,
              managers: true,
              can_be_reassigned: true,
              location: true,
              available_for_pick_up: true,
            },
          };

          var start = "";
          var end = "";

          if (setup.layout === "week") {
            start = moment(setup.date)
              .startOf(setup.layout)
              .add(1, "day")
              .unix();
            end = moment(setup.date).endOf(setup.layout).add(1, "day").unix();
          } else if (setup.layout === "day") {
            start = moment(setup.date).startOf(setup.layout).unix();
            end = moment(setup.date).endOf(setup.layout).unix();
          }

          matrix_assignmentQuerySetup.start_date.start = start;
          matrix_assignmentQuerySetup.start_date.end = end;

          if (schedule !== undefined) {
            matrix_assignmentQuerySetup.parent = schedule.id;
          } else {
            matrix_assignmentQuerySetup.is_template = 0;
          }

          if (team !== undefined) {
            matrix_assignmentQuerySetup.tagged_with = team.id;
          }

          sb.data.db.obj.getWhere(
            "groups",
            matrix_assignmentQuerySetup,
            function (data) {
              var assignments = [];

              if (onMyStuff === true) {
                var myServices = _.pluck(user.service, "id");

                data = _.filter(data, function (shift) {
                  return _.contains(myServices, shift.job_type.id);
                });
              }

              _.each(data, function (shift) {
                var userId = undefined;
                var serviceId = undefined;

                if (shift.user !== null) {
                  userId = shift.user.id;
                }

                if (shift.job_type !== null) {
                  serviceId = shift.job_type.id;
                }

                if (onMyStuff === true) {
                  if (shift.user === null) {
                    if (shift.available_for_pick_up === "Yes") {
                      assignments.push({
                        id: shift.id,
                        name: shift.name,
                        startTime: shift.start_date,
                        endTime: shift.end_date,
                        job_type: shift.job_type,
                        status: shift.status,
                        assignee: shift.user,
                        obj: shift,
                      });
                    }
                  } else {
                    assignments.push({
                      id: shift.id,
                      name: shift.name,
                      startTime: shift.start_date,
                      endTime: shift.end_date,
                      job_type: shift.job_type,
                      status: shift.status,
                      assignee: shift.user,
                      obj: shift,
                    });
                  }
                } else {
                  assignments.push({
                    id: shift.id,
                    name: shift.name,
                    startTime: shift.start_date,
                    endTime: shift.end_date,
                    job_type: shift.job_type,
                    status: shift.status,
                    assignee: shift.user,
                    obj: shift,
                  });
                }
              });

              callback(assignments);
              return;
            },
            true
          );
        },
        dom: function (cardUI, assignment, onComplete, layout) {
          5;
          build_shiftCard(cardUI, assignment, onComplete, layout);
        },
        singleView: function (ui, assignment) {
          build_singleShiftView(
            ui,
            assignment.obj,
            {
              patch: true,
            },
            function (ui, shift, setup) {
              var edit = true;

              if (state.myStuff === true) {
                edit = false;
              }

              sb.notify({
                type: "view-field",
                data: {
                  type: "users",
                  property: "managers",
                  obj: shift,
                  options: {
                    edit: false,
                  },
                  ui: ui.wrapper.head.col1.managers,
                },
              });

              sb.notify({
                type: "view-field",
                data: {
                  type: "detail",
                  property: "description",
                  obj: shift,
                  options: {
                    edit: true,
                    placeholder: "Add shift details...",
                    commitUpdates: true,
                  },
                  ui: ui.wrapper.body.details,
                },
              });
            }
          );
        },
        css: function (assignee, assignment, layout) {
          if (assignee === undefined) {
            return "orange";
          } else {
            if (assignment.user !== null) {
              if (assignment.status === "Notified") {
                return "grey";
              } else {
                return "green";
              }
            }
          }
        },
        actions: shiftActions,
        tooltip: {
          text: function (assignment) {
            if (assignment.hasOwnProperty("obj")) {
              if (assignment.obj.location !== null) {
                return "Location: " + assignment.obj.location.name;
              }
            }

            return "";
          },
          position: "top center",
        },
        groupBy: {
          job_type: "job_type",
        },
      },
      date: matrix_date,
      viewMenu: {
        users: {
          title: "Assign by staff",
          default: true,
          unassignedBar: true,
          shouldDisplay: function (assignee, assignment) {
            if (assignee !== undefined) {
              if (assignment.obj.user !== null) {
                if (assignment.obj.user.id === assignee.id) {
                  return true;
                }
              }
            } else {
              if (assignment.obj.user === null) {
                return true;
              }
            }
          },
          objectType: "users",
          where: {
            childObjs: {
              fname: true,
              lname: true,
              service: true,
            },
          },
        },
        jobTypes: {
          title: "Assign by job type",
          shouldDisplay: function (assignee, assignment) {
            if (assignment.obj.job_type !== null) {
              if (assignment.obj.job_type.id === assignee.id) {
                return true;
              }
            }
          },
          fields: {
            name: {
              title: "Job Type",
              type: "title",
            },
            cost: {
              view: function (assignmentList) {},
            },
          },
          where: {
            childObjs: 1,
          },
          objectType: "inventory_service",
          query: function (callback) {
            sb.data.db.obj.getAll(
              "inventory_service",
              function (data) {
                callback(data);
              },
              1
            );
          },
        },
      },
      onAssignmentDrop: function (data, callback) {
        var assignment = data.dropped.assignment;
        var assignee = data.in.assignee;
        var services = [];
        var dropCellDate = data.in.date
          .clone()
          .startOf("day")
          .add(sb.dom.utcOffset, "hours");
        var assignmentStartDate = moment(assignment.startTime).add(
          sb.dom.utcOffset,
          "hours"
        );
        var assignmentEndDate = moment(assignment.endTime).add(
          sb.dom.utcOffset,
          "hours"
        );
        var newStart = assignmentStartDate.set(
          "date",
          dropCellDate.get("date")
        );
        var newEnd = assignmentEndDate.set("date", dropCellDate.get("date"));

        function check_ifUserExists(shift, callback) {
          if (shift.user !== null && shift.user.hasOwnProperty("id")) {
            callback(true);
          } else {
            callback(false);
          }
        }

        function check_ifSameDate(cellDate, shift, callback) {
          if (
            moment(shift.start_date).dayOfYear() === cellDate.dayOfYear() &&
            moment(shift.start_date).year() === cellDate.year()
          ) {
            callback(true);
          } else {
            callback(false);
          }
        }

        _.each(assignee.service, function (service) {
          services.push(service.id);
        });

        if (_.contains(services, assignment.obj.job_type.id)) {
          check_ifUserExists(assignment.obj, function (userExists) {
            if (userExists) {
              // the object is NOT coming from the 'unassigned' row

              sb.dom.alerts.ask(
                {
                  title: "",
                  text: "Would you like to move or copy this shift?",
                  confirmButtonText: "Copy",
                  cancelButtonText: "Move",
                },
                function (resp) {
                  swal.disableButtons();

                  if (resp === false) {
                    // Move

                    update_shift(
                      assignment.obj,
                      {
                        start_date: newStart.subtract(
                          sb.dom.utcOffset,
                          "hours"
                        ),
                        end_date: newEnd.subtract(sb.dom.utcOffset, "hours"),
                      },
                      function () {},
                      function (newShift) {
                        run_shiftRules(
                          assignment,
                          assignee,
                          dropCellDate,
                          function (resp) {
                            callback();
                          }
                        );
                      }
                    );
                  } else {
                    sb.data.db.obj.createFromTemplate(
                      assignment.id,
                      function (newShift) {
                        if (newShift) {
                          update_shift(
                            newShift,
                            {
                              start_date: newStart.subtract(
                                sb.dom.utcOffset,
                                "hours"
                              ),
                              end_date: newEnd.subtract(
                                sb.dom.utcOffset,
                                "hours"
                              ),
                            },
                            function () {},
                            function (updatedShift) {
                              run_shiftRules(
                                updatedShift,
                                assignee,
                                dropCellDate,
                                function (resp) {
                                  swal.close();

                                  callback();
                                }
                              );
                            }
                          );
                        }
                      },
                      1
                    );
                  }
                }
              );
            } else {
              // the object is coming from the 'unassigned' row

              check_ifSameDate(
                dropCellDate.clone(),
                assignment.obj,
                function (isSameDate) {
                  if (isSameDate) {
                    run_shiftRules(
                      assignment,
                      assignee,
                      dropCellDate,
                      function (resp) {
                        if (assignment.obj.parent !== null) {
                          if (
                            assignment.obj.parent.object_bp_type === "groups" &&
                            assignment.obj.parent.group_type === "Schedule"
                          ) {
                            schedule == assignment.obj.parent;
                          }
                        }

                        // This block might be a little specific to scheduling for now
                        if (schedule !== undefined) {
                          if (schedule.parent !== null) {
                            if (schedule.parent.schedule_template !== null) {
                              sb.data.db.obj.update(
                                "proposals",
                                {
                                  id: schedule.parent.id,
                                  schedule_template: 0,
                                },
                                function (updatedProp) {
                                  update_shift(
                                    assignment.obj,
                                    {
                                      status: "Scheduled",
                                    },
                                    function () {},
                                    function (updated) {
                                      callback();
                                    }
                                  );
                                }
                              );
                            } else {
                              update_shift(
                                assignment.obj,
                                {
                                  status: "Scheduled",
                                },
                                function () {},
                                function (updated) {
                                  callback();
                                }
                              );
                            }
                          } else {
                            update_shift(
                              assignment.obj,
                              {
                                status: "Scheduled",
                              },
                              function () {},
                              function (updated) {
                                callback();
                              }
                            );
                          }
                        } else {
                          update_shift(
                            assignment.obj,
                            {
                              status: "Scheduled",
                            },
                            function () {},
                            function (updated) {
                              callback();
                            }
                          );
                        }
                      }
                    );
                  } else {
                    sb.dom.alerts.ask(
                      {
                        title: "",
                        text: "Looks like the shift date does not match the cell you are trying to drop it in.",
                        confirmButtonText: "Assign and change shift date",
                        cancelButtonText: "Cancel",
                      },
                      function (resp) {
                        if (resp) {
                          swal.disableButtons();

                          update_shift(
                            assignment.obj,
                            {
                              start_date: newStart.subtract(
                                sb.dom.utcOffset,
                                "hours"
                              ),
                              end_date: newEnd.subtract(
                                sb.dom.utcOffset,
                                "hours"
                              ),
                              status: "Scheduled",
                            },
                            function () {},
                            function (newShift) {
                              run_shiftRules(
                                assignment,
                                assignee,
                                dropCellDate,
                                function (resp) {
                                  callback();

                                  swal.close();
                                }
                              );
                            }
                          );
                        } else {
                          callback();
                        }
                      }
                    );
                  }
                }
              );
            }
          });
        } else {
          sb.dom.alerts.alert(
            "Error",
            "This staff member can not be scheduled for this service type. Please select a staff member from a staff list related to this service type.",
            "error"
          );

          callback();
        }

        if (onMyStuff) {
          notify_managersOfShiftChange(assignment.obj, true);
        }
      },
      bottomBar: function (ui, assignmentList, date, assigneeList) {
        var assignedShifts = {
          minutes_total: 0,
          price_total: 0,
        };
        var unassignedShifts = {
          minutes_total: 0,
          price_total: 0,
        };
        var unassignedShiftsList = [];
        var labelVisibility = "";
        var totalDuration = 0;
        var totalPrice = 0;

        assigneeList = _.pluck(assigneeList, "id");

        unassignedShiftsList = _.filter(assignmentList, function (assignment) {
          return assignment.assignee === null;
        });

        assignmentList = _.chain(assignmentList)
          .filter(function (assignment) {
            return (
              assignment.assignee !== null &&
              _.contains(assigneeList, assignment.assignee.id)
            );
          })
          .value();

        if (date === undefined) {
          labelVisibility = "";

          _.each(assignmentList, function (assignment) {
            assignedShifts.minutes_total += moment(assignment.endTime).diff(
              moment(assignment.startTime),
              "minutes"
            );

            assignedShifts.price_total += price_singleShift(assignment.obj);
          });

          _.each(unassignedShiftsList, function (assignment) {
            unassignedShifts.minutes_total += moment(assignment.endTime).diff(
              moment(assignment.startTime),
              "minutes"
            );

            unassignedShifts.price_total += price_singleShift(assignment.obj);
          });
        } else {
          labelVisibility = "visibility: hidden;";

          _.each(assignmentList, function (assignment) {
            if (
              moment(assignment.startTime)
                .add(sb.dom.utcOffset, "hours")
                .isBetween(
                  date.clone().startOf("day"),
                  date.clone().endOf("day")
                )
            ) {
              assignedShifts.minutes_total += moment(assignment.endTime).diff(
                moment(assignment.startTime),
                "minutes"
              );

              assignedShifts.price_total += price_singleShift(assignment.obj);
            }
          });

          _.each(unassignedShiftsList, function (assignment) {
            if (
              moment(assignment.startTime)
                .add(sb.dom.utcOffset, "hours")
                .isBetween(
                  date.clone().startOf("day"),
                  date.clone().endOf("day")
                )
            ) {
              unassignedShifts.minutes_total += moment(assignment.endTime).diff(
                moment(assignment.startTime),
                "minutes"
              );

              unassignedShifts.price_total += price_singleShift(assignment.obj);
            }
          });
        }

        ui.makeNode("assignedPrices", "div", {});
        ui.makeNode("divider1", "div", {
          css: "ui divider",
        });
        ui.makeNode("unassignedPrices", "div", {});
        ui.makeNode("divider2", "div", {
          css: "ui divider",
        });
        ui.makeNode("total", "div", {});

        ui.assignedPrices.makeNode("label", "div", {
          css: "text-bold",
          text: "Assigned",
          style: labelVisibility,
        });

        ui.assignedPrices.makeNode("duration", "div", {
          text:
            '<i class="small clock icon"></i> ' +
            assignedShifts.minutes_total / 60 +
            " hours",
        });

        ui.assignedPrices.makeNode("cost", "div", {
          text:
            '<i class="small dollar sign icon"></i> ' +
            (assignedShifts.price_total / 100).formatMoney(),
        });

        ui.unassignedPrices.makeNode("label", "div", {
          css: "text-bold",
          text: "Unassigned",
          style: labelVisibility,
        });

        ui.unassignedPrices.makeNode("duration", "div", {
          text:
            '<i class="small clock icon"></i> ' +
            unassignedShifts.minutes_total / 60 +
            " hours",
        });

        ui.unassignedPrices.makeNode("cost", "div", {
          text:
            '<i class="small dollar sign icon"></i> ' +
            (unassignedShifts.price_total / 100).formatMoney(),
        });

        totalDuration =
          (unassignedShifts.minutes_total + assignedShifts.minutes_total) / 60;
        totalPrice = (
          (unassignedShifts.price_total + assignedShifts.price_total) /
          100
        ).formatMoney();

        ui.total.makeNode("label", "div", {
          text: "Total",
          css: "text-bold",
          style: labelVisibility,
        });
        ui.total.makeNode("duration", "div", {
          text: '<i class="small clock icon"></i> ' + totalDuration + " hours",
          css: "text-bold",
        });

        ui.total.makeNode("cost", "div", {
          text: '<i class="small dollar sign icon"></i> ' + totalPrice,
          css: "text-bold",
        });
      },
      hideTimeRangeFilter: true,
      groupMainData: {
        by: "service",
      },
    };

    var actionsSetup = {
      notifyStaff: {
        icon: "bell outline",
        title: "Notify Staff",
        color: "red",
        requireSelection: false,
        action: function (obj, state, shouldUpdate) {},
        headerAction: function (selection, state, shouldUpdate) {
          notify_multipleShiftStaff(
            selection,
            function (done) {
              shouldUpdate(done);
            },
            schedule
          );
        },
      },
      edit: {
        icon: "edit outline",
        title: "Edit shift(s)",
        color: "yellow",
        domType: "custom",
        action: function (obj, state, shouldUpdate) {},
        requireSelection: true,
        ui: true,
        headerAction: function (selection, ui, shouldUpdate, options) {
          batch_editShifts(ui, selection, function (done) {
            if (done) {
              shouldUpdate(true);
            }
          });
        },
      },
      makeAvailable: {
        icon: "eye icon",
        title: "Toggle shift pickup availability",
        color: "green",
        domType: "custom",
        headerAction: function (selection, ui, shouldUpdate, options) {
          // !Shift action dropdown
          sb.data.db.obj.runSteps(
            {
              makeShiftsAvailableForPickup: {
                shifts: selection,
              },
            },
            selection,
            function (response) {
              shouldUpdate(true);
            }
          );
        },
        requireSelection: true,
      },
    };

    if (state.myStuff === true) {
      where.id = +sb.data.cookie.userId;
      onMyStuff = true;
      user = state.user;

      matrixActions.create = false;
      actionsSetup = {};
      matrixSetup.viewMenu = {};
      matrixSetup.unassignedBar = true;
      matrixSetup.shouldDisplay = function (assignee, assignment) {
        if (assignee !== undefined) {
          if (assignment.obj.user !== null) {
            if (assignment.obj.user.id === assignee.id) {
              return true;
            }
          }
        } else {
          if (assignment.obj.user === null) {
            return true;
          }
        }
      };

      shiftActions.unassign.title = "Remove me from shift";

      delete shiftActions.edit;
      delete matrixSetup.bottomBar;
    }

    if (schedule !== undefined) {
      matrix_date = moment(schedule.start_date);
    }

    if (state.hasOwnProperty("team")) {
      team = state.team;
    }

    if (state.hasOwnProperty("headquarters")) {
      HQ = state.headquarters;
    }

    delete collectionsState.team;

    sb.notify({
      type: "show-collection",
      data: {
        domObj: ui,
        templates: false,
        state: collectionsState,
        actions: actionsSetup,
        fields: {
          fname: {
            title: "First Name",
            type: "title",
            isSearchable: true,
          },
          lname: {
            title: "Last Name",
            type: "title",
            isSearchable: true,
          },
          service: {
            title: "Job Type",
            type: "service",
            options: {
              bold: false,
            },
          },
          other: {
            view: function (ui, assignee, options, assignments) {
              if (state.myStuff !== true) {
                if (assignee !== undefined) {
                  var color = "";
                  var totalCost = 0;
                  var shiftDuration = 0;
                  var list = _.chain(assignments)
                    .filter(function (assignment) {
                      return (
                        assignment.assignee !== null &&
                        assignment.assignee.id === assignee.id
                      );
                    })
                    .value();

                  _.each(list, function (assignment) {
                    totalCost += price_singleShift(assignment.obj);

                    shiftDuration += moment
                      .duration(
                        moment(assignment.endTime).diff(
                          moment(assignment.startTime)
                        )
                      )
                      .as("hours");
                  });

                  if (shiftDuration > 40) {
                    color = "color: red !important;";
                  } else {
                    color = "";
                  }

                  ui.makeNode("duration", "div", {
                    text:
                      '<span><i class="clock icon"></i></span> ' +
                      shiftDuration +
                      " hour(s)",
                    css: "text-muted",
                    style: color,
                  });

                  ui.makeNode("cost", "div", {
                    text: "$ " + (totalCost / 100).formatMoney(),
                    css: "text-muted",
                  });
                }
              }
            },
          },
        },
        pageLength: 15,
        objectType: "users",
        singleView: {},
        fullView: {},
        selectedView: "matrix",
        subviews: {
          matrix: matrixSetup,
        },
        menu: {
          subviews: false,
        },
        groupings: {},
        data: {
          update: function (cb) {
            UpdateShiftCollections = cb;
          },
        },
        where: where,
      },
    });
  }

  // F
  function fetchUser(fetch, callback) {
    if (fetch) {
      sb.data.db.obj.getById(
        "users",
        +sb.data.cookie.userId,
        function (user) {
          callback(user);
        },
        {
          fname: true,
          lname: true,
          service: true,
        }
      );
    } else {
      callback(undefined);
    }
  }

  // L
  function Loader(ui, text) {
    ui.makeNode("seg", "div", {
      css: "ui basic segment",
    });
    ui.seg.makeNode("dimmer", "div", { css: "ui active inverted dimmer" });
    ui.seg.dimmer.makeNode("loader_text", "div", {
      text: text,
      css: "ui active centered inline text loader",
    });
  }

  function notify_managersOfShiftChange(shiftObj, scheduling) {
    let map = new Map();
    map.set("scheduling", scheduling);
    sb.data.db.service(
      SERVICE_NAME,
      "notifyOnScheduleChange",
      wrapshiftObject(shiftObj, "MANAGER_SHIFT_CHANGE", map),
      function (response) {
        //console.log("notify_managers_backend ran : " + response);
      }
    );
  }

  function notify_staffOfAvailableShifts(shiftObj) {
    if (shiftObj.available_for_pick_up == "Yes") {
      sb.data.db.service(
        SERVICE_NAME,
        "notifyOnScheduleChange",
        wrapshiftObject(shiftObj, "STAFF_SHIFT_AVAILABLE", null),
        function (response) {
          //console.log("notifyOnScheduleChange ran : " + response);
        }
      );
    }
  }

  function wrapshiftObject(shiftObj, notificationType, argMap) {
    let jsonObject = {
      notificationType: notificationType,
      userRequested: +sb.data.cookie.userId,
      shiftObj: shiftObj,
    };

    if (!_.isNull(argMap)) {
      argMap.forEach(function (value, key) {
        //console.log(key + ' = ' + value);
        jsonObject[key] = value;
      });
    }

    //console.log(jsonObject)

    return jsonObject;
  }

  function notify_multipleShiftStaff(
    selection,
    shouldUpdate,
    schedule,
    setupArgs,
    callback
  ) {
    var dbCallSetup = {};

    // setup can contain the following props:
    // updateStatus -- bool
    // Will update shift status by default
    // messageSubject -- function
    // Will provide shift obj which can be used to build the email subject
    // messageTitle -- function
    // Will provide shift obj which can be used to build the email title
    // messageBody -- function
    // Will provide shift obj which can be used to build the email body
    // noShiftsFound -- function
    // This optional function will run if no shifts were found

    var setup = {
      noShiftsFound: function () {},
      messageSubject: function (s) {
        var notificationSubject = "Your shift has been scheduled";

        if (s.status === "Notified") {
          notificationSubject = "Your shift has been updated";
        }

        return notificationSubject;
      },
      messageTitle: function (s) {
        return "Hello, " + s.user.fname + " " + s.user.lname + "!";
      },
      messageBody: function (s) {
        var details = s.description;
        var shiftLink = sb.data.url.createPageURL("object-view", {
          id: s.id,
          name: s.name,
          type: "shift",
        });
        var schedule = s.parent;
        var scheduleName = "";
        var notificationIntro =
          "<div>You have been <strong>scheduled</strong> for the following shift:</div>";

        if (schedule) {
          scheduleName =
            "<div><strong>Schedule name: </strong><small>" +
            schedule.name +
            "</small></div>" +
            '<div style="margin: 10px 0;"></div>';
        }

        if (details === "") {
          details = "<i>empty</i>";
        }

        if (s.status === "Notified") {
          notificationIntro =
            "<div>The following shift has been updated:</div>";
        }

        return (
          "<h2>Hello, " +
          s.user.fname +
          " " +
          s.user.lname +
          "!</h2>" +
          notificationIntro +
          "<div><h3>" +
          s.job_type.name +
          "</h3></div>" +
          scheduleName +
          "<div><strong>Start Time</strong>: " +
          moment(s.start_date).format("dddd, MMMM Do YYYY, h:mm a") +
          "</div>" +
          "<div><strong>End Time</strong>: " +
          moment(s.end_date).format("dddd, MMMM Do YYYY, h:mm a") +
          "</div>" +
          '<div style="margin: 10px 0;"></div>' +
          "<div><strong>Details</strong>: <i>" +
          details +
          "</i></div>" +
          '<div style="margin: 10px 0;"></div>' +
          '<div><a href="' +
          shiftLink +
          '">View your shift here</a></div>'
        );
      },
    };

    if (setupArgs !== undefined) {
      setup = setupArgs;
    }

    if (_.isEmpty(selection)) {
      dbCallSetup.group_type = "Shift";
      dbCallSetup.parent = schedule.id;
      dbCallSetup.status = {
        type: "or",
        values: ["Scheduled", "Notified"],
      };
    } else {
      dbCallSetup.group_type = "Shift";
      dbCallSetup.id = {
        type: "or",
        values: selection,
      };
    }

    dbCallSetup.childObjs = 1;

    sb.data.db.obj.getWhere("groups", dbCallSetup, function (data) {
      if (_.isEmpty(data)) {
        sb.dom.alerts.alert("Error", "No scheduled shifts found", "error");

        if (setup.hasOwnProperty("noShiftsFound")) {
          setup.noShiftsFound();
        }
      } else {
        /*
sb.dom.alerts.ask({
					title: 'Are you sure?',
					text: 'Notify scheduled staff?'
				}, function(resp) {

					if(resp) {

						swal.disableButtons();
*/

        var shiftBatch = [];
        var emailBatch = [];

        _.each(data, function (v, k) {
          var args = {};

          args = {
            to: v.user.email,
            from: "<EMAIL>",
            subject: setup.messageSubject(v),
            mergevars: {
              TITLE: setup.messageTitle(v),
              BODY: setup.messageBody(v),
            },
          };

          emailBatch.push(args);
          shiftBatch.push(v);
        });

        sb.comm.sendEmail(emailBatch, function (ret) {
          if (ret) {
            _.each(
              shiftBatch,
              function (v, k) {
                v.status = "Notified";
              },
              this
            );

            sb.data.db.obj.update(
              "groups",
              shiftBatch,
              function (updatedShifts) {
                if (updatedShifts) {
                  if (typeof shouldUpdate === "function") {
                    shouldUpdate(true);
                  }

                  sb.dom.alerts.alert(
                    "Staff Member(s) Notified",
                    "",
                    "success"
                  );

                  if (callback !== undefined) {
                    callback();
                  }
                }
              },
              1
            );
          }
        });

        /*
}

				});
*/
      }
    });
  }

  function notify_shiftStaff(shiftObj, callback, updateStatus) {
    console.log("new notify shift staff");
    console.log(arguments);
    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "Notify staff member scheduled for this shift(s)?",
      },
      function (resp) {
        if (resp) {
          swal.disableButtons();

          let map = new Map();
          map.set("updateStatus", updateStatus);
          var shiftLink = sb.data.url.createPageURL("object-view", {
            id: shiftObj.id,
            name: shiftObj.name,
            type: "shift",
          });
          map.set("shiftLink", shiftLink);
          sb.data.db.service(
            SERVICE_NAME,
            "notifyOnScheduleChange",
            wrapshiftObject(shiftObj, "STAFF_SHIFT_CHANGE", map),
            function (response) {
              console.log("shift response");
              console.log(response);

              if (response) {
                sb.dom.alerts.alert("Staff Member Notified", "", "success");
                callback(true);
              } else {
                sb.dom.alerts.alert(
                  "Could Not Notify Staff",
                  "Please try again later, or do not notify staff",
                  "error"
                );
              }
            }
          );
        } else {
          if (updateStatus !== undefined) {
            shiftObj.status = updateStatus;

            if (updateStatus === "Unscheduled") {
              shiftObj.user = 0;
            }

            sb.data.db.obj.update(
              "groups",
              shiftObj,
              function (updatedShift) {
                if (updatedShift) {
                  callback(true);
                }
              },
              1
            );
          }
        }
      }
    );
  }

  // P
  function price_singleShift(shift) {
    if (
      shift.job_type.price_type === "hourly" ||
      shift.job_type.price_type === 2
    ) {
      var shiftDuration = moment(shift.end_date).diff(
          moment(shift.start_date),
          "minute"
        ),
        serviceRate = shift.job_type.rate;

      shiftDuration = shiftDuration / 60;

      return serviceRate * shiftDuration;
    } else if (
      shift.job_type.price_type === "flat" ||
      shift.job_type.price_type === "0"
    ) {
      var servicePrice = shift.job_type.price;

      return servicePrice;
    } else if (
      shift.job_type.price_type === "flat_and_hourly" ||
      shift.job_type.price_type === 1
    ) {
      var shiftDuration = moment(shift.end_date).diff(
          moment(shift.start_date),
          "minute"
        ),
        maxFlat_hours = shift.job_type.max_flat_hours;

      if (shiftDuration / 60 <= maxFlat_hours) {
        return shift.job_type.price;
      } else {
        var extraHours = shiftDuration / 60 - maxFlat_hours,
          hourlyRate = shift.job_type.rate;

        return shift.job_type.price + extraHours * hourlyRate;
      }
    } else if (
      shift.job_type.price_type === "non_billable" ||
      shift.job_type.price_type === 3
    ) {
      return 0;
    } else {
      return 0;
    }
  }

  // R
  function return_singleScheduleViewObj(state) {
    return {
      wrapper: {
        type: "div",
        content: {
          head: {
            type: "div",
            content: {
              css: "ui grid",
              left: {
                type: "column",
                w: 8,
                content: {
                  name: {
                    type: "title",
                    fieldName: "name",
                    tag: "h1",
                    edit: true,
                    parent: "parent",
                    fields: {
                      parent: {
                        use: function (schedule) {
                          return schedule.parent.main_object;
                        },
                      },
                    },

                    /*
{
											use: ['parent', 'main_object']
										}
*/
                  },
                  tags: {
                    type: "view",
                    view: function (ui, schedule, options) {
                      ui.makeNode("c", "div", {
                        css: "field",
                      });

                      ui.patch();

                      var tagsComp = sb.createComponent("tags");

                      tagsComp.notify({
                        type: "object-tag-view",
                        data: {
                          domObj: ui.c,
                          objectType: schedule.object_bp_type,
                          objectId: schedule.id,
                        },
                      });
                    },
                  },
                  lb_1: {
                    type: "lineBreak",
                    spaces: 1,
                  },
                  projectDetailsLabel: {
                    type: "div",
                    tag: "h5",
                    text: "PROJECT DETAILS:",
                    css: "ui mini grey sub header",
                    style:
                      "font-weight:1 !important; color:rgb(175, 175, 175) !important; margin: 0px 10px;",
                  },
                  lb_2: {
                    type: "lineBreak",
                    spaces: 1,
                  },
                  projectDetailsCont: {
                    type: "div",
                    style: "border:1px dotted lightgrey; margin: 0px 10px;",
                    content: {
                      details: {
                        type: "detail",
                        fieldName: "parent_description",
                        editing: false,
                        commitUpdates: false,
                        edit: false,
                        placeholder: "Project details...",
                      },
                    },
                  },
                  lb_3: {
                    type: "lineBreak",
                    spaces: 1,
                  },
                  detailsLabel: {
                    type: "div",
                    tag: "h5",
                    text: "SCHEDULE DETAILS:",
                    css: "ui mini grey sub header",
                    style:
                      "font-weight:1 !important; color:rgb(175, 175, 175) !important; margin: 0px 10px;",
                  },
                  lb_4: {
                    type: "lineBreak",
                    spaces: 1,
                  },
                  detailsCont: {
                    type: "div",
                    style: "border:1px dotted lightgrey; margin: 0px 10px;",
                    content: {
                      details: {
                        type: "detail",
                        fieldName: "description",
                        editing: true,
                        commitUpdates: true,
                        placeholder: "Add schedule details...",
                      },
                    },
                  },
                },
              },
              right: {
                type: "column",
                w: 8,
                content: {
                  menu: {
                    type: "view",
                    view: function (ui, schedule, options) {
                      ui.makeNode("menu", "div", {
                        css: "ui secondary pointing menu",
                        style: "margin-bottom:12px;",
                      });

                      ui.menu.makeNode("right", "div", {
                        css: "right menu",
                      });

                      ui.menu.right.makeNode("options", "div", {
                        css: "ui simple dropdown item",
                        text: 'Options <i class="ui dropdown icon"></i>',
                      });

                      ui.menu.right.options.makeNode("menu", "div", {
                        css: "menu",
                      });

                      ui.menu.right.options.menu
                        .makeNode("delete", "div", {
                          tag: "a",
                          css: "item",
                          text: '<i class="red archive icon"></i> Archive',
                        })
                        .notify(
                          "click",
                          {
                            type: [sb.moduleId + "-run"],
                            data: {
                              run: function (data) {
                                delete_schedule(schedule.id);
                              },
                            },
                          },
                          sb.moduleId
                        );

                      ui.menu.right.options.menu
                        .makeNode("template", "div", {
                          tag: "a",
                          css: "item",
                          text: '<i class="window restore icon"></i> Move to Templates',
                        })
                        .notify(
                          "click",
                          {
                            type: [sb.moduleId + "-run"],
                            data: {
                              run: function (data) {
                                //schedule.is_template = 1;
                              },
                            },
                          },
                          sb.moduleId
                        );

                      ui.patch();
                    },
                  },
                  lb_1: {
                    type: "lineBreak",
                    spaces: 2,
                  },
                  date: {
                    type: "date",
                    fieldName: "start_date",
                    label: "Date",
                    edit: true,
                    dateType: "datetime",
                    onEditEnd: function () {
                      //console.log(true);
                    },
                  },
                  location: {
                    type: "locations",
                    fieldName: "location",
                    label: "Location",
                    edit: true,
                    onUpdate: function (obj) {
                      var locationId = 0;

                      if (obj.location) {
                        locationId = obj.location.id;
                      }

                      sb.data.db.obj.getWhere(
                        "groups",
                        {
                          childObjs: {
                            name: true,
                            location: true,
                          },
                          group_type: "Shift",
                          parent: obj.id,
                        },
                        function (shifts) {
                          _.each(shifts, function (s) {
                            s.location = locationId;
                          });

                          sb.data.db.obj.update(
                            "groups",
                            shifts,
                            function (resp) {
                              UpdateShiftCollections(resp);
                            },
                            1
                          );
                        }
                      );
                    },
                  },
                  type: {
                    type: "type",
                    fieldName: "category",
                    label: "Category",
                    edit: true,
                    useCategory: true,
                  },
                  managers: {
                    type: "users",
                    fieldName: "managers",
                    label: "Managers",
                    edit: true,
                    multi: true,
                  },
                  head_count: {
                    type: "quantity",
                    fieldName: "head_count",
                    label: "Guest Count",
                    edit: true,
                    show: function (obj) {
                      if (obj.is_template === 1 || obj.parent !== null) {
                        return true;
                      }

                      return false;
                    },
                  },
                },
              },
            },
          },
          lb_1: {
            type: "lineBreak",
            spaces: 1,
          },
          body: {
            type: "view",
            view: function (ui, schedule, options) {
              display_shiftCollections(ui, schedule, options);
            },
          },
          lb_2: {
            type: "lineBreak",
            spaces: 1,
          },
          comments: {
            type: "view",
            view: function (ui, schedule, options) {
              sb.notify({
                type: "show-note-list-box",
                data: {
                  collapse: false,
                  domObj: ui,
                  objectIds: [schedule.id],
                  objectId: schedule.id,
                },
              });
            },
          },
        },
      },
    };
  }

  function run_shiftRules(shiftObj, userObj, cellDate, callback) {
    if (
      userObj.overlap === 0 ||
      userObj.overlap === undefined ||
      userObj.overlap === null
    ) {
      sb.data.db.obj.runSteps(
        {
          _if: {
            isAvailable: {
              assignment: shiftObj.id,
              date: cellDate,
              unsuccessfulMsg:
                userObj.fname + " is not available for this shift.",
            },
            _do: {
              assign: {
                assignment: shiftObj.id,
                property: "user",
                successMsg:
                  userObj.fname +
                  " was assigned to " +
                  shiftObj.name +
                  " shift",
              },
            },
          },
        },
        userObj.id,
        function (response) {
          callback(response);
        }
      );
    } else if (userObj.overlap === 1) {
      sb.data.db.obj.runSteps(
        {
          assign: {
            assignment: shiftObj.id,
            property: "user",
            successMsg:
              userObj.fname + " was assigned to " + shiftObj.name + " shift",
          },
        },
        userObj.id,
        function (response) {
          callback(response);
        }
      );
    }
  }

  // S
  function startInvoiceTool(data) {
    var dom = data.dom;

    dom.empty();

    dom.makeNode("modal", "modal", {
      type: "basic",
    });

    dom.makeNode("tr", "div", { tag: "tr" });
    dom.tr.makeNode("td1", "div", { tag: "td" });
    dom.tr.makeNode("td2", "div", { tag: "td" });

    dom.makeNode("tr2", "div", { tag: "tr" });
    dom.tr2.makeNode("td1", "div", { tag: "td" });
    dom.tr2.makeNode("td2", "div", { tag: "td" });

    if (
      data.state.object.schedule_template !== 0 &&
      data.state.object.schedule_template !== null &&
      data.state.object.schedule_template !== undefined
    ) {
      dom.tr.td1.makeNode("template", "div", {
        text: '<i class="notched circle loading icon"></i>',
      });

      dom.tr2.td2
        .makeNode("remove", "div", {
          css: "ui mini compact teal right floated button",
          text: "Remove Template",
        })
        .notify(
          "click",
          {
            type: "scheduling-run",
            data: {
              run: function () {
                function remove_schedule_shift() {
                  sb.data.db.obj.update(
                    "proposals",
                    {
                      id: data.state.object.id,
                      schedule_template: 0,
                    },
                    function (updated) {
                      data.state.object = updated;

                      delete_scheduleAndShifts(
                        {
                          proposal: updated.id,
                        },
                        function () {
                          data.state.object = updated;

                          startInvoiceTool(data);

                          $(".ui .modal").modal("hide");
                        }
                      );
                    },
                    1
                  );
                }

                dom.modal.body.empty();

                dom.modal.body.makeNode("title", "div", {
                  tag: "h1",
                  text: 'Are you sure? </br></br><div class="ui sub header" style="color: white !important;">Current schedule and shifts will be deleted.</div>',
                  css: "ui header text-center",
                  style: "color: white !important;",
                });

                dom.modal.body.makeNode("actions", "div", {
                  css: "actions",
                });

                dom.modal.body.actions
                  .makeNode("yesBtn", "div", {
                    css: "ui green inverted button right floated",
                    text: '<i class="checkmark icon"></i> Yes',
                  })
                  .notify(
                    "click",
                    {
                      type: [sb.moduleId + "-run"],
                      data: {
                        run: function () {
                          dom.modal.body.empty();

                          dom.modal.body.makeNode("title", "div", {
                            tag: "h1",
                            text: 'Do you want to notify scheduled staff members? </br></br><div class="ui sub header" style="color: white !important;">An email will be sent out to scheduled staff members informing them that this event is canceled.</div>',
                            css: "ui header text-center",
                            style: "color: white !important;",
                          });

                          dom.modal.body.makeNode("lb_1", "lineBreak", {
                            spaces: 1,
                          });

                          dom.modal.body.makeNode("actions", "div", {
                            css: "actions",
                          });

                          dom.modal.body.actions
                            .makeNode("yesBtn", "div", {
                              css: "ui green inverted button right floated",
                              text: '<i class="checkmark icon"></i> Yes',
                            })
                            .notify(
                              "click",
                              {
                                type: [sb.moduleId + "-run"],
                                data: {
                                  run: function () {
                                    dom.modal.body.actions.yesBtn.loading();

                                    sb.data.db.obj.getWhere(
                                      "groups",
                                      {
                                        group_type: "Schedule",
                                        parent: data.state.object.id,
                                        childObjs: {
                                          name: true,
                                        },
                                      },
                                      function (arr) {
                                        notify_multipleShiftStaff(
                                          [],
                                          false,
                                          arr[0],
                                          {
                                            messageSubject: function (s) {
                                              return "Your shift has been cancelled";
                                            },
                                            messageTitle: function (s) {
                                              return (
                                                "Hello, " +
                                                s.user.fname +
                                                " " +
                                                s.user.lname +
                                                "!"
                                              );
                                            },
                                            messageBody: function (s) {
                                              var details = "";

                                              if (s.details === "") {
                                                details = "<i>empty</i>";
                                              } else {
                                                details = s.details;
                                              }

                                              return (
                                                "<h2>Hello, " +
                                                s.user.fname +
                                                " " +
                                                s.user.lname +
                                                "!</h2>" +
                                                "<div>The following shift has been cancelled:</div>" +
                                                "<div><h3>" +
                                                s.job_type.name +
                                                "</h3></div>" +
                                                "<div><strong>Start Time</strong>: " +
                                                moment(s.start_date).format(
                                                  "dddd, MMMM Do YYYY, h:mm a"
                                                ) +
                                                "</div>" +
                                                "<div><strong>End Time</strong>: " +
                                                moment(s.end_date).format(
                                                  "dddd, MMMM Do YYYY, h:mm a"
                                                ) +
                                                "</div>" +
                                                '<div style="margin: 10px 0;"></div>' +
                                                "<div><strong>Description</strong>: <i>" +
                                                details +
                                                "</i></div>"
                                              );
                                            },
                                            noShiftsFound: function () {
                                              remove_schedule_shift();
                                            },
                                          },
                                          function () {
                                            remove_schedule_shift();
                                          }
                                        );
                                      }
                                    );
                                  },
                                },
                              },
                              sb.moduleId
                            );

                          dom.modal.body.actions
                            .makeNode("noBtn", "div", {
                              css: "ui red inverted button right floated",
                              text: '<i class="remove icon"></i> No',
                            })
                            .notify(
                              "click",
                              {
                                type: [sb.moduleId + "-run"],
                                data: {
                                  run: function () {
                                    dom.modal.body.actions.noBtn.loading();

                                    remove_schedule_shift();
                                  },
                                },
                              },
                              sb.moduleId
                            );

                          dom.modal.body.patch();
                        },
                      },
                    },
                    sb.moduleId
                  );

                dom.modal.body.actions.makeNode("noBtn", "div", {
                  css: "ui red cancel inverted button right floated",
                  text: '<i class="remove icon"></i> No',
                });

                dom.modal.body.patch();
                dom.modal.show();
              },
            },
          },
          sb.moduleId
        );

      sb.data.db.obj.getById(
        "categories",
        data.state.object.schedule_template,
        function (category) {
          // select proper template
          sb.data.db.obj.getWhere(
            "groups",
            {
              is_template: 1,
              group_type: "Schedule",
              category: category.id,
              head_count: {
                type: "greater_than",
                value: data.state.project.head_count - 1,
              },
            },
            function (templates) {
              if (_.isEmpty(templates)) {
                sb.dom.alerts.alert(
                  "No templates",
                  "It looks like templates have not been created by the admins yet.",
                  "error"
                );

                _.each(types, function (button) {
                  if (type.id !== button.id) {
                    modal.tempCont.table.tbody[
                      "tr" + button.id
                    ].btns.select.loading(false);
                  } else {
                    modal.tempCont.table.tbody[
                      "tr" + type.id
                    ].btns.select.loading(false);
                  }
                });
              } else {
                // sort by head count
                var sortedTemplates = _.sortBy(templates, "head_count");

                if (+sortedTemplates[0].category === category.id) {
                  sb.data.db.obj.getWhere(
                    "groups",
                    {
                      group_type: "Schedule",
                      parent: data.state.object.id,
                      childObjs: {
                        id: true,
                      },
                    },
                    function (currentSchedule) {
                      if (currentSchedule.length > 0) {
                        sb.notify({
                          type: "get-schedule-price",
                          data: {
                            schedule: currentSchedule[0].id,
                            price: function (priceObj) {
                              var pricing = 0;

                              pricing = _.reduce(
                                pricing,
                                function (memo, val, id) {
                                  return memo + val;
                                },
                                0
                              );

                              dom.tr.td1.empty();
                              dom.tr.td2.empty();

                              dom.tr.td1.makeNode("template", "div", {
                                text: category.name,
                              });
                              dom.tr.td2.makeNode("template", "div", {
                                text: "$" + pricing.formatMoney(),
                                style: "text-align:right;",
                              });

                              dom.tr.td1.patch();
                              dom.tr.td2.patch();

                              return; // <---- Make sure this stays
                            },
                          },
                        });
                      } else {
                        create_scheduleFromTemplate(
                          sortedTemplates[0],
                          data.state.object,
                          function (newSchedule) {
                            // Attaching new schedule to proposal object
                            sb.data.db.obj.update(
                              "groups",
                              {
                                id: newSchedule.id,
                                parent: data.state.object.id,
                              },
                              function (updatedSchdule) {
                                sb.notify({
                                  type: "get-schedule-price",
                                  data: {
                                    schedule: currentSchedule[0].id,
                                    price: function (priceObj) {
                                      var pricing = 0;

                                      pricing = _.reduce(
                                        pricing,
                                        function (memo, val, id) {
                                          return memo + val;
                                        },
                                        0
                                      );

                                      dom.tr.td1.empty();
                                      dom.tr.td2.empty();

                                      dom.tr.td1.makeNode("template", "div", {
                                        text: category.name,
                                      });
                                      dom.tr.td2.makeNode("template", "div", {
                                        text: "$" + pricing.formatMoney(),
                                        style: "text-align:right;",
                                      });

                                      dom.tr.td1.patch();
                                      dom.tr.td2.patch();

                                      return; // <---- Make sure this stays
                                    },
                                  },
                                });
                              }
                            );
                          }
                        );
                      }
                    }
                  );
                } else {
                  create_scheduleFromTemplate(
                    sortedTemplates[0],
                    data.state.object,
                    function (newSchedule) {
                      sb.data.db.obj.update(
                        "proposals",
                        {
                          id: data.state.object.id,
                          schedule_template: +sortedTemplates[0].category,
                        },
                        function (updatedProposal) {
                          // Attaching new schedule to proposal object
                          sb.data.db.obj.update(
                            "groups",
                            {
                              id: newSchedule.id,
                              parent: data.state.object.id,
                            },
                            function (updatedSchdule) {
                              sb.notify({
                                type: "get-schedule-price",
                                data: {
                                  schedule: updatedSchdule.id,
                                  price: function (priceObj) {
                                    var pricing = 0;
                                    pricing = _.reduce(
                                      pricing,
                                      function (memo, val, id) {
                                        return memo + val;
                                      },
                                      0
                                    );

                                    dom.tr.td1.empty();
                                    dom.tr.td2.empty();

                                    dom.tr.td1.makeNode("template", "div", {
                                      text: category.name,
                                    });
                                    dom.tr.td2.makeNode("template", "div", {
                                      text: "$" + pricing.formatMoney(),
                                      style: "text-align:right;",
                                    });

                                    dom.tr.td1.patch();
                                    dom.tr.td2.patch();

                                    data.state.object.schedule_template =
                                      +sortedTemplates[0].category;

                                    startInvoiceTool(data);

                                    return; // <---- Make sure this stays
                                  },
                                },
                              });

                              delete_scheduleAndShifts({
                                proposal: updatedProposal.id,
                              });
                            }
                          );
                        }
                      );
                    }
                  );
                }
              }
            }
          );
        },
        {
          category: true,
          name: true,
          selectionObj: true,
        }
      );
    } else {
      dom.tr.td1.makeNode("template", "div", { text: "No template selected." });
    }

    dom.tr2.td2
      .makeNode("add", "div", {
        css: "ui mini compact teal right floated button",
        text: "Add A Template",
      })
      .notify(
        "click",
        {
          type: "scheduling-run",
          data: {
            run: function () {
              $(dom.tr.td2.selector).addClass("disabled");

              dom.makeNode("modal", "modal", {
                onShow: function () {
                  var modal = dom.modal.body;

                  modal.makeNode("title", "div", {
                    text: "Choose a template",
                    css: "ui huge header",
                  });

                  modal.makeNode("tempCont", "div", {
                    css: "ui basic loading segment",
                  });

                  modal.patch();

                  sb.data.db.obj.getAll("categories", function (types) {
                    if (types.length == 0) {
                      modal.tempCont.makeNode("none", "div", {
                        css: "ui centered header",
                        text: "No templates",
                      });
                    } else {
                      modal.tempCont.makeNode("table", "div", {
                        css: "ui table",
                        tag: "table",
                      });
                      modal.tempCont.table.makeNode("thead", "div", {
                        tag: "thead",
                      });
                      modal.tempCont.table.thead.makeNode("tr", "div", {
                        tag: "tr",
                      });
                      modal.tempCont.table.thead.tr.makeNode("name", "div", {
                        tag: "th",
                        text: "Template Name",
                      });
                      modal.tempCont.table.thead.tr.makeNode("btns", "div", {
                        tag: "th",
                      });

                      modal.tempCont.table.makeNode("tbody", "div", {
                        tag: "tbody",
                      });

                      _.each(types, function (type) {
                        modal.tempCont.table.tbody.makeNode(
                          "tr" + type.id,
                          "div",
                          { tag: "tr" }
                        );
                        modal.tempCont.table.tbody["tr" + type.id].makeNode(
                          "name",
                          "div",
                          { tag: "td", text: type.name }
                        );
                        modal.tempCont.table.tbody["tr" + type.id].makeNode(
                          "btns",
                          "div",
                          { tag: "td" }
                        );
                        modal.tempCont.table.tbody["tr" + type.id].btns
                          .makeNode("select", "div", {
                            text: "Select This Template",
                            css: "ui mini compact teal right floated button",
                          })
                          .notify(
                            "click",
                            {
                              type: "scheduling-run",
                              data: {
                                run: function () {
                                  _.each(types, function (button) {
                                    if (type.id !== button.id) {
                                      modal.tempCont.table.tbody[
                                        "tr" + button.id
                                      ].btns.select.loading();
                                    } else {
                                      modal.tempCont.table.tbody[
                                        "tr" + type.id
                                      ].btns.select.loading();
                                    }
                                  });

                                  // select proper template
                                  sb.data.db.obj.getWhere(
                                    "groups",
                                    {
                                      is_template: 1,
                                      group_type: "Schedule",
                                      category: type.id,
                                      head_count: {
                                        type: "greater_than",
                                        value:
                                          data.state.project.head_count - 1,
                                      },
                                    },
                                    function (templates) {
                                      if (_.isEmpty(templates)) {
                                        sb.dom.alerts.alert(
                                          "No templates",
                                          "It looks like templates have not been created by the admins yet.",
                                          "error"
                                        );

                                        _.each(types, function (button) {
                                          if (type.id !== button.id) {
                                            modal.tempCont.table.tbody[
                                              "tr" + button.id
                                            ].btns.select.loading(false);
                                          } else {
                                            modal.tempCont.table.tbody[
                                              "tr" + type.id
                                            ].btns.select.loading(false);
                                          }
                                        });
                                      } else {
                                        // sort by head count
                                        var sortedTemplates = _.sortBy(
                                          templates,
                                          "head_count"
                                        );

                                        create_scheduleFromTemplate(
                                          sortedTemplates[0],
                                          data.state.object,
                                          function (newSchedule) {
                                            sb.data.db.obj.update(
                                              "proposals",
                                              {
                                                id: data.state.object.id,
                                                schedule_template:
                                                  sortedTemplates[0].category,
                                              },
                                              function (updatedProposal) {
                                                sb.data.db.obj.update(
                                                  "groups",
                                                  {
                                                    id: newSchedule.id,
                                                    parent:
                                                      data.state.object.id,
                                                  },
                                                  function (updatedSchdule) {
                                                    sb.notify({
                                                      type: "get-schedule-price",
                                                      data: {
                                                        schedule:
                                                          updatedSchdule.id,
                                                        price: function (
                                                          priceObj
                                                        ) {
                                                          data.state.object.schedule_template =
                                                            sortedTemplates[0].category;

                                                          startInvoiceTool(
                                                            data
                                                          );

                                                          return; // <---- Make sure this stays
                                                        },
                                                      },
                                                    });

                                                    dom.modal.hide();
                                                  }
                                                );
                                              }
                                            );
                                          }
                                        );
                                      }
                                    }
                                  );
                                },
                              },
                            },
                            sb.moduleId
                          );
                      });
                    }

                    modal.tempCont.patch();

                    modal.tempCont.loading(false);
                  });
                },
                onClose: function () {
                  startInvoiceTool(data);
                },
              });

              dom.patch();

              dom.modal.show();
            },
          },
        },
        sb.moduleId
      );

    dom.patch();

    data.callback();
  }

  // U
  function update_shift(shift, updatesObj, beforeUpdate, afterUpdate) {
    var updates = {
      id: shift.id,
    };

    beforeUpdate();

    _.each(updatesObj, function (value, key) {
      updates[key] = value;
    });

    sb.data.db.obj.update(
      "groups",
      updates,
      function (updatedShift) {
        afterUpdate(updatedShift);
      },
      1
    );
  }

  return {
    init: function () {
      comps.availability = sb.createComponent("calendar");

      sb.listen({
        [sb.moduleId + "-run"]: this.run,
        "start-schedule-availability": this.availability,
        "get-schedule-price": this.getSchedulePrice,
        "start-invoice-scheduling-tool": this.startInvoiceTool,
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "scheduling",
            title: "Scheduling",
            icon: '<i class="fa fa-table"></i> ',
            views: [
              // HQ Tool
              {
                id: "schedulingTool",
                type: "hqTool",
                name: "Scheduling",
                tip: "View and manage staff schedules",
                icon: {
                  type: "calendar outline",
                  color: "purple",
                },
                default: true,
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom) {
                      display_collections({
                        dom: dom,
                        state: state,
                      });
                    },
                  },
                ],
              },
              // Team Tool
              {
                id: "schedulingTool",
                type: "teamTool",
                name: "Scheduling",
                tip: "View and manage staff schedules",
                icon: {
                  type: "calendar outline",
                  color: "purple",
                },
                default: true,
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom) {
                      display_collections({
                        dom: dom,
                        state: state,
                      });
                    },
                  },
                ],
              },
              // Project Tool
              {
                id: "schedulingTool",
                type: "tool",
                name: "Staff Scheduling",
                default: true,
                tip: "Create staff schedules for active proposals on this project.",
                icon: {
                  type: "calendar outline",
                  color: "purple",
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw) {
                      var project = state.project;

                      dom.empty();

                      dom.makeNode("wrapper", "div", {});

                      Loader(dom.wrapper, "Checking for existing proposal...");

                      if (
                        project.proposal === null ||
                        !_.isObject(project.proposal)
                      ) {
                        dom.wrapper.empty();

                        dom.wrapper.makeNode("lb_1", "lineBreak", {
                          spaces: 1,
                        });

                        dom.wrapper.makeNode("note", "div", {
                          text: 'There are no proposals found for this project. Please create a proposal in the "Proposals and Billing" tool.',
                          css: "ui header text-center",
                          tag: "h2",
                        });

                        dom.wrapper.patch();
                      } else {
                        dom.wrapper.empty();

                        Loader(
                          dom.wrapper,
                          "Checking for existing staff schedule..."
                        );

                        dom.wrapper.patch();

                        sb.data.db.obj.getWhere(
                          "groups",
                          {
                            group_type: "Schedule",
                            parent: project.proposal.id,
                            childObjs: {
                              name: true,
                              parent: {
                                main_object: true,
                              },
                              category: true,
                              location: true,
                              start_date: true,
                              managers: true,
                              description: true,
                              head_count: true,
                              parent_description: true,
                            },
                          },
                          function (schedArr) {
                            if (!_.isEmpty(schedArr)) {
                              sb.notify({
                                type: "view-page",
                                data: {
                                  ui: dom,
                                  onDraw: function () {},
                                  page: return_singleScheduleViewObj(state),
                                  state: {
                                    pageObject: schedArr[0],
                                  },
                                },
                              });
                            } else {
                              dom.wrapper.empty();

                              dom.wrapper.makeNode("message", "div", {
                                css: "ui message",
                              });

                              dom.wrapper.message.makeNode("header", "div", {
                                css: "header",
                                text: "This project does not have a staff schedule",
                              });

                              dom.wrapper.message
                                .makeNode("content", "div", {
                                  css: "text-center",
                                })
                                .makeNode("btn", "div", {
                                  css: "ui green button",
                                  text: "Create a schedule",
                                })
                                .notify(
                                  "click",
                                  {
                                    type: [sb.moduleId + "-run"],
                                    data: {
                                      run: function (data) {
                                        state.proposal = project.proposal;
                                        state.domObj = dom;

                                        create_schedule(dom, {
                                          state: state,
                                          onClose: function () {
                                            sb.notify({
                                              type: "app-navigate-to",
                                              data: {
                                                type: "UP",
                                              },
                                            });
                                          },
                                        });
                                      },
                                    },
                                  },
                                  sb.moduleId
                                );

                              dom.wrapper.patch();
                            }
                          }
                        );
                      }

                      dom.patch();
                    },
                  },
                ],
                boxViews: [
                  {
                    id: "schedulingTool",
                    width: "six",
                    title: "Staff Scheduling",
                    dom: function (dom, state, draw) {
                      sb.data.db.obj.getWhere(
                        "groups",
                        {
                          group_type: "Schedule",
                          parent: state.project.proposal.id,
                          childObjs: 1,
                        },
                        function (schedArr) {
                          if (_.isEmpty(schedArr)) {
                            draw(false);
                            return;
                          } else {
                            sb.data.db.obj.getWhere(
                              "groups",
                              {
                                group_type: "Shift",
                                parent: schedArr[0].id,
                                childObjs: {
                                  id: true,
                                  status: true,
                                },
                              },
                              function (shifts) {
                                dom.makeNode("total", "div", {
                                  tag: "h1",
                                  text:
                                    shifts.length.toString() +
                                    '<div class="ui sub header">Total Shifts</div>',
                                  css: "text-center ui header",
                                });

                                if (schedArr[0].status === "Active") {
                                  dom.makeNode("label", "div", {
                                    text: schedArr[0].status,
                                    css: "ui orange label",
                                  });
                                }

                                dom.makeNode("content", "div", {
                                  tag: "h4",
                                  css: "ui header",
                                  text: "A staff schedule is available for active proposal.",
                                });

                                draw(dom);
                              }
                            );
                          }
                        }
                      );
                    },
                  },
                ],
              },
              // My Schedule
              {
                id: "myschedule",
                type: "myStuff",
                name: "Schedule",
                title: "Schedule",
                tip: "View and manage your schedule",
                icon: {
                  type: "calendar outline",
                  color: "purple",
                },
                default: true,
                mainViews: [
                  {
                    dom: function (dom, state, draw) {
                      sb.data.db.obj.getById(
                        "users",
                        +sb.data.cookie.userId,
                        function (user) {
                          //Time Clock PIN
                          dom.makeNode("timeclockNode", "div", {});
                          dom.makeNode("clockpinNode", "div", {});

                          dom.timeclockNode.makeNode("clockpin", "div", {
                            css: "ui label clockpin",
                            style:
                              "margin:0px 0px 10px 8px;font-size: 0.98rem;",
                            text:
                              '<i class="clock text-muted icon"></i><span class="">PIN # ' +
                              user.pin +
                              "</span>",
                          });

                          dom.patch();

                          state.myStuff = true;
                          state.user = user;

                          display_shiftCollections(
                            dom.clockpinNode,
                            undefined,
                            state
                          );
                        },
                        {
                          fname: true,
                          lname: true,
                          service: true,
                        }
                      );
                    },
                  },
                ],
                boxViews: [
                  {
                    id: "scheduleOverview",
                    width: "four",
                    title: "Schedule Overview",
                    dom: function (dom, state, draw) {
                      sb.data.db.obj.getWhere(
                        "groups",
                        {
                          group_type: "Shift",
                          user: +sb.data.cookie.get("uid"),
                          start_date: {
                            type: "between",
                            start: moment().startOf("day").unix(),
                            end: moment().add(1, "week").endOf("week").unix(),
                          },
                          is_template: 0,
                          childObjs: 1,
                        },
                        function (shifts) {
                          if (!_.isEmpty(shifts)) {
                            dom.makeNode("segs", "div", {
                              css: "ui basic segments",
                            });

                            _.each(shifts, function (shift) {
                              dom.segs.makeNode("seg-" + shift.id, "div", {
                                css: "ui green segment",
                              });

                              dom.segs["seg-" + shift.id].makeNode(
                                "title",
                                "div",
                                {
                                  tag: "h3",
                                  css: "ui header",
                                  text:
                                    shift.name +
                                    '<div class="ui sub header">' +
                                    moment(shift.start_date).format(
                                      "dddd h:mma"
                                    ) +
                                    " - " +
                                    moment(shift.end_date).format("h:mma") +
                                    "</div>",
                                }
                              );
                            });
                          } else {
                            dom.makeNode("none", "div", {
                              tag: "h3",
                              css: "text-center",
                              text: "No upcoming shifts",
                            });
                          }

                          draw(dom);
                        }
                      );
                    },
                  },
                ],
              },
              // Calendar Tool
              {},
              // Schedule object view
              {
                id: "schedule-obj",
                type: "object-view",
                title: "Staff Schedules",
                icon: "calendar alternate outline",
                view: return_singleScheduleViewObj(),
              },
              // Shift object view
              {
                id: "shift-obj",
                type: "object-view",
                title: "Shifts",
                icon: "calendar alternate outline",
                view: {
                  wrapper: {
                    type: "view",
                    view: function (ui, shift, state) {
                      build_singleShiftView(
                        ui,
                        shift,
                        {
                          patch: true,
                        },
                        function (ui, shift, setup) {
                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "users",
                              property: "managers",
                              obj: shift,
                              options: {
                                edit: false,
                              },
                              ui: ui.wrapper.head.col1.managers,
                            },
                          });

                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "detail",
                              property: "description",
                              obj: shift,
                              options: {
                                editing: sb.permissions.isGroupManager(
                                  +sb.data.cookie.userId,
                                  appConfig.headquarters
                                ),
                                placeholder: "Add shift details...",
                              },
                              ui: ui.wrapper.body.details.wrapper,
                            },
                          });

                          ui.wrapper.patch();

                          //ui.wrapper.head.col1.managers.patch();
                          //ui.wrapper.body.details.patch();

                          sb.notify({
                            type: "show-note-list-box",
                            data: {
                              collapse: false,
                              domObj: ui.wrapper.body.comments,
                              objectIds: [shift.id],
                              objectId: shift.id,
                            },
                          });
                        }
                      );
                    },
                  },
                },
              },
            ],
          },
        },
      });
    },

    run: function (data) {
      data.run(data);
    },

    // ---------------------- //

    availability: function (setup) {
      if (setup.domObj) {
        if (setup.obj) {
          availability(setup.domObj, setup.obj);
        } else {
          throw "You need to inject an obj.";
        }
      } else {
        throw "You need to inject a domObj.";
      }
    },

    getSchedulePrice: function (data) {
      var schedulePrice = {};

      sb.data.db.obj.getWhere(
        "groups",
        {
          group_type: "Shift",
          tagged_with: [data.schedule],
          childObjs: 1,
        },
        function (shifts) {
          _.each(shifts, function (shift, i) {
            if (
              schedulePrice.hasOwnProperty([
                shift.job_type.inventory_billable_categories,
              ])
            ) {
              schedulePrice[shift.job_type.inventory_billable_categories] +=
                price_singleShift(shift);
            } else {
              schedulePrice[shift.job_type.inventory_billable_categories] =
                price_singleShift(shift);
            }
          });

          data.price(schedulePrice);
        }
      );
    },

    startInvoiceTool: function (data) {
      startInvoiceTool(data);
    },
  };
});
