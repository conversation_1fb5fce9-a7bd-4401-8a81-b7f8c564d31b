Factory.register("proposals", function (sb) {
  var ui = {},
    tableUI = {},
    components = {},
    objectId = 0,
    defaultRequestEmail = "Please click below to view your proposals.",
    defaultRequestEmailSubject = "You have new proposals to view",
    defaultDisclaimer =
      "The parties agree that this agreement may be electronically signed. The parties agree that the electronic signatures appearing on this agreement are the same as handwritten signatures for the purposes of validity, enforceability and admissibility.<br /><br />You may withdraw your consent to receive electronic documents, notices or disclosures at any time. In order to withdraw consent, you must notify the sending party that you wish to withdraw consent and request that your future documents, notices and disclosures be provided in paper format. To request paper copies of documents; withdraw consent to conduct business electronically and receive documents, notices or disclosures electronically; or withdraw consent to sign documents electronically, please contact the sending party by telephone, postal mail or email.",
    defaultOppNoteText =
      'Here is your new Opportunity.<br /><span style="font-size:14px">You can use this area to type your notes and help you stay organized.</span><br /><br />1. How did you meet this contact?<br /><br /><br />2. What problems is this client having?<br /><br /><br />3. Who should be included in this deal?<br /><span style="font-size:14px; color:gray; background-color:lightgray;"><i> (@Team Member1, @Team Member2) </i></span>',
    sectionTypes = {},
    sections = {
      contract: "Yes",
      itemList: "Yes",
      lineItem: "No",
      pricingTable: "No",
      invoiceTable: "Yes",
      itemListCategories: {},
      pricingTableCategories: {},
      labor: false,
    };

  function checkSettingsObject(callback) {
    sb.data.db.obj.getAll("workorder_system", function (settingsObj) {
      if (settingsObj.length == 0) {
        var settingsObj = {
          signature_disclaimer: defaultDisclaimer,
          request_email: defaultRequestEmail,
          request_email_subject: defaultRequestEmailSubject,
          default_opp_note: defaultOppNoteText,
          follow_up_time: 1,
          follow_up_type: "days",
        };

        sb.data.db.obj.create(
          "workorder_system",
          settingsObj,
          function (created) {
            callback(created);
          }
        );
      } else {
        callback(settingsObj[0]);
      }
    });
  }

  function createMergedHTML(obj, objectType, callback) {
    if (obj) {
      sb.data.db.obj.getWhere(
        objectType,
        { id: obj.main_object, childObjs: 1 },
        function (mergeObj) {
          sb.data.db.obj.getBlueprint(objectType, function (mergeBP) {
            getAllBluprints(mergeBP, function (allBlueprints) {
              var htmlString = obj.html_string;

              mergeObj = mergeObj[0];

              if (obj.status == "Signed") {
                var searchIndex = [
                  {
                    term: "{{PLEASE SIGN HERE}}",
                    value:
                      '<img width="300px" src="https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/' +
                      obj.instance +
                      "/" +
                      obj.signatures.loc +
                      '"><br /><b>' +
                      obj.signer_name +
                      " @ " +
                      moment(obj.signatures.date_created).format(
                        "M/DD/YYYY h:mm:ss a"
                      ) +
                      " - " +
                      obj.signer_ip +
                      "</b>",
                  },
                ];
              } else {
                var searchIndex = [
                  {
                    term: "{{PLEASE SIGN HERE}}",
                    value:
                      '<span class="signtaureLine" style="font-weight:bold;">PLEASE SIGN HERE</span>',
                  },
                ];
              }

              _.each(mergeBP, function (bpObj, name) {
                if (mergeObj) {
                  searchIndex.push({
                    term: "{{" + objectType + "." + name + "}}",
                    value: mergeObj[name],
                  });
                }
              });

              _.each(allBlueprints, function (obj) {
                _.each(obj.blueprint, function (bpObj, name) {
                  if (mergeObj) {
                    if (mergeObj[obj.bp_name]) {
                      var searchValue = mergeObj[obj.bp_name][name];
                    } else {
                      var searchValue = "";
                    }
                  } else {
                    var searchValue = "";
                  }

                  searchIndex.push({
                    term: "{{" + obj.bp_name + "." + name + "}}",
                    value: searchValue,
                  });
                });
              });

              _.each(searchIndex, function (i) {
                htmlString = htmlString.replace(
                  new RegExp(i.term, "g"),
                  i.value
                );
              });

              callback(htmlString);
            });
          });
        },
        true
      );
    } else {
      callback("");
    }
  }

  function deleteObjects(allObjects, callback, count) {
    if (!count) {
      count = 0;
    }

    sb.data.db.obj.erase(
      allObjects[count].objectType,
      allObjects[count].id,
      function (done) {
        count++;

        if (count == allObjects.length) {
          callback(count);
        } else {
          deleteObjects(allObjects, callback, count);
        }
      }
    );
  }

  function estimateView(
    dom,
    obj,
    pricing,
    categories,
    shifts,
    objectType,
    compact,
    updateCallback,
    buttonDom,
    reloadCallbackFunc
  ) {
    sb.data.db.obj.getWhere(
      "proposals",
      { main_object: obj.id },
      function (proposals) {
        checkSettingsObject(function (settings) {
          var mainDom = dom,
            sections = {
              contract: "Yes",
              itemList: "Yes",
              lineItem: "No",
              pricingTable: "No",
              invoiceTable: "Yes",
              itemListCategories: {},
              pricingTableCategories: {},
              labor: false,
            },
            isTemplate = 0,
            workOrder = obj;
          var readyToSend = true;

          _.each(proposals, function (prop) {
            switch (prop.status) {
              case "Approved":
              case "Declined":
              case "Active":
              case "Editing":
                readyToSend = false;
                break;

              default:
                readyToSend = true;
            }

            if (!prop.status) {
              //readyToSend = false;
            }
          });

          _.each(categories, function (cat) {
            sections.itemListCategories[cat.id] = true;
            sections.pricingTableCategories[cat.id] = true;
          });

          if (shifts.length > 0) {
            sections.labor = true;
          }

          function copyCurrentProposal(obj, pricing, sections, callback) {
            var dom = this;

            if (
              obj.status == "Accepted" ||
              obj.status == "Paid" ||
              obj.status == "Completed"
            ) {
              sb.dom.alerts.alert(
                "Proposal accepted",
                "A proposal has already been accepted, so you can't create another one for this work order."
              );

              callback(true);
            } else {
              //dom.tools.cont.btns.save.loading();

              sb.data.db.obj.getById(
                "groups",
                obj.id,
                function (obj) {
                  var contractId = 0,
                    invoicesIds = [],
                    menuId = 0;

                  if (obj.proposal.contract) {
                    contractid = obj.proposal.contract.id;
                  }

                  if (obj.proposal.invoices) {
                    invoicesIds = _.pluck(obj.proposal.invoices, "id");
                  }

                  if (obj.proposal.menu) {
                    obj.proposal.menu.id;
                  }

                  sb.data.db.obj.getWhere(
                    "invoices",
                    { related_object: obj.proposal.id },
                    function (invoices) {
                      sb.data.db.obj.getWhere(
                        "inventory_menu",
                        { id: menuId },
                        function (menus) {
                          var historyItem = {
                            id: obj.proposal.id,
                            contract: contractId,
                            pricing: pricing,
                            invoices: _.pluck(invoices, "id"),
                            menu: menuId,
                            name: "Current Proposal",
                            date: moment(),
                          };

                          estimateBuilder(
                            dom,
                            obj,
                            pricing,
                            categories,
                            sections,
                            shifts,
                            function (done) {
                              callback(true);
                            }
                          );
                        }
                      );
                    }
                  );
                },
                1
              );
            }
          }

          function createASnapshot(obj, pricing, callback, name) {
            function saveSnapshot(orgObj, pricing, name, callback) {
              function createContractObject(
                obj,
                proposal,
                callback,
                active,
                objId
              ) {
                obj.proposal.contract.related_object = proposal.id;

                sb.data.db.obj.create(
                  "contracts",
                  obj.proposal.contract,
                  function (newContract) {
                    callback(newContract);
                  },
                  3
                );
              }

              function createMenuObject(
                obj,
                proposal,
                callback,
                active,
                objId
              ) {
                function duplicateMenuItems(menu, stack, count, callback, ret) {
                  if (!ret) {
                    ret = [];
                  }

                  if (stack[count]) {
                    var itemsToCreate = [];

                    _.each(stack[count].items, function (item) {
                      if (item != null && item.hasOwnProperty("menu")) {
                        item.menu = menu.id;
                        itemsToCreate.push(item);
                      }
                    });

                    sb.data.db.obj.create(
                      "inventory_menu_line_item",
                      itemsToCreate,
                      function (createdItems) {
                        stack[count].items = _.pluck(createdItems, "id");

                        ret.push(stack[count]);

                        count++;

                        return duplicateMenuItems(
                          menu,
                          stack,
                          count,
                          callback,
                          ret
                        );
                      }
                    );
                  } else {
                    return callback(ret);
                  }
                }

                if (obj.proposal.menu) {
                  var where = { id: obj.proposal.menu.id, childObjs: 3 };

                  if (active) {
                    where.active = active;
                  }

                  if (objId) {
                    where.id = +objId;
                  }

                  if (active == "No" && !objId) {
                    return callback(false);
                  }

                  sb.data.db.obj.getWhere(
                    "inventory_menu",
                    where,
                    function (res) {
                      if (res.length > 0) {
                        // has a menu
                        res[0].related = proposal.id;
                        res[0].guest_count = obj.proposal.guest_count;

                        sb.data.db.obj.create(
                          "inventory_menu",
                          res[0],
                          function (newMenu) {
                            duplicateMenuItems(
                              newMenu,
                              res[0].sections,
                              0,
                              function (newSections) {
                                newMenu.sections = newSections;

                                sb.data.db.obj.update(
                                  "inventory_menu",
                                  newMenu,
                                  function (updatedMenu) {
                                    callback(updatedMenu);
                                  },
                                  3
                                );
                              }
                            );
                          },
                          3
                        );
                      } else {
                        // no menu found
                        callback(false);
                      }
                    }
                  );
                } else {
                  callback(false);
                }
              }

              function createStaffScheduleObject(
                obj,
                proposal,
                callback,
                active,
                objId
              ) {
                if (!obj.proposal.schedule) {
                  return callback(false);
                } else {
                  if (obj.proposal.schedule.id == 0) {
                    return callback(false);
                  }
                }

                var where = { id: obj.proposal.schedule.id };

                if (active) {
                  //where.active = active;
                }

                if (objId) {
                  //where.id = +objId;
                }

                sb.data.db.obj.getWhere(
                  "staff_schedules",
                  where,
                  function (res) {
                    if (res.length > 0) {
                      // has a staff schedule
                      res[0].event = proposal.id;

                      sb.data.db.obj.create(
                        "staff_schedules",
                        res[0],
                        function (staffSchedule) {
                          callback(staffSchedule);
                        },
                        2
                      );
                    } else {
                      // no staff schedule found
                      callback(false);
                    }
                  }
                );
              }

              function createInvoiceObjects(
                obj,
                proposal,
                callback,
                active,
                objIds
              ) {
                sb.data.db.obj.getWhere(
                  "invoices",
                  { related_object: obj.proposal.id },
                  function (invoices) {
                    if (!invoices) {
                      return callback(false);
                    } else {
                      if (invoices.length == 0) {
                        return callback(false);
                      }
                    }

                    _.each(invoices, function (invoiceObj) {
                      invoiceObj.related_object = proposal.id;
                    });

                    sb.data.db.obj.create(
                      "invoices",
                      invoices,
                      function (invoice) {
                        callback(invoice);
                      },
                      2
                    );
                  }
                );
              }

              function duplicateHistoryItems(workOrder, template, callback) {
                function copyHistoryItems(stack, count, callback, ret) {
                  if (!ret) {
                    ret = [];
                  }

                  if (stack[count]) {
                    var item = stack[count],
                      itemsToCreate = [];

                    createContractObject(
                      template,
                      workOrder,
                      function (newContract) {
                        createMenuObject(
                          template,
                          workOrder,
                          function (newMenu) {
                            createStaffScheduleObject(
                              template,
                              workOrder,
                              function (newSchedule) {
                                createInvoiceObjects(
                                  template,
                                  workOrder,
                                  function (newInvoices) {
                                    var historyItem = item;

                                    historyItem.date = moment().format();
                                    historyItem.contract = newContract.id;
                                    historyItem.start_date =
                                      workOrder.start_date;
                                    historyItem.end_date = workOrder.end_date;

                                    if (newMenu) {
                                      historyItem.menu = newMenu.id;
                                    }

                                    if (newSchedule) {
                                      historyItem.schedule = newSchedule.id;
                                    }

                                    if (newInvoices) {
                                      historyItem.invoices = _.pluck(
                                        newInvoices,
                                        "id"
                                      );
                                    }

                                    ret.push(historyItem);

                                    count++;

                                    return copyHistoryItems(
                                      stack,
                                      count,
                                      callback,
                                      ret
                                    );
                                  },
                                  "No",
                                  item.invoices
                                );
                              },
                              "No",
                              item.schedule
                            );
                          },
                          "No",
                          item.menu
                        );
                      },
                      "No",
                      item.contract
                    );
                  } else {
                    return callback(ret);
                  }
                }

                if (template.history.length > 0) {
                  copyHistoryItems(template.history, 0, function (newHistory) {
                    workOrder.history = newHistory;

                    sb.data.db.obj.update(
                      "work_orders",
                      workOrder,
                      function (updatedWorkOrder) {
                        callback(updatedWorkOrder);
                      },
                      3
                    );
                  });
                } else {
                  callback(workOrder);
                }
              }

              sb.data.db.obj.create(
                "proposals",
                { main_object: orgObj.id },
                function (newProp) {
                  // Create a Copy of the Active Contract
                  createContractObject(obj, newProp, function (contractObj) {
                    // Create a Copy of the Active Menu and Appy Copied ID to the new Workorder
                    createMenuObject(obj, newProp, function (menuObj) {
                      // Create a Copy of the Active Schedule and Appy Copied ID to the new Workorder
                      createStaffScheduleObject(
                        obj,
                        newProp,
                        function (staffSchedule) {
                          // Create a Copy of the Active Invoices and Appy Copied ID to the new Workorder
                          createInvoiceObjects(
                            obj,
                            newProp,
                            function (invoices) {
                              sb.notify({
                                type: "get-menu-total-pricing",
                                data: {
                                  menu: menuObj,
                                  obj: newProp,
                                  pricing: function (pricedMenu) {
                                    newProp.contract = contractObj.id;
                                    newProp.invoices = _.pluck(invoices, "id");
                                    newProp.name = name;
                                    newProp.menu = menuObj.id;
                                    newProp.sections = sections;
                                    newProp.pricing = pricedMenu;
                                    newProp.schedule = staffSchedule.id;
                                    newProp.start_date = workOrder.start_date;
                                    newProp.end_date = workOrder.end_date;
                                    newProp.status = "Proposal";

                                    sb.data.db.obj.update(
                                      "proposals",
                                      newProp,
                                      function (newProposal) {
                                        var oldProposal = obj.proposal;

                                        sb.data.db.obj.update(
                                          "groups",
                                          { id: obj.id, status: "Proposal" },
                                          function (updatedWorkorders) {
                                            if (
                                              obj.object_bp_type ==
                                              "work_orders"
                                            ) {
                                              sb.data.db.obj.update(
                                                obj.object_bp_type,
                                                {
                                                  id: obj.id,
                                                  status: "Proposal",
                                                },
                                                function (updated) {
                                                  callback(updatedWorkorders);
                                                }
                                              );
                                            } else {
                                              callback(updatedWorkorders);
                                            }
                                          },
                                          3
                                        );
                                      }
                                    );
                                  },
                                },
                              });
                            }
                          );
                        }
                      );
                    });
                  });
                }
              );
            }

            if (name) {
              saveSnapshot(obj, pricing, name, function (done) {
                callback(done);
              });
            } else {
              swal(
                {
                  title: "Give it a name",
                  text: "",
                  type: "input",
                  showCancelButton: true,
                  closeOnConfirm: false,
                  inputValue:
                    "Proposal - " + moment().format("MMMM Do YYYY, h:mm a"),
                },
                function (name) {
                  if (name !== false) {
                    if (name === "") {
                      swal.showInputError("You need to write something!");
                      return false;
                    }

                    swal.disableButtons();

                    saveSnapshot(obj, pricing, name, function (done) {
                      swal.close();

                      if (callback) {
                        callback(done);
                      }
                    });
                  }
                }
              );
            }
          }

          function displayHistoryItem(
            dom,
            historyItem,
            obj,
            pricing,
            categories,
            callback,
            noModal,
            reloadCallback
          ) {
            var workOrderTxt = historyItem.start_date
              ? "<small>Work Order Date:</small><br />" +
                moment(historyItem.start_date, "YYYY-MM-DD HH:mm:ss").format(
                  "M/D/YYYY h:mm a"
                )
              : '<small>Work Order Date:</small><br /><span class="text-muted">No date set</span>';

            if (!noModal) {
              dom
                .makeNode("modals", "div", {})
                .makeNode("history", "modal", { size: "large" });
              var modal = dom.modals.history;
            } else {
              dom.makeNode("body", "div", {});
              var modal = dom;
            }

            modal.body.makeNode("titleBreak", "lineBreak", {});

            modal.body.makeNode("loading", "div", {});
            //modal.body.loading.makeNode('text', 'text', {text:'Loading proposal...', css:'text-center'});
            //modal.body.loading.makeNode('loader', 'loader', {});

            dom.patch();

            if (!noModal) {
              modal.show();
            }

            sb.data.db.obj.getWhere(
              "contracts",
              { id: +historyItem.contract },
              function (contractObjs) {
                var contractObj = contractObjs[0];

                modal.body.makeNode("cols", "div", {
                  css: "ui stackable grid",
                });

                if (!noModal) {
                  modal.body.cols.makeNode("info", "column", { w: 4 });

                  modal.body.cols.info.makeNode("name", "div", {
                    text:
                      historyItem.name +
                      " - created " +
                      moment(historyItem.date).calendar(),
                    css: "ui medium header",
                  });

                  modal.body.cols.info.makeNode("btns", "div", {
                    css: "ui vertical fluid buttons",
                  });

                  var buttonUI = modal.body.cols.info.btns;

                  modal.body.cols.info.makeNode("tagBreak", "div", {
                    text: "<br />",
                  });

                  modal.body.cols.info.makeNode("tags", "div", {});

                  modal.body.cols.info.makeNode("title", "div", {
                    text: "Details",
                    css: "ui small header",
                  });

                  modal.body.cols.info.makeNode("start", "headerText", {
                    size: "x-small",
                    text: workOrderTxt,
                  });
                  modal.body.cols.info.makeNode("price", "headerText", {
                    size: "x-small",
                    text:
                      "<small>Total Price:</small><br />$" +
                      (
                        _.chain(historyItem.pricing)
                          .values()
                          .reduce(function (memo, num) {
                            return memo + +num;
                          }, 0)
                          .value() / 100
                      ).formatMoney(),
                  });
                  modal.body.cols.info.makeNode("invoices", "headerText", {
                    size: "x-small",
                    text: workOrderTxt,
                  });

                  if (historyItem.venue) {
                    modal.body.cols.info.makeNode("location", "headerText", {
                      size: "x-small",
                      text:
                        "<small>Location:</small> " + historyItem.venue.name,
                    });
                  }

                  modal.body.cols.makeNode("contract", "column", { w: 12 });
                } else {
                  modal.body.cols.makeNode("btns", "div", {
                    css: "ui buttons",
                  });

                  var buttonUI = modal.body.cols.btns;

                  modal.body.cols.makeNode("contract", "column", { w: 16 });

                  modal.body.cols.contract.makeNode("title", "div", {
                    text:
                      historyItem.name +
                      " - created " +
                      moment(historyItem.date).calendar(),
                    css: "ui medium header",
                  });
                }

                if (
                  settings.require_approval != "yes" &&
                  historyItem.status != "Signed"
                ) {
                  var approvalString = "Client cannot see this proposal";
                  var approvalColor = "";
                  if (historyItem.status == "Approved") {
                    approvalString = "Client can see this proposal";
                    approvalColor = "green";
                  }

                  buttonUI
                    .makeNode("ready", "button", {
                      text: approvalString,
                      css: "ui " + approvalColor + " button",
                    })
                    .notify(
                      "click",
                      {
                        type: "proposals-run",
                        data: {
                          run: function (
                            obj,
                            pricing,
                            categories,
                            historyItem
                          ) {
                            var modal = this;
                            var newState;

                            buttonUI.ready.loading();

                            switch (historyItem.status) {
                              case "Approved":
                                newState = "Proposal";

                                break;

                              default:
                                newState = "Approved";
                            }

                            sb.data.db.obj.update(
                              "proposals",
                              { id: historyItem.id, status: newState },
                              function (updated) {
                                switch (newState) {
                                  case "Approved":
                                    buttonUI.ready.loading(false);
                                    buttonUI.ready.text(
                                      "Client can see this proposal"
                                    );

                                    break;

                                  default:
                                    buttonUI.ready.loading(false);
                                    buttonUI.ready.text(
                                      "Client cannot see this proposal"
                                    );
                                }

                                historyViewer(
                                  mainDom.tools.cont.history,
                                  obj,
                                  mainDom.estimate.cont,
                                  pricing,
                                  categories,
                                  compact
                                );
                              }
                            );
                          }.bind(modal, obj, pricing, categories, historyItem),
                        },
                      },
                      sb.moduleId
                    );
                }

                if (
                  historyItem.status != "Accepted" &&
                  historyItem.status != "Signed" &&
                  historyItem.status != "Paid" &&
                  historyItem.status != "Completed"
                ) {
                  if (historyItem.status != "Approved") {
                    buttonUI
                      .makeNode("delete", "button", {
                        text: "Delete",
                        css: "pda-btn-red",
                      })
                      .notify(
                        "click",
                        {
                          type: "proposals-run",
                          data: {
                            run: function (
                              obj,
                              pricing,
                              categories,
                              historyItem
                            ) {
                              var modal = this;

                              sb.dom.alerts.ask(
                                {
                                  title: "Are you sure?",
                                  text: "This cannot be undone.",
                                },
                                function (resp) {
                                  if (resp) {
                                    swal.disableButtons();

                                    obj.history = _.reject(
                                      obj.history,
                                      function (item) {
                                        return (
                                          +item.contract ===
                                          +historyItem.contract
                                        );
                                      }
                                    );

                                    sb.data.db.obj.erase(
                                      "proposals",
                                      historyItem.id,
                                      function (deleted) {
                                        modal.hide();

                                        setTimeout(function () {
                                          historyViewer(
                                            mainDom.tools.cont.history,
                                            obj,
                                            mainDom.estimate.cont,
                                            pricing,
                                            categories,
                                            compact
                                          );
                                        }, 500);

                                        swal.close();
                                      }
                                    );
                                  }
                                }
                              );
                            }.bind(
                              modal,
                              obj,
                              pricing,
                              categories,
                              historyItem
                            ),
                          },
                        },
                        sb.moduleId
                      );
                  }

                  buttonUI
                    .makeNode("apply", "button", {
                      text: "Edit Proposal",
                      css: "pda-btn-orange",
                    })
                    .notify(
                      "click",
                      {
                        type: "proposals-run",
                        data: {
                          run: function (obj, historyItem) {
                            var dom = this;

                            sb.dom.alerts.ask(
                              {
                                title: "Are you sure?",
                                text: "Your current proposal will be saved. You will be able to edit it again later.",
                              },
                              function (resp) {
                                if (resp) {
                                  swal.disableButtons();

                                  var saveSections = obj.proposal.sections;
                                  if (_.isArray(sections)) {
                                    saveSections = sections;
                                  }

                                  sb.data.db.obj.update(
                                    obj.object_bp_type,
                                    { id: obj.id, proposal: historyItem.id },
                                    function (updatedObj) {
                                      swal.close();

                                      dom.hide();

                                      if (reloadCallback) {
                                        reloadCallback(updatedObj);
                                      } else {
                                        sb.notify({
                                          type: "app-redraw",
                                          data: {},
                                        });
                                      }
                                    },
                                    2
                                  );
                                }
                              }
                            );
                          }.bind(modal, obj, historyItem),
                        },
                      },
                      sb.moduleId
                    );
                }

                buttonUI
                  .makeNode("download", "div", {
                    text: "Download PDF",
                    css: "ui blue button",
                  })
                  .notify(
                    "click",
                    {
                      type: "proposals-run",
                      data: {
                        run: function (obj, categories, historyItem, buttonUI) {
                          var dom = this;

                          buttonUI.download.loading();

                          viewProposalPDF(
                            historyItem,
                            categories,
                            historyItem.sections,
                            function (done) {
                              buttonUI.download.loading(false);
                            }
                          );
                        }.bind(modal, obj, categories, historyItem, buttonUI),
                      },
                    },
                    sb.moduleId
                  );

                var templateBtnColor = "blue";
                var templateBtnText = "Is not a template";
                if (historyItem.is_template == 1) {
                  templateBtnColor = "blue";
                  templateBtnText = "Is a template";
                }

                buttonUI
                  .makeNode("template", "button", {
                    text: templateBtnText,
                    css: "pda-btn-" + templateBtnColor,
                  })
                  .notify(
                    "click",
                    {
                      type: "proposals-run",
                      data: {
                        run: function (obj, categories, historyItem, buttonUI) {
                          buttonUI.template.css(
                            "pda-btn-" + templateBtnColor + " loading"
                          );

                          if (historyItem.is_template == 1) {
                            historyItem.is_template = 0;
                          } else {
                            historyItem.is_template = 1;
                          }

                          sb.data.db.obj.update(
                            "proposals",
                            historyItem,
                            function (updated) {
                              var templateBtnColor = "blue";
                              var templateBtnText = "Is not a template";

                              if (updated.is_template == 1) {
                                templateBtnColor = "blue";
                                templateBtnText = "Is a template";
                              }

                              buttonUI.template.css("");
                              buttonUI.template.css(
                                "pda-btn-" + templateBtnColor
                              );
                              buttonUI.template.text(templateBtnText);
                            }
                          );
                        }.bind(modal, obj, categories, historyItem, buttonUI),
                      },
                    },
                    sb.moduleId
                  );

                viewProposalPDF(
                  historyItem,
                  categories,
                  historyItem.sections,
                  function (htmlString) {
                    delete modal.body.loading;

                    modal.body.cols.contract
                      .makeNode("segment", "div", {
                        css: "ui raised basic segment",
                      })
                      .makeNode("text", "text", {
                        text:
                          '<div style="max-width:950px; margin:0 auto; display:block; padding:17px 50px !important;" class="pda-Panel pda-panel-gray pda-background-white">' +
                          htmlString +
                          "</div>",
                      });

                    modal.body.cols.contract.makeNode("notesBreak", "div", {
                      text: "<br />",
                    });
                    modal.body.cols.contract.makeNode("notes", "container", {
                      uiGrid: false,
                    });
                    modal.body.cols.contract.notes.makeNode(
                      "text",
                      "headerText",
                      {
                        text: "These notes are <i>not</i> visible to the client. Only your team can see them.",
                        css: "text-center pda-center pda-color-primary",
                        size: "x-small",
                      }
                    );
                    modal.body.cols.contract.notes.makeNode(
                      "notes",
                      "container",
                      { uiGrid: false }
                    );

                    modal.body.patch();

                    if (callback) {
                      var noteIds = [
                          +historyItem.contract,
                          historyItem.id,
                          obj.id,
                        ],
                        historyIds = _.chain(obj.history)
                          .pluck("contract")
                          .map(function (obj) {
                            return +obj;
                          })
                          .value();

                      noteIds.concat(historyIds);

                      sb.notify({
                        type: "show-note-list-box",
                        data: {
                          domObj: modal.body.cols.contract.notes.notes,
                          objectIds: noteIds,
                          objectId: +historyItem.id,
                        },
                      });

                      // tags comp
                      if (
                        sb.sys.state.components.tags &&
                        modal.body.cols.info
                      ) {
                        // remove old tags comp instance
                        if (components.tags) {
                          components.tags.destroy();
                          delete components.tags;
                        }

                        components.tags = sb.createComponent("tags");
                        components.tags.notify({
                          type: "object-tag-view",
                          data: {
                            domObj: modal.body.cols.info.tags,
                            objectType: "proposals",
                            objectId: historyItem.id,
                          },
                        });
                      }

                      callback(true);
                    }
                  },
                  true
                );
              }
            );
          }

          function estimateBuilder(
            dom,
            obj,
            pricing,
            categories,
            sections,
            shifts,
            callback,
            reloadCallback
          ) {
            function previewEstimate(dom, callback, pdf) {
              var html = true;
              if (pdf) {
                html = false;
              }

              if (html === true) {
                dom.empty();

                //dom.makeNode('title', 'headerText', {text:'Proposal Preview', size:'x-small', css:'pda-color-orange'});

                dom.makeNode("cont", "div", {});
              }

              if (!obj.proposal.schedule) {
                obj.proposal.schedule = {
                  id: 0,
                };
              }

              var invoicesIds = [],
                menuId = 0,
                contractId = 0,
                scheduleId = 0;

              if (obj.proposal.invoices) {
                invoicesIds = obj.proposal.invoices;
              }
              if (obj.proposal.menu) {
                menuId = obj.proposal.menu;
              }
              if (obj.proposal.contract) {
                contractId = obj.proposal.contract;
              }
              if (obj.proposal.schedule) {
                scheduleId = obj.proposal.schedule;
              }

              var historyItem = {
                id: obj.proposal.id,
                contract: contractId,
                invoices: invoicesIds,
                menu: menuId,
                name: "Proposal preview",
                date: moment(),
                pricing: obj.proposal.pricing,
                related_object: obj.proposal.id,
                main_object: obj.proposal.id,
              };

              if (scheduleId > 0) {
                historyItem.schedule = scheduleId;
              }

              viewProposalPDF(
                historyItem,
                categories,
                sections,
                function (htmlString) {
                  if (html === true) {
                    dom.cont.makeNode("contract", "text", { text: htmlString });

                    dom.patch();
                  }

                  callback(htmlString);
                },
                html
              );
            }

            sb.data.db.obj.getWhere(
              "company_logo",
              { is_primary: "yes", childObjs: 1 },
              function (logoObjs) {
                var totalPrice = 0;
                var shifts = [];
                var pricing;
                var proposal;

                if (obj.proposal) {
                  proposal = obj.proposal;
                  pricing = proposal.pricing;

                  if (proposal.schedule) {
                    shifts = proposal.schedule.shifts;
                  }

                  if (proposal.sections) {
                    if (!_.isArray(proposal.sections)) {
                      sections = proposal.sections;
                    }
                  }
                }

                dom
                  .makeNode("modals", "div", { uiGrid: false })
                  .makeNode("modal", "modal", {
                    size: "large",
                    onClose: function () {
                      if (reloadCallback) {
                        sb.data.db.obj.getById(
                          "",
                          proposal.main_object,
                          function (updatedProject) {
                            reloadCallback(updatedProject);
                          },
                          3
                        );
                      }
                    },
                    onShow: function () {
                      var dom = this.modals.modal.body,
                        modal = this.modals.modal,
                        ui = this.modals.modal.body;

                      ui.makeNode("cont", "div", { css: "ui stackable grid" });

                      ui.cont.makeNode("col1", "column", { w: 6 });
                      ui.cont.makeNode("col2", "column", { w: 10 });

                      ui.cont.col1.makeNode("btns", "div", {
                        css: "ui buttons",
                      });

                      ui.cont.col1.makeNode("btnsBreak", "div", {
                        text: "<br />",
                      });

                      ui.cont.col1.btns
                        .makeNode("download", "div", {
                          tag: "button",
                          text: "Download PDF",
                          css: "ui blue button",
                        })
                        .notify(
                          "click",
                          {
                            type: "proposals-run",
                            data: {
                              run: function (obj) {
                                var dom = this;

                                dom.cont.col1.btns.download.loading();

                                previewEstimate(
                                  {},
                                  function () {
                                    dom.cont.col1.btns.download.loading(false);
                                  },
                                  true
                                );
                              }.bind(dom, obj),
                            },
                          },
                          sb.moduleId
                        );

                      ui.cont.col1.btns
                        .makeNode("save", "button", {
                          text: "Save Changes",
                          css: "pda-btn-green",
                        })
                        .notify(
                          "click",
                          {
                            type: "proposals-run",
                            data: {
                              run: function (dom) {
                                var modal = this;

                                ui.cont.col1.btns.save.loading(true);

                                sb.data.db.obj.update(
                                  "proposals",
                                  { id: proposal.id, sections: sections },
                                  function (saved) {
                                    modal.hide(function () {
                                      ui.cont.col1.btns.save.loading(false);
                                    });
                                  },
                                  1
                                );
                              }.bind(modal, dom),
                            },
                          },
                          sb.moduleId
                        );

                      if (settings.require_approval == "no") {
                        ui.cont.col1.btns
                          .makeNode("saveClient", "button", {
                            text: "Save for client",
                            css: "pda-btn-green",
                          })
                          .notify(
                            "click",
                            {
                              type: "proposals-run",
                              data: {
                                run: function (dom) {
                                  var modal = this;

                                  ui.cont.col1.btns.saveClient.loading(true);

                                  var newProposal = {
                                    name:
                                      "New proposal " +
                                      moment()
                                        .local()
                                        .format("MM/DD/YY - h:mm A"),
                                    status: "Editing",
                                    main_object: proposal.main_object,
                                  };

                                  sb.data.db.obj.update(
                                    "proposals",
                                    {
                                      id: proposal.id,
                                      sections: sections,
                                      status: "Approved",
                                    },
                                    function (updated) {
                                      sb.data.db.obj.create(
                                        "proposals",
                                        newProposal,
                                        function (newProposal) {
                                          sb.data.db.obj.update(
                                            newProposal.main_object
                                              .object_bp_type,
                                            {
                                              id: proposal.main_object,
                                              proposal: newProposal.id,
                                            },
                                            function (updatedProject) {
                                              if (obj.status == "Editing") {
                                                sb.data.db.obj.update(
                                                  "proposals",
                                                  {
                                                    id: obj.id,
                                                    status: "Proposal",
                                                  },
                                                  function (updatedProp) {
                                                    modal.hide();
                                                  }
                                                );
                                              } else {
                                                modal.hide();
                                              }
                                            },
                                            3
                                          );
                                        },
                                        2
                                      );
                                    }
                                  );
                                }.bind(modal, dom),
                              },
                            },
                            sb.moduleId
                          );
                      }

                      ui.cont.col2.makeNode("loading", "div", {});
                      ui.cont.col2.makeNode("cont", "div", {
                        css: "ui raised basic segment",
                      });

                      ui.cont.col1.makeNode("title", "div", {
                        css: "ui medium header",
                        text: "Select things to include in this proposal.",
                      });

                      // LOGO
                      ui.cont.col1.makeNode("logoOpt", "div", {
                        css: "ui toggle checkbox",
                        text: '<input type="checkbox" name="logo">',
                      });
                      ui.cont.col1.logoOpt.makeNode("label", "div", {
                        tag: "label",
                        text: "Company Logo",
                      });

                      ui.cont.col1.makeNode("break0", "div", {
                        text: "<br />",
                      });

                      // PROJECT INFO
                      ui.cont.col1.makeNode("projectInfo", "div", {
                        css: "ui toggle checkbox",
                        text: '<input type="checkbox" name="logo">',
                      });
                      ui.cont.col1.projectInfo.makeNode("label", "div", {
                        tag: "label",
                        text: "Project Info",
                      });
                      ui.cont.col1.makeNode("break8", "div", {
                        text: "<br />",
                      });

                      // MENU ITEMS
                      ui.cont.col1.makeNode("itemsOpt", "div", {
                        css: "ui toggle checkbox",
                        text: '<input type="checkbox" name="items">',
                      });
                      ui.cont.col1.itemsOpt.makeNode("label", "div", {
                        tag: "label",
                        text: "Item List",
                      });
                      ui.cont.col1
                        .makeNode("itemCatCont", "div", {})
                        .makeNode("cont", "div", {
                          css: "ui basic padded segment",
                        })
                        .makeNode("title", "div", {
                          text: "Item Categories",
                          css: "ui tiny header",
                        });

                      ui.cont.col1.itemCatCont.cont.makeNode(
                        "lineItems",
                        "div",
                        {
                          css: "ui toggle checkbox",
                          text: '<input type="checkbox" name="items">',
                        }
                      );
                      ui.cont.col1.itemCatCont.cont.lineItems.makeNode(
                        "label",
                        "div",
                        { tag: "label", text: "Line Item" }
                      );

                      ui.cont.col1.makeNode("break2", "div", {
                        text: "<br />",
                      });

                      // MENU ITEMS (before discounts)
                      ui.cont.col1.makeNode("itemsOptBeforeDiscounts", "div", {
                        css: "ui toggle checkbox",
                        text: '<input type="checkbox" name="items">',
                      });
                      ui.cont.col1.itemsOptBeforeDiscounts.makeNode(
                        "label",
                        "div",
                        { tag: "label", text: "Item List (before discounts)" }
                      );
                      ui.cont.col1
                        .makeNode("itemCatContBeforeDiscounts", "div", {})
                        .makeNode("cont", "div", {
                          css: "ui basic padded segment",
                        })
                        .makeNode("title", "div", {
                          text: "Item Categories",
                          css: "ui tiny header",
                        });

                      ui.cont.col1.itemCatContBeforeDiscounts.cont.makeNode(
                        "lineItems",
                        "div",
                        {
                          css: "ui toggle checkbox",
                          text: '<input type="checkbox" name="items">',
                        }
                      );
                      ui.cont.col1.itemCatContBeforeDiscounts.cont.lineItems.makeNode(
                        "label",
                        "div",
                        { tag: "label", text: "Line Item" }
                      );

                      ui.cont.col1.makeNode("break2-1", "div", {
                        text: "<br />",
                      });

                      // PRICING OPTIONS
                      /*
								ui.cont.col1.makeNode('pricingOpt', 'div', {css:'ui toggle checkbox', text:'<input type="checkbox" name="pricing">'});
								ui.cont.col1.pricingOpt.makeNode('label', 'div', {tag:'label', text:'Pricing Breakdown'});	
								ui.cont.col1.makeNode('pricingCatCont', 'div', {});
	*/

                      if (shifts.length > 0) {
                        ui.cont.col1.makeNode("break3", "div", {
                          text: "<br />",
                        });

                        ui.cont.col1.makeNode("shiftsOpt", "div", {
                          css: "ui toggle checkbox",
                          text: '<input type="checkbox" name="pricing">',
                        });
                        ui.cont.col1.shiftsOpt.makeNode("label", "div", {
                          tag: "label",
                          text: "Labor",
                        });
                      }

                      ui.cont.col1.makeNode("break4", "div", {
                        text: "<br />",
                      });

                      ui.cont.col1.makeNode("invoiceOpt", "div", {
                        css: "ui toggle checkbox",
                        text: '<input type="checkbox" name="invoices">',
                      });
                      ui.cont.col1.invoiceOpt.makeNode("label", "div", {
                        tag: "label",
                        text: "Invoice List",
                      });

                      ui.cont.col1.makeNode("break5", "div", {
                        text: "<br />",
                      });

                      // CONTRACT
                      ui.cont.col1.makeNode("contractOpt", "div", {
                        css: "ui toggle checkbox",
                        text: '<input type="checkbox" name="contract">',
                      });
                      ui.cont.col1.contractOpt.makeNode("label", "div", {
                        tag: "label",
                        text: "Contract",
                      });

                      ui.cont.col1.makeNode("break1", "div", {
                        text: "<br />",
                      });

                      ui.cont.col1.makeNode("templateOpt", "div", {
                        css: "ui toggle checkbox",
                        text: '<input type="checkbox" name="template">',
                      });
                      ui.cont.col1.templateOpt.makeNode("label", "div", {
                        tag: "label",
                        text: "Is Template",
                      });
                      ui.cont.col1.makeNode("break6", "div", {
                        text: "<br />",
                      });

                      // COMMENTS
                      ui.cont.col1.makeNode("comments", "div", {
                        css: "ui toggle checkbox",
                        text: '<input type="checkbox" name="logo">',
                      });
                      ui.cont.col1.comments.makeNode("label", "div", {
                        tag: "label",
                        text: "Comments",
                      });
                      ui.cont.col1.makeNode("break7", "div", {
                        text: "<br />",
                      });

                      delete dom.loader;

                      ui.cont.col2.cont.makeNode("loader", "div", {});
                      ui.cont.col2.cont.loader.makeNode("text", "text", {
                        text: "Building proposal preview...",
                        css: "text-center",
                      });
                      ui.cont.col2.cont.loader.makeNode("loader", "loader", {});

                      ui.cont.col1.makeNode("tagBreak", "div", {
                        text: "<br />",
                      });

                      ui.cont.col1.makeNode("tags", "div", {});

                      ui.patch();

                      // tags comp
                      if (sb.sys.state.components.tags) {
                        // remove old tags comp instance
                        if (components.tags) {
                          components.tags.destroy();
                          delete components.tags;
                        }

                        components.tags = sb.createComponent("tags");
                        components.tags.notify({
                          type: "object-tag-view",
                          data: {
                            domObj: ui.cont.col1.tags,
                            objectType: "proposals",
                            objectId: obj.id,
                          },
                        });
                      }

                      _.each(categories, function (cat) {
                        /*
									$(ui.cont.col1.itemCatCont.cont['cat-'+cat.id].selector).checkbox('check');
									$(ui.cont.col1.itemCatCont.cont['cat-'+cat.id].selector).checkbox({
										onChecked:function(){
											
											ui.cont.col2.loading.makeNode('load', 'div', {css:'ui basic segment'})
												.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
													.makeNode('load', 'div', {css:'ui loader'});
												
											ui.cont.col2.loading.patch();
											
											sections.itemListCategories[cat.id] = true;
											
											previewEstimate(dom.cont.col2.cont, function(done){
												
												ui.cont.col2.loading.empty();								
												ui.cont.col2.loading.patch();
												
											});
											
										},
										onUnchecked:function(){
											
											ui.cont.col2.loading.makeNode('load', 'div', {css:'ui basic segment'})
												.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
													.makeNode('load', 'div', {css:'ui loader'});
												
											ui.cont.col2.loading.patch();
											
											sections.itemListCategories[cat.id] = false;
											
											previewEstimate(dom.cont.col2.cont, function(done){
												
												ui.cont.col2.loading.empty();								
												ui.cont.col2.loading.patch();
												
											});
											
										}
									});
			*/
                      });

                      if (!logoObjs) {
                        $(ui.cont.col1.logoOpt.selector).checkbox("uncheck");
                        ui.cont.col1.logoOpt.css("ui toggle checkbox disabled");
                      } else {
                        if (logoObjs.length > 0) {
                          if (!sections.logo) {
                            sections.logo = "Yes";
                          }

                          if (sections.logo == "Yes") {
                            $(ui.cont.col1.logoOpt.selector).checkbox("check");
                          }

                          $(ui.cont.col1.logoOpt.selector).checkbox({
                            onChecked: function () {
                              ui.cont.col2.loading
                                .makeNode("load", "div", {
                                  css: "ui basic segment",
                                })
                                .makeNode("loading", "div", {
                                  css: "ui active inverted dimmer",
                                })
                                .makeNode("load", "div", { css: "ui loader" });

                              ui.cont.col2.loading.patch();

                              sections.logo = "Yes";

                              previewEstimate(
                                dom.cont.col2.cont,
                                function (done) {
                                  ui.cont.col2.loading.empty();

                                  ui.cont.col2.loading.patch();
                                }
                              );
                            },
                            onUnchecked: function () {
                              ui.cont.col2.loading
                                .makeNode("load", "div", {
                                  css: "ui basic segment",
                                })
                                .makeNode("loading", "div", {
                                  css: "ui active inverted dimmer",
                                })
                                .makeNode("load", "div", { css: "ui loader" });

                              ui.cont.col2.loading.patch();

                              sections.logo = "No";

                              previewEstimate(
                                dom.cont.col2.cont,
                                function (done) {
                                  ui.cont.col2.loading.empty();

                                  ui.cont.col2.loading.patch();
                                }
                              );
                            },
                          });
                        } else {
                          $(ui.cont.col1.logoOpt.selector).checkbox("uncheck");
                          ui.cont.col1.logoOpt.css(
                            "ui toggle checkbox disabled"
                          );
                        }
                      }

                      if (sections.contract == "Yes") {
                        $(ui.cont.col1.contractOpt.selector).checkbox("check");
                      }

                      $(ui.cont.col1.contractOpt.selector).checkbox({
                        onChecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.contract = "Yes";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                        onUnchecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.contract = "No";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                      });

                      $(ui.cont.col1.contractOpt.selector).checkbox({
                        onChecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.contract = "Yes";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                        onUnchecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.contract = "No";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                      });

                      if (isTemplate == 1) {
                        $(ui.cont.col1.templateOpt.selector).checkbox("check");
                      }
                      $(ui.cont.col1.templateOpt.selector).checkbox({
                        onChecked: function () {
                          isTemplate = 1;
                        },
                        onUnchecked: function () {
                          isTemplate = 0;
                        },
                      });

                      // PROJECT INFO

                      if (sections.projectInfo == "Yes") {
                        $(ui.cont.col1.projectInfo.selector).checkbox("check");
                      }

                      $(ui.cont.col1.projectInfo.selector).checkbox({
                        onChecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.projectInfo = "Yes";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();
                            ui.cont.col2.loading.patch();
                          });
                        },
                        onUnchecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.projectInfo = "No";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();
                            ui.cont.col2.loading.patch();
                          });
                        },
                      });

                      // MENU ITEMS
                      if (sections.itemList == "Yes") {
                        $(ui.cont.col1.itemsOpt.selector).checkbox("check");
                      }
                      $(ui.cont.col1.itemsOpt.selector).checkbox({
                        onChecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.itemList = "Yes";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            /*
											ui.cont.col1.itemCatCont.makeNode('cont', 'div', {css:'ui basic segment'})
												.makeNode('title', 'div', {text:'Item Categories', css:'ui tiny header'});
											ui.cont.col1.itemCatCont.cont.makeNode('lineItems', 'div', {css:'ui toggle checkbox', text:'<input type="checkbox" name="items">'});
											ui.cont.col1.itemCatCont.cont.lineItems.makeNode('label', 'div', {tag:'label', text:'Line Item'});
											ui.cont.col1.itemCatCont.cont.makeNode('lineItemsBreak', 'div', {text:'<br />'});
											ui.cont.col1.itemCatCont.patch();
			*/

                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                        onUnchecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.itemList = "No";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            /*
											ui.cont.col1.itemCatCont.empty();
											ui.cont.col1.itemCatCont.patch();
			*/

                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                      });
                      if (sections.lineItem == "Yes") {
                        $(
                          ui.cont.col1.itemCatCont.cont.lineItems.selector
                        ).checkbox("check");
                      }
                      $(
                        ui.cont.col1.itemCatCont.cont.lineItems.selector
                      ).checkbox({
                        onChecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.itemList = "Yes";
                          sections.lineItem = "Yes";

                          $(ui.cont.col1.itemsOpt.selector).checkbox("check");

                          /*
										previewEstimate(dom.cont.col2.cont, function(done){
											
											ui.cont.col1.itemCatCont.makeNode('cont', 'div', {css:'ui basic segment'})
												.makeNode('title', 'div', {text:'Line Item', css:'ui tiny header'});
											ui.cont.col1.itemCatCont.patch();
											
											ui.cont.col2.loading.empty();
											
											ui.cont.col2.loading.patch();
											
										});
			*/
                        },
                        onUnchecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.lineItem = "No";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            /*
											ui.cont.col1.itemCatCont.empty();
											ui.cont.col1.itemCatCont.patch();
			*/

                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                      });

                      // MENU ITEMS (before discounts)
                      if (sections.lineItem == "Yes") {
                        $(
                          ui.cont.col1.itemCatCont.cont.lineItems.selector
                        ).checkbox("check");
                      }
                      $(ui.cont.col1.itemsOptBeforeDiscounts.selector).checkbox(
                        "check"
                      );
                      $(ui.cont.col1.itemsOptBeforeDiscounts.selector).checkbox(
                        {
                          onChecked: function () {
                            ui.cont.col2.loading
                              .makeNode("load", "div", {
                                css: "ui basic segment",
                              })
                              .makeNode("loading", "div", {
                                css: "ui active inverted dimmer",
                              })
                              .makeNode("load", "div", { css: "ui loader" });

                            ui.cont.col2.loading.patch();

                            sections.itemListBeforeDiscounts = "Yes";

                            previewEstimate(
                              dom.cont.col2.cont,
                              function (done) {
                                /*
											ui.cont.col1.itemCatCont.makeNode('cont', 'div', {css:'ui basic segment'})
												.makeNode('title', 'div', {text:'Item Categories', css:'ui tiny header'});
											ui.cont.col1.itemCatCont.cont.makeNode('lineItems', 'div', {css:'ui toggle checkbox', text:'<input type="checkbox" name="items">'});
											ui.cont.col1.itemCatCont.cont.lineItems.makeNode('label', 'div', {tag:'label', text:'Line Item'});
											ui.cont.col1.itemCatCont.cont.makeNode('lineItemsBreak', 'div', {text:'<br />'});
											ui.cont.col1.itemCatCont.patch();
			*/

                                ui.cont.col2.loading.empty();

                                ui.cont.col2.loading.patch();
                              }
                            );
                          },
                          onUnchecked: function () {
                            ui.cont.col2.loading
                              .makeNode("load", "div", {
                                css: "ui basic segment",
                              })
                              .makeNode("loading", "div", {
                                css: "ui active inverted dimmer",
                              })
                              .makeNode("load", "div", { css: "ui loader" });

                            ui.cont.col2.loading.patch();

                            sections.itemListBeforeDiscounts = "No";

                            previewEstimate(
                              dom.cont.col2.cont,
                              function (done) {
                                /*
											ui.cont.col1.itemCatCont.empty();
											ui.cont.col1.itemCatCont.patch();
			*/

                                ui.cont.col2.loading.empty();

                                ui.cont.col2.loading.patch();
                              }
                            );
                          },
                        }
                      );
                      //$(ui.cont.col1.itemCatContBeforeDiscounts.cont.lineItems.selector).checkbox('check');
                      $(
                        ui.cont.col1.itemCatContBeforeDiscounts.cont.lineItems
                          .selector
                      ).checkbox({
                        onChecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.itemListBeforeDiscounts = "Yes";
                          sections.lineItemBeforeDiscounts = "Yes";

                          $(ui.cont.col1.itemsOpt.selector).checkbox("check");

                          /*
										previewEstimate(dom.cont.col2.cont, function(done){
											
											ui.cont.col1.itemCatCont.makeNode('cont', 'div', {css:'ui basic segment'})
												.makeNode('title', 'div', {text:'Line Item', css:'ui tiny header'});
											ui.cont.col1.itemCatCont.patch();
											
											ui.cont.col2.loading.empty();
											
											ui.cont.col2.loading.patch();
											
										});
			*/
                        },
                        onUnchecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.lineItemBeforeDiscounts = "No";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            /*
											ui.cont.col1.itemCatCont.empty();
											ui.cont.col1.itemCatCont.patch();
			*/

                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                      });

                      /*
								$(ui.cont.col1.pricingOpt.selector).checkbox({
									onChecked:function(){
										
										ui.cont.col2.loading.makeNode('load', 'div', {css:'ui basic segment'})
											.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
												.makeNode('load', 'div', {css:'ui loader'});
											
										ui.cont.col2.loading.patch();
										
										sections.pricingTableBeforeDiscounts = 'Yes';
																	
										previewEstimate(dom.cont.col2.cont, function(done){
																					
											ui.cont.col2.loading.empty();
											ui.cont.col2.loading.patch();
											
										});
										
									},
									onUnchecked:function(){
										
										ui.cont.col2.loading.makeNode('load', 'div', {css:'ui basic segment'})
											.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
												.makeNode('load', 'div', {css:'ui loader'});
											
										ui.cont.col2.loading.patch();
										
										sections.pricingTableBeforeDiscounts = 'No';
										
										previewEstimate(dom.cont.col2.cont, function(done){
																					
											ui.cont.col2.loading.empty();
											
											ui.cont.col2.loading.patch();
											
										});
										
									}
								});
	*/

                      // COMMENTS
                      if (sections.comments == "Yes") {
                        $(ui.cont.col1.comments.selector).checkbox("check");
                      }

                      $(ui.cont.col1.comments.selector).checkbox({
                        onChecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.comments = "Yes";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();
                            ui.cont.col2.loading.patch();
                          });
                        },
                        onUnchecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.comments = "No";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();
                            ui.cont.col2.loading.patch();
                          });
                        },
                      });

                      if (shifts.length > 0) {
                        $(ui.cont.col1.shiftsOpt.selector).checkbox("check");
                        $(ui.cont.col1.shiftsOpt.selector).checkbox({
                          onChecked: function () {
                            ui.cont.col2.loading
                              .makeNode("load", "div", {
                                css: "ui basic segment",
                              })
                              .makeNode("loading", "div", {
                                css: "ui active inverted dimmer",
                              })
                              .makeNode("load", "div", { css: "ui loader" });

                            ui.cont.col2.loading.patch();

                            sections.labor = true;

                            previewEstimate(
                              dom.cont.col2.cont,
                              function (done) {
                                ui.cont.col1.itemCatCont
                                  .makeNode("cont", "div", {
                                    css: "ui basic segment",
                                  })
                                  .makeNode("title", "div", {
                                    text: "Item Categories",
                                    css: "ui tiny header",
                                  });
                                ui.cont.col1.itemCatCont.patch();

                                ui.cont.col2.loading.empty();

                                ui.cont.col2.loading.patch();
                              }
                            );
                          },
                          onUnchecked: function () {
                            ui.cont.col2.loading
                              .makeNode("load", "div", {
                                css: "ui basic segment",
                              })
                              .makeNode("loading", "div", {
                                css: "ui active inverted dimmer",
                              })
                              .makeNode("load", "div", { css: "ui loader" });

                            ui.cont.col2.loading.patch();

                            sections.labor = false;

                            previewEstimate(
                              dom.cont.col2.cont,
                              function (done) {
                                ui.cont.col1.itemCatCont.empty();
                                ui.cont.col1.itemCatCont.patch();

                                ui.cont.col2.loading.empty();

                                ui.cont.col2.loading.patch();
                              }
                            );
                          },
                        });
                      }

                      if (sections.invoiceTable == "Yes") {
                        $(ui.cont.col1.invoiceOpt.selector).checkbox("check");
                      }
                      $(ui.cont.col1.invoiceOpt.selector).checkbox({
                        onChecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.invoiceTable = "Yes";

                          proposal.sections.invoiceTable = "Yes";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                        onUnchecked: function () {
                          ui.cont.col2.loading
                            .makeNode("load", "div", {
                              css: "ui basic segment",
                            })
                            .makeNode("loading", "div", {
                              css: "ui active inverted dimmer",
                            })
                            .makeNode("load", "div", { css: "ui loader" });

                          ui.cont.col2.loading.patch();

                          sections.invoiceTable = "No";

                          proposal.sections.invoiceTable = "No";

                          previewEstimate(dom.cont.col2.cont, function (done) {
                            ui.cont.col2.loading.empty();

                            ui.cont.col2.loading.patch();
                          });
                        },
                      });

                      previewEstimate(ui.cont.col2.cont, function (done) {
                        proposal.sections = sections;

                        callback(true);
                      });
                    }.bind(dom),
                  });

                dom.patch();

                dom.modals.modal.body.makeNode("cont", "div", {});

                dom.modals.modal.body.patch();

                dom.modals.modal.show();
              }
            );
          }

          function historyViewer(
            dom,
            obj,
            estimateDom,
            pricing,
            categories,
            compact,
            callback
          ) {
            sb.data.db.obj.getWhere(
              "proposals",
              {
                main_object: obj.id,
                childObjs: 1,
              },
              function (proposals) {
                dom.empty();

                if (proposals.length < 1) {
                  dom.makeNode("cont", "div", { css: "ui info message" });

                  dom.cont.makeNode("title", "div", {
                    text: "No proposals yet.",
                    css: "ui huge header",
                  });
                  dom.cont.makeNode("break", "div", { text: "<br /><br />" });
                  dom.cont.makeNode("tip", "div", {
                    text: 'Use the <span class="ui teal label">Contracts</span>, <span class="ui teal label">Line Items</span>, and <span class="ui teal label">Invoices</span> tools, then create the first proposal.',
                    css: "ui header",
                  });

                  dom.patch();
                } else {
                  var signed = _.find(proposals, { status: "Signed" });

                  dom.makeNode("modals", "div", {});

                  dom.makeNode("table", "table", {
                    css: "ui large selectable table",
                    clearCSS: true,
                    columns: {
                      name: "Proposal Name",
                      status: "Status",
                      price: "Price",
                      date_created: "Last Updated",
                      btns: "",
                    },
                  });

                  _.each(
                    _.sortBy(proposals, "date").reverse(),
                    function (history, key) {
                      if (history.name != "") {
                        var sectionString = "",
                          sectionCount = 0;

                        _.each(
                          history.sections,
                          function (sectionOn, sectionName) {
                            if (sectionOn === "Yes") {
                              if (sectionCount > 0) {
                                sectionString += ", " + sectionName;
                              } else {
                                sectionString += sectionName;
                              }

                              sectionCount++;
                            }
                          }
                        );

                        var css = "";
                        var statusString = "Proposal";
                        var labelCSS = "";
                        var currentProposal = false;
                        var currentProposalLabel = "";

                        if (history.status) {
                          statusString = history.status;
                        }

                        if (history.id == obj.proposal.id) {
                          css = "";
                          currentProposal = true;

                          if (
                            history.status == "Proposal" ||
                            history.status == "Editing"
                          ) {
                            currentProposalLabel = " ";
                          }
                        }

                        switch (history.status) {
                          case "Approval Requested":
                            labelCSS = "yellow";
                            statusString =
                              '<i class="ui eye icon"></i> Approval Requested';
                            break;

                          case "Declined":
                            labelCSS = "red";
                            statusString =
                              '<i class="ui times icon"></i> Declined';
                            break;

                          case "Proposal":
                            labelCSS = "yellow";
                            break;

                          case "Accepted":
                            labelCSS = "green";
                            statusString = "Client Accepted";
                            break;

                          case "Editing":
                          case "Active":
                            if (currentProposal === true) {
                              labelCSS = "orange";
                              statusString =
                                '<i class="ui edit icon"></i> Currently Editing';
                            } else {
                              labelCSS = "basic orange";
                            }
                            break;

                          case "Approved":
                            labelCSS = "green";
                            statusString =
                              '<i class="ui check icon"></i> Ready For Client';
                            break;

                          case "Signed":
                            labelCSS = "green";
                        }

                        dom.table.makeRow(
                          "history-" + key,
                          [
                            history.name + currentProposalLabel,
                            '<div class="ui ' +
                              labelCSS +
                              ' label">' +
                              statusString +
                              "</div>",
                            "$" +
                              (
                                _.reduce(
                                  Object.values(history.pricing),
                                  function (price, memo) {
                                    return +memo + +price;
                                  },
                                  0
                                ) / 100
                              ).formatMoney(),
                            moment(history.last_updated).calendar(),
                            "",
                          ],
                          {
                            css: css,
                          }
                        );

                        if (currentProposal === true) {
                          //dom.table.body['history-'+key].btns.makeNode('text', 'div', {text:'You are currently editing this proposal.', css:'ui small header'});

                          dom.table.body["history-" + key].btns.makeNode(
                            "btns",
                            "div",
                            { css: "ui mini buttons" }
                          );

                          dom.table.body["history-" + key].btns.btns
                            .makeNode("copy", "div", {
                              text: "Options",
                              css: "ui orange button",
                            })
                            .notify(
                              "click",
                              {
                                type: "proposals-run",
                                data: {
                                  run: function (
                                    obj,
                                    estimateDom,
                                    history,
                                    pricing,
                                    categories
                                  ) {
                                    dom.table.body[
                                      "history-" + key
                                    ].btns.btns.copy.loading();

                                    sb.data.db.obj.getById(
                                      obj.object_bp_type,
                                      obj.id,
                                      function (prevObj) {
                                        estimateBuilder(
                                          dom,
                                          prevObj,
                                          pricing,
                                          categories,
                                          sections,
                                          [],
                                          function () {
                                            dom.table.body[
                                              "history-" + key
                                            ].btns.btns.copy.loading(false);
                                          },
                                          function (updated) {
                                            if (callback) {
                                              callback(updated);
                                            }
                                          }
                                        );
                                      },
                                      {
                                        proposal: true,
                                      }
                                    );
                                  }.bind(
                                    dom,
                                    obj,
                                    estimateDom,
                                    history,
                                    pricing,
                                    categories
                                  ),
                                },
                              },
                              sb.moduleId
                            );
                        }

                        if (currentProposal === false) {
                          dom.table.body["history-" + key].btns
                            .makeNode("btns", "div", { css: "ui mini buttons" })
                            .makeNode("view", "button", {
                              text: "View",
                              css: "pda-btn-green",
                            })
                            .notify(
                              "click",
                              {
                                type: "proposals-run",
                                data: {
                                  run: function (
                                    obj,
                                    estimateDom,
                                    history,
                                    pricing,
                                    categories
                                  ) {
                                    dom.table.body[
                                      "history-" + key
                                    ].btns.btns.view.loading();

                                    displayHistoryItem(
                                      this,
                                      history,
                                      obj,
                                      pricing,
                                      categories,
                                      function (done) {
                                        dom.table.body[
                                          "history-" + key
                                        ].btns.btns.view.loading(false);
                                      },
                                      false,
                                      function (updated) {
                                        if (callback) {
                                          callback(updated);
                                        }
                                      }
                                    );
                                  }.bind(
                                    dom,
                                    obj,
                                    estimateDom,
                                    history,
                                    pricing,
                                    categories
                                  ),
                                },
                              },
                              sb.moduleId
                            );

                          if (
                            history.status == "Accepted" ||
                            history.status == "Signed"
                          ) {
                            dom.table.body["history-" + key].btns.btns
                              .makeNode("copy", "button", {
                                text: "Update Proposal",
                                css: "pda-btn-blue",
                              })
                              .notify(
                                "click",
                                {
                                  type: "proposals-run",
                                  data: {
                                    run: function (
                                      obj,
                                      estimateDom,
                                      historyItem,
                                      pricing,
                                      categories
                                    ) {
                                      dom.table.body[
                                        "history-" + key
                                      ].btns.btns.copy.loading();

                                      sb.data.db.obj.getById(
                                        "proposals",
                                        historyItem.id,
                                        function (propObj) {
                                          obj.proposal = propObj;

                                          createASnapshot(
                                            obj,
                                            pricing,
                                            function () {
                                              sb.data.db.obj.getById(
                                                "object",
                                                obj.id,
                                                function (obj) {
                                                  historyViewer(
                                                    dom,
                                                    obj,
                                                    estimateDom,
                                                    pricing,
                                                    categories,
                                                    compact
                                                  );
                                                },
                                                2
                                              );
                                            },
                                            historyItem.name + " Copy"
                                          );
                                        },
                                        2
                                      );
                                    }.bind(
                                      dom,
                                      obj,
                                      estimateDom,
                                      history,
                                      pricing,
                                      categories
                                    ),
                                  },
                                },
                                sb.moduleId
                              );
                          }
                        }

                        if (
                          obj.status != "Accepted" &&
                          history.status != "Accepted" &&
                          obj.status != "Signed" &&
                          obj.status != "Paid" &&
                          obj.status != "Completed"
                        ) {
                          dom.table.body["history-" + key].btns.btns
                            .makeNode("name", "button", {
                              text: "Name",
                              css: "pda-btn-orange",
                            })
                            .notify(
                              "click",
                              {
                                type: "proposals-run",
                                data: {
                                  run: function (
                                    obj,
                                    estimateDom,
                                    historyItem,
                                    pricing,
                                    categories
                                  ) {
                                    swal(
                                      {
                                        title: "Change the name",
                                        text: "",
                                        type: "input",
                                        inputValue: historyItem.name,
                                        showCancelButton: true,
                                        closeOnConfirm: false,
                                        showLoaderOnConfirm: true,
                                      },
                                      function (name) {
                                        if (name !== false) {
                                          if (name === "") {
                                            swal.showInputError(
                                              "You need to write something!"
                                            );
                                            return false;
                                          }

                                          historyItem.name = name;

                                          obj.history = _.map(
                                            obj.history,
                                            function (his) {
                                              if (
                                                his.contract ==
                                                historyItem.contract
                                              ) {
                                                his.name = name;
                                              }

                                              return his;
                                            },
                                            []
                                          );

                                          sb.data.db.obj.update(
                                            "proposals",
                                            historyItem,
                                            function (updated) {
                                              swal.close();

                                              historyViewer(
                                                mainDom.tools.cont.history,
                                                obj,
                                                mainDom.estimate.cont,
                                                pricing,
                                                categories
                                              );
                                            },
                                            2
                                          );
                                        }
                                      }
                                    );
                                  }.bind(
                                    dom,
                                    obj,
                                    estimateDom,
                                    history,
                                    pricing,
                                    categories
                                  ),
                                },
                              },
                              sb.moduleId
                            );

                          switch (history.status) {
                            case "Approved":
                            case "Proposal":
                              if (currentProposal !== true) {
                                dom.table.body["history-" + key].btns.btns
                                  .makeNode("delete", "button", {
                                    text: "Delete",
                                    css: "pda-btn-red",
                                  })
                                  .notify(
                                    "click",
                                    {
                                      type: "proposals-run",
                                      data: {
                                        run: function (
                                          obj,
                                          estimateDom,
                                          historyItem,
                                          pricing,
                                          categories
                                        ) {
                                          sb.dom.alerts.ask(
                                            {
                                              title: "Are you sure?",
                                              text: "This cannot be undone.",
                                            },
                                            function (resp) {
                                              if (resp) {
                                                swal.disableButtons();

                                                obj.history = _.reject(
                                                  obj.history,
                                                  function (item) {
                                                    return (
                                                      +item.contract ===
                                                      +historyItem.contract
                                                    );
                                                  }
                                                );

                                                sb.data.db.obj.erase(
                                                  "proposals",
                                                  history.id,
                                                  function (updated) {
                                                    if (callback) {
                                                      historyViewer(
                                                        dom,
                                                        obj,
                                                        estimateDom,
                                                        pricing,
                                                        categories,
                                                        compact,
                                                        callback
                                                      );
                                                    } else {
                                                      sb.notify({
                                                        type: "app-redraw",
                                                        data: {},
                                                      });
                                                    }

                                                    swal.close();
                                                  },
                                                  2
                                                );
                                              }
                                            }
                                          );
                                        }.bind(
                                          dom,
                                          obj,
                                          estimateDom,
                                          history,
                                          pricing,
                                          categories
                                        ),
                                      },
                                    },
                                    sb.moduleId
                                  );
                              }

                              break;
                          }
                        }

                        if (settings.require_approval == "yes") {
                          if (
                            history.status != "Approval Requested" &&
                            history.status != "Approved"
                          ) {
                            dom.table.body["history-" + key].btns.btns
                              .makeNode("requestApproval", "div", {
                                css: "ui teal button",
                                text: "Request Approval",
                              })
                              .notify(
                                "click",
                                {
                                  type: "proposals-run",
                                  data: {
                                    run: function (dom, proposal) {
                                      dom.modals.makeNode("approval", "modal", {
                                        onShow: function () {
                                          dom.modals.approval.body.makeNode(
                                            "title",
                                            "div",
                                            {
                                              css: "ui huge header",
                                              text: "Write a note to the admins",
                                            }
                                          );

                                          dom.modals.approval.body.makeNode(
                                            "form",
                                            "form",
                                            {
                                              note: {
                                                name: "note",
                                                label: "Enter your notes here",
                                                type: "textbox",
                                                row: 5,
                                              },
                                            }
                                          );

                                          dom.modals.approval.body.makeNode(
                                            "formBreak",
                                            "div",
                                            { text: "<br />" }
                                          );

                                          dom.modals.approval.body.makeNode(
                                            "note",
                                            "div",
                                            {
                                              css: "ui compact info message",
                                              text: "This proposal will be locked and a new proposal will be created.",
                                            }
                                          );

                                          dom.modals.approval.body.makeNode(
                                            "tipbreak",
                                            "div",
                                            { text: "<br />" }
                                          );

                                          dom.modals.approval.body
                                            .makeNode("submit", "div", {
                                              css: "ui green button",
                                              text: "Ask for approval",
                                            })
                                            .notify(
                                              "click",
                                              {
                                                type: "proposals-run",
                                                data: {
                                                  run: function (
                                                    dom,
                                                    obj,
                                                    proposal
                                                  ) {
                                                    dom.modals.approval.body.submit.loading();

                                                    sb.data.db.obj.getById(
                                                      "users",
                                                      sb.data.cookie.get("uid"),
                                                      function (user) {
                                                        var note = "";
                                                        note +=
                                                          "<b>Approval requested for proposal " +
                                                          workOrder.name +
                                                          " by " +
                                                          user.fname +
                                                          " " +
                                                          user.lname +
                                                          ".</b><br /><br />Note from " +
                                                          user.fname +
                                                          ": ";
                                                        note +=
                                                          dom.modals.approval.body.form.process()
                                                            .fields.note.value;

                                                        sb.data.db.obj.update(
                                                          proposal.object_bp_type,
                                                          {
                                                            id: proposal.id,
                                                            status:
                                                              "Approval Requested",
                                                          },
                                                          function (updated) {
                                                            checkSettingsObject(
                                                              function (
                                                                settings
                                                              ) {
                                                                //sb.data.db.obj.create('proposals', newProposal, function(newProposal){

                                                                sb.data.db.obj.createFromTemplate(
                                                                  proposal.id,
                                                                  function (
                                                                    newProposal
                                                                  ) {
                                                                    sb.data.db.obj.update(
                                                                      obj.object_bp_type,
                                                                      {
                                                                        id: obj.id,
                                                                        proposal:
                                                                          newProposal.id,
                                                                      },
                                                                      function (
                                                                        updatedProject
                                                                      ) {
                                                                        sb.data.db.obj.getWhere(
                                                                          "users",
                                                                          {
                                                                            id: {
                                                                              type: "or",
                                                                              values:
                                                                                settings.approval_admin_users,
                                                                            },
                                                                          },
                                                                          function (
                                                                            users
                                                                          ) {
                                                                            sb.data.db.obj.getWhere(
                                                                              "proposals",
                                                                              {
                                                                                main_object:
                                                                                  workOrder.id,
                                                                              },
                                                                              function (
                                                                                allProps
                                                                              ) {
                                                                                var notesToCreate =
                                                                                  [];
                                                                                _.each(
                                                                                  allProps,
                                                                                  function (
                                                                                    prop
                                                                                  ) {
                                                                                    notesToCreate.push(
                                                                                      {
                                                                                        type_id:
                                                                                          prop.id,
                                                                                        type: "proposals",
                                                                                        note:
                                                                                          "<br />" +
                                                                                          note,
                                                                                        note_type: 0,
                                                                                        author:
                                                                                          +sb
                                                                                            .data
                                                                                            .cookie
                                                                                            .userId,
                                                                                        object_bp_type:
                                                                                          "notes",
                                                                                      }
                                                                                    );
                                                                                  }
                                                                                );

                                                                                sb.data.db.obj.create(
                                                                                  "notes",
                                                                                  notesToCreate,
                                                                                  function (
                                                                                    newNote
                                                                                  ) {
                                                                                    sb.comm.sendEmail(
                                                                                      {
                                                                                        to: _.pluck(
                                                                                          users,
                                                                                          "email"
                                                                                        ),
                                                                                        from: appConfig.emailFrom,
                                                                                        subject:
                                                                                          "Proposal Approval Request",
                                                                                        mergevars:
                                                                                          {
                                                                                            TITLE:
                                                                                              workOrder.name,
                                                                                            BODY:
                                                                                              note +
                                                                                              '<br /><br /> <a href="' +
                                                                                              window
                                                                                                .location
                                                                                                .href +
                                                                                              '">View Online</a>',
                                                                                          },
                                                                                        emailtags:
                                                                                          [
                                                                                            "Proposal Update",
                                                                                          ],
                                                                                        type: "notes",
                                                                                        typeId:
                                                                                          workOrder.id,
                                                                                      },
                                                                                      function (
                                                                                        sent
                                                                                      ) {
                                                                                        dom.modals.approval.hide();

                                                                                        dom.table.body[
                                                                                          "history-" +
                                                                                            key
                                                                                        ].btns.btns.requestApproval.loading();

                                                                                        sb.notify(
                                                                                          {
                                                                                            type: "app-redraw",
                                                                                            data: {},
                                                                                          }
                                                                                        );
                                                                                      }
                                                                                    );
                                                                                  }
                                                                                );
                                                                              }
                                                                            );
                                                                          }
                                                                        );
                                                                      }
                                                                    );
                                                                  },
                                                                  0,
                                                                  {
                                                                    name:
                                                                      "New proposal " +
                                                                      moment().format(
                                                                        "M/D/YYYY h:mm a"
                                                                      ),
                                                                    status:
                                                                      "Editing",
                                                                  }
                                                                );
                                                              }
                                                            );
                                                          }
                                                        );
                                                      }
                                                    );
                                                  }.bind(
                                                    {},
                                                    dom,
                                                    obj,
                                                    proposal
                                                  ),
                                                },
                                              },
                                              sb.moduleId
                                            );

                                          dom.modals.approval.body.patch();
                                        },
                                      });

                                      dom.modals.patch();

                                      dom.modals.approval.show();
                                    }.bind({}, dom, history),
                                  },
                                },
                                sb.moduleId
                              );
                          }
                        } else {
                        }

                        dom.table.body["history-" + key].btns.btns
                          .makeNode("pdf", "div", {
                            text: "Download PDF",
                            css: "ui blue button",
                          })
                          .notify(
                            "click",
                            {
                              type: "proposals-run",
                              data: {
                                run: function (
                                  obj,
                                  estimateDom,
                                  history,
                                  pricing,
                                  categories
                                ) {
                                  dom.table.body[
                                    "history-" + key
                                  ].btns.btns.pdf.loading();

                                  sb.data.db.obj.getById(
                                    "proposals",
                                    history.id,
                                    function (prevHistory) {
                                      if (_.isArray(prevHistory.sections)) {
                                        prevHistory.sections = sections;
                                      }

                                      viewProposalPDF(
                                        prevHistory,
                                        categories,
                                        prevHistory.sections,
                                        function () {
                                          dom.table.body[
                                            "history-" + key
                                          ].btns.btns.pdf.loading(false);
                                        }
                                      );
                                    }
                                  );
                                }.bind(
                                  dom,
                                  obj,
                                  estimateDom,
                                  history,
                                  pricing,
                                  categories
                                ),
                              },
                            },
                            sb.moduleId
                          );
                      }
                    }
                  );

                  dom.patch();

                  if (buttonDom) {
                    buttonDom.patch();
                  }

                  if (callback) {
                    callback(true);
                  }
                }
              }
            );
          }

          function viewProposalPDF(
            obj,
            categories,
            sections,
            callback,
            htmlOnly
          ) {
            var fullHTMLString = "",
              totalPrice = 0;

            sb.data.db.obj.getById(
              "work_orders",
              obj.main_object,
              function (workorder_obj) {
                sb.data.db.obj.getById(
                  "contracts",
                  obj.contract,
                  function (contract) {
                    sb.data.db.obj.getWhere(
                      "invoices",
                      { related_object: obj.id },
                      function (invoices) {
                        sb.data.db.obj.getWhere(
                          "inventory_menu",
                          { id: obj.menu.id, childObjs: 2 },
                          function (menus) {
                            sb.data.db.obj.getWhere(
                              "shifts",
                              {
                                staff_schedules: +obj.schedule,
                                childObjs: {
                                  service: {
                                    name: true,
                                  },
                                },
                              },
                              function (shifts) {
                                // get menu w/line item prices
                                sb.notify({
                                  type: "get-menu-total-pricing",
                                  data: {
                                    menu: menus[0],
                                    obj: obj,
                                    pricing: function (pricelist) {
                                      sb.notify({
                                        type: "get-menu-line-item-pricing",
                                        data: {
                                          menu: menus[0],
                                          obj: obj,
                                          workorder: workorder_obj,
                                          callback: function (pricedMenu) {
                                            sb.data.db.obj.getWhere(
                                              "company_logo",
                                              {
                                                is_primary: "yes",
                                                childObjs: 1,
                                              },
                                              function (logoObjs) {
                                                function getProposalComments(
                                                  sections,
                                                  callback
                                                ) {
                                                  if (
                                                    sections.comments === "Yes"
                                                  ) {
                                                    sb.data.db.obj.getWhere(
                                                      "notes",
                                                      {
                                                        type_id: obj.id,
                                                        childObjs: {
                                                          note: true,
                                                        },
                                                      },
                                                      function (comments) {
                                                        callback(comments);
                                                      }
                                                    );
                                                  } else {
                                                    callback([]);
                                                  }
                                                }

                                                function getContactInfo(
                                                  sections,
                                                  callback
                                                ) {
                                                  function getMainContact(
                                                    workorderObj,
                                                    callback
                                                  ) {
                                                    var mainContactId = false;
                                                    if (
                                                      workorderObj.main_contact &&
                                                      workorderObj.main_contact
                                                        .id
                                                    ) {
                                                      mainContactId =
                                                        workorderObj.main_contact;
                                                    } else if (
                                                      workorderObj.main_object &&
                                                      workorderObj.main_object
                                                        .main_contact
                                                    ) {
                                                      mainContactId =
                                                        workorderObj.main_object
                                                          .main_contact;
                                                    }
                                                    if (mainContactId) {
                                                      sb.data.db.obj.getById(
                                                        "contacts",
                                                        mainContactId,
                                                        function (mainContact) {
                                                          callback(mainContact);
                                                        },
                                                        3 /*
{
																					fname: 		true
																					, lname: 	true
																					, contact_info: 2
																					, company: {
																						name: true
																					}
																				}
*/
                                                      );
                                                    } else {
                                                      callback(null);
                                                    }
                                                  }

                                                  function getManagers(
                                                    workorderObj,
                                                    callback
                                                  ) {
                                                    if (
                                                      !_.isEmpty(
                                                        workorderObj.managers
                                                      )
                                                    ) {
                                                      return callback(
                                                        workorderObj.managers
                                                      );
                                                    } else if (
                                                      !_.isEmpty(
                                                        workorderObj.main_object
                                                      ) &&
                                                      !_.isEmpty(
                                                        workorderObj.main_object
                                                          .managers
                                                      )
                                                    ) {
                                                      sb.data.db.obj.getById(
                                                        "users",
                                                        workorderObj.main_object
                                                          .managers,
                                                        function (managers) {
                                                          callback(managers);
                                                        }
                                                      );
                                                    } else {
                                                      callback(null);
                                                    }
                                                  }

                                                  if (
                                                    sections.projectInfo ===
                                                    "Yes"
                                                  ) {
                                                    getMainContact(
                                                      workorder_obj,
                                                      function (mainContact) {
                                                        getManagers(
                                                          workorder_obj,
                                                          function (managers) {
                                                            var contactInfo = {
                                                              mainContact:
                                                                mainContact,
                                                              managers:
                                                                managers,
                                                            };

                                                            if (
                                                              !_.isEmpty(
                                                                workorder_obj.type
                                                              )
                                                            ) {
                                                              contactInfo.projectType =
                                                                workorder_obj.type;
                                                              callback(
                                                                contactInfo
                                                              );
                                                            } else {
                                                              sb.data.db.obj.getById(
                                                                "project_types",
                                                                workorder_obj
                                                                  .main_object
                                                                  .type,
                                                                function (
                                                                  type
                                                                ) {
                                                                  contactInfo.projectType =
                                                                    type;
                                                                  callback(
                                                                    contactInfo
                                                                  );
                                                                },
                                                                {
                                                                  name: true,
                                                                }
                                                              );
                                                            }
                                                          }
                                                        );
                                                      }
                                                    );
                                                  } else {
                                                    callback({});
                                                  }
                                                }

                                                function get_pdf_header(
                                                  Workorder,
                                                  menu,
                                                  contactInfo
                                                ) {
                                                  var contactPhoneTxt =
                                                    '<i class="text-muted">Not set</i>';
                                                  var topRightText = "";
                                                  var managerTxt =
                                                    '<i class="text-muted">Not set</i>';
                                                  var workorderObj = Workorder;
                                                  if (
                                                    !_.isEmpty(
                                                      Workorder.main_object
                                                    )
                                                  ) {
                                                    workorderObj =
                                                      Workorder.main_object;
                                                  }

                                                  _.each(
                                                    contactInfo.mainContact
                                                      .contact_info,
                                                    function (info) {
                                                      if (
                                                        info.type &&
                                                        info.type.data_type ===
                                                          "phone"
                                                      ) {
                                                        contactPhoneTxt =
                                                          info.info;
                                                      }
                                                    }
                                                  );

                                                  var companyTxt =
                                                    '<i class="text-muted">Not set</i>';
                                                  if (
                                                    contactInfo.mainContact &&
                                                    contactInfo.mainContact
                                                      .company
                                                  ) {
                                                    companyTxt =
                                                      contactInfo.mainContact
                                                        .company.name;
                                                  }
                                                  if (
                                                    !_.isEmpty(
                                                      contactInfo.managers
                                                    )
                                                  ) {
                                                    _.each(
                                                      contactInfo.managers,
                                                      function (manager) {
                                                        managerTxt =
                                                          manager.fname +
                                                          " " +
                                                          manager.lname;
                                                      }
                                                    );
                                                  }

                                                  var pdfHtml =
                                                    "<br /><br />" +
                                                    "Workorder: " +
                                                    Workorder.name +
                                                    "<br />" +
                                                    "Date: " +
                                                    moment(
                                                      workorderObj.start_date,
                                                      "YYYY-MM-DD HH:mm:ss"
                                                    ).format("lll") +
                                                    "<br />" +
                                                    "Client: " +
                                                    contactInfo.mainContact
                                                      .fname +
                                                    " " +
                                                    contactInfo.mainContact
                                                      .lname +
                                                    ", at " +
                                                    companyTxt +
                                                    "<br />" +
                                                    "Guest count: " +
                                                    workorderObj.head_count +
                                                    "<br />" +
                                                    "Phone: " +
                                                    contactPhoneTxt +
                                                    "<br />" +
                                                    "Workorder #: " +
                                                    Workorder.object_uid +
                                                    "<br />" +
                                                    "Event Manager: " +
                                                    managerTxt +
                                                    "<br />" +
                                                    "Type: " +
                                                    contactInfo.projectType
                                                      .name +
                                                    "<br />" +
                                                    "Date of report: " +
                                                    moment().format("lll") +
                                                    "<br />" +
                                                    "<br /><br />";

                                                  return pdfHtml;
                                                }

                                                getProposalComments(
                                                  sections,
                                                  function (comments) {
                                                    getContactInfo(
                                                      sections,
                                                      function (contactInfo) {
                                                        var logoPath;
                                                        if (logoObjs) {
                                                          if (
                                                            logoObjs.length > 0
                                                          ) {
                                                            logoPath =
                                                              sb.data.files.getURL(
                                                                logoObjs[0]
                                                                  .company_logo
                                                              );
                                                          }
                                                        }

                                                        if (
                                                          sections.logo == "Yes"
                                                        ) {
                                                          if (logoPath) {
                                                            fullHTMLString +=
                                                              '<div style="text-align:center;"><img style="margin:0 auto; display:block; max-height:100px;" src="' +
                                                              logoPath +
                                                              '"></div>';
                                                          }

                                                          fullHTMLString +=
                                                            "<br />";
                                                        }

                                                        if (
                                                          sections.projectInfo ==
                                                          "Yes"
                                                        ) {
                                                          fullHTMLString +=
                                                            get_pdf_header(
                                                              workorder_obj,
                                                              pricedMenu,
                                                              contactInfo
                                                            );
                                                        }

                                                        if (
                                                          sections.itemList ==
                                                          "Yes"
                                                        ) {
                                                          /*
																			if(sections.contract == 'Yes'){
																				fullHTMLString += '<pagebreak />';
																			}
		*/

                                                          if (
                                                            sectionTypes.menu.getHTML(
                                                              pricedMenu,
                                                              shifts,
                                                              sections,
                                                              {
                                                                includeDiscounts: true,
                                                              }
                                                            )
                                                          ) {
                                                            fullHTMLString +=
                                                              sectionTypes.menu.getHTML(
                                                                pricedMenu,
                                                                shifts,
                                                                sections,
                                                                {
                                                                  includeDiscounts: true,
                                                                }
                                                              );
                                                            fullHTMLString +=
                                                              "<br />";
                                                            fullHTMLString +=
                                                              "{{PLEASE SIGN HERE}}";
                                                            fullHTMLString +=
                                                              "<br /><br />";
                                                          }
                                                        }

                                                        if (
                                                          sections.itemListBeforeDiscounts ==
                                                          "Yes"
                                                        ) {
                                                          if (
                                                            sections.contract ==
                                                            "Yes"
                                                          ) {
                                                            fullHTMLString +=
                                                              "<pagebreak />";
                                                          }

                                                          if (
                                                            sectionTypes.menu.getHTML(
                                                              pricedMenu,
                                                              shifts,
                                                              sections,
                                                              {
                                                                includeDiscounts: false,
                                                              }
                                                            )
                                                          ) {
                                                            fullHTMLString +=
                                                              sectionTypes.menu.getHTML(
                                                                pricedMenu,
                                                                shifts,
                                                                sections,
                                                                {
                                                                  includeDiscounts: false,
                                                                }
                                                              );
                                                            fullHTMLString +=
                                                              "<br />";
                                                            fullHTMLString +=
                                                              "{{PLEASE SIGN HERE}}";
                                                            fullHTMLString +=
                                                              "<br /><br />";
                                                          }
                                                        }

                                                        if (
                                                          sections.pricingTable ==
                                                          "Yes"
                                                        ) {
                                                          if (
                                                            sections.itemList ==
                                                            "Yes"
                                                          ) {
                                                            fullHTMLString +=
                                                              "<pagebreak />";
                                                          }

                                                          fullHTMLString +=
                                                            "<h3>Pricing</h3>";

                                                          fullHTMLString +=
                                                            '<table style="width:100%; border-collapse: collapse;">';

                                                          _.each(
                                                            pricelist,
                                                            function (
                                                              price,
                                                              cat
                                                            ) {
                                                              var category =
                                                                _.find(
                                                                  categories,
                                                                  { id: +cat }
                                                                );

                                                              if (category) {
                                                                if (
                                                                  sections.pricingTableCategories
                                                                ) {
                                                                  if (
                                                                    sections
                                                                      .pricingTableCategories[
                                                                      cat.toString()
                                                                    ] == true
                                                                  ) {
                                                                    fullHTMLString +=
                                                                      '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
                                                                      category.name +
                                                                      '</td> <td style="padding:5px;">$' +
                                                                      (
                                                                        price /
                                                                        100
                                                                      ).formatMoney() +
                                                                      "</td> </tr>";

                                                                    totalPrice +=
                                                                      price;
                                                                  }
                                                                } else {
                                                                  fullHTMLString +=
                                                                    '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
                                                                    category.name +
                                                                    '</td> <td style="padding:5px;">$' +
                                                                    (
                                                                      price /
                                                                      100
                                                                    ).formatMoney() +
                                                                    "</td> </tr>";

                                                                  totalPrice +=
                                                                    price;
                                                                }
                                                              }
                                                            }
                                                          );

                                                          if (
                                                            shifts &&
                                                            sections.labor
                                                          ) {
                                                            var services =
                                                              _.pluck(
                                                                shifts,
                                                                "service"
                                                              );
                                                            var totalShifts = 0;
                                                            var shiftsTotal = 0;
                                                            var sectionTotal = 0;
                                                            var total = 0;

                                                            _.each(
                                                              obj.pricing,
                                                              function (
                                                                price,
                                                                serviceId
                                                              ) {
                                                                var service =
                                                                  _.findWhere(
                                                                    services,
                                                                    {
                                                                      id: +serviceId,
                                                                    }
                                                                  );

                                                                if (service) {
                                                                  totalShifts++;
                                                                  shiftsTotal +=
                                                                    +price;
                                                                  totalPrice +=
                                                                    +price;
                                                                }
                                                              }
                                                            );

                                                            fullHTMLString +=
                                                              '<tr style="border:1px solid gray;"> <td style="padding:5px;">Labor</td> <td style="padding:5px;">$' +
                                                              (
                                                                shiftsTotal /
                                                                100
                                                              ).formatMoney() +
                                                              "</td> </tr>";
                                                          }

                                                          fullHTMLString +=
                                                            '<tr style="border:1px solid gray; font-weight:bold;"> <td style="padding:5px; font-weight:bold; font-size:18px;">TOTAL</td> <td style="padding:5px; font-weight:bold; font-size:16px;">$' +
                                                            (
                                                              totalPrice / 100
                                                            ).formatMoney() +
                                                            "</td> </tr>";

                                                          fullHTMLString +=
                                                            "</table>";

                                                          fullHTMLString +=
                                                            "<br /><br />";
                                                          fullHTMLString +=
                                                            "{{PLEASE SIGN HERE}}";
                                                          fullHTMLString +=
                                                            "<br /><br /><br />";
                                                        }

                                                        if (
                                                          sections.invoiceTable ==
                                                          "Yes"
                                                        ) {
                                                          if (
                                                            sections.pricingTable ==
                                                            "Yes"
                                                          ) {
                                                            fullHTMLString +=
                                                              "<pagebreak />";
                                                          }

                                                          fullHTMLString +=
                                                            "<h3>Invoices</h3>";

                                                          fullHTMLString +=
                                                            '<table style="width:100%; border-collapse: collapse;">';

                                                          fullHTMLString +=
                                                            '<tr style="border:1px solid gray; font-size:12px; font-weight:bold;"> <td style="padding:5px;">INVOICE NAME</td> <td style="padding:5px; text-align:right;">DUE DATE</td> <td style="padding:5px; text-align:right;">BALANCE DUE</td> </tr>';

                                                          _.each(
                                                            _.sortBy(
                                                              invoices,
                                                              "due_date"
                                                            ),
                                                            function (inv) {
                                                              fullHTMLString +=
                                                                '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
                                                                inv.name +
                                                                '</td> <td style="padding:5px; text-align:right;">' +
                                                                moment(
                                                                  inv.due_date
                                                                ).format(
                                                                  "M/D/YYYY"
                                                                ) +
                                                                '</td> <td style="padding:5px; text-align:right;">$' +
                                                                (
                                                                  inv.balance /
                                                                  100
                                                                ).formatMoney() +
                                                                "</td> </tr>";
                                                            }
                                                          );

                                                          fullHTMLString +=
                                                            "</table>";

                                                          fullHTMLString +=
                                                            "<br /><br />";
                                                          fullHTMLString +=
                                                            "{{PLEASE SIGN HERE}}";
                                                          fullHTMLString +=
                                                            "<br /><br /><br />";
                                                        }

                                                        if (
                                                          sections.contract ==
                                                          "Yes"
                                                        ) {
                                                          if (contract) {
                                                            fullHTMLString +=
                                                              contract.html_string;
                                                          } else {
                                                            fullHTMLString +=
                                                              "<h3>No contract selected</h3>";
                                                          }

                                                          fullHTMLString +=
                                                            "<br /><br /><br />";
                                                        }

                                                        if (
                                                          sections.comments ==
                                                          "Yes"
                                                        ) {
                                                          fullHTMLString +=
                                                            "<h3><strong>Notes:</strong></h3>";
                                                          _.each(
                                                            comments,
                                                            function (comment) {
                                                              fullHTMLString +=
                                                                comment.note;
                                                            }
                                                          );
                                                        }

                                                        if (!contract) {
                                                          contract = {};
                                                        }

                                                        contract.html_string =
                                                          fullHTMLString;
                                                        contract.related_object =
                                                          obj.main_object;

                                                        createMergedHTML(
                                                          contract,
                                                          objectType,
                                                          function (
                                                            contractHTML
                                                          ) {
                                                            if (
                                                              htmlOnly === true
                                                            ) {
                                                              if (callback) {
                                                                callback(
                                                                  contractHTML
                                                                );
                                                              }
                                                            } else {
                                                              sb.data.makePDF(
                                                                contractHTML,
                                                                "I"
                                                              );

                                                              if (callback) {
                                                                callback(
                                                                  contractHTML
                                                                );
                                                              }
                                                            }
                                                          }
                                                        );
                                                      }
                                                    );
                                                  }
                                                );
                                              }
                                            );
                                          },
                                        },
                                      });
                                    },
                                  },
                                });
                              }
                            );
                          }
                        );
                      }
                    );
                  },
                  1
                );
              },
              {
                main_object: {
                  main_contact: true,
                },
              }
            );
          }

          var stackable = "";
          if (compact) {
            stackable = "";
          }

          //dom.empty();

          dom.makeNode("modals", "div", {});

          dom.makeNode("estimate", "column", { width: 12 });

          dom
            .makeNode("tools", "div", { width: 12 })
            .makeNode("cont", "div", { css: "" });

          dom.tools.cont.makeNode("btns", "buttonGroup", {
            css: "ui " + stackable + " buttons",
          });

          dom.tools.cont.makeNode("btnsBreak", "div", { text: "<br />" });

          switch (obj.status) {
            case "Accepted":
            case "Approval":
              dom.tools.cont.btns.makeNode("create", "div", {
                css: "ui green button",
                text: '<i class="plus icon"></i> Create a new proposal',
              });

              break;

            default:
              dom.tools.cont.btns
                .makeNode("create", "div", {
                  css: "ui green button",
                  text: '<i class="plus icon"></i> Create a new proposal',
                })
                .notify(
                  "click",
                  {
                    type: "proposals-run",
                    data: {
                      run: function (obj, pricing, sections) {
                        sb.dom.alerts.ask(
                          {
                            title: "Are you sure?",
                            text: "The current proposal will be saved. You will still be able to view and edit it when you want.",
                            primaryButtonText: "Continue",
                            cancelButtonText: "Go Back",
                          },
                          function (response) {
                            if (response) {
                              swal.disableButtons();

                              var newProposal = {
                                name:
                                  "New proposal " +
                                  moment().local().format("MM/DD/YY - h:mm A"),
                                status: "Editing",
                                main_object: obj.id,
                              };

                              sb.data.db.obj.create(
                                "proposals",
                                newProposal,
                                function (newProposal) {
                                  sb.data.db.obj.update(
                                    obj.object_bp_type,
                                    { id: obj.id, proposal: newProposal.id },
                                    function (updatedProject) {
                                      if (obj.status == "Editing") {
                                        sb.data.db.obj.update(
                                          "proposals",
                                          { id: obj.id, status: "Proposal" },
                                          function (updatedProp) {
                                            if (callback) {
                                              swal.close();
                                              callback(updatedProject);
                                            } else {
                                              window.location.reload();
                                            }
                                          }
                                        );
                                      } else {
                                        if (updateCallback) {
                                          swal.close();
                                          reloadCallbackFunc(updatedProject);
                                        } else {
                                          window.location.reload();
                                        }
                                      }
                                    },
                                    3
                                  );
                                }
                              );
                            }
                          }
                        );

                        var dom = this;
                      }.bind(dom, obj, pricing, sections),
                    },
                  },
                  sb.moduleId
                );

              dom.tools.cont.btns
                .makeNode("template", "button", {
                  css: "pda-btn-blue",
                  text: "Templates",
                })
                .notify(
                  "click",
                  {
                    type: "proposals-run",
                    data: {
                      run: function (dom, workOrder) {
                        dom.modals.makeNode("template", "modal", {
                          onShow: function () {
                            var mbody = dom.modals.template.body;

                            mbody.makeNode("title", "div", {
                              text: "Choose a proposal template",
                              css: "ui huge header",
                            });

                            mbody.makeNode("tableCont", "div", {});

                            mbody.patch();

                            components.templateSelectionTable.notify({
                              type: "show-table",
                              data: {
                                domObj: mbody.tableCont,
                                objectType: "proposals",
                                childObjs: 3,
                                searchObjects: {
                                  name: "Name",
                                  value: "name",
                                },
                                filters: false,
                                download: false,
                                navigation: false,
                                headerButtons: {
                                  reload: {
                                    name: "Reload",
                                    css: "pda-btn-blue",
                                    action: function () {},
                                  },
                                },
                                settings: false,
                                rowSelection: true,
                                multiSelectButtons: {
                                  apply: {
                                    name: "Add template to project",
                                    css: "pda-btn-green",
                                    domType: "none",
                                    action: function (selected) {
                                      function addProposals(
                                        props,
                                        callback,
                                        count,
                                        ret
                                      ) {
                                        if (props.length > 0) {
                                          if (!count) {
                                            var count = 0;
                                            var ret = [];
                                          }

                                          if (props[count]) {
                                            var prop = props[count];
                                            var newProp = {
                                              main_object: workOrder.id,
                                              status: "proposal",
                                            };

                                            sb.data.db.obj.create(
                                              "proposals",
                                              newProp,
                                              function (newProp) {
                                                createContractObject(
                                                  prop,
                                                  newProp.id,
                                                  function (contractObj) {
                                                    createMenuObject(
                                                      prop,
                                                      newProp.id,
                                                      function (menuObj) {
                                                        createStaffScheduleObject(
                                                          prop,
                                                          newProp.id,
                                                          function (
                                                            scheduleObj
                                                          ) {
                                                            createInvoiceObjects(
                                                              prop,
                                                              newProp.id,
                                                              function (
                                                                invoices
                                                              ) {
                                                                if (
                                                                  contractObj
                                                                ) {
                                                                  newProp.contract =
                                                                    contractObj.id;
                                                                }

                                                                if (menuObj) {
                                                                  newProp.menu =
                                                                    menuObj.id;
                                                                }

                                                                if (
                                                                  scheduleObj
                                                                ) {
                                                                  newProp.schedule =
                                                                    scheduleObj.id;
                                                                }

                                                                if (invoices) {
                                                                  newProp.invoices =
                                                                    _.pluck(
                                                                      invoices,
                                                                      "id"
                                                                    );
                                                                }

                                                                newProp.name =
                                                                  prop.name;
                                                                newProp.sections =
                                                                  prop.sections;
                                                                newProp.pricing =
                                                                  prop.pricing;
                                                                newProp.start_date =
                                                                  prop.start_date;
                                                                newProp.end_date =
                                                                  prop.end_date;

                                                                sb.data.db.obj.update(
                                                                  "proposals",
                                                                  newProp,
                                                                  function (
                                                                    newProposal
                                                                  ) {
                                                                    count++;
                                                                    ret.push(
                                                                      newProposal
                                                                    );

                                                                    addProposals(
                                                                      props,
                                                                      callback,
                                                                      count,
                                                                      ret
                                                                    );
                                                                  }
                                                                );
                                                              }
                                                            );
                                                          }
                                                        );
                                                      }
                                                    );
                                                  }
                                                );
                                              }
                                            );
                                          } else {
                                            callback(ret);
                                          }
                                        } else {
                                          callback(false);
                                        }
                                      }

                                      function createContractObject(
                                        obj,
                                        proposalId,
                                        callback
                                      ) {
                                        if (obj.contract) {
                                          obj.contract.related_object =
                                            proposalId;

                                          sb.data.db.obj.create(
                                            "contracts",
                                            obj.contract,
                                            function (newContract) {
                                              callback(newContract);
                                            }
                                          );
                                        } else {
                                          callback(false);
                                        }
                                      }

                                      function createMenuObject(
                                        obj,
                                        proposalId,
                                        callback
                                      ) {
                                        function duplicateMenuItems(
                                          menu,
                                          stack,
                                          count,
                                          callback,
                                          ret
                                        ) {
                                          if (!ret) {
                                            ret = [];
                                          }

                                          if (stack[count]) {
                                            var itemsToCreate = [];

                                            _.each(
                                              stack[count].items,
                                              function (item) {
                                                if (
                                                  item != null &&
                                                  item.hasOwnProperty("menu")
                                                ) {
                                                  item.menu = menu.id;
                                                  itemsToCreate.push(item);
                                                }
                                              }
                                            );

                                            sb.data.db.obj.create(
                                              "inventory_menu_line_item",
                                              itemsToCreate,
                                              function (createdItems) {
                                                stack[count].items = _.pluck(
                                                  createdItems,
                                                  "id"
                                                );

                                                ret.push(stack[count]);

                                                count++;

                                                return duplicateMenuItems(
                                                  menu,
                                                  stack,
                                                  count,
                                                  callback,
                                                  ret
                                                );
                                              }
                                            );
                                          } else {
                                            return callback(ret);
                                          }
                                        }

                                        if (obj.menu) {
                                          var where = {
                                            id: obj.menu.id,
                                            childObjs: 3,
                                          };

                                          sb.data.db.obj.getWhere(
                                            "inventory_menu",
                                            where,
                                            function (res) {
                                              if (res.length > 0) {
                                                // has a menu
                                                res[0].related = proposalId;
                                                res[0].guest_count =
                                                  obj.guest_count;

                                                sb.data.db.obj.create(
                                                  "inventory_menu",
                                                  res[0],
                                                  function (newMenu) {
                                                    duplicateMenuItems(
                                                      newMenu,
                                                      res[0].sections,
                                                      0,
                                                      function (newSections) {
                                                        newMenu.sections =
                                                          newSections;

                                                        sb.data.db.obj.update(
                                                          "inventory_menu",
                                                          newMenu,
                                                          function (
                                                            updatedMenu
                                                          ) {
                                                            callback(
                                                              updatedMenu
                                                            );
                                                          }
                                                        );
                                                      }
                                                    );
                                                  },
                                                  3
                                                );
                                              } else {
                                                // no menu found
                                                callback(false);
                                              }
                                            }
                                          );
                                        } else {
                                          callback(false);
                                        }
                                      }

                                      function createStaffScheduleObject(
                                        obj,
                                        proposalId,
                                        callback
                                      ) {
                                        if (!obj.schedule) {
                                          return callback(false);
                                        }

                                        var where = { id: obj.schedule.id };

                                        sb.data.db.obj.getWhere(
                                          "staff_schedules",
                                          where,
                                          function (res) {
                                            if (res.length > 0) {
                                              // has a staff schedule
                                              res[0].event = proposalId;

                                              sb.data.db.obj.create(
                                                "staff_schedules",
                                                res[0],
                                                function (staffSchedule) {
                                                  callback(staffSchedule);
                                                }
                                              );
                                            } else {
                                              // no staff schedule found
                                              callback(false);
                                            }
                                          }
                                        );
                                      }

                                      function createInvoiceObjects(
                                        obj,
                                        proposalId,
                                        callback
                                      ) {
                                        if (!obj.invoices) {
                                          return callback(false);
                                        }

                                        _.each(
                                          obj.invoices,
                                          function (invoiceObj) {
                                            invoiceObj.related_object =
                                              proposalId;
                                          }
                                        );

                                        sb.data.db.obj.create(
                                          "invoices",
                                          obj.invoices,
                                          function (invoices) {
                                            callback(invoices);
                                          }
                                        );
                                      }

                                      function duplicateHistoryItems(
                                        workOrder,
                                        template,
                                        callback
                                      ) {
                                        function copyHistoryItems(
                                          stack,
                                          count,
                                          callback,
                                          ret
                                        ) {
                                          if (!ret) {
                                            ret = [];
                                          }

                                          if (stack[count]) {
                                            var item = stack[count],
                                              itemsToCreate = [];

                                            createContractObject(
                                              template,
                                              workOrder,
                                              function (newContract) {
                                                createMenuObject(
                                                  template,
                                                  workOrder,
                                                  function (newMenu) {
                                                    createStaffScheduleObject(
                                                      template,
                                                      workOrder,
                                                      function (newSchedule) {
                                                        createInvoiceObjects(
                                                          template,
                                                          workOrder,
                                                          function (
                                                            newInvoices
                                                          ) {
                                                            var historyItem =
                                                              item;

                                                            historyItem.date =
                                                              moment().format();
                                                            historyItem.contract =
                                                              newContract.id;
                                                            historyItem.start_date =
                                                              workOrder.start_date;
                                                            historyItem.end_date =
                                                              workOrder.end_date;

                                                            if (newMenu) {
                                                              historyItem.menu =
                                                                newMenu.id;
                                                            }

                                                            if (newSchedule) {
                                                              historyItem.schedule =
                                                                newSchedule.id;
                                                            }

                                                            if (newInvoices) {
                                                              historyItem.invoices =
                                                                _.pluck(
                                                                  newInvoices,
                                                                  "id"
                                                                );
                                                            }

                                                            ret.push(
                                                              historyItem
                                                            );

                                                            count++;

                                                            return copyHistoryItems(
                                                              stack,
                                                              count,
                                                              callback,
                                                              ret
                                                            );
                                                          },
                                                          "No",
                                                          item.invoices
                                                        );
                                                      },
                                                      "No",
                                                      item.schedule
                                                    );
                                                  },
                                                  "No",
                                                  item.menu
                                                );
                                              },
                                              "No",
                                              item.contract
                                            );
                                          } else {
                                            return callback(ret);
                                          }
                                        }

                                        if (template.history.length > 0) {
                                          copyHistoryItems(
                                            template.history,
                                            0,
                                            function (newHistory) {
                                              workOrder.history = newHistory;

                                              sb.data.db.obj.update(
                                                "work_orders",
                                                workOrder,
                                                function (updatedWorkOrder) {
                                                  callback(updatedWorkOrder);
                                                },
                                                3
                                              );
                                            }
                                          );
                                        } else {
                                          callback(workOrder);
                                        }
                                      }

                                      sb.dom.alerts.ask(
                                        {
                                          title: "Are you sure?",
                                          text: "",
                                        },
                                        function (response) {
                                          if (response) {
                                            swal.disableButtons();

                                            addProposals(
                                              selected,
                                              function (done) {
                                                swal.close();
                                                dom.modals.template.hide();

                                                sb.data.db.obj.getById(
                                                  "projects",
                                                  workOrder.id,
                                                  function (obj) {
                                                    historyViewer(
                                                      dom,
                                                      obj,
                                                      dom,
                                                      pricing,
                                                      categories
                                                    );
                                                  },
                                                  3
                                                );
                                              }
                                            );
                                          }
                                        }
                                      );
                                    },
                                  },
                                },
                                rowLink: false,
                                visibleCols: {
                                  object_uid: "ID",
                                  name: "Name",
                                  venue: "Venue",
                                },
                                cells: {
                                  venue: function (obj) {
                                    if (obj.venue) {
                                      return obj.venue.name;
                                    } else {
                                      return "No venue";
                                    }
                                  },
                                },
                                data: function (paged, callback) {
                                  sb.data.db.obj.getWhere(
                                    "proposals",
                                    {
                                      is_template: 1,
                                      childObjs: 3,
                                      paged: paged,
                                    },
                                    function (ret) {
                                      callback(ret);
                                    }
                                  );
                                },
                              },
                            });
                          },
                        });

                        dom.modals.patch();

                        dom.modals.template.show();
                      }.bind({}, dom, obj),
                    },
                  },
                  sb.moduleId
                );
          }

          dom.tools.cont.btns
            .makeNode("send", "button", {
              text: '<i class="fa fa-envelope"></i> Send to client',
              css: "pda-btn-primary",
            })
            .notify(
              "click",
              {
                type: "proposals-run",
                data: {
                  run: function (obj) {
                    var dom = this,
                      emailAddresss = [],
                      link =
                        '<a href="https://bento.infinityhospitality.net/app/workorders/#?&i=' +
                        appConfig.instance +
                        "&wid=" +
                        obj.id +
                        '">CLICK HERE TO VIEW</a>';

                    dom.tools.cont.btns.send.loading();

                    sb.data.db.obj.getWhere(
                      "proposals",
                      { main_object: obj.id },
                      function (proposals) {
                        if (proposals.length == 0) {
                          sb.dom.alerts.alert(
                            "No proposals saved",
                            "Please save a proposal before sending to the client.",
                            "warning"
                          );

                          dom.tools.cont.btns.send.loading(false);
                        } else {
                          sb.data.db.obj.getById(
                            "contacts",
                            obj.main_contact.id,
                            function (mainContact) {
                              dom.tools.cont.btns.send.loading(false);

                              dom.modals.makeNode("send", "modal", {});

                              dom.modals.send.body.makeNode("cont", "div", {});

                              dom.modals.patch();

                              dom.modals.send.show();

                              if (obj.hasOwnProperty("main_contact")) {
                                if (
                                  mainContact.hasOwnProperty("contact_info")
                                ) {
                                  // 									console.log(' Send to client',mainContact.contact_info);
                                  _.each(
                                    mainContact.contact_info,
                                    function (info) {
                                      if (
                                        info.type.data_type == "email" &&
                                        info.is_primary == "yes"
                                      ) {
                                        emailAddresss.push(info.info);
                                      }
                                    }
                                  );
                                }
                              }

                              swal.close();

                              // check for additional contacts
                              var addEmails = [
                                {
                                  label:
                                    "<b>" +
                                    mainContact.fname +
                                    " " +
                                    mainContact.lname +
                                    " - Main Contact</b>",
                                  value: emailAddresss,
                                  checked: true,
                                },
                              ];

                              if (obj.additional_contacts) {
                                _.each(
                                  obj.additional_contacts,
                                  function (contact) {
                                    if (contact.contact_info) {
                                      _.each(
                                        contact.contact_info,
                                        function (info) {
                                          if (info.type.data_type == "email") {
                                            addEmails.push({
                                              label:
                                                contact.fname +
                                                " " +
                                                contact.lname +
                                                " - " +
                                                contact.type.name,
                                              value: info.info,
                                            });
                                          }
                                        }
                                      );
                                    }
                                  }
                                );
                              }

                              sb.data.db.obj.getAll(
                                "staff",
                                function (staff_list) {
                                  checkSettingsObject(function (settingsObj) {
                                    var venue = "";
                                    if (obj.venue) {
                                      venue = obj.venue.name;
                                    }

                                    var managerFname = "";
                                    var managerLname = "";
                                    if (obj.manager) {
                                      managerFname = obj.manager.fname;
                                      managerLname = obj.manager.lname;
                                    }

                                    var mergeVars = [
                                      {
                                        tag: "*|first_name|*",
                                        value: mainContact.fname,
                                      },
                                      {
                                        tag: "*|last_name|*",
                                        value: mainContact.lname,
                                      },
                                      {
                                        tag: "*|work_order_name|*",
                                        value: obj.name,
                                      },
                                      {
                                        tag: "*|venue|*",
                                        value: venue,
                                      },
                                      {
                                        tag: "*|start_date|*",
                                        value: moment(obj.start_date).format(
                                          "M/D/YYYY h:mm a"
                                        ),
                                      },
                                      {
                                        tag: "*|manager_first_name|*",
                                        value: managerFname,
                                      },
                                      {
                                        tag: "*|manager_last_name|*",
                                        value: managerLname,
                                      },
                                    ];

                                    if (!settingsObj.request_email) {
                                      settingsObj.request_email =
                                        "You have new proposals to view";
                                      settingsObj.request_email_subject =
                                        defaultRequestEmailSubject;
                                    }

                                    _.each(mergeVars, function (tagObj) {
                                      settingsObj.request_email =
                                        settingsObj.request_email.replace(
                                          tagObj.tag,
                                          tagObj.value
                                        );
                                      settingsObj.request_email_subject =
                                        settingsObj.request_email_subject.replace(
                                          tagObj.tag,
                                          tagObj.value
                                        );
                                    });

                                    sb.notify({
                                      type: "show-compose-form",
                                      data: {
                                        domObj: dom.modals.send.body.cont,
                                        objectId: obj.id,
                                        objectType: "work_orders",
                                        email: {
                                          to: "",
                                          additionalEmails: addEmails,
                                          subject:
                                            settingsObj.request_email_subject,
                                          message:
                                            settingsObj.request_email +
                                            "<br />" +
                                            link,
                                          staff: staff_list,
                                        },
                                        action: function (sentEmail) {
                                          sb.data.db.obj.update(
                                            "work_orders",
                                            { id: obj.id, status: "Sent" },
                                            function (updated) {
                                              var noteObj = {
                                                type_id: obj.id,
                                                type: "work_orders",
                                                note: "Proposal(s) have been emailed to the client.",
                                                note_type: 0,
                                                author: +sb.data.cookie.userId,
                                                notifyUsers: [],
                                              };

                                              sb.data.db.obj.create(
                                                "notes",
                                                noteObj,
                                                function (createdNote) {
                                                  dom.modals.send.hide();

                                                  sb.notify({
                                                    type: "app-redraw",
                                                    data: {},
                                                  });

                                                  //sb.dom.alerts.alert('Email sent', '', 'success');
                                                }
                                              );
                                            }
                                          );
                                        },
                                      },
                                    });
                                  });
                                },
                                {
                                  fname: true,
                                  lname: true,
                                  email: true,
                                }
                              );
                            },
                            2
                          );
                        }
                      }
                    );
                  }.bind(dom, obj),
                },
              },
              sb.moduleId
            );

          dom.tools.cont.btns.makeNode("see", "div", {
            tag: "a",
            css: "ui teal button",
            href:
              "https://bento.infinityhospitality.net/app/workorders#?&i=" +
              appConfig.instance +
              "&wid=" +
              obj.id,
            target: "_blank",
            text: "Client Portal",
          });

          dom.tools.cont.makeNode("history", "column", { w: 16 });

          delete dom.loading;

          dom.patch();

          historyViewer(
            dom.tools.cont.history,
            obj,
            dom.estimate.cont,
            pricing,
            categories,
            compact,
            reloadCallbackFunc
          );
        });
      }
    );

    /*
		if(obj.contract){
						
			dom.tools.cont.makeNode('history', 'column', {w:16});
							
			delete dom.loading;
			
			dom.patch();
							
			historyViewer(dom.tools.cont.history, obj, dom.estimate.cont, pricing, categories);
		
		}else{
							
			dom.makeNode('break1', 'lineBreak', {});
			
			dom.makeNode('noContract', 'headerText', {text:'Select a contract template first (step 1 above)', size:'x-small', css:'text-center'});
			
			dom.makeNode('break2', 'lineBreak', {});
			
			dom.patch();
						
		}
*/
  }

  function getAllBluprints(mainBP, callback) {
    var bpsToGet = _.compact(
      _.map(mainBP, function (o, name) {
        if (o.type == "objectId" || o.type == "objectIds") {
          o.bp_name = name;

          return o;
        }
      })
    );

    function getBlueprints(list, callback, count, ret) {
      if (!count) {
        count = 0;
      }

      if (!ret) {
        ret = [];
      }

      if (list[count]) {
        sb.data.db.obj.getBlueprint(list[count].objectType, function (bp) {
          ret.push({
            type: list[count].objectType,
            blueprint: bp,
            bp_name: list[count].bp_name,
          });

          count++;

          getBlueprints(list, callback, count, ret);
        });
      } else {
        callback(ret);
      }
    }

    getBlueprints(bpsToGet, function (allBlueprints) {
      callback(allBlueprints);
    });
  }

  // CLIENT PORTAL
  function acceptProposal(obj, historyItem, callback) {
    sb.data.db.obj.getById("", obj.main_object, function (workorder) {
      var contractId = 0;
      var menuId = 0;
      var scheduleId = 0;

      if (historyItem.contract) {
        contractId = historyItem.contract.id;
      }

      if (historyItem.menu) {
        menuId = historyItem.menu.id;
      }

      if (historyItem.schedule) {
        scheduleId = historyItem.schedule.id;
      }

      var updates = [
        {
          id: workorder.id,
          status: "Accepted",
          object_bp_type: workorder.object_bp_type,
          price: _.reduce(
            historyItem.pricing,
            function (memo, price, catId) {
              return memo + +price;
            },
            0
          ),
          balance: _.reduce(
            historyItem.pricing,
            function (memo, price, catId) {
              return memo + +price;
            },
            0
          ),
          amount_paid: 0,
        },
        {
          id: historyItem.id,
          object_bp_type: "proposals",
          status: "Accepted",
        },
        {
          id: contractId,
          active: "Yes",
          object_bp_type: "contracts",
        },
        {
          id: menuId,
          active: "Yes",
          object_bp_type: "inventory_menu",
        },
      ];

      if (+historyItem.schedule > 1) {
        updates.push({
          id: scheduleId,
          active: "Yes",
          object_bp_type: "staff_schedules",
        });
      }

      sb.data.db.controller(
        "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
        {
          queryObj: {
            related: obj.id,
            active: "Yes",
          },
          instance: instance,
          objectType: "inventory_menu",
          getChildObjs: {
            id: true,
          },
        },
        function (activeMenus) {
          var activeMenu = activeMenus[0];

          _.map(activeMenus, function (activeMenu) {
            updates.push({
              id: activeMenu.id,
              active: "No",
              object_bp_type: "inventory_menu",
            });
          });

          sb.data.db.controller(
            "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
            {
              queryObj: {
                related_object: obj.id,
                active: "Yes",
              },
              instance: instance,
              objectType: "invoices",
              getChildObjs: {
                id: true,
              },
            },
            function (activeInvoices) {
              if (activeInvoices.length > 0) {
                updates = updates.concat(
                  _.map(
                    activeInvoices,
                    function (inv) {
                      return {
                        id: inv.id,
                        active: "No",
                        object_bp_type: "invoices",
                      };
                    },
                    []
                  )
                );
              }

              updates = updates.concat(
                _.map(
                  historyItem.invoices,
                  function (inv) {
                    return {
                      id: +inv,
                      active: "Yes",
                      object_bp_type: "invoices",
                    };
                  },
                  []
                )
              );

              sb.data.db.controller(
                "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                {
                  queryObj: {
                    event: obj.id,
                    active: "Yes",
                  },
                  instance: instance,
                  objectType: "staff_schedules",
                  getChildObjs: {
                    id: true,
                  },
                },
                function (activeSchedules) {
                  if (activeSchedules.length > 0) {
                    var activeSchedule = activeSchedules[0];

                    updates.push({
                      id: activeSchedule.id,
                      active: "No",
                      object_bp_type: "staff_schedules",
                    });
                  }

                  sb.data.db.controller(
                    "updateObject&pagodaAPIKey=" + instance,
                    {
                      objectType: "objects",
                      objectData: updates,
                    },
                    function (updated) {
                      sb.data.db.controller(
                        "setMenuReservations&pagodaAPIKey=" + instance,
                        {
                          menuId: menuId,
                        },
                        function (response) {
                          callback(true);
                        }
                      );
                    }
                  );
                }
              );
            }
          );
        }
      );
    });
  }

  function buildInvoice(obj, edit, draw, domCache) {
    var dom = this,
      taxEditString = "",
      lineItemEditString = "",
      taxEditString = "";

    if (!dom.title) {
      dom.makeNode("loader", "loader", {});
      dom.patch();
    }

    var lineItems = "",
      subtotal = 0,
      taxRate = 0,
      tax = 0,
      total = 0,
      amountPaid = 0,
      totalDue = 0;

    if (obj.tax_rate) {
      taxRate = +obj.tax_rate;
    }

    _.each(obj.payments, function (p) {
      amountPaid += p.amount;
    });

    var clientAddress = "<i>No address given</i>",
      clientName = "<i>No client selected</i>";
    if (obj.main_client) {
      clientName = obj.main_contact.fname + " " + obj.main_contact.lname;

      if (obj.main_contact.company) {
        clientName =
          obj.main_contact.company.name +
          "<br />" +
          obj.main_contact.fname +
          " " +
          obj.main_contact.lname;
      }

      if (obj.main_contact.contact_info) {
        _.each(obj.main_contact.contact_info, function (info) {
          if (info.type.data_type == "address" && info.is_primary == "yes") {
            clientAddress =
              info.street +
              "<br />" +
              info.city +
              ", " +
              info.state +
              " " +
              info.zip;
          }
        });
      }
    }

    _.each(obj.items, function (item, k) {
      var itemSubtotal = item.amount * item.quantity,
        itemTotal = 0,
        itemTaxRate = 0,
        itemTax = 0;

      if (item.tax_rate) {
        itemTaxRate = item.tax_rate;
        itemTax = Math.round(item.amount * (itemTaxRate / 100));
      }

      itemTotal = itemSubtotal + itemTax;

      if (edit) {
        lineItemEditString =
          '<a data-id="' +
          k +
          '" class="remove-line-item"><i class="fa fa-trash-o" style="color:red; margin:0 5px 0px 0px;"></i></a> <a data-id="' +
          k +
          '" class="edit-line-item-' +
          obj.id +
          '"><i class="fa fa-pencil" style="color:orange; margin:0 10px;"></i></a> ';
        taxEditString =
          ' <a data-id="' +
          k +
          '" class="change-tax-rate-' +
          obj.id +
          '"><i class="fa fa-pencil" style="color:orange;"></i></a>';
      }

      lineItems +=
        '<tr class="bottom-border">  ' +
        '<td width="" class="edit-line-item-' +
        k +
        '">' +
        lineItemEditString +
        "" +
        item.name +
        "</td>  " +
        '<td width="" style="text-align: right;">$' +
        (item.amount / 100).formatMoney(2) +
        "</td>  " +
        '<td width="" style="text-align: right;">' +
        item.quantity +
        "</td>  " +
        '<td width="" style="text-align: right;" class="change-tax-rate-' +
        k +
        '" >$' +
        (itemTax / 100).formatMoney(2) +
        "" +
        taxEditString +
        "</td>  " +
        '<td width="" style="text-align: right;" class="lineTotal-' +
        k +
        '">$' +
        (itemTotal / 100).formatMoney(2) +
        "</td>  " +
        "</tr>";

      subtotal += itemTotal;
    });

    tax = subtotal * (+taxRate / 100);

    total = subtotal + tax;

    total = Math.round(total);

    totalDue = total - amountPaid;

    if (edit) {
      taxEditString =
        ' - <a style="color:blue;" class="tax-rate"><small>change</small></a>';
    }

    sb.data.db.obj.getAll("tax_rates", function (rates) {
      sb.data.db.obj.getAll(
        "invoice_system",
        function (settingsList) {
          getInstanceImage(appConfig, function (image) {
            var settings = settingsList[0],
              billingAddress = "<i>No billing address</i>";

            if (settings) {
              if (settings.billing_address) {
                billingAddress =
                  settings.billing_address.street +
                  "<br />" +
                  settings.billing_address.city +
                  ", " +
                  settings.billing_address.state +
                  " " +
                  settings.billing_address.zip;
              }
            }

            var paymentStubHTML = "<br /><br /><br /><br /><br /><br />";
            if (totalDue > 0) {
              paymentStubHTML =
                '   	<div style="border-bottom: 2px dotted #7e7e7e;"></div>  ' +
                "   	  " +
                "   	<br /><br />  " +
                "   	  " +
                '   	<table width="100%">  ' +
                "   		  " +
                "   		<tr>  " +
                "   			  " +
                "   			<td>  " +
                "   				  " +
                "   				<h3>PAYMENT STUB</h3>  " +
                "   				  " +
                "   				<br />  " +
                "   				  " +
                "   				<p>" +
                appConfig.systemName +
                "<br />" +
                billingAddress +
                "</p>  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   			<td>  " +
                "   				  " +
                '   				<div style="font-weight:bold;">  ' +
                '   					<p style="padding:10px;">To pay this invoice online, go to: <a href="https://bento.infinityhospitality.net/app/workorders/#?&i=' +
                appConfig.instance +
                "&wid=" +
                obj.related_object +
                '">LINK</a></p>' +
                "   				</div>  " +
                "   				  " +
                '   				<table width="100%">  ' +
                "   					  " +
                "   					<tr>  " +
                "     " +
                "   						<td>Client</td>  " +
                '   						<td style="text-align: right;">' +
                clientName +
                "</td>  " +
                "     " +
                "   					</tr>  " +
                "     " +
                '   					<tr class="bottom-border">  ' +
                "     " +
                "   						<td>Invoice #</td>  " +
                '   						<td style="text-align: right;">' +
                obj.object_uid +
                "</td>  " +
                "     " +
                "   					</tr>  " +
                "     " +
                '   					<tr class="bottom-border">  ' +
                "     " +
                "   						<td>Invoice Date</td>  " +
                '   						<td style="text-align: right;">' +
                moment(obj.due_date).format("M/D/YYYY") +
                "</td>  " +
                "     " +
                "   					</tr>  " +
                "   					  " +
                '   					<tr class="bottom-border">  ' +
                "     " +
                "   						<td>Balance Due</td>  " +
                '   						<td class="totalDue" style="text-align: right;">$' +
                (totalDue / 100).formatMoney(2) +
                "</td>  " +
                "     " +
                "   					</tr>  " +
                "   					  " +
                '   					<tr class="bottom-border">  ' +
                "     " +
                "   						<td>Amount Enclosed</td>  " +
                "   						<td></td>  " +
                "     " +
                "   					</tr>  " +
                "   					  " +
                "   				</table>  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                "   	</table>  " +
                "   	  " +
                "   	<br /><br />  ";
            } else {
              var paymentLineItemsHTML = "";

              _.each(obj.payments, function (payment) {
                paymentLineItemsHTML +=
                  '   					<tr style="border-top:1px dotted gray;">  ' +
                  "     " +
                  "   						<td>" +
                  moment(payment.date_created).format("M/D/YYYY h:mm a") +
                  "</td>  " +
                  '   						<td style="">$' +
                  (payment.amount / 100).formatMoney(2) +
                  "</td>  " +
                  "     " +
                  "   					</tr>  ";
              });

              paymentLineItemsHTML +=
                '   					<tr style="border-top:1px dotted gray;">  ' +
                "     " +
                '   						<td style="font-weight:bold;">TOTAL PAID</td>  ' +
                '   						<td style="font-weight:bold;">$' +
                (amountPaid / 100).formatMoney(2) +
                "</td>  " +
                "     " +
                "   					</tr>  ";

              paymentStubHTML =
                '<div style="border-bottom: 2px dotted #7e7e7e;"></div>  ' +
                '   	<table width="100%">  ' +
                "   		  " +
                "   		<tr>  " +
                "   			  " +
                "   			<td>  " +
                "   				  " +
                "   				<h3>PAYMENT HISTORY</h3>  " +
                "   				  " +
                "   				<br />  " +
                "   				  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   			<td>  " +
                '   				<table width="100%">  ' +
                "   					<tr>  " +
                "     " +
                '   						<td style="font-weight:bold;">Payment Date</td>  ' +
                '   						<td style="font-weight:bold;">Amount</td>  ' +
                "     " +
                "   					</tr>  " +
                "   					  " +
                paymentLineItemsHTML +
                "   					  " +
                "   				</table>  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                "   	</table>  " +
                "   	  " +
                "   	<br /><br />  ";
            }

            dom.empty();

            dom.makeNode("text", "text", {
              text:
                "" +
                '   <div id="invoice-container" style="width: 100%;">  ' +
                '		<table width="100%">' +
                "			<tr>" +
                '				<td width="50%">' +
                "   				<p>" +
                appConfig.systemName +
                "<br />" +
                billingAddress +
                "</p>  " +
                "				</td>" +
                '				<td width="50%" style="text-align:right;">' +
                ' 				  	<h2 style="text-align:right;">' +
                image +
                "</h2>  " +
                "				</td>" +
                "			</tr>" +
                "		</table>" +
                "   	  " +
                "   	<br /><br />" +
                "   	  " +
                '   	<table width="100%">  ' +
                "   		  " +
                "   		<tr>  " +
                "   			  " +
                '   			<td width="50%">  ' +
                "   				" +
                clientName +
                "<br />" +
                clientAddress +
                "   			</td>  " +
                "   			  " +
                '   			<td width="50%">  ' +
                "   				  " +
                '   				<table width="100%">  ' +
                "   					  " +
                "   					<tr>  " +
                "   						  " +
                '   						<td width="50%">Invoice #</td>  ' +
                '   						<td style="text-align: right;">' +
                obj.object_uid +
                "</td>  " +
                "   						  " +
                "   					</tr>  " +
                "     " +
                "   					<tr>  " +
                "   						  " +
                '   						<td width="50%">Invoice Date</td>  ' +
                '   						<td style="text-align: right;">' +
                moment(obj.due_date).format("M/D/YYYY") +
                "</td>  " +
                "   						  " +
                "   					</tr>  " +
                "     " +
                '   					<tr class="bold-table-row">  ' +
                "   						  " +
                '   						<td width="50%">Balance Due (USD)</td>  ' +
                '   						<td style="text-align: right;" class="totalDue">$' +
                (totalDue / 100).formatMoney(2) +
                "</td>  " +
                "   						  " +
                "   					</tr>  " +
                "   					  " +
                "   				</table>  " +
                "   				  " +
                "   			</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                "   	</table>  " +
                "   	  " +
                "   	<br /><br /><br /><br />" +
                "   	  " +
                '   	<table width="100%">  ' +
                "   					  " +
                '   		<tr class="bold-table-row">  ' +
                "   			  " +
                '   			<td width="35%">Line Item</td>  ' +
                '   			<td width="17.5%" style="text-align: right;">Unit Price</td>  ' +
                '   			<td width="15%" style="text-align: right;">Qty</td>  ' +
                '   			<td width="15%" style="text-align: right;">Tax</td>  ' +
                '   			<td width="17.5%" style="text-align: right;">Line Total</td>  ' +
                "   			  " +
                "   		</tr>  " +
                "     " +
                lineItems +
                "   		  " +
                '   		<tr style="font-weight: bold;">  ' +
                "   			  " +
                '   			<td width="50%"></td>  ' +
                '   			<td width="" style="text-align: right;">Subtotal</td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right;" class="subtotal">$' +
                (subtotal / 100).formatMoney(2) +
                "</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                '   		<tr style="font-weight: bold;">  ' +
                "   			  " +
                '   			<td width=""></td>  ' +
                '   			<td width="" style="text-align: right;">Amount Paid</td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right;" class="totalPaid">$' +
                (amountPaid / 100).formatMoney(2) +
                "</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                '   		<tr class="bold-table-row" style="font-weight:bold;">  ' +
                "   			  " +
                '   			<td width="" style="background-color: white; font-weight:bold;"></td>  ' +
                '   			<td width="" style="text-align: right;font-weight:bold;">Balance Due</td>  ' +
                '   			<td width="" style="text-align: right; font-weight:bold;"></td>  ' +
                '   			<td width="" style="text-align: right;"></td>  ' +
                '   			<td width="" style="text-align: right; font-weight:bold;" class="totalDue">$' +
                (totalDue / 100).formatMoney(2) +
                "</td>  " +
                "   			  " +
                "   		</tr>  " +
                "   		  " +
                "   	</table>  " +
                "   	  " +
                "   	<br /><br />  " +
                "   	  " +
                '   	<p style="font-weight: bold;">Memo</p>  ' +
                "   	  " +
                "   	<p>" +
                obj.memo +
                "</p>  " +
                "   	  " +
                "   	<br /><br /><br /><br />" +
                "   	  " +
                paymentStubHTML +
                "   	  " +
                "  </div>  " +
                "",
            });

            if (draw) {
              draw({
                dom: dom,
                after: function (dom) {
                  dom.css("pda-container pda-Panel pda-panel-gray");

                  /*
sb.notify({
									type: 'show-note-list-box',
									data: {
										domObj:dom.col2.cont.notes,
										objectIds:[obj.id],
										objectId:obj.id,
										collapse:true
									}
								});
*/
                },
              });
            } else {
              dom.patch();
            }

            if (edit) {
              $(".change-tax-rate-" + obj.id).click(function () {
                var itemId = $(this).data("id"),
                  invoiceTotal = 0;

                $(".change-tax-rate-" + itemId).webuiPopover({
                  placement: "left", //values: auto,top,right,bottom,left,top-right,top-left,bottom-right,bottom-left,auto-top,auto-right,auto-bottom,auto-left,horizontal,vertical
                  trigger: "click", //values:  click,hover,manual(handle events by your self),sticky(always show after popover is created);
                  animation: "pop", //pop with animation,values: pop,fade (only take effect in the browser which support css3 transition)
                  width: 200,
                  title: "Tax Rates", //the popover title, if title is set to empty string,title bar will auto hide
                  content: function () {
                    var optionText = '<option value="0">No Tax</option>';

                    _.each(rates, function (r) {
                      if (r.rate == obj.items[itemId].tax_rate) {
                        optionText +=
                          '<option selected="selected" value="' +
                          r.id +
                          '">' +
                          r.name +
                          "</option>";
                      } else {
                        optionText +=
                          '<option value="' +
                          r.id +
                          '">' +
                          r.name +
                          "</option>";
                      }
                    });

                    var html =
                      '<form class="form"><select id="" class="tax-rate-form pda-form pda-form-select pda-form-fullWidth">' +
                      optionText +
                      "</select></form>";

                    return html;
                  }, //content of the popover,content can be function
                  closeable: true, //display close button or not
                  type: "html", //content type, values:'html','iframe','async'
                  dismissible: true, // if popover can be dismissed by  outside click or escape key
                  onShow: function () {
                    $(".tax-rate-form").on("change", function () {
                      if (+$(this).val() > 0) {
                        obj.items[itemId].tax_rate = _.where(rates, {
                          id: +$(this).val(),
                        })[0].rate;
                      } else {
                        obj.items[itemId].tax_rate = "0.00";
                      }

                      var newAmount = 0,
                        totalPaid = 0;

                      _.each(obj.items, function (i) {
                        if (i.tax_rate != "0.00") {
                          newAmount += i.amount + i.amount * (i.tax_rate / 100);
                        } else {
                          newAmount += i.amount;
                        }
                      });

                      _.each(obj.payments, function (p) {
                        totalPaid += p.amount;
                      });

                      obj.amount = newAmount;
                      obj.paid = totalPaid;
                      obj.balance = newAmount - totalPaid;

                      sb.data.db.obj.update(
                        "invoices",
                        obj,
                        function (updated) {
                          var noteObj = {
                            type_id: updated.id,
                            type: "invoices",
                            note:
                              "Tax rate changed for item " +
                              obj.items[itemId].name +
                              ".",
                            note_type: 0,
                            author: sb.data.cookie.get("uid"),
                            notifyUsers: [],
                          };

                          sb.data.db.obj.create(
                            "notes",
                            noteObj,
                            function (newNote) {
                              WebuiPopovers.hideAll();

                              buildInvoice.call(dom, updated, true);
                            }
                          );
                        },
                        2
                      );
                    });
                  },
                });
              });

              $(".edit-line-item-" + obj.id).click(function (e) {
                var itemId = $(this).data("id");

                $(".edit-line-item-" + itemId).webuiPopover({
                  placement: "bottom", //values: auto,top,right,bottom,left,top-right,top-left,bottom-right,bottom-left,auto-top,auto-right,auto-bottom,auto-left,horizontal,vertical
                  trigger: "click", //values:  click,hover,manual(handle events by your self),sticky(always show after popover is created);
                  animation: "pop", //pop with animation,values: pop,fade (only take effect in the browser which support css3 transition)
                  width: 375,
                  title: "Enter a new name",
                  content: function () {
                    return "";
                  },
                  closeable: true, //display close button or not
                  type: "html", //content type, values:'html','iframe','async'
                  dismissible: true, // if popover can be dismissed by  outside click or escape key
                  onShow: function () {
                    var popDom = sb.dom.make(".webui-popover-content");

                    popDom.makeNode("form", "form", {
                      itemName: {
                        name: "itemName",
                        label: "Item Name",
                        type: "text",
                        value: obj.items[itemId].name,
                      },
                    });

                    popDom.makeNode("button", "button", {
                      text: "Save",
                      css: "pda-btn-green pda-btn-x-small",
                    });

                    popDom.button.notify(
                      "click",
                      {
                        type: "invoicesRun",
                        data: {
                          run: function (obj, itemId) {
                            var popDom = this,
                              newName =
                                this.form.process().fields.itemName.value;

                            popDom.button.loading();

                            obj.items[itemId].name = newName;

                            sb.data.db.obj.update(
                              "invoices",
                              { id: obj.id, items: obj.items },
                              function (updatedInvoice) {
                                WebuiPopovers.hideAll();

                                buildInvoice.call(dom, updatedInvoice, true);
                              },
                              2
                            );
                          }.bind(popDom, obj, itemId),
                        },
                      },
                      sb.moduleId
                    );

                    popDom.build();
                  },
                });
              });

              $(".remove-line-item").click(function () {
                var event = this;

                sb.dom.alerts.ask(
                  {
                    title: "",
                    text: "Are you sure you want to remove this line item?",
                  },
                  function (resp) {
                    if (resp) {
                      swal.disableButtons();

                      var itemId = $(event).data("id"),
                        invoiceTotal = 0,
                        itemToRemove = obj.items[itemId];

                      obj.items = _.reject(obj.items, function (item, k) {
                        return k == itemId;
                      });

                      _.each(obj.items, function (i) {
                        if (i.tax_rate) {
                          if (i.tax_rate > 0) {
                            invoiceTotal +=
                              i.amount * (i.tax_rate / 100) + i.amount;
                          } else {
                            invoiceTotal += i.amount;
                          }
                        } else {
                          invoiceTotal += i.amount;
                        }
                      });

                      if (invoiceTotal == 0) {
                        invoiceTotal = "0";
                      }

                      obj.amount = Math.round(+invoiceTotal);
                      obj.balance = obj.amount - obj.paid;

                      sb.data.db.obj.update(
                        "invoices",
                        obj,
                        function (updatedObj) {
                          var noteObj = {
                            type_id: updatedObj.id,
                            type: "invoices",
                            note:
                              itemToRemove.name +
                              " removed for $" +
                              (itemToRemove.amount / 100).formatMoney(2) +
                              ".",
                            note_type: 0,
                            author: sb.data.cookie.get("uid"),
                            notifyUsers: [],
                          };

                          sb.data.db.obj.create(
                            "notes",
                            noteObj,
                            function (newNote) {
                              swal.close();

                              buildInvoice.call(dom, updatedObj, true);
                            }
                          );
                        },
                        4
                      );
                    }
                  }
                );
              });
            }
          });
        },
        1
      );
    });
  }

  function getInstanceImage(config, callback) {
    if (config.company_logo) {
      sb.data.db.controller(
        "getObjectById&api_webform=true&pagodaAPIKey=" + config.instance,
        { value: config.company_logo, type: "file_meta_data" },
        function (image) {
          if (image.loc) {
            callback(
              '<img width="150px"; src="' + sb.data.files.getURL(image) + '">'
            );
          } else {
            callback(config.systemName);
          }
        },
        sb.url + "/api/_getAdmin.php?do="
      );
    } else {
      callback(config.systemName);
    }
  }

  function invoiceView(dom, obj, callback) {
    function activateResources(resourceList, callback) {
      var changeList = [];
      var running = 0;
      var complete = 0;
      var total = resourceList.length;
      var ret = [];

      _.each(resourceList, function (obj) {
        sb.data.db.controller(
          "updateObject&pagodaAPIKey=" + instance,
          {
            objectType: "proposals",
            objectData: {
              id: obj.id,
              active: "Yes",
            },
          },
          function (updates) {
            ret.push(updates);
            complete++;

            if (complete == total) {
              callback(ret);
            }
          }
        );
      });
    }

    var selectedInvoices = [];

    dom.makeNode("modals", "div", {});

    dom
      .makeNode("cont1", "div", { css: "ui one centered grid" })
      .makeNode("col", "column", { w: 16 });

    dom.cont1.col.makeNode("tableCont", "div", { css: "" });

    dom.cont1.col.tableCont.makeNode("btns", "buttonGroup", { css: "" });

    dom.cont1.col.tableCont.makeNode("tableBreak", "div", { text: "<br />" });

    dom.cont1.col.tableCont.makeNode("table", "table", {
      css: "table-hover table-condensed",
      columns: {
        select: "",
        id: "Invoice",
        due: "Amount Due",
        dueDate: "Due Date",
        btns: "",
      },
    });

    dom.cont1.col.tableCont.makeNode("invoiceBreak", "div", {
      text: "<br /><br />",
    });

    dom.cont1.col.tableCont.makeNode("invoiceCont", "container", { css: "" });

    dom.patch();

    sb.data.db.controller(
      "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
      {
        queryObj: {
          related_object: obj.id,
        },
        instance: instance,
        objectType: "invoices",
      },
      function (invoicesList) {
        activateResources([obj.menu], function (menu) {
          activateResources(invoicesList, function (invoices) {
            dom.cont1.col.tableCont.btns
              .makeNode("pay", "button", {
                text: '<i class="fa fa-usd"></i> Pay',
                css: "pda-btn-green",
              })
              .notify(
                "click",
                {
                  type: "invoicesPay-run",
                  data: {
                    run: function (obj) {
                      if (selectedInvoices.length == 0) {
                        sb.dom.alerts.alert(
                          "",
                          "Please select some invoices",
                          "error"
                        );
                        return;
                      }

                      var dom = this,
                        total = _.chain(selectedInvoices)
                          .pluck("balance")
                          .reduce(function (memo, num) {
                            return memo + +num;
                          });

                      dom.modals.makeNode("pay", "modal", {
                        onShow: function () {
                          /* 							console.log('obj',obj);					 */
                          sb.notify({
                            type: "show-make-payment-button",
                            data: {
                              domObj: dom.modals.pay.body,
                              payment: {
                                customerId: obj.main_object.main_contact,
                                invoiceId: selectedInvoices,
                                price: Math.trunc(total),
                                admin: false,
                              },
                              buttonSetup: {
                                text:
                                  '<i class="fa fa-credit-card"></i> Pay $' +
                                  (total / 100).formatMoney() +
                                  " now",
                                css: "pda-btn-green pda-btn-large",
                                skip: true,
                                notification:
                                  "client-invoice-payment-completed",
                                action: function (
                                  obj,
                                  selectedInvoices,
                                  callback
                                ) {
                                  var dom = this;

                                  callback(dom, obj, selectedInvoices);
                                }.bind(dom, obj, selectedInvoices),
                              },
                            },
                          });

                          dom.modals.pay.footer.makeNode("text", "headerText", {
                            text:
                              '<span class="small pda-color-red">Seperate invoices will be displayed as seperate charges on your bank statement.</span><br /><br />Total $' +
                              (total / 100).formatMoney(),
                            size: "x-small",
                            css: "text-center",
                          });

                          dom.modals.pay.footer.patch();
                        },
                      });

                      dom.modals.patch();

                      dom.modals.pay.show();
                    }.bind(dom, obj),
                  },
                },
                sb.moduleId
              );

            _.each(invoices, function (inv) {
              dom.cont1.col.tableCont.table.makeRow("inv-" + inv.id, [
                "",
                inv.name,
                "$" + (inv.balance / 100).formatMoney(),
                inv.due_date,
                "",
              ]);

              dom.cont1.col.tableCont.table.body["inv-" + inv.id].select
                .makeNode("select", "div", {
                  tag: "i",
                  css: "ui big square outline icon",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: function (inv) {
                        if (_.findWhere(selectedInvoices, { id: inv.id })) {
                          selectedInvoices = _.reject(
                            selectedInvoices,
                            function (obj) {
                              return obj.id == inv.id;
                            }
                          );

                          this.cont1.col.tableCont.table.body[
                            "inv-" + inv.id
                          ].select.select.css("ui big square outline icon");
                        } else {
                          selectedInvoices.push(inv);

                          this.cont1.col.tableCont.table.body[
                            "inv-" + inv.id
                          ].select.select.css(
                            "ui big check square outline icon"
                          );
                        }
                      }.bind(dom, inv),
                    },
                  },
                  sb.moduleId
                );

              dom.cont1.col.tableCont.table.body["inv-" + inv.id].btns.makeNode(
                "btns",
                "div",
                { css: "one ui fluid buttons" }
              );

              dom.cont1.col.tableCont.table.body["inv-" + inv.id].btns.btns
                .makeNode("download", "div", {
                  tag: "button",
                  text: '<i class="fa fa-eye"></i> View',
                  css: "ui blue fluid button",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: function (inv, dom) {
                        var button = this;

                        button.loading();

                        sb.data.db.controller(
                          "getObjectById&api_webform=true&pagodaAPIKey=" +
                            appConfig.instance,
                          {
                            value: inv.main_contact,
                            type: "contacts",
                            childObjs: 1,
                          },
                          function (contact) {
                            inv.main_contact = contact;

                            buildInvoice.call(dom, inv, false, function (draw) {
                              draw.dom.patch();

                              draw.after(draw.dom);

                              button.loading(false);
                            });
                          },
                          sb.url + "/api/_getAdmin.php?do="
                        );
                      }.bind(
                        dom.cont1.col.tableCont.table.body["inv-" + inv.id].btns
                          .btns.download,
                        inv,
                        dom.cont1.col.tableCont.invoiceCont
                      ),
                    },
                  },
                  sb.moduleId
                );
              //dom.table.body['inv-'+inv.id].btns.btns.makeNode('pay', 'button', {text:'<i class="fa fa-usd"></i> Pay', css:'pda-btn-green'});
            });

            delete dom.loading;

            //dom.makeNode('finalBreak', 'div', {text:'<br />'});

            dom.patch();

            callback(true);
          });
        });
      }
    );
  }

  function proposalView(dom, obj) {
    dom
      .makeNode("propGrid", "div", { css: "ui centered grid" })
      .makeNode("seg", "div", { css: "ten wide column" });

    dom.propGrid.seg
      .makeNode("seg", "div", { css: "ui padded clearing segment" })
      .makeNode("loader", "div", { css: "ui active inverted dimmer" })
      .makeNode("loader", "div", {
        css: "ui text loader",
        text: "Loading proposals",
      });

    dom.propGrid.seg.seg.makeNode("title", "div", {
      text:
        "Proposals for " +
        obj.main_contact.fname +
        " " +
        obj.main_contact.lname +
        " <small>" +
        obj.main_contact.company.name +
        "</small>",
      css: "ui huge header",
    });
    dom.propGrid.seg.seg.makeNode("manager", "div", {
      text:
        "Proposals for " +
        obj.main_contact.fname +
        " " +
        obj.main_contact.lname +
        " <small>" +
        obj.main_contact.company.name +
        "</small>",
      css: "ui huge header",
    });

    dom.patch();

    sb.data.db.controller(
      "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
      {
        queryObj: {
          main_object: obj.id,
        },
        instance: instance,
        objectType: "proposals",
        getChildObjs: 1,
      },
      function (proposals) {
        proposals = _.reject(proposals, function (prop) {
          return prop.name == "";
        });
        proposals = _.reject(proposals, function (prop) {
          return prop.status == null;
        });
        proposals = _.reject(proposals, function (prop) {
          return prop.status == "Proposal";
        });
        proposals = _.reject(proposals, function (prop) {
          return prop.status == "Active";
        });
        proposals = _.reject(proposals, function (prop) {
          return prop.status == "Editing";
        });

        if (proposals.length > 0) {
          //dom.propGrid.seg.seg.makeNode('contBreak', 'div', {text:'<br />'});

          dom.propGrid.seg.seg
            .makeNode("cont", "div", { css: "ui blue segment" })
            .makeNode("eventName", "div", {
              text:
                obj.name +
                " <small>" +
                moment(obj.start_date).format("M/D/YYYY h:mm a") +
                " - " +
                moment(obj.end_date).format("M/D/YYYY h:mm a") +
                "</small>",
              css: "ui header",
            });

          var cards = dom.propGrid.seg.seg.cont.makeNode("row", "div", {
            css: "four ui fluid cards",
          });

          _.each(proposals, function (prop, k) {
            if (prop.name != "") {
              cards
                .makeNode("col-" + k, "div", { css: "card" })
                .makeNode("prop", "div", { css: "content" });

              cards["col-" + k].prop.makeNode("name", "div", {
                text: prop.name,
                css: "header",
              });
              cards["col-" + k].prop.makeNode("price", "div", {
                text:
                  "Price: $" +
                  (
                    _.reduce(
                      Object.values(prop.pricing),
                      function (price, memo) {
                        return +memo + +price;
                      },
                      0
                    ) / 100
                  ).formatMoney(),
                css: "description",
              });
              cards["col-" + k]
                .makeNode("button", "div", {
                  css: "ui bottom attached blue button",
                  text: "View Proposal",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: function (prop, k, obj) {
                        var dom = this;

                        _.each(obj.history, function (item, key) {
                          if (k != key) {
                            //dom.row['col-'+key].makeNode('prop', 'div', {css:'ui piled segment'});
                            //dom.row['col-'+key].prop.css('');
                          } else {
                            //dom.row['col-'+key].prop.css('pda-container pda-Panel pda-panel-gray pda-background-orange');
                          }
                        });

                        dom
                          .makeNode("propCont", "div", {
                            css: "ui basic segment",
                            style: "max-width:850px; margin:1.5em auto;",
                          })
                          .makeNode("cont", "div", { css: "small-container" });

                        dom.patch();

                        showProposal(
                          dom.propCont,
                          prop,
                          k,
                          obj,
                          function (done) {}
                        );
                      }.bind(dom, prop, k, obj),
                    },
                  },
                  sb.moduleId
                );
            }
          });
        } else {
          dom.propGrid.seg.seg.makeNode("title", "div", {
            css: "ui center aligned huge header",
            text: "Nothing to approve.",
          });
        }

        delete dom.propGrid.seg.seg.loader;

        dom.propGrid.seg.seg.patch();
      },
      sb.url + "/api/_getAdmin.php?do="
    );
  }

  function showProposal(dom, prop, key, obj, callback) {
    dom.empty();

    dom.makeNode("loading", "div", { css: "ui active inverted dimmer" });
    dom.loading.makeNode("text", "text", {
      css: "ui text loader",
      text: "Loading proposal",
    });
    dom.makeNode("title", "div", {
      text: "Proposal: " + obj.name,
      css: "ui huge header",
    });
    dom.patch();

    sb.data.db.controller(
      "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
      {
        queryObj: {
          id: {
            type: "or",
            values: Object.keys(prop.pricing),
          },
        },
        instance: instance,
        objectType: "inventory_billable_categories",
        getChildObjs: 1,
      },
      function (categories) {
        //console.log('obj', obj);

        sb.data.db.obj.getById(prop.object_bp_type, prop.id, function (obj) {
          //console.log('opbj',obj);

          if (_.isArray(obj.sections)) {
            obj.sections = {
              contract: "Yes",
              itemList: "Yes",
              lineItem: "No",
              pricingTable: "No",
              invoiceTable: "Yes",
              itemListCategories: {},
              pricingTableCategories: {},
              labor: false,
            };
          }

          viewProposalPDF(
            obj,
            categories,
            obj.sections,
            function (contractHTML) {
              delete dom.loading;

              dom.makeNode("title", "div", {
                text: prop.name,
                css: "ui huge header",
              });

              dom.makeNode("btnsCont", "div", {
                css: "ui raised tertiary segment",
              });

              dom.btnsCont.makeNode("title", "div", {
                text: "What would you like to do?",
                css: "ui medium header",
              });

              dom.btnsCont.makeNode("btns", "div", { css: "three ui buttons" });

              dom.btnsCont.makeNode("notes", "div", { css: "" });

              dom.btnsCont.btns
                .makeNode("accept", "button", {
                  text: '<i class="fa fa-check"></i> Accept',
                  css: "pda-btn-green",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: function () {
                        var dom = this;

                        sb.dom.alerts.ask(
                          {
                            title: "Accept or provide notes?",
                            text: "Accepting this proposal will let you move on to sign the contract and pay the invoices. Providing notes will alert the admin to come take a look. You won't be able to sign or pay until the admin reviews your notes.",
                            primaryButtonText: "Yes. Accept the proposal",
                            cancelButtonText: "No, don't accept the proposal.",
                          },
                          function (resp) {
                            if (resp) {
                              swal.disableButtons();

                              acceptProposal(obj, prop, function (done) {
                                location.reload();
                              });
                            }
                          }
                        );
                      }.bind(dom),
                    },
                  },
                  sb.moduleId
                );

              dom.btnsCont.btns
                .makeNode("notes", "button", {
                  text: '<i class="fa fa-comment"></i> Add Notes',
                  css: "pda-btn-blue",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: function () {
                        var dom = this;

                        dom.btnsCont.notes.css("ui blue segment");

                        dom.btnsCont.notes.makeNode("title", "headerText", {
                          text: "Add your notes below.",
                          size: "small",
                        });

                        dom.btnsCont.notes.makeNode("form", "form", {
                          notes: {
                            name: "notes",
                            label: "",
                            type: "textbox",
                            rows: 5,
                          },
                        });

                        dom.btnsCont.notes.makeNode("break", "lineBreak", {
                          spaces: 1,
                        });

                        dom.btnsCont.notes
                          .makeNode("next", "button", {
                            text: 'Next Step <i class="fa fa-arrow-right"></i>',
                            css: "pda-btn-green",
                          })
                          .notify(
                            "click",
                            {
                              type: "invoicesPay-run",
                              data: {
                                run: function (prop, key, obj) {
                                  var dom = this,
                                    clientNotes =
                                      dom.btnsCont.notes.form.process().fields
                                        .notes.value;

                                  dom.btnsCont.notes.next.text(
                                    'Posting notes <i class="fa fa-circle-o-notch fa-spin"></i>'
                                  );
                                  dom.btnsCont.notes.next.css(
                                    "pda-btn-primary"
                                  );

                                  var noteObj = {
                                    type_id: prop.contract,
                                    type: "contracts",
                                    note:
                                      "Client notes about proposal - <b>" +
                                      prop.name +
                                      "</b><br /><br />" +
                                      clientNotes,
                                    note_type: 0,
                                    author: 0,
                                    notifyUsers: [],
                                  };

                                  sb.data.db.controller(
                                    "createNewObject&api_webform=true&pagodaAPIKey=" +
                                      instance,
                                    {
                                      objectType: "notes",
                                      objectData: noteObj,
                                    },
                                    function (newNote) {
                                      function sendManagerNotesEmail(
                                        obj,
                                        callback
                                      ) {
                                        var email = "";

                                        if (obj.manager) {
                                          if (obj.manager.related_object) {
                                            if (
                                              obj.manager.related_object.email
                                            ) {
                                              email =
                                                obj.manager.related_object
                                                  .email;

                                              var emailObj = {
                                                newThread: true,
                                                to: email,
                                                from: appConfig.emailFrom,
                                                subject:
                                                  obj.main_contact.fname +
                                                  " " +
                                                  obj.main_contact.lname +
                                                  " posted notes about proposal " +
                                                  prop.name,
                                                mergevars: {
                                                  TITLE:
                                                    obj.main_contact.fname +
                                                    " " +
                                                    obj.main_contact.lname +
                                                    " posted notes about proposal " +
                                                    prop.name,
                                                  BODY:
                                                    "Notes received from " +
                                                    obj.main_contact.fname +
                                                    " " +
                                                    obj.main_contact.lname +
                                                    " about proposal " +
                                                    prop.name +
                                                    ".<br /><br /><br /><br />" +
                                                    clientNotes +
                                                    '<br /><br /><a href="https://bento.infinityhospitality.net/app/' +
                                                    instance +
                                                    "/#workorders&page=workorders&view=table&single=" +
                                                    obj.id +
                                                    '">VIEW WORK ORDER</a>',
                                                  BUTTON: "",
                                                },
                                                emailtags: [
                                                  "Proposal client notes",
                                                ],
                                                type: "work_orders",
                                                typeId: obj.id,
                                              };

                                              sb.data.db.controller(
                                                "sendEmail&api_webform=true&pagodaAPIKey=" +
                                                  instance,
                                                emailObj,
                                                function (response) {
                                                  callback(true);
                                                },
                                                sb.url +
                                                  "/api/_getAdmin.php?do="
                                              );
                                            }
                                          }
                                        }

                                        callback(true);
                                      }

                                      sendManagerNotesEmail(
                                        obj,
                                        function (done) {
                                          dom.btnsCont.notes.empty();

                                          dom.btnsCont.notes.patch();

                                          dom.btnsCont.notes.css("");

                                          sb.dom.alerts.alert(
                                            "Got it!",
                                            "Your notes have been sent to the admin for review.",
                                            "success"
                                          );
                                        }
                                      );
                                    }
                                  );
                                }.bind(dom, prop, key, obj),
                              },
                            },
                            sb.moduleId
                          );

                        //dom.btnsCont.notes.makeNode('break', 'lineBreak', {spaces:2});

                        dom.btnsCont.notes.patch();
                      }.bind(dom),
                    },
                  },
                  sb.moduleId
                );

              dom.btnsCont.btns
                .makeNode("download", "button", {
                  text: '<i class="fa fa-download"></i> Download A PDF',
                  css: "pda-btn-blue",
                })
                .notify(
                  "click",
                  {
                    type: "invoicesPay-run",
                    data: {
                      run: function () {
                        var dom = this;

                        dom.btnsCont.btns.download.text(
                          '<i class="fa fa-circle-o-notch fa-spin"></i> Preparing PDF...'
                        );
                        sb.data.db.obj.getById(
                          prop.object_bp_type,
                          prop.id,
                          function (proposalObj) {
                            if (_.isArray(proposalObj.sections)) {
                              proposalObj.sections = sections;
                            }

                            viewProposalPDF(
                              proposalObj,
                              categories,
                              proposalObj.sections,
                              function (done) {
                                dom.btnsCont.btns.download.text(
                                  '<i class="fa fa-download"></i> Download PDF'
                                );
                              }
                            );
                          }
                        );
                      }.bind(dom),
                    },
                  },
                  sb.moduleId
                );

              dom.makeNode("cont", "div", { css: "ui raised basic segment" });
              dom.cont.makeNode("text", "text", { text: contractHTML });

              dom.patch();

              callback(true);
            },
            true
          );
        });
      }
    );
  }

  function showWorkorder(dom, workorder, callback) {
    dom.makeNode("loading", "div", {});
    dom.loading.makeNode("text", "text", {
      text: "Loading...",
      css: "text-center",
    });
    dom.loading.makeNode("loader", "loader", {});

    dom.patch();

    sb.data.db.controller(
      "getObjectById&api_webform=true&pagodaAPIKey=" + instance,
      {
        value: +workorder,
        type: "groups",
        childObjs: 3,
      },
      function (obj) {
        delete dom.loading;

        dom.makeNode("mainBreak", "div", { text: "<br />" });
        dom
          .makeNode("cont", "div", {
            css: "ui basic segment",
            style: "margin:0 auto; max-width:1150px;",
          })
          .makeNode("steps", "div", { css: "ui three fluid ordered steps" });
        dom.makeNode("nav", "div", { css: "ui grid" });

        switch (obj.status) {
          case "Accepted":
            dom.cont.steps.makeNode("step1", "div", { css: "completed step" });
            dom.cont.steps.step1.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step1.content.makeNode("title", "div", {
              css: "title",
              text: "Review proposals",
            });
            dom.cont.steps.step1.content.makeNode("desc", "div", {
              css: "description",
              text: "Review and approve your proposals.",
            });

            dom.cont.steps.makeNode("step2", "div", { css: "active step" });
            dom.cont.steps.step2.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step2.content.makeNode("title", "div", {
              css: "title",
              text: "Sign contract",
            });
            dom.cont.steps.step2.content.makeNode("desc", "div", {
              css: "description",
              text: "Sign the contract for the approved proposal.",
            });

            dom.cont.steps.makeNode("step3", "div", { css: "step" });
            dom.cont.steps.step3.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step3.content.makeNode("title", "div", {
              css: "title",
              text: "Pay invoices",
            });
            dom.cont.steps.step3.content.makeNode("desc", "div", {
              css: "description",
              text: "Select and pay invoices.",
            });

            dom.patch();

            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  main_object: obj.id,
                  status: "Accepted",
                },
                instance: instance,
                objectType: "proposals",
                getChildObjs: 1,
              },
              function (accepted) {
                if (accepted.length > 0) {
                  obj = accepted[0];
                  obj.menu = accepted[0].menu.id;

                  sb.data.db.controller(
                    "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                    {
                      queryObj: {
                        id: {
                          type: "or",
                          values: Object.keys(obj.pricing),
                        },
                      },
                      instance: instance,
                      objectType: "inventory_billable_categories",
                      getChildObjs: 1,
                    },
                    function (categories) {
                      /* console.log(obj); */
                      if (obj.status == "Accepted") {
                        signContractView(dom, obj, categories);
                      } else {
                        dom.cont.steps.makeNode("step1", "div", {
                          css: "completed step",
                        });
                        dom.cont.steps.step1.makeNode("content", "div", {
                          css: "content",
                        });
                        dom.cont.steps.step1.content.makeNode("title", "div", {
                          css: "title",
                          text: "Review proposals",
                        });
                        dom.cont.steps.step1.content.makeNode("desc", "div", {
                          css: "description",
                          text: "Review and approve your proposals.",
                        });

                        dom.cont.steps.makeNode("step2", "div", {
                          css: "completed step",
                        });
                        dom.cont.steps.step2.makeNode("content", "div", {
                          css: "content",
                        });
                        dom.cont.steps.step2.content.makeNode("title", "div", {
                          css: "title",
                          text: "Sign contract",
                        });
                        dom.cont.steps.step2.content.makeNode("desc", "div", {
                          css: "description",
                          text: "Sign the contract for the approved proposal.",
                        });

                        dom.cont.steps.makeNode("step3", "div", {
                          css: "active step",
                        });
                        dom.cont.steps.step3.makeNode("content", "div", {
                          css: "content",
                        });
                        dom.cont.steps.step3.content.makeNode("title", "div", {
                          css: "title",
                          text: "Pay invoices",
                        });
                        dom.cont.steps.step3.content.makeNode("desc", "div", {
                          css: "description",
                          text: "Select and pay invoices.",
                        });

                        dom.makeNode("contBreak", "div", {
                          text: "<br /><br />",
                        });

                        dom.makeNode("grid", "div", {
                          css: "ui centered stackable grid container",
                        });
                        dom.grid
                          .makeNode("propCol", "div", {
                            css: "four wide column",
                          })
                          .makeNode("cont", "div", {
                            css: "ui active loading segment",
                          });
                        dom.grid
                          .makeNode("invoiceCol", "div", {
                            css: "twelve wide column",
                          })
                          .makeNode("cont", "div", {
                            css: "ui active loading segment",
                          });

                        dom.grid.propCol.cont.makeNode("title", "div", {
                          css: "ui header",
                          text: "Loading proposal...",
                        });
                        dom.grid.propCol.cont.makeNode("titleBreak", "div", {
                          text: "<br />",
                        });
                        dom.grid.invoiceCol.cont.makeNode("title", "div", {
                          css: "ui header",
                          text: "Inoivce List",
                        });

                        dom.patch();

                        sb.data.db.controller(
                          "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                            instance,
                          {
                            queryObj: {
                              id: obj.id,
                              status: "Accepted",
                            },
                            instance: instance,
                            objectType: "proposals",
                          },
                          function (accepted) {
                            obj.proposal = accepted[0];

                            invoiceView(
                              dom.grid.invoiceCol.cont,
                              obj,
                              function (done) {
                                dom.grid.invoiceCol.cont.css(
                                  "ui basic segment"
                                );
                              }
                            );

                            sb.data.db.controller(
                              "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                                instance,
                              {
                                queryObj: {
                                  main_object: obj.id,
                                  status: "Approved",
                                },
                                instance: instance,
                                objectType: "proposals",
                              },
                              function (approved) {
                                var proposals = accepted.concat(approved);

                                smallProposalList(
                                  dom.grid.propCol,
                                  proposals,
                                  function (done) {}
                                );
                              }
                            );
                          }
                        );
                      }
                    }
                  );
                } else {
                  dom.cont.steps.makeNode("step1", "div", {
                    css: "completed step",
                  });
                  dom.cont.steps.step1.makeNode("content", "div", {
                    css: "content",
                  });
                  dom.cont.steps.step1.content.makeNode("title", "div", {
                    css: "title",
                    text: "Review proposals",
                  });
                  dom.cont.steps.step1.content.makeNode("desc", "div", {
                    css: "description",
                    text: "Review and approve your proposals.",
                  });

                  dom.cont.steps.makeNode("step2", "div", {
                    css: "completed step",
                  });
                  dom.cont.steps.step2.makeNode("content", "div", {
                    css: "content",
                  });
                  dom.cont.steps.step2.content.makeNode("title", "div", {
                    css: "title",
                    text: "Sign contract",
                  });
                  dom.cont.steps.step2.content.makeNode("desc", "div", {
                    css: "description",
                    text: "Sign the contract for the approved proposal.",
                  });

                  dom.cont.steps.makeNode("step3", "div", {
                    css: "active step",
                  });
                  dom.cont.steps.step3.makeNode("content", "div", {
                    css: "content",
                  });
                  dom.cont.steps.step3.content.makeNode("title", "div", {
                    css: "title",
                    text: "Pay invoices",
                  });
                  dom.cont.steps.step3.content.makeNode("desc", "div", {
                    css: "description",
                    text: "Select and pay invoices.",
                  });

                  dom.makeNode("contBreak", "div", {
                    text: "<br /><br /><br />",
                  });

                  dom.makeNode("grid", "div", {
                    css: "ui centered stackable grid container",
                  });
                  dom.grid
                    .makeNode("propCol", "div", { css: "four wide column" })
                    .makeNode("cont", "div", {
                      css: "ui active loading segment",
                    });
                  dom.grid
                    .makeNode("invoiceCol", "div", {
                      css: "twelve wide column",
                    })
                    .makeNode("cont", "div", {
                      css: "ui active loading segment",
                    });

                  dom.grid.propCol.cont.makeNode("title", "div", {
                    css: "ui header",
                    text: "Proposals",
                  });
                  dom.grid.propCol.cont.makeNode("titleBreak", "div", {
                    text: "<br />",
                  });
                  dom.grid.invoiceCol.cont.makeNode("title", "div", {
                    css: "ui header",
                    text: "Inoivce List",
                  });

                  dom.patch();

                  sb.data.db.controller(
                    "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                    {
                      queryObj: {
                        main_object: obj.id,
                        status: "Signed",
                      },
                      instance: instance,
                      objectType: "proposals",
                      getChildObjs: 1,
                    },
                    function (accepted) {
                      sb.data.db.controller(
                        "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                          instance,
                        {
                          queryObj: {
                            main_object: obj.id,
                            status: "Signed",
                          },
                          instance: instance,
                          objectType: "proposals",
                          getChildObjs: 1,
                        },
                        function (accepted) {
                          obj = accepted[0];

                          invoiceView(
                            dom.grid.invoiceCol.cont,
                            obj,
                            function (done) {
                              dom.grid.invoiceCol.cont.css("ui basic segment");
                            }
                          );

                          sb.data.db.controller(
                            "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                              instance,
                            {
                              queryObj: {
                                main_object: obj.id,
                                status: "Approved",
                              },
                              instance: instance,
                              objectType: "proposals",
                              getChildObjs: 1,
                            },
                            function (approved) {
                              var proposals = accepted.concat(approved);

                              smallProposalList(
                                dom.grid.propCol.cont,
                                proposals,
                                function (done) {
                                  dom.grid.propCol.cont.css("ui basic segment");
                                }
                              );
                            }
                          );
                        }
                      );
                    }
                  );
                }
              }
            );

            break;

          case "Paid":
            dom.nav
              .makeNode("col1", "column", { width: 4, css: "pda-center" })
              .makeNode("text", "button", {
                text: '1. Review Proposals <i class="fa fa-check"></i>',
                css: "pda-center pda-color-green pda-transparent pda-btn-large",
              });

            dom.nav
              .makeNode("col2", "column", { width: 4, css: "pda-center" })
              .makeNode("text", "button", {
                text: '2. Sign Contract <i class="fa fa-check"></i>',
                css: "pda-center pda-color-green pda-transparent pda-btn-large",
              });

            dom.nav
              .makeNode("col3", "column", { width: 4, css: "pda-center" })
              .makeNode("text", "button", {
                text: '3. Pay Invoices <i class="fa fa-check"></i>',
                css: "pda-center pda-color-green pda-transparent pda-btn-large",
              });

            dom.nav.makeNode("navBreak", "lineBreak", { spaces: 1 });

            dom.makeNode("break", "lineBreak", {});

            dom.makeNode("complete", "headerText", {
              text: "This proposal is complete.",
              css: "text-center",
            });

            dom.makeNode("contractBreak", "lineBreak", {});

            dom
              .makeNode("contract", "container", {
                css: "pda-container pda-Panel pda-panel-gray",
              })
              .makeNode("cont", "container", { css: "pda-container" });

            dom.contract.cont.makeNode("loading", "container", {});
            dom.contract.cont.loading.makeNode("text", "text", {
              text: "Loading completed proposal...",
              css: "text-center",
            });
            dom.contract.cont.loading.makeNode("loader", "loader", {});

            dom.patch();

            obj.pricing = _.find(obj.history, {
              contract: obj.contract.id.toString(),
            }).pricing;
            obj.sections = _.find(obj.history, {
              contract: obj.contract.id.toString(),
            }).sections;
            obj.menu = +_.find(obj.history, {
              contract: obj.contract.id.toString(),
            }).menu;
            obj.invoices = _.find(obj.history, {
              contract: obj.contract.id.toString(),
            }).invoices;

            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  id: {
                    type: "or",
                    values: Object.keys(obj.pricing),
                  },
                },
                instance: instance,
                objectType: "inventory_billable_categories",
                getChildObjs: 1,
              },
              function (categories) {
                obj.contract = obj.contract.id;

                viewProposalPDF(
                  obj,
                  categories,
                  obj.sections,
                  function (contractHtml) {
                    delete dom.contract.cont.loading;

                    dom.contract.cont.makeNode("btns", "buttonGroup", {
                      css: "pull-left",
                    });

                    dom.contract.cont.btns
                      .makeNode("download", "button", {
                        text: '<i class="fa fa-download"></i> Download a copy',
                        css: "pda-btn-primary",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function () {
                              var dom = this;

                              dom.contract.cont.btns.download.loading();

                              viewProposalPDF(
                                obj,
                                categories,
                                obj.sections,
                                function (done) {
                                  dom.contract.cont.btns.download.loading(
                                    false
                                  );
                                }
                              );
                            }.bind(dom),
                          },
                        },
                        sb.moduleId
                      );

                    dom.contract.cont.makeNode("btnBreak", "lineBreak", {});

                    dom.contract.cont.makeNode("text", "text", {
                      text: contractHtml,
                    });

                    dom.contract.cont.patch();
                  },
                  true
                );
              }
            );

            break;

          case "Signed":
            dom.cont.steps.makeNode("step1", "div", { css: "completed step" });
            dom.cont.steps.step1.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step1.content.makeNode("title", "div", {
              css: "title",
              text: "Review proposals",
            });
            dom.cont.steps.step1.content.makeNode("desc", "div", {
              css: "description",
              text: "Review and approve your proposals.",
            });

            dom.cont.steps.makeNode("step2", "div", { css: "completed step" });
            dom.cont.steps.step2.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step2.content.makeNode("title", "div", {
              css: "title",
              text: "Sign contract",
            });
            dom.cont.steps.step2.content.makeNode("desc", "div", {
              css: "description",
              text: "Sign the contract for the approved proposal.",
            });

            dom.cont.steps.makeNode("step3", "div", { css: "active step" });
            dom.cont.steps.step3.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step3.content.makeNode("title", "div", {
              css: "title",
              text: "Pay invoices",
            });
            dom.cont.steps.step3.content.makeNode("desc", "div", {
              css: "description",
              text: "Select and pay invoices.",
            });

            dom.makeNode("contBreak", "div", { text: "<br /><br />" });

            dom.makeNode("grid", "div", {
              css: "ui centered stackable grid container",
            });
            dom.grid
              .makeNode("propCol", "div", { css: "four wide column" })
              .makeNode("cont", "div", { css: "ui active loading segment" });
            dom.grid
              .makeNode("invoiceCol", "div", { css: "twelve wide column" })
              .makeNode("cont", "div", { css: "ui active loading segment" });

            dom.grid.propCol.cont.makeNode("title", "div", {
              css: "ui header",
              text: "Proposals",
            });
            dom.grid.propCol.cont.makeNode("titleBreak", "div", {
              text: "<br />",
            });
            dom.grid.invoiceCol.cont.makeNode("title", "div", {
              css: "ui header",
              text: "Inoivce List",
            });

            dom.patch();

            sb.data.db.controller(
              "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
              {
                queryObj: {
                  main_object: obj.id,
                  status: "Accepted",
                },
                instance: instance,
                objectType: "proposals",
                getChildObjs: 1,
              },
              function (accepted) {
                obj.proposal = accepted[0];

                invoiceView(dom.grid.invoiceCol.cont, obj, function (done) {
                  dom.grid.invoiceCol.cont.css("ui basic segment");
                });

                sb.data.db.controller(
                  "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                  {
                    queryObj: {
                      main_object: obj.id,
                      status: "Approved",
                    },
                    instance: instance,
                    objectType: "proposals",
                    getChildObjs: 1,
                  },
                  function (approved) {
                    var proposals = accepted.concat(approved);

                    smallProposalList(
                      dom.grid.propCol.cont,
                      proposals,
                      function (done) {
                        dom.grid.propCol.cont.css("ui basic segment");
                      }
                    );
                  }
                );
              }
            );

            break;

          default:
            // console.log('test');
            dom.cont.steps.makeNode("step1", "div", { css: "active step" });
            dom.cont.steps.step1.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step1.content.makeNode("title", "div", {
              css: "title",
              text: "Review proposals",
            });
            dom.cont.steps.step1.content.makeNode("desc", "div", {
              css: "description",
              text: "Review and approve your proposals.",
            });

            dom.cont.steps.makeNode("step2", "div", { css: "step" });
            dom.cont.steps.step2.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step2.content.makeNode("title", "div", {
              css: "title",
              text: "Sign contract",
            });
            dom.cont.steps.step2.content.makeNode("desc", "div", {
              css: "description",
              text: "Sign the contract for the approved proposal.",
            });

            dom.cont.steps.makeNode("step3", "div", { css: "step" });
            dom.cont.steps.step3.makeNode("content", "div", { css: "content" });
            dom.cont.steps.step3.content.makeNode("title", "div", {
              css: "title",
              text: "Pay invoices",
            });
            dom.cont.steps.step3.content.makeNode("desc", "div", {
              css: "description",
              text: "Select and pay invoices.",
            });

            dom.nav.makeNode("navBreak", "div", { text: "<br />" });

            proposalView(dom, obj);
        }

        callback(true);
      },
      sb.url + "/api/_getAdmin.php?do="
    );
  }

  function signContractView(dom, obj, categories) {
    function dataURItoBlob(dataURI) {
      // convert base64 to raw binary data held in a string
      // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
      var byteString = atob(dataURI.split(",")[1]);

      // separate out the mime component
      var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];

      // write the bytes of the string to an ArrayBuffer
      var ab = new ArrayBuffer(byteString.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }

      //Old Code
      //write the ArrayBuffer to a blob, and you're done
      //var bb = new BlobBuilder();
      //bb.append(ab);
      //return bb.getBlob(mimeString);

      //New Code
      return new Blob([ab], { type: mimeString });
    }

    function getConsent() {
      var contractId = 0;
      if (obj.contract) {
        contractId = obj.contract.id;
      }

      this.makeNode("consent", "div", {});

      this.consent.makeNode("break", "lineBreak", {});

      this.consent.makeNode("loading", "headerText", {
        text: "Loading consent form...",
        css: "text-center",
        size: "x-small",
      });

      this.consent.makeNode("loader", "loader", { size: "" });

      this.patch();

      var dom = this;

      sb.data.db.controller(
        "getAll&pagodaAPIKey=" + instance,
        { objectType: "contract_system" },
        function (systemSettings) {
          var systemSettings = systemSettings[0];

          //delete dom.break;
          delete dom.consent.loader;
          delete dom.consent.loading;

          dom.consent.makeNode("break", "div", { text: "<br /><br />" });

          dom
            .makeNode("col", "div", { css: "ui one column centered grid" })
            .makeNode("cont", "column", { w: 8 });

          dom.col.cont.makeNode("cont", "div", { css: "ui basic segment" });

          dom.col.cont.cont.makeNode("title", "headerText", {
            text: "Electronic Signature Disclaimer",
          });

          dom.col.cont.cont.makeNode("disclaimer", "text", {
            text: systemSettings.signature_disclaimer,
          });

          //dom.col.cont.cont.makeNode('break', 'lineBreak', {});

          dom.col.cont.cont.makeNode("form", "form", {
            name: {
              type: "text",
              name: "name",
              label: "Your Full Name",
            },
            email: {
              type: "text",
              name: "email",
              label:
                "Your Email Address (we will send a copy of the signed contract here)",
            },
          });

          dom.col.cont.cont.makeNode("formBreak", "div", { text: "<br />" });

          dom.col.cont.cont.makeNode("btns", "div", { css: "ui buttons" });

          dom.col.cont.cont.btns
            .makeNode("accept", "button", {
              text: '<i class="fa fa-check"></i> Accept & Continue',
              css: "pda-btn-green",
            })
            .notify(
              "click",
              {
                type: "invoicesPay-run",
                data: {
                  run: function () {
                    var formInfo = this.col.cont.cont.form.process();

                    if (formInfo.completed == false) {
                      sb.dom.alerts.alert(
                        "Error",
                        "Please enter your name.",
                        "error"
                      );
                      return;
                    }

                    fullName = formInfo.fields.name.value;
                    email = formInfo.fields.email.value;

                    this.col.cont.cont.btns.accept.loading();

                    obj.contract.signer_email = email;
                    obj.contract.signer_name = fullName;

                    delete this.col;

                    var dom = this;

                    sb.data.db.controller(
                      "getObjectById&pagodaAPIKey=" + instance,
                      { value: obj.id, type: obj.object_bp_type },
                      function (obj) {
                        viewContract(dom, obj);
                      }
                    );
                  }.bind(dom),
                },
              },
              sb.moduleId
            );

          dom.patch();
        },
        sb.url + "/api/_getAdmin.php?do="
      );
    }

    function getSignature(contract) {
      var modal = this.modals.modal,
        dom = this;

      contract.related_object = obj;

      dom.modals.empty();

      dom.modals.makeNode("signature", "modal", {
        onShow: function () {
          dom.modals.signature.footer.makeNode("btns", "buttonGroup", {
            css: "",
          });

          dom.modals.signature.body.makeNode("title", "div", {
            css: "ui huge header",
            text: "Create a signature",
          });

          dom.modals.signature.body.makeNode("cols", "div", { css: "ui grid" });

          //dom.modals.signature.body.cols.makeNode('col1', 'column', {width:3}).makeNode('cont', 'container', {css:'pda-container'}).makeNode('buttonBreak', 'lineBreak', {});
          dom.modals.signature.body.cols.makeNode("col2", "div", {
            css: "sixteen wide column",
          });

          //dom.modals.signature.body.cols.col1.makeNode('choiceBreak1', 'lineBreak', {});

          //dom.modals.signature.body.cols.col1.cont.makeNode('btns', 'buttonGroup', {});

          /*
					dom.modals.signature.body.cols.col1.cont.btns.makeNode('signature', 'div', {tag:'button', text:'Signature', css:'ui fluid blue button'}).notify('click', {
						type:'invoicesPay-run',
						data:{
							run:function(btns, footer){
		
								this.empty();
								
								this.makeNode('btns', 'buttonGroup', {css:'pull-right'}).makeNode('clear', 'button', {text:'<i class="fa fa-repeat"></i> Clear signature and start over', css:'pda-btnOutline-orange'}).notify('click', {
									type:'invoicesPay-run',
									data:{
										run:function(){
											
											signaturePad.clear();
											
										}
									}
								}, sb.moduleId);
								
								this.makeNode('btnsBreak', 'lineBreak', {});
								
								this.makeNode('sign', 'container', {css:'pda-container pda-background-blue pda-Panel', text:''});
								this.sign.makeNode('helperText', 'headerText', {text:'Sign below', css:'text-muted text-left', size:'x-small'});
								this.sign.makeNode('signingCanvas', 'text', {text:'<canvas id="signature-pad" class="signature-pad" width=700 height=200></canvas>'});
								this.patch();
								
								btns.signature.css('pda-btn-blue pda-btn-fullWidth');
								btns.printed.css('pda-btnOutline-blue pda-btn-fullWidth');
								
								var signaturePad = new SignaturePad(document.getElementById('signature-pad'), {
										backgroundColor: 'rgba(255, 255, 255, 0)',
										penColor: 'rgb(0, 76, 198)'
									});
									
								footer.makeNode('save', 'button', {text:'Save and continue <i class="fa fa-arrow-right"></i>', css:'pda-btn-green'}).notify('click', {
									type:'invoicesPay-run',
									data:{
										run:function(dom, contract, signaturePad){
											
											this.footer.btns.save.loading();
											
											if(signaturePad.isEmpty()){
												
												sb.dom.alerts.alert('Error', 'Please create a signature before continuing.', 'error');
												return;
												
											}
											
											saveSignature.call(this, dom, contract, signaturePad);
											
										}.bind(modal, dom, contract, signaturePad)
									}
								}, sb.moduleId);
								
								footer.patch();	
								
							}.bind(dom.modals.signature.body.cols.col2, dom.modals.signature.body.cols.col1.cont.btns, dom.modals.signature.footer.btns)
						}
					}, sb.moduleId);
*/

          /*
					modal.body.col1.cont.btns.makeNode('printed', 'button', {text:'Printed', size:'x-small', css:'pda-btn-fullWidth pda-btnOutline-blue'}).notify('click', {
						type:'invoicesPay-run',
						data:{
							run:function(btns, footer){
		
								this.empty();
								
								this.makeNode('break', 'lineBreak', {});
								
								this.makeNode('cont', 'container', {css:'pda-container pda-Panel pda-background-blue'});
								this.cont.makeNode('helperText', 'headerText', {text:'Signing as: '+ fullName, size:'x-small', css:'text-muted'});
								this.cont.makeNode('signature', 'headerText', {text:fullName, size:'x-large', css:'text-center handwritten'});
								
								this.patch();
								
								footer.makeNode('save', 'button', {text:'Save and continue <i class="fa fa-arrow-right"></i>', css:'pda-btn-green'}).notify('click', {
									type:'invoicesPay-run',
									data:{
										run:function(dom, contract){
											
											this.footer.btns.save.loading();
											
											useTextSignature.call(this, dom, contract);
											
										}.bind(modal, dom, contract)
									}
								}, sb.moduleId);
								
								footer.patch();
								
								btns.signature.css('pda-btnOutline-blue pda-btn-fullWidth');
								btns.printed.css('pda-btn-blue pda-btn-fullWidth');
								
							}.bind(modal.body.col2, modal.body.col1.cont.btns, modal.footer.btns)
						}
					}, sb.moduleId);
		*/

          dom.modals.signature.body.cols.col2.makeNode("btns", "buttonGroup", {
            css: "pull-right",
          });
          dom.modals.signature.body.cols.col2.makeNode(
            "btnsBreak",
            "lineBreak",
            {}
          );

          dom.modals.signature.body.cols.col2.makeNode("sign", "container", {
            css: "pda-container pda-background-blue pda-Panel",
            text: "",
          });
          dom.modals.signature.body.cols.col2.sign.makeNode(
            "helperText",
            "headerText",
            { text: "Sign below", css: "text-muted text-left", size: "x-small" }
          );
          dom.modals.signature.body.cols.col2.sign.makeNode(
            "signingCanvas",
            "div",
            {
              css: "ui tertiary inverted segment",
              text: '<canvas id="signature-pad" class="signature-pad" width=700 height=200></canvas>',
            }
          );

          dom.modals.signature.body.makeNode("finalBreak", "lineBreak", {});

          dom.modals.signature.body.patch();
          dom.modals.signature.footer.patch();

          setTimeout(function () {
            var signaturePad = new SignaturePad(
              document.getElementById("signature-pad"),
              {
                backgroundColor: "rgba(255, 255, 255, 0)",
                penColor: "rgb(0, 76, 198)",
              }
            );

            dom.modals.signature.body.cols.col2.btns
              .makeNode("clear", "button", {
                text: '<i class="fa fa-repeat"></i> Clear signature and start over',
                css: "pda-btnOutline-orange",
              })
              .notify(
                "click",
                {
                  type: "invoicesPay-run",
                  data: {
                    run: function () {
                      signaturePad.clear();
                    },
                  },
                },
                sb.moduleId
              );

            dom.modals.signature.body.cols.col2.btns.patch();

            dom.modals.signature.footer.btns
              .makeNode("save", "button", {
                text: 'Save and continue <i class="fa fa-arrow-right"></i>',
                css: "pda-btn-green",
              })
              .notify(
                "click",
                {
                  type: "invoicesPay-run",
                  data: {
                    run: function (dom, contract, signaturePad) {
                      dom.modals.signature.footer.btns.save.loading();

                      if (signaturePad.isEmpty()) {
                        sb.dom.alerts.alert(
                          "Error",
                          "Please create a signature before continuing.",
                          "error"
                        );

                        dom.modals.signature.footer.btns.save.loading(false);

                        return;
                      }

                      saveSignature.call(
                        dom.modals.signature,
                        dom,
                        contract,
                        signaturePad
                      );
                    }.bind(dom.modals.signature, dom, contract, signaturePad),
                  },
                },
                sb.moduleId
              );

            dom.modals.signature.footer.btns.patch();
          }, 0);
        },
      });

      dom.modals.patch();

      dom.modals.signature.show();
    }

    function saveSignature(dom, contract, signaturePad) {
      var managerEmail;
      var signature = signaturePad.toData(),
        imageBlob = dataURItoBlob(signaturePad.toDataURL()),
        fileMetaData = {
          fileType: "png",
          fileName: "contract-signature-" + contract.id + ".png",
          objectType: "contracts",
          objectId: contract.id,
          isPublic: 0,
        },
        modal = this,
        emails = {
          signer: contract.signer_email,
        },
        args = {},
        emailBatch = [];

      if (fileMetaData.objectType === "") {
        fileMetaData.objectType = "root";
      }
      if (isNaN(fileMetaData.objectId)) {
        fileMetaData.objectId = 0;
      }

      _.each(
        emails,
        function (email, owner) {
          args[owner] = {
            to: email,
            from: "<EMAIL>",
            subject: "Proposal Signed",
            mergevars: {
              TITLE: "Proposal Signed",
              BODY:
                "Proposal for <strong>" +
                contract.related_object.name +
                "</strong> has been signed by <strong>" +
                contract.signer_name +
                "</strong>" +
                " on <strong>" +
                moment().format("MMMM Do, YYYY h:mm:ss A") +
                "</strong>",
            },
          };

          emailBatch.push(args[owner]);
        },
        this
      );

      sb.comm.sendEmail(emailBatch, function (ret) {
        if (ret) {
          sb.data.files.changeInstance("rickyvoltz", "rickyvoltz");

          sb.data.files.upload(imageBlob, fileMetaData, function (savedFile) {
            sb.data.db.controller(
              "updateObject&api_webform=true&pagodaAPIKey=" + instance,
              {
                objectType: "contracts",
                objectData: { id: contract.id, signatures: savedFile.id },
              },
              function (updated) {
                sb.data.db.controller(
                  "getObjectById&api_webform=true&pagodaAPIKey=" + instance,
                  { value: contract.id, type: "contracts", childObjs: 4 },
                  function (contract1) {
                    sb.data.db.controller(
                      "getObjectById&api_webform=true&pagodaAPIKey=" + instance,
                      { value: contract.related_object.id, type: "proposals" },
                      function (propObj) {
                        viewProposalPDF(
                          propObj,
                          categories,
                          propObj.sections,
                          function (merged) {
                            contract.html_string_merged = merged;
                            contract.signatures = savedFile;

                            dom.modals.signature.hide();

                            dom.cont.btns.pdf.css(
                              "pda-btn-disabled pda-btn-blue"
                            );
                            dom.cont.btns.sign.css(
                              "pda-btn-disabled pda-btn-green"
                            );
                            dom.cont.btns.sign.text(
                              '<i class="fa fa-pencil"></i> Signing'
                            );

                            contract.html_string_merged =
                              contract.html_string_merged.replace(
                                new RegExp("PLEASE SIGN HERE", "g"),
                                "{{PLEASE SIGN HERE}}"
                              );

                            scanDocumentForSignatureLines.call(
                              this,
                              dom,
                              contract
                            );
                          },
                          true
                        );
                      }
                    );
                  }
                );
              }
            );
          });
        }
      });
    }

    function scanDocumentForSignatureLines(dom, contract, tour) {
      contract.html_string = contract.html_string_merged;
      contract.html_string_merged = contract.html_string_merged.replace(
        new RegExp("{{PLEASE SIGN HERE}}"),
        '<span id="signatureLine" class="text-warning signatureLine" style="font-weight:bold; font-size:18px;">SIGN HERE</span>'
      );

      dom.cont.contractContainer.makeNode("contract", "text", {
        text:
          '<div class="pda-container pda-Panel contractBox ui basic segment" style="max-width:850px; display:block; margin:0 auto;">' +
          contract.html_string_merged +
          "</div>",
      });
      dom.cont.contractContainer.patch();

      if ($(".signatureLine").get(0)) {
        $(".mainCanvas").animate(
          {
            scrollTop:
              $(".mainCanvas").scrollTop() -
              100 +
              $("#signatureLine").offset().top -
              $(".mainCanvas").offset().top,
          },
          {
            duration: 1000,
            specialEasing: {
              width: "linear",
              height: "easeOutBounce",
            },
            complete: function (e) {
              if (!tour) {
                var tour = {
                  id: "hello-hopscotch",
                  showNextButton: false,
                  smoothScroll: false,
                  steps: [
                    {
                      title: "Place Your Signature",
                      content:
                        '<button class="placeSignatureButton btn pda-btn-green pda-btn-full-width">PLACE YOUR SIGNATURE HERE</button>',
                      target: "signatureLine",
                      placement: "right",
                      onShow: function () {
                        $(".placeSignatureButton").on("click", function () {
                          $(".placeSignatureButton").html(
                            '<i class="fa fa-circle-o-notch fa-spin"></i> Placing Signature'
                          );

                          setTimeout(function () {
                            contract.html_string_merged =
                              contract.html_string.replace(
                                new RegExp("{{PLEASE SIGN HERE}}"),
                                '<img width="300px" src="' +
                                  sb.data.files.getURL(contract.signatures) +
                                  '"> - ' +
                                  fullName +
                                  " @ " +
                                  moment().format("M/DD/YYYY h:mm:ss a")
                              );
                            contract.html_string = contract.html_string_merged;

                            dom.cont.contractContainer.makeNode(
                              "contract",
                              "text",
                              {
                                text:
                                  '<div class="pda-container pda-Panel" style="max-width:850px; display:block; margin:0 auto;">' +
                                  contract.html_string_merged +
                                  "</div>",
                              }
                            );

                            dom.cont.contractContainer.patch();

                            hopscotch.endTour();

                            scanDocumentForSignatureLines(dom, contract, tour);
                          }, 1000);
                        });
                      },
                    },
                  ],
                };
              }

              hopscotch.startTour(tour);
            },
          }
        );
      } else {
        setTimeout(function () {
          $(".mainCanvas").animate(
            {
              scrollTop: $(".contractBox").scrollTop(),
            },
            {
              duration: 1000,
              specialEasing: {
                width: "linear",
                height: "easeOutBounce",
              },
              complete: function (e) {
                sb.data.db.controller(
                  "getIPAddress&api_web_form=true&pagodaAPIKey=" + instance,
                  {},
                  function (ip) {
                    var updateContract = {
                      id: contract.id,
                      signer_ip: ip,
                      status: "Signed",
                      signer_name: fullName,
                      signer_email: email,
                    };

                    sb.data.db.controller(
                      "updateObject&api_web_form=true&pagodaAPIKey=" + instance,
                      { objectType: "contracts", objectData: updateContract },
                      function (updated) {
                        sb.data.db.controller(
                          "updateObject&api_web_form=true&pagodaAPIKey=" +
                            instance,
                          {
                            objectType: "proposals",
                            objectData: {
                              id: contract.related_object.id,
                              status: "Signed",
                            },
                          },
                          function (updatedProposal) {
                            location.reload();
                          }
                        );
                      }
                    );
                  }
                );
              },
            }
          );
        }, 300);
      }
    }

    function useTextSignature(dom, contract, tour) {
      var imageBlob = "",
        signatureElement = document.getElementsByClassName(
          dom.modals.modal.body.col2.cont.signature.selector
        ),
        fileMetaData = {
          fileType: "png",
          fileName: "contract-signature-" + contract.id,
          objectType: contract.contract_types.contract_object_type,
          objectId: contract.related_object,
          parent: contract.related_object,
          isPublic: 0,
        },
        modal = this;

      if (fileMetaData.objectType === "") {
        fileMetaData.objectType = "root";
      }
      if (isNaN(fileMetaData.objectId)) {
        fileMetaData.objectId = 0;
      }

      appConfig.instance = contract.instance;

      sb.data.files.upload(imageBlob, fileMetaData, function (savedFile) {
        sb.data.db.controller(
          "updateObject&pagodaAPIKey=contracts",
          {
            objectType: "contracts",
            objectData: { id: contract.id, signatures: savedFile.id },
          },
          function (updated) {
            sb.data.db.controller(
              "getObjectById&pagodaAPIKey=" + instance,
              { value: contract.id, type: "contracts", childObjs: 4 },
              function (contract) {
                modal.hide(function (hidden) {
                  dom.cont.btns.pdf.css("pda-btn-disabled pda-btn-blue");
                  dom.cont.btns.sign.css("pda-btn-disabled pda-btn-green");
                  dom.cont.btns.sign.text(
                    '<i class="fa fa-pencil"></i> Signing'
                  );

                  mergeString(contract, function (merged) {
                    contract.html_string_merged = merged;

                    scanDocumentForSignatureLines.call(this, dom, contract);
                  });
                });
              }
            );
          },
          sb.url + "/api/_getAdmin.php?do="
        );
      });
    }

    function viewContract(dom, obj) {
      sb.data.db.controller(
        "getObjectById&pagodaAPIKey=" + instance,
        { value: obj.contract, type: "contracts", childObjs: 4 },
        function (contract) {
          viewProposalPDF(
            obj,
            categories,
            obj.sections,
            function (merged) {
              if (contract.status === "Signeds") {
                dom.empty();

                dom.makeNode("col", "column", { width: 6, offset: 3 });

                dom.col.makeNode("cont", "container", {
                  css: "pda-container pda-Panel",
                });

                dom.col.cont.makeNode("title", "headerText", {
                  text: "Signing Complete!",
                  css: "text-center",
                });

                dom.col.cont.makeNode("break1", "lineBreak", {});

                dom.col.cont.makeNode("download", "headerText", {
                  text: "Download a copy for your records.",
                  css: "text-center",
                  size: "small",
                });

                dom.col.cont.makeNode("btns", "buttonGroup", { css: "" });
                dom.col.cont.btns
                  .makeNode("pdf", "button", {
                    text: '<i class="fa fa-download"></i> Download',
                    css: "pda-btn-blue",
                  })
                  .notify(
                    "click",
                    {
                      type: "invoicesPay-run",
                      data: {
                        run: function (contract) {
                          var dom = this;

                          sb.data.makePDF(merged, "D");
                        }.bind(dom, contract, merged),
                      },
                    },
                    sb.moduleId
                  );

                dom.patch();
              } else {
                sb.data.db.controller(
                  "updateObject&api_webform=true&pagodaAPIKey=" + instance,
                  {
                    objectType: "contracts",
                    objectData: {
                      id: contract.id,
                      status: "Signing In Progress",
                    },
                  },
                  function (statusChanged) {
                    contract.html_string_merged = merged;

                    dom.makeNode("cont", "div", { css: "" });

                    dom.cont.makeNode("btns", "div", {
                      css: "buttonGroupBar ui buttons",
                    });

                    dom.cont.btns
                      .makeNode("sign", "button", {
                        text: '<i class="fa fa-pencil"></i> Ready To Sign',
                        css: "pda-btn-green pda-btn-",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: getSignature.bind(dom, contract),
                          },
                        },
                        sb.moduleId
                      );

                    dom.cont.btns
                      .makeNode("pdf", "button", {
                        text: '<i class="fa fa-download"></i> Download PDF',
                        css: "pda-btn-blue pda-btn-",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function (contract) {
                              this.cont.btns.pdf.loading();

                              var dom = this;

                              mergeString(contract, function (merged) {
                                sb.data.makePDF(merged, "D");

                                dom.cont.btns.pdf.loading(false);
                              });
                            }.bind(dom, contract),
                          },
                        },
                        sb.moduleId
                      );

                    dom.cont
                      .makeNode("contractContainer", "div", {
                        css: "animated fadeIn",
                      })
                      .makeNode("contract", "div", {
                        css: "ui basic segment",
                        text: merged,
                        style:
                          "max-width:850px; display:block; margin:3em auto;",
                      });

                    dom.patch();
                  }
                );
              }
            },
            true
          );
        }
      );
    }

    dom
      .makeNode("modals", "div", {})
      .makeNode("modal", "modal", { size: "large" });

    getConsent.call(dom);
  }

  function smallProposalList(dom, objs, callback) {
    dom.empty();

    dom.makeNode("break", "div", { text: "<br />" });

    dom.makeNode("cards", "div", { css: "ui cards" });

    _.each(_.sortBy(objs, "date_created").reverse(), function (obj) {
      var labelColor = "";
      switch (obj.status) {
        case "Accepted":
        case "Approved":
          labelColor = "green";
          break;

        case "Signed":
          labelColor = "green";

          break;
      }

      dom
        .makeNode("card" + obj.id, "div", { css: "ui card" })
        .makeNode("content", "div", { css: "content" });

      dom["card" + obj.id].content.makeNode("name", "div", {
        css: "header",
        text: obj.name,
      });
      dom["card" + obj.id].content.makeNode("created", "div", {
        css: "description",
        text: "Created on " + moment(obj.date_created).format("M/D/YYYY"),
      });
      dom["card" + obj.id].content.makeNode("price", "div", {
        css: "description",
        text:
          "Total Price: $" +
          (
            _.reduce(
              obj.pricing,
              function (memo, price, cat) {
                return memo + +price;
              },
              0
            ) / 100
          ).formatMoney(),
      });
      dom["card" + obj.id].content.makeNode("state", "div", {
        css: "ui " + labelColor + " center aligned label",
        text: obj.status,
      });
      dom["card" + obj.id]
        .makeNode("bottom", "div", {
          css: "ui bottom attached blue button",
          text: "View PDF",
        })
        .notify(
          "click",
          {
            type: "invoicesPay-run",
            data: {
              run: function (dom, obj) {
                dom["card" + obj.id].bottom.loading();

                sb.data.db.controller(
                  "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                  {
                    queryObj: {
                      id: {
                        type: "or",
                        values: Object.keys(obj.pricing),
                      },
                    },
                    instance: instance,
                    objectType: "inventory_billable_categories",
                    getChildObjs: 1,
                  },
                  function (categories) {
                    viewProposalPDF(
                      obj,
                      categories,
                      obj.sections,
                      function (done) {
                        dom["card" + obj.id].bottom.loading(false);
                      }
                    );
                  },
                  sb.url + "/api/_getAdmin.php?do="
                );
              }.bind({}, dom, obj),
            },
          },
          sb.moduleId
        );
    });

    dom.patch();

    callback(true);
  }

  function viewProposalPDF(obj, categories, sections, callback, htmlOnly) {
    var fullHTMLString = "",
      totalPrice = 0;

    sb.data.db.obj.getById(
      "work_orders",
      obj.main_object,
      function (workorder_obj) {
        sb.data.db.obj.getById("contracts", obj.contract, function (contract) {
          sb.data.db.obj.getWhere(
            "invoices",
            { related_object: obj.id },
            function (invoices) {
              sb.data.db.obj.getWhere(
                "inventory_menu",
                { id: obj.menu, childObjs: 2 },
                function (menus) {
                  sb.data.db.obj.getWhere(
                    "shifts",
                    {
                      staff_schedules: +obj.schedule,
                      childObjs: {
                        service: {
                          name: true,
                        },
                      },
                    },
                    function (shifts) {
                      // get menu w/line item prices
                      sb.notify({
                        type: "get-menu-total-pricing",
                        data: {
                          menu: menus[0],
                          obj: obj,
                          pricing: function (pricelist) {
                            sb.notify({
                              type: "get-menu-line-item-pricing",
                              data: {
                                menu: menus[0],
                                obj: obj,
                                workorder: workorder_obj,
                                callback: function (pricedMenu) {
                                  if (sections.contract == "Yes") {
                                    if (contract) {
                                      fullHTMLString += contract.html_string;
                                    } else {
                                      fullHTMLString +=
                                        "<h3>No contract selected</h3>";
                                    }

                                    fullHTMLString += "<br /><br /><br />";
                                  }

                                  if (sections.itemList == "Yes") {
                                    if (sections.contract == "Yes") {
                                      fullHTMLString += "<pagebreak />";
                                    }

                                    if (
                                      sectionTypes.menu.getHTML(
                                        pricedMenu,
                                        shifts,
                                        sections,
                                        { includeDiscounts: true }
                                      )
                                    ) {
                                      fullHTMLString +=
                                        sectionTypes.menu.getHTML(
                                          pricedMenu,
                                          shifts,
                                          sections,
                                          { includeDiscounts: true }
                                        );
                                      fullHTMLString += "<br />";
                                      fullHTMLString += "{{PLEASE SIGN HERE}}";
                                      fullHTMLString += "<br /><br />";
                                    }
                                  }

                                  if (
                                    sections.itemListBeforeDiscounts == "Yes"
                                  ) {
                                    if (sections.contract == "Yes") {
                                      fullHTMLString += "<pagebreak />";
                                    }

                                    if (
                                      sectionTypes.menu.getHTML(
                                        pricedMenu,
                                        shifts,
                                        sections,
                                        { includeDiscounts: false }
                                      )
                                    ) {
                                      fullHTMLString +=
                                        sectionTypes.menu.getHTML(
                                          pricedMenu,
                                          shifts,
                                          sections,
                                          { includeDiscounts: false }
                                        );
                                      fullHTMLString += "<br />";
                                      fullHTMLString += "{{PLEASE SIGN HERE}}";
                                      fullHTMLString += "<br /><br />";
                                    }
                                  }

                                  if (sections.pricingTable == "Yes") {
                                    if (sections.itemList == "Yes") {
                                      fullHTMLString += "<pagebreak />";
                                    }

                                    fullHTMLString += "<h3>Pricing</h3>";

                                    fullHTMLString +=
                                      '<table style="width:100%; border-collapse: collapse;">';

                                    _.each(pricelist, function (price, cat) {
                                      var category = _.find(categories, {
                                        id: +cat,
                                      });

                                      if (category) {
                                        if (sections.pricingTableCategories) {
                                          if (
                                            sections.pricingTableCategories[
                                              cat.toString()
                                            ] == true
                                          ) {
                                            fullHTMLString +=
                                              '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
                                              category.name +
                                              '</td> <td style="padding:5px;">$' +
                                              (price / 100).formatMoney() +
                                              "</td> </tr>";

                                            totalPrice += price;
                                          }
                                        } else {
                                          fullHTMLString +=
                                            '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
                                            category.name +
                                            '</td> <td style="padding:5px;">$' +
                                            (price / 100).formatMoney() +
                                            "</td> </tr>";

                                          totalPrice += price;
                                        }
                                      }
                                    });

                                    if (shifts && sections.labor) {
                                      var services = _.pluck(shifts, "service");
                                      var totalShifts = 0;
                                      var shiftsTotal = 0;
                                      var sectionTotal = 0;
                                      var total = 0;

                                      _.each(
                                        obj.pricing,
                                        function (price, serviceId) {
                                          var service = _.findWhere(services, {
                                            id: +serviceId,
                                          });

                                          if (service) {
                                            totalShifts++;
                                            shiftsTotal += +price;
                                            totalPrice += +price;
                                          }
                                        }
                                      );

                                      fullHTMLString +=
                                        '<tr style="border:1px solid gray;"> <td style="padding:5px;">Labor</td> <td style="padding:5px;">$' +
                                        (shiftsTotal / 100).formatMoney() +
                                        "</td> </tr>";
                                    }

                                    fullHTMLString +=
                                      '<tr style="border:1px solid gray; font-weight:bold;"> <td style="padding:5px; font-weight:bold; font-size:18px;">TOTAL</td> <td style="padding:5px; font-weight:bold; font-size:16px;">$' +
                                      (totalPrice / 100).formatMoney() +
                                      "</td> </tr>";

                                    fullHTMLString += "</table>";

                                    fullHTMLString += "<br /><br />";
                                    fullHTMLString += "{{PLEASE SIGN HERE}}";
                                    fullHTMLString += "<br /><br /><br />";
                                  }

                                  if (sections.invoiceTable == "Yes") {
                                    if (sections.pricingTable == "Yes") {
                                      fullHTMLString += "<pagebreak />";
                                    }

                                    fullHTMLString += "<h3>Invoices</h3>";

                                    fullHTMLString +=
                                      '<table style="width:100%; border-collapse: collapse;">';

                                    fullHTMLString +=
                                      '<tr style="border:1px solid gray; font-size:12px; font-weight:bold;"> <td style="padding:5px;">INVOICE NAME</td> <td style="padding:5px; text-align:right;">DUE DATE</td> <td style="padding:5px; text-align:right;">BALANCE DUE</td> </tr>';

                                    _.each(
                                      _.sortBy(invoices, "due_date"),
                                      function (inv) {
                                        fullHTMLString +=
                                          '<tr style="border:1px solid gray;"> <td style="padding:5px;">' +
                                          inv.name +
                                          '</td> <td style="padding:5px; text-align:right;">' +
                                          moment(inv.due_date).format(
                                            "M/D/YYYY"
                                          ) +
                                          '</td> <td style="padding:5px; text-align:right;">$' +
                                          (inv.balance / 100).formatMoney() +
                                          "</td> </tr>";
                                      }
                                    );

                                    fullHTMLString += "</table>";

                                    fullHTMLString += "<br /><br />";
                                    fullHTMLString += "{{PLEASE SIGN HERE}}";
                                    fullHTMLString += "<br /><br /><br />";
                                  }

                                  if (!contract) {
                                    contract = {};
                                  }

                                  contract.html_string = fullHTMLString;
                                  contract.related_object = obj.main_object;

                                  createMergedHTML(
                                    contract,
                                    obj.object_bp_type,
                                    function (contractHTML) {
                                      if (htmlOnly === true) {
                                        if (callback) {
                                          callback(contractHTML);
                                        }
                                      } else {
                                        sb.data.makePDF(contractHTML, "I");

                                        if (callback) {
                                          callback(contractHTML);
                                        }
                                      }
                                    }
                                  );
                                },
                              },
                            });
                          },
                        },
                      });
                    }
                  );
                }
              );
            }
          );
        });
      },
      {
        main_object: {
          main_contact: true,
        },
      }
    );
  }

  // For dev testing
  function script() {
    sb.data.db.obj.getAll("proposals", function (list) {
      /* console.log('list:: ', list); */

      var ids = [];

      _.each(list, function (proposal) {
        ids.push(proposal.id);
      });

      // console.log('ids:: ', ids);

      sb.data.db.obj.erase("", ids, function (res) {
        // console.log('res:: ', res);
      });
    });
  }

  return {
    init: function () {
      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            id: "proposals",
            title: "Proposals",
            icon: '<i class="fa fa-usd"></i>',
            afterLoad: function (state) {
              sb.data.db.obj.getWhere(
                "work_orders",
                { status: "Approval" },
                function (tasksList) {
                  sb.notify({
                    type: "app-update-quickAction",
                    data: {
                      itemId: "proposals",
                      viewObj: {
                        id: "qa-proposals",
                        icon:
                          '<i class="fa fa-usd"></i> <span class="ui red tiny label">' +
                          tasksList.length +
                          "</span>",
                        css: "",
                      },
                    },
                  });
                }
              );
            },
            views: [
              {
                id: "table",
                default: true,
                type: "table",
                title: "All Proposals",
                icon: '<i class="fa fa-th-list"></i>',
                setup: {
                  tableTitle: '<i class="fa fa-usd"></i> All Proposals',
                  objectType: "proposals",
                  searchObjects: [
                    {
                      name: "Name",
                      value: "name",
                    },
                    {
                      name: "ID",
                      value: "object_uid",
                    },
                    {
                      name: "Status",
                      value: "status",
                    },
                  ],
                  /*
									filters:{
										
										status:{
											name:'Status',
											type:'checkbox',
											all:true,
											field:'status',
											options:[
												{
													name:'status',
													label:'Proposal',
													value:'Proposal',
													checked:true
												},
												{
													name:'status',
													label:'Sent',
													value:'Sent',
													checked:true
												},
												{
													name:'status',
													label:'Accepted',
													value:'Accepted',
													checked:true
												},
												{
													name:'status',
													label:'Signed',
													value:'Signed',
													checked:true
												},
												{
													name:'status',
													label:'Paid',
													value:'Paid',
													checked:true
												},
												{
													name:'status',
													label:'Complete',
													value:'Complete',
													checked:true
												}
											]
										}
										
									},
									*/
                  filters: function (callback) {
                    callback({
                      status: {
                        name: "Workorder Status",
                        type: "checkbox",
                        field: "status",
                        options: [
                          {
                            name: "status",
                            label: "Proposal",
                            value: "Proposal",
                            checked: true,
                          },
                          {
                            name: "status",
                            label: "Sent",
                            value: "Sent",
                            checked: true,
                          },
                          {
                            name: "status",
                            label: "Accepted",
                            value: "Accepted",
                            checked: true,
                          },
                          {
                            name: "status",
                            label: "Signed",
                            value: "Signed",
                            checked: true,
                          },
                          {
                            name: "status",
                            label: "Paid",
                            value: "Paid",
                            checked: true,
                          },
                          {
                            name: "status",
                            label: "Complete",
                            value: "Complete",
                            checked: true,
                          },
                        ],
                      },
                    });
                  },
                  headerButtons: {
                    reload: {
                      name: "Reload",
                      css: "pda-btn-blue",
                      action: function () {},
                    },
                  },
                  rowSelection: true,
                  multiSelectButtons: {
                    erase: {
                      name: '<i class="fa fa-trash-o"></i> Delete',
                      css: "pda-btn-red",
                      domType: "erase",
                      action: "erase",
                    },
                    edit: {
                      name: '<i class="fa fa-plus"></i> Add Templates',
                      css: "pda-btn-green",
                      domType: "none",
                      action: function (selectedObjs, dom) {
                        if (
                          Array.isArray(selectedObjs) &&
                          selectedObjs.length > -1
                        ) {
                          _.each(selectedObjs, function (obj) {
                            if (obj.is_template != 1) {
                              obj.is_template = 1;

                              sb.data.db.obj.update(
                                "work_orders",
                                obj,
                                function (response) {
                                  if (!response) {
                                    sb.dom.alerts.alert(
                                      "Error",
                                      "Oops. Something went wrong.",
                                      "error"
                                    );
                                  }
                                }
                              );
                            }
                          });
                        } else {
                          sb.dom.alerts.alert(
                            "Error",
                            "Oops. Something went wrong.",
                            "error"
                          );
                        }

                        sb.notify({
                          type: "app-navigate-to",
                          data: {
                            itemId: "workorders",
                            viewId: "templateTable",
                            redraw: true,
                          },
                        });
                      },
                    },
                  },
                  visibleCols: {
                    start_date: "Date",
                    object_uid: "ID",
                    name: "Name",
                    type: "Type",
                    status: "Status",
                  },
                  cells: {
                    start_date: function (obj) {
                      return moment(obj.start_date).format("M/D/YYYY h:mm a");
                    },
                    manager: function (obj) {
                      var manager = "Not selected";

                      if (obj.manager) {
                        if (obj.manager.fname) {
                          manager = obj.manager.fname + " " + obj.manager.lname;
                        }
                      }

                      return manager;
                    },
                    type: function (obj) {
                      if (obj.type) {
                        return obj.type.name;
                      } else {
                        return "Not selected";
                      }
                    },
                    status: function (obj) {
                      var css = "label-warning";

                      switch (obj.status) {
                        case "Paid":
                          css = "label-success";

                          break;
                      }

                      if (obj.status) {
                        return (
                          '<label class="label ' +
                          css +
                          '">' +
                          obj.status +
                          "</label>"
                        );
                      } else {
                        return '<label class="label label-danger">Proposal</label>';
                      }
                    },
                    main_contact: function (obj) {
                      var mainContact = "No Contact on File";

                      if (obj.main_contact != null) {
                        mainContact =
                          obj.main_contact.fname + " " + obj.main_contact.lname;
                      }

                      return mainContact;
                    },
                  },
                  rowLink: {
                    type: "tab",
                    header: function (obj) {
                      return obj.name;
                    },
                    action: function (dom, state, draw) {},
                  },
                  childObjs: 2,
                  data: function (paged, callback) {
                    sb.data.db.obj.getAll(
                      "proposals",
                      function (ret) {
                        callback(ret);
                      },
                      2,
                      paged
                    );
                  },
                },
              },
              {
                id: "qa-proposals",
                type: "quickAction",
                icon: '<i class="fa fa-usd"></i> <span class="ui grey tiny label"><i class="notched circle loading icon"></i></span>',
                dom: function (dom, state, draw) {
                  draw({
                    dom: dom,
                    after: function (dom) {
                      sb.data.db.obj.getWhere(
                        "work_orders",
                        { status: "Approval" },
                        function (props) {
                          dom.makeNode("title", "div", {
                            text:
                              "There are " +
                              props.length +
                              " proposal(s) that need approval.",
                            css: "ui huge header",
                          });

                          dom
                            .makeNode("go", "div", {
                              css: "ui centered huge green button",
                              text: "Go to approval screen",
                              style: "margin:0 auto;",
                            })
                            .notify(
                              "click",
                              {
                                type: "proposals-run",
                                data: {
                                  run: function () {
                                    dom.closeView();

                                    sb.notify({
                                      type: "app-navigate-to",
                                      data: {
                                        itemId: "proposals",
                                        viewId: "needReview",
                                      },
                                    });
                                  },
                                },
                              },
                              sb.moduleId
                            );

                          dom.patch();
                        }
                      );
                    },
                  });
                },
              },
              /*
							{
								id:'proposalTool',
								type:'tool',
								name:'Proposals',
								default:true,
								tip:'Use the <b>Contracts</b>, <b>Line Items</b>, and <b>Invoices</b> tools, then create the first proposal.',
								icon: {
									type: 'inbox',
									color: 'olive'	
								},
								mainViews:[
									{
										dom:function(dom, state, draw){
										
											draw({
												dom:dom,
												after:function(dom){
													
													sb.data.db.obj.getAll('inventory_billable_categories', function(categories){
														estimateView(dom, state.project, {}, categories, {});
													});
													
												}
											});
																				
										}
									}
								],
								boxViews:[
									{
										dom:function(dom, state, draw){
										
										sb.data.db.obj.getWhere('proposals', {main_object:state.project.id, childObjs:1}, function(ret){							
																						
											if(ret.length > 1){
												
												var activeProp = _.findWhere(ret, {id:state.project.proposal.id});
												
												dom.makeNode('active', 'div', {css:'ui large header', text:'<small><small>Active proposal</small></small><br />'+ state.project.proposal.name});
												dom.makeNode('stat', 'div', {css:'ui header', text:(ret.length) +' total proposals'});
										
												draw(dom);
												
											}else{
												
												draw(false);
												
											}
											
										});
										
									}
									}
								]
							},
*/
              /*
							{
								id:'propTool',
								type:'hqTool',
								icon:{type:'inbox', color:'olive'},
								name:'Proposals',
								tip:'Use the <b>Contracts</b>, <b>Line Items</b>, and <b>Invoices</b> tools, then create the first proposal.',
								default:true,
								newButton:{
									text:'',
									action:function(){}
								},
								settings:[
									{
										name:'5. Proposal Email Template',
										object_type:'contract_settingss'
									}
								],
								mainViews:[
									{
										dom:function(dom, state, draw){
											
											sb.notify({
												type:'show-collection',
												data:{
													domObj:dom,
													state:state,
													objectType:'proposals',
													actions:{
														view:true
														// need to add a create action?
													},
													fields:{
														name:{
															title:'Name'
														},
														object_uid:{
															title:'ID'
														},
														type:{
															title:'Type',
															view:function(domObj, object){
																
																if(object.type){
																	
																	domObj.makeNode('type', 'div', {text:object.type.name});
																	
																}else{
																	
																	domObj.makeNode('type', 'div', {text:'Not Selected'});
																	
																}
																
																domObj.patch();
																
															}
														},
														status:{
															title:'Status',
															view:function(domObj, object){
																
																var css = 'label-warning';
																
																switch(object.status){
																	
																	case 'Paid':
																	
																		css = 'label-success';
																	
																		break;
																	
																}
																
																if(object.status){
							
																	domObj.makeNode('status', 'div', {text:'<label class="ui label '+ css +'">'+ object.status +'</label>'});
																	
																}else{
																	
																	domObj.makeNode('status', 'div', {css:'center aligned', text:'<label class="ui label label-danger">Proposal</label>'});
																}
																
																domObj.patch();
																
															}
														},
														start_date:{
															title:'Date',
															view:function(domObj, object){
																
																domObj.makeNode('start_date', 'div', {text:moment(object.date_created).format('M/D/YYYY')});
																domObj.patch();
																
															}
														}
														
													},
													fullView:{
														id:'propTool',
														type:'hqTool'
													},
													groupings:{
														status:'Status'
													},
													singleView:{
														view:function(ui, obj, draw){
															console.log(obj);
															sb.data.db.obj.getAll('inventory_billable_categories', function(categories){
																estimateView(ui, obj, {}, categories, {});
															});
															
														},
														select:2
													},
													state:state
												}
											});
											
										}
									}
								]
							},
*/
              /*
							{
								id:'propTool',
								type:'teamTool',
								icon:{type:'inbox', color:'olive'},
								name:'Proposals',
								tip:'Use the <b>Contracts</b>, <b>Line Items</b>, and <b>Invoices</b> tools, then create the first proposal.',
								default:true,
								newButton:{
									text:'',
									action:function(){}
								},
								mainViews:[
									{
										dom:function(dom, state, draw){
											
											sb.notify({
												type:'show-collection',
												data:{
													domObj:dom,
													state:state,
													objectType:'proposals',
													actions:{
														view:true
														// need to add a create action?
													},
													fields:{
														name:{
															title:'Name'
														},
														object_uid:{
															title:'ID'
														},
														type:{
															title:'Type',
															view:function(domObj, object){
																
																if(object.type){
																	
																	domObj.makeNode('type', 'div', {text:object.type.name});
																	
																}else{
																	
																	domObj.makeNode('type', 'div', {text:'Not Selected'});
																	
																}
																
																domObj.patch();
																
															}
														},
														status:{
															title:'Status',
															view:function(domObj, object){
																
																var css = 'label-warning';
																
																switch(object.status){
																	
																	case 'Paid':
																	
																		css = 'label-success';
																	
																		break;
																	
																}
																
																if(object.status){
							
																	domObj.makeNode('status', 'div', {text:'<label class="ui label '+ css +'">'+ object.status +'</label>'});
																	
																}else{
																	
																	domObj.makeNode('status', 'div', {css:'center aligned', text:'<label class="ui label label-danger">Proposal</label>'});
																}
																
																domObj.patch();
																
															}
														},
														start_date:{
															title:'Date',
															view:function(domObj, object){
																
																domObj.makeNode('start_date', 'div', {text:moment(object.date_created).format('M/D/YYYY')});
																domObj.patch();
																
															}
														}
														
													},
													fullView:{
														id:'propTool',
														type:'teamTool'
													},
													groupings:{
														status:'Status'
													},
													singleView:{
														view:function(ui, obj, draw){
																														
															sb.data.db.obj.getAll('inventory_billable_categories', function(categories){
																estimateView(ui, obj, {}, categories, {});
															});
															
														},
														select:2
													},
													state:state
												}
											});
											
										}
									}
								]
							},
*/
            ],
          },
        },
      });

      components.table = sb.createComponent("crud-table");
      components.approvalTable = sb.createComponent("crud-table");
      components.templateSelectionTable = sb.createComponent("crud-table");
      // 			components.menuBuilder = sb.createComponent('inventory');
      components.tags = sb.createComponent("tags");
      //components.emails = sb.createComponent('emails');
      components.workflows = sb.createComponent("workflows");

      sb.listen({
        "proposals-run": this.run,
        "register-proposal-section-type": this.registerProposalSectionType,
        "start-estimate-view": this.startEstimateView,
        "start-client-portal": this.startClientPortal,
        "invoicesPay-run": this.run,
        "client-invoice-payment-completed": this.paymentCompleted,
      });
    },

    registerProposalSectionType: function (setup) {
      sectionTypes[setup.name] = {
        getHTML: setup.getHTML,
      };
    },

    run: function (data) {
      data.run(data);
    },

    showObjectView: function (data) {
      var dom = data.domObj,
        pagedObj = {
          page: 0,
          pageLength: 6,
          paged: true,
          sortCast: "string",
          sortCol: "date_created",
          sortDir: "desc",
          objectType: "work_orders",
        };

      sb.data.db.obj.getWhere(
        "work_orders",
        { main_contact: data.contact.id, childObjs: 2 },
        function (resp) {
          sb.data.db.obj.getWhere(
            "work_orders",
            {
              additional_contacts: { type: "contains", value: data.contact.id },
              childObjs: 2,
            },
            function (addObjs) {
              contactView(
                dom,
                {
                  data: resp.concat(addObjs),
                  contact: data.contact,
                },
                data.draw
              );
            }
          );
        }
      );
    },

    start: function (data) {
      components.table = sb.createComponent("crud-table");
      // 			components.menuBuilder = sb.createComponent('inventory');
      //components.contracts = sb.createComponent('contractsComponent');
      //components.files = sb.createComponent('file-nav');
      components.tags = sb.createComponent("tags");

      if (data.objectId) {
        objectId = data.objectId;
      }
      if (data.contact) {
        contact = data.contact;
        objectId = data.contact.id;
      }
      if (data.settings === false) {
        showSettings = false;
      }
      if (data.calendar === false) {
        showCalendar = false;
      }

      if (data.hasOwnProperty("tableView")) {
        (showSettings = false),
          (showCalendar = false),
          (homeScreenDefault = false);
      }

      ui = sb.dom.make(data.domObj.selector);

      tableUI = ui.makeNode("table", "container", {});
      tableUI.run = startTableUI;

      ui.build();

      tableUI.run();
    },

    startClientPortal: function () {
      instance = sb.data.url.getParams().i;
      workorder = sb.data.url.getParams().wid;
      appConfig.instance = instance;

      sb.data.db.controller(
        "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
        {
          queryObj: {
            instance: instance,
          },
          instance: instance,
          objectType: "instances",
        },
        function (instanceObj) {
          /* console.log('instanceObj',instanceObj); */
          appConfig = instanceObj[0];

          dom = sb.dom.make(".main");

          dom
            .makeNode("cont", "div", { css: "ui" })
            .makeNode("cont", "div", { css: "" });

          dom.cont.cont.makeNode("modals", "div", {});

          dom.build();

          showWorkorder(dom.cont.cont, workorder, function () {});
        },
        sb.url + "/api/_getAdmin.php?do="
      );
    },

    startEstimateView: function (data) {
      var dom = data.domObj;
      var obj = data.object;
      var objectType = obj.object_bp_type;
      var onUpdate;
      var doneCallback;

      if (data.doneCallback) {
        doneCallback = data.doneCallback;
      }

      if (data.onUpdate) {
        onUpdate = data.onUpdate;
      }

      if (!doneCallback) {
        dom.makeNode("loading", "div", {
          css: "ui active centered inline loader",
        });
      }

      dom.patch();

      estimateView(
        dom,
        obj,
        {},
        {},
        {},
        objectType,
        data.compact,
        onUpdate,
        data.buttonDom,
        doneCallback
      );
    },
  };
});
