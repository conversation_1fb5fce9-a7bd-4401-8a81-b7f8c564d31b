Factory.register('documents', function(sb){
	
	var googleDocImage = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/releases/_production/_images/google-doc.svg';
	var googleSheetImage = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/releases/_production/_images/google-sheet.svg';
	var googleSlideImage = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/releases/_production/_images/google-slide.svg';
	var googleFileImage = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/releases/_production/_images/google-other.svg';
	var boxIncImage = 'https://pagoda.nyc3.digitaloceanspaces.com/_applications/bento/releases/_production/_images/BoxInc_logo.svg';
	var dropboxImage = 'https://pagoda.nyc3.digitaloceanspaces.com/_applications/bento/releases/_production/_images/DropboxGlyph_Blue.svg';
	var dropboxPaperImage = 'https://pagoda.nyc3.digitaloceanspaces.com/_applications/bento/releases/_production/_images/DropboxPaper_TwoColor.svg';
	var onedriveImage = 'https://pagoda.nyc3.digitaloceanspaces.com/_applications/bento/releases/_production/_images/onedrive_cloud.svg';
	
	var Cont;
	var onMobile = false;
	var collectionsDefaultSubView = 'cards';
	var isMultiFileMode = false;
	var isOnMultiFileUploadPage = false;
	
	// utility functions
	
	function removeHTMLTags(bodyText){
		
		return bodyText.replace(/<(?:.|\n)*?>/gm, '');
																	
	}
	
	function splitString(stringVal, multi){
		
		if(multi){
			
			return stringVal.split(' ', 10).join(' ');
			
		}else{

			return stringVal.split('', 10).join('');
		}

	}
	
	// views
		
	function documentLayout(dom, document, formObject, options) {

		// view functions
		
		function Header ( ui, document, formObject ) {

			// ui nodes
			var header;
			var category;
			
			// parsed info
			var title = '<i>Untitled Message</i>';
			var avatarURL = 'https://placeimg.com/250/250/tech';
			var detailsDate = moment().format('MM/DD/YY');
			var detailsAuthor = '';
			
			if (document) {
							
				if (
					document.created_by 
					&& document.created_by.profile_image 
					&& document.created_by.profile_image.loc != "//" 
					&& document.created_by.profile_image !== null 
					&& document.created_by.profile_image !== undefined
				) {
					
					avatarURL = sb.data.files.getURL(document.created_by.profile_image);
					
				}
								
				title = document.name;

				if (document.document_type === 'folder') {
					title = '<i class="folder inverted grey icon"></i><div class="content">'+ document.name 
				}
		 			
			}
			
			// category top right label
			if (document && document.category != undefined) {
				
				ui.makeNode('category', 'div', {
					css: 'ui top right attached label', 
					text:document.category.name
				});
				
			}
				
			if (document && formObject) {
				delete ui.category;
			}

			switch(document.document_type) {
				
				case 'google_doc':
				case 'google_sheet':
				case 'google_slide':
				case 'google_other':
					
					// header
					header = ui.makeNode('h', 'div', {css: 'row'});
					
					if (formObject) {
						
						delete formObject.messageTitle.name.fieldCSS;
						formObject.messageTitle.name.label = 'Document Name';
						formObject.messageCategory.category.label = 'Document Category';
												
						if (!appConfig.is_portal) {
							header.makeNode('htext', 'form', formObject.messageTitle);
							header.makeNode('f', 'div', {css: 'ui grid'});
							category = header.f.makeNode('r', 'div', {css: 'sixteen wide column'});
							category.makeNode('b', 'div', {text: '<br />'});
							category.makeNode('catSelect', 'form', formObject.messageCategory);
						}
												
					}
					
					break;
					
				default:
				
					// header
					header = ui.makeNode('h', 'div', {css: 'row'});	
					
					if (formObject 
						&& formObject.hasOwnProperty('messageTitle') 
						&& formObject.messageTitle
					) {
						
						if (!appConfig.is_portal) {
							header.makeNode('htext', 'form', formObject.messageTitle);
						}
												
					} else if (!isOnMultiFileUploadPage) {
						
						header.makeNode('htext', 'div', {tag:'h2', css: 'ui huge header', text: title});
						
					}		
					
					if (formObject && !appConfig.is_portal) {
						header.makeNode('f', 'div', {css: 'ui grid'});
						category = header.f.makeNode('r', 'div', {css: 'sixteen wide right floated column'});
						category.makeNode('catSelect', 'form', formObject.messageCategory);
					}
					
					if (!document.document_type == 'folder') {
						ui.makeNode('div', 'div', {css: 'ui divider'});
					}
				
			}
			
		}
		
		function Body (ui, document, formObject) {

			var bodyText = document.body || '';
			
			Cont = ui.makeNode('c', 'div', {css: 'container'});

			switch(options.document_type){
				
				case 'folder':

					if(document){
						
						sb.notify({
							type:'show-collection',
							data:{
								actions:{
									create:function(ui, newObj, onComplete){

										newObj.parent = document.id;
										newObj.tagged_with = document.tagged_with;
										newObj.action = {
											onSave:function(newObj){
	
												window.location.href = sb.data.url.createPageURL(
													'object', 
													{
														type:'document', 
														id:newObj.id,
														name:newObj.name
													}
												);
												
												onComplete(newObj);
												
											}
										};
										
										documentCreateEdit(ui, newObj);
																									
									},
									view:true,
									fullscreen:{
										icon:'eye',
										color:'blue',
										title:'View',
										domType:'navigate',
										action:function(item, ui, onComplete){
											
											window.location.href = sb.data.url.createPageURL(
												'object', 
												{
													type:'document', 
													id:item.id,
													name:item.name
												}
											);
											
																										
										}
									}
								},
								domObj:ui.c,
								fields:{
									name:{
										title:'Title',
										icon:function(obj){

											var titleIcon = (obj.document_type === 'folder') 
												? '<i class="folder inverted grey icon" style="display:inline-block"></i>'
												: null 
												
											return titleIcon;
																													
										},
										type:'title',
										counter:true,
// 										charLimit:25
									},
									body:{
										title:'Contents',
										type:'detail',
										view:function(ui, obj){
																																						
											if(obj.body !== null || obj.body != undefined){
												
												ui.makeNode('bodyText', 'div', {
													text:splitString(removeHTMLTags(obj.body), 
														_.contains(removeHTMLTags(obj.body), ' '))
													, css: 'truncate'
												});
																													
											} else {
												
												ui.makeNode('bodyText', 'div', {text:'<em>No Content Saved</em>'});
												
											}
																														
										}
									},
									created_by:{
										title:'Author',
										view:function(ui, obj){
											
											var staff = obj.created_by;
											
											if(staff){
												
												if(staff.profile_image){
												
													if(staff.profile_image.id){
													
														ui.makeNode('label-'+staff.id, 'div', {css:'ui image basic label', tag:'a', text:staff.fname +' '+ staff.lname.charAt(0) +'.'})
															.makeNode('image', 'div', {tag:'img', src:sb.data.files.getURL(staff.profile_image)});
														
													}else{
														
														ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
														
													}
													
												}else{
													
													ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
													
												}
												
											}
											
										},
										type:'author'
									},
									category:{
										title:'Category',
										type:'type'
									},
									is_public:{
										title:'Draft',
										view:function(ui, obj){
											
											var label = {
												text:'Draft',
												color:'ui orange label'
											};
											
											if(obj.document_type == 'folder'){
												label.text = 'Private';
												label.color = 'ui inverted grey label'
											}	

											if(obj.is_public == 0)
												ui.makeNode('label', 'div', {css: label.color, text:label.text});
										}
									},
									date_created:{
										title:	'Created On'
										, type: 'date'
										, edit: false
									}
								},
								objectType:'document',
								selectedView:'list',
								singleView:{
									view:function(ui, obj, onComplete){
	
										documentView(ui, {object:obj, edit:false, showSegment:false});
										
										onComplete({dom:ui});
										
									},
									select:3
								},
								fullView:{
									type:'hqTool',
									id:'messageBoardTool'
								}, 
								where:{
									parent:document.id,
									childObjs:{
										name:true,
										body:true,
										category:true,
										is_public:true,
										created_by:{
											fname:true,
											lname:true,
											profile_image:true
										},
										related_object:true,
										document_type:true,
										parent:true,
										date_created:true,
										tagged_with:true
									}
								}
							}
						});	
					}			

					break;
				
				case 'share':
				case 'other':
				case 'google':
				case 'google_doc':
				case 'google_sheet':
				case 'google_slide':
				case 'google_other':

					switch(document.storage_type || document.document_type){
						
						case 'google':
						
							var iconLink = googleDocImage;
							var buttonColor = 'blue';
						
							break;
						
						case 'dropbox':
						
							var iconLink = dropboxImage;
							var buttonColor = 'blue';
						
							break;
						
						case 'onedrive':
							
							var iconLink = onedriveImage;
							var buttonColor = 'blue';
							
							break;
							
						case 'box':
						
							var iconLink = boxIncImage;
							var buttonColor = 'blue';
						
							break;	
						
						case 'google_doc':
							var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-doc.svg';
							var buttonColor = 'blue';
							break;
						case 'google_sheet':
							var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-sheet.svg';
							var buttonColor = 'green';
							break;
						case 'google_slide':
							var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-slide.svg';
							var buttonColor = 'yellow';
							break;
						case 'google_other':
							var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-other.svg';
							var buttonColor = 'teal';
							break;
					}

					if (formObject) {
						ui.c.makeNode('bodyText', 'form', formObject.messageBody);
						ui.c.makeNode('error', 'div', {css: 'hidden ui warning message', text:'Please enter a valid share link URL.'});
						
					} else {
						ui.c.makeNode('cont', 'div', {css:'ui center aligned basic segment'});
						ui.c.cont.makeNode('bodyIcon', 'div', {tag:'img', src:iconLink, style:'width:100px;'});
						ui.c.cont.makeNode('bodyTitle', 'div', {css:'ui huge header', text:document.name});
						ui.c.cont.makeNode('bodyLink', 'div', {
							tag:'a', 
							href: sb.dom.cleanURL(document.share_link), 
							target:'_blank', 
							css:'ui large '+ buttonColor +' button', 
							text:'View Shared Link'
						});
					}
				
					break;
					
				case 'custom_file':
				case 'upload':
				
					if(document.file_upload){
						switch(document.file_upload.file_type){
							case 'pdf':
								var iconObj = {tag:'i', css:'large file pdf icon'};
								break;
							case 'jpg':
							case 'jpeg':
							case 'png':
								var iconObj = {tag:'img', src:sb.data.files.getURL(document.file_upload), css:'ui fluid centered image'};
								break;	
							default:
								var iconObj = {tag:'i', css:'large file icon'};
						}
					}

					if (formObject) {
						ui.c.makeNode('bodyText', 'form', formObject.messageBody);
					} else {
						ui.c.makeNode('cont', 'div', {css:'ui center aligned basic segment'});
						ui.c.cont.makeNode('bodyIcon', 'div', iconObj);
						ui.c.cont.makeNode('bodyTitle', 'div', {css:'ui huge header', text:document.name+'.'+document.file_upload.file_type});
						ui.c.cont.makeNode('bodyLink', 'div', {tag:'a', href:'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/'+ appConfig.instance +'/'+ document.file_upload.loc, target:'_blank', css:'ui large teal button', text:'Download File'});
					}
				
					break;
				
				default:
					
					Cont = ui.makeNode('c', 'div', {css: 'container', style:'box-shadow:0 1px 2px 0 rgba(34,36,38,.15);border:1px solid lightgrey; min-height:400px; padding: 10px;'});
			
					if(formObject){
						ui.c.makeNode('bodyText', 'div', {id:'textEditorBox', text:bodyText.replace(/\n/g, '<br />'), css: 'ql-editor'});
					}else{
						ui.c.makeNode('bodyText', 'div', {id:'textEditorBox', text: bodyText.replace(/\n/g, '<br />'), css: 'ql-editor'});
					}
					
					if (!Array.isArray(ui.c.bodyText)) {
						ui.c.bodyText.listeners = [];
					}
										
					ui.c.bodyText.listeners.push(
						function (selector) {
							
							/*
var viewQuill = new Quill(
								'#textEditorBox', 
								{
									theme: 			'snow',
									placeholder: 	'Compose an epic...',
									enabled: 		false,
									modules: 		{
														toolbar: false
													}
								}
							);
							viewQuill.enable(false);
*/
							
						}
					);
									
			}
			
		}
		
		// view state
		
		var columnCss = 'sixteen wide column';
		var segmentCSS = '';
		var label = {
			color:'ui orange left ribbon label',
			text:'Unpublished Draft'
		};
				
		if(options && options.hasOwnProperty('showSegment') && options.showSegment === false) {
			columnCss = 'sixteen wide column';
			segmentCSS = 'ui container';
		}
		
		// procedure

		if (document && formObject) {

			formObject.messageTitle.name.value = document.name;

			if(document.document_type != 'folder'){
				if(!document.document_type || document.document_type == 'text'){
					
					formObject.messageBody.body.value = document.body;
							
				}else{
					
					if(document.document_type !== 'custom_file' || document.document_type !== 'upload'){
	
						formObject.messageBody.body.value = document.share_link;
						
						formObject.messageBody.messageType.value = document.document_type;
							
						formObject.messageBody.messageTypeName.value = document.storage_type;
						
					}
				}
			}	
			
			segmentCSS = '';
			
			if(
				document.category != null 
				|| document.category != undefined 
				|| !_.isEmpty(document.category)
			) {
				
				formObject.messageCategory.category.value = document.category.id;
			
			}
			
		}
							 	
		dom.makeNode('r', 'div', {css: columnCss});	
		dom.r.makeNode('s', 'div', {css: segmentCSS});

		if (document.document_type == 'folder' || options.document_type == 'folder') {
			
			label.color = 'ui inverted grey left ribbon label';
			label.text = 'Private Folder';
			
			if(document.is_public == 0)
				dom.r.s.makeNode('folderLabel', 'div', {css: label.color, text: label.text});
			
		} else {
			
			if(document.is_public == 0){
				dom.r.s.makeNode('draft-label', 'div', {tag:'a', css: label.color, text:label.text});		
			}			
		}

		dom.r.s.makeNode('container', 'div', {css: 'fluid container'});

		Header ( dom.r.s.container, document, formObject );
		Body ( dom.r.s.container, document, formObject );

		return dom;
			
	}
	
	function documentCreateEdit(dom, state, draw, options, fieldName) {

		isMultiFileMode = ((options && options.multi) || state.object_bp_type) ? true : false;

		var message = state.object || false;
		var quill = {};
		    
		function askWhichKind(dom, state, draw) {

			dom.empty();

			if (state.object_bp_type === 'document') {
				options = {
					allowLinkingToExternalFiles: true,
					allowFolderCreation: true
				}
			}

			var columnWidth = 'one';
			if (options.allowLinkingToExternalFiles && options.allowFolderCreation) {
				columnWidth = 'three';
			} else if (options.allowLinkingToExternalFiles || options.allowFolderCreation) {
				columnWidth = 'two';
			} else {
				state.newDocType = 'upload';
				newDocument(dom, state, draw, fieldName);
				return;
			}
			
			dom.makeNode('title', 'div', {text:'New File or Folder', css:'ui huge center aligned header', style:'margin:0 !important;'});
			dom.makeNode('subtitle', 'div', {text:'What do you want to do?', css:'ui center aligned header', style:'margin:0 !important;'});
			
			dom.makeNode('segment', 'div', {css:'ui center placeholder basic segment', style:'min-height:0 !important; padding:0 !important;'});
			dom.segment.makeNode('grid', 'div', {css:'ui ' + columnWidth + ' column stackable center aligned grid'});
			dom.segment.grid.makeNode('row', 'div', {css:'middle aligned row'});
			dom.segment.grid.row.makeNode('col1', 'div', {css:'column'});
			dom.segment.grid.row.col1.makeNode('container', 'div', {css:'round-border', style:'padding:30px;'});
			dom.segment.grid.row.col1.container.makeNode('header', 'div', {css:'ui icon header', text:'<i class="cloud upload icon"></i> Upload a File'});
			
			var uploadAFileBtn = dom.segment.grid.row.col1.container.makeNode('uploadAFileBtn', 'div', {css:'ui large blue button', text:'<i class="cloud upload icon"></i> Upload a File'});
			uploadAFileBtn.notify('click', {
				type:'document-run',
				data:{
					run:function(){

						uploadAFileBtn.loading();

						state.newDocType = 'upload';
						isOnMultiFileUploadPage = isMultiFileMode ? true : false;

						newDocument(dom, state, draw, fieldName);

					}
				}
			}, sb.moduleId);

			if (options.allowLinkingToExternalFiles) {
				dom.segment.grid.row.makeNode('col2', 'div', {css:'column'});
				dom.segment.grid.row.col2.makeNode('container', 'div', {css:'round-border', style:'padding:30px;'});
				dom.segment.grid.row.col2.container.makeNode('header', 'div', {css:'ui icon header', text:'<i class="linkify icon"></i> Link External File'});
				
				var linkToFileBtn = dom.segment.grid.row.col2.container.makeNode('linkToFileBtn', 'div', {css:'ui large teal button', text:'<i class="linkify icon"></i> Link External File'});
				linkToFileBtn.notify('click', {
					type:'document-run',
					data:{
						run:function(){
	
							linkToFileBtn.loading();
							
							state.newDocType = 'share';
							isOnMultiFileUploadPage = false;
							
							newDocument(dom, state, draw);
							
						}
					}
				}, sb.moduleId);
				
			}
			
			if (options.allowFolderCreation) {
				dom.segment.grid.row.makeNode('col3', 'div', {css:'column'});
				dom.segment.grid.row.col3.makeNode('container', 'div', {css:'round-border', style:'padding:30px;'});
				dom.segment.grid.row.col3.container.makeNode('header', 'div', {css:'ui icon header', text:'<i class="folder open icon"></i> Create a Folder'});
				
				var createFolderBtn = dom.segment.grid.row.col3.container.makeNode('createFolderBtn', 'div', {css:'ui large violet button', text:'<i class="folder open icon"></i> Create a Folder'});
				createFolderBtn.notify('click', {
					type: 'document-run',
					data: {
						run:function(){
							
							createFolderBtn.loading();
							
							state.newDocType = 'folder';
							isOnMultiFileUploadPage = false;
							
							newDocument(dom, state, draw);
							
						}
					}
				}, sb.moduleId);
			
			}
			
			dom.patch();
			
		}

		function messageFormObject(callback, docType) {

		    var formObject = {
				messageTitle: {
					name: {
						label: 'File Name',
						name: 'name',
						type: 'text',
						css: 'ui huge fluid input',
						placeholder: 'Message Title...'
					}
				},
				messageBody:{
					body:{
						name: 'messageBod',
						type: 'textbox',
						rows: 10,
						wysiwyg: {
							focus: true,
							toolbar: false,
							height: 50
						},
						fieldCss: 'ui input',
						parentCSS: '',
						childCSS: '',
						bodyColor: '',
						header: false,
						placeholder: 'Highlight your text to format'
					}
				},
				messageCategory:{
					category: {
						label: 'Category (optional)',
						name: 'category',
						type: 'select',
						css: 'ui huge fluid',
						options: [{
							name: 'None',
							value: ''
						}]
					}
				}					
			};
					
			if (docType == 'google_sheet' || docType == 'google_doc' || docType == 'google_slide' || docType == 'other') {
				var docType = 'share';
			}
			
			switch(docType){
				
				case 'folder':
					
					formObject.messageTitle.name.placeholder = 'Folder Name...';
					formObject.messageBody.body.type = 'hidden';
				
					break;
				
				case 'share':
				case 'google':
				case 'other':
				
					var linkOptions = [
						{
							name:'Other',
							value:'other'
						},
						{
							name:'Google',
							value:'google'
						},
						{
							name:'DropBox',
							value:'dropbox'
						},
						{
							name:'OneDrive',
							value:'onedrive'
						},
						{
							name:'Box',
							value:'box'
						}								
					];
				
					formObject.messageTitle.name.placeholder = 'Document Title...';
				
					formObject.messageBody = {
						body: {
							label: 'Document share link',
							name: 'messageBod',
							type: 'text',
							css: 'ui huge fluid input',
							placeholder: 'https://...',
							change:function(form, selection, pasteContent){

								///pda-forms change evt calls with 2 args, paste evt calls with 3 (clipboard paste contents)
								var content = (arguments.length == 2) ? selection : pasteContent;
								///regex to validate url returns true/fals
								var isURL = validateURL(content);

								function validateURL(url) {
									var pattern = new RegExp('^(https?:\\/\\/)?'+ // protocol
									'((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|'+ // domain name
									'((\\d{1,3}\\.){3}\\d{1,3}))'+ // ip (v4) address
									'(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*'+ //port
									'(\\?[;&amp;a-z\\d%_.~+=-]*)?'+ // query string
									'(\\#[-a-z\\d_]*)?$','i');
									return pattern.test(url);
								}
								
								function escapeChar(s) {

								    return s.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
								}								
								
								///if true url make selection for messageType dropdown					
								if(isURL){

									///removes error message if present									
									Cont.error.addClass('hidden');
									
									var optList = [];
									var selected;
									
									///map over options and compare to option value
									_.map(linkOptions, function(option){
																
										content = escapeChar(content);
										
										var regex = new RegExp(option.value, 'i');
										
										///regex to match option.value in provided selection/paste content
										var output = content.match(regex, 'i');
										
										option.selected = false;
										
										///if contents matches option.value 'keyword' the select true
										if(!_.isNull(output)){
											selected = option;
											option.selected = true;
										}

										optList.push(option);
																											
									});
									
									///update messageType with 'selected' value
									Cont.bodyText.messageTypeName.update({
										options:optList,
										value:selected.value
									});														

									
								}else{
									///creates error message
									Cont.error.removeClass('hidden');
								}						

								
							}
						},
						messageType: {
							name: 'messageType',
							type: 'hidden',
							value: 'share'
						},
						messageTypeName: {
							label: 'Type of document',
							name: 'messageTypeName',
							type: 'select',
							css: 'ui huge fluid',
							options: linkOptions
						}
					};
				
					break;
					
				case 'custom_file':
				case 'upload':

					var uploadText = (isOnMultiFileUploadPage) ? 'Choose 1 or more files' : 'Choose a file';
				
					if (message !== false) {
						uploadText = (isOnMultiFileUploadPage) ? 'Choose 1 or more files (this will replace the current file(s))' : 'Choose a new file (this will replace the current file)';
					}
					
					if (isOnMultiFileUploadPage) {
						delete formObject.messageTitle;
					} else {
						formObject.messageTitle.name.placeholder = 'File name...';
					}
					
					formObject.messageBody = {
						file_upload: {
							label: uploadText,
							name: 'file_upload',
							type: (isOnMultiFileUploadPage) ? 'file-upload-multiple' : 'file-upload',
							style: 'padding:15px !important;'
						},
						messageBod: {
							name: 'messageBod',
							type: 'hidden',
							value: 'custom-file'
						},
						messageType: {
							name: 'messageType',
							type: 'hidden',
							value: 'custom_file'
						},
						messageTypeName: {
							name: 'messageTypeName',
							type: 'hidden',
							value: 'custom_file'
						}						
					};
					
					break;
				
				default:
				
			}

			sb.data.db.obj.getAll('document_category', function(categoryList){
					
				sb.data.db.obj.getById('users', sb.data.cookie.userId, function(user){
												
					_.map(categoryList, function(cat){
						
						formObject.messageCategory.category.options.push({
							name:cat.name,
							value: cat.id
						});
						
					});	
					
					author = formObject.messageAuthor = user;
					categories = categoryList;
					
					if (appConfig.is_portal) {
						
						formObject.messageCategory.category.type = 'hidden';						
						
					}
					
					callback(formObject);					

				});

			});
			
		}
		
		function messageProcessForm(forms, messageType, callback, isDraft) {

			var name = (!appConfig.is_portal && !isOnMultiFileUploadPage) ? forms.r.s.container.h.htext.process() : '';
			var nameVal = (!appConfig.is_portal && !isOnMultiFileUploadPage) ? name.fields.name.value : '';
			var category = (!appConfig.is_portal) ? forms.r.s.container.h.f.r.catSelect.process() : '';
			var categoryVal = (!appConfig.is_portal) ? category.fields.category.value : '';
			    
			switch(messageType) {
				
				case 'folder':
				
					var body = null;
					break;
				
				case 'text':
				
					var body = quill.root.innerHTML;					
					break;
					
				default:
				
					var body = forms.r.s.container.c.bodyText.process();			
				
			}

			if (!appConfig.is_portal && !isOnMultiFileUploadPage) {

				if (nameVal == undefined || _.isEmpty(nameVal)) {

					sb.dom.alerts.alert('Warning','Please type in a name before saving.','warning');
					return;			

				}

			}		
	
			if (messageType === 'text' && body == '<p><br></p>') {
				
				sb.dom.alerts.alert('Warning','Your message body is empty.','warning');
				return;	
					
			} else if (messageType === 'share' && _.isEmpty(body.fields.messageBod.value)) {
				
				sb.dom.alerts.alert('Warning','Your share link is empty.','warning');						
				return;	
			
			} else if (!isOnMultiFileUploadPage) {

				if (messageType === 'upload' && _.isUndefined(body.fields.file_upload.value.fileData)) {
				
					sb.dom.alerts.alert('Warning','Please select a file to upload','warning');						
					return;	
					
				}

			}

			switch (messageType) {
				
				case 'folder':

					messageObj = {
						name: nameVal,
						category: categoryVal,
						document_type: 'folder',
						is_public: 1						
					};
					
					break;
				
				case 'text':
				
					messageObj = {
						name: nameVal,
						body: body,
						document_type: 'text',
						category: categoryVal,
						is_public: 1
					};		
				
					break;
				
				case 'share':	
				case 'google':
				case 'other':
				
					messageObj = {
						name: nameVal,
						share_link: body.fields.messageBod.value,
						document_type: 'share',
						storage_type: body.fields.messageTypeName.value,
						category: categoryVal,
						is_public: 1
					};
				
					break;
					
				case 'custom_file':
				case 'upload':
				
					messageObj = {
						name: nameVal,
						share_link: body.fields.messageBod.value,
						document_type: 'upload',
						category: categoryVal,
						is_public: 1
					};

					if (!isOnMultiFileUploadPage) {

						messageObj.file_upload = 
							(!_.isUndefined(body.fields.file_upload.value.fileData)) 
							? body.fields.file_upload.value 
							: message.file_upload;

					}

					break;		
				
			}

			if (message) {
				messageObj.id = message.id;
				messageObj.is_public = message.is_public;
			}

			if (isDraft) {
				messageObj.is_public = 0;
			} else {
				messageObj.is_public = 1;
			}

			if (state.hasOwnProperty('parent')) {
				messageObj.parent = state.parent;
			}

			if (state.hasOwnProperty('category')) {
				messageObj.category = state.category;
			}

			callback(messageObj);
	
		}
		
		function messageSave(state, dom, message, callback) {

			function continueUploadingFile(message, callback){

				try{
					if (appConfig.instance === 'foundation_group' || appConfig.is_portal) {
						sb.data.db.obj.getById('', state.related_object, function (obj) {
							if(obj && obj.object_bp_type){
							var isRequest = obj.object_bp_type.startsWith('#990_Follow_up');
							var isReview = obj.object_bp_type.startsWith('#Review');
							if (isRequest || isReview) {
								var projectId = obj.parent;
								const includedTags = [1636580, 1803499, appConfig.main_contact, projectId];

								if(appConfig.is_portal){
									sb.data.db.obj.addRCATagByType(obj.id, 'uploads',function(rsp) {
										_continueUploadingFile(message, callback, includedTags);
									});
								} else {
									_continueUploadingFile(message, callback, includedTags);
								}

							} else {
								_continueUploadingFile(message, callback, []);
							}
						} else {
							_continueUploadingFile(message, callback, []);
						}
						});
					} else {
						_continueUploadingFile(message, callback, []);
					}

				} catch(e) {
					_continueUploadingFile(message, callback, []);
				}


			}

			function _continueUploadingFile(message, callback, additionalTags) {

				var db = (message.hasOwnProperty('id')) ? 'update' : 'create';
				
				if (db === 'create') {
			
					message.tagged_with = state.tagged_with;

					if(additionalTags.length > 0) {
						message.tagged_with = message.tagged_with.concat(additionalTags);
					}
					
					if (state.hasOwnProperty('is_template')) {
						message.is_template = state.is_template;
					}

					if (state.related_object) {
						message.related_object = state.related_object;
					}
					
				}

				// In client portals, also tag the new attachment with the 
				// associated Company and Contact tag for the user.
				if (
					appConfig.is_portal 
				) {
					
					if (_.isEmpty(message.tagged_with)) {
						message.tagged_with = [];
					}

					if (typeof appConfig.portal_company === 'number') {

						message.tagged_with.push(appConfig.portal_company);
					
					}

					if (typeof appConfig.portal_contact === 'number') {

						if (_.isEmpty(message.shared_with)) {
							message.shared_with = [];
						}
						
						message.shared_with.push(appConfig.portal_contact);
					
					}

					if (options && options.obj && options.obj.parent && options.obj.parent.id) {
						message.tagged_with.push(
							options.obj.parent.id
						);
					}

				}

				sb.data.db.obj[db]('document', message, function(res) {

					if (res) {
						
						var noteObj = {
							icon: {
								icon: 'pencil',
								color: 'blue'
							},
							type: 'document',
							note: `Edits made to message: ${res.name}`,
							record_type: 'log',
							author: sb.data.cookie.get('uid'),
							notification: {
								producer: res.id
							},
							notifyUsers: [],
							log_data: {
								type: db,
								objectName: res.name,
								details: splitString(removeHTMLTags(res.body), _.contains(removeHTMLTags(res.body), ' '))
							}
						};

						if (state.related_object) {
							noteObj.type_id = state.related_object;
							noteObj.tagged_with = state.related_object;
						}
						
						if (db == 'create') {
							
							if (message.document_type == 'text') { 
								noteObj.note = `A new text file titled '${res.name}' has been created.`;
							} else if (message.document_type == 'share') { 
								noteObj.note = `A new file titled '${res.name}' has been shared.`;
							} else if (message.document_type == 'upload') { 
								noteObj.note = `A new file titled '${res.name}' has been uploaded.`; 
							} else {
								noteObj.note = `A new file titled '${res.name}' has been created.`;
							}

						}

						if(res.related_object && res.related_object.id && res.related_object.object_bp_type){
							sb.data.db.obj.getBlueprint(res.related_object.object_bp_type, function (resp) {
								if (resp._class) {
									taggedUsers = [];
									noteObj.relatedFormType = resp._class;
									sb.data.db.obj.postComment(noteObj, function (newNote) {
									});
									callback(res);
								} else {
									callback(res);
								}
							}, false, true);
						} else {
							sb.data.db.obj.postComment(noteObj, function (newNote) {

								taggedUsers = [];

								//sb.dom.alerts.alert('Success!', dbSAlert, 'success');
								callback(res);

							});
						}
					}
					
				}, 2);	

			}

			if (isOnMultiFileUploadPage) {

				var processedForms = [];

				var category = $('select[name="category"]').find(":selected").val();
				var files = $('input[name="file_upload"]')[0].files;
				var numberFiles = $('input[name="file_upload"]')[0].files.length;
				
				for (let i = 0; i < numberFiles; i++) {

					var file = files[i];

					formData = {
						"category": { 
							"value" : category
						}
						, "file_upload": {
							"value": {
								"fileData": file
									, "fileName": file.name.split('\\')[file.name.split('\\').length - 1].split('.').slice(0, -1).join('_')
									, "fileType": "N/A"
									, "objectType": "root"
									, "objectId": "0"
									, "parent": "0"
							}
							,"type": "file-upload"
						}
					}

					processedForms.push(formData);

				}

				if (processedForms.length === 0){

					dom.grid.hrow.btns.save.loading(false);

					var error = {
						title: 'Please select a file to upload'
						, text: ''
					};

					sb.dom.alerts.alert(error.title, error.text, 'error');
					return;
					
				}
				
				var i = 1;
				var count = processedForms.length;
				var multiFileResponse = [];

				processedForms.forEach(formData => {

					messageProcessForm(dom.grid, state.newDocType, function(message) {

						message.name = formData.file_upload.value.fileName;
						message.file_upload = formData.file_upload.value;

						continueUploadingFile(message, function(res) {

							multiFileResponse.push(res);

							if (i == count) {
								callback(multiFileResponse);
							}

							i++;

						});

					});
					
				});

			} else {

				continueUploadingFile(message, function(res) {
					callback(res);
				});

			}
				
		}
		
		function newDocument(dom, state, draw, fieldName) {

			var draftButton = {};
			var saveButton = {};

			if (!state.newDocType) {
				state.newDocType = 'text';
			}
			
			var headerText = '';
			switch (state.newDocType) {

				case 'upload':
					headerText = '<i class="cloud upload icon" style="margin-top:-10px !important;"></i> Upload a File';
					draftButton.color = 'ui large grey button';
					draftButton.text = '<i class="save icon"></i> Save as Draft';
					draftButton.loading = 'ui large grey loading button';
					saveButton.color = 'ui large blue button';
					saveButton.text = '<i class="cloud upload icon"></i> Upload File';
					saveButton.loading = 'ui large blue loading button';
					break;

				case 'share':
					headerText = '<i class="linkify icon" style="margin-top:-10px !important;"></i> Link External File';
					draftButton.color = 'ui large grey button';
					draftButton.text = '<i class="save icon"></i> Save as Draft';
					draftButton.loading = 'ui large grey loading button';
					saveButton.color = 'ui large teal button';
					saveButton.text = '<i class="linkify icon"></i> Link External File';
					saveButton.loading = 'ui large teal loading button';
					break;

				case 'folder':
					headerText = '<i class="folder open icon" style="margin-top:-10px !important;"></i> Create a Folder';
					draftButton.color = 'ui large grey button';
					draftButton.text = '<i class="eye slash icon"></i> Make Private Folder';
					draftButton.loading = 'ui large grey loading button';
					saveButton.color = 'ui large violet button';
					saveButton.text = '<i class="eye icon"></i> Make Public Folder';
					saveButton.loading = 'ui large violet loading button';
					break;

			}
			
			dom.empty();
			
			dom.makeNode('grid', 'div', {css:'ui centered stackable grid'});
			dom.grid.makeNode('headerContainer', 'div', {css:'ui sixteen wide column', style:'padding-top:0;'});
			dom.grid.headerContainer.makeNode('header', 'div', {css:'ui huge header', text: headerText});

			messageFormObject(function(form){
	
				var toolbarOptions = [
					['bold', 'italic', 'underline', 'strike'],        // toggled buttons
					['blockquote', 'code-block'],
					
// 					[{ 'header': 1 }, { 'header': 2 }],               // custom button values
					[{ 'list': 'ordered'}, { 'list': 'bullet' }],
					[{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
					[{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
					[{ 'direction': 'rtl' }],                         // text direction
					
// 					[{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
					[{ 'header': [1, 2] }],
					
// 					[{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
// 					[{ 'font': [] }],
// 					[{ 'align': [] }],
					
// 					['clean']                                         // remove formatting button
				];
	
				documentLayout(dom.grid, message, form, {
					document_type:state.newDocType
				});

				if(typeof draw === 'function'){
					
					draw({
						dom:dom,
						after:function(dom){
							
							switch(state.newDocType){
								
								case 'text':
								
									quill = new Quill('#textEditorBox', {
										theme: 'snow',
										placeholder: 'Compose an epic...',
										modules: {
											toolbar: toolbarOptions
										}
									});
									
									break;
								
								default:
								
							}
														
						}
					});
					
				}else{

					dom.patch();
					
					switch (state.newDocType) {
								
						case 'text':
						
							quill = new Quill('#textEditorBox', {
								theme: 'snow',
								placeholder: 'Compose an epic...',
								modules: {
									toolbar: toolbarOptions
								}
							});
							
							break;
						
						default:
						
					}
					
				}

				dom.grid.makeNode('hrow', 'div', {css:'sixteen wide column'});
				dom.grid.hrow.makeNode('btns', 'div', {style:'text-align:right;'});

				if (options.allowFolderCreation || options.allowLinkingToExternalFiles) {
					dom.grid.hrow.btns.makeNode('back', 'div', {css:'ui large grey basic button', text:'<i class="arrow left icon"></i> Back', style:'float:left; margin-right:10px;'}).notify('click', {
						type: 'document-run',
						data: {
							run: function() {

								askWhichKind(dom, state, draw);

							}
						}
					}, sb.moduleId);
				}
				
				if(state.newDocType == 'text' || state.newDocType == 'folder'){
					dom.grid.hrow.btns.makeNode('draft', 'div', {css: draftButton.color + ' large', text:draftButton.text, style:'margin-right:10px;'}).notify('click', {
						type: 'document-run',
						data: {
							run:messageProcessForm.bind(null, dom.grid, state.newDocType, function(message, dbCall) {
								
								dom.grid.hrow.btns.draft.css(draftButton.loading);
								
								messageSave(state, dom, message, state.action.onSave);
								
							}, true)
						}
					}, sb.moduleId);
				}
			
				if(state.action && state.action.hasOwnProperty('onSave')){

					dom.grid.hrow.btns.makeNode('save', 'div', {css:saveButton.color + ' large', text:saveButton.text}).notify('click', {
						type: 'document-run',
						data: {
							run:messageProcessForm.bind(null, dom.grid, state.newDocType, function(message, dbCall) {

								dom.grid.hrow.btns.save.css(saveButton.loading);

								messageSave(state, dom, message, state.action.onSave);
								
							}, false)
						}
					}, sb.moduleId);
								
				}

				dom.patch();
				
			}.bind(this), state.newDocType);	
			
		}
				
		if (message) {
			
			state.newDocType = message.document_type;

			newDocument(dom, state, draw);
						
		} else {
	
			if (appConfig.is_portal) {
			
				state.newDocType = 'upload';
				
				newDocument(dom, state, draw);				
				
			} else {	
			
				askWhichKind(dom, state, draw);
			}
		}
		
	}

	function documentView(dom, state, draw){

		// data functions
		
		function deleteDocument (dom, state, draw, callback) {
			
			var messageObj = state.object;
			
			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: 'This will delete your document'
			}, function(res){
				
				if(res){
					
					sb.data.db.obj.erase('document', messageObj.id, function(resp){
						
						if(resp){
							
							swal.close();
							
							callback(messageObj.id);

						}else{
							
							sb.dom.alerts.alert(
								`Error`, 
								`Something went wrong.`, 
								`error`
							);
							
						}
												
					});				
					
					
				}
				
			});
	
		}	
		
		function moveDocument (ui, document, onMove) {
						
			sb.notify({
				type:'get-move-object-form',
				data:{
					dom:ui,
					onComplete:function(document, newLocation){
						
						var noteObj = {
							icon: {
								icon: 'truck',
								color: 'blue'
							},
							type_id: document.id,
							type: 'document',
							note: `<strong>${document.name}</strong> moved to <strong>${newLocation.name}</strong>.`,
							record_type:'log',
							author: sb.data.cookie.get('uid'),
							notification: {
								producer: res.id
							},
							notifyUsers:[],
							log_data:{
								type:'move',
								objectName:document.name,
								details:`<strong>${document.name}</strong> moved to <strong>${newLocation.name}</strong>.`
							}
						};
						
						sb.data.db.obj.postComment(noteObj, function(newNote){});
						
					},
					object:document,
					state:{
						tool:'messageBoardTool',
						parentProperty:'related_object'
					}
				}
			});
			
		}
		
		
		// view functions
		
		function EditMenu ( dom, state, draw ) {

			var pubButton = (state.object.is_public == 0) ? {text:'Publish Document', icon:'green book'} : {text:'Unpublish Document', icon:'grey book'};
			
			dom.grid.hrow.makeNode(
				'edit', 
				'div', {
					text:'<i class="large ellipsis horizontal circular blue icon"></i>',
					style:'float:right;',
					css:'ui right floated simple dropdown icon'
				}
			);
			
			menu = dom.grid.hrow.edit.makeNode('menu', 'div', {css:'left menu'});
			
			menu.makeNode(
				'edit', 
				'div', {
					css:'item', 
					text:'<i class="orange edit icon"></i> Edit Document'
				}
			);

			switch(state.object.document_type) {
				
				case 'custom_file':
				case 'google_doc':
				case 'google_sheet':
				case 'google_slide':
				case 'google_other':

					break;
				
				default:
				
					menu.makeNode(
						'pdf', 
						'div', {
							css:'item', 
							text:'<i class="red file pdf icon"></i> Download a PDF',
							dataAttr:[
								{
									name:'tooltip',
									value:'Check your browers popup blocker if this button doesnt seem to work.'
								},
								{
									name:'inverted',
									value:''
								},
								{
									name:'position',
									value:'left center'
								}
							]
						}
					).notify('click', {
						type: 'document-run',
						data: {
							run:function(){
								
								var details = '';
								if(obj.created_by){
									details = '<small><p>Created By: '+ obj.created_by.fname +' '+ obj.created_by.lname +' on '+ moment(obj.date_created).local().format('M/D/YYYY') +'</p></small>';
								}else{
									details = 'Created on '+ moment(obj.date_created).local().format('M/D/YYYY');
								}
								
								sb.data.makePDF('<h1>'+obj.name+'</h1>'+details+obj.body, 'D');
								
							}
						}
					}, sb.moduleId);
				
			}
			
			
			
			menu.makeNode(
				'move', 
				'div', {
					css: 'item', 
					text:'<i class="blue truck icon"></i> Move Document'
				}
			);
			
			menu.makeNode(
				'delete', 
				'div', {
					css:'item', 
					text:'<i class="red trash icon"></i> Delete Document'
				}
			);
			
			if(
				state 
				&& state.object 
				&& state.object.created_by != null 
				&& state.object.created_by.id == +sb.data.cookie.get('uid')
			){
				
				menu.makeNode(
					'publish', 
					'div', {
						css: 'item',
						text:'<i class="'+ pubButton.icon +' icon"></i> '+ pubButton.text
					}
				);
				
				menu.publish.notify('click', {
					type: 'document-run',
					data: {
						run:function(obj, dom, state, draw){
	
							var isDraft = !!obj.is_public;
	
							sb.data.db.obj.update('document', {id:obj.id, is_public:!isDraft}, function(resp){
								
								state.object.is_public = resp.is_public;
								
								documentView(dom, state, draw);
								
							}, 1);
							
						}.bind(null, obj, dom, state, draw)
					}
				}, sb.moduleId);
									
			}			

			
			menu.edit.notify('click', {
				type: 'document-run',
				data: {
					run:function(){
						
						state.action = {
							onSave:function(obj){

								documentView(dom, {object:obj}, draw);
							}
						};
						
						documentCreateEdit.call({}, dom, state, draw);
						
					}
				}
			}, sb.moduleId);
			
			menu.move.notify('click', {
				type: 'document-run',
				data: {
					run:function(obj, dom, state, draw){
	
						dom.grid.modal.makeNode('move', 'modal', {
							onShow:function(){
								
								moveDocument (
									dom.grid.modal.move.body, 
									obj, 
									function(response){

									}
								);
															
							}
						});
						
						dom.grid.modal.patch();
						dom.grid.modal.move.show();
						
					}.bind(null, obj, dom, state, draw)
				}
			}, sb.moduleId);
			
			menu.delete.notify('click', {
				type: 'document-run',
				data: {
					run:function(){
						
							deleteDocument.call({}, dom, state, draw, function(resp){
							
								if(resp){
									
									sb.notify({
										type:'app-navigate-to',
										data:{
											type:'UP'
										}
									});
									
								}
							
						})
						
					}
				}
			}, sb.moduleId);
			
		}
		
		// ui nodes
		var menu;
		
		var obj = state.object;
		var edit = (state.hasOwnProperty('edit')) ? state.edit : true;

		// procedure	

		dom.makeNode('grid', 'div', {css:'ui centered stackable grid'});
		dom.grid.makeNode('modal', 'div', {});
		dom.grid.makeNode('hrow', 'div', {css: 'sixteen wide column'});
		
		///permission check for unpublished docs
		if (obj.is_public == 0 && (+sb.data.cookie.get('uid') != obj.created_by.id)) {

			if(draw && typeof draw === 'function'){
				
				dom.grid.hrow.makeNode('cont', 'div', {css: 'ui center aligned very padded text container'});
				dom.grid.hrow.cont.makeNode('text', 'div', {tag:'h4', text:'Document is unavailable'});
				
				draw(dom);
				
				return;
								
			} else {
				
				dom.patch();
			}
	
		}

		if (edit) {
			
			EditMenu ( dom, state, draw );
						
		}
	
		documentLayout(dom.grid, obj, false, {
			document_type:obj.document_type,
			showSegment:state.showSegment
		});	
		
		if( obj && obj.category != null && !_.isEmpty(obj.category.name) ) {
		
		}
				
		// notes/comments area
		dom.makeNode('comments', 'div', {css: ''});
		
		if(draw && typeof draw === 'function'){
			
			draw({
				dom:dom,
				after:function(dom){
					
					sb.notify({
						type: 'show-note-list-box',
						data: {
							collapse:false,
							domObj:dom.comments,
							objectIds:[obj.id],
							objectId:obj.id
						}
					});
				}
			});

		}else{
			
			dom.patch();

		}
			
	}
	
	// collections
	
	function myDocuments (dom, state, draw, mainDom, options) {
		
		var headquarters = state.headquarters;
		var where = {
			tagged_with:[+sb.data.cookie.get('uid')],
			childObjs:{
				name:true,
				body:true,
				is_public:true,
				category:true,
				document_type:true,
				created_by:{
					id:true,
					fname:true,
					lname:true,
					profile_image:true
				},
				related_object:true,
				date_created:true,
				state: true,
				tagged_with:true,
				comment_count:true,
				parent:{
					name:true,
					group_type:true
				}
			}
		}

		if(options != undefined && options.userId)
			where.tagged_with = options.userId;

		sb.notify({
			type:'show-collection',
			data:{
				actions:{
					create:function(ui, newObj, onComplete){

						newObj.tagged_with = [+sb.data.cookie.get('uid')];
						newObj.action = {
							onSave:function(newObj){
								window.location.href = sb.data.url.createPageURL(
									'object', 
									{
										type:'document', 
										id:newObj.id,
										name:newObj.name
									}
								);
							}
						};
						
						documentCreateEdit(ui, newObj);
																					
					},
					view:true,
					fullscreen:{
						icon:'eye',
						color:'blue',
						title:'View',
						domType:'navigate',
						action:function(item, ui, onComplete){
							
							window.location.href = sb.data.url.createPageURL(
								'object', 
								{
									type:'document', 
									id:item.id,
									name:item.name
								}
							);
							
																						
						}
					}
				},
				domObj:dom,   
				fields:{
					name:{
						title:'Title',
						type:'title',
						icon:function(obj){

							var titleIcon = (obj.document_type === 'folder') 
								? '<i class="folder brown icon" style="display:inline-block"></i>'
								: null 
								
							return titleIcon;
																									
						},
						counter:true,
// 						charLimit:25	
					},
					parent:{
						title:'Parent',
						type:'parent',
						shouldShow:function(obj){

							if(obj.parent.id != headquarters.id){

								return true;
							}else{
								return false;
							}
						}
					},
					body:{
						title:'Contents',
						type:'detail',
						view:function(ui, obj){

							switch(obj.document_type){
								
								case 'google_doc':
								case 'google_sheet':
								case 'google_slide':
								case 'google_other':
									
									switch(obj.document_type){
										case 'google_doc':
											var iconLink = googleDocImage;
											break;
										case 'google_sheet':
											var iconLink = googleSheetImage;
											break;
										case 'google_slide':
											var iconLink = googleSlideImage;
											break;
										case 'google_other':
											var iconLink = googleFileImage;
											break;
									}
									
									ui.makeNode('google', 'div', {tag:'img', css:'ui mini middle aligned left floated image', src:iconLink});
									ui.makeNode('go', 'div', {text:'View in Google <i class="fa fa-external-link"></i>', tag:'a', css:'animated fadeIn revealChild', href:obj.share_link, target:'_blank'});
									
									break;
									
								default:
								
									if(obj.body !== null || obj.body != undefined){
								
										ui.makeNode('bodyText', 'div', {
											text:splitString(removeHTMLTags(obj.body), 
												_.contains(removeHTMLTags(obj.body), ' '))
											, css: 'truncate'
										});
																											
									} else {
										
										ui.makeNode('bodyText', 'div', {text:'<em>No Content Saved</em>'});
										
									}
								
							}
																										
						}
					},
					category: {
						title: 	'Type',
						type: 	'type'
					}, 
					state: {
						title: 'Status',
						type: 'state',
						typeField: 'category'
					},
					created_by:{
						title:'Author',
						view:function(ui, obj){
							
							var staff = obj.created_by;
							
							if(staff){
								
								if(staff.profile_image){
								
									if(staff.profile_image.id){
									
										ui.makeNode('label-'+staff.id, 'div', {css:'ui image basic label', tag:'a', text:staff.fname +' '+ staff.lname.charAt(0) +'.'})
											.makeNode('image', 'div', {tag:'img', src:sb.data.files.getURL(staff.profile_image)});
										
									}else{
										
										ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
										
									}
									
								}else{
									
									ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
									
								}
								
							}
							
						},
						type:'author'
					},
					category:{
						title:'Category',
						type:'type'
					},
					date_created:{
						title:	'Created On'
						, type: 'date'
						, edit: false
					},
					is_public:{
						title:'Draft',
						view:function(ui, obj){
							
							var label = {
								text:'Draft',
								color:'ui orange label'
							};
							
							if(obj.document_type == 'folder'){
								label.text = 'Private';
								label.color = 'ui brown label'
							}	

							if(obj.is_public == 0)
								ui.makeNode('label', 'div', {css: label.color, text:label.text});
						}
					}
				},
				objectType:'document',
				selectedView: collectionsDefaultSubView,
				singleView:{
					view:function(ui, obj, onComplete){

						documentView(ui, {object:obj, edit:false, showSegment:false});
						
						onComplete({dom:ui});
						
					},
					select:3
				},
				groupBy: 'state',
				groupings: {
					state: 'state'
				},
				subviews: {
					board: {
						groupBy: {
							defaultTo: 'state'
						}
					},
					list: {
						groupBy: {
							defaultTo: 'category'
						}
					}
				}, 
				fullView:{
					type:'hqTool',
					id:'messageBoardTool'
				}, 
				where:where
			}
		});
		
	}
	
	// !DELETE (lns 1930 - 2195) -- Candidate for deletion 
	function teamCollection (dom, state, draw, parentObj) {

		var team = {};
		var tagged_with = [];
		if (state.hasOwnProperty('team')) {
			team = state.team;
		} else if (state.hasOwnProperty('obj')) {
			team = state.obj;
		}
		if (team) {
			tagged_with = [team.id];
		}
		var entityColl = false;
		var where = {
			tagged_with:tagged_with,
			childObjs:{
				name:true,
				body:true,
				is_public:true,
				related_object:true,
				category:true,
				document_type:true,
				share_link:true,
				created_by:{
					fname:true,
					lname:true,
					profile_image:true
				},
				date_created:true,
				tagged_with:true,
				comment_count:true,
				parent:{
					name:true,
					group_type:true
				}
			}	
		} ;
		
		if (
			state 
			&& state.pageObject 
			&& (
				state.pageObject.object_bp_type === 'contacts'
				|| state.pageObject.object_bp_type === 'companies'
			)
		) {
			parentObj = state.pageObject;
		}

		var collection = {
			actions:{
				create:function(ui, newObj, onComplete){

					if (!entityColl) {
					
						newObj.related_obj = team.id;
						newObj.tagged_with = [team.id, +sb.data.cookie.get('uid')];
					
					}
					
					if (
						state.hasOwnProperty('entity')
						&& typeof state.entity === 'number'
					) {
						
						newObj.parent = {
							id: state.entity	
						};
						
						newObj.tagged_with = [+sb.data.cookie.get('uid'), state.entity];
						
					}

					if (!_.isEmpty(parentObj) && parentObj.object_bp_type === 'companies') {
						
						newObj.tagged_with.push(parentObj.id);
						newObj.tagged_with = _.uniq(newObj.tagged_with);

					}

					newObj.action = {
						onSave:function(newObj){
							window.location.href = sb.data.url.createPageURL(
								'object', 
								{
									type:'document', 
									id:newObj.id,
									name:newObj.name
								}
							);
						}
					};
					
					documentCreateEdit(ui, newObj);														
				},
				view: true
			},
			domObj:dom,
			fields:{
				name:{
					title:'Title',
					icon:function(obj){

						var titleIcon = (obj.document_type === 'folder') 
							? '<i class="folder brown icon" style="display:inline-block"></i>'
							: null 
							
						return titleIcon;
																								
					},
					type:'title',
					counter:true,
// 					charLimit:25
				},
				parent:{
					title:'Parent',
					type:'parent',
					shouldShow:function(obj){

						if (
							team !== undefined
						) {
							
							if (
								obj.parent.id != team.id
							){
								
								return true;
								
							} else {
								
								return false;
								
							}	
							
						} 
					}
				},
				body:{
					title:'Contents',
					type:'detail',
					view:function(ui, obj){

						switch(obj.document_type){
							
							case 'google_doc':
							case 'google_sheet':
							case 'google_slide':
							case 'google_other':
								
								switch(obj.document_type){
									case 'google_doc':
										var iconLink = googleDocImage;
										break;
									case 'google_sheet':
										var iconLink = googleSheetImage;
										break;
									case 'google_slide':
										var iconLink = googleSlideImage;
										break;
									case 'google_other':
										var iconLink = googleFileImage;
										break;
								}
								
								ui.makeNode('google', 'div', {tag:'img', css:'ui mini middle aligned left floated image', src:iconLink});
								ui.makeNode('go', 'div', {text:'View in Google <i class="fa fa-external-link"></i>', tag:'a', css:'animated fadeIn revealChild', href:obj.share_link, target:'_blank'});
								
								break;
								
							default:
							
								if(obj.body !== null || obj.body != undefined){
							
									ui.makeNode('bodyText', 'div', {
										text:splitString(removeHTMLTags(obj.body), 
											_.contains(removeHTMLTags(obj.body), ' '))
										, css: 'truncate'
									});
																										
								} else {
									
									ui.makeNode('bodyText', 'div', {text:'<em>No Content Saved</em>'});
									
								}
							
						}
																									
					}
				},
				created_by:{
					title:'Author',
					view:function(ui, obj){
						
						var staff = obj.created_by;
						
						if(staff){
							
							if(staff.profile_image){
							
								if(staff.profile_image.id){
								
									ui.makeNode('label-'+staff.id, 'div', {css:'ui image basic label', tag:'a', text:staff.fname +' '+ staff.lname.charAt(0) +'.'})
										.makeNode('image', 'div', {tag:'img', src:sb.data.files.getURL(staff.profile_image)});
									
								}else{
									
									ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
									
								}
								
							}else{
								
								ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
								
							}
							
						}
						
					},
					type:'author'
				},
				category:{
					title:'Category',
					type:'type'
				},
				date_created:{
					title:	'Created On'
					, type: 'date'
					, edit: false
				},
				is_public:{
					title:'Draft',
					view:function(ui, obj){
						
						var label = {
							text:'Draft',
							color:'ui orange label'
						};
						
						if(obj.document_type == 'folder'){
							label.text = 'Private';
							label.color = 'ui brown label'
						}	

						if(obj.is_public == 0)
							ui.makeNode('label', 'div', {css: label.color, text:label.text});
					}
				}
			},
			objectType:'document',
			selectedView: collectionsDefaultSubView,
			singleView:{
				view:function(ui, obj, onComplete){

					documentView(ui, {object:obj, edit:false, showSegment:false});
					
					onComplete({dom:ui});
					
				},
				select:3
			},
			fullView:{
				type:'teamTool',
				id:'messageBoardTool'
			},
			parseData: parse_collections, 
			where:where
		};

		if (state.style === 'attachments') {
			collection.menu = false;
			collection.selectedView = 'list';
			delete collection.fields.body;
			delete collection.fields.created_by;
		}
		
		if (state.hasOwnProperty('entity')) {

			entityColl = true;
			tagged_with = [];
			
			if (state.entity) {
				
				collection.entity = state.entity;
					
				state.parent = state.entity;
				delete state.tool;
				
				collection.where.tagged_with = [state.entity];	
				
//				tagged_with.push(state.parent.id)
// 				collection.where.parent = collection.parent.id;
			} 
			
			collection.parent = state.parent.id;
			
			delete collection.where.parent;
			
			if (state.tool) {
				collection.tool = state.tool.id;
			}
			
		}

		if (parentObj) {
			
			delete collection.where.tagged_with;
			delete collection.where.parent;
			delete collection.entity;

			if (parentObj.object_bp_type === 'contacts') {

				collection.where.shared_with = {
					type: 'any'
					, values: [parentObj.id]
					, or: {
						parent: parentObj.id
					}
				};

			} else if (parentObj.object_bp_type === 'companies') {

				collection.where.tagged_with = [parentObj.id];
			
			}

		}

		sb.notify({
			type: 'show-collection',
			data: collection
		});
												
	}
	
	function build_collection (dom, state, draw) {

		var pageObj = {};
		var tagged_with = [];
		
		if (state.hasOwnProperty('pageObject')) {
			pageObj = state.pageObject;
		}		
		
		tagged_with = [state.pageObject.id];
		
		var entityColl = false;
		var where = {
			tagged_with: {
				type: 'any',
				values: tagged_with,
				or: {
					shared_with: {
						type: 'any',
						values: tagged_with
					}
				}
			},
			childObjs:{
				name:true,
				body:true,
				is_public:true,
				related_object:true,
				category:true,
				document_type:true,
				share_link:true,
				created_by:{
					fname:true,
					lname:true,
					profile_image:true
				},
				date_created:true,
				tagged_with:true,
				comment_count:true,
				parent:{
					name:true,
					group_type:true
				}
			}	
		} ;
		
		var collection = {
			actions:{
				create:function(ui, newObj, onComplete){
					
					if (!entityColl) {
					
						newObj.related_obj = pageObj.id;
						newObj.tagged_with = [pageObj.id, +sb.data.cookie.get('uid')];
					
					}

					newObj.action = {
						onSave:function(newObj){
							window.location.href = sb.data.url.createPageURL(
								'object', 
								{
									type:'document', 
									id:newObj.id,
									name:newObj.name
								}
							);
						}
					};
					
					documentCreateEdit(ui, newObj);														
				},
				view: true
			},
			domObj:dom,
			fields:{
				name:{
					title:'Title',
					icon:function(obj){

						var titleIcon = (obj.document_type === 'folder') 
							? '<i class="folder brown icon" style="display:inline-block"></i>'
							: null 
							
						return titleIcon;
																								
					},
					type:'title',
					counter:true,
// 					charLimit:25
				},
				parent:{
					title:'Parent',
					type:'parent',
					shouldShow:function(obj){

						if(obj.parent.id != team.id){

							return true;
						}else{
							return false;
						}
					}
				},
				body:{
					title:'Contents',
					type:'detail',
					view:function(ui, obj){

						switch(obj.document_type){
							
							case 'google_doc':
							case 'google_sheet':
							case 'google_slide':
							case 'google_other':
								
								switch(obj.document_type){
									case 'google_doc':
										var iconLink = googleDocImage;
										break;
									case 'google_sheet':
										var iconLink = googleSheetImage;
										break;
									case 'google_slide':
										var iconLink = googleSlideImage;
										break;
									case 'google_other':
										var iconLink = googleFileImage;
										break;
								}
								
								ui.makeNode('google', 'div', {tag:'img', css:'ui mini middle aligned left floated image', src:iconLink});
								ui.makeNode('go', 'div', {text:'View in Google <i class="fa fa-external-link"></i>', tag:'a', css:'animated fadeIn revealChild', href:obj.share_link, target:'_blank'});
								
								break;
								
							default:
							
								if(obj.body !== null || obj.body != undefined){
							
									ui.makeNode('bodyText', 'div', {
										text:splitString(removeHTMLTags(obj.body), 
											_.contains(removeHTMLTags(obj.body), ' '))
										, css: 'truncate'
									});
																										
								} else {
									
									ui.makeNode('bodyText', 'div', {text:'<em>No Content Saved</em>'});
									
								}
							
						}
																									
					}
				},
				created_by:{
					title:'Author',
					view:function(ui, obj){
						
						var staff = obj.created_by;
						
						if(staff){
							
							if(staff.profile_image){
							
								if(staff.profile_image.id){
								
									ui.makeNode('label-'+staff.id, 'div', {css:'ui image basic label', tag:'a', text:staff.fname +' '+ staff.lname.charAt(0) +'.'})
										.makeNode('image', 'div', {tag:'img', src:sb.data.files.getURL(staff.profile_image)});
									
								}else{
									
									ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
									
								}
								
							}else{
								
								ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
								
							}
							
						}
						
					},
					type:'author'
				},
				category:{
					title:'Category',
					type:'type'
				},
				date_created:{
					title:	'Created On'
					, type: 'date'
					, edit: false
				},
				is_public:{
					title:'Draft',
					view:function(ui, obj){
						
						var label = {
							text:'Draft',
							color:'ui orange label'
						};
						
						if(obj.document_type == 'folder'){
							label.text = 'Private';
							label.color = 'ui brown label'
						}	

						if(obj.is_public == 0)
							ui.makeNode('label', 'div', {css: label.color, text:label.text});
					}
				}
			},
			objectType:'document',
			selectedView:'list',
			singleView:{
				view:function(ui, obj, onComplete){

					documentView(ui, {object:obj, edit:false, showSegment:false});
					
					onComplete({dom:ui});
					
				},
				select:3
			},
			fullView:{
				type:'jobTypeTool',
				id:'messageBoardTool'
			},
			parseData: parse_collections, 
			where:where
		};

		sb.notify({
			type: 'show-collection',
			data: collection
		});
												
	}	
	
	function parse_collections(data, callback, query, subview, range, types) {

		var docsList = data.data;														
		var output = [];

		_.each(docsList, function(docObj, i) {
			
			if(docObj.is_public === 1) {
				
				output.push(docObj);
				
			}

			if(docObj.created_by){
				
				if(docObj.is_public === 0 && docObj.created_by.id === +sb.data.cookie.get('uid')) {
					
					output.push(docObj);
					
				}
				
			}else{
				
				if(docObj.is_public === 0){
					
					output.push(docObj);
					
				}
				
			}
			
		});
		
		data.data = output;
		
		callback(data);
														
	}
	
	function main_toolCollection(dom, state, draw, setupArgs) {

		var relatedObj = {};
		var where = {
			childObjs:{
				name:true,
				body:true,
				category:true,
				is_public:true,
				file_upload:true,
				created_by:{
					fname:true,
					lname:true,
					profile_image:true
				},
				document_type:true,
				related_object:true,
				date_created:true,
				tagged_with:true,
				comment_count:true,
				parent:{
					name:true,
					group_type:true
				}
			}
		};	

		if (state.hasOwnProperty('layer')) {
			
			if (state.layer === 'team') {
				
				where.tagged_with = [state.team.id];
				relatedObj = state.team;
				
			} else if (state.layer === 'project') {
				
				where.tagged_with = [state.project.id];
				relatedObj = state.project;
				
			} else if (state.layer === 'object') {
				
				where.tagged_with = {
					type: 'any',
					values: [state.pageObject.id],
					or: {
						shared_with: {
							type: 'any',
							values: [state.pageObject.id]
						}
					}
				}
				relatedObj = state.pageObject;
				
			} else if (state.layer === 'myStuff') {
				
				where.tagged_with = [+sb.data.cookie.get('uid')];
				relatedObj.id = +sb.data.cookie.get('uid');
				
			} else {
				
				relatedObj = state.headquarters;
				
			}
			
		}
		
		var collectionsSetup = {
			state: state,
			actions:{
				create:function(ui, newObj, onComplete){

					newObj.obj = relatedObj;
					newObj.related_object = relatedObj.id;
					newObj.parent = relatedObj.id;
					newObj.tagged_with = [relatedObj.id, +sb.data.cookie.get('uid')];
					newObj.tagged_with = _.uniq(newObj.tagged_with);
					newObj.action = {
						onSave:function(newObj){

							if (
								_.isArray(newObj)
							) {
								
								ui.hide();
								
								main_toolCollection(dom, state, draw, setupArgs);
								
							} else {
								
								window.location.href = sb.data.url.createPageURL(
									'object', 
									{
										type:'document', 
										id:newObj.id,
										name:newObj.name
									}
								);	
								
							}
							
						}
					};

					if (state && state.pageObjectType === 'contacts') {
						newObj.shared_with = [relatedObj.id];
					}

					documentCreateEdit(ui, newObj);
																				
				},
				view:true,
				fullscreen:{
					icon:'eye',
					color:'blue',
					title:'View',
					domType:'navigate',
					action:function(item, ui, onComplete){
						
						window.location.href = sb.data.url.createPageURL(
							'object', 
							{
								type:'document', 
								id:item.id,
								name:item.name
							}
						);
						
																					
					}
				},
				download: true
			},
			domObj:dom,
			fields:{
				name:{
					title:'Title',	
					icon:function(obj){

						var titleIcon = (obj.document_type === 'folder') 
							? '<i class="folder brown icon" style="display:inline-block"></i>'
							: null 
							
						return titleIcon;
																								
					},
					type:'title',
					counter:true,
// 						charLimit:25
				},
				parent:{
					title:'Parent',
					type:'parent',
					shouldShow:function(obj){

						if(obj.parent.id !== relatedObj.id){

							return true;
						}else{
							return false;
						}
					}
				},
				body:{
					title:'Contents',
					type:'detail',
					view:function(ui, obj){

						if(obj.body !== null || obj.body != undefined){
							
							if (obj.document_type == 'upload') {
								
								ui.makeNode('bodyText', 'div', {text:'Image preview'});
								
								switch(obj.document_type){
			
									case 'folder':
					
										//ui.makeNode('bodyText', 'div', {text:'FOLDER'});		
					
										break;
									
									case 'share':
									case 'other':
									case 'google':
									case 'google_doc':
									case 'google_sheet':
									case 'google_slide':
									case 'google_other':											
									case 'custom_file':
									case 'upload':
									
										if(obj.file_upload){
											switch(obj.file_upload.file_type){
												case 'pdf':
													var iconObj = {tag:'i', css:'large file pdf icon'};
													break;
												case 'jpg':
												case 'jpeg':
												case 'png':
													var iconObj = {tag:'img', src:sb.data.files.getURL(obj.file_upload), css:'ui fluid centered image'};
													break;	
												default:
													var iconObj = {tag:'i', css:'large file icon'};
											}
										}
					
										ui.makeNode('bodyText', 'div', iconObj);
									
										break;
									
									default:
																									
								}
								
							}else{
								
								ui.makeNode('bodyText', 'div', {
									text:splitString(removeHTMLTags(obj.body), 
										_.contains(removeHTMLTags(obj.body), ' '))
								});
								
							}
																																
						} else {
							
							ui.makeNode('bodyText', 'div', {text:'<em>No Content Saved</em>'});
							
						}
																									
					}
				},
				created_by:{
					title:'Author',
					view:function(ui, obj){
						
						var staff = obj.created_by;
						
						if(staff){
							
							if(staff.profile_image){
							
								if(staff.profile_image.id){
								
									ui.makeNode('label-'+staff.id, 'div', {css:'ui image basic label', tag:'a', text:staff.fname +' '+ staff.lname.charAt(0) +'.'})
										.makeNode('image', 'div', {tag:'img', src:sb.data.files.getURL(staff.profile_image)});
									
								}else{
									
									ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
									
								}
								
							}else{
								
								ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
								
							}
							
						}
						
					},
					type:'author'
				},
				category:{
					title:'Category',
					type:'type'
				},
				date_created:{
					title:	'Created On'
					, type: 'date'
					, edit: false
				},
				is_public:{
					title:'Draft',
					view:function(ui, obj){
						
						var label = {
							text:'Draft',
							color:'ui orange label'
						};
						
						if(obj.document_type == 'folder'){
							label.text = 'Private';
							label.color = 'ui brown label'
						}	

						if(obj.is_public == 0)
							ui.makeNode('label', 'div', {css: label.color, text:label.text});
					}
				}
			},
			objectType:'document',
			selectedView: collectionsDefaultSubView,
			layer: 'hq',
			singleView:{
				view:function(ui, obj, onComplete){

					documentView(ui, {object:obj, edit:false, showSegment:false});
					
					onComplete({dom:ui});
					
				},
				select:3
			},
			fullView:{
				type:'hqTool',
				id:'messageBoardTool'
			},
			parseData: parse_collections,  
			where: where
		};
		
        if ( appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz') {
            collectionsSetup.actions.sharePortal = {
                instance: true
                , ui: function(modal, item, options){

                    function processForm(form, callback){ 
                        var formData = form.process();
                        if(formData.fields.name == ''){
                            sb.dom.alerts.alert('Error', 'Please provide a name. This is what the client will see in their Portal', 'error');
                            return;
                        }
                        callback(formData.fields.name.value);
                        return;
                    }

                    modal.body.empty();

                    modal.body.makeNode('header', 'div', {
                        tag: 'h2'
                        , text: 'Share in Portal'
                        , css: 'ui header'
                    });
                    modal.body.makeNode('helper', 'div', {text: 'Create a \'Deliverable\' item and add it to the Client Service Record.'});
                    modal.body.makeNode('divider', 'div', {
                        css: 'ui divider'
                    });
                    modal.body.makeNode('body', 'div', {});

                    var formSetup = {
                        name:{
                            name:'name'
                            , label:'Name (What the client will see)'
                            , type:'text'
                            , placeholder: item.name
                        }
                    };

                    modal.body.body.makeNode('form', 'form', formSetup);
                    modal.process = processForm.bind(
                        {}
                        , modal.body.body.form
                    ) ;

                    // Run button
                    modal.body.body.makeNode('runBtn', 'div', {
                        text: 'Share in Portal',
                        css: 'ui large green button pull-right',
                    }).listeners.push(
                        function (selector) {

                            $(selector).on(
                                'click'
                                , function () {

                                    // Turn on loading on button
                                    modal.body.body.runBtn.loading();
                                    modal.process(function(val){

                                        sb.data.db.service(
                                            'FGShareFilesToPortal'
                                            , 'createDeliverable'
                                            , {
                                                name: val
                                                , file: item.id
                                                , instance: appConfig.instance
                                            }
                                            , function(response) {
                                                modal.body.body.runBtn.loading(false);   
                                                modal.hide();                    
                        
                                            }
                                        );

                                    });
                                    
                                }
                            );
                            
                        }
                    );

                    // Cancel button
                    modal.body.body.makeNode('cancelBtn', 'div', {
                        css: 'ui large light-grey basic button pull-right',
                        text: 'Cancel'
                    }).notify('click', {
                        type: 'run-action',
                        data: {
                            run:function() {

                                // Hide modal
                                modal.hide();

                            }.bind({})
                        }
                    }, sb.moduleId);

                    modal.body.body.makeNode('clear', 'div', {
                        style: 'clear:both'
                    });

                    modal.body.patch();

                    modal.show();      
                }
            }
        }
		
		if (setupArgs.hasOwnProperty('collections')) {
			
			_.each(setupArgs.collections, function(v, k) {
				
				collectionsSetup[k] = v;
				
			});
			
		}

		if (state && state.pageObjectType === 'contacts') {
			
			delete collectionsSetup.where.tagged_with;
			delete collectionsSetup.where.parent;
			delete collectionsSetup.entity;

			collectionsSetup.where.shared_with = {
				type: 'any'
				, values: [state.pageObject.id]
				, or: {
					parent: state.pageObject.id
				}
			};

		}

		sb.notify({
			type:'show-collection',
			data: collectionsSetup
		});
		
	}	
	
	// single view
	
	function viewDocument (edit) {
		
		function viewContent (ui, obj) {
			
			function editMode () {
				
				var toolbarOptions = [
					['bold', 'italic', 'underline', 'strike'],        // toggled buttons
					['blockquote', 'code-block'],
					
// 					[{ 'header': 1 }, { 'header': 2 }],               // custom button values
					[{ 'list': 'ordered'}, { 'list': 'bullet' }],
					[{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
					[{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
					[{ 'direction': 'rtl' }],                         // text direction
					
// 					[{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
					[{ 'header': [1, 2] }],
					
// 					[{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
// 					[{ 'font': [] }],
// 					[{ 'align': [] }],
					
// 					['clean']                                         // remove formatting button
				];
				
				editor = new Quill(ui.content.selector, {
					theme: 'snow',
					placeholder: 'Compose an epic...',
					modules: {
						toolbar: toolbarOptions
					}
				});
				
				if ($(window).width() > 768) {
					
					$(ui.selector).find('.ql-toolbar').addClass('ui sticky');
					$('.ui.sticky')
					  .sticky({
					    context: '#mainContainerArea',
					  })
					;
					
				}
				
			}
			
			function viewMode () {
				
				ui.actions.edit.removeClass('hidden');
				
				ui.actions.post.addClass('hidden');
				ui.actions.saveDraft.addClass('hidden');
				
// 				document.getElementById('doc-editor').classList.add('inactive');
				editor.enable(false);
				$(ui.selector).find('.ql-toolbar').addClass('hidden');
				
			}
			
			function saveDoc (html, callback) {
				
				var message = {
					id: 				obj.id
					, body: 			html
				};
				
				sb.data.db.obj.update('document', message, function(res){
	
					if(res){
	
						var noteObj = {
							icon: {
								icon: 'pencil',
								color: 'blue'
							},
							type_id:		res.id,
							type:		'document',
							note:		`Edits made to message: ${res.name}`,
							record_type:	'log',
							author: 		sb.data.cookie.get('uid'),
							notification: {
								producer: res.id
							},
							notifyUsers:	[],
							log_data:{
								type:		'update',
								objectName:	res.name,
								details:		splitString(removeHTMLTags(res.body), _.contains(removeHTMLTags(res.body), ' '))
							}
						};

						sb.data.db.obj.postComment(noteObj, function(newNote){
	
							taggedUsers = [];
								
							//sb.dom.alerts.alert('Success!', dbSAlert, 'success');
							callback(res);
							
						});
					
					}
						
				});
				
			}
			
			switch (obj.document_type) {
				
				case 'folder':
					sb.notify({
						type:'show-collection',
						data:{
							actions:{
								create:function(ui, newObj, onComplete){

									newObj.parent = obj.id;
									newObj.tagged_with = obj.tagged_with;
									newObj.action = {
										onSave:function(newObj) {

											function goToDocument(newObj) {

												window.location.href = sb.data.url.createPageURL(
													'object', 
													{
														type: 'document', 
														id: newObj.id,
														name: newObj.name
													}
												);

											}

											if (newObj[0]) {

												if (newObj.length > 1) {

													window.location.reload();

												} else {

													newObj = newObj[0];
													goToDocument(newObj);

												}
												
											} else {

												goToDocument(newObj);

											}
											
										}
									};
									
									documentCreateEdit(ui, newObj);
																								
								},
								view:true,
								fullscreen:{
									icon:'eye',
									color:'blue',
									title:'View',
									domType:'navigate',
									action:function(item, ui, onComplete){
										
										window.location.href = sb.data.url.createPageURL(
											'object', 
											{
												type:'document', 
												id:item.id,
												name:item.name
											}
										);
										
																									
									}
								}
							},
							domObj:ui,
							fields:{
								name:{
									title:'Title',
									icon:function(obj){

										var titleIcon = (obj.document_type === 'folder') 
											? '<i class="folder inverted grey icon" style="display:inline-block"></i>'
											: null 
											
										return titleIcon;
																												
									},
									type:'title',
									counter:true,
// 									charLimit:25
								},
								body:{
									title:'Contents',
									type:'detail',
									view:function(ui, obj){
																																					
										if(obj.body !== null || obj.body != undefined){
											
											ui.makeNode('bodyText', 'div', {
												text:splitString(removeHTMLTags(obj.body), 
													_.contains(removeHTMLTags(obj.body), ' '))
												, css: 'truncate'
											});
																												
										} else {
											
											ui.makeNode('bodyText', 'div', {text:'<em>No Content Saved</em>'});
											
										}
																													
									}
								},
								created_by:{
									title:'Author',
									view:function(ui, obj){
										
										var staff = obj.created_by;
										
										if(staff){
											
											if(staff.profile_image){
											
												if(staff.profile_image.id){
												
													ui.makeNode('label-'+staff.id, 'div', {css:'ui image basic label', tag:'a', text:staff.fname +' '+ staff.lname.charAt(0) +'.'})
														.makeNode('image', 'div', {tag:'img', src:sb.data.files.getURL(staff.profile_image)});
													
												}else{
													
													ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
													
												}
												
											}else{
												
												ui.makeNode('label-'+staff.id, 'div', {css:'ui basic label', tag:'a', text:staff.fname +' '+ (staff.lname || '').charAt(0) +'.'});
												
											}
											
										}
										
									},
									type:'author'
								},
								category:{
									title:'Category',
									type:'type'
								},
								is_public:{
									title:'Draft',
									view:function(ui, obj){
										
										var label = {
											text:'Draft',
											color:'ui orange label'
										};
										
										if(obj.document_type == 'folder'){
											label.text = 'Private';
											label.color = 'ui inverted grey label'
										}	

										if(obj.is_public == 0)
											ui.makeNode('label', 'div', {css: label.color, text:label.text});
									}
								},
								date_created:{
									title:	'Created On'
									, type: 'date'
									, edit: false
								}
							},
							objectType:'document',
							selectedView:'list',
							singleView:{
								view:function(ui, obj, onComplete){

									documentView(ui, {object:obj, edit:false, showSegment:false});
									
									onComplete({dom:ui});
									
								},
								select:3
							},
							fullView:{
								type:'hqTool',
								id:'messageBoardTool'
							}, 
							where:{
								parent:obj.id,
								childObjs:{
									name:true,
									body:true,
									category:true,
									is_public:true,
									created_by:{
										fname:true,
										lname:true,
										profile_image:true
									},
									related_object:true,
									document_type:true,
									parent:true,
									date_created:true,
									tagged_with:true
								}
							}
						}
					});	
					break;
					
				case 'share':
				case 'other':
				case 'google':
				case 'google_doc':
				case 'google_sheet':
				case 'google_slide':
				case 'google_other':
					
					ui.makeNode('c', 'div', {});
					switch(obj.storage_type || obj.document_type){
						
						case 'google':
						
							var iconLink = googleDocImage;
							var buttonColor = 'blue';
						
							break;
						
						case 'dropbox':
						
							var iconLink = dropboxImage;
							var buttonColor = 'blue';
						
							break;
						
						case 'onedrive':
							
							var iconLink = onedriveImage;
							var buttonColor = 'blue';
							
							break;
							
						case 'box':
						
							var iconLink = boxIncImage;
							var buttonColor = 'blue';
						
							break;	
						
						case 'google_doc':
							var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-doc.svg';
							var buttonColor = 'blue';
							break;
						case 'google_sheet':
							var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-sheet.svg';
							var buttonColor = 'green';
							break;
						case 'google_slide':
							var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-slide.svg';
							var buttonColor = 'yellow';
							break;
						case 'google_other':
							var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-other.svg';
							var buttonColor = 'teal';
							break;
					}

					ui.c.makeNode('cont', 'div', {css:'ui center aligned basic segment'});
					ui.c.cont.makeNode('bodyIcon', 'div', {tag:'img', src:iconLink, style:'width:100px;'});
					ui.c.cont.makeNode('bodyTitle', 'div', {css:'ui huge header', text:obj.name});
					ui.c.cont.makeNode('bodyLink', 'div', {
						tag:'a', 
						href: sb.dom.cleanURL(obj.share_link), 
						target:'_blank', 
						css:'ui large '+ buttonColor +' button', 
						text:'View Shared Link'
					});
					ui.patch();
				
					break;
					
				case 'custom_file':
				case 'upload':
					ui.makeNode('c', 'div', {});
					if(obj.file_upload){
						switch(obj.file_upload.file_type){
							case 'pdf':
								var iconObj = {tag:'i', css:'large file pdf icon'};
								break;
							case 'jpg':
							case 'jpeg':
							case 'png':
								var iconObj = {tag:'img', src:sb.data.files.getURL(obj.file_upload), css:'ui fluid centered image'};
								break;	
							default:
								var iconObj = {tag:'i', css:'large file icon'};
						}
					}
					
					ui.c.makeNode('cont', 'div', {css:'ui center aligned basic segment'});
					ui.c.cont.makeNode('bodyIcon', 'div', iconObj);
					ui.c.cont.makeNode('bodyTitle', 'div', {css:'ui huge header', text:obj.name+'.'+obj.file_upload.file_type});
					ui.c.cont.makeNode('bodyLink', 'div', {tag:'a', href:'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/'+ appConfig.instance +'/'+ obj.file_upload.loc, target:'_blank', css:'ui large teal button', text:'Download File'});
					ui.patch();
					break;
					
				default:
					var state = {};
					var editor = {};
					
					// action buttons
					ui.makeNode(
						'actions'
						, 'div'
						, {
							css: 'ui borderless secondary right floated mini menu'
						}
					);
					
					if (!edit) {
						
						ui.actions.makeNode(
							'edit'
							, 'div'
							, {
								tag: 'a'
								, css: 'yellow circular icon ui button'
								, text: '<i class="ui pencil icon"></i>'
								, href: sb.data.url.createPageURL(
									'object'
									, {
										edit: 	true
										, type: 'document'
										, id: 	obj.id
										, name: ''
									}
								)
							}
						);
						
					} else {
						
						ui.actions.makeNode(
							'save'
							, 'div'
							, {
								tag: 'button'
								, css: 'teal ui tiny button'
								, text: 'Save'
							}
						).notify('click', {
							type: 'document-run'
							, data: {
								run: function () {

									sb.notify({
										type: 'display-alert',
										data: {
											header: 'Saved',
											body: 'Your document was saved successfully',
											color: 'green'
										}
									});

									ui.actions.save.loading();
									var html = editor.root.innerHTML;
									saveDoc (html, function (res) {
										ui.actions.save.loading(false);
									});
									
								}
							}
						}, sb.moduleId);
						
					}
					
					ui.makeNode('br', 'div', {text:'<br /><div class="ui clearing divider" style="color:transparent;border-color:transparent;"></div><br />'});
					
					ui.makeNode(
						'content'
						, 'div'
						, {
							id: 'doc-editor'
							, text: 	obj.body.replace(/\n/g, '<br />')
							, css: 	'ql-editor ui clearing container'
						}
					) ;
					
					ui.patch() ;
					
					if (edit) {
						editMode ();
					}
					break;
				
			}
			
		}
		
		var view = {
			segment: {
				type: 'div'
				, css: 'ui fluid container'
				, content: {
					type: {
						type: 'type'
						, fieldName: 'category'
// 						, label: 'Type'
						, edit: true
					}
					, body: {
						type: 'view'
						, view: viewContent
					}
					, divider: {
						type: 	'div'
						, css: 	'ui clearing divider'
					}
					, bottom: {
						type: 		'column'
						, w: 		8
						, css: 		'centered'
						, content: 	{
							comments: {
								type: 'view'
								, view: function (ui, obj, options) {
									
									sb.notify({
										type: 'show-note-list-box',
										data: {
											domObj: ui,
											objectIds: [obj.id],
											objectId: obj.id,
											collapse: 'open'
										}
									});
									
								}
							}
						}
					}
				}
			}
		}
		
		return view;
		
	}	
	
	// field view (attachments)
	
	function attachmentsView (fieldName, ui, obj, options) {

		var attachments = [];
		var canCreate = true;
		var objType = '';
		var multi = options.multi;
		var allowSearch = options.allowSearch;
		var userOptions = [];
		var values = [];
		// !TODO: where should be createable in field editor
		var where = {};
		
		// helper funcs
		
		function getIcon (item) {
			
			var icon = '';
			switch (item.document_type) {
				
				case 'folder':
				icon = 'folder';
				break;
				
				case 'share':
				icon = 'linkify';
				break;
				
				case 'text':
				icon = 'file alternate';
				break;
				
				case 'upload':
				icon = 'upload';
				break;
				
			}
			
			return icon;
			
		}
		
		function getFiles (fieldName, callback) {

			if (!fieldName) {
				
				sb.data.db.obj.getWhere(
					'document'
					, {
// 						related_object: 	obj.id
						tagged_with: 	obj.id
					}
					, function (attachments) {
						
						callback(attachments);
						
					}
				);
				
			} else {
				
				callback(
					obj[fieldName]
				);
				
			}
			
		}
		
		// views
		
		function viewView(attachments, fieldName) {

			var isDefault = (fieldName == '_default');
			
			function getItemSetup (item, _extraStyle = '') {
				
				var txt = '';
				var icon = getIcon(item);
				
				link = sb.data.url.createPageURL(
					'object'
					, {
						id: 		item.id
						, type: 	item.object_bp_type
						, name: 	item.name
					}
				);
				
				switch (item.document_type) {
					
					case 'upload':
						
						if (!_.isEmpty(item.file_upload)) {
							
							return {
								tag: 'a',
								href: 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/'+ item.instance +'/'+ item.file_upload.loc,
								style: 'margin-right:2px;' + _extraStyle,
								text: '<div class="ui basic label">'+
										'<i class="ui download icon"></i>'+
										item.name +
									  '</div>'
							};
							
						}
						break;
					
				}
				
				return {
					tag: 'a',
					href: link,
					style: 'margin-right:2px;' + _extraStyle,
					text: '<div class="ui basic label">'+
							'<i class="ui '+ icon +' icon"></i>'+
							item.name +
						  '</div>'
				};
				
			}

			function confirmModalDisplay(obj) {
				if(appConfig.is_portal && appConfig.instance == 'foundation_group') {

                    var bpType = obj.object_bp_type.split('.')[0];
                    var entityObject = _.where(appConfig.Types, {bp_name: bpType.substring(1)})[0];

					if ( entityObject ) {

                        sb.dom.alerts.alert(
                            'Thank you for your attachment.'
                            , 'Your file has been successfully uploaded.  We will contact you if we have any questions.'
                            , 'warning'
                            , false
                            , false
                            , true
                            , true
                            , "Okay"
                        );
					}
				}
			}
			
			function upload(obj, fieldName, options, callback, goBack) {

				obj.tagged_with = [];
				options.inheritsTags = true;

				sb.notify({
					type:'apply-inherit-tags',
					data:{
						options: options,
						parent: obj.parent,
						obj: obj,
						onComplete: function (obj) {

							obj.tagged_with.push([obj.id, +sb.data.cookie.get('uid')]);
							if (!_.isEmpty(obj.tagged_with)) {
								obj.tagged_with.concat(obj.tagged_with);
							}
							if (!_.isEmpty(options.additionalTags)) {
								obj.tagged_with = obj.tagged_with.concat(options.additionalTags);
							}
							if(obj.object_bp_type == '#0ZZQgI'){
								obj.tagged_with.push(obj.id);
							}
							
							var newObj = {
								related_object: obj.id,
								tagged_with: _.uniq(obj.tagged_with)
							};
							var created = false;
							
							sb.notify({
								type: 'get-sys-modal'
								, data: {
									callback: function (modal) {
										
										modal.body.empty();
										modal.show();
										
										newObj.action = {
											onSave:function(newObj){

												created = true;
												modal.hide();
												
												callback(
													newObj
												);
												
												ui.patch();
												
											}
										};

										newObj.tagged_with = obj.tagged_with;
										newObj.ui = ui;
										options.obj = obj;
										documentCreateEdit(
											modal.body,
											newObj,
											undefined,
											options,
											fieldName
										);
										
									},
									onClose: function () {
										
										if (typeof goBack === 'function') {
											goBack(obj);
										} else if (!created) {
											viewView(attachments);
											ui.patch();
										}
										
									}
								}
							});
							
							return false;

						}

					}

				});
				
			}
			
			var txt = '';
			var link = '';
			var placeholder = options.placeholder || ((['foundation_group', 'rickyvoltz'].includes(appConfig.instance) || appConfig.is_portal) ? 'Click here to upload your file(s)' : 'Empty');

			
			// empty view
			if (_.isEmpty(attachments) && !options.bigUploadBtn) {
				txt = '<div class="field-placeholder">'+ placeholder +'</div>';
			}
			
			var searchNode = ui.makeNode('t', 'div', {
				text: txt,
				isTabbable: true,
				css: options.custom ? '' : 'edge-field revealParent'
			});


				if((options._default && options.context) && !isDefault) {
					searchNode.makeNode('i'+ options._default._fieldId, 'div', getItemSetup(options._default, "margin-bottom:15px;display:block-inline;")).listeners.push(
						function (s) {
							
							$(s).on('click', function (e) {
								e.stopPropagation();
							});
							
						}
					);

					var searchNode = searchNode.makeNode('br'+ options._default._fieldId, 'div', {
						text: '<br />',
						style: 'margin-bottom:10px'
					});
				}

			
			if (Array.isArray(attachments)) {
				
				_.each(attachments, function (item) {
					
					if (item) {
						searchNode.makeNode('i'+ item.id, 'div', 
							getItemSetup(item)).listeners.push(function (s) {

								$(s).on('click', function (e) {
									e.stopPropagation();
								});
								
							}
						);
					
					}
					
				});
				
			} else if (_.isObject(attachments)) {
				
				searchNode.makeNode('i'+ attachments.id, 'div', getItemSetup(attachments)).listeners.push(
					function (s) {
						
						$(s).on('click', function (e) {
							e.stopPropagation();
						});
						
					}
				);
				
			}

			
			if (options.inCollection) {
				return;
			}

			// Create the add attachment button
			if (_.isEmpty(options._default) || ((_.isEmpty(attachments) || multi) && (options.edit || options.editing))) {

				var right = !_.isEmpty(attachments) && multi ? 'right:85px !important;' : '';
				var uploadButton;

				if(options.custom && options.edit){

					placeholder = 'Add attachment';

					uploadButton = searchNode.makeNode('upload' , 'div', {
						css: 'ui left floated labeled icon button',
						text: '<i class="upload icon"></i> '+ placeholder
					});

				}
				else if (options.bigUploadBtn && options.edit) {

					uploadButton = searchNode.makeNode('upload' , 'div', {
						css: 'ui fluid blue button',
						text: '<i class="ui upload icon"></i> '+ placeholder
					});

				} else {

					uploadButton = searchNode.makeNode('upload' , 'div', {
						css: 'ui circular mini basic icon button revealChild',
						text: '<i class="ui plus green icon"></i> Add attachment',
						style: 'box-shadow:none;background-color:white;' + right
					});

				}

				$(document).ready(function() {
				
					$(uploadButton.selector).on('click', function (e) {
					
						e.stopPropagation();
						
						upload(obj, fieldName, options, function (newVal) {
							
							if (fieldName) {
								
								if (options.commitUpdates) {

									var updateVal = [];

									function getUpdateValue(newVal, callback) {

										if (
											typeof newVal === 'object'
											&& newVal.hasOwnProperty('id')
										) {
											
											if (multi || options.multi) {
												
												if (_.isEmpty(updateVal)) {

													if (_.isEmpty(attachments)) {
														updateVal = [];
													} else {
														updateVal = _.pluck(attachments, 'id');
													}

												}
												
												updateVal.push(newVal.id);
												
											} else {

												updateVal = newVal.id;

											}
											
										} else if (_.isArray(newVal)) {
											
											_.each(newVal, function(doc, i) {
												
												updateVal.push(doc);
												
											});
											
										}

										callback(updateVal);
										
									}

									function updateField(updateVal) {

										sb.data.db.obj.update(
											obj.object_bp_type, {
												id: obj.id,
												[fieldName]: updateVal
											},
											function (response) {
	
												obj[fieldName] = response[fieldName];
	
												if (typeof options.goBack === 'function') {
													options.goBack(obj);
												} else {
													ui.empty();
													viewView(obj[fieldName], fieldName);
													ui.patch();
												}
												
												
											},
											{
												[fieldName]: 1
											}
										);

									}

									if (newVal.length >= 1) {

										var i = 1;
										var count = newVal.length;

										_.each(newVal, function(val) {

											getUpdateValue(val, function(updateVal) {

												if (i == count) {
													updateField(updateVal);
												}
												
												i++;
	
											});

										});

										confirmModalDisplay(obj);

									} else {

										getUpdateValue(newVal, function(updateVal) {

											updateField(updateVal);

										});

										confirmModalDisplay(obj);

									}
									
								} else {

									sb.data.db.obj.getById(objType, newVal, function (selected) {
										
										obj[fieldName] = newVal;

										// When the parent view is in a modal already, 
										// bring that modal back up.
										if (typeof options.goBack === 'function') {
											options.goBack(obj);
										} else {

											if(fieldName == '_default'){
												var _entity = _.findWhere(appConfig.Types, {bp_name: options.bp_name});
												var index = _.findKey(_entity.blueprint, {index: options.blueprint._default.index});
												_entity.blueprint[index] = options.blueprint['_default'];

												sb.data.db.obj.update('entity_type', {id: _entity.id, blueprint: _entity.blueprint}, function(resp){
													ui.empty();
													ui.patch();
												});
											}
										}

										confirmModalDisplay(obj);
										
									});
									
								}
								
							} else {
								
								if (newVal) {
									attachments = !_.isEmpty(attachments) ? attachments : [];
									attachments.push(newVal);
								}

								// When the parent view is in a modal already, 
								// bring that modal back up.
								if (typeof options.goBack === 'function') {
									options.goBack(obj);
								} else {
									viewView(attachments, fieldName);
									ui.patch();
								}
								
							}
								
						}, options.goBack);

					});
					
				});

			}

			if( ((Array.isArray(attachments)) || _.isObject(attachments)) && !isDefault ) {
				// Create the clear button
				if (!_.isEmpty(attachments) && (options.edit || options.editing)) {
					var clearButton = searchNode.makeNode('clear', 'div', {
						css: 'ui circular mini basic icon button revealChild',
						text: '<i class="ui times red icon"></i> Clear',
						style: 'box-shadow:none;background-color:white;'
					});
					$(document).ready(function () {
						$(clearButton.selector).on('click', function (e) {

						e.stopPropagation();
						$(searchNode.selector).dropdown('hide');

						sb.dom.alerts.ask({
							title: 'Are you sure?',
							text: 'This will remove the attachment(s).'
						}, function(resp) {
							
							if (resp) {
								
								swal.close();

								sb.data.db.obj.update(obj.object_bp_type, {
									id: obj.id,
									[fieldName]: []
								}, function (response) {
										
									ui.empty();
									obj[fieldName] = [];
									viewView([], fieldName);
									ui.patch();
										
								},
								{
									[fieldName]: 1
								});

							}
						
						});

						sb.data.db.obj.erase(
							'document',
							attachments.id,
							function (done) {
							}
						  );

						});
					});
				}
			} else if(isDefault && _.isObject(attachments)) {
				if (options.edit || options.editing) {
					var clearButton = searchNode.makeNode('clear', 'div', {
						css: 'ui circular mini basic icon button revealChild',
						text: '<i class="ui times red icon"></i> Clear',
						style: 'box-shadow:none;background-color:white;'
					});
					$(document).ready(function () {
						$(clearButton.selector).on('click', function (e) {

							e.stopPropagation();
							$(searchNode.selector).dropdown('hide');

							sb.dom.alerts.ask({
								title: 'Are you sure?',
								text: 'This will remove the attachment by default'
							}, function (resp) {

								if (resp) {

									swal.close();

									var _entity = _.findWhere(appConfig.Types, {bp_name: options.bp_name});
									var index = _.findKey(_entity.blueprint, {index: options.blueprint._default.index});

									delete _entity.blueprint[index].options['_default'];

									sb.data.db.obj.update('entity_type', {id: _entity.id, blueprint: _entity.blueprint}, function(resp){
										delete obj['_default'];
										ui.empty();
										viewView(obj[fieldName], fieldName);
										ui.patch();
									});

								}

							});

						});
					});
				}
			}

			// Set the click event to start the edit mode
			if ( options.edit || options.editing ) {
				
				if ( allowSearch || multi ) {

					$(document).ready(function() {
						$(searchNode.selector).off('click');
						$(searchNode.selector).on('click', function (e) {
							e.stopPropagation();
							$(searchNode.selector).off('click');
							editView(attachments, searchNode.selector);
							ui.patch();
						});
					});

				}
				
			}

		}
		
		function editView(attachments, selector) {
			
			function setSelection(selection) {
				
				function createIfNeeded(obj, fieldName, selection, callback) {
					
					if ( selection === 'new' || _.contains(selection, 'new') ) {

						obj.tagged_with = [];

						sb.notify({
							type:'apply-inherit-tags',
							data:{
								options: options,
								parent: obj.parent,
								obj: obj,
								onComplete: function (obj) {

									obj.tagged_with.push([obj.id, +sb.data.cookie.get('uid')]);
									if (!_.isEmpty(obj.tagged_with)) {
										obj.tagged_with.concat(obj.tagged_with);
									}
									if (!_.isEmpty(options.additionalTags)) {
										obj.tagged_with = obj.tagged_with.concat(options.additionalTags);
									}
						
									var newObj = {
										related_object: obj.id,
										tagged_with: _.uniq(obj.tagged_with)
									};
									var created = false;
									
									sb.notify({
										type: 'get-sys-modal',
										data: {
											callback: function (modal) {
												
												modal.body.empty();
												modal.show();
												
												newObj.action = {
													onSave:function(newObj){
														
														created = true;
														modal.hide();
														
														callback(obj, fieldName, newObj);
														ui.patch();
														
													}
												};

												newObj.tagged_with = obj.tagged_with;
												documentCreateEdit(modal.body, newObj);
												
											},
											onClose: function () {
												
												if (!created) {
													viewView(attachments);
													ui.patch();
												}
												
											}
										}
									});
									
									return false;

								}

							}

						});
						
					} else {

						if (fieldName == undefined) {
							
							var toRemove = [];
							var selectionAsInts = _.map(selection, function (v) {
								return parseInt(v);
							});
							
							// get attachments to remove
							_.each(attachments, function (att) {
								
								if ( !_.contains(selectionAsInts, att.id) ) {
									
									toRemove.push(
										att.id
									);
								
								}
								
							});
	
							if (!_.isEmpty(toRemove)) {
								
								sb.data.db.obj.removeTag(
									toRemove,
									obj.id,
									function (response) {
										
										attachments = _.filter(
											attachments,
											function (att) {
												
												return !_.contains(
													toRemove,
													att.id
												);
												
											}
										);
										
										callback(obj, fieldName, false) ;
										
									}
								);
								
							} else {
								
								callback(obj, fieldName, false) ;
								
							}
							
						} else {
							
							callback(obj, fieldName, selection) ;
							
						}
						
					}
					
				}
				
				if (typeof options.onEditEnd === 'function')
					options.onEditEnd();

				if (multi) {
					
					selection = selection.split(',');
					
				} else {
					
					selection = parseInt(selection);
					if (isNaN(selection)) {
						selection = 0;
					}
					
				}	

				searchNode.loading();
				$(searchNode.selector).dropdown('destroy');

				createIfNeeded(obj, fieldName, selection, function(obj, fieldName, selection) {

					if (fieldName) {
						
						if (options.commitUpdates) {
							
							var updateVal = selection;
							if ( typeof selection === 'object' && selection.hasOwnProperty('id') ) {
								
								if (multi) {
									
									if (_.isEmpty(attachments)) {
										updateVal = [];
									} else {
										updateVal = _.pluck(attachments, 'id');
									}
									
									updateVal.push(selection.id);
									
								} else {

									updateVal = selection.id;

								}
								
							}

							sb.data.db.obj.update(obj.object_bp_type, {
								id: obj.id,
								[fieldName]: updateVal
							}, function (response) {
									
								ui.empty();
								obj[fieldName] = response[fieldName];
								viewView(obj[fieldName]);
								ui.patch();
									
							},
							{
								[fieldName]: 1
							});
							
						} else {

							sb.data.db.obj.getById(objType, selection, function (selected) {
								
								obj[fieldName] = selected;
								ui.empty();
								attachmentsView(fieldName, ui, obj, options);
								ui.patch();
								
							});
							
						}
						
					} else {
						
						if (selection) {
							attachments.push(selection);
						}

						viewView(attachments);
						ui.patch();
						
					}
						
				});
				
			}
			
			if (
				options.blueprint
				&& options.blueprint[fieldName]
				&& !_.isEmpty(options.blueprint[fieldName].options)
			) {
				
				objType = options.blueprint[fieldName].options.objectType;
				multi = options.blueprint[fieldName].options.multi;
				
			} else if (options.objectType) {
				
				objType = options.objectType;
				multi = options.multi || false;
				if (options.where) {
					where = options.where;
				}
				
			}
			
			if (options.hasOwnProperty('canCreate')) {
				canCreate = options.canCreate;
			}
			
			// Initialize values
			var values = [];

			if (multi) {
				
				values = _.map(attachments, function (obj) {
	
					return {
						value: obj.id,
						name: '<i class="'+ getIcon(obj) +' icon"></i>'+ obj.name,
						selected: true
					};
					
				});
					
			} else if (!_.isEmpty(attachments)) {
				
				values = [{
					value: attachments.id,
					name: attachments.name,
					selected: true
				}];
				
			}

			// Set max selections
			var maxSelections = multi ? 1000 : 1;

			// Create a drop-down if search is turned on
			var searchSetup = {
				text: '<input type="hidden" name="attachments" class="" value="">' +
						'<i class="dropdown icon"></i>' +
						'<input class="search" autocomplete="off" tabindex="0" style="margin:0 !important; padding:0 !important;">' +
						'<span class="sizer" style=""></span>' +
						'<div class="default text">Select attachment...</div>',
				css: 'ui fluid transparent multiple selection search dropdown',
				listener: {
					type: 'dropdown',
					values: values,
					saveRemoteData: false,
					minCharacters: 0,
					forceSelection: false,
					maxSelections: maxSelections
				}
			}

			if (allowSearch) {

				searchSetup.listener.apiSettings = {
					cache: false,
					url: databaseConnection.obj.getSearchPath(
						'document'
						, where
						, {}
						, {
							parentObjectType: obj.object_bp_type.replace('#', '%23')
						}
					),
					onResponse: function (raw) {

						var results = [];
						var selectedIds = _.pluck(values, 'value');
						_.each(raw.results, function (item) {
							
							var selection = $(searchNode.selector).dropdown('get value').toString();
							if (multi) {
								selection = selection.split(',');
							}
							
							// filter out items without names, and items already
							// added to selection
							if ( !_.isEmpty(item.name) && !_.contains(selectedIds, item.id) ) {
								
								var icon = '';
								switch (item.document_type) {
									
									case 'share':
									icon = 'linkify';
									break;
									
									case 'text':
									icon = 'file alternate';
									break;
									
									case 'upload':
									icon = 'upload';
									break;
									
								}
		
								results.push({
									value: parseInt(item.id),
									name: '<i class="grey '+ icon +' icon"></i>'+ item.name
								});
								
							}
							
						});
						
						return {
							results: results
						};
						
					},
					mockResponseAsync: function(settings, callback){
						
						function GetCookieValue(name) {
							var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
							return found.length > 0 ? found[0].split("=")[1] : null;
						}
						
						$.ajax({
							type: 'post',
							url: settings.url,
							xhrFields: {
								withCredentials: true
							},
							crossDomain: true,
							data: {},
							success: function (response) { callback(response); },
							error: function (jqXHR, status, error) {},
							headers: {
								'bento-token': GetCookieValue('token')
							}
						});	
						
					}
				};

			}

			// Create the field
			var searchNode = ui.makeNode('t', 'div', searchSetup);

			// On hide
			searchSetup.listener.onHide = function() {

				// Make sure the dropdown gets cleared
				if ( _.isEmpty($(searchNode.selector).dropdown('get value')) ) {
					$(searchNode.selector).dropdown('clear');
				}

				// Snag the selection
				selection = $(searchNode.selector).dropdown('get value');

				// Set and save the selection to the database and close out edit mode
				setSelection(selection);

				// Reset the click event to turn on edit mode
				$(selector).on('click', function (e) {
					$(selector).off('click');
					e.stopPropagation();
					editView(attachments, selector);
					ui.patch();
				});

			}

			// Find the search field and focus on it
			searchNode.listeners.push(function (selector) {
				$(selector).find('.search').focus();
				$(searchNode.selector).dropdown('show');
			});

		}
			
		ui.empty();

		getFiles (fieldName, function (resp) {

			attachments = resp;
			viewView(attachments, fieldName);
			
			ui.patch();
			
		});
		
	}
	
	return {
		
		init: function(){

			sb.listen({
				'document-run':				this.run
				, 'show-object-attachments': this.showAttachments
				, 'show-custom-object-attachments': this.showCustomAttachments
				, 'build-documents-collection': this.buildDocumentsCollection
			});
			
			if($(window).width() <= 768) {
				
				onMobile = true;
				collectionsDefaultSubView = 'list';
				
			}
			
			var toolRegistrationsSetup = [
					// Main tool
					{
						id:'messageBoardTool',
						layers: ['hq', 'team', 'project', 'myStuff', 'object'],
						name:'Files',
						tip:'Create and View Headquarter messages',
						icon:{
							type:'folder open',
							color:'blue'
						},
						default:true,
						settings:[
							{
								object_type: 'document_category',
								name: '1. File Categories'
							}
						],
						mainViews:[
							{
								dom:function(dom, state, draw, mainDom, setup){
									
									var setupArgs = {};
									
									if (setup !== undefined) {
										
										setupArgs = setup
										
									}
									
									main_toolCollection(dom, state, draw, setupArgs);
									
								}
							}
						],
						boxViews:[
							// 'Recent Documents'
							{
								id:'recentDocuments',
								width: 'eight',
								title: 'Recent Files',
								collections: {
									selectedView: 'list',
									actions: {},
									fields: {
										created_by: {
											title: 'Created By',
											type: 'title',
											view: function(ui, obj) {

												ui.makeNode('feed', 'div', {
													css: 'ui small feed'
												});
												
												ui.feed.makeNode('ev-' + obj.id, 'div', {
													css: 'event'
												});

												if(
													!_.isEmpty(obj.created_by)
													&& !_.isEmpty(obj.created_by.profile_image)
													&& obj.created_by.profile_image.loc !== undefined
												) {
													
													if(obj.created_by.profile_image.loc != "//" && obj.created_by.profile_image !== null && obj.created_by.profile_image !== undefined) {
													
														ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {
															css: 'label'
														}).makeNode('ava-'+obj.id, 'image', {
															url: sb.data.files.getURL(obj.created_by.profile_image)
														});
														
													} else {
														
														ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {css: 'label'}).makeNode('ava-'+obj.id, 'div', {tag:'i', css: 'user circle icon'});
															
													}
													
												} else {
													
													ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {css: 'label'}).makeNode('ava-'+obj.id, 'div', {tag:'i', css: 'user circle icon'});
													
												}
												
											}
										},
										name: {
											title: 'Name',
											type: 'title',
											isSearchable: true												
										},
										date_created: {
											title: 'Created',
											type: 'title',
											view: function(ui, obj) {
												
												ui.makeNode('date', 'div', {
													css: 'date', 
													text:`Created ${moment(obj.date_created).local().fromNow()}</span>`
												});
												
											}
										}
									},
									objectType: 'document',
									emptyMessage: 'No recent documents',
									where: {
										is_public: 1,
										childObjs: {
											name: true,
											created_by:{
												fname:true,
												lname:true,
												profile_image:true
											}
										}
									},
									subviews: {
										list: {
											hideTimeRangeFilter: true
										}
									}
								}
							}
						]
					} 
					// Single object view
					, {
						id:'document-obj',
						type: 'object-view',
						title: 'File',
						icon:'file',
						edit: true,
						dom:function(dom, state, draw){
							
							ViewDocument (
								dom
								, state
								, draw
							) ;
							return ;
							
							sb.data.db.obj.getById('document', state.id, function(message){				

								state.object = message;
							
								documentView(dom, state, draw);
								
							}, 2);
						 
						},
						header: {
							title: true,
							tags: true,
							menu:{
								followUnfollow:true
								, save: {
									action:function(o, dom, state, draw, btn){
										
										
										
										
									}
									, title:'Edit'
									, icon:'edit orange'
									, ui:'page'
									, fullView: true
								}
							},
							actions: {
								archive: 		true
								, downloadPdf: 	true
								, toggleFollow: 	true
								, move: 			true
								, toggleTemplate:true
								, togglePublic: {
									title: 	'Public'
									, icon: 		'eye'
									, color: 	'primary'
									
									// run config
									, action: 	function (obj, state, callback) {
										
										sb.data.db.obj.update(
											obj.object_bp_type
											, {
												id: 				obj.id
												, is_public: 	!obj.is_public
											}
											, function (response) {
												
												obj.is_public = response.is_public;
												callback(obj);
												
											}
										);
										
									}
									, view: 		function (obj) {
				
										if (obj.is_public) {
											
											return {
												title: 	'Public'
												, icon: 	'eye'
												, color: 'blue'
											};
											
										} else {
											
											return {
												title: 	'Private'
												, icon: 	'eye slash'
												, color: 'grey'
											};
											
										}						
										
									}
								},
								download: true
							},
							detail: false
						},
						view: viewDocument(true),
						select: 2,
						size: 'page'
					}
					// Entity Tool Message Board
					, {
						id:'messageBoardTool',
						type:'tool',
						name:'Files',
						tip:'Share files and create text documents.',
						icon: {
							type: 'folder open',
							color: 'violet'
						},
						availableToEntities: true,
						hiddenFromProjects: true,
						mainViews:[
							{
								dom: function (dom, state, draw, mainDom) {
									if (
										state
										&& state.pageObject
										&& state.pageObject.object_bp_type === 'entity_tool'
										&& state.entity > 0
									) {
											
										sb.data.db.obj.getById(
											''
											, state.entity
											, function (parentObj) {

												teamCollection(dom, state, draw, parentObj);

											}
											, {
												name: true
											}
										);	

									} else {

										teamCollection(dom, state, draw, false);

									}
									
								}
							}
						],
						boxViews:[]								
					}
					// JobType Tool Message Board
					, {
						id:'messageBoardTool',
						type:'jobTypeTool',
						name:'Files',
						tip:'Easily share thoughts and get feedback.',
						icon: {
							type: 'folder open',
							color: 'violet'
						},
						display:true,
						default:true,
						mainViews:[
							{
								dom: function(dom, state, draw){

									sb.data.db.obj.getWhere('groups'
										, {
											job_type:state.id
											, group_type: 'JobType'
										}
										, function(res){
										
											state.id = res[0].id;												
											state.pageObject = res[0];
											state.pageObjectType = res[0].object_bp_type;
											
											build_collection(dom, state, draw);
										
									});
									
								}
							}
						],
						boxViews:[
							// 'Recent Documents'
							{
								id:'recentDocuments',
								width: 'eight',
								title: 'Recent Files',
								collections: {
									selectedView: 'list',
									actions: {},
									fields: {
										created_by: {
											title: 'Created By',
											type: 'title',
											view: function(ui, obj) {
												
												ui.makeNode('feed', 'div', {
													css: 'ui small feed'
												});
												
												ui.feed.makeNode('ev-' + obj.id, 'div', {
													css: 'event'
												});

												if(obj.created_by.profile_image.loc != "//" && obj.created_by.profile_image !== null && obj.created_by.profile_image !== undefined) {
													
													ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {
														css: 'label'
													}).makeNode('ava-'+obj.id, 'image', {
														url: sb.data.files.getURL(obj.created_by.profile_image)
													});
													
												} else {
													
													ui.feed['ev-' + obj.id].makeNode('label-'+obj.id, 'div', {css: 'label'}).makeNode('ava-'+obj.id, 'div', {tag:'i', css: 'user circle icon'});
														
												}
												
											}
										},
										name: {
											title: 'Name',
											type: 'title',
											isSearchable: true												
										},
										date_created: {
											title: 'Created',
											type: 'title',
											view: function(ui, obj) {
												
												ui.makeNode('date', 'div', {
													css: 'date', 
													text:`Created ${moment(obj.date_created).local().fromNow()}</span>`
												});
												
											}
										}
									},
									objectType: 'document',
									emptyMessage: 'No recent documents',
									where: function(state) {

										return {
											is_public: 1,
											tagged_with: [state.pageObject.id],
											childObjs: {
												name: true,
												created_by: {
													profile_image: true
												}
											}
										};
											
									},
									subviews: {
										list: {
											hideTimeRangeFilter: true
										}
									}
								}
							}
						]								
					}
				];
				
			if(onMobile) { 
				
				toolRegistrationsSetup.push({
					id:'mydocs',
					type:'nav-item',
					name:'Files',
					title:'Documents',
					tip:'Create and View messages',
					icon:{
						type:'file',
						color:'blue'
					},
					default:true,
					settings:[
						{
							object_type: 'document_category',
							name: '1. File Categories'
						}
					],
					mainViews:[
						{
							dom: myDocuments
						}
					]
				});
				
			} else { 
				
				toolRegistrationsSetup = _.reject(toolRegistrationsSetup, function(reg) {
					return reg.type === 'nav-item';
				});
				
			}
					
			sb.notify({
				type: 'register-tool',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id:'messageBoard',
						title:'Message Board',
						icon:'<i class="fa fa-comments"></i>',
						views: toolRegistrationsSetup
					}
				}
			});
			
			// register field view (attachments)
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 					'attachment'
					, view: 				attachmentsView
					, title: 				'Attachment'
					, availableToEntities: 	true
					, icon: 				'paperclip'
					, getIcon: 				function (options) {
						
						return 'paperclip';
						
					}
					, propertyType: 'objectId'
					, objectType: ''
					, select: {
						name: 				true
						, document_type: 	true
						, file_upload: 		true
						, share_link: 		true
					}
					, options: {
						multi: {
							name: 	'Allow multiple selections?'
							, type: 'bool'
						}
						, placeholder: {
							type: 	'string'
							, name: 'Placeholder'
						}
						, allowSearch: {
							name: 		'Allow selections through search?'
							, type: 	'bool'
						}
						, additionalTags: {
							name: 	'Additional Tag(s)'
							, type: 'tags'
						}
						, allowLinkingToExternalFiles: {
							name: 		'Allow linking to external files?'
							, type: 	'bool'
						}
						, allowFolderCreation: {
							name: 		'Allow folder creation?'
							, type: 	'bool'
						}
						, bigUploadBtn: {
							name: 		'Show large upload button'
							, type: 	'bool'
						}
						, onlyDownload: {
							name: 		'Only download button'
							, type: 	'bool'
						}
					}
					, parseOptions: function (selection, field) {
						
						if (selection.multi) {
							field.type = 'objectIds';
						}
						field.objectType = selection.objectType;
						
						return field ;
						
					}
				}
			});

		},
		
		buildDocumentsCollection: function (setup) {
		
			myDocuments(setup.dom, setup.state, setup.draw, setup.mainDom, setup.options);
			
		},
		
		run:function(data){ data.run(); },
		
		showAttachments: function (data) {

			attachmentsView (
				undefined
				, data.dom
				, data.state.object
				, {
					edit: 		true
					, multi: 	true
					, bigUploadBtn: true
				}
				, function () {}
			);
			
		},

		showCustomAttachments: function (data) {

			attachmentsView (
				undefined
				, data.dom
				, data.state.object
				, data.options
				, function () {}
			);

		}
		
	}
	
});
