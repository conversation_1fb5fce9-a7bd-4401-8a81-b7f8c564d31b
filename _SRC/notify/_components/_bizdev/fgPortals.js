Factory.register("fg-portals", function (sb) {
  var company = null;

  // displayHome (List View) -> displayActionItemsList (Single View) .
  function displayActionItemsList(ui, display, data) {
    var contextObj = data.project;
    var actionItems = data.project["##Action Items"];
    var setup = {
      options: {
        actions: true,
        actions_ui: function (container, actionItem) {
          if (!appConfig.is_portal) {
            var css = "";
            var menuCss = "left menu";
            var actionButtonCss = "ui mini basic circular icon simple dropdown";
            var actionButtonStyle = "border:none;";
            var menuStyle = "";

            container.makeNode("actions", "div", { css: css });
            container.actions
                .makeNode("menu", "div", {
                  css: "right floated ui mini icon circular basic simple dropdown",
                  text: '<i class="ellipsis horizontal centered icon" style="font-size:1.5em !important;"></i>',
                  style: "text-align:center;",
                })
                .makeNode("menu", "div", {
                  css: menuCss,
                  // 									, style: 	'left: -100px;'
                });

            // View
            container.actions.menu.menu.makeNode("linkTo", "div", {
              css: "ui blue basic fluid item",
              text: '<i class="external square blue icon"></i> Go to',
              tag: "a",
              style: "border-radius:0px;",
              href: sb.data.url.getObjectPageParams(actionItem),
            });

            // Archive
            container.actions.menu.menu
                .makeNode("archive", "div", {
                  css: "ui red item",
                  text: '<i class="red trash icon"></i> Archive',
                  style: "border-radius:0px;",
                })
                .listeners.push(function (s) {
              $(s).on("click", function (e) {
                e.stopPropagation();

                sb.dom.alerts.ask(
                    {
                      title: "Archive " + actionItem.name + "?",
                      text: "",
                    },
                    function (response) {
                      if (response) {
                        swal.disableButtons();

                        sb.data.db.obj.erase(
                            actionItem.object_bp_type,
                            actionItem.id,
                            function () {
                              swal.close();

                              // Filter out of current working list
                              actionItems = _.filter(
                                  actionItems,
                                  function (item) {
                                    return item.id !== actionItem.id;
                                  }
                              );

                              viewActionItemsList(setup);
                            }
                        );
                      } else {
                        swal.close();
                      }
                    }
                );
              });
            });
            container.patch();
          }
        },
        create_ui: function (createBtnContainer) {},
        // , emptyMessage: '<h1 style="color:#027eff;">You\'re all caught up!</h1>'
        fields: {
          name: {
            type: "title",
            title: "Name",
            view: function (ui, obj, titleSetup) {

              var opts = {
                edit: false,
                editing: false,
                overallStatus: true,
                inCollection: true,
                onClick: function (obj, selector) {
                  titleSetup.onClick(obj, selector);
                },
              };
              if ( titleSetup.muted ) {
                opts.muted = titleSetup.muted;
              }

              sb.notify(
                  {
                    type: "view-field",
                    data: {
                      type: "title",
                      property: "name",
                      obj: obj,
                      options: opts,
                      ui: ui,
                    },
                  },
                  sb.moduleId
              );
            },
          },
          ["any-state"]: {
            type: "toggle",
            title: "Status",
            options: {
              overall_status: true,
              edit: false,
            },
            view: function (ui, obj) {
              var txt = "";
              switch (obj.status) {
                case "done":
                  txt = '<i class="large square check icon bento-checked"></i>';
                  break;

                default:
                  txt =
                      '<i class="large square outline icon bento-unchecked"></i>';
                  break;
              }

              sb.notify(
                  {
                    type: "view-field",
                    data: {
                      type: "toggle",
                      property: "any-state",
                      obj: {
                        id: obj.id,
                        status: obj.status,
                      },
                      options: {
                        edit: false,
                        editing: false,
                        overallStatus: true,
                        inCollection: true,
                        is_portal: true
                      },
                      ui: ui,
                    },
                  },
                  sb.moduleId
              );
            },
          },
        },
        onNextItemTransition: function (obj) {
          if (obj.status === "done") {
            _.findWhere(actionItems, { id: obj.id }).status = "done";
          }

          var actionItemsAreDone = true;
          _.each(actionItems, function (actionItem) {
            if (actionItem.status !== "done") {
              actionItemsAreDone = false;
            }
          });

          if (actionItemsAreDone) {
            Factory.triggerEvent({
              type: "display-alert",
              data: {
                header: "You're all set!",
                body: "We'll keep you posted as we process this information.",
                color: "green",
              },
            });
            return false;
          }

          return true;
        },
        updateSelection: function () {},
        singleView: function (entity, dom, viewOptions) {
          dom.show();
          dom.body.makeNode("menu", "div", {
            css: "ui secondary right floated menu",
            style: "margin:0 !important;",
          });

          var closeLinkSetup = {
            css: "circular icon button item",
            text: '<i class="close icon"></i>',
            tag: "a",
          };

          dom.body.menu.makeNode("close", "div", closeLinkSetup).notify(
              "click",
              {
                type: "collections-run",
                data: {
                  run: function (item) {
                    viewOptions.close(item);
                  }.bind({}, entity),
                },
              },
              sb.moduleId
          );

          dom.body.makeNode("content", "div", {});
          dom.patch();

          sb.data.db.obj.getById(
              entity.object_bp_type,
              entity.id,
              function (entity) {
                sb.notify(
                    {
                      type: "view-entity",
                      data: {
                        id: entity.id,
                        ui: dom.body.content,
                        state: {
                          toNextInList: viewOptions.toNextInList,
                          isLastItem: viewOptions.isLastItem,
                          inCollections: true,
                        },
                        options: {
                          // hideComments:false
                          toNextInList: viewOptions.toNextInList,
                          viewStyle: "form",
                          hideTags: true,
                          editBlueprint: false,
                        },
                        onComplete: function () {},
                      },
                    },
                    sb.moduleId
                );
              }
          );
        },
        subviews: {
          list: {
            backlog: true,
          },
        },
        showUser: false,
        viewStyle: "form",
      },
    };

    actionItems = _.filter(actionItems, function (actionItem) {
      if (
          _.findWhere(appConfig.Types, {
            bp_name: actionItem.object_bp_type.substr(1),
          })
      ) {
        return _.findWhere(appConfig.Types, {
          bp_name: actionItem.object_bp_type.substr(1),
        }).is_task;
      }
      return false;
    });

    function prepList(actionItems, setup) {
      // Sort alphabetically by name
      var list = _.sortBy(setup.list, "name");

      setup.list = list;

      return setup.list;
    }

    function viewActionItemsList(setup) {
      _.each(setup.list, function (actionItem) {
        if (actionItem.status !== "done") {
          actionItemsAreDone = false;
        }
      });

      setup.list = prepList(setup.list, setup);

      sb.notify(
          {
            type: "field-list-view",
            data: setup,
          },
          sb.moduleId
      );

      ui.patch();
    }

    ui.empty();
    ui.patch();

    // Intake Forms
    var intakeQuestionnaireTagId = 4759706;

    var intakeForms = _.filter(actionItems, function (actionItem) {
      var bp = _.findWhere(appConfig.Types, {
        bp_name: actionItem.object_bp_type.substr(1),
      });
      if (
          Array.isArray(bp.tagged_with) &&
          bp.tagged_with.includes(intakeQuestionnaireTagId)
      ) {
        return true;
      }

      return false;
    });
    if (intakeForms.length > 0) {
      ui.makeNode("intakeForms-h", "div", {
        tag: "h3",
        text: "Intake Forms",
        css: "ui header",
        style: "padding-left:1rem;margin-bottom:0px;",
      });

      setup.container = ui.makeNode("intakeForms", "div", {});
      setup.list = intakeForms;

      viewActionItemsList(setup);
    }

    // Info Request/Review follow-up items
    var followUpActionItemTag = 4760308;
    var followUpSetup = _.clone(setup);
    var followUpItems = _.filter(actionItems, function (actionItem) {
      var bp = _.findWhere(appConfig.Types, {
        bp_name: actionItem.object_bp_type.substr(1),
      });

      if (
          Array.isArray(bp.tagged_with) &&
          bp.tagged_with.includes(followUpActionItemTag)
      ) {
        return true;
      }

      return false;
    });

    if (followUpItems.length > 0) {
      ui.makeNode("followUp-h", "div", {
        tag: "h3",
        text: "Action Items",
        css: "ui header",
        style: "padding-left:1rem;margin-bottom:0px;",
      });
      followUpSetup.container = ui.makeNode("followUp", "div", {});
      followUpSetup.list = followUpItems;
      viewActionItemsList(followUpSetup);
    }

    var allCaughtUp = false;

    if (intakeForms.length == 0 && followUpItems.length == 0)
      allCaughtUp = true;

    if (allCaughtUp) {
      ui.makeNode("caughtup", "div", {
        style: "margin:50px 0px 150px 0px; padding-left:1rem;",
        // , tag:'h3'
        // , css:'ui header'
        text: "All caught up on your action items.",
      });

      ///comments View
      ui.makeNode("comments-h", "div", {
        tag: "h3",
        text: "Have a question?",
        css: "ui header",
        style: "padding-left:1rem;margin-bottom:0px; padding-bottom:1rem;",
      });

      var commentsSetup = _.clone(setup);
      commentsSetup.container = ui.makeNode("comments", "div", {});

      var noteListConfig = {
        domObj: commentsSetup.container,
        objectIds: [data.project.id],
        objectId: data.project.id,
        collapse: "open",
        categories: false,
        actions: {
          edit: false,
          delete: false,
        },
        ///portal note type
        defaultNoteType: 5133499,
        portalOptions: {
          is_public: true,
          filters: false,
          search: false,
          hideDateRange: true,
        },
      };

      sb.notify({
        type: "show-note-list-box",
        data: noteListConfig,
      });
    }
  }

  function displayCharsolYearly(ui, options, data) {
    ui.makeNode("listCont", "div", { css: "ui middle aligned list" });

    var yearsList = ["2022", "2021", "2020", "2019", "2018", "2017"];

    _.each(
        yearsList,
        function (year, i) {
          var ui = this;
          var itemOptions = { tag: "a", css: "item", style: "padding:21px;" };

          if (i % 2 != 0) {
            itemOptions = {
              css: "item",
              style: "padding:21px; background-color: #f6f6f6",
            };
          }

          var item = ui.makeNode("item" + i, "div", itemOptions);

          item.makeNode("content", "div", { css: "left floated content" });
          item.content.makeNode("header", "div", { text: year, tag: "h3" });

          item.notify("click", {
            type: "fgportals-run",
            data: {
              run: function (data) {
                var headerTextName = "Charitable Solicitations";

                if (data.hasOwnProperty("#HDIjlE")) {
                  headerTextName = data["#HDIjlE"][0].name;
                } else if (data.hasOwnProperty("services")) {
                  headerTextName = data.services[0].name;
                }

                var linkItemOptions = {
                  header: {
                    setup: {
                      refresh: true,
                      title: {
                        text:
                            '<i class="grey file icon"></i>' +
                            '<div class="content">' +
                            "" +
                            headerTextName +
                            "" +
                            '<div class="sub header">TN</div>' +
                            "</div>",
                      },
                    },
                    display: {
                      title: true,
                    },
                  },
                  cont: {
                    setup: {
                      refresh: true,
                      loading: true,
                    },
                    display: {
                      view: "singleServiceView",
                    },
                  },
                };

                pageUI.header(linkItemOptions);
                pageUI.cont(linkItemOptions, data);
              }.bind(null, data),
            },
          });

          // item.makeNode('title', 'div', {
          //     style: 	'cursor:pointer;'
          //     , css: 	'ui medium header'
          //     , text:	serv.name
          //     , tag: 	'a'
          //     , href: sb.data.url.getObjectPageParams(serv.services[0])
          // });

          ui.patch();
        },
        ui.listCont
    );

    ui.patch();
  }

  function displaySingleServiceView(ui, options, data) {
    var actionItems = [];
    var deliverableDownloads = [];

    if (data.hasOwnProperty("##Action Items"))
      actionItems = data["##Action Items"];

    actionItems = _.filter(actionItems, function (actionItem) {
      if (
          _.findWhere(appConfig.Types, {
            bp_name: actionItem.object_bp_type.substr(1),
          })
      ) {
        return _.findWhere(appConfig.Types, {
          bp_name: actionItem.object_bp_type.substr(1),
        }).is_task;
      }
      return false;
    });

    if (data.id) {
      deliverableDownloads.push("Tennessee (D)");
      deliverableDownloads.push("Final Document to Upload");
      deliverableDownloads.push("Deliverable Items to Download");
    }

    ui.empty();

    ui.makeNode("grid", "div", { css: "ui two column grid" });

    ui.grid.makeNode("lcol", "div", {
      css: "ui column",
      style: "min-height: 300px;",
    });
    ui.grid.lcol.makeNode("lheader", "div", {
      css: "ui header",
      text: "Action Items:",
    });
    ui.grid.lcol.makeNode("divider", "div", { css: "ui dividing header" });
    ui.grid.lcol.makeNode("cont", "div", { css: "ui left aligned list" });
    ui.grid.makeNode("rcol", "div", {
      css: "ui column",
      style: "min-height:300px;",
    });
    ui.grid.rcol.makeNode("rheader", "div", {
      css: "ui header",
      text: "Downloads:",
    });
    ui.grid.rcol.makeNode("divider", "div", { css: "ui dividing header" });
    ui.grid.rcol.makeNode("cont", "div", { css: "ui left aligned list" });
    ui.patch();

    if (actionItems.length > 0) {
      /*
            <h2 class="ui header">
            <i class="plug icon"></i>
            <div class="content">
                Uptime Guarantee
            </div>
            </h2>*/

      _.each(
          actionItems,
          function (act, i) {
            var linkSetup = {
              css: "item",
              tag: "a",
              href: sb.data.url.getObjectPageParams(act, {}),
            };
            var ui = this;
            ui.makeNode("title-" + i, "div", linkSetup);
            ui["title-" + i].makeNode("header", "div", {
              text: act.name,
              tag: "h3",
            });
          },
          ui.grid.lcol.cont
      );
    } else {
      ui.grid.lcol.cont.makeNode("caughtup", "div", {
        text: "No action items available",
      });
    }

    if (deliverableDownloads.length > 0) {
      /*
            <h2 class="ui header">
            <i class="plug icon"></i>
            <div class="content">
                Uptime Guarantee
            </div>
            </h2>*/

      _.each(
          deliverableDownloads,
          function (dow, i) {
            var linkSetup = {
              css: "item",
              tag: "a",
              href: sb.data.url.getObjectPageParams(dow, {}),
            };
            var ui = this;
            ui.makeNode("title-" + i, "div", {});
            ui["title-" + i].makeNode("header", "div", {
              text: "• " + dow,
              tag: "h3",
            });
          },
          ui.grid.rcol.cont
      );
    } else {
      ui.grid.rcol.cont.makeNode("caughtup", "div", {
        text: "No downloads available",
      });
    }

    ui.patch();
  }

  function displayHome(ui, options, data) {

    var listco = ui.makeNode("listContainer", "div", { css: "ui middle aligned divided list" });
    ui.patch();

    if (!_.isEmpty(data)) {

        var index = 0;

        var statusOrders = {
            'n/a': {
              name: 'N/A'
              , color: 'grey'
            }
            , open: {
              name: 'Open'
              , color: 'black'
            }
            , not_started: {
              name: 'Not Started'
              , color: 'teal'
            }
            , preparing: {
              name: 'Preparing'
              , color: 'yellow'
            }
            , informationReceived: {
              name: 'Information Received'
              , color: 'blue'
            }
            , finalReview: {
              name: 'Final Review'
              , color: 'orange'
            }
            , deliveryPending: {
              name: 'Delivery Pending'
              , color: 'brown'
            }
            , pending: {
              name: 'Pending'
              , color: 'purple'
            }
            , onHold: {
              name: 'On Hold'
              , color: 'orange'
            }
            , actionNeeded: {
              name: 'Action Needed'
              , color: 'red'
            }
            , done: {
              name: 'Complete'
              , color: 'green'
            }
        };

        _.each(data, function(list, yearGroup, l){

            var itemui = this;

            var pad = (index == 0) ? '' : 'padding-top: 10px';

            itemui.makeNode('item-'+ yearGroup, 'div', {css: 'item', style: 'cursor:pointer; padding-top: 20px;'});
            itemui['item-'+ yearGroup].makeNode('hd', 'div', {tag: 'span', css: 'ui medium text', text: yearGroup, style: pad});
            itemui['item-'+ yearGroup].makeNode('list', 'div', {css: 'list', style: ''});

            _.each(list, function(proj, i){
                var itemOptions = {
                    css: "item",
                    style: "border-radius: 10px; padding: 20px 10px 25px 15px; margin: 10px 0px 20px 0px; background-color: #f6f6f6",
                };

                var dueDateText = "";
                var displayColor = "red";
                var displayText = "Action Needed";

                var charsolRenewalException;

                if ( 
                    proj.type.id == 2318047 
                    && proj.status == 'done' 
                ){
                    charsolRenewalException = _.find(proj['##Action Items'], function(ac){
                        return ac.status != 'done';
                    });
                }

                if ( statusOrders[proj.status] ) {
                    displayColor =  statusOrders[proj.status].color;
                    displayText = statusOrders[proj.status].name;
                }
                if ( charsolRenewalException ) {
                    displayColor = "red";
                    displayText = "Action Needed";
                }
                var btnOptions = {
                    css: "ui small " + displayColor + " label",
                    text: displayText,
                };

                var titleText = formatTitle(proj, appConfig.Types);

                function dueDateSet(clientService) {
                    var isSet = false;
                    if (!_.isEmpty(clientService) && !_.isEmpty(clientService["_9"])) {
                        isSet = true;
                    }
                    return isSet;
                }

                function formatTitle(proj, types) {
                    var title = proj.name;

                    if (proj["#HDIjlE"] && proj["#HDIjlE"][0]) {
                        var bpName = proj["#HDIjlE"][0].object_bp_type.replace("#", "");
                        var type = _.findWhere(types, { bp_name: bpName });
                        if (type) {
                        title = type.name;
                        }
                    }

                    return title;
                }

                if (dueDateSet(proj["#HDIjlE"][0])) {
                    dueDateText = moment(proj["#HDIjlE"][0]["_9"]).format("MM/DD/YY");
                }

                if (i % 2 != 0) {
                    itemOptions = { css: "item", style: "padding: 20px 10px 25px 15px; margin: 10px 0px 20px 0px;" };
                }

                var item = this.makeNode("item" + i, "div", itemOptions);

                item.makeNode("content", "div", { css: "right floated content" });
                item.content.makeNode("date", "div", {
                    style: "",
                    css: "ui medium " + displayColor + " text",
                    text: dueDateText,
                });
                item.content.makeNode("btn", "div", btnOptions);

                item.makeNode('icon', 'div', {css: 'ui large folder icon', tag: 'i'});
                item.makeNode('con', 'div', {css: 'content'});
                item.con.makeNode("title", "div", {
                    tag: 'span',
                    css: "ui medium text",
                    text: titleText,
                });

                item.notify("click", {
                    type: "fgportals-run",
                    data: {
                        run: function (data) {
                            console.log('clicked!', data);
                            window.location.href = sb.data.url.createPageURL("object", {
                                id: data.id,
                                name: data.name,
                                type: "actionitems",
                            });
                        }.bind(null, proj),
                    },
                });

                ///type charsol projects
                if (
                    proj.type == 2051672 ||
                    (proj.type == 2318047 && proj.hasOwnProperty("#Deliverables"))
                ) {
                    if (proj["#Deliverables"].length > 0) {
                        _.each(
                            proj["#Deliverables"],
                            function (del, j) {

                                if (del["_15"]) {
                                    var nestOptions = {
                                        css: "item",
                                        style: "padding:22px 0px 21px 45px;",
                                    };
    
                                    if (j == 0) {
                                        nestOptions = {
                                            css: "item",
                                            style: "padding:42px 0px 21px 45px;",
                                        };
                                    }
    
                                    var deliver = this.makeNode("item" + j, "div", nestOptions);
    
                                    var btn = {
                                        css: "ui disabled button",
                                        text: "Due:  " + moment(del["_15"]).format("MM/DD"),
                                        style: "border-radius:25px;",
                                    };
    
                                    deliver.makeNode("content", "div", {
                                        css: "right floated content",
                                    });

                                    deliver.content.makeNode("date", "div", {
                                        style: "display:inline-block; margin: 0px 21px 0px 0px;",
                                        css: "ui medium header",
                                        text: "",
                                    });
                                    deliver.content.makeNode("btn", "div", btn);
    
                                    deliver.makeNode("title", "div", {
                                        css: "ui small text",
                                        text: del.name,
                                    });
                                }
                            },
                            item
                        );
                    }
                }

                i++;

            }, itemui['item-'+ yearGroup].list);

            index++;

        }, listco);
    
    } else {
      ui.listContainer.makeNode("caughtup", "div", {
        style: "margin-top:50px; padding-left:1rem;",
        tag: "h3",
        css: "ui header",
        text: "All caught up on your action items.",
      });
    }

    ui.patch();
  }

  function displayCurrent(ui, options, data) {
    var statusOrders = {
      "n/a": {
        name: "N/A",
        color: "grey",
      },
      open: {
        name: "Not Started",
        color: "grey",
      },
      not_started: {
        name: "Not Started",
        color: "grey",
      },
      preparing: {
        name: "Preparing",
        color: "yellow",
      },
      informationReceived: {
        name: "Information Received",
        color: "blue",
      },
      finalReview: {
        name: "Final Review",
        color: "orange",
      },
      deliveryPending: {
        name: "Delivery Pending",
        color: "purple",
      },
      pending: {
        name: "Pending",
        color: "purple",
      },
      onHold: {
        name: "On Hold",
        color: "orange",
      },
      actionNeeded: {
        name: "Action Needed",
        color: "red",
      },
      done: {
        name: "Complete",
        color: "green",
      },
    };
    var backOptions = {
      ui: ui
      , options: options
      , data: data
    };

    function boardMemberView(ui, board, opts){

      ui.empty();

      ui.makeNode('back', 'div', {css: 'ui left floated button', text: 'Back', style: 'padding-left: 22px; border-radius:25px;'});
      ui.back.notify("click", {
        type: "fgportals-run",
        data: {
          run: function () {

            displayCurrent(opts.ui, opts.options, opts.data);
          }
        },
      });
      ui.makeNode('cont', 'div', {});
      ui.patch();

      sb.notify({
        type: 'view-entity'
        , data: {
          ui: ui.cont
          , id: board.id
          , hideTags: true
        }
      });

    }

    ui.empty();

    ui.makeNode('board', 'div', {
      css: 'ui right floated blue button'
      , tag: 'button'
      , text: 'Board Members'
      , style: 'margin-right: 22px; border-radius:25px;'
      , id:'boardb'});

    ui.board.notify("click", {
      type: "fgportals-run",
      data: {
        run: function (selectedYear, opts) {

          var container = this;

          $('#boardb').addClass('loading');

          sb.data.db.service(
              "FGPortalService"
              , 'getGovernanceStucture'
              , {
                selected_year: selectedYear
                , company: appConfig.portal_company
              }
              , function(res){

                boardMemberView( container, res , opts);
              }
          );

        }.bind(ui, options.year, backOptions),
      },
    });

    // ui.makeNode('files', 'div', {css: 'ui right floated blue button', text: 'Shared Files', style: 'margin-left: 22px; border-radius:25px;'});
    ui.makeNode("brk", "lineBreak", { spaces: 1 , style: 'margin-bottom:40px;'});

    ui.makeNode("listCont", "div", { css: "ui middle aligned list" });

    if (data.projects.length > 0) {
      _.each(
          data.projects,
          function (proj, i) {
            var itemOptions = {
              css: "item",
              style: "padding:21px; background-color: #f6f6f6",
            };
            var dueDateText = "";
            var displayColor = "grey";
            var displayText = 'Open';
            if ( statusOrders[proj.status] ) {
              displayText = statusOrders[proj.status].name;
              displayColor =  statusOrders[proj.status].color;
            }
            var btnOptions = {
              css: "ui " + displayColor + " button",
              text: displayText,
              style: "border-radius:25px;",
            };

            var titleText = formatTitle(proj, data.serviceTypes);
            var countTotal = formatCount(proj["#Action Items"]);

            function dueDateSet(clientService) {
              var isSet = false;
              if (!_.isEmpty(clientService) && !_.isEmpty(clientService["_9"])) {
                isSet = true;
              }
              return isSet;
            }

            function formatTitle(proj, types) {
              var title = proj.name;

              if (proj["#HDIjlE"] && proj["#HDIjlE"][0]) {
                var bpName = proj["#HDIjlE"][0].object_bp_type.replace("#", "");
                var type = _.findWhere(types, { bp_name: bpName });
                if (type) {
                  title = type.name;
                }
              }

              return title;
            }

            function formatCount(actionItems) {
              actionItems = _.filter(actionItems, function (actionItem) {
                if (
                    _.findWhere(appConfig.Types, {
                      bp_name: actionItem.object_bp_type.substr(1),
                    })
                ) {
                  return _.findWhere(appConfig.Types, {
                    bp_name: actionItem.object_bp_type.substr(1),
                  }).is_task;
                }
                return false;
              });

              var countText = "";
              var totalCount = actionItems.length;
              var numberCompleted = 0;

              _.map(actionItems, function (item) {
                if (item.status == "done") numberCompleted++;
              });

              if (totalCount != numberCompleted)
                countText = ` (${numberCompleted} Completed / ${totalCount} Total)`;

              return countText;
            }

            if (dueDateSet(proj["#HDIjlE"][0])) {
              dueDateText = moment(proj["#HDIjlE"][0]["_9"]).format("MM/DD/YY");
            }

            if (i % 2 != 0) {
              itemOptions = { css: "item", style: "padding:21px;" };
            }

            var item = this.makeNode("item" + i, "div", itemOptions);

            item.makeNode("content", "div", { css: "right floated content" });
            item.content.makeNode("date", "div", {
              style: "display:inline-block; margin: 0px 21px 0px 0px;",
              css: "ui medium " + displayColor + " header",
              text: dueDateText,
            });
            item.content.makeNode("btn", "div", btnOptions);

            item.makeNode("title", "div", {
              style: "cursor:pointer;",
              css: "ui medium header",
              text:
                  titleText +
                  '<span class="ui grey small text">' +
                  countTotal +
                  "</span>",
            });

            item.notify("click", {
              type: "fgportals-run",
              data: {
                run: function (data) {
                  window.location.href = sb.data.url.createPageURL("object", {
                    id: data.id,
                    name: data.name,
                    type: "actionitems",
                  });
                }.bind(null, proj),
              },
            });

            ///type charsol projects
            if (
                proj.type == 2051672 ||
                (proj.type == 2318047 && proj.hasOwnProperty("#Deliverables"))
            ) {
              if (proj["#Deliverables"].length > 0) {
                _.each(
                    proj["#Deliverables"],
                    function (del, j) {
                      if (del["_15"]) {
                        var nestOptions = {
                          css: "item",
                          style: "padding:22px 0px 21px 45px;",
                        };

                        if (j == 0) {
                          nestOptions = {
                            css: "item",
                            style: "padding:42px 0px 21px 45px;",
                          };
                        }

                        var deliver = this.makeNode("item" + j, "div", nestOptions);

                        var btn = {
                          css: "ui disabled button",
                          text: "Due:  " + moment(del["_15"]).format("MM/DD"),
                          style: "border-radius:25px;",
                        };

                        deliver.makeNode("content", "div", {
                          css: "right floated content",
                        });
                        deliver.content.makeNode("date", "div", {
                          style: "display:inline-block; margin: 0px 21px 0px 0px;",
                          css: "ui medium header",
                          text: "",
                        });
                        deliver.content.makeNode("btn", "div", btn);

                        deliver.makeNode("title", "div", {
                          css: "ui small header",
                          text: del.name,
                        });
                      }
                    },
                    item
                );
              }
            }
          },
          ui.listCont
      );
    } else {
      msgTxt = options.year
          ? "No active or ongoing services for " + options.year
          : "No active or ongoing services";

      ui.listCont.makeNode("caughtup", "div", {
        style: "margin:50px 0px 150px 0px; padding-left:1rem;",
        text: msgTxt,
      });
    }

    ui.patch();
  }

  function displayHistory(ui, options, data) {

    var options = options || {};
    var displayHistoryBack = {
      ui: ui,
      options: options,
      data: data,
    };
    var listco = ui.makeNode("listContainer", "div", { css: "ui middle aligned divided list" });

    function listHistoryItems(cont, options, list) {

        var csHistory = list;

        function formatTitle(proj, types) {
            var title = proj.name;

            if (proj["#HDIjlE"] && proj["#HDIjlE"][0]) {
                var bpName = proj["#HDIjlE"][0].object_bp_type.replace("#", "");
                var type = _.findWhere(types, { bp_name: bpName });
                if (type) {
                title = type.name;
                }
            }

            return title;
        }

        cont.empty();

        var index = 0;

        _.each(csHistory, function(list, yearGroup, l){

            var itemui = this;

            var pad = (index == 0) ? '' : 'padding-top: 10px';

            itemui.makeNode('item-'+ yearGroup, 'div', {css: 'item', style: 'padding-top: 20px;'});
            itemui['item-'+ yearGroup].makeNode('hd', 'div', {tag: 'span', css: 'ui medium text', text: yearGroup, style: pad});
            itemui['item-'+ yearGroup].makeNode('list', 'div', {css: 'list', style: ''});

            _.each(list, function(proj, i){

                var itemOptions = {
                    css: "item",
                    style: "padding: 0px 0px 25px 0px; margin: 10px 0px 20px 0px;",
                };

                var titleText = formatTitle(proj, appConfig.Types);
                var item = this.makeNode("item-" + i, "div", itemOptions);

                if (i % 2 != 0) {
                    itemOptions = { css: "item", style: "padding: 0px 0px 25px 0px; margin: 10px 0px 20px 0px;" };
                }

                item.makeNode("title", "div", {
                    tag: 'span',
                    css: "ui small header",
                    text: titleText,
                });
                item.makeNode('list', 'div', {css: 'list', style: ''});

                if ( proj.hasOwnProperty('#Deliverables') ) {
                      
                    _.each( proj["#Deliverables"], function(del, j){

                        console.log('::deliverable', j, del);
                        var file = del['_2'][0];
                        var fileAttached = !_.isEmpty(file);

                        var itemOptions = {
                            css: "item",
                            style: "margin-bottom: 10px; padding: 15px 15px 15px 10px; border-radius: 10px; background-color: #f6f6f6;",
                        };

                        if (j % 2 != 0) {
                            itemOptions = { css: "item", style: "margin-bottom: 10px; padding: 15px 15px 15px 10px; border-radius: 10px;" };
                        }
        
                        var titleOptions = {
                            tag: 'span',
                            css: "text-muted ui medium text",
                            text: del.name,
                        };
                        var iconOptions = {css: 'ui large circle outline yellow icon', tag: 'i'};

                        if ( fileAttached ){
                            
                            iconOptions.css = 'ui large check circle green icon';
                            titleOptions.css = 'ui medium text';

                        } 
                        
                        var item = this.makeNode("item" + j, "div", itemOptions);

                        item.makeNode("content", "div", { css: "right floated content" });

                        if ( fileAttached ) {

                            item.content.makeNode('btn', 'div', {
                                text: '<a class="ui green label" target="_blank" href="https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/' +
                                    file.instance +
                                    "/" +
                                    file.loc +
                                    '">' +
                                    '<i class="ui download icon"></i> Click to Download' +
                                    "</a>"
                            });

                        }

                        item.makeNode('icon', 'div', iconOptions);
                        item.makeNode('con', 'div', {css: 'content'});
                        item.con.makeNode("title", "div", titleOptions);
        
                    }, item.list);

                } else {
        
                    item.list.makeNode("emptmsg", "div", {
                        css:'text-muted',
                        text: "Awaiting file attachment to be added to this Deliverable item.",
                      });
    
                }

            }, itemui['item-'+ yearGroup].list);

            index++;

        }, cont);

        return cont.patch();

    }

    ui.makeNode("listCont", "div", { css: "ui middle aligned list" });

    if (!_.isEmpty(data)) {

      listHistoryItems(listco, options, data);

    } else {

      msgTxt = options.year
          ? "No services available for download for " + options.year
          : "No downloads available";

      ui.listCont.makeNode("caughtup", "div", {
        style: "margin:50px 0px 150px 0px; padding-left:1rem;",
        text: msgTxt,
      });

    }

    ui.patch();
    return;

  }

  function displayMembership(ui, options, data) {

    ///setup
    var portraitWidth = "830px";
    var landscapeWidth = "1253px";
    var config = {};

    ui.empty();

    function renderComments(ui, data){

      var commentsOrientationStyle =
          "landscape"
              ? "max-width:" + landscapeWidth
              : "max-width:" + portraitWidth;


      //container
      ui.makeNode("comments-h", "div", {
        tag: "h3",
        text: "Have a question?",
        css: "ui header",
        style: "padding-left:1rem;margin-bottom:0px; padding-bottom:1rem;",
      });

      //comment container
      ui.makeNode("comments", "div", {
        id: "bentoDocumentComments",
        style:
            "margin:0 auto; padding-top:60px;" + commentsOrientationStyle,
      });

      //comment rendered
      sb.notify({
        type: "show-note-list-box",
        data: {
          domObj: ui.comments,
          objectIds: [data.membershipRecord.id],
          objectId: data.membershipRecord.id,
          collapse: "open",
          categories: false,
          actions: {
            edit: false,
            delete: false,
          },
          ///portal note type
          defaultNoteType: 5133499,
          portalOptions: {
            is_public: true,
            filters: false,
            search: false,
            hideDateRange: true,
          },
        },
      });

      ui.patch();

    }

    function displayMembershipMessage(ui, config){

      ui.makeNode('mseg', 'div', {css: 'ui basic segment', style: 'margin-top: 50px;'});
      ui.mseg.makeNode('mess', 'div', {css: config.css });
      ui.mseg.mess.makeNode('icon', 'div', {tag: 'i', css: config.icon});
      ui.mseg.mess.makeNode('content', 'div', {
        css: 'content'
        , text: '<div class="header">'+
            config.messageHeader +
            '</div>'+
            '<p>'+ config.messageText +'</p>'
      });

      ui.patch();

    }

    function renderNoEngagment( ui ){

      var config = {
        icon:'landmark icon'
        , css:'ui icon info message'
        , messageHeader:'Unlock your FG 360 Membership today'
        , messageText:'You will receive exclusive access and benefits to help you make better decisions, reduce risk, and grow your organization.'
      };

      displayMembershipMessage(ui, config);

    }

    function renderInactiveOrExpired( ui, data ){

      var config = {
        icon:'ban icon'
        , css:'ui icon negative message'
        , messageHeader:'FG360 Membership has expired.'
        , messageText:'You have chosen not to renew your FG360 Membership. Should you decide to re-instate your membership, please contact FG Sales at (615) 361-9445'
      };

      displayMembershipMessage(ui, config);

    }

    function renderActive( ui, data){

      var config = {
        icon:'check circle icon'
        , css:'ui icon positive floating message'
        , messageHeader:'FG360 Membership is active through - ' + moment(data.membershipRecord['_16']).format('MM/DD/YY')
        , messageText:'Please contact your CSM or message us below if you have any questions regarding your FG360 membership.'
      };

      displayMembershipMessage(ui, config);

      displayAttachmentButton(ui, data.membershipRecord, config);

      ui.makeNode('helpSection', 'div', {});

      displayHelp(ui.helpSection, {membership: true}, data);

      renderComments(ui, data);

    }

    if ( data.membershipProject === false && data.membershipRecord === false ) {

      renderNoEngagment(ui);

    } else if ( data.membershipProject && data.membershipRecord === false ) {

      renderInactiveOrExpired(ui, data);

    } else if ( data.membershipProject && data.membershipRecord ) {

      renderActive(ui, data);
    }

  }

  function displayAttachmentButton(ui, entity, options){

    ui.makeNode("attachments", "div", { css: "ui basic segment" });

    sb.data.db.obj.getBlueprint('#0ZZQgI', function(bp){

      bp['_21'].options.custom = true;
      bp['_21'].options.edit = true;

      if(bp['_21'].options.onlyDownload) {
        bp['_21'].options.context = entity;
        bp['_21'].options.edit = false;
      }

      sb.notify({
        type: 'view-field',
        data: {
          fieldName: 'attachments',
          type: 'attachments',
          property: '_21',
          obj: entity,
          options: bp['_21'].options,
          ui: ui.attachments
        }
      });
    })

  }

  function displayHelp(ui, options, data) {
    var resourceList = [
      {
        title: "Board of Directors",
        url: "https://www.501c3.org/topic/board-of-directors",
      },
      {
        title: "Client Portal Help",
        url: "https://www.501c3.org/topic/client-portal-help",
      },
      {
        title: "Federal Compliance",
        url: "https://www.501c3.org/topic/federal-compliance",
      },
      {
        title: "Funding",
        url: "https://www.501c3.org/topic/funding",
      },
      {
        title: "Glossary",
        url: "https://www.501c3.org/topic/glossary",
      },
      {
        title: "Governance",
        url: "https://www.501c3.org/topic/governance",
      },
      {
        title: "Management Administration",
        url: "https://www.501c3.org/topic/management-administration",
      },
      {
        title: "Non-Profit Formation",
        url: "https://www.501c3.org/topic/nonprofit-formation",
      },
      {
        title: "Non-Profit Types Categories",
        url: "https://www.501c3.org/topic/nonprofit-types-categories",
      },
      {
        title: "Operations",
        url: "https://www.501c3.org/topic/operations",
      },
      {
        title: "State Compliance",
        url: "https://www.501c3.org/topic/state-compliance",
      },
      {
        title: "Sure Start Services Help",
        url: "https://www.501c3.org/topic/surestart-services-help",
      },
    ];
    resourceList = resourceList.sort((a, b) => a.title.localeCompare(b.title));

    ui.empty();

    if ( options.membership ){

      ui.makeNode("memseg", "div", { css: "ui basic segment" });
      ui.memseg.makeNode("know", "div", {
        style: "margin-top:50px;",
        tag: "h3",
        css: "ui header",
        text: "FG 360 Membership",
      });

      ui.memseg.makeNode("cards1", "div", { css: "ui green cards" });
      ui.memseg.cards1
          .makeNode("memonly", "div", {
            tag: "a",
            css: "ui raised card",
            href: "https://www.501c3.org/fg-360-membership/member-resources",
            target:'_blank'
          })
          .makeNode("content", "div", { css: "content" });
      ui.memseg.cards1.memonly.content.makeNode("title", "div", {
        css: "header",
        text: "Members Only Resources",
      });

      
      ui.memseg.cards1
            .makeNode("fbpage", "div", {
                tag: "a",
                css: "ui raised card",
                href: "https://www.facebook.com/groups/fg360",
                target:'_blank'
            })
            .makeNode("content", "div", { css: "content" });
        ui.memseg.cards1.fbpage.content.makeNode("title", "div", {
            css: "header",
            text: "FG 360 Member Group",
        });

    }

    ui.makeNode("seg", "div", { css: "ui basic segment" });
    ui.seg.makeNode("know", "div", {
      style: "margin-top:50px;",
      tag: "h3",
      css: "ui header",
      text: "Knowledge Base and Blog Articles",
    });

    ui.seg.makeNode("cards1", "div", { css: "ui blue cards" });
    ui.seg.cards1
        .makeNode("blog", "div", {
          tag: "a",
          css: "card",
          href: "https://www.501c3.org/blog",
          target:'_blank'
        })
        .makeNode("content", "div", { css: "content" });
    ui.seg.cards1.blog.content.makeNode("title", "div", {
      css: "header",
      text: "Foundation Group Blog",
    });
    ui.seg.cards1
        .makeNode("videos", "div", {
          tag: "a",
          css: "card",
          href: "https://www.501c3.org/topic/videos",
          target:'_blank'
        })
        .makeNode("content", "div", { css: "content" });
    ui.seg.cards1.videos.content.makeNode("title", "div", {
      css: "header",
      text: "Informational Videos",
    });
    ui.seg.makeNode("lb1", "lineBreak", { spaces: 1 });

    ui.seg.makeNode("title2", "div", {
      style: "margin-top:50px;",
      tag: "h3",
      css: "ui header",
      text: "Specific Topics",
    });

    ui.seg.makeNode("cards2", "div", { css: "ui violet cards" });
    ui.patch();

    _.each(
        resourceList,
        function (res, i) {
          this.makeNode("item-" + i, "div", {
            css: "card",
            text:
                '<a class="content" href="' +
                res.url +
                '" target="_blank" rel="noopener noreferrer" >' +
                '<div class="header">' +
                res.title +
                "</div>" +
                "</a>",
          });
        },
        ui.seg.cards2
    );

    ui.seg.makeNode("lb3", "lineBreak", { spaces: 2 });
    ui.patch();
  }

  function getActionItemData(config, callback) {
    var setup = config.setup;
    var options = config.options;

    var serviceBpName = "HDIjlE";
    // var serviceBpName = '1W37Ik';
    var serviceBp = _.findWhere(appConfig.Types, { bp_name: serviceBpName });

    sb.data.db.obj.getById(
        options.project.object_bp_type,
        options.project.id,
        function (resp) {
          var ret = {
            project: resp,
          };

          if (options.parseData && typeof options.parseData == "function") {
            options.parseData(ret, callback);
          } else {
            callback(ret);
          }
        },
        {
          name: true,
          type: "id",
          status: true,
          ["#" + serviceBpName]: true,
          ["##Action Items"]: {
            status: true,
            name: true,
          },
          selectionObj: true,
        }
    );
  }

  function getProjectData(config, callback, refac) {
    var setup = config.setup;
    var options = config.options;
    var queryName = options.queryName;
    // featureFlag = false;
    if (refac) {
      var serviceSetup = {
        company: setup.company,
        parse: options.parse,
      };

      /// get Projects && Action Items && Client Service
      sb.data.db.service("FGPortalService", queryName, serviceSetup, callback);
    } else {
      var serviceBpName = "HDIjlE";
      // var serviceBpName = '1W37Ik';
      var serviceBp = _.findWhere(appConfig.Types, { bp_name: serviceBpName });

      var whereObject = {
        group_type: "Project",
        tagged_with: [setup.company],
        status: {
          type: "not_equal",
          value: "done",
        },
        _dont_force_portal_user_tag: true,
        select: {
          name: true,
          type: "id",
          status: true,
          ["#" + serviceBpName]: true,
          ["#Action Items"]: {
            status: true,
            name: true,
          },
        },
      };

      // Get the projects shared with that user
      sb.data.db.obj.getWhere(
          "groups",
          whereObject,
          function (allProjects) {
            var ret = {
              userSetup: setup,
              projects: allProjects,
              serviceBp: serviceBp,
            };

            if (options.parseData && typeof options.parseData == "function") {
              options.parseData(ret, callback);
            } else {
              callback(ret);
            }
          },
          1
      );
    }
  }

  function getServiceData(config, callback, refac) {
    var setup = config.setup;
    var options = config.options;
    var queryName = options.queryName;

    if (refac) {
      var serviceSetup = {
        company: setup.company,
        parse: options.parse,
      };

      /// get Projects && Action Items && Client Service
      sb.data.db.service("FGPortalService", queryName, serviceSetup, callback);
    } else {
      // var serviceBpName = 'HDIjlE';
      // var serviceBp = _.findWhere(appConfig.Types, {bp_name: serviceBpName});
      // var serviceTypes = _.where(
      //     appConfig.Types
      //     , {
      //         _class: serviceBp.id
      //     }
      // );
      // var statusKey = '_3';
      // var yearKey = '_4';
      // var actionNeededState = 3;
      // var where = {
      //     tagged_with: [setup.company]
      //     ,select: {
      //         name: true
      //         , parent: {
      //             status: true
      //         }
      //         , type: true
      //         , _1: true
      //         , _2: true
      //         , _3: true
      //         , _4: true
      //         , _5: true
      //         , shared_with: true
      //         , tagged_with: true
      //     }
      // };
      // sb.data.db.obj.getWhere(
      //     '#'+ serviceBp.bp_name
      //     , where
      //     , function (services) {
      //         var statusOrders = {
      //             'n/a': 				-1
      //             , open: 				0
      //             , not_started: 		0
      //             , preparing: 			1
      //             , pending: 			2
      //             , informationReceived: 	3
      //             , finalReview:			4
      //             , deliveryPending: 		5
      //             , onHold: 			6
      //             , actionNeeded: 		7
      //             , done: 				8
      //         };
      //         _.each(serviceTypes, function (serviceType) {
      //             // Services
      //             var years = [];
      //             _.chain(services)
      //                 .where({object_bp_type: '#'+ serviceType.bp_name})
      //                  .each(function (service) {
      //                     service.status = 'n/a'
      //                     service.actionNeeded = (service[statusKey] === actionNeededState);
      //                     if (
      //                         !_.isEmpty(service)
      //                         && !_.isEmpty(service.parent)
      //                         && service.parent.object_bp_type === 'groups'
      //                     ) {
      //                         var validStatus = _.contains ( _.keys(statusOrders), service.parent.status);
      //                         var year = service[yearKey];
      //                         if ( service.parent.status && service.parent.status !== null && validStatus ) {
      //                             service.status = service.parent.status;
      //                         } else {
      //                             service.status = 'open'
      //                         }
      //                         if (typeof service[yearKey] === 'number') {
      //                             year = year.toString();
      //                         }
      //                         if (year.length === 4 && service.status !== 'n/a') {
      //                             if (_.isEmpty(_.findWhere(years, {year: year}))) {
      //                                 years.push({
      //                                     name: 		service[yearKey].toString()
      //                                     , id: 		service[yearKey]
      //                                     , year: 	service[yearKey]
      //                                     , services: [service]
      //                                 });
      //                             } else {
      //                                 _.findWhere(years, {year: year})
      //                                     .services.push(service);
      //                             }
      //                         }
      //                     }
      //                 });
      //                 years = _.sortBy(years, function (year) {
      //                     return -year.year;
      //                 });
      //             var serviceTypeStatus = 'done';
      //             var actionNeededForServiceType = false;
      //             // Set status for the year
      //             _.each(years, function (year) {
      //                 year.status = 'done';
      //                 year.category = '';
      //                 if (!_.isEmpty(year.services)) {
      //                     var yearStatus = 'done';
      //                     _.each(year.services, function (service) {
      //                         switch( service.object_bp_type ){
      //                             case "#HDIjlE.wxdJhD":
      //                                 year.category = 'Formation 1023';
      //                                 break;
      //                             case "#HDIjlE.sgcFW1":
      //                                 year.category = 'Formation 1023 EZ';
      //                                 break;
      //                             case "#HDIjlE.6LqxD5":
      //                                 year.category = 'Formation 1024';
      //                                 break;
      //                             case "#HDIjlE.TgK9yi":
      //                                 year.category = 'Formation 1024 A';
      //                                 break;
      //                             case "#HDIjlE.xPc4IL":
      //                                 year.category = '990 PF'
      //                                 break;
      //                             case "#HDIjlE.hp8d0g":
      //                                 year.category = '990 N';
      //                                 break;
      //                             case "#HDIjlE.1PBp0q":
      //                                 year.category = '990 EZ';
      //                                 break;
      //                             case "#HDIjlE.b6MdDR":
      //                                 year.category = '990';
      //                                 break;
      //                             case "#HDIjlE.oKxt3d":
      //                                 year.category = 'Charitable Solicitations - Initial';
      //                                 break;
      //                             case "#HDIjlE.lXjaM4":
      //                                 year.category = 'Charitable Solicitations - Renewal';
      //                                 break;
      //                             case "#HDIjlE.YRhgx6":
      //                                 year.category = 'Corporate Tax Exemption';
      //                                 break;
      //                             case "#HDIjlE.n1WVm5":
      //                                 year.category = 'Abatement';
      //                                 break;
      //                         }
      //                         // If the service needs more work than the rest in
      //                         // its year so far, display that status for the year
      //                         if (statusOrders[service.status] < statusOrders[yearStatus]) {
      //                             yearStatus = service.status;
      //                             if (statusOrders[service.status] < statusOrders[serviceTypeStatus]) {
      //                                 serviceTypeStatus = service.status;
      //                             }
      //                         }
      //                     });
      //                     year.status = yearStatus;
      //                 }
      //             });
      //             serviceType.years = years;
      //             serviceType.status = serviceTypeStatus;
      //         });
      //         // Only show Service types w/items within them.
      //         serviceTypes = _.filter(
      //             serviceTypes
      //             , function (serviceType) {
      //                 return !_.isEmpty(serviceType.years);
      //             }
      //         );
      //         var ret = {
      //             userSetup: 			setup
      //             , services:			_.sortBy(services, '_4').reverse()
      //             , serviceTypes: 	serviceTypes
      //             , serviceBp: 		serviceBp
      //         };
      //         if ( options.parseData && typeof options.parseData == 'function'){
      //             options.parseData(ret, callback);
      //         } else {
      //             callback(ret);
      //         }
      //     })
    }
  }

  function getUserData(appConfig, options) {
    var user = {
      company: false,
      contact: false,
      currentUser: false,
    };

    if (appConfig.hasOwnProperty("portal_company"))
      user.company = appConfig.portal_company;

    if (appConfig.hasOwnProperty("portal_contact"))
      user.contact = appConfig.portal_contact;

    if (options) {
      _.mapObject(options, function (v, k) {
        user[k] = v;
      });
    }

    return user;
  }

  function pageUI(ui, config, data, callback) {
    var setup = config.setup;
    var display = config.display;

    var Body = {};

    function pageAnnouncement(config) {
      // var ui = this;
      // var headerText = 'Foundation Group will be closed Friday (04/02)';
      // var paragraphText = 'in observance of Good Friday.';
      // ui.empty();
      // if ( config ){
      //     var setup = config.announcement.setup;
      //     var options = config.announcement.display;
      //     ui.makeNode('message', 'div', {css: 'ui center aligned warning message'});
      //     ui.message.makeNode('content', 'div', {css:'content'});
      //     if ( config && config.hasOwnProperty('announcement') ) {
      //         ui.message.content.makeNode('header', 'div', {css:'header', text: headerText});
      //         ui.message.content.makeNode('para', 'div', {tag:'p', text: paragraphText});
      //     }
      //     return ui.patch();
      // }
    }

    function pageHeader(config) {
      var ui = this;
      var setup = config.header.setup;
      var options = config.header.display;

      var headerLinkOptions = {
        id: "fgPHistory",
        name: "History",
        linkText: "See History",
        startAt: appConfig.breadcrumbs[0].url,
      };

      var titleOptions = {
        text:''
      }

      if ( config ){

        if (setup.refresh)
          ui.empty();

        ui.makeNode('container', 'div', {});

        if ( options.headerLink ){

          if ( setup.headerLink ){

            headerLinkOptions.id = setup.headerLink.id;
            headerLinkOptions.name = setup.headerLink.name;
            headerLinkOptions.linkText = setup.headerLink.linkText;

          }

          ui.container.makeNode("history", "div", {
            tag: "a",
            text: headerLinkOptions.linkText,
            css: "ui small primary tertiary button right floated",
            style:
                "display:inline-block; cursor:pointer; padding-top:21px !important",
            href: sb.data.url.createPageURL("custom", headerLinkOptions),
          });
        }

        if (options.title) {
          if (setup.title) {
            titleOptions.text = setup.title.text;
          }

          ui.container.makeNode("title", "div", {
            style: "margin:0px; display:inline-block;",
            tag: "h3",
            css: "ui header",
            text: titleOptions.text,
          });
        }

        ui.makeNode("divider", "div", {
          css: "ui clearing divider",
          style: "",
        });

        return ui.patch();
      }
    }

    function pageContainer(config, data) {
      var ui = this;

      var setup = config.cont.setup;
      var display = config.cont.display;

      if (data) {
        ui.empty();

        switch (display.view) {
              case "home":
                displayHome(ui, display, data);
                break;

          case "current":
            displayCurrent(ui, display, data);

            break;

          case "membership":
            displayMembership(ui, display, data);

            break;

          case "history":
            displayHistory(ui, display, data);

            break;

          case "help":
            displayHelp(ui, display, data);

            break;

          case "charSolYearRollup":
            displayCharsolYearly(ui, display, data);

            break;

          case "singleServiceView":
            displaySingleServiceView(ui, display, data);

            break;

          case "actionitems":
            displayActionItemsList(ui, display, data);

            break;
        }
      }

      return ui.patch();
    }

    function pageFooter(config) {
      /*
                pageFooter[bool]
                config: {
                    setup:{
                        refresh:bool
                        , loading: bool
                        , footerLink[bool]:{
                            id: 		string
                            name: 	string
                            linkText: string
                        }
                    }
                    display[bool]:{
                        footerLink:[bool]
                }
            */

      var ui = this;
      var setup = config.footer.setup;
      var options = config.footer.display;

      var footerLinkOptions = {
        id: "fgPHistory",
        name: "History",
        linkText: "See History",
        startAt: appConfig.breadcrumbs[0].url,
      };

      if (setup.footerLink) {
        footerLinkOptions.id = setup.footerLink.id;
        footerLinkOptions.name = setup.footerLink.name;
        footerLinkOptions.linkText = setup.footerLink.linkText;
      }

      if ( config ){

        if (setup.refresh)
          ui.empty();

        if ( options.footerLink ){

          ui.makeNode('history', 'div', {
            tag:'a'
            , text: footerLinkOptions.linkText
            , css: 'ui small primary tertiary button right floated'
            , style: 'display:inline-block; cursor:pointer; padding-top:21px !important'
            , href: sb.data.url.createPageURL(
                'custom'
                , footerLinkOptions
            )
          });
        }

        ui.patch();
      }

      return;

    }

    if ( setup.refresh )
      ui.empty();

    if ( display.announcement ){

      // ui.makeNode('announcement', 'div', {css:''});
      // ui.makeNode('lb_1', 'lineBreak', {spaces: 2});

    }

    ui.makeNode("portalPageSegment", "div", { css: "ui segment" });
    ui.portalPageSegment.makeNode("headerContainer", "div", {});
    ui.portalPageSegment.makeNode("bodyContainer", "div", {
      style: "width:100%; min-height:85vh;",
    });

    Body = ui.portalPageSegment.bodyContainer.makeNode("container", "div", {});

    ui.portalPageSegment.bodyContainer.makeNode("fdivid", "div", {
      css: "ui hidden divider",
    });
    ui.portalPageSegment.bodyContainer.makeNode("footerContainer", "div", {
      style: "margin: 20px 0px;",
    });
    ui.patch();

    if (display.announcement) {
      pageUI.announcement = pageAnnouncement.bind(ui.announcement);
      pageUI.announcement(display);
    }

    if (display.header) {
      pageUI.header = pageHeader.bind(ui.portalPageSegment.headerContainer);
      pageUI.header(display);
    }

    if (display.cont) {
      if (_.isNull(data)) {
        var loadingMsg = setup.loadingMsg ? setup.loadingMsg : "Loading...";

        Body.makeNode("loader", "div", {
          css: "ui active inverted dimmer",
          text: '<div class="ui text loader">' + loadingMsg + "</div>",
        });

        ui.patch();
      }

      pageUI.cont = pageContainer.bind(Body);

      pageUI.cont(display, data);

    }

    if (display.footer) {
      pageUI.footer = pageFooter.bind(
          ui.portalPageSegment.bodyContainer.footerContainer
      );
      pageUI.footer(display);
    }

    if (callback && typeof callback == "function") {
      callback(ui);
    }
  }

  function renderHomeAndHistory(ui, display, data){

    var uiMain = ui;

    ui.empty();

    // var displayHistoryBack = {
    //   ui: ui,
    //   options: config.options,
    //   data: serviceTypes,
    // };

    //go to action items
    function goToActionItems(ui, nestedObj){
      var projectName = nestedObj.name;

      var dataConfig = {
        setup: {},
        options: {
          project: nestedObj,
        },
      };

      var pageConfig = {
        setup: {
          refresh: true,
        },
        display: {
          header: {
            setup: {
              refresh: true,
              title: {
                text:
                    '<i class="grey home icon"></i>' +
                    '<div class="content">' +
                    projectName +
                    '<div class="sub header">Action Items</div>' +
                    "</div>",
              },
              headerLink: {
                id: "fgPHelp",
                name: "Help & Education",
                linkText:
                    '<i class="icon question"></i>' +
                    "Help & Education",
              },
            },
            display: {
              title: true,
              headerLink: true,
            },
          },
          cont: {
            setup: {
              refresh: true,
              loading: true,
            },
            display: {
              view: "actionitems",
            },
          },
          footer: {
            setup: {
              refresh: true,
              footerLink: {
                id: "fgPHistory",
                name: "History",
                linkText: "See History",
              },
            },
            display: {
              footerLink: true,
            },
          },
        },
      };

      pageUI(ui, pageConfig, null, function (ui) {
        getActionItemData(dataConfig, function (data) {
          pageConfig.display.cont.setup.loading = false;

          pageUI(ui, pageConfig, data);
        });
      });
    }
    function listHistoryDeliverables(cont, options, list, renderHistory) {

      var rowHighlight = 0;

      cont.empty();

      var dvHistory = _.clone(list);

    //   dvHistory.unshift({
    //     name: "Back",
    //     clickFunctionSetup: displayHistoryBack,
    //   });

      _.mapObject(
          dvHistory,
          function (v, k) {
            var itemText = v.name;

            var itemOptions = {
              css: "item",
              style: "padding:21px; cursor:pointer;",
            };

            var buttonOptions = {
              css: "ui green button",
              text: "Download",
              style: "border-radius:25px;",
            };

            var clickFunction = function (container, cat, data) {
              var obj = data;

              sb.notify({
                type: "get-sys-modal",
                data: {
                  callback: function (modal) {
                    modal.body.empty();
                    modal.show();

                    modal.body.makeNode("h", "div", {
                      tag: "h1",
                      css: "ui header",
                      text: obj.name,
                    });

                    // Show download links for returns files
                    if (!_.isEmpty(obj._2)) {
                      modal.body
                          .makeNode("table", "div", {
                            tag: "table",
                            css: "ui very basic celled table",
                          })
                          .makeNode("head", "div", {
                            tag: "thead",
                          })
                          .makeNode("tr", "div", {
                            tag: "tr",
                            text: "<th>Name</th><th>Uploaded On</th><th>Download</th>",
                          });

                      modal.body.table.makeNode("body", "div", {
                        tag: "tbody",
                      });

                      var file = obj._2;
                      if (Array.isArray(file) && !_.isEmpty(file[0])) {
                        file = file[0];
                      }

                      modal.body.table.body.makeNode("i-" + i, "div", {
                        tag: "tr",
                        text:
                            "<td>" +
                            file.name +
                            "</td><td>" +
                            moment(file.date_created, "YYYY-MM-DD HH:mm:ss")
                                .local()
                                .format("MMM D, YYYY") +
                            "</td>" +
                            "<td>" +
                            '<a style="border-radius:25px;" class="ui green icon button" target="_blank" href="https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/' +
                            file.instance +
                            "/" +
                            file.loc +
                            '">' +
                            '<i class="ui download icon"></i> Download here' +
                            "</a>" +
                            "</td>",
                      });
                    } else {
                      modal.body.makeNode("emptmsg", "div", {
                        text: "Awaiting file attachment to be added to this Deliverable item. Foundation Group will update your Client Service record soon.",
                      });
                    }

                    modal.body.patch();
                  },
                },
              });
            };

            if (v.name == "Back") {
              itemText = "";
              buttonOptions.css = "ui basic icon button";
              buttonOptions.style = "border-radius: 25px; width:91.12";
              buttonOptions.text = '<i class="ui angle left icon"></i> Back';

              clickFunction = function () {
                item.content.btn.loading(true);
                renderHomeAndHistory(
                    uiMain,
                    'getServiceData',
                    getServiceData
                );
              };
            }

            if (rowHighlight % 2 != 0) {
              itemOptions = {
                css: "item",
                style: itemOptions.style + "background-color: #f6f6f6",
              };
            }

            var item = this.makeNode("item-" + rowHighlight, "div", itemOptions);

            item.makeNode("content", "div", { css: "right floated content" });

            item.content.makeNode("btn", "div", buttonOptions);

            item.makeNode("title", "div", {
              css: "ui medium header",
              text: itemText,
            });

            item.notify("click", {
              type: "fgportals-run",
              data: {
                run: clickFunction.bind(null, this, k, v),
              },
            });

            rowHighlight++;
          },
          cont
      );

      if (list.length == 0) {
        cont.makeNode("emtpyMsg", "div", {
          text: "Awaiting final deliverable download(s). Foundation Group will update your Client Service record soon.",
        });
      }

      return cont.patch();
    }

    //render
    sb.notify({
      type: 'field-list-view'
      , data: {
        container: ui
        , list: data
        , options: {
          actions: true
          , actions_ui: function (container, nestedObj) {

            var css = '';
            var menuCss = 'left menu';

            container.makeNode('actions', 'div', { css: css });

            if(nestedObj.object_bp_type === 'groups'){

              container.actions.makeNode('menu', 'div', {
                css: 'right floated ui mini icon circular basic simple dropdown',
                text: '<i class="ellipsis horizontal centered icon" style="font-size:1.5em !important;"></i>',
                style: 'text-align:center;'
              }).makeNode('menu', 'div'
                  , {
                    css: menuCss
                    // 									, style: 	'left: -100px;'
                  }
              );

              // View (check this)
              container.actions.menu.menu.makeNode('linkTo', 'div', {
                css: 'ui blue basic fluid item',
                text: '<i class="external square blue icon"></i> Go to',
                tag: 'a',
                style: 'border-radius:0px;',
              }).listeners.push(
                  function(s){
                    $(s).on('click', function (e) {
                      goToActionItems(ui, nestedObj);
                    });
                  }
              );
            }

            container.patch();

          }
        //   , create_ui: function (createBtnContainer) { }
          , get_child_data: function (parent, onComplete) {

            onComplete({data: parent});
            // Years w/services of that type
            // if (parent.projectTypes) {

            //   onComplete({
            //     data: parent.projectTypes
            //   });

            // } else if (!_.isEmpty(parent.services)) {

            //   onComplete({
            //     data: parent.services
            //   });

            // }

          }
          , emptyMessage: false
          , fields: {
            name: {
              title: 'Name'
              , type: 'title'
              , view: function (ui, obj) {

                sb.notify({
                  type: 'view-field'
                  , data: {
                    ui: ui
                    , type: 'title'
                    , obj: obj
                    , fieldName: 'name'
                    , property: 'name'
                    , options: {
                      inCollection: true
                      , onClick: function (item) {

                        if (
                            obj.object_bp_type !== 'groups'
                            || typeof obj.object_bp_type !== 'string'
                        ) {
                          return;
                        }

                        window.location.href = sb.data.url.getObjectPageParams(item, {});
                        return;

                        sb.notify({
                          type: 'get-sys-modal'
                          , data: {
                            callback: function (modal) {

                              modal.body.empty();
                              modal.show();

                              modal.body.makeNode('menu', 'div', {
                                css: 'ui secondary right floated menu'
                              });

                              var linkSetup = {
                                css: 'circular icon button item',
                                text: '<i class="external square alternate icon"></i>',
                                tag: 'a',
                                href: sb.data.url.getObjectPageParams(item, {})
                              };

                              var closeLinkSetup = {
                                css: 'circular icon button item',
                                text: '<i class="close icon"></i>',
                                tag: 'a'
                              };

                              modal.body.menu.makeNode('open', 'div', linkSetup);

                              modal.body.menu.makeNode('close', 'div', closeLinkSetup)
                                  .listeners.push(

                                  function (selector) {

                                    $(selector).on(
                                        'click'
                                        , function () {
                                          modal.hide();
                                        }
                                    );

                                  }
                              );

                              modal.body.makeNode('c', 'div', {});
                              modal.body.patch();

                              sb.notify({
                                type: 'view-entity'
                                , data: {
                                  ui: modal.body.c
                                  , id: item.id
                                }
                              });

                            }
                          }
                        });

                      }
                    }
                  }
                });

              }
            }
            , actionNeeded: {
              title: 'Status'
              , type: 'state'
              , view: function (ui, obj) {

                var statusOrders = {
                  'n/a': {
                    name: 'N/A'
                    , color: 'grey'
                  }
                  , open: {
                    name: 'Not Started'
                    , color: 'grey'
                  }
                  , not_started: {
                    name: 'Not Started'
                    , color: 'grey'
                  }
                  , preparing: {
                    name: 'Preparing'
                    , color: 'yellow'
                  }
                  , informationReceived: {
                    name: 'Information Received'
                    , color: 'blue'
                  }
                  , finalReview: {
                    name: 'Final Review'
                    , color: 'orange'
                  }
                  , deliveryPending: {
                    name: 'Delivery Pending'
                    , color: 'purple'
                  }
                  , pending: {
                    name: 'Pending'
                    , color: 'purple'
                  }
                  , onHold: {
                    name: 'On Hold'
                    , color: 'grey'
                  }
                  , actionNeeded: {
                    name: 'Action Needed'
                    , color: 'red'
                  }
                  , done: {
                    name: 'Complete'
                    , color: 'green'
                  }
                };
                var style = '';
                var href = '';
                var tag = 'div';

                // Link to the service
                if (
                    typeof obj.object_bp_type === 'string'
                    && obj.object_bp_type.split('.')[0] === '#' + serviceBpName
                ) {

                  tag = 'a';
                  style = 'cursor:pointer;';
                  href = sb.data.url.getObjectPageParams(obj);

                }

                if (
                    obj.status === 'done'
                    && typeof obj.object_bp_type === 'string'
                    && obj.object_bp_type.split('.')[0] === '#' + serviceBpName
                ) {

                  ui.makeNode('test', 'div', {
                    css: 'ui green label'
                    , text: '<i class="ui download icon"></i> Download'
                    , style: style
                  }).notify('click', {
                    type: 'entities-run'
                    , data: {
                      run: function (obj) {

                        sb.notify({
                          type: 'get-sys-modal'
                          , data: {
                            callback: function (modal) {

                              modal.body.empty();
                              modal.show();

                              modal.body.makeNode('h', 'div', {
                                tag: 'h1'
                                , css: 'ui header'
                                , text: obj.name
                              });

                              // Show download links for returns files
                              if (!_.isEmpty(obj._1)) {

                                modal.body.makeNode(
                                    'table'
                                    , 'div'
                                    , {
                                      tag: 'table'
                                      , css: 'ui very basic celled table'
                                    }
                                ).makeNode(
                                    'head'
                                    , 'div'
                                    , {
                                      tag: 'thead'
                                    }
                                ).makeNode(
                                    'tr'
                                    , 'div'
                                    , {
                                      tag: 'tr'
                                      , text: '<th>Name</th><th>Uploaded On</th><th>Download</th>'
                                    }
                                );

                                modal.body.table.makeNode(
                                    'body'
                                    , 'div'
                                    , {
                                      tag: 'tbody'
                                    }
                                );

                                _.each(obj._1, function (file, i) {

                                  modal.body.table.body.makeNode(
                                      'i-' + i
                                      , 'div'
                                      , {
                                        tag: 'tr'
                                        , text: '<td>' + file.name + '</td><td>' + moment(file.date_created, 'YYYY-MM-DD HH:mm:ss').local().format('MMM D, YYYY') + '</td><td><a class="ui basic icon button" target="_blank" href="https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/' + file.instance + '/' + file.loc + '"><i class="ui download icon"></i> Download here</a></td>'
                                      }
                                  );

                                });

                              } else {

                                modal.body.makeNode('msg', 'div', {
                                  tag: ''
                                  , css: 'ui icon message'
                                  , text: '<i class="inbox icon"></i><div class="header">No documents to download</div>'
                                });

                              }

                              modal.body.patch();

                            }
                          }
                        });

                      }.bind({}, obj)
                    }
                  }, sb.moduleId);

                } else {
                  if(obj.status) {
                    ui.makeNode('test', 'div', {
                      css: 'ui ' + statusOrders[obj.status].color + ' label'
                      , text: statusOrders[obj.status].name
                      , style: style
                      , href: href
                      , tag: tag
                    });
                  } else if(obj.object_bp_type === '#HDIjlE'){
                    ui.makeNode('test', 'div', {
                      css: 'ui grey label'
                      , text: 'SELECT'
                      , style: style
                      , href: href
                      , tag: 'div'
                    }).notify("click", {
                      type: "fgportals-run",
                      data: {
                        run: function (container) {
                          if (obj["_14"]) {
                            listHistoryDeliverables(uiMain, {}, obj["_14"]);
                          }
                        }
                      },
                    });




                  }

                }

              }
            }
          }
          , nestChildren: true
          , updateSelection: function () { }
          , style: 'padding:17px;'
          , subviews: {
            list: {
              nestChildren: true
              , forceState: true
            }
          }
          , showDate: true
          , showQuantity: true
          , showMultiplePerType: true
          , groupStyle: 'margin:27px;border-none;'
        }
      }
    });

    ui.patch();
  }

  return {
    init: function () {
      sb.listen({
        "fgportals-run": this.run,
      });

      sb.notify(
          {
            type: "register-application",
            data: {
              navigationItem: {
                moduleId: sb.moduleId,
                instanceId: sb.instanceId,
                id: "fg-portals",
                title: "FG | Portals",
                icon: '<i class="fa fa-file-text-o"></i>',
                views: [
                  {
                    id: "fgPHome",
                    type: "custom",
                    default: true,
                    title: "Home",
                    icon: {
                      color: "grey",
                      type: "home",
                    },
                    dom: function (ui) {
                      var dataConfig = {
                        setup: getUserData(appConfig, false),
                        options: {
                          queryFunc: getProjectData,
                          queryName: "getProjectData",
                          parse: {
                            action_items: true,
                            project_status: 'incomplete',
                          },
                        },
                      };

                      var pageConfig = {
                        setup: {
                          refresh: true,
                          loadingMsg:
                            "Checking for incomplete Forms or Action Items",
                        },
                        display: {
                          header: {
                            setup: {
                              refresh: true,
                              title: {
                                text:
                                  '<i class="grey home icon"></i>' +
                                  '<div class="content">' +
                                  "Home" +
                                  '<div class="sub header">Any active service with incomplete Action Items.</div>' +
                                  "</div>",
                              },
                              headerLink: {
                                id: "fgPHelp",
                                name: "Help & Education",
                                linkText:
                                  '<i class="icon question"></i>' +
                                  "Help & Education",
                              },
                            },
                            display: {
                              title: true,
                              headerLink: true,
                            },
                          },
                          cont: {
                            setup: {
                              refresh: true,
                              loading: true,
                            },
                            display: {
                              view: "home",
                              dueDate: true,
                              status: true,
                            },
                          },
                          footer: {
                            setup: {
                              refresh: true,
                              footerLink: {
                                id: "fgPHistory",
                                name: "History",
                                linkText: "See History",
                              },
                            },
                            display: {
                              footerLink: true,
                            },
                          },
                        },
                      };

                      pageUI(ui, pageConfig, null, function (ui) {
                        getProjectData(
                          dataConfig,
                          function (data) {

                            pageConfig.display.cont.setup.loading = false;

                            pageUI(ui, pageConfig, data);

                          },
                          true
                        );
                      });
                    },
                  },
                  {
                    id: "fgPMem",
                    dom: function (ui) {
                      /*
                                          For Override when viewing Yellow Dashboard on single Contacts
                                          var dataSetup = {
                                              company: 		false
                                              , contact: 	false
                                              , currentUser: false
                                          };
                                      */
                      var config = {
                        setup: getUserData(appConfig, false),
                        options: {},
                      };

                      var pageConfig = {
                        setup: {
                          refresh: true,
                        },
                        display: {
                          header: {
                            setup: {
                              refresh: true,
                              title: {
                                text:
                                    '<i class="grey user icon"></i>' +
                                    '<div class="content">' +
                                    "Membership" +
                                    '<div class="sub header">Manage your membership services</div>' +
                                    "</div>",
                              },
                              headerLink: {
                                id: "fgPHelp",
                                name: "Help & Education",
                                linkText: "Help & Education",
                              },
                            },
                            display: {
                              title: true,
                              headerLink: true,
                            },
                          },
                          cont: {
                            setup: {
                              refresh: true,
                              loading: true,
                            },
                            display: {
                              dueDate: true,
                              status: true,
                              view: "membership",
                            },
                          },
                          footer: {
                            setup: {
                              refresh: true,
                              footerLink: {
                                id: "fgPHistory",
                                name: "History",
                                linkText: "See History",
                              },
                            },
                            display: {
                              footerLink: true,
                            },
                          },
                        },
                      };

                      sb.data.db.service("FGPortalService", "checkMembershipService", { 'company' : appConfig.portal_company }, function( membership ) {

                            pageConfig.display.cont.setup.loading = false;

                            pageUI(ui, pageConfig, membership);

                          }
                      );

                    },
                    type: "custom",
                    default: true,
                    title: "Membership",
                    icon: {
                      color: "grey",
                      type: "user",
                    },
                  },
                  {
                    id: "fgPHistory",
                    type: "custom",
                    default: true,
                    title: "History",
                    icon: {
                      color: "grey",
                      type: "history",
                    },
                    dom: function (ui) {

                    var dataConfig = {
                        setup: getUserData(appConfig, false),
                        options: {
                          queryFunc: getProjectData,
                          queryName: "getProjectData",
                          parse: {
                            project_status: 'done',
                          },
                        },
                      };

                      var pageConfig = {
                        setup: {
                          refresh: true,
                          loadingMsg: "Checking for completed Services...",
                        },
                        display: {
                          header: {
                            setup: {
                              refresh: true,
                              title: {
                                text:
                                  '<i class="grey history icon"></i>' +
                                  '<div class="content">' +
                                  "History" +
                                  '<div class="sub header">View completed services & Download Files</div>' +
                                  "</div>",
                              },
                            },
                            display: {
                              title: true,
                              headerLink: false,
                            },
                          },
                          cont: {
                            setup: {
                              refresh: true,
                              loading: true,
                            },
                            display: {
                              view: "history",
                            },
                          },
                        }
                      };

                      pageUI(ui, pageConfig, null, function (ui) {

                        getProjectData(
                          dataConfig,
                          function (data) {

                            pageConfig.display.cont.setup.loading = false;

                            pageUI(ui, pageConfig, data);

                          },
                          true
                        );
                      });
                    },
                  },
                  {
                    id: "fgPHelp",
                    dom: function (ui) {
                      /*
                                          For Override when viewing Yellow Dashboard on single Contacts
                                          var dataSetup = {
                                              company: 		false
                                              , contact: 	false
                                              , currentUser: false
                                          };
                                      */
                      var config = {
                        setup: getUserData(appConfig, false),
                        options: {
                          parseData: function (data, callback) {
                            callback(data);
                          },
                        },
                      };

                      var pageConfig = {
                        setup: {
                          refresh: true,
                        },
                        display: {
                          header: {
                            setup: {
                              refresh: true,
                              title: {
                                text:
                                    '<i class="grey question icon"></i>' +
                                    '<div class="content">' +
                                    "Help & Education" +
                                    '<div class="sub header">Resources to guide you through our process</div>' +
                                    "</div>",
                              },
                            },
                            display: {
                              title: true,
                              headerLink: false,
                            },
                          },
                          cont: {
                            setup: {
                              refresh: true,
                              loading: true,
                            },
                            display: {
                              dueDate: true,
                              status: true,
                              view: "help",
                            },
                          },
                        },
                      };

                      // pageUI(ui, pageConfig, null, function(ui){

                      // getProjectData( config, function(data){

                      pageConfig.display.cont.setup.loading = false;
                      data = [];
                      pageUI(ui, pageConfig, data);

                      // });

                      // });
                    },
                    type: "custom",
                    default: true,
                    title: "Help & Education",
                    icon: {
                      color: "grey",
                      type: "question",
                    },
                  },
                  {
                    id: "fgservice-obj",
                    type: "object-view",
                    name: "Service",
                    title: "Client Service",
                    icon: {
                      type: "key",
                    },
                    dom: function (ui, state, draw) {},
                  },
                  {
                    id: "actionitems-obj",
                    type: "object-view",
                    name: "Action Items",
                    title: "Project Action Items",
                    icon: {
                      type: "key",
                    },
                    dom: function (ui, state, draw) {
                      var projectName = state.pageObject.name;

                      var dataConfig = {
                        setup: {},
                        options: {
                          project: state.pageObject,
                        },
                      };

                      var pageConfig = {
                        setup: {
                          refresh: true,
                        },
                        display: {
                          header: {
                            setup: {
                              refresh: true,
                              title: {
                                text:
                                    '<i class="grey home icon"></i>' +
                                    '<div class="content">' +
                                    projectName +
                                    '<div class="sub header">Action Items</div>' +
                                    "</div>",
                              },
                              headerLink: {
                                id: "fgPHelp",
                                name: "Help & Education",
                                linkText:
                                    '<i class="icon question"></i>' +
                                    "Help & Education",
                              },
                            },
                            display: {
                              title: true,
                              headerLink: true,
                            },
                          },
                          cont: {
                            setup: {
                              refresh: true,
                              loading: true,
                            },
                            display: {
                              view: "actionitems",
                            },
                          },
                          footer: {
                            setup: {
                              refresh: true,
                              footerLink: {
                                id: "fgPHistory",
                                name: "History",
                                linkText: "See History",
                              },
                            },
                            display: {
                              footerLink: true,
                            },
                          },
                        },
                      };

                      pageUI(ui, pageConfig, null, function (ui) {
                        getActionItemData(dataConfig, function (data) {
                          pageConfig.display.cont.setup.loading = false;

                          pageUI(ui, pageConfig, data);
                        });
                      });
                    },
                  },
                  {
                    id: "deliverables-obj",
                    type: "object-view",
                    name: "Deliverables",
                    title: "Client Service Deliverables",
                    icon: {
                      type: "key",
                    },
                    dom: function (ui, state, draw) {},
                  },
                ],
              },
            },
          },
          sb.moduleId
      );
    },
    run: function (data) {
      data.run();
    },
  };
});
