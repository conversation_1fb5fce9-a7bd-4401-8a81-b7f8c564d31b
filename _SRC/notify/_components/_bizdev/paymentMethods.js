Factory.register("paymentMethods", function (sb) {
  var domObj = {},
    stripeKey = STRIPE_PK,
    objectId = {},
    components = {},
    ui = {},
    buttonUI = {},
    tableUI = {},
    modalsUI = {},
    compact = false,
    collapse = false,
    objectType = "contacts",
    flexible = false,
    stripeCustomer = null;

  var txFeePercent = 0.0;
  var txFeeFlat = 0;

  var plaidKey = "98d4fb666a3da81c09dd167dd48f0f";
  var plaidEnv = "development";

  // Sample stripe card
  // card number --> 4242 4242 4242 4242
  // exp date --> set a future date
  // zip --> any 5 digit number

  var STRIPE_ERROR_MESSAGE = "The payment method provided is unable to be authorized. Please use an alternate form of payment.";

  function allUI() {
    function homeView(dom) {}

    function showTable() {
      var dom = this;

      var crudSetup = {
        domObj: dom,
        objectType: "payments",
        childObjs: 2,
        tableTitle: '<i class="fa fa-users"></i> Payments',
        searchObjects: false,
        settings: {
          action: [
            {
              object_type: "bank_account",
              name: "Deposit Accounts",
            },
            {
              object_type: "invoice_type",
              name: "Invoice Types",
            },
            {
              object_type: "payment_schedule_template",
              name: "Payment Schedule Templates",
              action: paymentScheduleSettings,
            },
          ],
        },
        filters: false,
        download: false,
        headerButtons: {
          reload: {
            name: "Reload",
            css: "pda-btn-blue",
            action: function () {},
          },
        },
        rowSelection: false,
        multiSelectButtons: {},
        home: {
          header: "Payments",
          action: homeView,
          default: false,
        },
        rowLink: {
          type: "modal",
          header: function (obj) {
            return "Test";
          },
          action: singleView,
        },
        visibleCols: {
          date_created: "Date Created",
          amount: "Amount",
          status: "Status",
          transaction_id: "Transaction ID",
        },
        cells: {
          date_created: function (obj) {
            return moment(obj.date_created).format("M/D/YYYY h:mm a");
          },
          amount: function (obj) {
            if (obj.details && obj.details.object) {
              if (obj.details.object == "refund") {
                return "$" + (obj.details.amount / 100).formatMoney();
              }
            }

            return "$" + (obj.amount / 100).formatMoney();
          },
          created_by: function (obj) {
            return obj.created_by.fname + " " + obj.created_by.lname;
          },
          transaction_id: function (obj) {
            if (obj.details && obj.details.id) {
              return obj.details.id;
            } else if (obj.vendor_id) {
              return "Manual payment - " + obj.vendor_id.toUpperCase();
            } else {
              return "Manual payment";
            }
          },
          status: function (obj) {
            var ret = "";

            // Check if details exists before accessing its properties
            if (!obj.details || !obj.details.object) {
              return '<label class="label label-default">MANUAL</label>';
            }

            switch (obj.details.object) {
              case "charge":
                switch (obj.details.status) {
                  case "succeeded":
                    ret =
                      '<label class="label label-success">' +
                      obj.details.status.toUpperCase() +
                      "</label>";

                    break;

                  default:
                    ret =
                      '<label class="label label-warning">' +
                      obj.details.status.toUpperCase() +
                      "</label>";
                }

                break;

              case "refund":
                switch (obj.details.status) {
                  case "succeeded":
                    ret =
                      '<label class="label label-warning">Refunded - ' +
                      obj.details.status.toUpperCase() +
                      "</label>";

                    break;

                  default:
                    ret =
                      '<label class="label label-danger">Refund attempted - ' +
                      obj.details.status.toUpperCase() +
                      "</label>";
                }

                break;

              default:
                ret = '<label class="label label-info">MANUAL PAYMENT</label>';
            }

            return ret;
          },
        },
        data: function (paged, callback) {
          sb.data.db.obj.getAll(
            "payments",
            function (ret) {
              callback(ret);
            },
            1,
            paged
          );
        },
      };

      components.table.notify({
        type: "show-table",
        data: crudSetup,
      });
    }

    function singleView(payment, dom) {
      // Always show payment ID and amount
      dom.body.makeNode("id", "headerText", {
        text: "<small>Payment ID</small> " + payment.id,
        size: "x-small",
      });

      dom.body.makeNode("amount", "headerText", {
        text:
          "<small>Total paid</small> $" +
          (payment.amount / 100).formatMoney(2),
        size: "x-small",
      });

      dom.body.makeNode("created", "headerText", {
        text:
          "<small>Paid on</small> " +
          moment(payment.date_created).format("M/D/YYYY h:mm a"),
        size: "x-small",
      });

      // Check if payment has details object
      if (payment.details) {
        // If it has a type property, it's likely a Stripe payment
        if (payment.details.type) {
          dom.body.makeNode("method", "headerText", {
            text: "<small>Paid by</small> " + payment.details.type.toUpperCase(),
            size: "x-small",
          });

          // Only show notes if they exist
          if (payment.details.notes) {
            dom.body.makeNode("notes", "headerText", {
              text: "<small>Notes:</small><br />" + payment.details.notes,
              size: "x-small",
            });
          }
        } else {
          // For other payment types with details

          // Show status if available
          if (payment.details.status) {
            dom.body.makeNode("method", "headerText", {
              text: "<small>Status</small> " + payment.details.status.toUpperCase(),
              size: "x-small",
            });
          }

          // Show destination if available
          if (payment.details.destination) {
            dom.body.makeNode("notes", "headerText", {
              text: "<small>Account:</small> " + payment.details.destination,
              size: "x-small",
            });
          }

          // Show customer if available
          if (payment.details.customer) {
            dom.body.makeNode("customer", "headerText", {
              text: "<small>Customer:</small> " + payment.details.customer,
              size: "x-small",
            });
          }
        }
      } else {
        // For manual payments without details
        dom.body.makeNode("method", "headerText", {
          text: "<small>Payment Type</small> Manual Payment",
          size: "x-small",
        });
      }

      dom.footer.makeNode("btns", "buttonGroup", {});

      dom.footer.btns
        .makeNode("invoice", "button", {
          text: '<i class="fa fa-external-link"></i> View Invoice',
          css: "pda-btn-blue",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: function (payment) {
                this.footer.btns.invoice.loading();

                sb.data.db.obj.getWhere(
                  "invoices",
                  {
                    payments: {
                      type: "contains",
                      value: payment.id,
                    },
                  },
                  function (invoices) {
                    sb.notify({
                      type: "app-change-page",
                      data: {
                        to: "invoices",
                        pageParams: {
                          invoiceId: invoices[0].id,
                        },
                      },
                    });
                  }
                );
              }.bind(dom, payment),
            },
          },
          sb.moduleId
        );

      if (!payment.details.type) {
        dom.footer.btns
          .makeNode("refund", "button", {
            text: '<i class="fa fa-undo"></i> Refund',
            css: "pda-btn-orange",
          })
          .notify(
            "click",
            {
              type: "paymentMethodRun",
              data: {
                run: function (payment) {
                  var dom = this;

                  sb.dom.alerts.ask(
                    {
                      title: "Are you sure?",
                      text: "This cannot be undone.",
                    },
                    function (resp) {
                      if (resp) {
                        swal.disableButtons();

                        sb.data.db.controller(
                          "refundStripePayment",
                          { transactionId: payment.details.id },
                          function (refundDetails) {
                            sb.data.db.obj.getWhere(
                              "invoices",
                              {
                                payments: {
                                  type: "contains",
                                  value: payment.id,
                                },
                              },
                              function (invoices) {
                                var invoice = invoices[0];

                                invoice.paid = invoice.paid - payment.amount;
                                invoice.balance =
                                  invoice.balance + payment.amount;
                                invoice.memo +=
                                  "<br />Payment " +
                                  payment.details.id +
                                  " for $" +
                                  (payment.amount / 100).formatMoney(2) +
                                  " has been refunded on " +
                                  moment().format("M/D/YYYY @ h:mm a") +
                                  ".";

                                payment.amount = 0;
                                payment.details = refundDetails;

                                sb.data.db.obj.update(
                                  "invoices",
                                  invoice,
                                  function (updatedInvoice) {
                                    sb.data.db.obj.update(
                                      "payments",
                                      payment,
                                      function (updated) {
                                        sb.dom.alerts.alert(
                                          "Success",
                                          "The payment has been refunded.",
                                          "success"
                                        );

                                        dom.hide(function () {
                                          components.table.notify({
                                            type: "update-table",
                                            data: {},
                                          });
                                        });
                                      }
                                    );
                                  },
                                  4
                                );
                              }
                            );
                          }
                        );
                      }
                    }
                  );
                }.bind(dom, payment),
              },
            },
            sb.moduleId
          );
      }

      if (payment.details.type) {
        dom.footer.btns
          .makeNode("delete", "button", {
            text: '<i class="fa fa-trash-o"></i> Delete',
            css: "pda-btn-red",
          })
          .notify(
            "click",
            {
              type: "paymentMethodRun",
              data: {
                run: function (payment) {
                  var dom = this;

                  sb.dom.alerts.ask(
                    {
                      title: "Are you sure?",
                      text: "This cannot be undone.",
                    },
                    function (resp) {
                      if (resp) {
                        swal.disableButtons();

                        sb.data.db.obj.getWhere(
                          "invoices",
                          {
                            payments: {
                              type: "contains",
                              value: payment.id,
                            },
                          },
                          function (invoices) {
                            var invoice = invoices[0];

                            invoice.paid = invoice.paid - payment.amount;
                            invoice.balance = invoice.balance + payment.amount;
                            invoice.payments = _.reject(
                              invoice.payments,
                              function (obj) {
                                return obj.id == payment.id;
                              }
                            );

                            sb.data.db.obj.update(
                              "invoices",
                              invoice,
                              function (updatedInvoice) {
                                sb.data.db.obj.erase(
                                  "payments",
                                  payment.id,
                                  function (deleted) {
                                    sb.dom.alerts.alert(
                                      "Success",
                                      "The payment has been deleted.",
                                      "success"
                                    );

                                    dom.hide(function () {
                                      components.table.notify({
                                        type: "update-table",
                                        data: {},
                                      });
                                    });
                                  }
                                );
                              },
                              4
                            );
                          }
                        );
                      }
                    }
                  );
                }.bind(dom, payment),
              },
            },
            sb.moduleId
          );
      }

      dom.footer.btns
        .makeNode("close", "button", {
          text: '<i class="fa fa-times"></i> Close',
          css: "pda-btnOutline-red",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: function () {
                this.hide();
              }.bind(dom),
            },
          },
          sb.moduleId
        );

      dom.body.patch();
      dom.footer.patch();
    }

    this.state.show = showTable.bind(this);
  }

  function buildTableUI() {
    function createTable() {
      var compTitle = '<i class="fa fa-usd"></i> Payment Methods';
      var uiGrid = false;
      var getObject = function () {};
      var cardsCSS = "";

      function createNewMethod(customer, show, createButton, mainDom) {
        if (show) {
          //this.makeNode('break1', 'lineBreak', {spaces:1});

          var dom = this;

          this.makeNode("modal", "modal", {
            onShow: function () {
              var stripe = Stripe(stripeKey);

              var elements = stripe.elements();

              // Custom styling can be passed to options when creating an Element.
              var style = {
                base: {
                  // Add your base input styles here. For example:
                  fontSize: "16px",
                  lineHeight: "24px",
                },
              };

              // Create an instance of the card Element
              var card = elements.create("card", { style: style });

              // Add an instance of the card Element into the `card-element` <div>
              card.mount("#card-element");

              card.addEventListener("change", function (event) {
                var displayError = document.getElementById("card-errors");
                if (event.error) {
                  displayError.textContent = event.error.message;
                } else {
                  displayError.textContent = "";
                }
              });

              dom.modal.body.cont.btns
                .makeNode("cancel", "button", {
                  text: '<i class="fa fa-times"></i> Cancel',
                  css: "pda-btn-red",
                })
                .notify(
                  "click",
                  {
                    type: "paymentMethodRun",
                    data: {
                      run: function () {
                        this.modal.hide();
                      }.bind(dom),
                    },
                  },
                  sb.moduleId
                );

              dom.modal.body.cont.btns
                .makeNode("submit", "button", {
                  text: '<i class="fa fa-check"></i> Save',
                  css: "pda-btn-green",
                })
                .notify(
                  "click",
                  {
                    type: "paymentMethodRun",
                    data: {
                      run: saveNewMethod.bind(dom, stripe, card, customer),
                    },
                  },
                  sb.moduleId
                );

              dom.modal.body.cont.btns.patch();
            },
          });

          this.modal.body.makeNode("cont", "div", {
            css: "ui raised blue segment",
          });

          this.modal.body.cont.makeNode("cardForm", "div", {
            css: "card-element",
            text: '<div id="card-element"></div>',
          });

          this.modal.body.cont.makeNode("formErrors", "text", {
            css: "card-errors pda-color-red",
            text: '<div id="card-errors" class="pda-color-red"></div>',
          });

          this.modal.body.cont
            .makeNode("btns", "buttonGroup", { css: "" })
            .makeNode("submit", "button", {
              text: '<i class="fa fa-circle-o-notch fa-spin"></i> Loading',
              css: "pda-btn-primary",
            });

          this.patch();

          this.modal.show();
        } else {
          this.empty();

          this.patch();

          if (createButton) {
            createButton
              .makeNode("createButton", "button", {
                css: "pda-btnOutline-green",
                text: '<i class="fa fa-plus"></i> Add New Payment Method',
              })
              .notify(
                "click",
                {
                  type: "paymentMethodRun",
                  data: {
                    run: createNewMethod.bind(
                      this,
                      customer,
                      true,
                      createButton
                    ),
                  },
                },
                sb.moduleId
              );

            createButton.patch();
          }
        }
      }

      function saveNewMethod(stripe, card, customer) {
        var dom = this;

        stripe.createToken(card).then(function (result) {
          if (result.error) {
            // Inform the user if there was an error

            /*
						this.cont.btns.makeNode('submit', 'button', {text:'Try Again <i class="fa fa-arrow-right"></i>', css:'pda-btn-x-small pda-btn-primary'}).notify('click', {
							type:'paymentMethodRun',
							data:{
								run:saveNewMethod.bind(this, stripe, card, customer)
							}
						}, sb.moduleId);

						this.cont.btns.patch();
*/

            var errorElement = document.getElementById("card-errors");
            errorElement.textContent = result.error.message;
          } else {
            //delete this.cont.btns.cancel;
            dom.modal.body.cont.btns.makeNode("submit", "button", {
              text: 'Validating <i class="fa fa-circle-o-notch fa-spin"></i>',
              css: "pda-btn-x-small pda-btn-blue",
            });
            dom.modal.body.cont.btns.patch();

            var updateObj = {
                token: result.token,
                objectId: objectId,
                objectType: objectType,
              },
              dataCall = "createStripeCustomer";
            var root = undefined;

            if (objectType === "instances") {
              root = sb.url + "/api/_getAdmin.php?do=";
            }

            if (customer) {
              updateObj.customer = customer.id;
              dataCall = "addStripeSourceToCustomer";
            }

            sb.data.db.controller(
              dataCall,
              updateObj,
              function (response) {
                dom.modal.body.cont.btns.makeNode("submit", "button", {
                  css: "pda-btn-x-small pda-btn-green",
                  text: 'Validated <i class="fa fa-check"></i>',
                });
                dom.modal.body.cont.btns.patch();

                setTimeout(function () {
                  tableUI.state.show();

                  dom.modal.hide();
                }, 1000);
              },
              root
            );
          }
        });
      }

      if (objectType === "contacts") {
        getObject = get_contactObj;
      } else if (objectType === "instances") {
        getObject = get_instanceObj;
      } else {
        throw 'This component accepts "contacts" and "instances" for objectType';
        return;
      }

      if (collapse == false) {
        uiGrid = true;
      }

      if (flexible === true) {
        cardsCSS = "ui cards";
        cardCSS = "card";
      }

      var stripePaymentSourcesUI = this.makeNode("compWrapper", "container", {
        title: compTitle,
        uiGrid: uiGrid,
      });

      if (collapse === "none") {
        this.makeNode("compWrapper", "div", {});
      }

      if (compact !== true) {
        this.compWrapper.makeNode("title", "headerText", {
          text: compTitle,
          size: "x-small",
        });
      }

      this.patch();

      var dom = this;

      getObject(
        objectId,
        function (obj) {
          var getStripeCustomer_setup = {};
          var root = undefined;

          if (objectType === "contacts") {
            getStripeCustomer_setup.contactId = obj.id;
            getStripeCustomer_setup.stripeId = obj.stripe_id || false;
          } else if (objectType === "instances") {
            getStripeCustomer_setup.instanceId = obj.id;

            root = sb.url + "/api/_getAdmin.php?do=";
          }

          var paramObj = {
            contactId: obj.id,
            stripeId: obj.stripe_id,
            verifiedSources: true,
            instanceId: appConfig.id,
          };

          sb.data.db.service(
            "StripeService",
            "getStripeCustomer",
            paramObj,
            function (response) {
              delete dom.loading;

              if (!response.customer) {
                dom.compWrapper.makeNode("noItems", "headerText", {
                  text: "There was an error retrieving the Stripe payment sources for this contact.",
                  size: "xx-small",
                  css: "text-center",
                });
              } else {
                sb.notify({
                  type: "view-field",
                  data: {
                    type: "contact-payment-sources",
                    property: "object",
                    ui: stripePaymentSourcesUI,
                    obj: response,
                    options: {
                      contactId: obj.id,
                      initiatePayments: false,
                      feesList: null,
                      invoiceBalance: null,
                      instanceId: appConfig.id,
                      eventId: null,
                      selectedINvoices: [],
                      selectedINvoiceIds: [],
                      paymentForm: false,
                    },
                  },
                });
              }

              dom.patch();
            },
            root
          );
        },
        appConfig.instance
      );
    }

    this.state.show = createTable.bind(this);
  }

  function calculatePaymentWithFees(price, percentFee, flatFee) {
    var amount = parseFloat(price);
    var total =
      (amount + parseFloat(flatFee)) / (1 - parseFloat(percentFee) / 100);
    var fee = total - amount;

    return {
      amount: amount,
      fee: parseInt(fee * 100),
      total: parseInt(total * 100),
    };
  }

  function get_contactObj(objectId, callback, instanceName) {
    if (instanceName) {
      sb.data.db.obj.getWhere(
        "instances",
        { instance: instanceName },
        function (inst) {
          sb.data.db.obj.getById("contacts", objectId, function (contactObj) {
            if (!_.isEmpty(inst[0].stripe_account_id)) {
              contactObj.instanceStripeId = inst[0].stripe_account_id;
            }

            callback(contactObj);
          });
        }
      );
    }
  }

  function get_instanceObj(objectId, callback) {
    sb.data.db.obj.getById("instances", objectId, function (instanceObj) {
      callback(instanceObj);
    });
  }

  function buttonState(setup, buttonSetupOverride) {
    var buttonSetup = {
      text: "Make A Payment",
      css: "",
      notification: "payment-succeeded",
      action: "",
      refreshAction: false,
      skip: false,
      admin: false,
      oneSource: false,
    };

    if (buttonSetupOverride) {
      if (buttonSetupOverride.text) {
        buttonSetup.text = buttonSetupOverride.text;
      }

      if (buttonSetupOverride.css) {
        buttonSetup.css = buttonSetupOverride.css;
      }

      if (buttonSetupOverride.notification) {
        buttonSetup.notification = buttonSetupOverride.notification;
      }

      if (buttonSetupOverride.action) {
        buttonSetup.action = buttonSetupOverride.action;
      }

      if (buttonSetupOverride.skip) {
        buttonSetup.skip = buttonSetupOverride.skip;
      }

      if (buttonSetupOverride.admin) {
        buttonSetup.admin = buttonSetupOverride.admin;
      }

      if (buttonSetupOverride.refreshAction) {
        buttonSetup.refreshAction = buttonSetupOverride.refreshAction;
      }

      if (buttonSetupOverride.oneSource) {
        buttonSetup.oneSource = buttonSetupOverride.oneSource;
      }
    }

    if (buttonSetup.skip === true) {
      startPaymentFlow.call(this, setup, buttonSetup);
    } else {
      this.makeNode("pay", "button", {
        text: buttonSetup.text,
        css: buttonSetup.css,
      }).notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: startPaymentFlow.bind(this, setup, buttonSetup),
          },
        },
        sb.moduleId
      );
    }

    this.patch();
  }

  function createNewMethod(setup, contactObj, customer, methodType, callback) {
    var stripe = Stripe(stripeKey);

    function newBankAccount() {
      var formArgs = {
        routing_number: {
          name: "routing_number",
          type: "text",
          label: "Routing Number",
        },
        account_number: {
          name: "account_number",
          type: "text",
          label: "Account Number",
        },
        account_holder_name: {
          name: "account_holder_name",
          type: "text",
          label: "Account Holder Name",
        },
        account_holder_type: {
          name: "account_holder_type",
          type: "select",
          label: "Account Holder Type",
          options: [
            {
              name: "Individual",
              value: "individual",
            },
            {
              name: "Company",
              value: "company",
            },
          ],
        },
      };

      this.makeNode("break1", "lineBreak", { spaces: 1 });

      this.makeNode("cont", "container", { css: "pda-container text-left" });

      this.cont.makeNode("title", "headerText", {
        text: "Add a bank account",
        size: "small",
        css: "text-center",
      });

      this.cont.makeNode("cardForm", "form", formArgs);

      this.cont.makeNode("formErrors", "text", {
        css: "card-errors pda-color-red",
        text: '<div id="card-errors" class="pda-color-red"></div>',
      });

      this.cont
        .makeNode("btns", "buttonGroup", { css: "pull-right" })
        .makeNode("submit", "button", {
          text: '<i class="fa fa-circle-o-notch fa-spin"></i> Loading',
          css: "pda-btn-primary pda-btn-x-small",
        });

      this.patch();

      this.cont.btns
        .makeNode("cancel", "button", {
          text: '<i class="fa fa-times"></i> Cancel',
          css: "pda-btn-red pda-btn-x-small",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: startPaymentFlow.bind(this, setup, buttonSetup),
            },
          },
          sb.moduleId
        );

      this.cont.btns
        .makeNode("submit", "button", {
          text: '<i class="fa fa-check"></i> Save',
          css: "pda-btn-green pda-btn-x-small",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: saveNewBankMethod.bind(
                this,
                stripe,
                customer,
                contactObj,
                setup,
                buttonSetup
              ),
            },
          },
          sb.moduleId
        );

      this.cont.btns.patch();
    }

    function newCardAccount() {
      this.makeNode("break1", "lineBreak", { spaces: 1 });

      this.makeNode("cont", "div", { css: "" });

      this.cont.makeNode("title", "div", {
        text: "Enter credit/debit card number",
        css: "ui huge header",
      });

      this.cont.makeNode("cardForm", "div", {
        css: "card-element",
        text: '<div id="card-element" class="ui basic segment"></div>',
      });

      this.cont.makeNode("formErrors", "text", {
        css: "card-errors",
        text: '<div id="card-errors" class="ui red small header"></div>',
      });

      this.cont
        .makeNode("btns", "buttonGroup", { css: "" })
        .makeNode("submit", "div", {
          text: "Save",
          css: "ui green compact mini green button",
        });

      this.patch();

      var elements = stripe.elements();

      // Custom styling can be passed to options when creating an Element.
      var style = {
        base: {
          // Add your base input styles here. For example:
          fontSize: "16px",
          lineHeight: "24px",
        },
      };

      // Create an instance of the card Element
      var card = elements.create("card", { style: style });

      // Add an instance of the card Element into the `card-element` <div>
      card.mount("#card-element");

      card.addEventListener("change", function (event) {
        var displayError = document.getElementById("card-errors");
        if (event.error) {
          displayError.textContent = event.error.message;
        } else {
          displayError.textContent = "";
        }
      });

      this.cont.btns
        .makeNode("cancel", "div", {
          text: "Cancel",
          css: "ui mini compact yellow button",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: function () {
                callback();

                //createNewMethod.call(this, setup, contactObj, customer, methodType, callback);
              }.bind(this),
            },
          },
          sb.moduleId
        );

      this.cont.btns
        .makeNode("submit", "div", {
          text: "Save",
          css: "ui mini compact green button",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: function (dom) {
                dom.cont.btns.submit.loading();

                saveNewMethod2(
                  stripe,
                  card,
                  contactObj,
                  customer,
                  function (token) {
                    callback(token);
                  }
                );
              }.bind({}, this),
            },
          },
          sb.moduleId
        );

      this.cont.btns.patch();
    }

    this.empty();

    this.patch();

    switch (methodType) {
      case "bank_account":
        newBankAccount.call(this);

        break;

      default:
        newCardAccount.call(this);
    }
  }

  function deleteMethod(source, customer, setup, buttonSetup) {
    var dom = this;

    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "",
      },
      function (resp) {
        swal.disableButtons();

        if (resp) {
          sb.data.db.controller(
            "deleteStripePaymentSource&api_webform=true&pagodaAPIKey=" +
              appConfig.instance,
            { token: source, customer: customer },
            function (done) {
              swal.close();

              //sb.dom.alerts.alert('Payment method deleted!', '', 'success');

              delete dom["card-" + card.id];

              dom.patch();
            },
            sb.url + "/api/_getAdmin.php?do="
          );
        }
      }
    );
  }

  function enterManualPayment(setup, buttonSetup, customer, contactObj) {
    var dom = this,
      formArgs = {
        type: {
          name: "type",
          type: "select",
          label: "Payment Type",
          options: [
            { name: "Check", value: "check" },
            { name: "Cash", value: "cash" },
            { name: "Other", value: "other" },
          ],
          change: function (form, value) {
            if (value === "check") {
              dom.cont.form.checkNumber.update({
                type: "number",
              });

              dom.cont.form.checkImg.update({
                type: "file-upload",
              });
            } else {
              dom.cont.form.checkNumber.update({
                type: "hidden",
              });

              dom.cont.form.checkImg.update({
                type: "hidden",
              });
            }
          },
        },
        checkNumber: {
          name: "check_number",
          type: "number",
          label: "Check Number",
        },
        amount: {
          name: "amount",
          type: "usd",
          label: "Payment Amount",
          value: setup.price,
        },
        checkImg: {
          name: "check_image",
          type: "file-upload",
          label: "Upload check image",
        },
        payment_date: {
          name: "payment_date",
          type: "date",
          label: "Payment Date",
          dateFormat: "M/D/YYYY",
          value: moment().format("M/D/YYYY"),
        },
        details: {
          name: "text",
          type: "textbox",
          label: "Details",
          rows: 10,
        },
      };

    dom.empty();
    dom.empty();

    dom.makeNode("cont", "div", {});

    dom.cont.makeNode("title", "div", {
      text: "Manual Payment",
      css: "ui huge header",
    });

    dom.cont.makeNode("titleBreak", "div", { text: "<br />" });

    dom.cont.makeNode("form", "form", formArgs);

    dom.cont.makeNode("btnBreak", "div", { text: "<br />" });

    dom.cont.makeNode("btns", "div", { css: "ui buttons" });

    dom.cont.btns
      .makeNode("save", "button", {
        text: '<i class="fa fa-check"></i> Save Payment',
        css: "pda-btn-green",
      })
      .notify(
        "click",
        {
          type: "invoicesRun",
          data: {
            run: saveManualPayment.bind(
              dom,
              setup,
              buttonSetup,
              customer,
              contactObj
            ),
          },
        },
        sb.moduleId
      );

    dom.cont.btns
      .makeNode("cancel", "button", {
        text: '<i class="fa fa-times"></i> Cancel',
        css: "pda-btnOutline-red",
      })
      .notify(
        "click",
        {
          type: "invoicesRun",
          data: {
            run: startPaymentFlow.bind(dom, setup, buttonSetup),
          },
        },
        sb.moduleId
      );

    dom.patch();
  }

  function makeDefault(source, customer, setup, buttonSetup) {
    var dom = this;

    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "",
      },
      function (resp) {
        swal.disableButtons();

        if (resp) {
          sb.data.db.controller(
            "changeStripeDefaultSource&api_webform=true&pagodaAPIKey=" +
              appConfig.instance,
            { token: source, customer: customer },
            function (done) {
              //sb.dom.alerts.alert('Success!', '', 'success');

              swal.close();

              if (tableUI.state) {
                tableUI.state.show();
              } else {
                startPaymentFlow.call(dom, setup, buttonSetup);
              }
            },
            sb.url + "/api/_getAdmin.php?do="
          );
        }
      }
    );
  }

  function payWithCard(card, customer, payment, notification) {
    var dom = this;

    function chargeCustomer(invoices, callback, count, ret, remainingToCharge) {
      if (!count) {
        count = 0;
      }

      if (!ret) {
        ret = [];
      }

      if (invoices[count] && remainingToCharge > 0) {
        if (!invoices[count].balance) {
          invoices[count].balance = invoices[count].amount;
        }

        if (invoices[count].balance > 0 && remainingToCharge > 0) {
          var amountToCharge = 0;

          if (isNaN(invoices[count].balance)) {
            invoices[count].balance = invoices[count].amount;
          }

          if (remainingToCharge > invoices[count].balance) {
            amountToCharge = invoices[count].balance;
          } else {
            amountToCharge = remainingToCharge;
          }

          var feeSchedule = calculatePaymentWithFees(
            amountToCharge / 100,
            +txFeePercent,
            +txFeeFlat
          );

          sb.data.db.controller(
            "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
              appConfig.instance,
            {
              objectType: "instances",
              queryObj: { instance: appConfig.instance },
            },
            function (instances) {
              sb.data.db.controller(
                "chargeStripeConnectCustomer&api_webform=true&pagodaAPIKey=" +
                  appConfig.instance,
                {
                  amount: feeSchedule.total,
                  customer: customer,
                  connectAccountId: instances[0].stripe_account_id,
                  sourceId: card.id,
                },
                function (response) {
                  if (
                    response.status == "succeeded" ||
                    response.status == "pending"
                  ) {
                    var paymentObj = {
                      amount: amountToCharge,
                      details: response,
                      vendor_id: customer.id,
                      invoice: invoices[count].id,
                      status: response.status,
                      fee: feeSchedule.fee,
                      main_contact: payment.customerId,
                      main_client: payment.clientId,
                      main_object: invoices[count].related_object,
                    };

                    sb.data.db.controller(
                      "recordPayment&api_web_form=true&pagodaAPIKey=" +
                        appConfig.instance,
                      paymentObj,
                      function (newPayment) {
                        ret.push(newPayment);

                        /*
										ret.push({
												status:true,
												card:card,
												customer:customer,
												payment:newPayment,
												transaction:response
											});
	*/

                        count++;
                        remainingToCharge = remainingToCharge - amountToCharge;

                        chargeCustomer(
                          invoices,
                          callback,
                          count,
                          ret,
                          remainingToCharge
                        );
                      },
                      sb.url + "/api/_getAdmin.php?do="
                    );
                  } else {
                    ret.push({
                      status: false,
                      invoice: invoices[count].id,
                    });

                    count++;

                    // payment was not successful
                    // show error message
                    alert(STRIPE_ERROR_MESSAGE);
                    // TODO: Handle unsuccessful payments.
                    chargeCustomer(
                      invoices,
                      callback,
                      count,
                      ret,
                      remainingToCharge
                    );
                  }
                },
                sb.url + "/api/_getAdmin.php?do="
              );
            },
            sb.url + "/api/_getAdmin.php?do="
          );
        } else {
          ret.push({
            status: false,
            invoice: invoices[count].id,
          });

          count++;

          // no charge needed
          chargeCustomer(invoices, callback, count, ret, remainingToCharge);
        }
      } else {
        callback(ret);
      }
    }

    dom.makeNode("modal", "modal", {
      onShow: function () {
        sb.data.db.obj.getAll("invoice_fees", function (feesList) {
          var accountType = "card";
          var fees = feesList[0];
          if (fees) {
            if (fees && card.object == "card") {
              txFeePercent = fees.credit_card_percent;
              txFeeFlat = fees.credit_card_flat_fee;
            } else {
              txFeePercent = fees.ach_percent;
              txFeeFlat = fees.ach_flat_fee;
            }
          }

          feeSchedule = calculatePaymentWithFees(
            parseFloat(payment.price / 100),
            +txFeePercent,
            +txFeeFlat
          );
          var feeDisplayText = feeSchedule.fee;

          if (appConfig.instance == "infinity" || appConfig.instance == "nlp") {
            feeDisplayText = 3;
          }

          var modal = dom.modal.body;
          var fee = feeSchedule.fee;

          modal.makeNode("title", "div", {
            text: "How much would you like to pay?",
            css: "ui huge header",
          });

          modal.makeNode("form", "form", {
            amount: {
              name: "amount",
              label: "Enter Amount",
              type: "usd",
              value: payment.price,
              change: function (form, value) {
                var newVal = +value.replace(/\D/g, "");

                feeSchedule = calculatePaymentWithFees(
                  parseFloat(newVal / 100),
                  +txFeePercent,
                  txFeeFlat
                );

                var feeDisplayText = feeSchedule.fee;

                if (
                  appConfig.instance == "infinity" ||
                  appConfig.instance == "nlp"
                ) {
                  feeDisplayText = 3;
                }

                $(modal.value.selector).text(
                  "Balance: $" + (newVal / 100).formatMoney()
                );
                $(modal.fee.selector).text(
                  "Processing Fee: $" +
                    (feeSchedule.fee / 100).formatMoney() +
                    " (" +
                    feeDisplayText +
                    "% + $" +
                    (+txFeeFlat).formatMoney() +
                    ")"
                );
                $(modal.total.selector).html(
                  "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
                    (feeSchedule.total / 100).formatMoney() +
                    "</span>"
                );
                $(modal.btns.pay.selector).html(
                  "Pay $" + (feeSchedule.total / 100).formatMoney() + " Now"
                );
              },
            },
          });

          modal.makeNode("value", "div", {
            text: "Balance: $" + (payment.price / 100).formatMoney(),
          });
          modal.makeNode("fee", "div", {
            text:
              "Processing Fee: $" +
              (feeSchedule.fee / 100).formatMoney() +
              " (" +
              feeDisplayText +
              "% + $" +
              (+txFeeFlat).formatMoney() +
              ")",
          });
          modal.makeNode("total", "div", {
            text:
              "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
              (feeSchedule.total / 100).formatMoney() +
              "</span>",
          });

          modal.makeNode("btnsbreak", "div", { text: "<br />" });
          modal.makeNode("btns", "div", { css: "ui huge buttons" });
          modal.btns
            .makeNode("pay", "div", {
              css: "ui green button",
              text: "Pay $" + (feeSchedule.total / 100).formatMoney() + " Now",
            })
            .notify(
              "click",
              {
                type: "paymentMethodRun",
                data: {
                  run: function () {
                    var paymentAmount =
                      +modal.form.process().fields.amount.value;

                    feeSchedule = calculatePaymentWithFees(
                      parseFloat(paymentAmount / 100),
                      +txFeePercent,
                      txFeeFlat
                    );

                    var toBill = feeSchedule.total;

                    swal
                      .fire({
                        title:
                          "Pay $" +
                          (feeSchedule.total / 100).formatMoney() +
                          " now?",
                        text: "",
                        icon: "question",
                        showCancelButton: true,
                        confirmButtonText: "Yes",
                        confirmButtonColor: "#21BA45",
                        showLoaderOnConfirm: true,
                        preConfirm: function () {
                          return new Promise((resolve) => {
                            //swal.showLoading();
                            //swal.disableButtons();

                            payment.price = paymentAmount;

                            if (Array.isArray(payment.invoiceId)) {
                              chargeCustomer(
                                payment.invoiceId,
                                function (done) {
                                  if (done) {
                                    var success = false;
                                    _.each(done, function (item) {
                                      if (item.status !== false) {
                                        success = true;
                                      }
                                    });

                                    dom.modal.hide();

                                    if (success === false) {
                                      swal(
                                        "Error",
                                        "There was a problem processing this transaction.",
                                        "error"
                                      );
                                    } else {
                                      if (notification.refreshAction) {
                                        notification.action(
                                          done,
                                          notification.refreshAction
                                        );
                                      } else {
                                        notification.action(done);
                                      }

                                      resolve();
                                    }
                                  }
                                },
                                0,
                                [],
                                payment.price
                              );
                            } else {
                              sb.data.db.controller(
                                "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                                  appConfig.instance,
                                {
                                  objectType: "instances",
                                  queryObj: { instance: appConfig.instance },
                                },
                                function (instances) {
                                  sb.data.db.controller(
                                    "chargeStripeConnectCustomer&api_webform=true&pagodaAPIKey=" +
                                      appConfig.instance,
                                    {
                                      amount: toBill,
                                      customer: customer,
                                      connectAccountId:
                                        instances[0].stripe_account_id,
                                    },
                                    function (response) {
                                      if (
                                        response.status == "succeeded" ||
                                        response.status == "pending"
                                      ) {
                                        var paymentObj = {
                                          amount: payment.price,
                                          fee: feeSchedule.fee,
                                          details: response,
                                          status: response.status,
                                          vendor_id: customer.id,
                                          invoice: payment.invoiceId,
                                          main_contact: payment.customerId,
                                          main_client: payment.clientId,
                                        };

                                        sb.data.db.controller(
                                          "recordPayment&api_web_form=true&pagodaAPIKey=" +
                                            appConfig.instance,
                                          paymentObj,
                                          function (newPayment) {
                                            // payment was successful
                                            if (newPayment) {
                                              dom.modal.hide();

                                              if (notification.refreshAction) {
                                                notification.action(
                                                  newPayment,
                                                  notification.refreshAction
                                                );
                                              } else {
                                                notification.action(newPayment);
                                              }

                                              resolve();
                                            }
                                          },
                                          sb.url + "/api/_getAdmin.php?do="
                                        );
                                      } else {
                                        // payment was not successful
                                        // show error message
                                        alert(STRIPE_ERROR_MESSAGE);
                                        // TODO: Handle unsuccessful payments.
                                      }
                                    },
                                    sb.url + "/api/_getAdmin.php?do="
                                  );
                                },
                                sb.url + "/api/_getAdmin.php?do="
                              );
                            }
                          });
                        },
                      })
                      .then(function (response) {});
                  },
                },
              },
              sb.moduleId
            );
          modal.btns.makeNode("or", "div", { css: "ui or" });
          modal.btns
            .makeNode("cancel", "div", { css: "ui button", text: "Cancel" })
            .notify(
              "click",
              {
                type: "paymentMethodRun",
                data: {
                  run: function () {
                    dom.modal.hide();
                  },
                },
              },
              sb.moduleId
            );

          modal.patch();
        });
      },
    });

    dom.patch();

    dom.modal.show();
  }

  function paymentMade(paymentObj) {}

  function paymentScheduleSettings(dom, bp) {
    sb.data.db.obj.getAll(
      "payment_schedule_template",
      function (objects) {
        var comp = {
          table: sb.createComponent("crud-table"),
        };

        var crudSetup = {
          domObj: dom,
          objectType: "payment_schedule_template",
          searchObjects: false,
          filters: false,
          download: false,
          headerButtons: {
            reload: {
              name: "Reload",
              css: "pda-btn-blue",
              action: function () {},
            },
            newObject: {
              name: '<i class="fa fa-plus"></i> Create New',
              css: "pda-btn-green",
              domType: "full",
              action: function (obj, domObj) {
                domObj.makeNode("btns", "buttonGroup", { css: "pull-right" });

                domObj.makeNode("title", "headerText", {
                  text: "Create Payment Schedule",
                });

                domObj.makeNode("break", "lineBreak", {});

                domObj
                  .makeNode("cont", "container", { css: "pda-container" })
                  .makeNode("form", "form", {
                    name: {
                      name: "name",
                      type: "text",
                      label: "Payment Template Name",
                    },
                  });

                domObj.btns
                  .makeNode("back", "button", {
                    text: '<i class="fa fa-arrow-left"></i> Cancel',
                    css: "pda-btnOutline-red",
                  })
                  .notify(
                    "click",
                    {
                      type: "paymentMethodRun",
                      data: {
                        run: function (bp) {
                          paymentScheduleSettings(this, bp);
                        }.bind(dom, bp),
                      },
                    },
                    sb.moduleId
                  );

                domObj.btns
                  .makeNode("save", "button", {
                    text: '<i class="fa fa-floppy-o"></i> Save',
                    css: "pda-btn-green",
                  })
                  .notify(
                    "click",
                    {
                      type: "paymentMethodRun",
                      data: {
                        run: function () {
                          if (this.cont.form.process().completed == false) {
                            sb.dom.alerts.alert(
                              "Error",
                              "Please fill out the entire form.",
                              "error"
                            );
                            return;
                          }

                          this.btns.save.loading();
                          this.btns.back.css(
                            "pda-btnOutline-red pda-btn-disabled"
                          );

                          var templateObj = {
                            name: this.cont.form.process().fields.name.value,
                          };

                          var dom = this;

                          sb.data.db.obj.create(
                            "payment_schedule_template",
                            templateObj,
                            function (newObj) {
                              singlePaymentSchedule(newObj, dom);
                            },
                            1
                          );
                        }.bind(domObj),
                      },
                    },
                    sb.moduleId
                  );

                domObj.patch();
              },
            },
          },
          rowSelection: true,
          rowLink: {
            type: "tab",
            header: function (obj) {
              return obj.name;
            },
            action: singlePaymentSchedule,
          },
          multiSelectButtons: {
            erase: {
              name: '<i class="fa fa-trash-o"></i> Delete',
              css: "pda-btn-red",
              domType: "default",
              action: "erase",
            },
          },
          visibleCols: {
            name: "Name",
          },
          home: false,
          settings: false,
          cells: {
            name: function (obj) {
              return obj.name;
            },
          },
          childObjs: 1,
          data: function (paged, callback) {
            sb.data.db.obj.getAll(
              "payment_schedule_template",
              function (ret) {
                callback(ret);
              },
              2,
              paged
            );
          },
        };

        comp.table.notify({
          type: "show-table",
          data: crudSetup,
        });
      },
      1
    );
  }

  function saveManualPayment(setup, buttonSetup, customer, contactObj) {
    var dom = this,
      formData = dom.cont.form.process();

    /*
		if(formData.completed == false){
			sb.dom.alerts.alert('', 'Please fill out the whole form.', 'error');
			return;
		}
*/

    dom.cont.btns.save.loading();

    sb.data.db.controller(
      "getIPAddress&pagodaAPIKey=" + appConfig.instance,
      {},
      function (ip) {
        var company = 0;

        if (contactObj) {
          if (contactObj.company) {
            company = contactObj.company;
          }
        }

        var paymentObj = {
          amount: +formData.fields.amount.value,
          checkNumber: +formData.fields.check_number.value,
          checkImg: formData.fields.check_image.value,
          details: {
            payment_date: formData.fields.payment_date.value,
            type: formData.fields.type.value,
            notes: formData.fields.text.value,
            ip_address: ip,
          },
          vendor_id: company,
          invoice: setup.invoiceId,
          main_client: setup.clientId,
          main_contact: setup.customerId,
        };

        sb.data.db.obj.getById(
          "invoices",
          setup.invoiceId,
          function (invoice) {
            paymentObj.owner = +invoice.main_contact.id;

            sb.data.db.controller(
              "recordPayment&api_web_form=true&pagodaAPIKey=" +
                invoice.instance,
              paymentObj,
              function (newPayment) {
                if (buttonSetup.action) {
                  buttonSetup.action(newPayment);
                }

                setTimeout(function () {
                  window.location.reload();
                }, 5000);
              }
            );
          },
          2
        );
      }
    );
  }

  function saveNewACHMethod(stripe, contactObj) {
    var dom = this;
    var formInfo = dom.payment.body.achContainer.achForm.process();

    dom.payment.body.achContainer.btns.submit.loading();

    var account = {
      country: "US",
      currency: "usd",
    };

    _.each(formInfo.fields, function (field, fieldName) {
      account[fieldName] = field.value;
    });

    stripe.createACHToken(account).then(function (result) {
      if (result.error) {
        dom.payment.body.achContainer.makeNode("formErrors", "div", {
          css: "ui red small header",
          text: result.error.message,
        });

        dom.payment.body.achContainer.patch();

        dom.payment.body.achContainer.btns.submit.loading(false);
      } else {
        var obj = {
          btokToken: result.token.id,
          acctRouting: result.token.bank_account.routing_number,
          acctLast4: result.token.bank_account.last4,
          acctName: result.token.bank_account.account_holder_name,
          contactId: contactObj.id,
          verificationEmail: account.account_verification_email,
          instanceName: contactObj.instance,
        };

        sb.data.db.service(
          "StripeService",
          "initiateACHMicroDepositVerification",
          obj,
          function (response) {
            if (response.error) {
              dom.makeNode("payment", "div", {});

              dom.patch();

              dom.payment.makeNode("achConfirmation", "div", {
                css: "ui negative message",
                text: `Something went wrong. Please contact your invoice provider for assistance.`,
              });

              dom.payment.patch();
            } else if (response.bankAcct && response.bankAcct.status == "new") {
              dom.makeNode("payment", "div", {});

              dom.patch();

              dom.payment.makeNode("achConfirmation", "div", {
                css: "ui warning message",
                text: `The process of adding your new ACH Account has been initiated with Stripe.
							<br/>
							<br/>
							You will receive an email that will provide further details on verifying your ACH account.`,
              });

              dom.payment.patch();
            } else if (
              response.bankAcct &&
              response.bankAcct.status == "verified"
            ) {
              dom.makeNode("payment", "div", {});

              dom.patch();

              dom.payment.makeNode("achConfirmation", "div", {
                css: "ui positive message",
                text: `Great news, this account appears to already be verified. You can use it to complete this payment immediately.`,
              });

              dom.payment.patch();
            } else {
              dom.makeNode("payment", "div", {});

              dom.patch();

              dom.payment.makeNode("achConfirmation", "div", {
                css: "ui negative message",
                text: `Something went wrong. Please contact your invoice provider for assistance.`,
              });

              dom.payment.patch();
            }
          }
        );
      }
    });
  }

  function verifyACHMethod(stripeCustomerId, stripeAcctId, verificationInfo) {
    var dom = this;
    var instance = sb.data.url.getParams().i;
    appConfig.instance = instance;
    var formInfo = dom.cont.verificationContainer.verificationForm.process();
    var customerId = stripeCustomerId;
    var acctId = stripeAcctId;
    var microDeposit1 = formInfo.fields["micro_value_1"].value;
    var microDeposit2 = formInfo.fields["micro_value_2"].value;
    var email = formInfo.fields["email"].value;
    var acctVerificationObjId = verificationInfo["acctVerificationObj"]["id"];
    var obj = {
      customerId: customerId,
      acctId: acctId,
      acctVerificationObjId: acctVerificationObjId,
      microDeposit1: microDeposit1,
      microDeposit2: microDeposit2,
      email: email,
      instanceName: instance,
    };

    dom.cont.verificationContainer.btns.submit.loading();

    sb.data.db.service(
      "StripeService",
      "verifyACHAccountWithStripe",
      obj,
      function (response) {
        if (response.error) {
          dom.cont.makeNode("modal", "modal", {
            onShow: function () {
              dom.cont.modal.header.makeNode("modalHeader", "div", {
                css: "ui header large",
                text: "Verification Failed",
              });
              dom.cont.modal.header.makeNode("divider", "div", {
                css: "ui horizontal divider",
              });
              dom.cont.modal.header.patch();

              dom.cont.modal.body.makeNode("modalBody", "div", {
                text:
                  `
						<span class='ui small header'>Uh oh! Stripe was unable to verify your bank account.</span>
						<br/><br/>
						<span>You submitted the following micro deposit values:</span><br/><br/>
						<span>Micro Deposit 1 Amount:&emsp;<span style='font-weight:bold'>` +
                  response["microDeposit1"] +
                  `</span><br/>
						<span>Micro Deposit 2 Amount:&emsp;<span style='font-weight:bold'>` +
                  response["microDeposit2"] +
                  `</span><br/><br/>
						<span>Stripe returned the following message:</span><br/><br/>
						<span class='ui red text' style='font-weight:bold'>` +
                  response["error"]["error"]["message"] +
                  `</span><br/><br/>
						<span>If you would like assistnace with verifying your account, please reach out to the <a href='mailto: <EMAIL>?subject=Account Verification Assistance (` +
                  verificationInfo["bankAcct"]["account_holder_name"] +
                  `)' style='text-decoration:underline;' class='ui blue text'>Bento Support Team</a>.</span>`,
              });
              dom.cont.modal.body.makeNode("divider", "div", {
                css: "ui horizontal divider",
              });
              dom.cont.modal.body.patch();

              dom.cont.modal.footer
                .makeNode("cancel", "button", {
                  text: "Close",
                  css: "pda-btn-blue",
                })
                .notify(
                  "click",
                  {
                    type: "paymentMethodRun",
                    data: {
                      run: function () {
                        this.modal.hide();
                      }.bind(dom.cont),
                    },
                  },
                  sb.moduleId
                );
              dom.cont.modal.footer.patch();
            },
          });
          dom.cont.patch();
          dom.cont.modal.show();
        } else {
          dom.cont.makeNode("verificationContainer", "div", {});
          dom.cont.patch();

          dom.cont.makeNode("verificationContainer", "div", {
            css: "ui success message",
            text:
              `
					<span class='ui small header'>Success!</span>
					<br/>
					Stripe was able to verify your ` +
              response["bankAcct"]["bank_name"] +
              ` account ending in ` +
              response["bankAcct"]["last4"] +
              `. To avoid any issues please wait 5-10 minutes before initiating your first payment using your new account.`,
          });
          dom.cont.patch();
        }
      }
    );
  }

  function addCCtoStripeCustomer(stripeCustomer, contactId) {
    var stripe = Stripe(stripeKey);
    var dom = this;

    dom.paymentCont.makeNode("div", "div", {
      css: "ui right floated raised secondary grey compact clearing segment",
    });

    dom.paymentCont.div.makeNode("break1", "lineBreak", { spaces: 1 });

    dom.paymentCont.div.makeNode("cont", "div", { css: "" });

    dom.paymentCont.div.makeNode("title", "div", {
      text: "Enter credit/debit card number",
      css: "ui huge header",
    });

    dom.paymentCont.div.makeNode("cardForm", "div", {
      css: "card-element",
      text: '<div id="card-element" class="ui basic segment"></div>',
    });

    dom.paymentCont.div.makeNode("formErrors", "text", {
      css: "card-errors",
      text: '<div id="card-errors" class="ui red small header"></div>',
    });

    dom.paymentCont.div.makeNode("btns", "buttonGroup", { css: "" });

    dom.patch();

    var elements = stripe.elements();

    // Custom styling can be passed to options when creating an Element.
    var style = {
      base: {
        // Add your base input styles here. For example:
        fontSize: "16px",
        lineHeight: "24px",
      },
    };

    // Create an instance of the card Element
    var card = elements.create("card", { style: style });

    // Add an instance of the card Element into the `card-element` <div>
    card.mount("#card-element");

    card.addEventListener("change", function (event) {
      var displayError = document.getElementById("card-errors");
      if (event.error) {
        displayError.textContent = event.error.message;
      } else {
        displayError.textContent = "";
      }
    });

    dom.paymentCont.div.btns
      .makeNode("cancel", "div", {
        text: "Cancel",
        css: "ui mini compact yellow button",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function () {
              dom.makeNode("paymentCont", "div", { css: "" }).patch();
            }.bind(this),
          },
        },
        sb.moduleId
      );

    dom.paymentCont.div.btns
      .makeNode("submit", "div", {
        text: "Save",
        css: "ui mini compact green button",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function (dom) {
              dom.paymentCont.div.btns.submit.loading();

              saveNewCreditCard(dom, stripe, card, stripeCustomer, contactId);
            }.bind({}, this),
          },
        },
        sb.moduleId
      );

    dom.paymentCont.div.btns.patch();
  }

  function saveNewCreditCard(dom, stripe, card, stripeCustomer, contactId) {
    stripe.createToken(card).then(function (result) {
      var obj = {
        ctokToken: result.token.id,
        stripeCustomerId: stripeCustomer.id,
        cardLast4: result.token.card.last4,
        cardExpYear: result.token.card.exp_year,
        cardBrand: result.token.card.brand,
        cardZip: result.token.card.address_zip,
      };

      sb.data.db.service(
        "StripeService",
        "saveCreditCardToStripeCustomer",
        obj,
        function (response) {
          dom.paymentCont.div.btns.submit.loading(false);

          if (response.error) {
            dom.makeNode("paymentCont", "div", { css: "" }).patch();

            dom.paymentCont.makeNode("div", "div", {
              css: "ui negative message",
              text: `This card was unable to be added to the Stripe customer. Please contact your invoice provider for assistance.`,
            });

            dom.paymentCont.patch();
          } else {
            dom.makeNode("paymentCont", "div", { css: "" }).patch();

            dom.paymentCont.makeNode("div", "div", {
              css: "ui success message",
              text: `This card has been added to the Stripe customer, please select a 'Take Payment' button above to complete a payment using this card.`,
            });

            dom.paymentCont.patch();
          }

          console.log(response);
        }
      );
    });
  }

  function saveNewBankMethod(stripe, customer, contactObj, setup, buttonSetup) {
    var dom = this;
    formInfo = dom.cont.cardForm.process();

    dom.cont.btns.submit.loading();

    var account = {
      country: "US",
      currency: "usd",
    };

    _.each(formInfo.fields, function (field, fieldName) {
      account[fieldName] = field.value;
    });

    stripe.createToken("bank_account", account).then(function (result) {
      if (result.error) {
        // Inform the user if there was an error
        var errorElement = document.getElementById("card-errors");
        errorElement.textContent = result.error.message;

        //dom.cont.btns.submit.loading(false);
      } else {
        dom.cont.btns.makeNode("submit", "button", {
          text: 'Validating <i class="fa fa-circle-o-notch fa-spin"></i>',
          css: "pda-btn-x-small pda-btn-primary",
        });
        dom.cont.btns.patch();

        var updateObj = {
            token: result.token,
            contact: objectId,
          },
          dataCall = "createStripeCustomer";

        if (contactObj) {
          dataCall = "createStripeCustomer";
          updateObj.contact = contactObj.id;
        }

        if (customer) {
          updateObj.customer = customer;
          dataCall = "addStripeSourceToCustomer";
        }

        sb.data.db.controller(
          dataCall + "&api_webform=true&pagodaAPIKey=" + appConfig.instance,
          updateObj,
          function (response) {
            dom.cont.btns.makeNode("submit", "button", {
              css: "pda-btn-x-small pda-btn-green",
              text: 'Validated <i class="fa fa-check"></i>',
            });
            dom.cont.btns.patch();

            setTimeout(function () {
              dom.empty();

              startPaymentFlow.call(dom, setup, buttonSetup);
            }, 1000);
          },
          sb.url + "/api/_getAdmin.php?do="
        );
      }
    });
  }

  function saveNewMethod(
    stripe,
    card,
    customer,
    contactObj,
    setup,
    buttonSetup
  ) {
    var dom = this;

    stripe.createToken(card).then(function (result) {
      if (result.error) {
        // Inform the user if there was an error
        var errorElement = document.getElementById("card-errors");
        errorElement.textContent = result.error.message;
      } else {
        dom.cont.btns.makeNode("submit", "button", {
          text: 'Validating <i class="fa fa-circle-o-notch fa-spin"></i>',
          css: "pda-btn-x-small pda-btn-primary",
        });
        dom.cont.btns.patch();

        var updateObj = {
            token: result.token,
            contact: objectId,
          },
          dataCall = "createStripeCustomer";

        if (contactObj) {
          dataCall = "createStripeCustomer";
          updateObj.contact = contactObj.id;
        }

        if (customer) {
          updateObj.customer = customer;
          dataCall = "addStripeSourceToCustomer";
        }

        sb.data.db.controller(
          dataCall + "&api_webform=true&pagodaAPIKey=" + appConfig.instance,
          updateObj,
          function (response) {
            dom.cont.btns.makeNode("submit", "button", {
              css: "pda-btn-x-small pda-btn-green",
              text: 'Validated <i class="fa fa-check"></i>',
            });
            dom.cont.btns.patch();

            setTimeout(function () {
              dom.empty();

              startPaymentFlow.call(dom, setup, buttonSetup);
            }, 1000);
          },
          sb.url + "/api/_getAdmin.php?do="
        );
      }
    });
  }

  function saveNewMethod2(stripe, card, contactObj, customer, callback) {
    var dom = this;

    stripe.createToken(card).then(function (result) {
      if (result.error) {
        // Inform the user if there was an error
        var errorElement = document.getElementById("card-errors");
        errorElement.textContent = result.error.message;
      } else {
        var updateObj = {
            token: result.token,
            contact: objectId,
          },
          dataCall = "createStripeCustomer";

        if (contactObj) {
          dataCall = "createStripeCustomer";
          updateObj.contact = contactObj.id;
        }

        if (customer) {
          updateObj.customer = customer.id;
          dataCall = "addStripeSourceToCustomer";
        }

        sb.data.db.controller(
          dataCall + "&api_webform=true&pagodaAPIKey=" + appConfig.instance,
          updateObj,
          function (response) {
            callback(result.token);
          },
          sb.url + "/api/_getAdmin.php?do="
        );
      }
    });
  }

  function singlePaymentSchedule(paymentTemplateObject, domObj) {
    function getDataForForm(callback) {
      var data = {};

      sb.data.db.obj.getAll(
        "inventory_billable_categories",
        function (categories) {
          sb.data.db.obj.getAll("bank_account", function (accounts) {
            sb.data.db.obj.getAll("invoice_type", function (invoices) {
              data.categories = categories;
              data.accounts = accounts;
              data.invoices = invoices;

              callback(data);
            });
          });
        }
      );
    }

    function preparePaymentTemplateForm(modal, data, template) {
      var categoryNames = [],
        tableInfo = [],
        categories = data.categories,
        accounts = data.accounts,
        invoices = data.invoices;

      _.each(categories, function (category) {
        categoryNames.push({
          name: "inventory_billable_categories",
          label: category.name,
          value: category.id,
        });
      });

      /*
			var accountNames = [];

			_.each(accounts, function(account){
			   accountNames.push({
			        name: account.bank_name,
			        value: account.id
			   })
			});
			*/

      var invoiceTypes = [];

      _.each(invoices, function (invoice) {
        invoiceTypes.push({
          name: invoice.invoice_type,
          value: invoice.id,
        });
      });

      var paymentScheduleForm = {
        inventory_billable_categories: {
          type: "checkbox",
          name: "inventory_billable_categories",
          label: "Categories",
          options: categoryNames,
        },
        payment_type: {
          type: "radio",
          name: "payment_type",
          label: "Select One",
          options: [
            {
              name: "Percent of Total",
              value: "percentOfTotal",
            },
            {
              name: "Flat Rate",
              value: "flatRate",
            },
            {
              name: "Remaining Balance",
              value: "remainingBalance",
            },
          ],
        },
        percent_of_total: {
          type: "number",
          name: "percent_of_total",
        },
        flat_rate: {
          type: "usd",
          name: "flat_rate",
        },
        before_after: {
          type: "radio",
          name: "before_after",
          label:
            "Is this payment due before or after the event / project date?",
          options: [
            {
              name: "Before",
              value: "before",
            },
            {
              name: "After",
              value: "after",
            },
          ],
        },
        due_date: {
          type: "number",
          name: "due_date",
          label: "How many days before or after?:",
        },
        invoice_type: {
          type: "radio",
          name: "invoice_type",
          label: "Invoice Type",
          options: invoiceTypes,
        },
        /*
			   bank_account: {
			        type: 'radio',
			        name: 'bank_account',
			        label: 'Bank Account',
			        options: accountNames
			   }
			*/
      };

      if (template) {
        paymentScheduleForm.inventory_billable_categories.value = [];

        _.each(template.inventory_billable_categories, function (cat) {
          paymentScheduleForm.inventory_billable_categories.value.push(cat.id);
        });

        paymentScheduleForm.payment_type.value = template.payment_type;
        paymentScheduleForm.before_after.value = template.before_after;
        paymentScheduleForm.due_date.value = template.due_date;
        paymentScheduleForm.percent_of_total.value = template.percent_of_total;
        paymentScheduleForm.flat_rate.value = template.flat_rate;
        paymentScheduleForm.invoice_type.value = template.invoice_type.id;
        //paymentScheduleForm.bank_account.value = template.bank_account.id;
      }

      modal.empty();

      modal.makeNode("btns", "buttonGroup", { css: "pull-right" });

      modal.btns
        .makeNode("back", "button", {
          text: '<i class="fa fa-arrow-left"></i> Cancel',
          css: "pda-btnOutline-red",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: function (template) {
                singlePaymentSchedule(template, this);
              }.bind(modal, paymentTemplateObject),
            },
          },
          sb.moduleId
        );

      modal.makeNode("title", "headerText", {
        text: "Create a new invoice template",
      });

      modal.makeNode("body", "container", { css: "pda-container" });
      modal.makeNode("footer", "container", { css: "pda-container" });

      modal.body.makeNode("paymentScheduleForm", "form", paymentScheduleForm); // Payment Schedule Form

      modal.body.paymentScheduleForm.payment_type.notify("change", {
        type: "paymentMethodRun",
        data: {
          run: function (data) {
            radioOptionsChanged(data, this);
          }.bind(modal, modal.body.paymentScheduleForm),
        },
      });

      // If template already exists, display update button. If not, display Add button.
      if (template) {
        modal.btns
          .makeNode("updatePaymentButton", "button", {
            text: '<i class="fa fa-check"></i> Save Changes',
            css: "pda-btn-green",
          })
          .notify(
            "click",
            {
              type: "paymentMethodRun",
              data: {
                run: function (template, mainDom) {
                  var newTemplate = {},
                    domObj = this;

                  // Delete Current List Item
                  paymentTemplate.templates.splice(
                    _.indexOf(
                      paymentTemplate.templates,
                      _.findWhere(paymentTemplate.templates, {
                        id: template.id,
                      })
                    ),
                    1
                  );

                  sb.data.db.obj.update(
                    "payment_schedule_template",
                    paymentTemplate,
                    function (data) {
                      var formData = domObj.process();

                      _.each(formData.fields, function (field, key) {
                        newTemplate[key] = field.value;
                      });

                      // push to paymentTemplates
                      paymentTemplate.templates.push(newTemplate);

                      paymentTemplate.getChildObjs = 1;

                      sb.data.db.obj.update(
                        "payment_schedule_template",
                        paymentTemplate,
                        function (done) {
                          singlePaymentSchedule(done, mainDom);
                        },
                        1
                      );
                    },
                    1
                  );
                }.bind(modal.body.paymentScheduleForm, template, modal),
              },
            },
            sb.moduleId
          );
      } else {
        modal.btns
          .makeNode("addPaymentButton", "button", {
            text: '<i class="fa fa-check"></i> Save',
            css: "pda-btn-green",
          })
          .notify(
            "click",
            {
              type: "paymentMethodRun",
              data: {
                run: function (template, mainDom) {
                  var newTemplate = {},
                    formData = this.process();

                  _.each(formData.fields, function (field, key) {
                    newTemplate[key] = field.value;
                  });

                  // push to paymentTemplates
                  paymentTemplate.templates.push(newTemplate);

                  paymentTemplate.getChildObjs = 1;

                  sb.data.db.obj.update(
                    "payment_schedule_template",
                    paymentTemplate,
                    function (response) {
                      singlePaymentSchedule(response, mainDom);
                    }
                  );
                }.bind(modal.body.paymentScheduleForm, template, modal),
              },
            },
            sb.moduleId
          );
      }

      modal.patch();

      radioOptionsChanged(modal.body.paymentScheduleForm, modal);
    }

    function preparePaymentTemplateTable(container, paymentTemplates) {
      if (container.myTable) {
        delete container.myTable;
      }

      container.makeNode("myTable", "table", {
        css: "table-hover table-condensed",
        columns: {
          categories: "Categories",
          paymentType: "Payment Type",
          paymentAmount: "Payment Amount",
          dueDate: "Due Date",
          invoiceType: "Invoice Type",
          btns: "",
        },
      });

      var count = 0;
      var paymentDueDate;
      var paymentValue;
      var rowName;

      paymentTemplates = _.sortBy(paymentTemplates, function (paymentTemplate) {
        if (paymentTemplate.before_after == "before") {
          return -paymentTemplate.due_date;
        } else {
          return paymentTemplate.due_date;
        }
      });

      _.each(
        paymentTemplates,
        function (payment) {
          var categoryName = "";
          count = count + 1;
          rowName = "row-" + count;

          _.each(payment.inventory_billable_categories, function (cat, i) {
            if (i == 0) {
              categoryName = cat.name;
            } else {
              categoryName += ", " + cat.name;
            }
          });

          if (payment.payment_type == "flatRate") {
            paymentValue = "$" + (payment.flat_rate / 100).toFixed(2);
          } else if (payment.payment_type == "percentOfTotal") {
            paymentValue = payment.percent_of_total + "%";
          } else {
            paymentValue = "";
          }

          if (payment.before_after == "before") {
            paymentDueDate = payment.due_date + " days before";
          } else {
            paymentDueDate = payment.due_date + " days after";
          }

          container.myTable.makeRow(rowName, [
            categoryName,
            payment.payment_type_name,
            paymentValue,
            paymentDueDate,
            payment.invoice_type.invoice_type,
            "",
          ]);

          container.myTable.body[rowName].btns
            .makeNode("editBtn", "button", {
              text: '<i class="fa fa-pencil"></i> Edit',
              css: "pda-btn-orange pda-btn-fullWidth",
            })
            .notify("click", {
              type: "paymentMethodRun",
              data: {
                run: function (payment) {
                  var dom = this;

                  getDataForForm(function (data) {
                    preparePaymentTemplateForm(dom, data, payment);
                  });
                }.bind(container, payment),
              },
            });

          container.myTable.body[rowName].btns
            .makeNode("deleteBtn", "button", {
              text: '<i class="fa fa-times"></i> Delete',
              css: "pda-btn-red pda-btn-fullWidth",
            })
            .notify("click", {
              type: "paymentMethodRun",
              data: {
                run: function (payment, paymentTemplates) {
                  var domObj = this;

                  sb.dom.alerts.ask(
                    {
                      title: "Are you sure?",
                      text: "",
                    },
                    function (resp) {
                      if (resp) {
                        paymentTemplates.splice(
                          _.indexOf(
                            paymentTemplates,
                            _.findWhere(paymentTemplates, { id: payment.id })
                          ),
                          1
                        );

                        paymentTemplate.templates = paymentTemplates;

                        sb.data.db.obj.update(
                          "payment_schedule_template",
                          paymentTemplate,
                          function (data) {
                            singlePaymentSchedule(data, domObj);
                          },
                          1
                        );

                        sb.dom.alerts.alert(
                          "Complete",
                          "Payment Schedule Deleted",
                          "success"
                        );
                      }
                    }
                  );
                }.bind(container, payment, paymentTemplates),
              },
            });
        },
        this
      );
    }

    function radioOptionsChanged(data, domObj) {
      var formData = data.process();

      $(domObj.body.paymentScheduleForm.percent_of_total.selector).hide();
      $(domObj.body.paymentScheduleForm.flat_rate.selector).hide();

      if (formData.fields.payment_type) {
        if (formData.fields.payment_type.value == "percentOfTotal") {
          $(domObj.body.paymentScheduleForm.flat_rate.selector).hide();
          $(domObj.body.paymentScheduleForm.percent_of_total.selector).show();
        } else if (formData.fields.payment_type.value == "flatRate") {
          $(domObj.body.paymentScheduleForm.percent_of_total.selector).hide();
          $(domObj.body.paymentScheduleForm.flat_rate.selector).show();
        } else {
          $(domObj.body.paymentScheduleForm.percent_of_total.selector).hide();
          $(domObj.body.paymentScheduleForm.flat_rate.selector).hide();
        }
      }
    }

    count = 0;
    paymentTemplate = paymentTemplateObject;

    // add id to each template object in array
    _.each(paymentTemplate.templates, function (template) {
      count += 1;
      template.id = count;
    });

    domObj.empty();

    domObj.makeNode("panel", "container", {}).makeNode("body", "container", {});

    domObj.makeNode("modal", "modal", {});

    domObj.panel.body.makeNode("btns", "buttonGroup", { css: "pull-right" });

    domObj.panel.body.btns
      .makeNode("edit", "button", {
        text: '<i class="fa fa-pencil"></i> Edit Name',
        css: "pda-btn-orange",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function (obj) {
              domObj.empty();

              domObj.makeNode("btns", "buttonGroup", { css: "pull-right" });

              domObj.makeNode("title", "headerText", {
                text: "Editing " + obj.name,
              });

              domObj.makeNode("break", "lineBreak", {});

              domObj.makeNode("cont", "container", { css: "pda-container" });

              domObj.cont.makeNode("form", "form", {
                name: {
                  name: "name",
                  label: "Payment Template Name",
                  type: "text",
                  value: obj.name,
                },
              });

              domObj.btns
                .makeNode("back", "button", {
                  text: '<i class="fa fa-arrow-left"></i> Cancel',
                  css: "pda-btnOutline-red",
                })
                .notify(
                  "click",
                  {
                    type: "paymentMethodRun",
                    data: {
                      run: function (obj) {
                        singlePaymentSchedule(obj, this);
                      }.bind(domObj, obj),
                    },
                  },
                  sb.moduleId
                );

              domObj.btns
                .makeNode("save", "button", {
                  text: '<i class="fa fa-check"></i> Save',
                  css: "pda-btn-green",
                })
                .notify(
                  "click",
                  {
                    type: "paymentMethodRun",
                    data: {
                      run: function (obj) {
                        if (this.cont.form.process().completed == false) {
                          sb.dom.alerts.alert(
                            "Error",
                            "Please fill out the whole form",
                            "error"
                          );
                          return;
                        }

                        this.btns.save.loading();
                        this.btns.back.loading();

                        var domObj = this;

                        sb.data.db.obj.update(
                          "payment_schedule_template",
                          {
                            id: obj.id,
                            name: this.cont.form.process().fields.name.value,
                          },
                          function (updated) {
                            singlePaymentSchedule(updated, domObj);
                          },
                          1
                        );
                      }.bind(domObj, obj),
                    },
                  },
                  sb.moduleId
                );

              domObj.patch();
            }.bind(domObj, paymentTemplate),
          },
        },
        sb.moduleId
      );

    domObj.panel.body.btns
      .makeNode("button", "button", {
        text: '<i class="fa fa-plus"></i> Create New Invoice',
        css: "pda-align-right pda-btn-green",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function (template) {
              var domObj = this;

              domObj.panel.body.btns.button.loading();

              // empty modal
              domObj.modal.body.empty();
              domObj.modal.footer.empty();

              // make title
              domObj.modal.body.makeNode("header", "headerText", {
                size: "small",
                css: "text-center",
                text: "Create New Payment",
              });

              getDataForForm(function (formData) {
                preparePaymentTemplateForm(domObj, formData);
              });
            }.bind(domObj, paymentTemplateObject),
          },
        },
        sb.moduleId
      );

    domObj.panel.body.makeNode("title", "headerText", {
      text: paymentTemplateObject.name,
    });

    domObj.panel.body.makeNode("break", "lineBreak", {});

    domObj.panel.body.makeNode("tableTitle", "headerText", {
      text: "Invoice Template List",
      size: "x-small",
    });

    preparePaymentTemplateTable(domObj, paymentTemplate.templates);

    domObj.patch();

    getDataForForm(function (formData) {
      dataForForm = formData;
    });
  }

  function startPaymentFlow(setup, buttonSetup) {
    this.empty();

    if (setup.price == 0) {
      this.makeNode("text", "div", {
        text: "This invoice does not need a payment.",
        css: "ui large centered info",
      });
    } else {
      if (!buttonSetup.oneSource) {
        this.makeNode("pay", "text", { text: "Loading payment methods..." });

        this.patch();

        this.css("");
      }

      var dom = this;

      dom.makeNode("payment", "div", { css: "" });
      dom.payment.makeNode("header", "div", { css: "row" });
      dom.payment.header.makeNode("lcol", "div", {
        css: "sixteen wide column",
      });
      dom.payment.header.lcol.makeNode("title", "headerText", {
        css: "",
        text: "Make Payment",
      });
      dom.payment.header.makeNode("rcol", "div", { css: "eight wide column" });
      //dom.payment.makeNode('divi', 'div', {css: 'ui divided header'});
      dom.payment.makeNode("bodhead", "div", { css: "sixteen wide column" });
      dom.payment.makeNode("body", "div", { css: "sixteen wide column" });

      sb.data.db.controller(
        "getObjectById&api_webform=true&pagodaAPIKey=" + appConfig.instance,
        { value: setup.customerId, type: "contacts" },
        function (contactObj) {
          sb.data.db.obj.getAll("invoice_fees", function (feesList) {
            var fees = feesList[0];
            if (fees) {
              txFeePercent = fees.credit_card_percent;
              txFeeFlat = fees.credit_card_flat_fee;
            }

            if (contactObj) {
              if (contactObj.hasOwnProperty("stripe_id")) {
                sb.data.db.controller(
                  "getStripeCustomer&api_webform=true&pagodaAPIKey=" +
                    appConfig.instance,
                  { stripeId: contactObj.stripe_id, contactId: contactObj.id },
                  function (customer) {
                    delete dom.pay;

                    if (!customer) {
                      var customerId = setup.customerId;
                    } else {
                      var customerId = customer.id;
                    }

                    /*
								dom.buttons.makeNode('createBankButton', 'button', {css:'pda-btnOutline-green', text:'<i class="fa fa-plus"></i> Add Bank Account'}).notify('click', {
									type:'paymentMethodRun',
									data:{
										run:createNewMethod.bind(dom, setup, buttonSetup, customer, contactObj, 'bank_account')
									}
								}, sb.moduleId);
			*/

                    /*
								if(setup.admin === true){
									dom.payment.bodhead.makeNode('enterPayment', 'div', {text:'<i class="fa fa-plus"></i> Enter check or cash payment', css:'ui green button'}).notify('click', {
										type:'invoicesRun',
										data:{
											run:enterManualPayment.bind(dom, setup, buttonSetup, customer, contactObj)
										}
									}, sb.moduleId);
								}
	*/
                    if (!buttonSetup.oneSource) {
                      if (!customer) {
                        dom.payment.body.makeNode("noItems", "headerText", {
                          text: "No Payment Methods",
                          size: "x-small",
                          css: "",
                        });
                      } else {
                        dom.payment.body.makeNode("cardBreak1", "div", {
                          text: "<br />",
                        });
                        dom.payment.body.makeNode("cards", "div", {
                          css: "green ui three cards",
                        });
                        dom.payment.body.makeNode("cardBreak2", "div", {
                          text: "<br />",
                        });

                        _.each(customer.sources.data, function (card) {
                          var defaultSource = false,
                            brand = '<i class="credit card icon"></i>';
                          if (customer.default_source == card.id) {
                            defaultSource = true;
                          }

                          dom.payment.body.cards
                            .makeNode("card-" + card.id, "div", {
                              css: "raised link card",
                            })
                            .makeNode("cont", "div", { css: "content" });

                          switch (card.object) {
                            case "bank_account":
                              if (defaultSource === false) {
                                defaultSource = "";
                              } else {
                                //defaultSource = ' Default';
                              }
                              console.log("card", card);
                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("brand", "div", {
                                text: "<small>Bank:</small> " + card.bank_name,
                                css: "",
                              });
                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("cardholder", "div", {
                                text: "<small>Last 4:</small> " + card.last4,
                                css: "",
                              });
                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("card", "div", {
                                text:
                                  "<small>Routing Number:</small> " +
                                  card.routing_number,
                                css: "",
                              });

                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("btns", "div", {
                                css: "mini ui fluid buttons",
                              });

                              /*
													if(defaultSource === ''){

														dom.payment.body.cards['card-'+card.id].cont.btns.makeNode('default', 'div', {text:'Make Default', css:'ui teal button'}).notify('click', {
															type:'paymentMethodRun',
															data:{
																run:makeDefault.bind(dom, card, customer, setup, buttonSetup)
															}
														}, sb.moduleId);

													}
		*/

                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.btns
                                .makeNode("pay", "div", {
                                  text: "Use This Account",
                                  css: "ui green button",
                                })
                                .notify(
                                  "click",
                                  {
                                    type: "paymentMethodRun",
                                    data: {
                                      run: payWithCard.bind(
                                        dom,
                                        card,
                                        customer,
                                        setup,
                                        buttonSetup
                                      ),
                                    },
                                  },
                                  sb.moduleId
                                );

                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.btns
                                .makeNode("delete", "div", {
                                  text: '<i class="fa fa-trash-o"></i>',
                                  css: "ui basic red button",
                                })
                                .notify(
                                  "click",
                                  {
                                    type: "paymentMethodRun",
                                    data: {
                                      run: deleteMethod.bind(
                                        dom,
                                        card,
                                        customer,
                                        setup,
                                        buttonSetup
                                      ),
                                    },
                                  },
                                  sb.moduleId
                                );

                              break;

                            default:
                              switch (card.brand) {
                                case "Visa":
                                  brand =
                                    '<i class="credit card icon"></i> ' +
                                    card.brand;

                                  break;

                                default:
                                  brand =
                                    '<i class="credit card icon"></i> ' +
                                    card.brand;
                              }

                              if (defaultSource === false) {
                                defaultSource = "";
                              } else {
                                //defaultSource = ' Default';
                              }
                              console.log("card", card);
                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("brand", "div", {
                                text: brand,
                                css: "",
                              });
                              //dom.payment.body.cards['card-'+card.id].cont.makeNode('cardholder', 'div', {text:'<small>Card holder:</small> '+ contactObj.fname +' '+ contactObj.lname, css:''});
                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("card", "div", {
                                text: "<small>Last 4:</small> " + card.last4,
                                css: "",
                              });
                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("exp", "div", {
                                text:
                                  "<small>Exp:</small> " +
                                  card.exp_month +
                                  "/" +
                                  card.exp_year,
                                css: "",
                              });

                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("btnBreak", "div", {
                                text: "<br />",
                              });

                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.makeNode("btns", "div", {
                                css: "mini ui fluid buttons",
                              });

                              /*
													if(defaultSource === ''){

														dom.payment.body.cards['card-'+card.id].cont.btns.makeNode('default', 'div', {text:'Make Default', css:'ui teal button'}).notify('click', {
															type:'paymentMethodRun',
															data:{
																run:makeDefault.bind(dom, card, customer, setup, buttonSetup)
															}
														}, sb.moduleId);

													}
		*/

                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.btns
                                .makeNode("pay", "div", {
                                  text: "Use This Card",
                                  css: "ui green button",
                                })
                                .notify(
                                  "click",
                                  {
                                    type: "paymentMethodRun",
                                    data: {
                                      run: payWithCard.bind(
                                        dom,
                                        card,
                                        customer,
                                        setup,
                                        buttonSetup
                                      ),
                                    },
                                  },
                                  sb.moduleId
                                );

                              dom.payment.body.cards[
                                "card-" + card.id
                              ].cont.btns
                                .makeNode("delete", "div", {
                                  text: "Delete",
                                  css: "ui basic red button",
                                })
                                .notify(
                                  "click",
                                  {
                                    type: "paymentMethodRun",
                                    data: {
                                      run: deleteMethod.bind(
                                        dom,
                                        card,
                                        customer,
                                        setup,
                                        buttonSetup
                                      ),
                                    },
                                  },
                                  sb.moduleId
                                );
                          }
                        });
                      }
                    }

                    dom.payment.body
                      .makeNode("createButton", "div", {
                        css: "ui green button",
                        text: "Credit/Debit Card",
                      })
                      .notify(
                        "click",
                        {
                          type: "paymentMethodRun",
                          data: {
                            run: createNewMethod.bind(
                              dom,
                              setup,
                              contactObj,
                              customer,
                              "card",
                              function (token) {
                                if (buttonSetup.oneSource === true) {
                                  payWithCard.call(
                                    dom,
                                    token,
                                    customer,
                                    setup,
                                    buttonSetup
                                  );
                                } else {
                                  startPaymentFlow.call(
                                    dom,
                                    setup,
                                    buttonSetup
                                  );
                                }
                              }
                            ),
                          },
                        },
                        sb.moduleId
                      );

                    // dom.payment.body
                    //   .makeNode("bankButton", "div", {
                    //     css: "ui green button",
                    //     text: "Bank Account",
                    //   })
                    //   .notify(
                    //     "click",
                    //     {
                    //       type: "paymentMethodRun",
                    //       data: {
                    //         run: function () {
                    //           function startPlaid() {
                    //             $.getScript(
                    //               "https://cdn.plaid.com/link/v2/stable/link-initialize.js",
                    //               function (data, textStatus, jqxhr) {
                    //                 var linkHandler = Plaid.create({
                    //                   env: plaidEnv,
                    //                   clientName: appConfig.systemName,
                    //                   key: plaidKey,
                    //                   product: ["auth"],
                    //                   selectAccount: true,
                    //                   onSuccess: function (
                    //                     public_token,
                    //                     metadata
                    //                   ) {
                    //                     dom.payment.body.bankButton.loading();

                    //                     sb.data.db.controller(
                    //                       "getPlaidBankAccount",
                    //                       {
                    //                         pagodaAPIKey: appConfig.instance,
                    //                         publicToken: public_token,
                    //                         accountId: metadata.account_id,
                    //                         customer: customer.id,
                    //                       },
                    //                       function (ret) {
                    //                         if (
                    //                           buttonSetup.oneSource === true
                    //                         ) {
                    //                           payWithCard.call(
                    //                             dom,
                    //                             public_token,
                    //                             customer,
                    //                             setup,
                    //                             buttonSetup
                    //                           );
                    //                         } else {
                    //                           startPaymentFlow.call(
                    //                             dom,
                    //                             setup,
                    //                             buttonSetup
                    //                           );
                    //                         }
                    //                       },
                    //                       sb.url +
                    //                         "/api/_getAdmin.php?pagodaAPIKey=" +
                    //                         appConfig.instance +
                    //                         "&do="
                    //                     );
                    //                   },
                    //                   onExit: function (err, metadata) {
                    //                     // The user exited the Link flow.
                    //                     if (err != null) {
                    //                       // The user encountered a Plaid API error prior to exiting.
                    //                     }
                    //                   },
                    //                 });

                    //                 linkHandler.open();
                    //               }
                    //             );
                    //           }

                    //           if (
                    //             appConfig.instance != "infinity" &&
                    //             appConfig.instance != "nlp" &&
                    //             appConfig.instance != "rickyvoltz" &&
                    //             appConfig.instance != "voltzsoftware"
                    //           ) {
                    //             startPlaid();
                    //           } else {
                    //             dom.payment.body.makeNode("title", "div", {
                    //               css: "ui small header",
                    //               text: "Can you log into your bank account or will you use your routing and account number?",
                    //             });

                    //             dom.payment.body
                    //               .makeNode("plaid", "div", {
                    //                 css: "ui blue button",
                    //                 text: "Login to Your Bank Account",
                    //               })
                    //               .notify(
                    //                 "click",
                    //                 {
                    //                   type: "paymentMethodRun",
                    //                   data: {
                    //                     run: function () {
                    //                       dom.payment.body
                    //                         .makeNode("achContainer", "div", {})
                    //                         .patch();

                    //                       startPlaid();
                    //                     },
                    //                   },
                    //                 },
                    //                 sb.moduleId
                    //               );

                    //             dom.payment.body.makeNode(
                    //               "lb",
                    //               "lineBreak",
                    //               {}
                    //             );

                    //             if (
                    //               appConfig.instance == "rickyvoltz" ||
                    //               appConfig.instance == "voltzsoftware"
                    //             ) {
                    //               dom.payment.body
                    //                 .makeNode("ach", "div", {
                    //                   css: "ui blue button",
                    //                   text: "Use Routing and Account Numbers",
                    //                 })
                    //                 .notify(
                    //                   "click",
                    //                   {
                    //                     type: "paymentMethodRun",
                    //                     data: {
                    //                       run: function () {
                    //                         if (
                    //                           appConfig.instance == "infinity"
                    //                         ) {
                    //                           dom.payment.body.makeNode(
                    //                             "emailtext",
                    //                             "div",
                    //                             {
                    //                               css: "ui small header",
                    //                               text: "We will send a secure invoice to your inbox for direct ACH processing. Please enter the email address you would like us to send the invoice to.",
                    //                             }
                    //                           );

                    //                           dom.payment.body.makeNode(
                    //                             "form",
                    //                             "form",
                    //                             {
                    //                               email: {
                    //                                 name: "email",
                    //                                 label: "Email Address",
                    //                                 type: "text",
                    //                               },
                    //                             }
                    //                           );

                    //                           dom.payment.body.makeNode(
                    //                             "break",
                    //                             "div",
                    //                             { text: "<br />" }
                    //                           );

                    //                           dom.payment.body
                    //                             .makeNode("send", "div", {
                    //                               css: "ui green button",
                    //                               text: "Send Secure Invoice",
                    //                             })
                    //                             .notify(
                    //                               "click",
                    //                               {
                    //                                 type: "paymentMethodRun",
                    //                                 data: {
                    //                                   run: function () {
                    //                                     var emailAddress =
                    //                                       dom.payment.body.form.process()
                    //                                         .fields.email.value;

                    //                                     if (!emailAddress) {
                    //                                       sb.dom.alerts.alert(
                    //                                         "Enter an email address."
                    //                                       );
                    //                                       return;
                    //                                     }

                    //                                     dom.payment.body.send.loading();

                    //                                     sb.data.db.controller(
                    //                                       "getObjectById&api_webform=true&pagodaAPIKey=" +
                    //                                         appConfig.instance,
                    //                                       {
                    //                                         value:
                    //                                           setup.customerId,
                    //                                         type: "contacts",
                    //                                       },
                    //                                       function (contact) {
                    //                                         var invoiceId =
                    //                                           setup.invoiceId;
                    //                                         if (
                    //                                           _.isArray(
                    //                                             setup.invoiceId
                    //                                           )
                    //                                         ) {
                    //                                           invoiceId =
                    //                                             setup
                    //                                               .invoiceId[0]
                    //                                               .id;
                    //                                         }

                    //                                         sb.data.db.controller(
                    //                                           "getObjectById&api_webform=true&pagodaAPIKey=" +
                    //                                             appConfig.instance,
                    //                                           {
                    //                                             value:
                    //                                               invoiceId,
                    //                                             type: "invoices",
                    //                                           },
                    //                                           function (
                    //                                             invoice
                    //                                           ) {
                    //                                             sb.data.db.controller(
                    //                                               "getObjectById&api_webform=true&pagodaAPIKey=" +
                    //                                                 appConfig.instance,
                    //                                               {
                    //                                                 value:
                    //                                                   invoice.related_object,
                    //                                                 type: "proposals",
                    //                                               },
                    //                                               function (
                    //                                                 proposal
                    //                                               ) {
                    //                                                 sb.data.db.controller(
                    //                                                   "getObjectById&api_webform=true&pagodaAPIKey=" +
                    //                                                     appConfig.instance,
                    //                                                   {
                    //                                                     value:
                    //                                                       proposal.main_object,
                    //                                                     type: "groups",
                    //                                                   },
                    //                                                   function (
                    //                                                     project
                    //                                                   ) {
                    //                                                     var URL =
                    //                                                       sb.data.url.createPageURL(
                    //                                                         "object-view",
                    //                                                         {
                    //                                                           id: project.id,
                    //                                                           name: project.name,
                    //                                                           type: "project",
                    //                                                         }
                    //                                                       );

                    //                                                     var emailBody =
                    //                                                       "<h2>Request for ACH Invoice</h2><br />" +
                    //                                                       "<b>Requested By:</b> " +
                    //                                                       contact.fname +
                    //                                                       " " +
                    //                                                       contact.lname +
                    //                                                       "<br />" +
                    //                                                       "<b>Send To:</b> " +
                    //                                                       emailAddress +
                    //                                                       "<br />" +
                    //                                                       "<b>Event:</b> " +
                    //                                                       project.name +
                    //                                                       "<br />" +
                    //                                                       "<b>Event #:</b> " +
                    //                                                       project.id +
                    //                                                       "<br />" +
                    //                                                       "<b>Event Date:</b> " +
                    //                                                       project.start_date +
                    //                                                       "<br />" +
                    //                                                       "<b>Invoice #:</b> " +
                    //                                                       invoice.id +
                    //                                                       "<br />" +
                    //                                                       "<b>Invoice Amount: $</b>" +
                    //                                                       (
                    //                                                         invoice.amount /
                    //                                                         100
                    //                                                       ).formatMoney() +
                    //                                                       "<br /><br />" +
                    //                                                       '<a href="' +
                    //                                                       URL +
                    //                                                       '">View Online</a>';

                    //                                                     var emailObj =
                    //                                                       {
                    //                                                         newThread: true,
                    //                                                         to: [
                    //                                                           "<EMAIL>",
                    //                                                         ],
                    //                                                         from: appConfig.emailFrom,
                    //                                                         subject:
                    //                                                           "Request for ACH Invoice Project #" +
                    //                                                           project.object_uid,
                    //                                                         mergevars:
                    //                                                           {
                    //                                                             TITLE:
                    //                                                               "Request for ACH Invoice",
                    //                                                             BODY: emailBody,
                    //                                                             BUTTON:
                    //                                                               "",
                    //                                                           },
                    //                                                         emailtags:
                    //                                                           [
                    //                                                             "Infinity ACH Request Email",
                    //                                                           ],
                    //                                                         type: "notification",
                    //                                                         typeId:
                    //                                                           setup.customerId,
                    //                                                       };

                    //                                                     sb.comm.sendEmail(
                    //                                                       emailObj,
                    //                                                       function (
                    //                                                         response
                    //                                                       ) {
                    //                                                         sb.dom.alerts.alert(
                    //                                                           "Success!",
                    //                                                           "A secured invoice for bank account payment will be sent to you in the next 24 hours. For questions regarding payment, <NAME_EMAIL> (You can safely close this window now.)",
                    //                                                           "success"
                    //                                                         );

                    //                                                         if (
                    //                                                           buttonSetup.refreshAction
                    //                                                         ) {
                    //                                                           buttonSetup.refreshAction();
                    //                                                         }
                    //                                                       }
                    //                                                     );
                    //                                                   }
                    //                                                 );
                    //                                               }
                    //                                             );
                    //                                           }
                    //                                         );
                    //                                       }
                    //                                     );
                    //                                   },
                    //                                 },
                    //                               },
                    //                               sb.moduleId
                    //                             );

                    //                           dom.payment.body.patch();
                    //                         } else {
                    //                           dom.payment.body.makeNode(
                    //                             "achContainer",
                    //                             "div",
                    //                             {}
                    //                           );
                    //                           var formArgs = {
                    //                             routing_number: {
                    //                               name: "routing_number",
                    //                               type: "text",
                    //                               label: "Routing Number",
                    //                             },
                    //                             account_number: {
                    //                               name: "account_number",
                    //                               type: "text",
                    //                               label: "Account Number",
                    //                             },
                    //                             account_holder_name: {
                    //                               name: "account_holder_name",
                    //                               type: "text",
                    //                               label: "Account Holder Name",
                    //                             },
                    //                             account_verification_email: {
                    //                               name: "account_verification_email",
                    //                               type: "text",
                    //                               label:
                    //                                 "Email for Account Verification",
                    //                             },
                    //                             account_holder_type: {
                    //                               name: "account_holder_type",
                    //                               type: "select",
                    //                               label: "Account Holder Type",
                    //                               options: [
                    //                                 {
                    //                                   name: "Company",
                    //                                   value: "company",
                    //                                 },
                    //                                 {
                    //                                   name: "Individual",
                    //                                   value: "individual",
                    //                                 },
                    //                               ],
                    //                               value: "company",
                    //                             },
                    //                           };

                    //                           dom.payment.body.achContainer.makeNode(
                    //                             "lb_preForm",
                    //                             "lineBreak",
                    //                             {}
                    //                           );

                    //                           dom.payment.body.achContainer.makeNode(
                    //                             "achForm",
                    //                             "form",
                    //                             formArgs
                    //                           );

                    //                           dom.payment.body.achContainer.makeNode(
                    //                             "formErrors",
                    //                             "div",
                    //                             {}
                    //                           );

                    //                           dom.payment.body.achContainer.makeNode(
                    //                             "lb_preBtns",
                    //                             "lineBreak",
                    //                             {}
                    //                           );

                    //                           dom.payment.body.achContainer.makeNode(
                    //                             "btns",
                    //                             "div",
                    //                             {}
                    //                           );

                    //                           dom.payment.body.achContainer.btns
                    //                             .makeNode("submit", "button", {
                    //                               text: '<i class="fa fa-circle-o-notch fa-spin"></i> Submit',
                    //                               css: "pda-btn-primary",
                    //                             })
                    //                             .notify(
                    //                               "click",
                    //                               {
                    //                                 type: "paymentMethodRun",
                    //                                 data: {
                    //                                   run: saveNewACHMethod.bind(
                    //                                     dom,
                    //                                     stripe,
                    //                                     contactObj
                    //                                   ),
                    //                                 },
                    //                               },
                    //                               sb.moduleId
                    //                             );

                    //                           dom.payment.body.achContainer.btns
                    //                             .makeNode("cancel", "button", {
                    //                               text: '<i class="fa fa-times"></i> Cancel',
                    //                               css: "pda-btnOutline-red",
                    //                             })
                    //                             .notify(
                    //                               "click",
                    //                               {
                    //                                 type: "paymentMethodRun",
                    //                                 data: {
                    //                                   run: startPaymentFlow.bind(
                    //                                     dom,
                    //                                     setup,
                    //                                     buttonSetup
                    //                                   ),
                    //                                 },
                    //                               },
                    //                               sb.moduleId
                    //                             );

                    //                           dom.payment.body.patch();
                    //                         }
                    //                       },
                    //                     },
                    //                     sb.moduleId
                    //                   );
                    //             }

                    //             dom.payment.body.patch();
                    //           }
                    //         },
                    //       },
                    //     },
                    //     sb.moduleId
                    //   );

                    if (
                      appConfig.instance == "infinity" ||
                      appConfig.instance == "nlp"
                    ) {
                      txFeePercent = 3;
                    }

                    dom.payment.body.makeNode("feeDisclaimer", "div", {
                      text:
                        "When making a credit card payment a " +
                        txFeePercent +
                        "% processing fee will apply.",
                    });

                    dom.patch();
                  },
                  sb.url + "/api/_getAdmin.php?do="
                );
              } else {
                delete dom.loading;

                dom.empty();

                dom.makeNode("payment", "div", { css: "ui centered grid" });
                dom.payment.makeNode("header", "div", { css: "row" });
                dom.payment.header.makeNode("lcol", "div", {
                  css: "sixteen wide column",
                });
                dom.payment.header.lcol.makeNode("title", "headerText", {
                  css: "",
                  text: "Make Payment",
                });
                dom.payment.header.makeNode("rcol", "div", {
                  css: "eight wide column",
                });
                //dom.payment.makeNode('divi', 'div', {css: 'ui divided header'});
                dom.payment.makeNode("bodhead", "div", {
                  css: "sixteen wide column",
                });
                dom.payment.makeNode("body", "div", {
                  css: "sixteen wide centered column",
                });

                dom.payment.body.makeNode("noItems", "headerText", {
                  text: "No Payment Methods",
                  size: "x-small",
                  css: "text-center",
                });

                dom.payment.body
                  .makeNode("card", "div", {
                    css: "ui green button",
                    text: "Add a card",
                  })
                  .notify(
                    "click",
                    {
                      type: "paymentMethodRun",
                      data: {
                        run: function (dom, setup, buttonSetup, contactObj) {
                          createNewMethod.call(
                            dom,
                            setup,
                            buttonSetup,
                            contactObj,
                            contactObj
                          );
                        }.bind({}, dom, setup, buttonSetup, contactObj),
                      },
                    },
                    sb.moduleId
                  );

                dom.patch();
              }
            } else {
              delete dom.loading;

              dom.empty();

              dom.makeNode("payment", "div", { css: "ui centered grid" });
              dom.payment.makeNode("header", "div", { css: "row" });
              dom.payment.header.makeNode("lcol", "div", {
                css: "eight wide column",
              });
              dom.payment.header.lcol.makeNode("title", "headerText", {
                css: "ui center aligned",
                text: "Make A Payment",
              });
              //dom.payment.header.makeNode('rcol', 'div', {css: 'eight wide column'});
              //dom.payment.makeNode('bodhead', 'div', {css: 'sixteen wide center aligned column'});
              dom.payment.makeNode("body", "div", {
                css: "sixteen wide center aligned column",
              });

              dom.payment.body.makeNode("noItems", "headerText", {
                text: "No Payment Methods",
                size: "x-small",
                css: "",
              });

              dom.makeNode("finalBreak", "lineBreak", {});

              dom.payment.body
                .makeNode("card", "div", {
                  css: "ui green button",
                  text: "Add a card",
                })
                .notify(
                  "click",
                  {
                    type: "paymentMethodRun",
                    data: {
                      run: function (dom, setup, buttonSetup, contactObj) {
                        createNewMethod.call(
                          dom,
                          setup,
                          buttonSetup,
                          contactObj,
                          contactObj
                        );
                      }.bind(
                        {},
                        dom,
                        setup,
                        buttonSetup,
                        contactObj,
                        contactObj
                      ),
                    },
                  },
                  sb.moduleId
                );

              dom.payment.body
                .makeNode("manual", "div", {
                  css: "ui green button",
                  text: "Enter Manual Payment",
                })
                .notify(
                  "click",
                  {
                    type: "paymentMethodRun",
                    data: {
                      run: function (dom, setup, buttonSetup, contactObj) {
                        enterManualPayment.call(
                          dom,
                          setup,
                          buttonSetup,
                          {},
                          contactObj
                        );
                      }.bind(
                        {},
                        dom,
                        setup,
                        buttonSetup,
                        contactObj,
                        contactObj
                      ),
                    },
                  },
                  sb.moduleId
                );

              dom.patch();

              //dom.css('ui blue segment');
            }
          });
        },
        sb.url + "/api/_getAdmin.php?do="
      );
    }

    this.patch();
  }

  return {
    init: function () {
      sb.listen({
        paymentMethodRun: this.run,
        "show-all-payments": this.showAll,
        "show-make-payment-button": this.startPaymentButton,
        "show-payment-button": this.showPaymentButton,
        "show-paymentMethod-button": this.start,
        "start-ach-verification": this.startACHVerification,
      });
    },

    destroy: function () {
      _.each(components, function (comp) {
        comp.destroy();
      });

      (domObj = {}), (components = {});
    },

    run: function (data) {
      data.run();
    },

    showAll: function (data) {
      domObj = sb.dom.make(data.domObj.selector);

      if (data.hasOwnProperty("objectId")) {
        objectId = data.objectId;
      }

      components.table = sb.createComponent("crud-table");

      ui = sb.dom.make(data.domObj.selector);

      tableUI = ui.makeNode("paymentMethods", "container", { uiGrid: false });
      tableUI.state = allUI;

      ui.build();

      tableUI.state();
      tableUI.state.show();
    },

    startACHVerification: function (data) {
      var instance = sb.data.url.getParams().i;
      appConfig.instance = instance;
      var hash = sb.data.url.getParams().hid;
      var obj = {
        hash: hash,
        instanceName: instance,
      };

      dom = sb.dom.make(".main");

      dom.makeNode("menu", "div", { css: "ui huge inverted stackable menu" });

      dom.menu.makeNode("title", "div", {
        css: "ui item",
        text: appConfig.systemName,
      });

      dom.makeNode("cont", "div", {
        css: "ui basic very padded loading segment",
      });

      dom.build();

      dom.cont.makeNode("verificationContainer", "div", {});

      dom.cont.patch();

      sb.data.db.setAPIPath("../../api/_getAdmin.php");

      sb.data.db.service(
        "StripeService",
        "getACHVerificationObj",
        obj,
        function (response) {
          var stripeAcctObj = response.bankAcct;

          dom.cont.loading(false);

          if (stripeAcctObj["status"] == "verified") {
            dom.cont.makeNode("verificationContainer", "div", {});
            dom.cont.patch();

            dom.cont.makeNode("verificationContainer", "div", {
              css: "ui success message",
              text:
                `
						<span class='ui small header'>Success!</span>
						<br/>
						Stripe was able to verify your ` +
                response["bankAcct"]["bank_name"] +
                ` account ending in ` +
                response["bankAcct"]["last4"] +
                `. To avoid any issues please wait 5-10 minutes before initiating your first payment using your new account.`,
            });
            dom.cont.patch();
          } else {
            dom.cont.verificationContainer.makeNode("greeting", "div", {
              css: "ui medium header",
              text: "Hello " + stripeAcctObj["account_holder_name"] + ",",
            });

            dom.cont.verificationContainer.makeNode("lb_1", "lineBreak", {
              spaces: 1,
            });

            dom.cont.verificationContainer.makeNode("instructions", "div", {
              css: "ui",
              text:
                `
						You have arrived at the account verification page for the Stripe ACH payment method associated with your <span style='font-weight:bold'>` +
                stripeAcctObj["bank_name"] +
                `</span> account ending in <span style='font-weight:bold'>` +
                stripeAcctObj["last4"] +
                `</span>.
						<br/>
						<br/>
						Before beginning this process make sure that you have identified the two micro deposits that have been placed into your account by Stripe.
						These two charges will have a description containing the phrase <span style='font-weight:bold'>'AMTS'</span> and should have appeared within 1-2 business days of you initiating the account verification process.
						Be aware that there is a limit of 3 failed verification attempts. If this limit is exceeded, you will need to contact the <a href='mailto: <EMAIL>?subject=Account Verification Assistance (` +
                stripeAcctObj["account_holder_name"] +
                `)' style='text-decoration:underline;' class='ui blue text'>Bento Support Team</a> to help you unlock your account
						<br/>
						<br/>
						Note: Please enter micro transaction values as whole numbers. For example, if your bank shows two deposits of $0.32 and $0.45, please enter the values as 32 and 45. The order in which the values are entered does not affect the verification process.
						<br/>
						<br/>
						If you are ready to begin, please fill out the form below:
						<br/>
						<br/>`,
            });

            var formArgs = {
              micro_value_1: {
                name: "micro_value_1",
                type: "usd",
                label: "Micro Deposit 1 Amount",
              },
              micro_value_2: {
                name: "micro_value_2",
                type: "usd",
                label: "Micro Deposit 2 Amount",
              },
              email: {
                name: "email",
                type: "text",
                label: "Email Address",
              },
            };

            dom.cont.verificationContainer.makeNode(
              "verificationForm",
              "form",
              formArgs
            );

            dom.cont.verificationContainer.makeNode("formErrors", "div", {});

            dom.cont.verificationContainer.makeNode(
              "lb_preBtns",
              "lineBreak",
              {}
            );

            dom.cont.verificationContainer.makeNode("btns", "div", {});

            dom.cont.verificationContainer.btns
              .makeNode("submit", "button", {
                text: '<i class="fa fa-circle-o-notch fa-spin"></i> Submit',
                css: "pda-btn-primary",
              })
              .notify(
                "click",
                {
                  type: "paymentMethodRun",
                  data: {
                    run: verifyACHMethod.bind(
                      dom,
                      stripeAcctObj["customer"],
                      stripeAcctObj["id"],
                      response
                    ),
                  },
                },
                sb.moduleId
              );

            dom.cont.verificationContainer.patch();
          }
        }
      );
    },

    showPaymentButton: function (data) {
      var invoices = data.invoices;
      console.log('data showPaymentButton', data);
      var proposal_id = data.proposalId;

      function chargeCard(
        invoices,
        customer,
        source,
        formInfo,
        ipAddress,
        callback,
        percentFee,
        flatFee,
        runningBalance,
        paymentObjs,
        count
      ) {
        if (!count) {
          count = 0;
        }

        if (!paymentObjs) {
          paymentObjs = [];
        }

        if (!runningBalance) {
          runningBalance = +formInfo.amount.value;
        }

        if (runningBalance > invoices[count].balance) {
          paymentAmount = invoices[count].balance;
          runningBalance = runningBalance - invoices[count].balance;
        } else {
          paymentAmount = runningBalance;
          runningBalance = 0;
        }

        paymentSchedule = calculatePaymentWithFees(
          parseFloat(paymentAmount / 100),
          percentFee,
          flatFee
        );

        sb.data.db.controller(
          "getObjectsWhere&api_webform=true&pagodaAPIKey=" + appConfig.instance,
          {
            objectType: "instances",
            queryObj: { instance: appConfig.instance },
          },
          function (instances) {
            sb.data.db.controller(
              "chargeStripeConnectCustomer&api_webform=true&pagodaAPIKey=" +
                appConfig.instance,
              {
                amount: paymentSchedule.total,
                customer: customer,
                sourceId: source.id,
                connectAccountId: instances[0].stripe_account_id,
              },
              function (response) {
                if (
                  response.status == "succeeded" ||
                  response.status == "pending"
                ) {
                  paymentObjs.push({
                    amount: paymentAmount,
                    details: response,
                    vendor_id: invoices[count].main_client,
                    invoice: invoices[count].id,
                    main_object: invoices[count].related_object,
                    main_client: invoices[count].main_client,
                    main_contact: invoices[count].main_contact,
                    owner: invoices[count].main_contact,
                    fee: paymentSchedule.fee,
                  });

                  if (!invoices[count].fees) {
                    invoices[count].fees = 0;
                  }

                  invoices[count].balance =
                    invoices[count].balance - paymentAmount;
                  invoices[count].paid = invoices[count].paid + paymentAmount;
                  invoices[count].fees += paymentSchedule.fee;

                  if (invoices[count].balance == 0) {
                    invoices[count].locked = "locked";
                  }

                  sb.data.db.controller(
                    "createNewObject&api_webform=true&pagodaAPIKey=" +
                      appConfig.instance,
                    { objectType: "payments", objectData: paymentObjs[count] },
                    function (newPayment) {
                      if (!invoices[count].payments) {
                        invoices[count].payments = [];
                      }

                      invoices[count].payments.push(newPayment.id);

                      sb.data.db.controller(
                        "updateObject&api_webform=true&pagodaAPIKey=" +
                          appConfig.instance,
                        { objectType: "invoices", objectData: invoices[count] },
                        function (updateInvoice) {
                          if (newPayment) {
                            count++;

                            if (invoices[count]) {
                              chargeCard(
                                invoices,
                                customer,
                                source,
                                formInfo,
                                ipAddress,
                                callback,
                                percentFee,
                                flatFee,
                                runningBalance,
                                paymentObjs,
                                fees,
                                count
                              );
                            } else {
                              callback(true);
                            }
                          }
                        },
                        sb.url + "/api/_getAdmin.php?do="
                      );
                    },
                    sb.url + "/api/_getAdmin.php?do="
                  );
                } else {
                  // payment was not successful
                  // show error message
                  alert(STRIPE_ERROR_MESSAGE);
                  // TODO: Handle unsuccessful payments.
                }
              },
              sb.url + "/api/_getAdmin.php?do="
            );
          },
          sb.url + "/api/_getAdmin.php?do="
        );
      }

      function enterPayment(ui, selectedInvoices, invoices) {
        var balanceToPay = _.reduce(
          selectedInvoices,
          function (memo, inv) {
            return memo + inv.balance;
          },
          0
        );

        ui.paymentCont.makeNode("div", "div", {
          css: "ui right floated raised secondary grey compact clearing segment",
        });

        ui.paymentCont.div.makeNode("header", "div", {
          css: "ui header",
          text: "Enter Payment for " + selectedInvoices.length + " invoices.",
        });
        ui.paymentCont.div.makeNode("subtitle", "div", {
          css: "ui subtitle",
          text: "Balance is: $" + (balanceToPay / 100).formatMoney(),
        });

        ui.paymentCont.div.makeNode("formCont", "div", {
          css: "ui collapsed basic segment",
        });

        ui.paymentCont.div.formCont.makeNode("form", "form", {
          amount: {
            name: "amount",
            label: "Payment Amount",
            type: "usd",
            value: balanceToPay,
          },
          date: {
            name: "date",
            label: "Payment Date",
            type: "date",
            value: moment(),
          },
          notes: {
            name: "notes",
            label: "Notes",
            type: "textbox",
            rows: 5,
          },
        });

        ui.paymentCont.div.makeNode("btns", "div", { css: "ui mini buttons" });
        ui.paymentCont.div.btns
          .makeNode("save", "div", { css: "ui green button", text: "Save" })
          .notify("click", {
            type: "paymentMethodRun",
            data: {
              run: function (selectedInvoices) {
                var totalInvoiceValue = 0;
                _.each(selectedInvoices, function (inv) {
                  totalInvoiceValue += inv.balance;
                });

                var formInfo =
                  ui.paymentCont.div.formCont.form.process().fields;

                if (+formInfo.amount.value > totalInvoiceValue) {
                  sb.dom.alerts.ask(
                    {
                      title: "Create a credit?",
                      text: "The payment value is greater than the invoice value. This will create a credit. Would you like to continue?",
                    },
                    function (resp) {
                      if (resp) {
                        swal.disableButtons();

                        ui.paymentCont.div.btns.save.loading();

                        sb.data.db.controller(
                          "getIPAddress&pagodaAPIKey=" + appConfig.instance,
                          {},
                          function (ip) {
                            var runningPaymentBalance = +formInfo.amount.value;
                            var paymentObjs = [];
                            var paymentAmount = 0;
                            var invCount = selectedInvoices.length;

                            _.each(
                              _.sortBy(selectedInvoices, "due_date"),
                              function (inv, count) {
                                if (runningPaymentBalance > 0) {
                                  if (
                                    runningPaymentBalance > inv.balance &&
                                    count + 1 < invCount
                                  ) {
                                    paymentAmount = inv.balance;
                                    runningPaymentBalance =
                                      runningPaymentBalance - inv.balance;
                                  } else {
                                    paymentAmount = runningPaymentBalance;
                                    runningPaymentBalance = 0;
                                  }

                                  paymentObjs.push({
                                    amount: paymentAmount,
                                    details: {
                                      payment_date: formInfo.date.value,
                                      notes: formInfo.notes.value,
                                      ip_address: ip,
                                    },
                                    vendor_id: inv.main_client,
                                    invoice: inv.id,
                                    main_object: inv.related_object,
                                    main_client: inv.main_client,
                                    main_contact: inv.main_contact,
                                    owner: appConfig.user.id,
                                    manual_payment: true,
                                    test_payment: false,
                                  });

                                  inv.balance = inv.balance - paymentAmount;
                                  inv.paid = inv.paid + paymentAmount;
                                  inv.locked = "locked";
                                }
                              }
                            );

                            sb.data.db.controller(
                              "recordPayment&api_web_form=true&pagodaAPIKey=" +
                                appConfig.instance,
                              paymentObjs,
                              function (newPayments) {
                                _.each(newPayments, function (payment) {
                                  if (
                                    !_.where(selectedInvoices, {
                                      id: payment.invoice,
                                    })[0].payments
                                  ) {
                                    _.where(selectedInvoices, {
                                      id: payment.invoice,
                                    })[0].payments = [];
                                  }

                                  _.where(selectedInvoices, {
                                    id: payment.invoice,
                                  })[0].payments.push(payment.id);
                                });

                                sb.data.db.obj.update(
                                  "invoices",
                                  selectedInvoices,
                                  function (updatedInvoices) {
                                    sb.data.db.obj.getById(
                                      "invoices",
                                      _.pluck(invoices, "id"),
                                      function (invoices) {
                                        printTable(invoices);

                                        swal.close();
                                      },
                                      null,
                                      false,
                                      true
                                    );
                                  }
                                );
                              }
                            );
                          }
                        );
                      } else {
                        swal.disableButtons();

                        ui.paymentCont.div.btns.save.loading();

                        sb.data.db.controller(
                          "getIPAddress&pagodaAPIKey=" + appConfig.instance,
                          {},
                          function (ip) {
                            var runningPaymentBalance = +formInfo.amount.value;
                            var paymentObjs = [];
                            var paymentAmount = 0;
                            var invCount = selectedInvoices.length;
                            var invoicesToUse = [];

                            if (
                              invCount == 1 &&
                              selectedInvoices[0].balance <
                                runningPaymentBalance
                            ) {
                              var otherInvoices = [];
                              otherInvoices.push(selectedInvoices[0]);

                              invoicesToUse = otherInvoices.concat(
                                _.reject(invoices, function (inv) {
                                  return inv.id == selectedInvoices[0].id;
                                })
                              );
                            } else {
                              invoicesToUse = selectedInvoices;
                            }

                            _.each(
                              _.sortBy(invoicesToUse, "due_date"),
                              function (inv, count) {
                                if (
                                  runningPaymentBalance > inv.balance &&
                                  count + 1 <= invCount
                                ) {
                                  paymentAmount = inv.balance;
                                  runningPaymentBalance =
                                    runningPaymentBalance - inv.balance;
                                } else {
                                  paymentAmount = runningPaymentBalance;
                                  runningPaymentBalance = 0;
                                }

                                paymentObjs.push({
                                  amount: paymentAmount,
                                  details: {
                                    payment_date: formInfo.date.value,
                                    notes: formInfo.notes.value,
                                    ip_address: ip,
                                  },
                                  vendor_id: inv.main_client,
                                  invoice: inv.id,
                                  main_object: inv.related_object,
                                  main_client: inv.main_client,
                                  main_contact: inv.main_contact,
                                  owner: appConfig.user.id,
                                  manual_payment: true,
                                  test_payment: false,
                                });

                                inv.balance = inv.balance - paymentAmount;
                                inv.paid = inv.paid + paymentAmount;
                                inv.locked = "locked";
                              }
                            );

                            sb.data.db.controller(
                              "recordPayment&api_web_form=true&pagodaAPIKey=" +
                                appConfig.instance,
                              paymentObjs,
                              function (newPayments) {
                                _.each(newPayments, function (payment) {
                                  if (
                                    !_.where(invoicesToUse, {
                                      id: payment.invoice,
                                    })[0].payments
                                  ) {
                                    _.where(invoicesToUse, {
                                      id: payment.invoice,
                                    })[0].payments = [];
                                  }

                                  _.where(invoicesToUse, {
                                    id: payment.invoice,
                                  })[0].payments.push(payment.id);
                                });

                                sb.data.db.obj.update(
                                  "invoices",
                                  invoicesToUse,
                                  function (updatedInvoices) {
                                    sb.data.db.obj.getById(
                                      "invoices",
                                      _.pluck(invoices, "id"),
                                      function (invoices) {
                                        printTable(invoices);

                                        swal.close();
                                      },
                                      null,
                                      false,
                                      true
                                    );
                                  }
                                );
                              }
                            );
                          }
                        );
                      }
                    }
                  );
                } else {
                  ui.paymentCont.div.btns.save.loading();

                  sb.data.db.controller(
                    "getIPAddress&pagodaAPIKey=" + appConfig.instance,
                    {},
                    function (ip) {
                      var runningPaymentBalance = +formInfo.amount.value;
                      var paymentObjs = [];
                      var paymentAmount = 0;
                      var invCount = selectedInvoices.length;

                      _.each(
                        _.sortBy(selectedInvoices, "due_date"),
                        function (inv, count) {
                          if (
                            runningPaymentBalance > inv.balance &&
                            count + 1 < invCount
                          ) {
                            paymentAmount = inv.balance;
                            runningPaymentBalance =
                              runningPaymentBalance - inv.balance;
                          } else {
                            paymentAmount = runningPaymentBalance;
                            runningPaymentBalance = 0;
                          }

                          paymentObjs.push({
                            amount: paymentAmount,
                            details: {
                              payment_date: formInfo.date.value,
                              notes: formInfo.notes.value,
                              ip_address: ip,
                            },
                            vendor_id: inv.main_client,
                            invoice: inv.id,
                            main_object: inv.related_object,
                            main_client: inv.main_client,
                            main_contact: inv.main_contact,
                            owner: appConfig.user.id,
                            manual_payment: true,
                            test_payment: false,
                          });

                          inv.balance = inv.balance - paymentAmount;
                          inv.paid = inv.paid + paymentAmount;
                          inv.locked = "locked";
                        }
                      );

                      sb.data.db.controller(
                        "recordPayment&api_web_form=true&pagodaAPIKey=" +
                          appConfig.instance,
                        paymentObjs,
                        function (newPayments) {
                          _.each(newPayments, function (payment) {
                            if (
                              !_.where(selectedInvoices, {
                                id: payment.invoice,
                              })[0].payments
                            ) {
                              _.where(selectedInvoices, {
                                id: payment.invoice,
                              })[0].payments = [];
                            }

                            _.where(selectedInvoices, {
                              id: payment.invoice,
                            })[0].payments.push(payment.id);
                          });

                          sb.data.db.obj.update(
                            "invoices",
                            selectedInvoices,
                            function (updatedInvoices) {
                              sb.data.db.obj.getById(
                                "invoices",
                                _.pluck(invoices, "id"),
                                function (invoices) {
                                  printTable(invoices);
                                },
                                null,
                                false,
                                true
                              );
                            }
                          );
                        }
                      );
                    }
                  );
                }
              }.bind({}, selectedInvoices),
            },
          });
        ui.paymentCont.div.btns
          .makeNode("cancel", "div", { css: "ui grey button", text: "Cancel" })
          .notify("click", {
            type: "paymentMethodRun",
            data: {
              run: function (invoices) {
                _.each(invoices, function (inv) {
                  $(ui.table.body["row-" + inv.id].selector).removeClass(
                    "active"
                  );
                });

                ui.paymentCont.empty();
                ui.paymentCont.patch();
              }.bind({}, invoices),
            },
          });

        ui.paymentCont.patch();
      }

      function takePayment(ui, selectedInvoices, invoices) {
        var balanceToPay = _.reduce(
          selectedInvoices,
          function (memo, inv) {
            return memo + inv.balance;
          },
          0
        );

        ui.paymentCont.makeNode("div", "div", {
          css: "ui raised clearing segment",
        });

        ui.paymentCont.div.makeNode("header", "div", {
          css: "ui header",
          text: "Enter Payment for " + selectedInvoices.length + " invoices.",
        });
        ui.paymentCont.div.makeNode("balance", "div", {
          css: "ui subtitle",
          text: "Balance is: $" + (balanceToPay / 100).formatMoney(),
        });
        if (
          appConfig.instance == "infinity" ||
          appConfig.instance == "nlp" ||
          appConfig.instance == "rickyvoltz"
        ) {
          ui.paymentCont.div.makeNode("fees", "div", {
            css: "ui subtitle",
            text: "<br>Credit/Debit card transactions come with a 3% processing fee.<br>eCheck transactions have no associated fees.",
          });
        }

        ui.paymentCont.div.makeNode("headerBreak", "div", { text: "<br />" });

        var stripePaymentSourcesUI = ui.paymentCont.div.makeNode(
          "sources",
          "div",
          { css: "ui loading basic segment" }
        );

        ui.paymentCont.patch();

        sb.data.db.obj.getById(
          "proposals",
          invoices[0].related_object,
          function (proposal) {
            sb.data.db.obj.getById(
              "groups",
              proposal.main_object,
              function (group) {
                sb.data.db.obj.getById(
                  "contacts",
                  group.main_contact,
                  function (contact) {
                    var paramObj = {
                      contactId: contact.id,
                      stripeId: contact.stripe_id,
                      verifiedSources: true,
                      instanceId: appConfig.id,
                    };

                    sb.data.db.service(
                      "StripeService",
                      "getStripeCustomer",
                      paramObj,
                      function (response) {
                        ui.paymentCont.div.sources.removeClass(
                          "ui loading basic segment"
                        );

                        if (!response.customer) {
                          ui.paymentCont.div.sources.makeNode(
                            "noItems",
                            "headerText",
                            {
                              text: "There was an error retrieving the Stripe payment sources for this contact.",
                              size: "xx-small",
                              css: "text-center",
                            }
                          );
                        } else {
                          sb.data.db.obj.getAll(
                            "invoice_fees",
                            function (feesList) {
                              sb.notify({
                                type: "view-field",
                                data: {
                                  type: "contact-payment-sources",
                                  property: "object",
                                  ui: stripePaymentSourcesUI,
                                  obj: response,
                                  options: {
                                    contactId: contact.id,
                                    initiatePayments: true,
                                    feesList: feesList,
                                    invoiceBalance: balanceToPay,
                                    instanceId: appConfig.id,
                                    eventId: invoices[0].related_object,
                                    selectedInvoices: selectedInvoices.sort(
                                      function (a, b) {
                                        return (
                                          new Date(a.due_date) -
                                          new Date(b.due_date)
                                        );
                                      }
                                    ),
                                    selectedInvoiceIds: selectedInvoices
                                      .sort(function (a, b) {
                                        return (
                                          new Date(a.due_date) -
                                          new Date(b.due_date)
                                        );
                                      })
                                      .map((inv) => inv.id),
                                    paymentForm: false,
                                  },
                                },
                              });

                              stripePaymentSourcesUI.patch();
                            }
                          );
                        }
                      }
                    );
                  }
                );
              }
            );
          }
        );
      }

      function printTable(invoices) {


        console.log('printTable', invoices);
        function getInvoiceName(inv) {
          var invName = inv.name;

          if (
            !_.isEmpty(inv.related_object) &&
            !_.isEmpty(inv.related_object.main_object)
          ) {
            invName += " for " + inv.related_object.main_object.name;
          }

          return invName;
        }

        var totalAmount = 0;
        var totalPaid = 0;
        var totalBalance = 0;
        var ui = data.dom.makeNode("seg", "div", {
          css: "ui basic clearing segment",
        });
        var paymentsList = [];

        data.dom.patch();

        ui.empty();

        ui.makeNode("loading", "div", { css: "ui basic padded segment" })
          .makeNode("loading", "div", { css: "ui active inverted dimmer" })
          .makeNode("loading", "div", {
            css: "ui text loader",
            text: "Loading",
          });

        ui.patch();

        if (invoices.length > 0) {

          sb.data.db.obj.getWhere(
            "payments",
            { main_object: invoices[0].related_object },
            function (otherPayments) {

              paymentsList = _.pluck(otherPayments, "id");

              ui.empty();
              ui.patch();


              ui.makeNode("header", "div", {
                css: "ui huge header",
                text: "Payment Schedule",
              });

              ui.makeNode('editmessage', 'div', {
                css: 'ui orange message',
                text: "* Click to manually edit Payment and Balance values"
              });

              ui.makeNode("table", "div", {
                tag: "table",
                css: "ui striped table",
              });
              ui.table.makeNode("headerRow", "div", { tag: "thead" });
              ui.table.headerRow.makeNode("name", "div", {
                tag: "th",
                text: "Name/Type",
              });
              ui.table.headerRow.makeNode("due", "div", {
                tag: "th",
                text: "Due Date",
              });
              ui.table.headerRow.makeNode("total", "div", {
                tag: "th",
                text: "Total",
                css: "right aligned"
              });
              ui.table.headerRow.makeNode("paid", "div", {
                tag: "th",
                text: "(*) Paid",
                css: "right aligned orange"
              });
              ui.table.headerRow.makeNode("balance", "div", {
                tag: "th",
                text: "(*) Balance",
                css: "right aligned orange"
              });
              ui.table.headerRow.makeNode("btns", "div", {
                tag: "th",
                text: "Actions",
              });

              ui.table.makeNode("body", "div", { tag: "tbody" });

              _.each(_.sortBy(invoices, "due_date"), function (inv) {

                console.log('CURRENT INVOICE ROW ITEM', inv);
                var invName = getInvoiceName(inv);
                var relatedId = inv.related_object;
                if (
                  typeof relatedId === "object" &&
                  !_.isEmpty(inv.related_object)
                ) {
                  relatedId = inv.related_object.id;
                }

                var rowColor = "warning";
                if (inv.balance == 0) {
                  rowColor = "positive";
                }

                console.group('Processing Invoice:', inv.id);
                console.log('Initial Invoice State:', {
                    name: inv.name,
                    amount: inv.amount / 100,
                    paid: inv.paid / 100,
                    balance: inv.balance / 100
                });

                // Add to total amount
                totalAmount += inv.amount;
                console.log('Running Total Amount:', totalAmount / 100);

                // For paid amount, only count non-returned payments
                var activePayments = _.filter(otherPayments, function(p) {
                    var isActive = _.contains(inv.payments, p.id) && p.status !== 'Returned';
                    console.log('Payment', p.id, 'active?', isActive, 'status:', p.status);
                    return isActive;
                });
                console.log('Active Payments:', _.pluck(activePayments, 'id'));

                var activePaidAmount = _.reduce(activePayments, function(sum, p) {
                    console.log('Adding payment amount:', p.amount / 100, 'to sum:', sum / 100);
                    return sum + p.amount;
                }, 0);
                console.log('Total Active Paid Amount:', activePaidAmount / 100);
                console.log('Database Paid Amount:', inv.paid / 100);

                // Use the actual values from the database instead of recalculating
                totalPaid += inv.paid;
                console.log('Running Total Paid:', totalPaid / 100);

                totalBalance += inv.balance;
                console.log('Running Total Balance:', totalBalance / 100);

                paymentsList = paymentsList.concat(inv.payments);
                console.groupEnd();

                // Create invoice row with data attributes for drag-drop
                ui.table.body.makeNode("row-" + inv.id, "div", {
                    tag: "tr",
                    css: rowColor + "ui segment",
                    style: "margin-bottom: 1em;"
                });


                ui.table.body["row-" + inv.id].makeNode("name", "div", {
                  tag: "td",
                  text: "#" + relatedId + "-" + inv.id + ": " + invName,
                });
                ui.table.body["row-" + inv.id].makeNode("due", "div", {
                  tag: "td",
                  text: moment(inv.due_date).format("MM/DD/YYYY"),
                });
                ui.table.body["row-" + inv.id].makeNode("total", "div", {
                  tag: "td",
                  text: "<nobr>$ " + (inv.amount / 100).formatMoney() + "</nobr>",
                  css: "right aligned"
                });
                console.log('[Invoice Row] Creating paid field:', {
                    invoiceId: inv.id,
                    paid: inv.paid,
                    total: inv.amount
                });
                var paidCell = ui.table.body["row-" + inv.id].makeNode("paid", "div", {
                    tag: "td"
                });

                sb.notify({
                    type: 'view-field',
                    data: {
                        type: 'currency',
                        property: 'paid',
                        obj: inv,
                        ui: paidCell,
                        options: {
                            editing: true,
                            commitUpdates: true,
                            alignRight: true,
                            onEditStart: function() {
                                console.log('[Paid Field] Started editing:', {
                                    invoiceId: inv.id,
                                    currentValue: inv.paid
                                });
                            },
                            onChange: function(newValue) {
                                console.log('[Paid Field] Value changing:', {
                                    invoiceId: inv.id,
                                    oldValue: inv.paid,
                                    newValue: newValue
                                });
                            },
                            onEditEnd: function() {
                                console.log('[Paid Field] Finished editing:', {
                                    invoiceId: inv.id,
                                    newValue: inv.paid
                                });
                            }
                        }
                    }
                });
                // Create balance cell with currency field
                console.log('[Invoice Row] Creating balance field:', {
                    invoiceId: inv.id,
                    balance: inv.balance,
                    total: inv.amount
                });
                var balanceCell = ui.table.body["row-" + inv.id].makeNode("balance", "div", {
                    tag: "td"
                });

                // Add payment IDs row after the invoice row
                if (inv.payments && inv.payments.length > 0) {
                    var paymentsRow = ui.table.body.makeNode(
                      "payments-row-" + inv.id,
                      "div",
                      { text: `<td colspan="6" style="border-top: 0; color: #aeaeae; font-size: .9em; max-width: 300px; overflow-wrap: break-word;">
                        <div style="display: flex; flex-wrap: wrap; gap: 8px;">` +
                        _.map(inv.payments, function(paymentId) {
                            var payment = _.findWhere(otherPayments, {id: paymentId});
                            if (!payment) return '';
                            var amountFormatted = (payment.amount / 100).formatMoney();
                            return `<div class="ui mini basic label payment-label-${inv.id}" style="margin: 0; display: inline-flex; align-items: center; gap: 4px;" data-payment-id="${paymentId}" data-invoice-id="${inv.id}">
                                <i class="hashtag icon" style="margin: 0;"></i>
                                <span style="margin-right: 4px;">${paymentId}</span>
                                <span class="detail" style="margin: 0;">$${amountFormatted}</span>
                            </div>`;
                        }).join('') +
                        `</div>
                      </td>` }
                    );

                }

                sb.notify({
                    type: 'view-field',
                    data: {
                        type: 'currency',
                        property: 'balance',
                        obj: inv,
                        ui: balanceCell,
                        options: {
                            editing: true,
                            commitUpdates: true,
                            alignRight: true,
                            onEditStart: function() {
                                console.log('[Balance Field] Started editing:', {
                                    invoiceId: inv.id,
                                    currentValue: inv.balance
                                });
                            },
                            onChange: function(newValue) {
                                console.log('[Balance Field] Value changing:', {
                                    invoiceId: inv.id,
                                    oldValue: inv.balance,
                                    newValue: newValue
                                });
                            },
                            onEditEnd: function() {
                                console.log('[Balance Field] Finished editing:', {
                                    invoiceId: inv.id,
                                    newValue: inv.balance
                                });
                            }
                        }
                    }
                });
                ui.table.body["row-" + inv.id].makeNode("btns", "div", {
                  tag: "td",
                  css: "",
                });
                ui.table.body["row-" + inv.id].btns.makeNode("btns", "div", {
                  css: "ui mini compact buttons",
                });

                if (inv.balance != 0) {
                  ui.table.body["row-" + inv.id].btns.btns
                    .makeNode("manual", "div", {
                      css: "ui teal button",
                      text: "Enter Payment",
                    })
                    .notify(
                      "click",
                      {
                        type: "paymentMethodRun",
                        data: {
                          run: function (ui, inv, invoices) {
                            _.each(invoices, function (inv) {
                              $(
                                ui.table.body["row-" + inv.id].selector
                              ).removeClass("active");
                            });

                            $(ui.table.body["row-" + inv.id].selector).addClass(
                              "active"
                            );

                            enterPayment(ui, [inv], invoices);
                          }.bind({}, ui, inv, invoices),
                        },
                      },
                      sb.moduleId
                    );

                  ui.table.body["row-" + inv.id].btns.btns
                    .makeNode("online", "div", {
                      css: "ui teal button",
                      text: "Take Payment",
                    })
                    .notify(
                      "click",
                      {
                        type: "paymentMethodRun",
                        data: {
                          run: function (inv) {
                            _.each(invoices, function (inv) {
                              $(
                                ui.table.body["row-" + inv.id].selector
                              ).removeClass("active");
                            });

                            $(ui.table.body["row-" + inv.id].selector).addClass(
                              "active"
                            );

                            takePayment(ui, [inv], invoices);
                          }.bind({}, inv),
                        },
                      },
                      sb.moduleId
                    );
                }

                /*
							if(inv.locked != 'locked'){

								ui.table.body['row-'+inv.id].btns.btns.makeNode('delete', 'div', {css:'ui red button', text:'Delete'})
									.notify('click', {
										type:'paymentMethodRun',
										data:{
											run:function(ui, inv, invoices){

												sb.dom.alerts.ask({
													title: 'Are you sure?',
													text: 'This cannot be undone.'
												}, function(resp){

													if(resp){

														swal.disableButtons();

														sb.data.db.obj.erase('invoices', inv.id, function(deletedInvoice){

															invoices = _.reject(invoices, function(invoice){ return invoice.id == inv.id; });

															sb.data.db.obj.getById('invoices', _.pluck(invoices, 'id'), function(invoiceList){

																swal.close();

																printTable(invoiceList);

															});

														});

													}

												});

											}.bind({}, ui, inv, invoices)
										}
									}, sb.moduleId);

							}
*/
              });

              ui.table.makeNode("footer", "div", { tag: "tfoot" });

              ui.table.footer.makeNode("totalRow", "div", {
                tag: "tr",
                css: "ui bold",
              });
              ui.table.footer.totalRow.makeNode("name", "div", {
                tag: "th",
                text: "",
              });
              ui.table.footer.totalRow.makeNode("due", "div", {
                tag: "th",
                text: "",
              });
              ui.table.footer.totalRow.makeNode("total", "div", {
                tag: "th",
                text: "<nobr>$ " + (totalAmount / 100).formatMoney() + "</nobr>",
                css: "right aligned"
              });
              ui.table.footer.totalRow.makeNode("paid", "div", {
                tag: "th",
                text: "<nobr>$ " + (totalPaid / 100).formatMoney() + "</nobr>",
                css: "right aligned"
              });
              ui.table.footer.totalRow.makeNode("balance", "div", {
                tag: "th",
                text: "<nobr>$ " + (totalBalance / 100).formatMoney() + "</nobr>",
                css: "right aligned"
              });
              ui.table.footer.totalRow
                .makeNode("btns", "div", { tag: "th", css: "" })
                .makeNode("btns", "div", { css: "ui mini compact buttons" });

              // if (totalBalance != 0) {
              //   ui.table.footer.totalRow.btns.btns
              //     .makeNode("manual", "div", {
              //       css: "ui mini compact teal button",
              //       text: "Enter A Payment",
              //     })
              //     .notify(
              //       "click",
              //       {
              //         type: "paymentMethodRun",
              //         data: {
              //           run: function (ui, invoices) {
              //             _.each(invoices, function (inv) {
              //               $(
              //                 ui.table.body["row-" + inv.id].selector
              //               ).removeClass("active");
              //             });

              //             enterPayment(ui, invoices, invoices);
              //           }.bind({}, ui, invoices),
              //         },
              //       },
              //       sb.moduleId
              //     );
              //   ui.table.footer.totalRow.btns.btns
              //     .makeNode("online", "div", {
              //       css: "ui mini compact teal button",
              //       text: "Take Payment",
              //     })
              //     .notify(
              //       "click",
              //       {
              //         type: "paymentMethodRun",
              //         data: {
              //           run: function (ui, invoices) {
              //             _.each(invoices, function (inv) {
              //               $(
              //                 ui.table.body["row-" + inv.id].selector
              //               ).removeClass("active");
              //             });

              //             takePayment(
              //               ui,
              //               _.reject(invoices, function (inv) {
              //                 return +inv.balance == 0;
              //               }),
              //               invoices
              //             );
              //           }.bind({}, ui, invoices),
              //         },
              //       },
              //       sb.moduleId
              //     );
              // }

              ui.makeNode("paymentCont", "div", {});

              //ui.makeNode('break', 'div', {css:'ui clearing basic segment'});

              ui.makeNode("payments", "div", {
                css: "ui huge header",
                text: "Payment History",
              });
              ui.makeNode("paymentsCont", "div", {});

              ui.makeNode("paymentLinkContBreak", "div", { text: "<br />" });
              ui.makeNode("paymentLinkCont", "div", {});

              ui.patch();

              if (_.uniq(_.compact(paymentsList)).length == 0) {
                ui.paymentsCont.makeNode("none", "div", {
                  text: "No payments recorded so far.",
                });

                ui.paymentsCont.patch();
              } else {
                sb.data.db.obj.getById(
                  "payments",
                  _.uniq(_.compact(paymentsList)),
                  function (payments) {
                    if (payments.length == 0) {
                      ui.paymentsCont.makeNode("none", "div", {
                        text: "No payments recorded so far.",
                      });
                    } else {
                      ui.paymentsCont.makeNode("paymentsTable", "div", {
                        tag: "table",
                        css: "ui collapsing table",
                      });
                      ui.paymentsCont.paymentsTable.makeNode(
                        "headerRow",
                        "div",
                        { tag: "thead" }
                      );
                      ui.paymentsCont.paymentsTable.headerRow.makeNode(
                        "name",
                        "div",
                        { tag: "th", text: "Payment Schedule" }
                      );
                      ui.paymentsCont.paymentsTable.headerRow.makeNode(
                        "idnumber",
                        "div",
                        { tag: "th", text: "Payment ID" }
                      );
                      ui.paymentsCont.paymentsTable.headerRow.makeNode(
                        "due",
                        "div",
                        { tag: "th", text: "Payment Date" }
                      );
                      ui.paymentsCont.paymentsTable.headerRow.makeNode(
                        "amount",
                        "div",
                        { tag: "th", text: "Payment Amount" }
                      );
                      ui.paymentsCont.paymentsTable.headerRow.makeNode(
                        "fees",
                        "div",
                        { tag: "th", text: "Fees" }
                      );
                      ui.paymentsCont.paymentsTable.headerRow.makeNode(
                        "total",
                        "div",
                        { tag: "th", text: "Payment Total" }
                      );
                      ui.paymentsCont.paymentsTable.headerRow.makeNode(
                        "btns",
                        "div",
                        { tag: "th", text: "Actions" }
                      );

                      ui.paymentsCont.paymentsTable.makeNode("body", "div", {
                        tag: "tbody",
                      });

                      var invoiceIds = _.pluck(invoices, "id");
                      var testPaymentsExist = false;

                      var sortedPayments = _.sortBy(payments, function (pay) {
                          // Check if details exists and has payment_date property
                          if (pay.details && pay.details.payment_date) {
                            return pay.details.payment_date;
                          }
                          // Fall back to date_created if details.payment_date is not available
                          return pay.date_created;
                        });
                      // Add a counter for alternating row colors
                      var rowCounter = 0;
                      _.each(
                        sortedPayments,
                        function (payment) {
                          var txnId =
                            "#" + payment.main_object + "-" + payment.invoice;
                          // Check if details exists before trying to access its properties
                          if (payment.details && payment.details.id) {
                            var txnId =
                              "#" +
                              payment.main_object +
                              "-" +
                              payment.details.id;
                          }

                          if (payment.fee) {
                            var paymentFee = payment.fee;
                          } else {
                            var paymentFee = 0;
                          }

                          var nonStandardPaymentIndicator = "";
                          if (payment.test_payment === true) {
                            testPaymentsExist = true;
                            nonStandardPaymentIndicator = "ui info";
                          }

                          if (
                            payment.hasOwnProperty("icg_payment_id") &&
                            payment.icg_payment_id != "" &&
                            payment.status && payment.status.includes("R")
                          ) {
                            nonStandardPaymentIndicator = "ui red";
                          } else if (payment.status && payment.status == "Returned") {
                            nonStandardPaymentIndicator = "ui red";
                          }

                          // Determine row background color based on counter
                          var rowBackgroundColor = rowCounter % 2 === 0 ? "" : "#f9fafb";

                          ui.paymentsCont.paymentsTable.body.makeNode(
                            "row-" + payment.id,
                            "div",
                            {
                              tag: "tr",
                              css: nonStandardPaymentIndicator,
                              style: rowBackgroundColor ? "background-color: " + rowBackgroundColor + ";" : ""
                            }
                          );

                          ui.paymentsCont.paymentsTable.body[
                            "row-" + payment.id
                          ].makeNode("name", "div", { tag: "td", text: txnId });
                          ui.paymentsCont.paymentsTable.body[
                            "row-" + payment.id
                          ].makeNode("idnumber", "div", { tag: "td", text: payment.id });

                          if (payment.details && payment.details.payment_date) {
                            ui.paymentsCont.paymentsTable.body[
                              "row-" + payment.id
                            ].makeNode("due", "div", {
                              tag: "td",
                              text: moment(payment.details.payment_date).format(
                                "MM/DD/YY"
                              ),
                            });
                          } else {
                            ui.paymentsCont.paymentsTable.body[
                              "row-" + payment.id
                            ].makeNode("due", "div", {
                              tag: "td",
                              text: moment(payment.date_created).format(
                                "MM/DD/YY"
                              ),
                            });
                          }
                          ui.paymentsCont.paymentsTable.body[
                            "row-" + payment.id
                          ].makeNode("amount", "div", {
                            tag: "td",
                            text: "<nobr>$ " + (payment.amount / 100).formatMoney() + "</nobr>",
                          });
                          ui.paymentsCont.paymentsTable.body[
                            "row-" + payment.id
                          ].makeNode("fees", "div", {
                            tag: "td",
                            text: "<nobr>$ " + (payment.fee / 100).formatMoney() + "</nobr>",
                          });
                          ui.paymentsCont.paymentsTable.body[
                            "row-" + payment.id
                          ].makeNode("total", "div", {
                            tag: "td",
                            text: "<nobr>$ " + ((payment.amount + paymentFee) / 100).formatMoney() + "</nobr>",
                          });
                          // Build a combined notes string with all available information
                          var notesArray = [];

                          // Add payment source information if available
                          if (payment.details && payment.details.manual_payment == "true") {
                            notesArray.push("Manual Payment");
                          }

                          // Check for Stripe ID in either stripeId or stripe_payment_id
                          if (payment.hasOwnProperty("stripeId") && payment.stripeId != "") {
                            notesArray.push("Stripe: " + payment.stripeId);
                          } else if (payment.hasOwnProperty("stripe_payment_id") && payment.stripe_payment_id != "") {
                            notesArray.push("Stripe: " + payment.stripe_payment_id);
                          }

                          // Check for iCG payment ID
                          if (payment.hasOwnProperty("icg_payment_id") && payment.icg_payment_id != "") {
                            notesArray.push("iCG: " + payment.icg_payment_id);
                          }

                          // Add notes if available
                          if (payment.details && payment.details.notes && payment.details.notes.trim() !== "") {
                            notesArray.push(payment.details.notes);
                          }

                          // Join all notes with line breaks
                          var combinedNotes = notesArray.join("<br>");

                          // Add the notes to the table cell with improved styling
                          // ui.paymentsCont.paymentsTable.body[
                          //   "row-" + payment.id
                          // ].makeNode("notes", "div", {
                          //   tag: "td",
                          //   text: combinedNotes,
                          //   style: "white-space: pre-line; max-width: 300px; overflow-wrap: break-word;"
                          // });
                          ui.paymentsCont.paymentsTable.body[
                            "row-" + payment.id
                          ].makeNode("btns", "div", { tag: "td", css: "" });
                          ui.paymentsCont.paymentsTable.body[
                            "row-" + payment.id
                          ].btns.makeNode("btns", "div", {
                            css: "ui mini compact buttons",
                          });

                          ui.paymentsCont.paymentsTable.body['row-'+payment.id].btns.btns.makeNode('delete', 'div', {css:'ui button', text:'Archive'})
                          	.notify('click', {
                          		type:'paymentMethodRun',
                          		data:{
                          			run:function(payment){

                          				sb.dom.alerts.ask({
                          					title: 'Are you sure?',
                          					text: 'This cannot be undone.'
                          				}, function(resp){

                          					if(resp){

                          						swal.disableButtons();

                          						var invToFix = _.where(invoices, {id:payment.invoice})[0];

                          						if ( invToFix ) {

                          							invToFix.balance = invToFix.balance + payment.amount;
                          							invToFix.paid = invToFix.paid - payment.amount;
                          							invToFix.payments = _.reject(invToFix.payments, function(pay){ return pay == payment.id });

                          							if ( invToFix.balance == invToFix.amount ) {
                          								invToFix.locked = 'not-locked';
                          							}

                          							sb.data.db.obj.update('invoices', invToFix, function(updatedInvoice){

                          								sb.data.db.obj.getById('invoices', _.pluck(invoices, 'id'), function(invoiceList){

                          									sb.data.db.obj.erase('payments', payment.id, function(deleted){

                          										swal.close();

                          										printTable(invoiceList);

                          									});

                          								});

                          							});

                          						}else{

                          							sb.data.db.obj.erase('payments', payment.id, function(deleted){

                          								swal.close();

                          								printTable(invoices);

                          							});

                          						}

                          					}

                          				});

                          			}.bind({}, payment)
                          		}
                          	}, sb.moduleId);

                            console.log('if check _.contains(invoiceIds, payment.invoice)', _.contains(invoiceIds, payment.invoice), invoiceIds, payment.invoice);
                            console.log( 'elseif check  !_.contains(invoiceIds, payment.invoice)', !_.contains(invoiceIds, payment.invoice), invoiceIds, payment.invoice);
                          // First check if the payment is linked to an invoice
                          if (payment.invoice !== 0) {
                            // Payment is linked to an invoice - show Unlink button
                            ui.paymentsCont.paymentsTable.body[
                              "row-" + payment.id
                            ].btns.btns
                              .makeNode("unlink", "div", {
                                css: "ui button",
                                text: "Unlink Payment",
                              })
                              .notify(
                                "click",
                                {
                                  type: "paymentMethodRun",
                                  data: {
                                    run: function (payment) {
                                      sb.dom.alerts.ask(
                                        {
                                          title: "Are you sure?",
                                          text: "",
                                        },
                                        function (resp) {
                                          if (resp) {
                                            swal.disableButtons();

                                            var invToFix = _.where(invoices, {
                                              id: payment.invoice,
                                            })[0];

                                            invToFix.balance =
                                              invToFix.balance + payment.amount;
                                            invToFix.paid =
                                              invToFix.paid - payment.amount;
                                            invToFix.payments = _.reject(
                                              invToFix.payments,
                                              function (pay) {
                                                console.log("pay", pay);
                                                return pay == payment.id;
                                              }
                                            );

                                            sb.data.db.obj.update(
                                              "invoices",
                                              invToFix,
                                              function (updatedInvoice) {
                                                sb.data.db.obj.update(
                                                  "payments",
                                                  {
                                                    id: payment.id,
                                                    invoice: 0,
                                                  },
                                                  function (updatedPayment) {
                                                    sb.data.db.obj.getById(
                                                      "invoices",
                                                      _.pluck(invoices, "id"),
                                                      function (invoiceList) {
                                                        swal.close();

                                                        printTable(invoiceList);
                                                      }
                                                    );
                                                  }
                                                );
                                              }
                                            );
                                          }
                                        }
                                      );
                                    }.bind({}, payment),
                                  },
                                },
                                sb.moduleId
                              );
                          } else {
                            // Payment is not linked to an invoice (invoice=0) - show Link button
                            ui.paymentsCont.paymentsTable.body[
                              "row-" + payment.id
                            ].btns.btns
                              .makeNode("link", "div", {
                                css: "ui button",
                                text: "Link Payment",
                              })
                              .notify(
                                "click",
                                {
                                  type: "paymentMethodRun",
                                  data: {
                                    run: function (payment) {
                                      var selectedInvoices = [];

                                      ui.paymentLinkCont.makeNode(
                                        "link",
                                        "div",
                                        {
                                          text: "Choose a payment schedule to attach a payment.",
                                          css: "ui small header",
                                        }
                                      );

                                      ui.paymentLinkCont.makeNode(
                                        "table",
                                        "div",
                                        {
                                          tag: "table",
                                          css: "ui striped table",
                                        }
                                      );
                                      ui.paymentLinkCont.table.makeNode(
                                        "headerRow",
                                        "div",
                                        { tag: "thead" }
                                      );
                                      ui.paymentLinkCont.table.headerRow.makeNode(
                                        "btns",
                                        "div",
                                        { tag: "th", text: "Select" }
                                      );
                                      ui.paymentLinkCont.table.headerRow.makeNode(
                                        "name",
                                        "div",
                                        { tag: "th", text: "Name/Type" }
                                      );
                                      ui.paymentLinkCont.table.headerRow.makeNode(
                                        "due",
                                        "div",
                                        { tag: "th", text: "Due Date" }
                                      );
                                      ui.paymentLinkCont.table.headerRow.makeNode(
                                        "total",
                                        "div",
                                        { tag: "th", text: "Total" }
                                      );
                                      ui.paymentLinkCont.table.headerRow.makeNode(
                                        "paid",
                                        "div",
                                        { tag: "th", text: "Paid" }
                                      );
                                      ui.paymentLinkCont.table.headerRow.makeNode(
                                        "balance",
                                        "div",
                                        { tag: "th", text: "Balance" }
                                      );

                                      ui.paymentLinkCont.table.makeNode(
                                        "body",
                                        "div",
                                        { tag: "tbody" }
                                      );

                                      _.each(
                                        _.sortBy(invoices, "due_date"),
                                        function (inv) {
                                          var invName = getInvoiceName(inv);
                                          var relatedId = inv.related_object;
                                          if (
                                            typeof relatedId === "object" &&
                                            !_.isEmpty(inv.related_object)
                                          ) {
                                            relatedId = inv.related_object.id;
                                          }

                                          var rowColor = "warning";
                                          if (inv.balance == 0) {
                                            rowColor = "positive";
                                          }

                                          totalAmount += inv.amount;
                                          totalPaid += inv.paid;
                                          totalBalance += inv.balance;

                                          paymentsList = paymentsList.concat(
                                            inv.payments
                                          );

                                          ui.paymentLinkCont.table.body.makeNode(
                                            "row-" + inv.id,
                                            "div",
                                            { tag: "tr", css: rowColor }
                                          );
                                          ui.paymentLinkCont.table.body[
                                            "row-" + inv.id
                                          ].makeNode("btns", "div", {
                                            tag: "td",
                                            css: "",
                                          });
                                          ui.paymentLinkCont.table.body[
                                            "row-" + inv.id
                                          ].btns
                                            .makeNode("btns", "div", {
                                              css: "ui mini compact toggle green button",
                                              text: "Click to Link",
                                            })
                                            .notify(
                                              "click",
                                              {
                                                type: "paymentMethodRun",
                                                data: {
                                                  run: function (inv) {
                                                    ui.paymentLinkCont.table.body[
                                                      "row-" + inv.id
                                                    ].btns.btns.loading();

                                                    if (!inv.paid) {
                                                      inv.paid = 0;
                                                    }

                                                    inv.balance -=
                                                      payment.amount;
                                                    inv.paid += payment.amount;

                                                    if (inv.payments) {
                                                      inv.payments.push(
                                                        payment.id
                                                      );
                                                    } else {
                                                      inv.payments = [
                                                        payment.id,
                                                      ];
                                                    }

                                                    payment.invoice = inv.id;

                                                    sb.data.db.obj.update(
                                                      "invoices",
                                                      inv,
                                                      function (updatedInv) {
                                                        sb.data.db.obj.update(
                                                          "payments",
                                                          payment,
                                                          function (
                                                            updatedPayment
                                                          ) {
                                                            sb.data.db.obj.getById(
                                                              "invoices",
                                                              _.pluck(
                                                                invoices,
                                                                "id"
                                                              ),
                                                              function (
                                                                invoiceList
                                                              ) {
                                                                printTable(
                                                                  invoiceList
                                                                );
                                                              }
                                                            );
                                                          }
                                                        );
                                                      }
                                                    );
                                                  }.bind({}, inv),
                                                },
                                              },
                                              sb.moduleId
                                            );
                                          ui.paymentLinkCont.table.body[
                                            "row-" + inv.id
                                          ].makeNode("name", "div", {
                                            tag: "td",
                                            text:
                                              "#" +
                                              relatedId +
                                              "-" +
                                              inv.id +
                                              ": " +
                                              invName,
                                          });
                                          ui.paymentLinkCont.table.body[
                                            "row-" + inv.id
                                          ].makeNode("due", "div", {
                                            tag: "td",
                                            text: moment(inv.due_date).format(
                                              "MM/DD/YYYY"
                                            ),
                                          });
                                          ui.paymentLinkCont.table.body[
                                            "row-" + inv.id
                                          ].makeNode("total", "div", {
                                            tag: "td",
                                            text:
                                              "$ " +
                                              (inv.amount / 100).formatMoney(),
                                          });
                                          ui.paymentLinkCont.table.body[
                                            "row-" + inv.id
                                          ].makeNode("paid", "div", {
                                            tag: "td",
                                            text:
                                              "$ " +
                                              (inv.paid / 100).formatMoney(),
                                          });
                                          ui.paymentLinkCont.table.body[
                                            "row-" + inv.id
                                          ].makeNode("balance", "div", {
                                            tag: "td",
                                            text:
                                              "$ " +
                                              (inv.balance / 100).formatMoney(),
                                          });
                                        }
                                      );

                                      ui.paymentLinkCont.table.makeNode(
                                        "footer",
                                        "div",
                                        { tag: "tfoot" }
                                      );

                                      ui.paymentLinkCont.table.footer.makeNode(
                                        "totalRow",
                                        "div",
                                        { tag: "tr", css: "ui bold" }
                                      );
                                      ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                        "btns",
                                        "div",
                                        { tag: "th", css: "" }
                                      );
                                      ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                        "name",
                                        "div",
                                        { tag: "th", text: "" }
                                      );
                                      ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                        "due",
                                        "div",
                                        { tag: "th", text: "" }
                                      );
                                      ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                        "total",
                                        "div",
                                        {
                                          tag: "th",
                                          text:
                                            "$ " +
                                            (totalAmount / 100).formatMoney(),
                                        }
                                      );
                                      ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                        "paid",
                                        "div",
                                        {
                                          tag: "th",
                                          text:
                                            "$ " +
                                            (totalPaid / 100).formatMoney(),
                                        }
                                      );
                                      ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                        "balance",
                                        "div",
                                        {
                                          tag: "th",
                                          text:
                                            "$ " +
                                            (totalBalance / 100).formatMoney(),
                                        }
                                      );
                                      ui.paymentLinkCont.patch();
                                    }.bind({}, payment),
                                  },
                                },
                                sb.moduleId
                              );
                          }

                          // Show Mark as Returned button only if payment has a status and is not already returned
                          // The status check is separate from the Link/Unlink button logic
                          if (typeof payment.status === 'string' && !payment.status.includes("R")) {
                            ui.paymentsCont.paymentsTable.body[
                              "row-" + payment.id
                            ].btns.btns
                              .makeNode("return", "div", {
                                css: "ui red button",
                                text: "Mark as Returned",
                              })
                              .notify(
                                "click",
                                {
                                  type: "paymentMethodRun",
                                  data: {
                                    run: function (payment) {
                                      sb.dom.alerts.ask(
                                        {
                                          title: "Are you sure?",
                                          text: "Only proceed if the payment has been returned from within the payment processor.",
                                        },
                                        function (resp) {
                                          if (resp) {
                                            swal.disableButtons();

                                            if (payment.invoice == 0) {
                                              sb.data.db.obj.update(
                                                "payments",
                                                {
                                                  id: payment.id,
                                                  invoice: 0,
                                                  status: "Returned",
                                                },
                                                function (updatedPayment) {
                                                  sb.data.db.obj.getById(
                                                    "invoices",
                                                    _.pluck(invoices, "id"),
                                                    function (invoiceList) {
                                                      swal.close();

                                                      printTable(invoiceList);
                                                    }
                                                  );
                                                }
                                              );
                                            } else {
                                              var invToFix = _.where(invoices, {
                                                id: payment.invoice,
                                              })[0];

                                              invToFix.balance =
                                                invToFix.balance +
                                                payment.amount;
                                              invToFix.paid =
                                                invToFix.paid - payment.amount;
                                              invToFix.payments = _.reject(
                                                invToFix.payments,
                                                function (pay) {
                                                  console.log("pay", pay);
                                                  return pay == payment.id;
                                                }
                                              );

                                              sb.data.db.obj.update(
                                                "invoices",
                                                invToFix,
                                                function (updatedInvoice) {
                                                  sb.data.db.obj.update(
                                                    "payments",
                                                    {
                                                      id: payment.id,
                                                      invoice: 0,
                                                      status: "Returned",
                                                    },
                                                    function (updatedPayment) {
                                                      sb.data.db.obj.getById(
                                                        "invoices",
                                                        _.pluck(invoices, "id"),
                                                        function (invoiceList) {
                                                          swal.close();

                                                          printTable(
                                                            invoiceList
                                                          );
                                                        }
                                                      );
                                                    }
                                                  );
                                                }
                                              );
                                            }
                                          }
                                        }
                                      );
                                    }.bind({}, payment),
                                  },
                                },
                                sb.moduleId
                              );
                          }

                          ui.paymentsCont.paymentsTable.body[
                              "row-" + payment.id
                          ].btns.btns
                              .makeNode("split", "div", {
                                  css: "ui button",
                                  text: "Split Payment",
                              })
                              .notify(
                                  "click",
                                  {
                                      type: "paymentMethodRun",
                                      data: {
                                          run: function (
                                              payment
                                          ) {
                                              console.log("payment::SPLIT PAYMENT", payment);
                                              var selectedInvoices =
                                                  [];

                                              // Track remaining amount to split
                                              var remainingAmount =
                                                  payment.amount;
                                              var splitAmounts =
                                                  {};

                                              // Function to update the remaining amount display
                                              function updateRemainingAmount() {
                                                  var totalAllocated = 0;

                                                  // Calculate total allocated amount
                                                  _.each(
                                                      splitAmounts,
                                                      function (
                                                          amount
                                                      ) {
                                                          totalAllocated +=
                                                              amount;
                                                      }
                                                  );

                                                  // Update remaining amount
                                                  remainingAmount =
                                                      payment.amount -
                                                      totalAllocated;

                                                  console.log("SPLIT PAYMENT - Updating remaining amount:", {
                                                      totalAllocated: totalAllocated,
                                                      formattedTotalAllocated: "$" + (totalAllocated / 100).formatMoney(),
                                                      remainingAmount: remainingAmount,
                                                      formattedRemainingAmount: "$" + (remainingAmount / 100).formatMoney()
                                                  });

                                                  // Update header text - use jQuery directly like in stripe-payment-sources.js
                                                  $(ui.paymentLinkCont.header.selector).text(
                                                      "Selected Payment Previous Amount: $" +
                                                      (
                                                          payment.amount /
                                                          100
                                                      ).formatMoney() +
                                                      " - Selected Payment Updated Amount: $" +
                                                      (
                                                          remainingAmount /
                                                          100
                                                      ).formatMoney()
                                                  );

                                                  // We'll let the validation in the save button handler take care of validation
                                                  // rather than disabling the button
                                              }

                                              ui.paymentLinkCont.makeNode(
                                                  "header",
                                                  "div",
                                                  {
                                                      text:
                                                          "Selected Payment Previous Amount: $" +
                                                          (
                                                              payment.amount /
                                                              100
                                                          ).formatMoney() +
                                                          " - Selected Payment Updated Amount: $" +
                                                          (
                                                              payment.amount /
                                                              100
                                                          ).formatMoney(),
                                                      css: "ui small header",
                                                  }
                                              );

                                              // Call updateRemainingAmount once to initialize the display
                                              updateRemainingAmount();

                                              ui.paymentLinkCont.makeNode(
                                                  "table",
                                                  "div",
                                                  {
                                                      tag: "table",
                                                      css: "ui striped table",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.makeNode(
                                                  "headerRow",
                                                  "div",
                                                  {
                                                      tag: "thead",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.headerRow.makeNode(
                                                  "btns",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text: "Enter Amount to Apply",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.headerRow.makeNode(
                                                  "name",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text: "Name/Type",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.headerRow.makeNode(
                                                  "due",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text: "Due Date",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.headerRow.makeNode(
                                                  "total",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text: "Total",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.headerRow.makeNode(
                                                  "paid",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text: "Paid",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.headerRow.makeNode(
                                                  "balance",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text: "Balance",
                                                  }
                                              );

                                              ui.paymentLinkCont.table.makeNode(
                                                  "body",
                                                  "div",
                                                  {
                                                      tag: "tbody",
                                                  }
                                              );

                                              // Filter out the current payment's invoice since we're already reconciling it
                                              var filteredInvoices = _.filter(invoices, function(inv) {
                                                  return inv.id !== payment.invoice;
                                              });

                                              console.log("SPLIT PAYMENT - Filtered out current payment's invoice:", {
                                                  originalInvoicesCount: invoices.length,
                                                  filteredInvoicesCount: filteredInvoices.length,
                                                  currentPaymentInvoiceId: payment.invoice
                                              });

                                              _.each(
                                                  _.sortBy(
                                                      filteredInvoices,
                                                      "due_date"
                                                  ),
                                                  function (
                                                      inv
                                                  ) {
                                                      var invName =
                                                          getInvoiceName(
                                                              inv
                                                          );
                                                      var relatedId =
                                                          inv.related_object;
                                                      if (
                                                          typeof relatedId ===
                                                              "object" &&
                                                          !_.isEmpty(
                                                              inv.related_object
                                                          )
                                                      ) {
                                                          relatedId =
                                                              inv
                                                                  .related_object
                                                                  .id;
                                                      }

                                                      var rowColor =
                                                          "warning";
                                                      if (
                                                          inv.balance ==
                                                          0
                                                      ) {
                                                          rowColor =
                                                              "positive";
                                                      }

                                                      totalAmount +=
                                                          inv.amount;
                                                      totalPaid +=
                                                          inv.paid;
                                                      totalBalance +=
                                                          inv.balance;

                                                      paymentsList =
                                                          paymentsList.concat(
                                                              inv.payments
                                                          );

                                                      ui.paymentLinkCont.table.body.makeNode(
                                                          "row-" +
                                                              inv.id,
                                                          "div",
                                                          {
                                                              tag: "tr",
                                                              css: rowColor,
                                                          }
                                                      );
                                                      ui.paymentLinkCont.table.body[
                                                          "row-" +
                                                              inv.id
                                                      ].makeNode(
                                                          "btns",
                                                          "div",
                                                          {
                                                              tag: "td",
                                                              css: "",
                                                          }
                                                      );
                                                      // Create a form with a USD input field for each invoice
                                                      // Use the framework's built-in change handler
                                                      ui.paymentLinkCont.table.body[
                                                          "row-" +
                                                              inv.id
                                                      ].btns.makeNode(
                                                          "amountForm",
                                                          "form",
                                                          {
                                                              amount: {
                                                                  name: "amount",
                                                                  type: "usd",
                                                                  value: 0,
                                                                  // Use the framework's change handler
                                                                  change: function(form, value) {
                                                                      console.log("SPLIT PAYMENT - Framework change handler called for invoice " + inv.id + " with value: " + value);

                                                                      // Convert to cents
                                                                      var amountInCents = Math.round(parseFloat(value.replace(/[^0-9.-]/g, '')) * 100);
                                                                      if (isNaN(amountInCents)) {
                                                                          amountInCents = 0;
                                                                      }

                                                                      // Calculate how much is already allocated to other invoices
                                                                      var otherAllocated = 0;
                                                                      _.each(splitAmounts, function(amount, invoiceId) {
                                                                          if (invoiceId != inv.id) {
                                                                              otherAllocated += amount;
                                                                          }
                                                                      });

                                                                      // Calculate maximum allowed based on payment amount
                                                                      var maxAllowedByPayment = payment.amount - otherAllocated;

                                                                      // Calculate maximum allowed based on invoice balance
                                                                      var maxAllowedByInvoice = inv.balance;

                                                                      // Use the smaller of the two maximums
                                                                      var maxAllowed = Math.min(maxAllowedByPayment, maxAllowedByInvoice);

                                                                      // Validate the amount doesn't exceed the maximum allowed
                                                                      if (amountInCents > maxAllowed) {
                                                                          console.log("SPLIT PAYMENT - Amount exceeds maximum allowed:", {
                                                                              entered: amountInCents,
                                                                              formattedEntered: "$" + (amountInCents / 100).formatMoney(),
                                                                              maxAllowedByPayment: maxAllowedByPayment,
                                                                              formattedMaxAllowedByPayment: "$" + (maxAllowedByPayment / 100).formatMoney(),
                                                                              maxAllowedByInvoice: maxAllowedByInvoice,
                                                                              formattedMaxAllowedByInvoice: "$" + (maxAllowedByInvoice / 100).formatMoney(),
                                                                              maxAllowed: maxAllowed,
                                                                              formattedMaxAllowed: "$" + (maxAllowed / 100).formatMoney(),
                                                                              reason: maxAllowed === maxAllowedByPayment ? "payment amount" : "invoice balance"
                                                                          });

                                                                          // Cap the amount at the maximum allowed
                                                                          amountInCents = maxAllowed;

                                                                          // Update the input field to show the capped value
                                                                          setTimeout(function() {
                                                                              var formattedValue = "$ " + (amountInCents / 100).formatMoney();
                                                                              $(ui.paymentLinkCont.table.body["row-" + inv.id].btns.amountForm.amount.selector).val(formattedValue);
                                                                          }, 0);
                                                                      }

                                                                      // Update the split amounts object
                                                                      splitAmounts[inv.id] = amountInCents;

                                                                      // Update the remaining amount display
                                                                      updateRemainingAmount();

                                                                      console.log("SPLIT PAYMENT - Updated payment allocation for invoice " + inv.id + ":", {
                                                                          allocationAmount: amountInCents,
                                                                          formattedAllocationAmount: "$" + (amountInCents / 100).formatMoney(),
                                                                          invoiceAmount: inv.amount, // Original invoice amount (never changes)
                                                                          formattedInvoiceAmount: "$" + (inv.amount / 100).formatMoney(),
                                                                          currentBalance: inv.balance,
                                                                          formattedCurrentBalance: "$" + (inv.balance / 100).formatMoney(),
                                                                          newBalance: inv.balance - amountInCents,
                                                                          formattedNewBalance: "$" + ((inv.balance - amountInCents) / 100).formatMoney(),
                                                                          currentPaid: inv.paid,
                                                                          formattedCurrentPaid: "$" + (inv.paid / 100).formatMoney(),
                                                                          newPaid: inv.paid + amountInCents,
                                                                          formattedNewPaid: "$" + ((inv.paid + amountInCents) / 100).formatMoney()
                                                                      });

                                                                      // Return the form data to maintain framework compatibility
                                                                      return form;
                                                                  }
                                                              },
                                                          }
                                                      );

                                                      ui.paymentLinkCont.table.body[
                                                          "row-" +
                                                              inv.id
                                                      ].makeNode(
                                                          "name",
                                                          "div",
                                                          {
                                                              tag: "td",
                                                              text:
                                                                  "#" +
                                                                  relatedId +
                                                                  "-" +
                                                                  inv.id +
                                                                  ": " +
                                                                  invName,
                                                          }
                                                      );
                                                      ui.paymentLinkCont.table.body[
                                                          "row-" +
                                                              inv.id
                                                      ].makeNode(
                                                          "due",
                                                          "div",
                                                          {
                                                              tag: "td",
                                                              text: moment(
                                                                  inv.due_date
                                                              ).format(
                                                                  "MM/DD/YYYY"
                                                              ),
                                                          }
                                                      );
                                                      ui.paymentLinkCont.table.body[
                                                          "row-" +
                                                              inv.id
                                                      ].makeNode(
                                                          "total",
                                                          "div",
                                                          {
                                                              tag: "td",
                                                              text:
                                                                  "$ " +
                                                                  (
                                                                      inv.amount /
                                                                      100
                                                                  ).formatMoney(),
                                                          }
                                                      );
                                                      ui.paymentLinkCont.table.body[
                                                          "row-" +
                                                              inv.id
                                                      ].makeNode(
                                                          "paid",
                                                          "div",
                                                          {
                                                              tag: "td",
                                                              text:
                                                                  "$ " +
                                                                  (
                                                                      inv.paid /
                                                                      100
                                                                  ).formatMoney(),
                                                          }
                                                      );
                                                      ui.paymentLinkCont.table.body[
                                                          "row-" +
                                                              inv.id
                                                      ].makeNode(
                                                          "balance",
                                                          "div",
                                                          {
                                                              tag: "td",
                                                              text:
                                                                  "$ " +
                                                                  (
                                                                      inv.balance /
                                                                      100
                                                                  ).formatMoney(),
                                                          }
                                                      );
                                                  }
                                              );

                                              ui.paymentLinkCont.table.makeNode(
                                                  "footer",
                                                  "div",
                                                  {
                                                      tag: "tfoot",
                                                  }
                                              );

                                              ui.paymentLinkCont.table.footer.makeNode(
                                                  "totalRow",
                                                  "div",
                                                  {
                                                      tag: "tr",
                                                      css: "ui bold",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                                  "btns",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      css: "",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                                  "name",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text: "",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                                  "due",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text: "",
                                                  }
                                              );
                                              ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                                  "total",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text:
                                                          "$ " +
                                                          (
                                                              totalAmount /
                                                              100
                                                          ).formatMoney(),
                                                  }
                                              );
                                              ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                                  "paid",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text:
                                                          "$ " +
                                                          (
                                                              totalPaid /
                                                              100
                                                          ).formatMoney(),
                                                  }
                                              );
                                              ui.paymentLinkCont.table.footer.totalRow.makeNode(
                                                  "balance",
                                                  "div",
                                                  {
                                                      tag: "th",
                                                      text:
                                                          "$ " +
                                                          (
                                                              totalBalance /
                                                              100
                                                          ).formatMoney(),
                                                  }
                                              );

                                              // Add a Save Splits button
                                              ui.paymentLinkCont
                                                  .makeNode(
                                                      "saveBtn",
                                                      "div",
                                                      {
                                                          css: "ui green button",
                                                          text: "Save Splits",
                                                      }
                                                  )
                                                  .notify(
                                                      "click",
                                                      {
                                                          type: "paymentMethodRun",
                                                          data: {
                                                              run: function () {

                                                                console.log('Save splits clicked');

                                                                // 1) Process the table form fields to get user entered dollar amounts
                                                                var newPayments = [];
                                                                var totalAllocated = 0;

                                                                // Validate that we have at least one allocation
                                                                if (Object.keys(splitAmounts).length === 0) {
                                                                    sb.dom.alerts.alert('Error', 'Please allocate payment amounts before saving.', 'error');
                                                                    return;
                                                                }

                                                                // Create new payment objects for each allocation
                                                                _.each(splitAmounts, function(amount, invoiceId) {
                                                                    if (amount > 0) {
                                                                        // Add to total allocated amount
                                                                        totalAllocated += amount;
                                                                        var newPayment = _.clone(payment);
                                                                        // Remove the ID to avoid conflicts with the original payment
                                                                        delete newPayment.id;
                                                                        newPayment.amount = amount;
                                                                        newPayment.invoice = parseInt(invoiceId);

                                                                        newPayments.push(newPayment);
                                                                    }
                                                                });

                                                                // Validate that we have at least one new payment
                                                                if (newPayments.length === 0) {
                                                                    sb.dom.alerts.alert('Error', 'Please allocate payment amounts before saving.', 'error');
                                                                    return;
                                                                }

                                                                // Validate that total allocated doesn't exceed original payment amount
                                                                if (totalAllocated > payment.amount) {
                                                                    sb.dom.alerts.alert('Error', 'Total allocated amount exceeds the original payment amount.', 'error');
                                                                    return;
                                                                }

                                                                // Check if this is actually a transfer rather than a split
                                                                // (single allocation equal to the full payment amount)
                                                                if (newPayments.length === 1 && totalAllocated === payment.amount) {
                                                                    sb.dom.alerts.alert('Payment Transfer Detected', 'It looks like you\'re transferring the entire payment to a different invoice. Please use the "Link/Unlink" buttons above for this operation. The Split Payment feature is designed to divide a payment across multiple invoices.', 'info');
                                                                    return;
                                                                }

                                                                // Define the function to process the split payment
                                                                function processSplitPayment() {
                                                                    // Show loading state
                                                                    $(ui.paymentLinkCont.saveBtn.selector).addClass('loading');

                                                                    // 2) Package up the selected payment and the new payments
                                                                    var data = {
                                                                        selectedPayment: payment,
                                                                        newPayments: newPayments,
                                                                        proposal: proposal_id
                                                                    };
                                                                    console.log('>>>> data::', data);
                                                                    console.log("SPLIT PAYMENT - Sending data to service:", data);

                                                                    // Call the PaymentsService.splitPayment method
                                                                    sb.data.db.service("PaymentsService", "splitPayment", data, function(response) {

                                                                        console.log("SPLIT PAYMENT - Response from service:", response);

                                                                        // Remove loading state
                                                                        $(ui.paymentLinkCont.saveBtn.selector).removeClass('loading');

                                                                        if (response.success) {
                                                                            sb.dom.alerts.alert('Success', 'Payment has been split successfully.', 'success');

                                                                            // Check if invoices were returned in the response
                                                                            if (response.invoices && Array.isArray(response.invoices)) {
                                                                                printTable(response.invoices);
                                                                            } else {
                                                                                // If no invoices in response, fetch them from the database
                                                                                sb.data.db.obj.getWhere('invoices', { related_object: proposal_id }, function(updatedInvoices) {
                                                                                    if (updatedInvoices && updatedInvoices.length > 0) {
                                                                                        printTable(updatedInvoices);
                                                                                    } else {
                                                                                        console.error('Failed to retrieve invoices after split payment');
                                                                                        sb.dom.alerts.alert('Warning', 'Payment was split successfully, but there was an issue refreshing the display. Please reload the page.', 'warning');
                                                                                    }
                                                                                });
                                                                            }
                                                                        } else {
                                                                            sb.dom.alerts.alert('Error', response.message || 'An error occurred while splitting the payment.', 'error');
                                                                        }
                                                                    });
                                                                }

                                                                // Process the split payment
                                                                processSplitPayment();
                                                              },
                                                          },
                                                      },
                                                      sb.moduleId
                                                  );

                                              // Button will always be clickable, validation happens in the handler

                                              ui.paymentLinkCont.patch();

                                          }.bind(
                                              {},
                                              payment
                                          ),
                                      },
                                  },
                                  sb.moduleId
                              );

                          // Apply the same background color to the note row
                          ui.paymentsCont.paymentsTable.body.makeNode(
                            "note-row-" + payment.id,
                            "div",
                            { text: `<td colspan="7" style="border-top: 0; color: #aeaeae; font-size: .9em; max-width: 300px; overflow-wrap: break-word; background-color: ${rowBackgroundColor};"> ${combinedNotes}</td>` }
                          );

                          // Increment the row counter after processing both rows for a payment
                          rowCounter++;

                        }

                      );

                      if (testPaymentsExist) {
                        ui.paymentsCont.makeNode("testpaymentMessage", "div", {
                          text: "* Payments highlighted in blue are test payments.",
                          style: 'color: blue;'
                        });
                      }
                    }

                    ui.paymentsCont.patch();
                  }
                );
              }
            },
            true
          );
        } else {
          ui.empty();

          ui.makeNode("title", "div", {
            css: "ui large header",
            text: "No invoices",
          });

          ui.patch();
        }
      }

      printTable(invoices);
    },

    start: function (data) {
      if (data.hasOwnProperty("objectId")) {
        objectId = data.objectId;
      }

      if (data.hasOwnProperty("compact")) {
        compact = data.compact;
        collapse = true;
      }

      if (data.hasOwnProperty("collapse")) {
        collapse = data.collapse;
      }

      if (!data.hasOwnProperty("buildUI") && data.buildUI !== false) {
        ui = sb.dom.make(data.domObj.selector);
      } else {
        ui = data.domObj;
      }

      if (data.hasOwnProperty("objectType")) {
        objectType = data.objectType;
      }

      if (data.hasOwnProperty("flexible")) {
        flexible = data.flexible;
      }

      tableUI = ui.makeNode("paymentMethods", "container", { uiGrid: false });
      tableUI.state = buildTableUI;

      if (!data.hasOwnProperty("buildUI") && data.buildUI !== false) {
        ui.build();
      }

      tableUI.state();
      tableUI.state.show();
    },

    startPaymentButton: function (data) {
      ui = sb.dom.make(data.domObj.selector);

      buttonUI = ui.makeNode("buttonContainer", "div", { css: "" });
      buttonUI.run = buttonState;

      ui.build();

      buttonUI.run(data.payment, data.buttonSetup);
    },
  };
});
