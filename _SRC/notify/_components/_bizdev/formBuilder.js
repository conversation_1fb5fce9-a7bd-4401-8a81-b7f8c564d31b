Factory.register('formBuilder', function(sb){
	
	var components = {};
	
	function addFieldToForm(formObj, contactInfoTypes){

		var selected = _.findWhere(contactInfoTypes, {id:+this.col2.cont.formCont.form.process().fields.fieldType.value});
		
		if(_.isEmpty(selected)){
			
			selected = _.findWhere(contactInfoTypes, {id:this.col2.cont.formCont.form.process().fields.fieldType.value});
			
		}

		switch(selected.data_type){
			
			case 'address':
			
				formObj.fieldGroups['field-'+selected.id] = {
					street:{
						name:'street',
						label:'Street',
						type:'text'
					},
					city:{
						name:'city',
						label:'City',
						type:'text'
					},
					state:{
						name:'state',
						label:'State',
						type:'text'
					},
					zip:{
						name:'zip',
						label:'Zip',
						type:'text'
					}
				};
												
				break;
				
			case 'date':
								
				formObj.fieldGroups['field-'+selected.id] = {};
				formObj.fieldGroups['field-'+selected.id]['field-'+selected.id] = {
					name:'field-'+selected.id,
					label:selected.name,
					type:'date'
				};
			
				break;
				
			case 'account_info':
							
				formObj.fieldGroups['field-'+selected.id] = {};
				formObj.fieldGroups['field-'+selected.id]['field-'+selected.id] = {
					name:'field-'+selected.id,
					label:selected.name,
					type:'text'
				};
				
				formObj.fieldGroups['field-'+selected.id]['field-password-'+selected.id] = {
					name:'field-password-'+selected.id,
					label:'Password',
					type:'text'
				};
			
				break;	
				
			default:
			
				if(selected.id == 'notebox'){

					formObj.fieldGroups['field-'+selected.id] = {};
					formObj.fieldGroups['field-'+selected.id]['field-'+selected.id] = {
						name:'field-'+selected.id,
						label:selected.name,
						type:'textbox'
					};						
					
				}else{
					
					formObj.fieldGroups['field-'+selected.id] = {};
					formObj.fieldGroups['field-'+selected.id]['field-'+selected.id] = {
						name:'field-'+selected.id,
						label:selected.name,
						type:'text'
					};	
					
				}
			
		}
		
		formObj.fieldOrder.push('field-'+selected.id);
		
		contactInfoTypes = _.reject(contactInfoTypes, function(obj){
			
			return formObj.fieldOrder.indexOf('field-'+obj.id) > -1;
			
		});
		
		var newOptions = [];
		
		newOptions.push({
			name:'Please choose',
			value:0
		});
		
		_.each(contactInfoTypes, function(infoType){

			newOptions.push({
				name:infoType.name,
				value:infoType.id
			});
								
		});
		
		this.col2.cont.formCont.form.fieldType.update({
			options:newOptions
		});
				
		drawForm(this.col2.cont.cont, formObj, true);
		
	}
	
	function createNewForm(bp, dom, editObj, callback){
		
//console.log(`Create New Form: Arguments`, arguments);

		var taggedUsers = [],
			testFormObj = {
				submitButton:{
					text:'Submit',
					css:'pda-btn-green'
				},
				title:'',
				usersToNotify:[],
				fieldOrder:['name'],
				fieldGroups:{
					name:{
						fname:{
							name:'fname',
							label:'First Name',
							type:'text'
						},
						lname:{
							name:'lname',
							label:'Last Name',
							type:'text'
						}
					}
				}
			};
		
		if(!_.isEmpty(editObj) && editObj.fieldGroups){
						
			testFormObj = editObj;
			
		}

		dom.empty();

		//dom.makeNode('title', 'div', {text:'Create a form', css:'ui huge header'});
		
		//dom.makeNode('topCol', 'div', {css:'ui grid'});
		dom.makeNode('col1', 'div', {css:'five wide column'}).makeNode('cont', 'div', {css:'ui blue segment'});
		dom.makeNode('col2', 'div', {css:'eleven wide column'}).makeNode('cont', 'div', {css:''});
								
		//dom.topCol.makeNode('loader', 'loader', {});
		
		dom.patch();
		
		sb.data.db.obj.getAll('users', function(staff){
			
			sb.data.db.obj.getAll('contact_types', function(contactTypes){
				
				sb.data.db.obj.getAll('contact_info_types', function(contactInfoTypes){
					
					testFormObj.contactInfoTypes = contactInfoTypes;
					
					contactInfoTypes.push({
						id:'notebox',
						name:'Comment Box',
						data_type:'text'
					});
					
					contactInfoTypes.push({
						id:'company',
						name:'Company Name',
						data_type:'text'
					});
																		
					dom.col1.cont.makeNode('title', 'div', {text:'Form details', css:'ui medium header'});
					
					dom.col2.cont.makeNode('formCont', 'div', {css:'ui blue segment'});

					dom.col2.cont.makeNode('cont', 'div', {css:''})
					
					var setTask = 'no';	
					var contactTypeId;
					
					if(!_.isEmpty(editObj)){
						if(editObj.contact_type){
							contactTypeId = +editObj.contact_type.id;
						}
					}
									
					var formSetup = {
							name:{
								name:'name',
								label:'Form Name (internal use)',
								type:'text'
							},
							title:{
								name:'title',
								label:'Form Title (publically visible)',
								type:'text'
							},
							contact_type:{
								name:'contact_type',
								label:'Type of contact that will be created',
								type:'select',
								value:contactTypeId,
								options:_.map(contactTypes, function(contactType){
									
									var ret = {
											name:contactType.name,
											value:contactType.id
										};
									
									return ret;
									
								})
							},
							notifyList:{
								name:'notifyList',
								label:'Who should be notified when this form is filled out?',
								type:'checkbox',
								options:_.map(staff, function(s){
									
									var ret = {
											name:'notifyList',
											label:s.fname +' '+ s.lname,
											value:s.id
										};

									var currentAssigned = _.pluck(editObj.usersToNotify, 'id');

									if(currentAssigned.indexOf(s.id) >= 0){
										ret.selected = true;
									}	
									
									return ret;
								})
							},
							newTask:{
								name:'newTask',
								label:'Create a follow up task?',
								type:'select',
								options:[
									{
										name:'No',
										value:'no'
									},
									{
										name:'Yes',
										value:'yes'
									}
								],
								change:function(form, selected){

									if(selected == 'yes'){
										
										dom.col1.cont.form.taskDueDate.update({
											type:'number',
											value:1
										});
										
										setTask = 'yes';
										
									}else{
										
										dom.col1.cont.form.taskDueDate.update({
											type:'hidden',
											value:0
										});
										
										setTask = 'no';
										
									}
									
								}
							},
							taskDueDate:{
								name:'taskDueDate',
								label:'How many days after the submission should the task be due?',
								type:'hidden',
								value:0
								
							}
						};

					if(!_.isEmpty(editObj)){
						
						formSetup.name.value = editObj.name;
						formSetup.title.value = editObj.title;
						
						if(editObj.usersToNotify){
							
							_.each(editObj.usersToNotify, function(staff){
								
								taggedUsers.push(staff.fname +' '+ staff.lname);
								formSetup.notifyList.value += '<span class="label label-primary">'+ staff.fname +' '+ staff.lname +'</span><span>&nbsp;</span>';								
								
							});
							
						}
						
						if(editObj.create_task == 'yes'){
							
							formSetup.newTask.options[1].selected = true;
							formSetup.taskDueDate.type = 'number';
							formSetup.taskDueDate.value = editObj.task_due_date;
																					
						}
						
					}	
					
					dom.col1.cont.makeNode('form', 'form', formSetup);
					
					dom.col1.cont.makeNode('tagTitle', 'div', {css:'ui mini header', text:'What tags should be added when the form is filled out?'});
					dom.col1.cont.makeNode('tags', 'div', {});
					
					dom.col1.cont.makeNode('btnsBreak', 'div', {text:'<br />'});
					
					dom.col1.cont.makeNode('response', 'div', {tag:'button', css:'ui fluid blue button', text:'Auto-response'})
						.notify('click', {
							type:'formBuilder-run',
							data:{
								run:function(dom, editObj){
									
									dom.makeNode('responder', 'modal', {
										onShow:function(){
											
											sb.data.db.obj.getById('external_forms', editObj.id, function(editObj){
												
												var formObj = {
														subject:{
															name:'subject',
															label:'Subject',
															type:'text'
														},
														body:{
															name:'body',
															label:'Body',
															type:'textbox'
														}
													};
												
												if(editObj.send_email_subject){
													formObj.subject.value = editObj.send_email_subject;
												}
												
												if(editObj.send_email_body){
													formObj.body.value = editObj.send_email_body;
												}
												
												dom.responder.body.empty();
												
												dom.responder.body.makeNode('title', 'div', {text:'Edit the submission auto response', css:'ui huge header'});
												
												dom.responder.body.makeNode('form', 'form', formObj);
												
												dom.responder.body.makeNode('formBreak', 'div', {text:'<br />'});
												
												dom.responder.body.makeNode('save', 'div', {text:'Save', css:'ui green button', tag:'button'})
													.notify('click', {
														type:'formBuilder-run',
														data:{
															run:function(dom, obj){
																
																dom.body.save.loading();
																
																var formInfo = dom.body.form.process().fields;
																var updateObj = {
																		id:obj.id,
																		send_email_subject:formInfo.subject.value,
																		send_email_body:formInfo.body.value
																	};
																																
																sb.data.db.obj.update('external_forms', updateObj, function(updated){
																	
																	dom.body.save.loading(false);
																	
																	dom.hide();
																	
																});
																
															}.bind({}, dom.responder, editObj)
														}
													}, sb.moduleId);
												
												dom.responder.body.patch();
												
											}, {
												send_email_subject:true,
												send_email_body:true
											});
											
																						
										}
									});
									
									dom.patch();
									
									dom.responder.show();
									
								}.bind({}, dom.col1.cont, editObj)
							}
						}, sb.moduleId);

					dom.col1.cont.makeNode('btnsBreak2', 'div', {text:'<br /><br />'});
															
					dom.col1.cont.makeNode('btns', 'div', {css:'ui buttons'});
					
					dom.col1.cont.makeNode('modals', 'div', {});
					
					dom.makeNode('finalBreak', 'div', {text:'<br />'});

					dom.col1.cont.btns.makeNode('save', 'button', {text:'<i class="fa fa-floppy-o"></i> Save', css:'pda-btn-green'})
						.notify('click', {
							type:'formBuilder-run',
							data:{
								run:function(formObj, formBuilderDom, taggedUsers, staff, setup){
									
									var formData = this.col1.cont.form.process(),
										createUpdate = 'create';
									
									if(formData.fields.name == ''){
										sb.dom.alerts.alert('Error', 'Please provide a name for the form', 'error');
										return;
									}
									
									dom.col1.cont.btns.save.loading();

									formObj.title = formData.fields.title.value;
									formObj.name = formData.fields.name.value;
									formObj.contact_type = formData.fields.contact_type.value;
									formObj.create_task = setTask;
									formObj.task_due_date = formData.fields.taskDueDate.value;
									
									if(formData.fields.notifyList){
										formObj.usersToNotify = formData.fields.notifyList.value;
									}

									if(!_.isEmpty(setup)){
										
										if(setup.id){
											
											formObj.id = setup.id;
											createUpdate = 'update';
											
										}
										
									}

									sb.data.db.obj[createUpdate]('external_forms', formObj, function(newForm){
										
										setTimeout(function(){
											
											dom.col1.cont.btns.save.loading(false);
											
											dom.col1.cont.btns.save.text('Saved!');
											
											setTimeout(function(){
												
												dom.col1.cont.btns.save.text('<i class="fa fa-floppy-o"></i> Save');
												
											}, 1000);
											
										}, 1000);
										
										if(_.isEmpty(bp) && _.isEmpty(editObj)){
											
											callback(newForm);
											
										}else{
											
											sb.notify({
												type:'app-navigate-to',
												data:{
													itemId:'formBuilder',
													viewId:'table'
												}
											});
											
										}
										
									});
									
								}.bind(dom, testFormObj, dom.col2.cont.cont, taggedUsers, staff, editObj)
							}
						}, sb.moduleId);
					
					if(!_.isEmpty(editObj)){
						
						dom.col1.cont.btns.makeNode('embed', 'button', {text:'<i class="fa fa-code"></i> Embed Code', css:'pda-btn-primary'})
							.notify('click', {
								type:'formBuilder-run',
								data:{
									run:function(editObj){
																			
										if(!editObj.hasOwnProperty('id')){
											
											sb.dom.alerts.alert('', 'You need to save the form first.', 'error');
											return;
											
										}else{
											
											this.makeNode('code', 'modal', {});
										
											this.code.body.makeNode('title', 'div', {text:'Form Embed Code', css:'ui huge header'});
											
											this.code.body.makeNode('embed', 'div', {css:'ui secondary segment', text:''}).makeNode('embed', 'text', {text:''});
											
											this.code.footer.makeNode('close', 'button', {text:'<i class="fa fa-times"></i> Close', css:'pda-btnOutline-red'})
												.notify('click', {
													type:'formBuilder-run',
													data:{
														run:function(){
															
															this.code.hide();
															
														}.bind(this)
													}
												}, sb.moduleId);
											
											this.patch();
											
											this.code.show();
											
											$(this.code.body.embed.embed.selector).text('<div id="pagoda-form" class="pagoda-form" data-id="'+ appConfig.instance +'" data-f="'+ testFormObj.id +'"></div> <script src="https://pagoda.voltz.software/formEmbed.js"></script>')
											
											
										}
										
									}.bind(dom.col1.cont.modals, editObj)
								}
							}, sb.moduleId);
						
					}	
					
/*
					dom.col1.cont.btns.makeNode('done', 'button', {text:'<i class="fa fa-check"></i> Done', css:'pda-btnOutline-green'})
						.notify('click', {
							type:'formBuilder-run',
							data:{
								run:function(){
									
									sb.notify({
										type:'app-navigate-to',
										data:{
											itemId:'formBuilder',
											viewId:'table'
										}
									});
									
								}
							}
						}, sb.moduleId);
*/
														
					var formObj = {
							fieldType:{
								name:'fieldType',
								label:'',
								type:'select',
								options:[]
							}
						};
					
					formObj.fieldType.options.push({
						name:'Please choose',
						value:0
					});
					
					formObj.fieldType.options.push({
						name:'Comments',
						value:'notebox'
					});
					
					formObj.fieldType.options.push({
						name:'Company Name',
						value:'company'
					});
											
					_.each(contactInfoTypes, function(infoType){
						
						formObj.fieldType.options.push({
							name:infoType.name,
							value:infoType.id
						});
											
					});
					
					dom.col2.cont.formCont.makeNode('title', 'div', {css:'ui medium header', text:'Choose a field type to add'});
														
					dom.col2.cont.formCont.makeNode('form', 'form', formObj);

					dom.col2.cont.formCont.makeNode('addField', 'div', {tag:'button', text:'<i class="fa fa-plus"></i> Add Field', css:'ui green basic fluid button'}).notify('click', {
						type:'formBuilder-run',
						data:{
							run:addFieldToForm.bind(dom, testFormObj, contactInfoTypes)
						}
					}, sb.moduleId);
	
					//delete dom.topCol.loader;
					
					dom.patch();
					
					drawForm(dom.col2.cont.cont, testFormObj, true);
					
					if(sb.sys.state.components.tags){
			
						components.tags.notify({
							type: 'object-tag-view',
							data: {
								domObj: dom.col1.cont.tags,
								objectType: 'external_forms',
								objectId: editObj.id
							}
						});	
						
					}
					
				});
				
			});
			
		});
		
	}
	
	function changeFieldName(dom, formObj, groupName){
		
		var formData = dom.modals.editGroup.body.form.process();
			groupObj = formObj.fieldGroups[groupName];
		
		_.each(groupObj, function(fieldObj, fieldName){
			
			fieldObj.label = formData.fields[fieldObj.name].value;
			
		});
		
		var infoType = _.find(formObj.contactInfoTypes, {id: +groupName.replace(/[^0-9]/g, '')});
		
		if(infoType){
			
			if(infoType.data_type == 'email'){
				
				formObj.send_email = formData.fields.sendAutoresponder.value;
				formObj.send_email_field = groupName;
				formObj.send_email_subject = formData.fields.autoresponderSubject.value;
				formObj.send_email_body = formData.fields.autoresponderBody.value;
				
			}
			
		}

		dom.modals.editGroup.hide();
		
		drawForm(dom, formObj, true);
		
	}
	
	function drawForm(dom, formObj, edit){
		
		dom.empty();
		
		dom.css('ui piled segment');
		
		var setup = {
				submitButton:{
					text:'Submit',
					css:'pda-btn-blue'
				}
			};
			
		dom.makeNode('title', 'div', {text:'Form Preview', css:'ui large header'});
/*
		dom.makeNode('preview', 'div', {tag:'button', text:'<i class="fa fa-eye"></i> Preview Your Form', css:'ui fluid basic black button'}).notify('click', {
			type:'formBuilder-run',
			data:{
				run:function(formObj){
					
					this.modals.makeNode('preview', 'modal', {});
					
					this.modals.preview.body.makeNode('col', 'column', {width:6, offset:3}).makeNode('cont', 'container', {css:'pda-panel-gray pda-container'});
					
					this.modals.preview.body.makeNode('break', 'lineBreak', {});
					
					this.modals.preview.footer.makeNode('close', 'button', {text:'<i class="fa fa-times"></i> Close Preview', css:'pda-btnOutline-red'})
						.notify('click', {
							type:'formBuilder-run',
							data:{
								run:function(){ this.hide(); }.bind(this.modals.preview)
							}
						}, sb.moduleId);
					
					this.modals.patch();
					
					this.modals.preview.show();
					
					renderForm(this.modals.preview.body.col.cont, formObj);
					
				}.bind(dom, formObj)
			}
		}, sb.moduleId);
*/
		
		//dom.makeNode('titleBreak', 'div', {text:'<br />'});
				
		if(_.isEmpty(formObj) && edit){
			
			dom.makeNode('firstField', 'div', {text:'<small>Add the first field</small>', css:'ui small header'});
						
		}else{
			
			var count = 0,
				currentPosition = 0,
				totalGroups = 0;
				
			if(formObj.fieldOrder){
				totalGroups = formObj.fieldOrder.length;
			}	
				
			_.each(formObj.fieldOrder, function(groupName){
				
				var fields = formObj.fieldGroups[groupName];
								
				dom.makeNode('field-'+groupName, 'div', {css:'ui fluid card'});
				dom.makeNode('field-break-'+groupName, 'div', {text:'<br />'});

				dom['field-'+groupName].makeNode('content', 'div', {css:''})
					.makeNode('seg', 'div', {css:'ui tertiary segment'});
				dom['field-'+groupName].makeNode('bottom', 'div', {css:'ui bottom attached mini inverted buttons'});

				//dom['field-'+groupName].bottom.makeNode('btns', 'div', {css:'ui mini buttons'});
				
				if(currentPosition > 0){
					
					dom['field-'+groupName].bottom.makeNode('up', 'div', {tag:'button', text:'<i class="fa fa-arrow-up"></i>', css:'ui blue inverted button'})
						.notify('click', {
							type:'formBuilder-run',
							data:{
								run:moveField.bind({}, dom, formObj, groupName, 'up', currentPosition)
							}
						}, sb.moduleId);
					
				}
				
				if((currentPosition + 1) != totalGroups && totalGroups != 1){
					
					dom['field-'+groupName].bottom.makeNode('down', 'div', {tag:'button', text:'<i class="fa fa-arrow-down"></i>', css:'ui blue inverted button'})
						.notify('click', {
							type:'formBuilder-run',
							data:{
								run:moveField.bind({}, dom, formObj, groupName, 'down', currentPosition)
							}
						}, sb.moduleId);
					
				}	

				dom['field-'+groupName].bottom.makeNode('edit', 'div', {tag:'button', text:'<i class="fa fa-pencil"></i> Edit this field', css:'ui orange inverted button'})
					.notify('click', {
						type:'formBuilder-run',
						data:{
							run:editFormField.bind({}, dom, formObj, groupName)
						}
					}, sb.moduleId);	
				
				if(groupName != 'name'){
					
					dom['field-'+groupName].bottom.makeNode('remove', 'div', {tag:'button', text:'<i class="fa fa-trash-o"></i> Remove field', css:'ui red inverted button'})
						.notify('click', {
							type:'formBuilder-run',
							data:{
								run:removeFieldFromForm.bind({}, dom, formObj, groupName)
							}
						}, sb.moduleId);
					
				}	
				
				// order the fields correctly
				var ordered = {};				
				if(fields.zip){
					
					ordered.street = fields.street;
					ordered.city = fields.city;
					ordered.state = fields.state;
					ordered.zip = fields.zip;
					
				}else{
					ordered = fields;
				}
				
				_.each(ordered, function(fieldObj, fieldName){

					dom['field-'+groupName].content.seg.makeNode('field-'+fieldName, 'div', {css:'ui fluid card'})
						.makeNode('content', 'div', {css:'content'});
					
					dom['field-'+groupName].content.seg['field-'+fieldName].content.makeNode('fieldLabel', 'div', {text:fieldObj.label, css:'ui small header'});
										
					count++;
					
				});
				
				currentPosition++;		
				
			});
			
			if(formObj.submitButton){
				setup.submitButton.text = formObj.submitButton.text;
				setup.submitButton.css = formObj.submitButton.css;
			}
						
		}
		
		dom.makeNode('submitBtnBreak', 'div', {text:'<br />'});
		
		dom.makeNode('btns', 'buttonGroup', {css:''});
		
		dom.btns.makeNode('submit', 'button', {text:setup.submitButton.text, css:setup.submitButton.css});
		dom.btns.makeNode('edit', 'div', {tag:'button', text:'<i class="fa fa-pencil"></i> Edit the submit button', css:'ui basic orange button'})
			.notify('click', {
				type:'formBuilder-run',
				data:{
					run:editSubmitButton.bind({}, dom, formObj)
				}
			}, sb.moduleId);
			
		dom.makeNode('modals', 'div', {});
		
		dom.patch();
		
	}
	
	function editFormField(dom, formObj, groupName){
		
		var group = formObj.fieldGroups[groupName],
			formSetup = {};

		dom.modals.makeNode('editGroup', 'modal', {});
		
		dom.modals.editGroup.body.makeNode('title', 'headerText', {text:'Change field name(s)', size:'small'});
		
		_.each(group, function(fieldObj, fieldName){
			
			formSetup[fieldName] = {};
			
			formSetup[fieldName].name = fieldObj.name;
			formSetup[fieldName].label = 'Change name of '+ fieldObj.label;
			formSetup[fieldName].value = fieldObj.label;
			
		});

		switch(groupName){
			
			case 'name':
			case 'field-company':
			case 'field-notebox':
			
				break;
			
			default:
			
				if(_.find(formObj.contactInfoTypes, {id: +groupName.replace(/[^0-9]/g, '')}).data_type == 'email'){

					formSetup.sendAutoresponder = {
							type:'select',
							label:'Send an email to the user after they submit the form?',
							name:'sendAutoresponder',
							options:[
								{
									name:'No',
									value:'no'
								},
								{
									name:'Yes',
									value:'yes'
								}
							]
						};
					
					formSetup.autoresponderSubject = {
							type:'text',
							label:'Auto-Responder Email Subject',
							name:'autoresponderSubject',
							value:'We got it!'
						};
					
					formSetup.autoresponderBody = {
							type:'textbox',
							label:'Auto-Responder Email Body',
							name:'autoresponderBody',
							value:'Thanks for filling out our form. We received it and will process it shortly.'
						};
						
					if(formObj.send_email_subject != ''){
						formSetup.autoresponderSubject.value = formObj.send_email_subject;
					}
					
					if(formObj.send_email_body != ''){
						formSetup.autoresponderBody.value = formObj.send_email_body;
					}
					
					if(formObj.send_email == 'yes'){
						formSetup.sendAutoresponder.options[1].selected = true;
					}
								
				}	
			
		}
		
		dom.modals.editGroup.body.makeNode('formBreak', 'lineBreak', {});
			
		dom.modals.editGroup.body.makeNode('form', 'form', formSetup);
		
		dom.modals.editGroup.footer.makeNode('btns', 'buttonGroup', {css:'pull-left'});
		
		dom.modals.editGroup.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'})
			.notify('click', {
				type:'formBuilder-run',
				data:{
					run:function(formObj, groupName){
						
						changeFieldName(this, formObj, groupName);
						
					}.bind(dom, formObj, groupName)
				}
			}, sb.moduleId);	
		
		dom.modals.editGroup.footer.btns.makeNode('close', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btnOutline-red'})
			.notify('click', {
				type:'formBuilder-run',
				data:{
					run:function(){
						
						this.hide();
						
					}.bind(dom.modals.editGroup)
				}
			}, sb.moduleId);	
			
		dom.modals.patch();
		
		dom.modals.editGroup.show();
				
	}
	
	function editSubmitButton(dom, formObj){
		
		dom.modals.makeNode('submitButton', 'modal', {
			onShow:function(){
				
				dom.modals.submitButton.body.makeNode('title', 'headerText', {text:'Edit submit button', size:'small'});
					
				dom.modals.submitButton.body.makeNode('titleBreak', 'lineBreak', {});			
								
				dom.modals.submitButton.body.makeNode('form', 'form', {
					buttonText:{
						name:'buttonText',
						label:'Button Text',
						value:formObj.submitButton.text
					}
				});
				
				dom.modals.submitButton.footer.makeNode('btns', 'buttonGroup', {css:'pull-left'});
				
				dom.modals.submitButton.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'})
					.notify('click', {
						type:'formBuilder-run',
						data:{
							run:function(formObj){
								
								var formData = this.modals.submitButton.body.form.process();
								
								if(formData.completed == false){
									sb.dom.alerts.alert('Error', 'Please enter something', 'error');
									return;
								}
								
								formObj.submitButton.text = formData.fields.buttonText.value;
								
								dom.modals.submitButton.hide();
								
								drawForm(dom, formObj, true);
								
							}.bind(dom, formObj)
						}
					}, sb.moduleId);
				
				dom.modals.submitButton.footer.btns.makeNode('cancel', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btnOutline-red'})
					.notify('click', {
						type:'formBuilder-run',
						data:{
							run:function(){ this.hide(); }.bind(dom.modals.submitButton)
						}
					}, sb.moduleId);
					
				dom.modals.submitButton.patch();
				
			}
		});
		
		//dom.modals.submitButton.show();
			
		dom.modals.patch();
		
		dom.modals.submitButton.show();
			
		//dom.modals.submitButton.patch();	
		
	}
	
	function moveField(dom, formObj, groupName, direction, currentPosition){
		
		function move(currentArray, old_index, new_index) {
		    if (new_index >= currentArray.length) {
		        var k = new_index - currentArray.length;
		        while ((k--) + 1) {
		            currentArray.push(undefined);
		        }
		    }
		    currentArray.splice(new_index, 0, currentArray.splice(old_index, 1)[0]);
		    return currentArray; // for testing purposes
		};

		if(currentPosition == 0 && direction == 'up'){
			return;
		}
		
		if(currentPosition == formObj.fieldOrder.length && direction == 'down'){
			return;
		}
		
		var newPosition = 0;
		
		if(direction == 'down'){
			
			newPosition = currentPosition + 1;
			
		}else{
			
			newPosition = currentPosition - 1;
			
		}
					
		formObj.fieldOrder = move(formObj.fieldOrder, currentPosition, newPosition);		
		
		drawForm(dom, formObj, true);
	
	}
	
	function removeFieldFromForm(dom, formObj, groupName){

		delete formObj.fieldGroups[groupName];
		
		formObj.fieldOrder = _.reject(formObj.fieldOrder, function(gName){ return gName == groupName });
		
		drawForm(dom, formObj, true);
		
	}
	
	function renderForm(dom, formObj){
				
		var formSetup = {};
		
		_.each(formObj.fieldOrder, function(groupName){
			
			_.each(formObj.fieldGroups[groupName], function(fieldObj, fieldName){

				formSetup[fieldName] = fieldObj;
				
			});
			
		});
		
		if(formObj.title != ''){
			
			dom.makeNode('title', 'div', {text:formObj.title, css:'ui header'});
			
			dom.makeNode('titleBreak', 'lineBreak', {});
			
		}
		
		dom.makeNode('form', 'form', formSetup);
console.log(formObj.submitButton);		
		dom.makeNode('submit', 'button', {text:formObj.submitButton.text, css:formObj.submitButton.css});
		
		dom.patch();
		
	}
	
	return {
		
		init:function(data){
			
			sb.listen({
				'formBuilder-run':this.run
			});
			
			if(sb.sys.state.components.tags){
				components.tags = sb.createComponent('tags');
			}
			
			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'formBuilder',
						title:'Forms',
						icon:'<i class="fa fa-list-alt"></i>',
						views:[
							{
								id:'table',
								default:true,
								type:'table',
								title:'All Forms',
								icon:'<i class="fa fa-list-alt"></i>',
								setup:{
									objectType:'external_forms',
									tableTitle:'<i class="fa fa-users"></i> Contacts',
									childObjs:2,
									searchObjects:false,
									filters:false,
									download:false,
									noObjects:{
										action:function(dom, objectType, blueprint, setup){
							
											dom.empty();
																									
											dom.patch();
										
											createNewForm(blueprint, dom, setup);
										
										}
									},
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										},
										create: {
											name: '<i class="fa fa-plus"></i> New Form',
											css: 'pda-btn-green',
											domType: 'none',
											action: function(){
												
												sb.notify({
													type:'app-navigate-to',
													data:{
														itemId:'self',
														viewId:'newForm'
													}
												});
												
											}
										}
									},
									rowSelection:true,
									multiSelectButtons:{
										erase:{
											name:'<i class="fa fa-times"></i> Delete',
											css:'pda-btn-red',
											domType:'erase',
											action:'erase'
										}
									},
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.name;
										},
										action:function(obj, dom, state, draw){
											
											dom.patch();
											
											draw(false);
											
											createNewForm({}, dom, obj);
											
										}
									},
									visibleCols:{
										name:'Name',
										title:'Title',
										submissions:'Submissions'
									},
									cells: {
										name:function(obj){
											return obj.name;
										},
										title:function(obj){
											return obj.title;
										},
										submissions:function(obj){

											return obj.submissions;
										}
									},
									data:function(paged, callback){
										
										sb.data.db.obj.getAll('external_forms', function(ret){
							
											callback(ret);
											
										}, 1, paged);
										
									}
								}
							},
							{
								id:'newForm',
								type:'custom',
								title:'New Form',
								icon:'<i class="fa fa-plus"></i>',
								dom:function(dom, state, draw){
									
									dom.patch();
									
									draw(false);
									
									createNewForm({}, dom, {});							
									
								}
							},
							// HQ Tool
							{
								id:'formTool',
								type:'hqTool',
								name:'Forms',
								tip:'Customize forms to use in your web applications.',
								icon:{
									type:'list',
									color:'purple'
								},
								settings:false, // check to see if form builder has any settings to add here
								mainViews:[
									{
										dom:function(dom, state, draw){
											
											sb.notify({
												type:'show-collection',
												data:{
													actions:{
														create:function(ui, newObj, onComplete){
															
															ui.makeNode('body', 'div', {css:'ui stackable grid'});
															ui.patch();
															
															createNewForm({}, ui.body, {}, function(newForm){
																
																onComplete(newForm);
																
															});
															
														},
														view:true
													},
													layer: 'hq',
													templates: false,
													domObj:dom,
													fields:{
														name:{
															title:'Name (Internal)',
															type:'title'
														},
														title:{
															title:'Form Title (Public)'
														},
														submissions:{
															title:'Submissions',
															view:function(dom, obj){
																
																dom.makeNode('submissions', 'div', {text:''+obj.submissions});
																dom.patch();
																
															}
														}
													},
													fullView:{
														id:'formTool',
														type:'hqTool'
													},
													groupings:false, // ask Zach about groupings
													singleView:{
														view:function(ui, obj){

															renderForm(ui, obj);
															
														},
														select:2
													},
													state:state,
													objectType:'external_forms',
													where:{childObjs:2}
												}
											});
											
										}
									}
								]
							},
/*
							{
								id:'formTool',
								type:'teamTool',
								name:'Forms',
								tip:'Customize forms to use in your web applications.',
								icon:{
									type:'list',
									color:'purple'
								},
								settings:false, // check to see if form builder has any settings to add here
								mainViews:[
									{
										dom:function(dom, state, draw){
											
											sb.notify({
												type:'show-collection',
												data:{
													actions:{
														create:function(ui, newObj, onComplete){
															
															ui.makeNode('body', 'div', {css:'ui stackable grid'});
															ui.patch();
															
															createNewForm({}, ui.body, {}, function(newForm){
																
																onComplete(newForm);
																
															});
															
														},
														view:true
													},
													domObj:dom,
													fields:{
														name:{
															title:'Name',
															view:function(dom, obj){
																
																dom.makeNode('name', 'div', {text:obj.name});
																dom.patch();
																
															}
														},
														title:{
															title:'Title'
														},
														submissions:{
															title:'Submissions'
														}
													},
													fullView:{
														id:'formTool',
														type:'teamTool'
													},
													groupings:false, // ask Zach about groupings
													singleView:{
														view:function(ui, obj){
															
															renderForm(ui, obj);
															
														},
														select:2
													},
													state:state,
													objectType:'external_forms',
													where:{childObjs:2}
												}
											});
											
										}
									}
								]
							},
*/
							{
								id:'external_forms-obj',
								type:'object-view',
								title:'Forms',
								icon:{type:'list', color:'purple'},
								dom:function(dom, state, draw){
									
									sb.data.db.obj.getById('external_forms', state.id, function(form){
										
										dom.makeNode('grid', 'div', {css:'ui grid'});
										
										dom.patch();
										
										createNewForm({}, dom.grid, form);
										
									}, 2);
									
								}
								
							}
						]
					}
				}
			});
			
		},
		
		run:function(data){ data.run(); }
		
	}
	
});