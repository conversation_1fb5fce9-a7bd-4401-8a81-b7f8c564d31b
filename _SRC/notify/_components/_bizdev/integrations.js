Factory.register('integrations', function(sb) {
	
	var comps = {},
		qbOAuthRedirect = 'http://localhost:8080/api/quickbooksOauth.php?i='+appConfig.instance;
	
	function getApplicationSettings(user, callback){
		
		sb.data.db.obj.getWhere('view_settings', {user:1}, function(viewSettings){
								
			if(viewSettings.length == 0){
				
				viewSettings[0] = {
					id:0,
					user:1,
					settings:{}
				};
								
				sb.data.db.obj.create('view_settings', viewSettings[0], function(done){
					
					viewSettings[0].id = done.id;
					
					callback(viewSettings[0]);
					
				});
				
			}else{
												
				sb.data.db.obj.update('view_settings', viewSettings[0], function(done){
				
					callback(viewSettings[0]);
				
				});
				
			}
					
		});
		
	}
	
	function getUserSettings(user, callback){
		
		sb.data.db.obj.getWhere('view_settings', {user:user.id}, function(viewSettings){
								
			if(viewSettings.length == 0){
				
				viewSettings[0] = {
					id:0,
					user:user.id,
					settings:{}
				};
								
				sb.data.db.obj.create('view_settings', viewSettings[0], function(done){
					
					viewSettings[0].id = done.id;
					
					callback(viewSettings[0]);
					
				});
				
			}else{
								
				sb.data.db.obj.update('view_settings', viewSettings[0], function(done){
				
					callback(viewSettings[0]);
				
				});
				
			}
					
		});
		
	}
	
	function loadScript(){
		
		var s = document.createElement("script");
		s.type = "text/javascript";
		s.src = "https://appcenter.intuit.com/Content/IA/intuit.ipp.anywhere-1.3.3.js";
		$("head").append(s);
		console.log('test');
    
	}
	
	function mailChimpView(dom, state, draw){
		
		function chooseNextStep(dom, state, draw){
			
			dom.empty();
			
			dom.css('');
			
			dom.makeNode('title', 'div', {text:'MailChimp Integration', css:'ui huge header'});

			dom.makeNode('subTitle', 'div', {text:'What would you like to do next?', css:'ui medium header'});
			
			dom.makeNode('btns', 'buttonGroup', {css:'pull-left'});
			
			dom.btns.makeNode('download', 'button', {text:'Download Contacts From MailChimp', css:'pda-btn-x-large pda-btn-blue'})
				.notify('click', {
					type:'integrations-run',
					data:{
						run:function(dom, state, draw){
							
							dom.btns.download.loading();
							
							downloadContacts(dom, state, draw);
							
						}.bind({}, dom, state, draw)
					}
				}, sb.moduleId);
			
			dom.btns.makeNode('syncContacts', 'button', {text:'Sync Contacts To MailChimp List(s)', css:'pda-btn-x-large pda-btn-blue'})
				.notify('click', {
					type:'integrations-run',
					data:{
						run:function(dom, state, draw){
							
							dom.btns.syncContacts.loading();
							
							syncContactsWithList(dom, state, draw);
							
						}.bind({}, dom, state, draw)
					}
				}, sb.moduleId);
			
			//dom.btns.makeNode('syncTags', 'button', {text:'Sync Contacts By Tags', css:'pda-btn-x-large pda-btn-blue'});
			
			dom.btns.makeNode('disconnect', 'button', {text:'<i class="fa fa-plug"></i> Disconnect form MailChimp', css:'pda-btn-red'})
				.notify('click', {
					type:'integrations-run',
					data:{
						run:function(dom, state, draw){
							
							sb.dom.alerts.ask({
								title: 'Are you sure?',
								text: 'This cannot be undone.'
							}, function(resp){
								
								if(resp){
									
									swal.disableButtons();
									
									removeConnection(state.instance, function(done){
										
										mailChimpView(dom, state, draw);
										
										sb.dom.alerts.alert('Success', 'MailChimp has been successfully disconnect.', 'success');
										
									});
									
								}
								
							});
							
						}.bind({}, dom, state, draw)
					}
				}, sb.moduleId);
				
			draw(dom);
			
		}
		
		function downloadContacts(dom, state, draw){
			
			var map = [];
			
			function matchMCMergeFields(dom, state, draw, mcList, callback){

				dom.col2.cont.empty();
				dom.col3.cont.empty();

				dom.col2.cont.makeNode('title', 'div', {text:'MailChimp Merge Fields', css:'ui small header'});
										
				dom.col2.cont.makeNode('btns', 'div', {css:'ui buttons'});
										
				dom.col2.cont.makeNode('loadingtext', 'text', {text:'Loading MailChimp merge fields...', css:''});
				dom.col2.cont.makeNode('loading', 'loader', {});

				dom.col3.cont.makeNode('title', 'div', {text:'Select a contact type to sync with', css:'ui small header'});
				
				dom.col3.cont.makeNode('loadingtext', 'text', {text:'Loading contact types...', css:''});
				dom.col3.cont.makeNode('loading', 'loader', {});

				dom.col2.patch();
				dom.col3.patch();
				
				sb.data.db.controller('getMailchimpListMergeFields', {listId:mcList.id}, function(mergeFields){
					
					mergeFields.merge_fields.unshift({
						tag:'EMAILADDRESS',
						merge_id:0,
						name:'Email Address'
					});
					
					var mcMergeFields = _.reject(mergeFields.merge_fields, function(obj){ return obj.tag == 'FNAME' || obj.tag == 'LNAME'; });
																					
					_.each(mcMergeFields, function(field){
	
						dom.col2.cont.makeNode('field-'+field.merge_id, 'container', {
							uiGrid:false,
							css:'ui fluid card',
							drag:{
								moves: true,
								data: {
									type:'mergeFieldDrop',
									field:field,
									instanceId:sb.instanceId,
									run:function(){}
								},
								accepts: false,
								copy:false,
								drop: function(me, dropped){
//console.log('dropping', me, dropped);											
									if(dropped === 'return'){
										
										var deleteObj = _.find(map, {pagodaContactInfoId:me.field.id});
										
/*
										dom.col3.cont['field-'+deleteObj.mcFieldId].drop.cont.makeNode('text', 'headerText', {text:'<small>Drop Contact Info Here</small>', size:'xx-small', css:'text-center'});
										dom.col3.cont['field-'+deleteObj.mcFieldId].drop.cont.patch();
*/
										
										map = _.reject(map, function(obj){
											
											return obj.pagodaContactInfoId == me.field.id;
											
										});
										
									}else{
/*
console.log('map', map);
console.log('dropped', dropped);
console.log('me', me);										
console.log('moving', _.find(map, {mcFieldId:dropped.field.tag}));	
*/
										if(_.find(map, {mcFieldId:dropped.field.tag})){
											
											var deleteObj = _.find(map, {mcFieldId:dropped.field.tag});
											
											dom.col3.cont['col-'+deleteObj.pagodaContactInfoId].css('ui orange fluid card');
											
											dom.col3.cont['col-'+deleteObj.pagodaContactInfoId].cont['field-'+deleteObj.pagodaContactInfoId].cont.text.makeNode('text', 'headerText', {text:'<small>Drop Contact Info Here</small>', size:'xx-small', css:'text-center'});
											dom.col3.cont['col-'+deleteObj.pagodaContactInfoId].cont['field-'+deleteObj.pagodaContactInfoId].cont.text.patch();
											
											map = _.reject(map, function(obj){
											
												return obj.mcFieldId == dropped.field.tag;
												
											});
											
										}
										
										dom.col3.cont['col-'+me.field.id].css('ui green fluid card');
										dom.col3.cont['col-'+me.field.id].cont['field-'+me.field.id].cont.text.empty();
										dom.col3.cont['col-'+me.field.id].cont['field-'+me.field.id].cont.text.patch();
	
										map.push({
											mcFieldId:dropped.field.tag,
											data_type:me.field.data_type,
											pagodaContactInfoId:me.field.id
										});
										
									}						
									
									return true;
											
								}
							}
						});
																													
						dom.col2.cont['field-'+field.merge_id]
							.makeNode('cont', 'div', {css:'content'})
								.makeNode('text', 'div', {text:field.name +' <small>*|'+ field.tag +'|*</small>', css:'ui mini header'});
																														
					});
					
					delete dom.col2.cont.loadingtext;					
					delete dom.col2.cont.loading;
					
					dom.col2.patch();
					
				});
				
				sb.data.db.obj.getAll('contact_types', function(contactTypes){
										
					_.each(contactTypes, function(ctype){
					
						var matchLine = 'Select to match',
							matchColor = 'pda-color-blue';
							
						dom.col3.cont.makeNode('col-'+ctype.id, 'div', {css:'ui fluid card'});
												
						dom.col3.cont['col-'+ctype.id].makeNode('button-'+ ctype.id, 'button', {text:'<h4>'+ ctype.name +'<br /><small class="'+ matchColor +'">'+ matchLine +'</small></h4>', css:'text-center pda-transparent '+ matchColor +' pda-btn-x-large'})
							.notify('click', {
								type:'integrations-run',
								data:{
									run:function(dom, state, draw, ctype, mcList){
										
										dom.col3.cont.empty();
										
										dom.col3.cont.makeNode('title', 'div', {text:'Your contact info<br /><small>Drag the MailChimp merge fields here</small>', css:'ui small header'});

										dom.col3.cont.makeNode('btns', 'div', {css:'ui buttons'});

										dom.col3.cont.btns.makeNode('list', 'button', {text:'<i class="fa fa-pencil"></i> Change contact type', css:'pda-btnOutline-orange'});

										dom.col3.cont.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'})
											.notify('click', {
												type:'integrations-run',
												data:{
													run:function(dom, state, draw, mcList){

														if(map.length == 0 || !_.find(map, {mcFieldId:'EMAILADDRESS'})){
									
															sb.dom.alerts.alert('Error', 'You have to match the email address field.', 'error');
															return;
															
														}
																						
														dom.col3.cont.btns.save.loading();
														
														if(!state.appSettings.apps.integrations.mailchimp.pullFromMaps){
															state.appSettings.apps.integrations.mailchimp.pullFromMaps = {};
														}

														state.appSettings.apps.integrations.mailchimp.pullFromMaps[mcList.id] = {
															map:map,
															contactType:ctype.id
														};
																																				
														state.appSettings.save(function(done){
															
															sb.dom.alerts.alert('Saved!', 'All set. You can now match another contact type if you want.', 'success');
							
															downloadContacts(dom, state, draw);				
															
														});
														
													}.bind({}, dom, state, draw, mcList)
												}
											}, sb.moduleId);
																																							
										_.each(ctype.available_types, function(field){
											
											dom.col3.cont.makeNode('col-'+field.id, 'div', {width:12, css:'ui orange fluid card'})
												.makeNode('cont', 'div', {css:'content'});

											dom.col3.cont['col-'+field.id].cont.makeNode('name', 'div', {text:field.name, css:'ui small header'});

											dom.col3.cont['col-'+field.id].cont.makeNode('field-'+field.id, 'container', {
												css:'',
												uiGrid:false,
												drag: {
													accepts: function(me, dropped){

														// if the dropzone is already occupied						
														if(_.find(map, {pagodaContactInfoId:me.field.id})){
															return false;
														}
														
														return true;
													},
													data: {
														field:field
													}
												}
											})
												.makeNode('cont', 'div', {css:'content'});
																																		
											dom.col3.cont['col-'+field.id].cont['field-'+field.id].cont.makeNode('text', 'div', {text:'<small>Drag merge fields here</small>', css:'ui tiny header'});
												
										});
										
										dom.col3.patch();
																																							
									}.bind({}, dom, state, draw, ctype, mcList)
								}
							}, sb.moduleId);
						
					});
					
					delete dom.col3.cont.loadingtext;
					delete dom.col3.cont.loading;
					
					dom.col3.patch();	
					
				}, 2);
				
			}
			
			function selectMCList(dom, ctype, callback, chooseNewList){
				
				map = [];
				
				dom.col3.css('pda-border-left');
				
				dom.col3.cont.empty();
				
				dom.col3.cont.makeNode('title', 'headerText', {text:'MailChimp Lists<br /><small>Choose a list to sync with</small>', size:'small'});

				dom.col3.cont.makeNode('loader', 'loader', {});
				
				dom.col3.patch();
				
				delete dom.col3.cont.loader;
				
				var hasMap = false;
				if(state.appSettings.apps.integrations.mailchimp){
					if(state.appSettings.apps.integrations.mailchimp.pushToMaps){
						if(state.appSettings.apps.integrations.mailchimp.pushToMaps[ctype.id] && !chooseNewList){
							hasMap = true;
						}
					}
				}
				
				if(hasMap === true){
					
					sb.data.db.controller('getMailchimpList', {
						listId:state.appSettings.apps.integrations.mailchimp.pushToMaps[ctype.id].mailchimpListId
					}, function(mcList){
						
						if(!mcList){
							
							sb.data.db.controller('getMailchimpLists', {}, function(mcLists){
					
								_.each(mcLists.lists, function(list){
														
									dom.col3.cont.makeNode('list-'+ list.id, 'button', {text:'<h4>'+list.name +'<br /><small>List members: '+ list.stats.member_count +'</small></h4>', css:'pull-left text-left pda-transparent pda-color-blue'})
										.notify('click', {
											type:'integrations-run',
											data:{
												run:function(dom, state, draw, ctype, mcList){
													
													matchMCMergeFields(dom, state, draw, ctype, mcList, function(loaded){});
													
												}.bind({}, dom, state, draw, ctype, list)
											}
										}, sb.moduleId);
										
									dom.col3.cont.makeNode('break-'+ list.id, 'lineBreak', {});										
									
								});
								
								dom.col3.patch();
								
							});
							
						}else{
							
							matchMCMergeFields(dom, state, draw, ctype, mcList, function(loaded){});
							
						}
					
					});
					
				}else{
					
					sb.data.db.controller('getMailchimpLists', {}, function(mcLists){
					
						_.each(mcLists.lists, function(list){
												
							dom.col3.cont.makeNode('list-'+ list.id, 'button', {text:'<h4>'+list.name +'<br /><small>List members: '+ list.stats.member_count +'</small></h4>', css:'pull-left text-left pda-transparent pda-color-blue'})
								.notify('click', {
									type:'integrations-run',
									data:{
										run:function(dom, state, draw, ctype, mcList){
											
											matchMCMergeFields(dom, state, draw, ctype, mcList, function(loaded){});
											
										}.bind({}, dom, state, draw, ctype, list)
									}
								}, sb.moduleId);
								
							dom.col3.cont.makeNode('break-'+ list.id, 'lineBreak', {});										
							
						});
						
						dom.col3.patch();
						
					});
					
				}
				
			}
			
			sb.data.db.obj.getAll('contact_types', function(contactTypes){
								
				dom.empty();

				dom.css('ui grid');
				
				dom.makeNode('topCol', 'column', {w:16});
				
				dom.topCol.makeNode('title', 'div', {text:'MailChimp Integration', css:'ui huge header'});
				
				dom.topCol.makeNode('subTitle', 'div', {text:'Download contacts from your MailChimp account.', css:'ui medium header'});
				
				dom.topCol.makeNode('btns', 'div', {css:'ui buttons'});
				
				dom.topCol.btns.makeNode('back', 'button', {css:'pda-btn-orange', text:'<i class="fa fa-arrow-left"></i> Back'})
					.notify('click', {
						type:'integrations-run',
						data:{
							run:chooseNextStep.bind({}, dom, state, draw)
						}
					}, sb.moduleId);
					
				dom.topCol.btns.makeNode('settings', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-cog"></i> Sync Settings'})
					.notify('click', {
						type:'integrations-run',
						data:{
							run:function(dom, state, draw){
								
								var syncButtonText = '<i class="fa fa-toggle-on"></i> Stay in sync';
								
								dom.topCol.btns.settings.loading();
								
								dom.modals.makeNode('modal', 'modal', {
									onShow:function(){
										
										dom.modals.modal.body.empty();
										
										dom.modals.modal.body.makeNode('title', 'headerText', {text:'Sync Settings', size:'small'});
								
										dom.modals.modal.body.makeNode('table', 'table', {
											css: 'table-hover table-condensed',
											columns: {
												name: 'Setting',
												btns: 'Actions'
											}
										});
										
										dom.modals.modal.body.table.makeRow(
											'syncType',
											['<b>Sync contacts in real time?</b><br /><small>If on, changes you make here will sync with MailChimp automatically.</small>', '']
										);
										
										if(!state.appSettings.apps.integrations.mailchimp){
											state.appSettings.apps.integrations.mailchimp = {};
										}
										
										if(!state.appSettings.apps.integrations.mailchimp.syncSettings){
											state.appSettings.apps.integrations.mailchimp.syncSettings = {
												realTime:'yes'
											};
										}
										
										if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime){
											if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){
												syncButtonText = '<i class="fa fa-toggle-on"></i> Stay in sync';
											}else{
												syncButtonText = '<i class="fa fa-toggle-off"></i> Stay in sync';
											}
										}
										
										dom.modals.modal.body.table.body.syncType.btns.makeNode('on', 'button', {tag:'button', text:syncButtonText, css:'ui button blue basic fluid button'})
											.notify('click', {
												type:'integrations-run',
												data:{
													run:function(dom, state, draw){
														
														if(!state.appSettings.apps.integrations.mailchimp.syncSettings){
															state.appSettings.apps.integrations.mailchimp.syncSettings = {
																realTime:'yes'
															};
														}
														
														if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime){
															if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){
																state.appSettings.apps.integrations.mailchimp.syncSettings.realTime = 'no';
															}else{
																state.appSettings.apps.integrations.mailchimp.syncSettings.realTime = 'yes';
															}
														}
														
														state.appSettings.save(function(){
															
															if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){														
																dom.modals.modal.body.table.body.syncType.btns.on.text('<i class="fa fa-toggle-on"></i> Stay in sync');
															}else{
																dom.modals.modal.body.table.body.syncType.btns.on.text('<i class="fa fa-toggle-off"></i> Stay in sync');
															}
																												
														});
														
													}.bind({}, dom, state, draw)
												}
											}, sb.moduleId);
										
										dom.modals.modal.footer.makeNode('btns', 'div', {css:'ui buttons'});
										
										dom.modals.modal.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save Settings', css:'pda-btn-green'})
											.notify('click', {
												type:'integrations-run',
												data:{
													run:function(dom, state, draw){
														
														dom.modals.modal.footer.btns.save.loading();
														
														if(!state.appSettings.apps.integrations.mailchimp.syncSettings){
															state.appSettings.apps.integrations.mailchimp.syncSettings = {};
															state.appSettings.apps.integrations.mailchimp.syncSettings.realTime = 'yes';
														}
																										
														state.appSettings.save(function(){
																												
															setTimeout(function(){
																
																dom.modals.modal.hide();
																
															}, 300);
															
														});
														
													}.bind({}, dom, state, draw)
												}
											}, sb.moduleId);
										
										dom.modals.modal.body.patch();
										
									}
								});
								
								dom.modals.patch();
								
								dom.modals.modal.show();
								
							}.bind({}, dom, state, draw)
						}
					}, sb.moduleId);
					
				dom.topCol.btns.makeNode('startSync', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-refresh"></i> Start Syncing'})
					.notify('click', {
						type:'integrations-run',
						data:{
							run:function(dom, state, draw){
								
								sb.dom.alerts.ask({
									title: 'Are you sure?',
									text: 'This could take some time. You will need to stay on this page during the first, initial sync operation.'
								}, function(resp){
									
									if(resp){
										
										//dom.btns.startSync.loading();
										swal.disableButtons();
										
										setTimeout(function(){
											
											swal.close();
											//dom.btns.startSync.loading(false);
											
											dom.modals.makeNode('syncModal', 'modal', {
												onShow:function(){

													var modal = dom.modals.syncModal;
													
													modal.body.makeNode('title', 'headerText', {text:'Syncing with MailChimp...', size:'small'});
													modal.body.makeNode('text', 'text', {text:'Please do not close this window'});
													
													modal.footer.makeNode('loading', 'loader', {});
													
													modal.body.patch();
													modal.footer.patch();
													
													var contactTypes = _.map(Object.keys(state.appSettings.apps.integrations.mailchimp.pushToMaps), function(obj){
														return +obj;
													}, []);
													
													sb.data.db.obj.getWhere('contact_types', {
														id:{
															type:'or',
															values:contactTypes
														}
													}, function(contactTypes){
																												
														function pushContactsToMailChimp(callback){
															
															sb.data.db.controller('pushContactsToMailChimp', {}, function(done){
																
																callback(done);
																
															});
															
														}
														
														function pushContactTypes(contactTypes, dom, callback, count, total){
															
															if(!count){
																count = 0;
																total = 0;
																dom.empty();
															}else{
																dom.makeNode('break-'+count, 'lineBreak', {});
															}
															
															if(contactTypes[count]){
																
																var cType = contactTypes[count];
																
																dom.makeNode('type-'+count, 'text', {text:'Counting <b>'+ cType.name +'</b> contacts...', size:'x-small'});
																
																dom.patch();
																
																sb.data.db.obj.getWhere('contacts', {
																	type:cType.id,
																	paged:{
																		count:true,
																		page:0,
																		pageLength:1,
																		paged:true,
																		sortCol:'date_created',
																		sortDir:'desc',
																		sortCast:'string'
																	}
																}, function(allContacts){
																																		
																	total += allContacts.recordsTotal;
																	
																	dom.makeNode('count-'+count, 'text', {text:allContacts.recordsTotal +' contacts to sync.', size:'x-small'});
										
																	dom.patch();
										
																	count++;
																
																	return pushContactTypes(contactTypes, dom, callback, count, total);
																
																});
																
															}else{
																
																dom.makeNode('totalCount', 'headerText', {text:total +' total contacts to sync. Syncing now...', size:'x-small'});
																
																dom.makeNode('loadingBreak', 'lineBreak', {});																
																dom.makeNode('loading', 'loader', {});
																dom.makeNode('time', 'text', {text:'This will take a few minutes to complete.', css:'text-center'});
																dom.makeNode('warn', 'text', {text:'Please do not close this window.', css:'text-center'});
										
																dom.patch();
																
																pushContactsToMailChimp(function(done){
																	
																	callback(done);
																	
																});
																
															}
															
														}
														
														pushContactTypes(contactTypes, modal.footer, function(done){
															
															delete modal.footer.loading;
															delete modal.footer.time;
															delete modal.footer.warn;
															
															modal.footer.makeNode('done', 'headerText', {text:'<i class="fa fa-check"></i>', css:'text-center'});
															
															modal.footer.makeNode('doneText', 'headerText', {text:'Sync done. You can close this window.', size:'small', css:'text-center'});
															
															modal.footer.patch();															
															
														});
														
													});										
													
												}
											});
											
											dom.modals.patch();
											
											dom.modals.syncModal.show();
											
										}, 0);
										
									}
									
								});
								
							}.bind({}, dom, state, draw)
						}
					}, sb.moduleId);		
					
				//dom.makeNode('btnsBreak', 'lineBreak', {spaces:2});
				
				dom.makeNode('col1', 'column', {w:4}).makeNode('cont', 'div', {css:'pda-container'});
				dom.makeNode('col2', 'column', {w:4}).makeNode('cont', 'container',
					{
						uiGrid:false,
						css:'pda-container',
						drag:{
							accepts:function(me, dropped){
								return true;
							},
							data:'return'
						}
					}
				);
				dom.makeNode('col3', 'column', {w:8}).makeNode('cont', 'div', {css:'pda-container'});
				
				dom.col1.cont.makeNode('title', 'headerText', {text:'Select a MailChimp list', css:'ui small header'});
				
				dom.col1.cont.makeNode('titleBreak', 'div', {text:'<br />'});
				
				dom.col1.cont.makeNode('loadingtext', 'text', {text:'Loading Lists...', css:''});
				dom.col1.cont.makeNode('loading', 'loader', {});
				
				dom.makeNode('modals', 'column', {w:16});
									
				dom.patch();
								
				sb.data.db.controller('getMailchimpLists', {}, function(mcLists){
					
					dom.col1.cont.makeNode('btns', 'div', {css:'ui vertical fluid buttons'});
					
					_.each(mcLists.lists, function(mcList){
						
						var buttonColor = 'orange';
						var buttonIcon = '';
						
						if(state.appSettings.apps.integrations.mailchimp.pullFromMaps){
							if(state.appSettings.apps.integrations.mailchimp.pullFromMaps[mcList.id]){
								buttonColor = 'green';
								buttonIcon = '<i class="fa fa-check"></i> ';
							}
						}
						
						dom.col1.cont.btns.makeNode('list-'+ mcList.id, 'div', {tag:'button', css:'ui '+ buttonColor +' button', text:buttonIcon+mcList.name +' <small>('+ mcList.stats.member_count +' members)</small>'});
						
						//dom.col1.cont.btns.makeNode('listBreak-'+mcList.id, 'lineBreak', {});
						
						dom.col1.cont.btns['list-'+mcList.id].notify('click', {
							type:'integrations-run',
							data:{
								run:function(dom, state, draw, mcList){
									
									matchMCMergeFields.call({}, dom, state, draw, mcList);
									
								}.bind({}, dom, state, draw, mcList)
							}
						}, sb.moduleId);
						
					});
					
					delete dom.col1.cont.loadingtext;
					delete dom.col1.cont.loading;
					
					dom.col1.cont.patch();
					
				});
				
			}, 1);
						
		}
		
		function removeConnection(instance, callback){
			
			instance.mailchimp_api = '';
			instance.mailchimp_list_id = '';
			
			sb.data.db.obj.update('instances', instance, function(done){
				
				callback(done);
				
			});
			
		}
		
		function saveContactTypeMap(dom, state, draw, contactType, callback){

			if(map.length == 0){
				
				sb.dom.alerts.alert('No email', 'You must map at least an email address.', 'error');
				return;
				
			}
//console.log('map', map);			
			if(!_.find(map, {mcFieldId:'EMAILADDRESS'})){
				
				sb.dom.alerts.alert('No email', 'You must map at least an email address.', 'error');
				return;
				
			}
			
			dom.mapCont.col2.cont.next.css('pda-btn-primary pull-right');
			dom.mapCont.col2.cont.next.text('Loading <i class="fa fa-circle-o-notch fa-spin"></i>');
			
			state.appSettings.apps.integrations.mailchimp = {
				pushToMaps:{}
			};
			
			state.appSettings.apps.integrations.mailchimp.pushToMaps[contactType.id] = map;
			
			state.appSettings.save(function(done){
				
				callback(done);
				
			});
			
		}
		
		function start(dom, state, draw){
			
			sb.data.db.obj.getWhere('instances', {instance:appConfig.instance}, function(instances){
				
				state.instance = instances[0];
				
				if(instances[0].mailchimp_api){
					
					chooseNextStep(dom, state, draw);
					
				}else{
					
					dom.empty();
			
					dom.makeNode('title', 'div', {text:'MailChimp Integration', css:'ui huge header'});
					
					dom.makeNode('subTitle', 'div', {text:'Please enter your API Key to continue.', css:'ui medium header'});
					
					dom.makeNode('col', 'column', {width:4});
					
					dom.col.makeNode('form', 'form', {
						api:{
							name:'api',
							label:'MailChimp API Key',
							value:'',
							type:'text'
						}
					});
					
					dom.makeNode('formBreak', 'lineBreak', {});
					
					dom.makeNode('button', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
						type:'integrations-run',
						data:{
							run:function(dom, state, draw){
								
								dom.button.loading();
								
								var apiKey = dom.col.form.process().fields.api.value;
								
								sb.data.db.controller('checkMailchimpAPIKeyValidity', {key:apiKey}, function(check){
									
									if(check != false){
										
										sb.data.db.obj.getWhere('instances', {instance:state.instance.instance}, function(instances){
																		
											sb.data.db.obj.update('instances', {id:instances[0].id, mailchimp_api:apiKey}, function(updated){
												
												mailChimpView(dom, state, draw);
												
												swal.close();
												
											});
							
											
										});
										
									}else{
										
										sb.dom.alerts.alert('Error', 'This API Key is invalid.', 'error');
										
										dom.button.loading(false);
										
									}
																		
								});
														
							}.bind({}, dom, state, draw)
						}
					}, sb.moduleId);
					
					draw(dom);
					
				}
				
			});
			
		}
		
		function syncContactsWithList(dom, state, draw){
			
			var map = [];
			
			function matchMCMergeFields(dom, state, draw, ctype, mcList, callback){
				
				dom.col3.cont.empty();

				dom.col3.cont.makeNode('title', 'div', {text:'MailChimp Merge Fields<br /><small>Drag your contact info here</small>', css:'ui small header'});
										
				dom.col3.cont.makeNode('btns', 'div', {css:'ui buttons'});
	
				dom.col3.cont.btns.makeNode('changeList', 'button', {text:'<i class="fa fa-pencil"></i> Change List', css:'pda-btnOutline-orange'})
					.notify('click', {
						type:'integrations-run',
						data:{
							run:function(dom, state, draw, ctype, mcList){
								
								selectMCList(dom, ctype, function(){}, true);
								
							}.bind({}, dom, state, draw, ctype, mcList)
						}
					}, sb.moduleId);
	
				dom.col3.cont.btns.makeNode('saveMap', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'})
					.notify('click', {
						type:'integrations-run',
						data:{
							run:function(dom, state, draw, map, ctype, mcList){
								
								if(map.length == 0 || !_.find(map, {mcFieldId:'EMAILADDRESS'})){
									
									sb.dom.alerts.alert('Error', 'You have to match the email address field.', 'error');
									return;
									
								}
																
								dom.col3.cont.btns.saveMap.loading();
								
								if(!state.appSettings.apps.integrations.mailchimp.pushToMaps){
									state.appSettings.apps.integrations.mailchimp.pushToMaps = {};
								}
								
								state.appSettings.apps.integrations.mailchimp.pushToMaps[ctype.id] = {
									map:map,
									mailchimpListId:mcList.id
								};
																														
								state.appSettings.save(function(done){
									
									sb.dom.alerts.alert('Saved!', 'All set. You can now match another contact type if you want.', 'success');

									dom.col3.cont.btns.saveMap.loading(false);
	
									syncContactsWithList(dom, state, draw);				
									
								});
																						
							}.bind({}, dom, state, draw, map, ctype, mcList)
						}
					}, sb.moduleId);
								
				dom.col3.cont.makeNode('loading', 'loader', {});
				
				dom.col3.patch();

				sb.data.db.controller('getMailchimpListMergeFields', {listId:mcList.id}, function(mergeFields){
					
					mergeFields.merge_fields.unshift({
						tag:'EMAILADDRESS',
						merge_id:0,
						name:'Email Address'
					});
					
					var mcMergeFields = _.reject(mergeFields.merge_fields, function(obj){ return obj.tag == 'FNAME' || obj.tag == 'LNAME'; });
																					
					_.each(mcMergeFields, function(field){
	
						dom.col3.cont.makeNode('field-'+field.merge_id, 'div', {css:'ui orange fluid card'})
							.makeNode('cont', 'div', {css:'content'});
																													
						dom.col3.cont['field-'+field.merge_id].cont.makeNode('text', 'div', {text:field.name +' <small>*|'+ field.tag +'|*</small>', css:'ui mini header'});
						
						dom.col3.cont['field-'+field.merge_id].cont.makeNode('drop', 'container',
							{
								uiGrid:false,
								drag: {
									accepts: function(me, dropped){
										
										// if the dropzone is already occupied						
										if(_.find(map, {mcFieldId:me.field.tag})){
											return false;
										}
										
										return true;
									},
									data: {
										field:field
									}
								}
							});
						
						dom.col3.cont['field-'+field.merge_id].cont.drop
							.makeNode('cont', 'div', {})
								.makeNode('text', 'div', {text:'<small>Drop Contact Info Here</small>', css:'ui mini header'});
																		
					});
					
					delete dom.col3.cont.loading;
					
					dom.col3.patch();
					
				});
				
			}
			
			function selectMCList(dom, ctype, callback, chooseNewList){
				
				map = [];
				
				dom.col3.cont.empty();
				
				dom.col3.cont.makeNode('title', 'div', {text:'MailChimp Lists<br /><small>Choose a list to sync with</small>', css:'ui small header'});

				dom.col3.cont.makeNode('loader', 'loader', {});
				
				dom.col3.cont.makeNode('btns', 'div', {css:'ui vertical fluid buttons'});
				
				dom.col3.patch();
				
				delete dom.col3.cont.loader;
				
				var hasMap = false;
				if(state.appSettings.apps.integrations.mailchimp){
					if(state.appSettings.apps.integrations.mailchimp.pushToMaps){
						if(state.appSettings.apps.integrations.mailchimp.pushToMaps[ctype.id] && !chooseNewList){
							hasMap = true;
						}
					}
				}
				
				if(hasMap === true){
					
					sb.data.db.controller('getMailchimpList', {
						listId:state.appSettings.apps.integrations.mailchimp.pushToMaps[ctype.id].mailchimpListId
					}, function(mcList){
						
						if(!mcList){
							
							sb.data.db.controller('getMailchimpLists', {}, function(mcLists){
					
								_.each(mcLists.lists, function(list){
														
									dom.col3.cont.btns.makeNode('list-'+ list.id, 'div', {tag:'button', text:'<h4>'+list.name +'<br /><small>List members: '+ list.stats.member_count +'</small></h4>', css:'ui blue button'})
										.notify('click', {
											type:'integrations-run',
											data:{
												run:function(dom, state, draw, ctype, mcList){
													
													matchMCMergeFields(dom, state, draw, ctype, mcList, function(loaded){});
													
												}.bind({}, dom, state, draw, ctype, list)
											}
										}, sb.moduleId);
										
									//dom.col3.cont.makeNode('break-'+ list.id, 'lineBreak', {});										
									
								});
								
								dom.col3.patch();
																
							});
							
						}else{
							
							matchMCMergeFields(dom, state, draw, ctype, mcList, function(loaded){});
							
						}
					
					});
					
				}else{
					
					sb.data.db.controller('getMailchimpLists', {}, function(mcLists){
					
						_.each(mcLists.lists, function(list){
												
							dom.col3.cont.btns.makeNode('list-'+ list.id, 'button', {text:'<h4>'+list.name +' <small>List members: '+ list.stats.member_count +'</small></h4>', css:'ui blue basic button'})
								.notify('click', {
									type:'integrations-run',
									data:{
										run:function(dom, state, draw, ctype, mcList){
											
											matchMCMergeFields(dom, state, draw, ctype, mcList, function(loaded){});
											
										}.bind({}, dom, state, draw, ctype, list)
									}
								}, sb.moduleId);
								
							dom.col3.cont.makeNode('break-'+ list.id, 'lineBreak', {});										
							
						});
						
						dom.col3.patch();
						
					});
					
				}
				
			}
			
			sb.data.db.obj.getAll('contact_types', function(contactTypes){
								
				dom.empty();
				
				dom.css('ui grid');
				
				dom.makeNode('topCol', 'column', {w:16});

				dom.topCol.makeNode('title', 'div', {text:'MailChimp Integration', css:'ui huge header'});
				
				dom.topCol.makeNode('subTitle', 'div', {text:'Sync your contacts to lists in your MailChimp account.', css:'ui medium header'});
				
				dom.topCol.makeNode('btns', 'div', {css:'ui buttons'});
				
				dom.topCol.btns.makeNode('back', 'button', {css:'pda-btn-orange', text:'<i class="fa fa-arrow-left"></i> Back'})
					.notify('click', {
						type:'integrations-run',
						data:{
							run:chooseNextStep.bind({}, dom, state, draw)
						}
					}, sb.moduleId);
					
				dom.topCol.btns.makeNode('settings', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-cog"></i> Sync Settings'})
					.notify('click', {
						type:'integrations-run',
						data:{
							run:function(dom, state, draw){
								
								var syncButtonText = '<i class="fa fa-toggle-on"></i> Stay in sync';
								
								dom.topCol.btns.settings.loading();
								
								dom.modals.makeNode('modal', 'modal', {
									onShow:function(){
										
										dom.modals.modal.body.makeNode('title', 'headerText', {text:'Sync Settings', size:'small'});
								
										dom.modals.modal.body.makeNode('table', 'table', {
											css: 'table-hover table-condensed',
											columns: {
												name: 'Setting',
												btns: 'Actions'
											}
										});
										
										dom.modals.modal.body.table.makeRow(
											'syncType',
											['<b>Sync contacts in real time?</b><br /><small>If on, changes you make here will sync with MailChimp automatically.</small>', '']
										);
										
										if(!state.appSettings.apps.integrations.mailchimp){
											state.appSettings.apps.integrations.mailchimp = {};
										}
										
										if(!state.appSettings.apps.integrations.mailchimp.syncSettings){
											state.appSettings.apps.integrations.mailchimp.syncSettings = {
												realTime:'yes'
											};
										}
										
										if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime){
											if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){
												syncButtonText = '<i class="fa fa-toggle-on"></i> Stay in sync';
											}else{
												syncButtonText = '<i class="fa fa-toggle-off"></i> Stay in sync';
											}
										}
										
										dom.modals.modal.body.table.body.syncType.btns.makeNode('on', 'button', {tag:'button', text:syncButtonText, css:'ui button blue basic fluid button'})
											.notify('click', {
												type:'integrations-run',
												data:{
													run:function(dom, state, draw){
														
														if(!state.appSettings.apps.integrations.mailchimp.syncSettings){
															state.appSettings.apps.integrations.mailchimp.syncSettings = {
																realTime:'yes'
															};
														}
														
														if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime){
															if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){
																state.appSettings.apps.integrations.mailchimp.syncSettings.realTime = 'no';
															}else{
																state.appSettings.apps.integrations.mailchimp.syncSettings.realTime = 'yes';
															}
														}
														
														state.appSettings.save(function(){
															
															if(state.appSettings.apps.integrations.mailchimp.syncSettings.realTime == 'yes'){														
																dom.modals.modal.body.table.body.syncType.btns.on.text('<i class="fa fa-toggle-on"></i> Stay in sync');
															}else{
																dom.modals.modal.body.table.body.syncType.btns.on.text('<i class="fa fa-toggle-off"></i> Stay in sync');
															}
																												
														});
														
													}.bind({}, dom, state, draw)
												}
											}, sb.moduleId);
										
										dom.modals.modal.footer.makeNode('btns', 'div', {css:'ui buttons'});
										
										dom.modals.modal.footer.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save Settings', css:'pda-btn-green'})
											.notify('click', {
												type:'integrations-run',
												data:{
													run:function(dom, state, draw){
														
														dom.modals.modal.footer.btns.save.loading();
														
														if(!state.appSettings.apps.integrations.mailchimp.syncSettings){
															state.appSettings.apps.integrations.mailchimp.syncSettings = {};
															state.appSettings.apps.integrations.mailchimp.syncSettings.realTime = 'yes';
														}
																										
														state.appSettings.save(function(){
																												
															setTimeout(function(){
																
																dom.modals.modal.hide();
																
															}, 300);
															
														});
														
													}.bind({}, dom, state, draw)
												}
											}, sb.moduleId);
											
										dom.modals.modal.body.patch();
											
									}
									
								});
								
								dom.modals.patch();
								
								dom.modals.modal.show();
								
							}.bind({}, dom, state, draw)
						}
					}, sb.moduleId);
					
				dom.topCol.btns.makeNode('startSync', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-refresh"></i> Start Syncing'})
					.notify('click', {
						type:'integrations-run',
						data:{
							run:function(dom, state, draw){
								
								sb.dom.alerts.ask({
									title: 'Are you sure?',
									text: 'This could take some time. You will need to stay on this page during the first, initial sync operation.'
								}, function(resp){
									
									if(resp){
										
										//dom.btns.startSync.loading();
										swal.disableButtons();
										
										setTimeout(function(){
											
											swal.close();
											//dom.btns.startSync.loading(false);
											
											dom.modals.makeNode('syncModal', 'modal', {
												onShow:function(){

													var modal = dom.modals.syncModal;
													
													modal.body.makeNode('title', 'headerText', {text:'Syncing with MailChimp...', size:'small'});
													modal.body.makeNode('text', 'text', {text:'Please do not close this window'});
													
													modal.footer.makeNode('loading', 'loader', {});
													
													modal.body.patch();
													modal.footer.patch();
													
													var contactTypes = _.map(Object.keys(state.appSettings.apps.integrations.mailchimp.pushToMaps), function(obj){
														return +obj;
													}, []);
													
													sb.data.db.obj.getWhere('contact_types', {
														id:{
															type:'or',
															values:contactTypes
														}
													}, function(contactTypes){
																												
														function pushContactsToMailChimp(callback){
															
															sb.data.db.controller('pushContactsToMailChimp', {}, function(done){
																
																callback(done);
																
															});
															
														}
														
														function pushContactTypes(contactTypes, dom, callback, count, total){
															
															if(!count){
																count = 0;
																total = 0;
																dom.empty();
															}else{
																dom.makeNode('break-'+count, 'lineBreak', {});
															}
															
															if(contactTypes[count]){
																
																var cType = contactTypes[count];
																
																dom.makeNode('type-'+count, 'text', {text:'Counting <b>'+ cType.name +'</b> contacts...', size:'x-small'});
																
																dom.patch();
																
																sb.data.db.obj.getWhere('contacts', {
																	type:cType.id,
																	paged:{
																		count:true,
																		page:0,
																		pageLength:1,
																		paged:true,
																		sortCol:'date_created',
																		sortDir:'desc',
																		sortCast:'string'
																	}
																}, function(allContacts){
																																		
																	total += allContacts.recordsTotal;
																	
																	dom.makeNode('count-'+count, 'text', {text:allContacts.recordsTotal +' contacts to sync.', size:'x-small'});
										
																	dom.patch();
										
																	count++;
																
																	return pushContactTypes(contactTypes, dom, callback, count, total);
																
																});
																
															}else{
																
																dom.makeNode('totalCount', 'headerText', {text:total +' total contacts to sync. Syncing now...', size:'x-small'});
																
																dom.makeNode('loadingBreak', 'lineBreak', {});																
																dom.makeNode('loading', 'loader', {});
																dom.makeNode('time', 'text', {text:'This will take a few minutes to complete.', css:'text-center'});
																dom.makeNode('warn', 'text', {text:'Please do not close this window.', css:'text-center'});
										
																dom.patch();
																
																pushContactsToMailChimp(function(done){
																	
																	callback(done);
																	
																});
																
															}
															
														}
														
														pushContactTypes(contactTypes, modal.footer, function(done){
															
															delete modal.footer.loading;
															delete modal.footer.time;
															delete modal.footer.warn;
															
															modal.footer.makeNode('done', 'headerText', {text:'<i class="fa fa-check"></i>', css:'text-center'});
															
															modal.footer.makeNode('doneText', 'headerText', {text:'Sync done. You can close this window.', size:'small', css:'text-center'});
															
															modal.footer.patch();															
															
														});
														
													});										
													
												}
											});
											
											dom.modals.patch();
											
											dom.modals.syncModal.show();
											
										}, 0);
										
									}
									
								});
								
							}.bind({}, dom, state, draw)
						}
					}, sb.moduleId);		
					
				//dom.makeNode('btnsBreak', 'div', {text:'<br />'});
				
				dom.makeNode('col1', 'column', {w:4}).makeNode('cont', 'div', {css:'ui vertical fluid buttons'});
				dom.makeNode('col2', 'column', {w:4}).makeNode('cont', 'container',
					{
						uiGrid:false,
						drag:{
							accepts:function(me, dropped){
								return true;
							},
							data:'return'
						}
					}
				);
				dom.makeNode('col3', 'column', {w:8}).makeNode('cont', 'div', {css:''});
				dom.makeNode('finalBreak', 'lineBreak', {spaces:2});
				
				//dom.col1.cont.makeNode('title', 'div', {text:'Select a contact type', css:'ui small header'});
				
				_.each(contactTypes, function(ctype){
					
					var matchLine = 'Start',
						matchColor = 'blue';
					
					if(state.appSettings.apps.integrations.mailchimp){
						if(state.appSettings.apps.integrations.mailchimp.pushToMaps){
							if(state.appSettings.apps.integrations.mailchimp.pushToMaps[ctype.id]){
								matchLine = '<i class="fa fa-check"></i>';
								matchColor = 'green';
							}
						}	
					}
					
					dom.col1.cont.makeNode('button-'+ ctype.id, 'div', {tag:'button', css:'ui '+ matchColor +' button', text:matchLine +' '+ ctype.name})
						.notify('click', {
							type:'integrations-run',
							data:{
								run:function(dom, state, draw, ctype){
									
									//dom.col2.css('pda-border-left');
									
									dom.col2.cont.empty();
									
									dom.col2.cont.makeNode('title', 'div', {text:'Your contact info<br /><small>Drag this info to the right</small>', css:'ui small header'});
									
									_.each(ctype.available_types, function(field){
//console.log(field);											
										dom.col2.cont.makeNode('field-'+field.id, 'container', {uiGrid:false, css:'ui fluid card', drag: {
											moves: true,
											data: {
												type:'new-ingredient',
												field:field,
												instanceId:sb.instanceId,
												run:function(){}
											},
											drop: 'inventory-comp-run',
											accepts: false,
											copy:false,
											drop: function(me, dropped){
														
												if(me === 'return'){
													
													var deleteObj = _.find(map, {pagodaContactInfoId:dropped.field.id});
													
													dom.col3.cont['field-'+deleteObj.mcFieldId].css('ui orange fluid card');
													
													dom.col3.cont['field-'+deleteObj.mcFieldId].cont.drop.cont.makeNode('text', 'div', {text:'<small>Drop Contact Info Here</small>', css:'ui mini header'});
													dom.col3.cont['field-'+deleteObj.mcFieldId].cont.drop.cont.patch();
													
													map = _.reject(map, function(obj){
														
														return obj.pagodaContactInfoId == dropped.field.id;
														
													});
													
												}else{

													if(_.find(map, {pagodaContactInfoId:dropped.field.id})){
														
														var deleteObj = _.find(map, {pagodaContactInfoId:dropped.field.id});

														dom.col3.cont['field-'+deleteObj.mergeId].css('ui orange fluid card');
													
														dom.col3.cont['field-'+deleteObj.mergeId].cont.drop.cont.makeNode('text', 'div', {text:'<small>Drop Contact Info Here</small>', css:'ui mini header'});
														dom.col3.cont['field-'+deleteObj.mergeId].cont.drop.cont.patch();
														
														map = _.reject(map, function(obj){
														
															return obj.pagodaContactInfoId == dropped.field.id;
															
														});
														
													}
													
													dom.col3.cont['field-'+me.field.merge_id].css('ui green fluid card');
													
													dom.col3.cont['field-'+me.field.merge_id].cont.drop.cont.empty();
													dom.col3.cont['field-'+me.field.merge_id].cont.drop.cont.patch();

													map.push({
														mcFieldId:me.field.tag,
														data_type:field.data_type,
														pagodaContactInfoId:dropped.field.id,
														mergeId:me.field.merge_id
													});
													
												}						
												
												return true;
														
											}
										}});
										
										dom.col2.cont['field-'+field.id].makeNode('cont', 'div', {css:'content'});	
																																	
										dom.col2.cont['field-'+field.id].cont.makeNode('text', 'div', {text:field.name, css:'ui mini header', size:'xx-small'});
											
									});
																		
									dom.col2.patch();
									dom.col3.patch();
									
									delete dom.col3.cont.loader;
									
									selectMCList(dom, ctype, function(done){
										
										
										
									});
									
								}.bind({}, dom, state, draw, ctype)
							}
						}, sb.moduleId);
					
				});
				
				dom.makeNode('modals', 'div', {});
								
				dom.patch();
				
			}, 1);
						
		}
		
		start(dom, state, draw);
		
	}
	
	function quickbooksView(dom, state, draw){
		
		function createNewConnection(dom, state, draw){
			
			dom.empty();
			
			dom.makeNode('title', 'headerText', {text:'Setup QuickBooks Integration'});
			
			dom.makeNode('titleBreak', 'lineBreak', {});
			
			dom.makeNode('step1', 'headerText', {text:'1. Connect to your QuickBooks Online account.', size:'x-small'});

			dom.makeNode('step2', 'headerText', {text:'2. Choose the company you want to connect to.', size:'x-small'});

			dom.makeNode('step3', 'headerText', {text:'3. Match your chart of accounts.', size:'x-small'});

			dom.makeNode('step4', 'headerText', {text:'4. Sync your journal entries and chart of accounts.', size:'x-small'});

			dom.makeNode('stepsBreak', 'lineBreak', {});

			dom.makeNode('start', 'headerText', {text:'Get started', size:'small'});
			
			//dom.makeNode('loading', 'container', {});
			
/*
			dom.loading.makeNode('text', 'text', {text:'Loading QuickBooks integration...'});
			dom.loading.makeNode('loading', 'loader', {css:'pull-left'});
*/
			
			dom.makeNode('connectButton', 'text', {text:'<ipp:connectToIntuit></ipp:connectToIntuit>'}).notify('click', {
				type:'integrations-run',
				data:{
					run:function(dom, state, draw){
						
						delete dom.connectButton;
						
						dom.makeNode('loading', 'container', {});
						dom.loading.makeNode('text', 'text', {text:'Waiting for QuickBooks...'});
						dom.loading.makeNode('loader', 'loader', {css:'pull-left'});
						
						dom.patch();
						
						var intervalId = setInterval(function(){
							
							sb.data.db.obj.getAll('instances', function(instances){
										
								state.instance = instances[0];
								
								if(instances[0].quickbooks_refresh_token){
									
									clearInterval(intervalId);
									
									manageConnection(dom, state, draw);
									
								}			
										
							});
							
						}, 5000)						
						
					}.bind({}, dom, state, draw)
				}
			}, sb.moduleId);

			loadScript();
			
			dom.patch();
					
					setTimeout(function(){
						
						intuit.ipp.anywhere.setup({
							grantUrl:  qbOAuthRedirect,
							datasources: {
								quickbooks : true,
								payments : true
							},
							paymentOptions:{
								intuitReferred : true
							}
						});
						
/*
						delete dom.loading.text;
						delete dom.loading.loading;
						
						dom.patch();
*/
						
					}, 3000);
			
/*
			draw({
				dom:dom,
				after:function(dom){
console.log('test2');					
					loadScript();
					
					setTimeout(function(){
						
						intuit.ipp.anywhere.setup({
							grantUrl:  qbOAuthRedirect,
							datasources: {
								quickbooks : true,
								payments : true
							},
							paymentOptions:{
								intuitReferred : true
							}
						});
						
						delete dom.loading.text;
						delete dom.loading.loading;
						
						dom.patch();
						
					}, 3000);
					
					
					
				}
			});
*/
			
		}
		
		function manageConnection(dom, state, draw){
			
			dom.empty();
			
			dom.makeNode('btns', 'buttonGroup', {css:'pull-right'});
			
			dom.btns.makeNode('remove', 'button', {text:'Remove Connection', css:'pda-btn-red'})
				.notify('click', {
					type:'integrations-run',
					data:{
						run:function(dom, state, draw){
							
							sb.dom.alerts.ask({
								title: 'Are you sure?',
								text: 'This cannot be undone.'
							}, function(resp){
								
								if(resp){
									
									swal.disableButtons();
									
									removeConnection(state.instance, function(done){
										
										window.location.reload();
										
									});
									
								}
								
							});
							
						}.bind({}, dom, state, draw)
					}
				}, sb.moduleId);
			
			dom.makeNode('title', 'headerText', {text:'Configure Chart of Accounts'});
			
			draw(dom);
			
		}
		
		function removeConnection(instance, callback){
			
			instance.quickbooks_access_token = '';
			instance.quickbooks_refresh_token = '';
			
			sb.data.db.obj.update('instances', instance, function(done){
				
				callback(done);
				
			});
			
		}
		
		function start(dom, state, draw){
			
			sb.data.db.obj.getAll('instances', function(instances){
										
				state.instance = instances[0];
				
				if(instances[0].quickbooks_refresh_token){
					
					manageConnection(dom, state, draw);
					
				}else{
					
					createNewConnection(dom, state, draw);
					
				}			
						
			});
			
		}
		
		start(dom, state, draw);
		
	}

	function easyPDFCloudView(dom, state, draw) {
			
		dom.empty();
		
		dom.makeNode('title', 'headerText', {text:'Setup easyPDF Cloud Integration'});
		
		dom.makeNode('titleBreak', 'lineBreak', {});

		var adminURL = window.location.origin + '/api/';

		sb.data.db.controller('getEasyPDFCloudAccessToken', {}, function(response) {
			
			var parsedResponse = JSON.parse(response);
			var accessToken = parsedResponse.access_token;

			var fileName = 'test.pdf';
			var fileURL = 'https://secureservercdn.net/192.169.223.13/xzm.edd.myftpupload.com/wp-content/uploads/2020/11/hillside-church-bulletin-matthew-23-13-39.pdf';

			sb.data.db.controller('createEasyPDFCloudJob', {
				accessToken: accessToken, 
				fileName: fileName, 
				fileURL: fileURL
			}, function(response) {

				var parsedResponse = JSON.parse(response);
				var jobID = parsedResponse.jobID;

				var fileName = 'test2.pdf';
				var fileURL = 'https://secureservercdn.net/192.169.223.13/xzm.edd.myftpupload.com/wp-content/uploads/2020/10/hillside-church-bulletin-matthew-20-17-34.pdf';

				sb.data.db.controller('addFileToEasyPDFCloudJob', {
					accessToken: accessToken,
					jobID: jobID,
					fileName: fileName, 
					fileURL: fileURL
				}, function(response) {

					sb.data.db.controller('startEasyPDFCloudJob', {
						accessToken: accessToken, 
						jobID: jobID
					}, function(response) {

						sb.data.db.controller('waitOnEasyPDFCloudJob', {
							accessToken: accessToken, 
							jobID: jobID
						}, function(response) {
							
							sb.data.db.controller('downloadEasyPDFCloudFile', {
								accessToken: accessToken, 
								jobID: jobID
							}, function(response) {

								var parsedResponse = JSON.parse(response);
								var fileName = parsedResponse.fileName;

								// View the file
								window.open('http://localhost:8080/api/' + fileName);
	
								sb.data.db.controller('deleteEasyPDFCloudJob', {
									accessToken: accessToken, 
									jobID: jobID
								}, function(response) {
		
								});
	
							});

						});

					});

					// sb.data.db.controller('getEasyPDFCloudJob', {
					// 	accessToken: accessToken, 
					// 	jobID: jobID
					// }, function(response) {

					// 	var parsedResponse = JSON.parse(response);
					// 	console.log(parsedResponse);

					// });

				});

			});
			
				
		}, adminURL + '_getAdmin.php?do=');
		
		dom.patch();
		
	}
	
	function startApplicationSettings(user, callback){

		getApplicationSettings(user, function(appSettings){

			getUserSettings(user, function(userSettings){

				_.each(userSettings.settings, function(setting, settingName){
					
					if(Array.isArray(setting)){
						
						userSettings.settings[settingName] = {};

					}
					
				});
				
				_.each(appSettings.settings, function(setting, settingName){
					
					if(Array.isArray(setting)){
						
						appSettings.settings[settingName] = {};

					}
					
				});	
									
				callback({
					user:userSettings.settings,
					apps:appSettings.settings,
					save:function(callback){

						var settingsObj = this;

						sb.data.db.obj.update('view_settings', {id:settingsObj.appId, settings:settingsObj.apps}, function(appsDone){

							sb.data.db.obj.update('view_settings', {id:settingsObj.userId, settings:settingsObj.user}, function(userDone){

								if(callback){
									callback(true);
								}
	
							});

						});
				
					}.bind({
						userId:userSettings.id,
						appId:appSettings.id,
						user:userSettings.settings,
						apps:appSettings.settings
					})
				});	
				
			});
			
		});
		
	}
	
	return {
		
		init: function() {
			
			sb.listen({
				'integrations-run':this.run
			});
			
			comps.workflows = sb.createComponent('workflows');

			var settings = [
				{
					object_type:'project_types',
					name:'MailChimp',
					action:function(dom, blueprint){
						
						sb.data.db.obj.getById('users', +sb.data.cookie.userId, function(user){
						
							startApplicationSettings(user, function(settings){

								dom.empty();
							
								dom.makeNode('cont', 'div', {css:'ui basic clearing segment'});
								dom.makeNode('clear', 'div', {text:'<br /><br />'});
								
								dom.patch();
								
								mailChimpView(dom.cont, {
									appSettings:settings
								}, function(done){
									
									dom.cont.patch();
									
								});
								
							});
						
						});
						
					}
				},
				{
					object_type:'project_types2',
					name:'Quickbooks',
					action:function(dom, blueprint){
						
						sb.data.db.obj.getById('users', +sb.data.cookie.userId, function(user){
						
							startApplicationSettings(user, function(settings){

								dom.empty();
							
								dom.makeNode('cont', 'div', {css:'ui basic clearing segment'});
								dom.makeNode('clear', 'div', {text:'<br /><br />'});
								
								dom.patch();
								
								quickbooksView(dom.cont, {
									appSettings:settings
								}, function(done){
									
									dom.cont.patch();
									
								});
								
							});
						
						});
						
					}
				},
			];

			if (appConfig.instance === 'rickyvoltz' || appConfig.instance === 'voltzsoftware') {
				
				settings.push({
					object_type:'project_types3',
					name:'easyPDF Cloud',
					action:function(dom, blueprint){
						
						sb.data.db.obj.getById('users', +sb.data.cookie.userId, function(user){
						
							startApplicationSettings(user, function(settings){

								dom.empty();
							
								dom.makeNode('cont', 'div', {css:'ui basic clearing segment'});
								dom.makeNode('clear', 'div', {text:'<br /><br />'});
								
								dom.patch();
								
								easyPDFCloudView(dom.cont, {
									appSettings:settings
								}, function(done){
									
									dom.cont.patch();
									
								});
								
							});
						
						});
						
					}
				});

			}
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'integrations',
						title: 'Integrations',
						icon: '<i class="fa fa-plug"></i>',
						views: [
/*
							{
								id: 'quickbooks',
								type: 'custom',
								default: true,
								title: 'QuickBooks',
								icon: '<i class="fa fa-usd"></i>',
								dom: quickbooksView
								
							},
*/
							{
								id: 'mailchimp',
								type: 'custom',
								default: true,
								title: 'MailChimp',
								icon: '<i class="fa fa-envelope"></i>',
								dom: mailChimpView
								
							},
							{
								id:'integrations',
								type:'hqTool',
								name:'Integrations',
								tip:'Manage third party integrations.',
								icon: {
									type: 'user',
									color: 'yellow'
								},
								settings:settings,
								default:true,
								mainViews:false
							}
						]
					}
				}
			});
						
		},
		
		run: function(data) {
			
			data.run(data);
			
		}
		
	}
	
});