Factory.register('schedulingWeek', function(sb) {
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'schedulingWeek',
						title: 'Schedule Week View',
						icon: '<i class="fa fa-table"></i>',
						views: [
							{
								id: 'table',
								default: true,
								type: 'custom',
								title: 'Scheduling Week View',
								icon:'<i class="fa fa-th-table"></i>',
								dom: function(dom, state, draw) {
									
									sb.notify({
										type: 'start-scheduling-week',
										data: {
											dom: dom,
											state: state,
											draw: draw
										}
									});
									
								}
							}
						]
					}
				}
			});
			
		},
		
		destroy: function() {}
		
	}
	
});