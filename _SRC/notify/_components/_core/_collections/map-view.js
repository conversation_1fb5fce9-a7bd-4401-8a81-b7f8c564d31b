Factory.register('map', function(sb){
	
	function table_ui(ui, list, options){
console.log('list',list);
		var currentSelection = [];
		var allSelected = false;


		///DEV TEST FOR GROUP BY 
		options.groupBy = null;

		function table_layout(ui, list, options, group){

			function metrics_ui (ui, list, options) {

				if (!_.isEmpty(options.metrics)) {

					_.each(options.metrics, function(metric, key){
						
						var row = body.makeNode('m-'+ key, 'div', {tag:'tr'});
						
						row.makeNode('actions', 'div', {
							tag:'td',
							css:'center aligned',
							text:'<strong>'+ metric.title +'</strong>'
						});
						
						if( options.actions ) {
							
							row.makeNode('chck', 'div', {tag:'td'});
							
						}
						
						_.each(options.fields, function(field, key){
							
							var metricFunc = metric.fields[key];
							if (typeof metricFunc === 'function') {
								
								metricFunc(row.makeNode('m-'+ key, 'div', {
									tag:'td',
									css:'center aligned',
									text:'<i class="notched circle loading icon"></i>'
								}), list);
								
							} else {
								
								row.makeNode('m-'+ key, 'div', {
									tag:'td',
									css:'center aligned',
									text:''
								});
								
							}
							
						});
						
					});
					
				}
				
			}
			
			var groupLabel = '';
			var onMobile = false;
			var displayNone = '';
			var tableCSS = '';

			if(options.hasOwnProperty('subviews') && options.subviews !== undefined) {
				
				if(options.subviews.hasOwnProperty('table')) {
					
					if(options.subviews.table.hasOwnProperty('css')) {
						
						tableCSS = options.subviews.table.css;
						
					}
					
				}
				
			}
			
			if($(window).width() < 769) {
				onMobile = true;
				displayNone = 'display: none !important;'
			}
			
			if(group) {
				groupLabel = `<h4 class="ui header">${group}</h4>`;	
			}
			
			var table = ui.makeNode(
				'table', 
				'div', {
					css:			'ui ' + tableCSS + ' very basic celled compact sortable table'
					, tag:		'table'
					, style: 	'padding-bottom:100px;'
				});
				
			var headerRow = table.makeNode(
				'thead', 
				'div', { 
					tag:'thead'
				}).makeNode('row', 'div', {tag:'tr', style: displayNone});	
				
			
			var body = table.makeNode(
				'tbody', 
				'div', { 
					tag:'tbody' 
				});			
			
			if( options.actions ) {
				
				headerRow.makeNode('actions', 'div', {
					tag:'th',
					text:groupLabel,
					css:'center aligned one wide',
					style:'background-color: transparent !important;'
				});
				
			}
			
			if(options.fields.hasOwnProperty('select')) {
				
				if(options.fields.select.view() !== false) {
					
					headerRow.makeNode('select', 'div', {
						tag:'th', 
						text:'', 
						css:'center aligned one wide',
						style:'background-color: transparent !important;'
					}).makeNode('check', 'div', {
						css:'ui checkbox',
						text:'<input type="checkbox">',
						listener:{
							type:'checkbox',
							onChange:function(){
								
								allSelected = !allSelected;
								if(allSelected){
									currentSelection = _.pluck(list, 'id');
								}else{
									currentSelection = [];
								}
								
								_.each(list, function(item){
									
									var evt = ( allSelected ) ? 'set checked' : 'set unchecked';
									
									if(body['row-'+ item.id]){
										
										if(allSelected){
											body['row-'+ item.id].selection.checkbox.checkbox(evt);
										}else{
											body['row-'+ item.id].selection.checkbox.checkbox(evt);
										}
										
									}
									
								});
								
								options.updateSelection(currentSelection);
								
							}
						}
					});					
					
				}
				
			} else {
				
				headerRow.makeNode('select', 'div', {
					tag:'th', 
					text:'', 
					css:'center aligned one wide',
					style:'background-color: transparent !important;'
				}).makeNode('check', 'div', {
					css:'ui checkbox',
					text:'<input type="checkbox">',
					listener:{
						type:'checkbox',
						onChange:function(){
							
							allSelected = !allSelected;
							if(allSelected){
								currentSelection = _.pluck(list, 'id');
							}else{
								currentSelection = [];
							}
							
							_.each(list, function(item){
								
								var evt = ( allSelected ) ? 'set checked' : 'set unchecked';
								
								if(body['row-'+ item.id]){
									
									if(allSelected){
										body['row-'+ item.id].selection.checkbox.checkbox(evt);
									}else{
										body['row-'+ item.id].selection.checkbox.checkbox(evt);
									}
									
								}
								
							});
							
							options.updateSelection(currentSelection);
							
						}
					}
				});
				
			}

			_.each(options.fields, function(field, name){
				
				if(field.title !== undefined) {
					
					var pullIconRight = 'pull-right';
					var headerText = field.title;
					
					if (options.entity_type) {
						pullIconRight = '';
					}
					var sortArrow = '<i class="grey sort amount down '+ pullIconRight +' icon"></i>';
					
					if(field.sort === 'asc'){
						sortArrow = '<i class="teal up arrow pull-right '+ pullIconRight +' icon"></i>';
					}else if(field.sort === 'desc'){
						sortArrow = '<i class="teal down arrow pull-right '+ pullIconRight +' icon"></i>';
					}
					
					headerRow.makeNode(name, 'div', {
						tag:'th',
						style:'background-color: transparent !important;'
					});
					
					if (options.entity_type) {
						
						sb.notify ({
							type: 'view-field'
							, data: {
								type: 		'field'
								, property: 	name
								, obj:		options.entity_type
								, options: 	{
	// 								edit: 		options.editBlueprint
									edit: 		true
									, title: 	field.title
									, onUpdate: function (updated) {
										
										options.refresh(false, updated);
										
									}
								}
								, ui: 		headerRow[name]
							}
						}) ;
						
						headerRow[name].makeNode(
							'sortBtn', 
							'div', {
								text: sortArrow,
								style: 'display:inline-block;'
							}).notify('click', {
								type:'table-view-run',
								data:{
									run:function(fieldName){
										
										var btn = this;
										
										options.toggleSort(fieldName);
										
									}.bind({}, name)
								}
							}, sb.moduleId) ;
						
					} else {
						
						headerRow[name].makeNode(
							'sortBtn', 
							'div', {
								text: headerText +' '+ sortArrow,
							}).notify('click', {
								type:'table-view-run',
								data:{
									run:function(fieldName){
										
										var btn = this;
										
										options.toggleSort(fieldName);
										
									}.bind({}, name)
								}
							}, sb.moduleId) ;
						
					}
					
				}
				
			});			
			
			_.each(list, function(item, i){
				
				var delayCss = 'item-fade-in-'+ Math.floor(Math.random() * Math.floor(3));
				var row = body.makeNode('row-'+ item.id, 'div', { tag:'tr', css:'revealParent '+ delayCss });
					
				if ( options.actions ) {
					
					options.actions_ui(
					
						row.makeNode('actions', 'div', {
							tag:'td',
							css:'center aligned'
						}),
						
						item
					
					);
					
				}				
				
				if(options.fields.hasOwnProperty('select')) {
					
					if(options.fields.select.view() !== false) {
						
						row.makeNode('selection', 'div', {tag:'td', css:'center aligned'});
						row.selection.makeNode('checkbox', 'div', {
							css: 'ui checkbox', 
							text:'<input type="checkbox"><label></label>',
							listener:{
								type:'checkbox',
								onChange:function(itemId){
									
									if( _.contains(currentSelection, itemId) ){
										currentSelection = _.without(currentSelection, itemId);
									}else{
										currentSelection.push(itemId);
									}
									
									options.updateSelection(currentSelection);
									
								}.bind({}, item.id)
							}
						});	
						
					}
					
				} else {
					
					row.makeNode('selection', 'div', {tag:'td', css:'center aligned'});
					row.selection.makeNode('checkbox', 'div', {
						css: 'ui checkbox', 
						text:'<input type="checkbox"><label></label>',
						listener:{
							type:'checkbox',
							onChange:function(itemId){
								
								if( _.contains(currentSelection, itemId) ){
									currentSelection = _.without(currentSelection, itemId);
								}else{
									currentSelection.push(itemId);
								}
								
								options.updateSelection(currentSelection);
								
							}.bind({}, item.id)
						}
					});
					
				}
				
				_.each(options.fields, function(field, name){

					if(field.title !== undefined) {
						
						if(field.view){
						
							if(field.view(
								
								row.makeNode(name, 'div', {
									tag:'td'
								}),
								
								item
								
							) === false && onMobile === true) {
								
								delete row[name];
									
							} 
							
						} else{
							
							row.makeNode(name, 'div', {
								tag:'td',
								text:item[name]
							});
							
						}	
						
					}
					
				});
				
			});
			
			metrics_ui (body, list, options);
			
		}
		
		function build_placeholder(ui) {
			
			var field_amount = _.size(options.fields) + 2;
			
			ui.makeNode('grid1', 'div', {
				css: 'ui equal width grid'
			});
			ui.makeNode('grid2', 'div', {
				css: 'ui equal width grid'
			});
			ui.makeNode('grid3', 'div', {
				css: 'ui equal width grid'
			});
			ui.makeNode('grid4', 'div', {
				css: 'ui equal width grid'
			});
			
			ui.grid1.makeNode('row', 'div', {
				css: field_amount + ' column row'
			});
			ui.grid2.makeNode('row', 'div', {
				css: field_amount + ' column row'
			});
			ui.grid3.makeNode('row', 'div', {
				css: field_amount + ' column row'
			});
			ui.grid4.makeNode('row', 'div', {
				css: field_amount + ' column row'
			});
			
			for(var i = 1; i <= field_amount; i++) {
				
				// Grid 1
				ui.grid1.row.makeNode('col' + i, 'div', {
					css: 'column ui fluid placeholder'
				});
				
				ui.grid1.row['col'+i].makeNode('header', 'div', {
					css: 'header'
				});
				
				ui.grid1.row['col'+i].header.makeNode('p1', 'div', {
					css: 'line'
				});
				
				// Grid 2
				ui.grid2.row.makeNode('col' + i, 'div', {
					css: 'column ui fluid placeholder'
				});
				
				if(i === 1 || i === 2) {
					
					ui.grid2.row['col'+i].makeNode('para', 'div', {
						css: 'image header'
					});
					
					ui.grid2.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					
				} else {
					
					ui.grid2.row['col'+i].makeNode('para', 'div', {
						css: 'header'
					});
					
					ui.grid2.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					ui.grid2.row['col'+i].para.makeNode('p2', 'div', {
						css: 'line'
					});	
					
				}
				
				// Grid 3
				ui.grid3.row.makeNode('col' + i, 'div', {
					css: 'column ui fluid placeholder'
				});
				
				if(i === 1 || i === 2) {
					
					ui.grid3.row['col'+i].makeNode('para', 'div', {
						css: 'image header'
					});
					
					ui.grid3.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					
				} else {
					
					ui.grid3.row['col'+i].makeNode('para', 'div', {
						css: 'header'
					});
					
					ui.grid3.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					ui.grid3.row['col'+i].para.makeNode('p2', 'div', {
						css: 'line'
					});	
					
				}
				
				// Grid 4
				ui.grid4.row.makeNode('col' + i, 'div', {
					css: 'column ui fluid placeholder'
				});
				
				if(i === 1 || i === 2) {
					
					ui.grid4.row['col'+i].makeNode('para', 'div', {
						css: 'image header'
					});
					
					ui.grid4.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					
				} else {
					
					ui.grid4.row['col'+i].makeNode('para', 'div', {
						css: 'header'
					});
					
					ui.grid4.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					ui.grid4.row['col'+i].para.makeNode('p2', 'div', {
						css: 'line'
					});
					
				}
				
			}
			
		}
		
		ui.makeNode('seg', 'div', {css:'', id:'mapCont'});
		ui.seg.makeNode('seg', 'div', {
			css: '',
			id:'mapArea',
			style:'height:600px;'
		});
		
		ui.patch();
		
		var map;
		var infoWindow;
		var geocoder;
		var addresses =[];
		
		_.each(list, function(obj){
			
			_.each(obj.contact_info, function(info){
				
				if(info.type.data_type == 'address'){
					addresses.push(info.street+' '+info.city+' '+info.state+' '+info.zip);
				}
				
			});
			
		});
		
	      initMap = function() {
		  
		 	 geocoder = new google.maps.Geocoder();
		 	 map = new google.maps.Map(document.getElementById('mapArea'), {
		          center: {lat: 39.8283, lng: 98.5795},
		          zoom: 4
		        });
		        infoWindow = new google.maps.InfoWindow;

	        // Try HTML5 geolocation.
	        if (navigator.geolocation) {
	          navigator.geolocation.getCurrentPosition(function(position) {
	            var pos = {
	              lat: position.coords.latitude,
	              lng: position.coords.longitude
	            };
	
	            infoWindow.setPosition(pos);
	            infoWindow.setContent('You are here.');
	            //infoWindow.open(map);
	            map.setCenter(pos);
	          }, function() {
	            handleLocationError(true, infoWindow, map.getCenter());
	          });
	        } else {
	          // Browser doesn't support Geolocation
	          handleLocationError(false, infoWindow, map.getCenter());
	        }
	      }
	      
	      function codeAddress(address) {
		   // var address = 'nashville, tn';
		    geocoder.geocode( { 'address': address}, function(results, status) {
console.log(addresses);
		      if (status == 'OK') {
		        //map.setCenter(results[0].geometry.location);
		        var marker = new google.maps.Marker({
		            map: map,
		            position: results[0].geometry.location
		        });
		        //var marker = new google.maps.Marker({position: uluru, map: map});
		      } else {
		        alert('Geocode was not successful for the following reason: ' + status);
		      }
		    });
		  }
	      
	      function handleLocationError(browserHasGeolocation, infoWindow, pos) {
	        infoWindow.setPosition(pos);
	        infoWindow.setContent(browserHasGeolocation ?
	                              'Error: The Geolocation service failed.' :
	                              'Error: Your browser doesn\'t support geolocation.');
	        infoWindow.open(map);
	      }
	      
	      
	      
	      //initMap();
	    
	    var script = document.createElement('script');
		script.onload = function(){
			
			_.map(addresses, codeAddress);
			
		};
		script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCkexkHSAogKm1Joow20qsnGgMyMZ05boI&callback=initMap';
		document.getElementById('mapCont').appendChild(script);
		      
	    
		
		
		// !TESTING HERE
		/*
ui.makeNode('tst', 'div', {
			text:'test'
			, tag: 'button'
		}).notify('click', {
			type: 'table-view-run'
			, data: {
				run:function () {
					console.log('clicked!', options);
					options.setOptions({
						fields: {
							name: {
								type: 'title'
								, title: 'NAME'
							}
						}
						, objectType: 'companies'
						, where: {
							tagged_with: parseInt(sb.data.cookie.userId)
						}
					});
					
				}
			}
		}, sb.moduleId);
*/
		
		/*
if(options.isLoading === true) { // Pre data state
			
			build_placeholder(ui.seg);
			
		} else {
			
			
			
		}
*/

	}
	
	return {
		
		init:function(){
			
/*
			sb.notify({
				type:'register-collection-view',
				data:{
					default:true,
					icon:'list',
					id:sb.moduleId,
					name:'map',
					options:{},
					title:'Map',
					view:table_ui
				}
			});
			
			sb.listen({
				'table-view-run':this.run
			});
*/
			
		},
		
		load:function(){},
		
		destroy:function(){},
		
		run:function(data){
			
			data.run();
			
		}
		
	}
	
});