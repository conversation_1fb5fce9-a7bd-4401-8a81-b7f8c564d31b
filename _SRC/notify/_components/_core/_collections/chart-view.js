Factory.register('chart-view', function (sb) {

	function View (container, list, options) {

		function get_data (propToAgg, propToGroupBy, callback, dateProp) {

			options.getSum(propToAgg, propToGroupBy, function (response) {
				
				callback(response, showEmptyMessage);
				
			}, dateProp);

			options.getDataByRange()
			
		}

		function getChartTypeDisplayTxt (type) {
			
			return '<i class="'+ type.icon +' icon"></i> '+ type.title;
			
		}
		
		function showEmptyMessage (ui, msg) {
			
			if (!msg) {
				msg = 'No data in this range to display. Try broadening the selected time range/filters.';
			}
			
			ui.empty();
			ui.makeNode('msg', 'div', {
				css: 'ui secondary message'
				, text: '<i class="ui exclamation icon"></i>'+ msg
			});
			ui.patch();
			
		}
		
		// views
		
		function showChart (ui, list, options, legend) {

			switch (viewState.type.name) {
				
				case 'bar':
					showBarChart (
						ui
						, list
						, options
						, legend
					);
					break;
				
				case 'pie':
					showPieChart (
						ui
						, list
						, options
						, legend
					);
					break;
				
				case 'line':
					showLinePlot (
						ui
						, list
						, options
						, legend
					);
					break;
					
				case 'bar_byValue':
					showBarChart_byValue(
						ui, 
						list,
						options,
						legend	
					);
					break;

				case 'custom':
					options.subviews.chart.defaults.chartDisplay(
						ui,
						list,
						options,
						legend
					);
					break;
			}
			
		}
		
		function showBarChart (ui, list, options, legend) {
			
			showGroupingChart(ui.chart, list, options, legend, 'bar');
			
		}
		
		function showBarChart_byValue(ui, list, options, legend) {

			var xFieldOpts = {};
			var yFieldOpts = {};
			var chartUI    = ui.chart;
			
			function draw_chart (ui, list, settings) {

				function draw_table (ui, chartSetup, rawData, typeLabel) {

					var tableTotalValue = 0;
					var tableTotalCount = 0;
					
					ui.makeNode(
						't'
						, 'div'
						, {
							tag: 'table'
							, css: 'ui striped table'
						}
					);
					
					ui.t.makeNode('thead', 'div', {tag:'thead'});
					ui.t.thead.makeNode('tr', 'div', {tag:'tr'});
					ui.t.thead.tr.makeNode('date', 'div', {tag:'th', text:'Date'});
					ui.t.thead.tr.makeNode('value', 'div', {tag:'th', text:'Value'});
					ui.t.thead.tr.makeNode('total', 'div', {tag:'th', text:'Total'});
					
					ui.t.makeNode('tbody', 'div', {tag:'tbody'});
	
					_.each(chartSetup.data.datasets[0].data, function (row, i) {
						
						if (!row.y) { return; }
						
						var dateTxt = '';
						var valueTxt = '';

						tableTotalCount += rawData[i].grouped_total;
						
						ui.t.tbody.makeNode(
							'r'+ i
							, 'div'
							, {
								tag: 'tr'
							}
						);
						
						// Date
						if (moment.isMoment(row.x)) {

							dateTxt = row.x.local().format('MMM DD, YYYY');
							
						} else if (moment(row.x).isValid()) {
							
							dateTxt = moment(row.x).local().format('MMM DD, YYYY');
							
						} else {
						
							return;
							
						}

						ui.t.tbody['r'+ i]
							.makeNode(
								'd'
								, 'div'
								, {
									tag: 		'td'
									, text: 	dateTxt
								}
							);
						
						// Value
						if (row.y) {
							
							valueTxt = '$ '+ (row.y/100).formatMoney();
							
						} else {
						
							valueTxt = '$ 0.00';
							
						}
						
						tableTotalValue += row.y/100;
						
						ui.t.tbody['r'+ i]
							.makeNode(
								'v'
								, 'div'
								, {
									tag: 		'td'
									, text: 	valueTxt
								}
							);
							
						// Count
						ui.t.tbody['r'+ i]
							.makeNode(
								'c'
								, 'div'
								, {
									tag: 		'td'
									, text: 	''+ rawData[i].grouped_total +' '+ typeLabel +''
								}
							);
						
					});

					ui.t.makeNode('tfoot', 'div', {tag:'tfoot'});
					ui.t.tfoot.makeNode('tr', 'div', {tag:'tr'});
					ui.t.tfoot.tr.makeNode('date', 'div', {tag:'th', text:''});
					ui.t.tfoot.tr.makeNode('value', 'div', {tag:'th', text:'$'+tableTotalValue.formatMoney()});
					ui.t.tfoot.tr.makeNode('total', 'div', {tag:'th', text:tableTotalCount +' '+ typeLabel});
					
				}
				
				function get_chartGroupbyType(options) {
					
					var chartGroupbyType = '';
					
					if (
						typeof options.query.range === 'object'
						&& options.query.range.hasOwnProperty('start')
					) {
						
						var daysInRange = moment(options.query.range.end)
											.diff(moment(options.query.range.start), 'days');
						
						if (daysInRange < 2) {
							chartGroupbyType = 'hour';
						} else if (daysInRange < 31) {
							chartGroupbyType = 'day';
						} else {
							chartGroupbyType = 'month';
						}
						
					} else {
						
						switch(options.query.range) {
							
							case 'this_year':
										
								chartGroupbyType = 'month';
								
								break;
								
							case 'today':
										
								chartGroupbyType = 'hour';
	
								break;
								
							case 'this_week':
							case 'this_month':
										
								chartGroupbyType = 'day';
	
								break;
							
						}
						
					}
					
					return chartGroupbyType;
					
				}
				
				function get_timeRange(options, data) {

					if (
						typeof options.query.range === 'object'
						&& options.query.range.hasOwnProperty('start')
					) {
	
						data.push({
							x: moment(options.query.range.start).format('YYYY-MM-DD HH:mm:ss.SS'),
							y: 0
						});

						data.push({
							x: moment(options.query.range.end).format('YYYY-MM-DD HH:mm:ss.SS'),
							y: 0
						});
						
					} else {
						
						switch(options.query.range) {
									
							case 'this_year':
								
								data.push({
									x: moment().month("January").format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
	
								data.push({
									x: moment().month("December").format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
								
								break;
								
							case 'today':
								
								data.push({
									x: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
								
								data.push({
									x: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
	
								break;
								
							case 'this_week':
								
								data.push({
									x: moment().startOf('week').format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
								
								data.push({
									x: moment().endOf('week').format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
	
								break;
								
							case 'this_month':
								
								data.push({
									x: moment().startOf('month').startOf('day').format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
								
								data.push({
									x: moment().endOf('month').endOf('day').format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
								
								break;
								
							default:
							
								data.push({
									x: moment().month("January").format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
	
								data.push({
									x: moment().month("December").format('YYYY-MM-DD HH:mm:ss.SS'),
									y: 0
								});
							
								return; 
							
						}
						
					}
					
					return data;
					
				}

				get_data (
					settings.x
					, settings.y
					, function (response, error) {

						if (_.isEmpty(response)) {
							
							error(ui);
							
						} else {
								
							var data = [];
							var isOverTime = false;
							var itemCount = 0;
							
							if (
								xFieldOpts[settings.x]
								&& xFieldOpts[settings.x].type === 'date'
							) {
								isOverTime = true;
							}

							_.each(response, function (item) {

								itemCount += item.grouped_total;

								if (isOverTime) {

									data.push({
										x: 		moment(item.grouped)
										, y: 	Number(item[settings.aggregate])
									});
									
								} else {
									
									data.push({
										x: 		parseInt(item.grouped)
										, y: 	Number(item[settings.aggregate])
									});
									
								}
								
							});

							data = get_timeRange(options, data);

							var xFieldTxt = '';
							if (
								xFieldOpts[settings.x]
								&& xFieldOpts[settings.x].title
							) {
								xFieldTxt = xFieldOpts[settings.x].title;
							} else if (
								typeof settings.x === 'object' && settings.x.hasOwnProperty('num')
							) {
								xFieldTxt = xFieldOpts[settings.x.num].title +' / '+ xFieldOpts[settings.x.div].title;
							}
							
							data = _.sortBy(data, 'x');

							var labels = _.pluck(data, 'x');
							var chartSetup = {
							    type: 'bar'
							    , data: {
								    labels: labels,
							        datasets: [{
							            label: yFieldOpts[settings.y].title +' vs '+ xFieldTxt
							            , backgroundColor: 'rgba(119, 114, 255)'
							            , data: data
							        }],
							        options: {
								        zoom: false
							        }
							    }
							    , options: {
								    zoom: {
									    enabled: false
								    }
								    , tooltips: {
						                callbacks: {
							                label: function(tooltipItem, data) {

							                    var label = data.datasets[tooltipItem.datasetIndex].label || '';
							
							                    if (label) {
							                        label += ': ';
							                    }
							                    label += '$ ' +(tooltipItem.yLabel/100).formatMoney();
							                    return label;
							                }
							            }
								    }
							        , scales: {
							            xAxes: [{
							                type: 'linear'
											, time: {
												unit: 'day'
											}
							                , position: 'bottom'
							                ,  ticks: {
								              beginAtZero: true
								           }
								           , labelOffset: 0						      
							            }]
							            , yAxes: [{
								             ticks: {
									            callback: function(value) {
													return '$ ' +(value/100).formatMoney();
								                }
								                , beginAtZero: false
								            }
							            }]
							        }
							    }
							};

							if (isOverTime) {

								chartSetup.options.scales.xAxes[0].type = 'time';
								chartSetup.options.scales.xAxes[0].unit = 'day';
								
							}
							
							ui.makeNode('chart', 'chart', chartSetup);
							
							var objType = 'items';
  
							if(!_.isEmpty(list)) {
								
								objType = list[0].object_bp_type;
								
								if(objType === 'groups') {
									objType = list[0].group_type + '(s)';
								}
								
							} 
							
							// Table
							draw_table(
								ui.makeNode(
									'table'
									, 'div'
									, {css:'ui padded basic segment'}
								)
								, chartSetup
								, response
								, objType
							)
							
							ui.patch();

							var valueArr = _.pluck(data, 'y');
							var totalValue = 0;
							
							valueArr = _.filter(valueArr, function(v) {
								return !_.isNaN(v);
							});

							totalValue = _.reduce(valueArr, function(memo, value) {
								return memo + value;	
							}, 0);

							metricsUI.empty();
							
							metricsUI.makeNode('cont', 'div', {css:'ui raised padded segment'});
							
							metricsUI.cont.makeNode('statistics', 'div', {
								css: 'ui small statistics'
							});
							
							metricsUI.cont.statistics.makeNode('statistic1', 'div', {
								css: 'statistic'
							});
							metricsUI.cont.statistics.makeNode('statistic2', 'div', {
								css: 'statistic'
							});
							
							metricsUI.cont.statistics.statistic1.makeNode('val1', 'div', {
								css: 'value',
								text: itemCount
							});
							metricsUI.cont.statistics.statistic1.makeNode('label1', 'div', {
								css: 'label',
								text: objType
							});
							
							metricsUI.cont.statistics.statistic2.makeNode('val2', 'div', {
								css: 'value',
								text: '$' + (totalValue/100).formatMoney()
							});
							metricsUI.cont.statistics.statistic2.makeNode('label2', 'div', {
								css: 'label',
								text: 'Total Value'
							})
							
							metricsUI.patch();
						
						}
						
					}
					, {
						dateField: settings.x,
						groupBy: get_chartGroupbyType.call({}, options),
						group_type: function(options) {
							
							if(options.query.where.hasOwnProperty('group_type')) {
								
								return options.query.where.group_type;
								
							} else {
								
								return false;
								
							}
							
						}.call({}, options),
						type: function(options) {
							
							if(options.query.hasOwnProperty('type')
								&& options.query.type !== 0) {
								
								return options.query.type;
								
							} else {
								
								return false;
								
							}
							
						}.call({}, options),
						state: function(options) {
							
							if(options.query.hasOwnProperty('state') 
								&& options.query.state !== 0) {
								
								return options.query.state;
								
							} else {
								
								return false;
								
							}
							
						}.call({}, options),
						filters: function(options) {
							
							var filterArr = [];
							
							if(!_.isEmpty(options.query.filters)) {
								
								_.each(options.query.filters, function(filterObj) {
									
									filterArr.push({
										field: filterObj.field,
										value: filterObj.value
									});
									
								});
								
							}
							
							return filterArr;
							
						}.call({}, options),
						sumCast: 'INT'
					}
				);
				
			}
			
			_.each(options.fields, function (field, name) {

				switch (field.type) {
					
					case 'usd':
					case 'currency':
						/* xFieldOpts[name] = field; */
						yFieldOpts[name] = field;
						break;
						
					case 'date':
						xFieldOpts[name] = field;
						break;
					
				}
				
			});
			
			var settings = {
				x: 			Object.keys(xFieldOpts)[0]
				, y: 		Object.keys(yFieldOpts)[0]
				, aggregate:	'sum'
			};

			// axes config
			var xSelectionTxt = 'X axis';
			var ySelectionTxt = 'Y axis';
			if (options.fields) {
				
				if (options.fields[settings.x]) {
					xSelectionTxt = options.fields[settings.x].title;
				}
				if (options.fields[settings.y]) {
					ySelectionTxt = options.fields[settings.y].title +' ('+ settings.aggregate +')';
				}
				
			}
			
			chartUI.makeNode('chartCont', 'div', {css:'ui basic padded segment'});

			draw_chart (chartUI.chartCont.makeNode('chart', 'div', {}), list, settings);

			if(legend.hasOwnProperty('settings')) {
				$(legend.settings.selector).addClass('hidden');	
			} else {
				
				delete rightCol.legend;

			}
			
		}
		
		function showGroupingChart (ui, list, options, legend) {
			
			var chartUI = ui.chart;
			
			function getSecondaryData (data, options, callback) {
				
				if (settings.groupBy === 'parent') {
					
					var grpIds = _.chain(data)
									.pluck('group')
									 .flatten()
									  .uniq()
									   .value();
									   
					sb.data.db.obj.getById(
						'groups'
						, grpIds
						, function (refData) {
							
							callback(refData);
							
						}
						, {
							name: true
							, color: true
							, selectionObj: true
						}
					);
					
				} else {
					
					switch (options.fields[settings.groupBy].type) {
						
						case 'state':
							var type = _.findWhere(options.types, {id: options.query.type});
							
							if (!type) {
								showEmptyMessage(ui, 'Please select a <strong>Type</strong> to see distribution by stage in workflow.');
								return;
								
							}
							
							// group unset states w/entry point state
							var unset = _.findWhere(data, {group:'0'});
							if (unset) {
								
								var firstState = _.findWhere(type.states, {isEntryPoint: 1});
								var firstStateCount = _.findWhere(data, {group: firstState.uid.toString()});
								if (firstStateCount) {
									
									firstStateCount.count += unset.count;
									
								} else {
									
									data.push({
										count: 		unset.count
										, group: 	firstState.uid.toString()
									});
									
								}
								
								data = _.filter(data, function (datum) {
									return datum.group != '0';
								});
								
							}
							
							callback({
								type: 		'state'
								, typeObj: 	type
							});
							break;
						
						case 'users':
							
							data = _.filter(data, function (metric) {
								return metric.group !== null;
							});
							
							var userIds = _.chain(data)
											.pluck('group')
											 .flatten()
											  .uniq()
											   .compact()
											    .value();
							
							sb.data.db.obj.getById(
								'users'
								, userIds
								, function (refData) {
									
									callback(refData);
									
								}
								, {
									name: true
									, fname: true
									, lname: true
									, color: true
									, selectionObj: true
								}
							);
							break;
						
					}
					
				}
				
			}
			
			function draw_chart (ui, list, settings) {

				options.getCounts(settings.groupBy, 'end_date', function (data) {
					
					if (_.isEmpty(data)) {
						
						showEmptyMessage(ui);
						
					} else {
						
						getSecondaryData (data, options, function (groupedBy) {
							
							var counts = [];
							var colors = [];
							var labels = [];
							
							_.each(data, function (count) {
								
								var txt = '';
								var color = '';
								var usrDefined = false;
								var isFirst = true;
								
								if (Array.isArray(count.group)) {
									
									_.each(count.group, function (uid) {
										
										var usr = _.findWhere(groupedBy, {id: uid});
										if (!_.isEmpty(usr)) {
											
											if (!isFirst) {
												txt += ', ';
											}
											
											txt += usr.name;
											isFirst = false;
											
										}
										
										if (usr && usr.color && _.isEmpty(color)) {
											color = usr.color;
										}
										
									});
									
								} else if (groupedBy.type == 'state') {
									
									var state = _.findWhere(groupedBy.typeObj.states, {uid: parseInt(count.group)});
									if (!state) {
										state = _.findWhere(groupedBy.typeObj.states, {isEntryPoint:1});
									}
									
									txt = state.name;
									color = state.color || 'rgb(200,200,200)';
									
								} else {
									
									var grp = _.findWhere(groupedBy, {id: parseInt(count.group)});
									if (grp) {
										
										txt = grp.name;
										color = grp.color || 'rgb(200,200,200)';
										
									} else {
										
										return;
										
									}
									
								}
								
								colors.push(color);
								labels.push(txt);
								counts.push(count.count);
								
							});
							
							var chartSetup = {
							    type: viewState.type.name
							    , data: {
									labels: labels,
									datasets: [{
							            data: counts
							            , backgroundColor: colors
							        }]
							    }
							    , options: {
							        scales: {
							            yAxes: [{
								             ticks: {
								                beginAtZero: true
								            }
							            }]
							        }
							    }
							};
							
							ui.makeNode('chart', 'chart', chartSetup);
							ui.patch();
							
						});
						
					}
					
				});
				
			}
			
			draw_chart(chartUI, list, settings);
			
			metricsUI.empty();
			metricsUI.patch();
			
			var groupOptions = {};
			
			_.each(options.groupings, function (grouping, key) {
				
				if (key !== 'by_range' && key !== 'type') {
					
					groupOptions[key] = {
						title: grouping
					};
					
				}
				
			});
			
			var grpSelectionTxt = groupOptions[settings.groupBy].title;
			
			legend.makeNode('settings', 'div', {})
				.makeNode('groupBy', 'div', {
					css: 'ui basic search fluid dropdown',
					text: '<strong>Group by:</strong> <div class="text">'+ grpSelectionTxt +'</div> <i class="dropdown icon"></i>',
					listener: {
						type: 'dropdown',
						allowCategorySelection: true,
						onChange: function(value){
							
							if (!_.isEmpty(value) && value != settings.groupBy) {
								
								settings.groupBy = value;
								draw_chart(ui, list, settings);
								ui.patch();
								
							}
							
						}
					},
					placeholder: 'Group by'
				}).makeNode(
					'menu'
					, 'div'
					, {
						css: 'menu'
					}
				);
				
			legend.settings.makeNode('groupByBr', 'div', {text: '<br />'});
				
			_.each(groupOptions, function (val, key) {
				
				legend.settings.groupBy.menu
					.makeNode(key, 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'value'
								, value: key
							}
						]
					})
					.makeNode('txt', 'div', {
						css: 'text'
						, tag: 'span'
						, text: val.title
					});
				
				legend.settings.groupBy.menu[key].makeNode('menu', 'div', {css: 'menu'});
				
			});
			
			$(legend.settings.selector).removeClass('hidden');
			
		}
		
		function showPieChart (ui, list, options, legend) {
			
			showGroupingChart(ui.chart, list, options, legend, 'pie');
			
		}
		
		function showLinePlot (ui, list, options, legend) {
			
			var chartUI = ui.chart;
			
			function draw_chart (ui, list, settings) {
				
				get_data (
					settings.x
					, settings.y
					, function (response, error) {
						
						if (_.isEmpty(response)) {
							
							error(ui);
							
						} else {
								
							var data = [];
							var isOverTime = false;
							
							if (
								xFieldOpts[settings.x]
								&& xFieldOpts[settings.x].type === 'date'
							) {
								isOverTime = true;
							}
							
							_.each(response, function (item) {
								
								if (isOverTime) {
									
									data.push({
										x: 		new Date(item.grouped)
										, y: 	parseInt(item[settings.aggregate])
									});
									
								} else {
									
									data.push({
										x: 		parseInt(item.grouped)
										, y: 	parseInt(item[settings.aggregate])
									});
									
								}
								
							});
							
							var xFieldTxt = '';
							if (
								xFieldOpts[settings.x]
								&& xFieldOpts[settings.x].title
							) {
								xFieldTxt = xFieldOpts[settings.x].title;
							} else if (
								typeof settings.x === 'object' && settings.x.hasOwnProperty('num')
							) {
								xFieldTxt = xFieldOpts[settings.x.num].title +' / '+ xFieldOpts[settings.x.div].title;
							}
							
							data = _.sortBy(data, 'x');
							var labels = _.pluck(data, 'x');
							var chartSetup = {
							    type: 'line'
							    , data: {
								    labels: labels,
							        datasets: [{
							            label: yFieldOpts[settings.y].title +' vs '+ xFieldTxt
							            , data: data
							        }]
							    }
							    , options: {
							        scales: {
							            xAxes: [{
							                type: 'linear'
							                , position: 'bottom'
							                ,  ticks: {
								                beginAtZero: true
								            }
							            }]
							            , yAxes: [{
								             ticks: {
								                beginAtZero: true
								            }
							            }]
							        }
							    }
							};
							
							if (isOverTime) {
								
								chartSetup.options.scales.xAxes[0].type = 'time';
								chartSetup.options.scales.xAxes[0].unit = 'day';
								
							}
							
							ui.makeNode('chart', 'chart', chartSetup);
							
							ui.patch();
						
						}
						
					}
				);
				
			}
			
			var xFieldOpts = 	{};
			var yFieldOpts = 	{};
			
			_.each(options.fields, function (field, name) {
				
				switch (field.type) {
					
					case 'int':
					case 'number':
						xFieldOpts[name] = field;
						yFieldOpts[name] = field;
						break;
						
					case 'date':
						xFieldOpts[name] = field;
						break;
					
				}
				
			});
			
			var settings = {
				x: 			Object.keys(xFieldOpts)[0]
				, y: 		Object.keys(yFieldOpts)[0]
				, aggregate:	'avg'
			};
			
			// use default values
			if (
				options.subviews
				&& options.subviews.chart
				&& typeof options.subviews.chart.defaults === 'object'
			) {
				
				_.each(options.subviews.chart.defaults, function (val, key) {
					
					settings[key] = val;
					
				});
				
			}
			
			// axes config
			var xSelectionTxt = 'X axis';
			var ySelectionTxt = 'Y axis';
			if (options.fields) {
				
				if (options.fields[settings.x]) {
					xSelectionTxt = options.fields[settings.x].title;
				}
				if (options.fields[settings.y]) {
					ySelectionTxt = options.fields[settings.y].title +' ('+ settings.aggregate +')';
				}
				
			}
			
			
			legend.makeNode('settings', 'div', {})
				.makeNode('x', 'div', {
					css: 'ui basic fluid dropdown',
					text: '<strong>X</strong> = <div class="text">'+ xSelectionTxt +'</div> <i class="dropdown icon"></i>',
					listener: {
						type: 'dropdown',
						allowCategorySelection: true,
						onChange: function(value, text, three){
							
							if (!_.isEmpty(value) && value != settings.x) {
								
								var num = value.split('-')[0];
								var div = value.split('-')[1];
								
								if (!_.isEmpty(div)) {
									
									settings.x = {
										num: num
										, div: div
									};
									
								} else {
									
									settings.x = value;
								
								}
								
								draw_chart (ui.chart, list, settings);
								
							}
							
						}
					},
					placeholder: 'X axis'
				}).makeNode(
					'menu'
					, 'div'
					, {
						css: 'menu'
					}
				);
				
			legend.settings.makeNode('xbr', 'div', {text: '<br />'});
				
			_.each(xFieldOpts, function (val, key) {
				
				legend.settings.x.menu
					.makeNode(key, 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'value'
								, value: key
							}
						]
					})
					.makeNode('txt', 'div', {
						css: 'text'
						, tag: 'span'
						, text: val.title
					});
				
				legend.settings.x.menu[key].makeNode('menu', 'div', {css: 'menu'});
					
				switch (val.type) {
					
					case 'int':
					case 'number':
						_.each(options.fields, function (f, k) {
							
							if (key != k) {
								
								switch (f.type) {
									
									case 'int':
									case 'number':
										legend.settings.x.menu[key].menu
											.makeNode(key +'-'+ k, 'div', {
												css: 'item'
												, dataAttr: [
													{
														name: 'value'
														, value: key +'-'+ k
													}
												]
											})
											.makeNode('txt', 'div', {
												css: 'text'
												, tag: 'span'
												, text: val.title +' / '+ f.title
											});
										break;
									
								}
								
							}
							
						});
						break;
					
				}
				
			});
				
			legend.settings.makeNode('y', 'div', {
					css: 'ui basic fluid dropdown',
					text: '<strong>Y</strong> = <div class="text">'+ ySelectionTxt +'</div> <i class="dropdown icon"></i>',
					listener: {
						type: 'dropdown',
						onChange: function(value){
							
							if (!_.isEmpty(value)) {
								
								var prop = value.split('-')[0];
								var aggType = value.split('-')[1];
								
								if (
									prop !== settings.y
									|| aggType !== settings.aggregate
								) {
									
									settings.y = prop;
									settings.aggregate = aggType;
									draw_chart (ui.chart, list, settings);
									
								}
								
							}
							
						}
					},
					placeholder: 'Y axis'
				}).makeNode('menu', 'div', {css: 'menu'});
				
			legend.settings.makeNode('ybr', 'div', {text: '<br />'});
				
			_.each(yFieldOpts, function (field, key) {
				
				legend.settings.y.menu
					.makeNode('t-'+ key, 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'y = '+ field.title
								, value: key
							}
						]
					})
					.makeNode('txt', 'div', {
						css: 'text'
						, tag: 'span'
						, text: field.title
					});
					
				legend.settings.y.menu['t-'+ key]
					.makeNode('menu', 'div', {
						css: 'menu'
					}).makeNode('avg', 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'value'
								, value: key +'-avg'
							}
						]
					})
					.makeNode('txt', 'div', {
						css: 'text'
						, tag: 'span'
						, text: field.title +' (avg)'
					});
					
				legend.settings.y.menu['t-'+ key]
					.menu.makeNode('sum', 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'value'
								, value: key +'-sum'
							}
						]
					})
					.makeNode('txt', 'div', {
						css: 'text'
						, tag: 'span'
						, text: field.title +' (sum)'
					});
				
			});
			
			draw_chart (chartUI.makeNode('chart', 'div', {}), list, settings);
			
		}
		
		if (options.isLoading) {
			
			container.makeNode('lb_1', 'lineBreak', {spaces: 1});
			
			container.makeNode('loadingSeg', 'div', {
				css: 'ui segment'
				, style: 'height: 200px; border: none;'
			});
			
			container.loadingSeg.makeNode('dimmer', 'div', {
				css: 'ui active inverted dimmer'
			});
			
			container.loadingSeg.dimmer.makeNode('loader', 'div', {
				css: 'ui medium text loader'
				, text: 'Loading chart...'
			});
			
			container.patch();
			
		} else {
			
			var ui = container.makeNode('c', 'div', {css:'ui two column grid'});
			var chartOptions = [
				{
					name: 		'line'
					, title: 	'Line'
					, icon: 		'chart line'
				}
				, {
					name: 		'pie'
					, title: 	'Pie'
					, icon: 		'chart pie'
				}
				, {
					name: 		'bar'
					, title: 	'Bar'
					, icon: 		'chart bar'
				}
				, {
					name: 'bar_byValue'
					, title: 'Bar by value'
					, icon: 'chart bar'
				}
				, {
					name: 'custom'
					, title: 'Bar by value'
					, icon: 'chart bar'
				}
			];
			var settings = {
					groupBy: 'managers'
				};
			var metricsUI = {};
			var rightCol = {};
			var defaultChart = 'line';
			
			// remove hidden options
			chartOptions = _.filter(chartOptions, function (opt) {
				
				if (
					options.subviews 
					&& options.subviews.chart
					&& options.subviews.chart.hasOwnProperty(opt.name)
					&& options.subviews.chart[opt.name] == false
				) {
					return false;
				}
				
				return true;
				
			});
			
			if (
				options.subviews 
				&& options.subviews.chart
				&& options.subviews.chart.defaults
				&& options.subviews.chart.defaults.type
			) {
				defaultChart = options.subviews.chart.defaults.type;
			}
			
			var viewState = {
				type: _.findWhere(chartOptions, {name: defaultChart})
			};
	
			rightCol = ui.makeNode('right', 'div', {css: 'ui sixteen wide column'})
			
			// chart
			ui.makeNode(
				'chart'
				, 'div'
				, {
					css: 'ui fifteen wide column'
				}
			);
			
			// legend
			var typeTxt = getChartTypeDisplayTxt(viewState.type);
						
			ui.right.makeNode('legend', 'div', {
					css: 'ui secondary segment'
				});
				
			metricsUI = ui.right.makeNode('metrics', 'div', {});
			
			if (chartOptions.length > 1) {
				
				ui.right.legend.makeNode(
					'type'
					, 'div'
					, {
						css: 'ui basic search fluid dropdown',
						text: '<div class="text">'+ typeTxt +'</div> <i class="dropdown icon"></i>',
						listener: {
							type: 'dropdown',
							values: _.map(chartOptions, function (val, key) {
								
								return {
									name: 		getChartTypeDisplayTxt(val)
									, value:		val.name
									, selected: 	val.name === viewState.type.name
								};
								
							}),
							onChange: function(value){
	
								if (!_.isEmpty(value) && value != viewState.type.name) {
									
									viewState.type = _.findWhere(chartOptions, {name: value});
									
									ui.chart.empty();
									showChart(ui.chart, list, options, ui.right.legend);
									ui.chart.patch();
									
								}
								
							}
						},
						placeholder: 'X axis'
					}
				);
				
				ui.right.legend.makeNode('typebr', 'div', {text: '<br />'});
				
			}
	
			showChart(ui, list, options, ui.right.legend);
			
			ui.patch();	
			
		}
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-collection-view',
				data: {
					hidden: 		true
					, icon: 		'chart line'
					, id: 		sb.moduleId
					, name:		'chart'
					, options: 	{}		
					, title: 	'Chart'
					, view: 		View
				}
			});
			
		},
		
		destroy: function () {
			
		}
		
	}
	
});