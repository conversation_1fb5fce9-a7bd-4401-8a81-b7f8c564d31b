Factory.register('calendar-view', function(sb) {
    
    var comps = {};
    
    function calendar_ui(container, list, options) {

		if (options.isLoading === false) {
			
			container.patch();

			var setup = {
					domObj: container,
					events: function(callback, range) {
						
						options.getDataByRange(range, function(list){
		
							var ret = [];
							
							_.each(list.data, function(obj) {
		
								if(
									obj.start_date 
									&& obj.end_date
								) {
		
									obj.start_date = moment(obj.start_date).subtract(sb.dom.utcOffset, 'hours');
									obj.end_date = moment(obj.end_date).subtract(sb.dom.utcOffset, 'hours');
									
									if(obj.start_date.unix() >= range.start && obj.end_date.unix() <= range.end) {
		
										ret.push({
											id: 		obj.id,
											name: 		obj.name || 'Untitled',
											startTime: 	obj.start_date,
											endTime: 	obj.end_date,
											type: 		obj.group_type,
											color: 		obj.color || 'blue',
											modal: function(dom, event, date) {
		
												options.singleView(obj);
												
											} 
										});
										
									}
									
								} else if (
									!obj.start_date
									&& obj.end_date
								) {
		
									obj.end_date = moment(obj.end_date).subtract(sb.dom.utcOffset, 'hours');
									
									ret.push({
										id: 		obj.id,
										name: 		obj.name || 'Untitled',
										startTime: 	obj.end_date,
										endTime: 	obj.end_date.add(1, 'hour'),
										type: 		obj.group_type,
										color: 		obj.color || 'blue',
										modal: function(dom, event, date) {
		
											options.singleView(obj);
											
										} 
									});
									
								} else if (
									obj.start_date
									&& !obj.end_date
								) {
		
									obj.start_date = moment(obj.start_date).subtract(sb.dom.utcOffset, 'hours');
									
									ret.push({
										id: 		obj.id,
										name: 		obj.name || 'Untitled',
										startTime: 	obj.start_date,
										endTime: 	obj.start_date.add(1, 'hour'),
										type: 		obj.group_type,
										color: 		obj.color || 'blue',
										modal: function(dom, event, date) {
		
											options.singleView(obj);
											
										} 
									});
									
								} else {
		
									if (options.rangeOver !== undefined) {
										
										ret.push({
											id: 		obj.id,
											name: 		obj.name || 'Untitled',
											startTime: 	moment(obj[options.rangeOver]),
											endTime: 	moment(obj[options.rangeOver]),
											type: 		obj.status,
											color: 		obj.color || 'blue',
											modal: function(dom, event, date) {
			
												options.singleView(obj);
												
											} 
										});	
										
									} else {
										
										ret.push({
											id: 		obj.id,
											name: 		obj.name || 'Untitled',
											startTime: 	moment(obj.date_created).subtract(sb.dom.utcOffset, 'hours'),
											endTime: 	moment(obj.date_created).subtract(sb.dom.utcOffset, 'hours'),
											type: 		obj.status,
											color: 		obj.color || 'blue',
											modal: function(dom, event, date) {
			
												options.singleView(obj);
												
											} 
										});
										
									}
									
								} 
								
							});
							
							callback(ret);
							
						});
						
					}
				};
	
			// If there's a create action provided, add a 
			// create button to month/week views
			if (
				typeof options.create_action === 'function' 
				&& options.actions
				&& options.actions.create
			) {
	
				var calendarCellBtns = {
					new: {
						icon: 	'plus',
						color: 	'green',
						tooltip: {
							text: 		'Record a new item',
							position: 	'bottom center'
						},
						action: function (ui, date) {
							
							options.create_action(undefined, {
								today: date
							});
	
						}
					}
				};
				setup.views = {
					month: {
						show: true,
						cellButtons: calendarCellBtns
					},
					week: {
						body: true,
						cellButtons: calendarCellBtns
					},
					threeDay: {
						show: true
					},
					day: {
						show: true
					},
					list: {
						show: true
					}
				};
	
			}
	
			// If more config options passed in, pass them on
			if (
				options
				&& !_.isEmpty(options.subviews)
				&& !_.isEmpty(options.subviews.calendar)
				&& typeof options.subviews.calendar === 'object'
			) {
	
				_.each(options.subviews.calendar, function (option, optionKey) {
	
					setup[optionKey] = option;
	
				});
	
			}
	
	        comps.calendar.notify({
	            type: 'show-calendar',
	            data: setup
	        });	
			
		}
        
    }
    
    return {
        
        init: function() {
            
            sb.notify({
				type:'register-collection-view',
				data:{
					icon:'calendar',
					id:sb.moduleId,
					getsDataByRange:true,
					name:'calendar',
					options:{},
					title:'Calendar',
					view: calendar_ui
				}
			});
			
			comps.calendar = sb.createComponent('calendar');
            
        },
        
        load: function() {},
        
        destroy: function() {
            
            _.each(comps, function(comp) {
                comp.destroy();
            }, this);
            
            comps = {};
            
        }
        
    }
    
});