Factory.register('cards-view', function(sb){

	function cards_ui(ui, list, options){

		var cardWidth = 'four fluid stackable';
		var isMobile = false;

		function build_placeholder(ui, cardWidth) {

			ui.makeNode('cards', 'div', {
				css: 'ui ' + cardWidth + ' cards'
			});

			// Card 1
			ui.cards.makeNode('card1', 'div', {
				css: 'ui card'
			});

			ui.cards.card1.makeNode('content', 'div', {
				css: 'content'
			});

			ui.cards.card1.content.makeNode('plH', 'div', {
				css: 'ui placeholder'
			});

			ui.cards.card1.content.plH.makeNode('image', 'div', {
				css: 'square image'
			});

			// Card 2
			ui.cards.makeNode('card2', 'div', {
				css: 'ui card'
			});

			ui.cards.card2.makeNode('content', 'div', {
				css: 'content'
			});

			ui.cards.card2.content.makeNode('plH', 'div', {
				css: 'ui placeholder'
			});

			ui.cards.card2.content.plH.makeNode('image', 'div', {
				css: 'square image'
			});

			// Card 3
			ui.cards.makeNode('card3', 'div', {
				css: 'ui card'
			});

			ui.cards.card3.makeNode('content', 'div', {
				css: 'content'
			});

			ui.cards.card3.content.makeNode('plH', 'div', {
				css: 'ui placeholder'
			});

			ui.cards.card3.content.plH.makeNode('image', 'div', {
				css: 'square image'
			});

			// Card 4
			ui.cards.makeNode('card4', 'div', {
				css: 'ui card'
			});

			ui.cards.card4.makeNode('content', 'div', {
				css: 'content'
			});

			ui.cards.card4.content.makeNode('plH', 'div', {
				css: 'ui placeholder'
			});

			ui.cards.card4.content.plH.makeNode('image', 'div', {
				css: 'square image'
			});

		}

		if($(window).width() <= 768) {

			isMobile = true;

		}

		if(options.size){

			switch(options.size){
				case 'mini':
					cardWidth = 'eight doubling';
					break;
				case 'small':
					cardWidth = 'four doubling';
					break;
			}

		}

		ui.makeNode('topBreak', 'div', {text:'<br />'});

		if(options.isLoading === true) {

			build_placeholder(ui, cardWidth);

		} else {

			ui.makeNode('cards', 'div', {css:'ui '+ cardWidth +' cards'});

			_.each(list, function(item, i){

				var delayCss = 'item-fade-in-'+ Math.floor(Math.random() * Math.floor(3));

				ui.cards.makeNode('i-'+ i, 'div', {
					css:'ui card revealParent '+ delayCss
				});

				ui.cards['i-'+ i].makeNode('content', 'div', {
					css:'content'
				});

	/*
				if ( options.actions ) {

					options.actions_ui(

						ui.cards['i-'+ i].content.makeNode('actions', 'div', {
							css:'large screen widescreen computer only',
							style:'padding:8px;'
						}),

						item,
						{
							actionButtonStyle:'margin:4px;float:right;',
							css:'revealChild floated right large screen widescreen computer only'
						}
					);

				}
	*/

				_.each(options.fields, function(field, name){

					if(field.view){

						var viewUI = {};

						viewUI = ui.cards['i-'+ i].content.makeNode(name, 'div', {
							css:'description',
							style:'padding:8px; clear: none !important;'
						})

						if(field.view(

							viewUI,

							item

						) === false) {

							delete ui.cards['i-'+ i].content[name];

						}

					} else {

						ui.cards['i-'+ i].content.makeNode(name, 'div', {
							css:'description',
							text:item[name],
							style:'padding:8px;'
						});

					}

				});

				if ( options.actions ) {

					ui.cards['i-'+ i].makeNode('extra', 'div', {
						css:'extra content'
					});

					options.actions_ui(

						ui.cards['i-'+ i].extra.makeNode('actions', 'div', {
							css:''
						}),

						item,
						{
							actionButtonCss:'ui mini fluid icon basic blue button simple dropdown',
							actionButtonStyle:'text-align:center;',
							css:''
						}
					);

				}

			});

			options.create_ui(
				ui.cards.makeNode('create', 'div', {css:'ui card', style:'border:none;background-color:transparent;'})
					.makeNode('div', 'div', {style:'margin:calc(50% - 1em);'})
			);

		}

		ui.makeNode('bottomBreak', 'div', {text:'<br />'});

	}

	return {

		init:function(){

			// TURNED OFF ON UPDATE 276 BY BRANDON RAY
			sb.notify({
				type:'register-collection-view',
				data:{
					icon:'address card',
					id:sb.moduleId,
					name:'cards',
					options:{},
					title:'Cards',
					view:cards_ui
				}
			});

		},

		load:function(){},

		destroy:function(){}

	}

});
