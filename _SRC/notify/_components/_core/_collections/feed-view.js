Factory.register('feed-view', function(sb){
	
	function feed_ui(container, list, options){
		
		function draw_field(){
			
		}
		
		var ui = container.makeNode('container', 'div', { css:'ui feed' });
			
		_.each(list, function(item, i){
			
			ui.makeNode('br-'+ i, 'div', {
				text:'<div><br /></div>'
			});
			
			ui.makeNode('i-'+ i, 'div', {
				css:'event'
			});
			
			var groupedFields = _.groupBy(options.fields, 'type');
			
			// use first image type
			if(groupedFields.image){
				
				if(groupedFields.image[0].view){
					
					groupedFields.image[0].view(
						
						ui['i-'+ i].makeNode(groupedFields.image[0].name, 'div', {
							css:'label',
							text:item[groupedFields.image[0].name]
						}),
						
						item
						
					);
					
				}else{
					
					ui['i-'+ i].makeNode(groupedFields.image[0].name, 'div', {
						css:'label',
						text:item[groupedFields.image[0].name] +' '
					});
					
				}
			
			}
			
			ui['i-'+ i].makeNode('content', 'div', {
				css:'content'
			});
			
			// use first date type
			if(groupedFields.date){
				
				if(groupedFields.date[0].view){
					
					groupedFields.date[0].view(
						
						ui['i-'+ i].content.makeNode('date', 'div', {
							css:'date'
						}),
						
						item
						
					);
					
				}else{
					
					ui['i-'+ i].makeNode('date', 'div', {
						css:'date',
						text:item[groupedFields.date[0].name] +' '
					});
					
				}
				
			}
			
			
			// look for titles
			_.each(groupedFields.title, function(field, j){
				
				if(field.view){
					
					field.view(
						
						ui['i-'+ i].content.makeNode(field.name, 'div', {
							style:'display:inline-block;'
						}),
						
						item
						
					);
					
				}else{
					
					ui['i-'+ i].content.makeNode(field.name, 'div', {
						text:item[field.name] +'&nbsp;',
						style:'display:inline-block;'
					});
					
				}
				
			});
			
			// look for description
			_.each(groupedFields.detail, function(field, j){
				
				if(field.view){
					
					field.view(
						
						ui['i-'+ i].content.makeNode(field.name, 'div', {
						}),
						
						item
						
					);
					
				}else{
					console.log('title field', field);
					ui['i-'+ i].content.makeNode(field.name, 'div', {
						text:item[field.name] +'&nbsp;'
// 						style:'display:inline-block;'
					});
					
				}
				
			});
			
			// look for types
			_.each(groupedFields.type, function(field, j){
				
				if(field.view){
					
					field.view(
						
						ui['i-'+ i].content.makeNode(field.name, 'div', {
							style:'display:inline-block;',
							text:'&nbsp;'
						}),
						
						item
						
					);
					
				}else{

					ui['i-'+ i].content.makeNode(field.name, 'div', {
						text:item[field.name] +'&nbsp;',
						style:'display:inline-block;'
					});
					
				}
				
			});
			
			// look for links
			_.each(groupedFields.objectId, function(field, j){
				
				if(field.view){
					
					field.view(
						
						ui['i-'+ i].content.makeNode(field.name, 'div', {
							style:'display:inline-block;',
							text:'&nbsp;'
						}),
						
						item
						
					);
					
				}else{

					ui['i-'+ i].content.makeNode(field.name, 'div', {
						text:item[field.name] +'&nbsp;',
						style:'display:inline-block;'
					});
					
				}
				
			});
			
		});
		
		ui.patch();
		
	}
	
	return {
		
		init:function(){
			
			/*
sb.notify({
				type:'register-collection-view',
				data:{
					icon:'list alternate',
					id:sb.moduleId,
					name:'feed',
					options:{},
					title:'Feed',
					view:feed_ui
				}
			});
*/
			
		},
		
		load:function(){},
		
		destroy:function(){}
		
	}
	
});