Factory.register('matrix-view', function(sb) {
	
	var UI_CACHE = {
			matrix_body: {},
			matrix_topBar: {},
			modal: {},
			scroll_div: {}
		};
	var onDefaultView = true;
	var views = [];
	var currentDate = moment();
	var currentView = {};
	var currentDataSet = [];
	var currentAssignments = [];
	var OnMobile = false;
	var subviewActions = {};
	var assignmentFields = {};
	var assignmentDom = function(cardBodyUI, cardContentUI, assignmentObj, callback) {};
	var methods = { // This object can contain methods for dom refresh, data reload etc.
			refresh: {
				unassigned_row: function() {},
				week_view: function(rowObj) {},
				bottomBar: function() {},
				assignmentCard: function() {},
				day_view: function(rowObj) {},
				dateRangeSwitch: function(date) {},
				leftCol: function(rowObj) {},
				week_view_body: function(rowObj) {}
			}
		};
	var currentLayout = 'week';
	var assigneeList = [];
	
	function View(ui, list, options) {

		function get_assignments(options, callback) {
				
			if(options.subviews.matrix.hasOwnProperty('assignments')) {
	
				if(options.subviews.matrix.assignments.hasOwnProperty('query')) {
											
					options.subviews.matrix.assignments.query(function(assignmentList) {
							
						currentAssignments = assignmentList;

						callback(assignmentList);
							
					}, {
						date: currentDate.clone(),
						layout: currentLayout	
					});
						
				}
					
			}		
			
		}

		function matrix_bar(ui) {
			
			function view_layout_menu(ui) {
				
				ui.makeNode('week_btn', 'div', {
					text: 'Week',
					css: 'active item layoutControl weekLayout',
					style: 'cursor: pointer;'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function(data) {
							
							currentLayout = 'week';
							
							$('.layoutControl').removeClass('active');
							$(data.selector).addClass('active');
							
							methods.refresh.dateRangeSwitch(currentDate.clone());

							UI_CACHE.matrix_body.empty();

							build_placeholder(UI_CACHE.matrix_body);

							UI_CACHE.matrix_body.patch();

							get_assignments(options, function(assignmentList) {

								matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate.clone(), 'week');
								return;
							});
							
						},
						selector: ui.week_btn.selector
					}
				}, sb.moduleId);
				
				ui.makeNode('day_btn', 'div', {
					text: 'Day',
					css: 'item layoutControl dayLayout',
					style: 'cursor: pointer;'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function(data) {
							
							currentLayout = 'day';
							
							$('.layoutControl').removeClass('active');
							$(data.selector).addClass('active');
							
							methods.refresh.dateRangeSwitch(currentDate.clone());

							matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate.clone(), 'day');
							
						},
						selector: ui.day_btn.selector                                                                                                                  
					}
				}, sb.moduleId);
				
			}
			
			function today_btn(ui) {
				
				ui.makeNode('today', 'div', {
					tag: 'button',
					text: 'Today',
					css: 'ui button',
					style: 'background-color: white !important;'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function(data) {
							
							currentDate = moment();
							
							methods.refresh.dateRangeSwitch(currentDate.clone());

							UI_CACHE.matrix_body.empty();

							build_placeholder(UI_CACHE.matrix_body);

							UI_CACHE.matrix_body.patch();

							get_assignments(options, function(assignmentList) {

								matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate.clone(), currentLayout);
								return;
							});
							
						}
					}
				}, sb.moduleId);
				
			}
			
			function date_switch_btns(ui) {

				function build_dateRangeSwitch(ui, date) {

					var range = '';

					if(currentLayout === 'week') {

						range = date.clone().startOf('week').add(1, 'day').format('MMM Do') + ' - ' + date.clone().endOf('week').add(1, 'day').format('MMM Do, YYYY');

					} else if(currentLayout === 'day') {

						range = date.format('MMM Do, YYYY');

					}

					ui.makeNode('btn', 'div', {
						//text: currentDate.format('MMM Do, YYYY'),
						text: range,
						css: 'ui basic button',
						style: 'border-radius: 0;'
					});

					ui.patch();

				}
				
				ui.makeNode('left', 'div', {
					tag: 'button',
					text: '<i class="left chevron icon"></i>',
					css: 'ui icon button'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function(data) {
							
							if(currentLayout === 'week') {
								
								currentDate = currentDate.clone().subtract(1, 'week');
									
							} else if(currentLayout === 'day') {
								
								currentDate = currentDate.clone().subtract(1, 'day');
								
							}

							methods.refresh.dateRangeSwitch(currentDate.clone());
							
							UI_CACHE.matrix_body.empty();

							build_placeholder(UI_CACHE.matrix_body);

							UI_CACHE.matrix_body.patch();

							get_assignments(options, function(assignmentList) {

								matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate, currentLayout);
								return;
							});
							
						}
					}
				}, sb.moduleId);
				
				ui.makeNode('center', 'div', {
					css: 'ui calendar',
					style: 'background-color: white !important;',
					id: 'showCal'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function(data) {

							$('#showCal').calendar({
								type: 'date',
								onChange: function(date, newVal) {

									currentDate = moment(date);

									methods.refresh.dateRangeSwitch(currentDate.clone());

									UI_CACHE.matrix_body.empty();

									build_placeholder(UI_CACHE.matrix_body);

									UI_CACHE.matrix_body.patch();

									get_assignments(options, function(assignmentList) {

										matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate, currentLayout);
										return;
									});
			
								}
							});
							
						}
					}
				}, sb.moduleId);
				
				build_dateRangeSwitch(ui.center, currentDate.clone());
				
				ui.makeNode('right', 'div', {
					tag: 'button',
					text: '<i class="right chevron icon"></i>',
					css: 'ui icon button'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function(data) {
							
							if(currentLayout === 'week') {
								
								currentDate = currentDate.clone().add(1, 'week');
								
							} else if(currentLayout === 'day') {
								
								currentDate = currentDate.clone().add(1, 'day');
								
							}
							
							methods.refresh.dateRangeSwitch(currentDate.clone());

							UI_CACHE.matrix_body.empty();

							build_placeholder(UI_CACHE.matrix_body);

							UI_CACHE.matrix_body.patch();

							get_assignments(options, function(assignmentList) {

								matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate, currentLayout);
								return;
							});
							
						}
					}
				}, sb.moduleId);

				methods.refresh.dateRangeSwitch = function(date) {

					build_dateRangeSwitch(ui.center, date.clone());

				}
				
			}
			
			function view_type(ui, viewOptions) {
				
				var defaultView = _.find(viewOptions, function(view) {
					return view.default;
				});

				ui.makeNode('dropDown', 'div', {
					text:'<i class="caret right icon"></i> <span class="text">',
					css:'ui fluid dropdown item',
					tag: 'select',
					listener: {
						onChange:function(viewName, viewTitle) {
							
							if(viewName !== '') {
								
								currentView = viewOptions[viewName]; 
								
							} else {  
								
								currentView = defaultView;	
								
							}
							
							if(currentView.hasOwnProperty('query')) {
									
								onDefaultView = false;
								
								UI_CACHE.matrix_body.empty();
								
								build_placeholder(UI_CACHE.matrix_body);
								
								UI_CACHE.matrix_body.patch();
								
								currentView.query(function(data) {
									
									currentDataSet = data;

									get_assignments(options, function(assignmentList) {

										matrix_body(UI_CACHE.matrix_body, data, currentView.name, currentDate.clone(), currentLayout);
										return;
									});
									
								});
								
							} else {
								
								onDefaultView = true;
								
								currentDataSet = list;
								
								if(options.subviews.matrix.hasOwnProperty('assignments')) {
		
									if(options.subviews.matrix.assignments.hasOwnProperty('query')) {
										
										UI_CACHE.matrix_body.empty();
										
										build_placeholder(UI_CACHE.matrix_body);
										
										UI_CACHE.matrix_body.patch();

										get_assignments(options, function(assignmentList) {

											matrix_body(UI_CACHE.matrix_body, list, currentView.name, currentDate.clone(), currentLayout);
											return;
										});
										
									}
									
								}
								
							}
							
						},
						type:'dropdown',
						values: _.map(viewOptions, function(viewSetup, name) {

							return {
								name: viewSetup.title,
								value: name	
							};
							
						}),
						placeholder: defaultView.title
					}
				});
								
			}
			
			ui.makeNode('lb_1', 'lineBreak', {spaces: 2});
			
			ui.makeNode('top_bar', 'div', {
				css: 'ui grid',
				style: 'background-color: #F8F8F8;'
			});
			
			UI_CACHE.matrix_topBar = ui.top_bar;
			
			if(!OnMobile) {
				
				ui.top_bar.makeNode('col1', 'div', {
					css: 'four wide column'
				});
				
				ui.top_bar.makeNode('col2', 'div', {
					css: 'two wide column'
				});
				
				ui.top_bar.makeNode('col3', 'div', {
					css: 'six wide column text-center'
				});
				
				ui.top_bar.makeNode('col4', 'div', {
					css: 'four wide column'
				});	
				
			} else {
				
				ui.top_bar.makeNode('col2', 'div', {
					css: 'four wide column'
				});
				
				ui.top_bar.makeNode('col3', 'div', {
					css: 'twelve wide column text-center'
				});
				
				ui.top_bar.makeNode('col4', 'div', {
					css: 'sixteen wide column',
					style: 'padding-top: 0px; padding-bottom: 1.5rem;'
				});
				
			}
			
			if(!OnMobile) {
				
				ui.top_bar.col1.makeNode('btns', 'div', {
					css: 'small ui secondary stackable blue menu'
				});	
				
			}
			
			ui.top_bar.col2.makeNode('btns', 'div', {
				css: 'small basic ui buttons'
			});
			ui.top_bar.col3.makeNode('btns', 'div', {
				css: 'small ui buttons'
			});
			
			if(!OnMobile) {
				
				ui.top_bar.col4.makeNode('menu', 'div', {
					//css: 'small ui blue menu right floated'
				});

			} else {
				
				ui.top_bar.col4.makeNode('menu', 'div', {
					//css: 'small ui blue menu'
				});

			}
			
			if(!OnMobile) {
				
				view_layout_menu(ui.top_bar.col1.btns);	
				
			}
			
			today_btn(ui.top_bar.col2.btns);
			
			date_switch_btns(ui.top_bar.col3.btns);

			if(!_.isEmpty(options.subviews)) {

				if(options.subviews.hasOwnProperty('matrix') && !_.isEmpty(options.subviews.matrix)) {

					if(options.subviews.matrix.hasOwnProperty('viewMenu') && !_.isEmpty(options.subviews.matrix.viewMenu)) {

						view_type(ui.top_bar.col4.menu, options.subviews.matrix.viewMenu);
						
					}
					
				}
				
			}
			
		}
		
		function matrix_body(ui, data, viewType, date, viewLayout) {

			var fields = {};
			var assignments = [];
			var currentSelection = [];
			
			function build_assignmentCard(ui, assignment, assignee, groupedList) {

				var cardCSS = '';
				var cardStyle = '';
				var cardBodyUI = {};
				var drag = {
						moves: true,
						data: {
							options: options,
							assignment: assignment,
							cellUI: ui,
							assignee: assignee
						},
						drop: [sb.moduleId+'-assignment-dropped'],
						accepts: false,
						copy: false
					};
				var tooltip = {
						text: '',
						position: 'top center'
					};

				function navigate_grid(startTime, endTime) {

					var grid_unitWidth = 4.1;
					var grid_ref = {
							'12am': 0,
							'1am': grid_unitWidth,
							'2am': (grid_unitWidth * 2),
							'3am': (grid_unitWidth * 3),
							'4am': (grid_unitWidth * 4),
							'5am': (grid_unitWidth * 5),
							'6am': (grid_unitWidth * 6),
							'7am': (grid_unitWidth * 7),
							'8am': (grid_unitWidth * 8),
							'9am': (grid_unitWidth * 9),
							'10am': (grid_unitWidth * 10),
							'11am': (grid_unitWidth * 11),
							'12pm': (grid_unitWidth * 12),
							'1pm': (grid_unitWidth * 13),
							'2pm': (grid_unitWidth * 14),
							'3pm': (grid_unitWidth * 15),
							'4pm': (grid_unitWidth * 16),
							'5pm': (grid_unitWidth * 17),
							'6pm': (grid_unitWidth * 18),
							'7pm': (grid_unitWidth * 19),
							'8pm': (grid_unitWidth * 20),
							'9pm': (grid_unitWidth * 21),
							'10pm': (grid_unitWidth * 22),
							'11pm': (grid_unitWidth * 23)
						};
						
					return {
						left: grid_ref[startTime],
						width: grid_ref[endTime] - grid_ref[startTime]
					}
					
				}

				function build_cardActions(ui) {

					function build_multipleAssignmentModal(ui, action) {

						ui.makeNode('modal', 'modal', {
							onShow: function() {

								var assignmentObj_data = {
										objectType: '',
										group_type: '',
										ids: []
									};

								var modalUI = ui.modal;

								function build_body(ui, list) {

									ui.makeNode('cards', 'div', {
										css: 'ui cards'
									});
	
									_.each(list, function(assignment) {
	
										var cardCSS = '';

										assignmentObj_data.ids.push(assignment.id);
	
										if(options.subviews.matrix.assignments.hasOwnProperty('css')) {
						
											cardCSS = options.subviews.matrix.assignments.css(assignee, assignment, currentLayout);
											
										}
	
										ui.cards.makeNode('card'+assignment.id, 'div', {
											css: 'card ' + cardCSS
										});
	
										ui.cards['card'+assignment.id].makeNode('content', 'div', {
											css: 'content'
										});
	
										ui.cards['card'+assignment.id].makeNode('btn', 'div', {
											css: 'ui bottom attached button',
											text: '<i class="ui '+ action.icon +' icon"></i>' + action.name
										}).notify('click', {
											type: [sb.moduleId+'-run'],
											data: {
												run: function() {
	
													action.action(ui.cards['card'+assignment.id].content, assignment, function() {
											
														reload_data(assignmentObj_data, function(data) {

															if(data.length > 1) {

																build_body(ui, data);

															} else {

																modalUI.hide();

															}

															methods.refresh.unassigned_row(true);
															
															if(currentLayout === 'week') {
																
																methods.refresh.week_view(assignee, true);		
																
															} else if(currentLayout === 'day') {
																
																methods.refresh.day_view(assignee, true);
																
															}

														});
											
													});
	
												}
											}
										}, sb.moduleId);
	
										if(assignment.hasOwnProperty('obj')) {
						
											assignmentDom(ui.cards['card'+assignment.id].content, assignment.obj, function() {
												
												ui.patch();
												
											}, undefined);	
											
										}
	
									});

									ui.patch();

								}

								function reload_data(assignmentObj_data, callback) {

									var queryObj = {
											id: {
												type: 'or',
												values: assignmentObj_data.ids
											}
										};

									if(assignmentObj_data.group_type !== '') {

										queryObj.group_type = assignmentObj_data.group_type;

									}

									sb.data.db.obj.getWhere(assignmentObj_data.objectType, queryObj, function(data) {

										var dataIds = _.pluck(data, 'id');;
										var assignments = _.filter(currentAssignments, function(assignment) {
												return _.contains(dataIds, assignment.id);
											});
										
										callback(assignments);

									});

								}

								assignmentObj_data.objectType = groupedList[0].obj.object_bp_type;

								if(groupedList[0].obj.hasOwnProperty('group_type')) {
									assignmentObj_data.group_type = groupedList[0].obj.group_type;
								}

								ui.modal.body.makeNode('head', 'div', {});
								ui.modal.body.makeNode('lb_1', 'lineBreak', {
									sapces: 1
								});
								ui.modal.body.makeNode('body', 'div', {});

								ui.modal.body.head.makeNode('grid', 'div', {
									css: 'ui grid'
								});

								ui.modal.body.head.grid.makeNode('col1', 'div', {
									css: 'ten wide column'
								});
								ui.modal.body.head.grid.makeNode('col2', 'div', {
									css: 'six wide column'
								});

								ui.modal.body.head.grid.col1.makeNode('title', 'div', {
									tag: 'h2',
									text: action.title,
									css: 'ui header'
								});

								ui.modal.body.head.grid.col2.makeNode('btnGrp', 'div', {});

								ui.modal.body.head.grid.col2.btnGrp.makeNode('close', 'div', {
									text: 'Close',
									css: 'ui red right floated button'
								}).notify('click', {
									type: [sb.moduleId+'-run'],
									data: {
										run: function() {

											ui.modal.hide();

										}
									}
								}, sb.moduleId);

								build_body(ui.modal.body.body, groupedList);

								ui.modal.body.patch();

							}
						});

						ui.patch();
						ui.modal.show();

					}
					
					function desktop() {
						
						ui.makeNode('bottomContent', 'div', {
							css: 'content hidden',
							style:'padding: 0.2rem;'
						});
						
						ui.bottomContent.makeNode('wrap', 'div', {});
						
						ui.bottomContent.wrap.makeNode('col1', 'div', {
							css: 'left floated text-muted'
						});
						
						ui.bottomContent.wrap.makeNode('col2', 'div', {
							css: 'right floated'
						});
						
						if(options.subviews.matrix.hasOwnProperty('assignments')) {
							
							if(
								options.subviews.matrix.assignments.hasOwnProperty('actions') 
								&& !_.isEmpty(options.subviews.matrix.assignments.actions)
							) {
								
								_.each(options.subviews.matrix.assignments.actions, function(actionObj, actionName) {
		
									var shouldShow = true;
		
									if(actionObj.hasOwnProperty('shouldShow')) {
		
										shouldShow = actionObj.shouldShow(assignee, assignment, currentLayout);
										
									}
									
									if(shouldShow) {
										
										ui.bottomContent.wrap.col1.makeNode(actionName, 'div', {
											tag: 'span',
											text: '<i class="small '+ actionObj.icon +' icon"></i>',
											css: 'right floated show-pointer bento-color-hover',
											style: 'margin-right: 5px;',
											tooltip: {
												text: actionObj.title,
												position: 'bottom center'
											},
											listener:{
												type: 'popup',
												hoverable: true
											}
										}).notify('click', {
											type: [sb.moduleId+'-run'],
											data: {
												run: function(data) {
	
													if(groupedList !== undefined) {
	
														if(groupedList.length > 1) {
	
															build_multipleAssignmentModal(ui, actionObj);
	
														} else {
	
															actionObj.action(ui.content, assignment, assignee, function() {
											
																methods.refresh.unassigned_row(true);
																
																if(currentLayout === 'week') {
																	
																	methods.refresh.week_view(assignee, true);		
																	
																} else if(currentLayout === 'day') {
																	
																	methods.refresh.day_view(assignee, true);
																	
																}
													
															});
		
														}
	
													} else {
	
														actionObj.action(ui.content, assignment, assignee, function() {
										
															methods.refresh.unassigned_row(true);
															
															if(currentLayout === 'week') {
																
																methods.refresh.week_view(assignee, true);		
																
															} else if(currentLayout === 'day') {
																
																methods.refresh.day_view(assignee, true);
																
															}
												
														});
	
													}
										
												}
											}
										});		
										
									}
									
								});
								
							}
							
						}
						
						ui.bottomContent.patch();
												
						ui.listeners = [
							function(selector) {
																
								$(selector).hover(function(e) {
								
									$(ui.bottomContent.selector).removeClass('hidden');
									
								});	
								
								$(selector).on('mouseleave', function(e) {
									
									$(ui.bottomContent.selector).addClass('hidden');
									
								});
								
							}
						];	
						
					}
					
					function mobile() {
						
						if(options.subviews.matrix.hasOwnProperty('assignments')) {
							
							if(
								options.subviews.matrix.assignments.hasOwnProperty('actions') 
								&& !_.isEmpty(options.subviews.matrix.assignments.actions)
							) {
								
								ui.makeNode('btnGrp', 'div', {
									css: 'fluid ui vertical labeled icon buttons'
								});
								
								_.each(options.subviews.matrix.assignments.actions, function(actionObj, actionName) {
									
									var shouldShow = true;
		
									if(actionObj.hasOwnProperty('shouldShow')) {
		
										shouldShow = actionObj.shouldShow(assignee, assignment, currentLayout);
										
									}
									
									if (shouldShow) {
										
										ui.btnGrp.makeNode(actionName, 'div', {
											css: 'ui button'
											, text: '<i class="'+ actionObj.icon +' icon"></i> '+ actionObj.title
											, style: 'margin-bottom: 20px;'
										}).notify('click', {
											type: [sb.moduleId+'-run'],
											data: {
													run: function(data) {
														
														actionObj.action(ui, assignment, assignee, function(setup) {
										
															methods.refresh.unassigned_row(true);
															
															methods.refresh.week_view(assignee, true);
															
															if (
																setup
																&& setup.hasOwnProperty('closeModal')
															) {
																
																UI_CACHE.modal.hide();
																
															}		
												
														});
														
													}
												}
											}, sb.moduleId);
										
									}
									
								});
								
							}
							
						}
						
					}

					if (!OnMobile) {
						
						desktop();
						
					} else {
						
						mobile();
						
					}

				}
				
				function build_mobileItemView(modalUI, assignment) {

					if (
						options.subviews.matrix.hasOwnProperty('assignments')
						&& options.subviews.matrix.assignments.hasOwnProperty('singleView')
					) {

						options.subviews.matrix.assignments.singleView(modalUI.body, assignment)
						
					}
					
					build_cardActions(modalUI.footer);
					
					modalUI.patch();
					modalUI.show();
						
				}
				
				if (OnMobile) {
					
					drag.moves = false;
					
				}
				
				if(options.subviews.matrix.assignments.hasOwnProperty('css')) {
					
					cardCSS = options.subviews.matrix.assignments.css(assignee, assignment, currentLayout);
					
				}
				
				if(options.subviews.matrix.assignments.hasOwnProperty('tooltip')) {
					
					tooltip.text = options.subviews.matrix.assignments.tooltip.text(assignment);
					tooltip.position = options.subviews.matrix.assignments.tooltip.position;
					
				}

				if(currentLayout === 'week') {
					
					cardStyle = 'margin: 0.2rem 0;'
					
				} else if(currentLayout === 'day') {
					
					cardStyle = 'background-color: #F8F8F8; border-radius: 0 !important; position: relative; margin: 5px 0 5px 0; z-index: 100; left: '+ navigate_grid(moment(assignment.startTime).format('ha'), moment(assignment.endTime).format('ha')).left +'%; width: '+ navigate_grid(moment(assignment.startTime).format('ha'), moment(assignment.endTime).format('ha')).width +'%;';
					
				}

				if(groupedList !== undefined
				&& groupedList.length > 1) {

					drag.copy = true;

				}
					
				ui.makeNode('card'+assignment.id, 'div', {
					css: 'ui raised card show-grab ' + cardCSS,
					style: cardStyle + ' white-space: nowrap; overflow: hidden; text-overflow: ellipsis;',
					drag: drag
				});
				
				ui['card'+assignment.id].makeNode('content', 'div', {
					css: 'content truncate',
					style: 'padding: 0.5rem;',
					tooltip: tooltip,
					listener:{
						type: 'popup',
						hoverable: true
					}
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function() {

							if(ui['card'+assignment.id].hasOwnProperty('selected')) {
								
								if(ui['card'+assignment.id].selected === true) {
									
									if(groupedList !== undefined) {

										_.each(groupedList, function(assignment) {

											currentSelection.push(assignment.id);

										});

									} else {

										currentSelection.push(assignment.id);

									}
									
									options.updateSelection(currentSelection);
									
									ui['card'+assignment.id].selected = false;
									
									if (!OnMobile) {
										
										$(ui['card'+assignment.id].selector).css('border', '1px solid #027eff');	
										
									} else {
									
										build_mobileItemView(UI_CACHE.modal, assignment);
										
									}
									
								} else {

									if(groupedList !== undefined) {

										_.each(groupedList, function(assignment) {

											currentSelection = _.reject(currentSelection, function(id) {
										
												return id === assignment.id;
												
											});

										});

									} else {

										currentSelection = _.reject(currentSelection, function(id) {
										
											return id === assignment.id;
											
										});

									}
									
									options.updateSelection(currentSelection);
									
									ui['card'+assignment.id].selected = true;
									
									if (!OnMobile) {
										
										$(ui['card'+assignment.id].selector).css('border', 'none');	
										
									} else {
									
										build_mobileItemView(UI_CACHE.modal, assignment);
										
									}
									
								}
								
							} else {
								
								if(groupedList !== undefined) {

									_.each(groupedList, function(assignment) {

										currentSelection.push(assignment.id);

									});

								} else {

									currentSelection.push(assignment.id);

								}
								
								options.updateSelection(currentSelection);
								
								ui['card'+assignment.id].selected = false;
								
								if (!OnMobile) {
									
									$(ui['card'+assignment.id].selector).css('border', '1px solid #027eff');	
									
								} else {
									
									build_mobileItemView(UI_CACHE.modal, assignment);
									
								}
								
							}

						}
					}
				}, sb.moduleId);

				if(groupedList !== undefined) {

					if(groupedList.length > 1) {

						ui['card'+assignment.id].content.makeNode('grid', 'div', {
							css: 'ui grid'
						});
	
						ui['card'+assignment.id].content.grid.makeNode('col1', 'div', {
							css: 'thirteen wide column truncate'
						});
	
						ui['card'+assignment.id].content.grid.makeNode('col2', 'div', {
							css: 'three wide column'
						});

						ui['card'+assignment.id].content.grid.col2.makeNode('label', 'div', {
							css: 'ui mini right floated label',
							text: groupedList.length
						});

						cardBodyUI = ui['card'+assignment.id].content.grid.col1;

					} else {

						cardBodyUI = ui['card'+assignment.id].content;

					}

				} else {

					cardBodyUI = ui['card'+assignment.id].content;

				}
				
				if(assignment.hasOwnProperty('obj')) {
					
					assignmentDom(cardBodyUI, assignment.obj, function() {
						
						ui.patch();
						
					}, currentLayout);	
					
				}
				
				if (!OnMobile) {
					
					build_cardActions(ui['card'+assignment.id]);	
					
				}
				
				methods.refresh.assignmentCard = function(dropData, isLoading) {

					var cellUI = dropData.dropped.cellUI;
					var item = dropData.dropped.assignment; // This is the assignment obj

					if(isLoading === true) {

						cellUI['card'+item.id].empty();

						cellUI['card'+item.id].makeNode('loaderWrap', 'div', {
							css: 'text-center',
							style: 'padding: 5px;'
						});

						cellUI['card'+item.id].loaderWrap.makeNode('loader', 'loader', {});
						cellUI['card'+item.id].loaderWrap.makeNode('loadText', 'div', {
							text: 'Updating...'
						});

						cellUI['card'+item.id].patch();

					} else {

						build_assignmentCard(ui, assignment, assignee);

					}
					
				}
				
			}
			
			function build_placeHolder(ui) {
				
				ui.makeNode('holder', 'div', {
					css: 'ui placeholder',
					style: 'max-width: 100% !important;'
				});
				
				ui.holder.makeNode('square', 'div', {
					css: 'image'
				});
				
			}
			
			function modify_assignments(assignment, currentDataSet, callback) {
				
				
				
			}
			
			function group_assignments(ui, list, callback, setupArgs) {

				var typeBtnSelectors = [];
				var currentType = undefined;
				var setup = {};

				function matrix_grouping(ui, groupedList, groupingProperty) {

					ui.makeNode('groupingWrap', 'div', {
						style: 'margin: 2px 0 0 0;'
					});

					var groupingOptions = [];

					groupingOptions = _.map(groupedList, function(groupedObjs, type) {

						var displayName = '';

						if (groupedObjs[0][groupingProperty].hasOwnProperty('name')) {
							
							displayName = groupedObjs[0][groupingProperty].name;
							
						}

						return {
							name: groupedObjs.length+' - '+displayName,
							value: type
						};

					});
					
					groupingOptions.unshift({
						name: 'All',
						value:'All',
						selected:true
					});

					ui.groupingWrap.makeNode('dropDownList', 'div', {
						text:'<i class="caret right icon"></i> <span class="text">',
						css:'ui fluid dropdown item',
						tag: 'select',
						listener: {
							onChange: function(value, text) {
								
								if(value !== 'All'){
									
									callback(groupedList[value]);

									if (
										setup.hasOwnProperty('rowType')
										&& setup.rowType === 'unassigned'
									) {
										
										if (
											options.subviews.matrix.hasOwnProperty('groupMainData')
											&& !_.isEmpty(options.subviews.matrix.groupMainData)
										) {
											
											var filteredData = [];
											
											_.each(assigneeList, function(o) {

												if (_.isArray(o[options.subviews.matrix.groupMainData.by])) {
													
													_.each(o[options.subviews.matrix.groupMainData.by], function(x) {
														
														if (x.id === parseInt(value)) {
															
															filteredData.push(o);
															
														}
														
													});
													
												}
												
											});
											
											refresh_rows(filteredData);
											
										}	
										
									}

								} else {

									callback(undefined);

									if (
										setup.hasOwnProperty('rowType')
										&& setup.rowType === 'unassigned'
									) {
										
										if (
											options.subviews.matrix.hasOwnProperty('groupMainData')
											&& !_.isEmpty(options.subviews.matrix.groupMainData)
										) {
											
											refresh_rows(assigneeList);
											
										}	
										
									}
								}
					
							},
							type:'dropdown',
							values: groupingOptions
						}
					});	

					ui.patch();	

				}

				function gantt_grouping(ui, groupedList) {
					
					ui.makeNode('groupingWrap', 'div', {
						style: 'margin: 0 0 10px 0;'
					});

					_.each(groupedList, function(groupedObjs, type) {
						
						ui.groupingWrap.makeNode('itemNum-'+type.id, 'div', {
							text: type + ' <div class="detail">' + groupedObjs.length + '</div>',
							css: 'ui label',
							style: 'float: right; margin: 0 2px 0 0;'
						}).notify('click', {
							type: [sb.moduleId+'-run'],
							data: {
								run: function(data) {

									if(_.size(list) > 1) {

										_.each(typeBtnSelectors, function(btnUIObj) {

											$(btnUIObj.selector).removeClass('green');
											$(btnUIObj.selector).css('transform', 'translateY(0px)');
											
										});

										$(ui.groupingWrap['itemNum-'+type.id].selector).addClass('green');
										$(ui.groupingWrap.itemNum_all.selector).removeClass('green');

										if(ui.groupingWrap['itemNum-'+type.id].hasOwnProperty('clicked')) {
											
											if(ui.groupingWrap['itemNum-'+type.id].clicked === true) {
												
												ui.groupingWrap['itemNum-'+type.id].clicked = false;
												
												$(ui.groupingWrap['itemNum-'+type.id].selector).css('transform', 'translateY(-5px)');
												
												callback(groupedObjs, currentType, groupedObjs[0][currentType]);
												
											} else {
												
												ui.groupingWrap['itemNum-'+type.id].clicked = true;
												
												$(ui.groupingWrap['itemNum-'+type.id].selector).css('transform', 'translateY(-5px)');
												
												callback(groupedObjs, currentType, groupedObjs[0][currentType]);
												
											}
											
										} else {
											
											ui.groupingWrap['itemNum-'+type.id].clicked = true;
											
											$(ui.groupingWrap['itemNum-'+type.id].selector).css('transform', 'translateY(-5px)');
											
											callback(groupedObjs, currentType, groupedObjs[0][currentType]);
											
										}

									}

								}
							}
						}, sb.moduleId);

						typeBtnSelectors.push(ui.groupingWrap['itemNum-'+type.id]);

					});

					if(_.size(list) > 1) {
						
						ui.groupingWrap.makeNode('itemNum_all', 'div', {
							text: 'All',
							css: 'ui green label allTypeBtn',
							style: 'float: right; margin: 0 2px 0 0;'
						}).notify('click', {
							type: [sb.moduleId+'-run'],
							data: {
								run: function(data) {
									
									$(ui.groupingWrap.itemNum_all.selector).addClass('green');
									
									_.each(typeBtnSelectors, function(btnUIObj) {

										$(btnUIObj.selector).removeClass('green');
										$(btnUIObj.selector).css('transform', 'translateY(0px)');
										
									});
									
									callback(undefined, undefined, undefined);
									
								}
							}
						}, sb.moduleId);
						
					}

					ui.groupingWrap.makeNode('lb', 'lineBreak', {
						spaces: 1
					});

					ui.patch();

				}
				
				if (OnMobile) {
					
					callback(list);
					return;
					
				}
				
				if (setupArgs !== undefined) {
					
					setup = setupArgs;
					
				}
				
				if(!_.isEmpty(list)) {

					if(
						setup.hasOwnProperty('rowType')
						&& setup.rowType === 'unassigned'
					) {
						
						var groupingProperty = undefined;

						if(options.subviews.matrix.assignments.hasOwnProperty('groupBy') 
						&& !_.isEmpty(options.subviews.matrix.assignments.groupBy)) {

							_.each(options.subviews.matrix.assignments.groupBy, function(value1, key1) {

								currentType = key1;

								if(typeof value1 === 'object') {
									
									_.each(value1, function(value2, key2) {
										
										list = _.groupBy(list, function(assignment) {
											
											return assignment[options.subviews.matrix.assignments.groupBy[key1][value2]].id;
											
										});
										
										return;
										
									});
									
								} else if(typeof value1 === 'string') {

									list = _.groupBy(list, function(assignment) {
	
										return assignment[options.subviews.matrix.assignments.groupBy[value1]].id;
										
									});

									return;	
									
								}
								
							});

							if(currentLayout === 'week') {
								
								matrix_grouping(ui, list, currentType);
								
							} else if(currentLayout === 'day') {
								
								gantt_grouping(ui, list, currentType);
								
							}

						} else {
					
							ui.makeNode('itemNum', 'div', {
								text: list.length + ' items',
								css: 'text-right'
							});	
							
						}

					} else if (
						list.length > 1
						&& !setup.hasOwnProperty('rowType')
					) {
						
						var groupingProperty = undefined;

						if(options.subviews.matrix.assignments.hasOwnProperty('groupBy') 
						&& !_.isEmpty(options.subviews.matrix.assignments.groupBy)) {

							_.each(options.subviews.matrix.assignments.groupBy, function(value1, key1) {

								currentType = key1;

								if(typeof value1 === 'object') {
									
									_.each(value1, function(value2, key2) {
										
										list = _.groupBy(list, function(assignment) {
											
											return assignment[options.subviews.matrix.assignments.groupBy[key1][value2]].id;
											
										});
										
										return;
										
									});
									
								} else if(typeof value1 === 'string') {

									list = _.groupBy(list, function(assignment) {
	
										return assignment[options.subviews.matrix.assignments.groupBy[value1]].id;
										
									});

									return;	
									
								}
								
							});

							if(currentLayout === 'week') {
								
								matrix_grouping(ui, list, currentType);
								
							} else if(currentLayout === 'day') {
								
								gantt_grouping(ui, list, currentType);
								
							}

						} else {
					
							ui.makeNode('itemNum', 'div', {
								text: list.length + ' items',
								css: 'text-right'
							});	
							
						}
						
					}
					
				}
				
			}
			
			function build_bottomBar(ui, assignmentList, table_setup, viewLayout) {

				ui.makeNode('grid', 'div', {
					css: 'ui grid'
				});
				
				ui.grid.makeNode('col1', 'div', {
					css: 'two wide column hide-scrollbar',
					style: 'background-color: #F3F4F5;'
				});

				options.subviews.matrix.bottomBar(ui.grid.col1, assignmentList, undefined, currentDataSet);
				
				ui.grid.makeNode('col2', 'div', {
					css: 'fourteen wide column',
					style: ''
				});
				
				ui.grid.col2.makeNode('grid', 'div', {
					css: 'ui equal width grid'
				});
				
				_.each(table_setup, function(dayDate, dayName) {
					
					ui.grid.col2.grid.makeNode('col_'+dayName, 'div', {
						css: 'column',
						style: 'padding: 5px !important;'
					});
					
					options.subviews.matrix.bottomBar(ui.grid.col2.grid['col_'+dayName], assignmentList, dayDate.clone().add(sb.dom.utcOffset, 'hours'), currentDataSet);
					
				});
				
				methods.refresh.bottomBar = function() {
					
					build_bottomBar(ui, currentAssignments, table_setup, currentDataSet);
					
					ui.patch();
					
				}
				
			}
			
			// Matrix
			function week_view(ui, date, patch) {
				
				var day = date.clone().startOf('week').add(1, 'day');
				var table_setup = {};
					
				function build_header(ui) {
					
					var col1_width = '';
					var col2_width = '';
					var col2_style = '';
					var todayHighlight = '';
					
					if(OnMobile) {
						
						col1_width = 'five';
						col2_width = 'eleven';
						
						col2_style = 'padding: 0px;';
						
					} else {
						
						col1_width = 'two';
						col2_width = 'fourteen';
						
						col2_style = '';
							
					}
					
					ui.makeNode('top_row', 'div', {
						css: 'row',
						style: 'background-color: #F8F8F8; box-shadow: 0px 5px 5px -4px lightgrey;'
					});
					
					ui.top_row.makeNode('col1', 'div', {
						css: col1_width + ' wide column'
					});
					ui.top_row.makeNode('col2', 'div', {
						css: col2_width + ' wide column',
						style: col2_style
					});
					
					ui.top_row.col2.makeNode('grid', 'div', {
						css: 'ui equal width grid'
					});
					
					_.each(table_setup, function(dayDate, dayName) {
						
						if( (moment().dayOfYear() === dayDate.dayOfYear()) 
						&& (moment().year() === dayDate.year()) ) {
							todayHighlight = 'color: #2185D0 !important;';
						} else {
							todayHighlight = '';
						}
						
						if(OnMobile) {
							
							ui.top_row.col2.grid.makeNode('col_'+dayName, 'div', {
								css: 'column text-muted text-center',
								style: 'padding: 0px;'
							});
							
							ui.top_row.col2.grid['col_'+dayName].makeNode('grid', 'div', {
								css: 'ui grid'
							});
							
							ui.top_row.col2.grid['col_'+dayName].grid.makeNode('col_left', 'div', {
								css: 'three wide column',
								text: '<i class="left arrow icon"></i>'
							}).notify('click', {
								type: [sb.moduleId+'-run'],
								data: {
									run: function(data) {
										
										currentDate = dayDate.clone().subtract(1, 'day');
										
										get_assignments(options, function(assignmentList) {
											
											matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate, viewLayout);
											return;
											
										});
										
									}
								}
							}, sb.moduleId);
														
							ui.top_row.col2.grid['col_'+dayName].grid.makeNode('col_center', 'div', {
								css: 'ten wide column',
								style: todayHighlight,
								text: dayDate.format('ddd MMM Do')
							});
							
							ui.top_row.col2.grid['col_'+dayName].grid.makeNode('col_right', 'div', {
								css: 'three wide column',
								text: '<i class="right arrow icon"></i>'
							}).notify('click', {
								type: [sb.moduleId+'-run'],
								data: {
									run: function(data) {
										
										currentDate = dayDate.clone().add(1, 'day');
										
										get_assignments(options, function(assignmentList) {
											
											matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate, viewLayout);
											return;
											
										});
										
									}
								}
							}, sb.moduleId);
							
						} else {
							
							ui.top_row.col2.grid.makeNode('col_'+dayName, 'div', {
								css: 'column text-center linkHover',
								style: todayHighlight,
								text: dayDate.format('ddd MMM Do')
							}).notify('click', {
								type: [sb.moduleId+'-run'],
								data: {
									run: function(data) {

										currentLayout = 'day';
										currentDate = dayDate.clone();
							
										$('.weekLayout').removeClass('active');
										$('.dayLayout').addClass('active');
										
										methods.refresh.dateRangeSwitch(dayDate.clone());

										UI_CACHE.matrix_body.empty();

										build_placeholder(UI_CACHE.matrix_body);

										UI_CACHE.matrix_body.patch();

										get_assignments(options, function(assignmentList) {

											matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, currentDate, 'day');
											return;
										});

									}
								}
							}, sb.moduleId);	
							
						}
						
					});
					
				}
				
				function build_unassignedRow(ui) {

					var col1_width = '';
					var col2_width = '';
					var col2_style = '';
					
					function build_singleCell(ui, cellDate) {

						var hideBtnBar_ui = 'hidden';
						var cellAssignmentList = [];
						
						function build_cardAreaUI(ui, grabAssignments, activeAssignments) {
							
							if(activeAssignments === undefined) {
								
								activeAssignments = currentAssignments;
								
							}
							
							if(!_.isEmpty(activeAssignments)) {
							
								_.each(activeAssignments, function(assignment) {
	
									if( moment(assignment.startTime).dayOfYear() === cellDate.dayOfYear() 
									&& moment(assignment.startTime).year() === cellDate.year() ) {
										
										if(
											options.subviews.matrix.hasOwnProperty('viewMenu')
											&& !_.isEmpty(options.subviews.matrix.viewMenu)
										) {
											
											if(
												options.subviews.matrix.viewMenu[currentView.name].shouldDisplay(undefined, assignment, cellDate.clone())
											) {
											
												build_assignmentCard(ui, assignment, undefined);
												
												if(grabAssignments) {
													
													cellAssignmentList.push(assignment);		
													
												}
												
											}	
											
										} else {
											
											if(options.subviews.matrix.shouldDisplay(undefined, assignment, cellDate.clone())) {
												
												build_assignmentCard(ui, assignment, undefined);
												
												if(grabAssignments) {
													
													cellAssignmentList.push(assignment);		
													
												}
																							
											}
											
										}
										
									}
									
								});	
								
							}
							
						}
						
						if(OnMobile) {
							hideBtnBar_ui = '';
						} else {
							hideBtnBar_ui = 'hidden';
						}
						
						ui.makeNode('itemNumWrap', 'div', {});
						
						ui.makeNode('seg', 'div', {
							css: 'ui attached segment hide-scrollbar',
							style: 'padding: 2px; min-height: 30px; max-height: 100px; overflow: scroll; background-color: white;'
						});
						
						build_cardAreaUI(ui.seg, true);
			
						group_assignments(ui.itemNumWrap, cellAssignmentList, function(groupedObjs) {

							ui.seg.empty();
							build_cardAreaUI(ui.seg, false, groupedObjs);
							ui.seg.patch();
							
						}, {
							rowType: 'unassigned'
						});
						
						if(subviewActions.hasOwnProperty('create') && subviewActions.create !== false) {

							ui.makeNode('btnbar', 'div', {
								style: 'padding: 1px;',
								css: 'ui bottom attached inverted button ' + hideBtnBar_ui
							});

							ui.btnbar.makeNode('btn', 'div', {
								css: 'ui bottom attached mini basic button',
								text: '<i class="green plus icon"></i>',
								style: 'padding: 0px;'
							}).notify('click', {
								type: [sb.moduleId+'-run'],
								data: {
									run: function(data) {
		
										subviewActions.create(UI_CACHE.modal, undefined, cellDate.clone(), function() {
											
											get_assignments(options, function(assignmentList) {

												build_singleCell(ui, cellDate.clone());
												methods.refresh.bottomBar();
												
												ui.patch();
												
												UI_CACHE.modal.hide();
												
											});
														
										});
										
										UI_CACHE.modal.show();
										
									}
								}
							}, sb.moduleId);

							if(!OnMobile) {
								
								ui.listeners = [
									function(selector) {
										
										$(selector).hover(function(e) {
											
											$(ui.btnbar.selector).removeClass('hidden');
											
										});
										
										$(selector).on('mouseleave', function(e) {
		
											$(ui.btnbar.selector).addClass('hidden');
											
										});
										
									}
								];	
								
							}

						}
						
					}
					
					if(OnMobile) {
						
						col1_width = 'five';
						col2_width = 'eleven';
						
						col2_style = 'padding: 0px;';
						
					} else {
						
						col1_width = 'two';
						col2_width = 'fourteen';
						
						col2_style = '';
							
					}
					
					ui.makeNode('unassigned_row', 'div', {
						css: 'row',
						style: 'padding-bottom: 1.5rem; background-color: #F8F8F8;'
					});
					
					ui.unassigned_row.makeNode('col1', 'div', {
						css: col1_width + ' wide column'
					});
					
					ui.unassigned_row.makeNode('col2', 'div', {
						css: col2_width + ' wide column',
						style: col2_style
					});
					
					ui.unassigned_row.col2.makeNode('grid', 'div', {
						css: 'ui equal width grid'
					});
					
					_.each(table_setup, function(dayDate, dayName) {
					
						ui.unassigned_row.col2.grid.makeNode('col_'+dayName, 'div', {
							css: 'column',
							style: 'padding: 5px !important;'
						});
						
						build_singleCell(ui.unassigned_row.col2.grid['col_'+dayName], dayDate.clone());
						
					});
					
					methods.refresh.unassigned_row = function(getAssignments) {
						
						if(getAssignments) {
							
							get_assignments(options, function(assignmentList) {
									
								ui.empty();
								build_unassignedRow(ui);
								methods.refresh.bottomBar();
								ui.patch();
								
							});	
							
						} else {
							
							ui.empty();
							build_unassignedRow(ui);
							methods.refresh.bottomBar();
							ui.patch();
							
						}
						
					};
					
				}
				
				function build_rightCol(ui, obj) {
					
					var col_width = '';
					var col_style = '';
					
					if(OnMobile) {
						col_width = 'eleven';
						col_style = 'padding: 0px;';
					} else {
						col_width = 'fourteen';
						col_style = 'margin: 0 0 10px 0;';
					}
					
					function build_singleCell(ui, cellObj, cellDate, rowUI) {

						var hideBtnBar_ui = 'hidden';
						var cellAssignmentList = [];
						
						function build_cardAreaUI(ui, grabAssignments, activeAssignments) {
							
							if(activeAssignments === undefined) {
								
								activeAssignments = currentAssignments;
								
							}
							
							if(!_.isEmpty(activeAssignments)) {
							
								_.each(activeAssignments, function(assignment) {
	
									if( moment(assignment.startTime).dayOfYear() === cellDate.dayOfYear() 
									&& moment(assignment.startTime).year() === cellDate.year() ) {
										
										if(
											options.subviews.matrix.hasOwnProperty('viewMenu')
											&& !_.isEmpty(options.subviews.matrix.viewMenu)
										) {
											
											if( options.subviews.matrix.viewMenu[currentView.name].shouldDisplay(cellObj, assignment, cellDate.clone()) ) {
											
												build_assignmentCard(ui, assignment, cellObj);
												
												if(grabAssignments) {
													
													cellAssignmentList.push(assignment);	
													
												}
												
											} 
											
										} else {
											
											if(options.subviews.matrix.shouldDisplay(cellObj, assignment, cellDate.clone())) {
												
												build_assignmentCard(ui, assignment, cellObj);	
												
												if(grabAssignments) {
													
													cellAssignmentList.push(assignment);	
													
												}
												
											}
											
										}	
										
									}
									
								});	
								
							} else {

							}
							
						}
						
						if(OnMobile) {
							hideBtnBar_ui = '';
						} else {
							hideBtnBar_ui = 'hidden';
						}
						
						ui.makeNode('itemNumWrap', 'div', {});

						ui.makeNode('seg', 'div', {
							css: 'ui secondary attached segment hide-scrollbar',
							style: 'height: 90px !important; padding: 2px !important; overflow: scroll;',
							drag: {
								data: {
									assignee: cellObj,
									date: cellDate.clone(),
									refresh: function(dropData) {

										methods.refresh.assignmentCard(dropData, true);
										
										get_assignments(options, function(assignmentList) {

											methods.refresh.unassigned_row(false);
											build_rightCol(rowUI, cellObj);
											methods.refresh.leftCol(cellObj);

											if(dropData.dropped.assignee !== undefined) {
												build_rightCol(rowUI, dropData.dropped.assignee);
												methods.refresh.leftCol(dropData.dropped.assignee);
											}

											methods.refresh.assignmentCard(dropData, false);

											methods.refresh.bottomBar();
											
											refresh_rows(assigneeList);
											
										});
										
									}
								},
								accepts: true,
								moves: false
							}
						});
						
						build_cardAreaUI(ui.seg, true, undefined);
						
						group_assignments(ui.itemNumWrap, cellAssignmentList, function(groupedObjs) {

							ui.seg.empty();
							build_cardAreaUI(ui.seg, false, groupedObjs);
							ui.seg.patch();
							
						});

						if(subviewActions.hasOwnProperty('create') && subviewActions.create !== false) {
						
							ui.makeNode('btnbar', 'div', {
								style: 'padding: 1px;',
								css: 'ui bottom attached inverted button ' + hideBtnBar_ui
							});
							
							ui.btnbar.makeNode('btn', 'div', {
								css: 'ui bottom attached mini basic button',
								text: '<i class="green plus icon"></i>',
								style: 'padding: 0px;'
							}).notify('click', {
								type: [sb.moduleId+'-run'],
								data: {
									run: function(data) {
										
										subviewActions.create(UI_CACHE.modal, obj, cellDate.clone(), function() {
											
											get_assignments(options, function(assignmentList) {

												build_singleCell(ui, cellObj, cellDate.clone());
												methods.refresh.week_view(cellObj);
												
												ui.patch();
												
												UI_CACHE.modal.hide();

											});
											
										});
										
										UI_CACHE.modal.show();
										
									}
								}
							}, sb.moduleId);
							
							if(!OnMobile) {
								
								ui.listeners = [
									function(selector) {
										
										$(selector).hover(function(e) {
											
											$(ui.btnbar.selector).removeClass('hidden');
											
										});
										
										$(selector).on('mouseleave', function(e) {
		
											$(ui.btnbar.selector).addClass('hidden');
											
										});
										
									}
								];	
								
							}

						}
						
					}
					
					ui['row_'+obj.id].makeNode('col2', 'div', {
						css: col_width + ' wide column',
						style: col_style
					});
					
					ui['row_'+obj.id].col2.makeNode('grid', 'div', {
						css: 'ui equal width grid'
					});
					
					_.each(table_setup, function(dayDate, dayName) {
						
						ui['row_'+obj.id].col2.grid.makeNode('col_'+dayName, 'div', {
							css: 'column',
							style: 'padding: 5px !important;'
						});
						
						build_singleCell(ui['row_'+obj.id].col2.grid['col_'+dayName], obj, dayDate.clone(), ui);
						
					});
					
					ui['row_'+obj.id].patch();
					
					methods.refresh.week_view_body = function(ui, item) {

						build_rightCol(ui, item);
						
					}
					
				}

				if(OnMobile) {
					
					table_setup = {
						[day.format('ddd').toLowerCase()]: date	
					};
					
				} else {
					
					table_setup = {
						sun: day,
						mon: day.clone().add(1, 'day'),
						tue: day.clone().add(2, 'day'),
						wed: day.clone().add(3, 'day'),
						thu: day.clone().add(4, 'day'),
						fri: day.clone().add(5, 'day'),
						sat: day.clone().add(6, 'day')	
					};
					
				}
					
				ui.empty();
				
				ui.makeNode('wrapper', 'div', {});
				
				ui.wrapper.makeNode('header_div', 'div', {
					css: 'ui grid'
				});
				
				build_header(ui.wrapper.header_div);

				if(viewType !== undefined) {
					
					if(options.subviews.matrix.viewMenu[viewType].hasOwnProperty('unassignedBar') 
					&& options.subviews.matrix.viewMenu[viewType].unassignedBar === true) {
						
						ui.wrapper.makeNode('unassigned_wrap', 'div', {
							css: 'ui grid',
							style: 'box-shadow: 0px 5px 5px -4px lightgrey; z-index: 100;'
						});
						
						build_unassignedRow(ui.wrapper.unassigned_wrap, table_setup);		
						
					}
					
				} else {

					if(options.subviews.matrix.hasOwnProperty('unassignedBar') 
					&& options.subviews.matrix.unassignedBar === true) {
						
						ui.wrapper.makeNode('unassigned_wrap', 'div', {
							css: 'ui grid',
							style: 'box-shadow: 0px 5px 5px -4px lightgrey; z-index: 100;'
						});
						
						build_unassignedRow(ui.wrapper.unassigned_wrap, table_setup);		
						
					}
					
				}
				
				UI_CACHE.scroll_div = ui.wrapper.makeNode('scroll_div', 'div', {
					css: 'ui grid hide-scrollbar',
					style: 'margin-top: 2rem; min-height: 10vh; max-height: 60vh; overflow-y: scroll;'
				});

				_.each(data, function(item) {
					
					ui.wrapper.scroll_div.makeNode('row_'+item.id, 'div', {
						css: 'row',
						style: 'padding-top: .5rem; padding-bottom: .6rem;'
					});
					
					build_leftCol(ui.wrapper.scroll_div, item);
					
					build_rightCol(ui.wrapper.scroll_div, item);
					
				});
				
				if(options.subviews.matrix.hasOwnProperty('bottomBar')) {
					
					ui.wrapper.makeNode('bottomBar_break', 'lineBreak', {spaces: 1});
					
					ui.wrapper.makeNode('bottomBar', 'div', {
						css: 'row',
						style: 'padding-top: .5rem; padding-bottom: .6rem;'
					});
					
					build_bottomBar(ui.wrapper.bottomBar, currentAssignments, table_setup, viewLayout);	
					
				}
				
				ui.wrapper.makeNode('lb', 'lineBreak', {spaces: 1});

				if(patch !== false || patch === undefined) {
					
					ui.patch();	
					
				}
				
				methods.refresh.week_view = function(item, getAssignments) {
					
					if(getAssignments) {
						
						get_assignments(options, function(assignmentList) {
						
							week_view(ui, date.clone(), false);
							ui.wrapper.scroll_div['row_'+item.id].patch();
							
							methods.refresh.bottomBar();
							
						});	
						
					} else {
						
						week_view(ui, date.clone(), false);
						ui.wrapper.scroll_div['row_'+item.id].patch();
						
						methods.refresh.bottomBar();
						
					}
					
				}
				
			}
			
			// Gantt-like view
			function day_view(ui, date, patch) {
				
				var table_setup = {
						[date.format('ddd').toLowerCase()]: date	
					};
				
				function display_assignments(ui, list, assignee, cacheObjs) {

					cache = [];

					if(!_.isEmpty(list)) {

						if(options.subviews.matrix.assignments.hasOwnProperty('groupBy')
						&& !_.isEmpty(options.subviews.matrix.assignments.groupBy)) {

							var grouped = {};
							var groupedByStartTime = [];
							var groupedByEndTime = [];

							groupedByStartTime = _.groupBy(list, function(assignment) {

								return moment(assignment.startTime).unix();

							});

							groupedByEndTime = _.groupBy(list, function(assignment) {

								return moment(assignment.endTime).unix();

							});

							_.each(list, function(assignment) {

								_.each(groupedByStartTime, function(startTimeList, startTime) {

									_.each(groupedByEndTime, function(endTimeList, endTime) {

										if(moment(assignment.startTime).unix() === parseInt(startTime)
										&& moment(assignment.endTime).unix() === parseInt(endTime)) {

											if(grouped.hasOwnProperty(startTime)) {

												grouped[startTime].push(assignment);

											} else {

												grouped[startTime] = [];

												grouped[startTime].push(assignment);

											}

										}

									});

								});

							});

							_.each(grouped, function(arr, time) {

								_.each(options.subviews.matrix.assignments.groupBy, function(value, key) {

									arr = _.groupBy(arr, function(assignment) {
										return assignment[key];
									});

									_.each(arr, function(typeList, type) {

										var assignedList = [];
										var unassignedList = [];
										var reduced_unassignedList = [];

										_.each(typeList, function(assignment, i) {
											
											if(assignment.assignee !== null) {

												assignedList.push(assignment);

											} else {

												unassignedList.push(assignment);

											}

										});

										reduced_unassignedList.push(unassignedList[0]);

										_.each(reduced_unassignedList, function(assignment) {

											if(assignment !== undefined) {

												if( moment(assignment.startTime).dayOfYear() === date.dayOfYear() 
													&& moment(assignment.startTime).year() === date.year() ) {
													
													if(
														options.subviews.matrix.hasOwnProperty('viewMenu')
														&& !_.isEmpty(options.subviews.matrix.viewMenu)
													) {
													
														if(options.subviews.matrix.viewMenu[currentView.name].shouldDisplay(assignee, assignment, date.clone())) {

															build_assignmentCard(ui, assignment, assignee, unassignedList);
													
															if(cacheObjs) {
																
																cache.push(assignment);
																	
															}

														}
													}		
														
												}

											}

										});

										_.each(assignedList, function(assignment) {

											if(assignment !== undefined) {

												if( moment(assignment.startTime).dayOfYear() === date.dayOfYear() 
													&& moment(assignment.startTime).year() === date.year() ) {
													
													if(
														options.subviews.matrix.hasOwnProperty('viewMenu')
														&& !_.isEmpty(options.subviews.matrix.viewMenu)
													) {
															
														if(options.subviews.matrix.viewMenu[currentView.name].shouldDisplay(assignee, assignment, date.clone())) {
															
															build_assignmentCard(ui, assignment, assignee);
															
															if(cacheObjs) {
																
																cache.push(assignment);
																	
															}
															
														}
														
													}		
														
												}

											}

										});

									});

								});

							});

						} else {

							_.each(list, function(assignment, i) {
															
								if( moment(assignment.startTime).dayOfYear() === date.dayOfYear() 
									&& moment(assignment.startTime).year() === date.year() ) {
									
									if(
										options.subviews.matrix.hasOwnProperty('viewMenu')
										&& !_.isEmpty(options.subviews.matrix.viewMenu)
									) {
											
										if(options.subviews.matrix.viewMenu[currentView.name].shouldDisplay(assignee, assignment, date.clone())) {
											
											build_assignmentCard(ui, assignment, assignee);
											
											if(cacheObjs) {
												
												cache.push(assignment);
													
											}
											
										}
										
									}		
										
								}
								
							});

						}

						return cache;
						
					}
					
				}
					
				function build_header(ui) {
					
					var col2_style = '';
					var todayHighlight = '';
					
					if(OnMobile) {
						
						col2_style = 'padding: 0px;';
						
					} else {
						
						col2_style = '';
							
					}
					
					ui.makeNode('top_row', 'div', {
						css: 'row',
						style: 'background-color: #F8F8F8; box-shadow: 0px 5px 5px -4px lightgrey;'
					});
					
					ui.top_row.makeNode('col1', 'div', {
						css: 'two wide column'
					});
					ui.top_row.makeNode('col2', 'div', {
						css: 'fourteen wide column',
						style: col2_style
					});
					
					ui.top_row.col2.makeNode('grid', 'div', {
						css: 'ui equal width grid'
					});
					
					_.each(table_setup, function(dayDate, dayName) {
						
						if( (moment().dayOfYear() === dayDate.dayOfYear()) 
						&& (moment().year() === dayDate.year()) ) {
							todayHighlight = 'color: #2185D0 !important;';
						} else {
							todayHighlight = '';
						}
						
						if(OnMobile) {
							
							ui.top_row.col2.grid.makeNode('col_'+dayName, 'div', {
								css: 'column text-muted text-center',
								style: 'padding: 0px;'
							});
							
							ui.top_row.col2.grid['col_'+dayName].makeNode('grid', 'div', {
								css: 'ui grid'
							});
							
							ui.top_row.col2.grid['col_'+dayName].grid.makeNode('col_left', 'div', {
								css: 'three wide column',
								text: '<i class="left arrow icon"></i>'
							}).notify('click', {
								type: [sb.moduleId+'-run'],
								data: {
									run: function(data) {
										
										currentDate = dayDate.clone().subtract(1, 'day');
										
										matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, date);
										
									}
								}
							}, sb.moduleId);
														
							ui.top_row.col2.grid['col_'+dayName].grid.makeNode('col_center', 'div', {
								css: 'ten wide column',
								style: todayHighlight,
								text: dayDate.format('ddd MMM Do')
							});
							
							ui.top_row.col2.grid['col_'+dayName].grid.makeNode('col_right', 'div', {
								css: 'three wide column',
								text: '<i class="right arrow icon"></i>'
							}).notify('click', {
								type: [sb.moduleId+'-run'],
								data: {
									run: function(data) {
										
										currentDate = dayDate.clone().add(1, 'day');
										
										matrix_body(UI_CACHE.matrix_body, currentDataSet, currentView.name, date);
										
									}
								}
							}, sb.moduleId);
							
						} else {
							
							ui.top_row.col2.grid.makeNode('col_'+dayName, 'div', {
								css: 'column text-center',
								style: todayHighlight,
								text: dayDate.format('ddd MMM Do')
							});	
							
						}
						
					});
					
				}
				
				function build_unassignedRow(ui) {

					var col1_width = '';
					var col2_width = '';
					var col2_style = '';
					var hour = date.clone().startOf('day');
					var rowAssignments = [];
					var hideBtnBar_ui = 'hidden';
					
					if(OnMobile) {
						
						col1_width = 'five';
						col2_width = 'eleven';
						
						col2_style = 'padding: 0px;';
						
					} else {
						
						col1_width = 'two';
						col2_width = 'fourteen';
						
						col2_style = '';
							
					}
					
					ui.makeNode('unassigned_row', 'div', {
						css: 'row',
						style: 'padding-top: 0; padding-bottom: 1.5rem; background-color: #F8F8F8;'
					});
					
					ui.unassigned_row.makeNode('col1', 'div', {
						css: col1_width + ' wide column'
					});
					
					ui.unassigned_row.makeNode('col2', 'div', {
						css: col2_width + ' wide column',
						style: col2_style + ' padding-left: 0;'
					});
					
					ui.unassigned_row.col2.makeNode('grouping', 'div', {});
					
					ui.unassigned_row.col2.makeNode('grid', 'div', {
						style: 'clear: both;'
					});

					if(subviewActions.hasOwnProperty('create') && subviewActions.create !== false) {

						ui.unassigned_row.col2.makeNode('btnbar', 'div', {
							style: 'padding: 1px;',
							css: 'ui bottom attached inverted button ' + hideBtnBar_ui
						});

						ui.unassigned_row.col2.btnbar.makeNode('btn', 'div', {
							css: 'ui bottom attached mini basic button',
							text: '<i class="green plus icon"></i>',
							style: 'padding: 0px;'
						}).notify('click', {
							type: [sb.moduleId+'-run'],
							data: {
								run: function(data) {

									subviewActions.create(UI_CACHE.modal, undefined, date.clone().startOf('day'), function() {
										
										get_assignments(options, function(assignmentList) {

											methods.refresh.unassigned_row(true);
											
											ui.patch();
											
											UI_CACHE.modal.hide();
											
										});
													
									});
									
									UI_CACHE.modal.show();
									
								}
							}
						}, sb.moduleId);

					}
					
					ui.unassigned_row.col2.grid.makeNode('row1', 'div', {});
					ui.unassigned_row.col2.grid.makeNode('row2', 'div', {
						style: 'clear: both; height: 100px;'
					});
					
					for(var i = 1; i <= 24; i++) {
						
						ui.unassigned_row.col2.grid.row1.makeNode('cell_header'+i, 'div', {
							text: hour.format('h a'),
							style: 'font-size: 10px; width: 4.16%; display: inline-block; float: left;'
						});
						
						ui.unassigned_row.col2.grid.row2.makeNode('cell'+i, 'div', {
							style: 'background-color: white; width: 4.16%; border: 0.1px solid #f4f4f4; float: left; height: 100%;'
						});
						
						hour.add(1, 'hour');
						
					}
					
					ui.unassigned_row.col2.grid.row2.makeNode('wrapper', 'div', {
						style: 'width: 100%; background-color: transparent; position: absolute;'						
					});
					
					/*
get_assignments(options, function(assignmentList) {

						var height = 0;

						rowAssignments = display_assignments(ui.unassigned_row.col2.grid.row2.wrapper, assignmentList, undefined, true);
						
						if(!_.isEmpty(rowAssignments)) {
							
							$(ui.unassigned_row.col2.grouping.selector).css('margin', '0 0 30px 0');
							
							group_assignments(ui.unassigned_row.col2.grouping, rowAssignments, function(groupedObjs, typeFlag, typeId) {

								var activeList = [];
								
								if(groupedObjs !== undefined) {

									activeList = _.groupBy(assignmentList, function(assignment) {
										return assignment[typeFlag];
									});

									activeList = activeList[typeId];

								} else {

									activeList = assignmentList;

								}

								ui.unassigned_row.col2.grid.row2.wrapper.empty();
								display_assignments(ui.unassigned_row.col2.grid.row2.wrapper, activeList, undefined, false);
								ui.unassigned_row.col2.grid.row2.wrapper.patch();
								
								height = $(ui.unassigned_row.col2.grid.row2.wrapper.selector).height();

								$(ui.unassigned_row.col2.grid.row2.selector).height(height + 5);

							});	
							
						}
						
						ui.unassigned_row.col2.patch();

						height = $(ui.unassigned_row.col2.grid.row2.wrapper.selector).height();

						$(ui.unassigned_row.col2.grid.row2.selector).height(height + 5);
						
					});
*/

					if(subviewActions.hasOwnProperty('create') && subviewActions.create !== false) {

						if(!OnMobile) {
								
							ui.unassigned_row.col2.listeners = [
								function(selector) {
									
									$(selector).hover(function(e) {
										
										$(ui.unassigned_row.col2.btnbar.selector).removeClass('hidden');
										
									});
									
									$(selector).on('mouseleave', function(e) {

										$(ui.unassigned_row.col2.btnbar.selector).addClass('hidden');
										
									});
									
								}
							];	
							
						}

					}
					
					methods.refresh.unassigned_row = function(getAssignments) {
						
						if(getAssignments) {
							
							/*
get_assignments(options, function(assignmentList) {
									
								ui.empty();
								build_unassignedRow(ui);
								ui.patch();
								
							});	
*/
							
						} else {
							
							ui.empty();
							build_unassignedRow(ui);
							ui.patch();
							
						}
						
					};
					
				}
				
				function build_rightCol(ui, assignee) {

					var rowAssignments = [];
					var height = 0;
					var hideBtnBar_ui = 'hidden';

					ui['row_'+assignee.id].makeNode('col2', 'div', {
						css: 'fourteen wide column',
						style: 'padding-left: 0; padding-right: 0;'
					});
					
					ui['row_'+assignee.id].col2.makeNode('grouping', 'div', {});
					
					ui['row_'+assignee.id].col2.makeNode('grid', 'div', {
						style: 'height: 100px; min-height: 100px; clear: both;'
					});

					if(subviewActions.hasOwnProperty('create') && subviewActions.create !== false) {

						ui['row_'+assignee.id].col2.makeNode('btnbar', 'div', {
							style: 'padding: 1px;',
							css: 'ui bottom attached inverted button ' + hideBtnBar_ui
						});

						ui['row_'+assignee.id].col2.btnbar.makeNode('btn', 'div', {
							css: 'ui bottom attached mini basic button',
							text: '<i class="green plus icon"></i>',
							style: 'padding: 0px;'
						}).notify('click', {
							type: [sb.moduleId+'-run'],
							data: {
								run: function(data) {

									subviewActions.create(UI_CACHE.modal, assignee, date.clone().startOf('day'), function() {
										
										get_assignments(options, function(assignmentList) {

											build_rightCol(ui, assignee);
											methods.refresh.leftCol(assignee);

											methods.refresh.bottomBar();
											
											ui.patch();
											
											UI_CACHE.modal.hide();
											
										});
													
									});
									
									UI_CACHE.modal.show();
									
								}
							}
						}, sb.moduleId);

					}
					
					for(var i = 1; i <= 24; i++) {
						
						ui['row_'+assignee.id].col2.grid.makeNode('cell'+i, 'div', {
							style: 'border: 0.5px solid #f4f4f4; width: 4.1%; display: inline-block; float: left; height: 100%;',
						});
						
					}
					
					ui['row_'+assignee.id].col2.grid.makeNode('wrapper', 'div', {
						style: 'width: 100%; background-color: transparent; position: absolute; min-height: 100px;',
						drag: {
							data: {
								assignee: assignee,
								date: date.clone(),
								refresh: function(dropData) {

									methods.refresh.assignmentCard(dropData, true);

									get_assignments(options, function(assignmentList) {

										methods.refresh.unassigned_row(false);
										build_rightCol(ui, assignee);
										methods.refresh.leftCol(assignee);
										
										if(dropData.dropped.assignee !== undefined) {
											build_rightCol(ui, dropData.dropped.assignee);
											methods.refresh.leftCol(dropData.dropped.assignee);
										}

										methods.refresh.assignmentCard(dropData, false);

										methods.refresh.bottomBar();
										
									});
										
									
									
								}
							},
							accepts: true,
							moves: false
						}
					});

					/*
get_assignments(options, function(assignmentList) {

						rowAssignments = display_assignments(ui['row_'+assignee.id].col2.grid.wrapper, assignmentList, assignee, true);
						
						if(!_.isEmpty(rowAssignments)) {
							
							group_assignments(ui['row_'+assignee.id].col2.grouping, rowAssignments, function(groupedObjs) {
							
								var activeList = [];
								
								if(groupedObjs !== undefined) {
									activeList = groupedObjs;
								} else {
									activeList = rowAssignments;
								}
								
								ui['row_'+assignee.id].col2.grid.wrapper.empty();
								display_assignments(ui['row_'+assignee.id].col2.grid.wrapper, activeList, assignee, false);
								ui['row_'+assignee.id].col2.grid.wrapper.patch();
								
								height = $(ui['row_'+assignee.id].col2.grid.wrapper.selector).height();

								$(ui['row_'+assignee.id].col2.grid.selector).height(height);

							});	
							
						}
						
						ui['row_'+assignee.id].patch();

						height = $(ui['row_'+assignee.id].col2.grid.wrapper.selector).height();

						$(ui['row_'+assignee.id].col2.grid.selector).height(height);

					});
*/

					if(subviewActions.hasOwnProperty('create') && subviewActions.create !== false) {

						if(!OnMobile) {
							
							ui['row_'+assignee.id].col2.listeners = [
								function(selector) {
									
									$(selector).hover(function(e) {
										
										$(ui['row_'+assignee.id].col2.btnbar.selector).removeClass('hidden');
										
									});
									
									$(selector).on('mouseleave', function(e) {

										$(ui['row_'+assignee.id].col2.btnbar.selector).addClass('hidden');
										
									});
									
								}
							];	
						
						}

					}
					
				}
					
				ui.empty();
				
				ui.makeNode('wrapper', 'div', {});
				
				ui.wrapper.makeNode('header_div', 'div', {
					css: 'ui grid'
				});
				
				build_header(ui.wrapper.header_div);
				
				if(viewType !== undefined) {
					
					if(options.subviews.matrix.viewMenu[viewType].hasOwnProperty('unassignedBar') 
					&& options.subviews.matrix.viewMenu[viewType].unassignedBar === true) {
						
						ui.wrapper.makeNode('unassigned_wrap', 'div', {
							css: 'ui grid',
							style: 'box-shadow: 0px 5px 5px -4px lightgrey; z-index: 100;'
						});
						
						build_unassignedRow(ui.wrapper.unassigned_wrap);		
						
					}
					
				} else {
					
					if(options.subviews.matrix.hasOwnProperty('unassignedBar') 
					&& options.subviews.matrix.unassignedBar === true) {
						
						ui.wrapper.makeNode('unassigned_wrap', 'div', {
							css: 'ui grid',
							style: 'box-shadow: 0px 5px 5px -4px lightgrey; z-index: 100;'
						});
						
						build_unassignedRow(ui.wrapper.unassigned_wrap);		
						
					}
					
				}
				
				ui.wrapper.makeNode('scroll_div', 'div', {
					css: 'ui grid hide-scrollbar',
					style: 'margin-top: 2rem; min-height: 10vh; max-height: 60vh; overflow-y: scroll;'
				});
				
				_.each(data, function(item) {
					
					ui.wrapper.scroll_div.makeNode('row_'+item.id, 'div', {
						css: 'row',
						style: 'padding-top: .5rem; padding-bottom: .6rem;'
					});
					
					build_leftCol(ui.wrapper.scroll_div, item);
					
					build_rightCol(ui.wrapper.scroll_div, item);
					
				});

				if(options.subviews.matrix.hasOwnProperty('bottomBar')) {
					
					ui.wrapper.makeNode('bottomBar_break', 'lineBreak', {spaces: 1});
					
					ui.wrapper.makeNode('bottomBar', 'div', {
						css: 'row',
						style: 'padding-top: .5rem; padding-bottom: .6rem;'
					});
					
					build_bottomBar(ui.wrapper.bottomBar, currentAssignments, table_setup, viewLayout);	
					
				}
				
				ui.wrapper.makeNode('lb', 'lineBreak', {spaces: 1});
				
				if(patch !== false || patch === undefined) {
					
					ui.patch();	
					
				}
				
				methods.refresh.day_view = function(item, getAssignments) {

					if(getAssignments) {
						
						get_assignments(options, function(assignmentList) {
							
							day_view(ui, date.clone(), false);
							ui.wrapper.scroll_div['row_'+item.id].patch();

							methods.refresh.bottomBar();
							
						});	
						
					} else {
						
						day_view(ui, date.clone(), false);
						ui.wrapper.scroll_div['row_'+item.id].patch();

						methods.refresh.bottomBar();
						
					}
					
				}
				
			}
			
			function build_leftCol(ui, assignee) {
					
				var col_width = '';
				var col_style = '';
				
				if(OnMobile) {
					col_width = 'five';
					col_style = 'height: 100px !important; overflow-y: scroll; padding: 0px;';
				} else {
					col_width = 'two';
					col_style = 'height: 100px !important; overflow-y: scroll;';
				}
				
				ui['row_'+assignee.id].makeNode('col1', 'div', {
					css: col_width + ' wide column hide-scrollbar',
					style: col_style
				});

				_.each(fields, function(field, fieldName) {
					
					if(fieldName !== 'tagged_with') {
					
						if(field.view) {

							field.view(
								ui['row_'+assignee.id].col1.makeNode(fieldName, 'div', {}),
								assignee,
								field.options,
								currentAssignments
							);	
							
						} else {
							
							ui['row_'+assignee.id].col1.makeNode('div_'+fieldName, 'div', {
								text: assignee[fieldName]
							});
							
						}
						
					}
					
				});

				methods.refresh.leftCol = function(assignee) {

					build_leftCol(ui, assignee);
					ui.patch();

				}
				
			}
			
			function refresh_rows(data) {

				UI_CACHE.scroll_div.empty();

				_.each(data, function(item) {
					
					UI_CACHE.scroll_div.makeNode('row_'+item.id, 'div', {
						css: 'row',
						style: 'padding-top: .5rem; padding-bottom: .6rem;'
					});
					
					build_leftCol(UI_CACHE.scroll_div, item);
					
					methods.refresh.week_view_body(UI_CACHE.scroll_div, item);
					
				});
				
				UI_CACHE.scroll_div.patch();				
				
			}
			
			assigneeList = data;
				
			if(onDefaultView) {
				
				fields = options.fields;
				
			} else {
				
				fields = options.subviews.matrix.viewMenu[viewType].fields;
				
			}

			if(!_.isEmpty(data)) {
				
				var dataType = data[0].object_bp_type;
				
			}

			if(viewLayout === 'week') {
				
				week_view(ui, date);
				
			} 
			
			if(viewLayout === 'day') {
				
				day_view(ui, date);
				
			}
			
		}
		
		function build_placeholder(ui) {
			
			var col1_width = '';
			var col2_width = '';
			var col2_style = '';
			
			if(OnMobile) {
				
				col1_width = 'five';
				col2_width = 'eleven';
				
				col2_style = 'padding: 0px;';
				
			} else {
				
				ui.makeNode('lb_1', 'lineBreak', {spaces: 2});
				
				col1_width = 'two';
				col2_width = 'fourteen';
				
				col2_style = '';
				
				ui.makeNode('top_row', 'div', {
					css: 'row'
				});
				
				ui.top_row.makeNode('col1', 'div', {
					css: col1_width + ' wide column'
				});
				ui.top_row.makeNode('col2', 'div', {
					css: col2_width + ' wide column',
					style: col2_style
				});
				
				ui.top_row.col2.makeNode('grid', 'div', {
					css: 'ui equal width grid'
				});
				
				for(var i = 1; i <= 7; i++) {
					
					ui.top_row.col2.grid.makeNode('col'+i, 'div', {
						css: 'column'
					});
					
					ui.top_row.col2.grid['col'+i].makeNode('holder', 'div', {
						css: 'ui placeholder'
					});
					
					ui.top_row.col2.grid['col'+i].holder.makeNode('line', 'div', {
						css: 'line'
					});
					
					ui.top_row.col2.grid['col'+i].holder.makeNode('square1', 'div', {
						css: 'image'
					});
					
					ui.top_row.col2.grid['col'+i].holder.makeNode('square2', 'div', {
						css: 'image'
					});
					
					ui.top_row.col2.grid['col'+i].holder.makeNode('square3', 'div', {
						css: 'image'
					});
					
				}
				
			}
			
		}

		currentLayout = 'week';

		if(options.isLoading) {
			
			build_placeholder(ui);
			
		} else {

			if(options.subviews.matrix.hasOwnProperty('assignments')) {
				
				if(options.subviews.matrix.assignments.hasOwnProperty('dom')) {
						
					assignmentDom = options.subviews.matrix.assignments.dom;
					
				}
				
			}
			
			if(options.subviews.matrix.hasOwnProperty('actions')) {
								
				subviewActions = options.subviews.matrix.actions;
				
			}
			
			UI_CACHE.modal = ui.makeNode('modal', 'modal', {});
			
			ui.makeNode('topBreak', 'lineBreak', {spaces: 1});
			
			ui.makeNode('wrapper', 'div', {});
			
			matrix_bar(ui.wrapper);
			
			ui.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
			
			ui.wrapper.makeNode('matrix_body', 'div', {});
			
			UI_CACHE.matrix_body = ui.wrapper.matrix_body;
			
			currentDataSet = list;
			
			if(options.subviews.matrix.hasOwnProperty('date')) {
			
				currentDate = moment(options.subviews.matrix.date).add(sb.dom.utcOffset, 'hours');
				
			}
			
			if(
				options.subviews.matrix.hasOwnProperty('viewMenu') 
				&& !_.isEmpty(options.subviews.matrix.viewMenu)
			) {

				_.each(options.subviews.matrix.viewMenu, function(view, name) {
	
					view.name = name;
					
					views.push(view);
						
				});
				
				var defaultView = _.find(views, function(obj) {
					return obj.default;
				});
	
				if(defaultView !== undefined) {

					currentView = defaultView;
					
					if(options.subviews.matrix.hasOwnProperty('assignments') && options.subviews.matrix.assignments.hasOwnProperty('query')) {
						
						build_placeholder(UI_CACHE.matrix_body);

						get_assignments(options, function(assignmentList) {

							matrix_body(UI_CACHE.matrix_body, list, defaultView.name, currentDate, currentLayout);

						});
						
					}
					
				}	
				
			} else {

				if(
					options.subviews.matrix.hasOwnProperty('assignments') 
					&& options.subviews.matrix.assignments.hasOwnProperty('query')
				) {
					
					build_placeholder(UI_CACHE.matrix_body);

					get_assignments(options, function(options, assignmentList) {

						matrix_body(UI_CACHE.matrix_body, list, undefined, currentDate, currentLayout);

					});
					
				}
				
			}
			
		}
		
	}
	
	return {
		
		dropped: function(data) {

			setTimeout(function() {
			
				if(data.dropped.options.subviews.matrix.hasOwnProperty('onAssignmentDrop')) {
					
					data.dropped.options.subviews.matrix.onAssignmentDrop(data, function(resp) {
						
						data.in.refresh(data);
						
					});
					
				}
				
			}, 0.1);
			
		},
		
		init: function() {
			
			if($(window).width() < 769){
				OnMobile = true;
			}
			
			sb.listen({
				[sb.moduleId+'-run']:this.run,
				[sb.moduleId+'-assignment-dropped']: this.dropped
			});
			
			sb.notify({
				type:'register-collection-view',
				data:{
					hidden: true,
					icon: 'table',
					id: sb.moduleId,
					hideTimeRangeFilter:true,
					name: 'matrix',
					options: {},
					title: 'Matrix',
					view: View
				}
			});
			
		},
		
		run: function(data) {
			
			data.run(data);
			
		}
		
	}
	
});