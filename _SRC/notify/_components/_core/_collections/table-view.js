Factory.register('table', function(sb){

	function table_ui (container, list, options) {

		var ui = container.makeNode('container', 'div', {css: 'ui stackable grid'});
		var tableUI = ui;
		var metricsDrawn = {};
		var currentSelection = [];
		var defaultFlags = {};
		var mainFields = options.fields;
		
		// nestOption
		var nest = false;

		if (options.subviews !== undefined) {
			
			if(options.subviews.table){
				
				if (
					options.subviews.table.hasOwnProperty('nest')
				) {
					
					nest = options.subviews.table.nest;	
					
				}	
				
			}
			
		}

		function metrics_ui (ui, list, options, group) {

			if (!_.isEmpty(options.metrics)) {

				_.each(options.metrics, function(metric, metricName){

					var row = ui.makeNode('m-'+ metricName, 'div', {tag:'tr'});
					row.makeNode('actions', 'div', {
						tag:'td',
						css:'center aligned',
						text:'<strong>'+ metric.title +'</strong>'
					});

					if (options.actions) {

						row.makeNode('chck', 'div', {tag:'td'});

					}

					if (defaultFlags.hideSelectionBoxes === false) {

						row.makeNode('chck2', 'div', {tag:'td'});

					}
					
					var i = 0;
					_.each(options.fields, function(field, fieldName){
						
						if (field.isHidden) {
							return;
						}

						var metricFunc = metric.fields[fieldName];
						if (typeof metricFunc === 'function') {

							metricFunc(
								row.makeNode('m-'+ fieldName, 'div', {
									tag:'td',
									css:'center aligned',
									text:'<i class="notched circle loading icon"></i>'
								})
								, list
								, group
								, function (group) {

									var isComplete = true;
									
									if (
										group
										&& metricsDrawn
										&& metricsDrawn[group.value]
										&& metricsDrawn[group.value][metricName]
										&& metricsDrawn[group.value][metricName][fieldName]
									) {
										
										metricsDrawn[group.value][metricName][fieldName] = true;
										_.each(metricsDrawn, function (group) {
											_.each(group, function (metric) {
												_.each(metric, function (field) {
	
													if (field !== true) {
														isComplete = false;
													}
	
												});
											});
										});
										
									}

									if (isComplete) {

										options.onDraw();

									}



								}
							);

						} else if (i > 0 || defaultFlags.hideRowActions !== true) {

							row.makeNode('m-'+ fieldName, 'div', {
								tag:'td',
								css:'center aligned',
								text:''
							});

						}
						i++;

					});

				});

			}

		}

		///DEV TEST FOR GROUP BY
		options.groupBy = null;

		function table_layout(ui, list, options, group) {

			function buildMetricsCallbackChecklist (metricsDrawn, group, options) {

				metricsDrawn[group.value] = {};
				_.each(options.metrics, function (metric, name) {

					metricsDrawn[group.value][name] = {};
					_.each(metric.fields, function (field, fieldName) {

						metricsDrawn[group.value][name][fieldName] = false;

					});

				});

			}

			var groupLabel = '';
			var onMobile = false;
			var displayNone = '';
			var tableCSS = '';
			var tableStyle = '';

			defaultFlags = {
				hideSelectionBoxes: 	false
				, hideRowActions:		false
			};

			function check_OptionFlags(options) {

				if(options.subviews !== undefined && !_.isEmpty(options.subviews)) {

					if(options.subviews.hasOwnProperty('table')
						&& !_.isEmpty(options.subviews.table)) {

						_.each(options.subviews.table, function(flagVal, flagName) {

							if(defaultFlags.hasOwnProperty(flagName)) {
								defaultFlags[flagName] = flagVal;
							}

						});

					}

				} else {

					return false;

				}

			}

			check_OptionFlags(options);

			if(options.hasOwnProperty('subviews') && options.subviews !== undefined) {

				if(options.subviews.hasOwnProperty('table')) {

					if(options.subviews.table.hasOwnProperty('css')) {

						tableCSS = options.subviews.table.css;

					}

					if(options.subviews.table.hasOwnProperty('style')) {

						tableStyle = options.subviews.table.style;

					}

				}

			}

			if($(window).width() < 769) {
				onMobile = true;
				displayNone = 'display: none !important;'
			}

			if(group) {

				groupLabel = `<h4 class="ui header">${group}</h4>`;

			}

			var padding = 'padding-bottom:0px;';
			
			var contextWrapper = ui.makeNode('contextWrapper', 'div', {
				css: '',
				style: 'position:relative;'
			})
			
			var tableWrapper = contextWrapper.makeNode('tableWrapper', 'div', {
				css: 'responsive-horizontal'
			})

			var table = tableWrapper.makeNode('table', 'div', {
				css: 'ui ' + tableCSS + ' striped celled compact sortable table',
				tag: 'table',
				style: 	padding +' '+ tableStyle +'width:100%;'
			});

			var headerRow = table.makeNode('thead', 'div', {
				tag:'thead'
			}).makeNode('row', 'div', {tag:'tr', style: displayNone});


			var body = table.makeNode('tbody', 'div', {
				tag:'tbody'
			});

			delete options.actions;

			if ( defaultFlags.hideRowActions === false ) {

				headerRow.makeNode('actions', 'div', {
					tag:'th',
					css:'center aligned collapsing'
				});
			}

			if (defaultFlags.hideSelectionBoxes === false) {

				headerRow.makeNode('select', 'div', {
					tag:'th',
					css:'center aligned collapsing'
				});

				var selectAllBtn = headerRow.select.makeNode('check', 'div', {
					css: 'ui checkbox',
					text: '<input type="checkbox">',
					listener: {
						type: 'checkbox'
					}
				});

				$(document).ready(function() {
					$(selectAllBtn.selector + ' input[type="checkbox"]').on('change', function() {
						if ($(this).is(":checked")) {
							_.each(list, function(item) {
								if (body['row-'+ item.id]) {
									body['row-'+ item.id].selection.checkbox.checkbox('set checked');
								}
							});
						} else {
							_.each(list, function(item) {
								if (body['row-'+ item.id]) {
									body['row-'+ item.id].selection.checkbox.checkbox('set unchecked');
								}
							});
						}
						currentSelection = [];
						$(tableUI.selector + ' table input[type="checkbox"]').each(function() {
							if ( $(this).is(":checked") && !_.isEmpty($(this).attr('data-id')) ) {
								currentSelection.push($(this).attr('data-id'));
							}
						});
						options.updateSelection(currentSelection);
					});
				});

			}

			delete options.fields.tagged_with;

			_.each(options.fields, function(field, name){

				if(field.title !== undefined) {

					var pullIconRight = '';
					var headerText = field.title;

					if (options.entity_type) {
						pullIconRight = '';
					}
					var sortArrow = '<i class="grey sort amount down '+ pullIconRight +' icon"></i>';

					if(field.sort === 'asc'){
						sortArrow = '<i class="teal up arrow '+ pullIconRight +' icon"></i>';
					}else if(field.sort === 'desc'){
						sortArrow = '<i class="teal down arrow '+ pullIconRight +' icon"></i>';
					}
					
					var collapsing = 'collapsing';
					if ( name == 'name' ) {
						collapsing = '';
					}

					headerRow.makeNode(name, 'div', {
						css: ' ' + collapsing,
						tag: 'th'
					});

					if (options.blueprint[name] !== undefined) {

						if (options.entity_type) {

							sb.notify ({
								type: 'view-field'
								, data: {
									type: 		'field'
									, property: 	name
									, obj:		options.entity_type
									, options: 	{
										edit: 		options.editBlueprint
										, title: 	field.title
										, onUpdate: function (updated) {
	
											options.refresh(false, updated);
	
										}
									}
									, ui: 		headerRow[name]
								}
							}) ;
	
							headerRow[name].makeNode(
								'sortBtn',
								'div', {
									text: sortArrow,
									style: 'display:inline-block;'
								}).notify('click', {
									type:'table-view-run',
									data:{
										run:function(fieldName){
	
											var btn = this;
	
											options.toggleSort(fieldName);
	
										}.bind({}, name)
									}
								}, sb.moduleId) ;
	
						} else {
	
							headerRow[name].makeNode(
								'sortBtn',
								'div', {
									text: headerText +' '+ sortArrow,
									style:'white-space: nowrap;'
								}).notify('click', {
									type:'table-view-run',
									data:{
										run:function(fieldName){
	
											var btn = this;
	
											options.toggleSort(fieldName);
	
										}.bind({}, name)
									}
								}, sb.moduleId) ;
	
						}

					} else {

						headerRow.makeNode(name, 'div', {
							css: ' ' + collapsing,
							tag: 'th',
							text: field.title
						});

					}
				} 
			});

			_.each(list, function(item, i){

				var delayCss = 'item-fade-in-'+ Math.floor(Math.random() * Math.floor(3));
				var row = body.makeNode('row-'+ item.id, 'div', { tag:'tr'});

				if ( defaultFlags.hideRowActions === false){

					var rowAction = row.makeNode('actions', 'div', {
						tag:'td',
						css:'center aligned collapsing'
					});

					options.actions_ui(rowAction, item);

				}


				if(defaultFlags.hideSelectionBoxes === false) {

					row.makeNode('selection', 'div', {
						tag:'td', 
						css:'center aligned collapsing'
					});

					var rowCheckbox = row.selection.makeNode('checkbox', 'div', {
						css: 'ui checkbox',
						text:'<input type="checkbox" data-id="' + item.id + '"><label></label>',
						listener:{
							type:'checkbox',
						}
					});

					$(document).ready(function() {
						$(rowCheckbox.selector + ' input[type="checkbox"]').on('change', function() {
							currentSelection = [];
							$(tableUI.selector + ' table input[type="checkbox"]').each(function() {
								if ( $(this).is(":checked") && !_.isEmpty($(this).attr('data-id')) ) {
									currentSelection.push($(this).attr('data-id'));
								}
							});
							options.updateSelection(currentSelection);
						});
					});

				}

				_.each(options.fields, function(field, name) {

					if(field.title !== undefined) {

						if(field.view){
							
							var titleCellCSS = '';
							var collapsing = 'collapsing';
							var titleSetup = {};

							if (
								appConfig.instance !== 'infinity'
								&& appConfig.instance !== 'nlp'
								&& appConfig.instance !== 'nashvilleeventbartending'
								&& appConfig.instance !== 'dreamcatering'
							) {

								titleSetup.onClick = function(obj){

									if ( options.singleView && typeof options.singleView == 'function') {
										options.singleView(obj);
									}

								};

							}
							if ( name == 'name' || field.isPrimary ) {
								titleCellCSS = 'table-view-title-cell';
								collapsing = '';
							}

							if (field.view(
								
								row.makeNode(name, 'div', {
									css: titleCellCSS + ' ' + collapsing,
									tag: 'td'
								})
								, item
                                , titleSetup

							) === false && onMobile === true) {

								delete row[name];

							}

						} else{

							switch(field.type){
								
								case 'contacts':
									
									var contactLink = '';
									var contactName = '';
									var linkText = '<i>No contact selected</i>';
									var linkStyle = '';
									
									if(item[name]){
										
										contactLink = sb.data.url.createPageURL('object', {
											id: 					item[name].id
											, name: 				item[name].name
											, object_bp_type: 	'contacts'
											, type: 				'contacts'
										});
										
										contactName = item[name].name;
										
										linkText = '<a href="'+ contactLink +'">'+ contactName +' <i class="external link fitted icon"></i></a>';
										
										linkStyle = 'text-decoration:underline';
										
									}
									
									row.makeNode(name, 'div', {
										tag:'td',
										text:linkText,
										style:linkStyle
									});
								
									break;
																
								default:
								
									row.makeNode(name, 'div', {
										tag:'td',
										text:item[name]
									});
								
							}

						}

					}

				});

			});

			// Build checklist of metrics per group
			if (group) {

				buildMetricsCallbackChecklist(metricsDrawn, group, options);

			} else {

				buildMetricsCallbackChecklist(metricsDrawn, {value:'table'}, options);

			}

			metrics_ui (body, list, options, group);

		}

		function build_placeholder(ui) {

			var field_amount = _.size(options.fields) + 2;

			ui.makeNode('grid1', 'div', {
				css: 'ui equal width grid'
			});
			ui.makeNode('grid2', 'div', {
				css: 'ui equal width grid'
			});
			ui.makeNode('grid3', 'div', {
				css: 'ui equal width grid'
			});
			ui.makeNode('grid4', 'div', {
				css: 'ui equal width grid'
			});

			ui.grid1.makeNode('row', 'div', {
				css: field_amount + ' column row'
			});
			ui.grid2.makeNode('row', 'div', {
				css: field_amount + ' column row'
			});
			ui.grid3.makeNode('row', 'div', {
				css: field_amount + ' column row'
			});
			ui.grid4.makeNode('row', 'div', {
				css: field_amount + ' column row'
			});

			for(var i = 1; i <= field_amount; i++) {

				// Grid 1
				ui.grid1.row.makeNode('col' + i, 'div', {
					css: 'column ui fluid placeholder'
				});

				ui.grid1.row['col'+i].makeNode('header', 'div', {
					css: 'header'
				});

				ui.grid1.row['col'+i].header.makeNode('p1', 'div', {
					css: 'line'
				});

				// Grid 2
				ui.grid2.row.makeNode('col' + i, 'div', {
					css: 'column ui fluid placeholder'
				});

				if(i === 1 || i === 2) {

					ui.grid2.row['col'+i].makeNode('para', 'div', {
						css: 'image header'
					});

					ui.grid2.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});

				} else {

					ui.grid2.row['col'+i].makeNode('para', 'div', {
						css: 'header'
					});

					ui.grid2.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					ui.grid2.row['col'+i].para.makeNode('p2', 'div', {
						css: 'line'
					});

				}

				// Grid 3
				ui.grid3.row.makeNode('col' + i, 'div', {
					css: 'column ui fluid placeholder'
				});

				if(i === 1 || i === 2) {

					ui.grid3.row['col'+i].makeNode('para', 'div', {
						css: 'image header'
					});

					ui.grid3.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});

				} else {

					ui.grid3.row['col'+i].makeNode('para', 'div', {
						css: 'header'
					});

					ui.grid3.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					ui.grid3.row['col'+i].para.makeNode('p2', 'div', {
						css: 'line'
					});

				}

				// Grid 4
				ui.grid4.row.makeNode('col' + i, 'div', {
					css: 'column ui fluid placeholder'
				});

				if(i === 1 || i === 2) {

					ui.grid4.row['col'+i].makeNode('para', 'div', {
						css: 'image header'
					});

					ui.grid4.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});

				} else {

					ui.grid4.row['col'+i].makeNode('para', 'div', {
						css: 'header'
					});

					ui.grid4.row['col'+i].para.makeNode('p1', 'div', {
						css: 'line'
					});
					ui.grid4.row['col'+i].para.makeNode('p2', 'div', {
						css: 'line'
					});

				}

			}

		}

		// !TESTING HERE
		/*
ui.makeNode('tst', 'div', {
			text:'test'
			, tag: 'button'
		}).notify('click', {
			type: 'table-view-run'
			, data: {
				run:function () {
					console.log('clicked!', options);
					options.setOptions({
						fields: {
							name: {
								type: 'title'
								, title: 'NAME'
							}
						}
						, objectType: 'companies'
						, where: {
							tagged_with: parseInt(sb.data.cookie.userId)
						}
					});

				}
			}
		}, sb.moduleId);
*/

		if(options.isLoading === true) { // Pre data state

			build_placeholder(container);

		} else {

			delete ui.seg;

			if ( _.isEmpty(list) ) {

				ui.makeNode('msg', 'div', {
					text:'<br />'+ options.emptyMessage,
					css:'text-center',
					tag:'h5'
				});

			} else {

				if (options.isGrouped) {

					var style = options.style || '';
					
					ui.makeNode('col', 'div', {
						css: 'ui sixteen wide column'
						, style: style
					});

					if (!_.isEmpty(options.metrics)) {

						list.push({
							value: 		'sum1'
							, title: 	'OVERALL'
							, data: 		[]
						});

					}

					var i = 0;
					
					_.each(list, function (group, name) {

						// Create the container
						ui.col.makeNode('g-'+ name, 'div', {});

						// Create the divider (if needed)
						if (i > 0) {
							ui.col['g-'+ name].makeNode('divider', 'div', {
								text: '<div class="ui horizontal divider"></div>'
							});
						}

						var groupTitle = group.title;
						var groupColor = '';
						
						if(!isNaN(groupTitle) && options.types){
							groupTitle = options.types[name].name;
							
							if(!groupTitle){
								groupTitle = 'Uncategorized';
							}else{
								if(options.types[name].color){
									groupColor = 'ui '+ options.types[name].color +' text';
								}
							}
						}

						// Create the title
						ui.col['g-'+ name].makeNode('title', 'div', {
								tag: 'h3',
								css: groupColor,
								style: 'cursor:pointer; margin-bottom:10px;',
								text: '<i class="caret down icon collapseIcon'+ name +'" style="margin: 6px 10px 0px 2px;"></i>'+ groupTitle
							}).notify('click', {
								type: ['table-view-run'],
								data: {
									run: function(data) {
										
										if (ui.col['g-'+ name].hasOwnProperty('isCollapsed')) {
											
											if(ui.col['g-'+ name].isCollapsed === true) {
												
												ui.col['g-'+ name].isCollapsed = false;
												
												$('.collapseIcon'+name).removeClass('caret right');
												$('.collapseIcon'+name).addClass('caret down');
												
											} else {
												
												ui.col['g-'+ name].isCollapsed = true;
												
												$('.collapseIcon'+name).removeClass('caret down');
												$('.collapseIcon'+name).addClass('caret right');
												
											}
											
										} else {
											
											ui.col['g-'+ name].isCollapsed = true;
											
											$('.collapseIcon'+name).removeClass('caret down');
											$('.collapseIcon'+name).addClass('caret right');
											
										}
										
										if (group.data.length > 0 || group.value === 'sum1') {
											$(ui.col['g-'+ name].container.selector).toggleClass('hide');
										}
										
									}
								}
							}, sb.moduleId);

						if (group.data.length > 0 || group.value === 'sum1') {

							table_layout(
								ui.col['g-'+ name].makeNode('container', 'div', {}),
								group.data,
								options,
								group,
							);

						}
						
						// Increment counter
						i++;

					});

				} else if ( options.groupBy ){

					var groups = _.groupBy(list, function(item){

						var ret = item[options.groupBy];

						if( typeof ret === 'object' && _.has(ret, 'name')){
							return ret.name;
						}

						return ret;

					});

					_.each(groups, function(list, name){

						var tableUI = this.makeNode('cont-'+ name, 'div', {});

						table_layout(tableUI, list, options, name);

						tableUI.makeNode('sp', 'lineBreak', {});

					}, ui);


				} else if (nest !== false) {
					
					var style = options.style || '';
					var nestedProperty = nest.property;
					var dimentionalArray = [];
					
					// Switches
					var nestOn = false;
					var nestAt = 0;
					
					// Build multi dimentional array
					_.each(list, function(o, i) {
						
						if (o.hasOwnProperty(nestedProperty)) {
							
							nestOn = true;
							
							dimentionalArray[i] = o[nestedProperty];
							
						} else {
							
							if (nestOn) {
								
								nestAt = i;
								
								nestOn = false;
								
							}
							
							if (dimentionalArray[nestAt] === undefined) {
									
								dimentionalArray[nestAt] = [];
								
								dimentionalArray[nestAt].push(o);	
								
							} else {
								
								dimentionalArray[nestAt].push(o); 		
								
							}
							
						}
						
					});
					
					ui.makeNode('col', 'div', {
						css: 'ui sixteen wide column'
						, style: style
					});
					
					dimentionalArray = _.filter(dimentionalArray, function(arr) {
						
						return arr !== undefined;
						
					});
					
					_.each(dimentionalArray, function(anArray, i) {

						// Determine if you are dealing with main data or nested data
						
						var sampleObj = anArray[0];

						if (sampleObj.object_bp_type === nest.mainObjectType) {
							
							table_layout(
								ui.makeNode(
									'seg-'+i
									, 'div'
									, {
										style:'width:100% !important;'
									}
								)
								, anArray
								, options
								, null
							);
							
						} else {
							
							var groupObj = anArray[0].groupObj;

							ui.makeNode(
								'seg-'+i
								, 'div'
								, {
									style:'width:100% !important; margin: 10px 0;'
								}
							);
							
							ui['seg-'+i].makeNode('grid', 'div', {});
							
							ui['seg-'+i].makeNode('open', 'div', {
								css: 'text-center'
								, style: 'background-color: #ebebeb;'
							});
							
							// Create the title
							ui['seg-'+i].open.makeNode('title', 'div', {
									tag: 'h2',
									style: 'margin-bottom:10px; cursor: pointer;',
									text: '<i class="caret right icon collapseIcon'+ i +'" style="margin: 6px 10px 0px 2px;"></i>'
								}).notify('click', {
									type: ['table-view-run'],
									data: {
										run: function(data) {
											
											if (ui['seg-'+i].hasOwnProperty('isCollapsed')) {
												
												if(ui['seg-'+i].isCollapsed === true) {
													
													ui['seg-'+i].isCollapsed = false;
													
													$('.collapseIcon'+i).removeClass('caret right');
													$('.collapseIcon'+i).addClass('caret down');
													
												} else {
													
													ui['seg-'+i].isCollapsed = true;
													
													$('.collapseIcon'+i).removeClass('caret down');
													$('.collapseIcon'+i).addClass('caret right');
													
												}
												
											} else {
												
												ui['seg-'+i].isCollapsed = false;
												
												$('.collapseIcon'+i).removeClass('caret right');
												$('.collapseIcon'+i).addClass('caret down');
												
											}
											
											$(ui['seg-'+i].container.selector).toggleClass('hide');
											
										}
									}
								}, sb.moduleId);
								
							ui['seg-'+i].makeNode('container', 'div', {
								css: 'hide'
							});
							
							table_layout(
								ui['seg-'+i].grid
								, [groupObj]
								, options
								, null
							);
							
							options.fields = nest.fields;
							
							table_layout(
								ui['seg-'+i].container
								, anArray
								, options
								, null
							);

							options.fields = mainFields;
							
						}
						
					});					
					
				} else {

					table_layout(
						ui.makeNode(
							'seg'
							, 'div'
							, {
								style:'width:100% !important;'
							}
						)
						, list
						, options
						, null
					);

				}

			}

			options.create_ui(
				ui.makeNode('create', 'div', {css:'text-center'})
			);

		}

		if (_.isEmpty(options.metrics)) {
			options.onDraw();
		}

	}

	return {

		init:function(){

			sb.notify({
				type:'register-collection-view',
				data:{
					canGroup: true,
					default:true,
					icon:'list',
					id:sb.moduleId,
					name:'table',
					options:{},
					title:'Table',
					view:table_ui
				}
			});

			sb.listen({
				'table-view-run':this.run
			});

		},

		load:function(){},

		destroy:function(){},

		run:function(data){

			data.run();

		}

	}

});
