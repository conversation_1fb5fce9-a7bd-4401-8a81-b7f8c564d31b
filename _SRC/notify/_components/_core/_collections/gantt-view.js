Factory.register("gantt-view", function (sb) {
  function view(ui, list, options) {
    ui.makeNode("chart", "div", { tag: "svg", id: "chartDiv" });

    ui.makeNode("single", "modal", {});

    ui.makeNode("buttons", "div", {
      css: "ui buttons",
      style: "padding: 15px 0;"
    });

    ui.patch();

    if (
      (options.filterSelections.startDate == "" &&
        options.filterSelections.startDate == "") ||
      (!options.filterSelections.startDate && !options.filterSelections.endDate)
    ) {
      ui.empty();

      ui.makeNode("alert", "div", { css: "ui basic padded segment" }).makeNode(
        "text",
        "div",
        {
          css: "ui huge text",
          text:
            "No date fields detected. Please add date field to view on Gantt chart."
        }
      );

      return;
    }

    var tasks = [];
    var stateFields = false;
    var totalStages = 0;
    var progressKey;
    var estTimerKey;

    _.each(options.blueprint, function (field, key) {
      if (stateFields === false) {
        stateFields = {};
      }
      if (field.fieldType == "state") {
        stateFields[key] = field;
      }
      if (field.fieldType === "quantity") {
        if (field.options.is_progress) {
          progressKey = key;
        }
      }
      if (field.fieldType === "timer") {
        if (field.options.showEstimatedTime) {
          estTimerKey = key
        }
      }
    });

    if (stateFields) {
      _.each(stateFields, function (field) {
        if (field.workflow.states) {
          totalStages += _.filter(field.workflow.states, function (state) {
            if (state.type !== "onHold") {
              return state;
            }
          }).length;
        }
      });
    }

    // sort by start date
    var sortedList = _.sortBy(list, options.filterSelections.startDate);

    _.each(sortedList, function (obj, count) {
      var dependencies = [];
      if (options.filterSelections.dependencies == "on") {
        if (count > 0) {
          dependencies.push(sortedList[count - 1].id.toString());
        }
      }

      var startDate = "";
      var endDate = "";
      var startDateOptions =
        options.blueprint[options.filterSelections.startDate].options;
      var endDateOptions =
        options.blueprint[options.filterSelections.endDate].options;
      var customClass = "gantt-chart-grey";
      var icon = "";

      function addWeekdays(date, days) {
        date = moment(date); // use a clone
        while (days > 0) {
          date = date.add(1, 'days');
          // decrease "days" only if it's a weekday.
          if (date.isoWeekday() !== 6 && date.isoWeekday() !== 7) {
            days -= 1;
          }
        }
        return date;
      }

      function subtractWeekdays(date, days) {
        date = moment(date); // use a clone
        while (days > 0) {
          date = date.subtract(1, 'days');
          // increase "days" only if it's a weekday.
          if (date.isoWeekday() !== 6 && date.isoWeekday() !== 7) {
            days += 1;
          }
        }
        return date;
      }

      if (
        options.filterSelections.startDate == options.filterSelections.endDate
      ) {
        // if there is only one date and a timer field with an estimated time
        // make the end date the start date + estimated time 
        // considering 8 hours in a work day and no weekends
        if (obj[`${estTimerKey}_est`]) {
          startDate = obj[options.filterSelections.startDate] ? moment(obj[options.filterSelections.startDate]) : moment();
          var days = obj[`${estTimerKey}_est`] / 28800;
          endDate = addWeekdays(startDate, days)
        } else {

          // if there is only one date it is used as the end date
          // and the start date will be one day before
          if (obj[options.filterSelections.startDate]) {
            endDate = obj[options.filterSelections.startDate] ? moment(obj[options.filterSelections.startDate]) : moment();
            startDate = subtractWeekdays(endDate, 1);
          }
        }
      }

      if (
        options.filterSelections.startDate !== options.filterSelections.endDate
      ) {
        startDate = moment(obj[options.filterSelections.startDate]);
        endDate = moment(obj[options.filterSelections.endDate]);
      }

      // This looks like a bandaid to a larger problem:
      // when we create a new item to go on to chart the date is not immediately available
      // so the start and end dates are invalid which would break the chart
      // doing this prevents us from breaking the chart but then the incorrect dates are displayed
      if (!startDate || !startDate.isValid()) {
        startDate = moment();
      }
      if (!endDate || !endDate.isValid()) {
        endDate = addWeekdays(startDate, 1)
      }

      // if the end date is before the start date
      if (startDate.diff(endDate) > 0) {
        endDate = addWeekdays(startDate, 1);
      }

      if (startDate > moment()) {
        var dueDateUnix = parseInt(
          (
            new Date(obj[options.filterSelections.startDate]).getTime() / 1000
          ).toFixed(0)
        );
        if (+startDateOptions.yellow > 0) {
          Date.prototype.addDays = function (days) {
            var date = new Date(this.valueOf());
            date.setDate(date.getDate() + days);
            return date;
          };

          var triggerDate = new Date().addDays(+startDateOptions.yellow);

          currentDateUnix = parseInt((triggerDate.getTime() / 1000).toFixed(0));

          if (dueDateUnix < currentDateUnix) {
            customClass = "gantt-chart-yellow";
            icon = '<i class="exclamation triangle icon"></i>';
          }
        }

        if (+startDateOptions.red > 0) {
          var triggerDate = new Date().addDays(+startDateOptions.red);

          currentDateUnix = parseInt((triggerDate.getTime() / 1000).toFixed(0));

          if (dueDateUnix < currentDateUnix) {
            customClass = "gantt-chart-red";
            icon = '<i class="exclamation triangle icon"></i>';
          }
        }
      } else {
        var dueDateUnix = parseInt(
          (
            new Date(obj[options.filterSelections.endDate]).getTime() / 1000
          ).toFixed(0)
        );

        if (+endDateOptions.yellow > 0) {
          Date.prototype.addDays = function (days) {
            var date = new Date(this.valueOf());
            date.setDate(date.getDate() + days);
            return date;
          };

          var triggerDate = new Date().addDays(+endDateOptions.yellow);

          currentDateUnix = parseInt((triggerDate.getTime() / 1000).toFixed(0));

          if (dueDateUnix < currentDateUnix) {
            customClass = "gantt-chart-yellow";
            icon = '<i class="exclamation triangle icon"></i>';
          }
        }

        if (+endDateOptions.red > 0) {
          var triggerDate = new Date().addDays(+endDateOptions.red);

          currentDateUnix = parseInt((triggerDate.getTime() / 1000).toFixed(0));

          if (dueDateUnix < currentDateUnix) {
            customClass = "gantt-chart-red";
            icon = '<i class="exclamation triangle icon"></i>';
          }
        }
      }

      var progress = 0;
      var innerProgress = 0;
      var state;
      var calculatedProgress = 0;

      if (totalStages > 0) {
        _.each(stateFields, function (field, key) {
          var flowState = _.findWhere(field.workflow.states, {
            isEntryPoint: 1
          });
          var stopCounting = false;

          while (flowState !== undefined) {
            if (!stopCounting) {
              innerProgress++;
            }

            if (flowState.id == obj[key]) {
              state = flowState;
              stopCounting = true;
            }

            flowState = _.findWhere(field.workflow.states, {
              uid: parseInt(flowState.next[0])
            });
          }

          if (!state) {
            state = _.findWhere(field.workflow.states, {
              isEntryPoint: 1
            });
          }

          if (!stopCounting) {
            innerProgress = 1;
          }

          progress += innerProgress;
        });
      }

      if (progressKey) {
        calculatedProgress = obj[progressKey];
      } else {
        calculatedProgress = Math.round((progress / totalStages) * 100);
      }

      if (calculatedProgress == 100) {
        customClass = "gantt-chart-green";
        icon = '<i class="check icon"></i>';
      }

      tasks.push({
        id: obj.id.toString(),
        name: obj.name,
        start: startDate.format(),
        end: endDate.format(),
        progress: calculatedProgress,
        object_bp_type: obj.object_bp_type,
        custom_class: customClass,
        dependencies: dependencies,
        icon,
        state
      });
    });

    var gantt;

    setTimeout(function () {
      gantt = new Gantt("#chartDiv", tasks, {
        view_modes: ["Quarter Day", "Half Day", "Day", "Week", "Month"],
        custom_popup_html: function (task) {
          var start = moment(task.start).format("MMM D");
          var end = moment(task.end).format("MMM D");

          var retString = `<div style="padding:10px;" class="popup-wrapper" id="i-${task.id}">`;
          retString += `<nobr><h4 style="cursor: pointer;">${task.icon} ${task.name}</h4></nobr><br />`;
          retString += `<nobr><p>${options.blueprint[options.filterSelections.startDate].name}: ${start}</p></nobr>`;

          if (
            options.filterSelections.startDate !=
            options.filterSelections.endDate
          ) {
            retString += `<nobr><p>${options.blueprint[options.filterSelections.endDate].name}: ${end}</p></nobr>`;
          }

          if (
            task.progress &&
            ((!progressKey &&
              (task.state ? task.state.type !== "onHold" : true)) ||
              progressKey)
          ) {
            retString += `<nobr><p>Calculated Progress: ${task.progress}%</p></nobr>`;
          }
          if (task.state) {
            retString += `<nobr><p>${task.state.name}</p></nobr>`;
          }

          retString += "</div>";

          return retString;
        },
        on_date_change: function (task, start, end) {
          // if the task only has one date they can't change the dates more than one day away
          if (
            options.filterSelections.startDate ==
            options.filterSelections.endDate
          ) {
            if (
              moment(start).diff(moment(end), "days") > 1 ||
              moment(start).diff(moment(end), "days") < 0
            ) {
              // can't update it
              return;
            }
          }

          var updateObj = {
            id: +task.id
          };

          updateObj[options.filterSelections.startDate] = moment(
            start
          ).format();

          if (
            options.filterSelections.startDate !=
            options.filterSelections.endDate
          ) {
            updateObj[options.filterSelections.endDate] = moment(end).format();
          }

          sb.data.db.obj.update(task.object_bp_type, updateObj, function (done) {
            // TODO: update the task on the chart when it is clicked to be viewed
          });
        },
        on_progress_change: function (task, progress) {
          // can only update if there is a progress number property
          if (progressKey) {
            var updateObj = {
              id: +task.id
            };
            updateObj[progressKey] = progress;
            sb.data.db.obj.update(task.object_bp_type, updateObj, function (
              done
            ) {
              // TODO: update the task on the chart when it is clicked to be viewed
            });
          }
        }
      });
    }, 500);

    ui.buttons
      .makeNode("quarter-day-button", "button", {
        css: "ui button",
        text: "Quarter Day"
      })
      .notify("click", {
        type: "button",
        data: {
          run: function () {
            gantt.change_view_mode("Quarter Day");
          }
        }
      });

    ui.buttons
      .makeNode("half-day-button", "button", {
        css: "ui button",
        text: "Half Day"
      })
      .notify("click", {
        type: "button",
        data: {
          run: function () {
            gantt.change_view_mode("Half Day");
          }
        }
      });

    ui.buttons
      .makeNode("day-button", "button", {
        css: "ui button",
        text: "Day"
      })
      .notify("click", {
        type: "button",
        data: {
          run: function () {
            gantt.change_view_mode("Day");
          }
        }
      });

    ui.buttons
      .makeNode("week-button", "button", {
        css: "ui button",
        text: "Week"
      })
      .notify("click", {
        type: "button",
        data: {
          run: function () {
            gantt.change_view_mode("Week");
          }
        }
      });

    ui.buttons
      .makeNode("month-button", "button", {
        css: "ui button",
        text: "Month"
      })
      .notify("click", {
        type: "button",
        data: {
          run: function () {
            gantt.change_view_mode("Month");
          }
        }
      });

    _.each(tasks, function (task) {
      $(document).on("click", `#i-${task.id}`, function () {
        var item = _.findWhere(list, {
          id: +task.id
        });
        options.singleView(item);
      });
    });
  }

  return {
    init: function () {
      sb.notify({
        type: "register-collection-view",
        data: {
          icon: "chart area",
          id: sb.moduleId,
          name: "gantt",
          options: {},
          title: "Gantt Chart (BETA)",
          view: view
        }
      });
      sb.listen({
        button: this.run
      });
    },

    run: function (data) {
      data.run();
    },

    load: function () { },

    destroy: function () { }
  };
});
