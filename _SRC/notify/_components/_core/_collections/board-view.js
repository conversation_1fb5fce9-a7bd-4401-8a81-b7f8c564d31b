Factory.register('board-view', function(sb){

	function board_ui(container, groups, options) {

		var ui = container.makeNode('container', 'div', {
			css:'ui basic segment boardContainer',
			style: 'background-color: transparent !important; border-radius:.2rem !important;'
		});
		var Data = [];
		var groupedFields = _.groupBy(options.fields, 'type');
		var columnDoms = [];
		
		function build_placeholder(ui) {

			ui.makeNode('grid', 'div', {
				css: 'ui equal width grid'
			});

			_.each(groups, function(group, i) {

				ui.grid.makeNode('col-'+ i, 'div', {
					css: 'column'
				});

				ui.grid['col-'+i].makeNode('head', 'div', {
					css: 'ui fluid placeholder"'
				});
				ui.grid['col-'+i].makeNode('lb1', 'lineBreak', {
					spaces: 1
				});
				ui.grid['col-'+i].makeNode('body', 'div', {});

				ui.grid['col-'+i].head.makeNode('header', 'div', {
					css: 'header'
				});

				ui.grid['col-'+i].head.header.makeNode('line1', 'div', {
					css: 'line',
					style: 'background-color: transparent !important;'
				});
				ui.grid['col-'+i].head.header.makeNode('line2', 'div', {
					css: 'line',
					style: 'background-color: transparent !important;'
				});

				ui.grid['col-'+i].body.makeNode('plH', 'div', {
					css: 'ui placeholder'
				});

				ui.grid['col-'+i].body.plH.makeNode('img', 'div', {
					css: 'image',
					style: 'height: 500px !important;'
				});

			});

		}

		function build_collapsedColumn(ui, title, color, set, i, groupLength, parentUI) {

			if(color === null || color === undefined) {
				color = 'grey';
			}

			ui.empty();

			ui.makeNode('colTitle', 'div', {
				tag: 'h4'
				, text: '<strong>' + title + ' ('+ groupLength +')</strong>'
				, css: 'ui ' + color + ' header'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data: {
						run: function(data) {

							$(ui.selector).removeClass('ui '+ color +' segment show-pointer');
							$(ui.selector).css('min-width', '300px');
							$(ui.selector).css('margin', '0px');
							$(ui.selector).css('writing-mode', 'unset');
							$(ui.selector).css('text-orientation', 'unset');
							$(ui.selector).css('padding-top', '5px');

							build_expandedColumn(ui, set, i, parentUI);
							
							options.config.subviews.board.groupBy.groups[set.value.toString()].collapsed = false;
							
							sb.notify({
								type: 'update-browser-cache',
								data: {
									id: options.config.colUID
									, callback: function(res, callback) {

										res.subviews = options.config.subviews;
										
										callback(res);
										
									}
								}
							}, sb.moduleId);

						}
					}
				}, sb.moduleId);

			ui.patch();

			$(ui.selector).addClass('ui '+ color +' segment show-pointer');

			$(ui.selector).css('min-width', '50px');
			$(ui.selector).css('margin', '5px 5px');
			$(ui.selector).css('writing-mode', 'vertical-lr');
			$(ui.selector).css('text-orientation', 'sideways-right');
			$(ui.selector).css('padding-top', '15px');

		}

		function build_expandedColumn(ui, set, i, parentUI) {

			var columnColor = set.color;
			var columnTitle = set.title;
			var groupValue = set.value;
			var group = set.data;
			var groupLength = 0;
			var stateObj = _.findWhere(options.groupBy.options, {value: set.value});
			var isDoneFlag = ( !_.isEmpty(stateObj) && stateObj.isDone == true ) ? '<span><i class="green check circle icon"></i></span>' : '';

			ui.empty();

			ui.makeNode('column', 'div', {
				style: 'padding:6px !important; margin:0px 6px !important; background-color:rgb(245, 245, 247); border-radius:6px;'
			});

			ui.column.makeNode('titleArea', 'div', {

			});

			ui.column.titleArea.makeNode('title', 'div', {
				tag:'h4',
				text: columnTitle +' <small>('+ groupLength +')</small> ' + isDoneFlag,
				css:'ui '+ columnColor +' left floated header',
				style:'margin:4px 5px; padding-bottom:5px;'
			});

			if (Array.isArray(group)) {
				groupLength = group.length;
			}

			if (options.batch_actions_ui) {

				options.batch_actions_ui(
					ui.column.titleArea
					, _.pluck(group, 'id')
					, undefined
					, groupValue
					, undefined
					, {
						openDir: 'left',
						hideNewBtn: 1
					}
				);

			}

			ui.column.titleArea.makeNode('toggle', 'div', {
				text: '<i class="ui compress icon"></i>',
				css: 'ui basic white right floated circular icon button',
				style: 'box-shadow: none; padding:8px 5px !important;'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data: {
					run: function(data) {

						build_collapsedColumn(ui, columnTitle, columnColor, set, i, groupLength, parentUI);
						
						if(
							options.config.subviews.board.hasOwnProperty('groupBy')
							&& options.config.subviews.board.groupBy.hasOwnProperty('groups')
						) {
							
							if(options.config.subviews.board.groupBy.groups.hasOwnProperty(set.value.toString())) {
								
								options.config.subviews.board.groupBy.groups[set.value.toString()].id = set.value;
								options.config.subviews.board.groupBy.groups[set.value.toString()].collapsed = true;	
								
							} else {
								
								options.config.subviews.board.groupBy.groups[set.value.toString()] = {
									id: set.value
									, collapsed: true
								};
								
							}
							
						} else {
							
							if (
								options.config.subviews.board.hasOwnProperty('groupBy')
							) {
								options.config.subviews.board.groupBy.groups = {};
							} else {
								options.config.subviews.board.groupBy = {
									groups: {}
								};
							}
							
							options.config.subviews.board.groupBy.groups[set.value.toString()] = {
								id: set.value,
								collapsed: true
							};
							
						}

						sb.notify({
							type: 'update-browser-cache',
							data: {
								id: options.config.colUID
								, callback: function(res, callback) {

									res.subviews = options.config.subviews;
									
									callback(res);
									
								}
							}
						}, sb.moduleId);

					}
				}
			}, sb.moduleId);

			ui.column.makeNode('clear', 'div', {
				style: 'clear:both'
			});

			ui.column.makeNode('drop', 'div', {
				style: 'min-height:10px;',
				drag: {
	    			data: {
	        			group: groupValue,
	        			update: options.updateGroup
	    			},
	    			accepts: true,
	    			moves: false
				}
			});


            ui.column.makeNode('cfcont', 'div', {});

			if (
				set.hasOwnProperty('data')
				&& set.data.length > 0
				&& group[0].object_bp_type === 'groups'
				&& group[0].group_type === 'Project'
			) {

				displayInvoiceTotals = true;

			}

			_.each(group, function(item, j){

				var priorityProp = '';
				if (groupedFields.priority) {
					priorityProp = groupedFields.priority[0].name;
				}
				var groupColor = '';

				if (
					item.object_bp_type === 'groups'
					&& item.group_type === 'Task'
				) {
					priorityProp = 'priority';
				}

				if (!_.isEmpty(priorityProp)) {

					switch (item[priorityProp]) {

						case 0:
							groupColor = 'green';
							break;

						case 1:
							groupColor = 'olive';
							break;

						case 3:
							groupColor = 'orange';
							break;

						case 4:
							groupColor = 'red';
							break;

						case 2:
							groupColor = 'yellow';
							break;

						default:
							groupColor = 'green';
							break;

					}

				}

				ui.column.drop.makeNode('i-'+ j, 'div', {
					css: 'ui link fluid '+ groupColor +' card segment revealParent ',
					style: 'padding:2px !important; margin:0px 0px 10px 0px !important;',
					drag: {
    					moves: true,
    					data: {
        					obj: item,
        					column: ui.column.drop,
        					cardIndex: 'i-'+ j,
							run: function (data) {
									
								if (options.onSort) {

									options.onSort(data);

								} else {

									var before = '';
									var after = '';

									if (data.next && data.next.obj && data.next.obj.id) {
										before = data.next.obj.id;
									}

									if (data.prev && data.prev.obj && data.prev.obj.id) {
										after = data.prev.obj.id;
									}

									data.dropped.column[data.dropped.cardIndex].loading();

									data.in.update({
										objectId: data.dropped.obj.id,
										sort: {
											before: before,
											after: after
										},
										state: {
											stateProperty: options.groupBy.field,
											newState: data.in.group,
											link: sb.data.url.createPageURL(
												'object',
												data.dropped.obj
											)
										}
									}, function () {

										data.dropped.column[data.dropped.cardIndex].loading(false);

									});

								}
								
							}
    					},
    					drop: 'board-view-card-dropped',
    					accepts: false,
    					copy: false
					}
				});

				ui.column.drop['i-'+ j].makeNode('grid', 'div', {
					css: 'ui grid'
				});

				ui.column.drop['i-'+ j].grid.makeNode('row1', 'div', {
					css: 'sixteen wide column',
					style: 'padding-top:0; padding-bottom:0; margin-left:0; margin-right:5px; height:auto;'
				});

				ui.column.drop['i-'+ j].grid.makeNode('row2', 'div', {
					css: 'sixteen wide column',
					style: 'padding-top:0; padding-bottom:0; height:auto;'
				});

				ui.column.drop['i-'+ j].grid.makeNode('row3', 'div', {
					css: 'sixteen wide column',
					style: 'padding-top:0; padding-bottom:0; height:auto;'
				});

				ui.column.drop['i-'+ j].grid.makeNode('row4', 'div', {
					css: 'sixteen wide column',
					style: 'padding-top:0; padding-bottom:0; height:auto;'
				});

				ui.column.drop['i-'+ j].grid.makeNode('row5', 'div', {
					css: 'sixteen wide column',
					style: 'padding-top:2px; padding-bottom:0; padding-left:0; padding-right:0; margin-left:15px; margin-right:15px; margin-top:0px; border-top:1px dashed #ebebeb; height:auto;'
				});

				if (options.actions) {

					options.actions_ui(
						ui.column.drop['i-'+ j].grid.row5.makeNode('actions', 'div', {
							css: 'pull-right',
							style: 'padding:4.5px 0px 4px 6px'
						}),
						item,
						{
							css: 'right floated buttons',
							menuCss: 'left menu'
						}
					);

				}

				$(document).on('click', ui.column.drop['i-'+ j].selector, function (e) {
					options.singleView(item);
				});

				$(document).on('click', ui.column.drop['i-'+ j].grid.row5.actions.selector, function (e) {
					e.stopPropagation();
				});

				var fields = _.sortBy(options.fields, function(field) {
					if (field.type == 'image') {
						field.sort = 0;
					} else if (field.type == 'user' || field.type == 'users') {
						field.sort = 1;
					} else if (field.type == 'date') {
						field.sort = 2;
					} else if (field.type == 'timer') {
						field.sort = 3;
					} else if (field.type == 'title') {
						field.sort = 4;
					} else {
						field.sort = 5;
					}
					return field;
				});

				fields = _.sortBy(fields, 'sort');

				var imageAlreadyDisplayed = false;
				var dateAlreadyDisplayed = false;
				var timerAlreadyDisplayed = false;
				var usersAlreadyDisplayed = false;

				_.each(fields, function(field) {

					if (field.type === 'image') {
						
						if (field.view) {

							if (!_.isEmpty(item[field.name])) {

								if (options.blueprint[field.name].hasOwnProperty('options') && options.blueprint[field.name].options.isCover) {

									imageAlreadyDisplayed = true;

									field.view(

										ui.column.drop['i-'+ j].grid.row1.makeNode(field.name, 'div', {
											style:'padding:0px; border-right:1px dashed #ebebeb;'
										})
										, item
										, {
											size: 'large'
										}

									);

								}

							}

						}

					}

					if (field.type === 'title') {

						if (!imageAlreadyDisplayed) {
							ui.column.drop['i-'+ j].grid.row2.makeNode('top-gap', 'div', {
								style:'height:5px;'
							});
						}

						if (field.view) {

							field.view(

								ui.column.drop['i-'+ j].grid.row2.makeNode(field.name, 'div', {
									css:'description field-hover',
									style:'padding:0px 8px 0px 8px;'
								})
								, item
								, {
									counter: false,
									verticalAlign: 'top',
									link: false
								}

							);

						} else {

							ui.column.drop['i-'+ j].grid-row2.makeNode(field.name, 'div', {
								css:'description field-hover',
								text:item[fieldName],
								style:'padding:5px 8px 0px 8px;'
							});

						}

						if (!usersAlreadyDisplayed) {
							ui.column.drop['i-'+ j].grid.row2.makeNode('bottom-gap', 'div', {
								style:'height:5px;'
							});
						}

					}

					if (field.type === 'users' || field.type === 'user') {

						if (field.view) {

							if (!_.isEmpty(item[field.name])) {

								usersAlreadyDisplayed = true;

								field.view(

									ui.column.drop['i-'+ j].grid.row4.makeNode(field.name, 'div', {
										css:'description field-hover pull-left'
									})
									, item
									, {
										mini: true,
										placeholder: '',
										style:'padding:5px 5px 15px 8px;'
									}

								);

							}

						}

					}

					if (field.type === 'date') {

						if (field.view) {

							if (!_.isEmpty(item[field.name])) {

								if (!dateAlreadyDisplayed) {

									dateAlreadyDisplayed = true;

									if (options.blueprint[field.name].hasOwnProperty('options') && options.blueprint[field.name].options.is_due_date) {

										field.view(

											ui.column.drop['i-'+ j].grid.row5.makeNode(field.name, 'div', {
												css:'pull-left',
												style:'padding:3px 1px 2px 1px;'
											})
											, item
											, {
												mini: true,
												inBoardView: true,
												labelView: true
											}

										);

									}

								}

							}

						}

					}

					if (field.type === 'timer') {
						
						if (field.view) {

							if (!timerAlreadyDisplayed) {

								timerAlreadyDisplayed = true;

								field.view(

									ui.column.drop['i-'+ j].grid.row5.makeNode(field.name, 'div', {
										css: 'pull-right',
										style:'padding:1px 10px 0px 0px; border-right:1px dashed #ebebeb;'
									})
									, item
									, {
										mini: true,
										inCollection: true,
										border: false,
										background: false
									}

								);

							}	

						}

					}

				});

				if (item.comment_count) {
					ui.column.drop['i-'+ j].grid.row5.makeNode(
						'count'
						, 'div'
						, {
							text: '<i class="far fa-comment"></i> ' + item.comment_count,
							css: 'ui grey text pull-left',
							style: 'display:inline-block; padding:5px 8px 6px 8px; font-size:.85714286rem !important;'
						}
					);
				}

			});

			var templateObj = {};

			if (options.groupBy && options.groupBy.field) {
				templateObj[options.groupBy.field] = groupValue;
			}

            if (
                options.hasOwnProperty('create_action')
                && !appConfig.is_portal
            ) {

                ui.column.cfcont.makeNode('c', 'div', {
					css:'ui container center aligned', 
					style:'padding: 0em !important;'
				});

                ui.column.cfcont.c.makeNode('btns', 'div', {
                    css: 'ui basic transparent fluid buttons'
            	});

                ui.column.cfcont.c.btns.makeNode('new', 'div', {
					css:'ui button',
					text:'<i class="ui green plus icon"></i> New',
					style:'width:85%; padding:5px !important; text-align:left;'
                });

                if ( typeof options.create_action == 'function' ) {

                    ui.column.cfcont.c.btns.new.notify('click', {
                        type:'collections-run',
                        data:{
                            run:function(){

                                options.create_action(templateObj);

                            }
                        }
                    }, sb.moduleId);
                        
                }

                if ( options.create_fromTemplate_action && typeof options.create_fromTemplate_action == 'function' ) {

                    var tempDomId = 'tmplate-' + sb.dom.randomString(6, '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ');

                    ui.column.cfcont.c.btns.makeNode('temp', 'div', {
						css: 'ui floating dropdown icon button',
						style: 'padding:5px !important; text-align:right;',
						text: '<i class="far fa-clone"></i>',
						id: tempDomId
                    });

                    ui.column.cfcont.c.btns.temp.notify('click', {
                        type: 'collections-run',
                        data: {
                            run:function() {

                                sb.notify({
                                    type: 'get-sys-floater',
                                    data: {
                                        element: '#' + tempDomId,
                                        minWidth: '300px',
                                        maxWidth: '300px',
                                        offsetTop: 35,
                                        padding: '0px',
                                        callback: function (floater) {
                                            options.create_fromTemplate_action( floater, templateObj);
                                            floater.container.patch();
                                        }
                                    }
                                });
        
                            }
        
                        }
        
                    });
    
                }
                //! #2015: This is where the board view is placing the create buttons
                // ui.drop.makeNode('btns', 'div', {css:'ui button group', text:'ui btn group here'});
    
                // options.create_ui(
                //     ui.drop.makeNode('create', 'div', {css:'text-center'})
                //     , templateObj
                // );
            }

			ui.patch();

		}

		if(options.isLoading === true) {

			build_placeholder(ui);

		} else {
			
			container.empty();
						
			container.makeNode('boardContainer', 'div', {
				css:'boardContainer',
				style: 'background-color: transparent !important;'
			})
			
			container.boardContainer.makeNode('boardActual', 'div', {
				css:'boardActual',
				id:'boardActual',
				style: 'display: flex; flex-direction:row; flex-wrap: nowrap; width: 100%; overflow-x: scroll; overflow-y: hidden; transform:rotateX(180deg); border-radius:.2rem !important;'
			});
			
			ui = container.boardContainer.boardActual;
			
			_.each(_.sortBy(groups, 'sortId'), function(set, i){

				Data = _.union(Data, set.data);
				
				if(options.config){
					
					if(
						options.config.hasOwnProperty('subviews')
						&& options.config.subviews.hasOwnProperty('board')
						&& options.config.subviews.board.hasOwnProperty('groupBy')
						&& options.config.subviews.board.groupBy.hasOwnProperty('groups')
						&& options.config.subviews.board.groupBy.groups.hasOwnProperty(set.value.toString())
						&& options.config.subviews.board.groupBy.groups[set.value.toString()].collapsed === true
					) {
						
						ui.makeNode('grp-'+i, 'div', {
							style:'min-width:50px; max-width:300px; min-height:60vh; overflow-y: visible; transform:rotateX(180deg); writing-mode:vertical-lr; text-orientation:sideways-right; margin: 0 5px;',
							css: 'ui ' + set.color + ' segment show-pointer '
						});
						
						build_collapsedColumn(ui['grp-'+i], set.title, set.color, set, i, set.data.length, ui['wrap'+i]);
						
					} else {
						
						ui.makeNode('grp-'+i, 'div', {
							style:'min-width:300px; max-width:300px; min-height:60vh; overflow-y: visible; transform:rotateX(180deg); padding-top:5px !important;'
						});
						
						build_expandedColumn(ui['grp-'+i], set, i, ui['wrap'+i]);
						
					}
					
					
				} else {
					
					ui.makeNode('grp-'+i, 'div', {
						style:'min-width:300px; max-width:300px; min-height:60vh; overflow-y: visible; transform:rotateX(180deg);'
					});
					
					build_expandedColumn(ui['grp-'+i], set, i, ui['wrap'+i]);
					
				}

			});

		}

		ui.patch();

	}

	return {

		init:function(){

			sb.notify({
				type:'register-collection-view',
				data:{
					groups: true,
					list: false,
					icon: 'columns',
					id: sb.moduleId,
					name: 'board',
					options: {},
					title: 'Board',
					view: board_ui
				}
			});

			sb.listen({
    			'board-view-card-dropped': this.cardDropped,
    			'board-view-run': this.run,
    			[sb.moduleId+'-column-dropped']: this.columnDropped
			});

		},

		load:function(){},

		destroy:function(){},

		cardDropped: function(data) {

			if (
				data
				&& data.dropped
				&& typeof data.dropped.run === 'function'
			) {
				data.dropped.run(data);
			}

		},

		columnDropped: function(data) {

			var options = data.in.options;
			var groupDropped = data.dropped.groupObj;
			var nextGroup = {};

			if(data.next.hasOwnProperty('groupObj')) {
				nextGroup = data.next.groupObj;
			}

			setTimeout(function() {

				if(nextGroup.hasOwnProperty('value')) {
					options.onGroupReorder(groupDropped.value, nextGroup.value);
				} else {
					options.onGroupReorder(groupDropped.value, undefined);
				}

			}, 0.1);

		},

		run: function(data) {
    		data.run(data);
		}

	}

});
