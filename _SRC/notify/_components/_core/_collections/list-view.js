Factory.register('list-view', function (sb) {

	function getFieldHtml(entity, key, fieldDef, callback) {

		var ret = '';

		if (_.isEmpty(entity)) {

			if (typeof callback === 'function') {
				callback(ret);
			}

			return '';

		}

		switch (fieldDef.fieldType) {

			case 'address':
				sb.notify({
					type: 'view-field',
					data: {
						type: 'address',
						property: key,
						obj: entity,
						options: {
							callback: function (response) {

								ret += response;

								if (typeof callback === 'function') {
									callback(ret);
								}

							}
						}
					}
				});
				break;
			case 'date':
				sb.notify({
					type: 'view-field',
					data: {
						type: 'date',
						property: key,
						obj: entity,
						options: {
							callback: function (response) {

								ret += response;

								if (typeof callback === 'function') {
									callback(ret);
								}

							}
						}
					}
				});
				break;
			case 'edge':
			case 'state':
				sb.data.db.obj.getById(
					"entity_workflow",
					fieldDef.workflow,
					function (workflows) {
						var _state = _.filter(workflows.states, function(state){ return state.id === entity[key]; });

						if(_state[0]){
							ret += _state[0].name + ' / ' + _state[0].color;
							if (typeof callback === 'function') {
								callback(ret);
							}
						} else {
							if (typeof callback === 'function') {
								callback(ret);
							}
						}
					}
				);
				break;
			case 'reference':
				sb.notify({
					type: 'view-field',
					data: {
						type: fieldDef.fieldType,
						property: key,
						obj: entity,
						options: {
							callback: function (response) {

								ret += response;

								if (typeof callback === 'function') {
									callback(ret);
								}

							}
						}
					}
				});
				break;
			case 'timer':
			case 'title':
			case 'user':
			case 'users':
			case 'companies':
			case 'contacts':
				sb.notify({
					type: 'view-field',
					data: {
						type: 'user',
						property: key,
						obj: entity,
						options: {
							callback: function (response) {

								ret += response;

								if (typeof callback === 'function') {
									callback(ret);
								}

							}
						}
					}
				});
				break;
			case 'currency':
				var _val = entity[key];
				ret += '$ '+ (parseInt(_val)/100).formatMoney();
				if (typeof callback === 'function') {
					callback(ret);
				}
				break;
			case 'formula':

				var _val = entity[key];
				if(fieldDef.options && fieldDef.options.format === 'usd'){
					ret += '$ '+ (parseInt(_val)/100).formatMoney();
				} else {
					ret += _val;
				}

				if (typeof callback === 'function') {
					callback(ret);
				}
				break;
			case 'toggle':
				sb.notify({
					type: 'view-field',
					data: {
						type: fieldDef.fieldType,
						property: key,
						obj: entity,
						options: {
							callback: function (response) {

								ret += response;

								if (typeof callback === 'function') {
									callback(ret);
								}

							}
						}
					}
				});
				break;

			case 'attachment':
				sb.notify({
					type: 'view-field',
					data: {
						type: 'attachments',
						property: key,
						obj: entity,
						options: {
							callback: function (response) {

								ret += response;

								if (typeof callback === 'function') {
									callback(ret);
								}

							}
						}
					}
				});
				break;

			case 'checklist':
			case 'select':

				if(fieldDef.options && fieldDef.options.options) {

					sb.notify({
						type: 'view-field',
						data: {
							type: fieldDef.fieldType,
							property: key,
							obj: entity,
							options: {
								options: fieldDef.options.options,
								callback: function (response) {

									ret += response;

									if (typeof callback === 'function') {
										callback(ret);
									}

								}
							}
						}
					});

				} else {
					ret += entity[key];

					if (typeof callback === 'function') {
						callback(ret);
					}
				}
				break;

			case 'table':

				fieldDef.options.page = 0;
				fieldDef.options.pageLength = 100;
				fieldDef.options.pageThrough = true;
				fieldDef.options.callback = function (response) {

					ret += response;

					if (typeof callback === 'function') {
						callback(ret);
					}

				}

				console.log('try to render table: ', key);

				sb.notify({
					type: 'get-table-view',
					data: {
						fieldName: key,
						obj: entity,
						options: fieldDef.options
					}
				});

				break;

			default:

				ret += entity[key];

				if (typeof callback === 'function') {
					callback(ret);
				}

				break;

		}

		return ret;

	}

	function formatEntityAsHtml(entity, entityTypes, callback) {

		var counter = 0;

		var type = _.findWhere(
			entityTypes
			, {
				bp_name: entity.object_bp_type.substr(1)
			}
		);

		var html = '<h1>' + entity.name + '</h1>';
		var htmlArrays = [];
		htmlArrays.push({
			html: html,
			order: -1
		});
		var fields = [];
		_.each(type.blueprint, function (field, key) {
			fields.push({
				key: key
				, field: field
			});
		});

		fields = _.chain(fields)
			.filter(function (i) {

				return !i.field.is_archived;

			})
			.sortBy(
				fields
				, function (i) {

					return i.field.index;

				}
			).value();

		function renderSectionName(field){
			var value = field.options && field.options._sectionName;
			return value ? '<h2 class="single-header">' + value + '</h2>' : '';
		}

		function getName(field){
			return field.options && field.options._message || field.name;
		}

		_.each(fields, function (i) {

			var valHtml = '';
			var doc = '';

			function continueBuildingHTML(valHtml, htmlArrays) {

				var html = '';

				doc = new DOMParser().parseFromString(valHtml, 'text/html');

				html += renderSectionName(i.field);
				html += '<h3 class="single-header">' + getName(i.field) + '</h3>';

				// If there is a body defined, just add inner html
				if (
					doc
					&& doc.body
					&& doc.body.innerHTML
				) {

					//!TODO: run this fix in contracts when compiling
					// merge tags into doc as well.
					html += doc.body.innerHTML;

				} else {

					html += valHtml;

				}

				htmlArrays.push({
					html: html,
					order: i.field.index
				});

				return htmlArrays;

			}

			if (i.key == 'name' || i.key == 'date_created') {

				counter++;
				return;

			} else {

				switch (i.field.fieldType) {

					case 'state':

						htmlArrays = continueBuildingHTML(valHtml, htmlArrays);

						counter++;

						if (counter == fields.length) {

							if (typeof callback === 'function') {
								callback(htmlArrays);
							}

							return htmlArrays;

						}

					case 'edge':

						if (
							i.field.options
							&& i.field.options.multi
							&& i.field.options.listView
						) {

							formatListAsHtml(
								entity[i.key]
								, {}
								, {
									set: i.field.options.objectType.substr(1)
									, mergeForPdf: true
								},
								function (valHtml) {

									htmlArrays = continueBuildingHTML(valHtml, htmlArrays);

									counter++;

									if (counter == fields.length) {

										if (typeof callback === 'function') {
											callback(htmlArrays);
										}

										return htmlArrays;

									}

								}
							);

						} else {
		
							getFieldHtml(entity, i.key, i.field, function (valHtml) {

								htmlArrays = continueBuildingHTML(valHtml, htmlArrays);

								counter++;

								if (counter == fields.length) {

									if (typeof callback === 'function') {
										callback(htmlArrays);
									}

									return htmlArrays;

								}

							});
						}
						break;

					default:
						getFieldHtml(entity, i.key, i.field, function (valHtml) {

							htmlArrays = continueBuildingHTML(valHtml, htmlArrays);

							counter++;

							if (counter == fields.length) {

								if (typeof callback === 'function') {
									callback(htmlArrays);
								}

								return htmlArrays;

							}

						});
						break;

				}

			}

		});

	}


	function list_ui(container, list, options) {
		// console.log('list::', list, options, options.isSortable);
		//!TODO: groups shouldn't have separate containers, just
		// be separted by their headers.

		_.each(options.fields, function(field, key) {
			field.fieldName = key;
		});

		var groupedFields = _.groupBy(options.fields, 'type')
		var ui = container.makeNode('container', 'div', {css: 'ui stackable grid', style: 'margin-left:-1rem !important; margin-right:-1rem !important;'});


		if((appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz') && isActionItemMenu()) {
			ui.makeNode('btns', 'div', {
				css: 'ui basic segment',
				style: 'padding-bottom:5px; width: 100%;'
			});
			ui.btns.makeNode('downloadPdf', 'div', {
				css: 'ui mini right floated button',
				text: '<i class="grey file pdf icon"></i> Download PDF',
			});
			ui.btns.makeNode('share', 'div', {
				css: 'ui mini right floated button',
				text: '<i class="grey share square icon"></i> Share With Client',
			});

			ui.patch();
		}


		var singleView = {};
		var nestChildren = false;
		var ViewStyle = options.viewStyle || 'record';
		var LeftColWidth = 'eight';
		var RightColWidth = 'eight';
		var defaultForceState = false;
		if (
			!_.isEmpty(options)
			&& options.subviews
			&& options.subviews.list
			&& options.subviews.list.nestChildren
		) {
			nestChildren = true;
		}

		if (ViewStyle === 'form') {
			LeftColWidth = 'four';
			RightColWidth = 'twelve';
		}

		function draw_field(){}

		function build_placeholder(ui) {

			ui.makeNode('segs', 'div', {
				css: 'ui basic segments'
			});

			ui.segs.makeNode('seg1', 'div', {
				css: 'ui basic segment',
				style: 'border-radius: 0 !important;'
			});
			ui.segs.makeNode('seg2', 'div', {
				css: 'ui basic segment'
			});
			ui.segs.makeNode('seg3', 'div', {
				css: 'ui basic segment',
				style: 'border-radius: 0 !important;'
			});

			// Segment 1
			ui.segs.seg1.makeNode('plH', 'div', {
				css: 'ui fluid placeholder'
			});

			ui.segs.seg1.plH.makeNode('line1', 'div', {
				css: 'line'
			});
			ui.segs.seg1.plH.makeNode('line2', 'div', {
				css: 'line'
			});

			// Segment 2
			ui.segs.seg2.makeNode('plH', 'div', {
				css: 'ui fluid placeholder'
			});

			ui.segs.seg2.plH.makeNode('line1', 'div', {
				css: 'line'
			});
			ui.segs.seg2.plH.makeNode('line2', 'div', {
				css: 'line'
			});

			// Segment 3
			ui.segs.seg3.makeNode('plH', 'div', {
				css: 'ui fluid placeholder'
			});

			ui.segs.seg3.plH.makeNode('line1', 'div', {
				css: 'line'
			});
			ui.segs.seg3.plH.makeNode('line2', 'div', {
				css: 'line'
			});

		}

		function getAllFormEntities(entities, sb, callback) {
			var queries = _.map(list, function(item){
				return {
					responseName: item.id,
					table: item.object_bp_type,
					query: {
						id: item.id
					},
					childObjs: 2
				}
			});
			sb.data.db.service('DataRepository', 'getEntityTypes', [], function (entityTypes) {
				sb.data.db.service('DataRepository', "get", queries, function (response) {
					var newList = _.map(queries, function (_item) {
						return response[_item.query.id][0]
					});
					callback(newList, entityTypes['data']);
				});
			});

		}


		function isActionItemMenu(){
			var currentBreadCrumb = appConfig.breadcrumbs[appConfig.breadcrumbs.length - 1];
			return currentBreadCrumb.title == 'Client Action items';
		}

		function list_view (container, list, options, nestedIn) {

			if((appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz') && isActionItemMenu()) {
				ui.btns.downloadPdf.notify('click', {
					type: 'collections-run'
					, data: {
						run: function () {
							var completed = [];
							ui.btns.downloadPdf.loading();
							getAllFormEntities(list, sb, function (entities, entityTypes) {
							var pendingToResolve = [];
							var identifier = Date.now() + Math.random();
							pendingToResolve[identifier] = entities.length;
							var mergedHtml = '';
							_.map(entities, function (entity, _index) {
								try {
									formatEntityAsHtml(entity, entityTypes, function (htmlArrays) {
										htmlArrays = _.filter(htmlArrays, function (o) {
											return o.order !== undefined;
										});

										htmlArrays = _.sortBy(htmlArrays, 'order');

										var html = '';

										_.each(htmlArrays, function (htmlArray) {
											html += htmlArray.html
										});

										onComplete(html, _index, identifier, entity);

									});
								}catch(e){
									console.log('error generated: ', e.message);
									pendingToResolve[identifier]--;
								}
							});


							function onComplete(html, _index, identifier, entity) {
								pendingToResolve[identifier]--;
								var tmpHtml = html;
								tmpHtml += '<pagebreak>';

								if(!completed[identifier]){
									completed[identifier] = [];
								}

								completed[identifier][parseInt(_index)] = tmpHtml;

								console.log('pendingToResolve: ', pendingToResolve, entity.name);

								if (pendingToResolve[identifier] <= 0) {
									try {
										var allPdf = _.sortBy(completed[identifier]);
										sb.data.makePDF(allPdf.join(''), 'I');
										ui.btns.downloadPdf.loading(false);
									}catch(e){
										ui.btns.downloadPdf.loading(false);
									}
								}
							}
							});
						}.bind({})
					}
				});
                ui.btns.share.notify('click', {
					type: 'collections-run'
					, data: {
						run: function (actionItems) {

                            var acIDs = _.pluck(actionItems, 'id');
							ui.btns.share.loading();

                            if ( actionItems && _.isArray(actionItems) ) {
                                sb.data.db.service("FGProjectsService", "shareActionItemsWithAllContacts", acIDs, function (response) {

                                    ui.btns.share.loading(false);

                                    var msg = 'Now sharing with the following contacts: <br>'+
                                                '<div class="ui bulleted list">';

                                      _.each(response.contacts, function (contact) {
                                          msg += '<div class="item">' + contact.name + "</div>";
                                      });
                                      msg += "</div>";

                                    sb.notify({
                                        type: "display-alert",
                                        data: {
                                        header: "Updated the project and ("+ response.actionItems.length +") Action Items",
                                        body: msg,
                                        color: "green",
                                        displayTime: 10000,
                                        },
                                    });
                                    
                                });


                            }
							
						}.bind({}, list)
					}
				});
			}
			function updateOrder (item, before, after, list) {

				$('#loader').fadeIn();

				var request = {};

				if (
					before
					&& after
				) {
					request = {
						type:       'between'
						, before:   before
						, after:    after
					};
				} else if (after) {
					request = {
						type:       'after'
						, target:   after
					};
				} else if (before) {
					request = {
						type:       'before'
						, target:   before
					};
				}

				sb.data.db.obj.runSteps(
					{
						'moveSortOrder': request
					}
					, item
					, function (response) {

						_.findWhere(list, {id: item})
							.sort_index = response.sort_index;
						$('#loader').fadeOut();

					}
					, false
				);

			}

			var showDateField = checkSubviewOptions(options, 'showDateField');
			var init_forceState = false;

			if ( (options.subviews || {}).list &&  ((options.subviews || {}).list || {}).forceState ){
				init_forceState = true;
			}

			function checkSubviewOptions(options, optionName) {

				if(!_.isEmpty(options.subviews)) {

					if(options.subviews.hasOwnProperty('list') && !_.isEmpty(options.subviews.list)) {

						if(options.subviews.list.hasOwnProperty(optionName)) {
							return options.subviews.list[optionName];
						} else {
							return false;
						}

					}

				}

			}

			if (!_.isEmpty(list)) {

				var style = options.groupStyle || 'border:none;';
				var dragAndDropReorder = options.isSortable || false;
				var listContainerSetup = {css:'ui unstackable items', style:style};

				if (dragAndDropReorder) {

					listContainerSetup.drag = {
						accepts: function(me, dropped){
							return dropped.type === 'list-item';
						},
						data: {
							type: 'list'
						}
					};

				}


				container.makeNode('items', 'div', listContainerSetup);

				_.each(list, function(obj, i) {

					var priority = 'transparent';
					var avatarURL = 'https://placeimg.com/250/250/tech';
					var itemIcon = '';
					var author = '';
					var selectedListItem = '';

					if (
						options
						&& !_.isEmpty(options.config)
						&& !_.isEmpty(options.config.subviews)
						&& options.config.subviews.list.hasOwnProperty('lastSelectedItem')
						&& options.config.subviews.list.lastSelectedItem === obj.id
					) {

						options.open = obj.id;

					}

					if (
						options.open
						&& obj.id === options.open
					) {
						selectedListItem = ' selected-list-item';
					}


					const displayBlock = (options.config && options.config.customTemplate == 'block') ? 'display:block;' : '';

					var containerSetup = {
						css:'item list-view-item revealParent',
						style:'padding:4px; margin:0px; background:#fff;border:none; border-top: 1px solid rgb(245,245,245); position:relative;' + displayBlock,
						data: {
							'data-id': obj.id,
							'data-sort': i
						}
					};
					if (dragAndDropReorder) {

						containerSetup.style += 'padding-left:1em;';
						containerSetup.drag = {
							moves: 		true,
							drop: 		'list-item-dropped',
							accepts: 	false,
							copy: 		false,
							direction: 	'vertical',
							data: {
								type: 			'list-item',
								item: 			obj,
								instanceId: 	sb.instanceId,
								run: 			function (data) {

									if (options.onSort) {

										options.onSort(data);

									} else {

										var toMove = 	data.dropped.item.id;
										var before = 	'';
										var after = 	'';

										if (data.prev && data.prev.item && data.prev.item.id) {
											before = 	data.prev.item.id;
										}

										if (data.next && data.next.item && data.next.item.id) {
											after = 	data.next.item.id;
										}

										updateOrder(toMove, after, before, list);

									}


								}
							}
						};

					}

					var dom = container.items.makeNode('cont-' + obj.id, 'div', containerSetup);

					if (dragAndDropReorder) {

						dom.makeNode('handle', 'div', {
							css: 		'ui grey hamburger icon revealChild handle'
							, tag: 		'i'
							, style: 	'position:absolute; left:0em; padding-top:10px; color:rgba(175, 175, 175, 1) !important; cursor:grab;'
						});

					}

					///title content
					if (groupedFields.priority) {

						if (options && options.showMultiplePerType) {

							_.each(groupedFields.priority, function (priorityField, i) {

								priorityField.view(
									dom.makeNode('priority-'+ i, 'div', {
										css: 'list-view-item',
										style: 'margin-top:7px; margin-left:7px;'
									})
									, obj
									, {
										inCollection:true
									}
								);

							});

						} else {

							groupedFields.priority[0].view(
								dom.makeNode('priority', 'div', {
									css: 'list-view-item',
									style: 'margin-top:7px; margin-left:7px;'
								})
								, obj
								, {
									inCollection:true
								}
							);

						}

					} else {

						///list item priority block
						dom.makeNode('color', 'div', {style:'margin-right:4px; border-radius:3px; display:inline-block; width:4px !important; background-color:'+ priority +';'});

					}

					// Toggle field
					if (groupedFields.toggle) {

						_.each(
							groupedFields.toggle
							, function (toggleField, i) {

								toggleField.view(
									dom.makeNode(
										't-'+ i
										, 'div'
										, {
											css: 		'item list-view-item'
											, style: 	'margin-top:6px;'
										}
									)
									, obj
									, {
										inCollection: 	true
										, edit: 		true
									}
								);

							}
						);

					}

					// Icon for the list item
					if (nestChildren) {

						var styleOpts = {
							icon:{
								tag:'i'
								, css:'caret right icon'
								, style:'margin:6px 14px 0px 14px; cursor: pointer;'
							}
							, child_objs: {
								css: 	'hidden'
								, style:	'margin-left:24px;'
							}
						};

						if ( init_forceState ){
							styleOpts.icon.css = 'caret down icon';
							styleOpts.child_objs.css = '';
						}

						container.items.makeNode('child-'+ obj.id, 'div', styleOpts.child_objs);

						dom.makeNode('icon', 'div', styleOpts.icon).notify('click', {
							type: 'list-view-run'
							, data: {
								run: toggle_child_objs.bind(
									{}
									, container.items['child-'+ obj.id]
									, obj
									, dom.icon
								)
							}
						}, sb.moduleId);

						if ( init_forceState ) {

							options.get_child_data(obj, function (childData) {

								toggle_child_objs.call(
									{}
									, container.items['child-'+ obj.id]
									, obj
									, dom.icon
									, _.where(childData, {parent: obj.id})
									, 'open'
								);

							});

						}

					} else if (
						obj.object_bp_type == 'groups'
					){

						if(obj.group_type){

							switch(obj.group_type){

								case 'Team':

									itemIcon = 'users large icon';

									break;

								case 'Project':

									itemIcon = 'folder open large icon';

									break;

							}

							dom.makeNode('icon', 'div', {tag:'i', css:itemIcon, style:'margin:8px 14px 0px 14px'});

						} else if (!_.isEmpty(obj.type)) {

							itemIcon = obj.icon +' '+ obj.color + ' icon';

							dom.makeNode('label', 'div', {css: obj.color +' ui label', text:'<i class="'+obj.icon+' icon"></i>'+ obj.type.name.toUpperCase(), style:'font-size:12px; margin:5px 14px 5px 14px;'})

						}

					} else {

						if (groupedFields.author) {

							if(obj.created_by != null && obj.created_by !== false){

								if(
									obj[groupedFields.author[0].name].profile_image.loc != "//"
									&& obj[groupedFields.author[0].name].profile_image !== null
									&& obj[groupedFields.author[0].name].profile_image !== undefined
								){
									avatarURL = sb.data.files.getURL(obj[groupedFields.author[0].name].profile_image);
									dom.makeNode('ava', 'div', {css: 'ui mini spaced image', style:'margin:0px 14px 0px 14px;'})
										.makeNode('avatar', 'image', {url: avatarURL, css:'avatar'});
								} else {

									dom.makeNode('userIcon', 'div', {tag:'i', css: 'user circle large icon', style:'font-size:35px; margin:0px 11px 0px 11px;'});

								}
							}
						}

					}

					///title content
					_.each(groupedFields.title, function(field, j) {

						var titleSetup = { charLimit:20 };
						if (nestChildren) {

							var linkSetup = {
								type: 		obj.object_bp_type,
								id: 		obj.id,
								name: 		obj.name,
								group_type: obj.group_type
							};

							if (nestedIn) {
								linkSetup.startAt = nestedIn.link;
							}

							obj.link =  sb.data.url.createPageURL(
								'object'
								, linkSetup
							);

							titleSetup.parent = false;

						}

						if (
							options.subviews
							&& options.subviews.list
							&& options.subviews.list.backlog
							&& !sb.dom.isMobile
						) {

							titleSetup.onClick = function (obj, selector, willCache) {

								if (
									willCache === undefined
									|| willCache === true
								) {

									// Caching **********
									if (
										options
										&& !_.isEmpty(options.config)
										&& !_.isEmpty(options.config.subviews)
										&& !_.isEmpty(options.config.subviews.list)
										&& options.config.colUID
									) {

										options.config.subviews.list.lastSelectedItem = obj.id;
										sb.notify({
											type: 'update-browser-cache',
											data: {
												id: options.config.colUID
												, callback: function(res, callback) {

													res.subviews = options.config.subviews;

													callback(res);

												}
											}
										}, sb.moduleId);

									}


									// ********** Caching

								}

								// Removed previously selected item class and assign it to new item
								var selectedListItem = $(ui.selector).parent().parent().parent().find('.selected-list-item');
								selectedListItem.removeClass('selected-list-item');
								selectedListItem = $(selector).parent().parent().parent().addClass('selected-list-item');

								// Set padding top
								var paddingTop = selectedListItem.position().top - selectedListItem.offset().top + 72;
								paddingTop = Math.sign(paddingTop) == -1 ? 0 : paddingTop;

								// Set container height
								var containerHeight = $('#singleViewContainer').height();

								function moveSingleViewContainer(paddingTop) {

									function goToNextInList (obj) {

										var index = _.findIndex(
											list
											, function (record) {

												return (record.id === obj.id);

											}
										);
										index++;

										if (typeof options.onNextItemTransition === 'function') {

											if (options.onNextItemTransition(obj) === false) {
												return;
											}

										}

										if (list[index]) {

											$(ui.selector).parent().parent().parent().find('.selected-list-item')
												.removeClass('selected-list-item');

											// If there is a next item to navigate to, add the 'selected-list-item'
											// class to its list item to show it is selected in the list.
											if (
												container.items['cont-'+ list[index].id]
												&& $(container.items['cont-'+ list[index].id])
											) {
												$(
													container.items['cont-'+ list[index].id].selector
												).addClass('selected-list-item');
											}

											options.singleView(
												list[index]
												, singleView
												, {
													compact: true
													, close: function (item) {
														singleView.hide();
													}
													, toNextInList: function (obj) {
														goToNextInList(obj);
													}
													, isLastItem: ((list.length - 1) === index)
												}
											);

										}

									}

									var index = _.findIndex(
										list
										, function (record) {

											return (record.id === obj.id);

										}
									);

									options.singleView(obj, singleView, {
										compact: true
										, close: function (item) {
											singleView.hide();
										}
										, toNextInList: goToNextInList.bind({})
										, isLastItem: ((list.length - 1) === index)
									});

									$('#singleViewContainer').css('padding-top', paddingTop + 'px').fadeIn('fast');

								}

								setTimeout(function() {
									if ( $('#singleViewContainer').length ) {

										// Set container height to avoid flashing
										$('#singleViewContainer').css('min-height', containerHeight + 'px');

										$('#singleViewContainer').fadeOut('fast', function() {
											moveSingleViewContainer(paddingTop);
										});


									} else {

										moveSingleViewContainer(paddingTop);

									}
								}, 1);

							}.bind(dom);

							if (
								options
								&& !_.isEmpty(options.config)
								&& !_.isEmpty(options.config.subviews)
								&& !_.isEmpty(options.config.subviews.list)
								&& options.config.subviews.list.hasOwnProperty('lastSelectedItem')
							) {

								if (options.config.subviews.list.lastSelectedItem === obj.id) {

									titleSetup.lastSelectedItem = obj;

								}

							}

						} else {

							if (typeof field.link === 'function') {

								titleSetup.link = field.link;

							}

						}
						var isFullWidth = false;

						if(options.query && options.query.where && options.query.where.tagged_with) {
							isFullWidth = (_.filter(options.query.where.tagged_with, function(item) {
								return (item == 8348218) || (item == 8348219);
							}).length > 0);
						}

                        if ( appConfig.instance == 'foundation_group' && appConfig.is_portal && obj.status == 'done') {
                            titleSetup.muted = true;
                        }
						field.view(
							dom.makeNode('title-' + field.name, 'div', {
								style:'margin:6px 5px 0px 0px;' + (isFullWidth ? 'min-width:90%;' : '') //overflow:hidden; white-space:nowrap; text-overflow: ellipsis;'
							})
							, obj
							, titleSetup
						);

					});

					///date content
					if(groupedFields.date && sb.dom.isMobile === false && showDateField === true) {

						groupedFields.date[0].view(

							dom.makeNode('date', 'div', {
								style:'margin:6px 5px 0px 20px;'
							}),

							obj

						);

					}

					///detail content
					if(groupedFields.detail){

						if(
							obj.object_bp_type == 'document'
							&& (
								obj.document_type == 'google_doc'
								|| obj.document_type == 'google_sheet'
								|| obj.document_type == 'google_slide'
								|| obj.document_type == 'google_other'
							)

						){

							var googleDocImage = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/releases/_production/_images/google-doc.svg';
							var googleSheetImage = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/releases/_production/_images/google-sheet.svg';
							var googleSlideImage = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/releases/_production/_images/google-slide.svg';
							var googleFileImage = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/releases/_production/_images/google-other.svg';

							switch(obj.document_type){
								case 'google_doc':
									var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-doc.svg';
									var buttonColor = 'blue';
									break;
								case 'google_sheet':
									var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-sheet.svg';
									var buttonColor = 'green';
									break;
								case 'google_slide':
									var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-slide.svg';
									var buttonColor = 'yellow';
									break;
								case 'google_other':
									var iconLink = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_current/_images/google-other.svg';
									var buttonColor = 'teal';
									break;
							}

							var label = dom.makeNode('detail', 'div', {css: 'ui image label'});

							dom.detail.makeNode('google', 'div', {tag:'img', src:iconLink, style:'margin:4px'});

						} else {

							/*
groupedFields.detail[0].view(
                                dom.makeNode('detail', 'div', {
                                    style:'width: 30%; margin:9px 5px 0px 5px; overflow:hidden; white-space:nowrap; text-overflow: ellipsis;'
                                }),
                                obj
                                ,{
                                    charLimit:80
                                }

                            );
*/

						}

					}

					dom.makeNode('states', 'div', {css: 'content middle aligned'});

					if(obj.created_by != null && obj.created_by !== false){

						if(groupedFields.author){
							author = 'Posted by <strong>'+ obj[groupedFields.author[0].name].fname +' '+ obj[groupedFields.author[0].name].lname +'</strong>';
						}

						if(obj.created_by && obj.created_by.fname){
							dom.makeNode('author', 'div', {
								text:author,
								css:'right floated',
								style:'display:inline-block; margin:8px 14px 0px 0px'
							});
						}
					}

					// Users
					if (groupedFields.users) {

						if (options && options.showMultiplePerType) {

							_.each(groupedFields.users, function (userField, i) {

								if (!_.isEmpty(obj[userField.fieldName])) {

									userField.view(

										dom.makeNode('usrs-'+ i, 'div', {
											css: 'right floated',
											style:'margin:3px 10px 0px 0px'
										}),

										obj,

										{
											mini: true,
											placeholder: ''
										}

									);

								}

							});

						} else {

							if (!_.isEmpty(obj[groupedFields.users[0].fieldName])) {

								groupedFields.users[0].view(

									dom.makeNode('items', 'div', {
										css: 'right floated',
										style:'margin:3px 10px 0px 0px'
									}),

									obj,

									{
										mini: true,
										placeholder: ''
									}

								);

							}

						}

					} else if (groupedFields.user) {

						if (options && options.showMultiplePerType) {

							_.each(groupedFields.user, function (userField, i) {

								if (!_.isEmpty(obj[userField.fieldName])) {

									userField.view(

										dom.makeNode('usr-'+ i, 'div', {
											css: 'right floated',
											style:'margin:3px 10px 0px 0px'
										}),

										obj,

										{
											mini: true,
											placeholder: ''
										}

									);

								}

							});

						} else {

							if (!_.isEmpty(obj[groupedFields.user[0].fieldName])) {

								groupedFields.user[0].view(

									dom.makeNode('items', 'div', {
										css: 'right floated',
										style:'margin:3px 10px 0px 0px'
									}),

									obj,

									{
										mini: true,
										placeholder: ''
									}

								);

							}

						}

					}

					// Date
					if (groupedFields.date && options.showDate) {

						if (options && options.showMultiplePerType) {

							_.each(groupedFields.date, function (dateField, i) {

								if (!_.isEmpty(obj[dateField.fieldName])) {

									obj.options = dateField.options;
									dateField.view(
										dom.makeNode('date-'+ i, 'div', {
											css: 'right floated',
											style: 'margin:3px 3px 0px 0px'
										})
										, obj
										, {
											labelView: true
										}
									);

								}

							});

						} else {

							if (!_.isEmpty(obj[groupedFields.date[0].fieldName])) {

								obj.options = groupedFields.date[0].options;
								groupedFields.date[0].view(
									dom.makeNode(
										'date',
										'div', {
											css: 'right floated',
											style: 'margin:3px 3px 0px 0px'
										}
									)
									, obj
									, {
										labelView: true
									}
								);

							}

						}

					}

					// State
					if (groupedFields.state) {

						groupedFields.state[0].view(
							dom.makeNode(
								'body',
								'div', {
									css:'right floated',
									style:'margin:3px 10px 0px 0px'
								}
							)
							, obj
						);

					}

					// Quantity
					if (groupedFields.quantity && options.showQuantity) {

						if (options && options.showMultiplePerType) {

							_.each(groupedFields.quantity, function (qtyField, i) {

								if (obj[qtyField.fieldName]) {

									qtyField.view(
										dom.makeNode(
											'num-'+ i,
											'div', {
												css: 'right floated',
												style: 'margin:10px 5px 0px 0px; font-size:12px;'
											}
										)
										, obj
									);

								}

							});

						} else {

							if (obj[groupedFields.quantity[0].fieldName]) {

								groupedFields.quantity[0].view(
									dom.makeNode(
										'number',
										'div', {
											css: 'right floated',
											style: 'margin:10px 5px 0px 0px; font-size:12px;'
										}
									)
									, obj
								);

							}

						}

					}

					// Timer
					if(
						groupedFields.timer
						&& groupedFields.timer[0]
					) {

						groupedFields.timer[0].view(
							dom.makeNode('t'+i, 'div', {
								css:'right floated',
								style:'margin:2px 5px 0px 0px; height:31px !important;'
							})
							, obj
							, {
								edit: true,
								mini: true,
								inCollection: true
							}
						);

					}

					// Show comment feed fields
					if(groupedFields.comments) {

						_.each(groupedFields.comments, function (commentField, i) {

							commentField.view(

								dom.makeNode('comments-'+ i, 'div', {
									css: 'right floated',
									style:'margin:3px 10px 0px 0px'
								}),

								obj,

								{
									mini: 				true
									, inCollection: 	true
								}

							);

						});

					}

					if (options.actions) {

						options.actions_ui(

							dom.makeNode('actions', 'div', {
								style:'margin:7px 0px 0 0;'
							}),
							obj,
							{
								actionButtonCss:'right floated ui mini icon circular basic simple dropdown',
								actionButtonStyle:'text-align:center;',
								css:'',
								menuStyle:'left: -100px;'
							}
						);

					}

					return;

				});

			}

		}

		function toggle_child_objs(ui, obj, icon, childData, forceState) {

			if (
				(
					$(ui.selector).hasClass('hidden')
					&& forceState !== 'close'
				)
				|| forceState === 'open'
			) {

				$(ui.selector).removeClass('hidden');
				$(icon.selector).removeClass('caret right');
				$(icon.selector).addClass('caret down');

				if (!$(ui.selector).hasClass('loaded')) {

					if (!_.isEmpty(childData)) {

						$(ui.selector).addClass('loaded');
						list_view(
							ui
							, childData
							, options
							, obj
						);
						ui.patch();

					} else {

						options.get_child_data(obj, function (childData) {

							$(ui.selector).addClass('loaded');
							list_view(
								ui
								, childData.data
								, options
								, obj
							);
							ui.patch();

						});

					}

				}

			} else {

				$(icon.selector).removeClass('caret down');
				$(icon.selector).addClass('caret right');
				$(ui.selector).addClass('hidden');

			}

		}

		if (options.updateSelection) {
			options.updateSelection(_.pluck(list, 'id'));
		}

		var style = options.style || '';
		ui.makeNode('col', 'div', {
			css: 'ui sixteen wide column'
			, style: style
		});

		// Expand all button
		if (nestChildren) {

			ui.col.makeNode('icon', 'div', {
				tag:'i'
				, css:'more pull-right'
				, text: 'Expand'
				, style:'margin:6px 14px 0px 14px; cursor: pointer;'
			}).notify('click', {
				type: 'list-view-run'
				, data: {
					run: function (list, icon) {

						if ($(icon.selector).hasClass('more')) {

							$(icon.selector).html('Collapse');
							$(icon.selector).removeClass('more');
							$(icon.selector).addClass('less');

							options.get_child_data(list, function (childData) {

								$(icon.selector).addClass('loaded');

								_.each(list, function (obj) {

									toggle_child_objs(
										ui.col.items['child-'+ obj.id]
										, obj
										, ui.col.items['cont-' + obj.id].icon
										, _.where(childData, {parent: obj.id})
										, 'open'
									);

								});

							});

						} else {

							$(icon.selector).html('Expand');
							$(icon.selector).removeClass('less');
							$(icon.selector).addClass('more');

							_.each(list, function (obj) {

								toggle_child_objs(
									ui.col.items['child-'+ obj.id]
									, obj
									, ui.col.items['cont-' + obj.id].icon
									, []
									, 'close'
								);

							});

						}

					}.bind(
						{}
						, list
						, ui.col.icon
					)
				}
			}, sb.moduleId);

		}

		if (options.isLoading === true) {

			build_placeholder(container);

		} else {

			if (options.isGrouped) {

				if(!_.isEmpty(list)) {

					var i = 0;
					_.each(list, function(group, name) {

						// Create the container
						ui.col.makeNode('g-'+ name, 'div', {});

						// Create the divider (if needed)
						if (i > 0) {
							ui.col['g-'+ name].makeNode('divider', 'div', {
								text: '<div class="ui horizontal divider"></div>'
							});
						}

						var groupTitle = group.title;
						var groupColor = '';
						if(!isNaN(groupTitle) && options.types){

							if (!_.isEmpty(options.types[name])) {
								groupTitle = options.types[name].name;
							}

							if(!groupTitle){
								groupTitle = 'Uncategorized';
							}else{
								if(!_.isEmpty(options.types[name]) && options.types[name].color){
									groupColor = 'ui '+ options.types[name].color +' text';
								}
							}
						}

						// Create the title
						ui.col['g-'+ name].makeNode('title', 'div', {
							tag: 'h3',
							css: groupColor,
							style: 'cursor:pointer; padding-bottom:10px; border-bottom:1px solid rgb(245,245,245);',
							text: '<i class="caret down icon collapseIcon'+name+'" style="margin: 6px 10px 0px 2px;"></i>'+ groupTitle
						}).notify('click', {
							type: ['list-view-run'],
							data: {
								run: function(data) {

									if (ui.col['g-'+ name].hasOwnProperty('isCollapsed')) {

										if(ui.col['g-'+ name].isCollapsed === true) {

											ui.col['g-'+ name].isCollapsed = false;

											$('.collapseIcon'+name).removeClass('caret right');
											$('.collapseIcon'+name).addClass('caret down');

										} else {

											ui.col['g-'+ name].isCollapsed = true;

											$('.collapseIcon'+name).removeClass('caret down');
											$('.collapseIcon'+name).addClass('caret right');

										}

									} else {

										ui.col['g-'+ name].isCollapsed = true;

										$('.collapseIcon'+name).removeClass('caret down');
										$('.collapseIcon'+name).addClass('caret right');

									}

									$(ui.col['g-'+ name].container.selector).toggleClass('hide');

								}
							}
						}, sb.moduleId);

						list_view (
							ui.col['g-'+ name].makeNode('container', 'div', {}),
							group.data,
							options
						);

						// Increment counter
						i++;

					});

				} else {

					list_view(ui.col, list, options);

				}

			} else {

				list_view(ui.col, list, options);

				defaultForceState = false;

			}

		}

		singleView = ui.makeNode('single', 'div', {css: 'ui '+ RightColWidth +' wide column hidden'});
		singleView.makeNode('body', 'div', {css:'ui raised basic segment', style: 'max-height:80vh;overflow-x:hidden;overflow-y:scroll;'});

		if (_.isEmpty(list) && options.emptyMessage !== false) {

			var message = '';

			if(options.hasOwnProperty('emptyMessage')) {

				message = options.emptyMessage;

			} else {

				message = 'No saved items';

			}

			container.makeNode('msg', 'div', {
				tag: 'h5',
				text: message,
				css: 'text-center'
			});

		}

		ui.col.makeNode(
			'new'
			, 'div'
			, {
				css:'ui sixteen wide column'
				, style:'text-align:center;'
			}
		);

		if (options.create_ui) {
			options.create_ui(
				ui.col.new
			);
		}

		singleView.show = function () {

			singleView.removeClass('hidden');

			if ($(ui.col.selector).hasClass('sixteen')) {

				ui.col.removeClass('ui sixteen wide column')
				ui.col.addClass('ui '+ LeftColWidth +' wide column')
				$(singleView.selector).attr('id', 'singleViewContainer')

			}

		};

		singleView.hide = function () {

			singleView.addClass('hidden');

			ui.col.removeClass('ui '+ LeftColWidth +' wide column')
			ui.col.addClass('ui sixteen wide column')
			$(singleView.selector).attr('id', '')

			$(container.selector +' .selected-list-item').removeClass('selected-list-item')

		}

		if (options.open) {

			var openedObj = _.findWhere(list, {id: options.open});

			if (options.isGrouped) {

				openedObj = _.chain(list)
					.pluck('data')
					.flatten()
					.findWhere({id: options.open})
					.value();

			}

			if (openedObj) {

				setTimeout(function () {

					options.singleView(openedObj, singleView, {
						compact: true
						, close: function (item) {
							singleView.hide();
						}
						, toNextInList: function () {

						}
					});

				}, 1);

			}

		}

	}

	return {

		init:function(){

			sb.listen({
				'list-view-run': 		this.run,
				'field-list-view': 		this.fieldListView,
				'list-item-dropped': 	this.onListItemDropped
			});

			sb.notify({
				type:'register-collection-view',
				data:{
					groups:true,
					icon:'list alternate outline',
					id:sb.moduleId,
					name:'list',
					options:{},
					title:'List',
					view:list_ui
				}
			});

		},

		fieldListView:function(data){
			list_ui(data.container, data.list, data.options);
		},

		load:function(){},

		destroy:function(){},

		run:function(data){
			data.run();
		},

		onListItemDropped:function(data){

			if (
				data
				&& data.dropped
				&& typeof data.dropped.run === 'function'
			) {
				data.dropped.run(data);
			}

		}

	}

});
