Factory.register('backlog-view', function(sb){
	
	function backlog_ui(container, list, options){
		console.log('BACKLOG VIEW', arguments);
		
		var groups = _.groupBy(list, function(item){
			
			var ret = item[options.groupBy]
			if( typeof ret === 'object' && ret.hasOwnProperty('name') ) {
				return ret.name;
			}
			return ret;
			
		});
		
		var ui = container.makeNode('container', 'div', {});

		_.each( groups, function(group, name){
			
			ui.makeNode('grp-'+ name, 'div', {});
			
			ui['grp-'+ name].makeNode('title', 'div', {
				tag:'h5',
				text:name
			});
			
			ui['grp-'+ name].makeNode('segments', 'div', {
				css:'ui basic segments'
			});
			
			
			_.each(group, function(item, i){

				ui['grp-'+ name].segments.makeNode('i-'+ i, 'div', {
					css:'ui basic segment'
				});
				
				_.each(options.fields, function(field, fieldName){

					if(field.view){
						
						field.view(
							
							ui['grp-'+ name].segments['i-'+ i].makeNode(fieldName, 'div', {
								css:'description',
								text:item[fieldName],
								style:'display:inline-block;'
							}),
							
							item
							
						);
						
					}else{
						
						ui['grp-'+ name].segments['i-'+ i].makeNode(fieldName, 'div', {
							css:'description',
							text:item[fieldName],
							style:'display:inline-block;'
						});
						
					}
					
				});
				
			});
			
			ui['grp-'+ name].makeNode('div', 'div', {
				text:'<br />'
			});
			
		});
		
		ui.patch();
		
	}
	
	return {
		
		init:function(){
			
			/*
sb.notify({
				type:'register-collection-view',
				data:{
					icon:'th list',
					id:sb.moduleId,
					name:'backlog',
					options:{},
					title:'Backlog',
					view:backlog_ui
				}
			});
			
			sb.notify({
				type:'register-collection-view',
				data:{
					icon:'sliders horizontal',
					id:'timeline',
					name:'timeline',
					options:{},
					title:'Timeline',
					view:backlog_ui
				}
			});
*/
			
			
		},
		
		load:function(){},
		
		destroy:function(){}
		
	}
	
});