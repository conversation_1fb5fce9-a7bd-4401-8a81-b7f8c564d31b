Factory.register('workflows', function(sb){
	
	var ui = 		{};
	var Comps = 	{};
	
	function recurringStateUi (ui, obj) {
		
		var selectedPoint;
		var selectedFollower;
		//!TODO: non weekly/daily cycles will need their own arrays here
		var cycles = {
			daily: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
			weekly: ['S', 'M', 'T', 'W', 'T', 'F', 'S']
		};
		
		function updateState (ui, state, onComplete) {
			
			if (selectedPoint === undefined) {
				return false;
			}
			
			// set ui to loading state
			ui.btns['p-'+ selectedPoint]['p-'+ selectedPoint].loading();
			
			// if follower is already created
			if (selectedFollower) {
				
				sb.data.db.obj.updateState({
					objectId: 	selectedFollower.id,
					newState: 	parseInt(state.uid),
					link:		getObjectPageParams(selectedFollower)
				}, function(updatedObj){
					
					if (typeof onComplete === 'function') {
						onComplete(updatedObj);
					}
					
				});
				
			} else {
				
				//!TODO: non weekly/daily cycles will need their own logic here
				var start = moment().startOf('week')
								.add(selectedPoint, 'days')
								 .format('X');
								 
				var end = moment().startOf('week')
								.add(selectedPoint + 1, 'days')
								 .format('X');
				
				sb.data.db.obj.updateState({
					objectId: 	obj.id,
					newState: 	parseInt(state.uid),
					link:		getObjectPageParams(obj),
					isRecurring: 1,
					between: {
						start: 	start,
						end: 	end
					}
				}, function(updatedObj){
					
					if (typeof onComplete === 'function') {
						onComplete(updatedObj);
					}
					
				});
				
			}
			
		}
		
		ui.empty();
		ui.makeNode('btns', 'div', {
			css: ''
		});	
		ui.makeNode('states', 'div', { 
				css: 'ui fluid dropdown button',
				listener:{
					type:'dropdown'
				},
				style:'top:-16px; visibility:collapse; padding:0px;'
			}).makeNode('menu', 'div', { css: 'fluid menu'});
			
		var today = moment().day();
		
		// points in time (follower groups)
		
		_.each(cycles.weekly, function(point, i){
			
			var btnCss = 'grey';
			var btnStyle = '';
			var currentState;
			var follower = _.find(obj.followers, function(follower){
					return moment(follower.end_date, 'YYYY-MM-DD HH:mm:ss').day() === i;
				});
			
			if (i <= today) { btnCss = '' }

			if (obj.cycle === 'weekly' && !_.contains(obj.schedule_options, i)) {
			
				btnCss = 'disabled basic';
			
			} else if (follower) {
				
				currentState = _.findWhere(obj.type.states, {uid:follower.state});
				if (_.isEmpty(currentState)) {
					currentState = { color: 'red' };
				} else {
					
					if (_.isEmpty(currentState.color)) {
						btnCss = 'basic';
					} else {
						btnCss = currentState.color +' ';
					}
					
				}
				
			} else {
				
				btnCss = 'basic';
				
			}
			
			ui.btns.makeNode('p-'+ i, 'div', {
					style:'display:inline-block; padding:4px;'
				})
				.makeNode('p-'+ i, 'div', {
					text: point,
					css: 'ui icon '+ btnCss +' circular button ',
					style: 'width:2em; height:2em; margin-right:auto; margin-left:auto; padding:0px; margin-top:auto; margin-bottom:auto; padding-top:.5em;'
	// 				style: btnStyle
	// 				style: 'display:inline-block;width:13%;'
					}).notify('click', {
						type: 'workflows-run',
						data: {
							run: function (menu, i, follower) {
								
								selectedFollower = follower;
								selectedPoint = i;
								menu.dropdown('show');
								
							}.bind({}, ui.states, i, follower)
						}
					}, sb.moduleId);
			
		});
		
		var currentState = _.find(obj.type.states, { isEntryPoint: 1 });
		while (currentState) {
			
			ui.states.menu.makeNode(
				'state-'+ currentState.uid,
				'div',
				{
					text: '<i class="'+ currentState.icon +' icon"></i>'+ currentState.name,
					css: 'ui icon item '+ currentState.color +' fluid button',
					style: 'color:white; border-radius:0px !important;visibility:visible;'
				}
			).notify('click', {
				type: 'workflows-run',
				data: {
					run: updateState.bind({}, ui, currentState, function(update){
						
						// update cached follower
						var tmp = _.findWhere(obj.followers, {id: update.id});
						if (tmp) {
							tmp.state = update.state;
							tmp.status = update.status;
						} else {
							obj.followers.push(update);
						}
						
						// refresh ui
						recurringStateUi (ui, obj);
						
					})
				}
			}, sb.moduleId);
			
			currentState = _.find(obj.type.states, { uid: parseInt(currentState.next[0]) });
			
		}
		
		ui.patch();
		
	}

	function statesView(ui, setup){

		var header = setup.header || '';
		var updObject = setup.object;
		
		if (setup.stateProperty) {
			updObject.states = updObject[setup.stateProperty];
		}
		
		if (_.isEmpty(updObject.states)) {
			updObject.states = [
							{
								uid:1,
								name:'Assigned',
								icon:'',
								previous:[],
								next:[2],
								isEntryPoint:1
							}, {
								uid:2,
								name:'In progress',
								icon:'',
								previous:[1],
								next:[3],
								color:'blue'
							}, {
								uid:3,
								name:'Done',
								icon:'',
								previous:[2],
								next:[],
								color:'green',
								type: 'done'
							}
						];
		}

		function displayStates(state){

			var previousState = _.findWhere(updObject.states, {uid:parseInt(state.previous[0])});
			var nextState = _.findWhere(updObject.states, {uid:parseInt(state.next[0])});

			ui.steps.makeNode('state'+state.uid, 'div', {css:'ui step'});
			ui.steps.makeNode('addState'+state.uid, 'div', {css:'ui step'});

			if(state.icon){
				ui.steps['state'+state.uid].makeNode('icon', 'div', {css: state.color +' '+ state.icon+' icon', tag:'i'});
			}
			
			ui.steps['state'+state.uid].makeNode('content', 'div', {css:'content'})
				
			ui.steps['state'+state.uid].content.makeNode('title', 'div', {css:'ui '+ state.color +' header title', text:state.name});

			if(state.type === 'done') {
				
				ui.steps['state'+state.uid].content.makeNode('label', 'div', {
					css: 'ui mini label',
					text: 'Done'
				});
				
				ui.steps['state'+state.uid].content.makeNode('lb_1', 'lineBreak', {
					spaces: 1
				});	
				
			}

			if(nextState){
				displayStates(nextState);
			}
			
		}
		
		ui.empty();
		
		ui.makeNode('header', 'div', {
			css:'ui header',
			text:header
		});
		
		ui.makeNode('steps', 'div', {
			css:'ui fluid mini stackable steps'
			, style:'overflow-x:scroll;'
		});
		
		var modal = ui.makeNode('editModal', 'modal', {size:'small', bodyStyle:''});
		var firstState = _.findWhere(updObject.states, {isEntryPoint:1});

		ui.steps.makeNode('firstStep', 'div', {css:'ui step'});
		ui.steps.firstStep.makeNode('content', 'div', {css:'content'});

		displayStates(firstState);
		
	}
	
	function statesViewBIN(ui, setup){

		var header = setup.header || '';
		var updObject = setup.object;
		
		function displayStates(state){

			var previousState = _.findWhere(updObject.states, {uid:parseInt(state.previous[0])});
			var nextState = _.findWhere(updObject.states, {uid:parseInt(state.next[0])});

			ui.steps.makeNode('state'+state.uid, 'div', {css:'ui step'});

			if(state.icon){
				ui.steps['state'+state.uid].makeNode('icon', 'div', {css: state.color +' '+ state.icon+' icon', tag:'i'});
			}
			
			ui.steps['state'+state.uid].makeNode('content', 'div', {css:'content'})
			ui.steps['state'+state.uid].content.makeNode('title', 'div', {css:'ui '+ state.color +' header title', text:state.name});
			
			if(nextState){
				displayStates(nextState);
			}
			
		}
				
		ui.empty();
		
		ui.makeNode('header', 'div', {
			css:'ui header',
			text:header
		});
		
		ui.makeNode('steps', 'div', {css:'ui fluid steps'});
		
		var modal = ui.makeNode('editModal', 'modal', {size:'small', bodyStyle:'min-height:500px;'});
		var firstState = _.findWhere(updObject.states, {isEntryPoint:1});

		ui.steps.makeNode('firstStep', 'div', {css:'ui step'});
		ui.steps.firstStep.makeNode('content', 'div', {css:'content'});

		displayStates(firstState);
		
	}
	
	function workflow_ui(ui, state, draw){

		// utility funcs
		function get_current_stage(state){

			return _.find(state.stages, {name:state.object[state.stageProperty]});
			
		}
		
		function parse_input_state(ui, state){
			
			// view state parsing
			if(_.isEmpty(ui)){ throw "Invalid ui object"; }
			if(typeof state.objectType !== 'string'){ throw "Invalid objectType"; }
			if(_.isEmpty(state.object)){ throw "Invalid "+ state.objectType +" object"; }
			if(!state.object.hasOwnProperty(state.stageProperty)){ throw "Invalid stageProperty"; }
			
		}
		
		// ui funcs
		function header_ui(container, stages, current_stage){
			
			function map_stage_styles(stages, current_stage, callback, context){
				
				if(context === undefined){
					context = this;
				}
				var current_stage_index = _.findIndex(stages, {name:current_stage.name});

				_.chain(stages)
					.groupBy(function(stage, i){
						
						var stageIndex = _.findIndex(stages, {name:stage.name});
						
						if(stageIndex < current_stage_index){
							return 0;
						}
						if(stageIndex === current_stage_index){
							return 1;
						}
						if(stageIndex > current_stage_index){
							return 2;
						}
						
					})
					.each(function(stageGroup, placement){
						
						var groupColor = '';
						switch(parseInt(placement)){
							case 0:
							groupColor = 'completed';
							break;
							
							case 1:
							groupColor = 'active';
							break;
							
							case 2:
							groupColor = 'disabled';
							break;
						}
						
						_.each(stageGroup, function(stage, i){
							
							var stageText = stage.title;
							var stageIndex = _.findIndex(stages, {name:stage.name});
							
							if(parseInt(placement) === 1){
								stageText = stage.title;
							}
							
							callback.call(context, stage, stageText, groupColor);
													
						});
						
					});
				
			}
			
			function update_stage(current_stage){
				
				map_stage_styles(stages, current_stage, function(stage, stageText, groupColor){
					
					this['stage-'+ stage.name].css(groupColor +' step');
					this['stage-'+ stage.name].title.updateText(stageText);
					
				}, this);
				
			}
			
			map_stage_styles(stages, current_stage, function(stage, stageText, groupColor){
				
				container.makeNode('stage-'+ stage.name, 'div', {
					css:groupColor +' step'
				});
				
				if(stage.icon){
					container['stage-'+ stage.name].makeNode('icon', 'div', {
						css:stage.icon +' icon',
						tag:'i'
					});
				}
				
				container['stage-'+ stage.name].makeNode('content', 'div', {css:'content'})
					.makeNode('title', 'div', {
						text:stageText,
						css:'title'
					});
					
				container['stage-'+ stage.name].content.makeNode('description', 'div', {
						text:stage.subTitle,
						css:'description'
					});
				
			});
				
			container.update = update_stage;
			
		}
		
		function main_ui(ui, state, current_stage){
			
			current_stage.ui(ui, state.object);
			
		}
		
		function layout_ui(ui, state, current_stage){

			state.layout_ui(ui, state.object, current_stage.ui);
			
		}
		
		parse_input_state(ui, state);
		
		var current_stage = get_current_stage(state);
		var ordered = '';
		if(state.ordered){
			ordered = 'ordered';
		}
		
		ui.empty();
		
		header_ui(ui.makeNode('timeline', 'div', {css:'ui mini stackable fluid '+ ordered +' steps'}), state.stages, current_stage);
		layout_ui(ui.makeNode('main', 'div', {css:'column'}), state, current_stage);
		
		ui.patch();
		ui.update = function(object){

			state.object = object.object;
			current_stage = get_current_stage(state);
			this.timeline.update(current_stage);
			layout_ui(ui.makeNode('main', 'div', {}), state, current_stage);
			ui.main.patch();
			
		}
		
		return ui;
		
	}
	
	function getObjectPageParams(item){

		var objType = item.object_bp_type;
		if(objType === 'groups'){
			objType = item.group_type.toLowerCase();
		}
		
		var objName = item.name;
		switch(objType) {
			
			case 'contacts':
			case 'users':
			objName = item.fname +' '+ item.lname;
			break;
			
			case 'tasks':
			objName = item.title;
			break;
				
		}
		
		return sb.data.url.createPageURL(
			'object', 
			{
				type:objType, 
				id:item.id,
				name:objName
			}
		);
		
	}
	
	// workflows list
	
	function build_workflows_collection (ui, state, draw) {
		
		var collectionsSetup = {
				actions:{
					create: {
						type: 'quick'
						, action: function (seed, callback) {
							
							seed.name = 'Untitled workflow';
							sb.data.db.obj.create (
								state.params.t
								, seed
								, function (created) {
									
									created.link = sb.data.url.createPageURL('object', {
										id: 					created.id
										, name: 				created.name
										, object_bp_type: 		'entity_workflow'
										, type: 				'entity_workflow'
									});
									
									callback(created);
								}
							) ;
							
						}
					},
					view:true,
					copy: false
				},
				domObj:ui,
				fields: {
					name:{
						title: 'Name',
						type: 'title'
					},
					description:{
						title:'Description',
						type:'plain-text'
					}
				},
				fullView:{
					type: 	'object-view',
					id: 	'entity_workflow-obj'
				},
				menu: {
					subviews: {
						table: 	true
						, list: 	true
					}
				},
				modalSize: 			'small',
				objectType:			state.params.t,
				parseData: 			function (data, callback) {
					
					_.each (data.data, function (type) {
						
						type.link = sb.data.url.createPageURL('object', {
							id: 					type.id
							, name: 				type.name
							, object_bp_type: 		'entity_workflow'
							, type: 				'entity_workflow'
						});
						
					});
					callback(data);
					
				},
				selectedView: 		'table',
				selectedMobileView: 	'list',
				state:state,
				subviews: {
					table: false,
					board: false,
					list: {
						backlog: false
					},
					chart: false
				},
				where: {}
			};
		
		return collectionsSetup;
		
	}
	
	// open api
	
	function updateStateFlow (request) {

		var states = request.type.states;
		var before = request.before;
		
		if (before && states) {
			before = _.findWhere(states, {uid: before});
		}
		
		switch (request.operation) {
			
			case 'add':
			
				// if no states are yet set
				if (_.isEmpty(states)) {
					
					states = [{
						uid:			1,
						name:			'State 1',
						icon:			'',
						previous:		[],
						next:			[],
						isEntryPoint:	1,
						type: 			'active'
					}];
					
				} else {
					
					var nextId = _.reduce(states, function(memo, state){

						if(state.uid >= memo){
							return state.uid + 1;
						}else{
							return memo;
						}
						
					}, 0);
					var newState = {
						uid:			1,
						name:			'State 1',
						icon:			'',
						previous:		[],
						next:			[],
						isEntryPoint:	0,
						type: 			'active'
					};
					
					var after = {};
					if (before) {
						after = _.findWhere(states, {uid: parseInt(before.next[0])});
					} else {
						after = _.findWhere(states, {isEntryPoint: 1});
					}

					newState.uid = nextId;
					newState.name = 'State '+ nextId;
					
					if(before && before.uid){
						
						before.next = [nextId];
						newState.previous = [before.uid];
						
					}else{
						
						newState.previous = [];
					
					}
					
					if(after && after.uid){
						
						after.previous = [nextId];
						newState.next = [after.uid];
						
						if(after.isEntryPoint){
							after.isEntryPoint = 0;
							newState.isEntryPoint = 1;
						}
						
					}else{
						
						newState.next = [];
						
					}
					
					states.push(newState);
					
				}
				
				break;
				
			case 'remove':
				
				var current = _.findWhere(states, {uid: request.state});
				var before = _.findWhere(states, {uid: parseInt(current.previous[0])});
				var after = _.findWhere(states, {uid: parseInt(current.next[0])});
				
				if (before) {
					before.next = current.next;
				}
				if (after) {
					after.previous = current.previous;
					if (current.isEntryPoint) {
						after.isEntryPoint = true;
					}
				}
				
				states = _.filter(states, function (state) {
					return state.uid !== current.uid;
				});
				
				break;
				
			case 'reorder':

				var current = _.findWhere(states, {uid: request.current});
				var next = _.findWhere(states, {uid: request.next});
				var previous = _.findWhere(states, {uid: parseInt(_.findWhere(states, {uid: current.uid}).previous[0])});
				var current_previous = current.previous;
				var current_next = current.next;
				var next_previous = [];
				var next_next = [];

				if(next !== undefined) {
					
					if(next.isEntryPoint === 1) {
					
						next.isEntryPoint = 0;
						current.isEntryPoint = 1;
						
					} else if(current.isEntryPoint === 1) {
						
						next.isEntryPoint = 1;
						current.isEntryPoint = 0;
						
					}	
					
				}
				
				if(previous !== undefined) {
					
					if(_.contains(previous.next, current.uid.toString())) {
					
						previous.next = current.next;
						
					}	
					
				} else {
					
					states[(parseInt(current_next[0])-1)].previous = [];
					states[(parseInt(current_next[0])-1)].isEntryPoint = 1;
					
				}				
				
				if(next !== undefined) {
					
					next_previous = next.previous;
					next_next = next.next;
					
					next.previous = [current.uid.toString()];
					
					if(_.contains(next.next, current.uid)) {
					
						next.next = current_next;	
						
					}

					if(previous !== undefined && !_.isEmpty(current_next)) {
						
						states[(parseInt(current_next[0])-1)].previous = [previous.uid.toString()];
						
					}
					
					current.next = [next.uid.toString()];
					
					current.previous = next_previous;
					

					
				} else {

					current.next = [];

					if(previous !== undefined) {

						states[(parseInt(current_next[0])-1)].previous = [previous.uid.toString()];
						//states[(parseInt(current_next[0])-1)].previous = previous.next;
						
					} else {
						
						states[(parseInt(current_next[0])-1)].previous = [];
						states[(parseInt(current_next[0])-1)].isEntryPoint = 1;
						
					}

					current.previous = [states[(states.length - 1)].uid.toString()];
					current.isEntryPoint = 0;
					
					
				}
				
				if(next !== undefined) {
					
					_.each(next.next, function(value) {
					
						_.findWhere(states, {uid: parseInt(value)}).previous = [next.uid.toString()];
						
					});
	
					_.each(next.previous, function(value) {
						
						_.findWhere(states, {uid: parseInt(value)}).next = [next.uid.toString()];
						
					});	
					
				}
				
				_.each(current.next, function(value) {
					
					_.findWhere(states, {uid: parseInt(value)}).previous = [current.uid.toString()];
					
				});

				_.each(current.previous, function(value) {
					
					_.findWhere(states, {uid: parseInt(value)}).next = [current.uid.toString()];
					
				});

				break;
			
		}

		sb.data.db.obj.update(
			request.type.object_bp_type
			, {
				id: request.type.id
				, states: states
			}
			, function (updatedType) {
				
				request.type.states = updatedType.states;
				if (typeof request.onComplete === 'function') {
					request.onComplete(request.type.states);
				}
				
			}
			, {
				id: 		true
				, states: 	true
			}
		);
		
	}
	
	function viewWorkflowEditor (ui, state, draw) {

		var pageObjectType = state.pageObjectType;
		var fields = {};
		var workflow = state.pageObject;

		if (state.pageObject.parent != null) {
			fields = state.pageObject.parent.blueprint;
		}
		
		function addStateBtn (ui, nextState) {
			
			ui.states.states.makeNode(
				'c-'+ nextState.uid
				, 'div'
				, {
					style: 'text-align: center; height: 50px;'
				}
			).makeNode(
				'btn'
				, 'div'
				, {
					css: 		'ui circular icon mini button field-manager-plus'
					, text: 	'<i class="ui plus icon"></i>'
					, style: 	'width: 2.5em !important;'+
								'border-radius: 1.5em !important;'+
								'margin-top: calc(25px - 1.25em) !important;'+
								'background-color: white;'
				}
			).notify('click', {
				type: 'workflows-run'
				, data: {
					run:  function () {
						
						ui.states.states['c-'+ nextState.uid].btn.loading();
						
						updateStateFlow({
							before: 		this.uid
							, operation: 	'add'
							, type: 		Workflow
							, onComplete: 	function (updatedStates) {
								
								Workflow.states = updatedStates;
								viewWorkflowEditor(ui, state, draw);
								
							}
						});
						
					}.bind(nextState)
				}
			}, sb.moduleId);
			
		}
		
		function singleStateView (state) {
			
			function updateDescription (state, description, txt) {
				
				state.message = txt;
				sb.data.db.obj.update(
					Workflow.object_bp_type
					, {
						id: 		Workflow.id
						, states: 	Workflow.states
					}
					, function () {}
				);
				
			}
			
			function updateName (state, description, txt) {
				
				sb.data.db.obj.update(
					Workflow.object_bp_type
					, {
						id: 		Workflow.id
						, states: 	Workflow.states
					}
					, function (r) {
						
						viewStateCard(ui.states.states['l-'+ state.uid]['s-'+ state.uid], state);
						ui.states.states['l-'+ state.uid]['s-'+ state.uid].patch();
						
					}
				);
				
			}
			
			$(ui.states.states.selector).removeClass('ui eight sixteen wide column');
			$(ui.states.states.selector).addClass('ui eight wide column');
			
			ui.states.single.removeClass('hidden');
			
			// Header
			ui.states.single.makeNode(
				'c'
				, 'div'
				, {css: 'ui raised basic segment'}
			);
			
			// Remove btn
			ui.states.single.c.makeNode(
				'rm'
				, 'div'
				, {
					css: 'ui right floated circular icon basic red mini button',
					text: '<i class="ui trash icon"></i> Delete State'
				}
			).notify('click', {
				type: 'workflows-run'
				, data: {
					run: function (currentState) {
						
						sb.dom.alerts.ask({
							title: 		'Delete '+ state.name +'?'
							, text: 	''
						}, function (response) {
							
							if (response) {
								
								updateStateFlow({
									state: 			currentState.uid
									, operation: 	'remove'
									, type: 		Workflow
									, onComplete: 	function (updatedStates) {
										
										swal.close();
										Workflow.states = updatedStates;
										viewWorkflowEditor(ui, State, draw);
										
									}
								});
								
							}
							
						});
						
					}.bind({}, state)
				}
			}, sb.moduleId);
			
			sb.notify ({
				type: 'view-field'
				, data: {
					type: 			'title'
					, property: 	'name'
					, obj:			state
					, options: 	{
						edit: 		true
						, editing: 	true
						, commitChanges: false
						, onUpdate: _.throttle(
							updateName.bind({}, state)
							, 2000
						)
						
					}
					, ui: ui.states.single.c.makeNode('name', 'div', {})
				}
			});
			
			sb.notify ({
				type: 'view-field'
				, data: {
					type: 			'detail'
					, property: 	'message'
					, obj:			state
					, options: 	{
						edit: 		true
						, editing: 	true
						, commitChanges: false
						, onUpdate: _.throttle(
							updateDescription.bind({}, state)
							, 2000
						)
						
					}
					, ui: ui.states.single.c.makeNode('message', 'div', {})
				}
			});
			
			// Create tabs
			var style = sb.dom.isMobile ? 'overflow-x:scroll' : '';
			var tabs = ui.states.single.c.makeNode('tabs', 'div', {
				css: 'ui top attached tabular menu',
				style: style
			});
			
			$(document).ready(function() {
				$('.tabular.menu .item').tab();
			});
			
		
			// ============================= //
			// ========== DETAILS ========== //
			// ============================= //
			
			// Create tab
			tabs.makeNode('settingsTab', 'div', {
				css: 'ui item active',
				text: '<i class="cog icon"></i> Settings',
				data: {
					'data-tab': 'settings'
				}
			});
			
			// Create tab container
			var settingsTabContainer = ui.states.single.c.makeNode('settingsTabContainer', 'div', {
				css:'ui bottom attached active tab segment',
				data: {
					'data-tab': 'settings'
				}
			});
			
			// Create set as done toggle
			var setAsDoneToggleContainer = settingsTabContainer.makeNode('setAsDoneToggleContainer', 'div', {});
			setAsDoneToggleContainer.makeNode('label', 'div', {
				text: '<strong>Status Type</strong>'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 		'select',
					property: 	'type',
					obj: 		state,
					options: {
						inCollection: 	true,
						edit: 			true,
						editing: 		true,
						commitUpdates: 	false,
						options: 		[
							{
								name: 		'<i class="ui grey list alternate icon"></i> Open'
								, value: 	'open'
							}
							, {
								name: 		'<i class="ui yellow pencil icon"></i> Preparing'
								, value: 	'preparing'
							}
							, {
								name: 		'<i class="ui purple question icon"></i> Pending'
								, value: 	'pending'
							}
							, {
								name: 		'<i class="ui blue question icon"></i> Information Received'
								, value: 	'informationReceived'
							}
							, {
								name: 		'<i class="ui orange question icon"></i> Final Review'
								, value: 	'finalReview'
							}
							, {
								name: 		'<i class="ui purple question icon"></i> Delivery Pending'
								, value: 	'deliveryPending'
							}																					
							, {
								name: 		'<i class="ui teal pause icon"></i> On Hold'
								, value: 	'onHold'
							}
							, {
								name: 		'<i class="ui red exclamation icon"></i> Action Needed'
								, value: 	'actionNeeded'
							}
							, {
								name: 		'<i class="ui green check icon"></i> Done'
								, value: 	'done'
							}
						],
						onUpdate: function(value) {

							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									
								}
							);
						}
					},
					ui: setAsDoneToggleContainer.makeNode('toggle', 'div', {
						style: 'margin-bottom:15px;'
					})
				}
			});
						
			// Create can transition all toggle
			var canTransitionAllToggleContainer = settingsTabContainer.makeNode('canTransitionAllToggleContainer', 'div', {});
			canTransitionAllToggleContainer.makeNode('label', 'div', {
				text: '<strong>Allow transitions to any state?</strong>'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'toggle',
					property: 'allowAllTransitions',
					obj: state,
					options: {
						inCollection: true,
						edit: true,
						editing: true,
						commitUpdates: false,
						onUpdate: function(value) {
							
							// Update value
							state.allowAllTransitions = value;
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									
								}
							);
						}
					},
					ui: canTransitionAllToggleContainer.makeNode('toggle', 'div', {
						style: 'margin-bottom:15px;'
					})
				}
			});

			// Require confirmation from the user flag
			var requiresConfirmation = settingsTabContainer.makeNode('requiresConfirmation', 'div', {});
			requiresConfirmation.makeNode('label', 'div', {
				text: '<strong>Require confirmation from the user when transitioning to this state?</strong>'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'toggle',
					property: 'requiresConfirmation',
					obj: state,
					options: {
						inCollection: true,
						edit: true,
						editing: true,
						commitUpdates: false,
						onUpdate: function(value) {
							
							// Update value
							state.requiresConfirmation = value;
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									
								}
							);
						}
					},
					ui: requiresConfirmation.makeNode('toggle', 'div', {
						style: 'margin-bottom:15px;'
					})
				}
			});
		
			// Create should transition on tasks complete toggle
			if ( pageObjectType == 'project_types' ) {
			
				var shouldTransitionOnTasksCompleteToggleContainer = settingsTabContainer.makeNode('shouldTransitionOnTasksCompleteToggleContainer', 'div', {});
				shouldTransitionOnTasksCompleteToggleContainer.makeNode('label', 'div', {
					text: '<strong>Move to next state when all tasks in the project are completed?</strong>'
				});
				sb.notify ({
					type: 'view-field',
					data: {
						type: 'toggle',
						property: 'shouldTransitionOnTaskComplete',
						obj: state,
						options: {
							inCollection: true,
							edit: true,
							editing: true,
							commitUpdates: false,
							onUpdate: function(value) {
								
								// Update value
								state.shouldTransitionOnTaskComplete = value;
								
								// Update object
								sb.data.db.obj.update(
									Workflow.object_bp_type,
									{
										id: Workflow.id,
										states: Workflow.states	
									},
									function (r) {
										
									}
								);
							}
						},
						ui: shouldTransitionOnTasksCompleteToggleContainer.makeNode('toggle', 'div', {
							style: 'margin-bottom:15px;'
						})
					}
				});

			}
			
			// color selection
			var statusColorContainer = settingsTabContainer.makeNode('statusColorContainer', 'div', {});
			statusColorContainer.makeNode('label', 'div', {
				text: '<strong>Status Color</strong>',
				style: 'margin-top:5px;'
			});
			statusColorContainer.makeNode('fieldContainer', 'div', {
				css: 'round-border',
				style: 'display:inline-block; margin-top:2px;'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'color-picker',
					property: 'color',
					obj: state,
					options: {
						edit: true,
						editing: true,
						commitUpdates: false,
						onUpdate: function(obj) {
							
							// Update value
							state.color = obj.color;
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									viewStateCard(ui.states.states['l-'+ state.uid]['s-'+ state.uid], state);
									ui.states.states['l-'+ state.uid]['s-'+ state.uid].patch();
								}
							);
						}
					},
					ui: statusColorContainer.fieldContainer.makeNode('color', 'div', {})
				}
			});
			
			// icon selection
			var statusIconContainer = settingsTabContainer.makeNode('statusIconContainer', 'div', {});
			statusIconContainer.makeNode('label', 'div', {
				text: '<strong>Status Icon</strong>',
				style: 'margin-top:15px;'
			});
			statusIconContainer.makeNode('fieldContainer', 'div', {
				css: 'round-border',
				style: 'display:inline-block; margin-top:2px;'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'icon',
					property: 'icon',
					obj: state,
					options: {
						edit: true,
						editing: true,
						commitUpdates: false,
						onUpdate: function(obj) {
							
							// Update value
							state.icon = obj;
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									viewStateCard(ui.states.states['l-'+ state.uid]['s-'+ state.uid], state);
									ui.states.states['l-'+ state.uid]['s-'+ state.uid].patch();
								}
							);
						}
					},
					ui: statusIconContainer.fieldContainer.makeNode('icon', 'div', {})
				}
			});

			// weight
			var weightContainer = settingsTabContainer.makeNode('weightContainer', 'div', {});
			weightContainer.makeNode('label', 'div', {
				text: '<strong>Weight</strong>',
				style: 'margin-top:15px;'
			});
			weightContainer.makeNode('fieldContainer', 'div', {
				css: 'round-border',
				style: 'display:inline-block; margin-top:2px;'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'quantity',
					property: 'weight',
					obj: state,
					options: {
						edit: true,
						editing: true,
						commitUpdates: false,
						min: 0,
						style: 'padding:12px;',
						onUpdate: function(obj) {

							console.log(obj)
							
							// Update value
							state.weight = obj;
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									viewStateCard(ui.states.states['l-'+ state.uid]['s-'+ state.uid], state);
									ui.states.states['l-'+ state.uid]['s-'+ state.uid].patch();
								}
							);
						}
					},
					ui: weightContainer.fieldContainer.makeNode('weight', 'div', {})
				}
			});
			
				
			// ============================= //
			// ========= CONDITIONS ======== //
			// ============================= //
			
			if (!state.isEntryPoint) {
				
				// Create tab
				tabs.makeNode('conditionsTab', 'div', {
					css: 'ui item',
					text: '<i class="tasks icon"></i> Conditions',
					data: {
						'data-tab': 'conditions'
					}
				});
				
				// Create tab container
				var conditionsTabContainer = ui.states.single.c.makeNode('conditionsTabContainer', 'div', {
					css:'ui bottom attached tab segment',
					data: {
						'data-tab': 'conditions'
					}
				});
				
				conditionsTabContainer.makeNode('c-h', 'div', {
					tag: 'h4',
					css: 'ui header',
					text: '<i class="ui grey tasks icon"></i> Conditions that must be met to transition to this state.'
				});
				conditionsTabContainer.makeNode('conditions', 'div', {});
				
				sb.notify({
					type: 'view-conditions',
					data: {
						ui: conditionsTabContainer.conditions,
						state: {
							object: Workflow,
							state: state
						}
					}
				});
				
			}
			
			
			// ============================= //
			// ========== ACTIONS ========== //
			// ============================= //
			
			// Create tab
			tabs.makeNode('actionsTab', 'div', {
				css: 'ui item',
				text: '<i class="bolt icon"></i> Actions',
				data: {
					'data-tab': 'actions'
				}
			});
			
			// Create tab container
			var actionsTabContainer = ui.states.single.c.makeNode('actionsTabContainer', 'div', {
				css:'ui bottom attached tab segment',
				data: {
					'data-tab': 'actions'
				}
			});
			
			// Actions run when transitioned to this state.
			actionsTabContainer.makeNode('a-h', 'div', {
				tag: 'h4',
				css: 'ui header',
				text: '<i class="ui grey bolt icon"></i> Actions that run when successfully transitioned to this state.'
			});
			actionsTabContainer.makeNode('actions', 'div', {});
			
			sb.notify({
				type: 	'view-actions',
				data: 	{
					ui: actionsTabContainer.actions,
					state: {
						object: Workflow,
						state: state
					}
				}
			});
			
			// ============================= //
			// ========== VIEWS ========== //
			// ============================= //
			
			tabs.makeNode('viewsTab', 'div', {
				css: 'ui item',
				text: '<i class="th list icon"></i> Views',
				data: {
					'data-tab': 'views'
				}
			});
			
			var viewsTabContainer = ui.states.single.c.makeNode('viewsTabContainer', 'div', {
					css:'ui bottom attached tab segment',
					data: {
						'data-tab': 'views'
					}
				});
				
			viewsTabContainer.makeNode('a-h', 'div', {
				tag: 'h4',
				css: 'ui header',
				text: '<i class="ui grey th list icon"></i> View will that display when successfully transitioned to this state.'
			});
			viewsTabContainer.makeNode('lb_1', 'lineBreak', {spaces: 1});
			viewsTabContainer.makeNode('views', 'div', {
				css: 'ui secondary mini compact menu'
			});
			viewsTabContainer.makeNode('viewBody', 'div', {});
			
			sb.notify({
				type: 	'show-views-as-tabs'
				, data: 	{
					ui: viewsTabContainer.views
					, fields: fields
					, workflow: workflow // This is the workflow object here
					, options: {
						blueprint: fields
						, editBlueprint: true
						, inWorkflows: true
						, workflowState: state
					}
					, create: false
					, viewContainer: viewsTabContainer.viewBody
					, defaultViews: [
						{
							id: 'all-fields'
							, name: 'All Fields'
							, icon: 'list'
							, view: false
						}
					]
				}
			});
			
			ui.states.single.patch();
			
		}
		
		function viewStateCard (ui, state) {

			function getColor(color) {

				if ( sb.dom.colorsRGBToName[color] ) {
					color = sb.dom.colorsRGBToName[color];
				}
	
				return color;
				
			}
			
			var css = '';
			if (!_.isEmpty(state.previous)) {
				css += ' workflow-state-card-notched';
			}
			if (!_.isEmpty(state.next)) {
				css += ' workflow-state-card-pointing';
			}
			
			ui.makeNode(
				's-'+ state.id
				, 'div'
				, {
					css: 'ui raised basic segment workflow-state-card'+ css
				}
			).makeNode(
				'h'
				, 'div'
				, {
					css: 'ui header'
					, tag: 'h5'
					, text: '<i class="ui '+ getColor(state.color) +' '+ state.icon +' icon"></i>'+ state.name
				}
			);
			
			ui['s-'+ state.id].notify(
				'click'
				, {
					type: 'workflows-run'
					, data: {
						run: function () {
							
							$(ui.selector).parent().parent().find('.selected-list-item')
								.removeClass('selected-list-item');
							
							$(ui['s-'+ state.id].selector).addClass('selected-list-item');
							singleStateView(this)
							
						}.bind(state)
					}
				}
				, sb.moduleId
			);
			
		}

		var states = state.pageObject.states;
		var Workflow = state.pageObject;
		var State = state;
		
		// header
		ui.makeNode('h', 'div', {});
		var fieldOptions = {
			editing: 			true
			, edit: 				true
			, commitUpdates: 	true
		} ;
		
		fieldOptions.fontSize = '3rem';
		fieldOptions.style = 'padding:14px;padding-bottom:0px;';
		sb.notify ({
			type: 'view-field'
			, data: {
				type: 			'title'
				, property: 		'name'
				, obj: 			state.pageObject
				, options: 		fieldOptions
				, ui: 			ui.h
			}
		}) ;
		
		ui.makeNode('lb_1', 'lineBreak', {
			spaces: 1
		});
		
		ui.makeNode('tagsArea', 'div', {});
		
		// show states
		ui.makeNode('states', 'div', {
			css: 'ui column grid'
		});
		ui.states.makeNode('states', 'div', {
			css: 'ui sixteen wide column'
		});
		ui.states.makeNode('single', 'div', {
			css: 'ui eight wide column hidden'
		});
		
		var nextState = _.findWhere(states, {isEntryPoint: 1});
		
		//Create after btn
		addStateBtn(ui, {});
		
		var i = 0;
		while (nextState) {
			
			ui.states.states.makeNode(
				'l-'+ nextState.uid
				, 'div'
				, {
					css: 'ui three column centered grid'
				}
			);
			
			viewStateCard(
				ui.states.states['l-'+ nextState.uid].makeNode(
					's-'+ nextState.uid
					, 'div'
					, {
						css: 'column'
					}
				)
				, nextState
			);
			
			// Create after btn
			addStateBtn(ui, nextState);
			
			if (!_.isEmpty(nextState.next)) {
				nextState = _.findWhere(states, {uid: parseInt(nextState.next[0])});
			} else {
				nextState = false;
			}
			i++;
			
		}
		
		ui.patch();
		
		sb.notify ({
			type: 'view-field'
			, data: {
				type: 'tags'
				, property: 'tagged_with'
				, obj: state.pageObject
				, options: {}
				, ui: ui.tagsArea
			}
		});
		
	}
	
	function viewWorkflowEditorBeta (ui, state, draw) {
		
		var View = ui;
		var State = state;
		var Workflow = state.pageObject;
		var Grid = [];
		var pageObjectType = state.pageObjectType;
		var fields = {};
		if (state.pageObject.parent != null) {
			fields = state.pageObject.parent.blueprint;
		}

		// Model

		function getActionsAndConditions (workflow, onComplete) {

			sb.data.db.obj.getWhere(
				['event_type', 'condition']
				, {
					object: workflow.id
				}
				, function (nodes) {

					onComplete(nodes);

				}
			);

		}

		// View

		function viewDetail (ui) {
				
			ui.open = function (draw, parentSelector) {

				var currentPos = $('#workflow-popup').position();
				var pos;
				if (parentSelector) {

					pos = $('#'+ parentSelector)
							.position();

					pos.top = $('#workflow-grid').scrollTop();
					pos.left += $('#workflow-grid').scrollLeft();

					$('#workflow-popup').removeClass('hidden animated');

					if (!$('#workflow-popup').hasClass('fadeInDown')) {
						$('#workflow-popup').addClass('fadeInDown animated');
					}
					$('#workflow-popup').css(pos);
					// $('#workflow-popup').css({transform: 'translate(' + pos.left -currentPos.left +'px, ' + pos.top -currentPos.y + 'px)'})
						// .css({'webkit-transform': 'translate:(100px,100px);'});
					// console.log('translate::', pos, currentPos, 'translate(' + (+pos.left + -currentPos.left +'px, ' + +pos.top + -currentPos.y + 'px)');

				}

				ui.empty();

				// Close btn
				ui.makeNode(
					'closeBtn'
					, 'div'
					, {
						css: 'ui right floated circular icon basic mini button',
						text: '<i class="ui grey close icon"></i>'
					}
				).notify('click', {
					type: 'workflows-run'
					, data: {
						run: function (currentState) {
							
							ui.close();
							
						}.bind({}, state)
					}
				}, sb.moduleId);

				// Divider
				ui.makeNode('br', 'div', {text: '<br /><br />'});

				ui.makeNode('c', 'div', {});
				ui.patch();

				draw(ui.c);

			};

			ui.close = function () {

				$('#workflow-popup').removeClass('fadeInDown animated');
				$('#workflow-popup').addClass('hidden');

			};

		}

		function viewGrid (ui, grid, draw) {

			function addNodeBtn (ui, nextState) {
				return;
				ui.makeNode(
					'new'
					, 'div'
					, {
						text:'<i class="ui mini plus icon graph-add-node-btn" style="font-size:1.5em !important;"></i>'
						, css:' simple dropdown graph-add-node-btn ui circular icon mini basic button'
						, style:'width: 2em !important;'+
								'border-radius: 1.5em !important;'+
								'margin-top: calc(25px - 1.25em) !important;'
					}
				);

				ui.new.makeNode(
					'm'
					, 'div'
					, {css: 'ui menu'}
				);

				ui.new.m.makeNode(
					'new-state'
					, 'div'
					, {
						css: 		'ui item'
						, text: 	'<i class="ui plus icon"></i> new status'
					}
				).notify('click', {
					type: 'workflows-run'
					, data: {
						run:  function () {
							
							ui.new.m['new-state'].loading();
							
							updateStateFlow({
								before: 		this.uid
								, operation: 	'add'
								, type: 		Workflow
								, onComplete: 	function (updatedStates) {
									
									Workflow.states = updatedStates;
									viewWorkflowEditorBeta(View, State, draw);
									
								}
							});
							
						}.bind(nextState)
					}
				}, sb.moduleId);


				return;
				ui.makeNode(
					'c-'+ nextState.uid
					, 'div'
					, {
						style: 'text-align: center; height: 50px;'
					}
				).makeNode(
					'btn'
					, 'div'
					, {
						css: 		'ui circular icon mini button field-manager-plus'
						, text: 	'<i class="ui plus icon"></i>'
						, style: 	'width: 2.5em !important;'+
									'border-radius: 1.5em !important;'+
									'margin-top: calc(25px - 1.25em) !important;'+
									'background-color: white;'
					}
				).notify('click', {
					type: 'workflows-run'
					, data: {
						run:  function () {
							
							ui['c-'+ nextState.uid].btn.loading();
							
							updateStateFlow({
								before: 		this.uid
								, operation: 	'add'
								, type: 		Workflow
								, onComplete: 	function (updatedStates) {
									
									Workflow.states = updatedStates;
									viewWorkflowEditorBeta(View, State, draw);
									
								}
							});
							
						}.bind(nextState)
					}
				}, sb.moduleId);
				
			}

			function getNodeHtml (x, y, node) {
				
				function getRectangleHtml (x, y, node) {
					
					// Center in its cell in the grid
					var offset = {
						x: 		(+x -(nodeWidth)/2)
						, y: 	(+y -(nodeHeight)/2)
					};
					return '<rect id="'+ node.type +'-'+ node.content.uid +'" x="'+ offset.x +'" y="'+ offset.y +'" width="'+ nodeWidth +'" height="'+ nodeHeight +'" class="graph-node" style="stroke:'+ node.content.color +';fill:'+ node.content.color +';fill-opacity:.3;"></rect>';
				
				}

				function getDiamondHtml (x, y, node) {

					return '<rect id="'+ node.type +'-'+ node.content.id +'" x="0" y="0" width="'+ nodeWidth/1.35 +'" class="graph-node condition-node" height="'+ nodeWidth/1.35 +'" transform="translate('+ x  +', '+ +(y -42) +') rotate(45)" style="" />';
					// return '<rect x="0" y="0" width="'+ nodeWidth +'" class="graph-node condition-node" height="'+ nodeWidth +'" transform="translate('+ x  +', '+ +(y -56.56) +') rotate(45)" style="" />';

				}

				function getCircleHtml (x, y, node) {

					return '<circle id="'+ node.type +'-'+ node.content.id +'" cx="'+ x +'" cy="'+ y +'" r="'+ nodeWidth/2 +'" class="graph-node" />';

				}

				function getTxtHtml (x, y, state) {

					return '<foreignObject style="pointer-events:none;" x="'+ (+x -nodeWidth/2) +'" y="'+ (+y -nodeHeight/2) +'" font-size="14" width="'+ nodeWidth +'" height="'+ nodeHeight +'" dominant-baseline="middle" text-anchor="middle" fill="black">'+ 
								'<p style="text-align:center;height:'+ nodeHeight +'px;line-height:'+ nodeHeight +'px;pointer-events:none;">'+ state.name +'</p>'+
							'</foreignObject>';
					// return '<text x="'+ x +'" y="'+ y +'" font-size="14" width="100" height="100" dominant-baseline="middle" text-anchor="middle" fill="black">'+ state.name +'</text>';

				}

				var txt = 	'<g>';
				var state = node.content;

				switch (node.shape) {

					case 'circle':
						txt += getCircleHtml(x, y, node);
						break;

					case 'diamond':
						txt += 	getDiamondHtml(x, y, node);
						break;

					default:
						txt += 	getRectangleHtml(x, y, node);
						break;

				}
								
				txt +=			getTxtHtml(x, y, state) +
							'</g>';

				if (!_.isEmpty(node.pointsTo)) {

					if (!_.isEmpty(node.pointsTo.right)) {
						pathSvg += drawNodeConnections(ui.canvas, node.pointsTo.right, (+x +(nodeWidth/2)), y, grid);
					}
					if (!_.isEmpty(node.pointsTo.down)) {
						pathSvg += drawNodeConnections(ui.canvas, node.pointsTo.down, x, (y +(nodeHeight/2)), grid);
					}
					
				}

				var nodeId = node.content.id;
				switch (node.content.object_bp_type) {
					
					case 'event_type':
						nodeId = 'event_type-'+ node.content.id;
						break;

					case 'condition':
						nodeId = 'condition-'+ node.content.id;
						break;

					default:
						nodeId = 'state-'+ node.content.uid;
						break;

				}

				if (
					node
					&& !_.isEmpty(node.actions)
				) {

					if (!_.isEmpty(node.actions.left)) {

						txt += '<g id="create-left-'+ nodeId +'" class="graph-edge-create-container">'+
							'<foreignObject x="'+ (+x -nodeWidth/2 - 29.5) +'" y="'+ (+y -9.5) +'" style="" font-size="20" width="'+ nodeWidth +'" height="'+ nodeHeight +'" dominant-baseline="middle" text-anchor="middle" fill="black">'+ 
								'<i class="ui arrow alternate circle left icon graph-edge-create"></i>'+
							'</foreignObject>'+
						'</g>';

					}
					
					if (!_.isEmpty(node.actions.right)) {

						txt += '<g id="create-right-'+ nodeId +'" class="graph-edge-create-container">'+
									'<foreignObject x="'+ (+x +nodeWidth/2 + 9.5) +'" y="'+ (+y -9.5) +'" style="" font-size="20" width="'+ nodeWidth +'" height="'+ nodeHeight +'" dominant-baseline="middle" text-anchor="middle" fill="black">'+ 
										'<i class="ui arrow alternate circle right icon graph-edge-create"></i>'+
									'</foreignObject>'+
								'</g>';

					}

					if (!_.isEmpty(node.actions.down)) {
							
						txt += '<g id="create-down-'+ nodeId +'" class="graph-edge-create-container">'+
								'<foreignObject x="'+ (+x -9.5) +'" y="'+ (+y +9.5 +nodeHeight/2) +'" style="" font-size="20" width="'+ nodeWidth +'" height="'+ nodeHeight +'" dominant-baseline="middle" text-anchor="middle" fill="black">'+ 
									'<i class="ui arrow alternate circle down icon graph-edge-create"></i>'+
								'</foreignObject>'+
							'</g>';

					}

				}
				
				return txt;
				
			}

			function viewActionNode (x, y, action) {

				return 	'<g>'+
							'<circle cx="'+ x +'" cy="'+ y +'" r="'+ nodeWidth/2 +'" class="graph-node" />'+
							'<text x="'+ (+x) +'" y="'+ (+y) +'" font-size="16" width="100" height="100" dominant-baseline="middle" text-anchor="middle" fill="black">'+ action.name +'</text>'+
						'</g>';

			}

			function viewConditionNode (x, y, condition) {

				return 	'<g>'+
							'<rect x="0" y="0" width="80" class="graph-node" height="80" transform="translate('+ (+x + +48) +', '+ y +') rotate(45)" style="" />'+
							// '<rect x="'+ x +'" y="'+ y +'" width="100" height="100" style="fill:white;stroke:black;stroke-width:2;rx:4;"></rect>'+
							'<text x="'+ (+x +50) +'" y="'+ (+y +50) +'" font-size="16" width="100" height="100" dominant-baseline="middle" text-anchor="middle" fill="black">'+ condition.name +'</text>'+
						'</g>';

			}

			function drawNodeConnections (ui, pointers, x, y, grid) {

				var fromX = x;
				var fromY = y;
				var svgHtml = '';

				_.each(grid, function (row, y) {

					_.each(row, function (otherNode, x) {
						
						// Check if line should be draw
						if (
							!(
								otherNode
								&& !_.isEmpty(otherNode.content)
								&& _.contains(pointers, otherNode.content.id)
							)
						) {
							return;
						}

						var xPos = margin.left + colWidth*x;
						var yPos = margin.top + rowHeight*y;

						// Point to the top of it
						if (fromY < yPos) {

							xPos += nodeWidth/2;

						} else {

							xPos += +((colWidth - nodeWidth)/2);
							yPos += +((rowHeight - nodeHeight)/2);
							xPos += -nodeWidth/2;

						}
						
						// !TODO: Refactor follow-up case into this one
						// If its NOT a straight line
						if (
							xPos !== fromX
							&& yPos !== fromY
						) {
							// console.log('here::>>', xPos, fromX, yPos, fromY, otherNode);
						
							// Draw the path of the connection
							svgHtml += '<path class="graph-edge" fill="none" '+ 
											'd="'+
												// Start 
												'M '+ fromX +' '+ fromY +' '+
												// Move a small vertical offset
												'L '+ fromX +' '+ +(+fromY + 12) +' '+
												// Go past it a bit along x
												'L '+ +(+xPos -12) +' '+ +(+fromY + 12) +' '+
												// Get to the y spot
												'L '+ +(+xPos -12) +' '+ +yPos +' '+
												// Move to the final position
												'L '+ xPos +' '+ yPos +' '+
											'"'+
											'stroke-width="2" stroke="black" marker-end="url(#arrowhead)"'+
										'/>';

						// If its just a straight line
						} else {
							
							svgHtml += '<line class="graph-edge" x1="'+ fromX +'px" y1="'+ fromY +'px" x2="'+ xPos +'px" y2="'+ yPos +'px" stroke-width="2" stroke="black" marker-end="url(#arrowhead)"/>';

						}

					});

				});

				return svgHtml;

			}

			var markerSvg = '<defs>'+
								'<marker stroke="black" fill="black" id="arrowhead" viewBox="0 0 4 4" refX="4" refY="2" markerWidth="4" markerHeight="4" orient="auto-start-reverse">'+
									'<path d="M 0 0 L 4 2 L 0 4 z" />'+
								'</marker>'+
							'</defs>';
			var svgHtml = '';
			var pathSvg = '';

			_.each(grid, function (row, y) {

				_.each(row, function (node, x) {

					var xPos = colWidth*x + ((colWidth - nodeWidth)/2);
					var yPos = rowHeight*y + ((rowHeight - nodeHeight)/2);
					xPos += margin.left;
					yPos += margin.top;

					ui.makeNode(
						'c-'+ x +'-'+ y
						, 'div'
						, {
							// tag: 'td'
							style: 'position:absolute;top:'+ yPos +'px;left:'+ xPos +'px;'
							// , css: 'workflow-node'
							// , text: node.title
						}
					);

					if (
						_.isEmpty(node)
						|| _.isEmpty(node.content)
					) {
						return;
					}
					
					switch (node.content.object_bp_type) {

						case 'condition':
							svgHtml += getNodeHtml(xPos, yPos, node);
							break;

						case 'event_type':
							// svgHtml += viewActionNode(xPos, yPos, node.content);
							svgHtml += getNodeHtml(xPos, yPos, node);
							break;
						
						default:
							// getNodeHtml(ui['c-'+ x +'-'+ y], node.content);
							svgHtml += getNodeHtml(xPos, yPos, node);
							break;

					}

					// Connect to next item

					// Create node after btn
					if (node.createLeft) {

						addNodeBtn(
							ui.makeNode(x +'-'+ y +'-before', 'div', {
								css:'workflow-create-after-node'
								, style: 'top:'+ (+yPos -28) +'px;left:'+ (xPos -90) +'px;'
							})
							, {}
						);

					}
					if (node.createRight) {

						addNodeBtn(
							ui.makeNode(x +'-'+ y +'-after', 'div', {
								css:'workflow-create-after-node'
								, style: 'top:'+ (+yPos -28) +'px;left:'+ (xPos + +50) +'px;'
							})
							, node.content
						);

					}

				});

			});

			ui.makeNode('canvas', 'div', {tag:'svg', text: markerSvg + pathSvg + svgHtml, style:'width:'+ +(grid[0].length*colWidth +margin.left*2) +';height:'+ +(grid.length*colWidth +margin.top*2) +'px;'});

		}

		function singleStateView (view, state, onChange) {

			function updateDescription (state, description, txt) {
	
				state.message = txt;
				sb.data.db.obj.update(
					Workflow.object_bp_type
					, {
						id: 		Workflow.id
						, states: 	Workflow.states
					}
					, function () {}
				);
				
			}
			
			function updateName (state, description, txt) {
				
				sb.data.db.obj.update(
					Workflow.object_bp_type
					, {
						id: 		Workflow.id
						, states: 	Workflow.states
					}
					, function (r) {
						
						viewStateCard(ui, state);
						ui.patch();
						
					}
				);
				
			}

			// Remove btn
			view.makeNode(
				'rm'
				, 'div'
				, {
					css: 'ui right floated circular icon basic red mini button',
					text: '<i class="ui trash icon"></i> Delete State'
				}
			).notify('click', {
				type: 'workflows-run'
				, data: {
					run: function (currentState) {
						
						sb.dom.alerts.ask({
							title: 		'Delete '+ state.name +'?'
							, text: 	''
						}, function (response) {
							
							if (response) {
								
								updateStateFlow({
									state: 			currentState.uid
									, operation: 	'remove'
									, type: 		Workflow
									, onComplete: 	function (updatedStates) {
										
										swal.close();
										Workflow.states = updatedStates;
										onChange();
										
									}
								});
								
							}
							
						});
						
					}.bind({}, state)
				}
			}, sb.moduleId);
			
			sb.notify ({
				type: 'view-field'
				, data: {
					type: 			'title'
					, property: 	'name'
					, obj:			state
					, options: 	{
						edit: 		true
						, editing: 	true
						, commitChanges: false
						, onUpdate: _.throttle(
							updateName.bind({}, state)
							, 2000
						)
						
					}
					, ui: view.makeNode('name', 'div', {})
				}
			});
			
			sb.notify ({
				type: 'view-field'
				, data: {
					type: 			'detail'
					, property: 	'message'
					, obj:			state
					, options: 	{
						edit: 		true
						, editing: 	true
						, commitChanges: false
						, onUpdate: _.throttle(
							updateDescription.bind({}, state)
							, 2000
						)
						
					}
					, ui: view.makeNode('message', 'div', {})
				}
			});
			
			// Create tabs
			var style = sb.dom.isMobile ? 'overflow-x:scroll' : '';
			var tabs = view.makeNode('tabs', 'div', {
				css: 'ui top attached tabular menu',
				style: style
			});
			
			$(document).ready(function() {
				$('.tabular.menu .item').tab();
			});
			
		
			// ============================= //
			// ========== DETAILS ========== //
			// ============================= //
			
			// Create tab
			tabs.makeNode('settingsTab', 'div', {
				css: 'ui item active',
				text: '<i class="cog icon"></i> Settings',
				data: {
					'data-tab': 'settings'
				}
			});
			
			// Create tab container
			var settingsTabContainer = view.makeNode('settingsTabContainer', 'div', {
				css:'ui bottom attached active tab segment',
				data: {
					'data-tab': 'settings'
				}
			});
			
			// Create set as done toggle
			var setAsDoneToggleContainer = settingsTabContainer.makeNode('setAsDoneToggleContainer', 'div', {});
			setAsDoneToggleContainer.makeNode('label', 'div', {
				text: '<strong>Set as "Done" status?</strong>'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'toggle',
					property: 'type',
					obj: state,
					options: {
						inCollection: true,
						edit: true,
						editing: true,
						commitUpdates: false,
						onUpdate: function(value) {
							
							// Update value
							state.type = ( value == 'done' ) ? 'done' : 'open';
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									
								}
							);
						}
					},
					ui: setAsDoneToggleContainer.makeNode('toggle', 'div', {
						style: 'margin-bottom:15px;'
					})
				}
			});
						
			// Create can transition all toggle
			var canTransitionAllToggleContainer = settingsTabContainer.makeNode('canTransitionAllToggleContainer', 'div', {});
			canTransitionAllToggleContainer.makeNode('label', 'div', {
				text: '<strong>Allow transitions to any state?</strong>'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'toggle',
					property: 'allowAllTransitions',
					obj: state,
					options: {
						inCollection: true,
						edit: true,
						editing: true,
						commitUpdates: false,
						onUpdate: function(value) {
							
							// Update value
							state.allowAllTransitions = value;
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									
								}
							);
						}
					},
					ui: canTransitionAllToggleContainer.makeNode('toggle', 'div', {
						style: 'margin-bottom:15px;'
					})
				}
			});
		
			// Create should transition on tasks complete toggle
			if ( pageObjectType == 'project_types' ) {
			
				var shouldTransitionOnTasksCompleteToggleContainer = settingsTabContainer.makeNode('shouldTransitionOnTasksCompleteToggleContainer', 'div', {});
				shouldTransitionOnTasksCompleteToggleContainer.makeNode('label', 'div', {
					text: '<strong>Move to next state when all tasks in the project are completed?</strong>'
				});
				sb.notify ({
					type: 'view-field',
					data: {
						type: 'toggle',
						property: 'shouldTransitionOnTaskComplete',
						obj: state,
						options: {
							inCollection: true,
							edit: true,
							editing: true,
							commitUpdates: false,
							onUpdate: function(value) {
								
								// Update value
								state.shouldTransitionOnTaskComplete = value;
								
								// Update object
								sb.data.db.obj.update(
									Workflow.object_bp_type,
									{
										id: Workflow.id,
										states: Workflow.states	
									},
									function (r) {
										
									}
								);
							}
						},
						ui: shouldTransitionOnTasksCompleteToggleContainer.makeNode('toggle', 'div', {
							style: 'margin-bottom:15px;'
						})
					}
				});

			}
			
			// color selection
			var statusColorContainer = settingsTabContainer.makeNode('statusColorContainer', 'div', {});
			statusColorContainer.makeNode('label', 'div', {
				text: '<strong>Status Color</strong>',
				style: 'margin-top:5px;'
			});
			statusColorContainer.makeNode('fieldContainer', 'div', {
				css: 'round-border',
				style: 'display:inline-block; margin-top:2px;'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'color-picker',
					property: 'color',
					obj: state,
					options: {
						edit: true,
						editing: true,
						commitUpdates: false,
						onUpdate: function(obj) {
							
							// Update value
							state.color = obj.color;
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									viewStateCard(ui, state);
									ui.patch();
								}
							);
						}
					},
					ui: statusColorContainer.fieldContainer.makeNode('color', 'div', {})
				}
			});
			
			// icon selection
			var statusIconContainer = settingsTabContainer.makeNode('statusIconContainer', 'div', {});
			statusIconContainer.makeNode('label', 'div', {
				text: '<strong>Status Icon</strong>',
				style: 'margin-top:15px;'
			});
			statusIconContainer.makeNode('fieldContainer', 'div', {
				css: 'round-border',
				style: 'display:inline-block; margin-top:2px;'
			});
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'icon',
					property: 'icon',
					obj: state,
					options: {
						edit: true,
						editing: true,
						commitUpdates: false,
						onUpdate: function(obj) {
							
							// Update value
							state.icon = obj;
							
							// Update object
							sb.data.db.obj.update(
								Workflow.object_bp_type,
								{
									id: Workflow.id,
									states: Workflow.states	
								},
								function (r) {
									viewStateCard(ui, state);
									ui.patch();
								}
							);
						}
					},
					ui: statusIconContainer.fieldContainer.makeNode('icon', 'div', {})
				}
			});
			
			// ============================= //
			// ========= CONDITIONS ======== //
			// ============================= //
			
			if (!state.isEntryPoint) {
				
				// Create tab
				tabs.makeNode('conditionsTab', 'div', {
					css: 'ui item',
					text: '<i class="tasks icon"></i> Conditions',
					data: {
						'data-tab': 'conditions'
					}
				});
				
				// Create tab container
				var conditionsTabContainer = view.makeNode('conditionsTabContainer', 'div', {
					css:'ui bottom attached tab segment',
					data: {
						'data-tab': 'conditions'
					}
				});
				
				conditionsTabContainer.makeNode('c-h', 'div', {
					tag: 'h4',
					css: 'ui header',
					text: '<i class="ui grey tasks icon"></i> Conditions that must be met to transition to this state.'
				});
				conditionsTabContainer.makeNode('conditions', 'div', {});
				
				sb.notify({
					type: 'view-conditions',
					data: {
						ui: conditionsTabContainer.conditions,
						state: {
							object: Workflow,
							state: state
						}
					}
				});
				
			}
			
			// ============================= //
			// ========== ACTIONS ========== //
			// ============================= //
			
			// Create tab
			tabs.makeNode('actionsTab', 'div', {
				css: 'ui item',
				text: '<i class="bolt icon"></i> Actions',
				data: {
					'data-tab': 'actions'
				}
			});
			
			// Create tab container
			var actionsTabContainer = view.makeNode('actionsTabContainer', 'div', {
				css:'ui bottom attached tab segment',
				data: {
					'data-tab': 'actions'
				}
			});
			
			// Actions run when transitioned to this state.
			actionsTabContainer.makeNode('a-h', 'div', {
				tag: 'h4',
				css: 'ui header',
				text: '<i class="ui grey bolt icon"></i> Actions that run when successfully transitioned to this state.'
			});
			actionsTabContainer.makeNode('actions', 'div', {});
			
			sb.notify({
				type: 	'view-actions',
				data: 	{
					ui: actionsTabContainer.actions,
					state: {
						object: Workflow,
						state: state
					}
				}
			});

			// ============================= //
			// ========== VIEWS ========== //
			// ============================= //
			
			tabs.makeNode('viewsTab', 'div', {
				css: 'ui item',
				text: '<i class="th list icon"></i> Views',
				data: {
					'data-tab': 'views'
				}
			});
			
			var viewsTabContainer = view.makeNode('viewsTabContainer', 'div', {
					css:'ui bottom attached tab segment',
					data: {
						'data-tab': 'views'
					}
				});
				
			viewsTabContainer.makeNode('a-h', 'div', {
				tag: 'h4',
				css: 'ui header',
				text: '<i class="ui grey th list icon"></i> View will that display when successfully transitioned to this state.'
			});
			viewsTabContainer.makeNode('lb_1', 'lineBreak', {spaces: 1});
			viewsTabContainer.makeNode('views', 'div', {
				css: 'ui secondary mini compact menu'
			});
			viewsTabContainer.makeNode('viewBody', 'div', {});
			
			sb.notify({
				type: 	'show-views-as-tabs'
				, data: 	{
					ui: viewsTabContainer.views
					, fields: fields
					, workflow: Workflow // This is the workflow object here
					, options: {
						blueprint: fields
						, editBlueprint: true
						, inWorkflows: true
						, workflowState: state
					}
					, create: false
					, viewContainer: viewsTabContainer.viewBody
					, defaultViews: [
						{
							id: 'all-fields'
							, name: 'All Fields'
							, icon: 'list'
							, view: false
						}
					]
				}
			});
			
			view.patch();
			return;
			
		}

		var colWidth = 160;
		var rowHeight = 160;
		var nodeHeight = 80;
		var nodeWidth = 80;
		var margin = {
			left: 	100
			, top: 	100
		};
		
		ui.empty();

		// header
		ui.makeNode('h', 'div', {});
		var fieldOptions = {
			editing: 			true
			, edit: 				true
			, commitUpdates: 	true
		};
		
		fieldOptions.fontSize = '3rem';
		fieldOptions.style = 'padding:14px;padding-bottom:0px;';
		sb.notify ({
			type: 'view-field'
			, data: {
				type: 			'title'
				, property: 	'name'
				, obj: 			state.pageObject
				, options: 		fieldOptions
				, ui: 			ui.h
			}
		});
		
		ui.makeNode('lb_1', 'lineBreak', {
			spaces: 1
		});
		
		ui.makeNode('tagsArea', 'div', {style:'padding-left:14px;'});

		getActionsAndConditions(Workflow, function (actionsAndConditions) {

			// Each state node moves right (gets its own column)
			Grid = [[]];
			var currentState = _.findWhere(Workflow.states, {isEntryPoint:1});
			var i = 0;
			var GridSetup = {
				actions: {
					createState: {
						name: 		'Create a new state'
						, action: 	function (node, direction, onComplete) {

							var nextState = _.findWhere(Workflow.states, {uid: node.content.uid});
							if (direction === 'left') {

								if (nextState.isEntryPoint) {
									nextState = {};
								} else {
									nextState = _.findWhere(Workflow.states, {uid: nextState.previous[0]});
								}
								
							}

							updateStateFlow({
								before: 		nextState.uid
								, operation: 	'add'
								, type: 		Workflow
								, onComplete: 	function (updatedStates) {
									
									Workflow.states = updatedStates;
									onComplete(View, State, draw);
																		
								}
							});

						}
						, icon: 	'square outline'
					}
					, createCondition: {
						name: 		'Create a blocking condition'
						, action: 	function (node, direction, onComplete) {

							var toCreate = {
								object_bp_type: 'condition'
								, is_template: 	0
								, options: 		{}
								, tagged_with: 	[parseInt(appConfig.user.id)]
								, object: 		Workflow.id
							};

							switch (node.content.object_bp_type) {

								case 'condition':
								case 'event_type':
									toCreate.previous = {
										type: 		node.content.object_bp_type
										, pointer: 	node.content.id
									};
									break;

								// Workflow states
								default: 
									toCreate.state = node.content.uid;
									if (direction === 'down') {
										toCreate.is_after_state_change = true;
									}
									break;

							}

							sb.data.db.obj.create(
								'condition'
								, toCreate
								, function (newCondition) {
									
									actionsAndConditions.push(newCondition);
									switch (node.content.object_bp_type) {

										case 'condition':
										case 'event_type':
											var upd = {
												id: node.content.id
											};
											if (
												node.content.object_bp_type === 'condition'
												&& direction === 'down'
											) {
												upd.else_do = {
													type: 		'condition'
													, pointer: 	newCondition.id
												};
											} else {
												upd.next = {
													type: 		'condition'
													, pointer: 	newCondition.id
												};
											}
											sb.data.db.obj.update(
												node.content.object_bp_type
												, upd
												, function (updated) {

													node.content.next = updated.next;
													onComplete(View, State, draw);

												}
											);
											break;

										// Workflow states
										default: 
											onComplete(View, State, draw);
											break;

									}

								}
							);

						}
						, icon: 	'square outline'
					}
					, createAction: {
						name: 		'Create an action'
						, action: 	function (node, direction, onComplete) {

							var toCreate = {
								object_bp_type: 'event_type'
								, is_template: 	0
								, options: 		{}
								, tagged_with: 	[parseInt(appConfig.user.id)]
								, object: 		Workflow.id
							};

							switch (node.content.object_bp_type) {

								case 'condition':
								case 'event_type':
									toCreate.previous = {
										type: 		node.content.object_bp_type
										, pointer: 	node.content.id
									};
									break;

								// Workflow states
								default: 
									toCreate.state = node.content.uid;
									break;

							}

							sb.data.db.obj.create(
								'event_type'
								, toCreate
								, function (newAction) {
									
									actionsAndConditions.push(newAction);
									switch (node.content.object_bp_type) {

										case 'condition':
										case 'event_type':
											var upd = {
												id: node.content.id
											};
											
											if (
												node.content.object_bp_type === 'condition'
												&& direction === 'down'
											) {
												upd.else_do = {
													type: 		'event_type'
													, pointer: 	newAction.id
												};
											} else {
												upd.next = {
													type: 		'event_type'
													, pointer: 	newAction.id
												};
											}

											sb.data.db.obj.update(
												node.content.object_bp_type
												, upd
												, function (updated) {

													node.content.next = updated.next;
													onComplete(View, State, draw);

												}
											);
											break;

										// Workflow states
										default: 
											onComplete(View, State, draw);
											break;

									}

								}
							);

						}
						, icon: 	'circle outline'
					}
					, connectToState: {
						name: 		'Connect to existing state'
						, action: 	function (view, node, direction, onComplete) {

							view.empty();
							view.makeNode('select', 'form', [{
								name: 'state'
								, label: 'Select a state to connect to'
								, type: 'select'
								, options: _.map(Workflow.states, function (state) {
									
									return {
										name: 		state.name
										, value: 	state.uid
									};

								})
							}]);
							
							view.makeNode('br', 'div', {text: '<br />'});
							view.makeNode('submit', 'div', {
								text: 	'<i class="ui check icon"></i> Submit'
								, css: 	'ui teal fluid mini icon button'
							}).notify('click', {
								type: 'workflows-run'
								, data: {
									run: function () {

										view.submit.loading();
										
										var formData = view.select.process().fields;
										var upd = {
											id: 	node.content.id
										};

										switch (direction) {
											case 'down':
												upd.else_do = {
													type: 'state'
													, pointer: parseInt(formData.state.value)
												}
												break;
											
											case 'right':
												upd.next = {
													type: 'state'
													, pointer: parseInt(formData.state.value)
												}
												break;
											
											default:
												return false;
										}

										sb.data.db.obj.update(
											'condition'
											, upd
											, function (response) {

												node.content.else = response.else;
												console.log('formData::', formData, node);
												onComplete(View, State, draw);

											}
										);

									}
								}
							}, sb.moduleId);

							view.patch();

						}
						, icon: 	'hand point right outline'
					}
				}
				, views: {
					'state': 		function (view, nodeUid, onChange) {

						var state = _.findWhere(
							Workflow.states
							, {
								uid: parseInt(nodeUid)
							}
						);
						singleStateView(view, state, onChange);

					}
					, 'condition': 	function (view, conditionId, onChange) {

						sb.data.db.obj.getById(
							'condition'
							, conditionId
							, function (conditionObj) {
								
								view.makeNode(
									'rm'
									, 'div'
									, {
										css: 'ui right floated circular icon basic red mini button',
										text: '<i class="ui trash icon"></i> Delete Condition'
									}
								).notify('click', {
									type: 'workflows-run'
									, data: {
										run: function () {
											
											sb.dom.alerts.ask({
												title: 		'Delete '+ conditionObj.name +'?'
												, text: 	''
											}, function (response) {
												
												if (response) {
													
													sb.data.db.obj.erase(
														'condition'
														, conditionObj.id
														, function () {
															
															actionsAndConditions = _.filter(
																actionsAndConditions
																, function (node) {
																	return node.id !== conditionObj.id;
																}
															);
															onChange();
															swal.close();

														}
													);
													
												}
												
											});
											
										}.bind({}, state)
									}
								}, sb.moduleId);
								view.makeNode('c', 'div', {});
								view.patch();

								sb.notify({
									type: 'view-condition'
									, data: {
										ui: view.c
										, state: {
											object: Workflow
										}
										, condition: conditionObj
										, onComplete: function (response) {
											console.log('onResponse::', response);
											onChange(response);
											
										}
									}
								});

							}
						)

					}
					, 'action': 	function (view, actionId, onChange) {
						
						sb.data.db.obj.getById(
							'event_type'
							, actionId
							, function (actionObj) {

								var stateObj =_.findWhere(
									Workflow.states
									, {
										uid: parseInt(actionObj.state)
									}
								);

								view.makeNode(
									'rm'
									, 'div'
									, {
										css: 'ui right floated circular icon basic red mini button',
										text: '<i class="ui trash icon"></i> Delete Action'
									}
								).notify('click', {
									type: 'workflows-run'
									, data: {
										run: function () {
											
											sb.dom.alerts.ask({
												title: 		'Delete '+ actionObj.name +'?'
												, text: 	''
											}, function (response) {
												
												if (response) {
													
													sb.data.db.obj.erase(
														'condition'
														, actionObj.id
														, function () {
															
															actionsAndConditions = _.filter(
																actionsAndConditions
																, function (node) {
																	return node.id !== actionObj.id;
																}
															);
															onChange();
															swal.close();

														}
													);
													
												}
												
											});
											
										}.bind({}, state)
									}
								}, sb.moduleId);
								view.makeNode('br', 'div', {text: '<br /><br />'});
								view.makeNode('c', 'div', {});
								view.patch();
								
								sb.notify({
									type: 'view-single-action'
									, data: {
										ui: 			view.c
										, action: 		actionObj
										, onComplete: 	function () {
											onChange();
										}
										, refresh: 		false
										, state: {
											object: Workflow
											, state: stateObj
										}
									}
								});

							}
							, 1
						);

					}
				}
			};

			function getActionNode (action) {

				var node = {
					content: 	action
					, shape: 	'circle'
					, type: 	'action'
					, pointsTo: {
						down: []
					}
					, actions: 	{
						down: 	['createAction', 'createCondition']
					}
				};
				// !WORKING HERE:::
				// 	- Refactor this into a more general node func.
				switch (action.object_bp_type) {

					case 'event_type':
						break;
					
					case 'condition':
						node.shape = 'diamond';
						node.type = 'condition';
						node.actions = {};
						node = {
							content: action
							, shape: 'diamond'
							, pointsTo: {
								up: []
								, down: []
								, left: []
								, right: []
							}
							, type: 'condition'
						};
		
						node.createRight = true;
	
						// Create 'else' path
						node.actions = {
							down: ['connectToState', 'createAction', 'createCondition']
							, right: ['connectToState', 'createAction', 'createCondition']
							// down: ['connectToState', 'createAction', 'createCondition']
							// , right: ['connectToState', 'createAction', 'createCondition']
						};

						// Add path for true case
						if (!_.isEmpty(action.next)) {
							
							switch (action.next.type) {
	
								case 'state':
									node.pointsTo.right = [action.next.pointer];
									break;
	
							}
	
						}
	
						// Add path for else case
						if (!_.isEmpty(action.else_do)) {
							
							switch (action.else_do.type) {
	
								case 'state':
									node.pointsTo.down = [action.else_do.pointer];
									break;
	
							}
	
						}
						break;

				}

				return node;

			}

			function chainNextAction (action, Grid, memo) {

				// Establish the memo, tracking the offset
				// from the root node.
				if (memo === undefined) {
					memo = {
						x: 	0
						, y: 0
					};
				}
				
				// Add this node
				var rootX = xOff + memo.x;
				var rootY = yOff + memo.y;
				if (_.isEmpty(Grid[rootY])) {
					Grid[rootY] = [];
				}
				Grid[rootY][rootX] = getActionNode(action);
				
				// False path for conditions
				if (
					action.object_bp_type === 'condition'
					&& !_.isEmpty(action.else_do)
				) {

					nextAction = _.findWhere(actionsAndConditions, {id:action.else_do.pointer});
					if (nextAction) {
						
						Grid[rootY][rootX].pointsTo.down.push(nextAction.id);
						chainNextAction(nextAction, Grid, {
							x: 		memo.x
							, y: 	memo.y + 1
						});

					}

				}

				// Check if we should step to another node, recurse
				if (
					!_.isEmpty(action.next) 
					&& (
						action.next.type === 'event_type'
						|| action.next.type === 'condition'
					)
				) {
					
					// Next
					var nextAction = _.findWhere(actionsAndConditions, {id:action.next.pointer});
					if (nextAction) {
						
						switch (action.object_bp_type) {

							case 'event_type':
								Grid[rootY][rootX].pointsTo.down.push(nextAction.id);
								chainNextAction(nextAction, Grid, {
									x: memo.x
									, y: memo.y + 1
								});
								break;
							
							case 'condition':
								console.log('added to: ', yOff + memo.y, xOff + memo.x);
								Grid[rootY][rootX].pointsTo.right.push(nextAction.id);
								chainNextAction(nextAction, Grid, {
									x: memo.x + 1
									, y: memo.y
								});
								// These need to contribute to the overall xOff
								xOff++;
								break;

							default:
								return;

						}

					}

				}

			}

			var yOff = 0;
			var xOff = 0;
			while (currentState) {

				// Get blocking conditions, and create nodes for them before the 
				// state which they are blocking (to the left).
				var conditionsOnThisState = 
					_.chain(actionsAndConditions)
						.filter(function (node) {
							
							return (
								node.object_bp_type === 'condition'
								&& node.state === currentState.uid
								&& node.is_after_state_change !== true
							);

						})
							.compact()
							//  .sortBy()
							.value();
				
				_.each(conditionsOnThisState, function (condition, j) {

					var condX = xOff;
					var condY = yOff;

					// Chain to next action/condition
					chainNextAction(condition, Grid, {
						x: 0
						, y: 0
					});
					// Grid[yOff][xOff] = getActionNode(condition);
					if (xOff === 0) {
						Grid[yOff][xOff].createLeft = true;
					}

					// If there's another condition, point there
					if (conditionsOnThisState[j + 1]) {
	
						Grid[condY][condX].pointsTo.right = [conditionsOnThisState[j + 1].id];

					// Otherwise, point to the state
					} else {

						Grid[condY][condX].pointsTo.right = [currentState.uid];

					}

					xOff++;

				});

				var stateX = xOff;
				var stateY = yOff;

				// Create node for the state
				Grid[yOff][xOff] = {
					content: 	currentState
					, pointsTo: {
						up: []
						, down: []
						, left: []
						, right: []
					}
					, type: 	'state'
				};

				Grid[yOff][xOff].actions = {
					right: 	['createState']
					, left: ['createCondition', 'createState']
					, down: ['createAction', 'createCondition']
				};

				// Connect to next states
				//  . states
				//  . conditions
				//  . actions
				if (!_.isEmpty(currentState.next)) {
					_.each(currentState.next, function (nextStateUid) {

						var next = _.findWhere(Workflow.states, {
							uid: parseInt(nextStateUid)
						});
						if (next) {

							var nextStateConditions = _.chain(actionsAndConditions)
								.filter(function (node) {
									
									return (
										node.object_bp_type === 'condition'
										&& node.state === next.uid
										&& node.is_after_state_change !== true
									);
			
								})
									.compact()
								//	 .sortBy()
									  .value();
							
							// Check for condition blocking next state, and point there
							// if it exists.
							if (!_.isEmpty(nextStateConditions)) {
								Grid[yOff][xOff].pointsTo.right.push(nextStateConditions[0].id);
							// Otherwise, just point to the next state
							} else {
								Grid[yOff][xOff].pointsTo.right.push(next.id);
							}
							
						}

					});
				}

				// Get actions to run when transitioned to this state, and place them
				// underneath their corresponding state.
				var branch = _.chain(actionsAndConditions)
					.filter(function (node) {

						// Actions or conditions triggered by the state transition
						return (
							(
								node.object_bp_type === 'event_type'
								&& node.state === currentState.uid
							) 
							|| (
								node.object_bp_type === 'condition'
								&& node.state === currentState.uid
								&& node.is_after_state_change === true
							)
						);

					})
						.compact()
						 .value();
						//  .sortBy();
				

				_.each(branch, function (action, j) {

					if (action) {
						Grid[stateY][stateX].pointsTo.down.push(action.id);
					}
					
					// Chain to next action/condition
					chainNextAction(action, Grid, {
						x: j
						, y: 1
					});
					
				});

				if (branch.length > 1) {
					xOff += branch.length - 1;
				}

				// Proceed to next state and column
				currentState = _.findWhere(Workflow.states, {
					uid: parseInt(currentState.next[0])
				});
				
				xOff++;
				i++;

			}

			ui.makeNode('topScroll', 'div', {id: 'wfEditTopScrollbar', style: 'margin:18px; width:'+ (+Grid[0].length*colWidth +margin.left*2) +';overflow-x:scroll !important;', text: '<div style="width:1480px !important;min-height:10px;"></div>'});
			ui.makeNode('g', 'div', {css: 'workflow-grid', id: 'workflow-grid', style:'min-height:1000px;overflow-x:scroll;position:relative;'})
				.listeners.push(
					function () {

						$('.graph-node')
							.on('click', function (e) {
								// console.log('el::', this, e);
								console.log('e::', e, $(this).attr('id'));
								var nodeId = $(this).attr('id');
								var nodeType = nodeId.split('-')[0];

								if (GridSetup.views && GridSetup.views[nodeType]) {
									
									var nodeUid = parseInt(nodeId.split('-')[1]);
									View.g.detail.open(function (view) {

										GridSetup.views[nodeType](view, nodeUid, function (response) {
											viewWorkflowEditorBeta(View, State, draw);
										});

									}, nodeId);

								}
								// singleStateView($(this).attr('id'));
							});

						$('.graph-edge-create-container')
							.on('click', function (e) {
								
								var btnId = $(this).attr('id');
								var direction = btnId.split('-')[1];
								var nodeType = btnId.split('-')[2];
								var itemId = parseInt(btnId.split('-')[3]);
								var node = false;
								_.each(Grid, function (row, i) {
									_.each(row, function (cell, j) {
										if (
											!_.isEmpty(cell)
											&& !_.isEmpty(cell.content) 
											&& (
												cell.content.id === itemId
												|| cell.content.uid === itemId
											)
										) {
											node = cell;
										}
									});
								});

								var allowedActions = [];
								if (node.actions && node.actions[direction]) {
									allowedActions = node.actions[direction];
								}

								View.g.detail.open(function (view) {

									_.each(allowedActions, function (actionKey) {
										
										var action = GridSetup.actions[actionKey];
										view.makeNode(
											'a-'+ actionKey
											, 'div'
											, {
												style: 'text-align: center; height: 50px;'
											}
										).makeNode(
											'btn'
											, 'div'
											, {
												css: 		'ui mini fluid button field-manager-plus'
												, text: 	'<i class="ui '+ action.icon +' icon"></i> '+ action.name
												, style: 	'border-radius: 1.5em !important;'+
															'margin-top: calc(25px - 1.25em) !important;'+
															'background-color: white;'
											}
										).notify('click', {
											type: 'workflows-run'
											, data: {
												run:  function () {
													
													view['a-'+ actionKey].btn.loading();

													if (action.action.length === 3) {

														action.action(node, direction, function (response) {
															viewWorkflowEditorBeta(View, State, draw);
														});


													} else if (action.action.length === 4) {

														action.action(view, node, direction, function (response) {
															viewWorkflowEditorBeta(View, State, draw);
														});

													}
													
												}.bind(action)
											}
										}, sb.moduleId);

										view.patch();

									});

								}, btnId);
								return;

							});
						
						// Sync the top scrollbar with the native scrollbar
						$('#workflow-grid').scroll(function () {
							$('#wfEditTopScrollbar').scrollLeft(
								$('#workflow-grid').scrollLeft()
							);
						});

					}
				);

			viewGrid(
				ui.g
				, Grid
				, draw
			);
			viewDetail(ui.g.makeNode('detail', 'div', {
				id: 'workflow-popup'
				, css: 'ui raised basic segment workflow-popup hidden'
			}));
			ui.patch();

			sb.notify ({
				type: 'view-field'
				, data: {
					type: 'tags'
					, property: 'tagged_with'
					, obj: state.pageObject
					, options: {}
					, ui: ui.tagsArea
				}
			});

		});

	}

	return {
		
		init:function(){

			sb.listen({
				
				///state steps full display single view
				'show-workflow-states-view':		this.showWorkflowStatesView,
				'show-workflow-view':			this.showWorkflowView,
				'workflows-run':					this.run,
				
				// open api (pure funcs)
				'update-state-flow': 			this.updateStateFlow,
				'view-workflow': 				this.viewWorkflow
				
			});
			
			sb.notify({
				type: 'register-application'
				, data: {
					navigationItem: {
						moduleId: 		sb.moduleId,
						instanceId: 		sb.instanceId,
						id: 				'workflows',
						title: 			'Workflows',
						icon: 			'<i class="ui random icon"></i>',
						afterLoad: 		function(state) {},
						views: [
							// Single object view
							{
								id:		'entity_workflow-obj',
								type:	'object-view',
								title:	'Workflow',
								icon: 	{type:'random', color:'blue'},
								mainViews: [
									{
										dom: function (dom, state, draw) {
											
											try {
												
												// Create tabs for current + beta versions
												var style = sb.dom.isMobile ? 'overflow-x:scroll' : '';
												var tabs = dom.makeNode('tabs', 'div', {
													css: 'ui top attached tabular menu',
													style: style
												});
												
												$(document).ready(function() {
													$('.tabular.menu .item').tab();
												});
												
											
												// ============================= //
												// ============ TABS =========== //
												// ============================= //
												
												// Create tab
												tabs.makeNode('current', 'div', {
													css: 'ui item active',
													text: '<i class="ui list icon"></i>',
													data: {
														'data-tab': 'current'
													}
												});

												// Create tab container
												var currentEditorContainer = dom.makeNode('settingsTabContainer', 'div', {
													css:'ui bottom attached active tab segment',
													data: {
														'data-tab': 'current'
													}
												});

												// Create tab
												tabs.makeNode('beta', 'div', {
													css: 'ui item',
													text: '<i class="ui project diagram icon"></i> <div class="ui teal label">Beta</div>',
													data: {
														'data-tab': 'beta'
													}
												});
												
												// Create tab container
												var betaEditorContainer = dom.makeNode('betaTabContainer', 'div', {
													css:'ui bottom attached tab segment',
													data: {
														'data-tab': 'beta'
													}
												});
												
												dom.patch();

												// ============================= //
												// ========== CONTENT ========== //
												// ============================= //	
												
												// Current
												viewWorkflowEditor(
													currentEditorContainer
													, state
													, draw
												);

												// Beta
												viewWorkflowEditorBeta(
													betaEditorContainer
													, state
													, draw
												);
												
											} catch (e) {
												
												console.log(e);
												
											}
											
										}
									}
								],
								select: {
									name: 			true
									, description: 	true
									, states: 		true
								}
							}
							// Workflows tool (type collection)
							, {
								id: 	'workflows',
								type: 	'custom',
								name: 	'Workflows',
								title: 	'Workflows',
								tip: 	'Manage processes.',
								icon: 	{
									type: 'tasks',
									color: 'teal'
								},
								default:true,
								settings: false,
								newButton: {
									text: 'New Workflow',
									action: function () {}
								},
								mainViews: [{
									dom: function (dom, state, draw, mainDom, options) {
										
										var collSetup = build_workflows_collection(
											dom
											, state
											, draw
											, mainDom
											, options
										);
										
										sb.notify({
											type: 		'show-collection'
											, data: 	collSetup
										});

									}
								}],
								boxViews: []
							}
						]
					}
				}
			});
			
		},
		
		showWorkflowStatesView:function(setup){

			statesView(setup.dom, setup.state);
			
		},
		
		showWorkflowView:function(setup){
			
			sb.listen({
				'show-workflow-view':this.showWorkflowView,
				'update-workflow-view':this.updateWorkflowView
			});
			
			ui = workflow_ui(setup.ui, setup);
			
		},
		
		run:function(data){ if(sb.instanceId === undefined){ data.run(data); } },
		
		updateWorkflowView:function(setup){
		// !testing
// 		var stages = ['qualification', 'analysis', 'proposal', 'decision', 'wrap-up'];
// 		setup.stage = stages[Math.floor(Math.random()*stages.length)];

			ui.update(setup);
			
		},
		
		updateStateFlow: function (data) {
			
			if (sb.instanceId == undefined) {
				updateStateFlow(data);
			}
			
		},
		
		viewWorkflow: function (data) {
			
			if (sb.instanceId == undefined) {
				
				/*
viewWorkflowEditor(
					data.
				) ;
*/
				
			}
			
		}
		
	}
	
});
