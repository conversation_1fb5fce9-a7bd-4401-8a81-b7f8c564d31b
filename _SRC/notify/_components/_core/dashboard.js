Factory.register("dashboard-tool", function (sb) {
  var mainDom = {};
  var mainState = {};
  var mainDraw = {};
  var mainGroup = {};
  var mainGroupType = "";

  //var boxViewPositions = false;
  var tools = [];
  var layoutId = 0;
  var boxViews = [];
  var noBoxViews = [];
  var noMainViews = [];
  var systemTool = {};
  var icons = [];
  var href = "";
  var order = [
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "ten",
    "eleven",
    "twelve",
    "thirteen",
    "fourteen",
    "fifteen",
    "sixteen",
  ];
  var position = 0;
  var layout = false;
  var grid = {};
  var gridSetup = {};
  var AppViews = [];
  var NewAppViews = [];

  function appsBoxView(dom, state, draw, callback) {
    var centerMenu = "";
    var groupObject = {};
    var groupObjectType = "";

    if ($(window).width() <= 768) {
      centerMenu = " justify-content:center;";
    }

    $(dom.selector).addClass("center aligned");

    dom.makeNode("cards", "div", {
      css: "ui small labeled icon secondary menu center aligned",
      style:
        "display: flex !important; flex-direction: row !important; flex-wrap: wrap !important;" +
        centerMenu,
    });

    if (state.hasOwnProperty("myStuff")) {
      groupObject = state.myStuff;
      groupObjectType = "myStuff";
    } else {
      if (state.hasOwnProperty("pageObject")) {
        groupObject = state.pageObject;

        switch (state.pageObject.group_type) {
          case "Project":
            groupObjectType = "project";

            break;

          case "Team":
            groupObjectType = "team";

            break;

          default:
            groupObjectType = "hq";
        }
      }
    }

    var linkArgs =
      groupObject.group_type == "Project"
        ? "project-tools"
        : groupObjectType + "Tool";

    var appsToolList = _.uniq(tools.concat(noBoxViews), "id");

    _.each(_.sortBy(appsToolList, "name"), function (tool) {
      if (tool.id != "dashboardtool" && tool.id != "hqTool") {
        var linkOpts = {
          tool: tool.id,
        };

        if (mainGroup.group_type == "Project") {
          linkOpts = {
            tool: tool.id,
            startAt: sb.data.url.createPageURL("object", {
              id: mainGroup.id,
              name: mainGroup.name,
              type: "project",
            }),
          };
        }

        href = sb.data.url.createPageURL(linkArgs, linkOpts);

        dom.cards.makeNode("card-" + tool.id, "div", {
          css: "ui item center aligned",
          text:
            '<i class="' +
            tool.icon.color +
            " " +
            tool.icon.type +
            ' icon"></i>' +
            tool.name,
          href: href,
          tag: "a",
          style: "width:15%;margin:5px 2%;",
        });
      }
    });

    if (sb.permissions.isGroupManager(+sb.data.cookie.userId, groupObject)) {
      dom.cards
        .makeNode("settings", "div", {
          css: "ui item",
          text: '<i class="grey cogs icon disabled"></i> Apps',
          style: "width:15%;margin:5px 2%;",
          tag: "a",
          href: "",
        })
        .notify(
          "click",
          {
            type: "dashboard-tool-run",
            data: {
              run: function (dom, state, groupObj) {
                editGroupTools(
                  dom,
                  groupObject,
                  groupObjectType,
                  function (updatedGroup) {
                    groupObject = updatedGroup;

                    callback();
                  }
                );
              }.bind({}, dom, state, groupObject),
            },
          },
          sb.moduleId
        );
    }

    draw(dom);
  }

  function displaySectionMenu(dom, tools, layout, callback, refresh) {
    // Initialize variables
    AppViews = [];

    // Set variables
    var isRightTray = layout.is_rightTray;
    var rowId = 0;

    // Set app views
    if (isRightTray) {
      AppViews = layout.layout;
    } else {
      _.each(layout.layout, function (boxView) {
        AppViews.push({
          boxView: boxView.boxView,
          tool: boxView.tool,
          uid: boxView.uid,
          toolId: false,
        });
      });
    }

    var toolsWithBoxViews = _.filter(tools, function (t) {
      return (
        t.hasOwnProperty("boxViews") &&
        t.boxViews.length > 0 &&
        t.id !== "dashboardtool"
      );
    });

    dom.makeNode("grid", "div", {
      css: "ui equal width relaxed internally celled stackable grid",
    });

    _.each(_.sortBy(toolsWithBoxViews, "name"), function (tool, count) {
      var toolIDClassName = tool.id.replace(".", "-");

      var onScreen = 0;

      if (count % 4 == 0) {
        rowId++;
        dom.grid.makeNode("row" + rowId, "div", {
          css: "row",
        });
      }

      var appViewDomColumn = dom.grid["row" + rowId].makeNode(
        "col-" + toolIDClassName,
        "div",
        { css: "column" }
      );
      appViewDomColumn.makeNode("title", "div", {
        css: "ui small header",
        text: tool.name,
      });

      _.each(_.sortBy(tool.boxViews, "title"), function (view) {
        var viewIDClassName = view.id.replace(".", "-");

        onScreen = _.where(layout.layout, { boxView: view.id }).length;

        var appViewDomColumnView = appViewDomColumn.makeNode(
          "view-" + viewIDClassName,
          "div",
          {
            tag: "a",
            css: "ui label",
            text: view.title + " " + onScreen,
          }
        );

        appViewDomColumnView.onScreen = onScreen;

        appViewDomColumn.makeNode("break-" + viewIDClassName, "div", {
          text: "<br />",
        });

        appViewDomColumnView.notify(
          "click",
          {
            type: "dashboard-tool-run",
            data: {
              run: function (dom, boxView, tool, rowId) {
                $(appViewDomColumnView.selector).html(
                  "" +
                    boxView.title +
                    ' <i class="ui fitted loading notched circle icon"></i>'
                );

                var viewUid = sb.dom.randomString(
                  6,
                  "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
                );

                if (!boxView.hasOwnProperty("tool")) {
                  boxView.tool = {};
                  boxView.tool.id = "dashboardtool";
                }

                AppViews.push({
                  boxView: boxView.id,
                  tool: tool.id,
                  uid: viewUid,
                  toolId: tool.toolId,
                });

                NewAppViews.push({
                  boxView: boxView.id,
                  tool: tool.id,
                  uid: viewUid,
                  toolId: tool.toolId,
                });

                appViewDomColumnView.onScreen++;

                $(appViewDomColumnView.selector).html(
                  "" + boxView.title + " " + appViewDomColumnView.onScreen
                );
              }.bind({}, dom, view, tool, rowId),
            },
          },
          sb.moduleId
        );
      });
    });

    displaySectionMenu.refresh = function (tools) {
      displaySectionMenu(dom, tools, layout, callback, true);
    };

    if (refresh) {
      dom.patch();
    }
  }

  function editGroupTools(dom, groupObject, groupObjectType, callback) {
    dom.makeNode("modal", "modal", {
      onShow: function () {
        dom.modal.body.makeNode("title", "div", {
          text: "Manage Apps",
          css: "ui huge header",
        });

        dom.modal.body.makeNode("divider", "div", { css: "ui divider" });

        dom.modal.body.makeNode("cont", "div", {
          css: "ui eight stackable cards",
        });

        // filter out admin
        var availableTools = filterAdminOnlyViews(
          appConfig[groupObjectType + "Tools"]
        );

        _.each(_.sortBy(availableTools, "name"), function (tool) {
          if (tool.id != "dashboardtool" && tool.id != "hqTool") {
            var addButtonColor = "green";
            var addButtonText = "Add";
            var removeColor = "";
            var archive = 0;
            var objectTool = _.findWhere(groupObject.tools, {
              system_name: tool.id,
            });

            if (objectTool) {
              if (objectTool.is_archieved != 1) {
                removeColor = "green";
                addButtonColor = "";
                addButtonText = "Remove";
                archive = 1;
              }
            }

            dom.modal.body.cont.makeNode("tool-" + tool.id, "div", {
              css: "ui card",
            });

            dom.modal.body.cont["tool-" + tool.id].makeNode("content", "div", {
              css: "content",
            });

            dom.modal.body.cont["tool-" + tool.id].content.makeNode(
              "break0",
              "div",
              { text: "<br />" }
            );

            dom.modal.body.cont["tool-" + tool.id].content.makeNode(
              "icon",
              "div",
              {
                css: "center aligned",
                text:
                  '<i class="ui large ' +
                  tool.icon.color +
                  " " +
                  tool.icon.type +
                  ' icon"></i>',
              }
            );

            dom.modal.body.cont["tool-" + tool.id].content.makeNode(
              "break",
              "div",
              { text: "<br />" }
            );

            dom.modal.body.cont["tool-" + tool.id].content.makeNode(
              "header",
              "div",
              {
                css: "center aligned header",
                text: tool.name,
              }
            );

            dom.modal.body.cont["tool-" + tool.id].content.makeNode(
              "desc",
              "div",
              {
                css: "center aligned description",
                text: tool.tip,
              }
            );

            dom.modal.body.cont["tool-" + tool.id].content.makeNode(
              "break2",
              "div",
              { text: "<br />" }
            );

            dom.modal.body.cont["tool-" + tool.id]
              .makeNode("select", "div", {
                css: "ui bottom attached " + addButtonColor + " button",
                text: addButtonText,
                style: "display:block;margin:0 auto;",
              })
              .notify(
                "click",
                {
                  type: "dashboard-tool-run",
                  data: {
                    run: function (
                      dom,
                      tool,
                      archive,
                      removeColor,
                      addButtonColor
                    ) {
                      var buttonText = "Remove";

                      if (archive == 1) {
                        buttonText = "Add";
                      }

                      $(
                        dom.modal.body.cont["tool-" + tool.id].select.selector
                      ).addClass("loading disabled");

                      if (
                        _.findWhere(groupObject.tools, { system_name: tool.id })
                      ) {
                        _.findWhere(groupObject.tools, {
                          system_name: tool.id,
                        }).is_archieved = archive;
                      } else {
                        groupObject.tools.push({
                          allowed_users: [+sb.data.cookie.get("uid")],
                          system_name: tool.id,
                          display_name: tool.name,
                          is_archieved: 0,
                          order: groupObject.tools.length + 1,
                          added_by: +sb.data.cookie.get("uid"),
                          added_on: moment(),
                          settings: {},
                          box_color: tool.icon.color,
                        });
                      }

                      sb.data.db.obj.update(
                        "groups",
                        {
                          id: groupObject.id,
                          tools: groupObject.tools,
                        },
                        function (done) {
                          $(
                            dom.modal.body.cont["tool-" + tool.id].select
                              .selector
                          ).removeClass("loading disabled " + addButtonColor);

                          $(
                            dom.modal.body.cont["tool-" + tool.id].select
                              .selector
                          ).addClass(removeColor);

                          $(
                            dom.modal.body.cont["tool-" + tool.id].select
                              .selector
                          ).text(buttonText);

                          sb.notify({
                            type: "app-redraw",
                            data: {},
                          });
                        }
                      );
                    }.bind({}, dom, tool, archive, removeColor, addButtonColor),
                  },
                },
                sb.moduleId
              );
          }
        });

        dom.modal.patch();
      },
    });

    dom.patch();

    dom.modal.show();
  }

  // Views
  function dashboardView(dom, state, draw, callback, appViews, newAppViews) {
    tools = [];
    layoutId = 0;
    boxViews = [];
    noBoxViews = [];
    systemTool = {};
    icons = [];
    href = "";
    position = 0;
    layout = false;
    // grid = {};
    gridSetup = {
      data: {
        assignee: {},
        date: {},
        refresh: function () {
          false;
        },
      },
      accepts: true,
      moves: false,
    };

    // Set variables
    var isRightTray = state.hasOwnProperty("is_rightTray")
      ? state.is_rightTray
      : 0;

    // Initialize variables
    var isPublic = false;
    var layoutsBuilder = false;
    var layoutsObjId = false;

    if (state.hasOwnProperty("pageObjectType")) {
      if (state.pageObjectType == "layouts") {
        layoutsBuilder = true;
        layoutsObjId = state.id;
      }
    } else if (state.hasOwnProperty("layout_id")) {
      layoutsBuilder = true;
      layoutsObjId = state.id;
    }

    // In client portal
    if (
      (appConfig && appConfig.state && appConfig.state.portal) ||
      state.portal
    ) {
      isPublic = true;
    }

    if (state.viewOnly === true) {
      isPublic = true;
    }

    function addDashboard(dom, tool, groupTools) {
      if (tool == "blank") {
        var mainTool = {
          id: "blank",
          name: "New Dashboard",
        };
      } else {
        var mainTool = _.findWhere(tools, { id: tool });

        _.each(groupTools, function (otherTool) {
          var toolObj = _.findWhere(
            _.reject(tools, function (obj) {
              ret = false;

              if (obj.id == "dashboardtool") {
                ret = true;
              }

              if (obj.id == "entityTool") {
                ret = true;
              }

              return ret;
            }),
            { id: otherTool }
          );

          if (toolObj) {
            if (otherTool != tool) {
              $(
                dom.modal.body.cards["card" + otherTool].extraContent2.btn
                  .selector
              ).addClass("disabled");
            }
          }
        });
      }

      dom.modal.body.cards["card" + tool].extraContent2.btn.loading();

      if (tool != "blank")
        $(
          dom.modal.body.cards["cardblank"].extraContent2.btn.selector
        ).addClass("disabled");

      var mainGroup = {};
      var mainGroupType = "";
      var pageObjectId = mainGroup.id;

      if (state.hasOwnProperty("myStuff")) {
        mainGroup = state.myStuff;
        mainGroupType = "myStuff";
        pageObjectId = mainGroup.id;
        //layoutId = +sb.data.cookie.userId;
      } else if (state.hasOwnProperty("pageObject")) {
        mainGroup = state.pageObject;
        pageObjectId = mainGroup.id;

        // if pageObject is a set, use the setId property
        if (state.hasOwnProperty("setId")) {
          pageObjectId = state.setId;
        }

        switch (state.pageObject.group_type) {
          case "JobType":
            mainGroupType = "jobType";

            break;

          case "Project":
            mainGroupType = "project";

            break;

          case "Team":
            mainGroupType = "team";

            break;

          default:
            mainGroupType = "hq";
        }
      }

      var newLayout = {
        layout_id: mainGroup.id.toString(),
        name: mainTool.name,
        box_links: 1,
      };

      if (state.setId) {
        newLayout.set_layout_id = state.setId;
      }

      if (appConfig.state && appConfig.state.portal) {
        newLayout.is_public = 1;
      }

      sb.data.db.obj.create("layouts", newLayout, function (newLayout) {
        getLayout(
          state,
          function (newLayout) {
            dom.modal.hide();

            displayLayoutsMenu.call(state, dom, newLayout, function () {
              buildBoxes(grid, newLayout, function () {
                sb.notify({
                  type: "dashboard-tool-save-boxview",
                  data: {
                    dropped: {
                      grid: grid,
                    },
                    callback: function (newLayout) {},
                  },
                });
              });
            });
          },
          newLayout.id,
          mainTool
        );
      });
    }

    function buildBoxes(dom, layout, callback, appViews, newAppViews) {
      var isRightTray = layout.is_rightTray;
      var groupOwner = sb.permissions.isGroupManager(
        +sb.data.cookie.userId,
        mainGroup
      );
      var groupMember = sb.permissions.isGroupMember(
        +sb.data.cookie.userId,
        mainGroup
      );
      var totalLayouts = _.compact(layout.layout).length;
      var canView = isRightTray ? true : false;

      function orderBoxViews(list) {
        ///pin workflows to position [0] if found ('Project')
        var wfIndex = _.findIndex(list, {
          boxView: "workflowsApp",
        });

        if (wfIndex > 0) {
          ///remove item at index
          var wfItem = list.splice(wfIndex, 1);

          ///place into [0] of layout array
          list.unshift(wfItem[0]);
        }

        list = _.reject(list, function (boxview) {
          return boxview.tool === undefined;
        });

        _.each(list, function (boxview) {
          var tool = boxview.tool;

          if (tool !== undefined) {
            _.each(tool.boxViews, function (toolBoxview) {
              if (toolBoxview.hasOwnProperty("position")) {
                list = _.filter(list, function (b) {
                  return b.uid !== boxview.uid;
                });

                list.splice(toolBoxview.position, 0, boxview);
              }
            });
          }
        });

        return list;
      }

      if (!isRightTray) {
        if (groupOwner) {
          canView = true;
        } else {
          if (groupMember) {
            canView = true;
          } else {
            if (layout.is_public == 1) {
              canView = true;
            }
          }
        }
      }

      var orderedList = orderBoxViews(layout.layout);

      dom.empty();

      if (!_.isEmpty(orderedList) && totalLayouts > 0 && canView) {
        _.each(orderedList, function (boxView) {
          if (boxView && boxView.boxView !== "dashboardApps") {
            if (boxView.tool) {
              var systemBoxView = _.findWhere(boxViews, {
                id: boxView.boxView,
              });
              var settings = _.findWhere(layout.settings, { uid: boxView.uid });

              if (systemBoxView) {
                if (layout.box_links === 0) {
                  systemBoxView.boxLink = false;
                } else {
                  if (systemBoxView && systemBoxView.boxLink != 0) {
                    systemBoxView.boxLink = true;
                  }
                }

                systemBoxView.settings = settings;

                buildSingleBox(
                  dom,
                  systemBoxView,
                  boxView,
                  layout,
                  appViews,
                  newAppViews
                );
              }
            }
          }
        });
      } else {
        dom.empty();

        if (!isRightTray) {
          dom.makeNode("col", "div", { css: "ui sixteen wide column" });
          dom.col.makeNode("placeholder", "div", {
            css: "ui placeholder segment",
          });
          dom.col.placeholder.makeNode("header", "div", {
            css: "ui center aligned icon header",
          });

          if (groupOwner) {
            dom.col.placeholder.header.makeNode("icon", "div", {
              css: "ui pdf plus icon",
              tag: "i",
            });
            dom.col.placeholder.header.makeNode("content", "div", {
              css: "content",
              text: "Add a new App view",
            });
            dom.col.placeholder.makeNode("break", "div", { text: "<br />" });

            displaySectionMenu(
              dom.col.placeholder,
              mainGroup.tools,
              layout,
              function (updated) {
                getLayout(
                  state,
                  function (newLayout) {
                    displayLayoutsMenu(dom, newLayout[0], function () {
                      buildBoxes(grid, newLayout[0], function () {});
                    }).bind(state);
                  },
                  updated.id
                );
              }
            );
          } else {
            if (groupMember) {
              dom.col.placeholder.header.makeNode("icon", "div", {
                css: "ui th icon",
                tag: "i",
              });
              dom.col.placeholder.header.makeNode("content", "div", {
                css: "content",
                text: "No app views have been added to this dashboard.",
              });
              dom.col.placeholder.makeNode("content", "div", {
                css: "ui center aligned",
                style: "text-align:center;",
                text: "Contact the item owner and ask them to create a dashboard.",
              });
              dom.col.placeholder.makeNode("break", "div", { text: "<br />" });
            } else {
              dom.col.placeholder.header.makeNode("icon", "div", {
                css: "ui red dont icon",
                tag: "i",
              });
              dom.col.placeholder.header.makeNode("content", "div", {
                css: "content",
                text: "You don't have access to this item.",
              });
              dom.col.placeholder.makeNode("content", "div", {
                css: "ui center aligned",
                style: "text-align:center;",
                text: "Click the button below to request access from the admins.",
              });
              dom.col.placeholder.makeNode("break", "div", { text: "<br />" });
              dom.col.placeholder
                .makeNode("button", "div", {
                  text: "Request View Access",
                  css: "ui blue button",
                })
                .notify(
                  "click",
                  {
                    type: "dashboard-tool-run",
                    data: {
                      run: function (dom, mainGroup) {
                        dom.col.placeholder.button.loading();

                        requestGroupAccess(mainGroup, function (emailSent) {
                          sb.dom.alerts.alert(
                            "Success!",
                            "You will be notified when access is granted.",
                            "success"
                          );

                          dom.col.placeholder.button.loading(false);
                        });
                      }.bind({}, dom, mainGroup),
                    },
                  },
                  sb.moduleId
                );
            }
          }
        }
      }

      dom.patch();

      if (!_.isEmpty(orderedList) && totalLayouts > 0 && canView) {
        _.each(orderedList, function (position, count) {
          if (position && position.boxView !== "dashboardApps") {
            if (position.tool) {
              var view = _.findWhere(boxViews, { id: position.boxView });
              var settings = _.findWhere(layout.settings, {
                uid: position.uid,
              });

              if (!settings) {
                settings = {};
              }

              settings.uid = position.uid;
              settings.toolId = parseInt(position.toolId) || false;

              if (view) {
                view.settings = settings;
                view.toolDefaultOpt = position.tool.icon;
                view.toolDefaultOpt.tip = position.tool.tip;

                if (!isRightTray) {
                  var boxViewToolIDClassName = position.tool.id.replace(
                    ".",
                    "-"
                  );
                  var boxViewUIDClassName = position.uid.replace(".", "-");

                  dom[
                    "view-" + boxViewToolIDClassName + "-" + boxViewUIDClassName
                  ].card.content.empty();
                }
              }
            }
          }
        });
      }

      if (callback) {
        callback();
      }
    }

    function buildSingleBox(
      dom,
      systemBoxView,
      boxView,
      layout,
      appViews,
      newAppViews
    ) {
      var isRightTray = layout.is_rightTray;
      var settings = _.findWhere(layout.settings, { uid: boxView.uid });
      systemBoxView.settings = settings;

      var width = systemBoxView.width;
      var linkArgs =
        mainGroup.group_type == "Project"
          ? "project-tools"
          : mainGroupType + "Tool";

      var linkOpts = {
        tool: boxView.tool.id,
      };

      var boxViewDataAttr = [
        {
          name: "uid",
          value: boxView.uid,
        },
        {
          name: "boxid",
          value: boxView.boxView,
        },
        {
          name: "toolid",
          value: boxView.tool.id,
        },
        {
          name: "grouptoolid",
          value: boxView.toolId,
        },
        {
          name: "layoutid",
          value: layout.id,
        },
      ];

      var copiedState = JSON.parse(JSON.stringify(state));
      copiedState.boxView = systemBoxView;

      if (mainGroup.group_type == "Project") {
        linkOpts = {
          tool: boxView.tool.id,
          startAt: sb.data.url.createPageURL("object", {
            id: mainGroup.id,
            name: mainGroup.name,
            type: "project",
          }),
        };
      }

      if (settings) {
        if (settings.width) {
          width = settings.width;
        }
      }

      var name = systemBoxView.title;

      if (settings) {
        if (settings.name) {
          name = settings.name;
        }
      }

      if (boxView.tool.allowMulti && boxView.toolId) {
        var associatedTool = _.findWhere(mainGroup.tools, {
          id: parseInt(boxView.toolId),
        });

        if (associatedTool && associatedTool.settings) {
          linkOpts.name = associatedTool.settings.name;
          linkOpts.toolId = associatedTool.id;
        }
      }

      if (systemBoxView && typeof systemBoxView.link === "function") {
        href = systemBoxView.link(copiedState);
      } else {
        href = sb.data.url.createPageURL(linkArgs, linkOpts);
      }

      if (!boxView.uid) {
        boxView.uid = sb.dom.randomString(
          6,
          "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
        );
      }

      boxView.layout = layout;
      boxView.grid = dom;
      boxView.width = width;

      // Clone the system box view
      var currSystemBoxView = _.clone(systemBoxView);

      var boxViewToolIDClassName = boxView.tool.id.replace(".", "-");
      var boxViewUIDClassName = boxView.uid.replace(".", "-");

      if (isRightTray) {
        boxView.boxDom = dom
          .makeNode(
            "view-" + boxViewToolIDClassName + "-" + boxViewUIDClassName,
            "div",
            {
              text:
                '<i class="fal fa-' +
                sb.dom.iconsSemanticToFontAwesome(boxView.tool.icon.type) +
                ' fa-2x"></i>',
              css: "rightTrayBoxViewIcon",
              style: "padding-top:12px; padding-bottom:12px; cursor:pointer;",
              id: boxView.uid,
              listener: {
                type: "popup",
                hoverable: true,
                delay: {
                  show: 0,
                  hide: 0,
                },
              },
              tooltip: {
                title: name,
                text: "",
                position: "left center",
              },
              dataAttr: boxViewDataAttr,
            }
          )
          .notify("click", {
            type: "dashboard-tool-run",
            data: {
              run: function () {
                sb.data.db.obj.getById(
                  "layouts",
                  boxView.layout.id,
                  function (layout) {
                    // Grab the latest settings
                    currSystemBoxView.settings = _.findWhere(layout.settings, {
                      uid: boxView.uid,
                    });

                    // Show the app view in right tray
                    showAppViewInRightTray(boxView, currSystemBoxView);
                  }
                );
              },
            },
          });

        if (newAppViews) {
          if (newAppViews.length === 1) {
            if (newAppViews[0].uid === boxView.uid) {
              sb.data.db.obj.getById(
                "layouts",
                boxView.layout.id,
                function (layout) {
                  // Grab the latest settings
                  currSystemBoxView.settings = _.findWhere(layout.settings, {
                    uid: boxView.uid,
                  });

                  // Show the app view in right tray
                  showAppViewInRightTray(boxView, currSystemBoxView);
                }
              );
            }
          }
        }
      } else {
        // Show the app view
        sb.notify({
          type: "show-app-view",
          data: {
            dom: dom,
            boxView: boxView,
            systemBoxView: currSystemBoxView,
            state: state,
          },
        });
      }
    }

    function displayLayoutsMenu(dom, layout, callback) {
      var state = this;
      var queryObj = {
        is_rightTray: 0,
      };

      if (state.setId) {
        queryObj.set_layout_id = state.setId;
      } else {
        queryObj.layout_id = layout.layout_id;
        queryObj.set_layout_id = "";
      }

      if (layoutsBuilder) queryObj = { id: layout.id };

      // In a client portal
      if (isPublic) {
        queryObj.is_public = 1;
      }

      sb.data.db.obj.getWhere("layouts", queryObj, function (layouts) {
        var css = "";
        var currentLayout = {};
        var publicIcon = "";
        var groupOwner = sb.permissions.isGroupManager(
          +sb.data.cookie.userId,
          mainGroup
        );
        var groupMember = sb.permissions.isGroupMember(
          +sb.data.cookie.userId,
          mainGroup
        );
        var itemsOnScreen = 0;
        var groupTools = _.uniq(_.pluck(mainGroup.tools, "system_name"));
        var selectedLayout = layouts[0];

        dom.empty();

        _.each(layouts, function (item, i) {
          if (item.id == layout.id) {
            css = "active item";
            currentLayout = item;
          } else {
            css = "item";
          }

          publicIcon = '<i class="eye slash icon"></i> ';

          if (item.is_public && groupOwner && !appConfig.is_portal) {
            publicIcon = '<i class="eye icon"></i> ';
          }

          if (groupMember || item.is_public) {
            itemsOnScreen++;

            dom
              .makeNode("layout-" + item.id, "div", {
                css: css,
                text: publicIcon + item.name,
                tag: "a",
              })
              .notify(
                "click",
                {
                  type: "dashboard-tool-run",
                  data: {
                    run: function (selectedLayout) {
                      $(dom["layout-" + item.id].selector).addClass("disabled");

                      getLayout(
                        state,
                        function (newLayout) {
                          displayLayoutsMenu.call(
                            state,
                            dom,
                            newLayout,
                            function () {
                              buildBoxes(grid, newLayout, function () {});
                            }
                          );
                        },
                        selectedLayout.id
                      );
                    }.bind({}, item),
                  },
                },
                sb.moduleId
              );
          }
        });

        if (
          (sb.permissions.isGroupManager(+sb.data.cookie.userId, mainGroup) &&
            !isPublic) ||
          layoutsBuilder ||
          state.editDashboard
        ) {
          dom
            .makeNode("new", "div", {
              css: "item",
              text: '<i class="ui plus icon"></i> Dashboard',
              tag: "a",
              href: "",
            })
            .notify(
              "click",
              {
                type: "dashboard-tool-run",
                data: {
                  run: function (dom, groupTools) {
                    $(dom.new.selector).addClass("disabled");
                    $(dom.new.selector).html(
                      '<i class="ui circle notched loading icon"></i> Creating'
                    );

                    dom.makeNode("modal", "modal", {
                      onShow: function () {
                        dom.modal.body.makeNode("title", "div", {
                          text: "Choose a dashboard",
                          css: "ui huge header",
                        });
                        dom.modal.body.makeNode("desc", "div", {
                          text: "Select an application dashboard to create a dashboard devoted to that app. Use the Blank dashboard to create your own.",
                        });
                        dom.modal.body.makeNode("divider", "div", {
                          css: "ui divider",
                        });
                        dom.modal.body.makeNode("break", "div", {
                          text: "<br />",
                        });

                        dom.modal.body.makeNode("cards", "div", {
                          css: "ui eight cards",
                        });

                        dom.modal.body.cards
                          .makeNode("cardblank", "div", {
                            css: "ui black card",
                          })
                          .makeNode("content", "div", { css: "content" });
                        dom.modal.body.cards.cardblank.content.makeNode(
                          "header",
                          "div",
                          {
                            css: "header center aligned",
                            text: '<i class="ui fitted dot circle outline icon"></i> Blank',
                          }
                        );
                        dom.modal.body.cards.cardblank.makeNode(
                          "extraContent",
                          "div",
                          { css: "extra content center aligned" }
                        );
                        dom.modal.body.cards.cardblank.extraContent.makeNode(
                          "placeholder",
                          "div",
                          {
                            tag: "img",
                            css: "ui image",
                            src: "https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/app/placeholder.png",
                          }
                        );
                        dom.modal.body.cards.cardblank.makeNode(
                          "extraContent2",
                          "div",
                          { css: "extra content center aligned" }
                        );
                        dom.modal.body.cards.cardblank.extraContent2
                          .makeNode("btn", "div", {
                            css: "ui mini basic green button",
                            text: "Add",
                          })
                          .notify(
                            "click",
                            {
                              type: "dashboard-tool-run",
                              data: {
                                run: addDashboard.bind(
                                  {},
                                  dom,
                                  "blank",
                                  groupTools
                                ),
                              },
                            },
                            sb.moduleId
                          );

                        _.each(groupTools, function (tool) {
                          var toolObj = _.findWhere(
                            _.reject(tools, function (obj) {
                              ret = false;

                              if (obj.id == "dashboardtool") ret = true;

                              if (obj.id == "entityTool") ret = true;

                              return ret;
                            }),
                            { id: tool }
                          );

                          if (toolObj && toolObj.boxViews) {
                            dom.modal.body.cards
                              .makeNode("card" + tool, "div", {
                                css: "ui card",
                              })
                              .makeNode("content", "div", { css: "content" });
                            dom.modal.body.cards[
                              "card" + tool
                            ].content.makeNode("header", "div", {
                              css: "header center aligned",
                              text:
                                '<i class="ui fitted ' +
                                toolObj.icon.color +
                                " " +
                                toolObj.icon.type +
                                ' icon"></i> ' +
                                toolObj.name,
                            });
                            dom.modal.body.cards["card" + tool].makeNode(
                              "extraContent",
                              "div",
                              { css: "extra content center aligned" }
                            );
                            dom.modal.body.cards[
                              "card" + tool
                            ].extraContent.makeNode("placeholder", "div", {
                              tag: "img",
                              css: "ui image",
                              src: "https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/app/placeholder.png",
                            });
                            dom.modal.body.cards["card" + tool].makeNode(
                              "extraContent2",
                              "div",
                              { css: "extra content center aligned" }
                            );
                            dom.modal.body.cards["card" + tool].extraContent2
                              .makeNode("btn", "div", {
                                css: "ui mini basic green button",
                                text: "Add",
                              })
                              .notify(
                                "click",
                                {
                                  type: "dashboard-tool-run",
                                  data: {
                                    run: addDashboard.bind({}, dom, tool),
                                  },
                                },
                                sb.moduleId
                              );
                          }
                        });

                        dom.modal.patch();
                      },
                    });

                    dom.patch();

                    dom.modal.show();
                  }.bind({}, dom, groupTools),
                },
              },
              sb.moduleId
            );

          dom.makeNode("rightMenu", "div", { css: "right menu" });

          dom.rightMenu.makeNode("settings", "div", {
            css: "ui item",
            style: "padding-right:0",
            text: '<i class="ui cog icon"></i> Settings',
            tag: "a",
            href: "",
            tooltip: {
              title: "Settings",
              text: "View Dashboard settings.",
              position: "left center",
            },
            listener: {
              type: "popup",
              hoverable: true,
            },
          });

          dom.rightMenu
            .makeNode("editLayout", "div", {
              css: "ui simple dropdown item",
              style: "padding-right:0;",
              text: '<i class="plus icon"></i> App Views',
            })
            .notify(
              "click",
              {
                type: "dashboard-tool-run",
                data: {
                  run: function (data) {
                    var state = this;

                    // Show app view picker
                    sb.notify({
                      type: "show-app-view-picker",
                      data: {
                        ui: dom.rightMenu,
                        state: state,
                        layout: layout,
                        callback: function (layout) {
                          var currentTabLayout = layout;

                          getLayout(
                            state,
                            function (layouts) {
                              currentTabLayout = _.findWhere(layouts, {
                                id: currentTabLayout.id,
                              });

                              displayLayoutsMenu.call(
                                state,
                                dom,
                                currentTabLayout,
                                function (layout) {
                                  buildBoxes(grid, layout, function () {});
                                }
                              );
                            },
                            false
                          );
                        },
                      },
                    });
                  }.bind(state),
                },
              },
              sb.moduleId
            );
        } else if (!isPublic) {
          var accessLevel = "View";

          if (sb.permissions.isGroupMember(+sb.data.cookie.userId, mainGroup)) {
            accessLevel = "Admin";
          }

          dom.makeNode("rightMenu", "div", { css: "right menu" });

          dom.rightMenu
            .makeNode("settings", "div", { css: "item" })
            .makeNode("request", "div", {
              css: "",
              text: "Request " + accessLevel + " Access",
            })
            .notify(
              "click",
              {
                type: "dashboard-tool-run",
                data: {
                  run: function (dom, group) {
                    dom.rightMenu.settings.request.loading();

                    requestGroupAccess(
                      group,
                      function (done) {
                        sb.dom.alerts.alert(
                          "Success!",
                          "You will be notified when access is granted.",
                          "success"
                        );

                        dom.rightMenu.settings.request.loading(false);
                      },
                      accessLevel
                    );
                  }.bind({}, dom, mainGroup),
                },
              },
              sb.moduleId
            );
        }

        dom.makeNode("modals", "div", { css: "" });

        if (
          (sb.permissions.isGroupManager(+sb.data.cookie.userId, mainGroup) &&
            !isPublic) ||
          state.editDashboard
        ) {
          dom.rightMenu.settings.notify(
            "click",
            {
              type: "dashboard-tool-run",
              data: {
                run: function (dom, layout) {
                  dom.modals.makeNode("modal", "modal", {
                    onShow: function () {
                      var modal = dom.modals.modal;
                      var boxLinks = 1;

                      if (layout.box_links === 0) {
                        boxLinks = 0;
                      }

                      modal.body
                        .makeNode("delete", "div", {
                          css: "ui basic button right floated",
                          text: '<i class="ui trash icon"></i> Delete Dashboard',
                        })
                        .notify(
                          "click",
                          {
                            type: "dashboard-tool-run",
                            data: {
                              run: function (dom) {
                                sb.dom.alerts.ask(
                                  {
                                    title: "Are you sure?",
                                    text: "Are you sure you want to delete this dashboard?",
                                  },
                                  function (resp) {
                                    if (resp) {
                                      swal.disableButtons();

                                      $(modal.body.delete.selector).addClass(
                                        "disabled loading"
                                      );

                                      sb.data.db.obj.erase(
                                        "layouts",
                                        layout.id,
                                        function () {
                                          getLayout(
                                            state,
                                            function (newLayout) {
                                              var layout = newLayout[0]
                                                ? newLayout[0]
                                                : newLayout;

                                              displayLayoutsMenu(
                                                dom,
                                                layout,
                                                function () {
                                                  buildBoxes(
                                                    grid,
                                                    layout,
                                                    function () {
                                                      // Hide alert
                                                      swal.close();

                                                      // Hide modal
                                                      modal.hide();

                                                      // Show success
                                                      sb.notify({
                                                        type: "display-alert",
                                                        data: {
                                                          header:
                                                            "Dashboard removed successfully!",
                                                          color: "green",
                                                        },
                                                      });
                                                    }
                                                  );
                                                }
                                              );
                                            }
                                          );
                                        }
                                      );
                                    }
                                  }
                                );
                              }.bind({}, dom),
                            },
                          },
                          sb.moduleId
                        );

                      modal.body.makeNode("title", "div", {
                        text: "Dashboard Settings",
                        css: "ui huge header",
                      });

                      modal.body.makeNode("form", "form", {
                        name: {
                          name: "name",
                          label: "Dashboard Name",
                          type: "text",
                          value: layout.name,
                        },
                        is_public: {
                          name: "is_public",
                          label: "Make this dashboard public?",
                          type: "select",
                          options: [
                            {
                              name: "No",
                              value: 0,
                            },
                            {
                              name: "Yes",
                              value: 1,
                            },
                          ],
                          value: layout.is_public,
                        },
                        box_links: {
                          name: "box_links",
                          label: "Make the titles of each section a link?",
                          type: "select",
                          options: [
                            {
                              name: "Yes",
                              value: 1,
                            },
                            {
                              name: "No",
                              value: 0,
                            },
                          ],
                          value: boxLinks,
                        },
                      });

                      modal.body.makeNode("break", "div", { text: "<br />" });

                      modal.body.makeNode("buttons", "div", {
                        css: "ui buttons",
                      });

                      modal.body.buttons
                        .makeNode("save", "div", {
                          css: "ui green button",
                          text: "Save",
                        })
                        .notify(
                          "click",
                          {
                            type: "dashboard-tool-run",
                            data: {
                              run: function () {
                                modal.body.buttons.save.loading();
                                modal.body.buttons.cancel.loading();

                                var formData = modal.body.form.process();

                                sb.data.db.obj.update(
                                  "layouts",
                                  {
                                    id: layout.id,
                                    name: formData.fields.name.value,
                                    is_public: +formData.fields.is_public.value,
                                    box_links: +formData.fields.box_links.value,
                                  },
                                  function (updated) {
                                    modal.hide();

                                    getLayout(
                                      state,
                                      function (newLayout) {
                                        displayLayoutsMenu.call(
                                          state,
                                          dom,
                                          newLayout,
                                          function () {
                                            buildBoxes(
                                              grid,
                                              newLayout,
                                              function () {}
                                            );
                                          }
                                        );
                                      },
                                      updated.id
                                    );
                                  }
                                );
                              },
                            },
                          },
                          sb.moduleId
                        );

                      modal.body.buttons.makeNode("or", "div", { css: "or" });

                      modal.body.buttons
                        .makeNode("cancel", "div", {
                          css: "ui button",
                          text: "Cancel",
                        })
                        .notify(
                          "click",
                          {
                            type: "dashboard-tool-run",
                            data: {
                              run: function () {
                                modal.hide();
                              },
                            },
                          },
                          sb.moduleId
                        );

                      modal.patch();
                    },
                  });

                  dom.modals.patch();

                  dom.modals.modal.show();
                }.bind({}, dom, currentLayout),
              },
            },
            sb.moduleId
          );
        }

        dom.patch();

        /*
var sectionPopupPosition = 'left center';

					if ($(window).width() <= 768) {

						sectionPopupPosition = 'bottom center';

					}
*/

        if (
          (sb.permissions.isGroupManager(+sb.data.cookie.userId, mainGroup) &&
            !isPublic) ||
          layoutsBuilder ||
          state.editDashboard
        ) {
          /*
$(dom.rightMenu.editLayout.selector).popup(
							{
								boundry:				grid.selector
								, position:			sectionPopupPosition
								, prefer:				'opposite'
								, hoverable:			true
								, on:				'click'
								, lastResort:			true
								, arrowPixelsFromEdge: 	1
								, setFluidWidth:		true
							}
						);
*/
        }

        if (callback) {
          callback(layout);
        }
      });
    }

    getLayout(
      state,
      function (layout) {
        if (!isRightTray) {
          var layout = layout[0] ? layout[0] : layout;
        }

        layoutId = layout.id;

        var gridOptions = {
          css: "ui stackable grid",
        };

        if (!sb.dom.isMobile) {
          gridOptions.drag = gridSetup;
        }

        if (
          !sb.permissions.isGroupManager(+sb.data.cookie.userId, mainGroup) ||
          sb.dom.isMobile === true
        ) {
          gridSetup = false;
        }

        if (dom) {
          if (!isRightTray) {
            var style = sb.dom.isMobile ? "overflow-x:scroll" : "";
            dom.makeNode("menu", "div", {
              css: "ui top attached tabular menu",
              style: style,
            });

            dom.makeNode("tab", "div", {
              css: "ui bottom attached active tab segment",
            });

            grid = dom.tab.makeNode("grid", "div", gridOptions);
          }

          draw({
            dom: dom,
            after: function (dom) {
              var state = this;

              if (isRightTray) {
                buildBoxes(
                  dom,
                  layout,
                  function () {
                    sb.notify({
                      type: "dashboard-tool-save-boxview",
                      data: {
                        dropped: {
                          grid: dom,
                        },
                        callback: function (newLayout) {},
                      },
                    });
                  },
                  appViews,
                  newAppViews
                );
              } else {
                displayLayoutsMenu.call(
                  state,
                  dom.menu,
                  layout,
                  function (newLayout) {
                    if (newLayout) {
                      buildBoxes(
                        dom.tab.grid,
                        newLayout,
                        function () {
                          sb.notify({
                            type: "dashboard-tool-save-boxview",
                            data: {
                              dropped: {
                                grid: dom.tab.grid,
                              },
                              callback: function (newLayout) {},
                            },
                          });
                        },
                        appViews,
                        newAppViews
                      );

                      if (callback && typeof callback == "function") {
                        callback(true);
                      }
                    }
                  }
                );
              }
            }.bind(state),
          });
        }
      },
      layoutsObjId
    );
  }

  function showAppView(dom, boxView, systemBoxView, dashboardState) {
    var state = appConfig.state;
    var layout = boxView.layout;
    var isRightTray = layout.is_rightTray;
    var settings = _.findWhere(layout.settings, { uid: boxView.uid });
    var isPublic = boxView.layout.is_public;
    var view = systemBoxView;
    var width = view.width;
    var name = view.title;
    if (systemBoxView.hasOwnProperty("settings")) {
      if (systemBoxView.settings) {
        view.settings = systemBoxView.settings;
        if (systemBoxView.settings.hasOwnProperty("width")) {
          if (systemBoxView.settings.width) {
            width = systemBoxView.settings.width;
          }
        }
        if (systemBoxView.settings.hasOwnProperty("name")) {
          if (systemBoxView.settings.name) {
            name = systemBoxView.settings.name;
          }
        }
      } else {
        view.settings = {};
      }
    }

    var formArgs = {
      name: {
        name: "name",
        type: "text",
        label: "App View Section Name",
        value: name,
      },
    };

    var hideClass = sb.dom.isMobile ? "hide" : "";

    var boxDomOptions = {
      css: width + " wide column",
    };

    var boxViewDataAttr = [
      {
        name: "uid",
        value: boxView.uid,
      },
      {
        name: "boxid",
        value: boxView.boxView,
      },
      {
        name: "toolid",
        value: boxView.tool.id,
      },
      {
        name: "grouptoolid",
        value: boxView.toolId,
      },
      {
        name: "layoutid",
        value: layout.id,
      },
    ];
    boxDomOptions.dataAttr = boxViewDataAttr;

    var dragBox = {
      moves: function (el, source, handle, sib) {
        if (el.boxView == "workflowsApp") {
          return false;
        }
        return true;
      },
      data: {
        boxView: boxView.boxView,
        tool: boxView.tool,
        position: boxView.uid,
        grid: dom,
        layoutId: boxView.layout.id,
      },
      drop: [sb.moduleId + "-boxview-dropped"],
      accepts: false,
      copy: false,
    };

    if (
      !sb.permissions.isGroupManager(+sb.data.cookie.userId, mainGroup) ||
      sb.dom.isMobile === true ||
      isRightTray
    ) {
      dragBox = false;
    }

    if (dashboardState) {
      if (dashboardState.viewOnly === true) {
        dragBox = false;
      }
    }

    if (!sb.dom.isMobile) {
      boxDomOptions.drag = dragBox;
    }

    var boxViewToolIDClassName = boxView.tool.id.replace(".", "-");
    var boxViewUIDClassName = boxView.uid.replace(".", "-");

    boxView.boxDom = dom.makeNode(
      "view-" + boxViewToolIDClassName + "-" + boxViewUIDClassName,
      "div",
      boxDomOptions
    );

    boxView.boxDom.empty();

    var css = isRightTray
      ? "ui fluid card revealParent rightTrayCard"
      : "ui fluid card revealParent";
    var boxViewCard = boxView.boxDom.makeNode("card", "div", {
      css: css,
    });

    var style = isRightTray
      ? "background-color:#ffffff;"
      : "background-color:#f8f8f8;";
    var text = systemBoxView.boxLink
      ? '<a href="' +
        href +
        '"><i class="ui ' +
        boxView.tool.icon.color +
        " " +
        boxView.tool.icon.type +
        ' icon"></i> ' +
        name +
        "</a>"
      : '<i class="ui ' +
        boxView.tool.icon.color +
        " " +
        boxView.tool.icon.type +
        ' icon"></i> ' +
        name;
    var boxViewCardHeaderContent = boxView.boxDom.card.makeNode(
      "headerContent",
      "div",
      {
        css: "ui content",
        style: style,
      }
    );
    var boxViewCardHeader = boxViewCardHeaderContent.makeNode("header", "div", {
      text: text,
      css: "header",
      tag: "h4",
    });

    if (systemBoxView.boxLink) {
      if (!isRightTray) {
        var headerCollapse = boxViewCardHeader
          .makeNode("collapse", "div", {
            css: "right floated revealChild",
            style: "color:lightgrey;",
            text: '<i class="caret up icon"></i>',
            //tag:'a'
          })
          .notify(
            "click",
            {
              type: [sb.moduleId + "-run"],
              data: {
                run: function (data) {
                  if (data.dom.hasOwnProperty("isClosed")) {
                    if (data.dom.isClosed === true) {
                      data.dom.isClosed = false;

                      $(data.dom.selector).html(
                        '<i class="caret up icon"></i>'
                      );
                    } else {
                      data.dom.isClosed = true;

                      $(data.dom.selector).html(
                        '<i class="caret down icon"></i>'
                      );
                    }
                  } else {
                    data.dom.isClosed = true;

                    $(data.dom.selector).html(
                      '<i class="caret down icon"></i>'
                    );
                  }

                  $(boxViewCard.content.selector).toggleClass("hide");
                },
                dom: this,
              },
            },
            sb.moduleId
          );
      }

      boxViewCardHeader.makeNode("view", "div", {
        css: "revealChild",
        style: "color:lightgrey;",
        text: '<i class="right floated external square icon"></i>',
        tag: "a",
        href: href,
      });
    }

    if (sb.permissions.isGroupManager(+sb.data.cookie.userId, mainGroup)) {
      var currSystemBoxView = _.clone(systemBoxView);
      var currBoxView = _.clone(boxView);

      var boxViewCardHeaderSettings = boxViewCardHeader
        .makeNode("settings", "div", {
          css: "revealChild",
          style: "color:lightgrey;",
          text: '<i class="right floated cog icon"></i>',
          tag: "a",
          href: "",
        })
        .notify(
          "click",
          {
            type: "dashboard-tool-run",
            data: {
              run: function (dom, boxView) {
                boxViewCard.empty();
                boxViewCard.makeNode("content", "div", { css: "ui content" });
                boxViewCard.patch();

                displayBoxSettings(
                  dom,
                  currSystemBoxView,
                  currBoxView,
                  function (res) {
                    // sb.data.db.obj.getById(
                    // 	'layouts'
                    // 	, boxView.layout.id
                    // 	, function(layout) {
                    // 		buildSingleBox(dom, systemBoxView, boxView, layout);
                    // 	}
                    // );
                  }
                );
              }.bind({}, dom, boxView),
            },
          },
          sb.moduleId
        );
    }

    boxViewCard
      .makeNode("content", "div", {
        css: "ui content hide-scrollbar " + hideClass,
        style: "overflow-x: scroll; overflow:visible;",
      })
      .makeNode("placeholder", "div", {
        css: "ui fluid placeholder",
        text:
          '<div class="image header">' +
          '<div class="line"></div>' +
          '<div class="line"></div>' +
          "</div>" +
          '<div class="header">' +
          '<div class="line"></div>' +
          '<div class="line"></div>' +
          "</div>",
      });

    boxView.boxDom.patch();

    function displayBoxSettings(dom, systemBoxView, boxView, callback) {
      var boxViewToolIDClassName = boxView.tool.id.replace(".", "-");
      var boxViewUIDClassName = boxView.uid.replace(".", "-");

      var domInnerContainer =
        dom["view-" + boxViewToolIDClassName + "-" + boxViewUIDClassName].card
          .content;
      var isRightTray = boxView.layout.is_rightTray;
      var view = systemBoxView;
      var width = view.width;
      var name = view.title;
      var filterByParent = false;
      if (systemBoxView.hasOwnProperty("settings")) {
        if (systemBoxView.settings) {
          view.settings = systemBoxView.settings;
          if (systemBoxView.settings.hasOwnProperty("width")) {
            if (systemBoxView.settings.width) {
              width = systemBoxView.settings.width;
            }
          }
          if (systemBoxView.settings.hasOwnProperty("name")) {
            if (systemBoxView.settings.name) {
              name = systemBoxView.settings.name;
            }
          }
          if (systemBoxView.settings.hasOwnProperty("filterByParent")) {
            if (systemBoxView.settings.filterByParent) {
              filterByParent = true;
            }
          }
        } else {
          view.settings = {};
        }
      }

      var formArgs = {
        name: {
          name: "name",
          type: "text",
          label: "App View Section Name",
          value: name,
        },
      };

      if (settings) {
        if (settings.width) {
          width = settings.width;
        }

        if (settings.name) {
          name = settings.name;
        }
      }

      ///don't show if this is the workflows box view
      if (view.id != "workflowsApp") {
        if (!isRightTray) {
          formArgs.width = {
            name: "width",
            type: "select",
            label: "Section Width",
            value: width,
            options: [
              {
                name: "2",
                value: "two",
              },
              {
                name: "4",
                value: "four",
              },
              {
                name: "6",
                value: "six",
              },
              {
                name: "8",
                value: "eight",
              },
              {
                name: "10",
                value: "ten",
              },
              {
                name: "12",
                value: "twelve",
              },
              {
                name: "14",
                value: "fourteen",
              },
              {
                name: "16",
                value: "sixteen",
              },
            ],
          };
        }

        domInnerContainer
          .makeNode("remove", "div", {
            css: "ui mini basic icon button",
            text: '<i class="ui trash icon"></i>',
          })
          .notify(
            "click",
            {
              type: "dashboard-tool-run",
              data: {
                run: function (dom, boxView) {
                  sb.dom.alerts.ask(
                    {
                      title: "Are you sure?",
                      text: "Are you sure you want to delete this app view?",
                    },
                    function (resp) {
                      if (resp) {
                        swal.disableButtons();

                        swal.close();

                        removeBox(dom, boxView, function (res) {
                          callback(res);
                        });
                      }
                    }
                  );
                }.bind({}, dom, boxView),
              },
            },
            sb.moduleId
          );
      }

      // Add filter by parent option to collections
      if (systemBoxView && systemBoxView.collections) {
        formArgs.filterByParent = {
          name: "filterByParent",
          type: "check",
          label: "Filter by Parent?",
          value: filterByParent,
          options: [
            {
              label: "Yes",
              value: "yes",
              checked: filterByParent,
            },
          ],
        };
      }

      domInnerContainer.makeNode("tabs", "div", {
        css: "ui secondary pointing fluid two item menu",
      });

      domInnerContainer.tabs.makeNode("standard", "div", {
        tag: "a",
        dataAttr: [
          {
            name: "tab",
            value: "standard-" + boxViewUIDClassName,
          },
        ],
        css: "active item optionMenu-" + boxViewUIDClassName,
        text: "Standard",
      });

      domInnerContainer.tabs.makeNode("other", "div", {
        tag: "a",
        dataAttr: [
          {
            name: "tab",
            value: "other-" + boxViewUIDClassName,
          },
        ],
        css: "item optionMenu-" + boxViewUIDClassName,
        text: "Other",
      });

      domInnerContainer.makeNode("standard", "div", {
        css: "ui active tab",
        dataAttr: [
          {
            name: "tab",
            value: "standard-" + boxViewUIDClassName,
          },
        ],
      });

      domInnerContainer
        .makeNode("other", "div", {
          css: "ui tab",
          dataAttr: [
            {
              name: "tab",
              value: "other-" + boxViewUIDClassName,
            },
          ],
        })
        .makeNode("cont", "div", {});

      domInnerContainer.standard.makeNode("form", "form", formArgs);

      domInnerContainer.standard.makeNode("formBreak", "div", {
        text: "<br />",
      });

      domInnerContainer.standard.makeNode("buttons", "div", {
        css: "ui mini buttons",
      });

      domInnerContainer.standard.buttons
        .makeNode("save", "div", { css: "ui green button", text: "Save" })
        .notify(
          "click",
          {
            type: "dashboard-tool-run",
            data: {
              run: function (dom, boxView) {
                domInnerContainer.standard.buttons.save.loading();

                sb.data.db.obj.getById(
                  "layouts",
                  boxView.layout.id,
                  function (layout) {
                    if (!layout.settings) {
                      layout.settings = [];
                    }

                    var currentSettings = _.findWhere(layout.settings, {
                      uid: boxView.uid,
                    });
                    var formData = domInnerContainer.standard.form.process();

                    if (currentSettings) {
                      layout.settings = _.reject(
                        layout.settings,
                        function (setting) {
                          return setting.uid == boxView.uid;
                        }
                      );
                    } else {
                      currentSettings = {};
                    }

                    currentSettings.name = formData.fields.name.value;
                    currentSettings.width = !isRightTray
                      ? formData.fields.width.value
                      : "";
                    currentSettings.uid = boxView.uid;

                    if (!_.isEmpty(formData.fields.filterByParent)) {
                      currentSettings.filterByParent =
                        formData.fields.filterByParent.value;
                    }

                    layout.settings.push(currentSettings);
                    systemBoxView.settings = currentSettings;

                    sb.data.db.obj.update(
                      "layouts",
                      { id: layout.id, settings: layout.settings },
                      function (updated) {
                        sb.notify({
                          type: "dashboard-tool-save-boxview",
                          data: {
                            dropped: {
                              grid: grid,
                            },
                            callback: function (newLayout) {
                              domInnerContainer.empty();

                              // Show the app view
                              sb.notify({
                                type: "show-app-view",
                                data: {
                                  dom: dom,
                                  boxView: boxView,
                                  systemBoxView: systemBoxView,
                                },
                              });
                            },
                          },
                        });
                      }
                    );
                  }
                );
              }.bind({}, dom, boxView),
            },
          },
          sb.moduleId
        );

      domInnerContainer.standard.buttons.makeNode("or", "div", { css: "or" });

      domInnerContainer.standard.buttons
        .makeNode("close", "div", { css: "ui button", text: "Cancel" })
        .notify(
          "click",
          {
            type: "dashboard-tool-run",
            data: {
              run: function (dom, boxView) {
                domInnerContainer.standard.buttons.close.loading();

                setTimeout(function () {
                  // Show the app view
                  sb.notify({
                    type: "show-app-view",
                    data: {
                      dom: dom,
                      boxView: boxView,
                      systemBoxView: systemBoxView,
                    },
                  });
                }, 0);
              }.bind({}, dom, boxView),
            },
          },
          sb.moduleId
        );

      domInnerContainer.patch();

      if (view.settingsView) {
        view.settingsView(
          domInnerContainer.other.cont,
          view.settings,
          function (updatedSettings) {
            sb.data.db.obj.getById(
              "layouts",
              boxView.layout.id,
              function (layout) {
                systemBoxView.settings = updatedSettings;

                var layoutBoxView = _.findWhere(layout.layout, {
                  uid: boxView.uid,
                });

                if (layoutBoxView.options) {
                  layoutBoxView.options = updatedSettings;
                } else {
                  layoutBoxView["options"] = updatedSettings.options;
                }

                updatedSettings = layoutBoxView;

                if (!layout.settings) {
                  layout.settings = [];
                }

                var currentSettings = _.findWhere(layout.settings, {
                  uid: boxView.uid,
                });
                var formData = domInnerContainer.standard.form.process();

                if (currentSettings) {
                  layout.settings = _.reject(
                    layout.settings,
                    function (setting) {
                      return setting.uid == boxView.uid;
                    }
                  );
                }

                layout.settings.push(updatedSettings);
                var updatedLayout = {
                  id: layout.id,
                  settings: layout.settings,
                };

                sb.data.db.obj.update(
                  "layouts",
                  updatedLayout,
                  function (updated) {
                    boxView.layout = updated;

                    // Show the app view
                    sb.notify({
                      type: "show-app-view",
                      data: {
                        dom: dom,
                        boxView: boxView,
                        systemBoxView: systemBoxView,
                      },
                    });
                  }
                );
              }
            );
          },
          dashboardState
        );
      } else {
        domInnerContainer.other.makeNode("title", "div", {
          text: "No other settings.",
          css: "center aligned",
        });

        domInnerContainer.other
          .makeNode("close", "div", {
            css: "ui mini button center aligned",
            text: "Close",
          })
          .notify(
            "click",
            {
              type: "dashboard-tool-run",
              data: {
                run: function (dom, boxView) {
                  domInnerContainer.other.close.loading();

                  setTimeout(function () {
                    callback();
                  }, 500);
                }.bind({}, dom, boxView),
              },
            },
            sb.moduleId
          );

        domInnerContainer.other.patch();
      }

      $(".optionMenu-" + boxViewUIDClassName).tab();
    }

    function removeBox(dom, boxView, dashboardRefresh) {
      // Set variables
      var isRightTray = boxView.layout.is_rightTray;
      var grid = boxView.grid;
      var boxViewToolIDClassName = boxView.tool.id.replace(".", "-");
      var boxViewUIDClassName = boxView.uid.replace(".", "-");

      $(boxView.boxDom.selector).addClass("animated zoomOut");

      // Update and close the tray
      if (isRightTray) {
        // Remove box view
        $("#" + boxViewUIDClassName).slideUp("slow", function () {
          // Remove item
          $(this).remove();
          delete boxView
            .grid["view-" + boxViewToolIDClassName + "-" + boxViewUIDClassName];
          boxView.grid.patch();

          // Close the tray
          sb.notify({
            type: "close-right-tray",
          });
        });
      }

      setTimeout(function () {
        $(boxView.boxDom.selector).remove();

        sb.notify({
          type: "dashboard-tool-save-boxview",
          data: {
            layoutId: boxView.layout.id,
            boxView: boxView,
            dropped: {
              grid: grid,
            },
            callback: dashboardRefresh,
          },
        });
      }, 700);

      // Show success
      sb.notify({
        type: "display-alert",
        data: {
          header: "App View removed successfully!",
          color: "green",
        },
      });
    }

    function displayBox(dom, boxView, callback, uniqBoxView) {
      var myStuffUser = +sb.data.cookie.get("uid");

      if (mainGroupType == "myStuff") {
        if (parseInt(mainGroup.user) != +sb.data.cookie.get("uid")) {
          myStuffUser = mainGroup.user;
        }
      }

      dom.empty();

      var state = _.clone(appConfig.state);
      state.boxView = boxView;

      // copiedState = JSON.parse(JSON.stringify(state));
      copiedState = state;
      copiedState.user = myStuffUser;

      if (!copiedState.boxView.hasOwnProperty("settings")) {
        copiedState.boxView = {};
      }

      copiedState.boxView.updateSettings = function (newSettings, callback) {
        var uniqBoxView = this;
        var emptiedList = _.reject(layout.settings, function (set) {
          return set.uid == uniqBoxView.uid;
        });

        emptiedList.push(newSettings);

        sb.data.db.obj.update(
          "layouts",
          {
            id: layout.id,
            settings: emptiedList,
          },
          function (done) {
            if (callback) {
              callback(newSettings);
            }
          }
        );
      }.bind(uniqBoxView);

      if (boxView.hasOwnProperty("collections")) {
        dom.empty();

        dom.makeNode("div", "div", {});

        var collSetup = _.clone(boxView.collections);

        if (collSetup.actions && collSetup.actions.forcedMenu) {
          collSetup.menu = collSetup.menu;
        } else {
          collSetup.menu = false;
        }

        collSetup.selectedView = "table";

        if (!collSetup.hasOwnProperty("pageLength")) {
          collSetup.pageLength = 10;
        }

        if (!collSetup.hasOwnProperty("showPaging")) {
          collSetup.showPaging = false;
        }

        if (typeof collSetup.where === "function") {
          // This function should return a 'where' object
          collSetup.where = collSetup.where(copiedState);
        }

        if (!collSetup.where.hasOwnProperty("paged")) {
          collSetup.where.paged = {
            count: true,
            page: 0,
            pageLength: 5,
            sortCol: "date_created",
            sortDir: "desc",
          };
        }

        if (state.hasOwnProperty("headquarters")) {
          if (state.headquarters.hasOwnProperty("id")) {
            if (state.headquarters.id) {
              if (state.id === state.headquarters.id) {
                delete collSetup.where.tagged_with;
              }
            }
          }
        }

        if (boxView.hasOwnProperty("ignoreLayers")) {
          if (!_.contains(boxView.ignoreLayers, "myStuff")) {
            if (mainGroupType === "myStuff") {
              collSetup.where.tagged_with = [myStuffUser];
            }
          }
        } else {
          if (mainGroupType === "myStuff") {
            collSetup.where.tagged_with = [myStuffUser];
          }
        }

        collSetup.domObj = dom.div;
        collSetup.where.is_template = 0;

        dom.patch();

        collSetup.onBoxview = true;

        // For box views making use of the 'additionalTags' option
        if (
          copiedState.boxView &&
          copiedState.boxView.settings &&
          copiedState.boxView.settings.options &&
          Array.isArray(copiedState.boxView.settings.options.additionalTags)
        ) {
          _.each(
            copiedState.boxView.settings.options.additionalTags,
            function (t) {
              collSetup.where.tagged_with.push(parseInt(t));
            }
          );
        }

        if (isRightTray) {
          collSetup.submenu = false;
          collSetup.subviews = false;
        }

        if (!_.isEmpty(boxView.settings) && boxView.settings.filterByParent) {
          sb.data.db.obj.getById(
            "",
            collSetup.where.tagged_with[0],
            function (obj) {
              if (obj && obj.parent) {
                collSetup.where.tagged_with = [obj.parent.id];
                sb.notify({
                  type: "show-collection",
                  data: collSetup,
                });
              } else {
                // No parent message
                dom.makeNode("msg", "div", {
                  text: "No parent set on this page.",
                });
                dom.patch();
              }
            },
            {
              parent: true,
            }
          );
        } else {
          sb.notify({
            type: "show-collection",
            data: collSetup,
          });
        }
      } else {
        // Get settings from the tool, if they exist
        if (
          uniqBoxView.settings &&
          uniqBoxView.settings.toolId &&
          mainGroup.tools
        ) {
          copiedState.tool = _.findWhere(mainGroup.tools, {
            id: uniqBoxView.settings.toolId,
          });
        }

        copiedState.boxView.uid = uniqBoxView.uid;

        boxView.dom.call({}, dom, copiedState, function (draw) {
          if (draw) {
            draw.patch();
          } else {
            dom.empty();

            dom.makeNode("cont", "div", {
              css: "ui center aligned container",
            });

            dom.cont.makeNode("icon", "div", {
              css:
                "huge " +
                boxView.toolDefaultOpt.color +
                " " +
                boxView.toolDefaultOpt.type +
                " icon",
              tag: "i",
            });

            dom.cont.makeNode("tip", "div", {
              css: "ui small grey header",
              text: boxView.toolDefaultOpt.tip,
            });

            dom.patch();
          }
        });
      }
    }

    displayBox(boxViewCard.content, systemBoxView, function () {}, boxView);

    // Patch the UI
    if (dom.hasOwnProperty("build")) {
      dom.build();
    } else {
      dom.patch();
    }
  }

  function showAppViewInRightTray(boxView, systemBoxView) {
    var boxViewToolIDClassName = boxView.tool.id.replace(".", "-");
    var boxViewUIDClassName = boxView.uid.replace(".", "-");

    if ($("#" + boxViewUIDClassName).length) {
      // Make a node
      var rightTrayBoxViewContainer = domTool.make(
        ".rightTrayBoxViewContainer"
      );

      // Show the app view
      sb.notify({
        type: "show-app-view",
        data: {
          dom: rightTrayBoxViewContainer,
          boxView: boxView,
          systemBoxView: systemBoxView,
        },
      });

      // Open the right tray
      sb.notify({
        type: "open-right-tray",
      });

      // Set the current item as active
      $(".rightTrayBoxViewIcon").removeClass("active");
      $("#" + boxViewUIDClassName).addClass("active");

      // Set the cache
      cachedSelectedTrayView = {
        boxView: boxView,
        systemBoxView: systemBoxView,
      };
    } else {
      // Close the right tray
      sb.notify({
        type: "close-right-tray",
      });
    }
  }

  function showAppViewPicker(ui, state, layout, callback) {
    var modal = ui.makeNode("modal", "modal", {
      onShow: function () {
        modal.body.makeNode("modalHeader", "div", {
          css: "ui equal width grid",
        });
        modal.body.makeNode("divider", "div", {
          css: "ui divider",
        });

        modal.body.modalHeader.makeNode("col1", "div", {
          css: "column",
        });
        modal.body.modalHeader.makeNode("col2", "div", {
          css: "column",
        });

        modal.body.modalHeader.col1.makeNode("title", "div", {
          text: '<i class="ui plus icon"></i> Add app views',
          tag: "h2",
        });

        modal.body.modalHeader.col2
          .makeNode("btnGrp", "div", {})
          .makeNode("doneBtn", "div", {
            text: "Add",
            css: "ui green button right floated",
          })
          .notify(
            "click",
            {
              type: "dashboard-tool-run",
              data: {
                run: function (data) {
                  if (!_.isEmpty(AppViews)) {
                    sb.data.db.obj.update(
                      "layouts",
                      {
                        id: layout.id,
                        layout: AppViews,
                      },
                      function (updated) {
                        var appViews = _.clone(AppViews);
                        var newAppViews = _.clone(NewAppViews);

                        // Empty the array
                        AppViews = [];

                        // Show success
                        sb.notify({
                          type: "display-alert",
                          data: {
                            header: "App View(s) added successfully!",
                            color: "green",
                          },
                        });

                        // Callback
                        callback(layout, appViews, newAppViews);
                      }
                    );
                  }

                  modal.hide();
                },
              },
            },
            sb.moduleId
          );

        // Define tools
        tools = layout.group_type
          ? getTools(layout, true)
          : getTools(state.pageObject, true);

        // Get layout
        sb.data.db.obj.getWhere(
          "layouts",
          { id: layout.id },
          function (layout) {
            displaySectionMenu(
              modal.body,
              tools,
              layout[0],
              function (layout) {
                getLayout(state, function (layout) {}, layout.id);
              },
              true
            );
          }
        );
      },
    });

    ui.patch();
    modal.show();
  }

  // Helper functions
  function filterAdminOnlyViews(tools) {
    if (!_.contains(appConfig.headquarters.managers, +sb.data.cookie.userId)) {
      tools = _.filter(tools, function (tool) {
        return !tool.adminOnly;
      });
    }

    return _.reject(tools, { id: "dashboardtool" });
  }

  function getLayout(state, callback, layoutId, toolLayout) {
    var isPublic = false;
    var pageObjectId = 0;

    // In client portal
    if (appConfig && appConfig.state && appConfig.state.portal) {
      isPublic = true;
    }

    if (state.hasOwnProperty("myStuff")) {
      mainGroup = state.myStuff;
      mainGroupType = "myStuff";
      pageObjectId = mainGroup.id;
      //layoutId = +sb.data.cookie.userId;
    } else if (state.hasOwnProperty("pageObject")) {
      mainGroup = state.pageObject;
      pageObjectId = mainGroup.id;

      switch (state.pageObject.group_type) {
        case "JobType":
          mainGroupType = "jobType";

          break;

        case "Project":
          mainGroupType = "project";

          break;

        case "Team":
          mainGroupType = "team";

          break;

        default:
          mainGroupType = "hq";
      }
    }

    tools = getTools(mainGroup, true);
    boxViews = _.uniq(boxViews, "id");
    noBoxViews = _.uniq(noBoxViews, "id");
    noMainViews = _.uniq(noMainViews, "id");

    if (layoutId) {
      ///grab dashboard (layout) with provided id
      sb.data.db.obj.getById("layouts", layoutId, function (layout) {
        tools = layout.is_rightTray ? getTools(layout, true) : tools;

        if (layout) {
          if (layout.layout.length > 0) {
            _.each(layout.layout, function (layout) {
              layout.tool = _.findWhere(tools, { id: layout.tool });
            });
          } else {
            if (toolLayout) {
              if (toolLayout.id != "blank") {
                _.each(order, function (widthString) {
                  _.each(toolLayout.boxViews, function (view, count) {
                    if (view.width == widthString && view.default) {
                      layout.layout.push({
                        boxView: view.id,
                        tool: toolLayout,
                      });
                    }
                  });
                });
              }
            }
          }

          callback(layout);
        } else {
          if (
            sb.permissions.isGroupManager(+sb.data.cookie.userId, mainGroup)
          ) {
            ///create default 'Overview' dashboard (layout) minus boxView layouts
            sb.data.db.obj.create(
              "layouts",
              {
                layout_id: pageObjectId,
                name: "Overview",
                box_links: 1,
                is_public: 1,
              },
              function (getLayout) {
                layout = getLayout;

                layout.layout = [];

                _.each(order, function (widthString) {
                  _.each(tools, function (tool, i) {
                    _.each(tool.boxViews, function (view, count) {
                      if (view.width == widthString && view.default) {
                        var toolItem = {
                          boxView: view.id,
                          tool: _.findWhere(tools, { id: tool.id }),
                        };

                        layout.layout.push(toolItem);
                      }
                    });
                  });
                });

                callback(layout);
              }
            );
          } else {
            callback({ layout_id: pageObjectId });
          }
        }
      });
    } else {
      // Create query object
      if (state.setId) {
        var queryObj = {
          set_layout_id: state.setId,
          is_rightTray: 0,
        };
      } else {
        var queryObj = {
          layout_id: pageObjectId,
          is_rightTray: 0,
        };
      }

      if (queryObj.layout_id) {
        queryObj.set_layout_id = "";
      }

      // In a client portal
      if (isPublic) {
        queryObj.is_public = 1;
      }

      ///no layout id provided, getWhere
      sb.data.db.obj.getWhere("layouts", queryObj, function (layouts) {
        var visibleLayouts = layouts;

        if (
          sb.permissions.isGroupMember(+sb.data.cookie.userId, mainGroup) ==
          false
        ) {
          visibleLayouts = _.where(layouts, { is_public: 1 });
        }

        ///dashboards exist attach tools, else create default dashboard
        if (visibleLayouts.length > 0) {
          if (visibleLayouts[0].layout.length > 0) {
            ///each through dashboards (layouts) and attach tools from Tools Array
            _.each(visibleLayouts, function (visibleLayout) {
              _.each(visibleLayout.layout, function (layout) {
                layout.tool = _.findWhere(tools, { id: layout.tool });
              });
            });
          } else {
            ///add boxView layouts to dashboard
            if (visibleLayouts.length == 1) {
              _.each(order, function (widthString) {
                _.each(tools, function (tool) {
                  _.each(tool.boxViews, function (view, count) {
                    if (view.width == widthString && view.default) {
                      visibleLayouts[0].layout.push({
                        boxView: view.id,
                        tool: tool,
                      });
                    }
                  });
                });
              });
            }
          }

          callback(visibleLayouts);
        } else {
          if (
            sb.permissions.isGroupManager(+sb.data.cookie.userId, mainGroup)
          ) {
            var newLayout = {
              name: "Overview",
              box_links: 1,
              is_public: 1,
            };

            if (state.setId) {
              newLayout.set_layout_id = state.setId;
            } else {
              newLayout.layout_id = pageObjectId;
            }

            ///create default 'Overview' dashboard (layout) minus boxView layouts
            sb.data.db.obj.create("layouts", newLayout, function (getLayout) {
              layout = getLayout;

              layout.layout = [];

              _.each(order, function (widthString) {
                _.each(tools, function (tool, i) {
                  _.each(tool.boxViews, function (view, count) {
                    if (view.width == widthString && view.default) {
                      var toolItem = {
                        boxView: view.id,
                        tool: _.findWhere(tools, { id: tool.id }),
                      };

                      layout.layout.push(toolItem);
                    }
                  });
                });
              });

              callback(layout);
            });
          } else {
            callback({ layout_id: pageObjectId });
          }
        }
      });
    }
  }

  function getPageMenuItems(state, layer) {
    // Notes
    // state --> is a pageObject.

    function addMenuItem(selected, onComplete) {
      if (selected) {
        var tool = _.findWhere(
          filterAdminOnlyViews(appConfig[mainGroupType + "Tools"]),
          {
            id: selected,
          }
        );

        if (!tool) {
          return;
        }

        // If not options are set, default to allowing just renaming
        // the instance of the tool.
        if (_.isEmpty(tool.options)) {
          tool.options = {
            name: {
              defaultValue: tool.name,
              title: "Name",
              type: "title",
            },
          };
        }

        var opts = {};
        sb.notify({
          type: "get-options-form",
          data: {
            optionsDef: tool.options,
            options: opts,
            onComplete: function (opts) {
              if (
                !tool.allowMulti &&
                _.findWhere(mainGroup.tools, { system_name: tool.id })
              ) {
                _.findWhere(mainGroup.tools, {
                  system_name: tool.id,
                }).is_archieved = 0;
                _.findWhere(mainGroup.tools, {
                  system_name: tool.id,
                }).settings = opts;
              } else {
                mainGroup.tools.push({
                  allowed_users: [+sb.data.cookie.get("uid")],
                  system_name: tool.id,
                  display_name: tool.name,
                  is_archieved: 0,
                  order: mainGroup.tools.length + 1,
                  added_by: +sb.data.cookie.get("uid"),
                  added_on: moment().format("YYYY-MM-DD HH:mm:ss.SS"),
                  settings: opts,
                  box_color: tool.icon.color,
                });
              }

              sb.data.db.obj.update(
                "groups",
                {
                  id: mainGroup.id,
                  tools: mainGroup.tools,
                },
                function (done) {
                  swal.close();
                  mainGroup.tools = done.tools;
                  tools = getTools(done, false);
                  menuItems = getPageMenuItems(state, layer);

                  if (typeof displaySectionMenu.refresh === "function") {
                    displaySectionMenu.refresh(tools);
                  }

                  onComplete(menuItems);
                }
              );
            },
          },
        });
      }
    }

    var menuItems = [];
    var mainGroup = state;
    var mainGroupType = "";
    var tools = getTools(state, false);

    if (mainGroup) {
      switch (mainGroup.group_type) {
        case "Headquarters":
          mainGroupType = "hq";
          break;

        case "Project":
          mainGroupType = "project";
          break;

        case "Team":
          mainGroupType = "team";
          break;

        case "MyStuff":
          mainGroupType = "myStuff";
          break;

        case "JobType":
          mainGroupType = "jobType";
      }

      _.each(tools, function (tool) {
        if (
          tool.is_archieved != 0 &&
          ((Array.isArray(tool.mainViews) && !_.isEmpty(tool.mainViews)) ||
            typeof tool.dom === "function")
        ) {
          // get icon
          var icon = "";
          if (tool.icon) {
            icon = tool.icon.color + " " + tool.icon.type;
          }

          // get link
          var linkArgs =
            mainGroup.group_type == "Project"
              ? "project-tools"
              : mainGroupType + "Tool";

          var linkOpts = {
            tool: tool.id,
            startAt: layer.url,
          };

          if (mainGroup.group_type == "Project") {
            linkOpts = {
              tool: tool.id,
              startAt: sb.data.url.createPageURL("object", {
                id: mainGroup.id,
                name: mainGroup.name,
                type: "project",
              }),
            };
          }
          if (tool.toolId && tool.name) {
            linkOpts.name = tool.name;
            linkOpts.toolId = tool.toolId;
          } else if (typeof tool.name === "string" && !_.isEmpty(tool.name)) {
            linkOpts.name = tool.name;
          }

          href = sb.data.url.createPageURL(linkArgs, linkOpts);

          menuItems.push({
            id: tool.id,
            toolId: tool.toolId,
            title: tool.name,
            icon: icon,
            link: href,
            remove: function (tool, redraw) {
              sb.dom.alerts.ask(
                {
                  title: "Remove " + tool.name + "?",
                  text: "",
                },
                function (r) {
                  if (r) {
                    swal.disableButtons();

                    // Remove the selected tool
                    var i = 0;
                    _.find(mainGroup.tools, function (mainGroupTool) {
                      if (mainGroupTool) {
                        if (tool.allowMulti) {
                          if (tool.toolId === mainGroupTool.id) {
                            mainGroup.tools.splice(i, 1);
                            return;
                          }
                        } else if (tool.id === mainGroupTool.system_name) {
                          mainGroup.tools.splice(i, 1);
                          return;
                        }
                        i++;
                      }
                    });

                    sb.data.db.obj.update(
                      "groups",
                      {
                        id: mainGroup.id,
                        tools: mainGroup.tools,
                      },
                      function (done) {
                        sb.data.db.obj.getWhere(
                          "layouts",
                          {
                            layout_id: done.id,
                          },
                          function (layouts) {
                            _.each(layouts, function (layout) {
                              layout.layout = _.reject(
                                layout.layout,
                                function (o) {
                                  return o.tool === tool.id;
                                }
                              );
                            });

                            sb.data.db.obj.update(
                              "layouts",
                              layouts,
                              function () {
                                swal.close();

                                // Refresh dashboard
                                sb.notify({
                                  type: "field-updated",
                                  data: {
                                    obj: done,
                                    property: "tools",
                                    toolbarOpen: true,
                                  },
                                });

                                //!TODO -- Investigate why section menu UI is not updating
                                //displaySectionMenu.refresh(done.tools);
                              }
                            );
                          }
                        );
                      }
                    );
                  } else {
                    swal.close();
                  }
                }
              );
            }.bind({}, tool),
          });
        }
      });

      /*
	menuItems.push({
				view: function (ui) {

					var availableTools = filterAdminOnlyViews(appConfig[mainGroupType+'Tools']);

					//!TODO: filter out tools arleady added.

					ui.makeNode(
						'addTool'
						, 'div'
						, {
							css: 'ui grey scrolling dropdown item'
							, text: '<i class="grey plus icon"></i> Add a tool'
							, listener: {
								action: 'select'
								, onChange:function(value, text){

									if(value){

										var tool = _.findWhere(
											availableTools
											, {
												id: value
											}
										);

										if (!tool) {
											return;
										}

										if( _.findWhere(mainGroup.tools, {system_name:tool.id}) ){

											_.findWhere(mainGroup.tools, {system_name:tool.id}).is_archieved = 0;

										} else {

											mainGroup.tools.push(
												{
													allowed_users:		[+sb.data.cookie.get('uid')]
													, system_name:		tool.id
													, display_name:		tool.name
													, is_archieved:		0
													, order:				mainGroup.tools.length + 1
													, added_by:			+sb.data.cookie.get('uid')
													, added_on:			moment()
													, settings:			{}
													, box_color:			tool.icon.color
												}
											);

										}

										sb.data.db.obj.update('groups'
											, {
												id:			mainGroup.id
												, tools:		mainGroup.tools
											}
											, function(done){

												swal.close();
												mainGroup.tools = done.tools;
												tools = getTools (done);
												menuItems = getPageMenuItems(tools);
												state.drawPageMenu(menuItems);

											}
										);

									}

								},
								type:'dropdown',
								placeholder:'All types',
								allowCategorySelection: true
								, delay : {
								hide   : 300,
								show   : 200,
								search : 50,
								touch  : 50
								}
								, duration: 100
				// 				, transition: 'swing down'
							}
						}
					).makeNode('menu', 'div', {css:'menu'});

					_.each(_.sortBy(availableTools, 'name'), function (tool) {

						ui.addTool.menu.makeNode(
							't-'+ tool.id
							, 'div'
							, {
								css: 'item'
								, text: '<i class="'+ tool.icon.color +' '+ tool.icon.type +' icon"></i> '+ tool.name
								, dataAttr: [
									{
										name: 'value'
										, value: tool.id
									}
								]
							}
						);

					});

				}
			}) ;
	*/

      var selectedApps = filterAdminOnlyViews(
        appConfig[mainGroupType + "Tools"]
      );

      ///filters tools in '+' button dropdown of side nav
      _.map(mainGroup.tools, function (t) {
        if (t.system_name != "dashboardtool" && t.is_archieved == 0) {
          selectedApps = _.reject(selectedApps, function (page) {
            if (page.allowMulti) {
              return false;
            }
            return page.id == t.system_name;
          });
        }
      });

      return {
        links: menuItems,
        group: mainGroup,
        add: {
          add: addMenuItem,
          options: selectedApps,
        },
      };
    }
  }

  function getTools(mainGroup, allTools) {
    // Notes
    // mainGroup --> is a pageObject.

    var mainGroupType = "";
    var toolList = [];

    if (mainGroup) {
      switch (mainGroup.group_type) {
        case "Headquarters":
          mainGroupType = "hq";
          break;

        case "Project":
          mainGroupType = "project";
          break;

        case "Team":
          mainGroupType = "team";
          break;

        case "MyStuff":
          mainGroupType = "myStuff";
          break;

        case "JobType":
          mainGroupType = "jobType";

        default:
          mainGroupType = "object";
      }

      if (mainGroup.object_bp_type === "users") {
        mainGroupType = "myStuff";
      }

      if (allTools) {
        toolList = appConfig[mainGroupType + "Tools"];

        _.each(toolList, function (tools) {
          _.each(tools.boxViews, function (boxView) {
            if (!_.findWhere(boxViews, { id: boxView.id })) {
              boxViews.push(boxView);
            }
          });
        });
      } else {
        _.each(
          _.uniq(mainGroup.tools, function (tool) {
            var toolDef = _.findWhere(appConfig[mainGroupType + "Tools"], {
              id: tool.system_name,
            });
            if (toolDef && toolDef.allowMulti) {
              toolDef;
              return tool.id;
            }

            return tool.system_name;
          }),
          function (tool) {
            if (tool.is_archieved == 0 || tool.forceView === true) {
              systemTool = _.findWhere(appConfig[mainGroupType + "Tools"], {
                id: tool.system_name,
              });
              var toolItem = _.clone(systemTool);

              if (!toolItem) {
                return;
              }
              if (toolItem && toolItem.allowMulti) {
                toolItem.toolId = tool.id;
              }

              if (
                tool &&
                toolItem &&
                !_.isEmpty(tool.settings) &&
                tool.settings.name &&
                typeof tool.settings !== "undefined"
              ) {
                toolItem.name = tool.settings.name;
              }

              if (systemTool) {
                toolList.push(toolItem);
              }
            }
          }
        );
      }
    }

    return toolList;
  }

  function requestGroupAccess(obj, callback, accessLevel) {
    var peopleToContact = [];
    var accessString = "VIEW";

    if (accessLevel) {
      accessString = accessLevel.toUpperCase();
    }

    if (obj.managers) {
      peopleToContact = peopleToContact.concat(obj.managers);
    } else {
      peopleToContact = peopleToContact.concat(obj.tagged_with);
    }

    sb.data.db.obj.getById(
      "users",
      +sb.data.cookie.get("uid"),
      function (requestingUser) {
        sb.data.db.obj.getWhere(
          "users",
          {
            id: {
              type: "or",
              values: peopleToContact,
            },
          },
          function (people) {
            obj.type = "groups";

            var href = sb.data.url.createPageURL("object", obj);

            var args = {
              to: _.pluck(people, "email"),
              from: "<EMAIL>",
              subject:
                requestingUser.fname +
                " " +
                requestingUser.lname +
                " has requested " +
                accessString +
                " access to " +
                obj.name +
                ".",
              mergevars: {
                TITLE: "",
                BODY:
                  "<br />" +
                  requestingUser.fname +
                  " " +
                  requestingUser.lname +
                  " has requested " +
                  accessString +
                  " access to " +
                  obj.name +
                  "." +
                  "<br /><br />" +
                  "Click below to view the item and make changes:<br /><br />" +
                  href,
              },
            };

            sb.comm.sendEmail(args, function (emailSent) {
              callback(emailSent);
            });
          }
        );
      }
    );
  }

  function build_layouts_collection(dom, state, draw) {
    var group = state.pageObject || {};

    var options = state.options || {};

    var where = {
      is_template: 1,
      childObjs: {
        name: true,
        is_public: true,
        is_template: true,
        date_created: true,
        created_by: true,
        tagged_with: true,
      },
    };

    var collection = {
      actions: {
        create: function (dom, obj, onComplete, bp) {
          var formArgs = {
            name: {
              name: "name",
              type: "text",
              label: "Template Layout Name",
              placeholder: "Give this dashboard a name",
            },
            type: {
              name: "type",
              type: "hidden",
              value: "myStuff",
            },
            description: {
              name: "description",
              type: "textbox",
              rows: 1,
              label: "Template description",
            },
          };

          dom.makeNode("wrapper", "div", {});

          dom.wrapper.makeNode("head", "div", {});
          dom.wrapper.makeNode("lb_1", "lineBreak", {
            spaces: 1,
          });
          dom.wrapper.makeNode("body", "div", {});
          dom.wrapper.body.makeNode("formWrap", "div", {});
          dom.wrapper.body.formWrap.makeNode("form", "form", formArgs);
          dom.wrapper.body.formWrap.makeNode("br", "div", {
            text: "<br />",
          });
          dom.wrapper.makeNode("lb_2", "lineBreak", {
            spaces: 1,
          });
          dom.wrapper.makeNode("btnGrp", "div", {});

          dom.wrapper.head.makeNode("title", "div", {
            text: "Create a New Dashboard Template",
            tag: "h1",
            css: "text-center",
          });

          dom.wrapper.btnGrp
            .makeNode("save_btn", "div", {
              text: "Save",
              css: "ui green button right floated",
            })
            .notify("click", {
              type: "dashboard-tool-run",
              data: {
                run: function (dom) {
                  var formData = dom.wrapper.body.formWrap.form.process();

                  dom.wrapper.btnGrp.save_btn.loading();

                  if (formData.fields.name.value === "") {
                    sb.dom.alerts.alert(
                      "Incomplete Form",
                      "Please give this template a title.",
                      "error"
                    );

                    dom.wrapper.btnGrp.save_btn.loading(false);
                    dom.patch();
                  } else {
                    var layoutObj = {
                      name: formData.fields.name.value,
                      description: formData.fields.description.value,
                      is_template: 1,
                      is_public: 1,
                      layout_id: null,
                    };

                    sb.data.db.obj.create(
                      "layouts",
                      layoutObj,
                      function (resp) {
                        onComplete(resp);
                      }
                    );
                  }
                }.bind(null, dom),
              },
            });

          dom.patch();
        },
        view: false,
      },
      domObj: dom,
      objectType: "layouts",
      selectedView: "table",
      templates: false,
      fields: {
        name: {
          title: "Title",
          icon: function (obj) {
            return '<i class="tiny th icon" style="display:inline-block;"></i>';
          },
          type: "title",
          charLimit: 25,
        },
        is_public: {
          title: "Available to Portal",
          view: function (ui, obj) {
            var label = {
              text: "Public",
              icon: "eye",
              color: "blue",
            };

            if (obj.is_public == 0) {
              label.text = "Private";
              label.icon = "eye slash";
              label.color = "grey";
            }

            ui.makeNode("public", "div", {
              text:
                '<i class="' +
                label.icon +
                " " +
                label.color +
                ' icon"></i> ' +
                label.text,
            });
          },
        },
        date_created: {
          title: "Created On",
          type: "date",
          edit: false,
        },
      },
      created_by: {
        title: "Created By",
        view: function (ui, obj) {},
        type: "author",
      },
      is_public: {
        title: "Public in Portal",
        view: function (ui, obj) {},
      },
      where: where,
    };

    if (sb.permissions.isGroupManager(+sb.data.cookie.userId, group)) {
      sb.notify({
        type: "show-collection",
        data: collection,
      });
    }
  }

  function build_layouts_toolList(groupObjectType) {
    var availableTools = filterAdminOnlyViews(
      appConfig[groupObjectType + "Tools"]
    );
    var retTools = [];
    var toolOrder = 0;

    _.each(_.sortBy(availableTools, "name"), function (tool) {
      if (tool.id != "dashboardtool" && tool.id != "hqTool") {
        retTools.push({
          allowed_users: [+sb.data.cookie.get("uid")],
          system_name: tool.id,
          display_name: tool.name,
          is_archieved: 0,
          order: toolOrder,
          added_by: +sb.data.cookie.get("uid"),
          added_on: moment(),
          settings: {},
          box_color: tool.icon.color,
        });

        toolOrder++;
      }
    });

    return retTools;
  }

  return {
    init: function () {
      if (sb.dom.isMobile) {
        defaultCollectionsView = "cards";
      }

      sb.listen({
        "dashboard-redraw": this.redraw,
        "dashboard-tool-boxview-dropped": this.boxViewDropped,
        "dashboard-tool-save-boxview": this.boxViewDropped,
        "dashboard-tool-run": this.run,
        "show-dashboard": this.showDashboard,
        "get-dashboard-menu": this.getDashboardMenu,
        "field-updated": this.fieldsUpdated,
        "show-app-view": this.showAppView,
        "show-app-view-in-right-tray": this.showAppViewInRightTray,
        "show-app-view-picker": this.showAppViewPicker,
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "dashboard",
            title: "Dashboard",
            icon: '<i class="fa fa-tasks"></i>',
            views: [
              // Team Tool
              {
                id: "dashboardtool",
                type: "teamTool",
                name: "Dashboard",
                tip: "The team home page.",
                icon: {
                  type: "th",
                  color: "blue",
                },
                default: true,
                forceView: true,
                settings: false,
                newButton: false,
                boxViews: [
                  {
                    id: "dashboardApps",
                    width: "sixteen",
                    title: "Apps",
                    forceDisplay: true,
                    boxLink: false,
                    dom: function (dom, state, draw) {
                      appsBoxView(dom, state, draw, function (done) {
                        dashboardView(mainDom, mainState, mainDraw);
                      });
                    },
                  },
                ],
              },
              // Project Tool
              {
                id: "dashboardtool",
                type: "tool",
                name: "Dashboard",
                tip: "The project home page.",
                icon: {
                  type: "th",
                  color: "blue",
                },
                default: true,
                forceView: true,
                settings: false,
                newButton: false,
                boxViews: [
                  {
                    id: "dashboardApps",
                    width: "sixteen",
                    title: "Apps",
                    forceDisplay: true,
                    boxLink: false,
                    dom: function (dom, state, draw) {
                      appsBoxView(dom, state, draw, function (done) {
                        dashboardView(mainDom, mainState, mainDraw);
                      });
                    },
                  },
                ],
              },
              // HQ Tool
              {
                id: "dashboardtool",
                type: "hqTool",
                name: "Dashboard",
                tip: "The Headquarters.",
                icon: {
                  type: "th",
                  color: "blue",
                },
                default: true,
                forceView: true,
                settings: false,
                newButton: false,
                boxViews: [
                  {
                    id: "dashboardApps",
                    width: "sixteen",
                    title: "Apps",
                    forceDisplay: true,
                    boxLink: false,
                    dom: function (dom, state, draw) {
                      appsBoxView(dom, state, draw, function (done) {
                        dashboardView(mainDom, mainState, mainDraw);
                      });
                    },
                  },
                ],
              },
              {
                id: "dashboardCreator",
                name: "Dashboard Creator",
                title: "Dashboard Creator",
                layers: ["hq"],
                type: "hqTool",
                tip: "Create dashboards",
                icon: {
                  type: "th",
                  color: "black",
                },
                default: true,
                allowMulti: true,
                settings: false,
                mainViews: [
                  {
                    dom: function (dom, state, draw) {
                      dom.makeNode("title", "div", {
                        css: "ui huge header",
                        text: state._toolId,
                      });

                      dom.makeNode("cont", "div", {});

                      draw({
                        dom: dom,
                        after: function (dom) {
                          state.parent = state.pageObject;
                          state.setId =
                            state._config.system_name + "." + state._config.id;
                          state.setTags = [state.pageObject.id];

                          sb.notify({
                            type: "show-dashboard",
                            data: {
                              dom: dom.cont,
                              state: state,
                              draw: function (data) {
                                data.after(dom.cont);

                                dom.patch();
                              },
                            },
                          });
                        },
                      });
                    },
                  },
                ],
                boxViews: [],
              },
              // MyStuff Tool
              {
                id: "dashboardtool",
                type: "myStuff",
                name: "Dashboard",
                tip: "My Stuff.",
                icon: {
                  type: "th",
                  color: "blue",
                },
                default: true,
                forceView: true,
                settings: false,
                newButton: false,
                boxViews: [
                  {
                    id: "dashboardApps",
                    width: "sixteen",
                    title: "Apps",
                    forceDisplay: true,
                    boxLink: false,
                    dom: function (dom, state, draw) {
                      appsBoxView(dom, state, draw, function (done) {
                        dashboardView(mainDom, mainState, mainDraw);
                      });
                    },
                  },
                ],
              },
            ],
          },
        },
      });

      sb.notify({
        type: "register-tool",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "dashboardBuilder",
            title: "Dashboard Template Builder",
            icon: '<i class="th icon"></i>',
            views: [
              {
                id: "dashboardBuilder",
                name: "Dashboard Template Builder",
                title: "Dashboard Template Builder",
                layers: ["hq"],
                tip: "Manager your User's Dashboard Layouts",
                icon: {
                  type: "th",
                  color: "black",
                },
                default: true,
                settings: false,
                mainViews: [
                  {
                    dom: build_layouts_collection,
                  },
                ],
                boxViews: [],
              },
              {
                id: "layouts-obj",
                type: "object-view",
                title: "Dashboard Layout",
                icon: '<i class="th icon"></i>',
                default: true,
                header: {
                  name: true,
                  subTitle: function (ui, object) {
                    sb.notify({
                      type: "view-field",
                      data: {
                        type: "detail",
                        property: "description",
                        obj: object,
                        ui: ui,
                        options: {
                          edit: true,
                        },
                      },
                    });
                  },
                  tags: true,
                  menu: {
                    templates: false,
                  },
                  actions: {
                    togglePublic: {
                      title: "Public",
                      icon: "eye",
                      color: "primary",
                      view: function (obj) {
                        if (obj.is_public) {
                          return {
                            title: "Public",
                            icon: "eye",
                            color: "blue",
                          };
                        } else {
                          return {
                            title: "Private",
                            icon: "eye slash",
                            color: "grey",
                          };
                        }
                      },
                      action: function (obj, state, callback) {
                        sb.data.db.obj.update(
                          obj.object_bp_type,
                          {
                            id: obj.id,
                            is_public: !obj.is_public,
                          },
                          function (response) {
                            obj.is_public = response.is_public;
                            callback(obj);
                          }
                        );
                      },
                    },
                    archive: true,
                  },
                  select: {
                    name: true,
                  },
                  canEdit: true,
                },
                dom: function (dom, state, draw) {
                  if (state.pageObjectType == "layouts") {
                    var toolsList = build_layouts_toolList("myStuff");

                    state.myStuff = state.pageObject;
                    state.myStuff.group_type = "MyStuff";
                    state.myStuff.tools = toolsList;
                  }

                  if (appConfig.is_portal) state.portal = true;

                  dashboardView(dom, state, draw);
                },
                menu: function (state, draw, layer) {
                  var groupObj = state.headquarters;

                  if (state.myStuff) {
                    groupObj = state.myStuff;
                  }

                  if (groupObj) {
                    sb.notify({
                      type: "get-dashboard-menu",
                      data: {
                        draw: draw,
                        group: groupObj,
                        layer: layer,
                      },
                    });
                  }
                },
              },
            ],
          },
        },
      });
    },

    destroy: function () {
      _.each(comps, function (comp) {
        comp.destroy();
      });

      comps = {};
    },

    redraw: function (data) {
      var updateToolsBar = false;

      if (
        data.hasOwnProperty("object") &&
        data.object.hasOwnProperty("id") &&
        data.object.hasOwnProperty("tools")
      ) {
        mainState = appConfig.state;

        sb.notify({
          type: "refresh-toolBar",
          data: {
            state: mainState,
            after: function () {
              dashboardView(mainDom, mainState, mainDraw, function (done) {});
            },
            toolbarOpen: data.toolbarOpen,
          },
        });
      } else {
        if (mainState.appSettings) {
          dashboardView(mainDom, mainState, mainDraw, function (done) {});
        }
      }
    },

    run: function (data) {
      data.run(data);
    },

    boxViewDropped: function (data) {
      var grid = $(data.dropped.grid.selector).children();
      var layout = [];
      var layoutId = 0;

      if (grid.length > 0) {
        _.each(grid, function (gridItem, count) {
          layout.push({
            boxView: $(gridItem).data("boxid"),
            tool: $(gridItem).data("toolid"),
            uid: $(gridItem).data("uid"),
            toolId: $(gridItem).data("grouptoolid"),
          });

          layoutId = $(gridItem).data("layoutid");
        });
      } else {
        layoutId = data.layoutId;
      }

      var updLayout = { id: layoutId, layout: layout };

      function updateSettings(boxView) {
        var updSettings = [];
        if (boxView.uid) {
          _.each(boxView.layout.settings, function (set) {
            if (set.uid != boxView.uid) {
              updSettings.push(set);
            }
          });
        }

        return updSettings;
      }

      if (data.boxView && data.boxView.layout) {
        updLayout.settings = updateSettings(data.boxView);
      }

      if (layoutId) {
        sb.data.db.obj.update("layouts", updLayout, function (saved) {
          if (data.callback) {
            data.callback(saved);
          }
        });
      } else {
        if (data.callback) {
          data.callback();
        }
      }
    },

    showDashboard: function (data) {
      mainDom = data.dom;
      mainState = data.state;
      mainDraw = data.draw;

      dashboardView(
        data.dom,
        data.state,
        data.draw,
        function (done) {},
        data.appViews,
        data.newAppViews
      );
    },

    getDashboardMenu: function (data) {
      var menuItems = getPageMenuItems(data.group, data.layer);

      menuItems.refresh = getPageMenuItems.bind(data.group, data.layer);

      data.draw(menuItems);
    },

    fieldsUpdated: function (data) {
      var currentPageObj = {};

      if (data.hasOwnProperty("obj")) {
        if (data.obj.group_type == "Headquarters") {
          currentPageObj.id = data.obj.id;
          currentPageObj.object_bp_type = "groups";
          currentPageObj.group_type = "Headquarters";
          redrawPage(this, data);
        } else if (data.obj.group_type == "MyStuff") {
          currentPageObj.id = data.obj.id;
          currentPageObj.object_bp_type = "groups";
          currentPageObj.group_type = "MyStuff";
          redrawPage(this, data);
        } else if (data.obj.group_type == "Team") {
          currentPageObj.id = data.obj.id;
          currentPageObj.object_bp_type = "groups";
          currentPageObj.group_type = "team";
          redrawPage(this, data);
        } else if (data.obj.group_type == "Project") {
          currentPageObj.id = data.obj.id;
          currentPageObj.object_bp_type = "groups";
          currentPageObj.group_type = "project";
          redrawPage(this, data);
        }
      }

      function redrawPage(returnObj, data) {
        var ret = {};

        ret[data.property] = data.obj[data.property];
        ret.object = data.obj;

        if (data.hasOwnProperty("toolbarOpen")) {
          ret.toolbarOpen = data.toolbarOpen;
        } else {
          ret.toolbarOpen = false;
        }

        returnObj.redraw(ret);
      }
    },

    showAppView: function (data) {
      showAppView(data.dom, data.boxView, data.systemBoxView, data.state);
    },

    showAppViewInRightTray: function (data) {
      showAppViewInRightTray(
        cachedSelectedTrayView.boxView,
        cachedSelectedTrayView.systemBoxView
      );
    },

    showAppViewPicker: function (data) {
      showAppViewPicker(data.ui, data.state, data.layout, data.callback);
    },
  };
});
