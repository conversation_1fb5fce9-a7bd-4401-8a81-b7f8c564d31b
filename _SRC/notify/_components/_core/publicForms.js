Factory.register('publicForms', function(sb) {
	
	var listData = [];

	function viewList (ui) {

		function viewItemInModal (item) {
			
			sb.notify({
				type: 	'get-sys-modal'
				, data: 	{
					callback: 	function (modal) {
						
						modal.body.empty();
						modal.show();
						
						modal.body.makeNode('menu', 'div', {
							css: 'ui secondary right floated menu',
							style: 'margin:0 !important;'
						});
						
						var linkSetup = { 
							css:'circular icon button item', 
							text:'<i class="external square alternate icon"></i>', 
							tag:'a',
							href:sb.data.url.getObjectPageParams(item, {})
						};
			
						var closeLinkSetup = { 
							css:'circular icon button item', 
							text:'<i class="close icon"></i>', 
							tag:'a'
						};
						
						modal.body.menu.makeNode('open', 'div', linkSetup);
						
						modal.body.menu.makeNode('close', 'div', closeLinkSetup)
							.listeners.push(
								
								function (selector) {
									
									$(selector).on(
										'click'
										, function () {
											modal.hide();
										}
									);
									
								}
							);
						
						modal.body.makeNode('c', 'div', {});
						modal.body.patch();
						
						sb.notify({
							type: 'view-entity'
							, data: {
								ui: 				modal.body.c
								, id: 				item.id
								, hideComments: 	true
								, hideTags: 		true
							}
						});
						
					}
				}
			});
			
		}

		var fields = {
			name: {
				title: 'Name'
				, type: 'title'
				, view: function(ui, obj){

					sb.notify({
						type: 'view-field'
						, data: {
							ui: ui
							, type: 'title'
							, obj: obj
							, fieldName: 'name'
							, property: 'name'
							, options: {
								inCollection: true
								, onClick: function (obj) {
									
									viewItemInModal(obj);
									
								}
							}
						}
					});
					
				}
			}
		};

		sb.notify({
			type:'field-list-view'
			, data: {
				container: 	ui
				, list: 	listData
				, options: {
					actions: 		true
					, actions_ui: 	function (container, nestedObj) {
						
						var css = '';
						var menuCss = 'left menu';
						var actionButtonCss = 'ui mini basic circular icon simple dropdown';
						var actionButtonStyle = 'border:none;';
						var menuStyle = '';
		
						container.makeNode('actions', 'div', {css:css});
						container.actions.makeNode('menu', 'div', {
							css:'right floated ui mini icon circular basic simple dropdown',
							text:'<i class="ellipsis horizontal centered icon" style="font-size:1.5em !important;"></i>',
							style:'text-align:center;'
						}).makeNode('menu', 'div'
							, {
								css: 		menuCss
// 									, style: 	'left: -100px;'
							}
						);
						
						// Archive
						container.actions.menu.menu.makeNode(
							'archive'
							, 'div'
							, {
								css: 		'ui red item'
								, text: 	'<i class="red trash icon"></i> Archive'
								, style: 	'border-radius:0px;'
							}
						).listeners.push(
							
							function (s) {
								
								$(s).on('click', function (e) {
									
									e.stopPropagation();
									
									sb.dom.alerts.ask({
										title: 	'Archive '+ nestedObj.name +'?'
										, text: ''
									}, function (response) {
										
										if (response) {
											
											swal.disableButtons();
											
											sb.data.db.obj.erase(
												nestedObj.object_bp_type
												, nestedObj.id
												, function (res) {

													swal.close();
													
													listData = _.filter(
														listData
														, function (item) {
															return item.id !== nestedObj.id;
														}
													);

													ui.empty();
													viewList(ui);
													ui.patch();

												}
											);
											
										} else {
											
											swal.close();
											
										}
										
									});
									
								});
								
							}
							
						);
						container.patch();
						
					}
					, create_ui: function (createBtnContainer) {}
					, emptyMessage: false
					, fields: fields
					, updateSelection: function () {}
					, style: 'padding:0px;'
					, showDate: true
					, showQuantity: true
					, showMultiplePerType: true
					, groupStyle: 'margin:0px;border-none;'
				}
			}				
		});

	}

    return {

        init: function() {

            sb.listen({
				'display-public-form': this.display,
				'publicForms-run': this.run
			});

        },

        display: function(setup) {
			
			var instance = sb.data.url.getParams().i;
            var project = sb.data.url.getParams().p;
            var formType = '#'+sb.data.url.getParams().f;
			var dom = setup.ui;
            
            dom.makeNode('header', 'div', {});
            dom.header.makeNode('menu', 'div', {css:'ui black inverted menu'});
            
            dom.makeNode('cont', 'div', {css:'ui padded segment'});
            dom.makeNode('modalCont', 'div', {});
            
            dom.patch();
            
            sb.data.db.obj.getAll('entity_type', function (entityTypes) {

				sb.notify({
					type: 'entity-types-received'
					, data: {
						entityTypes: entityTypes
					}
				});
					
	            sb.data.db.obj.getWhere('groups', {group_type:'Headquarters', childObjs:{name:true}}, function(hqs){
		            
		            sb.data.db.obj.getById('groups', +project, function(project){
		            	
						var hq = hqs[0];
						var blueprint = _.findWhere(entityTypes, {bp_name: formType.substr(1)});
						
						var desc = '';
						
						if (blueprint.description)
							desc = blueprint.description;
						
						dom.header.menu.makeNode('item', 'div', {css:'ui header item', text:hq.name +' <i class="angle right icon"></i> '+ project.name});    
						
						dom.header.menu.patch();  
						
						dom.cont.makeNode('title', 'div', {text:blueprint.name +'<div class="sub header">'+ desc +'</div>', css:'ui huge header'});

						dom.cont.makeNode('button', 'div', {text:'Create New Item <i class="plus icon"></i>', css:'ui icon green button'})
							.notify('click', {
								type:'publicForms-run',
								data:{
									run:function() {

										dom.cont.button.loading();
										
										sb.notify({
								            type:'create-new-entity',
								            data:{
									            type:formType,
									            ui:dom.modalCont,
									            seed:{
										            object_bp_type:formType,
										            tagged_with:[hq.id, project.id]
									            },
									            onComplete:function(newObj){

													dom.cont.button.loading(false);
													
													sb.dom.alerts.alert(blueprint.name+' created!', 'Click the new button to create another one.', 'success');
													
													// Update the list
													listData.push(newObj);
													viewList(dom.cont.list);
													dom.cont.patch();
										            
									            }
								            }
							            });
										
									}
								}
							}, sb.moduleId);

						dom.cont.makeNode('list', 'div', {});
						
						dom.cont.patch();
						
						sb.notify({
				            type:'create-new-entity',
				            data:{
					            type:formType,
					            ui:dom.modalCont,
					            seed:{
						            object_bp_type:formType,
						            tagged_with:[hq.id, project.id]
					            },
					            onComplete:function(newObj){
						            			
									sb.dom.alerts.alert(blueprint.name+' created!', 'Click the new button to create another one.', 'success');		            
									
									// Update the list
									listData.push(newObj);
									viewList(dom.cont.list);
									dom.cont.patch();
									
					            }
				            }
			            });
						
					});   
		            
	            });
            
            });            

        },

        run: function(data) {

            data.run(data);

        }

    }

});