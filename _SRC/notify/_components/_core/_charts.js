Factory.registerComponent('chartComponent', function(sb){
	
	var blueprint = {},
		blueprintType = '',
		buildObj = {},
		chartLabels = '',
		chartSetup = {},
		chartType = '',
		chartTypes = [
			{
				name:'Line',
				value:'line'
			},
			{
				name:'Pie',
				value:'pie'
			},
			{
				name:'Bar',
				value:'bar'
			}
		],
		comps = {},
		domObj = null,
		endRange = moment(),
		objects = [],
		objectType = [],
		popoverDate = {},
		popoverFilter = {},
		popoverGroupBy = {},
		range = {},
		startRange = moment().subtract(7, 'day');
	
	function buildChart(setup, objs){
		
		if(setup.hasOwnProperty('title')){
			var title = {
			        text:setup.title,
			        display:true
		        };
		}else{
			var title = {
			        display:false
		        };
		}
		
		var chartSetupObj = {};
			
		switch(setup.chartType){
			
			case 'line':
				
				var chartSetup = {
						type:setup.chartType,
						data:{
							labels: _.pluck(objs, 'grouped'),
					        datasets: [{
						        label: setup.data.toUpperCase(),
						        lineTension: 0,
					            data: _.pluck(objs, setup.data)
					        }]
						},
						options: {
							bezierCurve: false,
						    scales: {
					            xAxes: [{
						            type: 'time',
					                time: {}
					            }],
					            yAxes: [{
					                stacked:true,
					                ticks: {
					                    beginAtZero:true
					                }
					            }]
					        },
					        title:title
					    }
				    };
				
				break;
				
			case 'pie':
				
				var chartSetup = {
						type:setup.chartType,
						data:{
							labels: ['red', 'yellow', 'blue'],
					        datasets: [{
						        label: setup.data.toUpperCase(),
						        lineTension: 0,
					            data: [10,20,30]
					        }]
						},
						options: {
							bezierCurve: false,
						    scales: {
					            xAxes: [{
						            type: 'time',
					                time: {}
					            }],
					            yAxes: [{
					                stacked:true,
					                ticks: {
					                    beginAtZero:true
					                }
					            }]
					        },
					        title:title
					    }
				    };
				
				break;	
			
		}
		    
		var tableSetup = {
				buttons: {
					create: false,
					view: false,
					edit: false,
					erase: false
				},
				cells: {
					avg:function(obj){
						return Math.round(obj.avg, 2);
					}
				},
				cols: {},
				data: objs,
				dataTables:{
					footerCallback: function ( row, data, start, end, display ) {

			            var api = this.api(), data;
			 
			            // Remove the formatting to get integer data for summation
			            var intVal = function ( i ) {
			                return typeof i === 'string' ?
			                    i.replace(/[\$,]/g, '')*1 :
			                    typeof i === 'number' ?
			                        i : 0;
			            };
			 
			            // Total over all pages
			            total = api
			                .column( 4 )
			                .data()
			                .reduce( function (a, b) {
			                    return intVal(a) + intVal(b);
			                }, 0 );
			 
			            // Total over this page
			            pageTotal = api
			                .column( 4, { page: 'current'} )
			                .data()
			                .reduce( function (a, b) {
			                    return intVal(a) + intVal(b);
			                }, 0 );
			 
			            // Update footer
			            $( api.column( 4 ).footer() ).html(
			                '$'+pageTotal +' ( $'+ total +' total)'
			            );
			        }
				},
				headerButtons:[],
				visibleCols: ['grouped', setup.data]
			};
		
		printChart(chartSetup, tableSetup);

		
	}
	
	function generateSetupForm(blueprint, formData){
		
		var dataMeasurements =[
				{
					name:'Count',
					value:'grouped_total'
				},
				{
					name:'Sum',
					value:'sum'
				},
				{
					name:'Average',
					value:'avg'
				}
			];
				
		var formSetup = {
				chartType:{
					type:'select',
					name:'chartType',
					label:'Chart Style',
					options:[]
				},
				line1: {
					type: 'section',
					name: 'line1',
					label: 'Line 1',
					fields: {
						objectType: {
							type:'select',
							name:'objectType',
							label:'Object Type',
							options:[]
						},
						data: {
							type: 'select',
							name: 'data',
							label: 'Field',
							options: [
								{
									name:'Select One',
									value:''
								}
							]
						},
						measurement: {
							type: 'select',
							name: 'measurement',
							label: 'Measurement',
							options: []
						}
					}
				}
			};
			
		if(!formData){
			var formType = 'line';
		}else{
			var formType = formData.fields.chartType.value;
		}
		
		_.each(chartTypes, function(chartType){
			
			var pushObj = {
					name:chartType.name,
					value:chartType.value
				};
				
			if(formType == chartType.value){
				pushObj.selected = true;
			}	
			
			formSetup.chartType.options.push(pushObj);
			
		});
		
		switch(formType){
			
			case 'line':
			
				_.each(objectType, function(objType){
					
					var pushObj = {
							name:objType,
							value:objType
						};
						
					if(blueprintType == objType){
						pushObj.selected = true;
					}	
					
					formSetup.line1.fields.objectType.options.push(pushObj);
					
				});
			
				break;
			
		}
		
		_.each(blueprint, function(fieldObj, fieldName){

			var pushObj = {
					name:fieldObj.name,
					value:fieldName
				};
			
			if(formData){
				if(formData.fields.data.value == fieldName){					
					pushObj.selected = true;
				}
			}
			
			switch(formType){
				
				case 'line':
					
					if(fieldObj.type == 'int'){
						formSetup.line1.fields.data.options.push(pushObj);
					}
				
					break;
		
				case 'pie':
				
					if(fieldObj.type == 'int'){
						formSetup.line1.fields.data.options.push(pushObj);
					}
				
					break;
					
				default:
					
					formSetup.yAxis = {
							type: 'section',
							name: 'yAxis',
							label: 'Y Axis',
							fields: {
								yAxisData: {
									type: 'select',
									name: 'yAxisData',
									label: 'Select one',
									options: [
										{
											name:'Select One',
											value:''
										}
									]
								}
							}
						}
					
					var yPushObj = {
							name:fieldObj.name,
							value:fieldName
						};
					
					if(formData.fields.yAxisData){
						if(formData.fields.yAxisData.value == fieldName){					
							yPushObj.selected = true;
						}
					}	
							
					formSetup.yAxis.fields.yAxisData.options.push(yPushObj);
				
			}	
			
		});
		
		if(formData){

			var line1 = blueprint[formData.fields.data.value];

			if(line1){
				
				switch(line1.type){
				
					case 'int':
						
						_.each(dataMeasurements, function(measure){
							
							if(formData.fields.hasOwnProperty('measurement')){
								
								if(formData.fields.measurement.value == measure.value){
									measure.selected = true;
								}
								
							}
							
							formSetup.line1.fields.measurement.options.push(measure);
							
						});				
											
						break;
					
				}
				
			}
			
		}			
				
		return formSetup;
		
	}
	
	function getChartData(setup, callback){
		
		sb.data.db.controller('sum', {
			dateRange:{
				start:startRange,
				end:endRange
			},
			objectType:blueprintType,
			field:setup.field,
			groupBy:setup.groupBy,
			limit: 365
		}, function(objs){
		
			callback(objs);
		
		});
	}
		
	function printChart(chartData, tableData){
				
		domObj.filterCol.container.makeNode('panel', 'container', {css:'pda-Panel'});
		
		domObj.filterCol.container.panel.makeNode('col1', 'column', {width:4, offset:3}).makeNode('date', 'form', {date:{type:'text',name:'date', label:''}});
		
		domObj.filterCol.container.panel.makeNode('col2', 'column', {width:2}).makeNode('btnGroup', 'buttonGroup', {css:'pull-left'});
		
		domObj.filterCol.container.panel.makeNode('col3', 'column', {width:12}).makeNode('btnGroup2', 'buttonGroup', {css:'text-center'});
		
		_.each(blueprint, function(field, fieldKey){
			
			switch(field.type){
						
				case 'select':
					
					domObj.filterCol.container.panel.col3.btnGroup2.makeNode(fieldKey, 'button', {css:'btn-warning', text:field.name +' <i class="fa fa-bars"></i>'}).notify('click', {
						type:'change-chart-field-filter',
						data:{
							btnSelector:domObj.filterCol.container.panel.col3.btnGroup2[fieldKey].selector,
							filterType:fieldKey
						}
					}, sb.moduleId);
					
					break;
				
			}
			
		});
						
/*
		domObj.filterCol.container.panel.btnGroup.makeNode('date', 'button', {css:'btn-primary', text:moment().subtract(7, 'days').format('MMMM Do YYYY')+' - '+moment().format('MMMM Do YYYY') +' <i class="fa fa-bars"></i>'}).notify('click', {
			type:'change-chart-date-filter',
			data:{}
		}, sb.moduleId);
*/
		
		domObj.filterCol.container.panel.col2.btnGroup.makeNode('groupBy', 'button', {css:'btn-primary', text:'BY DAY'}).notify('click', {
			type:'change-group-by-filter',
			data:{}
		}, sb.moduleId);
		
		domObj.filterCol.container.panel.col3.btnGroup2.makeNode('save', 'button', {css:'btn-success', text:'Save'}).notify('click', {
			type:'save-chart-template',
			data:{}
		}, sb.moduleId);
		
		domObj.filterCol.patch();
		
		domObj.chartCol.container.makeNode('chart', 'chart', chartData);
		
		domObj.chartCol.container.makeNode('tableBreak', 'lineBreak', {spaces:2});
		
		domObj.chartCol.container.makeNode('tableContainer', 'container', {});
		
		domObj.chartCol.container.patch();

		$(domObj.filterCol.container.panel.col1.date.date.selector).daterangepicker({
		    "showDropdowns": false,
		    "ranges": {
		        "Today": [moment(),moment()],
		        "Yesterday": [moment().subtract(1, 'day'), moment().subtract(1, 'day')],
		        "Last 7 Days": [moment().subtract(7, 'day'), moment()],
		        "Last 30 Days": [moment().subtract(30, 'day'), moment()],
		        "This Month": [moment().startOf('month'), moment().endOf('month')],
		        "This Year": [moment().month(0).startOf('month'),moment()],
		        "Last Month": [moment().subtract(1, 'month').startOf('month'),moment().subtract(1, 'month').endOf('month')],
		        "Last Year": [moment().subtract(1, 'year').month(0).startOf('month'),moment().subtract(1, 'year').month(11).endOf('month')]
		    },
		    "alwaysShowCalendars": true,
		    "startDate":startRange,
		    "endDate":endRange,
			"opens":"left"
		}, function(start, end, label) {
			
			startRange = start;
			endRange = end;
			
			getChartData({field:buildObj.field, groupBy:buildObj.groupBy}, function(data){
				
				buildChart(buildObj, data);
				
			});
			
		});
		
		tableData.domObj = domObj.chartCol.container.tableContainer;
		
		comps.table = sb.createComponent('crudPaged');
		comps.table.notify({
			type:'display-crud-table-paged',
			data:tableData
		});
					
	}
								
	return{
		
		init: function(){

			sb.listen({
				'build-table-button-clicked':this.buildTableButtonClicked,
				'display-chart-builder':this.displayChartBuilder,
				'display-line-chart':this.displayLineChart,
				'display-pivot-table-builder':this.displayPivotTableBuilder
			});
			
		},
		
		buildTableButtonClicked: function(setup){
			
			var formData = setup.form.process(),
				startDateRange = setup.dateRange.startDate,
				endDateRange = setup.dateRange.endDate;
								
			setup.button.makeNode('button', 'button', {css:'btn-primary', text:'Generating <i class="fa fa-circle-o-notch fa-spin"></i>'});
			
			setup.button.patch();
			
console.log(formData, startDateRange, endDateRange);
			
			sb.data.db.obj.getWhereBy(formData.fields.objectType.value, {
				date_created:{
					type:'between',
					start:startDateRange.format(),
					end:endDateRange.format()
				},
				groupBy:formData.fields.filter.value
			}, function(objs){
				
console.log(objs);				
				
			});
			
			sb.data.db.controller('metrics', {
				dateRange:{
					start:startDateRange,
					end:endDateRange
				},
				objectType:formData.fields.blueprintType.value,
				field:formData.fields.values.value,
				dateBy:'day',
				groupBy:formData.fields.rowLabels.value,
				calculation:formData.fields.calculation.value,
				limit: 365
			}, function(objs){

				var tableObjs = [];
				
				_.each(objs, function(obj){
					
					var pushObj = {};

					_.each(obj, function(val, key){

						switch(key){
							
							case 'grouped':
							
								pushObj[key.toUpperCase()] = val;
							
								break;
								
							case formData.fields.calculation.value:
							
								pushObj[formData.fields.calculation.value.toUpperCase()] = val
							
								break;
								
							case formData.fields.rowLabels.value:
							
								pushObj[blueprint[formData.fields.rowLabels.value].name.toUpperCase()] = val;
							
								break;	
															
						}
						
					});
					
					tableObjs.push(pushObj);
					
				});
				
				var tableDom = sb.dom.make(setup.domObj.selector);	
					
				comps.table = sb.createComponent('crudPaged');
				comps.table.notify({
					type:'display-crud-table-paged',
					data:{
						buttons: {
							create: false,
							view: false,
							edit: false,
							erase: false
						},
						cells: {},
						cols: {},
						data: tableObjs,
						domObj:setup.domObj,
						dataTables:{},
						headerButtons:[],
						visibleCols: []
					}
				});
				
				setup.button.makeNode('button', 'button', {css:'btn-primary', text:'Apply Changes <i class="fa fa-tasks"></i>'}).notify('click', {
					type:'build-table-button-clicked',
					data:{
						dateRange:range,
						form:domObj.setupCol.panel.body.form,
						button:domObj.setupCol.panel.body.formButton,
						domObj:domObj.chartCol.container
					}
				}, sb.moduleId);;
			
				setup.button.patch();
				
				setup.filterDom.makeNode('buttonGroup', 'buttonGroup', {});
				
				setup.filterDom.buttonGroup.makeNode('filterBtn', 'button', {css:'btn-primary', text:formData.fields.filter.value.toUpperCase() + ' (all) <i class="fa fa-filter"></i>'});
				
				setup.filterDom.makeNode('pop', 'popover', {parent: setup.filterDom.buttonGroup.filterBtn}).makeNode('btn', 'button', {text: 'testing', css: 'btn-success'});
								
				setup.filterDom.patch();
			
			});
			
		},
				
		destroy: function(){
			
			_.each(comps, function(comp){
				comp.destroy();
			});
			
			sb.listen({
				'display-chart-builder':this.displayChartBuilder,
				'display-line-chart':this.displayLineChart
			});

			chartLabels = '',
			chartSetup = {},
			chartType = '',
			comps = {},
			domObj = null,
			objects = [],
			objectType = '';
			
		},
		
		applySetupForm: function(setup){
			
			var formData = setup.form.process();
			
			buildObj = {
					field:formData.fields.data.value,
					chartType:formData.fields.chartType.value,
					data:formData.fields.measurement.value,
					objectType:setup.objectType,
					groupBy:'day'
				};
				
			getChartData({groupBy:buildObj.groupBy, field:buildObj.field}, function(data){
				
				buildChart(buildObj, data);
				
			});	
			
		},
				
		changeFieldFilter: function(setup){
			
			if(_.isEmpty(popoverFilter)){
				
				if(popoverDate){
					popoverDate = {};
				}
								
				popoverFilter = $(setup.btnSelector).webuiPopover({
					title:'Options',
					content:'',
					placement:'bottom-left',
					offsetLeft:0,
					offsetTop:0,
					position:'right',
					trigger:'manual',
					backdrop:false,
					closeable:false,
					animation:'pop',
					width:150
				});
				
				popoverFilter.webuiPopover('show');
				
				var popDom = sb.dom.make('.webui-popover-content'),
					formSetup = {};
				
				formSetup[setup.filterType] = {
					type: 'select',
					name: setup.filterType,
					label: blueprint[setup.filterType].name,
					options: []
				
				};
				
				formSetup[setup.filterType].options.push({
					name:'All',
					value:'All',
					selected:true
				});
				
				_.each(blueprint[setup.filterType].options, function(opt){
					
					formSetup[setup.filterType].options.push({
						name:opt,
						value:opt
					});
					
				});
				
				popDom.makeNode('form', 'form', formSetup);
				
				popDom.makeNode('apply', 'button', {css:'btn-primary pull-right', text:'Apply'}).notify('click', {
					type:'',
					data:{}
				}, sb.moduleId);
								
				popDom.build();
				
			}else{
				
				popoverFilter.webuiPopover('hide');
				popoverFilter = {};
				
			}

		},
		
		changeGroupByFilter: function(setup){
			
			if(_.isEmpty(popoverGroupBy)){
				
				popoverGroupBy = $(domObj.filterCol.container.panel.col2.btnGroup.groupBy.selector).webuiPopover({
					title:'Choose a date range',
					content:'<div class="row"><div class="col-sm-12 chart-group-by-filter-button"></div></div>',
					placement:'bottom-left',
					offsetLeft:0,
					offsetTop:0,
					position:'right',
					trigger:'manual',
					backdrop:false,
					closeable:false,
					animation:'pop'
				});
				
				popoverGroupBy.on('shown.webui.popover', function(){
					
					var popDom = sb.dom.make('.chart-group-by-filter-button'),
						groupByBtns = ['day', 'week', 'month', 'year'];
					
					popDom.makeNode('buttonGroup', 'buttonGroup', {css:'pull-right'});
					
					_.each(groupByBtns, function(btn){
						
						popDom.buttonGroup.makeNode(btn, 'button', {text:btn.toUpperCase(), css:'btn-primary'}).notify('click', {
							type:'group-type-changed',
							data:{
								groupBy:btn
							}
						}, sb.moduleId);
						
					});
											
					popDom.build();	
					
				});
				
				popoverGroupBy.webuiPopover('show');
				
			}else{
				
				popoverGroupBy.webuiPopover('hide');
				
				popoverGroupBy.off('show.webui.popover');
				
				popoverGroupBy = {};
				
			}
			
		},
		
		displayChartBuilder: function(setup){
			
			var listeners = {
					'apply-chart-setup-form':this.applySetupForm,
					'change-chart-date-filter':this.changeDateFilter,
					'change-chart-field-filter':this.changeFieldFilter,
					'change-group-by-filter':this.changeGroupByFilter,
					'chart-setup-form-updated':this.setupFormUpdated,
					'display-chart-builder':this.displayChartBuilder,
					'display-line-chart':this.displayChart,
					'group-type-changed':this.groupTypeChanged,
					'save-chart-template':this.saveTemplate,
					'update-line-chart': this.updateChart
				};
													
			sb.listen(listeners);
			
			objectType = setup.objectType;
			
			sb.data.db.obj.getBlueprint(setup.objectType[0], function(bp){
				
				blueprintType = setup.objectType[0];
				blueprint = bp;
				
				domObj = sb.dom.make(setup.domObj.selector);
				
				domObj.makeNode('modals', 'container', {});
				
				domObj.makeNode('setupCol', 'column', {width:3}).makeNode('panel', 'panel', {header:'Chart Settings'});
				domObj.makeNode('filterCol', 'column', {width:9}).makeNode('container', 'container', {css:'pda-container'});
				domObj.makeNode('chartCol', 'column', {width:9, css:''}).makeNode('container', 'container', {css:'pda-container'});	
								
				domObj.setupCol.panel.body.makeNode('break', 'lineBreak', {spaces:1});
					
				domObj.setupCol.panel.body.makeNode('form', 'form', generateSetupForm(blueprint)).notify('change', {
					type:'chart-setup-form-updated',
					data:{
						form:domObj.setupCol.panel.body.form,
						objectType:setup.objectType
					}
				}, sb.moduleId);
								
				domObj.build();			
				
			});
			
		},
		
		displayPivotTableBuilder: function(setup){
			
			var listeners = {
					'chartComponent-destroy': this.destroy,
					'chart-setup-form-updated':this.setupFormUpdated,
					'display-pivot-table-builder':this.displayPivotTableBuilder
				};
													
			sb.listen(listeners);
			
			sb.data.db.obj.getBlueprint(setup.objectType[0], function(bp){
				
				blueprintType = setup.objectType[0];
				blueprint = bp;
				
				domObj = sb.dom.make(setup.domObj.selector);
				
				domObj.makeNode('modals', 'container', {});
				
				domObj.makeNode('setupCol', 'column', {width:3}).makeNode('panel', 'panel', {header:'<i class="fa fa-table"></i> Table Settings'});
				domObj.makeNode('filterCol', 'column', {width:9}).makeNode('container', 'container', {css:'pda-container'});
				domObj.makeNode('chartCol', 'column', {width:9, css:''}).makeNode('container', 'container', {css:'pda-container'});	
								
				domObj.setupCol.panel.body.makeNode('break', 'lineBreak', {spaces:1});
				
				var formSetup = {
						objectType:{
							type:'select',
							name:'objectType',
							options:[],
							label:'Object Type'
						},
						dateRange:{
							type:'text',
							name:'dateRange',
							label:'Date Range',
						},
						rowLabels:{
							type:'select',
							name:'rowLabels',
							label:'Row Labels',
							options:[]
						},
						filter:{
							type:'select',
							name:'filter',
							label:'Filter',
							options:[]
						},
						columnLabels:{
							type:'select',
							name:'columnLabels',
							label:'Column Labels',
							options:[]
						},
						blueprintType:{
							type:'hidden',
							name:'blueprintType',
							value:blueprintType
						},
						section:{
							type:'section',
							name:'values',
							label:'Values',
							fields:{
								values:{
									type:'select',
									name:'values',
									label:'Values',
									options:[]
								},
								calculation:{
									type:'select',
									name:'calculation',
									label:'Calculation',
									options:[
										{
											name:'Average',
											value:'avg'
										},
										{
											name:'Count',
											value:'total'
										},
										{
											name:'Sum',
											value:'sum'
										}
									]
								}
							}
						}
					};
					
				_.each(setup.objectType, function(bpType){

					formSetup.objectType.options.push({
						name:bpType.replace('_', ' ').toUpperCase(),
						value:bpType
					});
					
				});
					
				_.each(blueprint, function(field, fieldName){
					
					var pushObj = {
							name:field.name,
							value:fieldName
							
						};
					
					switch(field.type){
						
						case 'date':
						
							formSetup.rowLabels.options.push(pushObj);
						
							break;
						
						case 'int':
						
							formSetup.section.fields.values.options.push(pushObj);
							
							break;
							
						case 'objectId':
						
							//formSetup.filter.options.push(pushObj);
							//formSetup.columnLabels.options.push(pushObj);
							
							break;
						
						case 'select':
						
							formSetup.rowLabels.options.push(pushObj);
						
							break;
							
						case 'string':
						
							switch(fieldName){
								
								case 'country':
								case 'state':
								
									formSetup.filter.options.push(pushObj);
								
									break;
								
							}
							
							break;	
												
					}
					
				});	
				
				domObj.setupCol.panel.body.makeNode('form', 'form', formSetup);
// reattach the this notify after talking with Zach about updating forms				
/*
				.notify('change', {
					type:'chart-setup-form-updated',
					data:{
						form:domObj.setupCol.panel.body.form,
						domObj:domObj.chartCol.container,
						formSetup:formSetup
					}
				}, sb.moduleId);
*/
				
				domObj.setupCol.panel.body.makeNode('formButton', 'container', {});
																	
				domObj.build();
				
				$(domObj.setupCol.panel.body.form.dateRange.selector).daterangepicker({
				    "showDropdowns": false,
				    "ranges": {
				        "Today": [moment(),moment()],
				        "Yesterday": [moment().subtract(1, 'day'), moment().subtract(1, 'day')],
				        "Last 7 Days": [moment().subtract(7, 'day'), moment()],
				        "Last 30 Days": [moment().subtract(30, 'day'), moment()],
				        "This Month": [moment().startOf('month'), moment().endOf('month')],
				        "This Year": [moment().month(0).startOf('month'),moment()],
				        "Last Month": [moment().subtract(1, 'month').startOf('month'),moment().subtract(1, 'month').endOf('month')],
				        "Last Year": [moment().subtract(1, 'year').month(0).startOf('month'),moment().subtract(1, 'year').month(11).endOf('month')]
				    },
				    "alwaysShowCalendars": true,
				    "startDate":startRange,
				    "endDate":endRange,
					"opens":"right"
				});
								
				range = $(domObj.setupCol.panel.body.form.dateRange.selector).data('daterangepicker');
				
				domObj.setupCol.panel.body.formButton.makeNode('button', 'button', {css:'btn-primary', text:'Build Chart <i class="fa fa-tasks"></i>'}).notify('click', {
					type:'build-table-button-clicked',
					data:{
						dateRange:range,
						form:domObj.setupCol.panel.body.form,
						button:domObj.setupCol.panel.body.formButton,
						domObj:domObj.chartCol.container,
						filterDom:domObj.filterCol.container
					}
				}, sb.moduleId);
								
				domObj.setupCol.panel.body.formButton.patch();
								
			});
			
		},
		
		groupTypeChanged: function(setup){
			
			buildObj.groupBy = setup.groupBy;
			
			getChartData({field:buildObj.field, groupBy:buildObj.groupBy}, function(data){
				
				popoverGroupBy.webuiPopover('hide');
				
				popoverGroupBy.off('show.webui.popover');
				
				popoverGroupBy = {};
				
				buildChart(buildObj, data);
				
				domObj.filterCol.container.panel.col2.btnGroup.makeNode('groupBy', 'button', {css:'btn-primary', text:'BY '+buildObj.groupBy.toUpperCase()}).notify('click', {
					type:'change-group-by-filter',
					data:{}
				}, sb.moduleId);
				
				domObj.filterCol.container.panel.col2.btnGroup.patch();
				
			});	
			
		},
										
		saveTemplate: function(setup){
			
console.log(setup);
			
		},
		
		setupFormUpdated: function(setup){
			
			delete domObj.setupCol.panel.body.form;
			delete domObj.setupCol.panel.body.formButton;
			
			var formData = setup.form.process();
			
			if(formData.fields.objectType.value != blueprintType){
				
				sb.data.db.obj.getBlueprint(formData.fields.objectType.value, function(bp){
				
					blueprint = bp;
					blueprintType = formData.fields.objectType.value;

					setup.formSetup.objectType.options = [];
					
					_.each(blueprint, function(bpFieldObj, bpName){
						
						var pushObj = {
								name:bpFieldObj.name,
								value:bpName
							};
						
						if(bpName == blueprintType){
							pushObj.selected = true;
						}
						
						setup.formSetup.objectType.options.push(pushObj);
						
					});
					
					domObj.setupCol.panel.body.makeNode('form', 'form', setup.formSetup ).notify('change', {
						type:'chart-setup-form-updated',
						data:{
							form:domObj.setupCol.panel.body.form,
							objectType:setup.objectType,
							formSetup:setup.formSetup
						}
					}, sb.moduleId);
					
					domObj.setupCol.panel.body.makeNode('formButton', 'button', {text:'Apply <i class="fa fa-angle-double-right"></i>', css:'btn-primary'}).notify('click', {
						type:'apply-chart-setup-form',
						data:{
							form:domObj.setupCol.panel.body.form,
							objectType:setup.objectType,
							formSetup:setup.formSetup
						}
					}, sb.moduleId);
					
					domObj.setupCol.patch();
					
				});
				
			}else{
				
				domObj.setupCol.panel.body.makeNode('form', 'form', setup.formSetup ).notify('change', {
					type:'chart-setup-form-updated',
					data:{
						form:domObj.setupCol.panel.body.form,
						objectType:setup.objectType,
						formSetup:setup.formSetup
					}
				}, sb.moduleId);
				
				domObj.setupCol.panel.body.makeNode('formButton', 'button', {text:'Apply <i class="fa fa-angle-double-right"></i>', css:'btn-primary'}).notify('click', {
					type:'apply-chart-setup-form',
					data:{
						form:domObj.setupCol.panel.body.form,
						objectType:setup.objectType,
						formSetup:setup.formSetup
					}
				}, sb.moduleId);
				
				domObj.setupCol.patch();
				
			}			
						
		}
										
	}
	
});