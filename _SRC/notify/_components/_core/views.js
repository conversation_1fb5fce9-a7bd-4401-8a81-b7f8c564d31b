Factory.registerComponent('views', function (sb) {
	var viewOpts = [
			'_sectionName'
			, '_message'
			, '_description'
			, '_breakAfter'
			, '_labelPos'
		];
	var cachedTabsSetup = {};

	function editConditionalFormats (view, onUpdate, goBack) {

		var AvailableStyles = [
			{
				name: 		'hidden'
				, title: 	'Hidden'
				, icon: 	'grey eye slash icon'
			}
		];

		if (_.isEmpty(view.conditional_formats)) {
			view.conditional_formats = [];
		}

		sb.notify({
			type: 'get-sys-modal'
			, data: {
				callback: 	function (modal) {
					
					function viewRulesList (ui, view) {

						// List of rules
						_.each(view.conditional_formats, function (rule, i) {
							
							ui.makeNode('r-'+ i, 'div', {
								css: 'hoverable'
								, style: 'padding:16px;border-radius:6px;'
							});

							// !Remove button
							ui['r-'+ i].makeNode(
								'rm'
								, 'div'
								, {
									text: 	'<i class="ui trash icon"></i> Remove rule'
									, css: 	'ui red icon button'
								}
							).notify('click', {
								type: 'views-run'
								, data: {
									run: function (index) {

										view.conditional_formats.splice(index, 1);
										ui.empty();
										viewRulesList(ui, view);

									}.bind({}, i)
								}
							});

							// Layout columns
							ui['r-'+ i].makeNode('cols', 'div', {css: 'ui grid'});
							ui['r-'+ i].cols.makeNode('left', 'div', {css: 'eight wide column'});

							// IF
							ui['r-'+ i].cols.left.makeNode('if', 'div', {text: 'If <i class="ui right arrow icon"></i>', tag: 'h1'});

							// Condition
							sb.notify({
								type: 'edit-conditions-on-obj'
								, data: {
									ui: 			ui['r-'+ i].cols.left
									, obj: 			rule
									, onComplete: 	function () {}
									, bp: 			view.blueprint
								}
							});

							// Fields
							ui['r-'+ i].cols.makeNode('center', 'div', {css: 'four wide column'})
								.makeNode('then', 'div', {text: 'Then <i class="ui right arrow icon"></i>', tag: 'h1'});
							
							ui['r-'+ i].cols.center
								.makeNode('fieldsSelection', 'div', {});

							// Format
							ui['r-'+ i].cols.makeNode('right', 'div', {css: 'four wide column'})
							ui['r-'+ i].cols.right
								.makeNode('are', 'div', {text: 'Formatted as', tag: 'h1'});

							ui['r-'+ i].cols.right
								.makeNode('styleSelection', 'form', {
									styleOption: {
										name: 		'styleOption'
										, label: 	''
										, type: 	'select'
										, options: 	_.map(
											AvailableStyles
											, function (style) {
												return {
													value: style.name
													, name: style.title
												};
											}
										)
									}
								});

						});

						ui.patch();

						_.each(view.conditional_formats, function (rule, i) {

							sb.notify({
								type: 'view-field-selection'
								, data: {
									ui: ui['r-'+ i].cols.center.fieldsSelection
									, blueprint: 	view.blueprint
									, options: 		{
										allowedTypes: 	['any']
										, multi: 		true
										, onUpdate: 	function (val) {

											rule.fields = val;
											
										}
										, value: rule.fields
									}
									, state: {}
								}
							});

						});

						ui.patch();

					}

					viewRulesList(modal.body.makeNode('rules', 'div', {}), view);

					// Add new rule button
					modal.body.makeNode('add', 'div', {
						text: '<i class="ui plus icon"></i> Add a new rule'
						, tag: 'button'
						, css: 'ui teal button'
					}).notify(
						'click'
						, {
							type: 'views-run'
							, data: {
								run: function () {

									// Add an empty rule
									view.conditional_formats.push({
										style: 'hidden'
									});

									// Refresh the ui
									modal.body.rules.empty();
									viewRulesList(modal.body.rules, view);
									modal.body.rules.patch();
									return;

								}
							}
						}
					);

					// Save button
					modal.body.makeNode('save', 'div', {
						text: 	'<i class="ui save icon"></i> Save & close'
						, tag: 	'button'
						, css: 	'ui right aligned teal icon button pull-right'
					}).notify('click', {
						type: 'views-run'
						, data: {
							run: function () {

								modal.body.save.loading();
								sb.data.db.obj.update(
									'view'
									, {
										id: 	view.id
										, conditional_formats: view.conditional_formats
									}
									, function (newView) {
										
										modal.hide();
										onUpdate(newView);
										
									}
								);

							}
						}
					});

					// Patch and show
					modal.body.patch();
					modal.show();

				}
			}
		});
		

	}
	
	function reorderViews (views, movedViewId, cutInFrontOf, onComplete) {
		
		var newIndex = 1;
		var movedView = _.findWhere(views, {id: movedViewId});
		var next = _.findWhere(views, {id: cutInFrontOf});
		
		if (!movedView) {
			return;
		}
		
		if (next) {
			
			newIndex = next.index || 1;
			movedView.index = newIndex;
			
		} else {
			
			// move to the end
			var lastView = _.max(views, function (view) { return view.index; });
			newIndex = lastView.index + 1;
			movedView.index = newIndex;

		}
		
		var updates = [];
		_.each(views, function (view) {
			
			if (view.index === undefined) {
				view.index = 1;
			}
			
			if (
				view.index >= newIndex
				&& view.id != movedViewId
			) {
				view.index++;
			}
			
			updates.push({
				id: 		view.id
				, index: 	view.index
			});

		});
		
		sb.data.db.obj.update (
			'view'
			, updates
			, function (response) {}
		);

	}
	
	function editViewForm (view, state, onComplete, goBack) {

		var newObj = {
				name: 'New view',
				icon: '',
				hideTabBorders: true,
				isVisible: true
			};
		
		if (view) {
			
			newObj.name = view.name;
			newObj.icon = view.icon;
			newObj.isVisible = view.isVisible;

		}
		
		if ( 
			state.hasOwnProperty('workflow')
			&& state.hasOwnProperty('workflowState')
		) {
			
			newObj.workflow = state.workflow; // This is an id, the entity is the workflow object.
			newObj.state = state.workflowState.id;
			
		}
		
		var optionsDef = {
				name: {
					title: 'Name'
					, type: 'title'
					, defaultValue: newObj.name
				}
				, icon: {
					title: 'Icon'
					, type: 'icon'
					, defaultValue: newObj.icon
				}
				, hideTabBorders: {
					defaultValue: true
				}
			};
			
		if ( view.workflow > 0 ) {
			
			optionsDef.isVisible = {
				title: 'Is visible?'
				, type: 'bool'
				, defaultValue: newObj.isVisible
			};
			
		}
		
		sb.notify({
			type: 	'get-options-form'
			, data: {
				optionsDef: optionsDef 
				, options: 		{
					name: 'New view',
					icon: '',
					hideTabBorders: true
				}
				, onComplete: 	function (opts) {

					if (view && view.id) {

						sb.data.db.obj.update(
							'view'
							, {
								id: 	view.id
								, name: opts.name
								, icon: opts.icon
								, isVisible: opts.isVisible
							}
							, function (newView) {

								onComplete(newView);
								
							}
						);
						
					} else {
						
						sb.data.db.obj.create(
							'view'
							, {
								name: opts.name
								, blueprint: {}
								, type: 'form'
								, icon: opts.icon
								, set: state.set.substr(1)
								, workflow: newObj.workflow
								, state: newObj.state
							}
							, function (newView) {
								
								onComplete(newView);
								
							}
						);
						
					}
					
				}
				, onClose: goBack
			}
		});
		
	}
	
	function viewAsTabs (ui, fields, entity, options, create, viewContainer, defaultViews) {

		function archiveView (view, onComplete) {		

			sb.dom.alerts.ask(
				{
					title: 'Send to archive?',
					text: 'This cannot be undone.'
				}
				, function (response) {

					if (response) {
						
						swal.disableButtons();
						
						// If its a subset, hide from this subset (not the 
						// parent/upstream sets)..
						if (entity.object_bp_type.includes('.')) {
							
							if (
								!Array.isArray(view.hideFrom)
								|| _.isEmpty(view.hideFrom)
							) {
								
								view.hideFrom = [entity.object_bp_type.substring(1)];
								
							} else {
								
								view.hideFrom.push(entity.object_bp_type.substring(1));
								
							}
							
							sb.data.db.obj.update(
								'view'
								, {
									id: 		view.id
									, hideFrom: view.hideFrom
								}
								, function (response) {
									
									swal.close();
									onComplete();
									
								}
							);
							
						// ..otherwise, just archive it.
						} else {
							
							sb.data.db.obj.erase(
								'view'
								, view.id
								, function () {
	
									swal.close();
									onComplete();
	
								}
							);
							
						}
						
					} else {
						
						swal.close();
						
					}
					
				}
			);
			
		}
		
		function getCachedViewSettings (entity, callback) {
			
			var pageId = entity.object_bp_type +'-singleView';

			// Read browser database
			sb.notify({
				type: 'get-from-browser-cache',
				data: {
					id: pageId
					, onComplete: function(res) {

						// if collections exist
						if (res) {
	
							activeTab = res.activeViewTab;
							callback(activeTab);
							
						} else {
							
							// if it does not, create it
							
							sb.notify({
								type: 'save-to-browser-cache',
								data: {
									obj: {
										id: pageId
										, activeViewTab: activeTab
									}
									, onComplete: function(newRes) {
	
										activeTab = newRes.activeViewTab;
										callback(activeTab);
										
									}
								}
							}, sb.moduleId);
							
						}
						
					}
					, override: function() {
						
						callback(activeTab);
							
					}		
				}
			}, sb.moduleId);				
			
		}
		
		function getEntityViews (entity, onGet, setup) {
			
			// setup
				// useWorkflows --> bool
					// Can be used to flag if the entity obj is a workflow

			var setName = '';
			var setNames = [];
			var where = {};
			var workflowsPropKeys = [];
				
			if ( 
				setup.hasOwnProperty('useWorkflow')
				&& setup.useWorkflow === true 
			) {
				
				setName = entity.parent.bp_name;
				where = {
					set: setName
					, workflow: entity.id
					, state: options.workflowState.id
				};
				
			} else {

				var workflowsIds = [];
		
				setName = entity.object_bp_type.substr(1);
				where = {
					set: setName
				};
				
				_.each(options.blueprint.blueprint, function(o, k) {
					if ( o.fieldType === 'state' && o.workflow) {
						
						workflowsIds.push(o.workflow.id);
						workflowsPropKeys.push({
							wfId: o.workflow.id
							, wfProp: k
						});	
						
					}
					
				});

				workflowsIds.push(null);
				
				where = {
					set: setName
				};

				
			}	

			//!TODO: Add selection object
			
			// For subsets, also inherit views
			if (setName.includes('.')) {
				
				var currentName = '';
				var i = 0;
				
				_.each(setName.split('.'), function (layerName) {
					
					if (i > 0) {
						currentName += '.';
					}
					currentName += layerName;
					setNames.push(currentName);
					i++;
					
				});
				
				where = {
					set: {
						type: 'or'
						, values: setNames
					}
				};
				
			}

			sb.data.db.obj.getWhere(
				'view'
				, where
				, function (formViews) {

					// Filter out 
					if (!_.isEmpty(setNames)) {
						
						_.each(setNames, function (setName) {
							
							formViews = _.filter(
								formViews
								, function (formView) {
									
									if (_.isEmpty(formView.hideFrom)) {
										return true;
									}
									
									return !_.contains(formView.hideFrom, setName);
								
								}
							);
							
						});
						
					}

					if ( 
						!_.isEmpty(workflowsPropKeys)
						&& options.editBlueprint === false 
					) {
						
						workflowsPropKeys = _.filter(workflowsPropKeys, function(o) {
							return o.wfId !== undefined;
						});

						var stateAssociatedViews = [];
						var staticViews = [];
						
						_.each(formViews, function(f) {
							
							if (!f.hasOwnProperty('state')) {
								f.state = 0;
							}
							if (!f.hasOwnProperty('workflow')) {
								f.workflow = 0;
							}

							if ( 
								f.state === 0
								&& f.workflow === 0
							) {
								
								staticViews.push(f);
								
							}
							
						});
						
						_.each(workflowsPropKeys, function(v, index) {
							
							if ( entity.hasOwnProperty(v.wfProp) ) {
								
								_.each(formViews, function(f) {

									if (
										f.state === entity[v.wfProp]
										&& f.workflow === v.wfId
									) {
										
										stateAssociatedViews.push(f);
										
									}
									
								});
								
							}
							
						});

						formViews = _.union(stateAssociatedViews, staticViews);
						
					}
					
					if (
						entity.object_bp_type !== 'entity_workflow'
					) {
						
						formViews = _.reject(formViews, function(v) {
							return (v.hasOwnProperty('isVisible') && v.isVisible === false);
						});
						
					}
					
					onGet(formViews);
					
				}
			);
			
		}
		
		function editView (view, onComplete, goBack) {
			
			editViewForm(
				view
				, {
					set: entity.object_bp_type
				}
				, onComplete
				, goBack
			);
			
		}
		
		function reorderView () {}
		
		function selectView (ui, view, shouldCache) {
			
			ui.empty();
			
			if (typeof view.view === 'function') {
				
				view.view(ui);
				
			} else {
				
				switch (view.type) {
					
					case 'form':
						viewForm(
							ui
							, view
							, {
								editBlueprint: 	options.editBlueprint
								, blueprint: 	options.blueprint
								, entity: 		entity
								, goBack: 		options.goBack
								, toNextInList: options.toNextInList || false
								, viewStyle: 	options.viewStyle
							}
							, function () {}
						);
						break;
					
				}
				
			}
			
			ui.patch();
			
			if (shouldCache) {
				
				sb.notify({
					type: 'update-browser-cache',
					data: {
						id: entity.object_bp_type +'-singleView'
						, callback: function(viewSettings, callback) {
							
							viewSettings.activeViewTab = view.id;
							callback(viewSettings);
							
						}
					}
				}, sb.moduleId);
				
			}
			
		}
		
		var activeTab = 'all-fields';
		var views = [];
		
		if (!_.isEmpty(defaultViews)) {
			views = views.concat(defaultViews);
		}

		if (
			entity.object_bp_type 
			&& ( entity.object_bp_type.substr(0, 1) === '#'
			|| entity.object_bp_type === 'entity_workflow' )
		) {
			
			var getEntityViewsSetup = {};
			
			if ( entity.object_bp_type === 'entity_workflow' ) {
				getEntityViewsSetup.useWorkflow = true;
			}
			
			viewContainer.makeNode('loader', 'div', {
				css: 		''
				, text: 	'<div class="ui hidden divider"></div><br /><br /><i class="notched circle loading icon"></i>'
				, style: 	'width:100%;height:100%;text-align:center;'
			});
			
			viewContainer.patch();

			getEntityViews (entity, function (formViews) {

				getCachedViewSettings (entity, function (activeTab) {
						
						views = views.concat(
							_.sortBy(formViews, 'index')
						);

						// Hide all fields if views exist and not in edit mode		
						if (!options.editBlueprint && !_.isEmpty(formViews)) {
							
							views = _.filter(views, function (view) {
								
								return view.id !== 'all-fields';
								
							});
							
						}
						
						var activeView = _.findWhere(views, {id: activeTab});
						
						if (_.isEmpty(activeView)) {
							
							activeView = views[0];
							activeTab = activeView.id;
							
						}

						// Don't show if no items and not in edit mode
						if (
							views.length < 2
							&& !options.editBlueprint
						) {

							if (ui && ui.selector) {

								ui.empty();
								ui.patch();
								
							}
							
						} else {

							if (entity.object_bp_type !== 'entity_workflow') {
								
								// All fields tab
								ui.makeNode('firstTab', 'div', {
									style: 'display: contents;'
								});
								
								_.each(views, function (view, i) {
	
									if (view.id == 'all-fields') {
	
										// Set variables
										var activeCSS = '';
										if (view.id === activeTab) {
											activeCSS = 'selected';
										}
	
										var tabSetup = {
												tag: 'a',
												css: 'ui item simple dropdown ' + activeCSS,
												style: 'font-style:italic;',
												text: '<i class="blue ' + view.icon + ' icon"></i> '+ view.name
											};
	
										ui.firstTab.makeNode('v-'+ i, 'div', tabSetup).listeners.push(function (view, selector) {
											
											$(selector).on('click', function () {
	
												$(ui.selector +' .selected').removeClass('selected');
												$(selector).addClass('selected');
												
												// Give fields a chance to run their blur/unfocus 
												// updates before changing the tab.
												$('input').blur();
												selectView(viewContainer, view, true);
												
											});
											
										}.bind({}, view));
	
									}
	
								});	
								
							} 

							// Sortable tabs
							ui.makeNode('sortableTabs', 'div', {
								style: 'display: contents;',
								drag: {
									moves: false,
									data: {
										views: formViews,
										instanceId:	sb.instanceId
									},
									accepts: true,
									copy: false
								}
							});

							_.each(views, function (view, i) {

								if (view.id != 'all-fields') { 

									// Set variables
									var activeCSS = '';
									
									if (view.id === activeTab) {
										activeCSS = 'selected';
									}
								
									var tabSetup = {
											tag: 'a',
											css: 'ui item simple dropdown ' + activeCSS,
											style: 'font-weight:600 !important;',
											text: '<i class="black ' + view.icon + ' icon"></i> '+ view.name
										};
										
									if ( 
										typeof view.state === 'number'
										&& view.state !== 0
										&& typeof view.workflow === 'number'
										&& view.workflow !== 0  
									) {
										
										var blueprintsWithWorkflows = _.filter(options.blueprint.blueprint, function(o) {
											return o.hasOwnProperty('workflow') && typeof(o.workflow) !== 'number'
										});
										
										_.each(blueprintsWithWorkflows, function(o) {
											
											var associatedState = _.findWhere(o.workflow.states, {id: view.state});

											if ( associatedState !== undefined ) {
												
												if ( associatedState.color === undefined ) {
													
													associatedState.color = 'grey';
													
												}
												
												tabSetup.style += 'background-color: ' + associatedState.color + ' !important; color: white !important;';
												tabSetup.text = '<i class="' + view.icon + ' icon"></i> '+ view.name
												tabSetup.css += ' associatedTab';	
												
											}
												
										});
										
									}
										
									if (options.editBlueprint) {
										
										tabSetup.drag = {
											moves: true,
											data: {
												view: view.id,
												instanceId:	sb.instanceId,
												run: function (data) {}
											},
											drop: 'view-tab-dropped',
											accepts: false,
											copy: false,
											direction:  'horizontal'
										};
										
									}
								
									ui.sortableTabs.makeNode('v-'+ i, 'div', tabSetup).listeners.push(function (view, selector) {
										
										$(selector).on('click', function () {
											
											//$('.associatedTab').css('color', 'white !important');	
											
											$(ui.selector +' .selected').removeClass('selected');
											$(selector).addClass('selected');
											
											/*
if ( $(selector).hasClass('associatedTab') ) {
												
												$(selector).css('color', 'lightblue');
												
											}
*/

											// Give fields a chance to run their blur/unfocus 
											// updates before changing the tab.
											$('input').blur();
											selectView(viewContainer, view, true);

										});
										
									}.bind({}, view));
								
									if ( !options.editBlueprint ) {
										return;
									}
								
									ui.sortableTabs['v-'+ i].makeNode('menu', 'div', {css: 'menu'});
									
									// Edit
									ui.sortableTabs['v-'+ i].menu
										.makeNode('edit', 'div', {
											css: 'item',
											style: '',
											text: '<i class="ui edit icon"></i> Edit'
										}).listeners.push(function (view, selector) {
										
										$(selector).on('click', function (e) {
											
											e.stopPropagation();
											editView(view, function () {
												
												if (typeof options.goBack === 'function') {

													options.goBack(entity);

												} else {

													// On complete
													ui.empty();
													viewAsTabs(ui, fields, entity, options, create, viewContainer, defaultViews);
													ui.patch();

												}
												
											}, options.goBack);
											
										});
										
									}.bind({}, view));
								
									// Conditional Formatting
									ui.sortableTabs['v-'+ i].menu
										.makeNode('conditionalFormats', 'div', {
											css: 'item',
											style: '',
											text: '<i class="ui paint brush icon"></i> Conditional Formatting <strong class="ui teal text">in beta</strong>'
										}).listeners.push(function (view, selector) {
										
										$(selector).on('click', function (e) {
											
											e.stopPropagation();
											editConditionalFormats(view, function () {
												
												if (typeof options.goBack === 'function') {

													options.goBack(entity);

												} else {

													// On complete
													ui.empty();
													viewAsTabs(ui, fields, entity, options, create, viewContainer, defaultViews);
													ui.patch();

												}
												
											}, options.goBack);
											
										});
										
									}.bind({}, view));

									// Archive
									ui.sortableTabs['v-'+ i].menu
										.makeNode('archive', 'div', {
											css: 'item',
											text: '<i class="ui archive icon"></i> Archive'
										}).listeners.push(function (view, selector) {
										
										$(selector).on('click', function (e) {
											
											e.stopPropagation();
											archiveView(view, function () {
												
												// On complete
												ui.empty();
												viewAsTabs(ui, fields, entity, options, create, viewContainer, defaultViews);
												ui.patch();
												
											});
											
										});
										
									}.bind({}, view));
								
								}

							});
							
							// New tab button
							ui.makeNode('lastTab', 'div', {
								style: 'display: contents;'
							});
							
							if (options.editBlueprint) {
								
								ui.lastTab.makeNode('new', 'div', {
									tag: 'a',
									css: 'ui item simple dropdown',
									style: 'font-style:italic;',
									text: '<i class="ui green plus icon" style="margin:0"></i>'
								}).notify('click', {
									type: 'entities-run',
									data: {
										run: function () {
											
											function alertThatFullViewGoesAway (formViews, onContinue) {
												
												if (_.isEmpty(formViews)) {
													
/*
													sb.dom.alerts.ask(
														{
															title: 		'Are you sure?'
															, text: 	'Adding tabs will hide the full view from the this set (except in edit set mode)'
														}
														, function (response) {
															
															if (response) {
																
																swal.close();
																onContinue();
																
															} else {
																
																swal.close();
																
															}
															
														}
													);
*/
													onContinue();
													
												} else {
													
													onContinue();
													
												}
												
											}
											
											alertThatFullViewGoesAway (formViews, function () {
												
												var objectType = '';
												var stateSetup = {};
												
												if ( entity.object_bp_type === 'entity_workflow' ) {
													
													objectType = '#' + entity.parent.bp_name;
													stateSetup = {
														set: objectType
														, workflow: entity
														, workflowState: options.workflowState
													};
													
												} else {
													
													objectType = entity.object_bp_type;
													stateSetup = {
														set: objectType
													}; 
													
												}

												sb.notify({
													type: 'create-new-form-view',
													data: {
														ui: {},
														state: stateSetup,
														onComplete: function (newView) {
															
															if (typeof options.goBack === 'function') {
																
																options.goBack(entity);
																
															} else {

																ui.empty();
																viewAsTabs(ui, fields, entity, options, create, viewContainer, defaultViews);
																ui.patch();

															}
															
														},
														onClose: options.goBack
													}
												});
												
											});
											
										}
									}
								}, sb.moduleId);
									
							}
							
						}
							
						ui.patch();
						
						selectView(viewContainer, activeView, false);
						
					});
				
			}, getEntityViewsSetup);
			
		} else {

			selectView(viewContainer, _.findWhere(views, {id: 'all-fields'}), false);
			
		}
		
	}
	
	// Kinds of views
	
	function viewForm (ui, view, options, onDraw) {

		function addAField (view, options, onComplete) {
			
			function addFieldsToView (view, request, blueprint, onComplete) {
				
				function addFieldToView (view, blueprint, fieldToAddKey) {
					
					var nextIndex = 1;
					_.each(view.blueprint, function (field) {
						if (field.index >= nextIndex) {
							nextIndex = field.index + 1;
						}
					});
					if (_.isEmpty(view.blueprint)) {
						view.blueprint = {};
					}
					
					view.blueprint[fieldToAddKey] = {
						index: nextIndex
					};
					
					if (request.carryOverViewOpts && blueprint[fieldToAddKey].options) {
						
						view.blueprint[fieldToAddKey].options = {};
						_.each(viewOpts, function (viewOptKey) {
							
							if (blueprint[fieldToAddKey].options[viewOptKey]) {
								
								view.blueprint[fieldToAddKey]
									.options[viewOptKey] = blueprint[fieldToAddKey].options[viewOptKey];
								
							}
							
						});
						
					}
					
				}
				
				_.each(request.field, function (fieldToAddKey) {
					
					addFieldToView (view, blueprint, fieldToAddKey);
					
				});
				
				sb.data.db.obj.update(
					'view'
					, {
						id: view.id
						, blueprint: view.blueprint
					}
					, function (updated) {
						
						view.blueprint = updated.blueprint;
						onComplete(updated);
						
					}
				);
				
			}
			
			sb.notify({
				type: 'get-sys-modal'
				, data: {
					callback: 	function (modal) {
						
						var request = {
							field: false
							, carryOverViewOpts: false
						};
						
						modal.body.empty();
						modal.show();
						
						// Header
						modal.body.makeNode(
							'h'
							, 'div'
							, {
								text: 'Add a field to the view'
								, tag: 'h1'
							}
						);

						// Field selection						
						modal.body.makeNode('field', 'div', {});
						modal.body.makeNode('br1', 'div', {text: '<br /><br />'});
						
						// Carry-over view opts?
						modal.body.makeNode('viewOpts', 'div', {}).makeNode('label', 'div', {
							text: '<strong>Carry over view options?</strong>'
						});;
						
						// Save
						modal.body.makeNode('br2', 'div', {text: '<br /><br />'});
						modal.body.makeNode(
							'save'
							, 'div'
							, {
								text: '<i class="ui check icon"></i> Add fields'
								, css: 'ui teal button'
								, tag: 'button'
							}
						).listeners.push(
							function (selector) {
								
								$(selector).on(
									'click'
									, function () {
										
										if (request.field) {
											
											addFieldsToView(
												view
												, request
												, options.blueprint.blueprint
												, function () {
													
													modal.hide();
													onComplete();
													
												}
											);
											
										} else {
											
											sb.dom.alerts.alert(
												'Select a field to continue'
												, ''
												, 'warning'
											);
											
										}
										
									}
								);
								
							}
						);
						
						modal.body.patch();

						var bp = {};
						
						if ( options.entity.object_bp_type === 'entity_workflow' ) {
							
							bp = options.blueprint;
							
						} else {
							
							bp = options.blueprint.blueprint;
							
						}

						sb.notify({
							type: 'view-field-selection'
							, data: {
								ui: 			modal.body.field
								, blueprint: 	bp
								, options: 		{
									allowedTypes: 	['any']
									, multi: 		true
									, onUpdate: 	function (val) {
										
										request.field = val;
										
									}
								}
								, state: {}
							}
						});
						
						sb.notify ({
							type: 'view-field',
							data: {
								type: 'toggle',
								property: 'carryOverViewOpts',
								obj: request,
								options: {
									edit: true,
									editing: true,
									commitUpdates: false
								},
								ui: modal.body.viewOpts.makeNode('toggle', 'div', {
									style: 'margin-bottom:15px;'
								})
							}
						});
						modal.body.viewOpts.patch();
						
					}
				}
			});
			
		}
			
		ui.makeNode('fields', 'div', {});
		ui.patch();
		
		var entity = options.entity;
		var fields = _.clone(view.blueprint);
		var opts = {
			blueprint: 		view
			, goBack: 		options.goBack
			, toNextInList:	options.toNextInList || false
			, viewStyle: 	options.viewStyle || 'record'
		};

		if (options.editBlueprint) {
			opts.editBlueprint = 'display-only';
		} else {
			opts.editBlueprint = false;
		}
		var refBlueprint = options.blueprint;
		if (refBlueprint) {
			if (refBlueprint.hasOwnProperty('blueprint')) {
				refBlueprint = refBlueprint.blueprint;
			}
		}
		
		_.each(fields, function (val, key) {
			
			if (
				view.blueprint[key].is_archived
				|| (
					!_.isEmpty(refBlueprint[key])
					&& refBlueprint[key].is_archived
				)
				|| _.isEmpty(refBlueprint[key])
			) {

				return delete fields[key];

			}

			// Root lvl options
			view.blueprint[key].name = refBlueprint[key].name;
			view.blueprint[key].type = refBlueprint[key].type;
			view.blueprint[key].fieldType = refBlueprint[key].fieldType;
			view.blueprint[key].permissions = refBlueprint[key].permissions;
			view.blueprint[key].select = refBlueprint[key].select;
			view.blueprint[key].workflow = refBlueprint[key].workflow;
			view.blueprint[key].is_archived = refBlueprint[key].is_archived;
			
			// Config options
			if (_.isEmpty(view.blueprint[key].options)) {
				view.blueprint[key].options = {};
			}
			// Only carry over non-view config-lvl opts
			if (!_.isEmpty(refBlueprint[key].options)) {
				
				_.each(refBlueprint[key].options, function (opt, optKey) {
					
					if (!_.contains(viewOpts, optKey)) {
						
						view.blueprint[key].options[optKey] = refBlueprint[key].options[optKey];
						
					}
					
				});
				
			}
			
		});

		// Distribute the formatting rules to the fields they should affect.
		if (!_.isEmpty(view.conditional_formats)) {
			
			_.each(view.conditional_formats, function (rule) {

				if (
					rule
					&& !_.isEmpty(rule.fields)
				) {
					
					_.each(rule.fields, function (fieldName) {
						
						if (!_.isEmpty(fields[fieldName])) {
							
							if (_.isEmpty(fields[fieldName].format)) {
								fields[fieldName].format = {};
							}

							fields[fieldName].format[rule.style] = {
								_if: {}
							};
							_.each(rule.conditions, function (condition) {
								
								if (condition && condition.type) {

									fields[fieldName].format[rule.style]._if = {
										[condition.type]: condition.options
									};

								}

							});

						}

					});

				}

			});

		}
		
		if (fields) {

			sb.notify({
				type: 'view-fields'
				, data: {
					ui: 		ui
					, fields: 	fields
					, entity: 	entity
					, options: 	opts
				}
			});
			
		}
		
		if (options.editBlueprint) {
			
			ui.makeNode(
				'addField'
				, 'div'
				, {
// 					tag: 'button'
					text: '<i class="green plus icon"></i> Add a field'
					, css: 'ui button field-manager-plus'
					, style: 'font-weight:300;color:rgb(175,175,175); margin:1rem 2px; font-style:italic;'
				}
			).listeners.push(
				function (selector) {
					
					$(selector).on('click', function () {
						
						addAField(view, options, function () {
							
							ui.empty();
							viewForm (ui, view, options, onDraw);
							ui.patch();
							
						});
						
					});
					
				}
			);
			
		}
		
	}
	
	return {
		
		init: function () {
			
			sb.listen({
				'create-new-form-view': 	this.newForm
				, 'show-views-as-tabs': 	this.viewAsTabs
				, 'views-run': 				this.run
				, 'view-tab-dropped': 		this.reorderViews
				, 'refresh-entity-tabs': 	this.refreshTabs
				, 'display-view':			this.displayView
			});
			
		}

		, run: function (data) {

			if (data && typeof data.run === 'function') {
				data.run(data);
			}

		}
		
		, displayView: function(data) {

			viewForm (data.ui, data.view, data.options, data.onDraw);
			
		}
		
		, newForm: function (data) {

			editViewForm(
				false
				, data.state
				, data.onComplete
				, data.onClose
			);
			
		}
		
		, refreshTabs: function(data) {

            if (data.entity.id != cachedTabsSetup.object.id)
                return;

			if ( data.hasOwnProperty('entity') ) {
				
				cachedTabsSetup.object = data.entity;
				
			}

			if (cachedTabsSetup.ui && cachedTabsSetup.fields && cachedTabsSetup.object && cachedTabsSetup.options && cachedTabsSetup.viewContainer && cachedTabsSetup.defaultViews) {


				viewAsTabs(
					cachedTabsSetup.ui
					, cachedTabsSetup.fields
					, cachedTabsSetup.object
					, cachedTabsSetup.options
					, cachedTabsSetup.create
					, cachedTabsSetup.viewContainer
					, cachedTabsSetup.defaultViews
				);

			}
			
		}
		
		, reorderViews: function (data) {
			
			reorderViews(
				data.in.views
				, data.dropped.view
				, data.next.view
				, function (response) {
					//console.log('arrangeFields->onComplete:', response);
				}
			);
			
		}
		
		, viewAsTabs: function (data) {

			var object = {};
			
			if ( data.hasOwnProperty('entity') ) {
				
				object = data.entity;
				
			} else if ( data.hasOwnProperty('workflow') ) {
				
				object = data.workflow;
				
			}

			viewAsTabs(
				data.ui // The container for the tabs to show in
				, data.fields
				, object
				, data.options
				, data.create
				, data.viewContainer // The container for the views to show in, not the tabs
				, data.defaultViews
			);
			
			cachedTabsSetup = {
				ui: data.ui
				, fields: data.fields
				, object: object
				, options: data.options
				, create: data.create
				, viewContainer: data.viewContainer
				, defaultViews: data.defaultViews
			};
			
		}
		
	};
	
});
