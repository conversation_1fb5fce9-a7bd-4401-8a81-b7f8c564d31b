Factory.register('test-suite', function (sb) {
	
	function showTests (ui, state, draw) {
		
		function runWhere (objType, onComplete, where) {
			
			var iterations = 100;
			var start = new Date();
			var i = 0;
			var memo = {
				err: 0
			};
			
			function runCalls (objType, onComplete, where, i, memo) {
				
				if (!where) {
					
					where = {};
					
				}
				
				where.paged = { 
					"count":true,
					"page":0,
					"pageLength":50,
					"paged":true,
					"sortCol":"date_created",
					"sortDir":"desc",
					"sortCast":"date"
				};
				where.is_template = {
					"type":"not_equal",
					"value":1
				};
				
				sb.data.db.obj.getWhere(
					objType
/*
					'#'+ appConfig
							.Types[
								Math.floor(Math.random() * appConfig.Types.length)
							].bp_name
*/
					, where
					, function (r) {
						
						if (i == iterations) {
							
							var end = new Date();
							memo.totalTime = end - start;
							memo.avgTime = memo.totalTime/iterations;
							memo.recordsTotal = r.recordsTotal;
							
							return onComplete(memo);
							
						} else {
							
							return runCalls(
								objType
								, onComplete
								, where
								, i + 1
								, memo
							);
							
						}
						
					}
					, true
				);
				
			}
			
			runCalls(objType, onComplete, where, 1, memo);
			
		}
		
		// Get Stories where..
		ui.makeNode(
			'story'
			, 'div'
			, {
				css: 'ui card'
			}
		);
		ui.story.makeNode(
			'run'
			, 'div'
			, {
				text: 'Run'
				, css: 'ui primary button'
			}
		).listeners.push(
			function (selector) {
				
				$(selector).on(
					'click'
					, function () {
						
						ui.story.run.loading();
						
						runWhere('contacts', function (data) {
							
							$(ui.story.c.selector)
								.text(
									'Contacts AVG:'+ data.avgTime.toFixed(2) +'(ms), recordsTotal: '+ data.recordsTotal
								);
								
							runWhere('groups', function (data) {
								
								$(ui.story.p.selector)
									.text(
										'Projects AVG:'+ data.avgTime.toFixed(2) +'(ms), recordsTotal: '+ data.recordsTotal
									);
									
								runWhere('#Story', function (data) {
									
									ui.story.run.loading(false);
									
									$(ui.story.s.selector)
										.text(
											'Stories AVG:'+ data.avgTime.toFixed(2) +'(ms), recordsTotal: '+ data.recordsTotal
										);
									
								});
								
							}, {
								group_type: 'Project'
							});
							
						});
						
					}
				);
				
			}
		);
		ui.story.makeNode(
			'j'
			, 'div'
			, {
				text:''
			}
		);
		ui.story.makeNode(
			'c'
			, 'div'
			, {
				text:''
			}
		);
		ui.story.makeNode(
			'p'
			, 'div'
			, {
				text:''
			}
		);
		ui.story.makeNode(
			's'
			, 'div'
			, {
				text:''
			}
		);
		
		ui.patch();
		
	}
	
	return {
		
		init: function () {
			
			/*
if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'voltzsoftware'
			) {
				
				sb.notify({
					type: 'register-tool',
					data: {
						navigationItem: {
							moduleId: 	sb.moduleId,
							instanceId: sb.instanceId,
							id: 		'tests-zachhhh',
							title: 		'Tests',
							icon: 		'<i class="fa fa-report"></i>',
							views: [
								// Test area
								{
									id: 	'apiTestss',
									name: 	'API Tests',
									tip: 	'Organize outgoing emails about the project.',
									icon: {
										type: 	'envelope',
										color: 	'blue'
									},
									default: true,
									hide: true,
									settings: false,
									mainViews: [{
										dom: showTests
									}],
									layers: [],
									boxViews: []
								}
							]
						}
					}
				});
				
			}
*/
			
		}
		
	};
	
});