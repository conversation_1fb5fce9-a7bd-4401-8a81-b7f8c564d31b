Factory.register('notifications', function(sb){

	var NavNotificationBadge = {};

	var unReadCount = 0;

	//UI FUNCTIONS

	function notificationDropdownDisplay(ui) {

		getNotifications(2, function(list) {

			list = list.data;

			ui.makeNode('add', 'div', {
				tag:'a',
				css: 'ui icon mini right floated basic button',
				text:'See All',
				href:window.location.href.split('#')[0] +'#notifications'
			});

			ui.makeNode('header', 'div', {
				css: 'ui left floated header',
				tag:'h5',
				text:'Recent Notifications:',
				style:'margin-bottom:10px; padding-top:5px;'
			});
			ui.makeNode('divdr', 'div', {css: 'ui clearing divider'});

			if(_.isArray(list) && list.length != 0){

				ui.makeNode('feed', 'div', {css: 'ui small feed'});

				_.each(list, function(n, i){
					
					var type = '';
					if ( n.hasOwnProperty('type') ) {
						if ( n.type ) {
							type = n.type.toUpperCase();
						}
					}

					var link = getNotificationLink(n);

					ui.makeNode('item-'+ i, 'div', {
						tag:'a',
						css: 'event',
						style:'padding:0;',
						href:link
					}).notify('click', {
						type: 'notifications-run',
						data: {
							run:updateNotificationStatus.bind(null, n, function(resp) {

								getNotifications(0, function(updList) {

									NavNotificationBadge.refresh(updList);

								});

							})
						}
					}, sb.moduleId);

					ui['item-'+ i].makeNode('content', 'div', {css: 'content'});

					ui['item-'+ i].content.makeNode('sum', 'div', {
						css: 'ui '+ n.color +' summary mini header',
						text:`<i class="${n.icon} icon"></i> ${type} <div class="date">${moment(n.date_created).fromNow()}</div>`
					});

					ui['item-'+ i].content.makeNode('ext', 'text', {
						text:`<strong>${n.title}</strong> <br /> ${n.details}`
					});
					ui.makeNode('div-'+i, 'div', {text:'<br />'});

				}, ui.feed);

			} else {

				ui.makeNode('subheader', 'div', {text:'No new notifications.'});

			}

			return ui.patch();

		});

	}

	function notificationBadgeDisplay() {

		var modal = this.makeNode('modal', 'modal', {});
		var ui = this;

		sb.data.db.obj.getCounts(
			'notification'
			, false
			, {
				user: 			parseInt(sb.data.cookie.userId)
				, is_viewed: 	0
			}
			, function (unReadCount) {

				if(unReadCount > 0){

					var unReadCountLength = unReadCount.toString().length;
					var left = '';
					if ( unReadCountLength == 1 ) {
						left = 'left:28px !important;';
					} else if ( unReadCountLength == 2 ) {
						left = 'left:24px !important;';
					} else if ( unReadCountLength == 3 ) {
						left = 'left:18px !important;';
					} else if ( unReadCountLength == 4 ) {
						left = 'left:13px !important;';
					} else {
						left = 'left:8px !important;';
					}
		
					ui.makeNode('icon', 'div', {
						text: '<i class="large bell icon" style="margin:0 !important;"></i><div class="floating ui red mini label notificationCount" style="' + left + '">' + unReadCount + '</div>',
					});
		
				} else {
		
					ui.makeNode('icon', 'div', {
						tag:'i',
						css: 'large envelope icon'
					});
		
				}
		
				ui.icon.notify('click', {
					type: 'notifications-run',
					data: {
						run: function () {
		
							notificationDropdownDisplay(modal.body);
							modal.body.patch();
		
							modal.show();
		
						}
					}
				}, sb.moduleId);
				return ui.patch();

			}
		);

		return;

	}

	function menuItemDisplay(ui) {

		NavNotificationBadge = ui;
		NavNotificationBadge.refresh = notificationBadgeDisplay.bind(NavNotificationBadge);
		NavNotificationBadge.refresh();

		return ui.patch();

	}

	//UTIL FUNCTIONS

	function getNotificationLink (obj) {

		function format_objectName(obj) {
													
			switch(obj.object_bp_type) {
					
				case 'users':
				case 'contacts':
					return obj.fname + ' ' + obj.lname;
					
				default:
					return obj.name;
				
			}
			
		}

		var link = '';
		if (obj.producer) {
		
			var pageURL = {
				type: 		obj.producer.object_bp_type, 
				id: 		obj.producer.id, 
				name: 		format_objectName(obj.producer),
				group_type: obj.producer.group_type
			};
			
			
			link = sb.data.url.createPageURL(
				'object'
				, pageURL
			);
			
		}

		return link;

	}

	function getTaskForm (notification, onComplete) {

		sb.notify({
			type: 'get-sys-modal'
			, data: {
				callback: function (modal) {

					modal.empty();
					modal.show();

					sb.notify({
						type: 'get-action-options-form'
						, data: {
							ui: 			modal
							, event_type: 	notification.event_type
							, obj: 			notification.producer
							, notification: notification
							, onComplete: 	function (response) {

								if (typeof onComplete === 'function') {
									onComplete(response);
								}

							}
						}
					});

				}
			}
		});

	}

	function toggleIsViewed(obj) {

		var updObj = _.clone(obj);
		
		if (updObj.is_viewed === 1) {
			
			updObj.is_viewed = 0;
			
		} else {
			
			updObj.is_viewed = 1;
			
		}
		
		return updObj;

	}

	function updateNotificationStatus(obj, callback) {

		if (
			_.isObject(obj)
			&& obj.hasOwnProperty('id')
		) {

			var updNot = toggleIsViewed(obj);

			sb.data.db.obj.update('notification', updNot, function(res){

				if(res){

					if(callback && typeof callback == 'function'){
						return callback(res);
					}

				}

			});

		}

		if (Array.isArray(obj)) { // If there is a list

			sb.data.db.obj.getWhere('notification', {
				id: {
					type: 'or',
					values: obj
				},
				childObjs: {
					is_viewed: true
				}
			}, function(objs) {

				_.each(objs, function(o) { // Loop over list

					o.is_viewed = toggleIsViewed(o).is_viewed; // Toggle object is_viewed property

				});

				sb.data.db.obj.update('notification', objs, function(res){

					if (res) {

						if (callback && typeof callback == 'function') {
							return callback(res);
						}

					}

				}, 1);


			});

		}

	}

	function getNotifications(viewed, callback) {

		var selectionObj = {
			user: parseInt(sb.data.cookie.userId),
			childObjs: {
				title:		true,
                secondaryTitle: true,
				details:	true,
				icon:		true,
				color:		true,
				type:		true,
				link:		true,
				is_viewed:	true,
				producer: 	true,
                producer_type: true,
                tagged_with: true
			},
			paged: {
				count:		true,
				page:		0,
				pageLength:	10,
				paged:		true,
				sortCol:	'date_created',
				sortDir:	'desc',
				sortCast:	'string'
			}
		}

		// 0 = not been viewed || 1 = has been viewed
		if (viewed === 0 || viewed === 1) {
			selectionObj.is_viewed = viewed;
		}

		sb.data.db.obj.getWhere('notification', selectionObj, callback);

		return;

	}

    function viewGroupsByTypeSettings(ui, settings, onComplete, options) {
        // options.type_bp
        var type = options.type_bp;
        var objectName = options.objectName;
    
        sb.data.db.obj.getAll(type, function (Types) {
          var currentSelection;
    
          if (settings.options) {
            currentSelection = +settings.options.types;
          }
          var mappedObjs = _.map(Types, function (element, index, list) {
            var selected = false;
    
            if (element.id === currentSelection) {
              selected = true;
            } else {
              selected = false;
            }
    
            return {
              name: element.name,
              value: element.id,
              selected: selected,
            };
          });
          ui.empty();
    
          var formArgs = [
            {
              name: "type",
              type: "select",
              label: "Select a " + objectName + " Type",
              options: mappedObjs,
            },
          ];
    
          ui.makeNode("form", "form", formArgs);
    
          ui.makeNode("break", "div", { text: "<br />" });
    
          ui.makeNode("buttons", "div", { css: "ui mini buttons" });
    
          ui.buttons
            .makeNode("save", "div", {
              text: "Save",
              css: "ui green button",
            })
            .notify(
              "click",
              {
                type: [sb.moduleId + "-run"],
                data: {
                  run: function (ui) {
                    ui.buttons.save.loading();
                    ui.buttons.cancel.loading();
    
                    var selectedObj = parseInt(ui.form.process().fields.type.value);
    
                    settings.options = {
                      type: selectedObj,
                    };
    
                    onComplete(settings);
                  }.bind({}, ui),
                },
              },
              sb.moduleId
            );
    
          ui.buttons.makeNode("or", "div", { css: "or" });
    
          ui.buttons
            .makeNode("cancel", "div", {
              text: "Cancel",
              css: "ui button",
            })
            .notify(
              "click",
              {
                type: "tasks-run",
                data: {
                  run: function (ui) {
                    ui.buttons.cancel.loading();
                    ui.buttons.save.loading();
    
                    onComplete(settings);
                  }.bind({}, ui),
                },
              },
              sb.moduleId
            );
    
          ui.patch();
        });
      }

	return {

		init: function(){

			sb.listen({
				'notifications-run':this.run
			});

			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'notifications',
						title: 'Notifications',
						icon: '<i class="envelope outline icon"></i>',
						display:false,
						menuItemView:function(ui) {

							setTimeout(function() {

								menuItemDisplay(ui);

							}, 0.1);

						},
						views: [
							{
								id:'notifications',
								type:'custom',
								title:'Notifications',
								icon:'<i class="envelope outline icon"></i>',
								default:true,
								dom:function(dom, state, draw){

									sb.notify({
										type:'show-collection',
										data:{
											header:{
												editMode:false
											},
											actions:{
												navigateTo:false,
												viewProd:{
													icon:'external square alternate',
													title:'GoTo',
													color:'teal',
													domType:'navigate',
													href:true
												},
												markAsRead:{
													icon:'envelope open outline',
													title:'Mark As Read/Unread',
													color:'yellow',
													domType:'none',
													action:function(obj, state, shouldUpdate) {

														updateNotificationStatus(obj, function(updNot) {

															getNotifications(0, function(updList) {

																shouldUpdate(updNot);

																NavNotificationBadge.refresh(updList);

															});

														});

													},
													headerAction: function(selection, state, shouldUpdate) {

														updateNotificationStatus(selection, function(updNot) {

															getNotifications(0, function(updList) {

																shouldUpdate(updNot);

																NavNotificationBadge.refresh(updList);

															});

														});

													}
												}
											},
											domObj:dom,
											fields:{
												icon: {
													title: 'icon',
													type: 'title',
													view: function(ui, obj) {

														var icon = '';

														if(obj.icon !== '' || obj.icon !== null) {

															icon = '<i class="large '+ obj.icon +' icon"></i>';

														} else {

															icon = '<i class="large exclamation icon"></i>';

														}

														ui.makeNode('icon', 'div', {
															text: icon
														});

													}
												},
												title:{
													title:'Title',
													type:'title'
												},
												producer: {
													title: 'Producer',
													type: 'parent'
												},
												details:{
													title:'Details',
													type:'detail'
												},
												type:{
													title:'Type',
													type:'type',
													view:function(c, obj){

														var typeTxt = '';
														if (obj.type !== undefined && typeof obj.type === 'string') {
															typeTxt = obj.type.toUpperCase();
														}
														c.makeNode('', 'div', {css: obj.color +' ui label', text:'<i class="'+ obj.icon +' icon"></i>'+ typeTxt});

													}
												},
												date_created:{
													title:'Created On',
													type:'date',
													view:function(c, obj){

														c.makeNode('text', 'div', {text:moment(obj.date_created).local().format("MM/DD/YY - hh:mm a")});
													}
												},
												is_viewed:{
													title:'Status',
													view:function(c, obj){

														function viewViewedField (ui, fieldName, obj) {

															var labelOp = {
																color:'',
																text:''
															};

															if(obj[fieldName] == 0){

																labelOp.color = 'yellow';
																labelOp.text = 'Unread';

																ui.makeNode('label', 'div', {css:`ui ${labelOp.color} label`, text:labelOp.text});

															}

														}

														viewViewedField(c, 'is_viewed', obj);

														if (obj.type === 'task') {

															c.notify('click', {
																type: 'notifications-run'
																, data: {
																	run: function (obj) {

																		getTaskForm(
																			obj, function (response) {

																				// Refresh ui
																				c.empty();
																				viewViewedField(c, 'is_viewed', response);
																				c.patch();

																				updateNotificationStatus(obj, function(updNot) {

																					getNotifications(0, function(updList) {

																						NavNotificationBadge.refresh(updList);

																					});

																				});

																			}
																		);

																	}.bind({}, obj)
																}
															}, sb.moduleId);

														}

													}
												},
												tags: {
													isHidden: true
													, view: function () {
														return false;
													}
												}
											},
											filterBy: {
												type: {
													title: 			'Type'
													, defaultText: 	'All'
													, defaultValue: 'all'
													, getOptions: 	function (callback) {

														callback([
															{
																name: 	'<i class="ui clipboard icon"></i> Assignment'
																, id: 	'assignment'
															}, {
																name: 	'<i class="ui comments icon"></i> Comment'
																, id: 	'comment'
															}
															, {
																name: 	'<i class="ui exclamation icon"></i> General'
																, id: 	'general'
															}, {
																name: 	'<i class="ui at icon"></i> Mention'
																, id: 	'mention'
															}, {
																name: 	'<i class="ui question icon"></i> Question'
																, id: 	'question'
															}, {
																name: 	'<i class="ui clock icon"></i> Reminder'
																, id: 	'reminder'
															}, {
																name: 	'<i class="ui tasks icon"></i> Task'
																, id: 	'task'
															}, {
                                                                name: 	'<i class="ui stream icon"></i> Recent Client Activity'
                                                                , id: 	'recent-client-activity'
                                                            }
														]);

													}
													, parseSelection: function (selection, options) {

														switch (selection) {

															case 'all':
															case '0':
																delete options.where.type;
																break;

															default:
																options.where.type = selection;
																break;

														}

													}
												}
											},
											objectType:'notification',
											parseData: function (data, draw) {
												
												_.each(data.data, function (obj) {
													
													// Get the link for the obj
													obj.link = getNotificationLink(obj);
													
												});
												
												draw(data);
												
											},
											size:'mini',
											selectedView:'table',
											//menu: false,
											menu: {
									        	subviews: {
										        	table: true,
										        	list: false
									        	}
								            },
// 											submenu: false,
											state:state,
											where:{
												user:+sb.data.cookie.userId,
												childObjs:{
													data: 			true,
													event_type: 	true,
													link:			true,
													color:			true,
													icon:			true,
													date_created:	true,
													is_viewed:		true,
													type:			true,
													details:		true,
													title:			true,
                                                    secondaryTitle: true,
													producer: 		true,
                                                    producer_type:  true,
                                                    tagged_with:    true
												}
											}
										}
									});

								}
							}
						]
					}
				}
			});

			//one render for both
			function domRecentClientActivity (ui, state, draw, _projectTypes = false, teamUsers = []) {
				// Charsol Renewal default
				var projectType = 2318047;
				var hasProjectTypeSearch = false;

				//Tagged Configuration
				var RCA = 8348218; // tag ID (dev: 8345702)
				var RCACompleted = 8348219; //tag ID (dev: 8345703)
				var RCAFinished = -1;

				var RCAComments = 9143452; // tag ID (dev: 8345702)
				var RCAUploads = 9143453; // tag ID (dev: 8345702)




				if ( ((state.boxView || {}).settings || {}).options ) {
					projectType = state.boxView.settings.options.type;
					hasProjectTypeSearch = true;
				}

				ui.empty();

				var collectionSetup = {
					domObj: ui,
					// actions:{
					// 	navigateTo:false
					// },
					objectType: 'default_objectremove a', //'notification',
					customTemplate: 'block',
					//sortDir: 'desc',
					size:'mini',
					fields: {
						title:{
							title:'Title',
							type:'title',
							isSearchable: true,
							view: function(ui, obj){

								var linkActionItem = '#';
                                var rowItemTitle = '<i class="yellow clipboard icon"></i> ' + obj.name;
                                var _companyName = 'No Organization set';
								var roleName = '';
                                var RCASelected = null;

  								//this will be control the flow (only change this for add or remove new options in the flow)
								function getNextStatus(current) {
									//default
                                    if(_.contains(current, RCA) || _.contains(current, RCAComments)|| _.contains(current, RCAUploads) ){
										//RCA
										return [RCACompleted];
									} else if(_.contains(current, RCACompleted)){
										//Completed
										return [RCAComments, RCAUploads];
									}
								}

								function getName(selected) {
									if(_.contains(selected, RCACompleted)){
										//RCA
										return 'Re-Open';
									} else {
                                        return 'Mark as Complete';
                                    }
								}

								function getColor(selected) {

                                    if(selected == null){
										return 'red';
									}
									if(selected == RCA){
										return 'light-grey';
									}
									if(selected == RCACompleted){
										return 'red';
									}
									if(selected == RCAFinished){
										return 'green';
									}
									return '';
								}
								//this render the button actions (in the ui)
								function refreshRCAButton(ui, RCASelected) {

									var btnName = getName(RCASelected);

                                    ui.makeNode('saveBtn', 'div', {
                                        css: 'pull-left ui mini blue button ' + getColor(RCASelected),
                                        style: 'margin-left:25px;',
                                        text: btnName
                                    }).notify('click', {
                                        type: [sb.moduleId+'-run'],
                                        data: {
                                            run: function () {
                                                console.warn('toggle mark complete');

                                                ui.saveBtn.loading();
                                                var _nextStatus = getNextStatus([RCASelected]);
                                                var taggedWith = obj.tagged_with.filter(function(item) {
                                                    return !([RCA,RCACompleted,RCAUploads,RCAComments].includes(item))
                                                });

                                                if(_nextStatus) {
                                                    taggedWith = taggedWith.concat(_nextStatus);
                                                }

                                                sb.data.db.obj.update(
                                                    obj.object_bp_type
                                                    , {
                                                        id: obj.id
                                                        , tagged_with: taggedWith
                                                    }
                                                    , function (updated) {

                                                        ui.saveBtn.loading(false);

                                                        sb.notify({
                                                            type: 'display-alert',
                                                            data: {
                                                                header: 'Updated state',
                                                                body: 'Your RCA Item was updated successfully',
                                                                color: 'green'
                                                            }
                                                        });

                                                        refreshRCAButton(ui, _nextStatus);

                                                    }
                                                );

                                            }
                                        }
                                    });
								

									ui.patch();
								}

								function removeTags(str) {
									if ((str===null) || (str===''))
										return false;
									else
										str = str.toString();
									return str.replace( /(<([^>]+)>)/ig, ' ');
								}

								if(obj.assigned && obj.assigned.length > 0){
									roleName = obj.assigned[0].role + ' - ' + obj.assigned[0].name;

									_.forEach(obj.assigned, function(_role){
										if(_role.role.toLowerCase().includes("specialist")){
											roleName =  _role.role + ' - ' + _role.name;
										}
									});

								}
                                if ( obj.parent ) {

                                    if(obj.parent && obj.parent.id) {
                                        linkActionItem = "/app/" + obj.instance + "#hq&1=hqt-projectTool-Projects&2=o-project-" + obj.parent.id + "-" + encodeURIComponent(obj.parent.name) + '&3=pt-entityList-Client Action items&4=e-' + obj.id + "-" + encodeURIComponent(obj.name);
                                    }

                                    switch ( obj.parent.object_bp_type ) {
                                        case 'companies':
                                          _companyName = obj.parent.name;
                                          rowItemTitle = '<div class="link truncate"><i class="grey building icon"></i> ' + _companyName +
                                                '<div class="sub header">'+ obj.name + '<span><div class="ui mini label">Organization</div></span></div></div>';
                                        break;
                                        case 'groups':
                                          _companyName = '';
                                          if ((obj.parent.main_contact || {}).company) {
                                            _companyName = obj.parent.main_contact.company.name;
                                          }

                                          rowItemTitle = '<div class="link truncate"><i class="grey building icon"></i> ' + _companyName +
                                                '<div class="sub header">'+ obj.name + '' +
											  '<span><div class="ui mini label">Project - ' + obj.parent.name + '</div></span>' +
											  '</div></div>';
                                              
                                            if ( roleName ) {
                                                  
                                                  rowItemTitle = '<div class="link truncate"><i class="grey building icon"></i> ' + _companyName +
                                                        '<div class="sub header">'+ obj.name + '' +
                                                      '<span><div class="ui mini label">Project - ' + obj.parent.name + '</div></span>' +
                                                      '<span><div class="ui mini label">' + roleName + '</div></span>' +
                                                      '</div></div>';

                                            }
                                        break;
                                        default:
                                        var blueprintname = obj.parent.object_bp_type.substring(1);
                                        var parent_blueprint = _.find(appConfig.Types, {bp_name: blueprintname});
                                        rowItemTitle = '<div class="link truncate"><i class="grey building icon"></i> ' + _companyName+ '<div class="sub header">'+ obj.name +
											'<span><div class="ui mini label">'+ parent_blueprint.name +'</div></span></div></div>';
                                        
                                        if ( roleName ) {
                                            rowItemTitle = '<div class="link truncate"><i class="grey building icon"></i> ' + _companyName + '<div class="sub header">'+ obj.name +
											'<span><div class="ui mini label">'+ parent_blueprint.name +'</div></span><span><div class="ui mini label">'+ roleName +'</div></span></div></div>';
                                        }
                                    }
								}

								if (_.contains(obj.tagged_with, RCACompleted)) {
									RCASelected = RCACompleted;
								}
								else if (_.contains(obj.tagged_with, RCAComments)) {
									RCASelected = RCAComments;
								}
								else if (_.contains(obj.tagged_with, RCAUploads)) {
									RCASelected = RCAUploads;
								} else if ( _.contains(obj.tagged_with, RCAUploads && _.contains(obj.tagged_with, RCAComments)) ) {
                                    RCASelected = 'both';
                                }

								var note = obj.notes && obj.notes.note && (removeTags(obj.notes.note).length > 120 ? removeTags(obj.notes.note).substring(0, 130) + '...' : removeTags(obj.notes.note));
								
                                ui.makeNode('section-' + obj.id, 'div', {});

								ui['section-' + obj.id].makeNode('rowSection', 'div', {css: 'rowSection'});
								ui['section-' + obj.id]['rowSection'].makeNode('formArea', 'div', {css: 'ui header mini formNameArea'});
								ui['section-' + obj.id]['rowSection']['formArea'].makeNode('linkArea', 'div', {tag: 'a', href: linkActionItem
                                    , text:  rowItemTitle
                                    });

								ui['section-' + obj.id].makeNode('commentSection', 'div', {css: 'commentSection', style:'display:block; margin:10px 0 20px 10px', text: note ? '<span class="text-quoted"><blockquote>' +note + '</blockquote></span>' : ''});

                                ui['section-' + obj.id].makeNode('rowSectionUpdated', 'div', {css: '', style: 'margin:0 0 30px 0'});
                                ui['section-' + obj.id]['rowSectionUpdated'].makeNode('rowSection2', 'div', {css: 'pull-right'});                    
								ui['section-' + obj.id]['rowSectionUpdated']['rowSection2'].makeNode('right-options', 'div', {css: 'rowSection'});                
								ui['section-' + obj.id]['rowSectionUpdated']['rowSection2']['right-options'].makeNode('toggleButton', 'div', {css: 'btn-area', style:'display:flex;align-items:center', text: '' });

								var btnActionUi = ui['section-' + obj.id]['rowSectionUpdated']['rowSection2']['right-options']['toggleButton'];
                                
                                refreshRCAButton(btnActionUi, RCASelected);

                                if ( obj.rca_last_updated_comments && (RCASelected == RCAComments || RCASelected == 'both') ) {
                                    ui['section-' + obj.id]['rowSectionUpdated'].makeNode('com1', 'div'
                                    , {
                                        css: 'ui right icon label'
                                        , style:'margin: 0 10px 0 0'
                                        , text: moment.utc(obj.rca_last_updated_comments).format('ddd, DD MMM YYYY - hh:mm A') + '<i class="ui chat red icon"></i>'
                                    });
                                }

                                if ( obj.rca_last_updated_uploads && (RCASelected == RCAUploads || RCASelected == 'both')) {
                                    ui['section-' + obj.id]['rowSectionUpdated'].makeNode('upl', 'div'
                                        , {
                                            css: 'ui right icon label'
                                            , style:'margin: 0 10px 0 0'
                                            , text: moment.utc(obj.rca_last_updated_uploads).format('ddd, DD MMM YYYY - hh:mm A') + ' <i class="ui upload blue icon"></i>'
                                        });
                                }

								ui.patch();

							}
						}
					},
					menu: false,
					selectedView: 'list',
					subviews: {
						list: {
							range: {
								defaultTo: "last_30",
								includedAll: true,
								not: ["all_time"],
							},
							groupBy: {
								defaultTo: 'by_range'
							},
							backlog: false
						}
					},
					search: true,
					tags: false,
					filterBy: {
						typeFilter: {
							title: 'Status',
							defaultText:  'Comment (Default)',
							defaultValue: 'comments',
							getOptions:   function (callback) {

								var options = [
									{
										name: 'Upload',
										id: 'uploads'
									},
									{
										name: 'RCA',
										id: 'only_rca'
									},
									{
										name: 'Completed',
										id: 'only_completed'
									},
								];

								callback(options);

							},
							parseSelection: function (selection, options) {

								var searchBy;

								switch (selection) {

									case 'only_rca':
										searchBy = RCA;
										break;

									case 'uploads':
										searchBy = RCAUploads;
										break;

									case 'only_completed':
										searchBy = RCACompleted;
										break;
									default:
										searchBy = RCAComments;
										break;

								}

								options.where.searchBy = searchBy;

								options.where.tagged_with = {
									type:     'any'
										, values: [searchBy]
										, and:    {
										tagged_with: [state.id]
									}
								}
							}

						},
						statusFilter: {
							title: 'Project Types',
							defaultText:  'All Project Types (default)',
							defaultValue: '',
							getOptions:   function (callback) {
								var options = _.map(_projectTypes, function(_item) {
									return {id: _item.id, name: _item.name}
								});
								callback(options);
							},
							parseSelection: function (selection, options) {
								options.where.project_type = selection;
							}

						},
						formTypeFilter: {
							title: 'Type',
							defaultText: 'All Types',
							defaultValue: '',
							getOptions:   function (callback) {

								var options = [
									{id: 'info_request', name: 'Info Request'},
									{id: 'client_review', name: 'Client Review'},
									{id: 'action_items', name: 'Action Items'},
								];

								callback(options);
							},
							parseSelection: function (selection, options) {
								options.where.formType = selection;
							}
						},
					},
					showPaging: true,
					pageLength: 25,
					config: {
						customTemplate: 'block'
					},
					sortCol: 'rca_last_updated_comments',
					sortDir: 'desc',
					where: {
						tagged_with: {
							type:     'any'
							, values: [RCAComments]
							, and:    {
								tagged_with: [state.id]
							}
						},
						childObjs: {
							name: true,
							parent: {
								name: true,
								main_contact: {
									company: true
								},
								type: true,
								group_type: true,
								last_updated: true,
                                parent: true
							},
							last_updated: true
                            , rca_last_updated_comments: true
                            , rca_last_updated_uploads: true
						}
					},
					query: function(data, callback){


						//setup
						var intakeQuestionnaire = 4759706; //Intake Form Id
						var actionItems = 4760308; // Action Item ID

						var projectType = data.project_type;
						var formType = data.formType;
						var dateCreated = data.date_created;
						var searchBy = data.searchBy;
						var taggedWith = data.tagged_with;
						var paged = data.paged;

						//format end date (fix)
						if(dateCreated.end){
							dateCreated.end = moment.utc(moment(dateCreated.end, 'X')).local().format('X');
						}

						var memberShipEntityTypeId = 5953145; // #0ZZQgI

						//entities to search
						var conditions = function(item) {
							return (
								item.is_task == true
								&& (item.id == memberShipEntityTypeId || item.tagged_with.includes(intakeQuestionnaire) || item.tagged_with.includes(actionItems))
								&& (!([5329890, 7391772, 2365041].includes(item.id)))
							)
						};

						var _entities = _.filter(appConfig.Types, conditions);
						_entities = _.map(_entities, function(_item) { return _item.bp_name});

						//force sortColumn order

						if(searchBy == RCA || searchBy == RCACompleted){
							data.paged.sortCol = 'last_updated';
						} else if(searchBy == RCAUploads){
							data.paged.sortCol = 'rca_last_updated_uploads';
						} else {
							data.paged.sortCol = 'rca_last_updated_comments';
						}

						var queries = {
							paged: paged,
							entity_types: _entities,
							last_updated: dateCreated,
							tagged_with: taggedWith,
							formType: formType,
							searchBy: searchBy,
							usersInRole: false
						};

						if(hasProjectTypeSearch || projectType){
							queries['project_type'] = projectType ? projectType : hasProjectTypeSearch;
						}

						sb.data.db.service(
							"FGProjectsService",
							"getActionsItemsWithRole",
							queries,
							function (res) {

								data.data = res.data;
								data.recordsFiltered = res.recordsFiltered;
								data.recordsTotal = res.recordsTotal;

								//order by date
								if(queries['searchBy'] == RCAUploads){
									data.data = _.sortBy(data.data, 'rca_last_updated_uploads').reverse();
								}
								else if(queries['searchBy'] == RCAComments){
									data.data = _.sortBy(data.data, 'rca_last_updated_comments').reverse();
								} else {
									data.data = _.sortBy(data.data, 'last_updated').reverse();
								}

								callback(data);
							}
						);

					}
				};

				if(_projectTypes == false){
					delete collectionSetup.filterBy.roleFilter;
					delete collectionSetup.filterBy.statusFilter;
				}

				sb.notify({
					type:'show-collection',
					data: collectionSetup
				});

				draw(ui);

			}

            sb.notify({
				type: 'register-tool',
				data: {
					navigationItem: {
						moduleId: 	sb.moduleId,
						instanceId: sb.instanceId,
						id: 		'rca',
						title: 		'Recent Client Activity',
						icon: 		'<i class="stream icon"></i>',
						views: [
							// Project outbox tool
							{
								id: 	'rcaNotifications',
								name: 	'Recent Client Activity',
								tip: 	'Notifications from RCA events',
					 			icon: {
									type: 	'stream',
									color: 	'red'
								},
								default: true,
								settings: false,
								availableToEntities: true,
								mainViews: [{
									//mainView
									dom: function(ui, state, draw) {

										const teamTags = state.pageObject.tagged_with;
										databaseConnection.obj.getWhere('users', {
											'id': {
												'type': 'or',
												'values': teamTags,
											},
											'childObjs': {
												'id': true,
												'profile_image': true,
												'fname': true,
												'lname': true,
												'object_bp_type': true
											}
										}, function(teamUsers) {
											sb.data.db.obj.getWhere('project_types', [], function (_projectTypes) {
												domRecentClientActivity(ui, state, draw, _projectTypes, teamUsers);
											});

										});
									}
								}], //not filtered by project type
								layers: ['team', 'project', 'myStuff'],
								boxViews: [
                                    {
                                        id: "rcaByProjectType",
                                        width: "eight",
                                        title: "RCA by Project Type",
										dom: function(ui, state, draw) {

											const teamTags = state.team.tagged_with;
											databaseConnection.obj.getWhere('users', {
												'id': {
													'type': 'or',
													'values': teamTags,
												},
												'childObjs': {
													'id': true,
													'profile_image': true,
													'fname': true,
													'lname': true,
													'object_bp_type': true
												}
											}, function(teamUsers) {
												domRecentClientActivity(ui, state, draw, false, teamUsers);
											});
										},
                                        settingsView: function (ui, settings, onComplete) {

                                            viewGroupsByTypeSettings(ui, settings, onComplete, {
                                              type_bp: "project_types",
                                              objectName: "project",
                                              object_bp_type: "groups",
                                              group_type: "Project",
                                            });
                                          },
                                      }
                                ] //filtered by project type
							}
						]
					}
				}
			});

		},

		run:function(data){ data.run(); }

	}

});
