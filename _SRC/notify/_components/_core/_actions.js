Factory.register ('actions', function (sb) {
	
	var	Defaults = {
		actionOptions: {
			_availableToUser: {
				name: 		'Available to User'
				, description: 'If <strong>Yes</strong>, this will appear as an action available in the actions menu when the object is in this state.'
				, type: 	'bool'
				, availableToDeferred: false
			}
			, _requiresInput: {
				name: 		'Prompt user'
				, description: 'If <strong>Yes</strong>, the user will be prompted to run this action when the object is moved to this state.'
				, type: 	'bool'
				, availableToDeferred: false
			}
			, _description: {
				name: 		'Description'
				, type: 	'text'
				, availableToDeferred: false
			}
		}
	};
	var Actions = [];
	
	// Create/edit options
	function getOptionsForm (ui, typeDef, obj, state, config, callback) {

        if (config === undefined) {
			config = {
				name: true
			};
		}
		
		var ret = obj.options || {};
		
		// Set default values.
		_.each(typeDef.options, function (option, optionKey) {
			
			if (option.type === 'bool') {
				
				if (
					option.hasOwnProperty('default')
					&& typeof ret[optionKey] !== 'boolean'
				) {
					ret[optionKey] = option.default;
				}

			} else if (
				option.hasOwnProperty('default')
				&& _.isEmpty(ret[optionKey])
			) {
				
				ret[optionKey] = option.default;
				
			}
			
		})
		
		function getMergeData (object, onComplete) {
			
			function getBlueprint (state, callback) {
			
				if (state.object.object_bp_type === 'entity_workflow') {
					
					var idToGet = state.object.parent;
					if (
						state.object.parent
						&& state.object.parent.id
					) {
						idToGet = state.object.parent.id;
					}
				
					sb.data.db.obj.getById(
						'entity_type'
						, idToGet
						, function (bp) {
							
							callback(bp.blueprint);
							
						}
					);
					
				} else if (
					state.object
					&& state.object.object_bp_type
					&& state.object.object_bp_type.charAt(0) === '#'
				) {

					var bp = _.findWhere(appConfig.Types, {
						bp_name: state.object.object_bp_type.substr(1)
					});
				
					callback(bp.blueprint);
					
				} else {
					
					var bpToGet = '';
					switch (state.object.object_bp_type) {
						
						case 'groups':
						case 'task_types':
						case 'project_types':
							bpToGet = 'groups';
							break;
							
						case 'contact_types':
							bpToGet = 'contacts';
							break;
						
					}
					
					sb.data.db.obj.getBlueprint (bpToGet, function (bp) {
						
						callback(bp);
						
					}, false);
					
				}
				
			}
			
			getBlueprint (state, function (bp) {
				
				if (
					object
					&& (
						object.object_bp_type !== 'project_types'
						&& object.object_bp_type !== 'contact_types'
						&& object.object_bp_type !== 'company_categories'
						&& object.object_bp_type !== 'entity_workflow'
					)
				) {
					
					// Build selection obj for getting data for merging in
					// the form.
					var selectionObj = {
						selectionObj: true // Tells api to use selection obj.
						, name: true
					};
					var splits = [];
					
					_.each(typeDef.options, function (option, key) {

						if (
							option.type === 'field'
							&& option.merge
						) {
							
							if (
								obj
								&& obj.options
								&& obj.options[key]
							) {
								//!REF:: Building selection objs w.bps and fields 
								// Multi select type fields.
								if (Array.isArray(obj.options[key])) {
									
									_.each(obj.options[key], function (propName) {
										
										if (bp[propName]) {
											
											if (typeDef.options[key].select) {
												
												selectionObj[propName] = typeDef.options[key].select;
												
											} else {
												
												selectionObj[propName] = true;
												
											}
											
										} else if (propName.charAt(0) === '#') {

											splits = propName.split('.');

											// Reset to dot seperator, for subsets
											splits[0] = splits[0].split('-').join('.');

											if (!selectionObj[splits[0]]) {
												selectionObj[splits[0]] = {};
											}
											
											if (typeDef.options[key].select) {
												
												selectionObj[splits[0]][splits[1]] = typeDef.options[key].select;
												
											} else {
												
												selectionObj[splits[0]][splits[1]] = true;
												
											}
											
										} else if (
											propName.split('.')[0] === 'parent' 
											&& propName.split('.')[1] === 'main_contact'
										) {
											
											if (
												typeDef
												&& typeDef.options
												&& typeDef.options[key]
												&& typeof typeDef.options[key].select === 'object'
												&& !_.isEmpty(typeDef.options[key].select)
											) {
												selectionObj.parent = {
													main_contact: typeDef.options[key].select
												};
											} else {
												selectionObj.parent = {
													main_contact: true
												};
											}
											
										} else if (propName.split('.')[0] === 'parent') {
											
											if (
												typeDef
												&& typeDef.options
												&& typeDef.options[key]
												&& typeof typeDef.options[key].select === 'object'
												&& !_.isEmpty(typeDef.options[key].select)
											) {
												selectionObj.parent = typeDef.options[key].select;
											} else {
												selectionObj.parent = true;
											}
											
										}
										
									});
								
								// Single select type fields.
								} else {
									
									if (bp[obj.options[key]]) {
										
										if (typeDef.options[key].select) {
											
											selectionObj[obj.options[key]] = typeDef.options[key].select;
											
										} else {
											
											selectionObj[obj.options[key]] = true;
											
										}
										
									} else if (obj.options[key]) {
										
										splits = obj.options[key].split('.');
										if (!selectionObj[splits[0]]) {
											selectionObj[splits[0]] = {};
										}
										
										if (typeDef.options[key].select) {
											
											selectionObj[splits[0]][splits[1]] = typeDef.options[key].select;
											
										} else {
											
											selectionObj[splits[0]][splits[1]] = true;
											
										}
										
									}
									
								}
								
							}
							
						}
						
					});

					if (!_.isEmpty(selectionObj)) {
						
						sb.data.db.obj.getById(
							object.object_bp_type
							, object.id
							, function (mergedData) {

								onComplete(bp, mergedData);
								
							}
							, selectionObj
						);
						return;
						
					} else {
						
						onComplete(bp, object);
						return;
						
					}
					
				} else {
					
					onComplete(bp, false);
					return;
					
				}
				
			});
			
		}

		function processForm (form, typeDef) {
			
			var formData = form.process().fields;
			
			if (config.eventType) {
				ret._actionName = formData._actionName.value;
			}
			
			_.each(typeDef.options, function (option, key) {
				
				if (
					option.availableToDeferred !== false
					|| config.isDeferred !== true
				) {
				
					switch (option.type) {
						
						case 'bool':
							if (formData[key] && formData[key].value == true) {
								ret[key] = true;
							} else {
								ret[key] = false;
							}
							break;
						
						case 'select':
						case 'string':
						case 'text':
							if (!_.isEmpty(formData[key])) {
								ret[key] = formData[key].value;
							}
							break;
							
						case 'tags':
							if (!_.isEmpty(tags[key])) {
								ret[key] = _.pluck(tags[key], 'id');
							} else {
								tags[key] = [];
							}
							break;
						
					}
					
				}
				
			});
			
			if(typeof typeDef.parseOptions === 'function') {
				
				ret.parsedData = typeDef.parseOptions(formData);
				
			}

			return ret ;
			
		}
		
		// Option types
		// !TODO: move these over to fields.
		function collectionOption(ui, obj, option) {
			
			var objectType = option.objectType;
			if (
				option.objectType.startsWith('.')
			) {

				if (
					_.isEmpty(
						ret[option.objectType.slice(1)]
					)
				) {
					return;
				}
				
				objectType = ret[option.objectType.slice(1)];
				
			}
			
			var collSetup = {
				actions: {
					type: 'quick'
					, create: {
						type: 'quick'
						, action: function (seed, callback) {
							
							option.create(function (seed) {
								
								seed.is_template = 1;
								seed.parent = obj.id;
								sb.data.db.obj.create (
									objectType
									, seed
									, function (created) {
										callback(created);
									}
								) ;
								
							});
							
						}
					}
				}
				, domObj: ui
				, fields: {
					name: {
						type: 'title'
						, title: 'Name'
					}
				}
				, groupings: 	{}
				, menu: {
					subviews: false,
					actions: false
				}
				, modalSize: 	'small'
				, objectType: 	objectType
				, onBoxview: 		true
				, selectedView: 	'list'
				, selectedMobileView: 	'list'
				, shouldKeepSegment: true
				, singleView: {
					view: option.view
				}
				, state: 			state
				, subviews: {
					list: {
						backlog: true
						, range: 'all_time'
					}
				}
				, submenu: false
				, where: {
					parent: 			obj.id
					, is_template: 	1
					, childObjs: {
						name: 			true,
						group_type: 	true
					}
				}
			};
			
			if (option.group_type) {
				collSetup.where.group_type = option.group_type;
			}

			sb.notify({
				type: 'show-collection',
				data: collSetup
			});
			
		}
		
		function objectOption (ui, optionKey, obj, option, onComplete) {
			
			function viewSelection (option, selectedObj, onComplete) {
				
				ui[optionKey].makeNode('disp', 'div', {});
				if (typeof option.viewSelection === 'function') {
					
					option.viewSelection(
						ui[optionKey].disp
						, selectedObj
						, state.object
						, obj.options
					);
					
				}
				if (typeof onComplete === 'function') {
					onComplete();
				}
				
			}
			
			var searchSetup = {
				css: 'ui fluid category transparent search'
				, text:
					'<div class="ui left icon fluid input">'+
						'<input value="" class="prompt" type="text" placeholder="Find something..">'+
						'<i class="search icon"></i>'+
					'</div>'+
					'<div class="results"></div>'
				, listener: {
					type: 		'search',
					objectType: option.objectType,
					onSelect: 	function(optionKey, result, response){
						
						if (!_.isEmpty(result)) {

							ret[optionKey] = result.id;
							viewSelection(option, result, function () {}, obj.options);
							
						}
						
					}.bind({}, optionKey),
					selection: {
						name: 		true
						, icon: 	true
						, bp_name: 	true
					}
				}
			};
			
			if (option.where) {

				if ( typeof option.where === 'function' ) {
					searchSetup.listener.selection.where = option.where(state.object);
				} else {
					searchSetup.listener.selection.where = option.where;
				}
			}

			// Option label
			ui[optionKey].makeNode(
				'label'
				, 'div'
				, {
					tag: 		'h5'
					, text: 	option.name
					, style: 	'margin-bottom: 4px;margin-top: 14px;'
					, css: 		'ui header'
				}
			);

			// Helper text
			if (!_.isEmpty(option.helperText)) {
				
				ui[optionKey].makeNode(
					'ht'
					, 'div'
					, {
						tag: 		'small'
						, text: 	option.helperText
						, css: 		'text-muted'
					}
				);
				
			}
			
			if (
				obj.options
				&& obj.options[optionKey]
			) {
				
				sb.data.db.obj.getById(
					option.objectType
					, parseInt(obj.options[optionKey])
					, function (selectedObj) {

						// Hide search if deferred and option is flagged to hide 
						// searching when deferred.
						if (
							!(
								option.searchAvailableToDeferred === false
								&& !_.isEmpty(state.notification)
							)
						) {

							var selectedTxt = '';
							if (!_.isEmpty(selectedObj)) {
								selectedTxt = selectedObj.name;
							}
							
							searchSetup.text =
									'<div class="ui left icon fluid input">'+
										'<input value="'+ selectedTxt +'" class="prompt" type="text" placeholder="Find something..">'+
										'<i class="search icon"></i>'+
									'</div>'+
									'<div class="results"></div>';
	
									ui[optionKey].makeNode(
										'search'
										, 'div'
										, searchSetup
									);
							

						}

						viewSelection(option, selectedObj, onComplete);
						
					}
				);
				
				
			} else {
				
				ui[optionKey].makeNode(
					'search'
					, 'div'
					, searchSetup
				);
				ui[optionKey].makeNode('disp', 'div', {});
				if (typeof onComplete === 'function') {
					onComplete();
				}
				
			}
			
		}
		
		var selection = {};
		var tags = {};
		var Comps = {};
		
		ui.empty();
		ui.makeNode('loader', 'div', {text: '<i class="ui grey notched circle loading icon"></i>'});
		ui.patch();
		
		getMergeData(state.object, function (bp, mergedData) {

			if (mergedData) {
				ret.isMerged = true;
			}
			
			ui.empty();
			
			if (config.isDeferred) {
			
				ui.makeNode(
					'h'
					, 'div'
					, {
						css: 'ui header'
						, tag: 'h1'
						, text: obj.name				}
				);
				
				if (
					obj.options
					&& !_.isEmpty(obj.options._description)
				) {
					
					ui.makeNode(
						'description'
						, 'div'
						, {
							css: ''
							, tag: ''
							, text: obj.options._description
						}
					);
					
				}
				
			} else {
				
				ui.makeNode(
					'h'
					, 'div'
					, {
						css: 'ui header'
						, tag: 'h5'
						, text: 'Create a '+ typeDef.title +' action'
					}
				);
				
			}
			
			// placeholder
			ui.makeNode('form', 'div', {});
			ui.makeNode('form-br', 'div', {text: '<br />'});
			
			var formSetup = {};
			if (config.eventType) {
				formSetup._actionName = {
					label: 		'Name'
					, value: 	obj.name || typeDef.title
					, type: 	'text'
					, name: 	'_actionName'
				};
			}
			
			_.each(typeDef.options, function (option, optionKey) {
				
				if (
					option.availableToDeferred !== false
					|| config.isDeferred !== true
				) {
				
					switch (option.type) {
						
						case 'bool':
							formSetup[optionKey] = {
								name: optionKey
								, type: 'check'
								, label: option.name
								, value: (ret[optionKey] === true)
								, options:[{
									name: 			optionKey
									, isImmutable: 	(option.isImmmutable === true)
									, label:		'Yes'
									, value:		'yes'
									, checked:		(ret[optionKey] === true)
								}]
								, helperText: option.description
							};
							break;
						
						case 'objectType':
							
							var searchPlaceholder = 'Find a data set..';
							if (
								ret[optionKey]
								&& typeof ret[optionKey] === 'string'
								&& _.findWhere(appConfig.Types, {bp_name: ret[optionKey].substr(1)})
							) {
								searchPlaceholder = _.findWhere(appConfig.Types, {bp_name: ret[optionKey].substr(1)}).name;
							}
							
							ui.makeNode(
								optionKey
								, 'div'
								, {
									css: 'ui fluid category search'
									, text:
										'<div class="ui left icon fluid input">'+
											'<input class="prompt" type="text" placeholder="'+ searchPlaceholder +'">'+
											'<i class="search icon"></i>'+
										'</div>'+
										'<div class="results"></div>'
									, listener: {
										type: 		'search',
										objectType: 'entity_type',
										onSelect: 	function(optionKey, result, response){

											if (!_.isEmpty(result)) {
												
												if(result.bp_name){
													ret[optionKey] = '#'+ result.bp_name;
												}else{
													ret[optionKey] = '#'+ _.findWhere(appConfig.Types, {id:result.id}).bp_name;												
												}
												
												ret[optionKey +'_name'] = result.name;
												
												if (
													typeDef.options.templates
													&& typeDef.options.templates.type === 'collection'
												) {
													
													collectionOption(
														ui['templates']
														, obj
														, typeDef.options.templates
													);
													
												}
												
											}
											
										}.bind({}, optionKey),
										selection: {
											name: 		true
											, icon: 	true
											, bp_name: 	true
										}
									}
								}
							);
							break;
							
						case 'object':
							ui.makeNode(
								optionKey
								, 'div'
								, {}
							);
							break;
						
						case 'collection':
							ui.makeNode(
								optionKey
								, 'div'
								, {}
							);
							break;
							
						case 'select':
						
							formSetup[optionKey] = {
								name: 		optionKey
								, type: 	'select'
								, label: 	option.name
								, value: 	ret[optionKey]
								, options: 	option.options
							};
							
							if(typeof option.options === 'function') {
								
								formSetup[optionKey].options = option.options(obj, state, config);
								
							}
							
							break;
							
						case 'string':
							formSetup[optionKey] = {
								name: 			optionKey
								, type: 		'text'
								, label: 		option.name
								, value: 		ret[optionKey]
								, helperText: 	option.helperText
							};

							if (
								option.merge
								&& typeof formSetup[optionKey].value === 'string'
								&& formSetup[optionKey].value.startsWith('{{This.')
								&& formSetup[optionKey].value.split('.').length > 0
								&& bp 
							) {

								function getFieldKeyFromName (blueprint, fieldName) {

									var fieldKey = '';
									_.each(blueprint, function (field, key) {
										if (field.name === fieldName) {
											fieldKey = key;
										}
									});
									
									return fieldKey;
	
								}

								var fieldRefName = formSetup[optionKey].value.split('.')[1].replace('}}', '');
								var fieldRefKey = getFieldKeyFromName(bp, fieldRefName);
								if (typeof state.object[fieldRefKey] === 'string') {
									formSetup[optionKey].value = state.object[fieldRefKey];
								} else {
									formSetup[optionKey].value = '';
								}

							}
							break;
							
						case 'tags':
							Comps.tags = sb.createComponent('tags');
							ui.makeNode(
								optionKey +'br'
								, 'lineBreak'
								, {}
							);
							ui.makeNode(
								optionKey +'label'
								, 'div'
								, {
									text: '<h5>'+ option.name +'</h5>'
								}
							);
							ui.makeNode(
								optionKey
								, 'div'
								, {}
							);
							ui.makeNode(
								optionKey +'br2'
								, 'lineBreak'
								, {}
							);
							break;
							
						case 'text':
							formSetup[optionKey] = {
								name: 		optionKey
								, type: 	'textbox'
								, label: 	option.name
								, value: 	ret[optionKey]
							};
							break;
							
						case 'field':

							var bpToUse = bp;
							// If a reference to another option, check for the option
							if (option.blueprint) {
								
								bpToUse = false;
								if (ret[option.blueprint]) {
									
									var foundBP = false;
									foundBP = _.findWhere(
										appConfig.Types
										, {
											bp_name: ret[option.blueprint].substr(1)
										}
									);
									
									if(foundBP){
										bpToUse = foundBP.blueprint;
									}else{
										return;
									}
									
								}
								
								if (!bpToUse) {
									return;
								}
								
							}

							propertySelection(
								ui.makeNode(optionKey, 'div', {})
								, option
								, optionKey
								, {
									object: mergedData
								}
								, ret
								, bpToUse
							);
							break;
						
					}
					
				}
				
			});
			
			ui.makeNode('form', 'form', formSetup);
			ui.process = processForm.bind(
				{}
				, ui.form
				, typeDef
			) ;
			
			ui.patch();
			
			_.each(typeDef.options, function (option, optionKey) {
			
				if (
					option.availableToDeferred !== false
					|| config.isDeferred !== true
				) {
				
					switch (option.type) {
						
						case 'tags':
							var selectedTags = [];
							
							if (
								obj
								&& obj.options
								&& Array.isArray(obj.options[optionKey])
							) {
								
								selectedTags = _.map(obj.options[optionKey]
												, function (t) {
													return parseInt(t);
												});
								
							} 
							
							// !TODO: make ''pullFrom' flag available to all
							// option types.
							if (
								typeDef.options[optionKey]
								&& typeDef.options[optionKey].pullFrom
								&& obj.options
								&& obj.options[typeDef.options[optionKey].pullFrom]
							) {
								
								if (Array.isArray(obj.options[typeDef.options[optionKey].pullFrom])) {
									
									_.each(obj.options[typeDef.options[optionKey].pullFrom], function (field) {
										
										if (mergedData[field]) {
											
											selectedTags.push(
												mergedData[field].id
											);
										
										// Read hardcoded properties from parent project.
										} else if (
											field.split('.')[0] === 'parent'
											&& field.split('.')[1]
											&& mergedData.parent
											&& mergedData.parent[field.split('.')[1]]
										) {
											
											selectedTags.push(
												mergedData.parent[field.split('.')[1]]
											);
											
										}
										
									});
									
								}
								
							}
							
							Comps.tags.notify({
								type: 'object-tag-view',
								data: {
									domObj: 		ui[optionKey],
									objectType: 	'',
									tags: 		selectedTags,
									onChange: 	function (key, response) {
				
										tags[key] = response;
				
									}.bind({}, optionKey),
									canEdit: true
								}
							});	
							break;
						
						case 'collection':
							collectionOption(
								ui[optionKey]
								, obj
								, option
							);
							break;

						case 'table':

							var related_object = undefined;

							if(!option.isProject){
								//var _entity = _.findWhere(appConfig.Types, {bp_name: state.object.object_bp_type.substr(1)});
								//var isClientReview = _entity._class == 1712510; //ClientReview
								//var isClientRequest = _entity._class == 1687652; //ClientRequest

								related_object = state.object.id;
							}

							var projectContext = state.object.parent.id;
							var tagged_with = [projectContext];

						function showLinkFile(ui, obj) {
							ui.makeNode('row-' + obj.id, 'div', {});

							ui['row-' + obj.id].makeNode('lnk-'+obj.id, 'div', {
								tag: 'a',
								href: 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/'+ obj.instance +'/'+ obj.loc,
								style: 'margin-right:2px',
								text: '<div class="ui basic label">'+
									'<i class="ui download icon"></i>'+
									obj.name +
									'</div>'
							});

							//ui.patch();
						}


						function showSelectButton(ui, obj) {

							ui.makeNode('row-' + obj.id, 'div', {});

							var rowCheckbox = ui['row-' + obj.id].makeNode('checkbox', 'div', {
								css: 'ui checkbox',
								text:'<input type="checkbox" data-id="' + obj.id + '"><label></label>',
								listener:{
									type:'checkbox',
								}
							});

							$(document).ready(function() {
								$(rowCheckbox.selector + ' input[type="checkbox"]').on('change', function() {
									currentSelection = [];
									$(tableUI.selector + ' table input[type="checkbox"]').each(function() {
										if ( $(this).is(":checked") && !_.isEmpty($(this).attr('data-id')) ) {
											currentSelection.push($(this).attr('data-id'));
										}
									});

									ret[optionKey] = currentSelection;
									ui.patch();
								});
							}.bind({}, optionKey));

						}

							ui.makeNode('tableDiv' + optionKey , 'div', {
								style: 'margin-bottom: 20px'
							});

							ui['tableDiv' + optionKey].makeNode('table-label', 'div', {
								text: option.name,
								css: 'ui field',
								tag:'label'
							});

							var tableUI = ui['tableDiv' + optionKey].makeNode('tableUI' + optionKey , 'div', {});

							ui.patch();

							sb.notify({
								type: 'show-collection'
								, data: {
									actions: {
										create: false,
										view: false,
									},
									domObj: tableUI,
									fields: {
										select: {
											title: '-',
											view: function(ui, obj) {
												showSelectButton(ui, obj, state, ret);
											}
										},
										name: {
											title: "Name",
											view: function(ui, obj) {
												showLinkFile(ui, obj);
											}
										}
									},
									objectType: option.objectType,
									singleView: {
										select: 2,
									},
									selectedView: "table",
									state: state,
									menu: false,
									groupings: false,
									hideDateRange: true,
									search: false,
									tags: false,
									onBoxview: true,
									size:'large',
									sortCol: "date_created",
									sortDir: "desc",
									submenu: false,
									searchAboveCollection: false,
									subviews: {
										table: {
											hideRowActions: true,
											hideSelectionBoxes: true,
										},
									},
									where: {
                                        related_object,
										tagged_with,
										is_archived: 0,
										is_deleted: 0,
										is_public: 1,
										childObjs: {
											id: true,
											name: true,
											loc: true,
											file_type: true,
										}
									}
								},
							});
							break;
							
						case 'object':
							objectOption(
								ui
								, optionKey
								, obj
								, option
								, function () {
									ui[optionKey].patch();
								}
							);
							break;

						case 'schedule': 

							ui.makeNode('schedule-padding-top', 'div', {
								style: 'padding-top:15px;'
							});

							var scheduleUI = ui.makeNode('schedule', 'div', {});

							ui.makeNode('schedule-padding-bottom', 'div', {
								style: 'padding-bottom:15px;'
							});

							ui.patch();

							sb.notify ({
								type: 'view-field',
								data: {
									type: 'schedule',
									property: 'schedule',
									obj: obj.options,
									options: {
										edit: true,
										editing: true,
										commitUpdates: false
									},
									ui: scheduleUI
								}
							});

							break;
				
					}
				
				}
				
			});

			if (typeof callback === 'function') {
				callback();
			}
			
		});
		
	}
	
	//!TODO: Move to rules on back-end.
	function logEvent (obj, eventType, response) {
		
		function getObjectPageParams(item){
	
			var objType = item.object_bp_type;
			if(objType === 'groups'){
				objType = item.group_type.toLowerCase();
			}
			
			var objName = item.name;
			switch(objType){
				
				case 'contacts':
				case 'users':
				objName = item.fname +' '+ item.lname;
				break;
				
				case 'tasks':
				objName = item.title;
				break;
					
				}
				
				return sb.data.url.createPageURL(
						'object', 
						{
							type:objType, 
							id:item.id,
							name:objName
						}
					);
			
		}
		
		var typeDef = _.findWhere(
			Actions
			, {
				name: eventType.type
			}
		);
		
		var	newComment = {
				type_id: 		obj.id,
				author: 		+sb.data.cookie.userId,
				note: 			'Triggered <i class="'+ typeDef.color +' '+ typeDef.icon +' icon"></i> <strong>'+ eventType.name +'</strong> action.',
				note_type: 		0,
				edited: 		0,
				public: 		0
			};

		if (typeof typeDef.parseLog === 'function') {
			newComment = typeDef.parseLog(newComment, response);
		}
		
		if (
			appConfig.state
			&& appConfig.state.portal > 0
		) {
			newComment.public = 1;
		}
		
		sb.data.db.obj.postComment(newComment, function(created){}, 2);
		
	}
	
	function propertySelection (ui, option, optionKey, state, obj, bp) {
		
		// If applying to an object and not config on the type/workflow,
		// pass in the object as state to the field.
		var fieldState = false;
		if (
			state.object
			&& (
				state.object.object_bp_type !== 'project_types'
				&& state.object.object_bp_type !== 'contact_types'
				&& state.object.object_bp_type !== 'company_categories'
				&& state.object.object_bp_type !== 'entity_workflow'
			)
		) {
			fieldState = state.object;
		}
		
		ui.makeNode('label', 'div', {
			tag: 		'h5'
			, text: 	option.name
			, css: 		'ui header'
			, style: 	'margin-bottom:4px;'
		});

		if (!_.isEmpty(option.helperText)) {

			ui.makeNode('ht', 'div', {
				tag: 		'small'
				, text: 	option.helperText
				, css: 		'text-muted'
			});

		}
		
		sb.notify({
			type: 'view-field-selection'
			, data: {
				ui: 			ui.makeNode('v', 'div', {})
				, blueprint: 	bp
				, options: 		{
					allowedTypes: 	option.allowedTypes
					, multi: 		option.multi
					, onUpdate: 	function (val) {
						
						obj[optionKey] = val;
						if (option.onChange) {
							option.onChange(
								obj
								, val
							);
						}
						
					}
					, merge: 		option.merge
					, parseMerge: 	option.parseMerge
					, value: 		obj[optionKey]
					, hidden:  		(option.availableToDeferred === false)
				}
				, state: fieldState
			}
		});
		
	}
	
	function makeAction () {
		
		return {
			type: 		'post'
			, state: 	0
			, trigger: 	'state-change'
		} ;
		
	}
	
	function showMenu (ui, actions, obj) {
		
		var isPublic = false;
		
		// In client portal
		if (
			appConfig
			&& appConfig.state
			&& appConfig.state.portal
		) {
			isPublic = true;
		}
		
		function showObjHistory (ui, obj) {
			
			function getChildData (obj, callback) {
				
				if (
					typeof obj.created_by === 'number'
					|| typeof obj.last_updated_by === 'number'
				) {
					
					sb.data.db.obj.getById(
						'users'
						, [obj.created_by, obj.last_updated_by]
						, function (data) {
							
							callback(
								_.findWhere(data, {id: obj.created_by})
								, _.findWhere(data, {id: obj.last_updated_by})
							);
							
						}
						, {
							created_by: {
								fname: true
								, lname: true
							}
							, last_updated_by: {
								fname: true
								, lname: true
							}
						}
					);
					
				} else if (
					!_.isEmpty(obj.created_by)
					&& !_.isEmpty(obj.created_by)
				) {
					
					callback(
						obj.created_by
						, obj.last_updated_by
					);
					
				}
				
			}
			
			var createdOn = moment(obj.date_created, 'YYYY-MM-DD HH:mm:ss.SS');
			var updatedOn = moment(obj.last_updated, 'YYYY-MM-DD HH:mm:ss.SS');
			
			if (
				!createdOn.isValid()
				|| !updatedOn.isValid()
			) {
				return;
			}
			
			if (obj.updatedBy && obj.createdBy) {

				getChildData(
					obj
					, function (createdBy, updatedBy) {
					
						var updatedTxt = ''+ updatedOn.fromNow();
						var createdTxt = 'Created '+ createdOn.fromNow();
					
						// Page history
						menu.makeNode('hdiv', 'div', {css: 'ui clearing divider'});
						menu.makeNode(
							'created'
							, 'div'
							, {
								css: 'grey header'
								, text: '<i class="history icon"></i> Last updated by '+ updatedBy.fname +' '+ updatedBy.lname +'<br />'+ updatedTxt
								, style: 'text-transform: none;font-weight: normal;'
								, tooltip: 'Created by '+ createdBy.fname +' '+ createdBy.lname +' '+ createdOn.fromNow()
								, tooltipPos: 'left center'
							}
						);
						menu.patch();
					
					}
				);
			}
		}
		
		function showCustomActions (menu, obj, onDraw) {
			
			function drawCustomAction (menu, obj, eventType) {

				if (
					isPublic
					&& (
						!eventType
						|| !eventType.public
					)
				) {
					return;
				}
				
				var actionDef = _.findWhere(
					Actions
					, {
						name: eventType.type
					}
				);
				var disp = {
					color: 		'grey'
					, icon: 	actionDef.icon
					, title: 	eventType.name
				};
				
				menu.makeNode(
					'i-'+ eventType.id, 
					'div', 
					{
						css:'item', 
						text:'<i class=" '+ disp.color +' '+ disp.icon +' icon"></i> '+ disp.title
					}
				);
				
				menu['i-'+ eventType.id].notify('click', {
					type:'run-method',
					data:{
						run:		function (action, eventType) {
							
							var actionBtn = this;
							actionBtn.loading();
							$(actionBtn.selector).find('i').removeClass().addClass('grey notched circle loading icon');
							
							if (
								action.modal
								|| (
									eventType
									&& eventType.object_bp_type === 'event_type'
								)
							) {
								
								sb.notify({
									type: 'get-sys-modal'
									, data: {
										callback: function (modal) {
											
											function deepClone(item) {
												
											    if (!item) { return item; } // null, undefined values check
											
											    var types = [ Number, String, Boolean ], 
											        result;
											
											    // normalizing primitives if someone did new String('aaa'), or new Number('444');
											    types.forEach(function(type) {
											        if (item instanceof type) {
											            result = type( item );
											        }
											    });
											
											    if (typeof result == "undefined") {
											        if (Object.prototype.toString.call( item ) === "[object Array]") {
											            result = [];
											            item.forEach(function(child, index, array) { 
											                result[index] = deepClone( child );
											            });
											        } else if (typeof item == "object") {
											            // testing that this is DOM
											            if (item.nodeType && typeof item.cloneNode == "function") {
											                var result = item.cloneNode( true );    
											            } else if (!item.prototype) { // check that this is a literal
											                if (item instanceof Date) {
											                    result = new Date(item);
											                } else {
											                    // it is an object literal
											                    result = {};
											                    for (var i in item) {
											                        result[i] = deepClone( item[i] );
											                    }
											                }
											            } else {
											                // depending what you would like here,
											                // just keep the reference, or create new object
											                if (false && item.constructor) {
											                    // would not advice to do that, reason? Read below
											                    result = new item.constructor();
											                } else {
											                    result = item;
											                }
											            }
											        } else {
											            result = item;
											        }
											    }
											
											    return result;
											    
											}
											
											// Turn off loader.
											var disp = {
												color: 		action.color
												, icon: 	action.icon
												, title: 	action.title
											};
											if (typeof action.view === 'function') {
												disp = action.view(obj);
											}
											$(this.selector).html('<i class=" '+ disp.color +' '+ disp.icon +' icon"></i> '+ disp.title);
											
											if (modal.body) {
												modal.body.empty();
											}
											
											modal.show();
											
											if (
												eventType
												&& eventType.object_bp_type === 'event_type'
											) {
												
												// Option form
												modal.body.makeNode('opts', 'div', {});

												// Run button
												modal.body.makeNode('runBtn', 'div', {
													text: 'Run Action',
													css: 'ui large green button pull-right',
												}).listeners.push(
													function (selector) {
														
														$(selector).on(
															'click'
															, function () {

																// Turn on loading on button
																modal.body.runBtn.loading();
																
																sb.data.db.obj.runSteps(
																	{
																		[actionDef.name]: {
																			obj: 		obj.id
																			, options: 	modal.body.opts.process()
																		}
																	}
																	, obj.id
																	, function(response) {

																		if (response) {

																			modal.body.empty();
																			modal.body.patch();
																			modal.hide();
																			
																			actionBtn.loading(false);
																			$(actionBtn.selector).find('i').removeClass('grey notched circle loading icon');
																			console.log( 'Line::1488   obj, eventType, response', obj, eventType, response );
																			logEvent(obj, eventType, response);

																		} else {

																			// Turn off loading on button
																			modal.body.runBtn.loading(false);

																			// Show alert
																			sb.notify({
																				type: 'display-alert',
																				data: {
																					header: 'Error Running Action!',
																					body: 'Please try again or reach out to support.',
																					color: 'red'
																				}
																			});

																		}
																		
																	}
																	
																);
																
															}
														);
														
													}
												);

												// Cancel button
												modal.body.makeNode('cancelBtn', 'div', {
													css: 'ui large light-grey basic button pull-right',
													text: 'Cancel'
												}).notify('click', {
													type: 'run-action',
													data: {
														run:function() {

															// Hide modal
															modal.hide();

														}.bind({})
													}
												}, sb.moduleId);

												modal.body.makeNode('clear', 'div', {
													style: 'clear:both'
												});
												
												modal.body.patch();

												getOptionsForm(
													modal.body.opts
													, action
													, deepClone(eventType)
													, {
														object: _.clone(obj)
													}
													, {
														eventType: false
														, isDeferred: true
													}
												);
												
											} else {
												
												action.action (
													obj
													, {}
													, modal.body
													, function () {}
												);
												
											}
											
										}.bind({selector:actionBtn.selector})
										, onClose: 	function () {
											
											$(this.selector).html('<i class=" '+ disp.color +' '+ disp.icon +' icon"></i> '+ disp.title);
											$(this.selector).removeClass('active selected');
											
										}.bind(menu['i-'+ eventType.id])
									}
								});
								
							} else {
								
								action.action (
									obj
									, {}
									, function (obj) {
										
										var disp = {
											color: 		action.color
											, icon: 		action.icon
											, title: 	action.title
										};
										
										if (typeof action.view === 'function') {
											disp = action.view(obj);
										}
										
										$(this.selector).html('<i class=" '+ disp.color +' '+ disp.icon +' icon"></i> '+ disp.title);
										
									}.bind(this)
								);
								
							}
							
						}.bind(menu['i-'+ eventType.id], actionDef, eventType)
// 								}.bind(menu[action.name], action)
					}
				}, sb.moduleId);
				
			}
			
			if (
				obj.group_type === 'Project'
			) {
				
				var typeId = obj.type;
				if (obj.type && obj.type.id) {
					typeId = obj.type.id;
				}
				
				sb.data.db.obj.getWhere(
					'event_type'
					, {
						availableToUser: true
						, state: 		obj.state
						, object: 		typeId
					}
					, function (eventTypes) {
						
						_.each(eventTypes, function (eventType) {
							
							drawCustomAction(menu, obj, eventType);
							
						});
						
						menu.patch();
						onDraw();
						
					}
				);
				
			} else if (obj.object_bp_type.charAt(0) === '#') {

				var entityBp = _.findWhere(
					appConfig.Types
					, {
						bp_name: obj.object_bp_type.substr(1)
					}
				);
				var workflowIds = [];
				_.each(entityBp.blueprint, function (field) {
					
					if (field.fieldType === 'state') {
						
						if (field.workflow && field.workflow.id) {
						
							workflowIds.push(field.workflow.id);
							
						} else if (Number.isInteger(field.workflow)) {
							
							workflowIds.push(field.workflow);
							
						}
						
					}
					
				});
				
				// Get all actions for this data type
				sb.data.db.obj.getWhere(
					'event_type'
					, {
						availableToUser: true
						, object: 		{
							type: 'or'
							, values: workflowIds
						}
					}
					, function (eventTypes) {

						_.each(eventTypes, function (eventType) {
							
							var fieldKey = '';
							_.each(entityBp.blueprint, function (field, key) {
								
								if (field.workflow === eventType.object) {
									
									fieldKey = key;
									
								}
								
							});
							
							if (obj[fieldKey] !== eventType.state) {
								
								return;
								
							}
							
							drawCustomAction(menu, obj, eventType);
							
						});
						
						menu.patch();
						// onDraw();
						
					}
				);
				 
			} else {
				
				onDraw();
				
			}
			
		}
		
		ui.makeNode('actions', 'div', {text:'<i class="ui horizontal ellipsis icon" style="font-size:1.5em !important;"></i>', css:'ui simple dropdown item actions-dropdown', style:'padding-right:0;'});
		var menu = ui.actions.makeNode('menu', 'div', {css:'left menu actions-menu'});
		
		_.each(actions, function (show, actionName) {
			
			if (
				isPublic
				&& (
					!show
					|| !show.public
				)
			) {
				return;
			}
			
			var action = _.findWhere(Actions, {name: actionName});
			if (show && typeof show === 'object' && show.hasOwnProperty('action')) {
				action = show;
				action.name = actionName;
			}

			// A basic link in the menu
			if (show && typeof show.link === 'string') {

				menu.makeNode(
					actionName, 
					'div', 
					{
						tag: 	'a',
						href: 	show.link,
						target: '_blank',
						css: 	'item', 
						text: 	'<i class=" '+ show.color +' '+ show.icon +' icon"></i> '+ show.title
					}
				);
				return;

			// If the menu item is not a link, and an action cannot be found, 
			// stop trying to display the action.
			} else if (!action) {
				
				return;
				
			}
			
			var disp = {
				color: 		action.color
				, icon: 		action.icon
				, title: 	action.title
			};
			
			if (typeof action.view === 'function') {
				
				disp = action.view(obj);
				
			}
			
			menu.makeNode(
				action.name, 
				'div', 
				{
					css:'item', 
					text:'<i class=" '+ disp.color +' '+ disp.icon +' icon"></i> '+ disp.title
				}
			);
			
			menu[action.name].notify('click', {
				type:'run-method',
				data:{
					run:		function (action) {
						
						var actionBtn = this;
						actionBtn.loading();
						$(actionBtn.selector).find('i').removeClass().addClass('grey notched circle loading icon');
							  
						if (action.modal) {
							
							sb.notify({
								type: 'get-sys-modal'
								, data: {
									callback: function (modal) {
										
										if (modal.body) {
											modal.body.empty();
										}
										modal.show();

										action.action (
											obj
											, {}
											, modal
											, function () {}
										);
										
									}
									, onClose: function () {
										
										actionBtn.loading(false);
										$(actionBtn.selector).html('<i class=" '+ disp.color +' '+ disp.icon +' icon"></i> '+ disp.title);
										$(actionBtn.selector).removeClass('active selected');
		
									}.bind(this)
								}
							});
							
						} else {
							
							action.action (
								obj
								, {}
								, function (obj) {
									
									var disp = {
										color: 		action.color
										, icon: 		action.icon
										, title: 	action.title
									};
									
									if (typeof action.view === 'function') {
										disp = action.view(obj);
									}
									
									actionBtn.loading(false);
									$(actionBtn.selector).html('<i class=" '+ disp.color +' '+ disp.icon +' icon"></i> '+ disp.title);
									$(actionBtn.selector).removeClass('active selected');
									
								}.bind(this)
							);
							
						}
						
					}.bind(menu[action.name], action)
				}
			}, sb.moduleId);
			
		});
		
		showCustomActions(menu, obj, function () {
			
			showObjHistory(ui, obj);
			
		});
		
	}

	function viewActionForm (ui, state, seed, onComplete, refresh) {

        var toggleOptionsFormSetup = {};

        if ( state.options && state.options.hasOwnProperty('toggleOptionsForm') ){
            _.mapObject(state.options.toggleOptionsForm, function(v, k){
                toggleOptionsFormSetup[k] = v;
            });
        }

		// var state = seed.state;
		function layoutTypeOption (action) {
			
			ui.select.m.makeNode(
				'a-'+ action.name
				, 'div'
				, {
					css: 'item'
					, dataAttr: [
						{
							name: 		'value'
							, value: 	action.name
						}
					]
					
				}
			).makeNode('txt', 'div', {
				css: 'text'
				, tag: 'span'
				, text: '<i class="'+ action.color +' '+ action.icon +' icon"></i> '+ action.title
			}) ;
			
		}

		if (
			_.isEmpty(seed)
			|| !seed.hasOwnProperty('id')
		) {

            var createSetup = {};

            if ( state.options && state.options.hasOwnProperty('createOptions') ){
                _.mapObject(state.options.createOptions, function(v, k){
                    createSetup[k] = v;
                });
            }

			sb.data.db.obj.create(
				'event_type'
				, createSetup
				, function (created) {
                    sb.data.db.obj.getById('', created.id, function(res){
                        seed = res;
                    });                  
				}
			);
			
		}

		function saveForm(ui, keepOpen) {
					
			var val = $(ui.select.selector).dropdown('get value');
			var typeDef = _.findWhere(
				Actions
				, {
					name: val
				}
			);
			var _availableToUser = false;
			var _requiresInput = false;
			seed.name = typeDef.title;
			seed.type = val;
			if (!_.isEmpty(state.state)) {
				seed.state = state.state.uid;
			}
			seed.object = state.object.id;
			
			if (
				typeDef
				&& !_.isEmpty(typeDef.options)
			) {
				seed.options = ui.opts.process();
				if (
					seed.options._actionName
					&& seed.options._actionName.value != seed.name
				) {
					seed.name = seed.options._actionName;
					delete seed.options._actionName;
				}
				
				if (seed.options._availableToUser) {
					_availableToUser = seed.options._availableToUser
				}
				if (seed.options._requiresInput) {
					_requiresInput = seed.options._requiresInput
				}
				
				if (
					typeDef.hasOwnProperty('parseOptions') 
				) {
					
					if ( !_.isEmpty(seed.options.parsedData) ) {
						seed.parsedData = seed.options.parsedData;	
					}
					
				}
				
			}

			var cb = onComplete;
			if (typeof refresh === 'function') {
				cb = refresh;
			}

            var seedSetup = {
                id: 				seed.id
                , availableToUser: 	_availableToUser
                , name: 			seed.name
                , options: 			seed.options
                , object: 			seed.object
                , requires_input: 	_requiresInput
                , state: 			seed.state
                , type: 			seed.type
                , trigger:          seed.trigger
            };

            if ( 
                seedSetup.type == 'sendEmail' 
                && ( _.isUndefined(seedSetup.trigger)|| _.isNull(seedSetup.trigger) )
            ) {
                seedSetup.trigger = 'workflowSchedule';
            }

			sb.data.db.obj.update(
				'event_type'
                , seedSetup
				, cb
			);

			if (!keepOpen
				&& ui.hasOwnProperty('hide')
				&& typeof ui.hide === 'function'
			) {
				ui.hide();
			}

		}
		
		function toggleOptionsForm (ui, selection) {

            var options = this;
			var typeDef = _.findWhere(
				Actions
				, {
					name: selection
				}
			);
			
			if (_.isEmpty(typeDef.options)) {
				typeDef.options = {};
			}
			_.each(Defaults.actionOptions, function (opt, key) {
				
				var toAdd = _.clone(opt);
				
				// Apply defaults from type definition.
				if (typeDef.options[key]) {
					
					_.each(typeDef.options[key], function (optionVal, optionKey) {
						
						toAdd[optionKey] = optionVal;
						
					});
					
				}
				
				typeDef.options[key] = toAdd;
				
			});
			
			if (
				typeDef
				&& typeDef.options
			) {
				
                if ( options ) {
                   
                    _.each(options, function(v, k){
                        if (typeDef.options[k] && v === false){
                            delete typeDef.options[k];
                        }
                    });
                }

				if (_.isEmpty(seed.options)) {
					seed.options = {};
				}

				getOptionsForm (
					ui.opts
					, typeDef
					, seed
					, state
					, {
						eventType: true
					},
					function() {

						if (!seed.type) {
							saveForm(ui, true);
						}
						

					}
				);
				
			} else {
				
				ui.opts.empty();
				ui.opts.patch();
				
			}
			
		}
		
		ui.makeNode(
			'select'
			, 'div'
			, {
				css: 		'ui fluid search selection dropdown'
				, listener: {
					type: 			'dropdown'
					, autofocus: 	false
					, onChange: 		toggleOptionsForm.bind(toggleOptionsFormSetup, ui)
				}
				, text: 			'<span class="text">Select a type of action to trigger..</span>'+
								'<input type="hidden" name="type" value="'+ seed.type +'">'+
								'<i class="dropdown icon"></i>'
			}
		).makeNode(
			'm'
			, 'div'
			, {
				css: 	'menu'
			}
		);
		
		var visibleActions = _.filter(Actions, function (action) {
			return action.hiddenFromEvents !== true;
		});

        if ( state.options && state.options.hasOwnProperty('actionOptions') ){

            visibleActions =  _.filter(visibleActions, function(act){

                if (state.options.actionOptions.include){

                    if ( _.contains(state.options.actionOptions.include, act.name) )
                        return act;
                } 

            });
        }

		var groupedActions = _.groupBy(visibleActions, 'category');

        if (groupedActions['undefined'] && groupedActions['undefined'].length > 0) {
            // uncategorized action types
            ui.select.m.makeNode('gen', 'div', {
                css: 'text-muted header'
                , text: 'General'
            });
    
            _.each(_.sortBy(groupedActions['undefined'], 'title'), layoutTypeOption);

        }

        if (groupedActions['Share'] && groupedActions['Share'].length > 0) {

            // share actions
            ui.select.m.makeNode('share', 'div', {
                css: 'text-muted header'
                , text: 'Share'
            });
            _.each(_.sortBy(groupedActions['Share'], 'title'), layoutTypeOption);

        }
		
        if (groupedActions['Integrate'] && groupedActions['Integrate'].length > 0) {

            // integration actions
            ui.select.m.makeNode('integrations', 'div', {
                css: 'text-muted header'
                , text: 'Integrate'
            });
            _.each(_.sortBy(groupedActions['Integrate'], 'title'), layoutTypeOption);

        }
		
		// options area
		ui.makeNode('br', 'lineBreak', {spaces: 2});
		ui.makeNode(
			'opts'
			, 'div'
			, {}
		) ;
		
		ui.makeNode('br', 'lineBreak', {spaces: 2});
		ui.makeNode('save', 'div', {
			css: 		'ui teal fluid button'
			, tag: 		'button'
			, text: 	'<i class=""></i> Save'
		}).notify('click', {
			type: 'run-action'
			, data: {
				run: function () {

					saveForm(ui, false);
					
				}
			}
		}, );
		
		ui.patch() ;
		
		if (
			seed
			&& seed.type
		) {
			
			toggleOptionsForm.call(toggleOptionsFormSetup, ui, seed.type);
			
		}
		
	}
	
	function viewActionEditor (ui, state, draw) {

        var whereObj = {
            state:          state.state.uid
            , object:       state.object.id
            , childObjs:    {
                _then: 			true
                , name: 		true
                , options: 		true
                , state: 		true
                , type: 		true
                , entity_type: 	true
                , schedule:     true
                , trigger:      true
            }
        };
        
        var collectionsSetup = {
            domObj:                 ui
            , state:                state
            , actions:{
                create:	function (ui, seed, onComplete, {}, {}, refresh) {
						
						viewActionForm(ui, state, seed, onComplete, refresh);

				}
				, view:            true
				, navigateTo:      false
                , comments:        false
            }
            , fields: {
                type: {
                    type: 	       'title'
                    , title:       'Action'
                    , view:        viewEventType
                }
            }
            , singleView: {
                useCache: 	        true
                , view: function (ui, seed, onComplete, refresh) {

                    viewActionForm(ui, state, seed, onComplete, refresh);

                }
            }
            , groupings: {}
            , menu: {
                subviews:           false
                , actions:          false
            }
            , submenu:              false
            , objectType:           'event_type'
            , selectedView:         'list'
            , selectedMobileView:   'list'
            , shouldKeepSegment:    true
            , subviews: {
                list: {
                    backlog:        true
                    , range:        'all_time'
                }
            }
            , tags:                 false
            , where:                whereObj
        };


        function viewEventType (ui, eventType, draw) {


            var actionDef = _.findWhere(
				Actions
				, {
					name: eventType.type
				}
			);

            var actionColor = 'grey';
            var actionIcon = 'bolt';

            if ( (actionDef || {}).color)
                actionColor = actionDef.color;

            if ((actionDef || {}).icon)
                actionIcon = actionDef.icon;

			ui.makeNode(
				't'
				, 'div'
				, {
					css: 'ui header mini',
					style: 'display:inline-block',
					text: '<div style=" display:inline-block; vertical-align:middle;"><i class="' + actionColor + ' ' + actionIcon +' icon"></i> ' + eventType.name + '</div>'
				}
			) ;
			
		}
		
		sb.notify({
			type:'show-collection',
			data: collectionsSetup
		});
		
	}
	
	// 'event-type' field
	
	return {
		
		init: function () {
			
			sb.listen ({
				'register-action': 		this.register
				, 'show-actions-menu': 	this.showMenu
				
				// action configuration
				, 'view-actions': 		this.viewActionEditor
				
				// public
				, 'run-action': 		this.run
				, 'get-action-options-form': this.getOptionsForm
				, 'view-single-action': this.viewSingleAction
			});
			
		}
		
		, register: function (data) {
			
			Actions.push({
				
				// identifying info
				name: 		data.name
				, category: 	data.category
				
				// display info
				, title: 	data.title
				, icon: 	data.icon
				, color: 	data.color || 'black'
				, hiddenFromEvents: data.hiddenFromEvents || false
				
				// run config
				, action: 		data.action
				, options: 		data.options
				, parseOptions: data.parseOptions || false
				, toggle: 		data.toggle || false
				, view: 		data.view || false
				, modal:		data.modal || false
				, parseLog: 	data.parseLog || false
				
			});
			
		}
		
		//!TODO: refactor this into a nice public endpoint to run actions/events
		, run: 		function (data) {
			
			if (sb.instanceId === undefined) {
				
				data.run(
					data
				);
				
			}
			
		}
		
		, getOptionsForm: function (data) {
			
			function onComplete (notification, response) {
				
				data.ui.empty();
				data.ui.patch();
				data.ui.hide();
				
				if (typeof data.onComplete === 'function') {
					
					data.onComplete(
						response
					);
					
				}
				
				logEvent(data.obj, data.event_type, response);
				
			}
			
			function runStep (data, i) {
				
				viewActionForm(
					data.ui
					, data.notifications[i].event_type
					, data.notifications[i]
					, data.obj
					, function (response) {
						
						data.ui.empty();
						data.ui.patch();
						data.ui.hide();
						
						logEvent(data.obj, data.notifications[i].event_type, response);
						
						if (
							!_.isEmpty(data.notifications[i + 1])
						) {
							
							data.ui.show();
							runStep(
								data
								, i + 1
							);
							
						}
						
					}
				);
				
			}
			
			function viewActionForm (ui, event_type, notification, obj, onComplete) {
				
				function getObjectData (obj, onComplete) {

					if (obj.object_bp_type.startsWith('#')) {
						sb.notify({
							type: 'get-entity'
							, data: {
								id: obj.id
								, object_bp_type: obj.object_bp_type
								, onResponse: function (obj) {
									
									onComplete(obj);

								}
							}
						});

					} else {
						
						onComplete(obj);

					}

				}

				var typeDef = _.findWhere(
					Actions
					, {
						name: event_type.type
					}
				);
				
				// Option form
				ui.makeNode('opts', 'div', {});

				// Run button
				ui.makeNode('runBtn', 'div', {
					text: 'Run Action',
					css: 'ui large green button pull-right',
				}).listeners.push(
					function (selector) {

						$(selector).on(
							'click'
							, function () {

								// Turn on loading on button
								ui.runBtn.loading();

								sb.data.db.obj.runSteps(
									{
										[typeDef.name]: {
											obj: 				obj.id
											, options: 			ui.opts.process()
											, notification: 	notification.id
										}
									}
									, obj.id
									, function(response) {

										if (response) {

											if (
												notification
												&& notification.id
											) {
												
												sb.data.db.obj.update(
													'notification'
													, {
														id: 			notification.id
														, is_viewed: 	1
													}
													, function (response) {
														
														onComplete(response);
														
													}
												);
												
											} else {
											
												onComplete(response);
												
											}

										} else {

											// Turn off loading on button
											ui.runBtn.loading(false);

											// Show alert
											sb.notify({
												type: 'display-alert',
												data: {
													header: 'Error Running Action!',
													body: 'Please try again or reach out to support.',
													color: 'red'
												}
											});

										}
										
									}
								);
								
							}
						);
						
					}
				);

				// Cancel button
				ui.makeNode('cancelBtn', 'div', {
					css: 'ui large light-grey basic button pull-right',
					text: 'Cancel'
				}).notify('click', {
					type: 'run-action',
					data: {
						run:function() {

							// Hide modal
							ui.hide();

						}.bind({})
					}
				}, sb.moduleId);

				ui.makeNode('clear', 'div', {
					style: 'clear:both'
				});
				
				ui.patch();
				
				getObjectData(obj, function (obj) {

					getOptionsForm(
						ui.opts
						, typeDef
						, event_type
						, {
							object: 			obj
							, notification: 	notification
						}
						, {
							eventType: false
							, isDeferred:  true
						}
					);

				});
				
			}
			
			// For stepping through multiple actions.
			if (
				data.notifications
				&& Array.isArray(data.notifications)
			) {
				
				// Track current step.
				runStep(data, 0);
				
			} else {
				
				viewActionForm(
					data.ui
					, data.event_type
					, data.notification
					, data.obj
					, onComplete
				);
				
			}
			
		}
		
		, showMenu: function (data) {

			if (appConfig) {
				if ( appConfig.hasOwnProperty('state') ) {
					if (appConfig.state) {
						if ( appConfig.state.hasOwnProperty('project') ) {
							if (appConfig.state.project) {
								if ( sb.permissions.isGroupManager(+sb.data.cookie.userId, appConfig.state.project) ) {
									showMenu(data.ui, data.actions, data.obj);
									return;
								}
							}
						}
						if ( appConfig.state.hasOwnProperty('team') ) {
							if (appConfig.state.team) {
								if ( sb.permissions.isGroupManager(+sb.data.cookie.userId, appConfig.state.team) ) {
									showMenu(data.ui, data.actions, data.obj);
									return;
								}
							}
						}
						if ( appConfig.state.hasOwnProperty('pageObject') ) {
							if (appConfig.state.pageObject) {

								if ( 
									appConfig.state.pageObject.hasOwnProperty('parent') 
									&& !_.isEmpty(appConfig.state.pageObject.parent)
									&& typeof appConfig.state.pageObject.parent.object_bp_type === 'string'
									&& appConfig.state.pageObject.parent.object_bp_type === 'groups'
								) {
									if (appConfig.state.pageObject.parent) {
										if ( sb.permissions.isGroupManager(+sb.data.cookie.userId, appConfig.state.pageObject.parent) ) {
											showMenu(data.ui, data.actions, data.obj);
											return;
										}
									}
								}
								if ( sb.permissions.isGroupManager(+sb.data.cookie.userId, appConfig.state.pageObject) ) {
									showMenu(data.ui, data.actions, data.obj);
									return;
								}
								if (appConfig.state.pageObject.object_bp_type === 'contracts') {
									showMenu(data.ui, data.actions, data.obj);
									return;
								}
							}
						}
					}
				}		
			}
			
			if ( sb.permissions.isGroupManager(+sb.data.cookie.userId, data.obj) ) {
				showMenu(data.ui, data.actions, data.obj);
				return;
			}
			
		}
		
		, viewActionEditor: function (data) {

            viewActionEditor (
				data.ui
				, data.state
				, data.draw
			) ;
			
		}

		, viewSingleAction: function (data) {

			viewActionForm (
				data.ui
				, data.state
				, data.action
				, data.onComplete
				, data.refresh
			);

		}
		
	};
	
});
