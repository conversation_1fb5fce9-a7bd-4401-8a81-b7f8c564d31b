Factory.register('reports', function(sb){

	var reportsCacheList = [];
	
	function reportItemsListUI(options, i){
		
		if(i > 0) {
			this.makeNode('lb', 'lineBreak', {
				spaces: 3
			});
		} else {
			this.makeNode('lb', 'lineBreak', {
				spaces: 1
			});
		}

		if(options.hasOwnProperty('groupHeader')){
			this.makeNode('col', 'div', {css:'ui grid container'});
			this.col.makeNode('cont', 'div', {css: 'ui sixteen wide column'});
			//this.col.cont.makeNode('sp', 'lineBreak', {spaces: 1});		
			this.col.cont.makeNode('header', 'div', {tag:'h1', css: 'ui header', text:options.groupHeader});
		}
		
		var container = this.col.cont.makeNode('cards', 'div', {css: 'ui two cards'});

		
		if(options.hasOwnProperty('reportList')){
			_.each(options.reportList, function(report, i){

				var segmentHeader = '';				
				var segment = this.makeNode('col-'+i, 'div', {
					tag:'a', 
					css: 'card',
					href:sb.data.url.createPageURL(
						'custom', 
						{
							id:report.id,
							name:report.header
						}
					)
				});
				
				segment.makeNode('content', 'div', {css: 'content'});
				segment.content.makeNode('icon', 'div', {tag:'i', css: report.icon + ' icon left floated'});
				segment.content.makeNode('header', 'div', {
					css: 'header',
					text:report.header
				});
				segment.content.makeNode('desc', 'div', {css: 'meta'})
					.makeNode('category', 'div', {css: 'category', text: report.subheader});

				
			}, container);
		}
		
	}
	
	function reportsDashboardUI(dom, state, draw){

		var reportListOptions = [];
		var dashboardHeader = 'Reports';
		
		if(state.hasOwnProperty('reportOptions')){
			dashboardHeader = state.reportOptions.dashboardHeader;
			reportListOptions = state.reportOptions.dashboardReports;
		}

		dom.empty();
		
		//dom.makeNode('header', 'div', {tag:'h1', css: 'ui dividing header', text:dashboardHeader});
		
		if(_.isEmpty(reportListOptions)){
			
			dom.makeNode('noReport', 'div', {css: '', text:'No Reports Available'});
			
		}else{
			_.each(reportListOptions, function (type, i) {
				
				reportItemsListUI.call(
					dom.makeNode('t-'+ i, 'div', {})
					, type
					, i
				);
				
			});			
		}

		
		dom.patch();
		
	}
	
	return {
		
		init:function(){
			
			sb.listen({
				'reports-run':this.run,
				'show-report-dashboard':this.showReportDashboard,
				'register-report':this.register
			});

			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						moduleId:sb.moduleId,
						id:'reports',
						title:'Reports',
						icon:'<i class="fa fa-bar-chart"></i>',
						views:[
							{
								id:'reportsTool',
								type:'hqTool',
								name:'Reports',
								tip:'Run reports on your data',
								icon:{
									type:'chart bar',
									color:'purple'
								},
								default:true,
								settings:[],
								mainViews:[
									{
										dom:function(dom, state, draw){
										
											state.reportOptions = {
												dashboardHeader:'HQ Reports',
												dashboardReports:[
													{
														groupHeader:'Accounting Reports',
														reportList:_.where(reportsCacheList, {type:'Accounting'})
													}
													, {
														groupHeader:'Workload Reports',
														reportList:_.where(reportsCacheList, {type:'Workload'})
													}
												]
											};
																						
											reportsDashboardUI(dom, state, draw);
										}
									}
								]
							}
							, {
								id:'reportsTool',
								type:'teamTool',
								name:'Reports',
								tip:'Run reports on your data',
								icon:{
									type:'chart bar',
									color:'purple'
								},
								default:true,
								settings:[],
								mainViews:[
									{
										dom:function(dom, state, draw){
										
											state.reportOptions = {
												dashboardHeader:'Reports',
												dashboardReports:[
													{
														groupHeader:'Accounting Reports',
														reportList:_.where(reportsCacheList, {type:'Accounting'})
													}
													, {
														groupHeader:'Workload Reports',
														reportList:_.where(reportsCacheList, {type:'Workload'})
													}
												]
											};
																						
											reportsDashboardUI(dom, state, draw);
										}
									}
								]
							}
						]
					}
				}
			});
			
		},
		
		register:function(data){

			if(_.isArray(data)){
				
				reportsCacheList = reportsCacheList.concat(data);
				
			}else{
				reportsCacheList.push(data);	
			}	

		},
		
		run:function(data){ data.run(); },
		
		showReportDashboard:function(setup){
			
			if(setup.hasOwnProperty('reportOptions')){
				reportsDashboardUI(setup.reportOptions);
			}
			
		}
	}
	
});