Factory.registerComponent('crud', function(sb){
	
	var columns = null,
		domObj = null,
		objects = [],
		objectType = '',
		buttons = {
			create: true,
			view: true,
			edit: true,
			erase: true
		},
		formFields = {},
		fields = [],
		childObjs = 1,
		setup = {};
				
	function buildTable(domObj, objects, objectType, columns, buttons, blueprint){
		
		domObj.empty();
		domObj.makeNode('lineBreak', 'lineBreak', {spaces:1});
		
		if(typeof buttons.create === 'function' && buttons.create(objects) === true || typeof buttons.create !== 'function' && buttons.create){

			var createNotification = {
					type:'crud-create-'+ objectType +'-object',
					data:{}
				};
			
			if(typeof buttons.create == 'object'){
				
				createNotification = buttons.create;
				
			}
			
			domObj.makeNode('create', 'button', {css:'pull-right btn-success', text:'Create New'}).notify('click', createNotification, sb.moduleId);
			
		}
		
		var cols = {},
			colDefs = [],
			hiddenCols = {},
			colCount = 0;
		
		_.each(columns, function(colValue, colKey){

			if(typeof colValue == 'object'){

				cols[colKey] = colValue.colName;
				
			}else{
				
				cols[colKey] = colValue;
				
			}
			
			if(typeof colValue == 'object' && colValue.hasOwnProperty('rowSetup')){
				
				colDefs.push({
					visible: true,
					title: colValue.colName
				});
				
			}else{
				
				colDefs.push({
					visible: true,
					title: colValue
				});
				
			}
			
			colCount++;
						
		});
		
		_.each(blueprint, function(fieldObj, propName){
			
			if( !cols.hasOwnProperty(propName) && typeof fieldObj.name !== 'undefined'){
				
				hiddenCols[propName] = fieldObj.name;
				
				colDefs.push({
					visible: false,
					title: fieldObj.name
			});
			
			colCount++;
				
			}
			
		});
		
		_.extend(cols, hiddenCols);
		
		var testCols = {};

		_.each(cols, function(colValue, colKey){
			
			if(typeof colValue === 'object'){
				testCols[colKey] = colValue.colName;
			}else{
				testCols[colKey] = colValue;
			}
			
		});
		
		if(typeof objects == 'object'){
			
			var tableSetup = {}
			
			if(setup.noDataTable){
				
				domObj.makeNode(
					'objectTable-'+ objectType,
					'table',
					{
						css: 'table-hover table-condensed',
						columns: cols
					}
				);
				
			}else{
				
				domObj.makeNode(
					'objectTable-'+ objectType,
					'table',
					{
						css: 'table-hover table-condensed',
						columns: cols,
						dataTable: {
							buttons: [
								'colvis',
								'csvHtml5',
						        'excel',
						        'print'
						    ],
						    colReorder: true,
						    columns: colDefs
						    //autoFill: true
						}
					}
				);
				
			}
			
			_.each(objects, function(object){
				
				var printColumns = [];
				_.each(cols, function(columnName, columnKey){
					
					switch(columnKey){
						
						case 'fullName':
						
							printColumns.push(object.fname +' '+ object.lname);
						
							break;
							
						case 'phone':
						
							printColumns.push( sb.dom.formatPhone( object[columnKey] ) );
						
							break;	
							
						case 'date_created':
						
							printColumns.push( moment(object.date_created).format('MM/DD/YYYY') );
													
							break;	
						
						default:
							
							// if cell is made by callback, set up only empty cell
							if(typeof columns[columnKey] === 'object' && columns[columnKey].hasOwnProperty('rowSetup') && columns[columnKey].rowSetup.length > 1){
								
								printColumns.push('');
							
							// if a string is returned by callback, put that string in cell
							}else if(typeof columns[columnKey] === 'object' && columns[columnKey].hasOwnProperty('rowSetup')){
								
								printColumns.push(columns[columnKey].rowSetup(object));
								
							}else if(typeof object[columnKey] == 'object' && object[columnKey] != null){
								
								if(Array.isArray(object[columnKey])){
									
									var count = 0,
										string = '';
	
									_.each(object[columnKey], function(obj){
										
										if(obj){
											if(count == 0){
												
												string += obj[columnName.propName];
												
											}else{
	
												string += ',<br />'+ obj[columnName.propName];
												
											}
											
											count++;
	
										}
									
									});
									
									printColumns.push(string);
									
								}else{
									
									printColumns.push(object[columnKey][columnName.propName]);
									
								}
								
							}else{
								
								printColumns.push(object[columnKey]);
								
							}
						
					}
					
				});
								
				domObj['objectTable-'+ objectType].makeRow(
					object.id,
					printColumns
				);
				
				// if cell is made by callback, apply callback to dom node
				_.each(cols, function(columnName, columnKey){
					
					if(typeof columns[columnKey] === 'object' && columns[columnKey].hasOwnProperty('rowSetup') && columns[columnKey].rowSetup.length > 1){
						
						columns[columnKey].rowSetup(object, domObj['objectTable-'+ objectType].body[object.id][columnKey]);
						
					}
					
				});
				
				domObj['objectTable-'+ objectType].body[object.id].btns.makeNode('buttons', 'buttonGroup', {});
				
				if(buttons.view){
					
					var viewNotification = {
						type:'crud-view-'+ objectType +'-object',
						data:object
					};
					
					if(typeof buttons.view == 'object'){
						
						var viewNotification = {
								type:buttons.view.type,
								data: {
									object:object
								}
							};
							
						viewNotification.data = Object.assign({}, buttons.view.data);
						viewNotification.data.object = object;
											
					}
					
				domObj['objectTable-'+ objectType].body[object.id].btns.buttons.makeNode('view', 'button', {text:'View', css:'btn-success'}).notify('click', viewNotification, sb.moduleId);
	
				}
				
				if(typeof buttons.edit === 'function' && buttons.edit(object) === true || typeof buttons.edit !== 'function' && buttons.edit){
					
					var editNotification = {
							type:'crud-edit-object',
							data:object
						};
											
					
					if(typeof buttons.edit == 'object'){
						
						var editNotification = {
								type:buttons.edit.type,
								data: {
									object:object
								}
							};
							
						editNotification.data = Object.assign({}, buttons.edit.data);
						editNotification.data.object = object;
											
					}
													
					domObj['objectTable-'+ objectType].body[object.id].btns.buttons.makeNode('edit', 'button', {text:'Edit', css:'btn-warning'}).notify('click', editNotification, sb.moduleId);
					
				}
															
				if(typeof buttons.erase === 'function' && buttons.erase(object) === true || typeof buttons.erase && buttons.erase){
					
					var eraseNotification = {
							type:'crud-delete-'+ objectType +'-object',
							data:object
						};
					
					if(typeof buttons.erase == 'object'){
						
						var eraseNotification = {
								type:buttons.erase.type,
								data: {
									object:object
								}
							};
							
						eraseNotification.data = Object.assign({}, buttons.erase.data);
						eraseNotification.data.object = object;
											
					}
					
					domObj['objectTable-'+ objectType].body[object.id].btns.buttons.makeNode('delete', 'button', {text:'Delete', css:'btn-danger'}).notify('click', eraseNotification, sb.moduleId);
					
				}
				
			});
			
		}
		
		if(typeof objects == 'function'){

			domObj.makeNode(
				'objectTable-'+ objectType,
				'table',
				{
					css: 'table-hover table-condensed',
					columns: cols,
					dataTable: {
						ajax: function(data, callback, settings){
							
							objects(function(objs){

								var ret = {
									data: objs
								};

console.log(columns, ret);								
											
								callback(ret);
								
							});
						},
						columns: columns,
						buttons: [
							'colvis',
							'csvHtml5',
					        'excel',
					        'print'
					    ],
					    colReorder: true,
					    //columns: colDefs
					    //autoFill: true
					}
				}
			);
			
		}
		
		domObj.makeNode('modals', 'container', {});
		
		return domObj;

	}	
				
	return{
		
		init: function(){

			sb.listen({
				'display-crud-table':this.displayTable
			});
			
		},
				
		destroy: function(){
			
			sb.listen({
				'display-crud-table':this.displayTable
			});
			
			columns = null;
			domObj = null;
			objects = [];
			objectType = '';
			buttons = {
				create: true,
				view: true,
				edit: true,
				erase: true
			};
			
		},
		
		create: function(){
			
			sb.obj.create(domObj.modals, objectType, formFields, childObjs);
																					
			domObj = buildTable(domObj, objects, objectType, columns, buttons);
						
			domObj.build();
			
		},
		
		deleteObj: function(obj){
			
			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: 'This cannot be undone.'
			}, function(resp){
				
				if(resp){
										
					swal.disableButtons();
					
					sb.data.db.obj.erase(objectType, obj.id, function(response){
						
						if(response){
							
							objects = _.reject(objects, function(singleObj){
							
								return singleObj.id == obj.id;
								
							});	
							
							domObj = buildTable(domObj, objects, objectType, columns, buttons);
				
							domObj.build();						
					
							sb.dom.alerts.alert('Success!', '', 'success');
							
						}
					
					});
																
				}
				
			});
			
		},
		
		displayTable: function(setupObj){
			
			var listeners = {
				'crud-edit-object':this.edit,
				'display-crud-table':this.displayTable,
				'staff-object-created':this.update,
				'update-crud-table': this.updateTable
			};
			
			listeners['crud-create-'+setupObj.objectType+'-object'] = this.create;
			listeners[setupObj.objectType+'-object-updated'] = this.update;
			listeners[setupObj.objectType+'-object-created'] = this.update;
			listeners['crud-delete-'+setupObj.objectType+'-object'] = this.deleteObj;
			
			sb.listen(listeners);
			
			domObj = sb.dom.make(setupObj.domObj.selector);
			objects = setupObj.objects;
			objectType = setupObj.objectType;
			columns = setupObj.columns;
			
			if(setupObj.hasOwnProperty('buttons')){
				
				if(setupObj.buttons.hasOwnProperty('create')){
					
					buttons.create = setupObj.buttons.create;
					
				}
					
				if(setupObj.buttons.hasOwnProperty('view')){
					
					buttons.view = setupObj.buttons.view;
					
				}
					
				if(setupObj.buttons.hasOwnProperty('edit')){
					
					buttons.edit = setupObj.buttons.edit;
					
				}
					
				if(setupObj.buttons.hasOwnProperty('erase')){
					
					buttons.erase = setupObj.buttons.erase;
					
				}
				
				if(setupObj.hasOwnProperty('formFields')){
					
					formFields = setupObj.formFields;
					
				}
				
				if(setupObj.hasOwnProperty('fields')){
					
					fields = setupObj.fields;
					
				}
								
			}
			if(setupObj.hasOwnProperty('childObjs')){
				childObjs = setupObj.childObjs;
			}
			
			setup = setupObj;
						
			columns.btns = 'Buttons';
			
			sb.data.db.obj.getBlueprint(objectType, function(blueprint){
				
				domObj = buildTable(domObj, objects, objectType, columns, buttons, blueprint, setupObj);

				domObj.build();
				
			});
			
		},
		
		edit: function(obj){
				
			sb.data.db.obj.getById(objectType, obj.id, function(originalObject){

				sb.obj.edit(domObj.modals, objectType, originalObject, formFields, childObjs);
								
				domObj = buildTable(domObj, objects, objectType, columns, buttons);
			
				domObj.build();
				
			});			
						
		},
				
		update: function(updatedObj){
			
			delete updatedObj.moduleId;
			
			objects = _.reject(objects, function(object){ return object.id === updatedObj.id;});
			objects.push(updatedObj);
			
			domObj = buildTable(domObj, objects, objectType, columns, buttons);
			
			domObj.build();
			
		},
		
		updateTable: function(data){
			
			// if objects are in a list, replace old object list with this
			if( data.hasOwnProperty('objects') && Array.isArray(data.objects) ){
				
				objects = data.objects;
				
			// if data is just a single object, push it onto current object list
			}else if( typeof data === 'object' ){
			
				objects.push(data);
			
			}
			
			domObj = buildTable(domObj, objects, objectType, columns, buttons);
			
			domObj.build();
			
		}
								
	}
	
});