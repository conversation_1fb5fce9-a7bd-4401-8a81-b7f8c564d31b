Factory.register("account-management", function (sb) {
  var currentUser = {};
  var currentInstance = {};
  var onMobile = false;

  var isPortal = false;

  var adminURL = "https://bento.infinityhospitality.net/api/";
  //var adminURL = 'https://api.voltz.software/_repos/_production/notify/pagoda/_coredev/_getAdmin.php?do=';
  //var adminURL = 'https://test.voltz.software/api/_getAdmin.php?do=';
  //var adminURL = 'http://localhost:8080/api/_getAdmin.php?do=';

  function createToken(ui, templateObj, onCreate) {
    ui.makeNode("title", "div", {
      text: "Create a new token",
      css: "ui huge header",
    });

    ui.makeNode("form", "form", {
      label: {
        name: "label",
        label: "Label - What is this token for?",
        type: "text",
      },
      expires: {
        name: "expires",
        label: "Will this token expire?",
        type: "select",
        value: "no",
        options: [
          {
            name: "Token does not expire",
            value: "no",
          },
          {
            name: "Token expires after 30 days of no usage",
            value: "yes",
          },
        ],
      },
    });

    ui.makeNode("break", "div", { text: "<br />" });

    ui.makeNode("save", "div", {
      text: "Create Token",
      css: "ui green button",
    }).notify(
      "click",
      {
        type: "account-management-run",
        data: {
          run: function (ui) {
            ui.save.loading();
            ui.cancel.loading();

            sb.data.db.controller("getIPAddress", {}, function (ip) {
              var tokenLabel = ui.form.process().fields.label.value;
              var expires = 0;

              if (ui.form.process().fields.expires.value == "no") {
                expires = 1;
              }

              var newToken = {
                staffId: +sb.data.cookie.get("uid"),
                ip_address: ip,
                fingerprint: tokenLabel,
                platform: "user_generated",
                does_not_expire: expires,
              };

              sb.data.db.controller(
                "generateToken&pagodaAPIKey=" + appConfig.instance,
                newToken,
                function (createdToken) {
                  onCreate(createdToken);
                }
              );
            });
          }.bind({}, ui),
        },
      },
      sb.moduleId
    );

    ui.makeNode("cancel", "div", {
      text: "Cancel",
      css: "ui button",
    });

    ui.patch();
  }

  function singleTokenView(dom, state, draw, obj) {
    sb.data.db.obj.getById("cookies", state.id, function (obj) {
      dom.makeNode("title", "div", { text: "Token", css: "ui huge header" });

      dom
        .makeNode("tokseg", "div", {
          css: "ui center aligned secondary segment",
        })
        .makeNode("tok", "div", {
          text: obj.token,
          style: "font-weight:bold;",
        });

      dom.makeNode("date", "div", {
        tag: "p",
        style: "text-align:center;",
        text:
          "Created " +
          moment(obj.date_created).local().format("M/D/YYYY h:mm a") +
          ", expires " +
          moment(obj.date_created).add(30, "days").local().fromNow() +
          ".",
      });

      draw(dom);
    });
  }

  function View(ui, state) {
    function Head(ui) {
      ui.makeNode("pageTitle", "div", {
        text: "Account",
        tag: "h1",
        css: "ui header",
      });
    }

    function Body(ui) {
      function profileImage(ui) {
        var profileImg = currentUser.profile_image;

        if (
          profileImg !== null &&
          profileImg !== undefined &&
          profileImg.loc != "//"
        ) {
          profileImg =
            '<img class="ui centered small circular image" src="' +
            sb.data.files.getURL(currentUser.profile_image) +
            '">';
        } else {
          profileImg = '<i class="massive user circle icon"></i>';
        }

        ui.makeNode("img", "div", {
          text: profileImg,
          css: "text-center",
        });
      }

      function profileInfo(ui) {
        var subHeader = "";

        if (currentUser.type_name) subHeader = currentUser.type_name;

        ui.makeNode("userName", "div", {
          text:
            "<h1>" +
            currentUser.fname +
            " " +
            currentUser.lname +
            ' <small class="text-muted">' +
            currentUser.nick_name +
            '</small><div class="ui sub header">' +
            subHeader +
            "</div></h1>",
          css: "ui header",
        });

        ui.makeNode("label", "div", {
          css: "ui " + currentUser.color + " label tag",
          text: currentUser.fname + " " + currentUser.lname,
          style: "margin: 0 !important",
        });

        ui.makeNode("btncont", "div", { css: "", style: "padding: 14px 0px;" });
        ui.btncont
          .makeNode("resetPassword", "div", {
            css: "ui teal mini basic button",
            text: '<i class="key icon"></i> Reset Password ',
          })
          .notify(
            "click",
            {
              type: "reset-user-password",
              data: {
                object: currentUser,
              },
            },
            sb.moduleId
          );
      }

      function accountTabs(ui) {
        var menuOptions = {
          profile: {
            name: "profile",
            title: "Profile",
            dom: userInfo,
          },
          security: {
            name: "security",
            title: "Security",
            dom: security,
          },
          billing: {
            name: "billing",
            title: "Billing",
            dom: accountBilling,
          },
          documentTags: {
            name: "documentTags",
            title: "Document Tags",
            dom: documentTags,
          },
          userSettings: {
            name: "userSettings",
            title: "User Settings",
            dom: userSettings,
          },
        };
        var active = "";

        function userInfo(ui) {
          var formObj1 = {
            fname: {
              type: "text",
              name: "fname",
              label: "First Name (required)",
              value: currentUser.fname,
            },
            lname: {
              type: "text",
              name: "lname",
              label: "Last Name (required)",
              value: currentUser.lname,
            },
            nick_name: {
              type: "text",
              name: "nick_name",
              label: "Nickname",
              value: currentUser.nick_name,
            },
            profile_image: {
              type: "file-upload",
              name: "profile_image",
              label: `Profile Image`,
              value: "",
            },
          };
          var formObj2 = {
            email: {
              type: "text",
              name: "email",
              label: "Email (required)",
              value: currentUser.email,
            },
            phone: {
              type: "text",
              name: "phone",
              label: "Phone",
              value: currentUser.phone,
            },
            street: {
              type: "text",
              name: "street",
              label: "Street",
              value: currentUser.street,
            },
            city: {
              type: "text",
              name: "city",
              label: "City",
              value: currentUser.city,
            },
          };
          var formObj3 = {
            state: {
              type: "text",
              name: "state",
              label: "State",
              value: currentUser.state,
            },
            zip: {
              type: "text",
              name: "zip",
              label: "Zip / Postal Code",
              value: currentUser.zip,
            },
            country: {
              type: "text",
              name: "country",
              label: "Country",
              value: currentUser.country,
            },
            id: {
              type: "hidden",
              name: "id",
              value: currentUser.id,
            },
          };

          function process(ui, forms, beforeUpdate, afterUpdate) {
            var formData1 = forms[0].process().fields;
            var formData2 = forms[1].process().fields;
            var formData3 = forms[2].process().fields;
            var data = _.extend(formData1, formData2, formData3);
            var userObj = {};

            function validate(data, callback) {
              if (data.fname.value === "" || data.lname.value === "") {
                Message(
                  ui.messageWrap,
                  "Please provide a first and last name",
                  "red",
                  true
                );

                ui.messageWrap.patch();

                callback(false);
              } else if (data.email.value === "") {
                Message(
                  ui.messageWrap,
                  "Please provide a valid email address",
                  "red",
                  true
                );

                ui.messageWrap.patch();

                callback(false);
              } else {
                callback(true);
              }
            }

            validate(data, function (process) {
              if (process) {
                beforeUpdate(ui);

                userObj = {
                  id: data.id.value,
                  fname: data.fname.value,
                  lname: data.lname.value,
                  nick_name: data.nick_name.value,
                  profile_image: data.profile_image.value,
                  email: data.email.value,
                  phone: data.phone.value,
                  street: data.street.value,
                  city: data.city.value,
                  state: data.state.value,
                  zip: data.zip.value,
                  country: data.country.value,
                  color: currentUser.color,
                };

                sb.data.db.obj.update("users", userObj, function (updatedObj) {
                  sb.data.db.controller(
                    "getUserAccountsNew",
                    { email: userObj.email },
                    function (allAccounts) {
                      var relatedIds = [];

                      relatedIds.push(updatedObj.related_object);

                      if (allAccounts.length > 0) {
                        _.each(allAccounts, function (acct) {
                          relatedIds.push(acct.related_object);

                          _.each(userObj, function (value, key) {
                            if (key != "instance" && key != "id") {
                              acct[key] = value;
                            }
                          });
                        });

                        relatedIds = _.uniq(relatedIds);

                        sb.data.db.obj.update(
                          "users",
                          allAccounts,
                          function (done) {
                            sb.data.db.obj.getById(
                              "staff",
                              relatedIds,
                              function (relatedObjs) {
                                _.each(relatedObjs, function (obj) {
                                  _.each(userObj, function (value, key) {
                                    if (key != "instance" && key != "id") {
                                      obj[key] = value;
                                    }
                                  });
                                });

                                sb.data.db.obj.update(
                                  "staff",
                                  relatedObjs,
                                  function (don) {
                                    afterUpdate(done);
                                  }
                                );
                              }
                            );
                          }
                        );
                      } else {
                        sb.data.db.obj.getById(
                          "staff",
                          updatedObj.id,
                          function (relatedObj) {
                            _.each(relatedObj, function (value, key) {
                              if (key != "instance" && key != "id") {
                                relatedObj[key] = value;
                              }
                            });

                            sb.data.db.obj.update(
                              "staff",
                              relatedObj,
                              function (done) {
                                afterUpdate(relatedObj);
                              }
                            );
                          }
                        );
                      }
                    }
                  );
                });
              } else {
                return;
              }
            });
          }

          ui.makeNode("head", "div", {});
          ui.makeNode("lb_1", "lineBreak", { spaces: 3 });
          ui.makeNode("messageWrap", "div", {});
          ui.makeNode("body", "div", {});

          ui.head
            .makeNode("update", "div", {
              text: "Update",
              css: "ui right floated green button",
            })
            .notify(
              "click",
              {
                type: [sb.moduleId + "-run"],
                data: {
                  run: function (data) {
                    process(
                      ui,
                      [
                        ui.body.grid.col1.form,
                        ui.body.grid.col2.form,
                        ui.body.grid.col3.form,
                      ],
                      function (ui) {
                        ui.empty();

                        Loader(ui, "Updating user data...");

                        ui.patch();
                      },
                      function (updatedUser) {
                        if (updatedUser) {
                          location.reload();
                        }
                      }
                    );
                  },
                },
              },
              sb.moduleId
            );

          ui.body.makeNode("grid", "div", {
            css: "ui stackable equal width grid",
          });

          ui.body.grid.makeNode("col1", "div", {
            css: "column",
          });
          ui.body.grid.makeNode("col2", "div", {
            css: "column",
          });
          ui.body.grid.makeNode("col3", "div", {
            css: "column",
          });

          ui.body.grid.col1.makeNode("form", "form", formObj1);
          ui.body.grid.col2.makeNode("form", "form", formObj2);
          ui.body.grid.col3.makeNode("form", "form", formObj3);

          // color selection
          var currentColor = currentUser.color || "grey";

          ui.body.grid.col3.makeNode("colorLabel", "div", {
            text: "Tag Color",
            css: "ui tiny header",
          });

          var colorSelector = ui.body.grid.col3.makeNode(
            "colorSelector",
            "div",
            {
              css: "ui dropdown",
              listener: {
                type: "dropdown",
                maxSelections: 1,
                onChange: function (value, text, choice) {
                  currentUser.color = choice[0].textContent;
                },
              },
            }
          );

          colorSelector.makeNode("text", "div", {
            tag: "span",
            css: "text",
            text:
              '<i class="ui ' +
              currentColor +
              ' empty circular label"></i>' +
              currentColor,
          });

          colorSelector.makeNode("menu", "div", { css: "menu" });

          var colors = sb.dom.colors;

          _.each(colors, function (color, i) {
            colorSelector.menu.makeNode("option-" + i, "div", {
              css: "item",
              text:
                '<div class="ui ' +
                color +
                ' empty circular label" value="' +
                color +
                '"></div>' +
                color,
            });
          });
        }

        function accountBilling(ui) {
          function subscription(ui) {
            var onTrial = true;
            var width = "five";
            var subscriptionVars = {
              plan: "Bento Trial",
              trial_start_date: moment(currentInstance.trial_start_date).format(
                "MM/DD/YY"
              ),
              trial_end_date: moment(currentInstance.trial_end_date).format(
                "MM/DD/YY"
              ),
              billing_cycle: "Monthly",
              last_billing_date: moment(
                currentInstance.last_billing_date
              ).format("MM/DD/YY"),
              next_billing_date: moment(currentInstance.last_billing_date)
                .add(30, "days")
                .format("MM/DD/YY"),
            };

            if (moment(currentInstance.trial_end_date).isBefore()) {
              // Not on trial

              onTrial = false;

              subscriptionVars.plan = "$99/month";
            }

            if (!onMobile) {
              width = "five";

              ui.makeNode("title", "div", {
                text: "Subscription",
                css: "ui header",
                tag: "h4",
              });

              ui.makeNode("divider", "div", {
                css: "ui divider",
              });

              ui.makeNode("lb_1", "lineBreak", { spaces: 1 });
            } else {
              width = "nine";
            }

            ui.makeNode("wrapper", "div", {});

            ui.wrapper.makeNode("grid", "div", {
              css: "ui grid",
            });

            ui.wrapper.grid.makeNode("col1", "div", {
              css: width + " wide column",
            });
            ui.wrapper.grid.makeNode("col2", "div", {
              css: "seven wide column",
            });

            ui.wrapper.grid.col1.makeNode("planType", "div", {
              text: "Subscription plan",
              css: "text-muted",
              style: "text-align: right;",
              tag: "h4",
            });

            ui.wrapper.grid.col2.makeNode("planType", "div", {
              text: subscriptionVars.plan,
              tag: "h4",
            });

            if (onTrial) {
              ui.wrapper.grid.col1.makeNode("trialStart", "div", {
                text: "Trial Start Date",
                css: "text-muted",
                style: "text-align: right;",
                tag: "h4",
              });

              ui.wrapper.grid.col2.makeNode("trialStartVal", "div", {
                text: subscriptionVars.trial_start_date,
                tag: "h4",
              });

              ui.wrapper.grid.col1.makeNode("trialEnd", "div", {
                text: "Trial End Date",
                css: "text-muted",
                style: "text-align: right;",
                tag: "h4",
              });

              ui.wrapper.grid.col2.makeNode("trialEndVal", "div", {
                text: subscriptionVars.trial_end_date,
                tag: "h4",
              });
            } else {
              ui.wrapper.grid.col1.makeNode("billingCycle", "div", {
                text: "Billing Cycle",
                css: "text-muted",
                style: "text-align: right;",
                tag: "h4",
              });

              ui.wrapper.grid.col2.makeNode("billingCycleVal", "div", {
                text: subscriptionVars.billing_cycle,
                tag: "h4",
              });

              ui.wrapper.grid.col1.makeNode("lastBilling", "div", {
                text: "Last Billing Date",
                css: "text-muted",
                style: "text-align: right;",
                tag: "h4",
              });

              ui.wrapper.grid.col2.makeNode("lastBillingVal", "div", {
                text: subscriptionVars.last_billing_date,
                tag: "h4",
              });

              ui.wrapper.grid.col1.makeNode("nextBilling", "div", {
                text: "Next Billing Date",
                css: "text-muted",
                style: "text-align: right;",
                tag: "h4",
              });

              ui.wrapper.grid.col2.makeNode("nextBillingVal", "div", {
                text: subscriptionVars.next_billing_date,
                tag: "h4",
              });
            }

            if (!onMobile) {
              ui.makeNode("lb_1", "lineBreak", { spaces: 1 });

              paymentMethods(ui);
            }
          }

          function paymentMethods(ui) {
            if (!onMobile) {
              ui.makeNode("title1", "div", {
                text: "Payment Methods",
                css: "ui header",
                tag: "h4",
              });

              ui.makeNode("divider1", "div", {
                css: "ui divider",
              });
            }

            ui.makeNode("methods", "div", {});

            sb.notify({
              type: "show-paymentMethod-button",
              data: {
                domObj: ui.methods,
                objectId: appConfig.id,
                objectType: "instances",
                collapse: "none",
                compact: true,
                title: false,
                buildUI: false,
                flexible: true,
              },
            });
          }

          function paymentHistory(ui) {
            function displayPayment(ui, transaction) {
              var mobileVars = {
                created: '<span class="text-muted">Created:</span> ',
                description: '<span class="text-muted">Description:</span> ',
              };

              if (!onMobile) {
                mobileVars.created = "";
                mobileVars.description = "";
              }

              ui.makeNode("paymentWrap" + transaction.id, "div", {});

              ui["paymentWrap" + transaction.id].makeNode("label", "div", {
                text: '<i class="credit card outline icon"></i> Payment',
                css: "ui green sub header",
              });

              ui["paymentWrap" + transaction.id].makeNode("grid", "div", {
                css: "ui stackable grid",
              });

              ui["paymentWrap" + transaction.id].grid.makeNode("col1", "div", {
                css: "four wide column",
              });
              ui["paymentWrap" + transaction.id].grid.makeNode("col2", "div", {
                css: "seven wide column",
              });

              ui["paymentWrap" + transaction.id].grid.col1.makeNode(
                "amount",
                "div",
                {
                  text:
                    "$" + (parseInt(transaction.amount) / 100).formatMoney(),
                }
              );
              ui["paymentWrap" + transaction.id].grid.col2.makeNode(
                "auth",
                "div",
                {
                  text: transaction.id,
                }
              );

              // Created

              if (!onMobile) {
                ui["paymentWrap" + transaction.id].grid.col1.makeNode(
                  "created",
                  "div",
                  {
                    text: "Created",
                    css: "text-muted",
                  }
                );
              }

              ui["paymentWrap" + transaction.id].grid.col2.makeNode(
                "date",
                "div",
                {
                  text:
                    mobileVars.created +
                    moment.unix(transaction.created).format("MM/DD/YY"),
                }
              );

              // Description

              if (!onMobile) {
                ui["paymentWrap" + transaction.id].grid.col1.makeNode(
                  "desc",
                  "div",
                  {
                    text: "Description",
                    css: "text-muted",
                  }
                );
              }

              ui["paymentWrap" + transaction.id].grid.col2.makeNode(
                "descText",
                "div",
                {
                  text:
                    mobileVars.description +
                    "Monthly charge for Bento subscription",
                }
              );
            }

            if (!onMobile) {
              ui.makeNode("title", "div", {
                text: "Payment History",
                css: "ui header",
                tag: "h5",
              });

              ui.makeNode("divider", "div", {
                css: "ui divider",
              });

              ui.makeNode("lb_1", "lineBreak", { spaces: 1 });
            }

            ui.makeNode("wrapper", "div", {});

            Loader(ui.wrapper, "Fetching payment history...");

            sb.data.db.controller(
              "getStripeCustomer",
              { instanceId: appConfig.id },
              function (customer) {
                sb.data.db.controller(
                  "getStripeCustomerTransactions",
                  { stripeId: customer.id },
                  function (transactions) {
                    ui.wrapper.empty();

                    if (_.isEmpty(transactions.data)) {
                      ui.wrapper.makeNode("noHistory", "div", {
                        text: "There is no payment history for this account",
                        css: "ui header text-center",
                        tag: "h4",
                      });
                    } else {
                      _.each(transactions.data, function (transaction, index) {
                        displayPayment(ui.wrapper, transaction);

                        if (index !== transactions.data.length - 1) {
                          ui.wrapper["paymentWrap" + transaction.id].makeNode(
                            "divider",
                            "div",
                            {
                              css: "ui divider",
                            }
                          );
                        }
                      });
                    }

                    ui.wrapper.patch();
                  }
                );
              },
              adminURL
            );
          }

          function mobileBilling(ui) {
            ui.makeNode("subscription", "container", {
              collapse: "open",
              title: "Subscription",
            });
            ui.makeNode("methods", "container", {
              collapse: "closed",
              title: "Payment Methods",
            });
            ui.makeNode("history", "container", {
              collapse: "closed",
              title: "Payment History",
            });

            subscription(ui.subscription);

            paymentMethods(ui.methods);

            paymentHistory(ui.history);
          }

          function desktopBilling(ui) {
            function billingMenu(ui, displayAreaUI) {
              var menuOptions = {
                subscription: {
                  name: "subscription",
                  title: "Subscription & Payment Methods",
                  dom: subscription,
                },
                paymentHistory: {
                  name: "paymentHistory",
                  title: "Payment History",
                  dom: paymentHistory,
                },
              };
              var active = "";

              ui.makeNode("menu", "div", {
                css: "ui secondary vertical menu",
              });

              _.each(menuOptions, function (item, name) {
                if (name === "subscription") {
                  active = "active";
                } else {
                  active = "";
                }

                ui.menu
                  .makeNode("item-" + name, "div", {
                    tag: "a",
                    css: "item billingMenuItem " + active,
                    text: item.title,
                  })
                  .notify(
                    "click",
                    {
                      type: [sb.moduleId + "-run"],
                      data: {
                        run: function (data) {
                          $(".billingMenuItem").removeClass("active");
                          $(data.selector).addClass("active");

                          displayAreaUI.empty();

                          item.dom(displayAreaUI);

                          displayAreaUI.patch();
                        },
                        selector: ui.menu["item-" + name].selector,
                      },
                    },
                    sb.moduleId
                  );
              });
            }

            ui.makeNode("grid", "div", {
              css: "ui grid",
            });

            ui.grid.makeNode("col1", "div", {
              css: "twelve wide column",
            });
            ui.grid.makeNode("col2", "div", {
              css: "four wide column",
            });

            billingMenu(ui.grid.col2, ui.grid.col1);

            subscription(ui.grid.col1);
          }

          Loader(ui, "Fetching billing info...");

          sb.data.db.obj.getWhere(
            "instances",
            { id: appConfig.id, childObjs: 1 },
            function (instance) {
              currentInstance = instance[0];

              ui.empty();

              if (onMobile) {
                mobileBilling(ui);
              } else {
                desktopBilling(ui);
              }

              ui.patch();
            },
            1
          );
        }

        function security(ui) {
          ui.empty();

          //ui.makeNode('break', 'div', {text:'<br />'});

          ui.makeNode("title", "div", {
            css: "ui large header",
            text: "Your authorized devices.",
          });

          ui.makeNode("seg", "div", { css: "" });
          ui.seg.makeNode("seg", "div", { css: "ui loading basic segment" });
          ui.seg.seg.makeNode("break", "div", { text: "<br />" });

          ui.patch();

          ui.seg.empty();

          sb.data.db.obj.getWhere(
            "cookies",
            {
              uid: +sb.data.cookie.get("uid"),
            },
            function (cookies) {
              sb.notify({
                type: "show-collection",
                data: {
                  domObj: ui.seg,
                  actions: {
                    view: true,
                    create: function (ui, templateObj, onCreate) {
                      createToken(ui, templateObj, onCreate);
                    },
                  },
                  fields: {
                    /*
									fingerprint:{
										title:'Device ID',
										type:'text'
									},
*/
                    platform: {
                      title: "Type",
                      type: "text",
                      view: function (dom, obj) {
                        var string = obj.platform;
                        if (obj.platform == "user_generated") {
                          string = "User Generated - " + obj.fingerprint;
                        }

                        dom.makeNode("text", "div", {
                          text: string,
                        });
                      },
                    },
                    token: {
                      title: "Token",
                      type: "text",
                      view: function (dom, obj) {
                        dom.makeNode("text", "div", {
                          text: obj.token.substring(0, 45) + "...",
                          tooltip: obj.token,
                        });
                      },
                    },
                    /*
									ip_address:{
										title:'IP Address',
										type:'text',
										view:function(dom, obj){
											
											dom.makeNode('text', 'div', {text:obj.ip_address});
											
										}
									},
*/
                    last_updated: {
                      title: "Last Used",
                      view: function (dom, obj) {
                        var lu = new Date(obj.last_updated);
                        dom.makeNode("text", "div", {
                          text: moment.utc(lu).fromNow(),
                        });
                      },
                    },
                    date_created: {
                      title: "Created On",
                      view: function (dom, obj) {
                        dom.makeNode("text", "div", {
                          text: moment(obj.date_created)
                            .local()
                            .format("M/D/YYYY h:mm a"),
                        });
                      },
                    },
                  },
                  groupings: false,
                  objectType: "cookies",
                  singleView: {
                    view: function (ui, obj, draw) {
                      contactView(obj, ui, {}, draw);
                    },
                    select: 3,
                  },
                  pageLength: 50,
                  where: {
                    uid: +sb.data.cookie.get("uid"),
                  },
                },
              });
            }
          );
        }

        function documentTags(ui) {
          ui.makeNode("link", "div", {}).makeNode("label", "div", {
            tag: "h5",
            text: "Link",
            css: "ui header",
          });

          sb.notify({
            type: "view-field",
            data: {
              type: "url",
              property: "doc_link",
              obj: currentUser,
              options: {
                edit: true,
                editing: true,
                commitChanges: true,
              },
              ui: ui.link.makeNode("val", "div", {}),
            },
          });

          ui.makeNode("br", "div", {
            text: "<br />",
            css: "ui clearing divider",
          });

          ui.makeNode("signature", "div", {}); /*
;.makeNode(
						'label'
						, 'div'
						, {
							tag: 'h5'
							, text: 'Signature'
							, css: 'ui header'
						}
					);
*/

          sb.notify({
            type: "view-field",
            data: {
              type: "detail",
              property: "doc_signature",
              obj: currentUser,
              options: {
                editing: true,
                useMedium: true,
              },
              ui: ui.signature.makeNode("val", "div", {}),
            },
          });

          ui.signature.val.patch();
        }

        function userSettings(ui) {
          sb.notify(
            {
              type: "user-settings-view",
              data: {
                ui: ui,
                user: currentUser,
              },
            },
            sb.moduleId
          );
        }

        if (currentUser.type !== "owner") {
          delete menuOptions.billing;
        }

        ui.makeNode("menu", "div", {
          css: "ui stacking secondary menu",
        });
        ui.makeNode("tabseg", "div", {
          css: "ui active tab basic segment",
        });

        _.each(menuOptions, function (item, name) {
          if (name === "profile") {
            active = "active";
          } else {
            active = "";
          }

          ui.menu
            .makeNode("item" + name, "div", {
              tag: "a",
              text: item.title,
              css: "item tabItem " + active,
            })
            .notify(
              "click",
              {
                type: [sb.moduleId + "-run"],
                data: {
                  run: function (data) {
                    $(".tabItem").removeClass("active");
                    $(data.selector).addClass("active");

                    ui.tabseg.empty();

                    item.dom(ui.tabseg);

                    ui.tabseg.patch();
                  },
                  selector: ui.menu["item" + name].selector,
                },
              },
              sb.moduleId
            );
        });

        userInfo(ui.tabseg);
      }

      Loader(ui, "Fetching user data...");

      sb.data.db.obj.getById(
        "users",
        sb.data.cookie.get("uid"),
        function (user) {
          currentUser = user;

          ui.empty();

          ui.makeNode("grid", "div", {
            css: "ui grid",
          });
          ui.makeNode("lb_1", "lineBreak", { spaces: 2 });
          ui.makeNode("tabsWrapper", "div", {});

          ui.grid.makeNode("col1", "div", {
            css: "ui four wide column",
          });
          ui.grid.makeNode("col2", "div", {
            css: "ui twelve wide column",
          });

          profileImage(ui.grid.col1);

          profileInfo(ui.grid.col2);

          if (!isPortal) accountTabs(ui.tabsWrapper);

          ui.patch();
        },
        1
      );
    }

    ui.empty();

    ui.makeNode("wrapper", "div", {});

    ui.wrapper.makeNode("head", "div", {});
    ui.wrapper.makeNode("lb_1", "lineBreak", { spaces: 2 });
    ui.wrapper.makeNode("body", "div", {});
    ui.wrapper.makeNode("lb_2", "lineBreak", { spaces: 1 });

    Head(ui.wrapper.head);

    Body(ui.wrapper.body);

    ui.patch();
  }

  function Loader(ui, text) {
    ui.makeNode("seg", "div", {
      css: "ui basic segment",
    });
    ui.seg.makeNode("dimmer", "div", { css: "ui active inverted dimmer" });
    ui.seg.dimmer.makeNode("loader_text", "div", {
      text: text,
      css: "ui active centered inline text loader",
    });
  }

  function Message(ui, text, color, isVisible) {
    var display = "hidden";
    var messageColor = "";

    if (isVisible === true) {
      display = "visible";
    } else if (isVisible === false) {
      display = "hidden";
    }

    if (color !== undefined || color !== "") {
      messageColor = color;
    }

    ui.makeNode("message", "div", {
      css: "ui " + messageColor + " " + display + " message",
      text: "<p>" + text + "</p>",
    });

    ui.makeNode("lb_1", "lineBreak", { spaces: 1 });
  }

  return {
    init: function () {
      if ($(window).width() <= 768) {
        onMobile = true;
      }

      sb.listen({
        [sb.moduleId + "-run"]: this.run,
        "account-management-start": this.start,
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            id: "account-management",
            title: "Account Management",
            icon: {
              type: "key",
            },
            views: [
              {
                id: "myaccount",
                type: "nav-item",
                name: "My Account",
                title: "Account",
                icon: {
                  type: "key",
                },
                default: true,
                display: false,
                dom: function (ui, state, draw) {
                  if (appConfig.is_portal) isPortal = true;

                  View(ui, state);
                },
              },
              {
                id: "cookies-obj",
                type: "object-view",
                name: "My Tokens",
                title: "Token",
                icon: {
                  type: "key",
                },
                dom: function (ui, state, draw) {
                  singleTokenView(ui, state, draw);
                },
                header: {
                  name: false,
                  subHeader: false,
                  menu: {
                    archive: true,
                    templates: false,
                  },
                  select: {},
                  canEdit: false,
                },
              },
            ],
          },
        },
      });
    },

    run: function (data) {
      if (data.hasOwnProperty("run")) {
        data.run(data);
      } else {
        throw 'You did not include a "run" method on the data object.';
      }
    },

    start: function (data) {
      var ui = data.domObj;
      var state = data.state;

      View(ui, state);
    },
  };
});
