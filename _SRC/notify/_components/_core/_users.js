Factory.register("userComponent", function (sb) {
  // PRIVATE SCOPE
  var accountInfoSetup = {},
    comps = {},
    domObj = {},
    home = true,
    userObj = {},
    userId = 0,
    ui = {},
    home_ui = {},
    calendar_ui = {},
    documents_ui = {},
    payroll_ui = {},
    userInfo_ui = {};

  function changeLoginState(state, userId, callback) {
    sb.data.db.obj.update(
      "users",
      { id: userId, enabled: state },
      function (updated) {
        if (updated) {
          callback(updated);
        }
      }
    );
  }

  function createUserAccount(userObj, callback) {
    var newAccount = true;

    sb.data.db.controller(
      "getUserAccountsNew",
      { email: userObj.email, enabled: 1 },
      function (users) {
        if (users.length > 0) {
          newAccount = false;
        }

        if (newAccount === true) {
          sb.data.db.createNewPassword(function (passwordObj) {
            userObj.password = passwordObj.pwdHash;
            userObj.enabled = 1;

            sb.data.db.obj.update("users", userObj, function (newUser) {
              var args = {
                to: newUser.email,
                from: appConfig.instance.emailFrom,
                subject: "New User Account",
                mergevars: {
                  TITLE: "New user account for " + appConfig.instance,
                  BODY:
                    "You can login to your account by clicking the login button below.<br /><br />Your login email: " +
                    newUser.email +
                    "<br />Your password: " +
                    passwordObj.pwd,
                  BUTTON: "Log In",
                  BUTTON_LINK: "https://bento.infinityhospitality.net/app/",
                },
                emailtags: ["password reset email"],
              };

              sb.comm.sendEmail(args, function (response) {
                callback({
                  user: newUser,
                  newAccount: newAccount,
                });
              });
            });
          });
        } else {
          sb.data.db.obj.update(
            "users",
            { id: userObj.id, enabled: 1 },
            function (newUser) {
              callback({
                user: newUser,
                newAccount: newAccount,
              });
            }
          );
        }
      }
    );
  }

  function resetUserPassword(userObj, sendEmail, callback, setPassword) {
    sb.data.db.controller(
      "getUserAccountsNew",
      { email: userObj.email },
      function (users) {
        sb.data.db.createNewPassword(function (passwordObj) {
          _.each(users, function (user) {
            user.password = passwordObj.pwdHash;
            user.enabled = 1;
          });

          sb.data.db.obj.update("users", users, function (updatedUsers) {
            var instanceName = appConfig.instance;
            if (!_.isEmpty(appConfig.systemName)) {
              instanceName = appConfig.systemName;
            }
            if (!_.isEmpty(setPassword)) {
              var body =
                "Your " +
                instanceName +
                " password has been reset. If you believe this was an error, please reset your password and reach out to our support team.";
            } else {
              var body =
                "Your " +
                instanceName +
                " password has been reset. Here are your new login credentials.<br /><br />Your login email: " +
                updatedUsers[0].email +
                "<br />Your password: " +
                passwordObj.pwd;
            }

            var args = {
              to: updatedUsers[0].email,
              from: appConfig.instance.emailFrom,
              subject: "Account Password Reset",
              mergevars: {
                TITLE: "Password Reset",
                BODY: body,
                BUTTON: "Log In",
                BUTTON_LINK: "https://bento.infinityhospitality.net/app/",
              },
              emailtags: ["password reset email"],
            };

            if (sendEmail) {
              sb.comm.sendEmail(args, function (response) {
                callback(updatedUsers[0]);
              });
            } else {
              callback(updatedUsers[0]);
            }
          });
        }, setPassword);
      }
    );
  }

  function viewSingleAccount(userObj, domObj, state, draw) {
    var accountLogin = {
        styles:
          "display:flex!important; justify-content:space-around!important; flex:auto!important; margin-bottom:0!important; padding: 0.7em 1.2em!important; border:none!important; box-shadow:none!important;",
      },
      accountEnabledMess = "ui negative center aligned message",
      accountEnabledText =
        '<i class="fa fa-lock fa-lg"></i>  Account Login:  Disabled',
      author = "",
      col1Width = 10,
      col2Width = 6,
      headerIcon = '<i class="fa fa-lock"></i>',
      headerColor = "pda-panel-red",
      queryObj = {},
      relatedObj = {},
      setup = {
        singleView: false,
        settingsView: {},
        home: false,
        profileImageInfo: {
          text: "Upload an Image",
          css: "ui bottom attached basic button",
        },
        userObj: userObj,
      };

    function uploadProfileImage(userObj, modal, state, draw) {
      var formSetup = {
          profile_image: {
            type: "file-upload",
            name: "profile_image",
            label: `Profile Image:`,
            value: "",
          },
          id: {
            type: "hidden",
            name: "id",
            value: userObj.id,
          },
        },
        profileImageName = " – No Image On File";

      modal.empty();
      modal.makeNode("wrapper", "div", { css: "ui stackable grid container" });
      modal.makeNode("contentSpacer", "lineBreak", { spaces: 1 });

      modal.wrapper.makeNode("header", "div", { css: "sixteen wide column" });
      modal.wrapper.header.makeNode("btnGroup", "buttonGroup", {
        css: "small ui right floated buttons",
      });
      modal.wrapper.header.makeNode("title", "headerText", {
        css: "ui dividing header",
        text: '<i class="fa fa-upload"></i> Upload A Profile Image',
      });

      modal.wrapper.makeNode("body", "div", { css: "sixteen wide column" });
      modal.wrapper.body.makeNode("col", "div", { css: "ui items" });
      modal.wrapper.body.col.makeNode("item", "div", { css: "item" });
      modal.wrapper.body.col.item.makeNode("image", "div", {
        css: "ui medium rounded image",
      });
      modal.wrapper.body.col.item.makeNode("content", "div", {
        css: "ui stackable grid container content",
      });

      if (
        userObj.profile_image.loc !== "" &&
        userObj.profile_image.loc !== "//"
      ) {
        profileImageName = ' – "' + userObj.profile_image.file_name + '"';

        modal.wrapper.body.col.item.image.makeNode("profileImage", "image", {
          url: sb.data.files.getURL(setup.userObj.profile_image),
        });
      } else {
        modal.wrapper.body.col.item.image.makeNode("profileImage", "image", {
          url: "https://placeimg.com/250/250/tech",
        });
      }

      if (userObj.hasOwnProperty("fname") && userObj.hasOwnProperty("lname")) {
        author = userObj.fname + " " + userObj.lname;

        modal.wrapper.body.col.item.content.makeNode("header", "div", {
          css: "header",
          text: '<i class="fa fa-file"></i> ' + author + profileImageName,
        });

        if (userObj.hasOwnProperty("nick_name")) {
          if (
            userObj.nick_name != "" &&
            userObj.nick_name != undefined &&
            userObj.nick_name != null
          ) {
            modal.wrapper.body.col.item.content.makeNode("meta", "div", {
              css: "meta",
              text: "<span>" + userObj.nick_name + "</span>",
            });
          }
        }
      } else {
        modal.wrapper.body.col.item.content.makeNode("header", "div", {
          css: "header",
          text: '<i class="fa fa-file"></i> ' + profileImageName,
        });
      }

      modal.wrapper.body.col.item.content.makeNode("formSpacer", "lineBreak", {
        spaces: 1,
      });
      modal.wrapper.body.col.item.content.makeNode("formCont", "container", {
        css: "ui stackable grid",
      });
      modal.wrapper.body.col.item.content.formCont.makeNode("col", "div", {
        css: "sixteen wide column",
      });
      modal.wrapper.body.col.item.content.formCont.col.makeNode(
        "form",
        "form",
        formSetup
      );

      //modal.wrapper.body.col.item.content.makeNode('btns', 'div', {css:'ui two bottom attached buttons'});
      modal.wrapper.header.btnGroup
        .makeNode("back", "button", {
          css: "ui red button",
          text: '<i class="fa fa-arrow-left"></i> Back',
        })
        .notify("click", {
          type: "userComponent-run",
          data: {
            run: function () {
              this.hide();
            }.bind(modal),
          },
        });
      modal.wrapper.header.btnGroup
        .makeNode("button", "button", {
          css: "ui blue button",
          text: '<i class="fa fa-check"></i> Save',
        })
        .notify(
          "click",
          {
            type: "userComponent-run",
            data: {
              run: function () {
                var formInfo =
                  this.wrapper.body.col.item.content.formCont.col.form.process()
                    .fields;
                var updateObj = {
                  id: formInfo.id.value,
                  profile_image: formInfo.profile_image.value,
                };

                function saveProfileImage(obj, domObj) {
                  var saveUi = this;

                  sb.data.db.obj.update("users", obj, function (response) {
                    sb.data.db.obj.getWhere(
                      "users",
                      { id: response.id, childObjs: 3 },
                      function (updatedUser) {
                        saveUi.hide();

                        viewSingleAccount(
                          updatedUser[0],
                          domObj,
                          {},
                          function (domCache) {}
                        );
                      }
                    );
                  });
                }

                this.wrapper.header.btnGroup.makeNode("button", "button", {
                  css: "ui primary button",
                  text: '<i class="fa fa-circle-o-notch fa-spin"></i> Saving',
                });
                this.wrapper.header.btnGroup.patch();

                saveProfileImage.call(modal, updateObj, domObj);
              }.bind(modal, userObj, domObj),
            },
          },
          sb.moduleId
        );

      modal.patch();
      modal.show();
    } ///

    if (setup.hasOwnProperty("singleView")) {
      (col1Width = 10), (col2Width = 6);
    }

    if (!setup.hasOwnProperty("home")) {
      (home = false), (setup.home = false);
    }

    if (`${userObj.id}` === sb.data.cookie.userId) {
      setup.home = true;
    }

    if (setup.userObj.hasOwnProperty("related")) {
      (relatedObj = setup.userObj.related),
        (queryObj = { related_object: relatedObj, childObjs: 1 });

      setup.userObj.related_object = relatedObj;
    } else {
      if (typeof setup.userObj.related_object == "object") {
        relatedObj = setup.userObj.id;
      } else {
        relatedObj = setup.userObj.related_object;
      }

      queryObj = { id: relatedObj, childObjs: 3 };
    }

    // Getting Users by Query Object
    sb.data.db.obj.getById(
      "users",
      queryObj.id,
      function (user) {
        domObj.makeNode("modal", "modal", { css: "ui modal" });

        if (user) {
          setup.userObj = user;

          if (
            setup.userObj.hasOwnProperty("fname") &&
            setup.userObj.hasOwnProperty("lname")
          ) {
            author = setup.userObj.fname + " " + setup.userObj.lname;
          }

          if (setup.userObj.enabled == 1) {
            accountEnabledText =
              '<i class="fa fa-unlock fa-lg"></i>  Account Login:  Enabled';
            accountEnabledMess = "ui positive center aligned message";
            headerIcon = '<i class="fa fa-unlock"></i>';
            headerColor = "pda-panel-blue";
          }

          // Account View - Ui Card
          domObj.makeNode("headerSpacer", "lineBreak", { spaces: 1 });
          domObj.makeNode("colOne", "div", { css: "six wide column" });
          domObj.makeNode("colTwo", "div", { css: "column" });
          domObj.makeNode("contentSpacer", "lineBreak", { spaces: 1 });

          domObj.colOne.makeNode("wrapper", "div", {
            css: "ui stackable cards",
          });
          domObj.colTwo.makeNode("wrapper", "div", {
            css: "ui grid container",
          });

          domObj.colOne.wrapper.makeNode("profileCard", "div", {
            css: "ui centered card",
          });

          if (
            setup.userObj.profile_image.loc !== "" &&
            setup.userObj.profile_image.loc !== "//"
          ) {
            setup.profileImageInfo.text = "Change Image";
            setup.profileImageInfo.css = "ui gray button";

            domObj.colOne.wrapper.profileCard.makeNode(
              "profileImage",
              "image",
              {
                css: "ui image",
                url: sb.data.files.getURL(setup.userObj.profile_image),
              }
            );
          } else {
            domObj.colOne.wrapper.profileCard.makeNode(
              "profileImage",
              "image",
              { css: "ui image", url: "https://placeimg.com/250/250/tech" }
            );
          }

          domObj.colOne.wrapper.profileCard.makeNode("content", "div", {
            css: "content",
          });
          domObj.colOne.wrapper.profileCard.content.makeNode("header", "div", {
            css: "header",
            text: `${author}`,
          });
          domObj.colOne.wrapper.profileCard.content.makeNode("meta", "div", {
            css: "meta",
          });

          if (!_.isEmpty(user.profiles)) {
            _.each(user.profiles, function (profile) {
              domObj.colOne.wrapper.profileCard.content.meta.makeNode(
                "column-" + profile.id,
                "div",
                {
                  css: "item",
                  text:
                    '<i class="fa fa-user"></i> <small class="date">' +
                    profile.name +
                    "</small>",
                }
              );
            });
          } else {
            domObj.colOne.wrapper.profileCard.content.meta.makeNode(
              "column",
              "div",
              {
                css: "item",
                text:
                  '<i class="fa fa-building"></i> <small class="date">' +
                  appConfig.systemName +
                  "</small>",
              }
            );
          }

          domObj.colOne.wrapper.makeNode("enabled", "div", {
            css: "ui centered card",
          });
          domObj.colOne.wrapper.enabled.makeNode("content", "div", {
            css: "content",
          });
          domObj.colOne.wrapper.enabled.content.makeNode(
            "accountLogin",
            "div",
            {
              css: accountEnabledMess,
              text: "<b>" + accountEnabledText + "</b>",
              style: "border:none!important; box-shadow:none!important;",
            }
          );
          domObj.colOne.wrapper.enabled.makeNode("btns", "div", {
            css: "ui bottom attached mini fluid stackable buttons",
          });

          if (setup.home) {
            domObj.colOne.wrapper.profileCard
              .makeNode("changeImage", "div", {
                css: setup.profileImageInfo.css,
                text:
                  '<i class="fa fa-camera"></i> ' + setup.profileImageInfo.text,
              })
              .notify(
                "click",
                {
                  type: "userComponent-run",
                  data: {
                    run: function () {
                      uploadProfileImage(user, this);
                    }.bind(domObj.modal, user),
                  },
                },
                sb.moduleId
              );

            domObj.colOne.wrapper.enabled.btns
              .makeNode("resetPassword", "button", {
                css: "ui teal button",
                text: '<i class="fa fa-key"></i> Reset Password',
              })
              .notify(
                "click",
                {
                  type: "reset-user-password",
                  data: {
                    object: user,
                  },
                },
                sb.moduleId
              );
          }

          // Disable and Enable Options
          if (!setup.home) {
            if (user.enabled == 1) {
              domObj.colOne.wrapper.enabled.btns
                .makeNode("disable", "button", {
                  css: "ui red button",
                  text: '<i class="fa fa-lock"></i> Click to Disable User\'s Login',
                })
                .notify(
                  "click",
                  {
                    type: "disable-user-login",
                    data: {
                      objectId: user.id,
                    },
                  },
                  sb.moduleId
                );

              domObj.colOne.wrapper.enabled.btns
                .makeNode("resetPassword", "button", {
                  css: "ui teal button",
                  text: '<i class="fa fa-key"></i> Reset Password',
                })
                .notify(
                  "click",
                  {
                    type: "reset-user-password",
                    data: {
                      object: user,
                    },
                  },
                  sb.moduleId
                );
            } else {
              domObj.colOne.wrapper.enabled.btns
                .makeNode("enable", "button", {
                  css: "ui green button",
                  text: '<i class="fa fa-unlock"></i> Click to Enable User\'s Login',
                })
                .notify(
                  "click",
                  {
                    type: "enable-user-login",
                    data: {
                      objectId: user.id,
                    },
                  },
                  sb.moduleId
                );
            }
          } else {
            if (appConfig.user) {
              if (+sb.data.cookie.userId === +appConfig.user.id) {
                domObj.colOne.wrapper.enabled.btns
                  .makeNode("changeUserViews", "button", {
                    css: "ui orange button",
                    text: '<i class="fa fa-pencil"></i> Change Permissions',
                  })
                  .notify(
                    "click",
                    {
                      type: "userComponent-run",
                      data: {
                        run: function () {
                          editAccount(this, user, domObj.modal);
                        }.bind(domObj, user, domObj.modal),
                      },
                    },
                    sb.moduleId
                  );
              } else {
                domObj.colOne.wrapper.enabled.btns.makeNode(
                  "changeUserViews",
                  "button",
                  {
                    css: 'ui gray button" data-content="Restricted: Admin Access Only"',
                    text: '<i class="fa fa-lock"></i> Change Permissions',
                  }
                );
              }
            }
          }

          domObj.colTwo.wrapper.makeNode("segment", "div", {
            css: "ui column segment",
          });

          // Profile & Other Account Details
          domObj.colTwo.wrapper.segment.makeNode("extraContent", "div", {
            css: "ui stackable equal width grid container",
          });
          domObj.colTwo.wrapper.segment.extraContent
            .makeNode("profiles", "div", { css: "column" })
            .makeNode("list", "div", { css: "ui list" });
          domObj.colTwo.wrapper.segment.extraContent
            .makeNode("otherAccounts", "div", { css: "column" })
            .makeNode("list", "div", { css: "ui list" });
          domObj.colTwo.wrapper.segment.extraContent.profiles.list.makeNode(
            "header",
            "headerText",
            { css: "ui dividing header", text: "Profiles:", size: "small" }
          );
          domObj.colTwo.wrapper.segment.extraContent.otherAccounts.list.makeNode(
            "header",
            "headerText",
            {
              css: "ui dividing header",
              text: "Other Accounts:",
              size: "small",
            }
          );

          if (user.related_object) {
            if (user.related_object.service) {
              _.each(user.related_object.service, function (service) {
                domObj.colTwo.wrapper.segment.extraContent.profiles.list.makeNode(
                  "prof-" + service.permission.id,
                  "div",
                  {
                    css: "item",
                    text:
                      '<i class="fa fa-user"></i> ' + service.permission.name,
                  }
                );
              });
            }
          }

          _.each(user.profiles, function (prof) {
            domObj.colTwo.wrapper.segment.extraContent.profiles.list.makeNode(
              "prof-" + prof.id,
              "div",
              { css: "item", text: '<i class="fa fa-user"></i> ' + prof.name }
            );
          });

          domObj.colTwo.wrapper.segment.extraContent.otherAccounts.list.makeNode(
            "loading",
            "text",
            { text: sb.dom.loadingGIF }
          );

          sb.data.db.controller(
            "getUserAccountsNew",
            { email: user.email },
            function (allAccounts) {
              _.each(
                _.reject(allAccounts, function (obj) {
                  return obj.instance == user.instance;
                }),
                function (userObj) {
                  domObj.colTwo.wrapper.segment.extraContent.otherAccounts.list.makeNode(
                    "user-" + userObj.id,
                    "div",
                    {
                      css: "item",
                      text:
                        '<i class="fa fa-user"></i> ' +
                        userObj.instance.toUpperCase(),
                    }
                  );
                }
              );

              if (allAccounts.length <= 1) {
                delete domObj.colTwo.wrapper.segment.extraContent.otherAccounts;

                domObj.colTwo.wrapper.segment.extraContent.patch();
              } else {
                domObj.colTwo.wrapper.segment.extraContent.otherAccounts.patch();
              }
            }
          );
        } else {
          if (!setup.home) {
            domObj.makeNode("buttonGroup", "buttonGroup", {});
            domObj.makeNode("modals", "container", {});

            domObj.buttonGroup.makeNode("create", "button", {
              css: "btn-success pda-btn-large",
              text: '<i class="fa fa-unlock"></i> Create A Login Account',
            });
          }
        }

        domObj.patch();

        if (domObj.create) {
          $(domObj.create.selector).on("click", function () {
            enableUserLogin_throttled({
              domObj: domObj.modals,
              userObj: setup.userObj,
            });
          });
        }
      },
      3
    );
  }

  function accountFieldView(userObj, domObj) {
    domObj.empty();

    function pageRefresh() {
      setTimeout(function () {
        $.ajax({
          url: window.location.href,
          headers: {
            Pragma: "no-cache",
            Expires: -1,
            "Cache-Control": "no-cache",
          },
        }).done(function () {
          location.reload(true);
        });
      }, 900);

      return;
    }

    if (userObj.enabled) {
      domObj
        .makeNode("disable", "div", {
          css: "ui mini gray basic button",
          text: '<i class="lock icon"></i> Disable User\'s Login',
        })
        .notify(
          "click",
          {
            type: "disable-user-login",
            data: {
              objectId: userObj.id,
              onUpdate: pageRefresh,
            },
          },
          sb.moduleId
        );

      domObj
        .makeNode("resetPassword", "div", {
          tag: "a",
          text: '<i class="key icon"></i> Reset Password',
          style:
            "cursor:pointer;font-size:.78571429rem;display:inline-block; padding:8.5px 16.5px;",
        })
        .notify(
          "click",
          {
            type: "reset-user-password",
            data: {
              object: userObj,
            },
          },
          sb.moduleId
        );
    } else {
      domObj.makeNode("create", "div", {
        css: "ui mini basic button",
        text: '<i class="unlock alternate icon"></i> Enable User\'s Login',
      });
    }

    domObj.patch();

    if (domObj.create) {
      $(domObj.create.selector).on("click", function () {
        enableUserLogin_throttled({
          userObj: userObj,
          onUpdate: pageRefresh,
        });
      });
    }
  }

  // Need to update the related_objects with the new info
  function updateUserObject(userObj, callback) {
    sb.data.db.obj.update("users", userObj, function (updatedObj) {
      sb.data.db.controller(
        "getUserAccountsNew",
        { email: userObj.email },
        function (allAccounts) {
          var relatedIds = [];

          relatedIds.push(updatedObj.related_object);

          if (allAccounts.length > 0) {
            _.each(allAccounts, function (acct) {
              relatedIds.push(acct.related_object);

              _.each(userObj, function (value, key) {
                if (key != "instance" && key != "id") {
                  acct[key] = value;
                }
              });
            });

            relatedIds = _.uniq(relatedIds);

            sb.data.db.obj.update("users", allAccounts, function (done) {
              sb.data.db.obj.getById(
                "staff",
                relatedIds,
                function (relatedObjs) {
                  _.each(relatedObjs, function (obj) {
                    _.each(userObj, function (value, key) {
                      if (key != "instance" && key != "id") {
                        obj[key] = value;
                      }
                    });
                  });

                  sb.data.db.obj.update("staff", relatedObjs, function (don) {
                    callback(updatedObj);
                  });
                }
              );
            });
          } else {
            sb.data.db.obj.getById(
              "staff",
              updatedObj.id,
              function (relatedObj) {
                _.each(relatedObj, function (value, key) {
                  if (key != "instance" && key != "id") {
                    relatedObj[key] = value;
                  }
                });

                sb.data.db.obj.update("staff", relatedObj, function (done) {
                  callback(updatedObj);
                });
              }
            );
          }
        }
      );
    });
  }

  // Need to adjust form fields
  function editAccount(domObj, userObj, modal, data) {
    var editDom = modal;

    var formSetup = {
      fname: {
        type: "text",
        name: "fname",
        label: "First Name:",
        value: userObj.fname,
      },
      lname: {
        type: "text",
        name: "lname",
        label: "Last Name:",
        value: userObj.lname,
      },
      nick_name: {
        type: "text",
        name: "nick_name",
        label: "Nick Name:",
        value: userObj.nick_name,
      },
      profile_image: {
        type: "file-upload",
        name: "profile_image",
        label: `Profile Image:`,
        value: "",
      },
      email: {
        type: "text",
        name: "email",
        label: "Email:",
        value: userObj.email,
      },
      phone: {
        type: "text",
        name: "phone",
        label: "Phone:",
        value: userObj.phone,
      },
      street: {
        type: "text",
        name: "street",
        label: "Street",
        value: userObj.street,
      },
      city: {
        type: "text",
        name: "city",
        label: "City",
        value: userObj.city,
      },
      state: {
        type: "text",
        name: "state",
        label: "State",
        value: userObj.state,
      },
      zip: {
        type: "text",
        name: "zip",
        label: "Zip",
        value: userObj.zip,
      },
      country: {
        type: "text",
        name: "country",
        label: "Country",
        value: userObj.country,
      },
      id: {
        type: "hidden",
        name: "id",
        value: userObj.id,
      },
    };

    editDom.empty();
    editDom.makeNode("modal", "div", {
      css: "ui stackable single column grid",
    });
    editDom.modal.makeNode("cont", "div", { css: "sixteen wide column" });
    editDom.modal.cont.makeNode("headerCol", "div", {
      css: "sixteen wide column",
    });
    editDom.modal.cont.headerCol.makeNode("headerSpacer", "lineBreak", {
      spaces: 1,
    });
    editDom.modal.cont.headerCol.makeNode("header", "headerText", {
      css: "ui dividing header",
      text: "Edit Account Info",
      size: "small",
      style: "margin-top:0!important;",
    });
    //editDom.modal.cont.makeNode('formSpacer', 'lineBreak', {spaces:1});
    editDom.modal.cont
      .makeNode("formCont", "div", { css: "doubling two column row" })
      .makeNode("col", "div", { css: "column" });
    editDom.modal.cont.formCont.col.makeNode("form", "form", formSetup);
    editDom.modal.cont.makeNode("formSpacer", "lineBreak", { spaces: 1 });
    editDom.modal.cont.makeNode("btnGroup", "div", { css: "ui buttons" });

    // color selection
    var currentColor = userObj.color || "grey";
    editDom.modal.cont.formCont.col.makeNode("colorLabel", "div", {
      text: "Tag Color",
      css: "ui tiny header",
    });
    var colorSelector = editDom.modal.cont.formCont.col.makeNode(
      "colorSelector",
      "div",
      {
        css: "ui dropdown",
        listener: {
          type: "dropdown",
          maxSelections: 1,
          onChange: function (value, text, choice) {
            userObj.color = choice[0].textContent;
          },
        },
      }
    );

    colorSelector.makeNode("text", "div", {
      tag: "span",
      css: "text",
      text:
        '<i class="ui ' +
        currentColor +
        ' empty circular label"></i>' +
        currentColor,
    });

    colorSelector.makeNode("menu", "div", { css: "menu" });

    var colors = sb.dom.colors;
    _.each(colors, function (color, i) {
      colorSelector.menu.makeNode("option-" + i, "div", {
        css: "item",
        text:
          '<div class="ui ' +
          color +
          ' empty circular label" value="' +
          color +
          '"></div>' +
          color,
      });
    });

    editDom.modal.cont.btnGroup
      .makeNode("backBtn", "button", {
        css: "pda-btn-red",
        text: '<i class="fa fa-arrow-left"></i> Back',
      })
      .notify(
        "click",
        {
          type: "userComponent-run",
          data: {
            run: function () {
              modal.hide();
            }.bind(editDom),
          },
        },
        sb.moduleId
      );
    editDom.modal.cont.btnGroup
      .makeNode("button", "button", {
        css: "pda-btn-green",
        text: "Save Changes",
      })
      .notify(
        "click",
        {
          type: "save-user-edit-form-button-clicked",
          data: {
            domObj: domObj,
            button: editDom.modal.cont.btnGroup,
            form: editDom.modal.cont.formCont.col.form,
            modal: editDom,
            home: home,
            accountView: {
              userObj: userObj,
              domObj: domObj,
              state: {},
              draw: function (domCache) {},
            },
          },
        },
        sb.moduleId
      );

    editDom.patch();

    if ($(window).width() <= 768) {
      editDom.show();
    }
  }

  function userViewSettings(dom, obj) {
    function createUserView(bp, dom, obj, objType) {
      dom.empty();

      dom.makeNode("cont", "container", { css: "pda-container" });

      var createUpdate = "update",
        formSetup = {
          name: {
            name: "name",
            type: "string",
            label: "View Name",
          },
          everything: {
            name: "everything",
            label: "<b>ALLOW EVERYTHING?</b>",
            type: "select",
            options: [
              {
                name: "No",
                value: "no",
              },
              {
                name: "Yes",
                value: "yes",
              },
            ],
            change: function (form, selected) {
              var dom = this;

              _.each(_.where(form, { type: "checkbox" }), function (checkbox) {
                _.each(checkbox.options, function (opt) {
                  if (selected == "yes") {
                    opt.checked = true;
                  } else {
                    opt.checked = false;
                  }
                });

                dom.cont.form[checkbox.name].update({
                  options: checkbox.options,
                });
              });
            }.bind(dom),
          },
        };

      if (_.isObject(obj.id)) {
        createUpdate = "create";
      } else {
        formSetup.name.value = obj.name;
      }

      _.each(appConfig.navigation, function (nav) {
        formSetup[nav.id + "-section"] = {
          type: "section",
          name: nav.id + "-section",
          label: "",
          fields: {},
        };

        formSetup[nav.id + "-field"] = {
          name: nav.id + "-field",
          label: nav.title.toUpperCase(),
          type: "checkbox",
          options: [],
        };

        if (obj.permissions[nav.id]) {
          if (obj.permissions[nav.id].indexOf(nav.id) > -1) {
            formSetup[nav.id + "-field"].options.push({
              name: nav.id,
              label: nav.title.toUpperCase(),
              value: nav.id,
              checked: true,
            });
          } else {
            formSetup[nav.id + "-field"].options.push({
              name: nav.id,
              label: nav.title.toUpperCase(),
              value: nav.id,
            });
          }
        } else {
          formSetup[nav.id + "-field"].options.push({
            name: nav.id,
            label: nav.title.toUpperCase(),
            value: nav.id,
          });
        }

        /*
				formSetup[nav.id+'-section'].fields[nav.id+'-field'] = {
						name:nav.id+'-field',
						label:nav.title.toUpperCase(),
						type:'checkbox',
						options:[
							{
								name:nav.id,
								label:nav.title.toUpperCase(),
								value:nav.id
							}
						]
					};
*/

        _.each(nav.views, function (view) {
          /*
					formSetup[nav.id+'-section'].fields[nav.id+'-field'].options.push({
							name:view.title,
							label:view.title,
							value:view.id
						});
*/

          if (obj.permissions[nav.id]) {
            if (obj.permissions[nav.id].indexOf(view.id) > -1) {
              formSetup[nav.id + "-field"].options.push({
                name: nav.id,
                label: view.title,
                value: view.id,
                checked: true,
              });
            } else {
              formSetup[nav.id + "-field"].options.push({
                name: nav.id,
                label: view.title,
                value: view.id,
              });
            }
          } else {
            formSetup[nav.id + "-field"].options.push({
              name: nav.id,
              label: view.title,
              value: view.id,
            });
          }
        });
      });

      dom.cont.makeNode("form", "form", formSetup);

      dom.cont.makeNode("btns", "buttonGroup", { css: "pull-left" });

      dom.cont.btns
        .makeNode("save", "button", {
          text: '<i class="fa fa-check"></i> Save',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "staffComponent-run",
            data: {
              run: function (obj, objType) {
                this.cont.btns.save.loading();

                var formData = this.cont.form.process();

                if (createUpdate == "create") {
                  obj = {
                    name: formData.fields.name.value,
                    permissions: {},
                  };
                }

                var permissions = {};

                _.each(formData.fields, function (field, name) {
                  if (name != "everything" && name != "name") {
                    permissions[name] = field.value;
                  }
                });

                obj.permissions = permissions;

                sb.data.db.obj[createUpdate](
                  "user_views",
                  obj,
                  function (updated) {
                    userViewSettings(mainDom, updated);
                  }
                );
              }.bind(dom, obj, objType),
            },
          },
          sb.moduleId
        );

      dom.cont.btns
        .makeNode("cancel", "button", {
          text: '<i class="fa fa-times"></i> Cancel',
          css: "pda-btnOutline-red",
        })
        .notify(
          "click",
          {
            type: "staffComponent-run",
            data: {
              run: function () {
                userViewSettings(mainDom);
              },
            },
          },
          sb.moduleId
        );

      dom.cont.makeNode("btnsBreak", "lineBreak", { spaces: 3 });

      dom.patch();
    }

    dom.empty();
    dom.makeNode("cont", "container", {});
    dom.patch();

    var mainDom = dom;

    var crudSetup = {
      domObj: dom.cont,
      objectType: "user_views",
      childObjs: 1,
      tableTitle: '<i class="fa fa-users"></i> User Views',
      searchObjects: false,
      settings: false,
      filters: false,
      download: false,
      headerButtons: {
        create: {
          name: '<i class="fa fa-plus"></i> New View',
          css: "pda-btn-green",
          domType: "full",
          action: createUserView,
        },
      },
      rowSelection: true,
      multiSelectButtons: {
        erase: {
          name: '<i class="fa fa-trash-o"></i> Delete',
          css: "pda-btn-red",
          domType: "erase",
          action: "erase",
        },
      },
      calendar: false,
      home: false,
      rowLink: {
        type: "edit",
        header: function (obj) {
          return obj.name;
        },
        action: createUserView,
      },
      visibleCols: {
        name: "Name",
        menu_items: "Can View",
      },
      cells: {
        name: function (obj) {
          return obj.name;
        },
        menu_items: function (obj) {
          if (obj.permissions) {
            var ret = "",
              count = 0;

            _.each(obj.permissions, function (field, name) {
              if (count > 0) {
                ret += ", " + name.toUpperCase();
              } else {
                ret += name.toUpperCase();
              }

              count++;
            });

            return ret;
          } else {
            return "<i>All pages</i>";
          }
        },
      },
      data: function (paged, callback) {
        sb.data.db.obj.getAll(
          "user_views",
          function (dataList) {
            callback(dataList);
          },
          1,
          paged
        );
      },
    };

    comps.userviews.notify({
      type: "show-table",
      data: crudSetup,
    });
  }

  // USER HOME PAGE VIEW

  function renderMyAccount(ui, state, draw) {
    var userObj = state.user;

    var fullName = userObj.fname + " " + userObj.lname;
    var isAdmin = true;
    var now = {
      //time: moment().format('h:mma'),
      date: moment().format("dddd, MMMM Do, YYYY"),
    };

    //var now = moment();

    function getGreeting(momentResponse) {
      var greeting;

      if (!momentResponse || !momentResponse.isValid()) {
        greeting = "Hi";
      }

      var afternoon = 12;
      var evening = 17;
      var currentHour = parseFloat(momentResponse.format("HH"));

      if (currentHour >= afternoon && currentHour <= evening) {
        greeting = "Good Afternoon";
      } else if (currentHour >= evening) {
        greeting = "Good Evening";
      } else {
        greeting = "Good Morning";
      }

      return greeting;
    }

    // Create Greetings Column to separate the Header and Tabs sections
    ui.makeNode("greetingsColumn", "column", { width: 12 });

    // Greetings Container
    ui.greetingsColumn.makeNode("greetings", "container", {
      css: "pda-text-container",
    });

    // Splitting Greetings Container Into Two Columns | If user has Profile Image create 2 columns with 11:1 ratio; else, create 1 full width column
    if (userObj.profile_image.loc !== "") {
      // Make greetingsCol1 width: 11 & greetingsCol2 width: 1
      ui.greetingsColumn.greetings.makeNode("greetingsCol1", "column", {
        width: 11,
      });
      ui.greetingsColumn.greetings.makeNode("greetingsCol2", "column", {
        width: 1,
        css: "pda-text-center",
      });

      // Profile Image
      ui.greetingsColumn.greetings.greetingsCol2.makeNode(
        "profileImage",
        "image",
        {
          url: sb.data.files.getURL(userObj.profile_image),
          style:
            "max-width:100%; height:auto; max-height:78px; background-size: contain; border-radius:0.4rem;",
        }
      );
    } else {
      // Make greetingsCol1 width: 12
      ui.greetingsColumn.greetings.makeNode("greetingsCol1", "column", {
        width: 12,
      });
    }

    // Greetings Header – Good *, userName
    ui.greetingsColumn.greetings.greetingsCol1.makeNode(
      "greetTheUser",
      "headerText",
      {
        text: `${getGreeting(moment())}, ${fullName}`,
        size: "large",
      }
    );

    // Greetings Header – Week-Day, Month Day, Year
    ui.greetingsColumn.greetings.greetingsCol1.makeNode(
      "moment",
      "headerText",
      {
        text: `${now.date}`,
        size: "small",
      }
    );

    // Line Break after Greetings Header
    ui.greetingsColumn.makeNode("greetingsLineBreak", "lineBreak", {
      spaces: 2,
    });

    var tabSetup = {
      accountInfo: '<i class="fa fa-user" aria-hidden="true"></i> Account Info',
      calendar: '<i class="fa fa-calendar" aria-hidden="true"></i> Schedule',
    };

    if (userObj.related_object != null || userObj.related_object != undefined) {
      tabSetup.documents =
        '<i class="fa fa-file-text" aria-hidden="true"></i> HR Documents';
    }
    if (isAdmin) {
      tabSetup.systemSettings =
        '<i class="fa fa-cogs" aria-hidden="true"></i> System settings';
    }

    ui.makeNode("viewTabs", "tabs", { tabs: tabSetup });

    calendar_ui = ui.viewTabs.tabs.calendar
      .makeNode("calendarWrapper", "container", { css: "pda-container" })
      .makeNode("calendarCol", "column", { width: 12 });
    userInfo_ui = ui.viewTabs.tabs.accountInfo
      .makeNode("userInfoWrapper", "container", { css: "pda-container" })
      .makeNode("accountInfoCol", "column", { width: 12 });

    if (userObj.related_object != null || userObj.related_object != undefined) {
      documents_ui = ui.viewTabs.tabs.documents
        .makeNode("documentsWrapper", "container", { css: "pda-container" })
        .makeNode("documentsCol", "column", { width: 12 });
    }

    var system_settings_ui = false;
    if (isAdmin) {
      var system_settings_ui = ui.viewTabs.tabs.systemSettings
        .makeNode("systemSettingsWrapper", "container", {
          css: "pda-container",
        })
        .makeNode("systemSettingsCol", "column", { width: 12 });
    }

    draw({
      dom: ui,
      after: function (ui) {
        userInfo_ui.load = buildUserInfoView;
        userInfo_ui.load(system_settings_ui);

        calendar_ui.load = buildCalendarView;
        calendar_ui.load(userObj);

        if (
          userObj.related_object != null ||
          userObj.related_object != undefined
        ) {
          documents_ui.load = buildDocumentsView;
          documents_ui.load(userObj);
        }
      },
    });
  }

  function buildHomeView(home_ui) {
    var leftCol = home_ui.makeNode("leftCol", "column", { width: 4 });
    var rightCol = home_ui.makeNode("rightCol", "column", { width: 8 });

    leftCol.makeNode("tasks", "container", { css: "pda-container" });
    rightCol.makeNode("inbox", "container", { css: "pda-container" });

    this.patch();

    sb.data.db.obj.getWhere(
      "users",
      { id: userId, childObjs: 1 },
      function (userObj) {
        sb.notify({
          type: "show-task-list",
          data: {
            domObj: leftCol.tasks,
            objectIds: [userObj.id],
            objectId: userObj.id,
            compact: true,
          },
        });

        sb.notify({
          type: "show-staff-list",
          data: {
            domObj: rightCol.decisions,
            documents: true,
            userObj: userObj,
          },
        });
      }
    );
  }

  function buildCalendarView(userObj) {
    this.makeNode("cont", "container", {});

    this.patch();

    sb.notify({
      type: "start-scheduling_staff",
      data: {
        domObj: this.cont,
        object: {
          type: "staff",
          staff: userObj,
        },
      },
    });
  }

  function buildDocumentsView(userObj) {
    this.makeNode("loader", "loader", { size: "small" });

    this.patch();

    sb.notify({
      type: "user-documents-view",
      data: {
        domObj: this,
        userObj: userObj,
      },
    });
  }

  function buildPayrollView() {}

  function buildUserInfoView(settingsView) {
    this.patch();

    sb.notify({
      type: "show-user-account-info",
      data: {
        domObj: this,
        home: true,
        userObj: {
          related_object: sb.data.cookie.get("uid"),
        },
        settingsView: settingsView,
      },
    });
  }

  function enableUserLogin(setup) {
    createUserAccount(setup.userObj, function (newAccountObj) {
      sb.dom.alerts.alert(
        "Email Sent",
        "This user can now log in.",
        "success",
        "",
        function (resp) {
          if (resp) {
            swal.disableButtons();

            if (setup.onUpdate && typeof setup.onUpdate == "function") {
              setup.onUpdate(newAccountObj.user);
            } else {
              sb.notify({
                type: "app-navigate-to",
                data: {
                  itemId: "staff",
                  viewId: {
                    id: "single-" + setup.userObj.id,
                    type: "table-single-item",
                    title: setup.userObj.fname + " " + setup.userObj.lname,
                    icon: '<i class="fa fa-user"></i>',
                    setup: {
                      objectType: "staff",
                    },
                    rowObj: setup.userObj,
                    removable: true,
                    parent: "table",
                  },
                },
              });
            }
          }
        }
      );
    });
  }

  var enableUserLogin_throttled = _.once(enableUserLogin);

  // PUBLIC SCOPE
  return {
    init: function () {
      userId = sb.data.cookie.get("uid");

      sb.listen({
        "account-updated": this.accountUpdated,
        "change-user-views": this.changeViews,
        "create-new-user-account": this.createNewUserAccount,
        "delete-user-account": this.deleteAccount,
        "disable-user-login": this.disableLogin,
        "edit-user-address": this.editAddress,
        "enable-user-login": this.enableLogin,
        "edit-account": this.editAccount,
        "reset-user-password": this.resetPassword,
        "reset-user-password-button-clicked":
          this.resetUserPasswordButtonClicked,
        "save-new-user-account": this.saveNewAccount,
        "save-user-edit-form-button-clicked":
          this.saveUserEditFormButtonClicked,
        "show-user-account-info": this.showAccountInfo,
        "show-user-account-info-field-view": this.showAccountInfoField,

        "show-users-table": this.showTable,
        "users-object-updated": this.accountUpdated,
        "user-login-state-button-clicked": this.loginStateButtonClicked,

        "show-user-account-home-view": this.showAccountHome,

        "userComponent-run": this.run,
      });

      if (!comps.userviews) {
        comps.userviews = sb.createComponent("crud-table");
      }

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            id: "account-home",
            title: "Account Home",
            icon: '<i class="fa fa-wrench"></i>',
            display: false,
            views: [
              {
                id: "my-account",
                default: true,
                type: "custom",
                title: "My Account",
                icon: '<i class="fa fa-wrench"></i>',
                dom: function (dom, state, draw) {
                  state.user = appConfig.user;

                  renderMyAccount(dom, state, draw);
                },
              },
            ],
          },
        },
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            id: "users",
            title: "Users",
            icon: '<i class="fa fa-users"></i>',
            display: false,
            views: [
              {
                id: "table",
                default: true,
                type: "table",
                title: "All User Accounts",
                icon: '<i class="fa fa-th-list"></i>',
                setup: {
                  objectType: "users",
                  childObjs: 1,
                  searchObjects: false,
                  filters: false,
                  navigation: false,
                  headerButtons: {},
                  multiSelectButtons: {},
                  rowSelection: false,
                  rowLink: {
                    type: "notify",
                    header: function (obj) {
                      return obj.fname + " " + obj.lname;
                    },
                    action: viewSingleAccount,
                  },
                  visibleCols: {
                    name: "Name",
                    enabled: "Enabled",
                  },
                  cells: {
                    enabled: function (obj) {
                      if (obj.enabled_name == "Enabled") {
                        return (
                          '<label class="label label-success">' +
                          obj.enabled_name +
                          "</label>"
                        );
                      } else {
                        return (
                          '<label class="label label-danger">' +
                          obj.enabled_name +
                          "</label>"
                        );
                      }
                    },
                    name: function (obj) {
                      return obj.fname + " " + obj.lname;
                    },
                  },
                  data: function (paged, callback) {
                    sb.data.db.obj.getAll(
                      "users",
                      function (ret) {
                        callback(ret);
                      },
                      1,
                      paged
                    );
                  },
                },
              },
              {
                id: "settings",
                type: "settings",
                title: "Settings",
                icon: '<i class="fa fa-cogs"></i>',
                setup: [
                  {
                    object_type: "user_views",
                    name: "User Views",
                    action: userViewSettings,
                  },
                ],
              },
            ],
          },
        },
      });
    },

    accountUpdated: function (setup) {
      sb.notify({
        type: "app-redraw",
        data: {},
      });
    },

    changeViews: function (setup) {
      sb.obj.edit(
        setup.domObj,
        "users",
        setup.userObj,
        {
          profiles: {},
        },
        1
      );
    },

    createNewUserAccount: function (setup) {
      createUserAccount(setup.userObj, function (newAccountObj) {
        sb.notify({
          type: "user-account-created",
          data: newAccountObj,
        });
      });
    },

    deleteAccount: function (setup) {
      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "This will allow the user to login and access thier account.",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();
          }
        }
      );
    },

    destroy: function () {
      _.each(comps, function (comp) {
        comp.destroy();
      });

      accountInfoSetup = {};
      comps = {};
    },

    disableLogin: function (setup) {
      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "This will block the user from logging in.",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();

            changeLoginState(0, setup.objectId, function (updated) {
              if (updated) {
                sb.dom.alerts.alert("Login Disabled", "", "success");

                if (setup.onUpdate && typeof setup.onUpdate == "function") {
                  setup.onUpdate(updated);
                } else {
                  sb.notify({
                    type: "app-redraw",
                    data: {},
                  });
                }
              }
            });
          }
        }
      );
    },

    editAccount: function (setup) {
      editAccount(setup.domObj, setup.userObj, setup.modal, setup);
    },

    enableLogin: function (setup) {
      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "This will allow the user to login and access thier account.",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();

            changeLoginState(1, setup.objectId, function (updated) {
              if (updated) {
                sb.notify({
                  type: "app-redraw",
                  data: {},
                });

                sb.dom.alerts.alert("Login Enabled", "", "success");
              }
            });
          }
        }
      );
    },

    loginStateButtonClicked: function (setup) {
      if (setup.enabled === 1) {
        sb.notify({
          type: "enable-user-login",
          data: {
            objectId: setup.object.id,
          },
        });
      } else {
        sb.notify({
          type: "disable-user-login",
          data: {
            objectId: setup.object.id,
          },
        });
      }
    },

    resetPassword: function (setup) {
      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "Would you like to generate a new password or set it yourself?",
          icon: "question",
          primaryButtonText: "Generate new password",
          secondaryButtonText: "Set the password",
        },
        function (primaryButtonClicked, secondaryButtonClicked) {
          if (primaryButtonClicked) {
            swal.disableButtons();

            resetUserPassword(setup.object, true, function (updated) {
              sb.dom.alerts.alert("Password changed", "", "success");

              sb.notify({
                type: "app-redraw",
                data: {},
              });
            });
          } else if (secondaryButtonClicked) {
            sb.dom.alerts.ask(
              {
                title: "Enter the new password",
                text: "Make it one to remember!",
                icon: "question",
                input: "password",
              },
              function (
                primaryButtonClicked,
                secondaryButtonClicked,
                newPassword
              ) {
                if (primaryButtonClicked) {
                  swal.disableButtons();

                  resetUserPassword(
                    setup.object,
                    true,
                    function (updated) {
                      sb.dom.alerts.alert("Password changed", "", "success");

                      sb.notify({
                        type: "app-redraw",
                        data: {},
                      });
                    },
                    newPassword
                  );
                }
              }
            );
          } else {
            swal.disableButtons();
            swal.close();
          }
        }
      );
    },

    resetUserPasswordButtonClicked: function (data) {
      sb.notify({
        type: "reset-user-password",
        data: {
          object: data.object,
        },
      });
    },

    run: function (data) {
      data.run(data);
    },

    saveNewAccount: function (setup) {
      sb.dom.alerts.ask(
        {
          title: "Are you sure?",
          text: "This will generate a password and send the user a login email.",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();

            var formData = setup.form.process();

            if (formData.fields.profiles) {
              setup.userObj.profiles = formData.fields.profiles.value;
            } else {
              setup.userObj.profiles = [];
            }

            createUserAccount(setup.userObj, function (newAccountObj) {
              sb.dom.alerts.alert("Created!", "", "success");

              setup.domObj.userModal.hide();

              sb.notify({
                type: "app-redraw",
                data: {},
              });

              setTimeout(function () {
                sb.notify({
                  type: "app-navigate-to",
                  data: {
                    itemId: "staff",
                    viewId: "table",
                  },
                });

                setTimeout(function () {
                  sb.notify({
                    type: "app-redraw",
                    data: {},
                  });
                }, 1000);
              }, 1000);
            });
          }
        }
      );
    },

    showAccountInfo: function (setup) {
      var domObj = setup.domObj;

      if (!setup.hasOwnProperty("home")) {
        (home = false), (setup.home = false);
      }

      if (setup.userObj.enabled) {
        viewSingleAccount(setup.userObj, domObj, {}, function (domCache) {});
      } else {
        if (!setup.home) {
          domObj.makeNode("cont", "div", { css: "ui two grid" });
          domObj.cont.makeNode("item", "div", { css: "ui basic segment" });
          domObj.cont.item.makeNode("buttonGroup", "div", {
            css: "ui buttons",
          });
          domObj.cont.item.buttonGroup.makeNode("create", "div", {
            css: "ui green button",
            text: '<i class="fa fa-unlock"></i> Create A Login Account',
          });

          domObj.patch();

          if (domObj.cont.item.buttonGroup.create) {
            $(domObj.create.selector).on("click", function () {
              enableUserLogin_throttled({
                userObj: setup.userObj,
              });
            });
          }
        }
      }

      if (setup.settingsView) {
        var settingsUI = sb.dom.make(setup.settingsView.selector);
      }
    },

    showAccountInfoField: function (setup) {
      if (setup.domObj && setup.userObj)
        accountFieldView(setup.userObj, setup.domObj);
    },

    showAccountHome: function (setup) {
      ui = sb.dom.make(setup.domObj.selector);

      renderMyAccount(ui, appConfig.user);
    },

    showTable: function (setup) {
      comps.table = sb.createComponent("crud-table");
      comps.table.notify({
        type: "show-table",
        data: {
          domObj: setup.domObj,
          objectType: "users",
          childObjs: 1,
          searchObjects: [
            {
              name: "First Name",
              value: "fname",
            },
            {
              name: "Last Name",
              value: "lname",
            },
          ],
          filters: false,
          navigation: false,
          headerButtons: {},
          multiSelectButtons: {},
          rowSelection: false,
          rowLink: {
            type: "tab",
            header: function (obj) {
              return obj.fname + " " + obj.lname;
            },
            action: function () {},
          },
          visibleCols: {
            name: "Name",
            email: "Email",
            phone: "Phone",
            enabled: "Enabled",
          },
          cells: {
            enabled: function (obj) {
              if (obj.enabled_name == "Enabled") {
                return (
                  '<label class="label label-success">' +
                  obj.enabled_name +
                  "</label>"
                );
              } else {
                return (
                  '<label class="label label-danger">' +
                  obj.enabled_name +
                  "</label>"
                );
              }
            },
            name: function (obj) {
              return obj.fname + " " + obj.lname;
            },
          },
          data: function (paged, callback) {
            sb.data.db.obj.getAll(
              "users",
              function (ret) {
                callback(ret);
              },
              1,
              paged
            );
          },
        },
      });
    },

    saveUserEditFormButtonClicked: function (setup) {
      setup.button.makeNode("button", "button", {
        css: "btn-primary",
        text: 'Saving <i class="fa fa-circle-o-notch fa-spin"></i>',
      });

      setup.button.patch();

      var formInfo = setup.form.process(),
        updateObj = {
          id: formInfo.fields.id.value,
          fname: formInfo.fields.fname.value,
          lname: formInfo.fields.lname.value,
          nick_name: formInfo.fields.nick_name.value,
          profile_image: formInfo.fields.profile_image.value,
          email: formInfo.fields.email.value,
          phone: formInfo.fields.phone.value,
          street: formInfo.fields.street.value,
          city: formInfo.fields.city.value,
          state: formInfo.fields.state.value,
          zip: formInfo.fields.zip.value,
          country: formInfo.fields.country.value,
          color: setup.accountView.userObj.color,
        };
      notifySetup = {
        type: "app-redraw",
        data: {},
      };

      updateUserObject(updateObj, function (updatedObj) {
        setup.button.makeNode("button", "button", {
          css: "btn-primary",
          text: 'Saved <i class="fa fa-check"></i>',
        });

        setup.button.patch();

        setTimeout(function () {
          setup.modal.hide();

          if (setup.hasOwnProperty("accountView")) {
            notifySetup.type = "userComponent-run";

            notifySetup.data = {
              run: function () {
                viewSingleAccount(
                  setup.accountView.userObj,
                  setup.accountView.domObj,
                  setup.accountView.state,
                  setup.accountView.draw
                );
              }.bind(setup),
            };
          }

          sb.notify(notifySetup);
        }, 0);
      });
    },
  };
});
