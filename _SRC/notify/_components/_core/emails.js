Factory.register("emails", function (sb) {
  var ui = {},
    comps = {},
    viewSettings = {
      collapse: false,
    },
    defaultTo = "",
    object_id = 0,
    object_type = "",
    searchPhrase = "",
    state = {
      folder: "inbox",
      thread_id: 0,
    },
    unreadCount = null,
    sentCallback = null;

  function back_to_table() {
    state.thread_id = 0;

    comps[state.folder].notify({
      type: "crud-table-back-to-table",
      data: {},
    });
  }

  function composeForm(
    bp,
    container,
    replyTo,
    replyOrForward,
    containerToScroll,
    emailData
  ) {
    function sendEmail(objectId, objectType) {
      var formData = this.c.form.process();

      var emailObj = {
        to: formData.fields.to.value,
        from: appConfig.emailFrom,
        subject: formData.fields.subject.value,
        mergevars: {
          TITLE: formData.fields.subject.value,
          BODY: formData.fields.message.value
            .split(/(?:\r\n|\r|\n)/g)
            .join("<br />"),
        },
        emailtags: [""],
        type: objectType,
        typeId: objectId,
      };

      if (formData.fields.emailSelection) {
        emailObj.to = emailObj.to.split(",");

        emailObj.to = emailObj.to.concat(formData.fields.emailSelection.value);
      }

      if (parseInt(formData.fields.newThread.value) > 0) {
        emailObj.newThread = true;
      } else {
        emailObj.newThread = false;
        emailObj.threadId = parseInt(formData.fields.threadId.value);
      }

      // check that user input is complete
      if (_.isEmpty(emailObj.subject)) {
        sb.dom.alerts.alert(
          "Sorry!",
          "Please add a subject to your email before you send it.",
          "error"
        );
        return false;
      } else if (_.isEmpty(emailObj.to)) {
        sb.dom.alerts.alert(
          "Sorry!",
          "Please specify the email address you want to send this to.",
          "error"
        );
        return false;
      } else if (
        _.isEmpty(emailObj.mergevars.BODY) &&
        objectType !== "contracts"
      ) {
        sb.dom.alerts.alert(
          "Sorry!",
          "You haven't written a message.",
          "error"
        );
        return false;
      }

      // check that the email address is valid
      var email_format_regex =
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (!email_format_regex.test(emailObj.to)) {
        /*
				sb.dom.alerts.alert('Sorry!', 'Please enter a valid email address.', 'error');
				return false;
*/
      }

      this.c.btns.send.loading(true);

      // send to selected staff members
      if (formData.fields.staffMembers) {
        emailObj.to = emailObj.to.concat(formData.fields.staffMembers.value);
      }

      emailObj.clicked = 1;

      emailObj.to = _.filter(emailObj.to, function (el) {
        return el !== "";
      });

      // append html string body to infinity contracts
      if (objectType === "contracts") {
        sb.data.db.obj.getById(objectType, objectId, function (obj) {
          if (obj.instance === "infinity" && obj.instance === "dreamcatering") {
            emailObj.mergevars.BODY += `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
            <head>
            	<meta http-equiv="Content-Type" content="text/html charset=UTF-8" />
            </head>
            <html>
            	<body>
            		<table style="width:100%; background-color:#f8f8f8; padding:0px 40px 40px 40px !important;">
            			<tbody>
            				<tr>
            					<td>
            						<table style="margin:0 auto; padding:10px;">
            							<tbody>
            								<tr>
            									<td style="color:grey !important;">
            										Does this email not look right? <a href="${sb.url}/app/documents#?&i=${obj.instance}&wid=${objectId}" style="color:grey !important;">Try viewing in browser instead.</a>
            									</td>
            								</tr>
            							</tbody>
            						</table>
            						<table style="background-color:#ffffff; margin:0 auto; padding:40px 40px 40px 40px !important; border:1px solid rgb(235, 235, 235); border-radius:0.375rem !important; width:830px; max-width:830px;">
            							<tbody>
            								<tr>
            									<td>
            										${obj.html_string}
            									</td>
            								</tr>
            							</tbody>
            						</table>
            					</td>
            				</tr>
            			</tbody>
            		</table>
            	</body>
            </html>`;
          }

          sb.comm.sendEmail(emailObj, function (response) {
            if (response) {
              //sb.dom.alerts.alert('Sent!', 'Your email has been sent!', 'success');

              if (ui.hasOwnProperty("table")) {
                ui.table[state.folder]();
              }

              if (sentCallback !== null) {
                sentCallback(response);
              }
            } else {
              sb.dom.alerts.alert(
                "Uh oh!",
                "There was a problem sending the email--please refresh and try again.",
                "error"
              );
            }
          });
        });
      } else {
        sb.comm.sendEmail(emailObj, function (response) {
          if (response) {
            //sb.dom.alerts.alert('Sent!', 'Your email has been sent!', 'success');

            if (ui.hasOwnProperty("table")) {
              ui.table[state.folder]();
            }

            if (sentCallback !== null) {
              sentCallback(response);
            }
          } else {
            sb.dom.alerts.alert(
              "Uh oh!",
              "There was a problem sending the email--please refresh and try again.",
              "error"
            );
          }
        });
      }

      //   sb.comm.sendEmail(emailObj, function (response) {
      //     if (response) {
      //       //sb.dom.alerts.alert('Sent!', 'Your email has been sent!', 'success');

      //       if (ui.hasOwnProperty("table")) {
      //         ui.table[state.folder]();
      //       }

      //       if (sentCallback !== null) {
      //         sentCallback(response);
      //       }
      //     } else {
      //       sb.dom.alerts.alert(
      //         "Uh oh!",
      //         "There was a problem sending the email--please refresh and try again.",
      //         "error"
      //       );
      //     }
      //   });
    }

    var formParams = {
      objectType: {
        name: "objectType",
        type: "hidden",
        value: object_type,
      },
      objectId: {
        name: "objectId",
        type: "hidden",
        value: object_id,
      },
      newThread: {
        name: "newThread",
        type: "hidden",
      },
      subject: {
        name: "subject",
        type: "text",
        label: "Subject",
      },
      emailSelection: {
        name: "emailSelection",
        type: "hidden",
        label: "Send To",
      },
      to: {
        name: "to",
        type: "text",
        label: "To",
        value: defaultTo,
        // 					inputList: [] put additional contacts here for events for autocomplete
      },
      message: {
        name: "message",
        type: "textbox",
        rows: 8,
        wysiwyg: {
          height: null,
          minHeight: 300,
          maxHeight: null,
          focus: true,
          toolbar: true,
        },
      },
    };

    if (emailData) {
      formParams.subject.value = emailData.subject;
      formParams.to.value = emailData.to;
      formParams.message.value = emailData.message;

      if (emailData.additionalEmails) {
        formParams.to.label = "Additional Email Addresses (seperate by comma)";
        formParams.emailSelection.type = "checkbox";
        formParams.emailSelection.options = _.map(
          emailData.additionalEmails,
          function (emailObj) {
            emailObj.name = "emailSelection";

            return emailObj;
          },
          []
        );
      }

      if (emailData.staff) {
        formParams.staff = {
          name: "staffMembers",
          type: "checkbox",
          label: "Send to other staff?",
          search: true,
          options: _.map(emailData.staff, function (staffMember) {
            var ret = {
              label: staffMember.fname + " " + staffMember.lname,
              name: "staffMember",
              value: staffMember.email,
            };

            return ret;
          }),
        };
      }
    }

    if (replyTo && parseInt(replyTo.id) === replyTo.id) {
      formParams.newThread.value = 0;
      formParams.threadId = {
        name: "threadId",
        type: "hidden",
        value: parseInt(replyTo.id),
      };

      switch (replyOrForward) {
        case "reply":
          formParams.subject.value =
            "Re: " + replyTo.emails[0].subject.replace("Re: ", "");
          formParams.to.value = replyTo.emails[0].from;
          break;

        case "forward":
          formParams.subject.value =
            "Fw: " + replyTo.emails[0].subject.replace("Fw: ", "");
          break;
      }
    } else {
      formParams.newThread.value = 1;
    }

    var titleText = "";
    if (replyTo && parseInt(replyTo.id) === replyTo.id) {
      switch (replyOrForward) {
        case "reply":
          titleText = "Reply to message";
          break;

        case "forward":
          titleText = "Forward message";
          break;
      }
    } else {
      titleText = "New message";
    }
    container.makeNode("c", "column", { w: 16 });
    if (replyOrForward === "reply" || replyOrForward === "forward") {
      container.c
        .makeNode("close", "button", {
          text: '<i class="fa fa-times" aria-hidden="true"></i>',
          css: "pull-right",
        })
        .notify(
          "click",
          {
            type: "emails-comp-run",
            data: {
              run: function () {
                this.empty();
                this.patch();
              }.bind(container),
            },
          },
          sb.moduleId
        );
    } else if (ui.hasOwnProperty("table")) {
      container.c
        .makeNode("backButton", "button", {
          text: '<i class="fa fa-chevron-left"></i> Back to inbox',
          css: "pda-btnOutline-blue",
        })
        .notify(
          "click",
          {
            type: "emails-comp-run",
            data: {
              run: back_to_table,
            },
          },
          sb.moduleId
        );

      container.c.makeNode("break", "lineBreak", {});
    }

    container.c.makeNode("title", "headerText", {
      size: "small",
      text: titleText,
    });
    container.c.makeNode("break", "lineBreak", {});
    container.c.makeNode("form", "form", formParams);
    container.c.makeNode("afterFormBreak", "div", {
      css: "ui sixteen wide column",
      text: '<div class="ui clearing divider"></div>',
    });
    container.c.makeNode("btns", "div", { css: "ui buttons" });
    container.c.btns
      .makeNode("send", "button", {
        text: "Send",
        css: "ui teal right floated button",
      })
      .notify(
        "click",
        {
          type: "emailsRun",
          data: {
            run: function () {
              sendEmail.call(container, object_id, object_type);
            },
          },
        },
        sb.moduleId
      );

    container.patch();

    if (replyOrForward === "reply" || replyOrForward === "forward") {
      scrollToEmailBottom(containerToScroll);
    }
  }

  function eraseThreads(threads) {
    function eraseThread(threadList, callback) {
      if (_.isEmpty(threadList)) {
        return callback(true);
      } else {
        sb.data.db.obj.erase("threadList", threadList[0].id, function (r) {
          if (r) {
            eraseEmail(threadList[0].emails, function (r) {
              threadList.shift();
              eraseThread(threadList, callback);
            });
          }
        });
      }
    }

    function eraseEmail(emailList, callback) {
      if (_.isEmpty(emailList)) {
        return callback(true);
      } else {
        sb.data.db.obj.erase("emails", emailList[0].id, function (r) {
          if (r) {
            emailList.shift();
            eraseEmail(emailList, callback);
          }
        });
      }
    }

    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "This can't be undone.",
        showLoaderOnConfirm: true,
      },
      function (response) {
        if (response) {
          eraseThread(threads, function (r) {
            if (r) {
              sb.dom.alerts.alert("Deleted!", "", "success");
              comps[state.folder].notify({
                type: "update-table",
                data: {},
              });
            }
          });
        }
      }
    );
  }

  function getSingleThread(thread_id, callback) {
    sb.data.db.obj.getById("threads", thread_id, function (thread) {
      sb.data.db.obj.getWhere(
        "emails",
        { thread_id: thread.id },
        function (emails) {
          thread.emails = _.sortBy(emails, function (email) {
            return moment(email.date_created, "YYYY-MM-DD HH:mm:ss");
          });

          return callback(thread);
        }
      );
    });
  }

  function highlightSearchPhrase(string) {
    if (
      typeof string !== "undefined" &&
      typeof string.split !== "undefined" &&
      typeof searchPhrase !== "undefined" &&
      searchPhrase != ""
    ) {
      return string
        .split(searchPhrase)
        .join(
          '<span class="text-primary bg-danger">' + searchPhrase + "</span>"
        );
    } else {
      return string;
    }
  }

  function scrollToEmailBottom(container) {
    $(container.selector).animate(
      {
        scrollTop: $(container.selector)[0].scrollHeight,
      },
      500
    );
  }

  function sendEmail() {}

  function setup_ui() {
    var ui = this;

    function get_threads(page, callback) {
      if (object_id === 0) {
        switch (state.folder) {
          case "inbox":
            var queryObj = {
              listeners: {
                type: "contains-id",
                value: parseInt(sb.data.cookie.userId),
              },
              replied_to: 1,
            };
            queryObj.paged = page;

            sb.data.db.obj.getWhere("threads", queryObj, function (data) {
              var threads = data.data,
                threadIds = _.pluck(threads, "id");

              sb.data.db.obj.getWhere(
                "emails",
                { thread_id: { type: "or", values: threadIds } },
                function (emails) {
                  data.data = sort_threads(threads, emails);

                  return callback(data);
                }
              );
            });

            break;

          case "outbox":
            var queryObj = {
              listeners: {
                type: "contains-id",
                value: parseInt(sb.data.cookie.userId),
              },
            };
            queryObj.paged = page;
            sb.data.db.obj.getWhere("threads", queryObj, function (data) {
              var threads = data.data,
                threadIds = _.pluck(threads, "id");

              sb.data.db.obj.getWhere(
                "emails",
                { thread_id: { type: "or", values: threadIds } },
                function (emails) {
                  data.data = sort_threads(threads, emails);

                  return callback(data);
                }
              );
            });

            break;
        }
      } else {
        switch (state.folder) {
          case "inbox":
            var queryObj = {
              object_id: object_id,
              replied_to: 1,
            };
            queryObj.paged = page;
            sb.data.db.obj.getWhere("threads", queryObj, function (data) {
              var threads = data.data,
                threadIds = _.pluck(threads, "id");

              sb.data.db.obj.getWhere(
                "emails",
                { thread_id: { type: "or", values: threadIds } },
                function (emails) {
                  data.data = sort_threads(threads, emails);

                  return callback(data);
                }
              );
            });

            break;

          case "outbox":
            var queryObj = {
              object_id: object_id,
            };
            queryObj.paged = page;
            sb.data.db.obj.getWhere("threads", queryObj, function (data) {
              var threads = data.data,
                threadIds = _.pluck(threads, "id");

              sb.data.db.obj.getWhere(
                "emails",
                { thread_id: { type: "or", values: threadIds } },
                function (emails) {
                  data.data = sort_threads(threads, emails);

                  return callback(data);
                }
              );
            });

            break;
        }
      }
    }

    function sort_threads(threads, emails) {
      _.each(threads, function (thread, i) {
        threads[i].emails = _.where(emails, { thread_id: thread.id });
      });

      threads = _.filter(threads, function (thread) {
        return thread.emails.length > 0;
      });

      _.each(threads, function (thread) {
        thread.emails = _.sortBy(thread.emails, function (email) {
          return moment(email.date_created, "YYYY-MM-DD HH:mm:ss");
        });
      });

      threads = _.sortBy(threads, function (thread) {
        return -moment(thread.emails[0].date_created, "YYYY-MM-DD HH:mm:ss");
      });

      return threads;
    }

    function setup_nav() {
      var nav_ui = this,
        inboxCss = "pda-btn-fullWidth",
        sentCss = "pda-btn-fullWidth";

      function change_folder(data) {
        state.folder = data.dataId;
        if (state.folder === "inbox") {
          this.inbox.css("pda-btn-fullWidth pda-btn-blue");
          this.inbox.text(get_folder_text("inbox"));

          this.outbox.text(get_folder_text("outbox"));
          this.outbox.css("pda-btn-fullWidth pda-btnOutline-blue");
        } else {
          this.inbox.text(get_folder_text("inbox"));
          this.inbox.css("pda-btn-fullWidth pda-btnOutline-blue");

          this.outbox.text(get_folder_text("outbox"));
          this.outbox.css("pda-btn-fullWidth pda-btn-blue");
        }

        ui.table[state.folder]();
      }

      function get_folder_text(folderType) {
        var ret = "";
        switch (folderType) {
          case "inbox":
            ret = "Inbox";
            if (unreadCount === null) {
              ret += " ...";
            } else if (unreadCount > 0) {
              ret = "<strong>" + ret + " (" + unreadCount + ")</strong>";
            }
            break;

          case "outbox":
            ret = "Sent";
            break;
        }

        if (folderType === state.folder && viewSettings.collapse === false) {
          ret += ' <i class="fa fa-chevron-right" aria-hidden="true"></i>';
        }

        return ret;
      }

      function update_unread_count() {
        this.inbox.text(get_folder_text("inbox"));
      }

      if (state.folder === "inbox") {
        inboxCss += " pda-btn-blue";
      } else {
        sentCss += " pda-btn-blue";
      }

      this.makeNode("inbox", "button", {
        text: get_folder_text("inbox"),
        css: inboxCss,
        dataId: "inbox",
      }).notify(
        "click",
        {
          type: "emails-comp-run",
          data: {
            run: change_folder.bind(this),
          },
        },
        sb.moduleId
      );

      this.makeNode("outbox", "button", {
        text: get_folder_text("outbox"),
        css: sentCss,
        dataId: "outbox",
      }).notify(
        "click",
        {
          type: "emails-comp-run",
          data: {
            run: change_folder.bind(this),
          },
        },
        sb.moduleId
      );

      this.updateCount = update_unread_count;

      this.patch();

      if (object_id === 0) {
        sb.data.db.obj.getWhere(
          "threads",
          {
            listeners: {
              type: "contains-id",
              value: parseInt(sb.data.cookie.userId),
            },
            replied_to: 1,
            not_viewed: 1,
            paged: {
              page: 0,
              pageLength: 1,
              paged: true,
              sortCol: "date_created",
              sortDir: "desc",
              sortCast: "string",
            },
          },
          function (data) {
            unreadCount = data.recordsTotal;
            nav_ui.updateCount();
          }
        );
      } else {
        sb.data.db.obj.getWhere(
          "threads",
          {
            object_id: object_id,
            replied_to: 1,
            not_viewed: 1,
            paged: {
              page: 0,
              pageLength: 1,
              paged: true,
              sortCol: "date_created",
              sortDir: "desc",
              sortCast: "string",
            },
          },
          function (data) {
            unreadCount = data.recordsTotal;
            nav_ui.updateCount();
          }
        );
      }
    }

    function setup_inbox_table() {
      var sysSetup = {
        domObj: this,
        tableTitle: "",
        objectType: "threads",
        searchObjects: false,
        navigation: false,
        headerButtons: {
          create: {
            name: '<i class="fa fa-pencil-square-o" aria-hidden="true"></i> Compose',
            css: "pda-btn-primary",
            domType: "full",
            action: composeForm,
          },
          reload: {
            name: "Reload",
            css: "pda-btn-blue",
            action: function () {},
          },
        },
        rowSelection: true,
        visibleCols: {
          disp: "",
        },
        cells: {
          disp: function (obj, ui) {
            if (obj.emails.length > 0) {
              var open = (close = "");
              if (obj.not_viewed === 1) {
                open = "<strong>";
                close = "</strong>";
              }

              ui.makeNode("disp", "container", { style: "padding:0px;" });

              // from
              ui.makeNode("from", "column", {
                width: 3,
                style:
                  "padding:0px;height:2em;overflow:hidden;text-overflow:ellipsis;",
              }).makeNode("text", "text", {
                text: open + obj.emails[obj.emails.length - 1].from + close,
              });

              // subject
              ui.makeNode("subject", "column", {
                width: 8,
                style:
                  "padding:0px;height:2em;overflow:hidden;text-overflow:ellipsis;",
              }).makeNode("text", "text", {
                style: "",
                text:
                  open +
                  obj.emails[0].subject +
                  ' <span class="text-muted">' +
                  obj.emails[obj.emails.length - 1].message +
                  "</span>" +
                  close,
              });

              // date
              var today = moment().startOf("day");
              var dateCreated = moment(
                obj.emails[0].date_created,
                "YYYY-MM-DD HH:mm:ss"
              );
              var momentFormat = "l";
              if (dateCreated.isSame(today, "d")) {
                momentFormat = "h:mm a";
              }

              ui.makeNode("date", "column", {
                width: 1,
                style:
                  "padding:0px;height:2em;overflow:hidden;text-overflow:ellipsis;",
              }).makeNode("text", "text", {
                text: open + dateCreated.format(momentFormat) + close,
              });
            }
          },
        },
        rowLink: {
          type: "tab",
          header: function (obj) {
            return obj.emails[0].subject;
          },
          action: singleThreadView,
        },
        multiSelectButtons: {
          erase: {
            name: '<i class="fa fa-trash-o"></i> Delete',
            css: "pda-btn-red",
            domType: "none",
            action: eraseThreads,
          },
        },
        data: function (page, callback) {
          get_threads(page, callback);
        },
      };

      if (state.thread_id > 0) {
        sysSetup.objectId = getSingleThread.bind({}, state.thread_id);
        sysSetup.navigation = true;
      }

      if (comps.hasOwnProperty("inbox")) {
        comps.inbox.destroy();
      }

      comps.inbox = sb.createComponent("crud-table");
      comps.inbox.notify({
        type: "show-table",
        data: sysSetup,
      });
    }

    function setup_outbox_table() {
      var sysSetup = {
        domObj: this,
        tableTitle: "",
        objectType: "threads",
        searchObjects: false,
        navigation: false,
        headerButtons: {
          create: {
            name: '<i class="fa fa-pencil-square-o" aria-hidden="true"></i> Compose',
            css: "pda-btn-primary",
            domType: "full",
            action: composeForm,
          },
          reload: {
            name: "Reload",
            css: "pda-btn-blue",
            action: function () {},
          },
        },
        rowSelection: true,
        visibleCols: {
          disp: "",
        },
        cells: {
          disp: function (obj, ui) {
            if (obj.emails.length > 0) {
              ui.makeNode("disp", "container", { style: "padding:0px;" });

              // from
              ui.makeNode("from", "column", {
                width: 3,
                style:
                  "padding:0px;height:2em;overflow:hidden;text-overflow:ellipsis;",
              }).makeNode("text", "text", {
                text: "To: " + obj.emails[obj.emails.length - 1].from,
              });

              // subject
              ui.makeNode("subject", "column", {
                width: 8,
                style:
                  "padding:0px;height:2em;overflow:hidden;text-overflow:ellipsis;",
              }).makeNode("text", "text", {
                style: "",
                text:
                  obj.emails[0].subject +
                  ' <span class="text-muted">' +
                  obj.emails[obj.emails.length - 1].message +
                  "</span>",
              });

              // date
              var today = moment().startOf("day");
              var dateCreated = moment(
                obj.emails[obj.emails.length - 1].date_created,
                "YYYY-MM-DD HH:mm:ss"
              );
              var momentFormat = "l";
              if (dateCreated.isSame(today, "d")) {
                momentFormat = "h:mm a";
              }

              ui.makeNode("date", "column", {
                width: 1,
                style:
                  "padding:0px;height:2em;overflow:hidden;text-overflow:ellipsis;",
              }).makeNode("text", "text", {
                text: dateCreated.format(momentFormat),
              });
            }
          },
        },
        rowLink: {
          type: "tab",
          header: function (obj) {
            return obj.emails[0].subject;
          },
          action: singleThreadView,
        },
        multiSelectButtons: {
          erase: {
            name: '<i class="fa fa-trash-o"></i> Delete',
            css: "pda-btn-red",
            domType: "none",
            action: eraseThreads,
          },
        },
        data: function (page, callback) {
          get_threads(page, callback);
        },
      };

      if (comps.hasOwnProperty("outbox")) {
        comps.outbox.destroy();
      }

      comps.outbox = sb.createComponent("crud-table");
      comps.outbox.notify({
        type: "show-table",
        data: sysSetup,
      });
    }

    this.empty();
    this.makeNode("nav", "column", { width: 1 });

    this.makeNode("table", "column", { width: 11, css: "pda-container" });
    if (this.hasOwnProperty("build")) {
      this.build();
    } else if (this.hasOwnProperty("patch")) {
      this.patch();
    }

    this.table.inbox = setup_inbox_table;
    this.table.outbox = setup_outbox_table;

    this.table[state.folder]();

    this.nav.setup = setup_nav;
    this.nav.setup();
  }

  function singleThreadView(thread, container) {
    function mark_as_read(thread) {
      if (thread.not_viewed === 1) {
        sb.data.db.obj.update(
          "threads",
          { id: thread.id, not_viewed: 0 },
          function (response) {
            unreadCount--;
            ui.nav.updateCount();
          }
        );
      }
    }

    var conversation = thread.emails;

    container
      .makeNode("backButton", "button", {
        text: '<i class="fa fa-chevron-left"></i> Back to inbox',
        css: "pda-btnOutline-blue",
      })
      .notify(
        "click",
        {
          type: "emails-comp-run",
          data: {
            run: back_to_table,
          },
        },
        sb.moduleId
      );

    // display area
    container
      .makeNode("email", "container", {
        style: "max-height: 500px; overflow-y: scroll;",
      })
      .makeNode("btns", "buttonGroup", { css: "pull-right" });

    container.email.makeNode("subject", "headerText", {
      size: "small",
      text:
        highlightSearchPhrase(
          conversation[0].subject.replace(/ *\[[^)]*\] */g, "")
        ) + "<hr>",
    });

    _.each(conversation, function (email, i) {
      var emailDate = moment(email.date_created, "YYYY-MM-DD HH:mm:ss"),
        containerInitialState = "closed";

      if (i === conversation.length - 1) {
        containerInitialState = "open";
      }

      container.email.makeNode("email-" + email.id, "container", {
        collapse: containerInitialState,
        title: emailDate.format("l") + " (" + emailDate.fromNow() + ")",
        subTitle:
          email.from +
          ' <i class="fa fa-arrow-circle-right" aria-hidden="true"></i>',
      });

      // 			container.email['email-'+ email.id].makeNode('date', 'text', {css: 'text-center', text: emailDate.format('l') +' ('+ emailDate.fromNow() +')'});

      // 			container.email['email-'+ email.id].makeNode('subject', 'headerText', {size: 'small', text: highlightSearchPhrase(email.subject.replace(/ *\[[^)]*\] */g, ""))});
      container.email["email-" + email.id].makeNode("from", "text", {
        text: "<strong>From:</strong> " + highlightSearchPhrase(email.from),
      });
      container.email["email-" + email.id].makeNode("to", "text", {
        text: "<strong>To:</strong> " + highlightSearchPhrase(email.to),
      });
      container.email["email-" + email.id].makeNode("message", "text", {
        text: highlightSearchPhrase(email.message) + "<hr>",
        css: "pda-background-gray pda-container",
      });
    });

    // reply area
    container.email
      .makeNode("replyArea", "column", { width: 12 })
      .makeNode("btns", "buttonGroup", { css: "pull-right" })
      .makeNode("reply", "button", { text: "Reply" });

    container.email.replyArea.btns.makeNode("forward", "button", {
      text: '<i class="glyphicon glyphicon-share-alt"></i>',
    });

    container.email.makeNode("message", "container", {});

    container.email.replyArea.btns.reply.notify(
      "click",
      {
        type: "emails-comp-run",
        data: {
          run: composeForm.bind(
            {},
            {},
            container.email.message,
            thread,
            "reply",
            container.email
          ),
        },
      },
      sb.moduleId
    );

    container.email.replyArea.btns.forward.notify(
      "click",
      {
        type: "emails-comp-run",
        data: {
          run: composeForm.bind(
            {},
            {},
            container.email.message,
            thread,
            "forward",
            container.email
          ),
        },
      },
      sb.moduleId
    );

    container.patch();

    mark_as_read(thread);

    scrollToEmailBottom(container.email);
  }

  // Newer registrations 2020

  function singleEmailView(ui, email, onDraw) {
    ui.makeNode("h", "div", {
      tag: "h1",
      text: email.subject,
    });

    ui.makeNode("to", "div", {
      // 					tag: 'i'
      text:
        "Sent to: " +
        email.to +
        ", <i>" +
        moment(email.date_created).fromNow() +
        "</i>",
    });

    ui.makeNode("div", "div", {
      css: "ui clearing divider",
    });

    ui.makeNode("body", "div", {
      text: email.message,
    });

    ui.makeNode("comments", "div", { css: "ui grid" }).makeNode("c", "div", {
      css: "fourteen wide centered column",
    });

    ui.patch();

    // comments
    sb.notify({
      type: "show-note-list-box",
      data: {
        domObj: ui.comments.c,
        objectIds: [email.id],
        objectId: email.id,
        collapse: "open",
      },
    });
  }

  function showCollection(dom, state, draw) {
    var setup = {
      actions: {
        create: false,
        view: true,
        copy: false,
        archive: false,
        resend: {
          name: "Resend",
          title: "Resend",
          icon: "share",
          domType: "none",
          action: function (email, customView, onComplete) {
            // Confirm with the user
            alerts.ask(
              {
                title: "Resend the email?",
                html: "This will go to " + email.to + ".",
              },
              function (response) {
                if (response) {
                  $("#loader").fadeIn();

                  // Resend the email
                  sb.data.db.service(
                    "EmailService",
                    "resendEmail",
                    { emailId: email.id },
                    function (response) {
                      $("#loader").fadeOut();

                      // Clear the cache and refresh the
                      // collection of emails.
                      sb.data.db.clearCache();
                      onComplete(true);
                    }
                  );
                }
              }
            );
          },
          headerAction: false,
        },
      },
      templates: false, // !Temporary, until template form is fixed.
      domObj: dom,
      fields: {
        to: {
          title: "To",
          type: "text",
          name: "To",
        },
        subject: {
          title: "Subject",
          type: "title",
          name: "Subject",
        },
        date_created: {
          title: "Sent On",
          type: "date",
          name: "sent on",
        },
        tags: {
          view: function () {
            return false;
          },
        },
      },
      singleView: {
        useCache: true,
        view: function (dom, email, onDraw) {
          singleEmailView(dom, email, onDraw);
        },
      },
      fullView: {
        type: "object-view",
        id: "email-obj",
      },
      menu: false,
      modalSize: "large",
      objectType: "emails",
      parseData: function (data, onComplete) {
        _.each(data.data, function (email) {
          email.name = email.subject;
        });

        onComplete(data);
      },
      selectedView: "table",
      selectedMobileView: "table",
      state: state,
      subviews: {
        table: {
          range: {
            defaultTo: "all_time",
          },
        },
        list: {
          backlog: true,
          range: {
            defaultTo: "all_time",
          },
        },
      },
      where: {
        childObjs: {
          subject: true,
          to: true,
          message: true,
          type_id: true,
          date_created: true,
        },
      },
    };

    if (state.hasOwnProperty("id")) {
      //setup.where.type_id = state.id;

      if (state.hasOwnProperty("pageObject")) {
        if (state.pageObject.hasOwnProperty("group_type")) {
          if (state.pageObject.group_type === "Headquarters") {
            delete setup.where.tagged_with;
          } else {
            setup.where.tagged_with = [state.id];
          }
        } else {
          setup.where.tagged_with = [state.id];
        }
      } else {
        setup.where.tagged_with = [state.id];
      }
    } else if (state.hasOwnProperty("entity")) {
      //setup.where.type_id = state.entity;

      setup.where.tagged_with = [state.entity];
    }

    /*
if (
			state.project 
			&& state.project.id
		) {
			setup.where.type_id = state.project.id;
		}
*/

    sb.notify({
      type: "show-collection",
      data: setup,
    });
  }

  return {
    init: function () {
      sb.listen({
        //'start-email-component':this.start,
        "show-compose-form": this.composeForm,
        "emails-comp-run": this.run,
        emailsRun: this.run,
      });

      sb.notify({
        type: "register-tool",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "emails",
            title: "Emails",
            icon: '<i class="fa fa-envelope"></i>',
            views: [
              // Project outbox tool
              {
                id: "outbox",
                name: "Outbox",
                tip: "Organize outgoing emails",
                icon: {
                  type: "envelope",
                  color: "blue",
                },
                default: true,
                settings: false,
                availableToEntities: true,
                mainViews: [
                  {
                    dom: showCollection,
                  },
                ],
                layers: ["hq", "team", "project", "object"],
                boxViews: [],
              },
              // obj view
              {
                id: "emails-obj",
                type: "object-view",
                title: "Email",
                icon: {
                  type: "envelope",
                  color: "blue",
                },
                dom: function (ui, state, onDraw) {
                  ui.makeNode("c", "div", { style: "padding:48px;" });
                  ui.patch();

                  singleEmailView(ui.c, state.pageObject, onDraw);
                },
                select: {
                  subject: true,
                  date_created: true,
                  to: true,
                  message: true,
                },
              },
            ],
          },
        },
      });
    },

    destroy: function () {
      sb.listen({
        "start-email-component": this.start,
      });

      _.each(comps, function (comp) {
        comp.destroy();
      });

      ui = {};
      comps = {};
    },

    start: function (setup) {
      /*
			sb.listen({
				'emails-comp-run':this.run
			});
*/

      ui = sb.dom.make(setup.domObj.selector);
      if (setup.hasOwnProperty("objectType")) {
        object_type = setup.objectType;
      }
      if (setup.hasOwnProperty("objectId")) {
        object_id = setup.objectId;
      }
      if (setup.hasOwnProperty("folder")) {
        state.folder = setup.folder;
      }
      if (setup.hasOwnProperty("defaultTo")) {
        defaultTo = setup.defaultTo;
      }
      if (setup.hasOwnProperty("state")) {
        state.thread_id = parseInt(setup.state.threadId);
      }
      if (setup.hasOwnProperty("collapse")) {
        viewSettings.collapse = setup.collapse;
      }

      if (
        viewSettings.collapse == true ||
        viewSettings.collapse == "open" ||
        viewSettings.collapse == "closed"
      ) {
        ui.makeNode("main", "container", {
          collapse: viewSettings.collapse,
          title: "Inbox",
        });
        ui.main.makeNode("c", "container", {});
        ui.build();

        ui.main.c.setup = setup_ui;
        ui.main.c.setup();
      } else {
        ui.setup = setup_ui;
        ui.setup();
      }
    },

    run: function (data) {
      data.run(data);
    },

    composeForm: function (setup) {
      /*
			sb.listen({
				'emails-comp-run':this.run
			});
*/

      object_id = setup.objectId;
      object_type = setup.objectType;
      if (setup.hasOwnProperty("action")) {
        sentCallback = setup.action;
      }

      var ui = sb.dom.make(setup.domObj.selector);
      ui.makeNode("form", "container", {});
      ui.build();

      composeForm({}, ui.form, undefined, undefined, undefined, setup.email);
    },
  };
});
