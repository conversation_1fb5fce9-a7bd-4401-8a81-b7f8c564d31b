Factory.register('trash-bin', function(sb) {
	
	var comps = {};
	
	function home_view(dom, state, draw) {
		
		var options = _.sortBy([{
				name:'Contact',
				value:'contacts'
			}, {
				name:'Workorder',
				value:'work_orders'
			}, {
				name:'Staff',
				value:'staff'
			}, {
				name:'Inventory stock item',
				value:'inventory_groups'
			}, {
				name:'Inventory stock item shipment',
				value:'inventory_items'
			}, {
				name:'Inventory recipe',
				value:'inventory_recipe'
			}, {
				name:'Inventory stock category',
				value:'inventory_categories'
			}, {
				name:'Inventory recipe category',
				value:'inventory_recipe_categories'
			}, {
				name:'Inventory product',
				value:'inventory_billable_groups'
			}, {
				name:'Inventory product category',
				value:'inventory_billable_groups'
			}, {
				name:'Inventory package',
				value:'inventory_billable_combinations'
			}, {
				name:'Inventory package category',
				value:'inventory_billable_combination_categories'
			}, {
				name:'Clients',
				value:'companies'
			}, {
				name:'Messages',
				value:'document'
			}], function(o){ return o.name; });
		
		dom.empty();
		
		dom.makeNode('wrapper', 'div', {});
		
		dom.wrapper.makeNode('head', 'div', {});
		dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 2});
		dom.wrapper.makeNode('body', 'div', {});
		
		dom.wrapper.head.makeNode('title', 'div', {text: '<h2><i class="trash icon"></i> Trash Bin', css: 'ui header'});
					
		var formObj = {
				bps: {
					name: 'bps',
					type: 'select',
					label: 'Select a record type',
					options: options
				}
			};
			
		function process_form(form, load, after) {
			
			var formData = form.process().fields.bps.value;
			
			load();

			sb.data.db.obj.getBlueprint(formData, function(bp) {
					
				after(bp, formData);
				
			});
			
		}
		
		function build_blueprintTable(bp, name, dom, state, draw) {
			
			var visibleCols = {};
			var recordTypeTitle = _.findWhere(options, {value:name}).name;
			
			_.each(bp, function(val, key) {
		
				visibleCols = _.extend(visibleCols, {[key]: val.name});
				
			});

			dom.empty();
			
			dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
			
			dom.wrapper.makeNode('head', 'div', {});
			dom.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
			dom.wrapper.makeNode('message', 'div', {css: 'ui message'})
				.makeNode('msg', 'div', {text:'To restore data, select the records you wish to restore and click "Restore"'});
			dom.wrapper.makeNode('body', 'div', {});
			dom.wrapper.makeNode('create', 'div', {});
			
			dom.wrapper.head.makeNode('title', 'div', {text: '<h2>Viewing all '+ recordTypeTitle +'(s) in the trash bin.</h2>'});
			
			draw({
				dom: dom,
				after: function(dom) {
					
					dom.patch();
					
					comps.dataCRUD.notify({
						type: 'show-table',
						data: {
							domObj: dom.wrapper.body,
							objectType: name,
							childObjs: 1,
							visibleCols: visibleCols,
							cells: {},
							searchObjects: false,
							rowLink: false,
							rowSelection: true,
							multiSelectButtons: {
								restore: {
									name: '<i class="recycle icon"></i> Restore',
									css: 'teal',
									domType: 'none',
									action: function(list) {
										
										var ids = _.pluck(list, 'id');
										
										sb.data.db.obj.restore(name, ids, function(restored) {
											
											if(restored) {
												
												comps.dataCRUD.notify({
													type: 'update-table',
													data: {}
												});
												
											}
											
										});
										
									}
								}
							},
							headerButtons: {
								reload: {
									name: 'Reload',
									css: 'pda-btn-blue',
									action: function() {}
								}	
							},
							data: function(page, callback) {
								
								sb.data.db.obj.getWhere(name, {
									paged:page,
									childObjs:1,
									is_deleted:true
								}, function(ret) {
									
									callback(ret);
									
								});
								
							}
						}
					});
					
				}
			});				
			
		}
		
		dom.wrapper.body.empty();
		
		dom.wrapper.body.makeNode('grid', 'div', {css: 'ui equal width grid'});
		
		dom.wrapper.body.grid.makeNode('col1', 'div', {css: 'column'});
		dom.wrapper.body.grid.makeNode('col2', 'div', {css: 'column'});
		dom.wrapper.body.grid.makeNode('col3', 'div', {css: 'column'});
		
		dom.wrapper.body.grid.col2.makeNode('form', 'form', formObj);
		
		dom.wrapper.body.grid.col2.makeNode('lb_1', 'lineBreak', {spaces: 1});
		
		dom.wrapper.body.grid.col2.makeNode('btnGrp', 'div', {});
		
		dom.wrapper.body.grid.col2.btnGrp.makeNode('next', 'div', {css: 'ui green button right floated', text: 'Next'}).notify('click', {
			type: 'trash-bin-run',
			data: {
				run: function(data) {
					
					process_form(dom.wrapper.body.grid.col2.form, function() {
						
						dom.wrapper.body.empty();
						
						dom.wrapper.body.makeNode('load_cont', 'div', {});
						dom.wrapper.body.load_cont.makeNode('loader', 'loader', {});
						dom.wrapper.body.load_cont.makeNode('load_text', 'div', {text: 'Fetching data ...', css: 'text-center'});
						
						dom.wrapper.body.patch();
						
					}, function(bp, value) {
						
						if(bp && _.isObject(bp)) {
							
							var recordTypeTitle = _.findWhere(options, {value:value}).name;
							
							sb.notify({
								type: 'app-navigate-to',
								data: {
									itemId: 'trash-bin',
									viewId: {
										id: 'single-' + value,
										title: recordTypeTitle,
										icon: '<i class="trash icon"></i>',
										removable: true,
										dom: build_blueprintTable.bind(this, bp, value)
									}
								}
							});
							
						} else {
							
							sb.dom.alerts.alert(
								'This data does not exist',
								'Can not find data',
								'warning'
							);
							
							home_view(dom, state, draw);
							
						}
						
					});
					
				}
			}
		}, sb.moduleId);
		
		dom.wrapper.patch();
		
		draw(false);
		dom.patch();
		
	}
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: sb.moduleId,
						title: 'Trash Bin',
						icon: '<i class="fa fa-trash"></i>',
						views: [
							{
								id: 'home-view',
								default: true,
								type: 'custom',
								title: 'Trash bin',
								icon: '<i class="fa fa-trash"></i>',
								dom: home_view
							}
						]
					}
				}
			});
			
			sb.listen({
				'trash-bin-run': this.run
			});
			
			comps.dataCRUD = sb.createComponent('crud-table');
			
		},
		
		destroy: function() {
		
			_.each(comps, function(v) {
				v.destroy();
			}, this)
			
			comps = {};
			
		},
		
		run: function(data) {
			data.run(data);
		}
		
	}
	
});