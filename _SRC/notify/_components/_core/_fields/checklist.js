var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('checklist-field', function (sb) {
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			_.each(obj[fieldName], function (v, i) {

				if (i > 0) {
					ret += ', ';
				}
				if (
					options
					&& options.options
					&& _.findWhere(options.options, {value:v})
				) {

					ret += _.findWhere(options.options, {value:v}).name;

				}
				
			});

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}
		
		var fieldOptions = {
			single: false
		};
		var fieldConfig = false;
		if (
			options.blueprint[fieldName]
			&& options.blueprint[fieldName].options
		) {
			fieldOptions = options.blueprint[fieldName].options;
		}

		if (obj && typeof obj.object_bp_type === 'string') {

			var set = _.findWhere(
				appConfig.Types
				, {
					bp_name: obj.object_bp_type.substring(1)
				}
			);
			if (set && set.blueprint && set.blueprint[fieldName]) {
				fieldConfig = set.blueprint[fieldName]
			}

		}

		function editOption (optionToUpdate, archive) {

			sb.notify ({
				type: 'show-edit-option-modal',
				data: {
					ui: ui,
					obj: obj,
					option: optionToUpdate,
					options: options,
					fieldName: fieldName,
					view: View
				}
			});
			
		}
		
		function getNarrativeOptions (options, fieldName) {
			if(options && options.blueprint && options.blueprint[fieldName] && options.blueprint[fieldName].options && options.blueprint[fieldName].options.relatedNarrativeOptions){

				let optionsNew = options.blueprint[fieldName].options.relatedNarrativeOptions.split('\r\n');

				optionsNew = _.map(optionsNew, function(item){
					item = item.replaceAll("^", "\r\n");
					return item;
				});

				return optionsNew;
			}
			return false;
		}

		function viewOption (ui, option) {
			
			function getCheckboxHtml (val) {
				
				if (fieldOptions.single) {
					
					switch (
						_.contains(
							obj[fieldName]
							, parseInt(val)
						)
						|| (
							options.allowStringVals
							&& _.contains(
								obj[fieldName]
								, val
							)
						)
					) {
						
						case true:
							return '<i class="large circle check icon bento-checked"></i> '+ option.name;
						
						default:
							return '<i class="large circle outline icon bento-unchecked"></i> '+ option.name;
						
					}
					
				} else {
					
					switch (
						_.contains(
							obj[fieldName]
							, parseInt(val)
						)
						|| (
							options.allowStringVals
							&& _.contains(
								obj[fieldName]
								, val
							)
						)
					) {
						
						case true:
							return '<i class="large square check icon bento-checked"></i> '+ option.name;
						
						default:
							return '<i class="large square outline icon bento-unchecked"></i> '+ option.name;
						
					}
					
				}
				
			}
			
			ui.makeNode(
				'i'+ option.value
				, 'div'
				, {
					css: 'item'
					, data: {
						value: option.value
					}
				}
			).makeNode(
				'c'
				, 'div'
				, {
					text: 		getCheckboxHtml(option.value)
					, style: 	'display:inline-block;'
				}
			);
			
			if (options.edit) {
				
				ui['i'+ option.value].listeners.push(
					
					function (selector) {
						
						$(selector).on('click', function () {
							
							var val = $(selector).attr('value');
							if (fieldOptions.single) {
								
								var previous = false;
								
								// Check for previous selection
								if (obj[fieldName][0]) {
									
									previous = _.find(
										selectOptions
										, function (opt) {
											return parseInt(opt.value) === obj[fieldName][0];
										}
									);
									console.log('pre', previous);
									
									
								}
								
								obj[fieldName] = [parseInt(val)];
								
								// Uncheck previous selection
								if (previous) {
									
									viewOption(ui, previous);
									ui['i'+ previous.value].patch();
									
								}
								
							} else {

								// toggle value
								if (
									_.contains(
										obj[fieldName]
										, parseInt(val)
									)
									|| (
										options.allowStringVals
										&& _.contains(
											obj[fieldName]
											, val
										)
									)
								) {
									
									obj[fieldName] = _.without(
										obj[fieldName]
										, parseInt(val)
									);
									obj[fieldName] = _.without(
										obj[fieldName]
										, val
									);
									
								} else if (options.allowStringVals === true) {

									obj[fieldName].push(
										val
									);

								} else {
									
									obj[fieldName].push(
										parseInt(val)
									);
									
								}
								
								obj[fieldName] = _.uniq(obj[fieldName]);
								
							}

							if(val == 123456789 && _.contains(obj[fieldName], parseInt(val))){
								
								var currentOtherValue = obj[fieldName+'_other'];
								if(!currentOtherValue){
									currentOtherValue = '';
								}
								
								ui.makeNode(
									'otherOption'
									, 'div'
									, {
										css: 'item'
										, data: {
											value: 'other'
										}
									}
								).makeNode(
									'other'
									, 'div'
									, {
										css: 		'ui input focus',
										text: 		'<input id="'+ fieldName +'_other" type="text" value="'+ currentOtherValue +'" placeholder="Type here">'
									}
								);
																
								ui.patch();
								
								obj[fieldName+'_other'] = '';
								
								$('#'+fieldName +'_other').focusout(function(e){
									
									obj[fieldName+'_other'] = $('#'+fieldName +'_other').val();
									
									function getNarrativeOptions (options, fieldName) {
										if(options && options.blueprint && options.blueprint[fieldName] && options.blueprint[fieldName].options && options.blueprint[fieldName].options.relatedNarrativeOptions){
											let optionsNew = options.blueprint[fieldName].options.relatedNarrativeOptions.split('\r\n');

											optionsNew = _.map(optionsNew, function(item){
												item = item.replaceAll("^", "\r\n");
												return item;
											});

											return optionsNew;

										}
										return false;
									}

									if (options.commitUpdates) {
										
										var field = {[fieldName+'_other']: 	obj[fieldName+'_other']};
										if(options.relatedNarrativeField){
											var narrativeOptions = getNarrativeOptions(options, fieldName);

											if(narrativeOptions) {
												var newValue = [];

												var selecteds = obj[fieldName];

												if(selecteds.length > 0) {
													_.map(selecteds, function (item) {
														newValue.push(narrativeOptions[item - 1]);
													});
												}

												if(newValue.length > 0){
													newValue = newValue.join(',');
													field = Object.assign(field, {[options.relatedNarrativeField] : newValue});
												}

											}
										}

										sb.data.db.obj.update(
											obj.object_bp_type
											, {
												id: 				obj.id
												, ...field
											}
											, function (r) {
												
												if (
													!_.isEmpty(options)
													&&  typeof options.onUpdate === 'function'
												) {
		
													options.onUpdate(obj[fieldName]);
		
												}
		
												ui.empty();
												View(
													fieldName+'_other'
													, ui
													, obj
													, options
												);
												ui.patch();

												if(options.blueprint) {

													//update narrative field
													_.each(options.blueprint, function (field, key) {
														if(key === options.relatedNarrativeField) {
															sb.notify({
																type: 'get-field-id'
																, data: {
																	obj: {
																		id: obj.id
																		, [key]: r[key]
																	}
																	, type: field.fieldType
																	, property: key
																	, callback: function (fieldId) {
																		if(fieldId && fieldId[0]) {
																			$('#' + fieldId[0]._id + ' .ql-editor').html(r[key]);
																		}
																	}
																}
															});
														}
													});
												}

											}
										);
										
									}
									
								});
								
							}
							
							if(val == 123456789 && !_.contains(obj[fieldName], parseInt(val))){
								
								delete ui.otherOption;
								ui.patch();
								
							}

							viewOption(ui, option);
							ui['i'+ option.value].patch();
							
							if (options.commitUpdates) {

								// If an on update action is set, then show a loader
								// until a response is received, to keep race 
								// conditions from being introduced.
								if (
									!_.isEmpty(fieldConfig)
									&& !_.isEmpty(fieldConfig.on)
									&& !_.isEmpty(fieldConfig.on.update)
								) {

									// Show loader
									$("#loader").fadeIn();

								}
								

								var field = {[fieldName]: 	obj[fieldName]};
								if(options.relatedNarrativeField){
									var narrativeOptions = getNarrativeOptions(options, fieldName);

									if(narrativeOptions) {
										var newValue = [];

										var selecteds = obj[fieldName];

										if(selecteds.length > 0) {
											_.map(selecteds, function (item) {
												newValue.push(narrativeOptions[item - 1]);
											});
										}

										if(newValue.length > 0){
											newValue = newValue.join(',');
											field = Object.assign(field, {[options.relatedNarrativeField] : newValue});
										}

									}
								}

								sb.data.db.obj.update(
									obj.object_bp_type
									, {
										id: 				obj.id
										, ...field
									}
									, function (r) {
										
										if (
											!_.isEmpty(options)
											&&  typeof options.onUpdate === 'function'
										) {

											options.onUpdate(obj[fieldName]);
										}

										ui.empty();
										View(
											fieldName
											, ui
											, obj
											, options
										);
										ui.patch();

										if(options.blueprint) {

											//update narrative field
											_.each(options.blueprint, function (field, key) {
												if(key === options.relatedNarrativeField) {
													sb.notify({
														type: 'get-field-id'
														, data: {
															obj: {
																id: obj.id
																, [key]: r[key]
															}
															, type: field.fieldType
															, property: key
															, callback: function (fieldId) {
																if(fieldId && fieldId[0]) {
																	$('#' + fieldId[0]._id + ' .ql-editor').html(r[key]);
																}
															}
														}
													});
												}
											});
										}

										// If an on update action is set, then show a loader
										// until a response is received, to keep race 
										// conditions from being introduced.
										if (
											!_.isEmpty(fieldConfig)
											&& !_.isEmpty(fieldConfig.on)
											&& !_.isEmpty(fieldConfig.on.update)
										) {

											// Show loader
											$("#loader").fadeOut();

										}

									}
								);
								
							}
							
						});
						
					}
					
				);
				
				if (options.editBlueprint) {
				
					ui['i'+ option.value]
						.makeNode('rm', 'div', {
							tag: 'i'
							, css: 'grey remove icon'
							, style: 'display:inline-block;padding:8px;'
						}).listeners.push(
							function (selector) {
								
								$(selector)
									.on('click', function (e) {
										
										e.stopPropagation();
										
										sb.notify ({
											type: 'remove-option',
											data: {
												ui: ui,
												obj: obj,
												option: option.value,
												options: options,
												fieldName: fieldName,
												view: View
											}
										});
										
									});

								}
							);
					
					ui['i'+ option.value]
						.makeNode('edit', 'div', {
							tag: 'i'
							, css: 'grey pencil icon'
							, style: 'display:inline-block;padding:8px;'
						}).listeners.push(
							function (selector) {
								
								var opt = this;
								$(selector)
									.on('click', function (e) {
										
										e.stopPropagation();
										editOption(opt.value, false);
									
									});
								
							}.bind(option)
						);
						
				}
				
			}
			
		}
		
		var selectedVal = false;
		if (!Array.isArray(obj[fieldName])) {
			obj[fieldName] = [];
		}
		if (obj[fieldName]) {
			
			selectedVal = _.findWhere(
				options.blueprint[fieldName].options.options
				, {
					value: obj[fieldName].toString()
				}
			);
			
		}
		
		if (
			selectedVal
			&& selectedVal.is_archived
		) {
			selectedVal = false;
		}
		
		var currentVal = '';
		var selectOptions = options.blueprint[fieldName].options.options || [];
		
		ui.empty();
		ui.makeNode('list', 'div', {
			style: 'padding:8px;'
		});

		if(fieldOptions){

			if(fieldOptions.other == true){
				selectOptions.push({
					name:'Other',
					value:123456789
				});
			}
		}
		
		_.each(selectOptions, function (opt) {
			
			if (!opt.is_archived) {
				viewOption(ui.list, opt);
			}
			
		});
		
		if(options.other){

			if(_.contains(obj[fieldName], 123456789)){
				
				var currentOtherValue = obj[fieldName+'_other'];
				if(!currentOtherValue){
					currentOtherValue = '';
				}
				
				ui.makeNode(
					'otherOption'
					, 'div'
					, {
						css: 'item'
						, data: {
							value: 'other'
						}
					}
				).makeNode(
					'other'
					, 'div'
					, {
						css: 		'ui input focus',
						text: 		'<input id="'+ fieldName +'_other" type="text" value="'+ currentOtherValue +'" placeholder="Type here">'
					}
				);
												
				ui.patch();

				setTimeout(function(){
					
					$('#'+fieldName +'_other').focusout(function(e){
						
						obj[fieldName+'_other'] = $('#'+fieldName +'_other').val();

						if (options.commitUpdates) {
							var field = {[fieldName+'_other']: 	obj[fieldName+'_other']};
							if(options.relatedNarrativeField){
								var narrativeOptions = getNarrativeOptions(options, fieldName);

								if(narrativeOptions) {
									var newValue = [];

									var selecteds = obj[fieldName];

									if(selecteds.length > 0) {
										_.map(selecteds, function (item) {
											newValue.push(narrativeOptions[item - 1]);
										});
									}

									if(newValue.length > 0){
										newValue = newValue.join(',');
										field = Object.assign(field, {[options.relatedNarrativeField] : newValue});
									}

								}
							}

							sb.data.db.obj.update(
								obj.object_bp_type
								, {
									id: 				obj.id
									, ...field
								}
								, function (r) {
									
									if (
										!_.isEmpty(options)
										&&  typeof options.onUpdate === 'function'
									) {
	
										options.onUpdate(obj[fieldName]);
	
									}

								}
								, {
									[fieldName]: true
								}
							);
							
						}
						
					});
					
				}, 500)
				
			}
			
		}
		
		// New option
		if (options.editBlueprint) {
			
			ui.list.makeNode('new', 'div', {
				css: 'item'
				, dataAttr: [
					{
						name: 'value'
						, value: 'new'
					}
				]
			}).makeNode('txt', 'div', {
				css: 'text'
				, tag: 'span'
				, text: '<i class="grey plus icon" style="padding:8px;"></i>'
			}).listeners.push(
				function (selector) {
					
					$(selector).on('click', function (e) {
						
						editOption();
						
					});
					
				}
			);
			
		}
		
		return;
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 					'checklist'
					, view: 					View
					, title: 				'Checklist'
					, availableToEntities: 	true
					, icon: 					'list alternate'
					, propertyType: 			'ints'
					, options: {
						single: {
							name: 	'Only allow one selection?'
							, type: 	'bool'
						},
						other: {
							name: 'Show \'Other\' option?',
							type: 'bool'
						},
						output: {
							name: 	'Export mask?'
							, type: 	'text'
						},
						relatedNarrativeField: {
							name: 'Related Narrative Field',
							type: 'field',
							allowedTypes: ['detail'],
							multi: false
						},
						relatedNarrativeOptions: {
							name: 	'Narrative Options',
							type: 'text'
						},
					}
				}
			});
			
		}
		
	}
	
});
