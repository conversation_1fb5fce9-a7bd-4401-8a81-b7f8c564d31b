var IN_NODE_ENV = typeof module !== "undefined" && module.exports;
if (IN_NODE_ENV) {
  // Get dependencies
  var Factory = require("../_core.js").Factory;
  var _ = require("underscore-node");

  var appConfig = {
    instance: global.dbAuth.dbPagodaAPIKey,
  };
}

Factory.register("stripe-payment-source-field", function (sb) {
  if (IN_NODE_ENV) {
    return;
  }

  // variables
  var stripeKey = STRIPE_PK;
  var plaidKey = "98d4fb666a3da81c09dd167dd48f0f";
  var plaidEnv = "development";
  var STRIPE_PAYMENT_DECLINED_MESSAGE = "The payment method provided is unable to be authorized. Please use an alternate form of payment.";

  function View(fieldName, ui, obj, options) {
    // variables
    var stripeId = obj.customer.id;
    var contactId = options.contactId;
    var initiatePayments = options.initiatePayments;
    var feesList = options.feesList;
    var invoiceBalance = options.invoiceBalance;
    var paymentForm = options.paymentForm;
    var instanceId = options.instanceId;
    var eventId = options.eventId;
    var selectedInvoices = options.selectedInvoices;
    var selectedInvoiceIds = options.selectedInvoiceIds;

    if (!paymentForm) {
      ui.makeNode("buttons", "div", { css: "" });
      ui.buttons
        .makeNode("createCC", "button", {
          css: "pda-btnOutline-green",
          text: '<i class="fa fa-plus"></i> New Credit/Debit Card',
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: addCCtoStripeCustomer.bind(ui, contactId, stripeId, options),
            },
          },
          sb.moduleId
        );
      ui.buttons
        .makeNode("createACH", "button", {
          css: "pda-btnOutline-green",
          text: '<i class="fa fa-plus"></i> New ACH Account',
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: initiateACHVerification.bind(
                ui,
                contactId,
                stripeId,
                options
              ),
            },
          },
          sb.moduleId
        );
      ui.makeNode("create", "div", { css: "" });
      ui.makeNode("lb_1", "lineBreak", { spaces: 1 });
    } else if (paymentForm) {
      ui.makeNode("buttons", "div", { css: "" });
      ui.makeNode("create", "div", { css: "" });
    }

    ui.makeNode("paymentSources", "div", { css: "ui cards" });

    if (
      obj.customer.sources != null &&
      Object.keys(obj.customer.sources).length > 0
    ) {
      _.each(obj.customer.sources, function (source) {
        // variables
        var isCard = source.object == "card" ? true : false;
        var isBA = source.object == "bank_account" ? true : false;
        var isDefault = obj.customer.default_source == source.id;
        var defaultSource = isDefault ? "&emsp;Default Source" : "";
        var brand = "";

        // credit card payment source:
        if (isCard) {
          switch (source.brand) {
            case "Visa":
              brand = '<i class="fab fa-cc-visa"></i>';
              break;
            case "MasterCard":
              brand = '<i class="fab fa-cc-mastercard"></i>';
              break;
            case "American Express":
              brand = '<i class="fab fa-cc-amex"></i>';
              break;
            case "JCB":
              brand = '<i class="fab fa-cc-jcb"></i>';
              break;
            case "Diners Club":
              brand = '<i class="fab fa-cc-diners-club"></i>';
              break;
            case "Discover":
              brand = '<i class="fab fa-cc-discover"></i>';
              break;
            default:
              brand = '<i class="fas fa-credit-card"></i>';
          }

          ui.paymentSources.makeNode("source-" + source.id, "div", {
            css: "ui card",
          });
          ui.paymentSources["source-" + source.id].makeNode("details", "div", {
            css: "content",
          });
          ui.paymentSources["source-" + source.id].details.makeNode(
            "brand",
            "div",
            { text: brand + defaultSource, css: "ui right", size: "x-small" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "cardholder",
            "div",
            { text: source.brand, css: "", size: "xx-small" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "card",
            "div",
            {
              text: "Ending in:&emsp;" + source.last4,
              css: "",
              size: "x-small",
            }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "exp",
            "div",
            {
              text: "Exp:&emsp;" + source.exp_month + "/" + source.exp_year,
              css: "text-right",
              size: "xx-small",
            }
          );
          ui.paymentSources["source-" + source.id].makeNode("btns", "div", {
            css: "ui bottom attached mini buttons",
          });
        } else if (isBA) {
          brand = '<i class="fas fa-university"></i>';

          ui.paymentSources.makeNode("source-" + source.id, "div", {
            css: "ui card",
          });
          ui.paymentSources["source-" + source.id].makeNode("details", "div", {
            css: "content",
          });
          ui.paymentSources["source-" + source.id].details.makeNode(
            "brand",
            "div",
            { text: brand + defaultSource, css: "", size: "x-small" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "cardholder",
            "div",
            { text: source.bank_name, css: "", size: "xx-small" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "card",
            "div",
            {
              text: "Ending in:&emsp;" + source.last4,
              css: "",
              size: "x-small",
            }
          );
          ui.paymentSources["source-" + source.id].makeNode("btns", "div", {
            css: "ui bottom attached mini buttons",
          });
        }

        if (!paymentForm) {
          ui.paymentSources["source-" + source.id].btns
            .makeNode("delete", "button", {
              text: '<i class="fas fa-trash"></i>',
              css: "pda-btn-red",
            })
            .notify(
              "click",
              {
                type: "paymentMethodRun",
                data: {
                  run: deleteSource.bind(
                    ui,
                    source.id,
                    contactId,
                    stripeId,
                    options
                  ),
                },
              },
              sb.moduleId
            );

          if (!isDefault) {
            ui.paymentSources["source-" + source.id].btns
              .makeNode("default", "button", {
                text: "Make Default",
                css: "pda-btn-primary",
              })
              .notify(
                "click",
                {
                  type: "paymentMethodRun",
                  data: {
                    run: makeDefault.bind(
                      ui,
                      source.id,
                      contactId,
                      stripeId,
                      options
                    ),
                  },
                },
                sb.moduleId
              );
          }

          if (initiatePayments) {
            ui.paymentSources["source-" + source.id].btns
              .makeNode("takePayment", "button", {
                text: "Use Source",
                css: "pda-btn-teal",
              })
              .notify(
                "click",
                {
                  type: "paymentMethodRun",
                  data: {
                    run: getPaymentSource.bind(
                      ui,
                      source.id,
                      stripeId,
                      options,
                      obj.customer
                    ),
                  },
                },
                sb.moduleId
              );
          }
        } else if (paymentForm) {
          var fees = feesList[0];
          var txFeePercent = 0;
          var txFeeFlat = 0;
          if (fees) {
            if (fees && source.object == "card") {
              txFeePercent = fees.credit_card_percent;
              txFeeFlat = fees.credit_card_flat_fee;
            } else {
              txFeePercent = fees.ach_percent;
              txFeeFlat = fees.ach_flat_fee;
            }
          } else {
            if (source.object == "card") {
              txFeePercent = 2.9;
              txFeeFlat = 0.3;
            }
          }

          ui.paymentSources["source-" + source.id].details.makeNode(
            "form",
            "form",
            {
              amount: {
                name: "amount",
                label: "Payment Amount",
                type: "usd",
                value: invoiceBalance,
                change: function (form, value) {
                  var newVal = +value.replace(/\D/g, "");
                  var feeDisplayText;

                  feeSchedule = calculatePaymentWithFees(
                    parseFloat(newVal / 100),
                    +txFeePercent,
                    txFeeFlat,
                    selectedInvoiceIds
                  );
                  feeDisplayText = feeSchedule.feeDisplayText;

                  if (
                    appConfig.instance == "infinity" ||
                    appConfig.instance == "nlp"
                  ) {
                    feeDisplayText = 3;
                  }

                  $(
                    ui.paymentSources["source-" + source.id].details.balance
                      .selector
                  ).text("Balance: $" + (newVal / 100).formatMoney());
                  $(
                    ui.paymentSources["source-" + source.id].details.fees
                      .selector
                  ).text(
                    "Processing Fee: $" +
                      (feeSchedule.fee / 100).formatMoney() +
                      " (" +
                      feeDisplayText +
                      "% + $" +
                      (+txFeeFlat * selectedInvoiceIds.length).formatMoney() +
                      ")"
                  );
                  $(
                    ui.paymentSources["source-" + source.id].details.total
                      .selector
                  ).html(
                    "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
                      (feeSchedule.total / 100).formatMoney() +
                      "</span>"
                  );
                },
              },
              // date:{
              //     name:'date',
              //     label:'Payment Date',
              //     type:'date',
              //     active: 'No',
              //     value:moment()
              // },
              email: {
                type: "text",
                name: "email",
                label: "One Time Email Address For Receipt",
              },
              notes: {
                name: "notes",
                label: "Notes",
                type: "textbox",
                rows: 5,
              },
            }
          );

          feeSchedule = calculatePaymentWithFees(
            parseFloat(invoiceBalance / 100),
            txFeePercent,
            txFeeFlat,
            selectedInvoiceIds
          );

          var feeDisplayText = feeSchedule.feeDisplayText;

          if (appConfig.instance == "infinity" || appConfig.instance == "nlp") {
            feeDisplayText = 3;
          }

          ui.paymentSources["source-" + source.id].details.makeNode(
            "balance",
            "div",
            { text: "Balance: $" + (invoiceBalance / 100).formatMoney() }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "fees",
            "div",
            {
              text:
                "Processing Fee: $" +
                (feeSchedule.fee / 100).formatMoney() +
                " (" +
                feeDisplayText +
                "% + $" +
                (+txFeeFlat * selectedInvoiceIds.length).formatMoney() +
                ")",
            }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "total",
            "div",
            {
              text:
                "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
                (feeSchedule.total / 100).formatMoney() +
                "</span>",
            }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "totalBreak",
            "div",
            { text: "<br />" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "btns",
            "div",
            { css: "ui bottom attached mini buttons" }
          );
          ui.paymentSources["source-" + source.id].details.btns
            .makeNode("takePayment", "button", {
              text: "Pay Now",
              css: "pda-btn-teal",
            })
            .notify(
              "click",
              {
                type: "paymentMethodRun",
                data: {
                  run: function () {
                    var paramObj = {
                      stripeId: stripeId,
                      sourceId: source.id,
                      contactId: contactId,
                      percentFee: txFeePercent,
                      flatFee: txFeeFlat,
                      instanceId: instanceId,
                      eventId: eventId,
                      sourceType: source.object,
                      selectedInvoices: selectedInvoices,
                      paymentTotal: feeSchedule.total,
                      fees: feeSchedule.fee,
                      optionalEmail:
                        ui.paymentSources[
                          "source-" + source.id
                        ].details.form.process().fields.email.value,
                    };

                    ui.paymentSources[
                      "source-" + source.id
                    ].details.btns.takePayment.loading(true);

                    sb.data.db.service(
                      "StripeService",
                      "chargeStripeConnectCustomer",
                      paramObj,
                      function (response) {
                        if (response.includes("Status 402")){
                          ui.makeNode("paymentSources", "div", {
                            css: "ui negative message",
                            text: `${STRIPE_PAYMENT_DECLINED_MESSAGE}`,
                          });

                          ui.patch();
                        }
                        else if (!response.paymentIntent && !response.success) {
                          ui.makeNode("paymentSources", "div", {
                            css: "ui negative message",
                            text: "There was an error creating this payment with Stripe. Please contact your invoice provider for assistance.",
                          });

                          ui.patch();
                        } else {
                          ui.makeNode("paymentSources", "div", {
                            css: "ui positive message",
                            text: "Payment successfully created. Please check back later to see the status of the payment.",
                          });

                          ui.patch();
                        }
                      }
                    );
                  },
                },
              },
              sb.moduleId
            );
          ui.paymentSources["source-" + source.id].details.btns
            .makeNode("cancelPayment", "button", {
              text: "Cancel",
              css: "pda-btn-yellow",
            })
            .notify(
              "click",
              {
                type: "paymentMethodRun",
                data: {
                  run: function () {
                    var paramObj = {
                      stripeId: stripeId,
                      contactId: contactId,
                      instanceId: instanceId,
                      verifiedSources: true,
                    };

                    options.paymentForm = false;

                    ui.paymentSources[
                      "source-" + source.id
                    ].details.btns.cancelPayment.loading(true);

                    sb.data.db.service(
                      "StripeService",
                      "getStripeCustomer",
                      paramObj,
                      function (response) {
                        if (!response.customer) {
                          ui.makeNode("paymentSources", "div", {
                            css: "ui negative message",
                            text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                          });

                          ui.patch();
                        } else {
                          View(fieldName, ui, response, options);

                          ui.patch();
                        }
                      }
                    );
                  },
                },
              },
              sb.moduleId
            );
        }
      });
    } else {
      ui.makeNode("noItems", "headerText", {
        text:
          "No Stripe payment sources were found for " + obj.customer.name + ".",
        size: "xx-small",
        css: "text-center",
      });
    }
  }

  // api functions
  function deleteSource(sourceId, contactId, stripeId, options) {
    // variables
    var ui = this;
    var fieldName;
    var paramObj = {
      sourceId: sourceId,
      stripeId: stripeId,
      contactId: contactId,
      instanceId: options.instanceId,
      verifiedSources: true,
    };

    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "",
      },
      function (resp) {
        if (resp) {
          ui.paymentSources["source-" + sourceId].btns.delete.loading(true);

          sb.data.db.service(
            "StripeService",
            "deletePaymentSource",
            paramObj,
            function (response) {
              if (!response.response) {
                ui.makeNode("paymentSources", "div", {
                  css: "ui negative message",
                  text: "There was an error updating the default source. Please contact your invoice provider for assistance.",
                });

                ui.patch();
              } else {
                sb.data.db.service(
                  "StripeService",
                  "getStripeCustomer",
                  paramObj,
                  function (response) {
                    if (!response.customer) {
                      ui.makeNode("paymentSources", "div", {
                        css: "ui negative message",
                        text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                      });

                      ui.patch();
                    } else {
                      sb.dom.alerts.alert(
                        "The Card has been Removed!",
                        "",
                        "success"
                      );

                      View(fieldName, ui, response, options);

                      ui.patch();
                    }
                  }
                );
              }
            }
          );
        }
      }
    );
  }

  function makeDefault(sourceId, contactId, stripeId, options) {
    // variables
    var ui = this;
    var fieldName;
    var paramObj = {
      sourceId: sourceId,
      stripeId: stripeId,
      contactId: contactId,
      instanceId: options.instanceId,
      verifiedSources: true,
    };

    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "",
      },
      function (resp) {
        if (resp) {
          ui.paymentSources["source-" + sourceId].btns.default.loading(true);

          sb.data.db.service(
            "StripeService",
            "updateDefaultSource",
            paramObj,
            function (response) {
              if (!response.response) {
                ui.makeNode("paymentSources", "div", {
                  css: "ui negative message",
                  text: "There was an error updating the default source. Please contact your invoice provider for assistance.",
                });

                ui.patch();
              } else {
                sb.data.db.service(
                  "StripeService",
                  "getStripeCustomer",
                  paramObj,
                  function (response) {
                    if (!response.customer) {
                      ui.makeNode("paymentSources", "div", {
                        css: "ui negative message",
                        text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                      });

                      ui.patch();
                    } else {
                      sb.dom.alerts.alert("Success!", "", "success");

                      View(fieldName, ui, response, options);

                      ui.patch();
                    }
                  }
                );
              }
            }
          );
        }
      }
    );
  }

  function addCCtoStripeCustomer(contactId, stripeId, options) {
    // variables
    var stripe = Stripe(stripeKey);
    var ui = this;
    var fieldName;
    var paramObj = {
      stripeId: stripeId,
      contactId: contactId,
      verifiedSources: true,
      instanceId: options.instanceId,
    };

    ui.buttons.makeNode("lb_1", "lineBreak", { spaces: 1 });
    ui.create.makeNode("div", "div", { css: "ui raised clearing segment" });
    ui.create.div.makeNode("cont", "div", { css: "" });
    ui.create.div.makeNode("title", "div", {
      text: "Enter credit/debit card number",
      css: "ui medium header",
    });
    ui.create.div.makeNode("cardForm", "div", {
      css: "card-element",
      text: '<div id="card-element" class="ui basic segment"></div>',
    });
    ui.create.div.makeNode("formErrors", "text", {
      css: "card-errors",
      text: '<div id="card-errors" class="ui red text"></div>',
    });
    ui.create.div.makeNode("btns", "buttonGroup", { css: "" });
    ui.patch();

    var elements = stripe.elements();
    var style = {
      base: {
        fontSize: "16px",
        lineHeight: "24px",
      },
    };
    var card = elements.create("card", { style: style });
    card.mount("#card-element");
    card.addEventListener("change", function (event) {
      var displayError = document.getElementById("card-errors");
      if (event.error) {
        displayError.textContent = event.error.message;
      } else {
        displayError.textContent = "";
      }
    });

    ui.create.div.btns
      .makeNode("save", "button", {
        text: "Save",
        css: "mini compact basic green",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function () {
              ui.create.div.btns.save.loading(true);

              stripe.createToken(card).then(function (result) {
                var cardObj = {
                  ctokToken: result.token.id,
                  stripeCustomerId: stripeId,
                  cardLast4: result.token.card.last4,
                  cardExpYear: result.token.card.exp_year,
                  cardBrand: result.token.card.brand,
                  cardZip: result.token.card.address_zip,
                  instanceId: options.instanceId,
                };

                sb.data.db.service(
                  "StripeService",
                  "saveCreditCardToStripeCustomer",
                  cardObj,
                  function (response) {
                    if (!response.cardObj) {
                      ui.makeNode("paymentSources", "div", {
                        css: "ui negative message",
                        text: "There was an error adding this credit card. Please contact your invoice provider for assistance.",
                      });

                      ui.makeNode("create", "div", { css: "" });

                      ui.patch();
                    } else {
                      sb.data.db.service(
                        "StripeService",
                        "getStripeCustomer",
                        paramObj,
                        function (response) {
                          if (!response.customer) {
                            ui.makeNode("paymentSources", "div", {
                              css: "ui negative message",
                              text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                            });

                            ui.patch();
                          } else {
                            sb.dom.alerts.alert(
                              "Card has been successfully added!",
                              "",
                              "success"
                            );

                            delete ui.noItems;

                            View(fieldName, ui, response, options);

                            ui.patch();
                          }
                        }
                      );
                    }
                  }
                );
              });
            },
          },
        },
        sb.moduleId
      );
    ui.create.div.btns
      .makeNode("cancel", "button", {
        text: "Cancel",
        css: "mini compact basic grey",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function () {
              ui.create.div.btns.cancel.loading(true);

              sb.data.db.service(
                "StripeService",
                "getStripeCustomer",
                paramObj,
                function (response) {
                  if (!response.customer) {
                    ui.makeNode("paymentSources", "div", {
                      css: "ui negative message",
                      text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                    });

                    ui.patch();
                  } else {
                    View(fieldName, ui, response, options);

                    ui.patch();
                  }
                }
              );
            },
          },
        },
        sb.moduleId
      );

    ui.create.div.btns.patch();
  }

  function initiateACHVerification(contactId, stripeId, options) {
    // variables
    var stripe = Stripe(stripeKey);
    var ui = this;
    var fieldName;
    var paramObj = {
      stripeId: stripeId,
      contactId: contactId,
      verifiedSources: true,
      instanceId: options.instanceId,
    };
    var formArgs = {
      routing_number: {
        name: "routing_number",
        type: "text",
        label: "Routing Number",
      },
      account_number: {
        name: "account_number",
        type: "text",
        label: "Account Number",
      },
      account_holder_name: {
        name: "account_holder_name",
        type: "text",
        label: "Account Holder Name",
      },
      account_verification_email: {
        name: "account_verification_email",
        type: "text",
        label: "Email for Account Verification",
      },
      account_holder_type: {
        name: "account_holder_type",
        type: "select",
        label: "Account Holder Type",
        options: [
          {
            name: "Company",
            value: "company",
          },
          {
            name: "Individual",
            value: "individual",
          },
        ],
        value: "company",
      },
    };

    ui.makeNode("ask", "div", {
      text: "How will access your bank account?",
      css: "ui medium header",
    });

    ui.makeNode("btns", "buttonGroup", {});

    // ui.btns
    //   .makeNode("plaid", "button", {
    //     css: "mini compact basic green",
    //     text: "Log In To My Bank",
    //   })
    //   .notify(
    //     "click",
    //     {
    //       type: "paymentMethodRun",
    //       data: {
    //         run: function () {
    //           function startPlaid() {
    //             $.getScript(
    //               "https://cdn.plaid.com/link/v2/stable/link-initialize.js",
    //               function (data, textStatus, jqxhr) {
    //                 var linkHandler = Plaid.create({
    //                   env: plaidEnv,
    //                   clientName: appConfig.systemName,
    //                   key: plaidKey,
    //                   product: ["auth"],
    //                   selectAccount: true,
    //                   onSuccess: function (public_token, metadata) {
    //                     ui.btns.plaid.loading();

    //                     sb.data.db.service(
    //                       "StripeService",
    //                       "getStripeCustomer",
    //                       paramObj,
    //                       function (response) {
    //                         View(fieldName, ui, response, options);

    //                         ui.patch();
    //                       }
    //                     );
    //                   },
    //                   onExit: function (err, metadata) {
    //                     // The user exited the Link flow.
    //                     if (err != null) {
    //                       // The user encountered a Plaid API error prior to exiting.
    //                     }
    //                   },
    //                 });

    //                 linkHandler.open();
    //               }
    //             );
    //           }

    //           startPlaid();
    //         },
    //       },
    //     },
    //     sb.moduleId
    //   );

    if (appConfig.instance == "infinity" || appConfig.instance == "nlp") {
      ui.btns
        .makeNode("invoice", "button", {
          css: "mini compact basic green",
          text: "Receive a secure invoice via email",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: function () {
                ui.empty();

                ui.makeNode("emailtext", "div", {
                  css: "ui small header",
                  text: "We will send a secure invoice to your inbox for direct ACH processing. Please enter the email address you would like us to send the invoice to.",
                });

                ui.makeNode("form", "form", {
                  email: {
                    name: "email",
                    label: "Email Address",
                    type: "text",
                  },
                });

                ui.makeNode("break", "div", { text: "<br />" });

                ui.makeNode("send", "button", {
                  css: "mini compact basic green",
                  text: "Send Secure Invoice",
                }).notify(
                  "click",
                  {
                    type: "paymentMethodRun",
                    data: {
                      run: function () {
                        var emailAddress = ui.form.process().fields.email.value;

                        if (!emailAddress) {
                          sb.dom.alerts.alert("Enter an email address.");
                          return;
                        }

                        ui.send.loading();

                        sb.data.db.controller(
                          "getObjectById&api_webform=true&pagodaAPIKey=" +
                            appConfig.instance,
                          { value: options.contactId, type: "contacts" },
                          function (contact) {
                            var invoiceId = options.selectedInvoiceIds[0];

                            sb.data.db.controller(
                              "getObjectById&api_webform=true&pagodaAPIKey=" +
                                appConfig.instance,
                              {
                                value: options.selectedInvoiceIds[0],
                                type: "invoices",
                              },
                              function (invoice) {
                                sb.data.db.controller(
                                  "getObjectById&api_webform=true&pagodaAPIKey=" +
                                    appConfig.instance,
                                  { value: options.eventId, type: "proposals" },
                                  function (proposal) {
                                    sb.data.db.controller(
                                      "getObjectById&api_webform=true&pagodaAPIKey=" +
                                        appConfig.instance,
                                      {
                                        value: proposal.main_object,
                                        type: "groups",
                                      },
                                      function (project) {
                                        // var URL = sb.data.url.createPageURL( 'object-view', {
                                        //         id: 		project.id,
                                        //         name: 	project.name,
                                        //         type: 	'project'
                                        //     });

                                        var URL =
                                          "https://bento.infinityhospitality.net/app/infinity#hq&1=hqt-projectTool-Projects&2=o-project-" +
                                          project.id +
                                          "-" +
                                          project.name;

                                        if (appConfig.instance == "nlp") {
                                          var URL =
                                            "https://bento.infinityhospitality.net/app/nlp#hq&1=hqt-projectTool-Projects&2=o-project-" +
                                            project.id +
                                            "-" +
                                            project.name;
                                        }

                                        var emailBody =
                                          "<h2>Request for ACH Invoice</h2><br />" +
                                          "<b>Requested By:</b> " +
                                          contact.fname +
                                          " " +
                                          contact.lname +
                                          "<br />" +
                                          "<b>Send To:</b> " +
                                          emailAddress +
                                          "<br />" +
                                          "<b>Event:</b> " +
                                          project.name +
                                          "<br />" +
                                          "<b>Event #:</b> " +
                                          project.object_uid +
                                          "<br />" +
                                          "<b>Event Date:</b> " +
                                          project.start_date +
                                          "<br />" +
                                          "<b>Invoice #:</b> " +
                                          invoice.id +
                                          "<br />" +
                                          "<b>Invoice Amount: $</b>" +
                                          (invoice.amount / 100).formatMoney() +
                                          "<br /><br />" +
                                          '<a href="' +
                                          URL +
                                          '">View Online</a>';

                                        var emailObj = {
                                          newThread: true,
                                          to: [
                                            "<EMAIL>",
                                          ],
                                          from: appConfig.emailFrom,
                                          subject:
                                            "Request for ACH Invoice Project #" +
                                            project.object_uid,
                                          mergevars: {
                                            TITLE: "Request for ACH Invoice",
                                            BODY: emailBody,
                                            BUTTON: "",
                                          },
                                          emailtags: [
                                            "Infinity ACH Request Email",
                                          ],
                                          type: "notification",
                                          typeId: options.contactId,
                                        };

                                        sb.comm.sendEmail(
                                          emailObj,
                                          function (response) {
                                            sb.dom.alerts.alert(
                                              "Success!",
                                              "A secured invoice for bank account payment will be sent to you in the next 24 hours. For questions regarding payment, <NAME_EMAIL> (You can safely close this window now.)",
                                              "success"
                                            );

                                            ui.send.loading(false);
                                          }
                                        );
                                      }
                                    );
                                  }
                                );
                              }
                            );
                          }
                        );
                      },
                    },
                  },
                  sb.moduleId
                );

                ui.patch();
              },
            },
          },
          sb.moduleId
        );
    } else {
      ui.btns
        .makeNode("classic", "button", {
          css: "mini compact basic green",
          text: "Using my routing and account numbers",
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: function () {
                ui.empty();

                ui.buttons.makeNode("lb_1", "lineBreak", { spaces: 1 });
                ui.create.makeNode("div", "div", {
                  css: "ui raised clearing segment",
                });
                ui.create.div.makeNode("cont", "div", { css: "" });
                ui.create.div.makeNode("title", "div", {
                  text: "Enter account information",
                  css: "ui medium header",
                });
                ui.create.div.makeNode("achForm", "form", formArgs);
                ui.create.div.makeNode("formErrors", "text", {
                  css: "card-errors",
                  text: '<div id="card-errors" class="ui red text"></div>',
                });
                ui.create.div.makeNode("btns", "buttonGroup", { css: "" });
                ui.patch();

                ui.create.div.btns
                  .makeNode("save", "button", {
                    text: "Save",
                    css: "mini compact basic green",
                  })
                  .notify(
                    "click",
                    {
                      type: "paymentMethodRun",
                      data: {
                        run: async function () {
                          ui.create.div.btns.save.loading(true);

                          // variables
                          var formInfo = ui.create.div.achForm.process();
                          var account = {
                            country: "US",
                            currency: "usd",
                          };
                          _.each(formInfo.fields, function (field, fieldName) {
                            account[fieldName] = field.value;
                          });

                          stripe
                            .createToken("bank_account", account)
                            .then(function (result) {
                              ui.makeNode("buttons", "div", { css: "" });

                              if (result.error) {
                                ui.create.div.makeNode("formErrors", "div", {
                                  css: "ui red small header",
                                  text: result.error.message,
                                });

                                ui.patch();

                                ui.create.div.btns.save.loading(false);
                              } else {
                                var obj = {
                                  btokToken: result.token.id,
                                  acctRouting:
                                    result.token.bank_account.routing_number,
                                  acctLast4: result.token.bank_account.last4,
                                  acctName:
                                    result.token.bank_account
                                      .account_holder_name,
                                  contactId: contactId,
                                  verificationEmail:
                                    account.account_verification_email,
                                  instanceId: options.instanceId,
                                };

                                sb.data.db.service(
                                  "StripeService",
                                  "initiateACHMicroDepositVerification",
                                  obj,
                                  function (response) {
                                    if (response.error) {
                                      ui.makeNode("paymentSources", "div", {
                                        css: "ui negative message",
                                        text: "There was an error adding this bank account. Please contact your invoice provider for assistance.",
                                      });

                                      ui.makeNode("create", "div", { css: "" });

                                      ui.patch();
                                    } else if (
                                      response.bankAcct &&
                                      response.bankAcct.status == "new"
                                    ) {
                                      ui.makeNode("paymentSources", "div", {
                                        css: "ui warning message",
                                        text: `The process of adding your new ACH Account has been initiated with Stripe.
                                                  <br/>
                                                  <br/>
                                                  You will receive an email that will provide further details on verifying your ACH account.`,
                                      });

                                      ui.makeNode("create", "div", { css: "" });

                                      ui.patch();
                                    } else if (
                                      response.bankAcct &&
                                      response.bankAcct.status == "verified"
                                    ) {
                                      ui.makeNode("paymentSources", "div", {
                                        css: "ui positive message",
                                        text: "Great news, this account appears to already be verified. You can use it to complete this payment immediately.",
                                      });

                                      ui.makeNode("create", "div", { css: "" });

                                      ui.patch();
                                    } else {
                                      ui.makeNode("paymentSources", "div", {
                                        css: "ui negative message",
                                        text: "There was an error adding this bank account. Please contact your invoice provider for assistance.",
                                      });

                                      ui.makeNode("create", "div", { css: "" });

                                      ui.patch();
                                    }
                                  }
                                );
                              }
                            });
                        },
                      },
                    },
                    sb.moduleId
                  );
                ui.create.div.btns
                  .makeNode("cancel", "button", {
                    text: "Cancel",
                    css: "mini compact basic grey",
                  })
                  .notify(
                    "click",
                    {
                      type: "paymentMethodRun",
                      data: {
                        run: function () {
                          ui.create.div.btns.cancel.loading(true);

                          sb.data.db.service(
                            "StripeService",
                            "getStripeCustomer",
                            paramObj,
                            function (response) {
                              if (!response.customer) {
                                ui.makeNode("paymentSources", "div", {
                                  css: "ui negative message",
                                  text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                                });

                                ui.patch();
                              } else {
                                View(fieldName, ui, response, options);

                                ui.patch();
                              }
                            }
                          );
                        },
                      },
                    },
                    sb.moduleId
                  );

                ui.create.div.btns.patch();
              },
            },
          },
          sb.moduleId
        );
    }

    ui.patch();
  }

  function getPaymentSource(sourceId, stripeId, options, stripeCustomerObj) {
    // variables
    var ui = this;
    var fieldName;
    var options = options;
    options["paymentForm"] = true;

    ui.paymentSources["source-" + sourceId].btns.takePayment.loading(true);

    stripeCustomerObj.sources = stripeCustomerObj.sources.filter((source) => {
      if (source.id == sourceId) {
        return source;
      }
    });

    ui.paymentSources["source-" + sourceId].btns.takePayment.loading(false);

    View(fieldName, ui, { customer: stripeCustomerObj }, options);

    ui.patch();
  }

  // helper functions
  function calculatePaymentWithFees(
    price,
    percentFee,
    flatFee,
    selectedInvoiceIds
  ) {
    var amount = parseFloat(price);
    var variableFee = parseFloat(
      (amount * (parseFloat(percentFee) / 100)).toFixed(2)
    );
    var fixedFee = parseFloat(
      parseFloat(flatFee * selectedInvoiceIds.length).toFixed(2)
    );
    var total = amount + parseFloat((variableFee + fixedFee).toFixed(2));
    var fee = total - amount;

    return {
      amount: amount,
      fee: parseInt(fee * 100),
      total: parseInt(total * 100),
      feeDisplayText: percentFee,
    };
  }

  return {
    init: function () {
      sb.notify({
        type: "register-field-type",
        data: {
          name: "stripe-payment-sources",
          view: View,
        },
      });
    },
  };
});
