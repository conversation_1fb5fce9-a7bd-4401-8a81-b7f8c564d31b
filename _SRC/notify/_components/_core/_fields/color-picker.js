var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('color-picker-view', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	function getDisplayHTML (color, options) {
		
		if ( color.includes('#') ) {
			
			if (options.inCollection) {
				return '<div class="ui empty circular label" value="'+ color +'" style="background-color:' + color + '"></div>';
			} else {
				return '<div class="ui empty circular label" value="'+ color +'" style="margin-left:2px; margin-right:5px; background-color:' + color + '"></div>' + color;
				
			}
			
		} else {
			
			if (options.inCollection) {
				return '<div class="ui '+ color +' empty circular label" value="'+ color +'"></div>';
			} else {
				return '<div class="ui '+ color +' empty circular label" value="'+ color +'" style="margin-left:2px; margin-right:5px;"></div>' + color.charAt(0).toUpperCase() + color.slice(1);
				
			}
			
		}
		
	}
	
	function getColor(color, defaultColor) {
		
		if ( !_.isEmpty(color) ) {
		
			// Check to see if color is part of color pallete
			if ( color.includes('#') ) {
				
				if ( sb.dom.colorsHexToName[color] ) {
					color = sb.dom.colorsHexToName[color];
				}
				
			} else if ( color.includes('rgb') ) {

				// Make sure the RGB value doesn't have spaces
				color.replace(', ', ',');
				
				if ( color.includes('rgba') ) {
					
					color = defaultColor;
					
				} else {
					
					if ( sb.dom.colorsRGBToName[color] ) {
						color = sb.dom.colorsRGBToName[color];
					} else {
						color = sb.dom.colorsRGBToHex(color);
					}
					
				}
				
			}

		} else {
			
			color = defaultColor;
			
		}
		
		return color;
		
	}

	function saveSelection() {
		if (window.getSelection) {
			var sel = window.getSelection();
			if (sel.getRangeAt && sel.rangeCount) {
				return sel.getRangeAt(0);
			}
		} else if (document.selection && document.selection.createRange) {
			return document.selection.createRange();
		}
		return null;
	}
	
	function View (fieldName, ui, obj, options) {
		
		// Set default color
		var defaultColor = !_.isEmpty(options.defaultColor) ? options.defaultColor : 'grey';
		if ( options.defaultColor == 'none' ) {
			defaultColor = 'none';
		}

		function draw (ui, color, options) {
			
			var currentColor = getColor(color, defaultColor) || defaultColor;
			var colors = sb.dom.colors;

			var css = 'edge-field';
			var style = 'padding:.78571429em 1em .78571429em 1em;';

			if (options.inCollection || options.mini ) {
				css = '';
				style: '';
			}

			ui.makeNode('disp', 'div', {
				text: getDisplayHTML(currentColor, options),
				css: css,
				style: style
			});	
			
		}
		
		draw(ui, obj[fieldName], options);
		
		ui.disp.listeners.push(
			function (selector) {

				if ( options.isOpen ) {
					Edit(fieldName, ui, obj, options);	
				} else {
					$(selector).on('click', function(){
						if (options.edit) {
							Edit(fieldName, ui, obj, options);					
						}
					});
				}
				
			}
		);				
		
	}
		
	function Edit (fieldName, ui, obj, options) {

		// Set default color
		var defaultColor = !_.isEmpty(options.defaultColor) ? options.defaultColor : 'grey';
		if ( options.defaultColor == 'none' ) {
			defaultColor = 'none';
		}

		ui.empty();

		if (typeof options.onEditStart === 'function') {
			options.onEditStart();
		}
		
		var color = obj[fieldName];
		var currentColor = getColor(color, defaultColor) || defaultColor;
		var colors = sb.dom.colors;
		var allowAdditions = options.allowAdditions ? true : false;
		
		var searchNode = ui.makeNode('select', 'div', {
			css:'ui basic fluid dropdown search selection edge-field',
			style: 'display:inline-block',
			text: '<div class="text">'+ getDisplayHTML(currentColor, options) +'</div><i class="dropdown icon"></i>',
			listener:{
				type:'dropdown',
				maxSelections: 1,
				allowAdditions: allowAdditions,
				forceSelection: false,
				onShow() {

					setTimeout(function() {
						$('.floater-container').css({'overflow':'visible'});
					}, 100);
						
				},
				onHide() {

					setTimeout(function() {
						$('.floater-container').css({'overflow-y':'scroll', 'overflow-x':'auto'});
					}, 100);
						
				},
				onChange: function (value, text, choice) {
					
					if (!_.isEmpty(value) && value != color) {
						
						// Get color
						if ( choice[0].textContent.includes('Add') ) {
							
							// Make sure the value has a hash
							value = choice[0].textContent.includes('#') ? value : '#' + value;
							
							if ( sb.dom.colorsIsHexadecimal(value) ) {
								currentColor = value;
							} else {
								currentColor = 'grey';
							}
							
						} else {
							currentColor = choice[0].textContent;
						}
						
						// Return color name to RGB if set
						if (options.returnAsRGB) {
							if ( sb.dom.colorsNameToRGB[currentColor] ) {
								currentColor = sb.dom.colorsNameToRGB[currentColor];
							}
						}

						// Grab color object for new value. 
						obj[fieldName] = currentColor;
					
						if (typeof options.onUpdate === 'function') {
							options.onUpdate(obj);
						}
						
						if (options.commitUpdates) {

							sb.data.db.obj.update(
								obj.object_bp_type, 
								{
									id: obj.id,
									[fieldName]: currentColor
								}, 
								function (updates) {
									
									ui.empty();
									
									if (typeof options.onEditStart === 'function') {
										options.onEditEnd();
									}
										
									$(colorSelector.selector).dropdown('destroy');
									
									View(fieldName, ui, obj, options);
									
									ui.patch();
									
									sb.notify({
										type: 'field-updated',
										data: {
											obj: updates,
											type: 'color-picker',
											property: fieldName
										}
									});
									
								}
							);	
							
						} else {

							ui.empty();
							
							if (typeof options.onEditStart === 'function') {
								options.onEditEnd();
							}
								
							$(searchNode.selector).dropdown('destroy');
							View(fieldName, ui, obj, options);
							
							ui.patch();

						}
						
					}	
									
				}
				
			}
			
		});
		
		searchNode.makeNode('menu', 'div', {
			css:'menu'
		});
		
		// Add an empty color
		if (options.defaultColor == 'none') {
			colors.push('none');
			colors = _.uniq(colors);
		}

		_.each(colors, function(color, i){
				
			searchNode.menu.makeNode('option-'+ i, 'div', {
				css:'item',
				text:'<div class="ui '+ color +' empty circular label" value="'+ color +'"></div>' + color
			});

		});
		
		if ( options.isOpen ) {
			$(searchNode.selector).dropdown('show');
		} else {
			ui.select.listeners.push(function (selector) {
				$(selector).dropdown('show');			
			});
		}

		// Get current text selection
		if ( options.saveSelection ) {
			obj.selection = saveSelection();
		}
		
		ui.patch();
		
	}
	
	return {

		init: function () {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'color-picker',
					view: View,
					edit: Edit,
					icon: 'picture',
					propertyType: 'string',
					availableToEntities: false
				}
			});
			
		}

	}

});