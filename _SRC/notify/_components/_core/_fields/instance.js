var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('instance-field', function (sb) {

	function View(fieldName, ui, obj, options) {

		sb.data.db.controller(
			'getConnectedInstances&api_webform=true&pagodaAPIKey='+ appConfig.instance,
			{},
			function (response){
		
				console.log('api response',response);
		
				console.log('obj before update',obj);

				// Instance Selection
				sb.notify ({
					type: 'view-field',
					data: {
						type: 'select',
						property: 'shared_with_instances',
						obj: obj,
						options: {
							placeholder: 'Instance...',
							options: response,
							multi: true,
							edit: true,
							editing: true,
							commitUpdates: false,
							onUpdate(obj) {

								console.log('obj after update',obj);
								
								var sel_array = obj.shared_with_instances.split(',');

								//console.log(sel_array);

								//update the instance value selection
								sb.data.db.obj.update(
									'entity_type'
									, {
									id: 						obj.id,
									shared_with_instances: 		sel_array
									}
									, function (updateResponse) {
										console.log('updateResponse::', updateResponse);
									}
								);
								
							}
						},
						ui: ui
					}
				});
		
				// Set up a dropdown form (multi-select) with the options in response.
				// Allow the users to select these values
					// Update the values 
		
					// sb.data.db.obj.update(
					// 	'entity_type'
					// 	, {
					// 		id: 						obj.id
					// 		, shared_with_instances: 	newValue
					// 	}
					// 	, function (updateResponse) {
					// 		console.log('updateResponse::', updateResponse);
					// 	}
					// );
		
			}
		);
		
	}

	return {

		init: function () {

			// TODO: Remove this feature block and actually do this at the field config in the editor
			if (appConfig.instance === 'foundation_group') {
				// delete options.onlyRelated;
			}

			//Instance
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'instance',
					view: View,
					title: 'Instance',
					availableToEntities: false,
					icon: 'building',
					type: 'Collaborate',
					propertyType: 'objectId',
					objectType: 'user',
					select: {
						name: 		true
					},
					options: {}
				}
			});


			if (IN_NODE_ENV) {
				var user = View;
				var users = View;
				var contacts = View;
				var companies = View;
				module.exports = { 
					user, users, contacts, companies
				}
			}

		}

	};

});

if (IN_NODE_ENV) {
	Factory.startAll();
}