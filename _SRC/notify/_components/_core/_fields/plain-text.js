var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('plain-text-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }

	// data functions

	function Construct (obj, callback) {}

	function Update (obj, callback) {}

	function Parse (obj, callback) {}

	// ui functions

	function View (fieldName, ui, obj, options) {
		
		var underlineTxt = options.link ? '' : '';
		var fieldText = '<div class="" style="'+ underlineTxt +'display:inline-block;">'+ plainTextFormat(obj[fieldName], options) +'</div>';
		var icon;
		var parentName = '';
		var show_parent = false;
		var editable = true;
		var placeholder = fieldName;

		if (!options.hasOwnProperty('commitUpdates')) {
			options.commitUpdates = true;
		}

		if (_.isEmpty(obj[fieldName])) {
			obj[fieldName] = '';
		}
		placeholder = ['foundation_group', 'rickyvoltz'].includes(appConfig.instance) ? 'Click here to type your response' : 'Empty';
		
		if (options.format === 'ein') {
			placeholder = 'XX-XXXXXXX';
		}
		
		function isCurrent(obj){

			return (obj.object_bp_type == 'users' && obj.id == +sb.data.cookie.userId);
		}

		if (options.editing) {

			ui.listeners.push(
				function (ui, obj, fieldName) {
					editTxt(ui, obj, fieldName);
				}.bind({}, ui, obj, fieldName)
			);
			return ;

		}

		if (options.label) {
			placeholder = options.label;
		}

		if(options.hasOwnProperty('edit'))
			editable = options.edit;

		if(!_.isEmpty(obj[fieldName])){

			if(options && options.fields && options.fields[fieldName] && options.fields[fieldName].hasOwnProperty('charLimit')){

				fieldText = '<div class="" style="'+ underlineTxt +'display:inline-block;">'+ obj[fieldName].slice(0, options.fields[fieldName].charLimit) +'</div>';

			}

			if(options.fields && options.fields[fieldName] && options.fields[fieldName].counter){

				if(obj.hasOwnProperty('comment_count')){

					var commentLabel = '<div class="ui mini basic yellow label" style="margin-left:6px;margin-top:-5px;">'+
										'<i class="comments outline icon"></i> '+ obj.comment_count +
								    '</div>';

					ui.makeNode('label', 'div', {css: 'right floated', text:commentLabel});

				}
			}

			var htag = 'div';
			if (options.tag) {
				htag = options.tag;
			}

			var textDiv = '<'+ htag +' class="text" style="display:inline-block">'+ fieldText +'</'+ htag +'>';

			if(options.fields && options.fields[fieldName] && options.fields[fieldName].hasOwnProperty('icon')){

				if(typeof options.fields[fieldName].icon == 'function'){

					icon = options.fields[fieldName].icon(obj);

					if(!_.isNull(icon) && !_.isUndefined(icon))
						textDiv = '<'+ htag +' class="text" style="display:inline-block">'+ icon +' '+ fieldText +'</'+ htag +'>';

				}else if(_.isObject(options.fields[fieldName].icon)){

					icon = '<i class="'+ options.fields[fieldName].icon.type +' icon"></i>';

					if(options.fields[fieldName].icon.hasOwnProperty('color'))
						icon = '<i class="'+ options.fields[fieldName].icon.color +' '+ options.fields[fieldName].icon.type +' icon"></i>';

					textDiv = '<'+ htag +' class="text" style="display:inline-block">'+ icon +' '+ fieldText +'</'+ htag +'>';

				}

			}

			ui.makeNode('t', 'div', {
				tag: 'span'
			});

			ui.t.makeNode('name', 'div', {
				text: 	textDiv
			});

		} else {

			ui.makeNode('t', 'div', {
				text: '<i class="text-muted">' + placeholder + '</i>'
				, style: 'cursor:pointer;'
			});

		}
		
	}

	function Edit (fieldName, ui, obj, options) {

		var underlineTxt = options.link ? '' : '';
// 		var fieldText = '<div class="" style="'+ underlineTxt +'display:inline-block;">'+ obj[fieldName] +'</div>';
		var fieldText = '<div class="" style="'+ underlineTxt +'display:inline-block;">'+ plainTextFormat(obj[fieldName], options) +'</div>';		
		var icon;
		var parentName = '';
		var show_parent = false;
		var editable = true;
		var placeholder = fieldName;
		var css = options.css || '';
		var style = options.style || '';
		var size = options.size || '';

		if (!options.hasOwnProperty('commitUpdates')) {
			options.commitUpdates = true;
		}

		if (_.isEmpty(obj[fieldName])) {
			obj[fieldName] = '';
		}
		placeholder = ['foundation_group', 'rickyvoltz'].includes(appConfig.instance) ? 'Click here to type your response' : 'Empty';
		
		if (options.format === 'ein') {
			placeholder = 'XX-XXXXXXX';
		}
			
		function commitUpdate (objType, update, callback) {

			if (options.commitUpdates) {
				sb.data.db.obj.update(objType, update, callback);
			} else {
				callback(update);
			}

		}

		if (typeof options.onEditStart === 'function')
			options.onEditStart();
		
		ui.empty();
		ui.makeNode('i', 'div', {
			text: '<input style="border:none;background-color:transparent;outline:none;width:100%;' + style + '" placeholder="'+ placeholder +'" class="text ui fluid input ' + size + '" value="">'
		});
		ui.patch();
		
		// THIS FIXES ISSUE WITH DOUBLE QUOTE TRUNCATING THE FIELD 
		$(document).ready(function() {
			$(ui.i.selector + ' input').val(plainTextFormat(obj[fieldName], options));
		});

		setTimeout(function () {
			
			$(ui.i.selector).children().first().on('change input', function(e){
				
				var newVal = $(this).val();
				
				switch (options.format) {
					
					case 'ein':
						$(this).val(formatEIN(newVal));
						break;
					
				}
				
			});
			
			$(ui.i.selector).children().first().change(function(val) {

				$(this).val($(this).val().trim());
				
				if (typeof options.onUpdate === 'function') {
					
					obj[fieldName] = $(this).val();
					options.onUpdate($(this).val());
					
				}
	
				if (options.editing) {
	
					if (!options.commitUpdates) {
	
						obj[fieldName] = $(this).val();
	
						if (typeof options.update === 'function') {
							options.update(obj);
						}
	
					} else {
	
						commitUpdate (
							obj.object_bp_type
							, {
								id: 			obj.id
								, [fieldName]: 	$(this).val()
							}
							, function (response) {
	
						}, options);
	
					}
	
				} else if(typeof options.update === 'function') {
	
					options.update(obj, [fieldName], $(this).val(), function(response) {
	
						$(ui.selector).css('background-color', 'transparent');
	
						if(response){
							obj[fieldName] = response[fieldName];
						}else{
							$(ui.selector).children().first().removeClass('grey');
							$(ui.selector).children().first().addClass('red');
						}
	
						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();
							
						ui.empty();
						View(fieldName, ui, obj, options);
						ui.patch();
	
						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 		response
								, type: 		'plain-text'
								, property: 	fieldName
							}
						});
	
					});
	
				} else {
	
					commitUpdate (obj.object_bp_type, {
						id:obj.id,
						[fieldName]:$(this).val()
					}, function(response){
	
						$(ui.selector).css('background-color', 'transparent');
	
						if(response){
							obj[fieldName] = response[fieldName];
						}else{
							$(ui.selector).children().first().removeClass('grey');
							$(ui.selector).children().first().addClass('red');
						}
	
						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();
						
						ui.empty();
						View(fieldName, ui, obj, options);
						ui.patch();
	
						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 		response
								, type: 		'plain-text'
								, property: 	fieldName
							}
						});
	
					}, options);
	
				}
	
			});
	
			if (!options.editing) {
	
				$(ui.i.selector).children().first().focusout(function(){
	
					if (typeof options.onEditEnd === 'function')
						options.onEditEnd()
	
					View(fieldName, ui, obj, options);
					ui.patch();
	
				});
	
			}
			
		}, 1);
		

	}
	
	// format options
	
	function formatEIN (value) {
		
		var ret = '';
		
		if (!_.isEmpty(value)) {
			
			var base = value.replace(/\D/g,'');
	
			var p1 = base.substring(0, 2);
			var p2 = base.substring(2, 9);	
			
			if (p2.length > 0) {
				ret = p1 + '-' + p2;
			} else {
				ret = p1;
			}

			
		}
				
		return ret;	
		
	}
	
	function plainTextFormat(field, options) {

		var formattedField = field;

		if (options.hasOwnProperty('format') && options.format != 'plain')
			formattedField = formatEIN(field);
			
		return formattedField;
	}	

	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'plain-text',
					title: 'Line of Text',
					availableToEntities: true,
					icon: 'font',
					options: {
						format: {
							type: 		'select'
							, name: 		'Format Options'
							, options: 	[
								{
									name: 	'Plain Text'
									, value: 	'plain'
								}
								, {
									name: 	'EIN'
									, value: 	'ein'
								}
							]
						},
						isSearchable: {
							type: 'bool',
							name: 'Is Searchable'
						}
					},
					view: View,
					edit: Edit,
					propertyType: 'string',
					detail: function (key, obj, options) {
						
						if (options.inCollection) {
							return obj[key];							
						}
						
					},
					useFieldListener: true,
					focus: function (fieldName, ui, obj, options) {
						
						if (!options.editing) {
							$(ui.i.selector).children().first().select();
						}
						
					}
				}
			});

		}

	}

});
