var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('detail-view', function(sb){
	
	var Tags = [
		{
			name: 'H1'
			, description: 'A large heading'
			, type: 'format'
			, tag: 'h1'
			, icon: 'heading'
		}
		, {
			name: 'H2'
			, description: 'A medium heading'
			, type: 'format'
			, tag: 'h2'
			, icon: 'heading'
		}, {
			name: 'H3'
			, description: 'A small heading'
			, type: 'format'
			, tag: 'h3'
			, icon: 'heading'
		}, {
			name: 'Block Quote'
			, description: 'An extended quotation'
			, type: 'format'
			, tag: 'quote'
			, icon: 'quote left'
		}, {
			name: 'Ordered List'
			, description: 'An ordered list'
			, type: 'format'
			, tag: 'ordered-list'
			, icon: 'ordered list'
		}, {
			name: 'Unordered List'
			, description: 'An unordered list'
			, type: 'format'
			, tag: 'unordered-list'
			, icon: 'list'
		}, {
			name: 'Checklist'
			, description: 'A toggle-able list'
			, type: 'format'
			, tag: 'checklist'
			, icon: 'tasks'
		}, {
			name: 'Align Left'
			, description: 'Pull this text left'
			, type: 'format'
			, tag: 'align-left'
			, icon: 'align left'
		}, {
			name: 'Align Center'
			, description: 'Pull this text center'
			, type: 'format'
			, tag: 'align-center'
			, icon: 'align center'
		}, {
			name: 'Align Right'
			, description: 'Pull this text right'
			, type: 'format'
			, tag: 'align-right'
			, icon: 'align right'
		}, {
			name: 'Text'
			, description: 'Just normal text'
			, type: 'format'
			, tag: 'text'
			, icon: 'font'
		}, {
			name: 'Code Block'
			, description: 'A segment of code'
			, type: 'format'
			, tag: 'code-block'
			, icon: 'code'
		}
	];
	var inputType = '';

	if (!IN_NODE_ENV) {
	
		setQuillBlotTypes();

	}
	
	function setQuillBlotTypes () {
		
/*
		function setLinkBlot () {
			
			const InlineBlot = Quill.import('blots/inline');
			
			class NamedLinkBlot extends InlineBlot {
			  static create(value) {
			    const node = super.create(value);
				
			    node.setAttribute('href', value);
			    node.setAttribute('target', '_blank');
			    return node;
			  }
			}
			NamedLinkBlot.blotName = 'namedlink';
			NamedLinkBlot.tagName = 'A';
			
			Quill.register('formats/namedlink', NamedLinkBlot);
			
			const Tooltip = Quill.import('ui/tooltip');
			
			class NamedLinkTooltip extends Tooltip {
				
				show() {
				    super.show();
				    this.root.classList.add('ql-editing');
				}
				
			}
				
				NamedLinkTooltip.TEMPLATE = [
			  '<a class="ql-preview" target="_blank" href="about:blank"></a>',
			  '<input type="text" data-link="https://quilljs.com">',
			  'Url displayed',
			  '<input type="text" data-name="Link name">',
			  '<a class="ql-action"></a>',
			  '<a class="ql-remove"></a>',
			].join('');
			
			
			const QuillModule = Quill.import('core/module');
			
			class NamedLinkModule extends QuillModule {
			  constructor(quill, options) {
			    super(quill, options);
			    this.tooltip = new NamedLinkTooltip(this.quill, options.bounds);
			    this.quill.getModule('toolbar').addHandler('namedlink', this.namedLinkHandler.bind(this));
			  }
			
			  namedLinkHandler(value) {
			    if (value) {
			      var range = this.quill.getSelection();
			      if (range == null || range.length === 0) return;
			      var preview = this.quill.getText(range);
			      this.tooltip.show();
			    }
			  }
			}
			
			Quill.register('modules/namedlink', NamedLinkModule);
		
		}
		
		function setTableBlot () {
			
			const InlineBlot = Quill.import('blots/inline');
			
			class NamedLinkBlot extends InlineBlot {
			  static create(value) {
			    const node = super.create(value);
				console.log('value::', value);
// 			    node.setAttribute('href', value);
// 			    node.setAttribute('target', '_blank');
			    		
			    return node;
			  }
			}
			NamedLinkBlot.blotName = 'table';
			NamedLinkBlot.tagName = 'div';
			NamedLinkBlot.innerHTML = 
			    		'testing here';
			
			Quill.register('formats/table', NamedLinkBlot);
			
			const QuillModule = Quill.import('core/module');
			
			class NamedLinkModule extends QuillModule {
			  constructor(quill, options) {
			    super(quill, options);
			    this.tooltip = new NamedLinkTooltip(this.quill, options.bounds);
			    this.quill.getModule('toolbar').addHandler('namedlink', this.namedLinkHandler.bind(this));
			  }
			
			  namedLinkHandler(value) {
			    if (value) {
			      var range = this.quill.getSelection();
			      if (range == null || range.length === 0) return;
			      var preview = this.quill.getText(range);
			      this.tooltip.show();
			    }
			  }
			}
			
			Quill.register('modules/table', NamedLinkModule);
			
		}
*/
		
		function setMergeTagBlot () {
			
			const InlineBlot = Quill.import('blots/inline');
			
			class MergeTagBlot extends InlineBlot {
				
				static create (setup) {

					if (setup.tag_uid === null) {
					    return node;
				    }
				    const node = super.create(setup);
					
					// Assign uniq hash
					if (setup.tag_uid) {
						 node.setAttribute('tag-uid', setup.tag_uid);
					} else {
						node.setAttribute('tag-uid', Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15));
					}

				    node.setAttribute('tag-id', setup.tag_val);
				    node.className = 'ui label bento-merge-tag';
				    node.setAttribute('contenteditable', false);
				    if (setup.value && setup.value.options) {
					    
					    _.each(setup.value.options, function (val, key) {
						    
						    node.setAttribute(key, val);
						    
					    });
					    
				    }

					return node;
			    
				}
				
				static formats (node) {
					
					var tagType = _.findWhere(Tags, {
						tag: node.getAttribute('tag-id')
					});
					
					var atts = {
						tag_val: node.getAttribute('tag-id')
						, tag_uid: node.getAttribute('tag-uid')
						, value: {
							options: {}
						}
					};
					
					if (tagType && tagType.options) {
						
						_.each(tagType.options, function (type, key) {
							
							atts.value.options[key] = node.getAttribute(key);
							
						});
						
					}
					
					return atts;
					
				}
			  
			}
			MergeTagBlot.blotName = 'mergeTag';
			MergeTagBlot.tagName = 'DIV';
			MergeTagBlot.className = 'bento-merge-tag';
			
			Quill.register('formats/mergeTag', MergeTagBlot);
			
		}

		function setLinkBlot () {
			
			const InlineBlot = Quill.import('formats/link');
			
			class LinkBlot extends InlineBlot {
				
				static create (setup) {
					// if (setup.tag_uid === null) {
					    // return setup;
				    // }
				    const node = super.create(setup);

					// Assign uniq hash
					// if (setup.tag_uid) {
					// 	 node.setAttribute('tag-uid', setup.tag_uid);
					// } else {
					// 	node.setAttribute('tag-uid', Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15));
					// }

				    node.className = 'bento-link';
				    node.setAttribute('data-entity', setup['data-entity']);
					node.setAttribute('data-type', setup['data-type']);
					
					var link = sb.data.url.createPageURL(
						'object'
						, {
							name: 	setup.text
							, type: setup['data-type']
							, id: 	setup['data-entity']
						}
					);

					node.setAttribute('href', link);
				    // if (setup.value && setup.value.options) {
					    
					//     _.each(setup.value.options, function (val, key) {
						    
					// 	    node.setAttribute(key, val);
						    
					//     });
					    
				    // }

					return node;
			    
				}
				
				static formats (node) {
					
					var tagType = _.findWhere(Tags, {
						tag: node.getAttribute('tag-id')
					});
					
					var atts = {
						'data-entity': 	node.getAttribute('data-entity')
						, 'data-type': 	node.getAttribute('data-type')
						, 'text': 		node.text
						// , value: {
							// options: {}
						// }
					};
					
					if (tagType && tagType.options) {
						
						_.each(tagType.options, function (type, key) {
							
							atts.value.options[key] = node.getAttribute(key);
							
						});
						
					}
					
					return atts;
					
				}
			  
			}
			LinkBlot.blotName = 'bentoLink';
			LinkBlot.tagName = 'A';
			LinkBlot.className = 'bento-link';
			
			Quill.register('formats/bentoLink', LinkBlot);
			
		}
		
		setLinkBlot();
		setMergeTagBlot();
		
	}
	
	function mentionDropdown(editor, editorSelector, setup, obj){

		var dropdown;
		var popSelector = 'asdlkfjasldfas';
		var state = 'CLOSED';
		var searchText = '';
		var lastPlace;
		var trigger = '@';
		var triggerText = '';
		var options = setup;

		function getPos (el) {

            el.focus()
            lastPlace = window.getSelection().getRangeAt(0)
            var range = lastPlace.cloneRange()

            return range.getClientRects();

	    }
		
		function getSuggestionTxt (
			suggestion
			, state
			, options
		) {
			
			// Takes a suggestion (with or without preset options) and returns
			// a view as text.
			
			// suggestion 	Object | the suggestion, a merge tag with options 
			// 						 input from the user
			// state 		Object | the state of the selection (options that 
			// 						 go into the selection)
			// options 		Object | formatting options for the return value
			// 				 .
			// => 			String | text view of the suggestion
			
			var txt = '<i class="grey '+ suggestion.icon +' icon"></i> '+ suggestion.name;
			
			// Layout txt for options
			if (suggestion.options) {
				
				_.each(
					suggestion.options
					, function (opt, key) {
						
						var optTxt = '';
						var open = ' ';
						var close = '';
						
						if (
							options
							&& options.excludeOptional
						) {
							return;
						}
						
						if (
							state
							&& state[key]
						) {
							
							optTxt += state[key]
							
						} else if (
							!options
							|| !options.excludeOptional
						) {
							
							open += '.. ';
							optTxt += opt.name;
							
						}
						
						if (
							opt.isOptional
							&& _.isEmpty(state[key])
							&& (
								!options
								|| !options.excludeOptional
							)
						) {
							
							open += ' [';
							close += ']';
							
						}
						
						if (
							!_.isEmpty(opt.prep)
							&& (
								!options
								|| !options.excludeOptional
							)
						) {
							
							open += opt.prep +' ';

						}
						
						txt += open + optTxt + close;
						return;
						
					}
				);
				
			}
			
			return txt;
			
		}

	    function open (
	    	data
	    	, editable
	    	, hint
	    ) {
		    // Opens the suggestion box--currently contains formatting options 
		    // and a few merge tags. 
		    // @zach 3/22/20
		    
		    // data 		Object 	| ?state data
		    // editable 	String 	| the selector for node containing the 
		    // 						  document piece the user is working 
			// 						  within
		    // hint  		?Object | 

			// Search the api for objs instead of local opts
			function openApiSearch (data, editable, hint) {

				// State of the view
				searchText = '';
				var justOpened = true;

				// The cursor position within the editable content
				var pos = getPos($(editable))[0];
				var scrollOffset = $(window).scrollTop();

				// The current selection information
				var input = {
					tag: false
				};

				$('body').append(
					'<div id="'+ popSelector +'" class="ui transparent search" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;width:500px !important;position:absolute;left:calc('+pos.x +'px);top:calc('+ (+pos.y + scrollOffset) +'px);z-index:10000;">'+
						'<div class="ui transparent input suggestion-prompt" style="">'+
							'<input id="wysiwyg_editor_mention_prompt" class="prompt" type="text" placeholder="Type to search for a page..">'+
						'</div>'+
						'<div class="results" style="border-top-left-radius: 0px;border-top-right-radius: 0px;outline:none !important;border:0px !important;margin-top:0px;width:500px !important;"></div>'+
					'</div>'
				);
	
				dropdown = $('#'+ popSelector).search({
					fullTextSearch: 		false
					, searchFields: 		['searchTxt', 'description']
					// source: 				content
					, selectFirstResult: 	true
					, cache: 				false
	// 			    , debug: 				true
					, searchOnFocus: 		true
					, minCharacters: 		3
					, onSelect: 			function (entity) {

						if (!_.isEmpty(entity)) {

							var nodeSetup = {
								bentoLink: {
									text: 				entity.name
									, 'data-type': 		entity.object_bp_type
									, 'data-entity': 	entity.id
								}
							}
							var txt = entity.name;
							var selection = window.getSelection();
							selection.removeAllRanges();
							selection.addRange(lastPlace.cloneRange());
							
							editor.deleteText(
								editor.getSelection().index - 1
								, 1
							);
	
							editor.insertText(
								editor.getSelection().index
								, txt
								, nodeSetup
							);
	
							editor.insertText(
								editor.getSelection().index
								, '\n'
							);
							
							setTimeout(function () {	
								
								// Update the listeners, in case the new node
								// depends on some listeners.
								setNodeListeners();
								
							}, 1);

						} else {

							close();

						}
	
					}
					, apiSettings: 			{
						url: sb.data.db.obj.getSearchPath(
							'ANY-SET'
							, {}
							, {}
							, {
								limit: 			8
								, searchUids: 	true
							}
						)
						, onResponse: function(raw){

							var response = {
								results : {}
							};
							
							_.each(raw.results, function(item){
								
								item.title = item.name;
								if (item.object_uid) {
									item.title = '#'+ item.object_uid +' | '+ item.title;
								}

								if (response.results.hasOwnProperty(item.object_bp_type)) {
									
									response.results[item.object_bp_type].results.push(item);
								
								} else {
									
									var type = _.findWhere(
										appConfig.Types
										, {
											bp_name: item.object_bp_type.substring(1)
										}
									);
									
									if (!_.isEmpty(type)) {

										response.results[item.object_bp_type]
											= {
												name: 		'<i class="ui grey '+ type.icon +' icon"></i>'+ type.name
												, results: 	[item]
											};

									}
										
								}
								
							});
							
							return response;
							
						}
						, mockResponseAsync: function(selection, where, settings, callback) {
								
							function GetCookieValue(name) {
								var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
								return found.length > 0 ? found[0].split("=")[1] : null;
							}
							
							var headers = {
								'bento-token': GetCookieValue('token')
							};
							if (appConfig.is_portal) {
								
								headers.portal = appConfig.state.portal;
								headers['bento-token'] = GetCookieValue('p_token');
								
							}
							
							$.ajax({
								type: 'post',
								url: settings.url,
								xhrFields: {
									withCredentials: true
								},
								crossDomain: true,
								data: {
									json: JSON.stringify({
										selection: 	selection
										, where: 	where
									})
								},
								success: function (response) {
									
									// Filter out responses w/no name vals
									if (
										!_.isEmpty(response)
										&& Array.isArray(response.results)
									) {
										response.results = _.filter(response.results, function (result) {
											return (!_.isEmpty(result.name) && typeof result.name === 'string');
										});
									}

									// Close and allow user to continue typeing if nothing is found
									if (
										_.isEmpty(response)
										|| _.isEmpty(response.results)
									) {

										var txt = $('#wysiwyg_editor_mention_prompt').val();
										var selection = window.getSelection();
										selection.removeAllRanges();
										selection.addRange(lastPlace.cloneRange());
										var index = editor.getSelection().index;
										editor.insertText(
											index
											, txt
											, {}
										);
										
										setTimeout(function () {
					
											editor.setSelection(
												index + txt.length
												, 0
											);
											
										}, 1);

										close();

									} else {
										callback(response);
									}
									
								},
								error: function (jqXHR, status, error) {
					
									
									
								},
								headers: headers
							});	
							
						}.bind({}, {name: true}, {})
						// }.bind({}, args.listener.selection, args.listener.where)
					}
					, searchDelay: 			200
				    , type:  				'category'
					, onResultsClose: 		function(){
						close();
					}
				});
	
				dropdown.search('show');
				dropdown.search('set active');
				
				$('#wysiwyg_editor_mention_prompt').focus();
				$('#wysiwyg_editor_mention_prompt').on('keydown', function (e) {
	
					// Suppress the tab key from exiting the input, and, if possible, 
					// autofill from nearest suggestion.
					if (e && e.keyCode === 9) {
	
						var currentVal = $(this).val();
						var closestResult = dropdown.search('get result');
	
						// Autofill, and keep the search open
						if (closestResult) {
							
							$(this).val(closestResult.value +' ');
							e.stopPropagation();
							e.preventDefault();
	
						}
	
					}
					
				});
				
				state = 'OPEN';
				justOpened = false;

			}

			function addOptionSuggestions (
				// Reads the current input from the user and adds options based
				// on the option configuration of the selected tag, if it exists.
				selected 	// Object 		| the currently selected tag definition
				, options 	// [Object] 	| the options list to be appended to 
				, input 	// Object 		| the meta-data currently input by the user
				, inputText // String 		| the raw input from the user
				// => 		   [Object] 	| the updated list of options
			) {
				
				function addSuggestionsFromOptionDef (
					currentOpt 	// Object 		| the current option definition
					, options 	// [Object] 	| the options list to be appended to 
					, input 	// Object 		| the meta-data currently input by the user
					, memo 		// Object 		| the selection info to be used in the options
					// => 		   [Object] 	| the updated list of options
				) {
					
					// Take in the values, and return the display version
					function getOptsTxtVals (memo, options) {
						
						var ret = {};
						
						_.each(options, function (optDef, key) {
							
							if (memo[key]) {
								
								switch (optDef.type) {
									
									case 'set':
										var set = _.find(appConfig.Types, {
											bp_name: memo[key]
										});
										if (set) {
											ret[key] = set.name;
										}
										break;
										
									case 'field':
										var set = _.find(appConfig.Types, {
											bp_name: memo[optDef.set]
										});
										if (set) {
											ret[key] = set.blueprint[memo[key]].name;
										}
										break;
									
								}
								
							}
							
						});
						
						return ret;
						
					}
					
					// Add options if not yet selected
					switch (currentOpt.type) {
					    
					    case 'set':
					    	_.each(
						    	appConfig.Types
						    	, function (type) {
							    	
							    	if (typeof currentOpt.filter === 'function') {
								    	
								    	if (!currentOpt.filter(type)) {
									    	
									    	return false;
									    	
								    	}
								    	
							    	}
							    	
							    	memo[optKey] = type.bp_name;
							    	var disp = getOptsTxtVals(memo, selected.options);
							    	
							    	options.push({
										title: 			getSuggestionTxt(selected, disp)
										, searchTxt: 	getSuggestionTxt(selected, disp, {format: 'plain-text'}).toLowerCase()
										, description: 	''
										, value: 		{
											tag: 		selected.name
											, options: 	_.clone(memo)
										}
										, isOption: true
									});
							    	
						    	}
					    	);
					    	break;
					    	
					    case 'field':
					    	if (input[currentOpt.set]) {
						    	
						    	var typeBp = _.findWhere(
							    	appConfig.Types
							    	, {
								    	bp_name: input[currentOpt.set]
							    	}
						    	);
						    	
						    	// If optional, show last option, w/out current set
								if (currentOpt.isOptional) {
									
									options.push({
										title: getSuggestionTxt(selected, {
												//!TODO: build up these options in while loop
												[currentOpt.set]: 	typeBp.name
											}
											, {
												excludeOptional: 	true
											}
										)
										, searchTxt: getSuggestionTxt(
											selected
											, {
												//!TODO: build up these options in while loop
												[currentOpt.set]: 	typeBp.name
												, [optKey]: 		false
											}
											, {
												format: 			'plain-text'
												, excludeOptional: 	true
											}
										).toLowerCase()
										, description: 	''
										, value: 		{
											tag: selected.name
											, options: {
												[currentOpt.set]: 	typeBp.bp_name
												, [optKey]: 		false
											}
										}
										, isOption: true
									});
									
								}
						    	
						    	_.each(typeBp.blueprint, function (field, key) {
							    	
							    	// Check for allowed types 
							    	if (
								    	!_.isEmpty(currentOpt.allowedTypes)
								    	&& !_.contains(
								    		currentOpt.allowedTypes
								    		, field.fieldType
								    	)
							    	) {
								    	
								    	return;
								    	
							    	}
							    	
							    	options.push({
										title: getSuggestionTxt(selected, {
											//!TODO: build up these options in while loop
											[currentOpt.set]: 	typeBp.name
											, [optKey]: 		field.name
										})
										, searchTxt: getSuggestionTxt(
											selected
											, {
												//!TODO: build up these options in while loop
												[currentOpt.set]: 	typeBp.name
												, [optKey]: 		field.name
											}
											, {
												format: 'plain-text'
											}
										).toLowerCase()
										, description: 	''
										, value: 		{
											tag: selected.name
											, options: {
												[currentOpt.set]: 	typeBp.bp_name
												, [optKey]: 		key
											}
										}
										, isOption: true
									});
							    	
						    	});
						    	
					    	}
					    	break;
					    	
					    case 'select':
					    	
					    	_.each(currentOpt.options, function (opt, key) {
						    	
						    	memo[optKey] = key;
						    	var disp = getOptsTxtVals(memo, selected.options);
						    	disp[optKey] = opt;
// 						    	var disp = memo;
						    	
						    	options.push({
									title: 			getSuggestionTxt(selected, disp)
									, searchTxt: 	getSuggestionTxt(selected, disp, {format: 'plain-text'}).toLowerCase()
									, description: 	''
									, value: 		{
										tag: 		selected.name
										, options: 	_.clone(memo)
									}
									, isOption: true
								});
						    	
					    	});
							break;
							
						case 'string':

							break;
					    
				    }
				    
				    return options;
					
				}
				
			    // Current
			    if (selected && selected.options) {
				    
				    var i = 0;
				    var optKeys = Object.keys(selected.options);
				    var optKey = optKeys[i];
				    var currentOpt = selected.options[optKey];
				    var memo = {};
				    
				    while (currentOpt) {
						
						options = addSuggestionsFromOptionDef(
							currentOpt
							, options
							, input
							, _.clone(memo)
						);
						
						// Update memo
					    if (input[optKey]) {
						    memo[optKey] = input[optKey];
					    }

						// Go to next option
					    i++;
					    optKey = optKeys[i];
						currentOpt = selected.options[optKey];
						
					}
				    
			    }
			    
			    // Sort options by shortest suggestion txt first, so that 
			    // additional options can always be accessed by the user by
			    // being more specific in what they type.
			    options = _.chain(options)
			    			.sortBy(
							    function (opt) {
								    
/*
								    if (opt.title.indexOf('..') !== -1) {
									    
									    return 0;
									    
								    }
*/
								    
								    return opt.title.length;
								    
							    }
						    )
/*
						    .filter(
							    function (opt) {
								    
								    if (opt.title.indexOf('[') !== -1) {
									    
										return false;
									    
								    }
								    
								    return true;
								    
							    }
						    )
*/
						    .value();
				
			    return options;
			    
		    }

			if (hint && hint.type === 'searchAndLink') {
				
				return openApiSearch(data, editable, hint);

			}
		    
		    // State of the view
		    searchText = '';
		    var justOpened = true;
			
			// The cursor position within the editable content
		    var pos = getPos($(editable))[0];
		    var scrollOffset = $(window).scrollTop();
		    
		    // The current selection information
		    var input = {
			    tag: false
		    };

		    var content = [];
		    if (Array.isArray(hint.mentions) && hint.mentions.length > 0) {
			    
				content = _.map(
					hint.mentions
					, function(mention) {
				
						if (typeof mention === 'string') {
							
							return {
								title: mention
							};
							
						} else if (mention && mention.name) {

							var optsString = getSuggestionTxt(mention, {});
							return {
								title: 			optsString
								, description: 	mention.description
								, value: 		mention.name
								, searchTxt: 	mention.name.toLowerCase()
// 								, category: mention.type.toUpperCase()
							};
							
						}
					
					}
				);
				
		    }

			$('body').append(
				'<div id="'+ popSelector +'" class="ui transparent search" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;width:500px !important;position:absolute;left:calc('+pos.x +'px);top:calc('+ (+pos.y + scrollOffset) +'px);z-index:10000;">'+
					'<div class="ui transparent input suggestion-prompt" style="">'+
						'<input id="wysiwyg_editor_mention_prompt" class="prompt" type="text" placeholder="">'+
					'</div>'+
					'<div class="results" style="border-top-left-radius: 0px;border-top-right-radius: 0px;outline:none !important;border:0px !important;margin-top:0px;width:500px !important;"></div>'+
				'</div>'
			);

			dropdown = $('#'+ popSelector).search({
				fullTextSearch: 		false
				, searchFields: 			['searchTxt', 'description']
			    // source: 				content
			    , selectFirstResult: 	true
			    , cache: 				false
// 			    , debug: 				true
			    , searchOnFocus: 		true
			    , minCharacters: 		0
			    , onSelect: 			function(a){

				    var paste = a;
				    var input = dropdown.search('get result');
				    if(hint.content){
					    paste = hint.content(a.title);
				    }

					close(paste, input);

				}
				, apiSettings: 			{
					mockResponseAsync: function (settings, onComplete) {

						var a = settings.urlData.query;
						var queryTxt = a || '';
						queryTxt = queryTxt.toLowerCase();
						if (_.isEmpty(a) && !justOpened) {
							close();
						}
						
						// Check currently selected
						var input = dropdown.search('get result');
						var currentSelection = _.find(
							Tags
							, {
								name: input.value
							}
						);
						
						if (
							input.value
							&& input.value.options
						) {
							
							currentSelection = _.find(
								Tags
								, {
									name: input.value.tag
								}
							);
							_.each(input.value.options, function (val, key) {
								input[key] = val;
							});
							
						}
						if (currentSelection) {
							
							input.tag = currentSelection.name;
							input.tag_val = currentSelection.tag;
							
						}
						
						// Reset additional options
						content = _.filter(
							content
							, function (tag) {
								return tag.isOption !== true;
							}
						);

						var alwaysVisibleOption = {
							description: 	''
							, searchTxt: 	queryTxt
							, title: 		settings.urlData.query
							, value: 		''
						};
						var closestOption = _.find(
							content
							, function (opt) {
								return settings.urlData.query.startsWith(opt.value);
							}
						);
						if (closestOption) {

							alwaysVisibleOption.options = {
								text: settings.urlData.query.substring(closestOption.value.length)
							};

							// Remove leading space
							if (alwaysVisibleOption.options.text.startsWith(' ')) {
								alwaysVisibleOption.options.text = alwaysVisibleOption.options.text.substring(1);
							}
							alwaysVisibleOption.value = closestOption.value;
							alwaysVisibleOption.description = closestOption.description;
							alwaysVisibleOption.tag_val = closestOption.tag_val;
							alwaysVisibleOption.tag = closestOption.tag_val;
							
						}

						// Generate new options
						content = addOptionSuggestions(
							_.clone(currentSelection)
							, content
							, input
							, a
						);

						// Filter the options, and pass to the search results.
						var results = [];
						if (typeof hint.search === 'function') {

							results = hint.search(queryTxt);

						} else {

							results = _.filter(
								content
								, function (option) {
	
									if (typeof option.searchTxt === 'string') {
										
										return option.searchTxt.includes(queryTxt);
	
									} else {
	
										return option.title.includes(queryTxt);
	
									}
	
								}
							);

						}

						// If no results, just close and let the user proceed
						if (_.isEmpty(results) && _.isEmpty(closestOption)) {

							close(queryTxt, false);

						} else {

							results.push(alwaysVisibleOption);
							onComplete(results);

						}
						
					}
				}
			    , searchDelay: 			0
// 			    , type:  				'category'
			    , onResultsClose: 		function(){
				    close();
				}
			});

			dropdown.search('show');
			dropdown.search('set active');
			
			$('#wysiwyg_editor_mention_prompt').focus();
			$('#wysiwyg_editor_mention_prompt').on('keydown', function (e) {

				// Suppress the tab key from exiting the input, and, if possible, 
				// autofill from nearest suggestion.
				if (e && e.keyCode === 9) {

					var currentVal = $(this).val();
					var closestResult = dropdown.search('get result');

					// Autofill, and keep the search open
					if (closestResult) {
						
						$(this).val(closestResult.value +' ');
						e.stopPropagation();
						e.preventDefault();

					}

				}
				
			});
			
			state = 'OPEN';
			justOpened = false;
			
			return;

	    }
		
	    function close (paste, selected) {


			function applyFormatOption (tag) {
				
				switch (tag.tag) {
					
					case 'align-left':
						editor.format('align', false);
						break;
						
					case 'align-right':
						editor.format('align', 'right');
						break;
						
					case 'align-center':
						editor.format('align', 'center');
						break;
					
					case 'h1':
					case 'h2':
					case 'h3':
						editor.format('header', tag.tag);
						break;
					
					case 'code-block':
						editor.format('code-block', true);
						break;
					
					case 'quote':
						editor.format('blockquote', true);
						break;
					
					// Lists
					case 'checklist':
						editor.format('list', 'unchecked');
						break;
					case 'ordered-list':
						editor.format('list', 'ordered');
						break;
					case 'unordered-list':
						editor.format('list', 'unordered');
						break;
						
					case 'text':
						editor.format('align', 'left');
						editor.format('list', false);
						editor.format('blockquote', false);
						editor.format('header', false);
						break;
					
				}
				
				editor.deleteText(
					editor.getSelection().index - 1
					, 1
				);
				
				setTimeout(function () {
					
					editor.setSelection(
						index
						, 0
					);
					
				}, 1);
				
			}
			
			function applyViewOption (
				tag
				, tagOptions
				, txt
			) {
				
				function verifyOptions (tag, tagOptions) {
					
					var verified = true;
					
					_.each(tag.options, function (opt, key) {
						
						if (
							!opt.isOptional
							&& _.isEmpty(tagOptions.value.options[key])
						) {
							
							verified = false;
							
						}
						
					});
					
					return verified;
					
				}

				function runOnSelect (txt, tag, tagOpts, onComplete) {

					// Pass the tag options to the onSelect function
					// set on the tag registration, and pass it along, 
					// if it exists.
					if (
						tag
						&& typeof tag.onSelect === 'function'
					) {

						tag.onSelect(
							tag
							, tagOpts
							, obj
							, options
							, function (nodeSetup, text) {

								if (text && !_.isEmpty(text)) {
									txt = text;
								}
								onComplete(txt, nodeSetup);

							}
						);

					} else {

						onComplete(
							txt
							, {
								mergeTag: tagOpts
							}
						);

					}

				}
				
				// Verify that all mandatory options have been set
				if (verifyOptions(tag, tagOptions)) {
					
					runOnSelect(txt, tag, tagOptions, function (txt, nodeSetup) {

						editor.deleteText(
							editor.getSelection().index - 1
							, 1
						);

						editor.insertText(
							editor.getSelection().index
							, txt
							, nodeSetup
						);

						editor.insertText(
							editor.getSelection().index
							, '\n'
						);
						
						setTimeout(function () {	
							
							// index = editor.getSelection().index
							// editor.insertText(
							// 	index
							// 	, ' '
							// 	, {
							// 		p: {text: ' '}
							// 	}
							// );
// console.log('paste::', index, editor.root.innerHTML.substring(index - 1));
							// editor.setSelection(
							// 	index - 1 + paste.length
							// 	, 0
							// );
							
							// Update the listeners, in case the new node
							// depends on some listeners.
							setNodeListeners();
							
						}, 1);
							
					});

				}
				
			}

			dropdown.search('hide');
		    setTimeout(function () {
			    $('#'+ popSelector).remove();
		    }, 100)
		    
		    state = 'CLOSED';

		    var selection = window.getSelection();
			selection.removeAllRanges();
			selection.addRange(lastPlace.cloneRange());
			var index = editor.getSelection().index;
			var tag = _.find(Tags, function (tag) {
				
				return paste === '<i class="grey '+ tag.icon +' icon"></i> '+ tag.name;
				
			});

			// Get tag w/options from selected
			if (
				!tag
				&& selected
			) {
				
				if (!selected.hasOwnProperty('tag') && selected.value && selected.value.tag) {
					selected.tag = selected.value.tag;
				}

				tag = _.findWhere(Tags, {
					name: selected.tag
				});

				if (tag) {
					selected.tag_val = tag.tag;
				}
				
			}

			if (tag) {
				
				switch (tag.type) {
					
					case 'format':
						applyFormatOption(tag);
						break;
					
					case 'action':
					case 'view':
						applyViewOption(tag, selected, paste.replace(/<\/?[^>]+(>|$)/g, ""));
						break;
					
				}
				
			} else if (paste) {

				editor.insertText(
					index
					, paste
				);
				
				setTimeout(function () {
					
					editor.setSelection(
						index + paste.length
						, 0
					);
					
				}, 1);
				
			}

	    }

	    function shouldOpen (triggerText) {

		    var ret = false;

			_.each(options.hint, function(hint){

			    if (hint.hasOwnProperty('match')) {

				    if ((new RegExp(hint.match)).test(triggerText)) {
						ret = hint;
						inputType = hint.type;
				    }

			    }

		    });
			
		    return ret;

	    }

	    function update (data, editable) {
			
		    if (typeof data == 'string') {

			    searchText += data.data;
			    dropdown.dropdown('set text', '@'+ searchText);
				
		    } else {
			    
			    close();
			    
		    }

	    }

		return {

			update: function(data, editable, isDelete){

				switch(state){

					case 'OPEN':

// 						update(data, editable);

					break;

					case 'CLOSED':

						if(data){

							if(!isDelete) {

								triggerText += data;

							} else {

								triggerText.slice(-1, data);

								triggerText += ' ';

							}

						}

						var hint = shouldOpen(triggerText);

						if(hint){

							triggerText = '';
							open(data, editable, hint);

						}

					break;

				}

			}

		};

	}

	function setMentions (fieldName, ui, obj, options) {
		
		if (_.isEmpty(options.hint)) {
			options.hint = [];
		}
		
		// Formatting options
		options.hint.push({
			type: 'format',
			mentions: _.chain(Tags)
						.filter(function (t) {
							
							switch (t.type) {
								
								case 'format':
									return true;
									
								case 'view':
									return options.is_template;
								
							}
							
							return false;
							
						})
						 .value(),
			match: /\B\/(\w*)$/,
			// search: function (keyword, callback) {
				
			// 	var ret = _.map(this.mentions, function(item) {
					
			// 		if(item.toLowerCase().search(keyword.toLowerCase()) > -1){
			// 			return item;
			// 		}
					
			// 	});
				
			// 	callback(_.compact(ret));
				
			// },
			content: function (item) {

				if (typeof item == 'string') {
					/*
var mentUser = _.find(Setup.usersList, function(us){

										return us.name === item;	

									});

					if (mentUser != null || mentUser !== undefined) {

						Setup.mentionedUsers.push(mentUser.id);	

					}
*/
					
				}
				
				return item;
				
			}
		});

		// In-line Creates
		options.hint.push({
			type: 	'create',
			mentions: _.chain(Tags)
						.filter(function (t) {
							
							switch (t.type) {
								
								case 'action':
									return true;
									
								default:
									return false;
								
							}
							
							return false;
							
						})
						 .value(),
			match: 	/\+/,
			// search: function (keyword, callback) {

			// 	var ret = _.map(this.mentions, function(item) {
					
			// 		if(item.toLowerCase().search(keyword.toLowerCase()) > -1){
			// 			return item;
			// 		}
					
			// 	});
				
			// 	callback(_.compact(ret));
				
			// },
			content: function (item) {

				if (typeof item == 'string') {
					/*
var mentUser = _.find(Setup.usersList, function(us){

										return us.name === item;	

									});

					if (mentUser != null || mentUser !== undefined) {

						Setup.mentionedUsers.push(mentUser.id);	

					}
*/
					
				}
				
				return item;
				
			}
		});
		
		// In-line search+links
		if (false) {

			options.hint.push({
				type: 	'searchAndLink',
				mentions: _.chain(Tags)
							.filter(function (t) {
								
								switch (t.type) {
									
									case 'action':
										return true;
										
									default:
										return false;
									
								}
								
								return false;
								
							})
							 .value(),
				match: 	/\#/,
				// search: function (keyword, callback) {
	
				// 	var ret = _.map(this.mentions, function(item) {
						
				// 		if(item.toLowerCase().search(keyword.toLowerCase()) > -1){
				// 			return item;
				// 		}
						
				// 	});
					
				// 	callback(_.compact(ret));
					
				// },
				content: function (item) {
	
					if (typeof item == 'string') {
						/*
	var mentUser = _.find(Setup.usersList, function(us){
	
											return us.name === item;	
	
										});
	
						if (mentUser != null || mentUser !== undefined) {
	
							Setup.mentionedUsers.push(mentUser.id);	
	
						}
	*/
						
					}
					
					return item;
					
				}
			});
			
		}
		
		return options;
		
	}

	function getMergedHtml(obj, options, onComplete) {

		if (!obj.id) {
			return;
		}

		var mergeState = {
			entity: obj,
			format: 'html'
		};

		mergeState.object_bp_type = obj.object_bp_type;
		
		sb.notify({
			type: 'get-merged-html',
			data: {
				state: mergeState,
				template: options.template,
				related_object: obj,
				callback: function (html) {
					
					onComplete(html);
					
				}
			}
		});

	}

	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = obj[fieldName] ? obj[fieldName] : '';

            if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;
			
		} else {
            ui.empty();
        }


		// If read-only/merging and in a collection, just show
		// a link to open the merged version in a modal.
		if (
			options.inCollection
			&& options.alwaysMerge
		) {

			ui.makeNode('btn', 'div', {
				css: 'ui grey dropdown item field-manager edit'
				, text: '<i class="ui grey text file icon"></i>'
			});
			return;
			
		}

		if (obj) {

			if (!obj.hasOwnProperty(fieldName)) {
				obj.fieldName = '';
			}

			var txt = '';

            if (typeof obj[fieldName] === 'string' && !_.isEmpty(obj[fieldName])) {
				txt = obj[fieldName].replace(/\n/g, '<br />');
			}

			if(options && options.fields && options.fields[fieldName] && options.fields[fieldName].hasOwnProperty('charLimit')){
				
				if (obj[fieldName]) {
					txt = obj[fieldName].slice(0, options.fields[fieldName].charLimit) + '...';
				} else {
					txt = '';
				}

			}
			
			if (
				options.edit == false
				|| options.editing == false
			) {
				
				if (txt === '' || txt.replace(/(<([^>]+)>)/gi, "") == '' ) {
					txt = '<span class="text-muted placeholder" style="cursor: text;">Empty</span>';
				}	
				
			}
			
			// If merging, place a loader gif while it loads in
			if (
				options.alwaysMerge
				&& options.isTemplate !== true
			) {

				ui.makeNode('c', 'div', {
					id: ui.selector.replace('.', '#') +'-q',
					text: '<i class="ui notched circle loading icon"></i>',
					css: 'ql-editor snow',
					data: {
						'field-id': options._fieldId
					}
				});

			} else {
				
				if(options.limitInTable == true){
				
					txt = txt.slice(0, 30);
					ui.makeNode('c', 'div', {
						id: ui.selector.replace('.', '#') +'-q',
						text: txt+'...',
						css: 'ql-editor snow',
						style: 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis;',
						data: {
							'field-id': options._fieldId
						}
					});			
				
				}else{
					
					ui.makeNode('c', 'div', {
						id: ui.selector.replace('.', '#') +'-q',
						text: txt,
						css: 'ql-editor snow',
						style: 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis;',
						data: {
							'field-id': options._fieldId
						}
					});
				
				}
				
				

			}

			if (!options.edit && !options.editing) {
				
				if (!_.isEmpty(options.html_string)) {

					if (options.alwaysMerge) {

						options.template = options.html_string;
	
						getMergedHtml(
							obj,
							options,
							function (html) {
								
								ui.makeNode('c', 'div', {
									id: ui.selector.replace('.', '#') +'-q',
									text: html,
									css: 'bentoDocumentEditorPreviewer',
									data: {
										'field-id': options._fieldId
									}
								});
	
								ui.patch();
	
							}
						);
	
					} else {
						
						ui.makeNode('c', 'div', {
							id: ui.selector.replace('.', '#') +'-q',
							text: options.html_string,
							css: 'bentoDocumentEditorPreviewer',
							data: {
								'field-id': options._fieldId
							}
						});
	
						ui.patch();	
	
					}

				}

			}


		}

	}

	function setNodeListeners () {

		$('.bento-link').off('click');
		$('.bento-link').on('click', function (e) {
			
			function viewItemInModal (item) {
				
				if (item.object_bp_type === 'groups') {
					window.location.href =  sb.data.url.getObjectPageParams(item, {});
					return;
				}

				sb.notify({
					type: 	'get-sys-modal'
					, data: 	{
						callback: 	function (modal) {
							
							modal.body.empty();
							modal.show();
							
							modal.body.makeNode('menu', 'div', {
								css: 'ui secondary right floated menu',
								style: 'margin:0 !important;'
							});
							
							var linkSetup = { 
								css:'circular icon button item', 
								text:'<i class="external square alternate icon"></i>', 
								tag:'a',
								href:sb.data.url.getObjectPageParams(item, {})
							};
				
							var closeLinkSetup = { 
								css:'circular icon button item', 
								text:'<i class="close icon"></i>', 
								tag:'a'
							};
							
							modal.body.menu.makeNode('open', 'div', linkSetup);
							
							modal.body.menu.makeNode('close', 'div', closeLinkSetup)
								.listeners.push(
									
									function (selector) {
										
										$(selector).on(
											'click'
											, function () {
												modal.hide();
											}
										);
										
									}
								);
							
							modal.body.makeNode('c', 'div', {});
							modal.body.patch();
							
							sb.notify({
								type: 'view-entity'
								, data: {
									ui: 	modal.body.c
									, id: 	item.id
								}
							});
							
						}
					}
				});
				

				
			}

			sb.data.db.obj.getById(
				this.getAttribute('data-type')
				, this.getAttribute('data-entity')
				, function (item) {

					viewItemInModal(item);

				}
			);

		});

	}
	
	function editMode (fieldName, ui, obj, options) {

        function commitChange (update, onComplete) {
			
			sb.data.db.obj.update(
				obj.object_bp_type
				, update
				, function (updated) {

					obj[fieldName] = updated[fieldName];
					onComplete(updated);

				}
			);
			
		}
		
		options = setMentions(fieldName, ui, obj, options);

		// If read-only/merging and in a collection, just show
		// a link to open the merged version in a modal.
		if (
			options.inCollection
			&& options.alwaysMerge
		) {

			return;
			
		}
		
		if (
			options.hasOwnProperty('useMedium')
			&& options.useMedium === true
		) {

			sb.notify({
				type: 'get-medium-editor',
				data: {
					ui: ui,
					obj: obj,
					property: fieldName,
					options: {
						header: options.header,
						labelTxt: options.labelTxt,
						css: options.css,
						style: options.style,
						mentions: options.mentions,
						promptSave: options.promptSave,
						previewAndEdit: options.previewAndEdit,
						onChange: options.onChange || function(){}
					}
				}
			});

			if (
				options.commitUpdates === undefined
				|| options.commitUpdates === true
			) {

				ui.makeNode('saveBtn', 'div', {
					css: 'ui mini green bottom attached button'
					, text: 'Save'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
						run: function(data) {

							ui.saveBtn.loading();

							var formData = ui.bentoDocumentEditor.form.process().fields.bentoDocumentEditor.value;

							sb.data.db.obj.update(
								obj.object_bp_type
								, {
									id: obj.id
									, [fieldName]: 	formData
								}
								, function (updated) {

									obj[fieldName] = updated[fieldName];

									if (typeof options.onUpdate === 'function') {
										options.onUpdate(obj, formData);
									}

									ui.saveBtn.loading(false);

								}
							) ;

						}
					}
				}, sb.moduleId);

			}

		} else {

			if (_.isEmpty(ui.c)) {
				View(fieldName, ui, obj, options);
				ui.patch();
			}
			
			var timer = 0;
			var toolbarOptions = [
				['bold', 'italic', 'underline', 'strike'],        // toggled buttons
				[{ 'list': 'ordered'}, { 'list': 'bullet' }],
			];

			var editorPlaceholder = (['foundation_group', 'rickyvoltz'].includes(appConfig.instance) || appConfig.is_portal) ? 'Click here to type your response' : 'Empty';
			var toolbarOpts = [
				['bold', 'italic', 'underline', 'strike'],        // toggled buttons
				['blockquote', 'code-block'],

				[{ 'header': 1 }, { 'header': 2 }],               // custom button values
				[{ 'list': 'ordered'}, { 'list': 'bullet' }],
				[{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
				[{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
				[{ 'direction': 'rtl' }],                         // text direction

				[{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
				[{ 'header': [1, 2, 3, 4, 5, 6, false] }],
				[ 'link', 'image', /* 'video', */ 'formula' ],          // add's image support
				[{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
				[{ 'font': [] }],
				[{ 'align': [] }],

				['clean']                                         // remove formatting button
			];
			
			if (options.hasOwnProperty('placeholder')) {

				editorPlaceholder = options.placeholder;

			}

			setTimeout(function () {

                function destory_editor(selector){
                    if($(selector)[0])
                    {
                        var content = $(selector).find('.ql-editor').html();
                        $(selector).html(content);

                        $(selector).siblings('.ql-toolbar').remove();
                        $(selector + " *[class*='ql-']").removeClass (function (index, class_name) {
                           return (class_name.match (/(^|\s)ql-\S+/g) || []).join(' ');
                        });
                
                        $(selector + "[class*='ql-']").removeClass (function (index, class_name) {
                           return (class_name.match (/(^|\s)ql-\S+/g) || []).join(' ');
                        });
                    }
                    else
                    {
                        // console.error('editor not exists');
                    }
                }

                destory_editor('.field-edit-pop-div-c');

				// If it is editable
				if (
					!options.alwaysMerge
					|| options.isTemplate === true
				) {

                    $(ui.selector + ' .placeholder').remove();

                    options.editor = new Quill(ui.selector, {
						theme: 'snow',
						placeholder: editorPlaceholder,
						modules: {
							toolbar: 		toolbarOpts
							// 						, namedlink: 	true
							// 						, mergeTag: 	true
							, clipboard: 	true
							, magicUrl: 	{
								// Regex used to check URLs during typing
								urlRegularExpression: /(https?:\/\/[\S]+)|(www.[\S]+)|(mailto:[\S]+)|(tel:[\S]+)/,
								// Regex used to check URLs on paste
								globalRegularExpression: /(https?:\/\/|www\.|mailto:|tel:)[\S]+/g,
							}
						}
					});

					// Show the toolbar
					if (options.showToolbar) {
						$(ui.selector).parent().addClass('show-toolbar');
					}
					
					var mentionPop = {};
					var throttleTime = 2000;
					
					if (!options.commitUpdates) {
						throttleTime = 1;
					}
					
					if (
						options.hasOwnProperty('throttleTime')
						&& typeof options.throttleTime === 'number'
					) {
						throttleTime = options.throttleTime;
					}

					var runCommitChange = _.throttle(
							commitChange
							, throttleTime
							, {
								leading: false
							}
						);
					
					// Set update listener
					if (options.editor) {
						
						var alertOverLimit = _.throttle(
							function (charLimit) {

								sb.notify({
									type: 'display-alert',
									data: {
										header: 'You can only enter '+ options.charLimit +' characters here!',
										color: 	'red'
									}
								});
								
							}
							, 2000
						);

                        if ( !options.inCollection ) {

                            options.editor.on(
                                'text-change'
                                , _.throttle(
                                    function (delta, oldDelta, source) {
                                        
                                        function getInsertDelta(delta) {
                                            
                                            var ret = false;
                                            
                                            _.each(delta.ops, function(val) {
                                                
                                                if(val.hasOwnProperty('insert')) {
                                                    
                                                    ret = val.insert;
                                                    
                                                }
                                                
                                            });
                                            
                                            return ret;
                                            
                                        }
                                        
                                        function getDeleteDelta(delta) {
                                            
                                            var ret = false;
                                            
                                            _.each(delta.ops, function(val) {
                                                
                                                if(val.hasOwnProperty('delete')) {
                                                    
                                                    ret = val.delete;
                                                    
                                                }
                                                
                                            });
                                            
                                            return ret;
                                            
                                        }
                                        
                                        var insertDelta = getInsertDelta(delta);
                                        var deleteDelta = getDeleteDelta(delta);
                                        var updatedHTML = options.editor.root.innerHTML;

                                        // If only whitespace, no text content, set to empty string.
                                        if (
                                            _.isEmpty(options.editor.root.textContent)
                                            && options.editor.root.getElementsByTagName('img').length < 1
                                        ) {
                                            updatedHTML = '';
                                        }

                                        // If there is a character limit set, enforce it
                                        if (
                                            !_.isEmpty(options)
                                            && typeof options.charLimit === 'number'
                                            && options.charLimit > 0
                                            && options.editor.getLength() > options.charLimit
                                        ) {
                                            
                                            // Rm text over the limit
                                            options.editor.deleteText(options.charLimit, options.editor.getLength());
                                            
                                            // Show message to user
                                            alertOverLimit(options.charLimit);

                                        }
                                        
                                        // Update the triggering text, for the prompt 
                                        // dropdown.
                                        if (options.hint) {
                                            
                                            // Add to the mention triggering text
                                            if (insertDelta) {
                                            
                                                mentionPop.update(
                                                    insertDelta
                                                    , ui.selector.replace('.', '#') +'-q',
                                                    false
                                                );
                                                
                                            }
                                            
                                            // Remove from the mention triggering text
                                            if (deleteDelta) {
                                                
                                                mentionPop.update(
                                                    deleteDelta
                                                    , ui.selector.replace('.', '#') +'-q',
                                                    true
                                                    );
                                                    
                                            }
                                                
                                        }

                                        if (options.commitUpdates === false) {

                                            obj[fieldName] = updatedHTML;

                                            if (typeof options.onUpdate === 'function') {
                                                options.onUpdate(obj, updatedHTML);
                                            }
                                            
                                        } else if (options.commitUpdates === undefined || options.commitUpdates === true) {

                                            runCommitChange(
                                                {
                                                    id: 			obj.id
                                                    , [fieldName]: 	updatedHTML
                                                }
                                                , function (updated) {
                                                    
                                                    if (typeof options.onUpdate === 'function') {
                                                        options.onUpdate(obj, updatedHTML);
                                                    }
                                                    
                                                }
                                            );
                                    
                                        }
                                
                                    }
                                )
                                , throttleTime
                                , {leading: false}
                            );
                        }
						
					}
					
					if (options.hint) {
						
						var contextObj = obj;
						if (!obj.id && options.context) {
							contextObj = options.context;
						}
						mentionPop = mentionDropdown(options.editor, ui.c.selector, options, contextObj);
						
					}

					setNodeListeners();
				
				// If it is always merging
				} else if (options.alwaysMerge && !_.isEmpty(options._default)) {
					
					options.template = options._default;

					getMergedHtml(
						obj
						, options
						, function (html) {

							$(ui.c.selector).html(html);
							setNodeListeners();

						}
					);
					return;

				}

                $(ui.selector).on('focusout', function (val) {

                    var updated = options.editor.getContents();

                    if ( updated == 'Empty\n')
                        updated = '';

                    if ( obj[fieldName] != updated ) {

                        sb.data.db.obj.update(
                            obj.object_bp_type
                            , {
                                id:                 obj.id
                                , [fieldName]:      updated
                            }
                            , function(res){

                                var dataOpt = {
                                    obj:            res
                                    , type:         'detail'
                                    , property:     fieldName
                                    , _fieldId:      options._fieldId
                                };
        
                                if ( options.inTableField ) {
                                    sb.notify({
                                        type: 'field-updated',
                                        data: dataOpt
                                    });
                                }
                            } 
                        );


                    }

                });

			}, 100);

		}
		
	}

	return {

		initListeners: function () {
			
			sb.listen({
				[sb.moduleId+'-run']: 		this.run,
				'register-merge-tag': 		this.registerMergeTag,
				'get-medium-editor':		this.getMediumEditor
			});
			
		} 

		, init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'detail',
					view: View,
					edit: editMode,
					selector: 'bento-text-field',
					title: 'Text',
					icon: 'align left',
					propertyType: 'string',
					availableToEntities: true,
					detail: function (key, obj, options) {
						
						if (options.inCollection) {
							return obj[key];
						} else {
							return false;
						}
						
					},
					options: {
						alwaysMerge: {
							name: 		'Always merge? <i>(this will make this field read-only)</i>'
							, type: 	'bool'
						}
						, additionalTags: {
							name: 		'Additional tags <i>(additional tags to pass down to objects created within the text field, using the actions menu opened with the "+" key)</i>'
							, type: 	'tags'
						}
						, charLimit: {
							name: 		'Character Limit'
							, type: 	'number'
						}
                        , popupSize: {
                            width:      400
                            , height:   300
                        }
						, limitInTable: {
							name: 		'Limit display in table view?'
							, type: 	'bool'
						}
					},
                    blockRefresh: false,
					useFieldListener: true,
					usePopup: true,
					focus: function (fieldName, ui, obj, options) {

                        // Bring up a modal, for read-only
						if (
							options.inCollection
							&& options.alwaysMerge
						) {
							
							sb.notify({
								type: 	'get-sys-modal'
								, data: {
									callback: function (modal) {

										modal.show();
										modal.body.makeNode('loader', 'div', {
											css: 		''
											, text: 	'<div class="ui hidden divider"></div><br /><br /><i class="notched circle loading icon"></i>'
											, style: 	'width:100%;height:100%;text-align:center;'
										});
										modal.body.patch();

										options.template = options._default;

										getMergedHtml(
											obj
											, options
											, function (html) {
												
												modal.body.empty();
												modal.body.makeNode(
													'c'
													, 'div'
													, {
														id: 		ui.selector.replace('.', '#') +'-q'
														, text: 	html
														, css: 		'ql-editor snow'
									// 					, style: 	'margin-left:-22px !important;margin-top:-10px !important;'
														, data: {
															'field-id': options._fieldId
														}
													}
												);
												modal.body.patch();
					
											}
										);

										setNodeListeners();
	
									}
								}
							});

						} else {

                            setTimeout(function(){

                                options.editor.focus();

                                var range = options.editor.getLength();

                                options.editor.setSelection(range, range);
                              
                            }, 100);

						}
						
					}
				}
			});

			if (IN_NODE_ENV) {
				var text = View;
				module.exports = { 
					text
				}
			}

		}

		, run: function(data) {

			data.run(data);

		}
		
		, registerMergeTag: function (setup) {

			if (setup.type) {
				
				Tags.push({
					name: 			setup.name
					, description: 	setup.description || ''
					, type: 		setup.type
					, tag: 			setup.tag
					, onSelect: 	setup.onSelect || false
					, options: 		setup.options || false
					, icon: 		setup.icon || 'file alternate'
					, data: 		setup.data || false
					, parse: 		setup.parse || false
				});
				
			}

		},

		getMediumEditor: function(data) {

			var ui = data.ui;
			var obj = data.obj;
			var fieldName = data.property;
			var options = data.options ? data.options : [];

			var header = options.header ? options.header : false;
			var labelTxt = options.labelTxt ? options.labelTxt : '';
			var css = options.css ? options.css : '';
			var style = options.style ? options.style : 'min-height:100px;';
			var id = options.previewAndEdit ? 'bentoDocumentEditorPreviewer' : 'bentoDocumentEditor';

			var mappedMentions = options.mentions ? options.mentions : [];
			var hint = [];
			if (!_.isEmpty(mappedMentions)) {

				hint = [{
					mentions: mappedMentions,
					match: /\B{{(\w*)$/,
					search: function(keyword, callback) {

						var ret = _.map(this.mentions, function(item){

							if(item.search(keyword) > -1){
								return item;
							}

						});

						callback(_.compact(ret));

					},
					content: function(item) {
						return item;	
					}
				}];

			}

			ui.empty();

			ui.makeNode('bentoDocumentEditor', 'div', {
				id: id
			});

			function callback() {
				if (typeof options.onChange === 'function') {
					options.onChange($('.medium-editor-element').html());
				}
			}

			ui.bentoDocumentEditor.makeNode('form', 'form', {
				html: {
					name: 'bentoDocumentEditor',
					type: 'textbox',
					header: header,
					label: labelTxt,
					css: css,
					style: style,
					wysiwyg: {
						focus: true,
						toolbar: true,
						hint: hint
					},
					hint: hint,
					value: sb.dom.removeScriptTags(obj[fieldName]),
					onChange: function(html) {
						callback();
					}
				}

			});

			ui.patch();

			function removeEditingClasses() {
				$('.adding-cell-style').removeClass('adding-cell-style');
				$('.adding-table-style').removeClass('adding-table-style');
			}

			function getStylingFields(obj, floater, callback) {
			
				if ( obj.selector == '.adding-cell-style' ) {
					
					// Cell padding
					var paddingContainer = floater.container.body.makeNode('paddingContainer', 'div', {
						style: 'margin-bottom:15px;'
					});
					paddingContainer.makeNode('label', 'div', {
						text: '<strong>Padding (in px):</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'quantity',
							property: 'padding',
							obj: obj,
							options: {
								min: 0,
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate(obj) {
									
								}
							},
							ui: paddingContainer.makeNode('field', 'div', {
								css:'round-border',
								style:'padding:10px 10px 8px 10px;'
							})
						}
					});
					
				}

				if ( obj.selector == '.adding-table-style' ) {
				
					// Border width
					var borderWidthContainer = floater.container.body.makeNode('borderWidthContainer', 'div', {
						style: 'margin-bottom:15px;'
					});
					borderWidthContainer.makeNode('label', 'div', {
						text: '<strong>Border width (in px):</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'quantity',
							property: 'borderWidth',
							obj: obj,
							options: {
								min: 0,
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate(obj) {
									
								}
							},
							ui: borderWidthContainer.makeNode('field', 'div', {
								css:'round-border',
								style:'padding:10px 10px 8px 10px;'
							})
						}
					});
					
					// Border style
					var borderStyleContainer = floater.container.body.makeNode('borderStyleContainer', 'div', {
						style: 'margin-bottom:15px;'
					});
					borderStyleContainer.makeNode('label', 'div', {
						text: '<strong>Border style:</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'select',
							property: 'borderStyle',
							obj: obj,
							options: {
								options: [
									{
										name:'Solid',
										value:'solid'
									},
									{
										name:'Dotted',
										value:'dotted'
									},
									{
										name:'Dashed',
										value:'dashed'
									},
									{
										name:'None',
										value:'none'
									}
								],
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate(obj) {
									
								}
							},
							ui: borderStyleContainer.makeNode('field', 'div', {
								css:'round-border'
							})
						}
					});
					
					// FOR SOME REASON, BORDER COLOR DOES NOT WORK ON MPDF, SO REMOVING FOR NOW
					// // Border color
					// var borderColorContainer = floater.container.body.makeNode('borderColorContainer', 'div', {
					// 	style: 'margin-bottom:15px;'
					// });
					// borderColorContainer.makeNode('label', 'div', {
					// 	text: '<strong>Border color:</strong>'
					// });
					// borderColorContainer.makeNode('fieldContainer', 'div', {
					// 	css: 'round-border',
					// 	style: 'display:inline-block; margin-top:2px;'
					// });
					// sb.notify ({
					// 	type: 'view-field',
					// 	data: {
					// 		type: 'color-picker',
					// 		property: 'borderColor',
					// 		obj: obj,
					// 		options: {
					// 			defaultColor: 'none',
					// 			edit: true,
					// 			editing: true,
					// 			commitUpdates: false,
					// 			onUpdate(obj) {
									
					// 			}
					// 		},
					// 		ui: borderColorContainer.fieldContainer.makeNode('field', 'div', {})
					// 	}
					// });

					// Apply Borders To
					obj.applyBorderTo = '';
					if (obj.allBorders) {
						obj.applyBorderTo = 'allBorders';
					} else if (obj.outerBorderOnly) {
						obj.applyBorderTo = 'outerBorderOnly';
					} else if (obj.innerBorderOnly) {
						obj.applyBorderTo = 'innerBorderOnly';
					}

					var applyBorderToContainer = floater.container.body.makeNode('applyBorderToContainer', 'div', {
						style: 'margin-bottom:15px;'
					});
					applyBorderToContainer.makeNode('label', 'div', {
						text: '<strong>Apply to:</strong>',
						style: 'margin-bottom:5px;'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'select',
							property: 'applyBorderTo',
							obj: obj,
							options: {
								options: [
									{
										name:'Outer & Inner Border',
										value:'allBorders'
									},
									{
										name:'Outer Border Only',
										value:'outerBorderOnly'
									},
									{
										name:'Inner Border Only',
										value:'innerBorderOnly'
									}
								],
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate(obj) {

									// Reset variables
									obj.allBorders = false;
									obj.outerBorderOnly = false;
									obj.innerBorderOnly = false;

									// Set new variable
									if (obj.applyBorderTo === 'allBorders') {
										obj.allBorders = true;
									} else if (obj.applyBorderTo === 'outerBorderOnly') {
										obj.outerBorderOnly = true;
									} else if (obj.applyBorderTo === 'innerBorderOnly') {
										obj.innerBorderOnly = true;
									}

								}
							},
							ui: applyBorderToContainer.makeNode('field', 'div', {
								css:'round-border'
							})
						}
					});

				}
				
				// Background color
				var backgroundColorContainer = floater.container.body.makeNode('backgroundColorContainer', 'div', {
					style: 'margin-bottom:15px;'
				});
				backgroundColorContainer.makeNode('label', 'div', {
					text: '<strong>Background color:</strong>'
				});
				backgroundColorContainer.makeNode('fieldContainer', 'div', {
					css: 'round-border',
					style: 'margin-top:2px;'
				});
				sb.notify ({
					type: 'view-field',
					data: {
						type: 'color-picker',
						property: 'backgroundColor',
						obj: obj,
						options: {
							defaultColor: 'none',
							allowAdditions: true,
							returnAsRGB: true,
							edit: true,
							editing: true,
							commitUpdates: false,
							onUpdate(obj) {
								
							}
						},
						ui: backgroundColorContainer.fieldContainer.makeNode('field', 'div', {})
					}
				});
					
				// Width
				var widthContainer = floater.container.body.makeNode('widthContainer', 'div', {
					style: 'margin-bottom:15px;'
				});
				widthContainer.makeNode('label', 'div', {
					text: '<strong>Width (in %):</strong>'
				});
				sb.notify ({
					type: 'view-field',
					data: {
						type: 'quantity',
						property: 'width',
						obj: obj,
						options: {
							min: 0,
							edit: true,
							editing: true,
							commitUpdates: false,
							onUpdate(obj) {
								
							}
						},
						ui: widthContainer.makeNode('field', 'div', {
							css:'round-border',
							style:'padding:10px 10px 8px 10px;'
						})
					}
				});
				
				if ( obj.selector == '.adding-table-style' ) {
				
					// Table alignment
					var alignmentContainer = floater.container.body.makeNode('alignmentContainer', 'div', {
						style: 'margin-bottom:15px;'
					});
					alignmentContainer.makeNode('label', 'div', {
						text: '<strong>Horizontal alignment:</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'select',
							property: 'alignment',
							obj: obj,
							options: {
								options: [
									{
										name:'None',
										value:'none'
									},
									{
										name:'Left',
										value:'left'
									},
									{
										name:'Center',
										value:'center'
									},
									{
										name:'Right',
										value:'right'
									}
								],
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate(obj) {
									
								}
							},
							ui: alignmentContainer.makeNode('field', 'div', {
								css:'round-border'
							})
						}
					});
					
				}
				
				if ( obj.selector == '.adding-cell-style' ) {

					// Vertical alignment
					var verticalAlignmentContainer = floater.container.body.makeNode('verticalAlignmentContainer', 'div', {
						style: 'margin-bottom:15px;'
					});
					verticalAlignmentContainer.makeNode('label', 'div', {
						text: '<strong>Vertical alignment:</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'select',
							property: 'verticalAlignment',
							obj: obj,
							options: {
								options: [
									{
										name:'Top',
										value:'top'
									},
									{
										name:'Middle',
										value:'middle'
									},
									{
										name:'Bottom',
										value:'bottom'
									}
								],
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate(obj) {
									
								}
							},
							ui: verticalAlignmentContainer.makeNode('field', 'div', {
								css:'round-border'
							})
						}
					});

				}
				
				// Save button
				floater.container.body.makeNode('saveBtn', 'div', {
					css: 'ui button green pull-right',
					style: 'margin-top:15px;',
					text: 'Save Changes'
				}).notify('click', {
					type:[sb.moduleId+'-run'],
					data:{
						run:function() {

							// Update styles
							if ( obj.width ) {

								if ( obj.selector == '.adding-cell-style' ) {

									// Run through and set cell width for the same column
									$('.adding-table-style td').each(function() {
										if ( $(this).index() == $(obj.selector).index() ) {
											$(this).attr('width', obj.width + '%');
											$(this).attr('data-width', obj.width);
										}
									});

								} else if ( obj.selector == '.adding-table-style' ) {

									// Set table width
									$(obj.selector).attr('width', obj.width + '%');
									$(obj.selector).attr('data-width', obj.width);
								
								}

							}
							
							if ( obj.selector == '.adding-cell-style' ) {
								$(obj.selector).css({'padding': obj.padding, 'vertical-align': obj.verticalAlignment});
							} else if ( obj.selector == '.adding-table-style' ) {
								$(obj.selector).attr('align', obj.alignment);
								if ( obj.borderWidth == 0 || obj.borderStyle == 'none' ) {
									$(obj.selector).css({'border-color': '', 'border-style': '', 'border-collapse': 'collapse'});
									$(obj.selector + ' > tbody > tr').attr('style', '');
									$(obj.selector + ' > tbody > tr > td').css({'border-width': '', 'border-style': '', 'border-color': ''});
								} else {
									obj.borderColor = 'black';
									if ( obj.outerBorderOnly ) {
										$(obj.selector).css({'border-color': obj.borderColor, 'border-style': obj.borderStyle, 'border-width': obj.borderWidth + 'px', 'border-collapse': 'separate'});
										$(obj.selector + ' > tbody > tr').attr('style', 'border-top-width:0px; border-bottom-width:0px; border-left-width:' + obj.borderWidth + 'px; border-right-width:' + obj.borderWidth + 'px; border-style:' + obj.borderStyle + '; border-color:' + obj.borderColor + ';');
										$(obj.selector + ' > tbody > tr:first-child').attr('style', 'border-top-width:' + obj.borderWidth + 'px; border-bottom-width:0px; border-left-width:' + obj.borderWidth + 'px; border-right-width:' + obj.borderWidth + 'px; border-style:' + obj.borderStyle + '; border-color:' + obj.borderColor + ';');
										$(obj.selector + ' > tbody > tr:last-child').attr('style', 'border-top-width:0px; border-bottom-width:' + obj.borderWidth + 'px; border-left-width:' + obj.borderWidth + 'px; border-right-width:' + obj.borderWidth + 'px; border-style:' + obj.borderStyle + '; border-color:' + obj.borderColor + ';');
										$(obj.selector + ' > tbody > tr > td').css({'border-width': '', 'border-style': '', 'border-color': ''});
									} else if ( obj.innerBorderOnly ) {
										$(obj.selector).css({'border-color': '', 'border-style': '', 'border-width': '', 'border-collapse': 'collapse'});
										$(obj.selector + ' > tbody > tr').attr('style', '');
										$(obj.selector + ' > tbody > tr > td').attr('style', 'border-top-width:0px; border-bottom-width:' + obj.borderWidth + 'px; border-left-width:' + obj.borderWidth + 'px; border-right-width:0px; border-style:' + obj.borderStyle + '; border-color:' + obj.borderColor + ';');
										$(obj.selector + ' > tbody > tr:last-child > td').attr('style', 'border-top-width:0px; border-bottom-width:0px; border-left-width:' + obj.borderWidth + 'px; border-right-width:0px; border-style:' + obj.borderStyle + '; border-color:' + obj.borderColor + ';');
										$(obj.selector + ' > tbody > tr > td:first-child').css({'border-left-width': '0px'});
									} else {
										$(obj.selector).css({'border-color': obj.borderColor, 'border-style': '', 'border-collapse': 'collapse'});
										$(obj.selector + ' > tbody > tr').attr('style', '');
										$(obj.selector + ' > tbody > tr > td').css({'border-width': obj.borderWidth + 'px', 'border-style': obj.borderStyle, 'border-color': obj.borderColor});
									}
								}
							}

							if (obj.backgroundColor == 'none') {
								$(obj.selector).css({'background-color': 'transparent'});	
							} else {
								$(obj.selector).css({'background-color': obj.backgroundColor});	
							}

							// Update data attributes
							$(obj.selector).attr('data-collapse', obj.collapse);
							$(obj.selector).attr('data-padding', obj.padding);
							$(obj.selector).attr('data-alignment', obj.alignment);
							$(obj.selector).attr('data-vertical-alignment', obj.verticalAlignment);
							$(obj.selector).attr('data-border-width', obj.borderWidth);
							$(obj.selector).attr('data-border-style', obj.borderStyle);
							$(obj.selector).attr('data-border-color', obj.borderColor);
							$(obj.selector).attr('data-outer-border-only', obj.outerBorderOnly);
							$(obj.selector).attr('data-inner-border-only', obj.innerBorderOnly);
							$(obj.selector).attr('data-background-color', obj.backgroundColor);

							// Add cell widths to cells that are empty to prevent issues in mPDF
							$('table.medium-editor-table tr').each(function() {
								var amountOfTDs = $(this).children('td').length;
								var percentageOfEachTD = parseFloat(100 / amountOfTDs).toFixed(2);
								var hasWidth = 0;
								$(this).children('td').each(function() {
									if ( !_.isEmpty($(this).attr('width')) ) {
										hasWidth = 1;
									}
								});
								if (!hasWidth) {
									$(this).children('td').each(function() {
										$(this).attr('width', percentageOfEachTD + '%');
									});
								}
							});
					
							// Hide floater
							floater.close(function() {
								removeEditingClasses();
								callback();
							});

						}
					}
				}, sb.moduleId);
				
				// Cancel button
				floater.container.body.makeNode('cancelBtn', 'div', {
					css: 'ui button grey pull-right',
					style: 'margin-top:15px;',
					text: 'Cancel'
				}).notify('click', {
					type:[sb.moduleId+'-run'],
					data:{
						run:function() {

							// Hide floater
							floater.close(function() {
								removeEditingClasses();
							});

						}
					}
				}, sb.moduleId);
				
				// Clear float
				floater.container.body.makeNode('clear', 'div', {
					style: 'clear:both;',
				});

				// Patch
				floater.patch();
				
			}

			$(document).ready(function() {

				if (options.promptSave) {

					var currentHTML = $('.medium-editor-element').html();
					document.getElementsByClassName('medium-editor-element')[0].addEventListener('input', function() {
						if ( $('.medium-editor-element').html() != currentHTML ) {
							if ( currentHTML == '' && $('.medium-editor-element').html() == '<p><br></p>' ) {
								window.onbeforeunload = null;
								$('#isWaitingToSave').val('');
							} else {
								window.onbeforeunload = function() {
									return "You have unsaved changes, are you sure you want to leave?";
								}
								$('#isWaitingToSave').val('1');
							}
						} else {
							window.onbeforeunload = null;
							$('#isWaitingToSave').val('');
						}
					}, false);
				
				}

				// This code fixes everything
				$.contextMenu('destroy');

				$.contextMenu({
					selector: '#bentoDocumentEditor table.medium-editor-table td', 
					items: {
						"cellStyle": {
							name: "Cell properties",
							callback: function(item, menuOptions) {

								removeEditingClasses();

								// Add classes to selected elements
								$(menuOptions.$trigger[0]).addClass('adding-cell-style');
								$(menuOptions.$trigger[0]).closest('table.medium-editor-table').addClass('adding-table-style');

								sb.notify({
									type: 'get-sys-floater',
									data: {
										element: '.adding-cell-style',
										offsetTop: 25,
										callback: function (floater) {

											// Get outer border property (since it's special :P)
											var outerBorderOnly = false;
											if ( !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-outer-border-only')) ) {
												if ( $('#bentoDocumentEditor .adding-table-style').attr('data-outer-border-only') == 'true' ) {
													outerBorderOnly = true;
												}
											}

											// Get inner border property (since it's special :P)
											var innerBorderOnly = false;
											if ( !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-inner-border-only')) ) {
												if ( $('#bentoDocumentEditor .adding-table-style').attr('data-inner-border-only') == 'true' ) {
													innerBorderOnly = true;
												}
											}

											var allBorders = (outerBorderOnly || innerBorderOnly) ? false : true;
											
											var cellObj = {
												selector: '.adding-cell-style',
												collapse: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-collapse')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-collapse') : '',
												padding: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-padding')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-padding') : '1',
												borderWidth: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-border-width')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-border-width') : '0',
												borderStyle: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-border-style')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-border-style') : 'none',
												borderColor: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-border-color')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-border-color') : '',
												allBorders: allBorders,
												outerBorderOnly: outerBorderOnly,
												innerBorderOnly: innerBorderOnly,
												backgroundColor: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-background-color')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-background-color') : '',
												width: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-width')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-width') : '',
												alignment: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-alignment')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-alignment') : 'left',
												verticalAlignment: !_.isEmpty($('#bentoDocumentEditor .adding-cell-style').attr('data-vertical-alignment')) ? $('#bentoDocumentEditor .adding-cell-style').attr('data-vertical-alignment') : 'top'
											}
											
											// Header
											floater.container.makeNode('header', 'div', {
												'tag': 'h3',
												'text': 'Cell properties',
												'style': 'margin-bottom:10px;'
											});

											// Body
											floater.container.makeNode('body', 'div', {});
											
											getStylingFields(cellObj, floater, function() {
												callback();
											});

										}

									}

								});
				
							}
						},
						"tableStyle": {
							name: "Table properties",
							callback: function(item, menuOptions) {

								removeEditingClasses()

								// Add classes to selected elements
								$(menuOptions.$trigger[0]).addClass('adding-table-style');
								$(menuOptions.$trigger[0]).closest('table.medium-editor-table').addClass('adding-table-style');

								sb.notify({
									type: 'get-sys-floater',
									data: {
										element: '.adding-table-style',
										offsetTop: 25,
										callback: function (floater) {

											// Get outer border property (since it's special :P)
											var outerBorderOnly = false;
											if ( !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-outer-border-only')) ) {
												if ( $('#bentoDocumentEditor .adding-table-style').attr('data-outer-border-only') == 'true' ) {
													outerBorderOnly = true;
												}
											}

											// Get inner border property (since it's special :P)
											var innerBorderOnly = false;
											if ( !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-inner-border-only')) ) {
												if ( $('#bentoDocumentEditor .adding-table-style').attr('data-inner-border-only') == 'true' ) {
													innerBorderOnly = true;
												}
											}

											var allBorders = (outerBorderOnly || innerBorderOnly) ? false : true;

											// Create object
											var tableObj = {
												selector: '.adding-table-style',
												collapse: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-collapse')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-collapse') : 'collapse',
												padding: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-padding')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-padding') : '1',
												borderWidth: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-border-width')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-border-width') : '0',
												borderStyle: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-border-style')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-border-style') : 'none',
												borderColor: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-border-color')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-border-color') : '',
												allBorders: allBorders,
												outerBorderOnly: outerBorderOnly,
												innerBorderOnly: innerBorderOnly,
												backgroundColor: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-background-color')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-background-color') : '',
												width: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-width')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-width') : '100',
												alignment: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-alignment')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-alignment') : 'left',
												verticalAlignment: !_.isEmpty($('#bentoDocumentEditor .adding-table-style').attr('data-vertical-alignment')) ? $('#bentoDocumentEditor .adding-table-style').attr('data-vertical-alignment') : 'top'
											}
											
											// Header
											floater.container.makeNode('header', 'div', {
												'tag': 'h3',
												'text': 'Table properties',
												'style': 'margin-bottom:10px;'
											});

											// Body
											floater.container.makeNode('body', 'div', {});

											getStylingFields(tableObj, floater, function() {
												callback();
											});

										}

									}

								});
								
							}
						}
					}
				});

			});

		}

	}

});

if (IN_NODE_ENV) {
	Factory.startAll();
}
