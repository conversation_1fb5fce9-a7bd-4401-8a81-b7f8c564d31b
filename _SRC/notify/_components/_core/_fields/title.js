var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('title-field', function (sb) {

	// data functions

	function Construct (obj, callback) {}

	function Update (obj, callback) {}

	function Parse (obj, callback) {}

	// ui functions
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			var url = sb.data.url.createPageURL(
				'object'
				, {
					name: 		obj.name
					, type: 	obj.object_bp_type
					, id: 		obj.id
				}
			);	
			ret = '<a href="'+ url +'">'+ obj.name +'</a>';

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}

		var underlineTxt = options.link ? '' : '';
		var css = options.css || '';
		var style = options.style || '';
		var verticalAlign = options.verticalAlign || 'middle';
		var headerCss = '';
		
		if (options.inCollection) {
			headerCss = 'mini';
		}
		
		var titleText = '';

        if ( options.muted )
            css += ' text-muted';
		
		if (options.editing || options.inCollection) {

			if (options.uid && obj.object_uid) {
				titleText = '<small class="text-muted" style="vertical-align:' + verticalAlign + ';">#'+ obj.object_uid +'</small> <div class="'+ css +'" style="'+ style + ' ' + underlineTxt +'display:inline-block; vertical-align:' + verticalAlign + ';">'+ obj[fieldName]+'</div>';
			} else {
                if (_.isEmpty(obj[fieldName])) {
                    titleText = '<div class="'+ css +'" style="'+ style + ' ' + underlineTxt +'display:inline-block; vertical-align:' + verticalAlign + ';">Untitled</div>';
                } else {
				    titleText = '<div class="'+ css +'" style="'+ style + ' ' + underlineTxt +'display:inline-block; vertical-align:' + verticalAlign + ';">'+ obj[fieldName]+'</div>';
                }
			}

		} else {
			titleText = '<div class="'+ css +'" style="'+ style + ' ' + underlineTxt +'display:inline-block; margin-bottom:10px;">'+ obj[fieldName]+'</div>';
		}


		
		var icon;
		var parentName = '';
		var show_parent = false;
		var uidTxt = '';
        var placeholder = '';
		
		if (!options.hasOwnProperty('commitUpdates')) { options.commitUpdates = true; }
		
		var fontSize = '';
		
		if (options.fontSize) {
			fontSize = 'font-size:'+ options.fontSize +';';
		}

        if (_.isEmpty(options.placeholder) || _.isEmpty(obj[fieldName]) ) {
			placeholder = 'Untitled';
		}

		if (_.isEmpty(obj[fieldName])) {
			obj[fieldName] = '';
		}

		function editTxt (ui, obj, fieldName) {

			function commitChanges (obj, update, callback, options) {

				if (options.commitUpdates) {

					sb.data.db.obj.update(obj.object_bp_type, update, callback);

				} else {

					callback(update);

				}

			}

			function resizeTextarea (area) {

				var textarea = $(area);

				if (
					textarea === undefined
					|| textarea.get(0) === undefined
				) {

					textarea.css(
						'height'
						, '100px'
					);
					return;
				}

				var newHeight = textarea.get(0).scrollHeight;
				textarea.css(
					'height'
					, 'auto'
				);
				textarea.css(
					'height'
					, textarea.get(0).scrollHeight +'px'
				);

			}
			
			var minHeight = 'min-height:68px;';
			
			if (!_.isEmpty(options.height)) {
				minHeight = 'min-height:'+ options.height +'px;';
			}

			fontSize = 'font-size:2rem;';
			if (options.fontSize) {
				fontSize = 'font-size:'+ options.fontSize +';';
			}

			if (typeof options.onEditStart === 'function')
				options.onEditStart();

			$(ui.selector).html('<textarea rows="1" style="background-color:transparent;outline:none;width:100%;'+ fontSize + style +'resize:none;overflow:hidden;height:auto;" placeholder="'+ placeholder +'" class="ui header">'+ obj[fieldName] +'</textarea>');

			// Adjust height with text input.
			resizeTextarea(ui.selector +' textarea');
			$(ui.selector +' textarea').on('input', function() {

				resizeTextarea(this);

            });

			if (!options.editing) {
				$(ui.selector).children().first().select();
			}

			$(ui.selector).children().first().change(function(val) {

				$(this).val($(this).val().trim());

				if (options.editing) {

					obj[fieldName] = $(this).val();

					commitChanges(obj, {
						id: obj.id,
						[fieldName]: $(this).val()
					}, function (response) {

						if (typeof options.update === 'function') {
							options.update(obj[fieldName]);
						}
						if (typeof options.onUpdate === 'function') {
							options.onUpdate(obj[fieldName], obj);
						}

						View(fieldName, ui, obj, options);
						ui.patch();

						sb.notify({
							type: 'field-updated',
							data: {
								obj: obj,
								type: 'title',
								property: fieldName
							}
						});

					}, options);

				} else if (typeof options.update === 'function') {
					
					options.update(obj[fieldName], [fieldName], $(this).val(), function(response) {
						$(ui.selector).css('background-color', 'transparent');

						if(response){
							obj[fieldName] = response[fieldName];
						}else{
							$(ui.selector).children().first().removeClass('grey');
							$(ui.selector).children().first().addClass('red');
						}

						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();

						View(fieldName, ui, obj, options);
						ui.patch();

						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 		response
								, type: 		'title'
								, property: fieldName
							}
						});

					});

				} else {

					sb.data.db.obj.update(obj.object_bp_type, {
						id:obj.id,
						[fieldName]:$(this).val()
					}, function(response){

						$(ui.selector).css('background-color', 'transparent');

						if(response){
							obj[fieldName] = response[fieldName];
						}else{
							$(ui.selector).children().first().removeClass('grey');
							$(ui.selector).children().first().addClass('red');
						}

						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();

						View(fieldName, ui, obj, options);
						ui.patch();

						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 		response
								, type: 		'title'
								, property: 	fieldName
							}
						});

					});

				}

			});

			if (!options.editing) {

				$(ui.selector).children().first().focusout(function(){

					if (typeof options.onEditEnd === 'function')
						options.onEditEnd();

					View(fieldName, ui, obj, options);
					ui.patch();

				});

			}

		}

		function format_objectName(obj) {

			switch(obj.object_bp_type) {

				case 'users':

					return obj.fname + ' ' + obj.lname;

					break;

				default:
					return obj.name;

			}	

		}

		if (options.editing) {

			ui.makeNode('e', 'div', {});
				
			if (options.uid && obj.object_uid) {
				ui.e.makeNode('uid', 'div', {
					text: '<small class="text-muted">#'+ obj.object_uid +'</small>',
					style: 'margin-bottom:10px;'
				});
			}

			ui.e.makeNode('title', 'div', {});

			ui.e.title.listeners.push(
				function (ui, obj, fieldName) {
					editTxt(ui, obj, fieldName);
				}.bind({}, ui.e.title, obj, fieldName)
			);

		}
		
		if(
			options.parent !== false
		) {

			if(_.isObject(obj[options.parent])) {

				if(
					!_.isEmpty(options.fields)
					&& typeof options.fields[options.parent].shouldShow === 'function'
				) {

					if(options.fields[options.parent].shouldShow(obj)) {
						
						if ( obj[options.parent].hasOwnProperty('id') ) {
							
							show_parent = true;

							parentName = format_objectName(obj[options.parent]);	
							
						}

					}

				} else {

					if(
						options.hasOwnProperty('fields')
						&& options.fields[options.parent].hasOwnProperty('use')
						&& typeof options.fields[options.parent].use === 'function'
					) {

						if ( obj[options.parent].hasOwnProperty('id') ) {
						
							obj.parent = options.fields[options.parent].use(obj);

							if ( obj.parent.hasOwnProperty('id') ) {
								
								show_parent = true;

								parentName = format_objectName(obj.parent);	
								
							}
							
						}
						
					} else {
						
						if ( obj[options.parent].hasOwnProperty('id') ) {

							show_parent = true;
						
							parentName = format_objectName(obj[options.parent]);
							
						}	
						
					}

				}

			} else if(!_.isEmpty(obj.parent)) {

				if ( obj.parent.hasOwnProperty('id') ) {

					show_parent = true;
					parentName = format_objectName(obj.parent);	
					
				}

			}

		}

		if (options.editing) {

			if (
				!_.isEmpty(obj.parent)
				&& obj.parent.name
				&& !appConfig.is_portal
			) {
				
				var pageURL = {
					type: obj.parent.object_bp_type,
					id: obj.parent.id,
					name: parentName,
					group_type: obj.parent.group_type
				};
				
				ui.makeNode('p', 'div', {
					tag: 'span'
					, text: ' <a style="padding:0px !important;padding-left:2px !important;" class="link truncate" href="' + sb.data.url.createPageURL('object', pageURL) + '"> in ' + obj.parent.name +'</a>'
					, style: ''
				});

			}

			return;
		}

		if (
			options
			&& options.entityView
		) {

			var txt = 'Untitled';
			if (!_.isEmpty(obj[fieldName])) {
				txt = obj[fieldName];
			}

			ui.makeNode(
				'txt'
				, 'div'
				, {
					text: 		txt
					, css: 		'ui header'
					, style: 	style + fontSize
				}
			);

			if (typeof options.onClick === 'function') {

				ui.txt.listeners.push(

					function (obj, onClick, selector) {

						$(selector).on('click', function () {
							onClick(obj, selector, true);
						});

					}.bind({}, obj, options.onClick)

				);

			}

			return;

		}

// 		if(obj[fieldName]) {

			if(options && options.fields && options.fields[fieldName] && options.fields[fieldName].hasOwnProperty('charLimit')){

				titleText = '<div class="'+ css +'" style="'+ style + ' ' + underlineTxt +'display:inline-block;">'+ obj[fieldName].slice(0, options.fields[fieldName].charLimit) +'</div>';

			}

			titleText = uidTxt + titleText;

			var href = sb.data.url.getObjectPageParams(obj, options);

			if (obj.hasOwnProperty('link')) {
				href = obj.link;
			}

			if (typeof options.link === 'function') {
				href = options.link(obj);
			} else if(options.link === false) {
				href = '';
			}

			if(options.fields && options.fields[fieldName] && options.fields[fieldName].counter && options.counter !== false){

				if(obj.hasOwnProperty('comment_count')){

					var commentLabel =

						'<div class="right floated" style="margin-left:6px;margin-top:-5px;">'+
							'<i class="comments icon" style="color:rgb(150,150,150);display:inline-block;padding-top:8px;"></i> '+ obj.comment_count +
					    '</div>';

					ui.makeNode('label', 'div', {css: 'right floated', text:commentLabel});

				}
			}

			var htag = 'div';
			if (options.tag) {
				htag = options.tag;
			}

			var titleDiv = '<'+ htag +' class="ui header '+ headerCss +'" style="'+ style + fontSize +' display:inline-block;">'+ titleText +'</'+ htag +'>';
			
			if(options.fields && options.fields[fieldName] && options.fields[fieldName].hasOwnProperty('icon')){

				if(typeof options.fields[fieldName].icon == 'function'){

					icon = options.fields[fieldName].icon(obj);

					if(!_.isNull(icon) && !_.isUndefined(icon))
						titleDiv = '<'+ htag +' class="ui header" style="'+ style + fontSize +' display:inline-block;">'+ icon +' '+ titleText +'</'+ htag +'>';

				}else if(_.isObject(options.fields[fieldName].icon)){

					icon = '<i class="'+ options.fields[fieldName].icon.type +' icon"></i>';

					if(options.fields[fieldName].icon.hasOwnProperty('color'))
						icon = '<i class="'+ options.fields[fieldName].icon.color +' '+ options.fields[fieldName].icon.type +' icon"></i>';

					titleDiv = '<'+ htag +' class="ui header" style="'+ style + fontSize +' display:inline-block;">'+ icon +' '+ titleText +'</'+ htag +'>';

				}

			}

			ui.makeNode('t', 'div', {
				tag: 'span'
			});

			if (options.link == false) {
				
				ui.t.makeNode('name', 'div', {
					text: titleDiv,
				});

                ui.t.name.listeners.push(function(selector){

                    $(selector).on('click', function(e){
    
                        if(options.edit){
    
                            e.stopPropagation();
                            e.preventDefault();
    
                            editTxt (ui, obj, fieldName);
    
                        }
    
                    });
    
                });

			} else if (typeof options.onClick === 'function') {

				ui.t.makeNode('name', 'div', {
					tag: 'a',
					text: titleDiv,
                    style: 'cursor:pointer;'
				}).listeners.push(

					function (obj, onClick, selector) {

						$(selector).on('click', function () {
							onClick(obj, selector);
						});

					}.bind({}, obj, options.onClick)

				);

			} else {

                var divOpts = {
					tag:        'a'
					, text:     titleDiv
                    , style:    'display:inline-block;'
                    , href:     href
				};
                
                if ( options.cellActions && !_.isEmpty(options.cellActions ) ) {

                    divOpts.css = 'table-view-title-cell';
                    divOpts.style = 'display:inline-block; cursor:text;';
                    delete divOpts.href;

                    ui.t.makeNode('cell', 'div', {
                        isTabbable: true
                        , css:'item revealParent' 
                        , style:'padding:4px; margin-top:2px; border:none; position:relative;'
                    });
                    
                    if ( options.cellActions.view ) {

                    	var _label = options.cellActions.label ? options.cellActions.label : 'View';

                    	var _visibility = options.cellActions.forcedVisible ? '' : 'revealChild';

						var viewBtnNode = ui.t.cell.makeNode('view', 'div', {
							css: 		'ui right floated mini button ' + _visibility
							, text:     '<i class="expand alternate icon"></i>  ' + _label
							, style: 	'position:absolute; top: .1em; right:0em; background: #e8e8e8; color:rgba(175, 175, 175, 1) !important; cursor:pointer;'
						});

                    	if(obj && obj.id && obj.id.toString().startsWith('tmp-')){
                    		viewBtnNode = ui.t.cell.makeNode('view', 'div', {
								css: 		'loading ui right floated mini button ' + _visibility
								, text:     '<i class="expand alternate icon"></i>  ' + _label
								, style: 	'position:absolute; top: .1em; right:0em; background: #e8e8e8; color:rgba(175, 175, 175, 1) !important; cursor:pointer;'
							});
						}

						if(!(obj && obj.id && obj.id.toString().startsWith('tmp-'))) {
							viewBtnNode.listeners.push(function (selector) {

								$(selector).on('click', function (e) {

									e.stopPropagation();
									e.preventDefault();

									sb.notify({
										type: 'get-sys-modal'
										, data: {
											callback: function (modal) {

												modal.body.empty();
												modal.show();

												modal.patch();

												sb.notify({
													type: 'view-entity'
													, data: {
														ui: modal.body
														, id: obj.id
														, hideTags: true
														, hideComments: true
														, onComplete: options.cellActions.view.onComplete
														, onUpdate: options.cellActions.view.onChange
													}
												});

											},
											onClose: options.cellActions.view.onClose
										}
									});

								});

							});
						}
                    }

                    var titleNode = ui.t.cell.makeNode('name', 'div', divOpts); 
                    
                } else {
                    var titleNode = ui.t.makeNode('name', 'div', divOpts);

                    titleNode.listeners.push(function(selector){

                        $(selector).on('click', function(e){

                            if(options.edit){
        
                                e.stopPropagation();
                                e.preventDefault();
        
                                editTxt (ui, obj, fieldName);
        
                            }
        
                        });
        
                    });
                }

			}

			// Hide parent links in portals, to help keep users locked in
			if (appConfig.is_portal) {
				show_parent = false;
			}

			if(_.isObject(obj[options.parent])) {

				if(obj[options.parent].hasOwnProperty('id') && show_parent === true) {

					var pageURL = {
							type: obj[options.parent].object_bp_type,
							id: obj[options.parent].id,
							name: parentName,
							group_type: obj[options.parent].group_type
						};

					if(options.onMobile) {

						ui.t.makeNode('parent', 'div', {
							tag: 'span',
							text: ' <nobr><a style="padding:0px !important; font-size:12px;" class="link truncate" href="' + sb.data.url.createPageURL('object', pageURL) + '"> in ' + parentName +'</a></nobr>',
							style: 'margin-left: 5px;'
						});

					} else {

						ui.t.makeNode('parent', 'div', {
							tag: 'span',
							text: ' <nobr><a style="padding:0px !important; font-size:12px;" class="link truncate" href="' + sb.data.url.createPageURL('object', pageURL) + '"> in ' + parentName +'</a></nobr>'
						});

					}

				}

			}

// 		}

	}

	function Edit (fieldName, ui, obj, options) {
        var el = document.querySelector(ui.selector).parentNode;

        var inputHeight = options.inputHeight ? options.inputHeight : el.offsetHeight + 'px;';
		var inputContainerStyle = options.inputContainerStyle;
        var placeholder = '';

        if (!_.isEmpty(obj.name))
            placeholder = obj.name;

        ui.makeNode('inputContainer', 'div', {
            css: 'ui fluid input',
			style: inputContainerStyle
        });
        ui.inputContainer.makeNode('input', 'div', {
            tag:'input', 
            type:'text',
            placeholder: placeholder,
            style: 'background-color:rgb(245, 245, 245) !important; border:0px !important; font-weight: 700; height:' + inputHeight,
			value: obj.name
        })

	}

	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'title',
					title: 'ID',
					icon: 'id badge',
					view: View,
					edit: Edit,
					options: {
						placeholder: {
							type: 	'string',
							name: 'Placeholder'
						},
						parent: {
							type: 'bool',
							name: 'Show parent?'
						},
						uid: {
							type: 'bool',
							name: 'Show uid?'
						}
					},
                    blockRefresh: 			false,
					usePopup: true,
					focus: function (field, ui) {
                        $(ui.inputContainer.input.selector).focus();
                    },
                    focusout: function( fieldName, ui, obj, options ){

                        updated = $(ui.inputContainer.input.selector).val();

                        if ( obj.name != updated ) {
                            var updatedObj = _.clone(obj);
        
                            updatedObj.name = updated;
        
                            sb.data.db.obj.update(obj.object_bp_type, updatedObj, function(resp){

                                var dataOpt = {
                                    obj: updatedObj,
                                    type: 'title',
                                    property: fieldName
                                };
        
                                sb.notify({
                                    type: 'field-updated',
                                    data: dataOpt
                                });
        
                            });
                        }
                    },
					select: {
						object_uid: true
					}/*
,
					detail: function (key, obj, options) {
						return obj[key];
					}
*/
				}
			});

			if (IN_NODE_ENV) {
				var title = View;
				module.exports = { 
					title
				}
			}

		}

	}

});

if (IN_NODE_ENV) {
	Factory.startAll();
}
