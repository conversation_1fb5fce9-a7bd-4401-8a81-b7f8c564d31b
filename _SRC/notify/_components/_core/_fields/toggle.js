var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register ('toggle-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	// MISC 
	
	function getHtml (val, options) {

		switch (val) {
			
			case true:
			case 'true':
			case 'done':

				if (options.hasOwnProperty('formType')) {
					
					if (options.formType === 'toggleCheckbox') {
						
						return '<div class="inline field">' +
							'<div class="ui toggle checkbox">' +
								'<input type="checkbox" tabindex="0" class="hidden">' +
								'<label>' + options.label + '</label>'
							'</div>'
						'</div>';
						
					}
					
				}
				
				var ret = ( !options.is_portal ) ? '<i class="large square check icon bento-checked"></i>' : '<i class="large circle check green icon bento-checked"></i>';
				if(options.includeLabel){
					ret += ' '+options.label;
				}
				
				return ret;
				
			default:

				if (options.hasOwnProperty('formType')) {
					
					if (options.formType === 'toggleCheckbox') {
						
						return '<div class="inline field">' +
							'<div class="ui toggle checkbox">' +
								'<input type="checkbox" tabindex="0" class="hidden">' +
								'<label>' + options.label + '</label>'
							'</div>'
						'</div>';
						
					}
					
				}
			
				var ret = ( !options.is_portal ) ? '<i class="large square outline icon bento-unchecked"></i>' : '<i class="large circle minus yellow icon bento-unchecked"></i>';
				if(options.includeLabel){
					ret += ' '+options.label;
				}
				
				return ret;
			
		}
		
	}

	function getTxt (val, options) {

		switch (val) {
			
			case true:
			case 'true':
			case 'done':
				return 'Yes';
				
			default:
				return 'No';
			
		}
		
	}
	
	function toggle (fieldName, ui, obj, options) {

		if ( obj[fieldName] == 'open' ) {
			obj[fieldName] = 'done';
		} else if ( obj[fieldName] == 'done' ) {
			obj[fieldName] = 'open';
		} else {
			obj[fieldName] = !obj[fieldName];
		}

		$(ui.t.selector).html(getHtml(obj[fieldName], options));

		if (typeof options.onUpdate === 'function') {
			options.onUpdate(obj[fieldName]);
		}

		function getNarrativeOptions (options, fieldName) {
			if(options && options.blueprint && options.blueprint[fieldName] && options.blueprint[fieldName].options && options.blueprint[fieldName].options.relatedNarrativeOptions){
				let optionsNew = options.blueprint[fieldName].options.relatedNarrativeOptions.split('\r\n');

				optionsNew = _.map(optionsNew, function(item){
					item = item.replaceAll("^", "\r\n");
					return item;
				});

				return optionsNew;

			}
			return false;
		}

		if ( options.commitUpdates ) {
			

			var field = {[fieldName]: 	obj[fieldName]};
			if(options.relatedNarrativeField){
				var narrativeOptions = getNarrativeOptions(options, fieldName);
				if(narrativeOptions) {
						var newValue = obj[fieldName] == true? narrativeOptions[1] : narrativeOptions[0];
						field = Object.assign(field, {[options.relatedNarrativeField] : newValue});
					}
				}
			}

			sb.data.db.obj.update(
				obj.object_bp_type
				, {
					id: obj.id
					, ...field
				}
				, function (r) {
					

					ui.empty();
					View(
						fieldName
						, ui
						, obj
						, options
					);
					ui.patch();

					obj[fieldName] = r[fieldName];
					
					if (
						options.fireFieldUpdated === undefined
						|| options.fireFieldUpdated === true
					) {
						
						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 			obj
								, type: 		'toggle'
								, property: 	fieldName
							}
						});
					}

					if(options.blueprint) {
						//update narrative field
						_.each(options.blueprint, function (field, key) {
							if (key === options.relatedNarrativeField) {
								sb.notify({
									type: 'get-field-id'
									, data: {
										obj: {
											id: obj.id
											, [key]: r[key]
										}
										, type: field.fieldType
										, property: key
										, callback: function (fieldId) {
											if (fieldId && fieldId[0]) {
												$('#' + fieldId[0]._id + ' .ql-editor').html(r[key]);
											}
										}
									}
								});
							}
						});
					}
			
		}
			);

	}
	
	// VIEWS 
	
	function View (
		fieldName
		, ui
		, obj
		, options
	) {

		// Presents a checkbox tied to the bool value located on the 'obj' 
		// at the given key 'fieldName'.
		// @zach, 3/21/20
		
		// fieldName 	String 	| the key for the field 
		// ui 			UiObj 	| the container to present the view within
		// obj 			Object 	| the object the field exists on 
		// options 		Object 	| configuration vals
		// 					.edit 	Bool | whether the view should allow edits
		// 								   to 'obj'
		
		var txt = '';
		var css = 'toggle-field';
		var style = '';
		
		if (options.editing || options.edit) {
			css += ' clickable';
		}
		if (options.inCollection) {
			style = 'padding:0px;';
		}
		if (options.overallStatus) {
			txt = getHtml((obj.status === 'done'), options);
		} else {
			txt = getHtml(obj[fieldName], options);
		}

		if (!ui) {

			txt = getTxt(obj[fieldName], options);
			if (typeof options.callback === 'function') {
				options.callback(txt);
			}

			return;

		}
		
		ui.empty();
		ui.makeNode(
			't'
			, 'div'
			, {
				text: 		txt
				, css: 		css
				, style: 	style
			}
		);
		
		ui.patch();

		if (options.editing || options.edit) {
			
			ui.t.listeners.push(
				function (selector) {

					$(selector).on(
						'click'
						, toggle.bind({}, fieldName, ui, obj, options)
					);
					
					if (options.hasOwnProperty('formType')) {
					
						if (options.formType === 'toggleCheckbox') {
							
							if ( 
								obj[fieldName] == 'open'
								|| obj[fieldName] == true  
							) {
								
								$('.ui.checkbox').checkbox().first().checkbox('set checked');
								
							} else {
								
								$('.ui.checkbox').checkbox().first().checkbox('set unchecked');
								
							}
							
						}
						
					}
					
				}
			);
			
		}
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					
					// Config
					availableToEntities: 	true
					, css: 					' '
					, icon: 				'check square outline'
					, name: 				'toggle'
					, propertyType: 		'bool'
					, title: 				'Toggle'
					
					// Views
					, view: 				View
					, edit: 				function (fieldName, ui, obj, options) {
							
						View(
							fieldName
							, ui
							, obj
							, options
						);
						
					}
					, detail: 				function (key, obj, opts) {
						
						if (
							opts 
							&& opts.fields
							&& opts.fields[key]
						) {
							return opts.fields[key].title;
						}
						
						return false;
						
					}
					, useFieldListener: true
					, options: {
						relatedNarrativeField: {
							name: 'Related Narrative Field',
							type: 'field',
							allowedTypes: ['detail'],
							multi: false
						},
						relatedNarrativeOptions: {
							name: 	'Narrative Options',
							type: 'text'
						},
					}
				}
			});
			
		}
		
	};
	
}) ;
