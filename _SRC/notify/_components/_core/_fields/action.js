var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('actions-fields', function (sb) {
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 		'actions-set'
					, view: 	function (fieldName, ui, obj, options) {
						
						sb.notify({
							type: 'show-actions-menu'
							, data: {
								ui: 		ui
								, actions: 	options.actions
								, obj: 		obj
							}
						});
						ui.patch();
						
					}
				}
			});
			
		}
		
	};
	
});