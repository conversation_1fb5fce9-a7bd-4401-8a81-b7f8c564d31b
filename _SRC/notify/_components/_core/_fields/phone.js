var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('phone-field', function (sb) {
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = obj[fieldName] ? obj[fieldName] : '';

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;
			
		}
		
		function formatPhoneNumber(string) {
			
			if (
				typeof string !== 'string'
			) {
				string = '';
			}

			var base = string.replace(/\D/g,'');
			var areaCode = base.substring(0, 3);
			var p1 = base.substring(3, 6);
			var p2 = base.substring(6, 10);
			var ret = '';
			
			if (!_.isEmpty(areaCode)) {
				ret = areaCode;
				
			}
			if (!_.isEmpty(p1)) {
				ret = '('+ ret +')';
				ret += ' '+ p1;
			}
			if (!_.isEmpty(p2)) {
				ret += '-'+ p2;
			}
			
			return ret;
			
		}
		
		var value = formatPhoneNumber(obj[fieldName]);

		var canEdit = true;
		if(options && options.edit === false){
			canEdit = false;
		}
		
		ui.makeNode(
			'f'
			, 'div'
			, {
				css: ''
				, text: '<input value="'+ value +'" class="text" placeholder="(###) ### - ####" style="border:none;background-color:transparent;outline:none;width:100%;">'
			}
		).listeners.push(
			function (selector) {
				
				$(selector)
					.on('input', function () {
						
						var val = $(selector +' input').val();
						$(selector +' input').val(
							formatPhoneNumber(val)
						);
						
					});
					
				$(selector)
					.on('change', function () {
						
						var val = $(selector +' input').val();
						obj[fieldName] = val;
						
						if (options.commitUpdates) {
							
							sb.data.db.obj.update(
								obj.object_bp_type
								, {
									id: 				obj.id
									, [fieldName]: 	val
								}
								, function (r) {}
								, {
									[fieldName]: true
								}
							);
							
						}
						
					});

					if(!canEdit){
					$(selector+' input').attr('readonly', 'readonly');	
					}
				
			}
		);
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 					'phone'
					, type: 					'Contact Info'
					, view: 					View
					, title: 				'Phone'
					, availableToEntities: 	true
					, icon: 					'phone'
					, propertyType: 			'string'
					, options: {}
				}
			});

			if (IN_NODE_ENV) {
				var phone = View;
				module.exports = { 
					phone
				}
			}
			
		}
		
	}
	
});

if (IN_NODE_ENV) {
	Factory.startAll();
}