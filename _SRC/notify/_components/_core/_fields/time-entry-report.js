var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('time-entry-report-field', function (sb) {

    if (IN_NODE_ENV) {
        return;
    }

    function View (fieldName, ui, obj, opts) {

        function getData (startFieldKey, endFieldKey, onComplete) {

            // If a date field is passed in in opts, use that as the 
            // date to query on
            var startFieldVal = moment(obj[startFieldKey]);
            var endFieldVal = moment(obj[endFieldKey]);

            var start = startFieldVal.local().startOf('day').unix();
            var end = 	endFieldVal.local().endOf('day').unix();
            var queryObj = {
                start_date: {
                    type: 		'between'
                    , start: 	start
                    , end: 		end
                }
                , childObjs: {
                    start_date: true
                    , end_date: true
                    , shift: {
                        name: 	true
                    }
                    , note: 	true
                    , duration: true
                    , staff: 	'id'
                }
                , _dont_force_portal_user_tag: true
            };
            
            // Get time entries
            sb.data.db.obj.getWhere('time_entries', queryObj, function(entries) {
                
                // Get staff members
                sb.data.db.obj.getById('users', _.chain(entries).pluck('staff').compact().uniq().value(), function (staff) {

                    // Get tags to breakout hrs by
                    sb.data.db.obj.getById('', opts.breakoutTags, function (breakoutTags) {

                        onComplete({
                            entries:            entries
                            , staff:            staff
                            , breakoutTags:     breakoutTags
                        });

                    });

                });
                
            });

        }

        function getReportHtml (data) {

            function getTimeTxt (duration) {

                var tempTime = moment.duration(duration);
                var days = tempTime.days();
                var durTxt = (tempTime.hours() + 24*days) +'h '+ tempTime.minutes() +'m';

                return durTxt;

            }

            var html = '<table class="ui very basic simple celled table"><tbody>';
            var totalDur = 0;
            var breakoutTotals = { other: 0 };
            _.each(data.breakoutTags, function (tag) { breakoutTotals[tag.id.toString()] = 0; });

            // Table header
            html += '<thead><tr>'+
                        '<th>Team Member</th>';
            
            _.each(data.breakoutTags, function (tag) {

                var name = tag.tag;
                if (!_.isEmpty(tag.name) && _.isEmpty(tag.tag)) {
                    name = tag.name;
                }
                html += '<th>'+ name +'</th>';

            });

            html +=     '<th>Other</th>'+
                        '<th>Total Hrs</th>'+
                    '</tr></thead>';

            _.each(data.staff, function (staffMember) {

                var duration = 0;
                var memerLogs = _.where(data.entries, {staff: staffMember.id});
                var staffTagLogs = { other: 0 };
                _.each(data.breakoutTags, function (tag) { staffTagLogs[tag.id.toString()] = 0; });

                _.each(memerLogs, function (log) {
                    
                    var added = false;
                    duration += parseInt(log.duration)*1000;
                    totalDur += parseInt(log.duration)*1000;

                    // Set tag breakout
                    _.each(data.breakoutTags, function (tag) {

                        if (added) { return; }
                        if (_.contains(log.tagged_with, tag.id)) {

                            staffTagLogs[tag.id.toString()] += parseInt(log.duration)*1000;
                            breakoutTotals[tag.id.toString()] += parseInt(log.duration)*1000;
                            added = true;

                        }

                    });

                    if (added === false) {
                        staffTagLogs.other += parseInt(log.duration)*1000;
                        breakoutTotals.other += parseInt(log.duration)*1000;
                    }
                    
                });

                var durTxt = getTimeTxt(duration);
                html += '<tr>'+
                            '<td>'+ staffMember.name +'</td>';
                
                _.each(data.breakoutTags, function (tag) {

                    var durTxt = getTimeTxt(staffTagLogs[tag.id.toString()]);
                    html += 
                            '<td>'+ durTxt +'</td>';

                });

                var otherDurTxt = getTimeTxt(staffTagLogs.other);

                html += 
                            '<td>'+ otherDurTxt +'</td>'+
                            '<td>'+ durTxt +'</td>'+
                        '</tr>';

            });

            // Totals row at the bottom
            var totDurTxt = getTimeTxt(totalDur);
            var otherDurTxt = getTimeTxt(breakoutTotals.other);

            html += '<tr>'+
                        '<td></td>';

            _.each(data.breakoutTags, function (tag) {

                var durTxt = getTimeTxt(breakoutTotals[tag.id.toString()]);
                html += 
                        '<td><strong>'+ durTxt +'</strong></td>';

            });
            
            html +=     '<td><strong>'+ otherDurTxt +'</strong></td>'+
                        '<td><strong>'+ totDurTxt +'</strong></td>'+
                    '</tr>';

            html += '</tbody></table>';
            return html;

        }

        // Parse options
        var startFieldKey = false;
        var endFieldKey = false;
        var breakoutTags = false;
        if (
            typeof opts.start === 'string'
            && obj[opts.start]
        ) {
            startFieldKey = opts.start;
        }
        if (
            typeof opts.end === 'string'
            && obj[opts.end]
        ) {
            endFieldKey = opts.end;
        }
        if (typeof opts.breakoutTags === 'array') {
            breakoutTags = opts.breakoutTags;
        }

        // Error messages
        if (fieldName === '_default') {
            ui.makeNode('msg', 'div', {text: 'No default value to set for this field type.', css: 'ui message'});
            return;
        } else if (
            startFieldKey === false
            || endFieldKey === false
        ) {
            ui.makeNode('msg', 'div', {text: 'Need a valid date range.', css: 'ui message'});
            return;
        }

        // Place loader
        ui.empty();
		ui.makeNode('loader', 'div', {
			css: 		''
			, text: 	'<div class="ui hidden divider"></div><br /><br /><i class="notched circle loading icon"></i>'
			, style: 	'width:100%;height:100%;text-align:center;'
		});
		ui.patch();

        getData(startFieldKey, endFieldKey, function (data) {

            var html = getReportHtml(data);
            ui.empty();
            ui.makeNode('content', 'div', {text: html});
            ui.patch();

        });

    }

    return {
    
        init: function () {

            sb.notify({
				type: 'register-field-type'
				, data: {
					name: 					'time-entry-report'
					, view: 				View
					, title: 				'Hours Logged Report'
					, availableToEntities: 	true
					, icon: 				'clock'
					, propertyType:         'object'
					, objectType:           ''
					, options: {
                        // context: {

                        // },
                        start: {
							name: 	        'Start Date'
							, type:         'field'
							, allowedTypes: ['date']
                            , multi:        false
						}
                        , end: {
							name: 	        'End Date'
							, type:         'field'
							, allowedTypes: ['date']
                            , multi:        false
						}
						, breakoutTags: {
							name: 	        'Breakout Tags'
							, type:         'tags'
						}
					}
				}
			});

        }

    };

});