var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register ('next-due-date-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	// View
	function View (fieldName, ui, obj, options) {
// console.log('next-due-date-args', arguments);
		var set = false;
		var fieldReference = false;
		// Determine if the field is setup properly
		var isSetup = true;
		var isLocked = false;
		
		if(options.blueprint){
			var fieldOptions = options.blueprint[fieldName].options;
		}else{
			var fieldOptions = options;
		}

		ui.empty();
				
		// Show validation button
		if ( isSetup ) {
			
			options.is_due_date = true;
			options.edit = false;
			options.editing = false;
			
			if(obj[fieldName+'_refId'] && options.blueprint){
				
				if(options.blueprint[obj[fieldName+'_refId']]){
					
					//ui.makeNode('refId','div',{text:options.blueprint[obj[fieldName+'_refId']].name});
					ui.makeNode('cont','div',{css:''});
					
					ui.patch();

					// Show the field
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'date',
							property: fieldName,
							obj: obj,
							options: options,
							ui: ui.cont
						}
					});	
					
				}else{
					
					// Show the field
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'date',
							property: fieldName,
							obj: obj,
							options: options,
							ui: ui
						}
					});	
					
				}
				
			}else{
				
				// Show the field
				sb.notify ({
					type: 'view-field',
					data: {
						type: 'date',
						property: fieldName,
						obj: obj,
						options: options,
						ui: ui
					}
				});	
				
			}
						
			ui.patch();
			
		} else {
			
			ui.makeNode('i-'+ obj.id, 'div', {
				text: '<span class="ui text red"><i class="exclamation triangle icon"></i> Error: Please setup all required settings on this field.</span>',
				isTabbable: true
			});
			
		}

	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'next-due-date',
					view: View,
					title: 'Next Due Date',
					//type: 'General',
					availableToEntities: true,
					icon: 'exclamation',
					getIcon: function (options) {
						
						if ( !_.isEmpty(options.set) ) {
							
							var set = _.findWhere(appConfig.Types, {bp_name: options.set.substr(1)});
							var fieldReference = set.blueprint[options.field];
							
							if (fieldReference) {
								
								var icon = _.findWhere(appConfig.Fields, {name: fieldReference.fieldType}).icon;
							
								return icon;	
								
							}
							
						} else {
							
							return 'sync';
							
						}
						
					},
					options: {
						aggregationType: {
							name:'Aggregation Type',
							type:'select',
							options:[
								{
									name:'Most Urgent',
									value:'urgent'
								},
								{
									name:'Least Urgent',
									value:'least_urgent'
								}
							]
						}
						, is_due_date: {
							type: 		'bool'
							, name: 	'Is due date?'
						}
						, green: {
							type: 		'bool'
							, name: 	'Use green color for items due in the future?'
						}
						, yellow: {
							type: 		'string'
							, name: 	'Number of days before to highlight in yellow?'
						}
						, red: {
							type: 		'string'
							, name: 	'Number of days before to highlight in red?'
						}
						
					}
				}
			});
			
		}
		
	};
	
});
