var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('html-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }

	function View (fieldName, ui, obj, options) {
		
		sb.notify ({
			type: 'view-field',
			data: {
				type: 'detail',
				property: fieldName,
				obj: obj,
				options: {
					edit: false,
					editing: false,
					commitUpdates: false,
					alwaysMerge: options.alwaysMerge,
					promptSave: false,
					previewAndEdit: false,
					html_string: options.html_string
				}
				, ui: ui
			}
		});
		
	}

	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'html',
					title: 'HTML',
					availableToEntities: true,
					icon: 'code',
					options: {
						html_string: {
							type: 'detail',
							name: 'HTML Code',
							options: {
								edit: true,
								editing: true,
								commitUpdates: false,
								alwaysMerge: false,
								useMedium: true,
								header: false,
								labelTxt: '',
								mentions: [],
								promptSave: false,
								previewAndEdit: false,
								css: 'round-border',
								style: 'min-height:250px;'
							}
						},
						alwaysMerge: {
							name: 'Allow merge tags?',
							type: 'bool'
						}
					},
					view: View,
					propertyType: 'string',
					detail: function (key, obj, options) {
						
						if (options.inCollection) {
							return;							
						}
						
					},
					useFieldListener: true,
					hideDefault: true
				}
			});

		}

	}

});
