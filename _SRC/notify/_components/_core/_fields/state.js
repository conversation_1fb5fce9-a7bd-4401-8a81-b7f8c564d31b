var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('state-field', function (sb) {

	function View (fieldName, ui, obj, options) {

        var labelStyle = false;

        if (options.inTableField)
            labelStyle = true; 

        if ( ui )
            ui.empty();
		// Set the workflow obj
		var workflowObj = false;
		if ( options.hasOwnProperty('blueprint') ) {
			if ( options.blueprint ) {
				if ( options.blueprint.hasOwnProperty([fieldName]) ) {
					if ( options.blueprint[fieldName] ) {
						if ( options.blueprint[fieldName].hasOwnProperty(['workflow']) ) {
							if ( options.blueprint[fieldName].workflow ) {
								if ( options.blueprint[fieldName].workflow.hasOwnProperty(['states']) ) {
									if ( options.blueprint[fieldName].workflow.states ) {
										workflowObj = options.blueprint[fieldName].workflow;
									}
								} else if ( obj ) {
									if ( obj.hasOwnProperty('type') ) {
										if ( obj.type ) {
											if ( obj.type.hasOwnProperty('states') ) {
												if ( obj.type.states ) {
													workflowObj = obj.type;
												}
											}
										}
									}
								}
							}
						} else {
							return;
						}
					}
				}
			}
		} else if ( obj.hasOwnProperty('type') ) {
			if ( obj.type ) {
				if ( obj.type.hasOwnProperty('states') ) {
					if ( obj.type.states ) {
						workflowObj = obj.type;
					}
				}
			}
		}
		
		if (!ui) {

			// Get the states
			var states = options.blueprint[fieldName].workflow.states;

			// if value is undefined default button state and color will be set
			var defaultSelection = _.findWhere(states, {uid: parseInt(obj[fieldName])});

			// current state saved on object or defaults to isEntryPoint
			var currentState = defaultSelection || _.findWhere(states, {isEntryPoint:1});

            if ( labelStyle ){

                var labelColor = (currentState.color) ? currentState.color : 'grey';
                var lableIcon = (currentState.icon) ? currentState.icon : 'circle outline';

                ret = '<div class="ui ' + labelColor + ' label">' +
                    '<i class="'+ lableIcon +' icon"></i>'+ currentState.name +
                '</div>';

            } else {
                // Return the name
                ret = currentState.name;
            }

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;
			
		}

		// Helper Functions
		function getColor(color) {

			if ( sb.dom.colorsRGBToName[color] ) {
				color = sb.dom.colorsRGBToName[color];
			}

			return color;

		}

		function getIcon(icon) {

			var iconArray = {
				css: '',
				text: ''
			};

			if ( !_.isEmpty(icon) ) {
				iconArray.css = 'icon ';
				iconArray.text = '<i class="'+ icon +' icon"></i>';
			}

			return iconArray;

		}

		function confirmTransitionWithUser (state, onConfirm) {

            if (state.requiresConfirmation && !appConfig.is_portal) {

				sb.data.db.obj.getWhere(
					'event_type'
					, {
						object: 	workflowObj.id
						, state: 	parseInt(state.uid)
					}
					, function (actions) {

                        var html = '';
						if (!_.isEmpty(actions)) {

							html = 'Transitioning to <i class="ui '+ state.color +' '+ state.icon +' icon"></i> <strong>'+ state.name +'</strong> triggers the following actions:';
							html += '<ul>';
							_.each(actions, function (action) {
								html += '<li>'+ action.name +'</li>';
							});
							html += '</ul>';

						}

						sb.dom.alerts.ask({
							title: 'Are you sure?',
							text: '',
							html: html,
							primaryButtonText: 		'Transition, but don\'t trigger actions',
							secondaryButtonText: 	'Transition',
							cancelButtonText: 		'Cancel'
						}, function(primaryButtonClicked, secondaryButtonClicked){

                            if (primaryButtonClicked || secondaryButtonClicked) {

								swal.disableButtons();
								onConfirm({
									runTransition: 		(primaryButtonClicked || secondaryButtonClicked)
									, triggerActions: 	(!primaryButtonClicked)
								});

							} else {

								swal.disableButtons();
								onConfirm({
									runTransition: 		false
									, triggerActions: 	false
								});

							}

						});

					}
				);

			} else {

				onConfirm({
					runTransition: 		true
					, triggerActions: 	true
				});

			}

			return;

		}


		function confirmDialog(_callback){

			if(appConfig.is_portal) {
				sb.dom.alerts.ask({
					primaryButtonText: 'Submit'
					, cancelButtonText: 'Cancel'
					, title: 'Are you sure?'
					, text: 'Once your form is submitted, it cannot be updated.'
				}, function (r) {

					swal.close();
					if (r) {
						_callback();
					}

				});
			}
		}

		// Form Button UI
		function formButtonUI (fieldName, ui, obj, options) {

			function updateState(flowState, callback, verbose) {
					if (options.commitUpdates || !options.hasOwnProperty('commitUpdates')) {

						// Show loader
						$("#loader").fadeIn();

						// Set link
						var link = sb.data.url.createPageURL(
							'object',
							{
								type: 'state',
								id: obj.id,
								name: obj.name
							}
						);

						if (flowState && currentState.type !== 'done') {
							// if (flowState) {

							confirmTransitionWithUser(flowState, function (confirmation) {

									if (confirmation.runTransition) {

										sb.data.db.obj.updateState({
											objectId: obj.id,
											newState: parseInt(flowState.uid),
											link: link,
											stateProperty: fieldName,
											supressActions: !confirmation.triggerActions
										}, function (updatedObj) {

											if (appConfig.instance === 'foundation_group') {
												buildFormNotification(obj.id, flowState, sb);
											}

											obj[fieldName] = updatedObj[fieldName];
											// Update all state fields and trackers
											sb.notify({
												type: 'field-updated',
												data: {
													obj: {
														id: obj.id,
														[fieldName]: updatedObj[fieldName],
														status: updatedObj.status
													},
													type: 'state',
													property: fieldName
												}
											});

											// if (updatedObj[fieldName] == parseInt(flowState.uid)) {
											callback(updatedObj);
											// }

											// Hide loader
											$("#loader").fadeOut();

											//!#1284: Get the event_type records where:
											// - state = parseInt(flowState.uid)
											// - object = workflowObj.id
											// - requires_input = true
											// These are the actions that we need to pop up a form for for the user right after state is changed.

											// !TODO: Use the event_type records above to open the forms w/the 'get-action-options-form'
											// Factory.triggerEvent({
											// 	type: 'get-sys-modal'
											// 	, data: {
											// 		callback: function (modal) {

											// 			modal.empty();
											// 			modal.show();

											// 			Factory.triggerEvent({
											// 				type: 'get-action-options-form'
											// 				, data: {
											// 					ui: 				modal
											// 					, obj: 				response.response !-> context obj (project)
											// 					, notifications: 	response.msg.notifications !-> event_type records
											// 				}
											// 			});

											// 		}
											// 	}
											// });

										}, verbose);

									} else {

										// Hide loader
										$("#loader").fadeOut();

									}

								}
							);

						} else {

							callback(obj);

							// Hide loader
							$("#loader").fadeOut();

						}

					}
			}

			function onStateTransition (response) {

				var obj = response;
				if (response.response && response.response.id) {
					obj = response.response;
				}
				var passed = true;
				if (response.type === 'error') {
					passed = false;
				}

				if (passed) {

					ui.empty();
					obj['any-state'] = obj.status;
					sb.notify({
						type: 'field-updated'
						, data: {
							obj: 		obj
							, property: fieldName
						}
					});
					sb.notify({
						type: 'field-updated'
						, data: {
							obj: 		obj
							, property: 'any-state'
						}
					});

				}

				if (
					typeof options.toNextInList === 'function'
					&& passed
				) {

					options.toNextInList(obj);

				} else if (passed === false) {

					sb.dom.alerts.ask({
						icon: 'error',
						title: 'STOP!',
						text: 'This section has not been completed. Are you sure you want to move to the next section? Note: You can return to this section later.',
						primaryButtonText: 'Yes',
						cancelButtonText: 'No'
					}, function(resp) {
						swal.close();
						if (resp ) {
                            if ( options.toNextInList && typeof options.toNextInList == 'function') 
                                options.toNextInList();
						}
					});

				}

			}

			// Set states
			var states = {};
			if ( options.hasOwnProperty('blueprint') ) {
				if ( options.blueprint ) {
					if ( options.blueprint.hasOwnProperty([fieldName]) ) {
						if ( options.blueprint[fieldName] ) {
							if ( options.blueprint[fieldName].hasOwnProperty(['workflow']) ) {
								if ( options.blueprint[fieldName].workflow ) {
									if ( options.blueprint[fieldName].workflow.hasOwnProperty(['states']) ) {
										if ( options.blueprint[fieldName].workflow.states ) {
											states = options.blueprint[fieldName].workflow.states;
											workflowObj = options.blueprint[fieldName].workflow;
										}
									} else if ( obj ) {
										if ( obj.hasOwnProperty('type') ) {
											if ( obj.type ) {
												if ( obj.type.hasOwnProperty('states') ) {
													if ( obj.type.states ) {
														states =  obj.type.states;
														workflowObj = obj.type;
													}
												}
											}
										}
									}
								}
							} else {
								return;
							}
						}
					}
				}
			} else if ( obj.hasOwnProperty('type') ) {
				if ( obj.type ) {
					if ( obj.type.hasOwnProperty('states') ) {
						if ( obj.type.states ) {
							states =  obj.type.states;
							workflowObj = obj.type;
						}
					}
				}
			} else {
				return;
			}

            ///if value is undefined default button state and color will be set
			var defaultSelection = _.findWhere(states, {uid:parseInt(obj[fieldName])});

			///current state saved on object or defaults to isEntryPoint
			var currentState = defaultSelection || _.findWhere(states, {isEntryPoint:1});

			///current pointer to previous state object
			var previousState = '';
			if ( currentState && !_.isEmpty(currentState) ) {
				var previousState =_.findWhere(states, {uid:parseInt(currentState.previous[0])})
				///if current PREVIOUS prop empty, then find state obj with empty NEXT property
				|| _.find(states, function(st){
					return st.next.length == 0;
				});
			}

			///current pointer to next state object
			var nextState = '';
			if ( currentState && !_.isEmpty(currentState) ) {
				var nextState = _.findWhere(states, {uid:parseInt(currentState.next[0])})
				///if current NEXT empty, then find state obj with empty PREVIOUS property
				|| _.find(states, function(st){
					return st.previous.length == 0;
				});
			}

			var btnTxt = '<i class="ui right arrow icon"></i> Save & Continue';
			var btnCss = 'ui huge icon green button pull-right';


            var anythingOtherThanIntake = false;

            if (appConfig.is_portal) {

                var entitiesList = _.filter(appConfig.Types, function(e){
                    ///4760308 - {tag:'#Action Items' object_bp_type:"system_tags"}
                    return e.is_task && e.tagged_with.indexOf( 4760308 ) > -1 ;
                });
    
                if (obj && obj.hasOwnProperty('object_bp_type')) {
    
                    var blueprint_name;
                    
                    if (obj.object_bp_type.indexOf('#') > -1){
                        blueprint_name = obj.object_bp_type.substring(1);
                    } else {
                        blueprint_name = obj.object_bp_type;
                    }
    
                    if ( _.find(entitiesList, {bp_name: blueprint_name}) ) {
                        anythingOtherThanIntake = true;
                    }
    
                }
                
            }

			// Completed items just show the current status
			if (obj.status === 'done') {

				btnTxt = '<i class="ui '+ currentState.icon +' icon"></i> '+ currentState.name;
				btnCss = 'ui huge icon disabled button pull-right';

			} else if (options.isLastItem) {

				btnTxt = '<i class="ui check icon"></i> Submit';
				btnCss = 'ui huge icon green button pull-right';

			} else if ( anythingOtherThanIntake ){

                btnTxt = '<i class="ui '+ currentState.icon +' icon"></i> '+ currentState.name;
				btnCss = 'ui huge icon button pull-right';

            }

			// If there are two "next" options, shown buttons for both of them
			if (
				Array.isArray(currentState.next) 
				&& currentState.next.length === 2
			) {

				// Get the next state opts
				var firstOpt = _.findWhere(states, {uid:parseInt(currentState.next[0])});
				var secondOpt = _.findWhere(states, {uid:parseInt(currentState.next[1])});

				// Button group
				ui.makeNode(
					'btns'
					, 'div'
					, {css: 'ui fluid buttons'}
				);

				// First choice
				ui.btns.makeNode('first', 'div', {
					css: 		'ui '+ firstOpt.color +' button'
					, tag: 		'button'
					, text: 	'<i class="ui '+ firstOpt.icon +' icon"></i>'+ firstOpt.name
				}).notify('click', {
					type: 'workflows-run',
					data: {
						run: function () {

							confirmDialog(function() {
								updateState(
									firstOpt
									, onStateTransition
									, true
								);
							});
	
						}
	
					}
				}, sb.moduleId);

				// "or"
				ui.btns.makeNode('or', 'div', {css: 'or'});

				// Second choice
				ui.btns.makeNode('second', 'div', {
					css: 		'ui '+ secondOpt.color +' button'
					, tag: 		'button'
					, text: 	'<i class="ui '+ secondOpt.icon +' icon"></i>'+ secondOpt.name
				}).notify('click', {
					type: 'workflows-run',
					data: {
						run: function () {

							confirmDialog(function() {
								updateState(
									secondOpt
									, onStateTransition
									, true
								);
							});
	
						}
	
					}
				}, sb.moduleId);

			// Otherwise, just show the next item
			} else {

				ui.makeNode(
					'btn'
					, 'div'
					, {
						tag: 	'button'
						, text: btnTxt
						, css: 	btnCss
					}
				).notify('click', {
					type: 'workflows-run',
					data: {
						run: function () {
							confirmDialog(function() {
								updateState(
									nextState
									, onStateTransition
									, true
								);
							});
	
						}
	
					}
				}, sb.moduleId);

			}

		}

		// Button UI
		function buttonUI(fieldName, ui, obj, options) {

			var buttonSize = '' || options.size;

			// Set variables
			var objectID = obj.id;
			var objectName = obj.name;
			var commitUpdates = true;
			var fieldOptions = {};
			var edit = true;

			// Set states
			var states = {};
			if ( options.hasOwnProperty('blueprint') ) {
				if ( options.blueprint ) {
					if ( options.blueprint.hasOwnProperty([fieldName]) ) {
						if ( options.blueprint[fieldName] ) {
							if ( options.blueprint[fieldName].hasOwnProperty(['workflow']) ) {
								if ( options.blueprint[fieldName].workflow ) {
									if ( options.blueprint[fieldName].workflow.hasOwnProperty(['states']) ) {
										if ( options.blueprint[fieldName].workflow.states ) {
											states = options.blueprint[fieldName].workflow.states;
										}
									} else if ( obj ) {
										if ( obj.hasOwnProperty('type') ) {
											if ( obj.type ) {
												if ( obj.type.hasOwnProperty('states') ) {
													if ( obj.type.states ) {
														states =  obj.type.states;
													}
												}
											}
										}
									}
								}
							} else {
								return;
							}
						}
					}
				}
			} else if ( obj.hasOwnProperty('type') ) {
				if ( obj.type ) {
					if ( obj.type.hasOwnProperty('states') ) {
						if ( obj.type.states ) {
							states =  obj.type.states;
						}
					}
				}
			} else {
				return;
			}

			///if value is undefined default button state and color will be set
			var defaultSelection = _.findWhere(states, {uid:parseInt(obj[fieldName])});

			///current state saved on object or defaults to isEntryPoint
			var currentState = defaultSelection || _.findWhere(states, {isEntryPoint:1});

			///current pointer to previous state object
			var previousState = '';
			if ( currentState && !_.isEmpty(currentState) ) {
				var previousState =_.findWhere(states, {uid:parseInt(currentState.previous[0])})
				///if current PREVIOUS prop empty, then find state obj with empty NEXT property
				|| _.find(states, function(st){
					return st.next.length == 0;
				});
			}

			///current pointer to next state object
			var nextState = '';
			if ( currentState && !_.isEmpty(currentState) ) {
				var nextState = _.findWhere(states, {uid:parseInt(currentState.next[0])})
				///if current NEXT empty, then find state obj with empty PREVIOUS property
				|| _.find(states, function(st){
					return st.previous.length == 0;
				});
			}

			///sets color and icon to be shared across button group
			var currentColor = '';
			var currentIcon = '';
			if (currentState) {
				if ( currentState.hasOwnProperty('color') ) {
					currentColor = getColor(currentState.color);
				}
				if ( currentState.hasOwnProperty('icon') ) {
					currentIcon = getIcon(currentState.icon);
				}
			}

			if (
				options
				&& typeof options === 'object'
				&& options.hasOwnProperty('commitUpdates')
			) {
				commitUpdates = options.commitUpdates;
			}

			if (
				options
				&& typeof options === 'object'
				&& options.hasOwnProperty('fields')
				&& options.fields.hasOwnProperty('state')
			) {
				///checks for options on collections field setup
				fieldOptions = options.fields.state;

				if (fieldOptions.edit) {
					edit = fieldOptions.edit;
				}

			}

			var mainBtn = {};

			function updateState(flowState, callback) {

					if (options.commitUpdates || !options.hasOwnProperty('commitUpdates')) {

						// Show loader
						$("#loader").fadeIn();

						// Set link
						var link = sb.data.url.createPageURL(
							'object',
							{
								type: 'state',
								id: objectID,
								name: objectName
							}
						);

						confirmTransitionWithUser(flowState, function (confirmation) {

							if (confirmation.runTransition) {

								sb.data.db.obj.updateState({
									objectId: objectID,
									newState: parseInt(flowState.uid),
									link: link,
									stateProperty: fieldName,
									supressActions: !confirmation.triggerActions
								}, function (response) {

									var updatedObj = response.response;

									const fieldErrors = response && response.msg && response.msg.fieldErrors;

									if (fieldErrors && fieldErrors.length > 0) {
										try {
											_.each(fieldErrors, function (fieldError) {
												sb.notify({
													type: 'run-validations'
													, data: {
														obj: {
															id: objectID
														},
														property: fieldError
													}
												});
											});

											// Hide loader
											$("#loader").fadeOut();

										} catch (e) {
											console.log('error:', e.message);
										}

									} else {
										if (appConfig.instance === 'foundation_group') {
											buildFormNotification(objectID, flowState, sb);
										}

										if (typeof updatedObj === 'string') {
											updatedObj = JSON.parse(updatedObj);
										}

										// Update all state fields and trackers
										sb.notify({
											type: 'field-updated',
											data: {
												obj: {
													id: objectID,
													[fieldName]: updatedObj[fieldName]
												},
												type: 'state',
												property: fieldName
											}
										});

										obj[fieldName] = updatedObj[fieldName];

										if (
											!_.isEmpty(updatedObj)
											&& typeof updatedObj.object_bp_type === 'string'
											&& updatedObj.object_bp_type.substr(0, 1) === '#'
										) {

											sb.notify({
												type: 'refresh-entity-tabs',
												data: {
													entity: updatedObj
												}
											});

										}

										// Hide loader
										$("#loader").fadeOut();
									}

								}, true);

							} else {

								// Hide loader
								$("#loader").fadeOut();

							}

						});

					} else {

						obj[fieldName] = parseInt(flowState.uid);
						buttonUI(fieldName, ui, obj, options);
						ui.patch();

					}

			}

			function negwardButton(ui){

				var btnCSS = `ui icon ${currentColor} button`;

				ui.makeNode('left', 'button', {css: btnCSS, text:'<i class="left chevron icon"></i>'})
					.notify('click', {
						type: 'workflows-run',
						data: {
							run:updateState.bind(null, previousState, function(obj){

									buttonUI(fieldName, ui, obj, options);

								})
						}
					}, sb.moduleId);

			}

			function currentButton(ui){

				function transitionBtn(ui, opt, i, list, direction) {

					var directionTxt = '';
					switch (direction) {

						case 'forward':
						directionTxt = '<i style="opacity:.7;">Forward to</i> ';
						break;

						case 'back':
						directionTxt = '<i style="opacity:.7;">Back to</i> ';
						break;

					}

					if(opt.uid != currentState.uid){

						var optColor = '';
						var optIcon = '';
						if (opt) {
							if ( opt.hasOwnProperty('color') ) {
								optColor = getColor(opt.color);
							}
							if ( opt.hasOwnProperty('icon') ) {
								optIcon = getIcon(opt.icon);
							}
						}

						if(
							// Don't allow state transitions for projects when in
							// a client portal.
							!(
								appConfig.is_portal === true
								&& obj.object_bp_type === 'groups'
								&& obj.group_type === 'Project'
							)
						) {

							ui.makeNode('btn-'+opt.id, 'div', {
								css:		`ui item ${optColor} button `,
								text: 	directionTxt +' '+ optIcon.text +' '+ opt.name,
								style:	'border-radius:0px !important;border:none !important;'
							}).notify('click', {
								type: 'workflows-run',
								data: {
									run: updateState.bind(null, opt, function(obj){

											buttonUI(fieldName, ui, obj, options);

										})

								}
							}, sb.moduleId);

						}

					}

				}

				if(!currentState){
					var currentCSS = `ui grey dropdown fluid mini button`;
					var btnText = `<nobr> No Workflow </nobr>`;
					return;
				}

				var currentCSS = `ui ${currentColor} dropdown fluid mini button`;
				var btnText = `<nobr> ${currentIcon.text} ${currentState.name} </nobr>`;

				if(_.isUndefined(currentState)){
					btnText = 'No selection saved';
					currentCSS = 'ui dropdown mini button ';
				}

				if (options.edit === false) {

					var canEdit = false;
					var dropDownStyle = 'display:none !important;';
					var dropDownCSS = '';

					currentCSS = `ui ${currentColor} dropdown fluid mini disabled button  `;

				} else {

					var canEdit = true;
					var dropDownStyle = '';
					var dropDownCSS = 'menu override-dropdown-menu-alignment';

				}

				ui.makeNode('state', 'div', {
					text: btnText,
					css: currentCSS,
					style: 'text-align: center;',
					listener: {
						type:'dropdown',
						onShow() {

							setTimeout(function() {
								$('.floater-container').css({'overflow':'visible'});
							}, 100);
								
						},
						onHide() {
		
							setTimeout(function() {
								$('.floater-container').css({'overflow-y':'scroll', 'overflow-x':'auto'});
							}, 100);
								
						}
					}
				}).makeNode('menu', 'div', {
					css: dropDownCSS,
					style:dropDownStyle
				});

				if (obj.object_bp_type === 'groups') {
					canEdit = sb.permissions.isGroupManager(+sb.data.cookie.userId, obj);
				}

				if (appConfig.instance != obj.instance) {
					canEdit = false;
				}

				//!TODO: if not open transitions..
				if (canEdit) {

					if (!currentState.allowAllTransitions) {

						var next = _.findWhere(
							states, {
								uid: parseInt(currentState.next[0])
							}
						);

						var previous = _.findWhere(
							states, {
								uid: parseInt(currentState.previous[0])
							}
						);

						if (next) {

							transitionBtn(
								ui.state.menu
								, next
								, next.uid
								, states
								, 'forward'
							);

						}

						if (previous) {

							transitionBtn(
								ui.state.menu
								, previous
								, previous.uid
								, states
								, 'back'
							);

						}

						return ui.state;

					} else {

						// Put the states in order
						var newOrderArray = [];
						var count = 1;
						var arrayPos = 0;
						var curState = [];

						while(newOrderArray.length < 1){

							if(states[arrayPos].isEntryPoint == 1 && newOrderArray.length == 0){

								newOrderArray.push(states[arrayPos]);

								curState = states[arrayPos];

							}

							count++;
							arrayPos++;

							if(count > states.length){
								count = 1;
								arrayPos = 0;
							}

						}

						while(newOrderArray.length < states.length){

							curState = _.findWhere(
								states,
								{
									uid: parseInt(curState.next[0])
								}
							);

							newOrderArray.push(curState);

						}

						states = newOrderArray;

						_.each(states, function(opt, i, list) {

							if (opt) {

								transitionBtn(
									this
									, opt
									, i
									, list
								);

							}

						}, ui.state.menu);

					}

				}

				return ui.state;


			}

			function poswardButton(ui){

				var btnCSS = `ui icon ${currentColor} button`;

				ui.makeNode('right', 'div', {css:btnCSS, text:'<i class="right chevron icon"></i>'})
					.notify('click', {
							type: 'workflows-run',
							data: {
								run:updateState.bind(null, nextState, function(obj){

									buttonUI(fieldName, ui, obj, options);

								})
							}
					}, sb.moduleId);

			}

			var btnsCss = 'ui '+ buttonSize +' buttons';
			if (options.fluid) {
				btnsCss = 'ui '+ buttonSize +' fluid buttons';
			}

			ui.empty();
			ui.makeNode('btns-'+obj.id, 'div', {css: btnsCss });

			// Display middle dropdown button.
			mainBtn = currentButton(
				ui['btns-'+obj.id]
			);

		}

		//return the user id
		function getUserRole(project, roleName, callback){
			var selectedProject = project;
			var roleSheet = _.find(selectedProject.tools, function(_obj) {
				if(_obj.display_name && _obj.display_name.startsWith("Project Role Sheet")){
					return true;
				}
				return false;
			});
			var bluePrint = roleSheet.system_name;

			sb.data.db.obj.getWhere('entity_type', {bp_name: bluePrint}, function(entities){
				var userIndex = null;
				_.forEach(entities[0].blueprint, function (_entity, index){
					if(_entity.fieldType === 'user' && _entity.is_archived !== true &&  _entity.name){
						if (_entity.name.toLowerCase().indexOf(roleName.toLowerCase()) !== -1) {
							userIndex = index;
						}
					}
				});

					if(userIndex) {
						sb.data.db.obj.getWhere('#' + entities[0].bp_name, {parent: project['id']}, function (roleSheetData) {

							if (roleSheetData && roleSheetData[0]) {
								sb.data.db.obj.getById('users', roleSheetData[0][userIndex], function(user) {
									callback(user);
								});
							}

						}, {getChildObjs: {}});
					}

				//callback(null);
			});

		}

		function getMergeTagged(project){
			var tagged = [];
			var _formationProjectTypes = [2238449, 2972438, 2972443, 2972535];

			var teamId = 1652258; //Compliance Team Id

			if(_formationProjectTypes.includes(project['type'])){
				teamId = 6077227; //Formation Team Id
			}

			tagged.push(project['id']);
			tagged.push(teamId);
			tagged = tagged.concat(project['tagged_with']);
			return tagged;

		}


		function sendNotification(actionItem, type, user, organization, isClientReview, sb){


			var project = actionItem['parent'];

			var link = sb.data.url.createPageURL(
				'object',
				{
					object_bp_type: 'entity_type',
					id: actionItem['id'],
					name: actionItem['name']
				}
			);

			//in project.tools -> system_name
            var title = '';
			var notifyFormType = isClientReview ? 'Client Info Review' : 'Client Info Request';
			var secondaryTitle = '';
			var details = '';
			var icon = 'check';
			var portalName = 'Foundation Group';
			if(appConfig.isFoundationGroupPortal){
				portalName = appConfig && appConfig.headquarters && appConfig.headquarters.name
			}

			if(type != 'Action Items') {
                title = organization['name'] + ' Intake completed';
                if( user && user.hasOwnProperty('fname') ) {
                    var title = title + ' - ' + user['fname'] + ' '+ user['lname'];
                }
				secondaryTitle = '<i class="icon upload"></i> '+portalName+' has completed all Intake Forms for ' + project['name'];
				details = 'All Intake Forms submitted and ready for review';
				icon = 'check';
			} else {
                title = organization['name'] + ' Action Item submitted';
                if( user && user.hasOwnProperty('fname') ) {
                    title = title + ' - ' + user['fname'] + ' '+ user['lname'];
                }
				secondaryTitle = '<i class="icon check"></i> '+portalName+' has submitted the '+notifyFormType+' for ' + project['name'];
				details = 'Client has responded to the '+notifyFormType+' action item';
				icon = 'upload';
            }

			var actionItemFinishedNotification = {
				title: title
				, secondaryTitle
				, producer: project['id']
				, producer_type: project['type']
				, color: 'red'
				, link: link
				, user: user
				, icon: icon
				, type: 'recent-client-activity'
				, details: details
				, tagged_with: getMergeTagged(project)
			}

			sb.data.db.obj.create('notification', [actionItemFinishedNotification], function(res){});
		}

		function buildFormNotification(objectID, flowState, sb){

			var intakeQuestionnaire = 4759706; //Intake Form Id
			var actionItems = 4760308; // Action Item ID

			var clientReview = 1712510; // Client Review
			//var clientRequest = 1687652; //Client Request

			if(flowState.type == 'done') {

				sb.data.db.obj.getById('', objectID, function (item) {
					sb.data.db.obj.getBlueprint(item.object_bp_type, function (entity) {
						sb.data.db.obj.getById('', item['parent']['id'], function(project) {
						var isIntake = entity.tagged_with.includes(intakeQuestionnaire);
						var isActionItem = entity.tagged_with.includes(actionItems);
						var isClientReview = entity._class == clientReview;

						if (isActionItem) {
								getUserRole(item['parent'], 'specialist', function(specialistUser){
									if(userId) {
										sendNotification(item, 'Action Items', specialistUser, project['parent'], isClientReview, sb);
									}
								});
						}

						if (isIntake) {
							sb.data.db.service(
								'FGProjectsService'
								, 'hasPendingIntakeQuestionnaire', {
									projectId: item['parent']
								}, function(respService){
									if(respService['hasPending'] == false){
										//sent notification
										getUserRole(item['parent'], 'specialist', function(specialistUser){
											if(userId) {
												sendNotification(item, 'Intake Questionnaire', specialistUser, project['parent'], isClientReview, sb);
											}
										});
									}
								}
							);
						}

						}, {'parent': true});

					}, false, true);


				}, {getChildObjs: {parent: true}});
			}

		}

		// Tracker UI
		function trackerUI(fieldName, ui, obj, options) {

			if(obj.response) {

				obj = obj.response;

				if (typeof obj.type === 'number' ) {
	
					sb.data.db.obj.getById('project_types', obj.type, function(response){
	
						obj.type = response;
						trackerUI(fieldName, ui, obj, options);
	
					}, 0)
					return;
				}
			}

			// Set variables
			var objectID = obj.id;
			var objectName = obj.name;

			// Set states
			var states = {};
			if ( options.hasOwnProperty('blueprint') ) {
				if ( options.blueprint ) {
					if ( options.blueprint.hasOwnProperty([fieldName]) ) {
						if ( options.blueprint[fieldName] ) {
							if ( options.blueprint[fieldName].hasOwnProperty(['workflow']) ) {
								if ( options.blueprint[fieldName].workflow ) {
									if ( options.blueprint[fieldName].workflow.hasOwnProperty(['states']) ) {
										if ( options.blueprint[fieldName].workflow.states ) {
											states = options.blueprint[fieldName].workflow.states;
										}
									} else if ( obj ) {
										if ( obj.hasOwnProperty('type') ) {
											if ( obj.type ) {
												if ( obj.type.hasOwnProperty('states') ) {
													if ( obj.type.states ) {
														states =  obj.type.states;
													}
												}
											}
										}
									}
								}
							} else {
								return;
							}
						}
					}
				}
			} else if ( obj.hasOwnProperty('type') ) {
				if ( obj.type ) {
					if ( obj.type.hasOwnProperty('states') ) {
						if ( obj.type.states ) {
							states =  obj.type.states;
						}
					}
				}
			} else {
				return;
			}

			///if value is undefined default button state and color will be set
			var defaultSelection = _.findWhere(states, {uid:parseInt(obj[fieldName])});

			///current state saved on object or defaults to isEntryPoint
			var currentState = defaultSelection || _.findWhere(states, {isEntryPoint:1});

			///current pointer to previous state object
			var previousState = '';
			if ( currentState && !_.isEmpty(currentState) ) {
				var previousState =_.findWhere(states, {uid:parseInt(currentState.previous[0])})
				///if current PREVIOUS prop empty, then find state obj with empty NEXT property
				|| _.find(states, function(st){
					return st.next.length == 0;
				});
			}

			///current pointer to next state object
			var nextState = '';
			if ( currentState && !_.isEmpty(currentState) ) {
				var nextState = _.findWhere(states, {uid:parseInt(currentState.next[0])})
				///if current NEXT empty, then find state obj with empty PREVIOUS property
				|| _.find(states, function(st){
					return st.previous.length == 0;
				});
			}

			///sets color and icon to be shared across button group
			var currentColor = '';
			var currentIcon = '';
			if (currentState) {
				if ( currentState.hasOwnProperty('color') ) {
					currentColor = getColor(currentState.color);
				}
				if ( currentState.hasOwnProperty('icon') ) {
					currentIcon = getIcon(currentState.icon);
				}
			}

			var completed = true;
			var active = '';
			var stepColor = '';
			var componentType = 'ui right pointing label';

			// Figure out divided steps
			var dividedSteps = _.size(states) > 8 ? Math.round(_.size(states) / 2) : _.size(states);
			dividedSteps = dividedSteps <= 16 ? dividedSteps : '16';
			dividedStepsArray = {
				'1': 'one',
				'2': 'two',
				'3': 'three',
				'4': 'four',
				'5': 'five',
				'6': 'six',
				'7': 'seven',
				'8': 'eight',
				'9': 'nine',
				'10': 'ten',
				'11': 'eleven',
				'12': 'twelve',
				'13': 'thirteen',
				'14': 'fourteen',
				'15': 'fifteen',
				'16': 'sixteen'
			}
			dividedSteps = dividedStepsArray[dividedSteps];

			ui.makeNode('steps', 'div', {
				css:'ui steps ' + dividedSteps,
				style:'width:100%'
			});

			var flowState = _.findWhere(
					states,
					{
						isEntryPoint: 1
					}
				);

			while(flowState != undefined){

				stepColor = 'empty';
				if(flowState.id == currentState.id){

					if(flowState.color){

						completed = flowState.color;
						stepColor = flowState.color;

					}else{

						completed = 'grey';
						stepColor = 'grey';

					}

					completed = false;

				}

				if(flowState.icon){
					iconText = '<i class="'+ flowState.icon +' icon"></i>';
				}else{
					iconText = '<i class="circle icon"></i>';
				}

				if(!_.findWhere(
					states,
					{
						uid: parseInt(flowState.next[0])
					}
				)){

					componentType = 'ui icon large label';

				}

				// Set message
				var message = ( flowState && !_.isEmpty(flowState.message) ) ? ": " + flowState.message : '';

				// !TODO: Only set this listener if:
				// . it is not the current state of the object, and
				// . the state being displayed (flowState here) is pointed to ('uid')
				// 	   in its 'next' or 'previous' arrays.

				ui.steps.makeNode('step-'+flowState.id, 'div', {
					css: 'ui step ' + getColor(stepColor) + ' ' + active,
					text: iconText + ' ' + flowState.name,
					//tooltip: getLastChanged(fieldName, obj)
				});

				if (appConfig.instance != obj.instance) {
					options.edit = false;
				}

				if (
					!options.hasOwnProperty('edit')
					|| options.edit === true
				) {

					ui.steps['step-'+flowState.id].notify('click', {
						type: 'workflows-run',
						data: {
							run:function(ui, flowState) {

								if ( options.commitUpdates || !options.hasOwnProperty('commitUpdates') ) {

									// Set link
									var link = sb.data.url.createPageURL(
										'object',
										{
											type:'state',
											id:objectID,
											name:objectName
										}
									);

									confirmTransitionWithUser(flowState, function (confirmation) {

                                        // Show loader
                                        $("#loader").fadeIn();

										if (confirmation.runTransition) {

											sb.data.db.obj.updateState({
												objectId: 		objectID,
												newState: 		parseInt(flowState.uid),
												link:			link,
												stateProperty:	fieldName,
												supressActions: !confirmation.triggerActions
											}, function(updatedObj){
												if(appConfig.instance === 'foundation_group') {
													buildFormNotification(objectID, flowState, sb);
												}

                                                if (typeof updatedObj === 'string') {
													updatedObj = JSON.parse(updatedObj);
												}

												obj[fieldName] = updatedObj[fieldName];

												// Update all state fields and trackers
												sb.notify({
													type: 'field-updated',
													data: {
														obj: {
															id: objectID,
															[fieldName]: updatedObj[fieldName]
														},
														type: 	'state',
														property: fieldName
													}
												});

												if (
													!_.isEmpty(updatedObj)
													&& typeof updatedObj.object_bp_type === 'string'
													&& updatedObj.object_bp_type.substr(0, 1) === '#'
												) {

													sb.notify({
														type: 'refresh-entity-tabs',
														data: {
															entity: updatedObj
														}
													});

												}

                                                obj[fieldName] = parseInt(flowState.uid);
                                                // trackerUI(fieldName, ui, updatedObj, options);
                                                ui.patch();

												// Hide loader
												$("#loader").fadeOut();
											});

										} else {

											// Hide loader
											$("#loader").fadeOut();

										}

									});

								} else {

									obj[fieldName] = parseInt(flowState.uid);
									trackerUI(fieldName, ui, obj, options);
									ui.patch();

								}

							}.bind({}, ui, flowState)

						}
					}, sb.moduleId);

				}

				if(flowState.id == currentState.uid){
					completed = 'disabled';
				}

				flowState = _.findWhere(
					states,
					{
						uid: parseInt(flowState.next[0])
					}
				);

			}

			if (
				currentState
				&& !_.isEmpty(currentState.message)
                && appConfig.instance != 'foundation_group'
			) {

				ui.makeNode( 'msg', 'div', {
					text: '<strong>Workflow Note:</strong> ' + currentState.message
				});

			}

			ui.patch();

		}

		function processUI(fieldName, ui, obj, options) {

			var workflow = options.blueprint[fieldName].workflow;

			function getViews(wf, entity, callback) {

				var where = {
						workflow: wf.id
						//, state: step.id
						, childObjs: 1
					};

				sb.data.db.obj.getWhere('view', where, function(views) {

					callback(views);

				});

			}

			function completeStep(ui, state, workflow, callback) {

				function updateState(flowState, callback) {

					if ( options.commitUpdates || !options.hasOwnProperty('commitUpdates') ) {

						// Show loader
						$("#loader").fadeIn();

						// Set link
						var link = sb.data.url.createPageURL(
								'object',
								{
									type:'state',
									id: obj.id,
									name: obj.name
								}
							);

						confirmTransitionWithUser(flowState, function (confirmation) {

							if (confirmation.runTransition) {

								sb.data.db.obj.updateState({
									objectId: 		obj.id,
									newState: 		parseInt(flowState.uid),
									link:			link,
									stateProperty:	fieldName,
									supressActions: !confirmation.triggerActions
								}, function(updatedObj){

									if(appConfig.instance === 'foundation_group') {
										buildFormNotification(obj.id, flowState, sb);
									}

									// Update all state fields and trackers
									sb.notify({
										type: 'field-updated',
										data: {
											obj: {
												id: obj.id,
												[fieldName]: updatedObj[fieldName]
											},
											type: 	'state',
											property: fieldName
										}
									});

									obj[fieldName] = updatedObj[fieldName];

									callback(updatedObj);

									// Hide loader
									$("#loader").fadeOut();

								}, false);

							} else {

								// Hide loader
								$("#loader").fadeOut();

							}

						});

					}

				}

				ui.makeNode('btnGrp', 'div', {});

				ui.btnGrp.makeNode('completeBtn', 'div', {
					css: 'ui right floated tiny green button'
					, text: '<i class="check icon"></i> Complete'
				}).notify('click', {
					type: [sb.moduleId+'-run'],
					data: {
							run: function(data) {

								ui.btnGrp.completeBtn.loading();

								var nextState = _.findWhere(workflow.states, {uid: parseInt(state.next[0])});

								if ( nextState ) {

									updateState(nextState, function(updatedState) {

										callback(updatedState);

									});

								} else {

									updateState(state, function(updatedState) {

										callback(updatedState);

									});

								}

							}
						}
					}, sb.moduleId);

			}

			function displayProcess(ui, entity) {

				var processUI = ui;
				var nextState = _.findWhere(workflow.states, {isEntryPoint: 1});
				var previousState = {};
				var currentStates = [];

				function buildStates(ui, step, viewsUI) {

					var stepColor = getColor(step.color) ? getColor(step.color) : 'black';

					var stepSetup = {
						css: 'step processStep',
						tag: 'a',
						id: 'step-' + step.id
					};
					var iconSetup = {
						tag: 'i',
						css: 'ui '+ stepColor + ' ' + step.icon + ' icon'
					};

					if (
						step.isEntryPoint === 1
						&& step.isComplete === false
					) {

						stepSetup.css = 'active step processStep';

					}

					if (
						previousState.isComplete !== undefined
						&& previousState.isComplete
						&& !step.isComplete
					) {

						stepSetup.css = 'active step processStep';

					}

					if (
						step.isComplete
					) {

						stepSetup.css = 'completed step processStep';

					}

					ui.makeNode('breakPoint-'+step.id, 'div', {
						id: 'stepBreakpoint-'+step.id
					});

					ui.makeNode('step-'+step.id, 'div', stepSetup);

					ui['step-'+ step.id].makeNode('i', 'div', iconSetup);

					ui['step-'+ step.id].makeNode('content', 'div', {
						css: 'content'
					});

					ui['step-'+ step.id].content.makeNode('title', 'div', {
						css: 'title'
						, text: step.name
					});

					ui['step-'+ step.id].content.makeNode('description', 'div', {
						css: 'description'
						, text: step.message
					});

				}

				function buildViews(ui, entity, workflow, stepsUI) {

					var list = [];
					var viewSelectorList = [];
					var activeStep = {};
					var lastStep = currentStates[currentStates.length-1].id;

					function build_loader(ui, text) {

						ui.makeNode('loadingWrap', 'div', {
							css: 'text-center'
						});

						ui.loadingWrap.makeNode('textLoader', 'div', {
							css: 'ui active inline text loader',
							text: text
						});


					}

					ui.empty();

					build_loader(ui, 'Loading views...');

					getViews(workflow, entity, function(views) {

						var groupedViews = _.groupBy(views, function(v) {
								return v.state;
							});
						var previousStep = {};
						var activeViewPos = 0;

						list = groupedViews;

						currentStates = _.sortBy(currentStates, 'order');

						ui.empty();

						_.each(currentStates, function(step, index) {

							ui.makeNode('stepWrap-'+step.id, 'div', {
								id: 'viewPos-'+step.id
							});

							viewSelectorList.push({
								selector: ui['stepWrap-'+step.id]
								, step: step
							});

							ui['stepWrap-'+step.id].makeNode('breakPoint', 'div', {
								id: 'breakPoint-'+step.id
								, tag: 'a'
							});

							ui['stepWrap-'+step.id].makeNode('stepName', 'div', {
								text: step.name
								, css: 'ui header'
							});

							if (
								step.isComplete
								//|| obj[fieldName] === step.id
							) {

								ui['stepWrap-'+step.id].makeNode('stepName', 'div', {
									text: '<i class="green check icon"></i>' + step.name
									, css: 'ui header'
								});

							}

							_.each(groupedViews, function(viewsList, stepId) {

								_.each(viewsList, function(v, i) {

									if ( v.state === step.id ) {

										_.each(entity.blueprint, function(bpObj, k) {

											if (v.hasOwnProperty('blueprint')) {

												if (v.blueprint) {

													if (v.blueprint.hasOwnProperty(k)) {

														if (v.blueprint.k) {

															v.blueprint[k] = bpObj;

														}

													}

												}

											}

										});

										ui['stepWrap-'+step.id].makeNode('view-'+v.id, 'div', {});

										sb.notify({
											type: 'display-view',
											data: {
												ui: ui['stepWrap-'+step.id]['view-'+v.id]
												, view: v
												, options: {
													editBlueprint: 	false
													, blueprint: 	v.blueprint
													, entity: 		obj
												}
												, onDraw: function() {}
											}
										}, sb.moduleId);

									}

								});

							});

							if (
								index === 0
								&& ( step.isComplete === false
								|| step.isComplete === undefined )
							) {

								completeStep(ui['stepWrap-'+step.id], step, workflow, function(updatedEntity) {

									processUI.empty();

									displayProcess(processUI, updatedEntity);

									processUI.patch();

								});

								activeStep = step;

							}

							if (
								previousStep.hasOwnProperty('id')
								&& step.isComplete === false
								&& previousStep.isComplete
							) {

							 	completeStep(ui['stepWrap-'+step.id], step, workflow, function() {

									processUI.empty();

									displayProcess(processUI, entity);

									processUI.patch();

								});

								activeStep = step;

							 }

							ui['stepWrap-'+step.id].makeNode('lb_1', 'lineBreak', {spaces: 2});

							ui['stepWrap-'+step.id].makeNode('divider', 'div', {
								css: 'ui divider'
							});

							previousStep = step;

						});

						ui.patch();

						_.each(viewSelectorList, function(o, i) {

							if (o.step.id === activeStep.id) {

								$(ui.selector).animate({
									scrollTop: $('#viewPos-'+activeStep.id).position().top - 100
								});

							}

						});

					});

					ui.listeners.push(function(selector) {

						$(selector).on('scroll', function() {

							var scrollPos = $(this).scrollTop();
							var innerHeight = $(this).innerHeight();
							var height = $(this)[0].scrollHeight;

							_.each(list, function(stepList, stepId) {

								var viewPos = $('#viewPos-'+stepId).position().top;

								if ( viewPos < scrollPos) {

									$('.processStep').removeClass('active');
									$('#step-'+stepId).addClass('active');

								}

							});

							if( (scrollPos + innerHeight) >=  height) {

								$('.processStep').removeClass('active');
								$('#step-'+lastStep).addClass('active');

							}

						});

					});

				}

				ui.makeNode('wrapper', 'div', {});

				ui.wrapper.makeNode('head', 'div', {});
				ui.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
				ui.wrapper.makeNode('body', 'div', {
					css: 'ui grid'
				});

				ui.wrapper.head.makeNode('wfName', 'div', {
					tag: 'h2'
					, css: 'ui header'
					, text: workflow.name
				});

				ui.wrapper.body.makeNode('col1', 'div', {
					css: 'five wide column'
				});

				ui.wrapper.body.makeNode('col2', 'div', {
					css: 'eleven wide column'
					, style: 'max-height: 500px; overflow-x: scroll;'
				});

				ui.wrapper.body.col1.makeNode('steps', 'div', {
					css: 'ui fluid vertical steps'
				});

				workflow.states = _.sortBy(workflow.states, function(s) {
					return s.previous[0];
				});

				var i = 1;
				var markAsComplete = true;

				while ( nextState ) {

					nextState.order = i;
					nextState.isComplete = true;

					currentStates.push(nextState);

					if ( obj[fieldName] === nextState.id ) {

						markAsComplete = false;

					}

					nextState.isComplete = markAsComplete;

					buildStates(ui.wrapper.body.col1.steps, nextState, ui.wrapper.body.col2);

					if (!_.isEmpty(nextState.next)) {

						previousState = nextState;
						nextState = _.findWhere(workflow.states, {uid: parseInt(nextState.next[0])});

					} else {

						nextState = false;

					}

					i++;

				}

				buildViews(ui.wrapper.body.col2, entity, workflow, ui.wrapper.body.col1.steps);

			}

			sb.data.db.obj.getById('entity_type', workflow.parent, function(entity) {

				ui.empty();

				displayProcess(ui, entity);

				ui.patch();

			}, 1);

		}

		if ( options.viewStyle === 'form' && appConfig.is_portal ) {

			formButtonUI(fieldName, ui, obj, options);
			return;

		}

		if (
			!options.mini
			&& !options.inCollection
			&& !_.isEmpty(options)
			&& !options.entityPage
		) {
			//css = 'edge-field';
		}

		function getLastChanged(fieldName, obj) {

			// Display the relative time from last change.
			var lastChanged = '';

			if(!_.isEmpty(obj)){

				if ( obj.hasOwnProperty('object_bp_type') ) {

					if (obj.object_bp_type === 'groups') {

						if (obj.state_updated_on) {

							lastChanged = 'Set '+ moment(obj.state_updated_on).fromNow();

						} else if (obj.date_created) {

							lastChanged = 'Set '+ moment(obj.date_created).fromNow();

						}

					} else if (obj[fieldName +'_updated_on']) {

						lastChanged = 'Set '+ moment(obj[fieldName +'_updated_on']).fromNow();

					} else if (obj.date_created) {

						lastChanged = 'Set '+ moment(obj.date_created).fromNow();

					}

				}

			}

			return lastChanged;

		}

		function getObjectPageParams(item){

			var objType = item.object_bp_type;
			if(objType === 'groups'){
				objType = item.group_type.toLowerCase();
			}

			var objName = item.name;
			switch(objType) {

				case 'contacts':
				case 'users':
				objName = item.fname +' '+ item.lname;
				break;

				case 'tasks':
				objName = item.title;
				break;

			}

			return sb.data.url.createPageURL(
				'object',
				{
					type:objType,
					id:item.id,
					name:objName
				}
			);

		}

		if (
			options.hasOwnProperty('stateTracker')
			&& options.stateTracker === true && !options.inCollection && !sb.dom.isMobile
			&& obj.hasOwnProperty('object_bp_type')
		) {

			// Define ui
			ui.makeNode('f', 'div', {});

			// Initialize tracker

			trackerUI(fieldName, ui.f, obj, options);

		} else if (

			options.hasOwnProperty('processView')
			&& options.processView === true && !options.inCollection && !sb.dom.isMobile
			&& obj.hasOwnProperty('object_bp_type')

		) {

			ui.makeNode('f', 'div', {});

			processUI(fieldName, ui.f, obj, options);


		} else {

			if(ui){

				// Define ui
				ui.makeNode('f', 'div', {
					tooltip: getLastChanged(fieldName, obj)
				});

				// Initialize button
				buttonUI(fieldName, ui.f, obj, options);

			}else{

				return 'testing';

			}

		}

	}

	return {

		init: function () {

			sb.listen({
				[sb.moduleId+'-run']: this.run
			});

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 					'state'
					, view: 					View
					, title: 				'Workflow'
					, availableToEntities: 	true
					, icon: 					'random'
					, parseOptions: function (selection, field, callback, type) {

						// create new workflow if none is set
						if (_.isEmpty(field.workflow)) {

							sb.data.db.obj.create(
								'entity_workflow'
								, {
									name: 		type.name +'.'+ field.name
									, parent: 	type.id
									, states: [{
										uid:1,
										name:'Open',
										icon:'circle outline',
										previous:[],
										next:[2],
										isEntryPoint:1,
										color:'grey'
									}, {
										uid:2,
										name:'Done',
										icon:'check circle outline',
										previous:[1],
										next:[],
										color:'green',
										type:'done'
									}]
								}
								, function (workflow) {

									field.workflow = workflow;
									callback(field);

								}
							);

						} else {

							callback(field);

						}


					}
					, detail: 		function () {}
					, propertyType: 'int'
					, options: {
					  	stateTracker: {
					    	name: "Display as tracker? (only on desktop)"
							, type: 'bool'
							, edit: 'bool'
						}
						, processView: {
					    	name: "Display as process view? (only on desktop)"
							, type: 'bool'
							, edit: 'bool'
						}
						, _labelPos: {
							name: 			'Label position'
							, type: 		'select'
							, options: 		[
								{
									name: 'Left'
									, value: 'left'
								}
								, {
									name: 'Above'
									, value: 'above'
								}
								, {
									name: 'Hidden'
									, value: 'hidden'
								}
							]
							, configType: 		'view'
						}
					}
				}
			});

			if (IN_NODE_ENV) {
				var state = View;
				module.exports = { 
					state
				}
			}

		}

		, run: function(data) {

			data.run(data);

		}

	}

});

if (IN_NODE_ENV) {
	Factory.startAll();
}
