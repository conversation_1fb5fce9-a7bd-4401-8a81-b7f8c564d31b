var IN_NODE_ENV = typeof module !== "undefined" && module.exports;
if (IN_NODE_ENV) {
  // Get dependencies
  var Factory = require("../_core.js").Factory;
  var _ = require("underscore-node");

  var appConfig = {
    instance: global.dbAuth.dbPagodaAPIKey
  };
}

Factory.register("quantity-view", function(sb) {
  function View(fieldName, ui, obj, options) {
    if (!ui) {
      var ret = obj[fieldName] ? obj[fieldName] : 0;

      if (typeof options.callback === "function") {
        options.callback(ret);
      }

      return;
    }

    // Don't refresh current instance if this view triggered an update
    if (
      options &&
      options.isRefreshing &&
      options.refreshTriggeredBy &&
      options._fieldId &&
      options._fieldId === options.refreshTriggeredBy
    ) {
      return;
    }

    var setBlueprint = false;
    var detailText = obj[fieldName];
    var editing = false;

    if (obj && typeof obj.object_bp_type === "string") {
      setBlueprint = _.findWhere(appConfig.Types, {
        bp_name: obj.object_bp_type.substr(1)
      });
    }

    function editTxt(ui, obj, fieldName) {
      var currentValue = obj[fieldName] || null;

      if (typeof options.onEditStart === "function") {
        options.onEditStart();
      }

      $(ui.selector).html(
        '<input style="border:none; background-color:transparent; outline:none; width:100%;' +
          options.style +
          '" value="' +
          currentValue +
          '" type="number" name="quantity" min="' +
          options.min +
          '" max="' +
          options.max +
          '" placeholder="0">'
      );

      if (!options.editing) {
        $(ui.selector)
          .children()
          .first()
          .focus();

        // Go back to view mode after leaving focus
        $(ui.selector)
          .children()
          .first()
          .focusout(function() {
            var newVal = parseInt(
              $(ui.selector)
                .children()
                .first()
                .val()
            );
            obj[fieldName] = newVal;

            if (typeof options.onUpdate === "function") {
              options.onUpdate(obj);
            }

            if (options.commitUpdates) {
              sb.data.db.obj.update(
                obj.object_bp_type,
                {
                  id: obj.id,
                  [fieldName]: newVal
                },
                function(response) {
                  obj[fieldName] = response[fieldName];

                  if (typeof options.onEditEnd === "function") {
                    options.onEditEnd();
                  }

                  View(fieldName, ui, obj, options);
                  ui.patch();
                  editing = false;
                },
                1
              );
            }
          });
      } else {
        // Save after leaving focus
        $(ui.selector)
          .children()
          .first()
          .focusout(function() {
            var newVal = parseInt(
              $(ui.selector)
                .children()
                .first()
                .val()
            );
            obj[fieldName] = newVal;

            if (typeof options.onUpdate === "function") {
              options.onUpdate(obj);
            }

            if (options.commitUpdates) {
              sb.data.db.obj.update(
                obj.object_bp_type,
                {
                  id: obj.id,
                  [fieldName]: newVal
                },
                function(response) {
                  obj[fieldName] = newVal;

                  sb.notify({
                    type: "field-updated",
                    data: {
                      obj: {
                        id: obj.id,
                        [fieldName]: obj[fieldName]
                      },
                      type: "quantity",
                      property: fieldName,
                      _fieldId: options._fieldId
                    }
                  });

                  // Update formula fields
                  if (setBlueprint) {
                    _.each(setBlueprint.blueprint, function(field, key) {
                      if (
                        field.fieldType === "formula" &&
                        response.hasOwnProperty(key)
                      ) {
                        sb.notify({
                          type: "field-updated",
                          data: {
                            obj: {
                              id: obj.id,
                              [key]: response[key]
                            },
                            type: "formula",
                            property: key
                          }
                        });
                      }
                    });
                  }
                },
                1
              );
            } else {
              obj[fieldName] = newVal;
            }
          });
      }
    }

    if (obj[fieldName]) {
      ui.makeNode("t", "div", {
        text:
          '<h2 class="ui small header" style="font-weight:400; word-wrap: break-word;">' +
          detailText +
          "</h3>",
        style: "font-weight:lighter;"
      });
    } else {
      var placeholder = options.hasOwnProperty("placeholder")
        ? options.placeholder
        : "Not set";

      ui.makeNode("t", "div", {
        text:
          '<h2 class="ui small header" style="font-weight:400; word-wrap: break-word;">' +
          placeholder +
          "</h3>",
        style: "font-weight:lighter;"
      });
    }

    if (options.editing) {
      ui.listeners.push(
        function(ui, obj, fieldName) {
          editTxt(ui, obj, fieldName);
        }.bind({}, ui, obj, fieldName)
      );
      return;
    }

    ui.listeners.push(function(selector) {
      $(selector).on("click", function(e) {
        if (options.edit && !editing) {
          e.stopPropagation();
          e.preventDefault();

          editTxt(ui, obj, fieldName);
          editing = true;
        }
      });
    });

    if (options && options.isRefreshing) {
      ui.patch();
    }
  }

  return {
    init: function() {
      sb.notify({
        type: "register-field-type",
        data: {
          name: "quantity",
          view: View,
          title: "Number",
          availableToEntities: true,
          detail: function(fieldName, obj, options) {
            var fieldLabel = "";
            if (
              options &&
              (options.mini || options.inCollection) &&
              typeof obj.object_bp_type === "string"
            ) {
              var bp = _.findWhere(appConfig.Types, {
                bp_name: obj.object_bp_type.substr(1)
              });
              if (bp && bp.blueprint && bp.blueprint[fieldName]) {
                fieldLabel = bp.blueprint[fieldName].name;
              }
              return fieldLabel;
            }

            return false;
          },
          icon: "hashtag",
          propertyType: "float",
          shouldPatch: false,
          options: {
            is_progress: {
              type: "bool",
              name: "Is progress value?"
            }
          }
        }
      });

      if (IN_NODE_ENV) {
        var quantity = View;
        module.exports = {
          quantity
        };
      }
    }
  };
});

if (IN_NODE_ENV) {
  Factory.startAll();
}
