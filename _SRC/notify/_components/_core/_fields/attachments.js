var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('attachments-field', function (sb) {
	
	function View (fieldName, ui, obj, options) {

		function getSingleAttachmentHTML (attachment) {
									
			var ret = '';
			if (attachment.document_type === 'share') {
				ret = '<a href="'+ attachment.share_link +'">'+ attachment.name +'</a>';
			} else if (attachment.document_type === 'upload') {
				ret = '<a href="'+ sb.data.files.getURL(attachment)+'">'+ attachment.name +'</a>';
			} else {
				ret = attachment.name;
			}
			
			return ret;
			
		}

		if (!ui) {

			var ret = '';
			if (
				typeof obj[fieldName] === 'object'
				&& !_.isEmpty(obj[fieldName])
			) {
				
				if (Array.isArray(obj[fieldName])) {

					ret = '<ul class="edge-list">';
					
					_.each(obj[fieldName], function (attachment, i) {

						ret += '<li class="">' + getSingleAttachmentHTML(attachment) + '</li>';

					});

					ret += '</ul>';

				} else {

					ret += getSingleAttachmentHTML(obj[fieldName]);

				}
				
			} else {
				ret = '<i class="text-muted">Not set</i>';
			}

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}

		if(options.custom){
			sb.notify({
				type: 'show-custom-object-attachments',
				data: {
					dom: ui
					, state: {
						object: obj
						, fluid: false
						, size: 'mini'
						, patch: true
						, onUpdate: options.onUpdate
					}
					, options: options

				}
			});
		} else {
			sb.notify({
				type: 'show-object-attachments',
				data: {
					dom: ui
					, state: {
						object: obj
						, fluid: false
						, size: 'mini'
						, patch: true
						, onUpdate: options.onUpdate
					}

				}
			});
		}
		
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'attachments',
					view: View
				}
			});

			if (IN_NODE_ENV) {
				var attachments = View;
				module.exports = { 
					attachments
				}
			}
			
		}
		
	}
	
});

if (IN_NODE_ENV) {
	Factory.startAll();
}
