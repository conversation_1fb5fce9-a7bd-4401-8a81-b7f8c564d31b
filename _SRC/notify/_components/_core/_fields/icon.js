var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('icon-view', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }

	var icons = sb.dom.icons;

	icons = _.sortBy(icons, 'name');

	_.each(icons, function(icon, index) {
		icon.value = index
	});
	
	function getDisplayHTML (icon, options) {
		
		if (options.inCollection) {
			
			return '<i class="ui '+ icon.name +' icon"></i>';
			
		} else {
			
			return '<i class="ui '+ icon.name +' icon"></i> '+ icon.name;
			
		}
		
	}
	
	function View (fieldName, ui, obj, options) {

		function draw (ui, object, options) {
			
			var icon = _.findWhere(icons, {name: String(obj[fieldName])});
			
			if(!icon){
				icon = _.findWhere(icons, {value: parseInt(obj[fieldName])})
			}

			var css = 'edge-field';
			var style = 'padding:.78571429em 1em .78571429em 1em;';

			if (options.inCollection || options.mini) {
				css = '';
				style = '';
			}

			ui.makeNode('disp', 'div', {
				text: getDisplayHTML(icon, options),
				css: css,
				style: style
			});	
			
		}
		
		if (!obj[fieldName]) {

			obj[fieldName] = "add";

		}
		
		draw(ui, obj[fieldName], options);
		
		ui.disp.listeners.push(
			function (selector) {

				$(selector).on('click', function(){
					
					if (options.edit) {
						Edit(fieldName, ui, obj, options);					
					}
					
				});
				
			}
		);				
		
	}
	
	function Edit (fieldName, ui, obj, options) {
		
		ui.empty();

		if (typeof options.onEditStart === 'function') {
			options.onEditStart();
		}

		var where = '';
		var currentVal = false;
		
		if (obj && obj[fieldName]) {
			currentVal = _.findWhere(icons, {name: String(obj[fieldName])});
		}
		
		if (!currentVal) {
			currentVal = _.findWhere(icons, {value: 0});
		}
		
		var typeOptions = [{
			value: currentVal.value,
			name: currentVal.name,
			selected: true
		}];

		var selected = _.findWhere(icons, {name: String(obj[fieldName])});

		if (!selected){
			selected = _.findWhere(icons, {value: parseInt(obj[fieldName])})
		}

		var searchNode = ui.makeNode('select', 'div', {
			css: 'ui basic fluid dropdown search selection edge-field',
			style: 'display:inline-block',
			text: '<div class="text">'+ getDisplayHTML(selected, options) +'</div><i class="dropdown icon"></i>',
			listener: {
				type: 'dropdown',
				maxSelections:1,
				onChange: function(value, text, choice){
					
					if (!_.isEmpty(value) && value != obj[fieldName]) {
						
						// Get icon
						iconObject = _.findWhere(icons, {value: parseInt(value)});
						
						// Grab icon object for new value. 
						obj[fieldName] = iconObject.name;
					
						if (typeof options.onUpdate === 'function') {
							options.onUpdate(obj[fieldName]);
						}
						
						if (options.commitUpdates) {
							
							sb.data.db.obj.update(
								obj.object_bp_type,
								{
									id: obj.id,
									[fieldName]: iconObject.name
								}, 
								function (updates) {
									
									ui.empty();
									
									if (typeof options.onEditStart === 'function') {
										options.onEditEnd();
									}
										
									$(searchNode.selector).dropdown('destroy');
									
									View(fieldName, ui, obj, options);
									
									ui.patch();
									
									sb.notify({
										type: 'field-updated',
										data: {
											obj: updates,
											type: 'icon',
											property: fieldName
										}
									});
									
								}
							);
							
						} else {

							ui.empty();
							
							if (typeof options.onEditStart === 'function') {
								options.onEditEnd();
							}
								
							$(searchNode.selector).dropdown('destroy');
							View(fieldName, ui, obj, options);
							
							ui.patch();

						}
					
					}
					
				}
			},
			placeholder: 'Icon'
		}).makeNode(
			'menu'
			, 'div'
			, {
				css: 'menu'
			}
		);	
		
		_.chain(icons)
		.sortBy(function (i) {
			return (i.value);
		})
		.each(function (icon) {
				
			searchNode.makeNode('p'+ icon.value, 'div', {
				css: 'item',
				dataAttr: [
					{
						name: 'value',
						value: icon.value
					}
				]
			}).makeNode('txt', 'div', {
				css: 'text',
				tag: 'span',
				text: getDisplayHTML(icon, {})
			});
							
		});
		
		ui.select.listeners.push(function (selector) {
			$(selector).dropdown('show');			
		});
		
		ui.patch();
		
	}

	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'icon',
					title: 'Iconers',
					view: View,
					edit: Edit,
					icon: 'picture',
					propertyType: 'string',
					availableToEntities: true
				}
			});
			
		}

	}

});
