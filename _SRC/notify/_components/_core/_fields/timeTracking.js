var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');
	var moment = require('moment');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('timeTracking-view', function(sb){
	
	function View(fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			var tempTime = moment.duration(obj[fieldName]*1000);
			
			if (obj[fieldName]) {
				ret += tempTime.hours() +'h '+ tempTime.minutes() +'m';
			} else {
				ret += '0h 0m';
			}
			
			if (obj[fieldName +'_est']) {
				tempTime = moment.duration(obj[fieldName +'_est']*1000);
				ret += ' / '+ tempTime.hours() +'h '+ tempTime.minutes() +'m';
			}

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}
		
 		var viewState = {
	 		toggleDisplay: 		true
	 		, progressDisplay: 	true
	 		, fieldName: 		fieldName
 		};
 		
 		var viewOptions = {
	 		runningTime:	true
	 		, onUpdate: 	false
 		};

		_.mapObject(options, function(v, k){
			
			if (k == 'toggleDisplay' || k == 'progressDisplay'){
	
				viewState[k] = v;
		
			} else {
				
				viewOptions[k] = v;
			}		
							
		});
		
		if (options.onUpdate) {

			viewOptions.onUpdate = function(ui, response, state, options) {

				sb.notify({
					type: 'timeTracking-field-view'
					, data: {
						domObj: 	 	ui
						, obj:  		response
						, options: 		options
						, state: 		state
						, fieldName: 	fieldName
					}
				});		 		
				
				
			}.bind(null, ui)
			
		}

		sb.notify({
			type: 'timeTracking-field-view'
			, data: {
				domObj: 	 	ui
				, obj:  		obj
				, options: 		viewOptions
				, state: 		viewState
				, fieldName: 	fieldName
			}
		});
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'timeTracking'
					, view: View
				}
			});
			
			// Entity-friendly field registratoin.
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 						'timer'
					, destroy: 					function (fieldId, fieldName, ui, obj, state) {
						
						sb.notify({
							type: 'destroy-timer-field-view'
							, data: {
								_fieldId: 	fieldId
								, fieldName:fieldName
								, ui: 		ui
								, obj: 		obj
								, state: 	state
							}
						});
						
					}
					, options: {
						jobs: {
							type: 'tags'
							, name: 'Jobs'
							, tagType: 	'job'
						}
						, otherTags: {
							type: 'tags'
							, name: 'Other tags'
						}
						, showProgressBar: {
							name: 		'Show progress bar?'
							, type: 	'bool'
						}
						, showEstimatedTime: {
							name: 		'Show estimated time? (shows automatically when progress bar is enabled)'
							, type: 	'bool'
						}
						, showRateValue: {
							name: 		'Show Rate? (Time entries will use this rate value to store a rate dollar amount.)'
							, type: 	'bool'
						}
					}
					, view: 					View
					, title: 					'Timer'
					, availableToEntities: 		true
					, icon: 					'clock'
					, propertyType: 			'int'
					, blockRefresh: 			false
					, hideDefault:				true
				}
			});
			
		}
		
	}
	
});