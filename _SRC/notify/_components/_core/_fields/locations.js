var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('locations-field', function(sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	function View(fieldName, ui, obj, options) {

		var disabledCursor = '';
		
		if (!options.edit) {
			disabledCursor = 'cursor: not-allowed !important;';
		}

		function draw_loc(ui, loc, listFormat) {
			
			if(listFormat) {
				
				ui.makeNode('ui-'+loc.id, 'div', {
					text: '<strong> '+ loc.name +'</strong>',
					css: 'item',
					style: 'padding:8px; margin:2px 2px 0px 0px; background-color:#ebebeb; border-radius:0.375rem;' 
				});
				
			} else {
				
				ui.makeNode('ui-'+loc.id, 'div', {
					text: '<strong><i class="location arrow icon"></i> '+ loc.name +'</strong>',
					css: 'item' 
				});	
				
			}	
			
		}
		
		if(!_.isEmpty(obj[fieldName])) {
			
			ui.makeNode('locs', 'div', {
				css: 'ui vertical list edge-field'
				, style: 'cursor:pointer; ' + disabledCursor
			});
			
			if(Array.isArray(obj[fieldName])) {
				
				_.each(obj[fieldName], function(loc){

					draw_loc(ui.locs, loc, true);
					
				});
				
			} else if(typeof obj[fieldName] === 'object' && obj.id) {
				
				draw_loc(ui.locs, obj[fieldName]);
				
			}
			
		} else {
			
			ui.makeNode('locs', 'div', {
				text: 		'<div class="field-placeholder">Empty</div>'
				, css: 		'edge-field'
				, style: 	'cursor:pointer; ' + disabledCursor
			});
			
		}
		
		ui.locs.listeners.push(
			function (selector) {
				
				$(selector).on('click', function(){
					
					if(options.edit) {
					
						Edit(fieldName, ui, obj, options);
					
					}
					
				});
				
			}
		);
		
	}
	
	function Edit (fieldName, ui, obj, options) {
		
		var multiSelect = false;
		var searchNodeCSS = 'ui search selection transparent fluid dropdown';
		var locationOptions = [];
		var where = '';
		
		if(obj[fieldName]!== 0 
			&& obj[fieldName] !== null 
			&& obj[fieldName].hasOwnProperty('id')
		) {
			
			locationOptions = [
				{
					value: obj[fieldName].id,
					name: obj[fieldName].name,
					selected: true
				}
			];
			
		} else {
			
			locationOptions = [];
			
		}
		
		if(options.multi) {
			
			multiSelect = options.multi;
			
		}
		
		if (typeof options.onEditStart === 'function') {
			
			options.onEditStart();
			
		}
			
		if(multiSelect) {
			
			searchNodeCSS = 'ui search multiple selection transparent fluid dropdown';
			
			var locationOptions = _.map(obj[fieldName], function (loc) {

				return {
					value: loc.id,
					name: loc.name,
					selected: true
				};
				
			});
				
		}
		
		ui.empty();
		
		var searchNode = ui.makeNode('add', 'div', {
			css: searchNodeCSS,
			style: 'border:none;',
			text:
				'<input type="hidden" name="location">'+
				'<i class="dropdown icon"></i>'+
				'<input type="text" class="transparent search search-input" tabindex="0" style="padding:7px 15px !important; height:100%;">'+
				'<div class="default text">Find a location</div>'+
				'<div class="menu transition hidden" tabindex="-1"></div>',
			listener:{
				type: 'dropdown',
				values: locationOptions,
				saveRemoteData: false,
				minCharacters: 0,
				apiSettings: {
					cache: false,
					url: databaseConnection.obj.getSearchPath(
						'staff_base',
						where
					),
					onResponse: function (raw) {

						var results = [];
						_.each(raw.results, function (loc) {
							
							var selection = $(searchNode.selector).dropdown('get value');
							selection = selection.split(',');
							
							if (!_.isEmpty(loc.name)) {
								
								if (!_.contains(selection, loc.id.toString())) {
									
									results.push({
										value: parseInt(loc.id),
										name: loc.name
									});
									
								}
								
							}
							
						});
						
						if(!multiSelect) {
							
							results.unshift({
								value: 0,
								name: '<strong>No Location</strong>'
							});	
							
						}

						OptionsCache = results;
						
						return {
							results: results
						};
						
					},
					mockResponseAsync: function(settings, callback){
						
						function GetCookieValue(name) {
						    var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
						    return found.length > 0 ? found[0].split("=")[1] : null;
						}
						
						$.ajax({
							type: 'post',
							url: settings.url,
							xhrFields: {
								withCredentials: true
							},
							crossDomain: true,
							data: {},
							success: function (response) {
				
								callback(response);
								
							},
							error: function (jqXHR, status, error) {
				
								
								
							},
							headers: {
								'bento-token': GetCookieValue('token')
							}
						});	
						
					}
				},
				onHide: function () {
					
					if (typeof options.onEditEnd === 'function')
						options.onEditEnd();
					
					var selection = $(searchNode.selector).dropdown('get value');
					var locInts = [];

					if(multiSelect){
						
						selection = selection.split(',');
						
						_.each(selection, function(locId){
							if (parseInt(locId)) {
								
								locInts.push(
									parseInt(locId)
								);
								
							}
						});
						
					}else{
						
						locInts = parseInt(selection);
						
					}	

					searchNode.loading();
										
					var updates = {
							id: obj.id,
							[fieldName]: locInts,
							tagged_with: obj.tagged_with
						};
						
					if (options.hasOwnProperty('parseUpdates')) {
						updates = options.parseUpdates(updates);
					}

					sb.data.db.obj.update(
						obj.object_bp_type,
						updates, 
						function (updates) {

							obj[fieldName] = updates[fieldName];

							ui.empty();
							
							View(fieldName, ui, obj, options);
							
							ui.patch();
							
							sb.notify({
								type: 'field-updated',
								data: {
									obj: updates,
									type: 'locations',
									property: fieldName
								}
							});

							if (typeof options.onUpdate === 'function') {
													
								options.onUpdate(obj);
								
							}
							
						},
						1
					);
					
				}
			}
		});

		searchNode.listeners.push(function (selector) {
			
			$(selector).find('.search-input').focus();
						
		});
		
		ui.patch();
	}
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'locations',
					view: View,
					title: 'Location',
					availableToEntities: true
					, icon: 'location arrow'
					, propertyType: 'objectId'
					, objectType: ''
					, select: {
						name: 		true
						, fname: 	true
						, lname: 	true
					}
				}
			});
			
		}
		
	}
	
});