var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

var _templateOptions = {
	useStates : 'usa_states',
	custom: 'custom'
};

Factory.register('select-field', function (sb) {
	
	function View(fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			if (
				obj[fieldName]
				&& _.findWhere(options.options, {value:obj[fieldName].toString()})
			) {

				ret += _.findWhere(options.options, {value:obj[fieldName].toString()}).name;

			} else if (
				obj[fieldName]
				&& _.findWhere(options.options, {value:obj[fieldName]})
			) {

				ret += _.findWhere(options.options, {value:obj[fieldName]}).name;

			} else {
				ret += 'Not set'
			}

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}

		function buildSelectOptions(_template, options) {

			var selectOptions = [];

			//here can add more templates and values

			switch(_template){
				case _templateOptions.useStates:

					var _options = ['Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii',
						'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', ' Kentucky', 'Louisiana', 'Maine', 'Maryland', ' Massachusetts', 'Michigan',
						'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
						'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota', 'Tennessee',
						'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming'];

					selectOptions = _.map(_options, function (_item, index){
						return {
							name: _item.toString(),
							value: (index + 1).toString()
						};
					});

				break;
				default:
					if(!!options.selectedOptions){
						selectOptions = _.map(options.selectedOptions.split(','), function (_item) {
							return {
								value: _item.trim(),
								name: _item
							}
						});
					}
				break;
			}

			return selectOptions;


		}

		function getListOfTextFields(obj, callback){
			var bpName = obj.object_bp_type.replace('#','');
			sb.data.db.obj.getBlueprint(bpName, function(bp){
				var listOfItems = _.filter(bp, function(field) {
					return field.fieldType == 'detail' && field.type == 'string'
				})
				callback(listOfItems);
			});
		}

		function editOption (optionToUpdate, archive) {

			sb.notify ({
				type: 'show-edit-option-modal',
				data: {
					ui: ui,
					obj: obj,
					option: optionToUpdate,
					options: options,
					fieldName: fieldName,
					view: View
				}
			});
			
		}
		
		var currentVal = '';

		var selectOptions = [];

		if ( options.hasOwnProperty('blueprint') ) {
			if ( options.blueprint.hasOwnProperty([fieldName]) ) {
				if ( options.blueprint[fieldName].hasOwnProperty('options') ) {
					if ( options.blueprint[fieldName].options.hasOwnProperty('options') ) {
						if ( _.isObject(options.blueprint[fieldName].options.options) ) {
							selectOptions = options.blueprint[fieldName].options.options;
						}
					}
				}
			}
		} else if ( options.hasOwnProperty('options') ) {
			if (_.isObject(options.options)) {
				selectOptions = options.options;
			}
		}
		
		selectOptions = _.reject(selectOptions, {is_archived:true});

		//build the selections
		if(selectOptions.length === 0){
			selectOptions = buildSelectOptions(options.fieldType, options);
		}


		var selectedVal = false;
		function getSelectedValFromOpts(obj, fieldName, selectOptions, options) {

			if(options.multi) {
				selectedVal = obj[fieldName];

				return selectedVal;
			}

			var selectedVal = false;
			if (obj[fieldName] || obj[fieldName] === 0) {
			
				if(obj[fieldName] === 0){
					selectedVal = _.findWhere(selectOptions, {value: obj[fieldName]});
				} else {
					selectedVal = _.findWhere(selectOptions, {value: obj[fieldName].toString()});
				}
				
				if (!selectedVal) {
					selectedVal = _.findWhere(selectOptions, {value: obj[fieldName]});
				}
				if (!selectedVal) {
					selectedVal = _.findWhere(selectOptions, {value: obj[fieldName]});
				}
				if (!selectedVal) {
					selectedVal = obj[fieldName];
				}
				
			}

			return selectedVal;

		}
		selectedVal = getSelectedValFromOpts(obj, fieldName, selectOptions, options);

		if (
			selectedVal
			&& selectedVal.is_archived
		) {
			selectedVal = false;
		}
		
		// Setup placeholder
		var placeholderText = options.placeholder ? options.placeholder : 'Please make a selection';
		var placeholder = !_.isEmpty(selectedVal) ? '<div class="text truncate" style="left:-6px;">'+ selectedVal.name +'</div>' : '<div class="default text truncate field-placeholder" style="left:-6px;">' + placeholderText + '</div>';

		// Initialize values
		var values = [];

		if (!_.isEmpty(selectedVal)) {
			
			_.each(selectOptions, function (option, i) {

				if (
					(_.isObject(selectedVal) && selectedVal.value == option.value) // single object array
					|| (_.isString(selectedVal) && _.contains(selectedVal.split(','), option.value)) // comma separated string
					|| (_.isObject(selectedVal) && _.contains(selectedVal, option.value)) // 
				) {

					values[i] = {
						name: option.name,
						value: option.value,
						selected: true
					};

				} else {

					values[i] = {
						name: option.name,
						value: option.value
					};

				}	
				
			});

		} else {

			values = selectOptions;

		}

		// Empty the UI
		ui.empty();

		var multipleCSS = options.multi ? ' multiple' : '';
		var selectSetup = {
			css:'ui fluid search selection transparent dropdown edge-field' + multipleCSS,
			text: '<input type="hidden" placeholder="Property name" value="'+ currentVal +'"><i class="dropdown icon"></i>' + placeholder,
			listener: {
				type: 'dropdown',
				forceSelection: false,
				values: values,
				match: 'text',
				onShow() {

					setTimeout(function() {
						$('.floater-container').css({'overflow':'visible'});
					}, 100);
		
				},
				onHide() {

					setTimeout(function() {
						$('.floater-container').css({'overflow-y':'scroll', 'overflow-x':'auto'});
					}, 100);
		
				}
			}
		}

		function getPositions (allOptions, fieldValue) {
			var _currentvalue = [];
			if(fieldValue){
				if(fieldValue.split(',')){
					_currentvalue = fieldValue.trim().split(',');
				} else {
					_currentvalue = [fieldValue.trim()];
				}
			} else {
				_currentvalue = [fieldValue.trim()];
			}

			var positions = [];
			_.forEach(_currentvalue, function(_selectedValue, index){
				positions.push(allOptions.indexOf(_selectedValue.trim()));
			});

			return positions;
		}

		function getNarrativeOptions (options, fieldName) {
			if(options && options.blueprint && options.blueprint[fieldName] && options.blueprint[fieldName].options && options.blueprint[fieldName].options.relatedNarrativeOptions){
				let optionsNew = options.blueprint[fieldName].options.relatedNarrativeOptions.split("^");
				optionsNew.shift();

				optionsNew = _.map(optionsNew, function(item){
					item = item.replaceAll("^", "");
					return item;
				});

				return optionsNew;
			}
			return false;
		}

		var formattedContent = function(_str){
			_str = _str.replace(/\s+/g, ' ').trim();
			_str = _str.replaceAll("^", "\n");
			return _str;
		}

		function getAllOptions(options, fieldName) {
			if(options && options.options){
				var allOptions = _.map(options.options, function(item) {
					return item.value;
				});
				return allOptions;
			}
			else if(options.blueprint && options.blueprint[fieldName] && options.blueprint[fieldName].options && options.blueprint[fieldName].options.selectedOptions){
				return options.blueprint[fieldName].options.selectedOptions.split(',');
			}
			return false;
		}

		if (options.multi) {

			function getIsDeleted(select, aff){
				try {
					var currentSelected = select.split(',');
					var affectedSelected = aff.split(',');

					var isDeleted = false;
					if (!(currentSelected.includes(affectedSelected[0]))) {
						isDeleted = true;
					}

					return isDeleted;
				}catch(e){
					return false;
				}
			}

			selectSetup.listener.onChange = function (selected, affected) {

				var isDeleted = getIsDeleted(selected, affected);

				obj[fieldName] = $(ui.drop.selector).dropdown('get value');
					
				if (typeof options.onUpdate === 'function') {
					options.onUpdate(obj);
				}
				
				if (options.commitUpdates) {
					var newValue = "";
					var field = {[fieldName]: 	obj[fieldName]};
					if(options.relatedNarrativeField && !isDeleted){
						var narrativeOptions = getNarrativeOptions(options, fieldName);
						if(narrativeOptions) {
							var allOptions = getAllOptions(options, fieldName);
							var positions = getPositions(allOptions, affected);
							
							_.forEach(positions, function(position, index) {
								newValue += narrativeOptions[position];

								if(index < (positions.length - 1)) {
									newValue += ' ';
								}

							});

							//field = Object.assign(field, {[options.relatedNarrativeField] : newValue});

						}
					}

					sb.data.db.obj.update(
						obj.object_bp_type
						, {
							id: obj.id
							,
							...field
						}
						, function (r) {

							ui.empty();
							View(
								fieldName
								, ui
								, obj
								, options
							);
							ui.patch();

							if (options) {
								//update narrative field
								_.each(options.blueprint, function (_field, key) {
									if(key === options.relatedNarrativeField && !isDeleted) {
										sb.notify({
											type: 'get-field-id'
											, data: {
												obj: {
													id: obj.id
													, [key]: r[key]
												}
												, type: _field.fieldType
												, property: key
												, callback: function (fieldId) {

													if(fieldId && fieldId[0]) {
														$('#' + fieldId[0]._id + ' .ql-editor').append(newValue);
													}
												}
											}
										});
									}
								});
							}

							
						}
						, {
							[fieldName]: true
						}
					);
					
				}

			}

		} else {
		
			selectSetup.listener.onChange = function (selected) {
					
				// Remove placeholder
				$(ui.selector + ' .dropdown .text').removeClass('field-placeholder');

				if (selected === 'new') {
					
					editOption();

				} else {
					
					obj[fieldName] = selected;
					selectedVal = getSelectedValFromOpts(obj, fieldName, selectOptions, options);

					if (selectedVal) {
						if(selectedVal.value){
							obj[fieldName] = selectedVal.value;
						}else{
							if(parseInt(selectedVal)){
								obj[fieldName] = parseInt(selectedVal);
							}
						}
					}
					if (typeof options.onUpdate === 'function') {
						options.onUpdate(obj);
					}

					if (options.commitUpdates) {
						var field = {[fieldName]: 	obj[fieldName]};
						if(options.relatedNarrativeField){
							var narrativeOptions = getNarrativeOptions(options, fieldName);
							if(narrativeOptions) {
								var allOptions = getAllOptions(options, fieldName);
								var positions = getPositions(allOptions, obj[fieldName]);
								var newValue = "";

								_.forEach(positions, function(position, index) {
									newValue += narrativeOptions[position];

									if(index < (positions.length - 1)) {
										newValue += ' ';
									}

								});

								field = Object.assign(field, {[options.relatedNarrativeField] : newValue});
							}
						}

						sb.data.db.obj.update(
							obj.object_bp_type
							, {
								id: 			obj.id
								,
								...field
							}
							, function (r) {
								ui.empty();
								View(
									fieldName
									, ui
									, obj
									, options
								);
								ui.patch();

								if (options && typeof options.onUpdate === 'function') {
									// options.onUpdate(obj[fieldName]);
								}

								if (options) {
									//update narrative field
									_.each(options.blueprint, function (field, key) {
										if(key === options.relatedNarrativeField) {
											sb.notify({
												type: 'get-field-id'
												, data: {
													obj: {
														id: obj.id
														, [key]: r[key]
													}
													, type: field.fieldType
													, property: key
													, callback: function (fieldId) {
														if(fieldId && fieldId[0]) {
															$('#' + fieldId[0]._id + ' .ql-editor').html(r[key]);
														}
													}
												}
											});
										}
									});
									// Update formula fields
									_.each(options.blueprint, function (field, key) {

										if (
											field.fieldType === 'formula'
											&& r.hasOwnProperty(key)
										) {
											sb.notify({
												type: 'field-updated'
												, data: {
													obj: 		{
														id: 	obj.id
														, [key]: r[key]
													}
														, type: 	field.fieldType
													, property: key
												}
											});

										}

									});

								}
								
							}
						, 1
						);
						
					}
					
				}
					
			}

		}

		ui.makeNode('drop', 'div', selectSetup);

		ui.drop.listeners.push(
			function (selector) {
				$(selector).focus();
			}
		)

		if (options.updateOnLoad) {
			if (typeof options.onUpdate === 'function') {
				options.onUpdate(obj);
			}
		}
		
		ui.drop.makeNode('m', 'div', {
			css: 'menu',
			style:'z-index:1000;'
		});
		
		// Add a new option to the select field
		if (options.editBlueprint) {
			
			ui.drop.m.makeNode('new', 'div', {
				css: 'item'
				, dataAttr: [
					{
						name: 		'value'
						, value: 	'new'
					}
				]
			}).makeNode('txt', 'div', {
				css: 		'text'
				, tag: 		'span'
				, text: 	'<i class="green plus icon"></i> New'
			});
			
		}

		function removeSpecialChars (_str) {
			try {
				if (!_str) {
					return _str;
				}
				const noSpecialChars = _str.replace(/[^a-zA-Z0-9 ]/g, '');
				return noSpecialChars;
			}catch(e){
				return _str;
			}
		}
		
		// Layout options
		_.each(selectOptions, function (opt) {
			
			if (opt.is_archived) {
				return;
			}
			
			ui.drop.m.makeNode('v-'+ removeSpecialChars(opt.value), 'div', {
				css: 'item'
				, dataAttr: [
					{
						name: 'value'
						, value: opt.value
					}
				]
			}).makeNode('txt', 'div', {
				css: 'text'
				, tag: 'span'
				, text: opt.name
			});
			
			if (options.editBlueprint) {
			
				ui.drop.m['v-'+ removeSpecialChars(opt.value)]
					.makeNode('rm', 'div', {
						tag: 'i'
						, css: 'right floated grey remove icon'
					}).listeners.push(
						function (selector) {
							
							$(selector)
								.on('click', function (e) {
									
									e.stopPropagation();

									sb.notify ({
										type: 'remove-option',
										data: {
											ui: ui,
											obj: obj,
											option: opt.value,
											options: options,
											fieldName: fieldName,
											view: View
										}
									});
									
								});
							
						}
					);
				
				ui.drop.m['v-'+ removeSpecialChars(opt.value)]
					.makeNode('edit', 'div', {
						tag: 'i'
						, css: 'right floated grey pencil icon'
					}).listeners.push(
						function (selector) {
							
							$(selector)
								.on('click', function (e) {
									
									e.stopPropagation();
									editOption(opt.value, false);
								
								});
							
						}
					);
					
			}
			
		});
		
		ui.patch();
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'select',
					view: View,
					title: 'Select',
					availableToEntities: true,
					icon: 'caret square down',
					propertyType: 'string',
					options: {
						multi: {
							name: 'Allow multiple selections?',
							type: 'bool'
						},
						selectedOptions: {
						 	name: 	'Select options',
						 	type: 'text'
						 },
						fieldType: {
							name:'Option Type',
							type:'select',
							options:[
								{
									name:'Custom',
									value: _templateOptions.custom
								},
								{
									name:'USA States',
									value: _templateOptions.useStates
								},
							]
						},
						relatedNarrativeField: {
							name: 'Related Narrative Field',
							type: 'field',
							allowedTypes: ['detail'],
							multi: false
						},
						relatedNarrativeOptions: {
							name: 	'Narrative Options',
							type: 'text'
						},

					}
				}
			});

			if (IN_NODE_ENV) {
				var select = View;
				module.exports = { 
					select
				}
			}
			
		}
		
	}
	
});

if (IN_NODE_ENV) {
	Factory.startAll();
}
