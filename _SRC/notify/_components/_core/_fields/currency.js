var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('currency-view', function(sb){
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = !obj[fieldName] ? '$ 0.00' : '$ '+ (obj[fieldName]/100).formatMoney();

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;
			
		}

		var detailText = '$ 0.00';
		var editing = false;
		var defaultFlags = {
				alignRight: options.alignRight !== undefined ? options.alignRight : true // Default to right alignment for currency
			};
		var setBlueprint = false;
		
		if (obj[fieldName]) {
			detailText = '$ '+ (obj[fieldName]/100).formatMoney();
		}

		if (obj && typeof obj.object_bp_type === 'string') {
			setBlueprint = _.findWhere(appConfig.Types, {bp_name: obj.object_bp_type.substr(1)});
		}
		
		function editTxt (ui, obj, fieldName) {

            var currentValue = obj[fieldName] || 0;
			
			if (typeof options.onEditStart === 'function')
				options.onEditStart();
						
			$(ui.t.selector).html('<input class="ui field' + (defaultFlags.alignRight ? ' right aligned' : '') + '" value="$ '+ (currentValue/100).formatMoney() +'" type="text" name="quantity" style="border:none;background-color: white;">');

            if (!options.editing) {
				$(ui.t.selector).children().first().focus();
			}

            $(ui.t.selector +' input').on('change input', function(e){
				
				var newVal = parseInt($(this).val().replace(/[^0-9-]/g, '').replace(/(?!^)-/g, ''));
				if(isNaN(newVal)){
					newVal = 0;
				}

                if ( options.onChange && typeof options.onChange == 'function') {
                    options.onChange(newVal);
                }

				newVal = (newVal/100).formatMoney();
				newVal = '$ '+ newVal;
				$(this).val(newVal);
	
			});
			
			setTimeout(function () {
				
				$(ui.t.selector).children().first().focus(function() {	
					
				}).blur(function() {
					
					var newVal = parseInt($(this).val().replace(/[^0-9-]/g, '').replace(/(?!^)-/g, ''));

                    if (options.editing) {
						
						if (options.commitUpdates) {
							
							sb.data.db.obj.update(obj.object_bp_type, {
								id:obj.id,
								[fieldName]: newVal
							}, function(response){
								
								sb.data.db.obj.update(obj.object_bp_type, {
									id:obj.id,
									[fieldName]: newVal
								}, function(response){
								
									obj[fieldName] = response[fieldName];
		
									// Update formula fields
									if (setBlueprint) {
																	
										_.each(setBlueprint.blueprint, function (field, key) {
		
											if (
												field.fieldType === 'formula'
												&& response.hasOwnProperty(key)
											) {
		
												sb.notify({
													type: 'field-updated'
													, data: {
														obj: 		{
															id: 	obj.id
															, [key]: response[key]
														}
														, type: 	'formula'
														, property: key
													}
												});
		
											}
		
										});
		
									}
									
								}, 1);
	
							});
							
						} else {

							obj[fieldName] = newVal;

						}
						
					} else {
						
						if(typeof options.update === 'function') {
							
							options.update(obj, [fieldName], newVal, function(response) {
								
								options.update(obj, [fieldName], newVal, function(response) {
									
									obj[fieldName] = response[fieldName];
									
									if (typeof options.onEditEnd === 'function')
										options.onEditEnd();
									
									View(fieldName, ui, obj, options);					
									ui.patch();
									editing = false;
									
								});
								
							});
							
						} else {
							
							sb.data.db.obj.update(obj.object_bp_type, {
								id:obj.id,
								[fieldName]: newVal
							}, function(response){
								
								sb.data.db.obj.update(obj.object_bp_type, {
									id:obj.id,
									[fieldName]: newVal
								}, function(response){
								
									obj[fieldName] = response[fieldName];
									
									if (typeof options.onEditEnd === 'function')
										options.onEditEnd();
									
									View(fieldName, ui, obj, options);					
									ui.patch();
									editing = false;
									
								});
	
							});	
							
						}	
						
					}
					
				});
				
				$(ui.t.selector).children().first().on('keypress', function(e) {
					
					if(e.which == 13){
						
						var newVal = parseInt($(this).val().replace(/[^0-9-]/g, '').replace(/(?!^)-/g, ''));
		
						if (options.editing) {
							
							if (options.commitUpdates) {
								
								sb.data.db.obj.update(obj.object_bp_type, {
									id:obj.id,
									[fieldName]: newVal
								}, function(response){
									
									sb.data.db.obj.update(obj.object_bp_type, {
										id:obj.id,
										[fieldName]: newVal
									}, function(response){
									
										obj[fieldName] = response[fieldName];
			
										// Update formula fields
										if (setBlueprint) {
																		
											_.each(setBlueprint.blueprint, function (field, key) {
			
												if (
													field.fieldType === 'formula'
													&& response.hasOwnProperty(key)
												) {
			
													sb.notify({
														type: 'field-updated'
														, data: {
															obj: 		{
																id: 	obj.id
																, [key]: response[key]
															}
															, type: 	'formula'
															, property: key
														}
													});
			
												}
			
											});
			
										}
									
									}, 1);
		
								});
								
							} else {
								
								obj[fieldName] = newVal;
								
							}
							
						} else {
							
							if(typeof options.update === 'function') {
								
								options.update(obj, [fieldName], newVal, function(response) {
									
									options.update(obj, [fieldName], newVal, function(response) {
										
										obj[fieldName] = response[fieldName];
										
										if (typeof options.onEditEnd === 'function')
											options.onEditEnd();
										
										View(fieldName, ui, obj, options);					
										ui.patch();
										editing = false;
										
									});
									
								});
								
							} else {
								
								sb.data.db.obj.update(obj.object_bp_type, {
									id:obj.id,
									[fieldName]: newVal
								}, function(response){
									
									sb.data.db.obj.update(obj.object_bp_type, {
										id:obj.id,
										[fieldName]: newVal
									}, function(response){
										
										obj[fieldName] = response[fieldName];
										
										if (typeof options.onEditEnd === 'function')
											options.onEditEnd();
										
										View(fieldName, ui, obj, options);					
										ui.patch();
										editing = false;
										
									});
		
								});	
								
							}	
							
						}
						
					}
					
				});
				
			});
			
		}
		
		function checkOptions(options) {
			
			_.each(options, function(v, k) {
				
				if(defaultFlags.hasOwnProperty(k)) {
					defaultFlags[k] = v;	
				}
				
			});
			
		}

		checkOptions(options);
		
		// ***** CSS *****
		var tCSS_alignRight = '';
		// ***** *** *****
		
		if(defaultFlags.alignRight === true) {
			
			tCSS_alignRight = 'right aligned'
			
		}
			
		if (obj[fieldName]) {
			
			ui.makeNode('t', 'div', {
				text:'<nobr><span style="font-weight:400; word-wrap: break-word;">'+ detailText +'</span></nobr>',
				style:'font-weight:lighter;',
				css: 'ui ' + tCSS_alignRight
			});
			
		} else {
			
			ui.makeNode('t', 'div', {
				text:'<span style="font-weight:400; word-wrap: break-word;">$ 0.00</span>',
				style:'font-weight:lighter;',
				css: 'ui ' + tCSS_alignRight
			});
			
		}

		if (options.edit === false) {
			return;
		}
		
		if (options.editing) {
			
			ui.t.listeners.push(
				function (ui, obj, fieldName) {
					editTxt(ui, obj, fieldName);
				}.bind({}, ui, obj, fieldName)
			);
			return ;
			
		}
		
		ui.t.listeners.push(function(selector){
			
			$(selector).on('click', function(e){
				
				if(options.edit && !editing){
				
					e.stopPropagation();
					e.preventDefault();
					
					editTxt (ui, obj, fieldName);
					editing = true;
					
				}
				
			});
			
		});
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 					'currency'
					, view: 					View
					, title: 				'Value'
					, availableToEntities: 	true
					, icon: 					'dollar sign'
					, propertyType: 			'int'
				}
			});

			if (IN_NODE_ENV) {
				var currency = View;
				module.exports = { 
					currency
				}
			}
			
		}
		
	}
	
});

if (IN_NODE_ENV) {
	Factory.startAll();
}