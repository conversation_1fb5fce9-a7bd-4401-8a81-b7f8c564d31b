var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register ('reference-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	// View
	function View (fieldName, ui, obj, options) {

		var set = false;
		var fieldReference = false;
		// Determine if the field is setup properly
		var isSetup = false;
		var isLocked = false;

		ui.empty();

		if (
			!_.isEmpty(options.set)
			&& !_.isEmpty(options.field)
		) {
			
			set = _.findWhere(appConfig.Types, {bp_name: options.set.substr(1)});
			fieldReference = set.blueprint[options.field];

			if (fieldReference) {
				
				isSetup = true;
				fieldReference.options.editing = true;
				
			}
			
		}
		
		// Determine if field already has referenced value
		var hasReferencedValue = false;
		
		if ( obj.hasOwnProperty([fieldName + '_refId']) ) {
			
			if ( !_.isEmpty(obj[fieldName + '_refId'] ) ) {
				
				hasReferencedValue = true;
				
			}
		}
		
		if ( 
			obj.hasOwnProperty( [fieldName + '_lockedOn'] )
			&& !_.isEmpty( obj[fieldName + '_lockedOn'] ) 
		) {
			
			isLocked = true;
			fieldReference.options.editing = false;
			
		}

		// Show validation button
		if ( isSetup ) {

			// Show the field
			sb.notify ({
				type: 'view-field',
				data: {
					type: fieldReference.fieldType,
					property: fieldName,
					obj: obj,
					options: fieldReference.options,
					ui: ui
				}
			});
			
			ui.patch();
			
			// Show validate button
			if ( 
				options.shouldValidatePulledValue 
				&& !hasReferencedValue
				&& !isLocked 
			) {
			
				var validateBtn = ui.makeNode('i-' + obj.id + '-validateBtn', 'div', {
						css: 'ui circular mini basic icon button',
						text: '<i class="ui red exclamation triangle icon"></i> Validate',
						style: 'box-shadow:none;background-color:white !important;display:block;text-align:left;',
						listener:{
							type:'popup'
						},
						tooltip:{
							title:		'Clicking here will lock current value'
							, text:		''
							, position:	'right center'
						}
					});

				$(document).ready(function() {
					
					$(validateBtn.selector).on('click', function() {
								
						sb.dom.alerts.ask({
							title:'Are you sure?',
							text:'This will set the current field value the same as the referenced field.'
						}, function(response) {
							
							if(response) {
								
								swal.disableButtons();
								swal.close();
								
								sb.data.db.obj.runSteps({
									'lockReference': {
										obj: obj.id,
										options: {
											fieldName: fieldName
											, options: options
										}
									}
								},
								obj.id,
									function(response){
										
/*
										sb.notify({
											type: 'display-alert',
											data: {
												header: 'Success!',
												body: 'Field validated successfully.',
												color: 'green'
											}
										});
*/
										
									}
									
								);		
								
							}
							
						});
					
					});	
					
				});		
				
			}
		} else {
			
			ui.makeNode('i-'+ obj.id, 'div', {
				text: '<span class="ui text red"><i class="exclamation triangle icon"></i> Error: Please setup all required settings on this field.</span>',
				isTabbable: true
			});
			
		}

	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'reference',
					view: View,
					title: 'Reference',
					type: 'Link to Other Sets',
					availableToEntities: true,
					icon: 'sync',
					getIcon: function (options) {
						
						if ( !_.isEmpty(options.set) ) {
							
							var set = _.findWhere(appConfig.Types, {bp_name: options.set.substr(1)});
							var fieldReference = set.blueprint[options.field];
							
							if (fieldReference) {
								
								var icon = _.findWhere(appConfig.Fields, {name: fieldReference.fieldType}).icon;
							
								return icon;	
								
							}
							
						} else {
							
							return 'sync';
							
						}
						
					},
					options: {
						set: {
							name: 'Select a data set (required)',
							type: 'objectType'
						},
						field: {
							name: 'Referenced field (required):',
							type: 'field',
							multi: false,
							allowedTypes: ['currency', 'quantity', 'date', 'string', 'int', 'locations', 'address'],
							blueprint: 'set'
						},
						parentRef: {
							name: 'Filter by field from this set:',
							type: 'field',
							allowedTypes: ['groups', 'users', 'user', 'parent', 'companies', 'contacts', 'edge'],
							multi: false
						},
						shouldPush: {
							name: 'Allow changing referenced field when this field changes?',
							type: 'bool'
						},
						shouldValidatePulledValue: {
							name: 'Validate referenced field before pulling data into this field?',
							type: 'bool'
						}
						
					}
				}
			});
			
		}
		
	};
	
});
