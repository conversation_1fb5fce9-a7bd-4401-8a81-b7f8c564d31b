var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('duration-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }

	function View (fieldName, ui, obj, options) {

		if (options.type === 'picker') {

			$(document).ready(function() {

				ui.empty();

				var css = options.css ? options.css : '';
				if (!options.edit) {
					css += ' text-muted';
				}

				var style = options.style ? options.style : '';

				var disabled = '';
				if (!options.edit) {
					disabled = 'disabled';
				}

				ui.makeNode('i', 'div', {
					text: '<input class="html-duration-picker ' + css + '" style="border:none !important; background-color:transparent; outline:none; text-align:left; ' + style + '" data-duration="' + sb.dom.secondsToDurationFormat(obj[fieldName]) + '"' + disabled +'>'
				});

				ui.patch();

				HtmlDurationPicker.refresh();

				$(ui.i.selector + ' input').on('input', function(val) {

					obj[fieldName] = $(this).val();

					if (options.formatAsSeconds) {
						obj[fieldName] = sb.dom.durationToSeconds(obj[fieldName]);
					}

					if (typeof options.onUpdate === 'function') {
						options.onUpdate(obj[fieldName]);
					}
	
				});

			});

		} else {

			var retString = '';
			var animate = false;
			var formatString = null;
			
			var durationField = options.blueprint[fieldName];
			
			if (durationField.options.format) {
				if (durationField.options.format != 'default') {
					formatString = durationField.options.format;
				}
			}

			var startDate = moment(obj[durationField.options.startDateSelect]);
			var endDate = moment(obj[durationField.options.endDateSelect]);
			
			if (formatString) {
				var diff = endDate.diff(startDate, formatString);
				var preString = diff.toString() +' '+ formatString;
			} else {
				var diff = startDate.diff(endDate, 'seconds');
				var preString = moment.duration(diff, 'seconds').humanize();
			}
			retString = preString.charAt(0).toUpperCase() + preString.slice(1);
			
			if (obj[fieldName]) {
				
				// var preString = moment.duration(+obj[fieldName], 'seconds').humanize();
				// 
				// retString = preString.charAt(0).toUpperCase() + preString.slice(1);
				
			} else {
							
				// var startDate = moment(obj[durationField.options.startDateSelect]);
				// var endDate = moment(obj[durationField.options.endDateSelect]);
				// 
				// var diff = startDate.diff(endDate, 'seconds');
				// 
				// var preString = moment.duration(diff, 'seconds').humanize();
				// 
				// retString = preString.charAt(0).toUpperCase() + preString.slice(1);
				
				animate = 'animated flash';
				
			}
			
			ui.empty();
			
			ui.makeNode('t', 'div', {
				text: retString
				, css: animate
			});

		}
		
	}
	
	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'duration',
					title: 'Duration',
					availableToEntities: true,
					icon: 'calendar outline',
					options: {
						startDateSelect: {
							name: 	'Select a start date'
							, type: 'field'
							, allowedTypes: ['date']
						}
						, endDateSelect: {
							name: 	'Select an end date'
							, type: 'field'
							, allowedTypes: ['date']
						},
						format: {
							name: 'Format the duration'
							, type: 'select'
							, options: [
								{
									name: 'Default'
									, value: 'default'
								}
								, {
									name: 'Seconds'
									, value: 'seconds'
								}
								, {
									name: 'Days'
									, value: 'days'
								}
								, {
									name: 'Weeks'
									, value: 'weeks'
								}
								, {
									name: 'Months'
									, value: 'months'
								}
								, {
									name: 'Years'
									, value: 'years'
								}
							]
						}
					},
					view: View,
					propertyType: 'string',
					detail: function (key, obj, options) {
						
						if (options.inCollection) {
							return obj[key];							
						}
						
					},
					useFieldListener: true,
					focus: function (fieldName, ui, obj, options) {}
				}
			});

		}

	}

});
