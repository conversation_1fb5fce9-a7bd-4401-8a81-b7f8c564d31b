var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('email-field', function (sb) {
	
	// data functions
	
	function Construct (obj, callback) {}
	
	function Update (obj, callback) {}
	
	function Parse (obj, callback) {}
	
	// ui functions
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = obj[fieldName] ? obj[fieldName] : '';

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;
			
		}

		var underlineTxt = options.link ? '' : '';
		var fieldText = '<div class="" style="'+ underlineTxt +'display:inline-block;">'+ obj[fieldName] +'</div>';
		var icon;
		var parentName = '';
		var show_parent = false;
		var editable = true;
		var placeholder = fieldName;
		
		if (!options.hasOwnProperty('commitUpdates')) {
			options.commitUpdates = true;
		}
		
		if (_.isEmpty(obj[fieldName])) {
			obj[fieldName] = '';
		}
		placeholder = '<EMAIL>';
		
// 		var uidTxt = '';

		function editTxt (ui, obj, fieldName) {
			
			function commitUpdate (objType, update, callback) {
				
				if (options.commitUpdates) {
					sb.data.db.obj.update(objType, update, callback);
				} else {
					callback(update);
				}
				
			}
			
			if (typeof options.onEditStart === 'function')
				options.onEditStart();
			
			$(ui.selector).html('<input style="border:none;background-color:transparent;outline:none;width:100%;" placeholder="'+ placeholder +'" class="text" value="'+ obj[fieldName] +'">');
			
			if (!options.editing) {
				$(ui.selector).children().first().select();
			}
			
			$(ui.selector).children().first().change(function(val) {

				$(this).val($(this).val().trim());
				
				if (typeof options.onUpdate === 'function') {
					options.onUpdate($(this).val());
				}
				
				if (options.editing) {
					
					if (!options.commitUpdates) {
						
						obj[fieldName] = $(this).val();
						
						if (typeof options.update === 'function') {
							options.update(obj);
						}
						
					} else {
						
						commitUpdate (
							obj.object_bp_type
							, {
								id: 				obj.id
								, [fieldName]: 	$(this).val()
							}
							, function (response) {
							
						}, options);
						
					}
					
				} else if(typeof options.update === 'function') {
					
					options.update(obj, [fieldName], $(this).val(), function(response) {
						
						$(ui.selector).css('background-color', 'transparent');
						
						if(response){
							obj[fieldName] = response[fieldName];
						}else{
							$(ui.selector).children().first().removeClass('grey');
							$(ui.selector).children().first().addClass('red');
						}
						
						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();
						
						View(fieldName, ui, obj, options);					
						ui.patch();
						
						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 		response
								, type: 		'plain-text'
								, property: 	fieldName
							}
						});
						
					});
					
				} else {
					
					commitUpdate (obj.object_bp_type, {
						id:obj.id,
						[fieldName]:$(this).val()
					}, function(response){
						
						$(ui.selector).css('background-color', 'transparent');
						
						if(response){
							obj[fieldName] = response[fieldName];
						}else{
							$(ui.selector).children().first().removeClass('grey');
							$(ui.selector).children().first().addClass('red');
						}
						
						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();
						
						View(fieldName, ui, obj, options);					
						ui.patch();
						
						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 		response
								, type: 		'plain-text'
								, property: 	fieldName
							}
						});
						
					}, options);	
					
				}				
				
			});
			
			if (!options.editing) {
				
				$(ui.selector).children().first().focusout(function(){
					
					if (typeof options.onEditEnd === 'function')
						options.onEditEnd()
						
					View(fieldName, ui, obj, options);					
					ui.patch();
					
				});
				
			}
			
		}
		
		function format_objectName(obj) {
			
			switch(obj.object_bp_type) {
					
				case 'users':
				
					return obj.fname + ' ' + obj.lname;
					
					break;
					
				default:
					return obj.name;
				
			}
			
		}

		function isCurrent(obj){

			return (obj.object_bp_type == 'users' && obj.id == +sb.data.cookie.userId);
		}
		
		if (options.editing) {
			
			ui.listeners.push(
				function (ui, obj, fieldName) {
					editTxt(ui, obj, fieldName);
				}.bind({}, ui, obj, fieldName)
			);
			return ;
			
		}
		
		if (options.label) {
			placeholder = options.label;
		}
		
/*
		if(options.parent !== false) {

			if(_.isObject(obj[options.parent])) {

				if(
					!_.isEmpty(options.fields) 
					&& typeof options.fields[options.parent].shouldShow === 'function'
				) {
					
					if(options.fields[options.parent].shouldShow(obj)) {
						
						show_parent = true;
						
						parentName = format_objectName(obj[options.parent]);
						
					}
					
				} else {
					
					show_parent = true;
					
					parentName = format_objectName(obj[options.parent]);	
					
				}
				
			} 
			
		} 
*/
		if(options.hasOwnProperty('edit'))
			editable = options.edit;

		if(!_.isEmpty(obj[fieldName])){

			if(options && options.fields && options.fields[fieldName] && options.fields[fieldName].hasOwnProperty('charLimit')){
					
				fieldText = '<div class="" style="'+ underlineTxt +'display:inline-block;">'+ obj[fieldName].slice(0, options.fields[fieldName].charLimit) +'</div>';
									
			} 
			
// 			fieldText = uidTxt + fieldText;
			
			var href = sb.data.url.getObjectPageParams(obj, options);
			
			if (obj.hasOwnProperty('link')) {
				href = obj.link;
			}
			if (typeof options.link === 'function') {
				href = options.link(obj);
			}

			if(options.fields && options.fields[fieldName] && options.fields[fieldName].counter){
				
				if(obj.hasOwnProperty('comment_count')){
					
					var commentLabel = '<div class="ui mini basic yellow label" style="margin-left:6px;margin-top:-5px;">'+
										'<i class="comments outline icon"></i> '+ obj.comment_count +
								    '</div>';
					
					ui.makeNode('label', 'div', {css: 'right floated', text:commentLabel});
					
				}				
			}

			var htag = 'div';
			if (options.tag) {
				htag = options.tag;
			}

			var textDiv = '<'+ htag +' class="text" style="display:inline-block">'+ fieldText +'</'+ htag +'>';

			if(options.fields && options.fields[fieldName] && options.fields[fieldName].hasOwnProperty('icon')){
			
				if(typeof options.fields[fieldName].icon == 'function'){
						
					icon = options.fields[fieldName].icon(obj);
	
					if(!_.isNull(icon) && !_.isUndefined(icon))			
						textDiv = '<'+ htag +' class="text" style="display:inline-block">'+ icon +' '+ fieldText +'</'+ htag +'>';					
					
				}else if(_.isObject(options.fields[fieldName].icon)){
					
					icon = '<i class="'+ options.fields[fieldName].icon.type +' icon"></i>';
					
					if(options.fields[fieldName].icon.hasOwnProperty('color'))
						icon = '<i class="'+ options.fields[fieldName].icon.color +' '+ options.fields[fieldName].icon.type +' icon"></i>';
						
					textDiv = '<'+ htag +' class="text" style="display:inline-block">'+ icon +' '+ fieldText +'</'+ htag +'>';	

				}
									
			}			

			ui.makeNode('t', 'div', {
				tag: 'span'
			});

			if (typeof options.onClick === 'function') {

				ui.t.makeNode('name', 'div', {
					text: 	textDiv
				}).listeners.push(
					
					function (obj, onClick, selector) {
						
						$(selector).on('click', function () {
							onClick(obj);
						});
						
					}.bind({}, obj, options.onClick)
					
				);
				
			} else {

				ui.t.makeNode('name', 'div', {
					text: 	textDiv
					, href:	href
				});
				
			}

/*
			if(_.isObject(obj[options.parent])) {
				
				if(obj[options.parent].hasOwnProperty('id') && show_parent === true) {
					
					var pageURL = {
							type: obj[options.parent].object_bp_type, 
							id: obj[options.parent].id, 
							name: format_objectName(obj[options.parent]),
							group_type: obj[options.parent].group_type
						};
					
					if(options.onMobile) {
						
						ui.t.makeNode('parent', 'div', {
							tag: 'span',
							text: ' <a style="padding-left:0px !important;" class="link truncate" href="' + sb.data.url.createPageURL('object', pageURL) + '"> in ' + parentName +'</a>',
							style: 'margin-left: 5px;'
						});
						
					} else {
						
						ui.t.makeNode('parent', 'div', {
							tag: 'span',
							text: ' <nobr><a style="padding-left:0px !important;" class="link truncate" href="' + sb.data.url.createPageURL('object', pageURL) + '"> in ' + parentName +'</a></nobr>'
						});	
						
					}
					
				}	
				
			} 
*/
				
		} else {
			
			ui.makeNode('t', 'div', {
				text: '<i class="text-muted"> Click to save ' + placeholder + '</i>'
				, style: 'cursor:pointer;'
			});
			
		}
		
		ui.t.listeners.push(function(selector){
		
			$(selector).on('click', function(e){
				
				if(editable && isCurrent(obj)){
					
					e.stopPropagation();
					e.preventDefault();
					
					editTxt (ui, obj, fieldName);
				
				}
				
			});
			
		});		
		
	}
	
	function Edit (ui, obj, options) {
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'email-address'
					, type: 'Contact Info'
					, title: 'Email Address'
					, availableToEntities: true
					, icon: 'envelope'
					, view: View
					, edit: Edit
					, propertyType: 'string'
				}
			});

			if (IN_NODE_ENV) {
				var emailAddress = View;
				module.exports = { 
					emailAddress
				}
			}
			
		}
		
	}
	
});

if (IN_NODE_ENV) {
	Factory.startAll();
}