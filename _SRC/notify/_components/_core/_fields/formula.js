var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('formula-field', function (sb) {

    if (IN_NODE_ENV) {
        return;
    }

	// ui functions

	function View (fieldName, ui, obj, options) {

        var txt = '0';
        if (obj && obj[fieldName]) {
            txt = (obj[fieldName]).toString();
        }
        if (options && options.format === 'usd') {
            txt = '$ '+ (parseInt((obj[fieldName]))/100).formatMoney();
        }

        ui.makeNode(
            'v'
            , 'div'
            , {
                text:       '<nobr>'+txt+'</nobr>'
                , style:    'padding:8px;'
            }
        );
		
	}

	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name:   'formula',
					title:  'Formula',
					availableToEntities: true,
                    icon: 'teal equals',
                    // icon: 'fas fa-function',
                    iconType: 'fas',
					options: {
						formula: {
							type: 		'text'
							, name: 	'Formula (type in an algebraic expression here, e.g., "(2 + 2)/7)", and pull in (numeric) values from the current object with the this tag, like: "{{this.[Field Name Goes Here]}}")'
                        }
                        , format: {
                            type:       'select'
                            , name:     'Format'
                            , options:  [
                                {
                                    name:       'Number'
                                    , value:    'number'
                                }
                                , {
                                    name:       'USD ($)'
                                    , value:    'usd'
                                }
                            ]
                        }
					},
					view: View,
					propertyType: 'int',
					detail: function (key, obj, options) {
						
						if (!_.isEmpty(options.formula)) {
							return options.formula;							
						}
						
					}
				}
			});

		}

	}

});
