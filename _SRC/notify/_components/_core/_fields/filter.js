var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('filter-field', function (sb) {

    if (IN_NODE_ENV) {
        return;
    }

    var CachedFilter = {};

	// data functions

	// ui functions
    function getFilter (obj, blueprint, fieldName, options, callback) {

        // create new filter obj if none is set
        var field = blueprint[fieldName];

        if (_.isEmpty(field.options) || !field.options.filter) {
                                
            sb.data.db.obj.create('data_filter', {
                parent_object_type: sb.dom.blueprintNameFormatted(options.blueprints.contextType.bp_name),
                object_type: sb.dom.blueprintNameFormatted(options.blueprints.objectType.bp_name),
                where: {
                    operator: 'and',
                    filters: [{
                        field: 'parent',
                        value: 'current_record',
                        type: 'equals'
                    }]
                }
            }, function (filter) {
                    
                if (_.isEmpty(field.options)) {
                    field.options = {};
                }

                field.options.filter = filter.id;

                if (typeof options.updateField === 'function') {
                    options.updateField(field);
                }

                if (typeof callback === 'function') {
                    callback(filter);
                }
                    
            });
            
        } else {
            
            sb.data.db.controller('getDataFilter', {id: field.options.filter}, function(filter) {
                CachedFilter = JSON.parse(JSON.stringify(filter));
                callback(filter); 
            });
            
        }
        
    }

    function getWorkflows(blueprint, onComplete) {

		var workflowIds = [];
		
		_.each(blueprint, function (property, key) {

			if (
				property.fieldType === 'state'
				&& parseInt(property.workflow)
				&& !property.is_archived
			) {

				workflowIds.push(parseInt(property.workflow));

			}

		});

		sb.data.db.obj.getById(
			'entity_workflow'
			, workflowIds
			, function (workflows) {

				_.each(blueprint, function (property, key) {

					if (
						property.fieldType === 'state'
						&& !property.is_archived
					) {

						var wf = _.findWhere(workflows, {id: parseInt(property.workflow)});

						if (!_.isEmpty(wf)) {

							property.workflow = wf;

						}

					}

				});

				onComplete(blueprint);

			}
		);

	}

	function View (fieldName, ui, obj, options) {

        // !TODO: Allow this to be passed in
        var Type = options.blueprints.objectType;
        var ContextType = options.blueprints.contextType;
        var FilterGroup = {
            filters: [],
            operator: 'and'
        };

        function updateFilter(obj, callback) {

            var toSave = _.clone(FilterGroup);
            toSave.filters = [];
            _.each(FilterGroup.filters, function (filter) {
                
                if (Array.isArray(filter.value) && filter.value[0] && filter.value[0].id) {

                    toSave.filters.push({
                        field:      filter.field
                        , type:  filter.type
                        , isReference: filter.isReference
                        , value:    _.pluck(filter.value, 'id')
                    });

                } else if (!_.isEmpty(filter.value) && filter.value.id) {

                    toSave.filters.push({
                        field:      filter.field
                        , type:  filter.type
                        , isReference: filter.isReference
                        , value:    filter.value.id
                    });

                } else {

                    toSave.filters.push({
                        field:      filter.field
                        , type:  filter.type
                        , isReference: filter.isReference
                        , value:    filter.value
                        , date: filter.date
                    });

                }

            });

            sb.data.db.obj.update('data_filter', {
                id: obj.id,
                parent_object_type: sb.dom.blueprintNameFormatted(options.blueprints.contextType.bp_name),
                object_type: sb.dom.blueprintNameFormatted(options.blueprints.objectType.bp_name),
                where: toSave
            }, function (updatedFilter) {

                var refreshCollection = true;

                if (_.isEqual(CachedFilter.where, updatedFilter.where)) {
                    refreshCollection = false;
                }
                    
                if (typeof callback === 'function') {
                    callback(updatedFilter, refreshCollection);
                }                    

            });

        }

        function addFilter(obj) {

            FilterGroup.filters.push({
                field:      'tagged_with'
                , type:  'contains'
                , value:    []
            });

        }

        function removeFilter(obj, index) {

            FilterGroup.filters.splice(index, 1);

        }

        function viewFilters(obj, ui, filters) {

            // AND / OR selection
            function viewOperatorSelection(ui, filterGroup, i, onChange) {

                ui.makeNode('operatorSelectorContainer', 'div', {
                    css: 'ui one wide column',
                    style: 'min-width:70px; max-width:70px;  padding-top:5px !important; padding-right:0 !important; padding-bottom:0 !important;'
                })

                // First items in filter groups don't have an operator, they 
                // just say 'Where'.
                if (i === 0) {
                    
                    ui.operatorSelectorContainer.makeNode('where', 'div', {
                        text: 'Where',
                        style: 'text-align:center; margin-top:10px;'
                    });

                } else {

                    sb.notify ({
                        type: 'view-field',
                        data: {
                            type: 'select',
                            property: 'operator',
                            obj: filterGroup,
                            options: {
                                placeholder: 'And',
                                options: [
                                    {
                                        name: 'And',
                                        value: 'and'
                                    },
                                    {
                                        name: 'Or',
                                        value: 'or'
                                    }
                                ],
                                edit: true,
                                editing: true,
                                commitUpdates: false,
                                onUpdate(obj) {
    
                                    filterGroup.operator = obj.operator;
                                    onChange(filterGroup);
    
                                } 
                            },
                            ui: ui.operatorSelectorContainer.makeNode('operatorSelector', 'div', {
                                css: 'round-border',
                                style: 'min-width:58px; max-width:58px;'
                            })
                        }
                    });

                }

            }

            function getFields(filter) {

                var fieldOpts = [
                    {
                        value: 'tagged_with',
                        name: 'Tagged With',
                        fieldType: 'tags'
                    }, {
                        value: 'parent',
                        name: 'Parent',
                        fieldType: 'select',
                        type: 'int',
                        immutable: false,
                        options: {
                            multi: false,
                            options: [
                                {
                                    name: 'Current Record',
                                    value: 'current_record'
                                }
                            ]
                        }
                    }, {
                        value: 'status',
                        name: 'Status',
                        fieldType: 'select',
                        type: 'int',
                        immutable: false,
                        options: {
                            multi: false,
                            options: [
                                {
                                    name: 'Done',
                                    value: 'done'
                                }, {
                                    name: 'Open',
                                    value: 'open'
                                }
                            ]
                        }
                    }
                ]; 

                // Add fields from the blueprint
                _.each(Type.blueprint, function (field, key) {

                    if (!field.is_archived) {

                        switch (field.fieldType) {

                            case 'plain-text':
                            case 'contacts':
                            case 'companies':
                            case 'toggle':
                            case 'quantity':
                            case 'currency':
                            case 'edge':
                            case 'date':
                            case 'priority':
                            case 'detail':
                            case 'phone':
                            case 'title':
                            case 'select':
                                // Hide multi-selects for now
                                if (field.options && field.options.multi) {
                                    break;
                                }
                                fieldOpts.push({
                                    value: key,
                                    name: field.name,
                                    fieldType: field.fieldType
                                });
                                break;

                            case 'user':
                            case 'users':
                                fieldOpts.push({
                                    value: key,
                                    name: field.name,
                                    fieldType: field.fieldType,
                                    options: field.options
                                });
                                break;

                            case 'state':
                                fieldOpts.push({
                                    value: key,
                                    name: field.name + ' (Name)',
                                    fieldType: field.fieldType
                                });
                                fieldOpts.push({
                                    value: key + '_updated_on',
                                    name: field.name + ' (Last Updated)',
                                    fieldType: 'date'
                                });
                                break;

                        }

                    }

                });

                // If there's only one option, pre-select that value
                if (fieldOpts.length === 1) {
                    filter.type = fieldOpts[0].value;
                }

                return fieldOpts;

            }

            function getFieldOpts(filter) {

                var options = [];

                _.each(getFields(filter), function (opt) {

                    options.push({
                        name: '<i class="ui ' + sb.dom.getFieldIcon(Type.blueprint, opt, opt.value) + ' icon" style="margin-right:5px !important;"></i>' + opt.name,
                        value: opt.value,
                    });

                });

                return options;

            }

            // Field to filter on
            function viewFieldSelection(ui, filter, i) {

                ui.makeNode('fieldSelectorContainer', 'div', {
                    css: 'ui four wide column',
                    style: 'min-width:143px; max-width:143px; padding-top:5px !important; padding-left:5px !important; padding-right:0 !important; padding-bottom:0 !important;'
                });

                sb.notify ({
                    type: 'view-field',
                    data: {
                        type: 'select',
                        property: 'field',
                        obj: filter,
                        options: {
                            placeholder: 'Field...',
                            options: getFieldOpts(filter),
                            edit: true,
                            editing: true,
                            commitUpdates: false,
                            onUpdate(obj) {

                                // Update the field selection on the filter
                                filter.field = obj.field;
                                delete filter.value;

                                // Update the type & match value selection ui
                                viewFieldSelection(ui, filter, i);

                                // Update the compare options
                                filter.type = getCompareOpts(filter)[0].value;

                                viewCompareSelection(
                                    ui
                                    , filter
                                    , i
                                );

                                viewMatchSelection(
                                    ui
                                    , filter
                                    , i
                                );

                                ui.patch();

                            } 
                        },
                        ui: ui.fieldSelectorContainer.makeNode('fieldSelector', 'div', {
                            css: 'round-border',
                            style: 'min-width:140px; max-width:140px;'
                        })
                    }
                });

            }

            function getCompareOpts(filter) {

                var field = _.findWhere(getFields(filter), {value:filter.field});
                var fieldOpts = [];

                if (field && typeof field.fieldType === 'string') {

                    switch (field.fieldType) {
                        
                        case 'state':
                        case 'plain-text':
                        case 'state':
                        case 'checklist':
                        case 'toggle':
                        case 'detail':
                        case 'phone':
                        case 'select':
                        case 'contacts':
                        case 'companies':
                        case 'edge':
                            fieldOpts = [{
                                name:       'Is'
                                , value:    'equals'
                            }];
                            break;

                        case 'user':
                        case 'users':
                            fieldOpts = [{
                                name:       'Is'
                                , value:    'equals'
                            },{
                                name:       'Is Any Of'
                                , value:    'contains'
                            }];
                            break;
                        
                        case 'plain-text':
                        case 'title':
                            fieldOpts = [{
                                name:       'Is'
                                , value:    'equals'
                            }, {
                                name:       'Contains'
                                , value:    'contains'
                            }];
                            break;

                        // Numeric fields
                        case 'quantity':
                        case 'currency':
                        case 'priority':
                            fieldOpts = [{
                                name:       'Is'
                                , value:    'equals'
                            }, {
                                name:       'Is Greater Than'
                                , value:    'greater_than'
                            }, {
                                name:       'Is Less Than'
                                , value:    'less_than'
                            }];
                            break;

                        case 'date':
                            fieldOpts = [{
                                name:       'Is'
                                , value:    'on_same_day'
                            }, {
                                name:       'Is After'
                                , value:    'after'
                            }, {
                                name:       'Is Before'
                                , value:    'before'
                            }, {
                                name:       'Is On or After'
                                , value:    'on_or_after'
                            }, {
                                name:       'Is On or Before'
                                , value:    'on_or_before'
                            }];
                            break;

                    }

                }

                // For hard-coded filters, not necessarily for
                // fields that live on the blueprint.
                switch (filter.field) {

                    case 'tagged_with':
                        fieldOpts = [{
                            name:       'Contains'
                            , value:    'contains'
                        }
                        // !TODO Add NOT
                        ];
                        break;
                    
                    case 'parent':
                    case 'status':
                        fieldOpts = [{
                            name:       'Is'
                            , value:    'equals'
                        }];
                        break;

                }

                // If there's only one option, pre-select that value
                if (fieldOpts.length === 1) {
                    filter.type = fieldOpts[0].value;
                }

                return fieldOpts;

            }

            // Type of comparison to run on the field
            function viewCompareSelection(ui, filter, i) {

                ui.makeNode('compareSelectorContainer', 'div', {
                    css: 'ui four wide column',
                    style: 'min-width:113px; max-width:113px; padding-top:5px !important; padding-left:5px !important; padding-right:0 !important; padding-bottom:0 !important;'
                });

                sb.notify ({
                    type: 'view-field',
                    data: {
                        type: 'select',
                        property: 'type',
                        obj: filter,
                        options: {
                            placeholder: 'Is',
                            options: getCompareOpts(filter),
                            edit: true,
                            editing: true,
                            commitUpdates: false,
                            onUpdate(obj) {

                                filter.type = obj.type;

                                viewMatchSelection(
                                    ui
                                    , filter
                                    , i
                                );

                            } 
                        },
                        ui: ui.compareSelectorContainer.makeNode('compareSelector', 'div', {
                            css: 'round-border',
                            style: 'min-width:110px; max-width:110px;'
                        })
                    }
                });

            }

            // Selection of the value(s) to match in the comparison
            function viewMatchSelection(ui, filter, i) {

                var matchSelectorContainer = ui.makeNode('matchSelectorContainer', 'div', {
                    style: 'display: flex; flex-direction: column; flex: 1 1 auto; padding-top:5px !important; padding-left:5px !important; padding-right:0 !important; padding-bottom:0 !important; width:min-content;'
                });

                var css = filter.isReference ? '' : 'round-border';
                var matchSelector = ui.matchSelectorContainer.makeNode('matchSelector', 'div', {
                    css: css
                });

                var exactDateSelectorContainer = ui.makeNode('exactDateSelectorContainer', 'div', {
                    style: 'display:none; flex-direction: column; flex: 1 1 auto; padding-top:5px !important; padding-left:5px !important; padding-right:0 !important; padding-bottom:0 !important; width:min-content;'
                });

                var exactDateSelector = ui.exactDateSelectorContainer.makeNode('exactDateSelector', 'div', {
                    css: 'round-border'
                });

                function viewField(ui, filter, field) {

                    function updateFilterUI(ui, obj) {

                        $(document).ready(function() {

                            if (obj.value === 'exact_date') {

                                $(ui.matchSelectorContainer.selector).css({'min-width':'103px', 'max-width':'103px'});
                                $(ui.matchSelectorContainer.matchSelector.selector).css({'min-width':'100px', 'max-width':'100px'});
                                $(ui.exactDateSelectorContainer.selector).css({'display':'flex'});

                                sb.notify ({
                                    type: 'view-field'
                                    , data: {
                                        type: 'date'
                                        , property: 'date'
                                        , obj: filter
                                        , options: 	{
                                            placeholder: 'Value...',
                                            dateType: 'date',
                                            edit: 		        true
                                            , editing: 	        true
                                            , commitChanges:    false
                                            , commitUpdates:    false
                                            , is_template: 		false
                                            , isTemplate: 		false
                                            // , title: 	        ret[optionKey]
                                            , blueprint:{
                                                value: field
                                            }
                                        }
                                        , ui: exactDateSelector
                                    }
                                });

                                ui.exactDateSelectorContainer.exactDateSelector.patch();

                            } else {

                                $(ui.matchSelectorContainer.selector).css({'min-width':'inherit', 'max-width':'inherit'});
                                $(ui.matchSelectorContainer.matchSelector.selector).css({'min-width':'inherit', 'max-width':'inherit'});
                                $(ui.exactDateSelectorContainer.selector).css({'display':'none'});

                            }

                        });

                    }

                    if (!filter.value) {
                        if (field.hasOwnProperty('options')) {
                            if (field.options) {
                                if (field.options.hasOwnProperty('options')) {
                                    if (field.options.options) {
                                        filter.value = field.options.options[0].value
                                    }
                                }
                            }
                        }
                    }

                    var fieldType = _.clone(field).fieldType;

                    var options = {
                        placeholder: 'Value...',
                        edit: true,
                        editing: true,
                        commitChanges: false,
                        commitUpdates: false,
                        is_template: false,
                        isTemplate: false,
                        blueprint:{
                            value: field
                        },
                        onUpdate(obj) {
                            updateFilterUI(ui, obj);
                        }
                    }

                    if (field.hasOwnProperty('options')) {
                        if (field.options) {
                            _.each(field.options, function(option, key) {
                                options[key] = option;
                            });
                        }
                    }

                    switch(field.fieldType) {
                        case 'title':
                            fieldType = 'plain-text';
                            options.style = 'font-size:inherit !important; font-weight:inherit !important; padding: 10px 8px 10px 8px !important; height:auto !important;';
                            break;

                        case 'user':
                        case 'users':
                            if (!options.multi && filter.type == 'contains') {
                                options.multi = true;
                            }
                            break;
                    }

                    sb.notify ({
                        type: 'view-field'
                        , data: {
                            type: 			fieldType
                            , property: 	'value'
                            , obj:			filter
                            , options: options
                            , ui: matchSelector
                        }
                    });

                    updateFilterUI(ui, filter);

                    matchSelector.patch();

                }

                var field = _.findWhere(getFields(filter), {value:filter.field});

                // For references
                if (filter.isReference) {

                    var allowedTypes = [];
                    if (field && field.fieldType) {
                        allowedTypes = [field.fieldType];
                    }
                    if (filter.field === 'tagged_with') {
                        allowedTypes = ['user', 'contact', 'companies', 'parent', 'current_record', 'edge'];
                    }
                    
                    sb.notify({
                        type: 'view-field-selection',
                        data: {
                            ui: matchSelector,
                            blueprint: ContextType.blueprint,
                            options: {
                                allowedTypes: allowedTypes,
                                onUpdate: function (result, response) {

                                    filter.value = result;
                                    return;
                                                                    
                                },
                                value: filter.value
                            },
                            state: {}
                        }
                    });
                    return;
                }

                if (filter.field === 'tagged_with') {

                    var selectedTags = [];
                    if (
                        filter
                        && Array.isArray(filter.value)
                    ) {
                        selectedTags = filter.value;
                    }

                    // Tags
                    sb.notify ({
                        type: 'view-field',
                        data: {
                            type: 'tags',
                            property: 'value',
                            obj: filter,
                            options: {
                                tagList: selectedTags,
                                build: false,
                                canEdit: true,
                                style: 'min-height:30px;', 
                                // maxTagAdditions: 1,
                                onChange: function (response) {

                                    filter.value = response;
    
                                }
                            },
                            ui: matchSelector
                        }
                    });

                    return;

                } else if (field) {

                    switch (field.fieldType) {

                        case 'plain-text':
                        case 'state':
                        case 'checklist':
                        case 'toggle':
                        case 'quantity':
                        case 'currency':
                        case 'priority':
                        case 'detail':
                        case 'phone':
                        case 'title':
                        case 'select':
                            viewField(ui, filter, field);
                            break;

                        // Pointers need their child objs on initial load
                        case 'user':
                        case 'users':
                        case 'contacts':
                        case 'companies':
                        case 'edge':
                            if (
                                typeof filter.value === 'number'
                                || (
                                    Array.isArray(filter.value)
                                    && typeof filter.value[0] === 'number'
                                )
                            ) {

                                ui.makeNode('loader', 'div', {text: '<i class="notched circle loading icon"></i>'});
                                sb.data.db.obj.getById('', filter.value, function (childObjs) {

                                    delete ui.loader;
                                    ui.patch();

                                    filter.value = childObjs;
                                    viewField(ui, filter, field);

                                });

                            } else {

                                viewField(ui, filter, field);

                            }
                            break;

                        case 'date':
                            var fieldToUse = _.clone(field);
                            fieldToUse.fieldType = 'select';
                            fieldToUse.options = {
                                options: [
                                    {
                                        name: 'Today',
                                        value: 'today',
                                        selected: true
                                    },
                                    {
                                        name: 'Tomorrow',
                                        value: 'tomorrow',
                                        selected: false
                                    },
                                    // {
                                    //     name:'Day After Tomorrow',
                                    //     value:'day_after_tomorrow',
                                    //     selected: false
                                    // },
                                    {
                                        name:'Yesterday',
                                        value:'yesterday',
                                        selected: false
                                    },
                                    {
                                        name: 'One Week Ago',
                                        value: 'one_week_ago',
                                        selected: false
                                    },
                                    {
                                        name: 'One Month Ago',
                                        value: 'one_month_ago',
                                        selected: false
                                    },
                                    {
                                        name: 'One Quarter Ago',
                                        value: 'one_quarter_ago',
                                        selected: false
                                    },
                                    {
                                        name: 'One Year Ago',
                                        value: 'one_year_ago',
                                        selected: false
                                    },
                                    {
                                        name: 'One Week From Now',
                                        value: 'one_week_from_now',
                                        selected: false
                                    },
                                    {
                                        name: 'One Month From Now',
                                        value: 'one_month_from_now',
                                        selected: false
                                    },
                                    {
                                        name: 'One Quarter From Now',
                                        value: 'one_quarter_from_now',
                                        selected: false
                                    },
                                    {
                                        name: 'One Year From Now',
                                        value: 'one_year_from_now',
                                        selected: false
                                    },
                                    {
                                        name: 'Exact Date',
                                        value: 'exact_date',
                                        selected: false
                                    }
                                ]
                            }
                            viewField(ui, filter, fieldToUse)
                            break;

                    }
                    
                } else {

                    ui.makeNode('msg', 'div', {text: 'Select a field', css: 'ui message'});

                }

            }

            _.each(filters, function (filter, i) {

                // Container for the filter
                var form = ui.makeNode('f-'+ i, 'div', {css: 'ui grid'});

                // Operator
                viewOperatorSelection(
                    form
                    , FilterGroup
                    , i
                    , function () {

                        ui.empty();
                        viewFilters(obj, ui, filters);
                        ui.patch();

                    }
                );

                // Field
                viewFieldSelection(
                    form
                    , filter
                    , i
                    , form
                );

                // Compare Operator
                viewCompareSelection(
                    form
                    , filter
                    , i
                );

                // Match value
                viewMatchSelection(
                    form
                    , filter
                    , i
                );

                // Dropdown container
                form.makeNode('dropdownContainer', 'div', {
                    css: 'ui one wide column',
                    style: 'padding-top:5px !important; padding-left:5px !important; padding-bottom:0 !important;'
                });
 
                form.dropdownContainer.makeNode('btn', 'div', {
                    css: 'ui mini basic circular icon simple dropdown',
                    text: '<i class="ui ellipsis horizontal centered icon" style="font-size:1.5em !important; margin-top:10px;"></i>'
                });

                form.dropdownContainer.btn.makeNode('menu', 'div', {
                    css:'left menu'
                });
            
                form.dropdownContainer.btn.menu.makeNode('removeBtn', 'div', {
                    css: 'ui item',
                    text: '<i class="ui red trash icon"></i> Remove'
                }).notify('click', {
                    type: 'filter-field-run',
                    data: {
                        run: function (index) {
        
                            removeFilter(obj, index);
                            ui.empty();
                            viewFilters(obj, ui, FilterGroup.filters);
                            ui.patch();
        
                        }.bind({}, i)
                    }
                });

                var icon = 'pen';
                var text = 'Match to Entered Value'
                if (!filter.isReference) {
                    icon = 'linkify';
                    text = 'Match to Field on Parent Set'
                }
                form.dropdownContainer.btn.menu.makeNode('referenceBtn', 'div', {
                    css: 'ui item',
                    text: '<i class="ui blue ' + icon + ' icon"></i> ' + text
                }).notify('click', {
                    type: 'filter-field-run',
                    data: {
                        run: function (index) {
        
                            filter.isReference = !filter.isReference;
                            ui.empty();
                            viewFilters(obj, ui, FilterGroup.filters)
                            ui.patch();
        
                        }.bind({}, i)
                    }
                });

            });

        }
        
        // Set loader while the filter is being fetched
        ui.empty();
		ui.makeNode('loader', 'div', {
			css: 		''
			, text: 	'<div class="ui hidden divider"></div><br /><br /><i class="notched circle loading icon"></i>'
			, style: 	'width:100%;height:100%;text-align:center;'
		});
		ui.patch();

        getFilter(obj, ContextType.blueprint, fieldName, options, function (filterObj) {

            getWorkflows(Type.blueprint, function (blueprint) {

                FilterGroup = filterObj.where;
                if (_.isEmpty(FilterGroup)) {
                    FilterGroup = {
                        filters:        []
                        , operator:     'and'
                    };
                }

                // Container for the filters
                ui.empty();
                ui.makeNode('filters', 'div', {});
                viewFilters(filterObj, ui.filters, FilterGroup.filters);
                
                // Add filter button
                ui.makeNode('btns', 'div', {css: 'ui grid'});
                ui.btns.makeNode('l', 'div', {
                    css: 'ui eight wide column',
                    style: 'padding-bottom:0 !important;'
                }).makeNode('addFilter', 'div', {
                    css: 'ui tertiary button',
                    text: '<i class="ui green plus icon"></i> Add a filter',
                    style: 'float:left;'
                }).notify('click', {
                    type: 'filter-field-run',
                    data: {
                        run: function () {

                            $(ui.btns.l.addFilter.selector).addClass('loading');
                            addFilter(filterObj);
                            $(ui.btns.l.addFilter.selector).removeClass('loading');

                            ui.filters.empty();
                            viewFilters(filterObj, ui.filters, FilterGroup.filters);
                            ui.filters.patch();

                        }
                    }
                });

                // Save button
                ui.btns.makeNode('r', 'div', {
                    css: 'ui eight wide column',
                    style: 'padding-bottom:0 !important;'
                }).makeNode('saveFilter', 'div', {
                    css: 'ui green button',
                    text: '<i class="ui save icon"></i> Save',
                    style: 'float:right;'
                }).notify('click', {
                    type: 'filter-field-run',
                    data: {
                        run: function () {

                            $(ui.btns.r.saveFilter.selector).addClass('loading');

                            updateFilter(filterObj, function(updatedFilter, refreshCollection) {
                                
                                $(ui.btns.r.saveFilter.selector).removeClass('loading');
                                if (typeof options.onUpdate === 'function') {
                                    options.onUpdate(updatedFilter, refreshCollection);
                                }

                            });
                            
                        }
                    }
                });

                ui.patch();

            });

        });

    }

    return {

		init: function () {

            sb.listen({
                'filter-field-run': this.run
            });

            // if (appConfig.instance === 'rickyvoltz') {

                sb.notify({
                    type: 'register-field-type',
                    data: {
                        name:                   'filter',
                        title:                  'Filter',
                        availableToEntities:    true,
                        icon:                   'filter',
                        options:                {},
                        view:                   View,
                        propertyType:           'object'
                    }
                });

            // }

		}

        , run: function (data) {

            data.run(data);
            
        }

	}

});
