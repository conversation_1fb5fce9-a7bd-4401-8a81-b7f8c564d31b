var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('url-field', function (sb) {
	
	// data functions
	
	function Construct (obj, callback) {}
	
	function Update (obj, callback) {}
	
	function Parse (obj, callback) {}
	
	// ui functions
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = obj[fieldName] ? obj[fieldName] : '';

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;
			
		}

		var underlineTxt = options.link ? '' : '';
		var fieldText = '<div class="" style="'+ underlineTxt +'display:inline-block;">'+ obj[fieldName] +'</div>';
		var icon;
		var parentName = '';
		var show_parent = false;
		var editable = true;
		var placeholder = fieldName;
		
		if (!options.hasOwnProperty('commitUpdates')) {
			options.commitUpdates = true;
		}
		
		if (_.isEmpty(obj[fieldName])) {
			obj[fieldName] = '';
		}
		placeholder = 'Empty';
		
		function editTxt (ui, obj, fieldName) {
			
			function commitUpdate (objType, update, callback) {
				
				if (options.commitUpdates) {
					sb.data.db.obj.update(objType, update, callback);
				} else {
					callback(update);
				}
				
			}
			

			var url = sb.dom.cleanURL(obj[fieldName]);
			
			if (typeof options.onEditStart === 'function')
				options.onEditStart();
			
			$(ui.selector).html(
				'<input '+
					'style="'+
						'border:none;'+
						'background-color:transparent;'+
						'outline:none;'+
						'width:100%;'+
						'text-decoration: underline;" '+
					'placeholder="'+ placeholder +'" '+
					'class="text" '+
					'value="'+ obj[fieldName] +
				'">'+
				'<div class="revealChild"><a id="'+ ui.selector.replace('.', '') +'" class="ui basic link mini button" style="position:absolute;top:18px;right:18px;" target="_blank" href="'+ url +'"><i class="ui linkify icon"></i> Open</a></div>'
			);
			
			if (!options.editing) {
				$(ui.selector).children().first().select();
			}
			
			$(ui.selector).children().first().on('input', function () {
				
				var link = $(this).val();
				obj[fieldName] = link;

				// Update the link's href
				document.getElementById(ui.selector.replace('.', ''))
					.href = link;
				
			});
			
			$(ui.selector).children().first().change(function(val){
				
				// Set link
				var link = $(this).val();
				obj[fieldName] = link;
				
				// Update HREF attribute
				if ( link.substring(0, 8) === 'https://' || link.substring(0, 7) === 'http://' ) {
					document.getElementById(ui.selector.replace('.', '')).href = $(this).val();
				} else {
					document.getElementById(ui.selector.replace('.', '')).href = 'https://' + $(this).val();
				}
				
				if (options.editing) {
					
					if (!options.commitUpdates) {
						
						if (typeof options.update === 'function') {
							options.update(obj);
						}
						
					} else {
						
						commitUpdate (
							obj.object_bp_type
							, {
								id: 			obj.id
								, [fieldName]: 	$(this).val()
							}
							, function (response) {
							
						}, options);
						
					}
					
				} else if(typeof options.update === 'function') {
					
					options.update(obj, [fieldName], $(this).val(), function(response) {
						
						$(ui.selector).css('background-color', 'transparent');
						
						if(response){
							obj[fieldName] = response[fieldName];
						}else{
							$(ui.selector).children().first().removeClass('grey');
							$(ui.selector).children().first().addClass('red');
						}
						
						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();
						
						View(fieldName, ui, obj, options);					
						ui.patch();
						
						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 		response
								, type: 		'plain-text'
								, property: 	fieldName
							}
						});
						
					});
					
				} else {
					
					commitUpdate (obj.object_bp_type, {
						id:obj.id,
						[fieldName]:$(this).val()
					}, function(response){
						
						$(ui.selector).css('background-color', 'transparent');
						
						if(response){
							obj[fieldName] = response[fieldName];
						}else{
							$(ui.selector).children().first().removeClass('grey');
							$(ui.selector).children().first().addClass('red');
						}
						
						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();
						
						View(fieldName, ui, obj, options);					
						ui.patch();
						
						sb.notify({
							type: 'field-updated'
							, data: {
								obj: 		response
								, type: 		'plain-text'
								, property: 	fieldName
							}
						});
						
					}, options);	
					
				}				
				
			});
			
			if (!options.editing) {
				
				$(ui.selector).children().first().focusout(function(){
					
					if (typeof options.onEditEnd === 'function')
						options.onEditEnd();
						
					View(fieldName, ui, obj, options);					
					ui.patch();
					
				});
				
			}
			
		}
		
		function format_objectName(obj) {
			
			switch(obj.object_bp_type) {
					
				case 'users':
				
					return obj.fname + ' ' + obj.lname;
					
					break;
					
				default:
					return obj.name;
				
			}
			
		}

		function isCurrent(obj){

			return (obj.object_bp_type == 'users' && obj.id == +sb.data.cookie.userId);
		}
		
		if (options.editing) {
			
			ui.listeners.push(
				
				function (ui, obj, fieldName) {
				
					editTxt(ui, obj, fieldName);
				
				}.bind({}, ui.makeNode('c', 'div', {css:'revealParent'}), obj, fieldName)
				
			);
			return ;
			
		}
		
		if (options.label) {
			placeholder = options.label;
		}
		
		if(options.hasOwnProperty('edit'))
			editable = options.edit;

		if(!_.isEmpty(obj[fieldName])){

			ui.makeNode('t', 'div', {
				text: obj[fieldName]
				, style: 'cursor:pointer;text-decoration:underline;'
			});
				
		} else {
			
			ui.makeNode('t', 'div', {
				text: '<i class="text-muted"> Click to save ' + placeholder + '</i>'
				, style: 'cursor:pointer;'
			});
			
		}
		
		ui.t.listeners.push(function(selector){
		
			$(selector).on('click', function(e){
				
				if(editable && isCurrent(obj)){
					
					e.stopPropagation();
					e.preventDefault();
					
					editTxt (ui, obj, fieldName);
				
				}
				
			});
			
		});		
		
	}
	
	function Edit (ui, obj, options) {
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 					'url'
					, title: 				'URL'
					, availableToEntities: 	true
					, icon: 				'linkify'
					, view: 				View
					, edit: 				Edit
					, propertyType: 		'string'
				}
			});

			if (IN_NODE_ENV) {
				var url = View;
				module.exports = { 
					url
				}
			}
			
		}
		
	}
	
});

if (IN_NODE_ENV) {
	Factory.startAll();
}