var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('qty-view', function(sb){

	if (IN_NODE_ENV) {
        return;
    }
	
	function View (fieldName, ui, obj, options) {
		
		var detailText = obj[fieldName];
		var editing = false;
		
		function editTxt (ui, obj, fieldName) {
			
			var currentValue = obj[fieldName] || 1;
			
			if (typeof options.onEditStart === 'function')
				options.onEditStart();
			
			$(ui.selector).html('<input class="pda-form pda-form-fullWidth field" value="'+ currentValue +'" type="number" name="quantity" style="border:none;background-color: white;outline:none;">');
			$(ui.selector).children().first().focus();
			
			$(ui.selector).children().first().focusout(function(val){
				
				var newVal = parseInt($(ui.selector).children().first().val());
				
				if(typeof options.update === 'function') {
					
					options.update(obj, [fieldName], newVal, function(response) {
						
						obj[fieldName] = response[fieldName];
						
						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();
						
						View(fieldName, ui, obj, options);					
						ui.patch();
						editing = false;
						
					});
					
				} else {
					
					sb.data.db.obj.update(obj.object_bp_type, {
						id:obj.id,
						[fieldName]: newVal
					}, function(response){
						
						obj[fieldName] = response[fieldName];
						
						if (typeof options.onEditEnd === 'function')
							options.onEditEnd();
						
						View(fieldName, ui, obj, options);					
						ui.patch();
						editing = false;
						
					});	
					
				}				
				
			});
			
		}
			
		if (obj[fieldName]) {

			ui.makeNode('t', 'div', {
				text:'<span style="font-weight:400; word-wrap: break-word;">'+ detailText +'</span>',
				style:'font-weight:lighter;'
			});
			
		} else {
			
			ui.makeNode('t', 'div', {
				text:'<span style="font-weight:400; word-wrap: break-word;">Not set</span>',
				style:'font-weight:lighter;'
			});
			
		}
		
		ui.listeners.push(function(selector){
			
			$(selector).on('click', function(e){
				
				if(options.edit && !editing){
				
					e.stopPropagation();
					e.preventDefault();
					
					editTxt (ui, obj, fieldName);
					editing = true;
					
				}
				
			});
			
		});
		
	}
	
	return {
		
		init: function () {
			
/*
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'qty'
					, view: View
					, title: 'Quantity'
					, availableToEntities: true
					, icon: 'times'
					, type: 'inventory'
				}
			});
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'recipe'
					, view: View
					, title: 'Recipe'
					, availableToEntities: true
					, icon: 'list'
					, type: 'inventory'
				}
			});
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'qty'
					, view: View
					, title: 'Scancode'
					, availableToEntities: true
					, icon: 'qrcode'
					, type: 'inventory'
				}
			});
*/
			
			/*
sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'edge'
					, view: View
					, title: 'Pointer'
					, availableToEntities: true
					, icon: 'external square'
// 					, type: 'inventory'
				}
			});
*/
			
/*
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'handshake'
					, view: View
					, title: 'Handshake'
					, availableToEntities: true
					, icon: 'handshake outline'
					, type: 'Collaboration'
				}
			});
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'handshake'
					, view: View
					, title: 'Signature'
					, availableToEntities: true
					, icon: 'thumbs up'
					, type: 'Collaboration'
				}
			});
*/
			
		}
		
	}
	
});