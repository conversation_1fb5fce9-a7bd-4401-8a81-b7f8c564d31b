var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('service-field', function(sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	function View(fieldName, ui, obj, options) {
		
		var boldStyle = true;

		function draw_serv(ui, serv, listFormat) {
			
			var textDisplay = '<strong>'+ serv.name +'</strong>';
			
			if(!boldStyle) {
				textDisplay = serv.name;
			} 
			
			if(listFormat) {
				
				ui.makeNode('ui-'+serv.id, 'div'
					, {
						text: textDisplay
						, css: 'item'
						, style: 'padding:8px; margin:2px 2px 0px 0px; background-color:#ebebeb; border-radius:0.375rem;' 
					}
				);
				
			} else {
				
				ui.makeNode('ui-'+serv.id, 'div'
					, {
						text: textDisplay
						, css: 'item' 
					}
				);	
				
			}	
			
		}
		
		if(options.hasOwnProperty('bold') && options.bold === false) {
			
			boldStyle = false;
			
		}
		
		if(!_.isEmpty(obj[fieldName])) {
			
			ui.makeNode('servs', 'div'
				, {
					css: 'ui horizontal list'
					, style: 'cursor:pointer;'
				}
			);
			
			if(Array.isArray(obj[fieldName])) {
				
				_.each(obj[fieldName], function(serv){

					draw_serv(ui.servs, serv, true);
					
				});
				
			} else if(typeof obj[fieldName] === 'object' && obj.id) {
				
				draw_serv(ui.servs, obj[fieldName]);
				
			}
			
		} else {
			
			ui.makeNode('servs', 'div', {
				text: '<i class="text-muted briefcase icon"></i><i class="text-muted">Click to select service</i>'
				, style: 'cursor:pointer;'
			});
			
		}
		
		ui.servs.listeners.push(
			function (selector) {

				$(selector).on('click', function(){
					
					if(options.edit) {
					
						Edit(fieldName, ui, obj, options);
					
					}
					
				});
				
			}
		);
		
	}
	
	function Edit (fieldName, ui, obj, options) {
		
		var multiSelect = false;
		var searchNodeCSS = 'ui search selection transparent fluid dropdown';
		var serviceOptions = [];
		var where = '';
		
		if(obj[fieldName]!== 0 
			&& obj[fieldName] !== null 
			&& obj[fieldName].hasOwnProperty('id')
		) {
			
			serviceOptions = [
				{
					value: obj[fieldName].id
					, name: obj[fieldName].name
					, selected: true
				}
			];
			
		} else {
			
			serviceOptions = [];
			
		}
		
		if(options.multi) {
			
			multiSelect = options.multi;
			
		}
		
		if (typeof options.onEditStart === 'function') {
			
			options.onEditStart();
			
		}
			
		if(multiSelect) {
			
			searchNodeCSS = 'ui search multiple selection transparent fluid dropdown';
			
			var serviceOptions = _.map(obj[fieldName], function (serv) {

				return {
					value: serv.id
					, name: serv.name
					, selected: true
				};
				
			});
				
		}
		
		ui.empty();
		
		var searchNode = ui.makeNode('add', 'div', {
			css: searchNodeCSS
			, style: 'border:none;'
			, text:
				'<input type="hidden" name="service">'+
				'<i class="dropdown icon"></i>'+
				'<input type="text" class="transparent search search-input" tabindex="0" style="padding:7px 15px !important; height:100%;">'+
				'<div class="default text">Select a Service</div>'+
				'<div class="menu transition hidden" tabindex="-1"></div>'
			, listener:{
				type: 'dropdown'
				, values: serviceOptions
				, saveRemoteData: false
				, minCharacters: 0
				, apiSettings: {
					cache: false
					, url: databaseConnection.obj.getSearchPath(
						'inventory_service'
						, where
					)
					, onResponse: function (raw) {

						var results = [];
						
						_.each(raw.results, function (serv) {
							
							var selection = $(searchNode.selector).dropdown('get value');
							
							selection = selection.split(',');
							
							if (!_.isEmpty(serv.name)) {
								
								if (!_.contains(selection, serv.id.toString())) {
									
									results.push({
										value: parseInt(serv.id)
										, name: serv.name
									});
									
								}
								
							}
							
						});
						
						if(!multiSelect) {
							
							results.unshift({
								value: 0
								, name: '<strong>No Service</strong>'
							});	
							
						}

						OptionsCache = results;
						
						return {
							results: results
						};
						
					}
					, mockResponseAsync: function(settings, callback){
						
						function GetCookieValue(name) {
						    var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
						    return found.length > 0 ? found[0].split("=")[1] : null;
						}
						
						$.ajax({
							type: 'post',
							url: settings.url,
							xhrFields: {
								withCredentials: true
							},
							crossDomain: true,
							data: {},
							success: function (response) {
				
								callback(response);
								
							},
							error: function (jqXHR, status, error) {
				
								
								
							},
							headers: {
								'bento-token': GetCookieValue('token')
							}
						});	
						
					}
				}
				, onHide: function () {

					if (typeof options.onEditEnd === 'function')
						options.onEditEnd();
					
					var selection = $(searchNode.selector).dropdown('get value');
					var servInts = [];
					var cachedServices = obj.service;

					if(multiSelect){
						
						selection = selection.split(',');
						
						_.each(selection, function(servId){
							if (parseInt(servId)) {
								
								servInts.push(
									parseInt(servId)
								);
								
							}
						});
						
					}else{
						
						servInts = parseInt(selection);
						
					}	

					searchNode.loading();
										
					var updates = {
							id: obj.id
							, [fieldName]: servInts
							, tagged_with: obj.tagged_with
						};
						
					if (options.hasOwnProperty('parseUpdates')) {
						updates = options.parseUpdates(updates);
					}

					sb.data.db.obj.update(
						obj.object_bp_type
						, updates 
						, function (updates) {

							sb.notify({
								type: 'update-job-type-group'
								, data: {
									userId: obj.id
									, cachedServices: cachedServices
									, serviceList: servInts
								}
							})

							obj[fieldName] = updates[fieldName];

							ui.empty();
							
							View(fieldName, ui, obj, options);
							
							ui.patch();
							
							if (typeof options.onComplete == 'function') {
								options.onComplete(updates);
							}
							
							sb.notify({
								type: 'field-updated'
								, data: {
									obj: updates
									, type: 'service'
									, property: fieldName
								}
							});
							
						}
						, 1
					);
					
				}
			}
		});

		searchNode.listeners.push(function (selector) {
			
			$(selector).find('.search-input').focus();
						
		});
		
		ui.patch();
	}
	
	return {
		
		init: function() {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'service'
					, view: View
				}
			});
			
		}
		
	}
	
});