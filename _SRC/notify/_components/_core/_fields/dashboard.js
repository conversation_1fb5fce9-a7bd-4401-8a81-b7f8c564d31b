var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('dashboard-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }

	function View (fieldName, ui, obj, options) {

		var state = appConfig.state;
		var fieldOptions = options.blueprint[fieldName].options;
		
		state.pageObject = obj;
		state.parent = obj;
		state.setId = obj.object_bp_type+'.'+fieldName;
		state.setTags = [];
		
		
		if(fieldOptions.perEntry === true){
			state.setId = obj.id+'.'+fieldName;
		}
		
		state.tool = {
				settings:{
					tags:[]
				}
			};
		
		if(options.editBlueprint == false){
			state.viewOnly = true;
		}
		
		state.id = state.pageObject.id;
		
		if(!obj.id){
			
			// don't show the dashboard if the object hasn't been created yet
			ui.makeNode('alert','div',{css:'ui yellow text', text:'You need to create the entry before you can edit it\'s dashboard.'});
			
		}else{
			
			if(options.inCollection === true){
								
				ui.makeNode('button','div',{css:'ui mini icon basic button', text:'<i class="ui tachometer alternate icon"></i> View Dashboard'}).notify('click', {
					type:'get-sys-modal',
					data:{
						callback: 	function (modal) {

							var state = this.state;
							var obj = this.obj;
							var fieldOptions = this.fieldOptions;
//console.log('this',this);							
							if(fieldOptions.inheritsTags == true){
//console.log('obj.id',obj.id);	
								state.tool.settings.tags = obj.tagged_with;
								state.tool.settings.tags.push(obj.id);
								state.parent.tagged_with = state.tool.settings.tags;
								state.setTags = [obj.id];
								state.rsetTags = [obj.id];
							}
							
							state.id = obj.id;
							state.pageObjectType = obj.object_bp_type;
							
							if(fieldOptions.perEntry === true){
								state.setId = obj.id+'.'+fieldName;
							}
//console.log('state',state);							
							modal.body.empty();
							//modal.body.makeNode('test','div',{text:'testing'+obj.id});
							modal.body.patch();
							modal.show();
							
							sb.notify({
								type:'show-dashboard',
								data:{
									dom: modal.body,
									state: state,
									draw: function(data) {
																				
										data.after(modal.body);
										
										data.dom.patch();
										
									}
								}
							});
	
						}.bind({
							obj:obj,
							state:state,
							fieldOptions:fieldOptions
						})
						, onClose: function (data) {
							
							
							
						}
					}
				}, sb.moduleId);		
				
			}else{
			
				if(fieldOptions.inheritsTags == true){
					state.tool.settings.tags = obj.tagged_with;
					state.tool.settings.tags.push(obj.id);
					state.parent.tagged_with = state.tool.settings.tags;
					state.setTags = state.parent.tagged_with;
				}

				sb.notify({
					type:'show-dashboard',
					data:{
						dom: ui,
						state: state,
						draw: function(data) {
		
							data.after(ui);
		
							ui.patch();
							
						}
					}
				});
				
			}
			
		}
		
	}
			
	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'dashboard',
					title: 'Dashboard',
					availableToEntities: true,
					icon: 'tachometer alternate',
					options: {
						inheritsTags: {
							name: 	'Inherits Tags? (passes on tags to records created here)'
							, type: 'bool'
						},
						perEntry: {
							name: 	'New dashboard per entry?'
							, type: 'bool'
						}
					},
					view: View,
					edit: View,
					propertyType: 'string',
					detail: function (key, obj, options) {
						
						if (options.inCollection) {
							return obj[key];							
						}
						
					},
					useFieldListener: true,
					focus: function (fieldName, ui, obj, options) {}
				}
			});

		}

	}

});
