var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('type-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	function View (fieldName, ui, obj, options) {

		function draw_objType (ui, object, options) {

			var color = '';
			var divElement = 'div';
			var currentVal = 0;
			var currentName = '<i class="text-muted">Not set</i>';
			var disabledClass = '';
			
			if (!options.edit) {
				disabledClass = 'disabled';
			}
			
			if (obj.type && obj.type.id) {
				currentVal = obj.type.id;
				currentName = obj.type.name;
			}
			var typeOptions = [{
				value: 		currentVal
				, name: 		currentName
				, selected: 	true
			}, {value:'name', name:'goes here', selected:false}];
			
			// For cases where bp names are set
			if (typeof object === 'string') {
				object = _.findWhere(appConfig.Types, {bp_name: object.substring(1)});
			}

			if (!_.isEmpty(object) && object.hasOwnProperty('id')) {
				
				if (!_.isEmpty(object.color)) {
					color = ' '+ object.color;
				}
				
				if(options.edit)
					divElement = 'a';
				
				ui.makeNode('btn', 'div', {
					css: 'ui floating dropdown search fluid basic button ' + disabledClass,
					style: 'text-align:center;' ,
					text:'<span style="white-space: nowrap;">'+ object.name +'</span>'
				});	
				
			} else {
				
				ui.makeNode('btn', 'div', {
					css: 'ui floating dropdown search fluid basic button ' + disabledClass,
					style: 'text-align:center;',
					text:'<span style="white-space: nowrap;"><i class="text-muted">Not set</i></span>'
				});	
				
			}
			
		}
		
// 		if (!_.isEmpty(obj[fieldName])) {
			
// 			if (typeof obj[fieldName] === 'object' && obj.id) {

				draw_objType(ui, obj[fieldName], options);
				
				ui.btn.listeners.push(
					function (selector) {
		
						$(selector).on('click', function(){
							
							if (options.edit) {
		
								Edit(fieldName, ui, obj, options);					
							
							}
							
						});
						
					}
				);				
				
// 			}
			
// 		} 


		
	}
	
	function Edit (fieldName, ui, obj, options) {

		if (typeof options.onEditStart === 'function') {
			options.onEditStart();
		}

		var where = '';
		var currentSelection = 	0;
		var typeOptions = [];
		var objType = '';

		if (!_.isEmpty(obj[fieldName])) {
			
			objType = obj[fieldName].object_bp_type
			currentSelection = obj[fieldName].id;
			
			typeOptions.push({
				value: 			currentVal
				, name: 		obj[fieldName].name
				, selected: 	true
			});
			
		}
		
		if (options.objectType) {
			objType = options.objectType;
		} else if (
			_.isEmpty(objType)
			&& obj
		) {
			
			switch (obj.object_bp_type) {
				
				case 'contacts':
					objType = 'contact_types';
					break;
					
				case 'groups':
					switch (obj.group_type) {
						
						case 'Project':
						
							if(!options.hasOwnProperty('useCategory')) {
								
								objType = 'project_types';
								
							} else {
								
								objType = 'categories';
								
							}
							
							break;
							
						case 'Task':
							objType = 'task_types';
							break;
							
						case 'Schedule':
								
							objType = 'categories';
						
							break;
						
					}
					break;
				
			}
			
		}

		var currentVal = 0;

		if (obj.type && obj.type.id) {
			currentVal = obj.type.id;
		}
		
		// Custom entities should select from other subsets
		if (obj.object_bp_type.substr(0, 1) === '#') {
			
			var setName = obj.object_bp_type.substr(1);
			if (setName.includes('.')) {
				setName = setName.split('.')[0];
			}
			
			objType = 'entity_type';
			if (_.findWhere(appConfig.Types, {bp_name: setName})) {
				
				where = {
					where: {
						_class: _.findWhere(appConfig.Types, {bp_name: setName}).id
					}
					, name: true
				};
				
			}
			
		}
		
		ui.empty();

		var fluid = options.fluid == false ? '' : 'fluid';
		var searchNode = ui.makeNode('add', 'div', {
			css: 'ui search selection transparent ' + fluid + ' dropdown',
			style: 'border:1px solid #ebebeb;',
			text:
				'<input type="hidden" name="object">'+
				'<i class="dropdown icon"></i>'+
				'<input type="text" class="transparent search search-input" tabindex="0" style="padding:7px 15px !important; height:100%;">'+
				'<div class="default text">Select Option</div>'+
				'<div class="menu transition hidden" tabindex="-1">option text here</div>',
			listener:{
				type: 'dropdown'
				, values: typeOptions
				, saveRemoteData: false
				, minCharacters: 0
				, forceSelection: false
				, apiSettings: {
					cache: false
					, url: databaseConnection.obj.getSearchPath(
						objType
						, where
					)
					, onResponse: function (raw) {

						var results = [];
						_.each(raw.results, function (object_type) {
							
							var selection = $(searchNode.selector).dropdown('get value');

							if (!_.isEmpty(object_type.name)) {

								results.push({
									value: parseInt(object_type.id)
									, name: object_type.name
								});
											
							}
							
						});
						
						// Custom entities should select from other subsets
						if (obj.object_bp_type.substr(0, 1) === '#') {
							
							var baseType = _.findWhere(
								appConfig.Types
								, {
									bp_name: obj.object_bp_type.substr(1).split('.')[0]
								}
							);
							results.unshift({
								value: 	baseType.id
								, name: baseType.name
							});
							
						} else {
							
							results.unshift({
								value: 0
								, name: '<strong>None</strong>'
							});
							
						}

						return {
							results: results
						};
						
					}
					, mockResponseAsync: function(where, settings, callback){
						
						function GetCookieValue(name) {
						    var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
						    return found.length > 0 ? found[0].split("=")[1] : null;
						}
						
						$.ajax({
							type: 'post',
							url: settings.url,
							xhrFields: {
								withCredentials: true
							},
							crossDomain: true,
							data: {
								json: JSON.stringify({
									selection: 	{
										where
									}
								})
							},
							success: function (response) {
				
								callback(response);
								
							},
							error: function (jqXHR, status, error) {
				
								
								
							},
							headers: {
								'bento-token': GetCookieValue('token')
							}
						});	
						
					}.bind({}, where)
				}
				, onChange: function (val, text) {

					function getUpdates(selection, onComplete) {

						var updates = {
							id: 			obj.id
							, [fieldName]: 	parseInt(selection)
						};

						// If a project/task, also set the state value to the 
						// entry point state of the new workflow it is being
						// set to.
						
						if(!_.isEmpty(obj)){
						
							if ( obj.hasOwnProperty('object_bp_type') ) {
								
								if (
									(obj.object_bp_type === 'groups' && options.useCategory !== true)
									|| obj.object_bp_type === 'contacts'
								) {
									
									sb.data.db.obj.getById(
										''
										, selection
										, function (wf) {
											
											updates.state = _.findWhere(
												wf.states
												, {
													isEntryPoint: 1
												}
											).uid;
											
											onComplete(updates, wf);
											
										}
									);
									
								} else {
		
									onComplete(updates);
									
								}
								
							}
						
						}	
						
					}

					if (parseInt(val) !== currentSelection) {
						
// 						if (typeof options.onEditEnd === 'function')
// 							options.onEditEnd();
						
						var selection = parseInt(val);
	
						searchNode.loading();
						
						// Custom entities have an action endpoint
						if (obj.object_bp_type.substr(0, 1) === '#') {
							
							sb.data.db.obj.runSteps(
								{
									changeType: {
										newType: val
									}
								}
								, obj.id
								, function (response) {
									
									obj.object_bp_type = response.object_bp_type;

									var newSet = _.findWhere(appConfig.Types, {id: parseInt(val)});
									if (newSet) {
										obj.object_bp_type = '#'+ newSet.bp_name;
									}

									ui.empty();
									if (typeof options.onEditStart === 'function') {
										options.onEditEnd();
									}
										
									$(searchNode.selector).dropdown('destroy');
									View(fieldName, ui, obj, options);
									ui.patch();
									
									if (typeof options.onUpdate === 'function') {
										
										options.onUpdate(fieldName, obj[fieldName]);
										
									}
									
								}
							);
							
						} else {
						
							getUpdates(selection, function (updates, newType) {
								
								if (options.hasOwnProperty('parseUpdates')) {
									updates = options.parseUpdates(updates);
								}
	
								sb.data.db.obj.update(
									obj.object_bp_type
									, updates 
									, function (updates) {
			
										obj[fieldName] = updates[fieldName];
										
										if(!_.isEmpty(obj)){
										
											if ( obj.hasOwnProperty('object_bp_type') ) {
											
												if (
													(obj.object_bp_type === 'groups' && options.useCategory !== true)
													|| obj.object_bp_type === 'contacts'
												) {
													
													function getTypeObj (type, callback) {
														
														if (typeof type === 'number') {
															
															sb.data.db.obj.getById(
																''
																, type
																, function (type) {
																	
																	callback(type);
																	
																}
																, {
																	[fieldName]: {
																		id: 		true
																		, name:   	true
																		, color: 	true
																		, states: 	true
																	}
																}
															);
															
														} else if (typeof type === 'object' && type.id) {
															
															callback(type);
															
														}
														
													}
													
													getTypeObj(
														updates.type
														, function (type) {
															
															updates.type = type;
															
															obj.state = updates.state;
															obj[fieldName] = newType;
															
															sb.notify({
																type: 'field-updated'
																, data: {
																	obj: 		updates
																	, type: 	'state'
																	, property: 'state'
																}
															});
															
															ui.empty();
															
															if (typeof options.onEditStart === 'function')
																options.onEditEnd();
																
															$(searchNode.selector).dropdown('destroy');
															
															View(fieldName, ui, obj, options);
															
															ui.patch();
															
															if (typeof options.onUpdate === 'function') {
																
																options.onUpdate(obj);
																
															}
															
														}
													);
													
												} else {
													
													ui.empty();
													
													if (typeof options.onEditStart === 'function')
														options.onEditEnd();
														
													$(searchNode.selector).dropdown('destroy');
													
													View(fieldName, ui, obj, options);
													
													ui.patch();
													
													if (typeof options.onUpdate === 'function') {
														
														options.onUpdate(obj);
														
													}
													
												}
												
											}	
											
										}	
										
									}
									, {
										[fieldName]: {
											id: 		true
											, name:   	true
											, color: 	true
											, states: 	true
										}
									}
								);
								
							});
							
						}
												
					}
					
				}, 
				onHide: function () {
					
					$(searchNode.selector).dropdown('destroy');
					
					ui.empty();
					
					if (typeof options.onEditStart === 'function')
						options.onEditEnd();
					
					View(fieldName, ui, obj, options);
					
					ui.patch();
					
				}
			}
		});
		
		searchNode.listeners.push(function (selector) {

			$(selector).find('.search-input').focus();
						
		});
		
		ui.patch();
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'type'
					, view: View
				}
			});
			
		}
		
	}
	
});