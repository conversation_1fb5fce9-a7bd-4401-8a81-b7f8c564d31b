var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('table-view', function(sb){

	function View(fieldName, ui, obj, options) {

		options.views = {
			table: true
		}

		sb.notify ({
			type: 'view-field',
			data: {
				type: 'collection',
				property: fieldName,
				obj: obj,
				options: options,
				ui: ui
			}
		});

	}
	
	function table(fieldName, ui, obj, options) {

		var boxOptions = {};
		var dateSelectSet = false;
		var dateRangeSet = false;
		var showTableSelect = 'yes';
		var nameLabel = 'Name';
		var parentLabel = 'Parent';
		var groupBySelect = false;
		var selectFieldSelect = '';
		var selectFieldValueSelect = 0;
		var workflowLimitSelect = false;
		var workflowLimitStateSelect = false;
		var doneIsSelect = 'either';
		var cachedList = [];
		var visibleColumns = [];
		var summedColumns = [];

		var thStyleForNoUI = 'text-align:left; padding:.92857143em .78571429em; bottom-bottom:1px solid rgba(34,36,38,.1); border-bottom:1px solid rgba(34,36,38,.1); border-left:1px solid rgba(34,36,38,.1);';
		var tdStyleForNoUI = 'vertical-align:top; padding:.78571429em .78571429em; border-bottom:1px solid rgba(0,0,0,.1);';

		var bpName = options.objectType;
		var bpObj = options.blueprint;

        var portalInstance = false;

        if( appConfig.is_portal || appConfig.isFoundationGroupPortal)
            portalInstance = true;

		if (!bpName) {

			if (ui) {

				ui.makeNode('msg', 'div', {
					css: 'ui compact warning message',
					text: 'Please select a Set to point to.'
				});				

			}

			return;
			

		}

		if (!bpObj) {

			sb.data.db.obj.getBlueprint(bpName, function(blueprint) {

				if (!blueprint) {

					if (ui) {

						ui.makeNode('msg', 'div', {
							css: 'ui compact warning message',
							text: 'There was an error getting the data for this field.'
						});
						
						ui.patch();
		
					}
		
					return;

				}

				options.blueprint = blueprint;
				bpObj = options.blueprint;

				table(fieldName, ui, obj, options);
	
			}, false, true);

			return;

		}

		_.each(options.includeInSelect, function(includedField, includedKey) {

			_.each(bpObj.blueprint, function(blueprintField, blueprintKey) {

				if (!blueprintField.is_archived) {

					if ((includedField.id === blueprintKey)) {

						if (includedField.active) {
							
							visibleColumns.push(blueprintKey);

							if (includedField.hasOwnProperty('metrics')) {
								if (includedField.metrics) {
									if (includedField.metrics.hasOwnProperty('sum')) {
										if (includedField.metrics.sum) {

											summedColumns.push(blueprintKey);

										}
									}
								}
							}

						}

					}

				}

			});

		});

		function getWorkflows(blueprint, onComplete) {

			var workflowIds = [];
			
			_.each(blueprint, function (property, key) {
	
				if (
					property.fieldType === 'state'
					&& parseInt(property.workflow)
					&& !property.is_archived
				) {
	
					workflowIds.push(parseInt(property.workflow));
	
				}
	
			});
	
			sb.data.db.obj.getById(
				'entity_workflow'
				, workflowIds
				, function (workflows) {
	
					_.each(blueprint, function (property, key) {
	
						if (
							property.fieldType === 'state'
							&& !property.is_archived
						) {
	
							var wf = _.findWhere(workflows, {id: parseInt(property.workflow)});
	
							if (!_.isEmpty(wf)) {
	
								property.workflow = wf;
	
							}
	
						}
	
					});
	
					onComplete(blueprint);
	
				}
			);
	
		}

		function getData(options, callback) {

			var recordCount = 0;
			var recordData = [];

			function pageThroughData(options, callback) {
				
				var sortCol = 'dateCreated';
				var sortDir = 'asc';
				var skipCache = false;

				if (options) {

					if (options.hasOwnProperty('sortCol')) {
						if (options.sortCol) {
							sortCol = options.sortCol;
						}
					}

					if (options.hasOwnProperty('sortDir')) {
						if (options.sortDir) {
							sortDir = options.sortDir;
						}
					}

					if (options.hasOwnProperty('skipCache')) {
						if (options.skipCache) {
							skipCache = options.skipCache;
						}
					}

				}

				var filterValue = options.filter;
				if(!options.filter && options.context){
					filterValue = options.context.id;
				}
					
				// build tableRequest.query
				var queryRequest = [];
				var tableRequest = {
					responseName: 'table',
					table: sb.dom.blueprintNameFormatted(bpObj.bp_name),
					query: {
						paged: {
							count: true,
							paged: true,
							page: options.page,
							pageLength: options.pageLength,
							sortCol: sortCol,
							sortDir: sortDir
						},
						is_template: {
							type: 'not_equal',
							value: 1
						},
						_data_filter: filterValue,
						_data_filter_context: obj.id
					},
					childObjs: {}
				}
				
				if(_.isArray(dateSelectSet)){
					dateSelectSet = dateSelectSet[0];
				}
				
				switch(dateRangeSet) {
					
					case 'today':
					
						startDateSet = moment().local().startOf('day');
						endDateSet = moment().local().endOf('day');
					
						break;
						
					case 'yesterday':
				
						startDateSet = moment().local().subtract(1,'day').startOf('day');
						endDateSet = moment().local().subtract(1,'day').endOf('day');
					
						break;
						
					case 'this_week':
				
						startDateSet = moment().local().startOf('week');
						endDateSet = moment().local().endOf('week');
					
						break;
						
					case 'last_week':
				
						startDateSet = moment().local().subtract(1,'week').startOf('week');
						endDateSet = moment().local().subtract(1,'week').endOf('week');
					
						break;
					
					case 'this_month':
				
						startDateSet = moment().local().startOf('month');
						endDateSet = moment().local().endOf('month');
					
						break;
						
					case 'last_month':
				
						startDateSet = moment().local().subtract(1,'month').startOf('month');
						endDateSet = moment().local().subtract(1,'month').endOf('month');
					
						break;
						
					case 'this_year':
			
						startDateSet = moment().local().startOf('year');
						endDateSet = moment().local().endOf('year');
					
						break;
						
					case 'last_year':
				
						startDateSet = moment().local().subtract(1,'year').startOf('year');
						endDateSet = moment().local().subtract(1,'year').endOf('year');
					
						break;
						
					case 'next_7_days':
			
						if(boxOptions.overdueSelect == 'yes'){
							startDateSet = moment().local().subtract(5,'year').startOf('year');
						}else{
							startDateSet = moment().local().startOf('day');
						}
						
						endDateSet = moment().local().add(7,'day').endOf('day');
					
						break;
						
					case 'next_14_days':
		
						if(boxOptions.overdueSelect == 'yes'){
							startDateSet = moment().local().subtract(5,'year').startOf('year');
						}else{
							startDateSet = moment().local().startOf('day');
						}
						
						endDateSet = moment().local().add(14,'day').endOf('day');
					
						break;
						
					case 'next_30_days':
		
						if(boxOptions.overdueSelect == 'yes'){
							startDateSet = moment().local().subtract(5,'year').startOf('year');
						}else{
							startDateSet = moment().local().startOf('day');
						}
						
						endDateSet = moment().local().add(30,'day').endOf('day');
					
						break;
						
				}
				
				// tableRequest.query[dateSelectSet] = {
				// 	type:'between',
				// 	start:moment(startDateSet).unix(),
				// 	end:moment(endDateSet).unix()
				// };
								
				tableRequest.childObjs.name = true;
				tableRequest.childObjs.status = true;
				tableRequest.childObjs.parent = true;
				
				if(selectFieldSelect && selectFieldValueSelect){
					// tableRequest.query[selectFieldSelect] = {
					// 	type:'or',
					// 	values:_.map(selectFieldValueSelect.split(','),function(state){
					// 		return +state;
					// 	},[])
					// };
					tableRequest.query[selectFieldSelect] = +selectFieldValueSelect;
				}
				
				if(groupBySelect){
					tableRequest.childObjs[groupBySelect] = true;
				}
				
				_.each(visibleColumns, function(col) {
					tableRequest.childObjs[col] = true;
				});
				
				if(doneIsSelect != 'either'){
				
					if(doneIsSelect == 'not_started'){
						tableRequest.query.status = {
							type:'not_equal',
							value:'done'
						};
					}else{
						tableRequest.query.status = doneIsSelect;
					}
					
				}

				if(workflowLimitSelect && workflowLimitStateSelect){
					
					if(isNaN(+workflowLimitStateSelect)){
						
						tableRequest.query[workflowLimitSelect] = {
							type:'or',
							values:_.map(workflowLimitStateSelect.split(','),function(state){
								return +state;
							},[])
						};
						
					}else{
						
						tableRequest.query[workflowLimitSelect] = +workflowLimitStateSelect;
					
					}
					
				}

				if (
					typeof options.filter === 'number'
					&& options.filter > 0
				) {

					tableRequest.query._data_filter = options.filter;
					tableRequest.query._data_filter_context = obj.id

				} else {

					tableRequest.query.parent = obj.id;

				}

				if (bpObj.bp_name === 'time_entries') {
					tableRequest.api = 'getObjectsWhereGrouped';
					tableRequest.childObjs['shift'] = true;
				}
				
				queryRequest.push(tableRequest);

				_.each(summedColumns, function(sumCol) {

					var sumRequest = {
						responseName: sumCol + '_sum',
						table: sb.dom.blueprintNameFormatted(bpObj.bp_name),
						field: sumCol,
						query: {
							paged: {
								count: true,
								paged: true,
								page: options.page,
								pageLength: options.pageLength,
								sortCol: sortCol,
								sortDir: sortDir
							},
							is_template: {
								type: 'not_equal',
								value: 1
							},
							_data_filter: options.filter,
							_data_filter_context: obj.id
						},
						api: 'simpleSum'
					}

					if (bpObj.bp_name === 'time_entries') {
						delete sumRequest.query.paged;
					}

					queryRequest.push(sumRequest);

				});

				sb.data.db.service('DataRepository', 'get', queryRequest, function(response) {

					recordCount = response.table.data ? recordCount + response.table.data.length : recordCount;
					recordData = response.table.data ? _.union(recordData, response.table.data) : recordData;

					response.table.data = recordData;
					
					if (options.pageThrough) {

						if (response.table.recordsTotal > recordCount) {

							options.page++;
							pageThroughData(options, callback);

						} else {

							if (typeof callback === 'function') {
								callback(response);
							}

						}

					} else {

						if (typeof callback === 'function') {
							callback(response);
						}

					}

				});

			}

			pageThroughData(options, function(response) {

				if (typeof callback === 'function') {
					callback(response);
				}

			});
				
		}

		function buildTable(ui, callback, cachedList, options) {

			var ret = '';

			var tableId = 'table-' + obj.object_bp_type.substr(1) + '-' + fieldName;

			var sortCol = 		'dateCreated';
			var sortDir = 		'asc';
			var page = 			0;
			var pageLength = 	10;
			var skipCache = 	false;
			var hideLoader = 	false;
			var filter = 		false;

			if (options) {

				if (options.hasOwnProperty('sortCol')) {
					if (options.sortCol) {
						sortCol = options.sortCol;
					}
				}

				if (options.hasOwnProperty('sortDir')) {
					if (options.sortDir) {
						sortDir = options.sortDir;
					}
				}

				if (options.hasOwnProperty('skipCache')) {
					if (options.skipCache) {
						skipCache = options.skipCache;
					}
				}

				if (options.hasOwnProperty('filter')) {
					if (options.filter) {
						filter = options.filter;
					}
				}

				if (options.hasOwnProperty('hideLoader')) {
					if (options.hideLoader) {
						hideLoader = options.hideLoader;
					}
				}

				if (options.hasOwnProperty('page')) {
					if (options.page) {
						page = options.page;
					}
				}

				if (options.hasOwnProperty('pageLength')) {
					if (options.pageLength) {
						pageLength = options.pageLength;
					}
				}

			}

			//render for add new row
			function buildAddRowButton(ui, groupKey, visibleColumns, obj, saveApiAction, onSuccess, onAsyncSuccess, allRecords){
				ui.makeNode(
					'addField'
					, 'div'
					, {
						text: '<i class="green plus icon"></i> Add a record'
						, css: 'ui button field-manager-plus'
						, style: 'font-weight:300;color:rgb(175,175,175); margin:1rem 2px; font-style:italic;'
					}
				).listeners.push(
					function (selector) {

						$(selector).on('click', function () {


							ui.tableWrapper['table'+groupKey].body.makeNode('newRecord','div',{tag:'tr'});

							ui.tableWrapper['table'+groupKey].body['newRecord'].makeNode('col-options', 'div', {
								tag: 'td',
								text: '',
								css: ''
							});

							_.each(visibleColumns, function(col){

								var css = (col == 'name' || col == 'shift') ? 'ui form' : '';

								ui.tableWrapper['table'+groupKey].body['newRecord'].makeNode('col'+col, 'div', {
									tag: 'td',
									text: '',
									css: css
								});

								if(col == 'name'){
									ui.tableWrapper['table'+groupKey].body['newRecord']['col'+col]
										.makeNode('input','div',{tag:'input', id:'entity-add-record-'+groupKey, type:'text', placeholder:'Add your record'});

									var _inputKey = "#entity-add-record-" + groupKey;

									var addRecodAction = function (){
										var tagged_with = [parseInt(sb.data.cookie.userId), obj.id].concat(obj.tagged_with);
										var request = {name: $(_inputKey).val(), parent: obj.id, tagged_with: tagged_with};
										var objectType = sb.dom.blueprintNameFormatted(bpObj.bp_name);
										saveApiAction(objectType, request, obj, onSuccess, onAsyncSuccess, allRecords);
									}

									ui.tableWrapper['table'+groupKey].body['newRecord']['col'+col].input.listeners.push(function (_selector) {
										$(_selector).focus();
										$(_selector).on('keypress',function(e) {
											if(e.which == 13) {
												addRecodAction();
											}
										});
									});

									ui.tableWrapper.listeners.push(function (_selector) {
										$(_selector).on('click', function (e) {
											if ($(e.target).closest(_inputKey).length === 0) {
												if($(_inputKey).val() != '') {
													addRecodAction();
												}
												else {
													onSuccess(allRecords);
												}
											}
										});
									});
								}

							});

							ui.patch();

						});

					}
				);
			}

			function createRecord(_objectType, request, _obj, onSuccess, onAsyncSuccess, allRecords){

						if(_.isEmpty(request.name)){
							return false;
						}

						var objectType = sb.dom.blueprintNameFormatted(bpObj.bp_name);

						sb.data.db.service(
							'EntityManagementService'
							, 'createEntityWithName', {
								objectType: 		objectType
								, queryObj: 		request
							}, function(response) {
								onAsyncSuccess();
							}
						);

						var tableRecordAdded = {
							date_created: moment().format('M/D/YYYY h:mm a'),
							id: 'tmp-' + new Date().getTime(),
							instance: appConfig.instance,
							is_deleted: false,
							name: request.name,
							notify: [parseInt(sb.data.cookie.userId)],
							object_bp_type: _objectType,
							parent: _obj.id,
							read: [],
							shared_with: [],
							sort_index: false,
							status: "",
							tagged_with: [parseInt(sb.data.cookie.userId), _obj.id].concat(_obj.tagged_with),
							write: [parseInt(sb.data.cookie.userId)],
						};
						onSuccess(tableRecordAdded, allRecords);
			}

			function refreshTable(options) {

				if (options) {

					if (options.hasOwnProperty('sortCol')) {
						if (options.sortCol) {
							sortCol = options.sortCol;
						}
					}
	
					if (options.hasOwnProperty('sortDir')) {
						if (options.sortDir) {
							sortDir = options.sortDir;
						}
					}

					if (options.hasOwnProperty('filter')) {
						if (options.filter) {
							filter = options.filter;
						}
					}
	
					if (options.hasOwnProperty('skipCache')) {
						if (options.skipCache) {
							skipCache = options.skipCache;
						}
					}

					if (options.hasOwnProperty('page')) {
						if (options.page) {
							page = options.page;
						}
					}
	
					if (options.hasOwnProperty('pageLength')) {
						if (options.pageLength) {
							pageLength = options.pageLength;
						}
					}
	
				}

				buildTable(ui, callback, cachedList, {
					sortCol: 	sortCol,
					sortDir: 	sortDir,
					skipCache: 	skipCache,
					page: 		page,
					pageLength:	pageLength,
					filter: 	filter,
					hideLoader: true
				});

			}

			if (showTableSelect == 'yes') {

				if (!skipCache && !hideLoader) {
				
					if (ui) {

						ui.empty();
						
						ui.makeNode('loading','div',{text:'<i class="notched circle loading icon"></i> Loading Table', css:'ui header center aligned'});
						
						ui.patch();

					}

				}

				getWorkflows(bpObj.blueprint, function(fullBlueprint) {
				
					bpObj.blueprint = fullBlueprint;
					var renderTable = function(response) {

						var onAsyncSuccess = function() {
							refreshTable({skipCache: true, sortCol: 'dateCreated', sortDir: 'asc'});
						}//refresh table
						var onSuccess = function(_record = false, _allRecords){
							if(_record) {
								_allRecords.push(_record);
							}
							renderTable(response);
						};

						if (!response.table.data || _.isEmpty(response.table.data)) {
							if (typeof options.callback === 'function') {
								options.callback('');
							}
						}

						if (typeof options.pagingCallback === 'function') {
							options.pagingCallback(response.table);
						}

						var data = response.table.data;
						var recordsDisplayed = response.table.data ? response.table.data.length : 0;
						var recordsTotal = response.table.recordsTotal ? response.table.recordsTotal : 0;
						var groupsTotal = response.table.groupsTotal ? response.table.groupsTotal : 0;
						var bpNameSingularPlural = '';
						var bpNameSingularPluralGrouped = '';
						var helperText = '';

						if (bpObj.bp_name === 'time_entries') {

							bpNameSingularPlural = recordsTotal == 1 ? 'time entry' : 'time entries';
							bpNameSingularPluralGrouped = groupsTotal == 1 ? 'time entry' : 'time entries';

							helperText = '(Displaying ' + groupsTotal + ' grouped ' + bpNameSingularPluralGrouped + ' made up of ' + recordsTotal + ' ' + bpNameSingularPlural + '.)';

						} else {

							helperText = '(Displaying ' + recordsDisplayed + ' of ' + recordsTotal + ' record(s). Only 100 records can be displayed.)';

						}

						if (ui) {
							ui.empty();
						}

						if(ui && _.isEmpty(response.table.data)) {

							var groupKey = 'newrecord';

							ui.makeNode('tableWrapper', 'div', {
								css: 'responsive-horizontal'
							});

							ui.tableWrapper.makeNode('table' + groupKey, 'div', {
								tag: 'table',
								id: tableId,
								css: 'ui sortable very basic celled table table-field'
							});

							ui.tableWrapper['table' + groupKey].makeNode('head','div',{tag:'thead'});
							ui.tableWrapper['table' + groupKey].head.makeNode('row','div',{tag:'tr'});

							_.each(visibleColumns, function (col) {
								var _defaultSortColumnString = 'default-sort';
								var sortArrow = '<i class="grey sort amount down icon"></i>';
								ui.tableWrapper['table'+groupKey].head.row.makeNode('actions','div',{tag:'th', css:_defaultSortColumnString, text:''});
								ui.tableWrapper['table' + groupKey].head.row.makeNode('col'+col,'div',{tag:'th', css:_defaultSortColumnString, text: sortArrow + ' ' + bpObj.blueprint[col].name});
							});

							ui.tableWrapper['table'+groupKey].makeNode('body','div',{tag:'tbody'});

							ui.tableWrapper['table' + groupKey].body.makeNode('row','div',{tag:'tr'});

							_.each(visibleColumns, function (col) {
								var _defaultSortColumnString = 'default-sort';
								ui.tableWrapper['table'+groupKey].body.row.makeNode('actions','div',{tag:'th', css:_defaultSortColumnString, text:''});
								ui.tableWrapper['table' + groupKey].body.row.makeNode('col'+col,'div',{tag:'td', css:_defaultSortColumnString, text: ''});
							});

							buildAddRowButton(ui, groupKey, visibleColumns, obj, createRecord, onSuccess, onAsyncSuccess, response.table.data);

							ui.patch();

							return;
						}

						if (data === false) {

							if (ui) {
								ui.makeNode('setupWarning','div',{text:'<br />Please choose a date range.<br /><br />',css:'ui header center aligned'});
							}

						} else {

							var groupedObjs = {};
							var groupingData = _.map(data, function(obj){

								if(obj[groupBySelect]){

									if(obj[groupBySelect].id){
										groupedObjs[obj[groupBySelect].id] = obj[groupBySelect];
										obj[groupBySelect] = obj[groupBySelect].id;
									}else{
										groupedObjs[obj[groupBySelect]] = obj[groupBySelect];
										obj[groupBySelect] = obj[groupBySelect];
									}


								}

								return obj;
							}, []);

							groupedData = _.groupBy(data, groupBySelect);

							if(groupBySelect == 'status'){

								if(groupedData['']){

									if(!groupedData['not_started']){
										groupedData['not_started'] = [];
									}

									groupedData['not_started'] = groupedData[''];
									delete groupedData[''];

								}

							}

							_.each(groupedData, function(group, groupKey) {

								if (ui) {

									ui.makeNode('tableWrapper', 'div', {
										css: 'responsive-horizontal'
									});

									// ui.tableWrapper.makeNode('note', 'div', {
									// 	css: 'text-muted',
									// 	style: 'margin-top:5px; font-style:italic;',
									// 	text: helperText
									// });

									ui.tableWrapper.makeNode('table' + groupKey, 'div', {
										tag: 'table',
										id: tableId,
										css: 'ui sortable very basic celled table table-field'
									});

									ui.tableWrapper['table'+groupKey].makeNode('head','div',{tag:'thead'});
									ui.tableWrapper['table'+groupKey].head.makeNode('row','div',{tag:'tr'});

								} else {

									ret += '<table class="ui very basic celled sortable table" style="width:100%; border-collapse:collapse; border-style:hidden;">';
										ret += '<thead>';
											ret += '<tr class="borderTable">';

								}

								var defaultSortColumnString = '';
								if ('name' == sortCol) {
									defaultSortColumnString = 'default-sort';
								}

								if (bpObj.bp_name !== 'time_entries') {
									if (visibleColumns.indexOf('name') != -1) {
										if (ui) {
											if (appConfig.instance == bpObj.instance) {
												ui.tableWrapper['table'+groupKey].head.row.makeNode('actions','div',{tag:'th', css:defaultSortColumnString, text:''});
											}
										}
									} else {
										if (ui) {
											if (appConfig.instance == bpObj.instance) {
												ui.tableWrapper['table'+groupKey].head.row.makeNode('actions','div',{tag:'th', css:'', text:''});
											}
										}
									}
								}

								defaultSortColumnString = '';
								if(visibleColumns.indexOf('parent') != -1 && groupBySelect != 'parent'){
									if ('parent' == sortCol) {
										defaultSortColumnString = 'default-sort';
									}
									if (ui) {
										ui.tableWrapper['table'+groupKey].head.row.makeNode('parent','div',{tag:'th', css:defaultSortColumnString, text:parentLabel});
									} else {
										ret += '<th class="borderTable" style="' + thStyleForNoUI + '">' + parentLabel + '</th>'
									}
								}

								_.each(visibleColumns, function(col) {

									defaultSortColumnString = '';
									var width = "";
									if(col == 'name'){
										width = "width:230px;display:inline-block;";
									}

									var sortArrow = '<i class="grey sort amount down icon"></i>';

									if (sortCol === col) {
										if (sortDir === 'asc') {
											sortArrow = '<i class="teal up arrow icon"></i>';
										} else if (sortDir === 'desc') {
											sortArrow = '<i class="teal down arrow icon"></i>';
										}
									}

									var colHead;
									if (col == 'parent') {
										if (ui) {
											colHead = ui.tableWrapper['table'+groupKey].head.row.makeNode('col'+col,'div',{tag:'th', css:defaultSortColumnString, text: sortArrow + ' ' + parentLabel, style: width});
										} else {
											ret += '<th class="borderTable" style="' + thStyleForNoUI + '">' + parentLabel + '</th>';
										}
									} else {
										if (ui) {
											colHead = ui.tableWrapper['table'+groupKey].head.row.makeNode('col'+col,'div',{tag:'th', css:defaultSortColumnString, text: sortArrow + ' ' + bpObj.blueprint[col].name, style: width});
										} else {
											ret += '<th class="borderTable" style="' + thStyleForNoUI + '">' + bpObj.blueprint[col].name + '</th>'
										}
									}

									if (ui) {

										$(document).ready(function() {

											$(colHead.selector).off().on('click', function() {

												refreshTable({
													sortCol: col,
													sortDir: (sortDir === 'asc') ? 'desc' : 'asc',
													skipCache: true
												});

											});

										});

									}

								});

								if (!ui) {

										ret += '</tr>';
									ret += '</thead>';

								}

								if (ui) {

									ui.tableWrapper['table'+groupKey].makeNode('foot','div',{tag:'tfoot'});
									ui.tableWrapper['table'+groupKey].foot.makeNode('row','div',{tag:'tr'});
									if (bpObj.bp_name !== 'time_entries') {
										if (appConfig.instance == bpObj.instance) {
											ui.tableWrapper['table'+groupKey].foot.row.makeNode('actions','div',{tag:'th'});
										}
									}

								} else {

									if (!_.isEmpty(summedColumns)) {
										ret += '<tfoot>';
											ret += '<tr>';
									}

								}

								if (visibleColumns.indexOf('parent') != -1 && groupBySelect != 'parent') {
									if (ui) {
										ui.tableWrapper['table'+groupKey].foot.row.makeNode('parent','div',{tag:'th'});
									}
								}

								_.each(visibleColumns, function(col) {

									if (col != 'parent') {

										if (_.contains(summedColumns, col)) {

											if (col == 'duration') {

												if (ui) {

													ui.tableWrapper['table'+groupKey].foot.row.makeNode('col'+col,'div',{
														tag:'th',
														text: sb.dom.durationDisplay(response[col + '_sum'])
													});

												} else {

													ret += '<th class="borderTable" style="' + thStyleForNoUI + '">' + sb.dom.durationDisplay(response[col + '_sum']) + '</th>';

												}

											} else {

												if (ui) {

													sb.notify ({
														type: 'view-field',
														data: {
															type: bpObj.blueprint[col].fieldType,
															property: col + '_sum',
															obj: response,
															options: {
																edit: false,
																editing: false
															},
															ui: ui.tableWrapper['table'+groupKey].foot.row.makeNode('col'+col,'div',{tag:'th'})
														}
													});

												} else {

													sb.notify ({
														type: 'view-field',
														data: {
															type: bpObj.blueprint[col].fieldType,
															property: col + '_sum',
															obj: response,
															options: {
																edit: false,
																editing: false,
																callback: function(response) {
																	ret += '<th class="borderTable" style="' + thStyleForNoUI + '">' + response + '</th>';
																}
															},
														}
													});

												}

											}

										} else {

											if (ui) {

												ui.tableWrapper['table'+groupKey].foot.row.makeNode('col'+col,'div',{tag:'th'});

											} else {

												if (!_.isEmpty(summedColumns)) {
													ret += '<th class="borderTable" style="' + thStyleForNoUI + '"></th>';
												}

											}

										}

									}

								});

								if (!ui) {
									if (!_.isEmpty(summedColumns)) {
											ret += '</tr>';
										ret += '</tfoot>';
									}
								}

								if (ui) {
									ui.tableWrapper['table'+groupKey].makeNode('body','div',{tag:'tbody'});
								} else {
									ret += '<tbody>';
								}

								_.each(group, function(obj) {

									if (ui) {
										ui.tableWrapper['table'+groupKey].body.makeNode('row'+obj.id,'div',{tag:'tr'});
									} else {
										ret += '<tr class="borderTable">';
									}

									// Actions menu
									if (appConfig.instance == bpObj.instance) {

										if (bpObj.bp_name !== 'time_entries') {

											if (ui) {

                                                var actionsMenuSetup = {
                                                    actions: {
                                                        view: true,
                                                        archive: {
                                                            onArchive: function() {
                                                                refreshTable({
                                                                    skipCache: true
                                                                });
                                                            },
                                                            bypassRefresh: true
                                                        },
                                                        copy: {
                                                            onCopy: function() {
                                                                refreshTable({
                                                                    skipCache: true
                                                                });
                                                            },
                                                            bypassRefresh: true
                                                        },
                                                        comments: true
                                                    }
                                                };

                                                if ( portalInstance ) {
                                                    actionsMenuSetup.actions.view = false;
                                                    actionsMenuSetup.actions.copy = false;
                                                    actionsMenuSetup.actions.comments = false;
                                                    actionsMenuSetup.actions.navigateTo = false;
                                                }
												sb.notify ({
													type: 'get-actions-menu',
													data: {
														ui: ui.tableWrapper['table'+groupKey].body['row'+obj.id].makeNode('actions','div',{tag:'td'}),
														obj: obj,
														options: actionsMenuSetup
													}
												});

											}

										}

									}

									_.each(visibleColumns, function(col) {

										var viewOptions = {
											blueprint: bpObj.blueprint,
											commitUpdates: true,
											edit: false,
											inCollection: true,
											isGrouping: false,
											placeholder: 'Empty'
										};

										var fieldType = (col != 'parent') ? bpObj.blueprint[col].fieldType : 'edge';
										var css = (col == 'name' || col == 'shift') ? 'table-view-title-cell' : '';

										if (ui) {

											ui.tableWrapper['table'+groupKey].body['row'+obj.id].makeNode('col'+col, 'div', {
												tag: 'td',
												text: '',
												css: css
											});

										}

										if (col == 'shift') {

											if (!obj.shift) {
												obj.shift = {};
											}
											obj.shift.object_uid = obj.object_uid;

											var shiftType = obj.shift.object_bp_type;
											if (shiftType === 'groups') {
												shiftType = obj.shift.group_type;
											}
											if (shiftType) {
												shiftType = shiftType.toLowerCase();
											}

											if (ui) {

												var setup = {
													text: obj.shift.name,
													tag: 'a',
													css: 'ui small header',
													style: 'display:block'
												}

												if (appConfig.instance == bpObj.instance) {
													setup.href = sb.data.url.createPageURL(
														'object',
														{
															id: 			obj.shift.id,
															type: 			shiftType,
															object_type: 	obj.shift.object_bp_type,
															name: 			obj.shift.name
														}
													)
												}

												ui.tableWrapper['table'+groupKey].body['row'+obj.id]['col'+col].makeNode('span', 'div', {
													tag: 'span'
												}).makeNode('a', 'div', setup);

											} else {

												ret += '<td class="borderTable" style="' + tdStyleForNoUI + '">' + obj.shift.name + '</td>';

											}

										} else if (col == 'duration') {

											var duration = sb.dom.durationDisplay(obj);

											if (ui) {

												ui.tableWrapper['table'+groupKey].body['row'+obj.id]['col'+col].makeNode('row-' + obj.oid, 'div', {
													text: duration
												});

											} else {

												ret += '<td class="borderTable" style="' + tdStyleForNoUI + '">' + duration + '</td>';

											}

										} else {

											var fieldSetup = {
												type: fieldType
												, property: col
												, obj:      obj
												, options:  viewOptions
											};

											if (
												fieldSetup.type == 'title'
												|| fieldSetup.type == 'detail'
											) {

												if (fieldSetup.property == 'name') {
													fieldSetup.options.cellActions = {
														label: 'Edit',
														forcedVisible: true,
														view: {
															onComplete: function(_response){
																console.log('table js onComplete');
																refreshTable(options)
															}
															, onClose: function(_response){
																console.log('table js onClose');
																refreshTable(options)
															}
															, onChange: function(data){

																response.table.data = _.map(response.table.data, function(dataTbObj){
																	if(dataTbObj.id === obj.id){
																		if(dataTbObj[data.key] != undefined) {
																			dataTbObj[data.key] = data.newVal;
																		}
																		return dataTbObj;
																	}

																	return dataTbObj;
																});

																renderTable(response);
															}
														}
													};
												}
												fieldSetup.options.useFieldListener = true;
												fieldSetup.options.edit = options.edit;
												if ( fieldSetup.type == 'detail' ){
													fieldSetup.options.commitUpdates = false;
													fieldSetup.options.inTableField = true;
												}
											}

											if (ui) {

												fieldSetup.ui = ui.tableWrapper['table'+groupKey].body['row'+obj.id]['col'+col];

											} else {

												fieldSetup.options.callback = function(response) {
													ret += '<td class="borderTable" style="' + tdStyleForNoUI + '">' + response + '</td>';
												}

											}

											if (appConfig.instance == bpObj.instance) {

												switch(fieldType) {

													case 'state':

														fieldSetup.options.edit = true;
														fieldSetup.options.editing = true;
														fieldSetup.options.stateTracker = false;
														fieldSetup.options.processView = false;
														fieldSetup.options.size = 'mini';
														fieldSetup.options.inTableField = true;

														break;

												}

											} else {

												switch(fieldType) {

													case 'title':

														fieldSetup.options.link = false;

														break;

												}

											}

											sb.notify({
												type: 'view-field',
												data: fieldSetup
											});

										}

									});

									if (!ui) {
										ret += '</tr>';
									}

								});

								if(ui){
									buildAddRowButton(ui, groupKey, visibleColumns, obj, createRecord, onSuccess, onAsyncSuccess, response.table.data);
								}
							});

						}

						if (ui) {

							ui.patch();

						} else {

							if (!_.isEmpty(ret)) {

									ret += '</tbody>';
								ret += '</table>';

								if (typeof options.callback === 'function') {
									options.callback(ret);
								}

								return;

							}

						}

						if (!IN_NODE_ENV) {

							$('#selectAll').on('click', function(){

								if(selectedItems.length == 0){

									// select all
									selectedItems = data;

									$('#selectAll').html('<i class="circle icon"></i>');
									$('.item-selection').html('<i class="circle icon"></i>');

								}else{

									// deselect all
									selectedItems = [];
									$('#selectAll').html('<i class="circle outline icon"></i>');
									$('.item-selection').html('<i class="circle outline icon"></i>');

								}

							});

							$('.item-selection').on('click', function(e){

								var selectedId = $(this).data('id');

								var alreadySelected = _.findWhere(selectedItems, {id:selectedId});

								if(alreadySelected){

									// deselect single item
									selectedItems = _.reject(selectedItems, function(child){
										return child.id == alreadySelected.id;
									});

									$('#item-selection'+selectedId).html('<i class="circle outline icon"></i>');

								}else{

									// select single item
									selectedItems.push(_.findWhere(data, {id:selectedId}));
									$('#item-selection'+selectedId).html('<i class="circle icon"></i>');

								}

							});

						}

						if (callback) {
							callback();
						}
					};
					getData(options, renderTable);

				});
				
			} else {

				if (callback) {
					callback();
				}
				
			}
			
		}

		buildTable(ui, function(){}, cachedList, options);
		
	}

	return {

		initListeners: function () {
			
			sb.listen({
				'get-table-view': this.view
			});
			
		},

		init: function() {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'table',
					view: View,
					title: 'Table',
					type: 'Link to Other Sets',
					availableToEntities: true,
					icon: 'list',
					propertyType: 'objectId',
					objectType: '',
					select: {
						name: true,
						status: true,
						object_uid: true
					},
					options: {
						objectType: {
							name: 	'Select a data set',
							type: 'objectType',
							includeBlueprints: ['time_entries']
						},
						includeInSelect: {
							name: 'Show field(s) in table',
							type: 'field',
							listPicker: {
								metrics: {
									sum: true
								}
							},
							blueprint: 'objectType',
							allowedTypes: [
                                'address'
                                , 'attachments'
                                , 'companies'
                                , 'contacts'
                                , 'currency'
                                , 'date'
                                , 'detail'
                                , 'edge'
                                , 'email-address'
                                , 'image'
                                , 'phone'
                                , 'plain-text'
                                , 'priority'
                                , 'quantity'
                                , 'select'
                                , 'state'
                                , 'timer'
                                , 'title'
                                , 'toggle'
                                , 'url'
                                , 'user'
                                , 'users'
                            ]
						},
						pageLength: {
							name: 'How many records should be shown at a time?',
							type: 'select',
							options: [
								{
									name: '5',
									value: 5
								},
								{
									name: '10',
									value: 10
								},
								{
									name: '25',
									value: 25
								},
								{
									name: '50',
									value: 50
								},
								{
									name: '75',
									value: 75
								},
								{
									name: '100',
									value: 100
								},
								{
									name: '200',
									value: 200
								}
							]
						}
					},
					parseOptions: function (selection, field) {
						
						if (selection.multi) {
							field.type = 'objectIds';
						}
						field.objectType = selection.objectType;
						
						return field ;
						
					},
					hideDefault: true
				}
			});

			if (IN_NODE_ENV) {
				module.exports = { 
					table
				}
			}

		},

		view: function(data) {

			table(data.fieldName, data.ui, data.obj, data.options);

		}

	}

});

if (IN_NODE_ENV) {
	Factory.startAll();
}
