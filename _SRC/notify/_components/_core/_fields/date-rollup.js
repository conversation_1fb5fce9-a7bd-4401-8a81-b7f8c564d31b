var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register ('date-rollup-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	// View
	function View (fieldName, ui, obj, options) {
//console.log('date-rollup-args', arguments);
		var set = false;
		var fieldReference = false;
		// Determine if the field is setup properly
		var isSetup = true;
		var isLocked = false;
		
		if(options.blueprint){
			var fieldOptions = options.blueprint[fieldName].options;
		}else{
			var fieldOptions = options;
		}
		
		var fieldOptions = options;

		ui.empty();
		
		if (
			!_.isEmpty(options.set)
			&& !_.isEmpty(options.field)
		) {
			
			set = _.findWhere(appConfig.Types, {bp_name: options.set.substr(1)});
			fieldReference = set.blueprint[options.field];

			if (fieldReference) {
				
				isSetup = true;
				fieldReference.options.editing = false;
				
			}
			
		}
		
		// Determine if field already has referenced value
		var hasReferencedValue = false;
		
		if ( obj.hasOwnProperty([fieldName + '_refId']) ) {
			
			if ( !_.isEmpty(obj[fieldName + '_refId'] ) ) {
				
				hasReferencedValue = true;
				isSetup = true;
			}
		}
		
		// Show validation button
		if ( isSetup ) {
			
			ui.makeNode('cont','div',{css:'ui basic button', style:'cursor:pointer;'})
				.notify('click',{
					type:'fields-run',
					data:{
						run:function(){
							
							sb.notify({
								type: 	'get-sys-modal'
								, data: 	{
									callback: 	function (modal) {
										
										modal.body.empty();
										modal.show();
										
										if(obj[fieldName+'_refId']){
											
											sb.notify({
												type: 'view-entity'
												, data: {
													ui:     modal.body
													, id:   obj[fieldName+'_refId']
												}
											});
											
										}else{
											
											sb.data.db.obj.getById(obj.object_bp_type, obj.id, function(fullObj){

												sb.notify({
													type: 'view-entity'
													, data: {
														ui:     modal.body
														, id:   fullObj[fieldName+'_refId']
													}
												});
																																		
											}, 1, true);
											
										}
										
									}
									, onClose: function () {
										
										
					
									}
								}
							});
							
						}
					}
				},sb.moduleId);
			
			options.edit = false;
			options.editing = false;

			// Show the field
			sb.notify ({
				type: 'view-field',
				data: {
					type: 'date',
					property: fieldName,
					obj: obj,
					options: options,
					ui: ui.cont
				}
			});			
						
			ui.patch();
			
		} else {
			
			ui.makeNode('i-'+ obj.id, 'div', {
				text: '<span class="ui text red"><i class="exclamation triangle icon"></i> Error: Please setup all required settings on this field.</span>',
				isTabbable: true
			});
			
		}

	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'date-rollup',
					view: View,
					title: 'Date Rollup',
					type: 'Link to Other Sets',
					availableToEntities: true,
					icon: 'calendar',
					getIcon: function (options) {
						
						if ( !_.isEmpty(options.set) ) {
							
							var set = _.findWhere(appConfig.Types, {bp_name: options.set.substr(1)});
							var fieldReference = set.blueprint[options.field];
							
							if (fieldReference) {
								
								var icon = _.findWhere(appConfig.Fields, {name: fieldReference.fieldType}).icon;
							
								return icon;	
								
							}
							
						} else {
							
							return 'sync';
							
						}
						
					},
					options: {
						set: {
							name: 'Select a data set (required)',
							type: 'objectType'
						},
						field: {
							name: 'Referenced field (required):',
							type: 'field',
							multi: false,
							allowedTypes: ['date','next-due-date'],
							blueprint: 'set'
						},
						aggregationType: {
							name:'Aggregation Type',
							type:'select',
							options:[
								{
									name:'Most Urgent',
									value:'urgent'
								},
								{
									name:'Least Urgent',
									value:'least_urgent'
								}
							]
						}
						, is_due_date: {
							type: 		'bool'
							, name: 	'Is due date?'
						}
						, green: {
							type: 		'bool'
							, name: 	'Use green color for items due in the future?'
						}
						, yellow: {
							type: 		'string'
							, name: 	'Number of days before to highlight in yellow?'
						}
						, red: {
							type: 		'string'
							, name: 	'Number of days before to highlight in red?'
						}
						
					}
				}
			});
			
		}
		
	};
	
});
