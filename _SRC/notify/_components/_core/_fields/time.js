var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('time-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }
	
	function View (fieldName, ui, obj, options) {
		
		var time = options.minutesOnly ? obj[fieldName] : moment(obj[fieldName], 'h:mm a').local();
		var showRelative = true;
		var defaultFlags = {
			style: '',
			format: 'time'
		};
		
		var disabledClass = '';
		var disableCursor = '';
			
		if (!options.edit) {
			
			disabledClass = 'text-muted';
			disableCursor = 'cursor: not-allowed !important;';
			
		}
		
		if (!options.hasOwnProperty('commitUpdates')) { options.commitUpdates = true; }
		
		function checkOptions(options) {
			
			_.each(options, function(v, k) {
				
				if(defaultFlags.hasOwnProperty(k)) {
					
					defaultFlags[k] = v;
					
				}
				
			});
			
		}
		
		checkOptions(options);
		
		if (options.hasOwnProperty('showRelative')) {
			showRelative = options.showRelative;
		}

		ui.empty();

		var txt = (_.isEmpty(obj[fieldName])) ? '<i class="text-muted">Not set</i>' : time;
		if (!options.minutesOnly) {
			if (time.isValid()) {
				txt = time.format('LT');
			}
		}
		
		var style = style + ' ' + defaultFlags.style;
		ui.makeNode('txt', 'div', {
			text: 	txt,
			style: 'font-weight: bold; ' + style + ' ' + disableCursor,
			css: disabledClass
		});
		
		if (options.editing && options.edit) {
			
			ui.listeners.push( function (selector) {
				
				Edit(fieldName, ui, obj, options);
				
			});
			
			return ;
			
		} else {
			
			ui.listeners.push( function (selector) {
				
				$(selector).on('click', function(e){
					
					if (options.edit) {
						
						Edit(fieldName, ui, obj, options);
					}
					
				});
				
			});
			
		}
		
		if (options.isRefreshing) {	
			ui.patch();	
			return;
		}
		
	}
	
	function Edit (fieldName, ui, obj, options) {

		function closeEdit (newVal) {

			obj[fieldName] = newVal;

			if (!options.minutesOnly) {
			
				var utcOffset = new Date().getTimezoneOffset()/60;
				var momentObj = moment(newVal, 'h:mm A');
				
				if (momentObj.isValid()) {
					
					// check for daylight savings time shift
					var dt = momentObj.format('HH:mm:ss');
					var nowTime = moment().format('HH:mm:ss');

					// If is daylight savings, and time selected outside of dst, 
					// shift an hour forward.
					if (
						!moment.tz(dt, moment.tz.guess()).isDST()
						&& moment.tz(nowTime, moment.tz.guess()).isDST()
					) {
						
						momentObj.add(1, 'hour');
						
					// If its not daylight savings, and time selected is in dst, 
					// shift back an hour.
					} else if (
						moment.tz(dt, moment.tz.guess()).isDST()
						&& !moment.tz(nowTime, moment.tz.guess()).isDST()
					) {
						
						momentObj.subtract(1, 'hour');
						
					}

					obj[fieldName] = momentObj.add(utcOffset, 'hours').format('h:mm a');

					if (typeof options.update === 'function') {
						options.update(obj, fieldName, obj[fieldName], function(){});
					}
					
				}

			}
			
			if (!options.editing) {
				View(fieldName, ui, obj, options);
				ui.patch();
			}
			
			if(options.removeUTC && !options.minutesOnly) {
				obj[fieldName] = momentObj.subtract(utcOffset, 'hours').format('h:mm a');
			}
			
			if (typeof options.onUpdate === 'function') {
				options.onUpdate(obj);
			}
			
			if (options.commitUpdates) {

				if (momentObj.isValid() || options.minutesOnly) {

					sb.data.db.obj.update(
						obj.object_bp_type
						, {
							id: 			obj.id
							, [fieldName]: 	obj[fieldName]
						}
						, function(updated){
							
							sb.notify({
								type: 'field-updated'
								, data: {
									obj: 			obj
									, property: 	fieldName
									, updatedBy: 	options.fieldId
								}
							});
							
							if ( options.hasOwnProperty('blueprint') ) {
								
								if ( options.blueprint ) {
									
									Edit (fieldName, ui, obj, options);
									
								} else {
									
									ui.patch();
									
								}
								
							} else {
								
								ui.patch();
								
							}
							
						}
					);
					
				}	
				
			}
			
		}
		
		if (typeof options.onEditStart === 'function') {
			options.onEditStart();
		}

		var timeVal = obj[fieldName];
		var timeTxt = timeVal;

		if (options.minutesOnly) {
			timeTxt = '00:' + timeTxt;
		} else {
			timeVal = (_.isEmpty(obj[fieldName]) || _.isNull(obj[fieldName])) ? moment() : moment(obj[fieldName] +' +0000', 'h:mm a').local();		
			timeTxt = timeVal.format('LT');
		}
		
		// Empty state, for entities.
		if (
			options 
			&& options.blueprint
			&& _.isEmpty(obj[fieldName])
		) {
			timeTxt = '';
		}
		
		$(ui.selector).html(
			'<div class="ui calendar" id="'+ options._fieldId +'">'+
				'<div class="ui fluid input">'+
					'<input style="border:none; background-color:transparent; outline:none; width:100%;' + options.style + '" type="text" value="'+ timeTxt +'" placeholder="Empty" name="'+ fieldName +'" autocomplete="off">'+
				'</div>'+
			'</div>');

		if (options.minutesOnly) {
			
			$('#'+ options._fieldId).calendar({
				type: 'time',
				startMode: 'minute',
				formatter: {
					time: function (time, settings) {
						if (!time) {
							return '';
						}
						return time.getMinutes();
					}
				},
				onHide: function (time, text, mode) {

					var newVal = $(this.selector +' .calendar input').val();

					if (!_.isEmpty(newVal)) {
						closeEdit(newVal);
					}
					
				}.bind(ui)
			});

		} else {

			$('#'+ options._fieldId).calendar({
				type: 'time',
				onHide: function (time, text, mode) {
					
					var newVal = $(this.selector +' .calendar input').val();

					if (!_.isEmpty(newVal)) {
						closeEdit(newVal);
					}
					
				}.bind(ui)
			});

		}
		
		if (!options.editing) {
			$('#'+ options._fieldId).calendar('focus');
		}
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'time'
					, title: 'Time'
					, view: View
					, detail: function (key, obj, opts) {
						
						if (
							opts 
							&& opts.fields
							&& opts.fields[key]
						) {
							return opts.fields[key].title;
						}
						
						return false;
						
					}
					, availableToEntities: false
					, propertyType: 'time'
					, icon: 'clock'
					, options: {}
					, shouldPatch: false
				}
			});
			
			sb.listen({
				'time-field-comp-run': this.run
			});
			
		}
		
		, run: function (data) { data.run(); }
		
	}
	
});