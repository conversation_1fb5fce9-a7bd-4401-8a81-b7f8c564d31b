var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('schedule-field', function (sb) {

    if (IN_NODE_ENV) {
        return;
    }
	
	function View (fieldName, ui, obj, options) {
console.log( 'schedule.js -- Line 21   View ( args', arguments );
		function updateField(scheduleObj, prop, callback, override) {

			if (
				((scheduleObj[prop] != obj[fieldName][prop]) 
				|| override)
				&& options.commitUpdates != false
				) {
				
				sb.data.db.obj.update(
					obj.object_bp_type
					, {
						id: 			obj.id
						, [fieldName]: 	scheduleObj
					}
					, function () {

						obj[fieldName] = _.clone(scheduleObj);
	
						callback(scheduleObj);
	
					}
				);

			} else {

				obj[fieldName] = _.clone(scheduleObj);

				callback(scheduleObj);

			}

		}

		if (!obj[fieldName]) {
			obj[fieldName] = {
				quantityOfTime: 1,
				unitOfTime: 'week',
				dayOfWeek: 'Monday, Tuesday, Wednesday, Thursday, Friday',
				dayOfMonth: '1',
				monthOfYear: 'January',
				timeOfDay: '12:00 pm',
				startAfter: false,
				startAfterQuantity: 1,
				endsAfter: false,
				endsAfterQuantity: 1,
				isScheduled: false,
                startReference:''
			}
		}

		// Define the obj
		var scheduleObj = _.clone(obj[fieldName]);
		// Ensure commitUpdates is correct
		options.commitUpdates = options.commitUpdates == false ? false : true;

		// Setup the UI
		var scheduleContainer = ui.makeNode('scheduleContainer', 'div', {
			css: 'ui accordion round-border field-value',
			style: 'padding:10px;'
		});
		var scheduleTitleContainer = scheduleContainer.makeNode('scheduleTitleContainer', 'div', {
			css: 'title',
			text: '<i class="dropdown icon"></i> Scheduling Options'
		});
		var scheduleContentContainer = scheduleContainer.makeNode('scheduleContentContainer', 'div', {
			css: 'content'
		});

		// Patch the UI
		ui.patch();

		var timeframeContainer = scheduleContentContainer.makeNode('timeframeContainer', 'div', {});
		var timeframeFieldsContainer = scheduleContentContainer.makeNode('timeframeFieldsContainer', 'div', {});

		// Timeframe (minutes, hours, days, weeks, months, years)
		var labelText = (scheduleObj.type === 'recurring') ? 'Run this schedule every:' : 'Schedule this action to run every:';
		timeframeContainer.makeNode('label', 'div', {
			text: labelText, 
			css: 'ui header',
			style: 'margin-top:15px !important; margin-bottom:2px !important;',
			tag:'h5'
		});
		var quantityOfTimeField = timeframeContainer.makeNode('quantityOfTimeField', 'div', {
			css: 'round-border',
			style: 'width:10%; float:left; min-height:40px !important; border-right:none !important;'
		});
		sb.notify ({
			type: 'view-field',
			data: {
				type: 'quantity',
				property: 'quantityOfTime',
				obj: scheduleObj,
				options: {
					default: 1,
					min: 1,
					edit: true,
					editing: true,
					updateOnLoad: true,
					onUpdate(scheduleObj) {

						updateField(scheduleObj, 'quantityOfTime', function(scheduleObj) {});

					}
				},
				ui: quantityOfTimeField
			}
		});
		var unitOfTimeField = timeframeContainer.makeNode('unitOfTime', 'div', {
			css: 'round-border',
			style: 'width:90%; float:left;'
		});
		sb.notify ({
			type: 'view-field',
			data: {
				type: 'select',
				property: 'unitOfTime',
				obj: scheduleObj,
				options: {
					options: [
						{
							name:'Hour(s)',
							value:'hour'
						},
						{
							name:'Day(s)',
							value:'day'
						},
						{
							name:'Week(s)',
							value:'week'
						},
						{
							name:'Month',
							value:'month'
						},
						{
							name:'Year(s)',
							value:'year'
						}
					],
					edit: true,
					editing: true,
					updateOnLoad: true,
					onUpdate(scheduleObj) {

						updateField(scheduleObj, 'unitOfTime', function(scheduleObj) {

							// Clear the UI
							timeframeFieldsContainer.empty();

							// Hours
							if (scheduleObj.unitOfTime === 'hour') {

								// Show the quantity field
								$(document).ready(function() {
									$(quantityOfTimeField.selector).show();
									$(unitOfTimeField.selector).css({'width':'90%'});
								});

							}

							// Day of week
							else if (scheduleObj.unitOfTime === 'day') {

								// Show the quantity field
								$(document).ready(function() {
									$(quantityOfTimeField.selector).hide();
									$(unitOfTimeField.selector).css({'width':'100%'});
								});
			
								var dayOfWeekContainer = timeframeFieldsContainer.makeNode('dayOfWeekContainer', 'div', {});
								dayOfWeekContainer.makeNode('label', 'div', {
									text: 'On these days:', 
									css: 'ui header', 
									style: 'margin-top:15px !important; margin-bottom:2px !important;',
									tag:'h5'
								});
								dayOfWeekContainer.makeNode('field', 'div', {
									css: 'round-border'
								});
								sb.notify ({
									type: 'view-field',
									data: {
										type: 'select',
										property: 'dayOfWeek',
										obj: scheduleObj,
										options: {
											options: [
												{
													name:'Sunday',
													value:'Sunday'
												},
												{
													name:'Monday',
													value:'Monday'
												},
												{
													name:'Tuesday',
													value:'Tuesday'
												},
												{
													name:'Wednesday',
													value:'Wednesday'
												},
												{
													name:'Thursday',
													value:'Thursday'
												},
												{
													name:'Friday',
													value:'Friday'
												},
												{
													name:'Saturday',
													value:'Saturday'
												}
											],
											multi: true,
											edit: true,
											editing: true,
											updateOnLoad: true,
											onUpdate(scheduleObj) {
			
												updateField(scheduleObj, 'dayOfWeek', function(scheduleObj) {});

											}
										},
										ui: dayOfWeekContainer.field
									}
								});
			
							}

							// Day of week
							else if (scheduleObj.unitOfTime === 'week') {

								// Show the quantity field
								$(document).ready(function() {
									$(quantityOfTimeField.selector).show();
									$(unitOfTimeField.selector).css({'width':'90%'});
								});
			
								var dayOfWeekContainer = timeframeFieldsContainer.makeNode('dayOfWeekContainer', 'div', {});
								dayOfWeekContainer.makeNode('label', 'div', {
									text: 'On these days:', 
									css: 'ui header', 
									style: 'margin-top:15px !important; margin-bottom:2px !important;',
									tag:'h5'
								});
								dayOfWeekContainer.makeNode('field', 'div', {
									css: 'round-border'
								});
								sb.notify ({
									type: 'view-field',
									data: {
										type: 'select',
										property: 'dayOfWeek',
										obj: scheduleObj,
										options: {
											options: [
												{
													name:'Sunday',
													value:'Sunday'
												},
												{
													name:'Monday',
													value:'Monday'
												},
												{
													name:'Tuesday',
													value:'Tuesday'
												},
												{
													name:'Wednesday',
													value:'Wednesday'
												},
												{
													name:'Thursday',
													value:'Thursday'
												},
												{
													name:'Friday',
													value:'Friday'
												},
												{
													name:'Saturday',
													value:'Saturday'
												}
											],
											multi: true,
											edit: true,
											editing: true,
											updateOnLoad: true,
											onUpdate(scheduleObj) {
			
												updateField(scheduleObj, 'dayOfWeek', function(scheduleObj) {});

											}
										},
										ui: dayOfWeekContainer.field
									}
								});
			
							}

							// Month of year
							else if (scheduleObj.unitOfTime === 'month') {

								// Hide the quantity field
								$(document).ready(function() {
									$(quantityOfTimeField.selector).hide();
									$(unitOfTimeField.selector).css({'width':'100%'});
								});
			
								var monthOfYearContainer = timeframeFieldsContainer.makeNode('monthOfYearContainer', 'div', {});
								monthOfYearContainer.makeNode('label', 'div', {
									text: 'On these months:', 
									css: 'ui header', 
									style: 'margin-top:15px !important; margin-bottom:2px !important;',
									tag:'h5'
								});
								monthOfYearContainer.makeNode('field', 'div', {
									css: 'round-border'
								});
								sb.notify ({
									type: 'view-field',
									data: {
										type: 'select',
										property: 'monthOfYear',
										obj: scheduleObj,
										options: {
											options: [
												{
													name:'January',
													value:'January'
												},
												{
													name:'February',
													value:'February'
												},
												{
													name:'March',
													value:'March'
												},
												{
													name:'April',
													value:'April'
												},
												{
													name:'May',
													value:'May'
												},
												{
													name:'June',
													value:'June'
												},
												{
													name:'July',
													value:'July'
												},
												{
													name:'August',
													value:'August'
												},
												{
													name:'September',
													value:'September'
												},
												{
													name:'October',
													value:'October'
												},
												{
													name:'November',
													value:'November'
												},
												{
													name:'December',
													value:'December'
												}
											],
											multi: true,
											edit: true,
											editing: true,
											updateOnLoad: true,
											onUpdate(scheduleObj) {

												updateField(scheduleObj, 'monthOfYear', function(scheduleObj) {});

											}
										},
										ui: monthOfYearContainer.field
									}
								});
			
							}
			
							// Month of year
							else if (scheduleObj.unitOfTime === 'year') {

								// Show the quantity field
								$(document).ready(function() {
									$(quantityOfTimeField.selector).show();
									$(unitOfTimeField.selector).css({'width':'90%'});
								});
			
								var monthOfYearContainer = timeframeFieldsContainer.makeNode('monthOfYearContainer', 'div', {});
								monthOfYearContainer.makeNode('label', 'div', {
									text: 'On this month:', 
									css: 'ui header', 
									style: 'margin-top:15px !important; margin-bottom:2px !important;',
									tag:'h5'
								});
								monthOfYearContainer.makeNode('field', 'div', {
									css: 'round-border'
								});
								sb.notify ({
									type: 'view-field',
									data: {
										type: 'select',
										property: 'monthOfYear',
										obj: scheduleObj,
										options: {
											options: [
												{
													name:'January',
													value:'January'
												},
												{
													name:'February',
													value:'February'
												},
												{
													name:'March',
													value:'March'
												},
												{
													name:'April',
													value:'April'
												},
												{
													name:'May',
													value:'May'
												},
												{
													name:'June',
													value:'June'
												},
												{
													name:'July',
													value:'July'
												},
												{
													name:'August',
													value:'August'
												},
												{
													name:'September',
													value:'September'
												},
												{
													name:'October',
													value:'October'
												},
												{
													name:'November',
													value:'November'
												},
												{
													name:'December',
													value:'December'
												}
											],
											edit: true,
											editing: true,
											updateOnLoad: true,
											onUpdate(scheduleObj) {

												updateField(scheduleObj, 'monthOfYear', function(scheduleObj) {});

											}
										},
										ui: monthOfYearContainer.field
									}
								});
			
							}
			
							// Day of month
							if (scheduleObj.unitOfTime === 'month' || scheduleObj.unitOfTime === 'year') {
			
								var dayOfMonthContainer = timeframeFieldsContainer.makeNode('dayOfMonthContainer', 'div', {});
								dayOfMonthContainer.makeNode('label', 'div', {
									text: 'On this day of the month:', 
									css: 'ui header', 
									style: 'margin-top:15px !important; margin-bottom:2px !important;',
									tag:'h5'
								});
								dayOfMonthContainer.makeNode('field', 'div', {
									css: 'round-border'
								});
								sb.notify ({
									type: 'view-field',
									data: {
										type: 'select',
										property: 'dayOfMonth',
										obj: scheduleObj,
										options: {
											options: [
												{
													name:'1',
													value:'1'
												},
												{
													name:'2',
													value:'3'
												},
												{
													name:'3',
													value:'3'
												},
												{
													name:'4',
													value:'4'
												},
												{
													name:'5',
													value:'5'
												},
												{
													name:'6',
													value:'6'
												},
												{
													name:'7',
													value:'7'
												}
												,
												{
													name:'8',
													value:'8'
												}
												,
												{
													name:'9',
													value:'9'
												}
												,
												{
													name:'10',
													value:'10'
												}
												,
												{
													name:'11',
													value:'11'
												}
												,
												{
													name:'12',
													value:'12'
												}
												,
												{
													name:'13',
													value:'13'
												}
												,
												{
													name:'14',
													value:'14'
												}
												,
												{
													name:'15',
													value:'15'
												}
												,
												{
													name:'16',
													value:'16'
												}
												,
												{
													name:'17',
													value:'17'
												}
												,
												{
													name:'18',
													value:'18'
												}
												,
												{
													name:'19',
													value:'19'
												}
												,
												{
													name:'20',
													value:'20'
												}
												,
												{
													name:'21',
													value:'21'
												}
												,
												{
													name:'22',
													value:'22'
												}
												,
												{
													name:'23',
													value:'23'
												}
												,
												{
													name:'24',
													value:'24'
												}
												,
												{
													name:'25',
													value:'25'
												}
												,
												{
													name:'26',
													value:'26'
												}
												,
												{
													name:'27',
													value:'27'
												}
												,
												{
													name:'28',
													value:'28'
												}
												,
												{
													name:'29',
													value:'29'
												}
												,
												{
													name:'30',
													value:'30'
												}
												,
												{
													name:'31',
													value:'31'
												}
											],
											edit: true,
											editing: true,
											updateOnLoad: true,
											onUpdate(scheduleObj) {

												updateField(scheduleObj, 'dayOfMonth', function(scheduleObj) {});

											}
										},
										ui: dayOfMonthContainer.field
									}
								});
			
							}

							// Time of day
							// if (scheduleObj.unitOfTime === 'hour') {

							// 	var timeOfDayContainer = timeframeFieldsContainer.makeNode('timeOfDayContainer', 'div', {});
							// 	timeOfDayContainer.makeNode('label', 'div', {
							// 		text: 'Starting from this hour:', 
							// 		css: 'ui header', 
							// 		style: 'margin-top:15px !important; margin-bottom:2px !important;',
							// 		tag:'h5'
							// 	});
							// 	timeOfDayContainer.makeNode('field', 'div', {
							// 		css: 'round-border'
							// 	});
							// 	sb.notify ({
							// 		type: 'view-field',
							// 		data: {
							// 			type: 'time',
							// 			property: 'timeOfDay',
							// 			obj: scheduleObj,
							// 			options: {
							// 				minutesOnly: false,
							// 				commitUpdates: false,
							// 				edit: true,
							// 				editing: true,
							// 				onUpdate(scheduleObj) {
												
							// 					updateField(scheduleObj, 'timeOfDay', function(scheduleObj) {});

							// 				}
							// 			},
							// 			ui: timeOfDayContainer.field
							// 		}
							// 	});

							// } else {

							// 	var timeOfDayContainer = timeframeFieldsContainer.makeNode('timeOfDayContainer', 'div', {});
							// 	timeOfDayContainer.makeNode('label', 'div', {
							// 		text: 'At this time:', 
							// 		css: 'ui header', 
							// 		style: 'margin-top:15px !important; margin-bottom:2px !important;',
							// 		tag:'h5'
							// 	});
							// 	timeOfDayContainer.makeNode('field', 'div', {
							// 		css: 'round-border'
							// 	});
							// 	sb.notify ({
							// 		type: 'view-field',
							// 		data: {
							// 			type: 'time',
							// 			property: 'timeOfDay',
							// 			obj: scheduleObj,
							// 			options: {
							// 				commitUpdates: false,
							// 				edit: true,
							// 				editing: true,
							// 				onUpdate(scheduleObj) {

							// 					updateField(scheduleObj, 'timeOfDay', function(scheduleObj) {});

							// 				}
							// 			},
							// 			ui: timeOfDayContainer.field
							// 		}
							// 	});

							// }

							// Starts after
							var startAfterContainer = timeframeFieldsContainer.makeNode('startAfterContainer', 'div', {});
							startAfterContainer.makeNode('label', 'div', {
								text: 'When moved to this state of the workflow, start this schedule after:', 
								css: 'ui header', 
								style: 'margin-top:20px !important; margin-bottom:5px !important',
								tag:'h5'
							});

							var startAfterCheckbox = startAfterContainer.makeNode('startAfterCheckbox', 'div', {
								style: 'width:40px; float:left;'
							});
							sb.notify ({
								type: 'view-field',
								data: {
									type: 'toggle',
									property: 'startAfter',
									obj: scheduleObj,
									options: {
										edit: true,
										editing: true,
										onUpdate(response) {

											scheduleObj.startAfter = response;

											updateField(scheduleObj, 'startAfter', function(scheduleObj) {});

										}
									},
									ui: startAfterCheckbox
								}
							});
			
							var startAfterQuantity = startAfterContainer.makeNode('startAfterQuantity', 'div', {
								css: 'round-border',
								style: 'width:50px; float:left; position:relative; margin-right:5px;'
							});
							sb.notify ({
								type: 'view-field',
								data: {
									type: 'quantity',
									property: 'startAfterQuantity',
									obj: scheduleObj,
									options: {
										default: 1,
										min: 1,
										edit: true,
										editing: true,
										onUpdate(scheduleObj) {

											updateField(scheduleObj, 'startAfterQuantity', function(scheduleObj) {});

										}
									},
									ui: startAfterQuantity
								}
							});
							startAfterContainer.makeNode('label2', 'div', {
								style: 'float:left; margin-top:10px;',
								text: 'days'
							});
			
							// Clear
							startAfterContainer.makeNode('clear', 'div', {
								style: 'clear:both;'
							});

							// End after quantity of times run
							var endsAfterContainer = timeframeFieldsContainer.makeNode('endsAfterContainer', 'div', {});
							endsAfterContainer.makeNode('label', 'div', {
								text: 'End this scheduled action after:', 
								css: 'ui header', 
								style: 'margin-top:20px !important; margin-bottom:5px !important',
								tag:'h5'
							});
			
							var endsAfterCheckbox = endsAfterContainer.makeNode('endsAfterCheckbox', 'div', {
								style: 'width:40px; float:left;'
							});
							sb.notify ({
								type: 'view-field',
								data: {
									type: 'toggle',
									property: 'endsAfter',
									obj: scheduleObj,
									options: {
										edit: true,
										editing: true,
										onUpdate(response) {

											scheduleObj.endsAfter = response;

											updateField(scheduleObj, 'endsAfter', function(scheduleObj) {});

										}
									},
									ui: endsAfterCheckbox
								}
							});
			
							var endsAfterQuantity = endsAfterContainer.makeNode('endsAfterQuantity', 'div', {
								css: 'round-border',
								style: 'width:50px; float:left; position:relative; margin-right:5px;'
							});
							sb.notify ({
								type: 'view-field',
								data: {
									type: 'quantity',
									property: 'endsAfterQuantity',
									obj: scheduleObj,
									options: {
										default: 1,
										min: 1,
										edit: true,
										editing: true,
										onUpdate(scheduleObj) {

											updateField(scheduleObj, 'endsAfterQuantity', function(scheduleObj) {});

										}
									},
									ui: endsAfterQuantity
								}
							});
							endsAfterContainer.makeNode('label2', 'div', {
								style: 'float:left; margin-top:10px;',
								text: 'times'
							});
			
							// Clear
							endsAfterContainer.makeNode('clear', 'div', {
								style: 'clear:both;'
							});

							// Trigger radio button change
							$(document).ready(function() {
								$('input[name="endsWhen"]').on('change', function() {
									scheduleObj.endsAfter = false;
									scheduleObj[this.id] = true;
									updateField(scheduleObj, 'endWhen', function(scheduleObj) {}, true);
								});
							});

							// Is Active
							var isScheduledContainer = timeframeFieldsContainer.makeNode('isScheduledContainer', 'div', {});
							isScheduledContainer.makeNode('label', 'div', {
								text: 'Schedule is Active:', 
								css: 'ui header', 
								style: 'margin-top:20px !important; margin-bottom:5px !important',
								tag:'h5'
							});
			
							var isScheduled = isScheduledContainer.makeNode('isScheduled', 'div', {
								style: 'top:-10px; position:relative; margin-right:5px;'
							});
							sb.notify ({
								type: 'view-field',
								data: {
									type: 'toggle',
									property: 'isScheduled',
									obj: scheduleObj,
									options: {
										edit: true,
										editing: true,
										onUpdate(response) {

											scheduleObj.isScheduled = response;

											updateField(scheduleObj, 'isScheduled', function(scheduleObj) {});

										}
									},
									ui: isScheduled
								}
							});

                            var startReference = timeframeFieldsContainer.makeNode('startReferenceContainer', 'div', {});
                            startReference.makeNode('label', 'div', {
                                text: 'Starting time reference:', 
                                css: 'ui header', 
                                style: 'margin-top:15px !important; margin-bottom:2px !important;',
                                tag:'h5'
                            });
                            startReference.makeNode('field', 'div', {
                                css: 'round-border'
                            });
                            sb.notify ({
								type: 'view-field',
								data: {
									type: 'date',
									property: 'startReference',
									obj: scheduleObj,
									options: {
										edit: true,
										editing: true,
										onUpdate(response) {

                                            updateField(scheduleObj, 'startReference', function(scheduleObj) {});

										}
									},
									ui: startReference.field
								}
							});

							// Patch the UI
							timeframeFieldsContainer.patch();

						});
						
					}
				},
				ui: unitOfTimeField
			}
		});

		// Clear
		timeframeContainer.makeNode('clear', 'div', {
			style: 'clear:both;'
		});

		// Patch the UI
		scheduleContentContainer.patch();

		$(document).ready(function() {
			$('.ui.accordion').accordion();
		});

	}
	
	return {
		
		init: function () {

			// if (
			// 	appConfig.instance == 'rickyvoltz' ||
			// 	appConfig.instance == 'voltzsoftware'
			// ) {
			
				sb.notify({
					type: 'register-field-type'
					, data: {
						name: 'schedule'
						, title: 'Schedule'
						, view: View
						, detail: function (key, obj, opts) {
							
							if (
								opts 
								&& opts.fields
								&& opts.fields[key]
							) {
								return opts.fields[key].title;
							}
							
							return false;
							
						}
						, availableToEntities: false
						// , propertyType: 'schedule'
						, icon: 'clock'
						, options: {}
						, shouldPatch: false
					}
				});
				
				sb.listen({
					'schedule-field-comp-run': this.run
				});

			// }
			
		}
		
		, run: function (data) { data.run(); }
		
	}
	
});