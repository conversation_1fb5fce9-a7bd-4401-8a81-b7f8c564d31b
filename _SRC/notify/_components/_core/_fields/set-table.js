var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('set-table-field', function(sb){

	if (IN_NODE_ENV) {
        return;
    }
	
	function View (fieldName, ui, obj, options, cachedChildren) {
// console.log('table args', arguments);	

		function buildTable(children, childBP){
			
			var nameLabel = 'Name';
			var nameCreateLabel = 'Give It A Name';
			
			if(options.nameLabel){
				nameLabel = options.nameLabel;
			}
			
			if(options.nameCreateLabel){
				nameCreateLabel = options.nameCreateLabel;
			}
			
			ui.empty();
			
			if(options.canSearch == true && hideButtons == false && showSearchButton == true){
				
				ui.makeNode('search','div',{text:'<i class="search icon"></i> Search', css:'ui mini blue icon button'})
					.notify('click', {
						type:'fields-run',
						data:{
							run:function(fieldName, ui, obj, options){
								
								sb.notify({
									type: 	'get-sys-modal'
									, data: 	{
										callback: 	function (modal) {
											
											modal.body.empty();
											modal.show();
											
											modal.body.makeNode('title','div',{text:'Search for Labor '+ options.blueprint[fieldName].name, css:'ui large header'});
											
											modal.body.makeNode('search','div',{css:'ui fluid big action input'});
											modal.body.search.makeNode('input','div',{tag:'input', id:'entity-search', type:'text', placeholder:'Type here to search or create'});
											modal.body.search.makeNode('icon','div',{text:'Search', css:'ui button', id:'set-table-perform-search'});
											
											modal.body.makeNode('currentTitle','div',{css:'ui header', text:'Search Results'});
											modal.body.makeNode('results','div',{css:'ui basic segment'});
											
											// modal.body.makeNode('currentTitle','div',{css:'ui header', text:'Currently selected '+ options.blueprint[fieldName].name});
											// modal.body.makeNode('currentTable','div',{css:'ui basic segment'});
											
											modal.body.patch();
																									
											// sb.notify({
											// 	type: 'view-field',
											// 	data: {
											// 		type: 		'set-table'
											// 		, property: fieldName
											// 		, obj: 		obj
											// 		, options: 	options
											// 		, ui: 		modal.body.currentTable
											// 	}
											// });
											
											function searchForLink(searchText, callback){
												
												if(searchText.length > 2){
													
													sb.data.db.obj.getById('',obj.tagged_with, function(tags){
														
														var companyObj = _.findWhere(tags, {object_bp_type:'companies'});
														
														var searchWhere = {
																name: {
																	type: 'contains',
																	value:searchText
																},
																childObjs:{
																	name:true
																}
															};
														_.each(options.visibleColumns, function(col){
															searchWhere.childObjs[col] = true;
														});
														
														if(options.searchTags){
															searchWhere.tagged_with = options.searchTags;
														}
														
														if(options.searchCompanyOnly){
															if(options.searchCompanyOnly == true){
																searchWhere.tagged_with = [companyObj.id];
															}
														}
														
														// start searching
														sb.data.db.obj.getWhere(options.set, searchWhere, function(searchResults){
																																	
															modal.body.results.makeNode('table','div',{css:'ui striped sortable celled table',tag:'table'});
															modal.body.results.table.makeNode('head','div',{tag:'thead'});
															modal.body.results.table.head.makeNode('row','div',{tag:'tr'});
															modal.body.results.table.head.row.makeNode('select','div',{tag:'th',text:''});
															modal.body.results.table.head.row.makeNode('name','div',{tag:'th',text:nameLabel});
															
															_.each(options.visibleColumns, function(key){
																
																modal.body.results.table.head.row.makeNode(key,'div',{tag:'th',text:childBP[key].name});
																
															});
															
															modal.body.results.table.makeNode('body','div',{tag:'tbody'});
															
															_.each(searchResults, function(result){
																
																modal.body.results.table.body.makeNode('row'+result.id,'div',{tag:'tr'});
																modal.body.results.table.body['row'+result.id].makeNode('select','div',{tag:'td',text:''})
																	.makeNode('button','div',{css:'ui mini blue button', text:'Select'})
																		.notify('click',{
																			type:'fields-run',
																			data:{
																				run:function(obj){
																					
																					$(modal.body.results.table.body['row'+result.id].select.button.selector).addClass('loading');
																																													
																					var updateObj = {
																							id:this.id,
																							tagged_with:this.tagged_with,
																							parent:obj.id
																						};
																					
																					if(updateObj.tagged_with.indexOf(obj.id) == -1){
																						
																						updateObj.tagged_with.push(obj.id);
																						
																					}																									
	
																					sb.data.db.obj.update(obj.object_bp_type, updateObj, function(updated){
																						
																						if(options.tagParent == true){
																							
																							var parentUpdate = {
																									id:obj.id,
																									tagged_with:obj.tagged_with
																								};
																								
																							if(parentUpdate.tagged_with){
																								if(parentUpdate.tagged_with.indexOf(updated.id) == -1){
																									parentUpdate.tagged_with.push(updated.id);
																								}
																							}else{
																								parentUpdate.tagged_with = [updated.id];
																							}
																							
																							sb.data.db.obj.update(obj.object_bp_type, parentUpdate, function(parentUpdated){
																								
																								modal.hide(function(){
																									
																									sb.notify({
																										type: 'field-updated',
																										data:{
																											obj: {
																												id: parentUpdated.id,
																												tagged_with:parentUpdated.tagged_with
																											}
																											, type: 		'tags'
																											, property: 	'tagged_with'
																										}
																									});
																									
																									sb.notify({
																									 type: 'refresh-app-page',
																									 data: {}
																									});
																									
																								});
																								
																							});
																							
																						}else{
																							
																							modal.hide(function(){
																								
																								sb.notify({
																								 type: 'refresh-app-page',
																								 data: {}
																								});
																								
																							});
																							
																						}
																						
																					});
																					
																				}.bind(result, obj)
																			}
																		},sb.moduleId);
																		
																modal.body.results.table.body['row'+result.id].makeNode('name','div',{tag:'td',text:result.name});
																
																_.each(options.visibleColumns, function(key){
																	
																	modal.body.results.table.body['row'+result.id].makeNode(key,'div',{tag:'td',text:result[key]});
																	
																});
																
															});
															
															modal.body.results.patch();
															
															callback(true);
															
														});
														
													}, {
														object_bp_type:true
													});
													
												}
												
											}
											
											setTimeout(function(){
												
												$('#entity-search').keypress(function(){
													
													var searchText = $('#entity-search').val();
													
													if(searchText.length > 2){
														
														$('#set-table-perform-search').addClass('loading');
														
														searchForLink(searchText, function(){
															
															$('#set-table-perform-search').removeClass('loading');
															
														});
														
													}
													
												});
												
												$('#set-table-perform-search').on('click', function(){
													
													var searchText = $('#entity-search').val();
													
													if(searchText.length > 2){
														
														$('#set-table-perform-search').addClass('loading');
														
														searchForLink(searchText, function(){
															
															$('#set-table-perform-search').removeClass('loading');
															
														});
														
													}
													
												});
												
											}, 300);
											
										}
										, onClose: function () {
											
											View (fieldName, ui, obj, options, cachedChildren);
											
											if (typeof goBack === 'function') {
												goBack();
											}
						
										}
									}
								});

							}.bind({}, fieldName, ui, obj, options)
						}
					}, sb.moduleId);
				
			}	
			
			if(showCreateBtn == true && hideButtons == false){
				
				ui.makeNode('new','div',{text:'<i class="plus icon"></i> New', css:'ui mini green icon button'})
					.notify('click', {
						type:'fields-run',
						data:{
							run:function(fieldName, ui, obj, options){
								
								sb.dom.alerts.ask({
									title: nameCreateLabel,
									text: '(You can edit the other information after you click save.)',
									icon: '',
									html: '<div class="ui input"><input id="create-entity-name" type="text"></div>',
									primaryButtonText: 		'Save',
									cancelButtonText: 		'Cancel'
								}, function(primaryButtonClicked){

									if (primaryButtonClicked) {
										
										swal.disableButtons();
										
										var entityName = $('#create-entity-name').val();
										
										var seed = {
												object_bp_type: 	options.set
												, parent: 			obj.id
												, name: entityName
												, tagged_with:[]
											};
											
										if(obj.tagged_with.length > 0){
											seed.tagged_with = obj.tagged_with;
										}
										
										seed.tagged_with.push(obj.id);
										
										if(options.templateSelection){
											
											sb.data.db.obj.runSteps(
												{
													'createEntity': {
														objectType: 		options.set
														, templates: 		[+options.templateSelection]
														, createTemplate: 	false
														, mergeVars: 		false
													}
												}
												, obj.id
												, function (response) {
													
													var newObj = response.msg.memo[0];
													
													newObj.name = seed.name;
													newObj.tagged_with = seed.tagged_with;
													
													sb.data.db.obj.update(options.set, newObj, function(created){
														 
														if(inPageCreate == true){
															
															singleObjectView(created, function(done){
																
																View(fieldName, ui, obj, options);
																
																sb.notify({
																	type: 'field-updated',
																	data:{
																		obj: obj
																		, type: 		'set-table'
																		, property: 	fieldName
																	}
																});
																
															});
															
														}else{
															
															View(fieldName, ui, obj, options);
															
															sb.notify({
																type: 'field-updated',
																data:{
																	obj: obj
																	, type: 		'set-table'
																	, property: 	fieldName
																}
															});
															
														}
																									
													});
													
													
												}
												, true // receive the memo in the response
											);
											
										}else{
											
											sb.data.db.obj.create(options.set, seed, function(created){
												 
												if(inPageCreate == true){
													
													singleObjectView(created, function(done){
														
														View(fieldName, ui, obj, options);
														
														sb.notify({
															type: 'field-updated',
															data:{
																obj: obj
																, type: 		'set-table'
																, property: 	fieldName
															}
														});
														
													});
													
												}else{
													
													View(fieldName, ui, obj, options);
													
													sb.notify({
														type: 'field-updated',
														data:{
															obj: obj
															, type: 		'set-table'
															, property: 	fieldName
														}
													});
													
												}
																							
											});
											
										}
																					
									} else {
		
										swal.close();
										
		
									}
		
								});

							}.bind({}, fieldName, ui, obj, options)
						}
					}, sb.moduleId);
				
			}
			
			if(hideButtons == false){
				
				ui.makeNode('unlink','div',{text:'<i class="trash icon"></i>', css:'ui mini red basic icon button'})
				.notify('click', {
					type:'fields-run',
					data:{
						run:function(fieldName, ui, obj, options){
							
							if(selectedItems.length == 0){
								
								sb.dom.alerts.alert('', 'Please select some items first.', 'warning');
								
							}else{
								
								var askSetup = {
										title: 'Remove link?',
										text: 'Unlinking just removes the link between these items. The items will NOT be deleted.',
										primaryButtonText: 		'Unlink',
										cancelButtonText: 		'Cancel'
									};
									
								if(options.allowDeletion == true){
									askSetup.title = 'Unlink or Delete?';
									askSetup.text = 'Unlinking just removes the link between these items. Click Delete to remove the item from the system completely.';
									askSetup.secondaryButtonText = 'Delete';
								}
								
								sb.dom.alerts.ask(askSetup, function(primaryButtonClicked, secondaryButtonClicked){

									if (primaryButtonClicked || secondaryButtonClicked) {
										
										var updateObj = [];
										_.each(selectedItems, function(selection){

											updateObj.push({
												id:selection.id,
												tagged_with:_.reject(selection.tagged_with, function(num){
													return num == obj.id;
												}),
												parent:0
											});
											
										});
										
										if(primaryButtonClicked){
											
											sb.data.db.obj.update(options.set, updateObj, function(updated){
												
												var newChildList = children;
												_.each(updateObj, function(remove){
													newChildList = _.reject(newChildList, function(child){
														return child.id == remove.id;
													});
												});
												
												if(options.tagParent == true){
													
													var parentUpdate = {
															id:obj.id,
															tagged_with:[]
														};
														
													var childIds = _.pluck(updateObj, 'id');
														
													_.each(obj.tagged_with, function(tag){
														
														if(childIds.indexOf(tag) == -1){
															
															parentUpdate.tagged_with.push(tag);
															
														}
														
													});

													sb.data.db.obj.update(obj.object_bp_type, parentUpdate, function(parentUpdated){
														
														View(fieldName, ui, obj, options, newChildList);
														
														sb.notify({
															type: 'field-updated',
															data:{
																obj: {
																	id: parentUpdated.id,
																	tagged_with:parentUpdated.tagged_with
																}
																, type: 		'tags'
																, property: 	'tagged_with'
															}
														});
														
													});
													
												}else{
													
													View(fieldName, ui, obj, options, newChildList);
													
												}
																								
											});
											
										}
										
										if(secondaryButtonClicked){
											
											var deleteArray = _.pluck(updateObj, 'id');
											
											sb.data.db.obj.erase(options.set, deleteArray, function(updated){
													
												var newChildList = children;
												_.each(updateObj, function(remove){
													newChildList = _.reject(newChildList, function(child){
														return child.id == remove.id;
													});
												});
												
												View(fieldName, ui, obj, options, newChildList);	
													
											});
											
										}
																					
									} else {
										
										swal.close();												
		
									}
		
								});
								
							}
							
							

						}.bind({}, fieldName, ui, obj, options)
					}
				}, sb.moduleId);
				
			}
			
			if (inCollection == true) {
							
				_.map(children, function(child) {
					
					var link = sb.data.url.createPageURL(
						'object'
						, {
							id: 		child.id
							, type: 	child.object_bp_type
							, name: 	child.name
						}
					);
					
					ui.makeNode('info'+child.id,'div',{text:'<nobr><a href="'+ link +'">'+ child.name +' <i class="ui external link icon fitted"></i></a></nobr>'});
					
				});
				
				ui.patch();
				
			} else {

				var contextWrapper = ui.makeNode('contextWrapper', 'div', {
					css: '',
					style: 'position:relative; margin-top:5px;'
				})
				
				var tableWrapper = contextWrapper.makeNode('tableWrapper', 'div', {
					css: 'responsive-horizontal'
				})
				
				var table = tableWrapper.makeNode('table', 'div', {
					css: 'ui striped celled compact sortable table',
					tag: 'table'
				});
				
				if(!options.visibleColumns){
					options.visibleColumns = [];
				}
				
				// if(options.groupingField){
				// 	
				// 	if(options.groupingValue){
				// 		
				// 		children = _.reject(children, function(child){
				// 			return child[options.groupingField] != options.groupingValue;
				// 		});
				// 		
				// 		children = _.reject(children, function(child){
				// 			return child[options.groupingField] != +options.groupingValue;
				// 		});
				// 		
				// 	}
				// 	
				// }

				var totalObj = {};
				_.each(options.visibleColumns, function(col){

					if(col != 'parent'){
						
						_.each(children, function(child){
							
							switch(childBP[col].fieldType){
								
								case 'currency':
								case 'duration':
								case 'formula':
								case 'int':
								case 'qty':
								case 'reference':
								case 'reference-calculation':

									
									if(!totalObj[col]){
										totalObj[col] = 0;
									}
									
									if(!isNaN(child[col])){
										totalObj[col] += child[col];
									}
	
									break;
									
								default:
																					
									if(childBP[col].options){
										
										if(
											childBP[col].options.displayAs == 'currency'
											|| childBP[col].options.displayAs == 'money'
											|| childBP[col].options.format == 'usd'
										){
																										
											if(!totalObj[col]){
												totalObj[col] = 0;
											}
											
											if(!isNaN(child[col])){
												totalObj[col] += child[col];
											}
																										
										}
										
									}
								
							}
							
						});
						
					}
					
				});

				table.makeNode('head','div',{tag:'thead'});
				table.head.makeNode('row','div',{tag:'tr'});
				
				table.head.row.makeNode('check','div',{tag:'th',id:'selectAll'+options._fieldId,css:'one wide center aligned no-sort',text:'<i class="circle outline icon"></i>'});

				var defaultSortColumnString = '';
				var columnName = '';
				if('name' == sortBySelect){
					defaultSortColumnString = 'default-sort';
				}
				
				if(options.visibleColumns.indexOf('name') == -1){
					table.head.row.makeNode('name','div',{tag:'th',css:defaultSortColumnString,text:nameLabel});
				}else{
					table.head.row.makeNode('name','div',{tag:'th',css:'one wide center aligned no-sort'});
				}
								
				_.each(options.visibleColumns, function(key) {
					
					defaultSortColumnString = '';
					if(key == sortBySelect){
						defaultSortColumnString = 'default-sort';
					}
					
					if(key == 'name'){
						columnName = nameLabel;
					}else{
						columnName = childBP[key].name
					}

					table.head.row.makeNode('row-'+key,'div',{tag:'th',css:defaultSortColumnString,text:columnName});
					
				});
				
				table.makeNode('body','div',{tag:'tbody'});	
				
				children = _.sortBy(children, sortBySelect);
				
				if (sortOrder == 'desc') {
					children.reverse();
				}
				
				_.each(children, function(child){
					
					if(inPageCreate != true){
						
						var link = sb.data.url.createPageURL(
							'object'
							, {
								id: 		child.id
								, type: 	child.object_bp_type
								, name: 	child.name
							}
						);
						
					}
					
					table.body.makeNode('row-'+child.id,'div',{tag:'tr'});
					
					table.body['row-'+child.id].makeNode('checkbox','div',{tag:'td',css:'center aligned'});							
					table.body['row-'+child.id].checkbox.makeNode('checkbox','div',{
						css:'item-selection'+options._fieldId,
						id:'item-selection'+options._fieldId+child.id,
						text:'<i class="circle outline icon"></i>',
						dataAttr:[
							{
								name:'id',
								value:child.id
							}
						]
					});
					
					if(options.visibleColumns.indexOf('name') > -1){
						
						table.body['row-'+child.id].makeNode('name','div',{tag:'td',css:'center aligned'});	
						
					}
					
					if (inPageCreate != true) {
						
						if(options.visibleColumns.indexOf('name') == -1){
						
							table.body['row-'+child.id].makeNode('name','div',{tag:'td',text:'<nobr><a href="'+ link +'">'+child.name +' <i class="ui external link icon fitted"></i></a></nobr>'});
						
						}else{
							
							table.body['row-'+child.id].makeNode('name','div',{tag:'td',text:'<nobr><a href="'+ link +'"><i class="ui external link icon fitted"></i></a></nobr>'});
							
						}
						
					} else {
						
						if(options.visibleColumns.indexOf('name') == -1){
						
							table.body['row-'+child.id].makeNode('name','div',{tag:'td',style:'cursor:pointer; text-decoration:underline;', text:'<i class="ui eye icon fitted"></i> '+child.name +''})
								.notify('click',{
									type:'fields-run',
									data:{
										run:function(child){
											
											singleObjectView(child, function(){
												
												View(fieldName, ui, obj, options);
												
												sb.notify({
													type: 'field-updated',
													data:{
														obj: obj
														, type: 		'set-table'
														, property: 	fieldName
													}
												});
												
												_.each(options.blueprint, function (field, key) {
													
													if (field.fieldType === 'date-rollup') {
														
														sb.data.db.obj.getById(obj.object_bp_type, obj.id, function(updated){
															
															sb.notify({
																type: 'field-updated'
																, data: {
																	obj: updated
																	, type: 'date-rollup'
																	, property: key
																}
															});
															
														}, 1, false, true);
					
													}
													
												});
												
											});
											
										}.bind({}, child)
									}
								}, sb.moduleId);
								
						}else{
							
							table.body['row-'+child.id].makeNode('name','div',{tag:'td',style:'cursor:pointer;', text:'<i class="ui eye icon"></i>'})
							.notify('click',{
								type:'fields-run',
								data:{
									run:function(child){
										
										singleObjectView(child, function(){
											
											View(fieldName, ui, obj, options);
											
											sb.notify({
												type: 'field-updated',
												data:{
													obj: obj
													, type: 		'set-table'
													, property: 	fieldName
												}
											});
											
											_.each(options.blueprint, function (field, key) {
												
												if (field.fieldType === 'date-rollup') {
													
													sb.data.db.obj.getById(obj.object_bp_type, obj.id, function(updated){
														
														sb.notify({
															type: 'field-updated'
															, data: {
																obj: updated
																, type: 'date-rollup'
																, property: key
															}
														});
														
													}, 1, false, true);
				
												}
												
											});
											
										});
										
									}.bind({}, child)
								}
							}, sb.moduleId);
							
						}
						
					}
					
					_.each(options.visibleColumns, function(key) {
						
						table.body['row-'+child.id].makeNode('row-'+key,'div',{tag:'td',text:''});
						
					});
												
				});	
								
				_.each(children, function(child) {
				
					_.each(options.visibleColumns, function(key) {
						
						var viewOptions = {
							blueprint:childBP
							, commitUpdates:true
							, edit:false
							, editing:false
							, inCollection:true
							, isGrouping:false
							, placeholder:'Empty'
						};
							
						var fieldSetup = {
							type: 		childBP[key].fieldType
							, property: key
							, obj: 		child
							, options: 	viewOptions
							, ui: 		table.body['row-'+child.id]['row-'+key]
						};

						switch(childBP[key].fieldType){
							
							case 'date':
																					
								if (childBP[key].name != 'Created On') {
									
									fieldSetup.options.edit = true;
									fieldSetup.options.mini = false;
									
								} else {
									
									fieldSetup.options.mini = false;
									
								}
							
								break;
							
							case 'currency':
							
								fieldSetup.options.editing = true;
							
								break;
							
							case 'int':
							case 'number':
							case 'select':
							case 'state':
							case 'plain-text':
							
								fieldSetup.options.edit = true;
								fieldSetup.options.editing = true;
							
								break;
							
						}

						sb.notify({
							type: 'view-field',
							data: fieldSetup
						});
						
					});
				
				});
				
				table.makeNode('foot','div',{tag:'tfoot'});
				table.foot.makeNode('row','div',{tag:'tr'});
				table.foot.row.makeNode('select','div',{tag:'th',text:''});	
				table.foot.row.makeNode('name','div',{tag:'th',text:''});

				_.each(options.visibleColumns, function(key) {
					
					if(totalObj[key]){
						
						table.foot.row.makeNode('row-'+key,'div',{tag:'th',text:'$ '+(totalObj[key]/100).formatMoney()});
						
					}else{
						
						table.foot.row.makeNode('row-'+key,'div',{tag:'th',text:''});
						
					}
					
				});
				
				ui.patch();
				
				$('table').tablesort();
				//$('table.sortable').tablesort().data('tablesort').sort($("th.default-sort"));
				
				$('#selectAll'+options._fieldId).on('click', function() {
												
					if(selectedItems.length == 0){
						
						// select all
						selectedItems = children;
						
						$('#selectAll'+options._fieldId).html('<i class="circle icon"></i>');
						$('.item-selection'+options._fieldId).html('<i class="circle icon"></i>');
						
					}else{
						
						// deselect all
						selectedItems = [];
						$('#selectAll'+options._fieldId).html('<i class="circle outline icon"></i>');
						$('.item-selection'+options._fieldId).html('<i class="circle outline icon"></i>');
						
					}
					
				});
				
				$('.item-selection'+options._fieldId).on('click', function(e){
													
					var selectedId = $(this).data('id');	
						
					var alreadySelected = _.findWhere(selectedItems, {id:selectedId});

					if (alreadySelected) {

						// deselect single item
						selectedItems = _.reject(selectedItems, function(child){
							return child.id == alreadySelected.id;
						});

						$('#item-selection'+options._fieldId+selectedId).html('<i class="circle outline icon"></i>');
						
					} else {
						
						// select single item
						selectedItems.push(_.findWhere(children, {id:selectedId}));
						$('#item-selection'+options._fieldId+selectedId).html('<i class="circle icon"></i>');
						
					}
					
				});
				
			}
			
		}

		function getData(callback) {
			
			var where = {
				parent:obj.id,
				select: {
					name:true,
					tagged_with:true
				}
			};
			if(options.searchByTag){
				delete where.parent;
				where.tagged_with = [obj.id];
			}
			
			_.each(options.visibleColumns, function(key){
				where.select[key] = true;
			});
			
			if(options.groupingField){
				if(options.groupingValue){
					where.select[options.groupingField] = true;
				}
			}

			sb.data.db.obj.getWhere(options.set, where, function(children){
				
				callback(children);
				
			}, true);
			
		}
		
		function getWorkflows (blueprint, onComplete) {
	
			var workflowIds = [];
			_.each(blueprint, function (property, key) {
	
				if (
					property.fieldType === 'state'
					&& parseInt(property.workflow)
					&& !property.is_archived
				) {
	
					workflowIds.push(parseInt(property.workflow));
	
				}
	
			});
	
			sb.data.db.obj.getById(
				'entity_workflow'
				, workflowIds
				, function (workflows) {

					_.each(blueprint, function (property, key) {
	
						if (
							property.fieldType === 'state'
							&& !property.is_archived
						) {
	
							var wf = _.findWhere(workflows, {id: parseInt(property.workflow)});
	
							if (!_.isEmpty(wf)) {
	
								property.workflow = wf;
	
							}
	
						}
	
					});

					onComplete(blueprint);
	
				}
			);
	
		}
		
		function singleObjectView(obj, callback){
			
			ui.empty();
			
			ui.makeNode('cont','div',{css:'ui clearing blue segment'});
			
			ui.cont.makeNode('buttons','div',{css:'ui mini buttons'});
			ui.cont.buttons.makeNode('back','div',{css:'ui button set-table-backBtn',text:'Back'});
			ui.cont.buttons.makeNode('save','div',{css:'ui green button set-table-backBtn',text:'Save/Done'});
			
			ui.cont.makeNode('singleView','div',{css:'ui clearing basic segment'});
			
			ui.cont.makeNode('bottomButtons','div',{css:'ui mini buttons'});
			ui.cont.bottomButtons.makeNode('back','div',{css:'ui button set-table-backBtn',text:'Back'});
			ui.cont.bottomButtons.makeNode('save','div',{css:'ui green button set-table-backBtn',text:'Save/Done'});
			
			ui.patch();
			
			sb.notify({
				type: 'view-entity'
				, data: {
					ui:     ui.cont.singleView
					, id:   obj.id
				}
			});
			
			$('.set-table-backBtn').on('click', function(){
				
				// Give fields a chance to run their blur/unfocus 
				// updates before changing the tab.
				$('input').blur();
				$('textarea').blur();

				// Set visual loading state
				$('.set-table-backBtn').addClass('loading');
				
				setTimeout(function(){
					
					callback(true);
					
				}, 1000);
				
			});
			
		}
	
		if(options && fieldName != '_default'){
			
			if(options.set){
				
				var selectedItems = [];
				var hideButtons = false;
				var showSearchButton = true;
				var inCollection = false;
				var sortBySelect = false;
				var sortOrder = 'asc';
				var inPageCreate = false;
				var searchByTag = true;
				
				if(cachedChildren){
					children = cachedChildren;
				}
				
				var showCreateBtn = true;
				
				if(options.hideCreateBtn === true){
					
					showCreateBtn = false;
					
				}
				
				if(options.inPageCreate){
					inPageCreate = options.inPageCreate;
				}
				
				if(searchByTag == true){
					options.searchByTag = true;
				}
				
				if(options.inCollection){
					if(options.inCollection == true){
						hideButtons = true;
						inCollection = true;
					}
				}
				
				if(options.hideButtons){
					if(options.hideButtons === true){
						hideButtons = true;
					}
				}
				
				if(options.sortColumn){
					sortBySelect = options.sortColumn[0];
				}else{
					sortBySelect = 'name';
				}
				
				if(options.sortOrder){
					sortOrder = options.sortOrder;
				}
				
				if(inCollection == true){
					
					ui.makeNode('load','div',{css:'ui icon button', text:'<nobr><i class="ui eye icon"></i> View '+ options.blueprint[fieldName].name +'</nobr>'})
						.notify('click',{
							type:'fields-run',
							data:{
								run:function(){
									
									sb.notify({
										type: 	'get-sys-modal'
										, data: 	{
											callback: 	function (modal) {
												
												modal.body.empty();
												modal.show();
																										
												options.inCollection = false;
												options.inPageCreate = true;
												
												modal.body.makeNode('title','div',{css:'ui huge header',text:options.blueprint[fieldName].name +' for '+ obj.name});
												
												modal.body.makeNode('tableCont','div',{css:'ui segment'});
												
												modal.body.patch();
												
												View(fieldName, modal.body.tableCont, obj, options);
												
											}
										}
									});
									
								}
							}
						},sb.moduleId);
					
					ui.patch();
					
				}else{
					
					ui.makeNode('cont','div',{css:'ui center aligned basic segment'});
					ui.cont.makeNode('loading','div',{text:'<i class="notched circle loading icon"></i> Loading table...'});
					ui.patch();
					
					sb.data.db.obj.getBlueprint(options.set, function(childBP){
						
						getWorkflows(childBP, function(fullBlueprint){
							
							childBP = fullBlueprint;
							
							getData(function(children){
								
								if(options.onlyOneItem == true && children.length > 0){
									
									showCreateBtn = false;
									showSearchButton = false;
									
								}
								
								buildTable(children, childBP);
								
							});
							
						});
											
					});
					
				}
				
			}
			
		}
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'set-table',
					view: View,
					title: 'Set Table',
					type: 'Link to Other Sets',
					availableToEntities: true,
					icon: 'table',
					options: {
						set: {
							name: 'Select a data set (required)',
							type: 'objectType'
						}
						, visibleColumns: {
							name: 	'Show column(s) in table'
							, type: 'field'
							, blueprint: 'set'
							, allowedTypes: ['any']
						}
						, sortColumn: {
							name: 	'Default Sort Column'
							, type: 'field'
							, blueprint: 'set'
							, allowedTypes: ['any']
						}
						, templateSelection: {
							name: 	'Use a template?'
							, type: 'string'
						}
						, sortOrder: {
							name: 	'Sort Order'
							, type: 'select'
							, options: [
								{
									name:'Ascending',
									value:'asc'
								},
								{
									name:"Descending",
									value:'desc'
								}
							]
						}
						, onlyOneItem: {
							name: 	'Allow only one item?'
							, type: 'bool'
						}
						, hideCreateBtn: {
							name: 	'Hide the create new button?'
							, type: 'bool'
						}
						, canSearch: {
							name: 	'Can the user search for records?'
							, type: 'bool'
						}
						, searchByTag: {
							name: 	'Search By Tag'
							, type: 'bool'
						}
						, searchTags: {
							name: 	'Restrict search to these tags'
							, type: 'tags'
						}
						, tagParent: {
							name: 	'Tag the parent?'
							, type: 'bool'
						}
						, searchCompanyOnly: {
							name: 	'Restrict search to parent company?'
							, type: 'bool'
						}
						, allowDeletion: {
							name: 	'Allow deletion of related items?'
							, type: 'bool'
						}
						, nameLabel: {
							name: 	'Name Label'
							, type: 'string'
						}
						, nameCreateLabel: {
							name: 	'New item name label'
							, type: 'string'
						}
						, inPageCreate: {
							name: 	'Use the in-page create flow?'
							, type: 'bool'
						}
						, groupingField: {
							name: 	'Group By Field'
							, type: 'field'
							, blueprint: 'set'
							, allowedTypes: ['any']
						}
						, groupingValue: {
							name: 	'Grouping Value (if applicable)'
							, type: 'string'
						}
						
					}
				}
			});
			
		}
		
	}
	
});