var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('priority-view', function (sb) {

	var Levels = [
		{
			name: 'Lowest'
			, icon: 'arrow down'
			, color: 'green'
			, value: 0
		}
		, {
			name: 'Low'
			, icon: 'arrow down'
			, color: 'olive'
			, value: 1
		}
		, {
			name: 'Medium'
			, icon: 'arrow up'
			, color: 'yellow'
			, value: 2
		}
		, {
			name: 'High'
			, icon: 'arrow up'
			, color: 'orange'
			, value: 3
		}
		, {
			name: 'Highest'
			, icon: 'arrow up'
			, color: 'red'
			, value: 4
		}
	];
	
	function getDisplayHTML (priority, options) {

		if (options.inCollection) {
			
			return '<i class="ui '+ priority.color +' '+ priority.icon +' icon"></i>';
			
		} else {
			
			return '<i class="ui '+ priority.color +' '+ priority.icon +' icon"></i> '+ priority.name;
			
		}
		
	}
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			obj[fieldName] = obj[fieldName] ? obj[fieldName] : 0;

			var priority = _.findWhere(Levels, {value: parseInt(obj[fieldName])});
			var ret = getDisplayHTML(priority, options);

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;
			
		}

		function draw (ui, object, options) {
			
			var priority = _.findWhere(Levels, {value: parseInt(obj[fieldName])});
			var css = 'ui floating dropdown search fluid basic selection edge-field';
			if (options.inCollection || options.mini) {
				css = '';
			}
			
			ui.makeNode('disp', 'div', {
				text: getDisplayHTML(priority, options),
				css: css,
				style: 'border: 0px;',
			});	
			
		}
		
		if (!obj[fieldName] || obj[fieldName] > 4 || typeof obj[fieldName] !== 'number') {
			obj[fieldName] = 0;
		}
		
		draw(ui, obj[fieldName], options);
		
		ui.disp.listeners.push(
			function (selector) {

				$(selector).on('click', function(){
					
					if (options.edit) {

						Edit(
							fieldName
							, ui
							, obj
							, options
						);					
					
					}
					
				});
				
			}
		);				
		
	}
	
	function Edit (fieldName, ui, obj, options) {

		if (typeof options.onEditStart === 'function')
			options.onEditStart();
			
		var where = '';
		var currentVal = false;
		if (obj && obj[fieldName]) {
			currentVal = _.findWhere(Levels, {value: parseInt(obj[fieldName])});
		}
		if (!currentVal) {
			currentVal = _.findWhere(Levels, {value: 0});
		}
		
		var typeOptions = [{
				value: 		currentVal.value
				, name: 		currentVal.name
				, selected: 	true
			}];

		ui.empty();
		var selected = _.findWhere(Levels, {value: parseInt(obj[fieldName])});
		var searchNode = ui.makeNode('select', 'div', {
				css: 'ui floating dropdown search fluid basic selection edge-field',
				style: 'border: 0px;',
				text: '<div class="text">'+ getDisplayHTML(selected, options) +'</div> <i class="dropdown icon"></i>',
				listener: {
					type: 'dropdown',
					onShow() {

						setTimeout(function() {
							$('.floater-container').css({'overflow':'visible'});
						}, 100);
							
					},
					onHide() {
	
						setTimeout(function() {
							$('.floater-container').css({'overflow-y':'scroll', 'overflow-x':'auto'});
						}, 100);
							
					},
					onChange: function(value){
						
						function getChangeNote (options, onComplete) {

							if (options.requireChangeNote) {

								var askSetup = {
									title: 'Reason for change:'
									, text: ''
									, input: 'text'
									, primaryButtonText: 'Change'
									, cancelButtonText: 'Cancel'
								};

								sb.dom.alerts.ask(askSetup, function (primaryButtonClicked, secondaryButtonClicked, note) {

									if (!_.isEmpty(note)) {

										swal.disableButtons();
										onComplete(note);
										swal.close();

									} else {

										ui.empty();
										View(fieldName, ui, obj, options);
										ui.patch();

									}

								});


							} else {

								onComplete(false);

							}

						}

						function updateField(obj, fieldName, options, updates) {

							obj[fieldName] = updates[fieldName];
							ui.empty();
							
							if (typeof options.onEditStart === 'function') {
								options.onEditEnd();											
							}
								
							$(searchNode.selector).dropdown('destroy');
							View(fieldName, ui, obj, options);
							ui.patch();
							
							if (options.commitUpdates) {

								if (typeof options.onUpdate === 'function') {
									options.onUpdate(obj, updates);
								}
								
								sb.notify({
									type: 'field-updated',
									data: {
										obj: updates,
										type: 'priority',
										property: fieldName
									}
								});

							}

						}

						if (!_.isEmpty(value) && value != obj[fieldName]) {
							
							if (options.commitUpdates === false) {

								obj[fieldName] = parseInt(value);
								$(searchNode.selector).dropdown('destroy');

								setTimeout(1, function () {

									ui.empty();
									View(fieldName, ui, obj, options);
									ui.patch();

								})
								
							} else {
			
								getChangeNote(options, function (note) {

									var steps = {
										'setPriority': {
											fieldKey: fieldName,
											setTo: value,
											note: note,
										}
											
									};

									if (typeof options.runSteps === 'function') {

										options.runSteps(steps, obj.id, function(updates) {

											updateField(obj, fieldName, options, updates);

										});

									} else {

										sb.data.db.obj.runSteps(steps, obj.id, function (updates) {
		
												updateField(obj, fieldName, options, updates);
												
											}
										);

									}

								});
								
							}
														
						}
						
					}
				},
				placeholder: 'Priority'
			}).makeNode(
				'menu'
				, 'div'
				, {
					css: 'menu'
				}
			);	
		
		_.chain(Levels)
			.sortBy(function (lvl) {
				return -(lvl.value);
			})		
			.each(function (level) {
					
				searchNode
					.makeNode('p'+ level.value, 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'value'
								, value: level.value
							}
						]
					})
					.makeNode('txt', 'div', {
						css: 'text'
						, tag: 'span'
						, text: getDisplayHTML(level, {})
					});
								
			});
		
		ui.select.listeners.push(function (selector) {
			
			$(selector).dropdown('show');
						
		});
		
		ui.patch();
		
	}

	return {

		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'priority'
					, title: 'Rating'
					, view: View
					, icon: 'star outline'
					, propertyType: 'int'
					, availableToEntities: true
					, detail: function (fieldName, obj, options) {
						
						var fieldLabel = '';
						if (
							options
							&& (options.mini || options.inCollection)
							&& typeof obj.object_bp_type === 'string'
						) {

							var bp = _.findWhere(appConfig.Types, {bp_name: obj.object_bp_type.substr(1)});
							if (bp && bp.blueprint && bp.blueprint[fieldName]) {
								fieldLabel = bp.blueprint[fieldName].name;
							}
							return fieldLabel;

						}
						
						return false;
						
					}
					, options: {
						requireChangeNote: {
							type: 'bool'
							, name: 'Require change note?'
						}
					}
				}
			});

			if (IN_NODE_ENV) {
				var priority = View;
				module.exports = { 
					priority
				}
			}
			
		}

	}

});

if (IN_NODE_ENV) {
	Factory.startAll();
}