var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('field', function (sb) {

	var Types = [];
	appConfig.Fields = Types;
	var Nodes = [];
	var DefaultTypeOptions = {
		_sectionName: {
			name: 	'Section name (appears as header above description)'
			, type: 'detail'
			, configType: 'view'
		}
		, _message: {
			name: 	'Description (appears as text before the field.)'
			, type: 'detail'
			, showToolbar: true
			, configType: 'view'
		}
		, _description: {
			name: 	'Helper text (appears on hover)'
			, type: 'text'
			, configType: 'view'
		}
		, _breakAfter: {
			name: 	'Break after?'
			, type: 'bool'
			, configType: 'view'
		}
		, _default: {
			name: 			'Default value'
			, type: 		'currentType'
			, is_template: 	true
		}
		, _labelPos: {
			name: 			'Label position'
			, type: 		'select'
			, options: 		[
				{
					name: 'Left'
					, value: 'left'
				}
				, {
					name: 'Above'
					, value: 'above'
				}
				, {
					name: 'Hidden'
					, value: 'hidden'
				}
			]
			, configType: 		'view'
		}
	};

	// App
	var main = false;
	var document = document || '';

	function getOption(option, options, fieldName) {

		var option = _.findWhere(options.blueprint[fieldName].options.options, {
			value: option
		});

		return option;

	}

	function getOptions(options, fieldName) {

		var opts = options.blueprint[fieldName].options;

		if (_.isEmpty(options.blueprint[fieldName].options)) {
			options.blueprint[fieldName].options = {};
		}

		if (!Array.isArray(opts.options)) {
			opts.options = [];
		}

		return opts;

	}

	function updateOption(ui, obj, option, options, fieldName, view) {

		var View = view;

		options.updateField(
			options.blueprint[fieldName],
			function (updatedField) {

				options.blueprint[fieldName] = updatedField;
				ui.empty();
				View(
					fieldName
					, ui
					, obj
					, options
				);
				ui.patch();

			}
		);

	}

	function showEditOptionModal(ui, obj, option, options, fieldName, view) {

		// Define globals
		var BlueprintName = '';

		function triggerFieldSelect(ui) {

			var fieldSelectContainer = ui.fieldSelectContainer;

			sb.data.db.obj.getBlueprint('#' + BlueprintName, function(blueprint) {

				// Select a Field
				var fieldSelectPlaceholder = 'Find a field...';
				if (option) {
					if ( option.hasOwnProperty('associated') ) {
						if (option.associated) {
							if ( option.associated.hasOwnProperty('field') ) {
								if (option.associated.field) {
									if ( option.associated.field.hasOwnProperty('id') ) {
										if (option.associated.field.id) {
											fieldSelectPlaceholder = option.associated.field.id;
										}
									}
								}
							}
						}
					}
				}
				fieldSelectContainer.makeNode('label', 'div', {
					text: '<strong>Find a field:</strong>'
				});

				sb.notify({
					type: 'view-field-selection',
					data: {
						ui: fieldSelectContainer.makeNode('fieldSelect', 'div', {}),
						blueprint: blueprint,
						options: {
							allowedTypes: ['any'],
							onUpdate: function (result, response) {

								_.findWhere(options.blueprint[fieldName].options.options, {
									value: option.value
								}).associated.field = {
									id: result,
									type: blueprint[result].fieldType
								};

							},
							value: fieldSelectPlaceholder
						},
						state: {}
					}
				});

				fieldSelectContainer.patch();

				$(fieldSelectContainer.selector).show();

			});

		}

		function triggerRecordSelect(ui) {

			var recordSelectContainer = ui.recordSelectContainer;

			// Select a Record
			var recordSelectPlaceholder = 'Find a record..';
			recordSelectContainer.makeNode('label', 'div', {
				text: '<strong>Find a record:</strong>'
			});
			recordSelectContainer.makeNode('recordSelect', 'div', {
				css: 'ui fluid category search',
				text:
					'<div class="ui left icon fluid input">'+
					'<input class="prompt" type="text" placeholder="'+ recordSelectPlaceholder +'">'+
					'<i class="search icon"></i>'+
					'</div>'+
					'<div class="results"></div>',
				listener: {
					type: 'search',
					objectType: encodeURIComponent('#' + BlueprintName),
					onSelect: 	function(result, response) {

						_.findWhere(options.blueprint[fieldName].options.options, {
							value: option.value
						}).associated.record = {
							id: result.id
						};

						triggerFieldSelect(ui);

					},
					selection: {
						name: true,
						icon: true,
						bp_name: true
					}
				}
			});

			recordSelectContainer.patch();

			$(recordSelectContainer.selector).show();
			$(fieldSelectContainer.selector).hide();

			if (option) {
				if ( option.hasOwnProperty('associated') ) {
					if (option.associated) {
						if ( option.associated.hasOwnProperty('record') ) {
							if (option.associated.record) {
								if ( option.associated.record.hasOwnProperty('id') ) {
									if (option.associated.record.id) {

										// Get selected value
										sb.data.db.obj.getById('', option.associated.record.id, function(record) {

											if (record.name) {

												$(recordSelectContainer.recordSelect.selector).search('set value', record.name);

												triggerFieldSelect(ui);

											}

										});

									}
								}
							}
						}
					}
				}
			}

		}

		function triggerSetSelect(ui) {

			var setSelectContainer = ui.setSelectContainer;

			var setSelectPlaceholder = 'Find a set..';
			setSelectContainer.makeNode('label', 'div', {
				text: '<strong>Find a set:</strong>'
			});
			setSelectContainer.makeNode('setSelect', 'div', {
				css: 'ui fluid category search',
				text:
					'<div class="ui left icon fluid input">'+
					'<input class="prompt" type="text" placeholder="'+ setSelectPlaceholder +'">'+
					'<i class="search icon"></i>'+
					'</div>'+
					'<div class="results"></div>',
				listener: {
					type: 'search',
					objectType: 'entity_type',
					onSelect: function(result, response) {

						BlueprintName = result.bp_name;

						_.findWhere(options.blueprint[fieldName].options.options, {
							value: option.value
						}).associated = {
							entity: {
								bp_name: BlueprintName
							}
						};

						triggerRecordSelect(ui);

					},
					selection: {
						name: true,
						icon: true,
						bp_name: true
					}
				}
			});

			setSelectContainer.patch();

			if (option) {
				if ( option.hasOwnProperty('associated') ) {
					if (option.associated) {
						if ( option.associated.hasOwnProperty('entity') ) {
							if (option.associated.entity) {
								if ( option.associated.entity.hasOwnProperty('bp_name') ) {
									if (option.associated.entity.bp_name) {

										// Get selected value
										var selectedVal = _.findWhere(appConfig.Types, {bp_name: option.associated.entity.bp_name});

										if (selectedVal) {

											// Set selected value
											setSelectContainer.setSelect.search('set value', selectedVal.name);

											BlueprintName = option.associated.entity.bp_name;

											triggerRecordSelect(ui);

										}

									}
								}
							}
						}
					}
				}
			}

		}

		// Define option
		var option = getOption(option, options, fieldName);

		if (!option) {
			option = {
				name: ''
			}
		}

		// Create mobal
		ui.makeNode('modal', 'modal', {});

		// Patch the UI
		ui.patch();

		// Define the modal
		var modal = ui.modal;

		// Create the header
		modal.body.makeNode('header', 'div', {
			text: '<h1>Edit Option</h1>',
			style: 'margin-bottom:15px;'
		});

		// Option name
		var optionNameContainer = modal.body.makeNode('optionNameContainer', 'div', {
			style: 'margin-bottom:30px;'
		});
		optionNameContainer.makeNode('header', 'div', {
			text: '<h3>Option Value</h3>',
			style: 'margin-bottom:10px;'
		});
		optionNameContainer.makeNode('label', 'div', {
			text: '<strong>Name:</strong>'
		});
		sb.notify ({
			type: 'view-field',
			data: {
				type: 'plain-text',
				property: 'name',
				obj: option,
				options: {
					edit: true,
					editing: true,
					commitChanges: false,
					title: 'Option Name'
				},
				ui: optionNameContainer.makeNode('optionName', 'div', {
					css: 'field-value round-border'
				})
			}
		});

		var associatedValueContainer = modal.body.makeNode('associatedValueContainer', 'div', {
			style: 'margin-bottom:30px;'
		});
		associatedValueContainer.makeNode('header', 'div', {
			text: '<h3>Associated Value</h3>',
			style: 'margin-bottom:10px;'
		});
		var setSelectContainer = associatedValueContainer.makeNode('setSelectContainer', 'div', {
			style: 'margin-bottom:15px;'
		});
		var recordSelectContainer = associatedValueContainer.makeNode('recordSelectContainer', 'div', {
			style: 'margin-bottom:15px; display:none;'
		});
		var fieldSelectContainer = associatedValueContainer.makeNode('fieldSelectContainer', 'div', {
			style: 'margin-bottom:15px; display:none;'
		});

		// Save button
		modal.body.makeNode('saveBtn', 'div', {
			css: 'ui button green pull-right',
			style: 'margin-top:15px; margin-bottom:15px;',
			text: 'Save Changes'
		}).notify('click', {
			type:'contracts-run',
			data:{
				run:function() {

					var name = $(optionNameContainer.optionName.selector + ' input').val();
					var opts = getOptions(options, fieldName);

					// Create a new option
					if (!option.value) {

						opts.options.push({
							name: name,
							value: opts.options.length + 1
						});

						// Edit an option
					} else {

						_.findWhere(opts.options, {
							value: option.value
						}).name = name;

					}

					updateOption(ui, obj, option, options, fieldName, view);

					// Hide modal
					modal.hide();

				}

			}

		});

		// Cancel button
		modal.body.makeNode('cancelBtn', 'div', {
			css: 'ui button grey pull-right',
			style: 'margin-top:15px; margin-bottom:15px;',
			text: 'Cancel'
		}).notify('click', {
			type:'contracts-run',
			data:{
				run:function() {

					// Hide modal
					modal.hide();

				}.bind({})
			}
		}, sb.moduleId);

		// Clear float
		modal.body.makeNode('clear', 'div', {
			style: 'clear:both;',
		});

		modal.patch();

		// Select a Set
		triggerSetSelect(associatedValueContainer);

		modal.show();

	}

	function removeOption(ui, obj, option, options, fieldName, view) {

		// Define option
		var option = getOption(option, options, fieldName);

		sb.dom.alerts.ask({
			title: 	'Archive '+ option.name +'?'
			, text: ''
		}, function (response) {

			if (response) {

				swal.disableButtons();

				var opts = getOptions(options, fieldName);

				_.findWhere(opts.options, {
					value: option.value
				}).is_archived = true;

				updateOption(ui, obj, option, options, fieldName, view);

			} else {

				swal.close();

			}

		});


	}

	function getFieldMergeTags (set, opts, memo) {

		var tags = [];

		_.each(appConfig.Types, function (type) {

			_.each(type.blueprint, function (field, key) {

				if (
					!_.contains(opts.allowedTypes, field.type)
					&& !_.contains(opts.allowedTypes, field.fieldType)
					&& !_.contains(opts.allowedTypes, field.objectType)
					&& !_.contains(opts.allowedTypes, 'any')
				) {
					return;
				}

				if (
					// Don't show archived fields
					!field.is_archived
					// Don't show multi fields if allowMulti option is
					// set and is set to false
					&& !(
						!_.isEmpty(opts)
						&& opts.hasOwnProperty('allowMulti')
						&& opts.allowMulti === false
						&& !_.isEmpty(field)
						&& !_.isEmpty(field.options)
						&& field.options.multi === true
					)
				) {

					var isOnContext = (opts.context == type.bp_name);

					tags.push({
						name: 'this.parent.#'+ type.name +'.'+ field.name,
						value: 'this.parent.#'+ type.bp_name.replace(/\./g, '-') +'.'+ key,
						display: '<span style="display:inherit; border-bottom:1px solid lightgray; padding-bottom:4px;">Field (<b><i>' + field.name + '</i></b>) on <i>last updated</i> (<b><i>' + type.name + '</i></b>) record from <i>parent context</i>.</span></br><span style="display:block; margin-top:6px;"><i>{{this.parent.#'+ type.name +'.'+ field.name + '}}</i></span>',
						bpName: type.bp_name,
						typeName: type.name,
						fieldName: field.name,
						type: 'associated',
						isOnContext: isOnContext,
						sortString: opts.context == 0 ? type.name + 1 +  '-' + field.name : !isOnContext + '-' + 1 + '-' + type.name + '-' + field.name
					});

					// Merge in properties on the parent obj
					if (opts.parentSet === 'ANY') {

						tags.push({
							name: 'this.parent.('+ type.name +').'+ field.name,
							value: 'this.parent.('+ type.bp_name.replace(/\./g, '-') +').'+ key,
							display: '<span style="display:inherit; border-bottom:1px solid lightgray; padding-bottom:4px;">Field (<b><i>' + field.name + '</i></b>) on parent (<b><i>' + type.name + '</i></b>) record.</span></br><span style="display:block; margin-top:6px;"><i>{{this.Parent ('+ type.name +').'+ field.name + '}}</i></span>',
							bpName: type.bp_name,
							typeName: type.name,
							fieldName: field.name,
							type: 'parent',
							isOnContext: isOnContext,
							sortString: opts.context == 0 ? type.name + 0 + '-' + field.name : !isOnContext + '-' + 0 + '-' + type.name + '-' + field.name
						});

					}

					//!TODO: Make this recursive, looking at child blueprint
					// if the field is an edge.

				}

			});

		});

		return _.sortBy(tags, 'sortString');

	}

	function setListeners () {

		function runEvt (evt) {

			function openEdit (node, type) {

				var editBoxId = sb.dom.randomString(6, '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ');

				function isOutOfViewport(element) {
					// Get the dom element
					// element = document.querySelector(element);

					// Get element's bounding
					var bounding = element.getBoundingClientRect();

					// Check if it's out of the viewport on each side
					var out = {};
					out.top = bounding.top < 0;
					out.left = bounding.left < 0;
					out.bottom = bounding.bottom > (window.innerHeight || document.documentElement.clientHeight);
					out.right = bounding.right > (window.innerWidth || document.documentElement.clientWidth);
					out.any = out.top || out.left || out.bottom || out.right;
					out.all = out.top && out.left && out.bottom && out.right;

					return out;

				}

				function setPopupPosition() {

					function getOffset(el) {

						var rect = el.getBoundingClientRect(),
							scrollLeft = window.pageXOffset || document.documentElement.scrollLeft,
							scrollTop = window.pageYOffset || document.documentElement.scrollTop;
						return {
							top: rect.top + scrollTop
							, left: rect.left + scrollLeft
						}

					}

					if ( node && node._id ) {

						try {
							var el = document.getElementById(node._id).parentNode;

							var pos = getOffset(el);
							var height = el.offsetHeight;
							var width = el.offsetWidth;

							var isOut = isOutOfViewport(el);
							// var rightX = 'auto';

							// if (isOut.right) {
							//     leftX = 'auto';
							//     rightX = $(window).width() - pos.left - 30;
							//     arrowFloatAddClass = 'arrow-float-right';
							//     arrowFloatRemoveClass = 'arrow-float-left';
							//     $(el).attr('data-right-floated', rightX);
							// }


							if (type.options && type.options.popupSize) {

								var popup = type.options.popupSize;

								if ( popup.width && popup.width > width )
									width = type.options.popupSize.width;

								if ( popup.height && popup.height > height )
									height = type.options.popupSize.height;
							}

							$('#' + editBoxId).css(
								{
									'top': pos.top + 'px'
									, 'left': pos.left + 'px'
									, 'height': height + 'px'
									, 'min-height': height + 'px'
									, 'max-height': height + 'px'
									, 'width': width + 'px'
									, 'min-width': width + 'px'
									, 'max-width': width + 'px'
								}
							);
						} catch (error) {
							console.log('error', error);
						}



					}


				}
				if(node.options.edit == false)
					return;

				setTimeout(function() {

					$('.field-edit-pop').remove();
					$('body').append(
						'<div class="field-edit-pop" id="'+ editBoxId +'" '+
						'style="'+
						'position:absolute;'+
						'border-radius:4px;'+
						'background-color:rgb(245, 245, 245) !important;'+
						'box-shadow:0 0 20px rgba(0,0,0,0.3);'+
						'z-index:998;'+
						'">'+
						'<div class="field-edit-pop-div-c" style="height:100%;"></div>'+
						'</div>'
					);

					var pop = sb.dom.make('.field-edit-pop').makeNode('c', 'div', {});

					node.options.inputHeight = '100%';
					// node.options.inputContainerStyle = 'height:100%;';

					type.edit(
						node.property
						, pop
						, node.obj
						, node.options
					);

					pop.patch();

					if (typeof type.focus === 'function') {

						setTimeout(function () {

							type.focus(
								node.property
								, pop
								, node.obj
								, node.options
							);

						}, 1);

					}

					// Set position when resizing the screen
					$(window).on('resize', function() {
						setPopupPosition();
					});

					// Set position when scrolling
					window.addEventListener('scroll', function() {
						setPopupPosition();
					}, true);

					// Set position on initialization
					setPopupPosition();

					$(document).off('mouseup');

					// Hide when clicked out of
					$(document).mouseup(function(e){
						var container = $('.field-edit-pop');
						if(!container.is(e.target) && container.has(e.target).length === 0){


							if ( type.focusout && typeof type.focusout == 'function') {

								type.focusout(
									node.property
									, pop
									, node.obj
									, node.options
								);
							}
							container.hide();
						}
					});

				}, 1);

				return;

			}

			function getFieldId (node, i) {

				if (!i) {
					i = 1;
				}
				var fieldId = node.getAttribute('field-id');
				if (
					_.isEmpty(fieldId)
					&& node.parentElement
					&& i < 10
				) {

					fieldId = getFieldId(node.parentElement, i + 1);

				}

				return fieldId;

			}

			var fieldId = getFieldId(evt.target);

			if (!_.isEmpty(fieldId)) {

				// Get node
				var node = _.findWhere(Nodes, {
					_id: fieldId
				});

				if (node) {

					// Get field type
					var type = _.findWhere(
						Types
						, {
							name: node.type
						}
					);
					var useFieldListener = type.useFieldListener;

					if ( node.options && node.options.hasOwnProperty('useFieldListener') ) {
						useFieldListener = node.options.useFieldListener;
					}

					// Enter edit mode
					if (
						typeof type.edit === 'function'
						&& useFieldListener
						&& !node.options.editing
// 						&& node.options.edit !== false
					) {

						if (type.usePopup) {

							openEdit(node, type);

						} else {

							type.edit(
								node.property
								, node.ui.f
								, node.obj
								, node.options
							);

							if (typeof type.focus === 'function') {

								setTimeout(function () {

									type.focus(
										node.property
										, node.ui.f
										, node.obj
										, node.options
									);

								}, 1);

							}

						}

					}

				}

			}

		}

		if (
			main === false
			&& document
		) {

			main = document.querySelector("#mainContainerArea");
			main.removeEventListener('click', runEvt);
			main.addEventListener(
				'click'
				, runEvt
				, false
			);

		}

	}

	var updateListeners = _.throttle(
		setListeners
		, 100
		, {
			leading: false
		}
	);

	function commitBlueprintChange (entityType, onComplete) {

		var bpUpdate = _.clone(entityType.blueprint);
// 		var workflows = [];
		// !WORKING HERE:: FIX THIS ON BACK END.
		/*
_.each(bpUpdate, function (field) {
			if (
				!_.isEmpty(field.workflow)
				&& typeof field.workflow === 'object'
				&& field.workflow.hasOwnProperty('id')
			) {
				workflows.push(field.workflow);
				field.workflow = parseInt(field.workflow.id);
			}
		});
*/
		sb.data.db.obj.update (
			entityType.object_bp_type
			, {
				id: 			entityType.id
				, blueprint: 	entityType.blueprint
			}
			, function (response) {

				/*
_.each(response.blueprint, function (field, key) {
					if (
						field.hasOwnProperty('workflow')
					) {
						field.workflow = _.findWhere(workflows, {id: parseInt(field.workflow)});
					}
				});
*/

				onComplete(response);

			}
		) ;

	}

	function removeProperty (entityType, key, onComplete) {

		/*
                sb.dom.alerts.ask(
                    {
                        title: 'Remove '+ entityType.blueprint[key].name +'?'
                        , text: ''
                    }
                    , function (response) {

                        if (!response) {
                            swal.close();
                            return;
                        }

                        swal.disableButtons();
        */
		entityType.blueprint[key].is_archived = true;
		sb.data.db.obj.update (
			'entity_type'
			, {
				id: 				entityType.id
				, blueprint: 	entityType.blueprint
			}
			, function (response) {

// 						swal.close();
				onComplete(response);

			}
		) ;
		/*

                    }
                );
        */

	}

	function changePropertyType (entityType, key, newType, onComplete, shouldCheck, optionsType, goBack, context) {

		function getOptionsForm (ui, typeDef, entityType, onComplete, optionsType) {

			var ret = {};
			var tags = {};

			if (
				entityType.blueprint[key]
				&& !_.isEmpty(entityType.blueprint[key].options)
			) {
				ret = entityType.blueprint[key].options;
			}

			function processForm (form, typeDef) {

				var formData = form.process().fields;

				_.each(typeDef.options, function (option, key) {

					// Only look at appropriate options
					if (
						optionsType === 'view-options'
						&& option.configType !== 'view'
					) {
						return;
					} else if (
						optionsType === 'field-options'
						&& option.configType === 'view'
					) {
						return;
					}

					switch (option.type) {

						case 'bool':
							if (formData[key] && formData[key].value == true) {
								ret[key] = true;
							} else {
								ret[key] = false;
							}
							break;

						case 'currentType':
						case 'detail':
							break;

						case 'select':
						case 'string':
						case 'text':
							if (formData[key] && formData[key].value) {
								ret[key] = formData[key].value;
							} else {
								ret[key] = '';
							}
							break;

						case 'number':
							if (formData[key] && formData[key].value) {
								ret[key] = parseInt(formData[key].value);
							} else {
								ret[key] = '';
							}
							break;

						case 'tags':
							if (!_.isEmpty(tags[key])) {
								ret[key] = _.pluck(tags[key], 'id');
							} else {
								ret[key] = [];
							}
							break;

					}

				});

				return ret;

			}

			var selection = {};

			ui.makeNode(
				'h'
				, 'div'
				, {
					css: 'ui header'
					, tag: 'h2'
					, text: 'Create a '+ typeDef.title +' property'
				}
			);

			var formSetup = {};
			_.each(typeDef.options, function (option, optionKey) {

				// Only show appropriate options
				if (
					optionsType === 'view-options'
					&& option.configType !== 'view'
				) {
					return;
				} else if (
					optionsType === 'field-options'
					&& option.configType === 'view'
				) {
					return;
				}

				switch (option.type) {

					case 'bool':
						formSetup[optionKey] = {
							name: optionKey
							, type: 'check'
							, label: option.name
							, options:[{
								name: 		optionKey,
								label:		'Yes',
								value:		'yes',
								checked: 	(ret[optionKey] === true)
							}]
						};
						break;

					case 'currentType':

						ui.makeNode(optionKey, 'div', {css: 'field', style:'padding:0px;'})
							.makeNode('h', 'div', {text: '<strong>'+ option.name +'</strong>'});

						sb.notify ({
							type: 'view-field'
							, data: {
								type: 			newType
								, property: 	optionKey
								, obj:			ret
								, options: 	{
									edit: 		true
									, editing: 	true
									, commitChanges: false
									, commitUpdates: false
									, is_template: 		option.is_template || false
									, isTemplate: 		option.is_template || false
									, title: 	ret[optionKey]
									, blueprint:{
										[optionKey]: entityType.blueprint[key]
									},
									context: context,
									bp_name: entityType.bp_name
								}
								, ui: ui[optionKey].makeNode('v', 'div', {})
							}
						});
						break;

					case 'detail':
						ui.makeNode(optionKey, 'div', {css: 'field', style:'padding:0px;'})
							.makeNode('h', 'div', {text: '<strong>'+ option.name +'</strong>'});

						var options = {
							edit: true,
							editing: true,
							commitUpdates: option.commitUpdates,
							title: ret[optionKey],
							is_template: option.is_template,
							showToolbar: option.showToolbar,
							onUpdate: function (updated, txt) {
								updated[optionKey] = txt;
							}
						}

						_.each(option.options, function(option, key) {

							options[key] = option;

							if (key == 'useMedium' && option == true) {
								options.onChange = function (html) {
									ret[optionKey] = html;
								}
							}

						});

						sb.notify ({
							type: 'view-field',
							data: {
								type: 'detail',
								property: optionKey,
								obj: ret,
								options: options,
								ui: ui[optionKey].makeNode('v', 'div', {})
							}
						});
						break;

					case 'field':

					function continueBuildingFieldPicker() {

						ui.makeNode(optionKey +'-h', 'div', {text: '<strong>'+ option.name +'</strong>'});

						var setup = {
							multi: true,
							allowedTypes: option.allowedTypes,
							onUpdate: function (newVal) {

								ret[optionKey] = newVal;

							}.bind(optionKey),
							value: ret[optionKey]
						};

						if (option.hasOwnProperty('multi')) {

							setup.multi = option.multi;

						}

						if (option.listPicker) {

							setup.listPicker = option.listPicker;

							fieldListPicker(
								ui.makeNode(optionKey, 'div', {})
								, bpToUse
								, setup
								, _.clone(option)
							);

						} else {

							fieldSelection(
								ui.makeNode(optionKey, 'div', {})
								, bpToUse
								, setup
								, _.clone(option)
							);

						}

						ui.makeNode(optionKey +'br', 'div', {css: 'ui clearing divider'});

					}

						var bpToUse = entityType.blueprint;

						// If a reference to another option, check for the option
						if (option.blueprint) {

							bpToUse = false;
							if (ret[option.blueprint]) {

								sb.data.db.obj.getBlueprint(ret[option.blueprint], function(blueprint) {

									bpToUse = blueprint;
									option.blueprintName = ret[option.blueprint];

									if (!_.isEmpty(bpToUse) && bpToUse.blueprint) {
										bpToUse = bpToUse.blueprint;
									} else {
										bpToUse = false;
									}

									if (!bpToUse) {
										return;
									}

									continueBuildingFieldPicker();

								}, false, true);

							}

						} else {

							if (!bpToUse) {
								return;
							}

							continueBuildingFieldPicker();

						}
						break;

					case 'objectType':
						//!#1552: Pass down flag to also pull cross-instance filters if set
						var objTypePlaceholder = 'Find a data set..';

						if (ret[optionKey]) {

							var selectedType = _.findWhere(appConfig.Types, {bp_name: ret[optionKey].substr(1)});

							if (!selectedType && option.includeBlueprints) {

								_.find(option.includeBlueprints, function(blueprint) {

									if (blueprint == ret[optionKey]) {

										selectedType = {
											name: sb.dom.blueprintPrettyName(blueprint)
										}

									}

								});

							}

							if (selectedType) {
								objTypePlaceholder = selectedType.name;
							}

						}

						ui.makeNode(
							optionKey
							, 'div'
							, {
								css: 'ui fluid category search'
								, text:
									'<div class="ui left icon fluid input">'+
									'<input class="prompt" type="text" placeholder="'+ objTypePlaceholder +'">'+
									'<i class="search icon"></i>'+
									'</div>'+
									'<div class="results"></div>'
								, listener: {
									type: 			'search',
									objectType: 	'entity_type',
									onResponse: function(response) {

										var searchVal = $(ui[optionKey].selector + ' > .input > input').val();

										_.each(option.includeBlueprints, function(blueprintId) {

											if (blueprintId.includes(searchVal)) {

												response.results.push({
													value: blueprintId,
													name: sb.dom.blueprintPrettyName(blueprintId)
												});

											}

										});

										return response;

									},
									onSelect: 	function(optionKey, result, response) {

										if (!_.isEmpty(result)) {

											if (result.value) {
												ret[optionKey] = result.value;
											} else if (result.bp_name) {
												ret[optionKey] = '#'+ result.bp_name;
											} else {
												ret[optionKey] = '#'+_.findWhere(appConfig.Types, {id: result.id}).bp_name;
											}

										}

									}.bind({}, optionKey),
									selection: {
										name: 		true
										, icon: 	true
										, bp_name: 	true
										, object_bp_name: true
									}
								}
							}
						);
						break;

					case 'number':
						formSetup[optionKey] = {
							name: 		optionKey
							, type: 	'number'
							, label: 	option.name
							, value: 	ret[optionKey]
						};
						break;

					case 'string':
						formSetup[optionKey] = {
							name: 		optionKey
							, type: 		'text'
							, label: 	option.name
							, value: 	ret[optionKey]
						};
						break;

					case 'text':
						formSetup[optionKey] = {
							name: 		optionKey
							, type: 		'textbox'
							, label: 	option.name
							, value: 	ret[optionKey]
						};
						break;

					case 'select':
						formSetup[optionKey] = {
							name: 		optionKey
							, type: 		'select'
							, label: 	option.name
							, value: 	ret[optionKey]
							, options: 	option.options
						};
						break;

					case 'tags':
						Comps[optionKey] = sb.createComponent('tags');
						ui.makeNode(
							optionKey +'br'
							, 'lineBreak'
							, {}
						);
						ui.makeNode(
							optionKey +'hr'
							, 'div'
							, {
								tag: 		'h5'
								, text: 	option.name
							}
						);
						ui.makeNode(
							optionKey
							, 'div'
							, {}
						);
						ui.makeNode(
							optionKey +'br2'
							, 'lineBreak'
							, {}
						);
						break;

					case 'template':
						var objTypePlaceholder = 'Find a template..';
						if (ret[optionKey]) {

							var selectedType = _.findWhere(appConfig.Types, {bp_name: ret[optionKey].substr(1)});
							if (selectedType) {
								objTypePlaceholder = selectedType.name;
							}

						}

						ui.makeNode(
							optionKey
							, 'div'
							, {
								css: 'ui fluid category search'
								, text:
									'<div class="ui left icon fluid input">'+
									'<input class="prompt" type="text" placeholder="'+ objTypePlaceholder +'">'+
									'<i class="search icon"></i>'+
									'</div>'+
									'<div class="results"></div>'
								, listener: {
									type: 			'search',
									objectType: 	entityType.object_bp_type,
									onSelect: 	function(optionKey, result, response){

										if (!_.isEmpty(result)) {

											if(result.bp_name){
												ret[optionKey] = '#'+ result.bp_name;
											}else{
												ret[optionKey] = '#'+_.findWhere(appConfig.Types, {id: result.id}).bp_name;
											}

										}

									}.bind({}, optionKey),
									selection: {
										name: 		true
									}
								}
							}
						);
						break;

					case 'filter':

						var obj = {
							object_bp_type: '#' + entityType.bp_name
						}

						ui.makeNode(optionKey +'-h', 'div', {text: '<strong>'+ option.name +'</strong>'});

						sb.notify ({
							type: 'view-field',
							data: {
								type: 'filter',
								property: key,
								obj: obj,
								options:  {
									updateField: function(filter) {
										ret[optionKey] = filter;
									},
									objectType: ret.objectType.substr(1),
									contextType: entityType.bp_name
								},
								ui: ui.makeNode(optionKey, 'div', {})
							}
						});

						break;

				}

			});

			ui.makeNode('form', 'form', formSetup);

			ui.makeNode('formBreak','div',{text:'<br />'});

			ui.makeNode(
				'commit'
				, 'div'
				, {
					css: 'ui green button'
					, text: '<i class="check icon"></i> Save'
				}
			).notify('click', {
				type: 'fields-run'
				, data: {
					run: function () {

						function verifyCheck (shouldCheck, onComplete) {

							if (shouldCheck) {

								sb.dom.alerts.ask({
									title: 	'Are you sure?'
									, text: 'By changing the field type, you will lose any data stored in this field.'
								}, function (r) {

									swal.close();
									if (r) {
										onComplete();
									}

								});

							} else {
								onComplete();
							}

						}

						verifyCheck(
// 							shouldCheck
							false
							, function () {

								ui.commit.loading();
								onComplete(processForm(ui.form, typeDef));

							}
						);

					}
				}
			}, sb.moduleId);

			ui.patch();

			_.each(typeDef.options, function (option, optionKey) {

				switch (option.type) {

					case 'tags':
						var selectedTags = [];
						if (
							ret
							&& Array.isArray(ret[optionKey])
						) {
							selectedTags = _.map(ret[optionKey]
								, function (t) {
									return parseInt(t);
								});
						}

						Comps[optionKey].notify({
							type: 'object-tag-view',
							data: {
								canEdit: true,
								domObj: 		ui[optionKey],
								objectType: 	'',
								tags: 			selectedTags,
								tagType: 		option.tagType,
								onChange: function (key, response) {

									tags[key] = response;

								}.bind({}, optionKey)
							}
						});
						break;

				}

			});
		}

		function parseOptions (entityType, typeDef, onComplete) {

			if (typeof typeDef.parseOptions === 'function') {

				// If 3 args appear in func signature, this
				// 3rd is assumed a callback to run this async-ly.
				if (typeDef.parseOptions.length > 2) {

					typeDef.parseOptions(
						entityType.blueprint[key].options
						, entityType.blueprint[key]
						, function (response) {

							entityType.blueprint[key] = response;
							commitBlueprintChange(entityType, onComplete);

						}
						, entityType
					);
					return ;

				} else {

					entityType.blueprint[key] = typeDef.parseOptions(
						entityType.blueprint[key].options
						, entityType.blueprint[key]
					);

				}

			}

			commitBlueprintChange(entityType, onComplete);

		}

		var typeDef = _.findWhere(Types, {name: newType});
		var Comps = {};
		entityType.blueprint[key].fieldType = newType;
		if (typeDef.hasOwnProperty('propertyType')) {
			entityType.blueprint[key].type = typeDef.propertyType;
		}
		if (typeDef.hasOwnProperty('objectType')) {
			entityType.blueprint[key].objectType = typeDef.objectType;
		}
		if (typeDef.hasOwnProperty('select')) {
			entityType.blueprint[key].select = typeDef.select;
		}
		if (typeDef.hasOwnProperty('hideDefault') && typeDef.hideDefault) {
			delete DefaultTypeOptions._default;
		}

		// Set default options
		if (_.isEmpty(typeDef.options)) {
			typeDef.options = {};
		}
		_.each(DefaultTypeOptions, function (opt, key) {

			typeDef.options[key] = opt;

		});

		sb.notify({
			type: 	'get-sys-modal'
			, data: 	{
				callback: 	function (modal) {

					modal.body.empty();
					modal.show();
					getOptionsForm(
						modal.body
						, typeDef
						, entityType
						, function (selection) {

							modal.hide();
							modal.body.empty();
							entityType.blueprint[key].options = selection;

							parseOptions(entityType, typeDef, function (response) {

								onComplete(response);
								if (typeof goBack === 'function') {
									goBack();
								}

							});

						}
						, optionsType
					);

				}
				, onClose: function () {

					if (typeof goBack === 'function') {
						goBack();
					}

				}
			}
		});

	}

	function updateField(entityId, objectBpType, propName, fieldType, options, obj, oldValue, _text, onRefresh = false){
		var newText = ((!!oldValue) ? oldValue : '') + '' + _text;
		sb.data.db.obj.update(
			objectBpType
			, {
				id: 			entityId
				, [propName]: 	newText
			}
			, function (r) {
				if(!onRefresh) {
					options.onUpdate(obj, newText);
				} else {
					onRefresh(newText);
				}
			});
	}

	function wrapWithSnippets(propName, obj, callback){
		try {
			if (appConfig.instance === 'foundation_group' || appConfig.instance === 'rickyvoltz' && obj.object_bp_type != undefined) {
				if (obj.object_bp_type != 'entity_type') {
					var bpName = obj.object_bp_type.replace('#', '');
					obj = _.find(appConfig.Types, {bp_name: bpName});
				}
				sb.data.db.obj.getWhere('snippet', {
					entity_type: obj.id,
					entity_field_key: propName
				}, function (_snippets) {
					callback(_snippets);
				});
			} else {
				callback([]);
			}
		}catch(e){
			console.error('error: ', e);
			callback([]);
		}

	}

	function removeTags(str, tag) {
		try {
			return str.replace('<'+tag+'>', '').replace('</'+tag+'>', '');
		}catch(e){
			return str;
		}
	};

	function buildDropDownOptions(ui, data, options, redraw){

		var propName = data.property;
		var viewStyle = options.viewStyle || 'record';

		var cleanedName = removeTags(options._message, 'p');

		var dropdownConfig = {
			text: 	'<span class="text"><i class="align left icon" style="color:rgba(175, 175, 175, 1);"></i> ' + cleanedName + '</span>'
			, css: 	'ui grey dropdown item field-manager'

		};
		if (viewStyle === 'form') {
			dropdownConfig.text = '<h4 class="ui header">' + cleanedName +'</h5>';
			dropdownConfig.css = 'ui grey dropdown item';
		}

		if (options.edit) {

			dropdownConfig.css += ' edit';

			dropdownConfig.listener = {
				action: 'select'
				, onChange:function(value) {

					if (value) {

						switch (value) {
							case 'snippets':
								sb.notify({
									type: 	'get-sys-modal'
									, data: 	{
										callback: 	function (modal) {

											modal.body.empty();
											modal.show();

											modal.body.makeNode(
												'h'
												, 'div'
												, {
													css: 'ui header'
													, tag: 'h2'
													, text: 'Create a Snippet'
												}
											);

											var formSetup = {};

											formSetup['name'] = {
												name: 		'name'
												, type: 		'text'
												, label: 	'Snippet name'
												, value: 	''
												, required: true
											};

											formSetup['content'] = {
												name: 		'content'
												, type: 		'textbox'
												, label: 	'Snippet content'
												, value: 	''
												, required: true
											};


											modal.body.makeNode('form', 'form', formSetup);

											modal.body.makeNode('formBreak','div',{text:'<br />'});

											modal.body.makeNode(
												'commit'
												, 'div'
												, {
													css: 'ui green button'
													, text: '<i class="check icon"></i> Save'
												}
											).notify('click', {
												type: 'fields-run'
												, data: {
													run: function () {

														var formData = modal.body.form.process().fields;

														if(formData.name.value === '' || formData.content.value === ''){
															sb.notify({
																type: 'display-alert',
																data: {
																	header: 'Failed!',
																	body: 'Please complete the form values',
																	color: 'red'
																}
															});
															return true;
														}

														var bpName = data.obj.object_bp_type.replace('#', '');
														var entityObj = _.find(appConfig.Types, {bp_name: bpName});

														var snippetAdded = {
															name: formData.name.value,
															contents: formData.content.value,
															entity_type: entityObj.id,
															entity_field_key: propName,
														};

														modal.body.commit.loading();
														sb.data.db.obj.create('snippet'
															, snippetAdded
															, function(res){
																sb.notify({
																	type: 'display-alert',
																	data: {
																		header: 'Success!',
																		body: 'The new snippet has been created successfully',
																		color: 'green'
																	}
																});
																modal.hide();
																redraw(data);
															}
														);

													}
												}
											}, sb.moduleId);
											modal.body.patch();
										}
										, onClose: function () {

											if (typeof options.goBack === 'function') {
												options.goBack();
											}

										}
									}
								});
								return;
							case 'search-snipped':
								sb.notify({
									type: 	'get-sys-modal'
									, data: {
										callback: function (modal, state) {
											modal.body.empty();
											modal.show();

											modal.body.makeNode("title", "headerText", {
												text: 'Search Snippets',
												size: "large",
											});

											modal.body.makeNode("newDocBreak", "lineBreak", {});

											modal.body.makeNode("searchLabel", "div", {
												tag: "label",
												text: "Search Snippet",
											});
											modal.body.makeNode("inputContainer", "div", {
												css: "ui huge fluid action input",
											});

											function onAddSnippet(snippet) {
												var entityId = data.obj.id;
												var contextObjectBpType = data.obj.object_bp_type;
												var fieldType = data.obj.type;
												var oldValue = data.obj[propName];

												updateField(
													entityId,
													contextObjectBpType,
													propName,
													fieldType,
													options,
													data.obj,
													oldValue,
													'<p>' + snippet.contents + '</p>',
													function(newData){
														$('#' + options._fieldId + ' .ql-editor').html(newData);
													}
												);
											}

											modal.body.inputContainer
												.makeNode("input", "div", {
													tag: "input",
													type: "text",
													placeholder: "Search...",
												})
												.notify(
													"change",
													{
														type: "headquarters-run",
														data: {
															run: function (modal, state) {
																searchSnippets.call({}, modal, state, onAddSnippet);
															}.bind({}, modal, state, onAddSnippet),
														},
													},
													sb.moduleId
												);

											modal.body.inputContainer
												.makeNode("btn", "div", {
													text: "Search",
													css: "ui button",
												})
												.notify(
													"click",
													{
														type: "headquarters-run",
														data: {
															run: function (modal, state) {
																searchSnippets.bind(modal, state, onAddSnippet);
															}.bind({}, modal, state, onAddSnippet),
														},
													},
													sb.moduleId
												);

											modal.body.makeNode("results", "div", {
												css: "sixteen wide column",
												style: "margin-top:0px !important; padding:0 !important;",
											});
											modal.body.makeNode("snippetBreak1", "lineBreak", {});

											modal.body.makeNode("allSnippetLabel", "div", {
												tag: "label",
												text: "All Available Snippets",
											});

											modal.body.patch();

										}
										, onClose: function () {
											if (typeof options.goBack === 'function') {
												options.goBack();
											}
										}
									}
								});
								return;
						}

						var upd = value.split('.');

						switch (upd[0]) {
							case 'snipped':

								var entityId = data.obj.id;
								var contextObjectBpType = data.obj.object_bp_type;
								var fieldType = data.obj.type;
								var oldValue = data.obj[propName];

								//search the blue print with id upd[1], and this blueprint will be the updatedField..
								sb.data.db.obj.getById('snippet', upd[1], function(snippet) {

									updateField(
										entityId,
										contextObjectBpType,
										propName,
										fieldType,
										options,
										data.obj,
										oldValue,
										'<p>' + snippet.contents + '</p>',
										function(newData){
											$('#' + options._fieldId + ' .ql-editor').html(newData);
										}									);

								});

								break;

						}

					}

				},
				type:'dropdown',
				placeholder:'All types',
				allowCategorySelection: true
				, delay : {
					hide   : 300,
					show   : 200,
					search : 50,
					touch  : 50
				}
				, duration: 100
			} ;

		}

		ui.makeNode('c', 'div', dropdownConfig);

		ui.c.makeNode('menu', 'div', {
			css:'menu'
		});



		var hasSnippets = options.snippets && options.snippets.length > 0;

		var subIcon = '<i class="right floated grey chevron right icon pull-right"></i>';
		if(!hasSnippets){
			subIcon = '';
		}

		//snippets
		ui.c.menu
			.makeNode('snippets', 'div', {
				css: 'item'
				, dataAttr: [
					{
						name: 'value'
						, value: 'snippets'
					}
				]
			})
			.makeNode('txt', 'div', {
				css: 'text'
				, tag: 'span'
				, text: '<i class="code icon"></i> Snippet Saved' + subIcon
			});

		//snippet childs
		ui.c.menu.snippets.makeNode('menu', 'div', {
			css: 'menu'
			, style: 'width: 200px; max-height: 450px; overflow-y: scroll;'
		});


		if(hasSnippets) {

			//formats
			var Snippets = _.map(options.snippets, function (item) {
				return {
					id: item.id + '',
					nid: item.id,
					name: item.name + '',
					text: item.contents
				};
			});

			_.chain(Snippets).each(function (item, id) {

				//item
				ui.c.menu.snippets.menu.makeNode(item.id, 'div', {
					css: 'item'
					, dataAttr: [
						{
							name: 'value'
							, value: 'snipped.' + item.id
						}
					]
				})
					.makeNode('txt', 'div', {
						css: 'text'
						, tag: 'span'
						, text: '<h6 class="text-muted header" style="margin-bottom:15px;text-transform:uppercase;"><i class="text icon"></i> '+item.name+' </h6><p style="max-width: 400px;white-space: normal;">' + item.text + '</p>'
					});

			});

			//add functionality for search snippets
			ui.c.menu.snippets.menu.makeNode('search-snippet', 'div', {
				css: 'item'
				, dataAttr: [
					{
						name: 'value'
						, value: 'search-snipped'
					}
				]
			})
				.makeNode('searchsnippet', 'button', {
					css: 'pda-btn-x-large pda-btn-blue pda-btn-fullWidth'
					, tag: 'span'
					, text: 'Search All Snippets'
				});

		}

	}

	function searchSnippets(modal, state, onAddSnippet) {
		// variables
		var searchString = $(modal.body.inputContainer.input.selector).val();

		var collectionsSetup = {
			actions: {},
			domObj: modal.body.results,
			fields: {
				name: {
					title: "Snippet Name",
					view: function (ui, snippet) {
						ui.makeNode("row-" + snippet.id, "div", {
							style: "font-weight:bold",
							text: snippet.name,
						});
					},
				},
				contents: {
					title: "Contents",
					view: function (ui, snippet) {
						ui.makeNode("row-" + snippet.id, "div", {
							style: "font-weight:bold",
							text: snippet.contents,
						});
					},
				},
				select: {
					title: "",
					view: function (ui, snippet) {
						ui.makeNode("row-" + snippet.id, "div", {});
						ui["row-" + snippet.id]
							.makeNode("select", "button", {
								text: '<i class="fa fa-plus"></i> Use This Snippet',
								css: "pda-btn-green",
								style: "float:right;",
							})
							.notify(
								"click",
								{
									type: "headquarters-run",
									data: {
										run: function () {
											ui["row-" + snippet.id].select.loading();
											onAddSnippet(snippet);
											modal.hide();
										}.bind(ui, snippet, state),
									},
								},
								sb.moduleId
							);
					},
				},
			},
			objectType: "snippet",
			selectedView: "table",
			subviews: {
				table: {
					hideSelectionBoxes: true,
					hideRowActions: true,
				},
			},
			state: state,
			sortCol: "name",
			sortDir: "asc",
			menu: false,
			submenu: false,
			tags: false,
			searchAboveCollection: false,
			where: {
				name: {
					type: "contains",
					value: searchString,
				}
			},
		};

		sb.notify({
			type: "show-collection",
			data: collectionsSetup,
		});

		modal.body.results.patch();
	}

	function fieldEditor (propName, ui, obj, options, redraw) {

		var type = 'plain-text';
		var typeTitle = 'Text';
		var name = 'Property';
		var icon = 'question';
		var blueprint = obj;
		var viewStyle = options.viewStyle || 'record';

		if (
			obj.object_bp_type === 'entity_type'
			|| obj.object_bp_type === 'view'
			|| obj.hasOwnProperty('blueprint')
		) {
			blueprint = obj.blueprint;
		}

		if (typeof options.onUpdate !== 'function') { options.onUpdate = function () {} ; }
		if (propName === 'tagged_with') {
			return ;
		}
		var property = blueprint[propName];
		name = property.name;
		type = property.fieldType;

		var isInherited = (parseInt(property._inheritedFrom) > 0);

		var typeDef = _.findWhere(Types, {name: type});

		if ( typeDef ){
			icon = typeDef.icon;
			typeTitle = typeDef.title;
		}

		var config = {
			canSetName: 	true
			, canSetType: 	true
			, canRemove: 	true
		};

		if (options) {

			_.each(config, function (defaultVal, key) {

				if (options.hasOwnProperty(key)) {
					config[key] = options[key];
				}

			});

		}


		// Icons that change with options
		if (
			blueprint
			&& typeDef
			&& typeDef.hasOwnProperty('getIcon')
			&& typeof typeDef.getIcon === 'function'
			&& blueprint.hasOwnProperty(propName)
			&& blueprint[propName]
			&& blueprint[propName].hasOwnProperty('options')
			&& blueprint[propName].options
		) {
			icon = typeDef.getIcon(blueprint[propName].options);
		}

		var dropdownConfig = {
			text: 	'<span class="text"><i class="'+ icon +' icon" style="color:rgba(175, 175, 175, 1);"></i>'+ name +'</span>'
			, css: 	'ui grey dropdown item field-manager'
		};
		if (viewStyle === 'form') {
			dropdownConfig.text = '<h4 class="ui header">'+ name +'</h5>';
			dropdownConfig.css = 'ui grey dropdown item';
		}

		if (appConfig.is_portal) {
			dropdownConfig.text = '<span>'+ name +'</span>';
		}

		if (options.edit) {

			dropdownConfig.css += ' edit';

			dropdownConfig.listener = {
				action: 'select'
				, onChange:function(value) {

					if (value) {
						switch (value) {

							case 'validations':

								function openModalConditions(ui, workFlowButton, obj, condition){

									ui.body.makeNode('menu', 'div', {
										css: 'ui secondary right floated menu',
										style: 'margin:0 !important;'
									});

									var linkSetup = {
										css: 'circular icon button item',
										text: '<i class="external square alternate icon"></i>',
										tag: 'a',
										href: sb.data.url.getObjectPageParams(workFlowButton.workflow, {})
									};

									var closeLinkSetup = {
										css: 'circular icon button item',
										text: '<i class="close icon"></i>',
										tag: 'a'
									};

									ui.body.menu.makeNode('open', 'div', linkSetup);

									ui.body.menu.makeNode('close', 'div', closeLinkSetup)
										.listeners.push(
										function (selector) {

											$(selector).on(
												'click'
												, function () {
													ui.hide();
												}
											);

										}
									);

									ui.body.makeNode('c', 'div', {});
									ui.patch();

									workFlowButton.workflow['parent'] = obj.id;

									sb.notify({
										type: 'view-condition'
										, data: {
											ui: ui.body.c
											, state: {
												object: workFlowButton.workflow
											}
											, condition: condition
											, onComplete: function (response) {
												ui.body.patch();
												ui.hide();

											},
											bp: blueprint
										}
									});

								}

								sb.notify({
									type: 'get-sys-modal'
									, data: {
										callback: function (modal) {
											try {
												modal.body.empty();
												modal.show();

												var workFlowButton = _.find(blueprint, function (bp) {
													return bp.fieldType === 'state' && bp.is_archived !== true
												});

												var doneState = _.find(workFlowButton.workflow.states, function (_states) {
													return _states.type === 'done';
												});

												sb.data.db.obj.getWhere('condition', {
													object: workFlowButton.workflow.id,
													state: doneState.id
												}, function (conditions) {

													if(conditions.length > 0){
														//open the last condition
														openModalConditions(modal, workFlowButton, obj, conditions[0]);
													} else {
														//create new condition by default if not exists
														var templateObj = {
															"object_bp_type":"condition",
															"tagged_with":[appConfig.user.id],
															"is_template":0,
															"object":workFlowButton.workflow.id,
															"state":doneState.id,
															"conditions":[],
															"name":"default-entity-condition"
														};

														sb.data.db.obj.create(
															"condition",
															templateObj,
															function (newCondition) {
																openModalConditions(modal, workFlowButton, obj, newCondition);
															},
															1
														);
													}


												});
											}catch(e){
												modal.hide();
												console.log('e', e);
											}

										}
									}
								});

								return;

							case 'rm':
								removeProperty(
									obj
									, propName
									, options.onUpdate
								);
								return;

							case 'permissions':
								sb.notify({
									type: 	'view-permissions-form'
									, data: 	{
										ui: 			ui.c.menu
										, obj: 		obj
										, property: 	propName
										, types: 	{
											view: 	{
												title: 	'View'
												, icon: 'eye'
											}
											, edit: {
												title: 	'Edit'
												, icon: 'edit'
											}
										}
										, onComplete: function (response) {

											blueprint = response.blueprint;
											if (typeof options.goBack === 'function') {
												options.goBack(obj);
											}

										}
										, onClose: options.goBack
									}
								});
								return;

							case 'view-options':
								changePropertyType (
									obj
									, propName
									, type
									, options.onUpdate
									, false
									, 'view-options'
									, options.goBack
								);
								return;
							case 'search-snipped':
								sb.notify({
									type: 	'get-sys-modal'
									, data: {
										callback: function (modal, state) {
											modal.body.empty();
											modal.show();

											modal.body.makeNode("title", "headerText", {
												text: 'Search Snippets',
												size: "large",
											});

											modal.body.makeNode("newDocBreak", "lineBreak", {});

											modal.body.makeNode("searchLabel", "div", {
												tag: "label",
												text: "Search Snippet",
											});
											modal.body.makeNode("inputContainer", "div", {
												css: "ui huge fluid action input",
											});

											function onAddSnippet(snippet) {
												var entityId = options.context.id;
												var contextObjectBpType = options.context.object_bp_type;
												var fieldType = obj.blueprint[propName].fieldType;
												var oldValue = options.context[propName];

												//search the blue print with id upd[1], and this blueprint will be the updatedField..
												updateField(entityId, contextObjectBpType, propName, fieldType,  options,obj, oldValue,'<p>' + snippet.contents + '</p>');
											}

											modal.body.inputContainer
												.makeNode("input", "div", {
													tag: "input",
													type: "text",
													placeholder: "Search...",
												})
												.notify(
													"change",
													{
														type: "headquarters-run",
														data: {
															run: function (modal, state) {
																searchSnippets.call({}, modal, state, onAddSnippet);
															}.bind({}, modal, state, onAddSnippet),
														},
													},
													sb.moduleId
												);

											modal.body.inputContainer
												.makeNode("btn", "div", {
													text: "Search",
													css: "ui button",
												})
												.notify(
													"click",
													{
														type: "headquarters-run",
														data: {
															run: function (modal, state) {
																searchSnippets.bind(modal, state, onAddSnippet);
															}.bind({}, modal, state, onAddSnippet),
														},
													},
													sb.moduleId
												);

											modal.body.makeNode("results", "div", {
												css: "sixteen wide column",
												style: "margin-top:0px !important; padding:0 !important;",
											});
											modal.body.makeNode("snippetBreak1", "lineBreak", {});

											modal.body.makeNode("allSnippetLabel", "div", {
												tag: "label",
												text: "All Available Snippets",
											});

											modal.body.patch();

										}
										, onClose: function () {
											if (typeof options.goBack === 'function') {
												options.goBack();
											}
										}
									}
								});
								return;
							case 'snippets':
								sb.notify({
									type: 	'get-sys-modal'
									, data: 	{
										callback: 	function (modal) {

											modal.body.empty();
											modal.show();

											modal.body.makeNode(
												'h'
												, 'div'
												, {
													css: 'ui header'
													, tag: 'h2'
													, text: 'Create a Snippet'
												}
											);

											var formSetup = {};

											formSetup['name'] = {
												name: 		'name'
												, type: 		'text'
												, label: 	'Snippet name'
												, value: 	''
												, required: true
											};

											formSetup['content'] = {
												name: 		'content'
												, type: 		'textbox'
												, label: 	'Snippet content'
												, value: 	''
												, required: true
											};


											modal.body.makeNode('form', 'form', formSetup);

											modal.body.makeNode('formBreak','div',{text:'<br />'});

											modal.body.makeNode(
												'commit'
												, 'div'
												, {
													css: 'ui green button'
													, text: '<i class="check icon"></i> Save'
												}
											).notify('click', {
												type: 'fields-run'
												, data: {
													run: function () {

														var formData = modal.body.form.process().fields;

														if(formData.name.value === '' || formData.content.value === ''){
															sb.notify({
																type: 'display-alert',
																data: {
																	header: 'Failed!',
																	body: 'Please complete the form values',
																	color: 'red'
																}
															});
															return true;
														}

														var snippetAdded = {
															name: formData.name.value,
															contents: formData.content.value,
															entity_type: obj.id,
															entity_field_key: propName,
														};

														modal.body.commit.loading();
														sb.data.db.obj.create('snippet'
															, snippetAdded
															, function(res){
																sb.notify({
																	type: 'display-alert',
																	data: {
																		header: 'Success!',
																		body: 'The new snippet has been created successfully',
																		color: 'green'
																	}
																});
																modal.hide();
																redraw();
															}
														);

													}
												}
											}, sb.moduleId);
											modal.body.patch();
										}
										, onClose: function () {

											if (typeof options.goBack === 'function') {
												options.goBack();
											}

										}
									}
								});
								return;

						}

						var upd = value.split('.');

						switch (upd[0]) {

							case 'field':
								changePropertyType (
									obj
									, propName
									, upd[1]
									, options.onUpdate
									, upd[1] !== obj.blueprint[propName].fieldType
									, 'field-options'
									, options.goBack
									, options.context
								);
								break;
							case 'snipped':

								var entityId = options.context.id;
								var contextObjectBpType = options.context.object_bp_type;
								var fieldType = obj.blueprint[propName].fieldType;
								var oldValue = options.context[propName];

								//search the blue print with id upd[1], and this blueprint will be the updatedField..
								sb.data.db.obj.getById('snippet', upd[1], function(snippet) {

									updateField(entityId, contextObjectBpType, propName, fieldType,  options,obj, oldValue,'<p>' + snippet.contents + '</p>');
								});

								break;

						}

					}

				},
				type:'dropdown',
				placeholder:'All types',
				allowCategorySelection: true
				, delay : {
					hide   : 300,
					show   : 200,
					search : 50,
					touch  : 50
				}
				, duration: 100
// 				, transition: 'swing down'
			} ;

		}

		ui.makeNode('c', 'div', dropdownConfig);

		if (!options.edit) {
			return ;
		}

		ui.c.makeNode('menu', 'div', {
			css:'menu'
		});

		// Field name input
		var updateName = _.throttle(function (e) {

			obj.blueprint[propName].name = e.currentTarget.value;
			commitBlueprintChange(obj, function (r) {

				name = e.currentTarget.value;
				$(ui.c.selector).children('.text').html('<i class="grey '+ icon +' icon"></i>'+ name);

			});

		}, 1000);

		if (
			config.canSetName
			&& options.edit === true
			&& !isInherited
		) {

			ui.c.menu.makeNode(
				'name'
				, 'div'
				, {
					css: 'ui input'
					, text: '<input type="text" placeholder="Property name" value="'+ name +'">'
				}
			).listeners.push(function (selector) {

				$(selector).find('input')
					.on('change keydown', function (e) {
						e.stopPropagation();
					});

				$(selector).find('input')
					.on('blur input', updateName);

			});

		}

		// Field type options
		if (
			options.edit === true
			&& !isInherited
		) {

			ui.c.menu
				.makeNode('type', 'div', {
					css: 'item'
					, dataAttr: [
						{
							name: 'value'
							, value: 'field.'+ obj.blueprint[propName].fieldType
						}
					]
				})
				.makeNode('txt', 'div', {
					css: 'text'
					, tag: 'span'
					, text: '<i class="'+ icon +' icon"></i> Field options'+
						'<i class="right floated grey chevron right icon pull-right"></i>'
				});

		}

		// Field type options
		if (
			options.edit === true
			&& !isInherited
			&& options.title === 'Name'
			&& options.canSetType === false
		) {

			ui.c.menu
				.makeNode('validations', 'div', {
					css: 'item'
					, dataAttr: [
						{
							name: 'value'
							, value: 'validations'
						}
					]
				})
				.makeNode('txt', 'div', {
					css: 'text'
					, tag: 'span'
					, text: '<i class="check icon"></i> Modify validations'
				});

		}

		var hasSnippets = options.snippets && options.snippets.length > 0;
		var canUseSnippet = appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz';

		if(((obj.blueprint && obj.blueprint[propName].fieldType) == 'detail') && canUseSnippet) {

			var subIcon = '<i class="right floated grey chevron right icon pull-right"></i>';
			if(!hasSnippets){
				subIcon = '';
			}

			//snippets
			ui.c.menu
				.makeNode('snippets', 'div', {
					css: 'item'
					, dataAttr: [
						{
							name: 'value'
							, value: 'snippets'
						}
					]
				})
				.makeNode('txt', 'div', {
					css: 'text'
					, tag: 'span'
					, text: '<i class="code icon"></i> Snippet Saved' + subIcon
				});

			//snippet childs
			ui.c.menu.snippets.makeNode('menu', 'div', {
				css: 'menu'
				, style: 'width: 200px; max-height: 450px; overflow-y: scroll;'
			});

		}

		// Formatting options
		ui.c.menu
			.makeNode('view-opts', 'div', {
				css: 'item'
				, dataAttr: [
					{
						name: 'value'
						, value: 'view-options'
					}
				]
			})
			.makeNode('txt', 'div', {
				css: 	'text'
				, tag: 	'span'
				, text: '<i class="eye icon"></i> View options'
			});

		if (
			config.canSetType
			&& options.edit === true
			&& !isInherited
		) {
			if(hasSnippets) {

				//formats
				var Snippets = _.map(options.snippets, function (item) {
					return {
						id: item.id + '',
						nid: item.id,
						name: item.name + '',
						text: item.contents,
					};
				});

				_.chain(Snippets).each(function (item, id) {

					//item
					ui.c.menu.snippets.menu.makeNode(item.id, 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'value'
								, value: 'snipped.' + item.id
							}
						]
					})
						.makeNode('txt', 'div', {
							css: 'text'
							, tag: 'span'
							, text: '<h6 class="text-muted header" style="margin-bottom:15px;text-transform:uppercase;"><i class="' + icon + ' icon"></i> '+item.name+' </h6><p style="max-width: 400px;white-space: normal;">' + item.text + '</p>'
						});

				});

				//add functionality for search snippets
				ui.c.menu.snippets.menu.makeNode('search-snippet', 'div', {
					css: 'item'
					, dataAttr: [
						{
							name: 'value'
							, value: 'search-snipped'
						}
					]
				})
					.makeNode('searchsnippet', 'button', {
						css: 'pda-btn-x-large pda-btn-blue pda-btn-fullWidth'
						, tag: 'span'
						, text: 'Search All Snippets'
					});

			}

			ui.c.menu.type.makeNode('menu', 'div', {
				css: 'menu'
				, style: 'width: 300px; max-height: 300px; overflow-y: scroll;'
			});

			_.chain(Types)
				.where({availableToEntities: true})
				.groupBy('type')
				.each (function (types, name) {

					var txt = name;
					if (name == 'undefined') {
						txt = 'General';
					}

					ui.c.menu.type.menu.makeNode('h-'+ name, 'div', {
						css: 'text-muted header'
						, text: txt
					});

					_.chain(types)
						.sortBy('title')
						.each(function (type) {

							ui.c.menu.type.menu.makeNode(type.title, 'div', {
								css: 'item'
								, dataAttr: [
									{
										name: 'value'
										, value: 'field.'+ type.name
									}
								]
							})
								.makeNode('txt', 'div', {
									css: 'text'
									, tag: 'span'
									, text: '<i class="'+ type.icon +' icon"></i> '+ type.title
								});

						})

					ui.c.menu.type.menu.makeNode('d-'+ name, 'div', {
						css: 'divider'
					});

				});

		}

		if (
			options.edit === true
			&& !isInherited
		) {

			// If state field, show link to workflow page
			if (type === 'state' && property.hasOwnProperty('workflow') ) {

				ui.c.menu.makeNode('wf', 'div', {
					tag: 'a'
					, css: 'item'
					, href: sb.data.url.createPageURL('object', {
						id: 					property.workflow.id
						, name: 				property.workflow.name
						, object_bp_type: 	property.workflow.object_bp_type
						, type: 				property.workflow.object_bp_type
					})
				})
					.makeNode('txt', 'div', {
						css: 'text'
						, tag: 'span'
						, text: '<i class="teal random icon"></i> Edit workflow'
					});

			}

			// Set permissions
			ui.c.menu
				.makeNode('own', 'div', {
					css: 'item'
					, dataAttr: [
						{
							name: 'value'
							, value: 'permissions'
						}
					]
				})
				.makeNode('txt', 'div', {
					css: 'text'
					, tag: 'span'
					, text: '<i class="yellow key icon"></i> Permissions'
				});

		}

		// Remove btn
		if (config.canRemove) {

			ui.c.menu
				.makeNode('rm-div', 'div', {css:'divider'});
			ui.c.menu
				.makeNode('rm', 'div', {
					css: 'item'
					, dataAttr: [
						{
							name: 'value'
							, value: 'rm'
						}
					]
				})
				.makeNode('txt', 'div', {
					css: 'text'
					, tag: 'span'
					, text: '<i class="red trash icon"></i> Remove'
				});

		}

	}

	function fieldListPicker (ui, blueprint, options, state) {

		function mergedView (ui, blueprint, options, object) {

			function getVal (obj) {

				var ret = '';
				if (typeof options.parseMerge === 'function') {

					ret = options.parseMerge(
						obj
					);

				} else if (
					obj
					&& obj.name
				) {

					ret = obj.name;

				} else {

					ret = obj;

				}

				return ret;

			}

			// Get the value to replace/merge into the form
			var mergedValue = '';

			// For options w/ multiple values
			if (Array.isArray(options.value)) {

				mergedValue = [];

				_.each(options.value, function (propName) {

					var splits = [];
					if (propName.charAt(0) === '#') {

						splits = propName.split('.');

						// Reset to dot seperator, for subsets
						splits[0] = splits[0].split('-').join('.');

						if (!object[splits[0]]) {
							object[splits[0]] = {};
						}
						_.each(object[splits[0]], function (obj) {

							mergedValue.push(
								getVal(obj[splits[1]])
							);

						});


					} else {

						mergedValue.push(
							getVal(object[propName])
						);

					}

				});

				mergedValue = _.without(mergedValue, '');
				mergedValue = _.uniq(mergedValue);
				mergedValue = mergedValue.join(', ');

				// For single values
			} else {

				var splits = [];
				if (options) {

					// If the option provides custom merging
					if (typeof options.parseMerge === 'function') {

						mergedValue = options.parseMerge(
							object[options.value]
						);

						// If a reference to a field on the obj
					} else if (options.value) {

						if (options.value.charAt(0) === '#') {

							splits = options.value.split('.');
							if (!object[splits[0]]) {
								object[splits[0]] = {};
							}

							mergedValue = [];
							_.each(object[splits[0]], function (obj) {

								mergedValue.push(
									getVal(obj[splits[1]])
								);

							});

						}

						// If the value is an object, place the object's name
					} else if (
						object[options.value]
						&& object[options.value].name
					) {

						mergedValue.push(
							object[options.value].name
						);

						// If its just a value, place it
					} else {

						mergedValue.push(object[options.value]);

					}

				}

			}

			// Update the value on the obj itself
			options.onUpdate(mergedValue);

			// Don't show hidden fields
			if (options.hidden) {
				return;
			}

			// Show the field
			sb.notify ({
				type: 'view-field'
				, data: {
					type: 		'plain-text'
					, property: 'val'
					, obj:		{
						val: mergedValue
					}
					, options: 	{
						edit: 		true
						, editing: 	true
						, commitChanges: false
						, title: 	'Title'
						, onUpdate: function (updated) {

							if (typeof options.onUpdate === 'function') {

								options.onUpdate(updated);

							}

						}
					}
					, ui: ui.makeNode('f', 'div', {})
				}
			});
			ui.patch();

		}

		function view (ui, blueprint, options, state) {

			function getEdgeOptions (fieldOptions, options) {

				_.each(appConfig.Types, function (type) {

					_.each(type.blueprint, function (field, key) {

						if (
							!_.contains(options.allowedTypes, field.type)
							&& !_.contains(options.allowedTypes, field.fieldType)
							&& !_.contains(options.allowedTypes, field.objectType)
							&& !_.contains(options.allowedTypes, 'any')
						) {
							return;
						}

						if (!field.is_archived) {

							var bpTxt = '#'+ type.bp_name.replace('.', '-');
							fieldOptions[bpTxt +'.'+ key] = _.clone(field);
							fieldOptions[bpTxt +'.'+ key].name = type.name +'<i class="grey arrow right icon"></i>'+ field.name;

							//!TODO: Make this recursive, looking at child blueprint
							// if the field is an edge.

						}

					});

				});

				return fieldOptions;

			}

			var selectedTxt = 'Select a field';
			var multiText = '';
			var fieldOptions = {};
			var parentProjOpts = {
				['parent.main_contact']: {
					name: 			'Parent <i class="ui right arrow grey icon"></i> Main contact',
					objectType: 	'contacts',
					type: 			'objectId'
				}
			};

			if (state.blueprintName === 'time_entries') {
				options.allowedTypes = ['any']
			}

			// Get options from current blueprint.
			_.each(blueprint, function (field, key) {

				if (field && field.is_archived) {
					return;
				}

				if (
					!_.contains(options.allowedTypes, field.type)
					&& !_.contains(options.allowedTypes, field.fieldType)
					&& !_.contains(options.allowedTypes, field.objectType)
					&& !_.contains(options.allowedTypes, 'any')
				) {
					return;
				}

				// Hide properties not being used on hardcoded data structures.
				if (
					key === 'user'
					|| key === 'users'
					|| key === 'project_lead'
					|| key === 'related_contacts'
					|| key === 'allowed_users'
				) {
					return;
				}

				fieldOptions[key] = field;

			});

			if (_.contains(options.allowedTypes, 'any')) {

				fieldOptions['name'] = {
					type: 			'string'
					, fieldType: 	'title'
					, name: 		'Name'
				};
			}

			// Get options from child entities.
			if (options.merge && !_.contains(options.allowedTypes, 'rootOnly')) {
				fieldOptions = getEdgeOptions(blueprint, options);
			}

			// Hardcoded pointers through parent for now..
			_.each(parentProjOpts, function (field, key) {

				if (
					!_.contains(options.allowedTypes, field.type)
					&& !_.contains(options.allowedTypes, field.fieldType)
					&& !_.contains(options.allowedTypes, field.objectType)
					&& !_.contains(options.allowedTypes, 'any')
				) {
					return;
				}

				fieldOptions[key] = field;

			});

			if (options.multi) {
				multiText = 'multiple ';
			}
			var dropdownSetup = {
				css: 'ui fluid '+ multiText +'selection dropdown',
				listener: {
					type: 'dropdown',
					onChange: function(value){

						if (options.multi) {
							value = value.split(',');
						}

						if (typeof options.onUpdate === 'function') {

							options.onUpdate(value);

						}

					}
				},
				placeholder: 'Priority'
			};

			if (options.multi) {

				// Carry over when field is changed from single to multi.
				if (!Array.isArray(options.value)) {

					if (_.isEmpty(options.value)) {

						options.value = [];

					} else {
						options.value = [options.value];
					}

				}

			}

			function viewList() {

				var columns = [];
				_.each(listData, function(listDataField) {

					_.each(blueprint, function(blueprintField, blueprintKey) {

						if (listDataField.id == blueprintKey) {

							var column = _.clone(blueprintField);
							_.each(listDataField, function(value, key) {
								column[key] = value;
							});

							columns.push(column);

						}

					});

				});

				var fields = {
					active: {
						title: 'Active',
						type: 'toggle',
						view: function(ui, obj) {

							function getIcon() {

								var color = obj.active ? 'blue' : 'grey';

								return '<i class="far fa-eye ' + color + '" style="margin-top:2px !important; margin-right:10px; font-size:17px; cursor:pointer;"></i>';

							}

							ui.makeNode('activeToggle', 'div', {
								text: getIcon(),
								tooltip: 'Show/Hide Column',
								listener: {
									type: 'popup',
									hoverable: true
								}
							}).notify('click', {
								type:'fields-run',
								data:{
									run:function() {

										obj.active = !obj.active;

										_.each(listData, function(field) {
											if (obj.id === field.id) {
												field.active = obj.active;
											}
										});

										if (typeof options.onUpdate === 'function') {
											options.onUpdate(listData);
										}

										$(ui.activeToggle.selector).html(getIcon());

									}
								}
							});

						}
					}
				}

				if (options.listPicker.hasOwnProperty('metrics')) {
					if (options.listPicker.metrics) {
						if (options.listPicker.metrics.hasOwnProperty('sum')) {
							if (options.listPicker.metrics.sum) {

								fields.metrics = {
									title: 'Metrics',
									type: 'toggle',
									view: function(ui, obj) {

										function getIcon() {

											var color = obj.metrics.sum ? 'blue' : 'grey';

											return '<i class="far fa-sigma ' + color + '" style="margin-top:2px !important; margin-right:10px; font-size:17px; cursor:pointer;"></i>';

										}

										var setup = {};

										if (
											( obj.fieldType === 'currency' ||
												obj.fieldType === 'quantity' ||
												obj.fieldType === 'duration' )
											&& obj.id != 'rate'
										) {

											setup = {
												text: getIcon(),
												tooltip: 'Show/Hide Sum',
												listener: {
													type: 'popup',
													hoverable: true
												}
											}

										} else {

											setup = {
												text: '-',
												style: 'margin-right:10px; font-size:17px; color:#BBBBBB; min-width:10px; text-align:center;',
											}

										}

										ui.makeNode('sumToggle', 'div', setup).notify('click', {
											type:'fields-run',
											data:{
												run:function() {

													obj.metrics.sum = !obj.metrics.sum;

													_.each(listData, function(field) {
														if (obj.id === field.id) {
															field.metrics.sum = obj.metrics.sum;
														}
													});

													if (typeof options.onUpdate === 'function') {
														options.onUpdate(listData);
													}

													$(ui.sumToggle.selector).html(getIcon());

												}
											}
										});

									}
								}

							}
						}
					}
				}

				fields.name = {
					title: 'Name',
					type: 'title',
					view: function(ui, obj) {

						sb.notify({
							type: 'view-field',
							data: {
								ui: ui,
								type: 'title',
								obj: obj,
								fieldName: 'name',
								property: 'name',
								options: {
									inCollection: true,
									style: 'margin:0 !important;'
								}
							}
						});

					}
				}

				sb.notify({
					type:'field-list-view',
					data: {
						container: ui,
						list: columns,
						options: {
							actions: false,
							emptyMessage: false,
							style: 'padding:0px;',
							isSortable: true,
							returnArrayOnSort: true,
							fields: fields,
							onSort: function(data) {

								_.each(data.updatedList, function(updatedItem) {

									_.each(listData, function(item) {

										if (item.id === updatedItem.id) {

											item.sort = updatedItem.sort;

										}

									});

								});

								listData = _.sortBy(listData, 'sort');

								if (typeof options.onUpdate === 'function') {
									options.onUpdate(listData);
								}

							}
						}
					}
				});

			}

			if (options.value) {

				if (
					options.multi
				) {

					listData = [];
					i = 0;

					_.each(fieldOptions, function (field, key) {

						if (
							!_.contains(options.allowedTypes, field.type)
							&& !_.contains(options.allowedTypes, field.fieldType)
							&& !_.contains(options.allowedTypes, field.objectType)
							&& !_.contains(options.allowedTypes, 'any')
						) {
							return;
						}

						// Hide properties not being used on hardcoded data structures.
						if (
							key === 'user'
							|| key === 'users'
							|| key === 'project_lead'
							|| key === 'related_contacts'
							|| key === 'allowed_users'
						) {
							return;
						}

						var metrics = {};

						listData.push({
							id: key,
							active: false,
							sort: i,
							metrics: metrics
						});

						i++;

					});

					// Update sort order
					_.each(options.value, function(field) {

						_.each(listData, function(item) {

							if (item.id === field.id) {

								item.active = field.active;
								item.sort = field.sort;
								item.metrics = !_.isEmpty(field.metrics) ? field.metrics : {};

							}

						});

					});

					listData = _.sortBy(listData, 'sort');

				}

			}

			viewList();

			// Set single select options.
			if (!options.multi) {

				_.each(fieldOptions, function (field, key) {

					if (
						!_.contains(options.allowedTypes, field.type)
						&& !_.contains(options.allowedTypes, field.fieldType)
						&& !_.contains(options.allowedTypes, field.objectType)
						&& !_.contains(options.allowedTypes, 'any')
						&& (
							key !== 'parent'
							&& !_.contains(options.allowedTypes, 'parent')
						)
					) {
						return;
					}

					// Hide properties not being used on hardcoded data structures.
					if (
						key === 'user'
						|| key === 'users'
						|| key === 'project_lead'
						|| key === 'related_contacts'
						|| key === 'allowed_users'
					) {
						return;
					}

					var icon = sb.dom.getFieldIcon(blueprint, field, key);

					ui.field.select.menu.makeNode('p'+ key, 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'value'
								, value: key
							}
						]
					})
						.makeNode('txt', 'div', {
							css: 'text'
							, tag: 'span'
							, text: '<i class="grey '+ icon +' icon"></i> '+ field.name
						});

				});

			}

			ui.patch();

		}

		if (
			options.merge
			&& state
		) {

			mergedView(
				ui
				, blueprint
				, options
				, state
			);

		} else {

			view(
				ui
				, blueprint
				, options
				, state
			);

		}

	}

	function fieldSelection (ui, blueprint, options, state) {

		function mergedView (ui, blueprint, options, object) {

			function getVal (obj) {

				var ret = '';
				if (typeof options.parseMerge === 'function') {

					ret = options.parseMerge(
						obj
					);

				} else if (
					obj
					&& obj.name
				) {

					ret = obj.name;

				} else {

					ret = obj;

				}

				return ret;

			}

			// Get the value to replace/merge into the form
			var mergedValue = '';

			// For options w/ multiple values
			if (Array.isArray(options.value)) {

				mergedValue = [];

				_.each(options.value, function (propName) {

					var splits = [];
					if (propName.charAt(0) === '#') {

						splits = propName.split('.');

						// Reset to dot seperator, for subsets
						splits[0] = splits[0].split('-').join('.');

						if (!object[splits[0]]) {
							object[splits[0]] = {};
						}
						_.each(object[splits[0]], function (obj) {

							mergedValue.push(
								getVal(obj[splits[1]])
							);

						});


					} else {

						splits = propName.split('.');
						if (Array.isArray(splits) && splits.length === 2) {

							mergedValue.push(
								getVal(object[splits[0]][splits[1]])
							);

						} else {

							mergedValue.push(
								getVal(object[propName])
							);

						}

					}

				});

				mergedValue = _.without(mergedValue, '');
				mergedValue = _.uniq(mergedValue);
				mergedValue = mergedValue.join(', ');

				// For single values
			} else {

				var splits = [];
				if (options) {

					// If the option provides custom merging
					if (typeof options.parseMerge === 'function') {

						mergedValue = options.parseMerge(
							object[options.value]
						);

						// If a reference to a field on the obj
					} else if (options.value) {

						if (options.value.charAt(0) === '#') {

							splits = options.value.split('.');
							if (!object[splits[0]]) {
								object[splits[0]] = {};
							}

							mergedValue = [];
							_.each(object[splits[0]], function (obj) {

								mergedValue.push(
									getVal(obj[splits[1]])
								);

							});

						}

						// If the value is an object, place the object's name
					} else if (
						object[options.value]
						&& object[options.value].name
					) {

						mergedValue.push(
							object[options.value].name
						);

						// If its just a value, place it
					} else {

						mergedValue.push(object[options.value]);

					}

				}

			}

			// Update the value on the obj itself
			options.onUpdate(mergedValue);

			// Don't show hidden fields
			if (options.hidden) {
				return;
			}

			// Show the field
			sb.notify ({
				type: 'view-field'
				, data: {
					type: 		'plain-text'
					, property: 'val'
					, obj:		{
						val: mergedValue
					}
					, options: 	{
						edit: 		true
						, editing: 	true
						, commitChanges: false
						, title: 	'Title'
						, onUpdate: function (updated) {

							if (typeof options.onUpdate === 'function') {

								options.onUpdate(updated);

							}

						}
					}
					, ui: ui.makeNode('f', 'div', {})
				}
			});
			ui.patch();

		}

		function view (ui, blueprint, options, state) {

			function getEdgeOptions (fieldOptions, options) {

				_.each(appConfig.Types, function (type) {

					_.each(type.blueprint, function (field, key) {

						if (
							!_.contains(options.allowedTypes, field.type)
							&& !_.contains(options.allowedTypes, field.fieldType)
							&& !_.contains(options.allowedTypes, field.objectType)
							&& !_.contains(options.allowedTypes, 'any')
						) {
							return;
						}

						if (!field.is_archived) {

							var bpTxt = '#'+ type.bp_name.replace('.', '-');
							fieldOptions[bpTxt +'.'+ key] = _.clone(field);
							fieldOptions[bpTxt +'.'+ key].name = type.name +'<i class="grey arrow right icon"></i>'+ field.name;

							//!TODO: Make this recursive, looking at child blueprint
							// if the field is an edge.

						}

					});

				});

				return fieldOptions;

			}

			var selectedTxt = 'Select a field';
			var multiText = '';
			var fieldOptions = {};
			var parentProjOpts = {
				['parent.main_contact']: {
					name: 			'Parent <i class="ui right arrow grey icon"></i> Main contact',
					objectType: 	'contacts',
					type: 			'objectId'
				}
			};

			// Get options from current blueprint.
			_.each(blueprint, function (field, key) {

				if (field && field.is_archived) {
					return;
				}

				if (
					!_.contains(options.allowedTypes, field.type)
					&& !_.contains(options.allowedTypes, field.fieldType)
					&& !_.contains(options.allowedTypes, field.objectType)
					&& !_.contains(options.allowedTypes, 'any')
				) {
					return;
				}

				// Hide properties not being used on hardcoded data structures.
				if (
					key === 'user'
					|| key === 'users'
					|| key === 'project_lead'
					|| key === 'related_contacts'
					|| key === 'allowed_users'
				) {
					return;
				}

				fieldOptions[key] = field;

			});

			if (_.contains(options.allowedTypes, 'any')) {

				fieldOptions['name'] = {
					type: 			'string'
					, fieldType: 	'title'
					, name: 		'Name'
				};
			}

			if (_.contains(options.allowedTypes, 'parent')) {
				fieldOptions.parent = {
					type: 			'int'
					, fieldType: 	'pointer'
					, name: 		'Parent'
					, icon: 		'external square'
					, options: {
						multi: false
					}
				};
			}
			if (_.contains(options.allowedTypes, 'current_record')) {
				fieldOptions.id = {
					type: 			'int'
					, fieldType: 	'edge'
					, name: 		'Current Record'
					, options: {
						multi: false
					}
				};
			}

			// Get options from child entities.
			if (options.merge && !_.contains(options.allowedTypes, 'rootOnly')) {
				fieldOptions = getEdgeOptions(blueprint, options);
			}

			// Hardcoded pointers through parent for now..
			_.each(parentProjOpts, function (field, key) {

				if (
					!_.contains(options.allowedTypes, field.type)
					&& !_.contains(options.allowedTypes, field.fieldType)
					&& !_.contains(options.allowedTypes, field.objectType)
					&& !_.contains(options.allowedTypes, 'any')
				) {
					return;
				}

				fieldOptions[key] = field;

			});

			if (options.multi) {
				multiText = 'multiple ';
			}
			var dropdownSetup = {
				css: 'ui fluid '+ multiText +'selection dropdown',
				listener: {
					type: 'dropdown',
					onChange: function(value){

						if (options.multi) {
							value = value.split(',');
						}

						if (typeof options.onUpdate === 'function') {

							options.onUpdate(value);

						}

					}
				},
				placeholder: 'Priority'
			};

			if (options.multi) {

				// Carry over when field is changed from single to multi.
				if (!Array.isArray(options.value)) {

					if (_.isEmpty(options.value)) {

						options.value = [];

					} else {
						options.value = [options.value];
					}

				}

			}

			if (options.value) {

				if (
					options.multi
				) {

					dropdownSetup.listener.values = [];
					_.each(fieldOptions, function (field, key) {

						if (
							!_.contains(options.allowedTypes, field.type)
							&& !_.contains(options.allowedTypes, field.fieldType)
							&& !_.contains(options.allowedTypes, field.objectType)
							&& !_.contains(options.allowedTypes, 'any')
						) {
							return;
						}

						// Hide properties not being used on hardcoded data structures.
						if (
							key === 'user'
							|| key === 'users'
							|| key === 'project_lead'
							|| key === 'related_contacts'
							|| key === 'allowed_users'
						) {
							return;
						}

						var icon = sb.dom.getFieldIcon(blueprint, field, key);
						if (_.isEmpty(icon) && typeof field.icon === 'string') {
							icon = field.icon;
						}

						dropdownSetup.listener.values.push({
							name: 		'<i class="grey '+ icon +' icon"></i> '+ field.name +'</div>'
							, value: 	key
							, selected: _.contains(options.value, key)
						});

					});

					selectedTxt = '<div class="default text"></div> <i class="dropdown icon"></i>';
					dropdownSetup.text = selectedTxt;

				} else {

					if (fieldOptions[options.value]) {

						var icon = sb.dom.getFieldIcon(blueprint, fieldOptions[options.value], options.value);
						if (_.isEmpty(icon) && typeof fieldOptions[options.value].icon === 'string') {
							icon = fieldOptions[options.value].icon;
						}
						selectedTxt = '<i class="grey '+ icon +' icon"></i> '+ fieldOptions[options.value].name;

					}

				}

			}

			dropdownSetup.text = '<div class="text">'+ selectedTxt +'</div> <i class="dropdown icon"></i>';

			ui.makeNode('field', 'div', {})
			ui.field.makeNode('select', 'div', dropdownSetup).makeNode(
				'menu'
				, 'div'
				, {
					css: 'menu'
				}
			);

			if ( options.prohibitedFields && Array.isArray(options.prohibitedFields)){

				fieldOptions = _.reduce(fieldOptions, function(memo, val, key, obj){

					if ( !_.contains(options.prohibitedFields, key) ) {
						memo[key] = val;
					}

					return memo;

				}, {});

			}

			// Set single select options.
			if (!options.multi) {

				_.each(fieldOptions, function (field, key) {

					if (
						!_.contains(options.allowedTypes, field.type)
						&& !_.contains(options.allowedTypes, field.fieldType)
						&& !_.contains(options.allowedTypes, field.objectType)
						&& !_.contains(options.allowedTypes, 'any')
						&& (
							key !== 'parent'
							&& !_.contains(options.allowedTypes, 'parent')
						)
					) {
						return;
					}

					// Hide properties not being used on hardcoded data structures.
					if (
						key === 'user'
						|| key === 'users'
						|| key === 'project_lead'
						|| key === 'related_contacts'
						|| key === 'allowed_users'
					) {
						return;
					}

					var icon = sb.dom.getFieldIcon(blueprint, field, key);

					ui.field.select.menu.makeNode('p'+ key, 'div', {
						css: 'item'
						, dataAttr: [
							{
								name: 'value'
								, value: key
							}
						]
					})
						.makeNode('txt', 'div', {
							css: 'text'
							, tag: 'span'
							, text: '<i class="grey '+ icon +' icon"></i> '+ field.name
						});

				});

			}

			ui.patch();

		}

		if (
			options.merge
			&& state
		) {

			mergedView(
				ui
				, blueprint
				, options
				, state
			);

		} else {

			view(
				ui
				, blueprint
				, options
				, state
			);

		}

	}

	// Viewing fields

	function viewField (field, node, data) {

		function drawField (field, node, data) {

			var errorMsg = {
				text: '<i class="ui warning icon"></i> Uh-oh--there was a problem loading the field--let us know so we can fix it for you!'
				, css: 'ui error message'
			};

			var useFieldListener = field.useFieldListener;

			if ( node.options && node.options.hasOwnProperty('useFieldListener') )
				useFieldListener = node.options.useFieldListener;

			if (
				typeof field.edit === 'function'
				&& useFieldListener
				&& !data.options.bypassFieldListener
				&& data.ui
			) {

				var css = 'edge-field';
				if (typeof field.css === 'string') {
					css = field.css;
				}
				var validInstances = appConfig.instance === 'foundation_group' || appConfig.instance === 'rickyvoltz';
				if(data.type == 'detail' && data.options.editBlueprint === false && validInstances) {
					//render dropdown
					data.ui.makeNode('b', 'div', {});
					function generateSnippet(data) {
						wrapWithSnippets(data.property, data.obj, function (_snippets) {
							data.options['snippets'] = _snippets;
							buildDropDownOptions(data.ui.b, data, data.options, function (data){
								generateSnippet(data);
							}, field);
							data.ui.b.patch();
						});
					}

					generateSnippet(data);
				}

				//render field div
				data.ui.makeNode(
					'f'
					, 'div'
					, {
						css: 	css
						, id: 	node._id
						, data: {
							'field-id': node._id
						}
					}
				);

				if (data.options && data.options.shouldPatchInFieldContainer) {
					data.ui.patch();
				}

				if (
					node.options
					&& node.options.editing
				) {

// 					data.ui.patch();

					if ( data.options && data.options.edit){

						try {

							field.edit(
								data.property
								, data.ui.f
								, data.obj
								, data.options
							);

							data.ui.patch();

						} catch (err) {

							data.ui.f.makeNode('msg', 'div', errorMsg);
							console.log('There was a problem loading the field\'s edit view: ', err, data);

						}
					}


				} else {

					try {

						field.view(
							data.property
							, data.ui.f
							, data.obj
							, data.options
						);

					} catch (err) {

						data.ui.f.makeNode('msg', 'div', errorMsg);
						console.log('There was a problem loading the field\'s view: ', err, data);

					}

				}

			} else {

				try {

					field.view(
						data.property
						, data.ui
						, data.obj
						, data.options
					);

				} catch (err) {

					// data.ui.makeNode('msg', 'div', errorMsg);
					console.log('There was a problem loading the field\'s view: ', err, data);

				}

			}

		}

		if (field) {

			data.options._fieldId = node._id;
			drawField(field, node, data);

			Nodes.push(node);

			if (typeof field.detail === 'function' && field.name !== 'detail') {

				var detailTxt = field.detail(
					data.property
					, data.obj
					, data.options
				);

				if (!_.isEmpty(detailTxt) && data.ui) {

					data.ui.makeNode(
						data.obj.id +'updated_on'
						, 'div'
						, {
							css: 'ui inverted custom popup'
							, text: detailTxt
						}
					);

					data.ui.listeners.push(
						function (selector) {

							var set = false;

							$(data.ui.selector)
								.children()
								.first()
								.hover(function () {

									if (!set) {

										$(selector)
											.children()
											.first()
											.popup({
												popup: $(data.ui[data.obj.id +'updated_on'].selector)
												, on: 'hover'
											});

										$(selector)
											.children()
											.first()
											.popup('show');

										set = true;

									}

								});

						}
					);

				}

				return true;

			}

			return true;

		} else if (data.type === 'field') {

			function renderEditor(){
				wrapWithSnippets(data.property, data.obj, function(_snippets){
					data.options['snippets'] = _snippets;
					fieldEditor (
						data.property
						, data.ui
						, data.obj
						, data.options
						, function(){
							renderEditor();
						}
					);
					data.ui.patch();
				});
			}

			renderEditor();
		}

	}

	return {

		initListeners: function() {

			sb.listen({

				// user API
				'view-field': 				this.view
				, 'view-field-selection': 	this.viewFieldSelection

				// field view API
				, 'register-field-type': 	this.register

				// refresh state
				, 'get-field-merge-tags': 	this.getFieldMergeTags
				, 'page-changed': 			this.refresh
				, 'clear-collections-fields': this.clearCollectionsFields
				, 'field-updated': 			this.runUpdates
				, 'run-validations':        this.runValidations
				, 'get-field-id':           this.runGetFieldId

				// actions
				, 'apply-inherit-tags':		this.applyInheritTags

				// internal use
				, 'fields-run': 			this.run

				// get options
				, 'show-edit-option-modal':	this.showEditOptionModal
				, 'remove-option':			this.removeOption

			});

		}

		, applyInheritTags: function(data) {

			var options = data.options;
			var parent = data.parent;
			var obj = data.obj;
			var onComplete = data.onComplete;

			if (options.inheritsTags && parent) {

				var parentId = '';

				if (parent.hasOwnProperty('id')) {
					if (parent.id) {
						parentId = parent.id;
					}
				} else if (Number.isInteger(parent)) {
					parentId = parent;
				}

				if (parentId) {

					sb.data.db.obj.getById(
						parent.object_bp_type
						, parentId
						, function (parent) {

							if (parent) {
								obj.tagged_with.push(parent.id);
								obj.tagged_with = obj.tagged_with.concat(parent.tagged_with);
							}

							onComplete(obj);

						},
						{
							tagged_with: true
						}
					);

				} else {

					onComplete(obj);

				}


			} else {

				onComplete(obj);

			}

		}

		, init: function() {}

		, destroy: function () {


		}

		// user API

		, getFieldMergeTags: function (data) {

			data.callback(
				getFieldMergeTags(data.rootSet, data.options)
			);

		}

		, view: function (data) {

			var field = _.findWhere(
				Types
				, {
					name: data.type
				}
			);

			var node = {
				_id: 		Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
				, _collId: 	data.collId || false
				, obj: 		_.clone(data.obj)
				, objId: 	data.obj.id
				, options: 	data.options
				, property: data.property
				, type:		data.type
				, ui: 		data.ui
// 				, selector: ui.selector
			};

			// For metrics
			if (data.isMetric) {

				node.objectType = data.obj.object_bp_type;
				node.isMetric = true;

			}

			if (data.options.edit) {
				data.options.onEditStart = function () { $(this.selector).addClass('active'); }.bind(data.ui);
				data.options.onEditEnd = function () { $(this.selector).removeClass('active'); }.bind(data.ui);
			}

			// Check permissions on blueprinted fields.
			if (
				data
				&& data.options
				&& data.options.blueprint
				&& data.options.blueprint[data.property]
			) {

				if (!IN_NODE_ENV) {

					sb.notify({
						type: 'check-permissions'
						, data: {
							permissionSet: data.options.blueprint[data.property].permissions
							, onComplete: function (r) {

								// Edit permissions
								if (r.edit === false) {

									data.options.edit = false;
									data.options.editing = false;

								}

								// Bring options stored in bp to root lvl of options
								// as the default.
								if (data.options.blueprint[data.property].options) {

									_.each(
										data.options.blueprint[data.property].options
										, function (val, key) {

											if (!data.options.hasOwnProperty(key)) {
												data.options[key] = val;
											}

										}
									);

								}

								viewField(field, node, data);

							}
						}
					});

				} else {

					viewField(field, node, data);

				}

			} else {

				viewField(field, node, data);

			}

			updateListeners();
			return false;

		}

		, viewFieldSelection: function (data) {

			fieldSelection(
				data.ui
				, data.blueprint
				, data.options
				, data.state
			);

		}

		// field view API

		, register: function (field) {

			var shouldPatch = true;
			if (field.hasOwnProperty('shouldPatch')) {
				shouldPatch = field.shouldPatch;
			}

			Types.push({
				name: 					field.name
				, blockRefresh: 		field.blockRefresh
				, destroy: 				field.destroy || function () {}
				, detail: 				field.detail
				, title: 				field.title
				, icon: 				field.icon
				, getIcon: 				field.getIcon
				, type: 				field.type
				, propertyType:			field.propertyType
				, objectType:			field.objectType
				, options: 				field.options
				, parseOptions: 		field.parseOptions
				, select: 				field.select
				, shouldPatch: 			shouldPatch
				, availableToEntities: 	field.availableToEntities
// 				, update: 	field.update

				// Views
				, view: 				field.view
				, edit: 				field.edit
				, focus: 				field.focus
				, focusout:             field.focusout
				, selector: 			field.selector
				, css: 					field.css || false

				// Turns on separation between view/edit modes
				, useFieldListener: 	field.useFieldListener || false
				, usePopup: 			field.usePopup || false
				, hideDefault:			field.hideDefault || false
			});

			appConfig.fieldTypes = _.pluck(Types, 'name');

		}

		, run: function (data) {

			data.run(data);

		}

		, runGetFieldId: function(data) {
			// Gather nodes of the same object and property.
			var nodes = _.where(
				Nodes
				, {
					objId: 		data.obj.id
					, property: data.property
				}
			);

			// Gather metrics nodes of the object's type.
			nodes = nodes.concat(
				_.where(
					Nodes
					, {
						isMetric: 		true
						, objectType: 	data.obj.object_bp_type
						, property: 	data.property
					}
				)
			);

			// Gather updates for fields on an entity that talk to each other.
			if (data.type) {

				nodes = nodes.concat(
					_.where(
						Nodes
						, {
							objId:  		data.obj.id
							, property: 	'any-'+ data.type
						}
					)
				);

			}

			data.callback(nodes);
		}

		, runValidations: function (data) {

			var nodes = _.where(
				Nodes
				, {
					objId: data.obj.id
					, property: data.property
				}
			);

			// Gather metrics nodes of the object's type.
			nodes = nodes.concat(
				_.where(
					Nodes
					, {
						isMetric: 		true
						, objectType: 	data.obj.object_bp_type
						, property: 	data.property
					}
				)
			);

			// Gather updates for fields on an entity that talk to each other.
			if (data.type) {

				nodes = nodes.concat(
					_.where(
						Nodes
						, {
							objId:  		data.obj.id
							, property: 	'any-'+ data.type
						}
					)
				);

			}
			
			//clear validations
			try {
				_.each(Nodes, function (node) {

					var nodeUI = node.ui;

					if (nodeUI.hasOwnProperty('f') && nodeUI.f) {
						nodeUI = node.ui.f;
					}

					var _object = $('#' + node._id)

					if (_object.selector) {
						$('#' + node._id).removeClass('having-error');
					} else {
						var _css = $(nodeUI.selector).children().attr("style");
						if(_css) {
							_css = _css.replace('border:1px red solid', '');
							$(nodeUI.selector).children().attr("style", _css);
						}
					}

				});
			}catch(e){
				console.log('error: ', e);
			}

			var errorNode = [];

			_.each(nodes, function (node) {

				if (node) {

					var nodeUI = node.ui;

					if (nodeUI.hasOwnProperty('f') && nodeUI.f) {
						nodeUI = node.ui.f;
					}

					var _object = $('#' + node._id);

					if(_object.length > 0){
						_object.addClass('having-error');
						if(errorNode){
							errorNode.push('#' + node._id);
						}
					} else {
						$(nodeUI.selector).children().css("border", "1px red solid");
						if(errorNode){
							errorNode.push(nodeUI.selector);
						}
					}

				}

			});

			if(errorNode.length > 0) {
				$('#spaceViewCol').animate({
					scrollTop: $(errorNode[0]).offset().top - 10
				}, 1000);
			}

		}

		, runUpdates: function (data) {

			// Gather nodes of the same object and property.
			var nodes = _.where(
				Nodes
				, {
					objId: 		data.obj.id
					, property: data.property
				}
			);

			// Gather metrics nodes of the object's type.
			nodes = nodes.concat(
				_.where(
					Nodes
					, {
						isMetric: 		true
						, objectType: 	data.obj.object_bp_type
						, property: 	data.property
					}
				)
			);

			// Gather updates for fields on an entity that talk to each other.
			if (data.type) {

				nodes = nodes.concat(
					_.where(
						Nodes
						, {
							objId:  		data.obj.id
							, property: 	'any-'+ data.type
						}
					)
				);

			}

			_.each(nodes, function (node) {

				var field = _.findWhere(
					Types
					, {
						name: node.type
					}
				);

				if (
					field
					&& (
						!field.blockRefresh
						|| node.isMetric
					)
				) {

					//!TODO: refactor these checks into callbacks in field
					// registration.
					if (
						node.obj[node.property] != data.obj[node.property]
						//!TODO: Refactor to state field reg.
						|| (
							data.obj.type
							&& node.type === 'state'
							&& !_.isEmpty(node.obj.type)
							&& !_.isEmpty(data.obj.type)
							&& node.obj.type.id != data.obj.type.id
						)
						//!TODO: Refactor to timer field reg.
						|| (
							node.type === 'timer'
							&& (
								( node.obj[node.property +'_est'] != data.obj[node.property +'_est'] )
								||
								( node.obj[node.property +'_rate'] != data.obj[node.property +'_rate'] )
								||
								( data.obj.hasOwnProperty('tracking') && data.obj.tracking === true )
								||
								( data.obj.hasOwnProperty('deleted') && data.obj.deleted === true )
							)
						)
						//!TODO: Refactor to reference field reg.
						|| (
							node.type === 'reference'
							&& node.obj[node.property +'_lockedOn'] != data.obj[node.property +'_lockedOn']
						)

						|| node.property === 'any-'+ data.type
					) {

						_.mapObject(data.obj, function(val, key) {

							node.obj[key] = data.obj[key];

						});

						node.options.isRefreshing = true;

						if (data._fieldId) {
							node.options.refreshTriggeredBy = data._fieldId;
						}

						//!TODO: Refactor to timer field reg.
						if (data.obj.object_bp_type === 'groups') {

							if (data.property === 'time_estimate' || data.property === 'rate') {

								var projectTimerNode = _.where(
									Nodes
									, {
										objId: 		data.obj.id
										, property: 'time_logged'
									}
								);

								var projectTimerField = _.findWhere(
									Types
									, {
										name: 'timeTracking'
									}
								);

								if (!_.isEmpty(projectTimerNode) && !_.isEmpty(projectTimerField)) {
									node = projectTimerNode[0];
									field = projectTimerField;
								}

							}

						}

						var nodeUI = node.ui;

						if (nodeUI.hasOwnProperty('f') && nodeUI.f) {
							nodeUI = node.ui.f;
						}

						field.view(
							node.property
							, nodeUI
							, node.obj
							, node.options
						);

						if (field.shouldPatch) {
							if (node.ui) {
								node.ui.patch();
							}
						}

					}

				}

			});

		}

		, refresh: function () {

			_.each(Nodes, function (node) {

				var type = _.findWhere(
					Types
					, {
						name: node.type
					}
				);

				if (typeof type.destroy === 'function') {

					type.destroy(
						node._id
						, node.property
						, node.ui
						, node.obj
						, node.options
					);

				}

			});

			Nodes = [];

		}

		, clearCollectionsFields: function (data) {

			_.each(Nodes, function (node) {

				var type = _.findWhere(
					Types
					, {
						name: node.type
					}
				);

				if (
					data.collId === node._collId
					&& typeof type.destroy === 'function'
				) {

					type.destroy(
						node._id
						, node.property
						, node.ui
						, node.obj
						, node.options
					);

				}

			});

			Nodes = _.filter(Nodes, function (node) {

				return node._collId !== data.collId;

			});

		}

		, showEditOptionModal: function(data) {

			showEditOptionModal(data.ui, data.obj, data.option, data.options, data.fieldName, data.view);

		}

		, removeOption: function(data) {

			removeOption(data.ui, data.obj, data.option, data.options, data.fieldName, data.view);

		}

	}

});
