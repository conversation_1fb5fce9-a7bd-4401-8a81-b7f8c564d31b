var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('tags-field', function (sb) {

	if (IN_NODE_ENV) {
        return;
    }

	//!TODO: fix this in tags component.
	var Comps = {};

	function View(fieldName, ui, obj, options) {

		if (options.formView) {
			
			setTimeout(function () {
				
				Comps[obj.id +'-'+ fieldName] = sb.createComponent('tags');
				Comps[obj.id +'-'+ fieldName].notify({
					type: 'object-tag-view',
					data: {
						domObj: 		ui,
						objectType: 	'',
						tags: 			options.defaultValue || [],
						onChange: 		function (key, response) {
		
							obj[key] = _.pluck(response, 'id');
		
						}.bind({}, fieldName),
						canEdit: true
					}
				});	
				ui.patch();
				
			}, 500);
			
			return;
			
		}
		
		if (Comps.tags) {
			Comps.tags.destroy();
		}
		
		Comps.tags = sb.createComponent('tags');
		Comps.tags.notify({
			type: 'object-tag-view',
			data: {
				domObj: 		ui,
				objectType: 	obj.object_bp_type,
				objectId: 		obj.id,
				tags: 			options.tagList,
				canEdit: 		options.canEdit,
				options: 		options,
				onChange: 		options.onChange
			}
		});

	}

	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 					'tags',
					view: 				View,
					title: 				'Tags',
					availableToEntities: 	false,
					icon: 				'tags',
					shouldPatch: 			false,
					detail: function (key, obj, options) {
						return obj[key];
					}
				}
			});

			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 					'tagset'
					, title: 				'Tags'
					, availableToEntities: 	false
					, icon: 				'tags'
					, shouldPatch: 			false
					, view: function (
							fieldName
							, ui
							, obj
							, options
						) {
						
						options.formView = true;
						View(
							fieldName, ui, obj, options
						);
						
					}
				}
			});
			
		}

	};

});
