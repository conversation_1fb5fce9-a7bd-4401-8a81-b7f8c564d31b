var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');
	var moment = require('moment');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('date-field', function (sb) {

	var Formats = {
		date: 'MMM D, YYYY'
		, datetime: 'MMM D, YYYY h:mm a'
		, time: 'h:mm a'
		, raw: 'YYYY-MM-DD HH:mm:ss ZZ'
	};

	function View(fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			var m = moment(obj[fieldName], 'YYYY-MM-DD HH:mm:ss.SS');

			if (m.isValid()) {
				ret += m.local().format('MM/DD/YY, h:mm a');
			}

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}

		function createCompletionButton(ui, obj, options, doPatch) {

			var isComplete = 0;
			var buttonText = 'Not Complete';
			var buttonIcon = 'circle outline';
			if (obj[fieldName + '_done']) {
				if (obj[fieldName + '_done'] == 1) {
					isComplete = 1;
					buttonText = 'Complete';
					buttonIcon = 'check';
				}
			}

			ui.makeNode('txt', 'div', {
				text: '<i class="' + buttonIcon + ' icon"></i> ' + buttonText,
				css: 'ui basic icon button'
			}).notify('click', {
				type: 'fields-run',
				data: {
					run: function () {

						$(ui.txt.selector).addClass('loading');

						var updateObj = {
							id: obj.id
						};

						if (isComplete == 1) {
							updateObj[fieldName + '_done'] = 0;
						} else {
							updateObj[fieldName + '_done'] = 1;
						}

						sb.data.db.obj.update(obj.object_bp_type, updateObj, function (updated) {

							// sb.notify({
							// 	type: 'field-updated'
							// 	, data: {
							// 		obj: updated
							// 		, type: 'date'
							// 		, property: fieldName
							// 	}
							// });
							options.edit = false;
							options.editing = false;
							options.isRefreshing = true;
							View(fieldName, doPatch, updated, options);

							//createCompletionButton(ui,updated,options,true);



							var setBlueprint = false;
							if (obj && typeof obj.object_bp_type === 'string') {
								setBlueprint = _.findWhere(appConfig.Types, { bp_name: obj.object_bp_type.substr(1) });
							}

							// Update date duration fields
							if (setBlueprint) {

								_.each(setBlueprint.blueprint, function (field, key) {

									if (field.fieldType === 'duration') {

										updated[key] = false;

										sb.notify({
											type: 'field-updated'
											, data: {
												obj: updated
												, type: 'duration'
												, property: key
											}
										});

									}

									if (field.fieldType === 'next-due-date') {

										//updated[key] = false;

										sb.notify({
											type: 'field-updated'
											, data: {
												obj: updated
												, type: 'next-due-date'
												, property: key
											}
										});

									}

								});

							}

						}, 1);

					}
				}
			}, sb.moduleId);

			if (doPatch) {
				ui.patch();
			}

		}

		var date = moment(obj[fieldName], Formats.raw);
		var txt = '';
		var showRelative = true;
		var defaultFlags = {
			style: ''
			, format: 'datetime'
		};

		if (options.dateType !== 'date') {
			date = date.local();
		}

		var disabledClass = '';
		var disableCursor = '';

		if (!options.edit && !options.is_due_date) {
			disabledClass = 'muted';
			disableCursor = 'cursor: not-allowed !important;';
		}

		if (!options.hasOwnProperty('commitUpdates')) { options.commitUpdates = true; }

		function checkOptions(options) {

			_.each(options, function (v, k) {

				if (defaultFlags.hasOwnProperty(k)) {

					defaultFlags[k] = v;

				}

			});

		}

		checkOptions(options);

		if (options.hasOwnProperty('showRelative')) {
			showRelative = options.showRelative;
		}

		if (options.dateType === 'day') {

			date = moment(obj[fieldName], Formats.raw);

		} else if (options.dateType === 'date') {

			date = moment(obj[fieldName], Formats.raw);

		} else if (!options.hasOwnProperty('dateType')) {

			options.dateType = 'datetime';

		}

		ui.empty();

		if (obj.is_recurring) {

			txt = obj.cycle_name;
			if (obj.repeat_forever) {
				txt += ', <i>forever</i>';
			} else {

				txt += ', <i>until ' + moment(obj.repeat_end_date, Formats.raw).local().format('l') + '</i>';

				if (options.dateType === 'day') {
					txt += ', <i>until ' + moment(obj.repeat_end_date, Formats.raw).format('l') + '</i>';
				}

			}

		} else if (date.isValid()) {

			switch (options.dateType) {

				case 'datetime':

					txt = date.format('l, h:mma');
					if (showRelative) {
						txt += '<i class="grey" style="font-weight:normal; font-size:12px; display:block;">' + date.from() + '</i>';
					}
					break;

				default:
					txt = date.format('l');
					if (showRelative) {
						txt += '<i class="grey" style="font-weight:normal; font-size:12px; display:block;">' + date.from() + '</i>';
					}
					break;

			}

		} else {

			txt = '<i class="text-muted">Not set</i>'

		}

		if (_.isEmpty(obj[fieldName])) {
			txt = '<i class="text-muted">Not set</i>';
		}

		var style = '';
		// mini mode
		if (options.mini) {

			if (_.isEmpty(obj[fieldName])) {
				return;
			}

			if (date.isValid()) {
				txt = date.format('MMM D');
			} else {
				txt = '';
			}

			if (_.isEmpty(obj[fieldName])) {
				txt = '';
			}

			if (options.end && obj[options.end]) {

				txt += ' - ' + moment(obj[options.end] + ' +0000', Formats.raw).local().format('MMM D');

			}

			if (options.hideInMini) {
				txt = '';
			}

		}

		style = style + ' ' + defaultFlags.style;

		// Add icon to the date text
		var icon = getIcon(fieldName, ui, obj, options);

		if (!_.isEmpty(icon)) {
			txt = '<i class="fas fa-' + icon.icon + '"></i> ' + txt;
			disabledClass = icon.color;
		}

		if (options.show_completion_checkbox && options.inCollection != true) {

			ui.makeNode('cont', 'div', { css: 'ui grid' })
			ui.cont.makeNode('col1', 'div', { css: 'ui six wide column' });
			ui.cont.makeNode('col2', 'div', { css: 'ui ten wide column' });

			ui.cont.col1.makeNode('txt', 'div', {
				text: '<nobr>' + txt + '</nobr>',
				style: style + ' ' + disableCursor,
				css: 'ui ' + disabledClass + ' label'
			});

			createCompletionButton(ui.cont.col2, obj, options, ui);

		} else {

			ui.makeNode('txt', 'div', {
				text: '<nobr>' + txt + '</nobr>',
				style: style + ' ' + disableCursor,
				css: 'ui ' + disabledClass + ' label'
			});

		}


		if (options.editing && options.edit) {

			if (options.show_completion_checkbox) {

				ui.cont.col1.txt.listeners.push(function (selector) {

					Edit(fieldName, ui.cont.col1, obj, options);

				});

			} else {

				ui.txt.listeners.push(function (selector) {

					Edit(fieldName, ui, obj, options);

				});

			}

			return;

		} else {

			if (options.show_completion_checkbox && !options.inCollection) {

				ui.cont.col1.txt.listeners.push(function (selector) {

					$(selector).on('click', function (e) {

						if (options.edit) {

							Edit(fieldName, ui, obj, options);

						}

					});

				});

			} else {

				ui.txt.listeners.push(function (selector) {

					$(selector).on('click', function (e) {

						if (options.edit) {

							Edit(fieldName, ui, obj, options);

						}

					});

				});

			}

		}

		if (options.isRefreshing) {
			ui.patch();
			return;
		}

	}

	function Edit(fieldName, ui, obj, options) {

		function closeEdit(newVal, skipUpdateNotification) {

			// Initialize the field
			obj[fieldName] = '';

			if (!_.isEmpty(newVal)) {

				var momentObj = moment(newVal, 'MMMM D, YYYY h:mm A');

				if (momentObj.isValid()) {

					// check for daylight savings time shift
					var dt = momentObj.format('YYYY-MM-DD HH:mm:ss');
					var nowTime = moment().format('YYYY-MM-DD HH:mm:ss');

					// If is daylight savings, and time selected outside of dst, 
					// shift an hour forward.
					if (
						!moment.tz(dt, moment.tz.guess()).isDST()
						&& moment.tz(nowTime, moment.tz.guess()).isDST()
					) {

						momentObj.add(1, 'hour');

						// If its not daylight savings, and time selected is in dst, 
						// shift back an hour.
					} else if (
						moment.tz(dt, moment.tz.guess()).isDST()
						&& !moment.tz(nowTime, moment.tz.guess()).isDST()
					) {
						momentObj.subtract(1, 'hour');
					}

					if (options.removeUTC) {
						obj[fieldName] = momentObj.subtract(sb.dom.utcOffset, 'hours').format(Formats.raw);
					} else {
						obj[fieldName] = momentObj.add(sb.dom.utcOffset, 'hours').format(Formats.raw);
					}

				}

			}

			if (!options.editing && skipUpdateNotification != true) {
				View(fieldName, ui, obj, options);
				ui.patch();
			}

			if (typeof options.onUpdate === 'function' && skipUpdateNotification != true) {
				options.onUpdate(obj[fieldName]);
			}

			if (options.commitUpdates) {

				sb.data.db.obj.update(
					obj.object_bp_type
					, {
						id: obj.id
						, [fieldName]: obj[fieldName]
					}
					, function (updated) {

						if (skipUpdateNotification != true) {

							sb.notify({
								type: 'field-updated'
								, data: {
									obj: obj
									, property: fieldName
									, updatedBy: options.fieldId
								}
							});

							if (options.hasOwnProperty('blueprint')) {

								if (options.blueprint) {

									//Edit (fieldName, ui, obj, options);

								} else {

									ui.patch();

								}

							} else {

								ui.patch();

							}

						}

						var setBlueprint = false;
						if (obj && typeof obj.object_bp_type === 'string') {
							setBlueprint = _.findWhere(appConfig.Types, { bp_name: obj.object_bp_type.substr(1) });
						}

						// Update date duration fields
						if (setBlueprint) {

							_.each(setBlueprint.blueprint, function (field, key) {

								if (skipUpdateNotification != true) {

									if (field.fieldType === 'duration') {

										updated[key] = false;

										sb.notify({
											type: 'field-updated'
											, data: {
												obj: updated
												, type: 'duration'
												, property: key
											}
										});

									}

									if (field.fieldType === 'next-due-date') {

										//updated[key] = false;

										sb.notify({
											type: 'field-updated'
											, data: {
												obj: updated
												, type: 'next-due-date'
												, property: key
											}
										});

									}

								}

							});

						}

					}
				);

			}

		}

		function processScheduleForm(form) {

			var schedInput = form.process().fields;
			var obj = {};

			obj.cycle = schedInput.cycle.value;
			obj.repeat_forever = false;

			if (
				schedInput.hasOwnProperty('repeat_forever')
				&& schedInput.repeat_forever.value
			) {

				obj.repeat_forever = true;

			} else if (obj.is_recurring) {

				obj.repeat_end_date = schedInput.repeat_end_date.value;

				if (!obj.repeat_end_date) {

					sb.dom.alerts.alert(
						'Incomplete Form',
						'Recurring tasks not set to repeat forever need an end date.',
						'error'
					);
					return false;

				}

			}

			obj.schedule_options = cycleOptions;
			return obj;

		}

		var isTemplate = (obj.is_template || fieldName === '_default');

		if (typeof options.onEditStart === 'function')
			options.onEditStart();

		var dateVal = (_.isEmpty(obj[fieldName]) || _.isNull(obj[fieldName]))
			? moment()
			: moment(obj[fieldName] + ' +0000', Formats.raw);

		if (options.dateType !== 'date') {
			dateVal = dateVal.local();
		}

		var cycleOptions = [];
		var dateTxt = dateVal.format('MMMM D, YYYY h:mm A');

		// Empty state, for entities.
		if (
			options
			&& options.blueprint
			&& _.isEmpty(obj[fieldName])
		) {
			dateTxt = '';
		}

		if (obj.is_recurring) {

			var repeatForeverVal = false;
			if (obj.repeat_forever) {
				repeatForeverVal = 'yes';
			}

			ui.makeNode('modal', 'modal', {});
			ui.modal.body.makeNode(
				'form',
				'form',
				{
					cycle: {
						name: 'cycle',
						type: 'select',
						label: 'Repeat',
						value: obj.cycle,
						options: [
							{
								name: 'Daily',
								value: 'daily'
							}, {
								name: 'Weekly',
								value: 'weekly'
							}/*
, {
								name:'Montly',
								value:'monthly'
							}, {
								name:'Yearly',
								value:'yearly'
							}
*/
						],
						change: function (form, value) {

							switch (value) {

								case 'weekly':
									$(ui.modal.body.form.cycle_options.selector).parent().removeClass('hidden');
									break;

								default:
									$(ui.modal.body.form.cycle_options.selector).parent().addClass('hidden');
									break;

							}

						}
					},
					cycle_options: {
						name: 'cycle_options',
						type: 'checkbox',
						label: 'Days of week',
						value: obj.schedule_options,
						options: [
							{
								name: 'cycle_options',
								value: 1,
								label: 'Mon'
							}, {
								name: 'cycle_options',
								value: 2,
								label: 'Tue'
							}, {
								name: 'cycle_options',
								value: 3,
								label: 'Wed'
							}, {
								name: 'cycle_options',
								value: 4,
								label: 'Thur'
							}, {
								name: 'cycle_options',
								value: 5,
								label: 'Fri'
							}, {
								name: 'cycle_options',
								value: 6,
								label: 'Sat'
							}, {
								name: 'cycle_options',
								value: 0,
								label: 'Sun'
							}
						],
						onChange: function (val) {
							cycleOptions = val;
						}
					},
					repeat_forever: {
						name: 'repeat_forever',
						type: 'check',
						label: '',
						options: [{
							name: 'repeat_forever',
							value: 'yes',
							label: '<strong>Repeat forever?</strong>'
						}],
						onChange: function (checked) {

							if (checked) {
								$(ui.modal.body.form.repeat_end_date.selector)
									.parent().parent().addClass('hidden');
							} else {
								$(ui.modal.body.form.repeat_end_date.selector)
									.parent().parent().removeClass('hidden');
							}

						},
						value: [repeatForeverVal]
					},
					repeat_end_date: {
						name: 'repeat_end_date',
						type: 'date',
						label: 'End on',
						placeholder: moment().endOf('month').local().format('MMMM Do YYYY'),
						value: obj.repeat_end_date
					}
				}
			);

			ui.modal.footer.makeNode(
				'update'
				, 'div'
				, {
					text: '<i class="save icon"></i> Save'
					, css: 'ui right center aligned blue icon button'
					, tag: 'button'
				}
			).notify('click', {
				type: 'date-field-comp-run'
				, data: {
					run: function () {

						var updates = processScheduleForm(ui.modal.body.form);
						updates.id = obj.id;
						ui.modal.footer.update.loading();

						sb.data.db.obj.update(
							obj.object_bp_type
							, updates
							, function (updated) {

								obj.cycle = updated.cycle;
								obj.repeat_forever = updated.repeat_forever;
								obj.repeat_end_date = updated.repeat_end_date;
								obj.schedule_options = updated.schedule_options;

								ui.modal.hide();
								View(fieldName, ui, obj, options);
								ui.patch();

							}
						);

					}.bind(ui.modal.body)
				}
			}, sb.moduleId);

			ui.patch();
			ui.modal.show();
			return;

		}

		var icon = getIcon(fieldName, ui, obj, options);
		var iconText = '';
		var _pholders = (['foundation_group', 'rickyvoltz'].includes(appConfig.instance) || appConfig.is_portal) ? 'Click here to type your response' : 'Empty';

		if (!_.isEmpty(icon)) {
			iconText = '<div class="ui ' + icon.color + ' label"><i class="fas fa-' + icon.icon + '"></i></div>';
		}

		$(ui.selector).html(
			'<div class="ui calendar" id="' + options._fieldId + '">' +
			'<div id="' + options._fieldId + '-inp" class="ui labeled fluid input ' + options.size + '" style="min-width:150px;" ">' +
			iconText +
			'<input type="text" placeholder="'+_pholders+'" name="' + fieldName + '" autocomplete="off" style="border: none !important;">' +
			'</div>' +
			'</div>');

		var calSetup = {};
		switch (options.dateType) {

			case 'date':
				calSetup.type = 'date';
				break;

			case 'time':
			default:
				calSetup.type = 'time';
				calSetup.type = 'datetime';
				break;

		}

		// If the object is not a template, and there is no merge value set, then
		// just show the standard date field.
		if (!isTemplate || _.isEmpty(obj[fieldName + '_merge'])) {

			if (dateTxt) {
				$('#' + options._fieldId + ' input').val(dateTxt);
			}

			calSetup.onChange = function (e) {

				setTimeout(function () {
					$('.floater-container').css({ 'overflow-y': 'scroll', 'overflow-x': 'auto' });
				}, 100);

				var newVal = moment(e).local().format('MMMM D, YYYY h:mm A');

				closeEdit(newVal, true);

				return true;
			};

			$('#' + options._fieldId).calendar(calSetup);

			$('#' + options._fieldId + ' input').on('focus', function () {

				setTimeout(function () {
					$('.floater-container').css({ 'overflow': 'visible' });
				}, 100);

				$('#' + options._fieldId + ' input').select();

			});

			$('#' + options._fieldId + '-inp').off().on('focusout', function () {

				setTimeout(function () {
					$('.floater-container').css({ 'overflow-y': 'scroll', 'overflow-x': 'auto' });
				}, 100);

				var newVal = $(this.selector + ' .calendar input').val();

				closeEdit(newVal);

			}.bind(ui));

			if (!options.editing) {
				$('#' + options._fieldId).calendar('focus');
			}

			// Otherwise, (if it is a template AND a merge value is set) allow
			// users to set merge tags for templating, like {{today}}
		} else {

			$('#' + options._fieldId + ' input').val('{{' + obj[fieldName + '_merge'] + '}}');

			// Update the merge templating value at fieldKey_merge
			$('#' + options._fieldId + '-inp').on('focusout', function () {

				var newVal = $(this.selector + ' .calendar input').val();
				obj[fieldName + '_merge'] = newVal.replace('{{', '').replace('}}', '');

				sb.data.db.obj.update(
					obj.object_bp_type
					, {
						id: obj.id
						, [fieldName + '_merge']: obj[fieldName + '_merge']
					}
					, function (response) { }
				);

			}.bind(ui))

		}

		// Add a toggle btn to go between templating w/merge tags and 
		// standard static date inputs.
		if (isTemplate) {

			var templateToggleBtnId = ui.selector.replace('.', '') + '-temp-toggle';
			var icon = 'calendar';
			if (!_.isEmpty(obj[fieldName + '_merge'])) {
				icon = 'code';
			}
			$(ui.selector).append(
				'<button id="' + templateToggleBtnId + '" class="basic circular ui icon button" style="position:absolute;top:0;right:0;">' +
				'<i class="ui ' + icon + ' icon"></i>' +
				'</button>'
			);
			$('#' + templateToggleBtnId).on('click', function () {

				if (_.isEmpty(obj[fieldName + '_merge'])) {
					obj[fieldName + '_merge'] = 'today';
				} else {
					obj[fieldName + '_merge'] = '';
				}

				Edit(fieldName, ui, obj, options);
				ui.patch();

				sb.data.db.obj.update(
					obj.object_bp_type
					, {
						id: obj.id
						, [fieldName + '_merge']: ''
					}
					, function (response) { }
				);

			});

		}

	}

	function getIcon(fieldName, ui, obj, options, returnObject) {

		// Initialize icon and color
		var icon = '';
		var color = '';

		if (options.hasOwnProperty('is_due_date') && !_.isEmpty(obj[fieldName])) {

			if (options.is_due_date) {

				console.log(obj);

				// Set dates
				var objectDate = obj[fieldName].replace(" ", "T");
				objectDate = objectDate.replace(" +0000", "");
				var currentDate = new Date().getFullYear() + new Date().getDate() + new Date().getDay();
				var dueDate = new Date(objectDate).getFullYear() + new Date(objectDate).getDate() + new Date(objectDate).getDay();
				var currentDay = new Date().getDate();
				var dueDay = new Date(objectDate).getDate();
				var dueDayMinus7 = new Date(objectDate).getDate() - 7;
				var currentDateUnix = parseInt((new Date().getTime() / 1000).toFixed(0));
				var dueDateUnix = parseInt((new Date(objectDate).getTime() / 1000).toFixed(0));

				if (!options.yellow && !options.red) {

					// Due today
					if (dueDate == currentDate) {
						icon = 'exclamation-triangle';
						color = 'yellow';
						// Due this week	
					} else if ((dueDateUnix > currentDateUnix) && (dueDayMinus7 < currentDay) && (currentDay < dueDay)) {
						icon = 'exclamation-triangle';
						color = 'yellow';
						// Overdue
					} else if (dueDateUnix < currentDateUnix) {
						icon = 'exclamation-triangle';
						color = 'red';
					} else {
						icon = 'exclamation-triangle';
						color = options.inBoardView ? 'transparent muted' : 'muted';
					}
				}

				if (+options.green == true) {

					Date.prototype.subtractDays = function (days) {
						var date = new Date(this.valueOf());
						date.setDate(date.getDate() - days);
						return date;
					}

					var triggerDate = new Date();

					currentDateUnix = parseInt((triggerDate.getTime() / 1000).toFixed(0));

					if (dueDateUnix >= currentDateUnix) {
						icon = 'check';
						color = 'green';
					}

				}

				if (+options.yellow > 0) {

					Date.prototype.addDays = function (days) {
						var date = new Date(this.valueOf());
						date.setDate(date.getDate() + days);
						return date;
					}

					var triggerDate = new Date().addDays(+options.yellow);

					currentDateUnix = parseInt((triggerDate.getTime() / 1000).toFixed(0));

					if (dueDateUnix <= currentDateUnix) {
						icon = 'exclamation-triangle';
						color = 'yellow';
					}

				}

				if (+options.red > -1) {

					Date.prototype.addDays = function (days) {
						var date = new Date(this.valueOf());
						date.setDate(date.getDate() + days);
						return date;
					};

					if (+options.red == 0) {
						var triggerDate = new Date();
					} else {
						var triggerDate = new Date().addDays(+options.red);
					}

					currentDateUnix = parseInt((triggerDate.getTime() / 1000).toFixed(0));

					if (dueDateUnix < currentDateUnix) {
						icon = 'exclamation-triangle';
						color = 'red';
					}

				}

				//#2287 - Date Field | Dates display overdue when they have been completed.
				if (obj.status == 'done') {
					icon = 'exclamation-triangle';
					color = 'muted';
				}

			}

		}

		if (options.show_completion_checkbox && options.inCollection != true) {
			if (obj[fieldName + '_done'] == 1) {
				icon = 'check inverted';
				color = 'green';
			}
		}

		if (!_.isEmpty(icon)) {

			return {
				icon: icon,
				color: color
			};

		} else {

			return {};

		}


	}

	return {

		init: function () {

			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'date'
					, title: 'Date'
					, view: View
					, detail: function (fieldName, obj, options) {

						var fieldLabel = '';
						if (
							options
							&& (options.mini || options.inCollection)
							&& typeof obj.object_bp_type === 'string'
						) {

							var bp = _.findWhere(appConfig.Types, { bp_name: obj.object_bp_type.substr(1) });
							if (bp && bp.blueprint && bp.blueprint[fieldName]) {
								fieldLabel = bp.blueprint[fieldName].name;
							}
							return fieldLabel;

						}

						return false;

					}
					, availableToEntities: true
					, propertyType: 'date'
					, icon: 'calendar'
					, options: {
						dateType: {
							type: 'select'
							, name: 'Type'
							, options: [
								{
									name: 'Date & time'
									, value: 'datetime'
								}
								, {
									name: 'Date only'
									, value: 'date'
								}
							]
						}
						, show_completion_checkbox: {
							type: 'bool'
							, name: 'Show completion checkbox?'
						}
						, is_due_date: {
							type: 'bool'
							, name: 'Is due date?'
						}
						, green: {
							type: 'bool'
							, name: 'Use green color for items due in the future?'
						}
						, yellow: {
							type: 'string'
							, name: 'Number of days before to highlight in yellow?'
						}
						, red: {
							type: 'string'
							, name: 'Number of days before to highlight in red?'
						}
						, relative_date_type: {
							type: 'select'
							, name: 'Relative Date Type'
							, options: [
								{
									name: 'After Today'
									, value: 'after'
								}
								, {
									name: 'Before Today'
									, value: 'before'
								}
							]
						}
						, relative_date: {
							type: 'number'
							, name: 'Relative date amount?'
						}
						, default_date_type: {
							type: 'select'
							, name: 'Default Date Type'
							, options: [
								{
									name: 'Start Date'
									, value: 'start_date'
								}
								, {
									name: 'End Date'
									, value: 'end_date'
								}
								, {
									name: 'Neither'
									, value: 'neither'
								}
							]
						}
					}
					, shouldPatch: false
				}
			});

			sb.listen({
				'date-field-comp-run': this.run
			});

			if (IN_NODE_ENV) {
				var date = View;
				module.exports = {
					date
				}
			}

		}

		, run: function (data) { data.run(); }

	}

});

if (IN_NODE_ENV) {
	Factory.startAll();
}
