var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('collection-view', function(sb){

	if (IN_NODE_ENV) {
        return;
    }
	
	function View (fieldName, ui, obj, options) {

		console.log('options', options)

		if (!options) {
			options = {};
		}

		var numPages = 0;
		var currentPage = 1;
		var pageBtnCushion = 3; // number of pages directly next to current page to show
		var subviewContainer = '';

        var portalInstance = false;

        if( appConfig.is_portal || appConfig.isFoundationGroupPortal)
            portalInstance = true;
		
        options.page = 0;
		options.pageLength = 10;
		if (options.hasOwnProperty('blueprint')) {
			if (options.blueprint) {
				if (options.blueprint.hasOwnProperty([fieldName])) {
					if (options.blueprint[fieldName]) {
						if (options.blueprint[fieldName].hasOwnProperty('options')) {
							if (options.blueprint[fieldName].options) {
								if (options.blueprint[fieldName].options.hasOwnProperty('pageLength')) {
									if (options.blueprint[fieldName].options.pageLength) {
										options.pageLength = options.blueprint[fieldName].options.pageLength;
									}
								}
							}
						}
					}
				}
			}
		}

		function page_nav_ui(ui) {

			function change_page(btn, newPage) {

				$(btn.selector).html(
					'<i class="ui grey notched circle loading icon"></i>'
				);

				currentPage = parseInt(newPage);
				options.page = (currentPage == 0) ? 0 : (currentPage - 1) * options.pageLength;

				refreshCollection(subviewContainer, fieldName, obj, options);

			}

			function change_page_length(btn, pageLength) {

				currentPage = 1;
				options.page = 0;
				options.pageLength = pageLength;
				
				refreshCollection(subviewContainer, fieldName, obj, options);

			}

			function applyPageBtnNotification(btn, page) {

				btn.notify('click', {
					type:[sb.moduleId + '-run'],
					data:{
						run:change_page.bind({}, btn, page)
					}
				}, sb.moduleId);

			}

			function applyPageLengthBtnNotification(btn, pageLength) {

				btn.notify('click', {
					type:[sb.moduleId + '-run'],
					data: {
						run: change_page_length.bind({}, btn, pageLength)
					}
				}, sb.moduleId);

			}

			ui.empty();

			ui.makeNode('pageLength', 'div', {
				text:'# of Items',
				css:'ui simple dropdown item'
			});
			ui.pageLength.makeNode('menu', 'div', {
				css:'menu'
			});
			ui.pageLength.menu.makeNode('five', 'div', {
				css:'item',
				tag:'a',
				text:'5'
			});
			ui.pageLength.menu.makeNode('ten', 'div', {
				css:'item',
				tag:'a',
				text:'10'
			});
			ui.pageLength.menu.makeNode('twentyfive', 'div', {
				css:'item',
				tag:'a',
				text:'25'
			});
			ui.pageLength.menu.makeNode('fifty', 'div', {
				css:'item',
				tag:'a',
				text:'50'
			});
			ui.pageLength.menu.makeNode('seventyfive', 'div', {
				css:'item',
				tag:'a',
				text:'75'
			});
			ui.pageLength.menu.makeNode('hundred', 'div', {
				css:'item',
				tag:'a',
				text:'100'
			});
			ui.pageLength.menu.makeNode('twohundred', 'div', {
				css:'item',
				tag:'a',
				text:'200'
			});

			applyPageLengthBtnNotification(ui.pageLength.menu.five, 5);
			applyPageLengthBtnNotification(ui.pageLength.menu.ten, 10);
			applyPageLengthBtnNotification(ui.pageLength.menu.twentyfive, 25);
			applyPageLengthBtnNotification(ui.pageLength.menu.fifty, 50);
			applyPageLengthBtnNotification(ui.pageLength.menu.seventyfive, 75);
			applyPageLengthBtnNotification(ui.pageLength.menu.hundred, 100);
			applyPageLengthBtnNotification(ui.pageLength.menu.twohundred, 200);

			// loading state
			if (!isNaN(numPages)) {

				var btnCss;

				// buttons at start of set
				if (currentPage - pageBtnCushion > 1) {

					applyPageBtnNotification(
						ui.makeNode('btn-to-start', 'div', {
							text:1,
							css:'item',
							tag:'a'
						})
						, 1
					);

					ui.makeNode('ell-to-start', 'div', {
						text:'...',
						css:'disabled item',
						tag:'a'
					});

				}

				// buttons near current page
				for (var i = currentPage - pageBtnCushion; i <= currentPage + pageBtnCushion; i++) {

					if (i > 0 && i <= numPages) {

						if (i === currentPage) {
							btnCss = 'active item';
						} else {
							btnCss = 'item';
						}

						applyPageBtnNotification(
							ui.makeNode('btn-'+ i, 'div', {
								text:i.toString(),
								css:btnCss,
								tag:'a'
							})
							, i
						);

					}

				}

				// buttons at end of set
				if (currentPage + pageBtnCushion < numPages) {

					ui.makeNode('ell-to-end', 'div', {
						text:'...',
						css:'disabled item',
						tag:'a'
					});

					applyPageBtnNotification(
						ui.makeNode('btn-to-end', 'div', {
							text:numPages,
							css:'item',
							tag:'a'
						})
						, numPages
					);

				}

			}

		}

		function topPaging(ui) {

			if (!options.hidePagingAbove) {
			
				if (options.showPaging === true
				|| options.showPaging === undefined) {

					ui.empty();

					if (numPages > 1) {
						page_nav_ui(ui);
					}

					ui.patch();

				}

			}

		}

		function bottomPaging(ui) {

			if (!options.hidePagingUnder) {

				if (options.showPaging === true
				|| options.showPaging === undefined) {

					ui.empty();
	
					if (numPages > 1) {
						page_nav_ui(ui);
					}

					ui.patch();
	
				}

			}

		}

		function refreshCollection(ui, fieldName, obj, options) {

			sb.notify ({
				type: 'get-table-view',
				data: {
					fieldName: fieldName,
					obj: obj,
					options: options,
					ui: ui
				}
			});
			
		}

		var blueprintFieldDisplayName = '';
		if (options.hasOwnProperty('blueprint')) {
			if (options.blueprint) {
				if (options.blueprint.hasOwnProperty([fieldName])) {
					if (options.blueprint[fieldName]) {
						if (options.blueprint[fieldName].hasOwnProperty('name')) {
							if (options.blueprint[fieldName].name) {
								blueprintFieldDisplayName = options.blueprint[fieldName].name;
							}
						}
					}
				}
			}
		}

		sb.data.db.obj.getBlueprint(options.objectType, function(blueprint) {

			if (!blueprint || blueprint.length == 0) {

				if (ui) {

					ui.makeNode('msg', 'div', {
						css: 'ui compact warning message',
						text: 'There was an error getting the data for this field.'
					});
					
					ui.patch();
	
				}
	
				return;

			}

			var objectTypeBP = blueprint;
			options.blueprint = blueprint;

			sb.data.db.obj.getBlueprint(obj.object_bp_type, function(blueprint) {

				if (!blueprint || blueprint.length == 0) {

					if (ui) {

						ui.makeNode('msg', 'div', {
							css: 'ui compact warning message',
							text: 'There was an error getting the data for this field.'
						});
						
						ui.patch();
		
					}
		
					return;

				}

				var contextTypeBP = blueprint;

				// Top menu
				var topMenu = ui.makeNode('topMenu', 'div', {});

				ui.topMenu.makeNode('grid', 'div', {
					css: 'ui grid'
				});

				var topMenuLeft = ui.topMenu.grid.makeNode('left', 'div', {
					css: 'ui eight wide column',
					style: 'text-align:left;'
				});

				var topMenuRight = ui.topMenu.grid.makeNode('right', 'div', {
					css: 'ui eight wide column',
					style: 'text-align:right;'
				});

				// Top paging
				var topPagingContainer = topMenuLeft.makeNode('topPagingContainer', 'div', {
					css:'ui tiny pagination menu'
				});

				// Subview
				subviewContainer = ui.makeNode('subviewContainer', 'div', {
					style: 'width:100%;'
				});

				var bottomMenu = ui.makeNode('bottomMenu', 'div', {});

				ui.bottomMenu.makeNode('grid', 'div', {
					css: 'ui grid'
				});

				var bottomMenuLeft = ui.bottomMenu.grid.makeNode('left', 'div', {
					css: 'ui eight wide column',
					style: 'text-align:left;'
				});

				var bottomMenuRight = ui.bottomMenu.grid.makeNode('right', 'div', {
					css: 'ui eight wide column',
					style: 'text-align:right;'
				});

				// Bottom paging
				var bottomPagingContainer = bottomMenuRight.makeNode('bottomPagingContainer', 'div', {
					css:'ui tiny pagination menu'
				});

				if (appConfig.instance == objectTypeBP.instance) {
				
					if (options.editBlueprint && !portalInstance) {

						var filterDomId = 'filter-' + sb.dom.randomString(6, '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ');

						topMenuRight.makeNode('filter', 'div', {
							text: '<i class="far fa-filter light-grey"></i> Filter',
							style: 'float:right; cursor:pointer;',
							id: filterDomId
						}).notify('click', {
							type: [sb.moduleId + '-run'],
							data: {
								run:function() {

									sb.notify({
										type: 'get-sys-floater',
										data: {
											element: '#' + filterDomId,
											minWidth: '550px',
											maxWidth: '650px',
											offsetTop: 25,
											callback: function (floater) {
						
												floater.container.makeNode('header', 'div', {
													text: 'Filters for <strong>' + blueprintFieldDisplayName + '</strong>',
													style: 'margin-bottom:5px;'
												});
						
												sb.notify ({
													type: 'view-field',
													data: {
														type: 'filter',
														property: fieldName,
														obj: obj,
														options:  {
															blueprints: {
																objectType: objectTypeBP,
																contextType: contextTypeBP
															},
															updateField: options.updateField,
															onUpdate: function (updatedFilter, shouldRefreshCollection) {
																
																floater.close();
																
																if (shouldRefreshCollection) {

																	currentPage = 1;
																	options.page = 0;

																	refreshCollection(
																		subviewContainer,
																		fieldName,
																		obj,
																		options
																	);

																}

															}
														},
														ui: floater.container.makeNode('filters', 'div', {})
													}
												});

												floater.container.patch();

											}
										}
									});

								}

							}

						});

					}

				}

				if (options) {
					if (options.hasOwnProperty('views')) {
						if (options.views) {

							// Table view
							if (options.views.hasOwnProperty('table')) {
								if (options.views.table) {

									refreshCollection(
										subviewContainer,
										fieldName,
										obj,
										options
									);

								}
							}

							options.pagingCallback = function(paging) {

								numPages = Math.ceil(paging.recordsTotal / options.pageLength);

								topPaging(topPagingContainer);
								bottomPaging(bottomPagingContainer);

							}

						}
					}
				}

				ui.patch();

			}, false, true);

		}, false, true);

	}

	return {

		initListeners: function () {
			
			sb.listen({
				[sb.moduleId + '-run']: this.run,
				'get-actions-menu':	this.getActionsMenu
			});
			
		},

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'collection',
					view: View,
					title: 'Collection',
					type: 'Link to Other Sets',
					availableToEntities: false,
					icon: 'list',
					propertyType: 'objectId',
					objectType: '',
					options: {
						// allowSearch: {
						// 	name: 'Allow selections through search?',
						// 	type: 'bool'
						// },
						// objectType: {
						// 	name: 	'Select a data set',
						// 	type: 'objectType'
						// },
						// preventCreate: {
						// 	name: 		'Prevent creates?',
						// 	type: 	'bool'
						// },
						// useObjectAsSeed: {
						// 	name: 	'Use main object as template? (if child is the same type)',
						// 	type: 'bool'
						// },
						// additionalTags: {
						// 	name: 	'Additional Tag(s)',
						// 	type: 'tags'
						// },
					},
					parseOptions: function (selection, field) {
						
						if (selection.multi) {
							field.type = 'objectIds';
						}
						field.objectType = selection.objectType;
						
						return field ;
						
					}
				}
			});

		},

		getActionsMenu: function(data) {

			function single_ui(modal, options, item, viewOptions) {
	
				function openSingleView (modal, objectData) {
		
					function reopenModal (options, item) {
		
						sb.notify({
							type: 'get-sys-modal'
							, data: {
								callback: function (modal) {

									single_ui(modal, options, item, {inModal: true});

								}
							}
						});
		
					}
		
					var reopenModalCallback = false;
					if (!_.isEmpty(viewOptions) && viewOptions.inModal) {
						viewOptions.goBack = function (obj) {
							reopenModal(options, item);
						};
					}

					sb.notify({
						type: 'view-entity',
						data: {
							ui: modal.body.singleArea,
							id: item.id
						}
					});
		
					// if (options.singleView && typeof options.singleView.view === 'function') {
		
					// 	modal.body.singleArea.empty();
					// 	options.singleView.view(modal.body.singleArea, objectData, function(ready){
		
					// 		if(ready){
		
					// 			if(ready.dom){
					// 				ready.dom.patch();
					// 			}
					// 			if(ready.after){
					// 				ready.after(ready.dom);
					// 			}
								
					// 		}else{
					// 			ui.patch();
					// 		}
		
					// 	}, function(refresh) {
		
					// 		if(refresh) {
					// 			refresh_pool (ui.Body, options);
					// 		}
		
					// 		modal.hide();
		
					// 	}, options, viewOptions);
		
					// }else{
		
					// 	_.each(fieldsForSubview, function(field, name){
		
					// 		if(field.view){
		
					// 			field.view(
		
					// 				modal.body.singleArea.makeNode(name, 'div', {
					// 					css:'description',
					// 					style:'padding:8px;'
					// 				}),
		
					// 				item
		
					// 			);
		
					// 		}else{
		
					// 			modal.body.singleArea.empty();
		
					// 			modal.body.singleArea.makeNode(name, 'div', {
					// 				css:'description',
					// 				text:item[name],
					// 				style:'padding:8px;'
					// 			});
		
					// 		}
		
					// 	});
		
					// 	modal.body.makeNode('comments', 'div', {});
					// 	modal.body.patch();
		
					// 	sb.notify({
					// 		type: 'show-note-list-box',
					// 		data: {
					// 			domObj:modal.body.comments,
					// 			objectId:item.id,
					// 			collapse:true
					// 		}
					// 	});
		
					// }
		
				}
		
				modal.body.empty();
		
				modal.body.makeNode('menu', 'div', {
					css: 'ui secondary right floated menu',
					style: 'margin:0 !important;'
				});
		
				var linkSetup = {
					css:'circular icon button item',
					text:'<i class="external square alternate icon"></i>',
					tag:'a',
					href:sb.data.url.getObjectPageParams(item, options)
				};
		
				var closeLinkSetup = {
					css:'circular icon button item',
					text:'<i class="close icon"></i>',
					tag:'a'
				};
		
				if (options.singleView && options.singleView.action) {
					delete linkSetup.href;
				}
		
				if (options.singleView && typeof options.singleView.link === 'function') {
					linkSetup.href = options.singleView.link(item);
				}
		
				modal.body.menu.makeNode('open', 'div', linkSetup);
		
				if (
					!_.isEmpty(viewOptions)
					&& typeof viewOptions.close === 'function'
				) {
		
					modal.body.menu.makeNode('close', 'div', closeLinkSetup).notify('click', {
							type: [sb.moduleId + '-run']
							, data: {
								run: function (item) {
									viewOptions.close(item);
								}.bind({}, item)
							}
						}, sb.moduleId);
		
				} else if (typeof modal.hide === 'function') {
		
					modal.body.menu.makeNode('close', 'div', closeLinkSetup).notify('click', {
							type: [sb.moduleId + '-run']
							, data: {
								run: function () {
		
									modal.hide();
		
								}
							}
						}, sb.moduleId);
		
				}
		
		
				if (options.singleView && options.singleView.action) {
					modal.body.menu.open.notify('click', {
						type:[sb.moduleId + '-run'],
						data:{
							run:function(item){
								options.singleView.action(item);
							}.bind({}, item)
						}
					}, sb.moduleId);
				}
		
				if (options.singleView && options.singleView.action === 'hide') {
					delete modal.body.menu.open;
				}
		
		// 			modal.body.menu.makeNode('left', 'div', {text: '<i class="left chevron icon"></i>', css: 'icon link item'});
		// 			modal.body.menu.makeNode('right', 'div', {text: '<i class="right chevron icon"></i>', css: 'icon link item'});
		
				modal.body.makeNode('singleArea', 'div', {css:''})
					.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
						.makeNode('loader', 'div', {css:'ui loader'});
		
				modal.show();
				modal.body.patch();
		
				if (
					options
					&& options.singleView
					&& options.singleView.useCache
				) {
		
					openSingleView(modal, item);
		
				} else {
		
					var selection = 1;
					if( options.singleView && options.singleView.select ){
						selection = options.singleView.select;
					}
		
					sb.data.db.obj.getById(options.objectType, item.id, function(objectData){
		
						modal.body.singleArea.empty();
						openSingleView(modal, objectData);
		
					}, selection, true);
		
				}
		
			}
		
			function comments_ui(modal, item, options) {
		
				modal.body.empty();
		
				modal.body.makeNode('header', 'div', {
					tag: 'h2'
					, text: 'Comments'
					, css: 'ui header'
				});
				modal.body.makeNode('divider', 'div', {
					css: 'ui divider'
				});
				modal.body.makeNode('body', 'div', {});
		
				modal.body.patch();
		
				sb.notify({
					type: 'show-note-list-box',
					data: {
						domObj: modal.body.body,
						objectIds: [item.id],
						objectId: item.id,
						collapse: 'open',
						activityFeed: true
					}
				});
		
				modal.show();
		
			}

			function archive_selected_items(itemId, options, onComplete) {
	
				// if (_.isEmpty(CurrentSelection) && itemId === undefined) {
		
				// 	sb.dom.alerts.alert('No items selected', 'Select some items first.', 'warning');
				// 	return false;
		
				// }
		
				actionVerb = 'archived';
				var msgTxt = 'Send to archive?';
				var txt = 'You can always un-archive this later.'
				var objectType = options.objectType ? options.objectType : '';
				var updateFunc = 'erase';
		
				// if (Query.archive) {
				// 	actionVerb = 'restored';
				// 	msgTxt = 'Restore from archive?';
				// 	txt = 'This will return this back where it came from.'
				// 	updateFunc = 'restore';
				// }
		
				var action = sb.data.db.obj[updateFunc];
				var bypassRefresh = false;
				var onArchive = false;
		
				sb.dom.alerts.ask({
					title: msgTxt,
					text: txt
				}, function(response) {
		
					swal.disableButtons();
		
					if (response) {
		
						// var itemsToArchive = CurrentSelection;
						if (itemId !== undefined) {
							itemsToArchive = itemId;
						}
		
						if (options) {
							
							if (options.hasOwnProperty('actions')) {
		
								if (options.actions) {
		
									if (options.actions.hasOwnProperty('archive')) {
		
										if (options.actions.archive) {
		
											if (options.actions.archive.hasOwnProperty('action')) {
		
												if (typeof options.actions.archive.action === 'function') {
						
													action = options.actions.archive.action;
						
												}
						
											}
		
											if (options.actions.archive.hasOwnProperty('bypassRefresh')) {
		
												if (options.actions.archive.bypassRefresh) {
		
													bypassRefresh = true;
		
												}
		
											}
		
											if (options.actions.archive.hasOwnProperty('onArchive')) {
		
												if (options.actions.archive.onArchive) {
		
													onArchive = options.actions.archive.onArchive;
		
												}
		
											}
		
										}
		
									}
		
								}
		
							}
		
						}
		
						action(objectType, itemsToArchive, function(response) {
		
							swal.close();
		
							if (response) {
		
								// CurrentSelection = [];
		
								if (!bypassRefresh) {
									refresh_pool (View.Body, options);
								}
		
								if (typeof onComplete === 'function') {
									onComplete();
								}
		
								if (typeof onArchive === 'function') {
									onArchive();
								}
		
								sb.notify({
									type: 'display-alert',
									data: {
										header: actionVerb.charAt(0).toUpperCase() + actionVerb.slice(1),
										body: !Array.isArray(itemsToArchive) ? 'Record ' + actionVerb + ' successfully!' : 'Records ' + actionVerb + ' successfully!',
										color: 'green'
									}
								});
		
							} else {
							
								if (typeof onComplete === 'function') {
									onComplete(false);
								}
		
								if (typeof onArchive === 'function') {
									onArchive(false);
								}
		
								sb.notify({
									type: 'display-alert',
									data: {
										header: 'Error',
										body: 'There was an issue performing this action.',
										color: 'red'
									}
								});
		
							}
		
						});
		
					}
		
				});
		
			}

			var ui = data.ui;
			var obj = data.obj;
			var options = data.options;
	
			var css = '';
			var menuCss = 'menu';
			if(options !== undefined && options.css){
				css = options.css;
			}
			if(options !== undefined && options.menuCss){
				menuCss = options.menuCss;
			}
	
			var actionButtonCss = 'ui mini basic circular icon simple dropdown';
			if(options !== undefined && options.actionButtonCss){
				actionButtonCss = options.actionButtonCss;
			}
	
			var actionButtonStyle = 'border:none;';
			if(options !== undefined && options.actionButtonStyle){
				actionButtonStyle = options.actionButtonStyle;
			}
	
			var menuStyle = '';
			if ( options !== undefined && options.menuStyle )
				menuStyle = options.menuStyle;
	
			if (
				!_.isEmpty(options.actions)
				// && !appConfig.is_portal
			) {
	
				ui.makeNode('actions', 'div', {css:css});
	
				ui.actions.makeNode('menu', 'div', {
					css:actionButtonCss,
					text:'<i class="ellipsis horizontal centered icon" style="font-size:1.5em !important;"></i>',
					style:actionButtonStyle
				}).makeNode('menu', 'div'
					, {
						css: 		menuCss
						, style: 	menuStyle
					}
				);
	
				_.each(options.actions, function(actionSetting, name){
	
					if (
						actionSetting.hasOwnProperty('singleAction')
						&& !actionSetting.singleAction
					) {
						return;
					}
	
					var action = {};
	
					if(actionSetting === false){
						return;
					}
	
					switch ( name ) {
	
						// actions that act on the collection
						case 'create':
						return;
						break;
	
						// actions that act on single objs
						case 'comments':
						return;
						action = {
							icon:'comment',
							color:'purple',
							title:'Comments'
						};
						break;
	
						case 'edit':
						action = {
							icon:'pencil',
							color:'yellow',
							title:'Edit'
						};
						break;
	
						case 'view':
						action = {
							icon:'expand',
							color:'gray',
							title:'Open'
						};
						break;
	
						default:
						action = actionSetting;
						break;
	
					}
	
					var menuItemSetup = {
							css:'ui '+ action.color +' item',
							text:'<i class="'+ action.icon +' icon"></i> '+ action.title,
							style:'border-radius:0px;'
						};
	
					if(action.hasOwnProperty('href') && action.href == true){
	
						menuItemSetup.tag = 'a';
						menuItemSetup.href = obj.link;
	
					}
	
					ui.actions.menu.menu.makeNode(name, 'div', menuItemSetup);
	
					if(!action.hasOwnProperty('href')){
	
						ui.actions.menu.menu[name].notify('click', {
							type:[sb.moduleId + '-run'],
							data:{
								run:function( actionType ) {
	
									switch( actionType ) {
	
										case 'view':

											sb.notify({
												type: 'get-sys-modal'
												, data: {
													callback: function (modal) {

														single_ui(modal, options, obj, {inModal: true});

													}
												}
											});

											break;
	
										case 'comments':

											sb.notify({
												type: 'get-sys-modal',
												data: {
													callback: 	function (modal) {
											
														comments_ui(modal, obj, options);

													}
												}
											});

											break;
	
										default:
	
										// custom funcs
										if( options.actions[actionType] && typeof options.actions[actionType].action === 'function' ){
	
	// 										View['large-modal'].body.empty();
	
											switch(options.actions[actionType].domType){
	
												case 'none':
	
													options.actions[actionType].action( obj, customView, function(shouldUpdateList){
	
														if( shouldUpdateList ){
	
															refresh_pool(View.Body, options);
	
														}
	
													} );
	
													break;
	
												case 'navigate':
	
													options.actions[actionType].action( obj, customView, function(shouldUpdateList){});
	
													break;
	
												default:
	
													var customView = View['large-modal'].body.makeNode('c', 'div', {style:'min-height:37vh!important;'});
	
													View['large-modal'].body.patch();
													View['large-modal'].show();
	
													options.actions[actionType].action( obj, customView, function(shouldUpdateList){
	
														View['large-modal'].hide();
	
														if( shouldUpdateList ){
	
															refresh_pool(View.Body, options);
	
														}
	
													} );
	
											}
	
										}
	
										break;
	
									}
	
								}.bind({}, name, obj)
							}
						}, sb.moduleId);
	
					}
				});
	
				if (options.actions.copy !== false) {
	
					ui.actions.menu.menu.makeNode('copy', 'div'
						, {
							css: 'ui yellow basic fluid item'
							, text:'<i class="copy outline icon"></i> Duplicate'
							, tag: 'a'
							, style: 'border-radius:0px;'
						}
					).notify('click', {
						type: [sb.moduleId + '-run']
						, data: {
							run: function(obj) {
	
								var action = sb.data.db.obj.createFromTemplate;
								var bypassRefresh = false;
								var onCopy = false;
	
								var copyObj = {
										name: obj.name + '-COPY'
										, object_bp_type: options.objectType
										, is_template: 0
									};
									
								if (options.templates === true) {
									copyObj.is_template = 1;
								}
	
								if (options) {
						
									if (options.hasOwnProperty('actions')) {
				
										if (options.actions) {
				
											if (options.actions.hasOwnProperty('copy')) {
				
												if (options.actions.copy) {
				
													if (options.actions.copy.hasOwnProperty('action')) {
				
														if (typeof options.actions.copy.action === 'function') {
								
															action = options.actions.copy.action;
								
														}
								
													}
				
													if (options.actions.copy.hasOwnProperty('bypassRefresh')) {
				
														if (options.actions.copy.bypassRefresh) {
				
															bypassRefresh = true;
				
														}
				
													}
				
													if (options.actions.copy.hasOwnProperty('onCopy')) {
				
														if (options.actions.copy.onCopy) {
				
															onCopy = options.actions.copy.onCopy;
				
														}
				
													}
				
												}
				
											}
				
										}
				
									}
				
								}
								
								ui.actions.menu.menu.copy.loading();
								$(ui.actions.menu.menu.copy.selector).html('<i class="ui notched circle loading icon"></i> Duplicating');
								
								action(obj.id, function(response) {
	
									ui.actions.menu.menu.copy.loading(false);
									$(ui.actions.menu.menu.copy.selector).html('<i class="ui copy outline icon"></i> Duplicate');
									
									if (response) {
										
										if (!bypassRefresh) {
											refresh_pool(View.Body, options);
										}
										
										if (typeof onCopy === 'function') {
											onCopy();
										}
	
										sb.notify({
											type: 'display-alert',
											data: {
												header: 'Duplicated',
												body: 'Record duplicated successfully!',
												color: 'green'
											}
										});
										
									} else {
	
										if (typeof onCopy === 'function') {
											onCopy(false);
										}
	
										sb.notify({
											type: 'display-alert',
											data: {
												header: 'Error',
												body: 'There was an issue performing this action.',
												color: 'red'
											}
										});
	
									}
	
								}, 0, copyObj);
	
							}.bind(null, obj)
						}
					}, sb.moduleId);;
	
				}
	
				if(options.actions.navigateTo !== false){
	
					var goToLink = sb.data.url.getObjectPageParams(obj, options);
					if (obj.hasOwnProperty('link')) {
						goToLink = obj.link;
					}
					if (options.singleView && typeof options.singleView.link === 'function') {
						goToLink = options.singleView.link(obj);
					}
	
					// add link
					ui.actions.menu.menu.makeNode('linkTo', 'div', {
						css:'ui blue basic fluid item',
						text:'<i class="external square icon"></i> Go to',
						tag:'a',
						style:'border-radius:0px;',
						href:goToLink
					});
				}
	
				if(options.actions.archive !== false
					|| options.actions.archive === undefined){
	
					var archiveBtnTxt = '<i class="ui black archive icon"></i> Archive';
					var archiveBtnLoadingTxt = '<i class="ui notched circle loading icon"></i> Archiving';
					// if (Query.archive) {
					// 	archiveBtnTxt = '<i class="ui yellow redo icon"></i> Restore';
					// 	var archiveBtnLoadingTxt = '<i class="ui notched circle loading icon"></i> Restoring';
					// }
	
					// add link
					ui.actions.menu.menu.makeNode('archive', 'div', {
						css:'ui basic fluid item archive-action-single-btn',
						text:archiveBtnTxt,
						tag:'a',
						style:'border-radius:0px;'
					}).notify('click', {
						type:[sb.moduleId + '-run'],
						data:{
							run: function(objId) { 
	
								ui.actions.menu.menu.archive.loading();
								$(ui.actions.menu.menu.archive.selector).html(archiveBtnLoadingTxt);
	
								archive_selected_items(objId, options, function() {
	
									ui.actions.menu.menu.archive.loading(false);
									$(ui.actions.menu.menu.archive.selector).html(archiveBtnTxt);
	
								});
	
							}.bind({}, obj.id)
						}
					}, sb.moduleId);
				}
	
				if(options.actions.comments !== false) {
					ui.actions.menu.menu.makeNode('commentsAction', 'div', {
						css:'ui basic fluid item archive-action-single-btn',
						text:'<i class="comment icon"></i> Comments',
						tag:'a',
						style:'border-radius:0px;'
					}).notify('click', {
						type: 'run-method',
						data:{
							run: function() {

								sb.notify({
									type: 'get-sys-modal',
									data: {
										callback: 	function (modal) {

											comments_ui(modal, obj, options);

										}
									}
								});

							}
						}
					}, sb.moduleId);
				}
	
			}
	
			ui.patch();
	
		},

		run: function(data){ data.run(); }

	}

});