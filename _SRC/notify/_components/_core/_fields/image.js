var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('image-field', function (sb) {
	
	function createNewItem (fieldName, ui, obj, options, onUpload, onClose) {
  
		function uploadDoc (formData, onUpload, onExit) {
			
			var err = {};
			var validFileTypes = ['image/gif', 'image/jpeg', 'image/png'];
			
			// If the form is empty
			if (_.isEmpty(formData)) {
				
				err = {
					title: 'Please complete the form'
					, text: ''
				};
				
			}
			
			// If no file is set
			if (
				_.isEmpty(formData.file_upload)
				|| _.isEmpty(formData.file_upload.value)
				|| formData.file_upload.value.fileData === undefined
			) {
				
				err = {
					title: 'Please select a file to upload'
					, text: ''
				};
				
			} else if (!_.contains(validFileTypes, formData.file_upload.value.fileData.type)) {
				
				err = {
					title: 'File must be an image'
					, text: '(gif, jpeg, png)'
				};
				
			}
			
			if (!_.isEmpty(err)) {
				
				sb.dom.alerts.alert(
					err.title
					, err.text
					, 'error'
				);
				onExit();
				return;
				
			}
   
			sb.data.db.obj.update(
				obj.object_bp_type
				, {
					id: 			obj.id
					, [fieldName]: 	formData.file_upload.value
					, file_upload_name: formData.name.value
					, _fieldName: 		fieldName
				}
				, function (response) {
					
					return onUpload(response);
					
				}
			);
			
		}
		
		// Read options/flags 
		var objType = '';
		if (
			options.blueprint
			&& options.blueprint[fieldName]
			&& !_.isEmpty(options.blueprint[fieldName].options)
		) {			
			objType = options.blueprint[fieldName].options.objectType;
		} else if (options.objectType) {
			objType = options.objectType;
		}
		
		var seed = {
			object_bp_type: 	objType
			, parent: 			obj.id
		};
		
		if (options.inheritsTags) {
			seed.tagged_with = obj.tagged_with;
		}
		
		sb.notify({
			type: 'get-sys-modal'
			, data: {
				callback: 	function (modal) {
					
					modal.body.empty();
					
					var formObject = {
						name: {
							type: 'text'
							, name: 'name'
							, label: 'Name'
							, placeholder: 'Name of the file'
							// !TODO: should auto-update this w/file name from 
							// the upload on change of the upload.
						}
						, file_upload: {
							label:'Upload the image file below:',
							name:'file_upload',
							type:'file-upload'
						}						
					};
					
					modal.body.makeNode(
						'form'
						, 'form'
						, formObject
					);
					
					modal.body.makeNode(
						'save'
						, 'div'
						, {
							css: 'ui fluid primary button'
							, text: 'Save'
						}
					).listeners.push(function (s) {
						
						$(s).on('click', function () {
							
							modal.body.save.loading();
							
							uploadDoc(
								modal.body.form.process().fields
								, function (response) {
         
									modal.hide();
									onUpload(response);
									
								}
								, function () {
									
									modal.body.save.loading(false);
									
								}
							)
							
						});
						
					});
					
					modal.body.patch();
					modal.show();
					
				}
				, onClose: 	onClose || function () {}
			}
		});
				
	}
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			_.each(obj[fieldName], function (img) {
										
				if (img && img.loc) {
					ret += '<img src="' + sb.data.files.getURL(img) + '"">';
				}

			});

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;
			
		}
		
		function getItemHtml (item) {
			
			var txt = '';
			
			link = sb.data.url.createPageURL(
				'object'
				, {
					id: 		item.id
					, type: 	item.object_bp_type
					, name: 	item.name
				}
			) ;
					
			txt = '<i class="ui file icon"></i>'+ item.name;
			
			return txt;
			
		}
		
		function viewItem (ui, item) {
   
			var css = 'ui image';
			var imageMissing = false;
			
			// Set to full width of option is turned on and not in a collection view
			if (
				options 
				&& options.fullWidth
				&& !options.inCollection
			) {
				css = 'ui fluid image';
			}

			if (item.loc == "//") {

				imageMissing = true;
				var missingImageIcon = { 
					// css: 	'accessible icon', 
					text: 	'<i class="eye slash outline icon" style="font-size: 3rem"></i><br/>Image Not Available',
					style:	'font-size: 1rem!important; text-align:center;',
					tag: 	'div'
				};

				ui.makeNode('i-'+ item.id, 'div', missingImageIcon);

			} else {

				ui.makeNode(
					'i-'+ item.id
					, 'div'
					, {
						css: 	css
						, tag: 	'img'
						, src: 	sb.data.files.getURL(item)
						, isTabbable: true
					}
				)
			}

			if (!options.inCollection && !imageMissing) {
				
				ui['i-'+ item.id].listeners.push(
					
					function (item, selector) {
						
						$(selector).on(
							'click'
							, function (e) {
	
								e.stopPropagation();
								e.preventDefault();
								
								sb.notify({
									type: 	'get-sys-modal'
									, data: 	{
										style: 'basic'
										, callback: 	function (modal) {
											
											modal.body.empty();
											modal.show();
											
											modal.body.makeNode('menu', 'div', {
												css: 'ui secondary right floated menu',
												style: 'margin:0 !important;'
											});
											
											var linkSetup = { 
												css: 	'circular icon button item', 
												text: 	'<i class="inverted blue download icon"></i>', 
												tag: 	'a',
												href: 	sb.data.files.getURL(this),
												target:'_blank'
											};
											
											var archiveSetup = { 
												css: 	'circular icon button item', 
												text: 	'<i class="inverted red trash icon"></i>', 
												tag: 	'a'
											};
								
											var closeLinkSetup = { 
												css: 	'circular icon button item', 
												text: 	'<i class="inverted white close icon"></i>', 
												tag: 	'a'
											};
											
											modal.body.menu.makeNode('download', 'div', linkSetup);
											
											modal.body.menu.makeNode('archive', 'div', archiveSetup)
												.listeners.push(
													
													function (selector) {
														
														var img = this;
														$(selector).on(
															'click'
															, function () {
																
																sb.dom.alerts.ask({
																		title: 'Archive this image?'
																		, text: ''
																	}, function (r) {
																		
																		if (r) {
																			
																			swal.disableButtons();
																			sb.data.db.obj.erase(
																				'file_meta_data'
																				, img.id
																				, function (r) {
																					
																					swal.close();
																					modal.hide();
																					if (Array.isArray(obj[fieldName])) {
									
																						obj[fieldName] = _.filter(
																							obj[fieldName]
																							, function (i) {
																								
																								return i.id !== img.id;
																								
																							}
																						);
																						
																					} else {
																						
																						obj[fieldName] = null;
																						
																					}
																					
																					ui.empty();
																					View(fieldName, ui, obj, options);
																					return ui.patch();
																					
																				}
																			);
																			
																		} else {
																			
																			swal.close();
																			
																		}
																		
																	}
																);
																
															}
														);
														
													}.bind(this)
													
												);
											
											modal.body.menu.makeNode('close', 'div', closeLinkSetup)
												.listeners.push(
													
													function (selector) {
														
														$(selector).on(
															'click'
															, function () {
																modal.hide();
															}
														);
														
													}
												);
											
											modal.body.makeNode('d', 'div', {css: 'ui clearing divider'});
											modal.body.makeNode(
												'img'
												, 'div'
												, {
													css: 'ui fluid image'
													, tag: 'img'
													, src: 	sb.data.files.getURL(this)
												}
											);
											
											modal.body.makeNode('c', 'div', {});
											modal.body.patch();
											
										}.bind(this)
									}
								});
								
							}.bind(item)
						);
						
						$(selector).on(
							'focus'
							, function (e) {
	
								e.stopPropagation();
								e.preventDefault();
								
							}
						);
	
						
					}.bind({}, item)
					
				);
			
			}
			
		}
		
		var canCreate = true;
		var link = '';
		var placeholder = options.placeholder || 'Empty';
		var imgSize = options.size || 'small';
		
		if (options && options.inCollection && !options.size) {
			imgSize = 'mini';
		}
		
		if (options.hasOwnProperty('canCreate')) {
			canCreate = options.canCreate;
		}
		
		var css = 'edge-field ui '+ imgSize +' rounded images revealParent';
		
		// Set to full width of option is turned on and not in a collection view
		if (
			options 
			&& options.fullWidth
			&& !options.inCollection
		) {
			css = 'edge-field ui rounded fluid images revealParent'
		}
		
		ui.makeNode(
			't'
			, 'div'
			, {
				isTabbable: true
				, css: css
			}
		);
		
		// empty view
		if (_.isEmpty(obj[fieldName])) {
			
			ui.t.makeNode(
				'msg'
				, 'div'
				, {
					css: 'field-placeholder'
					, text: placeholder
					, style: "font-size: 12px !important;"
				}
			);
			
		} else if (Array.isArray(obj[fieldName])) {
			
			_.each(obj[fieldName], function (item) {
				
				if (item && item.id) {
					viewItem(ui.t, item);
				}
				
			});
			
		} else {
			
			viewItem(ui.t, obj[fieldName]);
			
		}
		
		if (options && options.inCollection) {
			return true;
		}
  
		if (
			canCreate
			&& (
				options.multi
				|| !(
					Array.isArray(obj[fieldName])
					&& obj[fieldName].length > 0
				)
			)
		) {
			
			ui.t.makeNode(
				'c'
				, 'div'
				, {
					css: 'ui circular mini basic icon button revealChild'
					, text: '<i class="ui green plus icon"></i> upload'
					, style: 'box-shadow:none;background-color:white !important;'
				}
			);
			
			ui.t.c.listeners.push(
				function (selector) {
     
					if (options.edit || options.editing) {
						
						$(selector).on('click focus', function (e) {
							
							e.stopPropagation();
							createNewItem(fieldName, ui, obj, options, function (updates) {
								
								sb.data.db.obj.update(obj.object_bp_type, {
										id: 			obj.id
										, image: 		updates.file_meta_data.id
									}
									, function () {}
								);

								if (Array.isArray(obj[fieldName])) {
									
									obj[fieldName].push(updates.file_meta_data);
									
								} else {
									
									obj[fieldName] = updates.file_meta_data;
									
								}
								
								View(fieldName, ui, obj, options);
								ui.patch();
								
							});
							
						});
						
					}
					
				}
			);
			
		}
		
		// Don't show the dropdown if create is on and its not multi
		/*
if (
			!canCreate
			|| options.multi
		) {
			
			ui.t.listeners.push(
				function (selector) {
					
					if (options.edit || options.editing) {
						
						$(selector).on('click focus', function () {
							
							Edit (
								fieldName
								, ui
								, obj
								, options
							) ;
							
						});
						
					}
					
				}
			);
			
		}
*/
		
	}
	
	function Edit (fieldName, ui, obj, options) {

		function createIfNeeded (
			obj
			, fieldName
			, newVal
			, callback
		) {
			
			var formComplete = false;
			if (
				newVal === 'new'
				|| _.contains(newVal, 'new')
			) {
				
				var seed = {
					object_bp_type: 	objType
					, parent: 			obj.id
				};
				
				if (options.inheritsTags) {
					seed.tagged_with = obj.tagged_with;
				}
				
				sb.notify({
					type: 	'create-new-entity'
					, data: 	{
						type: 			objType
						, seed: 		seed	
						, onClose: 		function () {
							
							if (!formComplete) {
								View(fieldName, ui, obj, options);
								ui.patch();
							}
							
						}
						, onComplete: 	function (created) {
							
							formComplete = true;
							callback(
								obj
								, fieldName
								, created.id
							) ;
							
						}
					}
				});
				
			} else {
				
				callback(
					obj
					, fieldName
					, newVal
				) ;
				
			}
			
		}
		
		var allowSearch = true;
		var canCreate = true;
		var objType = '';
		var multi = false;
		var userOptions = [];
		var searchNodeCSS = 'ui search selection transparent fluid dropdown';
		var values = [];
		// !TODO: where should be createable in field editor
		var where = {};
		
		if (
			options.blueprint
			&& options.blueprint[fieldName]
			&& !_.isEmpty(options.blueprint[fieldName].options)
		) {
			
			objType = options.blueprint[fieldName].options.objectType;
			multi = options.blueprint[fieldName].options.multi;
			allowSearch = options.blueprint[fieldName].options.allowSearch;
			
		} else if (options.objectType) {
			objType = options.objectType;
			multi = options.multi || false;
			if (options.where) {
				where = options.where;
			}
		}
		if (options.hasOwnProperty('canCreate')) {
			canCreate = options.canCreate;
		}

		ui.empty();

		if(multi){

/*
			if (appConfig.instance == 'rickyvoltz') {

				multiEdit(fieldName, ui, obj, options);
				
				return;

				
			} else {
*/
			
				searchNodeCSS = 'ui fluid transparent multiple selection search dropdown';
				values = _.map(obj[fieldName], function (obj) {
					
					return {
						value: 		obj.id
						, name: 	obj.name
						, selected: true
					};
					
				});			
				
// 			}

				
		} else if (!_.isEmpty(obj[fieldName])) {
			
			values = [{
				value: 		obj[fieldName].id
				, name: 		obj[fieldName].name
				, selected: 	true
			}];
			
		}
		
// 		ui.empty();
		
		var searchSetup = {
			css: 	searchNodeCSS +' edge-field',
			style: 	'border:none;',
			text:
				'<input type="hidden" name="user">'+
				'<i class="dropdown icon"></i>'+
				'<input type="text" class="transparent search search-input" tabindex="0" style="padding:7px 15px !important; height:100%;">'+
				'<div class="default text">Find a user</div>'+
				'<div class="menu transition hidden" tabindex="-1"></div>',
			listener: 	{
				type: 				'dropdown'
				, values: 			values
				, saveRemoteData: 	false
				, minCharacters: 	0
				, onHide: 			function () {
					
					if (typeof options.onEditEnd === 'function')
						options.onEditEnd();
					
					var newVal = $(searchNode.selector).dropdown('get value');

					if(multi){
						
						newVal = newVal.split(',');
						
					}else{
						
						newVal = parseInt(newVal);
						if (isNaN(newVal)) {
							
							newVal = $(searchNode.selector)
										.dropdown('get value');
						
						}
						
					}	

					searchNode.loading();
					
					ui.t.loading();
					createIfNeeded (
						obj
						, fieldName
						, newVal
						, function (
							obj
							, fieldName
							, newVal
						) {
							
							var updateVal = obj[fieldName];
							if (
								multi
								&& typeof newVal === 'number'
							) {
								if (_.isEmpty(updateVal)) {
									updateVal = [];
								}
								updateVal.push(newVal);
							} else {
								updateVal = newVal;
							}
							
							if (options.commitUpdates) {								
								
								sb.data.db.obj.update(
									obj.object_bp_type
									, {
										id: 			obj.id
										, [fieldName]: 	updateVal
									}
									, function (response) {
	
										ui.empty();
										obj[fieldName] = response[fieldName];
										View (
											fieldName
											, ui
											, obj
											, options
										);
										ui.patch();
										
									}
									, {
										[fieldName]: 1
									}
								);
								
							} else {
								
								sb.data.db.obj.getById(objType, updateVal, function (selected) {
									
									obj[fieldName] = selected;
									ui.empty();
									View (
										fieldName
										, ui
										, obj
										, options
									);
									ui.patch();
									
								});
								
							}
							
						}
					);
					
				}
				, onAdd: 			function (addedValue) {
					
					if (addedValue === 'new') {
						
						createIfNeeded (
							obj
							, fieldName
							, addedValue
							, function (newItem) {
								
								if (multi) {
									obj[fieldName].push(newItem);
								} else {
									obj[fieldName] = newItem;
								}
								
							}
						);
						
					}
					
				}
				, onRemove: 		function (removedValue) {
					
					// remove values from cached state
					if (multi) {
						
						obj[fieldName] = _.filter(
							obj[fieldName]
							, function (item) {
								
								return item.id !== parseInt(removedValue);
								
							}
						);
						
					}
					
				}
			}
		};
		
		if (allowSearch) {
			
			searchSetup.listener.apiSettings = {
				cache: 	false
				, url: 	databaseConnection.obj.getSearchPath(
					objType.replace('#', '%23')
					, where
				)
				, onResponse: 			function (raw) {

					var results = [];
					var selectedIds = _.pluck(values, 'value');
					_.each(raw.results, function (item) {
						
						var selection = $(searchNode.selector).dropdown('get value');
						selection = selection.split(',');
						
						// filter out items without names, and items already
						// added to selection
						if (
							!_.isEmpty(item.name) 
							&& !_.contains(selectedIds, item.id)
						) {
							
							results.push({
								value: parseInt(item.id)
								, name: item.name
							});
							
						}
						
					});
					
					if (canCreate) {
						/*

						results.push({
							value: 		'new'
							, name: 	'<i class="green plus icon"></i> New'
						});
						
*/
					}
					
					return {
						results: results
					};
					
				}
				, mockResponseAsync: 	function(settings, callback){
					
					function GetCookieValue(name) {
					    var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
					    return found.length > 0 ? found[0].split("=")[1] : null;
					}
					
					var headers = {
						'bento-token': GetCookieValue('token')
					};
					if (appConfig.is_portal) {
						
						headers.portal = appConfig.state.portal;
						headers['bento-token'] = GetCookieValue('p_token');
						
					}
					
					$.ajax({
						type: 'post',
						url: settings.url,
						xhrFields: {
							withCredentials: true
						},
						crossDomain: true,
						data: {},
						success: function (response) { callback(response); },
						error: function (jqXHR, status, error) {},
						headers: headers
					});	
					
				}
			};
			
		} else {
			
			searchSetup.listener.values = values;
			/*
searchSetup.listener.values.push({
				value: 		'new'
				, name: 	'<i class="green plus icon"></i> New'
			});
*/
			
		}
		
		var searchNode = ui.makeNode('t', 'div', searchSetup);
		
		searchNode.listeners.push(function (selector) {
			
			$(selector).find('.search-input').focus();
						
		});
			
		ui.patch();
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 					'image',
					title: 					'Image',
					availableToEntities: 	true,
					icon: 					'picture',
					options: {
						multi: {
							type: 		'bool'
							, name: 	'Allow multiple files?'
						}
						, fullWidth: {
							type: 		'bool'
							, name: 	'Display image(s) full width?'
						}
						, isCover: {
							type: 		'bool'
							, name: 	'Is the cover image?'
						}
					},
					view:					View,
					objectType: 			'file_meta_data',
					propertyType: 			'objectIds',
					select: {
						name: 			true
						, file_type: 	true
						, file_name: 	true
						, loc: 			true
						, oid_type: 	true
						, oid: 			true
					},
					focus: function (fieldName, ui, obj, options) {
						
						if (!options.editing) {
							$(ui.i.selector).children().first().select();
						}
						
					}
				}
			});

			if (IN_NODE_ENV) {
				var image = View;
				module.exports = { 
					image
				}
			}
			
		}
		
	};
	
});

if (IN_NODE_ENV) {
	Factory.startAll();
}