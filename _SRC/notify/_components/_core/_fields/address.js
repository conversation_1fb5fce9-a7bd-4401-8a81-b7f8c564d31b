var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('address-field', function (sb) {

	function getDisplayHtml (fieldName, obj, options) {

		var isPrimaryIcon = '';
		if (options.is_primary == 'yes') {
			isPrimaryIcon = '<i class="fa fa-check"></i> ';
		}

		var displayString = isPrimaryIcon;
		if (!_.isEmpty(obj[fieldName])) {

			var country = '';
			var regions = '';
			var state = '';
			var isNumericStateShortCode = false;
			
			if ( obj[fieldName].hasOwnProperty('country') ) {
				country = _.findWhere(sb.data.countries, {countryShortCode: obj[fieldName].country});
				regions = country ? country.regions : '';
				country = country ? country.countryName : '';
			}
			
			if ( obj[fieldName].hasOwnProperty('state') && regions ) {
				state = _.findWhere(regions, {shortCode: obj[fieldName].state});
				if (state) {
					if (Number.isInteger(parseInt(state.shortCode))) {
						state = state.name;
						isNumericStateShortCode = true;
					} else {
						state = state.shortCode;
					}
				} else {
					state = '';
				}
			}
			
			if (!options.countryProvinceOnly) {

				if (obj[fieldName].street) {
					displayString += obj[fieldName].street;
					if (obj[fieldName].add2) {
						displayString += ', '+ obj[fieldName].add2;
					}
					if (
						obj[fieldName].city
						|| state
						|| country
					) {
						displayString += '<br/>';
					}
				}

				if (obj[fieldName].city) {

					displayString += obj[fieldName].city;
					if (state) {
						if (isNumericStateShortCode) {
							displayString += '<br/>'+ state;
						} else {
							displayString += ', '+ state;
						}
					}
					if (obj[fieldName].zip) {
						if (isNumericStateShortCode) {
							displayString += '<br/>'+ obj[fieldName].zip;
						} else {
							displayString += ' '+ obj[fieldName].zip;
						}	
					}
					if (country) {
						displayString += '<br/>'+ country;
					}

				}

			} else {

				if (state) {
					displayString += state;
				}
				if (country) {
					displayString += '<br/>'+ country;
				}
			
			}

		}

		return displayString;

	}

	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			if (!_.isEmpty(obj[fieldName])) {
					
				ret =
					obj[fieldName].street +' '+ obj[fieldName].add2 +'<br />'+
					obj[fieldName].city +
					' '+ obj[fieldName].state +
					', '+ obj[fieldName].zip +
					' '+ obj[fieldName].country;
			
			}

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}

		if (typeof obj[fieldName] !== 'object') {
			obj[fieldName] = {};
		}

		var displayString = getDisplayHtml(fieldName, obj, options);

		// Empty state
		if (_.isEmpty(displayString) && !options.editing) {

			var placeholderTxt = (['foundation_group', 'rickyvoltz'].includes(appConfig.instance) || appConfig.is_portal) ? 'Click here to type your response' : 'Empty';
			if (!_.isEmpty(options) && !_.isEmpty(options.placeholder) && typeof options.placeholder === 'string') {
				placeholderTxt = options.placeholder;
			}

			ui.makeNode('d', 'div', {
				tag: 			'input'
				, placeholder: 	placeholderTxt
				, css: 			'text'
				, style: 		'border:none;background-color:transparent;outline:none;width:100%;'
			});

			ui.makeNode('c', 'div', {
				css: 'ui form fold collapsed'
			});

			if (options.edit) {

				if (options.editing) {

					Edit(
						fieldName
						, ui
						, obj
						, options
					);

				} else {

					ui.d.listeners.push(
						function (selector) {
		
							$(selector).on('focus', function () {
		
								Edit(
									fieldName
									, ui
									, obj
									, options
								);

							});
		
						}
					);

				}

			}

		} else {

			ui.makeNode('d', 'div', {
				text: displayString
			});
			
			ui.makeNode('c', 'div', {
				css: 'ui form fold collapsed'
			});

			if (options.edit) {

				if (options.editing) {

					Edit(
						fieldName
						, ui
						, obj
						, options
					);

				} else {

					ui.d.listeners.push(
						function (selector) {

							$(selector).on('click', function () {

								Edit(
									fieldName
									, ui
									, obj
									, options
								);

							});

						}
					);
			
				}

			}

		}
		if (!(_.isEmpty(displayString) && !options.editing)){
			if (options.edit) {
				setTimeout(function(){
					Edit(fieldName, ui, obj, options)
				}, 1000);
				return;
			}
		}
	}

	function Edit (
		fieldName
		, ui
		, obj
		, options
	) {

		var subFields = [
			{
				title: 		'Street Address'
				, name: 		'street'
				, width: 	'full'
				, autocomplete: 'off'
			}
			, {
				title: 		'Apt / Suite / Other'
				, name: 		'add2'
				, width: 	'full'
				, autocomplete: 'off'
			}
			, {
				title: 		'City'
				, name: 		'city'
				, width: 	'half'
				, autocomplete: 'off'
			}
			, {
				title: 		'State / Province / Region'
				, name: 		'state'
				, width: 	'half'
				, type: 		'select'
				, options: 	'state'
			}
			, {
				title: 		'Zip / Postal Code'
				, name: 		'zip'
				, width: 	'half'
			}
			, {
				title: 		'Country'
				, name: 		'country'
				, width: 	'half'
				, type: 		'select'
				, options: 	'country'
			}
		];

		// Hide fields from country-province/state only fields
		if (options.countryProvinceOnly) {

			subFields[0].isHidden = true;
			subFields[1].isHidden = true;
			subFields[2].isHidden = true;
			subFields[4].isHidden = true;

		}

		// Default to empty state
		if (_.isEmpty(obj[fieldName])) {

			obj[fieldName] = {
				street: 		''
				, add2: 		''
				, city: 		''
				, state: 	''
				, zip: 		''
				, country: 	'US'
			};

		}

		function commitChange (obj, onComplete) {
			
			sb.data.db.obj.update(
				obj.object_bp_type
				, {
					id: 				obj.id
					, [fieldName]: 	obj[fieldName]
				}
				, function (updated) {
					onComplete(updated);
				}
				, {
					[fieldName]: true
				}
			);

		}

		function drawInput (ui, field, callback) {

			if (field.isHidden) {
				if (callback) {
					callback(ui);
				}
			}

			var val = obj[fieldName][field.name];
			var widthCss = 'width:100%;';
			if (field.width === 'half') {
				widthCss = 'width:calc(50% - 12px);';
			}

			switch (field.type) {

				case 'select':

					ui.c.makeNode(
						field.name
						, 'div'
						, {
							text: 		''
							, css: 		'field'
							, style: 	'display:inline-block;'+ widthCss
						}
					).makeNode('l', 'div', {
						tag: 	'label'
						, text: field.title
					});
					ui.c[field.name].makeNode(
						's'
						, 'div'
						, {
							tag: 'select'
							, css: 'fluid dropdown'
							, style: 'position:relative;top:10px;margin-top:-10px;width:100%;'
							, data: [
								{
									placeholder: 'Empty'
								}
							]
						}
					);

					var data = {
						value: ''
					};
					if (!val) {
						data.selected = 'selected'
					}
					ui.c[field.name].s.makeNode(
						'o-empty'
						, 'div'
						, {
							tag: 'option'
							, text: ''
							, data: data
						}
					);

					switch (field.options) {
						case 'country':
							_.each(sb.data.countries, function (opt) {

								var data = {
									value: opt.countryShortCode
								};
								if (opt.countryShortCode === val) {
									data.selected = 'selected'
								}
								ui.c[field.name].s.makeNode(
									'o'+ opt.countryShortCode
									, 'div'
									, {
										tag: 	'option'
										, text: 	opt.countryName
										, data: data
									}
								);

							});
							break;

						case 'state':

							if (
								obj[fieldName]
								&& obj[fieldName].country
							) {

								var country = _.findWhere(
									sb.data.countries
									, {
										countryShortCode: obj[fieldName].country
									}
								);
								if (
									country
									&& country.regions
								) {
									_.each(country.regions, function (region) {

										var data = {
											value: region.shortCode
										};
										if (region.shortCode === val) {
											data.selected = 'selected'
										}
										ui.c[field.name].s.makeNode(
											'o'+ region.shortCode
											, 'div'
											, {
												tag: 	'option'
												, text: 	region.name
												, data: data
											}
										);

									});
								}

							}
							break;

					}

					break;

				default:
					ui.c.makeNode(
						field.name
						, 'div'
						, {
							text: 	'<label>'+ field.title +'</label>'+
									'<input value="'+ val +'" type="text" style="border:none;background-color:transparent;outline:none;width:100%;" placeholder="Empty">'
							, css: 		'field'
							, style: 	'display:inline-block;'+ widthCss
						}
					);
					break;

			}

			ui.c[field.name].listeners.push(
				function (selector) {

					var subField = this;
					switch (field.type) {

						case 'select':
							$(selector +' select').on('change', function () {

								obj[fieldName][subField.name] = $(this).val();

								// If country selection has changed, update the
								// states/province/regions list.
								if (subField.name === 'country') {
									ui.c.state.empty();
									drawInput(
										ui
										, subFields[3]
									);
									ui.c.state.patch();
								}

							});
							$(selector +' select').first().on('blur', function () {

								setTimeout(function () {

									var shouldClose = true;
									_.each(subFields, function (subfield) {

										if (!subfield.isHidden) {

											if (
												$(ui.c[subfield.name].selector +' input').first()
													.is(':focus')
												|| $(ui.c[subfield.name].selector +' select').first()
													.is(':focus')
											) {
												shouldClose = false;
											}

										}

									});

									if (shouldClose && !options.editing) {

										View (
											fieldName
											, ui
											, obj
											, options
										);
										ui.patch();
										commitChange(obj, function () {});

									}

								}, 1);

							});
							break;

						default:
							$(selector +' input').on('change', function () {

								obj[fieldName][subField.name] = $(this).val();

							});
							$(selector +' input').first().on('blur', function () {

								setTimeout(function () {

									var shouldClose = true;
									_.each(subFields, function (subfield) {
										
										if (!subfield.isHidden) {
											
											if (
												$(ui.c[subfield.name].selector +' input').first()
													.is(':focus')
												|| $(ui.c[subfield.name].selector +' select').first()
													.is(':focus')
											) {
												shouldClose = false;
											}
											
										}

									});

									if (shouldClose && !options.editing) {

										var isPending = true;
										_.each(subFields, function (subfield) {
                                            if(obj[fieldName][subField.name] != ''){
												isPending = false;
											}
										});


										if(!isPending){
											View (
												fieldName
												, ui
												, obj
												, options
											);
											ui.patch();
											commitChange(obj, function () {});
										}

									}

								}, 1);

							});
							break;

					}

				}.bind(field)
			);

			if (callback) {
				callback(ui);
			}

		}

		// Draw subfields
		var i = 0;
		var addressFields = _.difference(subFields, _.where(subFields, {isHidden:true}));

		function drawInputs(ui) {

			drawInput(ui, addressFields[i], function(ui) {
				if (i < addressFields.length - 1) {
					i++;
					drawInputs(ui);
				} else {
	
					setTimeout(function () {

						ui.patch();
	
						// Show edit form and hide display node
						$(ui.d.selector).addClass('hidden');
						$(ui.c.selector).removeClass('collapsed');

						if (!options.countryProvinceOnly) {
	
							//$(ui.c.street.selector +' input').first().focus();
	
						} else {
	
							//$(ui.c.state.selector +' input').first().focus();
	
						}
						
					}, 1);
	
				}
			});

		}

		drawInputs(ui);

	}

	return {

		init: function () {

			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'address',
					type: 'Contact Info',
					view: View,
					title: 'Address',
					availableToEntities: 	true,
					icon: 'map marker alternate',
					propertyType: 'object',
					options: {
						countryProvinceOnly: {
							name: 		'Only collect Country & State/Province?'
							, type: 	'bool'
						}
						, placeholder: {
							type: 	'string'
							, name: 'Placeholder'
						}
					},
					detail: function (key, obj, options) {
						
						return getDisplayHtml(key, obj, options);

					}
				}
			});

		}

	}

});
