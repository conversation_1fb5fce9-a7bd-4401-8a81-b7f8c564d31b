var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register ('edge-field', function (sb) {
	
	function createNewItem (fieldName, ui, obj, options, callback, defaultVals) {

		function applyTags (options, parent, seed, onComplete) {
			
			if (options.inheritsTags) {
				
				sb.data.db.obj.getById(
					parent.object_bp_type
					, parent.id
					, function (obj) {
						
						seed.tagged_with = obj.tagged_with;
						onComplete(seed);
						
					}
					, {
						tagged_with: true
					}
				);
				
			} else {
				
				onComplete(seed);
				
			}
			
		}

		var objType = '';
		
		if (
			options.blueprint
			&& options.blueprint[fieldName]
			&& !_.isEmpty(options.blueprint[fieldName].options)
		) {			
			
			objType = options.blueprint[fieldName].options.objectType;
			
		} else if (options.objectType) {
			
			objType = options.objectType;
			
		}
		
		var entityType = _.findWhere(
			appConfig.Types
			, {
				//bp_name: obj.object_bp_type.substr(1)
				bp_name: objType.substr(1)
			}
		);
		
		// Build seed for new object
		var seed = {};
		
		//!#1947: Reference for the current 'carry-over'
		if (
			options.useObjectAsSeed
			&& (
				obj.object_bp_type === objType
				|| objType.split('.')[0] === obj.object_bp_type.split('.')[0]
			)
		) {
			
			seed = _.clone(obj);
			delete seed.id;
			delete seed.object_uid;
			delete seed[fieldName];

			// Get the blueprint of the set, since sometimes this blueprint is the view 
			// on top of the set.
			var blueprint = _.findWhere(
				appConfig.Types
				, {
					bp_name: objType.substr(1)
				}
			).blueprint;
			
			if (
				blueprint
				&& blueprint[fieldName]
				&& !_.isEmpty(blueprint[fieldName].options)
			) {
				
				_.each(blueprint, function (field, key) {
					
					switch (field.fieldType) {
						
						case 'timer':
							seed[key] = 0;
							break;
							
						case 'state':
							var entryPoint = 0;
							var entryPointStatus = '';
							_.each(field.workflow.states, function (state) {
								
								if (state.isEntryPoint) {
									entryPoint = state.uid;
									entryPointStatus = state.type;
								}
								
							});
							seed[key] = entryPoint;
							seed[key +'_status'] = entryPointStatus;
							seed['status'] = 'open';
							break;
						
					}
					
				});
				
			}
			
		} else {
			
			seed = {
				object_bp_type: 	objType
				, parent: 			obj.id
			};
			
		}

		if (!Array.isArray(seed.tagged_with)) {
			seed.tagged_with = [];
		}

		if (!_.isEmpty(options.additionalTags)) {
			seed.tagged_with = seed.tagged_with.concat(options.additionalTags);
		}
		
		// If the parent is space-like, add the parent tag
		if (
			entityType
			&& entityType.is_space
		) {
			
			if (!Array.isArray(seed.tagged_with)) {
				seed.tagged_with = [];
			}
			seed.tagged_with.push(obj.id);
			
		}

		seed.parent = obj.id;

		// Use the default vals, if provided
		if (defaultVals) {
			
			_.each(defaultVals, function (val, key) {
				seed[key] = val;
			});
			
		}
		
		callback(
			obj
			, fieldName
			, seed
		);
		
	}
	
	function createOrSearchInput (ui, searchNodeId, fieldName, obj, options, onComplete, onClose) {

		var newTxt = '';
		var css = '';
        var currentQuery = '';
        var icon = 'green plus icon';
        var where = {
            permissions_check:    true
            , tagged_with: {
                type: 		'any'
                , values: 	[]
            }
        };

		if (options.allowSearch && !options.preventCreate) {
			newTxt = 'Type to search or create..';
		} else if (options.allowSearch) {
			newTxt = 'Type to search..';
		} else if (!options.preventCreate) {
			newTxt = 'Type to create..';
		}
		if (
			options 
			&& !_.isEmpty(options.placeholder)
		) {
			newTxt = options.placeholder;
		}

		if (
			!options.multi
			&& !_.isEmpty(obj[fieldName])
		) {
			css = 'hidden';
		}

        // In portal instances, allow users to search for records that are tagged with 
        // their associated company (in the parent instance).
        if (
            appConfig.is_portal === true
            && typeof appConfig.portal_company === 'number'
        ) {

            where.tagged_with.values.push(appConfig.portal_company);
            where._dont_force_portal_user_tag = true;

        }

        // Adjust per options set on the field by the user
        if (options) {

            // Additional tags
            if (!_.isEmpty(options.additionalTags)) {
                
                where.tagged_with.and = {
                    tagged_with: options.additionalTags
                };

            }

            // Is template filter
            if (options.pointsToTemplate) {
                where.is_template = 1;
            }

        }

        if (options && options.preventCreate === true && options.allowSearch === true) {
            icon = 'grey search icon';
        }

        var searchSetup = {
            id: 	searchNodeId,
            style: 	'padding-top:4px;padding-left:8px;',
            css: 	css,
            text:
                '<div id="" class="ui transparent search" style="border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;">'+
                    '<div class="ui transparent fluid icon input" style="">'+
                        '<input style="font-weight:bold;" id="'+ ui.selector.replace('.', '') +'pointer-search" class="prompt" type="text" placeholder="'+ newTxt +'">'+
                        '<i class="ui '+ icon +'"></i>'+
                    '</div>'+
                    '<div class="results" style="border-top-left-radius: 0px;border-top-right-radius: 0px;outline:none !important;border:0px !important;margin-top:0px;width:100%;"></div>'+
                '</div>',
            listener:{
                type: 		'search',
                objectType: encodeURIComponent(options.objectType),
                onSelect: function(result, response){

                    $('#'+ searchNodeId).search('set value', '');

                    // Link to
                    if (result && result.id !== 0) {
                        
                        if(options.hasOwnProperty('tagParent')){
                            
                            if(options.tagParent == true){
                                
                                if(obj.tagged_with.length > 0){
                                    var parentTags = obj.tagged_with;
                                }else{
                                    var parentTags = [];
                                }
                                
                                parentTags.push(result.id);
                                parentTags = _.map(parentTags, function(tag){
                                    return +tag;
                                }, []);

                                sb.data.db.obj.update(obj.object_bp_type, {id:obj.id, tagged_with:parentTags}, function(update){
                                    
                                    obj.tagged_with = update.tagged_with;
                                    
                                    sb.notify({
                                        type: 'field-updated',
                                        data:{
                                            obj: {
                                                id: obj.id,
                                                tagged_with:obj.tagged_with
                                            }
                                            , type: 		'tags'
                                            , property: 	'tagged_with'
                                        }
                                    });
                                    
                                    onComplete({
                                        id:             result.id
                                        , name:         result.itemName
                                        , isTemplate:   result.isTemplate
                                    });

                                    if (!options.multi) {
                                        $('#'+ searchNodeId).addClass('hidden');
                                    }
                                    
                                });
                                
                            }else{
                                
                                onComplete({
                                    id:             result.id
                                    , name:         result.itemName
                                    , isTemplate:   result.isTemplate
                                });

                                if (!options.multi) {
                                    $('#'+ searchNodeId).addClass('hidden');
                                }
                                
                            }

                        }else{
                            
                            onComplete({
                                id:             result.id
                                , name:         result.itemName
                                , isTemplate:   result.isTemplate
                            });

                            if (!options.multi) {
                                $('#'+ searchNodeId).addClass('hidden');
                            }
                            
                        }
                                    
                    // Create
                    } else if (result && result.id === 0) {

                        onComplete({
                            name: currentQuery
                        });

                        if (!options.multi) {
                            $('#'+ searchNodeId).addClass('hidden');
                        }

                    }
                    
                },
                selectFirstResult: true,
                searchDelay: 100,
                input: function (test) {
                    console.log('oninput::', test);
                },
                where: where,
                selection:{
                    fname: true
                    , lname: true
                    , name:true
                    , parent: {
                        name: true
                    }
                },
                onResponse: function(raw){
                    
                    var response = {
                        results : []
                    };

                    if (!options.preventCreate) {

                        response.results.push({
                            name : '<i class="green plus icon"></i> '+ $('#'+ ui.selector.replace('.', '') +'pointer-search').val(),
                            id: 0
                        });

                    }
                    
                    _.each(raw.results, function(item){
                        
                        var name = item.name;
                        var is_template = false;

                        if ( item.is_template == 1)
                            is_template = true;

                        if (item.object_uid) {
                            name = '<small class="text-muted">#'+ item.object_uid +'</small> '+ name;
                        }
                        var txt = name;
                        if (item.parent && item.parent.name) {
                            txt = '<span class="text-muted"> .. / '+ item.parent.name +' </span> <span class="text-muted"> / </span> '+ name;
                        }
                        
                        var iconTxt = '<i class="ui teal linkify icon"></i> ';

                        
                        if ( is_template ) {
                            iconTxt = '<i class="far fa-clone"></i> ';
                        }

                        txt = iconTxt + txt;

                        if ( is_template ) {
                            txt = '<div>' +
                                    txt + '<div class="ui right floated basic mini button" style="padding:0.4em !important">Use this template</div>' +
                                '</div>';
                        }
                        
                        response.results.push({
                            id: 	        item.id
                            , name:         txt
                            , itemName:     item.name
                            , isTemplate:   item.is_template
                        });
                                                        
                    });

                    return response;
                    
                }
            },
            urlOpts: {
                limit: 			8
                , searchUids: 	true
            }
        };

        if (!options.allowSearch) {

            if (options.preventCreate) {
                return;
            }

            var content = [{
                name: ''
                , id: 0
            }];
            searchSetup.listener.source = content;
            searchSetup.listener.searchDelay = 0;
            searchSetup.listener.searchFields = ['name'];
            searchSetup.listener.cache = false;
            searchSetup.listener.searchOnFocus = true;
            searchSetup.listener.minCharacters = 0;
            searchSetup.listener.onSearchQuery = function (query) {
                
                content[0].name = '<i class="green plus icon"></i> '+ query;
                $('#'+ searchNodeId).search('clear cache');
                $('#'+ searchNodeId).search('setting', 'source', content);
                $('#'+ searchNodeId).search('search local', query);
                
            };

            delete searchSetup.listener.onResponse;

        }

        ui.makeNode('input', 'div', searchSetup);
        
        ui.input.listeners.push(
            function (selector) {

                $('#'+ ui.selector.replace('.', '') +'pointer-search').on('input', function (val) {
                    currentQuery = $(this).val();
                });

            }
        );

        ui.patch();

	}

	// Views
	
	function View (fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			if (obj[fieldName]) {

				if (Array.isArray(obj[fieldName])) {

					ret = '<ul class="edge-list">';

					_.each(obj[fieldName], function (c, i) {

						if (c) {

							ret += '<li class="">' + c.name + '</li>';
						}

					});

					ret += '</ul>';

				} else {
					ret = obj[fieldName].name
				}

			} else {
				ret = '<i class="text-muted">Not set</i>';
			}

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}
		
		function getItemHtml(item) {
			
			return '<i class="ui eye icon"></i>'+ item.name;
			
		}
		
		function viewItemInModal(item) {

			sb.notify({
				type: 	'get-sys-modal'
				, data: 	{
					callback: 	function (modal) {
						
						modal.body.empty();
						modal.show();
						
						modal.body.makeNode('menu', 'div', {
							css: 'ui secondary right floated menu',
							style: 'margin:0 !important;'
						});
						
						var linkSetup = { 
							css:'circular icon button item', 
							text:'<i class="external square alternate icon"></i>', 
							tag:'a',
							href:sb.data.url.getObjectPageParams(item, {})
						};
			
						var closeLinkSetup = { 
							css:'circular icon button item', 
							text:'<i class="close icon"></i>', 
							tag:'a'
						};
						
						modal.body.menu.makeNode('open', 'div', linkSetup);
						
						modal.body.menu.makeNode('close', 'div', closeLinkSetup)
							.listeners.push(
								
								function (selector) {
									
									$(selector).on(
										'click'
										, function () {
											modal.hide();
										}
									);
									
								}
							);
						
						modal.body.makeNode('c', 'div', {});
						modal.body.patch();
						
						sb.notify({
							type: 'view-entity'
							, data: {
								ui: 	modal.body.c
								, id: 	item.id
							}
						});
						
					},
					onClose: function(){

						var updateFields = {};

						_.each(options.blueprint, function(fieldObj, fieldId){
							
							if(fieldObj.fieldType == 'reference-calculation'){
								
								updateFields[fieldId] = fieldObj;
								
							}
							
						});
						
						if(!_.isEmpty(updateFields)){
							
							setTimeout(function(){
								
								sb.data.db.obj.getById('', obj.id, function(updatedObj){
									
									_.each(updateFields, function(fieldObj, fieldId){
										
										var updateDBObj = {
											id:obj.id
										};
										updateDBObj[fieldId] = updatedObj[fieldId];
										
										sb.notify({
											type: 'field-updated',
											data:{
												obj: updateDBObj
												, type: 		'reference-calculation'
												, property: 	fieldId
											}
										});
										
									});
									
								});
								
							}, 500);
							
						}
											
					}
				}
			});
			
		}
		
		function viewItem(ui, item) {

			ui.makeNode(
				'i-'+ item.id
				, 'div'
				, {
					css: 	'ui icon button'
					, text: 	'<nobr>'+getItemHtml(item)+'</nobr>'
					, isTabbable: true
				}
			).listeners.push(
				
				function (item, selector) {
					
					$(selector).on(
						'click'
						, function (e) {

							e.stopPropagation();
							e.preventDefault();
							
							if (
								appConfig.instance == item.instance
								|| !_.isEmpty(_.findWhere(appConfig.Types, {bp_name: item.object_bp_type.substr(1)}))
							) {
								viewItemInModal(item);
							}
							
						}
					);
					
					$(selector).on(
						'focus'
						, function (e) {

							e.stopPropagation();
							e.preventDefault();
							
						}
					);

					
				}.bind({}, item)
				
			);
			
		}
		
		function viewInList (fieldName, ui, obj, options) {

			var listData = [];
			var fields = {};
			var multiSelectObjTypes = options.blueprint[fieldName].options.objectType;
			var pointToBp = _.findWhere(appConfig.Types, {bp_name: options.objectType.substr(1)});
			
			// Utility funcs
			function getListData (obj, fieldName) {

				var list = [];
				
				if (Array.isArray(obj[fieldName])) {
					list = obj[fieldName];
				} else if (!_.isEmpty(obj[fieldName])) {
					list = [obj[fieldName]];
				} else if (typeof obj[fieldName] === 'object') {
					list = [obj[fieldName]];
				}

				return _.chain(list)
							.compact()
								.value();

			}

			function isStateful (type) {
				// Checks is an entity type has any workflows.
				
				var ret = false;
				
				_.each(
					type.blueprint
					, function (field, key) {
						
						if (
							!field.is_archived
							&& field.fieldType === 'state'
						) {
							ret = true;
						}
						
					}
				);
				
				return ret;
				
			}
			
			// View funcs

			function viewList(ui) {

				sb.notify({
					type:'field-list-view'
					, data: {
						container: 	ui
						, list: 	listData
						, options: {
							actions: 		true
							, actions_ui: 	function (container, nestedObj) {
								
								var css = '';
								var menuCss = 'left menu';
								var actionButtonCss = 'ui mini basic circular icon simple dropdown';
								var actionButtonStyle = 'border:none;';
								var menuStyle = '';
				
								container.makeNode('actions', 'div', {css:css});
								container.actions.makeNode('menu', 'div', {
									css:'right floated ui mini icon circular basic simple dropdown',
									text:'<i class="ellipsis horizontal centered icon" style="font-size:1.5em !important;"></i>',
									style:'text-align:center;'
								}).makeNode('menu', 'div'
									, {
										css: 		menuCss
									}
								);
								
								// View
								container.actions.menu.menu.makeNode('linkTo', 'div', {
									css: 	'ui blue basic fluid item',
									text: 	'<i class="external square blue icon"></i> Go to',
									tag: 	'a',
									style: 	'border-radius:0px;',
									href: 	sb.data.url.getObjectPageParams(nestedObj)
								});

								// Duplicate
								if (options.commitUpdates !== false) {

									container.actions.menu.menu.makeNode(
										'duplicate'
										, 'div'
										, {
											css: 		'ui blue item'
											, text: 	'<i class="ui copy outline icon"></i> Duplicate'
											, style: 	'border-radius:0px;'
										}
									).notify('click', {
										type: [sb.moduleId+'-run'],
										data: {
												run: function(data) {
		
													var actionSetup = {
															'updateEdge': {
																obj: 			obj.id
																, multi: 		options.multi
																, fieldName: 	fieldName
																, userId: 		appConfig.user.id
																, template: 	nestedObj.id
															}
																
														};
		
													sb.data.db.obj.runSteps(
														actionSetup,
														obj.id,
														function (res) {
																	
															obj[fieldName] = res[fieldName];
															
															listData = getListData(res, fieldName);
															
															ui.empty();
															viewList(ui);
															ui.patch();
															
														}
													);
													
												}
											}
										}, sb.moduleId);
									
								}
								
								// Unlink
								container.actions.menu.menu.makeNode(
									'unlink'
									, 'div'
									, {
										css: 		'ui orange item'
										, text: 	'<i class="orange unlink icon"></i> Unlink'
										, style: 	'border-radius:0px;'
									}
								).notify('click', {
									type: [sb.moduleId+'-run'],
									data: {
											run: function(data) {
												
												sb.dom.alerts.ask({
													title: 	'Unlink '+ nestedObj.name +' from '+ obj.name +'?'
													, text: ''
												}, function (response) {
													
													if (response) {
	
														swal.disableButtons();

														if (options.commitUpdates) {

															sb.data.db.obj.runSteps(
																{
																	'updateEdge': {
																		obj: nestedObj
																		, parent: obj.id
																		, unlink: true
																		, multi: options.multi
																		, fieldName: fieldName
																	}
																		
																},
																nestedObj,
																function (res) {
		
																	swal.close();
																	
																	obj[fieldName] = res[fieldName];
																	
																	listData = getListData(res, fieldName);
																	
																	ui.empty();
																	viewList(ui);
																	ui.patch();
																	
																}
															);

														} else {

															swal.close();
															obj[fieldName] = _.filter(
																obj[fieldName]
																, function (listItem) {
																	return listItem.id !== nestedObj.id;
																}
															);
															listData = getListData(obj, fieldName);
															ui.empty();
															viewList(ui);
															ui.patch();

														}
														
													} else {
														
														swal.close();
														
													}
													
												});
												
											}
										}
									}, sb.moduleId);
								
								// Archive
								if (options.commitUpdates !== false) {
									
									container.actions.menu.menu.makeNode(
										'archive'
										, 'div'
										, {
											css: 		'ui red item'
											, text: 	'<i class="red trash icon"></i> Archive'
											, style: 	'border-radius:0px;'
										}
									).notify('click', {
										type: [sb.moduleId+'-run'],
										data: {
												run: function(data) {

													sb.dom.alerts.ask({
														title: 	'Archive '+ nestedObj.name +'?'
														, text: ''
													}, function (response) {
														
														if (response) {
															
															swal.disableButtons();
															
															sb.data.db.obj.runSteps(
																{
																	'updateEdge': {
																		obj: nestedObj
																		, parent: obj.id
																		, remove: true
																		, multi: options.multi
																		, fieldName: fieldName
																	}
																		
																},
																nestedObj,
																function (res) {

																	swal.close();
																	
																	obj[fieldName] = res[fieldName];
																	
																	listData = getListData(res, fieldName);
																	
																	ui.empty();
																	viewList(ui);
																	ui.patch();
		
																	$('.results').addClass('hidden');
																	
																}
															);
															
														} else {
															
															swal.close();
															
														}
														
													});
													
												}
											}
										}, sb.moduleId);

								}
								
								container.patch();
								
							}
							, create_ui: function (createBtnContainer) {}
							, emptyMessage: false
							, fields: fields
							, updateSelection: function () {}
							, style: 'padding:0px;'
							, showDate: true
							, showQuantity: true
							, showTimer: true
							, showMultiplePerType: true
							, groupStyle: 'margin:0px;border-none;'
							, isSortable: true
						}
					}				
				});

			}
				
			if (_.isEmpty(options.objectType)) {
			
				ui.makeNode('msg', 'div', {
					css: 'ui compact warning message'
					, text: 'Please select a set to point to.'
				});
				return;
				
			}

			if (_.isEmpty(pointToBp)) {
				
				ui.makeNode('msg', 'div', {
					css: 'ui compact warning message'
					, text: 'There was an error getting the data for this field.'
				});
				
				return;
				
			}
			
			listData = getListData(obj, fieldName);
			
			fields = {
				name: {
					title: 'Name'
					, type: 'title'
					, view: function(ui, obj) {

						sb.notify({
							type: 'view-field'
							, data: {
								ui: ui
								, type: 'title'
								, obj: obj
								, fieldName: 'name'
								, property: 'name'
								, options: {
									inCollection: true
									, uid: true
									, onClick: function (obj) {
										
										if (
											appConfig.instance == obj.instance
											|| !_.isEmpty(_.findWhere(appConfig.Types, {bp_name: obj.object_bp_type.substr(1)}))
										) {
											viewItemInModal(obj);
										}
										
									}
								}
							}
						});
						
					}
				}
			};
			
			if (isStateful(pointToBp)) {
				
				fields['any-state'] = {
					type: 		'toggle'
					, title: 	'Status'
					, options: {
						overall_status: true
						, edit: 		false
					}
					, view: 	function (ui, obj) {
						
						var txt = '';
						switch (obj.status) {
							
							case 'done':
								txt = '<i class="large square check icon bento-checked"></i>';
								break;
								
							default:
								txt = '<i class="large square outline icon bento-unchecked"></i>';
								break;
							
						}
						
						sb.notify ({
							type: 'view-field'
							, data: {
								type: 			'toggle'
								, property: 	'any-state'
								, obj: 			{
									id: obj.id
									, status: obj.status
								}
								, options: 		{
									edit: 		false
									, editing: 	false
									, overallStatus: true
									, inCollection: true
								}
								, ui: 			ui
							}
						});
						
					}
				};
				
			}
			
			// Show additional fields
			if (Array.isArray(options.includeInSelect)) {
				
				_.each(options.includeInSelect, function (fieldKey) {
					
					if (pointToBp.blueprint[fieldKey]) {
						
						fields[fieldKey] = {
							type: 		pointToBp.blueprint[fieldKey].fieldType
							, title:	pointToBp.blueprint[fieldKey].name
							, options: 	pointToBp.blueprint[fieldKey].options
							, view: 	function (ui, obj, state) {

								var options = _.clone(pointToBp.blueprint[fieldKey].options);
								if (_.isEmpty(options)) {
									options = {};
								}

								options.edit = false;
								options.editing = false;
								options.overallStatus = true;
								options.inCollection = true;
								options.mini = true;
								options.labelView = true;
								options.placeholder = '';

								if (this.field.fieldType == 'timer') {
									options.edit = true;
								}
								
								sb.notify({
									type: 'view-field'
									, data: {
										type: 			this.field.fieldType
										, property: 	this.key
										, obj: 			obj
										, options: options
										, ui: ui
									}
								});
								
							}.bind({
								field: 	pointToBp.blueprint[fieldKey]
								, key: 	fieldKey
							})
						};
						
					}
					
				});
				
			}
			
			ui.makeNode('list', 'div', {});
			
			var createBtnContainer = ui.makeNode('create', 'div', {});
			var searchNodeId = createBtnContainer.selector.replace('.', '');
			
			createOrSearchInput(createBtnContainer, searchNodeId, fieldName, obj, options, function (defaultVals) {

				function addToList (defaultVals, callback) {

					if (defaultVals && defaultVals.id) {

                        var def;

                        if ( defaultVals.isTemplate == 1 ) {
                            def = defaultVals;
                        } else {
                            def = defaultVals.id;
                        }

						callback(obj, fieldName, def);

					} else {

						createNewItem (fieldName, ui, obj, options, function (obj, fieldName, newVal) {

							callback(obj, fieldName, newVal);
						
						}, defaultVals);

					}

				}

				addToList(defaultVals, function (obj, fieldName, newVal) {

					if (
						options.blueprint
						&& options.blueprint[fieldName]
						&& !_.isEmpty(options.blueprint[fieldName].options)
					) {
						
						multi = options.blueprint[fieldName].options.multi;
						
					} else if (options.objectType) {

						multi = options.multi || false;

					}

					var actionSetup = {
						'updateEdge': {
							multi: multi
							, fieldName: fieldName
							, userId: appConfig.user.id
							, pointsToTemplate: options.pointsToTemplate
						}
					};

					// To create object from template
					if (options.useObjectAsSeed) {
						actionSetup['updateEdge'].template = obj.id;
					}

					if ( typeof newVal === 'number') {

						actionSetup['updateEdge'].obj = {
							id: newVal
						};

						actionSetup['updateEdge'].parent = obj.id;

					} else {

                        actionSetup['updateEdge'].obj = newVal;

                        if ( newVal.isTemplate == 1){
                            actionSetup['updateEdge'].parent = obj.id;
                            actionSetup['updateEdge'].createTemplate = false;
                        }

                    }

					if (
						options.commitUpdates
						&& obj.id
					) {								

						$('#loader').fadeIn();

						sb.data.db.obj.runSteps(
							actionSetup,
							obj.id,
							function (res) {
										
								$('#loader').fadeOut();

								obj[fieldName] = res[fieldName];
								
								listData = getListData(res, fieldName);

								ui.list.empty();
								viewList(ui.list);
								ui.list.patch();
								
							}
						);
						
					} else {
							
						// When updates do not need to be committed (setting values on 
						// non-record objs)
						var valToUpd = [];
						if (options.multi) {

							valToUpd = [];
							if (Array.isArray(obj[fieldName])) {
								valToUpd = _.pluck(obj[fieldName], 'id');
							}
							valToUpd.push(newVal);

						} else {

							valToUpd = newVal;

						}

						sb.data.db.obj.getById(options.objectType, valToUpd, function (selected) {

								// Need to account for selected already being an array below
								if (options.multi) {
									obj[fieldName] = selected;
								} else {
									obj[fieldName] = [selected];
								}
								
								listData = getListData(obj, fieldName);
	
								ui.list.empty();
								viewList(ui.list);
								ui.list.patch();
								
							}
							, selectionObj
						);
						
					}
					
				});

			});
			
			viewList(ui.list);
			ui.patch();
			
		}
		
		// Check if obj exists in scope
			// Prevent user from trying to set a default value (for now)
		
		var canCreate = true;
		var placeholder = options.placeholder || 'Empty';
		var selectionObj = true;

		if (!obj.hasOwnProperty('id')) {

			if (fieldName === '_default') {

				ui.makeNode('alert', 'div', {
					css:'ui grey text', 
					text:'Adding default values for this field is not available at this time.'
				});
				
				ui.makeNode('br1', 'lineBreak', {
					spaces: 1
				});
				
				return;

			}

			canCreate = false;
			options.canCreate = false;
			options.allowSearch = true;
			
			
		}
		
		sb.notify({
			type: 'get-selection-from-blueprint'
			, data: {
				type: obj.object_bp_type
				, callback: function (select) {
					if (select && select[fieldName]) {
						selectionObj = select[fieldName];
					}
				}
			}
		});

		options.listView = true;
		
		if (options.hasOwnProperty('canCreate')) {
			canCreate = options.canCreate;
		}
		
		if (
			options.listView 
			&& !options.inCollection
			&& !options.mini
		) {
			
			viewInList(fieldName, ui, obj, options);
			return;
			
		}

		if(canCreate && !options.inCollection){
			
			ui.makeNode(
				't'
				, 'div'
				, {
					isTabbable: true
					, css: 'edge-field revealParent'
				}
			);
			
		}else{
			
			ui.makeNode(
				't'
				, 'div'
				, {
					isTabbable: true
					, css: 'edge-field'
				}
			);
			
		}

		if (options.inCollection) {
			ui.t.makeNode('edgeList', 'div', {
				css: 'edge-list',
				tag: 'ul'
			});
		}

		// empty view
		if (_.isEmpty(obj[fieldName])) {

			ui.t.makeNode(
				'msg'
				, 'div'
				, {
					css: 'field-placeholder'
					, text: placeholder
				}
			);
			
		} else if (Array.isArray(obj[fieldName])) {

			_.each(obj[fieldName], function (item) {

				if (!options.inCollection) {
				
					viewItem(ui.t, item);

				} else {

					ui.t.edgeList.makeNode('item-' + item.id, 'div', {
						text: item.name,
						tag: 'li'
					});

				}
				
			});
			
		} else {

			if (!options.inCollection) {

				viewItem(ui.t, obj[fieldName]);

			} else {

				ui.t.edgeList.makeNode('item-' + obj[fieldName].id, 'div', {
					text: obj[fieldName].name,
					tag: 'li'
				});

			}
			
		}
		
		if (canCreate && !options.inCollection) {
			
			ui.t.makeNode(
				'c'
				, 'div'
				, {
					css: 'ui circular mini basic icon button revealChild'
					, text: '<i class="ui green plus icon"></i> new'
					, style: 'box-shadow:none;background-color:white !important;'
				}
			);
			
			ui.t.c.listeners.push(
				function (selector) {
	
					if (options.edit || options.editing) {
						
						$(selector).on('click', function (e) {
							
							e.stopPropagation();
							
							createNewItem(fieldName, ui, obj, options, function (obj, fieldName, newVal) {

								if (
									options.blueprint
									&& options.blueprint[fieldName]
									&& !_.isEmpty(options.blueprint[fieldName].options)
								) {
									
									multi = options.blueprint[fieldName].options.multi;
									
								} else if (options.objectType) {
	
									multi = options.multi || false;
	
								}
	
								/*
	var updateVal = obj[fieldName];
								
								if (
									multi
									&& typeof newVal === 'number'
								) {
									if (_.isEmpty(updateVal)) {
										updateVal = [];
									}
									updateVal.push(newVal);
								} else {
									updateVal = newVal;
								}
	*/
								
								if (options.commitUpdates) {								
	
									sb.data.db.obj.getById(
										obj.object_bp_type
										, obj.id
										, function(resp) {
			
											ui.empty();
											obj[fieldName] = resp[fieldName];
											View (
												fieldName
												, ui
												, obj
												, options
											);
											ui.patch();
											
										}, {
											[fieldName]: 1
										});
									/*
	sb.data.db.obj.update(
										obj.object_bp_type
										, {
											id: 			obj.id
											, [fieldName]: 	updateVal
										}
										, function (response) {
		
											ui.empty();
											obj[fieldName] = response[fieldName];
											View (
												fieldName
												, ui
												, obj
												, options
											);
											ui.patch();
											
										}
										, {
											[fieldName]: 1
										}
									);
	*/
									
								} else {
	
									sb.data.db.obj.getById(objType, updateVal, function (selected) {
										
										obj[fieldName] = selected;
										ui.empty();
										View (
											fieldName
											, ui
											, obj
											, options
										);
										ui.patch();
										
									});
									
								}
								
							});
							
						});
						
					}
					
				}
			);
			
		}
		
		// Don't show the dropdown if create is on and its not multi
		if (
			!canCreate
			|| options.multi
		) {
			
			ui.t.listeners.push(
				function (selector) {
					
					if (options.edit || options.editing) {
						
						$(selector).on('click', function () {
							
							Edit (
								fieldName
								, ui
								, obj
								, options
							) ;
							
						});
						
					}
					
				}
			);
			
		}
		
	}
	
	function Edit (fieldName, ui, obj, options) {

		function createIfNeeded (
			obj
			, fieldName
			, newVal
			, callback
		) {
			
			var formComplete = false;
			if (
				newVal === 'new'
				|| _.contains(newVal, 'new')
			) {
				
				var seed = {
					object_bp_type: 	objType
					, parent: 			obj.id
				};
				
				if (options.inheritsTags) {
					seed.tagged_with = obj.tagged_with;
				}
				
				sb.notify({
					type: 	'create-new-entity'
					, data: 	{
						type: 			objType
						, seed: 		seed	
						, onClose: 		function () {
							
							if (!formComplete) {
								View(fieldName, ui, obj, options);
								ui.patch();
							}
							
						}
						, onComplete: 	function (created) {
							
							formComplete = true;
							callback(
								obj
								, fieldName
								, created.id
							) ;
														
						}
					}
				});
				
			} else {
				
				callback(
					obj
					, fieldName
					, newVal
				) ;
				
			}
			
		}
		
		var allowSearch = true;
		var canCreate = true;
		var objType = '';
		var multi = false;
		var userOptions = [];
		var searchNodeCSS = 'ui search selection transparent fluid dropdown';
		var values = [];
		// !TODO: where should be createable in field editor
		var where = {};
		
		if (
			options.blueprint
			&& options.blueprint[fieldName]
			&& !_.isEmpty(options.blueprint[fieldName].options)
		) {
			
			objType = options.blueprint[fieldName].options.objectType;
			multi = options.blueprint[fieldName].options.multi;
			allowSearch = options.blueprint[fieldName].options.allowSearch;
			
		} else if (options.objectType) {
			objType = options.objectType;
			multi = options.multi || false;
			if (options.where) {
				where = options.where;
			}
		}
		
		if (options.hasOwnProperty('canCreate')) {
			canCreate = options.canCreate;
		}

		ui.empty();

		if(multi){

			/*
if (options.listView) {
				
				viewInList(fieldName, ui, obj, options);
				return;

				
			} else {
*/
			
				searchNodeCSS = 'ui fluid transparent multiple selection search dropdown';
				values = _.map(obj[fieldName], function (obj) {
					
					return {
						value: 		obj.id
						, name: 	obj.name
						, selected: true
					};
					
				});
				
// 			}

				
		} else if (!_.isEmpty(obj[fieldName])) {
			
			values = [{
				value: 		obj[fieldName].id
				, name: 		obj[fieldName].name
				, selected: 	true
			}];
			
		}
		
// 		ui.empty();
		
		var searchSetup = {
			css: 	searchNodeCSS +' edge-field',
			style: 	'border:none;',
			text:
				'<input type="hidden" name="user">'+
				'<i class="dropdown icon"></i>'+
				'<input type="text" class="transparent search search-input" tabindex="0" style="padding:7px 15px !important; height:100%;">'+
				'<div class="default text">Find a user</div>'+
				'<div class="menu transition hidden" tabindex="-1"></div>',
			listener: 	{
				type: 'dropdown',
				values: values,
				saveRemoteData: false,
				minCharacters: 0,
				onShow() {

					setTimeout(function() {
						$('.floater-container').css({'overflow':'visible'});
					}, 100);
						
				},
				onHide: function () {

					setTimeout(function() {
						$('.floater-container').css({'overflow-y':'scroll', 'overflow-x':'auto'});
					}, 100);
					
					if (typeof options.onEditEnd === 'function')
						options.onEditEnd();
					
					var newVal = $(searchNode.selector).dropdown('get value');

					if(multi){
						
						newVal = newVal.split(',');
						
					}else{
						
						newVal = parseInt(newVal);
						if (isNaN(newVal)) {
							
							newVal = $(searchNode.selector)
										.dropdown('get value');
						
						}
						
					}	

					searchNode.loading();
					
					ui.t.loading();

					createIfNeeded (
						obj
						, fieldName
						, newVal
						, function (
							obj
							, fieldName
							, newVal
						) {
							
							var updateVal = obj[fieldName];
							
							if (
								multi
								&& typeof newVal === 'number'
							) {
								if (_.isEmpty(updateVal)) {
									updateVal = [];
								}
								updateVal.push(newVal);
							} else {
								updateVal = newVal;
							}

							if (options.commitUpdates) {								
								
								sb.data.db.obj.update(
									obj.object_bp_type
									, {
										id: 			obj.id
										, [fieldName]: 	updateVal
									}
									, function (response) {

										ui.empty();
										obj[fieldName] = response[fieldName];
										View (
											fieldName
											, ui
											, obj
											, options
										);
										ui.patch();

										// sb.notify({
										// 	type: 'reference-calculation',
										// 	data:{
										// 		obj: {
										// 			id: obj.id,
										// 			tagged_with:obj.tagged_with
										// 		}
										// 		, type: 		'tags'
										// 		, property: 	'tagged_with'
										// 	}
										// });
										
									}
									, {
										[fieldName]: 1
									}
								);
								
							} else {
								
								sb.data.db.obj.getById(objType, updateVal, function (selected) {

									obj[fieldName] = selected;
									ui.empty();
									View (
										fieldName
										, ui
										, obj
										, options
									);
									ui.patch();
									
								});
								
							}
							
						}
					);
					
				},
				onAdd: function (addedValue) {

					if (addedValue === 'new') {
						
						createIfNeeded (
							obj
							, fieldName
							, addedValue
							, function (newItem) {
								
								if (multi) {
									obj[fieldName].push(newItem);
								} else {
									obj[fieldName] = newItem;
								}
								
							}
						);
						
					}
					
				},
				onRemove: function (removedValue) {

					// remove values from cached state
					
					if (multi) {
						
						obj[fieldName] = _.filter(
							obj[fieldName]
							, function (item) {
								
								return item.id !== parseInt(removedValue);
								
							}
						);
						
					}
					
				}
			}
			, urlOpts: {
				limit: 			8
				, searchUids: 	true
			}
		};
		
		if (allowSearch) {

			searchSetup.listener.apiSettings = {
				cache: 	false
				, url: 	databaseConnection.obj.getSearchPath(
					objType.replace('#', '%23')
					, where
					, {}
					, {
						parentObjectType: obj.object_bp_type.replace('#', '%23')
					}
				)
				, onResponse: 			function (raw) {

					var results = [];
					var selectedIds = _.pluck(values, 'value');
					_.each(raw.results, function (item) {
						
						var selection = $(searchNode.selector).dropdown('get value');
						selection = selection.split(',');
						
						// filter out items without names, and items already
						// added to selection
						if (
							!_.isEmpty(item.name) 
							&& !_.contains(selectedIds, item.id)
						) {
							
							results.push({
								value: parseInt(item.id)
								, name: item.name
							});
							
						}
						
					});
					
					if (canCreate) {
						/*

						results.push({
							value: 		'new'
							, name: 	'<i class="green plus icon"></i> New'
						});
						
*/
					}
					
					return {
						results: results
					};
					
				}
				, mockResponseAsync: 	function(settings, callback){
					
					function GetCookieValue(name) {
					    var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
					    return found.length > 0 ? found[0].split("=")[1] : null;
					}
					
					var headers = {
						'bento-token': GetCookieValue('token')
					};
					if (appConfig.is_portal) {
						
						headers.portal = appConfig.state.portal;
						headers['bento-token'] = GetCookieValue('p_token');
						
					}
					
					$.ajax({
						type: 'post',
						url: settings.url,
						xhrFields: {
							withCredentials: true
						},
						crossDomain: true,
						data: {},
						success: function (response) { callback(response); },
						error: function (jqXHR, status, error) {},
						headers: headers
					});	
					
				}
			};
			
		} else {
			
			searchSetup.listener.values = values;
			/*
searchSetup.listener.values.push({
				value: 		'new'
				, name: 	'<i class="green plus icon"></i> New'
			});
*/
			
		}
		
		var searchNode = ui.makeNode('t', 'div', searchSetup);
		
		searchNode.listeners.push(function (selector) {
			
			$(selector).find('.search-input').focus();
						
		});
			
		ui.patch();
		
	}
	
	function multiEdit (fieldName, ui, obj, options) {

		var multiSelectObjTypes = options.blueprint[fieldName].options.objectType;
		
		sb.notify({
			type:'field-list-view'
			, data: {
				ui:		 ui
				, list:	 obj[fieldName]
				, options: {
					create_ui: function(){}
					, fields: {
						name: {
							title: 'Name'
							, type: 'title'
							, view: function(ui, obj){

								ui.makeNode('name' , 'div'
									, {
										css: ''
										, text: obj.name
									}
								);
								
							}
						}
					}
				}
			}				
		});	
		
		ui.patch();	
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'edge'
					, view: View
					, title: 'Pointer'
					, type: 'Link to Other Sets'
					, availableToEntities: true
					, icon: 'external square'
					, getIcon: function (options) {
						return 'external square';
					}
					, propertyType: 'objectId'
					, objectType: ''
					, select: {
						name: 		true
						, status: 	true
						, object_uid: true
					}
					, options: {
						allowSearch: {
							name: 		'Allow selections through search?'
							, type: 	'bool'
						}
						, multi: {
							name: 		'Allow multiple selections?'
							, type: 	'bool'
						}
						/*
, listView: {
							name: 		'Show in List?'
							, type: 	'bool'
						}
*/
						, objectType: {
							name: 	'Select a data set'
							, type: 'objectType'
						}
						, placeholder: {
							type: 	'string'
							, name: 'Placeholder'
						}
						, preventCreate: {
							name: 		'Prevent creates?'
							, type: 	'bool'
						}
						, inheritsTags: {
							name: 	'Inherits Tags? (passes on tags to records created here)'
							, type: 'bool'
						}
						, tagParent: {
							name: 	'Tag the parent?'
							, type: 'bool'
						}
						, pointsToTemplate: {
							name: 	'Items in this pointer are a template(s)?'
							, type: 'bool'
						}
						, useObjectAsSeed: {
							name: 	'Use main object as template? (if child is the same type)'
							, type: 'bool'
						}
						, additionalTags: {
							name: 	'Additional Tag(s)'
							, type: 'tags'
						}
						, includeInSelect: {
							name: 	'Show field(s) in list'
							, type: 'field'
							, blueprint: 'objectType'
							, allowedTypes: ['date', 'user', 'quantity', 'priority', 'timer']
						}
					}
					, parseOptions: function (selection, field) {
						
						if (selection.multi) {
							field.type = 'objectIds';
						}
						field.objectType = selection.objectType;
						
						return field ;
						
					}
				}
			});
			
			sb.listen({
				[sb.moduleId + '-run']: this.run
			});

			if (IN_NODE_ENV) {
				var edge = View;
				module.exports = { 
					edge
				}
			}
			
		}
		
		, run: function(data) { data.run(data); }
		
	};
	
});

if (IN_NODE_ENV) {
	Factory.startAll();
}