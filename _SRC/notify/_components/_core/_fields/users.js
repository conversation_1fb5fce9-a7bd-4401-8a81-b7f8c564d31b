var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Get dependencies
	var Factory = require('../_core.js').Factory;
	var _ = require('underscore-node');

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

Factory.register('user-field', function (sb) {

	var AvailableMergeTags = [];

	var UserFieldMergeTags = [{
		value: 	'me',
		name: 'me',
		display: '<span style="display:inherit; border-bottom:1px solid lightgray; padding-bottom:4px;"><b>User</b> <i>triggering</i> <b>record creation</b></span></br><span style="display:block; margin-top:6px;"><i>{{me}}</i></span>',
		bpName: 'ANY'
	}];

	function getMergeTags(fieldName, ui, obj, options, callback) {

		if (options.context) {
			obj = options.context;
		}

		var bpName = 0;
		var fieldType = options.blueprint[fieldName].fieldType;

		var allowedTypes = [fieldType];
		if (options.multi) {
			allowedTypes.push(fieldType);
		}

		function completeCallback(bpName, callback) {

			sb.notify({
				type: 'get-field-merge-tags',
				data: {
					rootSet: obj.object_bp_type,
					options: {
						allowedTypes: allowedTypes,
						allowMulti: (options.multi || false),
						parentSet: 'ANY',
						context: bpName
					},
					callback: function (tags) {

						if (bpName == 0) {

							AvailableMergeTags = tags;

						} else {

							AvailableMergeTags = _.filter(tags, function(tag) {

								if (tag.bpName == bpName || tag.bpName == 'ANY' || tag.type == 'associated') {
									return tag;
								}
	
							});

						}

						if (fieldType == 'users' || fieldType == 'user') {
							AvailableMergeTags = UserFieldMergeTags.concat(AvailableMergeTags);
						}

						callback({
							bpName: bpName
						});
						
					}
				}
			});

		}

		if (obj.id) {

			bpName = obj.parent && obj.parent.hasOwnProperty('bp_name') ? obj.parent.bp_name : 0;

			if (bpName) {

				// If object has parent with bp_name
				completeCallback(bpName, function(ret) {
					callback(ret);
				});

			} else {

				// If within workflow editor
				if (obj.parent && obj.parent.hasOwnProperty('object_bp_type') && obj.parent.object_bp_type == 'event_type') {

					if (obj.parent.hasOwnProperty('object') && obj.parent.object) {
	
						sb.data.db.obj.getById('', obj.parent.object, function(workflow) {
	
							sb.data.db.obj.getById('', workflow.parent, function(parent) {
	
								bpName = parent.bp_name;
	
								completeCallback(bpName, function(ret) {
									callback(ret);
								});
	
							});
	
						});
	
					} else {
	
						// If within the list view or single view page
						sb.data.db.obj.getById(obj.parent.object_bp_type, obj.parent.id, function(parent) {
	
							sb.data.db.obj.getById('', parent.object, function(object) {
	
								sb.data.db.obj.getById('', object.parent, function(set) {
	
									bpName = set.bp_name;
	
									completeCallback(bpName, function(ret) {
										callback(ret);
									});
	
								});
	
							});
	
						});
	
					}
	
				} else {

					// If no parent context
					completeCallback(bpName, function(ret) {
						callback(ret);
					});

				}

			}

		} else {

			// If no context
			completeCallback(bpName, function(ret) {
				callback(ret);
			});
			
		}

	}

	function View(fieldName, ui, obj, options) {

		if (!ui) {

			var ret = '';

			if (obj[fieldName]) {

				if (Array.isArray(obj[fieldName])) {

					ret = '<ul class="edge-list">';

					_.each(obj[fieldName], function (c, i) {

						if (c) {

							if (c.object_bp_type == 'companies') {

								ret += '<li class="">' + c.name + '</li>';

							} else {

								ret += '<li class="">' + c.fname +' '+ c.lname + '</li>';

							}
	
						}

					});

					ret += '</ul>';

				} else {

					if (obj[fieldName].object_bp_type == 'companies') {

						ret = obj[fieldName].name;

					} else {

						ret = obj[fieldName].fname +' '+ obj[fieldName].lname;

					}

				}

			}

			if (typeof options.callback === 'function') {
				options.callback(ret);
			}

			return;

		}

		var onContacts = false;
		var disabledCursor = '';
		var multiSelect = false;
		var isTemplate = options.isTemplate === true || options.is_template === true || obj.is_template === 1 ? true : false;

		if (options.multi) {
			multiSelect = options.multi;
		}

		function buildUser(ui, user, userIndex, list, bpName) {

			function getUserProfileImage(user, obj, options, callback) {

				if (typeof user.profile_image === 'number') {
				
					sb.data.db.obj.getById('file_meta_data', user.profile_image, function(img) {
	
						user.profile_image = img;
	
						callback(user);
	
					}, 1);
	
				} else {
	
					callback(user);
	
				}

			}
			
			getUserProfileImage(user, obj, options, function(user) {
				
				if (isTemplate && typeof user === 'string') {

					var selectedTag = user;

					if (
						(
							user.startsWith('this.Parent.')
							|| user.startsWith('this.parent.')
						)
						&& !user.startsWith('this.Parent.(')
						&& !user.startsWith('this.parent.(')
						&& !user.startsWith('this.Parent.#')
						&& !user.startsWith('this.parent.#')
					) {

						if (bpName) {
							selectedTag = 'this.parent.('+ bpName.replace(/\./g, '-') +').'+ user.split('.')[2];
						}

					}

					selectedTag = _.findWhere(AvailableMergeTags, {value: selectedTag});

					if (selectedTag) {

						ui.makeNode('u-' + userIndex, 'div', {
							css: 'ui label bento-merge-tag',
							text: selectedTag.display
						});

						ui.patch();

					}

				}
					
				if (
					_.isEmpty(user) 
					|| (
						user.object_bp_type !== 'users'
						&& user.object_bp_type !== 'contacts'
					)
				) {
					return;
				}
				
				if (userIndex !== undefined && list !== undefined) {

					if (userIndex !== 0) {
						userIcon_pos = (userIndex * list.length) / 3;
					}

				}

				var style = 'display:inline-block;';

				var roleTxt = '';
				if (!_.isEmpty(options.title)) {
					roleTxt = options.title;
				}

				var nameTxt = '';
				if (options.full) {
					nameTxt = '<div class="ui sub header">'+ user.fname +' '+ user.lname +'</div>';
				} else {
					nameTxt = '';
					style = 'float:left; background-color:white; border-radius:50%; margin-right:-10px;';
				}

				if (user.object_bp_type == 'contacts') {
					
					var contactTitle = user.fname +' '+ user.lname +' <i class="external link icon"></i>';
					if (user.object_bp_type === 'contacts' && !_.isEmpty(user.company) && typeof user.company === 'object' && typeof user.company.name === 'string') {
						contactTitle = '<strong>'+ user.fname +' '+ user.lname +'</strong> <i>at '+ user.company.name +'</i> <i class="external link icon"></i>';
					}

					ui.makeNode(
						'u-' + user.id
						, 'div'
						, {
							css: 		'ui label'
							, style:	'display:initial;'
							, text: 	contactTitle
							, tag: 		'a'
							, href:		sb.data.url.createPageURL('object', {
								id: 					user.id
								, name: 				user.fname+' '+user.lname
								, object_bp_type: 		'contacts'
								, type: 				'contacts'
							})

						});
					
				} else {

					var fieldLabel = '';
					if (
						(options.mini || options.inCollection)
						&& typeof obj.object_bp_type === 'string'
					) {

						var bp = _.findWhere(appConfig.Types, {bp_name: obj.object_bp_type.substr(1)});
						if (bp && bp.blueprint && bp.blueprint[fieldName]) {
							fieldLabel = bp.blueprint[fieldName].name +': ';
						}

					}
					
					ui.makeNode(
						'u-' + user.id
						, 'div'
						, {
							css: 'item'
							, style: style
							, tooltip: {
								text: fieldLabel + user.fname + ' ' + user.lname
								, position: 'top center'
							}
							, listener:{
								type:'popup',
								hoverable:true
							}
							, text: sb.dom.getUserAvatar(user) + 
								'<span class="content" style="'+ style +'">'+
									nameTxt +
									roleTxt +
								'</span>'

						});
					
				}

				ui.patch();

			});
			
		}

		function buildUsers(ui, obj, fieldName, options, isTemplate) {

			if (
				!_.isEmpty(obj[fieldName])
				|| ( isTemplate && !_.isEmpty(obj[fieldName +'_merge']))
			) {

				var css = options.css || '';
				var edgeCss = '';

				if (options.full && options.entityPage) {
					edgeCss = 'edge-field ';
				}
				
				ui.makeNode('users', 'div', {
					css: edgeCss +'revealParent '+ edgeCss
					, style: disabledCursor + ' ' + options.style
				}).makeNode('users', 'div', {
					css: 'ui horizontal '+ css +' list'
				});

				if (Array.isArray(obj[fieldName]) && fieldName != 'shared_with') {
					_.each(obj[fieldName], function(user, i) {

						// user variable can be a company
						if (user) {

							if (user.object_bp_type === 'companies') {

								ui.users.makeNode(
									'u-' + user.id
									, 'div'
									, {
										css: 'ui label'
										, style:'display:initial;'
										, text: user.name +' <i class="external link icon"></i>'
										, tag: 'a'
										, href:sb.data.url.createPageURL('object', {
											id: 					user.id
											, name: 				user.name
											, object_bp_type: 		'companies'
											, type: 				'companies'
										})
				
									});
							
							} else {

								buildUser(ui.users, user, i, obj[fieldName]); // For multiple users

							}

						}

					});

					if (isTemplate && !_.isEmpty(obj[fieldName +'_merge'])) {

						getMergeTags(fieldName, ui, obj, options, function (ret) {
							_.each(obj[fieldName +'_merge'], function(user, i){
								buildUser(ui.users, user, i, obj[fieldName], ret.bpName); // For multiple users		
							});

						});
						
					}

				} else {

					if (isTemplate && !_.isEmpty(obj[fieldName +'_merge'])) {
						
						getMergeTags(fieldName, ui, obj, options, function (ret) {
							buildUser(ui.users, obj[fieldName +'_merge'], 0, undefined, ret.bpName); // For single user
						});
						
					} else {

						if (obj[fieldName].object_bp_type === 'companies') {

							ui.users.makeNode(
								'u-' + obj[fieldName].id
								, 'div'
								, {
									css: 'ui label'
									, style:'display:initial;'
									, text: obj[fieldName].name +' <i class="external link icon"></i>'
									, tag: 'a'
									, href:sb.data.url.createPageURL('object', {
										id: 					obj[fieldName].id
										, name: 				obj[fieldName].name
										, object_bp_type: 		'companies'
										, type: 				'companies'
									})
			
								});

						} else {

                        
                            if ( _.isArray(obj[fieldName]) ) {
                                ui.patch();
                                _.each( obj[fieldName], function(contact, index){

                                    contact.object_bp_type = 'users';

                                    buildUser(this, contact, index, undefined); // For single user
                                }, ui.users);
                            } else {
                                buildUser(ui.users, obj[fieldName], 0, undefined); // For single user
                            }

						}

					}

				}

			} else {

				var placeholder = options.hasOwnProperty('placeholder') ? options.placeholder : 'Empty'

				ui.makeNode('users', 'div', {
					text: 		'<div class="field-placeholder">' + placeholder + '</div>'
					, css: 		'edge-field'
					, style: 	'cursor:pointer;' + options.style
				});

			}

			// edit hover
			if (options.full && options.entityPage) {

				ui.users.makeNode(
					'e'
					, 'div'
					, {
						css: 'ui circular mini basic icon button revealChild'
						, text: '<i class="ui pencil icon"></i>'
						, style: 'box-shadow:none;background-color:white !important;'
					}
				);

			}
			
			if (!options.inCollection) {
				
				ui.users.listeners.push(
					function (selector) {
		
						$(selector).on('click', function(){
		
							if (options.edit) {
		
								options.onContacts = onContacts;
								Edit(fieldName, ui, obj, options);
		
							}
		
						});
		
					}
				);
				
			}

		}
		
		// Carry over data between single/list versions
		if (multiSelect && !Array.isArray(obj[fieldName])) {
			
			obj[fieldName] = _.compact([obj[fieldName]]);
			
		} else if (!multiSelect && Array.isArray(obj[fieldName])) {
			
			obj[fieldName] = obj[fieldName][0];
			
		}
		
		if (!options.edit) {
			disabledCursor = 'cursor: not-allowed !important;';
		}

		if (options.hasOwnProperty('blueprint')) {

			if(
				options.blueprint[fieldName].fieldType === 'contacts'
				|| options.blueprint[fieldName].fieldType === 'companies'
			) {

				onContacts = true;
				fieldPlaceHolder = 'Empty';

			}

		}

		if (!options.hasOwnProperty('commitUpdates')) {
			options.commitUpdates = true;
		}

		if (_.isNumber(obj[fieldName]) && !_.isNull(obj[fieldName])) {

			sb.data.db.obj.getById('users', obj[fieldName], function(user) {

				obj[fieldName] = user;
				buildUsers(ui, obj, fieldName, options, isTemplate);

			});

		} else {

			buildUsers(ui, obj, fieldName, options, isTemplate);

		}
		
	}

	function Edit (fieldName, ui, obj, options) {

		var multiSelect = false;
		var searchNodeCSS = 'ui search selection transparent fluid dropdown edge-field';
		var userOptions = [];
		var objType = 'users';
		var canCreate = true;
		var where = {};
		var isTemplate = options.isTemplate === true || options.is_template === true || obj.is_template === 1 ? true : false;
		var tmpTag = {};

		function buildSearchInput() {
			
			var inputId = ui.selector.replace('.', '') +'-input';
			var searchSetup = {
					css: searchNodeCSS,
					style: 'border:none;',
					text:
						'<input type="hidden" name="user">'+
						'<i class="dropdown icon"></i>'+
						'<input id="'+ inputId +'" placeholder="Type to search.." type="text" class="transparent search search-input" tabindex="0" style="padding:7px 15px !important; height:100%;">'+
						'<div class="default text">Find a user</div>'+
						'<div class="menu transition hidden" tabindex="-1"></div>',
					listener:{
						type: 'dropdown'
						, values: userOptions
						, saveRemoteData: false
						, minCharacters: 0
						, forceSelection: false
						, apiSettings: {
							cache: false
							, url: databaseConnection.obj.getSearchPath(
								objType
								, ''
								, where
								, {
									parentObjectType: obj.object_bp_type.replace('#', '%23')
								}
							)
							, onResponse: function (raw, two) {

								var results = [];
								_.each(raw.results, function (user) {

									var selection = $(searchNode.selector).dropdown('get value');
									selection = selection.split(',');

									if (
										objType === 'users'
										, objType === 'contacts'
									) {

										if (!_.isEmpty(user.fname)) {

											if (
												user.id !== parseInt(sb.data.cookie.userId)
												&& !_.contains(selection, user.id.toString())
											) {
		
												results.push({
													value: parseInt(user.id)
													, name: getUserOptionText (user)
												});
		
											}
		
										}

									} else {

										results.push({
											value: parseInt(user.id)
											, name: user.name
										});

									}

								});

								// Add clear option.
								if (!_.isEmpty(obj[fieldName])) {

									results.unshift({
										value: 	'clear'
										, name: '<i class="remove icon"></i> <strong>Unassign</strong> '
									});

								}

								// Add merge tag options if in templating mode.
								if (isTemplate) {

									var searchVal = $('#'+ inputId).val().toUpperCase();

									_.chain(AvailableMergeTags)
										.filter(function (tag) {

											return (
												!_.isEmpty(tag)
												&& typeof tag.name === 'string'
												&& tag.name.toUpperCase().includes(searchVal)
											);

										})
										.each(function (tag) {
											results.push({
												value: tag.value,
												name: '<div class="ui label bento-merge-tag">'+ tag.display +'</div>'
											});
										});

								}

								if (options.onContacts === false) {

									var lengthCheck = false;
									if (options.jobType) {
										lengthCheck = _.intersection(where.tagged_with, appConfig.user.tagged_with).length === options.jobType.length
									}

									if (
										_.isEmpty(where)
										|| _.isEmpty(where.tagged_with)
										|| lengthCheck
									) {

										if (appConfig.instance == obj.instance) {
											results.unshift({
												value: parseInt(parseInt(sb.data.cookie.userId))
												, name: '<i class="user icon"></i> <strong>Assign to myself</strong> '
											});
										}

									}

								}

								OptionsCache = results;

								if(options.onContacts === true
									&& !multiSelect) {

									if (canCreate) {

										if (objType !== 'companies') {

											results.push({
												value: 		'new'
												, name: 	'<i class="green plus icon"></i> New or find from company'
											});

										}

									}

								}

								return {
									results: results
								};

							}
							, mockResponseAsync: function(settings, callback){

								function GetCookieValue(name) {
									var found = document.cookie.split(';').filter(c => c.trim().split("=")[0] === name);
									return found.length > 0 ? found[0].split("=")[1] : null;
								}
								
								var headers = {
									'bento-token': GetCookieValue('token')
								};
								if (appConfig.is_portal) {
									
									headers.portal = appConfig.state.portal;
									headers['bento-token'] = GetCookieValue('p_token');
									
								}
								
								$.ajax({
									type: 'post',
									url: settings.url,
									xhrFields: {
										withCredentials: true
									},
									crossDomain: true,
									data: {},
									success: function (response) {

										callback(response);

									},
									error: function (jqXHR, status, error) {



									},
									headers: headers
								});

							}
						},
						onShow() {

							setTimeout(function() {
								$('.floater-container').css({'overflow':'visible'});
							}, 100);
								
						},
						onHide: function () {

							setTimeout(function() {
								$('.floater-container').css({'overflow-y':'scroll', 'overflow-x':'auto'});
							}, 100);

							if (typeof options.onEditEnd === 'function')
								options.onEditEnd();

							var selection = $(searchNode.selector).dropdown('get value');
							var userInts = [];

							if(selection === 'new') {
								
								sb.notify({
									type: 	'get-sys-modal'
									, data: 	{
										callback: function (modal) {
											
											modal.body.empty();
											modal.show();
											
											// If in a portal, allow creation of contacts
											// only in companies tied to the current user's
											// company in the provider instance.
											if (
												objType === 'contacts'
												&& appConfig.is_portal
											) {
												
												sb.notify({
													type: 'create-new-contact'
													, data: {
														ui: 		modal.body
														, seed: 	{}
														, company: 	where.company
														, onSave: 	function (newItem) {
															
															if(multiSelect) {
		
																obj[fieldName].push(newItem.id);
		
															} else {
		
																obj[fieldName] = newItem.id;
		
															}
		
															sb.data.db.obj.update(
																obj.object_bp_type
																, obj
																, function (updates) {
		
																	obj[fieldName] = updates[fieldName];
		
																	ui.empty();
																	View(fieldName, ui, obj, options);
																	ui.patch();
		
																	sb.notify({
																		type: 'field-updated'
																		, data: {
																			obj: 			updates
																			, type: 		objType
																			, property: 	fieldName
																		}
																	});
		
																	$('.ui.modal').modal('hide');
		
																}
																, 1
															);
															
														}
													}
												});
												
											} else {
												
												sb.notify({
													type: 'crm-init-create-flow'
													, data: {
														domObj: modal.body
														, obj:  obj
														, options: {
															onSave: function(newItem) {
			
																if(multiSelect) {
			
																	obj[fieldName].push(newItem.id);
			
																} else {
			
																	obj[fieldName] = newItem.id;
			
																}
			
																sb.data.db.obj.update(
																	obj.object_bp_type
																	, obj
																	, function (updates) {
			
																		obj[fieldName] = updates[fieldName];
			
																		ui.empty();
																		View(fieldName, ui, obj, options);
																		ui.patch();
			
																		sb.notify({
																			type: 'field-updated'
																			, data: {
																				obj: 			updates
																				, type: 		objType
																				, property: 	fieldName
																			}
																		});
			
																		$('.ui.modal').modal('hide');
			
																	}
																	, 1
																);
			
															}
														}
													}
												});
												
											}
											
										}
										, onClose: function () {
											
											ui.empty();
											View(fieldName, ui, obj, options);
											ui.patch();
											
										}
									}
								});
								return;

							}

							var tags = '';
							if(multiSelect){

								tags = [];
								selection = selection.split(',');

								_.each(selection, function(userId){

									if (parseInt(userId)) {
										userInts.push(
											parseInt(userId)
										);
									} else if (typeof userId === 'string') {
										tags.push(userId);
									}

								});

								if (_.contains(selection, 'clear')) {
									userInts = [];
								}

							}else{

								if (selection === 'clear') {
									userInts = 0;
								} else if (parseInt(selection)) {
									userInts = parseInt(selection);
								} else if (typeof selection === 'string') {
									tags = selection;
								}

							}

							searchNode.loading();

							var updates = {
									id: 			obj.id
									, [fieldName]: 	userInts
								};

							if (options.hasOwnProperty('parseUpdates')) {
								updates = options.parseUpdates(updates);
							}

							if (isTemplate) {

								updates[fieldName +'_merge'] = tags;
								if (!multiSelect) {
									updates[fieldName] = 0;
								} 

							}


							if (options.commitUpdates) {

								sb.data.db.obj.update(
									obj.object_bp_type
									, updates
									, function (updates) {

										obj[fieldName] = updates[fieldName];
										if (isTemplate) {

											obj[fieldName +'_merge'] = tags;
											updates[fieldName +'_merge'] = tags;
				
										}

										ui.empty();
										View(fieldName, ui, obj, options);
										ui.patch();

										sb.notify({
											type: 'field-updated'
											, data: {
												obj: 			updates
												, type: 		objType
												, property: 	fieldName
											}
										});

										if (options.shouldTag) {

											var ids = [];
											if (options.multi) {
												ids = _.pluck(obj[fieldName], 'id');
											} else {
												
												if(obj[fieldName]){
													ids = [obj[fieldName].id];
												}
												
											}

											sb.data.db.obj.runSteps(
												{
													applyTags: {
														tags: 		ids
														, tagType: 	'tagged_with'
														, notifyEmail: options.notifyEmail
														, notifyApp: options.notifyApp
														, fieldName: fieldName
													}
												}
												, obj.id
												, function (response) {

													sb.notify({
														type: 'field-updated'
														, data: {
															obj: {
																id: response.id
																, tagged_with: response.tagged_with
															}
															, property: 'tagged_with'
														}
													});

												}
											);
										}

									}
									, {
										[fieldName]: {
											id: 		true
											, fname: 	true
											, lname: 	true
											, profile_image:true
											, color: 	true
											, name: 	true
										}
									}
								) ;

							} else {
								sb.data.db.obj.getById(
									'users'
									, userInts
									, function (upd) {

										obj[fieldName] = upd;
										if (isTemplate) {
											obj[fieldName +'_merge'] = updates[fieldName +'_merge'];
										}

										ui.empty();
										View(fieldName, ui, obj, options);
										ui.patch();

									}
									, {
										[fieldName]: {
											id: 		true
											, fname: 	true
											, lname: 	true
											, profile_image:true
											, color: 	true
											, name: 	true
										}
									}
								);

							}

						}
					}
				};

			var searchNode = ui.makeNode('add', 'div', searchSetup);

			searchNode.listeners.push(function (selector) {

				$(selector).find('.search-input').focus();

			});

		}

		ui.empty();

		if (options.onContacts === true) {
			objType = options.blueprint[fieldName].fieldType;
		}

		if (options.hasOwnProperty('canCreate')) {
			canCreate = options.canCreate;
		}

		// Show current selection
		if (!_.isEmpty(obj[fieldName])) {

			userOptions.push({
				value:obj[fieldName].id
				, name: obj[fieldName].name
				, selected:true
			});
			
		}

		function getUserOptionText (user) {

			var profileImg = '<i class="user icon"></i>';

			if (user.profile_image && !_.isEmpty(user.profile_image.loc) && user.profile_image.loc.length > 3) {
				profileImg = '<img class="ui avatar image" src="'+ sb.data.files.getURL(user.profile_image) +'">';
			}

			if (user.object_bp_type === 'contacts' && !_.isEmpty(user.company) && typeof user.company === 'object' && typeof user.company.name === 'string') {
				return profileImg +' '+ user.fname +' '+ user.lname +' <i><strong> at '+ user.company.name +'</strong></i>';
			}

			return profileImg +' '+ user.fname +' '+ user.lname;

		}

		if (options.multi) {		
			multiSelect = options.multi;
		}

		// read in options from blueprint

		if (
			options.blueprint
			&& options.blueprint[fieldName]
			&& options.blueprint[fieldName].options
		) {

			if (options.blueprint[fieldName].options.multi) {
				multiSelect = true;
			}

		}

		if (typeof options.onEditStart === 'function')
			options.onEditStart();

		if (multiSelect) {

			searchNodeCSS = 'ui search multiple selection transparent fluid dropdown edge-field';
			var userOptions = _.map(obj[fieldName], function (user) {

				// user can be a company object

				if (user.object_bp_type === 'companies') {

					return {
						value: 		user.id
						, name: 	user.name
						, selected: true
					};

				} else {

					return {
						value: 		user.id
						, name: 	getUserOptionText(user)
						, selected: true
					};

				}

			});

		}
		
		if (
			options
			&& !_.isEmpty(options.jobType)
		) {

			where = {
				tagged_with: []
			};

			_.each(options.jobType, function (j) {
				where.tagged_with.push(parseInt(j));
			});
			
		}
		
		// If in a portal, limit search to portal user's client obj in the 
		// provider instance.
		if (
			objType === 'contacts'
			&& appConfig.is_portal
		) {
			
			if (appConfig.state) {
				where.company = appConfig.portal_company;
			}
			
		}

		// Only show related contacts
		// TODO: Move this option to settings.
		if (appConfig.instance === 'foundation_group') {
			options.onlyRelated = true;
		}
		if (options.onlyRelated) {

			var parentId = '';

			if (obj.parent) {
				if (_.isObject(obj.parent)) {
					parentId = obj.parent.id;
				} else {
					parentId = obj.parent;
				}
			}

			if (_.isObject(where.tagged_with)) {
				where.tagged_with.push(parentId);
			} else if (Number.isInteger(where.tagged_with)) {
				where.tagged_with = [parentId];
			}

			where.tagged_with = {
				type: 'any',
				values: where.tagged_with,
				or: {
					company: parentId
				}
			}

		}

		if (isTemplate) {

			// Get merge tags to provide for selection
			getMergeTags(fieldName, ui, obj, options, function (ret) {

				function buildOptions() {

					if (multiSelect) {

						_.each(obj[fieldName +'_merge'], function (tag) {
		
							tmpTag = _.findWhere(AvailableMergeTags, {value: tag});

							if (tmpTag) {
								userOptions.push({
									value:		tag
									, name: 	tmpTag.display
									, selected:	true
								});
							}
		
						});

					} else {

						if (!_.isEmpty(obj[fieldName +'_merge'])) {
							
							tmpTag = _.findWhere(AvailableMergeTags, {value: obj[fieldName +'_merge']});
				
							if (tmpTag) {
								userOptions.push({
									value:		obj[fieldName +'_merge']
									, name: 	tmpTag.display
									, selected:	true
								});
							}
							
						}

					}
	
				}

				buildOptions();
				buildSearchInput();
				ui.patch();

			});

		} else {

			buildSearchInput();
			ui.patch();

		}

	}

	return {

		init: function () {

			var options = {
				multi: {
					name: 'Allow multiple selections?',
					type: 'bool'
				},
				shouldTag: {
					name: 'Automatically tag this obj with this contact?',
					type: 'bool'
				}
				// onlyRelated: {
				// 	name: 'Only show related contacts?',
				// 	type: 'bool'
				// }
			}

			var optionsUsers = {
				...options,
				notifyEmail: {
					name: 'Allow notify in Email? (Should tags must be selected)',
					type: 'bool',
				},
				notifyApp: {
					name: 'Allow notify in App? (Should tags must be selected)',
					type: 'bool',
				}
			};

			// TODO: Remove this feature block and actually do this at the field config in the editor
			if (appConfig.instance === 'foundation_group') {
				// delete options.onlyRelated;
			}

			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 'users'
					, view: View
					, title: 'Teammate(s)'
					, icon: 'users'
					, type: 'Collaborate'
					, propertyType: 'objectIds'
					, objectType: 'users'
					, select: {
						fname: 		true
						, lname: 	true
						, profile_image: true
						, color: 	true
					},
					options: optionsUsers
				}
			});

			// Contacts
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'contacts',
					view: View,
					title: 'Contact(s)',
					availableToEntities: true,
					icon: 'address book',
					type: 'Collaborate',
					propertyType: 'objectId',
					objectType: 'contacts',
					select: {
						fname: 		true,
						lname: 		true,
						company: {
							name: true
						}
					},
					options: options,
					parseOptions: function (selection, field) {

						if (selection.multi) {
							field.type = 'objectIds';
						}

						return field;

					}
				}
			});

			// Companies
			sb.notify({
				type: 'register-field-type',
				data: {
					name: 'companies',
					view: View,
					title: 'Company',
					availableToEntities: true,
					icon: 'building',
					type: 'Collaborate',
					propertyType: 'objectId',
					objectType: 'companies',
					select: {
						name: true
					},
					options: options,
					parseOptions: function (selection, field) {

						if (selection.multi) {
							field.type = 'objectIds';
						}

						return field;

					}
				}
			});

			var options = {
				multi: {
					name: 'Allow multiple selections?',
					type: 'bool'
				},
				jobType: {
					name: 'Assignable to: <i>(if empty, assignable to anyone)</i>',
					type: 'tags',
					tagType: 'job'
				},
				shouldTag: {
					name: 'Automatically tag this obj with this user?',
					type: 'bool'
				},
				notifyEmail: {
					name: 'Allow notify in Email? (Should tags must be selected)',
					type: 'bool',
				},
				notifyApp: {
					name: 'Allow notify in App? (Should tags must be selected)',
					type: 'bool',
				}
				// onlyRelated: {
				// 	name: 'Only show related teammates?',
				// 	type: 'bool'
				// }
			}

			// TODO: Remove this feature block and actually do this at the field config in the editor
			if (appConfig.instance === 'foundation_group') {
				// delete options.onlyRelated;
			}

			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 		'user'
					, view: 	View
					, title: 	'Teammate(s)'
					, availableToEntities: true
					, detail: function (fieldName, obj, options) {

						// Only show for empty fields, since the popovers are on the 
						// individual user nodes within the field if they are set.
						if (!_.isEmpty(obj[fieldName])) {
							return false;
						}

						// Otherwise, show the name of the field (in this case, the role)
						var fieldLabel = '';
						if (
							options
							&& (options.mini || options.inCollection)
							&& typeof obj.object_bp_type === 'string'
						) {

							var bp = _.findWhere(appConfig.Types, {bp_name: obj.object_bp_type.substr(1)});
							if (bp && bp.blueprint && bp.blueprint[fieldName]) {
								fieldLabel = bp.blueprint[fieldName].name;
							}
							return fieldLabel;

						}
						
						return false;
						
					}
					, icon: 	'user'
					, getIcon: 	function (options) {

                        if (options && options.multi) {
							return 'users';
						} else {
							return 'user';
						}

					}
					, type: 	'Collaborate'
					, propertyType: 'objectId'
					, objectType: 	'users'
					, select: {
						fname: 		true
						, lname: 	true
						, profile_image: true
						, color: 	true
					}
					, options: options
					, parseOptions: function (selection, field) {

						if (selection.multi) {
							field.type = 'objectIds';
						}

						return field;

					}
				}
			});

			if (IN_NODE_ENV) {
				var user = View;
				var users = View;
				var contacts = View;
				var companies = View;
				module.exports = { 
					user, users, contacts, companies
				}
			}

		}

	};

});

if (IN_NODE_ENV) {
	Factory.startAll();
}
