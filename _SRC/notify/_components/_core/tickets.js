Factory.register('tickets', function(sb) { 

	function getTickets(queryObj, callback) {

		var where = {};

		_.each(queryObj, function(value, key) {
			where[key] = value;
		});

		sb.data.db.controller('getTickets', where, function(data) {
			callback(data);
		});
						 
	}

	function getTicket(id, callback) {

		sb.data.db.controller('getTicket', {
			id: id
		}, function(data) {
			callback(data);
		});
						 
	}

	function getTicketBlueprint(callback) {

		sb.data.db.controller('getTicketBlueprint', {}, function(data) {
			callback(data);
		});
						 
	}

	function getTicketComments(queryObj, callback) {

		var where = {};

		_.each(queryObj, function(value, key) {
			where[key] = value;
		});

		where['type_id'] = queryObj.type_id.value;

		sb.data.db.controller('getTicketComments', where, function(data) {
			callback(data);
		});
						 
	}

	function updateTicketPriority(steps, objId, callback) {

		sb.data.db.controller('updateTicketPriority', {
			steps: steps,
			objId: objId
		}, function(data) {
			callback(data);
		});
						 
	}

	function navigateToTicket(ticket) {

		return sb.data.url.createPageURL(
			'custom', 
			{
				id: 'ticket',
				params: {
					id: ticket.id,
					name: ticket.name
				}
			}
		);

	}

	function getTicketView(ui, ticket, options, draw, onComplete) {

		getTicketBlueprint(function(blueprint) {

			var padding = 'padding:14px;';
			if (
				!options.inCollections
				&& !sb.dom.isMobile
			) {
				padding = 'padding-top:28px;padding-left:54px;padding-right:54px;';
			}

			var page = {
				segment: {
					type: 'div',
					css: 'ui stackable grid container',
					style: padding,
					content: {
						css: 'ui row',
						left: {
							type: 'column',
							w: 16,
							css: '',
							content: {
								name: {
									type: 'title',
									fieldName: 'name',
									edit: false,
									tag: 'h1',
									uid: true,
									link: navigateToTicket
								},
								_1: {
									type: 'detail',
									fieldName: '_1',
									edit: false
								},
								_4: {
									type: 'state',
									label: 'Status',
									fieldName: '_4',
									edit: false,
									blueprint: blueprint
								},
								_24: {
									type: 'priority',
									fieldName: '_24',
									label: 'Priority',
									edit: true,
									requireChangeNote: true,
									commitUpdates: true,
									runSteps: updateTicketPriority
								},					
								date_created: {
									type: 'date',
									fieldName: 'date_created',
									edit: false,
									label: 'Created'
								},
								last_updated: {
									type: 'date',
									fieldName: 'last_updated',
									edit: false,
									label: 'Updated'
								},
								comments: {
									type: 'view',
									fieldName: 'comments',
									view: function (ui, obj, opts) {
		
										sb.notify({
											type: 'show-note-list-box',
											data: {
												domObj: ui,
												obj: obj,
												objectIds: [obj.id],
												objectId: obj.id,
												collapse: 'open',
												actions: {},
												categories: false,
												filters: true,
												public: true,
												mentions: false,
												query: getTicketComments
											}
										});
		
									}
								}
							}
						}
					}
				}
			};
			
			sb.notify({
				type: 'view-page',
				data: {
					ui: ui,
					onDraw: onComplete,
					page: page,
					onDraw: draw,
					state: {
						pageObject: ticket
					},
					options: 	{
						compact: true
					}
				}
			});		
			
		});

	}
	
	function allTickets(ui, state, draw) {
		
		function build_collection(ui, options) {

			getTicketBlueprint(function(blueprint) {

				var collectionSetup = {
					actions: {},
					entityType: {
						blueprint: blueprint		
					},
					fields: {
						name: {
							title: 'Name',
							type: 'title',
							uid: true,
							isSearchable: true,
							link: navigateToTicket
						},
						_4: {
							title: 'Status',
							type: 'state',
							edit: false
						},
						_24: {
							title: 'Priority',
							type: 'priority'
						},
						_1: {
							title: 'Details',
							type: 'detail'
						}
					},
					singleView: {
						useCache: true,
						link: navigateToTicket,
						view: function(ui, ticket, onComplete) { 
	
							var options = {
								inCollections: true
							};
	
							getTicketView(ui, ticket, options, draw, onComplete);
	
						}
					},
					fullView: {
						id: 'ticket-obj'
					},
					query: getTickets,
					menu: false,
					submenu: true,
					filters: false,
					search: true,
					tags: false,
					editBlueprint: false,
					onBoxview: true,
					page:0,
					pageLength:50,
					paged:true,
					count:true,
					offset: 0,
					sortCol: '_24',
					sortDir: 'desc',
					sortCast: 'string',
					groupBy: '_4',
					isGrouped: true,
					groupings: {
						_4: 'Status'
					},
					selectedView: 'list',
					subviews: {
						list: {
							groupBy: {
								defaultTo: '_4'
							},
							backlog: true
						}
					}
				};
	
				sb.notify({
					type: 'entity-collection-view',
					data: {
						dom: ui,
						objectType: '#Help_ticket',
						draw: function(){},
						options: collectionSetup
					}
				});	

			});	
			
		}

		ui.makeNode('header', 'div', {
			css: 'ui header huge',
			text: 'Tickets'
		});
		
		ui.makeNode('collection' , 'div', {
			css: 'sixteen wide column'
		});
		
		ui.patch();

		build_collection(ui.collection, {});
				
	}

	function singleTicket(ui, state, draw, onComplete) {

		getTicket(state.params.id, function(ticket) {

			var options = {};

			getTicketView(ui, ticket, options, draw, onComplete);

		});

	}
					
	return {
		
		init: function() {
			
			sb.listen({
				'tickets-run':	this.run
			});			
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'tickets',
						title: 'Tickets',
						icon: '<i class="ui circle question icon"></i>',
						views: [
							{
								id:	'tickets',
								type: 'custom',
								title: 'Tickets',
								icon: '<i class="circle question icon"></i>',
								default: true,
								dom: allTickets
							},
							{
								id:	'ticket',
								type:'custom',
								title: 'Ticket',
								icon: '<i class="circle question icon"></i>',
								dom: singleTicket
							}
						]
					}
				}
			});
			
		}
		
		, run: function(data) { 
			data.run(data);
		}
		
	}
	
});