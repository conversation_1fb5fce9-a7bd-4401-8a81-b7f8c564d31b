Factory.register('staff', function(sb){
	
 	var ui = {};
	var tableUI = {};
	var modals = {};
	var tileDom = {};
	var popUI = {};

	var reqDocumentsTab = {};


	var components = {};
	var updStaffList = [];
	var selectedStaffObj = {};
	var setupData = {};
	var settings = {
		singleView:false
	};

	var fields = [];

	function return_singleUserHeader(state, options) {
	
		var singleUserHeader = {
			viewBody: {
				type:	'div'
				, css: 	'ui stackable grid'
				, content: {
					css:	'ui row'
					, sp1: {
						type: 	'column'
						, w:		16
						, style:	isMobile() ? 'height: 0px; padding:0px !important;' : 'height:10px;'
					}
					, left: {
						type: 	'column'
						, w:		4
						, css: 	'text-center'
						, content: {
							avatar: {
								type:	'view'
								, view: 	profileImage
							}
							, tagColor: {
								type:	'view'
								, view: 	profileColor
							}

						}
					}
					, middle: {
						type: 	'column'
						, w: 	5
						, css: 	isMobile() ? 'text-center' : ''
						, style:  isMobile() ? 'padding:0px !important;' : ''
						, content: {
							gap: {
								type: 	'column'
								, w:		16
								, style:	isMobile() ? 'height: 0px; padding:0px !important;' : 'height:66px;'
							}
							, name: {
								type: 'title'
								, fieldName: 'name'
								, edit: true
								, tag: 'h1'
								, update: function (oldVal, fieldName, newVal, cb) {
									
									var parsedVal = (newVal.trim()).split(' ');

									if (parsedVal.length < 2) {
										sb.dom.alerts.alert('Please enter a First and Last Name.', '', 'warning');
										return;
									}

									var updlname = parsedVal.pop();
									var updfname = parsedVal.join(' ');

									sb.data.db.obj.update('users'
										, {
											id: state.id,
											fname: updfname,
											lname: updlname
										}
										, function(res){

											if (res) {
												cb(res);
											}
												
										}
									);

								}
							}
							, email: 	{
								type: 		'plain-text'
								, fieldName:	'email'
								, edit:		true
								, fields: 	{
									email:{
										icon:{
											type:'envelope outline'
											, color: 'gray'
										}
									}
								}
								, update:		profileEmailParse
							}
							, status: {
								type: 'view'
								, view: profileStatus
							}

						}
					}
					, right: {
						type: 	'column'
						, w: 	7
						, style: isMobile() ? 'padding:0px !important;' : ''
						, content: {
							css: isMobile() ? 'text-center field' : 'field'
							, style: isMobile() ? 'padding:0px !important;' : ''
							, gap: {
								type: 	'column'
								, w:		16
								, style:	isMobile() ? 'height: 0px; padding:0px !important;' : 'height:56px;'
							}
							, locations: {
								type:		'locations'
								, fieldName: 	'base'
								, edit:		true
								, multi:		true
								, label: 		'Location'
							}
							, service: {
								type:		'service'
								, fieldName: 	'service'
								, edit:		true
								, multi:		true
								, label: 		'Service'
								, onComplete: 	function (obj) {

									viewSingleStaff.req_docs(reqDocumentsTab, obj);

								}
							}
						}
					}
					, tags: {
						type: 	'column'
						, w:		16
						, style: isMobile() ? 'padding:0px !important;' : ''
						, content: {
							tags: {
								type:	'view'
								, view: 	profileTags
							}
						}
					}
					, bottom: {
						type: 	'column'
						, w: 	16
						, style: isMobile() ? 'padding:0px !important;' : ''
						, content: {
							css: 'field'
							, dash: {
								type:'view'
								, view:function(dom, user, state){

									var ui = dom;

									parseStaffForPaperwork({data:[user]}, function(res){

										var userObj = res.data[0];

										viewSingleStaff(userObj, ui, state);

									});
								}
							}
						}
					}
				}
			}
		};

		return singleUserHeader;

	}

	function trx(x){

		function exist(x){ return x != null }

		return (x !== false) && exist(x)

	}

	function checkChartOfAccountsSetup(callback){

		function createAccount(coaCompanyId, callback){

			sb.data.db.obj.create('chart_of_accounts', {
				chart_of_accounts_company:coaCompanyId,
				name:'Default chart of account',
				account_id:'1',
				note:'Default chart of account generated by the system.'
			}, function(account){

				callback(account);

			});

		}

		function getCompanies(callback){

			sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

				callback(coaComps);

			});

		}

		function getAccounts(coaCompanyId, callback){

			sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:coaCompanyId}, function(accounts){

				callback(accounts);

			});

		}

		getCompanies(function(coaComps){

			if(coaComps.length == 0){

				sb.data.db.obj.create('chart_of_accounts_companies', {
					name:'Default Chart of Account company'
				}, function(newCoaCompany){

					getAccounts(newCoaCompany.id, function(accounts){

						if(accounts.length == 0){

							// create first account
							createAccount(newCoaCompany.id, function(done){

								callback(true);

							});

						}else{

							callback(true);

						}

					});

				});

			}else{

				// check accounts
				getAccounts(coaComps[0].id, function(accounts){

					if(accounts.length == 0){

						// create first account
						createAccount(coaComps[0].id, function(done){

							callback(done);

						});

					}else{

						callback(accounts[0]);

					}

				});

			}

		});

	}

	function checkJobTypesSetup(callback){

		sb.data.db.obj.getAll('inventory_service', function(jobTypes){

			if(jobTypes.data.length == 0){

				checkChartOfAccountsSetup(function(coa){

					var newJobType = {
							can_be_billed:'no',
							chart_of_account:coa.id,
							description:'Default job type generated by the system.',
							name:'Team Member'
						};

					sb.data.db.obj.create('inventory_service', newJobType, function(jt){

						sb.data.db.obj.create('groups'
							, {
								name:		jt.name
								, description: jt.description
								, job_type:	jt.id
								, group_type:	'JobType'
								, managers:	appConfig.headquarters.managers
								, parent:		appConfig.headquarters.id
								, tools: 		appConfig.jobtypeTools
							}
							, function(jtGroupObj){

								sb.data.db.obj.update(
									'inventory_service'
									, {
										id:jt.id
										, group_object:jtGroupObj.id
									}
									, function(res){ return true; }
								);

								return true;

							}
						);


						callback([jt]);

					});

				});

			}else{

				callback(jobTypes.data);

			}

		}, 1, {page:0,pageLength:50,paged:true,sortCol:"name",sortDir:"asc",sortCast:"string",count:true});

	}

	function createFormSetup(obj, opt){

		var hasService = false;
		var serviceOptions = _.map(opt.service, function(v, k){

			var ret = {
				name: 		'service'
				, value: 		v.id
				, label: 		v.name
				, checked:	false
			};

			if(obj){
				_.each(obj.service, function(s){

					if(s.id === parseInt(v.id)){
						ret.checked = true;
						hasService = true;
					}

				});
			}

			return ret;

		}, []);

		var locationOptions = _.map(opt.base, function(v,k){

			var ret = {
				name: 	 'base'
				, value: 	 v.id
				, label: 	 v.name
				, checked: false
			};

			if(obj){

				if(obj.base){

					if(obj.base[0]){

						if( _.where(obj.base, {id: +v.id}).length > 0){
							ret.checked = true;
						}

					}

				}

			}

			return ret;

		}, []);

		var personalInfo = {
				fname:{
					type:'text',
					name:'fname',
					label:'First Name',
					placeholder: 'Required'
				},
				lname:{
					type:'text',
					name:'lname',
					label:'Last Name',
					placeholder: 'Required'
				},
				nickname:{
					type:'text',
					name:'nickname',
					label:'Nickname'
				},
				email:{
					type:'text',
					name:'primary_email',
					label:'Primary Email (login account)',
					placeholder: 'Required'
				},
				phone:{
					type:'text',
					name:'primary_phone',
					label:'Primary Phone #'
				},
				work_email:{
					type:'text',
					name:'work_email',
					label:'Secondary Email'
				},
				work_phone:{
					type:'text',
					name:'work_phone',
					label:'Secondary Phone #'
				}
			},
			details = {
				hire_date:{
					name:'hire_date',
					label:'Hire Date',
					type:'date',
					dateType: 'datetime',
					dateFormat:'MM/DD/YYYY'
				},
				base:{
					type:'checkbox',
					name:'base',
					label:'Location(s)',
					options: _.sortBy(locationOptions, 'label')
				},
				service: {
					type: 'checkbox',
					name: 'service',
					label: 'Service',
					options:_.sortBy(serviceOptions, 'label')
				}
			};

		if(obj){

			personalInfo.fname.value = obj.fname;
			personalInfo.lname.value = obj.lname;
			personalInfo.nickname.value = obj.nick_name;
			personalInfo.email.value = obj.email;
			personalInfo.phone.value = obj.phone;
			personalInfo.work_email.value = obj.work_email;
			personalInfo.work_phone.value = obj.work_phone;

			details.termination_date = {
				type:'date',
				dateType: 'date',
				name:'termination_date',
				label:'Termination Date',
				dateFormat:'MM/DD/YYYY',
				placeholder: 'MM/DD/YYYY'
			};
			details.termination_date.value = moment(obj.termination_date);

			details.hire_date = {
				type:'date',
				dateType: 'date',
				name:'hire_date',
				label:'Hire Date',
				dateFormat:'MM/DD/YYYY',
				placeholder: 'MM/DD/YYYY'
			};
			details.hire_date.value = moment(obj.hire_date);


		}else{

			if(hasService === false){
				details.service.options[0].selected = 'selected';
				details.service.options[0].checked = true;

			}

		}

		var ret = {
				personal:personalInfo,
				details:details
			};

		return ret;

	}

	function createFormSetupData(callback){

		sb.data.db.obj.getAll('staff_base', function(loc){

			checkJobTypesSetup(function(serv){

				callback({
						service:  serv
						, base: 	loc
					});

			});

		});
	}

	function createNewState(dom, state, draw, close){

		dom.empty();

		dom.makeNode('edit', 'div', {css: 'ui two column grid'});
		dom.edit.makeNode('sp', 'div', {css:'row'});
		dom.edit.makeNode('hrow', 'div', {css: 'row'});
		dom.edit.hrow.makeNode('lcol', 'div', {css: 'ten wide column'});
		dom.edit.hrow.lcol.makeNode('title', 'headerText', {text: `<i class="fa fa-user-circle"></i> Create New Staff Member`});
		dom.edit.hrow.makeNode('rcol', 'div', {css: 'six wide column'});
		dom.edit.hrow.rcol.makeNode('btns', 'buttonGroup', {css:'pull-right'}).makeNode('back', 'div', {css:'ui grey basic button', text:'<i class="fa fa-times"></i> Close'}).notify('click', {
			type:'companyComponent-run',
			data:{
				run:function(close){

					$('.ui.modal').modal('hide');

					//close();

				}.bind({}, close)
			}
		}, sb.moduleId);

		dom.edit.hrow.rcol.btns.makeNode('save', 'div', {css:'ui green button disabled', text:'<i class="fa fa-check"></i> Save'});
		dom.edit.makeNode('frow', 'div', {css: 'row'});
		dom.edit.frow.makeNode('lcol', 'div', {css: 'ten wide column'});
		dom.edit.frow.lcol.makeNode('infoh', 'div', {css: 'ui dividing header', text: 'Info'});
		dom.edit.frow.lcol.makeNode('sp', 'lineBreak', {spaces: 1});
		dom.edit.frow.makeNode('rcol', 'div', {css: 'six wide column'});
		dom.edit.frow.rcol.makeNode('deth', 'div', {css: 'ui dividing header', text: 'Details'});
		dom.edit.frow.rcol.makeNode('sp', 'lineBreak', {spaces: 1});

		dom.patch();

		createFormSetupData(function(options) {

			dom.edit.frow.lcol.makeNode('form', 'form', createFormSetup(false, options).personal);
			dom.edit.frow.rcol.makeNode('form', 'form', createFormSetup(false, options).details);

			dom.edit.hrow.rcol.btns.makeNode('save', 'div', {css:'ui green button', text:'<i class="fa fa-check"></i> Save'}).notify('click', {
				type:'staffComponent-run',
				data:{
					run:function(dom){

							function processForm(dom, callback){

								var lform = dom.edit.frow.lcol.form.process(),
								    rform = dom.edit.frow.rcol.form.process(),
								    user = {},
								    isValidated = true;

								if(!rform.fields.hasOwnProperty('service')){

									sb.dom.alerts.alert('Wait', 'Please assign your new staff member at least one service.', 'warning');
									isValidated = false;

								}

								if(!rform.fields.hasOwnProperty('base')){

									sb.dom.alerts.alert('Wait', 'Please assign your new staff member at least one location.', 'warning');
									isValidated = false;

								}

								if(lform.fields.primary_email.value == "" || lform.fields.fname.value == "" || lform.fields.lname.value == ""){

									sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'warning');

									isValidated = false;

								}

								if(isValidated){
									user = {
										fname:lform.fields.fname.value,
										lname:lform.fields.lname.value,
										nickname:lform.fields.nickname.value,
										email:lform.fields.primary_email.value.replace(/(^\s+|\s+$)/g,''),
										phone:lform.fields.primary_phone.value,
										work_email:lform.fields.work_email.value.replace(/(^\s+|\s+$)/g,''),
										work_phone:lform.fields.work_phone.value,
										hire_date:rform.fields.hire_date.value,
										enabled: 0,
										type:'staff',
										profiles:[],
										base:rform.fields.base.value,
										service:rform.fields.service.value
									};
								}

								return callback(isValidated, user);

							}

							dom.edit.hrow.rcol.btns.save.loading();

							processForm(dom, function(isVal, user){

							dom.edit.hrow.rcol.btns.save.loading(false);

							if(isVal && user){

								sb.notify({
									type:'save-new-staff-member',
									data:{
										dom:dom,
										user:user
									}
								});

							}

						});


					}.bind(null, dom)
				}
			}, sb.moduleId);

			dom.patch();

			draw(false);

		})

	}

	function getStaffWithPaperwork(paged, callback){

		paged.sortCol = 'fname';
		paged.sortDir = 'asc';

		sb.data.db.obj.getWhere('users', {
			type: 'staff',
			childObjs:{
				fname:true,
				lname:true,
				service:{
					name:true
				},
				base:{
					name:true
				},
				enabled:{
					name:true
				},
				email:true,
				phone:true,
				nick_name:true
			},
			paged:paged
		}, function(user){
			// empty array for building the staff list with the staff paperwork obejcts

			// get staff paperwork objects that have the staff ids of the staff members in the staff var
			sb.data.db.obj.getWhere('staff_paperwork', {
				staff:
					{
						type:'or',
						values:_.pluck(user.data, 'id')
					}
			}, function(docs){

				if(docs.length > 0){

					// if there are docs for the staff members, add them to the staff object in a new prop called staff_paperwork

					ret = _.map(user.data, function(u){

						u.staff_paperwork = _.where(docs, {staff:u.id});

						return u;

					});

					// ret contains a list of staff objects with the staff_paperwork prop

					var sortedRet = ret.sort(function (a, b) {

					    	return a.fname.toLowerCase().localeCompare(b.fname.toLowerCase());
					});

					user.data = sortedRet;

				}else{

					// if there aren't any docs for the staff members, create the prop as an empty array

					ret = _.map(user.data, function(u){

						u.staff_paperwork = [];

						return u;

					});

					user.data = ret;

				}

				updStaffList = user.data;

				callback(user);

			});

		});

	}

	function getSingleStaffPaperwork(queryParams, callback){

		sb.data.db.obj[queryParams.dbCall]('users', queryParams.queryObj, function(staff){

			var staffObject;

			if(queryParams.dbCall == 'getWhere'){
				staffObject = staff[0];
			} else {
				staffObject = staff;
			}

			sb.data.db.obj.getWhere('staff_paperwork', {staff: staffObject.id}, function(docs){

				staffObject.staff_paperwork = docs;

				callback(staffObject);
			});

		}, 2);

	}

	function docLabeler(staff){

		var docTotal = staff.staff_paperwork_status.length;
		var numberMissing = docTotal - staff.staff_paperwork.length;

		if(numberMissing < 0)
			numberMissing = 0;

		if(staff.staff_paperwork.length === 0 && docTotal == 0){

			this.css = 'ui label',
			this.text = 'No Documents Required';

		} else {

			this.css = 'ui label orange';
			this.text = ' Missing ' + numberMissing + ' (' + docTotal + ')';
		}

		if(docTotal > 0 && numberMissing == 0){
			this.css = 'ui label green';
			this.text = 'All Reqired Documents';
		}

		return this;

	}

	function checkDoc(docTypes, staffTypes){

		var ret = false;

			var listOfStaffJobs =
				 _.each(staffTypes, function(s, i){

					var staffId = s;

					if(typeof s === 'object') {
						 staffId = s.id;
					}

					if( s != null && s != undefined && (_.contains(docTypes, parseInt(staffId)) || _.contains(_.pluck(docTypes, 'id'), parseInt(staffId)))){

						ret = true;
					}

				 });
		return ret;

	}

	function docViewCard(docType, i, staff, jobTypes){

		var statusColor = 'yellow',
		    statusName = '<i class="fa fa-circle-o"></i> Missing',
		    docState = {
			    docType: docType,
			    index: i,
			    matchedDocument: false
		    },
		    docName = 'Untitled',
		    docServ = '';

		selectedStaffObj = staff;

		function truncName(str, max, add){

		   var add = add || '...';

		   return (typeof str === 'string' && str.length > max ? str.substring(0, max) + add : str);

		};

		function cardBtnsShow(state, onStatusChange){

			function downloadDocument(){

				sb.data.files.getWhere({id: state.docType.file_download.id}, function(document){

					if(document.length > 0){

						sb.data.files.open(document[0]);

					}else{
						sb.dom.alerts.alert('Missing File', 'Add file to download from Staff Settings menu', 'warning');
					}

				});

			}

			function uploadDocument(){

				var modals = this;

				var formFields = {
					name: {
						type: 'text',
						name: 'name',
						label: 'Name',
						value: state.docType.name
					},
					document_type: {
						name: 'document_type',
						value: state.docType.id,
						type: 'hidden'
					},
					staff: {
						name: 'staff',
						value: selectedStaffObj.id,
						type: 'hidden'
					}
				};

				if (state.docType.hasOwnProperty('need_file_upload') && state.docType.need_file_upload == 'Yes') {

					formFields.file_upload = {
						type: 'file-upload',
						label: 'Select a File',
						name: 'document_file'
					}

				}

				if (state.docType.hasOwnProperty('need_exp_date') && state.docType.need_exp_date === 'Yes') {

					formFields.exp_date = {
						type: 'date',
						label: 'Expiration Date',
						name: 'expiration_date'
					}

				}

				_.each(state.docType.fields, function(field){

					formFields['field-' + field.id] = {
						type: 'text',
						label: field.name,
						name: 'field-' + field.id,
						id: field.id
					}

				});

				modals.body.makeNode('header', 'headerText', {text: 'Upload Document', size: 'small', css: 'text-center'});

				var uploadForm = modals.body.makeNode('uploadForm', 'form', formFields);

				modals.footer.makeNode('btnGroup', 'buttonGroup', {css: 'ui mini buttons'});

				modals.footer.btnGroup.makeNode('upldadBtn', 'div', {css: 'ui green button', text: 'Upload'}).notify('click', {
					type: 'staffComponent-run',
					data: {
						run: function(){

							var formData = uploadForm.process();

							if(state.docType.need_file_upload == 'Yes' && formData.fields.document_file.value.fileData == undefined){

								sb.dom.alerts.alert('Warning', 'Please add a file for upload', 'warning');

								return;
							}

						     state.dbCall = 'create',

							processDocument.call(this);

						}.bind(this)
					}
				}, sb.moduleId);

				modals.body.patch();
				modals.footer.patch();

				modals.show();

			}

			function viewDocument(callback){

				var fileData = _.find(selectedStaffObj.staff_paperwork, function(file){

					return file.document_type === state.docType.id

				});

				if(fileData.hasOwnProperty('document_file')){

					sb.data.files.getWhere({id: fileData.document_file}, function(document){

						sb.data.files.open(document[0], function(){

							if(callback){
								callback(true);
							}

						});

					});

				} else {

					sb.dom.alerts.alert('No File Saved', 'Click the Edit button below to add a file', 'warning');

				}

			}

			function editDocument(){

				var  formFields = {
						name: {
							type: 'text',
							name: 'name',
							label: 'Name',
							value: state.matchedDocument.name
						}
					};

				if (state.docType.hasOwnProperty('need_file_upload') && state.docType.need_file_upload == 'Yes') {

					formFields.file_upload = {
						type: 'file-upload',
						label: 'Select a File',
						name: 'document_file'
					}

				}

				if (state.docType.hasOwnProperty('need_exp_date') && state.docType.need_exp_date === 'Yes') {

					formFields.exp_date = {
						type: 'date',
						label: 'Expiration Date',
						name: 'expiration_date',
						value: state.matchedDocument.expiration_date
					}

				}

				_.each(state.matchedDocument.fields, function(field){

					formFields['detail-' + field.id] = {
						type: 'text',
						label: field.label,
						name: 'detail-' + field.id,
						value: field.value

					}

				});

				this.body.makeNode('header', 'headerText', {text: state.matchedDocument.name, size: 'small', css: 'text-center'});

				var editForm = this.body.makeNode('uploadForm', 'form', formFields);

				this.footer.makeNode('btnGroup', 'buttonGroup', {css: 'ui mini buttons'});

				this.footer.btnGroup.makeNode('remove', 'div', {css:'ui red button', text:'<i class="fa fa-trash-o"></i> Remove Saved Document'}).notify('click', {
					type: 'staffComponent-run',
					data:{
						run: function(){

							state.dbCall = 'erase';

							removeDocument.call(modals);

						}.bind(this)
					}
				}, sb.moduleId);

				this.footer.btnGroup.makeNode('save', 'div', {css:'ui green button', text:'<i class="fa fa-check"></i> Save Updates'}).notify('click', {
					type: 'staffComponent-run',
					data: {
						run: function(){

							var formData = editForm.process();

							if(state.docType.need_file_upload == 'Yes' && formData.fields.document_file.value.fileData == undefined){

								sb.dom.alerts.alert('Warning', 'Please add a file for upload', 'warning');

								return;
							}

						    state.dbCall = 'update';

						    processDocument.call(this);

						}.bind(this)
					}
				}, sb.moduleId);

				this.body.patch();
				this.footer.patch();

				this.show();

			}

			function processDocument(){

				var formData = this.body.uploadForm.process();
				    staffPaperworkObj = {},
				    fieldsArr = [],
				    form = this.body.uploadForm;

				if(state.matchedDocument){
					staffPaperworkObj.id = state.matchedDocument.id
				}

				_.each(formData.fields, function(fieldData, fieldKey, l){

					if(fieldKey.includes('field')){

						var fieldInfo = _.pick(form, fieldKey);

						fieldsArr.push({
							name: fieldKey,
							value: fieldData.value,
							id: fieldInfo[fieldKey].args.id,
							label: fieldInfo[fieldKey].args.label
						});

					} else {

						staffPaperworkObj[fieldKey] = fieldData.value;

					}


				});

				staffPaperworkObj.fields = fieldsArr;

				sb.data.db.obj[state.dbCall]('staff_paperwork', staffPaperworkObj, function(docData){

					if(docData){

						if(state.dbCall == 'create'){

							selectedStaffObj.staff_paperwork.push(docData);

							this.hide();

							setTimeout(function(){

								onStatusChange(state);

							}, 250);

						} else {

							this.hide();

							var updStaffPaperwork = _.reject(selectedStaffObj.staff_paperwork, function(obj){
								return obj.id == docData.id
							});

							updStaffPaperwork.push(docData);

							selectedStaffObj.staff_paperwork = updStaffPaperwork;

							setTimeout(function(){

								onStatusChange(state);

							}, 250);

						}

					} else {

						sb.dom.alerts.alert('Error!', 'Please refresh and try again.', 'error');

					}

				}.bind(this));

			}

			function removeDocument(){

				sb.dom.alerts.ask({

					title: 'Remove this Uploaded Document?',
					text: ''

				}, function(response){

					if(response){

						this.hide();

						sb.data.db.obj.erase('staff_paperwork', state.matchedDocument.id, function(resp){

							if(resp){

								var updStaffPaperwork = _.reject(selectedStaffObj.staff_paperwork, function(obj){
									return obj.id == state.matchedDocument.id;
								});

								selectedStaffObj.staff_paperwork = updStaffPaperwork;

								setTimeout(function(){

									sb.dom.alerts.alert('Removed!', 'Staff member updated', 'success');

									onStatusChange(state);

								}, 500);

							} else {

								sb.dom.alerts.alert('Error', 'An error occurred while removing your Document', 'error');

							}

						});

					}

				}.bind(this));

			}

			this.empty();

			this.makeNode('btns', 'div', {css: 'right floated'});

			if(state.matchedDocument){

				this.btns.makeNode('editBtn', 'div',
					{
						tag:		'a'
						, text:	'Edit'
						, style:	'cursor:pointer;font-size:.78571429rem;display:inline-block; padding:0px 16.5px;'
					}
				).notify('click', {
					type: 'staffComponent-run',
					data: {
						run:editDocument.bind(modals)
					}
				}, sb.moduleId);

				this.btns.makeNode('viewBtn', 'div', {css: 'mini ui basic button', text: 'View'}).notify('click', {
					type: 'staffComponent-run',
					data: {
						run: function(dom){

							dom.btns.viewBtn.loading();

							viewDocument.call(modals, function(){

								dom.btns.viewBtn.loading(false);

							});

						}.bind({}, this)
					}

				}, sb.moduleId);

			} else {

				if( state.docType.need_file_download === 'Yes' && state.docType.file_download.hasOwnProperty('id') ){

					this.btns.makeNode('downloadBtn', 'div'
						, {
							tag:		'a'
							, text: 	'<i class="fa fa-download"></i> <em>Download</em>'
							, style:	'cursor:pointer;font-size:.78571429rem;display:inline-block; padding:0px 16.5px;'
						}
					).notify('click', {
						type: 'staffComponent-run',
						data: {
							run: function(){

								if(trx(state.docType.file_download)){
									downloadDocument.call(modals)

								}else{
									sb.dom.alerts.alert('Missing File', 'Add file to download from Staff Settings menu', 'warning');
								}

							}
						}
					}, sb.moduleId);
				}

				if(state.docType.need_file_upload === 'Yes'){

					this.btns.makeNode('uploadBtn', 'div', {css: 'mini ui basic button', text: '<i class="fa fa-upload"></i> <em>Upload</em>'}).notify('click', {
						type: 'staffComponent-run',
						data: {
							run:uploadDocument.bind(modals)
						}
					}, sb.moduleId);
				}

				if(state.docType.need_file_download === 'No' && state.docType.need_file_upload === 'No'){
					delete this.btns;
					this.makeNode('sp', 'lineBreak', {style:'height:29.28px;'});
				}

			}

			this.patch();

		}

		var matchedDocument = _.find(staff.staff_paperwork, function(match){

			return parseInt(match.document_type) === docType.id;

		});

		if(matchedDocument){ docState.matchedDocument = matchedDocument; }

		this.empty();

		if(docType.name){ docName = truncName(docType.name, 20); }

		if(docType.need_file_download == 'Yes' && docType.need_file_upload == 'No'){
			statusColor = 'gray',
			statusName = 'Download Only';
		}

		if(docType.service_type.length == 1){
			docServ = docType.service_type[0].name;
		}else if(docType.service_type.length == jobTypes.length){
			docServ = 'All Services'
		}

		this.makeNode('content', 'div', {css: 'content'});

		if(matchedDocument){

			statusColor = 'grey',
			statusName = '<i class="fa fa-check-circle"></i>  On File';

			this.content.makeNode('statlabel', 'div', {css: `ui top right attached green label`, text: `${statusName}`});
			this.content.makeNode('chead', 'div', {css: 'header', text:`${docName}`});
			this.content.makeNode('meta', 'div', {css: 'meta', text: `Document for ${docServ}` });
			this.content.makeNode('exp', 'div', {css: 'description', text: '<span class="small"><small><em><i class="fa fa-calendar"></i> Expiration Date: </em></small></span>  N/A'});

			if( matchedDocument.hasOwnProperty('expiration_date') && !_.isEmpty(matchedDocument.expiration_date) ){

				this.content.makeNode('exp', 'div', {css: 'description', text: '<span class="small"><small><em><i class="fa fa-calendar"></i> Expiration Date: </em></small></span>' + moment(matchedDocument.expiration_date).format('MM/DD/YY')});

			}else {

				this.content.makeNode('exp', 'div', {css: 'description', text: '<span class="small"><small><em><i class="fa fa-calendar"></i> Expiration Date: </em></small></span> No date saved'});

			}

		} else {

			if(docType.need_file_upload !== 'No'){

				this.content.makeNode('statlabel', 'div', {css: `ui top right attached ${statusColor} label`, text: `${statusName}`});

			} else {
				this.content.makeNode('sp', 'lineBreak', {spaces: 1, style:'height:30px'});
			}

			this.content.makeNode('chead', 'div', {css: 'header', text:`${docName}`});
			this.content.makeNode('meta', 'div', {css: 'meta', text: `Document for ${docServ}` });
			this.content.makeNode('exp', 'div', {css: 'description', text: '<span class="small"><small><em><i class="fa fa-calendar"></i> Expiration Date: </em></small></span>  N/A'});

			if(docType.hasOwnProperty('need_exp_date') && docType.need_exp_date == 'Yes'){

				this.content.makeNode('exp', 'div', {css: 'description', text: '<span class="small"><small><em><i class="fa fa-asterisk"></i> Requires an Expiration Date </em></small></span>'});

			}

		}

		var cardBtns = this.makeNode('extra', 'div', {css: 'extra content'});
		this.update = docViewCard.bind(this);

		cardBtnsShow.call(cardBtns, docState, function(resp){
			this.update(resp.docType, resp.index, selectedStaffObj, jobTypes);
		}.bind(this));

		this.patch();

	}

	function singleStaffView(obj, dom, state, draw){

		selectedStaffObj = obj;

		function singleStaffHeader(staffObj){ 

			if(staffObj.nickname){ var nickname = '<small>Nickname: '+staffObj.nickname+'</small>'; } else { var nickname = ''; }

			this.makeNode('h', 'div', {css: 'ui two column grid'});
			this.h.makeNode('hleft', 'div', {css: 'column'});
			this.h.hleft.makeNode('title', 'headerText', {text: `<i class="fa fa-user-circle"></i> ${staffObj.fname} ${staffObj.lname} ${nickname}`});

			this.h.makeNode('hright', 'div', {css: 'column'});
			this.h.hright.makeNode('btnGroup', 'buttonGroup', {css: 'right floated'});
			this.h.hright.btnGroup.makeNode('edit', 'button', {css:'pda-btn-orange', text:'<i class="fa fa-pencil"></i> Edit / <i class="fa fa-trash-o"></i> Delete'}).notify('click', {
				type:'staffComponent-run',
				data:{
					run: function(){ singleStaffEdit.call(dom, staffObj, state, draw); }
				}
			}, sb.moduleId);
			this.makeNode('tags', 'div', {css: 'sixteen wide column'});

			this.patch();

		}

		function singleStaffDashboard(staff){

			this.empty();

			function detailsView(staffObj){

				if(_.isEmpty(staffObj.email))
					staffObj.email = 'No email saved';

				if(_.isEmpty(staffObj.phone))
					staffObj.phone = 'No number saved';

				if(_.isEmpty(staffObj.hire_date))
					staffObj.hire_date = 'Not selected';

				var status = {
					ltext:'Enabled',
					lcss: 'ui mini teal basic label'
				};
				var cardBody = this;
				var left = cardBody.makeNode('leftCol', 'div', {css: 'column'});
				var right = cardBody.makeNode('rightCol', 'div', {css: 'column'});

				if(staffObj.enabled == 0){
					status.ltext = 'Disabled',
					status.lcss = 'ui mini basic label';
				}

				left.makeNode('lhead', 'div', {tag: 'h4', css: 'ui header', text: 'Staff Details'});
				left.makeNode('ldiv', 'div', {css: 'ui clearing divider'});
				left.makeNode('llist', 'div', {css: 'ui large list'});

				left.llist.makeNode('stat', 'div', {css: 'item'});
				left.llist.stat.makeNode('icon', 'div', {tag: 'i', css: 'icon center aligned', text: '<i class="fa fa-user-o"></i>'});
				left.llist.stat.makeNode('statcon', 'div', {css: 'content'});
				left.llist.stat.statcon.makeNode('label', 'div', {css: status.lcss, text: status.ltext});

				left.llist.makeNode('em', 'div', {css: 'item'});
				left.llist.em.makeNode('icon', 'div', {tag: 'i', css: 'icon', text: '<i class="fa fa-envelope"></i>'});
				left.llist.em.makeNode('content', 'div', {css: 'content', text:'P: '+ staffObj.email});

				if(staffObj.phone){
					left.llist.makeNode('phone', 'div', {css: 'item'});
					left.llist.phone.makeNode('icon', 'div', {css: 'icon', tag:'i', text: '<i class="fa fa-phone"></i>'});
					left.llist.phone.makeNode('content', 'div', {css:'content', text: 'P:'+ sb.dom.formatPhone(staffObj.phone)});
				}

				if(staffObj.work_email){
					left.llist.makeNode('workem', 'div', {css: 'item'});
					left.llist.workem.makeNode('icon', 'div', {tag: 'i', css: 'icon', text: '<i class="fa fa-envelope"></i>'});
					left.llist.workem.makeNode('content', 'div', {css: 'content', text:'W: '+ staffObj.work_email});
				}

				if(staffObj.work_phone){
					left.llist.makeNode('workphone', 'div', {css: 'item'});
					left.llist.workphone.makeNode('icon', 'div', {css: 'icon', tag:'i', text: '<i class="fa fa-phone"></i>'});
					left.llist.workphone.makeNode('content', 'div', {css:'content', text:'W: '+ sb.dom.formatPhone(staffObj.work_phone)});
				}

				left.llist.makeNode('hdate', 'div', {css: 'item'});
				left.llist.hdate.makeNode('icon', 'div', {tag: 'i', css: 'icon', text: '<i class="fa fa-calendar"></i>'});

				if(staffObj.hire_date !== 'Not selected') {
					left.llist.hdate.makeNode('content', 'div', {css: 'content', text:'Date of Hire: '+ moment(staffObj.hire_date).format('MM/DD/YYYY')});
				} else {
					left.llist.hdate.makeNode('content', 'div', {css: 'content', text:'Date of Hire: Not selected'});
				}

				if(staffObj.termination_date && !_.isEmpty(staffObj.termination_date)){
					left.llist.makeNode('tdate', 'div', {css: 'item'});
					left.llist.tdate.makeNode('icon', 'div', {tag: 'i', css: 'icon', text: '<i class="fa fa-calendar"></i>'});
					left.llist.tdate.makeNode('content', 'div', {css: 'content', text:'Termination Date: '+ moment(staffObj.termination_date).format('MM/DD/YYYY')});
				}

				right.makeNode('mhead', 'div', {tag: 'h4', css: 'ui header', text: 'Services'});
				right.makeNode('sdiv', 'div', {css: 'ui clearing divider'});
				right.makeNode('servlist', 'div', {css: 'ui large bulleted list'});
				_.each(staffObj.service, function(job){

					this.makeNode('job-'+job.id, 'div', {css: 'item', text: job.name});

				}, right.servlist);

				right.makeNode('rhead', 'div', {tag: 'h4', css: 'ui header', text:'Locations'});
				right.makeNode('bdiv', 'div', {css: 'ui clearing divider'});
				right.makeNode('baselist', 'div', {css: 'ui large bulleted list'});

				_.each(staffObj.base, function(base){

					this.makeNode('base-'+base.id, 'div', {css: 'item', text: `${base.name}, <em>${base.state}</em>`});


				}, right.baselist);

				this.patch();

			}

			function documentsView(staffObj){

				var dom = this;

				sb.data.db.obj.getAll('inventory_service', function(jobTypes){

					sb.data.db.obj.getAll('staff_paperwork_type', function(docs){

						var docTypes = docs;

						if(Array.isArray(docTypes) && _.isEmpty(docTypes)){

							dom.makeNode('header', 'text', {text: 'No Document Types in Staff Settings', size: 'small', css: 'pda-text-center'});

						} else {

							var staffDocs = [];

							_.each(docTypes, function(doc, i){

								if(checkDoc(doc.service_type, staffObj.service)){

									staffDocs.push(doc);

								}

							});

							if(staffDocs.length == 0){

								dom.makeNode('header', 'text', {text: 'No matched documents', size: 'small', css: 'pda-text-center'});

							} else {

								_.each(_.sortBy(staffDocs, 'name'), function(doc, i){

									this.makeNode('p-' + i, 'div', {css: 'card'});

									this.patch();

									docViewCard.call(this['p-' + i], doc, i, staffObj, jobTypes);

								}, dom);

							}

						}

						dom.patch();

					});

				});

			}

			function decisionsView(staffObj){

				this.empty();

				var decisionsContainer = this.makeNode('cont', 'container', {});

				this.patch();

				sb.notify({
					type:'show-decision-dashboard',
					data:{
						domObj: decisionsContainer,
						createdBy: sb.data.cookie.userId,
						askedTo: sb.data.cookie.userId,
						objectId: staff.id,
						objectType: 'staff',
						headerText: 'Decisions',
						headerSize: 'x-small',
						collapse: 'closed'
					}
				});


			}

			function notesView(staffObj){

				this.empty();

				this.patch();

				sb.notify({
					type: 'show-note-list-box',
					data: {
						domObj: this,
						objectIds:[staff.id],
						objectId:staff.id,
						headerText: 'Notes',
						headerSize: 'x-small',
						collapse: 'closed'
					}
				});

			}

			this.makeNode('cont', 'div', {css: 'ui grid animated fadeIn'});
			this.cont.makeNode('detCont', 'div', {css: 'sixteen wide column'});
			this.cont.makeNode('docCont', 'div', {css: 'sixteen wide column'});
			this.cont.makeNode('decCont', 'div', {css: 'sixteen wide column'});
			this.cont.makeNode('notCont', 'div', {css: 'sixteen wide column'});

			this.cont.detCont.makeNode('hrdet', 'container', {css: 'pda-container', title: '', collapse: true});
			this.cont.detCont.hrdet.makeNode('col', 'div', {css: 'ui two column stackable grid'});
			this.cont.docCont.makeNode('hrdoc', 'container', {title: 'Documents', collapse: 'open'});
			this.cont.docCont.hrdoc.makeNode('doccol', 'div', {css: 'ui cards'});

			this.viewDetails = detailsView.bind(this.cont.detCont.hrdet.col);
			this.viewDocuments = documentsView.bind(this.cont.docCont.hrdoc.doccol);
			this.viewDecisions = decisionsView.bind(this.cont.decCont.hrdec);
			this.viewNotes = notesView.bind(this.cont.notCont.hrnot);

			this.patch();

			this.viewDetails(staff);
			this.viewDocuments(staff);

		}

		function availView(staff){

			var dom = this;

			dom.empty();

			dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});

			dom.patch();

			sb.notify({
				type: 'start-schedule-availability',
				data: {
					domObj: dom.wrapper,
					obj: staff
				}
			});

		}

		function commentsView (staff) {

			var dom = this;

			dom.empty();
			dom.makeNode('wrapper', 'div', {css: 'animated fadeIn'});
			dom.patch();

			sb.notify({
				type: 'show-note-list-box',
				data: {
					domObj:dom.wrapper,
					objectId:obj.id,
					state:state
				}
			});

		}

		function payrollView(staff){

			var dom = this;

			dom.empty();

			dom.makeNode('load_cont', 'div', {css: 'text-center'});
			dom.load_cont.makeNode('loader', 'loader', {});
			dom.load_cont.makeNode('load_text', 'div', {text: 'Fetching staff payroll ...'});

			dom.patch();

			sb.data.db.obj.getById('users', staff.id, function(found){

				if(found) {

					dom.empty();

					dom.patch();

					sb.notify({
						type: 'payroll-single-staff-start',
						data: {
							domObj: dom,
							staffObj: found
						}
					});

				}

			}, 1);

		}

		function filesView(staff){

			this.empty();

			this.makeNode('fileDisplay', 'panel', {header: 'Files View', css: 'text-center'});

			this.patch();

				components.fileNav = sb.createComponent('file-nav');
				components.fileNav.notify({
					type: 'start-file-nav',
					data: {
						domObj: this.fileDisplay.body,
						files: {},
						fileTypes: {},
						objectData: staff,
						permissions: {
							edit: true
						}
					}
				});


		}

		function accountView(staffObj){

			this.empty();

			this.makeNode('cont', 'div', {css: 'ui grid'});

			this.cont.makeNode('accountCont', 'div', {css: 'sixteen wide column'}).makeNode('item', 'container', {css: 'ui stackable grid container', title:'Account Details', collapse:true});

			var account = this.cont.accountCont.item.makeNode('hrdet', 'div', {css: 'ui stackable centered grid'})
							.makeNode('accountContainer', 'div', {css:'center aligned two column row'});

			sb.notify({
				type:'show-user-account-info',
				data:{
					domObj: account,
					userObj:staffObj,
					singleView: true
				}
			});

			this.patch();
		}

		function updateNavBtn(menu, selected){

			_.each(menu, function(menuItem){
				if(menuItem.hasOwnProperty('removeClass')){
					menuItem.removeClass('active');
				}
			});

			selected.addClass('active');

		}

		function onDraw (dom) {

			components.tags.notify({
				type: 'object-tag-view',
				data: {
					domObj: dom.body.headerCont.tags,
					objectType: 'users',
					objectId: obj.id
				}
			});

		}

		dom.empty();
		modals = dom.makeNode('modals', 'modal', {});

		dom.makeNode('body', 'div', {css: 'ui one column grid'});
		dom.body.makeNode('alertContainer', 'div', {css: 'column'});

		dom.body.makeNode('headerCont', 'div', {css: 'column'});
		dom.body.makeNode('buttonCont', 'div', {});
		dom.body.makeNode('dashCont', 'div', {css: 'column'});

		dom.body.buttonCont.makeNode('navBtnGroup', 'div', {css: 'ui secondary pointing menu'});

		dom.body.buttonCont.navBtnGroup.makeNode('hr', 'div', {tag:'a', css: 'active teal item', text: '<i class="users icon"></i> HR'}).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function(){

					updateNavBtn(dom.body.buttonCont.navBtnGroup, dom.body.buttonCont.navBtnGroup.hr);
					dom.viewDashboard(selectedStaffObj);

				}
			}
		}, sb.moduleId);

		dom.body.buttonCont.navBtnGroup.makeNode('account', 'div', {tag:'a', css: 'teal item', text: '<i class="user icon"></i> Account'}).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function(){

					updateNavBtn(dom.body.buttonCont.navBtnGroup, dom.body.buttonCont.navBtnGroup.account);
					dom.viewAccount(selectedStaffObj);

				}
			}
		}, sb.moduleId);

		dom.body.buttonCont.navBtnGroup.makeNode('payroll_btn', 'div', {tag:'a', css: 'teal item', text: '<i class="money icon"></i> Payroll'}).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function() {

					updateNavBtn(dom.body.buttonCont.navBtnGroup, dom.body.buttonCont.navBtnGroup.payroll_btn);
					dom.viewPayroll(selectedStaffObj);

				}
			}
		}, sb.moduleId);

		dom.body.buttonCont.navBtnGroup.makeNode('avai_requests', 'div', {tag:'a', css: 'teal item', text: '<i class="calendar icon"></i> Availability'}).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function() {

					updateNavBtn(dom.body.buttonCont.navBtnGroup, dom.body.buttonCont.navBtnGroup.avai_requests);
					dom.viewAvail(selectedStaffObj);

				}
			}
		}, sb.moduleId);

		dom.body.buttonCont.navBtnGroup.makeNode('comments', 'div', {tag:'a', css: 'teal item', text: '<i class="comments icon"></i> Notes'}).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function() {

					updateNavBtn(dom.body.buttonCont.navBtnGroup, dom.body.buttonCont.navBtnGroup.comments);
					dom.viewComments(selectedStaffObj);

				}
			}
		}, sb.moduleId);

		dom.viewHeader = singleStaffHeader.bind(dom.body.headerCont);
		dom.viewDashboard = singleStaffDashboard.bind(dom.body.dashCont);
		dom.viewAvail = availView.bind(dom.body.dashCont);
		dom.viewPayroll = payrollView.bind(dom.body.dashCont);
		dom.viewFiles = filesView.bind(dom.body.dashCont);
		dom.viewAccount = accountView.bind(dom.body.dashCont);
		dom.viewComments = commentsView.bind(dom.body.dashCont);

		if(selectedStaffObj.hasOwnProperty('staff_paperwork')){

			dom.viewHeader(selectedStaffObj);
			dom.viewDashboard(selectedStaffObj);

			draw({
				dom: dom,
				after: onDraw
			});

		} else {

			var qParam = {
				dbCall: 'getById',
				queryObj: +selectedStaffObj.id
			};

			getSingleStaffPaperwork(qParam, function(res){

				selectedStaffObj = res;

				dom.viewHeader(selectedStaffObj);
				dom.viewDashboard(selectedStaffObj);

				draw({
					dom: dom,
					after: onDraw
				});

			}.bind(dom));
		}

	}

	function singleStaffEdit(object, state, draw){

		this.empty();

		this.makeNode('edit', 'div', {css: 'ui two column grid'});
		this.edit.makeNode('sp', 'div', {css:'row'});
		this.edit.makeNode('hrow', 'div', {css: 'row'});
		this.edit.hrow.makeNode('lcol', 'div', {css: 'ten wide column'});
		this.edit.hrow.lcol.makeNode('title', 'headerText', {text: `<i class="fa fa-pencil-square-o"></i> Edit ${object.fname} ${object.lname}`});
		this.edit.hrow.makeNode('rcol', 'div', {css: 'six wide column'});
		this.edit.hrow.rcol.makeNode('btns', 'buttonGroup', {css:'pull-right'}).makeNode('back', 'div', {css:'ui grey basic button', text:'<i class="fa fa-times"></i> Close'}).notify('click', {
			type:'staffComponent-run',
			data:{
				run:function(dom, object, state, draw){

					singleStaffView(object, dom, state, draw);

				}.bind({}, this, object, state, draw)
			}
		}, sb.moduleId);

		this.edit.hrow.rcol.btns.makeNode('delete', 'div', {css:'ui red button disabled', text:'<i class="fa fa-trash-o"></i> Delete'});
		this.edit.hrow.rcol.btns.makeNode('save', 'div', {css:'ui green button disabled', text:'<i class="fa fa-check"></i> Save'});
		this.edit.makeNode('frow', 'div', {css: 'row'});
		this.edit.frow.makeNode('lcol', 'div', {css: 'ten wide column'});
		this.edit.frow.lcol.makeNode('infoh', 'div', {css: 'ui dividing header', text: 'Info'});
		this.edit.frow.lcol.makeNode('sp', 'lineBreak', {spaces: 1});
		this.edit.frow.makeNode('rcol', 'div', {css: 'six wide column'});
		this.edit.frow.rcol.makeNode('deth', 'div', {css: 'ui dividing header', text: 'Details'});
		this.edit.frow.rcol.makeNode('sp', 'lineBreak', {spaces: 1});

		this.patch();

		createFormSetupData(function(options){

			this.edit.frow.lcol.makeNode('form', 'form', createFormSetup(object, options).personal);
			this.edit.frow.rcol.makeNode('form', 'form', createFormSetup(object, options).details);

			this.edit.hrow.rcol.btns.makeNode('delete', 'div', {css:'ui red button', text:'<i class="fa fa-trash-o"></i> Delete'}).notify('click', {
				type:'delete-staff-object',
				data:{
					object:object
				}
			}, sb.moduleId);

			this.edit.hrow.rcol.btns.makeNode('save', 'div', {css:'ui green button', text:'<i class="fa fa-check"></i> Save'}).notify('click', {
				type:'update-staff-member',
				data:{
					dom: this,
					state:state,
					draw:draw,
					object: object,
					form:{
						personal:this.edit.frow.lcol.form,
						details:this.edit.frow.rcol.form
					}
				}
			}, sb.moduleId);

			this.patch();

		}.bind(this));

	}

	function paperworkSettingsState(dom, bp){

		var crudSetup = {
			type:'show-table',
			data: {
				domObj: dom,
				objectType: 'staff_paperwork_type',
				childObjs:1,
				searchObjects:false,
				filters:false,
				download:false,
				navigation:false,
				rowSelection:true,
				multiSelectButtons:{
					erase:{
						name:'<i class="fa fa-times"></i> Delete',
						css:'pda-btn-red',
						domType:'erase',
						action:'erase'
					}
				},
				headerButtons:{
					create:{
						name: '<i class="fa fa-plus"></i> New Staff Document',
						css: 'pda-btn-green',
						domType: 'none',
						action: function(b, d, o){

							docTypeCreateEdit.call({}, dom, bp)
						}
					},
					reload: {
						name: 'Reload',
						css: 'pda-btn-blue',
						action: function() {}
					}
				},
				rowLink:{
					type:'edit',
					header:function(obj){
						return obj.name
					},
					action:function(bp, domObj, obj){

						docTypeCreateEdit.call({}, dom, bp, obj);
					}
				},
				visibleCols:{
					name: 'Name',
					uploadedDoc: 'Attached Document'
				},
				cells:{
					name:function(obj){
						return obj.name;
					},
					uploadedDoc:function(obj, cell){

						if(obj.file_download && obj.file_download.hasOwnProperty('file_name')){
							cell.makeNode('fileBtn', 'div', {css: 'ui left floated basic button', text:`<i class="fa fa-eye"></i> ${obj.file_download.file_name}`}).notify('click', {
								type:'staffComponent-run',
								data:{
									run: function(){

										sb.data.files.getWhere({id: obj.file_download.id}, function(document){

											sb.data.files.open(document[0]);

										});
									}
								}
							}, sb.moduleId);
						}
					}
				},
				data:function(paged, callback){
					sb.data.db.obj.getAll('staff_paperwork_type', function(ret){

						ret.data = _.sortBy(ret.data, 'name');
						callback(ret);

					}, 1, paged);
				}
			}
		};

		dom.empty();
		dom.patch();

		components.docTypes.notify(crudSetup);

	}

	function locationsSettingsState(dom){

		components.locationsTable.notify({
			type: 'show-table',
			data: {
				domObj:dom,
				objectType:'staff_base',
				childObjs:1,
				searchObjects:false,
				filters:false,
				download:false,
				navigation: false,
				headerButtons:{
					create:{
						name:'<i class="fa fa-plus"></i> New Location',
						css:'pda-btn-green',
						domType:'full',
						action:'create'
					}
				},
				rowSelection:true,
				multiSelectButtons:{
					erase:{
						name:'<i class="fa fa-times"></i> Delete',
						css:'pda-btn-red',
						domType:'erase',
						action:'erase'
					}
				},
				settings: false,
				rowLink:{
					type:'edit',
					header:function(obj){},
					formObjs:{
						name:{},
						street:{},
						city:{},
						state:{
							type:'state'
						},
						zip:{},
						country:{},
						description:{}
					},
					action:'edit'
				},
				visibleCols:{
					name:'Name',
					street:'Address',
					city:'City',
					state:'State',
					zip:'Zip Code',
					country:'Country'
				},
				cells: {
					name: function(obj){
						return obj.name;
					},
					street: function(obj){
						return obj.street;
					},
					city: function(obj){
						return obj.city;
					},
					state: function(obj){
						return obj.state;
					},
					zip: function(obj){
						return obj.zip;
					},
					country: function(obj){
						return obj.country;
					},
					description: function(obj){
						return obj.description;
					}

				},
				data:function(paged, callback){

					sb.data.db.obj.getAll('staff_base', function(ret){

						ret.data = _.sortBy(ret.data, 'name');

						callback(ret);

					}, 1, paged);

				}
			}
		});

	}

	function docTypeCreateEdit(dom, bp, obj){

		var selectedObj = null,
		    panelHeader = 'Create',
		    formArgs = {
				name: {
					name: 'name',
					type: 'text',
					label: 'Name'
				},
				service_type: {
					name: 'service_type',
					type: 'checkbox',
					label: 'Service Type',
					select_all_checked: false,
					options: []
				},
				need_file_download: {
					name: 'need_file_download',
					type: 'select',
					label: 'Need to Provide Document to Download?',
					options: [
						{
							name: 'Yes',
							label: 'Yes',
							value: 'Yes'
						},
						{
							name: 'No',
							label: 'No',
							value: 'No'
						}
					]
				},
				file_download: {
					type: 'file-upload',
					label: 'Select a File for Staff to Download',
					name: 'file_download'
				},
				need_file_upload: {
					name: 'need_file_upload',
					type: 'select',
					label: 'Require a Document upload from Staff?',
					options: [
						{
							name: 'Yes',
							label: 'Yes',
							value: 'Yes'
						},
						{
							name: 'No',
							label: 'No',
							value: 'No'
						}
					]
				},
				need_exp_date: {
					name: 'need_exp_date',
					type: 'select',
					label: 'Does this Document require an expiration date?',
					options: [
						{
							name: 'No',
							label: 'No',
							value: 'No'
						},
						{
							name: 'Yes',
							label: 'Yes',
							value: 'Yes'
						}
					]
				},
				not_frequency: {
					name: 'not_frequency',
					type: 'hidden',
					label: 'How many days to alert before expiration date?'
				}

			};

		if(obj){ selectedObj = obj;}

		function saveDocTypeForm(obj){

			var formData = this.body.cont.form.process();

			var docTypeObj = obj || {};

			_.each(formData.fields, function(fieldData, fieldKey){

				docTypeObj[fieldKey] = fieldData.value;
			});

			var updServiceList = _.reject(docTypeObj.service_type, function(d){

				return parseInt(d) == 0;

			});

			docTypeObj.service_type = _.map(updServiceList, function(o, i){

					return parseInt(o);

				});

			if(docTypeObj.need_exp_date == 'No'){

				docTypeObj.not_frequency = 0;
			}

			docTypeObj.fields = [];

			_.each(fields, function(field, i){

				var formData = this.body.cont.optionsCont['fieldColform-' + i].form.process().fields;

				docTypeObj.fields.push({

					name: formData.field.value

				});

			}, this);

			var validatedForm = docTypeFormValidator(docTypeObj);

			if (validatedForm.status === true){

				this.btns.cont.btnGroup.save_btn.addClass('ui green button loading');

				if(docTypeObj.hasOwnProperty('id')){

					sb.data.db.obj.update('staff_paperwork_type', docTypeObj, function(resp){

						if(resp){

							sb.dom.alerts.alert('Success!', '', 'success');

							sb.notify({
								type: 'update-table',
								data: {
									type: 'update',
									data: getStaffWithPaperwork
								}
							});

							paperworkSettingsState(dom, bp);

						} else {

							sb.dom.alerts.alert('Error', 'An error occurred while editing your Document', 'error');
							this.btns.cont.btnGroup.save_btn.addClass('ui green button disabled');
						}

					});


				}else{

					sb.data.db.obj.create('staff_paperwork_type', docTypeObj, function(resp){

						if(resp){

							sb.dom.alerts.alert('Success!', '', 'success');

							sb.notify({
								type: 'update-table',
								data: {
									type: 'update',
									data: getStaffWithPaperwork
								}
							});

							paperworkSettingsState(dom, bp);

						} else {

							sb.dom.alerts.alert('Error', 'An error occurred while creating your Document', 'error');
							this.btns.cont.btnGroup.save_btn.addClass('ui green button disabled');

						}

					});

				}

			} else {

				sb.dom.alerts.alert('Warning', validatedForm.errorMessage, 'warning');
				return;

			}

		}

		if(trx(selectedObj)){

			var filteredServiceType = _.filter(selectedObj.service_type, function(s){

				if(trx(s)){ return s; }
			});

			panelHeader = 'Edit',
			formArgs.id = {
				type: 'hidden',
				name: 'id',
				value: parseInt(selectedObj.id)
			},
			formArgs.name.value = selectedObj.name,
			formArgs.service_type.value = _.pluck(filteredServiceType, 'id');
			formArgs.need_file_download.value = selectedObj.need_file_download,
			formArgs.need_file_upload.value = selectedObj.need_file_upload,
			formArgs.need_exp_date.value = selectedObj.need_exp_date,
			formArgs.not_frequency.value = selectedObj.not_frequency;

			if(selectedObj.need_file_download === 'No'){

				formArgs.file_download.type = 'hidden';

				formArgs.need_file_download.selected = true;

			}

			if(trx(selectedObj.file_download) && selectedObj.file_download.hasOwnProperty('file_name')){

				formArgs.file_download.label = `(Current file uploaded: ${selectedObj.file_download.file_name})`;

			}

			if(selectedObj.need_exp_date === 'Yes'){

				formArgs.not_frequency.type = 'int';

			}

		}

		dom.empty();

		dom.makeNode('segment', 'div', {css: 'ui raised blue segment'});
		dom.segment.makeNode('wrapper', 'div', {css: 'ui grid'});
		dom.segment.wrapper.makeNode('head', 'div', {css:'centered row'});
		dom.segment.wrapper.makeNode('body', 'div', {css: 'centered row'});
		dom.segment.wrapper.head.makeNode('col1', 'div', {css:'fifteen wide column'});
		dom.segment.wrapper.head.col1.makeNode('title', 'headerText', {text: panelHeader});
		dom.segment.wrapper.makeNode('btns', 'div', {css: 'centered row'});
		dom.segment.wrapper.btns.makeNode('cont', 'div', {css: 'fifteen wide column'});
		dom.segment.wrapper.btns.cont.makeNode('btnGroup', 'div', {css:'ui buttons'});
		dom.segment.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {text: '<i class="fa fa-check"></i> Save', css: 'ui green button disabled'});
		dom.segment.wrapper.btns.cont.btnGroup.makeNode('back_btn', 'div', {text: '<i class="fa fa-arrow-left"></i> Back', css: 'ui yellow button'}).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function() {

					paperworkSettingsState(dom, bp);

				}
			}
		}, sb.moduleId);

		dom.segment.wrapper.body.makeNode('load_cont', 'div', {css: 'fifteen wide column centered'});
		dom.segment.wrapper.body.load_cont.makeNode('loader', 'loader', {size: 'large'});
		dom.segment.wrapper.body.makeNode('cont', 'div', {css: 'fifteen wide column centered'});

		dom.patch();


		sb.data.db.obj.getAll('inventory_service', function(services){

			var sortedServices = services.sort(function (a, b) {

				    	return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
				});

			_.each(sortedServices, function(serv){

				formArgs.service_type.options.push({
					name: 'service_type',
					value: serv.id,
					label: serv.name
				});

				dom.segment.wrapper.body.load_cont.empty();

				dom.segment.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {text: '<i class="fa fa-check"></i> Save', css: 'ui green button'});

				dom.segment.wrapper.body.cont.makeNode('form', 'form', formArgs);

				dom.segment.wrapper.body.cont.makeNode('sp1', 'lineBreak', {spaces: 1});

				if(selectedObj && !_.isEmpty(selectedObj.fields)){

					fields = [];

					_.each(selectedObj.fields, function(field, i){

						var fieldObj = {
							name: 'field',
							type: 'text',
							label: 'Name',
							value: field.name,
							id: field.id
						};

						fields.push(fieldObj);

					});

				}

				dom.segment.wrapper.body.cont.makeNode('addBtn', 'div', {css: 'ui teal basic button', text: '<i class="fa fa-plus fa-lg"></i> Add Options Field'})

				dom.segment.wrapper.body.cont.makeNode('sp2', 'lineBreak', {spaces: 1});

				dom.segment.wrapper.body.cont.makeNode('optionsCont', 'div', {css: 'ui middle aligned two column grid'});

				dom.segment.wrapper.body.cont.addBtn.notify('click', {
					type: 'update-doc-type-add-field',
					data: {
						addField: addFieldRemoveField.bind(dom.segment.wrapper.body.cont.optionsCont),
						add: true
					}
				}, sb.moduleId);

				dom.segment.wrapper.body.cont.form.need_file_download.notify('change', {
					type: 'update-doc-type-create-form-download',
					data: {
						form: dom.segment.wrapper.body.cont.form,
						dom: dom,
						file_download: true
					}
				}, sb.moduleId);

				dom.segment.wrapper.body.cont.form.need_exp_date.notify('change', {
					type: 'update-doc-type-create-form-download',
					data: {
						form: dom.segment.wrapper.body.cont.form,
						dom: dom,
						not_frequency: true
					}
				}, sb.moduleId);

				dom.segment.wrapper.btns.cont.btnGroup.back_btn.notify('click', {
					type: 'staffComponent-run',
					data: {
						run: function(){ paperworkSettingsState(this); }.bind(dom)
					}
				});

				dom.segment.wrapper.btns.cont.btnGroup.save_btn.notify('click', {
					type: 'staffComponent-run',
					data: {
						run: saveDocTypeForm.bind(dom.segment.wrapper, selectedObj)
					}
				});

				dom.patch();

				printField(dom.segment.wrapper.body.cont.optionsCont, fields);

			});

		});

	}

	function printField(modDom, fields){

		modDom.empty();

		_.each(fields, function(field, i){

		    modDom.makeNode('fieldColform-'+i, 'div', {css:'fifteen wide column',dataId: i});
		    modDom.makeNode('fieldColbtn-'+i, 'div', {css: 'one wide column', dataId: i});
		    modDom['fieldColbtn-'+i].makeNode('deleteBtn', 'div', {css: 'circular ui red button', text: 'X'}).notify('click', {
			    type: 'update-doc-type-delete-field',
			    data: {
				    removeField: addFieldRemoveField.bind(modDom, false),
				    fieldIndex: i,
			    }
		    }, sb.moduleId);
		    modDom['fieldColform-'+i].makeNode('form', 'form', [field]);

		});

		modDom.patch();

	}

	function addFieldRemoveField(add, index){

		var cont = this,
		fieldObj = {
			name: 'field',
			type: 'text',
			placeholder:'Enter a name for new field'
		};

		_.each(fields, function(field, i){

			var formData = cont['fieldColform-' + i].form.process().fields;

			field.value = formData.field.value;

		});

		if(add){

			fields.push(fieldObj);

			printField(cont, fields);

		} else {

			fields.splice(index, 1);

			printField(cont, fields);

		}

	}

	function docTypeFormValidator(obj){

		var ret = {
				status:true,
				errorMessage:''
			};

		if(_.isEmpty(obj.name)){

			ret.status = false;
			ret.errorMessage += 'Please fill out the name and description. ';

		}

		if(obj.need_file_download == 'No' && obj.need_file_upload == 'No'){

			ret.status = false;
			ret.errorMessage += 'You must require staff to upload a file or provide one for them to download';

		}

		if (!obj.service_type){

			ret.status = false;
			ret.errorMessage += 'Please select a service type. ';
		}

		if(trx(obj.file_download) && obj.file_download.hasOwnProperty('file_name')){

			return;

		} else if (obj.need_file_download == 'Yes' && obj.file_download.fileData == undefined){

			ret.status = false;
			ret.errorMessage += 'Please select a file for staff to download. ';
		}

		if(obj.need_exp_date == 'Yes' && _.isEmpty(obj.not_frequency)){

			ret.status = false;
			ret.errorMessage += 'Please pick number of days to be notified of expiring documents. ';

		}

		if(obj.fields.length > 0){

			_.each(obj.fields, function(field){

				if(_.isEmpty(field.name)){

					ret.status = false;
					ret.errorMessage += 'Additional fields require a name.';
				}
			});
		}

		//return ret;

		ret.status = true;
		ret.errorMessage = '';

		return ret;

	}

	function getChartOfAccounts(coaCompId, callback){

		if(coaCompId.chart_of_accounts_company){

			sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

				sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:coaCompId.chart_of_accounts_company}, function(accounts){

					callback({
						coaComps:coaComps,
						accounts:accounts
					});

				});

			});

		}else{

			sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

				if(coaComps.length > 0){

					sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:coaComps[0].id}, function(accounts){

						callback({
							coaComps:coaComps,
							accounts:accounts
						});

					});

				}else{

					callback({
						coaComps:coaComps,
						accounts:[]
					});

				}

			});

		}

	}

	function jobTypeSettings(dom, bp) {

		function create_jobType(dom) {

			var formObj = {
					name: {
						name: 'name',
						type: 'text',
						label: 'Name'
					},
					description: {
						type: 'textbox',
						name: 'description',
						label: 'Description'
					},
					permission: {
						type: 'select',
						name: 'permission',
						label: 'User Permissions',
						options: []
					},
					manager_locations:{
						type:'checkbox',
						name:'manager_locations',
						label:'Location Manager: <small>Allows Time Clock Admin Privileges</small>',
						options: []
					},
					can_be_billed: {
    				    type: 'select',
    				    name: 'can_be_billed',
    				    label: 'Can be billed?',
    				    options: [
        				    {
            				    name: 'Yes',
            				    value: 'yes'
        				    },
        				    {
            				    name: 'No',
            				    value: 'no'
        				    }
    				    ]
					},
					price_type: {
						type: 'select',
						name: 'price_type',
						label: 'Billing Type',
						options: [],
						onChange: switch_formFields
					},
					price: {
						type: 'usd',
						name: 'price',
						label: 'Flat Fee Amount'
					},
					rate: {
						type: 'hidden',
						name: 'rate',
						label: 'Rate'
					},
					min_hours: {
						type: 'hidden',
						name: 'min_hours',
						label: 'Minimum Hours'
					},
					max_flat_hours: {
						type: 'hidden',
						name: 'max_flat_hours',
						label: 'Maximum hours of flat rate time before hourly billing starts'
					},
					surcharges: {
						type: 'checkbox',
						name: 'surcharges',
						label: 'Surcharges',
						options: []
					},
					vendor: {
						type: 'select',
						name: 'vendor',
						label: 'Vendor (for Contract Labor)',
						options: []
					},
					chart_of_account_company: {
						name: 'chart_of_account_company',
						type: 'select',
						label: 'Chart Of Account Company',
						options: [],
						change: update_CoAFormField.bind(this, dom)
					},
					chart_of_account: {
						name: 'chart_of_account',
						type: 'select',
						label: 'Chart Of Account',
						options: []
					},
					inclusive_tax: {
						type: 'checkbox',
						name: 'inclusive_tax',
						label: 'Inclusive Tax',
						options: []
					},
					exclusive_tax: {
						type: 'checkbox',
						name: 'exclusive_tax',
						label: 'Exclusive Tax',
						options: []
					}
				};

			dom.empty();

			dom.makeNode('segment', 'div', {css: 'ui raised blue segment'});
			dom.segment.makeNode('wrapper', 'div', {css: 'ui grid'});
			dom.segment.wrapper.makeNode('head', 'div', {css:'centered row'});
			dom.segment.wrapper.makeNode('body', 'div', {css: 'centered row'});
			dom.segment.wrapper.head.makeNode('col1', 'div', {css:'fifteen wide column'});
			dom.segment.wrapper.head.col1.makeNode('title', 'headerText', {text: '<i class="fa fa-user-o"></i> Create'});
			dom.segment.wrapper.makeNode('btns', 'div', {css: 'centered row'});
			dom.segment.wrapper.btns.makeNode('cont', 'div', {css: 'fifteen wide column'});
			dom.segment.wrapper.btns.cont.makeNode('btnGroup', 'div', {css:'two ui buttons'});
			dom.segment.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {text: '<i class="fa fa-check"></i> Save', css: 'ui green button disabled'});
			dom.segment.wrapper.btns.cont.btnGroup.makeNode('back_btn', 'div', {text: '<i class="fa fa-arrow-left"></i> Back', css: 'ui basic red button'}).notify('click', {
				type: 'staffComponent-run',
				data: {
					run: function() {

						jobTypeSettings(dom, bp);

					}
				}
			}, sb.moduleId);

			dom.segment.wrapper.body.makeNode('load_cont', 'div', {css: 'fifteen wide column centered'});
			dom.segment.wrapper.body.load_cont.makeNode('loader', 'loader', {size: 'large'});
			dom.segment.wrapper.body.makeNode('cont', 'div', {css: 'fifteen wide column centered'});

			checkChartOfAccountsSetup(function(){

				getChartOfAccounts({}, function(coaInfo) {

					sb.data.db.obj.getWhere('companies', {is_vendor: 1, childObjs: 1}, function(companies) {

						sb.data.db.obj.getAll('tax_rates', function(taxes) {

							sb.data.db.obj.getAll('surcharges', function(surcharges) {

								sb.data.db.obj.getAll('inventory_billable_categories', function(categories) {

									sb.data.db.obj.getAll('user_views', function(permissions) {

										sb.data.db.obj.getAll('staff_base', function(locations){

											if(_.isEmpty(permissions)){

												delete formObj.permission;

											}else{

												formObj.permission.options = _.map(_.sortBy(permissions, 'name'), function(permission) {

													return {
														name: permission.name,
														value: permission.id
													};

												});

											}

											formObj.manager_locations.options = _.map(_.sortBy(locations, 'name'), function(location){
												return {
													name: 'manager_locations',
													value: location.id,
													label: location.name
												};
											});

											formObj.price_type.options = _.map(_.sortBy(bp.price_type.options, 'price_type'), function(field, fieldName) {

												return {
													name: field,
													value: fieldName
												};

											});

											if(!_.isEmpty(surcharges)) {

												formObj.surcharges.options = _.map(_.sortBy(surcharges, 'name'), function(tax) {

													return {
														name: 'surcharges',
														value: tax.id,
														label: tax.name
													};

												});

											} else {

												delete formObj.surcharges;

											}

											formObj.vendor.options = _.map(_.sortBy(companies, 'name'), function(company) {

												return {
													name: company.name,
													value: company.id
												};

											});
											formObj.vendor.options.unshift({
												name: 'N/A',
												value: 0
											});

											formObj.chart_of_account_company.options = _.map(_.sortBy(coaInfo.coaComps, 'name'), function(company) {

												return {
													name: company.name,
													value: company.id
												};

											});

											formObj.chart_of_account.options = _.map(_.sortBy(coaInfo.accounts, 'name'), function(company) {

												return {
													name:company.name,
													value:company.id
												};

											});

											formObj.inclusive_tax.options = _.map(_.sortBy(taxes, 'name'), function(tax) {

												return {
													name: 'inclusive_tax',
													value: tax.id,
													label: tax.name
												};

											});

											formObj.exclusive_tax.options = _.map(_.sortBy(taxes, 'name'), function(tax) {

												return {
													name: 'exclusive_tax',
													value: tax.id,
													label: tax.name
												};

											});

											dom.segment.wrapper.body.load_cont.empty();

											dom.segment.wrapper.body.cont.makeNode('form', 'form', formObj);

											dom.segment.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {text: '<i class="fa fa-check"></i> Save', css: 'ui green button right floated'}).notify('click', {
												type: 'staffComponent-run',
												data: {
													run: function() {
														save_jobType(dom.segment.wrapper.body.cont.form, dom, undefined, permissions);
													}
												}
											}, sb.moduleId);

											dom.segment.wrapper.patch();

										}, 1);

									}, 1);

								}, 1);

							}, 1);

						}, 1);

					});

				});

			});

			dom.patch();

		}

		function edit_jobType(bp, dom, obj) {

			var formObj = {
					name: {
						name: 'name',
						type: 'text',
						label: 'Name',
						value: obj.name
					},
					description: {
						type: 'textbox',
						name: 'description',
						label: 'Description',
						value: obj.description
					},
					permission: {
						type: 'select',
						name: 'permission',
						label: 'User Permissions',
						options: [],
						value: obj.permission
					},
					manager_locations:{
						type:'checkbox',
						name:'manager_locations',
						//label:'Manage Scheduling Permissions by Location (allows this job type to reschedule shifts at certain locations)',
						//label:'Manager Locations <small>(selecting a location enables this job type to reassign user schedules/shifts at the specified (selected) locations)</small>',
						//label:'Enable Manager Privileges by Location for this Job Type',
						label:'Location Manager: <small>Time Clock Admin Privileges</small>',
						options: [],
						value:obj.manager_locations
					},
					can_be_billed: {
	    				    type: 'select',
	    				    name: 'can_be_billed',
	    				    label: 'Can be billed?',
	    				    options: [
	        				    {
	            				    name: 'Yes',
	            				    value: 'yes'
	        				    },
	        				    {
	            				    name: 'No',
	            				    value: 'no'
	        				    }
	    				    ],
	    				    value: obj.can_be_billed
					},
					price_type: {
						type: 'select',
						name: 'price_type',
						label: 'Billing Type',
						options: [],
						change: switch_formFields.bind(this, dom),
						value: obj.price_type
					},
					price: {
						type: 'usd',
						name: 'price',
						label: 'Flat Fee Amount'
					},
					rate: {
						type: 'hidden',
						name: 'rate',
						label: 'Rate'
					},
					min_hours: {
						type: 'hidden',
						name: 'min_hours',
						label: 'Minimum Hours'
					},
					max_flat_hours: {
						type: 'hidden',
						name: 'max_flat_hours',
						label: 'Maximum hours of flat rate time before hourly billing starts'
					},
					surcharges: {
						type: 'checkbox',
						name: 'surcharges',
						label: 'Surcharges',
						options: [],
						value: obj.surcharges
					},
					vendor: {
						type: 'select',
						name: 'vendor',
						label: 'Vendor (for Contract Labor)',
						options: []
					},
					chart_of_accounts_company: {
						name: 'chart_of_account_company',
						type: 'select',
						label: 'Chart Of Account Company',
						options: [],
						change: update_CoAFormField.bind(this, dom),
					},
					chart_of_account: {
						name: 'chart_of_account',
						type: 'select',
						label: 'Chart Of Account',
						options: []
					},
					inclusive_tax: {
						type: 'checkbox',
						name: 'inclusive_tax',
						label: 'Inclusive Tax',
						options: [],
						value: obj.inclusive_tax
					},
					exclusive_tax: {
						type: 'checkbox',
						name: 'exclusive_tax',
						label: 'Exclusive Tax',
						options: [],
						value: obj.exclusive_tax
					}
				};

			if(trx(obj.chart_of_account)){
				formObj.chart_of_accounts_company.value = obj.chart_of_account.chart_of_accounts_company;
			}

			dom.empty();

			dom.makeNode('segment', 'div', {css: 'ui raised blue segment'});
			dom.segment.makeNode('wrapper', 'div', {css: 'ui grid'});
			dom.segment.wrapper.makeNode('head', 'div', {css:'centered row'});
			dom.segment.wrapper.makeNode('body', 'div', {css: 'centered row'});
			dom.segment.wrapper.head.makeNode('col1', 'div', {css:'fifteen wide column'});
			dom.segment.wrapper.head.col1.makeNode('title', 'headerText', {text: `Edit`});
			dom.segment.wrapper.makeNode('btns', 'div', {css: 'centered row'});
			dom.segment.wrapper.btns.makeNode('cont', 'div', {css: 'fifteen wide column'});
			dom.segment.wrapper.btns.cont.makeNode('btnGroup', 'div', {css:'ui buttons'});
			dom.segment.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {text: '<i class="fa fa-check"></i> Save', css: 'ui green button disabled'});
			dom.segment.wrapper.btns.cont.btnGroup.makeNode('erase_btn', 'div', {text: '<i class="fa fa-trash"></i> Delete', css: 'ui red button disabled'});
			dom.segment.wrapper.btns.cont.btnGroup.makeNode('back_btn', 'div', {text: '<i class="fa fa-arrow-left"></i> Back', css: 'ui basic red button'}).notify('click', {
				type: 'staffComponent-run',
				data: {
					run: function() {

						jobTypeSettings(dom, bp);

					}
				}
			}, sb.moduleId);

			dom.segment.wrapper.body.makeNode('load_cont', 'div', {css: 'fifteen wide column centered'});
			dom.segment.wrapper.body.load_cont.makeNode('loader', 'loader', {size: 'large'});
			dom.segment.wrapper.body.makeNode('cont', 'div', {css: 'fifteen wide column centered'});

			getChartOfAccounts((trx(obj.chart_of_account)) ? obj.chart_of_account : {}, function(coaInfo) {

				sb.data.db.obj.getWhere('companies', {is_vendor: 1, childObjs: 1}, function(companies) {

					dom.segment.wrapper.body.load_cont.makeNode('load_text', 'text', {text: 'Loading tax rates ...', css: 'pda-text-center'});

					dom.segment.wrapper.body.load_cont.patch();

					sb.data.db.obj.getAll('tax_rates', function(taxes) {

						dom.segment.wrapper.body.load_cont.makeNode('load_text', 'text', {text: 'Loading surcharges ...', css: 'pda-text-center'});

						dom.segment.wrapper.body.load_cont.patch();

						sb.data.db.obj.getAll('surcharges', function(surcharges) {

							dom.segment.wrapper.body.load_cont.makeNode('load_text', 'text', {text: 'Loading billable categories ...', css: 'pda-text-center'});

							dom.segment.wrapper.body.load_cont.patch();

							sb.data.db.obj.getAll('inventory_billable_categories', function(categories) {

								dom.segment.wrapper.body.load_cont.makeNode('load_text', 'text', {text: 'Loading user views ...', css: 'pda-text-center'});

								dom.segment.wrapper.body.load_cont.patch();

								sb.data.db.obj.getAll('user_views', function(permissions) {

									dom.segment.wrapper.body.load_cont.makeNode('load_text', 'text', {text:'Loading staff locations ...', css:'pda-text-center'});

									dom.segment.wrapper.body.load_cont.patch();

									sb.data.db.obj.getAll('staff_base', function(locations){

										if(permissions.length > 0){

											formObj.permission.options = _.map(_.sortBy(permissions, 'name'), function(permission) {

		 										if(obj){
			 										if(obj.permission){
				 										if(obj.permission.id == permission.id){

															return {
																name: permission.name,
																value: permission.id,
																selected:true
															};

														}else{

															return {
																name: permission.name,
																value: permission.id
															};

														}
			 										}else{

														return {
															name: permission.name,
															value: permission.id
														};

													}
		 										}else{

													return {
														name: permission.name,
														value: permission.id
													};

												}

											});

										}else{

											delete formObj.permission;

										}

										formObj.manager_locations.options = _.map(_.sortBy(locations, 'name'), function(location){
											return {
												name: 'manager_locations',
												value: location.id,
												label: location.name
											};
										});

										formObj.price_type.options = _.map(_.sortBy(bp.price_type.options, 'price_type'), function(field, fieldName) {

											return {
												name: field,
												value: fieldName
											};

										});

										switch(obj.price_type) {

											case '0':
											case 'flat':

												formObj.price.value = obj.price;
												formObj.price.type = 'usd';

												break;

											case '1':
											case 'hourly':

												formObj.rate.value = obj.rate;
												formObj.rate.type = 'usd';
												formObj.price.type = 'hidden';

												break;

											case '3':
											case 'flat_and_hourly':

												formObj.price.value = obj.price;
												formObj.price.type = 'usd';
												formObj.rate.value = obj.rate;
												formObj.rate.type = 'usd';
												formObj.min_hours.value = obj.min_hours;
												formObj.min_hours.type = 'number';
												formObj.max_flat_hours.value = obj.max_flat_hours;
												formObj.max_flat_hours.type = 'number';

												break;

											case '2':
											case 'non_billable':

												formObj.price.type = 'hidden',
												formObj.rate.type = 'hidden';
												formObj.min_hours.type = 'hidden';
												formObj.max_flat_hours.type = 'hidden';

												break;

										}

										if(!_.isEmpty(surcharges)) {

											formObj.surcharges.options = _.map(_.sortBy(surcharges, 'name'), function(tax) {

												return {
													name: 'surcharges',
													value: tax.id,
													label: tax.name
												};

											});

										} else {

											delete formObj.surcharges;

										}

										formObj.vendor.options = _.map(_.sortBy(companies, 'name'), function(company) {

											return {
												name: company.name,
												value: company.id
											};

										});

										formObj.vendor.options.unshift({
											name: 'N/A',
											value: 0
										});

										if(obj.vendor_id !== null) {
											formObj.vendor.value = obj.vendor_id;
										}

										formObj.chart_of_accounts_company.options = _.map(_.sortBy(coaInfo.coaComps, 'name'), function(company) {

											return {
												name: company.name,
												value: company.id
											};

										});

										formObj.chart_of_account.options = _.map(_.sortBy(coaInfo.accounts, 'name'), function(company) {

											return {
												name: company.name,
												value: company.id
											};

										});

										formObj.inclusive_tax.options = _.map(_.sortBy(taxes, 'name'), function(tax) {

											return {
												name: 'inclusive_tax',
												value: tax.id,
												label: tax.name
											};

										});

										formObj.exclusive_tax.options = _.map(_.sortBy(taxes, 'name'), function(tax) {

											return {
												name: 'exclusive_tax',
												value: tax.id,
												label: tax.name
											};

										});

										dom.segment.wrapper.body.load_cont.empty();

										dom.segment.wrapper.body.cont.makeNode('form', 'form', formObj);

										dom.segment.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {text: '<i class="fa fa-check"></i> Save', css: 'ui green button'}).notify('click', {
											type: 'staffComponent-run',
											data: {
												run: function() {
													save_jobType(dom.segment.wrapper.body.cont.form, dom, obj, permissions)
												}
											}
										}, sb.moduleId);

										dom.segment.wrapper.btns.cont.btnGroup.makeNode('erase_btn', 'div', {text: '<i class="fa fa-trash"></i> Delete', css: 'ui red button'}).notify('click', {
											type: 'staffComponent-run',
											data: {
												run: function(obj) {

													sb.dom.alerts.ask({
														title: 'Are you sure?',
														text: 'Delete this job type?'
													}, function(resp){

														if(resp){
															sb.data.db.obj.erase('inventory_service', obj.id, function(){

																sb.dom.alerts.alert('Deleted!', '', 'success');

																sb.data.db.obj.getWhere('groups'
																	, {
																		job_type: 	obj.id
																		, group_type:  'JobType'
																	}
																	, function(relatedGroup){

																		if (relatedGroup) {

																			sb.data.db.obj.erase(''
																				, relatedGroup[0].id
																				, function(res){ return res; }
																			);
																		}

																	}
																);

																jobTypeSettings(dom, bp);

															});
														}

													});

												}.bind({}, obj)
											}
										}, sb.moduleId);

										dom.segment.wrapper.patch();

									}, 2);

								}, 1);

							}, 1);

						}, 1);

					}, 1);

				});

			});

			dom.patch();

		}

		function save_jobType(form, dom, obj, permissions) {

			var formData = form.process().fields;
			var jobTypeObj = {};
			var create = 'create';
			var jobType_payroll_cache = {};

			function display_jobTypeRate(setup) {

				// setup.obj --> job type object

				if(typeof setup.obj.price_type === 'number') {
					setup.obj.price_type = setup.obj.price_type.toString();
				}

				switch(setup.obj.price_type) {

					case '0':
					case 'flat':

						return {
							text: '$' + (setup.obj.price/100).formatMoney() + ' flat fee',
							rate: (setup.obj.price/100).formatMoney()
						};

						break;

					case '1':
					case 'hourly':

						return {
							text: '$' + (setup.obj.rate/100).formatMoney() + ' per hour',
							rate: (setup.obj.rate/100).formatMoney()
						};

						break;

					case '2':
					case 'non-billable':

						return {
							text: '',
							rate: 0
						};

						break;

					case '3':
					case 'flat_and_hourly':

						return {
							text: '$' + (setup.obj.price/100).formatMoney() + ' flat fee ($'+ (setup.obj.rate/100).formatMoney() +' per hour after '+ setup.obj.max_flat_hours +' hours)',
							rate: (setup.obj.rate/100).formatMoney(),
							price: 	(setup.obj.price/100).formatMoney()
						};

						break;

					default:
						return;

				}

			}

			function display_billingTypeName(billing_type) {

				switch(billing_type) {

					case '0':
					case 'flat':

						return 'Flat';

						break;

					case '1':
					case 'hourly':

						return 'Hourly';

						break;

					case '2':
					case 'non-billable':

						return 'Non-billable';

						break;

					case '3':
					case 'flat_and_hourly':

						return 'Flat and hourly';

						break;

				}

			}

			function compare_payrollData(setup) {

				// setup.jobType --> job type object
				// setup.cached_payrollData

				var jobType = setup.jobType;
				var cached_data = setup.cached_payrollData;

				if(jobType.price_type !== cached_data.price_type) {

					var changed = '<div><i>Billing type</i> was changed from <strong>'+ display_billingTypeName(cached_data.price_type) +'</strong> to <strong>'+ display_billingTypeName(jobType.price_type) +'</strong></div>';

					switch(jobType.price_type) {

						case '0':
						case 'flat':

							return {
								text: changed + '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>'+
									  '<div><strong>Rate: </strong>$'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee</div>'
							};

							break;

						case '1':
						case 'hourly':

							return {
								text: changed + '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>'+
									  '<div><strong>Rate: </strong>$'+ (parseInt(jobType.rate)/100).formatMoney() +' per hour</div>'
							};

							break;

						case '2':
						case 'non-billable':

							return {
								text: changed + '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>'
							};

							break;

						case '3':
						case 'flat_and_hourly':

							return {
								text: changed + '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>'+
									  '<div><strong>Rate: </strong>$'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee ($'+ (parseInt(jobType.rate)/100).formatMoney() +' per hour after '+ jobType.max_flat_hours +' hours)</div>'
							};

							break;

					}

				} else {

					var data = '<div><strong>Billing Type: </strong>'+ display_billingTypeName(jobType.price_type) +'</div>';

					switch(jobType.price_type) {

						case '0':
						case 'flat':

							var price_change = '';

							if(parseInt(jobType.price) !== parseInt(cached_data.price)) {

								price_change = '<div><i>Flat rate</i> was changed from $'+ (parseInt(cached_data.price)/100).formatMoney() +' flat fee to $'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee</div>' + data;

								return {
									text: price_change
								};

							} else {

								return {
									text: 'No changes were made'
								};
							}

							break;

						case '1':
						case 'hourly':

							var rate_change = '';

							if(parseInt(jobType.rate) !== parseInt(cached_data.rate)) {

								rate_change = '<div><i>Hourly rate</i> was changed from $'+ (parseInt(cached_data.rate)/100).formatMoney() +' per hour to $'+ (parseInt(jobType.rate)/100).formatMoney() +' per hour</div>' + data;

								return {
									text: rate_change
								};

							} else {

								return {
									text: 'No changes were made'
								};

							}

							break;

						case '2':
						case 'non-billable':

							return {
								text: data
							};

							break;

						case '3':
						case 'flat_and_hourly':

							var rate_change = '';
							var price_change = '';

							if(parseInt(jobType.price) !== parseInt(cached_data.price) && parseInt(jobType.rate) !== parseInt(cached_data.rate)) {

								price_change = '<div><i>Flat rate</i> was changed from $'+ (parseInt(cached_data.price)/100).formatMoney() +' flat fee to $'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee</div>';
								rate_change = '<div><i>Hourly rate</i> was changed from $'+ (parseInt(cached_data.price)/100).formatMoney() +' per hour to $'+ (parseInt(jobType.price)/100).formatMoney() +' per hour</div>';

								return {
									text: price_change + rate_change + data
								};

							} else if(parseInt(jobType.price) !== parseInt(cached_data.price) && parseInt(jobType.rate) === parseInt(cached_data.rate)) {

								price_change = '<div><i>Flat rate</i> was changed from $'+ (parseInt(cached_data.price)/100).formatMoney() +' flat fee to $'+ (parseInt(jobType.price)/100).formatMoney() +' flat fee</div>';

								return {
									text: price_change + data
								};

							} else if(parseInt(jobType.price) === parseInt(cached_data.price) && parseInt(jobType.rate) !== parseInt(cached_data.rate)) {

								rate_change = '<div><i>Hourly rate</i> was changed from $'+ (parseInt(cached_data.rate)/100).formatMoney() +' per hour to $'+ (parseInt(jobType.rate)/100).formatMoney() +' per hour</div>';

								return {
									text: rate_change + data
								};

							} else {

								return {
									text: 'No changes were made'
								};

							}

							break;

					}

				}

			}

			if(!formData.permission && permissions.length > 0){
				sb.dom.alerts.alert('Warning', 'Please create a User Permission before trying to save.', 'warning');
				return;
			}

			if(formData.name.value === '') {

				sb.dom.alerts.alert(
					'Error',
					'Name field can not empty. Please name the job type to create it.',
					'error'
				);

				return;

			} else {

				jobTypeObj.name = formData.name.value;
				jobTypeObj.description = formData.description.value;
				jobTypeObj.vendor_id = +formData.vendor.value;
				jobTypeObj.chart_of_account = +formData.chart_of_account.value;
				jobTypeObj.price_type = formData.price_type.value;

				if(formData.manager_locations) {
					jobTypeObj.manager_locations = formData.manager_locations.value;
				} else {
					jobTypeObj.manager_locations = [];
				}

				jobTypeObj.ç = formData.can_be_billed.value;

				if(formData.permission)
					jobTypeObj.permission = +formData.permission.value;

				if(formData.surcharges) {
					jobTypeObj.surcharges = formData.surcharges.value;
				} else {
					jobTypeObj.surcharges = [];
				}

				if(formData.inclusive_tax) {
					jobTypeObj.inclusive_tax = formData.inclusive_tax.value;
				} else {
					jobTypeObj.inclusive_tax = [];
				}

				if(formData.exclusive_tax) {
					jobTypeObj.exclusive_tax = formData.exclusive_tax.value;
				} else {
					jobTypeObj.exclusive_tax = [];
				}

				switch(formData.price_type.value) {

					case '0':
					case 'flat':

						jobTypeObj.price = formData.price.value;

						break;

					case '1':
					case 'hourly':

						jobTypeObj.rate = formData.rate.value;
						jobTypeObj.min_hours = formData.min_hours.value;

						break;

					case '2':
					case 'non-billable':

						break;

					case '3':
					case 'flat_and_hourly':

						jobTypeObj.price = formData.price.value;
						jobTypeObj.rate = formData.rate.value;
						jobTypeObj.min_hours = formData.min_hours.value;
						jobTypeObj.max_flat_hours = formData.max_flat_hours.value;

						break;

					default:

				}

				if(obj !== undefined) {

					create = 'update';
					jobTypeObj.id = obj.id;

					jobType_payroll_cache.price_type = obj.price_type;
					jobType_payroll_cache.rate = obj.rate;
					jobType_payroll_cache.price = obj.price;
					jobType_payroll_cache.max_flat_hours = obj.max_flat_hours;

				}

				dom.segment.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {css: 'ui button green loading'});

				dom.segment.wrapper.body.cont.empty();

				dom.segment.wrapper.body.load_cont.makeNode('loader', 'loader', {});
				dom.segment.wrapper.body.load_cont.makeNode('load_text', 'text', {text: 'Processing form data ...', css: 'pda-text-center'});

				dom.segment.wrapper.patch();

				sb.data.db.obj[create]('inventory_service', jobTypeObj, function(newJobType) {

					if(newJobType) {

						sb.data.db.obj.create('groups'
							, {
								name:		newJobType.name
								, description: newJobType.description
								, job_type:	newJobType.id
								, group_type:	'JobType'
								, managers:	appConfig.headquarters.managers
								, parent:		appConfig.headquarters.id
								, tools: 		appConfig.jobtypeTools
							}
							, function(jtGroupObj){

								sb.data.db.obj.update(
									'inventory_service'
									, {
										id:newJobType.id
										, group_object:jtGroupObj.id
									}
									, function(res){ return true; }
								);

								return true;

							}
						);

						var noteObj = {
								type_id: newJobType.id,
								type: 'inventory_service',
								note: '',
								record_type: 'log',
								author: sb.data.cookie.get('uid'),
								notifyUsers: [],
								log_data: {
									type: create,
									objectName: newJobType.name,
									details: ''
								}
							};

						if(create === 'create') {

							noteObj.log_data.details = '<div><strong>Billing Type:</strong> '+ display_billingTypeName(newJobType.price_type) +'</div>'+
													   '<div><strong>Rate:</strong> '+ display_jobTypeRate({
														   obj: newJobType
													   }).text +'</div>';

							noteObj.note = '<div>A new job type: <strong>'+ newJobType.name +'</strong> has been created</div></br>' + noteObj.log_data.details;

						} else if(create === 'update') {

							noteObj.log_data.details = compare_payrollData({
								jobType: newJobType,
								cached_payrollData: jobType_payroll_cache
							}).text;

							noteObj.note = '<div>Job type: <strong>' + newJobType.name + '</strong> has been updated<div></br>' + noteObj.log_data.details;

						}

						sb.data.db.obj.create('notes', noteObj, function(newNote) {

							jobTypeSettings(dom, bp);

						});

					}

				}, 1);

			}

		}

		function getChartOfAccounts(coaCompId, callback) {

			if(coaCompId.chart_of_accounts_company){

				sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

					sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: coaCompId.chart_of_accounts_company}, function(accounts){

						callback({
							coaComps:coaComps,
							accounts:accounts
						});

					});

				});

			}else{

				sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

					if(coaComps.length > 0){

						sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:coaComps[0].id}, function(accounts){

							callback({
								coaComps:coaComps,
								accounts:accounts
							});

						});

					}else{

						callback({
							coaComps:coaComps,
							accounts:[]
						});

					}

				});

			}

		}

		function switch_formFields(value) {

			switch(value) {

				case '0':
				case 'flat':

					dom.segment.wrapper.body.cont.form.price.update({
						type: 'usd'
					});

					dom.segment.wrapper.body.cont.form.rate.update({
						type: 'hidden',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.min_hours.update({
						type: 'hidden',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.max_flat_hours.update({
						type: 'hidden',
						value: 0
					});

					break;

				case '2':
				case 'hourly':

					dom.segment.wrapper.body.cont.form.price.update({
						type: 'hidden',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.rate.update({
						type: 'usd'
					});

					dom.segment.wrapper.body.cont.form.min_hours.update({
						type: 'number',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.max_flat_hours.update({
						type: 'hidden',
						value: 0
					});

					break;

				case '3':
				case 'non_billable':

					$(dom.segment.wrapper.body.cont.form.price.selector).addClass('hidden');
					$(dom.segment.wrapper.body.cont.form.rate.selector).addClass('hidden');

					dom.segment.wrapper.body.cont.form.price.update({
						type: 'hidden',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.rate.update({
						type: 'hidden',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.min_hours.update({
						type: 'hidden',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.max_flat_hours.update({
						type: 'hidden',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.chart_of_account_company.update({
						type: 'hidden'
					});

					dom.segment.wrapper.body.cont.form.chart_of_account.update({
						type: 'hidden'
					});

					break;

				case '1':
				case 'flat_and_hourly':

					dom.segment.wrapper.body.cont.form.price.update({
						type: 'usd'
					});

					dom.segment.wrapper.body.cont.form.rate.update({
						type: 'usd'
					});

					dom.segment.wrapper.body.cont.form.min_hours.update({
						type: 'number',
						value: 0
					});

					dom.segment.wrapper.body.cont.form.max_flat_hours.update({
						type: 'number',
						value: 0
					});

					break;

			}

		}

		function update_CoAFormField(dom, form, value) {

			sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:+value}, function(accounts) {

				dom.segment.wrapper.body.cont.form.chart_of_account.update({
					options: _.map(accounts, function(company) {

						return {
							name: company.name,
							value: company.id
						};

					})
				});

			});

		}

		dom.empty();

		dom.patch();

		var crudSetup = {
				type: 'show-table',
				data: {
					domObj: dom,
					objectType: 'inventory_service',
					childObjs: 1,
					searchObjects: false,
					filters: false,
					download: false,
					navigation: false,
					headerButtons: {
						create:{
							name: '<i class="fa fa-plus"></i> New Job Type',
							css: 'pda-btn-green',
							domType: 'none',
							action: create_jobType.bind(this, dom)
						},
						reload: {
							name: 'Reload',
							css: 'pda-btn-blue',
							action: function() {}
						}
					},
					rowSelection: false,
					rowLink: {
						type: 'edit',
						header: function(obj) {},
						action: function(bp, domObject, obj){

							edit_jobType.call(this, bp, dom, obj);
						}
					},
					visibleCols: {
						name: 'Job Type',
					},
					cells: {
						name: function(obj) {
							return obj.name;
						},
						surcharges: function(obj) {

							if(!_.isEmpty(obj.surcharges)) {
								return ' ' + _.pluck(obj.surcharges, 'name');
							} else {
								return '<span class="text-muted"><i>N/A</i></span>';
							}


						}
					},
					data: function(paged, callback) {

						sb.data.db.obj.getAll('inventory_service', function(ret) {

							ret.data = _.sortBy(ret.data, 'name');

							callback(ret);

						}, 1, paged);

					}
				}
			};

		components.jobTypes.notify(crudSetup);

	}

	function jobTypeAvailabilitySettings(dom) {

    	function edit_jobTypeAvailability(bp, dom, obj) {

    		dom.empty();

    		dom.makeNode('wrapper', 'div', {css: 'ui basic segment'});

    		dom.wrapper.makeNode('jobType_type', 'div', {text: 'Job type: ' + obj.name, tag: 'h4'});

    		dom.wrapper.makeNode('cal_wrapper', 'div', {});

    		dom.patch();

    		sb.notify({
				type: 'start-schedule-availability',
				data: {
					domObj: dom.wrapper.cal_wrapper,
					obj: obj
				}
			});

		}

    	dom.empty();

    	dom.patch();

    	var crudSetup = {
				type: 'show-table',
				data: {
					domObj: dom,
					objectType: 'inventory_service',
					childObjs: 1,
					searchObjects: false,
					filters: false,
					download: false,
					navigation: false,
					headerButtons: {
						reload: {
							name: 'Reload',
							css: 'pda-btn-blue',
							action: function() {}
						}
					},
					rowSelection: false,
					rowLink: {
						type: 'edit',
						header: function(obj) {},
						action: function(bp, dom, obj){

                            edit_jobTypeAvailability.call(this, bp, dom, obj);

						}
					},
					visibleCols: {
						name: 'Job Type',
					},
					cells: {
						name: function(obj) {
							return obj.name;
						},
						surcharges: function(obj) {

							if(!_.isEmpty(obj.surcharges)) {
								return ' ' + _.pluck(obj.surcharges, 'name');
							} else {
								return '<span class="text-muted"><i>N/A</i></span>';
							}


						}
					},
					data: function(paged, callback) {

						sb.data.db.obj.getAll('inventory_service', function(ret) {

							ret.data = _.sortBy(ret.data, 'name');

							callback(ret);

						}, 1, paged);

					}
				}
			};

		components.jobTypesAvail.notify(crudSetup);

	}

	function userDocView(dom, staffObj, notifier){

		sb.data.db.obj.getAll('inventory_service', function(jobTypes){

			sb.data.db.obj.getAll('staff_paperwork_type', function(docs){

				var docTypes = docs;

				dom.empty();
				dom.makeNode('cards'
					, 'div'
					, {
						css: 'ui cards'
					}
				);

				modals = dom.makeNode('modals', 'modal', {});

				if(notifier){

					if(notifier == 'userDashboard'){

						if(Array.isArray(docTypes) && _.isEmpty(docTypes)){

							this.makeNode('header', 'text', {text: 'No Document Types in Staff Settings', size: 'small', css: 'pda-text-center'});

						} else {

							var staffDocs = [];

							_.each(docTypes, function(doc, i){

								if(checkDoc(doc.service_type, staffObj.service)){

									staffDocs.push(doc);

								}

							});

							if(staffDocs.length == 0){

								dom.makeNode('header', 'text', {text: 'No matched documents', size: 'small', css: 'pda-text-center'});

							} else {

								_.each(_.sortBy(staffDocs, 'name'), function(doc, i){

									dom.makeNode('p-' + i, 'div', {css: 'card'});

									dom.patch();

									docViewCard.call(this['p-' + i], doc, i, staffObj, jobTypes);

								}, dom);

							}

						}

						dom.patch();
					}

				}else{

					if(Array.isArray(docTypes) && _.isEmpty(docTypes)){

						dom.makeNode('header', 'text', {text: 'No Document Types in Staff Settings', size: 'small', css: 'pda-text-center'});

					} else {

						var staffDocs = [];

						_.each(docTypes, function(doc, i){

							if(checkDoc(doc.service_type, staffObj.service)){

								staffDocs.push(doc);

							}

						});

						if(staffDocs.length == 0){

							dom.makeNode('sp', 'lineBreak', {spaces: 2});
							dom.makeNode('header', 'div', {
								tag:'p'
								, text: 'No matched documents'
								, css: 'text-center'
							});


						} else {

							_.each(_.sortBy(staffDocs, 'name'), function(doc, i){

								this.makeNode('p-' + i, 'div', {css: 'card'});

								this.patch();

								docViewCard.call(this['p-' + i], doc, i, staffObj, jobTypes);

							}, dom.cards);

						}

					}
					dom.patch();
				}

			}, 1);

		});
	}

	function userViewSettings(dom){

		function createEditUserView(bp, dom, obj){

			var  formUI = {
					headerText:'Create',
					segColor:'blue'
				},
				formName = {
					name:{
						name:'name',
						type:'text',
						label:'Permission View\'s Name'
					}
				},
				formSelect = {
					everything:{
						name:'everything',
						label:'<b>ALLOW EVERYTHING?</b>',
						type:'select',
						options:[
							{
								name:'No',
								value:'no'
							},
							{
								name:'Yes',
								value:'yes'
							}
						],
						change:function(form, selected){

							var nameVal = formname.process().fields.name.value;

							///need to save value of 'Name' field if name given
							formname.name.update({value:nameVal});

							_.each(form.options , function(opt){

									///needed to save value of 'everything' selection
									if(opt.value == selected){
										opt.selected = true;
									}else{
										opt.selected = false;
									}

									formselect['everything'].update({options:form.options});

							});

							_.each(formSetup, function(prop){

								_.each(prop.options, function(opt){

									if(selected == 'yes'){
										opt.selected = true;
									}else{
										opt.selected = false;
									}

								});

									formObject[prop.name].update({options:formSetup[prop.name].options});

							});

							formObject.patch();
						}
					}
				},
				formSetup = {};

			if(obj && obj.hasOwnProperty('name')){
				formUI.headerText = 'Edit',
				formUI.segColor = 'orange',
				formName.name.value = obj.name;
			}

			///process form data then pass through callback to save
			function processPermissionForm(form, object, callback){

				var ret = {
						dbCall:'create',
						status:{
							type:'success',
							message:'You have created a Permission View.'
						},
						permissionsObj:{
							permissions:{}
						}
					};

				///due to layout of form, fn needed to gather data and format to save
				function sanitizeData(output, form){

					output.name = form.cont.segment.wrapper.body.formName.form.process().fields.name.value;

					var formData = form.cont.segment.wrapper.body.formCont.form.process().fields;

					var formDataUpd = _.omit(formData, function(v, k, form){
						///exclude these properties from processed form object
						return (k == 'everything' || k == 'selectAll');

					});

					_.map(formDataUpd, function(item, k){

						if(k == 'name'){


							this.name = _.property(['value'])(item);


						}else{


							this.permissions[k] = _.uniq(_.property(['value'])(item));


						}

						return;

					}, output);

					return output;

				}

				sanitizeData(ret.permissionsObj, form);

				if(object !== false && object != null){

					ret.dbCall = 'update';
					ret.status.type = 'success';
					ret.status.message = `You have updated ${ret.permissionsObj.name} Permission View`;
					ret.permissionsObj.id = object.id;

				}

				///form validation checks for name and view selections
				if(_.isEmpty(ret.permissionsObj.permissions)){

					ret.status.type = 'warning';
					ret.status.message = `Please select which views ${ret.permissionsObj.name} is allowed to see.`

				}

				if(_.isEmpty(ret.permissionsObj['name'])){

					ret.status.type = 'warning';
					ret.status.message = 'Please enter a name for your Permission View'

				}

				callback(ret);

			}

			dom.empty();

			dom.makeNode('cont', 'div', {css: 'sixteen wide column'});
			dom.cont.makeNode('segment', 'div', {css:`ui raised ${formUI.segColor} segment`});
			dom.cont.segment.makeNode('wrapper', 'div', {css: 'ui grid'});
			dom.cont.segment.wrapper.makeNode('head', 'div', {css:'centered row'});
			dom.cont.segment.wrapper.head.makeNode('col1', 'div', {css:'fifteen wide column'});
			dom.cont.segment.wrapper.head.col1.makeNode('title', 'headerText', {text: `${formUI.headerText}`});
			dom.cont.segment.wrapper.makeNode('body', 'div', {css: 'centered row'});

			dom.cont.segment.wrapper.body.makeNode('formName', 'div', {css: 'twelve wide column'});
			dom.cont.segment.wrapper.body.makeNode('formSelect', 'div', {css: 'three wide column'});

			dom.cont.segment.wrapper.body.makeNode('formCont', 'div', {css: 'fifteen wide column'});
			dom.cont.segment.wrapper.makeNode('btns', 'div', {css: 'centered row'});
			dom.cont.segment.wrapper.btns.makeNode('cont', 'div', {css: 'fifteen wide column'});
			dom.cont.segment.wrapper.btns.cont.makeNode('btnGroup', 'div', {css:'ui fluid buttons'});
			dom.cont.segment.wrapper.btns.cont.btnGroup.makeNode('back_btn', 'div', {text: 'Back', css: 'ui grey basic button'}).notify('click', {
				type:'staffComponent-run',
				data:{
					run:function(){

						userViewSettings(mainDom);

					}
				}
			}, sb.moduleId);

			if(obj && obj != null)
				dom.cont.segment.wrapper.btns.cont.btnGroup.makeNode('delete_btn', 'div', {css: 'ui red button', text: 'Delete'}).notify('click', {
					type: 'staffComponent-run',
					data: {
						run:function(o){

							sb.data.db.obj.erase('user_views', o.id, function(resp){

								if(resp){

									sb.dom.alerts.alert('Success', 'You have successfully deleted this permission view.', 'success');

									userViewSettings(mainDom);

								}else{

									sb.dom.alerts.alert('Error', 'Unable to delete this permission view at this time.', 'error');
								}

							});

						}.bind(null, obj)
					}
				}, sb.moduleId);;

			var saveBtn = dom.cont.segment.wrapper.btns.cont.btnGroup.makeNode('save_btn', 'div', {text: 'Save', css: 'ui green button'});


			///each over appConfigs allowed side nav components
			_.each(appConfig.navigation, function(nav){

				///exlude account-home & login because they are default
				if(nav.id != 'user-dashboard' && nav.id != 'login'){

					///exclude if display property not set or display= false (backwards compatibility)
					if(nav.display == undefined || nav.display != false){

						///build formSetup object to have nav items and their views as the options
						formSetup[nav.id] = {
								name:nav.id,
								label:'<div class="ui divider"></div>'+nav.title.toUpperCase(),
								type:'checkbox',
								options:[]
							};

						var navItems = _.reject(nav.views, function(view){
							return view.type === 'hqTool';
						});

						navItems = _.reject(navItems, function(view){
							return view.type === 'nav-item';
						});

						navItems = _.reject(navItems, function(view){
							return view.type === 'teamTool';
						});

						navItems = _.reject(navItems, function(view){
							return view.type === 'tool';
						});

						navItems = _.reject(navItems, function(view){
							return view.type === 'object-view';
						});

/*
						navItems = _.reject(navItems, function(view){
							return view.type === 'custom';
						});
*/

						navItems = _.reject(navItems, function(view){
							return view.type === 'quickAction';
						});

						///loop through app nav item view objects ('table', 'settings', etc.)
						_.each(navItems, function(view, i){

							///isUndefined to filter out any view objects that can be dynamically added('single-'+obj crud table views)
							if(_.isUndefined(view.removable)){

								///setup to preselect form options else build out blank form
								if(obj != null && obj.permissions[nav.id] != null){

									///if user object permissions contain nav item's view object select on form
									if(obj.permissions[nav.id].indexOf(view.id) > -1){

										formSetup[nav.id].options.push({
											name:view.id,
											label:view.title || view.id,
											value:view.id,
											checked:true
										});

									}else{

										formSetup[nav.id].options.push({
											name:view.id,
											label:view.title || view.id,
											value:view.id
										});

									}

								}else{

									formSetup[nav.id].options.push({
										name:view.id,
										label:view.title || view.id,
										value:view.id
									});

								}

							}

						});

					}

				}

			});

			var formname = dom.cont.segment.wrapper.body.formName.makeNode('form', 'form', formName);
			var formselect = dom.cont.segment.wrapper.body.formSelect.makeNode('form', 'form', formSelect);
			var formObject = dom.cont.segment.wrapper.body.formCont.makeNode('form', 'form', formSetup);

			saveBtn.notify('click', {
				type:'staffComponent-run',
				data:{
					run:function(obj){

						var domObj = this;

						domObj.cont.segment.wrapper.btns.cont.btnGroup.save_btn.loading();

						processPermissionForm(domObj, obj, function(output){

							if(output.status.type == 'warning'){

								setTimeout(function(){

									domObj.cont.segment.wrapper.btns.cont.btnGroup.save_btn.loading(false);

									sb.dom.alerts.alert('Warning', output.status.message, output.status.type);

								}, 250);

							}else{

								sb.data.db.obj[output.dbCall]('user_views', output.permissionsObj, function(updated){

									sb.dom.alerts.alert('Success!', output.status.message, output.status.type);

									userViewSettings(mainDom);

								});

							}

						});

					}.bind(dom, obj)
				}
			}, sb.moduleId);

			dom.patch();

		}

		dom.empty();
		dom.makeNode('cont', 'div', {css: 'ui grid'});
		dom.patch();

		var mainDom = dom;

		var crudSetup = {
				domObj:dom,
				objectType:'user_views',
				childObjs:1,
				searchObjects:false,
				settings:false,
				filters:false,
				download:false,
				headerButtons:{
					create:{
						name:'<i class="fa fa-plus"></i> New Permission',
						css:'pda-btn-green',
						domType:'full',
						action:function(bp, dom){
							createEditUserView.call(null, bp, dom);
						}
					}
				},
				rowSelection:false,
				calendar:false,
				home:false,
				rowLink:{
					type:'edit',
					header:function(obj){
						return obj.name;
					},
					action:createEditUserView
				},
				visibleCols:{
					name:'Name',
					menu_items:'Can View'
				},
				cells: {
					name:function(obj){
						return obj.name;
					},
					menu_items:function(obj){

						if(!_.isEmpty(_.omit(obj.permissions, ['login','account-home']))){

							var ret = '',
								count = 0;


							_.each(_.keys(obj.permissions), function(name){

								if(name != 'login' && name != 'account-home'){

									if(count > 0){

										ret += ', '+name.toUpperCase();

									}else{

										ret += name.toUpperCase();

									}

									count++;

								}

							});


							return ret;

						}else{

							return 'No permissions set';

						}

					}
				},
				data:function(paged, callback){

					sb.data.db.obj.getAll('user_views', function(dataList){

						callback(dataList);

					}, 1, paged);
				}
			}

		components.userviews.notify({
			type:'show-table',
			data:crudSetup
		});

	}

	function timeOffVestingSchedules(dom, obj){

		function editTemplate(bp, ui, obj){

			function goBack(){

				components.settingsTable.notify({
					type:'crud-table-back-to-table',
					data:{}
				});

				components.settingsTable.notify({
					type:'update-table',
					data:{}
				});

			}

			function process_form(form){

				var formData = form.process().fields;

				var ret = {
					name: formData.name.value,
					description: formData.description.value,
					initial_qty: parseInt(formData.initial_qty.value),
					max_qty: parseInt(formData.max_qty.value),
					vesting_start_date: formData.vesting_start_date.value,
					vesting_period_type: formData.vesting_period_type.value,
					qty_vested_per_period: parseInt(formData.qty_vested_per_period.value),
					is_template:1
				};

				if(formData.does_carry_over && _.contains(formData.does_carry_over.value, 'yes')){
					ret.does_carry_over = 'yes';
				}else{
					ret.does_carry_over = 'no';
				}

				// validate
				if(_.isEmpty(ret.name)){

					sb.dom.alerts.alert('Incomplete', 'Please enter a name for this template.', 'warning');
					return false;

				}
				if(isNaN(ret.initial_qty) || ret.initial_qty < 0){

					sb.dom.alerts.alert('Incomplete', 'Initial Quantity must be a non-negative number.', 'warning');
					return false;

				}
				if(isNaN(ret.max_qty) || ret.max_qty < 0){

					sb.dom.alerts.alert('Incomplete', 'Quantity Cap must be a non-negative number.', 'warning');
					return false;

				}
				if(isNaN(ret.qty_vested_per_period) || ret.qty_vested_per_period < 0){

					sb.dom.alerts.alert('Incomplete', 'Days vested per period must be a non-negative number.', 'warning');
					return false;

				}

				return ret;

			}

			ui.empty();
			ui.patch();

			var create = true;
			if(obj.object_bp_type === 'time_off_vesting_schedule'){
				create = false;
			}

			var formSetup = {
				name:{
					name:'name',
					type:'text',
					label:'Name'
				},
				description:{
					name:'description',
					type:'textbox',
					label:'Description'
				},
				initial_qty:{
					name:'initial_qty',
					type:'number',
					label:'Initial Quantity'
				},
				max_qty:{
					name:'max_qty',
					type:'number',
					label:'Quantity Cap'
				},
				vesting_start_date:{
					name:'vesting_start_date',
					type:'select',
					label:'Vesting begins after..',
					options:_.map(bp.vesting_start_date.options, function(name, value){

						return {
							name:name,
							value:value
						}

					})
				},
				vesting_period_type:{
					name:'vesting_period_type',
					type:'select',
					label:'Vesting period',
					options:_.map(bp.vesting_period_type.options, function(name, value){

						return {
							name:name,
							value:value
						}

					})
				},
				qty_vested_per_period:{
					name:'qty_vested_per_period',
					type:'number',
					label:'Days vested per period'
				},
				does_carry_over:{
					name:'does_carry_over',
					type:'check',
					label:'Do vacation days carry over?',
					options:[{
						name:'does_carry_over',
						value:'yes',
						label:'Yes'
					}]
				}
			};

			if(!create){

				_.each(formSetup, function(field, key){

					formSetup[key].value = obj[key];
					if(field.type === 'check'){
						formSetup[key].value = [obj[key]];
					}

				});

			}

			ui.makeNode('main', 'div', {css:'ui sixteen wide column'});

			ui.main.makeNode('form', 'form', formSetup);

			// actions
			ui.main.makeNode('br', 'div', {text:'<br />'});
			ui.main.makeNode('actions', 'div', {css:'ui right floated buttons'})
				.makeNode('back', 'div', {css:'ui basic red button', text:'Cancel', tag:'button'})
				.notify('click', {
					type:'staffComponent-run',
					data:{
						run:goBack
					}
				}, sb.moduleId);

			var btnText = 'Create';
			if(!create){
				btnText = 'Update';
			}
			ui.main.actions.makeNode('update', 'div', {css:'ui green button', text:btnText, tag:'button'})
				.notify('click', {
					type:'staffComponent-run',
					data:{
						run:function(){


							var newObj = process_form(ui.main.form);

							if(newObj){

								ui.main.actions.update.loading();

								if(create){

									sb.data.db.obj.create('time_off_vesting_schedule', newObj, function(response){

										if(response){

											goBack();

										}else{

											ui.main.actions.update.loading(false);

											sb.dom.alerts.alert('Error', 'Something went wrong--please refresh and try again.', 'error');

										}

									});

								}else{

									newObj.id = obj.id;
									sb.data.db.obj.update('time_off_vesting_schedule', newObj, function(response){

										if(response){

											goBack();

										}else{

											ui.main.actions.update.loading(false);

											sb.dom.alerts.alert('Error', 'Something went wrong--please refresh and try again.', 'error');

										}

									});

								}

							}

						}
					}
				}, sb.moduleId);

			ui.patch();

		}

		var tableSetup = {
			domObj:dom,
			tableTitle:'Time off vesting schedule templates',
			objectType:'time_off_vesting_schedule',
			rowLink:{
				type:'edit',
				header:function(obj){
					return obj.name;
				},
				action:function(bp, dom, obj){
					editTemplate(bp, dom, obj);
				}
			},
			searchObjects:false,
			navigation:false,
			headerButtons:{
				create:{
					name: '<i class="fa fa-plus"></i> New Template',
					css: 'green',
					domType: 'full',
					action:editTemplate
				},
				reload: {
					name: 'Reload',
					css: 'pda-btn-blue',
					action: function() {}
				}
			},
			visibleCols:{
				name:'Name'
			},
			cells:{
				name:function(obj){ return obj.name; }
			},
			data:function(page, callback){

				sb.data.db.obj.getWhere('time_off_vesting_schedule', {
					is_template:1,
					paged:page
				}, function(data){

					callback(data);

				});

			},
			rowSelection:true,
			multiSelectButtons:{
				erase:{
					name:'<i class="fa fa-trash-o"></i> Delete',
					css:'pda-btn-red',
					domType:'none',
					action:function(objs){

						sb.dom.alerts.ask({
							title: 'Are you sure?',
							text: ''
						}, function(resp){

							if(resp){
								sb.data.db.obj.erase('time_off_vesting_schedule', _.pluck(objs, 'id'), function(){

									sb.dom.alerts.alert('Deleted!', '', 'success');
									components.settingsTable.notify({
										type:'update-table',
										data:{}
									});

								});
							}

						});

					}
				}
			}
		};

		if(components.settingsTable){
			components.settingsTable.destroy();
		}
		components.settingsTable = sb.createComponent('crud-table');
		components.settingsTable.notify({
			type:'show-table',
			data:tableSetup
		});

	}

	function parseStaffForPaperwork(data, callback, query){

		var ids = _.chain(data.data)
			.pluck('service')
			.flatten()
			.pluck('id')
			.uniq()
			.value();

		if (_.isEmpty(data)) {

			callback({ data: [] });

		} else {

			sb.data.db.obj.getWhere('staff_paperwork_type',
				{
					service_type:{
						type:'intersect',
						value:ids
					},
					need_file_upload:'Yes'
				}, function(doc_types){

				sb.data.db.obj.getWhere('staff_paperwork',
					{
						staff:{
							type:'or',
							values:_.map(data.data, function(staff){

								return parseInt(staff.id);
							})
						}
					}, function(docs){

					var sortedDocs = _.groupBy(docs, 'staff');

					_.map(data.data, function(st){

						st.staff_paperwork = [];
						st.staff_paperwork_status = [];

						_.map(doc_types, function(type){

							if(sortedDocs[st.id]){

								_.each(sortedDocs[st.id], function(doc){

									if(doc.document_type == type.id)
										st.staff_paperwork.push(doc);
								});

							}

							_.each(type.service_type, function(servApply){

								if(_.contains(_.map(st.service, function(ser){ return ser.id; }), servApply)){

									if(!_.contains(st.staff_paperwork_status, type))
										st.staff_paperwork_status.push(type);
								}
							});

						});

						return st;
					});

					callback(data);

				});
			});
		}
	}

	// collections

	function staffListCollection (dom, state, draw, mainDom) {

		var setupLayer = 'hq';

		if (state.pageObject.group_type == 'JobType')
			setupLayer = 'jt';

		sb.notify({
			type:'show-collection',
			data:{
				actions:{
					create:function(ui, newObj, onComplete){

						createNewState(ui, newObj, draw, function(newUser){

							sb.notify({
								type:'app-navigate-to',
								data:{
									itemId:'users',
									viewId:{
										viewState:{
											obj: newUser
										}
									}
								}
							});

						});

					},
					copy: false,
					view:true
				},
				domObj:dom,
				layer: setupLayer,
				templates: false,
				fields:{
					image:{
						type:'image',
						title:'',
						view:function(ui, obj){

							if(obj.profile_image.id){

								ui.makeNode('avatar-'+obj.id, 'div', {css:'ui avatar image', tag:'img', src:sb.data.files.getURL(obj.profile_image)})

							}else{

								ui.makeNode(obj.id +'-icon', 'div', {
									css:'ui user icon',
									tag:'i'
								});
							}

						}
					},
					fname:{
						title:'First Name',
						type:'title',
						isSearchable:true
					},
					lname:{
						title:'Last Name',
						type:'title',
						isSearchable:true
					},
					enabled:{
						type:'label',
						title:'User Status',
						view:function(ui, user){

							var enabled = {
								text:'Disabled',
								css:'ui label'
							};

							if(user.enabled){
								enabled.text = 'Enabled',
								enabled.css = 'ui teal label'
							}

							ui.makeNode('label', 'div', {css: enabled.css, text:enabled.text});

						}
					},
					document_status:{
						title:'Document Status',
						view:function(ui, user){

							var cellDom = ui;

							var docStatus = {
								css:'ui label',
								text:'No Service Assigned'
							};

							if(user.hasOwnProperty('service') && !_.isEmpty(user.service)){

								docLabeler.call(docStatus, user);

							}

							cellDom.makeNode('label', 'div', {css: docStatus.css, text:docStatus.text});

							cellDom.patch();

						}
					}
				},
				groupings:{
					service:'Service',
					enabled:'Status'
				},
				objectType:'users',
				singleView:{
					view:function(ui, user, draw){

						if(user.profile_image.id){

							ui.makeNode('image', 'div', {tag:'img', css:'ui small circular centered image', src:sb.data.files.getURL(user.profile_image)});

						}

						var nickName = '';
						if(user.nick_name){
							nickName = '<br /><small><small><i>Nickname: '+ user.nick_name +'</i></small></small>';
						}

						ui.makeNode('title', 'div', {text:user.fname +' '+ user.lname + nickName, css:'ui huge centered header'});
						ui.makeNode('email', 'div', {text:user.email, css:'ui centered header'});
						ui.makeNode('phone', 'div', {text:sb.dom.formatPhone(user.phone) , css:'ui centered header'});
						ui.patch();

					},
					select:3
				},
				parseData:function(data, callback, query){

					parseStaffForPaperwork(data, callback, query);

				},
				where:{
					childObjs:{
						fname:true,
						lname:true,
						enabled:true,
						profile_image:true,
						service:true
					}
				}
			}
		});

	}

	// single user

	function viewSingleStaff(obj, dom, state){

		var userObj = obj;
		var mobile = false;
		var defaultCollectionsView = 'list';
		var activeTabCss = 'active blue item';
		var tabText = {
			applications:	'<span style="padding:0px 12px;"><i class="th icon"></i>' + userObj.fname + '\'s Apps</span>'
			, tasks: 		'<span style="padding:0px 12px;"><i class="tasks icon"></i>' + userObj.fname + '\'s Tasks</span>'
			, projects: 	'<span style="padding:0px 12px;"><i class="folder open icon"></i>' + userObj.fname + '\'s Projects</span>'
			, teams: 		'<span style="padding:0px 12px;"><i class="users icon"></i>' + userObj.fname + '\'s Teams</span>'
			, docs: 		'<span style="padding:0px 12px;"><i class="copy icon"></i>' + userObj.fname + '\'s Documents</span>'
			, availability: '<span style="padding:0px 12px;"><i class="calendar icon"></i>' + userObj.fname + '\'s Availability</span>'
			//, settings:		'<span style="padding:0px 12px;"><i class="calendar icon"></i>' + userObj.fname + '\'s User settings</span>'
		};
		var menuCSS = (isCurrent(userObj) == false)
			? 'ui secondary pointing five item menu'
			: 'ui secondary pointing four item menu';

		if (isEnabled(userObj) && isCurrent(userObj)){
			tabText.applications =	'<i class="th icon"></i> My Apps';
			tabText.tasks = 		'<i class="tasks icon"></i> My Tasks';
			tabText.projects = 		'<i class="folder open icon"></i> My Projects';
			tabText.teams = 		'<i class="users icon"></i> My Teams';
			tabText.docs = 		'<i class="copy icon"></i> My Documents';
			tabText.availability =   '<i class="calendar icon"></i> My Availability';
			//tabText.settings	= '<i class="calendar icon"></i> My Settings';
		}

		if(isMobile())
			mobile = true;

		function dashboard (ui, user) {

			function setupUsersMyStuff (userId, callback){

				sb.data.db.obj.getWhere('groups', {
					user:userId,
					group_type:'MyStuff'
				}, function(myStuffGroup){

					if(myStuffGroup.length > 0){

						callback(myStuffGroup[0]);

					}else{

						var tools = [];

						/*
_.each(appConfig.myStuffTools, function(tool, toolOrder){

							tools.push({
								allowed_users:[userId],
								system_name:tool.id,
								display_name:tool.name,
								is_archieved:0,
								order:toolOrder,
								added_by:+sb.data.cookie.get('uid'),
								added_on:moment(),
								settings:{},
								box_color:tool.icon.color
							});

						});
*/

						sb.data.db.obj.getById('users', userId, function(user){

							sb.data.db.obj.create('groups', {
								group_type:'MyStuff',
								user:userId,
								name:user.fname +' '+ user.lname,
								tools:tools
							}, function(myStuffGroup){

								callback(myStuffGroup);

							});

						});

					}

				});

			}

			setupUsersMyStuff (user.id, function(myStuffGroup){

				state.myStuff = myStuffGroup;

				sb.notify({
					type:'show-dashboard'
					, data:{
						dom:ui
						, state:state
						, draw:function(setup){

							if (setup.dom) {
								setup.dom.patch();
								if (setup.hasOwnProperty('after') && typeof setup.after == 'function')
									setup.after(setup.dom);
							}

						}
					}
				});


			});


		}

		function user (ui, user) {

			ui.empty();
			ui.makeNode('usmenu', 'div'
				, {
					css: 'ui secondary stackable five item menu'
				}
			)
			ui.makeNode('cont'
				, 'div'
				, {
					css: 	'basic segment animated fadeIn'
					, style:  isMobile() ? 'padding-top:0px;' : 'padding-top:40px;'
				}
			);

			ui.usmenu.makeNode('teams', 'div'
				, {
					css: 'active item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;">' + tabText.teams + '</span>'

				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.usmenu, ui.usmenu.teams);
						teams(ui.cont, userObj);

					}
				}
			}, sb.moduleId);

			ui.usmenu.makeNode('projects', 'div'
				, {
					css: 'item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;">' + tabText.projects + '</span>'
				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.usmenu, ui.usmenu.projects);
						projects(ui.cont, userObj);

					}
				}
			}, sb.moduleId);

			ui.usmenu.makeNode('tasks', 'div'
				, {
					css: 'item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;">' + tabText.tasks + '</span>'
				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.usmenu, ui.usmenu.tasks);
						tasks(ui.cont, userObj);

					}
				}
			}, sb.moduleId);

			ui.usmenu.makeNode('documents', 'div'
				, {
					css: 'item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;">' + tabText.docs + '</span>'
				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.usmenu, ui.usmenu.documents);
						documents(ui.cont, userObj);

					}
				}
			}, sb.moduleId);

			ui.usmenu.makeNode('availability', 'div'
				, {
					css: 'item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;">' + tabText.availability + '</span>'
				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.usmenu, ui.usmenu.availability);
						availView(ui.cont, userObj);

					}
				}
			}, sb.moduleId);

			teams(ui.cont, user);

			ui.patch();
		}

		function teams (ui, user) {

			sb.notify({
				type:'build-teams-collection'
				, data: {
					dom:ui
					, state:state
					, draw: function(){}
					, mainDom: ui
					, options: {
						userId:user.id
					}
				}
			});

		}

		function projects (ui, user) {

			sb.notify({
				type:'build-projects-collection'
				, data: {
					dom:ui
					, state:state
					, draw: function(){}
					, mainDom: ui
					, options: {
						userId:user.id
					}
				}
			});

		}

		function tasks (ui, user) {

			sb.notify({
				type:'tasks-build-collection'
				, data: {
					dom:ui
					, state:state
					, draw: function(){}
					, mainDom: ui
					, options: {
						userId:user.id
					}
				}
			});

		}

		function documents (ui, user) {

			sb.notify({
				type:'build-documents-collection'
				, data: {
					dom:ui
					, state:state
					, draw: function(){}
					, mainDom: ui
					, options: {
						userId:user.id
					}
				}
			});

		}

		function availView(ui, user) {

			ui.empty();

			ui.makeNode('wrapper', 'div', {css: 'animated fadeIn'});

			ui.patch();

			sb.notify({
				type: 'start-schedule-availability',
				data: {
					domObj: ui.wrapper,
					obj: user
				}
			});

		}
		

		function admin (ui, user) {

			ui.empty();
			ui.makeNode('hrmenu', 'div'
				, {
					css: 'ui secondary stackable four item menu'
				}
			)
			reqDocumentsTab = ui.makeNode('cont'
				, 'div'
				, {
					css: 	'basic segment animated fadeIn'
					, style:  isMobile() ? 'padding-top:0px;' : 'padding-top:40px;'
				}
			);

			ui.hrmenu.makeNode('contact', 'div'
				, {
					css: 'active item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;"> <i class="address card outline icon"></i> Contact Information</span>'

				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.hrmenu, ui.hrmenu.contact);
						contact(ui.cont, userObj);

					}
				}
			}, sb.moduleId);

			ui.hrmenu.makeNode('reqdoc', 'div'
				, {
					css: 'item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;"> <i class="file alternate outline icon"></i> Required Documents</span>'

				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.hrmenu, ui.hrmenu.reqdoc);
						req_docs(reqDocumentsTab, userObj);

					}
				}
			}, sb.moduleId);

			ui.hrmenu.makeNode('payroll', 'div'
				, {
					css: 'item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;"> <i class="money bill alternate outline icon"></i> Payroll </span>'
				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.hrmenu, ui.hrmenu.payroll);
						payroll(ui.cont, userObj);

					}
				}
			}, sb.moduleId);

			ui.hrmenu.makeNode('notes', 'div'
				, {
					css: 'item'
					, tag: 'a'
					, text: '<span style="padding:0px 12px;"> <i class="sticky note outline icon"></i> Notes </span>'
				}
			).notify('click', {
				type: 'staffComponent-run'
				, data: {
					run: function(){

						updateNavBtn(ui.hrmenu, ui.hrmenu.notes);
						notes(ui.cont, userObj);

					}
				}
			}, sb.moduleId);

			contact (ui.cont, user);

			ui.patch();
		}

		function docTags(ui, user) {

			ui.empty();

			ui.makeNode('wrapper', 'div', {});

			ui.wrapper.makeNode(
				'link'
				, 'div'
				, {}
			).makeNode(
				'label'
				, 'div'
				, {
					tag: 'h5'
					, text: 'Link'
					, css: 'ui header'
				}
			);

			sb.notify ({
				type: 'view-field'
				, data: {
					type: 			'url'
					, property: 	'doc_link'
					, obj:			user
					, options: 	{
						edit: 		true
						, editing: 	true
						, commitChanges: true
					}
					, ui: ui.wrapper.link.makeNode('val', 'div', {})
				}
			});

			ui.wrapper.makeNode(
				'br'
				, 'div'
				, {
					text: '<br />'
					, css: 'ui clearing divider'
				}
			);

			ui.wrapper.makeNode(
				'signature'
				, 'div'
				, {}
			);

			sb.notify ({
				type: 'view-field'
				, data: {
					type: 			'detail'
					, property: 	'doc_signature'
					, obj:			user
					, options: 	{
						//edit: true
						//, editing: true
						//, commitUpdates: false
						useMedium: true
					}
					, ui: ui.wrapper.signature.makeNode('val', 'div', {})
				}
			});

			ui.patch();
			ui.wrapper.signature.patch();

		}

		function contact(ui, user) {

			ui.empty();
			ui.patch();

			var contactViewObj = {
				body: {
					type:	'div'
					, css: 	'ui stackable grid'
					, content: {
						css:'ui row'
						, left: {
							type: 	'column'
							, w: 	8
							, style: isMobile() ? 'padding:0px !important;' : ''
							, content: {
								nick_name: {
									type:		'plain-text'
									, fieldName: 	'nick_name'
									, label: 		'Nickname'
								}
								, work_email: {
									type:		'plain-text'
									, fieldName: 	'work_email'
									, label: 		'Secondary Email'
								}
								, phone: {
									type:		'plain-text'
									, fieldName: 	'phone'
									, label: 		'Primary Phone'
								}
								, work_phone: {
									type:		'plain-text'
									, fieldName: 	'work_phone'
									, label: 		'Seconday Phone'
								}
							}
						}
						, right: {
							type: 	'column'
							, w: 	8
							, style: isMobile() ? 'padding:0px !important;' : ''
							, content: {
								contact: {
									type:		'view'
									, view: function () {

									}
								}
							}
						}

					}
				}
			};

			sb.notify({
				type: 'view-page'
				, data: {
					ui: ui
					, page: contactViewObj
					, state: {
						pageObject: user
					}
				}
			});

		}

		function req_docs (ui, user) {

			if (!_.isEmpty(ui)) {

				ui.empty();
				userDocView(ui, user);

			}

			return;

		}

		function payroll (ui, user) {

			ui.empty();
			ui.patch();

			var payrollViewObj = {
				viewBody: {
					type:	'div'
					, css: 	'ui stackable grid'
					, content: {
						css: 	'ui row'
						, hire: {
							type: 	'column'
							, w: 	8
							, style: isMobile() ? 'padding:0px !important;' : ''
							, content: {
								hire_date: {
									type:		'date'
									, fieldName: 	'hire_date'
									, label: 		'Hire Date'
									, edit: 		true
								}
							}
						}
						, termination: {
							type: 	'column'
							, w: 	8
							, style: isMobile() ? 'padding:0px !important;' : ''
							, content: {
								termination_date: {
									type: 		'date'
									, fieldName: 	'termination_date'
									, label: 		'Termination Date'
									, edit: 		true
								}
							}
						}
						, payroll: {
							type: 	'column'
							, w: 	16
							, style: 	isMobile() ? 'padding:0px !important;' : ''
							, content: {
								css: 'field'
								, dash: {
									type:	'view'
									, view:	function(dom, user, state){

										sb.notify({
											type: 'payroll-single-staff-start',
											data: {
												domObj: dom,
												staffObj: user
											}
										});

									}
								}
							}
						}
					}
				}
			};

			sb.notify({
				type: 'view-page'
				, data: {
					ui: ui
					, page: payrollViewObj
					, state: {
						pageObject: user
					}
				}
			});

		}

		function notes (ui, user) {

			ui.empty();

			ui.patch();

			sb.notify({
				type: 'show-note-list-box',
				data: {
					domObj: ui,
					objectIds:[user.id],
					objectId:user.id,
					headerText: 'Notes',
					headerSize: 'x-small',
					collapse: 'closed'
				}
			});
		}
		

		function updateNavBtn(menu, selected){

			_.each(menu, function(menuItem){
				if(menuItem.hasOwnProperty('removeClass')){
					menuItem.removeClass('active');
				}
			});

			selected.addClass('active');

		}

		dom.empty();

		dom.makeNode('navBtnGroup', 'div', {css: menuCSS});

		dom.makeNode('cont'
			, 'div'
			, {
				css: 'basic segment animated fadeIn'
				, style: isMobile() ? 'min-height:200px;' : 'min-height:200px; padding-top:35px;'
			}
		);

		if (isCurrent(userObj) == false) {

			dom.navBtnGroup.makeNode('dashboard', 'div'
				, {
					tag:'a'
					, css: activeTabCss
					, text: '<span style="padding:0px 12px;"><i class="th large icon"></i>'+ userObj.fname + '\'s Dashboard</span>'
				}
			).notify('click', {
				type: 'staffComponent-run',
				data: {
					run: function(){

						updateNavBtn(dom.navBtnGroup, dom.navBtnGroup.dashboard);
						dom.viewUsersDash(userObj);

					}
				}
			}, sb.moduleId);

		}

		dom.navBtnGroup.makeNode('user', 'div'
			, {
				tag:'a'
				, css: (isCurrent(userObj) == false) ? 'blue item' : 'active blue item'
				, text: '<span style="padding:0px 12px;">' + tabText.applications + '</span>'
			}
		).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function(){

					updateNavBtn(dom.navBtnGroup, dom.navBtnGroup.user);
					dom.viewUserApps(userObj);

				}
			}
		}, sb.moduleId);

		dom.navBtnGroup.makeNode('admin', 'div'
			, {
				tag:		'a'
				, css: 	'blue item'
				, text: 	'<span style="padding:0px 12px;"> <i class="globe icon"></i> HR Resources </span>'
			}
		).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function(){

					updateNavBtn(dom.navBtnGroup, dom.navBtnGroup.admin);
					dom.viewAdmin(userObj);

				}
			}
		}, sb.moduleId);

		dom.navBtnGroup.makeNode('docTags', 'div'
			, {
				tag:		'a'
				, css: 	'blue item'
				, text: 	'<span style="padding:0px 12px;"> <i class="file icon"></i> Document Tags </span>'
			}
		).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function(){

					updateNavBtn(dom.navBtnGroup, dom.navBtnGroup.docTags);
					docTags(dom.cont, userObj);

				}
			}
		}, sb.moduleId);
		
		dom.navBtnGroup.makeNode('userSettings', 'div'
			, {
				tag:		'a'
				, css: 	'blue item'
				, text: 	'<span style="padding:0px 12px;"> <i class="cogs icon"></i> User Settings </span>'
			}
		).notify('click', {
			type: 'staffComponent-run',
			data: {
				run: function(){
					
					updateNavBtn(dom.navBtnGroup, dom.navBtnGroup.userSettings);
					userSettings(dom.cont, userObj);

				}
			}
		}, sb.moduleId);

		dom.viewUsersDash = dashboard.bind(null, dom.cont);
		dom.viewUserApps = user.bind(null, dom.cont);
		dom.viewAdmin = admin.bind(null, dom.cont);

		if (isCurrent(userObj) == false) {

			dom.viewUsersDash(userObj);

		} else {

			dom.viewUserApps(userObj);

		}

		dom.patch();

		viewSingleStaff.req_docs = req_docs;

	}

	// util

	function isMobile(){ return ($(window).width() <= 768) }

	function isEnabled(userObject){ return !!userObject.enabled; }

	function isCurrent(userObject){ return (+sb.data.cookie.userId == userObject.id); }

	function profileImage (ui, currentUser) {

		var profileImg = currentUser.profile_image;

		if(profileImg !== null && profileImg !== undefined && profileImg.loc != "//") {

			profileImg = '<img class="ui small centered circular image" src="'+ sb.data.files.getURL(currentUser.profile_image) +'">';

		} else {

			profileImg = '<i class="massive user circle icon"></i>';

		}

		ui.makeNode('img', 'div', {
			text: profileImg
			, style: isMobile() ? '' : 'padding-top:12px; padding-bottom:12px;'
		});

		ui.patch();

	}

	function profileColor (ui, currentUser) {

		if(currentUser.enabled){

			var labelColor = currentUser.color || 'grey';

			ui.empty();
			ui.makeNode('field', 'div', {css:'field'});
			ui.field.makeNode('user-tag'
				, 'div'
				, {
					css: 'ui '+ labelColor +' label tag'
					, text:'User Tag Color'
				}
			);

			sb.notify({
				type: 'view-field',
				data: {
					type: 		'color-picker'
					, property: 	'color'
					, obj: 		currentUser
					, options: 	{
						onUpdate:profileColor.bind(null, ui)
					}
					, ui: 		ui.field
				}
			});

			ui.patch();

		} else {

			return;

		}

	}

	function profileDetails (ui, currentUser) {

		if(_.isEmpty(currentUser.email))
			currentUser.email = 'No email saved';

		if(_.isEmpty(currentUser.phone))
			currentUser.phone = 'No number saved';

		if(_.isEmpty(currentUser.hire_date))
			currentUser.hire_date = 'Not selected';

		ui.makeNode('llist', 'div', {css: 'ui large list'});

		ui.makeNode('servlist', 'div', {css: 'ui large bulleted list'});

		_.each(currentUser.service, function(job){

			this.makeNode('job-'+job.id, 'div', {css: 'item', text: job.name});

		}, ui.servlist);

		ui.makeNode('baselist', 'div', {css: 'ui large bulleted list'});

		_.each(currentUser.base, function(base){

			this.makeNode('base-'+base.id, 'div', {css: 'item', text: `${base.name}, <em>${base.state}</em>`});


		}, ui.baselist);

		ui.patch();

	}

	function profileEmailParse (obj, fieldName, val, cb) {

		var parsedVal = val.trim();

		if(_.isEmpty(parsedVal)){
			sb.dom.alerts.alert('A primary email is required.', '', 'warning');
			return;
		}

		sb.data.db.obj.update(obj.object_bp_type
			, {
				id: 		obj.id
				, email:  parsedVal
			}
			, function(res){

				if(res)
					cb(res);
			}
		);

	}

	function profileStatus (ui, currentUser) {

		ui.makeNode('profStat'
			, 'div'
			, {
				css: 'field'
			}
		);

		ui.patch();

		sb.notify({
			type:'show-user-account-info-field-view'
			, data:{
				domObj: ui.profStat
				, userObj:currentUser
			}
		});

	}

	function profileTags (ui, currentUser, state) {

		ui.makeNode('profTags'
			, 'div'
			, {
				css: 'field'
				, style: isMobile() ? '' : 'padding-top:14px;'
			}
		);

		ui.patch();

		components.tags.notify({
			type: 'object-tag-view',
			data: {
				domObj: ui.profTags,
				objectType: 'users',
				objectId: currentUser.id
			}
		});

	}
	
	// Used in other components
	function userSettings(ui, user) {

		ui.empty();
		
		ui.makeNode('seg', 'div', {
			css: 'ui basic segment'
		});
		
		ui.seg.makeNode('notifySec', 'div', {});
				
		ui.seg.notifySec.makeNode('title', 'div', {
			css: 'ui header'
			, text: '<i class="bell icon"></i> Notifications'
		});
		
		ui.seg.notifySec.makeNode('cont','div',{});
		
		ui.seg.notifySec.makeNode('divider1', 'div', {
			css: 'ui divider'
		});
		
		ui.seg.makeNode('break','div',{text:'<br /><br />'});
		
		ui.seg.makeNode('homepageSec', 'div', {});
		
		ui.seg.homepageSec.makeNode('title', 'div', {
			css: 'ui header'
			, text: '<i class="sign in alternate icon"></i> Login'
		});
		
		ui.seg.homepageSec.makeNode('cont','div',{});
		
		ui.seg.homepageSec.makeNode('divider1', 'div', {
			css: 'ui divider'
		});

		if (user.canBeNotified === undefined) {
			
			user.canBeNotified = true;
			
		}
		
		ui.patch();

		sb.notify({
			type: 'view-field',
			data: {
				type: 		'toggle'
				, property: 	'canBeNotified'
				, obj: 		user
				, options: 	{
					edit: true
					, editing: true
					, includeLabel: true
					, label: 'Send emails with notifications?'
					, commitUpdates: true
					, fireFieldUpdated: false
				}
				, ui: 		ui.seg.notifySec.cont
			}
		});

		sb.notify({
			type: 'view-field',
			data: {
				type: 		'toggle'
				, property: 	'go_to_hq'
				, obj: 		user
				, options: 	{
					edit: true
					, editing: true
					, includeLabel: true
					, label: 'Go directly to the HQ after logging in?'
					, commitUpdates: true
					, fireFieldUpdated: false
				}
				, ui: 		ui.seg.homepageSec.cont
			}
		});	
		
		
		
	}

	return {

		init: function(){

			if(!components.userviews){
				components.userviews = sb.createComponent('crud-table');
			}

			components.tags = sb.createComponent('tags');
			components.jobTypes = sb.createComponent('crud-table');
			components.jobTypesAvail = sb.createComponent('crud-table');
			components.locationsTable = sb.createComponent('crud-table');
			components.docTypes = sb.createComponent('crud-table');

			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						moduleId: sb.moduleId,
						id:'staff',
						title:'Staff',
						icon:'<i class="fa fa-users"></i>',
						views:[
							///BIN staffList comp table
							{
								id:'table',
								type:'table',
								title:'All Staff',
								icon:'<i class="fa fa-th-list"></i>',
								display:false,
								setup:{
									objectType:'users',
									childObjs:1,
									tableTitle: '<i class="fa fa-users"></i> Staff',
									searchObjects:[
										{
											name:'First Name',
											value:'fname'
										},
										{
											name:'Last Name',
											value:'lname'
										},
										{
											name:'Email Address',
											value:'email'
										},
										{
											name:'Phone',
											value:'phone'
										}

									],
									filters:function(callback){

										sb.data.db.obj.getAll('inventory_service', function(jobTypes){

											var filterTypes = [];

											_.each(jobTypes, function(jt){

												filterTypes.push({
													name: 'service',
													label: jt.name,
													value: jt.id,
													checked: false
												});

											});

											callback({
												service: {
													name: 'Filter By',
													type: 'checkbox',
													field: 'service',
													options: filterTypes
												},
												enabled:{
													name: 'Enabled',
													type: 'select',
													field: 'enabled',
													options:[
														{
															name:'Inactive',
															label: 'inactive',
															value:0
														},
														{
															name:'Active',
															label: 'active',
															value:1
														}
													]
												}
											});

										}, 1);

									},
									download:false,
									headerButtons:{},
									multiSelectButtons:{},
									calendar:{},
									rowSelection:false,
									rowLink:{
										type:'tab',
										header:function(obj){

											return obj.fname +' '+ obj.lname;
										},
										action: singleStaffView
									},
									visibleCols:{
										fname:'First Name',
										lname: 'Last Name',
										nickname:'Nick Name',
										email:'Email Address',
										phone:'Phone',
										service:'Service',
										base:'Location',
										enabled:'User Status',
										documents:'Document Status',
									},
									cells: {
										fname:function(obj){

											return obj.fname;
										},
										lname: function(obj){
											return obj.lname;
										},
										nickname:function(obj){
											if(obj.nick_name){
												return obj.nick_name;
											}else{
												return obj.fname;
											}
										},
										email:function(obj){
											return obj.email;
										},
										phone:function(obj){
											if(obj.phone){
												return sb.dom.formatPhone(obj.phone);
											}else{
												return 'No phone saved';
											}
										},
										service:function(obj){

											if(obj.service){
												if(obj.service.length > 0){

													var ret = '',
														count = 0;

													_.each(obj.service, function(service){

														if(count > 0){

															ret += ',  '+ service.name;

														}else{

															ret = service.name;

														}

														count++;

													});

													if(obj.service.length > 3)
														ret += '...';

													return ret;

												}
											}

											return '<i>No job type selected</i>';

										},
										hire_date: function(obj){
											if(obj.hire_date && !_.isEmpty(obj.hire_date)){
												return moment(obj.hire_date).format('MM/DD/YY');
											}else{
												return 'No hire date set';
											}
										},
										termination_date: function(obj){

											if(moment(obj.termination_date).format('MM/DD/YY') == moment().format('MM/DD/YY')){

												return 'Active';

											}else{

												if(obj.termination_date && !_.isEmpty(obj.termination_date)){

													return moment(obj.termination_date).format('MM/DD/YY');

												}else{

													return 'Active';

												}

											}
										},
										base: function(obj){ 

											var	baseName = '<em>No Location(s) Selected</em>';

											if(obj.base != null && obj.base != undefined && obj.base[0] != null && obj.base[0] != undefined){

												baseName = '';

												_.map(obj.base, function(b, i){

													if(i < 3){

														if(i == 0){
															return baseName += `${b.name}`;
														}else{
															return baseName += `,  ${b.name}`;
														}

													}

													return;

												});

												if(obj.base.length > 3)
													baseName += '...';

											}

											return baseName;

										},
										documents: function(obj, dom){

											sb.data.db.obj.getAll('staff_paperwork_type', function(docs){

												var docTypes = docs;

												var docStatus = {
													css:'ui label',
													text:'No Service Assigned'
												};

												if(obj.hasOwnProperty('service') && !_.isEmpty(obj.service)){

													docLabeler.call(docStatus, obj);

												}

												dom.makeNode('label', 'div', {css: docStatus.css, text:docStatus.text});

												dom.patch();

											});

										},
										enabled: function(obj, dom){

											var enabled = {
												text:'Disabled',
												css:'ui label'
											};

											if(obj.enabled){
												enabled.text = 'Enabled',
												enabled.css = 'ui teal label'
											}

											dom.makeNode('label', 'div', {css: enabled.css, text:enabled.text});

											dom.patch();

										}

									},
									data: getStaffWithPaperwork
								}
							},
							{
								id:'staffToolNew',
								type:'custom',
								title:'All Staff',
								icon: '',
								isCollection: true,
								default:true,
								settings:false,
								dom:function(dom, state, draw){

									draw({
										dom:dom,
										after:function(dom){

											sb.notify({
												type:'show-collection',
												data:{
													actions:{
														create:function(ui, newObj, onComplete){

															createNewState(ui, newObj, draw, function(newUser){

																sb.notify({
																	type:'app-navigate-to',
																	data:{
																		itemId:'users',
																		viewId:{
																			viewState:{
																				obj: newUser
																			}
																		}
																	}
																});

															});

														},
														copy: false,
														view:true
													},
													domObj:dom,
													fields:{
														image:{
															type:'image',
															title:'',
															view:function(ui, obj){

																if(obj.profile_image.id){

																	ui.makeNode('avatar-'+obj.id, 'div', {css:'ui avatar image', tag:'img', src:sb.data.files.getURL(obj.profile_image)})

																}else{

																	ui.makeNode(obj.id +'-icon', 'div', {
																		css:'ui user icon',
																		tag:'i'
																	});
																}

															}
														},
														fname:{
															title:'First Name',
															type:'title',
															isSearchable:true
														},
														lname:{
															title:'Last Name',
															type:'title',
															isSearchable:true
														},
														email:{
															title:'Email Address',
															view:function(ui, obj){
																ui.makeNode('info','div',{text:obj.email});
															}
														},
														work_email:{
															title:'Work Email Address',
															view:function(ui, obj){
																if(obj.work_email){
																	ui.makeNode('info','div',{text:work_email});
																}else{
																	ui.makeNode('info','div',{text:'<i>No email saved</i>'});
																}
															}
														},
														phone:{
															title:'Phone',
															view:function(ui, obj){
																if(obj.phone){
																	ui.makeNode('info','div',{text:sb.dom.formatPhone(obj.phone)});
																}else{
																	ui.makeNode('info','div',{text:'<i>No phone saved</i>'});
																}
															}
														},
														work_phone:{
															title:'Work Phone',
															view:function(ui, obj){
																if(obj.work_phone){
																	ui.makeNode('info','div',{text:sb.dom.formatPhone(obj.work_phone)});
																}else{
																	ui.makeNode('info','div',{text:'<i>No phone saved</i>'});
																}
															}
														},
														service:{
															title:'Service',
															view:function(ui, obj){
																var ret = '<i>No service selected</i>';
																if(obj.service){
																	if(obj.service.length > 0){

																		var count = 0;

																		_.each(obj.service, function(service){

																			if(count > 0){

																				ret += ',  '+ service.name;

																			}else{

																				ret = service.name;

																			}

																			count++;

																		});

																		if(obj.service.length > 3)
																			ret += '...';

																	}
																}
																ui.makeNode('info', 'div', {text:ret});
															}
														},
														base: {
															title:'Location',
															view:function(ui, obj){

																var	baseName = '<i>No Location(s) Selected</i>';

																if(obj.base != null && obj.base != undefined && obj.base[0] != null && obj.base[0] != undefined){

																	baseName = '';

																	_.map(obj.base, function(b, i){

																		if(i < 3){

																			if(i == 0){
																				return baseName += `${b.name}`;
																			}else{
																				return baseName += `,  ${b.name}`;
																			}

																		}

																		return;

																	});

																	if(obj.base.length > 3)
																		baseName += '...';

																}

																ui.makeNode('info','div',{text:baseName});

															}
														},
														enabled:{
															type:'label',
															title:'User Status',
															view:function(ui, user){

																var enabled = {
																	text:'Disabled',
																	css:'ui label'
																};

																if(user.enabled){
																	enabled.text = 'Enabled',
																	enabled.css = 'ui teal label'
																}

																ui.makeNode('label', 'div', {css: enabled.css, text:enabled.text});

															}
														},
														document_status:{
															title:'Document Status',
															view:function(ui, user){

																var cellDom = ui;

																var docStatus = {
																	css:'ui label',
																	text:'No Service Assigned'
																};
																if(user.hasOwnProperty('service') && !_.isEmpty(user.service)){

																	docLabeler.call(docStatus, user);

																}

																cellDom.makeNode('label', 'div', {css: docStatus.css, text:docStatus.text});

																cellDom.patch();



															}
														}
													},
													parseData:parseStaffForPaperwork,
													sortCol:'lname',
													sortDir:'asc',
													sortCast:'string',
													groupings:{
														service:'Service',
														enabled:'Status'
													},
													objectType:'users',
													singleView:{
														view:function(ui, user, draw){

															if(user.profile_image.id){

																ui.makeNode('image', 'div', {tag:'img', css:'ui small circular centered image', src:sb.data.files.getURL(user.profile_image)});

															}

															var nickName = '';
															if(user.nick_name){
																nickName = '<br /><small><small><i>Nickname: '+ user.nick_name +'</i></small></small>';
															}

															ui.makeNode('title', 'div', {text:user.fname +' '+ user.lname + nickName, css:'ui huge centered header'});
															ui.makeNode('email', 'div', {text:user.email, css:'ui centered header'});
															ui.makeNode('phone', 'div', {text:sb.dom.formatPhone(user.phone) , css:'ui centered header'});
															ui.patch();

														},
														select:3
													},
													where:{
														childObjs:{
															fname:true,
															lname:true,
															email:true,
															phone:true,
															base:true,
															enabled:true,
															profile_image:true,
															service:true
														}
													}
												}
											});

										}
									});

								}
							},
							///BIN staffList settings
							{
								id:'settings',
								type:'settings',
								title:'Settings',
								icon:'<i class="fa fa-cog"></i>',
								setup:[
									{
										object_type: 'staff_job_types',
										name: '1. User Permissions',
										action: userViewSettings
									},
									{
										object_type: 'inventory_service',
										name: '2. Job Types',
										action:jobTypeSettings
									},
									{
    									object_type: 'inventory_service_2',
    									name: '3. Job Types Availability',
    									action: jobTypeAvailabilitySettings
									},
									{
										object_type: 'staff_base',
										name: '4. Locations',
										action: locationsSettingsState
									},
									{
										object_type: 'staff_paperwork_type',
										name: '5. Document Types',
										action: paperworkSettingsState
									},
									{
										object_type:'vacation_day_vesting_schedule',
										name:'6. Time-off Vesting Schedule Templates',
										action:timeOffVestingSchedules
									}
								]
							},
							///BIN staffList tags
							{
								id:'tags',
								type:'tags',
								title:'Tags',
								color: 'red',
								icon:'<i class="fa fa-tags"></i>',
								setup: {
									type: 'users',
									childObjs:2,
									resultList:function(dom, obj){

										dom.makeNode('content', 'div', {css: 'content'});
										dom.content.makeNode('name', 'div', {css:'ui header', text: `<i class="fa fa-user-circle"></i> ${obj.fname} ${obj.lname}`});

										dom.content.makeNode('jobs', 'div', {css:'meta', text:'<span class="small">Services:</span>'});

										dom.content.makeNode('serv', 'div', {css: 'ui bulleted list'});

										_.each(obj.service, function(job){

											this.makeNode('job-'+job.id, 'div', {css:'item', text:job.name});

										}, dom.content);

										dom.makeNode('btn', 'div', {css: 'ui bottom attached button', text:`View ${obj.fname}`});
										dom.btn.notify('click', {
											type: 'app-navigate-to',
											data: {
												itemId: 'staff',
												viewId:{
													id:'single-'+obj.id,
													type:'table-single-item',
													title: obj.fname +' '+ obj.lname,
													icon: '<i class="fa fa-user"></i>',
													setup:{
														objectType:'users'
													},
													dom: function(obj, dom, state, draw){

														singleStaffView(obj, dom, state, draw);

													}.bind({}, obj),
													rowObj:obj,
													removable:true,
													parent:'table'
												}
											}
										}, sb.moduleId);

										return dom;
									}
								}
							},
							///BIN staffList Create
							{
								id:'create',
								type:'custom',
								title:'New Staff Member',
								icon:'<i class="fa fa-user-circle"></i>',
								color: 'green',
								dom:createNewState
							},
							///BIN staffList QuickCreate
							{
								id: 'qc-staff',
								type: 'quickCreate',
								size: 'large',
								title: 'New Staff Member',
								icon: '<i class="fa fa-user-circle"></i>',
								dom: createNewState
							},
							// HQ collections tool StaffList
							{
								id:'staffingTool',
								adminOnly: true,
								type:'hqTool',
								name:'Team Members',
								tip:'Manage all team members.',
								icon: {
									type: 'users',
									color: 'teal'
								},
								settings:[
									{
										object_type:'users',
										name:'Team Members',
										tip:'The people listed below can be added to any team or project within your headquarters. Click the Create New button to add members. You can also use this screen to manage HR documents, schedule availability, and job types.',
										action:function(dom){

											dom.empty();

											dom.makeNode('break', 'div', {text:'<br />'});

											dom.makeNode('cont', 'div', {});

											dom.patch();

											sb.notify({
												type:'show-collection',
												data:{
													actions:{
														create:function(ui, newObj, onComplete){

															createNewState(ui, newObj, function(newUser){

																sb.notify({
																	type:'app-navigate-to',
																	data:{
																		itemId:'users',
																		viewId:{
																			viewState:{
																				obj: newUser
																			}
																		}
																	}
																});

															});

														},
														copy: false,
														view:true
													},
													domObj:dom.cont,
													fields:{
														image:{
															type:'image',
															title:'',
															view:function(ui, obj){

																if(obj.profile_image.id){

																	ui.makeNode('avatar-'+obj.id, 'div', {css:'ui avatar image', tag:'img', src:sb.data.files.getURL(obj.profile_image)})

																}else{

																	ui.makeNode(obj.id +'-icon', 'div', {
																		css:'ui user icon',
																		tag:'i'
																	});
																}

															}
														},
														fname:{
															title:'First Name',
															type:'title',
															isSearchable:true
														},
														lname:{
															title:'Last Name',
															type:'title',
															isSearchable:true
														},
														enabled:{
															type:'label',
															title:'User Status',
															view:function(ui, user){

																var enabled = {
																	text:'Disabled',
																	css:'ui label'
																};

																if(user.enabled){
																	enabled.text = 'Enabled',
																	enabled.css = 'ui teal label'
																}

																ui.makeNode('label', 'div', {css: enabled.css, text:enabled.text});

															}
														},
														document_status:{
															title:'Document Status',
															view:function(ui, user){

																var cellDom = ui;

																var docStatus = {
																	css:'ui label',
																	text:'No Service Assigned'
																};
																if(user.hasOwnProperty('service') && !_.isEmpty(user.service)){

																	docLabeler.call(docStatus, user);

																}

																cellDom.makeNode('label', 'div', {css: docStatus.css, text:docStatus.text});

																cellDom.patch();

															}
														}
													},
													groupings:{
														service:'Service',
														enabled:'Status'
													},
													objectType:'users',
													singleView:{
														view:function(ui, user, draw){

															if(user.profile_image.id){

																ui.makeNode('image', 'div', {tag:'img', css:'ui small circular centered image', src:sb.data.files.getURL(user.profile_image)});

															}

															var nickName = '';
															if(user.nick_name){
																nickName = '<br /><small><small><i>Nickname: '+ user.nick_name +'</i></small></small>';
															}

															ui.makeNode('title', 'div', {text:user.fname +' '+ user.lname + nickName, css:'ui huge centered header'});
															ui.makeNode('email', 'div', {text:user.email, css:'ui centered header'});
															ui.makeNode('phone', 'div', {text:sb.dom.formatPhone(user.phone) , css:'ui centered header'});
															ui.patch();

														},
														select:3
													},
													parseData:function(data, callback, query){

														parseStaffForPaperwork(data, callback, query);

													},
													where:{
														childObjs:{
															fname:true,
															lname:true,
															enabled:true,
															profile_image:true,
															service:true
														}
													}
												}
											});
										}
									},
/*
									{
										object_type: 'inventory_service',
										name: '1. Job Types',
										tip:'These are the types of jobs that can be worked by the people in your organization. Not how they get paid, but how you bill your clients for their time.',
										action:jobTypeSettings
									},
*/
									{
	    									object_type: 'inventory_service_2',
	    									name: '1. Default Job Types Availability',
	    									tip:'Set the default schedule availability per job type. Click the Edit button next to a job type to get started.',
	    									action: jobTypeAvailabilitySettings
									},
									{
										object_type: 'staff_base',
										name: '2. Locations',
										tip:'These are the locations people can be associated with. They are also used in the timeclock and staffing features.',
										action: locationsSettingsState
									},
									{
										object_type: 'staff_paperwork_type',
										name: '3. Document Types',
										tip:'Create required docuements for each job type. Require employee handbooks, copies of driver licenses and more.',
										action: paperworkSettingsState
									},
									{
										object_type:'vacation_day_vesting_schedule',
										name:'4. Time-off Vesting Schedule Templates',
										tip:'Have complicated vacation day vesting schedules? No worries, use this area to set them up.',
										action:timeOffVestingSchedules
									}
								],
								default:true,
								mainViews:[
									{
										dom:function(dom, state, draw, mainDom){

											staffListCollection(dom, state, draw, mainDom);

										}
									}
								]
							},
							// Team collections tool StaffList
							{
								id:'staffingTool',
								adminOnly: true,
								type:'teamTool',
								name:'Team Members',
								tip:'Manage all team members.',
								icon: {
									type: 'users',
									color: 'teal'
								},
								settings:[],
								default:false,
								mainViews:[
									{
										dom:function(dom, state, draw, mainDom){

											staffListCollection(dom, state, draw, mainDom);

										}
									}
								]
							},
							///HQ single object view
							{
								id:'users-obj',
								type:'object-view',
								title:'Staff',
								icon:'user',
								dom:function(dom, state, draw){

									sb.notify({
										type: 'view-page',
										data: {
											ui: dom,
											onDraw: draw,
											page: return_singleUserHeader(state, {
												draw: draw
												, dom: dom
											}),
											state: {
												pageObject: state.pageObject
											}
										}
									});
									
								}
								, select: {
									name:		true
									, email:  	true
									, enabled: 	true
								}
								, menu: function (state, draw, layer) {
									
									var userId = 0;
									// When looking at other people's my-stuff tools
									if (
										state.pageObject 
										&& typeof state.pageObject.object_bp_type === 'string'
										&& state.pageObject.object_bp_type === 'users'
									) {
										
										userId = state.pageObject.id;
										
									// When looking at your own my-stuff tool
									} else if (state.parent > 0) {
										
										userId = state.parent;
										
									} else {
										
										userId = +sb.data.cookie.get('uid');
										
									}
									
									sb.data.db.obj.getWhere(
										'groups' 
										, {
											user: 			userId,
											group_type: 	'MyStuff'
										}
										, function(myStuffGroup){
											
											if (myStuffGroup) {
												sb.notify({
													type: 	'get-dashboard-menu'
													, data:	{
														draw: 		draw
														, group: 	myStuffGroup[0]
														, layer: 	layer
													}
												});
											}
											
										}
									) ;
									
								}
							},
							///JobType TeamMembers Tool
							{
								id:'staffingTool',
								adminOnly: true,
								type:'jobTypeTool',
								name:'All Team Members',
								tip:'View all of your team members.',
								icon: {
									type: 'users',
									color: 'teal'
								},
								mainViews:[
									{
										dom:function(dom, state, draw, mainDom){

											sb.data.db.obj.getWhere(
												'groups'
												, {
													job_type:state.id
													, group_type:'JobType'
												}
												, function(res){

												state.id = res[0].id;
												state.pageObject = res[0];
												state.pageObjectType = res[0].object_bp_type;

												staffListCollection(dom, state, draw, mainDom);

											});

										}
									}
								]
								, boxViews:[
									{
										id: 		'teamMemberOverview',
										width: 	'six',
										title: 	'Team Members',
										collections: {
											selectedView: 'list',
											actions: {},
											fields: {
												name: {
													title: 'Name',
													type: 'title',
													isSearchable: true
												},
												date_created: {
													title: 'Created',
													type: 'title',
													view: function(ui, obj) {

														ui.makeNode('date', 'div', {
															css: 'date',
															text:`Created ${moment(obj.date_created).local().fromNow()}</span>`
														});

													}
												}
											},
											pageLength: 10,
											objectType: 'users',
											emptyMessage: 'No Users created recently',
											where: function(state) {

												return {
													group_type: 'JobType',
													tagged_with:[state.pageObject.id],
													childObjs: {
														name: true,
														group_type: true,
														enabled:true
													}
												};

											},
											groupings: {}
										}
									}
								]
							}
						]
					}
				}
			});

			sb.listen({
				'account-updated':this.view,
				'create-staff-member':this.create,
				'delete-staff-object':this.erase,
				'save-new-staff-member':this.saveNewStaffMember,
				'staff-member-created':this.view,
				'update-staff-member':this.update,
				'view-staff-member': this.view,

				'update-doc-type-add-field': this.updateDocFormAddField,
				'update-doc-type-delete-field': this.updateDocFormDeleteField,
				'update-doc-type-create-form-download': this.updateDocFormDownload,

				'user-documents-view': this.userDocumentsView,
				'user-settings-view': this.userSettings,

				'staffComponent-run': this.run
			});

		},

		create: function(data){

			createNewState([], data.dom);

		},

		erase: function(data){

			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: ''
			}, function(resp){

				if(resp){

					swal.disableButtons();
					// get all notes, tasks, accounts
					var objsToDelete = [
							{
								objectType:'users',
								id:data.object.id
							}
						];

					sb.data.db.obj.getWhere('tasks', {author: data.object.id}, function(tasks){

						_.each(tasks, function(o){

							objsToDelete.push(
								{
									objectType:'tasks',
									id:o.id
								}
							);

						});

						sb.data.db.obj.getWhere('notes', {author: data.object.id}, function(notes){

							_.each(notes, function(o){

								objsToDelete.push(
									{
										objectType:'notes',
										id:o.id
									}
								);

							});

							sb.data.db.obj.getWhere('staff_paperwork', {user: data.object.id}, function(paper){

								_.each(paper, function(o){

									objsToDelete.push({
										objectType: 'staff_paperwork',
										id: o.id
									});

								});

								sb.dom.alerts.ask({
									title: objsToDelete.length + ' object(s) to delete. Continue?',
									text: 'Nothing has been deleted yet. You can still cancel by selecting \'No\''
								}, function(resp){

									if(resp){


										swal.disableButtons();

										function deleteObjects(allObjects, callback, count){

											if(!count){
												count = 0;
											}

											sb.data.db.obj.erase(allObjects[count].objectType, allObjects[count].id, function(done){

												count++;

												if(count == allObjects.length){

													callback(count);

												}else{

													deleteObjects(allObjects, callback, count);

												}

											});

										}

										deleteObjects(objsToDelete, function(deleted){

											sb.dom.alerts.alert('Success!', deleted +' object(s) deleted.', 'success');



											sb.notify({
												type:'app-remove-main-navigation-item',
												data:{
													itemId:'self',
													viewId:'single-'+data.object.id
												}
											});

											setTimeout(function(){

												sb.notify({
												type:'app-navigate-to',
													data:{
														itemId:'staff',
														viewId:'staffToolNew'
													}
												});

												sb.notify({
													type:'app-redraw',
													data:{}
												});

											}, 500);

										});


									}

								});

							});

						});

					});

				}

			});

		},

		run: function(data){ data.run(); },

		saveNewStaffMember: function(data) {

			// Disable buttons
			data.dom.edit.hrow.rcol.btns.back.loading();
			data.dom.edit.hrow.rcol.btns.save.loading();

			// Create user
			sb.data.db.obj.create('users', data.user, function(newStaff) {

				if (newStaff) {

					var tools = [];

					_.each(appConfig.myStuffTools, function(tool, toolOrder) {

						tools.push({
							allowed_users:[newStaff],
							system_name:tool.id,
							display_name:tool.name,
							is_archieved:0,
							order:toolOrder,
							added_by:+sb.data.cookie.get('uid'),
							added_on:moment(),
							settings:{},
							box_color:tool.icon.color
						});

					});

					sb.data.db.obj.create('groups', {
						group_type:'MyStuff',
						user:newStaff.id,
						name:newStaff.fname +' '+ newStaff.lname,
						tools:tools
					}, function(myStuffGroup) {

						// Close the modal
						//data.dom.hide();

						sb.notify({
							type: 'app-navigate-to',
							data: {
								itemId:'staff',
								viewId:{
									id:'single-'+newStaff.id,
									type:'table-single-item',
									title:newStaff.fname +' '+ newStaff.lname,
									icon:'<i class="fa fa-user"></i>',
									setup:{
										objectType:'staff'
									},
									rowObj:newStaff,
									removable:true,
									parent:'table'
								}
							}
						});

						data.dom.patch();

					});	

				} else {

					// Enable buttons
					data.dom.edit.hrow.rcol.btns.back.loading(false);
					data.dom.edit.hrow.rcol.btns.save.loading(false);

					// Show error
					sb.dom.alerts.alert('Error!', 'Please refresh and try again.', 'error');

				}

			}, 1);
		},

		update: function(data){

			var forms = {
					personal: data.form.personal.process().fields,
					details: data.form.details.process().fields
				};
			var staff = {
				    id:data.object.id
			    };

			if(forms.personal.fname.value === '' || forms.personal.lname.value === '') {

				sb.dom.alerts.alert(
					'Incomplete form',
					'First and last name fields can not be blank',
					'error'
				);

				return;

			} else if(forms.personal.primary_email.value === 'No email saved' || forms.personal.primary_email.value === '') {

				sb.dom.alerts.alert(
					'Incomplete form',
					'Primary email address field can not be blank',
					'error'
				);

				return;

			}
// !staff details wip
			staff.fname = forms.personal.fname.value;
			staff.lname = forms.personal.lname.value;

			staff.nick_name = forms.personal.nickname.value;
			staff.email = forms.personal.primary_email.value;
			staff.phone = forms.personal.primary_phone.value;
			staff.work_email = forms.personal.work_email.value;
			staff.work_phone = forms.personal.work_phone.value;

			if(forms.details.hire_date.value === undefined) {
				staff.hire_date = 0;
			} else {
				staff.hire_date = forms.details.hire_date.value;
			}

			if(forms.details.termination_date.value === undefined) {
				staff.termination_date = 0;
			} else {
				staff.termination_date = forms.details.termination_date.value;
			}

			if(forms.details.base.value === null) {
				staff.base = [];
			} else {
				staff.base = forms.details.base.value;
			}

			if(forms.details.service.value === null) {
				staff.service = [];
			} else {
				staff.service = forms.details.service.value;
			}

			data.dom.edit.hrow.rcol.btns.makeNode('save', 'div', {
				css:'ui green button', 
				text:'<i class="fa fa-check"></i> Save'
			});
			data.dom.edit.hrow.rcol.btns.patch();

			data.dom.edit.hrow.rcol.btns.save.loading();

			sb.data.db.obj.update('users', staff, function(updated){

				sb.notify({
					type:'staffComponent-run',
					data:{
						run:function(dom, object, state, draw){

							singleStaffView(object, dom, state, draw);

						}.bind({}, data.dom, updated, data.state, data.draw)
					}
				});

			}, 1);

		},

		updateDocFormAddField: function(data){ data.addField(data.add); },

		updateDocFormDeleteField: function(data){ data.removeField(data.fieldIndex); },

		updateDocFormDownload: function(data){

			var formData = data.form.process().fields;

			function downloadField(){

				if(formData.need_file_download.value == 'Yes'){

					data.form.file_download.update({
							type: 'file-upload'
					});

				} else {

					data.form.file_download.update({
						type: 'hidden'
					});
				}

			}

			function frequencyField(){

				if(formData.need_exp_date.value == 'Yes'){

					data.form.not_frequency.update({
						type: 'int'
					});
				} else {

					data.form.not_frequency.update({
						type: 'hidden'
					});
				}
			}

				if(data.file_download){

					downloadField();

				} else if(data.not_frequency){

					frequencyField();
				}

		},

		userDocumentsView: function(data){

			var qParams = {
				dbCall: 'getWhere',
				queryObj: {

					id: +data.userObj.id

				}
			};

			getSingleStaffPaperwork(qParams, function(staff){

				selectedStaffObj = staff;

				if(data.hasOwnProperty('notifier')){

					userDocView(data.domObj, selectedStaffObj, data.notifier);

				}else{

					userDocView(data.domObj, selectedStaffObj);

				}

			});

		},
		
		userSettings: function(data) {
			
			userSettings(data.ui, data.user);
			
		},

		view: function(data){

			if(!settings.singleView){

				components.table.notify({
					type:'crud-table-row-link-clicked',
					data:{
						object:data.object,
						type:'tab',
						header: function(data){
							return data.object.fname +' '+ data.object.lname;
						},
						action: singleStaffView

					}
				});

			}

		},
	}

});
