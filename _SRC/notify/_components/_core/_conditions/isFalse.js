Factory.register('isFalse-condition', function (sb) {
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-condition'
				, data: {
					name: 		'isFalse'
					, title: 		'Is not checked'
					, icon: 		'circle outline'
					, appliesTo: 	['toggle', 'anyField']
					, check: 		function (obj, state, opts) {
					
						return !obj[opts.field];
						
					} // => Bool
				}
			});
			
		}
		
	}
	
});