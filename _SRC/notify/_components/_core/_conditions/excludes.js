Factory.register('excludes-condition', function (sb) {
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-condition'
				, data: {
					name: 			'excludes'
					, title: 		'Excludes'
					, icon: 		'circle'
					, appliesTo: 	['anyField']
					, check: 		function (obj, state, opts) {

						var test = obj[opts.field];
						if (!Array.isArray(test)) {
							test = [test];
						}
						var test2 = opts.value;
						if (!Array.isArray(test2)) {
							test2 = [test2];
						}
						// console.log('here::', _.chain(test)
						// .filter(function (v) {
							
						// 	var isOverlap = false;
						// 	_.each(test2, function (cmp) {

						// 		if (
						// 			cmp === v
						// 			|| parseInt(cmp) === parseInt(v)
						// 		) {
						// 			isOverlap = true;
						// 		}

						// 	});

						// 	return isOverlap;

						// })
						//  .isEmpty()
						//   .value(), test, test2);
						return _.chain(test)
									.filter(function (v) {
										
										var isOverlap = false;
										_.each(test2, function (cmp) {

											if (
												cmp === v
												|| parseInt(cmp) === parseInt(v)
											) {
												isOverlap = true;
											}

										});

										return isOverlap;

									})
									 .isEmpty()
									  .value();
						
                    }
                    , options :     {
                        value: {
                            type:           'currentType'
                            , isTemplate:   false
                            , name:         ''
                        }
                    }
                    // => Bool
				}
			});
			
		}
		
	}
	
});