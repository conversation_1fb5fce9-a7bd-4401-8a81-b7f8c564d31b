Factory.register('isSet-condition', function (sb) {
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-condition'
				, data: {
					name: 			'isSet'
					, title: 		'Not empty'
					, icon: 		'circle'
					, appliesTo: 	['anyField']
					, check: 		function (obj, state, opts) {
						
						return !_.isEmpty(obj[opts.field]);
						
					} // => Bool
				}
			});
			
		}
		
	}
	
});