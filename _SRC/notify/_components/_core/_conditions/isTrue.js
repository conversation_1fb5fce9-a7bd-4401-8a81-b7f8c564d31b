Factory.register('isTrue-condition', function (sb) {
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-condition'
				, data: {
					name: 			'isTrue'
					, title: 		'Is checked'
					, icon: 		'check circle'
					, appliesTo: 	['toggle', 'anyField']
					, check: 		function (obj, state, opts) {
						
						return !!obj[opts.field];
						
					} // => Bool
				}
			});
			
		}
		
	}
	
});