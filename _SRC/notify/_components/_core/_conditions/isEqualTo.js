Factory.register('isEqualTo-condition', function (sb) {
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-condition'
				, data: {
					name: 			'isEqualTo'
					, title: 		'Is equal to'
					, icon: 		'equals'
					, appliesTo: 	['anyField']
					, check: 		function (obj, state, opts) {
						console.log('isEqualTo::', arguments, (
							obj[opts.field] === opts.value
							|| parseInt(obj[opts.field]) === parseInt(opts.value)
						));
						return (
							obj[opts.field] === opts.value
							|| parseInt(obj[opts.field]) === parseInt(opts.value)
						);
						
					} // => Bool
					, options :     {
                        value: {
                            type:           'currentType'
                            , isTemplate:   false
                            , name:         ''
                        }
                    }
				}
			});
			
		}
		
	}
	
});
