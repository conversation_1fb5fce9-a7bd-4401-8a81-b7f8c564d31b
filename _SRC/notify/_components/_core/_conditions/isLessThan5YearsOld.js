Factory.register('isLessThan5YearsOld-condition', function (sb) {
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-condition'
				, data: {
					name: 			'isLessThan5YearsOld'
					, title: 		'Is less than 5 years old'
					, icon: 		'circle'
					, appliesTo: 	['date']
					, check: 		function (obj, state, opts) {
						
						return !moment(obj[opts.field]).isBefore(moment().subtract(5, 'years'));
						
					} // => Bool
				}
			});
			
		}
		
	}
	
});
