Factory.register('conditions', function (sb) {

	var Conditions = [];

	// Data funcs

	function getObjectBlueprint (state, callback) {
			
		if (state.object.object_bp_type === 'entity_workflow') {
			
			var idToGet = state.object.parent;
			if (
				state.object.parent
				&& state.object.parent.id
			) {
				idToGet = state.object.parent.id;
			}
			
			sb.data.db.obj.getById(
				'entity_type'
				, idToGet
				, function (bp) {
					
					callback(bp.blueprint);
					
				}
			);
			
		} else {
			
			var bpToGet = '';
			switch (state.object.object_bp_type) {
				
				case 'task_types':
				case 'project_types':
					bpToGet = 'groups';
					break;
					
				case 'contact_types':
					bpToGet = 'contacts';
					break;
				
			}
			
			sb.data.db.obj.getBlueprint (bpToGet, function (bp) {
				
				callback(bp);
				
			}, false);
			
		}
		
	}


	// Views

	function viewCondition (ui, condition, onComplete, bp, opts) {
		
		var Ui = ui;
		var Condition = condition;
		var showTitle = true;
		var showSaveBtn = true;

		// View Options
		if (!_.isEmpty(opts) && typeof opts === 'object') {
			if (opts.hasOwnProperty('showTitle')) {
				showTitle = opts.showTitle;
			}
			if (opts.hasOwnProperty('showSaveBtn')) {
				showSaveBtn = opts.showSaveBtn;
			}
		}
		
		function generateUID () {

		    var firstPart = (Math.random() * 46656) | 0;
		    var secondPart = (Math.random() * 46656) | 0;
		    firstPart = ("000" + firstPart.toString(36)).slice(-3);
		    secondPart = ("000" + secondPart.toString(36)).slice(-3);
		    return firstPart + secondPart;

		}
		
		function viewSingleCondition (ui, condition, i) {
			
			function getOptionsForm (ui, condition) {

				var conditionDef = _.findWhere(Conditions, {
					name: condition.type
				});

				if (
					!_.isEmpty(conditionDef) 
					&& !_.isEmpty(conditionDef.options) 
					&& !_.isEmpty(condition.options)
					&& !_.isEmpty(condition.options.field)
				) {

					_.each(conditionDef.options, function (option, optionKey) {

						switch (option.type) {

							case 'currentType':
								ui.makeNode(optionKey, 'div', {css: 'field', style:'padding:0px;'})
									.makeNode('h', 'div', {text: '<strong>'+ option.name +'</strong>'});
								ui.patch();

								sb.notify ({
									type: 'view-field'
									, data: {
										type: 			bp[condition.options.field].fieldType
										, property: 	optionKey
										, obj:			condition.options
										, options: 	{
											edit: 		true
											, editing: 	true
											, commitChanges: false
											, commitUpdates: false
											, is_template: 		option.is_template || false
											, title: 	condition.options[optionKey]
											, blueprint:{
												[optionKey]: bp[condition.options.field]
											}
										}
										, ui: ui[optionKey].makeNode('v', 'div', {})
									}
								});
								break;

						}

					});
				}
				
			}

			// Operator
			if (i > 0) {
				
				ui.makeNode(
					'operator'
					, 'div'
					, {
						tag: 	'td'
						, text: '<div class="ui black label">'+ condition.operator.toUpperCase() +'</div>'
					}
				);
				
			} else {
				
				ui.makeNode(
					'operator'
					, 'div'
					, {
						tag: 	'td'
						, text: 	''
					}
				);
				
			}
			
			var UI = ui;

			// Field / (more general apply to in the future)
			ui.makeNode(
				'f'
				, 'div'
				, {
					tag: 'td'
					, text: 'Field'
				}
			).listeners.push(
				function (ui) {
					
					sb.notify({
						type: 'view-field-selection'
						, data: {
							ui: 			ui
							, blueprint: 	bp
							, options: 		{
								allowedTypes: 	['any']
								, onUpdate: 	function (val) {

									condition.options.field = val;
									UI.value.empty();
									UI.value.patch();

									getOptionsForm(
										UI.value
										, condition
									);
									UI.value.patch();
									
								}
								, value: 		condition.options.field
							}
						}
					});
					
				}.bind({}, ui.f)
			);
			
			// Condition type
			var selectedTxt = '<i class="circle icon"></i> Not empty';
			var selectedType = _.findWhere(Conditions, {name: condition.type});
			if (selectedType) {
				
				selectedTxt = '<i class="'+ selectedType.icon +' icon"></i> '+ selectedType.title;
				
			} else {
				
				selectedTxt = 'Select a condition';
				
			}
			
			ui.makeNode(
				'c'
				, 'div'
				, {
					tag: 	'td'
					, text: 	''
				}
			);
			ui.c.makeNode('select', 'div', {
				css: 'ui fluid selection dropdown',
				text: '<div class="text">'+ selectedTxt +'</div> <i class="dropdown icon"></i>',
				listener: {
					type: 'dropdown',
					onChange: function(uid, value){
						
						_.findWhere(
							Condition.conditions
							, {
								uid: 	uid
							}
						).type = value;
						
					}.bind({}, condition.uid)
				},
				placeholder: 'Select a condition'
			}).makeNode(
				'menu'
				, 'div'
				, {
					css: 'menu'
				}
			);
			
			_.chain(Conditions)
				.filter(function (c) {
					
					return _.contains(c.appliesTo, 'anyField');
					
				})
				.each(function (c) {
				
				ui.c.select.menu.makeNode('p'+ c.name, 'div', {
					css: 'item'
					, dataAttr: [
						{
							name: 		'value'
							, value: 	c.name
						}
					]
				})
				.makeNode('txt', 'div', {
					css: 'text'
					, tag: 'span'
					, text: '<i class="grey '+ c.icon +' icon"></i> '+ c.title
				});
				
			});

			// Value (only w/some conditions)
			getOptionsForm(
				ui.makeNode(
					'value'
					, 'div'
					, {
						tag: 'td'
						, text: ''
						, style: 'width:40%;'
					}
				)
				, condition
			);
			
			// Remove
			ui.makeNode(
				'r'
				, 'div'
				, {
					tag: 'td'
					, text: ''
				}
			).makeNode(
					'rm'
					, 'div'
					, {
						tag: 'button'
						, text: '<i class="remove icon"></i>'
						, css: 'ui red mini circular icon button'
					}
				).notify('click', {
					type: 'conditions-run'
					, data: {
						run: function (uid) {
							
							Condition.conditions = _.filter(
								Condition.conditions
								, function (c) {
									
									return c.uid !== uid;
									
								}
							);
							
							viewConditions(Ui, Condition.conditions);
							Ui.patch();
							
						}.bind({}, condition.uid)
					}
				}, sb.moduleId);
			
		}
		
		function viewConditions (ui, conditions) {
			
			ui.c.b.empty();
			_.each(
				condition.conditions
				, function (cond, i) {
					
					viewSingleCondition(
						ui.c.b.makeNode(
							'c-'+ i
							, 'div'
							, {
								tag: 'tr'
							}
						)
						, cond
						, i
					);
					
				}
			);
			
		}
		
		var create = !(parseInt(condition.id));
		if (!Array.isArray(condition.conditions)) {
			condition.conditions = [];
		}
		
		// Condition name
		var fieldOptions = {
			editing: 			true
			, blueprint: 		bp
			, edit: 			true
			, commitUpdates: 	false
		};
		fieldOptions.fontSize = '3rem';
		fieldOptions.style = 'padding:14px;padding-bottom:0px;';

		if (showTitle) {

			sb.notify ({
				type: 'view-field'
				, data: {
					type: 			'title'
					, property: 	'name'
					, obj: 			condition
					, options: 		fieldOptions
					, ui: 			ui.makeNode('h', 'div', {})
				}
			});

		}
		
		ui.makeNode(
			'c'
			, 'div'
			, {
				tag: 'table'
				, css: 'ui very basic fluid table'
			}
		).makeNode(
			'b'
			, 'div'
			, {
				tag: 'tbody'
			}
		);
		viewConditions(ui, condition.conditions);
		
		// New condition btn
		ui.makeNode(
			'addAnd'
			, 'div'
			, {
				css: 'ui circular icon button'
				, text: 'AND'
			}
		).notify('click', {
			type: 'conditions-run'
			, data: {
				run: function () {
					
					condition.conditions.push({
						operator: 		'and'
						, conditions: 	[]
						, options: 		{}
						, uid: 			generateUID()
					});
					viewConditions(ui, condition.conditions);
					ui.c.b.patch();
					
				}
			}
		}, sb.moduleId);
		/*
ui.makeNode(
			'addOr'
			, 'div'
			, {
				css: 'ui circular icon button'
				, text: 'OR'
			}
		).notify('click', {
			type: 'conditions-run'
			, data: {
				run: function () {
					
					condition.conditions.push({
						operator: 		'or'
						, conditions: 	[]
						, options: 		{}
						, uid: 			generateUID()
					});
					viewConditions(ui, condition.conditions);
					ui.c.b.patch();
					
				}
			}
		}, sb.moduleId);
*/
		ui.makeNode('br', 'div', {text: '<br /><br />'});
		
		if (showSaveBtn) {
			
			ui.makeNode(
				'save'
				, 'div'
				, {
					tag: 		'button'
					, css: 		'ui teal button'
					, text: 	'<i class="save icon"></i> Save'
				}
			).notify('click', {
				type: 'conditions-run'
				, data: {
					run: function () {
						
						if (create) {

							sb.data.db.obj.create(
								'condition'
								, condition
								, function (response) {
									
									onComplete(response);
									
								}
							);

						} else {

							sb.data.db.obj.update(
								'condition'
								, condition
								, function (response) {
									
									onComplete(response);
									
								}
							);

						}
					}
					
				}
			}, sb.moduleId);
		}
		
		ui.patch();
		
	}

	function ViewSet (ui, state, draw) {

		getObjectBlueprint(state, function (bp) {
			
			sb.notify({
				type:'show-collection',
				data:{
					actions:{
						create:	function (ui, condition, onComplete) {
							
							condition.object = state.object.id;
							condition.state = state.state.uid;
							
							viewCondition(ui, condition, onComplete, bp);
							
						}
						, view:	true
						, navigateTo:      false
						, comments:        false
					},
					domObj:ui,
					fields: {
						name: {
							type:		'title'
							, title: 	'Name'
	// 						, view: 	viewEventType
						}
					},
					groupings: 				{},
					menu: {
						subviews: false,
						actions:  true
					},
					objectType: 			'condition',
					templates: false,
					selectedView: 			'list',
					selectedMobileView: 	'list',
					shouldKeepSegment: 		true,
					singleView: 				{
						useCache: 	true
						, view: 	function (ui, state, draw) {
							
							viewCondition (ui, state, draw, bp);
							
						}
					},
					state:	state,
					subviews: {
						table: 	false,
						board: 	false,
						list: 	{
							backlog: true
							, range: 'all_time'
						},
						chart: 	false
					},
					submenu: false,
					where: {
						state: 		state.state.uid
						, object: 	state.object.id
						, childObjs: {
							name: 			true
							, conditions: 	true
							, state: 		true
						}
					}
				}
			});
			
		});
		
	}
	
	return {
		
		init: function () {
			
			sb.listen({
				
				'conditions-run': 	this.run
				
				// Open api
				, 'check-conditions': 		this.runChecks
				, 'register-condition': 	this.register
				, 'view-conditions': 		this.viewSet
				, 'view-condition': 		this.viewCondition
				, 'edit-conditions-on-obj': this.editConditionsOnObj
				
			});
			
		}
		
		, run: function (data) {
			
			data.run(data);
			
		}
		
		// Open api
		
		, register: function (data) {
			
			Conditions.push({
				name: 			data.name
				, icon: 		data.icon
				, title: 		data.title
				, appliesTo: 	data.appliesTo
				, check: 		data.check || function (obj, state, opts) { return true; }
				, options: 		data.options || {}
			});
			
		}
		
		, runChecks: function (data) {
			
			var pass = {};
			_.each(data.conditions, function (check, type) {
				
				var checkPassed = true;
				var operator = '';
				
				if (check._if && !_.isEmpty(check._if)) {
					
					operator = '_if';
					
				} else if (check._not && !_.isEmpty(check._not)) {
					
					operator = '_not';
					
				}
				
				var keys = Object.keys(check[operator]);
				var condition = _.findWhere(
					Conditions
					, {
						name: keys[0]
					}
				);
				
				checkPassed = condition.check(
					data.object
					, {}
					, check[operator][condition.name]
				);
				
				pass[type] = checkPassed;
				
				if (operator === '_not') {
					pass[type] = !pass[type];
				}
				
			});
			
			data.onComplete(pass);
			
		}
		
		, viewSet: function (data) {
			
			ViewSet(
				data.ui
				, data.state
				, function () {}
			);
			
		}

		, viewCondition: function (data) {

			getObjectBlueprint(data.state, function (bp) {

				viewCondition (
					data.ui
					, data.condition
					, data.onComplete
					, bp
				);

			});
		
		}

		, editConditionsOnObj: function (data) {

			viewCondition (
				data.ui
				, data.obj
				, data.onComplete
				, data.bp
				, {
					showTitle:  	false
					, showSaveBtn: 	false
				}
			);

		}
		
	}
	
});
