Factory.register('page-data-devs-only', function (sb) {
	
	function pageUpdates (objType, where, mapFunc, commitChanges, page) {
		
		function getMappedData (data, mapFunc, onComplete) {

			if (mapFunc.length === 1) {

				onComplete(mapFunc(data.data));

			} else if (mapFunc.length === 2) {

				mapFunc(data.data, function (ret) {

					onComplete(ret);

				});

			}
			
		}

		if (page === undefined) {
			
			page = {
				count: true,
				page: 0,
				pageLength: 25
			};
			
		}
		
		var query = _.clone(where);
		query.paged = page;
		
		sb.data.db.obj.getWhere(
			objType
			, query
			, function(data){
				
				// if there are more updates to be made
				if (data && data.data && data.data.length > 0) {
					
					console.log(
						'Batch updates:: Fetching -> '+ page.page +' - '+ (page.page + page.pageLength)
					);

					getMappedData(
						data
						, mapFunc
						, function (updates) {

							if (commitChanges) {
						
								sb.data.db.obj.update(
									objType
									, updates 
									, function(response) {
										
										pageUpdates (
											objType
											, where
											, mapFunc 
											, commitChanges
											, {
												count: true
												, page: page.page + page.pageLength
												, pageLength: page.pageLength
											}
										);
										
									}
								);
								
							} else {
								
								pageUpdates (
									objType
									, where
									, mapFunc 
									, commitChanges
									, {
										count: true
										, page: page.page + page.pageLength
										, pageLength: page.pageLength
									}
								);
								
							}

							console.log('Batch updates:: Updates -> ', updates);

						}
					);
					
				} else {
					
					console.log(
						'Batch updates:: Complete!'
					) ;
					
					if (typeof onComplete === 'function') {
						onComplete(true);
					}
					
				}
				
			}
		);
		
	}
	
	return {
		
		init: function () {
			
			sb.listen({
				'batch-update-objects': this.batchUpdate
			});
			
			return ;
			
			/*
Factory.triggerEvent({
				type: 'batch-update-objects'
				, data: {
					objectType: 'inventory_billable_groups'
					, where: {
						category: 1098
						, childObjs: {
							default_pricing_option: 			true
							, price:							true
							, price_per_hour: 				true
							, price_per_person: 				true
							, price_per_hour_per_person: 	true
						}
					}
					, map: function (data) {
						
						var ret = [];
						_.each(data, function (item) {
							
							if (parseInt(item.price_per_person) <= 0) {
								
								ret.push({
									id: item.id
									, price_per_person: item.price
								});
								
							}
							
						});
						
						return ret;
						
					}
				}
			}, 'console');
*/
			
		}
		
		, batchUpdate: function (data) {
			
			sb.data.db.obj.getById('users', sb.data.cookie.get('uid'), function (user) {
				
				if (
					user.type === 'admin'
					|| user.type === 'developer'
				) {
					
					pageUpdates (
						data.objectType
						, data.where
						, data.map
						, data.commitChanges
					) ;
					
				} else {
					
					console.log('You don\'t have permission to run this command.');
				
				}
				
			});
			
		}
		
	} ;
	
});