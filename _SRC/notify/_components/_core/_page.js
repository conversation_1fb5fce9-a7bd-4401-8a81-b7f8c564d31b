Factory.register('page-view', function (sb) {

	function View (page, ui, state, draw, options) {

		var pageObj = _.clone(state.pageObject);
		
		function update_page (newObj) {

// 			View(page, ui, obj, draw);
			crawl_page (page, state.pageObject, function (el, name, container) {

				if (_.contains(appConfig.fieldTypes, el.type)) {
					
					var shouldUpdate = (state.pageObject[el.fieldName] !== newObj[el.fieldName]);
					
					// some field types depend on other values, so those
					// are check for changes here
					if (!shouldUpdate) {
						
						switch (el.type) {
							
							case 'state':

								if (
									state.pageObject.type 
									&& pageObj.type.hasOwnProperty('id')
									&& newObj.type
									&& newObj.type.hasOwnProperty('id')
									&& newObj.type.id !== pageObj.type.id
								) {
									shouldUpdate = true;
								}
								break;
							
						}
						
					}

					if (shouldUpdate) {
						
						container.makeNode(name, 'div', { css: '' });
						
						sb.notify({
							type: 'view-field',
							data: {
								type: 		el.type
								, property: el.fieldName
								, obj: 		state.pageObject
								, options: 	el
								, ui: 		container[name]
							}
						});
						
						container[name].patch();
						
					}
					
				}
				
			}, ui);
			
			pageObj = _.clone(newObj);
			
		}
		
		function crawl_page (page, obj, perEl, ui) {
			
			_.each(page, function(el, name){

				if(el.hasOwnProperty('show')) {
					
					if(el.show(obj) === false) {
						return;
					}
					
				}

				perEl(el, name, ui);
				
				if (!_.isEmpty(el.content)) {
					
					crawl_page (
						el.content
						, obj
						, perEl
						, ui[name].content
					);
					
				}
				
			});
			
		}

		crawl_page (page, state.pageObject, function(el, name, container){
			
			var tmp = {};
			var fieldCss = '';
			
			// a layout node
			if (sb.dom.hasOwnProperty(el.type)) {
				
				tmp = _.clone(el);
				if (tmp.hasOwnProperty('content')) {
					delete tmp.content;
				}
				
				// compact views have full-width columns
				if (options && options.compact && el.type === 'column') {
					tmp.w = 16;
				}
				
				container.makeNode(
					name
					, el.type
					, tmp
				);
			
			// a field
			} else if (_.contains(appConfig.fieldTypes, el.type)) {
				
				if (el.edit && el.type !== 'detail') {
					fieldCss = 'field';
				} else {
					fieldCss = 'disp-field';
				}
				
				if (!_.isEmpty(el.label)) {
					
					var gridCss = 'ui stackable grid';
					var col1Css = 'four wide column';
					var col2Css = 'twelve wide column';
					
					if (el.labelStyle === 'stacked') {
						gridCss = 'ui basic segment';
						col1Css = '';
						col2Css = '';
					}
					
					container.makeNode(state.pageObject.id +'-'+ name, 'div', {
						css: fieldCss
					});
					
					container[state.pageObject.id +'-'+ name].makeNode('grid', 'div', {css:gridCss});
					
					container[state.pageObject.id +'-'+ name].grid.makeNode('col1', 'div', {
						css: col1Css
					});
					container[state.pageObject.id +'-'+ name].grid.makeNode('col2', 'div', {
						css: col2Css
					});
					
					container[state.pageObject.id +'-'+ name].grid.col1.makeNode('assignee_label', 'div', {
						text: el.label +':',
						css: 'ui mini grey sub header',
						tag: 'h5',
						style: 'font-weight:4 !important;line-height:2em;color:rgb(175,175,175) !important;'
					});
					
					if (
						!el.hasOwnProperty('onUpdate')
						|| typeof el.onUpdate !== 'function'
					) {
						
						el.onUpdate = update_page;
						
					}
					
					sb.notify({
						type: 'view-field',
						data: {
							type: 		el.type
							, property: el.fieldName
							, obj: 		state.pageObject
							, options: 	el
							, ui: 		container[state.pageObject.id +'-'+ name].grid.col2
						}
					});
					
				} else {
					
					container.makeNode(state.pageObject.id +'-'+ name, 'div', { css: fieldCss });
					
					sb.notify({
						type: 'view-field',
						data: {
							type: 		el.type
							, property: el.fieldName
							, obj: 		state.pageObject
							, options: 	el
							, ui: 		container[state.pageObject.id +'-'+ name]
						}
					});
					
				}
				
			// a component
			} else if (el.type === 'view') {
				
				container.makeNode(name, 'div', {});
				
			}
			
			if (!_.isEmpty(el.content)) {
				
				tmp = _.clone(el.content);
				container[name].makeNode('content', 'div', tmp);
				
			}
			
		}, ui);

		ui.patch();

		crawl_page (page, state.pageObject, function(el, name, ui){
			
			// a callback for component setups, (or custom work)
			if (el.type === 'view')
				el.view(ui[name], state.pageObject, state);
			
		}, ui);
		
	}
	
	return {
		
		init: function() {
			
			sb.listen({
				'view-page': this.view
			});
			
		},
		
		destroy: function () {},
		
		view: function (data) {

			View(
				data.page
				, data.ui
				, data.state
				, data.onDraw
				, data.options
			);
			
		}
		
	}
	
});