Factory.register('portals', function (sb) {
	
	var currentPortal = false;
	
	function viewPortal (ui, state, onDraw) {
		
		var tokenId = 0;
		
		function setupMyStuff(
			userId
			, callback
		){
			
			sb.data.db.obj.getWhere('groups', {
				user: 			userId,
				group_type: 	'MyStuff'
			}, function(myStuffGroup){
				
				if (myStuffGroup.length > 0) {
					
					callback(myStuffGroup[0]);
					
				} else {
					
					var tools = [];
					
					sb.data.db.obj.create('groups', {
						group_type: 	'MyStuff'
						, user: 		userId
						, name: 		state.pageObject.name
						, tools: 		tools
					}, function(myStuffGroup){
						
						callback(myStuffGroup);
						
					});
					
				}
			
			});
			
		}

		if (appConfig.isFoundationGroupPortal) {

			var homePage = _.findWhere(appConfig.customTools, {id: 'fgPHome'});
			homePage.dom(ui, state, onDraw);
			return;

		}
		
		if (state.portal) {
			
			tokenId = state.portal;
			sb.data.db.obj.getById(
				'portal_access_token'
				, tokenId
				, function (token) {
					
					sb.data.db.obj.getById(
						'contacts'
						, token.contact
						, function (contact) {
							
							setupMyStuff(
								contact.id
								, function(myStuffGroup){
									
									state.myStuff = myStuffGroup;									
									
									sb.notify({
										type:'show-dashboard',
										data:{
											dom:		ui
											, state:	state
											, draw:		onDraw
										}
									});
									
								}
							);
							
						}
					);
					
				}
			);
			
		} else {
			
			tokenId = parseInt(state.params.t)
			setupMyStuff(
				state.pageObject.id
				, function(myStuffGroup){
					
					state.myStuff = myStuffGroup;
					
					sb.notify({
						type:'show-dashboard',
						data:{
							dom:		ui
							, state:	state
							, draw:		onDraw
						}
					});
					
				}
			);
			
		}
		
	}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: 		sb.moduleId
						, instanceId: 	sb.instanceId
						, id: 			'portals'
						, title: 		'Portals'
						, icon: 		'<i class="fa fa-file-text-o"></i>'
						, views: [
							{
								id: 		'client-portal'
								, dom: 		viewPortal
								, type: 	'custom'
								, default: 	true
								, title: 	'Portals'
								, icon: 	'<i class="building outline icon"></i>'
								, menu: function (state, draw, layer) {

									if (appConfig.isFoundationGroupPortal) {
										// get icon
										var tools = [
											_.findWhere(appConfig.customTools, {id: 'fgPHome'})
											, _.findWhere(appConfig.customTools, {id: 'fgPCurrent'})
											, _.findWhere(appConfig.customTools, {id: 'fgPMem'})
											, _.findWhere(appConfig.customTools, {id: 'fgPHistory'})
											, _.findWhere(appConfig.customTools, {id: 'fgPHelp'})
										];

										var links = [];

										_.each(tools, function (tool) {

											if ( tool ){

												var icon = '';
												if (tool.icon) {
													icon = tool.icon.color +' '+ tool.icon.type;
												}
												
												// get link
												var linkArgs = 'custom';
													
												var linkOpts = {
													tool: 		tool.id
													, id: 		tool.id
													, name: 	tool.title
													, startAt: 	appConfig.breadcrumbs[0].url
												};
												
												if (
													tool.toolId
													&& tool.title
												) {
													
													linkOpts.name = tool.title;
													linkOpts.toolId = tool.toolId;
													
												} else if (
													typeof tool.name === 'string'
													&& !_.isEmpty(tool.name)
												) {
													linkOpts.name = tool.name;
												}
												
												href = sb.data.url.createPageURL(
													linkArgs 
													, linkOpts
												);
		
												links.push({
													id: 		tool.id
													, toolId: 	tool.toolId
													, title:	tool.title
													, icon: 	icon
													, link: 	href
												});

											}

										});

										draw({
											links: 				links
											, maintainOrder: 	true
											// 
										});

									} else if (state.pageObject) {
										
										sb.data.db.obj.getWhere(
											'groups' 
											, {
												user:			parseInt(sb.data.cookie.get('p_uid')),
												group_type:		'MyStuff'
											}
											, function(myStuffGroup){
												if (_.isEmpty(layer.title)) {
													layer.title = 'Contact Dashboard';
												}
												
												if (myStuffGroup) {
													sb.notify({
														type: 	'get-dashboard-menu'
														, data:	{
															draw: 		draw
															, group: 	myStuffGroup[0]
															, layer: 	layer
														}
													});
												}
												
											}
										)
										
									}
									
								}
							}
							, {
								id: 		'contactDashboard'
								, dom: 		function (ui, state, onDraw) {
									
									state.editDashboard = true;
									viewPortal(ui, state, onDraw);
									
								}
								, type: 	'custom'
								, default: 	true
								, title: 	'Contact Dashboard'
								, icon: 	'<i class="building outline icon"></i>'
								, portal: 	true
								, getPortal: 	function (state, callback) {
									
									if (state.params && state.params.t) {
										
										sb.data.db.obj.getById(
											'portal_access_token'
											, parseInt(state.params.t)
											, function (portal) {
												
												currentPortal = portal;
												state.portal = parseInt(state.params.t);
												callback(portal);
												
											}
										);
										
									} else {
										
										callback(currentPortal);
										
									}
									
								}, menu: function (state, draw, layer) {
									if (state.pageObject) {
										
										sb.data.db.obj.getWhere(
											'groups' 
											, {
												user:			currentPortal.contact,
// 												user:			state.portal,
												group_type:		'MyStuff'
											}
											, function(myStuffGroup){
												
												if (_.isEmpty(layer.title)) {
													layer.title = 'Contact Dashboard';
												}
												
												if (myStuffGroup) {
													sb.notify({
														type: 	'get-dashboard-menu'
														, data:	{
															draw: 		draw
															, group: 	myStuffGroup[0]
															, layer: 	layer
														}
													});
												}
												
											}
										)
										
									}
									
								}
							}
						]
					}
				}
			});
			
		}
		
	};
	
});