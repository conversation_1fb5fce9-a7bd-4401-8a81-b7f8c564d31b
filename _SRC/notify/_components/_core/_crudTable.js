Factory.registerComponent('crud-table', function(sb){
	
	var domObj = {},
		components = {},
		selectedRows = [],
		compCache = {},
		cachedData = [],
		counterString = '',
		paged = {
			count:true,
			page:0,
			pageLength:50,
			paged:true,
			sortCol:'date_created',
			sortDir:'desc',
			sortCast:'string'
		},
		blueprint = {},
		setupData = {
			searchObjects:[],
			borderColor:'',
			newTab:false,
			buttonState:{},
			childObjs:1,
			download:false,
			navigation:true,
			container:false,
			settingsTable:false,
			tags: false,
			singleObject:false,
			noObjects:false,
			rowStyle:'',
			cellStyles:[],
			drawComplete:null,
			parseSearchData:function(data, callback){ callback(data); }
		},
		dataCall = {},
		ui = {},
		historyUI ={},
		tableUI = {},
		fullUI = {};
	
	// fullpage UI functions
	function buildFullScreenUI(){
		
		function build(panelHeader){
			
			var panelColor = '';
			if(!panelColor){
				panelColor = 'pda-panel-primary';
			}
			
			panelColor = setupData.borderColor;
						
			// container for the button dom
			this.makeNode('container', 'div', {css:''});
			
			this.patch();
			
/*
			sb.notify({
				type:'crud-table-back-to-top',
				data:{}
			});
*/
			
		}
		
		this.state.display = build.bind(this);
		
	}
	
	function buildHistoryUI(){
		
		var tabHistory = [],
			onScreen = false;
					
		function build(name, onlyIfOnScreen){
			
			if(!onlyIfOnScreen){
				onlyIfOnScreen = false;
			}
			
			if(onlyIfOnScreen == false || (onlyIfOnScreen == true && onScreen == true) ){
				
				var css = '';
				
				if(onScreen == false){
					//var css = 'slideInDown';
				}

				var dom = this;
				
				onScreen = true;
				
				buildMenu(dom, css, name);

				
			}
						
		}
		
		function buildMenu(dom, animationClass, name){
						
			dom.makeNode('container', 'div', {css:''});
			
			if(typeof setupData.tableTitle == 'object'){
				
				if(setupData.tableTitle.size == 'large'){
					
					dom.container.makeNode('home', 'headerText', {css:homeColor, text:''+ setupData.tableTitle.title}).notify('click', {
						type:'crud-table-view-home',
						data:{}
					}, sb.moduleId);
					
					//dom.container.makeNode('titleBreak', 'lineBreak', {});
					
				}
				
			}
						
			dom.container.makeNode('btns', 'div', {css:'ui buttons'});

			if(setupData.home){
				
				var homeColor = 'basic';
				
				if(name == 'home'){
					homeColor = '';
				}

/*
				if(typeof setupData.tableTitle == 'string'){
					
					dom.container.btns.makeNode('home', 'button', {css:homeColor +' pda-btn-medium pda-btnOutline-blue pda-transparent pda-btn-large', text:''+ setupData.tableTitle}).notify('click', {
						type:'crud-table-view-home',
						data:{}
					}, sb.moduleId);
					
				}
*/
				dom.container.btns.makeNode('home', 'div', {css:'ui blue '+ homeColor +' button', tag:'button', text:'<i class="fa fa-home"></i>'}).notify('click', {
					type:'crud-table-view-home',
					data:{}
				}, sb.moduleId);
				
			}else{
				
				if(setupData.tableTitle){
					
					var homeColor = 'basic';
				
					if(name == 'home'){
						homeColor = '';
					}
					
					if(typeof setupData.tableTitle == 'string'){
						
						dom.container.btns.makeNode('home', 'button', {css:'ui blue '+ homeColor +' button', text:''+ setupData.tableTitle}).notify('click', {
							type:'crud-table-back-to-table',
							data:{}
						}, sb.moduleId);
						
					}
					
/*
					dom.container.btns.makeNode('home', 'button', {css:homeColor +' ', text:'<i class="fa fa-home"></i>'}).notify('click', {
						type:'crud-table-back-to-table',
						data:{}
					}, sb.moduleId);
*/
							
				}
				
			}
			
			
			
			if(name != 'table'){
				
/*
				dom.container.btns.makeNode('back', 'button', {css:'pda-btnOutline-blue pda-btn-x-small', text:'<i class="fa fa-list-ul"></i>'}).notify('click', {
					type:'crud-table-back-to-table',
					data:{}
				}, sb.moduleId);
*/
				
			}else{
				
				//dom.container.btns.makeNode('back', 'button', {css:'pda-btn-blue pda-btn-x-small', text:'<i class="fa fa-list-ul"></i>'});
				
			}
			
			if(setupData.calendar){
				
				var calColor = 'basic';
				
				if(name == 'calendar'){
					calColor = '';
				}
				
				dom.container.btns.makeNode('calendar', 'div', {css:'ui blue '+ calColor +' button', tag:'button', text:'<i class="fa fa-calendar"></i>'}).notify('click', {
					type:'crud-table-view-calendar',
					data:{}
				}, sb.moduleId);
				
			}
			
			if(setupData.views){
				
				_.each(setupData.views, function(customView, viewKey){
					
					var btnColor = 'pda-btnOutline-blue';
					
					if(name == viewKey){
						btnColor = 'pda-btn-blue';
					}
					
					dom.container.btns.makeNode(viewKey, 'button', {css: btnColor +' pda-btn-x-small', text: customView.label}).notify('click', {
						type: 'crud-table-view-custom-view',
						data: {
							view:viewKey
						}
					}, sb.moduleId);
					
				});
				
			}
			
			if(setupData.settings){
				
				var settingsColor = 'pda-btnOutline-blue';
				
				if(name == 'settings'){
					settingsColor = 'pda-btn-blue';
				}
				
				dom.container.btns.makeNode('settings', 'button', {css:settingsColor +' pda-btn-x-small', text:'<i class="fa fa-cogs"></i>'}).notify('click', {
					type:'crud-table-view-settings',
					data:{}
				}, sb.moduleId);
				
			}
			
			if(setupData.tags){
				
				var tagsColor = 'pda-btnOutline-blue';
				
				if(name == 'tags'){
					tagsColor = 'pda-btn-blue';
				}
				
				dom.container.btns.makeNode('tags', 'button', {css: tagsColor +' pda-btn-x-small', text:'<i class="fa fa-tags"></i>'}).notify('click', {
					type: 'crud-table-view-tags',
					data: {}
				}, sb.moduleId);
			}
			
			if(setupData.rules){
				
				var rulesColor = 'pda-btnOutline-blue';
				
				if(name == 'rules'){
					rulesColor = 'pda-btn-blue';
				}
				
				dom.container.btns.makeNode('rules', 'button', {css: rulesColor, text: '<i class="fa fa-gavel"></i>'}).notify('click', {
					type: 'crud-table-view-rules',
					data: {}
				}, sb.moduleId);
				
			}

			_.each(tabHistory, function(h){

				var btnColor = 'pda-btnOutline-primary';

				if(typeof name == 'object'){
					
					if(h.object.id == name.id){
					
						btnColor = 'pda-btn-primary';
					
					}
						
				}

				dom.container.btns.makeNode('history-'+h.object.id, 'button', {dataId:h.object.id, css:btnColor, text:''+ h.name}).notify('click', {
					type:'crud-table-row-link-clicked',
					data:{
						dataId:h.object.id,
						type:h.viewType
					}
				}, sb.moduleId);
				
			});
			
			if(name != 'loading'){
				
				delete dom.container.btns.loading;
								
			}else{
								
				dom.container.btns.makeNode('loading', 'button', {css:'pda-transparent', text:''}).makeNode('loading', 'loader', {size:'small'});
				
			}
			
			dom.makeNode('navBreak', 'div', {text:'<br /><br />'});
			
/*
			dom.makeNode('navBreak', 'lineBreak', {});
			dom.makeNode('seperator', 'container', {css:'pda-panel-blue'});
			dom.makeNode('navBreak2', 'lineBreak', {});
*/

			dom.patch();
				
		}	
		
		function hide(force){

			if(force || tabHistory.length == 0){

				buildMenu(this, 'fadeOutUp');
				
				onScreen = false;
				
			}
			
		}
		
		function update(obj, name, print, viewType){
			
			if(!viewType){
				viewType = 'tab';
			}
			
			if( _.where(tabHistory, {name:name}).length == 0 ){
					
				tabHistory.unshift({
					name:name,
					object:$.extend(true, {}, obj),
					viewType:viewType
				});
					
			}

			if(print){

				this.state.show(obj);
								
			}
				
		}
				
		this.state.hide = hide.bind(this);
		this.state.show = build.bind(this);
		this.state.update = update.bind(this);
		
	}
		
	// default button functions
	function buildSingleObjectForm(objectData, formFields, domObj, blueprint, objectType, duplicating){
		
		function checkFormCompleteness(objectData, blueprint){
			
			var ret = true;
			var msg = '';
			
			_.each(objectData, function(val, key){
				
				if(blueprint[key] && blueprint[key].notEmpty && _.isEmpty(val)){

					ret = false;
					msg += '<strong class="pda-color-red">'+ blueprint[key].name +' can not be left empty.</strong><br /><br />';
					
				}
				
			});
			
			if(!ret){
				sb.dom.alerts.alert('Please complete the form.', msg, 'error', true);
			}else{
				return true;
			}
			
		}
		
		function createObject(buttonDom, objectType, update, settingsTable){

			var newData = {
					id: objectData.id
				};
			
			// if in diff columns, process each col into single obj
			if(Array.isArray(formFields)){
				
				_.each(formFields, function(col, i){

					var formData = domObj.editObjectModal.body['col-'+ i].editObjectForm.process();
					
					_.each(formData.fields, function(datum, key){

						newData[key] = datum.value;

					});
					
				});
				
			}else{
				
				var formData = domObj.editObjectModal.body.editObjectForm.process();

				_.each(formData.fields, function(datum, key){

					if(datum.value > -1){
						
						if(datum.type == 'checkbox'){
							newData[key] = datum.value;
						}else if(datum.type == 'text'){
							newData[key] = datum.value.toString();
						}else{
							if(Array.isArray(datum.value)){
								newData[key] = datum.value;
							}else{
								newData[key] = +datum.value;
							}
							
						}
						
					}else{
						
						if(datum.type == 'text'){
							newData[key] = datum.value.toString();
						}else{
							newData[key] = datum.value;
						}
						
					}

				});

			}
			
			if(checkFormCompleteness(newData, blueprint)){
				
				buttonDom.btns.makeNode('updateObjectButton', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving'});
				buttonDom.btns.patch();
				
				if(childObjs){
					newData.getChildObjs = childObjs;
				}else{
					newData.getChildObjs = 0;
				}
				
				if(update == 'update'){
					var createType = 'update';
				}else{
					var createType = 'create';
				}
	
				sb.data.db.obj[createType](objectType, newData, function(createdObject){
	
					compCache.empty();
						
					compCache.getPage(paged, function(objs){
				
						fullUI.hide();
					
						tableUI.show();
						
						tableUI.state();
						
						tableUI.state.buttons(setupData.headerButtons, 'btns');
						tableUI.state.table(objs);
						tableUI.state.pages(objs, 0);
											
						if(setupData.searchObjects){
							tableUI.state.search();
							tableUI.state.searchBar();
						}
						
						historyUI.state.show('table', true);
									
					}, true);
									
				});
				
			}
			
		}
		
		// deprecated - not currently in use.
		// all update and create operations run through the function above
		function updateObject(buttonDom, objectType){

			var newData = {
					id: objectData.id
				};
							
			buttonDom.btns.makeNode('updateObjectButton', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving'});
			buttonDom.btns.patch();
			
			// if in diff columns, process each col into single obj
			if(Array.isArray(formFields)){
				
				_.each(formFields, function(col, i){
					
					var formData = domObj.editObjectModal.body['col-'+ i].editObjectForm.process();
					
					_.each(formData.fields, function(datum, key){

						newData[key] = datum.value;

					});
					
				});
				
			}else{
				
				var formData = domObj.editObjectModal.body.editObjectForm.process();

				_.each(formData.fields, function(datum, key){

					newData[key] = datum.value;

				});

			}
			
			if(childObjs){
				newData.getChildObjs = childObjs;
			}else{
				newData.getChildObjs = 0;
			}

			sb.data.db.obj.update(objectType, newData, function(updatedObject){

				components.settings.notify({
					type:'crud-table-back-to-table',
					data:{}
				});
				
				components.settings.notify({
					type:'update-table',
					data:{}
				});
				
				sb.notify({
					type: objectType + '-object-updated',
					data: updatedObject
				});	
				
			});		
			
		}

		var formSetup = {};

		if(_.isEmpty(formFields)) {

			_.each(blueprint, function(field, key){	
				formSetup = getFormField(formFields, field, key, formSetup, objectData);
			});

		} else {

			// if array provided, split into seperate columns
			if(Array.isArray(formFields)){
				
				formSetup = [];
				_.each(formFields, function(col, i){
					formSetup.push({});
					_.each(formFields[i], function(field, key){
						
						if(key !== 'form_options'){
							
							if (blueprint.hasOwnProperty(key)) {
								formSetup[i] = getFormField(formFields[i], blueprint[key], key, formSetup[i], objectData);
							} else {
// 								console.log('Blueprint property ' + key + ' doesn\'t exist');
							}
							
						}
						
					});
				});
				
			}else{
				
				_.each(formFields, function(field, key){
					if (blueprint.hasOwnProperty(key)) {
						formSetup = getFormField(formFields, blueprint[key], key, formSetup, objectData);
					} else {
// 						console.log('Blueprint property ' + key + ' doesn\'t exist');
					}
				});
				
			}
			
		}

		var btnCss = '';

		domObj.makeNode('break', 'lineBreak', {});
		domObj.makeNode('editObjectModal', 'div', {uiGrid:false, css: 'ui raised blue segment'});
		domObj.editObjectModal.makeNode('body', 'container', {uiGrid:false});
		domObj.editObjectModal.makeNode('footer', 'container', {uiGrid:false});

		btnCss = 'pull-left';
		
		if(typeof objectData !== 'undefined' && !_.isEmpty(objectData)){
			domObj.editObjectModal.body.makeNode('header', 'headerText', {text:'Edit<br />', size:'small', css: ''});
			domObj.editObjectModal.body.makeNode('headerBreak', 'lineBreak', {text:'<br />', size:'small', css: ''});
		}else{
			domObj.editObjectModal.body.makeNode('header', 'headerText', {text:'Create<br />', size:'small', css: ''});
			domObj.editObjectModal.body.makeNode('headerBreak', 'lineBreak', {text:'<br />', size:'small', css: ''});
		}
		
		if(Array.isArray(formFields)){
			_.each(formFields, function(column, i){
				domObj.editObjectModal.body.makeNode('col-'+ i, 'column', {width: 12/formFields.length, css: 'pda-container'});
				
				if(column.hasOwnProperty('form_options') && column.form_options.hasOwnProperty('title')){
					domObj.editObjectModal.body['col-'+ i].makeNode('title', 'headerText', {size: 'x-small', css: 'text-center', text: column.form_options.title});
				}
				
				domObj.editObjectModal.body['col-'+ i].makeNode('editObjectForm', 'form', formSetup[i]);
			});
		}else{
			domObj.editObjectModal.body.makeNode('editObjectForm', 'form', formSetup);
		}
		
		domObj.editObjectModal.footer.makeNode('buttonBreak', 'lineBreak', {});
		
		domObj.editObjectModal.footer.makeNode('btns', 'div', {css:'ui buttons'});
		
		if(typeof objectData !== 'undefined' && !_.isEmpty(objectData)){
			domObj.editObjectModal.footer.btns.makeNode('updateObjectButton', 'button', {text:'<i class="fa fa-check"></i> Save Changes', css: btnCss +' pda-btn-green'}).notify('click', {
				type:'crud-table-run',
				data:{ run:createObject.bind(domObj.editObjectModal.body.editObjectForm, domObj.editObjectModal.footer, objectType, 'update', this) }
			}, sb.moduleId);
		}else{
			domObj.editObjectModal.footer.btns.makeNode('updateObjectButton', 'button', {text:'<i class="fa fa-check"></i> Save', css: btnCss +' pda-btn-green'}).notify('click', {
				type:'crud-table-run',
				data:{ run:createObject.bind(domObj.editObjectModal.body.editObjectForm, domObj.editObjectModal.footer, objectType, '', this) }
			}, sb.moduleId);
		}
		
		if(!duplicating && !_.isEmpty(objectData)){
			domObj.editObjectModal.footer.btns.makeNode('duplicate', 'button', {text:'<i class="fa fa-copy"></i> Duplicate', css: 'pda-btn-teal'}).notify('click', {
				type:'crud-table-run',
				data:{
					run:buildSingleObjectForm.bind({}, objectData, formFields, domObj, blueprint, objectType, true)
				}
			}, sb.moduleId);
		}
		
		domObj.editObjectModal.footer.btns.makeNode('back', 'button', {text:'<i class="fa fa-times"></i> Cancel', css: 'pda-btn-red'}).notify('click', {
			type:'crud-table-back-to-table',
			data:{}
		}, sb.moduleId);

		domObj.patch();
				
	}
	
	function defaultCreateFunction(buttonObj, dom, bp, objectType){

		dom.makeNode('cont', 'column', {w:16, css:setupData.borderColor});
		
		dom.makeNode('break', 'lineBreak', {});
		
		dom.patch();

		var formFields = {};

		if(setupData.rowLink.formObjs){
			formFields = setupData.rowLink.formObjs;
		}

		buildSingleObjectForm({}, formFields, dom.cont, bp, objectType);
		
	}
	
	function defaultEditFunction(dom, obj, buttonObj, bp, objectType){

		dom.makeNode('cont', 'column', {w:16, header:obj.name});
		
		dom.patch();
				
		var formFields = {};
		if(setupData.rowLink.formObjs){
			formFields = setupData.rowLink.formObjs;
		}
		
		buildSingleObjectForm(obj, formFields, dom.cont, bp, objectType);
				
	}
	
	function defaultEraseFunction(objsToDelete, callback){

		sb.dom.alerts.ask({
			title: 'Are you sure?',
			text: ''
		}, function(resp){
			
			if(resp){
			
				swal.disableButtons();
				
				function deleteObjects(allObjects, callback, count){
										
					if(!count){
						count = 0;
					}
					
					sb.data.db.obj.erase(allObjects[count].objectType, allObjects[count].id, function(done){
						
						count++;
						
						if(count == allObjects.length){
							
							callback(true);
																					
						}else{
							
							deleteObjects(allObjects, callback, count);
							
						}
						
					});
					
				}

				deleteObjects(objsToDelete, function(deleted){
					
					compCache.empty();
				
					compCache.getPage(paged, function(objs){
						
// 						cachedData = [];
						selectedRows = [];
												
						fullUI.hide();
					
						tableUI.show();
						
						tableUI.state();
						
						tableUI.state.buttons(setupData.headerButtons, 'btns');
						tableUI.state.table(objs);
						tableUI.state.pages(objs, 0);
											
						if(setupData.searchObjects){
							tableUI.state.search();
							tableUI.state.searchBar();
						}
						
						historyUI.state.show('table', true);
						
						sb.dom.alerts.alert('Success', 'Item(s) deleted successfully.', 'success');
						//swal.close();
									
					}, true);
																			
				});
			
			}
			
		});
		
	}
	
	function defaultRulesView(dom, rules){
		
		dom.makeNode('rules', 'column', {width: 12});
		dom.rules.makeNode('loader', 'loader', {size: 'medium', css: 'text-center'});
		dom.patch();
		
		rules.domObj = dom;
		
		components.rules.notify({
			type: 'show-rules-table',
			data: rules
		});
		
	}
	
	function defaultSettingsView(dom, settings){
		
		function displaySetting(dom, obj){
			
			delete dom.settings.cont.startText;
			
			dom.settings.cont.css('');
						
			//dom.settings.cont.makeNode('loading', 'loader', {});
			
			dom.settingsNav.cont['nav-'+ obj.object_type].text('<i class="fa fa-circle-o-notch fa-spin"></i> '+ obj.name);
						
			sb.data.db.obj.getBlueprint(obj.object_type, function(bp){

				if(obj.action){
					
					dom.settings.cont.patch();
					
					obj.action(dom.settings.cont, bp);
					
				}else{
					
					var visibleCols = {};
				
					_.each(bp, function(fieldObj, fieldName){
						
						if(fieldObj.immutable == false){
							
							visibleCols[fieldName] = fieldObj.name;
							
						}
						
					});

					var rowLink = {
							type:'edit',
							header:function(obj){},
							action:'edit'
						};
					
					if(obj.formObjs){
						rowLink.formObjs = obj.formObjs;
					}

					components.settings.notify({
						type:'show-table',
						data:{							
							domObj:dom.settings.cont,
							tableTitle:obj.name,
							settingsTable:true,
							navigation:false,
							objectType:obj.object_type,
							searchObjects:false,
							filters:false,
							download:false,
							headerButtons:{
								reload:{
									name:'Reload',
									css:'pda-btn-blue',
									action:function(){}
								},
								create:{
									name:'<i class="fa fa-plus"></i> Create New',
									css:'pda-btn-green',
									domType:'full',
									action:'create'
								}
							},
							calendar:false,
							rowSelection:true,
							rowLink:rowLink,
							multiSelectButtons:{
								erase:{
									name:'<i class="fa fa-trash-o"></i> Delete',
									css:'pda-btn-red',
									domType:'default',
									action:'erase'
								}
							},
							visibleCols:visibleCols,
							searchObjects:false,
							dateRange:false,
							home:false,
							settings:false,
							cells: {},
							childObjs:1,
							data:function(paged, callback){
								
								sb.data.db.obj.getAll(obj.object_type, function(ret){
		
									callback(ret);
									
								}, 1, paged);
								
							}
						}
					});
					
				}
				
				dom.settingsNav.cont['nav-'+ obj.object_type].text('<i class="fa fa-circle"></i> '+ obj.name +' <i class="fa fa-arrow-right"></i>');
				dom.settingsNav.cont['nav-'+ obj.object_type].css('pda-btn-medium pda-btnOutline-primary pda-transparent');	
				
				_.each(settings, function(o){
					
					if(obj.name != o.name){
						dom.settingsNav.cont['nav-'+ o.object_type].text('<i class="fa fa-circle-o"></i> '+ o.name);
						dom.settingsNav.cont['nav-'+ o.object_type].css('pda-btn-medium pda-btnOutline-blue pda-transparent');
					}
					
				});
				
			});
			
		}
				
		dom.makeNode('settingsNav', 'column', {width:4}).makeNode('cont', 'container', {css:''});
		dom.makeNode('settings', 'column', {width:8}).makeNode('cont', 'container', {css:''});
		
		dom.settingsNav.cont.makeNode('title', 'headerText', {text:'Settings', size:'small'});
		//dom.settingsNav.cont.makeNode('titleBreak', 'lineBreak', {});
		
		dom.settings.cont.makeNode('startText', 'headerText', {text:'<br />Please choose a setting to edit', size:'xx-small', css:'text-center'});
				
		_.each(_.sortBy(settings, 'name'), function(obj){
				
			dom.settingsNav.cont.makeNode('nav-'+ obj.object_type, 'button', {text:'<i class="fa fa-circle-o"></i> '+ obj.name +'', css:'pda-btn-medium pda-btnOutline-blue pda-transparent'}).notify('click', {
				type:'crud-table-run',
				data:{
					run:displaySetting.bind(this, dom, obj)
				}
			}, sb.moduleId);
						
		});
		
		dom.patch();
				
	}
	
	function getFormField(formFields, field, key, formSetup, objectData) {

		if(
			key !== 'is_template'
			&& (
				!field.immutable && typeof formFields === 'undefined'
				|| !field.immutable && _.isEmpty(formFields)
				|| !field.immutable && formFields.hasOwnProperty(key)
			)
		){

			switch(field.type){

				case 'date':
				var dateFormat;
				if(field.displayFormat){
					dateFormat = field.displayFormat;
				}else{
					dateFormat = 'M/D/YYYY, h:mm a';
				}
				formSetup[key] = {
					type: 'date',
					name: key,
					label: field.name,
					dateFormat: dateFormat
				};
				break;
				
				case 'time':

				var dateFormat;
				if(field.displayFormat){
					dateFormat = field.displayFormat;
				}else{
					dateFormat = 'h:mma';
				}
				formSetup[key] = {
					type: 'time',
					name: key,
					label: field.name,
					dateFormat: dateFormat
				};
				break;

				case 'string':
				formSetup[key] = {
					type: 'text',
					name: key,
					label: field.name
				};
				break;

				case 'object':
				formSetup[key] = {
					type: 'textbox',
					name: key,
					label: field.name
				};
				break;

				case 'int':
				formSetup[key] = {
					type: 'text',
					name: key,
					label: field.name,
					step: 1
				};
				break;

				case 'objectId':

				if(field.objectType === 'file_meta_data'){
					formSetup[key]= {
						type: 'file-upload',
						name: key,
						label: field.name,
						rename: true
					};
				}else{
					formSetup[key] = {
						type: 'select',
						name: key,
						label: field.name,
						options: []
					};

					_.each(field.options, function(label, name){
	
						var optionVal = name;
						if(!isNaN(parseInt(optionVal))){
							optionVal = parseInt(optionVal);
						}
	
						formSetup[key].options.push({
							name: label,
							value: optionVal
						});
	
					});					
				}
				break;

				case 'float':
				formSetup[key] = {
					type: 'text',
					name: key,
					label: field.name
				};
				break;

				case 'select':
				
				formSetup[key] = {
					type: 'select',
					name: key,
					label: field.name,
					options: []
				};

				_.each(field.options, function(label, name){

					var optionVal = name;
					if(!isNaN(parseInt(optionVal))){
						optionVal = parseInt(optionVal);
					}

					formSetup[key].options.push({
						name: label,
						value: optionVal
					});

				});
				break;
								
				case 'multi-select':

				formSetup[key] = {
					type: 'checkbox',
					name: key,
					label: field.name,
					selectAll:true,
					options: []
				};

				_.each(field.options, function(label, name){

					var optionVal = name;
					if(!isNaN(parseInt(optionVal))){
						optionVal = parseInt(optionVal);
					}

					formSetup[key].options.push({
						name: key,
						value: optionVal,
						label: label
					});

				});
				break;

				case 'usd':
				formSetup[key] = {
					type: 'usd',
					name: key,
					label: field.name
				};
				break;

			}

			if(typeof formFields !== 'undefined' && formFields.hasOwnProperty(key)){

				if(formFields[key].hasOwnProperty('type')){
					formSetup[key].type = formFields[key].type;
				}
				if(formFields[key].hasOwnProperty('value')){
					formSetup[key].value = formFields[key].value;
				}
				if(formFields[key].hasOwnProperty('options')){
					formSetup[key].options = formFields[key].options;
				}
				_.each(formFields[key], function(arg, argKey){
					formSetup[key][argKey] = arg;
				});

			}
			
			// pre-fill data if its an edit form
			if(typeof objectData !== 'undefined' && !_.isEmpty(objectData)){

				if(typeof objectData[key] !== 'undefined' && typeof formSetup[key] !== 'undefined' && !formSetup[key].hasOwnProperty('value')){

					formSetup[key].value = objectData[key];

					if(field.type == 'select'){

						if(typeof objectData[key] === 'object'){

							if(objectData[key]){
								if(objectData[key].id){
									formSetup[key].options[_.findIndex(formSetup[key].options, {value: objectData[key].id})].selected = true;
								}
							}							
							
						}else{
							
							if(_.findIndex(formSetup[key].options, {value: objectData[key]}) > -1){

								formSetup[key].options[_.findIndex(formSetup[key].options, {value: objectData[key]})].selected = true;
	
							}else if(_.findIndex(formSetup[key].options, {value: parseInt(objectData[key])}) > -1){
	
								formSetup[key].options[_.findIndex(formSetup[key].options, {value: parseInt(objectData[key])})].selected = true;
	
							}
							
						}

					}else if(field.type == 'multi-select'){

						_.each(formSetup[key].options, function(option, optionKey){

							if(_.indexOf(objectData[key], option.value) > -1 || typeof _.pluck(objectData[key], 'id') !== 'undefined' && _.indexOf(_.pluck(objectData[key], 'id'), option.value) > -1){

								formSetup[key].options[optionKey].checked = true;

							}

						});

					}else if(field.type == 'objectId' && field.objectType == 'file_meta_data' && formSetup[key].value > 0){

						formSetup[key].label += ' <small class="text-danger">(replace current file)</small>';

					}else if(field.type == 'time'){
						
						formSetup[key].value = moment(objectData[key], 'h:mma');
						
					}
				}
			}
		}

		return formSetup;
	}
	
	// main table UI functions	
	function buildTableUI(){

		var currentState = {};
		
		// the function that runs when the download button is clicked
		function downloadButtonAction(allRows, dom){

			var data = [];

			_.each(allRows, function(o){
				
				data.push(
					[
						setupData.headerButtons.download.process(
							compCache.getSingle(o)
						)
					]
				);
				
			});
			
			var dataToConvert = [],
				headerRow = [];
			
			_.each(data[0], function(o){

				_.each(o, function(field){				
					
					headerRow.push(field.name);
					
				});
				
			});

			dataToConvert.push(headerRow);
			
			_.each(data, function(o){
											
				_.each(o, function(field){

					var singleRow = [];
					
					_.each(field, function(fieldObj){
						
						singleRow.push(fieldObj.value);
						
					});				
					
					dataToConvert.push(singleRow);
					
				});
				
				
			});

			var csvContent = "data:attachement/csv;charset=utf-8,";
			
			dataToConvert.forEach(function(infoArray, index){
			
				dataString = infoArray.join("\t");
				csvContent += index < data.length ? dataString+ "\n" : dataString;
			
			}); 
			
			var encodedUri = encodeURI(csvContent);
			var link = document.createElement("a");
			link.setAttribute("href", encodedUri);
			link.setAttribute("download", "download.tsv");
			document.body.appendChild(link); // Required for FF
			
			link.click();
															
		}
		
		function getSelectDisplayStringForObjectField(value, bp){

			var count = 0;
				selectDisplayKeys = [],
				ret = '';
			
			if(bp){

				switch(bp.type){
					
					//case 'select':
					case 'objectId':
	
						selectDisplayKeys = bp.selectDisplay.replace(/[\[\]&]+/g, '').split(' ');

						_.each(selectDisplayKeys, function(key){
							
							if(count > 0){
								ret += ' '
							}
							
							if(value){
								ret += value[key];
							}
							
							count++;
							
						});
						
						return ret;
					
						break;
					
					case 'multi-select':	
					case 'objectIds':
                        
                        if ( bp.selectDisplay ){
                            selectDisplayKeys = bp.selectDisplay.replace(/[\[\]&]+/g, '').split(' ');

                            if(value){
    
                                _.each(value, function(v){
                                    
                                    if(v !== null){
                                        
                                        var i = 0;
                                        
                                        _.each(selectDisplayKeys, function(key){
    
                                            if(i > 0){
                                                ret += ' ';
                                            }
                                            
                                            ret += v[key];
                                            
                                            i++;
                                            
                                        });
    
                                        count++;
                                        
                                        if(count > 0){
                                            if(count == value.length){
                                                
                                            }else{
                                                ret += ', ';
                                            }
                                        };
        
                                    }
                                                                
                                });
                            
                            }
                        }
						
						return ret;
					
						break;
						
					case 'select':

						switch(typeof bp.options){
							
							case 'array':
							
								return bp.options[value];
							
								break;
								
							case 'object':
							
								if(value){
									if(value.id){
										return bp.options[value.id];
									}else{
										return bp.options[value];
									}
								}else{
									return '<i>Not selected</i>';
								}
							
								break;	
							
						}
						
					case 'usd':
					
						return '$'+(value/100).formatMoney(2);
					
						break;	
						
					default:
					
						return value;	
					
				}	
				
			}else{
				
				return value;
				
			}	
		
		}
			
		function getState(){
			return currentState;
		}
		
		// default loading for the table, also puts a loader in the page length popover
		// arg on = true/false
		function loading(on, target){

			if(on){

				if(target){
					
					switch(target){
						
						case 'download':
						
							this.right.btns.makeNode('download', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-circle-o-notch fa-spin"></i> Downloading'});
							
							this.right.btns.patch();

							break;
						
					}
					
				}else{
					
					this.left.counter.empty();
					
					this.left.counter.makeNode('counterBreak', 'div', {text:'<br />'});
					
					if(setupData.drawComplete === null){
						this.left.counter.makeNode('loader', 'loader', {css:'pull-left'});
					}
					this.left.counter.makeNode('text', 'headerText', {size:'xx-small', text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading...'});
					this.left.counter.patch();
				
					if(this.left.pageBtns.icon){
						
// 						this.pop.text.empty();
						
// 						this.pop.text.makeNode('text', 'headerText', {size:'xx-small', text:'<i class="fa fa-circle-o-notch fa-spin"></i> Loading...'});
						
// 						this.pop.text.patch();
					
					}
					
					if(this.right.btns.reload){
						
						//this.btns.makeNode('reload', 'button', {css:'pda-btn-blue', text:'<i class="fa fa-refresh fa-spin"></i> Loading' });
						
						this.right.btns.reload.text('<i class="fa fa-refresh fa-spin"></i> Loading');
						this.right.btns.reload.css('pda-btn-primary');
						
						//this.btns.patch();
						
					}
					
				}
				
			}else{
				
				if(target){
					
					switch(target){
						
						case 'download':
						
							this.right.btns.makeNode('download', 'button', {css:'pda-btn-blue', text:'<i class="fa fa-download"></i> Download'});
						
							break;
						
					}
					
				}else{
					
					delete this.left.counter.loader;
					
					this.left.counter.makeNode('counterBreak', 'div', {text:'<br />'});
					
					this.left.counter.makeNode('text', 'headerText', {size:'xx-small', text:counterString});
					
					this.right.btns.makeNode('reload', 'button', {css:'pda-btn-blue', text:'<i class="fa fa-refresh"></i> Reload' }).notify('click', {
						type:'crud-table-header-button-clicked',
						data:{
							buttonObj:{
								name:'Reload'
							}
						}
					}, sb.moduleId);
				
				}
					
				this.right.btns.patch();
				
			}
			
			this.left.counter.patch();
			
		}
		
		// build the header buttons on the right of the table (create, reload...)
		function buttons(buttons, position){
			
// 			this.right.btns.empty();
			this.right.rowBtns.empty();
			
			position = 'btns';
			
			var domObj = this;

			if(setupData.download != false){

				buttons.download = {
					name:'<i class="fa fa-download"></i> Download',
					css:'pda-btn-blue',
					domType:'download',
					download:true,
					process:setupData.download,
					action:downloadButtonAction
				}
				
			}
						
			// loop over each buttons
			_.each(buttons, function(b, name){
				
				switch(position){
					
					case 'rowBtns':
					
						b.css += ' pda-btn-x-small';
					
						break;
					
				}
				
				switch(name){
					
					case 'reload':
					
						this.right[position].makeNode(name, 'button', {css:b.css, text:'<i class="fa fa-refresh"></i> Reload' });
					
						break;
					
					default:
					
						// check the button value type
						switch(typeof b){
								
							case 'object':
		
								this.right[position].makeNode(name, 'button', {css:b.css, text: b.name });
							
								break;	
							
						}
					
				}
				
				this.right[position][name].notify('click', {
					type:'crud-table-header-button-clicked',
					data:{
						buttonObj:b
					}
				}, sb.moduleId);
									
			}, this);

			this.right.btns.patch();
			this.right.rowBtns.patch();
			
		}
		
		// build the paging buttons on the left of the table
		function pagingButtons(pageData, currentOffset){

			// figuring out the 'showing x - y of z' string for display
			var throughVal = (pageData.offset + pageData.pageLength),
				showingVal = (pageData.offset + 1 );
			
			if(pageData.offset > 0){
				showingVal = (pageData.offset);
				throughVal = (pageData.offset + pageData.pageLength) - 1;
			}
			
			if(throughVal > pageData.totalRecords){
				throughVal = pageData.totalRecords;
			}
			
			throughVal = sb.dom.formatNumber(throughVal);
			showingVal = sb.dom.formatNumber(showingVal);
			
			this.headerBtns.left.counter.empty();
			this.headerBtns.left.pageBtns.empty();
			this.pageBtnsLower.empty();
			
			// if there is more than 1 page of data, show the pagination buttons
			if(pageData.totalPages >= 1){
				
				// create page length buttons and form with options
// 				this.pageBtns.makeNode('icon', 'button', {text:'Page Length <i class="fa fa-caret-square-o-down"></i>'});
// 				this.makeNode('pop', 'popover', {parent:this.pageBtns.icon, header:'Page Length'});

				var formSetup =	[
						{
							name:'15 rows',
							value:15
						},
						{
							name:'50 rows',
							value:50
						},
						{
							name:'100 rows',
							value:100
						},
						{
							name:'200 rows',
							value:200
						}
					];
					
				_.findWhere(formSetup, {value:pageData.pageLength}).selected = true;
					
				this.headerBtns.left.pageBtns.makeNode('pageLength', 'div', {
						css:'ui floating dropdown labeled search icon button',
						listener:{
							type:'dropdown',
							values:formSetup,
							placeholder:'15',
							onChange:function(value){

								if(+value && +pageData.pageLength != +value){
									sb.notify({
										type:'crud-page-length-change',
										data:{
											pageLength:value
										}
									});
								}
								
							}
						}
					})
					.makeNode('icon', 'div', {tag:'i', css:'caret down icon'});
					
				/*
this.pageBtns.pageLength.notify('change', {
					type:'crud-page-length-change',
					run:function(){
						form:this.pageBtns.pageLength
					}
				}, sb.moduleId);
*/
				
				/*
this.pop.makeNode('form', 'form', formSetup).notify('change', {
					type:'crud-page-length-change',
					data:{
						form:this.pop.form
					}
				}, sb.moduleId);
*/
					
				this.headerBtns.left.pageBtns.pageLength.makeNode('text', 'div', {css:'default text', tag:'span', text:'Page Length'});
				
				// start button that takes the user to the first page
				this.headerBtns.left.pageBtns.makeNode('start', 'div', {css:'ui button', text:'<i class="fa fa-angle-double-left"></i>'}).notify('click', {
					type:'change-crud-table-page',
					data:{
						offset:0
					}
				}, sb.moduleId);
				
				this.pageBtnsLower.makeNode('startLower', 'div', {css:'ui button', text: '<i class="fa fa-angle-double-left"></i>'})
					.notify('click', {
						type:'change-crud-table-page',
						data:{
							offset:0
						}
					}, sb.moduleId);
								
				var count = 0,
					buttonsOnScreen = 0,
					endOffset = 0,
					nextOffset = 0,
					backOffset = 0,
					totalPages = pageData.totalPages;
										
				// create the individual page buttons based on how may pages of data are preset
				while( totalPages > 0 ){

					// set the current loop offset
					var offset = 0;
					
					if(count > 0){
						offset = (((count) * pageData.pageLength));
					}
					
					// if the loop offset is less than the total amount of records, continue
					//if(offset < pageData.totalRecords){
					if(1==1){
						
						var css = 'ui button';
						
						// if the current loop offset is equal to the current page offset, change the color of the button to appear highlighted
						if(offset == pageData.offset){
							
							css = 'ui active button';
							
							// set the next and back button offsets - to be used in the click notifications
							if(offset > 0){
								
								nextOffset = offset + pageData.pageLength;
								backOffset = offset - pageData.pageLength;
								
							}else{
								
								nextOffset = pageData.pageLength + 1;
								backOffset = 0;
								
							}
							
						}
						
						if(pageData.currentPage < 7){
								
							if(buttonsOnScreen < 8){
							
								this.headerBtns.left.pageBtns.makeNode('page-'+count, 'div', {css:css, text: (count + 1) }).notify('click', {
									type:'change-crud-table-page',
									data:{
										offset: offset
									}
								}, sb.moduleId)
								
								this.pageBtnsLower.makeNode('page-'+count, 'div', {css:css, text: (count + 1) }).notify('click', {
									type:'change-crud-table-page',
									data:{
										offset: offset
									}
								}, sb.moduleId);
								
								buttonsOnScreen++;
							
							}else{
								
								this.headerBtns.left.pageBtns.makeNode('page-end', 'div', {css:'ui button', text:'...' });
								
								this.pageBtnsLower.makeNode('page-end', 'div', {css:'ui button', text:'...' });
								
								buttonsOnScreen++;
							
							}
							
						}
						
						if(pageData.currentPage >= 9){

							if(
								count == (pageData.currentPage - 1)
								|| count == (pageData.currentPage - 2)
								|| count == (pageData.currentPage - 3)
								|| count == (pageData.currentPage - 4)
								|| count == (pageData.currentPage)
								|| count == (pageData.currentPage + 1)
								|| count == (pageData.currentPage + 2)
								|| count == (pageData.currentPage + 3)
								|| count == (pageData.currentPage + 4)
							){
								
								this.headerBtns.left.pageBtns.makeNode('page-'+count, 'button', {css:css, text: (count + 1) }).notify('click', {
									type:'change-crud-table-page',
									data:{
										offset: offset
									}
								}, sb.moduleId);
								
								this.pageBtnsLower.makeNode('page-'+count, 'button', {css:css, text: (count + 1) }).notify('click', {
									type:'change-crud-table-page',
									data:{
										offset: offset
									}
								}, sb.moduleId);
								
								buttonsOnScreen++;
								
							}else{
								
								var positionString = 'start';
								
								if(count < pageData.currentPage){
									positionString = 'end';
								}
								
								this.headerBtns.left.pageBtns.makeNode('page-skip-'+positionString, 'div', {css:'ui button', text:'...' });
								
								this.pageBtnsLower.makeNode('page-skip-'+positionString, 'div', {css:'ui button', text:'...' });
								
							}
							
						}
																		
						endOffset = offset;
						
					}					
					
					count++;
					totalPages--;
					
				}
								
				// last button that takes the user to the last page
				this.headerBtns.left.pageBtns.makeNode('end', 'button', {text:'<i class="fa fa-angle-double-right"></i>'}).notify('click', {
					type:'change-crud-table-page',
					data:{
						offset: endOffset
					}
				}, sb.moduleId);
				
				this.pageBtnsLower.makeNode('endLower', 'button', {text: '<i class="fa fa-angle-double-right"></i>'})
					.notify('click', {
						type:'change-crud-table-page',
						data:{
							offset: endOffset
						}
					}, sb.moduleId);
				
				// count line shown in the page length popover
				counterString = 'Showing '+ showingVal +' - '+ throughVal +' of '+ sb.dom.formatNumber(pageData.totalRecords);
			
			}
			
			// count line show above the table to the left, under the pagination buttons
			counterString = 'Showing '+ showingVal +' - '+ throughVal +' of '+ sb.dom.formatNumber(pageData.totalRecords);
			
			this.headerBtns.left.counter.makeNode('counterBreak', 'div', {text:'<br />'});
			this.headerBtns.left.counter.makeNode('text', 'div', {text:'<br /><b>'+counterString+'</b>'});
			this.pageBtnsLower.makeNode('counterBreak2', 'lineBreak', {});
			this.pageBtnsLower.makeNode('text', 'div', {text:'<br /><b>'+counterString+'</b>'});
						
			this.patch();
			
			if(setupData.onDraw){
				
				setupData.onDraw();
				
			}
			
		}
		
		function removeSearchTerm(){
			delete currentState.search;
		}
		
		// process the checkbox click for each row
		function rowSelect(rowObjIds, all){
			
			if(all){
				
				if(selectedRows.length != currentState.data.length){
					
					selectedRows = [];
					
				}
				
			}

			_.each(rowObjIds, function(rowObjId){
				
				if(setupData.feedUI){

					this.table.feed['row-'+rowObjId].checkbox.empty();
	
					if(_.contains(selectedRows, +rowObjId)){
						
						this.table.feed['row-'+rowObjId].checkbox.makeNode('text', 'text', {text:'<i class="text-muted square outline icon"></i>'});
						
						selectedRows = _.reject(selectedRows, function(id){
							return id == +rowObjId;
						});
		
					}else{
						
						this.table.feed['row-'+rowObjId].checkbox.makeNode('text', 'text', {text:'<i class="square check icon"></i>'});
						
						selectedRows.push(+rowObjId);
						
					}
					
				}else{
				
					this.table.table.body['row-'+rowObjId].checkbox.empty();
	
					if(_.contains(selectedRows, +rowObjId)){
						
						this.table.table.body['row-'+rowObjId].checkbox.makeNode('text', 'text', {text:'<i class="text-muted square outline icon"></i>'});
						
						selectedRows = _.reject(selectedRows, function(id){
							return id == +rowObjId;
						});
		
					}else{
						
						this.table.table.body['row-'+rowObjId].checkbox.makeNode('text', 'text', {text:'<i class="square check icon"></i>'});
						
						selectedRows.push(+rowObjId);
						
					}
					
				}
				
			}, this);

			if(setupData.feedUI){
				
				if(selectedRows.length > 0){
				
					if(currentState.data.length == selectedRows.length){
						
						this.table.feed.header.checkbox.makeNode('text', 'text', {css:'text-success', text:'<p class="text-success"><i class="square check icon"></i></p>'});
					
					}else{
						
						this.table.feed.header.checkbox.makeNode('text', 'text', {css:'text-warning', text:'<p class="text-warning"><i class="square check icon"></i></p>'});
						
					}
					
				}else{
					
					this.table.feed.header.checkbox.makeNode('text', 'text', {text:'<i class="text-muted square outline icon"></i>'});
				
				}
				
				this.table.feed.patch();
				
			}else{
				
				if(selectedRows.length > 0){
					
					if(currentState.data.length == selectedRows.length){
						
						this.table.table.header.row.checkbox.makeNode('text', 'text', {css:'text-success', text:'<p class="text-success"><i class="square check icon"></i></p>'});
					
					}else{
						
						this.table.table.header.row.checkbox.makeNode('text', 'text', {css:'text-warning', text:'<p class="text-warning"><i class="square check icon"></i></p>'});
						
					}
					
				}else{
					
					this.table.table.header.row.checkbox.makeNode('text', 'text', {text:'<i class="text-muted square outline icon"></i>'});
				
				}
				
				this.table.table.header.row.checkbox.patch();
				this.table.table.body.patch();
				
			}
			
		}
		
		function search(state, start, end, currentFilterSelection){

			switch(state){
				
				case 'dateRange':
					
					var range = '';
					
					if(start){
						
						range = start + ' - '+ end;
						
					}else{
						
						range = 'All Time';
						
					}
					
					this.headerBtns.right.btns.makeNode('date', 'button', {css:'pull-right pda-btnOutline-blue', text:'<i class="fa fa-calendar"></i> <i class="fa fa-arrows-h"></i> <i class="fa fa-calendar"></i> '+range});
					
					WebuiPopovers.hideAll();
					
					this.headerBtns.right.btns.patch();
					
					break;
				
				case 'hide':
				
/*
					this.search.empty();
				
					this.search.makeNode('btn', 'button', {css:'pull-right pda-btn-red', text:'<i class="fa fa-times"></i> Cancel Search'}).notify('click', {
						type:'crud-table-cancel-search',
						data:{}
					}, sb.moduleId);
					
					this.search.patch();
*/
				
					break;
					
				case 'loading':

/*
					this.search.btn.pop.text.empty();
					
					this.search.btn.pop.text.makeNode('btn', 'text', {text:'<a class="pda-Btn pda-btn-small"><i class="fa fa-circle-o-notch fa-spin"></i> Searching</a>'});
					
					this.search.btn.pop.text.patch();
*/
					
					break;
					
				case 'noItems':

/*
					this.search.btn.pop.text.empty();
					
					this.search.btn.pop.text.makeNode('btn', 'text', {text:'<a class="pda-Btn pda-btn-small pda-btnOutline-red"><i class="fa fa-times"></i> No Results</a>'});
					
					this.search.btn.pop.text.patch();
*/
					
					var dom = this;
					
					//setTimeout(function(){
						
/*
						dom.search.btn.pop.text.empty();
						
						dom.search.btn.pop.text.makeNode('btn', 'text', {text:'<a class="pda-Btn pda-btn-small"><i class="fa fa-search"></i> Search</a>'}).notify('click', {
							type:'crud-table-start-search',
							data:{
								form:dom.search.btn.pop.form
							}
						}, sb.moduleId);
						
						dom.search.btn.pop.text.patch();
*/
						
					//}, 1200);
					
					break;
															
				default:
				
// 					this.search.empty();
					
					if(setupData.dateRange){
						
						this.search.makeNode('date', 'button', {css:'pull-right pda-btnOutline-blue', text:'<i class="fa fa-calendar"></i> <i class="fa fa-arrows-h"></i> <i class="fa fa-calendar"></i> All Time'});
						
					}
					
/*
					if(!_.isEmpty(setupData.searchObjects)){
						
						this.search.makeNode('btn', 'button', {css:'pull-right pda-btnOutline-blue', text:'<i class="fa fa-search"></i> Search'});
					
						this.search.btn.makeNode('pop', 'popover', {parent:this.search.btn, header:'Search'});
						
						this.search.btn.pop.makeNode('form', 'form', {
							searchObject:{
								type:'select',
								name:'searchObject',
								label:'Search In',
								options:setupData.searchObjects
							},
							searchTerm:{
								name:'searchTerm',
								label:'Search For',
								type:'text'
							}
						});

						this.search.btn.pop.makeNode('text', 'headerText', {size:'xx-small', text:''}).makeNode('btn', 'text', {text:'<a class="pda-Btn pda-btn-small"><i class="fa fa-search"></i> Search</a>'}).notify('click', {
							type:'crud-table-start-search',
							data:{
								form:this.search.btn.pop.form
							}
						}, sb.moduleId);
						
					}
*/
					
					if(setupData.filters){
						
						switch(typeof setupData.filters){
							
							case 'function':

								var searchDom = this;

								searchDom.headerBtns.right.btns.makeNode('filter', 'button', {css:'pull-right pda-btnOutline-blue disabled', text:'<i class="fa fa-circle-o-notch"></i> Filter'});

								setupData.filters(function(filters){

									setupData.filters = filters;
									
									var btnsArea = searchDom.headerBtns.right.btns;
									btnsArea.makeNode('filter', 'button', {css:'pull-right pda-btnOutline-blue', text:'<i class="fa fa-filter"></i> Filter'});
				
									btnsArea.filter.makeNode('pop', 'popover', {parent:searchDom.headerBtns.right.btns.filter, placement: 'bottom-left'});

									var formSetup = {};
									_.each(setupData.filters, function(f){
																	
										formSetup[f.field] = {
											type:f.type,
											name:f.field,
											label:f.name,
											options:f.options
										};
										
									});
									
									btnsArea.filter.pop.makeNode('form', 'form', formSetup);
									
									btnsArea.filter.pop.makeNode('text', 'headerText', {size:'xx-small', text:''}).makeNode('btn', 'text', {text:'<a class="pda-Btn pda-btn-small"><i class="fa fa-filter"></i> Apply Filter</a>'}).notify('click', {
										type:'crud-table-start-filter',
										data:{
											form:btnsArea.filter.pop.form
										}
									}, sb.moduleId);
									
									btnsArea.patch();
																		
								}, blueprint);
							
								break;
								
							default:
							
								this.headerBtns.right.btns.makeNode('filter', 'button', {css:'pull-right pda-btnOutline-blue', text:'<i class="fa fa-filter"></i> Filter'});
			
								this.headerBtns.right.btns.filter.makeNode('pop', 'popover', {parent:this.headerBtns.right.btns.filter, placement: 'auto'});
								
								var formSetup = {};
								_.each(setupData.filters, function(f){
																
									formSetup[f.field] = {
										type:f.type,
										name:f.field,
										label:f.name,
										options:f.options
									};
									
									if(currentFilterSelection && currentFilterSelection[f.field] && Array.isArray(currentFilterSelection[f.field].values)){
										
										var currentSelInts = _.map(currentFilterSelection[f.field].values, function(r){ return parseInt(r); });
										_.each(formSetup[f.field].options, function(option){
											
											if(_.contains(currentFilterSelection[f.field].values, option.value) || _.contains(currentSelInts, option.value)){
												option.checked = true;
											}else{
												option.checked = false;
											}
											
										});
										
									}
									
								});

								this.headerBtns.right.btns.filter.pop.makeNode('form', 'form', formSetup);
								
								this.headerBtns.right.btns.filter.pop.makeNode('text', 'headerText', {size:'xx-small', text:''}).makeNode('btn', 'text', {text:'<a class="pda-Btn pda-btn-small"><i class="fa fa-filter"></i> Apply Filter</a>'}).notify('click', {
									type:'crud-table-start-filter',
									data:{
										form:this.headerBtns.right.btns.filter.pop.form
									}
								}, sb.moduleId);
							
						}
											
					}
										
					this.patch();
														
			}
			
			if(setupData.dateRange && this.search.date){
						
				this.headerBtns.right.btns.date.makeNode('pop', 'popover', {parent:this.search.date, header:'Search'});
				
				var dom = this;
				
				this.headerBtns.right.btns.date.pop.makeNode('form', 'form', {
					dateType:{
						change:function(form, val){

							switch(val){
								
								case 'custom':
									
									form.start.type = 'date';
									form.end.type = 'date';
									form.dateType.options[1].selected = true;
									form.dateType.options[0].selected = false;
									
									break;
									
								case 'alltime':
									
									form.start.type = 'hidden';
									form.end.type = 'hidden';
									form.dateType.options[0].selected = true;
									form.dateType.options[1].selected = false;
									
									break;	
								
							}
																
							dom.headerBtns.right.btns.date.pop.makeNode('form', 'form', form);

							dom.headerBtns.right.btns.date.pop.patch();
												
						},
						type:'select',
						name:'dateType',
						label:'',
						options:[
							{value:'alltime',name:'All Time'},
							{value:'custom', name:'Custom'}
						]
					},
					start:{
						type:'hidden',
						name:'start',
						label:'Start Date',
						dateFormat:'YYYY-MM-DD',
						value:moment('2017-08-10', 'YYYY-MM-DD').format('YYYY-MM-DD')
					},
					end:{
						name:'end',
						label:'End Date',
						type:'hidden',
						dateFormat:'YYYY-MM-DD',
						value:moment('2017-08-19', 'YYYY-MM-DD').format('YYYY-MM-DD')
					}
				});
				
				this.headerBtns.right.btns.date.pop.makeNode('text', 'headerText', {size:'xx-small', text:''}).makeNode('btn', 'text', {text:'<a class="pda-Btn pda-btn-small"><i class="fa fa-eye"></i> View</a>'}).notify('click', {
					type:'crud-table-date-range-filter',
					data:{
						form:this.search.date.pop.form
					}
				}, sb.moduleId);
				
				this.patch();
				
			}
			
		}
		
		function searchBar(){
			
			var searchLoading = false;
			
			function initialDisplay(dom){

				dom.makeNode('open', 'text', {text:'<i class="fa fa-caret-down"></i> Search'}).notify('click', {
					type:'crud-table-run',
					data:{
						run:showSearch.bind(this, dom)
					}
				}, sb.moduleId);
				
				dom.patch();
				
			}
						
			function performSearch(dom){

				var searchTerm = dom.col2.searchBar.process().fields.searchTerm.value;
				
				if(searchTerm.length < 2){
					
					sb.dom.alerts.alert('', 'Please enter more characters.', 'warning');
					return;
					
				}
				
				dom.col4.btn.loading();
								
				var searchField = dom.col1.searchField.process().fields.searchField.value,
		        	searchObj = _.where(setupData.searchObjects, {value:searchField})[0],
		        	paged = {
						page:0,
						pageLength:50,
						paged:true,
						sortCol:searchObj.value,
						sortOrder:'desc'
					},
					where = {};
					
				if(setupData.queryObj !== null){
					where = setupData.queryObj;
				}

				if(+searchTerm > 0 && searchObj.value != 'zip'){
			
					searchObj.term = +searchTerm;
					
					where[searchObj.value] = searchObj.term;
					
				}else{
					
					searchObj.term = searchTerm;
					
					where[searchObj.value] = {
						type:'contains',
						value:searchObj.term.toString()
					};
					
				}
				
				if(searchObj.join){
					searchObj.objectType = blueprint[searchObj.join].objectType;
				}else{
					searchObj.objectType = setupData.objectType;
				}
					
				where.paged = paged;
				
				where.childObjs = setupData.childObjs;
				
				currentState.search = {
					field:searchField,
					term:searchTerm
				};

		        sb.data.db.obj.getWhere(searchObj.objectType, where, function(ret){
			        
			        setupData.parseSearchData(ret, function(ret){
			        
						if(ret){
							
							if(searchObj.join){
						
								where = {};
							
								where.paged = paged;
								where.childObjs = setupData.childObjs;
								
								var joinOn = 'id';
									join = searchObj.join;
								
								if(searchObj.joinOn){
									joinOn = searchObj.joinOn;
									join = 'id';
								}
		
								where.paged.sortCol = 'date_created';
								where[join] = {
									type:'or',
									values:_.pluck(ret.data, joinOn)
								};
	
								sb.data.db.obj.getWhere(setupData.objectType, where, function(searchRet){
									
									if(searchRet){
										
										var parsed = compCache.parseData(searchRet, paged, {
											field:searchField,
											term:searchTerm
										});
						
										tableUI.state.buttons(setupData.headerButtons, 'btns');
										tableUI.state.table(parsed, searchObj);
										tableUI.state.pages(parsed);
																													
										searchLoading = false;
										
										delete dom.col3.loader;
										dom.col3.patch();
																			
									}else{
										
										// no search results
										var parsed = compCache.parseData(ret, paged, {
											field:searchField,
											term:searchTerm
										});
						
										tableUI.state.buttons(setupData.headerButtons, 'btns');
										tableUI.state.table(parsed, searchObj);
										tableUI.state.pages(parsed);
																			
										searchLoading = false;
										
										delete dom.col3.loader;
										dom.col3.patch();
										
									}
								
								});
								
							}else{
								
								var parsed = compCache.parseData(ret, paged, {
									field:searchField,
									term:searchTerm
								});
						
								tableUI.state.buttons(setupData.headerButtons, 'btns');
								tableUI.state.table(parsed, searchObj);
								tableUI.state.pages(parsed);
															
								searchLoading = false;
								
								delete dom.col3.loader;
								dom.col3.patch();
																
							}
							
						}else{
							
							// no search results
																						
							searchLoading = false;
							
							delete dom.col3.loader;
							dom.col3.patch();
							
						}	
						
					});
			        
		        });
				
			}

			function showSearch(dom){

				var col1Css = '',
					inputCSS = '',
					inputStyle = '';
				
				if(setupData.searchObjects.length === 1){

					col1Css = 'hidden';
					
					if(setupData.searchObjects[0].hasOwnProperty('inputStyle')){
						
						inputStyle = setupData.searchObjects[0].inputStyle;
						
					}
					
					if(setupData.searchObjects[0].hasOwnProperty('inputCSS')){
						
						inputCSS = setupData.searchObjects[0].inputCSS;
						
					}
					
				}
				
				_.each(setupData.searchObjects, function(){})
				
				dom.makeNode('titleCol', 'div', {css:'sixteen wide column'})
					.makeNode('title', 'div', {css:'ui medium header', text:'Search'});
				//dom.makeNode('titleBreak', 'div', {text:'<br />'});
				
				dom.makeNode('col1', 'column', {w:3, css:col1Css});
				dom.makeNode('col2', 'column', {w:11, css:''});
				dom.makeNode('col4', 'column', {w:2, css:''})
					.makeNode('btn', 'button', {css:'pda-btn-blue fluid', text:'Search'})
						.notify('click', {
							type:'crud-table-run',
							data:{
								run:function(dom){

									if(searchLoading == false){
								
										searchLoading = true;
										performSearch(dom);
										
									}
									
								}.bind({}, dom)
							}
						}, sb.moduleId);
				dom.makeNode('col3', 'column', {width:12, css:'noHeight'});
						
				dom.col2.makeNode('searchBar', 'form', {
					searchTerm:{
						name:'searchTerm',
						type:'text',
						label:'',
						style:inputStyle,
						css:inputCSS
					}
				});
				
				var searchFields = {
						searchField:{
							name:'searchField',
							type:'select',
							label:'',
							options:[]
						}
					};
				
				_.each(setupData.searchObjects, function(field){
					
					searchFields.searchField.options.push({
						label:'searchTerm',
						name:field.name,
						value:field.value
					});
					
				});
				
				dom.col1.makeNode('searchField', 'form', searchFields);
												
				dom.patch();				
												
			}
						
			//initialDisplay(this);
			showSearch(this);
			
		}
		
		function kanban(parsedData, searching) {
			
			var objs = parsedData.data;
			var	kanban_title = '';
			var	colColor = '';
			var	labelColor = '';
			var	colOpacity = 1;
			
			this.table.empty();
			
			if(setupData.kanban.hasOwnProperty('title') && setupData.kanban.title !== '') {
				kanban_title = '<h2>' + setupData.kanban.title + '</h2>';
			} else {
				kanban_title = '';
			}
			
			this.table.makeNode('kanbanTable', 'table', {
				columns: {
					title: kanban_title
				},
				tableCss: 'ui very basic table kanban-table',
				headerStyles:['background-color:transparent !important;']
			});
			
			this.table.kanbanTable.makeRow('headRow', [], {});
			this.table.kanbanTable.makeRow('bodyRow', [], {});
			
			this.table.kanbanTable.body.headRow.title.makeNode('cont', 'div', {});
			this.table.kanbanTable.body.bodyRow.title.makeNode('cont', 'div', {});
			
			_.each(setupData.kanban.groups, function(group, k) {

				if(group.hasOwnProperty('color') && group.color !== '') {
					colColor = group.color;
				} else {
					colColor = 'grey';
				}
				
				// HEAD ROW
				this.table.kanbanTable.body.headRow.title.cont.makeNode('colHead-' + k, 'container', {uiGrid: false, style: 'width: ' + (100 / setupData.kanban.groups.length) + '%;', css: 'pull-left'});
				
				this.table.kanbanTable.body.headRow.title.cont['colHead-' + k].makeNode('headCont', 'container', {uiGrid: true, css: colColor + ' ui basic segment'});
				
				this.table.kanbanTable.body.headRow.title.cont['colHead-' + k].headCont.makeNode('col1', 'column', {w: 10});
				this.table.kanbanTable.body.headRow.title.cont['colHead-' + k].headCont.makeNode('col2', 'column', {w: 6});
				
				this.table.kanbanTable.body.headRow.title.cont['colHead-' + k].headCont.col1.makeNode('name', 'div', {text: '<strong>' + group.name + '</strong>'});
				this.table.kanbanTable.body.headRow.title.cont['colHead-' + k].headCont.col2.makeNode('label', 'div', {text: '0', css: 'ui label pull-right'});
				
				// BODY ROW
				this.table.kanbanTable.body.bodyRow.title.cont.makeNode('colBody-' + k, 'container', {uiGrid: false, style: 'width: ' + (100 / setupData.kanban.groups.length) + '%;', css: 'pull-left'});
				
				if(setupData.kanban.hasOwnProperty('draggable') && typeof(setupData.kanban.draggable) === 'object') {
				 
					this.table.kanbanTable.body.bodyRow.title.cont['colBody-' + k].makeNode('bodyCont', 'container', {uiGrid: false, style: 'opacity: ' + colOpacity + ';', drag: {
						data: {
							group: k
						},
						accepts: true,
						moves: false
					}});
					 	
				} else {
					
					this.table.kanbanTable.body.bodyRow.title.cont['colBody-' + k].makeNode('bodyCont', 'container', {uiGrid: false, style: 'opacity: ' + colOpacity + ';'});
					
				}
				
				if(objs.length > 0) {
					
					var list = [];	
					var cardColor = '';
					
					_.each(objs, function(objId) {
					
						var o = compCache.getSingle(objId);
	
						list.push(o);
						
					}, this);
					
					list = _.groupBy(list, function(obj) {
						return obj[setupData.kanban.groupBy];
					});
					
					_.each(list, function(objArr, prop) {
						
						if(prop === group.value.toString()) {
							
							this.table.kanbanTable.body.headRow.title.cont['colHead-' + k].headCont.col2.makeNode('label', 'div', {text: objArr.length, css: 'ui label pull-right'});
							
							_.each(objArr, function(obj) {
								
								if(setupData.kanban.hasOwnProperty('cardColor')) {
								
									cardColor = setupData.kanban.cardColor(obj);
									
								}

								if(setupData.kanban.hasOwnProperty('draggable') && typeof(setupData.kanban.draggable) === 'object') {
									
									this.table.kanbanTable.body.bodyRow.title.cont['colBody-' + k].bodyCont.makeNode('card-' + obj.id, 'container', {uiGrid: false, css: 'ui '+ cardColor +' segment grab', drag: {
										moves: true,
										data: {
											type: setupData.kanban.draggable.data.type,
											obj: obj
										},
										drop: setupData.kanban.draggable.notify,
										accepts: false,
										copy: false
									}});
									
								} else {
									
									this.table.kanbanTable.body.bodyRow.title.cont['colBody-' + k].bodyCont.makeNode('card-' + obj.id, 'container', {uiGrid: false, css: 'ui '+ cardColor +' segment'});
									
								}
								
								setupData.kanban.cardUI(this.table.kanbanTable.body.bodyRow.title.cont['colBody-' + k].bodyCont['card-' + obj.id], obj);
								
							}, this);
							
						}
						
					}, this);
					
					if(!list.hasOwnProperty(group.value)) {
					
						this.table.kanbanTable.body.bodyRow.title.cont['colBody-' + k].bodyCont.makeNode('empty_text', 'div', {text: 'Empty', css: 'ui center aligned text-muted'});
						
					}
					
				}
				
				if(objs.length === 0) {
				
					this.table.kanbanTable.body.bodyRow.title.cont['colBody-' + k].bodyCont.makeNode('empty_text', 'div', {text: 'Empty', css: 'ui center aligned text-muted'});
					
				}
				
			}, this);
			
			this.table.patch();
			
		}
				
		function table(parsedData, searching){

			currentState = parsedData;

			var columns = {},
				objs = parsedData.data;

			if(setupData.rowSelection == true){
				columns.checkbox = '';
			}
			
			if(setupData.rowLink){
				columns.rowlink = '';
			}
			
			_.each(setupData.visibleCols, function(b, k){

				var css = 'text-muted',
					sortIcon = 'fa-arrows-v',
					bp = blueprint[k];
				
				if(paged.sortCol == k){
					
					css = 'text-bold';
					
					if(paged.sortDir == 'asc'){
					
						sortIcon = 'fa-long-arrow-up';
						
					}else{
						
						sortIcon = 'fa-long-arrow-down';
						
					}
					
				}
				
				if(blueprint.hasOwnProperty(k)){
				
					columns[k] = `<span style="">${b} <small><i class="fa ${sortIcon} ${css}"</i></small></span>`;
				
				}else{
					
					columns[k] = `<span style="">${b}</span>`;
					
				}
			});
			
			this.table.empty();
			
			var tableCSS = '';
			
			if(searching){

				this.table.makeNode('search', 'headerText', {css:'', text:'Search Results - <small>'+ searching.name +' contains "'+ searching.term +'"<small> <button class="ui red icon button"><i class="times icon"></i></button>'}).notify('click', {
					type:'crud-table-back-to-table',
					data:{}
				}, sb.moduleId);
				tableCSS = ' bg-info';
				
			}
			
			if(setupData.css) {
				
				if(setupData.css.table) {
				
					tableCSS += ' '+ setupData.css.table;
					
				}
			}

			if(setupData.css) {
				
				if(setupData.css.columns) {
					
					var columnCSS = setupData.css.columns;
					
				} else {
					
					var columnCSS = [];
					
				}
			}
			
			if(setupData.feedUI){
				
				var feedUICSS = 'ui stackable grid ',
					feedUIGrid = true,
					feedUIStyles = '',
					feedUITool = {
						nodeType:'container',
						tag:''
					};
				
				// New feedUI parentDom properties/arguments
				if(setupData.feedUI.hasOwnProperty('feedCSS')){
					feedUICSS += setupData.feedUI.feedCSS;
				}
				if(setupData.feedUI.hasOwnProperty('feedGrid')){
					
					feedUIGrid = setupData.feedUI.feedGrid;
															
					if(setupData.feedUI.hasOwnProperty('feedCSS')){
						
						if(feedUIGrid == false){
							feedUICSS = setupData.feedUI.feedCSS;
						}else{
							feedUICSS = 'ui stackable grid ' + setupData.feedUI.feedCSS;
						}
						
					}else{
						
						feedUICSS = '';
						
					}
					
				}
				if(setupData.feedUI.hasOwnProperty('feedStyle')){
					feedUIStyles = setupData.feedUI.feedStyle;
				}
				if(setupData.feedUI.hasOwnProperty('feedTag')){
					
					feedUITool.nodeType = 'div';
					feedUITool.tag = setupData.feedUI.feedTag;
					feedUIGrid = '';
					
					if(setupData.feedUI.hasOwnProperty('feedCSS')){
						feedUICSS = setupData.feedUI.feedCSS;
					}else{
						feedUICSS = '';
					}
					
				}
				
				if(setupData.feedUI.hasOwnProperty('feedCSS') || setupData.feedUI.hasOwnProperty('feedGrid') || setupData.feedUI.hasOwnProperty('feedStyle') || setupData.feedUI.hasOwnProperty('feedTag')){
					
					this.table.makeNode('feed', feedUITool.nodeType, {tag: feedUITool.tag, css: feedUICSS, uiGrid: feedUIGrid, style: feedUIStyles});
					
				}else{
					
					this.table.makeNode('feed', 'container', {css:'pda-container'});
					
				}
				
// 				this.table.makeNode('feed', 'container', {css:'pda-container'});
				
				this.table.feed.makeNode('header', 'container', {});
				
				this.table.feed.header.makeNode('loader', 'loader', {});
				
				this.table.patch();
				
				if(setupData.rowSelection == true){
					
					this.table.feed.header.makeNode('checkbox', 'div', {})
						.notify('click', {
							type:'crud-table-row-clicked',
							data:{
								allRows:true
							}
						}, sb.moduleId);
						
					this.table.feed.header.checkbox.makeNode('text', 'text', {text:'<p><i class="text-muted square outline icon"></i></p>'});
					
				}
				
				var objName;

				if(objs.length > 0){
					
					_.each(objs, function(objId){
						
						var o = compCache.getSingle(objId);
						
						var rowInfo = [];
						
						if(setupData.rowSelection == true){
							rowInfo.push('');
						}
						
						rowName = 'row-'+ o.id;
						
						// Old variable declarations and row properties/arguments
						/*
						var feedStyle = '',
							feedUIWidth = 12;
						if(setupData.feedUI.inline){
							feedStyle = 'display:inline-block;';
						}
						if(setupData.feedUI.width){
							feedUIWidth = setupData.feedUI.width;
						}
						*/
						
						var feedCSS = '',
							semanticWidth = 'width',
							feedStyle = '',
							feedUIWidth = 12;
						
						// Old feedUI row properties/arguments -- Still in use
						if(setupData.feedUI.css){
							feedCSS = setupData.feedUI.css;
						}
						if(setupData.feedUI.inline){
							feedStyle = 'display:inline-block;';
						}
						if(setupData.feedUI.style){
							feedStyle = setupData.feedUI.style;
						}
						if(setupData.feedUI.width){
							feedUIWidth = setupData.feedUI.width;
						}
						
						// New feedUI row properties/arguments
						if(feedUITool.nodeType == 'div' && feedUIGrid != false){
							semanticWidth = 'w';
							feedUIWidth = 16;
						}
						if(setupData.feedUI.hasOwnProperty('rowCSS')){
							feedCSS = setupData.feedUI.rowCSS;
						}
						if(setupData.feedUI.hasOwnProperty('rowStyle')){
							feedStyle = setupData.feedUI.rowStyle;
						}
						if(setupData.feedUI.hasOwnProperty('rowWidth')){
							semanticWidth = 'w';
							feedUIWidth = setupData.feedUI.rowWidth;
						}
						
						//this.table.feed.makeNode(rowName, 'div', {width:feedUIWidth, style:feedStyle, css:'item-fade-in-'+ Math.round(Math.random()*3)});

						if(setupData.feedUI.hasOwnProperty('rowCSS') || setupData.feedUI.hasOwnProperty('rowStyle') || setupData.feedUI.hasOwnProperty('rowWidth')){
							
							//this.table.feed.makeNode(rowName, 'column', {semanticWidth:feedUIWidth, style:feedStyle, css:feedCSS});
							
						}else{
							
							//this.table.feed.makeNode(rowName, 'column', {width:feedUIWidth, style:feedStyle});
							
						}
						
						// start row multi-selection boxes
						if(setupData.rowSelection == true){
							
							var icon = '<i class="text-muted square outline icon"></i>';
	
							if(selectedRows.indexOf(o.id) > -1){
								
								icon = '<i class="square check icon"></i>';
								
							}
							
							this.table.feed[rowName].makeNode('checkbox', 'div', {dataId:o.id, width:2});
							
							this.table.feed[rowName].checkbox.makeNode('text', 'text', {text:icon});
							
							this.table.feed[rowName].checkbox.notify('click', {
								type:'crud-table-row-clicked',
								data:{}
							}, sb.moduleId);
							
							this.table.feed[rowName].makeNode('container', 'div', {css:'container'});
							
						}else{
							
							this.table.feed.makeNode('container', 'div', {css:'container'});
							
						}
						
					}, this);
					
				}
				
				delete this.table.feed.header.loader;
				
			}else{
	
				this.table.makeNode(
					'table',
					'table',
					{
						css: 'ui basic striped table '+ tableCSS,
						clearCSS:true,
						columns: columns,
						columnCSS: columnCSS/*
,
						headerCss:'animated fadeIn'
*/
// 						style:'table-layout:fixed !important;min-width:100% !important;left:0px !important;'
					}
				);
				
				/*
this.table.table.header.row.listeners = [function(selector){
					$(selector).addClass('ui fixed sticky');
					$(selector).sticky('margin-top', '100px');
					$(selector).sticky();
				}];
*/
				
				var rowName;

				if(objs.length > 0){
					
					_.each(objs, function(objId){
											
						var o = compCache.getSingle(objId);

						var rowInfo = [];
						
						if(setupData.rowSelection == true){
							rowInfo.push('');
						}
						
						// start row link buttons
						if(setupData.rowLink){
							
							switch(setupData.rowLink.type){
								
								case 'edit':
								
									var rowLinkType = '';
									if(typeof setupData.rowLink.action == 'function'){
										rowLinkType = 'custom';
									}
									
									//Old Edit Icon -- black and white pencil
// 									rowInfo.push('<p class="text-center" data-id="'+ o.id +'" data-id2="'+ rowLinkType +'"><i class="text-center fa fa-pencil fa-2x"></i></p>');

									//New Edit Button -- replacing the old pencil
									rowInfo.push('<p class="text-center" data-id="'+ o.id +'" data-id2="'+ rowLinkType +'"><button class="ui yellow button">Edit</button></p>');
								
									break;
								
								case 'link':
		
									var linkTarget = '_self';
									
									if(setupData.newTab === true){
										
										linkTarget = '_blank';
										
									}
									
									rowInfo.push('<p class="text-center"><a href="'+ setupData.rowLink.action(o) +'" target="'+ linkTarget +'"><i class="text-center fa fa-eye fa-2x"></i></a></p>');
								
									break;
								
								case 'app_component':
								
									rowInfo.push('');
								
									break;
									
								case 'view_object':
								case 'modal':
								case 'tab':
								case 'notify':
								
									rowInfo.push('<p class="text-center" data-id="'+ o.id +'"><i class="text-center fa fa-eye fa-2x"></i></p>');
								
									break;	
								
								default:

									rowInfo.push('<p class="text-center" data-id="'+ o.id +'"><i class="text-center fa fa-eye fa-2x"></i></p>');
								
							}
							
						}

						// insert actual cell data
						_.each(columns, function(n,k){
							
							if(k != 'checkbox' && k != 'rowlink'){
	
								if(setupData.cells[k]){
	
									if(setupData.cells[k].length === 1){
										
										var cellData = setupData.cells[k](o);
										
										if(cellData){
											if(cellData.length > 150 && !cellData.includes('<br />')){
												rowInfo.push('<nobr><span style="">'+ cellData.substring(0,150) +'...</span></nobr>');
											}else{
												rowInfo.push('<nobr><span style="">'+ cellData +'</span></nobr>');
											}
										}else{
											rowInfo.push('<nobr><span style="">'+ cellData +'</span></nobr>');
										}
										
										
									}else{
										
										rowInfo.push('<span style=""> </span>');
										
									}						
									
								}else{
									
									// if property is a file, display the image
									if(blueprint[k].type === 'objectId' && blueprint[k].objectType === 'file_meta_data'){
										
										rowInfo.push('<img class="ui mini image" src="'+ sb.data.files.getURL(o[k]) +'">');
										
									// display default text
									}else{

                                        var displayString = getSelectDisplayStringForObjectField(o[k], blueprint[k]);

                                        if( displayString ){
                                            rowInfo.push('<span style="">'+ displayString +'<span>');
                                        }
									
									}
									
								}
									
							}
							
						});
		
						rowName = 'row-'+ o.id;
						
						// apply row style
						var rowStyle = '',
							cellStyles = [];
						if(setupData.hasOwnProperty('rowStyle')){
							rowStyle = setupData.rowStyle;
						}
						if(setupData.hasOwnProperty('cellStyles')){
							cellStyles = setupData.cellStyles;
						}
						// apply animation-delay-{n} class
						this.table.table.makeRow(
							rowName,
							rowInfo,
							{dataId:o.id, style:rowStyle, cellStyles:cellStyles, css:'item-'+ (+Math.round(Math.random()*2) +1)}
						);
						
						if(setupData.rowLink){
							
							if(setupData.rowLink.type == 'app_component'){
								
								this.table.table.body[rowName].rowlink.makeNode('button', 'button', {text:'<i class="fa fa-eye"></i>', size:'pda-btn-large pda-transparent pda-color-blue'});
								
							}
							
							if(setupData.newTab === true){
								
								this.table.table.body[rowName].rowlink.makeNode('button', 'div', {tag:'a', css:'ui small button', text:'<i class="fa fa-eye"></i>', href:'https://pagoda.voltz.software/app/'+ appConfig.instance +'/#'+ setupData.navigationItem.id +'&view='+ setupData.viewItem.id +'&single='+ o.id, target:'_blank'});
								//this.table.table.body[rowName].rowlink.makeNode('button', 'button', {text:'<a href="https://pagoda.voltz.software/app/'+ appConfig.instance +'/#'+ setupData.navigationItem.id +'&view='+ setupData.viewItem.id +'&single='+ o.id +'" target="_blank"><i class="fa fa-eye"></i></a>', size:'pda-btn-large pda-transparent pda-color-blue'});
								
							}
							
						}
	
						// provide dom object to cell data function if provided
						_.each(columns, function(n,k){
							
							if(k != 'checkbox' && k != 'rowlink'){
	
								if(setupData.cells[k]){
									
									if(setupData.cells[k].length > 1){
																																	
										setupData.cells[k](o, this.table.table.body[rowName][k]);
										
									}						
									
								}
									
							}
							
						}, this);
						
						// start row multi-selection boxes
						if(setupData.rowSelection == true){
							
							var icon = '<i class="text-muted square outline icon"></i>';
	
							if(selectedRows.indexOf(o.id) > -1){
								
								icon = '<i class="square check icon"></i>';
								
							}
							
							this.table.table.body['row-'+o.id].checkbox.makeNode('text', 'text', {text:icon});
							
							this.table.table.body['row-'+o.id].checkbox.notify('click', {
								type:'crud-table-row-clicked',
								data:{}
							}, sb.moduleId);
							
						}
						
						if(setupData.rowLink){
							
							if(setupData.rowLink.type == 'link' || setupData.rowLink.type == 'app_component'){
								
								var icon = '<p class="text-center"><i class="fa fa-square-o fa-1x"></i><br />New Tab?</p>';
							
								if(setupData.newTab){
									
									icon = '<p class="text-center"><i class="fa fa-check-square-o fa-1x"></i><br />New Tab?</p>';
									
								}
			
								this.table.table.header.row.rowlink.makeNode('text', 'text', {css:'text-center', text:icon});
								
								this.table.table.header.row.rowlink.notify('click', {
									type:'crud-table-row-link-style',
									data:{}
								}, sb.moduleId);
								
							}
							
						}
										
					}, this);
					
					if(setupData.rowSelection == true){
						
						this.table.table.header.row.checkbox.makeNode('text', 'text', {text:'<p><i class="text-muted square outline icon"></i></p>'});
						
						this.table.table.header.row.checkbox.notify('click', {
							type:'crud-table-row-clicked',
							data:{
								allRows:true
							}
						}, sb.moduleId);
						
					}
		
					_.each(setupData.visibleCols, function(colVal, colName){
						
						this.table.table.header.row[colName].notify('click', {
							type:'crud-table-sort-column',
							data:{
								sortCol:colName
							}
						}, sb.moduleId);			
						
					}, this);

					switch(setupData.rowLink.type){
						
						case 'app_component':
							
							if(setupData.newTab !== true){
								
								_.each(objs, function(objId){
							
									var o = compCache.getSingle(objId);
									
									var customDom = false;
									if(typeof setupData.rowLink.action == 'function'){
										customDom = true;
									}
	
									this.table.table.body['row-'+o.id].rowlink.notify('click', {
										type:'crud-table-run',
										data:{
											run:function(setupData){
												
												var dom = this;
												
												dom.text('<i class="fa fa-circle-o-notch fa-spin"></i>');
												
												function getRowObj(objectType, objId, callback){

													if(setupData.singleDataCall){
														
														setupData.singleDataCall(objId, function(rowObj){

															callback(rowObj);
															
														});
													
													}else{
														
														sb.data.db.obj.getById(setupData.objectType, objId, function(rowObj){
															
															callback(rowObj);
															
														}, setupData.childObjs);
														
													}
													
												}
												
												getRowObj(setupData.objectType, objId, function(rowObj){
													
													dom.text('<i class="fa fa-eye"></i>');

													var newAppNavView = {
															id:'single-'+rowObj.id,
															type:'table-single-item',
															title:setupData.viewItem.setup.rowLink.header(rowObj),
															icon:'<i class="fa fa-user"></i>',
															dom:setupData.viewItem.setup.rowLink.action,
															rowObj:rowObj,
															parent:setupData.viewItem.id,
															viewState:setupData.viewItem.viewState,
															removable:true			
														};
																												
													if(setupData.viewItem.pageChange){
														newAppNavView.pageChange = setupData.viewItem.pageChange;
													}
													
													setupData.navigationItem.views.push(newAppNavView);
														
													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:setupData.navigationItem.id,
															viewId:newAppNavView
														}
													});	
																									
												}, setupData.childObjs);
																							
											}.bind(this.table.table.body['row-'+o.id].rowlink.button, setupData)
										}
									}, sb.moduleId);
									
	/*
									this.table.table.body['row-'+o.id].rowlink.notify('click', {
										type:'crud-table-row-link-clicked',
										data:{
											type:'app_component',
											custom:customDom
										}
									}, sb.moduleId);
	*/
														
								}, this);
								
							}
							
							break;
							
						case 'view_object':
						
							_.each(objs, function(objId){
							
								var o = compCache.getSingle(objId);
								
								var customDom = false;
								if(typeof setupData.rowLink.action == 'function'){
									customDom = true;
								}
								
								this.table.table.body['row-'+o.id].rowlink.notify('click', {
									type:'crud-table-row-link-clicked',
									data:{
										type:'view_object',
										custom:customDom
									}
								}, sb.moduleId);
													
							}, this);
							
							break;	
												
						case 'notify':
						case 'tab':
						case 'edit':
						case 'modal':
						
							_.each(objs, function(objId){
							
								var o = compCache.getSingle(objId);
								
								if(setupData.rowLink.type == 'notify'){
									
									setupData.rowLink.action.data.object = o;
									
								}
		
								var customDom = false;
								if(typeof setupData.rowLink.action == 'function'){
									customDom = true;
								}
								
								if(setupData.rowLink.type == 'notify'){
									
									setupData.rowLink.action.data.run.bind({object:$.extend(true, {}, o), itemId:setupData.itemId});
									
									this.table.table.body['row-'+o.id].rowlink.notify('click', setupData.rowLink.action, sb.moduleId);
																	
								}else{
									
									this.table.table.body['row-'+o.id].rowlink.notify('click', {
										type:'crud-table-row-link-clicked',
										data:{
											type:setupData.rowLink.type,
											custom:customDom
										}
									}, sb.moduleId);
									
								}
													
							}, this);
						
							break;
						
						default:
							
							if(setupData.rowLink){
								
								_.each(objs, function(objId){
								
									var o = compCache.getSingle(objId);
									
									if(setupData.rowLink.type == 'notify'){
										
										setupData.rowLink.action.data.object = o;
										
									}
			
									var customDom = false;
									if(typeof setupData.rowLink.action == 'function'){
										customDom = true;
									}
									
									if(setupData.rowLink.type == 'notify'){
										
										setupData.rowLink.action.data.run.bind({object:$.extend(true, {}, o), itemId:setupData.itemId});
										
										this.table.table.body['row-'+o.id].rowlink.notify('click', setupData.rowLink.action, sb.moduleId);
																		
									}else{

										this.table.table.body['row-'+o.id].rowlink.notify('click', {
											type:'crud-table-row-link-clicked',
											data:{
												type:setupData.rowLink.type,
												custom:customDom
											}
										}, sb.moduleId);
										
									}
														
								}, this);
							
							}
						
					}
											
				}else{
					
					this.table.makeNode('text', 'div', {text: '<h3>No Data</h3>', css: 'ui header center aligned'});
					
				}
				
			}
						
			this.table.patch();
			
			if(setupData.feedUI){
				
				_.each(objs, function(objId){
					
					var o = compCache.getSingle(objId),
						rowName = 'row-'+ o.id;
					
					setupData.feedUI.action.call(this, this.table.feed, o);
					
				}, this);
				
			}
			
			if(setupData.onDraw){
				
				setupData.onDraw();
				
			}
			
		}
		
		function custom(parsedData, searching) {
			
			var objs = parsedData.data;
			var list = [];

			_.each(objs, function(objId) {
				
				var o = compCache.getSingle(objId);
				
				list.push(o);
				
			});
			
			setupData.custom.dom(this.table, list);
			
		}

		// build the .state object
		this.empty();

		var tableCss = 'pda-container';
		if(setupData.hasOwnProperty('css')){
			tableCss = setupData.css;
		}
		
		this.makeNode('tableContainer', 'div', {css:tableCss + setupData.borderColor});
		
		if(setupData.tableTitle){
			
			//this.tableContainer.makeNode('nameCol', 'column', {width:3}).makeNode('title', 'headerText', {text:setupData.tableTitle, size:'small'});
			
		}
		
		if(setupData.navigation !== false){
			this.tableContainer.makeNode('headerBreak', 'lineBreak', {});
		}
		
		if(setupData.searchObjects){
			
			//this.tableContainer.makeNode('rowBtnBreak', 'lineBreak', {});
		
			this.tableContainer.makeNode('searchCol', 'column', {width:12}).makeNode('searchBar', 'container', {css:''});

			//this.tableContainer.makeNode('rowBtnBreak2', 'lineBreak', {});
			
		}
		
		if(setupData.searchObjects !== false){
			
			this.tableContainer.makeNode('search', 'buttonGroup', {css:'pull-right'});
						
		}
		
		// header buttons
		this.tableContainer.makeNode('headerBtns', 'div', {css:'ui stackable grid'});
		this.tableContainer.headerBtns.makeNode('left', 'div', {css:'nine wide column'});

		this.tableContainer.headerBtns.makeNode('right', 'div', {css:'seven wide column'});
		
		this.tableContainer.headerBtns.left.makeNode('pageBtns', 'div', {css:'ui stackable mini buttons', style:'display:block!important;'});
		this.tableContainer.headerBtns.left.makeNode('counter', 'div', {});
		this.tableContainer.headerBtns.left.makeNode('rowBtns', 'div', {css:'ui stackable buttons pull-left'});
		
		this.tableContainer.headerBtns.right.makeNode('btns', 'div', {css:'ui mini stackable buttons right floated'});

		this.tableContainer.headerBtns.right.makeNode('rowBtns', 'div', {css:'ui stackable buttons'});
		
		if(setupData.feedUI){
			this.tableContainer.makeNode('clear', 'div', {css:'ui clearing divider'});
			this.tableContainer.makeNode('sp', 'lineBreak', {spaces: 1});
		}
		
		this.tableContainer.makeNode('table', 'column', {w:16});
		
		this.tableContainer.makeNode('tableBreak', 'div', {text:'<br />'});
		
		this.tableContainer.makeNode('pageBtnsLower', 'buttonGroup', {css:'pull-left'});
		
		//this.makeNode('break', 'lineBreak', {});
				
		this.patch();
		
		// attach all of the functionality to the .state property
		this.state.get = getState;
		this.state.loading = loading.bind(this.tableContainer.headerBtns);
		this.state.buttons = buttons.bind(this.tableContainer.headerBtns);
		this.state.search = search.bind(this.tableContainer);
		this.state.removeSearchTerm = removeSearchTerm;
		
		if(setupData.searchObjects){
			this.state.searchBar = searchBar.bind(this.tableContainer.searchCol.searchBar);
		}
		
		this.state.pages = pagingButtons.bind(this.tableContainer);

		if(setupData.hasOwnProperty('kanban')) {
			this.state.table = kanban.bind(this.tableContainer);
		} else if(setupData.hasOwnProperty('custom')) {
			this.state.table = custom.bind(this.tableContainer);
		} else {
			this.state.table = table.bind(this.tableContainer);
		}
		
		this.state.rowSelect = rowSelect.bind(this.tableContainer);
		
	}
	
	function systemCache(){
		
		var cache = [],
			pagesCache = [],
			recordsTotal = null;
		
		function addPage(page, cachedPagedData){

			if(cachedPagedData){
				paged = cachedPagedData;
			}
		
			parseData(page, paged);
			
			if(setupData.drawComplete){
				setupData.drawComplete({page:page, paged:paged});
			}
			
		}
		
		function addObject(object){
			
			cache.push(object);
			
			return true;
			
		}
		
		function checkCacheForPage(paged){

			if(_.where(pagesCache, {offset:paged.page, pageLength:paged.pageLength}).length > 0){
				
				return _.where(pagesCache, {offset:paged.page, pageLength:paged.pageLength})[0];
				
			}else{
				
				return false;
				
			}
			
		}
		
		function emptyCache(){
			
			cache = [];
			pagesCache = [];
			
			return true;
			
		}
				
		function getPagedData(paged, callback, resetCount){

			var cachedPage = checkCacheForPage(paged);

			if(cachedPage){

				if( cachedPage.hasOwnProperty('totalRecords') ){
					recordsTotal = cachedPage.totalRecords;
				}
				
				var copiedData = _.map(cachedPage.data, function(objId){
					
					return compCache.getSingle(objId);
					
				}, []);

				callback(cachedPage);
				
			}else{

				if( recordsTotal !== null && !resetCount ){
					paged.count = false;
				}else {
					paged.count = true;
				}

				dataCall(paged, function(objs){

					if( recordsTotal !== null && !resetCount ){
						objs.recordsTotal = recordsTotal;
						objs.recordsFiltered = recordsTotal;
					}else if( objs.hasOwnProperty('recordsTotal') ){
						recordsTotal = objs.recordsTotal;
					}

					if(setupData.drawComplete){
						setupData.drawComplete({page:objs, paged:paged});
					}
					
					var ret = parseData(objs, paged);

					callback(ret);
					
				});
				
			}
			
		}
		
		function getSearchPageData(objectType, where, callback, filterObj, parseDataCallback){

			function parseSearch(objs, parseDataCallback, callback) {

    			if(parseDataCallback) {

        			parseDataCallback(objs, callback);
        			
    			} else {
        			
        			callback(objs);
        			
    			}
    			
			}
			
			// always reset total records when searching/filtering
			recordsTotal = null;
			
			sb.data.db.obj.getWhere(objectType, where, function(objs){
				
				parseSearch(objs, parseDataCallback, function(objs) {
    				
    				if(!_.isEmpty(filterObj)){
					
    					var filtered = [];
    					
    					_.each(filterObj, function(filter, type){
    						
    						_.each(objs.data, function(o){
    
    							if(o[filter.field]){
    
    								if(o[filter.field][filter.childObjField]){
    									
    									if(filter.values.indexOf(o[filter.field][filter.childObjField].id) > -1){						
    								
    										filtered.push(o);
    										
    									}
    										
    								}							
    									
    							}
    							
    						});											
    						
    					});
    					
    					objs.recordsTotal = filtered.length;
    					objs.recordsFiltered = filtered.length;
    					objs.data = filtered;			
    					
    				}
    
    				var ret = parseData(objs, paged);
    
    				callback(ret);
    				
				});
				
			});
		
		}
		
		function getSingle(objId){
			
			return $.extend(
				true,
				{}, 
				_.where(cache, {id:objId})[0]
			);
			
		}
		
		function parseData(data, paged, search){

			var ret = {
					totalPages:0,
					totalRecords:0,
					offset:0,
					pageLength:0,
					currentOffset:0
				};
				
			if(search){
				ret.search = search;
			}
			
			ret.pageLength = paged.pageLength;
			ret.totalRecords = data.recordsTotal;
			ret.totalPages = ret.totalRecords / paged.pageLength;	
			ret.offset = paged.page;
			
			if(ret.offset == 0){
				
				ret.currentPage = 1;
				
			}else{
				
				ret.currentPage = Math.ceil( ret.offset / ret.pageLength );
				
			}
						
			ret.data = data.data;

			if(!ret.totalRecords){
				
				ret.totalRecords = 0;
				ret.totalPages = 0;
				ret.data = [];
				
			}

			_.each(ret.data, function(o){

				cache.push($.extend(true, {}, o));
				
			});

			cachedData.push($.extend(true, {}, ret));
			
			ret.data = _.pluck(ret.data, 'id');
			
			pagesCache.push($.extend(true, {}, ret));
			
			return ret;
			
		}	
		
		function totalObjects(){
			
			return pagesCache[0].totalRecords;
			
		}
		
		function updateObject(object){
			
			var newCache = _.reject(cache, function(o){
				
				return o.id == object.id;
				
			});

			newCache.push(object);
			
			cache = newCache;			
			
			return true;
			
		}
		
		this.add = addObject.bind(this);
		this.addPage = addPage.bind(this)
		this.empty = emptyCache.bind(this);
		this.getPage = getPagedData.bind(this);
		this.getSearchPage = getSearchPageData.bind(this);
		this.getSingle = getSingle.bind(this);
		this.parseData = parseData.bind(this);
		this.totalObjects = totalObjects.bind(this);
		this.update = updateObject.bind(this);
				
	}
						
	return {
		
		init: function(){
			
			sb.listen({
				'change-crud-table-page':this.changePage,
				'crud-page-length-change':this.changePageLength,
				'crud-table-back-to-table':this.backToTable,
				'crud-table-back-to-top':this.backToTop,
				'crud-table-cancel-search':this.stopSearch,
				'crud-table-header-button-clicked':this.headerButtonClicked,
				'crud-table-row-clicked':this.rowClicked,
				'crud-table-row-link-clicked':this.rowLinkClicked,
				'crud-table-row-link-external-click':this.rowLinkExternalClick,
				'crud-table-row-link-style':this.openInTabs,
				'crud-table-run':this.run,
				'crud-table-sort-column':this.sortColumn,
				'crud-table-start-filter':this.filter,
				'crud-table-start-search':this.search,
				'crud-table-view-custom-view':this.viewCustomView,
				'crud-table-view-home':this.viewHome,
				'crud-table-view-settings':this.viewSettings,
				'crud-table-view-tags': this.tags,
				'show-table':this.showTable,
				'update-table':this.update,
				'view-kanban': this.showTable 
			});
			
		},
		
		// reloads the table from cache data
		backToTable: function(data){

			if(setupData.navigation === true){
				
				if(historyUI.state){
					
					historyUI.state.show('loading', true);
					
				}			
				
			}
			
			//cachedData = [];						
			selectedRows = [];
			
			tableUI.state.removeSearchTerm();
			
			compCache.getPage(paged, function(objs){
				
				fullUI.hide();
			
				tableUI.show();
				
				tableUI.state();
				
				tableUI.state.buttons(setupData.headerButtons, 'btns');
				tableUI.state.table(objs);
				tableUI.state.pages(objs, 0);
									
				if(setupData.searchObjects){
					tableUI.state.search();
					tableUI.state.searchBar();
				}
				
				historyUI.state.show('table', true);
							
			});
			
		},
		
		backToTop: function(data){
			
			$(".main").animate({ scrollTop: 0 }, "slow");

		},
		
		// handles all page change requests
		changePage: function(data){
			
			tableUI.state.loading(true);
						
			paged.page = data.offset;
			
			selectedRows = [];
			
			tableUI.state.buttons(setupData.headerButtons, 'btns');
			
			compCache.getPage(paged, function(objs){			
					
				tableUI.state.table(objs);
				tableUI.state.pages(objs);	
				tableUI.state.loading(false);
				
			}, false);
			
		},
		
		// handles all page length change requests
		changePageLength: function(data){

			var newPageLength = +data.pageLength || 15;
			
			//historyUI.state.show('loading');
			tableUI.state.loading(true);
			
			paged.pageLength = newPageLength;
			paged.page = 0;
			
			cachedData = [];
			selectedRows = [];
			
			compCache.getPage(paged, function(objs){

				WebuiPopovers.hideAll();
				
				//historyUI.state.show('table', true);
				
				tableUI.state.table(objs);
				tableUI.state.pages(objs);
				tableUI.state.buttons(setupData.headerButtons, 'btns');	
				tableUI.state.loading(false);
				
			}, false);
			
		},
		
		dateRangeFilter: function(data){
						
			var form = data.form.process().fields;
			
			switch(form.dateType.value){
				
				case 'alltime':
					
					dataCall = setupData.data;
					
					historyUI.state.show('loading');
					
					compCache.empty();
					
					compCache.getPage(paged, function(objs){
													
						tableUI.state.table(objs);
						tableUI.state.pages(objs, 0);
						tableUI.state.search('dateRange');
						historyUI.state.show('table');
					
					});
					
					break;
					
				case 'custom':
					
					var where = {
							childObjs:setupData.childObjs
						};
					
					if(setupData.dateRange){
						
						if(typeof setupData.dateRange != 'boolean'){
							
							where[setupData.dateRange] = {
								type:'between',
								start:moment(form.start.value).unix(),
								end:moment(form.end.value).unix()
							};
							
						}else{
							
							where.date_created = {
								type:'between',
								start:moment(form.start.value).unix(),
								end:moment(form.end.value).unix()
							};
							
						}
						
					}
					
					dataCall = function(paged, callback){
		
						where.paged = paged;			

						sb.data.db.obj.getWhere(setupData.objectType, where, callback);
						
					};
					
					historyUI.state.show('loading');
					
					compCache.empty();
					
					compCache.getPage(paged, function(objs){
													
						tableUI.state.table(objs);
						tableUI.state.pages(objs, 0);
						tableUI.state.search('dateRange', form.start.value, form.end.value);						
						historyUI.state.show('table');
					
					});
				
					break;	
				
			}	
			
		},
				
		// handles all header button clicks
		headerButtonClicked: function(data){

			switch(data.buttonObj.name){
				
				case 'Reload':

					cachedData = [];
					selectedRows = [];
					
					tableUI.state.loading(true);
					
					compCache.empty();
					
					compCache.getPage(paged, function(objs){
											
						tableUI.state.buttons(setupData.headerButtons, 'btns');
						tableUI.state.table(objs);
						tableUI.state.pages(objs);	
						tableUI.state.loading(false);
						
					}, true);
				
					break;
					
				case 'All Rows':
				
					tableUI.state.get();
				
					break;	
				
				default:
				
					var buttonData = [];

					if(selectedRows.length == 0){

						if(data.buttonObj.download){
							
							// if button download was clicked
														
							var allData = [];
							
							paged.pageLength = 100;
							
							function getAllData(totalRecords, paged, callback){
							
								if(paged.page < totalRecords){
									
									compCache.getPage(paged, function(data){
										
										_.each(data.data, function(obj){
											
											allData.push(obj);
											
										});
										
										paged.page = paged.page + paged.pageLength;
										
										getAllData(totalRecords, paged, callback);
										
									});
									
								}else{
									
									callback(allData);
									
								}
								
							}
							
							if(compCache.totalObjects() > 200){
								
								sb.dom.alerts.ask({
									title: 'Are you sure?',
									text: 'There are '+ compCache.totalObjects() +' rows. This can take a few minutes.'
								}, function(resp){
									
									if(resp){
										
										tableUI.state.loading(true, 'download');
									
										sb.dom.alerts.alert('Download Started', 'The file is currently downloading. Please do not close this window. You can click OK button here though.', '');
										
										getAllData(compCache.totalObjects(), paged, function(done){
		
											data.buttonObj.action(
												$.extend(true, {}, done),
												false
											);
											
											tableUI.state.loading(false, 'download');
											
										});
										
									}
									
								});	
								
							}else{
								
								tableUI.state.loading(true, 'download');

								getAllData(compCache.totalObjects(), paged, function(done){
	
									data.buttonObj.action(
										$.extend(true, {}, done),
										false
									);
									
									tableUI.state.loading(false, 'download');
									
								});
								
							}
						
						}else{

							switch(typeof data.buttonObj.action){
								
								case 'string':
									
									break;
								
								default:
								
									buttonData = $.extend(true, {}, tableUI.state.get().data, blueprint);
								
							}					
							
						}
						
					}else{
						
						_.each(selectedRows, function(objId){

							var currentState = tableUI.state.get();
							var sendData = [];
							
							if(currentState.search){
								
								sendData = 
									
									_.chain(cachedData)
										.where({
											offset:currentState.offset
										}).filter(function(page){
											return (page.search && page.search.term == currentState.search.term && page.search.field == currentState.search.field);
										}).value()[0];

							}else{

								sendData = _.where(cachedData, {offset:currentState.offset})[0];
							
							}

							buttonData.push( 
								$.extend(true, {},
									_.where(sendData.data, {id: +objId})[0]
								)
							);
							
						});

						
					}			

					switch(data.buttonObj.domType){
						
						case 'full':
							
							historyUI.state.show('header', true);
							
							tableUI.hide();
							
							fullUI.show();
							fullUI.state.display(data.buttonObj.name);

							switch(typeof data.buttonObj.action){
								
								case 'string':
									
									switch(data.buttonObj.action){
										
										case 'create':

											defaultCreateFunction(data.buttonObj, fullUI.container, blueprint, setupData.objectType);
																					
											break;
											
										case 'edit':
											
											defaultEditFunction(fullUI.container, buttonData, blueprint, setupData.objectType);
																					
											break;	
										
									}
									
									break;
								
								default:
							
									data.buttonObj.action(buttonData, fullUI.container, blueprint, setupData.objectType, setupData.buttonState);
								
							}
							
							
						
							break;
							
						case 'modal':
							
							ui.modals.modal.body.empty();
							ui.modals.modal.footer.empty();
							
							ui.modals.modal.body.patch();
							ui.modals.modal.footer.patch();
							
							ui.modals.modal.show();
						
							data.buttonObj.action(buttonData, ui.modals.modal);
						
							break;
							
						case 'none':
													
							data.buttonObj.action(buttonData, false, blueprint);
						
							break;	
							
						default:

							switch(typeof data.buttonObj.action){
								
								case 'string':
									
									switch(data.buttonObj.action){
											
										case 'erase':
										
											defaultEraseFunction(buttonData, function(){
												
/*
												cachedData = [];
												selectedRows = [];
												
												tableUI.state.loading(true);
												
												compCache.empty();
												
												compCache.getPage(paged, function(objs){
																		
													tableUI.state.buttons(setupData.headerButtons, 'btns');
													tableUI.state.table(objs);
													tableUI.state.pages(objs);	
													tableUI.state.loading(false);
													
													sb.dom.alerts.alert('Success', 'Item(s) deleted successfully.', 'success');
													
												});
*/
												
											});
																					
											break;
										
										default:
										
									}
									
									break;
								
								default:
								
									
								
							}	
						
					}	
				
			}	
			
		},
		
		filter: function(data){

			tableUI.state.search('loading');
			
			var formData = data.form.process().fields,
				filterOn = {};
			
			//var orgLength = paged.pageLength;			
			//paged.pageLength = 250;
			
			var where = {};
			
			if(setupData.queryObj !== null){
				where = setupData.queryObj;
			}
			
			where.paged = paged;
			where.childObjs = setupData.childObjs;
			
			_.each(formData, function(o,n){
				
				if(setupData.filters[n]){
				
					if(setupData.filters[n].field.indexOf('.') == -1){
						
						if(blueprint[n].type == 'multi-select'){
							
							where[n] = {
								type:'contains',
									value:{
										type:'or',
										values:_.map(o.value, function(o){ return o; }, [])
									}
								};
							
						}else{
							
							where[n] = {
									type:'or',
									values:_.map(o.value, function(o){ return o; }, [])
								};
								
						}
						
					}else{
						
						filterOn[n] = {
								field:setupData.filters[n].field.slice(0, setupData.filters[n].field.indexOf(".")),
								childObjField:setupData.filters[n].field.substr(setupData.filters[n].field.indexOf(".") + 1),
								values:_.map(o.value, function(n){ return +n; })
							};
						
					}
					
				}
				
			});

			compCache.empty();

			compCache.getSearchPage(setupData.objectType, where, function(objs){

				//paged.pageLength = orgLength;
				
				tableUI.state.buttons(setupData.headerButtons, 'btns');
				tableUI.state.search(undefined, undefined, undefined, where);
				tableUI.state.table(objs);
				tableUI.state.pages(objs);

			}, filterOn, setupData.parseSearchData);			
			
		},
		
		openInTabs: function(data){
			
			if(setupData.newTab === true){
				setupData.newTab = false;
			}else{
				setupData.newTab = true;
			}

			if(tableUI.state){
				tableUI.state.table(tableUI.state.get());
			}
			
		},
		
		// handles all row checkbox clicks
		rowClicked: function(data){
			
			if(!_.isEmpty(tableUI)){
				
				if(data.allRows){
												
					tableUI.state.rowSelect(tableUI.state.get().data, 'all');
					
				}else{
					
					tableUI.state.rowSelect([data.sender]);
					
				}
				
				if(selectedRows.length > 0){
						
					tableUI.state.buttons(setupData.multiSelectButtons, 'rowBtns');
					
				}else{
					
					tableUI.state.buttons(setupData.headerButtons, 'btns');
					
				}
				
			}
			
		},
		
		rowLinkClicked: function(data){

			switch(data.type){
				
				case 'view_object':
					
					var itemId = 'self';
					
					if(setupData.rowLink.itemId){
						itemId = setupData.rowLink.itemId;
					}

					sb.notify({
						type:'app-navigate-to',
						data:{
							itemId:itemId,
							viewId:setupData.rowLink.viewObj(compCache.getSingle(+data.dataId))
						}
					});
				
					break;
					
				case 'custom':
				
					var rowObj = compCache.getSingle(+data.dataId);
				
					setupData.rowLink.action(rowObj, function(draw){
						
						if(draw.dom){
							
							draw.dom.patch();
							
							draw.after(draw.dom);
							
						}else{
							
							draw.patch();
							
						}
																
					});
				
					break;
				
				case 'app_component':
								
					setupData.navigation = true;
					
					// allows the user to pass in an object not currently in the table state
					if(data.object){
					
						var rowObj = data.object;
						
						compCache.add(data.object);
																						
					}else{
					
						var rowObj = compCache.getSingle(+data.dataId);
						
					}

					if(!_.isEmpty(tableUI)){
						tableUI.hide();
					}
					
					historyUI.state.update(rowObj, setupData.rowLink.header(rowObj), true, 'app_component');
								
					fullUI.show();
					fullUI.state.display(setupData.rowLink.header(rowObj));
					
					setupData.rowLink.action(rowObj, fullUI.container, blueprint, function(draw){
						
						if(draw.dom){
							
							draw.dom.patch();
							
							draw.after(draw.dom);
							
						}else{
							
							draw.patch();
							
						}
																
					});
				
					break;
				
				case 'edit':
					
					if(!_.isEmpty(tableUI)){
						tableUI.hide();
					}
					
					historyUI.state.show('', true);
							
					fullUI.show();
					fullUI.state.display(setupData.rowLink.header(compCache.getSingle(+data.dataId)));

					if(data.custom === true){
						
						setupData.rowLink.action(blueprint, fullUI.container, compCache.getSingle(+data.dataId));
						
					}else{
						
						defaultEditFunction(fullUI.container, compCache.getSingle(+data.dataId), data, blueprint, setupData.objectType);						
						
					}
														
					break;
					
				case 'modal':
				
					ui.modals.modal.body.empty();
					ui.modals.modal.footer.empty();
					
					ui.modals.modal.body.patch();
					ui.modals.modal.footer.patch();
					
					ui.modals.modal.show();
					
					// allows the user to pass in an object not currently in the table state
					if(data.object){
					
						var rowObj = data.object;
						
						compCache.add(data.object);
																						
					}else{
					
						var rowObj = compCache.getSingle(+data.dataId);
						
					}
					
					setupData.rowLink.action(rowObj, ui.modals.modal, blueprint);
								
					break;	
				
				case 'tab':
					
					setupData.navigation = true;
					
					// allows the user to pass in an object not currently in the table state
					if(data.object){
					
						var rowObj = data.object;
						
						compCache.add(data.object);
																						
					}else{
					
						var rowObj = compCache.getSingle(+data.dataId);
						
					}

					if(!_.isEmpty(tableUI)){
						tableUI.hide();
					}
					
					historyUI.state.update(rowObj, setupData.rowLink.header(rowObj), true);
								
					fullUI.show();
					fullUI.state.display(setupData.rowLink.header(rowObj, data.setup));
					
					setupData.rowLink.action(rowObj, fullUI.container, {}, function(dom){
						
						if(dom.dom){
							dom.dom.patch();
							dom.after(dom.dom);
						}else{
							dom.patch();
						}
						
					});
					
					sb.notify({
						type:setupData.objectType+'-single-view-loaded',
						data:{
							objectId:rowObj.id,
							object:rowObj
						}
					});
				
					break;
					
				case 'notification':
					
					//setupData.rowLink.action.data.object = compCache.getSingle(+data.dataId);
				
					sb.notify({
						type:setupData.rowLink.notificationType,
						data:setupData.rowLink.data(compCache.getSingle(+data.dataId))
					});	
					
					break;	
					
				default:
				
					setupData.rowLink.action.data.object = compCache.getSingle(+data.dataId);
				
					sb.notify({
						type:setupData.rowLink.action.type,
						data:setupData.rowLink.action.data
					});	
				
			}
						
		},
		
		run: function(data){ data.run(); },
		
		search: function(data){
			
			tableUI.state.search('loading');
			tableUI.state.loading(true);

			var formData = data.form.process().fields,
				searchObj = _.where(setupData.searchObjects, {value:formData.searchObject.value})[0],
				paged = {
					page:0,
					pageLength:50,
					paged:true,
					sortCol:searchObj.value,
					sortOrder:'desc'
				},
				where = {};

			if(+formData.searchTerm.value > 0 && searchObj.value != 'zip'){
				
				searchObj.term = +formData.searchTerm.value;
				
				where[searchObj.value] = searchObj.term;
				
			}else{
				
				searchObj.term = formData.searchTerm.value;
				
				where[searchObj.value] = {
					type:'contains',
					value:searchObj.term
				};
				
			}
			
			if(searchObj.join){
				searchObj.objectType = blueprint[searchObj.join].objectType;
			}else{
				searchObj.objectType = setupData.objectType;
			}
				
			where.paged = paged;
			
			if(searchObj.objecType == setupData.objectType){
				where.childObjs = setupData.childObjs;
			}
			
			sb.data.db.obj.getWhere(searchObj.objectType, where, function(ret){

				setupData.parseSearchData(ret, function(ret){
					
					if(ret.data){
	
						if(searchObj.join){
							
							where = {};
						
							where.paged = paged;
							where.childObjs = setupData.childObjs;
							
							var joinOn = 'id';
								join = searchObj.join;
							
							if(searchObj.joinOn){
								joinOn = searchObj.joinOn;
								join = 'id';
							}
	
							where.paged.sortCol = 'date_created';
							where[join] = {
								type:'or',
								values:_.pluck(ret.data, joinOn)
							};
	
							sb.data.db.obj.getWhere(setupData.objectType, where, function(searchRet){
								
								if(searchRet.data){
									
									var parsed = compCache.parseData(searchRet, paged);
								
									WebuiPopovers.hideAll();
									tableUI.state.buttons(setupData.headerButtons, 'btns');
									tableUI.state.table(parsed, searchObj);
									tableUI.state.pages(parsed);
									
									if(setupData.searchObjects){
										tableUI.state.search('hide');
									}
									
								}else{
									
									tableUI.state.search('noItems');
									
								}
								
							});
							
						}else{
													
							var parsed = compCache.parseData(ret, paged);
							
							WebuiPopovers.hideAll();
							tableUI.state.buttons(setupData.headerButtons, 'btns');
							tableUI.state.table(parsed, searchObj);
							tableUI.state.pages(parsed);
							
							if(setupData.searchObjects){
								tableUI.state.search('hide');
							}
							
						}
						
					}else{
	
						tableUI.state.search('noItems');
						
					}
					
				});
								
			});
						
		},
		
		// initial entry point		
		showTable: function(data){

				domObj = {},
				components = {},
				selectedRows = [],
				compCache = {},
				cachedData = [],
				counterString = '',
				paged = {
					page:0,
					pageLength:50,
					paged:true,
					sortCol:'date_created',
					sortDir:'desc',
					sortCast:'string',
					count:true
				},
				blueprint = {},
				setupData = {
					searchObjects:[],
					borderColor:'',
					newTab:false,
					childObjs:1,
					download:false,
					navigation:true,
					buttonState:{},
					container:false,
					settingsTable:false,
					tags: false,
					singleObject:false,
					noObjects:false,
					rowStyle:'',
					cellStyles:[],
					drawComplete:null,
					queryObj:null,
					parseSearchData:function(data, callback){ callback(data); }
				},
				dataCall = {},
				ui = {},
				historyUI ={},
				tableUI = {},
				fullUI = {};

			// overide component defaults with user settings
			_.each(data, function(v,k){
				
				setupData[k] = v;
				
			});

			var listen = {
					'change-crud-table-page':this.changePage,
					'crud-page-length-change':this.changePageLength,
					'crud-table-back-to-table':this.backToTable,
					'crud-table-back-to-top':this.backToTop,
					'crud-table-cancel-search':this.stopSearch,
					'crud-table-date-range-filter':this.dateRangeFilter,
					'crud-table-header-button-clicked':this.headerButtonClicked,
					'crud-table-row-clicked':this.rowClicked,
					'crud-table-row-link-clicked':this.rowLinkClicked,
					'crud-table-row-link-external-click':this.rowLinkExternalClick,
					'crud-table-row-link-style':this.openInTabs,
					'crud-table-run':this.run,
					'crud-table-sort-column':this.sortColumn,
					'crud-table-start-filter':this.filter,
					'crud-table-start-search':this.search,
					'crud-table-view-calendar':this.viewCalendar,
					'crud-table-view-home':this.viewHome,
					'crud-table-view-rules':this.viewRules,
					'crud-table-view-settings':this.viewSettings,
					'crud-table-view-tags':this.viewTags,
					'show-table':this.showTable,
					'update-table':this.update,
					'update-table-data': this.updateData,
					'update-table-settings':this.updateSettings,
					'update-title':this.updateTitle
				};

			sb.listen(listen);
			
			if(setupData.calendar){
				components.calendar = sb.createComponent('calendar');
			}
			
			components.settings = sb.createComponent('crud-table');
			
			if(setupData.rules){
				
				components.rules = sb.createComponent('rules');
				
			}
			
			if(setupData.tags){

				components.tags = sb.createComponent('tags');
			}
			
			compCache.data = systemCache;
			compCache.data();

			// saving the data call
			dataCall = data.data;

			if(setupData.compCache){

				compCache.addPage(setupData.compCache.page, setupData.compCache.paged);
			}
			
			if(setupData.paged){
				
				if(setupData.paged.sortCol){
					paged.sortCol = setupData.paged.sortCol;
				}
				
				if(setupData.paged.sortDir){
					paged.sortDir = setupData.paged.sortDir;
				}
				
			}

			// ui setup
			ui = sb.dom.make(data.domObj.selector);
			//ui = data.domObj;
			ui.modals = ui.makeNode('modals', 'container', {});
			ui.modals.makeNode('modal', 'modal', {});
			
			if(setupData.feedUI){
				
				ui.makeNode('container', 'div', {});
				tableUI = ui.container.makeNode('table', 'div', {});
				
			}else{
				
				if(setupData.container === true){
					ui.makeNode('container', 'container', {css:'pda-container pda-Panel '+ setupData.borderColor});
				}else{
					ui.makeNode('container', 'container', {css:''});
				}
							
				ui.makeNode('container', 'container', {css:''});
				
			}
			
			historyUI = ui.container.makeNode('history', 'container', {width:12});
			historyUI.state = buildHistoryUI;
			
			tableUI = ui.container.makeNode('table', 'column', {width:12});
			tableUI.state = buildTableUI;
						
			fullUI = ui.container.makeNode('full', 'column', {width:12});
			fullUI.state = buildFullScreenUI;
			
			if(setupData.navigation === true){
				
				ui.build();
			
				historyUI.state();
				fullUI.state();
			
				//historyUI.state.show('loading');
			
			}
			
			sb.data.db.obj.getBlueprint(data.objectType, function(bp){

				blueprint = bp;
			
				var otherView = false;

				if(setupData.home && !setupData.objectId){
					
					if(setupData.home.default != false){
						
						if(setupData.navigation === true){				
							historyUI.state.show('home');
						}else{
							
							ui.build();
			
							historyUI.state();
							fullUI.state();
							
						}
						
						fullUI.show();
						fullUI.state.display(setupData.home.header, 'pda-panel-blue');
						
						setupData.home.action(fullUI.container, blueprint);
						
						otherView = true;
						
					}
					
				}

				if(setupData.objectId){

					function getSingleData(objectId, callback){

						switch(typeof objectId){
							
							case 'function':
							
								objectId(function(obj){
								
									callback(obj);
									
								}, data.objectType, setupData.objectId);
							
								break;
							
							case 'number':
							
								sb.data.db.obj.getById(data.objectType, setupData.objectId, function(obj){
							
									callback(obj);
								
								}, setupData.childObjs);
								
								break;
								
							default:
							
// 								console.log('.objectId is neither a function or number');
							
								callback('.objectId is neither a function or number');	
								
						}
						
					}
					
					getSingleData(setupData.objectId, function(obj){
						
						compCache.add(obj);
							
						historyUI.state.update(obj, setupData.rowLink.header(obj), true);
													
						fullUI.show();
						fullUI.state.display(setupData.rowLink.header(obj));
						
						setupData.rowLink.action(obj, fullUI.container, blueprint);
												
					});
					
					otherView = true;
					
				}

				if(otherView == false){

					if(setupData.navigation === false){
						
						ui.build();
			
						historyUI.state();
						fullUI.state();
						
					}
					
					tableUI.state();
					
					tableUI.state.loading(true);

					compCache.getPage(paged, function(objs){

						//if(otherView == false){

							if(objs.data.length == 1 && setupData.rowLink.type == 'tab' && setupData.singleObject === true){

								var rowObj = compCache.getSingle(objs.data[0]);

								if(setupData.navigation === true){
									
									historyUI.state.update(rowObj, setupData.rowLink.header(rowObj), true);
								
								}
								
								tableUI.hide();
													
								fullUI.show();
								fullUI.state.display(setupData.rowLink.header(rowObj));
								
								setupData.rowLink.action(rowObj, fullUI.container, blueprint);
																
							}else{

								if(objs.data.length == 0 && setupData.noObjects){
									
									if(setupData.navigation === true){
										historyUI.state.show('home');
									}
									
									tableUI.hide();

									fullUI.state();
									fullUI.show();
									fullUI.state.display('');
									
									//tableUI.state.buttons(setupData.headerButtons, 'btns');
									
									setupData.noObjects.action(fullUI.container, setupData.objectType, blueprint, setupData);
									
									tableUI.state.loading(false);
									
								}else{

									tableUI.state.buttons(setupData.headerButtons, 'btns');
									tableUI.state.table(objs);
									tableUI.state.pages(objs, 0);
									
									if(setupData.searchObjects){
										tableUI.state.search();
										tableUI.state.searchBar();
									}
									
									tableUI.state.loading(false);
									
									if(setupData.navigation === true){
										historyUI.state.show('table');
									}
									
								}
								
							}
							
						//}
						
					});
				
				}
						
			}, true);
			
		},
		
		sortColumn: function(data){
			
			if(blueprint.hasOwnProperty(data.sortCol)){
			
				if(!_.isEmpty(tableUI)){
	
					var bp = blueprint[data.sortCol];
	
					tableUI.state.loading(true);
					
					if(paged.sortCol == data.sortCol){
						
						if(paged.sortDir == 'desc'){
							
							paged.sortDir = 'asc';
							
						}else{
							
							paged.sortDir = 'desc';
							
						}
						
					}else{
						
						paged.sortCol = data.sortCol;
						paged.sortDir = 'desc';
						paged.sortCast = bp.type;
						
					}
					
					compCache.empty();
								
					compCache.getPage(paged, function(objs){
		
						tableUI.state.table(objs);
						tableUI.state.pages(objs);	
						tableUI.state.loading(false);
						
					});	
					
				}
			
			}else{
				
				return;
				
			}
			
		},
		
		stopSearch: function(data){
			
			tableUI.state.loading(true);
			
			paged.page = 0;
			
			compCache.empty();
						
			compCache.getPage(paged, function(objs){
				
				WebuiPopovers.hideAll();
				tableUI.state.table(objs);
				tableUI.state.pages(objs);	
				
				if(setupData.searchObjects){
					tableUI.state.search();
				}
				
				tableUI.state.loading(false);
				
			}, true);
			
		},
		
		update: function(data){

			if(data.type){
				
				switch(data.type){
					
					case 'add':

						_.each(data.objects, function(o){
								
							compCache.add(o);
							
						});
						
						if(data.redraw){
							
							compCache.getPage(paged, function(objs){
								
								tableUI.state.buttons(setupData.headerButtons, 'btns');
								tableUI.state.table(objs);
								tableUI.state.pages(objs);
								
							}, true);	
							
						}
					
						break;
						
					case 'update':

						selectedRows = [];
						
						_.each(data.objects, function(o){
							
							compCache.update(o);
							
						});
						
						if(data.redraw){
							
							compCache.getPage(paged, function(objs){

								tableUI.state.buttons(setupData.headerButtons, 'btns');
								tableUI.state.table(objs);
								tableUI.state.pages(objs);
								
							}, true);	
							
						}
					
						break;	
					
				}
				
			}else{

				tableUI.state.loading(true);
		
				compCache.empty();
				
				selectedRows = [];
						
				compCache.getPage(paged, function(objs){
	
					tableUI.state.buttons(setupData.headerButtons, 'btns');
					tableUI.state.table(objs);
					tableUI.state.pages(objs);	
					tableUI.state.loading(false);
					
				}, true);	
				
			}	
			
		},
		
		// change the data call
		updateData: function(data){
			
			dataCall = data.data;
			
			tableUI.state.loading(true);
							
			compCache.empty();
			
			selectedRows = [];
					
			compCache.getPage(paged, function(objs){
					
				tableUI.state.buttons(setupData.headerButtons, 'btns');
				tableUI.state.table(objs);
				tableUI.state.pages(objs);	
				tableUI.state.loading(false);
				
				if(data.hasOwnProperty('after')) {
					data.after();
				}
				
			}, true);	
			
		},
		
		updateSettings: function(data){
			
			// override component defaults with user settings
			_.each(data, function(v,k){
				
				setupData[k] = v;
				
				if(k == 'data'){
					dataCall = v;
				}
				
			});
			
			if(data.print){
				
				fullUI.hide();
			
				tableUI.show();
				
				tableUI.state();
				
				tableUI.state.loading(true);
				
				compCache.empty();
					
				compCache.getPage(paged, function(objs){
						
					tableUI.state.buttons(setupData.headerButtons, 'btns');
					tableUI.state.table(objs);
					tableUI.state.pages(objs, 0);
					
					if(setupData.searchObjects){
						tableUI.state.search();
					}
					
					tableUI.state.loading(false);
					historyUI.state.show('table');
				
				}, true);
				
			}
			
		},
		
		// updates the table title property and reprints
		updateTitle: function(data){
			
			setupData.tableTitle = data.tableTitle;
			
			if(ui.container){
				if(ui.container.history){
					if(ui.container.history.container){
						if(ui.container.history.container.btns){
							if(ui.container.history.container.btns.home){
								ui.container.history.container.btns.home.text(setupData.tableTitle);
							}
						}
					}
				}
			}
			
		},
		
		viewCalendar: function(data){
			
			tableUI.hide();
		
			historyUI.state.show('calendar');
						
			fullUI.show();
			fullUI.state.display('Calendar', 'pda-panel-blue');
			
			var calSetup = {
					domObj: fullUI.container,
					viewDate: moment(),
					viewType: 'month',  // could be changed to any of the other view types
					cellBtns: {},
					calMenu: {
						list: {
							text: 'List',
							type: 'list',
							show: true
						},
						day: {
							text: 'Day',
							type: 'day',
							show: true
						},
						threeDay: {
							text: '3 Days',
							type: 'threeDays',
							show: true	
						},
						week: {
							text: 'Week',
							type: 'week',
							show: true
						},
						month: {
							text: 'Month',
							type: 'month',
							show: true
						}
					}
				};
				
			if(setupData.calendar.fullSetup){
				
				_.each(setupData.calendar.fullSetup, function(v,k){
					
					calSetup[k] = v;
					
				});
				
			}else{
				
				calSetup.events = function(callback, range){
						
					var where = {
							childObjs:setupData.childObjs,
							date_created:range
						}
					
					_.each(setupData.calendar.where, function(o,k){
						where[k] = o;
					});
											
					if(setupData.calendar.dateField){
						
						delete where.date_created;
						
						where[setupData.calendar.dateField] = range;
						
					}
					
					sb.data.db.obj.getWhere(setupData.objectType, where, function(data){
						
						var ret = [];
						
						_.each(data, function(obj) {

							ret.push(setupData.calendar.prepare(obj));
						
						});
						
						callback(ret);
						
					});
					
				};
				
			}
			
			if(setupData.calendar.eventBox){
				calSetup.eventBox = setupData.calendar.eventBox;
			}
			
			components.calendar.notify({
				type: 'display-calendar',
				data: calSetup
				
			});
						
			sb.notify({
				type:setupData.objectType+'-calendar-view-loaded',
				data:{}
			});
			
		},
			
		viewCustomView: function(data){
			
			tableUI.hide();
			
			historyUI.state.show(data.view);
			
			fullUI.show();
			fullUI.state.display(setupData.settings.header, 'pda-panel-blue');

			setupData.views[data.view].action(fullUI.container);
			
			sb.notify({
				type:setupData.objectType+'-'+ data.view +'-custom-view-loaded',
				data:{}
			});
			
		},
				
		viewHome: function(data){

			tableUI.hide();
		
			historyUI.state.show('home');
						
			fullUI.show();
			fullUI.state.display(setupData.home.header, 'pda-panel-blue');
			
			setupData.home.action(fullUI.container, blueprint);
			
			sb.notify({
				type:setupData.objectType+'-home-view-loaded',
				data:{}
			});
			
		},
		
		viewRules: function(data){
			
			tableUI.hide();
			
			historyUI.state.show('rules');
			
			fullUI.show();
			fullUI.state.display(setupData.settings.header, 'pda-panel-blue');
						
			switch(typeof setupData.rules){
				
				case 'function':
				
					setupData.rules(fullUI.container);
				
					break;
					
				case  'object':
				
					defaultRulesView(fullUI.container, setupData.rules);
									
					break;	
				
			}
						
			sb.notify({
				type:setupData.objectType+'-rules-view-loaded',
				data:{}
			});

		},
		
		viewSettings: function(data){

			tableUI.hide();
		
			historyUI.state.show('settings');
						
			fullUI.show();
			fullUI.state.display(setupData.settings.header, 'pda-panel-blue');
						
			switch(typeof setupData.settings.action){
				
				case 'function':
				
					setupData.settings.action(fullUI.container);
				
					break;
					
				case  'object':
				
					defaultSettingsView(fullUI.container, setupData.settings.action);
									
					break;	
				
			}
						
			sb.notify({
				type:setupData.objectType+'-settings-view-loaded',
				data:{}
			});
			
		},
		
		viewTags: function(data){

			tableUI.hide();
			historyUI.state.show('tags');
			fullUI.show();
			fullUI.state.display(setupData.tags.header, 'pda-panel-blue');
			
			components.tags.notify({
				type: 'crud-table-tag-view',
				data: {
					domContainer: fullUI.container,
					type: setupData.objectType,
					tags: [],
					resultList: setupData.tags
				}
			});
			
			sb.notify({
				type:setupData.objectType+'-tag-view-loaded',
				data:{}
			});
			
		},
		
		destroy: function(){
			
			sb.listen({
				'start-crud-table':this.start
			});
			
			// in case instance is created, but show table has not yet been run
			if(compCache.hasOwnProperty('empty')){
				compCache.empty();
			}
									
			_.each(components, function(comp){
				comp.destroy();
			});
			
				domObj = {},
				components = {},
				selectedRows = [],
				compCache = {},
				cachedData = [],
				counterString = '',
				paged = {
					page:0,
					pageLength:50,
					paged:true,
					sortCol:'date_created',
					sortDir:'desc',
					sortCast:'string'
				},
				blueprint = {},
				setupData = {
					searchObjects:[],
					newTab:true,
					childObjs:1,
					download:false
				},
				dataCall = {},
				ui = {},
				historyUI ={},
				tableUI = {},
				fullUI = {};
			
		},
									
	}
	
});