Factory.register("login-component", function (sb) {
  var attempts = 0,
    domObj = null,
    pageModules = [
      {
        pageName: "Send Message",
        pageModuleId: "messenger",
      },
      {
        pageName: "View Messages",
        pageModuleId: "staff-messenger",
      },
      {
        pageName: "Files",
        pageModuleId: "files",
      },
      {
        pageName: "Staff List",
        pageModuleId: "staff",
        subList: [
          {
            displayName:
              '<i class="glyphicon glyphicon-cog"></i> Status Options',
            page: "edit-staff-status",
            pageParams: {},
          },
          {
            displayName: '<i class="glyphicon glyphicon-cog"></i> Base Options',
            page: "edit-staff-base",
            pageParams: {},
          },
          {
            displayName:
              '<i class="glyphicon glyphicon-cog"></i> Job Type Options',
            page: "edit-staff-job-type",
            pageParams: {},
          },
        ],
      },
    ];

  function checkLoginCreds(email, password, callback) {
    sb.data.db.loginUser(
      { email: email, password: password },
      function (passed) {
        setTimeout(function () {
          if (passed) {
            callback(passed);
          } else {
            attempts++;

            callback(false);
          }
        }, 1000);
      }
    );

    /*
		sb.data.db.controller('getIPAddress&api_web_form=true&pagodaAPIKey='+ instance, {}, function(ip){
console.log('ip',ip);
callback(false);		
			sb.data.db.loginUser({email:email, password:password, ip_address:ip}, function(passed){
					
				setTimeout(function(){
					
					if(passed){
						
						callback(passed);
										
					}else{
						
						attempts++;
						
						callback(false);
						
					}
					
				}, 1000);
				
			});
		
		});
*/
  }

  function destroyCollectionsCookie() {
    var cookie = document.cookie;
    var cookieArr = cookie.split(";");

    _.each(cookieArr, function (val) {
      var valSplit = val.split("=");

      if (valSplit[0].search("collections") !== -1) {
        document.cookie =
          valSplit[0] + "=; expires=Thu, 01 Jan 1970 00:00:00 GMT;";
      }
    });
  }

  return {
    init: function () {
      sb.listen({
        "login-form-submitted": this.login,
        "stop-login-component": this.stop,
        "logout-user": this.logout,
        "successful-login": this.success,
        "login-account-selected": this.accountLogin,
        "get-accounts": this.getAccounts,
      });

      sb.notify({
        type: "register-application",
        data: {
          navigationItem: {
            id: "login",
            title: "Logout",
            icon: '<i class="fa fa-sign-out"></i>',
            display: false,
            views: [
              {
                id: "logout",
                type: "custom",
                title: "Accounts",
                icon: '<i class="fa fa-sign-out"></i>',
                default: true,
                dom: function (domObj, state, draw) {
                  sb.notify({
                    type: "get-accounts",
                    data: {
                      callback: function (accounts) {
                        accounts = _.reject(accounts, function (account) {
                          return (
                            account.instance == sb.data.cookie.get("instance")
                          );
                        });

                        if (!_.isEmpty(accounts)) {
                          domObj
                            .makeNode("panel", "container", { uiGrid: false })
                            .makeNode("body", "container", { uiGrid: false });

                          domObj.panel.body.makeNode("title", "headerText", {
                            text: "Switch Accounts",
                          });

                          domObj.panel.body
                            .makeNode("logout", "button", {
                              text: 'Logout <i class="fa fa-sign-out"></i>',
                              css: "pda-btn-red pull-right",
                            })
                            .notify(
                              "click",
                              {
                                type: "logout-user",
                                data: {},
                              },
                              sb.moduleId
                            );

                          domObj.panel.body
                            .makeNode("cont", "container", {
                              uiGrid: false,
                              css: "",
                            })
                            .makeNode("table", "table", {
                              css: "",
                              columns: {
                                instance: "Account",
                                btns: "",
                              },
                            });

                          _.each(
                            _.sortBy(accounts, "instance"),
                            function (account) {
                              domObj.panel.body.cont.table.makeRow(
                                "instance-" + account.id,
                                [account.instance.toUpperCase(), ""]
                              );

                              domObj.panel.body.cont.table.body[
                                "instance-" + account.id
                              ].btns
                                .makeNode("btn", "button", {
                                  text: 'Login <i class="fa fa-arrow-right"></i>',
                                  css: "pda-btn-green",
                                })
                                .notify(
                                  "click",
                                  {
                                    type: "logout-user",
                                    data: {
                                      toAccount: account,
                                      dom: domObj,
                                    },
                                  },
                                  sb.moduleId
                                );
                            }
                          );

                          draw(domObj);
                        } else {
                          domObj
                            .makeNode("panel", "container", { uiGrid: false })
                            .makeNode("body", "container", { uiGrid: false });

                          domObj.panel.body.makeNode("title", "headerText", {
                            text: "Switch Accounts",
                          });

                          domObj.panel.body
                            .makeNode("logout", "button", {
                              text: 'Logout <i class="fa fa-sign-out"></i>',
                              css: "pda-btn-red pull-right",
                            })
                            .notify(
                              "click",
                              {
                                type: "logout-user",
                                data: {},
                              },
                              sb.moduleId
                            );

                          domObj.panel.body
                            .makeNode("cont", "container", {
                              css: "pda-container",
                            })
                            .makeNode("header", "text", {
                              text: "No other Accounts Registered",
                            });

                          draw(domObj);
                        }
                      },
                    },
                  });
                },
              },
              {
                id: "qa-logout",
                type: "quickAction",
                title: "Logout",
                size: "small",
                icon: '<i class="fa fa-sign-out"></i>',
                dom: function (domObj, state, draw) {
                  sb.notify({
                    type: "get-accounts",
                    data: {
                      callback: function (accounts) {
                        accounts = _.reject(accounts, function (account) {
                          return (
                            account.instance == sb.data.cookie.get("instance")
                          );
                        });

                        if (!_.isEmpty(accounts)) {
                          domObj
                            .makeNode("panel", "container", { uiGrid: false })
                            .makeNode("body", "container", { uiGrid: false });

                          domObj.panel.body.makeNode("btns", "buttonGroup", {
                            css: "right floated",
                          });
                          domObj.panel.body.btns
                            .makeNode("logout", "button", {
                              text: 'Logout <i class="fa fa-sign-out"></i>',
                              css: "pda-btn-red",
                            })
                            .notify(
                              "click",
                              {
                                type: "logout-user",
                                data: {},
                              },
                              sb.moduleId
                            );

                          domObj.panel.body.makeNode("title", "headerText", {
                            text: "Switch Accounts",
                          });

                          domObj.panel.body.makeNode("break", "lineBreak", {
                            spaces: 1,
                          });

                          domObj.panel.body
                            .makeNode("cont", "container", {})
                            .makeNode("cont", "column", { width: 12 })
                            .makeNode("table", "table", {
                              css: "table-hover table-condensed",
                              columns: {
                                instance: "Account",
                                btns: "",
                              },
                            });

                          _.each(
                            _.sortBy(accounts, "instance"),
                            function (account) {
                              domObj.panel.body.cont.cont.table.makeRow(
                                "instance-" + account.id,
                                [account.instance.toUpperCase(), ""]
                              );

                              domObj.panel.body.cont.cont.table.body[
                                "instance-" + account.id
                              ].btns
                                .makeNode("btn", "button", {
                                  text: 'Login <i class="fa fa-arrow-right"></i>',
                                  css: "pda-btnOutline-green fluid",
                                })
                                .notify(
                                  "click",
                                  {
                                    type: "logout-user",
                                    data: {
                                      toAccount: account,
                                      dom: domObj,
                                    },
                                  },
                                  sb.moduleId
                                );
                            }
                          );

                          domObj.panel.body.makeNode(
                            "contBreak",
                            "lineBreak",
                            {}
                          );

                          //domObj.build();

                          draw(domObj);
                        } else {
                          domObj
                            .makeNode("panel", "container", {})
                            .makeNode("body", "container", {});

                          domObj.panel.body
                            .makeNode("logout", "button", {
                              text: 'Logout <i class="fa fa-sign-out"></i>',
                              css: "pda-btn-large pda-btn-red pull-right",
                            })
                            .notify(
                              "click",
                              {
                                type: "logout-user",
                                data: {},
                              },
                              sb.moduleId
                            );

                          domObj.panel.body.makeNode("title", "headerText", {
                            text: "Switch Accounts",
                          });

                          domObj.panel.body
                            .makeNode("cont", "container", {
                              css: "pda-container",
                            })
                            .makeNode("header", "text", {
                              text: "No other Accounts Registered",
                            });

                          draw(domObj);
                        }
                      },
                    },
                  });
                },
              },
            ],
          },
        },
      });
    },

    run: function (data) {
      data.run(data);
    },

    stop: function () {
      attempts = 0;
      domObj = null;

      sb.listen({
        "start-login-component": this.run,
      });
    },

    getAccounts: function (data) {
      var adminPath =
        "../../api/_getAdmin.php?api_webform=1&pagodaAPIKey=" +
        appConfig.instance +
        "&do=";

      sb.data.db.controller(
        "getUserAccountsNew",
        { userId: sb.data.cookie.get("uid"), multi: true },
        function (accounts) {
          accounts = _.uniq(accounts, function (a) {
            return a.instance;
          });

          if (typeof data.callback === "function") {
            data.callback(accounts);
          }
        },
        adminPath
      );
    },

    accountLogin: function (data) {
      if (data.dom) {
        data.dom.login.body.empty();
        data.dom.login.body.makeNode("text", "headerText", {
          text: "Signing In...",
          size: "small",
          css: "text-center",
          style: "margin-top:15px;",
        });
        data.dom.login.body.makeNode("loading", "loader", {});
        data.dom.build();
      }

      var adminPath =
        "../../api/_getAdmin.php?api_webform=1&pagodaAPIKey=" +
        data.instance +
        "&do=";
      var pageParams = sb.data.url.getParams();

      if (
        pageParams.client_id &&
        pageParams.redirect_uri &&
        pageParams.response_type === "code"
      ) {
        sb.data.db.controller(
          "authorizeClient",
          {
            account: data.account.id,
            instance: data.instance,
          },
          function (response) {
            if (response.redirect_uri) {
              window.location.replace(response.redirect_uri);
            }
          },
          adminPath
        );
      } else {
        sb.data.db.controller(
          "getIPAddress",
          {},
          function (ip) {
            var createCookie = {
              staffId: data.account.id,
              ip_address: ip,
              fingerprint: window.navigator.userAgent.replace(/\D+/g, ""),
              platform: window.navigator.platform,
            };

            sb.data.db.controller(
              "createCookie",
              createCookie,
              function (cookie) {
                setTimeout(function () {
                  // if (ga) {
                  // 	ga('send', {
                  // 		hitType: 'event',
                  // 		eventCategory: 'Accounts',
                  // 		eventAction: 'Login - ' + data
                  // 			.instance
                  // 	});
                  // }

                  if (data.refreshPage) {
                    location.reload();
                  } else {
                    if (data.account.hasOwnProperty("go_to_hq")) {
                      if (data.account.go_to_hq === true) {
                        window.location.href = "/app/" + data.instance + "#hq";
                      } else {
                        window.location.href = "/app/" + data.instance;
                      }
                    } else {
                      window.location.href = "/app/" + data.instance;
                    }
                  }
                }, 0);
              },
              adminPath
            );
          },
          adminPath
        );
      }
    },

    login: function (loginObj) {
      delete domObj.loginPanel.body.loginButton;

      domObj.loginPanel.body.makeNode("loading", "text", {
        text: sb.dom.loadingGIF,
      });

      var formObj = loginObj.form.process();

      var email = formObj.fields.email.value,
        password = formObj.fields.password.value;

      checkLoginCreds(email, password, function (passed) {
        sb.data.cookie.set("type", "");
        sb.data.cookie.set("appNav", "");

        if (passed) {
          sb.notify({
            type: "successful-login",
            data: {
              passed: passed,
            },
          });
        } else {
          sb.dom.alerts.alert("Try again", "", "error");

          sb.notify({
            type: "logout-user",
            data: {},
          });
        }
      });

      domObj.loginPanel.patch();
    },

    logout: function (setup) {
      var instance = sb.data.cookie.get("instance");
      var text = setup.message
        ? setup.message
        : "You are about to log out of the application.";

      function continueLogout() {
        var adminPath =
          "../../api/_getAdmin.php?api_webform=1&pagodaAPIKey=" +
          appConfig.instance +
          "&do=";

        sb.data.db.controller(
          "deleteCookie",
          {},
          function (deleted) {
            if (deleted) {
              setTimeout(function () {
                // Destroy all collections' cookies
                destroyCollectionsCookie();

                sb.data.cookie.set("uid", "", "now");
                sb.data.cookie.set("token", "", "now");
                sb.data.cookie.set("series", "", "now");
                sb.data.cookie.set("instance", "", "now");
                sb.data.cookie.set("type", "", "now");
                sb.data.cookie.set("appNav", "", "now");

                // Handle setting redirect cookie
                if (setup.cookieError) {
                  var currentURL = window.location.href;
                  var redirectInstanceName =
                    currentURL.includes("/app/") && currentURL.includes("#")
                      ? currentURL.split("/app/").pop().split("#")[0]
                      : "";

                  if (redirectInstanceName && Factory.publicLink !== true) {
                    sb.data.cookie.set(
                      "redirectURL",
                      window.location.href,
                      3600
                    );
                  }
                }

                if (setup.toAccount) {
                  if (setup.dom) {
                    setup.dom.panel.body.cont.cont.table.body[
                      "instance-" + setup.toAccount.id
                    ].btns.makeNode("btn", "button", {
                      text: 'Signing In <i class="fa fa-circle-o-notch fa-spin"></i>',
                      css: "pda-btnOutline-green",
                    });

                    setup.dom.patch();
                  }

                  sb.notify({
                    type: "login-account-selected",
                    data: {
                      account: setup.toAccount,
                      dom: setup.dom,
                      instance: setup.toAccount.instance,
                      refreshPage: setup.refreshPage,
                    },
                  });
                } else {
                  window.location.href = "/";
                }
              }, 1500);
            }
          },
          adminPath
        );
      }

      if (setup.cookieError) {
        continueLogout();
      } else {
        sb.dom.alerts.ask(
          {
            text: text,
            title: "Are you sure?",
          },
          function (resp) {
            if (resp) {
              swal.disableButtons();

              continueLogout();
            } else {
              if (setup.refreshPage) {
                window.location.href = "/app/" + instance;
              }
            }
          }
        );
      }
    },

    success: function (successObj) {
      delete domObj.loginPanel.body.loading;

      domObj.loginPanel.body.makeNode("success", "text", {
        text: 'Success <i class="glyphicon glyphicon-ok"></i>',
        css: "text-center text-success",
      });

      domObj.loginPanel.body.makeNode("loading", "text", {
        css: "text-center text-success",
        text: "Signing In...<br /><br />" + sb.dom.loadingGIF,
      });

      domObj.loginPanel.patch();

      //window.location = 'https://bento.infinityhospitality.net/app/'+ appConfig.instance;
      window.open("/app/" + appConfig.instance, "_self");
    },
  };
});
