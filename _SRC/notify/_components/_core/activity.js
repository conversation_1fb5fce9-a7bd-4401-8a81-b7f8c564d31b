Factory.register('notes', function(sb) {

	var Setup = {};
	var Methods = {
			refresh: {
				postUI: function() {}
			}
		};

	function getComments(queryObj, callback) {

		var where = {};

		_.each(queryObj, function(value, key) {
			where[key] = value;
		});

		if (
			!_.isEmpty(queryObj)
			&& !_.isEmpty(queryObj.type_id)
			&& queryObj.type_id.value
		) {

			where.tagged_with = {
				type: 		'any'
				, values: 	[queryObj.type_id.value]
				, or: 		{
					type_id: queryObj.type_id.value
				}
			};
			delete where.type_id;
			
		}		
        var controllerCall = 'getComments';

        if ((appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz') && this.tool == 'noteTool') {
            controllerCall = 'getWhiteListComments';
        }
        
		sb.data.db.controller(controllerCall, where, function(data) {


			callback(data);
		});
							
	}
		
	function getObjectPageParams(item){

		var objType = item.object_bp_type;
		if(objType === 'groups'){
			objType = item.group_type.toLowerCase();
		}
		
		var objName = item.name;
		switch(objType){
			
			case 'contacts':
			case 'users':
			objName = item.fname +' '+ item.lname;
			break;
			
			case 'tasks':
			objName = item.title;
			break;
				
			}
			
			return sb.data.url.createPageURL(
					'object', 
					{
						type:objType, 
						id:item.id,
						name:objName
					}
				);
		
	}	

	function CheckSetup(data) {

		var setup = {};

		if(data.hasOwnProperty('actions')) {
			setup.actions = data.actions;
		}

		if(data.hasOwnProperty('categories')) {
			setup.categories = data.categories;
		}

		if(data.hasOwnProperty('domObj')) {
			setup.domObj = data.domObj;
		} 

		if(data.hasOwnProperty('fieldKey')) {
			setup.fieldKey = data.fieldKey;
		}

		if(data.hasOwnProperty('filters')) {
			setup.filters = data.filters;
		}

		if(data.hasOwnProperty('mentions')) {
			setup.mentions = data.mentions;
		} else {
			setup.mentions = true;
		}

		if(data.hasOwnProperty('obj')) {
			setup.obj = data.obj;
		} 

		if(data.hasOwnProperty('objectId')) {
			setup.objectId = data.objectId;
		}

		if(data.hasOwnProperty('query')) {
			setup.query = data.query;
		}

		if(data.hasOwnProperty('public')) {
			setup.public = data.public;
		}

		if(data.hasOwnProperty('state')) {
			setup.state = data.state;
		}
		if(data.hasOwnProperty('defaultNoteType')) {
			setup.defaultNoteType = data.defaultNoteType;
		}

		if(data.hasOwnProperty('portalOptions')) {
			setup.portalOptions = data.portalOptions;
		}
		
		setup.mentionedUsers = [];

		return setup;

	}

	function InitComments(ui, options, Setup) {

		if (!options) {
			options = {
				activityFeed: false
			};
		}
		var inputUI = {};
		
		// Data
		
		function getUsers (Setup, onComplete) {
			
			// #2014 - potential
			sb.data.db.obj.getWhere('users', {
				//enabled:1, 
				childObjs: {
					fname:true, 
					lname:true, 
					profile_image:true, 
					enabled:true
				}
			}, function (usersList) {
				
				// If the object is shared with client portal users, 
				// make them available as tag-able mentions.
				if (
					Setup.related_object
					&& !_.isEmpty(Setup.related_object.shared_with)
				) {
					
					// #2014 - potential
					sb.data.db.obj.getById(
						''
						, Setup.related_object.shared_with
						, function (tags) {
							
							_.each(tags, function (tag) {
								
								if (tag.object_bp_type === 'contacts') {
									
									usersList.push(tag);
									
								}
								
							});
							
							onComplete(usersList);
							
						}
						, {
							fname: 		true
							, lname: 	true
							, company: {
								name: true
							}
						}
					);
					
				} else {
					
					onComplete(usersList);
					
				}
			
			});
			
		}
		
		// View
		
		function build_input(ui, setup) {
			
			// setup.onEdit = bool;
			// setup.comment = {};
			
			var newComment = {
					note: ''
				};
			var activeComment = {};
				
			function build_postUI(ui) {
				
				ui.makeNode('postCont', 'div', {
					css: 		'field-value'
// 					, style: 	'padding:0px;'
				});
				
			}
			
			function display_detailsField(ui, onEdit, commentObj) {
				
				var fieldSetup = {
					type: 		'detail',
					property: 	'note',
					obj: 		commentObj,
					options: {
						context: 	Setup.related_object || {},
						edit: 			true,
						editing: 		true,
						placeholder: 	(appConfig.is_portal) ? 'Type your question here and click the \'Contact Foundation Group\' Button' : 'Add a comment...',
						commitUpdates: 	false,
						shouldPatchInFieldContainer: true
					},
					ui: ui
				};

				if (Setup.mentions) {

					fieldSetup.options.hint = [{
						// for testing
/*
						mentions: _.map(Setup.usersList, function(s){ 
							return s.fname +' '+ s.lname 
						}),
*/
						mentions: _.map(_.reject(Setup.usersList, {id:+sb.data.cookie.userId}), function(s){
							 
							if(s.id != +sb.data.cookie.userId){

								// return s.fname +' '+ s.lname;
								return {
									name: s.fname +' '+ s.lname
									, title: s.fname +' '+ s.lname
									, type: 'user'
								};
							}	
							
						}),
						//match: /\B@(\w*)$/,
						match: /\B@$/,
						search: function (keyword) {

							var ret = _.filter(this.mentions, function(item) {

								if(item.title.toLowerCase().search(keyword.toLowerCase()) > -1){
									return true;
								}
								
							});

							return ret;
							
						},
						content: function (item) {

							if(typeof item === 'string') {
								
								var mentUser = _.find(Setup.usersList, function(us){

													return us.name === item;	

												});

								if(mentUser !== null || mentUser !== undefined){

									Setup.mentionedUsers.push(mentUser.id);	

								}
								
							}
							
							return item;
							
						}
					}]

				}
					
				if(onEdit) {
						
					fieldSetup.commitUpdates = true;
					
				}

				sb.notify({
					type: 'view-field',
					data: fieldSetup
				});
				
			}
			
			if(setup.hasOwnProperty('comment')) {
				activeComment = setup.comment;
			} else {
				activeComment = newComment;
			}

			ui.empty();
			ui.makeNode('wrapper', 'div', {
				css: 'ui comments',
				style: 'max-width: 100% !important;'
			});
			
			ui.wrapper.makeNode('comment', 'div', {
				css: 'comment'
			});

			ui.wrapper.comment.makeNode('avatar', 'div', {
				css: 'ui avatar circular image'
			});
			ui.wrapper.comment.makeNode('content', 'div', {
				css: 'content'
			});

			if(Setup.hasOwnProperty('currentUser')) {

				if(
					Setup.currentUser
					&& Setup.currentUser.profile_image !== undefined 
					&& Setup.currentUser.profile_image.loc != "//" 
					&& Setup.currentUser.profile_image !== null
				) {

					ui.wrapper.comment.avatar.makeNode('userImage', 'div', {
						tag:'img', 
						css:'ui mini avatar ', 
						src: sb.data.files.getURL(Setup.currentUser.profile_image)
					});

				} else {

					ui.wrapper.comment.avatar.makeNode('icon', 'div', {
						tag: 'i', 
						css: 'big user icon'
					});

				}

			}

			ui.wrapper.comment.content.makeNode('inputCont', 'div', {
				css:'text'
			});

			build_postUI(ui.wrapper.comment.content.inputCont);
			
			ui.wrapper.comment.content.inputCont.makeNode('btnGrp', 'div', {
				css: 'ui bottom attached basic segment'
			});
			
			build_inputBtns(ui.wrapper.comment.content.inputCont.btnGrp, activeComment, setup.onEdit);
			
			ui.patch();
			
			display_detailsField(ui.wrapper.comment.content.inputCont.postCont, setup.onEdit, activeComment);
			
			
			Methods.refresh.postUI = function() {
				
				activeComment.note = '';
				ui.wrapper.comment.content.inputCont.btnGrp.save.loading(false);
				$(ui.wrapper.comment.content.inputCont.postCont.selector +' .ql-editor').text('');

				if (
					Setup 
					&& typeof Setup.onPost === 'function'
				) {

					Setup.onPost();

				}

			}
			
			Methods.refresh.postBtn = function() {

				ui.wrapper.comment.content.inputCont.btnGrp.save.loading(false);
				
			}

		}
		
		function build_inputBtns(ui, comment, onEdit) {
			

            var setDefaultNoteType = Setup.defaultNoteType;
            var categories = _.map(Setup.noteTypesList, function(o){

					var ret = {
							name: o.name,
							value: o.id
						};
						
					if(!comment.hasOwnProperty('id')) {
						
                        if( o.id == setDefaultNoteType.id){
                            
                            ret.selected = true;
                            
                        }
						
					} else {
						
						if(o.id === comment.note_type.id) {
							
							ret.selected = true;
							
						}
						
					}

					return ret;
					
				});
			var saveBtnText = (appConfig.is_portal) ? 'Contact Foundation Group' : 'Post';
            var selectedCategory = _.find(categories, {selected: true});
			var formSetup = {
					category: {
						name: 'category',
						type: 'radio',
						label: 'Note Category',
						selected: selectedCategory.value,
						options: categories
					}
				};
			var noteTypeText = '';
			
			if (
				!(
					appConfig.state
					&& appConfig.state.portal > 0
				) && !Setup.public
			) {

				formSetup.public = {
					name: 		'public'
					, type: 	'check'
					, label: 	''
					, options: 	[{
						name: ''
						, value: 1
						, label: 'Post publicly'
					}]
					, onChange: function (val) {
						
						if (val) {
							comment.public = 1;
						} else {
							comment.public = 0;
						}
						
					}
				};
			}

			if (Setup.public) {
				comment.public = 1;
			}
				
			if(comment.hasOwnProperty('id')) {

				noteTypeText = _.where(categories, {value:comment.note_type.id})[0].name;
				
			} else {
				
				noteTypeText = _.where(Setup.noteTypesList, {default: 'yes'})[0].name;
				
			}

			if(onEdit) {
				
				saveBtnText = 'Save';
				
			}

			ui.makeNode('save', 'div', {
				text: saveBtnText,
				css: 'ui mini right floated button'
			}).notify('click', {
				type: [sb.moduleId+'-run'],
				data: {
					run: function(data) {

						var savedComment = comment;
						
						if(savedComment.note === '' || savedComment.note === '<p><br></p>'){
				
							sb.dom.alerts.alert('Please type something', '', 'error');
							
							return false;
							
						}
						
						ui.save.loading();

						build_input(inputUI, {});
						
						if(!onEdit) {
							
							var categoryId = ui.pop.form.process().fields.category.value;
							
							savedComment.note_type = _.where(categories, {value: +categoryId})[0].value;
							
							create_comment(savedComment, function() {}, function(newPost) {
								
								Setup.mentionedUsers = [];
								
								sb.notify({
									type: 'display-alert',
									data: {
										header: 'Comment Posted',
										body: 'Your comment has been posted successfully',
										color: 'green'
									}
								});
								
								Setup.addToCollections(newPost);

								$('#loader').fadeOut();

								if(appConfig.is_portal && appConfig.instance == 'foundation_group'){
									sb.dom.alerts.alert(
                                        'Thank you for leaving a comment.'
                                        ,'We will review your comment and respond within 3 - 5 business days.'
                                        ,'warning'
                                        , false
                                        , false
                                        , true
                                        , true
                                        , "Okay"
                                    );
								}

							});
							
						} else {
							
							savedComment.edited = 1;
							
							sb.data.db.obj.update('notes', savedComment, function(updated) {

								sb.notify({
									type: 'display-alert',
									data: {
										header: 'Comment Updated',
										body: 'Your comment has been posted successfully',
										color: 'green'
									}
								});
								
								Setup.updateCollections(updated, {
									note: true,
									note_type: true,
									edited: true
								});

								$('#loader').fadeOut();
								
							}, 1);
							
						}
						
					}
				}
			}, sb.moduleId);

			var display = (Setup.categories === false) ? 'display:none;' : ''; 

			ui.makeNode('type', 'div', {
				text: 'Posting to: ' + _.where(categories, {selected:true})[0].name,
				css: 'ui mini right floated basic button',
				style: display
			});
			
			ui.makeNode('pop', 'popover', {
				parent: ui.type
			});

			ui.pop.makeNode('form', 'form', formSetup);

			ui.pop.form.category.notify('change', {
				type: [sb.moduleId+'-run'],
				data: {
					run: function() {

						var categoryId = ui.pop.form.process().fields.category.value;

						noteTypeText = _.where(categories, {value: +categoryId})[0].name;
						
						$(ui.type.selector).text('Posting to: '+ noteTypeText);
						
						comment.note_type = +categoryId;
						
					}
				}
			}, sb.moduleId);
			
		}
		
		function init_commentsCollections(dom, setup) {

			var CachedOptions = {};
			
			function build_log (ui, obj) {
				
				var icon = 'exclamation';
				
				if (!_.isEmpty(obj.icon)) {
					icon = obj.icon.color +' '+ obj.icon.icon;
				}
				
				var profileImg = '<i class="large '+ icon +' icon"></i>';
				var author = '';
				var note_type = '';
				// var timeDiff = moment().local().diff(obj.last_updated, 'minutes');
				var timeText = moment(obj.date_created).local().format('M/D/YYYY, h:mm a');
				
				// if(timeDiff > 60) {
					
				// 	timeText = moment(obj.last_updated, 'YYYY-MM-DD hh:mm:ss.SSSSS').local().format('M/D/YYYY, h:mm a');
					
				// }
				
				if (obj.icon && !_.isEmpty(obj.icon)) {
					icon = obj.icon.color +' '+ obj.icon.icon;
				} else {
					icon = 'exclamation';
				}
	
				ui.makeNode('comments', 'div', {
					css: 'ui comments',
					style: 'max-width: none !important;'
				});
				
				ui.comments.makeNode('comment', 'div', {
					css: 'comment'
				});
				
				ui.comments.comment.makeNode('avatar', 'div', {
					tag: 'a',
					css: 'ui circular mini avatar image',
					text: profileImg
				});
				
				ui.comments.comment.makeNode('content', 'div', {
					css: 'content'
				});
				
				if (!_.isEmpty(author)) {
					
					ui.comments.comment.content.makeNode('author', 'div', {
						tag: 'span',
						css: 'author',
						text: author.fname + ' ' + author.lname
					});
					
				}
				
				ui.comments.comment.content.makeNode('metadata', 'div', {
					css: 'metadata'
					, style: 'margin-left:0px;'
				});
				
				if(_.isObject(obj.note_type) && obj.note_type.hasOwnProperty('name')) {
					
					note_type = obj.note_type.name;
				}
	
				ui.comments.comment.content.metadata.makeNode('date', 'div', {
					tag: 'span',
					css: 'date',
					text: timeText + ' (System message)'
				});
				
				// Link to the object.
				if (
					obj.type_id 
					&& obj.type_id.hasOwnProperty('id')
					&& obj.type_id.name !== undefined
					&& obj.type_id.id !== Setup.objectId
				) {
					
					ui.comments.comment.content.metadata.makeNode('link', 'div', {
						tag: 'a',
						href: sb.data.url.createPageURL('object', {
							id: obj.type_id.id,
							name: obj.type_id.name,
							type: obj.type_id.object_bp_type,
							group_type: obj.type_id.group_type
						}),
						text: 'in ' + obj.type_id.name,
						css: 'link',
						style: 'padding: 0 !important;'
					});																
					
				}
				
				ui.comments.comment.content.makeNode('note', 'div', {
					css: 'text ql-editor',
					text: obj.note
				});

				if (
					!(
						appConfig.state
						&& appConfig.state.portal > 0
					)
					&& obj.public
				) {
					
					ui.listeners.push(
						function (selector) {
							
							$(selector).parent().css('border-left', '4px solid rgb(251,189,8, 0.6)');
							
						}
					);
					
				}
				
			}

			var query = Setup.query ? Setup.query : getComments;

			var collectionSetup = {
				query: query,
				domObj: dom,
				objectType: 'notes',
				sortDir: 'desc',
				fields: {
					note: {
						title: 'Comments',
						type: 'title',
						view: function(ui, obj) {

							switch (obj.record_type) {
								
								case 'log':
									build_log(ui, obj);
									break;
									
								default:
									build_comment(ui, obj);
									break;
								
							}
							
						},
						isSearchable: true
					},
					date_created: {
						title: '',
						type: 'date',
						rangeOver: true
					}
				},
				menu: false,
				data: {
					add: function(callback) {
						
						Setup.addToCollections = function(newNote) {

							if (!_.isEmpty(CachedOptions)) {

								// Comment
								if (CachedOptions.commentsOnly && newNote.record_type == null) {
									callback(newNote);

								// State change
								} else if (CachedOptions.statusHistoryOnly && CachedOptions.where.log_type == newNote.log_type && CachedOptions.where.record_type == newNote.record_type) {
									callback(newNote);

								// System log
								} else if (CachedOptions.systemLogsOnly && CachedOptions.where.record_type == newNote.record_type) {
									callback(newNote);
								}
							
							} else {
								callback(newNote);
							}

						};
							
					},
					remove: function(callback) {
						
						Setup.removeFromCollections = callback;
						
					},
					update: function(callback) {
						
						Setup.updateCollections = callback;
						
					}
				},					
				selectedView: 'list',
				groupings: {
					by_range: true
				},
				subviews: {
					list: {
						range: {
							defaultTo: 'next_3_months',
							not: ['all_time']
						},
						groupBy: {
							defaultTo: 'by_range'
						},
						backlog: false
					}
				},
				search: true,
				tags: false,
				filterBy: {
					typeFilter: {
						title: 'Type',
						defaultText:  'All Types',
						defaultValue: 'all',
						getOptions:   function (callback) {

                            var options = [
                                {
                                    name: '<i class="ui square envelope icon"></i> Automated Email',
                                    id: 'automated-email'
                                }, 
								{
									name: '<i class="ui comments icon"></i> Comments',
									id: 'comment'
								}, 
								{
									name: '<i class="ui random icon"></i> Status History',
									id: 'state-change'
								}, 
								{
									name: '<i class="ui pencil alternate icon"></i> System Log',
									id: 'system-log'
								}
							];


							callback(options);

						},
						parseSelection: function (selection, options) {

							delete options.where.log_type;	
							delete options.where.record_type;
							delete options.commentsOnly;
							delete options.statusHistoryOnly;
							delete options.systemLogsOnly;

							switch (selection) {

								case 'comment':
									options.where.record_type = {
										type: 'not_equal',
										value: 'log'
									};
									options.commentsOnly = true;
									CachedOptions = options;
									break;

								case 'state-change':
									options.where.log_type = 'state-change';
									options.where.record_type = 'log';
									options.statusHistoryOnly = true;
									CachedOptions = options;
									break;

                                case 'automated-email':
									options.where.log_type = 'automated-email';
									options.where.record_type = 'log';
									options.statusHistoryOnly = true;
									CachedOptions = options;
									break;

								case 'system-log':
									options.where.record_type = 'log';
									options.systemLogsOnly = true;
									CachedOptions = options;
									break;

								default:
									CachedOptions = {};
									break;

							}

						}
					
					}
				}
			}
			
			collectionSetup.where = {
				type_id: {
					type: 'or',
					value: setup.related_object.id,
					fields: ['tagged_with', 'type_id']
				},
				childObjs: {
					last_updated: true,
					note_type: {
						name:true
					},
					note: true,
					author: 'id',
					icon: true,
					type: true,
					type_id: {
						id: true
						, name: true
						, object_bp_type: true
						, group_type: true
					},
					public: true,
					record_type: true,
                    log_type: true
				}
			};

			if (
				appConfig.state
				&& appConfig.state.portal > 0
			) {
				collectionSetup.where.public = 1;
			}

			// Filter feeds tied to a specific field
			if (
				options.fieldKey 
				&& typeof options.fieldKey === 'string'
				&& !_.isEmpty(options.fieldKey)
			) {
				collectionSetup.where.field = options.fieldKey;
			}

			// For comment-only feeds
			if (options.commentsOnly) {
				
				collectionSetup.where.record_type = {
					type: 'not_equal',
					value: 'log'
				};

				delete collectionSetup.filterBy.typeFilter;

			}

            if ( options.recent_client_activity ) {
                collectionSetup.where.log_type = 'recent-client-action';
            }
			if (Setup.filters === false) {
				collectionSetup.filters = false;
			}

			// For the HQ space, no filtering required
			if (
				setup 
				&& setup.related_object 
				&& setup.related_object.group_type === 'Headquarters'
			) {
				delete collectionSetup.where.type_id;
			}

			if (Setup.portalOptions) {
				
				if (Setup.portalOptions.hasOwnProperty('groupings'))
					collectionSetup.groupings = Setup.portalOptions.groupings; 
				if (Setup.portalOptions.hasOwnProperty('filters'))
					collectionSetup.filters = Setup.portalOptions.filters; 
				if (Setup.portalOptions.hasOwnProperty('search'))
					collectionSetup.search = Setup.portalOptions.search; 
				if (Setup.portalOptions.hasOwnProperty('hideDateRange'))
					collectionSetup.hideDateRange = Setup.portalOptions.hideDateRange; 
				
			}

			try {

				sb.notify({
					type: 'show-collection',
					data: collectionSetup
				});

			} catch (err) {

				console.log('err::', err);

			}
			
		}
		
		function create_comment(comment, beforeSave, afterSave) {

			//Check the final comment to make sure every mentioned user is still present
			validate_mentions(comment.note, function (usrs, isPublic) {

				var	newComment = {
					type_id: Setup.related_object,
					author: +sb.data.cookie.userId,
					notification:{
						producer: Setup.related_object.id,
						link: getObjectPageParams(Setup.related_object),
						notify: Setup.mentionedUsers
					},
					note: parseLinks(comment.note),
					note_type: comment.note_type,
					edited: 0,
					public: (comment.public || isPublic),
                    record_type: 'comment'
				};

                // if this is a comment made in the portal, then create an RCA event (notification)
                if ( appConfig.is_portal || appConfig.isFoundationGroupPortal) {
                    newComment.notification.rca = true;
                }

				if (typeof newComment.type_id === 'object' && typeof newComment.type_id.id === 'number') {
					newComment.type_id = newComment.type_id.id;
				}
				
				// Make specific to the field it is about
				if (options.fieldKey) {
					newComment.field = options.fieldKey;
				}

				if (
					appConfig.state
					&& appConfig.state.portal > 0
				) {
					newComment.public = 1;
				}

				// Additional tags
				if (!_.isEmpty(options) && !_.isEmpty(options.tags)) {
					newComment.tagged_with = options.tags;
				}

				$('#loader').fadeIn();

				sb.data.db.obj.postComment(newComment, function(created) {

					sb.data.db.controller('parseAuthors', {
						data: created
					}, function(data) {
						afterSave(data[0]);
					});
					
				}, {
					last_updated: true,
					note_type: {
						name: true
					},
					note: true,
					author: 'id',
					icon: true,
					type: true,
					type_id:{
						id: true
						, name: true
						, object_bp_type: true
						, group_type: true
					},
					public: true,
					record_type: true
				});
				
			});
			
		}

		function validate_mentions (note, onComplete) {
			// Checks that the mentions should stay in the list to be notified 
			// (issue where they can be removed from the text but remain in the
			// list)
			// note 			Object | the related object of the note feed
			// onComplete 	  Function | callback to run when completed, receiving 
			// 						     the updated list of mentions

			var mentionedClients = [];

			// Iterate over every mentioned user backwards, incase we need to remove a user and not mess up the index
			for (var i = Setup.mentionedUsers.length -1; i >= 0 ; i--) {
					
					// Grab the userId from list
					var userId = Setup.mentionedUsers[i];

					// Match user from the entire user list
					var mentUser = _.find(
										Setup.usersList
										, function(us){
					
											return us.id === userId;	
					
										}
									);

					//Lower case text, to make matching more effective
					var lowerName = mentUser.name.toLowerCase();
					var lowerContent = note.toLowerCase();

					//check to see if the note contains the mentioned user
					var namePresentInNote = lowerContent.includes(lowerName);
					
			    if (!namePresentInNote) {
			    	
			    	//Remove user if they were deleted from the note. 
			        Setup.mentionedUsers.splice(i, 1);
			        
			    } else {
				    
				    // If the mention is a client, add to mentioned clients
					if (mentUser.object_bp_type === 'contacts') {
						mentionedClients.push(mentUser);
					}
				    
			    }
			    
			}

			if (mentionedClients.length > 0) {
				
				var alertTxt = '';
				_.each(mentionedClients, function (client, i) {
					
					if (i > 0) {
						alertTxt += ', ';
					}
					alertTxt += client.fname +' '+ client.lname +' at '+ client.company.name;
					
				});
				
				alertTxt += ' was mentioned, and will be notified of this message.';
				
				sb.dom.alerts.ask({
					title: 'Post to client(s)?'
					, text: alertTxt
				}, function (response) {
					
					swal.close();
					if (response) {
						
						onComplete(Setup.mentionedUsers, true);
						
					} else {
						
						Methods.refresh.postBtn();
						
					}
					
				});
				
			} else {
				
				onComplete(Setup.mentionedUsers, false);
				
			}
			
		}
		
		function build_comment(ui, obj) {

			var profile_img = '';
			var author = '';
			var note_type = '';
			var editedFlag = '';
			var timeDiff = moment().local().diff(obj.last_updated, 'minutes');
			var timeText = moment(obj.last_updated).local().from(moment().local());
			var publicMsg
			var noteText = obj.note;

			if ( 
				Setup.state 
				&& Setup.state.hasOwnProperty('tool')
				&& ((Setup.state.tool || {}).settings || {}).summary
				 
			){
				if (
					obj.type_id 
					&& obj.type_id.hasOwnProperty('id')
					&& obj.type_id.id !== Setup.objectId
				) {				
					noteText = 'Entry created';
				}			
				
			} 

			if(timeDiff > 60) {
				
				timeText = moment(obj.last_updated, 'YYYY-MM-DD hh:mm:ss.SSSSS').local().format('M/D/YYYY, h:mm a');
				
			}

			if(obj.edited === 1) {
				
				editedFlag = '(Edited)';
				
			} else {
				
				editedFlag = '';
				
			}

			if(_.isObject(obj.author) && obj.author.hasOwnProperty('id')) {

				author = obj.author;
				
				if (
					!_.isEmpty(obj.author.profile_image)
					&& !_.isEmpty(obj.author)
					&& obj.author.profile_image.loc != "//" 
					&& obj.author.profile_image !== null 
					&& obj.author.profile_image !== undefined
				) {
				
					profileImg = '<img class="ui mini avatar circular image" src="' + sb.data.files.getURL(obj.author.profile_image) + '">';
					
				} else {
					
					profileImg = '<i class="big user circle icon"></i>';
					
				}	
				
			} else {
				
				author = obj.created_by;
				profileImg = '<i class="big user circle icon"></i>';
				
			}

			ui.makeNode('comments', 'div', {
				css: 'ui comments',
				style: 'max-width: none !important;'
			});
			
			ui.comments.makeNode('comment', 'div', {
				css: 'comment'
			});
			
			ui.comments.comment.makeNode('avatar', 'div', {
				tag: 'a',
				css: 'ui circular mini avatar image',
				text: profileImg
			});
			
			ui.comments.comment.makeNode('content', 'div', {
				css: 'content'
			});
			
			if (!_.isEmpty(author)) {
				
				ui.comments.comment.content.makeNode('author', 'div', {
					tag: 'span',
					css: 'author',
					text: author.fname + ' ' + author.lname
				});
				
			}
			
			ui.comments.comment.content.makeNode('metadata', 'div', {
				css: 'metadata'
			});
			
			if(_.isObject(obj.note_type) && obj.note_type.hasOwnProperty('name')) {
				
				note_type = obj.note_type.name;
			}

			ui.comments.comment.content.metadata.makeNode('date', 'div', {
				tag: 'span',
				css: 'date',
				text: '<span style="color:#1E7ACA !important; margin-right: 5px;">'+ note_type +'</span>' + timeText + ' ' + editedFlag
			});
			
			// Link to the object.
			if (
				obj.type_id 
				&& obj.type_id.hasOwnProperty('id')
				&& obj.type_id.id !== Setup.objectId
			) {
			
				ui.comments.comment.content.metadata.makeNode('link', 'div', {
					tag: 'a',
					href: sb.data.url.createPageURL('object', {
						id: obj.type_id.id,
						name: obj.type_id.name,
						type: obj.type_id.object_bp_type,
						group_type: obj.type_id.group_type
					}),
					text: 'in ' + obj.type_id.name,
					css: 'link',
					style: 'padding: 0 !important;'
				});																
				
			}
			
			ui.comments.comment.content.makeNode('note', 'div', {
				css: 'text ql-editor',
				text: noteText
			});

			if(!_.isEmpty(author) && +sb.data.cookie.userId === author.id) {
				
				if (_.isUndefined(Setup.actions)) {

					ui.comments.comment.content.makeNode('actions', 'div', {
						css: 'actions'
					});
					
					ui.comments.comment.content.actions.makeNode('edit', 'div', {
						tag: 'a',
						text: 'Edit',
						css: 'reply'
					});
					
					ui.comments.comment.content.actions.edit.listeners.push(function(selector) {
						
						$(selector).on('click', function(e) {
							
							ui.comments.comment.content.empty();
							ui.comments.comment.content.patch();
							
							build_input(ui.comments.comment, {
								onEdit: true,
								comment: obj,
								shouldPatchInFieldContainer: true
							});
							
						});
						
					});
					
					ui.comments.comment.content.actions.makeNode('delete', 'div', {
						tag: 'a',
						text: 'Delete',
						css: 'reply'
					}).notify('click', {
						type: 'notes-run',
						data: {
							run: function(data) {
								
								sb.dom.alerts.ask({
									title: 'Are you sure?',
									text: 'This cannot be undone.'
									
								}, function(response){
																			
									if(response) {
										
										swal.disableButtons();
										
										sb.data.db.obj.erase('notes', obj.id, function(res){
											
											swal.close();
											
											Setup.removeFromCollections(obj);
											
											sb.notify({
												type: 'display-alert',
												data: {
													header: 'Comment Removed',
													body: 'Your comment has been removed successfully',
													color: 'green'
												}
											});
											
										});
										
									} 
									
								});
								
							}
						}
					}, sb.moduleId);
					
				}
				
			}
			
			if (
				!(
					appConfig.state
					&& appConfig.state.portal > 0
				)
				&& obj.public
			) {
				
				ui.listeners.push(
					function (selector) {
						
						$(selector).parent().css('border-left', '4px solid rgb(251,189,8, 0.6)');
						
					}
				);
				
			}
			
		}
		
		function parseLinks(noteString){

			var urlRegex = /^(?:(?:ht|f)tp(?:s?)\:\/\/|~\/|\/)?(?:\w+:\w+@)?((?:(?:[-\w\d{1-3}]+\.)+(?:com|org|net|gov|mil|biz|info|mobi|name|aero|jobs|edu|co\.uk|ac\.uk|it|fr|tv|museum|asia|local|travel|[a-z]{2}))|((\b25[0-5]\b|\b[2][0-4][0-9]\b|\b[0-1]?[0-9]?[0-9]\b)(\.(\b25[0-5]\b|\b[2][0-4][0-9]\b|\b[0-1]?[0-9]?[0-9]\b)){3}))(?::[\d]{1,5})?(?:(?:(?:\/(?:[-\w~!$+|.,=]|%[a-f\d]{2})+)+|\/)+|\?|#)?(?:(?:\?(?:[-\w~!$+|.,*:]|%[a-f\d{2}])+=?(?:[-\w~!$+|.,*:=]|%[a-f\d]{2})*)(?:&(?:[-\w~!$+|.,*:]|%[a-f\d{2}])+=?(?:[-\w~!$+|.,*:=]|%[a-f\d]{2})*)*)*(?:#(?:[-\w~!$ |\/.,*:;=]|%[a-f\d]{2})*)?$/i;
			
			var link = noteString.replace(urlRegex, function(url) {
				return '<a href="' + url + '" target="_blank">' + url + '</a>';			
			});

			return link;
					
		}

		if (Setup && Setup.fieldKey === '_default') {
			return;
		}

		ui.empty();

		Loader(ui, 'Fetching comments');

		ui.patch();
		
		// #2014 - potential
		sb.data.db.obj.getById('', +Setup.objectId, function(related) {
	
			if (!related && Setup.obj) {
				related = Setup.obj;
			}

			Setup.related_object = related;

			// #2014 - potential
			getUsers(Setup, function (usersList) {
				
				Setup.usersList =  usersList;
		
				Setup.currentUser = _.find(usersList, {
					id:+sb.data.cookie.userId
				});
	
				// #2014 - potential
				sb.data.db.obj.getAll('note_types', function(noteTypesList) {

					Setup.noteTypesList = noteTypesList;
		
					var defaultNoteType = _.find(noteTypesList, {
						name:'General', 
						default:'yes'
					});
                    if ( Setup.defaultNoteType && typeof Setup.defaultNoteType == 'number' ) {
                        defaultNoteType = _.find(noteTypesList, {id: Setup.defaultNoteType});
                    }
                    Setup.defaultNoteType = defaultNoteType;
					ui.empty();
	
					inputUI = ui.makeNode('input', 'div', {});
					ui.makeNode('feed', 'div', {});
					
					if (!options.activityFeed) {
						
						if (!_.isUndefined(defaultNoteType) ) {
	
							
							build_input(ui.input, {
								fieldKey: options.fieldKey || false
							});
	
						} else {
	
							sb.data.db.obj.create('note_types', {
								name:'General', 
								default:'yes'
							}, function(resp) {
	
								build_input(ui.input, {});
	
							});
	
						}
						
					}

					init_commentsCollections(ui.feed, {
						related_object: 	related
						, fieldKey: 		options.fieldKey || false
					});
	
					ui.patch();
	
				});
				
			});
		
		}, undefined, true);

	}

	function Loader(ui, text) {

		ui.makeNode('seg', 'div', {
			css: 'ui basic segment'
		});
		ui.seg.makeNode('dimmer', 'div', {css: 'ui active inverted dimmer'});
		ui.seg.dimmer.makeNode('loader_text', 'div', {
			text: text,
			css: 'ui active centered inline text loader'
		});

	}
	
	// Tools
	
	function viewActivityFeed (dom, state, draw) {

		var data = {
			domObj: 		dom
			, objectId: 	state.pageObject.id
			, state: 		state
		};
		var Setup = CheckSetup(data);

        var initCommentOpts =  {
            activityFeed: true
        };

        if ( state.recent_client_activity ) 
            initCommentOpts.recent_client_activity = state.recent_client_activity;

		InitComments(
			Setup.domObj
			, initCommentOpts
			, Setup
		);
	
	}
	
	return {

		init: function() {

			sb.listen({
				'show-note-list-box': this.display,
				[sb.moduleId+'-run']: this.run
			});
			
			var toolRegistrationSetup = [
				{
					id:'settings',
					type:'settings',
					title:'Settings',
					icon:'<i class="fa fa-cog"></i>',
					setup:[{
						object_type:'note_types',
						name:'Comment Categories'
					}]
				},
				// HQ Tool
				{
					id:'noteTool',
					type:'hqTool',
					name:'Comments',
					tip:'View all comments.',
					icon: {
						type: 'comment',
						color: 'blue'
					},
					default:true,
					settings:[
						{
							object_type:'note_types',
							name:'Comment Categories'
						}/*
						Commenting out CommentSettings until feature blocks enabled
						,
						{
							object_type:'test',
							name:'Comment Settings',
							action : commentSettings
						}*/
					],
					mainViews:[
						{
							dom:function(dom, state, draw, mainDom){

                                if ( appConfig.instance == 'foundation_group' || appConfig.instance == 'rickyvoltz') {

                                    sb.notify({
                                        type:'show-collection',
                                        data:{
                                            query: getComments.bind({tool: 'noteTool'}),
                                            actions:{
                                                view: true
                                                , download: true
                                                , archive: true
                                                , downloadPdf: 	true
                                                , downloadCSV: true
                                            },
                                            domObj:dom,
                                            fields:{
                                                company: {
                                                    title: "Organization",
                                                    type: 'title',
                                                    view: function(ui, obj) {
                                                        var company = ( (obj.related_company || {}).name ) ? obj.company : ' - ';
                                                        if (obj.related_company) {
                                                                company = obj.related_company.name;
                                                        }

                                                        if (ui) {
                                                            ui.makeNode('author', 'div', {text: company});
                                                        } else {
                                                            return company;
                                                        }

                                                    }
                                                },
                                                note:{
                                                    title:"Comment",
                                                    isSearchable:true,
                                                    type: 'title',
                                                    view:function(ui, obj){
    
                                                        // Input HTML string with line breaks
                                                        var htmlString = obj.note;
    
                                                        // Regular expression to match line breaks
                                                        var regex = /<br\s*\/?>/gi;
    
                                                        // Remove line breaks using replace() method
                                                        var cleanHtmlString = htmlString.replace(regex, "");
                                                        


                                                        if (ui) {
                                                            ui.makeNode('summary', 'div', {
                                                                style: 'white-space: normal', 
                                                                text: cleanHtmlString
                                                            });
                                                        } else {
                                                            cleanHtmlString = cleanHtmlString.replace(/<(.|\n)*?>/g, '');
                                                            return cleanHtmlString;
                                                        }
    
                                                    }
                                                }, 
                                                created_by: {
                                                    title: "Made By",
                                                    type: 'title',
                                                    view: function(ui, obj) {
                                                        var author = ( (obj.author || {}).name ) ? obj.author.name : 'created by ...';
                                                    
                                                        if (ui) {
                                                            ui.makeNode('author', 'div', {text: author});
                                                        } else {
                                                            return author;
                                                        } 
                                                    }
                                                }, 
                                                related: {
                                                    isSearchable:	false
                                                    , title: "Comment About"
                                                    , view: function(ui, obj) {

                                                        if (obj.type_id && obj.type_id.hasOwnProperty('id')) {

                                                            if (ui) {
                                                                ui.makeNode('link', 'div', {
                                                                    tag: 'a',
                                                                    href: sb.data.url.createPageURL('object', {
                                                                        id: obj.type_id.id,
                                                                        name: obj.type_id.name,
                                                                        type: obj.type_id.object_bp_type,
                                                                        group_type: obj.type_id.group_type
                                                                    }),
                                                                    text: obj.type_id.name,
                                                                    css: 'link',
                                                                    style: 'padding: 0 !important;'
                                                                });	 
                                                            } else {
                                                                return obj.type_id.name;
                                                            } 

                                                        }
                                                    }
                                                },
                                                project: {
                                                    isSearchable:	false
                                                    , title:"Related Project"
                                                    , view: function(ui, obj) {

                                                        if (obj.related_project) {
                                                
                                                            if (ui) {
                                                                ui.makeNode('comp', 'div', {text: obj.related_project.name});
                                                            } else {
                                                                return obj.related_project.name;
                                                            }

                                                        }

                                                    }
                                                },
                                                status: {
                                                    isSearchable:	false
                                                    , title:"Project Status"
                                                    , view: function(ui, obj) {

                                                        if (obj.related_project) {

                                                            var stateName = '-';
                                                            var status = _.find(obj.related_project.type.states, function(state){
                                                                return state.uid == obj.related_project.state;
                                                            });
                                                            if (status)
                                                                stateName = status.name;

                                                            if (ui) {
                                                                ui.makeNode('comp', 'div', {text: stateName});
                                                            } else {
                                                                return stateName;
                                                            } 

                                                        }

                                                    }
                                                },
                                                date_created:{
                                                    title:"Comment Made",
                                                    type: 'detail',
                                                    view:function(ui, obj){

                                                        var date_created = moment(obj.date_created).local().format('M/D/YYYY, h:mm a');

                                                        if (ui) {
                                                            ui.makeNode('date', 'div', {css: '', text:date_created});
                                                        } else {
                                                            return date_created;
                                                        }
                                                    }
                                                }
                                            },
                                            objectType:'notes',
                                            singleView:{
                                                view:function(ui, obj, draw){
                                                    singleState(obj, ui, {}, draw);
                                                },
                                                select:3
                                            },
                                            pageLength:50,
                                            selectedView: 'table',
                                            groupings: {
                                                by_range: true
                                            },
                                            subviews: {
                                                list: {
                                                    range: {
                                                        defaultTo: 'next_3_months',
                                                        not: ['all_time']
                                                    },
                                                    groupBy: {
                                                        defaultTo: 'by_range'
                                                    }
                                                }
                                            },
                                            search: true,
                                            filterBy: {
                                                typeFilter: 'comment'
                                            }
                                            , sortCol: 'date_created'
                                            , sortDir: 'desc'
                                            , where:{
                                                record_type: 'comment', 
                                                childObjs:{
                                                    last_updated: true,
                                                    note_type: {
                                                        name: true
                                                    },
                                                    note: true,
                                                    author: 'id',
                                                    icon: true,
                                                    type: true,
                                                    type_id:{
                                                        id: true
                                                        , name: true
                                                        , object_bp_type: true
                                                        , group_type: true
                                                        , parent: true
                                                        , related_object: true
                                                    },
                                                    public: true,
                                                    record_type: true,
                                                    state: true
                                                }
                                            }
                                        }
                                    });

                                } else {

                                    sb.notify({
                                        type:'show-collection',
                                        data:{
                                            query: getComments,
                                            actions:{
                                                view: false,
                                                copy: false
                                            },
                                            domObj:dom,
                                            fields:{
                                                note:{
                                                    title:'Comment',
                                                    isSearchable:true,
                                                    type: 'title',
                                                    view:function(ui, obj){
        
                                                        var createdBy = '';
                                                        var authorImg = '';
                                                        var authorName = '';
                                                        var timeDiff = moment().diff(obj.last_updated, 'minutes');
                                                        var timeText = moment(obj.last_updated).local().format('M/D/YYYY, h:mm a');;
                                                        
                                                        if(timeDiff > 60) {
                                                            
                                                            timeText = moment(obj.last_updated, 'YYYY-MM-DD hh:mm:ss.SSSSS').local().format('M/D/YYYY, h:mm a');
                                                            
                                                        } 
                                                        
                                                        if(obj.author !== null) {
                                                            
                                                            authorName = 'by ' + obj.author.fname + ' ' + obj.author.lname;
        
                                                            if (
                                                                !_.isEmpty(obj.author) 
                                                                && !_.isEmpty(obj.author.profile_image) 
                                                                && obj.author.profile_image !== undefined 
                                                                && obj.author.profile_image !== null 
                                                                && obj.author.profile_image.loc != "//"
                                                            ) {
        
                                                                authorImg = '<img src="'+ sb.data.files.getURL(obj.author.profile_image) +'">';
                                                                
                                                            } else {
        
                                                                authorImg = '<i class="big user circle icon"></i>';
                                                                
                                                            }	
                                                            
                                                        } else {
        
                                                            authorImg = '<i class="exclamation icon"></i>'
                                                            
                                                        }
        
                                                        ui.makeNode('feed', 'div', {
                                                            css: 'ui feed'
                                                        });
                                                        
                                                        ui.feed.makeNode('event', 'div', {
                                                            css: 'event'
                                                        });
                                                        
                                                        ui.feed.event.makeNode('label', 'div', {
                                                            text: authorImg,
                                                            css: 'label'
                                                        });
                                                        
                                                        ui.feed.event.makeNode('content', 'div', {
                                                            css: 'content'
                                                        });
                                                        
                                                        if(authorName === '') {
                                                            authorName = '(System message)';
                                                        }
                                                        
                                                        ui.feed.event.content.makeNode('date', 'div', {
                                                            css: 'date',
                                                            text: timeText + ' ' + authorName  
                                                        });
                                                        
                                                        ui.feed.event.content.makeNode('summary', 'div', {
                                                            css: 'summary',
                                                            text: obj.note
                                                        });
                                                        
                                                        ui.feed.event.content.makeNode('extra', 'div', {
                                                            css: 'extra'
                                                        });
                    
                                                        if (obj.type_id && obj.type_id.hasOwnProperty('id')) {
                                                        
                                                            ui.feed.event.content.extra.makeNode('link', 'div', {
                                                                tag: 'a',
                                                                href: sb.data.url.createPageURL('object', {
                                                                    id: obj.type_id.id,
                                                                    name: obj.type_id.name,
                                                                    type: obj.type_id.object_bp_type,
                                                                    group_type: obj.type_id.group_type
                                                                }),
                                                                text: 'on ' + obj.type_id.name,
                                                                css: 'link',
                                                                style: 'padding: 0 !important;'
                                                            });																
                                                            
                                                        }
                                                        
                                                    }
                                                },
                                                date_created:{
                                                    title:'Created On',
                                                    type: 'detail',
                                                    view:function(ui, obj){
                                                        ui.makeNode('date', 'div', {css: '', text:moment(obj.date_created).local().format("MMM Do YYYY , h:mm a")});
                                                    }
                                                }
                                            },
                                            objectType:'notes',
                                            singleView:{
                                                view:function(ui, obj, draw){
                                                    singleState(obj, ui, {}, draw);
                                                },
                                                select:3
                                            },
                                            pageLength:50,
                                            parseData:function(data, callback){
                                                
                                                _.each(data.data, function(item, i){
        
                                                    var name = '';
                                                    var type = '';
                                                    
                                                    if(!_.isNull(item.type_id) && !_.isUndefined(item.type_id)){
                                                        
                                                        name = item.type_id.name;
                                                        
                                                        if (_.isEmpty(name)) {
                                                            
                                                            name = item.type_id.fname +' '+ item.type_id.lname;
                                                        }
                                                                                                                                                        
                                                        var type = item.type_id.object_bp_type;
                                                        
                                                        if (type === 'groups') {
                                                            type = item.type;
                                                        }
                                                        
                                                        item.link = sb.data.url.createPageURL(
                                                            'object', 
                                                            {
                                                                type:type, 
                                                                id:item.type_id.id,
                                                                name:name
                                                            }
                                                        );
                                                        
                                                    }
                                                    
                                                });
                                                
                                                callback(data);
                                                
                                            },
                                            selectedView: 'list',
                                            menu: false,
                                            groupings: {
                                                by_range: true
                                            },
                                            subviews: {
                                                list: {
                                                    range: {
                                                        defaultTo: 'next_3_months',
                                                        not: ['all_time']
                                                    },
                                                    groupBy: {
                                                        defaultTo: 'by_range'
                                                    }
                                                }
                                            },
                                            search: false,
                                            where:{
                                                childObjs:{
                                                    last_updated: true,
                                                    note_type: {
                                                        name: true
                                                    },
                                                    note: true,
                                                    author: 'id',
                                                    icon: true,
                                                    type: true,
                                                    type_id:{
                                                        id: true
                                                        , name: true
                                                        , object_bp_type: true
                                                        , group_type: true
                                                    },
                                                    public: true,
                                                    record_type: true
                                                }
                                            }
                                        }
                                    });
                                }
																
							}
						}
					]
				},
				// Team Tool
				{
					id:'noteTool',
						type: 'teamTool',
						name: 'Comment Feed',
						tip: 'View all comments.',
						icon: {
							type: 'comment',
							color: 'blue'
						},
					default: true,
					mainViews: [
						{
							dom: function(dom, state, draw, mainDom) {

								sb.notify({
									type:'show-collection',
									data:{
										query: getComments,
										actions:{
											view: false,
											copy: false
										},
										domObj:dom,
										fields:{
											note:{
												title:'Comment',
												isSearchable:true,
												type: 'title',
												view:function(ui, obj){
	
													var createdBy = '';
													var authorImg = '';
													var authorName = '';
													var timeDiff = moment().diff(obj.last_updated, 'minutes');
													var timeText = moment(obj.last_updated).local().format('M/D/YYYY, h:mm a');
													
													if(timeDiff > 60) {
														
														timeText = moment(obj.last_updated, 'YYYY-MM-DD hh:mm:ss.SSSSS').local().format('M/D/YYYY, h:mm a');
														
													}
													
													if(obj.author !== null) {
														
														authorName = 'by ' + obj.author.fname + ' ' + obj.author.lname;
	
														if(obj.author.profile_image !== undefined && obj.author.profile_image !== null && obj.author.profile_image.loc != "//") {
	
															authorImg = '<img src="'+ sb.data.files.getURL(obj.author.profile_image) +'">';
															
														} else {
	
															authorImg = '<i class="big user circle icon"></i>';
															
														}	
														
													} else {
	
														authorImg = '<i class="exclamation icon"></i>'
														
													}
	
													ui.makeNode('feed', 'div', {
														css: 'ui feed'
													});
													
													ui.feed.makeNode('event', 'div', {
														css: 'event'
													});
													
													ui.feed.event.makeNode('label', 'div', {
														text: authorImg,
														css: 'label'
													});
													
													ui.feed.event.makeNode('content', 'div', {
														css: 'content'
													});
													
													if(authorName === '') {
														authorName = '(System message)';
													}
													
													ui.feed.event.content.makeNode('date', 'div', {
														css: 'date',
														text: timeText + ' ' + authorName  
													});
													
													ui.feed.event.content.makeNode('summary', 'div', {
														css: 'summary',
														text: obj.note
													});
													
													ui.feed.event.content.makeNode('extra', 'div', {
														css: 'extra'
													});
													
													
													if (obj.type_id && obj.type_id.hasOwnProperty('id')) {
	
														ui.feed.event.content.extra.makeNode('link', 'div', {
															tag: 'a',
															href: sb.data.url.createPageURL('object', {
																id: obj.type_id.id,
																name: obj.type_id.name,
																type: obj.type_id.object_bp_type,
																group_type: obj.type_id.group_type
															}),
															text: 'on ' + obj.type_id.name,
															css: 'link',
															style: 'padding: 0 !important;'
														});
													
													}
													
												}
											},
											date_created:{
												title:'Created On',
												type: 'detail',
												view:function(ui, obj){
													ui.makeNode('date', 'div', {css: '', text:moment(obj.date_created).local().format("MMM Do YYYY , h:mm a")});
												}
											}
										},
										objectType:'notes',
										singleView:{
											view:function(ui, obj, draw){
												singleState(obj, ui, {}, draw);
											},
											select:3
										},
										pageLength:50,
										parseData:function(data, callback){
											
											_.each(data.data, function(item, i){
	
												var name = '';
												var type = '';
												
												if(!_.isNull(item.type_id) && !_.isUndefined(item.type_id)){
													
													name = item.type_id.name;
													
													if (_.isEmpty(name)) {
														
														name = item.type_id.fname +' '+ item.type_id.lname;
													}
																																					
													var type = item.type_id.object_bp_type;
													
													if (type === 'groups') {
														type = item.type;
													}
													
													item.link = sb.data.url.createPageURL(
														'object', 
														{
															type:type, 
															id:item.type_id.id,
															name:name
														}
													);
													
												}
												
											});
											
											callback(data);
											
										},
										selectedView: 'list',
										menu: false,
										groupings: {
											by_range: true
										},
										subviews: {
											list: {
												range: {
													defaultTo: 'next_3_months',
													not: ['all_time']
												},
												groupBy: {
													defaultTo: 'by_range'
												}
											}
										},
										search: false,
										where: {
											tagged_with: [state.team.id],
											public:1
											, record_type: {
												type: 'not_equal',
												value: 'log'
											}, 
											childObjs: {
												last_updated: true,
												note_type: {
													name: true
												},
												note: true,
												author: 'id',
												icon: true,
												type: true,
												type_id:{
													id: true
													, name: true
													, object_bp_type: true
													, group_type: true
												},
												public: true,
												record_type: true
											}
										}
									}
								});
								
							}
						}
					]
				},
                {
					id:'rcaTool',
						type: 'teamTool',
						name: 'Recent Client Activity Log',
						tip: 'System generated RCA logs',
						icon: {
							type: 'tags',
							color: 'red'
						},
					default: true,
					mainViews: [
						{
							dom: function(dom, state, draw, mainDom) {

								sb.notify({
									type:'show-collection',
									data:{
										query: getComments,
										actions:{
											view: false,
											copy: false
										},
										domObj:dom,
										fields:{
											note:{
												title:'Comment',
												isSearchable:true,
												type: 'title',
												view:function(ui, obj){
	
													var createdBy = '';
													var authorImg = '';
													var authorName = '';
													var timeDiff = moment().diff(obj.last_updated, 'minutes');
													var timeText = moment(obj.last_updated).local().format('M/D/YYYY, h:mm a');
													
													if(timeDiff > 60) {
														
														timeText = moment(obj.last_updated, 'YYYY-MM-DD hh:mm:ss.SSSSS').local().format('M/D/YYYY, h:mm a');
														
													}
													
													if(obj.author !== null) {
														
														authorName = 'by ' + obj.author.fname + ' ' + obj.author.lname;
	
														if(obj.author.profile_image !== undefined && obj.author.profile_image !== null && obj.author.profile_image.loc != "//") {
	
															authorImg = '<img src="'+ sb.data.files.getURL(obj.author.profile_image) +'">';
															
														} else {
	
															authorImg = '<i class="big user circle icon"></i>';
															
														}	
														
													} else {
	
														authorImg = '<i class="exclamation icon"></i>'
														
													}
	
													ui.makeNode('feed', 'div', {
														css: 'ui feed'
													});
													
													ui.feed.makeNode('event', 'div', {
														css: 'event'
													});
													
													ui.feed.event.makeNode('label', 'div', {
														text: authorImg,
														css: 'label'
													});
													
													ui.feed.event.makeNode('content', 'div', {
														css: 'content'
													});
													
													if(authorName === '') {
														authorName = '(System message)';
													}
													
													ui.feed.event.content.makeNode('date', 'div', {
														css: 'date',
														text: timeText + ' ' + authorName  
													});
													
													ui.feed.event.content.makeNode('summary', 'div', {
														css: 'summary',
														text: obj.note
													});
													
													ui.feed.event.content.makeNode('extra', 'div', {
														css: 'extra'
													});
													
													
													if (obj.type_id && obj.type_id.hasOwnProperty('id')) {
	
														ui.feed.event.content.extra.makeNode('link', 'div', {
															tag: 'a',
															href: sb.data.url.createPageURL('object', {
																id: obj.type_id.id,
																name: obj.type_id.name,
																type: obj.type_id.object_bp_type,
																group_type: obj.type_id.group_type
															}),
															text: 'on ' + obj.type_id.name,
															css: 'link',
															style: 'padding: 0 !important;'
														});
													
													}
													
												}
											},
											date_created:{
												title:'Created On',
												type: 'detail',
												view:function(ui, obj){
													ui.makeNode('date', 'div', {css: '', text:moment(obj.date_created).local().format("MMM Do YYYY , h:mm a")});
												}
											}
										},
										objectType:'notes',
										singleView:{
											view:function(ui, obj, draw){
												singleState(obj, ui, {}, draw);
											},
											select:3
										},
										pageLength:50,
										parseData:function(data, callback){
											
											_.each(data.data, function(item, i){
	
												var name = '';
												var type = '';
												
												if(!_.isNull(item.type_id) && !_.isUndefined(item.type_id)){
													
													name = item.type_id.name;
													
													if (_.isEmpty(name)) {
														
														name = item.type_id.fname +' '+ item.type_id.lname;
													}
																																					
													var type = item.type_id.object_bp_type;
													
													if (type === 'groups') {
														type = item.type;
													}
													
													item.link = sb.data.url.createPageURL(
														'object', 
														{
															type:type, 
															id:item.type_id.id,
															name:name
														}
													);
													
												}
												
											});
											
											callback(data);
											
										},
										selectedView: 'list',
										menu: false,
										groupings: {
											by_range: true
										},
										subviews: {
											list: {
												range: {
													defaultTo: 'next_3_months',
													not: ['all_time']
												},
												groupBy: {
													defaultTo: 'by_range'
												}
											}
										},
										search: false,
										where: {
											tagged_with: [state.team.id]
											, record_type: 'log'
                                            , log_type: 'recent-client-action'
											, childObjs: {
												last_updated: true,
												note_type: {
													name: true
												},
												note: true,
												author: 'id',
												icon: true,
												type: true,
												type_id:{
													id: true
													, name: true
													, object_bp_type: true
													, group_type: true
												},
												public: true,
												record_type: true
											}
										}
									}
								});
								
							}
						}
					]
				},
				// Project activity feed
				{
					id:		'activityFeed',
					type:	'tool',
					name:	'Activity Feed',
					tip:	'See this project\'s history.',
					icon: 	{
						type: 	'history',
						color: 	'grey'
					},
					default:	true,
					settings:	false,
					mainViews:	[
						{
							dom: viewActivityFeed
						}
					]
				},				
                // Project activity feed
				{
					id:		'projectActivityFeed',
					type:	'tool',
					name:	'Recent Client Activity Log',
					tip:	'See this project\'s history.',
					icon: 	{
						type: 	'tags',
						color: 	'red'
					},
					default:	true,
					settings:	false,
					mainViews:	[
						{
							dom: function(dom, state, draw, mainDom) {

								sb.notify({
									type:'show-collection',
									data:{
										query: getComments,
										actions:{
											view: false,
											copy: false
										},
										domObj:dom,
										fields:{
											note:{
												title:'Comment',
												isSearchable:true,
												type: 'title',
												view:function(ui, obj){
	
													var createdBy = '';
													var authorImg = '';
													var authorName = '';
													var timeDiff = moment().diff(obj.last_updated, 'minutes');
													var timeText = moment(obj.last_updated).local().format('M/D/YYYY, h:mm a');
													
													if(timeDiff > 60) {
														
														timeText = moment(obj.last_updated, 'YYYY-MM-DD hh:mm:ss.SSSSS').local().format('M/D/YYYY, h:mm a');
														
													}
													
													if(obj.author !== null) {
														
														authorName = 'by ' + obj.author.fname + ' ' + obj.author.lname;
	
														if(obj.author.profile_image !== undefined && obj.author.profile_image !== null && obj.author.profile_image.loc != "//") {
	
															authorImg = '<img src="'+ sb.data.files.getURL(obj.author.profile_image) +'">';
															
														} else {
	
															authorImg = '<i class="big user circle icon"></i>';
															
														}	
														
													} else {
	
														authorImg = '<i class="exclamation icon"></i>'
														
													}
	
													ui.makeNode('feed', 'div', {
														css: 'ui feed'
													});
													
													ui.feed.makeNode('event', 'div', {
														css: 'event'
													});
													
													ui.feed.event.makeNode('label', 'div', {
														text: authorImg,
														css: 'label'
													});
													
													ui.feed.event.makeNode('content', 'div', {
														css: 'content'
													});
													
													if(authorName === '') {
														authorName = '(System message)';
													}
													
													ui.feed.event.content.makeNode('date', 'div', {
														css: 'date',
														text: timeText + ' ' + authorName  
													});
													
													ui.feed.event.content.makeNode('summary', 'div', {
														css: 'summary',
														text: obj.note
													});
													
													ui.feed.event.content.makeNode('extra', 'div', {
														css: 'extra'
													});
													
													
													if (obj.type_id && obj.type_id.hasOwnProperty('id')) {
	
														ui.feed.event.content.extra.makeNode('link', 'div', {
															tag: 'a',
															href: sb.data.url.createPageURL('object', {
																id: obj.type_id.id,
																name: obj.type_id.name,
																type: obj.type_id.object_bp_type,
																group_type: obj.type_id.group_type
															}),
															text: 'on ' + obj.type_id.name,
															css: 'link',
															style: 'padding: 0 !important;'
														});
													
													}
													
												}
											},
											date_created:{
												title:'Created On',
												type: 'detail',
												view:function(ui, obj){
													ui.makeNode('date', 'div', {css: '', text:moment(obj.date_created).local().format("MMM Do YYYY , h:mm a")});
												}
											}
										},
										objectType:'notes',
										singleView:{
											view:function(ui, obj, draw){
												singleState(obj, ui, {}, draw);
											},
											select:3
										},
										pageLength:50,
										parseData:function(data, callback){
											
											_.each(data.data, function(item, i){
	
												var name = '';
												var type = '';
												
												if(!_.isNull(item.type_id) && !_.isUndefined(item.type_id)){
													
													name = item.type_id.name;
													
													if (_.isEmpty(name)) {
														
														name = item.type_id.fname +' '+ item.type_id.lname;
													}
																																					
													var type = item.type_id.object_bp_type;
													
													if (type === 'groups') {
														type = item.type;
													}
													
													item.link = sb.data.url.createPageURL(
														'object', 
														{
															type:type, 
															id:item.type_id.id,
															name:name
														}
													);
													
												}
												
											});
											
											callback(data);
											
										},
										selectedView: 'list',
										menu: false,
										groupings: {
											by_range: true
										},
										subviews: {
											list: {
												range: {
													defaultTo: 'next_3_months',
													not: ['all_time']
												},
												groupBy: {
													defaultTo: 'by_range'
												}
											}
										},
										search: false,
										where: {
											tagged_with: [state.project.id]
											, record_type: 'log'
                                            , log_type: 'recent-client-action'
											, childObjs: {
												last_updated: true,
												note_type: {
													name: true
												},
												note: true,
												author: 'id',
												icon: true,
												type: true,
												type_id:{
													id: true
													, name: true
													, object_bp_type: true
													, group_type: true
												},
												public: true,
												record_type: true
											}
										}
									}
								});
								
							}
						}
					]
				},
				// Project comments
				{
					id:		'commentFeed',
					type:	'tool',
					name:	'Chat Feed',
					tip:	'All comments in the project.',
					icon: 	{
						type: 	'conversation',
						color: 	'grey'
					},
					default:	true,
					settings:	false,
					mainViews:	[
						{
							dom: function (ui, state, draw) {

								// Header area, showing the project name, client name, and company name
								if (
									state
									&& state.project
									&& state.project.main_contact
								) {

									ui.makeNode('h', 'div', {})
										.makeNode('loader', 'div', {
											text: 	'<i class="notched circle loading icon"></i>'
											, style: 	'width:100%;height:100%;text-align:center;'
										});

									sb.data.db.obj.getById(
										'companies'
										, state.project.main_contact.company
										, function (company) {

											var contactLink = sb.data.url.createPageURL(
												'object'
												, {
													name: 	state.project.main_contact.name
													, type: 'contacts'
													, id: 	state.project.main_contact.id
												}
											);
											var companyLink = sb.data.url.createPageURL(
												'object'
												, {
													name: 	company.name
													, type: 'companies'
													, id: 	company.id
												}
											);

											var companyTxt = ' at '+
												'<a class="ui link" href="'+ companyLink +'">'+ company.name +'</a>';

											var txt = state.project.name +
												'<div class="sub header">for '+
												'<a class="ui link" href="'+ contactLink +'">'+ state.project.main_contact.name +'</a>'+
												companyTxt
											'</div>';

											ui.h.empty();
											ui.h.makeNode('h', 'div', {
												tag: 		'h1'
												, css: 		'ui header'
												, text: 	txt
											});
											ui.h.makeNode('d', 'div', {css: 'ui clearing divider'});
											ui.h.patch();

										}
										, {
											name: true
										}
									);

								}

								ui.makeNode('c', 'div', {style: 'padding:22px;'});
								ui.patch();
								
								// Aligning w/public 'display' method
								var data = {
									domObj: 			ui.c
									, objectId: 		state.project.id
									, objectIds: 		[state.project.id]
									, activityFeed: 	false
									, commentsOnly: 	true
								};
								
								var Setup = CheckSetup(data);
								InitComments(Setup.domObj, data, Setup);
								
							}
						}
					]
					, boxViews: [{
						id: 'projectCommentFeed',
						width: 'four',
						title: 'Chat',
	// 									settingsView: function (dom, settings, done) {},
						dom: function (ui, state, draw) {
							
							ui.makeNode('div', 'div', {
								text: 'Chat about this project with the team.'
							});
							draw(ui);
							
						}
					}]
				},
				///entity Tool Comments
				{
					id:'entityCommentFeed',
					type:'tool',
					name:'Chat',
					tip:'Space for logging conversations',
					icon: {
						type: 'conversation',
						color: 'violet'
					},
					availableToEntities: true,
					hiddenFromProjects: true,
					mainViews:[
						{
							dom: function (dom, state, draw, mainDom) {
								
								if (
									state
									&& state.pageObject
									&& state.pageObject.object_bp_type === 'entity_tool'
									&& state.entity > 0
								) {
									
									sb.data.db.obj.getById(
										''
										, state.entity
										, function(resp){

											dom.makeNode('c', 'div', {style: 'padding:22px;'});
											dom.patch();
											
											// Aligning w/public 'display' method
											var data = {
												domObj: 			dom.c
												, objectId: 		resp.id
												, objectIds: 		[resp.id]
												, activityFeed: 	false
												, commentsOnly: 	true
												, state:			state
											};
											
											var Setup = CheckSetup(data);
											
											InitComments(Setup.domObj, data, Setup);
											
										}
									);
									
								} 
									
							}
						}
					],
					boxViews:[]
					, options: {
						name: {
							type: 			'title'
							, title: 			'Name'
						}
						, summary: {
							type: 			'bool'
							, title: 			'Display a summary of Note content for nested notes?'
							, defaultValue: 	false
						}
						, tags: {
							type: 			'tags'
							, title: 			'Tag(s)'
							, defaultValue: 	[]
						}						
					}								
				},							
				// My Stuff activity feed
				{
					id:		'activityFeed',
					type:	'myStuff',
					name:	'Activity',
					tip:	'See your activity.',
					icon: 	{
						type: 	'history',
						color: 	'grey'
					},
					default:	true,
					settings:	false,
					mainViews:	[
						{
							dom: function (ui, state, draw) {
	
								var feedState = _.clone(state);
								feedState.pageObject = appConfig.user;
								viewActivityFeed(ui, feedState, draw);
	
							}
						}
					]
				},
				// My Stuff activity feed
				{
					id:		'activityFeed',
					type:	'hqTool',
					name:	'Activity',
					tip:	'See activity across the system.',
					icon: 	{
						type: 	'history',
						color: 	'grey'
					},
					default:	true,
					settings:	false,
					mainViews:	[
						{
							dom: function (ui, state, draw) {
	
								var feedState = _.clone(state);
								feedState.pageObject = appConfig.headquarters;
								viewActivityFeed(ui, feedState, draw);
	
							}
						}
					]
				}
			];



			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'notes',
						title:'Comments',
						icon:'<i class="fa fa-comments"></i>',
						views: toolRegistrationSetup
					}
				}
			});

			sb.notify({
				type: 'register-field-type'
				, data: {
					name: 					'comments'
					, view: 				function (fieldKey, ui, obj, options) {

						function viewFieldFeed (fieldKey, ui, obj, options, miniView) {

							ui.makeNode('c', 'div', {style: 'padding:8px;'});
							ui.patch();
							
							// Aligning w/public 'display' method
							var data = {
								domObj: 			ui.c
								, objectId: 		obj.id
								, objectIds: 		[obj.id]
								, activityFeed: 	false
								, commentsOnly: 	true
								, fieldKey: 		fieldKey
							};
							
							var Setup = CheckSetup(data);

							// Add tags set in the field config
							if (
								!_.isEmpty(options)
								&& !_.isEmpty(options.tags)
							) {

								data.tags = options.tags;

							}
							
							// Refresh the ui if in the mini view
							if (miniView) {
								
								Setup.onPost = function () {

									count++;
									$(miniView.c.selector).html('<i class="grey comments icon"></i> '+ count);
									
								};
							
							}

							InitComments(Setup.domObj, data, Setup);

						}

						if (options && options.inCollection) {
							
							var count = obj[fieldKey];
							if (!count) {
								count = 0;
							}

							if(options.blueprint[fieldKey].options.showLatest == true){
								
								if(obj[fieldKey+'_latest']){
									var iconTxt = '<small>on '+ moment(obj[fieldKey+'posted_on']).format('MM/DD/YY, h:mm a') +'</small>'+obj[fieldKey+'_latest'];
								}else{
									var iconTxt = '<i class="grey comments icon"></i> '+ count;
								}
						
								
							}else{
								
								var iconTxt = '<i class="grey comments icon"></i> '+ count;
								
							}
							
							ui.makeNode('c', 'div', {
								css: 'ui grey dropdown item field-manager edit'
								, text: iconTxt
							}).notify('click', {
								type: sb.moduleId+'-run'
								, data: {
									run: function (data) {

										sb.notify({
											type: 'get-sys-modal'
											, data: {
												callback: function (modal) {
													
													// modal.empty();
													modal.show();
													viewFieldFeed(fieldKey, modal, obj, options, ui);
													
												}
											}
										});

									}
								}
							});
							
						} else {
							
							if(obj.id){
								
								viewFieldFeed(fieldKey, ui, obj, options);
									
							}else{
								
								ui.makeNode('alert','div',{css:'ui grey text', text:'You can add comments after you create the item.'});
								
								ui.patch();
								
							}

						}

					}
					, title: 				'Comment Feed'
					, type: 				'Keep a record of conversation'
					, availableToEntities: 	true
					, icon: 				'comments'
					, detail: function (fieldName, obj, options) {
						
						var fieldLabel = '';
						if (
							options
							&& (options.mini || options.inCollection)
							&& typeof obj.object_bp_type === 'string'
						) {

							var bp = _.findWhere(appConfig.Types, {bp_name: obj.object_bp_type.substr(1)});
							if (bp && bp.blueprint && bp.blueprint[fieldName]) {
								fieldLabel = bp.blueprint[fieldName].name;
							}
							return fieldLabel;

						}
						
						return false;
						
					}
					, getIcon: function (options) {
						return 'comments';
					}
					, propertyType: 'int'
					, objectType: ''
					, options: {
						/*
, permissions: {
							name: 		'Permissions'
							, type: 	'permissions'
						}
*/
						tags: {
							name: 	'Tag(s)'
							, type: 'tags'
						},
						showLatest: {
							name:	'Show latest comment in collection view?'
							, type:	'bool'
						}
					}
				}
			});

		},

		run: function(data) {

			data.run(data);

		},

		display: function(data) {

			var Setup = CheckSetup(data);
			InitComments(Setup.domObj, undefined, Setup);

		}

	}

	function commentSettings(dom){

		dom.empty();

		dom.makeNode('seg', 'div', {
			css: 'ui basic segment'
		});

		dom.seg.makeNode('notifySec', 'div', {});

		dom.seg.notifySec.makeNode('title', 'div', {
			css: 'ui header'
			, text: '<i class="bell icon"></i> Notifications'
		});
		dom.seg.notifySec.makeNode('cont','div',{});

		dom.seg.notifySec.makeNode('divider1', 'div', {
			css: 'ui divider'
		});

		dom.patch();

	}
});
