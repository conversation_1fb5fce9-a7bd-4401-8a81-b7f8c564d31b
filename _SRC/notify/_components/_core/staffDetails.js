Factory.registerComponent("staffDetails", function (sb) {
  var domObj = {},
    staff = [],
    staffObj = {},
    staffId = 0,
    components = {},
    pageSetup = {};

  return {
    init: function () {
      sb.listen({
        "start-staff-details-component": this.start,
        "display-staff-details": this.load,
        "edit-staff-object": this.editObject,
        "change-staff-password": this.changeStaffPassword,
        "confirm-password-change": this.confirmPasswordChange,
        "reset-staff-password": this.resetStaffPassword,
      });
    },

    start: function () {
      sb.listen({
        "display-staff-details": this.load,
        "stop-staff-details-component": this.stop,
      });
    },

    stop: function () {
      sb.listen({
        "start-staff-details-component": this.start,
        "display-staff-details": this.load,
      });
    },

    load: function (setup) {
      if (setup) {
        if (setup.domContainer) {
          var domObj = setup.domContainer;
        }

        if (setup.staff) {
          var staffObj = setup.staff;
        } else {
          return;
        }
      }

      if (!domObj) {
        return;
      }

      var staffId = setup.staff.id;

      domObj.makeNode("detailColumn1", "container", {
        css: "tile tile-w-12 pda-background-blue",
      }); // This is column 1 of details wrapper

      domObj.makeNode("detailColumn2", "container", { css: "tile tile-w-12" }); // This is the button of details wrapper

      // Detail elements

      _.each(setup.info, function (propName, propKey) {
        var infoText = "";

        switch (propKey) {
          case "date_created":
            infoText = moment(staffObj[propKey]).format("MM/DD/YYYY");

            break;

          case "phone":
            infoText = sb.dom.formatPhone(staffObj[propKey]);

            break;

          default:
            switch (typeof staffObj[propKey]) {
              case "object":
                if (Array.isArray(staffObj[propKey])) {
                  var count = 0;

                  _.each(staffObj[propKey], function (subObj) {
                    if (count > 0) {
                      infoText += ", " + subObj.lname;
                    } else {
                      if (subObj.hasOwnProperty("name")) {
                        infoText += subObj.name;
                      }
                    }

                    count++;
                  });
                } else {
                  infoText = staffObj[propKey].name;
                }

                break;

              default:
                infoText = staffObj[propKey].toString();
            }
        }

        if (infoText == "") {
          infoText = "n/a";
        }

        domObj.detailColumn1.makeNode(propKey, "text", {
          css: "detailsFontSize col1Color",
          text:
            "<span style='font-size: 18px;'>" +
            propName +
            "</span>" +
            ":" +
            "</br>" +
            "<span style='color: black; font-weight: bold;'>" +
            infoText +
            "</span>",
        });
      });

      domObj.detailColumn2.makeNode("staffDetailsBtnsWrapper", "container", {
        css: "tile tile-w-12",
      });

      domObj.detailColumn2.staffDetailsBtnsWrapper.makeNode("lb", "lineBreak", {
        spaces: 1,
      });

      domObj.makeNode("modalContainers", "container", {});

      domObj.detailColumn2.staffDetailsBtnsWrapper
        .makeNode("permissionBtn", "button", {
          css: "pda-Btn pda-btnOutline-primary staffBtn",
          text: "Edit Permissions",
        })
        .notify(
          "click",
          {
            type: "edit-staff-permission",
            data: {
              staff: staffObj,
              modalContainer: domObj.modalContainers,
              updating: false,
            },
          },
          sb.moduleId
        );

      domObj.detailColumn2.staffDetailsBtnsWrapper
        .makeNode("changePasswordButton", "button", {
          css: "pda-Btn pda-btnOutline-green staffBtn",
          text: "Change Password",
        })
        .notify(
          "click",
          {
            type: "change-staff-password",
            data: {
              staff: staffObj,
              modalContainers: domObj.modalContainers,
              updating: false,
            },
          },
          sb.moduleId
        );

      domObj.detailColumn2.staffDetailsBtnsWrapper
        .makeNode("resetStaffButton", "button", {
          css: "pda-Btn pda-btnOutline-red staffBtn",
          text: "Reset Staff Password",
        })
        .notify(
          "click",
          {
            type: "reset-staff-password",
            data: {
              staffObj: staffObj,
            },
          },
          sb.moduleId
        );

      domObj.patch();
    },

    changeStaffPassword: function (changePasswordObj) {
      var domObj = changePasswordObj.modalContainers;

      domObj
        .makeNode("changePasswordModal", "modal", {})
        .body.makeNode("newPasswordText", "headerText", {
          text: "Please enter your new password",
        });

      domObj.changePasswordModal.body.makeNode("titleBreak", "lineBreak", {
        spaces: 1,
      });

      domObj.changePasswordModal.body.makeNode("changePasswordForm", "form", {
        firstPassword: {
          type: "password",
          name: "firstPassword",
          label: "New password",
          value: "",
        },

        confirmPassword: {
          type: "password",
          name: "confirmPassword",
          label: "New password again",
          value: "",
        },
      });

      domObj.changePasswordModal.footer
        .makeNode("confirmPasswordBtn", "button", { text: "Change Password" })
        .notify(
          "click",
          {
            type: "confirm-password-change",
            data: {
              form: domObj.changePasswordModal.body.changePasswordForm,
              staff: changePasswordObj.staffObj,
              domObj: domObj.changePasswordModal.footer.confirmPasswordBtn,
              modal: domObj.changePasswordModal,
            },
          },
          sb.moduleId
        );

      domObj.patch();

      domObj.changePasswordModal.show();
    },

    confirmPasswordChange: function (changePasswordObj) {
      var formObj = changePasswordObj.form.process();
      newPassword = formObj.fields.confirmPassword.value;

      if (newPassword === formObj.fields.confirmPassword.value) {
        changePasswordObj.domObj.makeNode("loading", "text", {
          text: sb.dom.loadingGIF,
        });

        changePasswordObj.domObj.patch();

        sb.data.db.createNewPassword(function (passwordObj) {
          sb.data.db.obj.getById(
            "staff",
            changePasswordObj.staff.id,
            function (orgStaffObj) {
              orgStaffObj.password = passwordObj.pwdHash;

              sb.data.db.obj.update(
                "staff",
                { id: orgStaffObj.id, password: passwordObj.pwdHash },
                function (staffObj) {
                  var args = {
                    to: changePasswordObj.staff.email,
                    from: "Pagoda Dev",
                    subject: "Password Changed",
                    mergevars: {
                      TITLE:
                        "This is a confirmation that your password was changed",
                      BODY:
                        "Username: " +
                        changePasswordObj.staff.email +
                        "<br />Password: " +
                        passwordObj.pwd,
                      BUTTON: "Log in now",
                      BUTTON_LINK: "https://bento.infinityhospitality.net/app/",
                    },
                    emailtags: ["password reset email"],
                  };

                  sb.comm.sendEmail(args, function (response) {
                    if (response) {
                      sb.dom.alerts.alert(
                        "Password Changed!",
                        "Your password has been successfully changed",
                        "success"
                      );

                      changePasswordObj.modal.hide();
                    }
                  });
                }
              );
            }
          );
        }, newPassword);
      } else {
        sb.dom.alerts.alert(
          "Password entries do not match!",
          "Please make sure your password matches in both fields ",
          "error"
        );
      }
    },

    editObject: function (editObj) {
      sb.data.db.obj.getById("staff", editObj.id, function (orgObj) {
        sb.data.db.obj.getWhere(
          "users",
          { related_object: editObj.id },
          function (users) {
            console.log(users);
            if (users.length > 0) {
              sb.obj.edit(domObj.modalContainers, "staff", orgObj, {
                pin: {},
                base: {},
                status: {},
                job_type: {},
                vacation_days: {},
              });
            } else {
              sb.obj.edit(domObj.modalContainers, "staff", orgObj);
            }
          }
        );
      });
    },

    resetStaffPassword: function (resetObj) {
      sb.dom.alerts.ask(
        {
          title:
            "Please Confirm That You Want To Reset This Staff Member's Password",
          text: "Yes to proceed or No to cancel",
        },
        function (resp) {
          if (resp) {
            swal.disableButtons();

            sb.data.db.createNewPassword(function (passwordObj) {
              sb.data.db.obj.getById(
                "staff",
                resetObj.staffObj.id,
                function (orgStaffObj) {
                  orgStaffObj.password = passwordObj.pwdHash;

                  sb.data.db.obj.update(
                    "staff",
                    orgStaffObj,
                    function (staffObj) {
                      var args = {
                        to: staffObj.email,
                        from: "Pagoda Dev",
                        subject: "Password Reset",
                        mergevars: {
                          TITLE:
                            "This is a confirmation that your password was reset",
                          BODY: "Your new password is: " + passwordObj.pwd,
                          BUTTON: "Log in now",
                          BUTTON_LINK:
                            "https://bento.infinityhospitality.net/app/" +
                            appConfig.instance +
                            "/#login",
                        },
                        emailtags: ["password reset email"],
                      };

                      sb.comm.sendEmail(args, function (response) {
                        if (response) {
                          sb.dom.alerts.alert(
                            "Password Reset!",
                            "A new password has been emailed to the staff member",
                            "success"
                          );
                        }
                      });
                    }
                  );
                }
              );
            });
          }
        }
      );
    },
  };
});
