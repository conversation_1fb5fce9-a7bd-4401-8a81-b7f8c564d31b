Factory.register('permissions', function (sb) {

	var State = {
		comps: {}
	};
	var userData = {};
	
	function viewForm (
		ui
		, data
		, onComplete
	) {
		
		var permission = {};
		var body = ui.makeNode('t', 'div', {
			css:'ui very basic celled table'
			, tag: 'table'
		}).makeNode('b', 'div', {
			tag: 'tbody'
		});
		
		// Set default items
		if (
			data.obj
			&& data.property
			&& data.obj.blueprint
			&& data.obj.blueprint[data.property]
			&& data.obj.blueprint[data.property].permissions
		) {
			
			permission = data.obj.blueprint[data.property].permissions;
			
		}
		_.each(
			data.types
			, function (type, key) {
				
				if (_.isEmpty(permission[key])) {
					permission[key] = {
						tags: []
					};
				}
				
			}
		);
		
		_.each(
			data.types
			, function (type, key) {

				body.makeNode('r-'+ key, 'div', {tag: 'tr'});
				
				body['r-'+ key].makeNode('name', 'div', {
					tag: 	'td'
					, text: 
						'<h5>'+
							'<div class="ui basic header">'+
								'<i class="'+ type.icon +' grey icon"></i>'+
								type.title +
							'</div>'+
						'</h5>'
				});
				body['r-'+ key].makeNode('value', 'div', {
					tag: 		'td'
					, text: 	'TAGS GOES HERE'
				});	
				
			}
		);
		
		// Save btn
		ui.makeNode(
			'save'
			, 'div'
			, {
				css: 	'ui fluid teal button'
				, tag: 	'button'
				, text: '<i class="save icon"></i> Save'
			}
		).notify('click', {
			type: 'permissions-run'
			, data: {
				run: function () {
					
					sb.data.db.obj.runSteps(
						{
							setPermission: {
								obj: 			data.obj.id
								, property: 	data.property
								, permissions: 	permission
							}
						}
						, data.obj.id
						, function(response){
							
							data.obj.blueprint = response.blueprint;
							data.obj.blueprint[data.property].permissions = permission;
							onComplete(data.obj);
							
						}
					);
					
				}
			}
		}, sb.moduleId);
		
		ui.patch();

		_.each(
			data.types
			, function (type, key) {
				
				if (State.comps[key]) {
					State.comps[key].destroy();
				}

				var selectedTags = permission[key].tags;
				var tags = [];
				_.each(selectedTags, function (v) {
					tags.push(parseInt(v));
				});
// 				console.log('selected::', selectedTags, key);
				State.comps[key] = sb.createComponent('tags');
				State.comps[key].notify({
					type: 'object-tag-view',
					data: {
						domObj: 		body['r-'+ key].value
						, objectType: 	''
						, tags: 		tags
						, canEdit: 		true
						, onChange: 	function (key, response) {
	
							permission[key].tags = _.pluck(response, 'id');
	
						}.bind({}, key)
					}
				});
				
			}
		);
		
	}
	
	return {
		
		init: function () {
			
			sb.listen({
				
				// Internal use
				'permissions-run': this.run
				
				// Open api
				, 'check-permissions': 		this.check
				, 'load-permissions': 		this.start
				, 'view-permissions-form': 	this.viewForm
				, 'get-allowed-teams': 		this.getAllowedTeams
				
			});
			
		}
		
		, run: function (data) {
			
			data.run(data);
			
		}
		
		// Open api
		
		, check: function (data) {
			
			var ret = {};
			_.each(data.permissionSet, function (setting, key) {
				
				// Where no permissions are set, anyone
				// can access.
				if (_.isEmpty(setting.tags)) {
					
					ret[key] = true;
					return;
					
				}
				
				setting.tags = _.map(setting.tags, function (tag) {
					return parseInt(tag);
				});
				
				// User check
				if (_.contains(setting.tags, +sb.data.cookie.userId)) {
					
					ret[key] = true;
					return;
					
				}
				
				// Team check
				ret[key] = !_.chain(setting.tags)
								.intersection(
									_.pluck(userData.teams, 'id')
								)
								 .isEmpty()
								  .value();
								  
				return;
				
			});
			
			data.onComplete(ret);
			
		}

		, getAllowedTeams: function (data) {

			if (typeof data.callback === 'function') {

				data.callback(
					_.pluck(userData.teams, 'id')
				);

			} else {

				console.error('This evt requires a callback function at data.callback => (allowedTeams)');

			}

		}
		
		, start: function (data) {
			
			sb.data.db.obj.getWhere(
				'groups'
				, {
					group_type: 		'Team'
					, tagged_with: 	+sb.data.cookie.userId
					, childObjs: {
						id: 		true
						, name: 	true
					}
				}
				, function (teams) {
					
					userData.teams = teams;
					
					if (typeof data.onLoad === 'function') {
						
						data.onLoad(userData);
						
					}
					
				}
			);
			
		}
		
		, viewForm: function (data) {
			
			sb.notify({
				type: 	'get-sys-modal'
				, data: 	{
					callback: 	function (modal) {
						
						modal.body.empty();
						modal.show();
						viewForm(
							modal.body
							, data
							, function (response) {
								
								modal.body.empty();
								modal.hide();
								
								data.onComplete(response);
								
							}
						);
						
					}
					, onClose: function () {
						
						if (typeof data.onClose === 'function') {
							data.onClose();
						}

					}
				}
			});
			
		}
		
	};
	
});
