Factory.register ('tools-layer', function (sb) {

	var Tools = [];
		
	// tool mgmt
	
	function addToolMenu (ui, obj, options, getConfig) {
		
		function addToolToBlueprint (blueprint, selection, toolOptions, onComplete) {

			if (_.isEmpty(blueprint.tools)) {
				blueprint.tools = [];
			}
			
			// Validate the tool selection
			var toolDef = _.findWhere(Tools, {id: selection});
			if (!toolDef) {
				return;
			}
				
			var opts = {};
			sb.notify({
				type: 	'get-options-form'
				, data: {
					optionsDef: 	toolDef.options
					, options: 		opts
					, onComplete: 	function (opts) {
						
						sb.data.db.obj.create(
							'entity_tool'
							, {
								allowed_users:		[+sb.data.cookie.get('uid')]
								, name: 			toolDef.name
								, type: 			toolDef.id
								, entityType: 		blueprint.id
								, system_name:		toolDef.id
								, display_name:		toolDef.name
								, is_archieved:		0
								, is_archived:		0
// 								, order:			mainGroup.tools.length + 1
								, added_by:			+sb.data.cookie.get('uid')
								, added_on:			moment().format('YYYY-MM-DD HH:mm:ss.SS')
								, settings:			opts
								, box_color:		toolDef.icon.color
							}
							, function (response) {
								
								if (
									!_.isEmpty(options)
									&& typeof options.onAdd === 'function'
								) {
									options.onAdd(response);
								} else if (onComplete) {
									onComplete(response);
								}
								
								return;
								
							}
						);
						
						return;
						
					}
				}
			});
			
			return;
			
		}
		
		if (typeof getConfig === 'function') {
			
			getConfig({
				add: 		addToolToBlueprint.bind({}, obj)
				, options: 	Tools
			}) ;
			
		} else {
			
			ui.makeNode(
				'addTool'
				, 'div'
				, {
					css: 'ui grey dropdown item'
					, text: '<i class="grey plus icon"></i> Add a tool'
					, listener: {
						action: 'select'
						, onChange:function(value, text){
							
							if(value){
								
								addToolToBlueprint (
									obj
									, value
									, {}
									, options.onAdd
								) ;
								
							}
							
						},
						type:'dropdown',
						placeholder:'All types',
						allowCategorySelection: true
						, delay : {
						  hide   : 300,
						  show   : 200,
						  search : 50,
						  touch  : 50
						}
						, duration: 100
		// 				, transition: 'swing down'
					}
				}
			).makeNode('menu', 'div', {css:'menu'});
			
			_.each(Tools, function (tool) {
				
				ui.addTool.menu.makeNode(
					't-'+ tool.id
					, 'div'
					, {
						css: 'item'
						, text: '<i class="'+ tool.icon.color +' '+ tool.icon.type +' icon"></i> '+ tool.name
						, dataAttr: [
							{
								name: 'value'
								, value: tool.id
							}
						]
					}
				);
				
			});
		
		}
		
	}
	
	function viewToolItem (ui, tool, obj, options) {
		
		var toolDef = _.findWhere(
			Tools
			, {
				id: tool.type
			}
		);
		
		ui.makeNode(
			't-'+ tool.id
			, 'div'
			, {
				css: 'ui grey dropdown item'
				, text: '<i class="'+ toolDef.icon.color +' '+ toolDef.icon.type +' icon"></i>'+ tool.name
			}
		);
		
		ui['t-'+ tool.id].notify('click', {
			type: 'tools-run'
			, data: {
				run: function () {
					
					if ($(ui['t-'+ tool.id].selector).hasClass('active')) {
						
						$(ui['t-'+ tool.id].selector).html(
							'<i class="'+ toolDef.icon.color +' '+ toolDef.icon.type +' icon"></i>'+ 
							'<div class="ui transparent input">'+
								'<input type="text" value="'+ tool.name +'" placeholder="'+ toolDef.name +'" />'+
							'</div>'
						);
						$(ui['t-'+ tool.id].selector +' input').focus();
						$(ui['t-'+ tool.id].selector +' .ui.input input').on('blur', function () {
							
							tool.name = $(ui['t-'+ tool.id].selector +' .ui.input input').val();
							$(ui['t-'+ tool.id].selector).html(
								'<i class="'+ toolDef.icon.color +' '+ toolDef.icon.type +' icon"></i>'+ tool.name
							);
							
							// update tool name
							sb.data.db.obj.update('entity_tool', {
								id: tool.id
								, name: tool.name
							}, function () {});
							
						});
						
					} else {
						
						$(ui.selector +' .active').removeClass('active');
						$(ui['t-'+ tool.id].selector).addClass('active');
						
						if (typeof options.onSelect === 'function') {
							
							options.onSelect (
								function (btnUi, ui) {
									
									viewTool(
										ui
										, options.blueprint
										, this
										, {
											entity: options.entity
											, parent: obj
											, tool: tool
											, onRemove: options.onRemove
											, onUpdate: function (updatedTool) {
												
												tool = updatedTool;
												$(btnUi.selector).html(
													'<i class="'+ toolDef.icon.color +' '+ toolDef.icon.type +' icon"></i>'+ tool.name
												);
												
											}
										} 
									) ;
									
								}.bind(this, ui['t-'+ tool.id])
							) ;
							
						}
						
					}
					
					return ;
					
				}.bind(toolDef)
			}
		}, sb.moduleId) ;
		
	}
	
	// view tools
	function viewTool (ui, blueprint, toolDef, options) {

		var tool = options.tool;
		
		if (false) {
			
			// tool header
			ui.makeNode(
				'h'
				, 'div'
				, {
					css: 'ui right floated secondary tiny menu'
				}
			);
			
			// remove tool btn
			ui.h.makeNode(
				'rm'
				, 'div'
				, {
					css: 'icon item'
					, text: '<i class="grey trash icon"></i>'
					, tag: 'a'
				}
			).notify('click', {
				type: 'tools-run'
				, data: {
					run: function () {
						
						sb.dom.alerts.ask(
							{
								title: 'Remove '+ tool.name +'?'
								, text: ''
							}
							, function (r) {
								
								if (r) {
									
									swal.disableButtons();
									sb.data.db.obj.erase(
										tool.object_bp_type
										, tool.id
										, function (r) {
											
											swal.close();
											if (typeof options.onRemove === 'function') {
												options.onRemove(tool);
											}
											
										}
									);
									
								}
								
							}
						);
						
					}
				}
			}, sb.moduleId);
			
			// link to full view
			if (!options.fullView) {
				
				ui.h.makeNode(
					'link'
					, 'div'
					, {
						css: 'icon item'
						, text: '<i class="grey external square alternate icon"></i>'
						, tag: 'a'
						, href: sb.data.url.createPageURL('object', {
							id: 				tool.id,
							name: 				tool.name,
							object_bp_type: 	tool.object_bp_type,
							params: 			{
								p: options.parent.id
							},
							type: 			tool.object_bp_type
						})
					}
				) ;
				
			}
	
			if (options.fullView) {
				
				ui.h.makeNode('lb_1', 'lineBreak', {
					spaces: 2
				});
				
				var fieldOptions = {
					editing: true
		// 			, blueprint: blueprint
					, edit: true
					, onUpdate: function (response) {
						
						if (typeof options.onUpdate === 'function') {
							options.onUpdate(response);
						}
						
					}
				} ;
				
				fieldOptions.fontSize = '3rem';
				sb.notify ({
					type: 'view-field'
					, data: {
						type: 			'title'
						, property: 	'name'
						, obj: 			tool
						, options: 		fieldOptions
						, ui: 			ui.makeNode('t', 'div', {})
					}
				});			
				
			}
			
		}
		
		ui.makeNode('b', 'div', {});
		ui.patch();
		
		// return;
		toolDef.mainViews[0].dom(
			ui.b
			, _.clone(options)
			, function () {}
			, ui.b
		) ;
		
	}
	
	function viewTools (blueprint, entity, edit, drawMenu) {

		function layoutTools (ui, tools) {

			// menu
			ui.makeNode('menu', 'div', {css:'ui small borderless menu toolMenu'});
			ui.makeNode('toolArea', 'div', {style:'min-height:600px;'});

			_.each(tools, function (tool) {

				sb.notify({
					type: 	'view-tool-item'
					, data: 	{
						ui: 		ui.menu
						, tool: 	tool
						, obj: 	blueprint
						, options: {
							entity: entity
							, toolUi: ui.toolArea
							, onRemove: function (removed) {

								tools = _.filter(tools, function (t) {
									return t.id !== removed.id;
								});
								layoutTools(ui, tools);
								ui.patch();

							}
							, onSelect: function (draw) {

								draw(ui.toolArea);
								return ;

							}
						}
					}
				});

			});

			// add tool btn
			if (edit) {

				sb.notify({
					type: 	'view-add-tool-menu'
					, data: 	{
						ui: ui.menu
						, obj: blueprint
						, options: {
							onAdd: function (response) {

								tools.push(response);
								layoutTools(ui, tools);
								ui.patch();

							}
						}
					}
				});

			}

		}

		//!TODO: move this to be shared
// 		ui.makeNode('modal', 'modal', {});

		sb.data.db.obj.getWhere(
			'entity_tool'
			, {
				entityType: blueprint.id
			}
			, function (tools) {

				if (typeof drawMenu === 'function') {

					sb.notify({
						type: 		'get-tool-defs'
						, data: 	{
							callback: function (toolDefs) {

								function getMenuLinks() {

									return _.map(tools, function (tool) {

										var icon = '';
										var toolDef = _.findWhere(appConfig.Types, {bp_name: tool.type});
										if (toolDef && toolDef.icon) {
											icon = 'grey '+ toolDef.icon;
										}
										var toolName = tool.name;
										if (!_.isEmpty(tool.settings)) {
											
											if (tool.settings.name) {
												toolName = tool.settings.name;
											}
											
										}
										if (_.isEmpty(icon)) {
											icon = 'grey list';
										}
										
										return {
											id: 		tool.id
											, title:	toolName
											, icon: 	icon
											, link: 	sb.data.url.createPageURL('object', {
												id: 				tool.id,
												name: 				toolName,
												object_bp_type: 	tool.object_bp_type,
												params: 			{
													p: 		blueprint.id
													, e: 	entity.id
												},
												type: 	tool.object_bp_type
											})
											, remove: function (tool, redraw) {

												sb.dom.alerts.ask(
													{
														title: 		'Remove '+ tool.name +'?'
														, text: 	''
													}
													, function (r) {

														if (r) {

															swal.disableButtons();
															sb.data.db.obj.erase(
																'entity_tool'
																, tool.id
																, function () {

																	swal.close();
																	tools = _.filter(
																		tools
																		, function (link) {
																			return link.id !== tool.id;
																		}
																	);

																	getPageMenuConfig (function (config) {
																		drawMenu(config);
																	});

																}
															);

														} else {

															swal.close();

														}

													}
												);


											}.bind({}, tool)
										};

									});

								}

								function getPageMenuConfig (onComplete) {

									var links = getMenuLinks();

									sb.notify({
										type: 	'view-add-tool-menu'
										, data: 	{
// 											ui: ui
											obj: blueprint
											, options: {
												onAdd: function (tool) {

													var icon = '';
													var toolDef = _.findWhere(toolDefs, {id: tool.type});
													if (toolDef) {
														icon = toolDef.icon.color +' '+ toolDef.icon.type;
													}

													tools.push(tool);

													getPageMenuConfig (function (config) {
														drawMenu(config);
													}) ;

												}
											}
											, getConfig: function (addPage) {

												onComplete({
													add: 		addPage
													, links: 	links
												});

											}
										}
									});

								}

								getPageMenuConfig(function (config) {

									drawMenu(config);

								});


							}
						}
					});

				}

			}
		);

	}
	
	function toolFactory(tools) {

		var compiledTools = [];
		var defaultLayers = ['hq', 'project', 'team', 'myStuff', 'entity', 'object']; 

		function translate_layerType(layer) {
			
			switch(layer) {
				
				case 'project':
				case 'entity':
					return 'tool';
					break;
					
				case 'team':
					return 'teamTool';
					break;
					
				case 'hq':
					return 'hqTool';
					break;
					
				case 'myStuff':
					return 'myStuff';
					break;
					
				case 'object':
					return 'objectTool';
					break;
				
				default:
					return;
				
			}
			
		}
		
		function generateToolsFromLayer(layers, tool) {
			
			_.each(layers, function(layer) {
					
				var newTool = _.clone(tool);
				var toolBoxViews = [];
				var wrappedViews = [];
				var boxViews = [];
				
				newTool.type = translate_layerType(layer);

				_.each(tool.mainViews, function(view) {

					wrappedViews.push({
						dom: function(v, l, ui, state, draw) {

							state.layer = l;

							switch(l) {
								
								case 'project':
								case 'team':
								case 'entity':

									state.where = {
										tagged_with: [state.pageObject.id]
									};
									
									if(state.hasOwnProperty('team')
										&& state.hasOwnProperty('project')) {
										
										state.where.parent = state.pageObject.id;
										
									}
									
									break;
									
								case 'object': 

									var object = {};
									
									if(state.hasOwnProperty('object')) {
										object = state.object;
									} else {
										object = state.pageObject;
									}

									state.where = {
										tagged_with: [object.id]
										, parent: object.id
									};
									break;
									
								case 'myStuff':
									state.where = {
										tagged_with: [+sb.data.cookie.userId]	
									};
									break;
									
								default:
								
							}
							
							view.dom(ui, state, draw);
							
						}.bind({}, view, layer)
					});
					
				});

				_.each(tool.boxViews, function(boxView) {

					var newBoxView = _.clone(boxView);

					if(newBoxView.hasOwnProperty('dom')) {
						
						newBoxView.dom = function(v, l, ui, state, draw) {

							state.layer = l;
							
							if(l === 'team') {

								if (state.hasOwnProperty('team')) {
									if (state.team.hasOwnProperty('id')) {
										state.where = {};
										state.where.tagged_with = [state.team.id];	
									}
								}

							}
							
							v.dom(ui, state, draw);
							
						}.bind({}, boxView, layer)
						
					} else if(newBoxView.hasOwnProperty('collections')) {

						function configureCollections(ignoredLayers) {
							
							if(newBoxView.collections.hasOwnProperty('where')) {
							
								var where = _.clone(newBoxView.collections.where);
								
								setTimeout(function(layer) {
	
									newBoxView.collections.where = function(w, l, state) {
	
										switch(l) {
											
											case 'team':
											case 'project':
											case 'object':
											case 'entity':
											
												if(ignoredLayers === undefined) {

													if (state.hasOwnProperty('pageObject')) {
														if (state.pageObject.hasOwnProperty('id')) {
															if (state.pageObject.id) {
																w.tagged_with = [state.pageObject.id];
															}
														}
													} else if (state.hasOwnProperty('user')) {
														if (state.user) {
															w.tagged_with = [state.user];
														}
													}													
													
												} else {

													if(_.contains(ignoredLayers, l)) {
														
														delete w.tagged_with;
														
													}
													
												}

												break;
												
											case 'myStuff':

												if(ignoredLayers === undefined) {

													w.tagged_with = [+sb.data.cookie.userId];
													
												} else {

													if(_.contains(ignoredLayers, l)) {

														delete w.tagged_with;

													}
													
												}
											
												break;
											
										}

										return w;
										
									}.bind({}, where, layer);
									
								}.bind({}, layer), 0.1);
								
							}
							
						}

						if(newBoxView.ignoreLayers === undefined) {
								
							configureCollections(undefined);
								
						} else {
							
							configureCollections(newBoxView.ignoreLayers);
							
						}
						
					}
					
					boxViews.push(newBoxView);
					
				});
				
				newTool.mainViews = wrappedViews;
				
				newTool.boxViews = boxViews;
				
				compiledTools.push(newTool);
				
			});
			
		}

		_.each(tools, function(tool) {
			
			if(tool.hasOwnProperty('layers')) {
				
				if(!_.isEmpty(tool.layers)) {
				
					generateToolsFromLayer(tool.layers, tool);
					
				} else {
					
					generateToolsFromLayer(defaultLayers, tool);
					
				}	
				
			}
			
		});

		return compiledTools;
		
	}
	
	return {
		
		initListeners: function () {
			
			sb.listen({
				'register-tool': 			this.registerTool
				, 'register-application': 	this.register
				, 'get-tool-defs': 			this.getToolDefs
				, 'tools-run': 				this.run
				, 'view-add-tool-menu': 	this.addToolMenu
				, 'view-tool-item': 		this.viewToolItem
				, 'get-object-tools': 		this.getObjectTools
			});
			
		}
		
		, init: function () {

			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: 'tools',
						title: 'Tools',
						icon: '',
						views: [
							{
								id: 'entity_tool-obj',
								type: 'object-view',
								title: 'Tool',
								icon: '',
								dom: function(ui, state, onDraw) {

									try {
										
										sb.data.db.obj.getById(
											''
											, state.params.p
											, function (parent) {
												
												viewTool(
													ui
													, undefined
													, _.findWhere(Tools, {id: state.pageObject.type})
													, {
														entity: 		state.params.e
														, onUpdate: 	function () {}
														, onRemove: 	function () {
															
															sb.notify({
																type: 'app-navigate-to'
																, data: {
																	type: 'UP'
																}
															}) ;
															
														}
														, pageObject: 	state.pageObject
														, parent: 		parent
														, drawPageMenu: state.drawPageMenu
														, tool: 		state.pageObject
														, team: 		state.team
														, fullView: 	true
													}
												) ;
												
											}
										) ;
										
										
										
									} catch (e) {

										console.log(e);

									}

								}
							}
						]
					}
				}
			});
			
		}
		
		, registerTool: function (data) {

			var incomingViews = data.navigationItem.views;

			var views = _.filter(incomingViews, function(t) {
				return t.layers === undefined;
			});
				
			var tools = _.filter(incomingViews, function(t) {
				return t.layers !== undefined;
			});

			var generatedTools = toolFactory(tools);

			views = _.union(views, generatedTools);

			sb.notify({
				type: 'register-application',
				data: {
					navigationItem: {
						moduleId: sb.moduleId,
						instanceId: sb.instanceId,
						id: data.navigationItem.id,
						title: data.navigationItem.title,
						icon: '',
						views: views
					}
				}
			});
			
		}
		
		, register: function(data) {
			
			_.each(data.navigationItem.views, function(view){

				if(view){
					
					if(
						view.type === 'tool'
						&& view.availableToEntities
					){
						Tools.push(view);	
					}
					
				}
				
			});	
			
		}
		
		, getObjectTools: function(data) {

			viewTools (
				data.type
				, data.pageObject
				, true
				, data.draw
			);	
			
		}
		
		, getToolDefs: function (data) {
			
			data.callback(Tools);
			
		}
		
		, run: function (data) {
			
			data.run(data);
			
		}
		
		, addToolMenu: function (data) {
			
			addToolMenu (
				data.ui
				, data.obj
				, data.options
				, data.getConfig
			) ;
			
		}
		
		, viewToolItem: function (data) {
			
			viewToolItem (
				data.ui
				, data.tool
				, data.obj
				, data.options
			) ;
			
		}
		
	}
	
}) ;