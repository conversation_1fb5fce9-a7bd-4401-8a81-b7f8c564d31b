Factory.register('walkthrough-system', function (sb) {

	var nodeSteps = [];

	function getUsersWalkthrough (setup, cb) {

		function createInitialWalkthrough (setup, callback) {
	
			var walkthroughState = {
				name: 		setup.walkthrough
				, user: 		setup.user || {}
				, module_id: 	setup.moduleId || ''
				, steps:  	[]
			};
			
			sb.data.db.obj.create('walkthrough'
				, walkthroughState
				, function(res){

					callback(res);
				
				}
			);
	
		}

		sb.data.db.obj.getWhere('walkthrough'
			, {
				user: 	setup.user.id
				, name:	setup.walkthrough
			}
			, function(resp){

				var usersWalkthrough = resp[0];

				if(_.isEmpty(resp)){

					createInitialWalkthrough(setup, function(walkthrough){

						setup.user_walkthrough = walkthrough;

						cb(setup);
				
						// if(ga){
						// 	// !Google Analytics New User's Walkthrough Created	
						// 	ga('send', {
						// 		hitType: 'event'
						// 		, eventCategory: 	'Walkthrough'
						// 		, eventAction: 	'Users Walkthrough Obj Created'
						// 		, eventLabel:		setup.walkthrough  + ' walkthroughObj Created'
						// 	});
					
						// }				
						
					});
					
				}else{

					setup.user_walkthrough = usersWalkthrough;

					cb(setup);
				
				}
								
			}
		);
		
	}
	
	function skipAllSteps (setupData, reset) {

		var updUserWalk = _.clone(setupData.user_walkthrough);
		
		if(reset){
			
			updUserWalk.steps = [];
			
		} else {
			
			updUserWalk.steps = _.map(setupData.steps, function(st){
	
				return { name: st.name };
	
			});
						
		}

		sb.data.db.obj.update('walkthrough', updUserWalk, function(res){

			if(res)
				return true;
		});							
			
	}

	function startWalkthrough (data, nodeSteps) {

		var driverOpt = {
			doneBtnText: 	 	'All set!' 
			, animate:		true            
			, closeBtnText:	'Skip'
			, allowClose: 		false
			, opacity: 		.45
		};
		
		var driver = new Driver(driverOpt);

		var steps = [];

		_.each(data.steps, function (step, i) {

			if (!_.findWhere(data.user_walkthrough.steps, {name:step.name})) {

				var nodeInfo = _.where(nodeSteps
					, {
						name: 		step.name
						, walkthrough: data.walkthrough
					}
				);

				if (
					nodeInfo != undefined 
					&& nodeInfo.length > 0
					&& !_.isEmpty($(nodeInfo[0].selector))
				) {

					var currentStep = _.clone(step);
	
					currentStep.element = nodeInfo[0].selector;
					
					var onHighlighted = function(step, data){
											
						stepHasBeenViewed(step, data);

						driver.window.onclick = function(step, data, event){

							if(event.target.classList.contains('driver-close-btn') && event.target.classList.length == 1){

								skipAllSteps(data);

								// if(ga){
								// 	// !Google Analytics Skip Button clicked	
								// 	ga('send', {
								// 		hitType: 'event'
								// 		, eventCategory: 	'Walkthrough'
								// 		, eventAction: 	'Steps Skipped'
								// 		, eventLabel:		'wlk_' + data.walkthrough+ 'skip clicked while on stp_' + step.name
								// 	});
							
								// }

							} else if (event.target.classList.contains('driver-close-only-btn') && event.target.classList.length == 2){

								// if(ga){
								// 	// !Google Analytics Complete Btn (One Step) clicked
								// 	ga('send', {
								// 		hitType: 'event'
								// 		, eventCategory: 	'Walkthrough'
								// 		, eventAction: 	'Complete Button Clicked (One Step)'
								// 		, eventLabel:		'wlk_' + data.walkthrough + ' stp_' + step.name + ' completed'
								// 	});
								// }
							
							}
							
						}.bind(null, step, data);
										
					}.bind(null, currentStep, data);
					
					var nextFn = function(step, data){
						
						if(driver.hasNextStep()){

							// if(ga){
							// 	// !Google Analytics Next Btn clicked
							// 	ga('send', {
							// 		hitType: 'event'
							// 		, eventCategory: 	'Walkthrough'
							// 		, eventAction: 	'Next Button Clicked'
							// 		, eventLabel:		'wlk_' + data.walkthrough + ' next clicked while on stp_' + step.name 
							// 	});
							// }
														
						} else {

							// if(ga){
							// 	// !Google Analytics Complete Btn (All Steps) clicked
							// 	ga('send', {
							// 		hitType: 'event'
							// 		, eventCategory: 	'Walkthrough'
							// 		, eventAction: 	'Complete Button Clicked'
							// 		, eventLabel:		'wlk_' + data.walkthrough + ' stp_' + step.name + ' completed'
							// 	});
							// }							
							
						}
						
					}.bind(null, currentStep, data);
										
					var prevFn = function(step, data){

						// if(ga){
						// 	// !Google Analytics Previous Btn clicked
						// 	ga('send', {
						// 		hitType: 'event'
						// 		, eventCategory: 	'Walkthrough'
						// 		, eventAction: 	'Previous Button Clicked'
						// 		, eventLabel:		'wlk_' + data.walkthrough + 'prev clicked while on stp_' + step.name 
						// 	});
						// }
						
					}.bind(null, currentStep, data);

					currentStep.onHighlighted = onHighlighted;
					currentStep.onNext = nextFn;
					currentStep.onPrevious = prevFn;
				
					steps.push(currentStep);
					
				}
				
			}
											
		});

		if(!_.isEmpty(steps)){		
			// run driver
			driver.defineSteps(steps);
			
			driver.start();

		}
		
	}
	
	function stepHasBeenViewed (currentStep, setup) {

		var updatedWalk = _.clone(setup.user_walkthrough);
		
		if(!_.find(setup.user_walkthrough.steps, {name:currentStep.name})){
		
			updatedWalk.steps.push( {name:currentStep.name} );

			sb.data.db.obj.update('walkthrough'
				, updatedWalk
				, function(response){

					if(response)
						return true;
				}
			);
			
			// if(ga){
			// 	// !Google Analytics Step Viewed	
			// 	ga('send', {
			// 		hitType: 'event'
			// 		, eventCategory: 	'Walkthrough'
			// 		, eventAction: 	'Step Viewed'
			// 		, eventLabel:		'wlk_' + setup.walkthrough + ' stp_' + currentStep.name + ' viewed'
			// 	});
		
			// }
				
		}
		
	}
	
	function verifyRegisteredNodes (walkData, nodeSteps) {

		var nodeNames = _.chain(nodeSteps)
						.filter(function(n){ return $(n.selector).length > 0})
						.pluck('name')
						.uniq()
						.value();

		walkData.steps = _.filter(walkData.steps, function(step){
			
			return _.contains(nodeNames, step.name);
			
		});
		
		return walkData;		

	}
		
	return {
		
		init: function () {
			
/*
			// !GoogleAnalytics ***DEBUGGER
			if (location.hostname == 'localhost') {
			  ga('set', 'sendHitTask', null);
			}
*/

			sb.listen({
				'start-walkthrough': 			this.start
				, 'register-walkthrough-step': 	this.registerNodeSteps
			});

		}
		
		, registerNodeSteps: function (data) { nodeSteps.push(data); }
				
		, start: function (data) {

			if(!_.isEmpty(data.steps) && data.compInstanceId === undefined){

				var walkthroughData = verifyRegisteredNodes(data, _.uniq(nodeSteps));
				
				setTimeout(function () {
																				
					getUsersWalkthrough(walkthroughData, function(setup){

						startWalkthrough(setup, nodeSteps);						
						
					});
					
				}, 1000);
					
			}    
			
		}
		
	}
	
});