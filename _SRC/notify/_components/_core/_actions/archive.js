Factory.register ('archive-action', function (sb) {
	
	function Run (obj, state, callback) {
		
		var method = 	'erase';
		var newVal = 	1;
		var msg = 		'Archive '+ obj.name +' ?';
		
		function checkForChildObjs(obj) {
			
			var childObjsIds = [];
			
			if(!_.isEmpty(obj)){

				if ( obj.hasOwnProperty('object_bp_type') ) {
	
					if(obj.object_bp_type === 'groups') {
						
						if(obj.group_type === 'Project') {
							
							sb.data.db.obj.getWhere('groups', {
								group_type: 'Schedule',
								parent: obj.proposal.id,
								childObjs: {
									id: true
								}
							}, function(schedule) {
		
								if(!_.isEmpty(schedule)) {
									
									childObjsIds.push(schedule[0].id);
									
									sb.data.db.obj.getWhere('groups', {
										group_type: 'Shift',
										parent: schedule[0].id,
										childObjs: {
											id: true
										}
									}, function(shifts) {
									
										_.each(shifts, function(o) {
											
											childObjsIds.push(o.id);
											
										});
		
										sb.data.db.obj.erase('groups', childObjsIds, function(resp) {});
									
									});
									
								}
							
							});
							
						}
						
					}
	
				}

			}
			
		}
		
		if (obj.is_deleted) {
			method = 	'restore';
			newVal = 	0;
			msg = 		'Restore '+ obj.name +' ?';
		}

		sb.dom.alerts.ask(
			{
				title: 	msg
				, text: 	''
			}
			, function (r) {
				
				if (r) {
					
					swal.disableButtons();
					
					sb.data.db.obj[method](
						obj.object_bp_type
						, obj.id
						, function (response) {
							
							swal.close();
							obj.is_deleted = newVal;
							
							// Navigate user back to collection
							sb.notify({
							  type: 'app-navigate-to'
							  , data: {
							    type:'UP'
							  }
							});
							
							// callback(obj);
							
							checkForChildObjs(obj);
							
						}
					);
					
				} else {
					
					callback(obj);
					
				}
				
			}
		);
		
	}
	
	return {
		
		init: 	function () {
			
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'archive'
					, title:	 	'Archive'
					, icon: 		'archive'
					, color: 	'grey'
					, action: 	Run
					, view: 		function (obj) {

						if (obj.is_deleted) {
							
							return {
								title: 	'Restore'
								, icon: 	'redo'
								, color: 'teal'
							};
							
						} else {
							
							return {
								title: 	'Archive'
								, icon: 	'archive'
								, color: 'grey'
							};
							
						}						
						
					}
				}
			});
			
		}
		
	} ;
	
}) ;
