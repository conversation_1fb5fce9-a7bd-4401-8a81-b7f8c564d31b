Factory.register ('FGcreateFormationProject-action', function (sb) {
	
	return {
		
		init: 	function () {
			
			// Only turn this on in our dev instances or in the 
			// 'foundation_group' instance where it is ultimately going to be 
			// used.

			if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'foundation_group'
			) {

				sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGcreateFormationProject'
						, title:	 	'Create Formation'
						, icon: 		'plus'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{
							type: {
								name: 		'Type' 
								, type: 	'select'
								, multi:	false
								, options: [
									{
										name: 		'1023'
										, value: 	'1023'
									}
									, {
										name: 		'1023 EZ'
										, value: 	'1023EZ'
									}
									, {
										name: 		'1024'
										, value: 	'1024'
									}
									, {
										name: 		'1024 A'
										, value: 	'1024A'
									}
								]
							}
						}
					}
				});

			}

			
		}
		
	} ;
	
}) ;
