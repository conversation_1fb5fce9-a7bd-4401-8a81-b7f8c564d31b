Factory.register ('toggleIsPublic-action', function (sb) {
	
	function Run (obj, state, callback) {
		
		function updateNotifyList(object, callback){
			
			// Toggle whether or not the object is public
			object.is_public = !object.is_public;
			sb.data.db.obj.update(
				object.object_bp_type
				, {
					id: 			object.id
					, is_public: 	object.is_public
					, active: 		'Yes'
				}, function(resp){
					
					obj.is_public = resp.is_public;
					
					if (callback && typeof callback == 'function') {
						callback(obj);
					}
										
				}
			);
			
		}
		
		updateNotifyList(obj, callback);
		
	}
	
	return {
		
		init: 	function () {
			
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'toggleIsPublic'
					, hiddenFromEvents: true
					, title:	'Is Public?'
					, icon: 	'linkify'
					, color: 	'grey'
					, action: 	Run
					, view: 	function (obj) {
						
						if (obj.is_public) {
							
							return {
								title: 	'Public'
								, icon: 'unlock'
								, color:'yellow'
							};
							
						} else {
							
							return {
								title: 	'Private'
								, icon: 'lock'
								, color:'grey'
							};
							
						}						
						
					}
				}
			});
			
		}
		
	} ;
	
}) ;
