Factory.register ('FGcreate990Project-action', function (sb) {

	return {

		init: 	function () {



			if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'foundation_group'
			) {

				sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGcreate990Project'
						, title:	 	'Create 990 Project'
						, icon: 		'plus'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{
							type: {
								name: 		'Type'
								, type: 	'select'
								, multi:	false
								, options: [
									{
										name: 		'990'
										, value: 	'990L'
									}
									, {
										name: 		'990 EZ'
										, value: 	'990EZ'
									}
									, {
										name: 		'990 N'
										, value: 	'990N'
									}
									, {
										name: 		'990 PF'
										, value: 	'990PF'
									}
								]
							}
						}
					}
				});

			}


		}

	} ;

}) ;
