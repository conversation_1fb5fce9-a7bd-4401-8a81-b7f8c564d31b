Factory.register ('FGcreateAbatementProject-action', function (sb) {
	
	return {
		
		init: 	function () {
			
			// Only turn this on in our dev instances or in the 
			// 'foundation_group' instance where it is ultimately going to be 
			// used.

			if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'foundation_group'
			) {

				sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGcreateAbatementProject'
						, title:	 	'Create Abatement Project'
						, icon: 		'plus'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{
							type: {
								name: 		'Type' 
								, type: 		'select'
								, multi:		false
								, options: [
									{
										name: 		'Abatement'
										, value: 		'abatement'
									}
								]
							}
						}
					}
				});

			}

			
		}
		
	} ;
	
}) ;
