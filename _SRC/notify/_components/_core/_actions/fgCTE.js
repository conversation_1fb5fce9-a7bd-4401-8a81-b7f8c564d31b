Factory.register ('FGcreateCTEProject-action', function (sb) {
	
	return {
		
		init: 	function () {
			
			// Only turn this on in our dev instances or in the 
			// 'foundation_group' instance where it is ultimately going to be 
			// used.

			if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'foundation_group'
			) {

				sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGcreateCTEProject'
						, title:	 	'Create CTE (Corporate Tax Exempt) Project'
						, icon: 		'plus'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{}
					}
				});

                sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGcreateCTEActionItems'
						, title:	 	'Create CTE Action Items'
						, icon: 		'plus'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{}
					}
				});

			}

			
		}
		
	} ;
	
}) ;
