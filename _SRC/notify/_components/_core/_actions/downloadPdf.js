Factory.register ('download-pdf-action', function (sb) {
	
	function Run (obj, state, callback) {
		
		var details = '';
		if(obj.created_by){
			details = '<small><p>Created By: '+ obj.created_by.fname +' '+ obj.created_by.lname +' on '+ moment(obj.date_created).local().format('M/D/YYYY') +'</p></small>';
		}else{
			details = 'Created on '+ moment(obj.date_created).local().format('M/D/YYYY');
		}
		
		sb.data.makePDF('<h1>'+obj.name+'</h1>'+details+obj.body, 'D');
		
		callback(obj);
		
	}
	
	return {
		
		init: function () {
			
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'downloadPdf'
					, hiddenFromEvents: true
					, title:	'Download PDF'
					, icon: 	'file pdf'
					, color: 	'grey'
					, action: 	Run
				}
			});
			
		}
		
	} ;
	
}) ;