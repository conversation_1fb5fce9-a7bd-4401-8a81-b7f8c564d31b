Factory.register('addTools-action', function (sb) {
	
	function Run () {} 
	
	return {
		
		init: function () {

			sb.notify ({
				type: 	'register-action'
				, data: {
					name: 			'addTools'
					, title:	 		'Add Tool'
					, icon: 			'tools'
					, color: 		'green'
					, action: 		Run
					, options: {
						selectTools: {
							name: 		'Select tool' 
							, type: 	'select'
							, multi:	true
							, options: function(obj, state, config) {
 
								var projectTools = appConfig.projectTools;
								var options = [];
								
								_.chain(projectTools)
									.filter(function(o) {
										
										if(
											!o.hasOwnProperty('hide')
											|| o.hide !== true
										) {
											return true;
										}
										
									}).each(function(o) {
									
									options.push({
										name: o.name
										, value: o.id
									});
									
								});
								
								return options;
								
							}
						}
					}
					, parseOptions: function(formData) {
						
						var projectTools = appConfig.projectTools;
						var toolObj = _.findWhere(projectTools, {id: formData.selectTools.value});
						
						return {
							selectTools: toolObj
						};
							
					}
				}
			});
			
		}
		
	};
	
});