Factory.register('createDocument-action', function (sb) {
	
	function Run (obj, state, modal) {}
	
	return {
		
		init: function () {

			function getTemplateQuery(state) {

				var where = {
					is_template: 1
				};
				
				switch (state.object_bp_type) {
		
					case 'entity_workflow':
						where.merge_type = 'space';
						break;
					case 'project_types':
						where.merge_type = 'proposal';
						break;

				}
		
				return where;
			
			}
						
			sb.notify ({
				type: 	'register-action'
				, data: {
					name: 			'createDocument'
					, title:	 	'Create document'
					, icon: 		'file'
					, color: 		'yellow'
					, action: 		Run
					, modal: 		true
					, options: 		{
						_availableToUser: {
							default: 		true
							, isImmmutable: true
						}
						, activeDoc: {
							name: 'Create an active document?',
							description: 'If <strong>Yes</strong>, the document will continue to merge the latest data and stay up to date (Note: Any changes made to the document here will not be reflected). If <strong>No</strong>, the document will be created as a snapshot at time of creation.',
							type: 'bool',
							availableToDeferred: false
						}
						, name: {
							name: 	'Document Name'
							, type: 'string'
							, merge: true
						}
						, document: {
							name: 			'Document Body'
							, type: 		'object'
							, objectType: 	'contracts'
							, where: function(state) {
								return getTemplateQuery(state);
							}
							, viewSelection: function (ui, selection, object, options) {

								sb.notify({
									type: 'view-single-contract',
									data: {
										ui: ui,
										selection: selection,
										object: object,
										options: options
									}
								});

							}
						}
					}
					, parseLog: 	function (log, response) {

						// If the email is logged in a doc, append the doc link 
						// to the log.
						if (response && response._doc) {

							var link = sb.url + '/api/document.php?instance='+ appConfig.instance +'&document='+ response._doc;
							log.note += ' View document <a href="'+ link +'">here</a>';

						}
						
						return log;

					}
				}
			});
			
		}
		
	};
	
});