Factory.register('tag-actions', function (sb) {
	
	function run_action () {
		
	}
	
	return {
		
		init: function () {
			
			function mergeTagsFromObj (obj) {
				console.log('obj::', obj);
				return obj;
				
			}
			
			// Register tagging actions..
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'applyTags'
					, category: 'Share'
					, title:	'Add tag(s)'
					, icon: 	'tags'
					, color: 	'teal'
					, action: 	run_action
					, options: {
						tags: {
							name: 'Tag(s)'
							, type: 'tags'
							, pullFrom: 'tagsFromObj'
						}
						, tagsFromObj: {
							name: 				'Tags from objs:'
							, type: 			'field'
							, allowedTypes: 	['user', 'users', 'contacts']
							, multi:		 	true
							, merge: 	 		true
							, select: 			{
								name: true
								, fname: true
								, lname: true
							}
							, parseMerge: mergeTagsFromObj
							, availableToDeferred: false
						}
						, applyToParent: {
							name: 		'Apply to parent?'
							, description: 	'If checked, the tags will be applied to the context object\'s parent instead of directly on the context object.'
							, default: 	false
							, type: 	'bool'
						}
					}
				}
			});
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'rmTags'
					, category: 	'Share'
					, title:	 'Remove tag(s)'
					, icon: 	'tags'
					, color: 	'red'
					, action: 	run_action
					, options: {
						tags: {
							name: 'Tags'
							, type: 'tags'
						}
						, tagsFromObj: {
							name: 				'Tags from objs:'
							, type: 			'field'
							, allowedTypes: 	['user', 'users', 'contacts']
							, multi:		 	true
							, merge: 	 		true
							, select: 			{
								name: true
								, fname: true
								, lname: true
							}
							, parseMerge: mergeTagsFromObj
							, availableToDeferred: false
						}
						, rmFromParent: {
							name: 		'Remove from parent?'
							, description: 	'If checked, the tags will be removed from the context object\'s parent instead of directly from the context object.'
							, default: 	false
							, type: 	'bool'
						}
					}
				}
			});
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'setTags'
					, category: 	'Share'
					, title:	 'Set tag(s)'
					, icon: 	'tags'
					, color: 	'black'
					, action: 	run_action
					, options: {
						tags: {
							name: 'Tags'
							, type: 'tags'
						}
					}
				}
			});
			
			// Register subscribing actions.
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'addSubs'
					, category: 'Share'
					, title: 	'Add subscription(s)'
					, icon: 	'bell'
					, color: 	'teal'
					, action: 	run_action
					, options: {
						tags: {
							name: 'Teammate(s)'
							, type: 'tags'
						}
						, applyTo: {
							name: 'Apply to:'
							, type: 'select'
							, options: [
								{
									name: 'This thing'
									, value: 'this'
								}, {
									name: 'Nested action items'
									, value: 'child_entities'
								}
							]
							,  availableToDeferred: false
						}
					}
				}
			});
/*
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'rmSubs'
					, category: 'Share'
					, title: 	'Remove subscription(s)'
					, icon: 	'bell'
					, color: 	'red'
					, action: 	run_action
					, options: {
						tags: {
							name: 'Teammate'
							, type: 'tags'
						}
					}
				}
			});
*/
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'setSubs'
					, category: 'Share'
					, title: 	'Set subscription(s)'
					, icon: 	'bell'
					, color: 	'black'
					, action: 	run_action
					, options: {
						tags: {
							name: 'Teammate'
							, type: 'tags'
						}
						, applyTo: {
							name: 'Apply to:'
							, type: 'select'
							, options: [
								{
									name: 'This thing'
									, value: 'this'
								}, {
									name: 'Nested action items'
									, value: 'child_entities'
								}
							]
							,  availableToDeferred: false
						}
					}
				}
			});
			
		}
		
	};
	
});