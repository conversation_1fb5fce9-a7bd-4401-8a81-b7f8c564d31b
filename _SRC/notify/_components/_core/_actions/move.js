Factory.register ('move-action', function (sb) {
		
	function Run (obj, state, dom, onMove){
		
		var onComplete = function (obj, newLocation) {
		
			var noteObj = {
				type_id: obj.id,
				type: obj.object_bp_type,
				note: `<strong>${obj.name}</strong> moved to <strong>${newLocation.name}</strong>.`,
				record_type:'log',
				author: sb.data.cookie.get('uid'),
				notifyUsers:[],
				log_data:{
					type:'move',
					objectName:obj.name,
					details:`<strong>${document.name}</strong> moved to <strong>${newLocation.name}</strong>.`
				}
			};
			
			sb.data.db.obj.create('notes', noteObj, function(newNote){
				
				onMove(obj);
				
			});
			
		};
		
		function addTeamSection(team, tool, onComplete){
			
			sb.data.db.obj.getById('groups', team.id, function(currentProject) {
				
				if ( !_.isEmpty(currentProject) ) {
					
					team.tools = !_.isEmpty(currentProject.tools) ? currentProject.tools : [];
					var numOfTools = !_.isEmpty(currentProject.tools) ? currentProject.tools.length : 0;
		
					var projectTool = _.findWhere(team.tools, {system_name:tool.id});
				
					if (projectTool) {
						
						if (projectTool.is_archieved == 1) {
							
							// turn the tool on
							_.findWhere(team.tools, {system_name:tool.id}).is_archieved = 0;
							
							if(tool.required){
							
								var pass = true;
								
								_.each(tool.required, function(toolName){
									
									var requiredTool = _.findWhere(appConfig.teamTools, {id:toolName});
		
									if(requiredTool){
										
										var toolOnProject = _.findWhere(team.tools, {system_name:toolName});
										
										if(toolOnProject){
											
											_.findWhere(team.tools, {system_name:toolName}).is_archieved = 0;
											
										}else{
											
											team.tools.push({
												allowed_users:[],
												system_name:requiredTool.id,
												display_name:requiredTool.name,
												is_archieved:0,
												order: numOfTools,
												added_by:sb.data.cookie.userId,
												added_on:moment(),
												settings:{},
												box_color:''
											});
											
										}
										
									}else{
										
										pass = false;
										
									}
									
								});
								
								
								
								if(pass === false){
									
									sb.dom.alerts.alert('Error', 'A required tool is not available', 'error');
									onComplete(false);
									return;
									
								}
								
							}
							
							sb.data.db.obj.update('groups', team, function(updated){
		
								onComplete(updated);
																																		
							}, 2);
							
						} else {
							
							var requiredByAnotherTool = false;
							_.each(appConfig.teamTools, function(systemTool){
		
								if(systemTool.required){
									
									if(systemTool.required.indexOf(tool.id) > -1){
										
										requiredByAnotherTool = _.findWhere(team.tools, {system_name:systemTool.id});
										
									}
									
								}
								
							});
		
							if (requiredByAnotherTool) {
								
								if(requiredByAnotherTool.is_archieved == 0){
									
									sb.dom.alerts.alert('', 'This tool is required by '+ requiredByAnotherTool.display_name +'. Please remove that tool first.', 'error');
									return;
									
								}
								
							}
							
							// turn the tool off
							_.findWhere(team.tools, {system_name:tool.id}).is_archieved = 1;
						
							sb.data.db.obj.update('groups', {id:team.id, tools:team.tools}, function(updated){
								
								onComplete(updated);
								
							}, 2);
							
						}
						
					} else {
		
						// add the tool
						var toolObj = {
							allowed_users: [],
							system_name: tool.id,
							display_name: tool.name,
							is_archieved: 0,
							order: numOfTools,
							added_by: sb.data.cookie.userId,
							added_on: moment(),
							settings: {},
							box_color: ''
						};
		
						team.tools.push(toolObj);
						
						if (tool.required) {
							
							var requiredTools = [];
							
							_.each(tool.required, function(toolName){
								
								var requiredTool = _.findWhere(appConfig.teamTools, {id:toolName});
								
								requiredTools.push({
									allowed_users: [],
									system_name: requiredTool.id,
									display_name: requiredTool.name,
									is_archieved: 0,
									order: numOfTools,
									added_by: sb.data.cookie.userId,
									added_on: moment(),
									settings: {},
									box_color: ''
								});
								
							});
							
						}

						sb.data.db.obj.update('groups', team, function(updated){
		
							onComplete(updated);
							
						}, 2);
						
					}
					
				}			
				
			},
			{
				tools:{}
			});
			
		}
		
		function form_ui(dom, obj){
		
			function processForm(form, callback, selectedParent) {
				
				// Ask the user if they want to keep the object in their My Stuff
				var keepInMyStuff = false;
				if ( _.contains(obj.tagged_with, parseInt(sb.data.cookie.userId)) ) {
				
					sb.dom.alerts.ask({
						title: 	'Keep in My Stuff?'
						, text: 'Do you want to keep this in your My Stuff area?'
					}, function (r) {
						
						// Close dialoge
						swal.close();
						
						if (r) {
						
							// Set keep in my stuff to true
							keepInMyStuff = true;
							
							// Continue processing
							moveObject();
							
						} else {
							
							// Continue processing
							moveObject();
						}
						
					});
					
				} else {
					
					// Continue processing
					moveObject();
					
				}
				
				function moveObject() {
					
					// If it is an entity, just update the parent property and add
					// the tag of the new parent to the entity.
					if ( obj.object_bp_type && obj.object_bp_type.substr(0, 1) === '#' ) {	
						
						// Move entity
						sb.data.db.obj.runSteps({
							'move': {
								obj: obj.id,
								options: {
									newParent: selectedParent.id,
									userID: parseInt(sb.data.cookie.userId),
									keepInMyStuff: keepInMyStuff
								}
							}
						},
						obj.id,
						function(response){
							
							// Refresh window
							location.reload();
							
						});
										
					} else {
					
						var relationProperty = 'parent';
						if(state.hasOwnProperty('parentProperty')){
							relationProperty = state.parentProperty;
						}
						
						sb.data.db.obj.getWhere(selectedParent.object_bp_type, {parent:state.currentGroupObject.id, childObjs:2}, function(nestedGroups) {
							
							var queryObject = {
								id: selectedParent.id,
								childObjs:2
							};
							if(queryObject.id == 0){
								queryObject = {
									parent:0,
									childObjs:2
								};
							}
			
							sb.data.db.obj.getWhere(selectedParent.object_bp_type, queryObject, function(newGroupLocation) {	
									
								var updatedObject = [{
									id: obj.id,
									name: obj.name,
									object_bp_type: obj.object_bp_type,
									[relationProperty]: selectedParent,
									related_object: selectedParent,
									tagged_with: newGroupLocation[0].tagged_with
								}];
								
								var child = _.findWhere(nestedGroups, {id:updatedObject[0].parent});
								if(child != undefined){
		
									updatedObject.unshift({
										id: child.id,
										name: child.name,
										object_bp_type: child.object_bp_type,
										parent: state.currentGroupObject.parent,
										related_object: state.currentGroupObject.parent,
										tagged_with: state.currentGroupObject.tagged_with
									});
									
								}
								
								updatedObject[0].tagged_with.push(newGroupLocation[0].id);
								
								// Update to keep the object in My Stuff
								if ( keepInMyStuff ) {
									updatedObject[0].tagged_with.push(parseInt(sb.data.cookie.userId));
								} else {
									updatedObject[0].tagged_with = _.without(updatedObject[0].tagged_with, parseInt(sb.data.cookie.userId));
								}
								
								if ( selectedParent.object_bp_type == 'contacts' ) {
								
									if ( obj.hasOwnProperty('group_type') ) {
										
										if ( obj.group_type = 'Project' ) {
											
											sb.dom.alerts.ask({
												title: 	'Are you sure?'
												, text: 'By moving the project to this contact, you will also be changing the point of contact for this project.'
											}, function (r) {
												
												// Close dialoge
												swal.close();
												
												if (r) {
													
													// Update the main contact
													updatedObject[0]['main_contact'] = selectedParent;
													
													// Save
													callback(updatedObject, newGroupLocation);
													
												} else {
													
													// Enable save button
													right.seg.formCont.btnCol.btnGroup.moveGroup.loading(false);
												}
												
											});
										}
										
									}
									
								} else {
									
									// Save
									callback(updatedObject, newGroupLocation);
									
								}
								
							});
							
						});
						
					}
					
				}
				
			}
			
			function moveGroup(updatedObject, newGroupLocation){

				function update_data() {
					
					sb.data.db.obj.update(obj.object_bp_type, updatedObject, function(response){

						// Refresh page
						location.reload();
						
					});
					
				}
				
				var toolType = 'teamTool';
				var needsTool = false;

				if(state.tool){
					
					var projectTool = _.findWhere(newGroupLocation[0].tools, {system_name:state.tool});
					
					toolType = state.tool;
					
					if(projectTool == undefined){
						
						needsTool = true;
						
					} else {
						
						projectTool.is_archieved = 0;
						updatedObject.unshift({
							id: newGroupLocation[0].id,
							tools: newGroupLocation[0].tools
						});
						
					}
					
				} else if (obj.group_type == 'Project') {
					
					var projectTool = _.findWhere(newGroupLocation[0].tools, {system_name:'projectTool'});
					
					toolType = 'projectTool';
					
					if(projectTool == undefined){
						
						needsTool = true;
						
					} else {
						
						projectTool.is_archieved = 0;
						updatedObject.unshift(newGroupLocation[0]);
						
					}
					
				} else {
					
					var teamTool = _.findWhere(newGroupLocation[0].tools, {system_name:'teamTool'});
					
					if(teamTool == undefined){
						
						needsTool = true;
						
					} else {
						
						teamTool.is_archieved = 0;
						updatedObject.unshift(newGroupLocation[0]);
						
					}
					
				}
				
				if (needsTool) {
					
					addTeamSection(newGroupLocation[0], _.findWhere(appConfig.teamTools, {id:toolType}), function(newTeam) {
						
						update_data();
						
					});
					
				} else {
					
					update_data();
					
				}
				
				state.currentGroupObject = {};
				
			}
			
			var left = dom.makeNode(
				'wrapper', 
				'div', 
				{
					css:'ui stackable grid container'
					, style:'min-height:500px;'
				}
			).makeNode(
				'colOne', 
				'div', 
				{
					css:'ui five wide column'
				}
			);
				
			var right = dom.wrapper.makeNode(
				'colTwo', 
				'div', 
				{
					css:'ui eleven wide column'
				}
			);
			
			var selectedParent;
			
			left.makeNode('tool', 'div', {css:'ui card'});
			left.tool.makeNode('body', 'div', {css:'content ui aligned'});
			left.tool.body.makeNode('seg', 'div', {css:'ui basic yellow segment'});
			left.tool.body.seg.makeNode('header', 'div', {css:'ui small header', text:obj.name});
			left.tool.body.seg.makeNode('details', 'div', {css:'description', text:obj.details});
			
			right.makeNode('seg', 'div', {css:'ui basic segment', style:'padding-top:0!important;'});
			right.seg.makeNode('header', 'div', {css:'ui small dividing header', text:'Select a new location to move <b>'+ obj.name +'</b> to.'});
			right.seg.makeNode('break', 'div', {text:'<br />'});
			right.seg.makeNode('formCont', 'div', {css:'ui stackable grid'});
			
			right.seg.formCont.makeNode('formCol', 'div', {css:'ui thirteen wide column'});
			right.seg.formCont.makeNode('btnCol', 'div', {css:'ui three wide column'});
			
			right.seg.formCont.btnCol.makeNode('btnGroup', 'div', {css:''});
// 			right.seg.formCont.formCol.makeNode('form', 'form', formObj);
			right.seg.formCont.formCol.makeNode('moveToSelect', 'div', {
				css: 'ui fluid search item',
				text:
					'<div class="ui icon fluid input">'+
						'<input class="prompt" type="text" placeholder="Search..">'+
						'<i class="search icon"></i>'+
					'</div>'+
					'<div class="results"></div>',
				listener:{
					type: 'search',
					objectType: 'Tag',
					category: 'group_type',
					onSelect: function(result, response) {
						
						if (!_.isEmpty(result)) {
							selectedParent = result;
						}
						
					},
					onResponse: function(raw){
						
					    var response = {
						    results : {}
					    };
					    
					    _.each(raw.results, function(item) {
						    
						    var catTitle = '';
						    if (item && !_.isEmpty(item.description)) {
							    item.description = item.description.replace(/<[^>]+>/g, '');
						    }
						    
						    // Get legacy colors (this was done because old tags used hex colors)	
							var legacyColors = sb.dom.legacycolors;
							if ( legacyColors.hasOwnProperty(item.color) ) {
								item.color = legacyColors[item.color];
							}
							
							if (
								item.object_bp_type 
								&& item.object_bp_type.substring(0, 1) === '#'
							) {
								
								var type = _.findWhere(appConfig.Types, {bp_name: item.object_bp_type.substring(1)});
								if (type) {
									
									catTitle = '<i class="ui '+ type.icon +' grey icon"></i>'+ type.name;
									
								} else {
									
									return;
									
								}
								
							} else {
								
								switch (item.object_bp_type) {
								    
								    case 'users':
								    	item.name = item.fname +' '+ item.lname;
								    	item.group_type = 'user';
								    	catTitle = '<i class="ui grey user icon"></i> Team Member';
								    	break;
								    	
								    case 'groups':
								    
								    	catTitle = '<i class="ui grey users icon"></i> Team';
								    	
								    	if ( item.group_type == 'Project' ) {
									    	
									    	catTitle = '<i class="ui grey folder icon"></i> Project';
								    
								    	} else if( item.group_type == 'JobType') {
									    	
									    	catTitle = '<i class="ui grey wrench icon"></i> Job Type';
								    	}
								    	
								    	break;
								    									    	
								    case 'system_tags':
										item.name = item.tag;
										item.group_type = 'system_tag';
								    	catTitle = '<i class="ui grey hashtag icon"></i> Tag';
								    	break;
								    	
								    case 'contacts':
								    	item.name = item.fname +' '+ item.lname;
								    	item.group_type = 'contacts';
								    	catTitle = '<i class="ui yellow user icon"></i> Contact';
								    	break;
								    	
								    default:
								    	catTitle = '<i class="ui grey plus icon"></i> New';
								    	break;
								    
							    }
								
							}

							if (
								response.results[item.object_bp_type + '-' + item.group_type]
							) {

								response.results[item.object_bp_type + '-' + item.group_type].results.push(item);							
								
							} else {
								
								response.results[item.object_bp_type + '-' + item.group_type] = {
							    	name : catTitle,
							    	results : [item]
						    	};
								
							}
						    
					    });
					    
					    return response;
					    
				    },
					where: {}
				}
			});
			
			right.seg.formCont.btnCol.btnGroup.makeNode(
				'moveGroup', 
				'button', 
				{
					css:'ui large blue circular icon button', 
					text:'<i class="arrow right icon"></i>'
				}).notify('click', {
					type:'headquarters-run',
					data:{
						
						run:function(dom, obj, state) {

							// Disable save button
							right.seg.formCont.btnCol.btnGroup.moveGroup.loading();
							
							// Process
							processForm(dom.wrapper.colTwo.seg.formCont.formCol.form, moveGroup, selectedParent);
							
						}.bind({}, dom, obj, state)
						
					}
				});
			
			dom.patch();
			
		}
		
		if(!state.hasOwnProperty('currentGroupObject')){
			
			state.currentGroupObject = {};
			
			if(obj.parent != null){
				state.currentGroupObject.parent = obj.parent.id;
			}else{
				state.currentGroupObject.parent = 0;
			}
			
			state.currentGroupObject.name = obj.name;
			
		}else if(!state.hasOwnProperty('currentGroupObject') && (obj.parent == null || obj.parent == 0) && state.headquarters){
			
			state.currentGroupObject.parent = state.headquarters.id;
			state.currentGroupObject.name = state.headquarters.name;
			
		}else{
			
			state.currentGroupObject = {};
			
		}
		
		var queryObj = {
			parent:0,
			group_type:'Team',
			childObjs:2
		};
		
		state.currentGroupObject = {
			id:obj.id
		};
		
		if(obj.parent == null || obj.parent == 0){
			state.currentGroupObject.parent = 0;
		}else{
			state.currentGroupObject.parent = obj.parent.id;
		}

		dom.makeNode('loader', 'div', {css:'ui basic loading segment'});
		dom.patch();
		
		dom.empty();
			
		form_ui(dom, obj);
		
	}
	
	return {
		
		init: 	function () {

			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'move'
					, hiddenFromEvents: true
					, title:	 	'Move'
					, icon: 		'truck'
					, color: 	'grey'
					, action: 	Run
					, modal: 	true
				}
			});
			
		}
		
	} ;
	
}) ;
