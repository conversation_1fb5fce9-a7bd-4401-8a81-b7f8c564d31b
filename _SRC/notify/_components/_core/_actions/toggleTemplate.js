Factory.register ('toggle-template-action', function (sb) {
	
	function Run (obj, state, callback) {
		
		var newVal = 	1;
		//var msg = 		'Move '+ obj.name +' to templates?';
		var msg = 		'Generate a template from '+ obj.name +'?';
		
		function creatFromTemplate(obj, callback) {
			
			var templateOptions = {
					name: obj.name + ' template'
					, is_template: 1
				}; 

			sb.data.db.obj.createFromTemplate(obj.id, function(response) {
				
				callback(response);
				
			}, 1, templateOptions);
			
		}
		
		if (obj.is_template) {
			newVal = 	0;
			msg = 		'Remove '+ obj.name +' from templates?';
		}
		
		sb.dom.alerts.ask(
			{
				title: 	msg
				, text: 	''
			}
			, function (r) {
				
				if (r) {
					
					swal.disableButtons();
					
					if (obj.is_template === 0) {
						
						creatFromTemplate(obj, function(template) {
							
							swal.close();
							//callback(obj);
							
							sb.notify({
								type:'app-navigate-to',
								data:{
									itemId:'groups',
									viewId:{
										viewState:{
											obj: template
										}
									}
								}
							});
							
						});	
						
					} else {
						
						sb.data.db.obj.update(
							obj.object_bp_type
							, {
								id: obj.id
								, is_template: 	0
							}
							, function (response) {
	
								swal.close();
								obj.is_template = 0;
								callback(obj);
								
							}
						);
						
					}
					
				} else {
					
					callback(obj);
					
				}
				
			}
		);
		
	}
	
	return {
		
		init: 	function () {
			
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'toggleTemplate'
					, title:	 	'Template'
					, hiddenFromEvents: true
					, icon: 		'copy'
					, color: 	'grey'
					, action: 	Run
					, view: 		function (obj) {
						
						if (obj.is_template) {
							
							return {
								title: 	'Remove from templates'
								, icon: 	'copy outline'
								, color: 'grey'
							};
							
						} else {
							
							return {
								title: 	'Generate a template'
								, icon: 	'copy outline'
								, color: 'grey'
							};
							
						}						
						
					}
				}
			});
			
		}
		
	} ;
	
}) ;
