Factory.register('triggerWebhook-action', function (sb) {

	function Run () {}
	
	return {
		
		init: function () {
			
			sb.notify({
				type: 	'register-action'
				, data: {
					name: 			'triggerWebhook'
					, action: 		Run
					, category: 	'Integrate'
					, color: 		'teal'
					, icon: 		'share square'
					, title:	 	'Trigger Webhook'
					, options: 		{
						_availableToUser: {
							default: 		false
							, isImmmutable: true
						}
						, url: {
							name: 	'Endpoint'
							, type: 'string'
						}
						, payload: {
							name: 				'Properties/values to send'
							, type: 			'field'
							, allowedTypes: 	['any', 'rootOnly']
							, multi:		 	true
							, merge: 	 		true
							, requiresInput: 	false
						}
						, transition: {
							name: 				'Include transition data (state from/to)'
							, type: 			'bool'
						}
					}
				}
			});
			
		}
		
	};
	
});