Factory.register('notify-to-role-action', function (sb) {

    function Run () {}

    return {

        init: function () {

            sb.notify({
                type: 	'register-action'
                , data: {
                    name: 			'notifyToRole'
                    , title:	 	'Create Notification for Role'
                    , icon: 		'envelope outline'
                    , color: 		'yellow'
                    , action: 		Run
                    , modal: 		true
                    , options: 		{
                        _availableToUser: {
                            default: 		false
                            , isImmmutable: true
                        },
                        workflow: {
                            name: 			'Select Workflow'
                            , type: 			'object'
                            , objectType: 		'project_types'
                            , where: 		{}
                            , viewSelection: function (ui, selection, object, options){

                                // If the workflow can't be found, don't
                                // try and display the states.
                                if (!(selection && selection.id)) {
                                    return;
                                }

                                var selectedTxt = 'Select a field';

                                //recovery for groups
                                sb.data.db.obj.getWhere('groups', {group_type: "Project", type:selection.id, select: {tools: true}}, function(projects) {

                                    var selectedProject = projects[0];
                                    var roleSheet = _.find(selectedProject.tools, function(_obj) {
                                        if(_obj.display_name && _obj.display_name.startsWith("Project Role Sheet")){
                                            return true;
                                        }
                                        return false;
                                    });

                                    var bluePrint = roleSheet.system_name;

                                    //drop down for roles
                                    var dropdownSetup = {
                                        css: 'ui fluid multiple selection dropdown',
                                        listener: {
                                            type: 'dropdown',
                                            onChange: function(value){
                                                value = value.split(',');
                                                options.roles = value;
                                            }
                                        },
                                        placeholder: 'Select a role'
                                    };

                                    dropdownSetup.listener.values = [];

                                    sb.data.db.obj.getWhere('entity_type', {bp_name: bluePrint}, function(entities){

                                        _.forEach(entities[0].blueprint, function (_entity, index){

                                            if(_entity.fieldType === 'user' && _entity.is_archived !== true){

                                                dropdownSetup.listener.values.push({
                                                    name: 		'<i class="grey user icon"></i> '+ _entity.name +'</div>'
                                                    , value: 	index
                                                    , selected: _.contains(options.roles, index)
                                                });

                                            }

                                            selectedTxt = '<div class="default text"></div> <i class="dropdown icon"></i>';
                                            dropdownSetup.text = selectedTxt;

                                            dropdownSetup.text = '<div class="text">'+ selectedTxt +'</div> <i class="dropdown icon"></i>';


                                            ui.makeNode('br1', 'div', {
                                                text: '<br />'
                                            });

                                            ui.makeNode(
                                                'label'
                                                , 'div'
                                                , {
                                                    tag: 'h5'
                                                    , text: 'Roles'
                                                }
                                            );

                                            ui.makeNode('wf', 'div', {});
                                            ui.wf.makeNode('select', 'div', dropdownSetup).makeNode(
                                                'menu'
                                                , 'div'
                                                , {
                                                    css: 'menu'
                                                }
                                            );
                                            ui.makeNode('br2', 'div', {
                                                text: '<br />'
                                            });

                                            ui.patch();

                                        });
                                    });

                                });

                            }
                        }
                    }
                }
            });

        }

    };

});
