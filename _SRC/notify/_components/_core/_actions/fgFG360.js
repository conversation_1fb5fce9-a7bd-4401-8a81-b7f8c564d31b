Factory.register ('FGcreateFG360-action', function (sb) {
	
	return {
		
		init: 	function () {
			// Only turn this on in our dev instances or in the 
			// 'foundation_group' instance where it is ultimately going to be 
			// used.

			if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'foundation_group'
			) {

				sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'createFG360Membership'
						, title:	 	'Create FG 360 Membership'
						, icon: 		'plus'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{}
					}
				});

                sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'createFG360Consultation'
						, title:	 	'Create FG 360 Consultation'
						, icon: 		'plus'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{}
					}
				});

			}

			
		}
		
	} ;
	
}) ;
