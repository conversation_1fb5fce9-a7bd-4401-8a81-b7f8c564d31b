Factory.register ('FGclientInfoRequest-action', function (sb) {
	
	return {
		
		init: 	function () {

			// Only turn this on in our dev instances or in the 
			// 'foundation_group' instance where it is ultimately going to be 
			// used.

			if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'foundation_group'
			) {

                sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGAddAttachmentToPortal'
						, title:	 	'Share Attachment in client\'s portal'
						, icon: 		'upload'
						, color: 		'purple'
						, action: 		function () {}
						, options: {
							currentDocs: {
								name: 'Current Client Request/Review Files',
								type: 'table',
								objectType: "document",
								isProject: false
							},
							allDocs: {
								name: 'All Documents in Project',
								type: 'table',
								objectType: "document",
								isProject: true
							}
						}
					}
				});

			}

		}
		
	} ;
	
}) ;
