Factory.register('copy-action', function (sb) {
	
	function Run (obj, state, callback) {
		
		function creatFromTemplate(obj, callback) {

			var templateOptions = {
					name: obj.name + ' -copy'
					, is_template: obj.is_template
				}; 

			sb.data.db.obj.createFromTemplate(obj.id, function(response) {
				
				callback(response);
				
			}, 1, templateOptions);
			
		}
		
		sb.dom.alerts.ask(
			{
				title: 	'Duplicate ' + obj.name + '?'
				, text: 	''
			}
			, function (r) {
				
				if (r) {
					
					swal.disableButtons();
					
					creatFromTemplate(obj, function(template) {
						
						swal.close();
						//callback(obj);
						
						sb.notify({
							type:'app-navigate-to',
							data:{
								itemId:'groups',
								viewId:{
									viewState:{
										obj: template
									}
								}
							}
						});
						
					});
					
				} else {
					
					callback(obj);
					
				}
				
			}
		);
		
	} 
	
	return {
		
		init: function () {

			sb.notify ({
				type: 	'register-action'
				, data: {
					name: 			'copy'
					, title:	 	'Duplicate'
					, icon: 		'copy'
					, color: 		'grey'
					, hiddenFromEvents: true
					, action: 		Run
				}
			});
			
		}
		
	};
	
});