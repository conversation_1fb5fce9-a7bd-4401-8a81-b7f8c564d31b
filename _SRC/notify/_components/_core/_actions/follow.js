Factory.register ('follow-action', function (sb) {
	
	function Run (obj, state, callback) {
		
		function updateNotifyList(object, users, callback){
		
			var notifyList = _.clone(object.notify);
			var usersList = users || [+sb.data.cookie.userId];
			var updatedList;
		
			if(_.intersection(notifyList, usersList).length == usersList.length){
		
				updatedList = _.difference(notifyList, usersList);
		
			}else{
		
				updatedList = _.union(notifyList, usersList);
				
			}
			
			updatedList = _.uniq(updatedList);
		
			sb.data.db.obj.update('', {id:object.id, notify:updatedList}, function(resp){
				
				obj.notify = resp.notify;
				
				if(callback && typeof callback == 'function')
					callback(obj);
									
			});
			
		}
		
		updateNotifyList(obj, false, callback);
		
	}
	
	return {
		
		init: 	function () {
			
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 		'toggleFollow'
					, hiddenFromEvents: true
					, title:	'Follow'
					, icon: 	'bell'
					, color: 	'grey'
					, action: 	Run
					, view: 	function (obj) {
						
						if (_.contains(obj.notify, +sb.data.cookie.userId)) {
							
							return {
								title: 	'Stop following'
								, icon: 'bell'
								, color:'yellow'
							};
							
						} else {
							
							return {
								title: 	'Follow'
								, icon: 'bell outline'
								, color:'grey'
							};
							
						}						
						
					}
				}
			});
			
		}
		
	} ;
	
}) ;
