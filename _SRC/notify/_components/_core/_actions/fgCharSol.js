Factory.register ('FGcreateCharSolProject-action', function (sb) {
	
	return {
		
		init: 	function () {
			
			// Only turn this on in our dev instances or in the 
			// 'foundation_group' instance where it is ultimately going to be 
			// used.

			if (
				appConfig.instance === 'rickyvoltz'
				|| appConfig.instance === 'foundation_group'
			) {

				sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGcreateCharSolProject'
						, title:	 	'Create Charitable Solicitation Project'
						, icon: 		'plus'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{
							type: {
								name: 		'Type' 
								, type: 	'select'
								, multi:	false
								, options: [
									{
										name: 		'Initial'
										, value: 	'initial'
									}
									, {
										name: 		'Renewal'
										, value: 	'renewal'
									}
								]
							}
						}
					}
				});

				sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGSetCharSolHolds'
						, title:	 	'Update the Hold Status on a Charitable Solicitation Project'
						, icon: 		'refresh'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{}
					}
				});

				sb.notify ({
					type: 'register-action'
					, data: {
						name: 			'FGClientApprove990'
						, title:	 	'When Client Approves a 990'
						, icon: 		'refresh'
						, color: 		'purple'
						, action: 		function () {}
						, options: 		{}
					}
				});

			}

			
		}
		
	} ;
	
}) ;
