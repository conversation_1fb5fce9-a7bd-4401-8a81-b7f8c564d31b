Factory.register ('setManagers-action', function (sb) {
		
	return {
		
		init: 	function () {

			sb.notify ({
				type: 'register-action'
				, data: {
                    name: 		    'setManagers'
					, title:	 	'Set Project Manager'
					, icon: 		'user'
					, color: 	    'yellow'
					, action: 	    function () {}
					, options: {
						pullFrom: {
							name: 				'Pull From:'
							, type: 			'field'
							, allowedTypes: 	['user', 'users']
							, multi:		 	true
							, merge: 			true
							, availableToDeferred: false
						}
					}
				}
			});
			
		}
		
	} ;
	
}) ;
