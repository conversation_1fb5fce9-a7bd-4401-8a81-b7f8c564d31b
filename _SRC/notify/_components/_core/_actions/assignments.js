Factory.register('assignments-action', function (sb) {

    function Run (obj, state, callback) {}

    function renderAssigneField(ui, bp_name, options, setup) {

        var selectedTxt = 'Select a field';

        var filteredName = 'Assignee'; //this is not valid on the selection

        sb.data.db.obj.getBlueprint(bp_name, function(fields){

            _.forEach(fields, function (_field, index){

                if(_field.fieldType === 'user' && _field.is_archived !== true && _field.name !== filteredName){

                    setup.listener.values.push({
                        name: 		'<i class="grey user icon"></i> '+ _field.name +'</div>'
                        , value: 	index
                        , selected: _.contains(options.roles, index)
                    });

                }

                selectedTxt = '<div class="default text"></div> <i class="dropdown icon"></i>';
                setup.text = selectedTxt;

                setup.text = '<div class="text">'+ selectedTxt +'</div> <i class="dropdown icon"></i>';


                ui.makeNode('br1', 'div', {
                    text: '<br />'
                });

                ui.makeNode(
                    'label'
                    , 'div'
                    , {
                        tag: 'h5'
                        , text: 'Roles'
                    }
                );

                ui.makeNode('wf', 'div', {});
                ui.wf.makeNode('select', 'div', setup).makeNode(
                    'menu'
                    , 'div'
                    , {
                        css: 'menu'
                    }
                );
                ui.makeNode('br2', 'div', {
                    text: '<br />'
                });

                ui.patch();

            });

        });

    }

    return {

        init: function () {

            sb.notify({
                type: 	'register-action'
                , data: {
                    name: 			'assignments'
                    , title:	 	'Assignee Teammate on Role Sheet'
                    , icon: 		'users icon'
                    , color: 		'blue'
                    , action: 		Run
                    , modal: 		true
                    , options: 		{
                        _availableToUser: {
                            default: 		false
                            , isImmmutable: true
                        },
                        workflow: {
                            name: 			'Select Workflow'
                            , type: 			'object'
                            , objectType: 		'project_types'
                            , where: 		{}
                            , viewSelection: function (ui, selection, object, options){

                                // If the workflow can't be found, don't
                                // try and display the states.
                                if (!(selection && selection.id)) {
                                    return;
                                }

                                //recovery for groups
                                sb.data.db.obj.getWhere('groups', {group_type: "Project", type:selection.id, select: {tools: true}}, function(projects) {

                                    var selectedProject = projects[0];
                                    var roleSheet = _.find(selectedProject.tools, function(_obj) {
                                        if(_obj.display_name && _obj.display_name.startsWith("Project Role Sheet")){
                                            return true;
                                        }
                                        return false;
                                    });

                                    var bpName = '#' + roleSheet.system_name;

                                    //drop down for roles
                                    var dropdownSetup = {
                                        css: 'ui fluid selection dropdown',
                                        listener: {
                                            type: 'dropdown',
                                            onChange: function(value){
                                                value = value.split(',');
                                                options.roles = value;
                                            }
                                        },
                                        placeholder: 'Select a role'
                                    };

                                    dropdownSetup.listener.values = [];
                                    renderAssigneField(ui, bpName, options, dropdownSetup);

                                });

                            }
                        }
                    }
                }
            });

        }

    };

});
