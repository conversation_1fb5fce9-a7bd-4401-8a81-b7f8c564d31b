Factory.register('send-for-signature-action', function (sb) {

    function getTemplateQuery(state) {

        var where = {
            is_template: 1,
            // childObjs: 1,
            active: {
                type: 'not_equal',
                value: 'No'
            },
        };
        
        switch (state.object_bp_type) {

            case 'entity_workflow':
                where.merge_type = 'space';
                break;
            case 'project_types':
				where.merge_type = 'proposal';
                break;
				
			}
			
			return where;
			
	}
		
	function mergeEmailAddress (obj) {
			
			console.log('mergeEmailAddress::', obj);
			function getContactEmail (obj) {
				
				var ret = '';
				if (Array.isArray(obj.contact_info)) {
					
					_.each(obj.contact_info, function (info) {
						
						if (
                        info.is_primary
                        && info.type.data_type === 'email'
                    ) {
                        
                        ret = info.info;
                        
                    }
                    
                });
                
            }
            
            return ret;
            
        }
        
        var ret = '';
        
        if (typeof obj === 'string') {
            
            return obj;
            
        }else if (Array.isArray(obj)) {
            
            ret = [];
            _.each(obj, function (c) {
                
                if (!c) {
                    return;
                }
                
                if (c.email) {
                    
                    ret.push(c.email);
                    
                } else if (c.object_bp_type === 'contacts') {
                    
                    // Get primary email from contact info data.
                    ret.push(
                        getContactEmail(c)
                    );
                    
                }
                
            });
            
            return ret.join(', ');
            
        } else {
            
            if (obj) {
                
                if (obj.email) {
                    
                    return obj.email;
                    
                } else if (obj.object_bp_type === 'contacts') {
                    
                    // Get primary email from contact info data.
                    return getContactEmail(obj);
                    
                }
                
            }
            
        }
        return '';
        
    }

    return {

        init: function () {

            sb.notify({
                type: 'register-action'
                , data: {
                    name: 		'sendForSignature'
					// , category: 'Share'
					, title:	'Send contract for signature'
					, icon: 	'file'
					, color: 	'teal'
					, action: 	function () {}
					, options: {
						_availableToUser: {
							default: 		true
							, isImmmutable: true
						}
						, signers: {
							name: 				'Signer(s) (a set of <i>Team members</i> or <i>Contacts</i> to get signature from)'
							, type: 			'field'
							, allowedTypes: 	['user', 'users', 'contacts', 'email-address']
							, multi:		 	true
							, availableToDeferred: false
							, merge: true
						}
						, notify: {
							name: 				'Notify (a set of <i>Team members</i> to keep notified of views/signatures of the new contracts)'
							, type: 			'field'
							, allowedTypes: 	['user', 'users']
							, multi:		 	true
							, availableToDeferred: false
							, merge: true
						}
						, title: {
							name: 	'Contract title (the name of the Contract document to send for signature)'
							, type: 'string'
							, merge: true
						}
						, emailTitle: {
							name: 	'Email title (the subject line of the email being sent)'
							, type: 'string'
							, merge: true
						}
						, document: {
							name: 			'Document (the <i>Document Template</i> used to build the contract)'
							, type: 		'object'
							, objectType: 	'contracts'
							, where: function(state) {
								return getTemplateQuery(state);
							}
							, viewSelection: function (ui, selection, object, options) {
								
								sb.notify({
									type: 'view-single-contract',
									data: {
										ui: ui,
										selection: selection,
										object: object,
										options: options
									}
								});
								
							}
						}
					}
                }
            });

        }

    };

});