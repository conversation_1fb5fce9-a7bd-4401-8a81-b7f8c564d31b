Factory.register('createPortal', function (sb) {

	function Run () {}
	
	return {
		
		init: function () {
			
			var contactId = 0;
			
			function mergeEmailAddress (obj) {
				
				function getContactEmail (obj) {

					var ret = '';
					if (Array.isArray(obj.contact_info)) {
						
						_.each(obj.contact_info, function (info) {

							if (
								info.is_primary === 'yes'
								&& info.type.data_type === 'email'
							) {
								
								contactId = obj.id;
								ret = info.info;
								
							}
							
						});
						
					}
					
					return ret;
					
				}

				var ret = '';
				
				if (Array.isArray(obj)) {
					
					ret = [];
					_.each(obj, function (c) {
						
						if (!c) {
							return;
						}
						
						contactId = c.id;
						
						if (c.email) {
							
							ret.push(c.email);
							
						} else if (c.object_bp_type === 'contacts') {
							
							// Get primary email from contact info data.
							ret.push(
								getContactEmail(c)
							);
							
						}
						
					});
					
					return ret.join(', ');
					
				} else {
					
					if (obj) {
						
						contactId =  obj.id;
						
						if (obj.email) {
							
							return obj.email;
							
						} else if (obj.object_bp_type === 'contacts') {
							
							// Get primary email from contact info data.
							return getContactEmail(obj);
							
						}
						
					}
					
				}
				
				// !TODO: Add contact id to options to submit.
				// !TODO: Don't allow this to be empty.
				return '';
				
			}
			
			sb.notify({
				type: 	'register-action'
				, data: {
					name: 			'createPortal'
					, title:	 	'Create Client Portal'
					, icon: 		'key'
					, color: 		'blue'
					, action: 		Run
					, options: 		{
						_availableToUser: {
							default: 		true
							, isImmmutable: true
						}
						, email: {
							name: 				'Email Address'
							, helperText: 		'The client\'s email address, which the login credentials will be sent to/they will use to log in'
							, type: 			'field'
							, allowedTypes: 	['contacts']
							, multi:		 	false
							, merge: 	 		true
							, select: 			{
								email: true
								, contact_info: {
									info: 	true
									, is_primary: true
									, type: {
										type: true
										, data_type: true
									}
								}
							}
							, parseMerge: 		mergeEmailAddress
							
						}
						, message: {
							name: 			'Message'
							, type: 		'object'
							, objectType: 	'contracts'
							, searchAvailableToDeferred: false
							, helperText: 	'The message the client will receive (with login credentials) when they are given access to their portal'
							, where: 		{
								related_object: 0
							}
							, viewSelection: function (ui, selection, object, options) {

								if (options && contactId) {
									options.contact = contactId;
								}

								sb.notify({
									type: 'view-single-contract',
									data: {
										ui: ui,
										selection: selection,
										object: object,
										options: options
									}
								});
								
							}
						}
						, layout: {
							name: 			'Dashboard(s)'
							, type: 		'object'
							, objectType: 	'layouts'
							, where: 		{
								is_template: 1
							}
							, availableToDeferred: false
						}
					}
				}
			});
			
		}
		
	};
	
});