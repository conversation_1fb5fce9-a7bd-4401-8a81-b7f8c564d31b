Factory.register('sendEmail-action', function (sb) {
	
	function Run (obj, state, modal) {
		
		sb.notify({
			type: 'get-merged-html'
			, data: {
				obj: obj
				, callback: function (html_string) {

					var options = {
						isMerged: true,
						message: obj.id,
						body: {
							html: html_string
						},
						title: obj.name
					};

					modal.header.makeNode('header', 'div', {
						text: 'Send Email',
						tag: 'h2',
						style: 'margin-bottom:15px;'
					});

					var toContainer = modal.body.makeNode('toContainer', 'div', {});
					toContainer.makeNode('label', 'div', {
						text: '<strong>To</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'plain-text',
							property: 'sendTo',
							obj: options,
							options: {
								inCollection: true,
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate: function(option) {
									options.sendTo = option;
								}
							},
							ui: toContainer.makeNode('title', 'div', {
								style: 'margin-bottom:15px;'
							})
						}
					});

					var ccContainer = modal.body.makeNode('ccContainer', 'div', {});
					ccContainer.makeNode('label', 'div', {
						text: '<strong>CC</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'plain-text',
							property: 'cc',
							obj: options,
							options: {
								inCollection: true,
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate: function(option) {
									options.cc = option;
								}
							},
							ui: ccContainer.makeNode('title', 'div', {
								style: 'margin-bottom:15px;'
							})
						}
					});

					var bccContainer = modal.body.makeNode('bccContainer', 'div', {});
					bccContainer.makeNode('label', 'div', {
						text: '<strong>BCC</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'plain-text',
							property: 'bcc',
							obj: options,
							options: {
								inCollection: true,
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate: function(option) {
									options.bcc = option;
								}
							},
							ui: bccContainer.makeNode('title', 'div', {
								style: 'margin-bottom:15px;'
							})
						}
					});

					var titleContainer = modal.body.makeNode('titleContainer', 'div', {});
					titleContainer.makeNode('label', 'div', {
						text: '<strong>Title</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'plain-text',
							property: 'title',
							obj: options,
							options: {
								inCollection: true,
								edit: true,
								editing: true,
								commitUpdates: false,
								onUpdate: function(option) {
									options.title = option;
								}
							},
							ui: titleContainer.makeNode('title', 'div', {
								style: 'margin-bottom:15px;'
							})
						}
					});

					var sendDocumentAsLinkToggleContainer = modal.body.makeNode('sendDocumentAsLinkToggleContainer', 'div', {});
					sendDocumentAsLinkToggleContainer.makeNode('label', 'div', {
						text: '<strong>Send document ONLY as a link?</strong>'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'toggle',
							property: 'sendDocumentAsLink',
							obj: options,
							options: {
								inCollection: true,
								edit: true,
								editing: true
							},
							ui: sendDocumentAsLinkToggleContainer.makeNode('toggle', 'div', {
								style: 'margin-bottom:15px;'
							})
						}
					});

					var activeDocToggleContainer = modal.body.makeNode('activeDocToggleContainer', 'div', {});
					activeDocToggleContainer.makeNode('label', 'div', {
						text: '<strong>Create an active document?</strong>'
					});
					activeDocToggleContainer.makeNode('description', 'div', {
						text: 'If <strong>Yes</strong>, the linked document will continue to merge the latest data and stay up to date (Note: Any changes made to the document here will not be reflected). If <strong>No</strong>, the linked document will be created as a snapshot at time of creation.'
					});
					sb.notify ({
						type: 'view-field',
						data: {
							type: 'toggle',
							property: 'activeDoc',
							obj: options,
							options: {
								inCollection: true,
								edit: true,
								editing: true
							},
							ui: activeDocToggleContainer.makeNode('toggle', 'div', {
								style: 'margin-bottom:15px;'
							})
						}
					});

					// Save button
					modal.body.makeNode('saveBtn', 'div', {
						css: 'ui button green pull-right',
						style: 'margin-top:15px; margin-bottom:15px;',
						text: 'Send Email'
					}).notify('click', {
						type:'contracts-run',
						data:{
							run:function() {

								modal.body.saveBtn.loading();

								// Validate form
								if (_.isEmpty(options.sendTo)) {

									modal.body.saveBtn.loading(false);
									sb.dom.alerts.alert('Uh-oh', 'Please provide an email address.', 'error');
									return;

								} else if (_.isEmpty(options.title)) {

									modal.body.saveBtn.loading(false);
									sb.dom.alerts.alert('Uh-oh', 'Please provide a title.', 'error');
									return;

								} else {

									sb.data.db.obj.runSteps(
										{
											'sendEmail': {
												options: options
											}
										}
										, obj.id
										, function () {

											modal.body.saveBtn.loading(false);
											
											// Hide modal
											modal.hide();
							
										}
										, true // receive the memo in the response
									);

								}

							}

						}

					});

					// Cancel button
					modal.body.makeNode('cancelBtn', 'div', {
						css: 'ui button grey pull-right',
						style: 'margin-top:15px; margin-bottom:15px;',
						text: 'Cancel'
					}).notify('click', {
						type:'contracts-run',
						data:{
							run:function() {

								// Hide modal
								modal.hide();

							}.bind({})
						}
					}, sb.moduleId);

					// Clear float
					modal.body.makeNode('clear', 'div', {
						style: 'clear:both;',
					});

					modal.patch();

					return;

				}

			}

		});

	}
	
	return {
		
		init: function () {
			
			function mergeEmailAddress (obj) {
				
				function getContactEmail (obj) {
					
					var ret = '';
					if (Array.isArray(obj.contact_info)) {
						
						_.each(obj.contact_info, function (info) {
							
							if (
								info.is_primary
								&& info.type.data_type === 'email'
							) {
								
								ret = info.info;
								
							}
							
						});
						
					}
					
					return ret;
					
				}
				
				var ret = '';
				if (typeof obj === 'string') {
					
					return obj;
					
				}else if (Array.isArray(obj)) {
					
					ret = [];
					_.each(obj, function (c) {
						
						if (!c) {
							return;
						}
						
						if (c.email) {
							
							ret.push(c.email);
							
						} else if (c.object_bp_type === 'contacts') {
							
							// Get primary email from contact info data.
							ret.push(
								getContactEmail(c)
							);
							
						}
						
					});
					
					return ret.join(', ');
					
				} else {
					
					if (obj) {
						
						if (obj.email) {
							
							return obj.email;
							
						} else if (obj.object_bp_type === 'contacts') {
							
							// Get primary email from contact info data.
							return getContactEmail(obj);
							
						}
						
					}
					
				}
				
				return '';
				
			}

			function getTemplateQuery(state) {

				var where = {
					is_template: 1,
					active: {
						type: 'not_equal',
						value: 'No'
					}
				};
				
				switch (state.object_bp_type) {
		
					case 'entity_workflow':
						where.merge_type = 'space';
						break;
					case 'project_types':
						where.merge_type = 'proposal';
						break;

				}
		
				return where;
			
			}
						
			sb.notify ({
				type: 	'register-action'
				, data: {
					name: 			'sendEmail'
					, title:	 	'Send email'
					, icon: 		'envelope outline'
					, color: 		'blue'
					, action: 		Run
					, modal: 		true
					, options: 		{
						_availableToUser: {
							default: 		false
							, isImmmutable: true
						}
						, sendTo: {
							name: 				'To:'
							, type: 			'field'
							, allowedTypes: 	['user', 'users', 'contacts', 'email-address']
							, multi:		 	true
							, merge: 	 		true
							, select: 			{
								email: true
								, contact_info: {
									info: 	true
									, is_primary: true
									, type: {
										type: true
										, data_type: true
									}
								}
							}
							, parseMerge: mergeEmailAddress
						}
						, cc: {
							name: 				'CC:'
							, type: 			'field'
							, allowedTypes: 	['user', 'users', 'contacts', 'email-address']
							, multi:		 	true
							, merge: 	 		true
							, select: 			{
								email: true
								, contact_info: {
									info: 	true
									, is_primary: true
									, type: {
										type: true
										, data_type: true
									}
								}
							}
							, parseMerge: mergeEmailAddress
						}
						, bcc: {
							name: 				'BCC:'
							, type: 			'field'
							, allowedTypes: 	['user', 'users', 'contacts', 'email-address']
							, multi:		 	true
							, merge: 	 		true
							, select: 			{
								email: true
								, contact_info: {
									info: 	true
									, is_primary: true
									, type: {
										type: true
										, data_type: true
									}
								}
							}
							, parseMerge: mergeEmailAddress
						}
						, title: {
							name: 	'Email title'
							, type: 'string'
							, merge: true
						}
						, sendDocumentAsLink: {
							name: 'Send document ONLY as a link?',
							type: 'bool'
						}
						, activeDoc: {
							name: 'Create an active document?',
							description: 'If <strong>Yes</strong>, the linked document will continue to merge the latest data and stay up to date (Note: Any changes made to the document here will not be reflected). If <strong>No</strong>, the linked document will be created as a snapshot at time of creation.',
							type: 'bool',
							availableToDeferred: false
						}
						, message: {
							name: 			'Message'
							, type: 		'object'
							, objectType: 	'contracts'
							, where: function(state) {
								return getTemplateQuery(state);
							}
							, viewSelection: function (ui, selection, object, options) {

								sb.notify({
									type: 'view-single-contract',
									data: {
										ui: ui,
										selection: selection,
										object: object,
										options: options
									}
								});

							}
						}, schedule: {
							name: 'Schedule',
							type: 'schedule'
						}
					}
					, parseLog: 	function (log, response) {

						// If the email is logged in a doc, append the doc link 
						// to the log.
						if (response && response._doc) {

							var link = sb.url +'/app/documents#?&i='+ appConfig.instance +'&wid='+ response._doc
							log.note += ' View document <a href="'+ link +'">here</a>';

						}
						
						return log;

					}
				}
			});
			
		}
		
	};
	
});
