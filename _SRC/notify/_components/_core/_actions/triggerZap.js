Factory.register ('triggerZap-action', function (sb) {
	
	function Run (obj, state, callback) {
		
/*
		var method = 	'erase';
		var newVal = 	1;
		var msg = 		'Archive '+ obj.name +' ?';
		
		if (obj.is_deleted) {
			method = 	'restore';
			newVal = 	0;
			msg = 		'Restore '+ obj.name +' ?';
		}
		
		sb.dom.alerts.ask(
			{
				title: 	msg
				, text: 	''
			}
			, function (r) {
				
				if (r) {
					
					swal.disableButtons();
					
					sb.data.db.obj[method](
						obj.object_bp_type
						, obj.id
						, function (response) {
							
							swal.close();
							obj.is_deleted = newVal;
							callback(obj);
							
						}
					);
					
				} else {
					
					callback(obj);
					
				}
				
			}
		);
*/
		
	}
	
	return {
		
		init: 	function () {
			
			sb.notify ({
				type: 'register-action'
				, data: {
					name: 			'triggerZap'
					, category: 	'Integrate'
					, title:	 	'Trigger Zapier'
					, icon: 		'share square'
					, color: 		'orange'
					, action: 		Run
					, options: {
						url: {
							name: 'Paste your Zapier webhook url here'
							, type: 'string'
						}
					}
				}
			});
			
		}
		
	} ;
	
}) ;
