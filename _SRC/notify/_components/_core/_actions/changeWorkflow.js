Factory.register('changeWorkflow-action', function (sb) {
	
	function Run () {
		
	}
	
	return {
		
		init: function () {
			
			sb.notify ({
				type: 	'register-action'
				, data: {
					name: 			'changeWorkflow'
					, title:	 		'Change Workflow'
					, icon: 			'random'
					, color: 			'teal'
					, action: 		Run
					, options: {
						_availableToUser: {
							default: 		false
							, isImmmutable: true
						}
						, workflow: {
							name: 			'Select Workflow'
							, type: 			'object'
							, objectType: 		'project_types'
							, where: 		{}
							, viewSelection: function (ui, selection, object, options){

								// If the workflow can't be found, don't
								// try and display the states.
								if (!(selection && selection.id)) {
									return;
								}

								sb.data.db.obj.getById(
									'project_types'
									, selection.id
									, function (workflow) {
										
										var dropdownSetup = {
											css: 'ui fluid selection dropdown',
											listener: {
												type: 'dropdown',
												onChange: function(value){
													
													if (value) {
														
														options.newState = parseInt(value);
														
													}
													
												}
											},
											placeholder: 'Select a state'
										};
										var selectedTxt = '';
										var selectedState = _.findWhere(workflow.states, {uid: parseInt(options.newState)});
										
										if (options && options.newState) {
											
											if (selectedState) {
											
												selectedTxt = '<i class="grey '+ selectedState.icon +' icon"></i> '+ selectedState.name;	
												
											}
											
										}
										
										dropdownSetup.text = '<div class="text">'+ selectedTxt +'</div> <i class="dropdown icon"></i>';
										
										ui.makeNode('br1', 'div', {
											text: '<br />'
										});
										ui.makeNode(
											'label'
											, 'div'
											, {
												tag: 'h5'
												, text: 'New State'
											}
										);
										
										ui.makeNode('wf', 'div', {});
										ui.wf.makeNode('select', 'div', dropdownSetup).makeNode(
											'menu'
											, 'div'
											, {
												css: 'menu'
											}
										);
										ui.makeNode('br2', 'div', {
											text: '<br />'
										});
									
										var state = _.findWhere(
											workflow.states
											, {
												isEntryPoint: 1
											}
										);
										var i = 0;
										while (state) {
											
											var icon = state.icon;
											var color = state.color || 'grey';
											
											ui.wf.select.menu.makeNode('p'+ i, 'div', {
												css: 'item'
												, dataAttr: [
													{
														name: 'value'
														, value: state.uid
													}
												]
											})
											.makeNode('txt', 'div', {
												css: 'text'
												, tag: 'span'
												, text: '<i class="'+ color +' '+ icon +' icon"></i> '+ state.name
											});
											
											state = _.findWhere(workflow.states, {
												uid: parseInt(state.next[0])
											});
											
											i++;
											
										}
										
										ui.patch();
										
								});
								
							}							
						}
					}
				}
			});
			
		}
		
	};
	
});