Factory.register('alerts', function(sb) {

    function build_alertMessage(ui, setup) {
	    
	    // Set display time
	    var displayTime = ( setup.displayTime != null ) ? setup.displayTime : "auto";

		// Display alert
		$('body').toast({
			title: setup.header,
			message: setup.body,
			class: setup.color,
			showProgress: 'bottom',
			classProgress: setup.color,
			displayTime: displayTime
		});

	}

    return {

        init: function() {

            sb.listen({
                'display-alert': this.display,
                [sb.moduleId+'-run']: this.run
            });

        },

        display: function(setup) {

            sb.notify({
                type: 'get-mainUI',
                data: {
                    callback: function(mainUI) {

                        build_alertMessage(mainUI.wrapper, setup);

                    }
                }
            });

        },

        run: function(data) {

            data.run(data);

        }

    }

});