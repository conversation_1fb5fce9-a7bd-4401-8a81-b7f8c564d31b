var fileAPI = (function(){

	var postPath = appConfig.files.write +'pagodaAPIKey='+ appConfig.instance +'&do=',
		getPath = appConfig.files.read +'pagodaAPIKey='+ appConfig.instance +'&do=',
		deletePath = appConfig.files.delete +'pagodaAPIKey='+ appConfig.instance +'&do=',
		bucket = appConfig.files.bucket;
		
	function get(phpFunction, obj, callback, i){

		$.ajax({
			type: 'post',
			url: getPath + phpFunction,
			data: { json: JSON.stringify(obj) },
			success: function (response) {

				callback(response);
				
			},
			
			error: function (jqXHR, status, error) {

				if(jqXHR.status == 404){
					
					if(typeof i === 'undefined'){
						i = 1;
					}
					i++;
					if(i <= 6){
						
						setTimeout(function(){
							
							get(phpFunction, obj, callback, i);
							
						}, i*i*100);
						
					}else{
						
						alerts.alert('Error!', 'Bad connection--try refreshing your page.', 'error');
						
					}
					
				}
				
			}
			
		});	

	}
	
	function deleteItem(phpFunction, obj, callback, i){

		$.ajax({
			type: 'post',
			url: deletePath + phpFunction,
			data: { json: JSON.stringify(obj) },
			success: function (response) {

				callback(response);
				
			},
			
			error: function (jqXHR, status, error) {

				if(jqXHR.status == 404){
					
					if(typeof i === 'undefined'){
						i = 1;
					}
					i++;
					if(i <= 6){
						
						setTimeout(function(){
							
							get(phpFunction, obj, callback, i);
							
						}, i*i*100);
						
					}else{
						
						alerts.alert('Error!', 'Bad connection--try refreshing your page.', 'error');
						
					}
					
				}
				
			}
			
		});	

	}
	
	function uploadFile(phpFunction, file, metaData, callback, i){
		
		$.ajax({
			url: postPath + phpFunction,
			cache: false,
			contentType: false,
			processData: false,
			data: file,                         
			type: 'POST',
			success: function (response) {

				callback(response);
				
			},
			error: function (jqXHR, status, error) {

				if(jqXHR.status == 404){
					
					if(typeof i === 'undefined'){
						i = 1;
					}
					i++;
					if(i <= 6){
						
						setTimeout(function(){
							
							uploadFile(phpFunction, file, metaData, callback, i);
							
						}, i*i*100);
						
					}else{
						
						alerts.alert('Error!', 'Bad connection--try refreshing your page.', 'error');
						
					}
					
				}
				
			}
		});	

	}
	
	return {
		
		changePaths: function(apiKey){
			
			postPath = appConfig.files.write +'pagodaAPIKey='+ apiKey +'&do=';
			getPath = appConfig.files.read +'pagodaAPIKey='+ apiKey +'&do=';
			deletePath = appConfig.files.delete +'pagodaAPIKey='+ apiKey +'&do=';
			
			return true;
			
		},
		
		update: function(file, metaData, callback){
			
			var fileData = new FormData();                  
				fileData.append('file', file);
				
			_.each(metaData, function(val, key){
				fileData.append(key, val);
			});
			
			uploadFile('update', fileData, metaData, function(response){
				
				callback(response);
				
			});
			
		},
		
		upload: function(file, metaData, callback){
						
			var fileData = new FormData();                  
				fileData.append('file', file);
				
			_.each(metaData, function(val, key){
				fileData.append(key, val);
			});
			
			uploadFile('upload', fileData, metaData, function(response){
				
				callback(response);
				
			});
			
		},
		
		delete: function(fileId, callback){
			
			deleteItem('delete', {id: fileId}, function(response){
				
				callback(response);
				
			});
			
		},
		
		getAll: function(callback){
			
			get('getAll', {}, function(response){
						
				callback(response);
				
			});
			
		},
		
		getWhere: function(queryObj, callback){
			
			get('getWhere', queryObj, function(response){
						
				callback(response);
				
			});
			
		},
		
		open: function(file){

			return window.open(bucket + appConfig.instance +'/'+ file.loc);
			
		},
		
		read: function(file, callback){

			function loadHandler(event) {
				
				var csv = event.target.result;
				processData(csv);
			}
			
			function processData(csv) {
				/*
var allTextLines = csv.split(/\r\n|\n/);
				var lines = [];
				for (var i=0; i<allTextLines.length; i++) {
					var data = allTextLines[i].split(';');
					var tarr = [];
					for (var j=0; j<data.length; j++) {
// 						tarr.push(data[j].split(','));
						// ignore commas within double-quotes
						tarr.push(data[j].match(/(".*?"|[^",\s]+)(?=\s*,|\s*$)/g));
					}
					lines.push(tarr[0]);
				}
*/				
				var lines = $.csv.toObjects(csv);
				callback(lines);
			}
			
			function errorHandler(evt) {
				if(evt.target.error.name == "NotReadableError") {
					alert("Cannot read file !");
				}
			}
			
			if(file.constructor === File){
				
				var reader = new FileReader();
				reader.readAsText(file);
				
				reader.onload = loadHandler;
				reader.onerror = errorHandler;
				
			}else{
				
				var blob = null;
				var xhr = new XMLHttpRequest(); 
				xhr.open("GET", bucket + appConfig.instance +'/'+ file.loc); 
				xhr.responseType = "blob";//force the HTTP response, response-type header to be blob
				xhr.onload = function() 
				{
				    blob = xhr.response;//xhr.response is now a blob object
				    
				    var reader = new FileReader();
					// Read file into memory as UTF-8      
					reader.readAsText(blob);
					// Handle errors load
					reader.onload = loadHandler;
					reader.onerror = errorHandler;
				
				}
				xhr.send();
				
			}
			
		},
		
		getURL: function(file){

			return bucket + appConfig.instance +'/'+ file.loc;
			
		}
		
	}
	
})();