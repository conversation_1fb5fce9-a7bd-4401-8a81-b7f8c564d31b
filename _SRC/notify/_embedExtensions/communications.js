var communications = (function(){
	
	var comm = databaseConnection;
	
	return {
		
		sendEmail: function(emailObj, callback){
			
			var post = {};
			if(Array.isArray(emailObj)){
				post = {
					emails: emailObj
				}
			}else{
				post = emailObj;
			}
			
			comm.controller('sendEmail', post, function(response){
				
				callback(response);
				
			});
			
		},
		
		sendMassSMS: function(smsObj, callback){
			
			comm.controller('sendMassSMS', smsObj, function(response){
				
				callback(response);
				
			});
			
		},
		
		sendSMS: function(smsObj, callback){
			
			comm.controller('sendSMS', smsObj, function(response){
				
				callback(response);
				
			});
			
		}
		
	}
	
})();