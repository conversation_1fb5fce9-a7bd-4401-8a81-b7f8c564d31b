Factory.register('AppNav', function(sb){
	
	var allowedItems = [],
		menuSelector = '.sidebar-offcanvas',
		mainItems = [],
		testItems = [],
		removedMainItems = [],
		viewItems = [],
		currentPage = '',
		activeSubPage = {},
		user = {},
		showNavBtn = '<button class="pda-Btn pda-btn-small" id="showSideNav"><i class="fa fa-chevron-right" aria-hidden="true"></i></button>',
		sideNavState,
		filterPhrase = '',
		active = '';	
	
	function collapseSideNav(navState) {
		
		if(navState === 'close') {
			
			sideNavState = 'closed';
			
			//console.log(sideNavState);
			
			$('#sidebar').hide(350, function() {
				
				$('.main-right').css('transitionDuration', '0.3s');
				$('.main-right').addClass('pda-full-width');
				
			});
			
			if($('#showSideNav').length) {
				
				return;
				
			} else {
				
				$('#showSideNav').show();
			
				$('.toggle-left-nav-container').before(showNavBtn);	
				
			}
							
		} else if(navState === 'open') {
			
			sideNavState = 'open';
			
			//console.log(sideNavState);
			
			$('#sidebar').show(350);
			
			$('.main-right').css('transitionDuration', '0s');
			$('.main-right').removeClass('pda-full-width');
			
			$('#showSideNav').remove();
									
		}
		
		$('#showSideNav').on('click', function() {
					
			if(sideNavState === 'open') {
				return;
			}
		
			sideNavState = 'open';
			
			$('#showSideNav').remove();
			
			$('.main-right').css('transitionDuration', '0s');
			$('.main-right').removeClass('pda-full-width');
			
			$('#sidebar').animate({
				width: 'toggle'
			}, 350);
			
		});
		
	}
	
	function createList(active){
		
		sb.sys.nav.set(mainItems);
		
		sb.data.cookie.set('appNav', JSON.stringify(mainItems));

		var listItems = '',
			menuHTML = '';
		
		var sideNavigation = sb.dom.make(menuSelector, sb.moduleId, 'module');
		
		sideNavigation.makeNode('mobileMenuButton', 'button', {css:'pda-Btn pda-btn-fullWidth mobileMenuBtn', text:'<i class="fa fa-bars" aria-hidden="true"></i> '+ appConfig.systemName});
		
		sideNavigation.mobileMenuButton.listeners = [function(selector) {
			
			$(selector).on('click', function() {
				
				$('#sidebar li').slideToggle();
				
			});
			
			if($(window).width() <= 992) {
					
				$('#sidebar li').slideUp(10);
				
				$('#sidebar li').on('click', function() {
				
					$('#sidebar li').slideUp(10);
					
				});
				
			}
			
			$(window).resize(function() {
				
				if($(window).width() <= 992) {
					
					$('#sidebar li').slideUp(10);
					
				} else if($(window).width() >= 992) {
					
					$('#sidebar li').show();
					
				} 
				
			});
			
			if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
				
				$(window).off('resize');
				
			}
			
		}];
		
		sideNavigation.makeNode('list', 'list', {name: 'nav-items', css: 'nav-sidebar', style: 'overflow:scroll;'});
		
		var icon = '<i class="fa fa-chevron-left" aria-hidden="true"></i>';
		
		sideNavigation.list.makeNode('collapseBtn', 'button', {text: icon, css: 'pda-align-right hideNavMenu'});
		
		//sideNavigation.list.makeNode('clearDiv', 'container', {css: 'clear'});
		
		sideNavigation.list.collapseBtn.listeners = [function(selector) {
			
			function showSideNav() {
				
				$('#showSideNav').on('click', function() {
					
					if(sideNavState === 'open') {
						return;
					}
				
					sideNavState = 'open';
					
					$('#showSideNav').remove();
					
					$('.main-right').css('transitionDuration', '0s');
					$('.main-right').removeClass('pda-full-width');
					
					$('#sidebar').animate({
						width: 'toggle'
					}, 350);
					
				});
				
			}
			
			$(selector).on('click', function() {
				
				sideNavState = 'closed';
				
				$('#showSideNav').show();
				
				$('#sidebar').animate({
					width: 'toggle'
				}, 350, function() {
					$('.main-right').css('transitionDuration', '0.3s');
					$('.main-right').addClass('pda-full-width');
				});
				
				$('.toggle-left-nav-container').before(showNavBtn);
				
				showSideNav();
				
			});
			
		}];
		
		sideNavigation.list.makeNode('filter', 'listItem', {content: '', dataId: '', css: 'side-nav-filter'})
			.makeNode('form', 'form', [{
				type:'text',
				name:'filter',
				placeholder:'Filter...',
				value:filterPhrase
			}]);
		
		sideNavigation.list.filter.form.notify('input', {
				type:'AppNav-run',
				data: {
					run: filterList.bind(sideNavigation.list.filter.form)
				}
			}, sb.moduleId);
		
		var filteredItems = [];
		
		if(!_.isEmpty(filterPhrase)){
			
			filteredItems = _.filter(mainItems, function(item){

				return item.displayName.toUpperCase().indexOf(filterPhrase.toUpperCase()) !== -1;
				
			});
			
		}else{
			
			filteredItems = _.filter(mainItems, function(){ return true; });
			
		}
		
		_.each(filteredItems, function(pageReference){

			if(
				(
					user.type == 'contacts' &&
					allowedItems.indexOf(pageReference.pageModuleId) > -1
				) || (
					user.type == 'staff'
				)
			){

				if(pageReference.pageModuleId == active){
				
					sideNavigation.list.makeNode(pageReference.pageModuleId, 'listItem', {content: pageReference.displayName, dataId: pageReference.pageModuleId, css: 'side-nav-button-active'});
					
					if(_.size(pageReference.subList) > 0){
						
						sideNavigation.list.makeNode(pageReference.pageModuleId +'sub-list-container', 'list', {});
						_.each(pageReference.subList, function(subListItem, index){
							
							var subListItemStyle = [];
							if(activeSubPage == subListItem || _.isEmpty(activeSubPage) && /* _.isMatch(sb.data.url.getParams(), subListItem.pageParams) && */ sb.data.url.getPage() == subListItem.page){
								subListItemStyle = ['<strong>', '</strong>'];
							}else{
								subListItemStyle = ['', ''];
							}
							
							sideNavigation.list[pageReference.pageModuleId +'sub-list-container'].makeNode('sub-'+ index, 'listItem', {css: 'nav-sub-list-item', content: subListItemStyle[0] + subListItem.displayName + subListItemStyle[1], dataId: pageReference.pageModuleId})
								.notify('click', {
									type: 'side-nav-change-page',
									data: {
										pageModuleId: pageReference.pageModuleId,
										subPage: subListItem
									}
								});
							
						});
						
					}
					
				}else{
					
					sideNavigation.list.makeNode(pageReference.pageModuleId, 'listItem', {content: pageReference.displayName, dataId: pageReference.pageModuleId, css: 'side-nav-button'});
					
				}

				sideNavigation.list[pageReference.pageModuleId].notify('click', {
					type: 'side-nav-change-page',
					data: {
						pageParams: pageReference.pageParams
					}
				}, sb.moduleId);
				
			}
			
		});
		
		return sideNavigation;
		
	}
	
	function filterList(){
		
		filterPhrase = this.process().fields.filter.value;
		
		var ui = createList(currentPage);
		
		ui.build();
		
		var el = $(ui.list.filter.form.filter.selector);
		
		// w/out timeout, chrome will focus before the cursor pos has been set
		el.focus(function(){
			
		    setTimeout((function(el) {

		        var strLength = el.value.length;
		        
		        return function() {
			        
		            if(el.setSelectionRange !== undefined) {
			            
		                el.setSelectionRange(strLength, strLength);
		                
		            } else {
			            
		                $(el).val(el.value);
		                
		            }
		            
		    }}(this)), 0);
		    
		});
		
		el.focus();
		/*
var range = el.createTextRange();
        range.collapse(false);
        range.select();
*/
		
	}
	
	return {
		
		init: function(){

/*
			sb.listen({
				'AppNav-run':this.run,
				'create-sidebar-menu': this.create,
				'destroy-sidebar-menu': this.destroy,
				'restore-side-nav': this.restore,
				'side-nav-register-page': this.registerPage,
				'side-nav-register-subpage': this.registerSubpage,
				'side-nav-change-page': this.changePage,
				'toggle-sidenav': this.toggleSideNav
			});
			
			if(_.isEmpty(user)){

				sb.data.db.obj.getById('users', sb.data.cookie.userId, function(userObj){

					user = userObj;
					
					_gs('identify', {

						// We require an email address or an id for the user
						id: user.id, // Required if no email address
						email: user.email, // Required if no id
						
						name: user.fname +' '+ user.lname,
						username: user.email,
						description: '',
						avatar: '',
						phone: user.phone,
						company_name: appConfig.systemName
					
					});
							
				}, 1);
				
			}else{
				
				_gs('identify', {

					// We require an email address or an id for the user
					id: user.id, // Required if no email address
					email: user.email, // Required if no id
					
					name: user.fname +' '+ user.lname,
					username: user.email,
					description: '',
					avatar: '',
					phone: user.phone,
					company_name: appConfig.systemName
				
				});
				
			}
*/
						
		},
				
		changePage: function(data){

			filterPhrase = '';
			activePage = data.dataId;
			
			var	pageParams = {},
				to = activePage;
				
			if(typeof data.subPage !== 'undefined'){
				
				activeSubPage = data.subPage;
				activePage = data.pageModuleId;
				pageParams = data.subPage.pageParams;
				to = data.subPage.page;
				
			}else{
				
				activeSubPage = {};
				pageParams = data.pageParams;
				
			}
			
			sb.notify({
				type: 'create-sidebar-menu',
				data: activePage
			});
						
			sb.notify({
				type: 'app-change-page',
				data: {
					previousModuleId: currentPage,
					to: to,
					pageParams: pageParams
				}
			});
			
			currentPage = activePage;
			
			if(_.where(mainItems, {pageModuleId:currentPage})[0].hasOwnProperty('pageTitle')){
				document.title = _.where(mainItems, {pageModuleId:currentPage})[0].pageTitle;
			}else{
				document.title = _.where(mainItems, {pageModuleId:currentPage})[0].displayName;
			}
									
		},
				
		create: function(activePage){
			
			$('.nav-button').off('click');
				
			$(menuSelector).html( '<br />' + sb.dom.loadingGIF );
						
			if(_.isEmpty(user)){
				
				sb.data.db.obj.getWhere('users', {id:sb.data.cookie.userId, childObjs:1}, function(userObj){

					user = userObj[0];

					if(user.hasOwnProperty('id')){
						
						var allowedPages = [];
						
						_.each(user.profiles, function(prof){
							if(prof){
							allowedPages = allowedPages.concat(prof.menu_items);
							}
						});
						
						allowedItems = _.uniq(allowedPages);
	
					}
					
					allowedItems.push('login');

					
					var newList = [];
					_.each(mainItems, function(i){

						if(allowedItems.indexOf(i.pageModuleId) > -1){
							
							newList.push(i);
							
						}
						
					});

					if(newList.length > 1){
						mainItems = newList;
					}					

					var sideNav = createList(activePage);
					sideNav.build();
									
				}, 1);
				
			}else{
				
				var sideNav = createList(activePage);
				sideNav.build();
				
			}
			
			sb.sys.nav.set(mainItems);

		},
		
		destroy: function(){
			
			_.each(mainItems, function(menuItem){
				removedMainItems.push(menuItem);
			});
			
			menuSelector = '.sidebar-offcanvas',
			mainItems = [];
			viewItems = [];
			currentPage = '';
			activeSubPage = {};
			
			$('.nav-button').off('click');
			
			var sideNav = createList();
			
			sideNav.build();
			
		},
		
		registerPage: function(data){
			
			if(data.hasOwnProperty('pageTitle')){
				var displayName = data.pageTitle;
			}else{
				var displayName = data.pageName;
			}
				
			if(typeof data.subList === 'undefined'){
				data.subList = [];
			}
			
			if(data.pageModuleId == 'login'){
				
				mainItems.push({
					pageModuleId: data.pageModuleId,
					pageParams: data.pageParams,
					displayName: data.pageName,
					pageTitle: displayName,
					subList: data.subList
				});
				
			}else{

/*
				var permission = _.where(appConfig.permissions, {pageModuleId:data.pageModuleId});
			
				if(permission.length > 0){
	
					if( $.inArray(sb.data.cookie.get('uid').toString(), permission[0].view) > -1 ){
						
						testItems.push({
							pageModuleId: data.pageModuleId,
							pageParams: data.pageParams,
							displayName: data.pageName,
							subList: data.subList
						});
	
						
					}
					
				}else{
					
					testItems.push({
						pageModuleId: data.pageModuleId,
						pageParams: data.pageParams,
						displayName: data.pageName,
						subList: data.subList
					});
					
				}
*/
								
				if(sb.data.cookie.get('uid') == '753'){
					
					mainItems = [];
					
					mainItems = testItems;
					
				}else{
					
					mainItems.push({
						pageModuleId: data.pageModuleId,
						pageParams: data.pageParams,
						displayName: data.pageName,
						pageTitle: displayName,
						subList: data.subList
					});
					
				}

/*
				mainItems = [];
					
				mainItems = testItems;
*/
				
			}			
						
		},
		
		registerSubpage: function(data){
			
			if( _.where(_.where(mainItems, {pageModuleId:data.parent})[0]).length > 0 && _.where(_.where(mainItems, {pageModuleId:data.parent})[0].subList, {displayName:data.menuItem.displayName}).length == 0 ){
				
				_.where(mainItems, {pageModuleId:data.parent})[0].subList.unshift(data.menuItem);
								
			}

			var sideNav = createList(data.parent);
			
			sideNav.build();
			
		},
		
		restore: function(){
			
			mainItems = [];
			
			_.each(removedMainItems, function(menuItem){
				mainItems.push(menuItem);
			});
			
			removedMainItems = [];
			
			sb.notify({
				type:'side-nav-change-page',
				data: {
					dataId: mainItems[0].pageModuleId
				}
			});
			
		},
		
		run: function(data){

			data.run(data);
			
		},
		
		toggleSideNav: function(data) {
			
			if(data.hasOwnProperty('sideNav')) {
					
				collapseSideNav(data.sideNav);	
					
			}
			
		}
		
	}
	
});