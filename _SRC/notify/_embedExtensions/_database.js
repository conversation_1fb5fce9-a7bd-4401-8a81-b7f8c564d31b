var databaseConnection = (function () {
  var objects = [],
    db_calls = [],
    obj_blueprints = {},
    obj_bp_w_options = {},
    configTest = {},
    appConfig = {},
    rootPath = "",
    postPath = "",
    GET_PATH = "https://bento.infinityhospitality.net/api/_get.php",
    UPDATE_PATH = "https://bento.infinityhospitality.net/api/_get.php";

  var bentoToken = "";

  function deepClone(item) {
    if (!item) {
      return item;
    } // null, undefined values check

    var types = [Number, String, Boolean],
      result;

    // normalizing primitives if someone did new String('aaa'), or new Number('444');
    types.forEach(function (type) {
      if (item instanceof type) {
        result = type(item);
      }
    });

    if (typeof result == "undefined") {
      if (Object.prototype.toString.call(item) === "[object Array]") {
        result = [];
        item.forEach(function (child, index, array) {
          result[index] = deepClone(child);
        });
      } else if (typeof item == "object") {
        // testing that this is DOM
        if (item.nodeType && typeof item.cloneNode == "function") {
          var result = item.cloneNode(true);
        } else if (!item.prototype) {
          // check that this is a literal
          if (item instanceof Date) {
            result = new Date(item);
          } else {
            // it is an object literal
            result = {};
            for (var i in item) {
              result[i] = deepClone(item[i]);
            }
          }
        } else {
          // depending what you would like here,
          // just keep the reference, or create new object
          if (false && item.constructor) {
            // would not advice to do that, reason? Read below
            result = new item.constructor();
          } else {
            result = item;
          }
        }
      } else {
        result = item;
      }
    }

    return result;
  }

  function getCookie(name) {
    var value = "; " + document.cookie;
    var parts = value.split("; " + name + "=");
    if (parts.length == 2) return parts.pop().split(";").shift();
  }

  function getFromCache(call, queryObj, previousCall) {
    var ret = false;

    switch (call) {
      case "getObjectBlueprint":
        if (queryObj.getOptions) {
          ret = obj_bp_w_options[queryObj.objectType];
        } else {
          ret = obj_blueprints[queryObj.objectType];
        }
        break;

      default:
        var response = previousCall.response,
          ret = [];

        if (
          !queryObj.hasOwnProperty("paged") &&
          queryObj.hasOwnProperty("queryObj") &&
          !queryObj.queryObj.hasOwnProperty("paged")
        ) {
          ret = deepClone(response.data);
        } else {
          ret = deepClone(response);
        }
        break;
    }

    return ret;
  }

  function get(phpFunction, obj, callback, i, noCache, root, update) {
    // 		if(getCookie('uid') === '208582' && true == false){
    //!TODO: get working for all read calls; currently the cache is only turned on for blueprints
    if (phpFunction === "getObjectBlueprint") {
      if (!noCache) {
        // if we don't have blueprints list, bypass caching and get list for next time
        if (_.isEmpty(obj_blueprints)) {
          get(
            "getObjectBlueprints",
            {},
            function (blueprints) {
              for (i in blueprints) {
                obj_blueprints[blueprints[i].blueprint_name] =
                  blueprints[i].blueprint;
              }
            },
            1,
            true
          );
        } else {
          // if its an update call, mark any dependent caches as outdated
          if (
            phpFunction === "createNewObject" ||
            phpFunction === "updateObject"
          ) {
            for (i in db_calls) {
              if (
                db_calls[i].queryObj.objectType === obj.objectType ||
                db_calls[i].queryObj.type === obj.objectType ||
                _.contains(db_calls[i].childObjTypes, obj.objectType) ||
                _.contains(db_calls[i].childObjTypes, obj.type)
              ) {
                db_calls[i].getNew = true;
              }
            }

            // if its a read call, check if can use cache
          } else {
            if (phpFunction === "getObjectBlueprint") {
              var ret = getFromCache(phpFunction, obj);

              if (ret) {
                return callback(ret);
              }
            } else {
              var previousCalls = _.where(db_calls, { call: phpFunction });
              for (i in previousCalls) {
                if (
                  (_.isEqual(obj, previousCalls[i].queryObj) &&
                    !previousCalls[i].getNew &&
                    previousCalls[i].response_date.isAfter(
                      moment().subtract(120, "seconds")
                    )) ||
                  (_.isEqual(obj, previousCalls[i].queryObj) &&
                    phpFunction === "getObjectBlueprints")
                ) {
                  var ret = getFromCache(phpFunction, obj, previousCalls[i]);
                  if (ret) {
                    console.log(
                      "used the cache: (" +
                        phpFunction +
                        ", " +
                        obj.objectType +
                        ")",
                      obj,
                      ret
                    );
                    return callback(ret);
                  }
                }
              }
            }
          }
        }
      }
    }

    var callTimestamp = moment().unix();
    Factory.triggerEvent(
      {
        type: "db-call-triggered",
        data: {
          call: phpFunction,
          queryObj: obj,
          timestamp: callTimestamp,
        },
      },
      "db"
    );

    if (root != undefined) {
      var dbPath = root;
    } else {
      var dbPath = rootPath;
    }

    if (update) {
      dbPath =
        UPDATE_PATH +
        "?pagodaAPIKey=" +
        appConfig.instance +
        "&api_webform=true&do=";
    } else {
      if (root) {
        dbPath = root;
      } else {
        dbPath =
          GET_PATH +
          "?pagodaAPIKey=" +
          appConfig.instance +
          "&api_webform=true&do=";
      }
    }

    switch (appConfig.instance) {
      case "rickyvoltz":
      case "zachvoltz":
      case "petermikhail":
      case "johnwhittenburg":
      case "joshgantt":
        if (update) {
          dbPath =
            "https://bento.infinityhospitality.net/api/_get.php?pagodaAPIKey=" +
            appConfig.instance +
            "&api_webform=true&do=";
        } else {
          dbPath =
            "https://bento.infinityhospitality.net/api/_get.php?pagodaAPIKey=" +
            appConfig.instance +
            "&api_webform=true&do=";
        }

      /*
			case 'voltzsoftware':
			
				if(update){
					dbPath = 'https://pagoda.voltz.software/_repos/_production/notify/pagoda/_coredev/_get.php?pagodaAPIKey='+ appConfig.instance +'&api_webform=true&do=';
				}else{
					dbPath = 'https://get-staging.voltz.software/_repos/_production/notify/pagoda/_coredev/_get.php?pagodaAPIKey='+ appConfig.instance +'&api_webform=true&do=';
				}	
				
				break;
*/
    }

    // 		var ajaxTime= new Date().getTime();
    $.ajax({
      type: "post",
      beforeSend: function (xhr) {
        xhr.setRequestHeader(
          "Content-type",
          "application/x-www-form-urlencoded"
        );
        xhr.setRequestHeader("bento-token", bentoToken);
      },
      xhrFields: {
        withCredentials: true,
      },
      crossDomain: true,
      url: dbPath + phpFunction,
      data: { json: JSON.stringify(obj) },
      success: function (response) {
        // 				var responseTime = new Date().getTime()-ajaxTime;
        // 				console.log(responseTime);
        if (getCookie("uid") === "208582" && true == false) {
          if (
            (obj.getChildObjs < 2 && !_.isEmpty(obj_blueprints)) ||
            (obj.getChildObjs === undefined && !_.isEmpty(obj_blueprints))
          ) {
            var cached = {
              data: [],
            };

            if (phpFunction === "getObjectBlueprint") {
              if (obj.getOptions) {
                obj_bp_w_options[obj.objectType] = response;
                cached = response;
              } else {
                obj_blueprints[obj.objectType] = response;
                cached = response;
              }
            } else if (Array.isArray(response)) {
              cached.data = response;
            } else if (
              response !== null &&
              typeof response === "object" &&
              response.hasOwnProperty("data") &&
              Array.isArray(response.data)
            ) {
              cached = response;
            }

            var queryingObjectType = obj.objectType || obj.type;
            cached.childObjTypes = [];
            if (obj.childObjs > 0) {
              for (i in obj_blueprints[queryingObjectType]) {
                if (
                  obj_blueprints[queryingObjectType][i].type === "objectId" ||
                  obj_blueprints[queryingObjectType][i].type === "objectIds"
                ) {
                  cached.childObjTypes.push(
                    obj_blueprints[queryingObjectType][i].objectType
                  );
                }
              }
            }

            db_calls.push({
              response_date: moment(),
              call: phpFunction,
              queryObj: obj,
              response: cached,
            });
          }
        }

        Factory.triggerEvent(
          {
            type: "db-call-completed",
            data: {
              call: phpFunction,
              queryObj: obj,
              data: deepClone(response),
              timestamp: callTimestamp,
            },
          },
          "db"
        );

        callback(deepClone(response));
      },
      error: function (jqXHR, status, error) {
        if (jqXHR.status == 404) {
          if (typeof i === "undefined") {
            i = 1;
          }
          i++;
          if (i <= 6) {
            setTimeout(function () {
              get(phpFunction, obj, callback, i);
            }, i * i * 100);
          } else {
            Factory.triggerEvent(
              {
                type: "db-call-completed",
                data: {
                  call: phpFunction,
                  queryObj: obj,
                  data: jqXHR,
                  error: error,
                  status: status,
                  timestamp: callTimestamp,
                },
              },
              "db"
            );

            alerts.alert(
              "Error!",
              "Bad connection--try refreshing your page.",
              "error"
            );
          }
        }
      },
    });
  }

  // takes a {} object and returns a FormData object
  function objectToFormData(obj, form, namespace) {
    var fd = form || new FormData();
    var formKey;

    for (var property in obj) {
      if (obj.hasOwnProperty(property)) {
        if (namespace) {
          formKey = namespace + "[" + property + "]";
        } else {
          formKey = property;
        }

        // if the property is an object, but not a File,
        // use recursivity.
        if (
          typeof obj[property] === "object" &&
          !(obj[property] instanceof File)
        ) {
          objectToFormData(obj[property], fd, formKey);
        } else {
          // if it's a string or a File object
          fd.append(formKey, obj[property]);
        }
      }
    }

    return fd;
  }

  function upload(phpFunction, file, callback, i) {
    // usage example
    var z = objectToFormData(file);

    var xhr = new XMLHttpRequest();
    xhr.open("POST", postPath + phpFunction, true);
    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
        callback(JSON.parse(xhr.responseText));
      }
    };

    xhr.send(z);
  }

  function submitForm(phpFunction, formSelector, callback) {
    $.ajax({
      type: "post",
      url: rootPath + phpFunction,
      type: "post",
      data: $(formSelector).serialize(),
      success: function (ret) {
        callback(ret);
      },
    });
  }

  function updateObject(object, callback) {
    get("updateObject", object, function (response) {
      callback(response);
    });
  }

  function usePost(objectData, objectType, childObjs) {
    var ret = false,
      fileData,
      fileKey;

    _.each(objectData, function (datum, key) {
      if (
        datum !== null &&
        typeof datum === "object" &&
        datum.hasOwnProperty("fileData")
      ) {
        fileData = datum;
        fileKey = key;
      }
    });

    if (fileData) {
      ret = new FormData();

      if (fileData) {
        ret.append("file", fileData.fileData);
        ret.append("sys-file-prop-key", fileKey);

        ret.append("sys-obj-type", objectType);
      }

      objectData.file = fileData.fileData;
      objectData["sys-file-prop-key"] = fileKey;
      objectData["sys-obj-type"] = objectType;

      _.each(objectData, function (val, key) {
        ret.append(key, val);
      });

      _.each(fileData, function (val, key) {
        if (key !== "fileData") {
          ret.append("file-" + key, val);
          objectData["file-" + key] = val;
        }
      });

      if (childObjs) {
        ret.append("getChildObjs", childObjs);
        objectData["getChildObjs"] = childObjs;
      }
    }

    return ret;
  }

  return {
    controller: function (functionName, obj, callback, root) {
      get(
        functionName,
        obj,
        function (response) {
          callback(response);
        },
        false,
        false,
        root,
        true
      );
    },

    controllerForm: function (functionName, formSelector, callback) {
      submitForm(functionName, formSelector, function (response) {
        callback(response);
      });
    },

    blueprint: (function () {
      return {
        create: function (objectInfo, blueprint, callback) {
          get(
            "createObjectBlueprint",
            { objectInfo: objectInfo, blueprint: blueprint },
            function (blueprintObj) {
              callback(blueprintObj);
            }
          );
        },

        get: function () {},

        getAll: function (callback) {
          get("getObjectBlueprints", {}, function (blueprints) {
            callback(blueprints);
          });
        },

        update: function (object, callback) {
          get("updateBlueprint", { objectData: object }, function (response) {
            callback(response);
          });
        },
      };
    })(),

    createEventInvoice: function (invoiceObj, callback) {
      get("createEventInvoice", invoiceObj, function (response) {
        callback(response);
      });
    },

    createNewPassword: function (callback, password) {
      if (!password) {
        password = null;
      }

      get("createNewPassword", { pwd: password }, function (response) {
        callback(response);
      });
    },

    getEventInvoice: function (invoiceId, callback) {
      var requestObj = {
        invoiceId: invoiceId,
      };

      get("getEventInvoice", requestObj, function (response) {
        callback(response);
      });
    },

    getEventInvoices: function (eventId, callback) {
      var requestObj = {
        eid: eventId,
      };

      get("getEventInvoices", requestObj, function (response) {
        callback(response);
      });
    },

    getEventInvoiceString: function (invoiceId, callback) {
      var requestObj = {
        invoiceId: invoiceId,
      };

      get("createEventInvoicePDF", requestObj, function (response) {
        callback(response);
      });
    },

    loginUser: function (loginObj, callback) {
      var adminPath =
        appConfig.db.write.replace("_get.php", "_getAdmin.php") +
        "pagodaAPIKey=" +
        appConfig.instance +
        "&do=";

      get(
        "checkLoginCreds&api_webform=1",
        loginObj,
        function (response) {
          callback(response);
        },
        false,
        false,
        adminPath
      );
    },

    notes: (function () {
      return {
        getFeed: function (queryObj, callback) {
          get("getObjNoteFeed", { queryObj: queryObj }, function (response) {
            callback(response);
          });
        },
      };
    })(),

    obj: (function () {
      var systemObjects = [];

      function addToObjectList(objType) {
        if (_.indexOf(systemObjects, objType) == -1) {
          systemObjects.push(objType);
        }
      }

      return {
        create: function (type, object, callback, childObjs) {
          if (object.length > 0) {
            var requestObj = {
              objectType: type,
              objectData: object,
              multiple: 1,
            };
          } else {
            var requestObj = {
              objectType: type,
              objectData: object,
            };
          }

          if (childObjs) {
            requestObj.childObjs = childObjs;
          }

          if (usePost(object, type, childObjs)) {
            upload("create", object, function (response) {
              callback(response);
            });
          } else {
            get(
              "createNewObject",
              requestObj,
              function (response) {
                callback(response);
              },
              undefined,
              undefined,
              undefined,
              true
            );
          }
        },

        erase: function (type, typeId, callback) {
          //console.log(this.parentModule, this.permission);
          /*
if(_.indexOf(this.permission.erase, Sandbox.create(Factory, 'databaseConnection').data.cookie.get('uid')) > -1){
						
						get('deleteObject', {type: type, id: typeId}, function(response){
						
							callback(response);
							
						});
						
					}else{
						
						Sandbox.create(Factory, 'databaseConnection').dom.alerts.alert('Permission Denied', 'You don\'t have permission to delete this item.', 'error');
						
						callback(false);
						
					}
*/

          get(
            "deleteObject",
            { type: type, id: typeId },
            function (response) {
              callback(response);
            },
            undefined,
            undefined,
            undefined,
            true
          );
        },

        getById: function (type, value, callback, childObjs) {
          addToObjectList(type);
          var queryObj = { type: type, value: value };

          if (childObjs) {
            queryObj.childObjs = childObjs;
          }

          get("getObjectById", queryObj, function (response) {
            callback(response);
          });
        },

        getAll: function (objectType, callback, getChildObjs, paged) {
          addToObjectList(objectType);

          if (paged) {
            paged.paged = true;
          }

          get(
            "getAllObjects",
            {
              objectType: objectType,
              getChildObjs: getChildObjs,
              paged: paged,
            },
            function (response) {
              callback(response);
            }
          );
        },

        getBlueprint: function (type, callback, getOptions, getSetObj) {
          var requestObj = {
            objectType: type,
          };

          if (getOptions) {
            requestObj.getOptions = getOptions;
          }

          if (getSetObj) {
            requestObj.getSetObj = getSetObj;
          }

          get("getObjectBlueprint", requestObj, function (blueprint) {
            callback(blueprint);
          });
        },

        getGroupSum: function (objectType, field, queryObj, callback) {
          get(
            "groupSum",
            { objectType: objectType, field: field, queryObj: queryObj },
            function (response) {
              callback(response);
            }
          );
        },

        getOptions: function (objectType, field, callback) {
          get(
            "getOptions",
            { objectType: objectType, field: field },
            function (response) {
              callback(response);
            }
          );
        },

        getStaffAccessLevels: function (callback) {
          get("getStaffAccessLevels", {}, function (response) {
            callback(response);
          });
        },

        getStaffDepartments: function (callback) {
          get("getStaffDepartments", {}, function (response) {
            callback(response);
          });
        },

        getInstanceObjects: function () {
          return systemObjects;
        },

        getSum: function (objectType, field, queryObj, callback) {
          get(
            "sum",
            { objectType: objectType, field: field, queryObj: queryObj },
            function (response) {
              callback(response);
            }
          );
        },

        getWhere: function (type, queryObj, callback) {
          // alias for childObjs
          if (queryObj.hasOwnProperty("select")) {
            queryObj.childObjs = queryObj.select;
            delete queryObj.select;
          }

          if (typeof queryObj.childObjs === "undefined") {
            childObjs = 0;
          } else {
            childObjs = queryObj.childObjs;
            delete queryObj.childObjs;
          }

          var getJust = {};
          if (queryObj.hasOwnProperty("getJust")) {
            getJust = queryObj.getJust;
            delete queryObj.getJust;
          }

          addToObjectList(type);

          get(
            "getObjectsWhere",
            {
              objectType: type,
              queryObj: queryObj,
              getChildObjs: childObjs,
              getJust: getJust,
            },
            function (response) {
              callback(response);
            }
          );
        },

        getWhereBy: function (type, queryObj, callback) {
          if (typeof queryObj.childObjs === "undefined") {
            childObjs = 0;
          } else {
            childObjs = queryObj.childObjs;
            delete queryObj.childObjs;
          }

          addToObjectList(type);

          get(
            "getObjectsWhereBy",
            { objectType: type, queryObj: queryObj, getChildObjs: childObjs },
            function (response) {
              callback(response);
            }
          );
        },

        getWhereWith: function (
          objectType,
          queryObj,
          childObj,
          childObjKey,
          callback
        ) {
          if (typeof queryObj.childObjs === "undefined") {
            childObjs = 0;
          } else {
            childObjs = queryObj.childObjs;
            delete queryObj.childObjs;
          }

          addToObjectList(objectType);

          get(
            "getObjectsWhereWith",
            {
              objectType: objectType,
              queryObj: queryObj,
              childObj: childObj,
              childObjKey: childObjKey,
              getChildObjs: childObjs,
            },
            function (response) {
              callback(response);
            }
          );
        },

        parentModule: "",

        permission: {},

        restore: function (type, typeId, callback) {
          get("restoreObjs", { type: type, id: typeId }, function (response) {
            callback(response);
          });
        },

        save: function (saveData, callback, childObjs) {
          if (childObjs === undefined) {
            childObjs = 0;
          }

          get(
            "saveObjects",
            { saveData: saveData, childObjs: childObjs },
            function (response) {
              callback(response);
            },
            undefined,
            undefined,
            undefined,
            true
          );
        },

        setModuleId: function (modId) {
          this.parentModule = modId;

          var permission = _.where(appConfig.permissions, {
            pageModuleId: this.parentModule,
          });

          if (permission.length > 0) {
            this.permission = permission;
          }
        },

        update: function (type, object, callback, childObjs) {
          //console.log(this.parentModule, this.permission);
          /*
if(_.indexOf(this.permission.edit, Sandbox.create(Factory, 'databaseConnection').data.cookie.get('uid')) > -1 || _.isEmpty(this.permission)){
					
						var requestObj = {
							objectType: type,
							objectData: object
						};
						
						get('updateObject', requestObj, function(response){
							
							callback(response);
							
						});
						
					}else{
						
						Sandbox.create(Factory, 'databaseConnection').dom.alerts.alert('Permission Denied', 'You don\'t have permission to edit this item.', 'error');
						
						callback(false);
						
					}
*/

          // if object type is not provided, args are shifted forward
          if (typeof type === "object" && typeof object === "function") {
            callback = object;
            object = _.clone(type);
            type = "";
          }

          var requestObj = {
            objectType: type,
            objectData: object,
          };

          if (object.getChildObjs) {
            requestObj.getChildObjs = object.getChildObjs;
          }
          if (childObjs !== undefined) {
            requestObj.getChildObjs = childObjs;
          }

          if (usePost(object, type, childObjs)) {
            // 						var fileData = usePost(object, type);

            upload("update", object, function (response) {
              callback(response);
            });
          } else {
            get(
              "updateObject",
              requestObj,
              function (response) {
                callback(response);
              },
              undefined,
              undefined,
              undefined,
              true
            );
          }
        },

        updateState: function (update, callback) {
          var request = {
            objectId: parseInt(update.objectId),
            newState: parseInt(update.newState),
            link: update.link,
          };

          if (!_.isEmpty(update.stateProperty)) {
            request.stateProperty = update.stateProperty;
          }

          if (update.isRecurring) {
            request.isRecurring = 1;
            request.between = {
              start: update.between.start,
              end: update.between.end,
            };
          }

          get(
            "updateState",
            request,
            function (response) {
              callback(response);
            },
            undefined,
            undefined,
            undefined,
            true
          );
        },
      };
    })(),

    setAppConfig: function (newConfig) {
      appConfig = newConfig;

      if (appConfig.hasOwnProperty("api_webform")) {
        rootPath =
          appConfig.db.write +
          "pagodaAPIKey=" +
          appConfig.instance +
          "&api_webform=true&do=";
        postPath =
          appConfig.db.post +
          "pagodaAPIKey=" +
          appConfig.instance +
          "&api_webform=true&do=";
      } else {
        rootPath =
          appConfig.db.write + "pagodaAPIKey=" + appConfig.instance + "&do=";
        postPath =
          appConfig.db.post + "pagodaAPIKey=" + appConfig.instance + "&do=";
      }
    },

    setToken: function (token) {
      bentoToken = token;
    },

    test: function () {
      get("test", {}, function (response) {
        console.log(response);
      });
    },
  };
})();
