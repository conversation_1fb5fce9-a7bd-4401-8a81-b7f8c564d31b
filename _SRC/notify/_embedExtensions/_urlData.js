var urlData = (function(){
	
	var currentHistoryIndex = 0,
		navHistory = [],
		hashHistory = [],
		shouldIndex = false;
	
	function getPage(){
		
		var page =  window.location.href.split('#')[1];
		
		if(page){
			page = page.substr(0, page.indexOf('&')); 
		}else{
			page = false;
		}

		return page;

	}

	function getParams(){
		
		var query = window.location.href.split('&'),
			params = {};
			
		query.shift();
			
		_.each(query, function(pair){
			
			var split = pair.split('=');
			if (split[1]) {
				
				params[pair.split('=')[0]] = decodeURIComponent(split[1].split('%d%').join('HYPHEN'));
				params[pair.split('=')[0]] = params[split[0]].split('HYPHEN').join('%d%');	
				
			}			

		});
		
		return params;
		
	}
	
	function move(move){
		
		switch(move){
			
			case 'forward':
			currentHistoryIndex++;
			break;
			
			case 'back':
			currentHistoryIndex = currentHistoryIndex - 1;
			break;
			
		}
		
		window.location.hash = hashHistory[currentHistoryIndex];
		
	}
	
	function updateHistory(){
		
		hashHistory.push(window.location.hash);

	}
	
	function checkURLParams(data, newBreadcrumb) {

		if (!_.isEmpty(data.params)) {
								
			newBreadcrumb += '-';
			
			_.each(data.params, function (v, k) {
				
				newBreadcrumb += '!'+ k +':'+ v;
				
			});
			
		}

		return newBreadcrumb;
		
	}
	
	return {
		
		createPageURL: function(page, params){
			
			shouldIndex = ( Factory.logState(true).components.hasOwnProperty('breadcrumbs') );
			
			if(shouldIndex){

				var current = window.location.href.split('#')[1];
				var indexes = current.split('&');
				var nextIndex = 1;
				var newBreadcrumb = '';

				if (params && params.startAt) {
					indexes = params.startAt.split('&');
				}

				if(Array.isArray(indexes)){
					nextIndex = indexes.length;
				}
				
				var type = page;
				var data = params;


				switch(type){
					
					case 'UP':
						var ret =  window.location.href
							.split('&');
							
						ret.pop(-1, 1);
						return ret.join('&');
					
					case 'hqTool':
					case 'hqtools':
						newBreadcrumb = 'hqt-'+ data.tool;
						if (data.name) {
							newBreadcrumb += '-'+ encodeURIComponent(data.name.split('-').join('–'));
						}
						newBreadcrumb = checkURLParams(data, newBreadcrumb);
						break;
					
					case 'teamTool':
					case 'team-tools':
						newBreadcrumb = 'tt-'+ data.tool;
						if (data.name && data.toolId) {
							newBreadcrumb += '-'+ data.toolId +'-'+ encodeURIComponent(data.name.split('-').join('–'));
						} else if (data.name) {
							newBreadcrumb += '-'+ encodeURIComponent(data.name.split('-').join('–'));
						}
						newBreadcrumb = checkURLParams(data, newBreadcrumb);
						break;
						
					case 'jobTypeTool':
					case 'job-type-tools':
						newBreadcrumb = 'jt-'+ data.tool;
						newBreadcrumb = checkURLParams(data, newBreadcrumb);
						break;						
					
					case 'myStuffTool':
						newBreadcrumb = 'mst-'+ data.tool;
						if (data.name && data.toolId) {
							newBreadcrumb += '-'+ data.toolId +'-'+ encodeURIComponent(data.name.split('-').join('–'));
						} else if (data.name) {
							newBreadcrumb += '-'+ encodeURIComponent(data.name.split('-').join('–'));
						}
						newBreadcrumb = checkURLParams(data, newBreadcrumb);
						break;

					case 'project-tools':
						newBreadcrumb = 'pt-'+ data.tool;
						if (data.name) {
							newBreadcrumb += '-'+ encodeURIComponent(data.name.split('-').join('–'));
						}
						newBreadcrumb = checkURLParams(data, newBreadcrumb);
						break;
					
					case 'object-view':
					case 'object':
						var currentType = data.type;
						var objName = data.name;
						if (!_.isEmpty(objName)) {
							objName = objName.split('-').join('–');
							objName = encodeURIComponent(objName);
						}
						
						if (
							!_.isEmpty(currentType)
							&& currentType[0] === '#'
						) {
							
							newBreadcrumb = 'e-'+ data.id +'-'+ objName;
							newBreadcrumb = checkURLParams(data, newBreadcrumb);
							
						} else {
							
							if (
								data.type === 'groups' 
								&& data.hasOwnProperty('group_type')
								&& typeof data.group_type === 'string'
							) {
	
								currentType = data.group_type.toLowerCase();
	
								if(currentType !== '' || currentType !== null) {
	
									if(currentType.toLowerCase() === 'headquarters') {
										
										return window.location.href.split('#')[0] + '#hq';
										
									}	
									
								}
								
							} 
	
							if(data.edit){
								newBreadcrumb = 'o-'+ currentType +'-e'+'-'+ data.id +'-'+ objName;
							}else if(data.create){
								newBreadcrumb = 'o-'+ currentType +'-c';
							}else{
								newBreadcrumb = 'o-'+ currentType +'-'+ data.id +'-'+ objName;
							}	
							
							newBreadcrumb = checkURLParams(data, newBreadcrumb);
							
						}
						break;
						
					case 'custom':
						newBreadcrumb = data.id +'-'+ data.name;
						newBreadcrumb = checkURLParams(data, newBreadcrumb);
						break;
						
				}
				
				if (
					window.location.href.indexOf(newBreadcrumb.split(' ').join('%20')) > -1
					&& page !== 'teamTool'
				) {
					
					var indexToUse = window.location.href.split(newBreadcrumb.split(' ').join('%20'))[0]
										.split('&').length - 1;
					
					var upstream = window.location.href.split(newBreadcrumb.split(' ').join('%20'))[0]
									.split('&');
									
					var newBase = '';
					
					_.each(upstream, function(breadcrumb, i){
						
						if (i < upstream.length - 1) {
							
							if (i > 0) {
								newBase += '&';
							}
							
							newBase += breadcrumb;
						}
						
					});
					
					return newBase +'&'+ indexToUse +'='+ newBreadcrumb;
					
				}
				
				if (data.startAt) {
					return data.startAt +'&'+ nextIndex +'='+ newBreadcrumb;
				} else {
					return window.location.href +'&'+ nextIndex +'='+ newBreadcrumb;
				}
				
				
			}else if (page === 'object') {
				
				var url = window.location.href.split('&')[0];
				url += '&page=single-'+ params.id +'&view=table&single='+ params.id;
				return url;
				
			}else{
								
				var url = window.location.href.split('#')[0];
			
				url += '#'+ page +'?';
				_.each(params, function(val, key){
					
					if(typeof val !== 'undefined' && typeof key !== 'undefined' && key !== 'undefined' && key !== '' && val !== 'undefined' && val !== ''){
						url += '&'+ key +'='+ val;
					}
					
				});
				
				var queryString = '';
				var baseUrl = window.location.href.split('#')[0];
				
				_.each(params, function(val, key){
						
					if(typeof val !== 'undefined' && typeof key !== 'undefined' && key !== 'undefined' && key !== '' && val !== 'undefined' && val !== ''){
						queryString += '&'+ key +'='+ val;
					}
					
				});
	
				return baseUrl + '#'+ page + queryString;
				
			}
			
		},
		
		get: function(paramName){
			
			return getParams();
			
		},
		
		pageChange: function(callback){
			
			window.addEventListener('popstate', function(event) {

				var ret = {
						itemId:getPage(),
						viewId:''
					};
					
				if(event.state){
					if(event.state.view){
						ret.viewId = event.state.view;
					}
				}

				if(ret.viewId == '' && getParams().view){
					ret.viewId = getParams().view;
				}
				
				if(getParams().single){
					ret.single = getParams().single;
				}
				
				if(getParams().contactId){
					ret.single = getParams().contactId;
					ret.viewId = 'table';
				}			
				
				callback(ret);

			});
			
		},
		
		set: function(params){
			
/*
			var currentParams = getParams(),
				queryString = '?',
				url = window.location.href;
			
			_.each(params, function(val, key){
				
				currentParams[key] = val;
				
			});
			
			_.each(currentParams, function(val, key){
				
				queryString += '&'+ key +'='+ val;
				
			});
*/
			
// window.history.pushState(“object or string”, “Title”, “/new-url”);			
// 			window.location.href = url + queryString;
			
		}, 
		
		getPage: function(){
			
			return getPage();
			
		},
		
		getParams: function(){

			return getParams();
			
		},
		
		getObjectPageParams: function(item, options){
			
			var objType = item.object_bp_type;
			if(objType === 'groups' && options.where){
				objType = options.where.group_type.toLowerCase();
			} else if (objType === 'groups' && item.group_type) {
				objType = item.group_type.toLowerCase();
			}

			var objName = item.name;
			switch(objType){
				
				case 'contacts':
				case 'users':
				objName = item.fname +' '+ item.lname;
				break;
				
				case 'tasks':
				objName = item.title;
				break;
					
			}
				
			return this.createPageURL(
					'object', 
					{
						type:objType, 
						id:item.id,
						name:objName
					}
				);
				
		}, 
		
		setPage: function(page, params){

			var queryString = '';

			_.each(params, function(val, key){
					
				if(typeof val !== 'undefined' && typeof key !== 'undefined' && key !== 'undefined' && key !== '' && val !== 'undefined' && val !== ''){
					
					if(key != 'pageTitle'){
						
						queryString += '&'+ key +'='+ val;
						
					}
					
				}
				
			});

			history.pushState(params, page, '#'+ page + queryString);
			
			navHistory.push({
				page:page,
				params:params
			});

			if(params.pageTitle){
				document.title = params.pageTitle;
			}else{
				document.title = page;
			}
			
		}
		
	}
	
})();