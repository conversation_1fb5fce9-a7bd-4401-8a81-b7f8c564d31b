Factory.register('blueprints', function(sb){
	
	var domObj = {},
		currentBlueprint = {
			id: {
				type: 'int',
				name: 'id',
				immutable: true
			}
		},
		currentBlueprintInfo = {},
		currentField = {},
		allBlueprints;
		
	function getObjectInfoForm(){
		
		var infoFormObj = {
			objectType: {
				type: 'text',
				name: 'objectType',
				label: 'Object Type'
			}, accessLevel: {
				type: 'text',
				name: 'accessLevel',
				label: 'Access Level'
			}
			
		};

		if(typeof currentBlueprintInfo.objectType !== 'undefined'){
			infoFormObj.objectType.value = currentBlueprintInfo.objectType;
		}

		
		if(typeof currentBlueprintInfo.accessLevel !== 'undefined'){
			infoFormObj.accessLevel.value = currentBlueprintInfo.accessLevel;
		}
		domObj.newBlueprintModal.body.makeNode('blueprintObjectInfoForm', 'form', infoFormObj);
		
	}
		
	function processNewFieldForm(){
		
		var newFieldInfo = domObj.newBlueprintModal.body.newFieldFormContainer.newFieldForm.process();
		
		_.each(newFieldInfo.fields, function(field, fieldName){
			if(fieldName == 'immutable' && typeof field.value[0] !== 'undefined'){
				currentField['immutable'] = true;
			}else if(typeof field.value !== 'undefined'){
				currentField[fieldName] = field.value;
			}else{
				currentField[fieldName] = '';
			}
		});
		
		if(typeof currentField['immutable'] === 'undefined' || typeof newFieldInfo.fields['immutable'] === 'undefined'){
			currentField['immutable'] = false;
		}
				
		var index;
		currentField.options = {};
		_.each(currentField, function(prop, propName){
			
			if(propName.substring(0, 6) == 'option' && propName !== 'options'){
				
				index = parseInt(propName.split('-')[1]);
				currentField.options[currentField['optionValue-'+ index]] = currentField['optionName-'+ index];
				
			}
			
		});
		
		_.each(currentField, function(prop, propName){
			if(propName.substring(0, 10) == 'optionName' || propName.substring(0, 11) == 'optionValue'){
				delete currentField[propName];
			}
		});
		
		console.log(currentField);
		
	}
	
	function processObjectData(){
		
		var data = domObj.newBlueprintModal.body.blueprintObjectInfoForm.process().fields;

		currentBlueprintInfo['objectType'] = data['objectType'].value;
		currentBlueprintInfo['accessLevel'] = data['accessLevel'].value;

	}
	
	return {
		
		addFieldToBlueprint: function(){
			
			processNewFieldForm();
			processObjectData();
			
			// validate new field
			if(typeof currentField.dbColumnName === 'undefined' || typeof currentField.name === 'undefined' || typeof currentField.type === 'undefined' || currentField.dbColumnName == '' || currentField.name == ''){

				sb.dom.alerts.alert('Nope!', 'Please complete the form.', 'warning');

			}else if(currentField.type == 'select' && currentField.options.length < 1){
				
				sb.dom.alerts.alert('Nope!', 'You must supply options for "select"-type fields.', 'warning');
				
			}else{
				
				currentBlueprint[currentField.dbColumnName] = {
					name: currentField.name,
					type: currentField.type,
					immutable: currentField.immutable
				};
				
				// if this field has custom option types, set those options
				if(currentField.type == 'select' || currentField.type == 'multi-select'){
					currentBlueprint[currentField.dbColumnName].options = currentField.options;
				}
				
				// if this field is a reference to another object type, set objectType field
				if(currentField.type == 'objectId' || currentField.type == 'objectIds'){
					currentBlueprint[currentField.dbColumnName].objectType = currentField.objectType;
				}

				currentField = {};
				sb.notify({
					type: 'add-new-blueprint-field-clicked',
					data: {}
				});
			}
			
		},
		
		changeCurrentField: function(data){

			currentField = currentBlueprint[data.dataId];
			currentField.dbColumnName = data.dataId;
			sb.notify({
				type: 'add-new-blueprint-field-clicked',
				data: {}
			});
			
		},
		
		createNewBlueprint: function(){
			
			domObj.newBlueprintModal.footer.createNewBlueprintButton.loading();
			if(!_.isEmpty(currentBlueprint) && !_.isEmpty(currentBlueprintInfo)){
				
				sb.data.db.blueprint.create(currentBlueprintInfo, currentBlueprint, function(createdBlueprint){
					
					if(createdBlueprint){
						
						currentBlueprint = {
							id: {
								type: 'int',
								name: 'id',
								immutable: true
							}
						};
						
						domObj.newBlueprintModal.hide();
						sb.data.db.blueprint.getAll(function(blueprints){
							
							allBlueprints = blueprints;
							sb.notify({
								type: 'blueprints-component-load',
								data: {}
							});
							
						});
						
					} else {
						
						alert('error');

						domObj.newBlueprintModal.footer.createNewBlueprintButton.loading(false);
						
					}
					
				});
				
			}
			
		},
		
		init: function(){
			
			sb.listen({
				'blueprints-component-load': this.load,
				'new-blueprint-button-clicked': this.getNewBlueprintModal,
				'add-new-blueprint-field-clicked': this.getAddNewBlueprintFieldForm,
				'add-field-to-blueprint-button-clicked': this.addFieldToBlueprint,
				'change-current-blueprint-button-clicked': this.changeCurrentField,
				'create-new-blueprint-clicked': this.createNewBlueprint,
				'field-type-switch': this.fieldTypeSwitch,
				'remove-field-button-clicked': this.removeFieldFromCurrentBlueprint
			});
			
		},
		
		load: function(setup){

			// if initial load
			if(_.isEmpty(domObj)){
				
				domObj = sb.dom.make(setup.domObj.selector);
				allBlueprints = _.groupBy(setup.blueprints, 'object_type');
								
			}
			
			// make heading
			domObj.makeNode('heading', 'headerText', {text: 'Object Blueprints', size: 'medium'});
			domObj.heading.makeNode('newBlueprintButton', 'button', {text: '<i class="glyphicon glyphicon-plus"></i> new blueprint', css: 'pull-right btn-success'});
			domObj.heading.newBlueprintButton.notify('click', {
				type: 'new-blueprint-button-clicked',
				data: {}
			}, sb.moduleId);
			
			// make blueprints table
			domObj.makeNode('blueprintsTable', 'table', {columns: {objectType: 'Object Type', blueprints: 'Blueprints'}});

			_.each(allBlueprints, function(object, objectType){
				
				domObj.blueprintsTable.makeRow(objectType, [objectType, '']);
				
				_.each(object, function(blueprint){
					
					domObj.blueprintsTable.body[objectType].blueprints.makeNode('access-level-'+ blueprint.access_level +'-button', 'button', {text: blueprint.access_level +' <i class="glyphicon glyphicon-lock"></i>'});
					
				});

			});

			domObj.build();
			
		},
		
		getNewBlueprintModal: function(){
			
			domObj.makeNode('newBlueprintModal', 'modal', {size: 'large'});
			domObj.newBlueprintModal.body.makeNode('header', 'headerText', {text: 'Create New Blueprint', size: 'small', css: 'text-center'});
			
			getObjectInfoForm();
			
			domObj.newBlueprintModal.body.makeNode('fieldContainer', 'columns', {width: 6});
// 			domObj.newBlueprintModal.body.makeNode('break', 'lineBreak', {spacing: 2});
			
			domObj.newBlueprintModal.body.makeNode('newFieldFormContainer', 'columns', {width: 6, css: 'bg-warning'});
			
			domObj.newBlueprintModal.body.makeNode('newFieldButtonContainer', 'container', {css: 'text-center'});
			domObj.newBlueprintModal.body.newFieldButtonContainer.makeNode('newFieldButton', 'button', {text: '<i class="glyphicon glyphicon-plus"></i> new property', css: 'btn-success'});
			domObj.newBlueprintModal.body.newFieldButtonContainer.newFieldButton.notify('click', {
				type: 'add-new-blueprint-field-clicked',
				data: {}
			}, sb.moduleId);
			
			domObj.newBlueprintModal.footer.makeNode('createNewBlueprintButton', 'button', {css: 'pull-right btn-success', text: 'Create'});
			domObj.newBlueprintModal.footer.createNewBlueprintButton.notify('click', {
				type: 'create-new-blueprint-clicked',
				data: {}
			}, sb.moduleId);
			
			domObj.build();
			domObj.newBlueprintModal.show();
			
		},
		
		getAddNewBlueprintFieldForm: function(){
			
			processObjectData();
			delete domObj.newBlueprintModal.body.newFieldButtonContainer.newFieldButton;
			
			domObj.newBlueprintModal.body.makeNode('header', 'headerText', {text: 'Create New Blueprint', size: 'small', css: 'text-center'});
			
			getObjectInfoForm();
			var optionsText = '';
			_.each(currentBlueprint, function(field, fieldDBName){
				
				domObj.newBlueprintModal.body.fieldContainer.makeNode('con-'+ fieldDBName, 'columns', {width: 12, css: 'bg-info'});
				domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].makeNode('name', 'headerText', {size: 'x-small', text: field.name});
				
				if(fieldDBName !== 'id'){
					domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].name.makeNode('buttons', 'buttonGroup', {css: 'pull-right'});
					
					domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].name.buttons.makeNode('editButton', 'button', {text: '<i class="glyphicon glyphicon-edit"></i>', dataId: fieldDBName, css: 'btn-warning'});
					domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].name.buttons.editButton.notify('click', {type: 'change-current-blueprint-button-clicked', data: {}}, sb.moduleId);
					
					domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].name.buttons.makeNode('deleteButton', 'button', {text: '<i class="glyphicon glyphicon-remove"></i>', dataId: fieldDBName, css: 'btn-danger'});
					domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].name.buttons.deleteButton.notify('click', {type: 'remove-field-button-clicked', data: {}}, sb.moduleId);

				}
				
				domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].makeNode('type', 'text', {text: '<label>Type: </label> '+ field.type});
				domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].makeNode('dbColumnName', 'text', {text: '<label>DB Column Name: </label> '+ fieldDBName});
				domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].makeNode('immutable', 'text', {text: '<label>Immutable: </label> '+ field.immutable});
				
				if(field.type == 'select'){
					
					_.each(field.options, function(option, optionValue){
						optionsText = option +' = '+ optionValue +'<br />';
					});
					domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].makeNode('type', 'text', {text: '<label>Options: </label><br />'+ optionsText});
					
				}else if(field.type == 'objectId' || field.type == 'objectIds'){
					domObj.newBlueprintModal.body.fieldContainer['con-'+ fieldDBName].makeNode('objectType', 'text', {text: '<label>Reference Object Type: </label> '+ field.objectType});
				}
				
				domObj.newBlueprintModal.body.fieldContainer.makeNode('break-'+ fieldDBName, 'lineBreak', {spacing: 2});
				
			});
			
			var formSetup = {
				dbColumnName: {
					type: 'text',
					label: 'DB Column Name',
					name: 'dbColumnName'
				}, name: {
					type: 'text',
					label: 'Property Name',
					name: 'name'
					
				}, type: {
					type: 'select',
					label: 'Input Type',
					name: 'type',
					options: [{
						name: 'Int',
						value: 'int'
					}, {
						name: 'Select',
						value: 'select'
					}, {
						name: 'Multi-select',
						value: 'multi-select'
					}, {
						name: 'String',
						value: 'string'
					}, {
						name: 'Object Id',
						value: 'objectId'
					}, {
						name: 'Object Ids',
						value: 'objectIds'
					}]
				}, immutable: {
					type: 'checkbox',
					label: 'Immutable',
					name: 'immutable',
					options: [{
						name: 'immutable',
						value: 'immutable',
						label: 'Is Immutable?'
					}]
				}
			};
			
			if(!_.isEmpty(currentField)){
				
				formSetup.dbColumnName.value = currentField.dbColumnName;
				formSetup.name.value = currentField.name;
				
				var i=0;
				_.each(formSetup.type.options, function(option){
					
					if(option.value == currentField.type){
						formSetup.type.options[i].selected = true;
					}
					i++;
				});

				if(currentField.immutable){
					formSetup.immutable.options[0].checked = true;
				}
			}
			
			if(currentField.type == 'select' || currentField.type == 'multi-select'){
								
				i=0;
				if(typeof currentField.options !== 'undefined'){
					_.each(currentField.options, function(option){
						
						formSetup['selectBoxOption'+ i] = {
							type: 'section',
							label: 'Option '+ i,
							name: 'option'+ i,
							fields: {}
						}
						
						formSetup['selectBoxOption'+ i].fields['optionName-'+ i] = {
							type: 'text',
							label: 'Option '+ i +' Name',
							name: 'optionName-'+ i,
							value: currentField.options[Object.keys(currentField.options)[i]]
						}
						
						formSetup['selectBoxOption'+ i].fields['optionValue-'+ i] = {
							type: 'text',
							label: 'Option '+ i +' Value',
							name: 'optionValue-'+ i,
							value: Object.keys(currentField.options)[i]
						}
						i++;
					});
				}
				
				formSetup['selectBoxOption'+ i] = {
					type: 'section',
					label: 'Option '+ i,
					name: 'option'+ i,
					fields: {}
				}
				
				formSetup['selectBoxOption'+ i].fields['optionName-'+ i] = {
					type: 'text',
					label: 'Option '+ i +' Name',
					name: 'optionName-'+ i
				}
				
				formSetup['selectBoxOption'+ i].fields['optionValue-'+ i] = {
					type: 'text',
					label: 'Option '+ i +' Value',
					name: 'optionValue-'+ i
				}
				
			}else if(currentField.type == 'objectId' || currentField.type == 'objectIds'){
				
				formSetup.objectType = {
					type: 'select',
					label: 'Reference Object Type',
					name: 'objectType',
					options: []
				};
				
				_.each(allBlueprints, function(object, objectType){
					formSetup.objectType.options.push({
						name: objectType,
						value: objectType
					});
				});
				
				formSetup.selectDisplay = {
					type: 'text',
					label: 'Display Select As ([refObject columnName] text text text [another refObject columnName])',
					name: 'selectDisplay',
				};
				
			}
			
			domObj.newBlueprintModal.body.newFieldFormContainer.makeNode('newFieldForm', 'form', formSetup);
			
			if(currentField.type == 'select' || currentField.type == 'multi-select'){
				domObj.newBlueprintModal.body.newFieldFormContainer.makeNode('newOptionButton', 'button', {text: '<i class="glyphicon glyphicon-plus"></i> option'});
				domObj.newBlueprintModal.body.newFieldFormContainer.newOptionButton.notify('click', {
					type: 'field-type-switch',
					data: {}
				}, sb.moduleId);
			}
			
			domObj.newBlueprintModal.body.newFieldFormContainer.newFieldForm.type.notify('click', {
				type: 'field-type-switch',
				data: {}
			}, sb.moduleId);
			
			domObj.newBlueprintModal.body.newFieldFormContainer.makeNode('addFieldToBlueprintButton', 'button', {text: 'update', css: 'pull-right btn-success'});
			domObj.newBlueprintModal.body.newFieldFormContainer.addFieldToBlueprintButton.notify('click', {
				type: 'add-field-to-blueprint-button-clicked',
				data: {}
			}, sb.moduleId);
			
			domObj.newBlueprintModal.body.newFieldFormContainer.makeNode('endBreak', 'lineBreak', {spacing: 2});
			domObj.newBlueprintModal.body.makeNode('endBreak', 'lineBreak', {spacing: 2});
			
			domObj.newBlueprintModal.body.patch();
			
		},
		
		fieldTypeSwitch: function(){
			
			processObjectData();
			processNewFieldForm();
			sb.notify({
				type: 'add-new-blueprint-field-clicked',
				data: {}
			});
			
		},
		
		removeFieldFromCurrentBlueprint: function(data){

			delete currentBlueprint[data.dataId];
			delete domObj.newBlueprintModal.body.fieldContainer['con-'+ data.dataId];
			delete domObj.newBlueprintModal.body.fieldContainer['break-'+ data.dataId];
			
			sb.notify({
				type: 'add-new-blueprint-field-clicked',
				data: {}
			});
			
		}
		
	}
	
});