// Version A001
// IndexedDB

Factory.register('indexedDB-service-worker', function(sb) {
	
	var DB = undefined;
	
	function addData(data, callback, storeName) {
			
		var tx = {};
		var store = {};
		
		if (storeName !== undefined) {
			
			tx = DB.transaction(storeName, 'readwrite');
			store = tx.objectStore(storeName);
			
		} else {
			
			tx = DB.transaction('viewCache', 'readwrite');
			store = tx.objectStore('viewCache');
			
		}
		
		tx.onerror = function(e) {
			
			console.log('e:: ', e.target.error);
			
		}
		
		store.add(data);
		
		if (callback) {
			
			callback(data);	
			
		}
		
	}
	
	function openDB(onComplete) {
		
		var db_name = 'bento_cache_db';
		var request = window.indexedDB.open(db_name, 1);

		// setting event handlers
	
		request.onupgradeneeded = function(e) {
			
			DB = e.target.result;
			
			var mainStore = DB.createObjectStore('viewCache', {keyPath: 'id'});
			
		};
		
		request.onsuccess = function(e) {

			DB = e.target.result;
			
			onComplete();
			
		}
		
		request.onerror = function(e) {

			console.log('indexedDB Error:: ', e.target.error);
				
		};
		
		
		return true;
		
	}
	
	function removeData(id, callback, storeName) {
			
		var tx = {};
		var store = {};
		
		if (storeName !== undefined) {
			
			tx = DB.transaction(storeName, 'readwrite');
			store = tx.objectStore(storeName);
			
		} else {
			
			tx = DB.transaction('viewCache', 'readwrite');
			store = tx.objectStore('viewCache');
			
		}
		
		tx.onerror = function(e) {
			
			console.log('e:: ', e.target.error);
			
		}
		
		var request = store.delete(id);
		
		request.onsuccess = function(e) {
			
			if (callback) {
			
				callback(true);	
				
			}	
			
		}
		
	}
	
	function updateData(id, callback, storeName) {
			
		var tx = {};
		var store = {};
		var request = {};
		
		if (storeName !== undefined) {
			
			tx = DB.transaction(storeName, 'readwrite');
			store = tx.objectStore(storeName);
			
		} else {
			
			tx = DB.transaction('viewCache', 'readwrite');
			store = tx.objectStore('viewCache');
			
		}
		
		request = store.get(id);
		
		request.onsuccess = function(e) {
			
			var data = e.target.result;
			
			callback(data, function(modifiedData) {
				
				store.put(modifiedData);
				
			});
			
		}
		
	}
	
	function viewData(callback, id, storeName) {
			
		var tx = {};
		var store = {};
		var request = {};
		
		if (storeName !== undefined) {
			
			tx = DB.transaction(storeName, 'readonly');
			store = tx.objectStore(storeName);
			
		} else {
			
			tx = DB.transaction('viewCache', 'readonly');
			store = tx.objectStore('viewCache');
			
		}
		
		if (id !== undefined) {
			
			request = store.get(id);
			
		} else {
			
			request = store.getAll();
			
		}

		request.onsuccess = function(e) {

			var data = request.result;

			if (data) {
				
				callback(data);
				
			} else {
				
				callback(false);
				
			}
				
		};
		
	}
	
	function setLimit(limit) {
		
		var cacheLimit = 100;
		
		if (limit !== undefined) {
			cacheLimit = limit;
		}
		
		viewData(function(res) {

			if (res.length >= cacheLimit) {
				
				var removeList = _.rest(res, cacheLimit);
				
				_.each(removeList, function(o) {
					
					removeData(o.id);
					
				});
				
			}
			
		});
		
	}
	
	return {
		
		init: function() {
			
			appConfig._indexDBavailable = false;

			// Added compatibility with different browsers
			window.indexedDB = window.indexedDB || window.mozIndexedDB || window.webKitIndexedDB || window.msIndexedDB;

			// Check if indexed db is not supported
			if (!window.indexedDB) {
				
				console.log("indexedDB is not supported by this browser");
				
			}
			
			sb.listen({
				'save-to-browser-cache': 		this.saveToCache
				, 'get-from-browser-cache': 	this.getFromCache
				, 'update-browser-cache': 		this.updateCache
				, 'remove-from-browser-cache': 	this.removeFromCache
				, 'set-browser-cache-limit':	this.setLimit
			});

			openDB( 
				function() { 
					
					appConfig._indexDBavailable = true;
					
					setLimit();

				}
			);
				
		}
		
		, removeFromCache: function(data) {
			
			removeData(data.id, data.onComplete, data.storeName);
			
		}
		
		, saveToCache: function(data) {
			
			if (DB !== undefined) {
				
				addData(data.obj, data.onComplete, data.storeName);	
				
			}
			
		}
		
		, setLimit: function() {
			
			setLimit();
			
		}
		
		, getFromCache: function(data) {

			try {
				
				if ( appConfig._indexDBavailable ) {
					
					setLimit();
					
					viewData(data.onComplete, data.id, data.storeName);	
					
				} else {

					data.override();
					
				}
				
			} catch ( error ) {
							
				console.log( error );
				data.override();
			
			}
			
		}
		
		, updateCache: function(data) {
			
			if (DB !== undefined) {
				
				updateData(data.id, data.callback, data.storeName);	
				
			}
			
		}
		
	}
	
});

/*
onmessage = function(cmd, time_stamp, page_name, data) {
console.log('args:: ', arguments);
    switch (cmd) {
	    
        case "serveLocalCache":

            var database_name = "simple_cache_db";
            var request = indexedDB.open(database_name);
            
            request.onsuccess = function() {
                var search_param = IDBKeyRange.only(time_stamp);
                db = request.result;
                var tx = db.transaction("simple_cache", "readonly");
                var store = tx.objectStore("simple_cache");
            
                // Check for records after time stamp and send latest record
                store.openCursor(search_param).onsuccess = function(event) {
                    var cursor = event.target.result;
                    var record = cursor.value;
                    postMessage(record);
                };
            };
            break;
        
        case "saveToLocalCache":
    
            var database_name = "simple_cache_db";
            var request = indexedDB.open(database_name);
            
            request.onsuccess = function() {
                var search_param = IDBKeyRange.only(time_stamp);
                db = request.result;
                var tx = db.transaction("simple_cache", "readonly");
                var store = tx.objectStore("simple_cache");
            
                // Check if the record exists, otherwise update w/ current
                store.openCursor(search_param).onsuccess = function(event) {
                    var cursor = event.target.result;
                    var record = cursor.value;
                    if (record) {
                        //
                    } else {
                        
                    };
                };

            };
            break;
    };
};
*/