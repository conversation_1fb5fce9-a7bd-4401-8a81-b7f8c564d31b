var objectsModule = (function(){

	var blueprint,
		domObj,
		objectType,
		
		// can be none, small, or large
		modalStyle = 'small';
	
	function getEditObjectForm(objectData, formFields){
		
		var formSetup = {};

		if(_.isEmpty(formFields)) {
			_.each(blueprint, function(field, key){
				formSetup = getFormField(formFields, field, key, formSetup, objectData);
			});
		} else {
			
			// if array provided, split into seperate columns
			if(Array.isArray(formFields)){
				
				formSetup = [];
				_.each(formFields, function(col, i){
					formSetup.push({});
					_.each(formFields[i], function(field, key){
						
						if(key !== 'form_options'){
							
							if (blueprint.hasOwnProperty(key)) {
								formSetup[i] = getFormField(formFields[i], blueprint[key], key, formSetup[i], objectData);
							} else {
								console.log('Blueprint property ' + key + ' doesn\'t exist');
							}
							
						}
						
					});
				});
				
			}else{
				
				_.each(formFields, function(field, key){
					if (blueprint.hasOwnProperty(key)) {
						formSetup = getFormField(formFields, blueprint[key], key, formSetup, objectData);
					} else {
						console.log('Blueprint property ' + key + ' doesn\'t exist');
					}
				});
				
			}
			
		}
		
		var btnCss = '';
		if(modalStyle === 'large' || modalStyle === 'small'){
			domObj.makeNode('editObjectModal', 'modal', {size: modalStyle});
		}else{
			
			domObj.makeNode('editObjectModal', 'container', {css: 'pda-container'});
			domObj.editObjectModal.makeNode('footer', 'container', {});
			domObj.editObjectModal.makeNode('body', 'container', {});
			btnCss = '';
		}
		
		if(typeof objectData !== 'undefined' && !_.isEmpty(objectData)){
			domObj.editObjectModal.body.makeNode('headerBreak', 'headerText', {text:'<br />', size:'small', css: 'text-center'});
			domObj.editObjectModal.body.makeNode('header', 'headerText', {text:'Edit<br />', size:'small', css: 'text-center'});
		}else{
			domObj.editObjectModal.body.makeNode('header', 'headerText', {text:'Create<br />', size:'small', css: 'text-center'});
		}
		
		if(Array.isArray(formFields)){
			_.each(formFields, function(column, i){
				domObj.editObjectModal.body.makeNode('col-'+ i, 'column', {width: 12/formFields.length, css: 'pda-container'});
				
				if(column.hasOwnProperty('form_options') && column.form_options.hasOwnProperty('title')){
					domObj.editObjectModal.body['col-'+ i].makeNode('title', 'headerText', {size: 'x-small', css: 'text-center', text: column.form_options.title});
				}
				
				domObj.editObjectModal.body['col-'+ i].makeNode('editObjectForm', 'form', formSetup[i]);
			});
		}else{
			domObj.editObjectModal.body.makeNode('editObjectForm', 'form', formSetup);
		}

		if(typeof objectData !== 'undefined' && !_.isEmpty(objectData)){
			domObj.editObjectModal.footer.makeNode('updateObjectButton', 'button', {text:'<i class="fa fa-check"></i> Save Changes', css: btnCss +' pda-btn-green'});
		}else{
			domObj.editObjectModal.footer.makeNode('updateObjectButton', 'button', {text:'<i class="fa fa-check"></i> Save', css: btnCss +' pda-btn-green'});
		}

		domObj.patch();
		
		if(modalStyle === 'large' || modalStyle === 'small'){
			domObj.editObjectModal.show();
		}
		
	}

	function getFormField(formFields, field, key, formSetup, objectData) {
		
		if(
			!field.immutable && typeof formFields === 'undefined'
			|| !field.immutable && _.isEmpty(formFields)
			|| !field.immutable && formFields.hasOwnProperty(key)
		){

			switch(field.type){

				case 'date':
				var dateFormat;
				if(field.displayFormat){
					dateFormat = field.displayFormat;
				}else{
					dateFormat = 'M/D/YYYY, h:mm a';
				}
				formSetup[key] = {
					type: 'date',
					name: key,
					label: field.name,
					dateFormat: dateFormat
				};
				break;
				
				case 'time':

				var dateFormat;
				if(field.displayFormat){
					dateFormat = field.displayFormat;
				}else{
					dateFormat = 'h:mma';
				}
				formSetup[key] = {
					type: 'time',
					name: key,
					label: field.name,
					dateFormat: dateFormat
				};
				break;

				case 'string':
				formSetup[key] = {
					type: 'text',
					name: key,
					label: field.name
				};
				break;

				case 'object':
				formSetup[key] = {
					type: 'textbox',
					name: key,
					label: field.name
				};
				break;

				case 'int':
				formSetup[key] = {
					type: 'text',
					name: key,
					label: field.name,
					step: 1
				};
				break;

				case 'objectId':
				if(field.objectType === 'file_meta_data'){
					formSetup[key]= {
						type: 'file-upload',
						name: key,
						label: field.name,
						rename: true
					};
				}
				break;

				case 'float':
				formSetup[key] = {
					type: 'text',
					name: key,
					label: field.name
				};
				break;

				case 'select':
				formSetup[key] = {
					type: 'select',
					name: key,
					label: field.name,
					options: []
				};

				_.each(field.options, function(label, name){

					var optionVal = name;
					if(!isNaN(parseInt(optionVal))){
						optionVal = parseInt(optionVal);
					}

					formSetup[key].options.push({
						name: label,
						value: optionVal
					});

				});
				break;

				case 'multi-select':
				formSetup[key] = {
					type: 'checkbox',
					name: key,
					label: field.name,
					options: []
				};

				_.each(field.options, function(label, name){

					var optionVal = name;
					if(!isNaN(parseInt(optionVal))){
						optionVal = parseInt(optionVal);
					}

					formSetup[key].options.push({
						name: key,
						value: optionVal,
						label: label
					});

				});
				break;

				case 'usd':
				formSetup[key] = {
					type: 'usd',
					name: key,
					label: field.name
				};
				break;

			}

			if(typeof formFields !== 'undefined' && formFields.hasOwnProperty(key)){

				if(formFields[key].hasOwnProperty('type')){
					formSetup[key].type = formFields[key].type;
				}
				if(formFields[key].hasOwnProperty('value')){
					formSetup[key].value = formFields[key].value;
				}
				if(formFields[key].hasOwnProperty('options')){
					formSetup[key].options = formFields[key].options;
				}
				_.each(formFields[key], function(arg, argKey){
					formSetup[key][argKey] = arg;
				});

			}
			
			// pre-fill data if its an edit form
			if(typeof objectData !== 'undefined' && !_.isEmpty(objectData)){

				if(typeof objectData[key] !== 'undefined' && typeof formSetup[key] !== 'undefined' && !formSetup[key].hasOwnProperty('value')){

					formSetup[key].value = objectData[key];

					if(field.type == 'select'){

						if(_.findIndex(formSetup[key].options, {value: objectData[key]}) > -1){

							formSetup[key].options[_.findIndex(formSetup[key].options, {value: objectData[key]})].selected = true;

						}else if(_.findIndex(formSetup[key].options, {value: parseInt(objectData[key])}) > -1){

							formSetup[key].options[_.findIndex(formSetup[key].options, {value: parseInt(objectData[key])})].selected = true;

						}

					}else if(field.type == 'multi-select'){

						_.each(formSetup[key].options, function(option, optionKey){

							if(_.indexOf(objectData[key], option.value) > -1 || typeof _.pluck(objectData[key], 'id') !== 'undefined' && _.indexOf(_.pluck(objectData[key], 'id'), option.value) > -1){

								formSetup[key].options[optionKey].checked = true;

							}

						});

					}else if(field.type == 'objectId' && field.objectType == 'file_meta_data' && formSetup[key].value > 0){

						formSetup[key].label += ' <small class="text-danger">(replace current file)</small>';

					}else if(field.type == 'time'){
						
						formSetup[key].value = moment(objectData[key], 'h:mma');
						
					}
				}
			}
		}

		return formSetup;
	}

	return {

		create: function(domObject, objType, formFields, childObjs, duplicate, options, notificationData){
			
			domObj = domObject;
			objectType = objType;
			if(options !== undefined && !_.isEmpty(options) && options.hasOwnProperty('modal')){
				modalStyle = options.modal;
				if(modalStyle === false){
					modalStyle = 'none';
				}
			}

			databaseConnection.obj.getBlueprint(objectType, function(blueprintObj){

				blueprint = blueprintObj;
				getEditObjectForm(duplicate, formFields);
				
				// place listeners for changes
				if(Array.isArray(formFields)){
					
					var fullForm = {};
					_.each(formFields, function(column, i){
						_.each(column, function(field, key){
							fullForm[key] = domObj.editObjectModal.body['col-'+ i].editObjectForm[key];
						});
					});
					
					_.each(formFields, function(column, i){
						_.each(column, function(field, key){
							if(field.hasOwnProperty('change')){
								$(domObj.editObjectModal.body['col-'+ i].editObjectForm[key].selector).on('change', function(e){
									field.change($(this).val(), fullForm);
								});
							}
						});
					});
					
				}else{
					
					_.each(formFields, function(field, key){
						
						if(field.hasOwnProperty('change')){
							$(domObj.editObjectModal.body.editObjectForm[key].selector).on('change', function(e){
								field.change($(this).val(), domObj.editObjectModal.body.editObjectForm);
							});
						}
						
					});
					
				}

				// set create object button listener
				$(domObj.editObjectModal.footer.updateObjectButton.selector).on('click', function(){

					domObj.editObjectModal.footer.makeNode('updateObjectButton', 'button', {css:'pda-btn-primary', text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving'});
					domObj.editObjectModal.footer.patch();					
					
					var objectData = {};
					if(Array.isArray(formFields)){
						
						_.each(formFields, function(col, i){
							
							var formData = domObj.editObjectModal.body['col-'+ i].editObjectForm.process();
							
							_.each(formData.fields, function(datum, key){
	
								objectData[key] = datum.value;
		
							});
							
						});
						
					}else{
						
						var formData = domObj.editObjectModal.body.editObjectForm.process();
						_.each(formData.fields, function(field, key){
	
							if(typeof field.value !== 'undefined'){
	
								objectData[key] = field.value;
	
							}
	
						});

					}
					
					if(childObjs){
						objectData.getChildObjs = 0;
					}else{
						objectData.getChildObjs = childObjs;
					}

					databaseConnection.obj.create(objectType, objectData, function(newObject){
						
						newObject.objects = [newObject];
						
						if(notificationData){
							newObject.notificationData = notificationData;
						}
						
						if(appConfig.hasOwnProperty('searchObjects')){

							if( appConfig.searchObjects.indexOf(objectType) > -1 ){

								// find the elasticsearch document
								var sb = Sandbox.create(Factory, 'OBJECTS', 'component', 1);
								var searchClient = sb.data.search.newClient();
								
								sb.data.db.obj.getWhere(objectType, {id:newObject.id, childObjs:1}, function(wChildObjs){
									
									searchClient.index({
										index:objectType,
										type:appConfig.instance,
										body: newObject
										}, function(e, result){
																						
											Factory.triggerEvent({
												type: objectType+ '-object-created',
												data: newObject
											}, 'OBJECTS');
											
											// hide modal if form is within modal
											if(domObj.editObjectModal.hasOwnProperty('hide')){
												domObj.editObjectModal.hide();
											}else{
												objectsModule.create(domObject, objType, formFields, childObjs, duplicate, options);
											}
											
									});
									
								});
								
							}else{
																
								Factory.triggerEvent({
									type: objectType+ '-object-created',
									data: newObject
								}, 'OBJECTS');
								
								// hide modal if form is within modal
								if(domObj.editObjectModal.hasOwnProperty('hide')){
									domObj.editObjectModal.hide();
								}else{
									objectsModule.create(domObject, objType, formFields, childObjs, duplicate, options);
								}
								
							}
							
						}else{
							
							Factory.triggerEvent({
								type: objectType+ '-object-created',
								data: newObject
							}, 'OBJECTS');
							
							if(domObj.editObjectModal.hasOwnProperty('hide')){
								domObj.editObjectModal.hide();
							}else{
								objectsModule.create(domObject, objType, formFields, childObjs, duplicate, options);
							}
							
						}

					}, childObjs);
				});

			}, true);

		},

		edit: function(domObject, objType, objectData, formFields, childObjs, options){

			domObj = domObject;
			objectType = objType;
			
			if(options !== undefined && !_.isEmpty(options) && options.hasOwnProperty('modal')){
				modalStyle = options.modal;
				if(modalStyle === false){
					modalStyle = 'none';
				}
			}

			databaseConnection.obj.getBlueprint(objectType, function(blueprintObj){

				blueprint = blueprintObj;
				
				getEditObjectForm(objectData, formFields);
				
				// place listeners for changes
				if(Array.isArray(formFields)){
					
					var fullForm = {};
					_.each(formFields, function(column, i){
						_.each(column, function(field, key){
							fullForm[key] = domObj.editObjectModal.body['col-'+ i].editObjectForm[key];
						});
					});
					
					_.each(formFields, function(column, i){
						_.each(column, function(field, key){
							if(field.hasOwnProperty('change')){
								$(domObj.editObjectModal.body['col-'+ i].editObjectForm[key].selector).on('change', function(e){
									field.change($(this).val(), fullForm);
								});
							}
						});
					});
					
				}else{
					
					_.each(formFields, function(field, key){
						
						if(field.hasOwnProperty('change')){
							$(domObj.editObjectModal.body.editObjectForm[key].selector).on('change', function(e){
								field.change($(this).val(), domObj.editObjectModal.body.editObjectForm);
							});
						}
						
					});

					
				}
				
				// set update object button listener
				$(domObj.editObjectModal.footer.updateObjectButton.selector).on('click', function(){
					
					var newData = {
						id: objectData.id
					};
					
					var btnCss = 'pda-btn-primary';
					if(modalStyle === 'none'){
						btnCss = 'pda-btn-primary';
					}
					
					domObj.editObjectModal.footer.makeNode('updateObjectButton', 'button', {css:btnCss, text:'<i class="fa fa-circle-o-notch fa-spin"></i> Saving'});
					domObj.editObjectModal.footer.patch();
					domObj.editObjectModal.footer.updateObjectButton.loading();
					
					// if in diff columns, process each col into single obj
					if(Array.isArray(formFields)){
						
						_.each(formFields, function(col, i){
							
							var formData = domObj.editObjectModal.body['col-'+ i].editObjectForm.process();
							
							_.each(formData.fields, function(datum, key){
	
								newData[key] = datum.value;
		
							});
							
						});
						
					}else{
						
						var formData = domObj.editObjectModal.body.editObjectForm.process();
	
						_.each(formData.fields, function(datum, key){
	
							newData[key] = datum.value;
	
						});

					}
					
					if(childObjs){
						newData.getChildObjs = childObjs;
					}else{
						newData.getChildObjs = 0;
					}

					databaseConnection.obj.update(objectType, newData, function(updatedObject){
						
						updatedObject.objects = [updatedObject];
						
						if(appConfig.hasOwnProperty('searchObjects')){

							if( appConfig.searchObjects.indexOf(objectType) > -1 ){

								// find the elasticsearch document
								var sb = Sandbox.create(Factory, 'OBJECTS', 'component', 1);
								var searchClient = sb.data.search.newClient();
								
								sb.data.db.obj.getWhere(objectType, {id:updatedObject.id, childObjs:1}, function(wChildObjs){
									
									searchClient.search({
										index:objectType,
										type:appConfig.instance,
										q:'id:'+updatedObject.id,
									}, function(e, result){
																				
										var bulkActions = [];
										
										_.each(result.hits.hits, function(searchObj){
											
											bulkActions.push(
												{ update: { _index: objectType, _type: appConfig.instance, _id: searchObj._id } }
											);
											
											bulkActions.push(
												{ doc: wChildObjs[0] }
											);
											
										});
										
										searchClient.bulk({
											body: bulkActions
											}, function (err, resp) {
											
												Factory.triggerEvent({
													type: objectType+ '-object-updated',
													data: updatedObject
												}, 'OBJECTS');
												
												
												if(domObj.editObjectModal.hasOwnProperty('hide')){
													domObj.editObjectModal.hide();
												}else{
													objectsModule.edit(domObject, objType, updatedObject, formFields, childObjs, options);
												}
											
										});
										
									});	
									
								});				
								
							}else{
								
								Factory.triggerEvent({
									type: objectType+ '-object-updated',
									data: updatedObject
								}, 'OBJECTS');
		
								if(domObj.editObjectModal.hasOwnProperty('hide')){
									domObj.editObjectModal.hide();
								}else{
									objectsModule.edit(domObject, objType, updatedObject, formFields, childObjs, options);
								}
								
							}
							
						}else{
							
							Factory.triggerEvent({
								type: objectType+ '-object-updated',
								data: updatedObject
							}, 'OBJECTS');
	
							if(domObj.editObjectModal.hasOwnProperty('hide')){
								domObj.editObjectModal.hide();
							}else{
								objectsModule.edit(domObject, objType, updatedObject, formFields, childObjs, options);
							}
						
						}

					});
				});

			}, true);

		},

		erase: function(objType, objId){

			databaseConnection.obj.erase(objType, objId, function(response){

				if(appConfig.hasOwnProperty('searchObjects')){

					if( appConfig.searchObjects.indexOf(objectType) > -1 ){

						// find the elasticsearch document
						var sb = Sandbox.create(Factory, 'OBJECTS', 'component', 1);
						var searchClient = sb.data.search.newClient();
						
						searchClient.search({
							index:objectType,
							type:appConfig.instance,
							q:'id:'+updatedObject.id,
						}, function(e, result){
																	
							var bulkActions = [];
							
							_.each(result.hits.hits, function(searchObj){
								
								bulkActions.push(
									{ delete: { _index: objType, _type: appConfig.instance, _id: searchObj._id } }
								);
																
							});
							
							searchClient.bulk({
								body: bulkActions
								}, function (err, resp) {
								
									Factory.triggerEvent({
										type: objType+ '-object-deleted',
										data: {
											objectType: objType,
											objectId: objId
										}
									});			
								
							});
							
						});
						
					}else{
						
						Factory.triggerEvent({
							type: objType+ '-object-deleted',
							data: {
								objectType: objType,
								objectId: objId
							}
						});
				
					}
						
				}else{
					
					Factory.triggerEvent({
						type: objType+ '-object-deleted',
						data: {
							objectType: objType,
							objectId: objId
						}
					});
				
				}

			});

		}

	}

})();