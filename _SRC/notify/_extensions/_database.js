var IN_NODE_ENV = typeof module !== "undefined" && module.exports;
if (IN_NODE_ENV) {
  var _ = require("underscore-node");
  var moment = require("moment");
  var axios = require("axios");
  var SERVICE = process.env["SERVICE"];
}

var databaseConnection = (function () {
  var objects = [],
    db_calls = [],
    obj_blueprints = {},
    obj_bp_w_options = {},
    configTest = {},
    appConfig = {},
    rootPath = "",
    postPath = "",
    PATH_CHANGED = 0,
    GET_PATH = "../../api/_get.php",
    UPDATE_PATH = "../../api/_get.php";

  var bentoToken = "";
  var xhrList = [];
  var previousPage = "";

  // Caching.
  var Requests = [];
  var RequestLimit = 50;
  function isSame(a, b) {
    function getClass(obj) {
      return Object.prototype.toString.call(obj);
    }

    // If a and b reference the same value, return true
    if (a === b) return true;

    // If a and b aren't the same type, return false
    if (typeof a != typeof b) return false;

    // Already know types are the same, so if type is number
    // and both NaN, return true
    if (typeof a == "number" && isNaN(a) && isNaN(b)) return true;

    // Get internal [[Class]]
    var aClass = getClass(a);
    var bClass = getClass(b);

    // Return false if not same class
    if (aClass != bClass) return false;

    // If they're Boolean, String or Number objects, check values
    if (
      aClass == "[object Boolean]" ||
      aClass == "[object String]" ||
      aClass == "[object Number]"
    ) {
      return a.valueOf() == b.valueOf();
    }

    // If they're RegExps, Dates or Error objects, check stringified values
    if (
      aClass == "[object RegExp]" ||
      aClass == "[object Date]" ||
      aClass == "[object Error]"
    ) {
      return a.toString() == b.toString();
    }

    // Otherwise they're Objects, Functions or Arrays or some kind of host object
    if (typeof a == "object" || typeof a == "function") {
      // For functions, check stringigied values are the same
      // Almost certainly false if a and b aren't trivial
      // and are different functions
      if (aClass == "[object Function]" && a.toString() != b.toString())
        return false;

      var aKeys = Object.keys(a);
      var bKeys = Object.keys(b);

      // If they don't have the same number of keys, return false
      if (aKeys.length != bKeys.length) return false;

      // Check they have the same keys
      if (
        !aKeys.every(function (key) {
          return b.hasOwnProperty(key);
        })
      )
        return false;

      // Check key values - uses ES5 Object.keys
      return aKeys.every(function (key) {
        return isSame(a[key], b[key]);
      });
    }

    return false;
  }

  function readCookie(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(";");
    for (var i = 0; i < ca.length; i++) {
      var c = ca[i];
      while (c.charAt(0) == " ") c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  function getAjax(url, success) {
    var xhr = window.XMLHttpRequest
      ? new XMLHttpRequest()
      : new ActiveXObject("Microsoft.XMLHTTP");
    xhr.open("GET", url);
    xhr.onreadystatechange = function () {
      if (xhr.readyState > 3 && xhr.status == 200) success(xhr.responseText);
    };
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
    xhr.send();
    return xhr;
  }

  // example ajax request
  // getAjax('http://foo.bar/?p1=1&p2=Hello+World', function(data){ console.log(data); });

  function postAjax(url, data, success) {
    var params =
      typeof data == "string"
        ? data
        : Object.keys(data)
            .map(function (k) {
              return encodeURIComponent(k) + "=" + encodeURIComponent(data[k]);
            })
            .join("&");

    var xhr = window.XMLHttpRequest
      ? new XMLHttpRequest()
      : new ActiveXObject("Microsoft.XMLHTTP");
    xhr.open("POST", url);
    xhr.onreadystatechange = function () {
      if (xhr.readyState > 3 && xhr.status == 200) {
        success(xhr.responseText);
      }
    };
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send(params);
    return xhr;
  }

  // example request with data object
  //postAjax('http://foo.bar/', { p1: 1, p2: 'Hello World' }, function(data){ console.log(data); });

  // !TODO: Move to service worker.
  function updateCache(type, request, response) {
    // Invalidate old (more than 60 sec) requests.
    var currentTs = Math.round(new Date().getTime() / 1000);
    Requests = _.filter(Requests, function (req) {
      return !(currentTs - req.ts > 60);
    });
    // console.log('updateCache::', type, request);
    // Invalidate cached requests related to updated data.
    if (
      type === "update" ||
      type === "upload" ||
      type === "updateObject" ||
      type === "updateState" ||
      type === "runSteps" ||
      type === "addTagToObject" ||
      type === "removeTagFromObject" ||
      type === "createNewObject" ||
      type === "createFromTemplate" ||
      type === "deleteObject" ||
      type === "restoreObjs" ||
      type === "postComment"
    ) {
      var reqObjId = 0;
      var reqObjType = "";

      if (type === "createNewObject") {
        reqObjType = request.objectType;
      } else if (type === "createFromTemplate") {
        if (request.options && request.options.object_bp_type) {
          reqObjType = request.options.object_bp_type;
        }
      } else if (type === "deleteObject" || type === "restoreObjs") {
        reqObjId = request.id;
        reqObjType = request.type;
      } else if (type === "addTagToObject" || type === "removeTagFromObject") {
        reqObjId = request.typeId;
        reqObjType = request.objectType;
      } else if (type === "upload") {
        reqObjType = request.objectType;
      } else if (type === "runSteps") {
        if (request.objId) {
          reqObjId = request.objId;
        } else {
          return;
        }

        if (reqObjId && reqObjId.id) {
          reqObjId = reqObjId.id;
        }
      } else if (type === "postComment") {
        reqObjType = "notes";
      } else if (request.objectData) {
        reqObjId = request.objectData.id;
        reqObjType = request.objectType;
      } else if (response.id) {
        reqObjId = response.id;
        reqObjType = response.object_bp_type;
      }

      // Clear the cache entirely for updateState calls, since
      // they can trigger lots of downstream changes
      if (type === "updateState") {
        Requests = [];

        // Otherwise, clear requests based on objectType
      } else {
        Requests = _.filter(
          Requests,
          function (req) {
            switch (req.type) {
              case "getObjectById":
                if (
                  req.request.value == reqObjId ||
                  (Array.isArray(req.request.value) &&
                    _.contains(req.request.value, reqObjId)) ||
                  (Array.isArray(reqObjId) &&
                    _.contains(reqObjId, req.request.value)) ||
                  (Array.isArray(req.request.value) &&
                    Array.isArray(reqObjId) &&
                    !_.isEmpty(_.intersection(req.request.value, reqObjId)))
                ) {
                  return false;
                } else {
                  return true;
                }
                break;

              case "getObjectsWhere":
              case "getObjectBlueprint":
              case "getAllObjects":
                break;
            }

            var notObjType = req.request.objectType !== reqObjType;
            var startsWith = false;

            if (reqObjType) {
              startsWith = !reqObjType.startsWith(req.request.objectType + ".");
            }

            return notObjType && startsWith;
          }

          // return (
          // 	req.request.objectType !== reqObjType
          // 	&& !reqObjType.startsWith(req.request.objectType +'.')
          // );
        );
      }
    } else if (
      type === "getObjectById" ||
      type === "getObjectsWhere" ||
      type === "getObjectBlueprint" ||
      type === "getAllObjects"
    ) {
      if (Requests.length >= RequestLimit) {
        Requests.shift();
      }

      if (
        request.objectType !== "inventory_menu" &&
        request.objectType !== "inventory_groups" &&
        request.objectType !== "inventory_billable_combinations" &&
        request.objectType !== "inventory_billable_groups" &&
        request.objectType !== "inventory_recipe"
      ) {
        Requests.push({
          type: type,
          request: request,
          response: response,
          ts: currentTs,
        });
      }
    }

    return;
  }

  function checkCache(type, request, onResponse) {
    setTimeout(function () {
      var ofSameType = _.where(Requests, {
        type: type,
      });
      var found = false;

      if (
        type === "getObjectById" ||
        type === "getObjectsWhere" ||
        type === "getObjectBlueprint" ||
        type === "getAllObjects"
      ) {
        _.each(ofSameType, function (cached) {
          switch (type) {
            case "getObjectById":
              if (
                (Array.isArray(request.value) &&
                  isSame(cached.request, request)) ||
                (request.value == cached.request.value &&
                  isSame(cached.request, request))
              ) {
                found = cached.response;
              }
              break;

            case "getObjectsWhere":
            case "getObjectBlueprint":
            case "getAllObjects":
              if (
                request.objectType === cached.request.objectType &&
                isSame(cached.request, request)
              ) {
                found = cached.response;
              }
              break;
          }
        });
      }

      return onResponse(found);
    }, 1);
  }

  function deepClone(item) {
    if (!item) {
      return item;
    } // null, undefined values check

    var types = [Number, String, Boolean],
      result;

    // normalizing primitives if someone did new String('aaa'), or new Number('444');
    types.forEach(function (type) {
      if (item instanceof type) {
        result = type(item);
      }
    });

    if (typeof result == "undefined") {
      if (Object.prototype.toString.call(item) === "[object Array]") {
        result = [];
        item.forEach(function (child, index, array) {
          result[index] = deepClone(child);
        });
      } else if (typeof item == "object") {
        // testing that this is DOM
        if (item.nodeType && typeof item.cloneNode == "function") {
          var result = item.cloneNode(true);
        } else if (!item.prototype) {
          // check that this is a literal
          if (item instanceof Date) {
            result = new Date(item);
          } else {
            // it is an object literal
            result = {};
            for (var i in item) {
              result[i] = deepClone(item[i]);
            }
          }
        } else {
          // depending what you would like here,
          // just keep the reference, or create new object
          if (false && item.constructor) {
            // would not advice to do that, reason? Read below
            result = new item.constructor();
          } else {
            result = item;
          }
        }
      } else {
        result = item;
      }
    }

    return result;
  }

  function getCookie(name) {
    var value = "; " + document.cookie;
    var parts = value.split("; " + name + "=");
    if (parts.length == 2) return parts.pop().split(";").shift();
  }

  function getFromCache(call, queryObj, previousCall) {
    var ret = false;

    switch (call) {
      case "getObjectBlueprint":
        if (queryObj.getOptions) {
          ret = obj_bp_w_options[queryObj.objectType];
        } else {
          ret = obj_blueprints[queryObj.objectType];
        }
        break;

      default:
        var response = previousCall.response,
          ret = [];

        if (
          !queryObj.hasOwnProperty("paged") &&
          queryObj.hasOwnProperty("queryObj") &&
          !queryObj.queryObj.hasOwnProperty("paged")
        ) {
          ret = deepClone(response.data);
        } else {
          ret = deepClone(response);
        }
        break;
    }

    return ret;
  }

  function getPathToUse(appConfig, update, root) {
    if (root != undefined) {
      var dbPath = root;
    } else {
      var dbPath = rootPath;
    }

    if (update) {
      dbPath = UPDATE_PATH + "?pagodaAPIKey=" + appConfig.instance + "&do=";
    } else {
      if (root) {
        dbPath = root;
      } else {
        dbPath = GET_PATH + "?pagodaAPIKey=" + appConfig.instance + "&do=";
      }
    }

    if (PATH_CHANGED == 0) {
      switch (appConfig.instance) {
        case "rickyvoltz":
        case "zachvoltz":
        case "petermikhail":
        case "johnwhittenburg":
        case "joshgantt":
        case "jonathanwright":
          if (update) {
            dbPath =
              "../../api/_get.php?api_webform=true&pagodaAPIKey=" +
              appConfig.instance +
              "&do=";
          } else {
            dbPath =
              "../../api/_get.php?api_webform=true&pagodaAPIKey=" +
              appConfig.instance +
              "&do=";
          }

          break;

        /*
							case 'voltzsoftware':

								if(update){
									dbPath = 'https://pagoda.voltz.software/_repos/_production/notify/pagoda/_coredev/_get.php?pagodaAPIKey='+ appConfig.instance +'&api_webform=true&do=';
								}else{
									dbPath = 'https://get-staging.voltz.software/_repos/_production/notify/pagoda/_coredev/_get.php?pagodaAPIKey='+ appConfig.instance +'&api_webform=true&do=';
								}

								break;
				*/
      }
    }

    if (root) {
      dbPath = root;
    }

    return dbPath;
  }

  function get(
    phpFunction,
    obj,
    callback,
    i,
    noCache,
    root,
    update,
    verbose,
    service
  ) {
    if (IN_NODE_ENV) {
      if (SERVICE === "Merge") {
        var varToken = global.dbAuth.dbToken;
        var varSeries = global.dbAuth.dbSeries;
        var varUid = global.dbAuth.dbUid;
        var varInstance = global.dbAuth.dbInstance;
        var varPagoda = global.dbAuth.dbPagoda;
        var varPagodaAPIKey = global.dbAuth.dbPagodaAPIKey;

        var TEST_ENVIRONMENT = process.env.TEST_ENVIRONMENT;
        var data_endpoint = process.env.DATA_ENDPOINT;

        if (TEST_ENVIRONMENT == "ON") {
          var port = ":8080";
          var http = "http://";
        } else {
          var port = "";
          var http = "https://";
        }

        var url =
          http +
          data_endpoint +
          port +
          "/api/_get.php?api_webform=true&pagodaAPIKey=" +
          global.dbAuth.dbPagodaAPIKey +
          "&do=" +
          phpFunction;
        if (service) {
          url += "&service=" + service;
        }

        // This is the api call form the Merge service
        axios({
          method: "POST",
          url: url,
          headers: {
            Cookie:
              "pagoda=" +
              varPagoda +
              "; series=" +
              varSeries +
              "; token=" +
              varToken +
              "; uid=" +
              varUid +
              "; user_type=0; instance=" +
              varInstance +
              "",
            "bento-token": "" + varToken + "",
            "Content-Type": "application/x-www-form-urlencoded",
          },
          transformRequest: [
            function (data, headers) {
              const serializedData = [];

              for (const k in data) {
                if (data[k]) {
                  serializedData.push(`${k}=${encodeURIComponent(data[k])}`);
                }
              }

              return serializedData.join("&");
            },
          ],
          data: { json: JSON.stringify(obj) },
        })
          .then((res) => {
            callback(res.data);
          })
          .catch((error) => {
            callback(error);
          });
        return;
      } else {
        // This is the data call for tests from the node api test env
        axios({
          method: "POST",
          url:
            "http://localhost:8080/api/_get.php?api_webform=true&pagodaAPIKey=dev_test_env&do=" +
            phpFunction,
          headers: {
            // "Cookie": "_ga=GA1.1.35111699.1598486439; gs_v_GSN-752757-B=email:developers-bento.infinityhospitality.net; pagoda=b1187b464b9066d28a4c411961008f72; uid=920410; user_type=0; instance=rickyvoltz; _gid=GA1.1.1317143088.1602359065; series=bbc6aa15bc6841d211a72e12f67f8ec855dcc7bba958b6d7b536b8d0030e6dae3780eee19358a08cc6b8d6563aec898ebb962484a1da19e3c606810ca87abb03; token=d2e42312011a4fb274029741afdf8c68a8885c97f9d8ef10e6cdd17b24acd1613c9d3b6c7ac4fc9b667dae6d0f2bdcf7f30111899cdd6fc072b8f68da7ce9200; gs_u_GSN-752757-B=922e15bcf98ea3db7627016d8481c621:12979:590176:1602359516044"
            Cookie:
              "gs_v_GSN-752757-B=email:<EMAIL>; pagoda=b1187b464b9066d28a4c411961008f72; uid=920410; user_type=0; instance=dev_test_env;series=c6c484bb9f7608d4e7bf1bf96d98f5b46b90d684bef7db7395d6bc0ae9eca9c16cfce59ab96f927584cb4d765be5480408f628632655cae4dba803325bcda576; token=49f854f34250d9051ac326b0b53c311363dc9ac02c61b02a2024ff160dfd71537fb50d0e3b862a2bd5429a91ee8abbf91188636a864007df1672c9d720797f26;",
            "bento-token":
              "49f854f34250d9051ac326b0b53c311363dc9ac02c61b02a2024ff160dfd71537fb50d0e3b862a2bd5429a91ee8abbf91188636a864007df1672c9d720797f26",
            "Content-Type": "application/x-www-form-urlencoded",
            // , "Content-Type": "application/json"
            // , "Content-Length": data.length
          },
          transformRequest: [
            function (data, headers) {
              const serializedData = [];

              for (const k in data) {
                if (data[k]) {
                  serializedData.push(`${k}=${encodeURIComponent(data[k])}`);
                }
              }

              return serializedData.join("&");
            },
          ],
          data: { json: JSON.stringify(obj) },
        })
          .then((res) => {
            callback(res.data);
          })
          .catch((error) => {
            callback(error);
          });
        return;
      }
    }

    function onResponse(response) {
      if (response == "cookie error") {
        // Show login form
        Factory.triggerEvent(
          {
            type: "logout-user",
            data: {
              cookieError: true,
            },
          },
          "db"
        );
      } else {
        // !Patch, until we work out the misalignment between dev/production where
        // these are not followed through (might have to do with the image not matching
        // the most up to date)
        if (phpFunction === "updateState" && typeof response === "string") {
          response = JSON.parse(response);
        }

        Factory.triggerEvent(
          {
            type: "db-call-completed",
            data: {
              call: phpFunction,
              queryObj: obj,
              data: deepClone(response),
              timestamp: callTimestamp,
            },
          },
          "db"
        );

        // if (update) {
        // 	if (obj.objectType != 'view_settings') {
        // 		if (ga) {
        // 			ga('send', {
        // 				hitType: 'event',
        // 				eventCategory: 'Objects',
        // 				eventAction: phpFunction,
        // 				eventLabel: obj.objectType
        // 			});
        // 		}
        // 	}
        // }

        if (response && response._notify_usr) {
          if (!_.isEmpty(response.msg)) {
            // user alerts
            if (!_.isEmpty(response.msg.messages)) {
              // Invalidate cache, as we yet do not have a good way
              // to know what obj types should be invalidated.
              // Need to pass this back in the response from rules.
              Requests = [];

              var shouldShow = false;
              var msg = '<div class="ui bulleted list">';
              var color = "green";
              var displayTime = null;
              if (response.hasOwnProperty("type")) {
                if (response.type === "error") {
                  color = "red";
                } else if(response.type === "warning"){
                  color = "orange";
                  displayTime = 45000;
                }
              }
                _.each(response.msg.messages, function (message) {
                  if (message) {
                    msg += '<div class="item">' + message + "</div>";
                    shouldShow = true;
                  }
                });
                msg += "</div>";

              if (shouldShow) {
                // For page refresh messages
                if (
                  typeof msg === "string" &&
                  msg.indexOf("{{REFRESH PAGE}}") !== -1
                ) {
                  alerts.ask(
                    {
                      title: "Refresh the page?",
                      html: msg.replace("{{REFRESH PAGE}}", ""),
                    },
                    function (response) {
                      if (response) {
                        location.reload();
                      }
                    }
                  );
                } else {
                  Factory.triggerEvent({
                    type: "display-alert",
                    data: {
                      header: "",
                      body: msg,
                      color: color,
                      displayTime: displayTime
                    },
                  });
                }
              }
            }

            // data->ui updates
            if (!_.isEmpty(response.msg.updates)) {
              _.each(response.msg.updates, function (update) {
                _.each(update, function (val, key) {
                  if (key !== "id") {
                    Factory.triggerEvent({
                      type: "field-updated",
                      data: {
                        obj: update,
                        property: key,
                      },
                    });
                  }
                });
              });
            }

            // notifications
            if (!_.isEmpty(response.msg.notifications)) {
              Factory.triggerEvent({
                type: "get-sys-modal",
                data: {
                  callback: function (modal) {
                    modal.empty();
                    modal.show();

                    Factory.triggerEvent({
                      type: "get-action-options-form",
                      data: {
                        ui: modal,
                        obj: response.response,
                        notifications: response.msg.notifications,
                      },
                    });
                  },
                },
              });
            }
          }

          if (
            true
            // 					appConfig.instance === 'voltzsoftware'
            // 					|| appConfig.instance === 'rickyvoltz'
            // 					false
          ) {
            updateCache(phpFunction, obj, response.response);
          }

          if (verbose) {
            return callback(deepClone(response));
          } else {
            return callback(deepClone(response.response));
          }
        }

        callback(deepClone(response));

        if (
          true
          // 				appConfig.instance === 'voltzsoftware'
          // 				|| appConfig.instance === 'rickyvoltz'
          // 				false
        ) {
          updateCache(phpFunction, obj, response);
        }
      }
    }

    //!TODO: get working for all read calls; currently the cache is only turned on for blueprints
    if (phpFunction === "getObjectBlueprint") {
      if (!noCache) {
        // if we don't have blueprints list, bypass caching and get list for next time
        if (_.isEmpty(obj_blueprints)) {
          get(
            "getObjectBlueprints",
            {},
            function (blueprints) {
              for (i in blueprints) {
                obj_blueprints[blueprints[i].blueprint_name] =
                  blueprints[i].blueprint;
              }
            },
            1,
            true
          );
        } else {
          // if its an update call, mark any dependent caches as outdated
          if (
            phpFunction === "createNewObject" ||
            phpFunction === "updateObject"
          ) {
            for (i in db_calls) {
              if (
                db_calls[i].queryObj.objectType === obj.objectType ||
                db_calls[i].queryObj.type === obj.objectType ||
                _.contains(db_calls[i].childObjTypes, obj.objectType) ||
                _.contains(db_calls[i].childObjTypes, obj.type)
              ) {
                db_calls[i].getNew = true;
              }
            }

            // if its a read call, check if can use cache
          } else {
            if (phpFunction === "getObjectBlueprint") {
              var ret = getFromCache(phpFunction, obj);

              if (ret) {
                return callback(ret);
              }
            } else {
              var previousCalls = _.where(db_calls, { call: phpFunction });
              for (i in previousCalls) {
                if (
                  (_.isEqual(obj, previousCalls[i].queryObj) &&
                    !previousCalls[i].getNew &&
                    previousCalls[i].response_date.isAfter(
                      moment().subtract(120, "seconds")
                    )) ||
                  (_.isEqual(obj, previousCalls[i].queryObj) &&
                    phpFunction === "getObjectBlueprints")
                ) {
                  var ret = getFromCache(phpFunction, obj, previousCalls[i]);
                  if (ret) {
                    console.log(
                      "used the cache: (" +
                        phpFunction +
                        ", " +
                        obj.objectType +
                        ")",
                      obj,
                      ret
                    );
                    return callback(ret);
                  }
                }
              }
            }
          }
        }
      }
    }

    var callTimestamp = moment().unix();
    Factory.triggerEvent(
      {
        type: "db-call-triggered",
        data: {
          call: phpFunction,
          queryObj: obj,
          timestamp: callTimestamp,
        },
      },
      "db"
    );

    var dbPath = getPathToUse(appConfig, update, root);

    // 		var ajaxTime= new Date().getTime();
    var type = "post";
    /*
				if (JSON.stringify(obj).length > 1000) {
					type = 'post';
				}
		*/

    // Check local cache.
    checkCache(phpFunction, obj, function (cached) {
      if (cached && !_.isEmpty(cached) && !noCache) {
        // 	console.log('CACHE HIT', phpFunction);
        onResponse(cached);
        return;
      }

      // 	console.log('CACHE MISS', phpFunction);
      $.ajax({
        type: type,
        beforeSend: function (xhr) {
          xhr.setRequestHeader(
            "Content-type",
            "application/x-www-form-urlencoded"
          );
          xhr.setRequestHeader("bento-token", bentoToken);

          if (appConfig && appConfig.state && appConfig.state.portal) {
            xhr.setRequestHeader("portal", appConfig.state.portal);
          }

          xhrList.push(xhr);
        },
        url: dbPath + phpFunction,
        xhrFields: {
          withCredentials: true,
        },
        crossDomain: true,
        data: { json: JSON.stringify(obj) },
        success: onResponse,
        error: function (jqXHR, status, error) {
          if (jqXHR.status == 404) {
            if (typeof i === "undefined") {
              i = 1;
            }
            i++;
            if (i <= 6) {
              setTimeout(function () {
                get(phpFunction, obj, callback, i);
              }, i * i * 100);
            } else {
              Factory.triggerEvent(
                {
                  type: "db-call-completed",
                  data: {
                    call: phpFunction,
                    queryObj: obj,
                    data: jqXHR,
                    error: error,
                    status: status,
                    timestamp: callTimestamp,
                  },
                },
                "db"
              );

              alerts.alert(
                "Error!",
                "Bad connection--try refreshing your page.",
                "error"
              );
            }
          }
        },
      });
    });
  }

  function checkAuth(callback) {
    function onResponse(response) {
      if (response == "cookie error") {
        return false;
      } else {
        Factory.triggerEvent(
          {
            type: "db-call-completed",
            data: {
              data: deepClone(response),
              timestamp: callTimestamp,
            },
          },
          "db"
        );

        callback(deepClone(response));
      }
    }

    var callTimestamp = moment().unix();
    Factory.triggerEvent(
      {
        type: "db-call-triggered",
        data: {
          call: "checkAuth",
          timestamp: callTimestamp,
        },
      },
      "db"
    );

    $.ajax({
      type: "POST",
      beforeSend: function (xhr) {
        xhr.setRequestHeader(
          "Content-type",
          "application/x-www-form-urlencoded"
        );
        xhr.setRequestHeader("bento-token", bentoToken);

        if (appConfig && appConfig.state && appConfig.state.portal) {
          xhr.setRequestHeader("portal", appConfig.state.portal);
        }

        xhrList.push(xhr);
      },
      url: "/api/_checkAuth.php",
      xhrFields: {
        withCredentials: true,
      },
      crossDomain: true,
      data: {},
      success: onResponse,
      error: function (jqXHR, status, error) {
        if (jqXHR.status == 404) {
          if (typeof i === "undefined") {
            i = 1;
          }
          i++;
          if (i <= 6) {
            setTimeout(function () {
              checkAuth(callback);
            }, i * i * 100);
          } else {
            Factory.triggerEvent(
              {
                type: "db-call-completed",
                data: {
                  call: "checkAuth",
                  data: jqXHR,
                  error: error,
                  status: status,
                  timestamp: callTimestamp,
                },
              },
              "db"
            );

            alerts.alert(
              "Error!",
              "Bad connection--try refreshing your page.",
              "error"
            );
          }
        }
      },
    });
  }

  // takes a {} object and returns a FormData object
  function objectToFormData(obj, form, namespace) {
    var fd = form || new FormData();
    var formKey;

    for (var property in obj) {
      if (obj.hasOwnProperty(property)) {
        if (namespace) {
          formKey = namespace + "[" + property + "]";
        } else {
          formKey = property;
        }

        // if the property is an object, but not a File,
        // use recursivity.
        if (
          typeof obj[property] === "object" &&
          !(obj[property] instanceof File)
        ) {
          objectToFormData(obj[property], fd, formKey);
        } else {
          // if it's a string or a File object
          fd.append(formKey, obj[property]);
        }
      }
    }

    return fd;
  }

  function upload(phpFunction, file, callback, i) {
    // usage example
    var z = objectToFormData(file);

    var xhr = new XMLHttpRequest();

    switch (appConfig.instance) {
      case "rickyvoltz":
      case "zachvoltz":
      case "petermikhail":
      case "johnwhittenburg":
      case "joshgantt":
        postPath =
          "../../api/_post.php?pagodaAPIKey=" + appConfig.instance + "&do=";

        break;

      /*
						case 'voltzsoftware':

							if(update){
								dbPath = 'https://pagoda.voltz.software/_repos/_production/notify/pagoda/_coredev/_get.php?pagodaAPIKey='+ appConfig.instance +'&api_webform=true&do=';
							}else{
								dbPath = 'https://get-staging.voltz.software/_repos/_production/notify/pagoda/_coredev/_get.php?pagodaAPIKey='+ appConfig.instance +'&api_webform=true&do=';
							}

							break;
			*/
    }

    postPath =
      "../../api/_post.php?pagodaAPIKey=" + appConfig.instance + "&do=";

    xhr.open("POST", postPath + phpFunction, true);
    xhr.setRequestHeader("bento-token", bentoToken);
    xhr.onreadystatechange = function () {
      updateCache(
        "upload",
        {
          objectType: file["sys-obj-type"],
        },
        {}
      );

      if (xhr.readyState === 4 && xhr.status === 200) {
        callback(JSON.parse(xhr.responseText));
      }
    };

    xhr.send(z);
  }

  function submitForm(phpFunction, formSelector, callback) {
    $.ajax({
      type: "post",
      url: rootPath + phpFunction,
      type: "post",
      data: $(formSelector).serialize(),
      success: function (ret) {
        callback(ret);
      },
    });
  }

  function updateObject(object, callback) {
    get("updateObject", object, function (response) {
      callback(response);
    });
  }

  function usePost(objectData, objectType, childObjs) {
    var ret = false,
      fileData,
      fileKey;

    _.each(objectData, function (datum, key) {
      if (
        datum !== null &&
        typeof datum === "object" &&
        datum.hasOwnProperty("fileData")
      ) {
        fileData = datum;
        fileKey = key;
      }
    });

    if (fileData) {
      ret = new FormData();

      if (fileData) {
        ret.append("file", fileData.fileData);
        ret.append("sys-file-prop-key", fileKey);

        ret.append("sys-obj-type", objectType);
      }

      objectData.file = fileData.fileData;
      objectData["sys-file-prop-key"] = fileKey;
      objectData["sys-obj-type"] = objectType;

      _.each(objectData, function (val, key) {
        ret.append(key, val);
      });

      _.each(fileData, function (val, key) {
        if (key !== "fileData") {
          ret.append("file-" + key, val);
          objectData["file-" + key] = val;
        }
      });

      if (childObjs) {
        ret.append("getChildObjs", childObjs);
        objectData["getChildObjs"] = childObjs;
      }
    }

    return ret;
  }

  function trackXhr(checkUrl) {
    var currentPage = location.href;

    if (previousPage !== "" && (checkUrl === undefined || checkUrl === true)) {
      if (currentPage !== previousPage) {
        _.each(xhrList, function (xhr) {
          if (xhr.readyState === 0 || xhr.readyState === 1) {
            xhr.abort();
          }
        });

        xhrList = [];
      }
    } else {
      _.each(xhrList, function (xhr) {
        if (xhr.readyState === 0 || xhr.readyState === 1) {
          xhr.abort();
        }
      });

      xhrList = [];
    }

    previousPage = currentPage;
  }

  return {
    mergeEndpoint: function (requestData, onComplete) {
      var contextId = requestData.contextId;
      var context = requestData.context;

      var postData = {
        contextId: contextId,
        context: context,
        templateId: requestData.templateId,
        templateHtml: requestData.templateHtml,
        mergeVars: requestData.mergeVars,
        authData: {
          varPagodaAPIKey: appConfig.instance,
        },
      };

      postAjax(
        "/api/merge.php",
        {
          postData: JSON.stringify(postData),
        },
        function (response) {
          var response = JSON.parse(response);

          // Provide error (prevents document from being overwritten by code later)
          // Keeps me from having to refactor a ton of stuff - B
          if (response.error || response.success !== true) {
            alert("Oops. Something went wrong.");
            console.log("Merge Endpoint Error:", response.error);
            return;
          }

          onComplete(response.html);
        }
      );
    },

    controller: function (functionName, obj, callback, root, cache) {
      get(
        functionName,
        obj,
        function (response) {
          callback(response);
        },
        false,
        cache,
        root,
        true
      );
    },

    // sb.data.db.service
    service: function (serviceName, functionName, obj, callback) {
      get(
        functionName,
        obj,
        function (response) {
          callback(response);
        },
        false,
        false,
        UPDATE_PATH +
          "?pagodaAPIKey=" +
          appConfig.instance +
          "&service=" +
          serviceName +
          "&do=",
        true,
        false,
        serviceName
      );
    },

    clearCache: function () {
      Requests = [];
    },

    controllerForm: function (functionName, formSelector, callback) {
      submitForm(functionName, formSelector, function (response) {
        callback(response);
      });
    },

    checkAuth: function (callback) {
      checkAuth(function (response) {
        callback(response);
      });
    },

    blueprint: (function () {
      return {
        create: function (objectInfo, blueprint, callback) {
          get(
            "createObjectBlueprint",
            { objectInfo: objectInfo, blueprint: blueprint },
            function (blueprintObj) {
              callback(blueprintObj);
            }
          );
        },

        get: function () {},

        getAll: function (callback) {
          get("getObjectBlueprints", {}, function (blueprints) {
            callback(blueprints);
          });
        },

        update: function (object, callback) {
          get("updateBlueprint", { objectData: object }, function (response) {
            callback(response);
          });
        },
      };
    })(),

    createEventInvoice: function (invoiceObj, callback) {
      get("createEventInvoice", invoiceObj, function (response) {
        callback(response);
      });
    },

    createNewPassword: function (callback, password) {
      if (!password) {
        password = null;
      }

      get("createNewPassword", { pwd: password }, function (response) {
        callback(response);
      });
    },

    getEventInvoice: function (invoiceId, callback) {
      var requestObj = {
        invoiceId: invoiceId,
      };

      get("getEventInvoice", requestObj, function (response) {
        callback(response);
      });
    },

    getEventInvoices: function (eventId, callback) {
      var requestObj = {
        eid: eventId,
      };

      get("getEventInvoices", requestObj, function (response) {
        callback(response);
      });
    },

    getEventInvoiceString: function (invoiceId, callback) {
      var requestObj = {
        invoiceId: invoiceId,
      };

      get("createEventInvoicePDF", requestObj, function (response) {
        callback(response);
      });
    },

    loginUser: function (loginObj, callback) {
      var adminPath =
        appConfig.db.write.replace("_get.php", "_getAdmin.php") +
        "api_webform=true&pagodaAPIKey=" +
        appConfig.instance +
        "&do=";

      get(
        "getIPAddress",
        {},
        function (ip) {
          loginObj.fingerprint = window.navigator.userAgent.replace(/\D+/g, "");
          loginObj.platform = window.navigator.platform;
          loginObj.ip_address = ip;

          get(
            "checkLoginCreds",
            loginObj,
            function (response) {
              callback(response);
            },
            false,
            false,
            adminPath
          );
        },
        false,
        false,
        adminPath
      );
    },

    notes: (function () {
      return {
        getFeed: function (queryObj, callback) {
          get("getObjNoteFeed", { queryObj: queryObj }, function (response) {
            callback(response);
          });
        },
      };
    })(),

    obj: (function () {
      var systemObjects = [];

      function addToObjectList(objType) {
        if (_.indexOf(systemObjects, objType) == -1) {
          systemObjects.push(objType);
        }
      }

      return {
        checkConditions: function (setup, callback) {
          get(
            "checkConditionsList",
            {
              project: setup.object,
              conditions: setup.conditions,
            },
            function (response) {
              callback(response);
            }
          );
        },

        create: function (type, object, callback, childObjs) {
          if (object.length > 0) {
            var requestObj = {
              objectType: type,
              objectData: object,
              multiple: 1,
            };
          } else {
            var requestObj = {
              objectType: type,
              objectData: object,
            };
          }

          if (childObjs) {
            requestObj.childObjs = childObjs;
          }

          if (requestObj.multiple) {
            if (requestObj.objectType === "document") {
              var responseCache = [];

              _.each(requestObj.objectData, function (obj, i) {
                if (usePost(obj, type, childObjs)) {
                  upload("create", obj, function (response) {
                    responseCache.push(response);

                    if (requestObj.objectData.length === responseCache.length) {
                      callback(responseCache);
                    }
                  });
                }
              });
            } else {
              get(
                "createNewObject",
                requestObj,
                function (response) {
                  callback(response);
                },
                undefined,
                undefined,
                undefined,
                true
              );
            }
          } else {
            if (usePost(object, type, childObjs)) {
              upload("create", object, function (response) {
                callback(response);
              });
            } else {
              get(
                "createNewObject",
                requestObj,
                function (response) {
                  callback(response);
                },
                undefined,
                undefined,
                undefined,
                true
              );
            }
          }
        },

        createFromTemplate: function (objectId, callback, childObjs, options) {
          var request = {};

          request.objectId = objectId;
          request.options = options;
          request.childObjs = childObjs;

          get("createFromTemplate", request, function (response) {
            callback(response);
          });
        },

        erase: function (type, typeId, callback) {
          //console.log(this.parentModule, this.permission);
          /*
if(_.indexOf(this.permission.erase, Sandbox.create(Factory, 'databaseConnection').data.cookie.get('uid')) > -1){

						get('deleteObject', {type: type, id: typeId}, function(response){

							callback(response);

						});

					}else{

						Sandbox.create(Factory, 'databaseConnection').dom.alerts.alert('Permission Denied', 'You don\'t have permission to delete this item.', 'error');

						callback(false);

					}
*/

          get(
            "deleteObject",
            { type: type, id: typeId },
            function (response) {
              callback(response);
            },
            undefined,
            undefined,
            undefined,
            true
          );
        },

        getById: function (
          type,
          value,
          callback,
          childObjs,
          includeArchive,
          skipCache
        ) {
          addToObjectList(type);
          var queryObj = { type: type, value: value };

          if (childObjs) {
            queryObj.childObjs = childObjs;
          }

          if (includeArchive) {
            queryObj.includeArchive = true;
          }

          get(
            "getObjectById",
            queryObj,
            function (response) {
              callback(response);
            },
            undefined,
            skipCache
          );
        },

        getAll: function (
          objectType,
          callback,
          getChildObjs,
          paged,
          archive,
          skipCache
        ) {
          if (archive === undefined) {
            archive = false;
          }
          if (skipCache === undefined) {
            skipCache = false;
          }
          addToObjectList(objectType);

          if (paged) {
            paged.paged = true;
          }

          get(
            "getAllObjects",
            {
              objectType: objectType,
              getChildObjs: getChildObjs,
              paged: paged,
              archived: archive,
            },
            function (response) {
              callback(response);
            },
            undefined,
            skipCache
          );
        },

        getBlueprint: function (bpName, callback, getOptions, getSetObj) {
          var blueprint = {};

          if (bpName === "time_entries") {
            blueprint = {
              bp_name: "time_entries",
              blueprint: {
                shift: {
                  name: "Task",
                  fieldType: "title",
                },
                note: {
                  name: "Notes",
                  fieldType: "detail",
                },
                duration: {
                  name: "Duration",
                  fieldType: "duration",
                },
                rate: {
                  name: "Rate",
                  fieldType: "currency",
                },
                hourly_rate: {
                  name: "Rate x Time",
                  fieldType: "currency",
                },
                end_date: {
                  name: "Date/Time Logged",
                  fieldType: "date",
                },
              },
              instance: appConfig.instance,
            };

            callback(blueprint);
          } else {
            if (!_.isUndefined(bpName)) {
              blueprint = _.findWhere(appConfig.Types, {
                bp_name: bpName.includes("#") ? bpName.substr(1) : bpName,
              });
            }

            if (_.isEmpty(blueprint)) {
              var requestObj = {
                objectType: bpName,
              };

              if (getOptions) {
                requestObj.getOptions = getOptions;
              }

              if (getSetObj) {
                requestObj.getSetObj = getSetObj;
              }

              get("getObjectBlueprint", requestObj, function (blueprint) {
                if (_.isEmpty(blueprint)) {
                  requestObj.objectType = !bpName.includes("#")
                    ? "#" + bpName
                    : bpName;

                  get("getObjectBlueprint", requestObj, function (blueprint) {
                    callback(blueprint);
                  });
                } else {
                  callback(blueprint);
                }
              });
            } else {
              if (!getSetObj) {
                blueprint = blueprint.blueprint;
              }

              callback(blueprint);
            }
          }
        },
        fgGetCompaniesWithoutParents: function(callback){
            get("getCompaniesWithoutParent", {actionItems: true}, function (response) {
              callback(response);
            });
        },
        fgGetCompaniesWithoutContactCompany: function(callback){
          get("getCompaniesWithoutContactCompany", {}, function (response) {
            callback(response);
          });
        },
        fgUserAccounts: function(email, callback){
            get("getUserAccountsNew", {email:email}, function (response) {
                callback(response);
              });
        },

        /*
            @PARAMS - user id
            If an email is associated with multiple users across multiple instances, call this fn with that user id.
            it will query across instances, find the user obj with the linked email and the disable the account and
            archive the user obj.
            *invoke with the same id to restore and enable the user object.
            Use case - Foundation Group has inadvertantly created multiple portals and multiple portal users. Because
            application login works off of email, it's possible to have multiple instances display on login screen
            for portal client.
        */
        fgRemoveInstanceUser: function(id, callback){
            get("fgRemoveInstanceUser", {id:id}, function (response) {
                callback(response);
              });
        },
        fgContactList: function(company, callback){
            get("fgContactList", {organization:company}, function (response) {
                callback(response);
              });
        },
        fgRemoveToken: function(contactId, callback){
            get("fgRemoveToken", {contactId:contactId}, function (response) {
                callback(response);
              });
        },
        // fgShareWithContacts: function(contact, callback){
        //     get("fgShareWithContacts", {contact:contact}, function (response) {
        //         callback(response);
        //       });
        // },

        getCounts: function (objectType, groupBy, where, callback) {
          get(
            "countObjs",
            {
              objectType: objectType,
              groupBy: groupBy,
              where: where,
            },
            function (response) {
              callback(response);
            }
          );
        },

        getGroupSum: function (objectType, field, queryObj, callback) {
          get(
            "groupSum",
            { objectType: objectType, field: field, queryObj: queryObj },
            function (response) {
              callback(response);
            }
          );
        },

        getOptions: function (objectType, field, callback) {
          get(
            "getOptions",
            { objectType: objectType, field: field },
            function (response) {
              callback(response);
            }
          );
        },

        getSearchPath: function (objectType, selection, where, opts) {
          var ret =
            getPathToUse(appConfig, false) +
            "search&searchType=" +
            objectType +
            "&value={query}";
          if (!_.isEmpty(opts) && typeof opts === "object") {
            if (typeof opts.limit === "number") {
              ret += "&limit=" + opts.limit;
            }
            if (opts.searchUids === true) {
              ret += "&searchUids=1";
            }
            if (opts.parentObjectType) {
              ret += "&parentObjectType=" + opts.parentObjectType;
            }
          }
          if (!_.isEmpty(selection)) {
            ret += "&selection=" + JSON.stringify(selection);
          }
          if (!_.isEmpty(where)) {
            ret += "&where=" + JSON.stringify(where);
          }
          return ret;
        },

        getStaffAccessLevels: function (callback) {
          get("getStaffAccessLevels", {}, function (response) {
            callback(response);
          });
        },

        getStaffDepartments: function (callback) {
          get("getStaffDepartments", {}, function (response) {
            callback(response);
          });
        },

        getInstanceObjects: function () {
          return systemObjects;
        },

        getSum: function (objectType, field, queryObj, callback) {
          get(
            "sum",
            { objectType: objectType, field: field, queryObj: queryObj },
            function (response) {
              callback(response);
            }
          );
        },

        getWhere: function (type, queryObj, callback, skipCache) {
          // alias for childObjs
          if (queryObj.hasOwnProperty("select")) {
            queryObj.childObjs = queryObj.select;
            delete queryObj.select;
          }

          if (typeof queryObj.childObjs === "undefined") {
            childObjs = 0;
          } else {
            childObjs = queryObj.childObjs;
            delete queryObj.childObjs;
          }

          var getJust = {};
          if (queryObj.hasOwnProperty("getJust")) {
            getJust = queryObj.getJust;
            delete queryObj.getJust;
          }

          addToObjectList(type);

          get(
            "getObjectsWhere",
            {
              objectType: type,
              queryObj: queryObj,
              getChildObjs: childObjs,
              getJust: getJust,
            },
            function (response) {
              callback(response);
            },
            undefined,
            skipCache
          );
        },

        getWhereBy: function (type, queryObj, callback) {
          if (typeof queryObj.childObjs === "undefined") {
            childObjs = 0;
          } else {
            childObjs = queryObj.childObjs;
            delete queryObj.childObjs;
          }

          addToObjectList(type);

          get(
            "getObjectsWhereBy",
            { objectType: type, queryObj: queryObj, getChildObjs: childObjs },
            function (response) {
              callback(response);
            }
          );
        },

        getWhereWith: function (
          objectType,
          queryObj,
          childObj,
          childObjKey,
          callback
        ) {
          if (typeof queryObj.childObjs === "undefined") {
            childObjs = 0;
          } else {
            childObjs = queryObj.childObjs;
            delete queryObj.childObjs;
          }

          addToObjectList(objectType);

          get(
            "getObjectsWhereWith",
            {
              objectType: objectType,
              queryObj: queryObj,
              childObj: childObj,
              childObjKey: childObjKey,
              getChildObjs: childObjs,
            },
            function (response) {
              callback(response);
            }
          );
        },

        notify: function (request, callback) {
          /*
						{
							title:String,
							details:String,
							producer:objectId,
							color:String,
							icon:String,
							type: 'general' | 'mention' | 'question' | 'reminder' | 'assignment'
						}
					*/

          get(
            "notify",
            request,
            function (response) {
              callback(response);
            },
            undefined,
            undefined,
            undefined,
            true
          );
        },

        parentModule: "",

        permission: {},

        postComment: function (request, callback, childObjs) {
          var req = {};

          req.objectData = request;
          req.getChildObjs = childObjs;

          get("postComment", req, function (response) {
            callback(response);
          });
        },

        addRCATagByType: function (request, tagType, callback) {
          var req = {
            objectData: {
              _object: request,
              tagType: tagType
            }
          };

          get("addRCATagByType", req, function (response) {
            callback(response);
          });
        },

        removeTag: function (objectIds, tag, callback) {
          var req = {
            tagId: tag,
            typeId: objectIds,
            type: "",
          };

          get("removeTagFromObject", req, function (response) {
            callback(response);
          });
        },

        restore: function (type, typeId, callback) {
          get("restoreObjs", { type: type, id: typeId }, function (response) {
            callback(response);
          });
        },

        runSteps: function (steps, objId, callback, verbose) {
          get(
            "runSteps",
            {
              objId: objId,
              steps: steps,
            },
            function (response) {
              callback(response);
            },
            undefined,
            undefined,
            undefined,
            true,
            verbose
          );
        },

        save: function (saveData, callback, childObjs) {
          if (childObjs === undefined) {
            childObjs = 0;
          }

          get(
            "saveObjects",
            { saveData: saveData, childObjs: childObjs },
            function (response) {
              callback(response);
            },
            undefined,
            undefined,
            undefined,
            true
          );
        },

        setModuleId: function (modId) {
          this.parentModule = modId;

          var permission = _.where(appConfig.permissions, {
            pageModuleId: this.parentModule,
          });

          if (permission.length > 0) {
            this.permission = permission;
          }
        },

        sum: function (objectType, field, where, callback) {
          get(
            "simpleSum",
            {
              objectType: objectType,
              field: field,
              where: where,
            },
            function (response) {
              callback(response);
            }
          );
        },

        update: function (type, object, callback, childObjs) {
          //console.log(this.parentModule, this.permission);
          /*
if(_.indexOf(this.permission.edit, Sandbox.create(Factory, 'databaseConnection').data.cookie.get('uid')) > -1 || _.isEmpty(this.permission)){

						var requestObj = {
							objectType: type,
							objectData: object
						};

						get('updateObject', requestObj, function(response){

							callback(response);

						});

					}else{

						Sandbox.create(Factory, 'databaseConnection').dom.alerts.alert('Permission Denied', 'You don\'t have permission to edit this item.', 'error');

						callback(false);

					}
*/

          // if object type is not provided, args are shifted forward
          if (typeof type === "object" && typeof object === "function") {
            callback = object;
            object = _.clone(type);
            type = "";
          }

          var requestObj = {
            objectType: type,
            objectData: object,
          };

          if (object.getChildObjs) {
            requestObj.getChildObjs = object.getChildObjs;
          }
          if (childObjs !== undefined) {
            requestObj.getChildObjs = childObjs;
          }

          if (usePost(object, type, childObjs)) {
            upload("update", object, function (response) {
              callback(response);
            });
          } else {
            get(
              "updateObject",
              requestObj,
              function (response) {
                callback(response);
              },
              undefined,
              undefined,
              undefined,
              true
            );
          }
        },

        updateState: function (update, callback, verbose) {
          var request = {
            objectId: parseInt(update.objectId),
            newState: parseInt(update.newState),
            link: update.link,
          };

          if (verbose === undefined) {
            verbose = false;
          }

          if (!_.isEmpty(update.stateProperty)) {
            request.stateProperty = update.stateProperty;
          }

          if (update.isRecurring) {
            request.isRecurring = 1;
            request.between = {
              start: update.between.start,
              end: update.between.end,
            };
          }

          if (update.supressActions === true) {
            request.supressActions = true;
          }

          get(
            "updateState",
            request,
            function (response) {
              callback(response);
            },
            undefined,
            undefined,
            undefined,
            true,
            verbose
          );
        },
      };
    })(),

    setAppConfig: function (newConfig) {
      appConfig = newConfig;

      if (appConfig.hasOwnProperty("api_webform")) {
        rootPath =
          appConfig.db.write +
          "pagodaAPIKey=" +
          appConfig.instance +
          "&api_webform=true&do=";
        postPath =
          appConfig.db.post +
          "pagodaAPIKey=" +
          appConfig.instance +
          "&api_webform=true&do=";
      } else {
        if (appConfig.hasOwnProperty("db")) {
          rootPath =
            appConfig.db.write + "pagodaAPIKey=" + appConfig.instance + "&do=";
          postPath =
            appConfig.db.post + "pagodaAPIKey=" + appConfig.instance + "&do=";
        } else {
          rootPath =
            appConfig.db_write + "pagodaAPIKey=" + appConfig.instance + "&do=";
          postPath =
            appConfig.db_post + "pagodaAPIKey=" + appConfig.instance + "&do=";
        }
      }
    },

    setAPIPath: function (newPath) {
      PATH_CHANGED = 1;
      GET_PATH = newPath;
      UPDATE_PATH = newPath;
    },

    setToken: function (token) {
      bentoToken = token;
      if (fileAPI && typeof fileAPI.setToken === "function") {
        fileAPI.setToken(token);
      }
    },

    test: function () {
      get("test", {}, function (response) {
        console.log(response);
      });
    },

    trackXhr: trackXhr,
  };
})();

if (IN_NODE_ENV) {
  exports.api = databaseConnection;
}
