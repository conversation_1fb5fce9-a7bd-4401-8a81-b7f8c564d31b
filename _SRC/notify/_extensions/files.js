var IN_NODE_ENV = (typeof module !== 'undefined' && module.exports);
if (IN_NODE_ENV) {

	// Establish the module
	function _fileAPI(){};
	module.exports = _fileAPI;

	// Get dependencies
	require('dotenv').config();

	var appConfig = {
		instance: global.dbAuth.dbPagodaAPIKey
	}

}

var fileAPI = (function(){

/*
	var postPath = 'https://pagoda.voltz.software/_repos/rickyvoltz/notify/pagoda/_coredev/files/post/?pagodaAPIKey='+ appConfig.instance +'&do=',
		getPath = 'https://pagoda.voltz.software/_repos/rickyvoltz/notify/pagoda/_coredev/files/get/?pagodaAPIKey='+ appConfig.instance +'&do=',
		deletePath = 'https://pagoda.voltz.software/_repos/rickyvoltz/notify/pagoda/_coredev/files/delete/?pagodaAPIKey='+ appConfig.instance +'&do=',
		bucket = 'https://pagoda.voltz.software/_repos/rickyvoltz/pagoda/_files/_instances/';
*/
	
	var bucket = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/';
	var deletePath = '../../api/files/delete/?pagodaAPIKey='+ appConfig.instance +'&do=';
	var getPath = '../../api/_get.php?api_webform=true&pagodaAPIKey='+ appConfig.instance +'&do=';
	var postPath = '../../api/files/post/?pagodaAPIKey='+ appConfig.instance +'&do=';
	var bentoToken = '';
		
	function get(phpFunction, obj, callback, i){

		$.ajax({
			type: 'post',
			beforeSend: function (xhr) {

				xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
			    xhr.setRequestHeader('bento-token', bentoToken);
				
			},
			url: getPath + phpFunction,
			data: { json: JSON.stringify(obj) },
			success: function (response) {

				callback(response);
				
			},
			xhrFields: {
				withCredentials: true
			},
			crossDomain: true,
			error: function (jqXHR, status, error) {

				if(jqXHR.status == 404){
					
					if(typeof i === 'undefined'){
						i = 1;
					}
					i++;
					if(i <= 6){
						
						setTimeout(function(){
							
							get(phpFunction, obj, callback, i);
							
						}, i*i*100);
						
					}else{
						
						alerts.alert('Error!', 'Bad connection--try refreshing your page.', 'error');
						
					}
					
				}
				
			}
			
		});	

	}
	
	function deleteItem(phpFunction, obj, callback, i){

		$.ajax({
			type: 'post',
			url: deletePath + phpFunction,
			data: { json: JSON.stringify(obj) },
			success: function (response) {

				callback(response);
				
			},
			
			error: function (jqXHR, status, error) {

				if(jqXHR.status == 404){
					
					if(typeof i === 'undefined'){
						i = 1;
					}
					i++;
					if(i <= 6){
						
						setTimeout(function(){
							
							get(phpFunction, obj, callback, i);
							
						}, i*i*100);
						
					}else{
						
						alerts.alert('Error!', 'Bad connection--try refreshing your page.', 'error');
						
					}
					
				}
				
			}
			
		});	

	}
	
	function uploadFile(phpFunction, file, metaData, callback, i){
		
		$.ajax({
			url: postPath + phpFunction,
			cache: false,
			contentType: false,
			processData: false,
			data: file,                         
			type: 'POST',
			success: function (response) {

				callback(response);
				
			},
			error: function (jqXHR, status, error) {

				if(jqXHR.status == 404){
					
					if(typeof i === 'undefined'){
						i = 1;
					}
					i++;
					if(i <= 6){
						
						setTimeout(function(){
							
							uploadFile(phpFunction, file, metaData, callback, i);
							
						}, i*i*100);
						
					}else{
						
						alerts.alert('Error!', 'Bad connection--try refreshing your page.', 'error');
						
					}
					
				}
				
			}
		});	

	}
	
	return {
		
		changeInstance: function(apiKey, env){
			
			if(env){
				deletePath = '../../api/files/delete/?pagodaAPIKey='+ apiKey +'&do=';
				getPath = '../../api/files/get/?pagodaAPIKey='+ apiKey +'&do=';
				postPath = '../../api/files/post/?pagodaAPIKey='+ apiKey +'&do=';				
			}else{
				deletePath = '../../api/files/delete/?pagodaAPIKey='+ apiKey +'&do=';
				getPath = '../../api/files/get/?pagodaAPIKey='+ apiKey +'&do=';
				postPath = '../../api/files/post/?pagodaAPIKey='+ apiKey +'&do=';
			}
		
			return true;
			
		},
		
		changePaths: function(apiKey){
			
			postPath = appConfig.files.write +'pagodaAPIKey='+ apiKey +'&do=';
			getPath = appConfig.files.read +'pagodaAPIKey='+ apiKey +'&do=';
			deletePath = appConfig.files.delete +'pagodaAPIKey='+ apiKey +'&do=';
			
			return true;
			
		},
		
		rename: function(file, metaData, callback){
									
			uploadFile('renameFile', null, metaData, function(response){
				
				callback(response);
				
			});
			
		},
		
		update: function(file, metaData, callback){
			
			var fileData = new FormData();                  
				fileData.append('file', file);
				
			_.each(metaData, function(val, key){
				fileData.append(key, val);
			});
			
			uploadFile('update', fileData, metaData, function(response){
				
				callback(response);
				
			});
			
		},
		
		upload: function(file, metaData, callback){
			
			function objectToFormData(obj, form, namespace) {
			    
			  var fd = form || new FormData();
			  var formKey;
		
			  for(var property in obj) {
			    if(obj.hasOwnProperty(property)) {
			      
			      if(namespace) {
			        formKey = namespace + '[' + property + ']';
			      } else {
			        formKey = property;
			      }
		
			      // if the property is an object, but not a File,
			      // use recursivity.
			      if(typeof obj[property] === 'object' && !(obj[property] instanceof File)) {
			        
			        objectToFormData(obj[property], fd, formKey);
			        
			      } else {
			        
			        // if it's a string or a File object
			        fd.append(formKey, obj[property]);
			      }
			      
			    }
			  }
			  
			  return fd;
			    
			}
			
			var formData = _.clone(metaData);
			formData._file = file;
			
			var z = objectToFormData(formData);
			var xhr = new XMLHttpRequest;
			var postPath = '../../api/_post.php?pagodaAPIKey='+ appConfig.instance +'&do=';
			
			xhr.open('POST', postPath + 'upload', true);
			xhr.setRequestHeader('bento-token', bentoToken);
			xhr.onreadystatechange = function(){
	
				if(xhr.readyState === 4 && xhr.status === 200) {
	
					callback(JSON.parse(xhr.responseText));
					
				}
				
			}
			
			xhr.send(z);
			
		},
		
		delete: function(fileId, callback){
			
			deleteItem('delete', {id: fileId}, function(response){
				
				callback(response);
				
			});
			
		},
		
		getAll: function(callback){
			
			get('getAll', {}, function(response){
						
				callback(response);
				
			});
			
		},
		
		getWhere: function(where, callback){
			
			var queryObj = {
				objectType: 	'file_meta_data'
				, queryObj: 	where
			};

			get('getObjectsWhere', queryObj, function(response){
						
				callback(response);
				
			});
			
		},
		
		open: function(file, callback){
			
			var substrings = ['.png', '.gif', '.jpeg', '.pdf'];
			
			if (new RegExp(substrings.join("|")).test(file.file_name)) {
				
				if(callback){
					callback(true);
				}
				
				return window.open(bucket + appConfig.instance +'/'+ file.loc);
    
			}else{
				
				var path = bucket + appConfig.instance +'/'+ file.loc;
				var blob = null;
				var xhr = new XMLHttpRequest(); 
				
				xhr.open("GET", path); 
				xhr.responseType = "blob";//force the HTTP response, response-type header to be blob
				xhr.onload = function() 
				{
				    blob = xhr.response;//xhr.response is now a blob object
				    console.log(blob);
				    var myReader = new FileReader();
					myReader.addEventListener("loadend", function(e)
					{
					        var buffer = e.srcElement.result;//arraybuffer object
					});
					myReader.onloadend = function (e) {
				        var arr = (new Uint8Array(e.target.result)).subarray(0, 4);
				        var header = '';
				        for (var i = 0; i < arr.length; i++) {
				            header += arr[i].toString(16);
				        }
	
				        // Check the file signature against known types
				        var type = 'unknown';
				        switch (header) {
				            case '89504e47':
				            case '89504E47':
				                type = '.png';
				                break;
				            case '47494638':
				                type = '.gif';
				                break;
				            case 'ffd8ffe0':
				            case 'ffd8ffe1':
				            case 'ffd8ffe2':
				            case 'FFD8FFDB':
				            case 'FFD8FFE0':
				                type = '.jpeg';
				                break;
				            case '25504446':
				                type = '.pdf';
				                break;
				            case '504b0304':
				            case '504B0304':
				            	type = '.zip';
				            	break;
				        }
				    	
				    	file.name = file.file_name;
				    	file.file_name = file.file_name + type;
				    	file.new_name = file.file_name;
				    	file.loc = file.oid_type +'/'+ file.oid +'/'+ file.new_name;
				    	
				    	databaseConnection.controller('renameFile', file, function(){
					    	
					    	databaseConnection.obj.update('file_meta_data', file, function(){
						    	
						    	if(callback){
									callback(true);
								}
								
						    	return window.open(bucket + appConfig.instance +'/'+ file.loc);
						    	
					    	});
					    	
				    	});
				    				    	    
				    };
					myReader.readAsArrayBuffer(blob);
					
				}
				xhr.send();
				
			}
			
		},
		
		read: function(file, callback){

			function loadHandler(event) {
				
				var csv = event.target.result;
				processData(csv);
			}
			
			function processData(csv) {
				/*
var allTextLines = csv.split(/\r\n|\n/);
				var lines = [];
				for (var i=0; i<allTextLines.length; i++) {
					var data = allTextLines[i].split(';');
					var tarr = [];
					for (var j=0; j<data.length; j++) {
// 						tarr.push(data[j].split(','));
						// ignore commas within double-quotes
						tarr.push(data[j].match(/(".*?"|[^",\s]+)(?=\s*,|\s*$)/g));
					}
					lines.push(tarr[0]);
				}
*/				
				var lines = $.csv.toObjects(csv);
				callback(lines);
			}
			
			function errorHandler(evt) {
				if(evt.target.error.name == "NotReadableError") {
					alert("Cannot read file !");
				}
			}
			
			if(file.constructor === File){
				
				var reader = new FileReader();
				reader.readAsText(file);
				
				reader.onload = loadHandler;
				reader.onerror = errorHandler;
				
			}else{
				
				var blob = null;
				var xhr = new XMLHttpRequest(); 
				xhr.open("GET", bucket + appConfig.instance +'/'+ file.loc); 
				xhr.responseType = "blob";//force the HTTP response, response-type header to be blob
				xhr.onload = function() 
				{
				    blob = xhr.response;//xhr.response is now a blob object
				    
				    var reader = new FileReader();
					// Read file into memory as UTF-8      
					reader.readAsText(blob);
					// Handle errors load
					reader.onload = loadHandler;
					reader.onerror = errorHandler;
				
				}
				xhr.send();
				
			}
			
		},
		
		setToken: function (token) {
			
			bentoToken = token;
			
		},
		
		getURL: function(file){

			if(!file){
				return '';
			}
			
			if (file.instance) {
				
				return bucket + file.instance +'/'+ file.loc;
				
			} else {
				
				return bucket + appConfig.instance +'/'+ file.loc;
				
			}
			
		}
		
	}
	
})();

if (IN_NODE_ENV) {
	_fileAPI.fileAPI = fileAPI;
} 