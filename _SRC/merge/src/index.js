"use strict";

const express = require("express");
var path = require("path");
var bodyParser = require("body-parser");
var cookieParser = require("cookie-parser");
var cors = require("cors");
var needle = require("needle");
var Mustache = require("mustache");
require("dotenv").config();

var core = require("./modules/core-module.js");
var db = require("./modules/_database.js");
var version = require("./_version.js");

// Constants
const PORT = 8084;
const HOST = "0.0.0.0";

// App
const app = express();

//body parser json options
app.use(bodyParser.json({ limit: "80mb" }));
app.use(
  bodyParser.urlencoded({
    limit: "80mb",
    extended: true,
    parameterLimit: 5000000,
  })
);

//app.use(express.json({ limit: '50mb' }));
//app.use(express.json());
// app.use(express.urlencoded({
//   extended: false
// }));

//app.use(require('./routes'));

//use cookies on nodejs
app.use(cookieParser());

//app use cors
//app.use(cors());
//app.use(cors({
//  origin: '*'
//}));

//enables cors
const corsOptions = {
  origin: "*",
  methods: ["GET", "PUT", "POST", "DELETE", "OPTIONS"],
  optionsSuccessStatus: 200, // some legacy browsers (IE11, various SmartTVs) choke on 204
  credentials: true, //Credentials are cookies, authorization headers or TLS client certificates.
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "device-remember-token",
    "Access-Control-Allow-Origin",
    "Origin",
    "Accept",
  ],
  exposedHeaders: "Content-Range,X-Content- Range",
};

app.use(cors(corsOptions)); //adding cors middleware to the express with above configurations
//app.options('*', cors());

app.get("/", (req, res) => {
  res.send("Bento Merge APP NodeJS V. " + version.PAGODA_APP_VERSION);
});

app.post('/getByIdTest', (req, res) => {
  try{

    global.dbAuth = {
      dbToken: obj.authData.varToken,
      dbSeries: obj.authData.varSeries,
      dbUid: obj.authData.varUid,
      dbInstance: obj.authData.varInstance,
      dbPagoda: obj.authData.varPagoda,
      dbPagodaAPIKey: "QkBDTOeqxq",
    };

    console.log('setting conexion: ', )

    core.readById({contextId: 5511667}, function (obj) {
      console.log('read object: ', obj);
        res.send({
          success: true,
          obj: obj,
        });
      });

  }catch(e){
    console.log('error: ', e.message);
  }
});

/** Merge Service Start */
app.post("/mergeService", (req, res) => {
  try {
    var DataVars = req.body.postData;
    var isJson = core.tryParseJSONObject(DataVars);

    if (isJson) {
      var obj = JSON.parse(DataVars);
    } else {
      var obj = DataVars;
    }

    if (!obj) {
      console.log('error on merge service: ', e.message);
      return '_';
    }

    var contextId = obj.contextId || "";
    var templateId = obj.templateId || "";
    var templateHtml = obj.templateHtml || "";
    var mergeVars = obj.mergeVars || "";

    global.dbAuth = {
      dbToken: obj.authData.varToken,
      dbSeries: obj.authData.varSeries,
      dbUid: obj.authData.varUid,
      dbInstance: obj.authData.varInstance,
      dbPagoda: obj.authData.varPagoda,
      dbPagodaAPIKey: obj.authData.varPagodaAPIKey,
    };
    console.log('auth: ', global.dbAuth);

    if (templateId) {
      core.readById({contextId: templateId}, function (obj) {
        templateHtml = obj.html_string || "";

        core.getMerged(templateHtml, mergeVars, contextId, function (html) {

          console.log('success template: ', html);
          res.send({
            success: true,
            html: html,
          });
        });
      });
    } else {
      core.getMerged(templateHtml, mergeVars, contextId, function (html) {
        console.log('success without template: ', html);
        res.send({
          success: true,
          html: html,
        });
      });
    }
  }catch(e){
    console.log('error on merge service: ', e.message);
    return 'merge_service_error';
  }
  console.log('finishhed..');

  return;
});

/** Merge Service End */

app.get("/dataenv", (req, res) => {
  var data_endpoint = process.env.DATA_ENDPOINT;
  //var data_test = process.env.BENTO_DATABASE_URL;
  //var endpoint = data_endpoint+':8080/api/_get.php?MERGE_SERVICE=true';
  //console.log(endpoint);
  res.send("Data Endpoint in ENV is: " + data_endpoint);
});

app.post("/api-test", (req, res) => {
  var postData = req.body.postData;

  console.log(req.headers);

  console.log(postData);

  res.send(postData);
});

app.get("/viewenv", (req, res) => {
  var TEST_ENVIRONMENT = process.env.TEST_ENVIRONMENT;
  var data_endpoint = process.env.DATA_ENDPOINT;

  if (TEST_ENVIRONMENT == "ON") {
    var port = ":8080";
    var http = "http://";
  } else {
    var port = "";
    var http = "https://";
  }

  var data = {
    port: port,
    http: http,
  };
  res.send(data);
});

app.get("/cookies", (req, res) => {
  var TEST_ENVIRONMENT = process.env.TEST_ENVIRONMENT;
  var data_endpoint = process.env.DATA_ENDPOINT;

  if (TEST_ENVIRONMENT == "ON") {
    var port = ":8080";
    var http = "http://";
  } else {
    var port = "";
    var http = "https://";
  }

  var varToken = req.cookies["token"];
  var varSeries = req.cookies["series"];
  var varUid = req.cookies["uid"];
  var varInstance = req.cookies["instance"];
  var varPagoda = req.cookies["pagoda"];

  var cookiesData = {
    token: varToken,
    series: varSeries,
    uid: varUid,
    instance: varInstance,
    pagoda: varPagoda,
  };

  res.json({ cookies: cookiesData });

  return;
});

app.get("/api", (req, res) => {
  db.api.obj.getAll(
    "invoice_system",
    function (config) {
      console.log(config);
      //res.send(config);
      res.send(config[0].billing_address);
      // CachedMergeTags['instance_address_fix'] = config[0].billing_address;
      // callback(config[0].billing_address);
    },
    {
      billing_address: true,
    }
  );
  return;
});

app.get("/apidata", (req, res) => {
  var options = {
    json: false,
    headers: {
      Cookie:
        "gs_v_GSN-752757-B=email:<EMAIL>; pagoda=b1187b464b9066d28a4c411961008f72; uid=920410; user_type=0; instance=dev_test_env;series=c6c484bb9f7608d4e7bf1bf96d98f5b46b90d684bef7db7395d6bc0ae9eca9c16cfce59ab96f927584cb4d765be5480408f628632655cae4dba803325bcda576; token=49f854f34250d9051ac326b0b53c311363dc9ac02c61b02a2024ff160dfd71537fb50d0e3b862a2bd5429a91ee8abbf91188636a864007df1672c9d720797f26;",
      "bento-token":
        "49f854f34250d9051ac326b0b53c311363dc9ac02c61b02a2024ff160dfd71537fb50d0e3b862a2bd5429a91ee8abbf91188636a864007df1672c9d720797f26",
      "Content-Type": "application/x-www-form-urlencoded",
    },
  };

  needle.post(
    "http://host.docker.internal:8080/api/_get.php?do=api&service=MergeService&pagodaAPIKey=rickyvoltz",
    {
      // json variables
      name: "Zach",
      job: "PHP Developer",
    },
    options,
    function (error, response) {
      // you can pass params as a string or as an object.
      if (error) {
        console.error(error);
      }
      if (!error && response.statusCode == 200) res.send(response.body);
    }
  );
});

app.post("/request", (req, res) => {
  var postData = req.body;
  console.log(req.headers);
  console.log(postData);
  //res.send(postData);

  res.json({ name: req.body.name, job: req.body.job });
});

app.get("/merged", (req, res) => {
  var contextId = req.body.contextId;
  var context = req.body.context;

  if (contextId) {
    var contextData = {
      contextId: contextId,
      context: context,
    };
  } else {
    var contextData = null;
  }

  //console.log(contextData);

  // const templateHtml = '<h1>{{today}}</h1><h3>{{now}}</h3><p>{{instance_address}}<p>'; // Read this out of the req, but initially, hardcode this in here to test
  const templateHtml = "<h1>{{today}}</h1><h3>{{now}}</h3><p><p>"; // Read this out of the req, but initially, hardcode this in here to test

  // !TODO: Get this working with tags with spaces and capitals
  const tempHtml = "<h1>{{Today}}</h1><h3>{{Now}}</h3><p>{{Venue Address}}<p>"; // Read this out of the req, but initially, hardcode this in here to test

  var newHtml = tempHtml.replace(/\s+/g, "_");
  var newTemp = newHtml.toLowerCase();

  core.getMerged(newTemp, false, contextId, function (html) {
    res.send(html);
  });

  return;
});

app.post("/mergePost", (req, res) => {
  var contextId = req.body.contextId;
  var contextObjectType = req.body.contextObjectType;
  var template = req.body.template;

  if (contextId) {
    var contextData = {
      contextId: contextId,
      contextObjectType: contextObjectType,
    };
  } else {
    var contextData = null;
  }

  const tempHtml = "<h1>{{Today}}</h1><h3>{{Now}}</h3><p>{{Propossal Html}}<p>"; // Read this out of the req, but initially, hardcode this in here to test

  var newHtml = tempHtml.replace(/\s+/g, "_");
  var newTemp = newHtml.toLowerCase();

  core.getMerged(newTemp, false, contextId, function (html) {
    res.send(html);
  });

  return;
});

app.post("/mergeGetWhere", (req, res) => {
  var contextId = req.body.contextId;
  var contextObjectType = req.body.contextObjectType;
  var template = req.body.template;

  if (contextId) {
    var contextData = {
      contextId: contextId,
      contextObjectType: contextObjectType,
    };
  } else {
    var contextData = null;
  }

  const tempHtml = "<h1>{{Today}}</h1><h3>{{Now}}</h3><p>{{Client Address}}<p>"; // Read this out of the req, but initially, hardcode this in here to test

  var newHtml = tempHtml.replace(/\s+/g, "_");
  var newTemp = newHtml.toLowerCase();

  core.getMerged(newTemp, false, contextId, function (html) {
    res.send(html);
  });
});

app.listen(PORT, HOST);

console.log('config...');
console.log(process.env);
console.log(`Running on http://${HOST}:${PORT}`);
