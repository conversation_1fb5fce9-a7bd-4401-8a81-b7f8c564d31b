var bodyParser = require("body-parser");
var needle = require("needle");
var fs = require("fs");
var path = require("path");
var libDir = path.join(__dirname, "/tags");

var _ = require("underscore-node");
var db = require("./_database.js");

function formatPhoneNo(unformatted) {
  var formatted = "";

  if (unformatted) {
    ///removes any whitespace chars
    unformatted = unformatted.replace(/\s/g, "");
    unformatted = unformatted.replace(/\D/g, "");

    formatted = "(" + unformatted.slice(0, 3) + ")";
    formatted += " " + unformatted.slice(3, 6);
    formatted += "-" + unformatted.slice(6);
  }

  return formatted;
}

function getMerged(templateHtml, mergeVars, contextId, onComplete) {
  var files = getAllFiles(libDir);

  // Find the merge function from the tags directory
  files.forEach((file) => {
    // Get current file name and function
    var filePath = path.join(file.dirName, file.fileName);
    var fileName = path.parse(file.fileName).name;

    require(filePath);
  });

  function getMergeFunction(tagName) {
    var mergeFunction = false;

    // Find the merge function from the tags directory
    files.forEach((file) => {
      // Get current file name and function
      var filePath = path.join(file.dirName, file.fileName);
      var fileName = path.parse(file.fileName).name;

      if (fileName === tagName) {
        mergeFunction = require(filePath);
      }
    });

    return mergeFunction[tagName];
  }

  function replaceMergeTagsWithFieldHTML(obj, callback) {
    function returnReplacedHTML(html) {
      if (typeof callback === "function") {
        callback(html);
      }
    }

    db.api.obj.getBlueprint(
      obj.object_bp_type,
      function (bp) {
        var tags = [];
        if (templateHtml) {
          var tags = templateHtml.split("{{");
          if (tags.length > 1) {
            _.each(tags, function (tag, i) {
              if (tag.includes("this.")) {
                tag = tag.split("}")[0];
                tags[i] = tag.split("this.")[1];
                return;
              }
            });
            tags.splice(0, 1);
            tags = _.uniq(tags);
          }
        }

        var fields = _.filter(bp.blueprint, function (field, key) {
          if (!field.is_archived && _.contains(tags, field.name)) {
            field.key = key;
            return field;
          }
        });

        var i = 1;
        var count = fields.length;

        if (!count) {
          returnReplacedHTML(templateHtml);
        } else {
          _.each(fields, function (field) {
            // Doing this doesn't seem efficient
            var mergeFunc = getMergeFunction(field.fieldType);

            switch (field.fieldType) {
              case "image":
                field.options.callback = function (response) {
                  templateHtml = templateHtml.replaceAll(
                    "{{this." + field.name + "}}",
                    response
                  );

                  if (i == count) {
                    returnReplacedHTML(templateHtml);
                  }

                  i++;
                };

                if (typeof mergeFunc === "function") {
                  mergeFunc(field.key, null, obj, field.options);
                }

                break;

              case "table":
                (field.options.page = 0),
                  (field.options.pageLength = 100),
                  (field.options.pageThrough = true),
                  (field.options.callback = function (response) {
                    templateHtml = templateHtml.replaceAll(
                      "{{this." + field.name + "}}",
                      response
                    );

                    if (i == count) {
                      returnReplacedHTML(templateHtml);
                    }

                    i++;
                  });

                if (typeof mergeFunc === "function") {
                  mergeFunc(field.key, null, obj, field.options);
                }

                break;

              default:
                if (i == count) {
                  returnReplacedHTML(templateHtml);
                }

                i++;

                break;
            }
          });
        }
      },
      false,
      true
    );
  }

  var contextData = null;
  var childObjs = {};
  if (contextId) {
    var contextData = {
      contextId: contextId,
    };
    var childObjs = {
      related_object: true,
    };
  }

  readById(
    contextData,
    function (obj) {
      if (obj.object_bp_type == "contracts") {
        contextId = obj.related_object;
        obj = obj.related_object;
      }

      replaceMergeTagsWithFieldHTML(obj, function (templateHtml) {
        db.api.obj.runSteps(
          {
            merge: {
              template: templateHtml,
              mergeVars: mergeVars,
            },
          },
          contextId,
          function (merged) {
            if (merged && merged["msg"] && merged["msg"]["memo"]) {
              templateHtml = merged["msg"]["memo"];
            }

            var tags = [];
            if (templateHtml) {
              var tags = templateHtml.split("{{");
              if (tags.length > 1) {
                _.each(tags, function (tag, i) {
                  tags[i] = tag.split("}")[0];
                  return;
                });
                tags.splice(0, 1);
                tags = _.uniq(tags);
              }
            }

            var i = 1;
            var count = tags.length;

            if (!count) {
              onComplete(templateHtml);
            } else {
              _.each(tags, function (tag) {
                var mergeFunc = getMergeFunction(tag);

                if (typeof mergeFunc === "function") {
                  var fieldName = null;
                  var options = {
                    callback: function (response) {
                      templateHtml = templateHtml.replaceAll(
                        "{{" + tag + "}}",
                        response
                      );

                      if (i == count) {
                        onComplete(templateHtml);
                      }

                      i++;
                    },
                  };

                  mergeFunc(fieldName, null, obj, options);
                } else {
                  if (i == count) {
                    onComplete(templateHtml);
                  }

                  i++;
                }
              });
            }
          },
          true // receive the memo in the response
        );
      });
    },
    childObjs
  );
}

function readById(context, callback, childObjs) {
  var objId = context.contextId ? context.contextId : "";
  var objectType = context.contextObjectType ? context.contextObjectType : "";

  if (!objId) {
    return false;
  }

  db.api.obj.getById(
    objectType,
    objId,
    function (data) {
      callback(data);
    },
    childObjs
  );
}

function getWhere(context, callback) {
  var objId = context.contextId ? context.contextId : "";
  var objectType = context.contextObjectType ? context.contextObjectType : "";

  if (!objId) {
    return false;
  }

  db.api.obj.getWhere(objectType, objId, function (data) {
    callback(data);
  });
}

function filesOnDir(dir, processFile) {
  // read directory
  fs.readdir(dir, (error, fileNames) => {
    if (error) throw error;

    fileNames.forEach((filename) => {
      // get current file name
      const name = path.parse(filename).name;
      // get current file extension
      const ext = path.parse(filename).ext;
      // get current file path
      const filepath = path.resolve(dir, filename);

      // get information about the file
      fs.stat(filepath, function (error, stat) {
        if (error) throw error;

        // check if the current path is a file or a folder
        const isFile = stat.isFile();

        // exclude folders
        if (isFile) {
          // callback, do something with the file
          processFile(filepath, name, ext, stat);
        }
      });
    });
  });
}

const getAllFiles = function (dirPath, arrayOfFiles) {
  files = fs.readdirSync(dirPath);

  arrayOfFiles = arrayOfFiles || [];

  files.forEach(function (file) {
    if (fs.statSync(dirPath + "/" + file).isDirectory()) {
      arrayOfFiles = getAllFiles(dirPath + "/" + file, arrayOfFiles);
    } else {
      //arrayOfFiles.push(path.join(__dirname, dirPath, "/", file))
      var data = {
        fileName: file,
        dirName: dirPath,
      };
      arrayOfFiles.push(data);
    }
  });

  return arrayOfFiles;
};

const GetAllModules = (dirname) => {
  if (dirname) {
    let dirItems = require("fs").readdirSync(dirname);
    return dirItems.reduce((acc, value, index) => {
      if (path.extname(value) == ".js" && value.toLowerCase() != "index.js") {
        let moduleName = value.replace(/.js/g, "");
        acc[moduleName] = require(`${dirname}/${moduleName}`);
      }
      return acc;
    }, {});
  }
};

function getMerged2(template, data, onComplete) {
  var files = getAllFiles(libDir);

  //let dirModules = GetAllModules(libDir);
  //console.log(files);

  files.forEach((file) => {
    var fileName = path.join(file.dirName, file.fileName);
    // get current file name
    var fName = path.parse(file.fileName).name;
    // get current file extension
    var fExt = path.parse(file.fileName).ext;

    var name = require(fileName);

    var i = 0;

    data.forEach((tag) => {
      var tag0 = tag[0];
      var tag1 = tag[1];

      if (fName == tag1) {
        funName(function (tagHtml) {
          //console.log('func:', mergeFunc);
          template = template.replace(tags[0], tagHtml);

          console.log(template);

          callback(template);

          ///return mergeTagHtml(html, tags, onComplete, i + 1);
        });
      }

      console.log(i);

      console.log(name);

      console.log(fName);

      console.log(tag0);

      console.log(tag1);

      if (fName == tag1) {
        console.log("YES IT IS EQUAL");
        //template replacer
        //template = template.replace(tags[i][0], tagHtml);
      }

      ++i;

      return;
    });
  });
}

function replaceMe(template, data, onComplete) {
  const pattern = /{\s*(\w+?)\s*}/g; // {property}
  return onComplete(template.replace(pattern, (_, token) => data[token] || ""));
}

/**
 * If you don't care about primitives and only objects then this function
 * is for you, otherwise look elsewhere.
 * This function will return `false` for any valid json primitive.
 * EG, 'true' -> false
 *     '123' -> false
 *     'null' -> false
 *     '"I'm a string"' -> false
 */
function tryParseJSONObject(jsonString) {
  try {
    var o = JSON.parse(jsonString);

    // Handle non-exception-throwing cases:
    // Neither JSON.parse(false) or JSON.parse(1234) throw errors, hence the type-checking,
    // but... JSON.parse(null) returns null, and typeof null === "object",
    // so we must check for that, too. Thankfully, null is falsey, so this suffices:
    if (o && typeof o === "object") {
      return o;
    }
  } catch (e) {}

  return false;
}

function getData(data, res) {
  var res = "";

  var options = {
    json: true,
    headers: {
      Cookie:
        "gs_v_GSN-752757-B=email:<EMAIL>; pagoda=b1187b464b9066d28a4c411961008f72; uid=920410; user_type=0; instance=dev_test_env;series=c6c484bb9f7608d4e7bf1bf96d98f5b46b90d684bef7db7395d6bc0ae9eca9c16cfce59ab96f927584cb4d765be5480408f628632655cae4dba803325bcda576; token=49f854f34250d9051ac326b0b53c311363dc9ac02c61b02a2024ff160dfd71537fb50d0e3b862a2bd5429a91ee8abbf91188636a864007df1672c9d720797f26;",
      "bento-token":
        "49f854f34250d9051ac326b0b53c311363dc9ac02c61b02a2024ff160dfd71537fb50d0e3b862a2bd5429a91ee8abbf91188636a864007df1672c9d720797f26",
      "Content-Type": "application/x-www-form-urlencoded",
    },
  };

  needle.get(
    "http://host.docker.internal:8080/api/_get.php?MERGE_SERVICE=true",
    null,
    options,
    function (error, response) {
      if (!error && response.statusCode == 200)
        //console.log(response.body);
        return response.body;
    }
  );

  return res;
}

function postData(data, res) {
  var res = "";

  var options = {
    json: true,
    headers: {
      Cookie:
        "gs_v_GSN-752757-B=email:<EMAIL>; pagoda=b1187b464b9066d28a4c411961008f72; uid=920410; user_type=0; instance=dev_test_env;series=c6c484bb9f7608d4e7bf1bf96d98f5b46b90d684bef7db7395d6bc0ae9eca9c16cfce59ab96f927584cb4d765be5480408f628632655cae4dba803325bcda576; token=49f854f34250d9051ac326b0b53c311363dc9ac02c61b02a2024ff160dfd71537fb50d0e3b862a2bd5429a91ee8abbf91188636a864007df1672c9d720797f26;",
      "bento-token":
        "49f854f34250d9051ac326b0b53c311363dc9ac02c61b02a2024ff160dfd71537fb50d0e3b862a2bd5429a91ee8abbf91188636a864007df1672c9d720797f26",
      "Content-Type": "application/x-www-form-urlencoded",
    },
  };

  needle.post(
    "http://host.docker.internal:8080/api/_get.php?do=test&service=MergeService&pagodaAPIKey=rickyvoltz",
    {
      // json variables
      name: "Mario",
      job: "Web Developer",
    },
    options,
    function (error, response) {
      // you can pass params as a string or as an object.
      if (error) {
        console.error(error);
      }
      if (!error && response.statusCode == 200) return response.body;
    }
  );

  return res;
}

module.exports = {
  formatPhoneNo,
  filesOnDir,
  getMerged,
  readById,
  getWhere,
  replaceMe,
  tryParseJSONObject,
  getData,
  postData,
};
