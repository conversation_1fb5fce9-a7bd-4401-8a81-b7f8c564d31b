var db = require('../../_database.js');

function client_address(fieldName, ui, obj, options) {

    db.api.obj.getWhere(
        'contact_info'
        , {
            object_id: 		obj.main_contact
            , childObjs: 	{
                object_id: 	true
                , info: 	true
                , street: 	true
                , city: 	true
                , state: 	true 
                , zip: 		true
                , country: 	true
                , type: {
                    data_type: true
                }
                , is_primary: true
            }
        }
        , function (info) {

            //console.log(info);

            var response = '';
            info.forEach(function (i) {
                
                if(i.type){
                                            
                    if (i.type.data_type === 'address') {
                        response =
                            i.street +'<br />'+
                            i.city +
                            ' '+ i.state +
                            ', '+ i.zip +
                            ' '+ i.country;
                    }

                }

            });

            if (typeof options.callback === 'function') {
                options.callback(response);
            }

        }
    );

}

module.exports ={
    client_address
}