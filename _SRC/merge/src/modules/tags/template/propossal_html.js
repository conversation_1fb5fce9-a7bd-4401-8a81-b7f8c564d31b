var moment = require('moment-timezone');
moment.tz.setDefault('America/Panama');
var db = require('../../_database.js');

function propossal_html(fieldName, ui, obj, options) {

    var response = createMergedInvoiceHTML(obj, true, {});

    if (typeof options.callback === 'function') {
        options.callback(response);
    }

}

function createMergedInvoiceHTML(setup, callback, options){

    function generateString(invoices, pricedMenu, projectObj, payNow){

        if (projectObj === undefined) {
            return;
        }

        var htmlString = '';
        var paymentString = '';
        
        var showPayments = true;

        ///options from nlp merge tag
        if ( options && options.hasOwnProperty('showPayments') )
            showPayments = options.showPayments;
            
        var getHTMLSetup = { includeDiscounts:true };

        if ( options && _.isObject(options) )
            getHTMLSetup = Object.assign({}, getHTMLSetup, options);
            
        htmlString += sectionTypes.menu.getHTML( pricedMenu, {}, {}, getHTMLSetup );
        
        if ( showPayments ) {

            var total = 0;
            var balance = 0;
            var totalPaid = 0;
            var totalFees = 0;
            var paymentLink;
            var payments = [];

            htmlString += '<br /><div style="page-break-inside: avoid; border: 1px solid lightgray; padding: 15px;" >';

            htmlString += '<h3 style="text-align:left; font-weight:bold; padding-bottom: 15px;">Payment Schedule</h3>';

            htmlString += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">';
            htmlString += '<tr style="font-size:12px;font-weight:bold; background-color: #F2F3F4; color:#B9BEC4;"> <td style="color:#B9BEC4;padding:5px;"></td> <td style="padding:5px;color:#B9BEC4; text-align:right;">Due Date</td> <td style="padding:5px;color:#B9BEC4; text-align:right;">Total</td> <td style="padding:5px;color:#B9BEC4; text-align:right;">Paid</td> <td style="padding:5px;color:#B9BEC4; text-align:right;">Balance</td> </tr>';


            _.each(_.sortBy(invoices, 'due_date'), function(inv){

                paymentLink = '';

                if(setup.payNow === true){
                    paymentLink = '<div class="ui mini green compact button payButton" data-id="'+ inv.id +'" data-balance="'+ inv.balance +'">Pay Now</div> ';
                }
    
                if(setup.payNow === 'noButton'){
                    paymentLink = '';
                }

                total += inv.amount;
                balance += inv.balance;
                totalPaid += inv.paid;

                if(inv.balance <= 0){
                    paymentLink = '';
                }

                payments = payments.concat(inv.payments);

                htmlString += '<tr><td style="padding:5px;">'+ paymentLink +''+ inv.name +'</td> <td style="padding:5px; text-align:right;">'+ moment(inv.due_date).local().format('M/D/YYYY') +'</td> <td style="padding:5px; text-align:right;">$'+ (inv.amount/100).formatMoney() +'</td> <td style="padding:5px; text-align:right;">$'+ (inv.paid/100).formatMoney() +'</td> <td style="padding:5px; text-align:right;">$'+ (inv.balance/100).formatMoney() +'</td> </tr>';
                                    
            });

            htmlString += '<tr style="font-weight:900; border-top:1px solid lightgray;"> <td style="padding:5px; text-align:right;"></td> <td style="padding:5px; text-align:right;"></td> <td style="padding:5px; text-align:right;">$'+ (total/100).formatMoney() +'</td> <td style="padding:5px; text-align:right;">$'+ (totalPaid/100).formatMoney() +'</td> <td style="padding:5px; text-align:right;">$'+ (balance/100).formatMoney() +'</td> </tr>';

            htmlString += '</table>';


            htmlString += '</div><br />';

            // PAYMENT HISTORY
            
            paymentString += '<div style="page-break-inside: avoid; border: 1px solid lightgray; padding: 15px;" >';
                
            paymentString += '<h3 style="text-align:left; font-weight:bold; padding-bottom: 15px;">Payment History</h3>';

            paymentString += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">';
            paymentString += '<tr style="font-size:12px;font-weight:bold; background-color: #F2F3F4; color:#B9BEC4;"> <td style="padding:5px; text-align:right;color:#B9BEC4;">Transaction ID</td> <td style="padding:5px; text-align:right;color:#B9BEC4;">Payment Date</td> <td style="padding:5px; text-align:right;color:#B9BEC4;">Paid on Invoice</td> <td style="padding:5px; text-align:right;color:#B9BEC4;">Fees</td> <td style="padding:5px; text-align:right;color:#B9BEC4;">Total Paid</td> </tr>';
            
            totalPaid = 0;

            payments = _.uniq(payments, false, function(payment){return payment.id});
            
            _.each(_.sortBy(payments, 'date_created'), function(inv){
                
                var transxId = inv.id;
                if(inv.details.id){
                    transxId = inv.details.id;
                }
                
                var dateString = moment(inv.date_created).local().format('M/D/YYYY');
                if (inv.details && inv.details.payment_date) {
                    dateString = moment(inv.details.payment_date).local().format('M/D/YYYY');
                }
                
                if(!inv.fee){
                    inv.fee = 0;
                }
                
                totalPaid += inv.amount;
                totalFees += inv.fee;
                    
                if(setup.payNow === true){
                    paymentLink = '<div class="ui mini green button payButton" data-id="'+ inv.id +'" data-balance="'+ inv.balance +'">Pay Now</div> ';
                }
                
                paymentString += '<tr style="text-align:right;"> <td style="padding:5px;">'+ projectObj.id +'-'+ transxId +'</td> <td style="padding:5px; text-align:right;">'+ dateString +'</td> <td style="padding:5px; text-align:right;">$'+ (inv.amount/100).formatMoney() +'</td> <td style="padding:5px; text-align:right;">$'+ (inv.fee/100).formatMoney() +'</td> <td style="padding:5px; text-align:right;">$'+ ((inv.amount+inv.fee)/100).formatMoney() +'</td> </tr>';

            });

            paymentString += '<tr style="font-weight:900; border-top:1px solid lightgray;"> <td style="padding:5px; text-align:right;"></td> <td style="padding:5px; text-align:right;"></td> <td style="padding:5px; text-align:right;">$'+ (totalPaid/100).formatMoney() +'</td> <td style="padding:5px; text-align:right;">$'+ (totalFees/100).formatMoney() +'</td> <td style="padding:5px; text-align:right;">$'+ ((totalPaid+totalFees)/100).formatMoney() +'</td> </tr>';

            paymentString += '</table></div>';

            if(totalPaid > 0){
                htmlString += paymentString;
            }

            return htmlString;
            
        } else {
            
            return htmlString;
            
        }
        
    }

    if (callback) {

        db.api.controller('getMergeTagData', {
            objId: setup.id
        }, function(data) {
            
            var menus = data.inventory_menu;
            var invoices = data.invoices;

            sb.notify({
                type:'get-menu-line-item-pricing',
                data:{
                    menu: menus[0],
                    obj: setup.project,
                    workorder: setup.project,
                    callback:function(pricedMenu){

                        htmlString = generateString(invoices, pricedMenu, setup.project, setup.payNow);

                        callback(htmlString);

                    }
                }
            });

        });

    } else {

        return generateString(setup.invoices, setup.pricedMenu, setup.projectObj, setup.payNow);

    }

}

function getMenuProposalHTML(menuObj, shifts, sections, options){

    // This function is used to generate HTML for the PROPOSAL.INVOICE merge tag
    
    if (menuObj === null || _.isEmpty(menuObj)) {
        return '<i>No line items set</i>';
    }

    var total = 0,
        sectionTotal = 0,
        htmlString = '';
        
    var Options = {
        includeDiscounts: options.includeDiscounts
    };
    var includeLineItemPrice =  	true;
    var includeLineItemQty   = 	true;
    var includeRate          = 	true;
    var includeDescription 	= 	true;
    var includeNote 		= 	true;
    var showPricing          =  	true;
    var showSections         = 	{ 
        sectionBorder: true
        , sectionNotes: 		{
            color:'red'
        }
        , showDesc: true 
    };
    var showBreakdown 		= 	{ sectionBorder: true, inTable: true };
    var showPaymentSchedule  = 	true;
    var groupByCat           = 	false;
    var secMap = {};
    var rowBackgroundColor = '#F2F3F4';
    var showDiscountsPerLineItem = false;

    if (options.includeLineItemPrice === false) {
        includeLineItemPrice = false;
    }
    if (options.includeLineItemQty === false) {
        includeLineItemQty = false;
    }
    if (options.includeRate === false) {
        includeRate = false;
    }
    if (options.includeDescription === false) {
        includeDescription = false;
    }
    if (options.includeNote === false) {
        includeNote = false;
    }
    if (options.showPricing === false) {
        showPricing = false;
    }
    if (options.showSections === false) {
        showSections = false;
    }
    if (options.showBreakdown === false) {
        showBreakdown = false;
    }		
    if (options.groupByCat === true) {
        groupByCat = true;
    }
    if (options.showDiscountsPerLineItem === true) {
        showDiscountsPerLineItem = true;
    }

    if ( options.showSections && options.showSections.hasOwnProperty('sectionBorder') ) 
        showSections.sectionBorder = options.showSections.sectionBorder;
    
    if ( options.showSections && options.showSections.hasOwnProperty('showDesc') )
        showSections.showDesc = options.showSections.showDesc;

    if ( options.showSections && ((options.showSections || {}).sectionNotes || {}).color ){
        showSections.sectionNotes.color = options.showSections.sectionNotes.color;	
    }			
    
    if ( options.showBreakdown && options.showBreakdown.hasOwnProperty('sectionBorder') ) {
        showBreakdown.sectionBorder = options.showBreakdown.sectionBorder;
    }
    
    if ( options.showBreakdown && options.showBreakdown.hasOwnProperty('inTable') ) {
        showBreakdown.inTable = options.showBreakdown.inTable;
    }
        
    if ( options.showPaymentSchedule && options.showPaymentSchedule.hasOwnProperty('sectionBorder') ) {
        showPaymentSchedule.sectionBorder = options.showPaymentSchedule.sectionBorder;
    }
    
    function applyCategoryDiscounts (cat, menuObj) {
        
        // Apply per category discounts
        var catDisplay = '$'+ (sectionTotal/100).formatMoney();
        _.chain(menuObj.discounts)
            .where( { apply_to: 'category' } )
            .each(function(discount){
                
                if( _.contains( discount.categories, parseInt(cat.id) ) ){
                    
                    // apply discount to total
                    total -= sectionTotal;
                    var tmp = applyDiscountFactor(sectionTotal, discount, true);
                    sectionTotal = tmp.value;
                    total += sectionTotal;
                    catDisplay = tmp.displayString;
                    
                }
                
            });
            
        return catDisplay;
        
    }

    function getLineItemDescriptionHtml (item) {

        var html = '';

        if (item.item 
            && !_.isEmpty(item.item.description)
        ) {
            
            // Validate description html before using it
            var elCheck = document.createElement('div');
            elCheck.innerHTML = item.item.description;
            
            html += '</br><span class="text-muted">'+ elCheck.innerHTML +'</span>';
            
        }
        
        sb.notify({
            type: 'display-choice-items',
            data: {
                item: item,
                cb: function(choiceItemHTML) {

                    html += choiceItemHTML;

                }
            }
        }); 

        return html;

    }

    function getLineItemQtyHtml (item) {

        var htmlString = '';

        if (Options.includeDiscounts) {
            
            var pricingInfo = priceMenuItem(item, menuObj, false, true, true, true);
            var qtyTxt = item.absolute_qty;
            var qtyTxt = quantityView.call(null, item);

            function quantityView(item){

                // ui funcs
                function pricingCalculationView(item, newQty){

                    var text = '';
                    var textColor = '';
                    
                    // item name item units =
                    var tmp = Object.assign({}, item);
                    tmp.qty = newQty.qty;
                    
                    switch (newQty.qty_type) {
                            
                           case 'absolute':
                               tmp.price_type = 'price';
                               break;
                           
                           case 'per_guest':
                               tmp.price_type = 'price_per_person';
                               break;
                               
                           case 'guest_count':
                               tmp.price_type = 'price_per_person';
                               break;
                               
                           case 'per_hour':
                               tmp.price_type = 'price_per_hour';
                               break;
                               
                           case 'per_hour_per_guest':
                               tmp.price_type = 'price_per_hour_per_person';
                               break;
                           
                       }

                    // price
                    var newLinePrice = priceMenuItem(tmp, menu, false, false, true, undefined, true);
                    var oldLinePrice = priceMenuItem(item, menu, false, false, true, undefined, true);
                    
                    var qtyTypeText = '';
                    var changeArrow = '';
                    var changeArrowColor = ' text-muted';
                    var fromText = '&nbsp; <strong>(from $'+ (oldLinePrice/100).formatMoney() +')</strong>';

                    if(newLinePrice > oldLinePrice){
                        changeArrow = '<i class="fa fa-arrow-up animated bounce" aria-hidden="true"></i>';
                        changeArrowColor = 'orange';
                        
                        if(newLinePrice > oldLinePrice*2){
                            changeArrowColor = 'red';
                        }
                        
                    }else if(newLinePrice < oldLinePrice){
                        changeArrow = '<i class="fa fa-arrow-down" aria-hidden="true"></i>';
                        changeArrowColor = 'blue';
                        
                        if(newLinePrice*2 < oldLinePrice){
                            changeArrowColor = 'primary';
                        }
                        
                    }else {
                        
                        fromText = '';
                        
                    }
                    
                    switch(newQty.qty_type){
                        
                        case 'per_guest':
                            qtyTypeText = 'per '+ menu.guest_count +' guests';
                            break;
                            
                        case 'guest_count':
                            qtyTypeText = '<i class="fa fa-times" aria-hidden="true"></i>'+ menu.guest_count +' guests';
                            break;
                            
                        default:
                            break;
                        
                    }
                    
                    // equation
                    this.makeNode('calculation', 'headerText', {
                        text:'$'+ (item.item.price/100).formatMoney() +' '+ item.item.name +' <i class="fa fa-times" aria-hidden="true"></i> '+ newQty.qty +' '+ qtyTypeText +' &nbsp;',
                        size:'x-small',
                        css:'pda-gray',
                        style:'display:inline-block;'
                    });
                    
                    // result
                    this.makeNode('result', 'headerText', {
                        text:'<i class="fa fa-arrow-right" aria-hidden="true"></i>&nbsp; $'+ (newLinePrice/100).formatMoney() +' ',
                        size:'small',
                        css:'pda-'+ changeArrowColor +' animated bounceIn',
                        style:'display:inline-block;'
                    });
                    
                    // increase/decrease
                    this.makeNode('relative', 'headerText', {
                        text:fromText,
                        size:'xx-small',
                        style:'display:inline-block;'
                    });
                    
                }
                
                function updateQtyForm(ui, item, cb){

                    var newQty = {
                        qty:item.qty,
                        qty_type:item.qty_type
                    };

                    // pricing calculation display
                    ui.makeNode('pricing', 'column', {width:12, css:'pda-container'});
                    ui.refreshPricing = pricingCalculationView.bind(ui.pricing, item, newQty);
                    ui.refreshPricing();
                    
                    // qty form
                    ui.makeNode('left', 'column', {width:3, css:'pda-container'})
                        .makeNode('form', 'form', [{
                            name:'quantity',
                            type:'number',
                            value:item.qty,
                            label:''
                        }])
                        .notify('change', {
                            type:'inventory-comp-run',
                            data:{
                                run:function(item, pop, data){

                                    newQty.qty = parseFloat(pop.left.form.process().fields.quantity.value);
                                    
                                    pop.refreshPricing();
                                    pop.pricing.patch();
    
                                }.bind({}, item, ui)
                            }
                        }, sb.moduleId);
                        
                    ui.left.form.quantity.notify('keypress', {
                            type:'inventory-comp-run',
                            data:{
                                run:function(item, pop, data){
                                    
                                    newQty.qty = parseFloat(pop.left.form.process().fields.quantity.value);								
                                    WebuiPopovers.hideAll();
                                    
                                    // if enter key was pressed
                                    if(data.evt.which === 13){
                                        updateMenuLineItem(item, newQty, section, function(response){
    
                                            cb();
                                            
                                        });
                                    }
                                    
    
                                }.bind({}, item, ui)
                            }
                        }, sb.moduleId);
                        
                    var formOptions = [{
                            value:'absolute',
                            name:'in total'
                        }];
                    var guestCountTxt = '';
                    if (parseInt(menu.guest_count) > 0) {
                        guestCountTxt = ' ('+ menu.guest_count +' guests)';
                    }
                    
                    var pricePerUnit = 0;
                    var pricePerGuest = 0;
                    var pricePerHour = 0;
                    var pricePerHourPerGuest = 0;
                    
                    if(menu.type == 'event-menu'){
                        formOptions = [{
                            value:'absolute',
                            name:'in total'
                        }, {
                            value:'per_guest',
                            name:'per guest'+ guestCountTxt
                        }, {
                            value:'guest_count',
                            name:'use guest count'+ guestCountTxt
                        }, {
                            value:'per_hour',
                            name:'per hour'
                        }, {
                            value:'per_hour_per_guest',
                            name:'per hour per guest'+ guestCountTxt
                        }];
                    }
                        
                    // qty type form
                    ui.makeNode('right', 'column', {width:9, css:'pda-container'})
                        .makeNode('form', 'form', [{
                            name:'qtyType',
                            type:'select',
                            value:item.qty_type,
                            label:'',
                            options:formOptions
                        }])
                        .notify('change', {
                            type:'inventory-comp-run',
                            data:{
                                run:function(item, pop, data){
                                    
                                    setTimeout(function () {
                                        
                                        newQty.qty_type = pop.right.form.process().fields.qtyType.value;
                                        pop.refreshPricing();
                                        pop.pricing.patch();
                                        
                                    }, 1);
                                    
                                }.bind({}, item, ui)
                            }
                        }, sb.moduleId);
                        
                    ui.right.form.qtyType.notify('keypress', {
                            type:'inventory-comp-run',
                            data:{
                                run:function(item, pop, data){
                                    
                                    setTimeout(function () {
                                        
                                        newQty.qty_type = pop.right.form.process().fields.qtyType.value;
                                        WebuiPopovers.hideAll();
                                        
                                        // if enter key was pressed
                                        if(data.evt.which === 13){
                                            updateQty(item, newQty, function(response){
                                                
                                                cb();
                                                
                                            });
                                        }
                                        
                                    }, 1);
                                    
                                }.bind({}, item, ui)
                            }
                        }, sb.moduleId);
                        
                    ui.makeNode('br', 'div', {css:'ui hidden clearing divider'});
                        
                    ui.makeNode('update', 'div', {
                        css:'ui right floated teal button',
                        tag:'button',
                        text:'Update'
                    }).notify('click', {
                        type:'inventory-comp-run',
                        data:{
                            run:function(){
                                
                                newQty.qty_type = ui.right.form.process().fields.qtyType.value;
                                newQty.qty = parseFloat(ui.left.form.process().fields.quantity.value);
                                newQty.pricingType = 'price';
                                
                                switch (newQty.qty_type) {
                                        
                                       case 'absolute':
                                           newQty.pricingType = 'price';
                                           break;
                                       
                                       case 'per_guest':
                                           newQty.pricingType = 'price_per_person';
                                           break;
                                           
                                       case 'guest_count':
                                           newQty.pricingType = 'price_per_person';
                                           break;
                                           
                                       case 'per_hour':
                                           newQty.pricingType = 'price_per_hour';
                                           break;
                                           
                                       case 'per_hour_per_guest':
                                           newQty.pricingType = 'price_per_hour_per_person';
                                           break;
                                       
                                   }
                                
                                updateMenuLineItem(item, newQty, section, function(response){

                                    cb();
                                    
                                });
                                
                            }
                        }
                    }, sb.moduleId);
                    
                    ui.patch();
                    
                }
                
                var qtyText = item.qty;

                if(!_.isEmpty(item.yield_type) && item.yield_type !== 'servings' && item.measurement > 0){
                    
                    var measurementObj = _.findWhere(units_of_measure['uscu'][item.yield_type].measurements, {id:item.measurement});
                    qtyText = item.qty/to_base_units(item.yield_type, item.measurement, 1) + measurementObj.abbr;

                }
                
                if(item.yield_type === 'servings'){
                    switch(item.qty_type){
                        case 'per_guest':
                            qtyText = item.qty +' servings per guest';
                            break;
                        case 'guest_count':
                            qtyText = menu.guest_count +' guests';
                            break;
                        default:
                            qtyText = item.qty + ' servings';
                            break;
                    }
                }else{
                    switch(item.qty_type){
                        case 'per_guest':
                            qtyText = item.qty +'/guest';

                            if (parseInt(menuObj.guest_count) > 0) {
                                qtyText = Math.round(item.qty * menuObj.guest_count);
                                //qtyText = item.qty +' x'+ menuObj.guest_count +' guest(s)';
                            }
                            
                            break;
                            
                        case 'guest_count':
                            qtyText = menu.guest_count +' guests';
                            break;
                            
                        case 'per_hour':
                            qtyText = item.qty +'/hour';
                            break;
                            
                        case 'per_hour_per_guest':
                            qtyText = item.qty +'/hour/guest';
                            if (parseInt(menuObj.guest_count) > 0) {
                                qtyText = Math.round(item.qty * menuObj.guest_count);
                                //qtyText = item.qty +' x'+ menuObj.guest_count +' guest(s)';
                            }
                            break;
                    }
                }

                return qtyText;
                
            }
            
            if (sections.lineItem) {
                        
                var lineTotalDisplayString = '$'+ ((priceMenuItem(item, menuObj, false, true, true))/100).formatMoney();
                if( pricingInfo.isDiscounted ){
                    lineTotalDisplayString = pricingInfo.displayString;
                }
                
                if (sections.lineItem == 'Yes') {

                    // only show name and line total for estimates
                    if (item.item.is_estimate) {
                        
                        htmlString += '<td style="padding:5px; text-align:right;"></td> ';
                        
                    } else {
                        
                        htmlString += '<td style="padding:5px; text-align:right;">'+ qtyTxt +'</td>';
                                    
                    }
                    
                } else {
                    
                    // only show name and line total for estimates
                    if (item.item.is_estimate) {
                        
                        htmlString += '<td style="padding:5px; text-align:right;"></td> ';
                                    
                    } else {
                        
                        htmlString += '<td style="padding:5px; text-align:right;">'+ qtyTxt +'</td> ';
                                    
                    }
                    
                }
                
            } else {

                // only show name and line total for estimates
                if(item.item.is_estimate){
                    
                    htmlString += '<td style="padding:5px; text-align:right;"></td> ';
                                
                } else {
                    
                    htmlString += '<td style="padding:10px 0px; text-align:right;">'+ qtyTxt +'</td> ';
                                
                }
                
            }
            
        } else {
            
            if(sections.lineItem){
                
                if(sections.lineItem == 'Yes'){

                    // only show name and line total for estimates
                    if (item.item.is_estimate) {
                        
                        htmlString += '<td style="padding:5px; text-align:right;"></td> ';
                    
                    } else {
                        
                        htmlString += '<td style="padding:5px; text-align:right;">'+ qtyTxt +'</td> ';
                    
                    }
                    
                }else{
                    
                    // only show name and line total for estimates
                    if (item.item.is_estimate) {
                        
                        htmlString += '<td style="padding:5px; text-align:right;"></td> ';
                                    
                    } else {
                        
                        htmlString += '<td style="padding:5px; text-align:right;">'+ qtyTxt +'</td> ';
                    
                    }
                    
                }
                
            }else{
                
                // only show name and line total for estimates
                if (item.item.is_estimate) {
                    
                    htmlString += '<td style="padding:5px; text-align:right;"></td> ';
                
                } else {
                    
                    htmlString += '<td style="padding:5px; text-align:right;">'+ qtyTxt +'</td> ';
                
                }
                
            }
            
        }
        
        return htmlString;
        
    }
    
    function getLineItemRateHTML(item, menu){

        var priceText = '';
        if(item.vendor != null){
            priceText = '$'+ (((parseInt(item.item.price)))/100).formatMoney();
        }else{
            priceText = '$'+ (((priceMenuItem(item, menu, true, true, undefined, undefined, false)))/100).formatMoney();
            
            var currentSection = _.findWhere(menu.sections, {id:item.section});
            var hours = 1;
            if (!_.isEmpty(currentSection)) {
                
                if(currentSection.to && currentSection.from){
                    
                    hours = moment(currentSection.to, 'YYYY-MM-DD HH:mm:ss').diff(moment(currentSection.from, 'YYYY-MM-DD HH:mm:ss'))/(1000*60*60);
                                            
                }
                
            }				

        }
                
        switch(item.price_type){
            
            case 'price_per_person':
            priceText += '/guest';
            break;
            
            case 'price_per_hour':
            priceText += '/hour';
            break;
            
            case 'price_per_hour_per_person':
            priceText += '/hour/person x '+ hours +' hours';
            break;
            
            default:
            break;
            
        }

        return priceText;
        
    }		
    
    function getLineItemPriceHtml (item, forceCatDiscounts) {

        var htmlString = '';

        if (Options.includeDiscounts) {

            var pricingInfo = priceMenuItem(item, menuObj, false, true, true, true, false, forceCatDiscounts);	

            if (sections.lineItem) {

                var lineTotalDisplayString = '$'+ ((priceMenuItem(item, menuObj, false, true, true, true, false))/100).formatMoney();
                if( pricingInfo.isDiscounted ){
                    lineTotalDisplayString = pricingInfo.displayString;
                }
                
                if (sections.lineItem == 'Yes') {

                    // only show name and line total for estimates
                    if (item.item.is_estimate) {
                        
                        htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (item.lineTotal/100).formatMoney() +'</td> ';
                        
                    } else {
                        
                        htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (item.lineTotal/100).formatMoney() +'</td> ';
                                    
                    }
                    total += +pricingInfo.afterDiscount;
                    sectionTotal += +pricingInfo.afterDiscount;
                    
                } else {

                    // only show name and line total for estimates
                    if (item.item.is_estimate) {
                        
                        htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (lineTotal/100).formatMoney() +'</td> ';
                                    
                    } else {
                        
                        htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (lineTotal/100).formatMoney() +'</td> ';
                                    
                    }
                    
                    total += +pricingInfo.afterDiscount;
                    sectionTotal += +pricingInfo.afterDiscount;
                    
                }
                
            } else {

                var displayString = '$'+ (item.lineTotal/100).formatMoney();
                if (
                    showDiscountsPerLineItem 
                    && !_.isEmpty(pricingInfo)
                    && parseInt(pricingInfo.beforeDiscount) > parseInt(pricingInfo.afterDiscount)
                ) {
                    displayString = pricingInfo.displayString;
                } 

                // only show name and line total for estimates
                if (item.item.is_estimate) {
                    
                    htmlString += '<td style="padding:10px 0px; text-align:right;">'+ displayString +'</td> ';
                                
                } else {
                    
                    htmlString += '<td style="padding:10px 0px; text-align:right;">'+ displayString +'</td> ';
                                
                }
                
                if(pricingInfo){
                    
                    total += +pricingInfo.afterDiscount;
                    sectionTotal += +pricingInfo.afterDiscount;
                    
                }
                
            }
            
        } else {

            if(sections.lineItem){

                if(sections.lineItem == 'Yes'){

                    // only show name and line total for estimates
                    if (item.item.is_estimate) {
                        
                        htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (item.lineTotal/100).formatMoney() +'</td> ';
                    
                    } else {
                        
                        htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (item.lineTotal/100).formatMoney() +'</td> ';
                    
                    }
                    
                    total += +item.lineTotal;
                    sectionTotal += +item.lineTotal;
                    
                }else{
                    
                    // only show name and line total for estimates
                    if (item.item.is_estimate) {
                        
                        htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (lineTotal/100).formatMoney() +'</td> ';
                                    
                    } else {
                        
                        htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (lineTotal/100).formatMoney() +'</td> ';
                    
                    }
                    
                    total += +item.lineTotal;
                    sectionTotal += +item.lineTotal;
                    
                }
                
            }else{

                // only show name and line total for estimates
                if (item.item.is_estimate) {
                    
                    htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (item.lineTotal/100).formatMoney() +'</td> ';
                
                } else {
                    
                    htmlString += '<td style="padding:10px 0px; text-align:right;">$'+ (item.lineTotal/100).formatMoney() +'</td> ';
                
                }
                
                total += +item.lineTotal;
                sectionTotal += +item.lineTotal;
                
            }
            
        }
        
        return htmlString;
        
    }
    
    function isOdd(num) {
        return num % 2;
    }

    if(menuObj && showSections){
        
        if (groupByCat) {

            var catsShown = [];
            _.each(cache.menuItemCategories, function (cat) {

                if (_.contains(catsShown, cat.id)) {
                    return;
                }
                catsShown.push(cat.id);
                
                var items = [];
                _.each(menuObj.sections, function (section) {
                    
                    _.each(section.items, function (item) {
                        
                        if (parseInt(item.item.category) === cat.id) {
                            items.push(item);
                        }
                        
                    });
                    
                });

                if (!_.isEmpty(items)) {
                    
                    htmlString += '<div style="">';
                    
                    htmlString += '<p style="font-size:18px">'+ cat.name +'</p>';
                    
                    htmlString += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">';
                    htmlString += 
                            '<tr style="font-size:12px;font-weight:bold;">'+
                                '<td style="padding:5px; width:30%;">ITEM</td>';
                    
                    if (includeNote) {
                        htmlString += '<td style="padding:5px; text-align:right; width:15%;">NOTE</td>';
                    }
                    
                    if (includeRate) {
                        htmlString += '<td style="padding:5px; text-align:right;">RATE</td>';
                    } 
                                
                    if (includeLineItemQty) {
                        htmlString += '<td style="padding:5px; text-align:right;">QUANTITY</td>';
                    }
                    
                    if (includeLineItemPrice) {
                        htmlString += '<td style="padding:5px; text-align:right;">TOTAL</td>';
                    }
                    
                    htmlString += '</tr>';
                                            
                    sectionTotal = 0;
                    
                    _.each(items, function(item){

                        // only show items NOT nested under estimates
                        if (item) {
                            
                            var lineTotal = 0;
                            var rateTxt = '';
                            var nameCol = '<td style="padding:5px;">'+ item.item.name;
                            
                            if (item.lineTotal) {
                                lineTotal = item.lineTotal;
                            }

                            if ( showSections.showDesc ) {
                                    
                                nameCol += getLineItemDescriptionHtml(item);								
                                                                    
                            }

                            nameCol += '</td>';
                            
                            if (!(+item.item.estimate)) {

                                htmlString += 
                                    
                                    '<tr style="">'+
                                        nameCol;
                                
                                var itemNote = '';

                                if (includeNote) {

                                    //if(item.note) {
                                        itemNote = set_line_item_note(item);
                                    //}
                                    
                                    htmlString += '<td style="padding:5px; text-align:right;">'+ itemNote +'</td>';
                                }

                                if(includeRate) {
                                    
                                    rateTxt = getLineItemRateHTML(item, menuObj);

                                    htmlString += '<td style="padding:5px; text-align:right;">'+ rateTxt +'</td>';

                                }		
                                    
                                if (includeLineItemQty) {
                                    
                                    htmlString += getLineItemQtyHtml(item);
                                    
                                } else {
                                    
                                    getLineItemQtyHtml(item);
                                    
                                }

                                if (includeLineItemPrice) {
                                    
                                    htmlString += getLineItemPriceHtml(item);
                                    
                                } else {
                                    
                                    getLineItemPriceHtml(item);
                                    
                                }
                                
                                htmlString += 
                                
                                    '</tr>';
                                
                            }
                        }						
                        
                    });
                    
                    htmlString += '</table><br />';
                    
                    if (showPricing) {
                        
                        var catDisplay = '$'+ (sectionTotal/100).formatMoney();

                        // Use
                        
                        if (Options.includeDiscounts) {
                            catDisplay = applyCategoryDiscounts(
                                cat
                                , menuObj
                            );
                        }
                        
                        htmlString += '<div style="text-align:right;">'+ cat.name +' Total: <strong>$'+ catDisplay +'</strong></div>';
                    
                    }
                    
                    htmlString += '</div>';
                        
                }
                
            });
            
        } else {

            var orderedSections = _.sortBy(
                menuObj.sections
                , 'sortId'
            );

            var orderedSections = _.reject(orderedSections, function(section){
                return section.hidden_on_invoice == 'yes';
            });
            
            var borderStyle = ( showSections.sectionBorder ) ? 'border: 1px solid lightgray; padding: 15px;' : '';
            var totalStyle = ( showSections.sectionBorder ) ? 'border-top: 1px solid #DFE1E3;' : '';
            
            _.each(orderedSections, function(section, i) 
            {
                ///Five Column Section
                var tableColumnLayout = '<colgroup>'+
                                       '<col span="1" style="width: 30%;">' +
                                       '<col span="1" style="width: 15%;">' +
                                       '<col span="1" style="width: 15%;">' +
                                       '<col span="1" style="width: 20%;">' +
                                       '<col span="1" style="width: 20%;">' + 										       
                                    '</colgroup>';
                
                ///VENDOR sections do not require RATE column display. Check Name of Section
                if ( section.name.toLowerCase().indexOf('vendor') !== -1 ) {
                    
                    // Don't show the rate for vendor sections
                    includeRate = false;
                    
                    tableColumnLayout = '<colgroup>'+
                                       '<col span="1" style="width: 50%;">' +
                                       '<col span="1" style="width: 30%;">' +
                                       '<col span="1" style="width: 20%;">' + 										       
                                    '</colgroup>';

                }
                
                if (!_.isEmpty(section.items)) {
                    
                    var sectionTime = '';
                    var start = moment(section.from, 'YYYY-MM-DD HH:mm:ss');
                    var end = moment(section.to, 'YYYY-MM-DD HH:mm:ss');
                    if (
                        start.isValid()
                        && end.isValid()
                    ) {
                        sectionTime = ', '+ start.local().format('MM/DD/YY h:mm a') +' - '+ end.local().format('h:mm a') +'';
                    }
                    
                    htmlString += '<div style="'+ borderStyle +'">';
                    
                    if ( showSections.showDesc ) {
                        
                        htmlString += '<h3 style="font-weight:bold; padding-bottom: 15px;">'+ section.name +''+ sectionTime +'</h3>';
                        
                    }
                    
                    if (
                        showSections
                        || i === 0
                    ) {
                        
                        htmlString += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;"><tbody>';
                        htmlString += tableColumnLayout;

                        htmlString += 
                                '<tr style="font-size:12px;font-weight:bold; background-color: #F2F3F4;">'+
                                    '<td style="padding:5px; color:#B9BEC4; width:30%;">Item</td>';
                        
                        if (includeNote) {
                            htmlString += '<td style="padding:5px; color:#B9BEC4; text-align:left; width:15%;">Note</td>';
                        }
                        
                        if (includeLineItemQty && section.name.toLowerCase().indexOf('vendor') === -1) {
                            htmlString += '<td style="padding:5px; color:#B9BEC4; text-align:right;">Qty</td>';
                        }							
                        
                        if (includeRate) {
                            htmlString += '<td style="padding:5px; color:#B9BEC4; text-align:right;">Rate</td>';
                        } 
                        
                        if (includeLineItemPrice) {
                            htmlString += '<td style="padding:5px; color:#B9BEC4; text-align:right;">Total</td>';
                        }
                        
                        htmlString += '</tr>';
                        
                    }
                    
                    sectionTotal = 0;
                    
                    _.each(section.items, function(item, i){

                        var tableColor = "";
                        
                        if(isOdd(i) !== 0) {
                            
                            tableColor = "background-color: #F2F3F4;";
                            
                        }
    
                        // only show items NOT nested under estimates
                        if (item) {

                            var lineTotal = 0;
                            if (item.lineTotal) {
                                lineTotal = item.lineTotal;
                            }
                            
                            if (!(+item.item.estimate)) {

                                var nameCol = '<td style="padding:10px 0px;">'+ item.item.name;
                                
                                if ( showSections.showDesc ) {
                                    
                                    nameCol += getLineItemDescriptionHtml(item);										
                                    
                                }

                                nameCol += '</td>';
                                
                                htmlString += 
                                    
                                    '<tr style="'+ tableColor +'">'+
                                        nameCol;
                                
                                var itemNote = '';

                                if (includeNote) {
                                    
                                    //if(item.note) {
                                        itemNote = set_line_item_note(item);
                                    //}
                                    
                                    htmlString += '<td style="padding:10px 0px; text-align:left; color:'+ showSections.sectionNotes.color +';">'+ itemNote +'</td>';
                                }		

                                // Hide from vendor sections
                                if (section.name.toLowerCase().indexOf('vendor') === -1) {
                                    
                                    if (includeLineItemQty && !item.vendor) {
                                        
                                        htmlString += getLineItemQtyHtml(item);
                                        
                                    } else {
                                        
                                        if(!item.vendor){
                                            getLineItemQtyHtml(item);
                                        }else{
                                            htmlString += '<td style="padding:10px 0px;"></td>';
                                        }
                                        
                                    }

                                    if(includeRate && !item.vendor) {
                                    
                                        rateTxt = getLineItemRateHTML(item, menuObj);
                                        htmlString += '<td style="padding:10px 0px; text-align:right;">'+ rateTxt +'</td>';
                                    
                                    }else{
                                        
                                        htmlString += '<td style="padding:10px 0px;"></td>';
                                        
                                    }	

                                }
                                
                                if (includeLineItemPrice) {
                                    
                                    htmlString += getLineItemPriceHtml(item, true);
                                    
                                } else {
                                    
                                    getLineItemPriceHtml(item, true);
                                    
                                }
                                
                                htmlString += 
                                
                                    '</tr>';
                                
                            }
                        }						
                        
                    });
                    
                    if (
                        showSections
                        || i === menuObj.sections.length - 1
                    ) {
                        
                        htmlString += '</tbody></table>';
                        
                    }
                    
                    if (showPricing) {

                        // Use the pricing breakdown record if it exists
                        if (
                            menuObj
                            && menuObj._pricingBreakdown
                            && menuObj._pricingBreakdown.breakdown
                            && menuObj._pricingBreakdown.breakdown.by_section
                            && typeof menuObj._pricingBreakdown.breakdown.by_section[section.id] === 'number'
                        ) {

                            var sectionDisplay = '$'+ (menuObj._pricingBreakdown.breakdown.by_section[section.id]/100).formatMoney();

                            if (Options.includeDiscounts) {
                                
                                // Use the pricing breakdown record if it exists
                                if (
                                    menuObj
                                    && menuObj._pricingBreakdown
                                    && menuObj._pricingBreakdown.breakdown
                                    && menuObj._pricingBreakdown.breakdown.by_section_without_discounts
                                    && typeof menuObj._pricingBreakdown.breakdown.by_section_without_discounts[section.id] === 'number'
                                    && menuObj._pricingBreakdown.breakdown.by_section_without_discounts[section.id] > menuObj._pricingBreakdown.breakdown.by_section[section.id]
                                ) {
                                    
                                    sectionDisplay = '<del>$'+ (menuObj._pricingBreakdown.breakdown.by_section_without_discounts[section.id]/100).formatMoney() +'</del> '+ sectionDisplay;

                                }

                            }

                            htmlString += '<div style="'+ totalStyle +' padding-top: 15px; font-size:15px; font-weight: bold; text-align:right;">'+ section.name +'      Total: '+ sectionDisplay +'</div>';

                        } else {

                            htmlString += '<div style="'+ totalStyle +' padding-top: 15px; font-size:15px; font-weight: bold; text-align:right;">'+ section.name +'      Total: $'+ (sectionTotal/100).formatMoney() +'</div>';
                    
                        }
                    
                    }
                    
                    htmlString += '</div><br />';
                    
                }
                
                ///resets rate col display for next section
                includeRate = true;
                
            });
            
        }
        
    }
    
    if(
        !_.isEmpty(menuObj._labor)
        && showPricing
    ){
        
        var laborTot = 0;
        var laborText = '';
        laborText += '<div style="">';
        laborText += '<h3 style="font-weight:bold; padding-bottom: 15px;">Labor</h3>';
                            
        laborText += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">';
        laborText += '<tr style="font-size:12px; font-weight:bold;"> <td style="padding:5px;">Category</td> <td style="padding:5px; text-align:right;">Total</td> </tr>';

        _.each(menuObj._labor, function (amt, catId) {
            
            var cat = _.findWhere(
                cache.menuItemCategories
                , {
                    id: parseInt(catId)
                }
            );
            if (cat) {
                catName = cat.name;
            } else {
                return ;
            }
            
            laborText += '<tr style="">'+
                            '<td style="padding:5px;">'+ catName +'</td>'+
                            '<td style="padding:5px; text-align:right;">$'+ (amt/100).formatMoney() +'</td>'+
                        '</tr>';
                        
            laborTot += amt;
            
        });

        if(laborTot > 0){
            laborText += '<tr style=""> <td style="padding:5px; font-weight:bold; text-align:right;">Total</td> <td style="padding:5px; text-align:right;">$'+ (laborTot/100).formatMoney() +'</td> </tr>';
            laborText += '</table><br />';
            laborText += '</div>';
            htmlString += laborText;
        }
                    
        /*
_.each(obj.pricing, function(price, serviceId){
            
            var service = _.findWhere(services, {id:+serviceId});
            
            if(service){

                shiftsTotal += +price;
                total += +price;
                
                htmlString += '<tr style=""> <td style="padding:5px;">'+ service.name +'</td> <td style="padding:5px; text-align:right;">$'+ (price/100).formatMoney() +'</td> </tr>';
                
            }
                                                                                
        });
*/
        
        
        total += laborTot;
            
    }
    
    if (
        showBreakdown
        && !_.isEmpty(menuObj._pricingBreakdown)
        && !_.isEmpty(menuObj._pricingBreakdown.breakdown)
    ) {

        var pricingBreakdown = menuObj._pricingBreakdown.breakdown;
        var borderStyle = ( showBreakdown.sectionBorder ) ? 'border: 1px solid lightgray; padding: 15px;' : '';		

        htmlString += '<div style="'+ borderStyle +'">';

        if (pricingBreakdown) {
    
            if (
                htmlString != ''
                && showPricing
            ) {
                htmlString += '<h3 style="font-weight:bold;">Total</h3><br />';
                
                if (showBreakdown.inTable === true) {
                    htmlString += '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">';
                    htmlString += '<tr style="background-color: '+ rowBackgroundColor +';"> <td style="padding:5px;">Subtotal</td> <td style="text-align:right;padding:5px;">$'+ (pricingBreakdown.subTotal/100).formatMoney() +'</td> </tr>';		
                } else {
                    htmlString += '<div style="text-align:right; font-size:14px; padding-bottom:5px;">SubTotal<b>$'+ (pricingBreakdown.subTotal/100).formatMoney() +'</b></div>';
                }
                
                if (rowBackgroundColor == '') {
                    rowBackgroundColor = '#F2F3F4';
                } else {
                    rowBackgroundColor = '';
                }
                                
            }

            // surcharges
            if (
                showPricing
                && !_.isEmpty(pricingBreakdown.surcharges)
            ) {
                
                if(showBreakdown.inTable === false){
                    htmlString += '<div style="text-align:right; padding-bottom:5px;">';				
                }
                
                _.each(pricingBreakdown.surcharges, function (surchargeAmt, surchargeId) {

                    var surchargeObj = _.findWhere(
                        Surcharges
                        , {
                            id: parseInt(surchargeId)
                        }
                    );

                    if(showBreakdown.inTable === true){
                        htmlString += '<tr style="background-color: '+ rowBackgroundColor +';"> <td style="padding:5px;">'+ surchargeObj.name +'</td> <td style="text-align:right;padding:5px;">$'+ (surchargeAmt/100).formatMoney()+'</td> </tr>';
                    }else{
                        htmlString += surchargeObj.name +' 	<b>$'+ (surchargeAmt/100).formatMoney()+'</b>';
                    }
                    
                    if(rowBackgroundColor == ''){
                        rowBackgroundColor = '#F2F3F4';
                    }else{
                        rowBackgroundColor = '';
                    }
                    
                });
                
                if(showBreakdown.inTable === false){
                    htmlString += '</div>';				
                }
                
            }

            // discounts
            if (
                !_.isEmpty(menuObj.discounts)
                && showPricing
            ) {
                
                if(showBreakdown.inTable === false){
                    htmlString += '<div style="text-align:right; padding-bottom:5px;">';				
                }
                
                _.each(menuObj.discounts, function (discount) { 
                    
                    if (typeof pricingBreakdown.discounts[discount.id] === 'number') {

                        var applyToText = '';
                        var detailText = ', ';
                        switch(discount.type){
                            
                            case 'percent_off':
                            detailText += (discount.factor/10000) +'% off '+ applyToText;
                            break;
                            
                            case 'amount_off':
                            detailText += '$'+ (discount.factor/100).formatMoney() +' off '+ applyToText;
                            break;
                            
                            case 'replace_amount':
                            detailText += 'set '+ applyToText +' to $'+ (discount.factor/100).formatMoney();
                            break;
                            
                        }
                                            
                        if(showBreakdown.inTable === true){
                            htmlString += '<tr style="background-color: '+ rowBackgroundColor +';"> <td style="padding:5px;">'+ discount.name +''+ detailText +'</td> <td style="text-align:right;padding:5px;">($'+ (-pricingBreakdown.discounts[discount.id]/100).formatMoney() +')</td> </tr>';
                        }else{
                            htmlString += discount.name  +'		<b>'+ detailText +'</b>';
                        }
    
                        if(rowBackgroundColor == ''){
                            rowBackgroundColor = '#F2F3F4';
                        }else{
                            rowBackgroundColor = '';
                        }

                    }
                                                        
                });
                
                if(showBreakdown.inTable === false){
                    htmlString += '</div>';				
                }
            
            }
            
            // taxes
            var taxes ={};
            if (
                menuObj
                && menuObj._pricingBreakdown
                && menuObj._pricingBreakdown.breakdown
                && menuObj._pricingBreakdown.breakdown.taxes
            ) {
                taxes = menuObj._pricingBreakdown.breakdown.taxes;
            }
            
            if (
                !_.isEmpty(taxes)
                && showPricing
            ) {

                if(showBreakdown.inTable === false){
                    htmlString += '<div style="text-align:right;">';				
                }
                
                _.each(taxes, function(amt, taxRateId){

                    var taxRateObj = _.findWhere(tax_rates, {id:parseInt(taxRateId)});
                    
                    if(taxRateObj.type !== 'inclusive'){

                        total += +amt;
                                                
                        if(showBreakdown.inTable === true){
                            htmlString += '<tr style="background-color: '+ rowBackgroundColor +';"> <td style="padding:5px;">'+ taxRateObj.name +' ('+ taxRateObj.rate/100 +'%) </td> <td style="text-align:right;padding:5px;">$'+ (amt/100).formatMoney() +'</td> </tr>';
                        }else{
                            htmlString += '<div style="padding-bottom:5px;">' + taxRateObj.name  +' ('+ taxRateObj.rate/100 +'%)' +' 	<b>$'+ (amt/100).formatMoney() +'</b></div>';
                        }						
                        
                    } else {
                        
                        // if(showBreakdown.inTable === true){
                        // 	htmlString += '<tr style="background-color: '+ rowBackgroundColor +';"> <td style="padding:5px;">'+ taxRateObj.name +' ('+ taxRateObj.rate/100 +'%) (inclusive)</td> <td style="text-align:right;padding:5px;">$'+ (amt/100).formatMoney() +'</td> </tr>';
                        // }else{
                        // 	htmlString += '<div style="padding-bottom:5px;" class="text-muted"><i>' + taxRateObj.name  +' ('+ taxRateObj.rate/100 +'%)' +' (inclusive)</i> 	<b>$'+ (amt/100).formatMoney() +'</b></div>';
                        // }
                    
                    }

                    if(rowBackgroundColor == ''){
                        rowBackgroundColor = '#F2F3F4';
                    }else{
                        rowBackgroundColor = '';
                    }
                                                                                                        
                });

                if (showBreakdown.inTable === false) {
                    htmlString += '</div>';				
                }
                
            }
            
            // Pricing total
            if (
                htmlString != ''
                && showPricing
            ) {
                
                if (showBreakdown.inTable === true) {
                    htmlString += '<tr style="border-top: 1px solid lightgrey; font-weight:bold;"> <td style="padding:5px;">Total</td> <td style="padding:5px; text-align:right;">$'+ (pricingBreakdown.total/100).formatMoney() +'</td> </tr>';
                } else {
                    htmlString += '<br />';
                    htmlString += '<div style="'+ totalStyle +' padding-top: 5px; text-align:right; font-size:15px;">Total:<b>  $'+ (pricingBreakdown.total/100).formatMoney() +'</b></div>';
                }
                                
            }

            if (showBreakdown.inTable === true) {
                htmlString += '</table></div>';
            } else {
                htmlString += '</div><br />';
            }

        }

    }		

    return htmlString;			
    
}

module.exports ={
    propossal_html
}