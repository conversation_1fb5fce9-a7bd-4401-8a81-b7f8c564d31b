var settings = {
    port: '8088'
  }; 
  
  settings.mysql = {
    host : 'localhost',
    database : 'test'
  };

  var TEST_ENVIRONMENT = process.env.TEST_ENVIRONMENT;
  var data_endpoint = process.env.DATA_ENDPOINT;

  if(TEST_ENVIRONMENT == "ON"){
    settings.port = ":8080";
    settings.http = "http://";
  }else{
    var port = "";
    var http = "https://";
  }

  
  // Override default settings
  switch(process.env.NODE_ENV){
    case 'production':
      settings.port = 8082;
    break;
    case 'staging':
      settings.port = 8083;
    break;     
  }
  
  module.exports = settings;