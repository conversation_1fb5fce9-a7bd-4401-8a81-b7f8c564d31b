var router = require('express').Router();
//var moment = require('moment');
var moment = require('moment-timezone');
moment.tz.setDefault('America/Panama');

// return response merge tags
router.get('/today', function(req, res) {

  var response = moment().format("MM/DD/YY, h:mm a");
  res.send(response);

});

router.get('/now', function(req, res) {

  var response = moment().format("MM/DD/YY");
  res.send(response);

});

router.get('/7days', function(req, res) {

  var response =  moment().local().add(7, 'days').format('MM/DD/YY');
  res.send(response);

});

router.get('/this_week', function(req, res) {

  var response =  'Week of '+ moment().local().startOf('week').format('M/D/YY');
  res.send(response);

});

router.get('/this_year', function(req, res) {

  var response =  moment().local().format('YYYY');
  res.send(response);

});

router.get('/last_year', function(req, res) {

  var response =  moment().subtract(1, 'year').local().format('YYYY');
  res.send(response);

});

router.post('/seven_days_after_event', function(req, res) {

  var project =  req.body.project;

  var mergeText = '';

	if (project.end_date != null && project.end_date != false)
				mergeText = moment(project.end_date).local().add(7, 'days').format('MM/DD/YY');

  res.send(mergeText);

});


router.post('/fifteen_days_after_event', function(req, res) {

  var project =  req.body.project;

  var mergeText = '';

	if (project.end_date != null && project.end_date != false)
				mergeText = moment(project.end_date).local().add(15, 'days').format('MM/DD/YY');

  res.send(mergeText);

});

router.post('/thirty_days_after_event', function(req, res) {

  var project =  req.body.project;

  var mergeText = '';

	if (project.end_date != null && project.end_date != false)
				mergeText = moment(project.end_date).local().add(30, 'days').format('MM/DD/YY');

  res.send(mergeText);

});


router.post('/ten_days_before_event', function(req, res) {

  var project =  req.body.project;

  var mergeText = '';

	if (project.end_date != null && project.end_date != false)
				mergeText = moment(project.end_date).local().subtract(10, 'days').format('MM/DD/YY');

  res.send(mergeText);

});

router.post('/ninety_days_before_event', function(req, res) {

  var project =  req.body.project;

  var mergeText = '';

	if (project.end_date != null && project.end_date != false)
				mergeText = moment(project.end_date).local().subtract(90, 'days').format('MM/DD/YY');

  res.send(mergeText);

});

router.post('/ten_days_from_today', function(req, res) {

  var project =  req.body.project;

  var mergeText = '';

	if (project.end_date != null && project.end_date != false)
				mergeText = moment(project.end_date).local().add(10, 'days').format('MM/DD/YY');

  res.send(mergeText);

});

router.post('/thirty_days_from_today', function(req, res) {

  var project =  req.body.project;

  var mergeText = '';

	if (project.end_date != null && project.end_date != false)
				mergeText = moment(project.end_date).local().add(30, 'days').format('MM/DD/YY');

  res.send(mergeText);

});

  
module.exports = router;