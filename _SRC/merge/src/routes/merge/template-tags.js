var router = require('express').Router();
require('dotenv').config();
var db = require('../..//modules/_database.js');

var core = require("../../modules/core-module.js")

// return response merge template tags
router.get('/venue_address', function(req, res) {

    var response = '';
    var project =  req.body.project;

    if (
        project.locations
        && project.locations[0]
    ) {
        response =
        project.locations[0].street +'<br />'+
        project.locations[0].city +
            ' '+ project.locations[0].state +
            ', '+ project.locations[0].zip +
            ' '+ project.locations[0].country
    }

  res.send(response);

});


router.get('/instance_address_fix', function(req, res) {

    // Get the invoice system & billing address data
    db.api.obj.getAll(
        'invoice_system'
        , function (data) {
            
            //var parsed = JSON.parse(data); // an *array* that contains the user
            //var info = parsed[0];         // a simple user

            var info = data[0]; // Address info
 
            var response = '';  // HTML to send as response

            if (info) {

                response =
                    info.billing_address.street +'<br />'+
                    info.billing_address.city +
                    ' '+ info.billing_address.state +
                    ', '+ info.billing_address.zip +
                    ' '+ info.billing_address.country;

            }

            return res.send(response);
            
        }
        , {
            billing_address: true
        }
    );

});


router.get('/instance_address', function(req, res) {

    // Get the invoice system & billing address data
    db.api.obj.getAll(
        'invoice_system'
        , function (data) {
            
            var info = data[0]; // Address info
            var response = '';  // HTML to send as response

            if (info) {

                response =
                    info.billing_address.street +'<br />'+
                    info.billing_address.city +
                    ' '+ info.billing_address.state +
                    ', '+ info.billing_address.zip +
                    ' '+ info.billing_address.country;

            }

            return res.send(response);
            
        }
        , {
            billing_address: true
        }
    );

});

router.get('/main_contact_email', function(req, res) {

    // Get the invoice system & billing address data
    db.api.obj.getAll(
        'contact_info_types'
        , function (data) {

            console.log(data);
            
            var info = data[0]; // Address info
            var response = '';  // HTML to send as response

            if (info) {

                response =
                    info.billing_address.street +'<br />'+
                    info.billing_address.city +
                    ' '+ info.billing_address.state +
                    ', '+ info.billing_address.zip +
                    ' '+ info.billing_address.country;

            }

            return res.send(response);
            
        }
        , {
            billing_address: true
        }
    );

});


router.post('/main_contact_phone', function(req, res) {

    var obj = req.body;

    db.api.obj.getWhere(
        'contact_info'
        , {
            object_id: 		obj.main_contact.id
            , childObjs: 	{
                object_id: 	true
                , info: 	true
                , street: 	true
                , city: 	true
                , state: 	true
                , zip: 		true
                , country: 	true
                , type: {
                    data_type: true
                }
                , is_primary: true
            }
        }
        , function (info) {

            console.log(info);

            if (info) {

                var ret = '';
					// var e = _.find(info.info, function (i) {

					// 	if(i.type.data_type === 'phone') {
					// 		ret = i.info;
					// 	}

					// });

					return res.send(core.formatPhoneNo(ret));

            }

        }
    
    );

});


core


router.get('/data', function(req, res) {

   var response = core.getData(true,true) ;

   res.send(response);

});

module.exports = router;