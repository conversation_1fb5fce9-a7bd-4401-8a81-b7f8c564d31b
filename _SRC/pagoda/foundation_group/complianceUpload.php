<?php

error_reporting(E_ALL);
ini_set('display_errors', '1');

set_time_limit(600000000);

require_once '../apiExample/bento.php';

$bento = new BentoAPI('foundation_group', '58179da855780a7557ef22a6035b94290982f2ce6e4ae276c5e5843b76892f0d42e1774ec5fe007f4c43beee840583fff85e24d08b2a843a2f8dfdc56ae40b57');
//echo strtotime('2019-06-01');



// Read the files into arrays.
$organizations = fopen('compliance.csv', 'r');
$oldOrgs = fopen('organizations.csv', 'r');
$mapArray = [];
$systemTags = array(
	'SureStart'=>2764819,
	'Assurance 1'=>4027757,
	'PF Level 2'=>2765307,
	'Assurance 2'=>4027949,
	'High Impace Assurance'=>4028028,
	'Bookkeeping' =>4028139,
	'EGR'=>2458275
);
$projectRoleSheetSet = '#ReU6uX';

// while(($oldOrg = fgetcsv($oldOrgs)) !== false){
// 	
// 	$mapArray[$oldOrg[3]] = $oldOrg[0];
// 	
// }

$count = 0;

// echo 'test';
// die();

// $company = $bento->getWhere(['objectType'=>'companies',
// 'queryObj'=>array(
// 	'data_source'=>intval($oldOrg[0])
// ),
// 'getChildObjs'=>false
// ])[0];
// 
// var_dump($company);
// die();	
// 
// if($company){
// 
// $contacts = $bento->getWhere(['objectType'=>'contacts',
// 	'queryObj'=>array(
// 		'data_source_id'=>$company['data_source_id']
// 	),
// 	'getChildObjs'=>false
// ]);
// 
// $updateObj = [
// 	'objectType'=>'contacts',
// 	'objectData'=>[]
// ];
// 
// if(count($contacts)>0){
// 	
// 	foreach($contacts as $contact){
// 		
// 		if($contact['parent'] != $company['id']){
// 			
// 			array_push($updateObj['objectData'], [
// 				'id'=>$contact['id'],
// 				'parent'=>$company['id'],
// 				'company'=>$company['id'],
// 				'tagged_with'=>[$company['id'], 1636580, 1763496]
// 			]);
// 			
// 		}
// 		
// 	}
// 	
// }

while(($oldOrg = fgetcsv($oldOrgs)) !== false){

	// get the company
	// get the contacts with the same data source id
	// update the contacts with the company if they don't currently match
		// parent, tagged with, company should be set to the company id

	$company = $bento->getWhere(['objectType'=>'companies',
		'queryObj'=>array(
			'data_source'=>intval($oldOrg[0])
		),
		'getChildObjs'=>false
	])[0];
var_dump($company);
die();	
	if($company){
		
		$contacts = $bento->getWhere(['objectType'=>'contacts',
			'queryObj'=>array(
				'data_source_id'=>$company['data_source_id']
			),
			'getChildObjs'=>false
		]);
		
		$updateObj = [
			'objectType'=>'contacts',
			'objectData'=>[]
		];
		
		if(count($contacts)>0){
			
			foreach($contacts as $contact){
				
				if($contact['parent'] != $company['id']){
					
					array_push($updateObj['objectData'], [
						'id'=>$contact['id'],
						'parent'=>$company['id'],
						'company'=>$company['id'],
						'tagged_with'=>[$company['id'], 1636580, 1763496]
					]);
					
				}
				
			}
			
		}
		
	}

}

die();

while(($org = fgetcsv($organizations)) !== false){
	
	$company = $bento->getWhere(['objectType'=>'companies',
		'queryObj'=>array(
			'data_source'=>intval($mapArray[$org[1]])
		),
		'getChildObjs'=>false
	])[0];
// echo '<pre>';
// var_dump($company);
// echo '</pre>';
// die();	
	if($company){
		
		$contacts = $bento->getWhere(['objectType'=>'contacts',
			'queryObj'=>array(
				'company'=>intval($company['id'])
			),
			'getChildObjs'=>false
		]);
// echo '<pre>';
// echo count($contacts);
// var_dump($contacts);
// echo '</pre>';
// die();	
		
		if(count($contacts) > 0){
			
			foreach($contacts as $contact){
				
				$infoUpdateArray = [
					'objectType'=>'contact_info',
					'objectData'=>[]
				];
				
				$info = $bento->getWhere(['objectType'=>'contact_info',
					'queryObj'=>array(
						'object_id'=>intval($contact['id'])
					),
					'getChildObjs'=>false
				]);
// echo '<pre>';
// echo count($info);
// var_dump($info);
// echo '</pre>';
// die();					
				if(count($info) > 0){
					
					foreach($info as $datum){
						
						array_push($infoUpdateArray['objectData'], [
							'id'=>$datum['id'],
							'is_primary'=>'no'
						]);
												
					}
					
					$infoUpdateArray['objectData'][0]['is_primary'] = 'yes';
					
					$contactUpdate = [
						'objectType'=>'contacts',
						'objectData'=>[
							'id'=>$contact['id'],
							'contact_info'=>array_column($info, 'id')
						]
					];

// echo '<pre>';
// var_dump($contactUpdate);
// var_dump($infoUpdateArray);
// echo '</pre>';
// die();

					$bento->update($contactUpdate);
					$bento->update($infoUpdateArray);
					
				}
				
			}
			
		}
		
	}
	
}

die();

while(($org = fgetcsv($organizations)) !== false){
	
	$company = $bento->getWhere(['objectType'=>'companies',
		'queryObj'=>array(
			'data_source'=>intval($mapArray[$org[4]])
		),
		'getChildObjs'=>false
	])[0];
	
	if($company){
		
		switch($org[6]){
			
			case '2020 990':
			
				$projectName = '2020 990';
				
				break;
			
			case '2020 990N':
				
				$projectName = '2020 990N';
				
				break;
				
			case '2020 990EZ':
			
				$projectName = '2020 990EZ';
				
				break;
				
			case '2020 990PF':
		
				$projectName = '2020 990PF';
				
				break;
				
			case 'charSol Renewal':
	
				$projectName = '2020 Charitable Solicitations - Renewal';
				
				break;
			
		}
		
		$project = $bento->getWhere(['objectType'=>'groups',
			'queryObj'=>array(
				'name'=>$projectName
			),
			'getChildObjs'=>false
		])[0];
		
// echo '<pre>';
// var_dump($project);
// echo '</pre>';
// die();		
		
		if($project){
			
			$roleSheet = $bento->getWhere(
				[
					'objectType'=>$projectRoleSheetSet,
					'queryObj'=>array(
						'tagged_with'=>[$project['id']]
					),
					'getChildObjs'=>false
				]
			)[0];
// echo '<pre>';
// var_dump($roleSheet);
// echo '</pre>';
// die();		
			
			$preparer = null;
			switch($org[2]){
				
				case 'Andrew Payne':
				$preparer = 1719900;
				break;
				
				case 'Chanda Greenwood':
				$preparer = 2139612;
				break;
				
				case 'Dylan Boyd':
				$preparer = 1674149;
				break;
				
				case 'Madi Robinson':
				$preparer = 2139672;
				break;
				
				case 'Makenzie Turner':
				$preparer = 2039070;
				break;
				
				case 'Shannon Evans':
				$preparer = 1674157;
				break;
				
			}
			
			$reviewer = null;
			switch($org[3]){
				
				case 'Andrew Payne':
				$reviewer = 1719900;
				break;
				
				case 'Chanda Greenwood':
				$reviewer = 2139612;
				break;
				
				case 'Dylan Boyd':
				$reviewer = 1674149;
				break;
				
				case 'Madi Robinson':
				$reviewer = 2139672;
				break;
				
				case 'Makenzie Turner':
				$reviewer = 2039070;
				break;
				
				case 'Shannon Evans':
				$reviewer = 1674157;
				break;
				
			}
			
			if($roleSheet){
				
				$updateObj = [
					'objectType'=>$projectRoleSheetSet,
					'objectData'=>[
						'id'=>$roleSheet['id'],
						'_1'=>$preparer,
						'_2'=>$reviewer
					]
				];
				
// echo '<pre>';
// var_dump($updateObj);
// echo '</pre>';
// die();				
				
				$roleSheet = $bento->update($updateObj);
				
			}else{
				
				$createObj = [
					'objectType'=>$projectRoleSheetSet,
					'objectData'=>[
						'name'=> $projectName.' Role Sheet',
						'_1'=>$preparer,
						'_2'=>$reviewer
					]
				];
					
				$bento->create($updateObj);
				
				$tags = $project['tagged_with'];
				$tags = array_push($tags, $project['id']);
				
				$updateObj = [
					'objectType'=>$projectRoleSheetSet,
					'objectData'=>[
						'id'=>$createObj['id'],
						'tagged_with'=>$tags
					]
				];
				$roleSheet = $bento->update($updateObj);
// echo '<pre>';
// var_dump($updateObj);
// var_dump($createObj);
// echo '</pre>';
// die();				
			}
			
		}
		
	}
	
// echo '<pre>';
// var_dump($roleSheet);
// echo '</pre>';
// die();	
	
}

die();

while(($org = fgetcsv($organizations)) !== false){

	$company = $bento->getWhere(['objectType'=>'companies',
		'queryObj'=>array(
			'data_source'=>intval($mapArray[$org[1]])
		),
		'getChildObjs'=>false
	])[0];

	if($company){
		
		$contacts = $bento->getWhere(['objectType'=>'contacts',
			'queryObj'=>array(
				'tagged_with'=>intval($company['id'])
			),
			'getChildObjs'=>false
		]);
		
		$contact = null;
		if($contacts){
			
			foreach($contacts as $contactLookup){
			
				if($contactLookup['name'] == $org[6]){
					$contact = $contactLookup;
				}
				
			}
			
		}
		
		if($contact){
			
			$contactInfo = $bento->getWhere(['objectType'=>'contact_info',
				'queryObj'=>array(
					'object_id'=>$contact['id']
				),
				'getChildObjs'=>false
			]);

			if($contactInfo){
	
				foreach($contactInfo as $info){
					
					if($info['type'] == 1636586){
						
						$emailUpdate = $info;
						
					}
					
					if($info['type'] == 1636587){
						
						$phoneUpdate = $info;
						
					}
					
				}
				
				if($contact){
					
					$email = [
						'objectType'=>'contact_info',
						'objectData'=>[
							'id'=>$emailUpdate['id'],
							'info'=>$org[7]
						]
					];
					
					$phone = [
						'objectType'=>'contact_info',
						'objectData'=>[
							'id'=>$emailUpdate['id'],
							'info'=>$org[8]
						]
					];
					
					$serviceType = $org[3];
					$state = $org[2];
					$flag1 = $org[9];
					$flag2 = $org[10];
					$dueDate = new DateTime($org[5]);
					$notes = $org[11];
					$tags = [$company['id'], $contact['id'], 1636580, 1763496];
					$clientService = [
						'objectType'=>'',
						'objectData'=>[]
					];
					
					if($serviceType != 'charSol Renewal' && $serviceType != 'CharSol Initial'){
						
						if($flag1){
							array_push($tags, $systemTags[$flag1]);
						}
						
						if($flag2){
							array_push($tags, $systemTags[$flag2]);
						}
						
						switch($serviceType){
							
							case '2020 990':
							
								$templateName = '990 TEMPLATE';
								$projectName = '2020 990';
								
								break;
							
							case '2020 990N':
								
								$templateName = '990N TEMPLATE';
								$projectName = '2020 990N';
								
								break;
								
							case '2020 990EZ':
							
								$templateName = '990EZ TEMPLATE';
								$projectName = '2020 990EZ';
								
								break;
								
							case '2020 990PF':
						
								$templateName = '990PF TEMPLATE';
								$projectName = '2020 990PF';
								
								break;
							
						}
	
						$projectTemplate = $bento->getWhere(['objectType'=>'groups',
							'queryObj'=>array(
								'name'=>$templateName
							),
							'getChildObjs'=>false
						])[0];
	
						$newProject = $projectTemplate;
						
						unset($newProject['id']);
						unset($newProject['tools']);
						$newProject['is_template'] = false;
						$newProject['name'] = $projectName;
						$newProject['main_company'] = $company['id'];
						$newProject['parent'] = $company['id'];
						$newProject['main_contact'] = $contact['id'];
						$newProject['main_client'] = $contact['id'];
						$newProject['tagged_with'] = $tags;
// echo '<pre>';
// print_r($newProject);
// echo '</pre>';
// die();	
						
						$projectDueDate = new DateTime();
						switch($org[4]){
							
							case 'June':
							
								$projectDueDate->setDate(2021, 11, 15);
							
								break;
								
							default:
							
								$projectDueDate->setDate(2021, 5, 15);
							
						}
						
						$newProject['end_date'] = $projectDueDate->format('Y-m-d');
						$createdProject = $bento->createFromTemplate($projectTemplate['id']);
// echo '<pre>';
// print_r($createdProject);
// echo '</pre>';
// die();	
						
						$newProject['id'] = $createdProject['id'];
						
						$bento->update($email);
						$bento->update($phone);
						$updatedProject = $bento->update([
							'objectType'=>'groups',
							'objectData'=>$newProject
						]);
						
						$tags = array_merge($tags, $updatedProject['tagged_with']);
						
						$updatedProject = $bento->update([
							'objectType'=>'groups',
							'objectData'=>[
								'id'=>$newProject['id'],
								'tagged_with'=>$tags
							]
						]);
						
						if($notes){
							
							$noteBody = '<h3>Import Notes:</h3><br />';
							$noteBody .= $notes;
							
							$note = array(
								'type'=>'groups',
								'type_id'=>$createdProject['id'],
								'note'=>$noteBody,
								'author'=>0,
								'note_type'=>3755960,
								'notifyUsers'=>array()
							);
							
							$bento->create(array(
								'objectType'=>'notes',
								'objectData'=>$note
							));
							
						}
						
// echo '<pre>';
// print_r($updatedProject);
// echo '</pre>';
// die();					

							
					}
					
					if($serviceType == 'charSol Renewal' || $serviceType == 'CharSol Initial'){
						
						if($flag1){
							array_push($tags, $systemTags[$flag1]);
						}
						
						if($flag2){
							array_push($tags, $systemTags[$flag2]);
						}
						
						switch($serviceType){
							
							case 'CharSol Initial':
							
								$templateName = 'Charitable Solicitations Initial TEMPLATE';
								$projectName = '2020 Charitable Solicitations - Initial';
								$clientService['objectData']['name'] = '2020 '. $state .' Charitable Solicitations - Initial';
								$clientService['objectType'] = '#HDIjlE.oKxt3d';
								
								break;
							
							case 'charSol Renewal':
								
								$templateName = 'Charitable Solicitations Renewal TEMPLATE';
								$projectName = '2020 Charitable Solicitations - Renewal';
								$clientService['objectType'] = '#HDIjlE.lXjaM4';
								$clientService['objectData']['name'] = '2020 '. $state .' Charitable Solicitations - Renewal';
								$clientService['objectData']['_4'] = $state;
								$clientService['objectData']['_5'] = [
									'zip'=>'',
									'add2'=>'',
									'city'=>'',
									'state'=>$state,
									'street'=>'',
									'country'=>'US'
								];
								$clientService['objectData']['_6'] = $notes;
								$clientService['objectData']['_9'] = $dueDate->format('Y-m-d');
								
								break;
								
						}
						
						$charSolProjectCheck = $bento->getWhere(['objectType'=>'groups',
							'queryObj'=>array(
								'name'=>$projectName,
								'tagged_with'=>$company['id']
							),
							'getChildObjs'=>false
						])[0];
						
						if(!$charSolProjectCheck){
							
							$projectTemplate = $bento->getWhere(['objectType'=>'groups',
								'queryObj'=>array(
									'name'=>$templateName
								),
								'getChildObjs'=>false
							])[0];
		
							$newProject = $projectTemplate;
							
							unset($newProject['id']);
							unset($newProject['tools']);
							unset($newProject['is_template']);
							$newProject['name'] = $projectName;
							$newProject['main_company'] = $company['id'];
							$newProject['parent'] = $company['id'];
							$newProject['main_contact'] = $contact['id'];
							$newProject['main_client'] = $contact['id'];
							$newProject['tagged_with'] = $tags;
							$newProject['end_date'] = $dueDate->format('Y-m-d');
							
// echo '<pre>';
// print_r($newProject);
// echo '</pre>';
// die();	
							
							$createdProject = $bento->createFromTemplate($projectTemplate['id']);
							
							$newProject['id'] = $createdProject['id'];
							
							$bento->update($email);
							$bento->update($phone);
							$updatedProject = $bento->update([
								'objectType'=>'groups',
								'objectData'=>$newProject
							]);
							
							$fullTagList = array_merge($tags, $updatedProject['tagged_with']);
							
							$updatedProject = $bento->update([
								'objectType'=>'groups',
								'objectData'=>[
									'id'=>$newProject['id'],
									'tagged_with'=>$fullTagList
								]
							]);
							
							$charSolProjectCheck = $updatedProject;
							
						}
						
// echo '<pre>';
// print_r($charSolProjectCheck);
// echo '</pre>';
// die();	
						
// echo '<pre>';
// print_r($createdProject);
// echo '</pre>';
// die();	
						
						array_push($tags, $charSolProjectCheck['id']);
						
						
						$clientService['objectData']['parent'] = $charSolProjectCheck['id'];
						$clientService['objectData']['tagged_with'] = $tags;
						
						
												
// echo '<pre>';
// print_r($tags);
// echo '</pre>';
// die();					

						$createdClientService = $bento->create($clientService);

// echo '<pre>';
// print_r($createdClientService);
// echo '</pre>';
// die();					

							
					}
					
				}
				
			}
			
		}
		
	}	
		
}

die();

while(($publicCharityType = fgetcsv($publicCharityTypesCSV)) !== false){
	
	$publicCharityTypes[$publicCharityType[0]] = $publicCharityType[1];
	
}

while(($org = fgetcsv($organizations)) !== false){
	
	$coreDemographic = [
		'objectType'=>'#ml7laG',
		'objectData'=>[]
	];
	
	$company = $bento->getWhere(['objectType'=>'companies',
		'queryObj'=>array(
			'data_source'=>intval($org[0])
		),
		'getChildObjs'=>false
	])[0];
	
	if(count($company) > 0){
		
		$cLevelString = 'Unknown';
		if($org[8]){
			$cLevelString = $org[8];
		}
		
		$ppLookup = 1;
		switch($org[7]){
			
			case '455':
				$ppLookup = 4;
				break;
				
			case '137':
				$ppLookup = 2;
				break;
			
			case '138':
				$ppLookup = 3;
				break;
			
		}
		
		$accountNotes = '';
		$accountNotes = $publicCharityTypes[$org[23]];
		$accountNotes .= $charityTypes[$org[11]];

		$coreDemographic['objectData']['name'] = $company['name'];
		$coreDemographic['objectData']['tagged_with'] = [1636580, 1763496, $company['id']];
		$coreDemographic['objectData']['parent'] = $company['id'];
		$coreDemographic['objectData']['_1'] = $org[12];
		$coreDemographic['objectData']['_2'] = $org[18];
		$coreDemographic['objectData']['_3'] = $cLevelString;
		$coreDemographic['objectData']['_4'] = $ppLookup;
		$coreDemographic['objectData']['_21'] = $org[9];
		$coreDemographic['objectData']['_22'] = (intval($org[10]) * 100000);
		$coreDemographic['objectData']['_23'] = [
			'state'=>$org[19],
			'country'=>'US'
		];
		$coreDemographic['objectData']['_24'] = $org[20];
		$coreDemographic['objectData']['_25'] = $org[21];
		$coreDemographic['objectData']['_26'] = $org[22];
		$coreDemographic['objectData']['_31'] = $company['name'];
		$coreDemographic['objectData']['_38'] = $accountNotes;
		
		echo '<pre>';
		print_r($coreDemographic);
		echo '</pre>';
		
		$alreadyCreated = $bento->getWhere(['objectType'=>'#ml7laG',
			'queryObj'=>array(
				'parent'=>$company['id']
			),
			'getChildObjs'=>false
		])[0];
		
		if($alreadyCreated){
			
			$coreDemographic['objectData']['id'] = $alreadyCreated['id'];
			
			$created = $bento->update($coreDemographic);
			
		}else{
			
			$created = $bento->create($coreDemographic);
			
		}
		
		$count++;
		
		if($count > 50){
			//die();
		}
				
	}
	
}

// while(($company = fgetcsv($organizations)) !== false){
// 		
// 	$companiesToCreate[] = array(
// 		'objectType'=>'companies',
// 		'objectData'=>array(
// 			'name'=>$company[2],
// 			'type'=>1652262,
// 			'tagged_with'=>[1636580, 1763496],
// 			'date_created'=>$company[16],
// 			'data_source'=>$company[0], // contact ID
// 			'data_source_id'=>$company[1] // client ID
// 		)
// 	);
// 	
// }

//die();

// var_dump($companiesToCreate);
// die();

// foreach($companiesToCreate as $company){
// 	
// // 	if($company['objectData']['data_source']){
// // //echo intval($company['objectData']['data_source']);		
// // 		$objectsAlreadyThere = $bento->getWhere(['objectType'=>'companies',
// // 			'queryObj'=>array(
// // 				'data_source'=>intval($company['objectData']['data_source'])
// // 			),
// // 			'getChildObjs'=>false
// // 		]);
// // //echo ' - '.count($objectsAlreadyThere) .' <br /><br />';	
// // 		foreach($objectsAlreadyThere as $toDelete){
// // 			
// // 			$bento->archive('companies', $toDelete['id']);
// // 			
// // 		}
// // 		
// // 		// $count++;
// // 		// 
// // 		// if($count > 20){
// // 		// 	die();
// // 		// }
// // 		
// // 	}
// 	
// 	$bento->create($company);
// 	
// }
// 
// echo 'done';
// die();

while(($contact = fgetcsv($contacts)) !== false){
	
	//print_r($line);
var_dump(intval($contact[1]));	
	$company = $bento->getWhere(['objectType'=>'companies',
			'queryObj'=>array(
				'data_source'=>intval($contact[1])
			),
			'getChildObjs'=>false
		])[0];
//var_dump($company);	
	if($company){
		
		// $contactsToCreate[] = [
		// 	'objectType'=>'contacts',
		// 	'objectData'=>[
		// 		'fname'=>$contact[3],
		// 		'lname'=>$contact[5],
		// 		'type'=>1652260,
		// 		'company'=>$company['id'],
		// 		'parent'=>$company['id'],
		// 		'data_source'=>$contact[0], // contact ID
		// 		'data_source_id'=>$contact[1] // client ID
		// 	]
		// ];
		
		$bento->create([
			'objectType'=>'contacts',
			'objectData'=>[
				'fname'=>$contact[3],
				'lname'=>$contact[5],
				'type'=>1652260,
				'tagged_with'=>[1636580, 1763496, $company['id']],
				'company'=>$company['id'],
				'parent'=>$company['id'],
				'date_created'=>$contact[9],
				'data_source'=>$contact[0], // contact ID
				'data_source_id'=>$contact[1] // client ID
			]
		]);
			
	}
	
	
	// $count++;
	// 
	// 
	// if($count > 5){
	// 	echo 'done';
	// 	die;
	// }
	
}


echo 'done';
die();


$page = 0;
$objects = $bento->getWhere(array(
		'objectType'=>'requests',
		'queryObj'=>array(
			'status'=>'Shipped',
			'adults'=>array(
				'type'=>'greater_than',
				'value'=>300
			),
			'date_created'=>array(
				'type'=>'between',
				'start'=>strtotime('2019-05-08'),
				'end'=>strtotime('2020-01-01')
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'contact'=>true,
			'company'=>true,
			'adults'=>true
		)
	));
	

/*
echo 'Page: '.$page.'<br />';
//echo count($objects['data']).'<br />';
print_r($objects);
*/

//$page++;

//echo 'Page: '.$page.'<br />';
//echo 'test';
die;

/*
$page = 0;
$object = array(
	'data'=>array(
		'item'=>'one'
	)
);
*/
//echo $objects['recordsTotal'];

/*
echo 'test';
die();
*/

while(
	
// 	$page < 2
	
	count($objects['data']) >= 1
	&& $objects['recordsTotal'] > (1 * $page)
	
	){
	
	$objects = $bento->getWhere(array(
		'objectType'=>'requests',
		'queryObj'=>array(
			'status'=>'Shipped',
			'adults'=>array(
				'type'=>'greater_than',
				'value'=>300
			),
			'date_created'=>array(
				'type'=>'between',
				'start'=>strtotime('2019-05-08'),
				'end'=>strtotime('2020-01-01')
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'contact'=>true,
			'company'=>true,
			'adults'=>true
		)
	));

	echo '<br />Page: '.$page.'<br />';
	echo count($objects['data']).'<br />';
	print_r($objects);
	
	$page++;
	
	$tasks = array();
	
	foreach($objects['data'] as $req){
//echo 'contactID '.$req['contact']['id'].'<br />';	

		if($req['adults'] < 500){
			
			$cellPhone = $bento->getWhere(array(
				'objectType'=>'contact_info',
				'queryObj'=>array(
					'type'=>49,
					'object_id'=>$req['contact']['id'],
					'is_primary'=>'yes'
				)
			));
	//var_dump($cellPhone);
			$churchPhone = $bento->getWhere(array(
				'objectType'=>'contact_info',
				'queryObj'=>array(
					'type'=>60,
					'object_id'=>$req['contact']['id']
				)
			));
			
			$createdAlreadyCheck = $bento->getWhere(array(
				'objectType'=>'#Follow_Up',
				'queryObj'=>array(
					'_3'=>$req['contact']['id']
				)
			));
var_dump($createdAlreadyCheck);			
			if(count($createdAlreadyCheck) >= 1){
				
				
			}else{
				
				$tasks[] = array(
					'objectType'=>'#Follow_Up',
					'objectData'=>array(
						'name'=>'Call '. $req['contact']['fname'].' '.$req['contact']['lname'],
						'_1'=>1,
						'_2'=>$req['contact']['manager'],
						//'_2'=>11,
						'_3'=>$req['contact']['id'],
						'_6'=>$cellPhone[0]['info'],
						'_9'=>$churchPhone[0]['info'],
						'tagged_with'=>array(1431795, $req['contact']['manager'])
					)
				);
				
			}
			
		}
		
	}
	
	var_dump($tasks);
	
	foreach($tasks as $task){
		
		$bento->create($task);
		
	}
		
}
	
?>