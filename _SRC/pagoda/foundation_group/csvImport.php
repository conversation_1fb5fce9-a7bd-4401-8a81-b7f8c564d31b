<?php

error_reporting(E_ALL);
ini_set('display_errors', '1');

set_time_limit(60000);

require_once '../apiExample/bento.php';

$bento = new BentoAPI('foundation_group', '58179da855780a7557ef22a6035b94290982f2ce6e4ae276c5e5843b76892f0d42e1774ec5fe007f4c43beee840583fff85e24d08b2a843a2f8dfdc56ae40b57');
//echo strtotime('2019-06-01');

$objects = $bento->getWhere(['objectType'=>'document',
'queryObj'=>array(
	'search'=>[
		'fields'=>['name'],
		'value'=>'irs',
		'type'=>'or'
	]
),
'getChildObjs'=>[
	'name'=>true
]
]);
var_dump($objects);

echo 'test';
die();


// Read the files into arrays.
$contacts = fopen('contacts.csv', 'r');
$contactEmails = fopen('contactEmails.csv', 'r');
$contactNumbers = fopen('contactNumbers.csv', 'r');

$organizations = fopen('organizations.csv', 'r');
$organizationAddresses = fopen('organizationAddresses.csv', 'r');
$organizationSystems = fopen('organization_system.csv', 'r');

$charityTypesCSV = fopen('chrityTypes.csv', 'r');
$charityTypes = [];
$publicCharityTypesCSV = fopen('publicCharity.csv', 'r');
$publicCharityTypes = [];

// Loop over contacts to start building the contact objects
$contactsToCreate = [];
$companiesToCreate = [];
$emailsToCreate = [];
$count = 0;

while(($charityType = fgetcsv($charityTypesCSV)) !== false){
	
	$charityTypes[$charityType[0]] = $charityType[1];
	
}

while(($publicCharityType = fgetcsv($publicCharityTypesCSV)) !== false){
	
	$publicCharityTypes[$publicCharityType[0]] = $publicCharityType[1];
	
}

while(($org = fgetcsv($organizations)) !== false){
	
	$coreDemographic = [
		'objectType'=>'#ml7laG',
		'objectData'=>[]
	];
	
	$company = $bento->getWhere(['objectType'=>'companies',
		'queryObj'=>array(
			'data_source'=>intval($org[0])
		),
		'getChildObjs'=>false
	])[0];
	
	if(count($company) > 0){
		
		$cLevelString = 'Unknown';
		if($org[8]){
			$cLevelString = $org[8];
		}
		
		$ppLookup = 1;
		switch($org[7]){
			
			case '455':
				$ppLookup = 4;
				break;
				
			case '137':
				$ppLookup = 2;
				break;
			
			case '138':
				$ppLookup = 3;
				break;
			
		}
		
		$accountNotes = '';
		$accountNotes = $publicCharityTypes[$org[23]];
		$accountNotes .= $charityTypes[$org[11]];

		$coreDemographic['objectData']['name'] = $company['name'];
		$coreDemographic['objectData']['tagged_with'] = [1636580, 1763496, $company['id']];
		$coreDemographic['objectData']['parent'] = $company['id'];
		$coreDemographic['objectData']['_1'] = $org[12];
		$coreDemographic['objectData']['_2'] = $org[18];
		$coreDemographic['objectData']['_3'] = $cLevelString;
		$coreDemographic['objectData']['_4'] = $ppLookup;
		$coreDemographic['objectData']['_21'] = $org[9];
		$coreDemographic['objectData']['_22'] = (intval($org[10]) * 100000);
		$coreDemographic['objectData']['_23'] = [
			'state'=>$org[19],
			'country'=>'US'
		];
		$coreDemographic['objectData']['_24'] = $org[20];
		$coreDemographic['objectData']['_25'] = $org[21];
		$coreDemographic['objectData']['_26'] = $org[22];
		$coreDemographic['objectData']['_31'] = $company['name'];
		$coreDemographic['objectData']['_38'] = $accountNotes;
		
		echo '<pre>';
		print_r($coreDemographic);
		echo '</pre>';
		
		$alreadyCreated = $bento->getWhere(['objectType'=>'#ml7laG',
			'queryObj'=>array(
				'parent'=>$company['id']
			),
			'getChildObjs'=>false
		])[0];
		
		if($alreadyCreated){
			
			$coreDemographic['objectData']['id'] = $alreadyCreated['id'];
			
			$created = $bento->update($coreDemographic);
			
		}else{
			
			$created = $bento->create($coreDemographic);
			
		}
		
		$count++;
		
		if($count > 50){
			//die();
		}
				
	}
	
}

// while(($company = fgetcsv($organizations)) !== false){
// 		
// 	$companiesToCreate[] = array(
// 		'objectType'=>'companies',
// 		'objectData'=>array(
// 			'name'=>$company[2],
// 			'type'=>1652262,
// 			'tagged_with'=>[1636580, 1763496],
// 			'date_created'=>$company[16],
// 			'data_source'=>$company[0], // contact ID
// 			'data_source_id'=>$company[1] // client ID
// 		)
// 	);
// 	
// }

//die();

// var_dump($companiesToCreate);
// die();

// foreach($companiesToCreate as $company){
// 	
// // 	if($company['objectData']['data_source']){
// // //echo intval($company['objectData']['data_source']);		
// // 		$objectsAlreadyThere = $bento->getWhere(['objectType'=>'companies',
// // 			'queryObj'=>array(
// // 				'data_source'=>intval($company['objectData']['data_source'])
// // 			),
// // 			'getChildObjs'=>false
// // 		]);
// // //echo ' - '.count($objectsAlreadyThere) .' <br /><br />';	
// // 		foreach($objectsAlreadyThere as $toDelete){
// // 			
// // 			$bento->archive('companies', $toDelete['id']);
// // 			
// // 		}
// // 		
// // 		// $count++;
// // 		// 
// // 		// if($count > 20){
// // 		// 	die();
// // 		// }
// // 		
// // 	}
// 	
// 	$bento->create($company);
// 	
// }
// 
// echo 'done';
// die();

while(($contact = fgetcsv($contacts)) !== false){
	
	//print_r($line);
var_dump(intval($contact[1]));	
	$company = $bento->getWhere(['objectType'=>'companies',
			'queryObj'=>array(
				'data_source'=>intval($contact[1])
			),
			'getChildObjs'=>false
		])[0];
//var_dump($company);	
	if($company){
		
		// $contactsToCreate[] = [
		// 	'objectType'=>'contacts',
		// 	'objectData'=>[
		// 		'fname'=>$contact[3],
		// 		'lname'=>$contact[5],
		// 		'type'=>1652260,
		// 		'company'=>$company['id'],
		// 		'parent'=>$company['id'],
		// 		'data_source'=>$contact[0], // contact ID
		// 		'data_source_id'=>$contact[1] // client ID
		// 	]
		// ];
		
		$bento->create([
			'objectType'=>'contacts',
			'objectData'=>[
				'fname'=>$contact[3],
				'lname'=>$contact[5],
				'type'=>1652260,
				'tagged_with'=>[1636580, 1763496, $company['id']],
				'company'=>$company['id'],
				'parent'=>$company['id'],
				'date_created'=>$contact[9],
				'data_source'=>$contact[0], // contact ID
				'data_source_id'=>$contact[1] // client ID
			]
		]);
			
	}
	
	
	// $count++;
	// 
	// 
	// if($count > 5){
	// 	echo 'done';
	// 	die;
	// }
	
}


echo 'done';
die();


$page = 0;
$objects = $bento->getWhere(array(
		'objectType'=>'requests',
		'queryObj'=>array(
			'status'=>'Shipped',
			'adults'=>array(
				'type'=>'greater_than',
				'value'=>300
			),
			'date_created'=>array(
				'type'=>'between',
				'start'=>strtotime('2019-05-08'),
				'end'=>strtotime('2020-01-01')
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'contact'=>true,
			'company'=>true,
			'adults'=>true
		)
	));
	

/*
echo 'Page: '.$page.'<br />';
//echo count($objects['data']).'<br />';
print_r($objects);
*/

//$page++;

//echo 'Page: '.$page.'<br />';
//echo 'test';
die;

/*
$page = 0;
$object = array(
	'data'=>array(
		'item'=>'one'
	)
);
*/
//echo $objects['recordsTotal'];

/*
echo 'test';
die();
*/

while(
	
// 	$page < 2
	
	count($objects['data']) >= 1
	&& $objects['recordsTotal'] > (1 * $page)
	
	){
	
	$objects = $bento->getWhere(array(
		'objectType'=>'requests',
		'queryObj'=>array(
			'status'=>'Shipped',
			'adults'=>array(
				'type'=>'greater_than',
				'value'=>300
			),
			'date_created'=>array(
				'type'=>'between',
				'start'=>strtotime('2019-05-08'),
				'end'=>strtotime('2020-01-01')
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'contact'=>true,
			'company'=>true,
			'adults'=>true
		)
	));

	echo '<br />Page: '.$page.'<br />';
	echo count($objects['data']).'<br />';
	print_r($objects);
	
	$page++;
	
	$tasks = array();
	
	foreach($objects['data'] as $req){
//echo 'contactID '.$req['contact']['id'].'<br />';	

		if($req['adults'] < 500){
			
			$cellPhone = $bento->getWhere(array(
				'objectType'=>'contact_info',
				'queryObj'=>array(
					'type'=>49,
					'object_id'=>$req['contact']['id'],
					'is_primary'=>'yes'
				)
			));
	//var_dump($cellPhone);
			$churchPhone = $bento->getWhere(array(
				'objectType'=>'contact_info',
				'queryObj'=>array(
					'type'=>60,
					'object_id'=>$req['contact']['id']
				)
			));
			
			$createdAlreadyCheck = $bento->getWhere(array(
				'objectType'=>'#Follow_Up',
				'queryObj'=>array(
					'_3'=>$req['contact']['id']
				)
			));
var_dump($createdAlreadyCheck);			
			if(count($createdAlreadyCheck) >= 1){
				
				
			}else{
				
				$tasks[] = array(
					'objectType'=>'#Follow_Up',
					'objectData'=>array(
						'name'=>'Call '. $req['contact']['fname'].' '.$req['contact']['lname'],
						'_1'=>1,
						'_2'=>$req['contact']['manager'],
						//'_2'=>11,
						'_3'=>$req['contact']['id'],
						'_6'=>$cellPhone[0]['info'],
						'_9'=>$churchPhone[0]['info'],
						'tagged_with'=>array(1431795, $req['contact']['manager'])
					)
				);
				
			}
			
		}
		
	}
	
	var_dump($tasks);
	
	foreach($tasks as $task){
		
		$bento->create($task);
		
	}
		
}
	
?>