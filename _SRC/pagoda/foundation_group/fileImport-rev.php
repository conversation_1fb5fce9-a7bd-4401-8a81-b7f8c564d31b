<?php

// error_reporting(E_ALL);
// ini_set('display_errors', '1');

set_time_limit(6000000);

require_once '../apiExample/bento.php';

$bento = new BentoAPI('foundation_group', '58179da855780a7557ef22a6035b94290982f2ce6e4ae276c5e5843b76892f0d42e1774ec5fe007f4c43beee840583fff85e24d08b2a843a2f8dfdc56ae40b57');

$dir    = './client_files';
$organizations = fopen('organizations-rev.csv', 'r+');
$clientFiles = scandir($dir);

$GLOBALS["organizations-temp.csv"] = "organizations-rev-temp.csv";
$GLOBALS["organizations.csv"] = "organizations-rev.csv";

function removeRow($billingCode){
	$removed = false;
	$fptemp = fopen($GLOBALS["organizations-temp.csv"], "a+");
	if (($handle = fopen($GLOBALS["organizations.csv"], "r")) !== FALSE) {
		while (($data = fgetcsv($handle)) !== FALSE) {
// echo $billingCode.'<br />';
// echo $data[3].'<br /><br />';
		if ($billingCode != $data[3] ){
			$list = array($data);
			fputcsv($fptemp, $data);
			$removed = true;
		}
	}
	}
	fclose($handle);
	fclose($fptemp);
	unlink($GLOBALS["organizations.csv"]);
	rename($GLOBALS["organizations-temp.csv"], $GLOBALS["organizations.csv"]);
	return $removed;
}

function uploadFiles($bento, $currentDirectory, $company, $topDirectory, $count, $parent, $skipCheck){
echo '<br /><br />NEW LOOP<br />';				
echo $company['name'].'<br />';	
	if(!$count){
		$count = 2;
	}
	
	if(!$parent){
		$parent = false;
	}
	
	if(!$currentDirectory[$count]){
echo 'END COMPANY<br />';
		return true;
	}
	
	if($currentDirectory[$count] == '.' || $currentDirectory[$count] == '..'){
		
		$count++;

		uploadFiles($bento, $currentDirectory, $company, $topDirectory, $count, $parent, $skipCheck);
		
		return true;
		
	}
	
	$isFolder = is_dir($topDirectory.'/'.$currentDirectory[$count]);

	if($isFolder){
		
		if(!$skipCheck){
			
			$query = array(
				'name'=>$currentDirectory[$count],
				'document_type'=>'folder',
				'tagged_with'=>[1636580, 1763496, intval($company['id'])]
			);
			
			if($parent){
				$query['parent'] = $parent['id'];
			}
	
			$searchFiles = $bento->getWhere(['objectType'=>'document',
				'queryObj'=>$query,
				'getChildObjs'=>[
					'name'=>true
				]
			]);
			
		}else{
			
			$searchFiles = [];
			
		}
		
		if($searchFiles[0]){
			
			$folderObj = $searchFiles[0];
echo 'Folder found '.$folderObj['name'].'<br />';				
		}else{
			
			// create a folder
			$newFolder = array(
				'objectType'=>'document',
				'objectData'=>array(
					'name'=>$currentDirectory[$count],
					'document_type'=>'folder',
					'category'=>0,
					'is_public'=>1,
					'tagged_with'=>[1636580, 1763496, intval($company['id'])]
				));
				
			if($parent){
				
				$newFolder['objectData']['parent'] = $parent['id'];
				
			}
echo 'Create a folder '.$newFolder['objectData']['name'].'<br />';				
													
			$folderObj = $bento->create($newFolder);
			
		}
		
		$innerDirectory = scandir($topDirectory.'/'.$currentDirectory[$count]);

		uploadFiles($bento, $innerDirectory, $company, $topDirectory.'/'.$currentDirectory[$count], 0, $folderObj, $skipCheck);
		
		$count++;
		
		uploadFiles($bento, $currentDirectory, $company, $topDirectory, $count, $parent, $skipCheck);
		
	}else{
		
		// upload a file
		
		if(!$skipCheck){
			$query = array(
				'name'=>$currentDirectory[$count],
				'tagged_with'=>[1636580, 1763496, intval($company['id'])]
			);
			
			if($parent){
				$query['parent'] = $parent['id'];
			}
	
			$searchFiles = $bento->getWhere(['objectType'=>'document',
				'queryObj'=>$query,
				'getChildObjs'=>[
					'name'=>true
				]
			]);
		}else{
			$searchFiles = [];
		}
		
		if(!$searchFiles[0]){
			
			$newFile = [];
			$newFile['name'] = $currentDirectory[$count];
			$newFile['filePath'] = $topDirectory.'/'.$currentDirectory[$count];
			$newFile['tagged_with'] = [intval($company['id']), 1636580, 1763496];
			$newFile['is_public'] = 1;
			
			if($parent){
				
				$newFile['parent'] = $parent['id'];
	
			}
echo 'Create a file '.$newFile['name'].'<br />';				
			$newObj = $bento->createFile($newFile);
		
		}else{
			
echo 'File found '.$searchFiles[0]['name'].'<br />';							
			
		}
		
//var_dump($newObj);

		$count++;

		uploadFiles($bento, $currentDirectory, $company, $topDirectory, $count, $parent, $skipCheck);

//die();
			
		}
		
	}
		
$loopCount = 0;

while(($organization = fgetcsv($organizations)) !== false){
		

	$billingCode = $organization[3];
	$organizationName = $organization[2];
	$dataSourceId = intval($organization[0]);
	$topDirectory = './client_files/'.substr($organizationName, 0,1);


	$searchDirectory = scandir($topDirectory);
//var_dump($searchDirectory);	
	
	$foundFolder = false;
	foreach($searchDirectory as $key => $clientFolderName){
		
		if(strpos($clientFolderName, $billingCode) !== false){
			$foundFolder = $clientFolderName;
			break;
		}
		
	}

	$clientFolder = scandir($topDirectory.'/'.$foundFolder);
	
	if($foundFolder){
		
		// get item from Bento
		$company = $bento->getWhere(['objectType'=>'companies',
			'queryObj'=>array(
				'data_source'=>$dataSourceId
			),
			'getChildObjs'=>false
		]);
		
		if($company[0]){
			
			$skipCheck = true;
			
			$documents = $bento->getWhere(['objectType'=>'document',
				'queryObj'=>array(
					'tagged_with'=>$company[0]['id']
				),
				'getChildObjs'=>false
			]);
			
			if(count($documents) > 0){
				$skipCheck = false;
			}

			// have company from Bento and client folder, begin upload
			uploadFiles($bento, $clientFolder, $company[0], $topDirectory.'/'.$foundFolder, 2, false, $skipCheck);
			
		}
			
	}
		
	removeRow($billingCode);
	
	// $loopCount++;
	// if($loopCount > 0){
	// 	break;
	// }
	
}

fclose($organizations);

echo 'done';
die();

foreach($files1 as $folder){
	
//var_dump($folder);
	
	if($folder != '.' && $folder != '..'){
		
		if(is_dir($dir.'/'.$folder)){
			
			$innerFolders = scandir($dir.'/'.$folder);
			
			foreach($innerFolders as $innerFolder){
				
				if($innerFolder != '.' && $innerFolder != '..'){
					
					$companyName = explode("#",$innerFolder)[0];
					
echo $companyName.'<br />';
					
					$companies = $bento->getWhere(['objectType'=>'companies',
						'queryObj'=>array(
							'name'=>$companyName
						),
						'paged'=>array(
							'page'=>0,
							'sortCol'=>'date_created',
							'sortDir'=>'asc',
							'pageLength'=>1
						),
						'getChildObjs'=>[
							'name'=>true
						]
						]);
						
					if($companies[0] && $companies[0]['name']){
						
						$company = $companies[0];

						$companyDirectory = scandir($dir.'/'.$folder.'/'.$innerFolder);
						
						// inside the company directory
						
						foreach($companyDirectory as $companyFileFolder){
							
							if($companyFileFolder != '.' && $companyFileFolder != '..'){
							
echo $companyFileFolder.'<br />';									
								// if this is a file, check if it exists and upload if not
								// if folder, create the folder and check for more files to create
								if(is_dir($dir.'/'.$folder.'/'.$innerFolder.'/'.$companyFileFolder)){
									
									// is a folder
									// create a folder
									// scan the folder and upload the files
									$companyInnerFolderObj = $bento->getWhere(['objectType'=>'document',
										'queryObj'=>array(
											'name'=>$companyFileFolder,
											'tagged_with'=>[1636580, 1763496, intval($company['id'])]
										),
										'getChildObjs'=>[
											'name'=>true
										]
										]);
										
									if(!$companyInnerFolderObj){
										
										// folder does not exist
										// create the folder
										$newFolder = array(
											'objectType'=>'document',
											'objectData'=>array(
												'name'=>$companyFileFolder,
												'document_type'=>'folder',
												'category'=>0,
												'is_public'=>1,
												'tagged_with'=>[1636580, 1763496, intval($company['id'])]
											));
																				
										$companyInnerFolderObj = $bento->create($newFolder);
										
									}
// var_dump($companyInnerFolderObj);
// die();									
									$innerCompanyDirectory = scandir($dir.'/'.$folder.'/'.$innerFolder.'/'.$companyFileFolder);
									foreach($innerCompanyDirectory as $innerCompanyFileFolder){

										if(!is_dir($dir.'/'.$folder.'/'.$innerFolder.'/'.$companyFileFolder.'/'.$innerCompanyFileFolder)){
											
											// is a file
											$files = $bento->getWhere(['objectType'=>'document',
												'queryObj'=>array(
													'name'=>$innerCompanyFileFolder,
													'tagged_with'=>[1636580, 1763496, intval($company['id'])],
													'parent'=>$companyInnerFolderObj[0]['id']
												),
												'getChildObjs'=>[
													'name'=>true
												]
												]);
												
//var_dump($companyInnerFolderObj);
											
											if(!$files[0]){
												
												$newFile = [];
												$newFile['name'] = $innerCompanyFileFolder;
												$newFile['filePath'] = $dir.'/'.$folder.'/'.$innerFolder.'/'.$companyFileFolder.'/'.$innerCompanyFileFolder;
												$newFile['parent'] = intval($companyInnerFolderObj[0]['id']);
												$newFile['tagged_with'] = [intval($company['id']), 1636580, 1763496];
												$newFile['is_public'] = 1;
// echo 'Comany Inner Folder<br />';
// var_dump($companyInnerFolderObj);
// echo '<br /><br />';												
												$newObj = $bento->createFile($newFile);
												
var_dump($newObj);
//die();
												
											}
											
										}
										
									}
									
								}else{
									
									// is a file
									$files = $bento->getWhere(['objectType'=>'document',
										'queryObj'=>array(
											'search'=>[
												'fields'=>['name'],
												'value'=>$companyFileFolder,
												'type'=>'or'
											]
										),
										'getChildObjs'=>[
											'name'=>true
										]
										]);
										
//var_dump($files);
									
									if(!$files[0]){
										
										$newFile = [];
										$newFile['name'] = $companyFileFolder;
										$newFile['filePath'] = $dir.'/'.$folder.'/'.$innerFolder.'/'.$companyFileFolder;
										//$newFile['parent'] = intval($company['id']);
										$newFile['tagged_with'] = [intval($company['id']), 1636580, 1763496];
										$newFile['is_public'] = 1;
										
										$newObj = $bento->createFile($newFile);
										
// var_dump($newObj);
// die();
										
									}
									
								}
							
							}
								
						}
						
					}
					
	//var_dump($companies);
					
				}
				
			}
			
		}
		
	}
	
}

die();

$objects = $bento->getWhere(['objectType'=>'document',
'queryObj'=>array(
	'search'=>[
		'fields'=>['name'],
		'value'=>'irs',
		'type'=>'or'
	]
),
'getChildObjs'=>[
	'name'=>true
]
]);
var_dump($objects);

echo 'done';
die();


// Read the files into arrays.
$contacts = fopen('contacts.csv', 'r');
$contactEmails = fopen('contactEmails.csv', 'r');
$contactNumbers = fopen('contactNumbers.csv', 'r');

$organizations = fopen('organizations.csv', 'r');
$organizationAddresses = fopen('organizationAddresses.csv', 'r');
$organizationSystems = fopen('organization_system.csv', 'r');

$charityTypesCSV = fopen('chrityTypes.csv', 'r');
$charityTypes = [];
$publicCharityTypesCSV = fopen('publicCharity.csv', 'r');
$publicCharityTypes = [];

// Loop over contacts to start building the contact objects
$contactsToCreate = [];
$companiesToCreate = [];
$emailsToCreate = [];
$count = 0;

while(($charityType = fgetcsv($charityTypesCSV)) !== false){
	
	$charityTypes[$charityType[0]] = $charityType[1];
	
}

while(($publicCharityType = fgetcsv($publicCharityTypesCSV)) !== false){
	
	$publicCharityTypes[$publicCharityType[0]] = $publicCharityType[1];
	
}

while(($org = fgetcsv($organizations)) !== false){
	
	$coreDemographic = [
		'objectType'=>'#ml7laG',
		'objectData'=>[]
	];
	
	$company = $bento->getWhere(['objectType'=>'companies',
		'queryObj'=>array(
			'data_source'=>intval($org[0])
		),
		'getChildObjs'=>false
	])[0];
	
	if(count($company) > 0){
		
		$cLevelString = 'Unknown';
		if($org[8]){
			$cLevelString = $org[8];
		}
		
		$ppLookup = 1;
		switch($org[7]){
			
			case '455':
				$ppLookup = 4;
				break;
				
			case '137':
				$ppLookup = 2;
				break;
			
			case '138':
				$ppLookup = 3;
				break;
			
		}
		
		$accountNotes = '';
		$accountNotes = $publicCharityTypes[$org[23]];
		$accountNotes .= $charityTypes[$org[11]];

		$coreDemographic['objectData']['name'] = $company['name'];
		$coreDemographic['objectData']['tagged_with'] = [1636580, 1763496, $company['id']];
		$coreDemographic['objectData']['parent'] = $company['id'];
		$coreDemographic['objectData']['_1'] = $org[12];
		$coreDemographic['objectData']['_2'] = $org[18];
		$coreDemographic['objectData']['_3'] = $cLevelString;
		$coreDemographic['objectData']['_4'] = $ppLookup;
		$coreDemographic['objectData']['_21'] = $org[9];
		$coreDemographic['objectData']['_22'] = (intval($org[10]) * 100000);
		$coreDemographic['objectData']['_23'] = [
			'state'=>$org[19],
			'country'=>'US'
		];
		$coreDemographic['objectData']['_24'] = $org[20];
		$coreDemographic['objectData']['_25'] = $org[21];
		$coreDemographic['objectData']['_26'] = $org[22];
		$coreDemographic['objectData']['_31'] = $company['name'];
		$coreDemographic['objectData']['_38'] = $accountNotes;
		
		echo '<pre>';
		print_r($coreDemographic);
		echo '</pre>';
		
		$alreadyCreated = $bento->getWhere(['objectType'=>'#ml7laG',
			'queryObj'=>array(
				'parent'=>$company['id']
			),
			'getChildObjs'=>false
		])[0];
		
		if($alreadyCreated){
			
			$coreDemographic['objectData']['id'] = $alreadyCreated['id'];
			
			$created = $bento->update($coreDemographic);
			
		}else{
			
			$created = $bento->create($coreDemographic);
			
		}
		
		$count++;
		
		if($count > 50){
			//die();
		}
				
	}
	
}

// while(($company = fgetcsv($organizations)) !== false){
// 		
// 	$companiesToCreate[] = array(
// 		'objectType'=>'companies',
// 		'objectData'=>array(
// 			'name'=>$company[2],
// 			'type'=>1652262,
// 			'tagged_with'=>[1636580, 1763496],
// 			'date_created'=>$company[16],
// 			'data_source'=>$company[0], // contact ID
// 			'data_source_id'=>$company[1] // client ID
// 		)
// 	);
// 	
// }

//die();

// var_dump($companiesToCreate);
// die();

// foreach($companiesToCreate as $company){
// 	
// // 	if($company['objectData']['data_source']){
// // //echo intval($company['objectData']['data_source']);		
// // 		$objectsAlreadyThere = $bento->getWhere(['objectType'=>'companies',
// // 			'queryObj'=>array(
// // 				'data_source'=>intval($company['objectData']['data_source'])
// // 			),
// // 			'getChildObjs'=>false
// // 		]);
// // //echo ' - '.count($objectsAlreadyThere) .' <br /><br />';	
// // 		foreach($objectsAlreadyThere as $toDelete){
// // 			
// // 			$bento->archive('companies', $toDelete['id']);
// // 			
// // 		}
// // 		
// // 		// $count++;
// // 		// 
// // 		// if($count > 20){
// // 		// 	die();
// // 		// }
// // 		
// // 	}
// 	
// 	$bento->create($company);
// 	
// }
// 
// echo 'done';
// die();

while(($contact = fgetcsv($contacts)) !== false){
	
	//print_r($line);
var_dump(intval($contact[1]));	
	$company = $bento->getWhere(['objectType'=>'companies',
			'queryObj'=>array(
				'data_source'=>intval($contact[1])
			),
			'getChildObjs'=>false
		])[0];
//var_dump($company);	
	if($company){
		
		// $contactsToCreate[] = [
		// 	'objectType'=>'contacts',
		// 	'objectData'=>[
		// 		'fname'=>$contact[3],
		// 		'lname'=>$contact[5],
		// 		'type'=>1652260,
		// 		'company'=>$company['id'],
		// 		'parent'=>$company['id'],
		// 		'data_source'=>$contact[0], // contact ID
		// 		'data_source_id'=>$contact[1] // client ID
		// 	]
		// ];
		
		$bento->create([
			'objectType'=>'contacts',
			'objectData'=>[
				'fname'=>$contact[3],
				'lname'=>$contact[5],
				'type'=>1652260,
				'tagged_with'=>[1636580, 1763496, $company['id']],
				'company'=>$company['id'],
				'parent'=>$company['id'],
				'date_created'=>$contact[9],
				'data_source'=>$contact[0], // contact ID
				'data_source_id'=>$contact[1] // client ID
			]
		]);
			
	}
	
	
	// $count++;
	// 
	// 
	// if($count > 5){
	// 	echo 'done';
	// 	die;
	// }
	
}


echo 'done';
die();


$page = 0;
$objects = $bento->getWhere(array(
		'objectType'=>'requests',
		'queryObj'=>array(
			'status'=>'Shipped',
			'adults'=>array(
				'type'=>'greater_than',
				'value'=>300
			),
			'date_created'=>array(
				'type'=>'between',
				'start'=>strtotime('2019-05-08'),
				'end'=>strtotime('2020-01-01')
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'contact'=>true,
			'company'=>true,
			'adults'=>true
		)
	));
	

/*
echo 'Page: '.$page.'<br />';
//echo count($objects['data']).'<br />';
print_r($objects);
*/

//$page++;

//echo 'Page: '.$page.'<br />';
//echo 'test';
die;

/*
$page = 0;
$object = array(
	'data'=>array(
		'item'=>'one'
	)
);
*/
//echo $objects['recordsTotal'];

/*
echo 'test';
die();
*/

while(
	
// 	$page < 2
	
	count($objects['data']) >= 1
	&& $objects['recordsTotal'] > (1 * $page)
	
	){
	
	$objects = $bento->getWhere(array(
		'objectType'=>'requests',
		'queryObj'=>array(
			'status'=>'Shipped',
			'adults'=>array(
				'type'=>'greater_than',
				'value'=>300
			),
			'date_created'=>array(
				'type'=>'between',
				'start'=>strtotime('2019-05-08'),
				'end'=>strtotime('2020-01-01')
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'contact'=>true,
			'company'=>true,
			'adults'=>true
		)
	));

	echo '<br />Page: '.$page.'<br />';
	echo count($objects['data']).'<br />';
	print_r($objects);
	
	$page++;
	
	$tasks = array();
	
	foreach($objects['data'] as $req){
//echo 'contactID '.$req['contact']['id'].'<br />';	

		if($req['adults'] < 500){
			
			$cellPhone = $bento->getWhere(array(
				'objectType'=>'contact_info',
				'queryObj'=>array(
					'type'=>49,
					'object_id'=>$req['contact']['id'],
					'is_primary'=>'yes'
				)
			));
	//var_dump($cellPhone);
			$churchPhone = $bento->getWhere(array(
				'objectType'=>'contact_info',
				'queryObj'=>array(
					'type'=>60,
					'object_id'=>$req['contact']['id']
				)
			));
			
			$createdAlreadyCheck = $bento->getWhere(array(
				'objectType'=>'#Follow_Up',
				'queryObj'=>array(
					'_3'=>$req['contact']['id']
				)
			));
var_dump($createdAlreadyCheck);			
			if(count($createdAlreadyCheck) >= 1){
				
				
			}else{
				
				$tasks[] = array(
					'objectType'=>'#Follow_Up',
					'objectData'=>array(
						'name'=>'Call '. $req['contact']['fname'].' '.$req['contact']['lname'],
						'_1'=>1,
						'_2'=>$req['contact']['manager'],
						//'_2'=>11,
						'_3'=>$req['contact']['id'],
						'_6'=>$cellPhone[0]['info'],
						'_9'=>$churchPhone[0]['info'],
						'tagged_with'=>array(1431795, $req['contact']['manager'])
					)
				);
				
			}
			
		}
		
	}
	
	var_dump($tasks);
	
	foreach($tasks as $task){
		
		$bento->create($task);
		
	}
		
}
	
?>