<?php
//error_reporting(E_ERROR);
//ini_set('display_errors', '1');
class MergeService
{

    function __construct($sb)
    {
        $this->sb = $sb;
    }

    // PRIVATE


    // PUBLIC

    public function test($request)
    {

        // #1712: This is where the connection to the merge service
        // should be established.
        $client = new \GuzzleHttp\Client();
        $response = $client->request('GET', 'merge:8084');
        $data = json_decode($response->getBody()->getContents());

        return $this->sb->sendData(['data' => $data], 1);

    }

    public function api($request)
    {

        $json_data = key($request);
        $decoded = json_decode($json_data, true);

        $data = [
            'name' => $decoded["name"],
            'job' => $decoded["job"]
          ];

        return $this->sb->sendData(['data' => $data], 1);

        //return $this->sb->sendData(['data' => $request], 1);

    }

    public function sendForm($request)
    {
        $client = new \GuzzleHttp\Client();
        $response = $client->request('POST', 'merge:8084/request', [
            'form_params' => [
                'name' => $request["name"],
                'job' => $request["job"]
            ]
        ]);
        $data = json_decode($response->getBody()->getContents());

        return $this->sb->sendData(['data' => $data], 1);

    }

}

?>