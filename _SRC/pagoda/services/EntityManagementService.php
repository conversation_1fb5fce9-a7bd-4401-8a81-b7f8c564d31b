<?php

class EntityManagementService
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response = new stdClass();
    }

    public function createEntityWithName($request)
    {
        $queryRequest = json_decode(json_encode($request), true);
        $queryObj = $queryRequest["queryObj"];

        $objectType = $queryRequest["objectType"];
        $name = $queryObj["name"];
        $parent = $queryObj["parent"];
        $taggedWith = $queryObj["tagged_with"];

        if(!(array_key_exists($_COOKIE['uid'], $taggedWith))){
            $taggedWith[] = intval($_COOKIE['uid']);
        }

        $newRecord = Array(
            "object_bp_type" => $objectType,
            "tagged_with" => $taggedWith,
            "parent" => $parent
        );

        $entitySteps = $this->sb->pgObjects->runSteps(
            intval($_COOKIE['uid'])
            , [
                'createEntity' => [
                    'objectType' => $objectType
                    , 'template' => false
                    , 'createTemplate' => false
                    , 'seed' => $newRecord

                ]
            ]
            , true
        );

        $newEntity = $entitySteps['memo'][0];

        $recordSet = Array(
            "id" => $newEntity["id"],
            "name" => $name
        );

        $updatedObject = $this->sb->updateObject($recordSet, $objectType, 0, false);

        $response = new stdClass();
        $response->{'record'} = $updatedObject;

        return $this->sb->sendData($response, 1);

    }

}

?>
