<?php

// <PERSON>N<PERSON><PERSON> TESTING DEPLOYMENT CACHE CHANGE
class StripeService
{

    private $stripeSecretKey = null;
    private $currentEnv = null;

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->stripeSecretKey = getenv('STRIPE_SK');
        $this->currentEnv = getenv('CURRENT_ENV');
    }

    // PRIVATE
    private function createACHVerificationHash($contact, $stripe_customer_id, $stripe_bank_account_id, $stripe_bank_account_status, $verificationEmail)
    {

        $salt = base64_encode(openssl_random_pseudo_bytes(PBKDF2_SALT_BYTE_SIZE));

        $verificationHash = base64_encode($this->sb->pbkdf2(
            PBKDF2_HASH_ALGORITHM,
            $stripe_bank_account_id,
            $salt,
            PBKDF2_ITERATIONS,
            PBKDF2_HASH_BYTE_SIZE,
            true
        ));

        $this->sb->pgObjects->create('bank_account_verification', array(
            'contact' => $contact,
            'stripe_customer_id' => $stripe_customer_id,
            'stripe_bank_account_id' => $stripe_bank_account_id,
            'stripe_verification_hash' => $verificationHash,
            'stripe_bank_account_status' => $stripe_bank_account_status,
            'verification_email' => $verificationEmail
        ));
    }

    private function sendACHVerificationEmail($bankAcct, $emailAddress, $acctVerificationObj, $newAcct = false, $verifiedAcct = false)
    {

        if ($newAcct) {

            $mergevars = array(
                'TITLE' => '',
                'SUBJECT' => 'Stripe Account Verification Instructions for Account Ending in ' . $bankAcct['last4'],
                'BODY' => "Hello " . $bankAcct['account_holder_name'] . "," .
                    "<br><br>" .
                    "You recently initiated the Stripe account verification process for your " . $bankAcct['bank_name'] . " account ending in " . $bankAcct['last4'] . "." .
                    "<br><br>" .
                    "Within 2-3 business days you should see two separate small deposits (less than $1) that you will use to verify your account." .
                    "<br><br>" .
                    "When you have successfully identified these deposits you will be able to continue your account verification at:" .
                    "<br>" .
                    "<a href='https://bento.infinityhospitality.net/app/ach_verification#?&i=" . $this->sb->appConfig['instance'] . "&hid=" . $acctVerificationObj['stripe_verification_hash'] . "'>bento.infinityhospitality.net/app/ach_verification#?&i=" . $this->sb->appConfig['instance'] . "&hid=" . $acctVerificationObj['stripe_verification_hash'] . "</a>" .
                    "<br><br>" .
                    "If you have any questions please reach out to contact our <a href='mailto: <EMAIL>?subject=Account Verification Assistance (" . $bankAcct['account_holder_name'] . ")'>Support Team</a>." .
                    "<br><br>" .
                    "Thank you," .
                    "<br>" .
                    "Bento Systems",
                'INSTANCE_NAME' => 'Bento Systems'
            );
        }

        if ($verifiedAcct) {

            $mergevars = array(
                'TITLE' => '',
                'SUBJECT' => 'Stripe Account Verification Successful for Account Ending in ' . $bankAcct['last4'],
                'BODY' => "Hello " . $bankAcct['account_holder_name'] . "," .
                    "<br><br>" .
                    "You have successfully completed the Stripe account verification process for your " . $bankAcct['bank_name'] . " account ending in " . $bankAcct['last4'] . "." .
                    "<br><br>" .
                    "In order to use your new account to initiate a payment on an invoice, please follow these steps:" .
                    "<br><br>" .
                    "{STEPS GO HERE}" .
                    "<br><br>" .
                    "If you have any questions please reach out to contact our <a href='mailto: <EMAIL>?subject=Account Verification Assistance (" . $bankAcct['account_holder_name'] . ")'>Support Team</a>." .
                    "<br><br>" .
                    "Thank you," .
                    "<br>" .
                    "Bento Systems",
                'INSTANCE_NAME' => 'Bento Systems'
            );
        }

        $this->sb->sendEmail($emailAddress, '<EMAIL>', $mergevars['SUBJECT'], $mergevars, 'New Verification Email', false);
    }

    private function logGoSquaredTransaction($transactionID, $amount)
    {

        require_once APP_ROOT . '/vendor/gosquared/php-sdk/main.php';

        $opts = array(
            'site_token' => 'GSN-752757-B',
            'api_key' => '2QZ2TS8N853DO629'
        );
        $GS = new GoSquared($opts);

        $opts = array(
            'revenue' => ($amount / 100),
            'quantity' => 1,
            'referrer' => $this->instance
        );

        $transaction = $GS->Transaction($transactionID, $opts);
        $response = $transaction->track();
        $person = $GS->Person($this->instance . '-' + $transactionID);
        $transaction = $person->Transaction($transactionID);
        $response = $transaction->track();
    }


    private function notifyFinanceManager($payments, $stripePI) {
        // Define manager emails by instance
        $managerEmailsByInstance = [
            'infinity' => [
                "<EMAIL>",
                '<EMAIL>'
            ],
            'dreamcatering' => [
                "<EMAIL>",
                '<EMAIL>',
                '<EMAIL>'
            ],
            'rickyvoltz' => [
                '<EMAIL>',
                '<EMAIL>'
            ]
        ];

        // Get card details from the charge data
        $charge = $stripePI->charges->data[0];
        $cardDetails = $charge->payment_method_details->card;

        foreach($payments as $index => $payment) {
            // Get real related objects
            $proposal = $this->sb->pgObjects->getById('proposals', $payment["main_object"]);
            $invoice = $this->sb->pgObjects->getById('invoices', $payment["invoice"]);
            $group = $this->sb->pgObjects->getById('groups', $proposal["main_object"], 1);

            $instanceName = $group['instance'];
            $managerEmails = $managerEmailsByInstance[$instanceName] ?? [];

            if (empty($managerEmails)) continue;

            $paymentAmount = number_format(floatVal($payment["amount"])/100, 2);
            $paymentFees = number_format(floatVal($payment["fee"])/100, 2);
            $totalPayment = number_format(floatVal($payment["fee"])/100 + floatVal($payment["amount"])/100, 2);
            $stripePaymentAmount = number_format(floatVal($stripePI->amount)/100, 2);

            $date = date('m/d/y, h:i A');
            $startDate = empty($group['start_date']) ? 'Not set' : date_create($group['start_date'])->format('m/d/y, h:i A');

            //production
            $bentoUrl = "https://bento.infinityhospitality.net/app/{$group['instance']}#hq&1=hqt-projectTool-Projects&2=o-project-{$group["id"]}-" . urlencode($group["name"]) . "&3=pt-projectInvoiceTool-Invoice";
            $stripeUrl = "https://dashboard.stripe.com/payments/{$payment['stripeId']}";

            //local testing
            // $bentoUrl = "http://localhost:8080/app/{$group['instance']}#hq&1=hqt-projectTool-Projects&2=o-project-{$group["id"]}-" . urlencode($group["name"]) . "&3=pt-projectInvoiceTool-Invoice";
            // $stripeUrl = "https://dashboard.stripe.com/test/payments/{$payment['stripeId']}";

            // Add formatted card info
            $cardInfo = sprintf(
                "%s %s ending in %s (expires %d/%d)",
                ucfirst($cardDetails->funding),
                ucfirst($cardDetails->brand),
                $cardDetails->last4,
                $cardDetails->exp_month,
                $cardDetails->exp_year
            );

            $eventDisplay = "#{$group['object_uid']} {$group['name']}";

            $paymentTimestamp = date('M j, Y, h:i:s A', $stripePI->created);

            $mergevars = array(
                'TITLE' => 'Stripe Payment',
                'SUBJECT' => "Stripe Payment Processed",
                'BODY' => "
                    <div style='font-family: Arial, sans-serif; font-size: 13px;'>

                        <strong>PAYMENT DETAILS:</strong><br/>
                        <hr style='margin: 5px 0; width: 500px;'><br/>
                        <table style='width: 500px; font-size: 13px;'>
                            <tr>
                                <td>Payment Schedule:</td>
                                <td style='text-align: right;'>#{$payment['main_object']}-{$invoice['id']} : {$invoice['name']}</td>
                            </tr>
                            <tr>
                                <td>Amount:</td>
                                <td style='text-align: right;'>\${$paymentAmount}</td>
                            </tr>
                            <tr>
                                <td>Fees:</td>
                                <td style='text-align: right;'>\${$paymentFees}</td>
                            </tr>
                            <tr>
                                <td><strong>Total:</strong></td>
                                <td style='text-align: right;'><strong>\${$totalPayment}</strong></td>
                            </tr>
                            <tr>
                                <td>View in Bento:</td>
                                <td style='text-align: right;'><a target='_blank' rel='noopener noreferrer' href='{$bentoUrl}'>View Event</a></td>
                            </tr>
                        </table><br/>

                        <strong>STRIPE TRANSACTION:</strong><br/>
                        <hr style='margin: 5px 0; width: 500px;'><br/>
                        <table style='width: 500px; font-size: 13px;'>
                            <tr>
                                <td>Payment Intent ID:</td>
                                <td style='text-align: right;'>{$stripePI->id}</td>
                            </tr>
                            <tr>
                                <td>Transaction ID:</td>
                                <td style='text-align: right;'>{$charge->id}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount Processed:</strong></td>
                                <td style='text-align: right;'><strong>\${$stripePaymentAmount}</strong></td>
                            </tr>
                            <tr>
                                <td>Payment Succeeded:</td>
                                <td style='text-align: right;'>{$paymentTimestamp}</td>
                            </tr>
                            <tr>
                                <td>View in Stripe:</td>
                                <td style='text-align: right;'><a target='_blank' rel='noopener noreferrer' href='{$stripeUrl}'>View Transaction</a></td>
                            </tr>
                        </table><br/>

                        <strong>CUSTOMER DETAILS:</strong><br/>
                        <hr style='margin: 5px 0; width: 500px;'><br/>
                        <table style='width: 500px; font-size: 13px;'>
                            <tr>
                                <td>Main Contact:</td>
                                <td style='text-align: right;'>{$group['main_contact']['name']}</td>
                            </tr>
                            <tr>
                                <td>Payment Method:</td>
                                <td style='text-align: right;'>{$cardInfo}</td>
                            </tr>
                            <tr>
                                <td>Receipt:</td>
                                <td style='text-align: right;'><a href='{$charge->receipt_url}'>View Receipt</a></td>
                            </tr>
                            <tr>
                                <td>Event:</td>
                                <td style='text-align: right;'>{$eventDisplay}</td>
                            </tr>
                            <tr>
                                <td>Event Date:</td>
                                <td style='text-align: right;'>{$startDate}</td>
                            </tr>
                        </table><br/>
                    </div>",
                'INSTANCE_NAME' => $group['instance']
            );

            $this->sb->sendEmail(
                $managerEmails,
                '<EMAIL>',
                $mergevars['SUBJECT'],
                $mergevars,
                'Stripe Payment',
                false
            );
        }
    }

    // PUBLIC
    public function initiateACHMicroDepositVerification($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $customer = $this->sb->pgObjects->getById('contacts', $request->contactId);
        $customerCompany = $this->sb->pgObjects->getById('companies', $customer['company']);

        if ($request->instanceId) {
            $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);
        } else {
            $instance = $this->sb->pgObjects->where('instances', array('systemName' => $request->instanceName))[0];
        }

        $btokToken = $request->btokToken;
        $acctRouting = $request->acctRouting;
        $acctLast4 = $request->acctLast4;
        $acctName = $request->acctName;
        $verificationEmail = $request->verificationEmail;
        $acctExists = false;
        $existingAcctId = '';
        $returnObj = new stdClass();
        $returnObj->bankAcct = '';
        $returnObj->verificationEmail = $verificationEmail;
        $returnObj->acctVerificationObj = '';
        $stripeCustomerObj = null;

        try {
            if (empty($customer['stripe_id'])) {

                if ($this->currentEnv == "production") {

                    $stripeCustomerObject = \Stripe\Customer::create([
                        'name' => $customer['name'],
                        'description' => $acctName,
                        'email' => $verificationEmail,
                        'metadata' => ['bentoContactId' => $customer['id']]
                    ], [
                        "stripe_account" => $instance['stripe_account_id']
                    ]);
                } else {

                    $stripeCustomerObject = \Stripe\Customer::create([
                        'name' => $customer['name'],
                        'description' => $acctName,
                        'email' => $verificationEmail,
                        'metadata' => ['bentoContactId' => $customer['id']]
                    ], []);
                }

                $this->sb->pgObjects->update('contacts', array(
                    'id' => $customer['id'],
                    'stripe_id' => $stripeCustomerObject['id']
                ));

                if ($this->currentEnv == "production") {

                    $returnObj->bankAcct = \Stripe\Customer::createSource($stripeCustomerObject['id'], ['source' => $btokToken], [
                        "stripe_account" => $instance['stripe_account_id']
                    ]);

                    $returnObj->bankAcct = \Stripe\Customer::updateSOurce($stripeCustomerObject['id'], $returnObj->bankAcct["id"], ["account_holder_name" => $customerCompany["name"]], [
                        "stripe_account" => $instance['stripe_account_id']
                    ]);
                } else {

                    $returnObj->bankAcct = \Stripe\Customer::createSource($stripeCustomerObject['id'], ['source' => $btokToken], []);

                    $returnObj->bankAcct = \Stripe\Customer::updateSOurce($stripeCustomerObject['id'], $returnObj->bankAcct["id"], ["account_holder_name" => $customerCompany["name"]], []);
                }

                $this->createACHVerificationHash($customer['id'], $stripeCustomerObject['id'], $returnObj->bankAcct['id'], $returnObj->bankAcct['status'], $verificationEmail);

                $returnObj->acctVerificationObj = $this->sb->pgObjects->where('bank_account_verification', array('stripe_bank_account_id' => $returnObj->bankAcct['id']))[0];
            } else {

                if ($this->currentEnv == "production") {

                    $stripeCustomerObject = \Stripe\Customer::update($customer['stripe_id'], ['metadata' => ['bentoContactId' => $customer['id']]], [
                        "stripe_account" => $instance['stripe_account_id']
                    ]);

                    $sources = \Stripe\Customer::allSources($customer['stripe_id'], ['object' => 'bank_account'], ["stripe_account" => $instance['stripe_account_id']])['data'];
                } else {

                    $stripeCustomerObject = \Stripe\Customer::update($customer['stripe_id'], ['metadata' => ['bentoContactId' => $customer['id']]], []);

                    $sources = \Stripe\Customer::allSources($customer['stripe_id'], ['object' => 'bank_account'], [])['data'];
                }

                foreach ($sources as $s) {

                    if ($s->routing_number == $acctRouting && $s->last4 == $acctLast4) {

                        $acctExists = true;
                        $existingAcctId = $s->id;
                    }
                }

                if ($acctExists) {

                    if ($this->currentEnv == "production") {

                        $returnObj->bankAcct = \Stripe\Customer::retrieveSource($customer['stripe_id'], $existingAcctId, [
                            "stripe_account" => $instance['stripe_account_id']
                        ]);

                        $returnObj->bankAcct = \Stripe\Customer::updateSOurce($stripeCustomerObject['id'], $returnObj->bankAcct["id"], ["account_holder_name" => $customerCompany["name"]], [
                            "stripe_account" => $instance['stripe_account_id']
                        ]);
                    } else {

                        $returnObj->bankAcct = \Stripe\Customer::retrieveSource($customer['stripe_id'], $existingAcctId, []);

                        $returnObj->bankAcct = \Stripe\Customer::updateSOurce($stripeCustomerObject['id'], $returnObj->bankAcct["id"], ["account_holder_name" => $customerCompany["name"]], []);
                    }

                    $returnObj->acctVerificationObj = $this->sb->pgObjects->where('bank_account_verification', array('stripe_bank_account_id' => $returnObj->bankAcct['id']))[0];

                    $this->sb->pgObjects->update('bank_account_verification', array(
                        'id' => $returnObj->acctVerificationObj['id'],
                        'stripe_bank_account_id' => $returnObj->bankAcct['id'],
                        'verification_email' => $verificationEmail
                    ));

                    $returnObj->acctVerificationObj = $this->sb->pgObjects->where('bank_account_verification', array('id' => $returnObj->acctVerificationObj['id']))[0];
                } else {

                    if ($this->currentEnv == "production") {

                        $returnObj->bankAcct = \Stripe\Customer::createSource($customer['stripe_id'], ['source' => $btokToken], [
                            "stripe_account" => $instance['stripe_account_id']
                        ]);

                        $returnObj->bankAcct = \Stripe\Customer::updateSource($stripeCustomerObject['id'], $returnObj->bankAcct["id"], ["account_holder_name" => $customerCompany["name"]], [
                            "stripe_account" => $instance['stripe_account_id']
                        ]);
                    } else {

                        $returnObj->bankAcct = \Stripe\Customer::createSource($customer['stripe_id'], ['source' => $btokToken], []);

                        $returnObj->bankAcct = \Stripe\Customer::updateSource($stripeCustomerObject['id'], $returnObj->bankAcct["id"], ["account_holder_name" => $customerCompany["name"]], []);
                    }

                    $this->createACHVerificationHash($customer['id'], $customer['stripe_id'], $returnObj->bankAcct['id'], $returnObj->bankAcct['status'], $verificationEmail);

                    $returnObj->acctVerificationObj = $this->sb->pgObjects->where('bank_account_verification', array('stripe_bank_account_id' => $returnObj->bankAcct['id']))[0];
                }
            }

            if ($returnObj->bankAcct['status'] == 'new') {

                $returnObj->acctVerificationObj = $this->sb->pgObjects->where('bank_account_verification', array('stripe_bank_account_id' => $returnObj->bankAcct['id']))[0];

                $this->sendACHVerificationEmail($returnObj->bankAcct, $verificationEmail, $returnObj->acctVerificationObj, true, false);
            }

            return $this->sb->sendData($returnObj, 1);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            return $this->sb->sendData($e->getJsonBody(), 1);
        }
    }

    public function getACHVerificationObj($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $hash = $request->hash;
        $returnObj = new stdClass();
        $returnObj->acctVerificationObj = '';
        $returnObj->acctContact = '';
        $returnObj->bankAcct = '';
        $instance = $this->sb->pgObjects->where('instances', array('instance' => $request->instanceName))[0];

        $returnObj->acctVerificationObj = $this->sb->pgObjects->where('bank_account_verification', array('stripe_verification_hash' => $hash))[0];
        $returnObj->acctContact = $this->sb->pgObjects->where('contacts', array('id' => $returnObj->acctVerificationObj['contact']))[0];

        if ($this->currentEnv == "production") {

            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $returnObj->acctVerificationObj['stripe_customer_id'] . '/sources/' . $returnObj->acctVerificationObj['stripe_bank_account_id'],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'Stripe-Account: ' . $instance['stripe_account_id'],
                    'Authorization: Bearer ' . $this->stripeSecretKey
                ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $returnObj->bankAcct = json_decode($response);
        } else {

            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $returnObj->acctVerificationObj['stripe_customer_id'] . '/sources/' . $returnObj->acctVerificationObj['stripe_bank_account_id'],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer ' . $this->stripeSecretKey
                ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $returnObj->bankAcct = json_decode($response);
        }


        return $this->sb->sendData($returnObj, 1);
    }

    public function verifyACHAccountWithStripe($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $returnObj = new stdClass();
        $customerId = $request->customerId;
        $acctId = $request->acctId;
        $microDeposit1 = $request->microDeposit1;
        $microDeposit2 = $request->microDeposit2;
        $verificationEmail = $request->email;
        $acctVerificationobjId = $request->acctVerificationObjId;
        $instance = $this->sb->pgObjects->where('instances', array('instance' => $request->instanceName))[0];
        $responseObj = null;

        try {

            if ($this->currentEnv == "production") {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $customerId  . '/sources/' . $acctId . '/verify',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => 'amounts%5B%5D=' . $microDeposit1 . '&amounts%5B%5D=' . $microDeposit2,
                    CURLOPT_HTTPHEADER => array(
                        'Stripe-Account: ' . $instance['stripe_account_id'],
                        'Authorization: Bearer ' . $this->stripeSecretKey,
                        'Content-Type: application/x-www-form-urlencoded'
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $responseObj = json_decode($response);
            } else {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $customerId  . '/sources/' . $acctId . '/verify',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => 'amounts%5B%5D=' . $microDeposit1 . '&amounts%5B%5D=' . $microDeposit2,
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Bearer ' . $this->stripeSecretKey,
                        'Content-Type: application/x-www-form-urlencoded'
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $responseObj = json_decode($response);
            }


            if ($responseObj->error) {

                $returnObj->error = $responseObj;
                $returnObj->microDeposit1 = $microDeposit1;
                $returnObj->microDeposit2 = $microDeposit2;
                return $this->sb->sendData($returnObj, 1);
            } else {

                $returnObj->bankAcct = $responseObj;
            }

            $bentoVerificationObj = $this->sb->pgObjects->getById('bank_account_verification', $acctVerificationobjId);
            $this->sb->pgObjects->update('bank_account_verification', array(
                'id' => $bentoVerificationObj['id'],
                'verification_email' => $verificationEmail,
                'stripe_bank_account_status' => 'verified'
            ));

            $this->sendACHVerificationEmail($returnObj->bankAcct, $verificationEmail, $bentoVerificationObj, false, true);

            return $this->sb->sendData($returnObj, 1);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            $returnObj->error = $e->getJsonBody();
            $returnObj->microDeposit1 = $microDeposit1;
            $returnObj->microDeposit2 = $microDeposit2;

            return $this->sb->sendData($returnObj, 1);
        }
    }

    public function getStripeCustomer($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $stripeId = $request->stripeId;
        $contactId = $request->contactId;
        $contactObj = $this->sb->pgObjects->getById('contacts', $request->contactId);
        if ($stripeId != $contactObj["stripe_id"]) {
            $stripeId = $contactObj["stripe_id"];
        }
        $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);
        $returnObj = new stdClass();
        $verifiedOnly = $request->verifiedSources;
        $stripeCustomerObj = null;

        try {

            if ($contactObj["stripe_id"] == null) {

                if ($this->currentEnv == "production") {

                    $stripeCustomerObject = \Stripe\Customer::create([
                        'name' => $contactObj['name'],
                        'metadata' => ['bentoContactId' => $contactId]
                    ], ['stripe_account' => $instance['stripe_account_id']]);
                } else {

                    $stripeCustomerObject = \Stripe\Customer::create([
                        'name' => $contactObj['name'],
                        'metadata' => ['bentoContactId' => $contactId]
                    ], []);
                }

                $contactObj = $this->sb->pgObjects->update('contacts', array(
                    'id' => $contactId,
                    'stripe_id' => $stripeCustomerObject->id
                ));

                $stripeId = $stripeCustomerObject->id;
            }

            if ($this->currentEnv == "production") {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $stripeId,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'Stripe-Account: ' . $instance['stripe_account_id'],
                        'Authorization: Bearer ' . $this->stripeSecretKey
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $stripeCustomerObj = json_decode($response);

                $sources = \Stripe\Customer::allSources($stripeId, [], ['stripe_account' => $instance['stripe_account_id']])["data"];
            } else {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $stripeId,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Bearer ' . $this->stripeSecretKey
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $stripeCustomerObj = json_decode($response);

                $sources = \Stripe\Customer::allSources($stripeId, [], [])["data"];
            }

            $stripeCustomerObj->sources = $sources;

            if ($verifiedOnly && $stripeCustomerObj->sources != null) {

                $stripeCustomerObj->sources = array_filter($stripeCustomerObj->sources, function ($source) {

                    if ($source['object'] == 'bank_account') {

                        return $source['status'] == 'verified';
                    } else {

                        return $source;
                    }
                });
            }

            $returnObj->customer = $stripeCustomerObj;
            return $this->sb->sendData($returnObj, 1);
        } catch (\Stripe\Exception\InvalidRequestException $e) {

            if ($this->currentEnv == "production") {

                $newStripeCustomerObject = \Stripe\Customer::create([
                    'name' => $contactObj['name'],
                    'metadata' => ['bentoContactId' => $contactId]
                ], ['stripe_account' => $instance['stripe_account_id']]);
            } else {

                $newStripeCustomerObject = \Stripe\Customer::create([
                    'name' => $contactObj['name'],
                    'metadata' => ['bentoContactId' => $contactId]
                ], []);
            }

            $stripeCustomerObj = $newStripeCustomerObject;

            $this->sb->pgObjects->update('contacts', array(
                'id' => intval($contactId),
                'stripe_id' => $stripeCustomerObj->id
            ));

            $stripeId = $newStripeCustomerObject->id;

            $returnObj->customer = $stripeCustomerObj;
            return $this->sb->sendData($returnObj, 1);
        } catch (Exception $e) {
            return $this->sb->sendData($e, 0);
        }
    }

    public function saveCreditCardToStripeCustomer($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $ctokToken = $request->ctokToken;
        $stripeCustomerId = $request->stripeCustomerId;
        $cardLast4 = $request->cardLast4;
        $cardExpYear = $request->cardExpYear;
        $cardBrand = $request->cardBrand;
        $cardZip = $request->cardZip;
        $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);
        $returnObj = new stdClass();
        $cards = null;

        try {

            if ($this->currentEnv == "production") {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $stripeCustomerId . '/sources',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'Stripe-Account: ' . $instance['stripe_account_id'],
                        'Authorization: Bearer ' . $this->stripeSecretKey
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $cards = json_decode($response)->data;
            } else {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $stripeCustomerId . '/sources',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Bearer ' . $this->stripeSecretKey
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $cards = json_decode($response)->data;
            }

            foreach ($cards as $card) {

                if ($card->object == "card" && $card->last4 == $cardLast4  && $card->exp_year == $cardExpYear && $card->brand == $cardBrand && $card->address_zip == $cardZip) {

                    $returnObj->cardObj = $card;
                    return $this->sb->sendData($returnObj, 1);
                }
            }

            if ($this->currentEnv == "production") {

                $returnObj->cardObj = \Stripe\Customer::createSource($stripeCustomerId, ['source' => $ctokToken], ['stripe_account' => $instance['stripe_account_id']]);
            } else {

                $returnObj->cardObj = \Stripe\Customer::createSource($stripeCustomerId, ['source' => $ctokToken], []);
            }



            return $this->sb->sendData($returnObj, 1);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            return $this->sb->sendData($e->getJsonBody(), 1);
        }
    }

    public function deletePaymentSource($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $sourceId = $request->sourceId;
        $stripeId = $request->stripeId;
        $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);
        $returnObj = new stdClass();

        try {

            if ($this->currentEnv == "production") {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $stripeId . '/sources/' . $sourceId,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'DELETE',
                    CURLOPT_HTTPHEADER => array(
                        'Stripe-Account: ' . $instance['stripe_account_id'],
                        'Authorization: Bearer ' . $this->stripeSecretKey
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $returnObj->response = json_decode($response);
            } else {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $stripeId . '/sources/' . $sourceId,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'DELETE',
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Bearer ' . $this->stripeSecretKey
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $returnObj->response = json_decode($response);
            }

            return $this->sb->sendData($returnObj, 1);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            return $this->sb->sendData($e->getJsonBody(), 1);
        }
    }

    public function updateDefaultSource($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $sourceId = $request->sourceId;
        $stripeId = $request->stripeId;
        $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);
        $returnObj = new stdClass();

        try {

            if ($this->currentEnv == "production") {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $stripeId,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => 'default_source=' . $sourceId,
                    CURLOPT_HTTPHEADER => array(
                        'Stripe-Account: ' . $instance['stripe_account_id'],
                        'Authorization: Bearer ' . $this->stripeSecretKey,
                        'Content-Type: application/x-www-form-urlencoded'
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $returnObj->response = json_decode($response);
            } else {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.stripe.com/v1/customers/' . $stripeId,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => 'default_source=' . $sourceId,
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Bearer ' . $this->stripeSecretKey,
                        'Content-Type: application/x-www-form-urlencoded'
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $returnObj->response = json_decode($response);
            }

            return $this->sb->sendData($returnObj, 1);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            return $this->sb->sendData($e->getJsonBody(), 1);
        }
    }

    public function chargeStripeConnectCustomer($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $stripeId = $request->stripeId;
        $sourceId = $request->sourceId;
        $sourceType = ($request->sourceType == 'bank_account') ? 'ach_debit' : $request->sourceType;
        $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);
        $eventId = $request->eventId;
        $selectedInvoices = $request->selectedInvoices;
        $proposal = $this->sb->pgObjects->getById('proposals', $eventId);
        $project = $this->sb->pgObjects->getById('groups', $proposal['main_object'], 1);
        $allInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);
        $projectType = $project['type'];
        $percentFee = (floatval($request->percentFee) / 100) + 1;
        $flatFee = floatval($request->flatFee) * 100;
        $returnObj = new stdClass();
        $paymentTotal = $request->paymentTotal;
        $feesTotal = $request->fees;
        $invoiceValuePaid = $paymentTotal - $feesTotal;
        $paymentOwnerId = is_null($_COOKIE['uid']) ? $project['main_contact'] : $_COOKIE['uid'];
        $optionalEmail = $request->optionalEmail;

        try {

            // identify if first payment for possible state transition
            $hasAPaidInvoice = false;
            foreach ($allInvoices as $i => $inv) {

                if (
                    $inv['amount'] > 0
                    && $inv['balance'] < $inv['amount']
                ) {
                    $hasAPaidInvoice = true;
                }
            }

            foreach ($selectedInvoices as $inv) {

                if ($invoiceValuePaid == 0) {
                    break;
                }

                $amount = 0;
                if ($inv->amount >= $invoiceValuePaid) {

                    // paying up to the amount of the current invoice
                    $amount = $invoiceValuePaid;
                    $invoiceValuePaid = 0;
                } else {

                    // paying over the amount of the current invoice
                    $amount = $inv->amount;
                    $invoiceValuePaid = $invoiceValuePaid - $amount;
                }
                $chargeTotal = round($amount * $percentFee + $flatFee);
                $transactionFee = $chargeTotal - $amount;

                if ($this->currentEnv == "production") {

                    $returnObj->paymentIntent = \Stripe\PaymentIntent::create(
                        [
                            "amount" => $chargeTotal,
                            "currency" => "usd",
                            "customer" => $stripeId,
                            "source" => $sourceId,
                            'payment_method_types' => [$sourceType],
                            "confirm" => true,
                        ],
                        [
                            "stripe_account" => $instance['stripe_account_id']
                        ]
                    );
                } else {

                    $returnObj->paymentIntent = \Stripe\PaymentIntent::create(
                        [
                            "amount" => $chargeTotal,
                            "currency" => "usd",
                            "customer" => $stripeId,
                            "source" => $sourceId,
                            'payment_method_types' => [$sourceType],
                            "confirm" => true,
                        ],
                        []
                    );
                }

                $testPayment = true;
                if ($this->currentEnv == "production") {
                    $testPayment = false;
                }

                $paymentObject = $this->sb->pgObjects->create('payments', array(
                    'main_object' => $proposal['id'],
                    'amount' => $amount,
                    'fee' => $transactionFee,
                    'stripeId' => $returnObj->paymentIntent->id,
                    'invoice' => $inv->id,
                    'test_payment' => $testPayment,
                    'manual_payment' => false,
                    'owner' => $paymentOwnerId,
                    'optional_receipt_email' => $optionalEmail
                ));

                $invoiceObject = $this->sb->pgObjects->getById('invoices', $inv->id);
                $paymentsArray = $invoiceObject["payments"] == null ? [] : $invoiceObject["payments"];
                array_push($paymentsArray, $paymentObject["id"]);
                $invoiceObject["payments"] = $paymentsArray;
                $invoiceObject["balance"] -= $amount;
                $invoiceObject["paid"] += $amount;

                $this->sb->pgObjects->update('invoices', $invoiceObject);

                $this->logGoSquaredTransaction($returnObj->paymentIntent->id, $amount);
            }

            // transition state if necessary
            if (is_int($projectType['onFirstFullPayment'])) {

                if (!$hasAPaidInvoice) {

                    $transitionResponse = $this->sb->updateState(
                        $project['id'],
                        null,
                        $projectType['onFirstFullPayment'],
                        $this->sb->getUrl() . '/app/' . $project['instance'] . '#mystuff&1=o-project-' . $project['id'] . '-' . rawurlencode($project['name']),
                        '',
                        false,
                        0,
                        function ($response) use ($paymentObject) {

                            $returnObj = new stdClass();
                            $returnObj->success = true;
                            return $returnObj;
                        }
                    );
                }
            }

            return $this->sb->sendData($returnObj, 1);
        } catch (Exception $e) {
            return $this->sb->sendData($e, 0);
        }
    }

    public function chargeStripeConnectCustomer2($request)
    {

        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $returnObj = new stdClass();

        $stripeId = $request->stripeId;
        $sourceId = $request->sourceId;
        $sourceType = ($request->sourceType == 'bank_account') ? 'ach_debit' : $request->sourceType;
        $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);
        $eventId = $request->eventId;

        $paymentTotal = $request->paymentTotal;
        $feesTotal = $request->fees;

        $invoiceValuePaid = $paymentTotal - $feesTotal;

        $payment = $invoiceValuePaid;
        $percentFee = (floatval($request->percentFee) / 100) + 1;
        $flatFee = floatval($request->flatFee) * 100;

        $chargeTotal = round($payment*$percentFee + $flatFee);

        try{

            $returnObj->paymentIntent = \Stripe\PaymentIntent::create(
                [
                    "amount" => $chargeTotal,
                    "currency" => "usd",
                    "customer" => $stripeId,
                    "source" => $sourceId,
                    'payment_method_types' => [$sourceType],
                    "confirm" => true,
                ],
                [
                    "stripe_account" => $instance['stripe_account_id']
                    /// RPX Stripe Acct for Dev Testing
                    // "stripe_account" => "acct_1DnVhRGZFySInPwG"
                ]
            );

        }

        catch (Exception $e) {
            print_r($e);
            exit(1);
        };

        ob_start();

        echo " start :><br>";
        echo "<pre> stripe response « \n ", var_dump( json_encode($returnObj , JSON_PRETTY_PRINT) ), "</pre>\n";
        echo " <: end <br>";

        $stripeResp = ob_get_clean();

        ob_end_flush();

        function httpPost($url, $data)
        {
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($curl);
            curl_close($curl);
            return $response;
        }

        $payload = array(
            "data"=> $stripeResp
        );

        httpPost("https://eozo9b7j1hc6o5w.m.pipedream.net", $payload);

        if ( $returnObj->paymentIntent ) {

            $selectedInvoices = $this->sb->pgObjects->getById('invoices', __::pluck($request->selectedInvoices, 'id') , 1);

            usort($selectedInvoices, function($a, $b) {
                return strtotime($a["due_date"]) - strtotime($b["due_date"]);
            });

            ///log
            foreach ($selectedInvoices as $inv) {

                $selected = array(
                    'id'=> $inv['id']
                    , 'name'=> $inv['name']
                    , 'amount'=> $inv['amount']
                    , 'balance'=> $inv['balance']
                    , 'payments' => $inv['payments']
                    , 'due_date' => $inv['due_date']
                );

            }

            $proposal = $this->sb->pgObjects->getById('proposals', $eventId);
            $project = $this->sb->pgObjects->getById('groups', $proposal['main_object'], 1);

            // identify if first payment for possible state transition
            $allInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);
            $hasAPaidInvoice = false;
            foreach ($allInvoices as $i => $inv) {

                if (
                    $inv['amount'] > 0
                    && $inv['balance'] < $inv['amount']
                ) {
                    $hasAPaidInvoice = true;
                }
            }
            if (is_int($projectType['onFirstFullPayment'])) {

                if (!$hasAPaidInvoice) {

                    $transitionResponse = $this->sb->updateState(
                        $project['id'],
                        null,
                        $projectType['onFirstFullPayment'],
                        $this->sb->getUrl() . '/app/' . $project['instance'] . '#mystuff&1=o-project-' . $project['id'] . '-' . rawurlencode($project['name']),
                        '',
                        false,
                        0,
                        function ($response) use ($paymentObject) {

                            // $returnObj = new stdClass();
                            // $returnObj->success = true;
                            // return $returnObj;

                        }
                    );
                }
            }

            /// get ALL other unpaid invoices [including $selectedInvoices since they have not been reconciled just yet]
            $unpaidRemaining = $this->sb->pgObjects->where('invoices'
                , [
                    'related_object' => $proposal['id']
                    , 'balance' => [
                        'type' => 'greater_than',
                        'value' => 0
                    ],
            ]);
            ///sort remaining by closest due date
            usort($unpaidRemaining, function($a, $b) {
                return strtotime($a["due_date"]) - strtotime($b["due_date"]);
            });

            ///filter out the selectedinvoices from the $unpaidRemaining list
            $selectedIds = array_column($selectedInvoices, 'id');
            $unpaidRemaining = __::reject( $unpaidRemaining, function($invoice) use($selectedIds) {
                return in_array($invoice['id'], $selectedIds);
            });

            $projectType = $project['type'];

            $paymentOwnerId = is_null($_COOKIE['uid']) ? $project['main_contact'] : $_COOKIE['uid'];
            $optionalEmail = $request->optionalEmail;

            $logPaymentsArray = array();
            $logInvoicesArray = array();

            while ($invoiceValuePaid > 0):

                if(empty($selectedInvoices))
                    break;

                foreach($selectedInvoices as $i => &$invoice){

                    $currentInvoice = $invoice;

                    $lastInvoice = end($selectedInvoices);
                    $payment = 0;
                    $paymentObject = array();

                    if ( $invoiceValuePaid == 0 || $invoice['balance'] == 0 ) break 2;

                    if ($invoiceValuePaid < $invoice['balance']) {

                        ///create payment object
                        $payment = $invoiceValuePaid;
                        $chargeTotal = round($payment*$percentFee + $flatFee);
                        $transactionFee = $chargeTotal - $payment;

                        /// reconcile invoiceValuePaid amount
                        $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                        $paymentObj = array(
                            'main_object' => $proposal['id'],
                            'amount' => $payment,
                            'fee' => $transactionFee,
                            'stripeId' => $returnObj->paymentIntent->id,
                            'invoice' => $invoice["id"],
                            'test_payment' => $this->testKey,
                            'manual_payment' => false,
                            'owner' => $paymentOwnerId,
                            'optional_receipt_email' => $optionalEmail
                        );

                        // create payment object
                        $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                        $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                        array_push($paymentsArray, $paymentObject["id"]);

                        $invoice["payments"] = $paymentsArray;
                        $invoice["balance"] = $invoice['balance'] - $payment;
                        $invoice["paid"] += $payment;

                        $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                    } else {

                        /// reconcile invoiceValuePaid amount
                        $invoiceValuePaid = $invoiceValuePaid - $invoice['balance'];

                        ///create payment object
                        $payment = $invoice['balance'];

                        $invoice['balance'] = $invoice['balance'] - $payment;

                        if ( end($selectedInvoices)['id'] == $currentInvoice['id'] && $invoiceValuePaid > 0 && empty($unpaidRemaining) ) {

                            $payment = $invoiceValuePaid + $payment;

                            $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                        }

                        $chargeTotal = round($payment*$percentFee + $flatFee);
                        $transactionFee = $chargeTotal - $payment;

                        $paymentObj = array(
                            'main_object' => $proposal['id'],
                            'amount' => $payment,
                            'fee' => $transactionFee,
                            'stripeId' => $returnObj->paymentIntent->id,
                            'invoice' => $invoice["id"],
                            'test_payment' => $this->testKey,
                            'manual_payment' => false,
                            'owner' => $paymentOwnerId,
                            'optional_receipt_email' => $optionalEmail
                        );

                        // create payment object
                        $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                        $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                        array_push($paymentsArray, $paymentObject["id"]);

                        $invoice["payments"] = $paymentsArray;
                        $invoice['paid'] += $payment;

                        $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                    }

                    array_push($logPaymentsArray, $paymentObject);
                    array_push($logInvoicesArray, $updatedInvoice);

                    unset($payment);
                    unset($selectedInvoices[$i]);

                }

            endwhile;

            while ($invoiceValuePaid > 0):

                if(empty($unpaidRemaining))
                    break;

                foreach($unpaidRemaining as $i => &$invoice){

                    $currentInvoice = $invoice;

                    $lastInvoice = end($selectedInvoices);
                    $payment = 0;
                    $paymentObject = array();

                    if ( $invoiceValuePaid == 0 ) break 2;

                    if ($invoiceValuePaid < $invoice['balance']) {

                        ///create payment object
                        $payment = $invoiceValuePaid;
                        $chargeTotal = round($payment*$percentFee + $flatFee);
                        $transactionFee = $chargeTotal - $payment;

                        /// reconcile invoiceValuePaid amount
                        $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                        $paymentObj = array(
                            'main_object' => $proposal['id'],
                            'amount' => $payment,
                            'fee' => $transactionFee,
                            'stripeId' => $returnObj->paymentIntent->id,
                            'invoice' => $invoice["id"],
                            'test_payment' => $this->testKey,
                            'manual_payment' => false,
                            'owner' => $paymentOwnerId,
                            'optional_receipt_email' => $optionalEmail
                        );

                        // create payment object
                        $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                        $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                        array_push($paymentsArray, $paymentObject["id"]);

                        $invoice["payments"] = $paymentsArray;
                        $invoice["balance"] = $invoice['balance'] - $payment;
                        $invoice["paid"] += $payment;

                        $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                    } else {

                        /// reconcile invoiceValuePaid amount
                        $invoiceValuePaid = $invoiceValuePaid - $invoice['balance'];

                        ///create payment object
                        $payment = $invoice['balance'];

                        $invoice['balance'] = $invoice['balance'] - $payment;

                        if ( end($unpaidRemaining)['id'] == $currentInvoice['id'] && $invoiceValuePaid > 0 ) {

                            $payment = $invoiceValuePaid + $payment;

                            $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                        }

                        $chargeTotal = round($payment*$percentFee + $flatFee);
                        $transactionFee = $chargeTotal - $payment;

                        $paymentObj = array(
                            'main_object' => $proposal['id'],
                            'amount' => $payment,
                            'fee' => $transactionFee,
                            'stripeId' => $returnObj->paymentIntent->id,
                            'invoice' => $invoice["id"],
                            'test_payment' => $this->testKey,
                            'manual_payment' => false,
                            'owner' => $paymentOwnerId,
                            'optional_receipt_email' => $optionalEmail
                        );

                        // create payment object
                        $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                        $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                        array_push($paymentsArray, $paymentObject["id"]);

                        $invoice["payments"] = $paymentsArray;
                        $invoice['paid'] += $payment;

                        $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                    }

                    array_push($logPaymentsArray, $paymentObject);
                    array_push($logInvoicesArray, $updatedInvoice);

                    unset($payment);
                    unset($unpaidRemaining[$i]);

                }

            endwhile;

        }

        // Inside chargeStripeConnectCustomer2, just before the final return
        $updatedInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);

        $returnObj->success = true;
        $returnObj->invoices = $updatedInvoices;
        $returnObj->processedPaymentAmount = $paymentTotal;
        $returnObj->processedPayments = $logPaymentsArray;
        $returnObj->processedInvoices = $logInvoicesArray;

        // Call notification function
        $this->notifyFinanceManager($logPaymentsArray, $returnObj->paymentIntent);

        // IMPORTANT: Return the response - this was commented out
        return $this->sb->sendData($returnObj, 1);

    }
}
