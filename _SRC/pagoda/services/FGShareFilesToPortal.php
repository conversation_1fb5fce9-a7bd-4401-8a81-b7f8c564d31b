<?php

class FGShareFilesToPortal
{

    function __construct($sb)
    {
        $this->sb = $sb;
    }

// PUBLIC

    public function createDeliverable($request)
    {

        $deliverable_bp = '#D3yxcG';
        $deliverable_attachkey = '_2';
        $service_bp = '#HDIjlE';
        $service_deltable = '_14';

        $req = json_decode(json_encode($request), true);

        if ($req['instance'] == 'rickyvoltz') {
            $deliverable_bp = '#pyS4kB';
            $deliverable_attachkey = '_11';
            $service_bp = '#ZDns6v';
            $service_deltable = '_11';
        }

        $file = $this->sb->pgObjects->getById('document', $req['file']);

        $project = $this->sb->pgObjects->getById('groups', $file['parent'], 0);

        $client_service = $this->sb->getObjectsWhere(
            $service_bp
            , array(
                'parent' => $project['id']
            )
            , 0
            , 1
        )[0];

        $company = $this->sb->pgObjects->getById( "companies", $project['parent'], 0);

         $companyId = $company['id'];

            ///find contacts
            $contactList = $this->sb->pgObjects->where(
				'contacts'
                , [
					'company' => $companyId
				]
			);

            if ( !empty($contactList) ) {

                $sharedWith = array();

                ///grab ids of contact objs
                foreach ($contactList as $i => $contact) {
                    array_push($sharedWith, $contact['id']);
                }

            }

        $cs_shared_with = array_merge($client_service['shared_with'], $project['shared_with'], $sharedWith);
        $cs_shared_with = array_unique($cs_shared_with);

        $cs_tagged_with = array_merge($client_service['tagged_with'], $project['tagged_with']);
        array_push($cs_tagged_with, $client_service['id']);
        $cs_tagged_with = array_unique($cs_tagged_with);

        ///create deliverable
        $deliverable = $this->sb->pgObjects->create(
            $deliverable_bp
            , array(
                'name' => $req['name']
                , 'parent' => $client_service['id']
                , 'tagged_with' => $cs_tagged_with
                , 'shared_with' => $cs_shared_with
                , $deliverable_attachkey => $file['id']
            )
            , 0
            , 0
        );

        return $this->sb->sendData(
            [
                'deliverable' => $deliverable
            ]
            , 1
        );

    }

}

?>