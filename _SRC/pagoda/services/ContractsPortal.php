<?php


class ContractsPortal
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE


// PUBLIC

    public function initializeInstanceSetup($request)
    {

        $response = new stdClass();

        $queryRequest = json_decode(json_encode($request), true);

        $instance = null;
        $contract = null;
        
        $instance = $this->sb->pgObjects->where(
            'instances'
            , array(
                'instance' => $queryRequest['instance']
            )
            , ''
            , 0
            , true
            , 0
            , array()
            , 'desc'
            , 1
            , null
            , array()
            , 'date'
            , false
        )[0];

        $contract = $this->sb->getObjectsWhere(
            'contracts'
            , [
	            'id' => $queryRequest['contract']
            ]
            , 0
            , [
                'main_contact' => [
                    'contact_info' =>   true
                ]
                , 'notify' =>           true
                , 'name' =>             true
                , 'status' =>           true
                , 'signer_email' =>     true
                , 'signer_name' =>      true
                , 'html_string' =>      true
                , 'requires_approval'=> true
                , 'after_signature' =>  true
                , 'signatures' =>       true
                , 'related_object' =>   [
                    'main_contact' => [
                        'contact_info' => true
                    ]
                    , 'managers' => true
                    , 'sales_managers' => true
                ]
                , 'tagged_with' =>      true
            ]
        )[0];

        $contract_system = $this->sb->getAllObjects(
            'contract_system'
            , array(
                'signature_disclaimer' => true
            )
            , 0
        )[0];

        $response->{'instance'} = $instance;            
        $response->{'contract'} = $contract;            
        $response->{'system_settings'} = $contract_system;            

        return $this->sb->sendData($response, 1);

    }

    public function sendComms($request)
    {
        $response = new stdClass();

        $queryRequest = json_decode(json_encode($request), true);

        $instance = $this->sb->pgObjects->where(
            'instances'
            , array(
                'instance' => $queryRequest['instance']
            )
            , ''
            , 0
            , true
            , 0
            , array()
            , 'desc'
            , 1
            , null
            , array()
            , 'date'
            , false
        )[0];

        $url = $_SERVER['HTTP_ORIGIN'];
        if ($instance['instance'] == 'rickyvoltz'){
            $url = $_SERVER['HTTP_ORIGIN'];
        }

        $contract = $this->sb->getObjectsWhere(
            'contracts'
            , [
	            'id' => $queryRequest['contract']
            ]
            , 0
            , [
                'notify' =>                 true
                , 'name' =>                 true
                , 'signer_email' =>         true
                , 'signer_name' =>          true
                , 'requires_approval' =>    true
                , 'related_object' =>   [
                    'main_contact' => [
                        'contact_info' =>   true
                    ]
                    , 'managers' =>         true
                    , 'sales_managers' =>   true
                    , 'notify' =>           true
                ]
            ]
        )[0];

        $project = $contract['related_object'];

        $userIds = array();
        $userEmails = array();

        $tagged_with = array();
        array_push($tagged_with, $contract['id'], $project['id'], $project['main_contact']['id']);

        if ( !empty($contract['notify']) && $contract['requires_approval'] === true ){

            $userIds = array_merge($userIds, $contract['notify']);

            foreach ($contract['notify'] as $id) {

                $user = $this->sb->getObjectsWhere(
                    'users'
                    , [
                        'id' => $id
                    ]
                    , 0
                    , [
                        'email' =>      true
                    ]
                )[0];

                if ( $user['email'] ){
                    array_push($userEmails, $user['email']);
                }

            }

        }

        if ( !empty( $project['managers'] ) ) {

            foreach( $project['managers'] as $manager ){

                array_push($userIds, $manager['id']);
                array_push($userEmails, $manager['email']);

            }

        }

        $note = array();

        $noteTitle = 'The Electronic Signature Disclaimer for Contract '. $contract['name'] . ' has been Accepted';
        $noteBody = $contract['name'] . ' has been viewed by ' . $contract['signer_name'] . ' ('. $contract['signer_email'] . ') and the Electronic Signature Disclaimer has been accepted';

        $emailBody = array(
            'TITLE' => 'Contract is being viewed.'
            , 'BODY' => $noteBody . '<br /><br /><a href="' . $url . '/app/' . $instance['instance'] . '#mystuff&1=o-project-' . $project['id']. '-'. $project['name'].'&2=pt-contractTools&3=o-contracts-'. $contract['id'].'-'. $contract['name'].'">Click here to view this project</a>'
            , 'BUTTON' => 'View in Bento'
            , 'INSTANCE_NAME' => $instance['instance']
        );

        $sent = $this->sb->sendEmail(
            $userEmails
            , $instance['emailFrom']
            , $noteTitle
            , $emailBody
            , $tagged_with
            , false
        );
        $noteObj = array(
            'type_id' => $contract['id']
            , 'type' => 'contracts'
            , 'note' => $noteBody
            , 'record_type' => 'log'
            , 'author' => $project['main_contact']['id']
            , 'notifyUsers' => $userIds
            , 'tagged_with' => $tagged_with
        );

        $note = $this->sb->pgObjects->create(
                'notes'
                , $noteObj
        );

        $currentResponse = null;

        $currenResponse['email'] = (bool)$email;
        $currenResponse['note'] = (bool)$note;
        
        $response = $currentResponse;            

        return $this->sb->sendData($response, 1);

    }
  
}

?>