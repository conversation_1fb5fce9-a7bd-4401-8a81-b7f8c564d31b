<?php

class PaymentsService
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE

// PUBLIC

    public function filterTestPayments($request)
	{

		$queryRequest = json_decode(json_encode($request), true);
		$payments = $queryRequest['payments'];
		$filteredPayments = array();

		foreach($payments as $payment) {

			if(array_key_exists('test_payment', $payment)) {

				if (
					$payment['test_payment'] == false
					&& $payment['invoice'] != null
				) {

					array_push($filteredPayments, $payment);

				}

			} else {

				if ($payment['invoice'] != null) {

					array_push($filteredPayments, $payment);

				}

			}

		}

		return $this->sb->sendData($filteredPayments, 1);

	}

	public function getPaymentsForReports($request)
	{

		$queryRequest = json_decode(json_encode($request), true);
		$data = $queryRequest['data'];
		$paged = $data['paged'];
		$childObjs = $data['childObjs'];
		$dateRange = $data['date_created'];
		$payments = [];
		$where = [
			'paged' => $paged
            , 'test_payment'=>[
                "type" => 'not_equal'
                , "value" => true
            ]
			, 'status'=>[
                "type" => 'not_equal'
                , "value" => 'Returned'
            ]
        ];

        if ($dateRange != null) {
            $where["date_created"] = $dateRange;
        }

		// get payments objects:

		if ($dateRange == null) {

			$payments = $this->sb->getObjectsWhere('payments', $where, 0, $childObjs);

		} else {

			$payments = $this->sb->getObjectsWhere('payments', $where, 0, $childObjs);

		}


		foreach($payments["data"] as $index=>$payment){

			if (str_contains($payment["status"], "R")) {
				unset($payments["data"][$index]);
				continue;
			}

			// formatted date variables for report and csv download:
			$eventDateUTC = new DateTime($payment['main_object']['main_object']['start_date'], new DateTimeZone('UTC'));
			$eventDateCST = $eventDateUTC->setTimezone(new DateTimeZone('America/Chicago'))->format("Y/m/d");
			$paymentDateUTC = new DateTime($payment["date_created"], new DateTimeZone('UTC'));
			$paymentDateCST = $paymentDateUTC->setTimezone(new DateTimeZone('America/Chicago'))->format("Y/m/d");

			// update array with formatted dates:
			$payments["data"][$index]["main_object"]["main_object"]["start_date"] = $eventDateCST;
			$payments["data"][$index]["date_created"] = $paymentDateCST;

		}

		return $this->sb->sendData($payments, 1);

	}

	public function splitPayment($request)
	{
    // Decode the request data
    $data = json_decode(json_encode($request), true);

    $selectedPayment = $data['selectedPayment'];
    $newPayments = $data['newPayments'];
    $proposalId = $data['proposal'];

    // Initialize return object with detailed message
    $returnObj = [
      "success" => true,
      "message" => "",
      "selectedPayment" => null,
      "selectedInvoice" => null,
      "targetedPayments" => [],
      "targetedInvoices" => [],
      "operations" => []
    ];

		// Validate input data
		if (empty($selectedPayment) || empty($newPayments) || !is_array($newPayments)) {
			$returnObj["success"] = false;
			$returnObj["message"] = "Invalid input data";
			return $this->sb->sendData($returnObj, 1);
		}

		// Calculate total amount being allocated to new payments
		$totalAllocated = 0;
		foreach ($newPayments as $payment) {
			$totalAllocated += $payment['amount'];
		}

		// Validate that total allocated doesn't exceed original payment amount
		if ($totalAllocated > $selectedPayment['amount']) {
			$returnObj["success"] = false;
			$returnObj["message"] = "Total allocated amount exceeds the original payment amount";
			return $this->sb->sendData($returnObj, 1);
		}

		// [X] 1) Update the selected payment - reduce amount by the sum of new payment amounts
		$originalAmount = $selectedPayment['amount'];
		$selectedPayment['amount'] = $originalAmount - $totalAllocated;

		// Handle payment details - preserve existing details and add notes
		if (!isset($selectedPayment['details'])) {
			$selectedPayment['details'] = [];
		} else if (!is_array($selectedPayment['details'])) {
			// Convert to array if it's not already
			$selectedPayment['details'] = (array)$selectedPayment['details'];
		}

		// Add or update notes in details
		if (!isset($selectedPayment['details']['notes'])) {
			$selectedPayment['details']['notes'] = "";
		}

		// Add a note about the split with formatted date including AM/PM
		$formattedDate = date('m/d/y h:i A');

		// Get the target invoice IDs for better context
		$targetInvoiceIds = [];
		foreach ($newPayments as $newPayment) {
			if (isset($newPayment['invoice'])) {
				$targetInvoiceIds[] = '#' . $newPayment['invoice'];
			}
		}
		$targetInvoicesStr = implode(', ', $targetInvoiceIds);

		$splitNote = "* Split payment: $" . number_format($totalAllocated / 100, 2) . " transferred to " . $targetInvoicesStr . ". Remaining balance: $" . number_format(($originalAmount - $totalAllocated) / 100, 2) . ". " . $formattedDate . ".";

		if (!empty($selectedPayment['details']['notes'])) {
			$selectedPayment['details']['notes'] .= "\n\n" . $splitNote;
		} else {
			$selectedPayment['details']['notes'] = $splitNote;
		}

    // Get the original invoice ID first
		$originalInvoiceId = $selectedPayment['invoice'];
		$originalInvoice = null;

    // Update the selected payment
		$updatedSelectedPayment = $this->sb->pgObjects->update('payments', $selectedPayment);
		$returnObj['selectedPayment'] = $updatedSelectedPayment;
    $returnObj['operations']['selectedPayment'] = [
			"id" => $updatedSelectedPayment['id'],
			"originalAmount" => $originalAmount,
			"newAmount" => $updatedSelectedPayment['amount'],
			"allocated" => $totalAllocated,
			"invoice" => $originalInvoiceId
		];

		// Only update the original invoice if it exists and is not 0 (unlinked payment)
		if ($originalInvoiceId && $originalInvoiceId !== 0) {
			// Get the original invoice
			$originalInvoice = $this->sb->pgObjects->getById('invoices', $originalInvoiceId);

			if (!empty($originalInvoice)) {

				// [X] 2) Update the selected payment's invoice
				// Reduce the paid amount on the original invoice
				$originalInvoice['paid'] -= $totalAllocated;
				$originalInvoice['balance'] = $originalInvoice['amount'] - $originalInvoice['paid'];

				// Update the original invoice
				$updatedOriginalInvoice = $this->sb->pgObjects->update('invoices', $originalInvoice);

				$returnObj['selectedInvoice'][] = $updatedOriginalInvoice;
				$returnObj['operations']['selectedInvoice'][] = [
					"id" => $updatedOriginalInvoice['id'],
					"action" => "Updated original invoice - reduced paid amount by $" . number_format($totalAllocated / 100, 2)
				];
			}
		}

    foreach ( $newPayments as $newPayment ){

      // Copy the details from the selected payment
      if (!isset($newPayment['details']) || !is_array($newPayment['details'])) {
        $newPayment['details'] = [];
      }

      // Create a more user-friendly note for the new payment
      $newNote = "* Payment created: This payment was created from splitting payment #" . $selectedPayment['id'] . ". Original payment amount: $" . number_format($originalAmount / 100, 2) . ". " . $formattedDate . ".";

      // Append the note to any existing notes
      if (!empty($newPayment['details']['notes'])) {
        $newPayment['details']['notes'] .= "\n\n" . $newNote;
      } else {
        $newPayment['details']['notes'] = $newNote;
      }

      // [X] 3) Create the new payment records
			$createdPayment = $this->sb->pgObjects->create('payments', $newPayment);

			$returnObj['targetedPayments'][] = $createdPayment;
			$returnObj['operations']['targetedPayments'][] = [
				"id" => $createdPayment['id'],
				"amount" => $createdPayment['amount'],
				"invoice" => $createdPayment['invoice']
			];

      $targetInvoice = $this->sb->pgObjects->where('invoices', ['id' => $newPayment['invoice']])[0];
			// [X] 4) Update the target invoice

			// Add the payment to the invoice
			$targetInvoice['payments'][] = $createdPayment['id'];
			$targetInvoice['paid'] += $newPayment['amount'];
			$targetInvoice['balance'] = $targetInvoice['amount'] - $targetInvoice['paid'];
			$updatedTargetInvoice = $this->sb->pgObjects->update('invoices', $targetInvoice);
      $returnObj['targetedInvoices'][] = $updatedTargetInvoice;
			$returnObj['operations']['targetedInvoices'][] = [
				"id" => $updatedTargetInvoice['id'],
				"action" => "Updated target invoice - increased paid amount by $" . number_format($createdPayment['amount'] / 100, 2)
			];
      // Build the detailed message for the response
      $formattedDate = date('m/d/y h:i:s A');
      $returnObj['message'] = "Payment #" . $selectedPayment['id'] . " was split successfully on " . $formattedDate . ". ";
      $returnObj['message'] .= "Original amount: $" . number_format($originalAmount / 100, 2) . ". ";
      $returnObj['message'] .= "New amount: $" . number_format($selectedPayment['amount'] / 100, 2) . ". ";
      $returnObj['message'] .= "Allocated: $" . number_format($totalAllocated / 100, 2) . " to " . count($newPayments) . " invoice(s).";

    }

    $allUpdatedInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposalId]);

    $returnObj['invoices'] = $allUpdatedInvoices;

		return $this->sb->sendData($returnObj, 1);
}

	public function getPaymentsForPaymentsReportAccountsReceivable($request)
	{

		$queryRequest = json_decode(json_encode($request), true);
		$data = $queryRequest['data'];
		$paged = $data["paged"];
        $sortCol = $paged["sortCol"];
        $sortDir = $paged["sortDir"];
        $pageLength = $paged['pageLength'];
        $page = $paged['page'];
        $paymentIndexStart = $page;
		$childObjs = [
			"stripeId" => true
			, "stripe_payment_id" => true
			, "icg_payment_id" => true
			, "owner" => true
			, "main_client" => true
			, "amount" => true
			, "details" => true
			, "object_uid" => true
			, "fee" => true
			, 'invoice' => [
				"related_object" => [
					"name" => true
					, "main_object" => [
						"locations" => true,
						"name" => true,
						"main_contact" => [
							"fname" => true,
							"lname" => true,
							"company" => [
								"name" => true
							]
						],
						"object_uid" => true
					]
				]
				, "name" => true
				, "owner" => true
				, "id" => true
			]
			, "test_payment"=> true
			, "status" => true
		];

		$dateRange = $data['date_created'];
		$payments = [];
        $where = [
            'test_payment'=>[
                "type" => 'not_equal'
                , "value" => true
            ]
			, 'status'=>[
                "type" => 'not_equal'
                , "value" => 'Returned'
            ]
        ];
        $returnObj = [
            "draw" => true
            , "recordsFiltered" => $pageLength
            , "recordsTotal" => 0
            , "data" => []
        ];

        if ($dateRange != null) {
            $where["date_created"] = $dateRange;
        }

        $payments = $this->sb->getObjectsWhere('payments', $where, 0, $childObjs);
        $returnObj["recordsTotal"] = count($payments);

		// create payment type field:
        foreach ($payments as $index=>$payment) {

			if (str_contains($payment["status"], "R")) {
				unset($payments[$index]);
				continue;
			}

			$paymentDateUTC = new DateTime($payment['date_created'], new DateTimeZone('UTC'));
			$payments[$index]["pay_day"] = strtotime($paymentDateUTC->format("Y/m/d"));

            if ($payment["icg_payment_id"] != "") {

				$payments[$index]["type"] = "iCheckGateway";

			} else if ($payment["stripeId"] != "") {

				$payments[$index]["type"] = "Stripe";

			} else {

				$payments[$index]["type"] = "Manual";

			}
        }

        // sort payments
        if ($sortCol === "date_created") {

            if ($sortDir === "asc") {

				array_multisort(array_column($payments, 'pay_day'),  SORT_ASC, array_column($payments, 'type'), SORT_ASC, $payments);

            } else {

				array_multisort(array_column($payments, 'pay_day'),  SORT_DESC, array_column($payments, 'type'), SORT_ASC, $payments);

            }

        } else if ($sortCol === "amount") {

            if ($sortDir === "asc") {

                usort($payments,function($a, $b){

                    return $a["amount"] > $b["amount"];
                });

            } else {

                usort($payments,function($a, $b){

                    return $a["amount"] < $b["amount"];
                });

            }

        }

        // replace array:
        $returnObj["data"] = array_slice($payments, $paymentIndexStart, $pageLength);

		return $this->sb->sendData($returnObj, 1);

	}

}

?>
