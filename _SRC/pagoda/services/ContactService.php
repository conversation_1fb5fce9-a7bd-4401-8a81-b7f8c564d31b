<?php

//use <PERSON><PERSON>\Async\Pool;

class ContactService
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE

// PUBLIC

    public function setLeadSource($request)
    {
	    // Specific to Infinity - Dream Catering - NLP
        $contacts = $this->sb->getObjectsWhere('contacts', [
	        'lead_source' => ''
        ], 0, [
	        'contact_info' => true
	        , 'lead_source' => true
        ]);
        
        $contactsToUpdate = array();
        
        if (!empty($contacts)) {
		 	
		 	foreach($contacts as $contact) {
			 	
			 	if (!empty($contact['contact_info'])) {
				 	
				 	foreach($contact['contact_info'] as $cInfo) {
					 	
					 	if (
							//infinity contact info type
							$cInfo['type'] == '1828172'
							||
							//dream catering contact info type
			                $cInfo['type'] == '7692701' 
							||
							//nlp contact info type
							$cInfo['type'] == '6265824' 
			                && !empty($cInfo['info'])
			            ) {
				            
				            array_push($contactsToUpdate, [
					           'id'               => $contact['id']
					           , 'object_bp_type' => 'contacts'
					           , 'lead_source'    => $cInfo['info']
				            ]);
				            
			            }
					 	
				 	}
				 	
			 	}
			 	
		 	}
			
			if (empty($contactsToUpdate)) {
				
				return true;
				
			} else {
				
				$this->sb->pgObjects->update('contacts', $contactsToUpdate);

				return true;	
				
			}   
	        
        } else {

	        return true;
	        
        }

    }

}

?>