<?php

class InvoicesService
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE

// PUBLIC

	public function getInvoicesForInvoicesReport($request)
	{
        $development = false;

		$queryRequest = json_decode(json_encode($request), true);
		$data = $queryRequest['data'];
		$paged = $data["paged"];
        $sortCol = $paged["sortCol"];
        $sortDir = $paged["sortDir"];
        $pageLength = $paged['pageLength'];
        $page = $paged['page'];
        $invoiceIndexStart = $page;
		$childObjs = [
            "name"=> true
            , "sent" => true
            , "sent_by" => true
            , "sent_on" => true
            , "due_date"=> true
            , "amount"=> true
            , "balance"=> true
            , "payments"=> [
                "amount"=> true
            ]
            , "main_contact" => true
            , "related_object"=> [
                "name" => true
                , "main_object"=> [
                    "object_uid"=> true
                    , "start_date"=> true
                    , "end_date"=> true
                    , "name"=> true
                    , "state"=> true
                    , "main_contact"=> [
                        "id"=> true
                        , "fname"=> true
                        , "lname"=> true
                    ]
                    , "type"=> [
                        "name"=> true
                    ]
                ]
            ]
        ];
		$dateRange = $data['due_date'];
		$invoices = [];
        $where = [
            'balance'=>[
                "type" => "greater_than"
                , "value" => 0
            ]
        ];
        $filteredInvoices = [];
        $returnObj = [
            "draw" => true
            , "recordsFiltered" => $pageLength
            , "recordsTotal" => 0
            , "data" => []
        ];

        if ($dateRange != null) {
            $where["due_date"] = $dateRange;
        }

        $invoices = $this->sb->getObjectsWhere('invoices', $where, 0, $childObjs);

        // get only Event Workflow management in approved states
        foreach ($invoices as $invoice) {

            if ( $invoice['instance'] == 'rickyvoltz') {
                $development = true;
            }

            $workflow = $invoice["related_object"]["main_object"]["type"]["name"];

            $state = $invoice["related_object"]["main_object"]["state"];

            if($invoice["related_object"]["instance"] == "infinity"){
                $approvedStates = [1, 2, 3, 4, 9, 8]; //Signed Awaiting Payment, Booked, Assigned, Post Event Wrap Up, Finished, Paused Waiting for Payment state ids

                if (
                    $workflow === "Event Management" && in_array($state, $approvedStates)
                    || $development
                ) {

                    array_push($filteredInvoices, $invoice);

                }
            }

            if($invoice["related_object"]["instance"] == "dreamcatering"){
                $approvedStates = [4, 5, 6, 7, 8, 9]; //Ready to Assign, Booked, Event Setup, Setup Complete, Final Punchlist, Wrap Up -- state ids

                if (
                    $workflow === "Event Workflow" && in_array($state, $approvedStates)
                    || $development
                ) {

                    array_push($filteredInvoices, $invoice);

                }

            }

            if($invoice["related_object"]["instance"] == "nlp"){
                $approvedStates = [4, 5, 6]; //Active - Post Event Wrap Up - Completed ** state ids

                if (
                    $workflow === "NLP Client" && in_array($state, $approvedStates)
                    || $development
                ) {

                    array_push($filteredInvoices, $invoice);

                }
            }


        }

        // sort filteredInvoices
        if ($sortCol === "due_date") {

            if ($sortDir === "asc") {

                usort($filteredInvoices,function($a, $b){

                    return strtotime($a["due_date"]) - strtotime($b["due_date"]);
                });

            } else {

                usort($filteredInvoices,function($a, $b){

                    return strtotime($b["due_date"]) - strtotime($a["due_date"]);
                });

            }

        } else if ($sortCol === "amount") {

            if ($sortDir === "asc") {

                usort($filteredInvoices,function($a, $b){

                    return $a["amount"] > $b["amount"];
                });

            } else {

                usort($filteredInvoices,function($a, $b){

                    return $a["amount"] < $b["amount"];
                });

            }

        } else if ($sortCol === "balance") {

            if ($sortDir === "asc") {

                usort($filteredInvoices,function($a, $b){

                    return $a["balance"] > $b["balance"];
                });

            } else {

                usort($filteredInvoices,function($a, $b){

                    return $a["balance"] < $b["balance"];
                });

            }

        } else if ($sortCol === "sent"){

            if ($sortDir === "asc") {

                usort($filteredInvoices, function($first, $second)
                {
                    $first_timestamp = strtotime($first['sent_on']);
                    $secont_timestamp = strtotime($second['sent_on']);

                    return $first_timestamp < $secont_timestamp;

                });

            } else {

                usort($filteredInvoices, function($first, $second)
                {
                    $first_timestamp = strtotime($first['sent_on']);
                    $secont_timestamp = strtotime($second['sent_on']);

                    return $first_timestamp > $secont_timestamp;

                });

            }

        } else {

            if ($sortDir === "asc") {

                usort($filteredInvoices, function($a, $b)
                {
                    return strcmp($a["name"], $b["name"]);
                });

            } else {

                usort($filteredInvoices, function($a, $b)
                {
                    return strcmp($b["name"], $a["name"]);
                });

            }

        }
        // total filtered invoices records total
        $returnObj["recordsTotal"] = count($filteredInvoices);

        // replace array:
        $returnObj["data"] = array_slice($filteredInvoices, $invoiceIndexStart, $pageLength);

		return $this->sb->sendData($returnObj, 1);

	}

    public function sendSingleInvoices($request)
    {

        // variables
        $queryRequest = json_decode(json_encode($request), true);

        // $invoice = $this->sb->getObjectById('proposals', $queryRequest["invoice"], 1);
        $invoice = $this->sb->getObjectById('invoices', $queryRequest["invoice"], 0);

        $proposal = null;
        if (is_int($invoice["related_object"])) {

            $proposal = $this->sb->getObjectById('proposals', $invoice["related_object"], 0);

        } else {

            $proposal = $invoice["related_object"];
        }

        $project = $this->sb->getObjectById('groups', $proposal["main_object"], 0);
        $startDate = empty( $project['start_date'] ) ? 'Not set' : date_create($project['start_date'])->format('m/d/y, h:i A');

        $projectName = $proposal["name"];
        $invoiceNumber = $invoice["id"];
        $instanceName = $this->sb->appConfig["instance"];
        $instanceEmailFrom = $this->sb->appConfig["emailFrom"];
        $contact = null;
        $mainContactEmail = null;
        $DueDate = strtotime($invoice["due_date"]);
        $invoiceDueDate = date('F j, Y', $DueDate);

        if (is_int($project["main_contact"])) {

            $contact = $this->sb->getObjectById('contacts', $project["main_contact"], 0);
            $mainContactEmail = $this->sb->getContactInfo($project["main_contact"], 'email', true, false);

        } else {

            $contact = $project["main_contact"];
            $mainContactEmail = $this->sb->getContactInfo($project["main_contact"]["id"], 'email', true, false);

        }
        $mainContactName = $contact["fname"];

        $email_tagged_with = array_merge($project['tagged_with'], $invoice['tagged_with'], $proposal['tagged_with']);
        array_push($email_tagged_with, $contact['id'], $project['id'], $invoice['id'], $proposal['id']);
        $email_tagged_with = __::uniq($email_tagged_with);
        $mergevarsInstanceName = $instanceName;

        //#2366 - 800 | Add 'Send Email' button to NLP and Dream instances
        if($instanceName == 'infinity'){
            $subject_company =  'Infinity Hospitality';
            $email_company =  '<EMAIL>';
            $phone_company =  '(************* ext. 6';
        }
        if($instanceName == 'dreamcatering' || $instanceName == 'rickyvoltz'){
            $subject_company =  'Dream Catering';
            $email_company =  '<EMAIL>';
            $phone_company =  '************';

            ///In DreamCatering instance, this is the 'Daily Dish' Project Type
            if ($project['type'] == 12033227){

                $subject_company =  'Daily Dish';
                $email_company =  '<EMAIL>';
                $phone_company =  '************';

                $mergevarsInstanceName = 'Daily Dish';

            }

        }
        if($instanceName == 'nlp'){
            $subject_company =  'Nashville Lighting & Production';
            $email_company =  '<EMAIL>';
            $phone_company =  '(************* ext. 6';
        }

        // construct email
        $mergevars = array(
            'TITLE' => $subject_company.' Invoice Reminder' ,
            'SUBJECT' => $subject_company.' Invoice #' . $invoiceNumber . ' Due: ' . $invoiceDueDate .'',
            'BODY' => "Hi " . $mainContactName .", <br><br>
                You have an invoice due on " . $invoiceDueDate ." from ". $subject_company .". Please use the link below to make your next payment.<br/><br/>
                <a href='https://bento.infinityhospitality.net/app/invoices#?&i=" . $instanceName . "&iid=" . $invoice["id"] . "'>Payment Portal</a><br/><br/>
                <strong>EVENT DETAILS:</strong><br/>
                Event: ". $projectName . "<br/>
                Main Contact: ". $contact["fname"] . " " . $contact["lname"] . "<br/>
                Event Date: " . $startDate . "<br/><br/>
                <strong>INVOICE DETAILS:</strong><br/>
                Invoice: #". $invoiceNumber . "-" . $invoice['name'] . "<br/>
                Balance: $" . number_format(floatVal($invoice['balance'])/100, 2) . "<br/>
                Due Date: " . $invoiceDueDate . "<br/><br/>
                If you have any questions, please email <a href='mailto:" . $email_company ."'>" . $email_company ."</a> or call <a href='tel:" . $phone_company ."'>" . $phone_company ."</a><br><br><br>",
            'INSTANCE_NAME' => $mergevarsInstanceName
        );

        // send email
        return $this->sb->sendEmail(
            $mainContactEmail
            , $instanceEmailFrom
            , $mergevars['SUBJECT']
            , $mergevars
            , $email_tagged_with
            , false
        );

    }

}
