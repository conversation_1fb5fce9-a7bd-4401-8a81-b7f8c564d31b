<?php


class EntityDaily
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE

// PUBLIC

    public function getToday($request)
    {
        
        $response = new stdClass();

        $queryRequest = json_decode(json_encode($request), true);

        $options = array(
            'tagged_with' => [intval($_COOKIE['uid'])]
        );

        if (isset($queryRequest['box_view_settings']['options']) && !empty($queryRequest['box_view_settings']['options'] ) ){

            foreach ($queryRequest['box_view_settings']['options'] as $key => $value) {

                if($key == 'date'){
                    
                    $queryTimestamp = strtotime($queryRequest['date_selection']);

                    $beginOfDay = strtotime("today", $queryTimestamp);

                    $endOfDay   = strtotime("tomorrow", $beginOfDay) - 1;

                    $options[$value] = [
                        'type' =>               'between'
                        , 'start' =>            $beginOfDay
                        , 'end' =>              $endOfDay
                        , 'includeOffset' =>    false
                    ];

                }

                if ($key == 'user') {
                    $options[$value] = intval($_COOKIE['uid']);
                }
            }

        }

        $currentResponse = null;
        
        if (!is_null($queryRequest['box_view_settings']['options'])) {

            $currentResponse = $this->sb->getObjectsWhere(
                $queryRequest['blueprint']
                , $options
                , 0
                , 1
            )[0];

        }

        $response->{'record'} = $currentResponse;            

        return $this->sb->sendData($response, 1);

    }

    public function createDaily($request)
    {  

        // Read the request as an array (instead of an object)
        $request = json_decode(json_encode($request), true);

        if ( isset($request['settings']) && !empty($request['settings']) ) {

            $seedWithRecord = false;
            $newRecord = array();

            if ( isset($request['type']) && $request['type'] == 'previous' ){
                $seedWithRecord = true;
            }

            $user = $this->sb->pgObjects->getById(
                'users'
                , intval($_COOKIE['uid'])
                , [
                    'name' => true
                    , 'fname' => true
                    , 'lname' => true
                ]
            );

            $entity_type = $this->sb->pgObjects->where(
                'entity_type'
                , array(
                    'bp_name' => $request['settings']['tool']
                )
                , ''
                , 0
                , true
                , 0
                , $request['settings']['options']['date']
                , 'desc'
                , 1
                , null
                , array()
                , 'date'
                , false
            )[0];
            
            if ( $seedWithRecord ) {

                $where = array(
                    'tagged_with' => [intval($_COOKIE['uid'])]
                );
                
                if (isset($request['settings']['options'])){
    
                    if (array_key_exists('date', $request['settings']['options'])) {
                        $where[$request['settings']['options']['date']] = 
                            [
                                'type' =>       'before'
                                , 'date' =>     $request['selectedDate']
                            ];
                    }
    
                    if (array_key_exists('user', $request['settings']['options'])) {
                        $where[$request['settings']['options']['user']] = intval($_COOKIE['uid']);
                    }
    
                }

                // Get the previous day's record   
                $prevRecord = $this->sb->pgObjects->where(
                    '#'. $request['settings']['tool']
                    , $where
                    , ''
                    , 0
                    , true
                    , 0
                    , $request['settings']['options']['date']
                    , 'desc'
                    , 1
                    , null
                    , array()
                    , 'date'
                    , false
                )[0];

                $blueprint = $this->sb->pgObjects->getBlueprint($prevRecord['object_bp_type']);

                ///default to reset timer, and workflow fieldtypes
                foreach( $blueprint as $key => $val){

                    if ( $val['fieldType'] == 'timer' ) {

                        $newRecord[$val] = 0;
                        
                    } else if ( $val['fieldType'] == 'state' ) {

                        $entryPoint = 0;
                        $entryPointStatus = '';
                        $workflow = $this->sb->pgObjects->getById('entity_workflow', $val['workflow']);

                        foreach( $workflow['states'] as $i => $state){

                            if ( $state['isEntryPoint'] == 1) {

                                $entryPoint = $state['uid'];
                                $entryPointStatus = $state['type'];

                            }
                        }

                        $newRecord[$key] =              $entryPoint;
                        $newRecord['status'] =          'open';
                        $newRecord[$key . '_status'] =  $entryPoint;

                    }

                }

                ///boxView settings to select fields to carry over
                if (is_array($request['settings']['options']['fieldsToCarryOver'])) {

                    foreach($request['settings']['options']['fieldsToCarryOver'] as $i => $fieldKey) {

                        ///if carrying over a Workflow fieldType, set meta data - only applies to Workflow Field Types
                        if ( $blueprint[$fieldKey]['fieldType'] == 'state' ) {

                            $newRecord['status'] =              $prevRecord['status'];
                            $newRecord[$fieldKey . '_status'] = $prevRecord[$fieldKey . '_status'];
                            
                        }

                        ///set the carry over field based on key
                        $newRecord[$fieldKey] = $prevRecord[$fieldKey];

                    }

                }

            }
        
            // Set the date, tags, and user on the new record
            $newRecord['name'] = $entity_type['name']. ' | ' . $user['name'] .' - ' . date('D d M y', strtotime($request['selectedDate']) );
            $newRecord[$request['settings']['options']['date']] = $request['selectedDate'];
            $newRecord[$request['settings']['options']['user']] = intval($_COOKIE['uid']);
            $newRecord['parent'] = intval($_COOKIE['uid']);
            $newRecord['tagged_with'] = [intval($_COOKIE['uid'])];

            $mergeVarDate = new DateTime($request['selectedDate']);
            $mergeVarDate = $mergeVarDate->format('Y-m-d\TH:i:s\Z');

            $entitySteps = $this->sb->pgObjects->runSteps(
                    intval($_COOKIE['uid'])
                    , [
                        'createEntity' => [
                            'objectType' => '#'. $request['settings']['tool']
                            , 'template' => false
                            , 'createTemplate' => false
                            , 'seed' => $newRecord 
                            , 'options' => [
                                'passOnTags' => false
                                , 'additionalTags' => [intval($_COOKIE['uid'])]
                                , 'objectType_name' => $entity_type['name']
                            ]
                            , 'mergeVars' => [
                                'today' => $mergeVarDate
                            ]
                        ]
                    ]
                    , true
            );

            $createdRecord = $entitySteps['memo'][0];

            return $this->sb->sendData(
                [
                    'dailyRecord' => $createdRecord
                ]
                , 1
            );

        } else {

            return $this->sb->sendData(
                [
                    'error' => array(
                        'header'=> 'No Blueprint Available'
                        , 'body' => 'Add Field Types to this set using the Sets Tool'
                    )
                ]
                , 1
            );

        }    

    }
    
}

?>