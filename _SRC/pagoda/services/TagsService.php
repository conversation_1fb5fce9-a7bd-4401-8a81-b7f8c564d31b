<?php

//use <PERSON><PERSON>\Async\Pool;

class TagsService
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE

    private function getTags($ids) {

        try {

            $tags = $this->sb->getObjectsWhere('any', [
                'tagged_with' => [
                    'type' => 'any'
                    , 'values' => $ids
            ]], 0, 0);

            return $tags;
        
        } catch (exception $e) {

            return array();

        }

    }

    private function updateTag($type, $id, $taggedWith) {

        try {

            $updatedTag = $this->sb->pgObjects->update($type, array(
                'id'=> $id
                , 'tagged_with'=> $taggedWith
            ));

            return $updatedTag;

        } catch (exception $e) {

            return array();

        }

    }

    private function deleteTags($tagsToDelete) {

        try {

            foreach ($tagsToDelete as $i => $tag) {

                $this->sb->pgObjects->delete('system_tags', $tag);

            }

        } catch (exception $e) {

            return $e;

        }

    }

// PUBLIC

    public function removeTags($request) {

        $response = new stdClass();

        if (count($request) > 0) {

            $queryRequest = json_decode(json_encode($request), true);

            $tagsToDelete = $queryRequest['tagsToConsolidate'];

            if (count($tagsToDelete) > 0) {

                try {
                    $this->deleteTags($tagsToDelete);

                    $response->status = "success";

                    $response->message = 'Tags were removed';
                    
                    $response->data = 'Tags successfully removed: ' . implode(', ', $tagsToDelete);

                } catch (exception $e) {

                    $response->status = "error";

                    $response->message = 'Caught exception: ' .  $e->getMessage() . "\n";

                }

            } else {

                $response->status = "success";

                $response->message = 'No tags were sent to be removed';

            }

        } else {

            return http_response_code(400);

        }

        return $this->sb->sendData($response, 1);

    }

    public function consolidateTags($request) {

        $response = new stdClass();

        if (count($request) > 0) {

            $queryRequest = json_decode(json_encode($request), true);

            $mainTagId = $queryRequest['mainTag'];

            $tagsToConsolidate = $queryRequest['tagsToConsolidate'];

            $taggedObjects = $this->getTags($tagsToConsolidate);
            
            $shouldDeleteTags = (bool) $queryRequest['shouldDelete'];

            if (count($taggedObjects) > 0) {

                try {
                    
                    foreach ($taggedObjects as $i => $obj) {
                        
                        $taggedUpdated = array();

                        $taggedWith = $obj['tagged_with'];

                        // remove tags being consolidated from tagged_with value
                        foreach ($taggedWith as $i => $tag) {
                            if (__::contains($tagsToConsolidate, $tag) == false) {
                                $taggedUpdated[] = $tag;
                            }
                        }

                        $taggedUpdated[] = $mainTagId; // add new consolidated tag value

                        $taggedUpdated = array_unique($taggedUpdated); // remove any dupes

                        $objId = $obj['id'];

                        $this->updateTag(
                            $obj['object_bp_type']
                            , $objId
                            , $taggedUpdated
                        );

                    }

                    if ($shouldDeleteTags) {
                        
                        $this->deleteTags($tagsToConsolidate);

                    }

                    $response->status = "success";

                    $response->message = 'Tags were consolidated';
                    
                    $response->message2 = 'Items/Spaces were successfully updated to use the new tag.';
                    
                    $response->data = 'Tags successfully consolidated: ' . implode(', ', $tagsToConsolidate);


                } catch (exception $e) {

                    $response->status = "error";

                    $response->message = 'Caught exception: ' .  $e->getMessage() . "\n";

                }


            } else {

                if ($shouldDeleteTags) {

                    $this->deleteTags($tagsToConsolidate);
                    
                }

                $response->status = "success";

                $response->message = 'Tags were consolidated';

                $response->message2 = 'No Items/Spaces were found to update with the new tag.';

                $response->data = 'Tags successfully deleted: ' . implode(', ', $tagsToConsolidate);

            }


        } else {

            return http_response_code(400);

        }

        return $this->sb->sendData($response, 1);

    }

}

?>