<?php


class iCheckGatewayService
{

    private $stripeSecretKey = null;
    private $testKey = false;

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->stripeSecretKey = getenv('STRIPE_SK');

        if (strpos($this->stripeSecretKey, 'sk_test') !== false) {

            $this->testKey = true;

        }
    }

// PRIVATE
    private function sanitizeSOAPResponse($soapResponse, $publicFunction) {

        $regex = '/<'. $publicFunction .'Result>[\s\S]*<\/'. $publicFunction .'Result>/';
        preg_match($regex, $soapResponse, $matches);

        return $matches[0];

    }


// PUBLIC
    public function createICGPaymentObj($request) {

        // payment object specific variables
        $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);
        $proposalId = $request->proposalId;
        $proposal = $this->sb->pgObjects->getById('proposals', $proposalId);
        $selectedInvoices = $this->sb->pgObjects->getById('invoices', $request->invoiceIds, 1);
        usort($selectedInvoices, function($a, $b) {
            return strtotime($a["due_date"]) - strtotime($b["due_date"]);
        });
        $project = $this->sb->pgObjects->getById('groups', $proposal['main_object'], 1);
        $allInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);
        $projectType = $project['type'];
        $percentFee = (floatval($request->percentFee)/100) + 1;
        $flatFee = floatval($request->flatFee)*100;
        $paymentTotal = $request->paymentTotalAmount;
        $feesTotal = $request->paymentFees;
        $invoiceValuePaid = $request->paymentAmount;
        $paymentOwnerId = is_null($_COOKIE['uid']) ? $project['main_contact'] : $_COOKIE['uid'];
        $optionalEmail = $request->optionalEmail; // TODO - ADD OPTIONAL EMAIL TO THE contact-payment-sources.js FORM
        $icgConfirmationNumber = $request->icgConfirmationNumber;
        $icgResponse = $request->icgResponse;
        $accountLast4 = $request->accountLast4;

        // notification specific variables
		$group = $this->sb->pgObjects->getById('groups', $proposal["main_object"], 1);
		$groupId = $group["id"];
		$groupObjectUID = $group["object_uid"];
		$startDate = empty(trim($group["start_date"])) ? "No Set Start Date" : trim($group["start_date"]);
		$instanceName = $instance["instance"];
		$managersArray = $group["managers"];
		$managerEmails = [];
		foreach($managersArray as $manager){
			array_push($managerEmails, $manager["email"]);
		}
        $paymentContact = $this->sb->pgObjects->getById('contacts', $paymentOwnerId);
        $proposalGroup = $this->sb->pgObjects->getById('', $proposal['main_object'], 0);
        $mainContact = $proposalGroup["main_contact"];
        $mainContactInfoArray = $this->sb->pgObjects->where('contact_info', array(
			'object_id' => $mainContact
			, 'is_primary' => "yes"
		), '', 1);
        $mainContactEmail = null;
        foreach ($mainContactInfoArray as $info) {

			if ($info['type']['data_type'] === 'email') {

				$mainContactEmail = $info['info'];

			}

		}
        $receiptEmailAddresses = [$paymentContact["email"]];
        if ($paymentContact["email"] != $mainContactEmail) {
			array_push($receiptEmailAddresses, $mainContactEmail);
		}
        if (!in_array($optionalEmail, $receiptEmailAddresses)) {
			array_push($emailAddresses, $optionalEmail);
		}
        $instanceInfo = $this->sb->pgObjects->getAll('invoice_system', 1);
        $instanceStreet = $instanceInfo[0]["billing_address"]["street"];
		$instanceStreet2 = $instanceInfo[0]["billing_address"]["street2"];
		$instanceCity = $instanceInfo[0]["billing_address"]["city"];
		$instanceState = $instanceInfo[0]["billing_address"]["state"];
		$instanceZip = $instanceInfo[0]["billing_address"]["zip"];
		$instanceAddressHTML = "" . $instanceName . "<br/>
			" . $instanceStreet ."<br/>
			" . ($instanceStreet2 != "" ? "" . $instanceStreet2 . "<br/>" : "") . "
			" . $instanceCity . ", " . $instanceState. " " . $instanceZip . "";

        try {

            // identify if first payment for possible state transition
            $hasAPaidInvoice = false;
            foreach ($allInvoices as $i => $inv) {

                if (
                    $inv['amount'] > 0
                    && $inv['balance'] < $inv['amount']
                ) {
                    $hasAPaidInvoice = true;
                }

            }

            foreach ($selectedInvoices as $inv) {

                if ($invoiceValuePaid == 0) {
                    break;
                }

                $amount = 0;
                if ($inv["balance"] >= $invoiceValuePaid) {

                    // paying up to the amount of the current invoice balance
                    $amount = $invoiceValuePaid;
                    $invoiceValuePaid = 0;

                } else {

                    // paying over the amount of the current invoice balance
                    $amount = $inv["balance"];
                    $invoiceValuePaid = $invoiceValuePaid - $amount;

                }
                $chargeTotal = round($amount*$percentFee + $flatFee);
                $transactionFee = $chargeTotal - $amount;

                // create payment object
                $paymentObject = $this->sb->pgObjects->create('payments', array(
                    'main_object' => $proposal['id'],
                    'amount' => $amount,
                    'fee' => $transactionFee,
                    'icg_payment_id' => $icgConfirmationNumber,
                    'invoice' => $inv["id"],
                    'test_payment' => $this->testKey,
                    'manual_payment' => false,
                    'owner' => $paymentOwnerId,
                    // 'optional_receipt_email' => $optionalEmail
                ));

                $invoiceObject = $this->sb->pgObjects->getById('invoices', $inv["id"]);
                $paymentsArray = $invoiceObject["payments"] == null ? [] : $invoiceObject["payments"];
                array_push($paymentsArray, $paymentObject["id"]);
                $invoiceObject["payments"] = $paymentsArray;
                $invoiceObject["balance"] -= $amount;
                $invoiceObject["paid"] += $amount;

                $this->sb->pgObjects->update('invoices', $invoiceObject);

            }

            // transition state if necessary
            if (is_int($projectType['onFirstFullPayment'])) {

                if (!$hasAPaidInvoice) {

					$transitionResponse = $this->sb->updateState(
						$project['id']
						, null
						, $projectType['onFirstFullPayment']
						, $this->sb->getUrl() . '/app/'. $project['instance'] .'#mystuff&1=o-project-'. $project['id'] .'-'. rawurlencode($project['name'])
						, ''
						, false
						, 0
						, function($response) use($paymentObject) {

                            // $returnObj = new stdClass();
                            // $returnObj->success = true;
                            // return $returnObj;

						}
					);
				}
            }

            // send manager/accountant notifications
            if ($instanceName == 'infinity' || $instanceName == 'nlp' || $instanceName == 'rickyvoltz') {
                if ($this->testKey) {

                    array_push($managerEmails, "<EMAIL>");

                } else {

                    array_push($managerEmails, "<EMAIL>");
                }

            }

            // send manager/accountant notifications
            if ($instanceName == 'dreamcatering') {
                if ($this->testKey) {

                    array_push($managerEmails, "<EMAIL>");

                } else {

                    array_push($managerEmails, "<EMAIL>");
                    array_push($managerEmails, "<EMAIL>.");

                }

            }

            $projectName = trim($group["name"]);
            $proposalId = $proposal["id"];
            $date = date("Y/m/d");
            $paymentAmount = number_format(floatVal($request->paymentAmount)/100, 2);
            $paymentFees = number_format(floatVal($feesTotal)/100, 2);
            $totalPayment = number_format(floatVal($paymentTotal)/100, 2);
            $instanceReplyEmail = $instance[0]["emailFrom"];
            $urlText = "bento.infinityhospitality.net/app/". $instanceName . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $groupId . "-" . urlencode($projectName);

            $notificationmergevars = (object) array(
                'TITLE' => '' ,
                'SUBJECT' => 'A new payment has been posted to "'. $projectName .'"',
                'BODY' => "" . $date . "<br/><br/>
                Project Name: " . $projectName . "<br/><br/>
                Event Id: " . $groupObjectUID . "<br/><br/>
                Start Date: " . $startDate . "<br/><br/>
                Payment Amount: $" . $paymentAmount . "<br/><br/>
                Payment Fees: $" . $paymentFees . "<br/><br/>
                Payment Total: $" . $totalPayment . "<br/><br/>
                Project Link: <a href='" . $urlText . "'>" . $urlText . "</a><br/><br/>
                iCheckGateway Confirmation: ". $icgConfirmationNumber,
                'INSTANCE_NAME' => 'Bento Systems'
            );

            $this->sb->sendEmail($managerEmails, $instanceReplyEmail, 'A payment has been posted to "'. $projectName .'"', $notificationmergevars, null, 0);

            // receipt emails for payment
            $receiptmergevars = (object) array(
                'TITLE' => '' ,
                'SUBJECT' => 'Receipt for ' . $projectName . ' Payment',
                'BODY' => "" . $date . "<br/><br/>
                This email is the receipt for your payment related to the (Proposal Id: ". $proposalId . ") " . $projectName . " event invoice.<br/></br>
                Payment Method: bank account ending in " . $accountLast4 . "<br/>
                Payment Date: " . $date . "<br/>
                Payment Amount: $" . $paymentAmount . "<br/>
                Payment Fees: $" . $paymentFees . "<br/>
                Payment Total: $" . $totalPayment . "<br/>
                If you have any questions please reach out to your invoice provider.<br/><br/>",
                'INSTANCE_NAME' => 'Bento Systems'
            );

            $this->sb->sendEmail($receiptEmailAddresses, $instanceReplyEmail, 'Receipt for ' . $projectName . ' Payment', $receiptmergevars, null, 0);

            $returnObj = new stdClass();
            $returnObj->success = true;
            return $this->sb->sendData($returnObj, 1);

        } catch (Exception $e) {
            return $this->sb->sendData($e, 0);
        }
    }

    public function createICGPaymentObj2($request) {

        ob_start();

        echo " start :><br>";
        echo "<pre> icg response « \n ", var_dump( json_encode($request->icgResponse , JSON_PRETTY_PRINT) ), "</pre>\n";
        echo " <: end <br>";

        $icgresp = ob_get_clean();

        ob_end_flush();

        function httpPost($url, $data)
        {
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($curl);
            curl_close($curl);
            return $response;
        }

        $payload = array(
            "data"=> $icgresp
        );

        httpPost("https://eojwvcllygls5dw.m.pipedream.net", $payload);

        // $request->invoiceIds = array(1495);
        // $request->invoiceIds = array(1494);
        // // $request->invoiceIds = array(1543);
        // // $request->invoiceIds = array(1543, 1494, 1495);
        // $request->paymentAmount = 100000;
        // $request->instanceId = 920409;
        // $request->icgResponse = 'Accepted';
        // $request->accountLast4 = 9876;

        ///LOG output from real ICG response
        // start :>
        //     INCOMING REQUEST OBJ «
        //     string(1027) "{
        //         "instanceId": 1044707,
        //         "paymentAmount": 2283740,
        //         "paymentFees": 0,
        //         "paymentTotalAmount": 2283740,
        //         "proposalId": 6990407,
        //         "invoiceIds": [
        //             6990415
        //         ],
        //         "icgResponse": {
        //             "custId": "6955139",
        //             "accountNumber": "7444",
        //             "accountName": "Katrina Herman",
        //             "routingNumber": "*********",
        //             "accountType": "PC",
        //             "amount": "22837.4",
        //             "token": "85a142c1ea654a6f8051acd63278bd5b",
        //             "source": "iCheck",
        //             "error": null,
        //             "operation": "SaveACHTokenAndProcessACHTransaction",
        //             "transactionResponse": "APPROVED|NBK6|Herman|Katrina||||1fd146847889||||PC|*********|*****7444|22837.4||||N|||9\/27\/2023 6:41:31 PM|||",
        //             "verifyCheckResponse": "",
        //             "signature": "691395674614611713cbc2cf9e55b61c9acc431e9f5afa8b2aa514200cca6613e66077229c5f2c25238f30ba9546b1f3faabf9459b6678f23650cd1a35ded3ad"
        //         },
        //         "accountLast4": "7444",
        //         "percentFee": "0",
        //         "flatFee": "0",
        //         "icgConfirmationNumber": "1fd146847889"
        //     }"
        //     <: end

         // TODO - ADD OPTIONAL EMAIL TO THE contact-payment-sources.js FORM
        $optionalEmail = $request->optionalEmail;
        $icgConfirmationNumber = $request->icgConfirmationNumber;
        $icgResponse = $request->icgResponse;
        $accountLast4 = $request->accountLast4;
        $paymentTotal = $request->paymentTotalAmount;
        $feesTotal = $request->paymentFees;
        $invoiceValuePaid = $request->paymentAmount;

        $percentFee = (floatval($request->percentFee)/100) + 1;
        $flatFee = floatval($request->flatFee)*100;


        // payment object specific variables
        $instance = $this->sb->pgObjects->getById('instances', $request->instanceId);

        $proposalId = $request->proposalId;
        $proposal = $this->sb->pgObjects->getById('proposals', $proposalId);

        ///Incoming request is an array of invoice ids
        $selectedInvoices = $this->sb->pgObjects->getById('invoices', $request->invoiceIds, 1);
        ///Sort selected invoices by due date. First one with closest due_date to be settled first.
        usort($selectedInvoices, function($a, $b) {
            return strtotime($a["due_date"]) - strtotime($b["due_date"]);
        });

        $project = $this->sb->pgObjects->getById('groups', $proposal['main_object'], 1);
        $projectType = $project['type'];

        $paymentOwnerId = is_null($_COOKIE['uid']) ? $project['main_contact']['id'] : $_COOKIE['uid'];


        // identify if first payment for possible state transition
        $allInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);
        $hasAPaidInvoice = false;
        foreach ($allInvoices as $i => $inv) {

            if (
                $inv['amount'] > 0
                && $inv['balance'] < $inv['amount']
            ) {
                $hasAPaidInvoice = true;
            }

        }
        if (is_int($projectType['onFirstFullPayment'])) {
            if (!$hasAPaidInvoice) {

                $transitionResponse = $this->sb->updateState(
                    $project['id']
                    , null
                    , $projectType['onFirstFullPayment']
                    , $this->sb->getUrl() . '/app/'. $project['instance'] .'#mystuff&1=o-project-'. $project['id'] .'-'. rawurlencode($project['name'])
                    , ''
                    , false
                    , 0
                    , function($response) use($paymentObject) {

                        // $returnObj = new stdClass();
                        // $returnObj->success = true;
                        // return $returnObj;

                    }
                );
            }
        }

        /// get ALL other unpaid invoices [including $selectedInvoices since they have not been reconciled just yet]
        $unpaidRemaining = $this->sb->pgObjects->where('invoices'
            , [
                'related_object' => $proposal['id']
                , 'balance' => [
					'type' => 'greater_than',
					'value' => 0
				],
        ]);
        ///sort remaining by closest due date
        usort($unpaidRemaining, function($a, $b) {
            return strtotime($a["due_date"]) - strtotime($b["due_date"]);
        });

        ///filter out the selectedinvoices from the $unpaidRemaining list
        $selectedIds = array_column($selectedInvoices, 'id');
        $unpaidRemaining = __::reject( $unpaidRemaining, function($invoice) use($selectedIds) {
            return in_array($invoice['id'], $selectedIds);
        });

        // notification specific variables
		$group = $this->sb->pgObjects->getById('groups', $proposal["main_object"], 1);
		$groupId = $group["id"];
		$groupObjectUID = $group["object_uid"];
		$startDate = empty(trim($group["start_date"])) ? "No Set Start Date" : trim($group["start_date"]);
		$instanceName = $instance["instance"];
		$managersArray = $group["managers"];

		$managerEmails = [];
		foreach($managersArray as $manager){
			array_push($managerEmails, $manager["email"]);
		}

        $paymentContact = $this->sb->pgObjects->getById('contacts', $paymentOwnerId);
        $proposalGroup = $this->sb->pgObjects->getById('', $proposal['main_object'], 0);
        $mainContact = $proposalGroup["main_contact"];
        $mainContactInfoArray = $this->sb->pgObjects->where('contact_info', array(
			'object_id' => $mainContact
			, 'is_primary' => "yes"
		), '', 1);
        $mainContactEmail = null;
        foreach ($mainContactInfoArray as $info) {

			if ($info['type']['data_type'] === 'email') {

				$mainContactEmail = $info['info'];

			}

		}

        $receiptEmailAddresses = [$paymentContact["email"]];

        if ($paymentContact["email"] != $mainContactEmail) {
			array_push($receiptEmailAddresses, $mainContactEmail);
		}
        if (!in_array($optionalEmail, $receiptEmailAddresses)) {
			array_push($emailAddresses, $optionalEmail);
		}

        $instanceInfo = $this->sb->pgObjects->getAll('invoice_system', 1);
        $instanceStreet = $instanceInfo[0]["billing_address"]["street"];
		$instanceStreet2 = $instanceInfo[0]["billing_address"]["street2"];
		$instanceCity = $instanceInfo[0]["billing_address"]["city"];
		$instanceState = $instanceInfo[0]["billing_address"]["state"];
		$instanceZip = $instanceInfo[0]["billing_address"]["zip"];
		$instanceAddressHTML = "" . $instanceName . "<br/>
			" . $instanceStreet ."<br/>
			" . ($instanceStreet2 != "" ? "" . $instanceStreet2 . "<br/>" : "") . "
			" . $instanceCity . ", " . $instanceState. " " . $instanceZip . "";

        $logPaymentsArray = array();
        $logInvoicesArray = array();

        while ($invoiceValuePaid > 0):

            if(empty($selectedInvoices))
                break;

            foreach($selectedInvoices as $i => &$invoice){

                $currentInvoice = $invoice;

                $lastInvoice = end($selectedInvoices);
                $payment = 0;
                $paymentObject = array();

                if ( $invoiceValuePaid == 0 || $invoice['balance'] == 0 ) break 2;

                if ($invoiceValuePaid < $invoice['balance']) {

                    ///create payment object
                    $payment = $invoiceValuePaid;
                    $chargeTotal = round($payment*$percentFee + $flatFee);
                    $transactionFee = $chargeTotal - $payment;

                    /// reconcile invoiceValuePaid amount
                    $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                    $paymentObj = array(
                        'main_object' => $proposal['id'],
                        'amount' => $payment,
                        'fee' => $transactionFee,
                        'icg_payment_id' => $icgConfirmationNumber,
                        'invoice' => $invoice["id"],
                        'test_payment' => $this->testKey,
                        'manual_payment' => false,
                        'owner' => $paymentOwnerId,
                        // 'optional_receipt_email' => $optionalEmail
                    );

                    // create payment object
                    $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                    $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                    array_push($paymentsArray, $paymentObject["id"]);

                    $invoice["payments"] = $paymentsArray;
                    $invoice["balance"] = $invoice['balance'] - $payment;
                    $invoice["paid"] += $payment;

                    $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                } else {

                    /// reconcile invoiceValuePaid amount
                    $invoiceValuePaid = $invoiceValuePaid - $invoice['balance'];

                    ///create payment object
                    $payment = $invoice['balance'];

                    $invoice['balance'] = $invoice['balance'] - $payment;

                    if ( end($selectedInvoices)['id'] == $currentInvoice['id'] && $invoiceValuePaid > 0 && empty($unpaidRemaining) ) {

                        $payment = $invoiceValuePaid + $payment;

                        $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                    }

                    $chargeTotal = round($payment*$percentFee + $flatFee);
                    $transactionFee = $chargeTotal - $payment;

                    $paymentObj = array(
                        'main_object' => $proposal['id'],
                        'amount' => $payment,
                        'fee' => $transactionFee,
                        'icg_payment_id' => $icgConfirmationNumber,
                        'invoice' => $invoice["id"],
                        'test_payment' => $this->testKey,
                        'manual_payment' => false,
                        'owner' => $paymentOwnerId,
                        // 'optional_receipt_email' => $optionalEmail
                    );

                    // create payment object
                    $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                    $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                    array_push($paymentsArray, $paymentObject["id"]);

                    $invoice["payments"] = $paymentsArray;
                    $invoice['paid'] += $payment;

                    $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                }

                array_push($logPaymentsArray, $paymentObject);
                array_push($logInvoicesArray, $updatedInvoice);

                unset($payment);
                unset($selectedInvoices[$i]);

            }

        endwhile;

        while ($invoiceValuePaid > 0):

            if(empty($unpaidRemaining))
                break;

            foreach($unpaidRemaining as $i => &$invoice){

                $currentInvoice = $invoice;

                $lastInvoice = end($selectedInvoices);
                $payment = 0;
                $paymentObject = array();

                if ( $invoiceValuePaid == 0 ) break 2;

                if ($invoiceValuePaid < $invoice['balance']) {

                    ///create payment object
                    $payment = $invoiceValuePaid;
                    $chargeTotal = round($payment*$percentFee + $flatFee);
                    $transactionFee = $chargeTotal - $payment;

                    /// reconcile invoiceValuePaid amount
                    $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                    $paymentObj = array(
                        'main_object' => $proposal['id'],
                        'amount' => $payment,
                        'fee' => $transactionFee,
                        'icg_payment_id' => $icgConfirmationNumber,
                        'invoice' => $invoice["id"],
                        'test_payment' => $this->testKey,
                        'manual_payment' => false,
                        'owner' => $paymentOwnerId,
                        // 'optional_receipt_email' => $optionalEmail
                    );

                    // create payment object
                    $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                    $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                    array_push($paymentsArray, $paymentObject["id"]);

                    $invoice["payments"] = $paymentsArray;
                    $invoice["balance"] = $invoice['balance'] - $payment;
                    $invoice["paid"] += $payment;

                    $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                } else {

                    /// reconcile invoiceValuePaid amount
                    $invoiceValuePaid = $invoiceValuePaid - $invoice['balance'];

                    ///create payment object
                    $payment = $invoice['balance'];
                    var_dump("665 :: payment ", $payment ). PHP_EOL . "<br>";

                    $invoice['balance'] = $invoice['balance'] - $payment;

                    if ( end($unpaidRemaining)['id'] == $currentInvoice['id'] && $invoiceValuePaid > 0 ) {

                        $payment = $invoiceValuePaid + $payment;

                        $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                    }

                    $chargeTotal = round($payment*$percentFee + $flatFee);
                    $transactionFee = $chargeTotal - $payment;

                    $paymentObj = array(
                        'main_object' => $proposal['id'],
                        'amount' => $payment,
                        'fee' => $transactionFee,
                        'icg_payment_id' => $icgConfirmationNumber,
                        'invoice' => $invoice["id"],
                        'test_payment' => $this->testKey,
                        'manual_payment' => false,
                        'owner' => $paymentOwnerId,
                        // 'optional_receipt_email' => $optionalEmail
                    );

                    // create payment object
                    $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                    $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                    array_push($paymentsArray, $paymentObject["id"]);

                    $invoice["payments"] = $paymentsArray;
                    $invoice['paid'] += $payment;

                    $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                }

                array_push($logPaymentsArray, $paymentObject);
                array_push($logInvoicesArray, $updatedInvoice);

                unset($payment);
                unset($unpaidRemaining[$i]);

            }

        endwhile;

        // @TODO send manager/accountant notifications
        if ($instanceName == 'infinity' || $instanceName == 'nlp' || $instanceName == 'rickyvoltz') {

            array_push($managerEmails, "<EMAIL>");
            // if ($this->testKey) {

            //     array_push($managerEmails, "<EMAIL>");

            // } else {

            // }

        }

        // @TODO send manager/accountant notifications
        if ($instanceName == 'dreamcatering') {

            array_push($managerEmails, "<EMAIL>", "<EMAIL>");
            // if ($this->testKey) {

            //     array_push($managerEmails, "<EMAIL>");

            // } else {

            //     // array_push($managerEmails, "<EMAIL>");
            // }

        }

        $projectName = trim($group["name"]);
        $proposalId = $proposal["id"];
        $date = date("Y/m/d");
        $paymentAmount = number_format(floatVal($request->paymentAmount)/100, 2);
        $paymentFees = number_format(floatVal($feesTotal)/100, 2);
        $totalPayment = number_format(floatVal($paymentTotal)/100, 2);
        $instanceReplyEmail = $instance[0]["emailFrom"];
        $urlText = "bento.infinityhospitality.net/app/". $instanceName . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $groupId . "-" . urlencode($projectName);

        $notificationmergevars = (object) array(
            'TITLE' => '' ,
            'SUBJECT' => 'A new payment has been posted to "'. $projectName .'"',
            'BODY' => "" . $date . "<br/><br/>
            Project Name: " . $projectName . "<br/><br/>
            Event Id: " . $groupObjectUID . "<br/><br/>
            Start Date: " . $startDate . "<br/><br/>
            Payment Amount: $" . $paymentAmount . "<br/><br/>
            Payment Fees: $" . $paymentFees . "<br/><br/>
            Payment Total: $" . $totalPayment . "<br/><br/>
            Project Link: <a href='" . $urlText . "'>" . $urlText . "</a><br/><br/>
            iCheckGateway Confirmation: ". $icgConfirmationNumber,
            'INSTANCE_NAME' => 'Bento Systems'
        );

        ///SEND MANAGER NOTIFICATION EMAIL
        $this->sb->sendEmail(
            $managerEmails
            , $instanceReplyEmail
            , 'A payment has been posted to "'. $projectName .'"'
            , $notificationmergevars
            , null
            , 0
        );

        // receipt emails for payment
        $receiptmergevars = (object) array(
            'TITLE' => '' ,
            'SUBJECT' => 'Receipt for ' . $projectName . ' Payment',
            'BODY' => "" . $date . "<br/><br/>
            This email is the receipt for your payment related to the (Proposal Id: ". $proposalId . ") " . $projectName . " event invoice.<br/></br>
            Payment Method: bank account ending in " . $accountLast4 . "<br/>
            Payment Date: " . $date . "<br/>
            Payment Amount: $" . $paymentAmount . "<br/>
            Payment Fees: $" . $paymentFees . "<br/>
            Payment Total: $" . $totalPayment . "<br/>
            If you have any questions please reach out to your invoice provider.<br/><br/>",
            'INSTANCE_NAME' => 'Bento Systems'
        );

        /// SEND EMAIL RECEIPT
        $this->sb->sendEmail(
            $receiptEmailAddresses
            , $instanceReplyEmail
            , 'Receipt for ' . $projectName . ' Payment'
            , $receiptmergevars
            , null
            , 0
        );

        ///SEND LOGGING EMAIL [TEST ONLY]
        /// ob_start() - This function will turn output buffering on. While output buffering is active no output is sent from the script (other than headers)
        ///, instead the output is stored in an internal buffer.
        /// ob_get_contents() - The contents of this internal buffer may be copied into a string variable using ob_get_contents().
        /// ob_end_flush() - To output what is stored in the internal buffer, use ob_end_flush().

        ob_start();

        function test ($var) {
            echo " " . $var . "<br>";
        }

        test("start :>");
        echo "<pre> INCOMING REQUEST OBJ « \n ", var_dump( json_encode($request , JSON_PRETTY_PRINT) ), "</pre>\n";
        echo "<pre> notificationmergevars « \n ", var_dump( json_encode($notificationmergevars , JSON_PRETTY_PRINT) ), "</pre>\n";
        echo "<pre> payments created « \n ", var_dump( json_encode($logPaymentsArray , JSON_PRETTY_PRINT) ), "</pre>\n";
        echo "<pre> invoices updated « \n ", var_dump( json_encode($logInvoicesArray , JSON_PRETTY_PRINT) ), "</pre>\n";
        test("<: end");

        $content = ob_get_clean();

        ob_end_flush();


        $loggingEmail = (object) array(
            'TITLE' => '' ,
            'SUBJECT' => 'LOG',
            'INSTANCE_NAME' => 'Bento Systems',
            'BODY' => $content
        );

        $this->sb->sendEmail(
            '<EMAIL>'
            , $instanceReplyEmail
            , 'ICHECKGATEWAY LOGGING DETAILS'
            , $loggingEmail
            , null
            , 0
        );

        $updatedInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);

        $returnObj = new stdClass();
        $returnObj->success = true;
        $returnObj->invoices = $updatedInvoices;

        $returnObj->processedPaymentAmount = $request->paymentAmount;
        $returnObj->processedPayments = $logPaymentsArray;
        $returnObj->processedInvoices = $logInvoicesArray;

        return $this->sb->sendData($returnObj, 1);

    }

}
