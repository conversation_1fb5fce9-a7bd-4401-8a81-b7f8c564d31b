<?php

class FGProjectsService
{

    function __construct($sb)
    {
        $this->sb = $sb;
    }

// PUBLIC

    public function getCollectionData($request)
    {
// error_reporting(E_ALL);
// ini_set('display_errors', '1');

        $roleSheetKey = '#ReU6uX';
        $serviceKey = '#HDIjlE';

        $where = json_decode(json_encode($request->queryObj), true);
        $select = json_decode(json_encode($request->getChildObjs), true);

        // Assigned (from the role sheet under the project)
        $select[$roleSheetKey] = [
            'specialist' => true
        ];
        
        // Due Date (from the Service under the project)
        // $select[$serviceKey] = [
        //     '_9' => true // Due Date
        // ];
        
        $projects = $this->sb->getObjectsWhere(
            'groups'
            , $where
            , 0
            , $select
        );

        function findByUid($states, $searchUid) 
        {

            foreach ($states as $st) {
                if ($st['uid'] === $searchUid) {
                    return $st;
                }
            }

            return null;
        }

        $statusWhiteList = ['Intake', 'More Info Requested','WIP', 'Hold Until 990', 'Hold | State/Date of Inc.'
            , 'Hold | 1023 Application', 'Hold | IRS Determination Letter', 'Hold | 990'
            , 'Hold', 'Hold for Payment', 'Info Review', 'Information Review', 'Client Review'
            , 'Authorized', 'Final Review', 'Unassigned', 'Complete', 'Withdrawn'];
            
        $filtered = [];

        foreach($projects['data'] as $i => &$proj){

            $possibleStates = $proj['type']['states'];
            $currentState = $proj['state'];
            $foundState = findByUid($possibleStates, $currentState);

            if ($foundState !== null) {

                if ( in_array($foundState['name'], $statusWhiteList) ) {
                    array_push($filtered, $proj);
                }

            }

            unset($g);

        }
        $projects['data'] = $filtered;


        // Fiscal year end date (from the core demo on the company)
        $companyIds = []; // !Might not need this anymore
        $projectIds = [];
        
        foreach ($projects['data'] as $i => $project) {

            array_push($projectIds, $project['id']);

            if (
                is_array($project)
                && is_array($project['main_contact'])
                && is_array($project['main_contact']['company'])
            ) {

                array_push($companyIds, $projects['main_contact']['company']);

            }

        }

        // Date Intake Complete
        $logs = $this->sb->pgObjects->where(
            'notes'
            , [
                'type_id' => [
                    'type' => 'or'
                    , 'values' => $projectIds
                ]
                , 'note' => [
                    'type' => 'contains'
                    , 'value' => 'Changed from <strong>Intake</strong>'
                ]
            ]
            , ''
            , [
                'log_data' => true
                , 'note' => true
                , 'type_id' => 'id'
                , 'date_created' => true
            ]
        );
        $logs = __::sortBy($logs, function($log){
			
			return strtotime($log['date_created']);
			
		});
        // var_dump($logs);
        // die();

        // Get the associated Services records
        $services = $this->sb->pgObjects->where(
            $serviceKey
            , [
                'parent' => [
                    'type' => 'or'
                    , 'values' => $projectIds
                ]
            ]
            , ''
            , [
                'name' =>       true
                , 'parent' =>   'id'
                , '_9' =>       true // Due Date
                , '_16' =>      true // Fiscal Year End Date
            ]
        );

        // Get the associated Role Sheet records
        $roleSheets = $this->sb->pgObjects->where(
            $roleSheetKey
            , [
                'parent' => [
                    'type' => 'or'
                    , 'values' => $projectIds
                ]
            ]
            , ''
            , [
                'name' =>       true
                , 'parent' =>   'id'
                , '_7' =>       [
                    'name' => true
                    , 'fname' => true
                    , 'lname' => true
                    , 'profile_image' => true
                ] // Assigned
            ]
        );
        // var_dump($roleSheets);
        // die();

        // Merge in data from related objs
        foreach ($projects['data'] as $i => $project) {

            $company = $project['main_contact']['company'];

            $coreDemo = $this->sb->pgObjects->where(
                "#ml7laG"
                , [
                    'parent' => $company['id']
                ]
                , ''
                , [
                    'name' =>       true
                    , '_24' =>      true
                ]
            )[0];

            if ( is_array($coreDemo) && is_string($coreDemo['_24']) ) {
                $projects['data'][$i]['dateOfInc'] = $coreDemo['_24'];
                $projects['data'][$i]['core_demographic'] = $coreDemo['id'];
            } else {
                $projects['data'][$i]['dateOfInc'] = null;
            }

            // Due date from the service obj
            $service = false;
            $service = __::find(
                $services
                , function ($service) use ($project) {
                    return $project['id'] === $service['parent'];
                }
            );

            if (
                is_array($service)
                && is_string($service['_9'])
            ) {
                $projects['data'][$i]['dueDate'] = $service['_9'];
                $projects['data'][$i]['fiscalYearEndDate'] = $service['_16'];
            }

            // Assigned from the Role Sheet obj
            $roleSheet = false;
            $roleSheet = __::find(
                $roleSheets
                , function ($roleSheet) use ($project) {
                    return $project['id'] === $roleSheet['parent'];
                }
            );

            if (
                is_array($roleSheet)
                && !empty($roleSheet['_7'])
            ) {
                $projects['data'][$i]['managers'] = $roleSheet['_7'];
            } else {
                $projects['data'][$i]['managers'] = [];
            }

            // Intake Completed (from out of 'Intake' state change logs)
            if (is_array($logs)) {
                foreach ($logs as $log) {

                    if (
                        $log['type_id'] === $project['id']
                        && preg_match("/\Changed from Intake to \b/", strip_tags($log['note']))
                    ) {

                        $projects['data'][$i]['dateIntakeComplete'] = $log['date_created'];

                    } elseif (
                        $log['type_id'] === $project['id']
                        && (
                            preg_match("/\ to Intake by \b/", strip_tags($log['note']))
                            || preg_match("/\ to Intake  by \b/", strip_tags($log['note']))
                            || preg_match("/\ to More Info Requested by \b/", strip_tags($log['note']))
                            || preg_match("/\ to More Info Requested  by \b/", strip_tags($log['note']))
                        )
                        && strtotime($projects['data'][$i]['dateIntakeComplete']) !== false
                        && strtotime($log['date_created']) > strtotime($projects['data'][$i]['dateIntakeComplete'])
                    ) {

                        unset($projects['data'][$i]['dateIntakeComplete']);

                    }

                }
            }

        }

        return $this->sb->sendData($projects, 1);

    }

    //Allow to retrive the pending Intake Questionnaire for action items
    public function hasPendingIntakeQuestionnaire($request){

        $projectId = $request->projectId;

        $response = new stdClass();

        $select = [
            "##Action Items" => ["name" => true, "status" => true],
            "name" => true
        ];

        $project = $this->sb->pgObjects->getById(
            'groups',
            intval($projectId),
            $select
        );

        $intakeQuestionnaire = 4759706;

        $response->project = $project["id"];
        $response->hasPending = false;
        $response->actionItemName = "";
        $response->actionItemId = "";
        $response->status = "";

        foreach($project["##Action Items"] as $actionItem){
            if($actionItem["status"] != 'done'){
                $actionItemType = $this->sb->pgObjects->getBlueprint($actionItem['object_bp_type'], false, true, true);
                if(in_array($intakeQuestionnaire, $actionItemType['tagged_with'])){

                    $response->hasPending = true;
                    $response->actionItemName = $actionItem["name"];
                    $response->actionItemId = $actionItem["id"];
                    $response->status = $actionItem["status"];

                    break;
                }

            }
        }

        return $this->sb->sendData($response, 1);

    }

    private function searchRoleSheetSetting($item){
        $roleSheetSettings = __::find($item['parent']['tools'], function($obj){
            $startWith = "Project Role Sheet";
            $len = strlen($startWith);
            return (substr($obj["display_name"], 0, $len) === $startWith);
        });
        if(!$roleSheetSettings){
            return false;
        }
        return "#".$roleSheetSettings['system_name'];
    }

    private function contains($word, $inText){
        $patt = "/(?:^|[^a-zA-Z])" . preg_quote($word, '/') . "(?:$|[^a-zA-Z])/i";
        return preg_match($patt, $inText);
    }

    private function hasSomeValue($items, $value){
        foreach($items as $item) {
            if($value && $this->contains($item, $value)){
                return true;
            }
        }
        return false;
    }

    private function uniqueArray($array, $key) {
        $temp_array = array();
        $i = 0;
        $key_array = array();

        foreach($array as $val) {
            if (!in_array($val[$key], $key_array)) {
                $key_array[$i] = $val[$key];
                $temp_array[$i] = $val;
            }
            $i++;
        }
        return $temp_array;
    }

    function getActionItemsWithRoleUser($actionItem, $role){

        $roleSheetsCached = [];

        //filtered by role (and capture real names for rolesheets)

            $actionItem['assigned'] = [];

            // Get the associated Role Sheet records
            $roleSheetKeyPerProject = $this->searchRoleSheetSetting($actionItem);

            if(!$roleSheetKeyPerProject){
                return $actionItem;
            }

            //search blueprint
            $roleSheetBp = $this->sb->pgObjects->getBlueprint($roleSheetKeyPerProject);


            //filtered by rolesheet (only if exists rolesheet)
            $fieldIdRoleSheet = [];
            $fieldnamesRoleSheet = [];

            foreach ($roleSheetBp as $key => $item) {
                $name = strtolower($item['name']);
                if ($item["fieldType"] == "user" && $item['is_archived'] == false) {
                    if ($this->hasSomeValue($role, $name)) {
                        $fieldIdRoleSheet[] = $key;
                        $fieldnamesRoleSheet[] = $item["name"];
                    }
                }
            }



            $usersRole = [];

            foreach($fieldIdRoleSheet as $fieldId){
                $queryRoleSheetWhere[$fieldId] = true;
            }

            //cached rolesheet

            $roleSheets = null;

            if($roleSheetsCached[$roleSheetKeyPerProject] && $roleSheetsCached[$roleSheetKeyPerProject][$actionItem['parent']['id']]){
                //search in cache
                $roleSheets = $roleSheetsCached[$roleSheetKeyPerProject][$actionItem['parent']['id']];
            } else {
                //new query
                $roleSheets = $this->sb->pgObjects->where(
                    $roleSheetKeyPerProject
                    , [
                        'parent' => $actionItem['parent']['id']
                    ]
                    , ''
                    , $queryRoleSheetWhere
                )[0];
                $roleSheetsCached[$roleSheetKeyPerProject][$actionItem['parent']['id']] = $roleSheets;
            }

            foreach($fieldIdRoleSheet as $index => $field){
                if($roleSheets[$field]) {

                    $usersRole[] = [
                        'role' => $fieldnamesRoleSheet[$index],
                        'user' => $roleSheets[$field]
                    ];

                    $actionItem['assigned'][] = ['name' => $roleSheets[$field]['name'], 'role'=> $fieldnamesRoleSheet[$index]];
                }
            }

        return $actionItem;

    }

    public function getActionsItemsWithRole($request){

        $queryRequest = json_decode(json_encode($request), true);

        $entityTypes = $queryRequest['entity_types'];
        $dateCreated = $queryRequest['last_updated'];
        $taggedWith = $queryRequest['tagged_with'];
        $projectType = $queryRequest['project_type'];
        $usersToSearch = $queryRequest['usersInRole'] ? $queryRequest['usersInRole'] : false;
        $role = $queryRequest['roles'] ? $queryRequest['roles'] : false;
        $formType = $queryRequest['formType'] ? $queryRequest['formType'] : false; // (info_request,client_review,intake)

        $paged = $queryRequest["paged"];
        $sortCol = $paged["sortCol"];
        $sortDir = $paged["sortDir"];
        $pageLength = $paged['pageLength'];
        $page = $paged['page'];
        $actionItemsIndexStart = $page;


        $RCAType = $taggedWith['values'][0];



        $childObjects = [
            'name' =>       true
            , 'parent' =>   [
                'name' => true,
                'main_contact' => ["company" => true],
                'type' => ['name' => true],
                'group_type' => true,
                'last_updated' => true,
                'tools' => true,

            ]
            , 'last_updated' => true
            , 'rca_last_updated_uploads' => true
            , 'rca_last_updated_comments' => true
        ];

        
        $actionItems = [];

        foreach ($entityTypes as $i => $item) {

            $query = [
                'tagged_with' => $taggedWith,
                $sortCol => $dateCreated
            ];

            $objectType = '#'. $item;

            $actionItemsPerObjectType = $this->sb->pgObjects->where(
                $objectType
                , $query
                , ''
                , $childObjects
            );
// var_dump("430 :: actionItemsPerObjectType ", $actionItemsPerObjectType ). PHP_EOL . "<br>";

            //if tagged with RCA comments or RCA uploads

            $RCACompleted = 8348219;
            $RCAComments = 9143452;
            $RCAUploads = 9143453;

            if($RCAType === $RCAComments || $RCAType === $RCAUploads) {

                $actionItemsPerObjectType = __::filter($actionItemsPerObjectType,
                    function ($actionItem) use ($RCACompleted) {
                        return !(in_array($RCACompleted, $actionItem['tagged_with']));
                    }
                );

            }


            if($projectType){

                $actionItemsPerObjectType = __::filter($actionItemsPerObjectType,
                    function($actionItem) use($projectType) {
                        $hasValidProjectType = (($actionItem['parent']['type']['id']) == ($projectType));
                        return $hasValidProjectType;
                    }
                );

            }


            //items to add
            foreach($actionItemsPerObjectType as $item){
                $actionItems[] = $item;
            }

        }

        //remove the uniq array
        if(count($actionItems) > 0) {
            $actionItems = $this->uniqueArray($actionItems, 'id');
        }

        //sort order
        if ($sortCol) {

            if ($sortDir === "asc") {

                usort($actionItems,function($a, $b) use ($sortCol){

                    return strtotime($a[$sortCol]) - strtotime($b[$sortCol]);
                });

            } else {

                usort($actionItems,function($a, $b) use ($sortCol){

                    return strtotime($b[$sortCol]) - strtotime($a[$sortCol]);
                });

            }

        }
        //reduce limit pagination
        $actionItemsFiltered = array_slice($actionItems, $actionItemsIndexStart, $pageLength);

        foreach ( $actionItemsFiltered as $i => &$item) {

            //adding the rolesheet values
            $item = $this->getActionItemsWithRoleUser($item, $role);

            //adding the comments
            $whereCond = ['type_id' => $item['id']];

            $orderBy = 'date_created';

            $note = $this->sb->pgObjects->where(
                'notes',
                $whereCond,
                '',
                1,
                true,
                0,
                $orderBy,
                'desc',
                1
            );

            $note = __::filter($note,
                function($comment) {
                    if($comment['record_type'] == 'comment'){
                        return true;
                    }
                    return false;
                }
            );

            if($note){
                $actionItemsFiltered[$i]['notes'] = $note[0];
            } else {
                $actionItemsFiltered[$i]['notes'] = [];
            }

        }

        $returnObj = [
            "draw" => true
            , "recordsFiltered" => $pageLength
            , "recordsTotal" => 0
            , "data" => $actionItemsFiltered
        ];

        $returnObj["recordsTotal"] = count($actionItems);


        return $this->sb->sendData($returnObj, 1);

    }

    public function getRolesheetAssignments($request){

        $actionItems = json_decode(json_encode($request->actionItems), true);
        $roleSheetKey = '#ReU6uX';
        $projectIds = [];
        
        foreach ($actionItems as $i => $item) {
        
            if (
                $item['parent']['group_type'] == 'Project'
            ) {
                    array_push($projectIds, $item['parent']['id']);
            }

        }

        // Get the associated Role Sheet records
        $roleSheets = $this->sb->pgObjects->where(
            '#ReU6uX'
            , [
                'parent' => [
                    'type' => 'or'
                    , 'values' => $projectIds
                ]
            ]
            , ''
            , [
                'name' =>       true
                , 'parent' =>   'id'
                , '_7' =>       [
                    'name' => true
                    , 'fname' => true
                    , 'lname' => true
                    , 'profile_image' => true
                ] 
            ]
        );

        foreach ( $actionItems as $i => &$item) {

            // Assigned from the Role Sheet obj
            $roleSheet = false;
            $roleSheet = __::find(
                $roleSheets
                , function ($roleSheet) use ($item) {
                    return $item['parent']['id'] === $roleSheet['parent'];
                }
            );

            if (
                is_array($roleSheet)
                && !empty($roleSheet['_7'])
            ) {
                $item['assigned'] = $roleSheet['_7'];
            } else {
                $item['assigned'] = [];
            }

        }

        foreach ( $actionItems as $i => &$item) {

            $whereCond = [
                'parent' => $item['object_bp_type']
            ];
            $orderBy = 'date_created';


            $note = $this->sb->pgObjects->where(
                'notes',
                $whereCond,
                '',
                1,
                true,
                0,
                $orderBy,
                'desc',
                1
            );

            if($note){
                $item['notes'] = $note;
            } else {
                $item['notes'] = [];
            }

        }

        return $this->sb->sendData($actionItems, 1);

    }

    public function getActionItemsAcrossAllOrgs($request){

        $response = new stdClass();

        $data = json_decode(json_encode($request), true);

        $actionItems = $this->sb->pgObjects->where(
            'any'
            , [
                'tagged_with' => [ 
                    $data['rca_system_tag']
                    , $data['team'] 
                ]
            ]
            , ''
            , [
                'name' =>       true
                , 'parent' =>   'id'
                , 'last_updated' => true
            ]
            , true
            , 0
            , 'last_updated'
            , 'asc'
            , 50
            , null
            , array()
            , 'string'
            , true
        );

        $response->actionItems = $actionItems;

        return $this->sb->sendData($response, 1);

    }

    public function shareActionItemsWithAllContacts($request){

        $response = new stdClass();

        $actionItemsIds = json_decode(json_encode($request), true);
        $actionItemList = array();
        $parentOrg = array();

        if ( !empty($actionItemsIds) ) {

            foreach ( $actionItemsIds as $i => $ac) {
    
                $ac = $this->sb->pgObjects->getById(
                    ''
                    , $ac
                    , 1
                    , [
                        'name' =>           true
                        , 'parent' =>       [
                            'id' => true,
                            'object_bp_type' => true
                        ]
                        , 'shared_with' => true
                        , 'tagged_with' => true
                    ]
                    , true
                    , 0
                    , 'last_updated'
                    , 'asc'
                    , 50
                    , null
                    , array()
                    , 'string'
                    , true
                );
    
                array_push(
                    $actionItemList
                    , array(
                        'name' => $ac['name']
                        , 'shared_with' => $ac['shared_with']
                        , 'id' => $ac['id']
                        , 'parent' => $ac['parent']
                        , 'object_bp_type' => $ac['object_bp_type']
                        , 'tagged_with' => $ac['tagged_with']
                    ));
    
            }

            $parentOrg = __::find($actionItemList, function($item){
                return !empty($item['parent']) && $item['parent']['object_bp_type'] == 'companies';
            })['parent'];

            if ( empty($parentOrg) ){

                $parentProject = __::find($actionItemList, function($item){
                    return !empty($item['parent']) && $item['parent']['object_bp_type'] == 'groups';
                })['parent'];

                $mainContact = $this->sb->pgObjects->getById('', $parentProject['main_contact'], [
                    'name' => true
                    , 'company' => true
                    , 'id' => true
                ]);

                $parentOrg = $mainContact['company'];

            }

            if ( empty($parentOrg) ) {
                
                foreach($actionItemList as $item){

                    if (empty($parentOrg)) {

                        $tags = $this->sb->pgObjects->getById('', $item['tagged_with'], [
                            'name' => true
                            , 'object_bp_type' => true
                            , 'id' => true
                        ]);
    
                        $parentOrg = __::find($tags, function($tag){
                            return $tag['object_bp_type'] == 'companies';
                        });

                    } else { break; }

                }                
            }

            $contactList = $this->sb->pgObjects->where(
                'contacts'
                , [
                    'company' => $parentOrg['id']
                ]
                , ''
                , [
                    'name' =>       true
                    , 'id' =>       true
                    , 'parent' =>   'id'
                ]
            );

            $updatedActionItemList = array();
            foreach ( $actionItemList as $i => $item) {
                $updItem = array(
                    'id' => $item['id']
                    , 'shared_with' => __::pluck($contactList, 'id')
                );

                $output = $this->sb->pgObjects->update($item['object_bp_type'], $updItem, 0);
                array_push($updatedActionItemList, $output);
            }

            $updProject = $this->sb->pgObjects->update('groups', array('id'=>$parentOrg['id'], 'shared_with'=>$contactList));

        }

        $response->contacts = $contactList;
        $response->organization = $parentOrg;
        $response->actionItems = $updatedActionItemList;
        $response->project = $updProject;

        return $this->sb->sendData($response, 1);

    }
}

?>
