<?php

class SingleEntityView
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

    public function get($request) {

        // error_reporting(E_ALL);
		// ini_set('display_errors', '1');

        if (!empty($request->entityId) && !empty($request->object_bp_type)) {

            // Get blueprint
            $blueprint = $this->sb->pgObjects->getBlueprint($request->object_bp_type, false, true, true);

            if (!empty($blueprint)) {

                // Get entity
                $select = $this->sb->pgObjects->getSelectionFromBlueprint($blueprint['blueprint'], true);

                if (!empty($select)) {

                    $select['selectionObj'] = true;
                    $select['name'] = true;
                    $select['is_template'] = true;

                    $entity = $this->sb->pgObjects->getById('', $request->entityId, $select);

                } else {

                    $entity = $this->sb->pgObjects->getById('', $request->entityId);

                }

            }

        }

        if (!empty($blueprint) && !empty($entity)) {

            $response = array(
                'bp' => $blueprint,
                'entity' => $entity
            );
    
            return $this->sb->sendData($response, 1);

        } else {

            return http_response_code(400);

        }

    }

}

?>