<?php

class FGPortalService
{

    function __construct($sb)
    {
        $this->sb = $sb;
    }

// PUBLIC

    public function getProjectData($request)
    {

        $response = new stdClass();

        $company = json_decode(json_encode($request->company), true);
        $parse = json_decode(json_encode($request->parse), true);

        $serviceKey = '#HDIjlE';

        $where = [
            'tagged_with' => [$company]
        ];

        $select = [
            "name" => true
            , "type" => true
            , "status" => true
            , "##Action Items" => [
                "name" => true
                , "status" => true
            ]
            , "selectionObj" => true
            , $serviceKey => [
                'name' => true
                , 'id' => true
            ]
            , 'tagged_with' => [
                'id' => true
                , 'tag' => true
            ]
        ];

        $projects = $this->sb->getObjectsWhere(
            'groups'
            , $where
            , 0
            , $select
        );

        /// Remove Membership Projects by default
        $projects = __::filter($projects, function($proj) {

            if ( $proj['type']['id'] != 9234871) {
                return $proj;
            }

        });

        if ( $parse ){

            ///return all projects that aren't in a 'done' complete state
            if ( $parse['project_status'] && $parse['project_status'] == 'incomplete' ){

                $projects = __::filter($projects, function($proj) {

                    $renewalException = false;

                    if ( 
                        $proj['type']['id'] == 2318047
                        && $proj['status'] == 'done'
                    ) {

                        $incompleteActionItem = __::find($proj['##Action Items'], function($ai){
                            return $ai['status'] != 'done';
                        });

                        if ( !empty($incompleteActionItem) ){
                            $renewalException = true;
                        }
                    }

                    ///except CharSol Renewal project types (2318047) - can be done but may still have action items
                    if ( $proj['status'] != 'done' || $renewalException) {
                        return $proj;
                    }

                });
                
            }

            ///only return projects in a 'done' workflow state
            if ( $parse['project_status'] && $parse['project_status'] == 'done' ){

                $projects = __::filter($projects, function($proj) {

                    $completedProject = false;

                    if (  $proj['status'] == 'done' ) {
                        $completedProject = true;
                    }

                    if ( $proj['type']['id'] == 2318047 ) {
                        
                        $renewalException = __::find($proj['##Action Items'], function($ai){
                            return $ai['status'] != 'done';
                        });

                        if ( $renewalException ) {
                            $completedProject = false;
                        }
                    }

                    if ( $completedProject ) {
                        return $proj;
                    }

                });
                
            }

            // if ( $parse['action_items'] ){

            //     $projects = __::filter($projects, function($proj) {
        
            //         $actionNeeded = false;

            //         ///ANY incomplete Action Items, flag project as Action Needed
            //         if ( isset( $proj['##Action Items'] ) ) {
        
            //             foreach ( $proj['##Action Items'] as $i => $item) {

            //                 if ( $item['status'] != 'done'){ 
            //                     $actionNeeded = true; 
            //                 }
            //             }
        
            //         }

            //         if ($actionNeeded) {
            //             return $proj;
            //         } 
        
            //     });
                
            // }
            
        }

        foreach ($projects as &$proj) {

            // $clientService = $proj[$serviceKey][0];
            $deliverables = $this->sb->getObjectsWhere(
                ///Deliverable Set bp
                '#D3yxcG'
                , [ 
                    'parent' => $proj['#HDIjlE'][0]['id']
                ]
                , 0
                , [ 
                    'name' => true
                    , 'tagged_with' => true 
                    , 'date_created' => true 
                    ///Attachment Pointer Field
                    , '_2' => true
                    ///Intake Due Date Field
                    , '_15' => true 
                ]
            );

            $proj['#Deliverables'] = $deliverables;

            unset($proj);

        }

        ///find projects that have been tagged with a #YEAR  system_tag to prepare for sorting
        __::map($projects, function($proj, $i) use ( $response ) {

            $yearArr = array();
            ///find #YEAR tag in projects tagged with
            $yearTag = __::find( $proj['tagged_with'], function($tag){
                if ( $tag['object_bp_type'] === 'system_tags' && ctype_digit($tag['tag'])) {
                    return $tag;
                }
            });

            $yearCategory = $yearTag['tag'] ?? 'Uncategorized';

            if ( $proj['type']['id'] == 10037056) {
                $yearCategory = 'Consultation';
            }

            ///Format response obj to group by #YEAR
            if ( !property_exists($response, $yearCategory) ) {
                $response->{ $yearCategory } = array();
            }

            $yearArr = $response->{ $yearCategory };
            
            array_push( $yearArr, $proj);

            $response->{ $yearCategory } = $yearArr;

        });

        $resp_vars = get_object_vars($response);

        ksort($resp_vars);

        $response = $resp_vars;

        return $this->sb->sendData($response, 1);

    }

    public function getServiceData( $request )
    {

        $company = json_decode(json_encode($request->company), true);
        $parse = json_decode(json_encode($request->parse), true);
        //$year = json_decode(json_encode($request->year_selection), true);

        $serviceKey = '#HDIjlE';

        $where = [
            'tagged_with' => [$company]
        ];

        $years = [];
        $yearsAndAlias = [];

        // Check for the tag for all years
        $yearTags = $this->sb->getObjectsWhere(
            'system_tags'
            , []
            , ''
            , [
                'id' => true,
                'name' => true,
                'tag' => true
            ]
        );

        foreach($yearTags as $tag){
            if(strlen($tag['tag']) === 4 && is_numeric($tag['tag']) && $tag['status'] !== 'n/a') {
                $years[] = $tag['id'];
                $yearsAndAlias[] = ['name' => $tag['tag'], 'id' => $tag['id']];
            }

        }

        // query for projects to be able to check the status
        $projects = $this->sb->getObjectsWhere(
            'groups'
            , $where
            , 0
            , [
                'name' => true
                , 'type' => true
                , 'status' => true
                , $serviceKey => [
                    'name' => true
                    , 'id' => true
                    , '_14' => true
                ]
            ]
        );

        //filter by year
        $projects = __::filter($projects, function($project) use($years, $yearTags) {
            if($project['tagged_with']){

                foreach($project['tagged_with'] as $taggedW){
                    if(in_array($taggedW, $years)){
                        return true;
                    }
                }

            }
            return false;
        });

        // only working with 'done' proj Client Service records
        if ( $parse ){

            if ( $parse['project_status'] ){

                $projects = __::filter($projects, function($proj) {

                    if ( $proj['status'] == 'done') {
                        return $proj;
                    }

                });
    
                    
            }
            
        }

        $client_service_data = array();

        // get deliverables for Client Services and prepare response data to include client services instead of projs
        foreach ($projects as &$proj) {

            foreach($proj['tagged_with'] as $taggedW ){
                if(in_array($taggedW, $years)){
                    $key = array_search($taggedW, $years);
                    $proj['year'] = $yearsAndAlias[$key]['name'];
                }
            }

            // query all of the deliverable obj associated with the Project's CLIENT SERVICE record
            $deliverables = $this->sb->getObjectsWhere(
                ///Deliverable Set bp
                '#D3yxcG'
                , [ 
                    'parent' => $proj['#HDIjlE'][0]['id']
                ]
                , 0
                , [ 
                    'name' => true
                    , 'tagged_with' => true 
                    , 'date_created' => true 
                    ///Attachment Pointer Field
                    , '_2' => true
                    ///Intake Due Date Field
                    , '_15' => true 
                ]
            );

            // setting the deliverables property on the client service
            $proj['#HDIjlE'][0]['_14'] = $deliverables;


            $clientService = $proj['#HDIjlE'][0];
            $clientService['type'] = $proj['type'];
            $clientService['year'] = $proj['year'];

            // preparing for client service response instead of projects
            array_push( $client_service_data, $clientService);

            unset($proj);

        }

        $response = new stdClass();

        $allResources = [];

        foreach($client_service_data as $project){

            $currentYear = $project['year'];

            $yearObj = __::find($yearTags, function($year) use($currentYear) {
                return ($year['tag'] == $currentYear);
            });

            //not exists year for this
            if($project['year'] && !isset($allResources[$currentYear])){
                $allResources[] = [
                    'id' => $currentYear,
                    'name' => $currentYear,
                    'object_bp_type' => $yearObj['object_bp_type']
                ];
            }

            //search year
            $keyYear = null;
            foreach($allResources as $key => $year) {
                if($year['name'] == $currentYear){
                    $keyYear = $key;
                }
            }

            if(!isset($allResources[$keyYear]['projectTypes'])){
                $allResources[$keyYear]['projectTypes'][] = array_merge($project['type'], ['services' => []]);
            }

            $keyProjectType = null;
            foreach($allResources[$keyYear]['projectTypes'] as $key => $type) {
                if($type['id'] == $project['type']['id']){
                    $keyProjectType = $key;
                }
            }

            $allResources[$keyYear]['projectTypes'][$keyProjectType]['services'][]= $project;

        }

        //echo json_encode($allResources);die();

        //$response->{'resources'} = $allResources;


        return $this->sb->sendData($allResources, 1);

    }

    public function getGovernanceStucture($request)
    {  

        // Read the request as an array (instead of an object)
        $request = json_decode(json_encode($request), true);

        if ( isset($request['selected_year']) && isset($request['company']) ) {

            $governanceRecord = $this->sb->getObjectsWhere(
                '#xhnWyq'
                , array(
                    'parent' => intval($request['company']) 
                    , '_11' => intval($request['selected_year'])
                )
                , 0
                , 1
            )[0];

            if ( isset($governanceRecord) ) {

                return $this->sb->sendData(
                    $governanceRecord
                    , 1
                );

            } else {

                $prevYear = intval($request['selected_year']) - 1;

                $prevGovRecord = $this->sb->getObjectsWhere(
                    '#xhnWyq'
                    , array(
                        'parent' => intval($request['company']) 
                        , '_11' => $prevYear
                    )
                    , 0
                    , 1
                )[0];

                if ( isset($prevGovRecord) ) {

                    $governanceRecord = $this->sb->pgObjects->create(
                        '#xhnWyq'
                        , array(
                            'name' => $company['name'] . ' Board' . ' ' . $request['selected_year']
                            , 'parent' => intval($request['company'])
                            , 'tagged_with' => $company['tagged_with']
                            , 'shared_with' => __::pluck($contacts, 'id')
                            , '_11' => intval($request['selected_year'])
                            , '_12' => $prevGovRecord['_12']
                            , 'instance' => 'foundation_group'
                        )
                        , 0
                        , 1
                    );

                    return $this->sb->sendData(
                        $governanceRecord
                        , 1
                    );

                } else {

                    $company = $this->sb->getObjectsWhere(
                        'companies'
                        , array(
                            'id' => intval($request['company'])
                        )
                        , 0
                        , array(
                            'name' => true
                            , 'tagged_with' => true
                        )
                    )[0];

                    $contacts = $this->sb->getObjectsWhere(
                        'contacts'
                        , array(
                            'parent' => $company['id']
                        )
                        , 0
                        , array(
                            'name' => true
                            , 'id' => true
                        )
                    );

                    $governanceRecord = $this->sb->pgObjects->create(
                        '#xhnWyq'
                        , array(
                            'name' => $company['name'] . ' Board' . ' ' . $request['selected_year']
                            , 'parent' => intval($request['company'])
                            , 'tagged_with' => $company['tagged_with']
                            , 'shared_with' => __::pluck($contacts, 'id')
                            , '_11' => intval($request['selected_year'])
                            , 'instance' => 'foundation_group'
                        )
                        , 0
                        , 1
                    );

                    return $this->sb->sendData(
                        $governanceRecord
                        , 1
                    );
                }

            }

        } else {

            return $this->sb->sendData(
                [
                    'error' => array(
                        'header'=> 'No selected year provided'
                        , 'body' => 'Need year selection to query for governance structure record'
                    )
                ]
                , 1
            );

        }    

    }

    public function checkMembershipService($request){

        $request = json_decode(json_encode($request), true);

        $membershipProject = $this->sb->getObjectsWhere(
            'groups'
            , array(
                'parent' => intval($request['company'])
                , 'type' => 9234871
            )
            , 0
            , 1
        )[0];

        ///firstly, check to see if a memberhip project has been created. will be a manual process by FG Team using the Core Service record Batch create func.
        if ( is_null($membershipProject) ) {

            $membershipProject = false;
            $membershipRecord = false;
        ///next check to see that if there is a membership project, it is in 'Active' project workflow state (other 2 options are 'Inactive' & 'Payment Issue)
        } elseif ($membershipProject && $membershipProject['state'] !== 2) {

            $membershipRecord = false;

        /// if there is an active membership project, need to get active membership record. Membership project is 1:1 to an org, but can have many Membership records (auto renewal to create new records)
        } else {

            $memberShipRecords = $this->sb->getObjectsWhere(
                '#0ZZQgI'
                , array(
                    'parent' => intval($membershipProject['id'])
                )
                , 0
                , 1
            );

            ///find an active record that has not expired
            $membershipRecord = __::find( $memberShipRecords, function($membRec) {

                $expirationDate = new DateTime($membRec['_16']);
                $today = new DateTime();
                ///format dates to just use ymd and not use the time that is saved on the field
                $d1 = $expirationDate->format('Y-m-d');
                $d2 = $today->format('Y-m-d');

                ///if the expiry date set on the membership was before todays date, its considered expired
                if ( $d1 < $d2) {
                    return false;

                /// if the expiry date is todays date, its still valid until EOD. also if it is a future date then the memebership is active
                } elseif ( $d1 == $d2 || $d1 > $d2) {
                    return $membRec;
                }

            });

            /// If there is a membership record, verify that the 'End Date' property val is set (i.e. not empty) - FG clicked on the end date field and set an actual date
            if ( empty($membershipRecord['_16']) ) {
                $membershipRecord = false;
            }

        }

        $response = new stdClass();
        $response->{'membershipProject'} = $membershipProject;
        $response->{'membershipRecord'} = $membershipRecord;

        return $this->sb->sendData(
            $response
            , 1
        );

    }

}

?>
