<?php

//use Spatie\Async\Pool;

class DataRepository
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE

// PUBLIC

    public function get($request)
    {
        $response = new stdClass();
        if (count($request) > 0) {

            foreach ($request as $queryRequest) {

                $queryRequest = json_decode(json_encode($queryRequest), true);

                $currentResponse = null;

                if (!empty($queryRequest['api'])) {

                    if ($queryRequest['api'] == 'getObjectsWhereGrouped') {

                        $currentResponse = $this->sb->getObjectsWhereGrouped($queryRequest['table'], $queryRequest['query'], 0, $queryRequest['childObjs']);

                    } else if ($queryRequest['api'] == 'simpleSum') {

                        $currentResponse = $this->sb->simpleSum($queryRequest['table'], $queryRequest['field'], $queryRequest['query'], 0);

                    }

                } else if (!empty($queryRequest['query'])) {

                    $currentResponse = $this->sb->getObjectsWhere($queryRequest['table'], $queryRequest['query'], 0, $queryRequest['childObjs']);

                } else {
                    $currentResponse = $this->sb->getAllObjects($queryRequest['table'], $queryRequest['childObjs'], 0);

                }

                if (isset($currentResponse)) {

                    $response->{$queryRequest['responseName']} = $currentResponse;

                } else {

                    return http_response_code(500);

                }

            }


        } else {
            return http_response_code(400);
        }

        return $this->sb->sendData($response, 1);

    }


  /*  public function getAsync($request)
    {
        $pool = Pool::create();


        if (count($request) > 0) {
            foreach ($request as $queryRequest) {
                $queryRequest = (array)$queryRequest;
                $pool->add(function () use ($queryRequest) {
                    if (!is_null($queryRequest['query'])) {
                        $currentResponse = $this->sb->getObjectsWhere($queryRequest['table'], (array)$queryRequest['query'], 0, $queryRequest['childObjs']);
                    } else {
                        $currentResponse = $this->sb->getAllObjects($queryRequest['table'], 0, $queryRequest['childObjs']);
                    }
                    return array(
                        'responseName'  => $queryRequest['responseName'],
                        'response' => $currentResponse);
                })->then(function ($output) {
                    if (isset($output)) {
                        $this->response->{$output['responseName']} = $output['response'];
                    } else {
                        return http_response_code(500);
                    }

                })->catch(function (Throwable $exception) {
                    echo var_dump($exception);
                    die();
                });


            }


        } else {
            return http_response_code(400);
        }

        $pool->wait();

        return $this->sb->sendData($this->response, 1);

    }*/

    public function getEntityTypes($request)
    {
        $response = new stdClass();

        $currentResponse = $this->sb->getAllObjects('entity_type', null, 0);
        if (isset($currentResponse)) {
            $list = Array();
            $counter = 0;
            foreach($currentResponse as $value){
                $obj = $this->sb->getObjectById('entity_type', $value['id'], 0);
                $list[$counter] = $obj;
                $counter++;
            }
            $response->data = $list;

        }

        return $this->sb->sendData($response, 1);
    }

}

?>
