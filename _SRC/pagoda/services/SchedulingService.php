<?php


class SchedulingService
{

    private const EMPLOYEE_SHIFT_AVAILABLE_EMAIL = '<div>The following shift is available for pickup: </div> <div style=\"margin: 10px 0;\"></div> <div><strong>Shift Name</strong>: {shiftName} </div> <div><strong>Start Date</strong>: {startDate} </div> <div><strong>End Date</strong>: {endDate} </div> <div style=\"margin: 10px 0;\"></div> <div><strong>Description</strong>: <i>  {details} </i></div>';
    private const MANAGER_SHIFT_CHANGE_EMAIL = '<div> {userName} {actionType} the following shift: </div><div style="margin: 10px 0;"></div><div><strong>Shift Name</strong>: {shiftName} </div><div><strong>Start Date</strong>: {startDate}</div><div><strong>End Date</strong>: {endDate} </div><div style="margin: 10px 0;"></div><div><strong>Description</strong><i> {details} </i></div>';
    private const EMPLOYEE_SHIFT_CHANGE_EMAIL = '<div>{notificationIntro}</div> <div><h2>  {job_type.name} </h2></div> {scheduleName}<div><strong>Start Time</strong>: {startDate}</div> <div><strong>End Time</strong>: {endDate}</div> <div style="margin: 10px 0;"></div><div><strong>Details</strong>: <i>{details}</i></div><div style="margin: 10px 0;"></div><div><a href="{shiftLink}">View your shift here</a></div>';

    function __construct($sb)
    {
        $this->sb = $sb;
    }

// PRIVATE

    private function notifyStaffOfAvailableShift($shiftObj)
    {
        $staffToNotify = $this->sb->getObjectsWhere('users', [
            'service' => [
                'type' => 'contains',
                'value' => $shiftObj->job_type->id
            ]], 0, 0);

        $staffMemberEmails = $this->collectEmailsFromArray($staffToNotify);

        if (count($staffMemberEmails) == 0) {
            return 'no staff available for job type no emails sent';
        }

        $vars = array(
            '{shiftName}' => $shiftObj->name,
            '{startDate}' => $shiftObj->start_date,
            '{endDate}' => $shiftObj->end_date,
            '{details}' => $this->getDescriptionOrDefault($shiftObj)
        );

        return $this->sendEmail(SchedulingService::EMPLOYEE_SHIFT_AVAILABLE_EMAIL, "A Shift is Available!", $vars, $staffMemberEmails);
    }

    private function notifyManagerOfShiftChange($shiftObj, $scheduled, $userId)
    {
        $user = $this->sb->getObjectById('users', $userId, 0);
        $managerEmails = $this->collectEmailsFromArray($shiftObj->managers);

        if (count($managerEmails) == 0) {
            return 'no managers available to email';
        }

        $vars = array(
            '{userName}' => $user['name'],
            '{shiftName}' => $shiftObj->name,
            '{startDate}' => $shiftObj->start_date,
            '{actionType}' => ($scheduled ? 'scheduled' : 'unscheduled'),
            '{endDate}' => $shiftObj->end_date,
            '{details}' => $this->getDescriptionOrDefault($shiftObj)
        );

        $emailSubject = "{$vars['{userName}']} {$vars['{actionType}']} a shift";

        return $this->sendEmail(SchedulingService::MANAGER_SHIFT_CHANGE_EMAIL, $emailSubject, $vars, $managerEmails);

    }

    private function notifyStaffOfShiftChange($shiftObj, $updateStatus, $shiftLink)
    {
        //create scheduleName
        $scheduleName = '';

        if (isset($shiftObj->parent)) {
            $parentArray = (array)$shiftObj->parent;
            $scheduleName = '<div><strong>Schedule name: </strong><small>' . $parentArray['name'] . '</small></div><div style="margin: 10px 0;"></div>';
        }

        if ($shiftObj->status == 'Notified') {
            $emailSubject = 'Your shift has been updated';
            $notificationIntro = '<div>The following shift has been updated:</div>';
        } else {
            $notificationType = (isset($updateStatus) && $updateStatus == 'Unscheduled') ? 'unscheduled' : 'scheduled';
            $emailSubject = 'Your shift has been ' . $notificationType;
            $notificationIntro = '<div>You have been <strong>' . $notificationType . '</strong> for the following shift:</div>';
        }

        $staffMemberEmail = $this->collectEmailsFromArray(array($shiftObj->user));

        if (count($staffMemberEmail) == 0) {
            return null;
        }

        $vars = array(
            '{notificationIntro}' => $notificationIntro,
            '{job_type.name}' => $shiftObj->job_type->name,
            '{scheduleName}' => $scheduleName,
            '{startDate}' => $shiftObj->start_date,
            '{endDate}' => $shiftObj->end_date,
            '{details}' => $this->getDescriptionOrDefault($shiftObj),
            '{shiftLink}' => $shiftLink
        );

        $this->sendEmail(SchedulingService::EMPLOYEE_SHIFT_CHANGE_EMAIL, $emailSubject, $vars, $staffMemberEmail);


        if (isset($updateStatus) && $updateStatus == 'Unscheduled') {
            $shiftObj->user = 0;
            $this->sb->pgObjects->update('groups', array(
                'id'=> $shiftObj->id,
                'user'=> 0
            ));
        }else{
            $shiftObj->status = 'Notified';
            $this->sb->pgObjects->update('groups', array(
                'id'=> $shiftObj->id,
                'status'=>'Notified'
            ));
        }

        return $this->sb->sendData($shiftObj,1);

    }

    /**
     * @param $shiftObj - shift object to check if a description is present
     * @return string - Value of shift Object description or default value
     */
    private function getDescriptionOrDefault($shiftObj): string
    {
        return (empty($shiftObj->description) ? "No Description Provided" : $shiftObj->description);
    }

    /**
     * @param array $userArray - Array of users to collect email from
     * @return array - Validated and sanitized emails
     */
    private function collectEmailsFromArray(array $userArray): array
    {
        $emailArray = array();
        foreach ($userArray as $currentUser) {
            $currentUserArray = (array)$currentUser;
            if (filter_var($currentUserArray['email'], FILTER_VALIDATE_EMAIL)) {
                array_push($emailArray, $currentUserArray['email']);
            }
        }
        return $emailArray;
    }

    /**
     * @param string $emailTemplate - templated string for email generation
     * @param string $emailSubject - Subject for Email
     * @param array $templateVars - Variables to fill into the string
     * @param array $emailArray - Array of users to send email too
     */
    private function sendEmail(string $emailTemplate, string $emailSubject, array $templateVars, array $emailArray): void
    {
        $emailBody = strtr($emailTemplate, $templateVars);
        $mergevars = array(
            'BODY' => $emailBody,
            'INSTANCE_NAME' => $this->sb->appConfig['instance']
        );

        $this->sb->sendEmail($emailArray, $this->sb->appConfig['emailFrom'], $emailSubject, $mergevars, "SCHEDULE_NOTIFICATION", false);
    }


// PUBLIC

    public function notifyOnScheduleChange($request)
    {

        switch ($request->notificationType) {
            case "STAFF_SHIFT_AVAILABLE":
                echo $this->notifyStaffOfAvailableShift($request->shiftObj);
                break;
            case "MANAGER_SHIFT_CHANGE":
                echo $this->notifyManagerOfShiftChange($request->shiftObj, $request->scheduling, $request->userRequested);
                break;
            case "STAFF_SHIFT_CHANGE":
                echo $this->notifyStaffOfShiftChange($request->shiftObj, $request->updateStatus, $request->shiftLink);
                break;
            default:
                throw new InvalidArgumentException('User Type passed is not valid : ' . notificationType);

        }
    }


}

?>