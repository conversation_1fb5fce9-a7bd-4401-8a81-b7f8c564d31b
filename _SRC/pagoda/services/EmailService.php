<?php

class EmailService
{

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE

// PUBLIC

    public function resendEmail($request)
    {
        // error_reporting(E_ALL);
		// ini_set('display_errors', '1');
        // Request structure
        // $request :: [
        //     'emailId' => int
        // ];
        
        $emailId = $request->emailId;
        if (!$emailId) {
            return $this->sb->sendData(false, 1);
        }

        $email = $this->sb->pgObjects->getById(
            'emails'
            , $emailId
        );

        $instanceName = $this->sb->appConfig['instance'];
		if (!empty($this->sb->appConfig['systemName'])) {
			$instanceName = $this->sb->appConfig['systemName'];
		}

        $link = 'https://bento.infinityhospitality.net/';
        $mergevars = [
            'TITLE' => 			$email['subject'],
            'BODY' => 			$email['message'],
            'BUTTON' => 		'View in Bento',
            'BUTTON_LINK' => 	$link,
            'INSTANCE_NAME' => 	$instanceName
        ];

        $sendTo = explode(',', $email['to']);
        $resentEmail = $this->sb->comm->sendMandrillEmail(
			$sendTo, 
			$email['from'],
			$email['subject'], 
			$mergevars, 
			$email_tags, 
			$email['type_id'], 
			1,
			0,
			false,
			null,
			$email['instance'],
			0,
			$email['tagged_with']
		);
        

        $response = [];
        return $this->sb->sendData($response, 1);

    }

}
