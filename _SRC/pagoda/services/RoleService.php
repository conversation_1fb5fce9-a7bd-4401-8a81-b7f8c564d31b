<?php

class RoleService
{

    function __construct($sb)
    {
        $this->sb = $sb;
    }

// PUBLIC

    public function updateRole($request)
    {
        $roleRequested = $this->sb->getObjectsWhere('role_groups',
            array('role_type' => $request->roleType), 0, 0);

        if (count($roleRequested) == 0) {
            $updatedObject = $this->sb->createNewObject('role_groups', array('role_type' => $request->roleType,
                'require_approval' => $request->requireApproval,
                'approval_users' => $request->users,
                'approval_jobs' => $request->services), 0);
            return $this->sb->sendData(array('status' => "success", 'message' => 'Role Created', 'updatedObject' => $updatedObject), 1);

        } else {
            $shouldUpdate = false;
            $roleToUpdate = $roleRequested['0'];
            $roleToUpdate['require_approval'] = $this->updateProperty($roleToUpdate['require_approval'], $request->requireApproval, $shouldUpdate);
            $roleToUpdate['approval_users'] = $this->updateProperty($roleToUpdate['approval_users'], $request->users, $shouldUpdate);;
            $roleToUpdate['approval_jobs'] = $this->updateProperty($roleToUpdate['approval_jobs'], $request->services, $shouldUpdate);;


            if ($shouldUpdate) {
                $updatedObject = $this->sb->updateObject($roleToUpdate, 'role_groups', 0, false);
                if (isset($updatedObject)) {
                    return $this->sb->sendData(array('status' => "success", 'message' => 'Role Updated', 'updatedObject' => $updatedObject), 1);
                } else {
                    return $this->sb->sendData(array('status' => "error", 'message' => 'Update Unsuccessful', 'updatedObject' => $updatedObject), 1);
                }
            } else {
                return $this->sb->sendData(array('status' => "error", 'message' => 'There were no changes to this object', 'updatedObject' => null), 1);
            }
        }
    }

    public function validateUserPermissions($user, $roleName): bool
    {
        $roleGroup = $this->sb->getObjectsWhere('role_groups',
            array('role_type' => $roleName), 0, 1)[0];

        $approvalJobTypes = __::pluck($roleGroup['approval_jobs'], 'job_type');
        $approvalUsers = __::pluck($roleGroup['approval_users'], 'id');

        $userInService =
            __::any($approvalJobTypes, function ($value) use ($user) {
                return __::contains($user['service'], $value);
            });

        $userInGroup=__::contains($approvalUsers, $user['id']);

        return ($userInGroup || $userInService);
    }

    public function routeValidateUserPermissionsRequest($request)
    {
        $requestUser = $request->user;
        $roleName = $request->roleName;
        $user = $this->sb->getObjectById('users', $requestUser, 0);
        return $this->sb->sendData($this->validateUserPermissions($user,$roleName), 1);
    }

    public function getAllRoleUserByUniqueEmail($roleName): ?array
    {
        $allRoleUsers = $this->getAllRoleUsers($roleName);
        if (count($allRoleUsers) > 0) {
            $uniqueEmails = array_unique(__::pluck($allRoleUsers, 'email'));

            $contactInfo = array();
            foreach ($uniqueEmails as $uniqueEmail) {
                $currentUser = __::find($allRoleUsers, function ($value) use ($uniqueEmail) {
                    return $uniqueEmail == $value['email'];
                });

                if (isset($currentUser)) {
                    $currentContact = array('name' => $currentUser['name'],
                        'fname' => $currentUser['fname'],
                        'email' => $currentUser['email'],
                        'lname' => $currentUser['lname']);
                    array_push($contactInfo, $currentContact);
                }
            }

            return $contactInfo;
        } else {
            return null;
        }
    }

// PRIVATE

    private function getAllRoleUsers($roleName)
    {
        $roleGroup = $this->sb->getObjectsWhere('role_groups',
            array('role_type' => $roleName), 0, 1);

        $approvalJobs = ($roleGroup['0']['approval_jobs'] ?? array());
        $roleUsers = ($roleGroup['0']['approval_users'] ?? array());


        $staffToNotify = $this->sb->getObjectsWhere('users', [
            'service' => [
                'type' => 'contains',
                'value' => __::pluck($approvalJobs, 'job_type')
            ]], 0, 0);

        if (count($staffToNotify) > 0) {
            return $this->unique_multidim_array(array_merge($roleUsers, $staffToNotify), 'id');
        } else {
            return $roleUsers;
        }

    }

//UTILS

    private function updateProperty($currentValue, $newValue, &$shouldUpdate)
    {
        if (isset($newValue) && $currentValue != $newValue) {
            $shouldUpdate = true;
            return $newValue;
        } else {
            return $currentValue;
        }

    }


    private function unique_multidim_array($array, $key): array
    {
        $temp_array = array();
        $i = 0;
        $key_array = array();

        foreach ($array as $val) {
            if (!in_array($val[$key], $key_array)) {
                $key_array[$i] = $val[$key];
                $temp_array[$i] = $val;
            }
            $i++;
        }
        return $temp_array;
    }

}

?>