<?php

class FGProjectsByRole
{

    function __construct($sb)
    {
        $this->sb = $sb;
    }

// PUBLIC

    private function contains($word, $inText){
        $patt = "/(?:^|[^a-zA-Z])" . preg_quote($word, '/') . "(?:$|[^a-zA-Z])/i";
        return preg_match($patt, $inText);
    }

    private function hasSomeRole($roles, $value){
        foreach($roles as $role) {
            if($value && $this->contains($role, $value)){
                return true;
            }
        }
        return false;
    }

    public function getCollectionData($request)
    {
        $roleSheetKey = '#ReU6uX';
        $serviceKey = '#HDIjlE';
        $userToSearch = $_COOKIE['uid'];


        $role = $request->queryObj->role;
        if($role){
            unset($request->queryObj->role);
        }

        $where = json_decode(json_encode($request->queryObj), true);
        $select = json_decode(json_encode($request->getChildObjs), true);

        $rolesToSearch = ['admin', 'reviewer', 'specialist', 'technician', 'irs_liaison'];

        if($role){
            $rolesToSearch = [$role];
        }

        $projects = $this->sb->getObjectsWhere(
            'groups'
            , $where
            , 0
            , $select
        );

        // Fiscal year end date (from the core demo on the company)
        $companyIds = []; // !Might not need this anymore
        $projectIds = [];
        $roleSheetBlueprints = [];

        foreach ($projects['data'] as $i => $project) {

            array_push($projectIds, $project['id']);

            if (
                is_array($project)
                && is_array($project['main_contact'])
                && is_array($project['main_contact']['company'])
            ) {

                array_push($companyIds, $projects['main_contact']['company']);

            }

        }

        // Date Intake Complete
        $logs = $this->sb->pgObjects->where(
            'notes'
            , [
                'type_id' => [
                    'type' => 'or'
                    , 'values' => $projectIds
                ]
                , 'note' => [
                    'type' => 'contains'
                    , 'value' => 'Changed from <strong>Intake</strong>'
                ]
            ]
            , ''
            , [
                'log_data' => true
                , 'note' => true
                , 'type_id' => 'id'
                , 'date_created' => true
            ]
        );
        $logs = __::sortBy($logs, function($log){

            return strtotime($log['date_created']);

        });

        // Get the associated Role Sheet records (user values)
        $roleSheetFields = $this->sb->pgObjects->where(
            $roleSheetKey,
            []
        );

        $roleSheets = $this->sb->pgObjects->where(
            $roleSheetKey
            , [
                'parent' => [
                    'type' => 'or'
                    , 'values' => $projectIds
                ]
            ]
            , ''
            , [
                'name' =>       true
                , 'parent' =>   'id',
                'object_bp_type' => true
            ]
        );

        // Get the associated Services records
        $services = $this->sb->pgObjects->where(
            $serviceKey
            , [
                'parent' => [
                    'type' => 'or'
                    , 'values' => $projectIds
                ]
            ]
            , ''
            , [
                'name' =>       true
                , 'parent' =>   'id'
                , '_9' =>       true // Due Date
                , '_16' =>      true // Fiscal Year End Date
            ]
        );

        foreach($roleSheets as $roleSheet){
            if(!in_array($roleSheet['object_bp_type'], $roleSheetBlueprints)){
                $roleSheetBlueprints[] = substr( $roleSheet['object_bp_type'], 1);
            }
        }

        //get the rolesheet blueprint field names (for union with rolesheet file values)
        $roleSheetWithFieldName = $this->sb->pgObjects->where('entity_type', [
            'bp_name' => [
                'type' => 'or'
                , 'values' => $roleSheetBlueprints
            ]
        ]);

        $roleSheets = $this->sb->pgObjects->where(
            $roleSheetKey
            , [
                'parent' => [
                    'type' => 'or'
                    , 'values' => $projectIds
                ]
            ]
            , ''
            , [
                'name' =>       true
                , 'parent' =>   'id'
            ]
        );

        // Merge in data from related objs
        foreach ($projects['data'] as $i => &$project) {

            $service = false;
            $service = __::find(
                $services
                , function ($service) use ($project) {
                return $project['id'] === $service['parent'];
            }
            );

            if (
                is_array($service)
                && is_string($service['_9'])
            ) {
                $projects['data'][$i]['dueDate'] = $service['_9'];
                $projects['data'][$i]['fiscalYearEndDate'] = $service['_16'];
            }


            // Assigned from the Role Sheet obj
            $roleSheet = false;
            $roleSheet = __::find(
                $roleSheets
                , function ($roleSheet) use ($project) {
                return $project['id'] === $roleSheet['parent'];
            }
            );

            if (
            is_array($roleSheet)
            ) {

                $fields = __::find(
                    $roleSheetFields
                    , function ($field) use ($roleSheet) {
                    return $roleSheet['id'] === $field['id'];
                }
                );

                $rolesheet_fieldname = __::find(
                    $roleSheetWithFieldName
                    , function ($field) use ($roleSheet) {
                    return $roleSheet['object_bp_type'] === '#'  . $field['bp_name'];
                }
                );

                //flag for remove the project if not has assigned in any role
                $removed = true;

                foreach($rolesheet_fieldname["blueprint"] as $k => $v) {
                    $name = strtolower($v['name']);
                    if (($this->hasSomeRole($rolesToSearch,$name)) && $v['fieldType'] == "user") {
                        if ((array_key_exists($k, $fields)) && ($fields[$k] == $userToSearch)) {
                            $removed = false;
                            break;
                        }
                    }
                }

                if($removed){
                    unset($projects['data'][$i]);
                } else {
                    $projects['data'][$i]['parent'] = $roleSheet;
                }

            } else {
                unset($projects['data'][$i]);
            }

            // Intake Completed (from out of 'Intake' state change logs)
            if (is_array($logs)) {
                foreach ($logs as $log) {

                    if (
                        $log['type_id'] === $project['id']
                        && preg_match("/\Changed from Intake to \b/", strip_tags($log['note']))
                    ) {

                        $projects['data'][$i]['dateIntakeComplete'] = $log['date_created'];
                        $projects['data'][$i]['note'] = $log['note'];

                    } elseif (
                        $log['type_id'] === $project['id']
                        && (
                            preg_match("/\ to Intake by \b/", strip_tags($log['note']))
                            || preg_match("/\ to Intake  by \b/", strip_tags($log['note']))
                            || preg_match("/\ to More Info Requested by \b/", strip_tags($log['note']))
                            || preg_match("/\ to More Info Requested  by \b/", strip_tags($log['note']))
                        )
                        && strtotime($projects['data'][$i]['dateIntakeComplete']) !== false
                        && strtotime($log['date_created']) > strtotime($projects['data'][$i]['dateIntakeComplete'])
                    ) {

                        unset($projects['data'][$i]['dateIntakeComplete']);
                        unset($projects['data'][$i]['note']);

                    }

                }
            }

        }

        return $this->sb->sendData($projects, 1);

    }

}

?>
