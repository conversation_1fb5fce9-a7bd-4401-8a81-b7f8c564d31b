<?php

class ProjectService {
	// declaring const for venue fee
	const VENUE_FEE_CATEGORY_ID = 1709932;

    function __construct($sb)
    {
        $this->sb = $sb;
    }

    public function subtractTaxesFromProjectInvoice($projects) {

        $modifiedProjects = array();
        
        foreach($projects as $project){
	        
	        $invoice_value = $project->invoice_value;
	        $invoice_value_no_taxes = $invoice_value;
	        
			if (
				$invoice_value > 0
			) {
				
				$projectBreakdown = $this->sb->getObjectsWhere('inventory_menu_pricing_breakdown', array('space' => $project->id), 0, 1);
								
				foreach ($projectBreakdown[0]['breakdown']['taxes'] as $key => $value) {
	            
	                $invoice_value_no_taxes -=  $value;
	                
	            }
	
	            $project->invoice_value_no_taxes = $invoice_value_no_taxes;
				// Get Venue
				$venue = $project->locations[0] !== null ? $this->sb->getObjectById('location', $project->locations[0], 0) : null;
				// Venue Name
				$project->venue_name = $venue["name"] ?? "N/A";
				// Venue Fee
				$project->venue_fee = $projectBreakdown[0]["breakdown"]["categories"][self::VENUE_FEE_CATEGORY_ID] ?? "N/A";
	            
	            $this->sb->pgObjects->update('groups', array(
				    'id'=> $project->id,
				    'invoice_value_no_taxes'=> $invoice_value_no_taxes
				));
				
			}
			
            array_push($modifiedProjects, $project);

        }

        return $this->sb->sendData($modifiedProjects, 1);
		
    }
    
    public function getPaymentsTotal($projects) {
	    
	    $modifiedProjects = array();

	    foreach($projects as $project) {
		    
		    $project->totalPayments = 0;
		    
		    if (property_exists($project->proposal, 'id')) {
			    
			    $payments = $this->sb->getObjectsWhere('payments', array('main_object' => $project->proposal->id), 0, 1);
				
				foreach($payments as $payment) {

			    	$project->totalPayments += $payment['amount'];
			    
				}
				
				array_push($modifiedProjects, $project);

		    }
		    
	    }
	    
	    return $this->sb->sendData($modifiedProjects, 1);
	    
    }

	public function getProjectsForReports($request)
	{

		$queryRequest = json_decode(json_encode($request), true);
		$data = $queryRequest['data'];
		$paged = $data['paged'];
		$childObjs = $data['childObjs'];
		$invoicesPaged = $data['invoicesPaged'];
		$paymentsPaged = $data['paymentsPaged'];
		$dateRange = $data['date_created'];
		$groups = [];
		
		// get group objects:
		if ($dateRange == null) {

			$groups = $this->sb->getObjectsWhere('groups', array('paged'=>$paged, 'group_type'=>'Project', 'is_template'=>0), 0, $childObjs);

		} else {

			$groups = $this->sb->getObjectsWhere('groups', array('paged'=>$paged, 'group_type'=>'Project', 'is_template'=>0, 'start_date'=>$dateRange), 0, $childObjs);

		}

		// get proposal id array
		$projectIds = [];
		foreach ($groups["data"] as $group) {
			array_push($projectIds, $group["proposal"]["id"]);
		}

		// get array of associated invoices - proposal objects seem to usually have an empty array for the 'invoices' property
		$invoices = $this->sb->getObjectsWhere('invoices', array('paged'=>$invoicesPaged, 'related_object' => array('type' => 'or', 'values' => $projectIds)), 0, 0)["data"];
		$invoiceIds = __::pluck($invoices, 'id');

		// get array of associated payments - invoice objects seem to usually have an empty array for 'payments' property
		$payments = $this->sb->getObjectsWhere('payments', array('paged'=>$paymentsPaged, 'invoice' => array('type' => 'or', 'values' => $invoiceIds)), 0, 0)["data"];

		// combine payment information into payments array
		foreach ($payments as $p) {

			$invoiceId = $p['invoice'];

			foreach ($invoices as $key=>$i) {

				if ($i['id'] == $invoiceId) {

					$payments = $i['payments'];
					array_push($payments, $p);
					$invoices[$key]['payments'] = $payments;

				}
			}
		}

		// combine invoice information into groups array
		foreach ($invoices as $i) {

			$proposalId = $i['related_object'];

			foreach ($groups['data'] as $key=>$g) {

				if ($g['proposal']['id'] == $proposalId) {

					$invoices = $g['proposal']['invoices'];
					array_push($invoices, $i);
					$groups['data'][$key]['proposal']['invoices'] = $invoices;

				}
			}
		}

		return $this->sb->sendData($groups, 1);
		
	}

	public function getDataForPaymetsByProejctReport($request){

		$queryRequest = json_decode(json_encode($request), true);
		$data = $queryRequest['data'];
		$paged = $data["paged"];
        $sortCol = $paged["sortCol"];
        $sortDir = $paged["sortDir"];
        $pageLength = $paged['pageLength'];
        $page = $paged['page'];
        $IndexStart = $page;
		$childObjs = [
			"id" => true
			,"object_uid" => true
			,"start_date" => true
			,"name" => true
			,"locations" =>  true
			,"invoice_value" => true
			,"proposal" => true
		];

		$dateRange = $data['date_created'];
		$groups = [];
		$where = [
			'group_type' => 'Project'
			, 'is_template' => 0
			, 'type' => 1625566 // event management workflow
			, 'state' => [
				'type' => 'not_equal'
				, 'value' => 10 // all states but cancelled
			]
		];
		$returnObj = [
            "draw" => true
            , "recordsFiltered" => $pageLength
            , "recordsTotal" => 0
            , "data" => []
        ];

		if ($dateRange != null) {
            $where["start_date"] = $dateRange;
        }

		// get groups
		$groups = $this->sb->getObjectsWhere('groups', $where, 0, $childObjs);
		$returnObj["recordsTotal"] = count($groups);
		$selectedGroups = array_slice($groups, $IndexStart, $pageLength);

		// sort events
		if ($sortCol === "start_date") {

            if ($sortDir === "asc") {

				usort($selectedGroups,function($a, $b){

                    return strtotime($a["start_date"]) - strtotime($b["start_date"]);
                });

            } else {

				usort($selectedGroups,function($a, $b){

                    return strtotime($b["start_date"]) - strtotime($a["start_date"]);
                });

            }

        } else if ($sortCol === "invoice_value") {

            if ($sortDir === "asc") {

                usort($selectedGroups,function($a, $b){

                    return $a["invoice_value"] > $b["invoice_value"];
                });

            } else {

                usort($selectedGroups,function($a, $b){

                    return $a["invoice_value"] < $b["invoice_value"];
                });
                
            }

        }

		// get proposal id array
		$proposalIds = [];
		foreach ($selectedGroups as $group) {
			array_push($proposalIds, $group["proposal"]["id"]);
		}

		// get payments associated with proposalId
		$payments = $this->sb->getObjectsWhere('payments', array('main_object' => array('type' => 'or', 'values' => $proposalIds)), 0, 0);

		// combine arrays:
		foreach ($selectedGroups as $key=>$group) {

			$eventDateUTC = new DateTime($selectedGroups[$key]['start_date'], new DateTimeZone('UTC'));
			$formattedDate = $eventDateUTC->format("Y/m/d");
			$selectedGroups[$key]["start_date"] = $formattedDate;
			$selectedGroups[$key]["paid"] = 0;
			$selectedGroups[$key]["feesPaid"] = 0;
			$matchingPaymentKeys = array_keys(array_column($payments, 'main_object'), $group["proposal"]["id"]);
			
			foreach ($matchingPaymentKeys as $index) {
				$selectedGroups[$key]["paid"] += $payments[$index]["amount"];
				$selectedGroups[$key]["feesPaid"] += $payments[$index]["fee"];

			}
		}

		$returnObj["data"] = $selectedGroups;

		return $this->sb->sendData($returnObj, 1);
	}

	public function addClientService($request){

        $queryRequest = json_decode(json_encode($request), true);
		$data = $queryRequest['data'];
		$paged = $data["paged"];
        $sortCol = $paged["sortCol"];
        $sortDir = $paged["sortDir"];
        $pageLength = $paged['pageLength'];
        $page = $paged['page'];
        $IndexStart = $page;

		$childObjs = $data['childObjs'];


        $childObjs['#HDIjlE'] = true;

        $childObjs = array_merge($childObjs, $data['childObjs']);

		$dateRange = $data['date_created'];
		$groups = [];
		$where = [
			'group_type' => 'Project'
			, 'is_template' => 0
		];
		$returnObj = [
            "draw" => true
            , "recordsFiltered" => $pageLength
            , "recordsTotal" => 0
            , "data" => []
        ];

		if ($dateRange != null) {
            $where["start_date"] = $dateRange;
        }

        $childObjs['type'] = array(
            'name'=> true
            , 'states'=> true
        );

		// get groups
		$groups = $this->sb->getObjectsWhere('groups', $where, 0, $childObjs);

        $statusToMatch = ['Intake', 'WIP', 'More Info Requested'];

        function findByUid($states, $searchUid) 
        {

            foreach ($states as $st) {
                if ($st['uid'] === $searchUid) {
                    return $st;
                }
            }

            return null;
        }

        $statusWhiteList = ['Intake', 'More Info Requested','WIP'];
        $whitelistedGroups = array();

        foreach($groups as &$g){

            $possibleStates = $g['type']['states'];

            $currentState = $g['state'];

            // Use the function
            $foundState = findByUid($possibleStates, $currentState);

            if ($foundState !== null) {

                if ( in_array($foundState['name'], $statusWhiteList) ) {
                    array_push($whitelistedGroups, $g);
                }

            }

            unset($g);

        };

		$returnObj["recordsTotal"] = count($whitelistedGroups);

		$whitelistedGroups = array_slice($whitelistedGroups, $IndexStart, $pageLength);

		$returnObj["data"] = $whitelistedGroups;

        return $this->sb->sendData($returnObj, 1);
	}
}

?>