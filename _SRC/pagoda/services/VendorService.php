<?php
require_once("RoleService.php");

class VendorService
{
    private const VENDOR_PENDING_APPROVAL = '<div>Hello {approverName}, </div> <div>The following vendor is pending approval: </div> <div style=\"margin: 10px 0;\"></div> <div><strong>Name</strong>: {itemName} </div> <div><strong>Description</strong>: {details} </div> <div><strong>Requested By</strong>: {userName} </div> ';
    private const VENDOR_STATUS_CHANGED = '<div>Hello {vendorCreator}, </div> <div>The following vendor request has been changed to {approvalStatus}: </div> <div style=\"margin: 10px 0;\"></div> <div><strong>Name</strong>: {itemName} </div> <div><strong>Description</strong>: {details} </div> <div><strong>Notes</strong>: {requestNotes} </div> <div><strong>Reviewed By</strong>: {approvalUser} </div> ';


    function __construct($sb)
    {
        $this->sb = $sb;
        $this->roleService = new RoleService($sb);
    }
    

    public function notifyApproversOfPendingVendor($request)
    {
        $approverEmails = $this->roleService->getAllRoleUserByUniqueEmail("VENDOR_APPROVER");
        $user = $this->sb->getObjectById('users', $request['created_by'], 0);

        if (is_null($approverEmails)) {
            return;
        }
        foreach ($approverEmails as $approver) {
            $vars = array(
                '{approverName}' => $approver['name'],
                '{userName}' => $user['name'],
                '{itemName}' => $request['item']['name'],
                '{details}' => $request['item']['description']
            );

            //$this->sendEmail(VendorService::VENDOR_PENDING_APPROVAL, 'Vendor Pending Approval', $vars, array($approver['email']));

        }
    }

    public function changeStatusOfVendor($request)
    {
        $request = (array) $request;
        $approvalUser = $this->sb->getObjectById('users', $request['user'], 0);
        $newStatus = $request['requestedStatus'];
        $changeStatusPermission = $this->roleService->validateUserPermissions($approvalUser, "VENDOR_APPROVER");
        $requestUser = $this->sb->getObjectById('users', $request['requestUser'], 0);

        if ($changeStatusPermission) {
            $updatedVendorLineItem = $this->sb->pgObjects->update('inventory_menu_line_item', array(
                'id' => $request['vendorId'],
                'vendor_approval_status' => $newStatus
            ));
            if (isset($updatedVendorLineItem)) {
                $vars = array(
                    '{vendorCreator}' => $requestUser['name'],
                    '{approvalStatus}' => $updatedVendorLineItem['vendor_approval_status'],
                    '{details}' => $updatedVendorLineItem['item']['description'],
                    '{itemName}' => $updatedVendorLineItem['item']['name'],
                    '{requestNotes}' => $request['notes'],
                    '{approvalUser}' => $approvalUser['name']
                );
                //$this->sendEmail(VendorService::VENDOR_STATUS_CHANGED, 'Vendor Request ' . $newStatus, $vars, array(requestUser['email']));
                return $this->sb->sendData(array('status' => "success", 'message' => 'Vendor line item updated', 'updatedObject' => $updatedVendorLineItem), 1);
            } else {
                return $this->sb->sendData(array('status' => "error", 'message' => "Error updating vendor line item", 'updatedObject' => $updatedVendorLineItem), 1);
            }
        } else {
            return $this->sb->sendData(array('status' => "error", 'message' => "You don't have permissions to perform this action", 'updatedObject' => null), 1);
        }
    }


    private function sendEmail(string $emailTemplate, string $emailSubject, array $templateVars, array $emailArray): void
    {
        $emailBody = strtr($emailTemplate, $templateVars);
        $mergevars = array(
            'BODY' => $emailBody,
            'INSTANCE_NAME' => $this->sb->appConfig['instance']
        );

        $this->sb->sendEmail($emailArray, $this->sb->appConfig['emailFrom'], $emailSubject, $mergevars, "SCHEDULE_NOTIFICATION", false);
    }


}

?>