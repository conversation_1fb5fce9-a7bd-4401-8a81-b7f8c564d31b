<?php

class GroupByAgingService
{
    private const agingBuckets = ['zeroThirty', 'thirtySixty', 'sixtyNinety', 'ninetyOnePlus'];

    function __construct($sb)
    {
        $this->sb = $sb;
        $this->response= new stdClass();
    }

// PRIVATE

    private function initBalanceBuckets() {

        return $buckets = array(
            'zeroThirty' => [
                'balance' => 0,
                'count' => 0,
            ],
            'thirtySixty' => [
                'balance' => 0,
                'count' => 0,
            ],
            'sixtyNinety' => [
                'balance' => 0,
                'count' => 0,
            ],
            'ninetyOnePlus' => [
                'balance' => 0,
                'count' => 0,
            ],
        );

    }

    private function sumBuckets($buckets) {

        $total = 0;
        $count = 0;

        $bucket_names = GroupByAgingService::agingBuckets;

        foreach ($bucket_names as $i => $name) {
            $bucket = $buckets[$name];
            if (isset($bucket)) {
                if (isset($bucket['balance']) && $bucket['balance'] > 0) {
                    $total = $total + $bucket['balance'];
                };
                if (isset($bucket['count']) && $bucket['count'] > 0) {
                    $count = $count + $bucket['count'];
                }; 
            }
        }

        return array(
            'balance' => $total,
            'count' => $count
        );

    }

    private function getFormattedAddToDate($interval, $direction, $format) {

        $now = new DateTime();

        if ($direction === 'sub') {
            $now->sub(new DateInterval($interval));
        } else if ($direction === 'add') {
            $now->add(new DateInterval($interval));
        }

        if ($format === '') {

            return $now;

        } else {

            return $now->format($format);

        }

    }

    private function getDateRanges() {
        
        // $sqlFormat = 'Y-m-d 0:0:0';

        return array(
            'ninetyOnePlus' => [
                'start' => $this->getFormattedAddToDate('P900D', 'sub', ''),
                'end'   => $this->getFormattedAddToDate('P90D', 'sub', '')
            ],
            'sixtyNinety' => [
                'start' => $this->getFormattedAddToDate('P90D', 'sub', ''),
                'end'   => $this->getFormattedAddToDate('P60D', 'sub', '')
            ],
            'thirtySixty' => [
                'start' => $this->getFormattedAddToDate('P60D', 'sub', ''),
                'end'   => $this->getFormattedAddToDate('P30D', 'sub', '')
            ],
            'zeroThirty' => [
                'start' => $this->getFormattedAddToDate('P30D', 'sub', ''),
                'end'   => $this->getFormattedAddToDate('P0D', 'sub', '')
            ],
        );

    }

    private function getInvoiceSetup() {
        return [
            'amount' => true,
            'balance' => true,
            'due_date' => true,
            'id' => true,
            'main_contact' => true,
            'payments' => [
                'id' => true,
                'amount' => true,
                'fee' => true,
                'details' => [
                    'payment_date' => true
                ],
                'checkNumber' => true,
                'stripe_payment_id' => true,
            ],
            'state' => true,
            'related_object' => [
                'id' => true,
                'name' => true,
                'main_object' => [
                    'id' => true,
                    'name' => true,
                    'object_uid' => true,
                    'state' => true,
                    'type' => true
                ]
            ],
        ];
    }

    private function getAllProposalsInvoicesArrayInRange($start, $end, $array) {

        $invoicesSetup = $this->getInvoiceSetup();
        
        $invoices = $this->sb->getObjectsWhere(
            'invoices', 
            [
                'related_object' => [
                    'type' => 'or',
                    'values' => $array,
                    'and' => [
                        'balance' => [
                            'type' => 'greater_than',
                            'value' => 0,
                        ]
                    ],
                    'and' => [
                        'due_date' => [
                            'type' => 'between',
                            'start' => $start->format('U'),
                            'end' => $end->format('U'),
                        ]
                    ]
                ]
            ], 
            0,
            $invoicesSetup
        );

        return $invoices;

    }

    private function getAllInvoicesArrayInRange($start, $end, $array) {

        $invoicesSetup = $this->getInvoiceSetup();

        $invoices = $this->sb->getObjectsWhere(
            'invoices', 
            [
                'balance' => [
                    'type' => 'greater_than',
                    'value' => 0
                ], 
                'due_date' => array(
                    'type' => 'between',
                    'start' => $start->format('U'),
                    'end' => $end->format('U')
                ),
                'id' => [
                    'type' => 'any',
                    'values' => $array
                ],
            ], 
            0,
            $invoicesSetup
        );

        return $invoices;

    }

    private function getAllInvoicesInRange($start, $end) {

        $invoicesSetup = $this->getInvoiceSetup();

        $invoices = $this->sb->getObjectsWhere(
            'invoices', 
            [
                'balance' => [
                    'type' => 'greater_than',
                    'value' => 0
                ], 
                'due_date' => array(
                    'type' => 'between',
                    'start' => $start->format('U'),
                    'end' => $end->format('U')
                ),
            ], 
            0,
            $invoicesSetup
        );

        return $invoices;

        // 'related_object.status' => [
        //     'type' => 'or',
        //     'values' => ['Booked', 'Assigned', 'Paused - Waiting for Payment']
        // ],
        // 'is_deleted' => false,

    }

// PUBLIC

    public function getAgingAccounts($request) {
        
        $response = new stdClass();

        if (count($request) > 0) {

            $queryRequest = json_decode(json_encode($request), true);

            $type = $queryRequest['type'];

            if ($type === 'invoices') {

                $projects = array();

                $dateRanges = $this->getDateRanges();

                foreach ($dateRanges as $i => $range) {

                    $invoicesToSort = $queryRequest['invoicesData'];
                    
                    $subType = $queryRequest['subType'];

                    if ($subType === 'proposalsArray') {

                        $proposalsArray = $queryRequest['proposalsArray'];

                        $invoicesToSort = $this->getAllProposalsInvoicesArrayInRange($range['start'], $range['end'], json_decode(json_encode($proposalsArray)));
                        
                    } else if ($subType === 'invoicesArray') {

                        $invoicesArray = $queryRequest['invoicesArray'];

                        $invoicesToSort = $this->getAllInvoicesArrayInRange($range['start'], $range['end'], json_decode(json_encode($invoicesArray)));

                    } else {

                        if (!(count($invoicesToSort) > 0)) {

                            $invoicesToSort = $this->getAllInvoicesInRange($range['start'], $range['end']);
    
                        }

                    }

                    foreach ($invoicesToSort as $j => $invoice) {

                        $related_object = $invoice['related_object'];
                        $main_object = $related_object['main_object'];

                        if (!isset($projects[$main_object['id']])) {

                            $project_data = array(
                                'id' => $main_object['id'],
                                'name' => $main_object['name'],
                                'payments' => [],
                            );

                            $projects[$main_object['id']] = array_merge($project_data, $this->initBalanceBuckets());

                        }

                        // add this invoice to project's aging bucket
                        $bucket = $projects[$main_object['id']][$i];
                        $bucket['count'] = intval($bucket['count']) + 1;
                        $bucket['balance'] = $bucket['balance'] + $invoice['balance'];
                        $projects[$main_object['id']][$i] = $bucket;

                        // add any payments to project
                        $payments = $projects[$main_object['id']]['payments'];
                        foreach ($invoice['payments'] as $k => $payment) {

                            $payment_data = array(
                                'id' => $payment['id'],
                                'amount' => $payment['amount'],
                                'fee' => $payment['fee'],
                                'date' => $payment['details']['payment_date'],
                                'checkNumber' => $payment['checkNumber'],
                                'stripe_payment_id' => $payment['stripe_payment_id'],
                            );

                            if (count($payments)> 0) {
                                
                                $existingPayment = __::find($payments, function ($p) use($payment) {
                                    return intval($p['id']) === intval($payment['id']);
                                });

                                if (!(bool) $existingPayment) {

                                    array_push($projects[$main_object['id']]['payments'], $payment_data);
                                    
                                }

                            } else {

                                $payment_data['no_exists_check'] = !$existingPayment;

                                array_push($projects[$main_object['id']]['payments'], $payment_data);

                            }

                        }

                    }

                    // for debug:
                    // $response->{$i}->start = $range['start']->format('Y-m-d H:i:s');
                    // $response->{$i}->end = $range['end']->format('Y-m-d H:i:s');
                    // $response->{$i}->invoice = $invoicesToSort;

                };
                
                foreach ($projects as $i => $project) {
                    
                    $projects[$i]['totals'] = $this->sumBuckets($project);

                }

                $response->status = "success";

                $response->project_data = $projects;

            } else {

                $response->status = "error";

                $response->message = 'No type was defined';

            }

        } else {

            return http_response_code(400);

        }

        return $this->sb->sendData($response, 1);

    }

}

?>