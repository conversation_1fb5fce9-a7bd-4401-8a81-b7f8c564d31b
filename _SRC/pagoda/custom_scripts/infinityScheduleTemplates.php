<?php

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';

$bento = new BentoAPI('infinity', '93cfbc982047ca935cd83883bed1fb9f08580cfec6db37beda0e91d7353d2508fe587baa3275d8747bf5f792c72825b3a4f12365926c08be3e1aef7425980060');

/*
echo 'test';
die();
*/
//die();
$count = 0;

$page = 0;
$objects = $bento->getWhere(array(
	'objectType'=>'groups',
	'queryObj'=>array(
		'group_type'=>'Project',
		'paged'=>array(
			'page'=>$page,
			'sortCol'=>'date_created',
			'sortDir'=>'desc',
			'pageLength'=>10
		)
	)
));

/*
var_dump($objects);
die();
*/
echo '<br /><br />';

while(count($objects['data']) > 0){
	
	foreach($objects['data'] as $obj){
		
		$proposals = $bento->getWhere(array(
			'objectType'=>'proposals',
			'queryObj'=>array(
				'main_object'=>$obj['id']
			)));
			
		foreach($proposals as $prop){
			
			$updated = $bento->update(array(
				'objectType'=>'proposals',
				'objectData'=>array(
					'id'=>$prop['id'],
					'schedule_template'=>$obj['category']	
				)
			));
			
			$count++;
echo $obj['name'];		
echo '<br /><br />';
			
		}
			
//var_dump($updated);

// die();
					
	}
	
	$page++;

	$objects = $bento->getWhere(array(
		'objectType'=>'groups',
		'queryObj'=>array(
			'group_type'=>'Project',
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'desc',
				'pageLength'=>10
			)
		)
	));

	echo '<br /><br />';
	echo 'loop complete '.$count;
	echo '<br /><br />';
	echo 'page '.$page;
/*
	if($count > 25){
		echo 'complete! '.$count;
		echo '<br /><br />';
		echo 'page '.$page;
		die();
	}
*/
		
}

echo 'complete! '.$count;
	
?>