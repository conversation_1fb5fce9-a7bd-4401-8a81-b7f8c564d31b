<?php

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';

$bento = new BentoAPI('infinity', '93cfbc982047ca935cd83883bed1fb9f08580cfec6db37beda0e91d7353d2508fe587baa3275d8747bf5f792c72825b3a4f12365926c08be3e1aef7425980060');

/*
echo 'test';
die();
*/

$page = 0;

/*
// without venue
$objects = $bento->getWhere(array(
	'objectType'=>'groups',
	'queryObj'=>array(
		'group_type'=>'Project',
		'category'=>[
			'type'=>'or',
			'values'=>[2007768,1933206,1933202,1933201,1933197,1933195,1933189,1933187,1870692,1870688,1870685,1870637,1870633,1869420]
		],
		'paged'=>array(
			'page'=>$page,
			'sortCol'=>'date_created',
			'sortDir'=>'asc',
			'pageLength'=>10
		)
	)
));
*/

// with venue
$objects = $bento->getWhere(array(
	'objectType'=>'groups',
	'queryObj'=>array(
		'group_type'=>'Project',
		'category'=>[
			'type'=>'or',
			'values'=>[1933185,1933183,1933182,1933181,1870667,1870664,1870661]
		],
		'paged'=>array(
			'page'=>$page,
			'sortCol'=>'date_created',
			'sortDir'=>'asc',
			'pageLength'=>10
		)
	)
));

//var_dump($objects);

echo '<br /><br />';

while(count($objects['data']) > 0){
	
	foreach($objects['data'] as $obj){
echo $obj['id'];
echo $obj['name'];
echo '<br /><br />';
		
		$proposals = $bento->getWhere(array(
			'objectType'=>'proposals',
			'queryObj'=>array(
				'main_object'=>$obj['id']
			)));
			
		foreach($proposals as $prop){

/*
			// without venue
			$updated = $bento->update(array(
				'objectType'=>'proposals',
				'objectData'=>array(
					'id'=>$prop['id'],
					'invoice_template'=>1688918	
				)
			));
*/

			// with venue
			$updated = $bento->update(array(
				'objectType'=>'proposals',
				'objectData'=>array(
					'id'=>$prop['id'],
					'invoice_template'=>1688821	
				)
			));
			
		}
			
/*
var_dump($updated);

die();
*/
					
	}
	
	$page++;

/*
	// without venue
	$objects = $bento->getWhere(array(
		'objectType'=>'groups',
		'queryObj'=>array(
			'group_type'=>'Project',
			'category'=>[
				'type'=>'or',
				'values'=>[2007768,1933206,1933202,1933201,1933197,1933195,1933189,1933187,1870692,1870688,1870685,1870637,1870633,1869420]
			],
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>10
			)
		)
	));
*/

	// with venue
	$objects = $bento->getWhere(array(
		'objectType'=>'groups',
		'queryObj'=>array(
			'group_type'=>'Project',
			'category'=>[
				'type'=>'or',
				'values'=>[1933185,1933183,1933182,1933181,1870667,1870664,1870661]
			],
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>10
			)
		)
	));
	
}
	
?>