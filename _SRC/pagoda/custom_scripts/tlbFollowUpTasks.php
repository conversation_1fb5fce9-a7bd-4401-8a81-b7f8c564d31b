<?php

error_reporting(E_ALL);
ini_set('display_errors', '1');

require_once '../apiExample/bento.php';

$bento = new BentoAPI('thelifebook', '60a44d38485bacfc18d21a7f20ef3a3a309f65b6c5879e35a8b44e0141ea89f6b09c7df708fa1402a3af920713348b53be85c7faaec6affe913dce91cb25d09e');
//echo strtotime('2019-06-01');

//var_dump($objects);

$page = 0;
$objects = $bento->getWhere(array(
		'objectType'=>'requests',
		'queryObj'=>array(
			'status'=>'Shipped',
			'adults'=>array(
				'type'=>'greater_than',
				'value'=>300
			),
			'date_created'=>array(
				'type'=>'between',
				'start'=>strtotime('2019-05-08'),
				'end'=>strtotime('2020-01-01')
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'contact'=>true,
			'company'=>true,
			'adults'=>true
		)
	));
	

/*
echo 'Page: '.$page.'<br />';
//echo count($objects['data']).'<br />';
print_r($objects);
*/

//$page++;

//echo 'Page: '.$page.'<br />';
//echo 'test';
die;

/*
$page = 0;
$object = array(
	'data'=>array(
		'item'=>'one'
	)
);
*/
//echo $objects['recordsTotal'];

/*
echo 'test';
die();
*/

while(
	
// 	$page < 2
	
	count($objects['data']) >= 1
	&& $objects['recordsTotal'] > (1 * $page)
	
	){
	
	$objects = $bento->getWhere(array(
		'objectType'=>'requests',
		'queryObj'=>array(
			'status'=>'Shipped',
			'adults'=>array(
				'type'=>'greater_than',
				'value'=>300
			),
			'date_created'=>array(
				'type'=>'between',
				'start'=>strtotime('2019-05-08'),
				'end'=>strtotime('2020-01-01')
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'contact'=>true,
			'company'=>true,
			'adults'=>true
		)
	));

	echo '<br />Page: '.$page.'<br />';
	echo count($objects['data']).'<br />';
	print_r($objects);
	
	$page++;
	
	$tasks = array();
	
	foreach($objects['data'] as $req){
//echo 'contactID '.$req['contact']['id'].'<br />';	

		if($req['adults'] < 500){
			
			$cellPhone = $bento->getWhere(array(
				'objectType'=>'contact_info',
				'queryObj'=>array(
					'type'=>49,
					'object_id'=>$req['contact']['id'],
					'is_primary'=>'yes'
				)
			));
	//var_dump($cellPhone);
			$churchPhone = $bento->getWhere(array(
				'objectType'=>'contact_info',
				'queryObj'=>array(
					'type'=>60,
					'object_id'=>$req['contact']['id']
				)
			));
			
			$createdAlreadyCheck = $bento->getWhere(array(
				'objectType'=>'#Follow_Up',
				'queryObj'=>array(
					'_3'=>$req['contact']['id']
				)
			));
var_dump($createdAlreadyCheck);			
			if(count($createdAlreadyCheck) >= 1){
				
				
			}else{
				
				$tasks[] = array(
					'objectType'=>'#Follow_Up',
					'objectData'=>array(
						'name'=>'Call '. $req['contact']['fname'].' '.$req['contact']['lname'],
						'_1'=>1,
						'_2'=>$req['contact']['manager'],
						//'_2'=>11,
						'_3'=>$req['contact']['id'],
						'_6'=>$cellPhone[0]['info'],
						'_9'=>$churchPhone[0]['info'],
						'tagged_with'=>array(1431795, $req['contact']['manager'])
					)
				);
				
			}
			
		}
		
	}
	
	var_dump($tasks);
	
	foreach($tasks as $task){
		
		$bento->create($task);
		
	}
		
}
	
?>