<?php

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';

$bento = new BentoAPI('infinity', '93cfbc982047ca935cd83883bed1fb9f08580cfec6db37beda0e91d7353d2508fe587baa3275d8747bf5f792c72825b3a4f12365926c08be3e1aef7425980060');

echo 'test';
die();

$page = 0;
$objects = $bento->getWhere(array(
	'objectType'=>'contacts',
	'queryObj'=>array(
		'type'=>array(
			'type'=>'not_equal',
			'value'=>1929600
		),
		'paged'=>array(
			'page'=>$page,
			'sortCol'=>'date_created',
			'sortDir'=>'desc',
			'pageLength'=>1
		)
	),
	'getChildObjs'=>array(
		'type'=>true
	)
));

while(count($objects['data']) > 0){
	
	foreach($objects['data'] as $obj){
				
		$bento->update(array(
			'objectType'=>'contacts',
			'objectData'=>array(
				'id'=>$obj['id'],
				'type'=>1929600	
			)
		));
		
	}
	
	$page++;

	$objects = $bento->getWhere(array(
		'objectType'=>'contacts',
		'queryObj'=>array(
			'type'=>array(
				'type'=>'not_equal',
				'value'=>1929600
			),
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'desc',
				'pageLength'=>1
			)
		),
		'getChildObjs'=>array(
			'type'=>true
		)
	));
	
}
	
?>