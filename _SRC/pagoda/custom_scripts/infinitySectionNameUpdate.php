<?php

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';

$bento = new BentoAPI('infinity', '93cfbc982047ca935cd83883bed1fb9f08580cfec6db37beda0e91d7353d2508fe587baa3275d8747bf5f792c72825b3a4f12365926c08be3e1aef7425980060');

/*
echo 'test';
die();
*/

$page = 0;

$objects = $bento->getWhere(array(
	'objectType'=>'inventory_menu',
	'queryObj'=>array(
		'paged'=>array(
			'page'=>$page,
			'sortCol'=>'date_created',
			'sortDir'=>'asc',
			'pageLength'=>5
		)
	)
));

//var_dump($objects);

/*
echo '<br /><br />';
echo 'test';
die();
*/

while(count($objects['data']) > 0){
	
	foreach($objects['data'] as $obj){

/*
echo $obj['id'];
echo $obj['name'];
*/
echo '<br /><br />';
					
		foreach($obj['sections'] as $section){

			if($section['name'] == 'Event Staff'){
				
				$section['name'] = 'Labor';
				
			}
			
		}
		
		$updated = $bento->update(array(
			'objectType'=>'inventory_menu',
			'objectData'=>$obj
		));
								
	}
	
	$page++;

	$objects = $bento->getWhere(array(
		'objectType'=>'inventory_menu',
		'queryObj'=>array(
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>5
			)
		)
	));
	
}
	
?>