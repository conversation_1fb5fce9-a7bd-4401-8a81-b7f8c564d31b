<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';
require_once '../lib/_.php';

$apiKey = $_REQUEST['pagodaAPIKey'];
$apiKey = '4697QnqsoG';
$bento = new BentoAPI($apiKey, '616a7f165d4f9e1792051572612fd8b623e786afac4cc73dd0c6ed116baa3bcea46aab663cf3a3061f07b0186ffbba058c7e3b310cbf77370ded6f163390bc76');

// Read the input stream
$json = file_get_contents("php://input");
 
// Decode the JSON object
$formData = json_decode($json, true);

$emailAddress = $formData['Email'];

// check email address
$existingContact = $bento->getWhere(['objectType'=>'#RQYVbc',
	'queryObj'=>array(
		'_2'=>$emailAddress
	)
])[0];

if($existingContact){
	
	// supplier already exists
	$contact = $existingContact;
	
}else{
	
	// new supplier, create object
	$newContact = [
		'name'=>$formData['Name'],
		'_29'=>$emailAddress,
		'_2'=>$emailAddress,
		'_3'=>$formData['Title'],
		'_4'=>$formData['What is your affiliation with the US Military?']
	];
	
	$newOrganization = [
		'name'=>$formData['Organization']
	];
	
	$contact = $bento->create(array(
		'objectType'=>'#RQYVbc',
		'objectData'=>$newContact
	));
	
	$organization = $bento->create(array(
		'objectType'=>'#N35eY6',
		'objectData'=>$newOrganization
	));
		
}

$fieldsAsText = '';
$count = 0;
foreach($formData as $question => $answer){
	
	if($count > 0){
		$fieldsAsText .= '<br />';
	}
	
	$fieldsAsText .= $question .': ';
	
	if(is_array($answer)){
		
		foreach($answer as $key => $subAnswer){
			
			$fieldsAsText .= '<br /> - '.$subAnswer;
			
		}
		
	}else{
		
		$fieldsAsText .= $answer;
		
	}
		
	$count++;
	
}

$newProject = [
	'name'=>'Project '.$formData['Name'].' - '.$formData['Organization'],
	'_59'=>$fieldsAsText
];

$project = $bento->create(array(
	'objectType'=>'#TrQHTN',
	'objectData'=>$newProject
));

$bento->update(array(
	'objectType'=>'#TrQHTN',
	'objectData'=>[
		'id'=>$project['id'],
		'tagged_with'=>[$contact['id'], $organization['id']]
	]
));

$bento->update(array(
	'objectType'=>'#RQYVbc',
	'objectData'=>[
		'id'=>$contact['id'],
		'tagged_with'=>[$project['id'], $organization['id']]
	]
));

$bento->update(array(
	'objectType'=>'#N35eY6',
	'objectData'=>[
		'id'=>$organization['id'],
		'tagged_with'=>[$project['id'], $contact['id']]
	]
));

http_response_code(200);
	
?>