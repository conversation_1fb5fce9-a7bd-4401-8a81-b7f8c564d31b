<?php

error_reporting(E_ALL);
ini_set('display_errors', '1');
		
require_once '../apiExample/bento.php';

$bento = new BentoAPI('mysuperfd', '32276566b022b3760abf10a7bfba4de788cb8264feae5305c7cf80cc94ff1faff4b188c32117cfdcadbfcab160244e05fdcccb40c9c9482e9fdaef3f8c75261d');
//echo strtotime('2019-06-01');

//var_dump($objects);

$page = 0;
/*
$objects = $bento->getWhere(array(
		'objectType'=>'groups',
		'queryObj'=>array(
			'group_type'=>'Project',
			'is_template'=>0,
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'desc',
				'pageLength'=>50
			)
		)
	));
	

//echo 'Page: '.$page.'<br />';
//echo count($objects['data']).'<br />';
//print_r($objects['data']);

foreach($objects['data'] as $obj){
echo $obj['proposal'];	
	$prop = $bento->getWhere(array(
		'objectType'=>'proposals',
		'queryObj'=>array(
			'id'=> $obj['proposal']
		)
	));

	echo 'Details: '.strip_tags($prop[0]['name']).'<br />';
	echo 'Details: '.strip_tags($prop[0]['is_template']).'<br />';
	
	
	
}

$page++;

//echo 'Page: '.$page.'<br />';

die();

$page = 0;
$object = array(
	'data'=>array(
		'item'=>'one'
	)
);

echo 'testing';
die();
*/

die();

while(
	
	$page < 20
	
/*
	count($objects['data']) >= 1
	&& $objects['recordsTotal'] < (count($objects['data']) * $page)
*/
	
	){
	
	$objects = $bento->getWhere(array(
		'objectType'=>'groups',
		'queryObj'=>array(
			'group_type'=>'Project',
			'is_template'=>0,
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'desc',
				'pageLength'=>50
			)
		)
	));

/*
	echo '<br />Page: '.$page.'<br />';
	echo count($objects['data']).'<br />';
*/
	//print_r($objects);
	
	$page++;
	
	$proposalsToUpdate = array();
	
	foreach($objects['data'] as $obj){
/*
echo 'Name: '.$obj['name'].'<br />';
echo 'Date Created: '.$obj['date_created'].'<br />';
echo 'Description: '.strip_tags($obj['description']).'<br />';
echo 'Proposal: '.$obj['proposal'].'<br />';
*/
//echo 'contactID '.$req['contact']['id'].'<br />';		
		$prop = $bento->getWhere(array(
			'objectType'=>'proposals',
			'queryObj'=>array(
				'id'=> $obj['proposal']
			)
		));
//print_r($invoices);
//die();		
		
		if($prop[0]['is_template'] == 1){
			
			$proposalsToUpdate[] = array(
				'objectType'=>'proposals',
				'objectData'=>array(
					'id'=>$prop[0]['id'],
					'is_template'=>0
				)
			);
			
		}
				
	}
	
	var_dump($proposalsToUpdate);
	
	//die();
	
	foreach($proposalsToUpdate as $obj){
		
		$bento->update($obj);
		
	}
		
}
	
?>