<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';
require_once '../lib/_.php';

$apiKey = $_REQUEST['pagodaAPIKey'];
$apiKey = '4697QnqsoG';
$bento = new BentoAPI($apiKey, '616a7f165d4f9e1792051572612fd8b623e786afac4cc73dd0c6ed116baa3bcea46aab663cf3a3061f07b0186ffbba058c7e3b310cbf77370ded6f163390bc76');

// Read the input stream
$json = file_get_contents("php://input");
 
// Decode the JSON object
$formData = json_decode($json, true);

$emailAddress = $formData['Email'];

// check email address
$existingSupplier = $bento->getWhere(['objectType'=>'#cA5wdo',
	'queryObj'=>array(
		'_1'=>$emailAddress
	)
])[0];

if($existingSupplier){
	
	// supplier already exists
	$supplier = $existingSupplier;
	
}else{
	
	// new supplier, create object
	$newSupplier = [
		'name'=>$formData['Company'],
		'_1'=>$emailAddress,
		'_2'=>$formData['Name'],
		'_3'=>$formData['Title'],
		'_4'=>$formData['What is your affiliation with the US Military?']
	];
	
	$supplier = $bento->create(array(
		'objectType'=>'#cA5wdo',
		'objectData'=>$newSupplier
	));
	
}

// create a new product
$productDetails = '';

foreach($formData as $fieldKey => $fieldValue){
	
	switch($fieldKey){
		
		case 'Name':
		case 'Title':
		case 'Email':
		case 'Company':
		case 'TItle':
		case 'What is your affiliation with the US Military?':
		case 'What category does your product fall under? (choose all that apply)':
			
			// do nothing
			
			break;
			
		default:
			
			$productDetails .= $fieldKey .' '. $fieldValue .': <br /><br />';
		
	}
	
}

$newProduct = [
	'name'=>$formData['Name of Product & Short Description'],
	'_1'=>$supplier['id'],
	'_12'=>$productDetails
];

$product = $bento->create(array(
	'objectType'=>'#OXnzzW',
	'objectData'=>$newProduct
));

// check and/or create tags
$taggedWith = [];

foreach($formData['What category does your product fall under? (choose all that apply)'] as $selection){
	
	$existingTag = $bento->getWhere(['objectType'=>'system_tags',
		'queryObj'=>array(
			'tag'=>$selection
		)
	])[0];
	
	if($existingTag){
		
		array_push($taggedWith, $existingTag['id']);
		
	}else{
		
		$newTag = $bento->create(array(
			'objectType'=>'system_tags',
			'objectData'=>[
				'tag'=>$selection
			]
		));
		
		array_push($taggedWith, $newTag['id']);
		
	}
	
}

// update supplier and product tags
$bento->update(array(
	'objectType'=>'#cA5wdo',
	'objectData'=>[
		'id'=>$supplier['id'],
		'tagged_with'=>$taggedWith
	]
));

$bento->update(array(
	'objectType'=>'#OXnzzW',
	'objectData'=>[
		'id'=>$product['id'],
		'tagged_with'=>$taggedWith
	]
));

http_response_code(200);
	
?>