<?php

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';

	$bento = new BentoAPI('nlp', '7febb92b5a2dd92d4c969b9eec8694bfdc51a5ebdcbc91160d2f9581ff0bdbc3988fbcf80cdbefc3c9d71fbb32341058747d9832dd6a729e1e382d6d6321736b');

echo 'test';
die();

$page = 0;

/*
// without venue
$objects = $bento->getWhere(array(
	'objectType'=>'groups',
	'queryObj'=>array(
		'group_type'=>'Project',
		'category'=>[
			'type'=>'or',
			'values'=>[2007768,1933206,1933202,1933201,1933197,1933195,1933189,1933187,1870692,1870688,1870685,1870637,1870633,1869420]
		],
		'paged'=>array(
			'page'=>$page,
			'sortCol'=>'date_created',
			'sortDir'=>'asc',
			'pageLength'=>10
		)
	)
));
*/

// with venue
$objects = $bento->getWhere(array(
	'objectType'=>'groups',
	'queryObj'=>array(
		'group_type'=>'Project',
		'paged'=>array(
			'page'=>$page,
			'sortCol'=>'date_created',
			'sortDir'=>'asc',
			'pageLength'=>10
		)
	)
));

//var_dump($objects);

echo '<br /><br />';

while(count($objects['data']) > 0){
	
	foreach($objects['data'] as $obj){
echo $obj['id'];
echo $obj['name'];
echo '<br /><br />';
		
		$proposals = $bento->getWhere(array(
			'objectType'=>'proposals',
			'queryObj'=>array(
				'main_object'=>$obj['id']
			)));
			
		foreach($proposals as $prop){

/*
			// without venue
			$updated = $bento->update(array(
				'objectType'=>'proposals',
				'objectData'=>array(
					'id'=>$prop['id'],
					'invoice_template'=>1688918	
				)
			));
*/

			// with venue
			$updated = $bento->update(array(
				'objectType'=>'proposals',
				'objectData'=>array(
					'id'=>$prop['id'],
					'invoice_template'=>2332898	
				)
			));
			
		}
			
/*
var_dump($updated);

die();
*/
					
	}
	
	$page++;

/*
	// without venue
	$objects = $bento->getWhere(array(
		'objectType'=>'groups',
		'queryObj'=>array(
			'group_type'=>'Project',
			'category'=>[
				'type'=>'or',
				'values'=>[2007768,1933206,1933202,1933201,1933197,1933195,1933189,1933187,1870692,1870688,1870685,1870637,1870633,1869420]
			],
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>10
			)
		)
	));
*/

	// with venue
	$objects = $bento->getWhere(array(
		'objectType'=>'groups',
		'queryObj'=>array(
			'group_type'=>'Project',
			'paged'=>array(
				'page'=>$page,
				'sortCol'=>'date_created',
				'sortDir'=>'asc',
				'pageLength'=>10
			)
		)
	));
	
}
	
?>