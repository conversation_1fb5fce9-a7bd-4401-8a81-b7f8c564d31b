<?php

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
require_once 'bento.php';

$bento = new BentoAPI('infinity', '93cfbc982047ca935cd83883bed1fb9f08580cfec6db37beda0e91d7353d2508fe587baa3275d8747bf5f792c72825b3a4f12365926c08be3e1aef7425980060');

$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, TRUE);

$company = $bento->create(array(
	'objectType'=>'companies',
	'objectData'=>array(
		'name'=>'The '.$input['lname'].'\'s',
		'type'=>1324548,
		'tagged_with'=>array(1476973, 923473)
	)
));

$contact = $bento->create(array(
	'objectType'=>'contacts',
	'objectData'=>array(
		'fname'=>$input['fname'],
		'lname'=>$input['lname'],
		'company'=>$company['id'],
		'type'=>1625573,
		'state'=>1,
		'tagged_with'=>array(1476973, 923473)
	)
));

$contactInfoObjs = [];
	
array_push($contactInfoObjs, [
	'objectType'=>'contact_info',
	'objectData'=>array(
		'object_id' => $contact['id'],
		'object_type' => 'contacts',
		'name' => 'Email Address',
		'title' => 'Email Address',
		'info' => $input['email'],
		'type' => 1119285,
		'is_primary' => 'yes'
	)
]);

array_push($contactInfoObjs, [
	'objectType'=>'contact_info',
	'objectData'=>array(
		'object_id' => $contact['id'],
		'object_type' => 'contacts',
		'name' => 'Phone Number',
		'title' => 'Phone Number',
		'info' => $input['phone'],
		'type' => 1119281,
		'is_primary' => 'yes'
	)
]);

$contactInfoIds = [];
foreach($contactInfoObjs as $info){
	
	$contactInfoIds[] = $bento->create($info)['id'];
	
}

$bento->update(array(
	'objectType'=>'contacts',
	'objectData'=>array(
		'id'=>$contact['id'],
		'contact_info'=>$contactInfoIds	
	)
));

$noteBody = '<h3>New Form Submission:</h3>';
$noteBody .= 'Name: '.$input['fname'] .' '. $input['lname'];
$noteBody .= '<br />Email: '.$input['email'];
$noteBody .= '<br />Phone: '.$input['phone'];
$noteBody .= '<br />Event Type: '.$input['event_type'];
$noteBody .= '<br />Estimated Guest Count: '.$input['guest_count'];
$noteBody .= '<br />Notes: '.$input['note'];

$note = array(
	'type'=>'contacts',
	'type_id'=>$contact['id'],
	'note'=>$noteBody,
	'author'=>0,
	'note_type'=>883664,
	'notifyUsers'=>array()
);

$bento->create(array(
	'objectType'=>'notes',
	'objectData'=>$note
));


	
?>