<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';
require_once '../lib/_.php';


$apiKey = $_REQUEST['pagodaAPIKey'];
$apiKey = 'OiDkmAJw2T';
$bento = new BentoAPI($apiKey, 'efff2e2e90c3839c323e636c7a49f8faea1b451a276725b0039122a8b60720f075349d39263046d4e70a50e4695f703c3fc7c3f66daea1629e070dd276ed54ca');

// Read the input stream
$json = file_get_contents("php://input");
 
// Decode the JSON object
$data1 = json_decode($json, true);
$data = $data1['form_response'];
$answers = [];
// var_dump($data);
// die();
foreach($data['definition']['fields'] as $field){
//echo $field['id'].'<br />';	
	foreach($data['answers'] as $answer){
//echo $answer['field']['id'].' - '.$field['id'].'<br />';		
		if($answer['field']['id'] == $field['id']){
//var_dump($answer['type']);

			if($answer['type'] == 'choice'){
				
				$answers[$field['id']] = $answer[$answer['type']]['label'];
				
			}else{
				
				$answers[$field['id']] = $answer[$answer['type']];
				
			}
			
		}
		
	}
	
}

//var_dump($answers);

$contact = [];
$lead = [];

//check for existing contact
$existingContact = $bento->getWhere(['objectType'=>'#UD7mJg',
	'queryObj'=>array(
		'_1'=>$answers['vQ8KqyQfRIQh']
	)
])[0];

if(!$existingContact){
	
	$existingContact = $bento->getWhere(['objectType'=>'#UD7mJg',
		'queryObj'=>array(
			'_2'=>ltrim(str_replace('+', '', $answers['9W5KLeV4t6Tv']), str_replace('+', '', $answers['9W5KLeV4t6Tv'])[0])
		)
	])[0];
	
}

if($existingContact){
	$contactObj = $existingContact;
}

$contact['name'] = $answers['3iX2VKwZ1kVa']; // name
$contact['_1'] = $answers['vQ8KqyQfRIQh']; // email
$contact['_2'] = ltrim(str_replace('+', '', $answers['9W5KLeV4t6Tv']), str_replace('+', '', $answers['9W5KLeV4t6Tv'])[0]); // phone

$lead = $bento->createFromTemplate([
	'objectId'=>4948235
]);

$lead['name'] = $answers['3iX2VKwZ1kVa']; // name
$lead['_21'] = $lead['name'];
$lead['_1'] = $answers['vQ8KqyQfRIQh']; // email
$lead['_2'] = ltrim(str_replace('+', '', $answers['9W5KLeV4t6Tv']), str_replace('+', '', $answers['9W5KLeV4t6Tv'])[0]); // phone
$lead['_8'] = 1;

// NEEDS COMPLETION
switch($answers['lN9S5rXe5w09']){ // date choice
	
	case 'Specific Date':
	
		$date = new DateTime($answers['f9YdNp3ClrqB']);
		
		$lead['_3'] = $date->format('n-j-y'); // specific date
	
	break;
	
	case 'Month':
		$lead['_3'] = $answers['vQ8KqyQfRIQh']; // month choice
	break;
	
	case 'Range':
	
		$date1 = new DateTime($answers['LUSt2SUmfyHz']);
		$date2 = new DateTime($answers['yssJMurnFFo0']);
	
		$lead['_3'] = $date1->format('n-j-y') .' - '. $date2->format('n-j-y'); // date range
	
	break;
	
}

$lead['_4'] = $answers['7CsuvUNujccG']; // estimated guest count
$lead['_5'] = $answers['BTNAdImKsfZc']; // event category

// type of service
switch($answers['ncVjdhH06oen']){ // time of day
	
	case 'Corporate Event':
		$lead['_6'] = 1;
	break;
	
	case 'Social Event':
		$lead['_6'] = 2;
	break;
	
	case 'Wedding':
		$lead['_6'] = 3;
	break;
	
	case 'Party Pack Gift Only':
		$lead['_6'] = 4;
	break;
	
}

switch($answers['FWbZrvdkUUpU']){ // time of day
	
	case 'Morning':
		$lead['_7'] = 1;
	break;
	
	case 'Afternoon':
		$lead['_7'] = 2;
	break;
	
	case 'Evening':
		$lead['_7'] = 3;
	break;
	
	case 'Full Day':
		$lead['_7'] = 4;
	break;
	
	case 'Multiple Days':
		$lead['_7'] = 5;
	break;
	
}

// virtual services
if($answers['jmGrxiQdDNsS']){
	
	$lead['_17'] = [];
	
	foreach($answers['jmGrxiQdDNsS']['labels'] as $label){

		switch($label){
			
			case 'Mix':
			array_push($lead['_17'], 2);
			break;
			
			case 'Make':
			array_push($lead['_17'], 1);
			break;
			
			case 'Taste':
			array_push($lead['_17'], 3);
			break;
			
			case 'Travel':
			array_push($lead['_17'], 4);
			break;
			
			case 'Entertain':
			array_push($lead['_17'], 5);
			break;
			
		}
				
	}
	
}

// in-person services
if($answers['yBpYGK72oNzl']){
	
	$lead['_18'] = [];
	
	foreach($answers['yBpYGK72oNzl']['labels'] as $label){
		
		switch($label){
			
			case 'Decor':
			array_push($lead['_18'], 1);
			break;
			
			case 'Venue Selection':
			array_push($lead['_18'], 2);
			break;
			
			case 'Catering':
			array_push($lead['_18'], 3);
			break;
			
			case 'Entertainment':
			array_push($lead['_18'], 4);
			break;
			
			case 'Staffing':
			array_push($lead['_18'], 5);
			break;
			
			case 'Teambuilding':
			array_push($lead['_18'], 6);
			break;
			
			case 'Transportation':
			array_push($lead['_18'], 7);
			break;
			
			case 'Audio-Visual':
			array_push($lead['_18'], 8);
			break;
			
		}
				
	}
	
}

// estimated budget
switch($answers['3dNkWSB1q0WM']){
	
	case 'Up to $499':
	$lead['_14'] = 1;
	break;
	
	case '$500 - $2,500':
	$lead['_14'] = 2;
	break;
	
	case '$2,501 - $5,000':
	$lead['_14'] = 3;
	break;
	
	case '$5,000+':
	$lead['_14'] = 4;
	break;
	
}

// lead source
switch($answers['WtDsnZ2OPdtn']){
	
	case 'Google':
	$lead['_15'] = 1;
	break;
	
	case 'Instagram':
	$lead['_15'] = 2;
	break;
	
	case 'Facebook':
	$lead['_15'] = 3;
	break;
	
	case 'Friend':
	$lead['_15'] = 4;
	break;
	
	case 'The Grapevine':
	$lead['_15'] = 5;
	break;
	
	case 'I\'m a Repeat Client!':
	$lead['_15'] = 6;
	break;
	
}

$lead['_16'] = $answers['sPgjEiW7cOko']; // special notes

// follow up date
$today = new DateTime();
$today->add(new DateInterval('P10D'));
$lead['_25'] = $today->format('U'); // follow up date

// echo '<pre>';
// var_dump($data['definition']['fields']);
// var_dump($lead);
// echo '</pre>';

// set the event type
if(count($lead['_18']) > 0){
	
	$lead['_26'] = 1;
	
}else{
	
	$lead['_26'] = 2;
	
}

if(!$contactObj){
	
	$contactObj = $bento->create(array(
		'objectType'=>'#UD7mJg',
		'objectData'=>$contact
	));
	
}

$lead['parent'] = $contactObj['id'];
$leadObj = $bento->update(array(
	'objectType'=>'#nB8jKy',
	'objectData'=>$lead
));

$bento->update(array(
	'objectType'=>'#nB8jKy',
	'objectData'=>[
		'id'=>$leadObj['id'],
		'tagged_with'=>[$contactObj['id']]
	]
));

http_response_code(200);
	
?>