<?php

class BentoAPI  {
	
	private $apiKey = '';
	private $apiToken = '';
	
	function __construct($key, $token){
		
		if(!$key || !$token){
			return 'Please provide authentication.';
		}
		
		$this->apiKey = $key;
		$this->apiToken = $token;
		
	}
	
	public function archive($type, $id){
		
		$requestObj['do'] = 'deleteObject';
		$requestObj['json'] = array('id'=>$id, 'type'=>$type);
		
		$ret = $this->makeRequest($requestObj);
		
		return $ret;
		
	}

	public function create($objectArray){
		
		$requestObj['do'] = 'createNewObject';
		$requestObj['json'] = $objectArray;
		
		$ret = $this->makeRequest($requestObj);
		
		return $ret;
		
	}
	
	public function getById($id){
		
		$requestObj['do'] = 'getObjectById';
		$requestObj['json'] = array('value'=>$id);
		
		$ret = $this->makeRequest($requestObj);
		
		return $ret;
		
	}

	public function getWhere($requestObj){
		
		$requestObj['do'] = 'getObjectsWhere';
		
		$ret = $this->makeRequest($requestObj);
		
		return $ret;
		
	}

	public function update($updateArray){
				
		$requestObj['do'] = 'updateObject';
		$requestObj['json'] = $updateArray;
		
		$ret = $this->makeRequest($requestObj);
		
		return $ret;
		
	}
	
	public function changeState($id, $stateProperty, $newState){
		
		$requestObj['do'] = 'updateState';
		$requestObj['json'] = array(
			'objectId'=>$id,
			'newState'=>$newState,
			'stateProperty'=>$stateProperty
		);
		
		$ret = $this->makeRequest($requestObj);
		
	}
		
	private function makeRequest($requestObj){
		
		$requestObj['pagodaAPIKey'] = $this->apiKey;
		$requestObj['json'] = json_encode($requestObj['json']);

		$header = array();
		$header[] = 'Content-Type: application/x-www-form-urlencoded';
		$header[] = 'bento-token: '.$this->apiToken;

		$curl = curl_init();
		
		curl_setopt_array($curl, array(
			CURLOPT_URL => "https://bento.infinityhospitality.net/api/_get.php?".http_build_query($requestObj),
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "POST"
		));
		
		curl_setopt($curl, CURLOPT_HTTPHEADER,$header);
		
		$response = curl_exec($curl);

		curl_close($curl);
		return json_decode($response, true);
		
	}
	
}
