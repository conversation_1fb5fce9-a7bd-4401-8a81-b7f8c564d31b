<?php

// error_reporting(E_ALL);
// ini_set('display_errors', '1');

date_default_timezone_set('America/Chicago');

ob_start();

header('Access-Control-Allow-Origin: *');

if(!APP_ROOT){
	define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/../' );
}

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMTAdmin.php';
require_once APP_ROOT.'rules/rules.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
require_once APP_ROOT.'_communications.php';

$now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
// set instances
$pgObjects = new pgObjectsAdmin($pdo, 'voltzsoftware');
$instances = ["infinity", "nlp", "dreamcatering"]; // Dream Catering Requested by Infinity on 10/13/2022

// loop over instances
foreach ($instances as $instance) {

    // instance setup
    $_REQUEST['pagodaAPIKey'] = $instance;
    $appConfig = array(
        'systemName'=>$_REQUEST['pagodaAPIKey']
    );
    $rules = new Rules($appConfig);
    $pgObjects = new pgObjects($pdo, $_REQUEST['pagodaAPIKey'], $rules);
    $_POST = new stdClass();
    $comms = new Comm($pgObjects, $appConfig);
    $app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files, $_REQUEST['pagodaAPIKey']);
    $pgObjects->changeInstance($_REQUEST['pagodaAPIKey']);
    $invoiceEmailNotifications = $pgObjects->getAll('invoice_emails', 0);

    $inst = $pgObjects->whereAll(
        'instances'
        , array(
            'instance'=>$instance
        )
    )[0];

    foreach($invoiceEmailNotifications as $notification){

        // build date range for where clause
        $dateRange = [
            "type" => "between"
            , "start" => ""
            , "end" => ""
        ];
        $todayStart = new DateTime('UTC today'); //midnight this morning
        $todayEnd = new DateTime('UTC tomorrow'); //midnight tonight

        if ($notification["beforeAfter"] === "before") {

            if ($notification["everyday"] === "yes") { // invoice due date within x days from now

                $dateRange["start"] = $todayEnd->getTimestamp();
                $dateRange["end"] = $todayEnd->add(new DateInterval('P'. $notification["days"] .'D'))->getTimestamp();

            } else { // invoice due date is x days from now

                $dateRange["start"] = $todayEnd->add(new DateInterval('P'. $notification["days"] .'D'))->getTimestamp();
                $dateRange["end"] = $todayEnd->add(new DateInterval('P'. $notification["days"] .'D'))->getTimestamp();

            }

        } else {

            $dateRange["start"] = $todayEnd;

            if ($notification["everyday"] === "yes") { // invoice due date within x days ago

                $dateRange["start"] = $todayStart->sub(new DateInterval('P'. $notification["days"] .'D'))->getTimestamp();
                $dateRange["end"] = $todayEnd->getTimestamp();

            } else { // invoice due date was x days ago

                $dateRange["start"] = $todayStart->sub(new DateInterval('P'. $notification["days"] .'D'))->getTimestamp();
                $dateRange["end"] = $todayEnd->sub(new DateInterval('P'. $notification["days"] .'D'))->getTimestamp();

            }
        }

        // construct childObjs to pull only necessary data
        $childObjs = [
            "id" => true
            ,"related_object" => [
                "main_object" => [
                    "id" => true
                    ,"type" => [
                        "id" => true
                        , "name" => true
                    ]
                    ,"state" => true
                    ,'is_template' => [
                        'type' => 'not_equal',
                        'value' => 1
                    ]
                ]
                , 'is_template' => [
					'type' => 'not_equal',
					'value' => 1
				]
            ]
            ,"balance" => true
            ,"main_contact" => [
                "name"=>true
                ,"fname"=>true
                ,"contact_info" => [
                    "info" => true
                    ,"is_primary" => true
                    ,"type" => [
                        "data_type" => true
                    ]
                ]
            ]
        ];

        $whereClause = [
            'due_date' => $dateRange
            ,'balance' => [
                "type" => "greater_than"
                , "value" => 0
            ]
        ];

        // get appropriate invoices
        $invoices = $app->getObjectsWhere('invoices', $whereClause, 0, $childObjs);

        // loop through invoices and send reminder emails
        foreach ($invoices as $invoice) {

            $projectID = $state = $invoice["related_object"]["main_object"]["id"];
            $workflow = $invoice["related_object"]["main_object"]["type"]["name"];
            $state = $invoice["related_object"]["main_object"]["state"];
            $approvedStates = [];

            // INSTANCE/WORKFLOW SPECIFIC CHECKS. REMINDERS ARE NOT SENT OUT FOR INVOICES THAT DO NOT FALL INTO SPECIFIC
            // WORKFLOWS IN SPECIFIC STATES:
            if ($instance === "infinity") {
                if ($workflow === "Event Management") {
                    $approvedStates = [2, 3, 8]; //Booked, Assigned, Paused state Ids
                    if (!in_array($state, $approvedStates)) {
                        continue;
                    }
                } else {
                    continue;
                }
            }
            else if ($instance === "nlp") {
                if ($workflow === "NLP Client ") {
                    $approvedStates = [3]; //Booked  state Id
                    if (!in_array($state, $approvedStates)) {
                        continue;
                    }
                } else {
                    continue;
                }
            } else if ($instance === "dreamcatering") {
                if ($workflow === "Event Management") {
                    /*
                        {
                            id: 8248140
                            , name: 'Event Management'
                            , states: [
                                { uid: 1, name: 'Signed - Awaiting Payment' }
                                { uid: 2, name: 'Booked' }
                                { uid: 8, name: 'Paused - Waiting for Payment' }
                            ]
                        }
                    */
                    $approvedStates = [1, 2, 8];
                    if (!in_array($state, $approvedStates)) {
                        continue;
                    }
                } else {
                    continue;
                }
            }

            $subject = $notification['subject'];
            $body = $notification['body'];

            $subjectString = str_replace('{{this.related_object.name}}', $invoice["related_object"]["main_object"]["name"], $subject);

            $bodyString = str_replace('{{this.related_object.name}}', $invoice["related_object"]["main_object"]["name"], $body);

            // convert merge tags
            $mergedSubject = $pgObjects->runSteps(
                $invoice
                , [
                    'merge' => [
                        'obj' => 		$invoice
                        , 'template' => $subjectString
                        , 'format' => 	'html'
                        , 'parent' => 	$invoice['parent']
                    ]
                ]
                , true
            )['memo'];

            $mergedBody = $pgObjects->runSteps(
                $invoice
                , [
                    'merge' => [
                        'obj' => 		$invoice
                        , 'template' => $bodyString
                        , 'format' => 	'html'
                        , 'parent' => 	$invoice['parent']
                    ]
                ]
                , true
            )['memo'];

            // add payment portal link:
            $emailBodyWithLink = $mergedBody . "<br><br><a href='https://bento.infinityhospitality.net/app/invoices#?&i=". $instance ."&pid=". $invoice["related_object"]["main_object"]["id"] ."'>Invoice Payment Portal</a>";

            // email variables
            $mainContactEmailAddress = array_pop(array_reverse(array_filter($invoice["main_contact"]["contact_info"], function($obj){
                return $obj["is_primary"] == "yes" && $obj["type"]["data_type"] == "email";
            })))["info"];

            $instanceFromEmailAddress = $app->fromEmailAddress;
            $mergevars = (object) array(
                'TITLE' => '' ,
                'SUBJECT' => $mergedSubject,
                'BODY' => $emailBodyWithLink,
                'INSTANCE_NAME' => $app->appConfig["systemName"]
            );

            array_push($invoice['tagged_with'], $projectID);
            $invoice['tagged_with'] = array_unique($invoice['tagged_with']);

            // send email
            $comms->sendMandrillEmail(
                $mainContactEmailAddress,
                $instanceFromEmailAddress,
                $mergedSubject,
                $mergevars,
                [],
                null,
                1, // Save a record of the email
                0,
                false,
                null,
                $app->appConfig["systemName"],
                0,
                $invoice["tagged_with"]
            );

            $updatedInv = $pgObjects->update(
                'invoices'
                , array(
                    'id'=>$invoice['id']
                    , 'sent'=> 'Yes'
                    , 'sent_on'=> $now->format('Y-m-d H:i:s')
                    , 'sent_by'=> $inst['id']
                )
                , 1
            );

            $noteBody = "Payment reminder sent to:  ". $invoice['main_contact']['name'] . " (". $mainContactEmailAddress .") <br/>";

            $noteObj = array(
                'type_id' => $invoice['related_object']['main_object']
                , 'icon' => [
                    'icon' => 'envelope square'
                    , 'color' => 'teal'
                ]
                , 'log_type' => 'automated-email'
                , 'type' => 'groups'
                , 'note' => $noteBody
                , 'record_type' => 'log'
                , 'author' => $inst['id']
                , 'notifyUsers' => []
                , 'tagged_with' => [$inst['id']]
            );
            $note = $pgObjects->create(
                    'notes'
                    , $noteObj
            );

        }
    }
}

?>
