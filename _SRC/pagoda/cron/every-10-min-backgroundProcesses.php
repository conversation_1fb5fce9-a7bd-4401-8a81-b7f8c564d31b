<?php

return;
// 	error_reporting(E_ALL);
// 	ini_set('display_errors', '1');
	
	header('Access-Control-Allow-Origin: *');
	
	if (extension_loaded ('newrelic')) {
		newrelic_set_appname ("Pagoda");
		newrelic_background_job();
		newrelic_ignore_apdex();
	}
	
	if(!APP_ROOT){
		define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/../' );
	}
	
	$_REQUEST['pagodaAPIKey'] = 'root';
	require_once APP_ROOT.'_pgObjectsMTAdmin.php';
	
	if(!class_exists('MyApp')){
		require_once $_SERVER['DOCUMENT_ROOT'].'/app/_app/_config.php';
	}
	
/*
	require_once APP_ROOT.'_objects.php';
	require_once APP_ROOT.'_communications.php';
	require_once APP_ROOT.'_cookiesPG.php';
	require_once APP_ROOT.'files/_fileApi.php';
	require_once APP_ROOT.'stripe/init.php';
*/
		
	$pgObjects = new pgObjectsAdmin($pdo, $appConfig['instance']);
	$processes = $pgObjects->where('background_process', ['status' => 'not_started']);

	if(is_array($processes)){
		
		foreach($processes as $i => $process){
			
			$instance = $pgObjects->where('instances', array('instance'=>$process['instance']))[0];

			$objects = new Objects($pdo);
			$comms = new Comm($pgObjects, $instance);
			$cookies = new Cookies($pgObjects);
			$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey']);
			
			$pgObjects->changeInstance($process['instance']);
			
			$app = new MyApp($instance, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
			
			$proc = $pgObjects->getById('background_process', $process['id']);
			if($proc['status'] == 'not_started'){

				$pgObjects->update('background_process', [
					'id' => $process['id'],
					'status' => 'started'
				]);
				
				$funcToRun = $process['run'];
				$_POST = (object) $process['payload'];
				$app->$funcToRun();
				
				$pgObjects->update('background_process', [
					'id' => $process['id'],
					'status' => 'complete'
				]);
				
			}
			
		}
		
	}

// 	die();
	
?>