<?php

// error_reporting(E_ALL);
// ini_set('display_errors', '1');

date_default_timezone_set('America/Chicago');

ob_start();

header('Access-Control-Allow-Origin: *');

if(!APP_ROOT){
	define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/../' );
}

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMTAdmin.php';
require_once APP_ROOT.'rules/rules.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
require_once APP_ROOT.'_communications.php';

// get all instances
$pgObjects = new pgObjectsAdmin($pdo, 'voltzsoftware');
$instances = array_column($pgObjects->where('instances', ['enabled' => 1]), 'instance');

// loop over instances
foreach ($instances as $instance) {

    if($instance == 'infinity'){
        $ICG_SITEID =  getenv('ICG_SITEID');
        $ICG_SITEKEY =  getenv('ICG_SITEKEY');
        $ICG_APIKEY =  getenv('ICG_APIKEY');
    }
    if($instance == 'dreamcatering'){
        $ICG_SITEID =  getenv('ICG_SITEID_DREAM');
        $ICG_SITEKEY =  getenv('ICG_SITEKEY_DREAM');
        $ICG_APIKEY =  getenv('ICG_APIKEY_DREAM');
    }

    // instance setup
    $_REQUEST['pagodaAPIKey'] = $instance;
    $appConfig = array(
        'systemName'=>$_REQUEST['pagodaAPIKey']
    );
    $rules = new Rules($appConfig);
    $pgObjects = new pgObjects($pdo, $_REQUEST['pagodaAPIKey'], $rules);
    $_POST = new stdClass();
    $comms = new Comm($pgObjects, $appConfig);
    $app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files, $_REQUEST['pagodaAPIKey']);
    $pgObjects->changeInstance($_REQUEST['pagodaAPIKey']);
    $queryObj = array(
        "icg_payment_id"=>array("type"=>"is_set")
        ,"status"=>array('type'=>'or','values'=>array('', 'N'))
    );
    $icgPayments = $app->getObjectsWhere('payments', $queryObj, 0, 0);

    try {

        // loop over payments
        foreach ($icgPayments as $payment){

            // pull payment status from ICG:
            $curl = curl_init();
            curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://trans.icheckgateway.com/',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>'<?xml version="1.0" encoding="utf-8"?>
            <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-in-stance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
            <soap:Body>
                <GetACHTransaction xmlns="https://trans.icheckgateway.com">
                <SiteID>'. $ICG_SITEID .'</SiteID>
                <SiteKey>'. $ICG_SITEKEY .'</SiteKey>
                <APIKey>'. $ICG_APIKEY .'</APIKey>
                <ConfirmationNumber>'. $payment["icg_payment_id"] .'</ConfirmationNumber>
                <GatewayLiveMode>'. var_export(getenv('ICG_GATEWAYLIVEMODE'), true) .'</GatewayLiveMode>
                </GetACHTransaction>
            </soap:Body>
            </soap:Envelope>',
            CURLOPT_HTTPHEADER => array(
                'SOAPAction: https://trans.icheckgateway.com/GetACHTransaction',
                'Content-Type: text/xml'
            )
            ));

            $response = curl_exec($curl);
            $xml = new DOMDocument();
            $xml->loadXML($response);
            $xmlArray = xml_to_array($xml);
            $result = $xmlArray["soap:Envelope"]["soap:Body"]["GetACHTransactionResponse"]["GetACHTransactionResult"];
            $transResponse = $result["TransResponse"];
            $status = $result["Status"];
            $icgConfirmationNumber = $result["Confirmation"];
            $icgPaymentDate = $result["TransactionDate"];
            curl_close($curl);

            // update bento payment object if curl was successful
            if ($transResponse === "SUCCESS"){

                // update payment object
                $payment["status"] = $status;
                $pgObjects->update('payments', $payment);

                // send notification if status contains "R" for returned/rejected
                if (str_contains($status, 'R')) {

                    // email variables
                    if (getenv('ICG_GATEWAYLIVEMODE') && $payment["test_payment"] != "true") {

                        $accountantEmails = array('<EMAIL>', '<EMAIL>');

                    } else {

                        $accountantEmails = array('<EMAIL>');

                    }
                    $instanceFromEmailAddress = $app->fromEmailAddress;
                    $mergevars = (object) array(
                        'TITLE' => '' ,
                        'SUBJECT' => "Failed iCheckGateway Payment",
                        'BODY' => "" . $date . "<br/><br/>
                        An iCheckGateway ACH payment has been Returned:<br/><br/>
                        iCG Confirmation: " . $icgConfirmationNumber . "<br/>
                        Payment Date: " . $icgPaymentDate . "<br/><br/>
                        Details for this payment can be found in the iCheckGateway Potal: <a href='https://sb.icheckgateway.com/Default.aspx'>https://sb.icheckgateway.com/Default.aspx</a>.",
                        'INSTANCE_NAME' => $app->appConfig["systemName"]
                    );

                    // send email
                    $comms->sendMandrillEmail(
                        $accountantEmails,
                        $instanceFromEmailAddress,
                        "Failed iCheckGateway Payment",
                        $mergevars,
                        [],
                        null,
                        0,
                        0,
                        false,
                        null,
                        $app->appConfig["systemName"],
                        0,
                        $invoice["tagged_with"]
                    );

                    // unlink payment and invoice:
                    $invoice = $pgObjects->getById('invoices', $payment["invoice"], 0, 0);
                    $invoice["payments"] = array_diff($invoice["payments"], array($payment["id"]));
                    $invoice["balance"] = $invoice["balance"] + $payment["amount"];
                    $invoice["paid"] = $invoice["paid"] - $payment["amount"];
                    $pgObjects->update('invoices', $invoice);
                    $payment["invoice"] = 0;
                    $pgObjects->update('payments', $payment);
                }
            }
        }




    } catch (Exception $e) {

        echo($e->getMessage());

    }

}

    function xml_to_array($root) {
        $result = array();

        if ($root->hasAttributes()) {
            $attrs = $root->attributes;
            foreach ($attrs as $attr) {
                $result['@attributes'][$attr->name] = $attr->value;
            }
        }

        if ($root->hasChildNodes()) {
            $children = $root->childNodes;
            if ($children->length == 1) {
                $child = $children->item(0);
                if ($child->nodeType == XML_TEXT_NODE) {
                    $result['_value'] = $child->nodeValue;
                    return count($result) == 1
                        ? $result['_value']
                        : $result;
                }
            }
            $groups = array();
            foreach ($children as $child) {
                if (!isset($result[$child->nodeName])) {
                    $result[$child->nodeName] = xml_to_array($child);
                } else {
                    if (!isset($groups[$child->nodeName])) {
                        $result[$child->nodeName] = array($result[$child->nodeName]);
                        $groups[$child->nodeName] = 1;
                    }
                    $result[$child->nodeName][] = xml_to_array($child);
                }
            }
        }

        return $result;
    }
