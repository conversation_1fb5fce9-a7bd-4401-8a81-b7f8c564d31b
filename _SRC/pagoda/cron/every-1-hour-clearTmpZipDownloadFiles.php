<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header('Access-Control-Allow-Origin: *');

if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
}

/*
require_once '../_config.php';
require_once 'vendor/autoload.php';
require_once '_objects.php';
require_once '_pgObjectsMT.php';
require_once '_communications.php';
require_once '_cookiesPG.php';
require_once 'files/_fileApi.php';
*/

$objects = new Objects($pdo);
$pgObjects = new pgObjects($pdo, 'voltzsoftware');
$comms = new Comm($pgObjects, $appConfig);
$cookies = new Cookies($pgObjects);
$files = new FileApi($pgObjects, '/_files/_instances/voltzsoftware', 'voltzsoftware');

$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);

// !TODO: Clear files over a certain age (an hour old) from the 'temp-zip-downloads' dir in spaces
	
?>