<?php

/*
	error_reporting(E_ALL);
	ini_set('display_errors', '1');
*/

header('Access-Control-Allow-Origin: *');

if (extension_loaded('newrelic')) {
	newrelic_set_appname("Pagoda");
	newrelic_background_job();
	newrelic_ignore_apdex();
}

if (!APP_ROOT) {
	define("APP_ROOT", realpath(dirname(__FILE__)) . '/../');
}

$_REQUEST['pagodaAPIKey'] = 'voltzsoftware';

require_once APP_ROOT . 'vendor/autoload.php';
require_once APP_ROOT . 'lib/_.php';
require_once APP_ROOT . '_pgObjectsMTAdmin.php';
require_once APP_ROOT . '_communications.php';

$pgObjects = new pgObjectsAdmin($pdo, 'voltzsoftware');
$instances = $pgObjects->where('instances', ['enabled' => 1]);
$_POST = new stdClass();
$comms = new Comm($pgObjects, $appConfig);
$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);

foreach ($instances as $key => $instance) {
	//echo('INSTANCE: ' . $instance['instance'] . '</br>');
	$pgObjects->changeInstance($instance['instance']);

	$start = new DateTime();
	$end = (new DateTime());

	$pageNumber = 0;
	$pageCount = 20;
	$users = [];
	$shouldContinue = true;

	// Get invoices with a balance
	$notifications = $pgObjects->getAll('invoice_emails');

	foreach ($notifications as $notification) {
		/*
		var_dump($notification['beforeAfter']);
		var_dump($notification['days']);
		var_dump($notification['body']);
		*/
		if ($notification['beforeAfter'] == 'after') {

			$start->modify('+' . $notification['days'] . ' days');
			$end->modify('+' . $notification['days'] . ' days');
			/*
				$start->modify('+73 days');
				$end->modify('+73 days');
*/

			if ($notification['everyday'] == 'yes') {

				//$start = new DateTime();
				$end->modify('+1000 days');
			}
		} else {

			$start->modify('-' . $notification['days'] . ' days');
			$end->modify('-' . $notification['days'] . ' days');
			/*
				$start->modify('-28 days');
				$end->modify('-28 days');
*/

			if ($notification['everyday'] == 'yes') {

				$end->modify('-1000 days');
			}
		}



		$start->setTime(0, 0, 0);
		$end->setTime(23, 59, 59);

		/*
echo '<br /><br />';
echo('Start '.$start->format("Y-m-d H:i:s"));
echo '<br /><br />';
echo('End '.$end->format("Y-m-d H:i:s"));
*/

		$invoices = $pgObjects->where(
			'invoices',
			[
				'balance' => [
					'type' => 'greater_than',
					'value' => 0
				], 'due_date' => [
					'type' => 'between',
					'start' => intval($start->format('U')),
					'end' => intval($end->format('U'))
				], 'is_deleted' => false
			],
			'',
			0,
			false,
			0,
			'null',
			'asc',
			100,
			null,
			[],
			'string',
			$instance['instance']
		);

		//var_dump($invoices);

		foreach ($invoices as $inv) {

			/*
echo '<br />';
echo $inv['name'] .' - '. $inv['due_date'] .' - '. $inv['balance'] .'<br /><br />';
*/

			$contactInfoMap = $pgObjects->where('contact_info_types', [
				'data_type' => 'email'
			]);
			//var_dump($contactInfoMap);
			$contact = $pgObjects->getById('contacts', $inv['main_contact']);
			$contactInfo = $pgObjects->where('contact_info', [
				'object_id' => $contact['id'],
				'type' => $contactInfoMap[0]['id'],
				'is_primary' => 'yes'
			]);

			/*
echo 'Email: '.$contactInfo[0]['info'];
echo '<br />';
echo '<br />';
*/

			$mergeTags = [];
			foreach ($inv as $k => $v) {

				switch ($k) {

					case 'amount':
					case 'balance':
					case 'paid':

						$mergeTags['invoice.' . $k] = '$' . $v / 100;

						break;

					case 'main_contact':

						foreach ($contact as $cK => $cV) {

							$mergeTags['invoice.main_contact.' . $cK] = $cV;
						}

						break;

					case 'due_date':

						$date = new DateTime($v);

						$mergeTags['invoice.' . $k] = $date->format('Y-m-d');

						break;

					default:

						$mergeTags['invoice.' . $k] = $v;
				}
			}

			//var_dump($mergeTags);

			$subjectString = $notification['subject'];
			$bodyString = $notification['body'];
			foreach ($mergeTags as $k => $v) {
				//echo $k;
				$subjectString = str_replace('{{' . $k . '}}', $v, $subjectString);
				$bodyString = str_replace('{{' . $k . '}}', $v, $bodyString);
			}

			$bodyString .= '<br /><br />Click here: https://bento.infinityhospitality.net/app/invoices#?&i=' . $instance['instance'] . '&iid=' . $inv['id'];

			// prepare email
			$mergevars = array(
				'TITLE' => $subjectString,
				'SUBJECT' => $subjectString,
				'BODY' => $bodyString,
				'INSTANCE_NAME' => $instance['instance']
			);

			// send email to user
			$app->sendEmail(array($contactInfo[0]['info']), '<EMAIL>', $subjectString, $mergevars, 'Invoice Notification', false);

			/*
echo '<br />';
echo '<br />';
echo '$subjectString: '.$subjectString;
echo '<br />';
echo '$bodyString: '.$bodyString;
echo '<br />';
echo '<br />';
*/
		}

		//die();

	}
}

//echo('Complete');
