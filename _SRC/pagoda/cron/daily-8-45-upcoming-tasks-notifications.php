<?php 
	return;
/*
	error_reporting(E_ALL);
	ini_set('display_errors', '1');
*/
	
	function getOverdueTasks ($usersTasks) {
		
		return __::filter($usersTasks, function($task) {
			
			if((new DateTime($task['end_date']))->format('U') < (new DateTime())->format('U') ) {
				return true;
			}
			
			return false;
			
		});
		
	}
	
	function getUpcomingTasks ($usersTasks) {
		
		return __::filter($usersTasks, function($task) {
			
			if((new DateTime($task['end_date']))->format('U') > (new DateTime())->format('U') ) {
				return true;
			}
			
			return false;
			
		});
		
	}
	
	function getCustomTasksHtml ($user, $tasks, $types, $typesUsersFields, $typesDueDatesFields, $titleStr) {
		
		$html = '';
		
		if (
			!empty($types)
			&& !empty($tasks)
		) {
			
			foreach ($types as $type) {
				
				$txt = '';
				foreach ($tasks as $task) {
					
					$rolesOnTask = [];
					foreach ($typesUsersFields[$type['bp_name']] as $propKey) {
						
						if (
							$task[$propKey] === $user['id']
							|| (
								is_array($task[$propKey])
								&& in_array($user['id'], $task[$propKey])
							)
						) {
							
							array_push(
								$rolesOnTask
								, $type['blueprint'][$propKey]['name']
							);
							
						}
						
					}
					
					if (!empty($rolesOnTask)) {
						
						$txt .= '<li>'. 
									$task['name'] .', due on '. (new DateTime($task[$typesDueDatesFields[$type['bp_name']]]))->format('F jS') .
									' ('. implode(', ', $rolesOnTask) .')'.
								'</li>';
								
						$counter++;
						
					}
					
				}
				
				if (!empty($txt)) {
					
					$html .= '<div><h3>You have '. $counter .' '. $titleStr .' '. $type['name'] .'(s)</h3>'. $txt .'</div>';
					
				}
				
			}
			
		}
		
		return $html;
		
	}
	
	function groupCustomTasks ($tasks, $types) {
		
		$grouped = [
			'overdue' => []
			, 'upcoming' => []
		];
		foreach ($types as $type) {
			
			$dueDateProp = '';
			foreach ($type['blueprint'] as $key => $prop) {
				
				if (
					$prop['type'] === 'date'
					&& $prop['options']
					&& $prop['options']['is_due_date']
				) {
					
					$dueDateProp = $key;
					$tasks = __::sortBy(
						$tasks
						, function ($task) use ($dueDateProp) {
							
							$date = (new DateTime($task[$dueDateProp]))->format('U');
							return intval($date);
							
						}
					);
					
					foreach ($tasks as $task) {
						
						if ((new DateTime($task[$dueDateProp]))->format('U') < (new DateTime())->format('U')) {
							
							array_push(
								$grouped['overdue']
								, $task
							);
							
						} else {
							
							array_push(
								$grouped['upcoming']
								, $task
							);
							
						}
						
					}
					
				}
				
			}
			
		}
		
		return $grouped;
		
	}
	
	header('Access-Control-Allow-Origin: *');
	
	if (extension_loaded ('newrelic')) {
		newrelic_set_appname ("Pagoda");
		newrelic_background_job();
		newrelic_ignore_apdex();
	}
	
	if(!APP_ROOT){
		define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/../' );
	}
	
 	$_REQUEST['pagodaAPIKey'] = 'voltzsoftware';
	
	require_once APP_ROOT.'vendor/autoload.php';
	require_once APP_ROOT.'lib/_.php';
	require_once APP_ROOT.'_pgObjectsMTAdmin.php';
	require_once APP_ROOT.'_communications.php';
			
	$pgObjects = new pgObjectsAdmin($pdo, 'voltzsoftware');
 	$instances = $pgObjects->where('instances', ['enabled' => 1]);
	$_POST = new stdClass();
	$comms = new Comm($pgObjects, $appConfig);
	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
	
	foreach($instances as $key => $instance) {
//echo('INSTANCE: ' . $instance['instance'] . '</br>');
		$pgObjects->changeInstance($instance['instance']);

		$start = new DateTime();
		$end = (new DateTime())->add(new DateInterval('P3D'));
		
		$pageNumber = 0;
		$pageCount = 20;
		$users = [];
		$shouldContinue = true;
		
		// Get entity types that should also be considered 
		$types = $pgObjects->where(
			'entity_type'
			, [
				'is_task' => true
				, 'instance' => $instance['instance']
			]
		);
		$typesUsersFields = [];
		$typesDueDatesFields = [];
		
		// If custom types are also considered tasks, get those 'tasks' as well.
		if (!empty($types)) {
			
			foreach ($types as $type) {
				
				$typesUsersFields[$type['bp_name']] = [];
				foreach ($type['blueprint'] as $key => $field) {
					
					if (
						$field['fieldType'] === 'users'
						|| $field['fieldType'] === 'user'
					) {
						
						array_push(
							$typesUsersFields[$type['bp_name']]
							, $key
						);
						
					} elseif (
						$field['type'] === 'date'
						&& $field['options']
						&& $field['options']['is_due_date']
					) {
						
						$typesDueDatesFields[$type['bp_name']] = $key;
					
					}
					
				}
				
			}
			
			$bpNames = __::chain($types)
						->pluck('bp_name')
						 ->map(
							 function ($bpName) {
								 
								 return '#'. $bpName;
								 
							 }
						 )->value();
			
			$customTasks = $pgObjects->where(
				$bpNames
				, [
					'status' => [
						'type' => 		'not_equal'
						, 'value' => 	'done'
					]
				]
				
			);
			$customTasks = groupCustomTasks($customTasks, $types);
			
		}
		
		while ($shouldContinue) {
			
			$ids = []; // User ids
			
			$users = $pgObjects->where('users', [
				'enabled' => 1
			], '', [
				'fname' => true,
				'lname' => true,
				'email' => true
			], true, 
			   ($pageNumber * $pageCount), // This is the offset
			   'null', 
			   'asc', 
			   $pageCount, // This is the limit,
			   null,
			   [],
			   'string',
			   $instance['instance'] 
			);

			foreach($users as $index => $user) {
				
				array_push($ids, $user['id']);	
				
			}
			
			$pageNumber++;
			
			if(empty($users)) {
				$shouldContinue = false;
			}

//echo('Page Number ' . $pageNumber . '<br>');
//echo('Users Count ' . count($users) . '<br>');
	
			$tasks = $pgObjects->where('groups', [
				'group_type' => 'Task', 
				'status' => [
					'type' => 'not_equal',
					'value' => 'done'
				], 'is_recurring' => [
					'type' => 'not_equal',
					'value' => 1
				], 'end_date' => [
					'type' => 'before',
					'date' => intval($end->format('U')) 
				], 'tagged_with' => [
					'type' => 'any',
					'values' => $ids
				], 'is_deleted' => false
			],
				'',
				0,
				false,
				0,
				'null',
				'asc',
				100,
				null,
				[],
				'string',
				$instance['instance']
			);

//echo('Tasks collected in round ' . $pageNumber . ' is ' . count($tasks) . ' <br> ');
			
			foreach ($users as $index => $user) {
				
				$usersTasks = [];
				
				$usersTasks = __::filter($tasks, function($task) use($user) {

					if(__::includ($task['tagged_with'], $user['id'])) {
						return true;
					}
					
					return false;
	 
				});

//echo('Tasks collected for ' . $user['fname'] . ' is ' . count($usersTasks) . ' <br> ');
				
				if (!empty($usersTasks)) {
					
					$overDueTasks = [];
					$upcomingTasks = [];
					
					// Get overdue tasks
					$overDueTasks = getOverdueTasks($usersTasks);
					
					// Get upcoming tasks
					$upcomingTasks = getUpcomingTasks($usersTasks);
					
					$_POST->details = '<div>'; // Outer div
					
					// Overdue tasks
					if(!empty($overDueTasks)) {
						
						$_POST->details .= '<div> <h3>You have ' . count($overDueTasks) . ' overdue task(s)</h3> <ul>';
						
						foreach($overDueTasks as $index => $task) {
							
							$_POST->details .= '<li>'. $task['name'] .', due on '. (new DateTime($task['end_date']))->format('F jS') .'</li>';
							
						}
						
						$_POST->details .= '</ul> </div>';
						
					}
					
					// Overdue, custom type
					$_POST->details .= getCustomTasksHtml($user, $customTasks['overdue'], $types, $typesUsersFields, $typesDueDatesFields, 'overdue');
					
					// Upcoming tasks
					if(!empty($upcomingTasks)) {
						
						$_POST->details .= '<div> <h3>You have ' . count($upcomingTasks) . ' upcoming due task(s)</h3> <ul>';
						
						foreach($upcomingTasks as $index => $task) {
							
							$_POST->details .= '<li>'. $task['name'] .', due on '. (new DateTime($task['end_date']))->format('F jS') .'</li>';
							
						}
						
						$_POST->details .= '</ul> </div>';
						
					}
					
					// Upcoming, custom type
					$_POST->details .= getCustomTasksHtml($user, $customTasks['upcoming'], $types, $typesUsersFields, $typesDueDatesFields, 'upcoming');
					
					$_POST->details .= '</div>'; // Outer div
					
					$_POST->producer  = $user['id'];
					$_POST->title     = 'Hello ' . $user['fname'] . ', here is a summary of your task list.';
					$_POST->type      = 'general';
					$_POST->link      = 'https://bento.infinityhospitality.net/app/'. $task['instance'] .'#mytasks';
					$_POST->notify    = $task['notify'];
					$_POST->color     = 'blue';
					$_POST->icon      = 'tasks';
					$_POST->notifySet = [$user['id']];
					
					$app->notify(0);	
					
				} 
				
			}	
			
		}
		
	}
	
//echo('Complete');
