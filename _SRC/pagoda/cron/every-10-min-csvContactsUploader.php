<?php
	
/*
	error_reporting(E_ALL);
	ini_set('display_errors', '1');
*/
	
	header('Access-Control-Allow-Origin: *');
	
	if (extension_loaded ('newrelic')) {
		newrelic_set_appname ("Pagoda");
		newrelic_background_job();
		newrelic_ignore_apdex();
	}
	
	if(!APP_ROOT){
		define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/../' );
	}
	
	// << TEST START
	
/*
	require APP_ROOT.'../../DBPATH.php';
	
	$_REQUEST['pagodaAPIKey'] = 'root';
	$pdo = new PDO("pgsql:host=". $DB_PATH .";port=5432;dbname=pagoda;user=pagoda;password=***********");
	
	require APP_ROOT.'../../DBPATH.php';
	
	require_once APP_ROOT.'/lib/_.php';
	require_once APP_ROOT.'vendor/autoload.php';
	require_once APP_ROOT.'_objects.php';
	require_once '../_pgObjectsMTAdmin.php';
	
	require_once APP_ROOT.'_communications.php';
	require_once APP_ROOT.'_cookiesPG.php';
	require_once APP_ROOT.'files/_fileApi.php';
	require_once APP_ROOT.'_excel.php';
	
	require_once APP_ROOT.'_config.php';
*/
	
	// END TEST >> 
		
	$pgObjects = new pgObjects($pdo, $_REQUEST['pagodaAPIKey']);
	
	switch($_REQUEST['action']){
		
		case 'test':
			
			$upload = $pgObjects->getById('csv_upload', intval($_REQUEST['upload']));
			$instance = $pgObjects->where('instances', array('instance'=>$upload['instance']))[0];
			
			$objects = new Objects($pdo);
			$comms = new Comm($pgObjects, $instance);
			$cookies = new Cookies($pgObjects);
			$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey']);
			
			$pgObjects->changeInstance($upload['instance']);
			
			$app = new MyApp($instance, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
			
			$uploader = new contactsUploader($app, $instance, $upload['id']);
			
			$uploader->createContactData();
			
			echo $upload['id'] . ' COMPLETED <br />';
			die();
			break;
		
		case 'delete':
		
			$instance = $pgObjects->where('instances', array('instance'=>$upload['instance']))[0];
			
			$objects = new Objects($pdo);
			$comms = new Comm($pgObjects, $instance);
			$cookies = new Cookies($pgObjects);
			$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey']);
			$pgObjects->changeInstance($upload['instance']);
			$app = new MyApp($instance, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
			
			$uploader = new contactsUploader($app, $instance, $_REQUEST['source']);
			$uploader->deleteContactsFromSource();
			
			echo $upload['id'] . ' COMPLETED <br />';
			
			break;
			
		default:
			$uploads = $pgObjects->whereAll('csv_upload', ['status' => 'not_started']);
		
			foreach($uploads as $index => $upload){
				
				$instance = $pgObjects->whereAll('instances', array('instance'=>$upload['instance']))[0];
				
				$objects = new Objects($pdo);
				$comms = new Comm($pgObjects, $instance);
				$cookies = new Cookies($pgObjects);
				$files = new FileApi(
					$pgObjects
					, '../_files/_instances/'. $upload['instance']
					, $upload['instance']
				);
				
				$pgObjects->setInstance($upload['instance']);
				
				$app = new MyApp($instance, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
				
				$uploader = new contactsUploader($app, $instance, $upload['id']);
				
				$uploader->createContactData();
				
				echo $upload['id'] . ' COMPLETED <br />';
				
			}
			
			break;
		
	}
	
	echo 'PROCESS COMPLETED.';
	
// 	die();
// 	$uploader = new contactsUploader($app, $appConfig, $_REQUEST['uploadId']);
/*
	switch($_REQUEST['action']){
		
		case 'create':
			$uploader->createContactData();
			break;
			
		case 'delete':
			$uploader->deleteContactsFromSource();
			break;
		
	}
*/
	
	class contactsUploader {
		
		// private
		private function create_objs(){

			// get companies with the same names
			$already_created_companies = $this->app->pgObjects->where('companies', [
				'name' => [
					'type' => 'or',
					'values' => array_map(function($comp){ return $comp['name']; }, $this->companiesBatch)
				]
			]);
			
			// remove companies from to be created batch if they are already in the db
			if(is_array($this->companiesBatch)){
				
				foreach($this->companiesBatch as $i => $company){
					
					foreach($already_created_companies as $j => $already_created){
						
						if($company['name'] === $already_created['name']){
							
							unset($this->companiesBatch[$i]);
							
						}
						
					}
					
				}
				
			}
			
			// create companies
			$created_companies = array();
			if(count($this->companiesBatch) > 0){
				$created_companies = $this->app->pgObjects->create('companies', $this->companiesBatch, 1, 0);
			}
			
			// add companies created previously to list of companies created just now
			foreach($already_created_companies as $i => $comp){
				
				array_push($created_companies, $already_created_companies[$i]);
				
			}
			
			// place company ids into contacts
			foreach($this->contactsBatch as $i => $contact){
				
				if(is_array($created_companies)){
					foreach($created_companies as $j => $company){
						
						if($company['name'] === $contact['company']){
							
							$this->contactsBatch[$i]['company'] = $company['id'];
							
						}
						
					}
				}
				
			}
			
			// set tags
			foreach($this->contactsBatch as $i => $contact) {
				
				$this->contactsBatch[$i]['tagged_with'] = $this->map['tagged_with'];
				
			}

			// create contacts
			$created_contacts = $this->app->pgObjects->create('contacts', $this->contactsBatch, 1, 0);
			
			// place contact ids into contact infos
			foreach($this->contactInfoBatch as $i => $contactInfo){
				
				if(is_array($created_contacts)){
					foreach($created_contacts as $j => $contact){
						
						if($contact['data_source_id'] === $contactInfo['data_source_id']){
							
							$this->contactInfoBatch[$i]['object_id'] = $contact['id'];
							
						}
						
					}
				}
				
			}
			
			// create contact info
			$created_contact_infos = $this->app->pgObjects->create('contact_info', $this->contactInfoBatch, 1, 0);

			// add contact info array to contact objs
			foreach($created_contacts as $i => $created_contact){
				
				$created_contacts[$i]['contact_info'] = array();
				foreach($created_contact_infos as $j => $created_contact_info){
					
					if($created_contact['data_source_id'] === $created_contact_info['data_source_id']){
						
						array_push($created_contacts[$i]['contact_info'], $created_contact_info['id']);
						
					}
					
				}
				
			}
			
			// update contact objs
			$updated_contacts = $this->app->pgObjects->update('contacts', $created_contacts, 0);
			
			// clear batches
			$this->contactsBatch = array();
			$this->companiesBatch = array();
			$this->contactInfoBatch = array();
			
			echo $this->batch_size .' rows processed.<br />';
			
		}
		
		private function get_csv_data(){
			
			$loc = $new = str_replace(
							' '
							, '%20'
							, 'https://pagoda.nyc3.digitaloceanspaces.com/_instances/'
								. $this->appConfig['instance'] 
								.'/'. str_replace(
										'../../root/', ''
										, $this->map['file']['loc']
									)
								);
								
			$row = 0;
			$csvKeys = array();

// 			error_reporting(E_ALL);
// 	ini_set('display_errors', '1');

			if (($handle = fopen($loc, "r")) !== FALSE) {
				
			    while (($data = fgetcsv($handle, 0, ",")) !== FALSE) {

				    // get csv keys
				    if($row === 0){
					    $csvKeys = $data;
				    }else{
					    $this->parse_csv_row($csvKeys, $data, $row);
				    }
// 				    var_dump($csvKeys);

				    // create objs
				    if(count($this->contactsBatch) >= $this->batch_size){
					    $this->create_objs();
				    }
				    
			        $row++;

			    }
			    
			    fclose($handle);
			    
			}

			$this->create_objs();
			echo 'COMPLETE';
			
		}
		
		private function parse_csv_row($keys, $row, $rowId){

			// parse into temp array with keyed row data
			$pre = array();
			$contactObj = array(
				'data_source' => $this->map['id'],
				'data_source_id' => $rowId
			);
			$companyObj = array(
				'data_source' => $this->map['id'],
				'data_source_id' => $rowId
			);
			$contactInfoObjs = array();

			if(is_array($row)){
				
				foreach($row as $i => $val){

					$pre[$keys[$i]] = $val;
					
				}
				
			}
			
			// company obj
				// check if company exists with that name, if not, create (or add to batch to be created)
			$companyObj['name'] = $pre[$this->map['translation']['company']];

			// contact obj
			$contactObj['fname'] = $pre[$this->map['translation']['fname']];
			$contactObj['lname'] = $pre[$this->map['translation']['lname']];
			$contactObj['type'] = intval($this->map['translation']['type']);
			$contactObj['company'] = $pre[$this->map['translation']['company']];
			
			$contactObj['data_source'] = intval($this->map['id']);
			
			$contactObj['manager'] = 0;
			$contactObj['stripe_id'] = '';
			$contactObj['available_types'] = array();
			
			// contact info objs
			if(is_array($this->map['translation']['available_types'])){
				
				foreach($this->map['translation']['available_types'] as $i => $infoMap){
					
					switch($infoMap['data_type']){
						
						case 'address':
						
							array_push($contactInfoObjs, array(
								
								'data_source' => $this->map['id'],
								'data_source_id' => $rowId,
								'type' => intval($infoMap['type']),
								'is_primary' => $infoMap['is_primary'],
								'street' => $pre[$infoMap['street']],
								'city' => $pre[$infoMap['city']],
								'state' => $pre[$infoMap['state']],
								'zip' => $pre[$infoMap['zip']],
								'country' => $pre[$infoMap['country']]
								
							));
							
							break;
							
						default:
							
							array_push($contactInfoObjs, array(
								
								'data_source' => $this->map['id'],
								'data_source_id' => $rowId,
								'type' => intval($infoMap['type']),
								'is_primary' => $infoMap['is_primary'],
								'info' => $pre[$infoMap['info']]
								
							));
							
							break;
						
						
					}
// 					var_dump($infoMap);
					
				}
				
			}
// 			die();
// 			var_dump($pre);
			
			if($contactObj['fname'] !== "" || $contactObj['lname'] !== ""){
				array_push($this->contactsBatch, $contactObj);
				array_push($this->companiesBatch, $companyObj);
				foreach($contactInfoObjs as $i => $contactInfoObj){
					array_push($this->contactInfoBatch, $contactInfoObj);
				}
			}
			
// 			var_dump($contactObj);
// 			var_dump($contactInfoObjs);
// 			var_dump($companyObj);			
			
		}
		
		private function update_status($newStatus){
			
			$this->app->pgObjects->update('csv_upload', [
				'id' => $this->map['id'],
				'status' => $newStatus
			]);
			
		}
		
		// public
		public function __construct($app, $config, $transferId){
			
			//$transferId = 952052;
			$this->start_time = date('Y-m-d H:i:s');
			$this->batch_size = 175;
// 			$this->batch_size = 2;
			$this->app = $app;
			$this->map = $this->app->pgObjects->getById('csv_upload', intval($transferId), 1);
			$this->appConfig = $config;
			$this->notify = $this->map['created_by']['email'];
			
			// object batches for db
			$this->contactsBatch = array();
			$this->companiesBatch = array();
			$this->contactInfoBatch = array();
			
		}
		
		public function createContactData(){

			switch($this->map['status']){

				case 'not_started':
					$this->update_status('started');
					$this->get_csv_data();
					$this->update_status('complete');
					$this->app->sendEmail($this->notify, null, 'Contacts upload completed!', [
						'TITLE' => 'Contacts upload completed!',
						'BODY' => 'Your contacts upload has been completed! (started at '. $this->start_time .' and completed at '. date('Y-m-d H:i:s') .')',
						'BUTTON' => ''
					], null, 0);
					break;
					
				case 'started':
				// just testing: !!!! REMOVE THIS CODE
// 					$this->get_csv_data();
					// JUST TESTING REMOVE THIS CODE
					break;
					
				case 'complete':
					// just testing: !!!! REMOVE THIS CODE
// 					$this->get_csv_data();
					// JUST TESTING REMOVE THIS CODE
					break;
					
				case 'deleted':
					break;
				
				default:
					break;
				
			}
			
		}
		
		public function deleteContactsFromSource(){
// 			echo 'testing delete func';
// 			var_dump($this->map['id']);
// 			die();
			if($this->app->pgObjects->deleteWhere('contacts', ['data_source' => $this->map['id']])){
				
				$this->app->pgObjects->deleteWhere('companies', ['data_source' => $this->map['id']]);
				$this->app->pgObjects->deleteWhere('contact_info', ['data_source' => $this->map['id']]);
				echo 'data deleted';
				return true;
				
			}else{
				
				return false;
				
			}
			
		}
		
	}
	
?>