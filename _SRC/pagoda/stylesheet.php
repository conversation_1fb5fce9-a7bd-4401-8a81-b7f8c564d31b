<?php

// Set error reporting
error_reporting(0);

// Set the content type
header("Content-type: text/css");

// Set headers
header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Methods: GET, POST');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );

// Get required files
require APP_ROOT.'DBPATH.php';
require_once APP_ROOT.'lib/_.php';
require_once APP_ROOT.'vendor/autoload.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
// require_once APP_ROOT.'files/_fileApi.php';
require_once APP_ROOT.'_excel.php';

$instance = !empty($_GET['instance']) ? $_GET['instance'] : '';

$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");

// Get page objects
$pgObjects = new pgObjects($pdo, $instance);

// Get instance
$instance = $pgObjects->whereAcrossInstances('instances', array('instance' => $instance))[0];

if ($instance['is_portal']) {

	$portalAccessToken = $pgObjects->whereAcrossInstances('portal_access_token', array('client' => $instance['id']))[0];

	if (!empty($portalAccessToken)) {

		$company = $pgObjects->getByIdAcrossInstances('companies', array('id' => $portalAccessToken['company']))[0];

		if (!empty($company)) {

			$instance = $pgObjects->whereAcrossInstances('instances', array('instance' => $company['instance']))[0];

		}
		
	}

}

$instanceName = $instance['instance'];

function adjustColor($hex, $percent) {
	
	// validate hex string
	$hex = preg_replace( '/[^0-9a-f]/i', '', $hex );
	$new_hex = '#';
	
	if ( strlen( $hex ) < 6 ) {
		$hex = $hex[0] + $hex[0] + $hex[1] + $hex[1] + $hex[2] + $hex[2];
	}
	
	// convert to decimal and change luminosity
	for ($i = 0; $i < 3; $i++) {
		$dec = hexdec( substr( $hex, $i*2, 2 ) );
		$dec = min( max( 0, $dec + $dec * $percent ), 255 ); 
		$new_hex .= str_pad( dechex( $dec ) , 2, 0, STR_PAD_LEFT );
	}		
	
	return $new_hex;

}

// Colors
$red = '#FF2F00 !important;';
$redHover = '#ED2B00 !important;';

$orange = '#FF8400 !important;';
$orangeHover = '#FF8D12 !important;';

$yellow = '#F5D40F !important;';
$yellowHover = '#E8C70A !important;';

$olive = '#B5CC18 !important;';
$oliveHover = '#C5DF1A !important;';

$green = '#18CC54 !important;';
$greenHover = '#16BF4E !important;';

$teal = '#00CAC0 !important;';
$tealHover = '#00BDB4 !important;';

$blue = '#027eff !important;';
$blueHover = '#0072ED !important;';

$violet = '#7A52D1 !important;';
$violetHover = '#6D41CD !important;';

$purple = '#B04ED1 !important;';
$purpleHover = '#A93CCD !important;';

$pink = '#FF379B !important;';
$pinkHover = '#F4007A !important;';

$brown = '#BD7C51 !important;';
$brownHover = '#B67245 !important;';

$grey = '#767676 !important;';
$greyHover = '#6D6D6D !important;';

$black = '#2a2e3a !important;';
$blackHover = '#2A2C2E !important;';

$primaryColor = $blue;
$primaryColorHover = $blueHover;
$primaryColorLightened = adjustColor($primaryColor, .20) . ' !important;';
$primaryColorSuperLight = adjustColor($primaryColor, .50) . ' !important;';
$primaryColorMegaSuperLight = adjustColor($primaryColor, .75) . ' !important;';
$primaryColorDarkened = adjustColor($primaryColor, -.20) . ' !important;';
$primaryColorSuperDark = adjustColor($primaryColor, -.50) . ' !important;';
$primaryColorMegaSuperDark = adjustColor($primaryColor, -.75) . ' !important;';

$leftNavBackgroundColor = '#f8f8f8 !important;';
$leftNavBackgroundHoverColor = 'rgba(0,0,0,.05) !important;';
$leftNavBorderColor = '#ebebeb !important;';
$leftNavIconColor = $black;
$leftNavIconActiveColor = $blue;
$leftNavMiddleIconColor = '#4b4e57 !important;';
$leftNavBottomBackgroundColor = 'background-color:#2a2e3a !important;';
$leftNavBottomBackgroundHoverColor = 'background-color:#2a2e3a !important;';
$leftNavBottomIconColor = 'rgba(255,255,255,.7) !important;';
$toolbarBackgroundColor = '#ffffff !important;';
$toolbarBackgroundHoverColor = '#f4f4f4 !important;';
$toolbarTextColor = '#2B2F39 !important;';
$toolbarIconColor = '';
$editToolsItemBorderColor = '#ebebeb !important;';

if ( $instanceName === 'foundation_group' ) {

	$primaryColor = '#002f6c !important;';
	$primaryColorHover = adjustColor($primaryColor, .20) . ' !important;';
	$primaryColorLightened = adjustColor($primaryColor, .20) . ' !important;';
	$primaryColorSuperLight = adjustColor($primaryColor, .50) . ' !important;';
	$primaryColorMegaSuperLight = adjustColor($primaryColor, .75) . ' !important;';
	$primaryColorDarkened = adjustColor($primaryColor, -.20) . ' !important;';
	$primaryColorSuperDark = adjustColor($primaryColor, -.50) . ' !important;';
	$primaryColorMegaSuperDark = adjustColor($primaryColor, -.75) . ' !important;';

	$leftNavBackgroundColor = $primaryColorSuperDark;
	$leftNavBackgroundHoverColor = $primaryColorDarkened;
	$leftNavBorderColor = $primaryColorSuperDark;
	$leftNavIconColor = '#ffffff !important;';
	$leftNavIconActiveColor = '#ffffff !important;';
	$leftNavMiddleIconColor = '#ffffff !important;';
	$leftNavBottomBackgroundColor = $primaryColorMegaSuperDark;
	$leftNavBottomBackgroundHoverColor = $primaryColorMegaSuperDark;
	$leftNavBottomIconColor = '#ffffff !important;';
	$toolbarBackgroundColor = $primaryColor;
	$toolbarBackgroundHoverColor = $primaryColorHover;
	$toolbarTextColor = '#ffffff !important;';
	$toolbarIconColor = '#ffffff !important;';
	$editToolsItemBorderColor = $primaryColorSuperLight;

} else if ( $instanceName === 'erationalmarketing' ) {

	$primaryColor = '#1f53a3 !important;';
	$primaryColorHover = adjustColor($primaryColor, .20) . ' !important;';
	$primaryColorLightened = adjustColor($primaryColor, .20) . ' !important;';
	$primaryColorSuperLight = adjustColor($primaryColor, .50) . ' !important;';
	$primaryColorMegaSuperLight = adjustColor($primaryColor, .75) . ' !important;';
	$primaryColorDarkened = adjustColor($primaryColor, -.20) . ' !important;';
	$primaryColorSuperDark = adjustColor($primaryColor, -.50) . ' !important;';
	$primaryColorMegaSuperDark = adjustColor($primaryColor, -.75) . ' !important;';

	$leftNavBackgroundColor = $primaryColorSuperDark;
	$leftNavBackgroundHoverColor = $primaryColorDarkened;
	$leftNavBorderColor = $primaryColorSuperDark;
	$leftNavIconColor = '#ffffff !important;';
	$leftNavIconActiveColor = '#ffffff !important;';
	$leftNavMiddleIconColor = '#ffffff !important;';
	$leftNavBottomBackgroundColor = $primaryColorMegaSuperDark;
	$leftNavBottomBackgroundHoverColor = $primaryColorMegaSuperDark;
	$leftNavBottomIconColor = '#ffffff !important;';
	$toolbarBackgroundColor = $primaryColor;
	$toolbarBackgroundHoverColor = $primaryColorHover;
	$toolbarTextColor = '#ffffff !important;';
	$toolbarIconColor = '#ffffff !important;';
	$editToolsItemBorderColor = $primaryColorSuperLight;

}

?>

<style>
<?php include 'stylesheet.css'; ?>
</style>