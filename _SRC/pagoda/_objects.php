<?php

class Objects {

	protected $db;

	function __construct($dbConn){

		

		$this->db = $dbConn;

		

	}

	// returns the id of the newly created object
	public function create($tableName){

		$statement = $this->db->prepare("insert into $tableName () values ()");

		if($statement->execute()){

			return $this->db->lastInsertId();

		}

	}
	
	public function createObjectBlueprint($tableName){

		$statement = $this->db->prepare("INSERT INTO blueprints DEFAULT VALUES RETURNING *;");

		if($statement->execute()){

			return $this->db->lastInsertId();

		}else{
			
			print_r($statement->errorInfo());
			
		}

	}
	
	public function createObjectTable($tableName){
		
		$statementString = "CREATE TABLE IF NOT EXISTS ". $tableName ." (id INT NOT NULL AUTO_INCREMENT PRIMARY KEY, object_data TEXT, date_created DATETIME DEFAULT CURRENT_TIMESTAMP)";
								
		$statement = $this->db->prepare($statementString);

		$statement->bindParam(':tableName', $tableName, PDO::PARAM_STR);

		if($statement->execute()){

			return true;

		}

	}
	
	public function createObjectType($objectType){
				
		if($this->createObjectTable($objectType, $columns)){
			
			return true;
			
		}
		
	}

	// returns true if the deletion was successful
	public function deleteObject($tableName, $objectId){

		$statement = $this->db->prepare("delete from $tableName where id = :objectId");

		$statement->bindParam(':objectId', $objectId, PDO::PARAM_INT);

		if($statement->execute()){

			return true;

		}

	}

	public function getAll($tableName){

		$statement = $this->db->prepare("select * from $tableName");		
		$statement->execute();
		$objects = $statement->fetchAll();
		
		$i=0;
		if(is_array($objects)){
			foreach($objects as $object){
				
				if(is_array($object)){
					foreach($object as $fieldKey => $fieldData){
						
						if(is_string($fieldData) and is_array(json_decode($fieldData, true))){
							$objects[$i][$fieldKey] = json_decode($fieldData, true);
						}elseif(is_string($fieldData) and is_array(json_decode($fieldData, false))){
							$objects[$i][$fieldKey] = json_decode($fieldData, false);
						}
						
					}
				}
				$i++;
			}
		}

		return $objects;

	}
	
	public function getAllObjects($tableName, $getChildObjs){

		$statement = $this->db->prepare("select * from $tableName");		
		$statement->execute();
		$objects = $statement->fetchAll();
		
		$i=0;
		$objectData = array();
		if(is_array($objects)){
			foreach($objects as $object){
				$objectData[$i] = json_decode($object['object_data'], true);
				$objectData[$i]['id'] = $object['id'];
				$objectData[$i]['date_created'] = $object['date_created'];
				$i++;
			}
		}
		// get child objects
		$getChildObjs = 1;
		if($getChildObjs == 1){
			
			$statement = $this->db->prepare("SELECT * FROM object_blueprints WHERE object_type = '$tableName'");
			$statement->execute();
			$blueprint = $statement->fetchAll()[0];

			foreach(json_decode($blueprint['blueprint'], true) as $fieldName => $field){
				
				$i=0;
				foreach($objectData as $retObj){
					
					if($field['type'] == 'objectId'){
					
						$objectData[$i][$fieldName] = $this->getObject($field['objectType'], $retObj[$fieldName]);
						// if child object has been deleted, do remove the empty object
						if($objectData[$i][$fieldName]['id'] == null){
							$objectData[$i][$fieldName] = array();
						}
											
					}elseif($field['type'] == 'objectIds'){
						
						$j=0;
						foreach($retObj[$fieldName] as $itemId){

							$objectData[$i][$fieldName][$j] = $this->getObject($field['objectType'], $itemId);
							
							// if child object has been deleted, do remove the empty object
							if($objectData[$i][$fieldName][$j]['id'] == null){
								unset($objectData[$i][$fieldName][$j]);
								$j = $j - 1;
							}
							$j++;
							
						}
						
					}
					
					$i++;
				
				}
				
			}
			
		}

		return $objectData;

	}


	// returns an array of the requested object		
	public function getByColumn($tableName, $columnValue, $columnName = 'id'){

		if($columnValue === 'is not null'){

			$statement = $this->db->prepare("select * from $tableName where $columnName is not null");

			$statement->execute();			

		}else{

			$statement = $this->db->prepare("select * from $tableName where $columnName = :columnValue");

			$statement->execute(array(':columnValue' => $columnValue));

		}		

		$objects = $statement->fetchAll();
		
		$i=0;
		if(is_array($objects)){
			foreach($objects as $object){
				
				if(is_array($object)){
					foreach($object as $fieldKey => $fieldData){
						
						if(is_string($fieldData) and is_array(json_decode($fieldData, true))){
							$objects[$i][$fieldKey] = json_decode($fieldData, true);
						}elseif(is_string($fieldData) and is_array(json_decode($fieldData, false))){
							$objects[$i][$fieldKey] = json_decode($fieldData, false);
						}
						
					}
				}
				$i++;
			}
		}

		return $objects;

	}
	
	public function getObject($objectType, $objectId){
		
		$statement = $this->db->prepare("SELECT * FROM ". $objectType ." WHERE id = :objectId");
		$statement->execute(array(':objectId' => $objectId));
		$object = $statement->fetchAll()[0];
		
		$objectData = json_decode($object['object_data'], true);
		$objectData['id'] = $object['id'];
		$objectData['date_created'] = $object['date_created'];
		
		// get child objects
		$getChildObjs = 1;
		if($getChildObjs == 1){
			
			$statement = $this->db->prepare("SELECT * FROM object_blueprints WHERE object_type = '$objectType'");
			$statement->execute();
			$blueprint = $statement->fetchAll()[0];

			foreach(json_decode($blueprint['blueprint'], true) as $fieldName => $field){
					
				if($field['type'] == 'objectId'){
				
					$objectData[$fieldName] = $this->getObject($field['objectType'], $objectData[$fieldName]);
					
					// if child object has been deleted, do remove the empty object
					if($objectData[$fieldName]['id'] == null){
						$objectData[$fieldName] = array();
					}
										
				}elseif($field['type'] == 'objectIds'){

					$j=0;
					foreach($objectData[$fieldName] as $itemId){

						$objectData[$fieldName][$j] = $this->getObject($field['objectType'], $itemId);
						
						// if child object has been deleted, do remove the empty object
						if($objectData[$fieldName][$j]['id'] == null){
							unset($objectData[$fieldName][$j]);
							$j = $j - 1;
						}
						$j++;
						
					}
					
				}
													
			}
			
		}
		
		return $objectData;
		
	}

	// returns an array of the requested object		
	public function getCreatedToday($tableName){

	

		$statement = $this->db->prepare("select * from $tableName where date(date_created) = date(now()) order by date_created");		

	

		$statement->execute();

		

		$objects = $statement->fetchAll();



		return $objects;

	

	}

	public function getJust($tableName, $whereClause, $selectionArray){

		

		

		$statement = $this->db->prepare("select id, " . implode(', ', $selectionArray) . " from $tableName where $whereClause");

		$statement->execute();

		

		$objects = $statement->fetchAll();

		

		$ret = array();

		

		$i = 0;

		if(is_array($objects)){

			foreach($objects as $object){

				

				$ret[$i]['id'] = $object['id'];

				

				if(is_array($selectionArray)){

					foreach($selectionArray as $selection){

						

						if(is_string($object[$selection]) and is_array(json_decode($object[$selection]))){

							$ret[$i][$selection] = json_decode($object[$selection]);

// 							$ret[$i][$selection] = 'test';

						}else{

							$ret[$i][$selection] = $object[$selection];

						}
						

					}

				}

				$i++;

			}

		}

		

		return $ret;

		

	}
	
	public function getJustWhere($tableName, $whereClause, $selectionArray){

		$statement = $this->db->prepare("select id, " . implode(', ', $selectionArray) . " from $tableName where $whereClause");
// 		var_dump($statement);
		$statement->execute();

		

		$objects = $statement->fetchAll();

		

		$ret = array();

		

		$i = 0;

		if(is_array($objects)){

			foreach($objects as $object){

				

				$ret[$i]['id'] = $object['id'];

				

				if(is_array($selectionArray)){

					foreach($selectionArray as $selection){
						
						$ret[$i][$selection] = $object[$selection];
						
					}

				}

				$i++;

			}

		}

		return $ret;

	}
	
	public function getBlueprint($objectType, $accessLevel = 0){
		
		$statement = $this->db->prepare("SELECT * FROM object_blueprints WHERE object_type = :objectType AND access_level = $accessLevel");

		$statement->execute(array(':objectType' => $objectType));

		$blueprint = $statement->fetchAll()[0];
				
		$ret = array(
			'id' => $blueprint['id'],
			'object_type' => $blueprint['object_type'],
			'access_level' => $blueprint['access_level'],
			'blueprint' => json_decode($blueprint['blueprint'], true),
		);
		
		$i=0;
		if(is_array($ret['blueprint'])){
			foreach($ret['blueprint'] as $key => $retDatum){
				
				if($retDatum['type'] == 'objectId' && !$retDatum['immutable'] or $retDatum['type'] == 'objectIds' && !$retDatum['immutable']){
					
					switch($retDatum['type']){
						case 'objectId':
						$ret['blueprint'][$key]['type'] = 'select';
						break;
						
						case 'objectIds':
						$ret['blueprint'][$key]['type'] = 'multi-select';
						break;
						
						default:
						break;
					}
					
					$ret['blueprint'][$key]['options'] = array();
					$options = $this->getAll($retDatum['objectType']);
					
					$j=0;
					if(is_array($options)){
						foreach($options as $option){
							
							$displayName = $ret['blueprint'][$key]['selectDisplay'];
							$displayItems = explode('[', $displayName);
							
							if(is_array($displayItems)){
								$k=1;
								foreach($displayItems as $displayItem){

									$displayName = str_replace('['. explode(']', $displayItems[$k])[0] .']', $option['object_data'][explode(']', $displayItems[$k])[0]], $displayName);									
									$k++;
									
								}
							}
							
							$ret['blueprint'][$key]['options'][$option['id']] = $displayName;
							
							$j++;
						}
					}
					
				}
				
				$i++;
			}
		}
		
		return $ret;
		
	}
	
	public function getWhere($tableName, $whereClause){

		

		$statement = $this->db->prepare("select * from $tableName where $whereClause");

		

		$statement->execute();

		

		$objects = $statement->fetchAll();

		

		return $objects;

		

	}

	// returns an array of the requested object		
	public function getMostRecent($tableName){

		$statement = $this->db->prepare("select * from $tableName order by date_created limit 1");		
		$statement->execute();
		$objects = $statement->fetchAll();

		return $objects;

	}

	// updates the object
	// returns true if the update was successful
	public function update($objectType, $objectId, $columnName, $columnValue){

		$statement = $this->db->prepare("update $objectType set $columnName = :columnValue where id = $objectId");
		$statement->bindParam(':columnValue', $columnValue, PDO::PARAM_STR);

		if($statement->execute()){

			return true;

		}else{
			print_r($statement->errorInfo());
		}

	}

}

?>