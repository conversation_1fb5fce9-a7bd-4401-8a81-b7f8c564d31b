<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

date_default_timezone_set('America/Chicago');
			
if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
	newrelic_background_job();
	newrelic_ignore_apdex();
}

$_REQUEST['pagodaAPIKey'] = 'voltzsoftware';

define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );

require APP_ROOT.'DBPATH.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'vendor/autoload.php';
require_once APP_ROOT.'_objects.php';

require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
require_once APP_ROOT.'_excel.php';

require_once '_config.php';
		
/*
echo 'test';
die();
*/
			
$scripts = scandir( __DIR__ . '/cron' );

// get current time to be compared to script schedule
$now = date('Y-m-d H:i:s');
$nowDayOfMonth = date('j');
$nowDayOfWeek = date('w');
$nowMin = date('i');
$nowHour = date('H');
$nowMin = floor($nowMin / 5 ) * 5;
if($nowMin < 10) {
    $nowMin = '0' . $nowMin;
}

//require_once __DIR__.'/cron/daily-9-10-tlbTrackingNumbers.php';
die();

// process all background scripts
if(is_array($scripts)){
	foreach($scripts as $script){

		$type = explode('-', $script)[0];

		switch($type){
			
			case 'daily':
			$hour = explode('-', $script)[1];
			$min = explode('-', $script)[2];

			// if it is time to run the script, run it
			if($nowHour == $hour and $nowMin == $min){
				
				require_once __DIR__.'/cron/'.$script;
			
			}

			break;

			case 'every':
			$timeQuantity = explode('-', $script)[1];
			$timeUnit = explode('-', $script)[2];
			if($timeQuantity == 'hour'){
				$min = explode('-', $script)[3];
			}

			if($timeUnit == 'hour' and $nowHour %$timeQuantity  == 0 and $min == $nowMin or $timeUnit == 'min' and $nowMin %$timeQuantity == 0){
				
				require_once __DIR__.'/cron/'.$script;
				
			}
			break;
			
			case 'weekly':
			$day = explode('-', $script)[1];
			$hour = explode('-', $script)[2];
			$min = explode('-', $script)[3];

			// if it is time to run the script, run it
			if($nowDayOfWeek == $day and $nowHour == $hour and $nowMin == $min){
				
				require_once __DIR__.'/cron/'.$script;
				
			}
			break;
			
			case 'monthly':
			$day = explode('-', $script)[1];
			$hour = explode('-', $script)[2];
			$min = explode('-', $script)[3];
							
			// if it is time to run the script, run it
			if($nowDayOfMonth == $day and $nowHour == $hour and $nowMin == $min){
				
				require_once __DIR__.'/cron/'.$script;
			
			}
			break;
			
			case 'test':
 			var_dump('starting... '.__DIR__.'/cron/'.$script);
			require_once __DIR__.'/cron/'.$script;
			break;
			
			default:
			break;
			
		}
		
		
	}
	
}
	
?>