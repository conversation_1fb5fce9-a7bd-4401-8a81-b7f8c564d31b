/* CSS Document default STYLESHEET.CSS */

body {
	caret-color: #000f12 !important;
	caret-style: block !important;
	background-color:white;
	font-family: 'Lato', 'Helvetica Neue', Arial, Helvetica, sans-serif !important;
	font-family: 'Cedarville Cursive', cursive;
	font-family: 'Raleway', sans-serif;
	font-family: 'Roboto', sans-serif;
	color:#4a5055 !important;
	font-size:14px !important;
}

h1, h2, h3, h4, h5, h6 {
	margin:0;
}

.link {
	color: #027eff !important;
}

.link:hover {
	text-decoration: underline;
	cursor: pointer;
}

#loader {
	z-index:9999 !important;
}

/* ========== AVATAR ========== */
.ui.items>.item .avatar img, .ui.items>.item img.avatar {
	max-width: 28px !important;
	max-height: 28px !important;
	width: auto !important;
	height: auto !important;
}


.gantt-chart-grey .bar-progress {
	fill:#767676 !important;
}

/* .gantt .bar-progress {
	fill: #027eff !important;
} */

.gantt-chart-red .bar-progress {
	fill:#DB2828 !important;
}

.gantt-chart-yellow .bar-progress{
	fill:#e79e47 !important;
}

.gantt-chart-green .bar-progress{
	fill:#18cc54 !important;
}


/* ========== COLLECTIONS HEADER ========== */

#collectionsHeader {
	margin-top:0 !important;
	border-bottom: 1px dashed #ebebeb !important;
}

#collectionsHeader.ui.secondary.menu .item {
	margin: 0 0.1em !important;
	padding: 10px !important;
	color: #2a2e3a !important;
}

#collectionsHeader.ui.secondary.menu .item:first-child {
	margin-left:0 !important;
}

#collectionsHeader.ui.secondary.menu .item:last-child {
	margin-right:0 !important;
}

/**************** BUTTON STYLING **************************/

.ui.tertiary.button:hover {
	box-shadow:none !important
}

.ui.button, .ui.basic.buttons, .ui.buttons .button:last-child:not(.icon), .ui.buttons .button:first-child:not(.icon), .swal2-styled {
	padding: 0.90em 0.95em 0.90em 0.95em !important;
}

.ui.basic.buttons .ui.disabled.button {
	border-color:#eeeeee !important;
}

.ui.button.rounded, .ui.basic.buttons.rounded {
	border-radius: 2rem !important;
}

.ui.button.rounded-small, .ui.basic.buttons.rounded-small {
	border-radius: 0.375rem !important;
}

.ui.button.transparent, .ui.basic.buttons.transparent {
	border-radius: 0 !important;
	border:none !important;
}

.ui.basic.button {
	font-weight: 800 !important;
	outline: 8px !important;
	transition: 0.3s;
}

.ui.basic.buttons {
	background-color: transparent !important;
	color:#4a5055 !important;
}

.ui.basic.buttons .button {
	border-left:1px solid #eeeeee;
	border-radius:0 !important;
}

.ui.basic.buttons .button {
	border-left:1px solid #eeeeee;
	border-radius:0 !important;
}

.ui.basic.buttons.transparent .button {
	border-left:none !important;
	border-radius:0 !important;
}

.ui.basic.buttons .button:first-child {
	border-left:0 !important;
	border-top-left-radius: 2rem !important;
	border-bottom-left-radius: 2rem !important;
}

.ui.basic.buttons .button:last-child {
	border-top-right-radius: 2rem !important;
	border-bottom-right-radius: 2rem !important;
}

.ui.basic.buttons.rounded .button:first-child {
	border-left:0 !important;
	border-top-left-radius: 2rem !important;
	border-bottom-left-radius: 2rem !important;
}

.ui.basic.buttons.rounded .button:last-child {
	border-top-right-radius: 2rem !important;
	border-bottom-right-radius: 2rem !important;
}

.ui.basic.buttons.rounded-small .button:first-child {
	border-left:0 !important;
	border-top-left-radius: 0.375rem !important;
	border-bottom-left-radius: 0.375rem !important;
}

.ui.basic.buttons.rounded-small .button:last-child {
	border-top-right-radius: 0.375rem !important;
	border-bottom-right-radius: 0.375rem !important;
}

.ui.basic.buttons.transparent .button:first-child {
	border-left:0 !important;
	border-top-left-radius: 0 !important;
	border-bottom-left-radius: 0 !important;
}

.ui.basic.buttons.transparent .button:last-child {
	border-top-right-radius: 0 !important;
	border-bottom-right-radius: 0 !important;
}

.ui.basic.buttons .button:hover:not(.loading):not(.running):not(.spinning), .ui.basic.button:hover:not(.loading):not(.running):not(.spinning) {
	box-shadow:none !important;
	background: #eeeeee !important;
}

.ui.basic.buttons .button:active:not(.loading):not(.running):not(.spinning), .ui.basic.button:active:not(.loading):not(.running):not(.spinning) {
	box-shadow:none !important;
	background:none !important;
}

.ui.basic.buttons .button.loading:hover, .ui.basic.button.loading:hover, .ui.basic.buttons .button.spinning:hover, .ui.basic.button.spinning:hover {
	box-shadow:none !important;
	background:none !important;
}

.ui.basic.buttons.transparent .button:hover:not(.loading):not(.running):not(.spinning), .ui.basic.button:hover:not(.loading):not(.running):not(.spinning) {
	box-shadow:none !important;
	background:none !important;
}

.ui.basic.buttons.transparent .button:active:not(.loading):not(.running):not(.spinning), .ui.basic.button:active:not(.loading):not(.running):not(.spinning) {
	box-shadow:none !important;
	background:none !important;
}

.ui.basic.buttons.transparent .button.loading:hover, .ui.basic.button.loading:hover, .ui.basic.buttons .button.spinning:hover, .ui.basic.button.spinning:hover {
	box-shadow:none !important;
	background:none !important;
}

.ui.basic.buttons .button.transparent:hover:not(.loading):not(.running):not(.spinning), .ui.basic.button:hover:not(.loading):not(.running):not(.spinning) {
	box-shadow:none !important;
	background:none !important;
}

.ui.basic.buttons .button.transparent:active:not(.loading):not(.running):not(.spinning), .ui.basic.button:active:not(.loading):not(.running):not(.spinning) {
	box-shadow:none !important;
	background:none !important;
}

.ui.basic.buttons .button.transparent.loading:hover, .ui.basic.button.loading:hover, .ui.basic.buttons .button.spinning:hover, .ui.basic.button.spinning:hover {
	box-shadow:none !important;
	background:none !important;
}

.ui.basic.buttons .button.running:hover, .ui.basic.button.running:hover {
	box-shadow:none !important;
}

.ui.basic.buttons .button i, .ui.basic.buttons .button .icon {
	margin:0 !important;
}

.ui.item.button:hover {
	background-color:transparent !important;
	color: black !important;
}

.ui.button.red:not(.basic) {
	background-color:#FF2F00 !important;
	color:#ffffff !important;
}

.ui.button.red:not(.basic):hover {
	background-color:#FF2F00 !important;
	color:#ffffff !important;
}

.ui.button.orange:not(.basic) {
	background-color:#FF8400 !important;
	color:#ffffff !important;
}

.ui.button.orange:not(.basic):hover {
	background-color:#FF8D12 !important;
	color:#ffffff !important;
}

.ui.button.yellow:not(.basic) {
	background-color:#F5D40F !important;
	color:#ffffff !important;
}

.ui.button.yellow:not(.basic):hover {
	background-color:#E8C70A !important;
	color:#ffffff !important;
}

.ui.button.olive:not(.basic) {
	background-color:#B5CC18 !important;
	color:#ffffff !important;
}

.ui.button.olive:not(.basic):hover {
	background-color:#C5DF1A !important;
	color:#ffffff !important;
}

.ui.button.green:not(.basic) {
	background-color:#18CC54 !important;
	color:#ffffff !important;
}

.ui.button.green:not(.basic):hover {
	background-color:#16BF4E !important;
	color:#ffffff !important;
}

.ui.button.teal:not(.basic) {
	background-color:#00CAC0 !important;
	color:#ffffff !important;
}

.ui.button.teal:not(.basic):hover {
	background-color:#00BDB4 !important;
	color:#ffffff !important;
}

.ui.button.blue:not(.basic) {
	background-color:#027eff !important;
	color:#ffffff !important;
}

.ui.button.blue:not(.basic):hover {
	background-color:#0072ED !important;
	color:#ffffff !important;
}

.ui.button.violet:not(.basic) {
	background-color:#7A52D1 !important;
	color:#ffffff !important;
}

.ui.button.violet:not(.basic):hover {
	background-color:#6D41CD !important;
	color:#ffffff !important;
}

.ui.button.purple:not(.basic) {
	background-color:#B04ED1 !important;
	color:#ffffff !important;
}

.ui.button.purple:not(.basic):hover {
	background-color:#A93CCD !important;
	color:#ffffff !important;
}

.ui.button.pink:not(.basic) {
	background-color:#FF379B !important;
	color:#ffffff !important;
}

.ui.button.pink:not(.basic):hover {
	background-color:#A93CCD !important;
	color:#ffffff !important;
}

.ui.button.brown:not(.basic) {
	background-color:#BD7C51 !important;
	color:#ffffff !important;
}

.ui.button.brown:not(.basic):hover {
	background-color:#B67245 !important;
	color:#ffffff !important;
}

.ui.button.grey:not(.basic) {
	background-color:#767676 !important;
	color:#ffffff !important;
}

.ui.button.grey:not(.basic):hover {
	background-color:#6D6D6D !important;
	color:#ffffff !important;
}

.ui.button.black:not(.basic) {
	background-color:#2a2e3a !important;
	color:#ffffff !important;
}

.ui.button.black:not(.basic):hover {
	background-color:#2A2C2E !important;
	color:#ffffff !important;
}

.ui.button.bento-blue:not(.basic) {
	background-color:#00b1ff !important;
	color:#ffffff !important;
	transition: 0.3s;
}

.ui.button.bento-blue:not(.basic):hover, .ui.button.bento-blue:not(.basic):focus {
	background-color:#00A6ED !important;
	color:#ffffff !important;
}

.transparent {
	background-color: transparent !important;
}

/**************** END BUTTON STYLING **************************/

/**************** WORKFLOW STYLING **************************/

.ui.steps .step:first-child {
	border-radius:2rem 0 0 2rem;
}

.ui.steps .step:last-child {
	border-radius:0 2rem 2rem 0;
}

.ui.steps .step > .icon {
	font-size:inherit !important;
	color: #ffffff !important;
	margin:0px 5px 0px 5px !important;
}

.ui.steps .step.empty > .icon {
	color: #2f2c2c !important
}

.ui.steps .step {
	padding: 0.75em 2em !important;
	cursor: pointer;
}

.ui.steps {
	border:none !important;
}

.ui.step {
	font-size:14px !important;
	margin-bottom:5px !important;
}

.ui.step, .step:after {
	background-color:#eeeeee !important;
}

.ui.step.empty:hover, .step.empty:hover:after {
	background-color:#dddddd !important;
}

.ui.step.red, .step.red:after {
	background-color:#FF2F00 !important;
	color:#ffffff !important;
}

.ui.step.orange, .step.orange:after {
	background-color:#FF8400 !important;
	color:#ffffff !important;
}

.ui.step.yellow, .step.yellow:after {
	background-color:#F5D40F !important;
	color:#ffffff !important;
}

.ui.step.olive, .step.olive:after {
	background-color:#B5CC18 !important;
	color:#ffffff !important;
}

.ui.step.green, .step.green:after {
	background-color:#18CC54 !important;
	color:#ffffff !important;
}

.ui.step.teal, .step.teal:after {
	background-color:#00CAC0 !important;
	color:#ffffff !important;
}

.ui.step.blue, .step.blue:after {
	background-color:#027eff !important;
	color:#ffffff !important;
}

.ui.step.violet, .step.violet:after {
	background-color:#7A52D1 !important;
	color:#ffffff !important;
}

.ui.step.purple, .step.purple:after {
	background-color:#B04ED1 !important;
	color:#ffffff !important;
}

.ui.step.pink, .step.pink:after {
	background-color:#FF379B !important;
	color:#ffffff !important;
}

.ui.step.brown, .step.brown:after {
	background-color:#BD7C51 !important;
	color:#ffffff !important;
}

.ui.step.grey, .step.grey:after {
	background-color:#767676 !important;
	color:#ffffff !important;
}

.ui.step.black, .step.black:after {
	background-color:#2a2e3a !important;
	color:#ffffff !important;
}

/**************** END WORKFLOW STYLING **************************/

/**************** ICON COLORS **************************/

.ui.icon.red:not(.button):not(.label):not(.message), .icon.red:not(.button):not(.label):not(.message), i.red {
	color:#FF2F00 !important;
}

.ui.icon.orange:not(.button):not(.label):not(.message), .icon.orange:not(.button):not(.label):not(.message), i.orange {
	color:#FF8400 !important;
}

.ui.icon.yellow:not(.button):not(.label):not(.message), .icon.yellow:not(.button):not(.label):not(.message), i.yellow {
	color:#F5D40F !important;
}

.ui.icon.olive:not(.button):not(.label):not(.message), .icon.olive:not(.button):not(.label):not(.message), i.olive {
	color:#B5CC18 !important;
}

.ui.icon.green:not(.button):not(.label):not(.message), .icon.green:not(.button):not(.label):not(.message), i.green {
	color:#18CC54 !important;
}

.ui.icon.teal:not(.button):not(.label):not(.message), .icon.teal:not(.button):not(.label):not(.message), i.teal {
	color:#00CAC0 !important;
}

.ui.icon.blue:not(.button):not(.label):not(.message), .icon.blue:not(.button):not(.label):not(.message), i.blue {
	color:#027eff !important;
}

.ui.icon.violet:not(.button):not(.label):not(.message), .icon.violet:not(.button):not(.label):not(.message), i.violet {
	color:#7A52D1 !important;
}

.ui.icon.purple:not(.button):not(.label):not(.message), .icon.purple:not(.button):not(.label):not(.message), i.purple {
	color:#B04ED1 !important;
}

.ui.icon.pink:not(.button):not(.label):not(.message), .icon.pink:not(.button):not(.label):not(.message), i.pink {
	color:#FF379B !important;
}

.ui.icon.brown:not(.button):not(.label):not(.message), .icon.brown:not(.button):not(.label):not(.message), i.brown {
	color:#BD7C51 !important;
}

.ui.icon.grey:not(.button):not(.label):not(.message), .icon.grey:not(.button):not(.label):not(.message), i.grey {
	color:#767676 !important;
}

.ui.icon.black:not(.button):not(.label):not(.message), .icon.black:not(.button):not(.label):not(.message), i.black {
	color:#2a2e3a !important;
}

/**************** END ICON COLORS **************************/

/**************** LABEL COLORS **************************/

.label {
	font-weight:normal !important;
}

.label i {
	color: #2a2e3a !important;
}

.ui.label.rounded {
	border-radius:2rem !important;
}

.ui.label.red, .label.red  {
	background-color:#FF2F00 !important;
	border-color:#FF2F00 !important;
	color:#ffffff !important;
}
.ui.label.red i, .label.red i {
	color:#ffffff !important;
}

.ui.label.orange, .label.orange {
	background-color:#FF8400 !important;
	border-color:#FF8400 !important;
	color:#ffffff !important;
}
.ui.label.orange i, .label.orange i {
	color:#ffffff !important;
}

.ui.label.yellow, .label.yellow {
	background-color:#F5D40F !important;
	border-color:#F5D40F !important;
	color:#2a2e3a !important;
}
.ui.label.yellow i, .label.yellow i {
	color:#2a2e3a !important;
}

.ui.label.olive, .label.olive {
	background-color:#B5CC18 !important;
	border-color:#B5CC18 !important;
	color:#2a2e3a !important;
}
.ui.label.olive i, .label.olive i {
	color:#2a2e3a !important;
}

.ui.label.green, .label.green {
	background-color:#18CC54 !important;
	border-color:#18CC54 !important;
	color:#ffffff !important;
}
.ui.label.green i, .label.green i {
	color:#ffffff !important;
}

.ui.label.teal, .label.teal {
	background-color:#00CAC0 !important;
	border-color:#00CAC0 !important;
	color:#ffffff !important;
}
.ui.label.teal i, .label.teal i {
	color:#ffffff !important;
}

.ui.label.blue, .label.blue {
	background-color:#027eff !important;
	border-color:#027eff !important;
	color:#ffffff !important;
}
.ui.label.blue i, .label.blue i {
	color:#ffffff !important;
}

.ui.label.violet, .label.violet {
	background-color:#7A52D1 !important;
	border-color:#7A52D1 !important;
	color:#ffffff !important;
}
.ui.label.violet i, .label.violet i {
	color:#ffffff !important;
}

.ui.label.purple, .label.purple {
	background-color:#B04ED1 !important;
	border-color:#B04ED1 !important;
	color:#ffffff !important;
}
.ui.label.purple i, .label.purple i {
	color:#ffffff !important;
}

.ui.label.pink, .label.pink {
	background-color:#FF379B !important;
	border-color:#FF379B !important;
	color:#ffffff !important;
}
.ui.label.pink i, .label.pink i {
	color:#ffffff !important;
}

.ui.label.brown, .label.brown {
	background-color:#BD7C51 !important;
	border-color:#BD7C51 !important;
	color:#ffffff !important;
}
.ui.label.brown i, .label.brown i {
	color:#ffffff !important;
}

.ui.label.grey, .label.grey {
	background-color:#767676 !important;
	border-color:#767676 !important;
	color:#ffffff !important;
}
.ui.label.grey i, .label.grey i {
	color:#ffffff !important;
}

.ui.label.black, label.black {
	background-color:#2a2e3a !important;
	border-color:#2a2e3a !important;
	color:#ffffff !important;
}
.ui.label.black i, .label.black i {
	color:#ffffff !important;
}

.ui.label.white, label.white {
	background-color:#ffffff !important;
	border-color:#ffffff !important;
	color:#2a2e3a !important;
}
.ui.label.white i, .label.white i {
	color:#2a2e3a !important;
}

.ui.label.white.muted, label.white.muted {
	background-color:#ffffff !important;
	border-color:#ffffff !important;
	color:#767676 !important;
}
.ui.label.white.muted i, .label.white.muted i {
	color:#767676 !important;
}

.ui.label.transparent, label.transparent {
	background-color:none !important;
	border-color:none !important;
	color:#2a2e3a !important;
}
.ui.label.transparent i, .label.transparent i {
	color:#2a2e3a !important;
}

.ui.label.transparent.muted, label.transparent.muted {
	background-color:none !important;
	border-color:nonte !important;
	color:#767676 !important;
}
.ui.label.transparent.muted i, .label.transparent.muted i {
	color:#767676 !important;
}

/**************** END LABEL COLORS **************************/

/**************** HEADER COLORS **************************/

.ui.header {
	color:#2b2f3a !important;
}

.ui.header.mini:not(h1):not(h2):not(h3):not(h4):not(h5):not(h6) {
	font-size:1em !important;
}

.ui.header > .icon {
	font-size:inherit !important;
}

.ui.header .icon:only-child {
	margin-right:2px !important;
	margin-top:-5px !important;
}

.ui.sub.header {
	font-size:14px !important;
	font-weight: 400 !important;
	text-transform: capitalize !important;
}

.toast-box .ui.header {
	color:#ffffff !important;
	font-size:1.2em !important;
}

.ui.toast-container.top.right {
	top:0.2em !important;
	right:0.2em !important;
}

.ui.header.red, .header.red {
	color:#FF2F00 !important;
}

.ui.header.orange, .header.orange {
	color:#FF8400 !important;
}

.ui.header.yellow, .header.yellow {
	color:#F5D40F !important;
}

.ui.header.olive, .header.olive {
	color:#B5CC18 !important;
}

.ui.header.green, .header.green {
	color:#18CC54 !important;
}

.ui.header.teal, .header.teal {
	color:#00CAC0 !important;
}

.ui.header.blue, .header.blue {
	color:#027eff !important;
}

.ui.header.violet, .header.violet {
	color:#7A52D1 !important;
}

.ui.header.purple, .header.purple {
	color:#B04ED1 !important;
}

.ui.header.pink, .header.pink {
	color:#FF379B !important;
}

.ui.header.brown, .header.brown {
	color:#BD7C51 !important;
}

.ui.header.grey, .header.grey {
	color:#4a5055 !important;
}

.ui.header.black, .header.black {
	color:#2a2e3a !important;
}

/**************** END HEADER COLORS **************************/

/**************** TAG COLORS **************************/

.ui.tag {
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
	border-radius:2rem !important;
	cursor:pointer !important;
	position:relative;
	padding-left:0.8rem !important;
	padding-right:0.8rem !important;
	margin-left:0 !important;
	font-size:14px !important;
	font-weight:300 !important;
	vertical-align:top !important;
	color:#2a2e3a !important;
}

.ui.tag::before, .tag::after {
	display:none;
}

.ui.tag > .icon {
	color:inherit !important;
	margin-right:2px !important;
}

.ui.tag.red:not(.icon):not(.deselected), .tag.red:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.red:not(.deselected) > .icon, .tag.red:not(.deselected) > .icon {
	color:#FF2F00 !important;
}

.ui.tag.red > .ui.avatar.image {
	border:2px solid #FF2F00 !important;
	background-color: #FF2F00 !important;
}

.ui.tag.orange:not(.icon):not(.deselected), .tag.orange:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.orange:not(.deselected) > .icon, .tag.orange:not(.deselected) > .icon {
	color:#FF8400 !important;
}

.ui.tag.orange > .ui.avatar.image {
	border:2px solid #FF8400 !important;
	background-color: #FF8400 !important;
}

.ui.tag.yellow:not(.icon):not(.deselected), .tag.yellow:not(.icon):not(.deselected) {
	color:#2a2e3a !important; /* this is a different yellow so it shows up better */
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.yellow:not(.deselected) > .icon, .tag.yellow:not(.deselected) > .icon {
	color:#F6BC0E !important;
}

.ui.tag.yellow > .ui.avatar.image {
	border:2px solid #F6BC0E;
	background-color: #F6BC0E;
}

.ui.tag.olive:not(.icon):not(.deselected), .tag.olive:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.olive:not(.deselected) > .icon, .tag.olive:not(.deselected) > .icon {
	color:#B5CC18 !important;
}

.ui.tag.olive > .ui.avatar.image {
	border:2px solid #B5CC18 !important;
	background-color: #B5CC18 !important;
}

.ui.tag.green:not(.icon):not(.deselected), .tag.green:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.green:not(.deselected) > .icon, .tag.green:not(.deselected) > .icon {
	color:#18CC54 !important;
}

.ui.tag.green > .ui.avatar.image {
	border:2px solid #18CC54 !important;
	background-color: #18CC54 !important;
}

.ui.tag.teal:not(.icon):not(.deselected), .tag.teal:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.teal:not(.deselected) > .icon, .tag.teal:not(.deselected) > .icon {
	color:#00CAC0 !important;
}

.ui.tag.teal > .ui.avatar.image {
	border:2px solid #00CAC0 !important;
	background-color: #00CAC0 !important;
}

.ui.tag.blue:not(.icon):not(.deselected), .tag.blue:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.blue:not(.deselected) > .icon, .tag.blue:not(.deselected) > .icon {
	color:#027eff !important;
}

.ui.tag.blue > .ui.avatar.image {
	border:2px solid #027eff !important;
	background-color: #027eff !important;
}

.ui.tag.violet:not(.icon):not(.deselected), .tag.violet:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.violet:not(.deselected) > .icon, .tag.violet:not(.deselected) > .icon {
	color:#7A52D1 !important;
}

.ui.tag.violet > .ui.avatar.image {
	border:2px solid #7A52D1 !important;
	background-color: #7A52D1 !important;
}

.ui.tag.purple:not(.icon):not(.deselected), .tag.purple:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.purple:not(.deselected) > .icon, .tag.purple:not(.deselected) > .icon {
	color:#B04ED1 !important;
}

.ui.tag.purple > .ui.avatar.image {
	border:2px solid #B04ED1 !important;
	background-color: #B04ED1 !important;
}

.ui.tag.pink:not(.icon):not(.deselected), .tag.pink:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.pink:not(.deselected) > .icon, .tag.pink:not(.deselected) > .icon {
	color:#FF379B !important;
}

.ui.tag.pink > .ui.avatar.image {
	border:2px solid #FF379B !important;
	background-color: #FF379B !important;
}

.ui.tag.brown:not(.icon):not(.deselected), .tag.brown:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.brown:not(.deselected) > .icon, .tag.brown:not(.deselected) > .icon {
	color:#BD7C51 !important;
}

.ui.tag.brown > .ui.avatar.image {
	border:2px solid #BD7C51 !important;
	background-color: #BD7C51 !important;
}

.ui.tag.grey:not(.icon):not(.deselected), .tag.grey:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.grey:not(.deselected) > .icon, .tag.grey:not(.deselected) > .icon {
	color:#767676 !important;
}

.ui.tag.grey > .ui.avatar.image {
	border:2px solid #767676 !important;
	background-color: #767676 !important;
}

.ui.tag.black:not(.icon):not(.deselected), .tag.black:not(.icon):not(.deselected) {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
}

.ui.tag.black:not(.deselected) > .icon, .tag.black:not(.deselected) > .icon {
	color:#2a2e3a !important;
}

.ui.tag.black > .ui.avatar.image {
	border:2px solid #2a2e3a !important;
	background-color: #2a2e3a !important;
}

.ui.tag.deselected, .tag.deselected {
	color:#2a2e3a !important;
	border:1px solid #dddddd !important;
	background-color:#ffffff !important;
	opacity:0.3 !important;
}

.ui.tag.companies, .ui.tag.users {
	margin:0 !important;
	padding:0 8px 0 0 !important;
}

.ui.tag.companies > span, .ui.tag.users > span {
	display:inline-block;
	margin-top:9px;
}

.ui.tag > .ui.avatar.image {
	margin:2px !important;
	margin-right:5px !important;
	width:1.9em !important;
	height:1.9em !important;
	vertical-align:top;
}

/**************** END TAG COLORS **************************/

/**************** TEXT COLORS **************************/

.ui.text.red  {
	color:#db3d43 !important;
}

.ui.text.orange {
	color:#f07d40 !important;
}

.ui.text.yellow {
	color:#e79e47 !important;
}

.ui.text.olive {
	color:#c1ce48 !important;
}

.ui.text.green {
	color:#4fbf60 !important;
}

.ui.text.teal {
	color:#3cbcb5 !important;
}

.ui.text.blue {
	color:#3a95d3 !important;
}

.ui.text.violet {
	color:#6c54ca !important;
}

.ui.text.purple {
	color:#a654cb !important;
}

.ui.text.pink {
	color:#de52a0 !important;
}

.ui.text.brown {
	color:#ac7555 !important;
}

.ui.text.grey {
	color:#767676 !important;
}

.ui.text.black {
	color:#1b1c1d !important;
}

/**************** END TEXT COLORS **************************/

/**************** ALERTS **************************/

.swal2-styled {
	padding: .625em 1em !important;
}

.swal2-title, .swal2-content {
	color:#2a2f3a !important;
	margin-bottom:15px;
}

.swal2-cancel {
	background-color:transparent !important;
	border:1px solid #E8E8E8 !important;
	color:#2a2f3a !important;
}

.swal2-deny {
	background-color:#767676 !important;
}

.swal2-confirm {
	background-color:#18CC54 !important;
}

.swal2-icon.swal2-success .swal2-success-ring {
	border-color:#18CC54 !important;
}
.swal2-icon.swal2-success [class^=swal2-success-line] {
	background-color:#18CC54 !important;
}

.swal2-icon.swal2-error{
	border-color:#FF2F00 !important;
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line] {
	background-color:#FF2F00 !important;
}

.swal2-icon.swal2-warning {
	border-color:#FF8400 !important;
	color:#FF8400 !important;
}

.swal2-icon.swal2-info {
	border-color:#027eff !important;
	color:#027eff !important;
}

.swal2-icon.swal2-question {
	border-color:#2a2f3a;
	color:#2a2f3a;
}

/**************** END ALERTS **************************/

/**************** END COACH-ON-CALL **************************/

.ui.bulleted.list {
	white-space: normal;
}

.ui.vertical.menu #closeMobileNavBtn {
	float:right;
	position: absolute;
	top:18px;
	right:0;
	font-size:30px;
}

.ui.inverted.menu .item:hover {
  color: rgba(255, 255, 255, 0.9) !important;
  background: rgba(255, 255, 255, 0.25) !important;
}

.ui.inverted.menu #sidebarTopSection .item {
  color:#000000 !important;
  font-size:20px;
  font-weight:600;
  padding-left:30px;
  padding-top:10px;
  padding-bottom:10px;
}

.ui.inverted.menu #sidebarTopSection .accordion .item {
  padding-bottom:0px;
}

.ui.inverted.menu #sidebarTopSection .accordion {
  padding-bottom:10px;
}

.ui.inverted.menu #sidebarTopSection .accordion .content .item {
  color:rgba(0,0,0,0.5) !important;
  font-size:14px;
  font-weight:400;
  padding-left:40px;
	padding-top:10px;
  padding-bottom:5px;
	line-height:1.2em;
}

.ui.inverted.menu #sidebarBottomSection .item {
  color:rgba(0,0,0,0.5) !important;
  font-size:14px;
  font-weight:400;
  padding-left:30px;
  padding-top:10px;
  padding-bottom:10px;
}

.ui.inverted.menu #sidebarSignOffSection .item {
  color:#ffffff !important;
  background-color:transparent !important;
  font-size:14px;
  font-weight:600;
  padding-left:30px;
  padding-top:15px;
  padding-bottom:15px;
}

.ui.inverted.menu #sidebarHelpDeskSection .item {
  color:#ffffff !important;
  background-color:transparent !important;
  font-size:14px;
  font-weight:400;
  padding-left:30px;
  padding-top:15px;
  padding-bottom:15px;
}

.ui.secondary.menu .item {
	font-weight:600;
	padding:0.78571429em 0.92857143em !important;
}

.ui.button:disabled, .ui.buttons .disabled.button:not(.basic), .ui.disabled.active.button, .ui.disabled.button, .ui.disabled.button:hover, .disabled {
	opacity:1 !important;
	cursor: not-allowed !important;
}

.ui.button.half-opacity:disabled, .ui.buttons .disabled.button.half-opacity:not(.basic), .ui.disabled.active.button.half-opacity, .ui.disabled.button.half-opacity, .ui.disabled.button.half-opacity:hover, .disabled.half-opacity {
	opacity:0.5 !important;
	cursor: not-allowed !important;
}

.ui.toggle.checkbox input:checked~label:before {
	background-color: #027eff !important;
}

.ui.ui.progress.success .bar {
	background-color: #18CC54 !important;
}

.ui.breadcrumb .divider {
	margin: 0;
}

.section.disabled.link {
	color:rgb(126, 126, 126) !important;
}

.ui.basic.segment {
	border: none !important;
}

.ui.basic.segment.white {
	background-color:#ffffff !important;
}

.round-border, .dropdown-round-border .dropdown:not(.icon), .round-border.segment, .round-border.ui.basic.segment, .round-border-on-child-field .field, .round-border-on-child-field .disp-field, .field input, .ui.form textarea {
	border:1px solid #dedede !important;
	border-radius:0.375rem !important;
	box-shadow:none !important;
}

.round-border-on-child-field .field .edge-field, .round-border-on-child-field .disp-field .edge-field, .round-border-on-child-field .field .dropdown {
	padding: 0.6em 1.2em 0.6em !important;
}

.showOverflow {
	overflow: auto !important;
}

.ui.cards > .card, .ui.card {
	-webkit-box-shadow: 0 1px 3px 0 #ebebeb, 0 0 0 1px #ebebeb;
    box-shadow: 0 1px 3px 0 #ebebeb, 0 0 0 1px #ebebeb;
}

.ui.menu .right.menu .dropdown:last-child .menu {
	margin-top: 0px !important;
}

.ui.secondary.menu .dropdown.item > .menu {
	margin-top: 0px !important;
}

.ui.labels > .label {
	margin: .15em !important;
}

/* ========== SINGLE ENTITY VIEW ========== */
.field-container.drag-picked-up {
    border-top: 1px dashed lightGray;
	border-bottom: 1px dashed lightGray;
	cursor: grabbing !important;
}
.view-tab.drag-picked-up {
    border-left: 1px dashed lightGray !important;
	border-right: 1px dashed lightGray !important;
	cursor: grabbing !important;
}
.list-view-item.gu-mirror, .field-container.gu-mirror, .view-tab.gu-mirror {
	opacity: 0 !important;
	cursor: grabbing !important;
}
/* ========== END SINGLE ENTITY VIEW ========== */

.required, .required input {
	border-color:#FF2F00 !important;
}

label {
	display: block;
    margin: 0 0 0.28571429rem 0;
    color: rgba(0, 0, 0, 0.87);
    font-size: 0.92857143em;
    font-weight: bold;
    text-transform: none;
}

.field-hover:hover {
/* 	background-color:#f5f3ed; */
/* 	border-top:1px dashed #e5deca; */
/* 	border-bottom:1px dashed #e5deca; */

/* 	-webkit-box-shadow:	0 0 10px #f5f3ed; */
/*     -moz-box-shadow:	0 0 10px #f5f3ed; */
/*     box-shadow:			0 0 10px #f5f3ed; */

}
.field-value[data-position="top left"][data-tooltip]:after {
/* 	margin-bottom: 10px; */
	left: 20px;
}
.field-value[data-inverted][data-position~="top"][data-tooltip]:before {
/* 	margin-bottom: 30px; */
	left: 40px;
}

.field-value input:hover, .field-value input:focus {
	background-color:#ffffff !important;
}

.ui.grid > .row > .column.desktopStackableGridColumn[max-width~="550px"] {
	width: 99% !important;
	margin: 0em 0em !important;
	-webkit-box-shadow: none !important;
	box-shadow: none !important;
	padding: 0rem 0rem !important;
}

.transition {
	-webkit-transition: width .3s;
	transition: width .3s;
}

.revealParent:hover .revealChild {
	display: inline !important;
}

.revealChild {
	display: none !important;
}

.revealChild:hover {
	color: #027eff !important;
}

.mainContainer {
	padding:0 20px 20px;
}

.mainViewContainer {
/* 	margin-top: 35px !important; */
	border-none;
/* 	box-shadow: 0 14px 28px rgba(0,0,0,0.20), 0 10px 10px rgba(0,0,0,0.04) !important; */
	height: auto !important;
/* 	background-color: white; */
}

.projectToolCard {
	border-top: none !important;
}

.small-container {
	margin: -1rem 1.5rem !important;
}

.pushMenuContainer {
	padding-right: 125px !important;
/* 	width: 100% !important; */
/* 	margin-right: 18.7%; */
}

.ui.card>.content, .ui.cards>.card>.content {
/* 	border-top: none !important; */
}

.ui.styled.accordion .accordion .content, .ui.styled.accordion .content {
	padding: .5em 1em !important;
}


.ui.progress {
	background-color:#ffffff;
	border:1px solid #dedede;
}

.ui.progress, .ui.progress .bar {
	border-radius:2rem !important;
}

.ui.progress .bar {
	height: 1.2em !important;
}

.ui.progress.light-grey .bar {
	background-color: #b8b8b8;
}

.ui.progress .bar > .progress {
	font-size: 0.85em !important;
	margin-top: -0.45em !important;
}

.ui.progress:last-child {
	margin: 0 !important;
}



.ui.slider.checkbox {
	padding-bottom: 7px !important;
}

.main-div-appColumn {
	background-color: #f8f8f8 !important;
}

.ui.search .prompt {
	border-radius:0.375rem !important;
	border: 1px solid #ebebeb !important;
	box-shadow: none !important;
}

.ui .menu {
	border-radius: 0px;
}

.ui.menu .ui.dropdown .menu > .item .icon:not(.dropdown) {
	margin:0 !important;
	padding:0 !important;
}

.ui.vertical.menu .dropdown.item > .icon {
	float:left;
	margin-left:0;
}

.ui.menu .item:hover {
	cursor: pointer;
    background: rgba(0, 0, 0, 0.03);
    color: rgba(0, 0, 0, 0.95);
}

.item .content {
/* 	padding: 0px !important; */
}

.ui.styled.accordion {
	width:100%;
}

.hidden {
	display: none !important;
}

.centerColHead > thead > tr > th {
	text-align: center !important;
}

.centerColContent > tbody > tr > td {
	text-align: center !important;
}

/* EDITABLE ITEM CSS */

.pda-editable-item {
	cursor: pointer;
/* 	border-bottom: 2px dotted gray; */
}

.pda-editable-item:after {
	font-family: FontAwesome;
	content: "\f040";
	color: transparent;
	transition : all .2s ease-out;
    background : transparent;
    position: relative;
    left: -10px;
    width: 0px;
    display: inline-block;
    width: 0px;
}
.pda-editable-item:hover::after{
    color: #EEDA5C;
    left: 10px;
    width: 20px;
}

/**************** TIMECLOCK STYLING **************************/

.keypad {
	/* width: 700px !important; */
	margin: 0 auto !important;
}

/* Keypad buttons */
.keypad > div:nth-child(2) > div {
	margin: 0 !important;
}

.keypad > div:nth-child(2) > div > div {
	padding: .5rem !important;
}

.keypad > div:nth-child(2) > div > div > div {
	border-radius: 10px;
}

.timeClock-form {
	font-size: 26px;
	height: 4rem !important;
	background-color: transparent;
	margin: 0 auto;
	text-align: center;
}

.rotate_icon {
	animation-name: rotateIcon;
	animation-duration: .5s;
	animation-timing-function: ease-out;
	transform: rotate(180deg);
}

/**************** Scheduling week view **************************/

.scheduling-week-view > thead > tr > th,
.scheduling-week-view > tbody > tr > td {
	width: 13.29% !important;
}

.scheduling-week-view > thead > tr > th:nth-child(1),
.scheduling-week-view > tbody > tr > td:nth-child(1) {
	width: 7% !important;
}

/* SEMANTIC OVERRIDE */

/* Was causing problem when using table colors. */
/*
.ui.table > thead > tr > th {
	background-color: white !important;
}
*/

.ui.table > tbody > tr > td {
	max-width: 500px !important;
/* 	overflow-x: auto !important; */
}

.override-dropdown-menu-alignment {
	left: 0 !important;
	right: auto !important;
}

@keyframes rotateIcon {

	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(180deg);
	}

}

/* ANIMATE CSS */
@charset "UTF-8";

/*!
 * animate.css -http://daneden.me/animate
 * Version - 3.5.2
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2017 Daniel Eden
 */

.animated{animation-duration:1s;animation-fill-mode:both}.animated.infinite{animation-iteration-count:infinite}.animated.hinge{animation-duration:2s}.animated.bounceIn,.animated.bounceOut,.animated.flipOutX,.animated.flipOutY{animation-duration:.75s}@keyframes bounce{0%,20%,53%,80%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1);transform:translateZ(0)}40%,43%{animation-timing-function:cubic-bezier(.755,.05,.855,.06);transform:translate3d(0,-30px,0)}70%{animation-timing-function:cubic-bezier(.755,.05,.855,.06);transform:translate3d(0,-15px,0)}90%{transform:translate3d(0,-4px,0)}}.bounce{animation-name:bounce;transform-origin:center bottom}@keyframes flash{0%,50%,to{opacity:1}25%,75%{opacity:0}}.flash{animation-name:flash}@keyframes pulse{0%{transform:scaleX(1)}50%{transform:scale3d(1.05,1.05,1.05)}to{transform:scaleX(1)}}.pulse{animation-name:pulse}@keyframes rubberBand{0%{transform:scaleX(1)}30%{transform:scale3d(1.25,.75,1)}40%{transform:scale3d(.75,1.25,1)}50%{transform:scale3d(1.15,.85,1)}65%{transform:scale3d(.95,1.05,1)}75%{transform:scale3d(1.05,.95,1)}to{transform:scaleX(1)}}.rubberBand{animation-name:rubberBand}@keyframes shake{0%,to{transform:translateZ(0)}10%,30%,50%,70%,90%{transform:translate3d(-10px,0,0)}20%,40%,60%,80%{transform:translate3d(10px,0,0)}}.shake{animation-name:shake}@keyframes headShake{0%{transform:translateX(0)}6.5%{transform:translateX(-6px) rotateY(-9deg)}18.5%{transform:translateX(5px) rotateY(7deg)}31.5%{transform:translateX(-3px) rotateY(-5deg)}43.5%{transform:translateX(2px) rotateY(3deg)}50%{transform:translateX(0)}}.headShake{animation-timing-function:ease-in-out;animation-name:headShake}@keyframes swing{20%{transform:rotate(15deg)}40%{transform:rotate(-10deg)}60%{transform:rotate(5deg)}80%{transform:rotate(-5deg)}to{transform:rotate(0deg)}}.swing{transform-origin:top center;animation-name:swing}@keyframes tada{0%{transform:scaleX(1)}10%,20%{transform:scale3d(.9,.9,.9) rotate(-3deg)}30%,50%,70%,90%{transform:scale3d(1.1,1.1,1.1) rotate(3deg)}40%,60%,80%{transform:scale3d(1.1,1.1,1.1) rotate(-3deg)}to{transform:scaleX(1)}}.tada{animation-name:tada}@keyframes wobble{0%{transform:none}15%{transform:translate3d(-25%,0,0) rotate(-5deg)}30%{transform:translate3d(20%,0,0) rotate(3deg)}45%{transform:translate3d(-15%,0,0) rotate(-3deg)}60%{transform:translate3d(10%,0,0) rotate(2deg)}75%{transform:translate3d(-5%,0,0) rotate(-1deg)}to{transform:none}}.wobble{animation-name:wobble}@keyframes jello{0%,11.1%,to{transform:none}22.2%{transform:skewX(-12.5deg) skewY(-12.5deg)}33.3%{transform:skewX(6.25deg) skewY(6.25deg)}44.4%{transform:skewX(-3.125deg) skewY(-3.125deg)}55.5%{transform:skewX(1.5625deg) skewY(1.5625deg)}66.6%{transform:skewX(-.78125deg) skewY(-.78125deg)}77.7%{transform:skewX(.390625deg) skewY(.390625deg)}88.8%{transform:skewX(-.1953125deg) skewY(-.1953125deg)}}.jello{animation-name:jello;transform-origin:center}@keyframes bounceIn{0%,20%,40%,60%,80%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:scale3d(.3,.3,.3)}20%{transform:scale3d(1.1,1.1,1.1)}40%{transform:scale3d(.9,.9,.9)}60%{opacity:1;transform:scale3d(1.03,1.03,1.03)}80%{transform:scale3d(.97,.97,.97)}to{opacity:1;transform:scaleX(1)}}.bounceIn{animation-name:bounceIn}@keyframes bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}.bounceInDown{animation-name:bounceInDown}@keyframes bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}.bounceInLeft{animation-name:bounceInLeft}@keyframes bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}.bounceInRight{animation-name:bounceInRight}@keyframes bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}.bounceInUp{animation-name:bounceInUp}@keyframes bounceOut{20%{transform:scale3d(.9,.9,.9)}50%,55%{opacity:1;transform:scale3d(1.1,1.1,1.1)}to{opacity:0;transform:scale3d(.3,.3,.3)}}.bounceOut{animation-name:bounceOut}@keyframes bounceOutDown{20%{transform:translate3d(0,10px,0)}40%,45%{opacity:1;transform:translate3d(0,-20px,0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.bounceOutDown{animation-name:bounceOutDown}@keyframes bounceOutLeft{20%{opacity:1;transform:translate3d(20px,0,0)}to{opacity:0;transform:translate3d(-2000px,0,0)}}.bounceOutLeft{animation-name:bounceOutLeft}@keyframes bounceOutRight{20%{opacity:1;transform:translate3d(-20px,0,0)}to{opacity:0;transform:translate3d(2000px,0,0)}}.bounceOutRight{animation-name:bounceOutRight}@keyframes bounceOutUp{20%{transform:translate3d(0,-10px,0)}40%,45%{opacity:1;transform:translate3d(0,20px,0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}.bounceOutUp{animation-name:bounceOutUp}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}.fadeIn{animation-name:fadeIn}@keyframes fadeInDown{0%{opacity:0;transform:translate3d(0,-100%,0)}to{opacity:1;transform:none}}.fadeInDown{animation-name:fadeInDown}@keyframes fadeInDownBig{0%{opacity:0;transform:translate3d(0,-2000px,0)}to{opacity:1;transform:none}}.fadeInDownBig{animation-name:fadeInDownBig}@keyframes fadeInLeft{0%{opacity:0;transform:translate3d(-100%,0,0)}to{opacity:1;transform:none}}.fadeInLeft{animation-name:fadeInLeft}@keyframes fadeInLeftBig{0%{opacity:0;transform:translate3d(-2000px,0,0)}to{opacity:1;transform:none}}.fadeInLeftBig{animation-name:fadeInLeftBig}@keyframes fadeInRight{0%{opacity:0;transform:translate3d(100%,0,0)}to{opacity:1;transform:none}}.fadeInRight{animation-name:fadeInRight}@keyframes fadeInRightBig{0%{opacity:0;transform:translate3d(2000px,0,0)}to{opacity:1;transform:none}}.fadeInRightBig{animation-name:fadeInRightBig}@keyframes fadeInUp{0%{opacity:0;transform:translate3d(0,100%,0)}to{opacity:1;transform:none}}.fadeInUp{animation-name:fadeInUp}@keyframes fadeInUpBig{0%{opacity:0;transform:translate3d(0,2000px,0)}to{opacity:1;transform:none}}.fadeInUpBig{animation-name:fadeInUpBig}@keyframes fadeOut{0%{opacity:1}to{opacity:0}}.fadeOut{animation-name:fadeOut}@keyframes fadeOutDown{0%{opacity:1}to{opacity:0;transform:translate3d(0,100%,0)}}.fadeOutDown{animation-name:fadeOutDown}@keyframes fadeOutDownBig{0%{opacity:1}to{opacity:0;transform:translate3d(0,2000px,0)}}.fadeOutDownBig{animation-name:fadeOutDownBig}@keyframes fadeOutLeft{0%{opacity:1}to{opacity:0;transform:translate3d(-100%,0,0)}}.fadeOutLeft{animation-name:fadeOutLeft}@keyframes fadeOutLeftBig{0%{opacity:1}to{opacity:0;transform:translate3d(-2000px,0,0)}}.fadeOutLeftBig{animation-name:fadeOutLeftBig}@keyframes fadeOutRight{0%{opacity:1}to{opacity:0;transform:translate3d(100%,0,0)}}.fadeOutRight{animation-name:fadeOutRight}@keyframes fadeOutRightBig{0%{opacity:1}to{opacity:0;transform:translate3d(2000px,0,0)}}.fadeOutRightBig{animation-name:fadeOutRightBig}@keyframes fadeOutUp{0%{opacity:1}to{opacity:0;transform:translate3d(0,-100%,0)}}.fadeOutUp{animation-name:fadeOutUp}@keyframes fadeOutUpBig{0%{opacity:1}to{opacity:0;transform:translate3d(0,-2000px,0)}}.fadeOutUpBig{animation-name:fadeOutUpBig}@keyframes flip{0%{transform:perspective(400px) rotateY(-1turn);animation-timing-function:ease-out}40%{transform:perspective(400px) translateZ(150px) rotateY(-190deg);animation-timing-function:ease-out}50%{transform:perspective(400px) translateZ(150px) rotateY(-170deg);animation-timing-function:ease-in}80%{transform:perspective(400px) scale3d(.95,.95,.95);animation-timing-function:ease-in}to{transform:perspective(400px);animation-timing-function:ease-in}}.animated.flip{-webkit-backface-visibility:visible;backface-visibility:visible;animation-name:flip}@keyframes flipInX{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}.flipInX{-webkit-backface-visibility:visible!important;backface-visibility:visible!important;animation-name:flipInX}@keyframes flipInY{0%{transform:perspective(400px) rotateY(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateY(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateY(10deg);opacity:1}80%{transform:perspective(400px) rotateY(-5deg)}to{transform:perspective(400px)}}.flipInY{-webkit-backface-visibility:visible!important;backface-visibility:visible!important;animation-name:flipInY}@keyframes flipOutX{0%{transform:perspective(400px)}30%{transform:perspective(400px) rotateX(-20deg);opacity:1}to{transform:perspective(400px) rotateX(90deg);opacity:0}}.flipOutX{animation-name:flipOutX;-webkit-backface-visibility:visible!important;backface-visibility:visible!important}@keyframes flipOutY{0%{transform:perspective(400px)}30%{transform:perspective(400px) rotateY(-15deg);opacity:1}to{transform:perspective(400px) rotateY(90deg);opacity:0}}.flipOutY{-webkit-backface-visibility:visible!important;backface-visibility:visible!important;animation-name:flipOutY}@keyframes lightSpeedIn{0%{transform:translate3d(100%,0,0) skewX(-30deg);opacity:0}60%{transform:skewX(20deg);opacity:1}80%{transform:skewX(-5deg);opacity:1}to{transform:none;opacity:1}}.lightSpeedIn{animation-name:lightSpeedIn;animation-timing-function:ease-out}@keyframes lightSpeedOut{0%{opacity:1}to{transform:translate3d(100%,0,0) skewX(30deg);opacity:0}}.lightSpeedOut{animation-name:lightSpeedOut;animation-timing-function:ease-in}@keyframes rotateIn{0%{transform-origin:center;transform:rotate(-200deg);opacity:0}to{transform-origin:center;transform:none;opacity:1}}.rotateIn{animation-name:rotateIn}@keyframes rotateInDownLeft{0%{transform-origin:left bottom;transform:rotate(-45deg);opacity:0}to{transform-origin:left bottom;transform:none;opacity:1}}.rotateInDownLeft{animation-name:rotateInDownLeft}@keyframes rotateInDownRight{0%{transform-origin:right bottom;transform:rotate(45deg);opacity:0}to{transform-origin:right bottom;transform:none;opacity:1}}.rotateInDownRight{animation-name:rotateInDownRight}@keyframes rotateInUpLeft{0%{transform-origin:left bottom;transform:rotate(45deg);opacity:0}to{transform-origin:left bottom;transform:none;opacity:1}}.rotateInUpLeft{animation-name:rotateInUpLeft}@keyframes rotateInUpRight{0%{transform-origin:right bottom;transform:rotate(-90deg);opacity:0}to{transform-origin:right bottom;transform:none;opacity:1}}.rotateInUpRight{animation-name:rotateInUpRight}@keyframes rotateOut{0%{transform-origin:center;opacity:1}to{transform-origin:center;transform:rotate(200deg);opacity:0}}.rotateOut{animation-name:rotateOut}@keyframes rotateOutDownLeft{0%{transform-origin:left bottom;opacity:1}to{transform-origin:left bottom;transform:rotate(45deg);opacity:0}}.rotateOutDownLeft{animation-name:rotateOutDownLeft}@keyframes rotateOutDownRight{0%{transform-origin:right bottom;opacity:1}to{transform-origin:right bottom;transform:rotate(-45deg);opacity:0}}.rotateOutDownRight{animation-name:rotateOutDownRight}@keyframes rotateOutUpLeft{0%{transform-origin:left bottom;opacity:1}to{transform-origin:left bottom;transform:rotate(-45deg);opacity:0}}.rotateOutUpLeft{animation-name:rotateOutUpLeft}@keyframes rotateOutUpRight{0%{transform-origin:right bottom;opacity:1}to{transform-origin:right bottom;transform:rotate(90deg);opacity:0}}.rotateOutUpRight{animation-name:rotateOutUpRight}@keyframes hinge{0%{transform-origin:top left;animation-timing-function:ease-in-out}20%,60%{transform:rotate(80deg);transform-origin:top left;animation-timing-function:ease-in-out}40%,80%{transform:rotate(60deg);transform-origin:top left;animation-timing-function:ease-in-out;opacity:1}to{transform:translate3d(0,700px,0);opacity:0}}.hinge{animation-name:hinge}@keyframes jackInTheBox{0%{opacity:0;transform:scale(.1) rotate(30deg);transform-origin:center bottom}50%{transform:rotate(-10deg)}70%{transform:rotate(3deg)}to{opacity:1;transform:scale(1)}}.jackInTheBox{animation-name:jackInTheBox}@keyframes rollIn{0%{opacity:0;transform:translate3d(-100%,0,0) rotate(-120deg)}to{opacity:1;transform:none}}.rollIn{animation-name:rollIn}@keyframes rollOut{0%{opacity:1}to{opacity:0;transform:translate3d(100%,0,0) rotate(120deg)}}.rollOut{animation-name:rollOut}@keyframes zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}.zoomIn{animation-name:zoomIn}@keyframes zoomInDown{0%{opacity:0;transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;transform:scale3d(.475,.475,.475) translate3d(0,60px,0);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}.zoomInDown{animation-name:zoomInDown}@keyframes zoomInLeft{0%{opacity:0;transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;transform:scale3d(.475,.475,.475) translate3d(10px,0,0);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}.zoomInLeft{animation-name:zoomInLeft}@keyframes zoomInRight{0%{opacity:0;transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}.zoomInRight{animation-name:zoomInRight}@keyframes zoomInUp{0%{opacity:0;transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}.zoomInUp{animation-name:zoomInUp}@keyframes zoomOut{0%{opacity:1}50%{opacity:0;transform:scale3d(.3,.3,.3)}to{opacity:0}}.zoomOut{animation-name:zoomOut}@keyframes zoomOutDown{40%{opacity:1;transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}to{opacity:0;transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);transform-origin:center bottom;animation-timing-function:cubic-bezier(.175,.885,.32,1)}}.zoomOutDown{animation-name:zoomOutDown}@keyframes zoomOutLeft{40%{opacity:1;transform:scale3d(.475,.475,.475) translate3d(42px,0,0)}to{opacity:0;transform:scale(.1) translate3d(-2000px,0,0);transform-origin:left center}}.zoomOutLeft{animation-name:zoomOutLeft}@keyframes zoomOutRight{40%{opacity:1;transform:scale3d(.475,.475,.475) translate3d(-42px,0,0)}to{opacity:0;transform:scale(.1) translate3d(2000px,0,0);transform-origin:right center}}.zoomOutRight{animation-name:zoomOutRight}@keyframes zoomOutUp{40%{opacity:1;transform:scale3d(.475,.475,.475) translate3d(0,60px,0);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}to{opacity:0;transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);transform-origin:center bottom;animation-timing-function:cubic-bezier(.175,.885,.32,1)}}.zoomOutUp{animation-name:zoomOutUp}@keyframes slideInDown{0%{transform:translate3d(0,-100%,0);visibility:visible}to{transform:translateZ(0)}}.slideInDown{animation-name:slideInDown}@keyframes slideInLeft{0%{transform:translate3d(-100%,0,0);visibility:visible}to{transform:translateZ(0)}}.slideInLeft{animation-name:slideInLeft}@keyframes slideInRight{0%{transform:translate3d(100%,0,0);visibility:visible}to{transform:translateZ(0)}}.slideInRight{animation-name:slideInRight}@keyframes slideInUp{0%{transform:translate3d(0,100%,0);visibility:visible}to{transform:translateZ(0)}}.slideInUp{animation-name:slideInUp}@keyframes slideOutDown{0%{transform:translateZ(0)}to{visibility:hidden;transform:translate3d(0,100%,0)}}.slideOutDown{animation-name:slideOutDown}@keyframes slideOutLeft{0%{transform:translateZ(0)}to{visibility:hidden;transform:translate3d(-100%,0,0)}}.slideOutLeft{animation-name:slideOutLeft}@keyframes slideOutRight{0%{transform:translateZ(0)}to{visibility:hidden;transform:translate3d(100%,0,0)}}.slideOutRight{animation-name:slideOutRight}@keyframes slideOutUp{0%{transform:translateZ(0)}to{visibility:hidden;transform:translate3d(0,-100%,0)}}.slideOutUp{animation-name:slideOutUp}

/***** PULSE CLASS *****/

.pulse {
	display: block;
	box-shadow: 0 0 0 rgba(255,0,0, 0.4);
	animation: pulse 2s infinite;
}

@-webkit-keyframes pulse {

	0% {
		-webkit-box-shadow: 0 0 0 0 rgba(255,0,0, 0.9);
	}

	70% {
		-webkit-box-shadow: 0 0 0 3px rgba(255,0,0, 0.4);
	}

	100% {
		-webkit-box-shadow: 0 0 0 0 rgba(255,0,0, 0.4);
	}

}

@keyframes pulse {

	0% {
		-moz-box-shadow: 0 0 0 0 rgba(255,0,0, 0.9);
		box-shadow: 0 0 0 0 rgba(255,0,0, 0.9);
	}

	70% {
		-moz-box-shadow: 0 0 0 3px rgba(255,0,0, 0.4);
		box-shadow: 0 0 0 3px rgba(255,0,0, 0.4);
	}

	100% {
		-moz-box-shadow: 0 0 0 0 rgba(255,0,0, 0.4);
		box-shadow: 0 0 0 0 rgba(255,0,0, 0.4);
	}

}

/* SPINNER */

.pda-spinner-x-small,
.pda-spinner-x-small > div,
.pda-spinner-x-small > div > div,
.pda-spinner,
.pda-spinner > div,
.pda-spinner > div > div,
.pda-spinner-medium,
.pda-spinner-medium > div,
.pda-spinner-medium > div > div,
.pda-spinner-large,
.pda-spinner-large > div,
.pda-spinner-large > div > div,
.pda-spinner-main,
.pda-spinner-main > div,
.pda-spinner-main > div > div {
	border-radius: 50%;
	-webkit-animation: spin 3s infinite linear;
    animation: spin 3s infinite linear;
}

/* EXTRA SMALL SPINNER */

.pda-spinner-x-small {
	border: 1px solid #878787 !important;
	border-top: 1px solid transparent !important;
	width: 12px !important;
	height: 12px !important;
	display: flex;
    align-items: center;
	margin: auto;
}

.pda-spinner-x-small > div {
	border: 1px solid #00c0f0 !important;
	border-top: 1px solid transparent !important;
	width: 8px !important;
	height: 8px !important;
	display: flex;
    align-items: center;
	margin: auto;
}

.pda-spinner-x-small > div > div {
	border: 1px solid #878787 !important;
	border-top: 1px solid transparent !important;
	width: 4px !important;
	height: 4px !important;
	margin: auto;
}

/* STANDARD SIZE SPINNER */

.pda-spinner {
	border: 1.5px solid #878787;
	border-top: 1.5px solid transparent;
	width: 18px;
	height: 18px;
	display: flex;
    align-items: center;
	margin: 0 auto;
}

.pda-spinner > div {
	border: 1.5px solid #00c0f0;
	border-top: 1.5px solid transparent;
	width: 12px;
	height: 12px;
	display: flex;
    align-items: center;
	margin: 0 auto;
}

.pda-spinner > div > div {
	border: 1.5px solid #878787;
	border-top: 1.5px solid transparent;
	width: 6px;
	height: 6px;
	align-items: center;
	margin: 0 auto;
}

/* MEDIUM SPINNER */

.pda-spinner-medium {
	border: 1.5px solid #878787 !important;
	border-top: 1.5px solid transparent !important;
	width: 20px !important;
	height: 20px !important;
	display: flex;
    align-items: center;
	margin: auto;
}

.pda-spinner-medium > div {
	border: 1.5px solid #00c0f0 !important;
	border-top: 1.5px solid transparent !important;
	width: 14px !important;
	height: 14px !important;
	display: flex;
    align-items: center;
	margin: auto;
}

.pda-spinner-medium > div > div {
	border: 1.5px solid #878787 !important;
	border-top: 1.5px solid transparent !important;
	width: 8px !important;
	height: 8px !important;
	margin: auto;
}

/* LARGE SPINNER */

.pda-spinner-large {
	border: 1.5px solid #878787 !important;
	border-top: 1.5px solid transparent !important;
	width: 22px !important;
	height: 22px !important;
	display: flex;
    align-items: center;
	margin: auto;
}

.pda-spinner-large > div {
	border: 1.5px solid #00c0f0 !important;
	border-top: 1.5px solid transparent !important;
	width: 16px !important;
	height: 16px !important;
	display: flex;
    align-items: center;
    margin: auto;
}

.pda-spinner-large > div > div {
	border: 1.5px solid #878787 !important;
	border-top: 1.5px solid transparent !important;
	width: 10px !important;
	height: 10px !important;
	margin: auto;
}

/* HUGE SPINNER */

.pda-spinner-main {
    border: 10px solid #878787 !important;
    border-top: 10px solid white !important;
    width: 110px !important;
    height: 110px !important;
    display: flex;
    align-items: center;
    margin: auto;
}

.pda-spinner-main > div {
    border: 8px solid #00c0f0 !important;
    border-top: 8px solid white !important;
	width: 80px !important;
	height: 80px !important;
    display: flex;
    align-items: center;
    margin: auto;
}

.pda-spinner-main > div > div {
    border: 10px solid #878787 !important;
    border-top: 10px solid white !important;
    width: 50px !important;
    height: 50px !important;
    margin: auto;
}

.pda-spinner-inverted {
	border-color: white !important;
	border-top: 1px solid transparent !important;
}

.pda-spinner-inverted > div > div {
    border-color: white !important;
    border-top: 1px solid transparent !important;
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* Move it (define the animation) */
@-moz-keyframes scroll-left {
	0%   { -moz-transform: translateX(100%); }
	100% { -moz-transform: translateX(-100%); }
}
@-webkit-keyframes scroll-left {
	0%   { -webkit-transform: translateX(100%); }
	100% { -webkit-transform: translateX(-100%); }
}
@keyframes scroll-left {
	0%   {
		-moz-transform: translateX(100%); /* Browser bug fix */
		-webkit-transform: translateX(100%); /* Browser bug fix */
		transform: translateX(100%);
	}
	100% {
		-moz-transform: translateX(-100%); /* Browser bug fix */
		-webkit-transform: translateX(-100%); /* Browser bug fix */
		transform: translateX(-100%);
	}
}

.webui-popover {
	border-radius: 2px;
	border-color:white;
	box-shadow: none;
	border: 1px solid #ABB7B7;
}
.webui-popover.bottom > .webui-arrow, .webui-popover.bottom-right > .webui-arrow, .webui-popover.bottom-left > .webui-arrow {

	border-bottom-color: #ABB7B7;
/* 	border-bottom: 2px solid #ABB7B7; */
}

/* TIMELINE STYLING */

.pda-timeline {
	width: 90%;
	border: 1px solid #0EABA7;
	position: relative;
	margin: 20px auto;
}

.pda-timeline > div {
	position: absolute;
	width: 20px;
	height: 20px;
	border-radius: 100px;
	background-color: white;
	border: 3px solid #0EABA7;
	transform: translate(-50%, -50%);
}

.pda-timeline > div:nth-child(1) {
	left: 20%;
}

.pda-timeline > div:nth-child(2) {
	left: 40%;
}

.pda-timeline > div:nth-child(3) {
	left: 60%;
}

.pda-timeline > div:nth-child(4) {
	left: 80%;
}

.pda-timeline > div > p {
	position: absolute;
	width: 200px;
	text-align: center;
	transform: translateX(-47%);
	top: 20px;
}

.pda-timeline > div > div {
	position: absolute;
	width: 200px;
	top: -55px;
	transform: translateX(-47%);
	text-align: center;
}

.pda-timeline > div > div > p {
	margin: 2px;
}

.workflow-timeline {
	width:80%;
	height:60px;
	position:relative;
	left:10%;
	overflow-x: visible;
}

.workflow-timeline-item {
	display:inline-block;
	text-align:center;
	-webkit-transition: border-color .2s; /* Safari */
    transition: border-color .2s;
/* 	transform: translateX(-50%); */
}
.workflow-timeline-item > p {
	position:relative;
	left:-50%;
}

.workflow-timeline-item-completed {
	border-top:2px solid #245466;
}
.workflow-timeline-item-last-completed {
	border-top:2px solid #0EABA7;
}
.workflow-timeline-item-in-progress {
	border-top:2px dashed #ABB7B7;
}
.workflow-timeline-item-not-started {
	border-top:2px dashed #ABB7B7;
}
.workflow-timeline-item::before {
	content: '';
	position:relative;
	left:-50%;
	top:-13.5px;
	display: inline-block;
	width: 25px;
	height: 25px;
	-moz-border-radius: 15px;
	-webkit-border-radius: 15px;
	border-radius: 15px;
	background-color: #ABB7B7;
	-webkit-transition: background-color .2s; /* Safari */
    transition: background-color .2s;
}
.workflow-timeline-item-completed::before {
	background-color: #245466;
}
.workflow-timeline-item-last-completed::before {
	background-color: #245466;
}
.workflow-timeline-item-in-progress::before {
	border: 2px solid #0EABA7;
	background-color: white;
}
.workflow-timeline-item-final-in-progress::before {
	border: 2px solid #0EABA7;
	background-color: white;
}
.workflow-timeline-item-not-started::before {
/* 	border: 2px dashed #ABB7B7; */
	background-color: #ABB7B7;
}

.inventory-category-nav-item {
	padding-left:6px;
	cursor:pointer;
	border-radius:4px;
	-webkit-transition: background-color .2s; /* Safari */
    transition: background-color .2s;
    color:#0e303c;
}
.inventory-category-nav-item:hover {
	background-color:#f6f6f6;
}
.inventory-category-nav-item-selected {
	padding-left:6px;
	cursor:pointer;
	border-radius:4px;
	-webkit-transition: background-color .2s; /* Safari */
    transition: background-color .2s;
	background-color:#0e303c;
	color:white;
}

/***** KAN-BAN CSS *****/

.kanban-table > thead > tr > th {
	padding: 0 !important;
	border-bottom: none !important;
}

/* GENERAL ROW STYLING */
.kanban-table > tbody > tr > td {
	border: none !important;
}

.ui.selectable.table.kanban-table tbody tr:hover,
.ui.table tbody tr td.selectable:hover {
	background-color: transparent !important;
}

.kanban-table > tbody > tr > td {
	padding: 0 !important;
}

.kanban-table > tbody > tr > td > div > div {
	padding: 5px;
}

/* FIRST ROW STYLING */
.kanban-table > tbody > tr:nth-child(1) > td > div > div > div {
	padding: 10px;
	border-top-left-radius: 5px !important;
	border-top-right-radius: 5px !important;
}

.kanban-table > tbody > tr:nth-child(1) > td > div > div > div > h5 {
	margin: 0 !important;
}

/* SECOND ROW STYLING */

.kanban-table > tbody > tr:nth-child(2) > td > div {
	height: 80vh !important;
}

.kanban-table > tbody > tr:nth-child(2) > td > div > div > div {
	height: 80vh !important;
	overflow-y: auto !important;
	overflow-x: hidden !important;
}

/* KANBAN CURSOR FOR DRAG AND DROP */

.grab {
	cursor: -webkit-grab !important;
}

/* dragged items */

.drag-over {
/* 	background-color:gray; */
/* 	background-color: #adf2ef; */
/* 	border: 2px dashed lightGray; */
	background-color: rgb(235, 235, 242) !important;
/* 	border-radius: 6px; */
	opacity: 1;
}

.item-fade-in-1 {
    -webkit-animation: fadeInDown .3s; /* Safari, Chrome and Opera > 12.1 */
       -moz-animation: fadeInDown .3s; /* Firefox < 16 */
        -ms-animation: fadeInDown .3s; /* Internet Explorer */
         -o-animation: fadeInDown .3s; /* Opera < 12.1 */
            animation: fadeInDown .3s;
}

.item-fade-in-2 {
    -webkit-animation: fadeInDown .9s; /* Safari, Chrome and Opera > 12.1 */
       -moz-animation: fadeInDown .9s; /* Firefox < 16 */
        -ms-animation: fadeInDown .9s; /* Internet Explorer */
         -o-animation: fadeInDown .9s; /* Opera < 12.1 */
            animation: fadeInDown .9s;
}

.item-fade-in-3 {
    -webkit-animation: fadeInDown 1.2s; /* Safari, Chrome and Opera > 12.1 */
       -moz-animation: fadeInDown 1.2s; /* Firefox < 16 */
        -ms-animation: fadeInDown 1.2s; /* Internet Explorer */
         -o-animation: fadeInDown 1.2s; /* Opera < 12.1 */
            animation: fadeInDown 1.2s;
}

.segment.withBorder {
	border: inherit !important;
}

/*
.segment {
	border-radius:0px !important;
}
.card {
	border-radius:0px !important;
}
.table, th {
	border-radius:0px !important;
}

.card {
	border-radius:0px !important;
}
*/

.board-item {
	cursor: grab;
/* 	border-radius:0px !important; */
}
.board-item:hover {
/* 	background-color:rgba(251, 251, 255, 1) !important; */
/* 	background:rgba(251, 251, 255, 1) !important; */

}
.board-item:active {
	cursor: grabbing !important;
}



@keyframes fadeInDown {
   0% {
      opacity: 0;
      transform: translateY(-5px);
/*        -webkit-box-shadow:0 0 20px rgba(0,0,0,0.3); */
/* 	    -moz-box-shadow:0 0 20px rgba(0,0,0,0.3); */
/* 	    box-shadow:0 0 20px rgba(0,0,0,0.3); */

   }
   100% {
      opacity: 1;
      transform: translateY(0);
/*       -webkit-box-shadow:0 0 0px rgba(0,0,0,0); */
/* 	    -moz-box-shadow:0 0 00px rgba(0,0,0,0); */
/* 	    box-shadow:0 0 0px rgba(0,0,0,0); */
   }
}

.delay-1 {
	transition-delay: 1s !important;
}
.delay-2 {
	transition-delay: 2s !important;
}
.delay-3 {
	transition-delay: 3s !important;
}

.ui.segments .segment, .ui.segment {
	font-size: inherit;
}

.ui.placeholder.segment {
	border-color:#ebebeb !important;
}

#navLogo {
	max-height:33px;
}

.remove-segment-padding-and-margins .ui.stackable.grid > div {
	padding:0 !important;
}

@media only screen and (max-width: 767.98px) {
	.ui.container, .mainContainer {
		padding: 0px;
		margin: 0px !important;
		margin-left: 0px !important;
		margin-right: 0px !important;
	}
	.ui.grid {
		margin-left: 0px !important;
		margin-right: 0px !important;
	}
	.ui.segment {
/* 		padding: 0 !important; */
		box-shadow: none !important;
		border-radius: 0px !important;
	}
	.modal-body {
		padding: 0px;
	}
	.ui.styled.accordion {
		border-radius: 0px;
		box-shadow: none;
	}
	.mobilePadding {
		padding:0px 1.5rem !important;
	}
	.noMobilePadding {
		padding:0px 0px !important;
	}
	.noMobileHeight {
		height: 10px;
	}
	.mobile-margin-top-10, .ui.stackable.grid > .wide.column.mobile-margin-top-10 {
		margin-top:10px !important;
	}
	.mobile-margin-bottom-15, .ui.stackable.grid > .wide.column.mobile-margin-bottom-15 {
		margin-bottom:15px !important;
	}
	.mobile-padding-top-5, .ui.stackable.grid > .wide.column.mobile-padding-top-5 {
		padding-top:5px !important;
	}
	.mobile-padding-top-10, .ui.stackable.grid > .wide.column.mobile-padding-top-10 {
		padding-top:10px !important;
	}
	.mobile-padding-top-15, .ui.stackable.grid > .wide.column.mobile-padding-top-15 {
		padding-top:15px !important;
	}
	.mobile-padding-top-30, .ui.stackable.grid > .wide.column.mobile-padding-top-30 {
		padding-top:30px !important;
	}
	.mobile-padding-bottom-2, .ui.stackable.grid > .wide.column.mobile-padding-bottom-2 {
		padding-bottom:2px !important;
	}
	.mobile-padding-bottom-5, .ui.stackable.grid > .wide.column.mobile-padding-bottom-5 {
		padding-bottom:5px !important;
	}
	.mobile-padding-bottom-10, .ui.stackable.grid > .wide.column.mobile-padding-bottom-10 {
		padding-bottom:10px !important;
	}
	.mobile-padding-bottom-15, .ui.stackable.grid > .wide.column.mobile-padding-bottom-15 {
		padding-bottom:15px !important;
	}
	.mobile-padding-bottom-30, .ui.stackable.grid > .wide.column.mobile-padding-bottom-30 {
		padding-bottom:30px !important;
	}
	.mobile-padding-left-0, .ui.stackable.grid > .wide.column.mobile-padding-left-0 {
		padding-left:0 !important;
	}
	.mobile-padding-right-0, .ui.stackable.grid > .wide.column.mobile-padding-right-0 {
		padding-right:0 !important;
	}
	#spaceViewCol {
		padding-right:1rem !important;
	}
	.small-container {
		margin: -1rem .5rem !important;
	}
	.ui.stackable.buttons{
		display: -webkit-box;
		display: -webkit-flex;
		display: -ms-flexbox;
		display: flex;
		flex-direction: column;
		width: 100%;
	}
	.ui.stackable.buttons > .button{
		width: 100%;
	}
	.pusher {
		margin-top:0 !important;
	}
	.main-div-bc {
		display:none !important;
	}

/*
  .revealChild {
	  display: block;
  }
*/
}

/* DECREASING BUTTON FONT SIZE ON MOBILE SCREENS */
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {

	.keypad-btn {
		font-size: 1rem !important;
	}

}



/* Semantic UI has these classes, however they're only applicable to*/
/* grids, containers, rows and columns.*/
/* plus, there isn't any `mobile hidden`, `X hidden` class.*/
/* this snippet is using the same class names and same approach*/
/* plus a bit more but to all elements.*/
/* see https://github.com/Semantic-Org/Semantic-UI/issues/1114*/

/* Mobile */
@media only screen and (max-width: 767px) {
  [class*="mobile hidden"],
  [class*="tablet only"]:not(.mobile),
  [class*="computer only"]:not(.mobile),
  [class*="large screen only"]:not(.mobile),
  [class*="widescreen only"]:not(.mobile),
  [class*="or lower hidden"] {
    display: none !important;
  }
  .mainViewContainer {
		margin: 7px 4px !important;
		border-none;
	}
}

/* Tablet / iPad Portrait */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  [class*="mobile only"]:not(.tablet),
  [class*="tablet hidden"],
  [class*="computer only"]:not(.tablet),
  [class*="large screen only"]:not(.tablet),
  [class*="widescreen only"]:not(.tablet),
  [class*="or lower hidden"]:not(.mobile) {
    display: none !important;
  }
}

/* Computer / Desktop / iPad Landscape */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  [class*="mobile only"]:not(.computer),
  [class*="tablet only"]:not(.computer),
  [class*="computer hidden"],
  [class*="large screen only"]:not(.computer),
  [class*="widescreen only"]:not(.computer),
  [class*="or lower hidden"]:not(.tablet):not(.mobile) {
    display: none !important;
  }
}

/* Large Monitor */
@media only screen and (min-width: 1200px) and (max-width: 1919px) {
  [class*="mobile only"]:not([class*="large screen"]),
  [class*="tablet only"]:not([class*="large screen"]),
  [class*="computer only"]:not([class*="large screen"]),
  [class*="large screen hidden"],
  [class*="widescreen only"]:not([class*="large screen"]),
  [class*="or lower hidden"]:not(.computer):not(.tablet):not(.mobile) {
    display: none !important;
  }
}

/* Widescreen Monitor */
@media only screen and (min-width: 1920px) {
  [class*="mobile only"]:not([class*="widescreen"]),
  [class*="tablet only"]:not([class*="widescreen"]),
  [class*="computer only"]:not([class*="widescreen"]),
  [class*="large screen only"]:not([class*="widescreen"]),
  [class*="widescreen hidden"],
  [class*="widescreen or lower hidden"] {
    display: none !important;
  }
}

/* Custom Helper Classes */

.linkHover:hover {
	color: #027eff !important;
	text-decoration: underline;
	cursor: pointer;
}

.show-pointer {
	cursor: pointer;
}

.show-grab {
	cursor: -webkit-grab;
}

.bento-color {
	color: #027eff !important;
}

.bento-color-hover:hover {
	color: #027eff !important;
}

/* Fields */
.field, .disp-field {
/* 	padding: 6px; */
/* 	margin: 6px; */
	border-radius: 0.375rem;
	cursor: auto;
	min-height: 2em;
}

.field:hover {
	background-color:#ffffff !important;
	cursor:pointer;
}

.secondary .field:hover {
	background-color:#ffffff !important;
}
.grey.secondary .field:hover {
	background-color:transparent !important;
}
.ui.grid {
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}
.ui .grid .ui.stackable.grid {
	margin-left: 0px !important;
	margin-right: 0px !important;
}
.field.active:hover {
/* 	background-color: transparent !important; */
/* 	cursor: pointer; */
	transition : all .2s ease-out;
/* box-shadow: 1px 2px 3px rgba(0,0,0,0.12); */
/* 	background-color: rgb(250, 250, 250) !important; */
}
input, textarea, .dropdown, select {
	color:#4a5055 !important;
}
.link {
	padding: 8px !important;
	border-radius: 4px;
	transition : all .2s ease-out;
/* 	background-color: transparent !important; */
}
.link:hover {
/* 	background-color: rgb(247, 245, 245) !important; */
	text-decoration: none;
}

.ui.form .field > label, label {
	margin-bottom:0 !important;
}


.a {
	padding: 8px !important;
	border-radius: 4px;
	transition : all .2s ease-out;
	background-color: transparent !important;
}
a:hover {
	/* background-color: rgb(247, 245, 245) !important; */
	text-decoration: none;
}

.menu {
	box-shadow: none !important;
	transition: all 0.3s cubic-bezier(.25,.8,.25,1);
	border: none !important;
	outline: transparent;
}
.menu:hover {
/* 	outline: 1px dashed rgb(247, 245, 245) !important; */
	text-decoration: none;
}
#bentoDocumentEditorPreviewerToolbar .item {
	transition : all .2s ease-out;
	padding:8px 15px 8px 15px !important;
	box-shadow: none !important;
}
#bentoDocumentEditorPreviewerToolbar .item:hover {
	background-color: rgb(247, 245, 245) !important;
	text-decoration: none;
}

.card {
/* 	 box-shadow: 0 0 0px rgba(0,0,0,0.4) !important; */
	 transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}
.card:hover {
/* 	box-shadow: 0 0 1px rgba(0,0,0,0.1) !important; */
	box-shadow: 0 0 3px rgba(0,0,0,0.6) !important;
}
.card > .field:hover {
	background-color: transparent !important;
}

/* FONTS */
h1, h2, h3, h4, h5 {
/* 	font-family: 'Raleway', sans-serif !important; */
}
.header {
/* 	font-family: 'Raleway', sans-serif !important; */
}
p, a {
/* 	font-family: 'Roboto', sans-serif !important; */
}
.styled.accordion {
	box-shadow: 0px 0px 1px rgba(0,  0,  0, 0.4) !important;
/*
	outline:none;
	box-shadow:none !important;
	transition: all 0.3s cubic-bezier(.25,.8,.25,1);
*/
}

.styled.accordion:hover {
/* 	box-shadow: 0 7px 14px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22) !important; */
}

.bentoBreadcrumbs .item {
	border-radius: 0 !important;
}

.bentoBreadcrumbs .item:hover {
	color: #1e70bf !important;
}

.label {
/* 	box-shadow: 0 0 1px rgba(0,0,0,0.6) !important;  */
}

.label:hover .overlay {
	display: inline;
}

.overlay {
	display: none;
}

.overlay:hover {
	color: #027eff !important;
}

.label > .overlay {
    width:100%;
    height:100%;
    margin:0px;
    left: 0;
    top: 0;
    border-radius: .28571429rem;
    position:absolute;
    background-color:rgb(0, 20, 40);
    opacity:0.05;
/*     border-radius:30px; */
}

.list-view-item {

}
.list-view-item:hover {
	background-color:rgb(250,250,250) !important;
}

.ql-editor {
	padding: 8px !important;
	font-size:14px;
}
.text.ql-editor blockquote {
	border-left: 4px solid #ccc !important;
	margin-bottom: 5px !important;
	margin-top: 5px !important;
	padding-left: 16px !important;
}

.ql-container.ql-snow {
	padding:0px !important;
}
.ql-container  {
	border: 0px !important;
	font-family: inherit !important;
	/* height:auto !important; */
}
.ql-toolbar {
	text-align: center;
}
.ui.sortable.table thead th {
/*
	font-weight: 300;
	color: gray;
*/
}
.ql-editor.ql-blank::before {
	left: 8px !important;
}
.ql-editor ol, .ql-editor ul {
	padding-left: 0px !important;
}
.ql-editor img {
	max-width: 100%;
}
a.bento-link {
	cursor: pointer;
	text-decoration: underline;
}
a.bento-link:hover {
	color:#027eff !important;
}
.suggestion-prompt {
	width: 100%;
	padding: 6px;
	top: -6px;
	border-radius: 4px;
	background-color: rgb(245,245,245) !important;
	box-shadow: 0 2px 4px 0 rgba(34, 36, 38, 0.12);
}
s {
	color: rgba(175, 175, 175, 1) !important;
}
em {
/* 	color: rgba(0, 0, 0, .8) !important; */
/* 	size: small; */
/* 	font-weight: 250; */
/* 	font-size: .9em; */
}
.modal:active {
/* 	min-height: 90% !important; */
}

.ui.modal .scrolling.content {
	min-height: calc(80vh - 1em);
}

.ui.modal .modal-body {
	padding:0 !important;
}

p {
/* 	font-size: 1.5rem !important; */
/* 	line-height: 1.6rem !important; */
}

.truncate {
  display: block !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* LIST VIEW */
.selected-list-item {
	background-color: #f6f6f6 !important;
	background: #f6f6f6 !important;
	border-radius: 4px !important;
}
.selected-list-item:hover {
	background-color: #f2f2f2 !important;
	background: #f2f2f2 !important;
}

.top.fixed.menu .item {
	padding-top: 0px !important;
	padding-bottom: 0px !important;
}
/* BREADCRUMBS */
.inactive-breadcrumb {
/* 	color: rgba(131, 131, 131, 1) !important; */
/* 	font-weight: 425 !important; */
}

.inactive-breadcrumb:hover, .active-breadcrumb:hover {
/* 	color: #767676 !important; */
	background-color: transparent !important;
}
.active-breadcrumb {
/* 	font-weight: bold !important; */
	font-weight: 500 !important;
/* 	color: black !important; */
}

.ui.fixed.sticky {
  margin-top: 40px !important;
  margin-left:0px !important;
  background-color: white !important;
}

.dimmer {
/* 	background-color: rgba(0,0,0,.25) !important; */
/* 	background-color: rgba(255,255,255,.95) !important; */
/* 	background-color: rgba(0,0,0,.25) !important; */
/* 	background-color: red !important; */
}
.ql-syntax {
	background-color: rgb(245, 245, 245) !important;
	color:rgb(0, 20, 40) !important;
}
.ql-toolbar.ql-snow {
	border: none !important;
	background-color:rgba(0,0,0,.025);
	width: min-content;
	margin-left:-1em;
	margin-right:-1em;
}
.ql-toolbar.ql-snow.fixed {
	-webkit-box-shadow:	0 4px 6px rgba(0,0,0,.05);
    -moz-box-shadow:		0 4px 6px rgba(0,0,0,.05);
    box-shadow:			0 4px px rgba(0,0,0,.05);
    transition : all .2s ease-out;
    border: none !important;
}
.ql-toolbar {
	visibility: hidden !important;
	height: 	0px !important;
	padding:	0px !important;
}
.show-toolbar .ql-toolbar {
	visibility: visible !important;
	height: 	100% !important;
	padding: 	8px !important;
}
/* The tooltip used in the link module */
.ql-snow .ql-tooltip {
	border-radius: 4px !important;
	border: 0px !important;
}
.ql-formats > button {
	margin: .25em !important;
}
.ql-formats > button:hover {
	background-color: rgba(233, 244, 244, 1) !important;
	border-radius: 4em !important;
	color: black !important;
}

ul[data-checked="true"] {
	color: #9D9D9D !important;
	color: #697C89 !important;
	text-decoration: line-through !important;
}
td > ul {
	padding-top:0px !important;
	padding-bottom:0px !important;
	padding-left: 0px !important;
	margin-top:0px !important;
	margin-bottom:0px !important;
}
td > ul > li {
	padding-left: 1.5em !important;
}
td > ul > li::marker {
	content: '' !important;
}
.ql-editor ul > li::before, td > ul > li::before {
	content: '–' !important;
}
ul[data-checked=false] > li::before {
    font-family: 'outline-icons' !important;
    content: "\f0c8" !important;
    margin-right: .5em !important;
}
.ql-editor ul > li::before, td > ul > li::before {
	font-size:1.25em !important;
	position: relative;
	bottom: -1.5px;
	left: 0;
	margin: 0;
}
td > ul > li::before {
	left: -0.75em !important;
}
ul[data-checked=true] > li::before {
    font-family: 'Icons';
    content: "\f14a" !important;
    margin-right: .5em !important;
}
ul[data-checked=true] > li::before, .bento-checked {
	color: #697C89 !important;
}
ul[data-checked=false] > li::before, .bento-unchecked {
	color: #9D9D9D !important;
}
.bento-merge-tag {
	border: 1px solid lightGray !important;
	border-radius: 4px !important;
	background-color: transparent !important;
	color: inherit !important;
	cursor: pointer;
	font-weight:initial !important;
	line-height:1.4em !important;
}

.field-manager {
	padding: 8px !important;
	width: 100% !important;
	border-radius: 4px !important;
	color: rgba(175, 175, 175, 1) !important;
	transition : all .2s ease-out !important;
	cursor: default !important;
/* 	text-align: right !important; */
}

th .field-manager {
	width: auto !important;
}
.field-manager.edit {
	cursor: pointer !important;
}
.field-manager.edit:hover {
	background-color: rgb(245,245,245) !important;
	box-shadow: inset 0 0 3px rgba(0,0,0,0.1);
}

.field-value input {
	padding: 10px !important;
	font-size: 14px !important;
}

.ui.floater {
	position: absolute;
	z-index: 10000;
	display: flex;
	flex-direction: column;
}

.ui.floater > .floater-container {
	overflow-y: scroll;
	overflow-x: auto;
}

.ui.floater:before {
	position: absolute;
    content: '';
	left: 1em;
	top: -5px;
    width: 0.71428571em;
    height: 0.71428571em;
    background: #FFFFFF;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    z-index: 1901;
    -webkit-box-shadow: -1px -1px 0 0 #bababc;
    box-shadow: -1px -1px 0 0 #bababc;
}

.ui.floater.arrow-float-left:before {
	left: 1em;
	right: auto;
}

.ui.floater.arrow-float-right:before {
	right: 1em;
	left: auto;
}

.ui.popup, .ui.search > .results, .ui.floater {
	background-color:#ffffff;
	box-shadow:0px 1px 10px rgba(0,0,0,0.08) !important;
	border:1px solid #ebebeb !important;
}

.ui.popup, .ui.floater > .floater-container {
	padding:0.833em 1em;
}

.ui.bottom.center.popup:before {
	box-shadow:-1px -1px 0 0 rgba(0,0,0,0.08) !important;
}

/* datepicker */
.ui.popup.calendar th {
	color: rgba(0, 0, 0, .2);
}

.ui.popup.calendar tbody .link {
	border: 0px solid transparent !important;
	border-radius: 2em !important;
	border-radius: 4px !important;
	color: rgba(0, 0, 0, .8);
}
.ui.popup.calendar td:hover {
	border: 0px solid transparent !important;
}
.ui.popup.calendar {
	border: none !important;
}
.ui.popup.calendar::before {
	border: none !important;
	content: none;
}
.ui.popup.calendar table {
/* 	padding: 4px !important; */
}
.ui.calendar .ui.table tr .link {
	border:none !important;
}
.ui.popup.calendar .link.today {
	background-color: lightGray !important;
	border-radius: 2em !important;
}
.ui.popup.calendar tbody .link.focus {
	background-color: rgba(33, 133, 208, 0.3) !important;
	border: 0 !important;
	outline: 0 !important;
	box-shadow: none !important;
}
.ui.popup.calendar tbody .link.active {
	background-color: #2185D0 !important;
	color: white !important;
	border: none !important;
	outline: 0 !important;
	font-weight: bold !important;
}
.ui.popup.calendar table th tr {
	padding-bottom: 12px !important;
}
.ui.calendar .ui.table tr .link.range {
	border-radius: 0px !important;
}

#topNav {

}

#topNav .ui.breadcrumb a {
	color: #2a2e3a !important;
}

#topNav .ui.breadcrumb a.active {
	color: #027eff !important;
}

#leftNav {
	border-right: 1px solid #f8f8f8 !important;
	background-color: #f8f8f8 !important;
	padding-top:0px;
	padding-bottom:0px;
	padding-left:0px;
	padding-right:0px;
	height:100vh;
	width:65px !important;
}

#leftNav .ui.avatar.image {
	height:21px !important;
	width:21px !important;
	margin:0 !important;
}

#leftNav .middleNavigation > .item:not(:last-child), #leftNav .bottomNavigation > .item {
	margin-bottom:0px !important;
}

#leftNav .notificationCount {
	top:5px !important;
	padding:3px;
	right:auto !important;
	opacity:1 !important;
}

#rightTray {
	border-right:1px solid #ebebeb;
	background-color:#f8f8f8;
	padding-top:55px;
	padding-bottom:0px;
	padding-left:0px;
	padding-right:0px;
	height:100vh;
	width:65px !important;
	border-left:1px solid #ebebeb;
}

#rightTrayBoxView {
	height:100vh !important;
	overflow-x: hidden;
	overflow-y: scroll;
}

#rightTrayBoxView.open {
	width:360px !important;
}

.rightTrayBoxViewContainer {
	padding-top:63px !important;
	padding-bottom:20px;
}

.rightTrayBoxViewIcon:hover, .rightTrayBoxViewIcon.active {
	color: #027eff !important;
	cursor: pointer;
}

.rightTrayCard, .rightTrayCard:hover {
	box-shadow: none !important;
}

.rightTrayCard > .content {
	padding:1em 0em !important;
}

.rightTrayCard .hide-scrollbar {
	padding:0em !important;
}

.rightTrayCard .body {
	padding:0em !important;
}

.rightTrayCard .pools {
	display:none;
}

#closeRightTrayButton {
	position: absolute;
	cursor:pointer; top: 75px; right: 48px; z-index:999;
}

#spaceNavigationCol {
	background-color: #ffffff !important;
	height:100vh;
	overflow-y:auto;
	padding:0;
	padding-top:70px;
	padding-bottom:20px;
	-ms-overflow-style: none;
	scrollbar-width: none;
}

#spaceNavigationCol::-webkit-scrollbar {
	display: none;
  }

#spaceNavigationCol .segment .menu {
	margin-left:0 !important;
}

#spaceNavigationCol .segment .menu .item:hover, #spaceNavigationCol .segment .menu .item.active {
	background-color: #f4f4f4 !important;
}

#openMenuButton, #closeRightTrayButton {
	visibility:hidden;
}

#openMenuButton i, #closeRightTrayButton i {
	background-color: white;
	width: 25px;
	height: 25px;
	border: 1px solid #ebebeb;
	border-radius: 50%;
	padding: 5px;
}

#openMenuButton:hover i, #closeRightTrayButton:hover i {
	color:#ffffff;
	background-color: #027eff !important;
	border-color: #027eff !important;
}

#spaceViewCol {
	overflow-x:hidden;
	overflow-y:auto;
	margin-top:54px;
	margin-bottom:0px;
	padding-top:22px;
	padding-left:28px;
	padding-right:28px;
	display:flex;
	flex-direction:column;
	flex: 1 1 auto;
	background-color:#ffffff;
	border-left:1px solid #ebebeb;
	width:0px;
	height:90vh;
	height:-webkit-fill-available;
}

.space-navigation-col-open #openMenuButton {
	cursor:pointer; position: absolute; top: 75px; left: 277px; z-index:999;
}

.space-navigation-col-closed #openMenuButton {
	cursor:pointer; position: absolute; top: 75px; left: 110px; z-index:999;
}

.space-navigation-col-hidden #openMenuButton {
	display:none;
}

.space-navigation-col-open {
	width:225px !important;
}

.space-navigation-col-closed {
	width:57px !important;
}

.space-navigation-col-hidden {
	width:0 !important;
}

.toolBarSubMenuItem:hover {
	color: black !important;
}

.space-navigation-col-open .segment .menu .item {
	margin-left:10px !important;
	margin-right:10px !important;
	padding-left:10px !important;
	padding-right:5px !important;
	padding-top:8px !important;
	padding-bottom:8px !important;
	border-radius:0 !important;
	line-height:1.4em;
	display: flex;
	align-items: center;
	color: #2B2F39 !important;
}

.space-navigation-col-open .segment .menu .item .icon {
	margin-top:0 !important;
	margin-left:0 !important;
	color: '';
}

.space-navigation-col-closed .segment .menu .item {
	margin-left:10px !important;
	margin-right:10px !important;
	padding-left:5px !important;
	padding-right:5px !important;
	border-radius:0 !important;
	height:35px;
}

.space-navigation-col-closed .segment .menu .item .icon {
	margin-top:0 !important;
	color: '';
}

.space-navigation-col-open .toolBar {
	margin-top:0px !important;
}

.space-navigation-col-closed .toolBar {
	margin-top:0px !important;
}

.editToolsItem {
	border-top: 1px solid #ebebeb !important;
}

/**************** BENTO EDITOR **************************/

#bentoDocumentEditor table tr td {
	border: 1px dashed #ebebeb;
}

#bentoDocumentEditor table, #bentoDocumentEditor table tr td {
	border-collapse: collapse;
    vertical-align: top;
}

#bentoDocumentEditorPreviewer table, #bentoDocumentEditorPreviewer table tr td, .bentoDocumentEditorPreviewer table, .bentoDocumentEditorPreviewer table tr td {
	border-collapse: collapse !important;
	vertical-align: top;
}

#bentoDocumentEditorPreviewer table, #bentoDocumentEditorPreviewer td, .bentoDocumentEditorPreviewer table, .bentoDocumentEditorPreviewer td {
	border:none;
	border-width:1px;
}

#bentoDocumentEditor .ui.segments {
	box-shadow: none !important;
}

#bentoDocumentEditor img, #bentoDocumentEditorPreviewer img, .bentoDocumentEditorPreviewer img {
	max-width: 100%;
	max-height:100%;
}

.medium-editor-toolbar li button {
	background-color: #2a2e37 !important;
	color:#ffffff !important;
	border:0 !important;
}

.medium-editor-toolbar li button:hover {
	background-color: #20222b !important;
}

.medium-editor-toolbar li .medium-editor-action-pre {
	font-size:14px;
	padding:15px;
}

[contenteditable] {
	outline: 0px solid transparent;
}

ul.edge-list {
	padding-left:10px !important;
	margin:0 !important;
}

ul.edge-list > li {
	padding-left:0.5rem !important;
}

ul.edge-list > li::marker {
	content: '–' !important;
	font-size:1.25rem !important;
}

ul.edge-list > li::before {
	content: '' !important;
}

ul.edge-list > li > a.bento-link {
	text-decoration: underline !important;
	text-decoration-style: dotted !important;
	text-decoration-color: #b8b8b8 !important;
}

/**************** END BENTO EDITOR **************************/

.ui.placeholder .header {
	overflow:visible !important;
}

.ui.menu .item > i.icon {
	opacity:1 !important;
}

.ui.menu .item > .input input {
	font-size:14px;
}
input::-webkit-calendar-picker-indicator{
    display: none;
}
input[type="date"]::-webkit-input-placeholder{
    visibility: hidden !important;
}
.ui.attached.tabular.menu {
	border-bottom:1px solid #ebebeb !important;
}

.ui.attached.tab.segment, .ui.attached.tabular.menu .item.active {
	border-color:#ebebeb !important;
}

.ui.huge.input, .ui.dropdown.huge {
	font-size:1.42857143em !important;
}

.ui.attached.tabular.menu .item, .ui.dropdown:not(.huge), .ui.dropdown .menu > .item, .ui.horizontal.list > .item {
	font-size:14px !important;
	font-weight: 300 !important;
	color:#2b2f3a;
}

.ui.breadcrumb > .ui.dropdown .menu > .item {
	padding:0.75rem !important;
}

.ui.attached.tabular.menu .item {
	font-weight: 300 !important;
}

.ui.cards > .card > .content > .header, .ui.card > .content > .header {
	color:inherit !important;
	font-size:inherit !important;
}

.menu.dropdown, .dropdown .menu {
	box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
	transition: box-shadow 0.3s cubic-bezier(.25,.8,.25,1) !important;
}
.menu.dropdown:hover, .dropdown .menu:hover {
	box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22) !important;
}

.ui.simple.dropdown .menu {
	z-index:9999;
}

.ui.search > .results .result .ui.avatar.image {
	float:left;
	border-radius:500rem;
}

.multiple > input.search {
	padding:0 !important;
}

.ui.selection.dropdown, .ui.search.selection.dropdown > input.search {
	padding-right:1em !important;
}

.ui.selection.dropdown > .delete.icon, .ui.selection.dropdown > .dropdown.icon, .ui.selection.dropdown > .search.icon {
	padding-right:5px !important;
}

.ui.selection.dropdown .menu > .item {
	padding:0.75rem !important;
}

.ui.category.search > .results {
	margin-top:1em;
	overflow-y:scroll;
}

.ui.category.search > .results .category {
	background-color:#f8f8f8;
}

.ui.category.search > .results .category .name {
	font-weight:300 !important;
	color:#2a2e3a !important;
	border-color:#ebebeb !important;
}

.ui.category.search > .results .category .results {
	border-color:#ebebeb !important;
}

.ui.category.search > .results .category .result {
	border-bottom:1px dashed #ebebeb !important;
	padding:10px 8px 8px 8px;
}

.ui.category.search > .results .category .result:last-child {
	border-bottom:none !important;
	padding:10px 8px 8px 8px;
}

.ui.category.search > .results .category .result:hover {
	background:none !important;
}

.ui.category.search > .results .category .name {
	min-width:125px !important;
	max-width:125px !important;
	white-space:normal !important;
}

.ui.category.search > .results .category .results .result {
	min-width:260px !important;
	max-width:260px !important;
}

.versionUpdateContainer {
	position:fixed;
	bottom:21px;
	z-index:999;
}

.versionUpdateContainer .black {
	background-color: #2a2e3a !important;
}

.bento-top-nav {
/* 	box-shadow: 0 14px 28px rgba(0,0,0,0.04), 0 10px 10px rgba(0,0,0,0.04) !important; */
	-webkit-box-shadow:	0 4px 6px rgba(0,0,0,.05) !important;
    -moz-box-shadow:		0 4px 6px rgba(0,0,0,.05) !important;
    box-shadow:			0 4px 6px rgba(0,0,0,.05) !important;
    z-index: 1000 !important;
    margin: 0 !important;
    height: 40px;
    position: fixed !important;
    top: 0;
    width: 82%;
/*     background-color: rgb(15,15,15) !important; */
/*     color: rgb(250,250,250) !important; */
/* 	background-color:rgb(250,250,250) !important; */
}

.bento-top-navWidthAdjustment {
	width: 100.5% !important;
	left: 0 !important;
}

.bento-top-nav > .menu {
/* 	background-color: rgb(15,15,15) !important; */
/*     color: rgb(250,250,250) !important; */
}

::-webkit-scrollbar {
    width: 6px !important;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    -webkit-box-shadow: none !important;
    background-color: transparent !important;
/*     background-color: transparent !important; */
}

::-webkit-scrollbar-thumb {
/*   background-color: darkgrey !important; */
/*   outline: 1px solid slategrey !important; */
}
::placeholder, .ql-editor::before, .field-placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  color:rgba(175, 175, 175, 1) !important;
  opacity: 1 !important; /* Firefox */
  font-style: normal !important;
}
.field-value > ::placeholder, .ql-editor::before, .field-placeholder {
	font-weight: 300 !important;
	font-size: inherit !important;
}
.edge-field.revealParent {
	min-height: 48px !important;
}
.edge-field.revealParent > .icon {
/* 	display: inline-block !important; */
position: absolute !important;
margin: 0px !important;
right: 2em !important;
/* top: 2em !important; */
color: #3E5659 !important;
}
.edge-field.revealParent > .icon:hover {

	color: #2B2E39 !important;
	background-color: white !important;
	box-shadow: 0 0 3px rgba(0,0,0,0.1) !important;

}

.fold {
  overflow:hidden;
/*   transition: max-height .3s ease-out; */
  height:auto;
  max-height:600px;
  box-shadow: 0 0 3px rgba(0,0,0,0.1) !important;
/*   position: absolute !important; */
/*   z-index: 1000; */
/*   background-color: white; */
  padding: 16px;
}
.fold.collapsed {
  max-height:0;
  box-shadow: 0 0 3px rgba(0,0,0,0) !important;
  padding: 0px;
}


body {
/* 	overflow-y: overlay !important; */
	overflow-x:hidden !important;
}

html {
	width: 100%;
	height: 100vh;
	height:-webkit-fill-available;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

i.grey.icon, i.grey {
	color: rgba(175, 175, 175, 1) !important;
}

i.light-grey.icon, i.light-grey {
	color: #b8b8b8 !important;
}

.borderless.menu item {
	border: none !important;
}
.bottom.floating.ui.mini.label {
	border-radius: 1em !important;
	font-weight: 900 !important;
/* 	font-size: larger !important; */
	top:2em !important; left:85% !important;
}

.label {
	opacity: .9 !important;
}
/* FIELDS */
.clickable {
	cursor: pointer;
}
.toggle-field {
	padding: 8px;
}
.ui.selection.active.dropdown, .ui.search.dropdown.active > input.search, .ui.search.dropdown.visible > input.search {
	background-color:#f5f5f5 !important;
	box-shadow:none !important;
}
.edge-field {
/* 	padding: 8px; */
	border-radius: 0.375rem !important;
/* 	box-shadow:none !important; */
/* 	background-color: #ffffff !important; */
}

.edge-field:hover {
	/* background-color: rgb(245,245,245) !important; */
}
.edge-field > .label {
	cursor: pointer;
/* 	background-color: white !important; */
}

/* Workflow editor */
.workflow-grid {
	margin:18px;
	-moz-box-shadow:    inset 0 0 3px rgba(0,0,0,0.1) !important;
	-webkit-box-shadow: inset 0 0 3px rgba(0,0,0,0.1) !important;
	box-shadow: 		inset 0 0 3px rgba(0,0,0,0.1) !important;
	border-radius: 6px;
	background-color: rgb(245,245,250);
}
.graph-node {
	fill: white;
	stroke:black;
	stroke-width:2;
	rx:4;
	cursor: pointer;
}
.graph-node:hover, .graph-edge:hover {
	/* fill: rgb(241, 241, 241); */
	stroke: #027eff !important;
	-moz-box-shadow:    0 0 3px #027eff !important;
	-webkit-box-shadow: 0 0 3px #027eff !important;
	box-shadow: 		0 0 3px #027eff !important;
}
.graph-edge-create {
	color: transparent !important;
	cursor: pointer;
}
.graph-edge-create:hover {
	color: #027eff !important;
	background-color: white;
	border-radius: 1.2em;
	width: 20px;
	height: 20px;
}
.graph-node:active {
	stroke: #F5D40F !important;
}
.condition-node: {
	fill: #2B2E39 !important;
	stroke: #2B2E39 !important;
}
.workflow-state-card {
	z-index: 0;
/* 	margin-bottom: 100px !important; */
}
.workflow-state-card:hover {
	background-color: rgb(245,245,250);
	cursor: pointer;
}
.workflow-state-card-graph, .workflow-condition-node {
	z-index: 0;
	width:100px !important;
	height:100px !important;
/* 	margin-bottom: 100px !important; */
	box-shadow: none !important;
	outline: 2px solid black !important;
	border-radius: 6px !important;
	border-radius: 12px !important;
}
.workflow-state-card-graph:hover, .workflow-condition-node:hover {
	background-color: rgb(245,245,250);
	cursor: pointer;
}
.workflow-condition-node {
	background-color: lightgray !important;
}
#workflow-popup {
	position: absolute !important;
	z-index: 100 !important;
	top: 0px;
	left: 50%;
	width: 500px !important;
	transition: transform 0.5s ease-in;
	background-color: white;
}
.workflow-create-after-node {
	position: absolute;
	color: #027eff !important;

	/* left: 100%; */
	/* top: 50%; */
}
.graph-add-node-btn {
	color: transparent !important;
}
.graph-add-node-btn:hover, .graph-add-node-btn:active {
	color: #027eff !important;
	background-color: transparent !important;
	background: transparent !important;
}
/*
.workflow-state-card-notched::before {
	content: '';
	display: block;
	position: absolute;
	top:-10px;
	left: calc(50% - 10px);
	width: 20px;
	height: 20px;
	-moz-border-radius: 10px;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	border: 4px solid white;
	background-color: rgb(225,225,235);
	box-shadow: 0 0 3px rgba(0,0,0,0.1) !important;
}
.workflow-state-card-pointing:after {
    content: '';
	display: block;
	position: absolute;
	top: 100%;
	left: calc(50% - 2px);
	width: 4px;
	height: 45px;
	-moz-border-radius: 0px;
	-webkit-border-radius: 0px;
	border-radius: 0px;
	background-color: rgb(225,225,235);
	background-color: rgb(235,235,240);
	z-index: -10;
}
*/
.field-manager-plus {
	padding: 8px !important;
	width: 100% !important;
	border-radius: 4px !important;
	color: rgba(175, 175, 175, 1) !important;
	transition : all .2s ease-out !important;
	cursor: pointer;
	background-color: transparent !important;
	text-align: left !important;
}
.field-manager-plus:hover, .field-manager.edit:hover {
	background-color: rgb(245,245,245) !important;
	box-shadow: 0 0 3px rgba(0,0,0,0.1) !important;
}

.state-btn {
/* 	-webkit-box-shadow:0 0 20px rgba(0,0,0,0.3) !important; */
/* 	-moz-box-shadow:0 0 20px rgba(0,0,0,0.3) !important; */
/* 	box-shadow:0 0 20px rgba(0,0,0,0.3) !important; */
/* 	background-color:white !important; */
/* 	border-radius: 2px !important; */
}
.state-btn:hover {
/* 	background-color:white !important;  */
}

.toolMenu {
	margin-left:-1.1em !important;
	margin-right:-1.1em !important;
	margin-top:40px !important;
/* 	margin-bottom: 0px !important; */
	padding-top: 0px !important;
	padding-bottom: 0px !important;
	border-radius:0px !important;
/* 	padding-left:6em !important; */
	background-color:rgb(250,251,250) !important;
	border: none !important;
	box-shadow: none !important;
/*

	text-align: center !important;
	width: 100% !important;
	display: table !important;
	text-align: center !important;
*/
}
.toolMenu:hover {
	background-color:rgb(245,246,245) !important;
/* 	background-color:white !important; */

	-webkit-box-shadow:		inset rgba(0, 0, 0, 0.05) 0px 4px 6px 0px !important;
	-moz-box-shadow:		inset rgba(0, 0, 0, 0.05) 0px 4px 6px 0px !important;
	box-shadow: 			inset rgba(0, 0, 0, 0.05) 0px 4px 6px 0px !important;
}
.toolMenu > .item {
	border-radius: 0px !important;
	margin: 0px !important;
	border: none !important;
	display: inline-block !important;
	float: none !important;
}
.toolMenu > .item:hover {
	background-color:rgb(235,236,235) !important;
	background-color:white !important;
	-webkit-box-shadow:rgba(0, 0, 0, 0.05) 0px 4px 6px 0px !important;
	-moz-box-shadow:rgba(0, 0, 0, 0.05) 0px 4px 6px 0px !important;
	box-shadow: rgba(0, 0, 0, 0.05) 0px 4px 6px 0px !important;
}

/* MAIN LEFT NAV */
.mainNavigation {
	margin-top:40px !important;
/* 	background-color: #eee !important; */
	overflow-y: auto;
/* 	background-color: #f8f8f3 !important; */
	background-color: transparent !important;
/* 	overflow-y:auto; */
/* 	background-color: #f2f7f8 !important; */
/* 	background-color: rgba(0, 0, 50, 0.04) !important; */
}
.mainNavigation > .main-left-nav-item {
	border-radius:0px !important;
/* 	padding-left:24px !important; */
/* 	padding-right:24px !important; */
	margin: 0px !important;
}
.mainNavigation .mini.grey.header {
	padding-left: 14px !important;
	letter-spacing: 0.1em;
/* 	margin-left: 5px !important; */
/* 	padding-bottom: 0px !important; */
	font-size: small !important;
	color: rgba(0, 0, 0, .4) !important;
}
.mainNavigation .item > i.icon.left {
    float: none !important;
    margin: 0em 0.35714286em 0em 0em !important;
    padding-left: 1em !important;
    padding-right: 2em !important;
    color: rgba(0, 0, 0, .4) !important;
}
.main-left-nav-item:hover, .main-left-nav-item.active {
/* 	background-color: rgb(210,211,213) !important; */
}
.pageNavMenu {
/* 	max-height: 50vh; */
/* 	overflow-y:auto; */
}
.pageNavMenu .item, .mainNavigation .dropdown.item {
	margin: 0px !important;
	font-size: smaller !important;
}
.addToolBtn {
	border-radius: 	2em !important;
	font-size: 		10px !important;
	padding: 		4px !important;
	position:		absolute !important;
	right:			0 !important;
	background-color: white !important;
	box-shadow: 		0 1px 3px rgba(0,0,0,0.12), 0 1px 2px #eee !important;
}
.addToolBtn:hover {
	color: black !important;
}

.ui.dropdown .menu > .header.grey {
	color: rgb(175, 175, 175);
}

/* Dashboards */
/*
.ui .grid .ui.stackable.grid.bento-box {
	margin-left: -1rem !important;
	margin-right: -1rem !important;
}
.bento-box-view {
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px #eee !important;
  transition: all .2s cubic-bezier(.25,.8,.25,1) !important;
  min-height: 100% !important;
  background-color: transparent !important;
}

.bento-box-view:hover {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px #eee !important;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px #eee !important;
}
*/
/*
.mainNavigation .item {
	transition: all .3s cubic-bezier(.25,.8,.25,1) !important;
}
.mainNavigation .item:hover {
	background-color:white !important;
	box-shadow: 0 14px 28px rgba(0,0,0,0.125), 0 5px 5px rgba(0,0,0,0.025) !important;
}
*/

.toolBar::-webkit-scrollbar {
	height: 0px !important;
}

/*
.dashboard .card {
	box-shadow: none !important;
	outline: 0px !important;
}
.dashboard .column {
	padding: 0px !important;
	border-right: 1px solid lightGray;
	border-bottom: 1px solid lightGray;
}
*/
.createBtn:hover {
	background-color: white !important;
/* 	box-shadow: 		0 1px 3px rgba(0,0,0,0.12), 0 1px 2px #eee !important; */
}
.mainContainerArea div {
/* 	direction: rtl; */
}
.mainContainerArea .ui.vertical.menu .item:hover {
		background-color: white !important;
		background: white !important;
/* 		direction: rtl; */
	}


/*
.darkblue {
	color: #2B2E39 !important;
}
.blue, i.icon, a, .primary {
	color: #3E5659 !important;
}
.light {
	color: #86CBC3 !important;
}
.warning {
	color: #F9E49C !important;
}
body, .dark, h1, h2, h3, h4, h5, .header, p, i, .text {
	color: #2a2e3a !important;
}
*/

.ui.borderless.inverted.menu {
	background-color: #2B2E39 !important;
}

.mainNavigation.ui.borderless.menu > .item {
	color: #2a2e3a !important;
	position: absolute;
	width:65px;
	border-top:1px solid transparent !important;
	border-bottom:1px solid transparent !important;
	border-radius:0 !important;
	padding:10px;
	line-height:28px;
	max-height:50px;
	cursor:pointer;
}

.mainNavigation.ui.borderless.menu > .item.active:not(.dropdown) {
	background-color: #ffffff !important;
	color: #027eff !important;
	border-top: 1px solid #ebebeb !important;
	border-bottom: 1px solid #ebebeb !important;
}

.mainNavigation.ui.borderless.menu > .item:hover {
	background-color: rgba(0,0,0,.05) !important;
}

.mainNavigation.ui.borderless.labeled.icon.menu > .item > i.icon {
	margin-bottom:0 !important;
}

.middleNavigation.ui.borderless.menu > .item {
	color: #4b4e57 !important;
	cursor: pointer;
}

.bottomNavigation.ui.borderless.menu {
	background-color:#2a2e3a !important;
	color: rgba(255,255,255,.7) !important;
	cursor: pointer;
}

.bottomNavigation.ui.borderless.menu > .item:hover {
	background-color:#2a2e3a !important;
	color: #ffffff !important;
}

.ui.borderless.inverted.menu > .item, .ui.secondary.vertical.menu > .item {
	/* border-radius: 0 !important; */
}

::selection { background: #F9E49C; }
input::selection { background: #F9E49C; }
textarea::selection { background: #F9E49C; }
.field-value input, .ql-editor.ql-blank {
	/* border-bottom: 1.25px solid transparent !important; */
}

.search-input {
	margin:0px !important;
	padding:0px !important;
	width:100% !important;
}

.actions-dropdown:hover {
	background-color: transparent !important;
}

.inverted.warning.card {
	color: #F9E49C !important;
}

.modal .entity-fields-container {
	padding: 16px !important;
}


/* ===== MOBILE MENU STYLES ===== */

#sidebarLogoSection {
  background-color:#ffffff;
  text-align:center;
  padding-top:5px;
  padding-bottom:0px;
}

#sidebarTopSection {
  background-color:#ffffff;
  border-top:1px solid #ebebeb;
  border-bottom:1px solid #ebebeb;
  padding-top:15px;
  padding-bottom:15px;
  margin-bottom:15px;
}

#sidebarBottomSection {
  background-color:#ffffff;
  padding-bottom:15px;
  margin-bottom:15px;
}

#sidebarSignOffSection {
  background-color:#2b2e39;
}

#sidebarHelpDeskSection {
  background-color:#4b4d57;
}

#sidebarBentoVersionSection {
  background-color:#ffffff;
  padding:15px;
  text-align:center;
  color:rgba(0,0,0,0.3);
}

/*
.ui.inverted.menu {
  background:#ffffff !important;
}
*/

.ui.accordion .title:not(.ui) {
  padding:0;
}

.ui.vertical.menu .item > i.icon {
  float:left;
  margin-right:10px;
  margin-left:0;
}

:not(.field) > .ui.ui.ui.transparent[class*="left icon"].input > input {
	padding-left:1.5em !important;
}

#mobileHeader {
	position:fixed;
	top:0;
	left:0;
	background:#ffffff;
	width:100%;
	padding-top:5px;
	text-align:center;
	border-bottom:1px solid #ebebeb;
}

#mobileHeader #openMobileNavBtn {
	float:left;
	position: absolute;
	top:18px;
	left:10px;
	font-size:30px;
}

#openMobileNavBtn, #closeMobileNavBtn, #openRightTrayBtn, #closeRightTrayBtn {
	color: #2a2e3a !important;
}

#mobileHeader #openRightTrayBtn {
	float:right;
	position: absolute;
	top:18px;
	right:10px;
	font-size:30px;
}

#spaceNavigationCol .ui.secondary.menu .item {
	font-weight:300 !important;
}

.ui.secondary.menu .active.item {
	background: #EDF6FF !important;
	color: #027eff !important;
}

#spaceNavigationCol .ui.secondary.menu .active.item {
	color: #2B2F39 !important;
}

.dropdown, input, select, textarea {
	color: #2a2e3a !important;
}

/* ==================== */

/* GLOBAL UTILITY CLASSES */

.pull-right {
	float: right !important;
}

.pull-left {
	float: left !important;
}

.text-center {
	text-align: center !important;
}

.text-left {
	text-align: left !important;
}

.text-right {
	text-align: right !important;
}

.text-muted, .ui.label.text-muted, .label.text-muted {
	color: #a7a7a7 !important;
}

.text-italic {
	font-style: italic !important;
}

.clear {
	clear: both !important;
}

.hide-scrollbar::-webkit-scrollbar {
	display: none !important;
}

.visibility-none {
	visibility: hidden !important;
}

.hoverable:hover {
	background-color:rgb(245,245,245) !important;
}

.wordwrap {
   white-space: pre-wrap;      /* CSS3 */
   white-space: -moz-pre-wrap; /* Firefox */
   white-space: -pre-wrap;     /* Opera <7 */
   white-space: -o-pre-wrap;   /* Opera 7 */
   word-wrap: break-word;      /* IE */
}

/* ========== TABLE VIEW ========== */

.ui.table > tbody > tr > td {
	overflow: visible !important;
	overflow-x: inherit !important;
}

.table-view-title-cell, .table-view-title-cell span a .ui.header, .table-view-title-cell span {
	width: 100%;
}

.table-view-title-cell span a .ui.header div, .table-view-title-cell span a.ui.header {
	width: 100%;
	white-space: nowrap !important;
	overflow: hidden !important;
	text-overflow: ellipsis !important;
}

.responsive-horizontal {
	display:block !important;
	width:100% !important;
	overflow-x: auto !important;
	webkit-overflow-scrolling: touch !important;
}

.table-field > tbody > tr > td {
	vertical-align: top !important;
}

/* ========== END TABLE VIEW ========== */

/* ============= FIELDS =============== */

.user-badge {
	/* color: white !important; */
}
.user-badge::before {
	font-size: 28px !important;
	position: absolute !important;
	color: black;
	left: 0;
}
.user-badge-txt {
	position: absolute;
	top: 5px;
	left: 5px;
	color: white;
}

/* ============= END FIELDS =============== */

/* ========== ALERTS ========== */
.ui.inverted.floating.message.segment {
	box-shadow: 0 14px 28px rgba(0,0,0,0.20), 0 10px 10px rgba(0,0,0,0.04) !important;
	-webkit-transition: border-color width .4s !important; */
	transition: border-color .4s !important;
}
.ui.inverted.floating.message.segment.fadeOut, .ui.inverted.floating.message.segment.hidden {
	box-shadow: 0 0px 0px rgba(0,0,0,0.20), 0 0px 0px rgba(0,0,0,0.04) !important;
}

/* ========== END ALERTS ========== */

/* ========== SUGGESTER ========== */
.results.transition.visible .result {
	border: 0px !important;
/* 	outline: 0px !important; */
/* 	box-shadow: none !important; */
}

/* ========== END SUGGESTOR ========== */

/* ========== VIEW TABS ========== */

.view-tabs .item {
	margin-bottom:5px !important;
}

.view-tabs .ui.simple.active.dropdown, .view-tabs .ui.simple.dropdown:hover {
	border-radius:0.375rem !important;
}

.view-tabs .selected.item, .view-tabs .selected.item > i.icon.black, .view-tabs .item:hover, .view-tabs .item:hover > i.icon.black {
	-webkit-box-shadow: none !important;
    box-shadow: none !important;
    background: #EDF6FF !important;
	color: #027eff !important;
	border-radius:0.375rem !important;
}

/* ========== ENDVIEW TABS ========== */

/* ========== MEDIA QUERIES ========== */

@media only screen and (max-width: 990px) {
/* @media only screen and (max-width: 767px) { */

	html {
		height: 86.5vh !important;
	}

	#mobileNav {
		overflow:hidden !important;
		height:86.5vh !important;
	}

	.mainNavColumn {
		overflow-y: auto !important;
		margin-top:14px !important;
		background-color:#eee;
		background-color:rgba(0, 0, 45, 0.045);
	}

	#leftNav {
		float:left;
		height:86.5vh !important;
	}

	#spaceNavigationCol {
		padding-top:65px !important;
		padding-bottom:65px;
		float:right;
		height:86.5vh !important;
		overflow-y:scroll !important;
		-webkit-overflow-scrolling: touch;
	}

	.space-navigation-col-open {
		width:195px !important;
	}

	#spaceViewCol {
		margin-top:55px !important;
		padding-bottom:65px;
		height:86.5vh !important;
	}

	.dimmable {
		height:86.5vh !important;
	}

	.pushable {
		overflow:hidden;
		height:86.5vh !important;
	}

	.versionUpdateContainer {
		position:fixed;
		bottom:21px;
		right:89px;
		z-index:999;
	}

}

@media print {
    .pagebreak { page-break-before: always; } /* page-break-after works, as well */
}

.rowSection{
	display:flex;
	justify-content: space-between
}
.commentSection p{
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.date-aligned{
	display:flex;
	align-items: center;
}
.text-quoted blockquote{
	box-sizing: border-box;
	padding-left: 16px;
	border-left: 2px solid #DFE1E6;
	margin: 0.75rem 0px 0px;
}

.text-quoted blockquote::before {
	float: left;
	margin-left: -1em;
	text-align: right;
	width: 1em;
}

.having-error .ql-editor, .having-error .field-placeholder{
	border-bottom:2px red solid!important;
}
.having-error .bento-unchecked::before {
	color: red!important;
}


/* ========== END MEDIA QUERIES ========== */
