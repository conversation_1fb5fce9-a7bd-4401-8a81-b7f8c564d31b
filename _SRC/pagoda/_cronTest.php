<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

date_default_timezone_set('America/Chicago');
			
if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
	newrelic_background_job();
	newrelic_ignore_apdex();
}

$_REQUEST['pagodaAPIKey'] = 'voltzsoftware';

define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' ); 

require APP_ROOT.'DBPATH.php'; 

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'vendor/autoload.php';
require_once APP_ROOT.'_objects.php';

require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
require_once APP_ROOT.'_excel.php';

require_once '_config.php';
		
//echo 'test';
//die();

// require_once __DIR__.'/cron/every-30-min-intake-reminder-email.php';



?>
