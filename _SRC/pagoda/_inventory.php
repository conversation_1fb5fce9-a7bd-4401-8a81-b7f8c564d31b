<?php

	

class Inventory {

	

	private $db;

	public $bellTowerRooms = array(

		0 => 'The Bell Tower',

		1 => 'Board Room',
		
		2 => 'Basement',
		
		3 => 'Common Area',
		
		4 => 'Men\'s Lounge',
		
		5 => 'Women\'s Lounge',
		
		6 => 'Boardroom',
		
		7 => 'Front Lobby',
		
		8 => 'Sidewalk',
		
		9 => 'Main Space',
		
		10 => 'Tasting Room',
		
		11 => 'Parlor',
		
		12 => 'Patio',
		
		13 => 'Mezzanine',
		
		14 => 'VIP Area'

	);

	

	public $bridgeBuildingRooms = array(

		0 => 'Cumberland',

		1 => 'Observatory',

		2 => 'Cumberland River Compact',

		3 => 'Infinity',
		
		4 => '1st Floor',
		
		5 => 'Main Space',
		
		6 => 'Main Patio',
		
		7 => 'Side Patio',
				
		9 => 'Riverfront',
		
		10 => 'Lobby',
		
		18 => '2nd Floor Lounges',
		
		19 => '2nd Floor Common Area',
		
		11 => '3rd Floor',
		
		12 => 'CRC',
		
		13 => '4th Floor',
		
		14 => 'Atrium',
		
		15 => 'Rooftop Patio',
		
		16 => 'Lobby',
		
		17 => 'Backroom',
		
		

	);

	

	public $category = array(

		0 => 'Standard',

		1 => 'Premium',

		2 => 'Signature Drinks',

		3 => 'A La Carte Items',

		4 => 'Premium at Standard',

		5 => 'Packages',

		6 => 'Custom Selections'

	);

	

	public $foodCategory = array(

		'Vft' => 'Vegetarian',

		'Sft' => 'Seafood',

		'Pok' => 'Pork',

		'Bft' => 'Beef',

		'Pft' => 'Poultry'

	);

	

	public $foodType = array(

		0 => 'Hot Appetizer',

		1 => 'Cold Appetizer',

		2 => 'Bread',

		3 => 'Salad',

		4 => 'Entree',

		5 => 'Veggie Side',

		6 => 'Starch Side',

		7 => 'Dessert',

		8 => 'Beverage',

		9 => 'Stations',

		10 => 'Lunch',

		11 => 'Breakfast',

		12 => 'Breaks'

	);

	

	public $descriptor = array(

		0 => 'Rustic',

		1 => 'Silver',

		2 => 'Gold'

	);

	

	public $dietaryInfo = array(

		'Vdi' => 'Vegan',

		'Gdi' => 'Gluten Free',

		'Ddi' => 'Dairy Free',

		'Ndi' => 'Nut Free'

	);

	

	public $eventType = array(

		0 => 'Social',

		1 => 'Corporate',

		2 => 'Non-Profit'

	);

	public $hardingHouseRooms = array(
		
		1 => 'Full Restaurant',
		
		2 => 'Front Section',
		
		3 => 'Middle Section',
		
		4 => 'Back Section'
		
	);

	public $laborType = array(

		0 => 'Bartenders',

		1 => 'Event Staff Plated',

		2 => 'Event Staff Cocktail, Buffet or Stations',

		3 => 'Event Staff Reception (no food)',

		4 => 'Coat Check'

	);

	

	public $lightingPackageType = array(

		0 => 'Single Item',

		1 => 'Basic Package',

		2 => 'Custom Package'

	);

	

	public $packageType = array(

		0 => 'Single Item',

		1 => 'Basic Package',

		3 => 'Ultimate Package',

		2 => 'Custom Package'

	);

	

	public $requiredItemList = array(

		0 => 'No',

		1 => 'Yes'

	);

	

	public $requiredPackageItem = array(

		0 => 'No',

		1 => 'Bread',

		2 => 'Salad',

		3 => 'Veggie',

		4 => 'Starch'

	);

	

	public $rentalType = array(

		0 => 'CSG',

		1 => 'Furniture',

		2 => 'Other'

	);

	

	public $season = array(

		'Fs' => 'Fall',

		'Us' => 'Summer',

		'Ps' => 'Spring',

		'Ws' => 'Winter'

	);

	

	public $securityFeeType = array(

		0 => 'Fixed',

		1 => 'Custom'

	);

	

	public $serviceStyle = array(

		'Ass' => 'Passed',

		'Bss' => 'Buffet',

		'Sss' => 'Station/Displayed',

		'Lss' => 'Plated',

		'Oss' => 'On-Site Only',
		
		'Dis' => 'Displayed',
		
		'Pre' => 'Preset',
		
		'Tab' => 'Table-Side',
		
		'Che' => 'Chef-Manned'

	);

	

	public $tentSide = array(

		0 => 'Side Closed',

		1 => 'Open',

		2 => 'Pulled Back'

	);

	

	public $timeOfDay = array(

		0 => 'Morning',

		1 => 'Evening',

		2 => 'All Day',

		3 => 'Showcase',

		4 => 'Additional Hour',

		5 => 'Custom'

	);

	public $twelfthAndPorterRooms = array(
		
		0 => 'Restaurant',
		
		1 => 'Restaurant Bar',
		
		2 => 'Concert Hall',
		
		3 => 'Concert Hall Bar',
		
		4 => 'Mezzanine Concert Hall'
		
	);

	public $units = array(

		0 => 'Per Guest',

		1 => 'Per Event',

		2 => 'Percent of Guest Count',
		
		3 => 'Per Hour, Per Guest'

	);

	

	public $venue = array(

		0 => 'Harding House',

		1 => 'The Bridge Building',

		6 => 'The Bridge Building - Riverfront',
		
		2 => 'The Bell Tower',

		3 => '12th and Porter',

		5 => 'Bria Bistro',

		4 => 'Off-Site'

	);

	

	public $venueFeeDays = array(

		0 => 'Monday - Thursday',

		1 => 'Friday',

		2 => 'Saturday',

		3 => 'Sunday'

	);

	

	function __construct($db_conn){

		

		$this->db = $db_conn;

		

		// build descriptor array

		$descriptors = $this->getItemsByType('descriptor');

		

		foreach($descriptors as $descriptor){

			

			$descriptorArray[$descriptor['id']] = $descriptor['details']['name'];

			

		}

		

		$this->descriptor = $descriptorArray;

		

	}

	

	// input data, data type (db/post), and object type

	// returns array of object

	public function buildObject($data, $objectType){

		

		switch ($objectType){

			

			case 'av':

				

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'itemId' => $data['itemId'],

					'name' => $data['name'],

					'stock' => $data['stock'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),
					
					'additionalPrice' => intval(preg_replace('/[^0-9]+/', '', $data['additionalPrice'], 10)),
					
					'packageItems' => $data['packageItems'],
					
					'packageLimit' => $data['packageLimit'],
					
					'packageSelection' => $data['packageSelection'],
					
					'avSelection' => $data['avSelection'],
					
					'orderId' => $data['order-id']

				);

				

				break;

			

			case 'bar':

			

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'itemId' => $data['itemId'],

					'checkedOut' => $data['checkedOut'],

					'name' => $data['name'],

					'description' => $data['description'],

					'category' => $data['category'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),

					'additionalPrice' => intval(preg_replace('/[^0-9]+/', '', $data['additionalPrice'], 10)),

					'requiredItem' => $data['requiredItem'],

					'requiredItemList' => $data['requiredItemList'],
					
					'barSelection' => $data['barSelection'],
					
					'packageLimit' => $data['packageLimit'],

					'notes' => $data['notes'],
					
					'orderId' => $data['order-id']

				);

			

				break;

				

			case 'decor':

			

				$object = array(

					'checkedOut' => $data['checkedOut'],

					'description' => $data['description'],

					'descriptor' => $data['descriptor'],

					'imageName' => $data['imageName'],

					'itemId' => $data['itemId'],

					'name' => $data['name'],

					'operationalPrice' => intval(preg_replace('/[^0-9]+/', '', $data['operationalPrice'], 10)),

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),

					'stock' => $data['stock'],

					'type' => $data['type'],
					
					'orderId' => $data['order-id']

				);

			

				break;

				

			case 'descriptor':

				

				$object = array(

					'imageName' => $data['imageName'],

					'name' => $data['name'],

					'type' => $data['type'],
					
					'orderId' => $data['order-id']

				);

				

				break;

				

			case 'estimate':

			

				$object = array(

					'type' => $data['estimatetype'],
					
					'orderId' => $data['order-id']

				);

			

				break;			

				

			case 'eventPlanningPackage':

			

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'itemId' => $data['itemId'],

					'packageType' => $data['packageType'],

					'stock' => $data['stock'],

					'checkedOut' => $data['checkedOut'],

					'name' => $data['name'],

					'description' => $data['description'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),
					
					'additionalPrice' => intval(preg_replace('/[^0-9]+/', '', $data['additionalPrice'], 10)),

					'notes' => $data['notes'],
					
					'orderId' => $data['order-id'],
					
					'additionalPrice' => intval(preg_replace('/[^0-9]+/', '', $data['additionalPrice'], 10)),
					
					'packageItems' => $data['packageItems'],
					
					'packageLimit' => $data['packageLimit'],
					
					'packageSelection' => $data['packageSelection'],
					
					'eventPlanningPackageSelection' => $data['eventPlanningPackageSelection']

				);

			

				break;	

				

			case 'food':

				

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'itemId' => $data['itemId'],

					'checkedOut' => $data['checkedOut'],

					'foodCategory' => $data['foodCategory'],

					'foodType' => $data['food_type'],

					'name' => $data['name'],

					'description' => $data['description'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),

					'additionalPrice' => intval(preg_replace('/[^0-9]+/', '', $data['additionalPrice'], 10)),

					'units' => $data['units'],

					'dietaryInfo' => $data['dietaryInfo'],

					'serviceStyle' => $data['serviceStyle'],

					'season' => $data['season'],

					'requiredItem' => $data['requiredItem'],

					'requiredPackageItem' => $data['requiredPackageItem'],
					
					'foodSelection' => $data['foodSelection'],
					
					'packageItems' => $data['packageItems'],
					
					'packageLimit' => abs($data['packageLimit']),
					
					'packageSelection' => $data['packageSelection'],

					'notes' => $data['notes'],
					
					'ingredients' => $data['ingredients'],
					
					'orderId' => $data['order-id']

				);

			

				break;

				

			case 'labor':

				

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'name' => $data['name'],
					
					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),
					
					'orderId' => $data['order-id']

				);

				

				break;

				

			case 'lighting':

				

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'lightingPackageType' => $data['lightingPackageType'],
					
					'lightingSelection' => $data['lightingSelection'],

					'packageMin' => $data['packageMin'],

					'itemId' => $data['itemId'],

					'venue' => $data['venue'],

					'name' => $data['name'],

					'stock' => $data['stock'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),
					
					'additionalPrice' => intval(preg_replace('/[^0-9]+/', '', $data['additionalPrice'], 10)),

					'requiredItem' => $data['requiredItem'],

					'requiredItemList' => $data['requiredItemList'],
					
					'packageItems' => $data['packageItems'],
					
					'packageLimit' => $data['packageLimit'],
					
					'packageSelection' => $data['packageSelection'],

					'details' => $data['details'],
					
					'orderId' => $data['order-id'],
					
					'notes' => $data['notes']

				);

				

				break;	
				
			case 'photoBooth':
				
				$object = array(
					
					'type' => $data['type'],
					
					'name' => $data['details-name'],
										
					'price' => intval(preg_replace('/[^0-9]+/', '', $data['details-price'], 10)),
					
					'orderId' => $data['order-id'],
					
					'details' => $data['item-details']
					
				);
				
				break;	

				

			case 'rental':

				

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'itemId' => $data['itemId'],

					'stock' => $data['stock'],

					'checkedOut' => $data['checkedOut'],

					'name' => $data['name'],

					'description' => $data['description'],

					'rentalType' => $data['rentalType'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),

					'notes' => $data['notes'],

					'requiredItemList' => $data['requiredItemList'],
					
					'orderId' => $data['order-id']

				);

			

				break;

				

			case 'security':

				

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'itemId' => $data['itemId'],

					'venue' => $data['venue'],

					'securityFeeType' => $data['securityFeeType'],

					'name' => $data['name'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),

					'requiredItem' => $data['requiredItem'],
					
					'orderId' => $data['order-id']

				);

				

				break;	

				

			case 'tent':

				

				$object = array(

					'coolCost' => intval(preg_replace('/[^0-9]+/', '', $data['coolCost'], 10)),

					'imageName' => $data['imageName'],

					'details' => $data['details'],

					'heatCost' => intval(preg_replace('/[^0-9]+/', '', $data['heatCost'], 10)),					

					'itemId' => $data['itemId'],

					'name' => $data['name'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),

					'tentSide' => $data['tentSide'],

					'stock' => $data['stock'],

					'type' => $data['type'],					

					'tentType' => $data['tentType'],

					'venue' => $data['venue'],
					
					'orderId' => $data['order-id']

				);

				

				break;	

				

			case 'venueFee':

				

				$object = array(

					'type' => $data['type'],

					'imageName' => $data['imageName'],

					'itemId' => $data['itemId'],

					'stock' => $data['stock'],

					'checkedOut' => $data['checkedOut'],

					'venue' => $data['venue'],

					'offSiteVenueName' => $data['offSiteVenueName'],

					'offSiteVenueStreet' => $data['offSiteVenueStreet'],

					'offSiteVenueCity' => $data['offSiteVenueCity'],

					'offSiteVenueState' => $data['offSiteVenueState'],

					'offSiteVenueZip' => $data['offSiteVenueZip'],

					'offSiteVenueDirections' => $data['offSiteVenueDirections'],

					'timeOfDay' => $data['timeOfDay'],

					'customTimeOfDay' => $data['customTimeOfDay'],

					'eventType' => $data['eventType'],

					'price' => intval(preg_replace('/[^0-9]+/', '', $data['price'], 10)),

					'notes' => $data['notes'],

					'venueFeeDays' => $data['venueFeeDays'],

					'venueRoom' => $data['venueRoom'],
					
					'orderId' => $data['order-id']

				);

				

				break;			

			

		}

		
		$object['taxRate'] = $data['taxRate'];
		$object['chartOfAccounts'] = $data['chartOfAccounts'];
		
		return $object;

		

	}

	

	public function checkExist($value, $valueType = 'name'){

		

		$allInventory = $this->getAll();

				

		foreach($allInventory as $inventoryItem){

												

			$object = $this->parseDetails($inventoryItem['details']);

						

			if($object[$valueType] == $value){

				

				return $inventoryItem;

				

			}

			

		}

		

		return null;

		

	}

	

	public function createImageThumbnail($thumbnailWidth = 100, $imageFolder, $thumbnailFolder, $filename){

		

		if(preg_match('/[.](jpg)$/', $filename)) {

	        $im = imagecreatefromjpeg($imageFolder . $filename);

	    } else if (preg_match('/[.](gif)$/', $filename)) {

	        $im = imagecreatefromgif($imageFolder . $filename);

	    } else if (preg_match('/[.](png)$/', $filename)) {

	        $im = imagecreatefrompng($imageFolder . $filename);

	    }

	    

	    $ox = imagesx($im);

	    $oy = imagesy($im);

	     

	    $nx = $thumbnailWidth;

	    $ny = floor($oy * ($thumbnailWidth / $ox));

	     

	    $nm = imagecreatetruecolor($nx, $ny);

	     

	    imagecopyresized($nm, $im, 0,0,0,0,$nx,$ny,$ox,$oy);

	     

	    if(!file_exists($thumbnailFolder)) {

	      if(!mkdir($thumbnailFolder)) {

	           die("There was a problem. Please try again!");

	      } 

	       }

	 

	    imagejpeg($nm, $thumbnailFolder . $filename);

	    $tn = '<img src="' . $thumbnailFolder . $filename . '" alt="image" />';

	    $tn .= '<br />Congratulations. Your file has been successfully uploaded, and a      thumbnail has been created.';

	    return  $tn;

		

	}

	

	// returns new item id

	public function createItem($type, $itemInfo){

		

		if($type != 'venueFee' and $type != 'descriptor' and $type != 'lighting' and $type != 'av' and $type != 'eventPlanningPackage' and $type != 'security' and $type != 'tent' and $type != 'venue_fees' and $type != 'estimate' and $type != 'labor' and $type != 'photoBooth'){

			

			$nameExists = $this->checkExist($itemInfo['name']);

			$itemIdExists = $this->checkExist($itemInfo['itemId'], 'itemId');

		

		}else{

		

			$exists = null;

			$nameExists = null;

			$itemIdExists = null;

		

		}

		

// 		var_dump($type, $itemInfo);

		

		if($nameExists == null && $itemIdExists == null){

			// encode the itemInfo
			$object = json_encode($itemInfo);

			$object = $this->db->quote($object);

			$query = "insert into inventory (type, details) values ('$type', $object)";

			$insert = $this->db->query($query);

			return $this->db->lastInsertId();

		}else{

			

			return false;

			

		}

		

	}

	

	public function deleteItem($itemId){

		

		$query = "delete from inventory where id = $itemId";

		

		if($data = $this->db->query($query)){

			

			return true;

			

		}

		

	}

	

	public function getAll(){

		

		$query = "select * from inventory order by id";

		

		if($data = $this->db->query($query)){

			

			while($datum = $data->fetch(PDO::FETCH_ASSOC)){

				

				$datum['details'] = json_decode($datum['details'], 1);

				

				$ret[] = $datum;

				

			}

			

			return $ret;

			

		}

		

	}

	

	public function getAllRequired($type){

		

		switch($type){

			

			case 'bar':

				

				$query = "select * from inventory where type != 'labor' and type != 'venueFee' and type != 'lighting' order by type";

				

				break;

				

			case 'lighting':

				

				$query = "select * from inventory where type = 'lighting'";

				

				break;

				

			default:

			

				return false;

				

				break;	

			

		}

				

		if($data = $this->db->query($query)){

			

			while($datum = $data->fetch(PDO::FETCH_ASSOC)){



				$datum['details'] = json_decode($datum['details'], 1);



				if($type == 'lighting' and $datum['details']['packageType'] == 0){

					

					// select only single item lighting entries				

					$ret[] = $datum;

										

				}

				

				if($type != 'lighting'){

									

					$ret[] = $datum;

					

				}

				

			}

			

			return $ret;

			

		}

		

	}

	

	public function getAllRequiredExcept($type, $id){

		

		switch($type){

			
			case 'food':
			case 'bar':

				

				$query = "select * from inventory where id != $id and type != 'labor' and type != 'venueFee' and type != 'lighting' order by type";

				

				break;

				

			case 'lighting':

			

				$currentItem = $this->getSingleItem($id);

				

				$query = "select * from inventory where id != $id and type = 'lighting'";

				

				break;

				

			default:

			

				return false;

				

				break;	

			

		}

				

		if($data = $this->db->query($query)){

			

			while($datum = $data->fetch(PDO::FETCH_ASSOC)){

				

				$datum['details'] = json_decode($datum['details'], 1);

				

				if($type == 'lighting' and $currentItem['details']['lightingPackageType'] != '0'){

					

					// select only single item lighting entries				

					$ret[] = $datum;

										

				}

				

				if($type != 'lighting'){

									

					$ret[] = $datum;

					

				}

								

			}

			

			return $ret;

			

		}

		

	}

	

	public function getItemsByType($type){

		

		$query = "select * from inventory where type = '$type'";

		

		if($data = $this->db->query($query)){

			

			foreach($data as $datum){

				

				$datum['details'] = json_decode($datum['details'], 1);

				

				$ret[] = $datum;

				

			}

			

			return $ret;

			

		}

		

	}

	

	public function getAllCategories(){

		

		$query = "select type from inventory where type != 'descriptor' and type != 'av' group by type";

		

		if($data = $this->db->query($query)){

			

			foreach($data as $type){

				

				$ret[] = $type['type'];

				

			}

			

			return $ret;

			

		}

		

	}

	

	public function getAllOptions($itemType){

		

		switch ($itemType){

			

			case 'bar':

			

				$options = array(

					'category' => $this->category,

					'requiredItemList' => $this->requiredItemList

				);

				

				break;

				

			case 'decor':

			

				$options = array(

					'descriptor' => $this->descriptor

				);

			

				break;	

				

			case 'eventPlanningPackage':

				

				$options = array(

					'packageType' => $this->packageType

				);

				

				break;	

			

			case 'food':

			

				$options = array(

					'foodCategory' => $this->foodCategory,

					'foodType' => $this->foodType,

					'dietaryInfo' => $this->dietaryInfo,

					'requiredPackageItem' => $this->requiredPackageItem,

					'season' => $this->season,

					'serviceStyle' => $this->serviceStyle,

					'units' => $this->units

				);

				

				break;

				

			case 'labor':

			

				$options = array(

					'laborType' => $this->laborType

				);

				

				break;

				

			case 'lighting':

			

				$options = array(

					'lightingPackageType' => $this->lightingPackageType,

					'requiredItemList' => $this->requiredItemList,

					'venue' => $this->venue

				);

				

				break;		

				

			case 'rental':

			

				$options = array(

					'requiredItemList' => $this->requiredItemList,

					'rentalType' => $this->rentalType

				);

				

				break;

				

			case 'security':

				

				$options = array(

					'requiredItem' => $this->requiredItem,

					'securityFeeType' => $this->securityFeeType,

					'venue' => $this->venue

				);

				

				break;

				

			case 'tent':

			

				$options = array(

					'tentSide' => $this->tentSide,

					'venue' => $this->venue

				);

				

				break;	

				

			case 'venueFee':

			

				$options = array(

					'eventType' => $this->eventType,

					'timeOfDay' => $this->timeOfDay,

					'venue' => $this->venue,

					'venueFeeDays' => $this->venueFeeDays

				);

				

				break;		

			

		}

		

		if(is_array($options)){

			

			foreach($options as $name => $array){

			

				switch($name){

					

					case 'category':

						$retType = 'options';

						break;

						

					case 'descriptor':

						$retType = 'checkboxes';

						break;	

					

					case 'foodCategory':

						$retType = 'options';

						break;

					

					case 'foodType':

						$retType = 'options';

						break;

						

					case 'dietaryInfo':

						$retType = 'checkboxes';

						break;

						

					case 'eventType':

						$retType = 'options';

						break;

						

					case 'laborType':

						$retType = 'options';

						break;	

					

					case 'lightingPackageType':

						$retType = 'options';

						break;

						

					case 'packageType':

						$retType = 'options';

						break;

						

					case 'requiredItemList':

						$retType = 'options';

						break;

						

					case 'requiredPackageItem':

						$retType = 'options';

						break;				

						

					case 'rentalType':

						$retType = 'options';

						break;	

						

					case 'season':

						$retType = 'checkboxes';

						break;

						

					case 'securityFeeType':

						$retType = 'options';

						break;	

						

					case 'serviceStyle':

						$retType = 'checkboxes';

						break;

						

					case 'timeOfDay':

						$retType = 'options';

						break;	

						

					case 'units':

						$retType = 'options';

						break;

						

					case 'venue':

						$retType = 'options';

						break;

						

					case 'venueFeeDays':

						$retType = 'options';

						break;		

						

					default:

						

						break;	

					

				}

				

	 			$optionArray[$name]['option_name'] = $name;

				$optionArray[$name]['display_type'] = $retType;

				$optionArray[$name]['options'] = $array;

				

			}

			

		}else{

			

			$optionArray = null;

			

		}

		

		$allOptions = $optionArray;

		

		return $allOptions;

		

	}

		

	public function getSingleItem($itemId){

		

		$query = "select * from inventory where id = $itemId";

				

		if($data = $this->db->query($query)){

			

			$datum = $data->fetch(PDO::FETCH_ASSOC);

			

			$datum['details'] = json_decode($datum['details'], 1);

			

			if(empty($datum['details']['imageName'])){

			

				$datum['details']['imageName'] = 'comingsoon.jpg';

				

			}

			

			if(file_exists("../_images/inventory/" . $datum['details']['imageName'])){

				

				

				

			}else{

				

				$datum['details']['imageName'] = 'comingsoon.jpg';

				

			}

			

			$ret[] = $datum;

			

			return $ret[0];

			

		}

		

	}

	

	// replaces options in the array with their human readable values

	// returns the same inventory object as array

	public function parseDetails($inventoryObject){

		

		$ret = $inventoryObject;

		

		// replace default detail keys with their values

		$optionArray = $this->getAllOptions($ret['type']);

				

		foreach($ret as $detailKey => $detailValue){

			

			if(isset($optionArray[$detailKey]['option_name'])){

				

				if($detailKey == $optionArray[$detailKey]['option_name']){

				

					if($optionArray[$detailKey]['display_type'] == 'options'){

						

						// dropdown options, merge only 1 value

						$ret[$detailKey] = $optionArray[$detailKey]['options'][$detailValue];

					

					}else{

						

						$ret[$detailKey] = null;

						$setOptions = $optionArray[$detailKey]['options'];

						

						// checkbox options, merge multiple values

						

						if($detailValue){

							

							// explode object values into array

							foreach($detailValue as $objectValueKey => $objectValueValue){

								

								//print_r($setOptions);

								

								

								

								foreach($setOptions as $setKey => $setValue){

									

									//echo $setKey .' - '. $objectValueValue .' ';

									

									if($setKey == $objectValueValue){

										

										//echo $setValue.'    ';

									

										$ret[$detailKey] .= $setValue.' ';

										

									}

									

									//echo '<br />';

									

								}

																																

							}

							

						}

						

					}

					

				}

				

			}

								

		}

		

		// handle special cases

		switch ($ret['type']){

		

			case 'venueFee':

				

				// set custom venue name

				if($ret['venue'] == 'Off-Site'){

					

					$ret['venue'] = $ret['offSiteVenueName'];

						

				}

				

				// set custom time of day				

				if($ret['timeOfDay'] == 'Custom'){

					

					$ret['timeOfDay'] = $ret['customTimeOfDay'];

					

				}

				

				// get the venue rooms

				if($ret['venueRoom'] >= 0){

					

					// check the venue id

					switch($ret['venue']){

						

						case 'The Bridge Building':

						

							$ret['venueRoomName'] = $this->bridgeBuildingRooms[$ret['venueRoom']];

							break;

							

						case 'The Bell Tower':

						

							$ret['venueRoomName'] = $this->bellTowerRooms[$ret['venueRoom']];

							break;

							

						default:

						

							$ret['venueRoomName'] = 'N/A';		

						

					}

					

				}

				

				break;

			

		}

		

		// handle images

		if(empty($ret['imageName'])){

			

			$ret['imageName'] = 'comingsoon.jpg';

			

		}

		

		if(file_exists("../_images/inventory/" . $ret['imageName'])){

			

			

			

		}else{

			

			$ret['imageName'] = 'comingsoon.jpg';

			

		}

										

		return $ret;

		

	}

		

	public function saveImageFromPOST($fileInfo, $fileName){

		

		$validextensions = array("jpeg", "jpg", "png", "JPG");

		$temporary = explode(".", $fileInfo["file"]["name"]);

		

		$file_extension = end($temporary);

		

		if((($fileInfo["file"]["type"] == "image/png") || ($fileInfo["file"]["type"] == "image/jpg") || ($fileInfo["file"]["type"] == "image/jpeg"))

		&& ($fileInfo["file"]["size"] < 5000000)//Approx. 5000kb files can be uploaded.

		&& in_array($file_extension, $validextensions)) {

		if ($fileInfo["file"]["error"] > 0)

		{

		return false;

		//return "Return Code: " . $fileInfo["file"]["error"] . "<br/><br/>";

		}

		else

		{

		if (file_exists("../_images/inventory/" . $fileName.'.'.$file_extension)) {

		return false;

		//return $fileInfo["file"]["name"] . " <span id='invalid'><b>already exists.</b></span> ";

		}

		else

		{

		$sourcePath = $fileInfo['file']['tmp_name']; // Storing source path of the file in a variable

		$targetPath = "../_images/inventory/".$fileName.'.'.$file_extension; // Target path where file is to be stored

		move_uploaded_file($sourcePath,$targetPath) ; // Moving Uploaded file

		

		$this->createImageThumbnail(100, "../_images/inventory/", "../_images/inventory/thumbs/", $fileName.'.'.$file_extension);

		

		return $fileName.'.'.$file_extension;

/*

		echo "<span id='success'>Image Uploaded Successfully...!!</span><br/>";

		echo "<br/><b>File Name:</b> " . $fileInfo["file"]["name"] . "<br>";

		echo "<b>Type:</b> " . $fileInfo["file"]["type"] . "<br>";

		echo "<b>Size:</b> " . ($fileInfo["file"]["size"] / 1024) . " kB<br>";

		echo "<b>Temp file:</b> " . $fileInfo["file"]["tmp_name"] . "<br>";

*/

		}

		}

		}

		else

		{

		return false;

		//return "<span id='invalid'>***Invalid file Size or Type***<span>";

		}

		

	}

		

	public function updateItem($itemId, $object){

				

		$details = json_encode($object);

		$details = $this->db->quote($details);

		

		$query = "update inventory set details = $details where id = $itemId";

		

		$update = $this->db->prepare($query);

		

		if($update->execute()){

			

			return true;

			

		}

		

	}

	

}

	

?>