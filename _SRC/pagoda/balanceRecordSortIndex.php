<?php

// echo ord('0') .'<br / >';
// echo ord('z') .'<br / >';
// echo ord('a') .'a<br / >';
// echo ord('U') .'U<br / >';
// echo chr(
// 	(ord('0') + ord('z')) / 2
// ) .'<br / >';
// die();
header('Access-Control-Allow-Origin: *');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
date_default_timezone_set('America/Chicago');

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
$pdo = require_once APP_ROOT.'getDbConnection.php';

$instanceID = $_REQUEST['pagodaAPIKey'];
$db = new pgObjects($pdo, $instanceID, $rules, $write, $writeDocsDB);

if (empty($instanceID)) {
	die();
}

$pageSize = 100;

// $sets = $db->where(
// 	'entity_type' 		// $objectType
// 	, [
// 		'sortIndexIsBalanced' => [
// 			'type' => 		'not_equal'
// 			, 'value' => 	true
// 		]
// 	] 					// $where
// 	, '' 				// $additionalClause
// 	, [
// 		'name' => 		true
// 		, 'bp_name' => 	true
// 	] 					// $select
// 	, true 				// $isPaged
// 	, 0 				// $offset
// 	, 'date_created' 	// $sortCol
// 	, 'desc' 			// $sortDir
// 	, 1000 				// $limit
// 	, null 				// $sum
// 	, [] 				// depricated select arr.
// 	, 'date' 			// $sortCast
// 	, false 			// $forceLimit
// );

$prevIndex = '';

error_reporting(E_ERROR);
ini_set('display_errors', '1');

// foreach($sets as $set){

// 	echo $set['name'] .'<br />';

	$pageOffset = 0;
	if (!empty($_REQUEST['pageStart'])) {
		$pageOffset = intval($_REQUEST['pageStart']);
		$top = $db->where(
            '#.' // $objectType
            , [
                'sortIndexIsBalanced' => [
                    'type' => 		'not_equal'
                    , 'value' => 	true
                ]
            ] 					// $where
            , '' 				// $additionalClause
            , [
                'name' => 		true
            ] 					// $select
            , true 				// $isPaged
            , 0 		        // $offset
            , 'sortIndex' 	    // $sortCol
            , 'DESC' 			// $sortDir
            , 1 		        // $limit
            , null 				// $sum
            , [] 				// depricated select arr.
            , 'string' 			// $sortCast
            , true 				// $forceLimit
        )[0];
		$prevIndex = $top['sort_index'];
		// var_dump($prevIndex);
		// die();
	}

	$objs = $db->where(
		'#.' // $objectType
		// '#'. $set['bp_name'] // $objectType
		, [
			'sortIndexIsBalanced' => [
				'type' => 		'not_equal'
				, 'value' => 	true
			]
		] 					// $where
		, '' 				// $additionalClause
		, [
			'name' => 		true
		] 					// $select
		, true 				// $isPaged
		, $pageOffset 		// $offset
		, 'date_created' 	// $sortCol
		, 'asc' 			// $sortDir
		, $pageSize 		// $limit
		, null 				// $sum
		, [] 				// depricated select arr.
		, 'date' 			// $sortCast
		, true 				// $forceLimit
	);
	// var_dump($objs);
	// die();

	while (count($objs) > 0) {
		
		echo '..... PAGE: '. $pageOffset*$pageSize . ' / '. $objs[0]['full_count'] .' | ['. $objs[0]['id'] .'] | '. $prevIndex .'<br />';
		flush();
		ob_flush();

		foreach($objs as $i => $obj){
			
			// !TODO: Set the 'sort_index' value
			// Set the initial record value
			$prevIndex = $db->moveSortOrder(
				$obj['id']
				, 'before'
				, $prevIndex
				, ''
				, '#'. $set['bp_name']
			)['sort_index'];
			// $db->update(array(
			// 	'objectType'=>'contacts',
			// 	'objectData'=>array(
			// 		'id'=>$obj['id'],
			// 		'type'=>1929600	
			// 	)
			// ));
			// die();
			
		}
		
		$pageOffset++;
		$objs = $db->where(
			'#.' // $objectType
			// '#'. $set['bp_name'] // $objectType
			, [
				'sortIndexIsBalanced' => [
					'type' => 		'not_equal'
					, 'value' => 	true
				]
			] 					// $where
			, '' 				// $additionalClause
			, [
				'name' => 		true
			] 					// $select
			, true 				// $isPaged
			, $pageOffset*$pageSize // $offset
			, 'date_created' 	// $sortCol
			, 'asc' 			// $sortDir
			, $pageSize 		// $limit
			, null 				// $sum
			, [] 				// depricated select arr.
			, 'date' 			// $sortCast
			, true 				// $forceLimit
		);
		
	}
	
// 	echo '.......... COMPLETED.<br /><br />';
// 	// !TODO: Update the 'sort_index_is_balanced' value on the set
	
// }

echo 'COMPLETE<br />';
die();

?>