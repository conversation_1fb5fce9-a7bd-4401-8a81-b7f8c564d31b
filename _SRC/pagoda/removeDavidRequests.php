<?php

// error_reporting(E_ALL);
// ini_set('display_errors', '1');

/*
header('Access-Control-Allow-Origin: *');

if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
}

define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );

require_once APP_ROOT.'_objects.php';

require_once APP_ROOT.'_pgObjectsMT.php';

require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
require_once APP_ROOT.'stripe/init.php';

if(!empty($_REQUEST['do'])){

	header('Access-Control-Allow-Origin: *');
	
	if($_REQUEST['pagodaAPIKey'] == 'petermikhail' or $_REQUEST['pagodaAPIKey'] == 'rickydev' or $_REQUEST['pagodaAPIKey'] == 'pagodastaging' or $_REQUEST['pagodaAPIKey'] == 'zachvoltz' or $_REQUEST['pagodaAPIKey'] == 'joshgantt'){
		require_once '../_app/_config.php';
	}else{
		require_once APP_ROOT.'../app/_app/_config.php';
	}
	
	$objects = new Objects($pdo);

	$pgObjects = new pgObjects($pdo, $appConfig['instance']);
	
	$comms = new Comm($pgObjects, $appConfig);
	
	$cookies = new Cookies($pgObjects);
	
	$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey']);

	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
	
	$cookie = $cookies->checkCookie($_COOKIE['uid'], 'user', $_COOKIE['series'], $_COOKIE['token']);

	if(!empty($_REQUEST['json'])){
		$_POST = json_decode($_REQUEST['json']);
	}
	
	if($_REQUEST['do'] === 'run'){
		
		$batch = 0;
		$batchSize = 10;
		$staffList = $app->getAllObjects('staff', true, false);
		
		function updateContacts($batch, $batchSize, $staffList, $pgObjects, $app){
			
			// contacts to delete (640 is davids no.)
			$contacts = $pgObjects->where('contacts', array('manager' => 640, 'data_source' => 3224), '', 0, true, 0, 'id', 'asc', $batchSize);
			if(empty($contacts)){
				return false;
			}
			echo 'contacts: ' . count($contacts) . '<br />';
			
			foreach($contacts as $i => $contact){
				echo $contact['id'] . '<br />';
				// round robin the contact manager
				$assignTo = $app->smRoundRobin(0, $staffList);
				$pgObjects->update('contacts', array('id' => $contact['id'], 'manager' => $assignTo['id']));
				echo 'assign to: '. $assignTo['id'] . ' (userId) <br />';
				foreach($staffList as $j => $staffMember){
					if($staffMember['id'] === $assignTo['related_object']['id']){
						$staffList[$j]['daily_requests']++;
						echo $staffList[$j]['id'] .' -- '. $staffList[$j]['daily_requests'] . '<br />';
					}
				}
				
			}
			ob_flush();
			flush();
			return updateContacts($batch + 1, $batchSize, $staffList, $pgObjects, $app);
			
		}
		
		updateContacts($batch, $batchSize, $staffList, $pgObjects, $app);
		
	}
	
}
*/
	
?>