<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

// Suppress warnings while echoing to build html for the page
error_reporting(E_ERROR | E_PARSE);

$http_origin = $_SERVER['HTTP_ORIGIN'];

if ($http_origin == "http://localhost:8080" || $http_origin == "http://localhost:8084") {  
    header("Access-Control-Allow-Origin: $http_origin");
} else {
	header("Access-Control-Allow-Origin: $http_origin");
}

header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
date_default_timezone_set('America/Chicago');

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';
require 'BENTO_ENV.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'rules/rules.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';

// Get the JSON request
$request = json_decode($_POST['postData']);

// Set instance
$instanceName = $request->authData->varPagodaAPIKey;

// Get database object
$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");
	
// Build the rules api
$rules = new Rules($appConfig);

// Get page objects
$pgObjects = new pgObjects($pdo, $instanceName, $rules);

// Initialize cookies
$cookies = new Cookies($pgObjects);

// Check if cookie is already setup properly
$hasCookie = empty($_COOKIE['token']) && empty($_COOKIE['series']) && empty($_COOKIE['uid']) ? false : true;
$createCookie = $hasCookie ? false : true;
$destroyCookie = $createCookie ? true : false;
$isPublicEndpoint = $hasCookie ? false : true;

// Create cookie
$cookie = $createCookie ? $cookies->createCookie(1, '', $_SERVER['REMOTE_ADDR'], rand(), 'mergeService', false) : $_COOKIE;

// Set auth data
$authData = [
	'varToken' => $cookie['token'],
	'varSeries' => $cookie['series'],
	'varUid' => $cookie['uid'],
	'varInstance' => $instanceName,
	'varPagoda' => $instanceName,
	'varPagodaAPIKey' => $instanceName
];

// Set data
$data = [
	'contextId' 	=> $request->contextId,
	'templateId'  	=> $request->templateId,
	'templateHtml'  => $request->templateHtml,
	'authData'  	=> $authData
];

if ($isPublicEndpoint) {

	// Remove the template html since we don't want to allow this publically
	unset($data['templateHtml']);

	if (!empty($request->templateId)) {

		// Snag the document
		$document = $pgObjects->getById('contracts', $request->templateId);

		// Return the html if not an active document
		if ($document['active'] == 'No') {
			echo $document['html_string'];
			exit();
		}

	}

	// If template Id is not set or document is not found, but template html is set, just spit it back out
	if (
		(empty($request->templateId) || empty($document))
		&& !empty($request->templateHtml)
	) {
		echo $request->templateHtml;
		exit();
	}

}

$postData = [
	'postData' 	=> $data
];

// Package up payload to json
$payload = json_encode($postData);

// Merge
$response = $pgObjects->mergeEndpoint($payload);

// Destroy cookie
if ($destroyCookie) {
	$cookies->destroyCookie($cookie['uid'], '', $cookie['series']);
}

// Return html
echo $response;
// echo $response['html'];
// echo $response->html;

// Exit script
exit();
?>