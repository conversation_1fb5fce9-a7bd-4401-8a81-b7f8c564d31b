<?php

class TwimlConversation {
	
	public $callType;
	public $from;
	public $caller = null;
	public $callerType = 'unknown';
	public $say;
	
	// 	PUBLIC FUNCTIONS
	
	function __construct($app, $req, $instance){
		
		$this->app = $app;
		$this->req = $req;
		$this->instance = $instance;
		
		$this->setCallType();
		$this->setFrom();
		$this->setCaller($this->from);
		
	}
	
	public function addSentance($words){
		$this->say .= $this->createSentance($words);
	}
	
	public function beginTree($conversationType = 'tree_selection'){
		
		$staffList = $this->app->getObjectsWhere('staff', array('enabled'=>1), false);
		
		$sentance = $this->createSentance('Please choose from the following options.');
		
		foreach($staffList as $staff){
			
			$sentance .= $this->createSentance('Press '. implode(' ',str_split($staff['pin'])) .' for '. $staff['fname'] .' '. $staff['lname'] .'.'); 
			$sentance .= $this->pause(1);
			
		}
		
		$this->say .= $this->gather($sentance, $conversationType, 2);
		
	}
		
	public function forwardCall($to, $callerId = null){
		$this->say .= $this->connectCall($to, $callerId);
	}
	
	public function getKeypadEntry($words, $numDigits = null, $redirect = null, $timeout = 10){
		$this->say .= $this->gather($this->createSentance($words), $redirect, $numDigits, $timeout);
	}
	
	public function placeOutgoingCall(){
		$this->say .= $this->gather( $this->createSentance('If you would like to place an outgoing call, enter the phone number followed by the pound sign.'), 'outgoing_call' );
	}
	
	public function sayHello(){
		if($this->caller['fname']){
			$this->addSentance('Hello '. $this->caller['fname'] .'.');
		}else{
			$this->addSentance('Hello.');
		}
	}
		
	public function speak(){
		
		header('Content-type: application/xml');
		
		echo '<Response>';
		echo $this->say;
		echo '</Response>';
		
	}
	
	public function wait($seconds = 1){
		$this->say .= $this->pause($seconds);
	}
	
	
	
	/* 	PRIVATE FUNCTIONS */
	
	private function connectCall($numberToCall, $callerId = null){
		
		if($callerId == null){
			$callerId = $this->req['From'];
		}
		
		return '<Dial callerId="'. $callerId .'" action="_twilioIncoming.php?instance='. $this->instance .'&amp;conversation=disconnect" method="POST" timeout="30" >'. $numberToCall .'</Dial>';
	}
	
	private function createSentance($words){
		return '<Say voice="woman">'. $words .'</Say>';
	}
	
	private function gather($inner = null, $conversation = 'disconnect', $numDigits = 100, $timeout = 10, $finishOnKey = '#'){
		return '<Gather numDigits="'. $numDigits .'" action="_twilioIncoming.php?instance='. $this->instance .'&amp;conversation='. $conversation .'" method="POST" timeout="'. $timeout .'" finishOnKey="'. $finishOnKey .'">'. $inner .'</Gather>';
	}
	
	private function hangUp(){
		return '<Hangup/>';
	}
	
	private function pause($seconds = 1){
		return '<Pause length="'. $seconds .'"/>';
	}
	
	private function setCallType(){
		if($this->req['CallSid']){	
			$this->callType = 'call';
		}
		if($this->req['MessageSid']){
			$this->callType = 'sms';
		}
	}
	
	private function setFrom(){
		$this->from = substr($this->req['From'], 2);
	}
	
	private function setCaller($from){
		
		$this->caller = $this->app->getObjectsWhere('staff', array('phone'=>$from), false, 1)[0];
		if($this->caller){
			$this->callerType = 'user';
			return;
		}
		
		$info = $this->app->getObjectsWhere('contact_info', array('info'=>$from), false, 1)[0];
		$this->caller = $this->app->getObjectsWhere('contacts', array('id'=>$info['object_id']), false, 1)[0];
		if($this->caller){
			$this->callerType = 'contact';
			return;
		}
		
	}
		
}

?>