<?php

// 	error_reporting(E_ALL);
// 	ini_set('display_errors', '1');
	die();
	$_REQUEST['pagodaAPIKey'] = 'zachvoltz';
	set_time_limit(0);
	
	header('Access-Control-Allow-Origin: *');
	echo 'connecting...<br />';
	
	if (extension_loaded ('newrelic')) {
		newrelic_set_appname ("Pagoda");
		newrelic_background_job();
		newrelic_ignore_apdex();
	}
	
	define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
	
	require_once $_SERVER['DOCUMENT_ROOT'].'/app/_app/_config.php';
	require_once APP_ROOT.'/lib/_.php';
	require_once APP_ROOT.'_pgObjectsMTAdmin.php';
	require_once APP_ROOT.'_objects.php';
	require_once APP_ROOT.'_communications.php';
	require_once APP_ROOT.'_cookiesPG.php';
	require_once APP_ROOT.'files/_fileApi.php';
	require_once APP_ROOT.'stripe/init.php';
	
	require_once $_SERVER['DOCUMENT_ROOT'].'/app/_app/_config.php';
	
	$objects = new Objects($pdo);
	$pgObjects = new pgObjectsAdmin($pdo, $appConfig['instance']);
	$comms = new Comm($pgObjects, $appConfig);
	$cookies = new Cookies($pgObjects);
	$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey']);
	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
	
	$my_get = 
	
	$updater = new ObjectsUpdater(
		
		$app,
		['thelifebook']
		
	);
	
	$updater->runUpdates();
	
	echo 'OBJECT UPDATES COMPLETE;<br />';
	
	class ObjectsUpdater {
		
		// !PLACE CALLS TO GET DATA HERE
		private function my_getter($page){
			
			$childObjs = 2;
			
			$selectionArr = [
				'students' => 0,
				'adults' => 0
			];
			
			$requests = $this->app->pgObjects->where(
				'requests',
				$selectionArr,
				'',
				$childObjs,
				$page['paged'],
				$page['offset'],
				$page['sortCol'],
				$page['sortDir'],
				$page['limit']
			);	

			return $requests;
			
		}
		
		// !PLACE CALLS TO PROCESS AND UPDATE YOUR DATA HERE
		private function my_updater($requests){
			
			$toUpdate = [];
			
			// process data
			if(is_array($requests)){
				foreach($requests as $i => $request){
					
					$adults = 0;
					$students = 0;
					
					// filter contact infos
					$adults = intval(__::last(__::sortBy(__::filter($request['contact']['contact_info'], function($contactInfo){
						
						// adults info type in thelifebook instance is 65
						return $contactInfo['type'] === 65;
						
					}), function($contactInfo){
						
						return strtotime($contactInfo['date_created']);
						
					}))['info']);
					
					$students = intval(__::last(__::sortBy(__::filter($request['contact']['contact_info'], function($contactInfo){
						
						// students info type in thelifebook instance is 64
						return $contactInfo['type'] === 64;
						
					}), function($contactInfo){
						
						return strtotime($contactInfo['date_created']);
						
					}))['info']);
					
					// add to batch to update in db
// 					echo $request['contact']['id'];
// 					die();
					if($students > 0 or $adults > 0){

						$temp = array(
							'id' => $request['id'],
							'students' => $students,
							'adults' => $adults
						);
						
						
						
						// use values if they are already set
						if($request['adults'] > 0){
							$temp['adults'] = $request['adults'];
						}
						if($request['students'] > 0){
							$temp['students'] = $request['students'];
						}
						
						// if adults or students still = 0, increment the offset so they don't repeat
						if($temp['students'] == 0 or $temp['adults'] == 0){
							$this->offset++;
						}
						
						array_push($toUpdate, [
							'id' => $temp['id'],
							'students' => $temp['students'],
							'adults' => $temp['adults'],
							'object_bp_type' => 'requests'
						]);
						
					}else{
						
						$this->offset++;
						
					}
					
				}
			}
			
			// update in database
			if($response = $this->app->pgObjects->update('requests', $toUpdate)){
				
				return true;
				
			}else{
				
				return false;
				
			}
			
		}
		
		private function pluck($array, $key){
			
			$ret = [];
			if(is_array($array)){
				
				foreach($array as $i => $entry){
					
					array_push($ret, $entry[$key]);
					
				}
				
			}
			
			return $ret;
			
		}
		
		public function __construct($app, $instances){
			
			$this->app = $app;
			$this->instances = $instances;
			$this->batchSize = 50;
			
			$this->offset = 0;
			
		}
		
		private function getBatch(){
			
			$page = array(
				'paged' => true,
				'offset' => $this->offset,
				'sortCol' => 'date_created',
				'sortDir' => 'asc',
				'limit' => $this->batchSize,
			);
			
			return $this->my_getter($page);
			
		}
		
		private function updateBatch($data){
			
			return $this->my_updater($data);
			
		}
		
		public function runUpdates(){
			
			echo 'Updates started...<br />';
			foreach($this->instances as $i => $instance){
				
				$this->app->pgObjects->setInstance($instance);
				echo 'Changing instances to '. $instance .'...<br />';
				
				$numBatches = 0;
				$counter = 0;
				$proceed += true;
				$data = $this->getBatch();
				while($proceed){
					
					// update batch
					$this->updateBatch($data);
					$counter += count($data);
					echo count($data) ." items updated<br />";
					
					// get next batch
// 					$this->offset += $this->batchSize;
					$data = $this->getBatch();
					$numBatches++;
					if(count($data) == 0){
						$proceed = false;
					}elseif($numBatches == 10){
						$proceed = false;
					}
					
				}
				
				echo $counter .' items updated<br />';
				
				echo 'NEW OFFSET = '. $this->offset;
				
			}
			
		}
		
	}
	
?>