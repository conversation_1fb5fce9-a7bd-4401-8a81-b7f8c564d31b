<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

require("quickbooks/Client.php");
$configs = include('quickbooks/config.php');

$session_id = session_id();
if (empty($session_id))
{
    session_start();
}
$authorizationRequestUrl = $configs['authorizationRequestUrl'];
$tokenEndPointUrl = $configs['tokenEndPointUrl'];
$client_id = $configs['client_id'];
$client_secret = $configs['client_secret'];
$scope = $configs['oauth_scope'];
$redirect_uri = $configs['oauth_redirect_uri'];


$response_type = 'code';
$state = $_GET['i'];
$include_granted_scope = 'false';
$grant_type= 'authorization_code';
//$certFilePath = './Certificate/all.platform.intuit.com.pem';
$certFilePath = 'quickbooks/Certificate/cacert.pem';


$client = new Client($client_id, $client_secret, $certFilePath);

date_default_timezone_set('America/Chicago');
			
if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
/*
	newrelic_background_job();
	newrelic_ignore_apdex();
*/
}

if (!isset($_GET["code"]))
{
    /*Step 1
    /*Do not use Curl, use header so it can redirect. Curl just download the content it does not redirect*/
    //$json_result = $client->getAuthorizationCode($authorizationRequestUrl, $scope, $redirect_uri, $response_type, $state, $include_granted_scope);
    //unset $_SESSION global variables
    unset($_SESSION['access_token']);
    unset($_SESSION['refresh_token']);
    $authUrl = $client->getAuthorizationURL($authorizationRequestUrl, $scope, $redirect_uri, $response_type, $state);
//var_dump($authorizationRequestUrl);
//var_dump($authUrl);    
    header("Location: ".$authUrl);
    exit();
}
else
{
	
	$_REQUEST['pagodaAPIKey'] = $_GET['state'];
	
	define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );

	require_once APP_ROOT.'/lib/_.php';
	
	if(!class_exists('MyApp')){
		require_once '../app/_app/_config.php';
	}
	//require_once '../app/_app/_config.php';
	require_once '_objects.php';
	require_once '_pgObjectsMT.php';
	require_once '_communications.php';
	require_once '_cookiesPG.php';
	require_once 'files/_fileApi.php';
	
	$objects = new Objects($pdo);
	$pgObjects = new pgObjects($pdo, $_GET['state']);
	$comms = new Comm($pgObjects, $appConfig);
	$cookies = new Cookies($pgObjects);
	$files = new FileApi($pgObjects, $_SERVER["DOCUMENT_ROOT"].'/_files/_instances/rickyvoltz');
	
	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
	
    $code = $_GET["code"];
    $responseState = $_GET['state'];
//var_dump('test');    
    if(strcmp($state, $responseState) != 0){
      //throw new Exception("The state is not correct from Intuit Server. Consider your app is hacked.");
    }

    $result = $client->getAccessToken($tokenEndPointUrl,  $code, $redirect_uri, $grant_type);
    //record them in the session variable
    $_SESSION['access_token'] = $result['access_token'];
    $_SESSION['refresh_token'] = $result['refresh_token'];

	$user = $app->getObjectById('users', $_COOKIE['uid'], 0);
	$instances = $app->getObjectsWhere('instances', array('instance'=>$responseState), 0);
	$instance = $instances[0];
	
	$instance['quickbooks_access_token'] = $result['access_token'];
	$instance['quickbooks_refresh_token'] = $result['refresh_token'];

	$updatedInstance = $app->updateObject($instance, 'instances', 0);

    //
    // JS to close popup and refresh parent page
    echo '<script type="text/javascript">
                window.opener.location.href = window.opener.location.href;
                window.close();
              </script>';

}


?>
