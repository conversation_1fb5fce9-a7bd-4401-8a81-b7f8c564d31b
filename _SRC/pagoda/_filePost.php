<?php


// error_reporting(E_ALL);
// ini_set('display_errors', '1');


header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: X-Requested-With");
header('Access-Control-Allow-Credentials: true');

define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
define( "FILES_BUCKET", realpath('https://pagoda.nyc3.digitaloceanspaces.com/_instances').'/');

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';

require_once APP_ROOT.'/lib/_.php';

require_once APP_ROOT.'rules/rules.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'files/_fileApi.php';

if(!empty($_REQUEST['do'])){
	
	$instanceID = $_REQUEST['pagodaAPIKey'];

	switch($instanceID){
		
		case '_staging':
		case 'petermikhail':
		case 'joshgantt':
		case 'johnwhittenburg':
		case 'rickyvoltz':
		case 'zachvoltz':
	
			$apiFolder = 'notify/pagoda';	
			$instanceURL = $instanceID;
	
			break;
						
		default:
			
			$apiFolder = 'notify/pagoda';
			$instanceURL = '_production';
		
	}
	
	if(!class_exists('App')){
		if(!defined('APP_ROOT')){
			define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
		}
	
		require_once '_app.php';
	
	}
	
	$instanceName = str_replace('/', '', str_replace('/app/', '', $_REQUEST['pagodaAPIKey']));
	
	if(!class_exists('App') or !class_exists('pgObjects')){
		require_once '_pgObjectsMT.php';
	}
	
	if(!class_exists('App') or !class_exists('pgObjectsAdmin')){
		require_once '_pgObjectsMTAdmin.php';
	}
	
	$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");
	
	$rules = new Rules($appConfig);
	$pgObjects = new pgObjects($pdo, $instanceName, $rules);
	$pgObjectsAdmin = new pgObjects($pdo, $instanceName);

	$instance = $pgObjectsAdmin->where('instances', array('instance'=>$instanceName))[0];

	if(extension_loaded ('newrelic')) {
		newrelic_set_appname ($instance['systemName'].";Pagoda");
	}
	
	$appConfig = array();
	foreach($instance as $k => $v){
	
		switch($k){
			
			case 'components':
			case 'pageModules':
			case 'settingsModules':
			
				$appConfig[$k] = explode(',', $v);
			
				break;
			
			case 'db_post':
			
				$appConfig['db']['post'] = $v;
			
				break;
				
			case 'db_read':
			
				$appConfig['db']['read'] = $v;
			
				break;
				
			case 'db_write':
			
				$appConfig['db']['write'] = $v;
			
				break;
				
			case 'files_bucket':
			
				$appConfig['files']['bucket'] = $v;
			
				break;	
				
			case 'files_delete':
			
				$appConfig['files']['delete'] = $v;
			
				break;
				
			case 'files_read':
			
				$appConfig['files']['read'] = $v;
			
				break;
				
			case 'files_write':
			
				$appConfig['files']['write'] = $v;
			
				break;
				
			case 'twilio_sid':
			
				$appConfig['twilio']['sid'] = $v;	
			
				break;
				
			case 'twilio_token':
			
				$appConfig['twilio']['token'] = $v;
				
				break;	
				
			case 'sms_from':
			
				$appConfig['twilio']['smsFrom'] = $v;
				
				break;						
			
			default:
			
				$appConfig[$k] = $v;
			
		}
	
	}
	
	switch($instanceID){
		
		case '_staging':
		case 'petermikhail':
		case 'joshgantt':
		case 'johnwhittenburg':
		case 'rickyvoltz':
		case 'zachvoltz':
	
			break;
						
		default:
			
			$appConfig['db']['post'] = 'https://bento.infinityhospitality.net/api/_post.php?';
			$appConfig['db']['read'] = 'https://bento.infinityhospitality.net/api/_get.php?';
			$appConfig['db']['write'] = 'https://bento.infinityhospitality.net/api/_get.php?';

			$appConfig['files']['read'] = 'https://bento.infinityhospitality.net/api/files/get/?';
			$appConfig['files']['write'] = 'https://bento.infinityhospitality.net/api/files/post/?';
			$appConfig['files']['bucket'] = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com';
			$appConfig['files']['delete'] = 'https://bento.infinityhospitality.net/api/delete/?';
		
	}

	class MyApp extends App {
				
		function __construct($appConfig, $dbConn, $objects, $comms, $pgObjects, $cookies, $filesApi){
			
			$this->appConfig = $appConfig;		
			$this->db = $dbConn;
			$this->obj = $objects;
			$this->comm = $comms;
			$this->pgObjects = $pgObjects;
			$this->cookies = $cookies;
			$this->files = $filesApi;
			
		}
			
	}

	$obj = new pgObjects($pdo, $appConfig['instance'], $rules);
	
	$files = new FileApi($obj, FILES_BUCKET.$_REQUEST['pagodaAPIKey'], $_REQUEST['pagodaAPIKey']);

	$do = $_REQUEST['do'];
	
	if($_POST['getChildObjs']){
		$childObjs = $_POST['getChildObjs'];
	}else{
		$childObjs = 0;
	}
	
	if(is_numeric($childObjs)){
		$childObjs = intval($childObjs);
	}
	
	if ($do === 'create') {

		$_POST = json_decode($_POST['objectData'], true);

		// create object
		if($objectData = $obj->$do($_POST['sys-obj-type'], $_POST, 0, $childObjs)){

			$fileType = str_replace('application/','',$_FILES['file']['type']);
			$fileType = str_replace('image/','',$fileType);
			
			// upload file and create file meta data
			$fileData = array(
				'fileName' => $_FILES['file']['name'],
				'fileType' => $fileType,
				'objectType' => $_POST['sys-obj-type'],
				'objectId' => $objectData['id'],
				'parent' => $_POST['parent'],
				'tagged_with' => $_POST['tagged_with'],
				'isPublic' => 1
			);

			if($_FILES['file']){
				
				if($fileMetaData = $files->upload($_FILES['file'], $fileData, 0)){

					// update object with file id
					if($updatedObj = $obj->update($_POST['sys-obj-type'], array(
						'id' => $objectData['id'],
						$_POST['sys-file-prop-key'] => $fileMetaData['id'],
						'file_type' => $fileMetaData['file_type'],
						'loc' => $fileMetaData['loc']
					), $childObjs)){
						
						$updatedObj['file_meta_data'] = $fileMetaData;

						header("Content-Type: application/json");
						echo json_encode($updatedObj);
						return;
						
					}
					
				}
				
			}else{
				
				header("Content-Type: application/json");
				echo json_encode($objectData);
				return;
				
			}
			
		}else{
			
			return false;
			
		}
		
	} elseif ($do === 'update') {
		
		$objUpds = $_POST;
		if ($_POST['_fieldName']) {
			unset($objUpds[$_POST['_fieldName']]);
			unset($objUpds['_fieldName']);
		}
// 		var_dump('test:;', $_POST['sys-obj-type'], $objUpds, $childObjs, $_POST);
// 		die();
		// update object
		if ($objectData = $obj->$do($_POST['sys-obj-type'], $objUpds, $childObjs)) {
			
			$fileType = str_replace('application/','',$_FILES['file']['type']);
			$fileType = str_replace('image/','',$fileType);
			
			// upload file and create file meta data
			$fileData = [];
			$isEntity = false;
			
			// If a field name is provided to place the file on, use it
			if ($_POST['_fieldName']) {
				
				$fieldName = $_POST['_fieldName'];
				$fileData = array(
					'fileName' => 	$_POST['file_upload_name'],
					'fileType' => 	$fileType,
					'objectType' => 'document',
					'objectId' => 	$objectData['id'],
					'parent' => 	'0'
				);
				
				if (empty($fileData)) {
					
					$fileData['fileName'] = $_POST[$fieldName];
					
				}
				$isEntity = true;
				
			} else {
				
				$fieldName = 'file';
				$fileData = array(
					'fileName' => $_FILES['file']['name'],
					'fileType' => $fileType,
					'objectType' => $_POST['sys-obj-type'],
					'objectId' => $objectData['id'],
					'parent' => $_POST['file-parent']
				);
				if (empty($fileData['fileName'])) {
					$fileData['fileName'] = 'img';
				}
				
			}
			
			if ($_FILES['file']) {
				
				if ($fileMetaData = $files->upload($_FILES['file'], $fileData, 0)) {
					
					// Point from the object data to the file
					$upd = array(
						'id' => $objectData['id'],
						$_POST['sys-file-prop-key'] => $fileMetaData['id']
					);
					
					if ($isEntity) {
						
						$upd[$fieldName] = $objectData[$fieldName];

						if (is_array($upd[$fieldName])) {
							
							array_push($upd[$fieldName], $fileMetaData['id']);

						} else {
							
							$upd[$fieldName] = [$fileMetaData['id']];

						}
						
					}
					
					// update object with file id
					if ($updatedObj = $obj->update($_POST['sys-obj-type'], $upd)) {
						
						$updatedObj['file_meta_data'] = $fileMetaData;
						header("Content-Type: application/json");
						echo json_encode($updatedObj);
						return;
						
					}
					
				}
				
			} else {
				
				header("Content-Type: application/json");
				echo json_encode($objectData);
				return;
				
			}
			
		} else {
			
			return false;
			
		}
		
	} elseif ($do === 'upload') {
		
		// upload file and create file meta data
		$fileData = array(
			'fileName' => 		$_FILES['_file']['name'],
			'fileType' => 		$_POST['fileType'],
			'objectType' => 	$_POST['objectType'],
			'objectId' => 		$_POST['objectId'],
			'parent' => 		$_POST['parent']
		);
		
		if($_FILES['_file']){

			if($fileMetaData = $files->upload($_FILES['_file'], $fileData, 0)){
				
				header("Content-Type: application/json");
				echo json_encode($fileMetaData);
				return;
				
			}
			
		}else{
			
			header("Content-Type: application/json");
			echo json_encode($objectData);
			return;
			
		}
		
	}
	
// 		$files->$do(json_decode($_POST['json'], true));
	
}
