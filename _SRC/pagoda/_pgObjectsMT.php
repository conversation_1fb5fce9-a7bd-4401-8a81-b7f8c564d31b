<?php

// error_reporting(E_ALL);
// ini_set('display_errors', '1');

use chris<PERSON><PERSON><PERSON>\stringCalc;

	class pgObjects {

		private $db;
		private $debug = false;
		private $blueprints = [];
		private $defaultBPProperties = [
				'is_template' => [
					'type' => 'int',
					'name' => 'template',
					'immutable' => false
				]
				, 'object_uid' => [
					'type' => 		'int'
					, 'name' => 	'UID'
					, 'immutable' => false
				]
				, 'parent' => [
					'type' => 		'relatedObject'
					, 'name' => 	'Parent'
					, 'immutable' => false
				]
				, 'type' => [
					'type' => 		'int'
					, 'name' => 		'Type'
					, 'immutable' => false
				]

			];
		private $fieldTypes = [];

		public $instance = '';

		function __construct ($dbConn, $instance = 'root', $rules = []) {

			// Set the initial state/config
			$this->db = $dbConn;
			$this->instance = $instance;
			$this->instanceObj = null;
			$this->userObj = null;
			$this->bpDIR = APP_ROOT .'blueprints';
			$this->writeDb = null;
			$this->docsWriteDb = null;

			// Set child apis to use
			$this->rules = $rules;

			// Set connections to databases
			$this->db = $dbConn;
			$this->db->setAttribute(
				PDO::ATTR_DEFAULT_FETCH_MODE
				, PDO::FETCH_ASSOC
			);
			$this->docsDb = new PDO(
				"pgsql:host=". $GLOBALS['BENTO_DOCS_DB_PATH'] .";
				port=". $GLOBALS['BENTO_DB_PORT'] .";
				dbname=". $GLOBALS['BENTO_DB_DOCS_NAME'] .";
				sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
				user=". $GLOBALS['BENTO_DB_USER'] .";
				password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
			);
			$this->db->setAttribute(
				PDO::ATTR_DEFAULT_FETCH_MODE
				, PDO::FETCH_ASSOC
			);

			// Set the write db conn--if no specific path is provided for the
			// write db, fallback to the standard db conn.
			if ($GLOBALS['BENTO_DB_WRITE_PATH']) {

/*
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
*/

			} else {

				$this->writeDb = $dbConn;

			}
			if ($GLOBALS['BENTO_DOCS_DB_WRITE_PATH']) {

				try{

/*
					$this->docsWriteDb = new PDO(
						"pgsql:host=". $GLOBALS['BENTO_DOCS_DB_WRITE_PATH'] .";
						port=". $GLOBALS['BENTO_DB_PORT'] .";
						dbname=". $GLOBALS['BENTO_DB_DOCS_NAME'] .";
						sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
						user=". $GLOBALS['BENTO_DB_USER'] .";
						password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
					);
*/

				}catch(PDOException $e){

					die($e->getMessage());

				}


			} else {

				$this->docsWriteDb = $this->docsDb;

				$this->docsDb = new PDO("pgsql:host=". $GLOBALS['BENTO_DOCS_DB_PATH'] .";port=". $GLOBALS['BENTO_DB_PORT'] .";dbname=". $GLOBALS['BENTO_DB_DOCS_NAME'] .";sslmode=". $GLOBALS['BENTO_DB_SSL'] .";user=". $GLOBALS['BENTO_DB_USER'] .";password=". $GLOBALS['BENTO_DB_PASSWORD'] ."");

			}

			$fieldsDir = APP_ROOT .'objs/fields';
			$tmp = null;

			$this->appConfig = $appConfig;

			// Collect field types
			$dir = new DirectoryIterator(
				$fieldsDir
			);

			foreach ($dir as $fileinfo) {

				if (
					!$fileinfo->isDot()
					&& $fileinfo->getExtension() === 'php'
				) {

					$tmp = include $rulesDir .'objs/fields' .'/'. $fileinfo->getFilename();

					$this->fieldTypes[$tmp['name']] = [
						'setNext' => 			$tmp['setNext']
						, 'setBetween' => 		$tmp['setBetween']
						, 'setBefore' => 		$tmp['setBefore']
						, 'setAfter' => 		$tmp['setAfter']
					];

				}
			}

		}

		public function test(){
			//var_dump($this->where('staff', array('email' => '<EMAIL>')));

			//echo 'testing';

/*
			$statement = $this->db->prepare('SELECT * FROM object_blueprints;');
			$statement->execute();
			$blueprints = $statement->fetchAll();
			//var_dump($blueprints);
			foreach($blueprints as $i => $datum){

				$datum['blueprint'] = json_decode($datum['blueprint'], true);

				//var_dump($datum['object_type']);

				$statement = $this->db->prepare(
					"INSERT INTO blueprints DEFAULT VALUES RETURNING *;"
				);

				$statement->execute();

				$info = $statement->fetchAll()[0];

				$string = "UPDATE blueprints SET blueprint_type = 'object', instance = 'pagodadev', blueprint_name = '". $datum['object_type'] ."', blueprint = '". trim($this->db->quote(json_encode($datum['blueprint'], JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;";

				$statement = $this->db->prepare($string);

				$statement->execute();

			}
*/


		}

		function mergeEndpoint($payload) {

			// Set
			$endpoint = MERGE_ENDPOINT;
			if ($endpoint == "localhost") {
				$url = "merge:8084";
			} else {
				$url = 'https://' . $endpoint;
			}

			// Curl webhook
			$ch = curl_init($url.'/mergeService');
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			//curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);// Skip SSL Verification
			//curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);// Skip SSL Verification
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
				'Content-Type: application/json',
				'Accept: application/json',
				'Content-Length: ' . strlen($payload)
			));

			$response = curl_exec($ch);

			if ($response === false) {
				return json_encode(array(
					'error' => curl_error($ch)
				));
			}

			curl_close($ch);

			return $response;

		}

		private function wrapTransaction ($string) {

			return 'BEGIN '. rtrim($string, ";") .' END;';

		}

		public function closeDBConnection () {

			// Close all pdo (postgres) connections
			$this->db = null;
			$this->writeDb = null;
			$this->docsDb = null;
			$this->docsWriteDb = null;

			return true;

		}

		public function setInstance($instance){

			$this->instance = $instance;

			return true;

		}

		function setInstanceFromBlueprint($objectType) {

			$instance = $this->instance;

			if (!empty($objectType)) {

				$objectType = (strpos($objectType, '.') !== false) ? explode('.', $objectType)[0] : $objectType;

				$blueprint = $this->getBlueprint($objectType, false, true, true);

				if (in_array($this->instance, $blueprint['shared_with_instances'])) {
					$this->setInstance($blueprint['instance']);
					$instance = $blueprint['instance'];
				}

			}

			return $instance;

		}

		function getObjectBPTypeByIdAcrossInstances($type, $id) {

			$entity = $this->getByIdAcrossInstances('', $id);
			$entity = array_key_exists(0, $entity) ? $entity[0] : $entity;
			$objectType = !empty($entity['object_bp_type']) ? $entity['object_bp_type'] : '';

			if (substr($type, 0, 1) !== '#') {

				if (!empty($entity['parent'])) {
					$relatedObjectID = $entity['parent'];
				} else if (!empty($entity['related_object'])) {
					$relatedObjectID = $entity['related_object'];
				}

				$entity = $this->getByIdAcrossInstances('', $relatedObjectID);

				if (!empty($entity['bp_name'])) {
					$objectType = '#' . $entity['bp_name'];
				} else if (!empty($entity['object_bp_type'])) {
					$objectType = $entity['object_bp_type'];
				}

				if ($objectType == 'groups') {
					$this->setInstance($entity['instance']);
				}

			}

			return $objectType;
		}

		function getObjectTypeFromMethodOrServiceAndPost($methodOrService, $post) {

			switch($methodOrService) {

				case 'getObjectById':
					$objectType = $this->getObjectBPTypeByIdAcrossInstances($post->type, $post->value);
					break;

				case 'getObjectsWhere':
				case 'getObjectBlueprint':
				case 'getAllObjects':
				case 'updateObject':
					$objectType = !empty($post->objectType) ? $post->objectType : '';
					if ($post->objectType == 'notes') {
						$objectType = $this->getObjectBPTypeByIdAcrossInstances('', $post->queryObj->type_id->value);
					} else if ($post->objectType == 'view') {
						if (!empty($post->queryObj->set->values[0])) {
							$objectType = '#' . $post->queryObj->set->values[0];
						} else if (!empty($post->queryObj->set)) {
							$objectType = '#' . $post->queryObj->set;
						}
					} else if ($post->objectType == 'entity_tool') {
						if (empty($post->queryObj->entityType)) {
							die();
						}
						$objectType = $this->getObjectBPTypeByIdAcrossInstances('', $post->queryObj->entityType);
					}
					break;

				case 'getComments':
					$objectType = $this->getObjectBPTypeByIdAcrossInstances('', $post->tagged_with->values);
					break;

				case 'deleteObject':
				case 'restoreObjs':
					$objectType = !empty($post->type) ? $post->type : '';
					break;

				case 'runSteps':
					if (!empty($post->objId)) {
						$objectType = $this->getObjectBPTypeByIdAcrossInstances('', $post->objId);
					} else if (!empty($post->steps->createEntity->objectType)) {
						$objectType = $post->steps->createEntity->objectType;
					}
					break;

				case 'search':
					if (!empty($_REQUEST['parentObjectType'])) {
						$objectType = $_REQUEST['parentObjectType'];
					} else if (!empty($_REQUEST['searchType'])) {
						$objectType = $_REQUEST['searchType'];
					}
					break;

				case 'postComment':
					$objectType = $this->getObjectBPTypeByIdAcrossInstances('', $post->type_id);
					break;

				case 'updateState':
					$objectType = $this->getObjectBPTypeByIdAcrossInstances('', $post->objectId);
					break;

				case 'getMergeTagData':
					$objectType = $this->getObjectBPTypeByIdAcrossInstances('', $post->obj->objId);
					break;

				case 'createNewObject':
					$objectType = $this->getObjectBPTypeByIdAcrossInstances('', $post->objectData->related_object);
					break;

				case 'SingleEntityView':
					$objectType = !empty($post->object_bp_type) ? $post->object_bp_type : '';
					break;

			}

			return $objectType;

		}

		private function adjustTagsForPortal ($selectionArr) {

			// If in a portal instance, and searching tagged_with value
			// by current user id, switch to filtering shared_with by
			// id of
			if (
				$selectionArr['tagged_with']
				&& $GLOBALS['portal']
			) {

				if (in_array(intval($_COOKIE['uid']), $selectionArr['tagged_with'])) {

					$selectionArr['tagged_with'] = array_filter(
						$selectionArr['tagged_with']
						, function ($v) {
							return $v !== intval($_COOKIE['uid']);
						}
					);

				} elseif (in_array(intval($_COOKIE['p_uid']), $selectionArr['tagged_with'])) {

					$selectionArr['tagged_with'] = array_filter(
						$selectionArr['tagged_with']
						, function ($v) {
							return $v !== intval($_COOKIE['p_uid']);
						}
					);

				}

				if (count($selectionArr['tagged_with']) === 0) {
					unset($selectionArr['tagged_with']);
				}

				if ($selectionArr['_dont_force_portal_user_tag'] === true) {
					unset($selectionArr['_dont_force_portal_user_tag']);
				} else {
					$selectionArr['shared_with'] = [intval($_COOKIE['p_uid'])];
				}

			}

			if ($selectionArr['_dont_force_portal_user_tag'] === true) {
				unset($selectionArr['_dont_force_portal_user_tag']);
			}

			return $selectionArr;

		}

		private function applyBPDefaultProperties(
			$blueprint = null
			, $isCustomEntity = false
		) {

			if($blueprint == null) {
				return false;
			}

			foreach($this->defaultBPProperties as $key => $property) {

				if (empty($blueprint[$key])) {
					$blueprint[$key] = $property;
				}

			}

			if ($isCustomEntity) {

				foreach ($blueprint as $key => $property) {

					// Add additional properties for fields
					switch ($property['fieldType']) {

						case 'next-due-date':
							$blueprint[$key .'_refId'] = [
								'type' => 			'int'
								, 'name' => 		$property['name'] .' Next Due Date Reference'
								, 'immutable' => 	false
							];
							break;

						case 'date-rollup':
							$blueprint[$key .'_refId'] = [
								'type' => 			'int'
								, 'name' => 		$property['name'] .' Date Rollup'
								, 'immutable' => 	false
							];
							break;

						case 'state':
							$blueprint[$key .'_status'] = [
								'type' => 			'string'
								, 'name' => 		$property['name'] .' Status'
								, 'immutable' => 	false
							];
							$blueprint[$key .'_updated_on'] = [
								'type' => 			'date'
								, 'name' => 		$property['name'] .' Status'
								, 'immutable' => 	false
							];
							$blueprint[$key .'_weight'] = [
                                'type' =>           'float'
                                , 'name' =>         $property['name'] .' Status'
                                , 'immutable' =>    false

							];
							break;

						case 'timer':
							$blueprint[$key .'_est'] = [
								'type' => 			'int'
								, 'name' => 		$property['name'] .' Estimate'
								, 'immutable' => 	false
							];
                            $blueprint[$key .'_rate'] = [
								'type' => 			'int'
								, 'name' => 		$property['name'] .' Rate'
								, 'immutable' => 	false
							];
							$blueprint[$key .'_running'] = [
								'type' => 			'object'
								, 'name' => 		$property['name'] .' Running'
								, 'immutable' => 	false
							];
							break;

						case 'checklist':
							$blueprint[$key .'_other'] = [
								'type' => 			'string'
								, 'name' => 		$property['name'] .' Other Option'
								, 'immutable' => 	false
							];
							break;

						case 'user':
						case 'users':
						case 'companies':
						case 'contacts':
							$blueprint[$key .'_merge'] = [
								'type' => 			'string'
								, 'name' => 		$property['name'] .' Template'
								, 'immutable' => 	false
							];
							break;

						case 'date':
							$blueprint[$key .'_merge'] = [
								'type' => 			'string'
								, 'name' => 		$property['name'] .' Template'
								, 'immutable' => 	false
							];
							$blueprint[$key .'_done'] = [
								'type' => 			'int'
								, 'name' => 		$property['name'] .' Done Status'
								, 'immutable' => 	false
							];
							break;

						case 'set-table':
							$blueprint[$key .'_refId'] = [
								'type' => 			'int'
								, 'name' => 		$property['name'] .' Table Field'
								, 'immutable' => 	false
							];
							break;

						case 'reference-calculation':
							$blueprint[$key .'_refId'] = [
								'type' => 			'int'
								, 'name' => 		$property['name'] .' Reference Calculation Id'
								, 'immutable' => 	false
							];
							break;

						case 'reference':
							$blueprint[$key .'_refId'] = [
								'type' => 			'int'
								, 'name' => 		$property['name'] .' Reference Id'
								, 'immutable' => 	false
							];

							$blueprint[$key .'_lockedOn'] = [
								'type' => 			'date'
								, 'name' => 		$property['name'] .' Locked On'
								, 'immutable' => 	false
							];
							break;

					}

					switch ($property['fieldType']) {

						// Cue off of the 'multi' flag to switch between
						// 'objectId' and 'objectIds' property type.
						case 'user':
						case 'users':
						case 'edge':
						case 'attachment':
							if (
								is_array($property)
								&& is_array($property['options'])
								&& array_key_exists('multi', $property['options'])
							) {

								if ($property['options']['multi']) {
									$blueprint[$key]['type'] = 'objectIds';
								} else {
									$blueprint[$key]['type'] = 'objectId';
								}

							}
							break;

					}

				}

				$blueprint['status'] = [
					'type' => 			'string'
					, 'name' => 		'Status'
					, 'immutable' => 	false
				];
				$blueprint['time_logged'] = [
					'type' => 			'int'
					, 'name' => 		'Total Time Logged'
					, 'immutable' => 	false
				];
				$blueprint['time_estimate'] = [
					'type' => 			'int'
					, 'name' => 		'Total Time Estimated'
					, 'immutable' => 	false
				];
				$blueprint['date_created'] = [
					'type' => 			'date'
					, 'fieldType' => 	'date'
					, 'name' => 		'Date Created'
					, 'immutable' => 	false
				];
				$blueprint['created_by'] = [
					'type' => 			'objectId'
					, 'name' => 		'Created By'
					, 'immutable' => 	false
				];
				$blueprint['last_updated_by'] = [
					'type' => 			'objectId'
					, 'name' => 		'Last Updated By'
					, 'immutable' => 	false
				];
                $blueprint['rca_last_updated_comments'] = [
					'type' => 			'date'
					, 'fieldType' => 	'date'
					, 'name' => 		'RCA Comments Last Updated'
					, 'immutable' => 	false
				];
                $blueprint['rca_last_updated_uploads'] = [
					'type' => 			'date'
					, 'fieldType' => 	'date'
					, 'name' => 		'RCA Uploads Last Updated'
					, 'immutable' => 	false
				];
			}

			return $blueprint;

		}

		private function applyInheritedProperties($entityType) {

			$viewOpts = array(
				'_sectionName',
				'_message',
				'_description',
				'_breakAfter'
			);

			if ($entityType && $entityType['_class']) {

				$inheritsFrom = $this->where('entity_type', array('id' => $entityType['_class']))[0];

				if ($inheritsFrom && is_array($inheritsFrom['blueprint'])) {

					foreach ($inheritsFrom['blueprint'] as $key => $field) {

						// Don't carry over fields that have been archived on
						// the parent set/class
						if ($field['is_archived'] !== true) {

							// Don't carry over fields that have been archived/hidden
							// on the child set/subset
							if (
								!(
									array_key_exists($key, $entityType['blueprint'])
									&& is_array($entityType['blueprint'][$key])
									&& $entityType['blueprint'][$key]['is_archived'] === true
								)
							) {

								// Set a temp value
								$tmpField = $entityType['blueprint'][$key];

								// Carry over field
								$entityType['blueprint'][$key] = $field;
								$entityType['blueprint'][$key]['_inheritedFrom'] = $entityType['id'];

								// Keep the view options on the subset
								if (!empty($tmpField)) {

									$entityType['blueprint'][$key]['index'] = $tmpField['index'];
									if (!empty($tmpField['options'])) {

										foreach($tmpField['options'] as $optKey => $optVal) {

											if (in_array($optKey, $viewOpts)) {
												$entityType['blueprint'][$key]['options'][$optKey] = $optVal;
											}

										}

									}

								}

							}

						}

					}

				}

			}

			return $entityType;

		}

		function applyWorkflowStates($bp) {

			// Get workflows
			$workflowIds = array();

			foreach ($bp['blueprint'] as $key => $property) {

				if (
					$property['fieldType'] === 'state'
					&& intval($property['workflow'])
					&& !$property['is_archived']
				) {

					array_push($workflowIds, intval($property['workflow']));

				}

			}

			$workflows = $this->getByIdAcrossInstances('entity_workflow', $workflowIds, array('id' => true, 'states' => true), false, $bp['instance']);

			foreach ($bp['blueprint'] as $key => $property) {

				if (
					$property['fieldType'] === 'state'
					&& !$property['is_archived']
				) {

					$workflow = __::find($workflows, function($workflow) use ($property) {
						return $workflow['id'] == intval($property['workflow']);
					});

					if (!empty($workflow)) {

						$bp['blueprint'][$key]['workflow'] = $workflow;

					}

				}

			}

			return $bp;

		}

		public function getSelectionFromBlueprint($blueprint, $fullSelect) {

			$select = array(
				'type' => true,
				'date_created' => true,
				'object_uid' => true,
				'parent' => array(
					'name' => true,
					'group_type' => true
				)
			);

			foreach($blueprint as $key => $property) {

				if (!$property['is_archived']) {

					$availableSelectFields = array(
						'edge' => array(
							'select' => array(
								'name' => true,
								'object_uid' => true,
								'status' => true
							)
						),
						'image' => array(
							'select' => array(
								'file_name' => true,
								'file_type' => true,
								'loc' => true,
								'name' => true,
								'oid' => true,
								'oid_type' => true
							)
						),
						'locations' => array(
							'select' => array(
								'fname' => true,
								'lname' => true,
								'name' => true
							)
						),
						'table' => array(
							'select' => array(
								'name' => true,
								'object_uid' => true,
								'status' => true
							)
						),
						'title' => array(
							'select' => array(
								'object_uid' => true,
							)
						),
						'users' => array(
							'select' => array(
								'color' => true,
								'fname' => true,
								'lname' => true,
								'profile_image' => true
							)
						),
						'contacts' => array(
							'select' => array(
								'fname' => true,
								'lname' => true,
								'company' => [
									'name' => true
								]
							)
						),
						'companies' => array(
							'select' => array(
								'name' => true
							)
						),
						'user' => array(
							'select' => array(
								'color' => true,
								'fname' => true,
								'lname' => true,
								'profile_image' => true
							)
						),
						'attachment' => array(
							'select' => array(
								'document_type' => true,
								'file_upload' => true,
								'name' => true,
								'share_link' => true
							)
						)
					);

					$typeDef = array_key_exists($property['fieldType'], $availableSelectFields) ? $availableSelectFields[$property['fieldType']] : array();

					if (empty($typeDef)) {

						$select[$key] = true;
						switch ($property['fieldType']) {

							case 'state':
								$select[$key .'_updated_on'] = true;
								break;

							case 'timer':
								$select[$key .'_est'] = true;
								$select[$key .'_rate'] = true;
								$select[$key .'_running'] = true;
								break;

						}

					} else {

						$select[$key] = $typeDef['select'];

						switch ($property['fieldType']) {

							case 'edge':

								if (!empty($fullSelect)) {

									// Additional fields to include, based on config options
									if (
										!empty($property['options'])
										&& !empty($property['options']['objectType'])
										&& !empty($property['options']['includeInSelect'])
									) {

										$childType = $this->where('entity_type', array('bp_name' => substr($property['options']['objectType'], 1)))[0];

										if (!empty($childType)) {

											foreach($property['options']['includeInSelect'] as $innerProp) {

												// !TODO: don't just set to true, check based
												// on the field type
												if (
													!empty($childType['blueprint'][$innerProp])
													&& !$childType['blueprint'][$innerProp]['is_archived']
												) {

													if (!$select[$key][$innerProp]) {

														$innerTypeDef = array_key_exists($childType['blueprint'][$innerProp]['fieldType'], $availableSelectFields) ? $availableSelectFields[$childType['blueprint'][$innerProp]['fieldType']] : array();

														if (
															!empty($innerTypeDef)
															&& !empty($innerTypeDef['select'])
														) {

															$select[$key][$innerProp] = $innerTypeDef['select'];

														} else {

															$select[$key][$innerProp] = true;

														}

													}

												}

												if (!empty($childType['blueprint'][$innerProp] )
													&& !empty($childType['blueprint'][$innerProp]['fieldType'])
													&& $childType['blueprint'][$innerProp]['fieldType'] == 'timer'
												) {

													$select[$key][$innerProp + '_est'] = true;
													$select[$key][$innerProp + '_rate'] = true;
													$select[$key][$innerProp + '_running'] = true;

												}

											}

										}

									}
								}
								break;

						}

					}

				}

			}

			return $select;

		}

		private function getFuzzySearchDistanceSql ($key, $searchVal, $addCost = 1, $rmCost = 1, $subCost = 1) {

			$selectString = '';

			// Fuzzy search, and allow for words to be out of order
			$split = explode(' ', $searchVal);
			foreach ($split as $i => $searchWord) {

				$selectString .= "levenshtein('". trim($searchWord) ."', COALESCE(object_data->>'$key', ''), $addCost, $rmCost, $subCost)";
				if ($i + 1 < count($split)) {
					$selectString .= ' + ';
				}

			}

			return $selectString;

		}

		private function getContainsString($val, $key){

			$selectString = '';

			if (is_array($val['value']) and $val['value']['type'] === 'or') {

				$selectString .= '(';
				foreach($val['value']['values'] as $j => $value){
					if($j > 0){
						$selectString .= ' OR ';
					}

					if(is_numeric($value) and ($key != 'zip' or $key != 'tracking_number')){
						$selectString .= "(object_data->'$key')::jsonb @> '[" .$value."]'";
					}else{
						$selectString .= "(object_data->'$key')::jsonb @> '[\"" . $value ."\"]'";
					}

				}

				$selectString .= ')';

			} else if (is_array($val['values']) and $val['type'] === 'contains') {

				if ($val['options']['multi']) {
					$selectString .= "(object_data->>'$key')::jsonb @> '[". trim(implode(',', $val['values'])) ."]'::jsonb";
				} else {
					$selectString .= "(object_data->>'$key')::jsonb <@ '[". trim(implode(',', $val['values'])) ."]'::jsonb";
				}

			} else if ($val['value'] === intval($val['value']) and intval($val['value']) > 0 and $key != 'zip'){

				$selectString .= "(object_data->'$key')::jsonb @> '[" .$val['value']."]'";

			} else {

				switch ($key) {

					case 'fname':
					case 'lname':
						if (strpos($val['value'], ' ')) {

							$explode = explode(' ', trim($val['value']));
							$selectString .= "(object_data->>'$key' ILIKE '%". $explode[0] ."%'";
							$selectString .= " OR object_data->>'$key' ILIKE '%". $explode[1] ."%')";

						} else {

							$selectString .= "object_data->>'$key' ILIKE '%". trim($val['value']) ."%'";

						}
						break;

					default:
						// if ($val['fuzzySearch'] === true && $key === 'name') {

						// 	$selectString .= '(';
						// 	$selectString .= $this->getFuzzySearchDistanceSql($key, $val['value'], 0, 1, 1);
						// 	$selectString .= ') < '. (intval(strlen($val['value'])/12) + 1);

						// } else {

							// Escape single quotes for the search term
							$searchTxt = str_replace("'", "''", $val['value']);
							$explode = explode(' ', trim($searchTxt));

							if (count($explode) > 1) {

								$selectString .= '(';
								foreach ($explode as $i => $searchPhrase) {

									if ($i > 0) {
										$selectString .= ' AND ';
									}
									$selectString .= "object_data->>'$key' ILIKE '%". trim(str_replace(' ', '%', $searchPhrase)) ."%'";

								}
								$selectString .= ')';

							} else {

								$selectString .= "object_data->>'$key' ILIKE '%". trim(str_replace(' ', '%', $searchTxt)) ."%'";

							}

						// }
						break;

				}

			}
			return $selectString;

		}

		private function createObjectSequencesEntry(){

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			$statement = $this->writeDb->prepare(
				"INSERT INTO object_sequences (instance, date_created) VALUES ('". $this->instance ."', DEFAULT) RETURNING *;"
			);

			if($statement->execute()){

				$info = $statement->fetchAll()[0];
				$info['sequences'] = json_decode($info['sequences'], true);
				return $info;

			}else{

				var_dump($statement->errorInfo());

			}

		}

		// this version contains the instance's encryption key and should never be sent from server
		private function currentInstance($instance){

			if($this->instanceObj == null){

				$this->instanceObj = $instanceObj = $this->where('instances', ['instance' => $instance])[0];

			}

			return $this->instanceObj;

		}

		private function currentUser($uid){

			if($this->userObj == null){

				$instanceObj = $this->getById('users', $uid);

			}else{

				return $this->instanceObj;

			}

		}

		private function getArchivePhrase($selectionArr){
			// Convert both boolean and numeric values to true/false
			$isArchived = filter_var($selectionArr['archive'], FILTER_VALIDATE_BOOLEAN);
			return "is_deleted = " . ($isArchived ? "true" : "false");

		}

		private function getPropertyConditionPhrase($key, $val) {

			$selectString = '';

			// Allow for nested 'or' checks
			if (is_array($val) && is_array($val['or'])) {
				$selectString .= '(';
			}
			if (is_array($val) && is_array($val['and'])) {
				$selectString .= '(';
			}

			if($key === 'tagged_with'){

				if (is_array($val) and $val['type'] == 'any') {
					$selectString .= "tagged_with && ARRAY[". trim(implode(',', $val['values'])) ."]::integer[]";
				}elseif(is_array($val)){
					$selectString .= "tagged_with @> ARRAY[". trim(implode(',', $val)) ."]";
				}elseif(is_int($val)){
					$selectString .= "tagged_with @> ARRAY[". trim($val) ."]";
				}

			}elseif($key === 'shared_with'){

				if (is_array($val) and $val['type'] == 'any') {
					$selectString .= "shared_with && ARRAY[". trim(implode(',', $val['values'])) ."]::integer[]";
				}elseif(is_array($val)){
					$selectString .= "shared_with @> ARRAY[". trim(implode(',', $val)) ."]";
				}elseif(is_int($val)){
					$selectString .= "shared_with @> ARRAY[". trim($val) ."]";
				}

			}elseif($key === 'read'){

				if (is_array($val) and $val['type'] == 'any') {
					$selectString .= "read_obj && ARRAY[". trim(implode(',', $val)) ."]::integer[]";
				}elseif(is_array($val)){
					$selectString .= "read_obj @> ARRAY[". trim(implode(',', $val)) ."]";
				}elseif(is_int($val)){
					$selectString .= "read_obj @> ARRAY[". trim($val) ."]";
				}

			}elseif($key === 'write'){

				if (is_array($val) and $val['type'] == 'any') {
					$selectString .= "write_obj && ARRAY[". trim(implode(',', $val)) ."]::integer[]";
				}elseif(is_array($val)){
					$selectString .= "write_obj @> ARRAY[". trim(implode(',', $val)) ."]";
				}elseif(is_int($val)){
					$selectString .= "write_obj @> ARRAY[". trim($val) ."]";
				}

			}elseif($key === 'archive' and $val){

				// do nothing

			}else{

				if (is_string($val)) {

					$selectString .= 'object_data @>\'{"'.$key.'":"'. trim($val) .'"}\'';
					//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

				} else if(is_array($val)) {

					if ($val['type'] === 'contains') {

						$selectString .= $this->getContainsString($val, $key);

					} else if ($val['type'] === 'intersect') {

						$selectString .= "REPLACE(REPLACE((object_data->>'$key'), '[', '{'), ']', '}')::int[] && ARRAY[". implode(',', $val['value']) ."]";

					} else if ($val['type'] === 'contains-id') {

						$selectString .= "(object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";

					} else if ($val['type'] === 'between') {

						if ($this->validateDateString($val['start'], 'Y-m-d H:i:s')) {

							$startTime = $val['start'];
							$endTime = $val['end'];

						} else {

							$startTime = $val['start'];
							$endTime = $val['end'];

							if ($_COOKIE && $_COOKIE['tz_off'] && $val['includeOffset'] !== false) {

								$startTime = $startTime + (intval($_COOKIE['tz_off'])*60);
								$endTime = $endTime + (intval($_COOKIE['tz_off'])*60);

							}

							$startTime = date('Y-m-d H:i:s', $startTime);
							$endTime = date('Y-m-d H:i:s', $endTime);

						}

						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";

					} else if ($val['type'] === 'before') {

						if(is_numeric($val['date'])){
							$queryDate = date('Y-m-d H:i:s', $val['date']);
						}elseif($this->validateDateString($val['date'], 'Y-m-d H:i:s')){
							$queryDate = $val['date'];
						}

						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') < to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					} else if ($val['type'] === 'after') {

						if (is_numeric($val['date'])) {

							$queryDate = new DateTime();
							$queryDate->setTimestamp($val['date']);
							$queryDate->setTimezone(new DateTimezone('Africa/Abidjan'));
							$queryDate = $queryDate->format('Y-m-d H:i:s');

						} else if ($this->validateDateString($val['date'], 'Y-m-d H:i:s')) {
							$queryDate = $val['date'];
						}

						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') > to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					} else if ($val['type'] === 'on_or_before') {

						if(is_numeric($val['date'])){
							$queryDate = date('Y-m-d H:i:s', $val['date']);
						}elseif($this->validateDateString($val['date'], 'Y-m-d H:i:s')){
							$queryDate = $val['date'];
						}

						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') <= to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					} else if ($val['type'] === 'on_or_after') {

						if (is_numeric($val['date'])) {

							$queryDate = new DateTime();
							$queryDate->setTimestamp($val['date']);
							$queryDate->setTimezone(new DateTimezone('Africa/Abidjan'));
							$queryDate = $queryDate->format('Y-m-d H:i:s');

						} else if ($this->validateDateString($val['date'], 'Y-m-d H:i:s')) {
							$queryDate = $val['date'];
						}

						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') >= to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					} else if ($val['type'] === 'on_same_day') {

						if (is_numeric($val['date'])) {

							$queryDate = new DateTime();
							$queryDate->setTimestamp($val['date']);
							$queryDate->setTimezone(new DateTimezone('Africa/Abidjan'));
							$queryDate = $queryDate->format('Y-m-d H:i:s');

						} else if ($this->validateDateString($val['date'], 'Y-m-d H:i:s')) {
							$queryDate = $val['date'];
						}

						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS')::date = to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')::date";

					} else if ($val['type'] === 'or') {

						if ( is_array($val['fields']) ) {

							// !BACK HERE
							$selectString .= '(';
							foreach($val['fields'] as $j => $value){

								if($j > 0){
									$selectString .= ' OR ';
								}

								if ($value === 'tagged_with') {

									$selectString .= "tagged_with @> ARRAY[". trim($val['value']) ."]";

								} elseif (is_integer($val['value'])) {

// 										$selectString .= "(object_data->>'$value')::int = ". trim($val['value']) ."";
									$selectString .= 'object_data @>\'{"'.$key.'":'. trim($val['value']) .'}\'';
// 										$selectString .= "(object_data->>'$key')::int = ". trim($val['value']) ."";

								} elseif ( is_array($value) ) {

									if ( $value['multi'] == true ) {

										$selectString .= "REPLACE(REPLACE((object_data->>'". $value['key'] ."'), '[', '{'), ']', '}')::int[] && ('{". implode(',' , $value['value']). "}'::int[])";

									} else {

										$selectString .= "(object_data->>'" . $value['key'] . "')::int = ANY ('{". implode(',' , $value['value']). "}'::int[])";

									}

								} else {

									$selectString .= $this->getContainsString(
										$val,
										$value
									);

								}


							}

							$selectString .= ')';

						}else{

							$selectString .= '(';
							foreach($val['values'] as $j => $value){
								if($j > 0){
									$selectString .= ' OR ';
								}

								if(is_numeric($value)){
									$selectString .= "(object_data->>'$key')::int = ". trim($value) ."";
								}else{
									$selectString .= "object_data->>'$key' = '". trim($value) ."'";
								}

							}

							$selectString .= ')';

						}

					} else if ($val['type'] === 'less_than') {

						// !TODO: cast as int and compare
						$selectString .= '(object_data->>\''. $key .'\')::REAL < '. trim($val['value']);
						$selectString .= " OR (object_data->'".$key."' IS NULL OR (object_data->>'$key')::int = 0" . ' AND NOT object_data @>\'{"'.$key.'":"'. trim($val['not_equal']) .'"}\')';

					} else if ($val['type'] === 'greater_than') {

						$selectString .= '(object_data->>\''. $key .'\')::REAL > '. trim($val['value']);

					} else if ($val['type'] === 'less_than_or_equal_to') {

						// !TODO: cast as int and compare
						$selectString .= '(object_data->>\''. $key .'\')::REAL <= '. trim($val['value']);

					} else if ($val['type'] === 'greater_than_or_equal_to') {

						$selectString .= '(object_data->>\''. $key .'\')::REAL >= '. trim($val['value']);

					} else if ($val['type'] === 'not_equal') {

						if (is_string($val['value'])) {

							$selectString .= 'NOT object_data @>\'{"'.$key.'":"'. trim($val['value']) .'"}\'';

						} elseif (is_bool($val['value'])) {

							$boolStringVal = ($val['value']) ? 'true' : 'false';
							$selectString .= 'NOT object_data @>\'{"'.$key.'":'. $boolStringVal .'}\'';

						} else {

							$selectString .= 'NOT object_data @>\'{"'.$key.'":'. trim($val['value']) .'}\'';

						}

					} else if ($val['type'] === 'not_set') {

						if (array_key_exists('not_equal', $val)) {

							if (is_string($val['value'])) {

								$selectString .= "(object_data->'".$key."' IS NULL OR (object_data->>'$key')::int = 0" . ' AND NOT object_data @>\'{"'.$key.'":"'. trim($val['not_equal']) .'"}\')';

							} else {

								$selectString .= "(object_data->'".$key."' IS NULL OR (object_data->>'$key')::int = 0" . ' AND NOT object_data @>\'{"'.$key.'":'. trim($val['not_equal']) .'}\')';

							}

						} elseif (array_key_exists('or', $val)) {

							$selectString .= "(object_data->'".$key."' IS NULL OR (object_data->>'$key')::int = 0" . ' OR object_data @>\'{"'.$key.'":'. trim($val['or']) .'}\')';

						} else {

							$selectString .= "(object_data->'".$key."' IS NULL OR
												CASE jsonb_typeof(object_data->'$key')
													WHEN 'number' THEN (object_data->>'$key')::int = 0
													ELSE FALSE
												END)";

						}

					} else if ($val['type'] === 'is_set') {

						$selectString .= "((object_data->'".$key."' IS NOT NULL AND object_data->'".$key."' != '[]' AND object_data->'".$key."' != '{}')
											OR
											(CASE jsonb_typeof(object_data->'$key')
												WHEN 'number' THEN (object_data->>'$key')::int > 0
												ELSE FALSE
											END
											AND object_data->'".$key."' != '[]' AND object_data->'".$key."' != '{}')
										)";

					} else if ($val['type'] === 'overlap') {

						$range_start = $val['range']['start'];
						$range_end   = $val['range']['end'];
						$endField    = $val['endField'];

						$selectString .= "(to_timestamp(object_data->>'". $key ."', 'YYYY-MM-DD HH24:MI:SS')::DATE, to_timestamp(object_data->>'". $endField ."', 'YYYY-MM-DD HH24:MI:SS')::DATE) OVERLAPS ('". date('Y-m-d H:i:s', $range_start) ."'::DATE, '". date('Y-m-d H:i:s', $range_end) ."'::DATE)";

					} else {

						if (is_array($val)) {
							$selectString .= "REPLACE(REPLACE((object_data->>'$key'), '[', '{'), ']', '}')::int[] = ARRAY[". implode(',', $val) ."]";
						}

					}

				} else if (is_bool($val)) {

					$boolStringVal = ($val) ? 'true' : 'false';
					$selectString .= 'object_data @>\'{"'.$key.'":'. $boolStringVal .'}\'';

				} else {

					$selectString .= 'object_data @>\'{"'.$key.'":'. trim($val) .'}\'';
					//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

				}

			}

			// Allow for nested 'or' checks
			if (is_array($val) && is_array($val['or'])) {

				$selectString .= ' OR '.  $this->getPropertyConditionPhrase(
					array_key_first($val['or'])
					, $val['or'][array_key_first($val['or'])]
				);
				$selectString .= ')';

			}
			// Allow for nested 'and' checks
			if (is_array($val) && is_array($val['and'])) {

				$selectString .= ' AND '.  $this->getPropertyConditionPhrase(
					array_key_first($val['and'])
					, $val['and'][array_key_first($val['and'])]
				);
				$selectString .= ')';

			}

			return $selectString;

		}

		private function getNextObjectId($objectType){

			$statement = $this->db->prepare(
				"SELECT * FROM object_sequences WHERE instance = '". $this->instance ."';"
			);

			if($statement->execute()){

				$info = $statement->fetchAll()[0];

				// if not yet created, create it
				if($info == null){

					$info = $this->createObjectSequencesEntry();

				}

				// if sequences if null, set it as an object
				if($info['sequences'] == null){
					$info['sequences'] = array();
				}else{
					$info['sequences'] = json_decode($info['sequences'], true);
				}

				// Switch subsets to the base object type, since they should share
				// this counter w/all objs across their set.
				if (
					substr($objectType, 0, 1) === '#'
					&& strpos($objectType, '.') !== false
				) {

					$objectType = explode('.', $objectType)[0];

				}

				// if no entry for object type, create it and start it at 1
				if($info['sequences'][$objectType] == null){

					$info['sequences'][$objectType] = array(
						'lastId' => 1
					);

				}else{

					$info['sequences'][$objectType]['lastId']++;

				}

				if($this->writeDb == null){
					$this->writeDb = new PDO(
						"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
						port=". $GLOBALS['BENTO_DB_PORT'] .";
						dbname=". $GLOBALS['BENTO_DB_NAME'] .";
						sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
						user=". $GLOBALS['BENTO_DB_USER'] .";
						password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
					);
				}

				$statement = $this->writeDb->prepare(
					"UPDATE object_sequences SET sequences = '". trim($this->db->quote(json_encode($info['sequences'], JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;"
				);

				if($statement->execute()){

					$info = $statement->fetchAll()[0];
// var_dump($statement->fetchAll());
					$sequence = json_decode($info['sequences'], true);
					return $sequence[$objectType]['lastId'];

				}else{

					var_dump($statement->errorInfo());

				}

				var_dump($info);



			}else{

				var_dump($statement->errorInfo());

			}

		}

		private function getSelectClause($selectObj, $blueprint = null, $join = false, $selectionArr = []){

			$selectClause = 'SELECT id, instance, object_type, object_data, date_created, is_deleted, tagged_with, shared_with, read_obj, write_obj, notify';
			$joinClause = '';

			if (is_numeric($selectObj)) {

				$selectClause = 'SELECT id, instance, object_type, object_data, date_created, is_deleted, tagged_with, shared_with, read_obj, write_obj, notify';

			} else if (is_array($selectObj)) {

				if (count($selectObj) === 0 || count($selectObj) > 1) {

					// Turned off, since its unecessary for production, but useful when
					// tuning fuzzy matching
					// if (
					// 	!empty($selectionArr['search'])
					// 	&& !empty($selectionArr['search']['value'])
					// ) {
					// 	$selectClause = 'SELECT id, instance, object_type, object_data, date_created, is_deleted, tagged_with, shared_with, read_obj, write_obj, notify, '. "levenshtein('". $selectionArr['search']['value'] ."', COALESCE(object_data->>'name', ''), 1, 4, 4) AS DISTANCE";
					// } else {
						$selectClause = 'SELECT id, instance, object_type, object_data, date_created, is_deleted, tagged_with, shared_with, read_obj, write_obj, notify, sort_index';
					// }

				} else {

					$selectClause = 'SELECT id, instance, object_type, date_created, is_deleted, tagged_with, shared_with, read_obj, write_obj, notify, sort_index, ';

					$i = 0;
					foreach ($selectObj as $key => $val) {

						if (
							$key === 'notify'
							|| $key === 'tagged_with'
							|| $key === 'read_obj'
							|| $key === 'write_obj'
							|| $key === 'shared_with'
						) {

							if ($i > 0) {
								$selectClause .= ', ';
							}

							$selectClause .= $key ." AS $key";
							$i++;

						// Turned off, since its unecessary for production, but useful when
						// tuning fuzzy matching
						// } elseif ($key === 'DISTANCE') {

						// 	if (
						// 		!empty($selectionArr['search'])
						// 		&& !empty($selectionArr['search']['value'])
						// 	) {
						// 		$selectClause .= "levenshtein('". $selectionArr['search']['value'] ."', COALESCE(object_data->>'name', ''), 0, 0, 1) AS DISTANCE";
						// 		$i++;
						// 	}

						} elseif (substr($key, 0, 1) !== '#') {

							if ($i > 0) {
								$selectClause .= ', ';
							}

							$selectClause .= "object_data->>'". $key ."' AS $key";
							$i++;

						}

					}

				}

			}

			if ($join && $blueprint) {

				return [
					'select' => $selectClause
					, 'join' => $joinClause
				];

			} else {

				return $selectClause;

			}

		}

		private function getWhereClause($selectionArr) {

			// Set variables
			$operator = 'and';
			$instance = '';

			// Check for filter obj reference, and swap if necessary.
			if (array_key_exists('_data_filter', $selectionArr)) {

				// Get the filter obj
				//!1552: Allow this to grab filters cross-instance
				// . If the filter is from a different instance, the query should call
				//   data in that instance instead.
				$filterObj = $this->getByIdAcrossInstances('data_filter', $selectionArr['_data_filter']);

				// Get the context object
				$contextObj = $this->getByIdAcrossInstances('', $selectionArr['_data_filter_context']);

				// Get the blueprint
				$blueprint = $this->getBlueprint($filterObj['object_type']);

				// Check if data filter is shared
				if (!empty($filterObj['shared_with_instances'])) {
					$instance = $filterObj['instance'];
				}

				// Check for the operator (and/or)
				if (
					is_array(($filterObj['where']))
					&& (
						$filterObj['where']['operator'] === 'and'
						|| $filterObj['where']['operator'] === 'or'
					)
				) {
					$operator = $filterObj['where']['operator'];
				}

				// Parse the filter obj into the selection array
				if (
					is_array($filterObj)
					&& is_array($filterObj['where'])
					&& is_array($filterObj['where']['filters'])
				) {

					foreach ($filterObj['where']['filters'] as $i => $filter) {

						$compareClause = [];

						// Pull the value from context obj, if it is set
						if ($filter['isReference']) {

							if (array_key_exists($filter['value'], $contextObj)) {

								$filter['value'] = $contextObj[$filter['value']];
								if (
									is_string($filter['field'])
									&& is_array($blueprint[$filter['field']])
									&& is_string($blueprint[$filter['field']]['type'])
									&& $blueprint[$filter['field']]['type'] == 'date'
								) {
									$filter['date'] = $filter['value'];
								}

							} else {

								break;

							}

						}

						// Pull the parent value
						if (
							$filter['field'] === 'parent'
							&& $filter['value'] === 'current_record'
						) {
							$filter['value'] = $contextObj['id'];
						}

						// Pull the moment date value
						if ($blueprint[$filter['field']]['type'] == 'date') {

							switch ($filter['value']) {

								case 'today':
									$filter['date'] = strtotime('today');
									break;

								case 'tomorrow':
									$filter['date'] = strtotime('tomorrow');
									break;

								case 'yesterday':
									$filter['date'] = strtotime('yesterday');
									break;

								case 'one_week_ago':
									$filter['date'] = strtotime('-1 week');
									break;

								case 'one_month_ago':
									$filter['date'] = strtotime('-1 month');
									break;

								case 'one_quarter_ago':
									$currentMonth = date('m');
									$currentYear = date('Y');
									$previousYear = date('last year');
									$nextYear = date('next year');
									if( $currentMonth >= 1 && $currentMonth <= 3 ) {
										$filter['date'] = strtotime('1-October-'.$previousYear);
									} else if( $currentMonth >= 4 && $currentMonth <= 6 ) {
										$filter['date'] = strtotime('1-January-'.$currentYear);
									} else  if( $currentMonth >= 7 && $currentMonth <= 9 ) {
										$filter['date'] = strtotime('1-April-'.$currentYear);
									} else  if( $currentMonth >= 10 && $currentMonth <= 12 ) {
										$filter['date'] = strtotime('1-July-'.$currentYear);
									}
									break;

								case 'one_year_ago':
									$filter['date'] = strtotime('-1 year');
									break;

								case 'one_week_from_now':
									$filter['date'] = strtotime('+1 week');
									break;

								case 'one_month_from_now':
									$filter['date'] = strtotime('+1 month');
									break;

								case 'one_quarter_from_now':
									$currentMonth = date('m');
									$currentYear = date('Y');
									$previousYear = date('last year');
									$nextYear = date('next year');
									if( $currentMonth >= 1 && $currentMonth <= 3 ) {
										$filter['date'] = strtotime('1-April-'.$currentYear);
									} else if( $currentMonth >= 4 && $currentMonth <= 6 ) {
										$filter['date'] = strtotime('1-July-'.$currentYear);
									} else  if( $currentMonth >= 7 && $currentMonth <= 9 ) {
										$filter['date'] = strtotime('1-October-'.$currentYear);
									} else  if( $currentMonth >= 10 && $currentMonth <= 12 ) {
										$filter['date'] = strtotime('1-January-'.$nextYear);
									}
									break;

								case 'one_year_from_now':
									$filter['date'] = strtotime('+1 year');
									break;

								case 'exact_date':
									$filter['date'] = date('Y-m-d H:i:s', strtotime($filter['date']));
									break;

							}

						} else if ($filter['field'] == 'status') {

							if ($filter['value'] == 'open') {

								$compareClause = [
									'type' => 'not_equal',
									'value' => 'done'
								];

							}

						}

						if (empty($compareClause)) {

							switch ($filter['type']) {

								case 'contains':

									if ($filter['field'] == 'tagged_with') {

										$compareClause = $filter['value'];

									} else if (!empty($blueprint[$filter['field']]['fieldType'])) {

										switch ($blueprint[$filter['field']]['fieldType']) {

											case 'user':
											case 'users':
												if (!is_array($filter['value'])) {
													$values = explode(',', $filter['value']);
													$filter['value'] = array();
													foreach($values as $value) {
														array_push($filter['value'], $value);
													}
												}

												$compareClause = [
													'type' => $filter['type'],
													'values' => $filter['value'],
													'options' => $blueprint[$filter['field']]['options']
												];
												break;

											case 'plain-text':
											case 'title':
											default:
												$compareClause = [
													'type' => $filter['type'],
													'value' => $filter['value']
												];
												break;

										}

									} else {

										$compareClause = [
											'type' => $filter['type'],
											'value' => $filter['value']
										];

									}
									break;

								case 'greater_than':
								case 'less_than':
								case 'greater_than_or_equal_to':
								case 'less_than_or_equal_to':
									$compareClause = [
										'type' => $filter['type'],
										'value' => $filter['value'],
									];
									break;

								case 'on_same_day':
								case 'after':
								case 'before':
								case 'on_or_after':
								case 'on_or_before':
									$compareClause = [
										'type' => $filter['type'],
										'value' => $filter['value'],
										'date' => $filter['date']
									];
									break;

								case 'equals':
								default:
									$compareClause = $filter['value'];
									break;

							}

						}

						if (!empty($compareClause)) {

							// Add a new spot for the filter
							$selectionArr[$filter['field'] . $i] = [
								'_property' => 			$filter['field']
								, '_compareClause' => 	$compareClause
							];

						}

					}

				}
// $this->debug = true;
				// Remove the filter from selection arr
				unset($selectionArr['_data_filter']);
				unset($selectionArr['_data_filter_context']);

			}

			$selectionArr = $this->adjustTagsForPortal($selectionArr);
			$selectString = '';

			$i = 0;
			foreach($selectionArr as $key => $val){

				if ($i > 0) {

					switch ($operator) {

						case 'or':
							$selectString .= " OR ";
							break;

						default:
							$selectString .= " AND ";
							break;

					}
				}

				if (
					is_array($val)
					&& array_key_exists('_property', $val)
				) {

					$selectString .= $this->getPropertyConditionPhrase(
						$val['_property']
						, $val['_compareClause']
					);

				} else {
					$selectString .= $this->getPropertyConditionPhrase($key, $val);
				}

				$i++;

			}

			if (empty($selectString)) {
				$selectString = 'true = true';
			}

			return array(
				'selectString' => '('. $selectString .')',
				'instance' => $instance
			);

		}

		private function getObjectTypePhrase($objectType){

			$objectTypePhrase = '';

			if ($objectType === '#Action Items') {
				return 'is_task = true';
			}

			if(is_string($objectType)){

				if ($objectType === '#.') {

					$objectTypePhrase = "object_type LIKE '#%'";

				} elseif (substr($objectType, 0, 1) === '#') {

					$objectTypePhrase = "object_type LIKE '". $objectType ."%'";

				} elseif ($objectType === 'any') {

					$objectTypePhrase = "object_type IS NOT NULL";

				} else {

					$objectTypePhrase = "object_type = '$objectType'";

				}

			}elseif(is_array($objectType)){

				$objectTypePhrase = "object_type LIKE ANY(ARRAY[";

				foreach($objectType as $i => $bpName){

					if($i > 0){
						$objectTypePhrase .= ", ";
					}

					if ($bpName === '#.') {

						$objectTypePhrase .= "'#%'";

					} elseif (substr($bpName, 0, 1) === '#') {

						$objectTypePhrase .= "'". $bpName ."%'";

					} else {

						$objectTypePhrase .= "'$bpName'";

					}

				}

				$objectTypePhrase .= "])";

			}

			return $objectTypePhrase;

		}

		private function getWherePhrase($queryObj){

		}

		private function getIntArrUpdatePhrase ($objectData) {

			$objectData = $this->adjustTagsForPortal($objectData);

			$tagText = "";
			if (is_array($objectData['tagged_with'])) {

				// !also update write_obj column for now (until permissions ui is built into tag comp)
				if (count($objectData['tagged_with']) == 0) {

					$tagText = ", tagged_with = '{}' ";

					if (!$objectData['write']) {
						$tagText .= ", write_obj = '{}' ";
					}

				} else {

					$tagText = ", tagged_with = '{". implode(',', $this->parseObjectTaggedWith($objectData['tagged_with'])) ."}'";

					if (!$objectData['write']) {
						$tagText .= ", write_obj = '{". implode(',', $this->parseObjectTaggedWith($objectData['tagged_with'])) ."}' ";
					}

				}

			}

			if (is_array($objectData['shared_with'])) {

				// !also update write_obj column for now (until permissions ui is built into tag comp)
				if (count($objectData['shared_with']) == 0) {

					$tagText .= ", shared_with = '{}' ";

				} else {

					$tagText .= ", shared_with = '{". implode(',', $this->parseObjectTaggedWith($objectData['shared_with'])) ."}'";

				}

			}

			$readPermissionText = "";
			if (is_array($objectData['read'])) {

				if (count($objectData['read']) == 0) {
					$readPermissionText = ", read_obj = '{}' ";
				} else {
					$readPermissionText = ", read_obj = '{". implode(',', $this->parseObjectTaggedWith($objectData['read'])) ."}' ";
				}

			}

			$writePermissionText = "";
			if (
				is_array($objectData['write']
				&& !is_array($objectData['tagged_with']))
			){

				if( count($objectData['write']) == 0 ){
					$writePermissionText = ", write_obj = '{}' ";
				}else{
					$writePermissionText = ", write_obj = '{". implode(',', $this->parseObjectTaggedWith($objectData['write'])) ."}' ";
				}

			}

			$notifyText = "";
			if (is_array($objectData['notify'])) {

				if (count($objectData['notify']) == 0) {
					$notifyText = ", notify = '{}' ";
				} else {
					$notifyText = ", notify = '{". implode(',', $this->parseObjectTaggedWith($objectData['notify'])) ."}' ";
				}

			}

			return $tagText . $readPermissionText . $writePermissionText . $notifyText;

		}

		private function validateDateString($date, $format = 'Y-m-d'){

		    $d = DateTime::createFromFormat($format, $date);
		    return $d && $d->format($format) == $date;

		}

		public function castFromTemplate($object, $memo = null, $options = null) {
// echo "002::: " . PHP_EOL;
			if (
				$object['object_bp_type'] === 'users'
			) {
				return $object;
			}

			static $memo = array();

			// Pass in orginal template object if memo does not exist
			if($memo == null) {

				$memo['root'] = $object;

			}

			if (is_array($options) && array_key_exists('start_date', $options)) {
				$memo['rootNewStartDate'] = $options['start_date'];
			} elseif (is_object($options) && $options->start_date) {
				$memo['rootNewStartDate'] = $options->start_date;
			}

			// Identify object bp type
			$objectBPType = $object['object_bp_type'];

			$blueprint = $this->getBlueprint($objectBPType, false);

			// If blueprint can not be found, stop function
			if($blueprint == null) {

				if ($this->debug) {
					echo('Blueprint not found');
				}
				return false;

			} else {

				// This is the new object to be constructed from template
				$templateChild = array();

				// Temporary code
				unset($object['is_template']);
				unset($object['state']);
				unset($object['date_created']);
				unset($object['created_by']);
				unset($object['last_updated']);
				unset($object['last_updated_by']);
				unset($object['object_uid']);

				// Loop over blueprint
				foreach($blueprint as $propName => $property) {

					if ($property['fieldType'] == 'timer') {

						$options[$propName . '_running'] = array();
						$object[$propName . '_running'] = array();
						$templateChild[$propName . '_running'] = array();
						$templateChild[$propName] = $object[$propName];

					} else if (
						is_array($options)
						&& array_key_exists($propName, $options)
						&& $options[$propName]
					) {

						$templateChild[$propName] = $options[$propName];

					} else if ($propName == 'parent') {

						if ($object['parent'] != null || $object['parent'] != 0) {

							if(array_key_exists($object['parent'], $memo)) {

								$templateChild['parent'] = $memo[$object['parent']];

							} else {

								$templateChild['parent'] = $object['parent']['id'];

								$memo[$object['parent']['id']] = $object['parent'];

							}

						}

					} else if ($property['type'] == 'int'
						|| $property['type'] == 'select'
						|| $property['type'] == 'string'
						|| $property['type'] == 'usd'
						|| $property['type'] == 'tag'
						|| $property['type'] == 'date'
						|| $property['type'] == 'list'
						|| $property['type'] == 'object'
						|| $property['fieldType'] === 'title'
						|| $property['fieldType'] === 'address'
					) {

						// Make sure that line items in menus are up to date
						if ($objectBPType === 'inventory_menu' && $propName === 'sections') {

							$templateChild['sections'] = $this->runSteps(
								$object['proposal']
								, [
									'copyMenuFromTemplate' => [
										'template' 		 => $object['id']
										, 'menu'		 => $templateChild['id']
										, 'dateRef' 	 => (new DateTime($memo['root']['start_date']))->format('U')
										, 'newStartDate' => $memo['rootNewStartDate']
									]
								]
							)['sections'];

						// Use case for entity date scaling
						} else if(
							array_key_exists('scaleEntityDate', $options)
							&& $property['type'] == 'date'
						) {

							if(!empty($object[$propName])) {

								$entityParentdateCreated = (new DateTime($options['scaleEntityDate']))->format('U');
								$entityDateDiff = (new DateTime($object[$propName]))->format('U') - intval($entityParentdateCreated);

								$templateChild[$propName] = (new DateTime())->format('U') + intval($entityDateDiff);

							}

						} else {

							$templateChild[$propName] = $object[$propName];

						}

					} else if ($property['type'] == 'objectId') { // OBJECT ID

						if ($property['objectType'] == 'users'
							|| $property['fieldType'] == 'user'
							|| $property['fieldType'] == 'companies'
							|| $property['fieldType'] == 'contacts'
							|| $property['objectType'] == 'contacts'
							|| $property['objectType'] == 'project_types'
							|| $property['objectType'] == 'categories'
							|| $property['objectType'] == 'staff_base'
							|| $property['objectType'] == 'inventory_service'
							|| $property['objectType'] == 'staff'
							|| $property['objectType'] == 'vendors'
							|| $property['objectType'] == 'file_meta_data'
							|| $property['objectType'] == 'contracts'
							|| $property['objectType'] == 'payment_schedule_template'
							|| $property['objectType'] == 'contact_types'
							|| $property['objectType'] == 'company_categories'
							|| $property['fieldType'] == 'attachment'
							|| $property['fieldType'] == 'edge'
						) {

							$templateChild[$propName] = $object[$propName];

						} else {

							if($object[$propName] != null || $object[$propName] != 0) {

								// Check if the property value has an id property
								if($object[$propName]['id'] != null) {

									// Grab object from database
									$linkedObj = $this->getById($property['objectType'], intval($object[$propName]['id']), 1);

									// Check if object is not null
									if($linkedObj != null) {

										$linkedObjId = $linkedObj['id'];

										if(array_key_exists($linkedObjId, $memo)) {

											$templateChild[$propName] = $memo[$linkedObjId]; // Set from memo

										} else {

											// ** Checking the $options obj for 'is_template' property which will be passed through the
												// cycle of templating and be taken into account when creating a child obj.
											if($options != null) {

												// is_template
												if (
													property_exists($options, 'is_template')
												) {

													$memo['is_template'] = $options->is_template;

												}

											}

											// Point of recursion
											$templateChild[$propName] = $this->castFromTemplate($linkedObj, $memo, null); // Construct object

										}

									}

								}

							}

						}

					} else if($property['type'] == 'objectIds') { // OBJECT IDs

						$templateChild[$propName] = array();
						foreach($object[$propName] as $key => $value)	{

							array_push($templateChild[$propName], $value);

						}

					}

				}

				// If object has tools, reference those tools in new object
				if($object['tools']) {
					$templateChild['tools'] = $object['tools'];
				}

				$templateChild['data_source'] = $object['instance'];
				$templateChild['data_source_id'] = $object['id'];
				$templateChild['related_object'] = !empty($object['related_object']) ? $object['related_object'] : '';
				$templateChild['space'] = !empty($object['space']) ? $object['space']: '';

				// 'tagged_with' property logic

				foreach($object['tagged_with'] as $id) {

					if(array_key_exists($id, $memo)) {

						$value = $memo[$id];

						array_push($object['tagged_with'], $value);

					}

				}

				// Checking the $options object to modify root object with user selected values
				if($options != null) {

					// ******** Refactoring this block
					// Instead of checking for specific properties on options object, just loop through it.
					// Only exceptions are checked for.

					foreach ($options as $optionName => $option) {

						$templateChild[$optionName] = $option;

					}

					// *******************************
					// We can start treating exceptions here

					// tagged_with

					$object['tagged_with'] = __::filter($object['tagged_with'], function($id) use($memo) {

						if(array_key_exists($id, $memo)) {

							return false;

						}

						return true;

					});

					$object['tagged_with'] = array_unique($object['tagged_with']);
					$templateChild['tagged_with'] = $object['tagged_with'];

					if (
						property_exists($options, 'tagged_with')
						&& is_array($options->tagged_with)
					) {

						foreach ($options->tagged_with as $tag) {

							array_push(
								$templateChild['tagged_with']
								, $tag
							);

						}
					}

				}

				$templateChild['notify'] = $templateChild['tagged_with'];
				if (is_array($object['shared_with'])) {
					$templateChild['shared_with'] = $object['shared_with'];
				}

				// Remove date create vale from template being constructed for new value to be added by create function
				unset($templateChild['date_created']);

				// ** Checking the $memo obj and $options obj for 'is_template' property.
				if (array_key_exists('is_template', $memo)) {

					$templateChild['is_template'] = $memo['is_template'];

				}

				if (property_exists($options, 'is_template')) {

					$templateChild['is_template'] = $options->is_template;

				}
				// Create object in database
				$templateChild = $this->create($objectBPType, $templateChild, 0, 1);

				$memo[$object['id']] = $templateChild['id']; // Set to memo

				// For Duplicating Documents
				if ( $objectBPType === 'contracts' && !empty($object['html_string']) ) {
					$templateChild = $this->update($objectBPType, array('id' => $templateChild['id'],'html_string' => $object['html_string']), 0, 1);
				}

				// ****************** Search for related objs ******************

				// ***** Schedule *****

				if($objectBPType == 'groups' && $object['group_type'] == 'Schedule' && false) {

					// Get related shifts

					$shifts = array();

					$shifts = $this->where('groups', [
							'group_type' => 'Shift',
							'parent' => $object['id']
							/*
                            'tagged_with' => [
                                                            type => 'any',
                                                            values => [$object['id']]
                                                        ]
                            */
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					foreach($shifts as $shift) {

						$shiftOptions = null;

						$shiftStartDay = (new DateTime($templateChild['start_date']))->format('d');
						$shiftStartMonth = (new DateTime($templateChild['start_date']))->format('n');
						$shiftStartYear = (new DateTime($templateChild['start_date']))->format('Y');

						$shiftStartDate = new DateTime($shift['start_date']);
						$shiftEndDate = new DateTime($shift['end_date']);

						$newShiftStartDate = $shiftStartDate->setDate(intval($shiftStartYear), intval($shiftStartMonth), intval($shiftStartDay));
						$newShiftEndDate = $shiftEndDate->setDate(intval($shiftStartYear), intval($shiftStartMonth), intval($shiftStartDay));

						$shiftOptions['start_date'] = $newShiftStartDate->format('Y-M-d H:i:s');
						$shiftOptions['end_date'] = $newShiftEndDate->format('Y-M-d H:i:s');

						$newShift = $this->castFromTemplate($shift, $memo, $shiftOptions);

						// ***
						// !TESTING (Peter 05/15/20):  going to test with Ashley to see if this block is necessary.

						//$newShift['is_template'] = $templateChild['is_template'];
						//$newShift['parent'] 	 = $templateChild['id'];

						//$this->update('groups', $newShift, 0, null);
						// ***

					}

				}

				// ***** Project *****

				if($objectBPType == 'groups' && $object['group_type'] == 'Project') {

					// Look for tasks, documents and schedules

					$tasks = array();
					$documents = array();
					$schedules = array();

					$tasks = $this->where('groups', [
							'group_type' => 'Task',
							'tagged_with' => [
								type => 'any',
								values => [$object['id']]
							]
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					$schedules = $this->where('groups', [
							'group_type' => 'Schedule',
							'parent' => $object['proposal']['id']
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					$documents = $this->where('document', [
							'tagged_with' => [
								type => 'any',
								values => [$object['id']]
							]
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					$contracts = $this->where('contracts', [
							'related_object' => $object['id']
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					foreach($schedules as $schedule) {

						$scheduleOptions = null;

						$scheduleOptions['start_date'] = $templateChild['start_date'];

						$newSchedule = $this->castFromTemplate($schedule, $memo, $scheduleOptions); // Construct object

						if($options != null) {

							if(property_exists($options, 'name')) {

								$newSchedule['name'] = $options->name;

								$this->update('groups', $newSchedule, 0, null);

							}

						}

					}

					$rootTemplateStartDate = (new DateTime($object['start_date']))->format('U');
					$newProjectStartDate = (new DateTime($templateChild['start_date']))->format('U');

					foreach($tasks as $taskObj) {

						$newTask = array();
						$newDateVal = 0;

						$taskEndDate = (new DateTime($taskObj['end_date']))->format('U');

						$newTask = $this->castFromTemplate($taskObj, $memo, null); // Construct object

						// If project has a start date, then begin scaling
						if(property_exists($options, 'start_date')) {

							$diff = intval($taskEndDate) - intval($rootTemplateStartDate);

							$newDateVal = intval($newProjectStartDate) + intval($diff);

							$newTask['end_date'] = date('Y-M-d', $newDateVal);

							$this->update('groups', $newTask, 0, null);

						}

					}

					foreach($documents as $document) {

						$this->castFromTemplate($document, $memo, null);

					}

					foreach($contracts as $contract) {

						$this->castFromTemplate($contract, $memo, [
							'related_object' => $templateChild['id']
							, 'html_string' => $contract['html_string']
						]);

					}

					// This block fixes the issue of proposals being created before their parent has been created in the db
					$templateChild['proposal']['main_object'] = $templateChild['id'];

					if($options !== null) {

						if(property_exists($options, 'name')) {

							$templateChild['proposal']['name'] = $options->name;

						}

					}

					$this->update('proposals', $templateChild['proposal'], 0, null);

					// Layouts
					$layouts = $this->where(
						'layouts'
						, [
							'layout_id' => $object['id']
						]
					);

					foreach ($layouts as $layout) {

						$this->castFromTemplate($layout, $memo, ['layout_id' => $templateChild['id']]);

					}
					unset($layouts);

				}

				// ***** Task *****

				if($objectBPType == 'groups' && $object['group_type'] == 'Task') {

					$documents = array();

					$documents = $this->where('document', [
							'tagged_with' => [
								type => 'any',
								values => [$object['id']]
							]
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					foreach($documents as $document) {

						$this->castFromTemplate($document, $memo, null);

					}

				}

				// ***** Document *****

				if($objectBPType == 'document') {

					$documents = array();

					$documents = $this->where('document', [
							'tagged_with' => [
								type => 'any',
								values => [$object['id']]
							]
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					foreach($documents as $document) {

						$this->castFromTemplate($document, $memo, null);

					}

				}

				// ***** Team *****

				if($objectBPType == 'groups' && $object['group_type'] == 'Team') {

					$teams = array();
					$projects = array();
					$tasks = array();
					$documents = array();

					$teams = $this->where('groups', [
							'group_type' => 'Team',
							'tagged_with' => [
								type => 'any',
								values => [$object['id']]
							]
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					$projects = $this->where('groups', [
							'group_type' => 'Project',
							'tagged_with' => [
								type => 'any',
								values => [$object['id']]
							]
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					$tasks = $this->where('groups', [
							'group_type' => 'Task',
							'tagged_with' => [
								type => 'any',
								values => [$object['id']]
							]
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					$documents = $this->where('document', [
							'tagged_with' => [
								type => 'any',
								values => [$object['id']]
							]
						],
						'',
						0,
						false,
						0,
						'null',
						'asc',
						100,
						null,
						[],
						'string',
						$object['instance']
					);

					foreach($teams as $team) {

						$this->castFromTemplate($team, $memo, null);

					}

					foreach($projects as $project) {

						$this->castFromTemplate($project, $memo, null);

					}

					foreach($tasks as $task) {

						$this->castFromTemplate($task, $memo, null);

					}

					foreach($documents as $document) {

						$this->castFromTemplate($document, $memo, null);

					}

				}

				// ***** Workflows *****

				if (
					$objectBPType === 'entity_workflow'
					|| $objectBPType === 'contact_types'
					|| $objectBPType === 'project_types'
					|| $objectBPType === 'task_types'
				) {

					$actionSet = $this->where(
						'event_type'
						, [
							'object' => $object['id']
						]
					);

					if (is_array($actionSet)) {
						foreach ($actionSet as $action) {

							$action['object'] = $templateChild['id'];
							$this->castFromTemplate(
								$action
								, $memo
								, [
									'state' => $action['state']
								]
							);

						}
					}
					unset($actionSet);

				}

				// ***** Workflows *****
				if ($objectBPType === 'event_type') {

					switch ($object['type']) {

						case 'createTasks':
						$tasks = $this->where(
							'groups'
							, [
								'parent' => 			$object['id']
								, 'group_type' => 	'Task'
							]
						);

						if (is_array($tasks)) {
							foreach ($tasks as $task) {

								$this->castFromTemplate(
									$task
									, [
										'parent' => $templateChild['id']
									]
									, [
										'is_template' => 1
									]
								);

							}
						}
						unset($tasks);
						break;

						case 'createEntity':
						$entities = $this->where(
							$object['options']['objectType']
							, [
								'parent' => 			$object['id']
							]
						);

						if (is_array($entities)) {
							foreach ($entities as $entity) {

								$this->castFromTemplate(
									$entity
									, [
										'parent' => $templateChild['id']
									]
									, [
										'is_template' => 1
										, 'parent' => $templateChild['id']
									]
								);

							}
						}
						unset($entities);
						break;

					}

				}

				// Apply merge tags
				// !TODO: Batch these calls
				foreach ($blueprint as $propName => $property) {

					if (
						array_key_exists($propName .'_merge', $object)
						&& !empty($object[$propName .'_merge'])
					) {

						switch ($property['fieldType']) {

							case 'date':
							case 'user':
							case 'users':
							case 'companies':
							case 'contacts':
								$tmpTag = false;
								if (is_array($object[$propName .'_merge'])) {
									$tmpTag = [];
									foreach ($object[$propName .'_merge'] as $tag) {
										array_push($tmpTag, '{{'. $tag .'}}');
									}
								} else {
									$tmpTag = '{{'. $object[$propName .'_merge'] .'}}';
								}

								$templateChild[$propName] = $this->runSteps(
									$object
									, [
										'mergeText' => [
											'obj' => 		$templateChild
											, 'template' => $tmpTag
											, 'field' => 	$property
										]
									]
									, true
								)['memo'];
								if ($templateChild[$propName]) {
									$this->update(
										$templateChild['object_bp_type']
										, [
											'id' => $templateChild['id']
											, $propName => $templateChild[$propName]
										]
									);
								}
								break;

						}

					}

				}
// echo " :::: 002 " . PHP_EOL;

                return $templateChild;

			}

		}

		public function changeInstance($instanceKey){

			$this->instance = $instanceKey;

		}

		public function changeObjectType($objectId, $newType){

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			// !TODO: id should be able to take array of ids
			$updateClause = "UPDATE objects SET object_type = '$newType' WHERE instance = '".$this->instance."' AND id = ". $objectId ." RETURNING *;";

			$statement = $this->writeDb->prepare($updateClause);

			if($statement->execute()){

				array_push($returnArray, ['id' => $info['id']]);

			}elseif($this->debug){

				return $this->writeDb->errorInfo();

			}

		}

		public function countObjs (
			$objectType = ''
			, $where = []
			, $groupBy = ''
			, $dateField = ''
		) {

			// Set variables
			$additionalPhrase = '';
			$hostInstance = '';

			// Set instance
			$instance = $this->instance;

			// Get blueprint
			$bp = $this->getBlueprint($objectType, false);

			if (is_array($where['tagged_with']) and count($where['tagged_with']) == 0) {
				unset($where['tagged_with']);
			}

			if (is_array($where['read']) and count($where['read']) == 0) {
				unset($where['read']);
			}

			if (is_array($where['write']) and count($where['write']) == 0) {
				unset($where['write']);
			}

			if (array_key_exists('archive', $where)) {
				unset($where['archive']);
			}

			$objectTypePhrase = $this->getObjectTypePhrase($objectType);

			// Get additional phrase
			if (count($where) > 0) {
				$selectArray = $this->getWhereClause($where);
				$additionalPhrase = 'AND '. $selectArray['selectString'];
				$instance = !empty($selectArray['instance']) ? $selectArray['instance'] : $instance;
			}

			if ($groupBy === false) {

				$query = 	'SELECT '.
								'COUNT (id) AS count '.
							'FROM objects '.
							"WHERE instance = '". $instance ."' AND ". $objectTypePhrase ." AND is_deleted = false ". $selectString . " " . $additionalPhrase . ";";

			} else {

				$query = 	'SELECT '.
								'COUNT (id) AS count, object_data->>\''. $groupBy .'\' AS group '.
							'FROM objects '.
							"WHERE instance = '". $instance ."' AND ". $objectTypePhrase ." AND is_deleted = false ". $selectString . " " . $additionalPhrase ." ".
							'GROUP BY '.
								'object_data->>\''. $groupBy .'\';';

			}

			$statement = $this->db->prepare($query);

			if ($statement->execute() ){

				if ($groupBy === false) {

					return $statement->fetchAll()[0]['count'];

				}

				$ret = [];
				foreach ($statement->fetchAll() as $count) {

					$groupVal = $count['group'];
					switch ($bp[$groupBy]['type']) {

						case 'objectIds':
							$groupVal = json_decode($groupVal);
							break;

					}

					array_push(
						$ret
						, [
							'count' => 		$count['count']
							, 'group' => 	$groupVal
						]
					);

				}

				return $ret;

			} elseif ($this->debug) {

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function copyInstanceSettingsObjects($fromInstance, $toInstanceKey){

			$fromInstance = $this->where('instances', ['instance' => $fromInstance])[0];
			$settingsObjectsTypes = explode(',', $fromInstance['settings_objects']);

			// GET BATCH OF SETTINGS TO COPY
			$batchSize = 175;

			// LOOP OVER BATCH
			foreach($settingsObjectsTypes as $i => $type){

				$page = 0;
				$continue = true;

				while($continue){

					$this->setInstance($fromInstance['instance']);
					$batch = $this->getAll($type, 0, true, $batchSize*$page, 'id', 'ASC', $batchSize, $fromInstance['instance']);

					if(count($batch) == 0){

						$continue = false;

					}else{

						// COPY OBJECTS
						// USE DATA SOURCE ID PROP
						// PUT THE INSTANCE IN THE DATA SOURCE PROP

						// PLACE INSTANCE KEY IN -> data_source AND OLD ID IN -> data_source_id
						if(is_array($batch)){

							foreach($batch as $i => $item){

								$batch[$i]['data_source'] = $fromInstance['id'];
								$batch[$i]['data_source_id'] = $batch[$i]['id'];
								$batch[$i]['instance'] = $toInstanceKey;

							}

						}

						// CREATE OBJECTS
						$this->setInstance($toInstanceKey);
						$this->create($type, $batch, true, false);

						$page++;

					}

				}

			}

			// LOOP OVER BATCH A SECOND TIME, UPDATING RELATIONSHIPS
			foreach($settingsObjectsTypes as $i => $type){

				$blueprint = $this->getBlueprint($type, false);

				$page = 0;
				$continue = true;

				while($continue){

					$batch = $this->getAll($type, 0, true, $batchSize*$page, 'id', 'ASC', $batchSize, $toInstanceKey);

					if(count($batch) > 0){

						foreach($blueprint as $propertyKey => $property){

							if($property['type'] === 'objectId' and __::contains($settingsObjectsTypes, $property['objectType'])){

								// GET CHILD OBJS
								$childObjOldIds = __::pluck($batch, $propertyKey);
								$childObjOldIds = __::uniq($childObjOldIds);

								$childObjs = $this->where($property['objectType'], [
									'data_source' => $fromInstance['id'],
									'data_source_id' => [
										'type' => 'or',
										'values' => $childObjOldIds
									]
								]);

								// UPDATE RELATIONSHIPS
								foreach($batch as $i => $obj){

									// ONLY UPDATE IF COPIED FROM THE OTHER INSTANCE
									if($obj['data_source'] == $fromInstance['id']){

										$oldId = $obj[$propertyKey];
										$batch[$i][$propertyKey] = [];

										foreach($childObjs as $k => $childObj){

											if($childObj['data_source_id'] == $oldId){

												$batch[$i][$propertyKey] = $childObj['id'];

											}

										}

									}

								}

							}elseif($property['type'] === 'objectIds' and __::contains($settingsObjectsTypes, $property['objectType'])){

								// GET CHILD OBJS
								$temp = __::pluck($batch, $propertyKey);
								$childObjOldIds = [];
								foreach($temp as $k => $ids){
									foreach($ids as $l => $id){
										array_push($childObjOldIds, $id);
									}
								}

								$childObjOldIds = __::uniq($childObjOldIds);

								$childObjs = $this->where($property['objectType'], [
									'data_source' => $fromInstance['id'],
									'data_source_id' => [
										'type' => 'or',
										'values' => $childObjOldIds
									]
								]);

								// UPDATE RELATIONSHIPS
								foreach($batch as $i => $obj){

									// ONLY UPDATE IF COPIED FROM THE OTHER INSTANCE
									if($obj['data_source'] == $fromInstance['id']){

										$temp = $obj[$propertyKey];
										$batch[$i][$propertyKey] = [];

										if(is_array($temp)){
											foreach($temp as $j => $oldId){

												foreach($childObjs as $k => $childObj){

													if($childObj['data_source_id'] == $oldId){

														array_push($batch[$i][$propertyKey], $childObj['id']);

													}

												}

											}
										}

									}

								}

							}

						}


						// UPDATE
						$this->setInstance($toInstanceKey);
						$this->update($type, $batch, false);

						$page++;

					}else{

						$continue = false;

					}

				}

			}

			return true;

		}

		public function create($objectType, $objectData = null, $multiple = 0, $retChildObjs = 0){
// echo " 003:::" . PHP_EOL;
			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			if($_POST->multiple){
				$multiple = $_POST->multiple;
			}

			if ($multiple == 0) {

				// date_created override
				if (is_array($objectData) and array_key_exists('date_created', $objectData)) {

					$statement = $this->writeDb->prepare(
						"INSERT INTO objects (id, date_created) VALUES (DEFAULT, '". $objectData['date_created'] ."') RETURNING *;"
					);

				} else {

					// Get the sortIndex for groups & sets
					$sortIndex = '';
					if (
						$objectType === 'groups'
						|| substr($objectType, 0, 1) === '#'
					) {
						$sortIndex = $this->fieldTypes['sortIndex']['setNext'](['object_bp_type' => $objectType], 'sort_index', [], $this);
					}

					$statement = $this->writeDb->prepare(
						"INSERT INTO objects (
							id
							, instance
							, object_type
							, object_data
							, date_created
							, is_deleted
							, tagged_with
							, shared_with
							, read_obj
							, write_obj
							, notify
							, sort_index
						) VALUES (
							DEFAULT
							, '". $this->instance ."'
							, DEFAULT
							, DEFAULT
							, DEFAULT
							, DEFAULT
							, DEFAULT
							, DEFAULT
							, DEFAULT
							, DEFAULT
							, DEFAULT
							, '$sortIndex'
						) RETURNING *;"
					);

				}

				// var_dump($statement);
				// 		die();

				if ($statement->execute()) {

					$info = $statement->fetchAll()[0];

					// parse object data
					if(is_array($objectData)){

						$blueprint = $this->getBlueprint($objectType, false, true);

                        $bp;
                        ///use case if for new 'Sets' (entity_type)-which are the blueprints of entity records
                        if ( $objectType == 'entity_type' || $objectType == 'view') {
                            $bp = $blueprint;
                        } else {
                            $bp = $blueprint['blueprint'];
                        }

						$isTask = 'false';
						if (is_array($blueprint) && $blueprint['is_task'] === true) {
							$isTask = 'true';
						}

						$obj = $this->parseInput(
									$objectType
									, $objectData
									, array(
										'id' => intval($info['id'])
										, 'object_bp_type' => $objectType
									)
									, 1
									, $bp
								);

						if ($obj === false) {
							return false;
						}

						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];
						$obj['last_updated'] = $info['date_created'];

						if (
							$GLOBALS['portal']
							&& $_COOKIE['p_uid']
						) {
							$obj['created_by'] = intval($_COOKIE['p_uid']);
							$obj['last_updated_by'] = intval($_COOKIE['p_uid']);
						} else {
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated_by'] = intval($_COOKIE['uid']);
						}

						$obj['object_uid'] = $this->getNextObjectId($objectType);

						$tagText = $this->getIntArrUpdatePhrase($objectData);
						$string = "UPDATE objects SET object_type = '$objectType', instance = '".$this->instance."', is_task = ". $isTask .", object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."'". $tagText ." WHERE id = ". $info['id'] ." RETURNING *;";
						$statement = $this->writeDb->prepare($string);

						if ($statement->execute()) {

							$createdObj = $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0];

                            $this->triggerCreateActions($createdObj);

                            return $createdObj;

						} else {

							return $statement->errorInfo();

						}

					} else {

						$blueprint = $this->getBlueprint($objectType, false, true);
						$isTask = 'false';
						if (is_array($blueprint) && $blueprint['is_task'] === true) {
							$isTask = 'true';
						}

						$obj = $this->parseInput($objectType, array(), array(), 1, $blueprint['blueprint']);
						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];
						$obj['last_updated'] = $info['date_created'];
						$obj['object_uid'] = $this->getNextObjectId($objectType);

						if (
							$GLOBALS['portal']
							&& $_COOKIE['p_uid']
						) {
							$obj['created_by'] = intval($_COOKIE['p_uid']);
							$obj['last_updated_by'] = intval($_COOKIE['p_uid']);
						} else {
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated_by'] = intval($_COOKIE['uid']);
						}

						$tagText = $this->getIntArrUpdatePhrase($objectData);
						$string = "UPDATE objects SET object_type = '$objectType', instance = '".$this->instance."', is_task = ". $isTask .", object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."'". $tagText ." WHERE id = ". $info['id'] .";";
						$statement = $this->writeDb->prepare($string);

						if ($statement->execute()) {

							return $info['id'];

						} elseif ($this->debug) {

							return $this->writeDb->errorInfo();

						}

					}

				} else {

					return $this->writeDb->errorInfo();

				}

			}else{

				$objectDataArray = $objectData;
				$returnArray = [];

				foreach($objectDataArray as $objectData){

					// Get the sortIndex for groups & sets
					$sortIndex = '';
					if (
						$objectType === 'groups'
						|| substr($objectType, 0, 1) === '#'
					) {
						$sortIndex = $this->fieldTypes['sortIndex']['setNext'](['object_bp_type' => $objectType], 'sort_index', [], $this);
					}

					// date_created override
					if(is_array($objectData) and array_key_exists('date_created', $objectData)){
						$statement = $this->writeDb->prepare(
							"INSERT INTO objects (id, date_created) VALUES (DEFAULT, '". $objectData['date_created'] ."') RETURNING *;"
						);
					}else{
						$statement = $this->writeDb->prepare(
							"INSERT INTO objects (
								id
								, instance
								, object_type
								, object_data
								, date_created
								, is_deleted
								, tagged_with
								, shared_with
								, read_obj
								, write_obj
								, notify
								, sort_index
							) VALUES (
								DEFAULT
								, '". $this->instance ."'
								, DEFAULT
								, DEFAULT
								, DEFAULT
								, DEFAULT
								, DEFAULT
								, DEFAULT
								, DEFAULT
								, DEFAULT
								, DEFAULT
								, '$sortIndex'
							) RETURNING *;"
						);
					}

					if($statement->execute()){

						$info = $statement->fetchAll()[0];

						$blueprint = $this->getBlueprint($objectType, false, true);
						$isTask = 'false';
						if (is_array($blueprint) && $blueprint['is_task'] === true) {
							$isTask = 'true';
						}

						// parse object data
						if(is_array($objectData)){

							$obj = $this->parseInput($objectType, $objectData, array(), 1, $blueprint['blueprint']);

							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated'] = $info['date_created'];
							$obj['last_updated_by'] = intval($_COOKIE['uid']);
							$obj['object_uid'] = $this->getNextObjectId($objectType);

							$tagText = $this->getIntArrUpdatePhrase($objectData);
							$string = "UPDATE objects SET object_type = '$objectType', is_task = ". $isTask .", instance = '".$this->instance."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."'". $tagText ." WHERE id = ". $info['id'] ." RETURNING *;";
							$statement = $this->writeDb->prepare($string);

							if($statement->execute()){

								array_push($returnArray, $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0]);

							}

						}else{

							$obj = $this->parseInput($objectType, array(), array(), 1, $blueprint['blueprint']);
							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated'] = $info['date_created'];
							$obj['last_updated_by'] = intval($_COOKIE['uid']);
							$obj['object_uid'] = $this->getNextObjectId($objectType);

							$tagText = $this->getIntArrUpdatePhrase($objectData);
							$string = "UPDATE objects SET object_type = '$objectType', is_task = ". $isTask .", instance = '".$this->instance."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."'". $tagText ." WHERE id = ". $info['id'] .";";
							$statement = $this->writeDb->prepare($string);

							if($statement->execute()){

								array_push($returnArray, ['id' => $info['id']]);

							}

						}

					}

				}

				return $returnArray;

			}

		}

		public function createInstance($objectType, $objectData = null, $multiple = 0, $retChildObjs = 0){

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			if($_POST->multiple){
				$multiple = $_POST->multiple;
			}

			if($multiple == 0){

				$statement = $this->writeDb->prepare(
					"INSERT INTO objects DEFAULT VALUES RETURNING *;"
				);

				if($statement->execute()){

					$info = $statement->fetchAll()[0];

					// parse object data
					if(is_array($objectData)){

						$obj = $this->parseInput($objectType, $objectData, array(), 1);

						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];
						$obj['created_by'] = intval($_COOKIE['uid']);
						$obj['last_updated'] = $info['date_created'];
						$obj['last_updated_by'] = intval($_COOKIE['uid']);

						$string = "UPDATE objects SET object_type = '$objectType', instance = '". $obj['instance'] ."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;";
// 						$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] ." RETURNING *;";

						$statement = $this->writeDb->prepare($string);
						if($statement->execute()){

							return $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0];

						}else{

							return $this->writeDb->errorInfo();

						}

					}else{

						$obj = $this->parseInput($objectType, array(), array(), 1);
						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];
						$obj['created_by'] = intval($_COOKIE['uid']);
						$obj['last_updated'] = $info['date_created'];
						$obj['last_updated_by'] = intval($_COOKIE['uid']);

						$string = "UPDATE objects SET object_type = '$objectType', instance = '". $obj['instance'] ."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] .";";
// 						$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] .";";

						$statement = $this->writeDb->prepare($string);
						if($statement->execute()){

							return $obj;

						}elseif($this->debug){

							return $this->writeDb->errorInfo();

						}

					}


				}else{

					return $this->writeDb->errorInfo();

				}

			}else{

				$objectDataArray = $objectData;

				$returnArray = [];

				foreach($objectDataArray as $objectData){

					$statement = $this->writeDb->prepare(
						"INSERT INTO objects DEFAULT VALUES RETURNING *;"
					);

					if($statement->execute()){

						$info = $statement->fetchAll()[0];

						// parse object data
						if(is_array($objectData)){

							$obj = $this->parseInput($objectType, $objectData, array(), 1);

							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated'] = $info['date_created'];
							$obj['last_updated_by'] = intval($_COOKIE['uid']);

							$string = "UPDATE objects SET object_type = '$objectType', instance = '". $obj['instance'] ."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;";
// 							$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] ." RETURNING *;";

							$statement = $this->writeDb->prepare($string);
							if($statement->execute()){

								array_push($returnArray, $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0]);

							}

						}else{

							$obj = $this->parseInput($objectType, array(), array(), 1);
							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated'] = $info['date_created'];
							$obj['last_updated_by'] = intval($_COOKIE['uid']);

							$string = "UPDATE objects SET object_type = '$objectType', instance = '". $obj['instance'] ."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] .";";
// 							$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] .";";

							$statement = $this->writeDb->prepare($string);
							if($statement->execute()){

								array_push($returnArray, ['id' => $info['id']]);

							}

						}

					}

				}

				return $returnArray;

			}

		}

		public function createNewPassword($passwordToUse = false){

			$alphabet = "abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789";

		    $pass = array(); //remember to declare $pass as an array

		    $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache

		    for ($i = 0; $i < 8; $i++) {

		        $n = rand(0, $alphaLength);

		        $pass[] = $alphabet[$n];

		    }

		    if($passwordToUse){
				$newPassword = $passwordToUse;
			}else{
				$newPassword = implode($pass); //turn the array into a string
			}

			$salt = base64_encode(openssl_random_pseudo_bytes(PBKDF2_SALT_BYTE_SIZE));

		    $newPasswordHash = PBKDF2_HASH_ALGORITHM . ":" . PBKDF2_ITERATIONS . ":" .  $salt . ":" .

		        base64_encode($this->pbkdf2(

		            PBKDF2_HASH_ALGORITHM,

		            $newPassword,

		            $salt,

		            PBKDF2_ITERATIONS,

		            PBKDF2_HASH_BYTE_SIZE,

		            true

		        ));

			return array(
				'pwd' => $newPassword
				,'pwdHash' => $newPasswordHash
			);

		}

		public function delete($objectType, $objectId) {

			// update the last_updated and last_updated_by fields
// 			$this->update($objectType, ['id'=>$objectId], 0);

			if ($this->writeDb == null) {
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			$statement = $this->writeDb->prepare("UPDATE objects SET is_deleted = true WHERE id = ". $objectId ." and instance = '". $this->instance ."' RETURNING *;");

			if ($statement->execute()) {

				// !TODO: refactor this to an action, store the rule to run
				// that action in the blueprint, and cue off of the blueprint
				// to run actions rather than looking at groups.
				if ($objectType === 'groups') {

					// Get related proposal
					$proposals = $this->where(
						'proposals'
						, ['main_object' => $objectId]
					);

					// Get related invoices
					$invoices = $this->where(
						'invoices'
						, ['related_object' => [
							'type' => 'or'
							, 'values' => __::pluck($proposals, 'id')]
						]
					);

					// Get related menus
					$menus = $this->where(
						'inventory_menu'
						, ['id' => [
							'type' => 'or'
							, 'values' => __::pluck($proposals, 'menu')]
						]
					);

					// Get related pricing breakdown reports
					$priceBreakdowns = $this->where(
						'inventory_menu_pricing_breakdown'
						, ['space' => $objectId]
					);

					// Erase dependent data
					$toErase = __::pluck($proposals, 'id');
					$toErase = array_merge($toErase, __::pluck($invoices, 'id'));
					$toErase = array_merge($toErase, __::pluck($menus, 'id'));
					$toErase = array_merge($toErase, __::pluck($priceBreakdowns, 'id'));

					foreach ($toErase as $idToErase) {

						$this->delete('', $idToErase);

					}

					return true;

				} else {

					return true;

				}

			}elseif($this->debug){

				var_dump($statement, $statement->errorInfo(), "UPDATE objects SET is_deleted = true WHERE id = ". $objectId ." RETURNING *;");
				return false;

			}

		}

		public function getAll($objectType, $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'date_created', $sortDir = 'asc', $limit = 100, $sortCast = 'string', $count = true, $archived = false){

			// get paging count query strings
			$countSelect = ', full_count';
			$countParse = ', COUNT(id) OVER() AS full_count';
			$archivedPhrase = 'AND is_deleted = false';

			if ($archived === true) {
				$archivedPhrase = 'AND is_deleted = true';
			} elseif ($archived === 'any') {
				$archivedPhrase = '';
			}

			if($count === false){
				$countSelect = '';
				$countParse = '';
			}

			$instanceClause = "instance = '". $this->instance ."'";

			// Feature block for cross-instance sharing of sets
			if ($this->instance == 'rickyvoltz' || $this->instance == 'voltzsoftware') {

				if ($objectType == 'entity_type') {
					$instanceClause = "(" . $instanceClause . " OR (object_data->'shared_with_instances')::jsonb @> '[\"" .$this->instance."\"]')";
				}

			}

			// get selection sql from query obj
			$selectClause = $this->getSelectClause($getChildObjects, null, false);
			if($paged){

				switch($sortCast){

					case 'int':
						$orderByString = "(object_data->>'". $sortCol ."')::int";
						break;

					default:
						if ($sortCol === 'sortIndex') {
							$orderByString = "sort_index::bytea";
							$countSelect = ', row_number';
							$countParse .= ', (ROW_NUMBER() OVER() + '. $offset .') AS row_number';
						} else {
							$orderByString = "object_data->>'". $sortCol ."'";
						}

				}

				$statement = $this->db->prepare(
					$selectClause . $countSelect ."
						FROM (
							SELECT *". $countParse ."
							FROM objects
								WHERE $instanceClause AND object_type = '$objectType' $archivedPhrase
							ORDER BY ". $orderByString ." ".$sortDir ." OFFSET ". $offset ." LIMIT ". $limit .") query_data;"
				);

			}else{

				$statement = $this->db->prepare(
					$selectClause ." FROM objects where $instanceClause and object_type = '$objectType' $archivedPhrase;"
				);

			}

			// $this->debug = true;
			if($statement->execute()){

				$data = $statement->fetchAll();
				return $this->parseData($data, $getChildObjects, $objectType, array(), null, ['mergeDocs' => false]);


			}elseif($this->debug){
				// var_dump($statement->errorInfo());
				return $statement->errorInfo();

			}

		}

		public function getCount($objectType, $whereClause){

			$statement = $this->db->prepare("SELECT count(*) FROM $objectType $whereClause;");

			if($statement->execute()){

				return $statement->fetchAll()[0]['count'];

			}else{

				return $statement->errorInfo();

			}

		}

		public function getCountWhere($objectType, $selectionArr, $additionalClause = ''){

			$selectString = "SELECT COUNT(id) OVER() as full_count FROM objects WHERE instance = '". $this->instance ."' AND object_type = '$objectType' AND ";

			$i=0;
			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i == 0){
// 							$selectString .= "object_data->>'$key' like '". trim($val['value']) ."%'";
							$selectString .= "(object_data->>'$key')::jsonb @> '". trim($val['value']) ."'::jsonb";
						}else{
// 							$selectString .= " and object_data->>'$key' like '". trim($val['value']) ."%'";
							$selectString .= "and (object_data->>'$key')::jsonb @> '". trim($val['value']) ."'::jsonb";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					}

				}else{

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}


			$selectString .= ' '. $additionalClause .";";

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return count($statement->fetchAll());

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}
		}

		public function getDistinct($objectType, $byProperty){

			$statement = $this->db->prepare(
				"SELECT DISTINCT ON (object_data->>'$byProperty') * FROM $objectType;"
			);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), 0, $objectType);

			}else{
// 				var_dump($statement->errorInfo(), $statement);
				return $statement->errorInfo();

			}

		}

		public function getByIdAcrossInstances($objectType = null, $objectId, $childObjs = 0, $includeArchive = false, $instance = null) {

			// Get child objs
			if (!empty($objectType)) {
				$blueprint = $this->getBlueprint($objectType);
				$childObjs = $this->addDependentFieldsToSelectionArr($childObjs, $blueprint);
			}

			// Select clause
			$selectClause = $this->getSelectClause($childObjs, null, false);

			// Instance clause
			$instanceClause = '';
			if ($instance) {
				$instanceClause = " AND instance = '". $instance ."' ";
			}

			// Order by clause
			$orderByClause = '';

			// Archive clause
			$archiveClause = " AND is_deleted = false";
			if ($includeArchive) {
				$archiveClause = ';';
			}

			if( is_array($objectId) ){

				if (count($objectId) === 0) {
					return [];
				}

				// Sort by sort_index
				$orderByClause = ' ORDER BY sort_index::bytea DESC ';

				$objectId = array_values($objectId);
				$valuesList = '';
				foreach($objectId as $i => $oid){

					if( is_numeric($oid) ){

						$valuesList .= '('. intval($oid) .')';
						if( $i + 1 < count($objectId) ){
							$valuesList .= ', ';
						}

					}

				}

				$statement = $this->db->prepare(
					$selectClause ." FROM objects WHERE id = ANY (VALUES  ". $valuesList .")" . $instanceClause . $archiveClause . $orderByClause .';'
				);
// $this->debug = true;
				if ($statement->execute()) {

					$data = $statement->fetchAll();

					if ($objectType == null) {
						$objectType = $data[0]['object_type'];
					}

					return $this->parseData($data, $childObjs, $objectType);

				} else if ($this->debug) {
					var_dump($statement);
					die();
					return $statement;

				}

			} elseif (is_numeric($objectId)) {

				$objectId = intval($objectId);
				$statement = $this->db->prepare(
					$selectClause ." FROM objects WHERE id = :objectId" . $instanceClause . $orderByClause . $archiveClause .';'
				);

				if ($statement->execute([':objectId' => $objectId])) {

					$data = $statement->fetchAll();

					if ($objectType == null) {
						$objectType = $data[0]['object_type'];
					}

					$ret = $this->parseData($data, $childObjs, $objectType)[0];

					return $ret;

				} else if ($this->debug) {

					var_dump($statement, $statement->errorInfo());
					die();
					return $statement;

				}

			} else {

				return false;

			}

		}

		public function getById($objectType = null, $objectId, $childObjs = 0, $includeArchive = false){

			$ret = $this->getByIdAcrossInstances($objectType, $objectId, $childObjs, $includeArchive, $this->instance);

			return $ret;

		}

		//!CLEANUP: Should look at removing this as a security issue (if an app
		// level endpoint using this reads $whereClause off of POST or REQUEST)
		public function getWhere($objectType, $whereClause){

			$statement = $this->db->prepare(
				"SELECT * FROM $objectType $whereClause;"
			);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), 0, $objectType);

			}elseif($this->debug){
// 				var_dump($statement->errorInfo(), $statement);
				return $statement->errorInfo();

			}

		}

		public function getValueAtPath ($rootObjId = 0, $addressString = '', $opts = null) {

			$toks = explode('.', $addressString);
			foreach ($toks as $i => $tok) {
				$toks[$i] = str_replace('-', '.', $tok);
			}

			$select = buildSelectionArr($toks);

			if (!empty($select) && $rootObjId > 0) {

				$obj = $this->getById(
					''
					, $rootObjId
					, $select
				);

				return $this->getValueAtAddressPath(
					$toks
					, $obj
					, $opts
					, null
				);

			}

			return false;

		}

		public function getWith($objectType, $selectionArr, $childObj, $childObjKey, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null, $getJust = array(), $sortCast = 'string'){

			$whereClause = "";
			$i=0;
			$childObjs = 0;

			if($selectionArr['childObjs'] > 0){
				$childObjs = $selectionArr['childObjs'];
				unset($selectionArr['childObjs']);
			}

			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					$whereClause .= "and a.object_data->>'$key' = '". trim($val) ."'";

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						$whereClause .= "and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";

					}

				}else{

					$whereClause .= "and a.object_data->>'$key' = '". trim($val) ."'";

				}
				$i++;
			}

			$selectString =

				"SELECT a.object_data,  array_agg(b.object_data) as child_data ".
				"FROM objects a ".
				"LEFT JOIN objects b ON b.object_type = '$childObj' AND b.object_data->>'$childObjKey' = a.object_data->>'id' WHERE a.instance = '". $this->instance ."' and a.object_type = '". $objectType ."' AND is_deleted = false  $whereClause ".
				"GROUP BY a.object_data";

			if($paged){

				$orderByString = '';

				switch($sortCast){

					case 'int':

						$orderByString = "(a.object_data->>'". $sortCol ."')::int";

						break;

					default:

						$orderByString = "a.object_data->>'". $sortCol ."'";

				}

				$selectString .= " ORDER BY ". $orderByString ." ". $sortDir ." OFFSET ". $offset ." LIMIT ". $limit .";";

			}else{

				$selectString .= ';';

			}

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				$rawData = $statement->fetchAll();
				$ret = [];
				$i=0;

				if(is_array($rawData)){
					foreach($rawData as $rawDatum){

						$ret[$i] = json_decode($rawDatum['object_data'], true);
						$temp = json_decode("[". trim($rawDatum['child_data'], "{}") ."]", true);

						$j=0;
						if(is_array($temp)){
							foreach($temp as $tempData){

								$temp[$j] = json_decode($tempData, true);
								$j++;

							}
						}else{
							$temp = array();
						}

						$ret[$i][$childObj] = $temp;
						$i++;

					}
				}

				return $this->parseData($ret, $childObjs, $objectType, array(), $childObj);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function groupSum(
			$objectType = 'requests'
			, $field = 'books'
			, $dateBy = 'month'
			, $groupBy = 'day'
			, $start
			, $end
			, $offset = 0
			, $limit = 100
			, $search
			, $selectionArr
			, $dateField = 'date_created',
			 $coalesce = false
		){


			$selectString = "";

			if($selectionArr->groupOn){

				$groupOn = $selectionArr->groupOn;
				unset($selectionArr->groupOn);

			}
			if($selectionArr->sumCast){
				switch($selectionArr->sumCast){

					case 'numeric':
					$sumCast = 'NUMERIC';
					break;

					case 'real':
					$sumCast = 'REAL';
					break;

					default:
					$sumCast = 'INT';
					break;

				}
				$sumCast = $selectionArr->sumCast;
				unset($selectionArr->sumCast);
			}else{
				$sumCast = 'INT';
			}

			$selectionArr = json_decode(json_encode($selectionArr), true);
			$selectionArr = $this->adjustTagsForPortal($selectionArr);
			$selectionArr = json_decode(json_encode($selectionArr), true);

			$i=1;
			foreach($selectionArr as $key => $val){

				if($key == 'groupOn'){

				}elseif($key === 'tagged_with'){

					if($i > 0){
						$selectString .= " and ";
					}

					if (is_array($val) and $val['type'] == 'any') {
						$selectString .= "tagged_with && ARRAY[". trim(implode(',', $val['values'])) ."]::integer[]";
					}elseif(is_array($val)){
						$selectString .= "tagged_with @> ARRAY[". trim(implode(',', $val)) ."]";
					}elseif(is_int($val)){
						$selectString .= "tagged_with @> ARRAY[". trim($val) ."]";
					}

				}elseif($key === 'shared_with'){

					if($i > 0){
						$selectString .= " and ";
					}

					if (is_array($val) and $val['type'] == 'any') {
						$selectString .= "shared_with && ARRAY[". trim(implode(',', $val['values'])) ."]::integer[]";
					}elseif(is_array($val)){
						$selectString .= "shared_with @> ARRAY[". trim(implode(',', $val)) ."]";
					}elseif(is_int($val)){
						$selectString .= "shared_with @> ARRAY[". trim($val) ."]";
					}

				}elseif(is_string($val)){

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i == 0){
							$whereClause .= "(a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}else{
							$whereClause .= " and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					}elseif($val['type'] === 'or'){

						if($i == 0){
							$selectString .= '(';
						}else{
							$selectString .= 'AND (';
						}
						foreach($val['values'] as $j => $value){
							if($j > 0){
								$selectString .= ' OR ';
							}
							$selectString .= "object_data->>'$key' = '". trim($value) ."'";
						}

						$selectString .= ')';

					}elseif($val['type'] === 'before'){

						if($i > 0){
							$selectString .= ' AND ';
						}
						$queryDate = date('Y-m-d H:i:s', $val['date']);
						$selectString .= " to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') < to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					}elseif($val['type'] === 'after'){

						if($i > 0){
							$selectString .= ' AND ';
						}

						$queryDate = date('Y-m-d H:i:s', $val['date']);
						$selectString .= " to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') > to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					}elseif($val['type'] === 'not_equal'){

						if($i > 0){
							$selectString .= ' AND ';
						}

						if (is_string($val['value'])) {

							$selectString .= 'NOT object_data @>\'{"'.$key.'":"'. trim($val['value']) .'"}\'';

						} elseif (is_bool($val['value'])) {

							$boolStringVal = ($val['value']) ? 'true' : 'false';
							$selectString .= 'NOT object_data @>\'{"'.$key.'":'. $boolStringVal .'}\'';

						} else {

							$selectString .= 'NOT object_data @>\'{"'.$key.'":'. trim($val['value']) .'}\'';

						}

					}elseif($val['type'] === 'int') {

						if ($i > 0) {
							$selectString .= 'and ';
						}

						$selectString .= "object_data->>'$key' = '". trim($val['value']) ."'";

					}elseif($val['type'] === 'less_than'){

						if($i > 0){
							$selectString .= ' AND ';
						}

						$selectString .= '(object_data->>\''. $key .'\')::REAL < '. trim($val['value']);

					}elseif($val['type'] === 'greater_than'){

						if($i > 0){
							$selectString .= ' AND ';
						}

						$selectString .= '(object_data->>\''. $key .'\')::REAL > '. trim($val['value']);

					}elseif($val['type'] === 'int') {

						if ($i > 0) {
							$selectString .= 'and ';
						}

						$selectString .= "object_data->>'$key' = '". trim($val['value']) ."'";

					}

				}else{

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}

			if (is_object($groupOn)) {
				$groupOn = '((object_data->>\''. $groupOn->num .'\')::int / NULLIF((object_data->>\''. $groupOn->div .'\')::int, 0)) ';
			} elseif ($groupOn === 'tagged_with') {
				$groupOn = 'unnest(tagged_with)';
			} else {
				$groupOn = 'object_data->>\''. $groupOn .'\'';
			}

			$sumAvgString = "SUM (
					 CAST (
						  object_data->>'". $field ."' AS ". $sumCast ."
					 )
				),
				AVG (
					 CAST (
						  object_data->>'". $field ."' AS ". $sumCast ."
					 )
				)";

			if($coalesce === true){
				$sumAvgString = "SUM (
					 (COALESCE (
						case
						 	when (object_data->>'". $field ."') = '' then null
							else (object_data->>'". $field ."')
						end,
						'0'
					)::INT)
				),
				AVG (
					 (COALESCE (
						case
						   when (object_data->>'". $field ."') = '' then null
						  else (object_data->>'". $field ."')
					    end,
					    '0'
				)::INT)
			)";
			}

			$query = "select
				". $groupOn ." AS \"grouped\",
				COUNT(id) as grouped_total,
				". $sumAvgString ."
				FROM objects
				WHERE instance = '". $this->instance ."' AND object_type = '". $objectType ."' AND is_deleted = false and to_timestamp(object_data->>'". $dateField ."', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $start ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $end ."' ". $selectString ."
				GROUP BY grouped ORDER BY grouped asc;";
			// $this->debug = true;
			// var_dump($query);
			// die();

			$statement = $this->db->prepare($query);
			if($statement->execute()){

				$data = $statement->fetchAll();
//var_dump($data);
				// !TODO: If grouping by tags, and filtering by tags, only return the
				// groups for the tags being filtered by.

				return $data;

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function incrementCommentCount ($objectId, $options) {

			// If comment_count null then run sum call otherwise increment by 1;
			$object = $this->getById(null, $objectId);

			// Overall count
			$comments = $this->where(
				'notes', ['type_id' => intval($objectId)], '', 0, true, 0, 'null', 'asc', 1, null
			);

			$update = [
				'id' => $objectId
				, 'comment_count' => $comments[0]['full_count']
			];

			// if there's a field
			if ($options && $options['field']) {

				$fieldCount = $this->countObjs(
					'notes'
					, [
						'type_id' => intval($objectId)
						, 'field' => $options['field']
					]
					, $groupBy = ''
					, $dateField = ''
				);

				if ($fieldCount[0] and $fieldCount[0]['count']) {
					$update[$options['field']] = $fieldCount[0]['count'];
				}

			}

			return $this->update(
				$object['object_bp_type']
				, $update,
				0,
				$update
			);

		}

		public function query($sql){

			$statement = $this->db->prepare($sql);

			if($statement->execute()){

				return $statement->fetchAll();

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function restore($objectType, $objectId){

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			$statement = $this->writeDb->prepare("UPDATE objects SET is_deleted = false WHERE id = ". $objectId ." and instance = '". $this->instance ."' RETURNING *;");

			if($statement->execute()){

				// !TODO: refactor this to an action, store the rule to run
				// that action in the blueprint, and cue off of the blueprint
				// to run actions rather than looking at groups.
				if ($objectType === 'groups') {

					// Get related proposal
					$proposals = $this->where(
						'proposals'
						, [
							'main_object' => $objectId
							, 'archive' => true
						]
					);

					// Get related invoices
					$invoices = $this->where(
						'invoices'
						, [
							'archive' => true
							, 'related_object' => [
								'type' => 'or'
								, 'values' => __::pluck($proposals, 'id')
							]
						]
					);

					// Get related menus
					$menus = $this->where(
						'inventory_menu'
						, [
							'archive' => true
							, 'id' => [
								'type' => 'or'
								, 'values' => __::pluck($proposals, 'menu')
							]
						]
					);

					// Get related pricing breakdown reports
					$priceBreakdowns = $this->where(
						'inventory_menu_pricing_breakdown'
						, [
							'archive' => true
							, 'space' => $objectId
						]
					);

					// Erase dependent data
					$toErase = __::pluck($proposals, 'id');
					$toErase = array_merge($toErase, __::pluck($invoices, 'id'));
					$toErase = array_merge($toErase, __::pluck($menus, 'id'));
					$toErase = array_merge($toErase, __::pluck($priceBreakdowns, 'id'));

					foreach ($toErase as $idToErase) {

						$this->restore('', $idToErase);

					}

					return true;

				} else {

					return true;

				}

			}elseif($this->debug){

				var_dump($statement, $statement->errorInfo(), "UPDATE objects SET is_deleted = false WHERE id = ". $objectId ." RETURNING *;");

				return false;

			}

		}

		public function queue($objectType = 'object', $objectId = 0, $processName = 'queue') {

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			$statement = $this->writeDb->prepare("

			");

// 			var_dump($statement);
			die();

			//$this->debug = true;
			if ($statement->execute()) {

				return $this->parseData($statement->fetchAll(), $getChildObjects, $objectType, $getJust);

			} elseif($this->debug) {

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function update($objectType, $objectData, $childObjs = 0, $forceVals = null, $skipParent = false){

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			if ($objectType === 'entity_type') {
/*
				error_reporting(E_ALL);
				ini_set('display_errors', '1');
				$this->debug = true;
*/
			}

			// if $objectData contains multiple objects, update each
			if(array_keys($objectData) === range(0, count($objectData) - 1)){

				// return data
				$ret = array();
				foreach($objectData as $i => $datum){

					if($datum['object_bp_type']){
						$ret[] = $this->update($datum['object_bp_type'], $datum, $childObjs, $forceVals, $skipParent);
					}else{
						$ret[] = $this->update($objectType, $datum, $childObjs, $forceVals, $skipParent);
					}

				}
				return $ret;

			// update single object
			} else {

				if (!array_key_exists('id', $objectData)) {
					return false;
				}

				$memo = [];

				// Get current object
				$old = $this->getById($objectType, $objectData['id']);

				if ($old === null) {
					return false;
				}

				// Instance clause
				$instanceClause = " AND instance = '". $this->instance ."' ";

				// Tags clause
				$tagText = $this->getIntArrUpdatePhrase($objectData);

				// Parse data
				$obj = $this->parseInput($objectType, $objectData, $old, null, null, null, $memo, $skipParent);

				// special default vals, that work outside of parseInput
				if ($forceVals) {

					if (array_key_exists('comment_count', $forceVals) && is_numeric($forceVals['comment_count'])) {

						$obj['comment_count'] = intval($forceVals['comment_count']);

					}

				}

				$statement = $this->writeDb->prepare("UPDATE objects SET object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."'". $tagText ." WHERE id = ". $objectData['id'] . $instanceClause . "RETURNING *;");

				if ($statement->execute()) {

					$blueprint = $this->getBlueprint($objectType);

					$downStreamUpdates = [];
					if ($memo['__shouldRunRules']) {

						$downStreamUpdates = $this->rules->process(
							$blueprint
							, 'update'
							, $old
							, $obj
							, $this
						);

					}

					$parsedData = $this->parseData($statement->fetchAll(), $childObjs, $objectType)[0];

					if (is_array($downStreamUpdates)) {
						foreach ($downStreamUpdates as $key => $val) {

							if (is_array($val)) {
								foreach ($val as $prop => $change) {

									$parsedData[$prop] = $change;

								}
							}

						}
					}

					// Trigger update events
					// $this->triggerEventsFromBlueprint(
					// 	'update'
					// 	, $old
					// 	, $obj
					// 	, $blueprint
					// );

					return $parsedData;

				} elseif($this->debug) {

					var_dump($statement, $statement->errorInfo(), "UPDATE objects SET object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."'". $tagText ." WHERE id = ". $objectData['id'] . $instanceClause . "RETURNING *;");

					return false;

				}

			}

		}

		public function updateType ($objId = null, $newType = null) {

			if (
				!is_int($objId)
				|| !is_string($newType)
			) {
				return false;
			}

			$statement = $this->db->prepare("
				UPDATE objects
					SET object_type = '". trim($newType) ."'
					WHERE id = ". $objId ."
						AND instance = '". $this->instance ."'
					RETURNING *;"
			);

			if ($statement->execute()) {

				return true;

			} elseif ($this->debug) {

				var_dump(
					$statement
					, $statement->errorInfo()
				);

				return false;

			}

		}

		private function addDependentFieldsToSelectionArr ($selectionArr = [], $blueprint = []) {

			if (is_array($selectionArr)) {

				foreach ($selectionArr as $key => $_) {

					$field = $blueprint[$key];

					if (!empty($field) && isset( $field['fieldType'] ) ) {

						switch ($field['fieldType']) {

							case 'formula':
								foreach ($blueprint as $dependentKey => $dependentField) {

									switch ($dependentField['fieldType']) {

										case 'quantity':
										case 'currency':
										case 'reference-calculation':
											$selectionArr[$dependentKey] = true;
											break;

										case 'state':
											$selectionArr[$dependentKey.'_weight'] = true;
											break;

									}

								}
								break;

						}

					}

				}

			}

			return $selectionArr;

		}

		public function where($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null, $getJust = array(), $sortCast = 'string', $forceLimit = false){
			// Set variables
			$additionalPhrase = '';
			$hostInstance = '';

			// Set instance
			$instance = $this->instance;
			$tmpInstance = $this->instance;

			// Get blueprint
			$blueprint = $this->getBlueprint($objectType);
			$getChildObjects = $this->addDependentFieldsToSelectionArr($getChildObjects, $blueprint);
			$selectClause = $this->getSelectClause($getChildObjects, null, false, $selectionArr);
			// $selectClause = $selectClauses['select'];
			// $joinClause = $selectClauses['join'];

			// Get object phrase
			$objectTypePhrase = $this->getObjectTypePhrase($objectType);

			// Handle is_deleted and archive flags
			$isDeletedPhrase = '';
			if (isset($selectionArr['is_deleted'])) {
				// Convert both boolean and numeric values to true/false
				$isDeleted = filter_var($selectionArr['is_deleted'], FILTER_VALIDATE_BOOLEAN);
				$isDeletedPhrase = 'AND is_deleted = ' . ($isDeleted ? 'true' : 'false');
				unset($selectionArr['is_deleted']);
			} else if (isset($selectionArr['archive'])) {
				$isDeletedPhrase = 'AND ' . $this->getArchivePhrase($selectionArr);
			} else {
				$isDeletedPhrase = 'AND is_deleted = false';
			}

			// Unset tagged with
			if (is_array($selectionArr['tagged_with']) and count($selectionArr['tagged_with']) == 0) {
				unset($selectionArr['tagged_with']);
			}

			// Unset read
			if (is_array($selectionArr['read']) and count($selectionArr['read']) == 0) {
				unset($selectionArr['read']);
			}

			// Unset write
			if (is_array($selectionArr['write']) and count($selectionArr['write']) == 0) {
				unset($selectionArr['write']);
			}

			// Unset archive
			if (array_key_exists('archive', $selectionArr)) {
				unset($selectionArr['archive']);
			}

			// Get additional phrase
			if (count($selectionArr) > 0) {
				$selectArray = $this->getWhereClause($selectionArr);
				$additionalPhrase = 'AND '. $selectArray['selectString'];
				$instance = !empty($selectArray['instance']) ? $selectArray['instance'] : $instance;
			}

			if ($paged) {

				$selectString = $selectClause . ", COUNT(id) OVER() as full_count FROM objects WHERE instance = '" . $instance . "' AND " . $objectTypePhrase . " " . $isDeletedPhrase . " " . $additionalPhrase;

				if ($sum) {

					$selectString = $selectClause . ", SUM(object_data->>'$sum') as object_data->>'$sum', COUNT(id) OVER() as full_count FROM objects WHERE instance = '" . $instance . "' AND " . $objectTypePhrase . " " . $isDeletedPhrase . " " . $additionalPhrase;

				}

			} else {

				$selectString = $selectClause ." FROM objects WHERE instance = '". $instance ."' AND ". $objectTypePhrase ." ". $isDeletedPhrase . " " . $additionalPhrase;

			}

			if ($paged) {

				switch ($sortCast) {

					case 'int':
						$orderByString = "(object_data->>'". $sortCol ."')::int";
						break;

					case 'string':
						// By sort index
						if ($sortCol === 'sortIndex') {

							$orderByString = "sort_index::bytea";

						// Check for fuzzy string distance
						} else if (
							$sortCol === 'DISTANCE'
							&& is_array($selectionArr['search'])
							&& is_string($selectionArr['search']['value'])
							&& $selectionArr['search']['fuzzySearch'] === true
						) {
							$orderByString = "levenshtein('". $selectionArr['search']['value'] ."', COALESCE(object_data->>'name', ''), 1, 10, 10)";
							// $orderByString = $this->getFuzzySearchDistanceSql('name', $selectionArr['search']['value'], 1, 1, 1, true);
							$sortDir = 'ASC';
						} else {
							$orderByString = "COALESCE(object_data->>'". $sortCol ."', '')";
						}
						break;

					default:
						$orderByString = "COALESCE(object_data->>'". $sortCol ."', '')";
						break;

				}

				$selectString .= " ". $additionalClause ." order by ". $orderByString ." ". $sortDir ." offset ". $offset ." limit ". $limit .";";

			}else{

				if ($sortCol !== 'null') {

					switch($sortCast){
						case 'int':
							$orderByString = " ORDER BY (object_data->>'". $sortCol ."')::int";
							break;

						default:
							$orderByString = " ORDER BY COALESCE(object_data->>'". $sortCol ."', '')";
							break;
					}

				}

				if ($forceLimit) {
					$selectString .= ' '. $additionalClause . $orderByString ." LIMIT ". $limit .";";
				} else {
					$selectString .= ' '. $additionalClause . $orderByString .";";
				}

			}

			$statement = $this->db->prepare($selectString);
			$this->instance = $instance;

			try {

				$statement->execute();
				$ret = $this->parseData($statement->fetchAll(), $getChildObjects, $objectType, $getJust);
				$this->instance = $tmpInstance;
				return $ret;

			} catch (Exception $e) {

				return false;

			}

		}

		public function whereAcrossInstances($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null, $getJust = array(), $sortCast = 'string', $instance = null) {

			// Instance clause
			$instanceClause = '';
			if ($instance) {
				$instanceClause = " AND instance = '". $instance ."' ";
			}

			$selectClause = $this->getSelectClause($getChildObjects, null, false);
			$objectTypePhrase = $this->getObjectTypePhrase($objectType);
			if($paged){

				$selectString = $selectClause .", COUNT(id) OVER() as full_count FROM objects WHERE ". $objectTypePhrase ." AND is_deleted = false and ";

				if($sum){

					$selectString = $selectClause .", SUM(object_data->>'$sum') as object_data->>'$sum', COUNT(id) OVER() as full_count FROM objects WHERE ". $objectTypePhrase ." AND is_deleted = false and ";

				}

			}else{

				$selectString = $selectClause ." FROM objects WHERE ". $objectTypePhrase ." AND is_deleted = false and ";

			}

			$i=0;
			foreach($selectionArr as $key => $val){

				if($key != 'childObjs'){

					if($i > 0){
						$selectString .= " AND ";
					}

					if(is_string($val)){

						$selectString .= 'object_data @>\'{"'.$key.'":"'. trim($val) .'"}\'';
						//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

					}elseif(is_array($val)){

						if($val['type'] === 'contains'){

							if(is_array($val['value']) and $val['value']['type'] === 'or'){

								$selectString .= '(';
								foreach($val['value']['values'] as $j => $value){
									if($j > 0){
										$selectString .= ' OR ';
									}

									if(is_int($value) and ($key != 'zip' or $key != 'tracking_number')){
										$selectString .= "(object_data->'$key')::jsonb @> '[" .$value."]'";
									}else{
										$selectString .= "(object_data->'$key')::jsonb @> '[\"" .$value."\"]'";
									}

								}

								$selectString .= ')';

							}elseif($val['value'] == intval($val['value']) and intval($val['value']) > 0 and $key != 'zip'){
								$selectString .= "(object_data->'$key')::jsonb @> '[" .$val['value']."]'";
							}else{
								$selectString .= "object_data->>'$key' ILIKE '%". trim($val['value']) ."%'";
							}

						}elseif($val['type'] === 'contains-id'){

							$selectString .= "(object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";

						}elseif($val['type'] === 'between'){

							$startTime = date('Y-m-d H:i:s', $val['start']);
							$endTime = date('Y-m-d H:i:s', $val['end']);

							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";

						}elseif($val['type'] === 'before'){

							$queryDate = date('Y-m-d H:i:s', $val['date']);
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') < to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

						}elseif($val['type'] === 'after'){

							$queryDate = date('Y-m-d H:i:s', $val['date']);
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') > to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

						}elseif($val['type'] === 'or'){

							$selectString .= '(';
							foreach($val['values'] as $j => $value){
								if($j > 0){
									$selectString .= ' OR ';
								}
								$selectString .= "object_data->>'$key' = '". trim($value) ."'";
							}

							$selectString .= ')';

						}elseif($val['type'] === 'less_than'){

							// !TODO: cast as int and compare
							$selectString .= '(object_data->>\''. $key .'\')::REAL < '. trim($val['value']);

						}elseif($val['type'] === 'greater_than'){

							$selectString .= '(object_data->>\''. $key .'\')::REAL > '. trim($val['value']);

						}

					}else{

						$selectString .= 'object_data @>\'{"'.$key.'":'. trim($val) .'}\'';
						//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

					}

				}
				$i++;
			}

			if($paged){

				switch($sortCast){

					case 'int':

						$orderByString = "(object_data->>'". $sortCol ."')::int";

						break;

					default:

						$orderByString = "object_data->>'". $sortCol ."'";

				}

				$selectString .= " ". $additionalClause ." order by ". $orderByString ." ". $sortDir ." offset ". $offset ." limit ". $limit .";";

			}else{

				$selectString .= ' '. $additionalClause .";";

			}

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), $getChildObjects, $objectType, $getJust);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function whereAll($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null){

			if($paged){

				$selectString = "SELECT *, COUNT(id) OVER() as full_count FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";

				if($sum){
					$selectString = "SELECT *, SUM($sum) as $sum, COUNT(id) OVER() as full_count FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";
				}

			}else{

				$selectString = "SELECT * FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";

			}

			$i=0;
			foreach ($selectionArr as $key => $val) {

				if(is_string($val)){

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				} elseif (is_bool($val)) {

					if($i == 0){
						$selectString .= "(object_data->>'$key')::boolean = ". ($val ? 'true' : 'false') ."";
					}else{
						$selectString .= " and (object_data->>'$key')::boolean = ". ($val ? 'true' : 'false') ."";
					}

				} elseif(is_array($val)){

					if ($val['type'] === 'contains') {

						if($i == 0){
							$whereClause .= "(a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}else{
							$whereClause .= " and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}

					} elseif($val['type'] === 'between') {

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					} elseif ($val['type'] === 'or') {

						if ($i > 0) {
							$selectString .= ' AND ';
						}

						$selectString .= '(';
						foreach($val['values'] as $j => $value){

							if($j > 0){
								$selectString .= ' OR ';
							}

							$selectString .= "object_data->>'$key' = '". trim($value) ."'";

						}

						$selectString .= ')';

					}

				} else {

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}

				$i++;

			}

			if ($paged) {

				if($_POST->queryObj->paged->pageLength){

					$limit = $_POST->queryObj->paged->pageLength;

				}

				$selectString .= " ". $additionalClause ." order by object_data->>'". $sortCol ."' ". $sortDir ." offset ". $offset ." limit ". $limit .";";

			} else {

				$selectString .= ' '. $additionalClause .";";

			}

// 			$this->debug = true;
			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), $getChildObjects, $objectType);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function whereAllCaseInsensitive($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null){

			if($paged){

				$selectString = "SELECT *, COUNT(id) OVER() as full_count FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";

				if($sum){
					$selectString = "SELECT *, SUM($sum) as $sum, COUNT(id) OVER() as full_count FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";
				}

			}else{

				$selectString = "SELECT * FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";

			}

			$i=0;
			foreach ($selectionArr as $key => $val) {

				if(is_string($val)){

					if($i == 0){
						$selectString .= "LOWER(object_data->>'$key') = LOWER('". trim($val) ."')";
					}else{
						$selectString .= " and LOWER(object_data->>'$key') = LOWER('". trim($val) ."')";
					}

				} elseif (is_bool($val)) {

					if($i == 0){
						$selectString .= "(object_data->>'$key')::boolean = ". ($val ? 'true' : 'false') ."";
					}else{
						$selectString .= " and (object_data->>'$key')::boolean = ". ($val ? 'true' : 'false') ."";
					}

				} elseif(is_array($val)){

					if ($val['type'] === 'contains') {

						if($i == 0){
							$whereClause .= "(a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}else{
							$whereClause .= " and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}

					} elseif($val['type'] === 'between') {

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					} elseif ($val['type'] === 'or') {

						if ($i > 0) {
							$selectString .= ' AND ';
						}

						$selectString .= '(';
						foreach($val['values'] as $j => $value){

							if($j > 0){
								$selectString .= ' OR ';
							}

							$selectString .= "object_data->>'$key' = '". trim($value) ."'";

						}

						$selectString .= ')';

					}

				} else {

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}

				$i++;

			}

			if ($paged) {

				if($_POST->queryObj->paged->pageLength){

					$limit = $_POST->queryObj->paged->pageLength;

				}

				$selectString .= " ". $additionalClause ." order by object_data->>'". $sortCol ."' ". $sortDir ." offset ". $offset ." limit ". $limit .";";

			} else {

				$selectString .= ' '. $additionalClause .";";

			}

// 			$this->debug = true;
			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), $getChildObjects, $objectType);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function whereBy($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null){

			if($paged){

				$selectString = "SELECT json_agg(object_data) as object_data, COUNT(id) OVER() as full_count FROM objects WHERE instance = '". $this->instance ."' AND object_type = '$objectType' AND is_deleted = false AND ";

				if($sum){
					$selectString = "SELECT json_agg(object_data) as object_data, SUM(object_data->>'$sum') as object_data->>'$sum', COUNT(id) OVER() as full_count FROM objects WHERE instance = '". $this->instance ."' AND object_type = '$objectType' AND is_deleted = false AND ";
				}

			}else{

				$selectString = "SELECT json_agg(object_data) as object_data FROM objects WHERE instance = '". $this->instance ."' AND object_type = '$objectType' AND is_deleted = false ";

			}

			$i=0;
			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i == 0){
							$selectString .= "object_data->>'$key' like '". trim($val['value']) ."%'";
						}else{
							$selectString .= " and object_data->>'$key' like '". trim($val['value']) ."%'";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					}

				}else{

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}

			if($paged){

				$selectString .= " ". $additionalClause ." order by object_data->>'". $sortCol ."' ". $sortDir ." offset ". $offset ." limit ". $limit .";";
//echo $selectString;
			}else{

				$selectString .= ' '. $additionalClause .";";

			}

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return $statement->fetchAll();

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function getAllBlueprints(){

			$blueprints = [];
			$dir = new DirectoryIterator($this->bpDIR);
			$blueprint_temp = [];

			foreach ($dir as $fileinfo) {

				if (!$fileinfo->isDot()) {

					$blueprint_temp = json_decode( file_get_contents($this->bpDIR .'/'. $fileinfo->getFilename()), true);

					$blueprint_temp['blueprint'] = $this->applyBPDefaultProperties($blueprint_temp['blueprint']);

					array_push(
						$blueprints
						, $blueprint_temp
					);

				}

			}

			return $blueprints;

		}

		public function getBlueprintAcrossInstances($objectType, $getOptions = false, $getSetObj = false, $applyWorkflowStates = false, $instance = null) {

			// If the blueprint already exists in the cache, use it
			if (isset($objectType, $this->blueprints)) {
				if ($this->blueprints[$objectType]['getOptions'] === $getOptions
					&& $this->blueprints[$objectType]['getSetObj'] === $getSetObj
					&& $this->blueprints[$objectType]['applyWorkflowStates'] === $applyWorkflowStates
					&& $this->blueprints[$objectType]['instance'] === $instance
				) {
					return $this->blueprints[$objectType]['blueprint'];
				}
			}

			// Instance clause
			$instanceClause = '';
			if ($instance) {
				$instanceClause = " AND instance = '". $instance ."' ";
			}

			// Meta-blueprints, that coalesce other types together
			switch ($objectType) {

				case '#Action Items':
					return [
                        'name' => 'Action Items'
                        , 'blueprint' => [
                            'name' => [
                                'name' =>           'Name'
                                , 'type' =>         'string'
                                , 'immutable' =>    false
							]
							, 'status' => [
								'name' => 			'Status'
								, 'type' => 		'string'
								, 'immutable' => 	false
								, 'fieldType' => 	'status'
							]
                        ]
                    ];

			}

			// custom entity blueprints, stored in the db
			if (substr($objectType, 0, 1) === '#') {

				$blueprints = $this->whereAcrossInstances(
					'entity_type'
					, [
						'bp_name' => substr($objectType, 1)
					]
					, ''
					, 0
					, false
					, 0
					, null
					, 'asc'
					, 100
					, null
					, array()
					, 'string'
					, $instance
				);

				// If two blueprints with the same name are found
				// use the one for the current instance
				if (count($blueprints) > 1) {

					foreach($blueprints as $blueprint) {

						if ($blueprint['instance'] == $instance) {

							$thisBlueprint = $blueprint;

						}

					}

				// Otherwise, just use the first blueprint found
				} else {

					$thisBlueprint = $blueprints[0];

				}

				// Set the blueprint to the found blueprint
				$blueprint = $thisBlueprint;

				$isShared = false;

				if (!empty($blueprint) && $instance <> $blueprint['instance']) {

					if (in_array($instance, $blueprint['shared_with_instances'])) {
						$isShared = true;
					}

					if (!$isShared) {

						// Check to see if this blueprint is shared
						$dataFilter = $this->whereAcrossInstances(
							'data_filter'
							, [
								'object_type' => $objectType,
								'shared_with_instances' => array(
									'type' => 'contains',
									'value' => $instance
								)
							]
						);

						if (!empty($dataFilter)) {
							$isShared = true;
						}

					}

					if (!$isShared) {
						return array();
					}

				}

				$blueprint = $this->applyInheritedProperties($blueprint);
				if (!empty($dataFilter) || $applyWorkflowStates) {
					$blueprint = $this->applyWorkflowStates($blueprint);
				}

				$fields = $this->applyBPDefaultProperties($blueprint['blueprint'], true);

				if ($getSetObj === true) {

					$blueprint['blueprint'] = $fields;

					// Store the blueprint in cache to use again later
					$this->blueprints[$objectType] = array(
						'getOptions' => $getOptions,
						'getSetObj' => $getSetObj,
						'applyWorkflowStates' => $applyWorkflowStates,
						'instance' => $instance,
						'blueprint' => $blueprint
					);

					return $blueprint;

				} else {

					// Store the blueprint in cache to use again later
					$this->blueprints[$objectType] = array(
						'getOptions' => $getOptions,
						'getSetObj' => $getSetObj,
						'applyWorkflowStates' => $applyWorkflowStates,
						'instance' => $instance,
						'blueprint' => $fields
					);

					return $fields;

				}


			}

			// standard blueprints
			if (file_exists($this->bpDIR .'/'. $objectType .'.json')) {

				$blueprint = json_decode(
					file_get_contents($this->bpDIR .'/'. $objectType .'.json')
					, true
				)['blueprint'];

			}

			if ($blueprint) {

				$blueprint = $this->applyBPDefaultProperties($blueprint);
				return $blueprint;

			} else {

				return false;

			}

		}

		public function getBlueprint($objectType, $getOptions = false, $getSetObj = false, $applyWorkflowStates = false) {

			$ret = $this->getBlueprintAcrossInstances($objectType, $getOptions, $getSetObj, $applyWorkflowStates, $this->instance);

			return $ret;

		}

		public function runSteps ($objId = 0, $steps = [], $verbose = false, $trigger = []) {

			if (
				is_array($objId)
				&& is_int($objId['id'])
			) {

				$obj = $objId;

			} elseif (is_int($objId)) {

				$obj = $this->getById('', $objId);

			}

			return $this->rules->runSteps (
				$obj
				, $steps
				, $this
				, $verbose
				, $trigger
			);

		}

		public function simpleSum($objectType, $field, $selectionArr) {

			// Set variables
			$additionalPhrase = '';
			$hostInstance = '';
			$isDeletedPhrase = '';
			$sumCast = 'REAL';

			// Set instance
			$instance = $this->instance;

			// Get object phrase
			$objectTypePhrase = $this->getObjectTypePhrase($objectType);

			// Toggled archived/deleted
			if ($selectionArr['is_deleted']) {
				$isDeletedPhrase = 'AND is_deleted = true';
				unset($selectionArr['is_deleted']);
			} else {
				$isDeletedPhrase = 'AND is_deleted = false';
			}

			// Unset tagged_with
			if (is_array($selectionArr['tagged_with']) and count($selectionArr['tagged_with']) == 0) {
				unset($selectionArr['tagged_with']);
			}

			// Unset read
			if (is_array($selectionArr['read']) and count($selectionArr['read']) == 0) {
				unset($selectionArr['read']);
			}

			// Unset write
			if (is_array($selectionArr['write']) and count($selectionArr['write']) == 0) {
				unset($selectionArr['write']);
			}

			// Unset archive
			if (array_key_exists('archive', $selectionArr)) {
				unset($selectionArr['archive']);
			}

			// Get additional phrase
			if (count($selectionArr) > 0) {
				$selectArray = $this->getWhereClause($selectionArr);
				$additionalPhrase = 'AND '. $selectArray['selectString'];
				$instance = !empty($selectArray['instance']) ? $selectArray['instance'] : $instance;
			}

			$selectString = "SELECT
								SUM (
									CAST (
										COALESCE((cast(nullif(object_data->>'". $field ."', '') AS int)), 0) AS ". $sumCast ."
									)
								)
								FROM objects
							WHERE instance = '". $instance ."' AND ". $objectTypePhrase . " " . $isDeletedPhrase . " " . $additionalPhrase;

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				$data = $statement->fetchAll();
				return intval($data[0]['sum']);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function sum($objectType = 'requests', $field = 'books', $dateBy = 'month', $groupBy = 'day', $start, $end, $offset = 0, $limit = 100, $search, $selectionArr, $dateField = 'date_created'){

			$selectString = "";

			if($selectionArr['groupOn']){
				$groupOn = $selectionArr['groupOn'];
				unset($selectionArr['groupOn']);

			}

			$sumCast = 'INT';
			if (array_key_exists('sumCast', $selectionArr)) {

				switch($selectionArr['sumCast']){

					case 'REAL':
						$sumCast = 'REAL';
						break;

					default:
						$sumCast = 'INT';
						break;

				}

				unset($selectionArr['sumCast']);

			}

			$i=1;
			foreach($selectionArr as $key => $val){

				if($key === 'tagged_with'){

					if ($i != 0) {
						$selectString .= ' and ';
					}

					if (is_array($val) and $val['type'] == 'any') {
						$selectString .= "tagged_with && ARRAY[". trim(implode(',', $val['values'])) ."]::integer[]";
					}elseif(is_array($val)){
						$selectString .= "tagged_with @> ARRAY[". trim(implode(',', $val)) ."]";
					}elseif(is_int($val)){
						$selectString .= "tagged_with @> ARRAY[". trim($val) ."]";
					}

				} elseif(is_string($val)){

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i > 0){
							$selectString .= " and ";
						}

						if(is_array($val['value']) and $val['value']['type'] === 'or'){

							$selectString .= '(';
							foreach($val['value']['values'] as $j => $value){
								if($j > 0){
									$selectString .= ' OR ';
								}

								if(is_numeric($value) and ($key != 'zip' or $key != 'tracking_number')){
									$selectString .= "(object_data->'$key')::jsonb @> '[" .$value."]'";
								}else{
									$selectString .= "(object_data->'$key')::jsonb @> '[\"" .$value."\"]'";
								}

							}

							$selectString .= ')';

						}elseif($val['value'] === intval($val['value']) and intval($val['value']) > 0 and $key != 'zip'){
							$selectString .= "(object_data->'$key')::jsonb @> '[" .$val['value']."]'";
						}else{
							$selectString .= "object_data->>'$key' ILIKE '%". trim($val['value']) ."%'";
						}
// 						echo $selectString;

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					}elseif($val['type'] === 'or'){

						if($i == 0){
							$selectString .= '(';
						}else{
							$selectString .= 'AND (';
						}
						foreach($val['values'] as $j => $value){
							if($j > 0){
								$selectString .= ' OR ';
							}
							$selectString .= "object_data->>'$key' = '". trim($value) ."'";
						}

						$selectString .= ')';

					}

				}else{

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}

			if($groupOn){

				if(is_array($groupOn)){

					$query = "select
						object_data->'". $groupOn['prop'] ."'->>'". $groupOn['childProp'] ."' AS \"grouped\",
						COUNT(id) as grouped_total,
						SUM (
						     CAST (
						          object_data->>'". $field ."' AS ". $sumCast ."
						     )
						),
						AVG (
						     CAST (
						          object_data->>'". $field ."' AS ". $sumCast ."
						     )
						)
						FROM objects
						WHERE instance = '". $this->instance ."' AND object_type = '". $objectType ."' AND is_deleted = false and to_timestamp(object_data->>'". $dateField ."', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $start ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $end ."' ". $selectString ."
						GROUP BY grouped ORDER BY grouped asc;";

				}else{

					$query = "select
						object_data->>'". $groupOn ."' AS \"grouped\",
						COUNT(id) as grouped_total,
						SUM (
						     CAST (
						          object_data->>'". $field ."' AS ". $sumCast ."
						     )
						),
						AVG (
						     CAST (
						          object_data->>'". $field ."' AS ". $sumCast ."
						     )
						)
						FROM objects
						WHERE instance = '". $this->instance ."' AND object_type = '". $objectType ."' AND is_deleted = false and to_timestamp(object_data->>'". $dateField ."', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $start ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $end ."' ". $selectString ."
						GROUP BY grouped ORDER BY grouped asc;";

				}

			}else{

				$query = "select
					date_trunc('". $groupBy ."', CAST(object_data->>'". $dateField ."' AS date)) AS \"grouped\",
					COUNT(id) as grouped_total,
					SUM (
					     CAST (
					          object_data->>'". $field ."' AS ". $sumCast ."
					     )
					),
					AVG (
					     CAST (
					          object_data->>'". $field ."' AS ". $sumCast ."
					     )
					)
					FROM objects
					WHERE instance = '". $this->instance ."' AND object_type = '". $objectType ."' AND is_deleted = false and to_timestamp(object_data->>'". $dateField ."', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $start ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $end ."' ". $selectString ."
					GROUP BY grouped ORDER BY grouped asc;";

			}

			$statement = $this->db->prepare($query);

			if($statement->execute()){

				$data = $statement->fetchAll();
				return $data;

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		private function triggerCreateActions ($obj = null) {

            $objectType = $obj['object_bp_type'];
            $blueprint = $this->getBlueprint($objectType);

			if (substr($objectType, 0, 1) === '#') {

                if (is_array($blueprint)) {

                    foreach ($blueprint as $key => $field) {

                        if ( $field['is_archived'] === true) {
                            break;
                        }

						if (
							array_key_exists('fieldType', $field)
							&& $field['fieldType'] === 'state'
							&& is_numeric($field['workflow'])

						) {

								$workflow = $this->getById(
									'entity_workflow'
									, $field['workflow']
                                );

                                $this->triggerStateActions(
								$obj
                                , $workflow
								, $obj[$key]
								, 0
								, $key
							);

						}

					}

				}

            } elseif ($objectType === 'groups') {

				switch ($obj['group_type']) {

					case 'Project':
					case 'Task':
						$this->triggerStateActions(
							$obj
							, $obj['type']
							, $obj['state']
						);
						break;

				}

			} elseif ($objectType === 'contacts') {

				$this->triggerStateActions(
					$obj
					, $obj['type']
					, $obj['state']
				);

			}


			return $obj;

		}

		// !TODO: This is not yet functional, but should be used to pull field/blueprint logic out of the rules sys
		// when figuring out what actions to run when an event occurs to an entity.
		private function triggerEventsFromBlueprint (
			$triggerType // Currently 'update', but eventually 'create', 'archive', etc.
			, $old
			, $new
			, $blueprint
		) {
echo 'testing';
return;
			$actionsToRun = [];
			if (is_array($blueprint)) {
				foreach ($blueprint as $fieldKey => $field) {

					if (is_array($field['on'])) {

						switch ($triggerType) {

							case 'update':

								if (
									is_array($field['on']['update'])
									&& $old[$fieldKey] !== $new[$fieldKey]
								) {

									echo $field['name'] .' changed and has a rule set.<br />';
									array_push(
										$actionsToRun
										, [
											'field' => 		$fieldKey
											, $actions => 	$$field['on']['update']
										]
									);
									// $this->runSteps(
									// 	$new
									// 	, $field['on']['update']
									// 	, true
									// 	, $trigger
									// );

								}
								break;

						}

					}

				}
			}
			var_dump($actionsToRun);
			var_dump('die');
			// var_dump($blueprint);
			die();

		}

		public function triggerStateActions (
			$objectId = 		0
			, $workflowObj = 	[]
			, $newState = 		0
			, $prevState = 		0
			, $statusField = 	'state'
		) {
			// Build trigger meta-data
			$trigger = [
				'type' => 		'state-transition'
				, 'from' => 	[
					'id' => 	$prevState
					, 'name' => ''
				]
				, 'to' => 		[
					'id' => 	$newState
					, 'name' => ''
				]
				, 'state-field' => $statusField
			];

			if (is_array($workflowObj) && $workflowObj['states']) {
				foreach ($workflowObj['states'] as $state) {

					if ($state['uid'] === $newState) {
						$trigger['to']['name'] = $state['name'];
					}
					if ($state['uid'] === $prevState) {
						$trigger['from']['name'] = $state['name'];
					}

				}
			}

			// Get relevant event types.
			$eventTypes = $this->where(
				'event_type'
				, [
					'object' => $workflowObj['id']
					, 'state' => $newState
				]
			);
			$toRun = [];
			$decisions = [];

			// Divvy between actions to run and actions to create
			// notifications/decisions for.
			if (is_array($eventTypes)) {
				foreach ($eventTypes as $eventType) {

					if (
						$eventType['requires_input']
					) {

						array_push(
							$decisions
							, $eventType
						);

					} else {

						array_push(
							$toRun
							, $eventType
						);

					}

				}
			}

			// Clear
			$eventTypes = null;

			// Get relevant condition-led chains that should be triggered by
			// this workflow state change.
			$checks = [];
			$conditions = $this->where(
				'condition'
				, [
					'object' => $workflowObj['id']
					, 'state' => $newState
					, 'is_after_state_change' => true
				]
			);

			// Action/condition chains that should be triggered by this
			// state change.
			$toRun = array_merge(
				$toRun
				, $conditions
			);
			if (
				!empty($toRun)
				|| !empty($decisions)
			) {
				// Trigger automatic events
				$ret = $this->runSteps(
					$objectId
					, $toRun
					, true
					, $trigger
				);

				return [
						'msg' => 				$ret
						, '_notify_usr' => 		true
						, 'updated' => 			$ret['updated']
					];

			} else {

				return false;

			}

		}

		// DOCUMENTS STORE
		private function getDocument (
			$docId = 0
		) {

			$statement = $this->docsDb->prepare(
				"SELECT
					content
				FROM documents
				WHERE
					id = :id
					AND instance = :instance"
			);

			// $this->debug = true;

			if ($statement->execute(array(
				':id' => intval($docId),
				':instance' => $this->instance
			))) {
				$response = $statement->fetchAll()[0]['content'];
				return $response;

			} elseif ($this->debug) {

				return [
					'err' => $this->docsDb->errorInfo()
					, 'statement' => $statement
				] ;

			}

		}

		private function setDocument (
			$parent = null
			, $prop = ''
			, $doc = ''
		) {

			if($this->docsWriteDb == null){
				$this->docsWriteDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DOCS_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_DOCS_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			// Don't allow setting doc vals to null/empty, just find the
			// document that fits if it exists.
			if (
				!is_string($doc)
				or strlen($doc) < 2
			) {

				$statement = $this->docsWriteDb->prepare(
					"SELECT id FROM documents
						WHERE
							instance = :instance
							AND object_id = :objectId
							AND property = :property;"
				);

				$this->docsWriteDb->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
				$statement->execute(array(
					':instance' => $this->instance,
					':objectId' => $parent['id'],
					':property' => $prop
				));

			} else {

				// If docfield has id val, update
				// otherwise create.
				$statement = $this->docsWriteDb->prepare(
					"UPDATE documents SET
							content = :content
						WHERE
							instance = :instance
							AND object_id = :objectId
							AND property = :property
						RETURNING id;"
				);

				$this->docsWriteDb->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
				$statement->execute(array(
					':content' => trim($doc, "'"),
					':instance' => $this->instance,
					':objectId' => $parent['id'],
					':property' => $prop
				));

			}

			$result = $statement->fetchAll();

			if (
				count($result) > 0
			) {

				return intval($result[0]['id']);

			} else {

				$statement = $this->docsWriteDb->prepare (
					"INSERT INTO documents
						(
							instance
							, object_type
							, object_id
							, property
							, content
						) VALUES (
							'". $this->instance ."'
							, '". $parent['object_bp_type'] ."'
							, ". $parent['id'] ."
							, '". $prop ."'
							, :content
						) RETURNING *;"
					) ;

				if (
					$statement->execute(
						[
							':content' => trim($doc, "'")
						]
					)
				) {

					return intval($statement->fetchAll()[0]['id']);

				}

			}

		}

		private function archiveDocument () {}

		// TAG ENDPOINTS
		private function updateTagList($objectId = 0, $tag = 0, $updateType = 'add', $column = 'tagged_with', $allTags = false){

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			if( $objectId == 0 ){
				return false;
			}
			if( $tag == 0 ){
				return false;
			}

			$updateFunc = '';
			switch($updateType){

				case 'add':
				$updateFunc = "array_append(". $column .", ". intval($tag) .")";
				break;

				case 'remove':
				$updateFunc = "array_remove(". $column .", ". intval($tag) .")";
				break;

				case 'set':
				$updateFunc = "'{". implode(', ', $tag) ."}'";
				break;

				default:
				return false;
				break;

			}

			// update in db
			switch ($column) {

				// !for now, update in write_obj column too
				case 'tagged_with':
					$queryTxt = "UPDATE objects SET ". $column ." = ARRAY(SELECT DISTINCT UNNEST(". $updateFunc ."::int[]))";
					break;

				default:
					$queryTxt = "UPDATE objects SET ". $column ." = ARRAY(SELECT DISTINCT UNNEST(". $updateFunc ."::int[]))";
					break;

			}

			if(is_array($objectId)){

				$valuesList = '';
				foreach($objectId as $i => $oid){

					if( is_numeric($oid) ){

						$valuesList .= '('. intval($oid) .')';
						if( $i + 1 < count($objectId) ){
							$valuesList .= ', ';
						}

					}

				}

				$statement = $this->writeDb->prepare(
					$queryTxt ." WHERE id = ANY (VALUES  ". $valuesList .") AND instance = '". $this->instance ."' RETURNING *;"
				);

			}elseif(is_int($objectId)){

				$statement = $this->writeDb->prepare(
					$queryTxt ." WHERE id = ". $objectId ." AND instance = '". $this->instance ."' RETURNING *;"
				);

			}else{

				return false;

			}

			// execute and return
			if($statement->execute()){

				// if working with multiple objects, just return the intersection
				if (is_array($objectId)) {

					$raw = $statement->fetchAll();
					$retTags = $this->parseTagListFromDB($raw[0]['tagged_with']);
					foreach($raw as $i => $obj){
						$retTags = array_intersect($retTags, $this->parseTagListFromDB($obj['tagged_with']));
					}

					return array_values($retTags);

				} else {

					if ($allTags) {

						$raw = $statement->fetchAll()[0];

						return [
							'tagged_with' => $this->parseTagListFromDB($raw['tagged_with'], 1),
							'shared_with' => $this->parseTagListFromDB($raw['shared_with'], 1)
						];

					} else {

						return $this->parseTagListFromDB(
							$statement->fetchAll()[0]['tagged_with']
						);

					}

				}

			}else{

				return false;

			}

		}

		private function parseObjectTaggedWith($tags){

			$tagsArray = [];

			foreach($tags as $i => $tagId){

				if(is_numeric($tagId)){

					array_push($tagsArray, intval($tagId));

				}elseif(is_array($tagId) and array_key_exists('id', $tagId) and intval($tagId['id']) > 0){

					array_push($tagsArray, intval($tagId['id']));
				}

			}

			return $tagsArray;

		}

		private function parseTagListFromDB($entry = null){

			if( is_null($entry) ){
				return [];
			}

			if( strlen( substr($entry, 1, -1) ) === 0 ){
				return [];
			}

			return array_map('intval', explode(',', substr($entry, 1, -1)));

		}

		public function setTags ($object = null, $tag = 0, $column = 'tagged_with') {

			return $this->updateTagList(
				$object
				, $tag
				, 'set'
				, $column
			);

		}

		public function tagObject($object = null, $tag = 0, $column = 'tagged_with', $allTags = false){

			return $this->updateTagList($object, $tag, 'add', $column, $allTags);

		}

		public function tagObjectType($objType = null, $tagWith = null){

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}

			if ($objType == null) {
				return false;
			}

			$tagsSql = '';
			if (is_array($tagWith)) {

				foreach ($tagWith as $i => $tag) {

					if( is_numeric($tag) ){

						$tagsSql .= '('. intval($tag) .')';
						if( $i + 1 < count($tagWith) ){
							$tagsSql .= ', ';
						}

					}

				}

			} elseif (is_string($tagWith)) {

				$tagsSql = "((object_data->>'". $tagWith ."')::integer)";

			} else {

				return false;

			}

			$queryTxt = "UPDATE objects SET tagged_with = array_append(tagged_with, ". $tagsSql ."), write_obj = array_append(write_obj, ". $tagsSql .")";

			$statement = $this->writeDb->prepare(
				$queryTxt ." WHERE object_type = '". $objType ."' AND instance = '". $this->instance ."' RETURNING *;"
			);

			// execute and return
			if($statement->execute()){

				return true;

			}elseif($this->debug){

				return $statement->errorInfo();

			}else{
				return false;
			}

		}

		public function removeTagFromObject($object = null, $tag = 0, $column = 'tagged_with'){

			return $this->updateTagList($object, $tag, 'remove', $column);

		}

		// PRIVATE METHODS
		private function decrypt_property($property = [], $options = []){
// 			var_dump($this->instance);
// 			var_dump($this->currentInstance($this->instance));
			$method = 'AES-128-CBC';
// 			$key = $this->instance['key'];
			//!TESTING
			$key = 1241241;

			// check if user has read privileges
			$canRead = true;
			if($canRead){

				return openssl_decrypt($property['v'], $method, $key, OPENSSL_ZERO_PADDING, $property['iv']);

			}

			return null;

		}

		private function encrypt_property($value = '', $options = []){

			$method = 'AES-128-CBC';
// 			$key = $this->instance['key'];
			// 16 byes
// 			$iv = random_bytes(16);
			$iv = random_int(0, 0xffff);

			//!TESTING
			$key = 1241241;

			$encrypted = openssl_encrypt($value, $method, $key, OPENSSL_ZERO_PADDING, $iv);

			return [
				'v' => $encrypted,
				'iv' => $iv
			];

		}

		public function moveSortOrder ($objId = 0, $pos = 'between', $beforeIndex = 0, $afterIndex = 0, string $objectType) {

			if($this->writeDb == null){
				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);
			}
// var_dump($pos, $beforeIndex);
// die();
			switch ($pos) {

				case 'between':
					$newIndex = $this->fieldTypes['sortIndex']['setBetween'](
						$beforeIndex
						, $afterIndex
					);
					break;

				case 'before':
					$newIndex = $this->fieldTypes['sortIndex']['setBefore'](
						$beforeIndex
					);
					break;

				case 'after':
					$newIndex = $this->fieldTypes['sortIndex']['setAfter'](
						$beforeIndex
					);
					break;

				case 'to-top':
					$newIndex = $this->fieldTypes['sortIndex']['setNext'](['object_bp_type' => $objectType], 'sort_index', [], $this, 'top');
					break;

				case 'to-bottom':
					$newIndex = $this->fieldTypes['sortIndex']['setNext'](['object_bp_type' => $objectType], 'sort_index', [], $this, 'bottom');
					break;

			}
// var_dump(
// 	$beforeIndex
// 	, $afterIndex
// 	, $newIndex
// );
// die();
			if (is_string($newIndex)) {
				// error_reporting(E_ALL);
	// ini_set('display_errors', '1');
				$statement = $this->writeDb->prepare(
					$queryTxt ."UPDATE objects SET sort_index = '". $newIndex ."' WHERE id = ". $objId ." AND instance = '". $this->instance ."' RETURNING *;"
				);
				// var_dump($statement);
				// die();
// $this->debug = true;
				if ($statement->execute()) {

					$info = $statement->fetchAll()[0];
					// var_dump($info);
					// die();
					return $info;

				} elseif ($this->debug) {

					var_dump($statement->errorInfo());
					die();

				} else {

					return false;

				}

			}

		}

		private function new_instance_encrypt_key(){

			return random_int();

		}

		private function getChildObjsOld($objectData, $blueprint, $levels){

			$ret = $objectData;
			if( is_numeric($levels) and $levels > 0 ){

				foreach($blueprint as $key => $property){

					if($property['type'] == 'relatedObject'){

						$ret[$key] = $this->getById(null, $ret[$key], $levels - 1);

					}elseif($property['type'] == 'objectId'){

						// $getChildObjects will be int of how many levels of child objects you want to grab
						$ret[$key] = $this->getById($property['objectType'], $ret[$key], $levels - 1);
						if($property['objectType'] === 'file_meta_data'){
							$ret[$key]['loc'] = $this->getFileLocation($ret[$key]);
						}

					}elseif($property['type'] == 'objectIds'){

						if($ret[$key]){

							foreach($ret[$key] as $innerKey => $childObjId){

								$ret[$key][$innerKey] = $this->getById($property['objectType'], $childObjId, $levels - 1);

							}

							$ret[$key] = array_filter($ret[$key]);

						}

					//TODO: get child objects for blueprint 'object' types
					}elseif($property['type'] == 'list'){

						foreach($ret[$key] as $innerKey => $childObj){

							$ret[$key][$innerKey] = $this->getChildObjsOld($childObj, $property['blueprint'], $levels);

						}

					}elseif($property['type'] == 'select'){

						$ret[$key . '_name'] = $property['options'][$ret[$key]];

					}

				}

			}elseif( is_array($levels) ){

				foreach($blueprint as $key => $property){

					if( array_key_exists($key, $levels) ){

						if($property['type'] == 'relatedObject'){

							$ret[$key] = $this->getById(null, $ret[$key], $levels[$key]);

						}elseif($property['type'] == 'objectId'){

							// $getChildObjects will be int of how many levels of child objects you want to grab
							$ret[$key] = $this->getById($property['objectType'], $ret[$key], $levels[$key]);
							if($property['objectType'] === 'file_meta_data'){
								$ret[$key]['loc'] = $this->getFileLocation($ret[$key]);
							}

						}elseif($property['type'] == 'objectIds'){

							foreach($ret[$key] as $innerKey => $childObjId){

								$ret[$key][$innerKey] = $this->getById($property['objectType'], $childObjId, $levels[$key]);

							}

						//TODO: get child objects for blueprint 'object' types
						}elseif($property['type'] == 'list'){

							foreach($ret[$key] as $innerKey => $childObj){

								$ret[$key][$innerKey] = $this->getChildObjs($childObj, $property['blueprint'], $levels[$key]);

							}

						}elseif($property['type'] == 'select'){

							$ret[$key . '_name'] = $property['options'][$ret[$key]];

						}

					}

				}

			}

			return $ret;

		}

		private function getObjectPropertyChildObjs($objectData, $blueprint, $property, $selection){

			// $objectData = items in set

			$objectIds = [];
			$childObjects = [];
			$subSelection = 0;

			if( is_array($selection) ){
				$subSelection = $selection[$property];
			}elseif( is_int($selection) ){
				$subSelection = $selection - 1;
			}

			if ($subSelection === 'id') {
				return $objectData;
			}

			switch($blueprint[$property]['fieldType']) {

				case 'date-rollup':

// 					$childObjects = $this->where($blueprint[$property]['options']['set'], [
// 						'parent' => $objectData[0]['id']
// 					], '', [
// 						'id' => true,
// 						$blueprint[$property]['options']['field'] => true
// 					], false, 0, 'null', 'asc', 1, null, array(), 'string', true);
//
// 					$dateCompare = function ($a, $b) use ($blueprint, $property) {
//
// 						return new DateTime($a[$parentBlueprint[$property]['options']['field']]) <=> new DateTime($b[$parentBlueprint[$property]['options']['field']]);
//
// 					};
//
// 					array_push($childObjects, $objectData[0]);
//
// 					usort($childObjects, $dateCompare);
//
// 					if($blueprint[$property]['options']['aggregationType'] == 'least_urgent'){
//
// 						$childObjects = array_reverse($childObjects);
//
// 					}
//
// 					$this->update($blueprint[$property]['options']['set'], [
// 						'id' => $objectData[0]['id']
// 						, $property => $childObjects[0][$blueprint[$property]['options']['field']]
// 						, $property.'_refId' => $childObjects[0]['id']
// 					]);
//
// 					$objectData[0][$property] = $childObjects[0][$blueprint[$property]['options']['field']];

				break;

				case 'reference':

					// get child objects
					$parentIds = __::uniq(__::pluck($objectData, $blueprint[$property]['options']['parentRef']));

					if(count($parentIds) == 0){

						$parentId = $objectData[0]['parent'];

						$childObjects = $this->getById($blueprint[$property]['options']['set'], $parentId);

						$objectData[0][$property] = $childObjects[$blueprint[$property]['options']['field']];

						//$updatedObject = $this->update($objectData[0]['object_bp_type'], $objectData[0]);

						//$objectData[0] = $updatedObject;

					}else{

						$query = [];

						$query = [
							'parent' => [
								'type' => 'or'
								, 'values' => $parentIds
							]
						];

						$childObjects = $this->where($blueprint[$property]['options']['set'], $query, '', [
							$blueprint[$property]['options']['field'] => true
							, 'parent' => 'id'
						]);

						// merge child objects into data
						foreach($objectData as $i => $datum){


							if (
								empty($objectData[$i][$property . '_lockedOn'])
							) {

								$selectId = $datum[$blueprint[$property]['options']['parentRef']];

								if($selectId){

									$objectData[$i][$property] = __::find($childObjects, function($childObj) use($selectId) {
										return $selectId == $childObj['parent'];
									});

									$objectData[$i][$property] = $objectData[$i][$property][$blueprint[$property]['options']['field']];

								}else{

									$objectData[$i][$property] = null;

								}

								if($blueprint[$property]['options']['set'] === 'file_meta_data'){
									$objectData[$i][$property]['loc'] = $this->getFileLocation($objectData[$i][$property]);
								}


							} else {

								$objectData[$i][$property] = $objectData[$i][$property];

							}

						}

					}

					$objectIds = [];
					$childObjects = [];

				break;

				case 'reference-calculation':

					$query = [
						'parent'=>$objectData[0]['id']
					];
// echo $blueprint[$property]['options']['set'].' '.$objectData[0]['id'];
// die();
					$childObjects = $this->where($blueprint[$property]['options']['set'], $query, false);

					//$taggedObjects = $this->where($blueprint[$property]['options']['set'], ['tagged_with'=>$objectData[0]['id']], false);

					//$everything = array_merge($childObjects, $taggedObjects);

					//$childList = array_unique($everything, SORT_REGULAR);
					$childList = $childObjects;

					$objectData[0][$property] = 0;

					switch($objectData[0]['options']['aggregationType']){

						case 'sum':

							foreach($childList as $childObj){

								$objectData[0][$property] += $childObj[$blueprint[$property]['options']['field']];

							}

							break;

						default:

							foreach($childList as $childObj){

								$objectData[0][$property] += $childObj[$blueprint[$property]['options']['field']];

							}

					}

					$updateArray = [
						'id'=>$objectData[0]['id'],
						$property=>$objectData[0][$property]
					];

					//$updatedObject = $this->update($objectData[0]['object_bp_type'], $updateArray);

					$query = [];
					$objectIds = [];
					$childObjects = [];

				break;

				case 'formula':

					// Get up to date value for formulas
					if (
						!empty($blueprint[$property]['options'])
						&& is_string($blueprint[$property]['options']['formula'])
					) {

						// merge child objects into data
						foreach($objectData as $i => $datum){

							$objectData[$i][$property] =$this->parseFormulaField($property, $datum, $blueprint);

						}

					}

				break;

				case 'duration':
				// Get up to date value for duration
				if (!empty($blueprint[$property]['options'])) {

					$startDateField = $blueprint[$property]['options']['startDateSelect'][0];
					$endDateField = $blueprint[$property]['options']['endDateSelect'][0];

					$startDate = $objectData[0][$startDateField];
					$endDate = $objectData[0][$endDateField];

					$diff = strtotime($endDate) - strtotime($startDate);

					$objectData[0][$property] = $diff;

					$update = [
						'id'=>$objectData[0]['id'],
						$property=>strval($diff)
					];

					$updated = $this->update($objectData[0]['object_bp_type'], $update, false, false);

				}

			break;

			}

			switch($blueprint[$property]['type']){

				case 'objectId':
				case 'relatedObject':

					// get child objects
					$objectIds = __::uniq(__::pluck($objectData, $property));
					$childObjects = $this->getById($blueprint[$property]['objectType'], $objectIds, $subSelection);

					// merge child objects into data
					foreach($objectData as $i => $datum){

						$selectId = $datum[$property];
						if($selectId){
							$objectData[$i][$property] = __::find($childObjects, function($childObj) use($selectId) {
								$childID = !empty($childObj['id']) ? $childObj['id'] : '';
								return $selectId === $childID;
							});
						}else{
							$objectData[$i][$property] = null;
						}

						if($blueprint[$property]['objectType'] === 'file_meta_data'){
							$objectData[$i][$property]['loc'] = $this->getFileLocation($objectData[$i][$property]);
						}

					}
					$objectIds = [];
					$childObjects = [];

				break;

				case 'objectIds':

					// Contact info links back, but the pointer values are not always up to date,
					// so just use the pointers on the contact_info so we just have one source of truth
					// for the connection.
					if ($property === 'contact_info') {

						$objectIds = [];
						foreach ($objectData as $i => $datum) {

							array_push($objectIds, $datum['id']);

						}

						$subSelection['object_id'] = 'id';
						$childObjects = $this->whereAcrossInstances(
							'contact_info'
							, [
								'object_id' => [
									'type' => 'or'
									, 'values' => $objectIds
								]
							]
							, ''
							, $subSelection
							, false
							, 0
							, 'null'
							, 'asc'
							, 100
							, null
							, array()
							, 'string'
							, null
						);

					} else {

						// get child objs
						$objectIds = [];
						foreach ($objectData as $i => $datum) {

							if (is_array($datum[$property])) {
								foreach ($datum[$property] as $objId) {
									array_push($objectIds, $objId);
								}
							}

						}

						$objectIds = __::uniq($objectIds);
						$childObjects = $this->getByIdAcrossInstances($blueprint[$property]['objectType'], $objectIds, $subSelection);

					}


					// merge child objs into data
					foreach($objectData as $i => $datum){

						$selectIds = $datum[$property];
						if ($selectIds || $property === 'contact_info') {

							$objectData[$i][$property] = __::filter($childObjects, function($childObj) use($selectIds, $datum, $property) {

								if (is_array($childObj)) {

									if ($property === 'contact_info') {
										return $childObj['object_id'] === $datum['id'];
									} else {
										return in_array($childObj['id'], $selectIds);
									}
								} else {

									return false;

								}

							});

						} else {

							$objectData[$i][$property] = [];

						}

						$objectData[$i][$property] = array_filter($objectData[$i][$property]);

					}

					$objectIds = [];
					$childObjects = [];

				break;

				case 'list':

					$listSubSelection = $subSelection;
					if( is_int($listSubSelection) ){
						$listSubSelection++;
					}

					//!TODO: update this with batch calls like rest in this func
					foreach($objectData as $i => $datum) {

						if (!empty($objectData[$i][$property])) {

							foreach($objectData[$i][$property] as $innerKey => $childObj) {

								$objectData[$i][$property][$innerKey] = $this->getChildObjsOld($childObj, $blueprint[$property]['blueprint'], $listSubSelection);

							}

						}

					}

				break;

				//!TODO: put this somewhere more fitting
				case 'select':
					foreach($objectData as $i => $datum){
						$objectData[$i][$property . '_name'] = $blueprint[$property]['options'][$datum[$property]];
					}
				break;

			}
//var_dump($objectData);
// die();
			return $objectData;

		}

		private function parseFormulaField($key, $obj, $bp) {

			try {

				// Find all the matches
				$tags = [];
				$templateTxt = $bp[$key]['options']['formula'];

				preg_match_all(
					"/{{(.*?)}}/"
					, $templateTxt
					, $tags
				);

				// Get the graph data needed for merge
				$select = [
					'name' => true
				];

				$mergeTagOptions = [];

				foreach ($tags[0] as $i => $tag) {

					$fieldName = trim($tag, '{{}}');

					if (str_contains($fieldName, 'this.')) {
						$fieldName = str_replace('this.', '', $fieldName);
					}

					$Field = false;
					$FieldKey = false;

					// Fields on blueprints
					foreach ($bp as $refKey => $field) {

						if (!empty($field['fieldType']) && !$field['is_archived']) {

							if ($field['name'] === $fieldName) {

								$Field = $field;
								$FieldKey = $refKey;

								switch ($field['fieldType']) {

									case 'state':
										$Field = $field;
										$FieldKey = $refKey .'_weight';
										break;

								}

							}

						}

					}

					if ($FieldKey && is_numeric($obj[$FieldKey])) {

						$templateTxt = str_replace(
							$tag
							, $obj[$FieldKey]
							, $templateTxt
						);

					} else {

						$templateTxt = str_replace(
							$tag
							, '0'
							, $templateTxt
						);

					}

				}

				$merged = str_replace('$', '', $templateTxt);
				$merged = str_replace(',', '', $merged);

				$stringCalc = new ChrisKonnertz\StringCalc\StringCalc();
				$obj[$key] = $stringCalc->calculate($merged);

				if (is_nan($obj[$key]) || is_infinite($obj[$key])) {
					$obj[$key] = 0;
				}

			} catch (Exception $e) {

				$obj[$key] = 'Syntax error in formula';

			}

			return $obj[$key];

		}

		private function getChildObjs($objectData, $blueprint, $levels = 0){

			if( is_array($blueprint) ){

				foreach($blueprint as $propertyName => $property){

					if( is_array($levels) and array_key_exists($propertyName, $levels) ){
						$objectData = $this->getObjectPropertyChildObjs($objectData, $blueprint, $propertyName, $levels);
					}elseif( is_int($levels) and $levels > 0 ){
						$objectData = $this->getObjectPropertyChildObjs($objectData, $blueprint, $propertyName, $levels);
					}

				}

			}

			if (is_array($levels)) {
				foreach ($levels as $key => $select) {

					// Object type to pull in
					$objTypeToPull = '';
					if (
						$key === '#Action Items'
						|| $key === '##Action Items'
					) {

						if ($this->instance === null) {
							$this->setInstance($objectData[0]['instance']);
						}

						$objTypeToPull = '#Action Items';

					} elseif (substr($key, 0, 2) === '##') {

						$objTypeToPull = substr($key, 1);

					} elseif (substr($key, 0, 1) === '#') {

						$objTypeToPull = $key;

					}

					// Merge in direct child objects (whose parent property points here)
					foreach ($objectData as $i => $datum) {

						$where = [];

						if (!empty($select['where'])) {

							$where = $select['where'];
							unset($select['where']);

						}

						if (substr($key, 0, 2) === '##') {

							if ($select['_context'] === 'Any') {
								unset($select['_context']);
							} else {
								$where['tagged_with'] = $datum['id'];
							}

							$objectData[$i][$key] = $this->where(
								$objTypeToPull
								, $where
								, ''
								, $select
								, false
								, 0
								, 'name'
								, 'asc'
							);

						// Merge in objects visible in the space (anything tagged with it)
						} elseif (substr($key, 0, 1) === '#') {

							if ($select['_context'] === 'Any') {
								unset($select['_context']);
							} else {
								$where['parent'] = $datum['id'];
							}

							$objectData[$i][$key] = $this->where(
								$objTypeToPull
								, $where
								, ''
								, $select
								, false
								, 0
								, 'name'
								, 'asc'
							);

						}

					}

				}
			}

			return $objectData;

		}

		private function getFileLocation($file){

			if (is_array($file['oid'])) {

				return $file['oid_type'] .'/'. $file['oid']['id'] .'/'. $file['file_name'];

			} else {

				return $file['oid_type'] .'/'. $file['oid'] .'/'. $file['file_name'];

			}

		}

		private function array_merge_recursive_distinct ( array &$array1, array &$array2 )
		{
		$merged = $array1;

		foreach ( $array2 as $key => &$value )
		{
			if ( is_array ( $value ) && isset ( $merged [$key] ) && is_array ( $merged [$key] ) )
			{
			$merged [$key] = $this->array_merge_recursive_distinct ( $merged [$key], $value );
			}
			else
			{
			$merged [$key] = $value;
			}
		}

		return $merged;
		}

		public function getDataFromMap ($map = null, $contextObjId = null) {

			$ret = [];

			if (is_array($map) && !empty($map)) {

				// Build selection obj
				$mapSelectionObj = [];
				foreach ($map as $key => $val) {

					$mapSelectionObj = $this->array_merge_recursive_distinct(
							$this->getSelectionObjFromAddressString(
								explode('.', $val)
								, []
							)
							, $mapSelectionObj
					);

				}

				// Get data
				if (is_array($mapSelectionObj) && !empty($mapSelectionObj)) {
					// error_reporting(E_ALL);
		// ini_set('display_errors', '1');
					$graph = $this->getById('', $contextObjId, $mapSelectionObj);

					// Pass to templates
					foreach ($map as $key => $val) {
	// var_dump(
	// 	explode('.', $val)
	// 	, $graph
	// );
	// die();
						// $s
						$ret[$key] = $this->getValueAtAddressPath(
							explode('.', $val)
							, $graph
						);

					}

				}

			}

			return $ret;

		}

		public function getSelectionObjFromAddressString ($props = null, $memo = null, $i = null) {

			// Set initial state
			if ($memo == null) {
				$memo = [];
			}
			if ($i == null) {
				$i = 0;
			}
			$memo['name'] = true;

			if ($props[$i]) {

				// If final property, just set the value to true
				if (!$props[$i + 1]) {

					$memo[$props[$i]] = true;

				// Get nested selection obj
				} else {

					$memo[$props[$i]] = buildSelectionArr(
						$props
						, $memo
						, $i + 1
					);

				}

			}

			return $memo;

		}

		public function getValueAtAddressPath ($path, $graph = null, $opts = null, $i = null) {

			// Set initial state
			if ($i == null) {
				$i = 0;
			}
			if ($opts == null) {
				$opts = [];
			}
			$val = '';

			if ($path[$i]) {

				// If final step, set the value to the value there
				if (!$path[$i + 1]) {

					// Special cases
					switch ($path[$i]) {

						default:

							// Pointers to objs
							if (is_array($graph[$path[$i]]) && $graph[$path[$i]]['id']) {

								if ($opts && $opts['format'] === 'data') {
									$val = $graph[$path[$i]];
								} else {
									$val = $graph[$path[$i]]['id'];
								}

							// Entities within the space (#[set name])
							} elseif (is_array($graph[0][$path[$i]])) {

								// Pointers
								if (
									is_array($opts)
									&& $opts['field'] === 'address'
								) {

									$val = $graph[0][$path[$i]];

								} elseif (is_int($graph[0][$path[$i]]['id'])) {

									if ($opts && $opts['format'] === 'data') {
										$val = $graph[0][$path[$i]];
									} else {
										$val = $graph[0][$path[$i]]['id'];
									}

								// Array if ints
							 	} elseif (count($graph[0][$path[$i]]) > 0) {

									$val = __::pluck($graph[0][$path[$i]], 'id');

								}

							// Straight values at the given property
							} else {

								if (
									is_array($graph)
									&& !array_key_exists($path[$i], $graph)
									&& is_array($graph[0])
									&& array_key_exists($path[$i], $graph[0])
								) {

									$val = $graph[0][$path[$i]];

								} else {

									$val = $graph[$path[$i]];

								}

							}
							break;

					}

				// Get value nested within
				} else {

					$val = $this->getValueAtAddressPath(
						$path
						, $graph[$path[$i]]
						, $opts
						, $i + 1
						, $objs
					);

				}

			}

			if ($val == null) {
				$val = '';
			}

			return $val;

		}

		private function parseData($data, $getChildObjects = 0, $objectType = null, $getJust = array(), $with = null, $opts = array()){

			if (!array_key_exists('mergeDocs', $opts)) {
				$opts = [
					'mergeDocs' => true
				];
			}

			// get necessary blueprints if they are not already cached
			$objectType = __::uniq(__::pluck($data, 'object_type'));
			if ( count($objectType) == 1 ) {
				$objectType = $objectType[0];
			}

			if (is_string($objectType)) {

				$blueprint[$objectType] = $this->getBlueprint($objectType);

			} elseif(is_array($objectType)) {

				foreach($objectType as $i => $bpName) {

					$blueprint[$bpName] = $this->getBlueprint($bpName);

				}

			}

			$ret = array();

			if(is_array($data)){

				foreach($data as $key => $datum){

					if ($with === null) {

						$temp = json_decode($datum['object_data'], true);

						// Pull out obj data
						if (is_array($temp)) {
							foreach ($temp as $key => $val) {

								if (!array_key_exists($key, $datum)) {
									$datum[$key] = $val;
								}
							}
						}

					} else {

						$temp = $datum;

					}

					if (is_array($getChildObjects)) {

 						$temp = [];
						$temp['id'] = $datum['id'];
						$temp['date_created'] = $datum['date_created'];
                        $temp['last_updated'] = $datum['last_updated'];
						$temp['object_bp_type'] = $datum['object_type'];
						$temp['full_count'] = $datum['full_count'];
						$temp['instance'] = $datum['instance'];
						$temp['is_deleted'] = $datum['is_deleted'];
						$temp['sort_index'] = $datum['sort_index'];
						$temp['_sortIndex'] = $datum['row_number'];
						$temp['tagged_with'] = $this->parseTagListFromDB($datum['tagged_with']);
						$temp['shared_with'] = $this->parseTagListFromDB($datum['shared_with']);
						$temp['read'] = $this->parseTagListFromDB($datum['read_obj']);
						$temp['write'] = $this->parseTagListFromDB($datum['write_obj']);
						$temp['notify'] = $this->parseTagListFromDB($datum['notify']);

						if ($datum['comment_count']) {
							$temp['comment_count'] = intval($datum['comment_count']);
						}
						switch ($temp['object_bp_type']) {

							case 'users':
							case 'contacts':
								$temp['name'] = $datum['fname'] .' '. $datum['lname'];
								break;

							case 'system_tags':
								$temp['name'] = $datum['tag'];
								break;

						}

						$bp = $blueprint[$temp['object_bp_type']];

						foreach( $datum as $key => $val ) {

							if ( !is_bool($bp) ) {

								if (
									array_key_exists($key, $bp)
									&& $bp[$key]['hidden'] !== true
									&& $getChildObjects[$key]
								) {

									// decrypt private properties, if user has read privelages
									/*
	if($bp[$key]['encrypt']){
										$temp[$key] = $this->decrypt_property($temp[$key]);
									}
									if($bp[$key]['hidden']){
										$temp[$key] = null;
									}
	*/
									$temp[$key] = $datum[$key];
									switch($bp[$key]['type']){

										case 'bool':
										case 'boolean':
											if (is_string($temp[$key])) {
												if ($temp[$key] === 'true') {
													$temp[$key] = true;
												} else {
													$temp[$key] = false;
												}
											}
											break;

										case 'document':
											if (is_numeric($temp[$key])) {
												$temp[$key] = $this->getDocument($temp[$key]);
											}
											break;

										case 'int':
										case 'objectId':
										case 'usd':
										case 'parentId':
										case 'relatedObject':

											switch($bp[$key]['fieldType']){

												case 'comments':

													if($bp[$key]['options']){

														if($bp[$key]['options']['showLatest'] == true){

															$selectArray = [
																'type_id'=>$temp['id'],
																'field'=>$key
															];

															$latestNote = $this->where('notes', $selectArray, null, ['note'=>true], null, null, null, null, 1);

															$temp[$key.'_latest'] = $latestNote[0]['note'];
															$temp[$key.'_posted_on'] = $latestNote[0]['date_created'];

														}

													}

													break;

												default:

													$temp[$key] = intval($temp[$key]);

											}

											break;

										case 'objectIds':
											if (is_string($temp[$key])) {
												$temp[$key] = json_decode($temp[$key], true);
											}
											if (is_int($temp[$key])) {
												$temp[$key] = [$temp[$key]];
											}
											break;

										case 'list':
										case 'ints':
										case 'object':
										case 'map':
											if (is_string($temp[$key])) {
												$temp[$key] = json_decode($temp[$key], true);
											}

											if (
												$opts['mergeDocs']
												&& (
													$objectType === 'entity_type'
													|| $objectType === 'view'
												)
												&& $key === 'blueprint'
												&& is_array($temp[$key])
											) {

												foreach ($temp[$key] as $innerKey => $field) {

													if (
														is_array($field['options'])
														&& is_numeric($field['options']['_message'])
													) {

														$temp[$key][$innerKey]['options']['_message']
															= $this->getDocument($field['options']['_message']);

													}

												}

											}
											break;

										default:

// 											switch($bp[$key]['fieldType']){
//
// 												case 'reference-calculation':
//
// // echo '<pre>';
// // var_dump($bp[$key]);
// // echo '</pre>';
// // die();
//
// 													if($skipAgg == false){
//
// 														$datum[$key] = 0;
//
// 														$childTaggedWith = $this->where(
// 															$bp[$key]['options']['set']
// 															, [
// 																'tagged_with' => [$datum['id']]
// 															]
// 														);
//
// 														$childrenProper = $this->where(
// 															$bp[$key]['options']['set']
// 															, [
// 																'parent' => $datum['id']
// 															]
// 														);
//
// 														$allChildren = array_merge($childTaggedWith, $childrenProper);
// 														$allChildren = array_unique($allChildren);
//
//
//
// 	// echo '<pre>';
// 	// var_dump($allChildren);
// 	// echo '</pre>';
// 	// die();
// 														switch($bp[$key]['options']['aggregationType']){
//
// 															default:
//
// 																// includes 'sum' type
//
// 																foreach($allChildren as $child){
//
// 																	$datum[$key] += $child[$bp[$key]['options']['field']];
//
// 																}
//
// 														}
//
// 														$this->update($datum['object_bp_type'], ['id'=>$datum['id'], $key=>$datum[$key]]);
//
// 													}
//
//
//
// 													break;
//
// 											}

									}
								}

							}

						}

					}else{

						$temp['tagged_with'] = $this->parseTagListFromDB($datum['tagged_with']);
						$temp['shared_with'] = $this->parseTagListFromDB($datum['shared_with']);
						$temp['read'] = $this->parseTagListFromDB($datum['read_obj']);
						$temp['write'] = $this->parseTagListFromDB($datum['write_obj']);
						$temp['notify'] = $this->parseTagListFromDB($datum['notify']);

						// default to given object type to get bp if bp is not defined in object data
						if(array_key_exists('object_bp_type', $temp)){
							$bp = $blueprint[$temp['object_bp_type']];
						}elseif($bp == null and is_string($objectType)){
							$bp = $blueprint[$objectType];
						}

						switch ($temp['object_bp_type']) {

							case 'users':
							case 'contacts':
								$temp['name'] = $temp['fname'] .' '. $temp['lname'];
								break;

						}

						if(is_array($bp)){
							foreach($bp as $key => $prop){

								if($bp[$key]['hidden']){
									unset($temp[$key]);
								}

								switch ($prop['type']) {

									case 'bool':
									case 'boolean':
										if (is_string($temp[$key])) {
											if ($temp[$key] === 'true') {
												$temp[$key] = true;
											} else {
												$temp[$key] = false;
											}
										}
										break;

									case 'document':
										if (is_numeric($temp[$key])) {
											$temp[$key] = $this->getDocument($temp[$key]);
										}
										break;

									case 'map':
										if (
											$opts['mergeDocs']
											&& (
												$objectType === 'entity_type'
												|| $objectType === 'view'
											)
											&& $key === 'blueprint'
											&& is_array($temp[$key])
										) {

											foreach ($temp[$key] as $innerKey => $field) {

												if (
													is_array($field['options'])
													&& is_numeric($field['options']['_message'])
												) {

													$temp[$key][$innerKey]['options']['_message']
														= $this->getDocument($field['options']['_message']);

												}

											}

										}
										break;

									default:

// 										switch($bp[$key]['fieldType']){
//
// 											case 'reference-calculation':
//
// // echo '<pre>';
// // var_dump($bp[$key]['options']['set']);
// // echo '</pre>';
// // die();
//
//
//
// 												$childTaggedWith = $this->where(
// 													$bp[$key]['options']['set']
// 													, [
// 														'tagged_with' => [$temp['id']]
// 													]
// 												);
// // if($key == '_3'){
// // echo '<pre>';
// // var_dump($key);
// // echo '</pre>';
// // die();
// // }
// 												$childrenProper = $this->where(
// 													$bp[$key]['options']['set']
// 													, [
// 														'parent' => $temp['id']
// 													]
// 												);
//
// 												$allChildren = array_merge($childTaggedWith, $childrenProper);
// 												$allChildren = array_unique($allChildren);
// // if($key == '_3'){
// // 	echo '<pre>';
// // 	var_dump($allChildren);
// // 	echo '</pre>';
// // 	die();
// // }
// 												$temp[$key] = 0;
// 												switch($bp[$key]['options']['aggregationType']){
//
// 													default:
//
// 														// includes 'sum' type
//
// 														foreach($allChildren as $child){
//
// 															$temp[$key] += $child[$bp[$key]['options']['field']];
//
// 														}
//
// 												}
// // echo '<pre>';
// // var_dump($temp['object_bp_type']);
// //var_dump($allChildren);
// // var_dump(['id'=>$temp['id'], $key=>$temp[$key]]);
// // echo '</pre>';
// // die();
// 												$this->update($temp['object_bp_type'], ['id'=>$temp['id'], $key=>$temp[$key]]);
// // echo '<pre>';
// // var_dump(['id'=>$temp['id'], $key=>$temp[$key]]);
// // echo '</pre>';
// // die();
// 												break;
//
// 										}

								}

								// decrypt private properties, if user has read privelage
								/*
if($prop['encrypt']){
									$temp[$key] = $this->decrypt_property($temp[$key]);
								}
								if($prop['hidden']){
									$temp[$key] = null;
								}
*/

							}
						}

						if(!empty($getJust)){

							$just = array(
								'date_created' => $temp['date_created']
							);

							foreach($getJust as $i => $prop){
								$just[$prop] = $temp[$prop];
							}

							$temp = $just;

						}

						$temp['id'] = $datum['id'];
						$temp['date_created'] = $temp['date_created'];
						$temp['object_bp_type'] = $datum['object_type'];
						$temp['full_count'] = $datum['full_count'];
						$temp['instance'] = $datum['instance'];

						$bp = null;

					}

					if ($temp['object_bp_type'] === 'file_meta_data') {
						$temp['loc'] = $this->getFileLocation($temp);
					}

					if($with !== null){
						$temp[$with] = $datum[$with];
					}

					array_push($ret, $temp);

				}

				// !TODO: Only run this code if necessary (if query is looking
				// at multiple object types).
				// get and merge in child obj data
				$groups = __::groupBy($ret, 'object_bp_type');
				$order = __::pluck($ret, 'id');
				$ret = [];
				foreach($groups as $objType => $group){
					$ret = array_merge($ret, $this->getChildObjs($group, $blueprint[$objType], $getChildObjects));
				}

				$response = [];
				foreach ($order as $id) {
					array_push(
						$response,
						__::find($ret, function ($obj) use($id) {
							return $obj['id'] === $id;
						})
					);
				}

				return $response;

			}else{

				return false;

			}

		}

		private function parseInput(
			$objectType
			, $objectData
			, $obj
			, $newObj = 0
			, $blueprint = null
			, $depth = 0
			, &$state = []
			, $skipParent = false
		){

			if ($blueprint == null) {

				$blueprint = $this->getBlueprint($objectType, false, true);

				// For custom sets, accept the meta-data as well as the fields blueprint
				if ($blueprint && $blueprint['object_bp_type']) {

					$entityType = $blueprint;
					$blueprint = $entityType['blueprint'];

				}

			}

			$now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
			$toMerge = [];

			if (is_array($blueprint)) {

				foreach($blueprint as $key => $field){

					if (
						$key !== 'id'
						and $key !== 'date_created'
						and isset($objectData[$key])
						or $depth > 0
						and isset($objectData[$key])
						or (array_key_exists($key, $objectData) && $field['type'] == 'date')
					) {

						switch($field['fieldType']) {

							case 'date-rollup':

// 							$childObjects = $this->where($obj['object_bp_type'], [
// 									'parent' => $obj['id']
// 								]);
//
// 							$dateCompare = function ($a, $b) use ($parentBlueprint, $key) {
//
// 								try {
// 									$aDate = new DateTime($a[$field['options']['field']]);
// 								} catch (Exception $e) {
// 									// echo $e->getMessage();
// 									// exit(1);
// 								}
//
// 								try {
// 									$bDate = new DateTime($b[$field['options']['field']]);
// 								} catch (Exception $e) {
// 									// echo $e->getMessage();
// 									// exit(1);
// 								}
//
// 								return $aDate <=> $bDate;
//
// 							};
//
// 							array_push($childObjects);
//
// 							usort($childObjects, $dateCompare);
//
// 							if($field['options']['aggregationType'] == 'least_urgent'){
//
// 								$childObjects = array_reverse($childObjects);
//
// 							}
//
// 							$objectData[$key] = $childObjects[0][$field['options']['field']];
// 							$objectData[$key.'_refId'] = $childObjects[0]['id'];

							break;

							case 'next-due-date':

								// get child objects

// 								$childObjects = $this->where($blueprint[$key]['options']['set'], [
// 									'parent' => $obj['id']
// 								], '', [
// 									'id' => true,
// 									$blueprint[$key]['options']['field'] => true
// 								]);
//
// 								$dateCompare = function ($a, $b) use ($blueprint, $key) {
//
// 									return new DateTime($a[$blueprint[$key]['options']['field']]) <=> new DateTime($b[$blueprint[$key]['options']['field']]);
//
// 								};
//
// 								//array_push($childObjects, $objectData);
//
// 								usort($childObjects, $dateCompare);
//
// 								if($blueprint[$key]['options']['aggregationType'] == 'least_urgent'){
//
// 									$childObjects = array_reverse($childObjects);
//
// 								}
//
// 								$this->update($obj['object_bp_type'], [
// 									'id' => $obj['id']
// 									, $key => $childObjects[0][$blueprint[$key]['options']['field']]
// 									, $key.'_refId' => $childObjects[0]['id']
// 								]);
//
								break;

							case 'reference':

								if (
									$blueprint[$key]['options']['shouldPush']
									&& empty($obj[$key . '_lockedOn'])
								) {

									// get child objects
									$parentId = $obj[$blueprint[$key]['options']['parentRef']];

									$childObjects = $this->where($blueprint[$key]['options']['set'], [
										'parent' => $parentId
									], '', false, false, 0, 'null', 'asc', 1, null, array(), 'string', true);

									// $this->update($blueprint[$key]['options']['set'], [
									// 	'id' => $childObjects[0]['id']
									// 	, $blueprint[$key]['options']['field'] => $objectData[$key]
									// ]);

								}

								break;

							default;

						}

						switch($field['type']){

							case 'date':

								try {

									if ($objectData[$key] == null) {

										$obj[$key] = '';

									} else {

										$date = new DateTime($objectData[$key]);
										$obj[$key] = $date->format('Y-m-d H:i:s');

									}

								} catch (Exception $e) {}


							break;

							case 'document':

								try {

									$obj[$key] = $this->setDocument(
										$obj
										, $key
										, $objectData[$key]
									) ;

								} catch (Exception $e) {}

							break;

							case 'time':

								try {
									$date = new DateTime($objectData[$key]);
									$obj[$key] = $date->format('H:i:s');
								}catch (Exception $e){
									$obj[$key] = $objectData[$key];
								}

							break;

							case 'float':
							$obj[$key] = $objectData[$key];
							break;

							case 'int':
							case 'usd':

								$obj[$key] = intval(
									preg_replace(
										"/[^0-9]-/"
										,""
										, $objectData[$key]
									)
								);

								// For workflows linked to by another property.
								if (
									$field['fieldType'] === 'state'
									&& is_int($field['workflow'])
								) {

									// get workflow
									$workflowObj = $this->getByIdAcrossInstances(
										'entity_workflow'
										, $field['workflow']
										, [
											'id' => true,
											'states' => true
										]
										, false
										, $obj['instance']
									);

									// get state
									$crntState = __::find(
										$workflowObj['states']
										, function($state) use($obj, $key) {

											if ( !empty($state['uid']) ) {
												return $state['uid'] == $obj[$key];
											} else {
												return;
											}

										}
									);
									if (!$crntState) {

										// set to initial state
										$crntState = __::find(
											$workflowObj['states']
											, function($state) {

												if ( !empty($state['isEntryPoint']) ) {
													return $state['isEntryPoint'] == 1;
												} else {
													return;
												}

											}
										);
										$obj[$key] = $crntState['uid'];

									}

									// set to initial state
									$obj[$key .'_status'] = $crntState['type'];

									// set to state weight
									$obj[$key .'_weight'] = $crntState['weight'];

									if (is_null($obj[$key .'_status'])) {
										$obj[$key .'_status'] = 'not_started';
									}
									// Timestamp of state change.
									$obj[$key .'_updated_on'] = $now->format('Y-m-d H:i:s');

									// Update overall 'status' value
									$overallStatus = 'done';
									foreach ($blueprint as $k => $f) {

										if (
											$f['fieldType'] === 'state'
											&& !$f['is_archived']
										) {

											$wfFieldStatus = $obj[$k .'_status'];

											// Get most up to date workflow, in case the workflow
											// has been updated since this obj's states have been
											// updated.
											if (is_int($f['workflow'])) {

												$fieldWorkflow = $workflowObj = $this->getByIdAcrossInstances(
													'entity_workflow'
													, $f['workflow']
													, [
														'id' => true,
														'states' => true
													]
													, false
													, $obj['instance']
												);

												if (is_int($obj[$k])) {

													$fieldState = __::find(
														$fieldWorkflow['states']
														, function ($state) use ($obj, $k) {

															return $state['uid'] == $obj[$k];

														}
													);

												// In case the state value is unset, assume the entry point
												// state's status type
												} else {

													$fieldState = __::find(
														$fieldWorkflow['states']
														, function($state) {

															return $state['isEntryPoint'] == 1;

														}
													);

												}

												if ($fieldState) {
													$wfFieldStatus = $fieldState['type'];
												} else {
													$wfFieldStatus = 'done';
												}

											// If it can't find the workflow, assume the field is not
											// fully set up and just assume 'done' for this field
											} else {

												$wfFieldStatus = 'done';

											}
											if ($wfFieldStatus !== 'done') {
												$overallStatus = 'not_started';
											}

										}

									}
									$obj['status'] = $overallStatus;

								}

							break;

							case 'list':

								// get next id to use, and remove items sharing ids
								$tempList = array();
								$nextId = 1;
								$ids = array();
								if(is_array($objectData[$key])){
									foreach($objectData[$key] as $i => $datum){
										if(intval($datum['id']) >= $nextId){
											$nextId = intval($datum['id']) + 1;
										}
										if(intval($datum['id']) >= 1){

											if(isset($ids[$datum['id']])){
												continue;
											}else{
												array_push($tempList, $datum);
											}
											$ids[$datum['id']] = $i;

										}else{

											array_push($tempList, $datum);

										}
									}
								}

								$objectData[$key] = $tempList;
								$temp = array();
								$tempInner = array();

								if(is_array($objectData[$key])){
									foreach($objectData[$key] as $i => $datum){

										$tempInner = array();
										$tempOldDatum = __::find($obj[$key], function($listItem) use($datum) {

											if (is_array($datum) && is_array($listItem)) {

												return $datum['id'] == $listItem['id'];

											} else {

												return false;

											}

										});

										if(!$tempOldDatum){
											$tempOldDatum = [];
										}

										$tempInner = $this->parseInput($objectType, $datum, $tempOldDatum, $newObj, $field['blueprint'], $depth + 1);

										// add id if item in list is new
										if(!array_key_exists('id', $tempInner)){
											$tempInner['id'] = $nextId;
											$nextId++;
										}

										array_push($temp, $tempInner);

									}
								}
								$obj[$key] = $temp;

							break;

							case 'objectId':
							case 'parentId':
							case 'relatedObject':

								// For parent properties, check if config on sets limits this to
								// one per parent.
								if (
									$key === 'parent'
									&& $entityType
									&& $entityType['is_uniq_to_parent']
								) {

									// If there are any other items in this set with this parent
									// don't set the value.
									$count = $this->countObjs(
										'#'. $entityType['bp_name']
										, [
											'parent' => $objectData[$key]
										]
									);

									if ($count && $count[0] && $count[0]['count'] > 0) {
										return false;
									}

								}
								if(is_array($objectData[$key]) and array_key_exists('id', $objectData[$key]) and intval($objectData[$key]['id']) > 1){
									$obj[$key] = intval($objectData[$key]['id']);
								}elseif(!is_array($objectData[$key]) and intval(trim($objectData[$key], "'")) > 0){
									$obj[$key] = intval($objectData[$key]);
								}elseif( intval($objectData[$key]) === $objectData[$key] ){
									$obj[$key] = $objectData[$key];
								}

								// For templates, save the value to template
								if (
									$obj && $obj['is_template']
									&& array_key_exists($key .'_merge', $objectData)
								) {
									$obj[$key .'_merge'] = $objectData[$key .'_merge'];
								}

							break;

							case 'objectIds':
							case 'ints':

							// In case an int is passed in, convert it to an
							// array, the way it should be stored.
							if (is_int($objectData[$key])) {
								$objectData[$key] = [$objectData[$key]];
							}

							$temp = array();
							if(is_array($objectData[$key])){

								foreach($objectData[$key] as $i => $datum){

									if(is_array($objectData[$key][$i]) and array_key_exists('id', $objectData[$key][$i]) and intval($objectData[$key][$i]['id']) > 0){

										$temp[$i] = intval($objectData[$key][$i]['id']);

									}elseif(intval($objectData[$key][$i]) == $objectData[$key][$i]){

										$temp[$i] = intval($objectData[$key][$i]);

									}

								}

							}
							$obj[$key] = $temp;
							// For templates, save the value to template
							if (
								$obj && $obj['is_template']
								&& array_key_exists($key .'_merge', $objectData)
							) {
								$obj[$key .'_merge'] = $objectData[$key .'_merge'];
							}
							break;

							case 'map':
								if (
									is_array($objectData[$key])
									&& !empty($field['blueprint'])
								) {

									foreach ($objectData[$key] as $iKey => $iVal) {

										if (
											(
												$objectType === 'entity_type'
												|| $objectType === 'view'
											)
											&& $key === 'blueprint'
											&& is_array($iVal)
											&& is_array($iVal['options'])
											&& is_string($iVal['options']['_message'])
											&& $iVal['options']['_message']
											&& strlen($iVal['options']['_message']) > 200
										) {

											try {

												$iVal['options']['_message'] = $this->setDocument(
													$objectData
													, $iKey .'-msg'
													, $iVal['options']['_message']
												) ;

											} catch (Exception $e) {}

										}

										$obj[$key][$iKey] = $this->parseInput($objectType, $iVal, [], $newObj, $field['blueprint'], $depth + 1);

									}

								}
								break;

							case 'bool':
								$obj[$key] = boolval($objectData[$key]);
								break;

							case 'object':
							if(is_array($objectData[$key])){
								$obj[$key] = $objectData[$key];

								if($objectType != 'invoice'){

									foreach($obj[$key] as $innerKey => $innerDatum){

										$obj[$key][$innerKey] = $this->parseArrayForInput($innerDatum);

									}

								}

							}
							break;

							case 'string':
							if(is_string($objectData[$key])){

								$obj[$key] = trim(trim($objectData[$key], "'"), ' ');

							}
							break;

							default:
							$obj[$key] = $objectData[$key];
							break;

						}

						// Check if should merge
						switch ($field['type']) {

							case 'string':

								if(is_string($objectData[$key])){

									if (
										$field['fieldType'] === 'plain-text'
										|| $field['fieldType'] === 'title'
									) {

										if (strpos($objectData[$key], '{{')) {

											array_push(
												$toMerge
												, [
													'key' => 			$key
													, 'field' => 		$field
													, 'fieldType' => 	$field['fieldType']
												]
											);

										}

									}

								}
								break;

							case 'object':
								if(is_array($objectData[$key])){

									if ($field['fieldType'] === 'address') {

										array_push(
											$toMerge
											, [
												'key' => 		$key
												, 'field' => 	$field
											]
										);

									}

								}
								break;

						}

						if (method_exists($this->rules, 'shouldProcess')) {
							$this->rules->shouldProcess('update', $field, $state);
						}

					} elseif (
						$key !== 'id'
						and $key !== 'date_created'
						and $newObj == 1
						and $key !== 'last_updated'
					) {

						switch($field['type']){

							case 'date':
								if($objectData[$key] == null){

									$obj[$key] = '';

								}else{

									$date = new DateTime($objectData[$key]);
									$obj[$key] = $date->format('Y-m-d H:i:s');

								}
								break;

							case 'float':
							case 'int':
							case 'usd':
							case 'objectId':
							case 'parentId':
							case 'relatedObject':
								$obj[$key] = 0;
								switch ($field['fieldType']) {

									case 'state':
										// For workflows linked to by another property.
										if (
											(
												is_string($field['workflow'])
												&& is_int($objectData[$field['workflow']])
											)
											|| is_int($field['workflow'])
										) {

											// get workflow
											$prevState = $obj[$key];
											if (is_int($field['workflow'])) {

												// Sets (custom blueprints)
												$workflowObj = $this->getByIdAcrossInstances(
													'entity_workflow'
													, $field['workflow']
													, [
														'id' => true,
														'states' => true
													]
													, false
													, $obj['instance']
												);

											} else {

												// Older, hardcoded blueprints
												$workflowObj = $this->getByIdAcrossInstances(
													$blueprint[$field['workflow']]['objectType']
													, $objectData[$field['workflow']]
													, [
														'id' => true,
														'states' => true
													]
													, false
													, $obj['instance']
												);

											}

											// set to initial state
											$obj[$key] = __::find(
												$workflowObj['states']
												, function($state) {

													return $state['isEntryPoint'] == 1;

												}
											)['uid'];

											if (!$newObj) {

												// trigger state actions
												$this->triggerStateActions (
													$obj
													, $workflowObj
													, $obj[$key]
													, $prevState
													, $key
												);

											}

										}
										break;

									case 'user':
										if (
											$field['fieldType'] === 'user'
											|| $field['fieldType'] === 'users'
										) {

//!WORKING HERE: Trying to merge in the data called out from a merge tag in user field.
											// var_dump($obj);
// die();
											if (strpos($objectData[$key], '{{')) {

												array_push(
													$toMerge
													, [
														'key' => 			$key
														, 'field' => 		$field
														, 'fieldType' => 	$field['fieldType']
													]
												);

											}

										}
										break;

								}
								break;

							case 'list':
							case 'object':
								$obj[$key] = array();
								break;

							case 'string':
								$obj[$key] = '';
								break;

							default:
								$obj[$key] = $objectData[$key];
								break;

						}

					}elseif($key == 'date_created' and $newObj){

						$date = new DateTime($objectData[$key]);
						$obj[$key] = $date->format('Y-m-d H:i:s');
						$obj['last_updated'] = $date->format('Y-m-d H:i:s');

						if (method_exists($this->rules, 'shouldProcess')) {
							$this->rules->shouldProcess('update', $field, $state);
						}

					}

					switch($field['fieldType']){

						case 'date-rollup':

	// 						$children = $this->where($field['options']['set'], [
	// 								'parent' => $obj['id']
	// 							]);
	// 						$childObjects = [];
	//
	// 						foreach($children as $child){
	//
	// 							if($child[$field['options']['field']]){
	// 								$childObjects[] = $child;
	// 							}
	//
	// 						}
	//
	// 						$dateCompare = function ($a, $b) use ($parentBlueprint, $key) {
	//
	// 							try {
	// 								$aDate = new DateTime($a[$field['options']['field']]);
	// 							} catch (Exception $e) {
	// 								// echo $e->getMessage();
	// 								// exit(1);
	// 							}
	//
	// 							try {
	// 								$bDate = new DateTime($b[$field['options']['field']]);
	// 							} catch (Exception $e) {
	// 								// echo $e->getMessage();
	// 								// exit(1);
	// 							}
	//
	// 							return $aDate <=> $bDate;
	//
	// 						};
	//
	// 						array_push($childObjects);
	//
	// 						usort($childObjects, $dateCompare);
	//
	// 						if($field['options']['aggregationType'] == 'least_urgent'){
	//
	// 							$childObjects = array_reverse($childObjects);
	//
	// 						}
	//
	// 						// $updateArray = [
	// 						// 	'id' => $parentObj['id']
	// 						// 	, $key => $childObjects[0][$field['options']['field']]
	// 						// 	, $key.'_refId' => $childObjects[0]['id']
	// 						// ];
	//
	// 						$objectData[$key] = $childObjects[0][$field['options']['field']];
	// 						$objectData[$key.'_refId'] = $childObjects[0]['id'];
	//
	// 						$obj[$key] = $childObjects[0][$field['options']['field']];
	// 						$obj[$key.'_refId'] = $childObjects[0]['id'];

						break;

						case 'formula':

							// Get up to date value for formulas
							if (
								!empty($field['options'])
								&& is_string($field['options']['formula'])
							) {

								$obj[$key] = $this->parseFormulaField($key, $obj, $blueprint);

							}

						break;

						case 'next-due-date':
// echo '<pre>';
// var_dump($obj);
// echo '</pre>';
// die();
						// gather date fields
						$dateFields = [];
						$dateList = [];
						foreach($blueprint as $bpKey => $bpField){

							switch($bpField['fieldType']){

								case 'date':

									if($bpField['options'] && !$bpField['options']['is_archived']){

										if($bpField['options']['is_due_date'] == true){

											$fieldToUse = $obj[$bpKey.'_done'];
											if($objectData[$bpKey.'_done'] === 0){
												$fieldToUse = $objectData[$bpKey.'_done'];
											}elseif($objectData[$bpKey.'_done'] === 1){
												$fieldToUse = 1;
											}

											if($fieldToUse){

												if($fieldToUse != 1){

													$dateFields[$bpKey] = $obj[$bpKey];

												}

											}else{

												$dateFields[$bpKey] = $obj[$bpKey];

											}

										}


									}

								break;

							}

						}
// echo '<pre>';
// var_dump($dateFields);
// echo '</pre>';
// die();
						$compareByTimestamp = function ($time1, $time2){

							try {
								$aDate = new DateTime($time1);
							} catch (Exception $e) {
								// echo $e->getMessage();
								// exit(1);
							}

							try {
								$bDate = new DateTime($time2);
							} catch (Exception $e) {
								// echo $e->getMessage();
								// exit(1);
							}

							return $aDate <=> $bDate;

						};
// echo '<pre>';
// echo $key.'<br />';
// var_dump($objectData);
// var_dump($dateFields);
// echo '</pre>';
// die();


						foreach($objectData as $objKey => $objData){

							if($objKey != 'id'){
//echo $objKey.' - '. $key .'<br />';
								if($objKey == $key){

									$dateFields[$objKey] = $objData;
								}

								if($dateFields[$objKey]){
									$dateFields[$objKey] = $objData;
								}

								if($objKey == $key.'_done'){
									if($objData == 0){
										unset($dateFields[$objKey]);
									}
								}
							}

						}



						usort($dateFields, $compareByTimestamp);

						if($field['options']['aggregationType'] == 'least_urgent'){

							$dateFields = array_reverse($dateFields);

						}
// echo '<pre>';
// var_dump($dateFields);
// echo '</pre>';
// die();
						$dateFields = array_filter($dateFields);
						$dateFields = array_values($dateFields);

						$objectData[$key] = $dateFields[0];
						$obj[$key] = $dateFields[0];

						$selectedField;
						foreach($obj as $k => $value) {
							if ($value == $obj[$key] && $k != $key) {
								$obj[$key.'_refId'] = $k;
								break;
							}
						}
// echo '<pre>';
// var_dump($dateFields);
// echo '</pre>';
// echo $obj[$key].'<br />';
// echo $obj[$key.'_refId'].'<br /><br />';
// die();
						break;

					}

					// Set default values, according to the object's blueprint.
					if (
						$newObj == 1
						&& empty($objectData[$key])
						&& $field['options']
						&& $field['options']['_default']
						&& !empty($field['options']['_default'])
					) {

						$obj[$key] = $field['options']['_default'];

						// Keep objs from being stored in objectId properties
						if (
							$field['type'] === 'objectId'
							&& is_array($obj[$key])
							&& is_int($obj[$key]['id'])
						) {
							$obj[$key] = $obj[$key]['id'];
						}

					}

					// encrypt private properties data
					/*
	if($field['encrypt']){

						$obj[$key] = $this->encrypt_property($obj[$key]);

					}
	*/

				}

				if($obj['parent'] && $skipParent == false){

					$parentObj = $this->getById('',$obj['parent']);
					$parentBlueprint = $this->getBlueprint($parentObj['object_bp_type']);

					foreach($parentBlueprint as $key => $field){

						if($field['is_archived'] !== true){

							switch($field['fieldType']){

								case 'date-rollup':

									$childObjects = $this->where($obj['object_bp_type'], [
											'parent' => $obj['parent'],
											'id' => [
												'type' => 'not_equal',
												'value' => $obj['id']
											]
										]);

									$dateCompare = function ($a, $b) use ($parentBlueprint, $key) {

										try {

											$aDate = new DateTime($a[$parentBlueprint[$key]['options']['field']]);
										} catch (Exception $e) {
											// echo $e->getMessage();
											// exit(1);
										}

										try {

											$bDate = new DateTime($b[$parentBlueprint[$key]['options']['field']]);
										} catch (Exception $e) {
											// echo $e->getMessage();
											// exit(1);
										}

										return $aDate <=> $bDate;

									};

									array_push($childObjects, $objectData);
	// echo '<pre>';
	// var_dump($objectData[$parentBlueprint[$key]['options']['field']]);
	// echo '</pre>';
	// die();
// echo $parentBlueprint[$key]['options']['field'];
// die();
									$toSort = [];
									foreach($childObjects as $child){

										if($child[$parentBlueprint[$key]['options']['field']]){
											array_push($toSort, $child);
										}

									}

									usort($toSort, $dateCompare);

									if($parentBlueprint[$key]['options']['aggregationType'] == 'least_urgent'){

										$toSort = array_reverse($toSort);

									}
	// echo '<pre>';
	// //echo $parentBlueprint[$key]['options']['field'];
	// var_dump($toSort);
	// echo '</pre>';
	// die();
									$updateArray = [
										'id' => $parentObj['id']
										, $key => $toSort[0][$parentBlueprint[$key]['options']['field']]
										, $key.'_refId' => $toSort[0]['id']
									];
	// echo '<pre>';
	// var_dump($updateArray);
	// echo '</pre>';
	// die();
									$parentUpdated = $this->update($parentObj['object_bp_type'], $updateArray);

									break;

								case 'reference-calculation':
	// error_reporting(E_ALL);
	// ini_set('display_errors', '1');
	// echo '<pre>';
	// //var_dump($childrenProper);
	// var_dump($objectData);
	// echo '</pre>';
	// echo '<br />';
									if($field['options']['set'] == $objectType){

										$childTaggedWith = $this->where(
											$field['options']['set']
											, [
												'tagged_with' => [$obj['parent']]
											]
										);

										$childrenProper = $this->where(
											$field['options']['set']
											, [
												'parent' => $obj['parent']
											]
										);
	// echo '<pre>';
	// var_dump($childrenProper);
	// echo '</pre>';
	// die();

										$allChildren = array_merge($childTaggedWith, $obj);
										$allChildren = array_merge($allChildren, $childrenProper);
										$allChildren = array_unique($allChildren, SORT_REGULAR);

										$parentObj[$key] = 0;
										switch($field['options']['aggregationType']){

											default:
	//echo $field['name'].' - '.$key.'<br />';
												// includes 'sum' type
												$ids = [];
												if(is_numeric($obj[$field['options']['field']])){
													$ids[] = $obj['id'];

													if($objectData[$field['options']['field']]){
														$parentObj[$key] = $objectData[$field['options']['field']];
													}else{
														$parentObj[$key] = $obj[$field['options']['field']];
													}


												}

												// foreach($childTaggedWith as $child){
												//
												// 	if(!in_array($child['id'], $ids)){
												// 		if(is_numeric($child[$field['options']['field']])){
												// 			$ids[] = $child['id'];
												// 			$parentObj[$key] += $child[$field['options']['field']];
												// 		}
												// 	}
												//
												// }
	// echo 'Children '. count($childrenProper).'<br />';
	// var_dump($ids);
												foreach($childrenProper as $child){
	//echo $child['id'].'child<br />';
													if(!in_array($child['id'], $ids)){
	//echo 'in child<br />';
														if(is_numeric($child[$field['options']['field']])){
	// echo '<pre>';
	// //var_dump($childrenProper);
	// var_dump($child);
	// echo '</pre>';
	// echo '<br />';
	//echo $child['name'].' - '. $field['options']['field'] .' - '.$child[$field['options']['field']].' - field value<br />';
															$ids[] = $child['id'];
															$parentObj[$key] += $child[$field['options']['field']];
														}
													}

												}

										}
	// echo '<pre>';
	// var_dump($field);
	// var_dump($parentObj[$key]);
	// echo '</pre>';
	// echo '<br />';
	//die();
										$this->update($parentObj['object_bp_type'], ['id'=>$parentObj['id'], $key=>$parentObj[$key]]);

									}

									break;

							}

						}

					}

				}

			}

			// Merge in obj data
			if (!$obj['is_template']) {

				foreach ($toMerge as $merge) {

					switch ($merge['fieldType']) {

						case 'title':
							$merged = $this->runSteps(
								$obj
								, [
									'merge' => [
										'obj' => 		$obj
										, 'template' => $obj[$merge['key']]
										, 'format' => 	'plain-text'
										, 'parent' => 	$obj['parent']
									]
								]
								, true
							)['memo'];
							break;

						// This is an older path, using the 'mergeText' script instead of the
						// newer 'merge' action. Its currently used for the address and plain-text fields.
						default:
							$merged = $this->runSteps(
								$obj
								, [
									'mergeText' => [
										'obj' => 		$obj
										, 'template' => $obj[$merge['key']]
										, 'field' => 	$merge['field']
									]
								]
								, true
							)['memo'];
							break;

					}

					if ($merged !== null) {

						$obj[$merge['key']] = $merged;

					}

				}

			}

			// For group objs, set the initial state, if it is not set
			if (
				$obj['object_bp_type'] === 'groups'
				&& !$obj['state']
			) {

				// Get the workflow (in this case, the type)
				$workflowObj = $this->getById(
					''
					, $obj['type']
					, [
						'states' => true
					]
				);

				if (
					is_array($workflowObj)
					&& is_array($workflowObj['states'])
				) {

					// set to initial state
					$obj['state'] = __::find(
						$workflowObj['states']
						, function($state) {

							return $state['isEntryPoint'] == 1;

						}
					)['uid'];

				}

			}

			//apply properties only for RCA
            if($obj['object_bp_type']){

                //client info request
                $startWith = "#990_Follow_up";
                $len = strlen($startWith);
                $isClientRequest = (substr($obj['object_bp_type'], 0, $len) === $startWith);

                //client info request
                $startWith = "#Review";
                $len = strlen($startWith);
                $isClientReview = (substr($obj['object_bp_type'], 0, $len) === $startWith);

                if($isClientReview || $isClientRequest){
                    if($objectData['rca_last_updated_comments']) {
                        $obj['rca_last_updated_comments'] = $objectData['rca_last_updated_comments'];
                    }
                    if($objectData['rca_last_updated_uploads']) {
                        $obj['rca_last_updated_uploads'] = $objectData['rca_last_updated_uploads'];
                    }
                }

            }

			$obj['last_updated'] = $now->format('Y-m-d H:i:s');
			$obj['last_updated_by'] = intval($_COOKIE['uid']);

			return $obj;


		}

		private function parseArrayForInput($data){

			if(is_array($data)){

				foreach($data as $key => $datum){

					$data[$key] = $this->parseArrayForInput($datum);

				}

			}elseif(is_string($data)){

				$data = trim($data, "'");

			}elseif(is_int($data)){

// 				$data = preg_replace("/[^0-9]/", "", $data);

			}elseif(is_float($data)){

				$data = $data;

			}else{

				$data = $data;

			}

			return $data;

		}

		private function pbkdf2($algorithm, $password, $salt, $count, $key_length, $raw_output = false){

		    $algorithm = strtolower($algorithm);

		    if(!in_array($algorithm, hash_algos(), true))

		        trigger_error('PBKDF2 ERROR: Invalid hash algorithm.', E_USER_ERROR);

		    if($count <= 0 || $key_length <= 0)

		        trigger_error('PBKDF2 ERROR: Invalid parameters.', E_USER_ERROR);



		    if (function_exists("hash_pbkdf2")) {

		        // The output length is in NIBBLES (4-bits) if $raw_output is false!

		        if (!$raw_output) {

		            $key_length = $key_length * 2;

		        }

		        return hash_pbkdf2($algorithm, $password, $salt, $count, $key_length, $raw_output);

		    }



		    $hash_length = strlen(hash($algorithm, "", true));

		    $block_count = ceil($key_length / $hash_length);



		    $output = "";

		    for($i = 1; $i <= $block_count; $i++) {

		        // $i encoded as 4 bytes, big endian.

		        $last = $salt . pack("N", $i);

		        // first iteration

		        $last = $xorsum = hash_hmac($algorithm, $last, $password, true);

		        // perform the other $count - 1 iterations

		        for ($j = 1; $j < $count; $j++) {

		            $xorsum ^= ($last = hash_hmac($algorithm, $last, $password, true));

		        }

		        $output .= $xorsum;

		    }



		    if($raw_output)

		        return substr($output, 0, $key_length);

		    else

		        return bin2hex(substr($output, 0, $key_length));

		}

	}
