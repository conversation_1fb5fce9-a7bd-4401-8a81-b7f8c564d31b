<?php

// error_reporting(E_ALL);
// ini_set('display_errors', '1');

header('Access-Control-Allow-Origin: *');

if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
}

define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );

require_once APP_ROOT.'_objects.php';

require_once APP_ROOT.'_pgObjectsMT.php';

require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
require_once APP_ROOT.'stripe/init.php';

if(!empty($_REQUEST['do'])){

	header('Access-Control-Allow-Origin: *');
	
	if($_REQUEST['pagodaAPIKey'] == 'petermikhail' or $_REQUEST['pagodaAPIKey'] == 'rickydev' or $_REQUEST['pagodaAPIKey'] == 'pagodastaging' or $_REQUEST['pagodaAPIKey'] == 'zachvoltz' or $_REQUEST['pagodaAPIKey'] == 'joshgantt'){
		require_once '../_app/_config.php';
	}else{
		require_once APP_ROOT.'../app/_app/_config.php';
	}
	
	$objects = new Objects($pdo);

	$pgObjects = new pgObjects($pdo, $appConfig['instance']);
	
	$comms = new Comm($pgObjects, $appConfig);
	
	$cookies = new Cookies($pgObjects);
	
	$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey']);

	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
	
	$cookie = $cookies->checkCookie($_COOKIE['uid'], 'user', $_COOKIE['series'], $_COOKIE['token']);

	if(!empty($_REQUEST['json'])){
		$_POST = json_decode($_REQUEST['json']);
	}
	
	if($_REQUEST['email']){
		
		$pgObjects->delete('contact_info', $_REQUEST['email']);
		
	}
		
}
	
?>

<!DOCTYPE html>
<html>
<title>W3.CSS Template</title>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">

<body>
	
	<h1 style="text-align: center; font-family: sans-serif;">You have been unsubscribe successfully.</h1>
	
</body>

</html>
