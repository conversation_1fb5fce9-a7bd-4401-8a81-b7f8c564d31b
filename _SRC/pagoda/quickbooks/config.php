<?php
return array(
  'authorizationRequestUrl' => 'https://appcenter.intuit.com/connect/oauth2', //Example https://appcenter.intuit.com/connect/oauth2',
  'tokenEndPointUrl' => 'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer', //Example https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer',
  'client_id' => 'ABNf53gLPiYpXltIAdZxewQdLgg2gpsx0SQk7WwnrqcNu0aaTt', //Example 'Q0wDe6WVZMzyu1SnNPAdaAgeOAWNidnVRHWYEUyvXVbmZDRUfQ',
  'client_secret' => 'teeY2C2lo5ezzgW6k26oc6FjwNz10k99QCH8qdKJ', //Example 'R9IttrvneexLcUZbj3bqpmtsu5uD9p7UxNMorpGd',
  'oauth_scope' => 'com.intuit.quickbooks.accounting', //Example 'com.intuit.quickbooks.accounting',
  'openID_scope' => '', //Example 'openid profile email',
  'oauth_redirect_uri' => 'http://localhost:8080/api/quickbooksOauth.php', //Example https://d1eec721.ngrok.io/OAuth_2/OAuth2PHPExample.php',
//   'openID_redirect_uri' => 'https://bento.infinityhospitality.net/api/quickbooks/OAuthOpenIDExample.php',//Example 'https://d1eec721.ngrok.io/OAuth_2/OAuthOpenIDExample.php',
  'mainPage' => 'https://bento.infinityhospitality.net/app/quickbooks/index.php', //Example https://d1eec721.ngrok.io/OAuth_2/index.php',
  'refreshTokenPage' => 'http://localhost:8080/api/quickbooks/RefreshToken.php', //Example https://d1eec721.ngrok.io/OAuth_2/RefreshToken.php'
)
