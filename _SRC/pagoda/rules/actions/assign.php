<?php 
	
	return [
		'name' 		=> 'assign',
		'process'	=> function ($setup, $run) {
			
			//error_reporting(E_ALL);
			//ini_set('display_errors', '1');
			$objs 	   = $setup['objs'];
			$old 	   = $setup['old'];
			$new 	   = $setup['new'];
			$options   = $setup['setup'];
			$appConfig = $setup['appConfig'];

			$assignment = $options['assignment'];
			$property   = $options['property'];
			$successMsg = $options['successMsg'];
			
			$assignment = $objs->getById(
				'', 
				$assignment, 
				[]
			);
			
			$assignment[$property] = $new['id'];
			
			$updated = $objs->update(
				$assignment['object_bp_type'],
				$assignment,
				1,
				null
			);
			
			return [
				'msg' => $successMsg
				, 'update' => $updated
			];
			
		}
	];
	
?>