<?php

return [
	'name' 		=> 'addSubs'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];

		// find tags to apply, based on setup
		$tags = [];
		$notificationTxt = 'You are now subscribed to '. $new['name'];
		
		if (
			is_array($setup['options'])
			&& is_array($setup['options']['tags'])
		) {
			
			foreach ($setup['options']['tags'] as $i => $tagId) {
				
				array_push(
					$tags
					, intval($tagId)
				);
				
			}
			
		}
		
		
		$applyTo = $new['id'];
		if (
			$setup['options']
			&& $setup['options']['applyTo'] === 'child_entities'
		) {
			
			$children = $objs->where(
				'#.'
				, [
					'tagged_with' => $new['id']
				]
				, ''
				, [
					'id' => true
					, 'name' => true
				]
			);
			
			$applyTo = __::pluck(
				$children
				, 'id'
			);
			
		}
		
		// Update tags, if they exist.
		if (!empty($tags)) {
			
			foreach ($tags as $tag) {
				
				$updated = $objs->tagObject(
					$applyTo
					, $tag
					, 'notify'
				);
				
			}
			
			// Fire notifications.
			$notification = [];
			$notification['title']    = $notificationTxt;
			$notification['details']  = '';
			$notification['color']    = 'purple';
			$notification['icon']     = 'at';
			$notification['type']     = 'general';
			$notification['producer'] = $new['id'];			
			$notification['link']     = $link;
			$notification['notify']   = $tags;

			$run('notify', $notification);
			
			if (is_array($applyTo)) {
				
				$update = [];
				foreach ($applyTo as $upd) {
					
					array_push(
						$update
						, [
							'id' => $upd
							, 'notify' => $updated
						]
					);
					
				}
				
			} else {
				
				$update = [
					'id' => $new['id']
					, 'notify' => $updated
				];
				
			}

			return [
				'applyTags' => 	[
					'response' => 	$updated
				]
				, 'msg' =>		'Subscriptions added to '. $new['name'] .'.'
				, 'update' => 	$update
			];
			
		}
		
		return false;
		
	}
];

?>