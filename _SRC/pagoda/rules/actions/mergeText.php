<?php

function buildSelectionArr ($props = null, $memo = null, $i = null) {
	
	// Set initial state
	if ($memo == null) {
		$memo = [];
	}
	if ($i == null) {
		$i = 0;
	}
	$memo['name'] = true;
	
	if ($props[$i]) {
		
		// If final property, just set the value to true
		if (!$props[$i + 1]) {
			
			$memo[$props[$i]] = true;
		
		// Get nested selection obj
		} else {
			
			$memo[$props[$i]] = buildSelectionArr(
				$props
				, $memo
				, $i + 1
			);
			
		}
		
	}
	
	return $memo;
	
}

function getValueAtPath ($path, $graph = null, $i = null, $objs = null) {
	
	// Set initial state
	if ($i == null) {
		$i = 0;
	}
	$val = '';
	
	if ($path[$i]) {
		
		// If final step, set the value to the value there
		if (!$path[$i + 1]) {
			
			// Special cases
			switch ($path[$i]) {
				
				case 'primary_address':
					$primaryContactInfo = $objs->where(
						'contact_info'
						, [
							'object_id' => 		$graph['id']
							, 'is_primary' => 	'yes'
						]
						, ''
						, [
							'info' => [
								'data_type' => true
							]
							, 'type' => true
							, 'state' => true
							, 'street' => true
							, 'street2' => true
							, 'zip' => true
							, 'city' => true
							, 'country' => true
						]
					);
					foreach ($primaryContactInfo as $info) {
						
						if ($info['type']['data_type'] === 'address') {
							
							$val = [
								'street' => 	$info['street']
								, 'add2' => 	$info['street2']
								, 'city' => 	$info['city']
								, 'state' => 	$info['state']
								, 'zip' => 		$info['zip']
								, 'country' => 	$info['country']
							];
							
						}
						
					}
					
					break;
					
				default:

					// Pointers to objs
					if (is_array($graph[$path[$i]]) && $graph[$path[$i]]['id']) {

						$val = $graph[$path[$i]]['id'];

					// Entities within the space (#[set name])
					} elseif (is_array($graph[0][$path[$i]]) && is_array($graph[0][$path[$i]])) {

						if (is_int($graph[0][$path[$i]]['id'])) {
							$val = $graph[0][$path[$i]]['id'];
						} elseif (count($graph[0][$path[$i]]) > 0) {
							$val = __::pluck($graph[0][$path[$i]], 'id');
						}

					// Straight values at the given property
					} else {

						$val = $graph[$path[$i]];

					}
					break;
				
			}
			
		// Get value nested within
		} else {
			
			$val = getValueAtPath(
				$path
				, $graph[$path[$i]]
				, $i + 1
				, $objs
			);
			
		}
		
	}
	
	if ($val == null) {
		$val = '';
	}
	
	return $val;
	
}

function mergeTag ($objs, $text, $rootObj, $field) {
	
	// If a tag is set within the template text, get 
	// the value and replace it.
	// Currently, just set up to work with one tag at a time.
	$startTag = strpos($text, '{{');
	$endTag = strpos($text, '}}');

	if (
		$startTag !== false
		&& $endTag !== false
		&& $startTag < $endTag
	) {
		
		// Get just the tag text to interpret
		$tag = substr(
			$text
			, $startTag + 2
			, $endTag - 2
		);
		
		// For tags following the format of 
		// {{this.property.property.property}}
		if (substr($tag, 0, 4) === 'this') {
			
			$toks = explode('.', $tag);
			foreach ($toks as $i => $tok) {
				$toks[$i] = str_replace('-', '.', $tok);
			}

			$select = buildSelectionArr($toks);
			
			// !This can just be if the value is a pointer
			if ($toks[1] === 'parent') {
				
				$parentId = $rootObj['parent'];
				if (is_array($parentId) && $parentId['id']) {
					$parentId = $parentId['id'];
				}

				// Get nested object data
				$data = $objs->getById(
					''
					, $parentId
					, $select['this']['parent']
					, true
				);

				// Check if value is set, and merge it in
				$text = getValueAtPath(
					array_slice($toks, 2)
					, $data
					, null
					, $objs
				);
				
			}
			
		} elseif (substr($tag, 0, 2) === 'me') {

			switch ($field['fieldType']) {
				
				case 'user':
					$text = intval($_COOKIE['uid']);
					break;

			}

		} elseif ($tag === 'today') {

			if (
				$GLOBALS 
				&& $GLOBALS['MERGE_VARS'] 
				&& $GLOBALS['MERGE_VARS']['today']
			) {
		
				$currentDate = new DateTime($GLOBALS['MERGE_VARS']['today']);
				
			} else {
					
				$currentDate = new DateTime();
				$currentDate->setTimezone(new DateTimeZone('UTC'));
				if ($_COOKIE && $_COOKIE['tz_off']) {
					$interval = new DateInterval('PT'. $_COOKIE['tz_off'] .'M');
					$interval->invert = 1;
					$currentDate->add($interval);
				}

			}
			
			switch ($field['fieldType']) {
				
				case 'date':
					$text = $currentDate->format('Y-m-d');
					break;

			}

		}

		return $text;
		
	} else {

		return $setup['setup']['template'];

	}

}

return [
	'name' 		=> 'mergeText'
	, 'process'	=> function ($setup, $run) {

		// Objects api
		$objs = $setup['objs'];
		
		// Text to merge
		$text = $setup['setup']['template'];
		
		// The root obj
		$rootObj = $setup['setup']['obj'];
		
		// The field definition of the field to merge to
		$field = $setup['setup']['field'];
		switch ($field['fieldType']) {
			
			case 'address':
				if (is_array($text)) {
					$text = $text['street'];
				}
				break;
			
		}
		if (is_array($text)) {
			
			$ret = [];
			foreach ($text as $txt) {

				array_push(
					$ret
					, mergeTag($objs, $txt, $rootObj, $field)
				);

			}

			// Flatten the array
			$tmp = [];
			foreach ($ret as $v) {
				if (is_array($v)) {
					foreach ($v as $w) {
						array_push($tmp, $w);
					}
				} else {
					array_push($tmp, $v);
				}
			}
			// $ret = array_merge(...array_values($ret));
			$ret = $tmp;

		} else {

			$ret = mergeTag($objs, $text, $rootObj, $field);

		}

		return [
			'memo' => $ret
		];
		
	}
];

?>