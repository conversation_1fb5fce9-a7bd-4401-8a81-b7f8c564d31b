<?php

return [
	'name' 		=> 'FGClientApprove990'
	, 'process'	=> function ($setup, $run) {

		// Get setup info
		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		
		// Upload file to History
		// Create new deliverable uploads
		$service = $objs->where('#HDIjlE', ['parent' => $new['parent']])[0];
		
		$package = $objs->create('#D3yxcG', [
			'name' => 			$new['name'] .' | Package'
			, 'parent' => 		$service['id']
			, 'tagged_with' => 	[$service['id']]
			, '_2' => 			[$new['_1'][0]]
		]);

		$approvalLetter = $objs->create('#D3yxcG', [
			'name' => 			$new['name'] .' | Signed 8879-EO'
			, 'parent' => 		$service['id']
			, 'tagged_with' => 	[$service['id']]
			, '_2' => 			[$new['_7']]
		]);

		// Link upload to the Service obj key '_14'
		if (!is_array($service['_14'])) {
			$service['_14'] = [];
		}
		array_push($service['_14'], $package['id']);
		array_push($service['_14'], $approvalLetter['id']);
		$objs->update('#HDIjlE', ['id' => $service['id'], '_14' => $service['_14']]);

        // Get other states in that project
		// $stateReviewRecords = $objs->where('#Review', ['parent' => $new['parent']]);
		// $allDone = true;
		// if (is_array($stateReviewRecords)) {
		// 	foreach ($stateReviewRecords as $i => $reviewRec) {
		// 		if ($reviewRec['status'] !== 'done') {
		// 			$allDone = false;
		// 		}
		// 	}
		// }

		// If all other states are also complete, update the project state
		// if ($allDone) {
			// $proj = $objs->getById('groups', $new['parent']);
			// done state -> 13
			// $objs->update('groups', ['id' => $new['parent'], 'state' => 13]);
		// }
		
		return false;
		
	}
];

?>