<?php

return [
	'name' 		=> 'createFG360Membership'
	, 'process'	=> function ($setup, $run) {


		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$setup = 	$setup['setup'];

        // Core Service keys
        $taxYearKey = '_1';
		$mainContactKey = '_11';

		// Client Service keys
		$servicesBpKey = '#HDIjlE';
		$servicesYearKey = '_4';
		$servicesStateOfIncKey = '_5';

        // Don't allow creation without a tax year set on the core service
		if (empty($obj[$taxYearKey])) {

			return [
				'msg' => 'A <strong>Tax Year</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		} else {

            // // Check for the tag for the specific year
            $yearTag = $objs->where(
                'system_tags'
                , [
                    'tag' => strval($obj[$taxYearKey])
                ]
                , ''
                , [
                    'tag' => true
                ]
            )[0];

            // // Create a tag for the year if it does not exist
            if (empty($yearTag)) {
    
                $yearTag = $objs->create(
                    'system_tags'
                    , [
                        'name' => 		strval($obj[$taxYearKey])
                        , 'tag' => 		strval($obj[$taxYearKey])
                        , 'color' => 	'black'
                    ]
                    , 0
                    , [
                        'tag' => true
                    ]
                );
    
            }
            
        }

        /// Don't allow creation without a Main Contact set on the core service
		if ( empty($obj[$mainContactKey]) ) {

			return [
				'msg' => 'A <strong>Main Contact</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		} else {

            $mainContact = $objs->getById(
                'contacts'
                , $obj[$mainContactKey]
                , [
                    'name' => 	true
                    , 'fname' => 	true
                    , 'lname' => 	true
                    , 'company' => true
                ]
            );
            $mainContactCompany = $mainContact['company'];

        }

        $membershipProject = $objs->where(
            'groups'
            , [
                'parent' => $mainContactCompany['id']
                , 'type' => 9234871
                , 'category' => 10292830
            ]
            , ''
            , [
                'name' => true
            ]
        )[0];

        ///IF there is not a previously created Membership project, then create one ELSE use already made project
        if ( empty($membershipProject) ) {

            $tagged_withDefaults = [$yearTag['id'], $mainContact['id'],  $mainContactCompany['id']];

            $projectTemplate = array();
            /// FG 360 Membership Project Template (Production - FG)
            $projectTemplate = $objs->getById(
                'groups'
                , 10292745
            );

            $projectTemplate['tagged_with'] = array_merge($projectTemplate['tagged_with'], $tagged_withDefaults);
            $projectTemplate['status'] = 'open';
            $projectTemplate['shared_with'] = [ $mainContact['id'] ];

            $projectDefaults = array();
            $projectDefaults['name'] = 'FG 360 Membership';
            ///Shared_with::Org Contacts
            $projectDefaults['shared_with'] = array( $mainContact['id'] );
            $projectDefaults['main_contact'] = $mainContact['id'];
            ///Template::Project Template ID
            $projectDefaults['data_source_id'] = $projectTemplate['id'];
            ///Parent::Organization
            $projectDefaults['parent'] = $mainContactCompany['id'];
            $projectDefaults['state'] = 2;
            $projectDefaults['status'] = 'open';
            $projectDefaults['shared_with'] = [ $mainContact['id'] ];

            $membershipProject = $objs->castFromTemplate($projectTemplate, null, $projectDefaults);

        } 

        $membership = $objs->where(
            '#0ZZQgI'
            , [
                'parent' => $membershipProject['id']
            ]
            , ''
            , [
                'name' => true
            ]
        )[0];

        if ( empty($membership) ) {

            $membershipTemplate = array();
            $membershipTemplate['tagged_with'] = $membershipProject['tagged_with'];
            $membershipTemplate['tagged_with'] = array_merge($membershipTemplate['tagged_with'], array($membershipProject['id']));
            $membershipTemplate['parent'] = $membershipProject['id'];
            $membershipTemplate['shared_with'] = [ $mainContact['id'] ];
            // The Close Date is Start Date for Membership
            $membershipTemplate['_11'] = $obj['_58'];
            
            // The Expiration Date is one year following the Start Date
            $startDate = new DateTime($membershipTemplate['_11']);  
            $start = $startDate->format('Y');
    
            $endDate = $startDate->add(new DateInterval('P1Y'));
            $end = $endDate->format('Y');
    
            $membershipTemplate['_16'] = $endDate->format('Y-m-d H:i:s');
    
            $membershipTemplate['name'] = 'FG 360 Membership | '. $start . ' - '. $end;
    
            $membership = $objs->create('#0ZZQgI', $membershipTemplate);

        }
		// Return message
		return [
			'msg' => '<strong>'. $membership['name'] .'</strong> <i>FG360 Membership</i> created.'
		];
	}
];

?>