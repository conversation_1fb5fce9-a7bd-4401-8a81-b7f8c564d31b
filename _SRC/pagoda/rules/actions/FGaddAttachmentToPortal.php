<?php

return [
	'name' 		=> 'FGAddAttachmentToPortal'
	, 'process'	=> function ($setup, $run) {

		// Get setup info
		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];

        ///updated Client Info Request Obj
		$new = 		$setup['new'];

		$setup = 	$setup['setup'];
        $options =  $setup['options'];

		// Upload file to History
		// Create new deliverable uploads
		$service = $objs->where('#HDIjlE', ['parent' => $new['parent']])[0];


        $packageTaggedWith = array_merge($new['tagged_with'], $service['tagged_with'], array($service['id']));
        $packageTaggedWith = array_unique($packageTaggedWith);

        //get all documents for created packages
        $counter = 0;

        if($options) {

            $resourcesDocs = array($options['currentDocs'] , $options['allDocs']);
            foreach($resourcesDocs as $pointerDoc) {
                if($pointerDoc) {
                    foreach ($pointerDoc as $docSelected) {
                        $uploadedDocument = $objs->where('document', ['id' => intval($docSelected)])[0];
                        //setup package
                        $packageSetup = array(
                          'name' => $uploadedDocument['name'] . ' | Package'
                        , 'parent' => $service['id']
                        , 'tagged_with' => $packageTaggedWith
                        , '_2' => $uploadedDocument['id']
                        );

                        ///create Deliverable Obj aka package
                        $package = $objs->create(
                            '#D3yxcG'
                            , $packageSetup
                            , 0
                            , [
                                'tag' => true
                            ]
                        );
                        // Link upload to the Service obj key '_14'
                        if (!is_array($service['_14'])) {
                            $service['_14'] = [];
                        }

                        array_push($service['_14'], $package['id']);

                        $updatedClientService = $objs->update('#HDIjlE',
                            [
                                'id' => $service['id']
                                , '_14' => $service['_14']
                            ]
                        );
                        $counter++;
                    }
                }
            }
            }


		return [
            'msg' => '<strong>'. $counter .'</strong> files was created and the Attachment was shared to the Portal.'
        ];
		
	}
];

?>
