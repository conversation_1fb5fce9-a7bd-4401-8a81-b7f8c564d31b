<?php

return [
	'name' 		=> 'copyMenuFromTemplate'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new'];
		$setup = 	$setup['setup'];
				
		$menuObjOnTemplate = $objs->getById('', intval($setup['template']), 1);
		$menuObjOnParent = $objs->getById('', intval($setup['menu']), 1);
		
		$menuObjOnTemplate['sections'] = array();

		// For now, just make sure the category is up to date
		$productIds = [];
		foreach ($menuObjOnParent['sections'] as $key => $value) {
			foreach($value['items'] as $itemKey => $item) {
				
				if ($item['item'] and $item['item']['id']) {
					array_push($productIds, intval($item['item']['id']));
				}
				
			}
		}
		$productIds = array_unique($productIds);
		$products = $objs->getById(
			''
			, $productIds
		);
		$categoryIds = __::pluck(
			$products
			, 'category'
		);
		
		foreach ($menuObjOnParent['sections'] as $key => $value) {
			foreach($value['items'] as $itemKey => $item) {
				
				if ($item['item'] and $item['item']['category']) {
					array_push($categoryIds, intval($item['item']['category']));
				}
				
			}
		}
		
		$categories = $objs->getById(
			''
			, $categoryIds
		);
		
		$messages = [];
		$shouldAdd = false;
		
		// date_default_timezone_set('UTC');
		foreach ($menuObjOnParent['sections'] as $key => $value) {
			
			// $value => a section
			$sectionObj = $value;
			
			// Update the section from/to dates
			if (
				array_key_exists('from', $sectionObj)
				and !empty($sectionObj['from'])
			) {

				$newStartDate = $setup['newStartDate'];
				$fromTime = strtotime($value['from']);
				$toTime = strtotime($value['to']);
				$diff = $toTime - $fromTime;

				$projectIsDST = date('I', strtotime($setup['newStartDate']));
				$templateIsDST = date('I', $fromTime);

				$start = new DateTime($newStartDate);
				$start->setTimezone(new DateTimeZone('UTC'));
				if ($_COOKIE && $_COOKIE['tz_off']) {
					$interval = new DateInterval('PT'. $_COOKIE['tz_off'] .'M');
					$interval->invert = 1;
					$start->add($interval);
				}
				if (date('H:i:s', $fromTime) >= '00:00:00' && date('H:i:s', $fromTime) < '06:00:00') {
					$start->modify('+ 1 day');
				}
				$start->setTime(date('H', $fromTime), date('i', $fromTime), 0);				

				$to = new DateTime($newStartDate);
				$to->setTimezone(new DateTimeZone('UTC'));
				if ($_COOKIE && $_COOKIE['tz_off']) {
					$interval = new DateInterval('PT'. $_COOKIE['tz_off'] .'M');
					$interval->invert = 1;
					$to->add($interval);
				}
				if (date('H:i:s', $fromTime) >= '00:00:00' && date('H:i:s', $fromTime) < '06:00:00') {
					$to->modify('+ 1 day');
				}
				$to->setTime(date('H', $fromTime), date('i', $fromTime), 0);
				$to->add(date_interval_create_from_date_string(intval($diff) .' seconds'));
				
				if (!$projectIsDST && $templateIsDST) {
					$start->modify('+ 1 hour');
					$to->modify('+ 1 hour');
				} else if ($projectIsDST && !$templateIsDST) {
					$start->modify('- 1 hour');
					$to->modify('- 1 hour');
				}
				
				$sectionObj['from'] = $start->format('Y-m-d H:i:s');
				$sectionObj['to'] = $to->format('Y-m-d H:i:s');
				
			}
			
			$sectionObj['items'] = array();
			
			foreach($value['items'] as $itemKey => $itemValue) {

				// $itemValue => item
				
				if (
					$itemValue['object_bp_type'] === 'inventory_menu_line_item'
				) {
					
					$shouldAdd = true;
					$newItem = $itemValue;
					
					// Make sure the category is up to date (for moved products)
					if ($itemValue['item'] && $itemValue['item']['id']) {
						
						$product = __::find($products, function($prod) use($itemValue) {
							return intval($prod['id']) === intval($itemValue['item']['id']);
						});
						
						// Set the line item category to most up to date val
						if (!empty($product)) {
							
							$newItem['item']['category'] = 			$product['category'];
							$newItem['item']['name'] = 				$product['name'];
							$newItem['item']['items'] = 			$product['items'];
							$newItem['item']['price'] = 			$product['price'];
							$newItem['item']['price_per_person'] = 	$product['price_per_person'];
							$newItem['item']['price_per_hour'] = 	$product['price_per_hour'];
							$newItem['item']['price_per_hour_per_person'] = 	$product['price_per_hour_per_person'];
							$newItem['item']['description'] = 		$product['description'];

							// Set the line item chart of account to most up to date val
							$category = __::find($categories, function($cat) use($newItem) {
								return intval($cat['id']) === intval($newItem['item']['category']);
							});
							
							// Get additional pricing from choices
							$fullRecipe = $GLOBALS['app']->get_full_recipe($product);
							$assignData = $GLOBALS['app']->assignMenuItem(
								$menuObjOnTemplate
								, $sectionObj
								, $fullRecipe
								, $newItem['qty']
								, $newItem['id']
								, $newItem['choices']
								, $newItem['qty_type']
								, true
							);

							if(is_array($assignData['selections_strings']) and count($assignData['selections_strings']) > 0){
								$newItem['item']['description'] = $product['description'] .' w/' . implode(', ', $assignData['selections_strings']);
								$newItem['item']['ingredient_names'] = $assignData['selections_strings'];
							}

							$newItem['absolute_qty'] = $assignData['absolute_qty'];
							
							switch ($newItem['qty_type']) {
								
								case 'per_guest':
								case 'guest_count':
									$newItem['item']['price_per_person'] = $assignData['item_price'];
									break;
									
								case 'per_hour':
									$newItem['item']['price_per_hour'] = $assignData['item_price'];
									break;
								
								case 'per_hour_per_guest':
									$newItem['item']['price_per_hour_per_person'] = $assignData['item_price'];
									break;
								
								default:
									$newItem['item']['price'] = $assignData['item_price'];
									break;
								
							}
							
							if ($category) {

								$coa = $objs->getById(
									'chart_of_accounts'
									, $category['chart_of_account']
								);

								if (empty($coa)) {

									// Cannot add a product without of a chart of account
									$shouldAdd = false;
									array_push(
										$messages
										, '<strong>'. $newItem['item']['name'] .'</strong> is in a category without a valid chart of account, so was not added to the invoice.
										Link the <strong>'. $category['name'] .'</strong> category to a chart of account to allow for adding this item to invoices.'
									);

								} else {

									$newItem['coa'] = $category['chart_of_account'];

								}

							} else {

								// Cannot add a product without a valid category
								$shouldAdd = false;
								array_push(
									$messages
									, '<strong>'. $newItem['item']['name'] .'</strong> does not have a valid category, so was not added to the invoice.'
								);

							}

						} else {

							// Cannot add a product that has been archived
							$shouldAdd = false;
							array_push(
								$messages
								, '<strong>'. $newItem['item']['name'] .'</strong> no longer exists, so was not added to the invoice.'
							);

						}
						
					} elseif (!empty($newItem)) {
						
						// Set the line item chart of account to most up to date val
						$category = __::find($categories, function($cat) use($newItem) {
							return intval($cat['id']) === intval($newItem['item']['category']);
						});
						
						if (
							!empty($category)
							&& !empty($category['surcharges'])
						) {
							$newItem['surcharges'] = $category['surcharges'];
						}

						// Set the line item chart of account to most up to date val
						$category = __::find($categories, function($cat) use($newItem) {
							return intval($cat['id']) === intval($newItem['item']['category']);
						});
						
						if ($category) {

							$coa = $objs->getById(
								'chart_of_accounts'
								, $category['chart_of_account']
							);

							if (empty($coa)) {

								// Cannot add a product without of a chart of account
								$shouldAdd = false;
								array_push(
									$messages
									, '<strong>'. $newItem['item']['name'] .'</strong> is in a category without a valid chart of account, so was not added to the invoice.
									Link the <strong>'. $category['name'] .'</strong> category to a chart of account to allow for adding this item to invoices.'
								);

							} else {

								$newItem['coa'] = $category['chart_of_account'];

							}

						} else {

							// Cannot add a product without a valid category
							$shouldAdd = false;
							array_push(
								$messages
								, '<strong>'. $newItem['item']['name'] .'</strong> does not have a valid category, so was not added to the invoice.'
							);

						}
						
					} else {
						
						$shouldAdd = true;

					}
					
					if ($shouldAdd) {

						unset($newItem['id']);
						unset($newItem['date_created']);
						unset($newItem['created_by']);
						$newItem['menu'] = $menuObjOnTemplate['id'];
						$newItem = $objs->create('inventory_menu_line_item', $newItem, 0, 1);
						array_push($sectionObj['items'], $newItem);

					}
					
				}
				
			}

			$sectionObj['items'] = __::pluck(
				$sectionObj['items']
				, 'id'
			);
			array_push($menuObjOnTemplate['sections'], $sectionObj);
			
		}
		
		$objs->update(
			'inventory_menu'
			, [
				'id' => $menuObjOnTemplate
				, 'sections' => $menuObjOnTemplate['sections']
			]
			, 0
			, null
		);
		
		// Temporary, move this to rules layer
		if (!empty($messages)) {

			if (!array_key_exists('_resp', $GLOBALS)) {
				$GLOBALS['_resp'] = [];
			}
			if (
				!array_key_exists('_msg', $GLOBALS['_resp'])
				or !is_array($GLOBALS['_resp']['_msg'])
			) {
				$GLOBALS['_resp']['_msg'] = $messages;
			} else {
				$GLOBALS['_resp']['_msg'] = array_merge(
					$GLOBALS['_resp']['_msg']
					, $messages
				);
			}

		}

		return [
			'update' => [
				'sections' => $menuObjOnTemplate['sections']
				, '_msg' => $messages
			]
			, 'msg' => $messages
		];
		
	}
];

?>