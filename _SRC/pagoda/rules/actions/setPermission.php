<?php

return [
	'name' 		=> 'setPermission'
	, 'process'	=> function ($setup, $run) {
		
		$db = 		$setup['objs'];
		$obj = 		$setup['new'];
		$setup = 	$setup['setup'];
		$response = 	[];
		
		// Prepare for update
		switch ($obj['object_bp_type']) {
			
			case 'entity_type':
				
				try {
					
					$obj['blueprint'][$setup['property']]['permissions'] = $setup['permissions'];
					$obj = 
						$db->update(
							'entity_type'
							, [
								'id' => 				$obj['id']
								, 'blueprint' => 	$obj['blueprint']
							]
						);
					
				} catch (Exception $e) {
					
					return false;
					
				}
								
				break;
				
			default:
				return false;
			
		}
		
		return $obj;
		
		return false;
		
	}
];

?>
