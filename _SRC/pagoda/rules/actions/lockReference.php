<?php 
	
	return [
		'name' 		=> 'lockReference',
		'process'	=> function ($setup, $run) {
			
			//error_reporting(E_ALL);
			//ini_set('display_errors', '1');
			$objs 	   = $setup['objs'];
			$old 	   = $setup['old'];
			$new 	   = $setup['new'];
			$options   = $setup['setup'];
			$appConfig = $setup['appConfig'];
			
			// Get reference data
			$fieldRef 		= $options['options']['fieldName'];
			$parentFieldRef = $options['options']['options']['blueprint'][ $options['options']['fieldName'] ]['options']['parentRef'];
			$setRef 		= $options['options']['options']['blueprint'][ $options['options']['fieldName'] ]['options']['set'];
			$fieldOnReferencedItem 	= $options['options']['options']['blueprint'][ $options['options']['fieldName'] ]['options']['field'];
			$parentId 		= $new[$parentFieldRef];
			$now 			= new DateTime('now', new DateTimezone('Africa/Abidjan'));

			$childObjects = $objs->where($setRef, [
				'parent' => $parentId
			], '', 1, false, 0, 'null', 'asc', 1, null, array(), 'string', true);
			
			// Set the value, and lock the field 
			$update = array();
			$update['id'] = $new['id'];
			$update[$fieldRef . '_lockedOn'] = $now->format('Y-m-d H:i:s');
			$update[$fieldRef . '_refId'] = $childObjects[0]['id'];
			$update[$fieldRef] = $childObjects[0][$fieldOnReferencedItem];

			$updated = $objs->update(
				$new['object_bp_type'],
				$update,
				1,
				null
			);

			// Let the client know what has changed
			$update[$fieldRef] = $updated[$fieldRef];

			return [
				'msg' => 'Field validated successfully.'
				, 'update' => $update
			];
			
		}
	];
	
?>