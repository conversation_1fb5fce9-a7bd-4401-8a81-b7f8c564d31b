<?php
	
return [
	'name' => 		'changeWorkflow'
	, 'process' => 	function ($setup, $run) {
		
		$objs = 		$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 		$setup['setup'];

		$appConfig =	$objs->rules->appConfig;

		if(array_key_exists('downloaded', $setup)) {
			if ($appConfig['instance'] == 'foundation_group') {

				//form
				$object = $objs->getById(
					''
					, $new['id']
					, [
						'id' => true,
						'type' => true,
						'state' => true,
						'object_bp_type' => true
					]
				);


				$project = $object;

				//add formation type and status prev and next status

				$formations = [
					2238449 => [ 		//Project Type: Formation 1023
						10 => 11 		//Status: Not Downloaded => Downloaded
					],
					8270768 => [ 		//Project Type: Formation 1023E
						10 => 11		//Status: Not Downloaded => Downloaded
					],
					8270774 => [ 		//Project Type: Formation 1024
						10 => 11		//Status: Not Downloaded => Downloaded
					],
					8270798 => [ 		//Project Type: Formation 1024A
						10 => 11		//Status: Not Downloaded => Downloaded
					]
				];

				//$formationXXXX = [prev => new]

				//add here the new formation to watch
				$workFlowChange = $formations;

				$newState = false;
				foreach ($workFlowChange as $keyProjectType => $states) {
					if($keyProjectType == $project['type']['id']){
						foreach ($states as $keyState => $state) {
							if ($project['state'] == $keyState) {
								$newState = $state;
							}
						}
					}
				}

				if ($newState) {
					$updated = $objs->update(
						$project['object_bp_type']
						, [
							'id' => $project['id']
							, 'state' => $newState
						]
						, 0
						, null
					);

					return [
						'msg' => 'Project status has been updated '
						, 'update' => "succesds"
						, 'notifications' => "success"
					];
				} else {
					die();
				}
			}
			die();
		}
	
		$now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
		
		$object = $objs->getById(
			''
			, $new['id']
			, [
				'type' => 	true
				, 'state' =>  	true
				, 'status' => 	true
				, 'object_bp_type' => true
				, 'parent' => 	[
					'id' => true
				]
			]
		);
		$isChild = false;
		$child = [];
		
		// If it is an entity, change the state of the parent project.
		if (
			substr($object['object_bp_type'], 0, 1) === '#'
		) {
			
			$isChild = true;
			$child = $object;
			$object = $objs->getById(
				''
				, $object['parent']['id']
				, [
					'type' => 	true
					, 'state' =>  	true
					, 'status' => 	true
					, 'object_bp_type' => true
				]
			);
			
		}
		
		$newWorkflow = $objs->getById(
			''
			, $setup['options']['workflow']
		);

		$object['type'] = $newWorkflow['id'];
		$object['state'] = 1;
		$object['status'] = 'open';
		$object['state_updated_on'] = $now->format('Y-m-d H:i:s');

		$updated = $objs->update(
			$object['object_bp_type']
			, [
				'id' => $object['id']
				, 'state' => $object['state']
				, 'status' => $object['status']
				, 'state_updated_on' => $object['state_updated_on']
			]
			, 0
			, null
		);
		
		$notifications = [];
		
		if (
			$setup['options']
			&& intval($setup['options']['newState'])
		) {
			
			if ($object['object_bp_type'] === 'groups') {
				$_POST->stateProperty = 'state';
			}
			
			$stateChngUpds = $GLOBALS['app']->updateState(
				$object['id']
				, null
				, intval($setup['options']['newState'])
				, 'link'
				, ''
				, false
				, 0
			);
			
			$notifications = $stateChngUpds['msg']['notifications'];
			
			if (
				$stateChngUpds && $stateChngUpds['response']
				&& $isChild
			) {
				
				$updated = $stateChngUpds['response'];
				
			}
			
		}
		
		$update = [
			'id' => $updated['id']
			, 'type' => $newWorkflow
			, 'state' => $updated['state']
			, 'status' => $updated['status']
			, 'state_updated_on' => $updated['state_updated_on']	
		];

		$newStateName = $stateChngUpds['newState']['name'];
		$msg = '<strong>'. $updated['name'] .'</strong> transitioned to <strong>'. $newStateName .'</strong> (in '. $newWorkflow['name'] .' workflow)';
		
		return [
// 			'msg' => 				'"Change Workflow" action was triggered. <strong>'. $newWorkflow['name'] .'</strong> is the new workflow.'
			'msg' => 				$msg
			, 'update' => 			$update
			, 'notifications' => 	$notifications
		];
	}	
			
];
	
?>
