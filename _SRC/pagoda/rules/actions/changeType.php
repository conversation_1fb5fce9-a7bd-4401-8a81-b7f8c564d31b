<?php 
	
	return [
		'name' 		=> 'changeType',
		'process'	=> function ($setup, $run) {
			
			//error_reporting(E_ALL);
			//ini_set('display_errors', '1');
			$objs 	   = $setup['objs'];
			$old 	   = $setup['old'];
			$new 	   = $setup['new'];
			$options   = $setup['setup'];
			$appConfig = $setup['appConfig'];

			$newType = $options['newType'];
			$objId   = $new['id'];
			
			$toChange = $objs->getById(
				'' 
				, $objId
				, [
					'name' => true
				]
			);
			$newSetObj = $objs->getById(
				'entity_type'
				, $newType
				, [
					'name' => true
					, 'bp_name' => true
				]
			);

			// Check that type can be turned into new type
			if (
				'#'. explode('.', $newSetObj['bp_name'])[0]
				=== explode('.', $toChange['object_bp_type'])[0]
			) {
				
				$updated = $objs->updateType(
					$toChange['id']
					, '#'. $newSetObj['bp_name']
				);
				
				if ($updated !== true) {
					
					$msg = 'There was a problem changing <strong>'. $toChange['name'] .'</strong> to the specified type. Refresh the page and try again.';
			
				} else {
					
					$updated = $objs->update(
						'#'. $newSetObj['bp_name']
						, [
							'id' => 	$toChange['id']
							, 'type' => $newSetObj['id']
						]
						, 0
						, null
					);
					$msg = '<strong>'. $toChange['name'] .'\'s</strong> type was set to <i>'. $newSetObj['name'] .'</i>';

				}
				
			} else {
				
				$msg = 'Cannot change <strong>'. $toChange['name'] .'</strong> to the specified type.';
				
			}
			
			return [
				'msg' => 		$msg
				, 'update' => 	[
					'id' => 				$toChange['id']
					, 'object_bp_type' => 	'#'. $newSetObj['bp_name']
				]
			];
			
		}
	];
	
?>