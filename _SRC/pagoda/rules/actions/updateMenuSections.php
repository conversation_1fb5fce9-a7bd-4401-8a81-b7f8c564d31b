<?php

return [
	'name' => 'updateMenuSections',
	'process' => function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		
		$menuSection = $setup['sectionObj'];
		$filtered_menuSection = array();
		
		// Removing old section to replace with new updated section --> $menuSection
		$filtered_menuSection = __::filter($new['sections'], function($section) use($menuSection) {
	
			if ($section['id'] != $menuSection['id']) {
				
				return true;
				
			}
			
			return false;
			
		});
		
		// Replacing old section
		array_push($filtered_menuSection, $menuSection);
		
		// Setting new section list
		$new['sections'] = $filtered_menuSection;
		
		// Running update on menu object
		$updatedMenu = $objs->update(
			$new['object_bp_type'],
			$new,
			[
				$fieldName => 1
			],
			null
		);
			
		return [
			'msg' => ''
			, 'update' => $updatedMenu['id']
		];	
		  
	}
]
	
?>