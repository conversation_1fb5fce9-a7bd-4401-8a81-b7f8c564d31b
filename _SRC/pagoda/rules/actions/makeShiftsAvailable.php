<?php 
	
	// This action is able to toggle the 'can_be-reassigned' property on a groups object. This is currently used in scheduling to toggle a 'shift' group
	// object between 'Available for pickup' and 'Not available for pickup' modes in scheduling.
	
	return [
		'name' => 'makeShiftsAvailableForPickup'
		, 'process' => function ($setup, $run) {
			
			$objs 		= $setup['objs'];
			$obj 		= $setup['old'];
			$new 		= $setup['new'];
			$setup 		= $setup['setup'];

			$shiftIds = $setup['shifts'];

			$shifts = $objs->getById(
				'groups'
				, $shiftIds
				, [
					'available_for_pick_up' => true
					, 'can_be_reassigned' => true
				]
			);

			foreach($shifts as $shift) {
				
				$update = [
					'id' => $shift['id']	
				];
				
				if ($shift['available_for_pick_up'] == 'Yes') {
					
					$update['available_for_pick_up'] = 'No';
					
				} else if ($shift['available_for_pick_up'] == 'No') {
					
					$update['available_for_pick_up'] = 'Yes';
					
				}
				
				$new = $objs->update(
					'groups',
					$update,
					[],
					null
				);
				
			}
			
			return [
				'msg' => 'Shift(s) updated successfully'
				//, 'update' => $diff
			];
			
		}
	]
	
?>