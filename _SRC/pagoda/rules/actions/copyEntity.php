<?php

return [
	'name' 		=> 'copyEntity'
	, 'process'	=> function ($setup, $run) {
        
        // The objs api
        $objs = $setup['objs'];

        // The object to duplicate
        $template = $setup['new'];

        // The config set on the action by the user
        $config = $setup['setup'];
        $objType = $config['options']['objectType'];

        $objs->create(
            $objType
            , $template
        );
        
        return [
            'msg' => '\''. $template['name'] .'\' duplicated.'
        ];
		
	}
];

?>