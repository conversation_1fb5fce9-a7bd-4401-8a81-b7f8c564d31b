<?php

return [
	'name' 		=> 'setManagers'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new'];
		$opts = 	$setup['setup']['options'];
		
        // Parse input
        $pullFrom = $opts['pullFrom'];
        $assignees = [];

        // Get assignees from context data
        if (is_array($pullFrom)) {
            foreach ($pullFrom as $addressString) {

                $newVal = $objs->getValueAtPath(
                    $obj['id']
                    , str_replace('this.parent.', '', $addressString)
                );

                if (is_array($newVal)) {
                    foreach ($newVal as $val) {

                        if (is_int($val)) {
                            array_push(
                                $assignees
                                , $val
                            );
                        }

                    }
                } elseif (is_int($newVal)) {
                    
                    if (is_int($newVal)) {
                        array_push(
                            $assignees
                            , $newVal
                        );
                    }
                    
                }

            }
        }

		// Validate input
		if (
            $obj
            && is_array($assignees)
            && !empty($assignees)
		) {

            // Update the managers on the project
            // and tag the project with the new managers
            $assignees = array_unique($assignees);
            $tags = $obj['tagged_with'];
            $tags = array_merge($tags, $assignees);
            $tags = array_unique($tags);
            $update = [
				'id' => 			$obj['id']
				, 'managers' => 	$assignees
				, 'tagged_with' => 	$tags
			];

            $objs->update(
                'groups'
                , $update
            );

            // Get new manager objs for the response
            $newManagerObjs = $objs->getById(
                'users'
                , $update['managers']
                , [
                    'fname' => 		        true
                    , 'lname' => 	        true
                    , 'profile_image' =>    true
                    , 'color' => 	        true
                ]
            );
            $update['managers'] = $newManagerObjs;
			
			return [
				'setManagers' => 	[
					'response' => 	$update
				]
				, 'msg' =>		'New manager(s) set on <strong>'. $obj['name'] .'</strong>.'
				, 'update' => 	$update
			];
			
		} else {
			
			return false;
			
		}
		
	}
];

?>