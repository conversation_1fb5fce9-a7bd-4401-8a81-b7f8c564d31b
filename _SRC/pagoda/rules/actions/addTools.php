<?php

return [
	'name' 		=> 'addTools'
	, 'process'	=> function ($setup, $run) {
		
// 		error_reporting(E_ALL);
// 		ini_set('display_errors', '1');
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new']; // Project
		$setup = 	$setup['setup'];

		$toolObj = $setup['options']['parsedData']['selectTools'];

		$toolStruct = [];
		
		$toolStruct['id']				= 	1;
		$toolStruct['order']			= 	1;
		$toolStruct['added_by']			= 	intval( $_COOKIE['uid'] );
		$toolStruct['added_on'] 		= 	( new DateTime() )->format('Y-M-d H:i:s');
		$toolStruct['allowed_users'] 	= 	$obj['allowed_users']; // check that this is the project obj
		$toolStruct['is_archieved'] 	= 	0;
		$toolStruct['display_name']		= 	$toolObj['name'];
		$toolStruct['system_name']		= 	$toolObj['id'];
		$toolStruct['settings']			= 	[]; // There is no access to this data here.
		
		if( array_key_exists('options', $toolObj) ) {

			if( array_key_exists('single', $toolObj['options']) ) {
				
				$toolStruct['settings']['single'] = true;	
				
			}
			
		}
		
		$toolExists = false;
		
		foreach ($obj['tools'] as $index => $tool) {
			
			if ( $tool['id'] >= $toolStruct['id'] ) {
				
				$toolStruct['id'] = $tool['id'] + 1;
				
			}
			
			if ( $tool['order'] >= $toolStruct['order'] ) {
				
				$toolStruct['order'] = $tool['order'] + 1;
				
			}
			
			if ( $tool['system_name'] === $toolStruct['system_name']) {
				
				$toolExists = true;
				
			}
			
		}
		
		if ( $toolExists === false ) {
			
			array_push($obj['tools'], $toolStruct);
			
			$objs->update('groups', $obj, 0, null);
			
			$update = [
				'id' => $obj['id']
			];
			
			$update['tools'] = $obj['tools'];
			
			return [
				'msg' 		=> 	$toolStruct['system_name'] .' added to '. $obj['name']
				, 'update'	=>	$update
			];
			
		} else {
			
			return [
				'msg' 		=> 	''
			];
			
		}
		
	}
];

?>