<?php

return [
	'name' 		=> 'checkMenuReservationStatus'
	, 'process'	=> function ($setup, $run) {
		
		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new'];
        $setup = 	$setup['setup'];

        // Status to return in memo
        $reservationStatus = true;
        $statusMsg = '';

        // Get reservations
        $reservations = $objs->where(
            'item_reservation'
            , [
                'menu' => $obj['id']
            ]
            , ''
            , [
                'status' =>             true
                , 'quantity' =>         true
                , 'quantity_filled' =>  true
                , 'inventory_group' => [
                    'name' =>   true
                ]
            ]
        );

        if (is_array($reservations)) {
            foreach ($reservations as $i => $reservation) {

                if ($reservation['quantity_filled'] !== $reservation['quantity']) {

                    $reservationStatus = false;
                    if ($reservation['inventory_group']) {
                        $statusMsg .= '<li>'. $reservation['inventory_group']['name'] .'</li>';
                    }

                }

            }
        }

        if (!$reservationStatus) {
            $statusMsg = 'The following items could not be reserved for this project:<br /><ul>'. $statusMsg .'</ul>';
        }
		
		return [
			'msg' => 		$statusMsg
            , 'update' => 	[]
            , 'memo' =>     [
                'menu_is_reserved' => $reservationStatus
            ]
		];
		
	}
];

?>