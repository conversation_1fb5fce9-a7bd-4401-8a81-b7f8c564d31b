<?php

return [

	'name' =>		'triggerZap'
	, 'process' =>	function ($setup, $run) {

		$url = $setup['setup']['options']['url'];
		$myvars = 'objectName=' . $setup['old']['name'];
		
		$ch = curl_init( $url );
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $myvars);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		
		$response = curl_exec($ch);
		return [
			'msg' => 'Zapier action triggered.'
		];
				
	}
	
];
	
?>