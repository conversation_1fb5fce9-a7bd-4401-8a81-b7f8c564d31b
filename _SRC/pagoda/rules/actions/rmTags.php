<?php

return [
	'name' 		=> 'rmTags'
// 	, 'process'	=> function ($objs, $old, $new, $setup, $run) {
	, 'process'	=> function ($setup, $run) {
		
		$objs = 		$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		$rmFromParent = false;

		// find tags to apply, based on setup
		$tagType = 'tagged_with';
		$tags = [];
		switch ($setup['tagType']) {
			
			case 'share':
				$tagType = 'shared_with';
				break;
			
			default:
				$tagType = 'tagged_with';
				break;
			
		}

		// If the tags should be applied to the parent, swap the context obj
		// out for its parent.
		if ($setup['options'] && $setup['options']['rmFromParent'] === true) {
			
			$rmFromParent = true;
			$new = $objs->getById(
				''
				, $new['parent']
			);

		}
		
		if (
			is_array($setup['options'])
			&& is_array($setup['options']['tags'])
		) {
			
			foreach ($setup['options']['tags'] as $i => $tagId) {
				
				array_push(
					$tags
					, intval($tagId)
				);
				
			}
			
		}

		if (is_array($setup['options']['tagsFromObj'])) {
				
			foreach ($setup['options']['tagsFromObj'] as $addToTags) {
				
				$split = explode('.', $addToTags);
				
				// For tags on parent
				if (
					count($split) > 0
					and $split[0] === 'parent'
				) {
					
					$parent = $objs->getById(
						''
						, $new[$split[0]]
					);
					
					if (is_array($parent[$split[1]])) {
						
						$tags = array_merge($tags, $parent[$split[1]]);
						
					} else if(is_numeric($parent[$split[1]])) {
						
						array_push($tags, $parent[$split[1]]);
						
					}
					
				} elseif (substr($split[0], 0, 1) === '#') {

					// $objs->debug = true;
					$child = $objs->where(
						str_replace('-', '.', $split[0])
						, [
							'parent' => $new['id']
						]
						, ''
						, [
							$split[1] => 'id'
						]
					)[0];

					if (is_int($child[$split[1]])) {

						array_push($tags, $child[$split[1]]);

					} elseif (is_array($child[$split[1]])) {

						foreach ($child[$split[1]] as $childTag) {

							if (is_int($childTag)) {

								array_push($tags, $childTag);

							}

						}

					}
					
				} elseif ($new[$addToTags]) {
					
					array_push($tags, $new[$addToTags]);
					
				}
				
			}
			
		}
		
		// update tags, if they exist
		if (!empty($tags)) {
			
			foreach ($tags as $tag) {
				
				$updated = $objs->removeTagFromObject(
					$new['id']
					, $tag
					, $tagType
				);
				
			}
			
			$update = [
				'id' => 				$new['id']
			];
			$update[$tagType] = $updated;
			$ret = [
				'msg' => 		'Tags removed.'
			];

			// If there are updates for the context obj, return them
			// back to the logical flow.
			if (!$rmFromParent) {
				$ret['update'] = $update;
			}

			return $ret;
			
		}
		
		return false;
		
	}
];

?>