<?php

// error_reporting(E_ALL);
// ini_set('display_errors', '1');

class Timer {

	// Helper Functions
	public function getDuration($objs, $options) {

		$duration = 0;

		$timeEntries = $objs->where('time_entries', 
			[
				'shift' => $options['shift']['id'],
				'field_name' => $options['options']['timeLoggedProperty']
			]
		);

		// Get a sum of duration
		foreach ($timeEntries as $timeEntry) {
			$duration += $timeEntry['duration'];
		}

		return $duration;

	}

	public function getRate($rate, $duration) {

		$rateVal = 0;
		$updatedRateVal = 0;

		if (
			filter_var( $rate, FILTER_VALIDATE_INT )
			&& is_numeric( $rate )
		){
			$rateVal = $rate;
		}

		$updatedRateVal = ($duration / 3600) * $rateVal;

		return $updatedRateVal;
	}

	public function getShift($objId, $objs, $options) {

		return $objs->getById(
			''
			, $objId
			, [
				'parent' => true
				, 'location' => true
				, 'service' => true
				, $options['options']['timeLoggedProperty'] => true
				, $options['options']['timeEstimateProperty'] => true
				, $options['options']['rateValueProperty'] => true
				, $options['options']['runningProperty'] => true
				, 'object_bp_type' => true
				, 'tagged_with' => true
				, 'time_logged' => true 
				, 'time_estimate' => true
			]
		);

	}

	public function getRunningTimeEntries($objs, $options) {

		$runningArray = array();

		$timeEntries = $objs->where('time_entries', [
			'field_name' => $options['options']['timeLoggedProperty'],
			'shift' => $options['options']['shift'],
			'duration' => [
				'type' => 'not_set'
			]
		]);

		foreach($timeEntries as $timeEntry) {
			array_push($runningArray, array(
				'id' => $timeEntry['id'],
				'start_date' => $timeEntry['start_date'],
				'userId' => intval($timeEntry['staff'])
			));
		}

		return $runningArray;

	}

	public function logComment($objs, $options) {

		$user = $objs->getById('users', $_COOKIE['uid'], ['fname' => true, 'lname' => true]);
		
		$hours = floor($options['options']['duration'] / 3600);
		$mins = floor($options['options']['duration'] / 60 % 60);
		$secs = floor($options['options']['duration'] % 60);

		$durationString = '';

		if ($hours) {
			$durationString .= $hours . 'h';
			if ($mins or $secs) {
				$durationString .= ', ';
			}
		}

		if ($mins) {
			$durationString .= $mins . 'm';
			if ($secs) {
				$durationString .= ', ';
			}
		}

		if ($secs) {
			$durationString .= $secs . 's';
		}

		$message = '<strong>'. $user['fname'] .' '. $user['lname'] .'</strong> logged <strong>'. $durationString .'</strong>';

		if ($options['options']['note']) {
			$message .= ', "'. $options['options']['note'] .'"';
		}
		
		$GLOBALS['app']->postComment(
			[
				'type_id' => $options['shift']['id'],
				'note' => $message,
				'record_type' => 'log',
				'icon' => [
					'icon' => 'clock',
					'color' => 'blue'
				]
			]
			, 0
			, 0
		);
		
	}

	public function upstreamTimeLogged($objs, $options) {
						
		$timeLogged = $objs->simpleSum(
			'time_entries'
			, 'duration'
			, [
				'tagged_with' => [
					'type' => 'any',
					'values' => [$options['shift']['id']]
				]
			]
		);

		$options['shift'] = $objs->update(
			$options['shift']['object_bp_type']
			, [
				'id' => $options['shift']['id'],
				'time_logged' => $timeLogged
			]
			, 0
			, null
		);
					
		return $options['shift'];
		
	}

	public function upstreamTimeEstimate($objs, $options) {

		if (strpos($options['shift']['object_bp_type'], '#') !== false && $options['shift']['object_bp_type'] != 'groups') {

			$newEstimateSum = 0;

			foreach($options['shift'] as $key => $value) {

				if ( strpos($key, '_est') && $key !== 'time_estimate'){

					if (filter_var($value, FILTER_VALIDATE_INT)) {

						$newEstimateSum += $value;
					}

				}

			}

			$options['shift'] = $objs->update(
				$options['shift']['object_bp_type']
				, [
					'id' => $options['shift']['id'],
					'time_estimate' => $newEstimateSum
				]
				, 0
				, null
			);

		} else if ($options['shift']['object_bp_type'] == 'groups') {
			
			$timeEstimateCumulative = $objs->simpleSum(
				'groups'
				, 'time_estimate'
				, [
					'tagged_with' => [
						'type' => 'any',
						'values' => [$options['shift']['id']]
					]
				]
			);

			$options['shift'] = $objs->update(
				$options['shift']['object_bp_type'],
				[
					'id' => $options['shift']['id'],
					'time_estimate_cumulative' => $timeEstimateCumulative
				]
				, 0
				, null
			);
			
		}

		return $options['shift'];
		
	}

	public function upstreamRate($objs, $options) {

		$timeEntries = $objs->where(
			'time_entries'
			, [
				'tagged_with' => [
					'type' => 'any',
					'values' => [$options['shift']['id']]
				],
				'field_name' => $options['options']['timeLoggedProperty']
			]
			, ''
			, [
				'name' =>           true
				, 'rate' =>			true
				, 'hourly_rate' =>  true
				, 'duration' =>     true
				, 'field_name' =>   true
			]
		);

		if (
			filter_var($options['options']['rate'], FILTER_VALIDATE_INT)
			&& is_numeric($options['options']['rate'])
		) {

			foreach ($timeEntries as &$entry) {

				$updatedEntry = $objs->update(
					$entry['object_bp_type'],
					[
						'id' => $entry['id'],
						'rate' => $options['options']['rate'],
						'hourly_rate' => ( $entry['duration'] / 3600 ) * $options['options']['rate']
					]
					, 0
					, null
				);

				if ($updatedEntry['id'] == $options['shift']['id']) {
					$options['shift'] = $updatedEntry;
				}
				
			} 

		}

		return $options['shift'];

	}

	// Functions
	public function updateEstimate($objs, $options) {

		// Update shift
		$options['shift'] = $objs->update(
			$options['shift']['object_bp_type'],
			[
				'id' => $options['shift']['id'],
				$options['options']['timeEstimateProperty'] => $options['options']['time_estimate']
		]
		, 0
		, null);
		
		// Update estimate on shift
		$options['shift'] = $this->upstreamTimeEstimate($objs, $options);

		// Return
		return [
			'update' => [
				'id' => $options['shift']['id']
				, $options['options']['timeLoggedProperty'] => $options['shift'][$options['options']['timeLoggedProperty']]
				, $options['options']['timeEstimateProperty'] => $options['shift'][$options['options']['timeEstimateProperty']]
				, $options['options']['rateValueProperty'] => $options['shift'][$options['options']['rateValueProperty']]
				, 'object_bp_type' => $options['shift']['object_bp_type']
			]
		];

	}

	public function updateRate($objs, $options) {

		// Update shift
		$options['shift'] = $objs->update(
			$options['shift']['object_bp_type']
			, [
				'id' => $options['shift']['id'],
				$options['options']['rateValueProperty'] => $options['options']['rate']
			]
			, 0
			, null
		);
		
		// Update shift rate
		$options['shift'] = $this->upstreamRate($objs, $options); 

		// Return
		return [
			'update' => [
				'id' => $options['shift']['id']
				, $options['options']['timeLoggedProperty'] => $options['shift'][$options['options']['timeLoggedProperty']]
				, $options['options']['timeEstimateProperty'] => $options['shift'][$options['options']['timeEstimateProperty']]
				, $options['options']['rateValueProperty'] => $options['shift'][$options['options']['rateValueProperty']]
				, $options['options']['runningProperty'] => $this->getRunningTimeEntries($objs, $options)
				, 'object_bp_type' => $options['shift']['object_bp_type']
			]
		];

	}

	public function createTimeEntry($objs, $options) {

		// Set variables
		$date = new DateTime('now', new DateTimezone('Africa/Abidjan'));
		$created = false;
		$tracking = false;

		// Initialize array
		$new = array();

		// Setup array
		$new['start_date'] = empty($options['options']['duration']) ? $date->format('Y-m-d H:i:s') : $options['options']['start_date'];
		$new['end_date'] = $options['options']['end_date'];
		$new['duration'] = $options['options']['duration'];
		$new['note'] = $options['options']['note'];	
		$new['staff'] = $_COOKIE['uid'];
		$new['shift'] = $options['shift']['id'];
		$new['service'] = $options['shift']['job_type'];
		$new['location'] = $options['shift']['location'];
		$new['tagged_with'] = $options['options']['tagged_with'];
		$new['field_name'] = $options['options']['timeLoggedProperty'];
		$new['rate'] = $options['shift'][$options['options']['rateValueProperty']];
		$new['hourly_rate'] = $this->getRate($options['shift'][$options['options']['rateValueProperty']], $options['options']['duration']);
		
		// Push HQ tag
		array_push($new['tagged_with'], $options['hq'][0]['id']);

		// Create time entry
		$newEntry = $objs->create('time_entries', $new, 0, 1);

		// Set name of time entry with object uid
		$newEntry['name'] = '#'. $newEntry['object_uid'];

		// Update time entry
		$updatedEntry = $objs->update('time_entries', 
			$newEntry,
			array(
				'id' => true,
				'name' => true,
				'object_uid' => true,
				'start_date' => true,
				'end_date' => true,
				'duration' => true,
				'note' => true,
				'shift' => true,
				'staff' => array(
					'fname' => true,
					'lname' => true,
					'profile_image' => true
				),
				'parent' => true,
				'field_name' => true
			),
			null);

		if (empty($options['options']['duration'])) {

			// Set variables
			$tracking = true;

			// Update the shift
			$options['shift'] = $objs->update(
				$options['shift']['object_bp_type']
				, [
					'id' => $options['shift']['id'],
					$options['options']['runningProperty'] => $this->getRunningTimeEntries($objs, $options)
				]
				, 0
				, null
			);

		} else {

			// Set variables
			$created = true;

			// Update field on shift
			$options['shift'] = $objs->update(
				$options['shift']['object_bp_type']
				, [
					'id' => $options['shift']['id'],
					$options['options']['timeLoggedProperty'] => $this->getDuration($objs, $options)
				]
				, 0
				, null
			);

			// Update time logged on shift
			$options['shift'] = $this->upstreamTimeLogged($objs, $options);
			
			// Log a comment for the note
			$this->logComment($objs, $options);

		}
		
		return [
			'update' => [
				'id' => $options['shift']['id']
				, $options['options']['timeLoggedProperty'] => $options['shift'][$options['options']['timeLoggedProperty']]
				, $options['options']['timeEstimateProperty'] => $options['shift'][$options['options']['timeEstimateProperty']]
				, $options['options']['rateValueProperty'] => $options['shift'][$options['options']['rateValueProperty']]
				, $options['options']['runningProperty'] => $this->getRunningTimeEntries($objs, $options)
				, 'object_bp_type' => $options['shift']['object_bp_type']
				, 'time_entry' => $newEntry
				, 'created' => $created
				, 'tracking' => $tracking
			]
		];

	}

	public function updateTimeEntry($objs, $options) {

		// Initialize array
		$update = array();

		// Setup array
		$update['id'] = $options['options']['entry'];
		$update['start_date'] = $options['options']['start_date'];
		$update['end_date'] = $options['options']['end_date'];
		$update['duration'] = $options['options']['duration'];
		$update['note'] = $options['options']['note'];
		$update['tagged_with'] = $options['options']['tagged_with'];
		$update['rate'] = $options['shift'][$options['options']['rateValueProperty']];
		$update['hourly_rate'] = $this->getRate($options['shift'][$options['options']['rateValueProperty']], $opt['duration']);

		// Push HQ tag
		array_push($upd['tagged_with'], $options['hq'][0]['id']);
		
		// Update time entry
		$updatedEntry = $objs->update(
			'time_entries'
			, $update
			, array(
				'id' => true,
				'name' => true,
				'object_uid' => true,
				'start_date' => true,
				'end_date' => true,
				'duration' => true,
				'note' => true,
				'shift' => true,
				'staff' => array(
					'fname' => true,
					'lname' => true,
					'profile_image' => true
				),
				'parent' => true,
				'field_name' => true,
				'rate' => true,
				'hourly_rate' => true
			)
		);

		// Update shift
		$options['shift'] = $objs->update(
			$options['shift']['object_bp_type']
			, [
				'id' => $options['shift']['id']
				, $options['options']['timeLoggedProperty'] => $this->getDuration($objs, $options)
				, $options['options']['runningProperty'] => $this->getRunningTimeEntries($objs, $options)
			]
			, 0
			, null
		);
		
		// Update time logged on shift
		$options['shift'] = $this->upstreamTimeLogged($objs, $options);

		// If the log is newly completed, post a comment.
		if (empty($options['running']['duration']) && !empty($options['options']['duration'])) {
			$this->logComment($objs, $options);
		}
		
		// Return
		return [
			'update' => array(
				'id' => $options['shift']['id']
				, $options['options']['timeLoggedProperty'] => $options['shift'][$options['options']['timeLoggedProperty']]
				, $options['options']['timeEstimateProperty'] => $options['shift'][$options['options']['timeEstimateProperty']]
				, $options['options']['rateValueProperty'] => $options['shift'][$options['options']['rateValueProperty']]
				, $options['options']['runningProperty'] => $this->getRunningTimeEntries($objs, $options)
				, 'object_bp_type' => $options['shift']['object_bp_type']
				, 'time_entry' => $updatedEntry
				, 'updated' => true
			)
		];

	}

	public function deleteTimeEntry($objId, $objs, $options) {

		// Set variables
		$duration =	0;

		// Get the current time entry
		$deletedTimeEntry = $objs->getById('time_entries', $objId);

		// Get shift
		$options['shift'] = $this->getShift($deletedTimeEntry['shift'], $objs, $options);

		// Set options
		$options['options']['timeLoggedProperty'] = $deletedTimeEntry['field_name'];

		// Delete time entry
		$objs->delete('time_entries', $objId);
			
		// Update field on shift
		$options['shift'] = $objs->update(
			$options['shift']['object_bp_type'],
			[
				'id' => $options['shift']['id'],
				$deletedTimeEntry['field_name'] => $this->getDuration($objs, $options),
				$deletedTimeEntry['field_name'] . '_running' => $this->getRunningTimeEntries($objs, $options)
			],
			0,
			null
		);

		// Update time logged on shift
		$options['shift'] = $this->upstreamTimeLogged($objs, $options);

		// Return
		return [
			'update' => [
				'id' => $options['shift']['id'],
				$deletedTimeEntry['field_name'] => $duration,
				$deletedTimeEntry['field_name'] . '_running' => $this->getRunningTimeEntries($objs, $options),
				'object_bp_type' => $options['shift']['object_bp_type'],
				'time_entry' => $deletedTimeEntry,
				'deleted' => true
			]
		];

	}

}

return [

	'name' => 'timer',
	'process' => function ($setup, $run) {

		$timer = new Timer;
		
		$objs = 		$setup['objs'];
		$old = 			$setup['old'];
		$new = 			$setup['new'];
		$options = 		$setup['setup'];

		$options['hq'] = $objs->where('groups',
			[
				'group_type' => 'Headquarters',
				'parent' => 0
			]	
		);

		// Get shift
		$options['shift'] = $timer->getShift($options['options']['shift'], $objs, $options);

		// Update estimate
		if (
			is_int($options['options']['time_estimate'])
		) {

			return $timer->updateEstimate($objs, $options);

		}

		// Update rate
		if (
			is_int($options['options']['rate'])
		) {

			return $timer->updateRate($objs, $options);

		}

		// Delete time entries
		if ($options['delete'] && !empty($options['options']['selection'])) {

			// Initialize array
			$updates = array();

			if (is_array($options['options']['selection'])) {
				
				foreach ($options['options']['selection'] as $i => $objId) {

					$update = $timer->deleteTimeEntry($objId, $objs, $options);

					array_push($updates, $update);
				
				}
				
			} else {
				
				$update = $timer->deleteTimeEntry($options['options']['selection'], $objs, $options);

				array_push($updates, $update);
				
			}
			
			// Return results
			return $updates[0];

		}

		function updateTimeEntry($objs, $options, $timer) {

			$entryId = !empty($options['running']['id']) ? $options['running']['id'] : $options['options']['entry'];

			$entry = $objs->getById('time_entries', $entryId);
			
			if (!$entry) {
				return false;
			}

			$options['options']['entry'] = $entry['id'];

			$options['options']['start_date'] = !empty($options['running']['start_date']) ? $options['running']['start_date'] : $options['options']['start_date'];

			$startTimestamp = (new DateTime($options['options']['start_date'], new DateTimezone('Africa/Abidjan')))->format('U');
			$stopTimestamp = (new DateTime($options['options']['end_date'], new DateTimezone('Africa/Abidjan')))->format('U');

			$options['options']['duration'] = intval($stopTimestamp) - intval($startTimestamp);

			return $timer->updateTimeEntry($objs, $options);

		}

		// Manual time entry
		if ($options['options']['mode'] == 'manual' || (empty($options['options']['entry']) && is_int($options['options']['duration']) && $options['options']['duration'] > 0)) {

			if ($options['options']['entry']) {

				return updateTimeEntry($objs, $options, $timer);

			} else {

				return $timer->createTimeEntry($objs, $options);

			}

			
			
		}

		// Get current running timer for field
		$currentRunningEntry = $objs->where('time_entries', [
			'field_name' => $options['options']['timeLoggedProperty'],
			'shift' => $options['options']['shift'],
			'staff' => intval($_COOKIE['uid']),
			'duration' => [
				'type' => 'not_set'
				]
			]
		);

		$options['running'] = $currentRunningEntry[0];

		// Stop time entry / Update time entry
		if ($options['options']['mode'] == 'tracking' || (!empty($options['running']['id']) || !empty($options['options']['entry']))) {

			return updateTimeEntry($objs, $options, $timer);

		}

		// Start time entry
		if (empty($options['running']['id'])) {

			return $timer->createTimeEntry($objs, $options);

		}

		return false;
				
	}
	
]
	
?>