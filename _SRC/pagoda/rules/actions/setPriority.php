<?php

return [
	'name' 		=> 'setPriority'
	, 'process'	=> function ($setup, $run) {
        
        $Levels = [
            [
                'name' => 'Lowest'
                , 'icon' => 'arrow down'
                , 'color' => 'green'
                , 'value' => 0
            ]
            , [
                'name' => 'Low'
                , 'icon' => 'arrow down'
                , 'color' => 'olive'
                , 'value' => 1
            ]
            , [
                'name' => 'Medium'
                , 'icon' => 'arrow up'
                , 'color' => 'yellow'
                , 'value' => 2
            ]
            , [
                'name' => 'High'
                , 'icon' => 'arrow up'
                , 'color' => 'orange'
                , 'value' => 3
            ]
            , [
                'name' => 'Highest'
                , 'icon' => 'arrow up'
                , 'color' => 'red'
                , 'value' => 4
            ]
        ];

        // Apis
        $objs = 	$setup['objs'];
        
        // Inputs
		$obj = 		$setup['old'];
		$setup = 	$setup['setup'];
        $fieldKey = $setup['fieldKey'];
        $note = $setup['note'];
        $setTo = intval($setup['setTo']);
        $set = $objs->where(
            'entity_type'
            , [
                'bp_name' => substr($obj['object_bp_type'], 1)
            ]
        )[0];

        $from = __::find($Levels, function ($level) use ($obj, $fieldKey) {

            return $obj[$fieldKey] === $level['value'];

        });
        $to = __::find($Levels, function ($level) use ($setTo) {

            return $setTo === $level['value'];

        });

		if (
            $obj['id']
            && $fieldKey
            && $to
		) {

            // Update the value
            $upd = $objs->update(
                $obj['object_bp_type']
                , [
                    'id' => $obj['id']
                    , $fieldKey => $setTo
                ]
            );

            // Log a note
            if ($note) {

                $log = '<strong>'. $set['blueprint'][$fieldKey]['name'] .'</strong> set to ';
                $log .= '<i class="ui '. $to['color'] .' '. $to['icon'] .' icon"></i>'. $to['name'];
                if ($from) {
                    $log .= ' from <i class="ui '. $from['color'] .' '. $from['icon'] .' icon"></i>'. $from['name'];
                }
                if ($_COOKIE['uid']) {
                    $usr = $objs->getById(
                        'users'
                        , $_COOKIE['uid']
                    );
                    if ($usr) {
                        $log .= ' by <strong></i>'. $usr['name'] .'</i></strong>';
                    }
                }
                $log .= ':<blockquote>'. $note .'</blockquote>';

                $GLOBALS['app']->postComment([
                    'type_id' => 		$obj['id']
                    , 'note' => 		$log
                    , 'record_type' => 'log'
                    , 'icon' => [
                        'icon' => 		$to['icon']
                        , 'color' => 	$to['color']
                    ]
                    , 'public' =>       1
                ], 0, 0);

            }
			
			// return message
			return [
                'update' => [
                    'id' =>           $obj['id']
                    , $fieldKey =>    $upd[$fieldKey]
                ]
            ];
			
		}
		
		return false;
		
	}
];

?>