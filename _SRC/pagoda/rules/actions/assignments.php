<?php


/*error_reporting(E_ALL);
ini_set('display_errors', '1');*/


return [
    'name' 		=> 'assignments'
    , 'process'	=> function ($setup, $run) {

        $objs = 		$setup['objs'];
        $new = 		$setup['new'];
        $options = $setup['setup']['options'];

        $roles = $options['roles'];


        $roleSheet = __::find($new['tools'], function($obj){
            $startWith = "Project Role Sheet";
            $len = strlen($startWith);
            return (substr($obj["display_name"], 0, $len) === $startWith);
        });

        if(!$roleSheet){
            return [
                'msg' => 'Assignments was not completed.'
            ];
        }

        $bluePrintName = $roleSheet["system_name"];

        //all fields
        $bluePrintObject = $objs->getBlueprint('#' . $bluePrintName);

        $assignmentFieldKey = null;

        foreach($bluePrintObject as $key => $field){
            if ( strtolower($field['name'])=='assignee' && (!$field['is_archived']) ){
                $assignmentFieldKey = $key;
            }
        }


        if($assignmentFieldKey){

            //get roleSheet object for the project
            $roleSheetPerProject = ($objs->where(
                '#' . $bluePrintName,
                array(
                'parent' => $new['id']
                ),
                "",
                ['parent' => true, $roles[0] => true]
            ))[0];

            $updatedRole = $objs->update('#' . $bluePrintName,
                [
                    'id' => $roleSheetPerProject['id']
                    , $assignmentFieldKey => $roleSheetPerProject[$roles[0]]['id']
                ]
            );

            return [
                'msg' => 'Assignments was completed in RoleSheet.',
                'updated' => $updatedRole
            ];


        }

    }
];

?>
