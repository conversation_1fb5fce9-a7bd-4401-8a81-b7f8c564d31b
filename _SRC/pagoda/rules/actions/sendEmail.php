<?php
	
return [
	'name' => 'sendEmail',
	'process' => function ($setup, $run) {

		// Private methods
		$getAddressFromObjIds = function ($sendTo) {
			
			$addresses = [];
			
			if (is_array($sendTo)) {
				foreach ($sendTo as $receiver) {
					
					if ($receiver['email']) {
						
						array_push(
							$addresses
							, $receiver['email']
						);
						
					} elseif (is_array($receiver['contact_info'])) {
						
						foreach ($receiver['contact_info'] as $info) {
							
							if (
								$info['type']['data_type'] === 'email'
								&& $info['is_primary'] === 'yes'
							) {
								
								array_push(
									$addresses
									, $info['info']
								);
								
							}
							
						}
						
					}
					
				}
				
			}
			
			return $addresses;
			
		};
		
		$getEmailAddresses = function ($obj, $propertyName) {
			
			$addresses = [];

            if(strpos($propertyName, ".") !== false){
                $addressWithParent = explode('.', $propertyName);
                $value = $obj[$addressWithParent[0]][$addressWithParent[1]];

                array_push(
                    $addresses
                    , $value
                );

            }
			else if (is_int($obj[$propertyName])) {
				
				array_push(
					$addresses
					, $obj[$propertyName]
				);
				
			} elseif (is_array($obj[$propertyName])) {
				
				$addresses = array_merge(
					$addresses
					, $obj[$propertyName]
				);
				
			}
			
			return $addresses;
			
		};
		
		$objs = 	$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];

		$mergeVars = [];
		
		$appConfig =	$objs->rules->appConfig;
		$comm = new Comm($objs, $objs->rules->appConfig);

		$response = [];
		
		$addresses = [];
		
		// Using this to set tagged with on email object
		$taggedWithArr = [];
		
		foreach($new['tagged_with'] as $id) {
			
			array_push($taggedWithArr, intval($id));
			
		}

		array_push($taggedWithArr, $new['id']);

        if ($setup['options']['tagged_with']) {
            $taggedWithArr = array_merge(
                $taggedWithArr
                , $setup['options']['tagged_with']
            );
        }

        //If object is an project or object is and action item
        if($new['object_bp_type'] == "groups" && $new['group_type'] == "Project"){
            //is a project
            $mainContact = $new['main_contact'];
            array_push($taggedWithArr, $mainContact);
        } else {
            //is an action item
            $projectId = $new['parent'];
            //var_dump($objs);die();
            $project = $objs->getById('groups', $projectId);
            $mainContact = $project['main_contact'];
            array_push($taggedWithArr, $mainContact);
        }

		// For event setup merged on the front end
		if ($setup['options']['isMerged']) {

			$addresses = explode(', ', $setup['options']['sendTo']);
			
		} else {

			// (for automated messages)

            if(!($new['object_bp_type'] == "groups" && $new['group_type'] == "Project")){
                $project = $objs->getById('groups', $new['parent']);
                $new['parent'] = $project;
            }

            if (is_string($setup['options']['sendTo'])) {

				$addresses = array_merge(
					$addresses
					, $getEmailAddresses(
						$new
						, $setup['options']['sendTo']
					)
				);
				
			} elseif (is_array($setup['options']['sendTo'])) {
			    foreach($setup['options']['sendTo'] as $propertyName){

                    $addresses = array_merge(
                        $addresses
                        , $getEmailAddresses($new, $propertyName)
                    );
                }
            }

            $getAddress = true;

            if ( isset( $setup['options']['getAddressFromObjIds'] ) ) {
                $getAddress = $setup['options']['getAddressFromObjIds'];
            }

            if ($getAddress) {

                $addresses = $getAddressFromObjIds($objs->getById(
                    ''
                    , $addresses
                    , [
                        'email' => 			true
                        , 'contact_info' => 	[
                            'info' => 			true
                            , 'is_primary' => 	true
                            , 'type' => 			true
                        ]
                    ]
                ));
            }
			
		}


		$contextName = 'CONTEXT NAME';
		$details = 'DETAILS';
		$link = '';
		$producer = $new;

        if ( $setup['options']['producer']) {
            $producer = $setup['options']['producer'];
        }

		if ($setup['options']['title']) {
			$emailTitle = $setup['options']['title'];
		}

		$template = $objs->getById('contracts', $setup['options']['message']);

		// Set the email name
		$emailTitle = $template['name'];
		if ($setup['options']['title']) {
			$emailTitle = $setup['options']['title'];
		}

		// Merge the email name
		$emailTitle = $objs->runSteps(
			$obj
			, [
				'merge' => [
					'obj' => 			$template['id']
					, 'parent' => 		$new['id']
					, 'template' => 	$emailTitle
					, 'format' => 		'plain-text'
					, 'mergeVars' => 	$mergeVars
				]
			]
			, true
		)['memo'];
		
		$toCreate = array(
			'name' 			 	=>	$template['name'], 
			'html_string' 	 	=>	$setup['options']['body']['html'],
			'active' 		 	=>	'No', 
			'tagged_with' 	 	=>	$taggedWithArr,
			'is_public' 	 	=>	true,
			'related_object' 	=>	$new['id'],
			'merge_type'		=>	$template['merge_type']
		);


		// If the document should always merge on the public endpoint, mark the
		// document as active and pull in the unmerged html directly from the 
		// template.
		if ($setup['options']['activeDoc'] === true) {
			
			$toCreate['active'] = 'Yes';

			if (
				!empty($template)
				&& !empty($template['html_string'])
			) {
				
				$toCreate['html_string'] = $template['html_string'];
			}

		}else{

			if ($setup['availableToUser'] === false) {

				$authData = [
					'varToken' => $_COOKIE['token'],
					'varSeries' => $_COOKIE['series'],
					'varUid' => $_COOKIE['uid'],
					'varInstance' => $_COOKIE['instance'],
					'varPagoda' => $_COOKIE['pagoda'],
					'varPagodaAPIKey' => $_COOKIE['pagoda']
				];

				$data = [
					'contextId' 	=> $new['id'],
					'context' 		=> '',
					'templateHtml'  => $template['html_string'],
					//'mergeVars' 	=> $setup['options']['mergeVars'],
					'mergeVars'		=> '',
					'authData'  	=> $authData
				];

				$postData = [
					'postData' 	=> $data
				];

				// Package up payload to json
				$payload = json_encode($postData);

				$endpoint = MERGE_ENDPOINT;
				if($endpoint == "localhost"){
					$url = "merge:8084";
				}else{
					$url = $endpoint;
				}

				// Curl webhook
				$ch = curl_init($url.'/mergeService');
				curl_setopt($ch, CURLOPT_POST, true);
				curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);     
				curl_setopt($ch, CURLOPT_HTTPHEADER, array(                                                                          
					'Content-Type: application/json',                                                                                
					'Accept: application/json',                                                                                
					'Content-Length: ' . strlen($payload)                                                                       
				));                                                              
			
				$response = curl_exec($ch);
				curl_close($ch);

				//var_dump($response);
				//die();
				
				$toCreate['html_string'] = $response;

			}
			
		}

		// Create the new contract
		$contract = $objs->create('contracts', $toCreate);

		// Get contract ID
		$contractID = $contract['id'];

		// Set the new doc ID
		$new['_doc'] = $contractID;

		// Set the body
		if ( !empty($setup['options']['sendDocumentAsLink']) ) {

			// Set merge vars
			$mergevars = (object) [
				'TITLE' => $emailTitle,
				'BODY' => '<a href="https://bento.infinityhospitality.net/app/documents#?&i=' . $appConfig['instance'] . '&wid=' . $contractID .'">View Document</a>',
				'INSTANCE_NAME' => $appConfig['instance']
			];

		} else {

			$containerStyle = 'width:100%; background-color:#f8f8f8; padding:0px 40px 40px 40px !important;';
			$style = 'background-color:#ffffff; margin:0 auto; padding:40px 40px 40px 40px !important; border:1px solid rgb(235, 235, 235); border-radius:0.375rem !important;';
			if ($contract['orientation'] === 'landscape') {
				$style .= 'width:1253px; max-width:1253px; max-height:830px;';
			} else {
				$style .= 'width:830px; max-width:830px; max-height:1253px;';
			}

			$setup['options']['body']['html'] = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">' .
													'<head>' .
														'<meta http-equiv="Content-Type" content="text/html charset=UTF-8" />' .
													'</head>' .
													'<html>' .
														'<body>' .
															'<table style="' . $containerStyle . '">' .
																'<tbody>' .
																	'<tr>' .
																		'<td>' . 
																			'<table style="margin:0 auto; padding:10px;">' .
																				'<tbody>' .
																					'<tr>' .
																						'<td style="color:grey !important;">' . 
																							'Does this email not look right? <a href="https://bento.infinityhospitality.net/app/documents#?&i=' . $appConfig['instance'] . '&wid=' . $contractID .'" style="color:grey !important;">Try viewing in browser instead.</a>' .
																						'</td>' .
																					'</tr>' .
																				'</tbody>' .
																			'</table>' .
																			'<table style="' . $style . '">' .
																				'<tbody>' .
																					'<tr>' .
																						'<td>' . 
																							$setup['options']['body']['html'] . 
																						'</td>' .
																					'</tr>' .
																				'</tbody>' .
																			'</table>' .
																		'</td>' .
																	'</tr>' .
																'</tbody>' .
															'</table>' .
														'</body>' .
													'</html>';

			// Set merge vars
			$mergevars = (object) [
				'TITLE' => $emailTitle,
				'BODY' => $setup['options']['body']['html'],
				'INSTANCE_NAME' => $appConfig['instance']
			];

		}

		$adds = [];
		
		// To
		if (is_array($addresses)) {
			foreach ($addresses as $email) {
				
				array_push(
					$adds
					, [
						'email' => $email
						, 'type' => 'to'
					]
				);
				
			}
		}
		
		// CC
		if ($setup['options']['cc']) {
			foreach (explode(', ', $setup['options']['cc']) as $email) {
				
				array_push(
					$adds
					, [
						'email' => $email
						, 'type' => 'cc'
					]
				);
				
			}
		}
		
		// BCC
		if ($setup['options']['bcc']) {
			foreach (explode(', ', $setup['options']['bcc']) as $email) {
				
				array_push(
					$adds
					, [
						'email' => $email
						, 'type' => 'bcc'
					]
				);
				
			}
		}

		$comm->sendMandrillEmail(
			$adds, 
			null, 
			$emailTitle, 
			$mergevars, 
			['TITLE', 'BODY', 'INSTANCE_NAME'], 
			$producer, 
			1,
			0,
			false,
			null,
			$appConfig['instance'],
			1,
			$taggedWithArr
		);

		return [
			'msg' => 'Email sent successfully.',
			'update' => $new
		];		
		
	}
];
