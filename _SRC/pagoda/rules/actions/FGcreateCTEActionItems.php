<?php

function FGCoreDemoAccountEndOfMonthSet ($coreDemo, $key) {

    // Check that the state of inc is valid and set
    if (
        $coreDemo[$key]
        && is_int($coreDemo[$key])
        && !empty($coreDemo[$key])
    ) {

        return true;

    }

    return false;

}

function FGCoreDemoDateOfIncCheckInRange($start_date, $end_date, $date_from_user) {
    // Convert to timestamp
    $start = strtotime($start_date);
    $end = strtotime($end_date);
    $check = strtotime($date_from_user);
    // Check that user date is between start & end
    return (($start <= $check ) && ($check <= $end));
}

function FGgetStateFromValue($selected, $bpOptions){

    $statesSelected = array();

    // loop over choices selected on the Core Service
    foreach ($selected as $value) {

        // using the bluprint as map, get the selections and map them to values
        foreach ( $bpOptions as $opt) {

            if ($opt['value'] == $value) {

                // only need the Geographical State abbreviate
                $abbr = explode(" ", $opt['name']);

                array_push($statesSelected, $abbr[0]);
            }
       }
    }

    unset($value);
    return $statesSelected;
}

return [
	'name' 		=> 'FGcreateCTEActionItems'
	, 'process'	=> function ($setup, $run) {

		// Get setup info
        $db = 	            $setup['objs'];
		$contextID = 		$setup['old'];
		$new = 		        $setup['new'];
		$setup = 	        $setup['setup'];
		$opts = 	        $setup['options'];

        $contextCompany =   false;
        $project =          false;
        $coreService =      false;
        // Blueprint keys
        $coreDemoKey =                  '#ml7laG';
        $coreDemoStateOfIncKey =        '_23';
        $coreDemoDateOfIncKey =         '_24';
        $coreDemoAccountEndOfMonth =    '_21';

        $coreServiceKey =               '#3KelnN';
        $coreServiceStateOfIncKey  =    '_18';
        $coreServiceTaxYearKey =        '_1';

        $californiaSelection = false;

        // Need CS to get values selected for State options
        if ( isset($setup['options']) && !empty($setup['options']['coreserviceId']) )  {
            $contextID = $setup['options']['coreserviceId'];
        }
        if ( isset($setup['options']) && !empty($setup['options']['projectId']) ) {
            $projectID = $setup['options']['projectId'];
        }

        // $contextID = 7387494;
        // $project = 7663054;

        $coreService = $db->getById('', $contextID, [
            'tagged_with' => true
			, 'name' => true
            , $coreServiceTaxYearKey => true
            , $coreServiceStateOfIncKey => true
            // main_contact
            , '_11' => true
            // organization
            , '_61' => true
            // cte state selections
            , '_63' => true
        ]);

        $cteSelections = $coreService['_63'];

        $bpName = ltrim($coreService['object_bp_type'], '#');

        // grab Core Service blueprint to get values of selections
        $csBlueprint = $db->where(
            'entity_type'
            , [
                'bp_name' => $bpName
            ]
            , ''
            , [
                'blueprint' => true
            ]
        )[0]['blueprint'];
        $BPstateSelections = $csBlueprint['_63']['options']['options'];

        // Incoming selections stored as an int, need to get mapped values using the CoreServices BP
        $mappedSelectionAbbreviations = FGgetStateFromValue($cteSelections, $BPstateSelections);

        // Get context company
        $contextCompany = $db->getById('', $coreService['_61']['id']);
        // Need Project to 1) set parent on Action Items and 2) tagged_with
        $project = $db->getById('', $projectID, [
            'main_contact' => true
            , 'name' => true
            , 'tagged_with' => true
        ]);
        // Get the core demo record for the company
        $coreDemo = $db->where(
            $coreDemoKey
            , ['tagged_with' => [$contextCompany['id']]]
        )[0];

        $taxYear = (string)$coreService[$coreServiceTaxYearKey];
        $stateOfIncorporation = $coreDemo[$coreDemoStateOfIncKey]['state'];

        // Check for the tag for the specific year
		$yearTag = $db->where(
			'system_tags'
			, [
				'tag' => $taxYear
			]
			, ''
			, [
				'tag' => true
			]
		)[0];

		///Create a tag for the year if it does not exist
		if (empty($yearTag)) {

			$yearTag = $db->create(
				'system_tags'
				, [
					'name' => 		strval($coreService[$coreServiceTaxYearKey])
					, 'tag' => 		strval($coreService[$coreServiceTaxYearKey])
					, 'color' => 	'black'
				]
				, 0
				, [
					'tag' => true
				]
			); 
        }        

        // Create/toggle the forms
		$stateSpecificFormSets = $db->where(
			'entity_type'
			, [
				'name' => [
					'type' => 		'contains'
					, 'value' => 	'CTE\_'
				]
                , 'is_template' => [
                    'type' => 'not_equal'
                    , 'value' => 1
                ]
			]
		);


        // sort list alph for easier testing
        usort($stateSpecificFormSets, function($v1, $v2) { return strcmp($v1['name'], $v2['name']); });

        $stateForms = array();

        // compare against Selections (previously mapped using the Core Service BP)
        foreach ($mappedSelectionAbbreviations as $i => $abbr) {

            if ( $abbr == 'CA') {
                $californiaSelection = true;
            }

            // iterate over the Forms
            foreach ($stateSpecificFormSets as $j => $formSet) {
             
                $formName = substr($formSet['name'], 4);

                $formName2 = strstr($formName, ' ', true);

                if ( strpos( $formName2, $abbr ) === false ){

                    // echo $j . " not a match" . PHP_EOL;

                } else {

                    // echo $j . " - match found " . $abbr. PHP_EOL;
                    // echo $formSet['name']. PHP_EOL;

                    array_push($stateForms, $formSet);

                }
 
                unset($formSet);
            }

            unset($abbr);

        }
        
        $mergedTagged = $setup['options']['serviceTags'];

        array_push(
            $mergedTagged
            , $project['id']
            , $yearTag
            , $project['main_contact']
            , $contextCompany
        );

		$mergedTagged = array_unique($mergedTagged);

        if ( $californiaSelection && $stateOfIncorporation == 'CA' ) {
           
            $endOfMonth = '';
            switch($coreDemo[$coreDemoAccountEndOfMonth]){
                case 1:
                    $endOfMonth = 'January';
                    break;
                case 2:
                    $endOfMonth = 'February';
                    break;
                case 3:
                    $endOfMonth = 'March';
                    break;    
                case 4:
                    $endOfMonth = 'April';
                    break;    
                case 5:
                    $endOfMonth = 'May';
                    break;    
                case 6:
                    $endOfMonth = 'June';
                    break;    
                case 7:
                    $endOfMonth = 'July';
                    break;    
                case 8:
                    $endOfMonth = 'August';
                    break;    
                case 9:
                    $endOfMonth = 'September';
                    break;    
                case 10:
                    $endOfMonth = 'October';
                    break;    
                case 11:
                    $endOfMonth = 'November';
                    break;
                case 12:        
                    $endOfMonth = 'December';
                    break;
            }

            $accountEndofRange = gmdate('F j, Y 23:59:59', strtotime('last day of'. $endOfMonth .' '. $taxYear));

            $accountStartofRange = date('F j, Y 00:00:00', strtotime( $accountEndofRange . " - 365 day"));

            // $date_from_user = '2024-02-29';
            $date_from_user = $coreDemo[$coreDemoDateOfIncKey];

            $withinRange = FGCoreDemoDateOfIncCheckInRange($accountStartofRange, $accountEndofRange, $date_from_user);

            $intakeToFilter = '';

            if ( $withinRange ) {
                $intakeToFilter = 'Outside Fiscal';
            } else {
                $intakeToFilter = 'Within Fiscal';
            }

            $stateForms = __::filter(
                $stateForms
                , function ($formSet) use ($intakeToFilter) {

                    $name = trim( explode('|', explode('_', $formSet['name'])[2] )[0] );

                    if ($name != $intakeToFilter){
                        return true;
                    }
        
                    return false;
    
                }
            );

        }

        $formsThatShouldExist = array();

        foreach ($stateForms as $formBp) {

            array_push(
                $formsThatShouldExist
                , [
                    'name' => 				$taxYear . explode('|', $formBp['name'])[1]
                    , 'parent' => 			$project['id']
                    , 'tagged_with' => 		$mergedTagged
                    , 'shared_with' => 		[$coreService['_11']['id']]
                    , 'object_bp_type' => 	'#'. $formBp['bp_name']
                ]
            );

        }

        foreach ($formsThatShouldExist as $formToCreate) {
            $db->create(
                $formToCreate['object_bp_type']
                , $formToCreate
            );
        }

        return [
            'msg' => 'CTE Action Items created'
        ];
		
	}
];

?>
