<?php

return [

	'name' =>		'triggerWebhook'
	, 'process' =>	function ($api, $run) {
		
		// Validate input
		if (empty($api['setup']) || empty($api['setup']['options'])) {
			return false;
		}
		
		// Prase state
		$objectType = $api['new']['object_bp_type'];
		$objectId = $api['new']['id'];
		
		// Parse options
		$url = $api['setup']['options']['url'];
		$payloadFields = $api['setup']['options']['payload'];
		$getTransition = $api['setup']['options']['transition'];
		
		// Get payload
		$payload = [
			'data' => []
		];
		$select = [
			'created_by' => [
				'fname' => true
				, 'lname' => true
			]
			, 'last_updated_by' => [
				'fname' => true
				, 'lname' => true
			]
		];
		$bp = $api['objs']->getBlueprint($objectType);
		
		if (is_array($payloadFields)) {
			foreach ($payloadFields as $field) {
				
				if ($bp[$field]) {
					
					$select[$field] = true;
					
				}
				
			}
		}
		$payload['data'] = $api['objs']->getById(
			$objectType
			, $objectId
			, $select
		);
		
		// Parse object data
		if (is_array($payloadFields)) {
			foreach ($payloadFields as $fieldKey) {
				
				// Rename keys
				$payload['data'][$bp[$fieldKey]['name']] = $payload['data'][$fieldKey];
				unset($payload['data'][$fieldKey]);
				
			}
		}
		
		if ($getTransition) {
			
			$payload['event'] = $api['trigger'];
			
		}
		
		// Package up payload to json
		$payload = json_encode($payload);
		
		// Curl webhook
		$ch = curl_init($url);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");                                                                     
		curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);                                                                  
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);                                                                      
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(                                                                          
		    'Content-Type: application/json',                                                                                
		    'Accept: application/json',                                                                                
		    'Content-Length: ' . strlen($payload)                                                                       
		));
		
		$response = curl_exec($ch);
		
		return [
			'msg' => 'Webhook triggered.'
		];
				
	}
	
];
	
?>