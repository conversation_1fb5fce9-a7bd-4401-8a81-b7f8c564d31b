<?php

return [

	'name' =>		'invoiceValue'
	, 'process' =>	function ($setup, $run) {
		
		$upstreamInvoiceValue = function ($project, $proposalID, $value, $objs) {
			
			$updProject = [
				'id' => $project['id']
				, 'object_bp_type' => $project['object_bp_type']
			];				
			
			$totalInvoiceValue = $objs->simpleSum(
				'invoices'
				, 'amount'
				, [
					'related_object' => $proposalID
				]
			);

			$updProject['invoice_value'] = $totalInvoiceValue;
			
			$objs->update(
				$updProject['object_bp_type']
				, $updProject
				, 0
				, null
			);

			return $updProject;
			
		};		
		
		$objs = $setup['objs'];
		$old = $setup['old'];
		$new = $setup['new'];
		$options = $setup['setup'];
		
		$project = $objs->getById(
			''
			, $options['project']
			, [
				'invoice_value' => true
				, 'name' 		 => true
			]
		);

		$proposal = $objs->where(
			'proposals'
			, [
				'main_object' => $project['id']
			]
		);

		$updatedProject = $upstreamInvoiceValue(
			$project
			, $proposal[0]['id']
			, $options['processUpdatedPrice']
			, $objs
		);		

		return true;				
	}
	
];
	
?>