<?php

return [
	'name' 		=> 'createPortalUser'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 		$setup['objs'];
		$comm = new Comm($objs, $setup['objs']->rules->appConfig);		
		$old = 			$setup['old'];
		$new = 			$setup['new'];
		$setup = 		$setup['setup'];
		$provider = 	$objs->instance;

		// Find instance for the new user.
		if ($setup['contact'] && $setup['instance']) {
			
			$clientInstance = $objs->whereAll(
				'instances'
				, [
					'instance' => $setup['instance']
				]
			)[0];
			
		} else {
			
			$companyToken = $objs->where(
				'portal_access_token'
				, [
					'company' => 		$new['company']
					, 'is_active' => 	true
				]
			)[0];
						
			$clientInstance = $objs->whereAll(
				'instances'
				, [
					'id' => $companyToken['client']
				]
			)[0];

		}
		
		// If no instance found, do not create user
		if ($clientInstance == null) {
			
			return [
				'msg' => 			'No system found for this Client.'
				, 'continue' => 	false
			];
			
		}
			
		// Create new user from contact data.
		$contact = $objs->getById(
			'contacts'
			, $new['id']
			, [
				'fname' => 			true
				, 'lname' => 		true
				, 'name' => 			true
				, 'contact_info' => 	[
					'info' => 	true
					, 'type' => 	[
						'data_type' => true
					]
				]
			]
		);
		$objs->update(
			'contacts'
			, [
				'id' => 			$contact['id']
				, 'is_tag' => 	true
			]
		);
		
		// Get email address for first user.
		$email = '';
		foreach ($contact['contact_info'] as $info) {
			
			if ($info['type']['data_type'] === 'email') {
				
				$email = $info['info'];
				
			}
			
		}
				
		$password = $objs->createNewPassword();
		
		$user = [
			'fname' => 		$contact['fname']
			, 'lname' => 		$contact['lname']
			, 'email' => 		$email
			, 'enabled' => 	1
			, 'instance' => 	$clientInstance['instance']
			, 'password' => 	$password['pwdHash']
		];
		
		$objs->setInstance($clientInstance['instance']);
		
		$user = $objs->where(
			'users'
			, [
				'email' => 	 	$email
				, 'enabled' => 	1
			]
		)[0];
		
		$existingUser = $user;
			
		// Find/create user in client instance
		if ( !empty($existingUser) ) {
			
			$initialUser = $existingUser;
			
		} else {
			
			$initialUser = $objs->create(
				'users'
				, $user
			);
			
		}
		
		// Create token between user and contact.
		$objs->setInstance($provider);
		
		$token = $objs->create(
			'portal_access_token'
			, [
				'is_active' => 	true
				, 'client' => 	$clientInstance['id']
				, 'contact' => 	$new['id']
				, 'user' => 		$initialUser['id']
				, 'company' => 	$new['company']
			]
		);
		$providerHq = $objs->where(
			'groups'
			, [
				'group_type' => 'Headquarters'
			]
		)[0];
		
		// Send email
		$link = 'https://bento.infinityhospitality.net/';
		$title = $providerHq['name'] .' Account';
		$pwdTxt = '';
		if ($setup['user']) {
			$pwdTxt = '';
		} else {
			$pwdTxt = 'Your password is: '. $password['pwd'] .'';
		}
		
		$emailBody = '<table style="font-family: sans-serif !important;" align="" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
							<tr>
					        	<td align="center" bgcolor="#5084CC" style="padding: 5px; color: #ffffff; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold; letter-spacing:-.5px;"></td>
							</tr>
							<tr>
								<td style="padding:10px 10px;">
									<h2>'. $this->appConfig['systemName'] .'</h2>
								</td>
							</tr>
							<tr>
								<td style="padding:10px 10px;">'. $providerHq['name'] .' has granted permission for you to sign in. <br/> '. $pwdTxt .'</td>
							</tr>
							<tr>
								<td style="padding:10px 10px;">
									<br />
									<a href="'.$link.'" style="color: #5084cc; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold;">View in Bento</a>
								</td>
							</tr>
						</table>';
						
		$mergevars = [
			'TITLE' => $title,
			'BODY' => $emailBody,
			'BUTTON' => 'Log in to Bento Systems',
			'BUTTON_LINK' => $link,
			'INSTANCE_NAME' => $this->appConfig['instance']
			
		];
// 		error_reporting(E_ALL);
// 		ini_set('display_errors', '1');
// 		var_dump([$user['email']], $password, $token);
		$comm->sendMandrillEmail(
			[$user['email']], 
			null, 
			$title, 
			$mergevars, 
			null, 
			$clientInstance['id'], 
			1,
			0,
			false,
			null,
			$provider,
			0,
			$new['tagged_with']
		);
		
		return [
			'msg' => 	'Login access has been granted for '. $user['fname'] .' '. $user['lname'] .', '.
						' and an email sent to '. $user['email'] .' with login credentials. Please wait for app to refresh.'
		];
		
		return false;
		
	}
];
