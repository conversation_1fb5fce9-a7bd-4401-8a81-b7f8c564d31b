<?php

function applyLineItemDiscounts ($menu, $discounts, $pricingBreakdown) {

	$lineItems = $menu['_lineItems']; 	

	if (is_array($discounts)) {
		foreach ($discounts as $i => $discount) {

			$discount[$i]['amt_applied'] = 0;
			switch ($discount['apply_to']) {

				case 'line_item':

					switch ($discount['type']) {

						case 'amount_off':
							// Get line items to apply to
							foreach ($lineItems as $j => $lineItem) {

								if ($lineItem['item']['id'] === $discount['item']) {

									if ($lineItems[$j]['price_with_line_item_discounts'] >= $discount['factor']) {
	
										$lineItems[$j]['price_with_line_item_discounts'] -= $discount['factor'];
										$discounts[$i]['amt_applied'] += $discount['factor'];
	
									} else {
										
										$discounts[$i]['amt_applied'] += $lineItems[$j]['price_with_line_item_discounts'];
										$lineItems[$j]['price_with_line_item_discounts'] -= 0;
	
									}

								}

							}
							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;
						
						case 'percent_off':

							// Get line items to apply to
							foreach ($lineItems as $j => $lineItem) {

								if ($lineItem['item']['id'] === $discount['item']) {

									$amtOff = intval($lineItems[$j]['price_with_line_item_discounts']*($discount['factor']/1000000));
									$lineItems[$j]['price_with_line_item_discounts'] -= $amtOff;
									$discounts[$i]['amt_applied'] += $amtOff;

								}

							}

							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;
						
						case 'replace_amount':

							// Get line items to apply to
							foreach ($lineItems as $j => $lineItem) {

								if ($lineItem['item']['id'] === $discount['item']) {

									$amtOff = $lineItems[$j]['price_with_line_item_discounts'] - $discount['factor'];
									$lineItems[$j]['price_with_line_item_discounts'] -= $amtOff;
									$discounts[$i]['amt_applied'] += $amtOff;

								}

							}

							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;

					}

			}

		}
	}

	foreach ($lineItems as $i => $lineItem) {

		$lineItems[$i]['price_with_cat_discounts'] = 		$lineItems[$i]['price_with_line_item_discounts'];
		$lineItems[$i]['price_with_overall_discounts'] = 	$lineItems[$i]['price_with_line_item_discounts'];

	}

	$menu['_lineItems'] = $lineItems;
	return [
		'menu' => 			$menu
		, 'discounts' => 	$discounts
		, 'breakdown' => 	$pricingBreakdown
	];
	
}

function applyCategoryDiscounts ($menu, $discounts, $pricingBreakdown) {

	$lineItems = $menu['_lineItems']; 	

	if (is_array($discounts)) {
		foreach ($discounts as $i => $discount) {

			$discount[$i]['amt_applied'] = 0;
			switch ($discount['apply_to']) {

				case 'category':

					switch ($discount['type']) {

						case 'amount_off':

							$remainingDiscount = $discount['factor'];
							// Get line items to apply to
							foreach ($lineItems as $j => $lineItem) {

								if (
									(
										$lineItem['item']['category'] === $discount['category']
										|| in_array($lineItem['item']['category'], $discount['categories'])
									)
									|| (
										is_array($lineItem['item']['category'])
										&& (
											$lineItem['item']['category']['id'] === $discount['category']
											|| in_array($lineItem['item']['category']['id'], $discount['categories'])
										)
									)
								) {

									if ($lineItems[$j]['price_with_cat_discounts'] >= $remainingDiscount) {
	
										$lineItems[$j]['price_with_cat_discounts'] -= $remainingDiscount;
										$discounts[$i]['amt_applied'] += $remainingDiscount;
										$remainingDiscount = 0;
	
									} else {
										
										$discounts[$i]['amt_applied'] += $lineItems[$j]['price_with_cat_discounts'];
										$remainingDiscount -= $lineItems[$j]['price_with_cat_discounts'];
										$lineItems[$j]['price_with_cat_discounts'] = 0;
	
									}

								}

							}
							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;
						
						case 'percent_off':

							// Get line items to apply to
							foreach ($lineItems as $j => $lineItem) {

								if (
									(
										$lineItem['item']['category'] === $discount['category']
										|| in_array($lineItem['item']['category'], $discount['categories'])
									)
									|| (
										is_array($lineItem['item']['category'])
										&& (
											$lineItem['item']['category']['id'] === $discount['category']
											|| in_array($lineItem['item']['category']['id'], $discount['categories'])
										)
									)
								) {

									$amtOff = intval($lineItems[$j]['price_with_cat_discounts']*($discount['factor']/1000000));
									$lineItems[$j]['price_with_cat_discounts'] -= $amtOff;
									$discounts[$i]['amt_applied'] += $amtOff;

								}

							}

							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;
						
						case 'replace_amount':

							// Consolidate category/categories props in case of old data
							if (is_int($discounts['category'])) {
								
								array_push($discounts['categories'], $discount['category']);
								$discount['categories'] = array_unique($discount['categories']);

							}

							// For each category in discounts
							foreach ($discount['categories'] as $catId) {

								$catTotal = 0;

								// For each item in the category
								foreach ($lineItems as $j => $lineItem) {

									if (
										(
											$lineItem['item']['category'] === $discount['category']
											|| in_array($lineItem['item']['category'], $discount['categories'])
										)
										|| (
											is_array($lineItem['item']['category'])
											&& (
												$lineItem['item']['category']['id'] === $discount['category']
												|| in_array($lineItem['item']['category']['id'], $discount['categories'])
											)
										)
									) {

										// If the total goes over the set to amount
										if ($catTotal + $lineItems[$j]['price_with_cat_discounts'] > $discount['factor']) {

											// Reduce the amount on the line item to meet
											// the specified total for the category.
											$lineItems[$j]['price_with_cat_discounts'] -= ($catTotal + $lineItem['price_with_cat_discounts'] - $discount['factor']);
		
											// Update the tracked amount applied on the discount
											$discounts[$i]['amt_applied'] += -($lineItems[$j]['price_with_cat_discounts'] - $lineItems[$j]['price_with_line_item_discounts']);

										} 

										// Track the total for the category
										$catTotal += $lineItems[$j]['price_with_cat_discounts'];

									}

								}

							}

							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;

					}

			}

		}
	}

	foreach ($lineItems as $i => $lineItem) {
		
		$lineItems[$i]['price_with_overall_discounts'] = $lineItems[$i]['price_with_cat_discounts'];
		
	}

	$menu['_lineItems'] = $lineItems;
	return [
		'menu' => 			$menu
		, 'discounts' => 	$discounts
		, 'breakdown' => 	$pricingBreakdown
	];
	
}

function applyOverallDiscounts ($menu, $discounts, $pricingBreakdown) {

	$lineItems = $menu['_lineItems']; 	

	if (is_array($discounts)) {
		foreach ($discounts as $i => $discount) {

			$discount[$i]['amt_applied'] = 0;
			switch ($discount['apply_to']) {

				case 'workorder':

					switch ($discount['type']) {

						case 'amount_off':

							$discountAmt = $discount['factor'];

							// Get the overall total
							$currentOverallTotal = 0;
							foreach ($lineItems as $j => $lineItem) {

								$currentOverallTotal += $lineItems[$j]['price_with_overall_discounts'];

							}

							// If discount is greater than overall amt, set to the overall amt
							if ($discountAmt > $currentOverallTotal) {
								$discountAmt = $currentOverallTotal;
							}

							// Spread the discount evenly across all items
							foreach ($lineItems as $j => $lineItem) {
								
								// Remove ratio of line total to overall total of the discount
								$amtToRm = intval(($lineItems[$j]['price_with_overall_discounts']/$currentOverallTotal)*$discountAmt);
								
								$lineItems[$j]['price_with_overall_discounts'] -= $amtToRm;
								$discounts[$i]['amt_applied'] += $amtToRm;

								// !On the last item, if off by a penny, add/rm from the last item
								if (
									count($lineItems) - 1 === $j
									&& $discounts[$i]['amt_applied'] !== $discountAmt
								) {

									$lineItems[$j]['price_with_overall_discounts'] -= ($discountAmt - $discounts[$i]['amt_applied']);
									$discounts[$i]['amt_applied'] += ($discountAmt - $discounts[$i]['amt_applied']);

								}
							
							}

							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;
						
						case 'percent_off':

							// Get line items to apply to
							foreach ($lineItems as $j => $lineItem) {

								$amtOff = intval($lineItems[$j]['price_with_overall_discounts']*($discount['factor']/1000000));
								$lineItems[$j]['price_with_overall_discounts'] -= $amtOff;
								$discounts[$i]['amt_applied'] += $amtOff;

							}

							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;
						
						case 'replace_amount':

							$catTotal = 0;

							// For each item in the category
							foreach ($lineItems as $j => $lineItem) {

								// If the total goes over the set to amount
								if ($catTotal + $lineItems[$j]['price_with_overall_discounts'] > $discount['factor']) {

									// Reduce the amount on the line item to meet
									// the specified total for the category.
									$lineItems[$j]['price_with_overall_discounts'] -= ($catTotal + $lineItem['price_with_overall_discounts'] - $discount['factor']);

									// Update the tracked amount applied on the discount
									$discounts[$i]['amt_applied'] += -($lineItems[$j]['price_with_overall_discounts'] - $lineItems[$j]['price_with_cat_discounts']);

								} 

								// Track the total for the category
								$catTotal += $lineItems[$j]['price_with_overall_discounts'];

							}

							$pricingBreakdown['breakdown']['discounts'][$discount['id']] = -$discounts[$i]['amt_applied'];
							break;

					}

			}

		}
	}

	$menu['_lineItems'] = $lineItems;
	return [
		'menu' => 			$menu
		, 'discounts' => 	$discounts
		, 'breakdown' => 	$pricingBreakdown
	];
	
}

function applyTaxes ($menu, $taxRates, $cats, $pricingBreakdown, $client) {


	$lineItems = $menu['_lineItems'];
	$includeSurfaceChargesInTax = ($menu['instance'] == 'dreamcatering');

	if (is_array($lineItems)) {
		foreach ($lineItems as $i => $item) {

			// Find tax rates to apply, either on the item directly (if
			// it is set to override the category) or falling back to 
			// the defaults set on the category.
			$taxesToApply = [];
			if ($item['override_tax_rates']) {
				
				$taxesToApply = $item['tax_rates'];

			} else {
				
				$cat = __::find($cats, function ($cat) use ($item) {
					
					if (is_array($item['item']['category'])) {
						return intval($item['item']['category']['id']) === intval($cat['id']);
					} else {
						return intval($item['item']['category']) === intval($cat['id']);
					}
					
				});

				$taxesToApply = $cat['default_tax_rate'];

			}

			// Apply the tax rates, updating the pricing breakdown entry
			if (is_array($taxesToApply)) {
				foreach ($taxesToApply as $j => $rateToApply) {

					$rateObj = __::find($taxRates, function ($rate) use ($rateToApply) {
				
						if (is_array($rateToApply)) {
							return intval($rateToApply['id']) === intval($rate['id']);
						} else {
							return intval($rateToApply) === intval($rate['id']);
						}
						
					});

					// Only apply the tax is the client is not tax exempt 
					// AND the tax rate is not set to override that
					if (
						!(
							$client['tax_exempt']
							&& !$rateObj['always_apply']
						)
					) {

						$taxAmt = 0;

						switch ($rateObj['type']) {

							case 'exclusive':
								//tax amount
								$taxAmt = intval($item['price_with_overall_discounts']*($rateObj['rate']/10000));

								if($includeSurfaceChargesInTax) {
									$sumSurchargesApplied = 0;
									foreach ($item['applied_surcharges'] as $key => $_surchargeAmount) {
										$sumSurchargesApplied += $_surchargeAmount;
									}
									$taxAmt = intval(($item['price_with_overall_discounts']*($rateObj['rate']/10000)) + $sumSurchargesApplied * ($rateObj['rate']/10000));
								}

								if (is_int($taxAmt)) {

									// Update the line item and the tax entry on the breakdown
									// $lineItems[$i]['tax_amt'] += $taxAmt;

								}
								break;

							case 'inclusive':

								$taxAmt = intval(round(($item['price_with_overall_discounts']*($rateObj['rate']/100))/100));;

								if($includeSurfaceChargesInTax) {

									$sumSurchargesApplied = 0;
									foreach ($item['applied_surcharges'] as $key => $_surchargeAmount) {
										$sumSurchargesApplied += $_surchargeAmount;
									}
									$taxAmt = intval(round(($item['price_with_overall_discounts']*($rateObj['rate']/100))/100) + round(($sumSurchargesApplied *($rateObj['rate']/100))/100));;
								}

								if (is_int($taxAmt)) {

									// Update the line item and the tax entry on the breakdown
									// $lineItems[$i]['tax_amt'] += $taxAmt;
									$lineItems[$i]['price_to_chart_of_acct'] -= $taxAmt;

								}
								break;

						}

						// Update the tax amounts tracked on the breakdown.
						if (is_int($pricingBreakdown['breakdown']['taxes'][$rateObj['id']])) {

							$pricingBreakdown['breakdown']['taxes'][$rateObj['id']] += $taxAmt;

						} else {

							$pricingBreakdown['breakdown']['taxes'][$rateObj['id']] = $taxAmt;

						}

						$lineItems[$i]['applied_taxes'][$rateObj['id']] = $taxAmt;
					
					}

				}
			}

		}
	}

	$menu['_lineItems'] = $lineItems;

	return [
		'menu' => 			$menu
		, 'breakdown' => 	$pricingBreakdown
	];

}

function applySurcharges ($menu, $allSurcharges, $cats, $pricingBreakdown) {

	$lineItems = $menu['_lineItems']; 	

	if (is_array($lineItems)) {
		foreach ($lineItems as $i => $item) {

			// Find tax rates to apply, either on the item directly (if
			// it is set to override the category) or falling back to 
			// the defaults set on the category.
			$surcharges = $item['surcharges'];

			// Apply the tax rates, updating the pricing breakdown entry
			if (is_array($surcharges)) {
				foreach ($surcharges as $j => $rateToApply) {

					$surchargeObj = __::find($allSurcharges, function ($rate) use ($rateToApply) {

						if (is_array($rateToApply)) {
							return intval($rateToApply['id']) === intval($rate['id']);
						} else {
							return intval($rateToApply) === intval($rate['id']);
						}
						
					});

					$surchargeAmt = intval($item['absolute_price']*($surchargeObj['rate']/10000));

					// Update the tax amounts tracked on the breakdown.
					if (is_int($pricingBreakdown['breakdown']['surcharges'][$surchargeObj['id']])) {

						$pricingBreakdown['breakdown']['surcharges'][$surchargeObj['id']] += $surchargeAmt;

					} else {

						$pricingBreakdown['breakdown']['surcharges'][$surchargeObj['id']] = $surchargeAmt;

					}

					$lineItems[$i]['applied_surcharges'][$surchargeObj['id']] = $surchargeAmt;

				}
			}

		}
	}

	$menu['_lineItems'] = $lineItems;
	return [
		'menu' => 			$menu
		, 'breakdown' => 	$pricingBreakdown
	];

}

function setCatAndCoaArrays($menu, $cats, $pricingBreakdownRecord) {

	$lineItems = $menu['_lineItems']; 	

	$pricingBreakdownRecord['breakdown']['by_section'] = [];
	$pricingBreakdownRecord['breakdown']['by_section_without_discounts'] = [];
	$pricingBreakdownRecord['breakdown']['subTotal'] = 0;
	$pricingBreakdownRecord['breakdown']['total'] = 0;

	foreach ($lineItems as $item) {
		
		// Set the categories, cats_without_discounts, chartOfAccts
		// and vendorChartOfAccts entries on the pricing breakdown record.

		// Update the subtotal
		$pricingBreakdownRecord['breakdown']['subTotal'] += intval($item['absolute_price']);
		$pricingBreakdownRecord['breakdown']['total'] += intval($item['price_with_overall_discounts']);

		// Get the item category
		$cat = __::find($cats, function ($cat) use ($item) {
				
			if (is_array($item['item']['category'])) {
				return intval($item['item']['category']['id']) === intval($cat['id']);
			} else {
				return intval($item['item']['category']) === intval($cat['id']);
			}
			
		});

		// Update the categories arrays
		if (array_key_exists($cat['id'], $pricingBreakdownRecord['breakdown']['categories'])) {
				
			$pricingBreakdownRecord['breakdown']['categories'][$cat['id']] += intval($item['price_with_cat_discounts']);
			$pricingBreakdownRecord['breakdown']['cats_without_discounts'][$cat['id']] += intval($item['price_with_line_item_discounts']);
			
		} else {

			$pricingBreakdownRecord['breakdown']['categories'][$cat['id']] = intval($item['price_with_cat_discounts']);
			$pricingBreakdownRecord['breakdown']['cats_without_discounts'][$cat['id']] = intval($item['price_with_line_item_discounts']);
			
		}

		// Update the sections arrays
		if (array_key_exists($item['section'], $pricingBreakdownRecord['breakdown']['by_section'])) {
				
			$pricingBreakdownRecord['breakdown']['by_section'][$item['section']] += intval($item['price_with_overall_discounts']);
			$pricingBreakdownRecord['breakdown']['by_section_without_discounts'][$item['section']] += intval($item['absolute_price']);
			
		} else {

			$pricingBreakdownRecord['breakdown']['by_section'][$item['section']] = intval($item['price_with_overall_discounts']);
			$pricingBreakdownRecord['breakdown']['by_section_without_discounts'][$item['section']] = intval($item['absolute_price']);
			
		}

		// For vendors, group the chart of accout by vendor
		if ($item['vendor']) {

			if (!array_key_exists($item['vendor'], $pricingBreakdownRecord['breakdown']['vendorChartOfAccts'])) {
				
				$pricingBreakdownRecord['breakdown']['vendorChartOfAccts'][$item['vendor']] = [];
				
			}
			
			if (array_key_exists($cat['chart_of_account'], $pricingBreakdownRecord['breakdown']['vendorChartOfAccts'][$item['vendor']])) {
				
				$pricingBreakdownRecord['breakdown']['vendorChartOfAccts'][$item['vendor']][$cat['chart_of_account']] += $item['price_to_chart_of_acct'];
				
			} else {
				
				$pricingBreakdownRecord['breakdown']['vendorChartOfAccts'][$item['vendor']][$cat['chart_of_account']] = $item['price_to_chart_of_acct'];
				
			}
			
		} else {
			
			if($item['coa'] === 1){

				if($item['item']['chart_of_account']['id']){

					$item['coa'] = $item['item']['chart_of_account']['id'];

				}else{
					
					$item['coa'] = $cat['chart_of_account'];
					
				}

				
			}

			if (array_key_exists($item['coa'], $pricingBreakdownRecord['breakdown']['chartOfAccts'])) {
				
				$pricingBreakdownRecord['breakdown']['chartOfAccts'][$item['coa']] += $item['price_to_chart_of_acct'];

			} else {

				$pricingBreakdownRecord['breakdown']['chartOfAccts'][$item['coa']] = $item['price_to_chart_of_acct'];

			}
		
		}

	}

	return $pricingBreakdownRecord;

}

function filterTaxRatesByState ($taxRateObjs, $menu, $objs) {
	
	$taxRateIds = []; // To return
	$related = $objs->where(
		'proposals'
		, [
			'menu' => $menu['id']
		]
		, ''
		, [
			'main_object' => [
				'locations' => [
					'state' => true
				]
			]
		]
	)[0];
	$applicableStates = [];
	if (
		is_array($related)
		&& is_array($related['main_object'])
		&& is_array($related['main_object']['locations'])
	) {
		
		$applicableStates = __::pluck(
			$related['main_object']['locations']
			, 'state'
		);
		
	}
	
	if (!empty($applicableStates)) {
		
		if (is_array($taxRateObjs)) {
			
			foreach ($taxRateObjs as $taxRate) {
				
				if (
					// If no tax rate is set, always apply
					(
						empty($taxRate['state']) 
						or $taxRate['state'] === 'n/a'
					)
					// Apply if the tax rate state is in the applicable states
					or in_array($taxRate['state'], $applicableStates)
				) {
					
					array_push($taxRateIds, $taxRate);
					
				}
				
			}
			
		}
		
	} else {
		
		$taxRateIds = $taxRateObjs;
		
	}
	
	return $taxRateIds;
	
}

return [
	'name' 		=> 'setMenuPricingRecord'
	, 'process'	=> function ($setup, $run) {
		
// 		error_reporting(E_ALL);
// 		ini_set('display_errors', '1');
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new'];
		$setup = 	$setup['setup'];
		$initialTotal = 0;
		$start = $objs->getById(
			'inventory_menu_pricing_breakdown'
			, $setup['id']
			, [
				'total' => true
			]
		);
		$initialTotal = $start['total'];
		
		
		// Record to save
		$pricingBreakdownRecord = [
			'id' => 			$setup['id']
			, 'active' => 		$setup['active']
			, 'breakdown' => 	[
				'categories' => 				[]
				, 'cats_without_discounts' => 	[]
				, 'labor' => 					[]
				, 'subTotal' => 				0
				, 'surcharges' => 				[]
				, 'taxes' => 					[]
				, 'total' => 					0
				, 'chartOfAccts' => 			[]
				, 'vendorChartOfAccts' => 		[]
			]
			, 'total' => 		$setup['total']
			, 'space' => 		$setup['space']
			, 'event_date' => 	$setup['event_date']
			, 'is_template' => 	$setup['is_template']
			, 'client' => 		$setup['client']
		];
		
		if (empty($pricingBreakdownRecord['breakdown'])) {
			$pricingBreakdownRecord['breakdown'] = [];
		}

		// Get the client obj
		$client = $objs->getById(
			'companies'
			, $setup['client']
			, ['tax_exempt' => true]
		);
		
		// Gather values across line items
		$coaSums = [];
		$vendorCoaSums = [];
		$proposal = $objs->getById('proposals', intval($obj['id']));
		$menu = $objs->getById(
			'inventory_menu'
			, intval($obj['id'])
			, [
				'sections' => [
					'from' => true
					, 'to' => true
					, 'items' => [
						'coa' => 				'id'
						, 'absolute_price' => 	true
						, 'absolute_qty' => true
						, 'name' => 			true
						, 'item' => 			true
						, 'vendor' => 			'id'
						, 'tax_rates' => 		'id'
						, 'surcharges' => 		'id'
						, 'override_tax_rates' => true
						, 'price_type' => true
						, 'section' => true
						, 'qty' => true
					]
				]
				, 'guest_count' => true
			]
		);

		// Get categories
		$catIds = [];
		if (is_array($menu['sections'])) {
			foreach ($menu['sections'] as $section) {
				
				foreach ($section['items'] as $item) {
					
					if ($item['item']) {
						
						if (is_array($item['item']['category'])) {
							
							array_push($catIds, intval($item['item']['category']['id']));
							
						} elseif (is_numeric($item['item']['category'])) {
							
							array_push($catIds, intval($item['item']['category']));
							
						}
						
					}
					
				}
				
			}
		}
		$cats = $objs->getById('', $catIds);
		$taxRates = $objs->getAll(
			'tax_rates'
			, [
				'chart_of_account' => 	'id'
				, 'type' => 			true
				, 'rate' => 			true
				, 'state' => 			true
			]
		);
		$surcharges = $objs->getAll(
			'surcharges'
			, [
				'chart_of_account' => 	'id'
				, 'type' => 			true
				, 'rate' => 			true
				, 'state' => 			true
			]
		);
		
		// If a state/province is set on the project (.locations), exclude tax
		// rates that have a state/province set that does not fall into the 
		// locations list.
		$taxRates = filterTaxRatesByState($taxRates, $menu, $objs);

		// Place a flat list of line items at _lineItems
		$menu['_lineItems'] = [];
		if (is_array($menu['sections'])) {
			foreach ($menu['sections'] as $section) {

				if (is_array($section['items'])) {
					foreach ($section['items'] as $item) {

						$item['absolute_price'] = $GLOBALS['app']->getLineItemAbsolutePrice(
							[
								'price_type' => 	$item['price_type']
								, 'section' => 		$item['section']
								, 'qty' => 			$item['qty']
								, 'item' => 		$item['item']
							]
							, $menu
						);
						
						// Apply markups for vendors
						if ($item['vendor']) {
							
							$vendor = $objs->getById('', $item['vendor']);
							if (is_array($vendor) && is_int(intval($vendor['markup_percent']))) {
								$item['absolute_price'] = intval($item['item']['price'] + ((intval($vendor['markup_percent'])/100) * $item['item']['price']));
							}
							
						}

						// Set initial values for discount/tax/surcharge pricing
						$item['price_to_chart_of_acct'] = 			$item['absolute_price'];
						$item['price_with_line_item_discounts'] = 	$item['absolute_price'];
						$item['price_with_cat_discounts'] = 		$item['absolute_price'];
						$item['price_with_overall_discounts'] = 	$item['absolute_price'];
						$item['tax_amt'] = 							0;
						$item['surchart_amt'] = 					0;
						$item['applied_taxes'] = 					[];
						$item['applied_surcharges'] = 				[];

						array_push($menu['_lineItems'], $item);

					}
				}

			}
		}
		
		// Apply pre-tax discounts
		$discounts = $objs->where(
			'discounts'
			, ['menu' => $menu['id']]
		);
		$ret = applyLineItemDiscounts($menu, $discounts, $pricingBreakdownRecord);
		$menu = $ret['menu'];
		$discounts = $ret['discounts'];
		$pricingBreakdownRecord = $ret['breakdown'];

		$ret = applyCategoryDiscounts($menu, $discounts, $pricingBreakdownRecord);
		$menu = $ret['menu'];
		$discounts = $ret['discounts'];
		$pricingBreakdownRecord = $ret['breakdown'];
		
		$ret = applyOverallDiscounts($menu, $discounts, $pricingBreakdownRecord);
		$menu = $ret['menu'];
		$discounts = $ret['discounts'];
		$pricingBreakdownRecord = $ret['breakdown'];

		// Apply surcharges
		$ret = applySurcharges($menu, $surcharges, $cats, $pricingBreakdownRecord);
		$menu = $ret['menu'];
		$pricingBreakdownRecord = $ret['breakdown'];

		// Apply taxes
		$ret = applyTaxes($menu, $taxRates, $cats, $pricingBreakdownRecord, $client);
		$menu = $ret['menu'];
		$pricingBreakdownRecord = $ret['breakdown'];

		// Set the categories, cats_without_discounts, chartOfAccts
		// and vendorChartOfAccts entries on the pricing breakdown record.
		$pricingBreakdownRecord = setCatAndCoaArrays($menu, $cats, $pricingBreakdownRecord);

		// Update line item new vals
		$lineItemUpdates = [];
		foreach ($menu['_lineItems'] as $i => $lineItem) {

			array_push(
				$lineItemUpdates
				, [
					'id' 					=> $lineItem['id']
					, 'absolute_price' 		=> $lineItem['absolute_price']
					, 'applied_taxes' 		=> $lineItem['applied_taxes']
					, 'applied_surcharges' 	=> $lineItem['applied_surcharges']
				]
			);

		}

		$objs->update(
			'inventory_menu_line_item'
			, $lineItemUpdates
		);
		
		// !TODO: Update discount new vals
		// var_dump($pricingBreakdownRecord['breakdown']);
		// die();

		// Include surcharges in chart of accts
		if (is_array($surcharges)) {
			
			foreach ($surcharges as $surcharge) {
				
				if (array_key_exists($surcharge['id'], $pricingBreakdownRecord['breakdown']['surcharges'])) {
					
					if (array_key_exists($surcharge['chart_of_account'], $pricingBreakdownRecord['breakdown']['chartOfAccts'])) {
						
						$pricingBreakdownRecord['breakdown']['chartOfAccts'][$surcharge['chart_of_account']] += intval($pricingBreakdownRecord['breakdown']['surcharges'][$surcharge['id']]);
						
					} else {
						
						$pricingBreakdownRecord['breakdown']['chartOfAccts'][$surcharge['chart_of_account']] = intval($pricingBreakdownRecord['breakdown']['surcharges'][$surcharge['id']]);
						
					}
					
				}
				
			}
			
		}

		// Include discounts in chart of accts
		if (is_array($discounts)) {
			
			foreach ($discounts as $discount) {
				
				if (array_key_exists($discount['chart_of_account'], $pricingBreakdownRecord['breakdown']['chartOfAccts'])) {
					
					$pricingBreakdownRecord['breakdown']['chartOfAccts'][$discount['chart_of_account']] += -$discount['amt_applied'];
					
				} else {
					
					$pricingBreakdownRecord['breakdown']['chartOfAccts'][$discount['chart_of_account']] = -$discount['amt_applied'];
					
				}
				
			}
			
		}

		// Add tax vals into the chart of accts
		if (is_array($pricingBreakdownRecord['breakdown']['taxes'])) {
			
			foreach ($pricingBreakdownRecord['breakdown']['taxes'] as $taxKey => $amt) {
				
				$taxRate = __::find($taxRates, function ($taxRate) use ($taxKey) {
					
					return $taxKey == $taxRate['id'];
					
				});
				
				if ($pricingBreakdownRecord['breakdown']['chartOfAccts'][$taxRate['chart_of_account']]) {
					
					$pricingBreakdownRecord['breakdown']['chartOfAccts'][$taxRate['chart_of_account']] += (int) $amt;

				} else {
					
					$pricingBreakdownRecord['breakdown']['chartOfAccts'][$taxRate['chart_of_account']] = (int) $amt;
					
				}
				
			}
			
		}

		// Update the total w/taxes and surcharges
		foreach ($pricingBreakdownRecord['breakdown']['taxes'] as $taxKey => $amt) {

			$taxRate = __::find($taxRates, function ($taxRate) use ($taxKey) {
					
				return $taxKey == $taxRate['id'];
				
			});

			// Only add the exclusive taxes
			if ($taxRate['type'] === 'exclusive') {
				$pricingBreakdownRecord['breakdown']['total'] += $amt;	
			}

		}
		foreach ($pricingBreakdownRecord['breakdown']['surcharges'] as $surchargeId => $amt) {
			$pricingBreakdownRecord['breakdown']['total'] += $amt;
		}

		//!TODO: Put in checks to make sure totalling by cats/taxes/surcharges matches
		// totalling by chart of accts.
		// var_dump($pricingBreakdownRecord['breakdown']);
		// die();

		$pricingBreakdownRecord['total'] = $pricingBreakdownRecord['breakdown']['total'];

		$pricingBreakdownRecord['breakdown']['without_discounts'] = [
			'categories' => $pricingBreakdownRecord['breakdown']['cats_without_discounts']
		];
		unset($pricingBreakdownRecord['breakdown']['cats_without_discounts']);

		// Update the pricing record
		$updated = $objs->update(
			'inventory_menu_pricing_breakdown'
			, $pricingBreakdownRecord
		);
		$msg = '';

		// If the pricing has changed, update the value on the project and
		// send a message to the user.
		if ($updated['total'] !== $initialTotal) {
			
			$objs->update(
				'groups'
				, [
					'id' => $updated['space']
					, 'invoice_value' => $updated['total']
				]
			);
			$msg = 'Pricing updated.';

		}
		
		return [
			'msg' => 		$msg
			, 'update' => 	[
                'pricing_breakdown' => $updated
            ]
		];
		
	}
];

?>
