<?php

return [
	'name' 		=> 'FGcreateAssuranceMembership'
	, 'process'	=> function ($setup, $run) {

		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$setup = 	$setup['setup'];
		
		// Set the parent property
		$defaults = array();
		$template = array();
		$defaults['name'] = $projectName;
		$defaults['parent'] = [ 'id' => $mainContactCompany ];
		$template['tagged_with'] = [$mainContact['id']];
		$template['shared_with'] = [$mainContact['id']];
		$template['status'] = 'open';

		// Check for the tag for the specific year
		$yearTag = $objs->where(
			'system_tags'
			, [
				'tag' => strval($obj[$taxYearKey])
			]
			, ''
			, [
				'tag' => true
			]
		)[0];

		// Create a tag for the year if it does not exist
		if (empty($yearTag)) {

			$yearTag = $objs->create(
				'system_tags'
				, [
					'name' => 		strval($obj[$taxYearKey])
					, 'tag' => 		strval($obj[$taxYearKey])
					, 'color' => 	'black'
				]
				, 0
				, [
					'tag' => true
				]
			);

		}

		// Pass down tags from context
		$template['tagged_with'] = array_merge($obj['tagged_with'], $template['tagged_with']);
		$template['tagged_with'] = array_unique($template['tagged_with']);
		array_push($template['tagged_with'], 1636580, 1763496, $mainContact['id'], $mainContactCompany, $yearTag['id'], $obj['id']);

		$template['tagged_with'] = array_filter($template['tagged_with']);
		$template['shared_with'] = array_filter($template['shared_with']);

		// The Close Date is Start Date for Membership
		$template['_11'] = $obj['_58'];

		// The Expiration Date is one year following the Start Date
		$startDate = new DateTime($template['_11']);

		$template['name'] = 'Assurance Membership | '. $startDate->format('Y');
		$startDate->add(new DateInterval('P1Y'));
		$template['name'] .= ' - '. $startDate->format('Y');

		$template['_16'] = $startDate->format('Y-m-d H:i:s');
		$membership = $objs->create('#0ZZQgI.z2zAeS', $template);

		// Return message
		return [
			'msg' => '<strong>'. $membership['name'] .'</strong> <i>Assurance Membership</i> created.'
		];
		
	}
];

?>