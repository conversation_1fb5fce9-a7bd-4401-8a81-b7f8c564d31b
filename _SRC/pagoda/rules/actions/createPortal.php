<?php

return [
	'name' 		=> 'createPortal', 'process'	=> function ($setup, $run) {

		$objs = 		$setup['objs'];
		$comm = 		new Comm($objs, $setup['objs']->rules->appConfig);
		$old = 			$setup['old'];
		$new = 			$setup['new'];
		$setup = 		$setup['setup'];
		$provider = 	$objs->instance;
		$portalName = 	$setup['name'];
		$emailBody = 	'';
		$contactId = 	$setup['initialUser'];
		$email = 		'';
		$companyId = 	$old['id'];
		$company = 		[];
		$pwordMsg = 	'';
		$contactToken = [];

		// Parse input
		if (is_array($setup['options'])) {

			$portalName = 	$setup['options']['name'];
			$contactId = 	$setup['options']['contact'];

			if (is_array($setup['options']['email'])) {

				$email = 	$setup['options']['email'][0];
			} elseif (is_string($setup['options']['email'])) {

				$email = 	$setup['options']['email'];
			}
		}

		// Need an email address
		if (empty($email)) {

			return [
				'msg' => 'An email is needed to submit this request. Please try again.'
			];
		}

		// Need a contact to associate the new user with
		if (!intval($contactId)) {

			return [
				'msg' => 	'An contact with a valid email address is needed to submit ' .
					'this request. Please try again.'
			];
		}

		// Get the contact and its company
		$contact = $objs->getById(
			'contacts',
			$contactId,
			[
				'fname' => 			true, 'lname' => 		true, 'name' => 		true, 'company' => 		true, 'contact_info' => [
					'info' => 	true, 'type' => 	[
						'data_type' => true
					]
				]
			]
		);

		// Add the company associated with the contact for the user
		// to the new instance's name.
		if (!empty($contact['company']) && !empty($contact['company']['name'])) {
			$portalName = $contact['company']['name'];
		}

		if (is_array($setup['options'])) {

			$companyId = $contact['company']['id'];
		}

		// Create new instance from client data.
		$token = $objs->whereAll(
			'portal_access_token',
			[
				'is_active' => 	true, 'company' => 	$companyId
			]
		)[0];

		if (empty($token)) {

			$instance = [
				'instance' => 			preg_replace("/[^0-9]/", "", microtime()), 'is_portal' => 		true, 'systemName' => 		$portalName, 'enabled' => 			'Enabled', 'parentInstance' => 	$provider
			];

			$portal = $objs->createInstance(
				'instances',
				$instance
			);

			// !TODO: check if we need this--I don't think we do anymore.
			$objs->copyInstanceSettingsObjects(
				'bento',
				$instance['instance']
			);

			// Create token between instances.
			$objs->setInstance($provider);
			$token = $objs->create(
				'portal_access_token',
				[
					'is_active' => 	true, 'client' => 	$portal['id'], 'company' => 	$companyId
				]
			);
			$objs->update(
				'contacts',
				[
					'id' => 		$contactId, 'is_tag' => 	true
				]
			);

			$instance = $portal;
		} else {

			$portal = $objs->whereAll(
				'instances',
				[
					'id' => $token['client']
				]
			)[0];
			$objs->setInstance($provider);
			$instance['instance'] = $portal['instance'];

			// Get token associated with the user, if one exists
			$contactToken = $objs->where('portal_access_token', ['contact' => $contactId]);
		}

		// Create new user from contact data
		// Get email address for first user
		if (empty($email)) {

			foreach ($contact['contact_info'] as $info) {

				if ($info['type']['data_type'] === 'email') {

					$email = $info['info'];
				}
			}
		}

		// Check for user
		$objs->setInstance($instance['instance']);
		$user = $objs->where(
			'users',
			[
				'email' => 	 	$email, 'enabled' => 	1
			]
		)[0];

		$existingUser = $user;

		if (
			empty($user)
			|| empty($contactToken)
		) {

			$usrInOtherInst = $objs->whereAll(
				'users',
				[
					'email' => $email
				]
			);

			if (!empty($usrInOtherInst)) {

				$user = [
					'fname' => 		$contact['fname'], 'lname' => 	$contact['lname'], 'email' => 	$email, 'enabled' => 	1, 'password' => $usrInOtherInst[0]['password']
				];
			} else {

				$password = $objs->createNewPassword();
				$user = [
					'fname' => 		$contact['fname'], 'lname' => 	$contact['lname'], 'email' => 	$email, 'enabled' => 	1, 'password' => 	$password['pwdHash']
				];

				$pwordMsg = 'Your password is: ' . $password['pwd'];
			}

			if (empty($existingUser)) {

				$initialUser = $objs->create(
					'users',
					$user
				);

				// If a user is already in the portal instance for that company, 
				// update the password and send an email w/the new credentials.
			} else {

				$user['id'] = $existingUser['id'];

				$password = $objs->createNewPassword();
				$user['password'] = $password['pwdHash'];
				$pwordMsg = 'Your password is: ' . $password['pwd'];

				$initialUser = $objs->update(
					'users',
					$user
				);
			}

			// Create the hq for the new instance
			$objs->create(
				'groups',
				[
					'name' => 			$portalName, 'managers' => 	[], 'group_type' => 	'Headquarters', 'is_active' => 	false
				]
			);

			// Create new user's 'My Stuff' group and dashboard in provider instance.
			if (
				is_array($setup['options'])
			) {

				$objs->setInstance($provider);

				// Create the my stuff dashboard
				$layoutTemplate = $objs->getById(
					'layouts',
					$setup['options']['layout']
				);

				foreach ($layoutTemplate['tools'] as $i => $tool) {

					$layoutTemplate['tools'][$i]['allowed_users'] = [$contact['id']];
					$layoutTemplate['tools'][$i]['added_by'] = [$_COOKIE['uid']];
				}

				$myStuffGroup = $objs->create(
					'groups',
					[
						'group_type' => 	'MyStuff', 'user' => 		$contact['id'], 'name' => 		$contact['fname'] . ' ' . $contact['lname'], 'tools' => 		$layoutTemplate['tools']
					]
				);

				$layoutTemplate['is_template'] = 0;
				$layoutTemplate['layout_id'] = $myStuffGroup['id'];
				$layoutTemplate['user'] = $initialUser['id'];
				$objs->create('layouts', $layoutTemplate);
			}

			// Create token between user and contact.
			$objs->setInstance($provider);
			$token = $objs->create(
				'portal_access_token',
				[
					'is_active' => 	true, 'client' => 	$portal['id'], 'contact' => 	$contactId, 'user' => 	$initialUser['id'], 'company' => 	$companyId
				]
			);

			$providerHq = $objs->where(
				'groups',
				[
					'group_type' => 'Headquarters'
				]
			)[0];

			// Send email
			$link = 'https://bento.infinityhospitality.net/';
			$viewBtnTxt = 'View in Bento';
			if ($provider === 'foundation_group') {
				$link = 'https://foundationgroup.bento.infinityhospitality.net';
				$viewBtnTxt = 'Go to Portal';
			}

			$title = $providerHq['name'] . ' Account';
			if (
				is_array($setup['options'])
				&& is_array($setup['options']['body'])
			) {

				$emailBody = 	$providerHq['name'] . ' has given you access. Log in to see what they have shared with you. <br/> ' . $pwordMsg .
					$setup['options']['body']['html'] .
					'<a href="' . $link . '" align="center" bgcolor="#5084CC" style="padding: 14px; color: #ffffff; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold; letter-spacing:-.5px; line-height:150%;">Login here</a>';
			} else {

				$emailBody = '<body style="margin: 0; padding: 0;">
				     <table align="center" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse;">
				          <tr>
				               <td align="center" bgcolor="#5084CC" style="padding: 14px; color: #ffffff; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold; letter-spacing:-.5px; line-height:150%;">' .
					$this->appConfig['instance']
					. '</td>
				          </tr>
				          <tr>
				               <td bgcolor="#F6F9FC" style="padding: 40px 30px 40px 30px;">
				                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
				                         <tr>
				                              <td>' . $providerHq['name'] . ' has given you access. Log in to see what they have shared with you. <br/> ' . $pwordMsg . '</td>
				                         </tr>
				                    </table>
				               </td>
				          </tr>
				          <tr>
				               <td bgcolor="#5084CC" style="padding: 30px 30px 30px 30px;">
				                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
				                         <tr>
				                         	<td>
				                         	<a href="' . $link . '" align="center" bgcolor="#5084CC" style="padding: 14px; color: #ffffff; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold; letter-spacing:-.5px; line-height:150%;">Login here</a>						                              
					                        </td>
				                         </tr>
				                    </table>
				               </td>
				          </tr>
				     </table>
				</body>';
			}

			$mergevars = [
				'TITLE' => 			$title,
				'BODY' => 			$emailBody,
				'BUTTON' => 		$viewBtnTxt,
				'BUTTON_LINK' => 	$link,
				'INSTANCE_NAME' => 	$this->appConfig['instance']

			];
			// 		error_reporting(E_ALL);
			// 		ini_set('display_errors', '1');
			// 			var_dump([$user['email']], $password);
			// 			die();
			$comm->sendMandrillEmail(
				[$user['email']],
				null,
				$title,
				$mergevars,
				null,
				$instance['id'],
				1,
				0,
				false,
				null,
				$provider,
				0,
				[$contactId, $companyId, $new['id']] // tags for the email
			);

			return [
				'msg' => 	'Login access has been granted for ' . $user['fname'] . ' ' . $user['lname'] . ', ' .
					' and an email sent to ' . $user['email'] . ' with login credentials.'
			];
		} else {

			return [
				'msg' => 'A user already exists for this contact.'
			];
		}

		return false;
	}
];
