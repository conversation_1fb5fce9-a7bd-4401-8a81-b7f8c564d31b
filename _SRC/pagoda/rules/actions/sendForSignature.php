<?php

return [
    'name' => 'sendForSignature', 'process' => function ($setup, $run) {

        $objs =     $setup['objs'];
        $obj =         $setup['old'];
        $new =         $setup['new'];
        $setup =     $setup['setup'];
        $options = $setup['options'];

        // Options
        $signers = [];
        $toNotify = [];
        $signersRef =   $options['signers'];
        $toNotifyRef =  $options['notify'];
        $docHtml =      $options['body']['html'];
        $emailTitle =   $options['emailTitle'];
        $docTitle =     $options['title'];

        // Message for the user
        $userMsg = $docTitle . ' sent out for signature to: ';

        // Check for minimum required options/args
        if (
            empty($signersRef)
            || empty($docHtml)
        ) {
            return false;
        } elseif (empty($emailTitle)) {

            $emailTitle = 'Signature requested';
        }

        $temp = false;

        // Get email data for signers
        foreach ($signersRef as $i => $signerRef) {

            $temp = $objs->getValueAtPath(
                $obj['id'],
                $signerRef
            );

            if (is_array($temp)) {

                foreach ($temp as $tempObj) {

                    array_push(
                        $signers,
                        $tempObj['id']
                    );
                }
            } else if (is_int($temp)) {

                array_push(
                    $signers,
                    $temp
                );
            }
        }

        $signerObjs = $objs->getById('', $signers, [
            'email' => true, 'fname' => true, 'lname' => true, 'contact_info' => [
                'info' =>           true, 'is_primary' =>   true, 'type' =>         true
            ]
        ]);

        // Get users to notify
        foreach ($toNotifyRef as $i => $toNotifyRefItem) {

            $temp = $objs->getValueAtPath(
                $obj['id'],
                $toNotifyRefItem
            );

            if (is_array($temp)) {

                foreach ($temp as $tempObj) {

                    array_push(
                        $toNotify,
                        $tempObj['id']
                    );
                }
            } elseif (is_int($temp)) {

                array_push(
                    $toNotify,
                    $temp
                );
            }
        }

        $mergevars = array(
            'TITLE' =>          $emailTitle,
            'SUBJECT' =>        $emailTitle,
            'BODY' =>           'Please follow the link to sign..',
            'INSTANCE_NAME' =>  'Bento'
        );

        foreach ($signerObjs as $i => $signer) {

            $emailToUse = false;
            if ($signer['object_bp_type'] === 'contacts') {

                foreach ($signer['contact_info'] as $info) {

                    if (
                        $info['is_primary'] === 'yes'
                        && $info['type']
                        && $info['type']['data_type'] === 'email'
                    ) {

                        $emailToUse = $info['info'];
                    }
                }
            } else {

                $emailToUse = $signer['email'];
            }

            if ($emailToUse) {

                // Produce a document, with its status set to 'Out For Signature'
                $contractObj = $objs->create(
                    'contracts',
                    [
                        'active' =>                 'Yes', 'after_signature' =>      'nothing', 'html_string' =>          $docHtml
                        // , 'main_contact' =>
                        , 'notify_list' =>          $toNotify, 'merge_type' =>           'proposal', 'name' =>                 $docTitle . ': ' . $signer['fname'] . ' ' . $signer['lname'], 'related_object' =>       $obj['id'], 'requires_approval' =>    'No', 'status' =>               'Out For Signature', 'tagged_with' =>          [$obj['id']]
                    ]
                );

                // Link to contract portal
                $link = '<a target="_blank" href="https://bento.infinityhospitality.net/app/contracts#?&i=' . $contractObj['instance'] . '&wid=' . $contractObj['id'] . '">CLICK HERE TO VIEW</a>';
                // $link = '<a target="_blank" href="http://10.0.0.239:8080/app/contracts#?&i='. $contractObj['instance'] .'&wid='. $contractObj['id'] .'">CLICK HERE TO VIEW</a>';

                $mergevars['BODY'] = 'Hey ' . $signer['fname'] . ',<br />Your signature has been requested in regards to ' . $obj['name'] . '. Please follow the link below to sign:<br />' . $link;

                // Email the signer
                $GLOBALS['app']->sendEmail(
                    $emailToUse,
                    '<EMAIL>',
                    $emailTitle,
                    $mergevars,
                    'Signature Request',
                    false
                );

                // Update the message
                if ($i > 0) {
                    $userMsg .= ', ';
                }
                $userMsg .= $signer['fname'] . ' ' . $signer['lname'];
            }
        }

        return [
            'msg' => $userMsg
        ];
    }
];
