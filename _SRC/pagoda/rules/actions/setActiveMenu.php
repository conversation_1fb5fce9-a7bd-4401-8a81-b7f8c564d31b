<?php

return [
	'name' 		=> 'setActiveMenu'
	, 'process'	=> function ($setup, $run) {
		
// 		error_reporting(E_ALL);
// 		ini_set('display_errors', '1');
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new'];
		$setup = 	$setup['setup'];
		
		$proposal = $objs->getById(
			'proposals'
			, $obj['proposal']
			, [
				'menu' => [
					'id' => true
				]
			]
		);
		
		$updatedMenu = $objs->update(
			'inventory_menu'
			, [
				'id' => 		$proposal['menu']['id']
				, 'active' => 	'Yes'
			]
		);
		
		$_POST->menuId = $updatedMenu['id'];
		$GLOBALS['app']->setMenuReservations(
			$updatedMenu['id']
			, 0
		);
		
		return [
			'msg' => 		'Items reserved.'
			, 'update' => 	[]
		];
		
	}
];

?>