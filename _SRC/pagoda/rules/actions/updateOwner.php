<?php

return [
	'name' => 'updateOwner',
	'process' => function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		
		if($new['main_contact'] != 0) {
			
			$main_contact = $objs->getById(
				'', 
				$new['main_contact'], 
				[]
			);
			
			$new['owner'] = $main_contact['manager'];
			
			$objs->update(
				$new['object_bp_type'],
				$new,
				1,
				null
			);
			
			return true;
			
		} else {
			
			$new['owner'] = 0;
			
			$objs->update(
				$new['object_bp_type'],
				$new,
				1,
				null
			);
			
			return false;	
			
		}
		  
	}
]
	
?>