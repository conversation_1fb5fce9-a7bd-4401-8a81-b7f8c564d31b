<?php
	
return [
	'name' => 'createDocument',
	'process' => function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];

		$mergeVars = [];
		
		$appConfig = $objs->rules->appConfig;

		// Get the document
		$template = $objs->getById('contracts', $setup['options']['document']);

		// Set the document name
		$documentName = $template['name'];
		if ($setup['options']['name']) {
			$documentName = $setup['options']['name'];
		}

		// Merge the document name
		$documentName = $objs->runSteps(
			$obj
			, [
				'merge' => [
					'obj' => 			$template['id']
					, 'parent' => 		$new['id']
					, 'template' => 	$documentName
					, 'format' => 		'plain-text'
					, 'mergeVars' => 	$mergeVars
				]
			]
			, true
		)['memo'];

		// Set tags
		$tags = $new['tagged_with'];
		array_push($tags, $new['id']);
		
		// Make an array for the document creation
		$toCreate = array(
			'name' => 			$documentName, 
			'html_string' => 	$setup['options']['body']['html'],
			'active' => 		'Yes', 
			'tagged_with' => 	$tags,
			'is_public' => 		true,
			'related_object' => $new['id'],
			'merge_type' => 	$template['merge_type']
		);

		// If the document should always merge, mark the
		// document as active and pull in the unmerged html directly from the 
		// template.
		if ($setup['options']['activeDoc'] === true) {
			
			$toCreate['active'] = 'Yes';

			if (
				!empty($template)
				&& !empty($template['html_string'])
			) {
				$toCreate['html_string'] = $template['html_string'];
			}

		}

		// Create the new contract
		$contract = $objs->create('contracts', $toCreate);

		// Get contract ID
		$contractID = $contract['id'];
		$new['_doc'] = $contractID;

		return [
			'msg' => 'Document created successfully.',
			'update' => $new
		];		
		
	}
];
	
?>