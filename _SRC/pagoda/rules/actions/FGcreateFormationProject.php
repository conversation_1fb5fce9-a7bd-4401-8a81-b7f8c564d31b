<?php

return [
	'name' 		=> 'FGcreateFormationProject'
	, 'process'	=> function ($setup, $run) {
				
		$objs = 	$setup['objs'];
		$obj = 	$setup['old'];
		$new = 	$setup['new'];
		$setup = 	$setup['setup'];
		$opts = 	$setup['options'];
		$tagsFromContext = [
			'tagged_with' => 	[]
			, 'shared_with' => 	[]
		];

		$projectType = $opts['type']; // 1023, 1023EZ, 1024, 1024A
		$templateId = 0;
		$projectName = '';

		// Core Service keys
		$taxYearKey = '_1';
		$stateOfIncKey = '_18';
		$mainContactKey = '_11';

		// Client Service keys
		$servicesBpKey = '#HDIjlE';
		$servicesYearKey = '_4';
		$servicesStateOfIncKey = '_5';

		// Supplemental key-data
		$supplementalObjType = '#kpsODp';
		$supplementalStateOfIncKey = '_1';
		$supplementalStatesKey = '_10';
		$supplementalYearKey = '_89';
		$coreServicesStatesSelectionKey = '';

		// Don't allow creation without a tax year set on the core service
		if (empty($obj[$taxYearKey])) {

			return [
				'msg' => 'A <strong>Tax Year</strong> must be set on the Core Service to generate Formation projects.'
			];

		}
		
		/// Don't allow creation without a Main Contact set on the core service
		if (
			empty($obj[$mainContactKey])
		) {

			return [
				'msg' => 'A <strong>Main Contact</strong> must be set on the Core Service to generate Formation projects.'
			];

		}

		switch ($projectType) {
			case '1023':
				$templateId = 2240046; // Production
				$projectName = $obj[$taxYearKey] .' 1023';
				$servicesBpKey = '#HDIjlE.wxdJhD';
				$coreServicesStatesSelectionKey = '_65';//replace this for the real key
				break;
			case '1023EZ':
				$templateId = 8246799; // Production
				$projectName = $obj[$taxYearKey] .' 1023 EZ';
				$servicesBpKey = '#HDIjlE.sgcFW1';
				$coreServicesStatesSelectionKey = '_65';
				break;
			case '1024':
				$templateId = 8246942; // Production
				$projectName = $obj[$taxYearKey] .' 1024';
				$servicesBpKey = '#HDIjlE.6LqxD5';
				$coreServicesStatesSelectionKey = '_65';
				break;
			case '1024A':
				$templateId = 8246950; // Production
				$projectName = $obj[$taxYearKey] .' 1024 A';
				$servicesBpKey = '#HDIjlE.TgK9yi';
				$coreServicesStatesSelectionKey = '_65';
				break;
			default:
		}
	
		$template = $objs->getById(
			'groups'
			, $templateId
		);

		$mainContact = $objs->getById(
			'contacts'
			, $obj[$mainContactKey]
			, [
				'name' => 	true
				, 'fname' => 	true
				, 'lname' => 	true
				, 'company' => true
			]
		);
		$mainContactCompany = $mainContact['company']['id'];
		
		$defaults = array();
							
		if ($template) {
			
			$defaults['name'] = $projectName;
			// Set the parent property
			$defaults['parent'] = [ 'id' => $mainContactCompany ];
			
			if ($options['passOnParent'] === true) {
				$defaults['parent'] = $obj['parent'];
			}
								
			$template['main_contact'] = $mainContact['id'];
			$template['shared_with'] = [$mainContact['id']];
			$template['status'] = 'open';

			// Check for the tag for the specific year
			$yearTag = $objs->where(
				'system_tags'
				, [
					'tag' => strval($obj[$taxYearKey])
				]
				, ''
				, [
					'tag' => true
				]
			)[0];

			// Create a tag for the year if it does not exist
			if (empty($yearTag)) {

				$yearTag = $objs->create(
					'system_tags'
					, [
						'name' => 		strval($obj[$taxYearKey])
						, 'tag' => 		strval($obj[$taxYearKey])
						, 'color' => 	'black'
					]
					, 0
					, [
						'tag' => true
					]
				);

			}

			// Pass down tags from context
			$template['tagged_with'] = array_merge($obj['tagged_with'], $template['tagged_with']);
			$template['tagged_with'] = array_unique($template['tagged_with']);
			///TEST DATA system tag 2465644
			array_push($template['tagged_with'], $mainContact['id'], $mainContactCompany, $yearTag['id'], $obj['id']);
			
			$defaults['tagged_with'] = $template['tagged_with'];

			$newProj = $objs->castFromTemplate($template, null, $defaults);

			// Create the supplemental
			$supplementalToCreate = [
				'name' => 							'01 Formation Supplemental'
				, 'parent' => 						$newProj['id']
				, 'tagged_with' => 					$newProj['tagged_with']
				, 'shared_with' => 					[$obj[$mainContactKey]]
				, $supplementalStateOfIncKey => 	$obj[$stateOfIncKey]
				, $supplementalStatesKey => 		$obj[$coreServicesStatesSelectionKey]
				, $supplementalYearKey => 			$obj[$taxYearKey]
			];

			array_push($supplementalToCreate['tagged_with'], $newProj['id']);
			array_push($supplementalToCreate['tagged_with'], $yearTag['id']);
			array_push($supplementalToCreate['tagged_with'], $obj[$mainContactKey]);

			$supplementalToCreate['tagged_with'] = array_unique(
				$supplementalToCreate['tagged_with']
			);

			$supplementalObj = $objs->create(
				$supplementalObjType
				, $supplementalToCreate
			);

/*
	
- tags FG-1636580, All Company-1763496, TEST DATA system tag 2465644
*/

			// Create the service
			$serviceTags = array_unique($newProj['tagged_with']);
			array_push($serviceTags, $newProj['id']);
			
			$serviceObj = $objs->create(
				$servicesBpKey
				, [
					'name' => 					    $projectName
					, 'parent' => 					$newProj['id']
					, 'tagged_with' => 				$serviceTags
					, 'shared_with' => 				[$obj[$mainContactKey]]
					, $servicesStateOfIncKey => 	$obj[$stateOfIncKey]
					, $servicesYearKey => 			$obj[$taxYearKey]
				]
			);

			// Create the services and intake forms
			$run(
				'FGToggleFormationRecords'
				, [
					'supplementalObj' => $supplementalObj
				]
			);

			// Return message
			return [
				'msg' => '<strong>'. $newProj['name'] .'</strong> <i>Project</i> created.'
			];
			
		}
		
		return false;
		
	}
];

?>
