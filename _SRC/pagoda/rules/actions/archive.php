<?php

return [
	'name' 		=> 'archive'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 		$setup['objs'];
		$entity = 	$setup['old'];
		
		if ($entity['is_archived']) {
			
			$objects->restore(
				$entity['object_bp_type']
				, $entity['id']
			) ;
			
		} else {
			
			$objs->delete(
				$entity['object_bp_type']
				, $entity['id']
			) ;
			
		}
		
		return [
			'msg' => $entity['name'] .' has been archived.'
			, 'event_type' => 'archive'
		];
		
	}
];

?>