<?php

return [
	'name' 		=> 'createProject'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		$tagsFromContext = [
			'tagged_with' => 	[]
			, 'shared_with' => 	[]
		];

		$template = $objs->getById(
			'groups'
			, $setup['options']['template']
		);

		$defaults = [];
		$options = $setup['options'];
		$projectName = $template['name'];
		if ($setup['options']['projectName']) {
			$projectName = $setup['options']['projectName'];
		}
		
		if (
			$obj['object_bp_type'] === 'groups'
			&& $obj['group_type'] === 'Project'
			&& $obj['main_contact']
		) {
			
			$defaults['main_contact'] = $obj['main_contact'];
			
		} else {
			
			// Find main contact from tags
			$tags = $objs->getById(
				''
				, $obj['tagged_with']
				, [
					'main_contact' => 1
				]
			);
			
			// Try to use main_contact from a related project
			if (is_array($tags)) {
				foreach ($tags as $tag) {
					
					if ($tag['main_contact']) {
						
						$defaults['main_contact'] = $tag['main_contact']['id'];
						
					}
					
				}
			}

			// If there's a contact in tags, use that as the main contact
			if (is_array($tags)) {
				foreach ($tags as $tag) {
					
					if ($tag['object_bp_type'] === 'contacts') {
						
						$defaults['main_contact'] = $tag['id'];
						array_push(
							$tagsFromContext['shared_with']
							, $tag['id']
						);
						
					} else {

						array_push(
							$tagsFromContext['tagged_with']
							, $tag['id']
						);

					}
					
				}
			}

			// If there's a contact field, use that for the main_contact of the project.
			// If there's a teammate field, use that for the manager of the project.
			$bp = $objs->getBlueprint($obj['object_bp_type']);
			if (is_array($bp)) {
				foreach ($bp as $key => $field) {

					switch ($field['fieldType']) {

						case 'contacts':
							if (is_int($obj[$key]) && $obj[$key] > 0) {
								$defaults['main_contact'] = $obj[$key];
								
								// Share with the contact
								array_push($template['shared_with'], $obj[$key]);
	
								// Tag with the company for that contact
								$contact = $objs->getById(
									'contacts'
									, $obj[$key]
									, [
										'company' => 'id'
									]
								);
	
								if ($contact && $contact['company']) {
									array_push($template['tagged_with'], $contact['company']);
								}
							
							}
							break;
						
						case 'users':
						case 'user':
							if (is_int($obj[$key]) && $obj[$key] > 0) {
								
								// Set the manager
								if (empty($defaults['managers'])) {
									$defaults['managers'] = [];
								}
								array_push($defaults['managers'], $obj[$key]);

								// Share with the contact
								array_push($template['tagged_with'], $obj[$key]);
								
							}
							break;

					}

				}
			}

		}
		
		if ($obj['object_bp_type'] === 'contacts') {
			
			$defaults['main_contact'] = $obj;
			$mainContact = $obj;
			
		} elseif ($defaults['main_contact']) {
			
			$mainContact = $objs->getById(
				'contacts'
				, $obj['main_contact']
				, [
					'name' => 		true
					, 'fname' => 	true
					, 'lname' => 	true
				]
			);
			
		}
		
		if (!empty($mainContact) && !empty($mainContact['name'])) {
			$projectName .= ' for '. $mainContact['name'];
		}
		
		// Setting project dates
		if(!empty($obj['start_date'])) {

			$templateDateCreated = (new DateTime($template['date_created']))->format('U');
			$projectStartDate = (new DateTime($obj['start_date']))->format('U');
			$projectEndDate = (new DateTime($obj['end_date']))->format('U');
			
			$dateDiff = intval($projectStartDate) - intval($templateDateCreated);
			$startEndDateDiff = intval($projectEndDate) - intval($projectStartDate);
			
			$newProjectStartDate = (new DateTime())->format('U') + intval($dateDiff);
			$newProjectEndDate = intval($newProjectStartDate) + intval($startEndDateDiff);
			
			$defaults['start_date'] = date('Y-M-d', $newProjectStartDate);
			$defaults['end_date'] = date('Y-M-d', $newProjectEndDate);	
			
		} 

		if ($template) {
			
			$template['name'] = $projectName;

			if (strpos($template['name']) !== false) {

				$mergedTitle = $objs->runSteps(
					$obj
					, [
						'merge' => [
							'obj' => 		$obj['id']
							, 'parent' => 	$obj['id']
							, 'template' => $template['name']
							, 'format' => 	'plain-text'
						]
					]
					, true
				)['memo'];

				if (!empty($mergedTitle)) {
					$template['name'] = $mergedTitle;
				}

			}

			// Set the parent property
			$template['parent'] = [ 'id' => $obj['id'] ];
			if ($options['passOnParent'] === true) {
				$template['parent'] = $obj['parent'];
				$defaults['parent'] = $obj['parent'];
			}

			// Pass down tags from context
			$template['tagged_with'] = array_merge(
				$template['tagged_with']
				, $tagsFromContext['tagged_with']
			);
			$template['shared_with'] = array_merge(
				$template['shared_with']
				, $tagsFromContext['shared_with']
			);
			$template['tagged_with'] = array_unique($template['tagged_with']);
			$template['shared_with'] = array_unique($template['shared_with']);

			array_push($template['tagged_with'], $obj['id']);
			$newProj = $objs->castFromTemplate($template, null, $defaults);
			
			// return message
			return [
				'msg' => '<strong>'. $newProj['name'] .'</strong> <i>Project</i> created.'
			];
			
		}
		
		return false;
		
	}
];

?>