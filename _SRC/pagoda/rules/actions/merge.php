<?php

$GLOBALS['MERGE_VARS'] = [];

$tagsDir = APP_ROOT .'views';
$Views = [];
$tmp = null;

// Collect Views
$dir = new DirectoryIterator($tagsDir);

foreach ($dir as $fileinfo) {
    
    if (
        !$fileinfo->isDot()
        && $fileinfo->getExtension() === 'php'
    ) {

        $tmp = require_once(
            $tagsDir .'/'. $fileinfo->getFilename()
        );

        $Views[strtolower($tmp['name'])] = [
            'getHtml' =>            $tmp['getHtml']
            , 'getPlainText' =>     $tmp['getPlainText']
            , 'getValueText' =>     $tmp['getValueText']
        ];
        
    }

}

function getFieldFromName ($type, $fieldTitle) {

    // Hardcoded fields
    switch ($fieldTitle) {

        case 'Tagged With':
            return [
                'key' =>    'tagged_with'
                , 'field' => [
                    'type' => 'tags'
                ]
            ];
            break;
        
        case 'Created On':
        case 'Date Created':
            return [
                'key' =>    'date_created'
                , 'field' => [
                    'type' => 'date'
                    , 'fieldType' => 'date'
                ]
            ];
            break;

    }

    // Fields on blueprints
    foreach ($type['blueprint'] as $key => $field) {
        
        if ($field['name'] === $fieldTitle && !$field['is_archived']) {

            return [
                'key' =>        $key
                , 'field' =>    $field
            ];

        }

    }

    // When nothing can be found
    return [
        'key' =>        $fieldTitle
        , 'field' =>    false
    ];

}

function getWorkflowStateFromStateName ($type, $stateName, $fieldKey, $objs) {

    $currentState = [
        'name' => 'Not set'
    ];
    $workflow = false;
    
    // Get the workflow to use
    if (
        $type && $type['blueprint'] 
        && $type['blueprint'][$fieldKey] 
        && $type['blueprint'][$fieldKey]['fieldType'] === 'state'
        && $type['blueprint'][$fieldKey]['workflow'] > 0
    ) {

        $workflow = $objs->getById(
            'entity_workflow'
            , $type['blueprint'][$fieldKey]['workflow']
        );

    }

    // Find the status on the workflow
    if (
        $workflow
        && is_array($workflow['states'])
    ) {
        foreach ($workflow['states'] as $state) {

            if ($state['name'] === $stateName) {
                $currentState = $state;
            }

        }
    }

    return $currentState;

}

function parseOpts ($input = [], $select = [], $objs, $entityType = [], $setup, $context, $steppedContext) {
    
    if ($select === null) {
        $select = [];
    }

    $opts = [];
    $tagType = $input[0];

    // Parse out the additional arguments
    foreach ($input as $i => $arg) {

        if (
            (
                $tagType === 'Table' 
                || $tagType === 'List'
                || $tagType === 'Board'
                || $tagType === 'Calendar'
                || $tagType === 'this'
            )
            && $i === 1
        ) {

            if ($input[1]) {

                switch ($tagType) {
            
                    case 'this':

                        // Parse the options as a field address
                        $referenceBlueprint = $entityType;
                        $steps = [];

                        // The context for the field, updated as this steps
                        // across pointers.
                        $steppedContext =       $context;       //!TODO: Set initial val
                        $referenceBlueprint =   $entityType;   //!TODO: Set initial val

                        foreach (array_slice($input, 1) as $j => $step) {

                            // Get args for nested collection tags and move on 
                            // out of this loop.
                            if (
                                $step === 'Table'
                                || $step === 'List'
                            ) {

                                // Get options/select obj for the nested collection
                                $childOpts = parseOpts(
                                    array_slice($input, $j + 1)
                                    , $select[$steps[$j - 1]]
                                    , $objs
                                    , $entityType
                                    , $setup
                                    , $context
                                    , $steppedContext
                                );
                                $select[$steps[$j - 1]] = $childOpts[1];
                                $opts = $opts + $childOpts[0];
                                $opts['view'] = $step;
                                
                                break;

                            }

                            switch ($step) {

                                case 'Parent':
                                    if ($select['parent'] == null) {
                                        $select['parent'] = [
                                            'name' => true
                                            , 'fname' => true
                                            , 'lname' => true
                                        ];
                                    }

                                    // Set the parent property in the property address
                                    array_push(
                                        $steps
                                        , 'parent'
                                    );
                                    
                                    // Move to the parent's blueprint
                                    $steppedContext = $objs->getById(
                                        ''
                                        , $setup['parent']
                                    );

                                    if ($steppedContext['object_bp_type'][0] === '#') {

                                        $referenceBlueprint = $objs->getBlueprint(
                                            $steppedContext['object_bp_type']
                                            , false
                                            , true
                                        );

                                    } else {

                                        $referenceBlueprint = [
                                            'blueprint' => $objs->getBlueprint($steppedContext['object_bp_type']
                                        )];

                                        // Add the name property for users/contacts
                                        if ($referenceBlueprint['name'] === null) {

                                            $referenceBlueprint['blueprint']['name'] = [
                                                "immutable" => false
                                                , "name" => "Name"
                                                , "type" => "string"
                                            ];

                                        }

                                        // Align the groups workflows w/workflows pointed to 
                                        // on state fields on sets.
                                        if (
                                            $steppedContext['object_bp_type'] === 'groups'
                                            && $steppedContext['type']
                                        ) {
                                            $referenceBlueprint['blueprint']['state']['workflow'] = $objs->getById(
                                                ''
                                                , $steppedContext['type']
                                                , ['states' => true]
                                            );
                                        }

                                    }
                                    
                                break;

                                default:
                                    // Match to a field name on the set's blueprint
                                    $fieldFound = false;

                                    if ( is_array($referenceBlueprint['blueprint']) ) {
                                        foreach ($referenceBlueprint['blueprint'] as $key => $field) {
    
                                            if ($field['name'] === $step && !$field['is_archived']) {
                                                
                                                // When stepping across pointers, update the blueprint
                                                // and obj-context used here.
                                                if (
                                                    $field['type'] === 'objectId'
                                                    && isset($field['options'])
                                                    && isset($field['options']['objectType'])
                                                ) {
    
                                                    // Move to the pointer's value andn blueprint
                                                    $steppedContext = $objs->getById(
                                                        ''
                                                        , $steppedContext[$key]
                                                    );
                                                    if ($steppedContext['object_bp_type'][0] === '#') {
    
                                                        $referenceBlueprint = $objs->where(
                                                            'entity_type'
                                                            , [
                                                                'bp_name' => substr($steppedContext['object_bp_type'], 1)
                                                            ]
                                                        )[0];
    
                                                    } else {
    
                                                        $referenceBlueprint = [
                                                            'blueprint' => $objs->getBlueprint($steppedContext['object_bp_type']
                                                        )];
    
                                                    }
    
                                                }
    
                                                $fieldFound = true;
    
                                                array_push($steps, $key);
                                                $opts['fieldDef'] = $field;
    
                                            }
                                            
                                        }
    
                                    }
                                    if (!$fieldFound) {
                                        array_push($steps, $step);
                                    }

                                break;

                            }

                        }

                        // Set the selection obj for the data query
                        $fieldSelect = $objs->getSelectionObjFromAddressString(
                            array_slice($steps, 1)
                        );
                        if (is_array($select[$steps[0]]) && is_array($fieldSelect)) {
                            
                            foreach ($fieldSelect as $key => $sel) {

                                // Consolidate selection objs
                                if (empty($select[$steps[0]][$key])) {
                                    $select[$steps[0]][$key] = $sel;
                                } elseif (
                                    is_array($select[$steps[0]][$key])
                                    && is_array($sel)
                                ) {

                                    $select[$steps[0]][$key] = array_merge_recursive_distinct(
                                        $select[$steps[0]][$key]
                                        , $sel
                                    );

                                }

                            }

                        } else {

                            $select[$steps[0]] = $fieldSelect;

                        }

                        $opts['field'] = $steps;
                        return [$opts, $select];
                        break;
        
                    case 'Table':
                    case 'List':
                        // Get the set name
                        $opts['set'] = $input[1];

                        switch ($opts['set']) {

                            case 'Action Items':
                                if (
                                    $entityType['is_space']
                                    || $steppedContext['object_bp_type'] === 'users'
                                    || $steppedContext['object_bp_type'] === 'groups'
                                    || $steppedContext['object_bp_type'] === 'contacts'
                                    || $steppedContext['object_bp_type'] === 'companies'
                                ) {
                                    $opts['set'] = '##'. $opts['set'];
                                } else {
                                    $opts['set'] = '#'. $opts['set'];
                                }
                                $type = $objs->getBlueprint('#Action Items');
                                break;

                            // For custom sets
                            default:
                                $type = $objs->where(
                                    'entity_type'
                                    , [
                                        'name' => $opts['set']
                                    ]
                                )[0];

                                // For subsets, make sure to get blueprint with 
                                // inherited fields.
                                if ($type['_class'] > 0) {

                                    $type['blueprint'] = $objs->getBlueprint(
                                        '#'. $type['bp_name']
                                        , false
                                    );

                                }

                                if (
                                    $entityType['is_space']
                                    || $steppedContext['object_bp_type'] === 'users'
                                    || $steppedContext['object_bp_type'] === 'groups'
                                    || $steppedContext['object_bp_type'] === 'contacts'
                                    || $steppedContext['object_bp_type'] === 'companies'
                                ) {
                                    $opts['set'] = '##'. $type['bp_name'];
                                } else {
                                    $opts['set'] = '#'. $type['bp_name'];
                                }
                                $opts['entity_type'] = $type;
                                break;

                        }

                        // Update the selection object
                        $select[$opts['set']] = [
                            'name' => true
                            , 'status' => true
                            , 'parent' => [
                                'name' => true
                            ]
                            , 'created_by' => [
                                'fname' =>      true
                                , 'lname' =>    true
                                , 'name' =>     true
                            ]
                            , 'tagged_with' =>  true
                        ];

                        break;
        
                }
        
            }

        } else {

            $split = explode(':', $arg);
            $argKey = $split[0];

            if (count($split) > 1) {

                $opts[$argKey] = $split[1];
                switch ($argKey) {

                    case 'Context':
                        if ($split[1] === 'Any') {
                            $select[$opts['set']]['_context'] = 'Any';
                        }
                        break;

                    case 'Date':
                        // If the arg value matches a date field on the context obj, 
                        // then set the value to the field's key and add the field to
                        // the selection array.
                        $dateField = getFieldFromName($entityType, $split[1]);
                        if (
                            !empty($dateField)
                            && !empty($dateField['field'])
                            && $dateField['field']['fieldType'] === 'date'
                        ) {
                            
                            $opts[$argKey] = $dateField['key'];
                            $select[$dateField['key']] = true;

                        }
                        break;
                    
                    // Filter by overall 'Done' status
                    case 'Done':
                        if (empty($select[$opts['set']['where']])) {
                            $select[$opts['set']]['where'] = [];
                        }
                        if ($opts[$argKey] === 'true') {

                            $select[$opts['set']]['where']['status'] = 'done';
                        
                        } elseif ($opts[$argKey] === 'false') {

                            $select[$opts['set']]['where']['status'] = [
                                'type' =>       'not_equal'
                                , 'value' =>    'done'
                            ];

                        }
                        break;

                    case 'Nest':
                        //!WORKING HERE::: NEED TO ADD THE SELECTION FOR _MERGE VALS FOR DATE/USER FIELDS
                        // Translate field names to keys
                        //!TODO: Consolidate w/'Show' case in iteration below
                        // to a general field merge func
                        foreach ($type['blueprint'] as $key => $field) {
                            
                            if ($field['name'] === $opts[$argKey] && !$field['is_archived']) {

                                $opts[$argKey] = $key;
                                //!TODO: 
                                $select[$opts['set']][$key] = $select[$opts['set']];

                                // Get at least all vals, in case this is used for creates
                                if (
                                    is_array($select[$opts['set']][$key])
                                    && $type['blueprint'][$key]
                                    && $type['blueprint'][$key]['options']
                                    && $type['blueprint'][$key]['options']['objectType']
                                ) {

                                    $nestedBp = $objs->getBlueprint(
                                        $type['blueprint'][$key]['options']['objectType']
                                        , false
                                    );
                                    if (is_array($nestedBp)) {
                                        foreach ($nestedBp as $nestedBpFieldKey => $nestedBpField) {
                                            if (empty($select[$opts['set']][$key][$nestedBpFieldKey])) {
                                                $select[$opts['set']][$key][$nestedBpFieldKey] = true;
                                            }

                                        }
                                    }

                                }
                                // If pointing to the same obj type, nest another level
                                if (
                                    $field
                                    && $field['options']
                                    && $field['options']['objectType'] === substr($opts['set'], 1)
                                ) {
                                    $select[$opts['set']][$key][$key] = $select[$opts['set']];
                                }

                            }
                        }
                        break;

                    case 'CreatedOn':
                        switch ($opts[$argKey]) {

                            case 'today':

                                // Get start time, localized to the client
                                $start = strtotime('today');
                                if ($_COOKIE && $_COOKIE['tz_off']) {

                                    $start = $start - (intval($_COOKIE['tz_off'])*60);

                                }

                                $select[$opts['set']]['where'] = [
                                        'is_template' => 0
                                        , 'date_created' => [
                                            'type' => 'after'
                                            , 'date' =>    time() - (24 * 60 * 60)
                                            // , 'end' =>      time()
                                        ]
                                ];
                                break;

                        }
                        break;

                    case 'Where':
                        $filters = explode(', ', $split[1]);
                        if (empty($select[$opts['set']]['where'])) {
                            $select[$opts['set']]['where'] = [];
                        }
                        foreach ($filters as $i => $filter) {

                            $key = explode('=', $filter)[0];
                            $val = explode('=', $filter)[1];

                            $fieldOnChildSet = getFieldFromName($type, $key);
                            $key = $fieldOnChildSet['key'];

                            switch ($val) {

                                case 'Me':
                                    switch ($fieldOnChildSet['field']['type']) {

                                        case 'objectIds':
                                            $val = [
                                                'type' =>   'contains'
                                                , 'value' => intval($_COOKIE['uid'])
                                            ];
                                            break;

                                        default:
                                            $val = intval($_COOKIE['uid']);
                                            break;

                                    }
                                    break;

                                    case 'Parent':
                                        $val = $context['parent'];
                                        break;

                                case 'Parent Team Members':
                                    $parentObj = $objs->getById(
                                        ''
                                        , $context['parent']
                                        , [ 'tagged_with' => true ]
                                    );
                                    $teamMembers = $objs->where(
                                        'users'
                                        , [
                                            'id' => [
                                                'type' => 'or'
                                                , 'values' => $parentObj['tagged_with']
                                            ]
                                        ]
                                    );
                                    $compareType = 'or';
                                    if ($key === 'tagged_with') {
                                        $compareType = 'any';
                                    }
                                    $val = [
                                        'type' =>       $compareType
                                        , 'values' =>   __::pluck($teamMembers, 'id')
                                    ];
                                    break;

                                case 'Team Members':
                                    $teamMembers = $objs->where(
                                        'users'
                                        , [
                                            'id' => [
                                                'type' => 'or'
                                                , 'values' => $context['tagged_with']
                                            ]
                                        ]
                                    );
                                    $compareType = 'or';
                                    if ($key === 'tagged_with') {
                                        $compareType = 'any';
                                    }
                                    $val = [
                                        'type' =>       $compareType
                                        , 'values' =>   __::pluck($teamMembers, 'id')
                                    ];
                                    break;
                                
                                default:
                                    
                                    // Special per-field-type parsing per field type on the keys
                                    if (
                                        !empty($fieldOnChildSet) 
                                        && $fieldOnChildSet['field'] 
                                        & $fieldOnChildSet['field']['fieldType'] === 'state'
                                    ) {
                                        
                                        $specifiedState = getWorkflowStateFromStateName(
                                            $type
                                            , $val
                                            , $key
                                            , $objs
                                        );

                                        if ($specifiedState['isEntryPoint']) {
                                            $val = [
                                                'type' => 'not_set'
                                                , 'or' => $specifiedState['uid']
                                            ];
                                        }

                                        $val = $specifiedState['uid'];

                                    } else {

                                        $referencedField = getFieldFromName($entityType, $val);
                                        $val = $referencedField['key'];
                                        if ($referencedField['field']) {
                                            
                                            switch ($referencedField['field']['type']) {
    
                                                case 'date':
                                                    $val = [
                                                        'type' =>   'on_same_day'
                                                        , 'date' => $context[$val]
                                                    ];
                                                    break;
    
                                                default:
                                                    $val = $context[$val];
                                                    break;
    
                                            }
    
                                        }

                                    }
                                    break;

                            }

                            $select[$opts['set']]['where'][$key] = $val;

                        }
                        break;

                }

            } else {

                $opts[0] = $split[0];

            }

            if (strpos($split[1], ', ')) {
                
                $splitVal = explode(', ', $split[1]);

            } elseif ($argKey === 'Show') {

                $splitVal = [$split[1]];

            }
            
            if ( 
                is_array($splitVal) 
                && ( count($splitVal) > 1 || $argKey === 'Show') 
            ) {
                
                $opts[$split[0]] = [];

                foreach ($splitVal as $i => $val) {
                    
                    switch ($argKey) {

                        case 'Show':
                            // Translate field names to keys
                            foreach ($type['blueprint'] as $key => $field) {

                                if ($field['name'] === $val && !$field['is_archived']) {

                                    $opts[$split[0]][$key] = $field;

                                    $select[$opts['set']][$key] = true;
                                    switch ($field['fieldType']) {

                                        case 'attachment':
                                            $select[$opts['set']][$key] = [
                                                'name' =>				true
                                                , 'document_type' => 	true
                                                , 'file_upload' => 		true
                                                , 'share_link' => 		true
                                            ];
                                            break;

                                        case 'user':
                                        case 'users':
                                            $select[$opts['set']][$key] = [
                                                'id' => 	    	    true
                                                , 'fname' => 	        true
                                                , 'lname' => 	        true
                                                , 'profile_image' =>    true
                                                , 'color' => 	        true
                                                , 'name' => 	        true
                                            ];
                                            break;

                                    }

                                }
                            }
                            break;

                        default:
                            array_push(
                                $opts[$split[0]]
                                , $val
                            );
                            break;

                    }

                }
            }

        }

    }

    return [$opts, $select];

}

function array_merge_recursive_distinct ( array &$array1, array &$array2 )
{
  $merged = $array1;

  foreach ( $array2 as $key => &$value )
  {
    if ( is_array ( $value ) && isset ( $merged [$key] ) && is_array ( $merged [$key] ) )
    {
      $merged [$key] = array_merge_recursive_distinct ( $merged [$key], $value );
    }
    else
    {
      $merged [$key] = $value;
    }
  }

  return $merged;
}

function replaceMergeTagsWithData($template, $mergeTags, $mergeFormat, $objs, $run, $Views, $context, $mergeTagOptions) {

    // For each match, replace the tag with the view it collects
    foreach ($mergeTags as $i => $mergeTag) {

        $tagOptions = $mergeTagOptions[$i];

        foreach ($mergeTag[0] as $i => $tag) {

            if (str_contains($template, $tag)) {

                $split = explode('.', $mergeTag[1][$i]);
                $tagType = $split[0] ? $split[0] : $mergeTag[1][$i];
                $tagOpts = $mergeTagOptions[$i][0];

                // Pass in merge vars from request
                if (is_array($GLOBALS['MERGE_VARS'])) {
                    $tagOpts['mergeVars'] = $GLOBALS['MERGE_VARS'];
                }

                // Generate html and merge it in, being sure to leave tags 
                // that have not yet been transitioned to the back-end.
                $replaceTxt = '';
                
                if ($Views[strtolower($tagType)]) {

                    switch ($mergeFormat) {

                        case 'plain-text':
                            $replaceTxt = $Views[strtolower($tagType)]['getPlainText'](
                                [
                                    'objs' =>   $objs
                                    , 'run' =>  $run
                                    , 'views' => $Views
                                ]
                                , $context
                                , $tagOpts
                            );
                            break;

                        case 'value-text':
                            $replaceTxt = $Views[strtolower($tagType)]['getValueText'](
                                [
                                    'objs' =>   $objs
                                    , 'run' =>  $run
                                    , 'views' => $Views
                                ]
                                , $context
                                , $tagOpts
                            );
                            break;

                        default:
                            $replaceTxt = $Views[strtolower($tagType)]['getHtml'](
                                [
                                    'objs' =>   $objs
                                    , 'run' =>  $run
                                    , 'views' => $Views
                                ]
                                , $context
                                , $tagOpts
                            );
                            break;

                    }

                    $template = str_replace($tag, $replaceTxt, $template);

                }

            }

        }

    }
    
    return $template;

}

return [
    'name' =>       'merge'
    , 'process' =>  function ($setup, $run) use ($Views) {

        // error_reporting(E_ERROR);
        // ini_set('display_errors', '1');

        //!TODO: List/Table views should pull directly under current context obj, instead
        // of the parent.

        // Store merge vars from request globally, so they can be passed down
        // through the merge stack.
        if (
            is_array($setup['setup']['mergeVars']) 
            && !empty($setup['setup']['mergeVars'])
        ) {
            $GLOBALS['MERGE_VARS'] = $setup['setup']['mergeVars'];
        }

        $objs = $setup['objs'];
        $context = $setup['new'];

        // If no context is set, set context to the current user
        if (empty($context)) {
            $context = $objs->getById(
                'users'
                , intval($_COOKIE['uid'])
            );
        }

        $entityType = $objs->where('entity_type', [
            'bp_name' => substr($context['object_bp_type'], 1)
        ])[0];

        $blueprint = $objs->getBlueprint($context['object_bp_type']);

        if (!$setup['setup']['parent'] && $context['parent']) {
            $setup['setup']['parent'] = $context['parent'];
        }

        $template = $setup['setup']['template'];
        $templates = $setup['setup']['templates'];

        if (empty($templates)) {

            $mergeFormat = 'html';
            if ($setup['setup'] && $setup['setup']['format']) {
                $mergeFormat = $setup['setup']['format'];
            }

            $templates = array();
            $templates[0] = array(
                'template' => $template,
                'format' => $mergeFormat
            );

        }

        $mergeTags = [];
        $mergeTagOptions = [];
        $select = [
            'name' => true
        ];

        foreach ($templates as $key => $template) {

            // Initialize array
            $tags = [];
            $tagOptions = [];
            $mergeTags[$key] = [];
            $mergeTagOptions[$key] = [];

            // Find all the matches
            preg_match_all("/{{(.*?)}}/", $template['template'], $tags);

            // Push to main array
            if (!empty($tags)) {
                array_push($mergeTags[$key], $tags);
            }

            foreach ($tags[0] as $i => $tag) {

                $split = explode('.', $tags[1][$i]);
                $tagType = $split[0];

                $tmp = parseOpts($split, $select, $objs, $entityType, $setup['setup'], $context, $context);

                // Don't try to pull in data for List/Table tags here - let that happen within the
                // merge tag func itself
                switch ($tagType) {

                    case 'Table':
                    case 'List':
                        break;
                    
                    default:
                        $select = array_merge_recursive_distinct(
                            $select
                            , $tmp[1]
                        );
                        break;

                }

                $tmp[0]['select'] = array_merge_recursive_distinct(
                    $select
                    , $tmp[1]
                );

                $tagOptions[$i] = $tmp;

                // Push to main array
                array_push($mergeTagOptions[$key], $tagOptions[$i]);

            }

            foreach ($blueprint as $key => $field) {

                if ($select[$key]) {

                    if (!empty($field['select']) && is_array($field['select'])) {

                        foreach ($field['select'] as $selectKey => $selectField) {
                            $select[$key][$selectKey] = $selectField;
                        }
                        
                    }

                }

            }

        }

        if (!empty($context)) {
            
            $context = $objs->getById(
                $context['object_bp_type']
                , $context['id']
                , $select
            );
        
        }

        // On creates, where no obj yet exists
        if ($setup['setup']['parent'] > 0) {

            $context['parent'] = $objs->getById(
                $context['object_bp_type']
                , $setup['setup']['parent']
                , $select['parent']
            );

        }

        foreach($templates as $key => $template) { 

            $templates[$key] = replaceMergeTagsWithData($template['template'], $mergeTags[$key], $template['format'], $objs, $run, $Views, $context, $mergeTagOptions[$key]);

        }

        if (!empty($setup['setup']['templates'])) {

            return [
                'memo' => $templates
            ];

        } else {

            return [
                'memo' => $templates[0]
            ];

        }

    }
];

?>