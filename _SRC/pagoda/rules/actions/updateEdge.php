<?php

function getUpdatedParent($objs, $parentObj, $fieldName) {

	$blueprint = $objs->getBlueprint($parentObj['object_bp_type'], false, true, true);
	$select = $objs->getSelectionFromBlueprint($blueprint['blueprint'], true);

	$select['selectionObj'] = true;
	$select['name'] = true;
	$select['is_template'] = true;

	$updatedParent = $objs->update(
		$parentObj['object_bp_type'],
		$parentObj,
		$select,
		null
	);

	return $updatedParent;

}

return [
	'name' 		=> 'updateEdge'
	, 'process' => function ($setup, $run) {
		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');
		$objs 		= $setup['objs'];
		$obj 		= $setup['old'];
		$new 		= $setup['new'];
		$setup 		= $setup['setup'];

		$entity 	= $setup['obj'];
		$create 	= true;
		$remove 	= false;
		$unlink 	= false;
		$multi 		= false;
		$isTemplate = false;
		$fieldName 	= $setup['fieldName'];
		$userId 	= 0;

		$setBp = $objs->getBlueprint($obj['object_bp_type'], false);
		$fieldDef = $setBp[$fieldName];
		$passDownTags = true;
		$additionalTags = [];
		$template = false;
		if (
			is_array($fieldDef)
			&& is_array($fieldDef['options'])
		) {

			if ($fieldDef['options']['inheritsTags'] === false) {
				$passDownTags = false;
			}
			if (is_array($fieldDef['options']['additionalTags'])) {
				$additionalTags = $fieldDef['options']['additionalTags'];
			}
			
		}

		if ( 
			array_key_exists('id', $entity) 
			&& $entity['id'] !== 0
		) {
			
			$create = false;

            if (array_key_exists('isTemplate', $entity) && $entity['isTemplate'] == 1) {
                $create = true;
            }

			$entity = $objs->getById(
				''
				, $entity['id']
				, [
                    'name'=>            true
                    , 'is_template' =>  true
                ]
			);
		
		}
		
		// When creating from template, the obj comes in with just
		// the id
		if (
			is_int($setup['template'])
			&& is_int($entity)
		) {

			$parentObj = $objs->getById(
				''
				, $entity
				, [$fieldName => 'id']
			);

		} else {
			
			$parentId 	= $entity['parent'];
			$parentObj = $objs->getById(
				''
				, $parentId
				, [$fieldName => 'id']
			);

		}

		// ** Checking setup object **

		if (array_key_exists('parent', $setup)) {

			$parentId 	= $setup['parent'];

			$parentObj = $objs->getById(
				''
				, $parentId
				, [$fieldName => 'id']
			);

		}

		if (array_key_exists('multi', $setup)) {
			
			$multi = $setup['multi'];
			
		}
		
		if (array_key_exists('remove', $setup)) {
			
			$remove = $setup['remove'];
			
		}
		
		if (array_key_exists('unlink', $setup)) {
			
			$unlink = $setup['unlink'];
			
		}

		if (array_key_exists('userId', $setup)) {

			$userId = $setup['userId'];
            array_push($additionalTags, $userId);

		}

		if (array_key_exists('pointsToTemplate', $setup)) {

			$isTemplate = $setup['pointsToTemplate'];

		}

		// *****************************************

		if (
			$remove
			|| $unlink
		) {

			if ($multi) {

				$pointerList = $parentObj[$fieldName];

				$pointerList = __::filter($pointerList, function($id) use($entity) {
					return $id !== $entity['id'];
				});	

				$parentObj[$fieldName] = $pointerList;
				
				if ( $remove ) {
					
					$objs->delete('', $entity['id']);	
					
				} 	
				
			} else {
				
				if ( $remove ) {
					
					$objs->delete('', $entity['id']);	
					
				}
				
				$parentObj[$fieldName] = 0;	
				
			}

			unset($parentObj['tagged_with']);

			$updatedParent = getUpdatedParent($objs, $parentObj, $fieldName);
			
			$diff = array();
			
			$diff['id'] = $updatedParent['id'];
			$diff[$fieldName] = $updatedParent[$fieldName];
			
			return [
				'msg' => ''
				, 'update' => $diff
			];
			
		}

		if ( !$create ) { // Update

			if ($multi) {

				$pointerList = array();
				
				if ( !empty($parentObj[$fieldName]) ) {
					
					$pointerList = $parentObj[$fieldName];	
					
				}

				array_push($pointerList, $entity['id']);

				$parentObj[$fieldName] = $pointerList;

			} else {

				$parentObj[$fieldName] = $entity['id'];

			}

			unset($parentObj['tagged_with']);

			$updatedParent = getUpdatedParent($objs, $parentObj, $fieldName);
			
			$diff = array();
			$diff[$fieldName] = $updatedParent[$fieldName];

			return [
				'msg' => ''
				, 'update' => $diff
			];
			
		} else {


			if ($entity['tagged_with'] == null) {
				
				$entity['tagged_with'] = array();
				
			}

			if ($userId !== 0) {

				array_push($entity['tagged_with'], $userId);

			}

			// If a template is specified, create the new item from 
			// the template, otherwise just create	
			if ($isTemplate) {
				$entity['is_template'] = 1;
			}

			if ($passDownTags === false) {
				$entity['tagged_with'] = [];
			}

			if (!empty($setup['template'])) {
				$template = $setup['template'];
			}

			$resp = $run('createEntity', [
				'objectType' => 	$entity['object_bp_type']
				, 'old' => 			$obj 
				, 'template' => 	$template
				, 'seed' => 		$entity
				, 'options' => [
					'passOnTags' => 		$passDownTags
					, 'additionalTags' => 	$additionalTags
				]
			]);
			$newEntity = $resp['memo'][0];

			if ($multi) {
				
				$pointerList = array();
				
				if ( !empty($parentObj[$fieldName]) ) {
					
					$pointerList = $parentObj[$fieldName];	
					
				}

				array_push($pointerList, $newEntity['id']);

				$parentObj[$fieldName] = $pointerList;
				
			} else {
				
				$parentObj[$fieldName] = $newEntity['id'];
				
			}

			unset($parentObj['tagged_with']);
			
			$updatedParent = getUpdatedParent($objs, $parentObj, $fieldName);

			$diff = array();
			$diff[$fieldName] = $updatedParent[$fieldName];

			return [
				'msg' => ''
				, 'update' => $diff
			];
			
		}
		
	}
];
	
?>