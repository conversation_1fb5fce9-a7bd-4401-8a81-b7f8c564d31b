<?php

return [
	'name' 		=> 'toggleActiveGroup'
	, 'process'	=> function ($setup, $run) {
		
// 		error_reporting(E_ALL);
// 		ini_set('display_errors', '1');
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new']; // Project
		$setup = 	$setup['setup'];
		
		$proposal = $objs->getById(
			'proposals'
			, $obj['proposal']
			, []
		);
		
		if($obj['is_active'] == 'Yes') {
			
			$obj['is_active'] == 'No';
			
		} else {
			
			$obj['is_active'] == 'Yes';
			
		}
		
		$schedule = $objs->where('groups'
				, [
					'group_type' => 'Schedule'
					, 'parent' => $proposal['id'] 
				]	
				, ''
				, 0
				, false
				, 0
				, 'null'
				, 'asc'
				, 100
				, null
				, []
				, 'string'
			);
			
		$shifts = $objs->where('groups'
				, [
					'group_type' => 'Shift'
					, 'parent' => $schedule['id'] 
				]
				, ''
				, 0
				, false
				, 0
				, 'null'
				, 'asc'
				, 100
				, null
				, []
				, 'string'
			);
		
		if($obj['is_active'] == 'No') {
			
			$schedule['is_active'] = 'No';
			
		} else if($obj['is_active'] == 'Yes') {
			
			$schedule['is_active'] = 'Yes';
			
		}
		
		foreach($shifts as $key => $shift) {
			
			if($schedule['is_active'] == 'No') {
				
				$shift['is_active'] == 'No';
				
			} else if($schedule['is_active'] == 'Yes') {
				
				$shift['is_active'] == 'Yes';
				
			}
			
		}
		
		$objs->update('groups', $obj, 0, null);
		$objs->update('groups', $schedule, 0, null);
		$objs->update('groups', $shifts, 0, null);
		
		return [
			'msg' => 		'Groups updated'
		];
		
	}
];

?>