<?php

return [
	'name' 		=> 'setTags'
// 	, 'process'	=> function ($objs, $old, $new, $setup, $run) {
	, 'process'	=> function ($setup, $run) {
		
		$objs = 		$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];

		// find tags to apply, based on setup
		$tags = [];
		$tagType = 'tagged_with';
		switch ($setup['tagType']) {
			
			case 'share':
				$tagType = 'shared_with';
				break;
			
			default:
				$tagType = 'tagged_with';
				break;
			
		}
		
		if (
			is_array($setup['options'])
			&& is_array($setup['options']['tags'])
		) {
			
			foreach ($setup['options']['tags'] as $i => $tagId) {
				
				array_push(
					$tags
					, intval($tagId)
				);
				
			}
			
		}
		
		// update tags, if they exist
		if (!empty($tags)) {
			
			$updated = $objs->setTags(
				$new['id']
				, $tags
				, $tagType
			) ;
			
			$update = [
				'id' => 			$new['id']
			];
			$update[$tagType] = 	$updated;
			
			return [
				'msg' => 		'Tags set.'
				, 'update' => 	$update
			];
			
		}
		
		return false;
		
	}
];

?>