<?php

return [
	'name' 		=> 'applyTags'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		
		$applyToObj = $new;
		$tagType = 'tagged_with';
		$applyToParent = false;

		$appConfig = $objs->rules->appConfig;

		switch ($setup['tagType']) {
			
			case 'share':
				$tagType = 'shared_with';
				break;
			
			default:
				$tagType = 'tagged_with';
				break;
			
		}


		$vars = [];
		$project = null;
		$user = null;
		$notifyMessage = false;
		$error = false;

		//prepare info for any case notifyEmail or notifyApp
		if($setup['notifyEmail'] || $setup['notifyApp']){
			$userId = $setup["tags"][0];
			$user = $objs->getById(
				''
				, $userId
			);

			$project = $objs->getById(
				''
				, $applyToObj['parent']
			);

			if(!$project){
				$error = true;
			}

			$entity_type = $objs->where('entity_type', array(
				'bp_name' => substr($new["object_bp_type"], 1)
			))[0];
			$roleName = $entity_type["blueprint"][$setup["fieldName"]]["name"];

			$urlText = "bento.infinityhospitality.net/app/". $appConfig["instance"] . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $project["id"] . "-" . urlencode($applyToObj["name"]);

			$notifyMessage = $user["fname"]. " ".$user["lname"]. " has been assigned as ". $roleName. ". Notified via email";

			$vars = array(
				'{userRecipient}' => $user["fname"] . " ". $user["lname"],
				'{roleName}' => $roleName,
				'{projectName}' => $project["name"],
				'{company}' => $appConfig['systemName'],
				'{projectLink}' => $urlText
			);

		}

		//if is the user field has notify email send the email notification
		if($setup['notifyEmail']){

			$comm = new Comm($objs, $appConfig);

			$mergevars = (object) [
				'TITLE' => 'Your role has changed',
				'BODY' => '<div>{userRecipient}, you\'ve been given a <strong>{roleName}</strong> Assignment for {company} : <a href="{projectLink}"><strong> {projectName} </strong></a> </div>',
				'INSTANCE_NAME' => $appConfig['instance']
			];

			$mergevars->BODY = strtr($mergevars->BODY, $vars);

			if(!$error) {
				$comm->sendMandrillEmail(
					[$user["email"]],
					null,
					"You've been give a new role",
					$mergevars,
					['TITLE', 'BODY', 'INSTANCE_NAME'],
					$applyToObj['id'],
					1,
					0,
					false,
					null,
					$appConfig['instance'],
					1,
					[]
				);
			}


		}

		//if is the user field has notify app send a app notification
		if($setup['notifyApp']){

			$title = "{userRecipient}, you've been given a {roleName} Assignment for {company} : {projectName}";
			$title = strtr($title, $vars);

			$urlText = "bento.infinityhospitality.net/app/". $appConfig["instance"] . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $project["id"] . "-" . urlencode($project["name"]);

			$notificationsToCreate = [];

			$notification = [
				'title' => 		"Role given",
				'details' => 	$title,
				'producer' => 	$applyToObj['id'],
				'color' => 		"purple",
				'icon' => 		"linkify",
				'type' => 		"general",
				'user' => 		$user,
				'link' => 		$urlText,
				'is_viewed' => 	0
			];

			array_push($notificationsToCreate, $notification);
			if(!$error) {
				$objs->create('notification', $notificationsToCreate, true);
			}

		}


		// If the tags should be applied to the parent, swap the context obj
		// out for its parent.
		if ($setup['options'] && $setup['options']['applyToParent'] === true) {
			
			$applyToParent = true;
			$applyToObj = $objs->getById(
				''
				, $applyToObj['parent']
			);

		}
		
		// find tags to apply, based on setup
		$tags = [];
		$notificationTxt = $applyToObj['name'] .' has been shared with you.';
		
		if (
			$setup['object_bp_type'] === 'event_type'
			|| 
			(
				$setup['options']
				&& is_array($setup['options']['tags'])
			)
		) {
			
			if (
				is_array($setup['options'])
				&& is_array($setup['options']['tags'])
			) {
				
				foreach ($setup['options']['tags'] as $i => $tagId) {
					
					array_push(
						$tags
						, intval($tagId)
					);
					
				}
				
			}
			
			// Read 'pullFrom' values from obj
			if (is_array($setup['options']['tagsFromObj'])) {
				
				foreach ($setup['options']['tagsFromObj'] as $addToTags) {
					
					$split = explode('.', $addToTags);
					
					// For tags on parent
					if (
						count($split) > 0
						and $split[0] === 'parent'
					) {
						
						$parent = $objs->getById(
							''
							, $applyToObj[$split[0]]
						);
						
						if (is_array($parent[$split[1]])) {
							
							$tags = array_merge($tags, $parent[$split[1]]);
							
						} else if(is_numeric($parent[$split[1]])) {
							
							array_push($tags, $parent[$split[1]]);
							
						}
						
					} elseif (substr($split[0], 0, 1) === '#') {

						// $objs->debug = true;
						$child = $objs->where(
							str_replace('-', '.', $split[0])
							, [
								'parent' => $applyToObj['id']
							]
							, ''
							, [
								$split[1] => 'id'
							]
						)[0];

						if (is_int($child[$split[1]])) {

							array_push($tags, $child[$split[1]]);

						} elseif (is_array($child[$split[1]])) {

							foreach ($child[$split[1]] as $childTag) {

								if (is_int($childTag)) {

									array_push($tags, $childTag);

								}

							}

						}
						
					} elseif ($applyToObj[$addToTags]) {
						
						array_push($tags, $applyToObj[$addToTags]);
						
					}
					
				}
				
			}
			
		} elseif ($setup['from'] === 'state') {
			
			$typeObj = $objs->getById(
				''
				, $applyToObj[$setup['type']]
				, [ 'states' => true ]
			);
			
			$oldState = __::find($typeObj['states'], function($state) use($old, $setup) {
				return intval($old[$setup['from']]) === intval($state['uid']);
			});

			if (!$oldState) {
				
				$oldState = __::find($typeObj['states'], function($state) {
					return intval($state['isEntryPoint']) === 1;
				});
				
			}
			
			
			
			$state = __::find($typeObj['states'], function($state) use($applyToObj, $setup) {
				return $applyToObj[$setup['from']] === $state['uid'];
			});
			
			if (array_key_exists('tags', $state)) {
				$tags = $state['tags'];
			}
			
			$notificationTxt = $applyToObj['name'] .' has been moved from '. $oldState['name'] .' to '. $state['name'] .' and shared with you.';
			
			$link = $_POST->link;
			
		} else if (is_array($setup['tags'])) {
			
			$tags = $setup['tags'];
			$link = $setup['link'];
			
		}

		// update tags, if they exist
		if (!empty($tags)) {
			
			// Check tag type, and use shared_with column for 
			// contact properties.
			$tagObjs = $objs->getById(
				''
				, $tags
				, [
					'name' => true
				]
			);

			// Users to notify
			$usersToNotify = [];
			
			foreach ($tagObjs as $tag) {
				
				switch ($tag['object_bp_type']) {
					
					case 'contacts':
						$updated = $objs->tagObject(
							$applyToObj['id']
							, $tag['id']
							, 'shared_with'
						);
						break;
						
					default:
						// Create a notification for users newly tagged onto the obj
						if (
							$tag['object_bp_type'] === 'users'
							&& !in_array($tag['id'], $applyToObj['tagged_with'])
						) {
							array_push($usersToNotify, $tag['id']);
						}

						$updated = $objs->tagObject(
							$applyToObj['id']
							, $tag['id']
							, $tagType
						);
						break;
					
					
				}

			}


            //every change that i'll do update my date
			$date = new DateTime();
            $updated = $objs->update(
                $applyToObj['object_bp_type']
                , ['id' => $applyToObj['id'], 'last_updated' => $date->format('Y-m-d H:i:s')]
            );
			
			// fire notifications
			$notification = [];
			$notification['title']    = $notificationTxt;
			$notification['details']  = '';
			$notification['color']    = 'purple';
			$notification['icon']     = 'at';
			$notification['type']     = 'mention';
			$notification['producer'] = $applyToObj['id'];		

			// Link to My Stuff > Single obj page
			if ($applyToObj['object_bp_type'] === 'groups') {
				$notification['link']     = 'https://bento.infinityhospitality.net/app/'. $applyToObj['instance'] .'#mystuff&1=o-project-'. $applyToObj['id'] .'-'. $applyToObj['name'];
			} else {
				$notification['link']     = 'https://bento.infinityhospitality.net/app/'. $applyToObj['instance'] .'#mystuff&1=e-'. $applyToObj['id'] .'-'. $applyToObj['name'];
			}
			$notification['notify']   = $usersToNotify;// !TODO: new tags go here	

			$run('notify', $notification);
			$update = [
				'id' => $applyToObj['id']
			];
			$update[$tagType] = $updated;

			$messageNotify = $notifyMessage ? $notifyMessage : 'Tags added to '. $applyToObj['name'] .'.';
			$messageNotify = $error ? "Error (No Project Found)" : $messageNotify;

			$ret = [
				'msg' => $messageNotify
				, 'response' => 	$updated
			];

			// If there are updates for the context obj, return them
			// back to the logical flow.
			if (!$applyToParent) {
				
				$ret['update'] = $update;
				
			}
			
			return $ret;
			
		}

		return false;
		
	}
];
