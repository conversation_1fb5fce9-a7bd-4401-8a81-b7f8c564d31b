<?php

return [
	'name' 		=> 'moveSortOrder'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new'];
		$setup = 	$setup['setup'];
		
		// Parse input
        $objId = intval($obj['id']);
        $type = $setup['type'];

		if ($setup['before']) {
			$beforeIndex = $objs->getById(
				''
				, $setup['before']
				, ['sortIndex' => true]
			)['sort_index'];
		}
		if ($setup['after']) {
			$afterIndex = $objs->getById(
				''
				, $setup['after']
				, ['sortIndex' => true]
			)['sort_index'];
		}
		if ($setup['target']) {
			$targetIndex = $objs->getById(
				''
				, $setup['target']
				, ['sortIndex' => true]
			)['sort_index'];
		}

		// Validate input
		if (
			is_int($objId)
			&& is_string($beforeIndex)
			&& is_string($afterIndex)
			&& $type === 'between'
		) {

            if (
                $updated = $objs->moveSortOrder(
                    $objId
                    , $type
                    , $beforeIndex
                    , $afterIndex
					, $obj['object_bp_type']
                )
            ) {
// var_dump([
// 	'newSortIndex' => $updated['sort_index']
// ]);
// die();
                return [
					// 'moveSortOrder' => 	[
					// 	'newSortIndex' => $updated['sort_index']
					// ]
					// , 'msg' =>		$obj['name'] .' moved to '. $newParent['name']
					'update' => 	[
						'id' => 			$updated['id']
						, 'sort_index' => 	$updated['sort_index']
					]
				];

            } else {

                return false;

            }
			
		} elseif (
			is_int($objId)
			&& is_string($targetIndex)
			&& (
				$type === 'before'
				|| $type === 'after'
			)
		) {

			if (
                $updated = $objs->moveSortOrder(
                    $objId
                    , $type
                    , $targetIndex
                    , false
					, $obj['object_bp_type']
                )
            ) {
                
				return [
					'update' => 	[
						'id' => 			$updated['id']
						, 'sort_index' => 	$updated['sort_index']
					]
				];

            } else {

                return false;

            }

		} elseif (
			is_int($objId)
			&& (
				$type === 'to-top'
				|| $type === 'to-bottom'
			)
		) {

			if (
                $updated = $objs->moveSortOrder(
                    $objId
                    , $type
                    , false
                    , false
					, $obj['object_bp_type']
                )
            ) {
                
				return [
					'update' => 	[
						'id' => 			$updated['id']
						, 'sort_index' => 	$updated['sort_index']
					]
				];

            } else {

                return false;

            }

		} else {
			
			return false;
			
		}
		
	}
];

?>