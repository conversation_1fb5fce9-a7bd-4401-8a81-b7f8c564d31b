<?php
	
return [
	'name' => 		'notify'
// 	, 'process' => 	function ($objs, $old, $new, $setup, $run, $appConfig) {
	, 'process' => 	function ($setup, $run) {
		
		$objs = 		$setup['objs'];
		$old = 		$setup['old'];
		$new = 		$setup['new'];
		$appConfig =	$setup['appConfig'];
		$setup = 	$setup['setup'];
		
/*
 		error_reporting(E_ALL);
 		ini_set('display_errors', '1');
*/

		$comm = new Comm($objs, $appConfig);
		
		$title = 			$setup['title'];
		$details = 			$setup['details'];
		$producer = 		$setup['producer'];
		$color = 			$setup['color'];
		$icon = 			$setup['icon'];
		$type =				$setup['type'];
		$link = 			$setup['link'];
		$updNotifyList = 	$setup['notify'];
		
		$notificationsToCreate = [];
		$notification = [];

		// Get users and email from notification list
		$userObjs = $objs->getById(
			'users', 
			$updNotifyList, 
			[
				'email' => 				true
				, 'enabled' => 			true
				, 'canBeNotified' => 	true
			]
		);

		// Don't notify users who are not enabled
		$userObjs = __::filter($userObjs, function($user) { 
			
			if ($user['enabled'] == 1) {
				return $user;				
			} else {
				return false;
			}

		});

		$userObjs = __::filter($userObjs, function ($user) {
			
			return $user['canBeNotified'] === null || $user['canBeNotified'] === true;
			
		});

		// for each user in notify list
		if(is_array($userObjs)){
			
			foreach($userObjs as $i => $userToNotify){
				
				// create notification obj
				$notification = [
					'title' => 		$title,
					'details' => 	$details,
					'producer' => 	$producer,
					'color' => 		$color,
					'icon' => 		$icon,
					'type' => 		$type,
					'user' => 		$userToNotify['od'],
					'link' => 		$link,
					'is_viewed' => 	0
				];
				
				// push to $notificationsToCreate
				array_push($notificationsToCreate, $notification);
				
			}
		
		}

		// create notifications
		$objs->create('notification', $notificationsToCreate, true);

		// Grabbing parent object to identify context of notification
		$contextObj = $objs->getById('', intval($producer) );
		
		$contextName = '';

		// Commenting out this block for now

/*
		if( isset( $contextObj['name'] ) ) {
			
			$contextName = ' on <strong>'. $contextObj['name'] .'</strong>';
			
		} else if( isset( $contextObj['title'] ) ) {
			
			$contextName = ' on <strong>'. $contextObj['title'] .'</strong>';
			
		} else {
			
			$contextName = '';
			
		}
*/		

		// send emails
		$mergevars = [
			'TITLE' => $title
			, 'BODY' => '<table style="font-family: sans-serif !important;" align="" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
							<tr>
					        	<td align="center" bgcolor="#5084CC" style="padding: 5px; color: #ffffff; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold; letter-spacing:-.5px;"></td>
							</tr>
							<tr>
								<td style="padding:10px 10px;">
									<h2>'. $this->appConfig['systemName'] .'</h2>
								</td>
							</tr>
							<tr>
								<td style="padding:10px 10px;">'. $title . $contextName .'<br/><br />'. $details .'</td>
							</tr>
							<tr>
								<td style="padding:10px 10px;">
									<br />
									<a href="'.$link.'" style="color: #5084cc; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold;">View in Bento</a>
								</td>
							</tr>
						</table>'
			, 'TEXT_BODY' => ''.$this->appConfig['systemName'].'
							
							'. $title . $contextName .' '. $details .'
							
							'.$link.''
			, 'BUTTON' => 'View in Bento'
			, 'BUTTON_LINK' => 'https://bento.infinityhospitality.net/app/'. $this->appConfig['instance']
			, 'INSTANCE_NAME' => $this->appConfig['systemName']
		];
		
/*
		$mergevars = [
			'TITLE' => $title,
			'BODY' => '<body style="margin: 0; padding: 0;">
						     <table align="center" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse;">
						          <tr>
						               <td align="center" bgcolor="#5084CC" style="padding: 14px; color: #ffffff; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold; letter-spacing:-.5px; line-height:150%;">'.
						                   $this->appConfig['systemName']
						               .'</td>
						          </tr>
						          <tr>
						               <td bgcolor="#F6F9FC" style="padding: 40px 30px 40px 30px;">
						                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
						                         <tr>
						                              <td>'. $title . $contextName .'<br/> '. $details .'</td>
						                         </tr>
						                    </table>
						               </td>
						          </tr>
						          <tr>
						               <td bgcolor="#5084CC" style="padding: 30px 30px 30px 30px;">
						                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
						                         <tr>
						                         	<td>
						                         	<a href="'.$link.'" align="center" bgcolor="#5084CC" style="padding: 14px; color: #ffffff; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold; letter-spacing:-.5px; line-height:150%;">Click to View</a>						                              
							                        </td>
						                         </tr>
						                    </table>
						               </td>
						          </tr>
						     </table>
						</body>',
			'BUTTON' => 'View in Bento',
			'BUTTON_LINK' => 'https://bento.infinityhospitality.net/app/'. $this->appConfig['instance'],
			'INSTANCE_NAME' => $this->appConfig['systemName']
			
		];
*/

		$comm->sendMandrillEmail(
			__::pluck($userObjs, 'email'), 
			null, 
			$title, 
			$mergevars, 
			$email_tags, 
			$producer, 
			1,
			0,
			false,
			null,
			$this->appConfig['instance'],
			0,
			$new['tagged_with']
		);

		return true;
		
	}
];
