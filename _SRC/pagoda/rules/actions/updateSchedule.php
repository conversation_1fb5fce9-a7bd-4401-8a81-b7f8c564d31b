<?php 
	
	// This action is able to toggle the 'can_be-reassigned' property on a groups object. This is currently used in scheduling to toggle a 'shift' group
	// object between 'Available for pickup' and 'Not available for pickup' modes in scheduling.
	
	return [
		'name' => 'updateSchedule'
		, 'process' => function ($setup, $run) {

			$objs 		= $setup['objs'];
			$obj 		= $setup['old'];
			$new 		= $setup['new'];
			$setup 		= $setup['setup'];

			$project = $objs->getById(
				'groups'
				, $setup['project_id']
				, [
					'proposal' => true
				]
			);

			$schedule = $objs->where('groups', [
				'group_type' => 'Schedule',
				'parent' => $project['proposal']['id']
			],
				'',
				0,
				false,
				0,
				'null',
				'asc',
				100,
				null,
				[],
				'string',
				$object['instance']
			);

			if($schedule !== null) {
				$new = $objs->update(
					'groups',
					[
						'id' => $schedule[0]['id'],
						$setup['property'] => $setup['value']
					],
					[],
					null
				);
			}else {
				return [
					'msg' => 'Schedule not found'
					//, 'update' => $diff
				];
			}
		}
	]
	
?>
