<?php

return [
	'name' 		=> 'FGcreate990Project'
	, 'process'	=> function ($setup, $run) {
				
		$objs = 	$setup['objs'];
		$obj = 	$setup['old'];
		$new = 	$setup['new'];
		$setup = 	$setup['setup'];
		$opts = 	$setup['options'];

		$projectType990 = $opts['type']; // 990L, 990EZ, 990N, 990PF
		$templateId = 0;
		$projectName = '';

		// Core Service keys
		$taxYearKey = '_1';
		$stateOfIncKey = '_18';
		$mainContactKey = '_11';
		$form990Key = '_5';

		// Client Service keys
		$servicesBpKey = '#HDIjlE';
		$servicesYearKey = '_4';
		$servicesStateOfIncKey = '_5';

		// Don't allow creation without a tax year set on the core service
		if ( empty($obj[$taxYearKey]) ) {

			return [
				'msg' => 'A <strong>Tax Year</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		}
		
		/// Don't allow creation without a Main Contact set on the core service
		if ( empty($obj[$mainContactKey]) ) {

			return [
				'msg' => 'A <strong>Main Contact</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		}		

		switch ( $projectType990 ) {

			case '990L':
				$templateId = 1939029; // Production
				$projectName = $obj[$taxYearKey] .' 990';

				$servicesBpKey = '#HDIjlE.b6MdDR';
				break;

			case '990EZ':
				$templateId = 1940120; // Production
				$projectName = $obj[$taxYearKey] .' 990 EZ';
				$coreServicesStatesSelectionKey = '_17';

				$servicesBpKey = '#HDIjlE.1PBp0q';
				break;
				
			case '990N':
				$templateId = 2039013; // Production
				$projectName = $obj[$taxYearKey] .' 990 N';
				$coreServicesStatesSelectionKey = '_17';

				$servicesBpKey = '#HDIjlE.hp8d0g';
				break;
				
			case '990PF':
				$templateId = 1940115; // Production
				$projectName = $obj[$taxYearKey] .' 990 PF';
				$coreServicesStatesSelectionKey = '_17';

				$servicesBpKey = '#HDIjlE.xPc4IL';
				break;								
			
			default:
				return;

		}
	
		$mainContact = $objs->getById(
			'contacts'
			, $obj[$mainContactKey]
			, [
				'name' => 	true
				, 'fname' => 	true
				, 'lname' => 	true
				, 'company' => true
			]
		);

		$mainContactCompany = $mainContact['company'];

        // Check for the tag for the specific year
        $yearTag = $objs->where(
            'system_tags'
            , [
                'tag' => strval($obj[$taxYearKey])
            ]
            , ''
            , [
                'tag' => true
            ]
        )[0];

        // Create a tag for the year if it does not exist
        if (empty($yearTag)) {

            $yearTag = $objs->create(
                'system_tags'
                , [
                    'name' => 		strval($obj[$taxYearKey])
                    , 'tag' => 		strval($obj[$taxYearKey])
                    , 'color' => 	'black'
                ]
                , 0
                , [
                    'tag' => true
                ]
            );

        }

        $tagged_withDefaults = [$yearTag['id'], $mainContact['id'],  $mainContactCompany['id']];			

        $projectTemplate = array();
        $projectTemplate = $objs->getById(
            'groups'
            , $templateId
        );	
        $projectTemplate['tagged_with'] = array_merge($projectTemplate['tagged_with'], $tagged_withDefaults);
        $projectTemplate['status'] = 'open';
        $projectTemplate['shared_with'] = [ $mainContact['id'] ];
        

        $projectDefaults = array();
        $projectDefaults['name'] = $projectName;
        ///Shared_with::Org Contacts
        $projectDefaults['shared_with'] = array( $mainContact['id'] );
        $projectDefaults['main_contact'] = $mainContact['id'];
        ///Template::Project Template ID
        $projectDefaults['data_source_id'] = $projectTemplate['id'];
        ///Parent::Organization
        $projectDefaults['parent'] = $mainContactCompany['id'];
        // $projectDefaults['state'] = 2;
        // $projectDefaults['status'] = 'open';
        $projectDefaults['shared_with'] = [ $mainContact['id'] ];

        $newProj = $objs->castFromTemplate($projectTemplate, null, $projectDefaults);

        // Create the Client Service record that is associated with each FG Project Type
        $serviceTags = array_unique($newProj['tagged_with']);
        array_push($serviceTags, $newProj['id']);

        $serviceObj = $objs->create(
            $servicesBpKey
            , [
                'name' => 						$projectName
                , 'parent' => 					$newProj['id']
                , 'tagged_with' => 				$serviceTags
                , 'shared_with' => 				[$obj[$mainContactKey]]
                , $servicesStateOfIncKey => 	$obj[$stateOfIncKey]
                , $servicesYearKey => 			$obj[$taxYearKey]
            ]
        );

			// Create the first Intake Form
			$mainContact = $objs->getById('contacts', $obj[$mainContactKey], ['company' => 'id', 'contact_info' => ['type' => ['data_type' => true], 'is_primary' => true, 'info' => true]]);
			$org = $objs->getById('companies', $mainContact['company'], ['name' => true, 'contact_info' => ['type' => ['data_type' => true], 'is_primary' => true, 'info' => true]]);
			$coreDemo = $objs->where('#ml7laG', ['parent' => $org['id']], '', ['_2' => true, '_24' => true])[0];

        // Initial intake form, which should carry over data from other records
        $intakeDefaults = [
            'tagged_with' => 	$serviceTags
            , 'name' => 		'01 Intake Questionnaire'
            , 'shared_with' => [$mainContact['id']]
            , 'parent' => $newProj['id']
        ];

        switch ($projectType990) {
            
            case '990L':
            case '990EZ':
                $intakeKey = 				'#Organization_Information';
                $intakeOrgNameKey = 		'_11';
                $intakePrimContactKey = 	'_1';
                $intakeDateOfIncKey = 		'_6';
                $intakeEINKey = 			'_7';
                $intakeStateOfIntKey = 		'_20';
                $intakeOrgContactPhoneKey = '_2';
                $intakeOrgAddressKey = 		'_3';
                $intakeOrgPhoneKey = 		'_4';
                $intakeOrgWebsiteKey = 		'_5';
                break;
                
            case '990N':
                $intakeKey = 				'#2_Accounting_Method_1B_N_';
                $intakeOrgNameKey = 		'_12';
                $intakePrimContactKey = 	'_13';
                $intakeDateOfIncKey = 		'_19';
                $intakeEINKey = 			'_20';
                $intakeStateOfIntKey = 		'_30';
                $intakeOrgContactPhoneKey = '_14';
                $intakeOrgAddressKey = 		'_15';
                $intakeOrgPhoneKey = 		'_17';
                $intakeOrgWebsiteKey = 		'_18';
                break;
                
            case '990PF':
                $intakeKey = 				'#1_Organizational_Information_';
                $intakeOrgNameKey = 		'_1';
                $intakePrimContactKey = 	'_2';
                $intakeDateOfIncKey = 		'_8';
                $intakeEINKey = 			'_9';
                $intakeStateOfIntKey = 		'_17';
                $intakeOrgContactPhoneKey = '_3';
                $intakeOrgAddressKey = 		'_4';
                $intakeOrgPhoneKey = 		'_6';
                $intakeOrgWebsiteKey = 		'_7';
                break;

        }

        $intakeDefaults[$intakeOrgNameKey] = $org['name'];//  = Organization Name
        $intakeDefaults[$intakePrimContactKey] = $obj[$mainContactKey];//  = Primary Contact (id)
        $intakeDefaults[$intakeDateOfIncKey] = $coreDemo['_24'];//  = Date of Inc.
        $intakeDefaults[$intakeEINKey] = $coreDemo['_2'];//  = Federal EIN#
        $intakeDefaults[$intakeStateOfIntKey] = $obj['_18'];//  = State of Inc.

        if (is_array($mainContact['contact_info'])) {
            foreach ($mainContact['contact_info'] as $i => $cInfo) {
                if ((!empty($cInfo['type']) && $cInfo['type']['data_type'] === 'phone') && $cInfo['is_primary'] === 'yes') {
                    $intakeDefaults[$intakeOrgContactPhoneKey] = $cInfo['info'];//  = Contact's Phone
                }
            }
        }
        if (is_array($org['contact_info'])) {
            foreach ($org['contact_info'] as $i => $cInfo) {
                if ($cInfo['is_primary'] === 'yes' && !empty($cInfo['type'])) {
                    switch ($cInfo['type']['data_type']) {
                        case 'address':
                            $intakeDefaults[$intakeOrgAddressKey] = $cInfo['info'];//  = Organization's Address
                            break;
                        case 'phone':
                            $intakeDefaults[$intakeOrgPhoneKey] = $cInfo['info'];//  = Organization's Phone
                            break;
                        case 'website':
                            $intakeDefaults[$intakeOrgWebsiteKey] = $cInfo['info'];//  = Organization's Website
                            break;
                    }
                }
            }
        }

        $intakeForm = $objs->create( $intakeKey, $intakeDefaults);

        // Return message
        return [
            'msg' => '<strong>'. $newProj['name'] .'</strong> <i>Project</i> created.'
        ];
		
		return false;
		
	}
];

?>
