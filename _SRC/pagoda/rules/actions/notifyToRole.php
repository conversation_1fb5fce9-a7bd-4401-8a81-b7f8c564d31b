<?php

return [
    'name' => 'notifyToRole'
    , 'process' => function ($setup, $run) {
        $objs = 	$setup['objs'];
        $old = 		$setup['old'];
        $new = 		$setup['new'];
        $setup = 	$setup['setup'];
        $options = $setup['options'];
        $appConfig = $objs->rules->appConfig;

        $producer = 		$setup['producer'];

        $oldState = $setup['old']["state"];
        //get project for get types (list states)
        $project = $objs->getById('', $new["id"], ["type" =>  true, "last_updated_by" => true]);

        //get role sheet
        $roleSheet = __::find($new['tools'], function($obj){
            $startWith = "Project Role Sheet";
            $len = strlen($startWith);
            return (substr($obj["display_name"], 0, $len) === $startWith);
        });

        $bluePrint = $roleSheet["system_name"];

        $entity_type = $objs->where('entity_type', array(
            'bp_name' => $bluePrint
        ))[0];

        $queryOptions = Array("tagged_with" => $new["id"]);

        $childObjects = Array(
            "type" => true,
            "date_created" => true,
            "object_uid" => true,
            "parent" => true,
            "name" => ["object_uid" => true],
            "status" => true,
            "created_by"=>true,
            "is_template"=>true,
            "time_logged"=>true,
            "time_estimate"=>true,
            "last_updated_by"=>true,
            "date_created_done"=>true
        );

        foreach($entity_type["blueprint"] as $key => $value){
            if($value["fieldType"] == "user"){
                $childObjects = array_merge([$key => ["fname" => true,"lname" => true, "profile_image" => true,"color" => true]], $childObjects);
            }
        }

        $bps = $objs->where('#' . $bluePrint, $queryOptions, '', $childObjects);

        $recipients = [];

        //get states
        $currentState = __::find($project['type']['states'], function($state) use ($oldState) {
            if (intval($state['uid']) == intval($oldState)) {
                return true;
            }

            return false;
        })["name"];

        $nextState = __::find($project['type']['states'], function($state) use ($new) {
            if (intval($state['uid']) == intval($new["state"])) {
                return true;
            }

            return false;
        })["name"];


        foreach($options["roles"] as $role){

            $user = $objs->getById('users', $bps[0][$role]["id"]);

            $recipients[] = Array(
                "email" => $user["email"],
                "userRecipient" => $user["name"],
                "roleUserName" => array_key_exists('blueprint', $entity_type) && array_key_exists($role, $entity_type['blueprint']) ? $entity_type["blueprint"][$role]["name"] : "Not defined"
            );
        }


        $projectName = $new["name"];
        $updater = $project["last_updated_by"]["name"];

        $comm = new Comm($objs, $appConfig);
        $taggedWithArr = [];

        $mergevars = (object) [
            'TITLE' => 'The state of ' . $projectName . ' has been updated',
            'BODY' => '<div>Hello {userRecipient}, </div><br> <div>You are being notified because a project you\'re {managerName} - <strong> {projectName} </strong> - has been moved from <strong> {currentState} </strong> to <strong> {nextState} </strong> by <strong> {updater} </strong> </div> <div style=\"margin: 10px 0;\"></div></div>',
            'INSTANCE_NAME' => $appConfig['instance']
        ];



        foreach($recipients as $recipient) {

            $vars = array(
                '{userRecipient}' => $recipient["userRecipient"],
                '{managerName}' => $recipient["roleUserName"],
                '{projectName}' => $projectName,
                '{currentState}' => $currentState,
                '{nextState}' => $nextState,
                '{updater}' => $updater
            );


            $mergevars->BODY = strtr($mergevars->BODY, $vars);


            $comm->sendMandrillEmail(
                [$recipient["email"]],
                null,
                "The state has changed for the project",
                $mergevars,
                ['TITLE', 'BODY', 'INSTANCE_NAME'],
                $producer,
                1,
                0,
                false,
                null,
                $appConfig['instance'],
                1,
                $taggedWithArr
            );

        }

        return [
            'msg' => 'Email sent successfully.',
            'update' => $new
        ];
    }
];


?>
