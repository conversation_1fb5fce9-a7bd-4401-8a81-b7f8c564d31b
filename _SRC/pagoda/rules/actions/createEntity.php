<?php

function parseMergeTag($mergeTag) {
	return (substr($mergeTag, 0, 4) === 'this') ? substr($mergeTag, strrpos($mergeTag, '.') + 1) : $mergeTag;
}

function createEntityCreateItemsFromTemplates (
	$templates
	, $objs
	, $setup
	, $obj
	, $listenerSet
	, $shareTags
	, $assignee
	, $assignmentField
	, $mergeVars
) {

	$response = [];
	$mergeFields = [];

	$setBlueprint = $objs->getBlueprint($setup['options']['objectType'], false, false);

	foreach ($templates as $i => $template) {

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		// Use mapped data
		if (
			is_array($setup) 
			&& is_array($setup['options']) 
			&& is_array($setup['options']['map']) 
			&& is_array($setup['options']['_mappedData']) 
		) {
			foreach ($setup['options']['map'] as $key => $val) {
				$template[$key] = $setup['options']['_mappedData'][$key];
			}
		}
		
		if (!empty($listenerSet)) {
			$template['notify'] = $listenerSet;
		}
		
		// Set the parent
		$template['parent'] = $obj['id'];

		if (!empty($listenerSet)) {
			$template['notify'] = $listenerSet;
		}
		if (!empty($shareTags)) {
			$template['shared_with'] = $shareTags;
		}

		// Tags
		array_push($template['tagged_with'], $obj['id']);

		// Pass on parent's tags, (and the parent id as a tag) - this is an override
		if ($setup['options']['passOnTags'] !== false) {
			$template['tagged_with'] = $obj['tagged_with'];
			array_push($template['tagged_with'], $obj['id']);
		}

		// Add additional tags
		if (is_array($setup['options']['additionalTags'])) {
			$template['tagged_with'] = array_merge($template['tagged_with'], $setup['options']['additionalTags']);
		}

		$template['tagged_with'] = array_unique($template['tagged_with']);
		$template['shared_with'] = array_unique($template['shared_with']);

		// Creating a template
		$template['is_template'] = false;
		if ($setup['createTemplate'] === true) {
			$template['is_template'] = true;
		}

		// Pass on parent, if set
		if ($setup['options']['passOnParent']) {
			if (!empty($obj['parent'])) {
				$template['parent'] = $obj['parent'];
			}
		}
		
		if (is_array($setBlueprint)) {

			foreach ($setBlueprint as $key => $fieldDef) {

				$fieldOptionsDefault = $fieldDef['options']['_default'];
				$fieldOptionsDefaultMerge = $fieldDef['options']['_default_merge'];
				$templateKey = $template[$key];
				$templateKeyMerge = $template[$key.'_merge'];
				$seedKey = $setup['seed'][$key];

				if (
					is_array($fieldDef)
					&& $fieldDef['is_archived'] !== true
					&& is_array($fieldDef['options'])
					&& (
						!empty($fieldOptionsDefault)
						|| !empty($fieldOptionsDefaultMerge)
						|| !empty($templateKey)
						|| !empty($templateKeyMerge)
						|| !empty($seedKey)
					)
				) {
					switch ($fieldDef['fieldType']) {

						case 'title':

							$nameTemplate = '';

							if (!empty($setup['seed']['name'])) {
								$nameTemplate = $setup['seed']['name'];
							} else if (!empty($template['name'])) {
								$nameTemplate = $template['name'];
							} else if (!empty($fieldOptionsDefault)) {
								$nameTemplate = $fieldOptionsDefault;
							}

							if (!empty($nameTemplate)) {

								$mergeFields['name'] = array(
									'template' => $nameTemplate,
									'format' => 'plain-text'
								);

							}
							break;

						case 'address':

							if (
								is_array($templateKey)
								&& array_key_exists('street', $templateKey)
								&& is_string($templateKey['street'])
								&& strpos($templateKey['street'], '{{') !== false
							) {
								
								$template[$key] = $objs->getValueAtPath(
									$obj['id']
									, str_replace('this.parent.', '', trim($templateKey['street'], '{{}}'))
									, [
										'field' => 'address'
									]
								);

							}
							break;

						case 'date':

							$dateFieldMergeTemplate = '{{' . $fieldOptionsDefaultMerge . '}}';
							
							if (!empty($seedKey)) {
								$dateFieldMergeTemplate = $seedKey;
							} else if (!empty($templateKeyMerge)) {
								$dateFieldMergeTemplate = '{{' . $templateKeyMerge . '}}';
							}

							if (!empty($dateFieldMergeTemplate)) {

								$mergeFields[$key] = array(
									'template' => trim($dateFieldMergeTemplate),
									'format' => 'plain-text'
								);

							}
							break;

						case 'detail':

							if ($fieldDef['options']['alwaysMerge'] !== true) {

								$detailFieldMergeTemplate = $fieldOptionsDefault;
								
								if (!empty($seedKey)) {
									$detailFieldMergeTemplate = $seedKey;
								} else if (!empty($templateKey)) {
									$detailFieldMergeTemplate = $templateKey;
								}

								if (strpos($detailFieldMergeTemplate, '{{') !== false) {

									$mergeFields[$key] = array(
										'template' => $detailFieldMergeTemplate,
										'format' => 'html'
									);

								}

							}
							break;
						
						case 'user':
						case 'users':
						case 'companies':
						case 'contacts':

							$userFieldMergeTemplate = $fieldOptionsDefaultMerge;
								
							if (!empty($seedKey)) {
								$userFieldMergeTemplate = $seedKey;
							} else if (!empty($templateKeyMerge)) {
								$userFieldMergeTemplate = $templateKeyMerge;
							}

							$userFieldMergeTemplate = str_replace('{{', '', str_replace('}}', '', $userFieldMergeTemplate));
							$userFieldMergeTemplate = parseMergeTag($userFieldMergeTemplate);

							if (!empty($userFieldMergeTemplate)) {
								if ($userFieldMergeTemplate === 'me') {
									$template[$key] = intval($_COOKIE['uid']);
								} else if (is_array($userFieldMergeTemplate)) {
									$template[$key] = array();
									foreach($userFieldMergeTemplate as $mergeTag) {
										$mergeTag = parseMergeTag($mergeTag);
										$value = $objs->getValueAtPath($obj['id'], $mergeTag);
										if (is_array($value)) {
											$values = __::pluck($value, 'id');
											foreach($values as $value) {
												array_push($template[$key], $value);
											}
										}
									}
									$template[$key] = array_values(array_unique($template[$key]));
								} else {
									$template[$key] = $objs->getValueAtPath($obj['id'], $userFieldMergeTemplate);
								}
							}
							break;

						default:
							// Use the default value set on the blueprint IF it is set/not empty
							// AND there is no value set on the provided template. If there is a
							// value set on the template, that takes priority.
							if (
								isset($fieldOptionsDefault)
								&& !empty($fieldOptionsDefault)
								&& empty($template[$key])
							) {
								$template[$key] = $fieldOptionsDefault;
							}
							break;

					}

				}

			}

		}

        // Apply default vals from 'seed', if it is set
        if (
            is_array($setup)
            && is_array($setup['seed'])
        ) {
            
            foreach ($setup['seed'] as $key => $val) {

                switch ($key) {

                    case 'tagged_with':
                        if (is_array($val)) {
                            $template['tagged_with'] = array_unique(array_merge($template['tagged_with'], $val));
                            $template['tagged_with'] = array_filter($template['tagged_with']);
                        }
                        break;

					case 'title':
					case 'date':
					case 'detail':
					case 'user':
					case 'users':
					case 'companies':
					case 'contacts':
						break;
                    
                    default:
                        $template[$key] = $val;
                        break;

                }

            }

        }

		$mergedTemplates = $objs->runSteps(
			$obj
			, [
				'merge' => [
					'obj' => $obj['id'],
					'parent' => $obj['id'],
					'templates' => $mergeFields,
					'mergeVars' => $mergeVars
				]
			]
			, true
		)['memo'];

		foreach ($mergedTemplates as $key => $mergedTemplate) {

			$template[$key] = $mergedTemplate;

		}

		// Set the assignee, if set
		if ($assignee && $assignmentField) {
			$template[$assignmentField] = $assignee;
		}
		
        $castCreate = $objs->castFromTemplate($template, null, $template);
		// Create the entity
		array_push($response, $castCreate);
		
	}

	return $response;

}

return [
	'name' 		=> 'createEntity'
	, 'process'	=> function ($setup, $run) {

		// error_reporting(E_ERROR);
		// ini_set('display_errors', '1');

		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$setup = 	$setup['setup'];

        $sharedWithPortal = false;
        $entity_type = $objs->where('entity_type', array(
            'bp_name' => substr($setup['objectType'], 1)
        ))[0];

        $actionItemTags = [4759706, 4760308];

        $isActionItem = false;
        
        foreach ($entity_type['tagged_with'] as $key => $value) {
            if ( in_array( $value, $actionItemTags) && $entity_type['is_task'] ) {

                $isActionItem = true;
            }
        }

        if ( empty($setup['options']['objectType_name']) ) {
            $setup['options']['objectType_name'] = $entity_type['name'];
        }

        if ( empty($setup['options']['objectType']) ) {
            $setup['options']['objectType'] = $setup['objectType'];
        }
        
        if ( strpos($entity_type['bp_name'], 'Review') !== false ) {
            $sharedWithPortal = true;
        }

        if ( strpos($entity_type['bp_name'], '990_Follow_up') !== false ) {
            $sharedWithPortal = true;
        }
        if ( $entity_type['instance'] == 'foundation_group' && $isActionItem ) {
            $sharedWithPortal = true;
        }
        // find tags to apply, based on setup
		$tags = [];
		$mergeVars = [];

		if (is_array($setup['mergeVars'])) {
			$mergeVars = $setup['mergeVars'];
		}

		// If there are no templates to use, just use the default vals 
		// set on the blueprint
		if ($setup['template'] === false) {

			$template = [
				'object_bp_type' => $setup['objectType'],
				'parent' => $obj['id'],
				'tagged_with' => [$obj['id']]
			];

			$templates = [$template];

		} else if (!is_array($setup['template']) && !empty($setup['template'])) {

			$template = $objs->getById($setup['objectType'], $setup['template']);

			$templates = [$template];

		} else if (is_array($setup['templates']) && !empty($setup['templates'])) {

			$templates = $objs->getById($setup['objectType'], $setup['templates']);
			
		} else {
		
			$templates = $objs->where(
				$setup['options']['objectType'], [
					'parent' => intval($setup['id'])
				]
			);

		}

		// Assignment options
		$assignTo = false;
		$assignmentField = false;

		$listenerSet = [];
		$shareTags = [];
		if (
			$setup
			&& $setup['options']
		) {
			
			if ($setup['options']['autoListen']) {
				
				if (is_array($obj[$setup['options']['autoListen']])) {
	
					$listenerSet = $obj[$setup['options']['autoListen']];
					
				} elseif (is_int($obj[$setup['options']['autoListen']])) {
					
					$listenerSet = [$obj[$setup['options']['autoListen']]];
					
				}
				
			}
			
			if ($setup['options']['autoShare']) {
				
				if (is_array($obj[$setup['options']['autoShare']])) {
	
					$shareTags = $obj[$setup['options']['autoShare']];
					
				} elseif (is_int($obj[$setup['options']['autoShare']])) {
					
					$shareTags = [$obj[$setup['options']['autoShare']]];
					
				}
				
			}

			// Assignment options
			if (
				$setup['options']['assignTo']
				&& $setup['options']['assignmentField']
			) {

				$assignTo = $setup['options']['assignTo'];
				$assignmentField = $setup['options']['assignmentField'];

			}
			
		}

        if ( $obj['instance'] == 'foundation_group' && $sharedWithPortal ) {

            $mainContact = $objs->getById('', $obj['main_contact']);

            $companyId = $objs->getById('companies', $mainContact['company'])['id'];

            if ( !isset($companyId) && empty($companyId) ) {
                $companyId = $objs->getById('companies', $obj['parent'])['id'];
            }

            ///find contacts
            $contactList = $objs->where(
                'contacts'
                , [
                    'company' => $companyId
                ]
            );

            if ( !empty($contactList) ) {

                ///grab ids of contact objs
                foreach ($contactList as $i => $contact) {
                    array_push($shareTags, $contact['id']);
                }

            }

        }

		// Get assign to array
		$assignees = false;
		if (
			!empty($assignTo)
			&& !empty($assignmentField)
		) {

			if (strrpos($assignTo, '.' ) !== false) {
				$assignTo = substr($assignTo, strrpos($assignTo, '.' ) + 1);
			}
			
			$assignees = $objs->getValueAtPath($obj['id'], $assignTo);

			if (!is_array($assignees)) {
				$assignees = array($assignees);
			}

		}

		// Get mapped data
		if (
			is_array($setup['options']) 
			&& is_array($setup['options']['map']) 
			&& !empty($setup['options']['map'])
		) {
			
			$setup['options']['_mappedData'] = $objs->getDataFromMap($setup['options']['map'], $obj['id']);

		}

		// Create new record(s) from the provided templates
		if (
			is_array($templates)
			&& count($templates) > 0
		) {
			
			$userMsg = '';

			// If assigning out to multiple users, iterate over assignees and
			// create new records for each assignee, placing the assignee in 
			// the appropriate field set at $assignmentField by the user.
			if (
				$assignmentField
				&& is_array($assignees)
				&& !empty($assignees)
			) {
				
				foreach ($assignees as $i => $assignee) {
					
					createEntityCreateItemsFromTemplates(
						$templates
						, $objs
						, $setup
						, $obj
						, $listenerSet
						, $shareTags
						, $assignee
						, $assignmentField
						, $mergeVars
					);
					
				}

				$userMsg = count($templates) .' '. $setup['options']['objectType_name'] .'(s) created and assigned to '. count($assignees) .' team members.';
				
			// Otherwise, just create the items from the list
			} else {
				
				$response = createEntityCreateItemsFromTemplates(
					$templates
					, $objs
					, $setup
					, $obj
					, $listenerSet
					, $shareTags
					, false
					, false
					, $mergeVars
				);

				$userMsg = count($templates) .' '. $setup['options']['objectType_name'] .'(s) created.';

			}

			// Don't need a msg to the user for regular creates
			if ($setup['template'] === false) {
				$userMsg = '';
			}
			
			// return message
			return [
				'msg' => $userMsg,
				'memo' => $response
			];
			
		}

		return [
			'msg' => 'Nothing to create.'
		];
		
	}
];

?>