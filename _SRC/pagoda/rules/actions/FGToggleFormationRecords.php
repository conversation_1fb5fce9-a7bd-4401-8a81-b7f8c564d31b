<?php

return [
	'name' 		=> 'FGToggleFormationRecords'
	, 'process'	=> function ($setup, $run) {

		$test = false;

		// Get setup info
		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		
		$supplementalObj = $new;

		// Supplemental keys
		$supplementalStatesKey = '_10';
		$supplementalYearKey = '_89';

		// Proj types
		$initialProjType = 2051672;
		$renewalProjType = 2318047;

		$form1023Type = 2238449;
		$form1023EType = 8270768;
		$form1024Type = 8270774;
		$form1024AType = 8270798;

		$formationType = '';
		
		if (!empty($setup['supplementalObj'])) {
			$supplementalObj = $setup['supplementalObj'];
		}

		if($test){
			$supplementalObj = $objs->where(
				'#kpsODp'
				, [
					'id' => 8283518
				]
				, ''
				, [
					'parent' => true
					, $supplementalStatesKey => true
					, $supplementalYearKey => true
				]
			)[0];
		} else {
			$supplementalObj = $objs->where(
				$supplementalObj['object_bp_type']
				, [
					'id' => $setup['supplementalObj']['id']
				]
				, ''
				, [
					'parent' => true
					, $supplementalStatesKey => true
					, $supplementalYearKey => true
				]
			)[0];
		}


		// Services keys
		$servicesBpKey = '#HDIjlE';
		$servicesYearKey = '_4';
		
		$parentProj = $objs->where(
					'groups'
					, [
						'id' => $supplementalObj['parent']['id']
					]
					, ''
					, [
						'main_contact' => true
						, 'type' 		=> true
					]					
				)[0];

		switch ($parentProj['type']['id']) {

			case $form1023Type:
				$formationType = '1023';
				break;
			case $form1023EType:
				$formationType = '1023E';
				break;
            case $form1024Type:
                $formationType = '1024';
                break;
            case $form1024AType:
                $formationType = '1024A';
                break;
		}

		// Check for the tag for the specific year
		$yearTag = $objs->where(
			'system_tags'
			, [
				'tag' => strval($supplementalObj[$supplementalYearKey])
			]
			, ''
			, [
				'tag' => true
			]
		)[0];

		// Create a tag for the year if it does not exist
		if (empty($yearTag)) {

			$yearTag = $objs->create(
				'system_tags'
				, [
					'name' => 		strval($supplementalObj[$supplementalYearKey])
					, 'tag' => 		strval($supplementalObj[$supplementalYearKey])
					, 'color' => 	'black'
				]
				, 0
				, [
					'tag' => true
				]
			);

		}
		
		// Set/Field Keys
		$supplementalBlueprint = $objs->where(
			'entity_type'
			, [
				'bp_name' => substr($supplementalObj['object_bp_type'], 1)
			]
		)[0];

/*		return [
			'msg' => 'Created Forms: '. json_encode($supplementalObj)
		];*/
		
		$selectedStates = [];

		if (is_array($supplementalObj[$supplementalStatesKey])) {
			foreach ($supplementalObj[$supplementalStatesKey] as $selectedOption) {

				array_push(
					$selectedStates
					, __::find(
						$supplementalBlueprint['blueprint'][$supplementalStatesKey]['options']['options']
						, function ($opt) use ($selectedOption) {

							return $opt['value'] === $selectedOption;

						}
					)
				);

			}
		}
		
		// Create/toggle the forms
		$stateSpecificFormSets = $objs->where(
			'entity_type'
			, [
				'name' => [
					'type' => 		'contains'
					, 'value' => 	'FRMA_'
				]
			]
		);

		$stateSpecificFormKeys = [];
		foreach ($stateSpecificFormSets as $formSet) {
			array_push($stateSpecificFormKeys, '#'. $formSet['bp_name']);
		}

		// Find the forms that should exist in the current context
		$formsThatShouldExist = [];
		$formsAlreadyCreated = $objs->where(
			$stateSpecificFormKeys
			, [
				'tagged_with' => $parentProj['id']
			]
			, ''
			, [
				'name' => true
			]
		);
		$formsPreviouslyArchived = $objs->where(
			$stateSpecificFormKeys
			, [
				'tagged_with' => 	$parentProj['id']
				, 'archive' => 		true
			]
			, ''
			, [
				'name' => true
			]
		);

		$formTags = $parentProj['tagged_with'];
		array_push($formTags, $parentProj['id']);
		array_push($formTags, $yearTag['id']);

		$formTags = array_unique($formTags);

		foreach ($selectedStates as $selectedState) {

			$stateForms = __::filter(
				$stateSpecificFormSets
				, function ($formSet) use ($selectedState, $formationType) {

					// If it contains the CharSol Type (or BOTH) AND it contains the 
					// state value in the middle section (CS_[STATES GO HERE]_...)
					if (
						strpos(
							explode('_', $formSet['name'])[1]
							, explode(' - ', $selectedState['name'])[0]
						) !== false
						&& (
							strpos(
								$formSet['name']
								, '_BOTH'
							) !== false
							|| strpos(
								$formSet['name']
								, '_'. $formationType
							) !== false
						)
					) {
						return true;
					}

					return false;

				}
			);

			foreach ($stateForms as $formBp) {

				array_push(
					$formsThatShouldExist
					, [
						'name' => 				explode(' | ', $formBp['name'])[1]
						, 'parent' => 			$parentProj['id']
						, 'tagged_with' => 		$formTags
						, 'shared_with' => 		[$parentProj['main_contact']['id']]
						, 'object_bp_type' => 	'#'. $formBp['bp_name']
					]
				);

			}

		}

		// Only need one of each kind of form
		$formsThatShouldExist = __::uniq($formsThatShouldExist, function ($stateForm) {
			return $formBp['bp_name'];
		});

		//return [
		//	'msg' => 'selected: ' . json_encode($formsThatShouldExist)
		//];

		// Create the new forms
		$createdForms = [];

		foreach ($formsThatShouldExist as $formToCreate) {
			
			// If the form does not exist already, create it
			if (
				empty(
					__::filter(
						$formsAlreadyCreated
						, function ($formAlreadyCreated) use ($formToCreate) {
							
							return $formAlreadyCreated['object_bp_type'] === $formToCreate['object_bp_type'];
							
						}
					)
				)
				&& empty(
					__::filter(
						$formsPreviouslyArchived
						, function ($formAlreadyCreated) use ($formToCreate) {
							
							return $formAlreadyCreated['object_bp_type'] === $formToCreate['object_bp_type'];
							
						}
					)
				)
			) {
				
				array_push(
					$createdForms
					, $objs->create(
						$formToCreate['object_bp_type']
						, $formToCreate
					)
				);
					
			}
			
		}

		// Restore forms that should exist necessary
		$restoredForms = [];
		foreach ($formsPreviouslyArchived as $formPreviouslyArchived) {
			
			if (
				// If it should exist...
				!empty(
					__::filter(
						$formsThatShouldExist
						, function ($formThatShouldExist) use ($formPreviouslyArchived) {

							return $formThatShouldExist['object_bp_type'] === $formPreviouslyArchived['object_bp_type'];

						}
					)
				)
				//.. and another archived like it hasn't already been restored
				&& empty(
					__::filter(
						$restoredForms
						, function ($restoredForm) use ($formPreviouslyArchived) {
							
							return $restoredForm['object_bp_type'] === $formPreviouslyArchived['object_bp_type'];
							
						}
					)
				)
			) {
				
				array_push($restoredForms, $formPreviouslyArchived);
				/*$objs->restore(
					$formPreviouslyArchived['object_bp_type']
					, $formPreviouslyArchived['id']
				);*/
					
			}
			
		}

		// Temporary, move this to rules layer
		if (!array_key_exists('_resp', $GLOBALS)) {
			$GLOBALS['_resp'] = [];
		}
		if (
			!array_key_exists('_msg', $GLOBALS['_resp'])
			or !is_array($GLOBALS['_resp']['_msg'])
		) {
			$GLOBALS['_resp']['_msg'] = ['Action items for this project have been updated.{{REFRESH PAGE}}'];
		} else {
			$GLOBALS['_resp']['_msg'] = array_merge(
				$GLOBALS['_resp']['_msg']
				, ['Action items for this project have been updated.{{REFRESH PAGE}}']
			);
		}
		
		return false;
		
	}
];

?>
