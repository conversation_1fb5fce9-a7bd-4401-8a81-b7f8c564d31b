<?php

return [
	'name' 		=> 'FGcreateCharSolProject'
	, 'process'	=> function ($setup, $run) {

		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		$opts = 	$setup['options'];
		$tagsFromContext = [
			'tagged_with' => 	[]
			, 'shared_with' => 	[]
		];

        ///If Context (Core Service Obj) is type:'Surestart' then proceed with Hold Process
        ///[] Generate a Hold Status Record
        ///[] Default new CharSol project to Hold State
        $generateHoldStatus = false;

        ///Core Service subset type for SureStart - #3KelnN.z3st2d
        if ($obj['object_bp_type'] == '#3KelnN.z3st2d'){
            $generateHoldStatus = true;
        }

		//!TODO: Pull this in from context
		$CharSolType = $opts['type']; // 'INITIAL' or 'RENEWAL'
		$charSolTypeName = '';
		$templateId = 0;
		$projectName = '';

		// Core services field keys
		$taxYearKey = '_1';
		$stateOfIncKey = '_18';
		$coreServicesStatesSelectionKey = '';
		$mainContactKey = '_11';

		// Supplemental key-data
		$supplementalObjType = '#SuNLVr';
		$supplementalStateOfIncKey = '_1';
		$supplementalStatesKey = '_10';
		$supplementalYearKey = '_89';

		// Services keys
		$servicesBpKey = '#HDIjlE';
		$servicesYearKey = '_4';
		$servicesStateOfIncKey = '_5';

		$test = false;
		if ($test) {
			$servicesBpKey = '#1W37Ik';
			$servicesYearKey = '_1';
		}

		$test = false;
		if ($test) {
			$taxYearKey = '_8';
			$stateOfIncKey = '_9';
			$supplementalObjType = '#lfRgxX';
			$supplementalStateOfIncKey = '_1';
			$supplementalStatesKey = '_2';
			$supplementalYearKey = '_4';
		}

		// Don't allow creation without a tax year set on the core service
		if (empty($obj[$taxYearKey])) {

			return [
				'msg' => 'A <strong>Tax Year</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		}

		/// Don't allow creation without a Main Contact set on the core service
		if (
			empty($obj[$mainContactKey])
		) {

			return [
				'msg' => 'A <strong>Main Contact</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		}

		// Don't allow creation without a state of incorporation set on the core service
/*
		if (
			empty($obj[$stateOfIncKey])
			|| empty($obj[$stateOfIncKey]['state'])
		) {

			return [
				'msg' => 'A <strong>State of Incorporation</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		}
*/
		switch ($CharSolType) {

			case 'initial':
				$templateId = 2337747; // Production
				$projectName = $obj[$taxYearKey] .' Charitable Solicitation Initial';
				$coreServicesStatesSelectionKey = '_15';
				if ($test) {
					$templateId = 17571;// Test Instance
					$coreServicesStatesSelectionKey = '_6';
				}
				$charSolTypeName = 'Initial';
				$servicesBpKey = '#HDIjlE.oKxt3d';
				break;

			case 'renewal':
				$templateId = 4267340; // Production
				$projectName = $obj[$taxYearKey] .' Charitable Solicitation Renewal';
				$coreServicesStatesSelectionKey = '_17';
				if ($test) {
					$templateId = 17575;// Test Instance
					$coreServicesStatesSelectionKey = '_7';
				}
				$charSolTypeName = 'Renewal';
				$servicesBpKey = '#HDIjlE.lXjaM4';
				break;

			default:
				return;

		}

		$template = $objs->getById(
			'groups'
			, $templateId
		);

		// Find main contact from tags
		$tags = $objs->getById(
			''
			, $obj['tagged_with']
			, [
				'main_contact' => 1
			]
		);

		// Try to use main_contact from a related project
		if (is_array($tags)) {
			foreach ($tags as $tag) {

				if ($tag['main_contact']) {

					$defaults['main_contact'] = $tag['main_contact']['id'];

				}

			}
		}

		// If there's a contact in tags, use that as the main contact
		if (is_array($tags)) {
			foreach ($tags as $tag) {

				if ($tag['object_bp_type'] === 'contacts') {

					$defaults['main_contact'] = $tag['id'];
					array_push(
						$tagsFromContext['shared_with']
						, $tag['id']
					);

				} else {

					array_push(
						$tagsFromContext['tagged_with']
						, $tag['id']
					);

				}

			}
		}

		// If there's a contact field, use that for the main_contact of the project.
		// If there's a teammate field, use that for the manager of the project.
		$bp = $objs->getBlueprint($obj['object_bp_type']);
		// Turning this off for now - not necessary to set the manager
		if (is_array($bp)) {
			foreach ($bp as $key => $field) {

				switch ($field['fieldType']) {

					case 'contacts':
						if (is_int($obj[$key]) && $obj[$key] > 0) {
							$defaults['main_contact'] = $obj[$key];

							// Share with the contact
							array_push($template['shared_with'], $obj[$key]);

							// Tag with the company for that contact
							$contact = $objs->getById(
								'contacts'
								, $obj[$key]
								, [
									'company' => 'id'
								]
							);

							if ($contact && $contact['company']) {
								array_push($template['tagged_with'], $defaults['main_contact']);
								array_push($template['tagged_with'], $contact['company']);
							}

						}
						break;

					case 'users':
					case 'user':
						if (is_int($obj[$key]) && $obj[$key] > 0) {

							// Set the manager
							// if (empty($defaults['managers'])) {
							// 	$defaults['managers'] = [];
							// }
							// array_push($defaults['managers'], $obj[$key]);

							// Share with the contact
							array_push($template['tagged_with'], $obj[$key]);

						}
						break;

				}

			}
		}

		if ($obj['object_bp_type'] === 'contacts') {

			$defaults['main_contact'] = $obj;
			$mainContact = $obj;

		} elseif ($defaults['main_contact']) {

			$mainContact = $objs->getById(
				'contacts'
				, $obj['main_contact']
				, [
					'name' => 		true
					, 'fname' => 	true
					, 'lname' => 	true
				]
			);

		}

		// Setting project dates
		if(!empty($obj['start_date'])) {

			$templateDateCreated = (new DateTime($template['date_created']))->format('U');
			$projectStartDate = (new DateTime($obj['start_date']))->format('U');
			$projectEndDate = (new DateTime($obj['end_date']))->format('U');

			$dateDiff = intval($projectStartDate) - intval($templateDateCreated);
			$startEndDateDiff = intval($projectEndDate) - intval($projectStartDate);

			$newProjectStartDate = (new DateTime())->format('U') + intval($dateDiff);
			$newProjectEndDate = intval($newProjectStartDate) + intval($startEndDateDiff);

			$defaults['start_date'] = date('Y-M-d', $newProjectStartDate);
			$defaults['end_date'] = date('Y-M-d', $newProjectEndDate);

		}

        // Check for the tag for the specific year
        $yearTag = $objs->where(
            'system_tags'
            , [
                'tag' => strval($obj[$taxYearKey])
            ]
            , ''
            , [
                'tag' => true
            ]
        )[0];

        // Create a tag for the year if it does not exist
        if (empty($yearTag)) {

            $yearTag = $objs->create(
                'system_tags'
                , [
                    'name' => 		strval($obj[$taxYearKey])
                    , 'tag' => 		strval($obj[$taxYearKey])
                    , 'color' => 	'black'
                ]
                , 0
                , [
                    'tag' => true
                ]
            );

        }

		if ($template) {

			$template['name'] = $projectName;

			if (strpos($template['name']) !== false) {

				$mergedTitle = $objs->runSteps(
					$obj
					, [
						'merge' => [
							'obj' => 		$obj['id']
							, 'parent' => 	$obj['id']
							, 'template' => $template['name']
							, 'format' => 	'plain-text'
						]
					]
					, true
				)['memo'];

				if (!empty($mergedTitle)) {
					$template['name'] = $mergedTitle;
				}

			}

			// Set the parent property
			$template['parent'] = [ 'id' => $obj['id'] ];
			if ($options['passOnParent'] === true) {
				$template['parent'] = $obj['parent'];
				$defaults['parent'] = $obj['parent'];
			}

			// Pass down tags from context
			$template['tagged_with'] = array_merge(
				$template['tagged_with']
				, $tagsFromContext['tagged_with']
			);
			$template['shared_with'] = array_merge(
				$template['shared_with']
				, $tagsFromContext['shared_with']
			);
			$template['tagged_with'] = array_unique($template['tagged_with']);
			$template['shared_with'] = array_unique($template['shared_with']);
			$template['status'] = 'open';

			array_push($template['tagged_with'], $obj['id'], $yearTag['id']);

            if ( $generateHoldStatus ) {
                ///Set default state for new project - Hold State is 17
                $defaults['state'] = 17;
                $defaults['status'] = 'onHold';
            }

			if ($defaults['main_contact']) {
				$template['main_contact'] = $defaults['main_contact'];
			}

			// $newProj = $objs->castFromTemplate($template, null, $defaults);
			$template['is_template'] = 0;
			$newProj = $objs->create('groups', $template);

			if ( $generateHoldStatus ) {

                $holdStatusToCreate = [
                    'name' =>               $newProj['name']
                    , 'parent' =>           $newProj['id']
                    , '_1' =>               $obj[$stateOfIncKey]
                    , '_4' =>               3
                    , '_4_status' =>        'open'
                    , 'tagged_with' =>      $newProj['tagged_with']
                ];

                array_push($holdStatusToCreate['tagged_with'], $newProj['id']);
                array_push($holdStatusToCreate['tagged_with'], $yearTag);
                array_push($holdStatusToCreate['tagged_with'], $obj['parent']);


                $holdStatusToCreate['tagged_with'] = array_merge(
                    $holdStatusToCreate['tagged_with']
                    , $tagsFromContext['tagged_with']
                );

                $holdStatus = $objs->create(
                    '#IBDFHY'
                    , $holdStatusToCreate
                );

            }

			// Create the supplemental
			$supplementalToCreate = [
				'name' => 							'04 Charitable Solicitation Supplemental'
				, 'parent' => 						$newProj['id']
				, 'tagged_with' => 					$newProj['tagged_with']
				, 'shared_with' => 					[$obj[$mainContactKey]]
				, $supplementalStateOfIncKey => 	$obj[$stateOfIncKey]
				, $supplementalStatesKey => 		$obj[$coreServicesStatesSelectionKey]
				, $supplementalYearKey => 			$obj[$taxYearKey]
			];

			array_push($supplementalToCreate['tagged_with'], $newProj['id']);
			array_push($supplementalToCreate['tagged_with'], $yearTag['id']);
			array_push($supplementalToCreate['tagged_with'], $obj[$mainContactKey]);

			$supplementalToCreate['tagged_with'] = array_unique(
				$supplementalToCreate['tagged_with']
			);
			$supplementalObj = $objs->create(
				$supplementalObjType
				, $supplementalToCreate
			);

			// Create the service
			$serviceTags = array_unique($newProj['tagged_with']);
			array_push($serviceTags, $newProj['id']);
			array_push($serviceTags, $yearTag['id']);
			array_push($serviceTags, $obj[$mainContactKey]);

			$serviceObj = $objs->create(
				$servicesBpKey
				, [
					'name' => 					$obj[$taxYearKey] .' Charitable Solicitations '. $charSolTypeName
					, 'parent' => 					$newProj['id']
					, 'tagged_with' => 				$serviceTags
					, 'shared_with' => 				[$obj[$mainContactKey]]
					, $servicesStateOfIncKey => 		$obj[$stateOfIncKey]
					, $servicesYearKey => 			$obj[$taxYearKey]
				]
			);

			// Universal Intake Form BP Names
			$UniversalIntakeOneKey = 		'#CS_Organization_Information_1A_';
			$UniversalIntakeOneStatusKey = 	'_17';
			$UniversalIntakeTwoKey = 		'#CS_Organization_Stakeholders_1B_';
			$UniversalIntakeTwoStatusKey = 	'_11';
			$UniversalIntakeThreeKey = 		'#CS_Organization_Activity_1A_';
			$UniversalIntakeThreeStatusKey = '_31';

			// Get the previous year tag
			if ($CharSolType == 'renewal') {

				$previousYearTag = $objs->where(
					'system_tags'
					, [
						'tag' => strval($obj[$taxYearKey] - 1)
					]
					, ''
					, [
						'tag' => true
					]
				)[0];

			}

			// Tags for Universal Intake forms
			$universalIntakeTags = $newProj['tagged_with'];
			array_push($universalIntakeTags, $newProj['id']);
			array_push($universalIntakeTags, $yearTag['id']);
			array_push($universalIntakeTags, $obj[$mainContactKey]);
			array_push($universalIntakeTags, $contact['company']);

			$universalIntakeTags = array_unique(
				$universalIntakeTags
			);
			
			// Create new Universal form 1
			if ($CharSolType == 'renewal' && !empty($previousYearTag)) {

				$universalOneToCreate = $objs->where(
					$UniversalIntakeOneKey
					, [
						'tagged_with' => [$previousYearTag['id'], $contact['company']]
					]
				);
				unset($universalOneToCreate[$UniversalIntakeOneStatusKey]);
				unset($universalOneToCreate['status']);

			}

			if (empty($universalOneToCreate) || $universalOneToCreate == null) {
				$universalOneToCreate = [];
			}
			$universalOneToCreate['name'] = 			'01 Universal Intake';
			$universalOneToCreate['parent'] = 			$newProj['id'];
			$universalOneToCreate['tagged_with'] = 		$universalIntakeTags;
			$universalOneToCreate['shared_with'] = 		[$obj[$mainContactKey]];
			$universalOneToCreate['object_bp_type'] = 	$UniversalIntakeOneKey;

			// Create new Universal form 2
			if ($CharSolType == 'renewal' && !empty($previousYearTag)) {

				$universalTwoToCreate = $objs->where(
					$UniversalIntakeTwoKey
					, [
						'tagged_with' => [$previousYearTag['id'], $contact['company']]
					]
				)[0];
				unset($universalTwoToCreate[$UniversalIntakeTwoStatusKey]);
				unset($universalTwoToCreate['status']);

			}

			if (empty($universalTwoToCreate) || $universalTwoToCreate == null) {
				$universalTwoToCreate = [];
			}
			$universalTwoToCreate['name'] = 			'02 Universal Intake';
			$universalTwoToCreate['parent'] = 			$newProj['id'];
			$universalTwoToCreate['tagged_with'] = 		$universalIntakeTags;
			$universalTwoToCreate['shared_with'] = 		[$obj[$mainContactKey]];
			$universalTwoToCreate['object_bp_type'] = 	$UniversalIntakeTwoKey;

			// Create new Universal form 3
			if ($CharSolType == 'renewal' && !empty($previousYearTag)) {

				$universalThreeToCreate = $objs->where(
					$UniversalIntakeThreeKey
					, [
						'tagged_with' => [$previousYearTag['id'], $contact['company']]
					]
				)[0];
				unset($universalThreeToCreate[$UniversalIntakeThreeStatusKey]);
				unset($universalThreeToCreate['status']);

			}

			if (empty($universalThreeToCreate) || $universalThreeToCreate == null) {
				$universalThreeToCreate = [];
			}
			$universalThreeToCreate['name'] = 				'03 Universal Intake';
			$universalThreeToCreate['parent'] = 			$newProj['id'];
			$universalThreeToCreate['tagged_with'] = 		$universalIntakeTags;
			$universalThreeToCreate['shared_with'] = 		[$obj[$mainContactKey]];
			$universalThreeToCreate['object_bp_type'] = 	$UniversalIntakeThreeKey;
			
			// Create Default Univeral Intakes
			$uniOne = $objs->create(
				$universalOneToCreate['object_bp_type']
				, $universalOneToCreate
			);
			
			// Create new Universal form 2
			$uniTwo = $objs->create(
				$universalTwoToCreate['object_bp_type']
				, $universalTwoToCreate
			);

			// Carry over table-field content (Board Members)
			if ($universalTwoToCreate['id']) {

				$BoardMembersKey = '#wSxENS';
				$tableContent = $objs->where(
					$BoardMembersKey
					, ['parent' => $universalTwoToCreate['id']]
				);

				if (!empty($tableContent)) {
					foreach ($tableContent as $i => $rec) {
						$tableContent[$i]['tagged_with'] = array_diff($tableContent[$i]['tagged_with'], $universalTwoToCreate['id']);
						array_push($tableContent[$i]['tagged_with'], $universalTwoToCreate['id']);
						$tableContent[$i]['parent'] = $uniTwo['id'];
					}
				}

				$objs->create(
					$BoardMembersKey
					, $tableContent
					, 1
					, 0
				);
				
			}

			// Create new Universal form 3
			$uniThree = $objs->create(
				$universalThreeToCreate['object_bp_type']
				, $universalThreeToCreate
			);

			// Carry over table-field content (Court Offense & Court Injunction)
			if ($universalThreeToCreate['id']) {

				$BoardMembersKey = '#CS_Court_Injunction.JXDfpt';
				$tableContent = $objs->where(
					$BoardMembersKey
					, ['parent' => $universalThreeToCreate['id']]
				);

				if (!empty($tableContent)) {
					foreach ($tableContent as $i => $rec) {
						$tableContent[$i]['tagged_with'] = array_diff($tableContent[$i]['tagged_with'], $universalThreeToCreate['id']);
						array_push($tableContent[$i]['tagged_with'], $universalThreeToCreate['id']);
						$tableContent[$i]['parent'] = $uniThree['id'];
					}
				}

				$objs->create(
					$BoardMembersKey
					, $tableContent
					, 1
					, 0
				);
				
			}
			if ($universalThreeToCreate['id']) {
				
				$BoardMembersKey = '#CS_Court_Injunction.ZTQcs5';
				$tableContent = $objs->where(
					$BoardMembersKey
					, ['parent' => $universalThreeToCreate['id']]
				);

				if (!empty($tableContent)) {
					foreach ($tableContent as $i => $rec) {
						$tableContent[$i]['tagged_with'] = array_diff($tableContent[$i]['tagged_with'], $universalThreeToCreate['id']);
						array_push($tableContent[$i]['tagged_with'], $universalThreeToCreate['id']);
						array_push($tableContent[$i]['tagged_with'], $recentClientActivity);
						$tableContent[$i]['parent'] = $uniThree['id'];
					}
				}

				$objs->create(
					$BoardMembersKey
					, $tableContent
					, 1
					, 0
				);
				
			}

			// Create the services and intake forms
			$run(
				'FGToggleCharSolRecords'
				, [
					'supplementalObj' => $supplementalObj
				]
			);

			// Return message
			return [
				'msg' => '<strong>'. $newProj['name'] .'</strong> <i>Project</i> created.'
			];

		}
		
		return false;

	}
];

?>
