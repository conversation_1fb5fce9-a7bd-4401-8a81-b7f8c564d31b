<?php

function FGSetCharsolHoldsStateOfIncIsSet ($coreDemo, $key) {

    // Check that the state of inc is valid and set
    if (
        is_array($coreDemo[$key])
        && $coreDemo[$key]['state']
        && is_string($coreDemo[$key]['state'])
        && !empty($coreDemo[$key]['state'])
    ) {

        return true;

    }

    return false;

}

function FGSetCharsolHoldsDateOfIncIsSet ($coreDemo, $key) {

    // Check that the date of inc is valid and set
    $isDate = false;
    try {

        if (is_string($coreDemo[$key]) && !empty($coreDemo[$key])) {
            $d = DateTime::createFromFormat($format, $date);
            $isDate = ($d && $d->format($format) == $date);
        }

    } catch (Exception $err) {

        $isDate = false;

    }

    return $isDate;

}

function FGSetCharsolHoldsGetStateGroup ($coreDemo, $key) {

    // Get the group (from the spreadsheet)
    // https://docs.google.com/spreadsheets/d/1jk_VXNzZRd4N5JGgo3IRAysqLh-J1r4V/edit#gid=601326437
    switch ($coreDemo[$key]['state']) {

        case 'AL':
        case 'AK':
        case 'CA':
        case 'CO':
        case 'GA':
        case 'HI':
        case 'MI':
        case 'NV':
        case 'NH':
        case 'NY':
        case 'OK':
        case 'RI':
        case 'TN':
        case 'WA':
            return 'A';
        
        case 'AR':
        case 'CT':
        case 'FL':
        case 'IL':
        case 'KS':
        case 'MA':
        case 'MN':
        case 'MS':
        case 'NJ':
        case 'NM':
        case 'NC':
        case 'ND':
        case 'OH':
        case 'OR':
        case 'PA':
        case 'UT':
        case 'VA':
        case 'WI':
            return 'B';

        case 'AZ':
        case 'DC':
        case 'DE':
        case 'ID':
        case 'IN':
        case 'IA':
        case 'KY':
        case 'LA':
        case 'ME':
        case 'MD':
        case 'MO':
        case 'MT':
        case 'NE':
        case 'SC':
        case 'SD':
        case 'TX':
        case 'VT':
        case 'WV':
        case 'WY':
            return 'C';
        
        // Default to the 'A' group, which goes straight to intake
        default:
            return 'A';
        
    }

}

return [
	'name' 		=> 'FGSetCharSolHolds'
	, 'process'	=> function ($setup, $run) {

		$db = 	            $setup['objs'];
		$contextObj = 		$setup['old'];
		$new = 		        $setup['new'];
		$setup = 	        $setup['setup'];
		$opts = 	        $setup['options'];
		$updateHoldStatus = false;
        $charSolProj =      false;

        // Get context company
        $contextCompany = false;
        $contextObjTags = $db->getById('', $contextObj['tagged_with']);

        if (is_array($contextObjTags)) {
            foreach ($contextObjTags as $i => $tag) {

                if ($tag['object_bp_type'] === 'companies') {
                    $contextCompany = $tag;
                }

            }
        }

        if (!$contextCompany) {

            return [
				'msg' => '<strong>'. $charSolProj['name'] .'</strong> must be tagged with a Company to refresh the Hold Status.'
			];

        }

        // Sets to check values on
        $coreDemo =             false;
        $stateSupplemental =    false;

        // Blueprint keys
        $coreDemoKey =              '#ml7laG';
        $coreDemoStateOfIncKey =    '_23';
        $coreDemoDateOfIncKey =     '_24';
        $coreDemoDateIRSApproved =  '_25';
        $coreDemoDateIRSEffective = '_26';
        $stateSupplementalKey =     '';
        $stateSupplementalKey =     '';
        $Letter1023Key =            '#ocdCLG';
        $IRSLetterKey =             '#hNkn6H';

        // 990 project types
        $nine90ProjectTypes = [1938530, 1939262, 1939404, 2036649];

        // CharSol Initial Workflow states
        $charSolInitialDateStateHold =  17;
        $charSolInitial1023Hold =       18;
        $charSolInitialIRSLetterHold =  19;
        $charSolInitial990Hold =        20;
        $charSolInitialIntake =         3;

        // CharSol Renewal Workflow states
        $charSolRenewalTypeId =         2318047;
        $charSolRenewal990Hold =        9;
        $charSolRenewalIntake =         8;

        // CTE Workfow states
        $cteInitialDateStateHold =      15;
        $cteInitial1023Hold =           16;
        $cteInitialIRSLetterHold =      17;
        $cteInitialIntake =             8;

        // Get tag for the current year
        $contextTags = $db->getById('', $contextObj['tagged_with'], ['name' => true, 'tag' => true]);
        $yearTag = [];
        foreach ($contextTags as $i => $tag) {
            if (
                $tag['object_bp_type'] === 'system_tags'
                && is_string($tag['tag'])
                && strlen($tag['tag']) === 4
                && is_numeric($tag['tag'])
            ) {
                $yearTag = $tag;
            }
        }

        // Get open 990 projects for the year
        $current990IsCompleted = true;

        // Only necessary if the context obj is not a 990 (990 only triggers this on complete)
        if (
            !(
                $contextObj['object_bp_type'] === 'groups'
                && in_array($contextObj['type'], $nine90ProjectTypes)
            )
        ) {

            $incomplete990s = $db->where(
                'groups'
                , [
                    'status' => [
                        'type' => 'not_equal'
                        , 'value' => 'done'
                    ]
                    , 'type' => [
                        'type' => 'or'
                        , 'values' => $nine90ProjectTypes
                    ]
                    , 'tagged_with' => [$contextCompany['id'], $yearTag['id']]
                ]
            );
            if (!empty($incomplete990s)) {
                $current990IsCompleted = false;
            }
            $incomplete990s = null;

        }

        // Get the relevant records
        // Get the core demo record for the company
        $coreDemo = $db->where(
            $coreDemoKey
            , ['tagged_with' => [$contextCompany['id']]]
        )[0];

        // Get the project
        $charSolInitial = $db->where(
            'groups'
            , [
                'tagged_with' =>    [$contextCompany['id'], $yearTag['id']]
                , 'type' =>         2051672
            ]
        )[0];

        /// Get the CTE project
        $cteInitial = $db->where(
            'groups'
            , [
                'tagged_with' => [$contextCompany['id']]
                , 'type' => 5293248
            ]
        )[0];
        
        // Update the CharSol Renewal based on $current990IsCompleted
        if (empty($charSolInitial)) {

            $charSolRenewal = $db->where(
                'groups'
                , [
                    'tagged_with' =>    [$contextCompany['id'], $yearTag['id']]
                    , 'type' =>         $charSolRenewalTypeId
                ]
            )[0];

            if (!$current990IsCompleted) {
                
                if ($charSolRenewal['state'] !== $charSolRenewal990Hold) {

                    // Move the Renewal CharSol to 990 Hold
                    $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
                    $stateChngUpds = $db->update(
                        'groups'
                        , [
                            'id' =>                 $charSolRenewal['id']
                            , 'state' =>            $charSolRenewal990Hold
                            , 'status' => 			'actionNeeded'
                            , 'state_updated_on' => $now->format('Y-m-d H:i:s')
                        ]
                    );

                    // Return message
                    return [
                        'msg' => '<strong>'. $charSolRenewal['name'] .'</strong> moved to new status.'
                    ];

                } else {

                    // Return message
                    return [
                        'msg' => ''
                    ];

                }

            } else {
                
                // Move the Renewal CharSol to 990 Intake
                if ($charSolRenewal['state'] !== $charSolRenewalIntake) {

                    // Move the Renewal CharSol to 990 Hold
                    $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
                    $stateChngUpds = $db->update(
                        'groups'
                        , [
                            'id' =>                 $charSolRenewal['id']
                            , 'state' =>            $charSolRenewalIntake
                            , 'status' => 			'actionNeeded'
                            , 'state_updated_on' => $now->format('Y-m-d H:i:s')
                        ]
                    );

                    // Return message
                    return [
                        'msg' => '<strong>'. $charSolRenewal['name'] .'</strong> moved to new status.'
                    ];

                } else {

                    // Return message
                    return [
                        'msg' => ''
                    ];

                }

            }

        }

        // Match the state supplemental's State of Inc. value to the core 
        // demo's State of Inc. value
        if ($coreDemo) {
            //!TODO: Match the state of inc/date of inc vals on supplemental to 
            // the core demo
        }

        // If State of Inc. or Date of Inc. is not set, do nothing
        if (
            FGSetCharsolHoldsStateOfIncIsSet($coreDemo, $coreDemoStateOfIncKey)
            && FGSetCharsolHoldsDateOfIncIsSet($coreDemo, $coreDemoDateOfIncKey)
        ) {

            // Update the hold status on the project/hold record
            // Set the return message
            switch (FGSetCharsolHoldsGetStateGroup($coreDemo, $coreDemoStateOfIncKey)) {

                case 'A':
                    // Move the Initial CharSol to Intake or 990 Hold
                    $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));

                    if ($current990IsCompleted) {
                        $newState = $charSolInitialIntake;
                    } else {
                        $newState = $charSolInitial990Hold;
                    }
                    $stateChngUpds = $db->update(
                        'groups'
                        , [
                            'id' =>                 $charSolInitial['id']
                            , 'state' =>            $newState
                            , 'status' => 			'actionNeeded'
                            , 'state_updated_on' => $now->format('Y-m-d H:i:s')
                        ]
                    );

                    $cteCACheck = $coreDemo[$coreDemoStateOfIncKey]['state'];
                    $cteOptions = [
                        'id' =>                 $cteInitial['id']
                        , 'state' =>            $cteInitialIntake
                        , 'status' =>           'actionNeeded'
                        , 'state_updated_on' => $now->format('Y-m-d H:i:s')  
                    ];


                    if ( $cteCACheck == 'CA' ) {

                        $cteOptions['state'] = $cteInitialIRSLetterHold;
                        $cteOptions['status'] = 'onHold';

                    }

                    /// Move the Initial CTE to intake
                    $cteStateChngUpds = $db->update(
                        'groups'
                        , $cteOptions
                    );
                    
                    break;

                case 'B':
                    $Letter1023 = $db->where(
                        $Letter1023Key
                        , ['tagged_with' => [$contextCompany['id']]]
                    )[0];

                    if (empty($Letter1023)) {
                        return false;
                    } elseif (
                        is_array($Letter1023)
                        && $Letter1023['status'] === 'done'
                    ) {

                        // Move the Initial CharSol to Intake or 990 Hold state
                        $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
                        if ($current990IsCompleted) {
                            $newState = $charSolInitialIntake;
                        } else {
                            $newState = $charSolInitial990Hold;
                        }
                        
                        $stateChngUpds = $db->update(
                            'groups'
                            , [
                                'id' =>                 $charSolInitial['id']
                                , 'state' =>            $newState
                                , 'status' => 			'actionNeeded'
                                , 'state_updated_on' => $now->format('Y-m-d H:i:s')
                            ]
                        );

                        /// Move the Initial CTE to intake
                        $cteStateChngUpds = $db->update(
                            'groups'
                            , [
                                'id' =>                 $cteInitial['id']
                                , 'state' =>            $cteInitialIntake
                                , 'status' =>           'actionNeeded'
                                , 'state_updated_on' => $now->format('Y-m-d H:i:s')  
                            ]
                        );

                    } else {

                        // Move the Initial CharSol to 1023
                        $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
                        $stateChngUpds = $db->update(
                            'groups'
                            , [
                                'id' =>                 $charSolInitial['id']
                                , 'state' =>            $charSolInitial1023Hold
                                , 'status' => 			'onHold'
                                , 'state_updated_on' => $now->format('Y-m-d H:i:s')
                            ]
                        );

                        /// Move the Initial CTE to 1023
                        $cteStateChngUpds = $db->update(
                            'groups'
                            , [
                                'id' =>                 $cteInitial['id']
                                , 'state' =>            $cteInitial1023Hold
                                , 'status' =>           'onHold'
                                , 'state_updated_on' => $now->format('Y-m-d H:i:s')  
                            ]
                        );
                        
                    }
                    break;

                case 'C':
                    $IRSLetter = $db->where(
                        $IRSLetterKey
                        , ['tagged_with' => [$contextCompany['id']]]
                    )[0];
 
                    if (empty($IRSLetter)) {

                        $IRSLetterDefaults['name'] = $contextCompany['name']. ' IRS Determination Letter';
                        $IRSLetterDefaults['parent'] = $contextCompany['id'];
                        $IRSLetterDefaults['_4'] = $coreDemo['_2'];
                        $IRSLetterDefaults['tagged_with'] = array_unique( 
                            array_merge($charSolInitial['tagged_with'], $cteInitial['tagged_with']) 
                        );

                        $newIRSLetter = $db->create(
                            $IRSLetterKey
                            , $IRSLetterDefaults
                        );

                        return false;
                    } elseif (
                        is_array($IRSLetter)
                        && $IRSLetter['status'] === 'done'
                    ) {

                        // Move the Initial CharSol to Intake or 990 Hold state
                        $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
                        if ($current990IsCompleted) {
                            $newState = $charSolInitialIntake;
                        } else {
                            $newState = $charSolInitial990Hold;
                        }
                        $stateChngUpds = $db->update(
                            'groups'
                            , [
                                'id' =>                 $charSolInitial['id']
                                , 'state' =>            $newState
                                , 'status' => 			'actionNeeded'
                                , 'state_updated_on' => $now->format('Y-m-d H:i:s')
                            ]
                        );

                        /// Move the Initial CTE to intake
                        $cteStateChngUpds = $db->update(
                            'groups'
                            , [
                                'id' =>                 $cteInitial['id']
                                , 'state' =>            $cteInitialIntake
                                , 'status' =>           'actionNeeded'
                                , 'state_updated_on' => $now->format('Y-m-d H:i:s')  
                            ]
                        );

                    } else {

                        // Move the Initial CharSol to IRS Letter Hold
                        $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
                        $stateChngUpds = $db->update(
                            'groups'
                            , [
                                'id' =>                 $charSolInitial['id']
                                , 'state' =>            $charSolInitialIRSLetterHold
                                , 'status' => 			'onHold'
                                , 'state_updated_on' => $now->format('Y-m-d H:i:s')
                            ]
                        );

                        /// Move the Initial CTE to IRS Letter Hold
                        $cteStateChngUpds = $db->update(
                            'groups'
                            , [
                                'id' =>                 $cteInitial['id']
                                , 'state' =>            $cteInitialIRSLetterHold
                                , 'status' =>           'onHold'
                                , 'state_updated_on' => $now->format('Y-m-d H:i:s')  
                            ]
                        );
                        
                    }
                    break;

                default:
                    $updateHoldStatus = false;
                    break;

            }

        } else {

            // Move the Initial CharSol to IRS Letter Hold
            $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
            $stateChngUpds = $db->update(
                'groups'
                , [
                    'id' =>                 $charSolInitial['id']
                    , 'state' =>            $charSolInitialDateStateHold
                    , 'status' => 			'onHold'
                    , 'state_updated_on' => $now->format('Y-m-d H:i:s')
                ]
            );

            /// Move the Initial CTE to Date/State of Inc set Hold
            $cteStateChngUpds = $db->update(
                'groups'
                , [
                    'id' =>                 $cteInitial['id']
                    , 'state' =>            $cteInitialDateStateHold
                    , 'status' =>           'onHold'
                    , 'state_updated_on' => $now->format('Y-m-d H:i:s')  
                ]
            );

            // Do nothing, just return message
            return [
				'msg' => '<strong>'. $charSolProj['name'] .'</strong> must have State of Inc. and Date of Inc. fields on the Core Demographics record set in order to continue..'
			];

        }

        if ($updateHoldStatus) {

			// Return message
			return [
				'msg' => '<strong>'. $charSolProj['name'] .'</strong> moved to new status.'
			];

		}

		return false;

	}
];

?>
