<?php

return [
	'name' 		=> 'createFG360Consultation'
	, 'process'	=> function ($setup, $run) {

        $objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$setup = 	$setup['setup'];

        // Core Service keys
        $taxYearKey = '_1';
		$mainContactKey = '_11';

		// Client Service keys
		$servicesBpKey = '#HDIjlE';
		$servicesYearKey = '_4';
		$servicesStateOfIncKey = '_5';

        // Don't allow creation without a tax year set on the core service
		if (empty($obj[$taxYearKey])) {

			return [
				'msg' => 'A <strong>Tax Year</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		} else {

            // // Check for the tag for the specific year
            $yearTag = $objs->where(
                'system_tags'
                , [
                    'tag' => strval($obj[$taxYearKey])
                ]
                , ''
                , [
                    'tag' => true
                ]
            )[0];

            // // Create a tag for the year if it does not exist
            if (empty($yearTag)) {
    
                $yearTag = $objs->create(
                    'system_tags'
                    , [
                        'name' => 		strval($obj[$taxYearKey])
                        , 'tag' => 		strval($obj[$taxYearKey])
                        , 'color' => 	'black'
                    ]
                    , 0
                    , [
                        'tag' => true
                    ]
                );
    
            }
            
        }

        /// Don't allow creation without a Main Contact set on the core service
		if ( empty($obj[$mainContactKey]) ) {

			return [
				'msg' => 'A <strong>Main Contact</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		} else {

            $mainContact = $objs->getById(
                'contacts'
                , $obj[$mainContactKey]
                , [
                    'name' => 	true
                    , 'fname' => 	true
                    , 'lname' => 	true
                    , 'company' => true
                ]
            );
            $mainContactCompany = $mainContact['company'];

        }

        $consultProject = $objs->where(
            'groups'
            , [
                'parent' => $mainContactCompany['id']
                ///FG360 - Consulting Block Project Type
                , 'type' => 10037056
                , 'category' => 10292832
            ]
            , ''
            , [
                'name' => true
            ]
        )[0];

        ///IF there is not a previously created Membership project, then create one ELSE use already made project
        if ( empty($consultProject) ) {

            ///Compliance Team id: 1652258 & Formation Team id:6077227
            $tagged_withDefaults = [$yearTag['id'], $mainContact['id'],  $mainContactCompany['id'], 1652258, 6077227];

            $projectTemplate = array();
            /// FG 360 Consultation Project Template (Production - FG)
            $projectTemplate = $objs->getById(
                'groups'
                , 10294717
            );

            $projectTemplate['tagged_with'] = array_merge($projectTemplate['tagged_with'], $tagged_withDefaults);
            $projectTemplate['status'] = 'open';
            $projectTemplate['shared_with'] = [ $mainContact['id'] ];

            $projectDefaults = array();
            $projectDefaults['name'] = 'FG 360 Consultation';
            ///Shared_with::Org Contacts
            $projectDefaults['shared_with'] = array( $mainContact['id'] );
            $projectDefaults['main_contact'] = $mainContact['id'];
            ///Template::Project Template ID
            $projectDefaults['data_source_id'] = $projectTemplate['id'];
            ///Parent::Organization
            $projectDefaults['parent'] = $mainContactCompany['id'];
            $projectDefaults['status'] = 'open';
            $projectDefaults['shared_with'] = [ $mainContact['id'] ];

            $consultProject = $objs->castFromTemplate($projectTemplate, null, $projectDefaults);

        } 

        $consultBlockTemplate = array();
        $consultBlockTemplate['tagged_with'] = $consultProject['tagged_with'];
        // $consultBlockTemplate['tagged_with'] = array_push($consultBlockTemplate['tagged_with'], $consultProject['id']);
        $consultBlockTemplate['tagged_with'] = array_merge($consultBlockTemplate['tagged_with'], array($consultProject['id']));
        $consultBlockTemplate['parent'] = $consultProject['id'];
        $consultBlockTemplate['shared_with'] = [ $mainContact['id'] ];



		$consultBlockTemplate['name'] = 'FG 360 Consultation Block';
 
		$consultBlock = $objs->create('#xmWR7v', $consultBlockTemplate);

		// Return message
		return [
			'msg' => '<strong>'. $consultBlock['name'] .'</strong> <i> consultation block </i> created.'
		];
		
	}
];

?>