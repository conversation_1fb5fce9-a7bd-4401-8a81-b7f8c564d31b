<?php

return [
	'name' 		=> 'FGcreateAbatementProject'
	, 'process'	=> function ($setup, $run) {
				
		$objs = 	$setup['objs'];
		$obj = 	$setup['old'];
		$new = 	$setup['new'];
		$setup = 	$setup['setup'];
		$opts = 	$setup['options'];
		$tagsFromContext = [
			'tagged_with' => 	[]
			, 'shared_with' => 	[]
		];

		$projectType = "Abatement";
		$templateId = 0;
		$projectName = '';

		// Core Service keys
		$taxYearKey = '_1';
		$stateOfIncKey = '_18';
		$mainContactKey = '_11';
		$abatementKey = '_22';


		// Client Service keys
		$servicesBpKey = '#HDIjlE';
		$servicesYearKey = '_4';
		$servicesStateOfIncKey = '_5';

		// Don't allow creation without a tax year set on the core service
		if (empty($obj[$taxYearKey])) {

			return [
				'msg' => 'A <strong>Tax Year</strong> must be set on the Core Service to generate an Abatement Project.'
			];

		}

		/// Don't allow creation without a Main Contact set on the core service
		if (
			empty($obj[$mainContactKey])
		) {

			return [
				'msg' => 'A <strong>Main Contact</strong> must be set on the Core Service to generate an Abatement Project.'
			];

		}		

		///FG Abatement Project Template Id 5207217
		$templateId = 5207217; 
		$projectName = $obj[$taxYearKey] .' Abatement';
		
		///Client Service Abatement Subset
		$servicesBpKey = '#HDIjlE.n1WVm5';

		$template = $objs->getById(
			'groups'
			, $templateId
		);

		$mainContact = $objs->getById(
			'contacts'
			, $obj[$mainContactKey]
			, [
				'name' => 	true
				, 'fname' => 	true
				, 'lname' => 	true
				, 'company' => true
			]
		);
		$mainContactCompany = $mainContact['company']['id'];

		
		$defaults = array();
							
		if ($template) {
			
			$defaults['name'] = $projectName;
			// Set the parent property
			$defaults['parent'] = [ 'id' => $mainContactCompany ];
			
			if ($options['passOnParent'] === true) {
				$defaults['parent'] = $obj['parent'];
			}
								
			$template['main_contact'] = $mainContact['id'];
			$template['shared_with'] = [$mainContact['id']];

			$template['status'] = 'open';

			// Check for the tag for the specific year
			$yearTag = $objs->where(
				'system_tags'
				, [
					'tag' => strval($obj[$taxYearKey])
				]
				, ''
				, [
					'tag' => true
				]
			)[0];

			// Create a tag for the year if it does not exist
			if (empty($yearTag)) {

				$yearTag = $objs->create(
					'system_tags'
					, [
						'name' => 		strval($obj[$taxYearKey])
						, 'tag' => 		strval($obj[$taxYearKey])
						, 'color' => 	'black'
					]
					, 0
					, [
						'tag' => true
					]
				);

			}

			// Pass down tags from context
			$template['tagged_with'] = array_merge($obj['tagged_with'], $template['tagged_with']);
			$template['tagged_with'] = array_unique($template['tagged_with']);
			///TEST DATA system tag 2465644
			array_push($template['tagged_with'], 1636580, 1763496, $mainContact['id'], $mainContactCompany, $yearTag['id'], $obj['id']);
			
			$defaults['tagged_with'] = $template['tagged_with'];

			$newProj = $objs->castFromTemplate($template, null, $defaults);
 
 			// Create the service
			$serviceTags = array_unique($newProj['tagged_with']);
			array_push($serviceTags, $newProj['id']);
			
			$serviceObj = $objs->create(
				$servicesBpKey
				, [
					'name' => 					$projectName
					, 'parent' => 					$newProj['id']
					, 'tagged_with' => 				$serviceTags
					, 'shared_with' => 				[$obj[$mainContactKey]]
					, $servicesStateOfIncKey => 		$obj[$stateOfIncKey]
					, $servicesYearKey => 			$obj[$taxYearKey]
				]
			);

			// Return message
			return [
				'msg' => '<strong>'. $newProj['name'] .'</strong> <i>Project</i> created.'
			];
			
		}
		
		return false;
		
	}
];

?>