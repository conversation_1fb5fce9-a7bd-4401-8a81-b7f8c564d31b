<?php

return [
	'name' 		=> 'move'
// 	, 'process'	=> function ($objs, $old, $new, $setup, $run) {
	, 'process'	=> function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['new'];
		$setup = 	$setup['setup'];
		
		// Parse input
		$userID = intval($setup['options']['userID']);
		$keepInMyStuff = intval($setup['options']['keepInMyStuff']);
		$newParentId = intval($setup['options']['newParent']);
		
		$newParent = $objs->getById(
			''
			, $newParentId
			, [
				'name' => true
			]
		);
		$oldParent = $obj['parent'];
		
		// Validate input
		if (
			$obj
			&& $newParentId
			&& !empty($newParent)
		) {
			
			// Set new parent
			$updated = $objs->update(
				$obj['object_bp_type']
				, [
					'id' => $obj['id']
					, 'parent' => $newParentId
				]
			);
			
			// Tag w/parent
			$updated = $objs->tagObject(
				$obj['id']
				, $newParentId
				, 'tagged_with'
			);
			
			// Remove old parent from tag set, if it is there
			if (in_array($oldParent, $updated)) {
				
				$updated = $objs->removeTagFromObject(
					$obj['id']
					, $oldParent
					, 'tagged_with'
				);
				
			}
			
			// Add tag for user, if set
			if ( !isEmpty($userID) && !$keepInMyStuff ) {
					
				$updated = $objs->removeTagFromObject(
					$obj['id']
					, $userID
					, 'tagged_with'
				);
				
			}
			
			$update = [
				'id' => 			$obj['id']
				, 'name' => 		$obj['name']
				, 'parent' => 		$newParent
				, 'tagged_with' => 	$updated
				, '_forceUpdate' => true
			];
			
			return [
				'applyTags' => 	[
					'response' => 	$updated
				]
				, 'msg' =>		$obj['name'] .' moved to '. $newParent['name']
				, 'update' => 	$update
			];
			
		} else {
			
			return false;
			
		}
		
	}
];

?>