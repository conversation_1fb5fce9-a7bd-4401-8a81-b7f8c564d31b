<?php

return [
	'name' 		=> 'createTasks'
	, 'process'	=> function ($setup, $run) {
		
		$objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];

		// find tags to apply, based on setup
		$tags = [];
		$templates = $objs->where(
			'groups'
			, [
				'parent' => 			intval($setup['id'])
				, 'group_type' => 	'Task'
			]
		);
		$defaults = [];
		
		if (
			is_array($templates)
			&& count($templates) > 0
		) {
			
			foreach ($templates as $i => $template) {
				
				$defaults = [];
				
				$template['parent'] = [ 'id' => $obj['id'] ];
				array_push($template['tagged_with'], $obj['id']);
				
				if ($template['tagged_with']) {
					$defaults['tagged_with'] = $template['tagged_with'];
				}
				
				$objs->castFromTemplate($template, null, $defaults);
				
			}
			
			// return message
			return ['msg' => count($templates) .' task(s) created.'];
			
		}
		
		return false;
		
	}
];

?>