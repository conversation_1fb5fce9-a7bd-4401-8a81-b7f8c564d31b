<?php

return [
	'name' 		=> 'FGcreateCTEProject'
	, 'process'	=> function ($setup, $run) {

        $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));

        $objs = 	$setup['objs'];
		$obj = 		$setup['old'];
		$new = 		$setup['new'];
		$setup = 	$setup['setup'];
		$opts = 	$setup['options'];
		$tagsFromContext = [
			'tagged_with' => 	$obj['tagged_with']
			, 'shared_with' => 	$obj['shared_with']
		];

        // $obj = $objs->getById(
        //     ''
        //     , 7387494
        // );

        ///If Context (Core Service Obj) is type:'Surestart' then proceed with Hold Process
        ///[] Generate a Hold Status Record
        ///[] Default new CTE project to Hold State
        $generateHoldStatus = false;

        ///Core Service subset type for SureStart - #3KelnN.z3st2d
        if ($obj['object_bp_type'] == '#3KelnN.z3st2d'){
            $generateHoldStatus = true;
        }

        ///CTE Template Id - 5346263
		$templateId = 5346263;

		// Core services field keys
		$taxYearKey = '_1';
		$stateOfIncKey = '_18';
		$mainContactKey = '_11';

		// CTE Services BP - #HDIjlE.YRhgx6
		$servicesBpKey = '#HDIjlE.YRhgx6';
		$servicesYearKey = '_4';
		$servicesStateOfIncKey = '_5';

		// Don't allow creation without a tax year set on the core service
		if ( empty($obj[$taxYearKey]) ) {

			return [
				'msg' => 'A <strong>Tax Year</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		}

		/// Don't allow creation without a Main Contact set on the core service
		if ( empty($obj[$mainContactKey]) ) {

			return [
				'msg' => 'A <strong>Main Contact</strong> must be set on the Core Service to generate Charitable Solicitation projects.'
			];

		}

        ///set main contact using incoming Contact Field
        $mainContact = $objs->getById(
            'contacts'
            , $obj[$mainContactKey]
            , [
                'company' => 'id'
            ]
        );

        ///set contact's company
        $mainCompany = $mainContact['company'];

        // Check for the tag for the specific year
        $yearTag = $objs->where(
            'system_tags'
            , [
                'tag' => strval($obj[$taxYearKey])
            ]
            , ''
            , [
                'tag' => true
            ]
        )[0];

        // Create a tag for the year if it does not exist
        if (empty($yearTag)) {

            $yearTag = $objs->create(
                'system_tags'
                , [
                    'name' => 		strval($obj[$taxYearKey])
                    , 'tag' => 		strval($obj[$taxYearKey])
                    , 'color' => 	'black'
                ]
                , 0
                , [
                    'tag' => true
                ]
            );

        }

		$template = $objs->getById(
			'groups'
			, $templateId
		);

		// Setting project dates
		if(!empty($obj['start_date'])) {

			$templateDateCreated = (new DateTime($template['date_created']))->format('U');
			$projectStartDate = (new DateTime($obj['start_date']))->format('U');
			$projectEndDate = (new DateTime($obj['end_date']))->format('U');

			$dateDiff = intval($projectStartDate) - intval($templateDateCreated);
			$startEndDateDiff = intval($projectEndDate) - intval($projectStartDate);

			$newProjectStartDate = (new DateTime())->format('U') + intval($dateDiff);
			$newProjectEndDate = intval($newProjectStartDate) + intval($startEndDateDiff);

			$defaults['start_date'] = date('Y-M-d', $newProjectStartDate);
			$defaults['end_date'] = date('Y-M-d', $newProjectEndDate);

		}

        /// set Project Name
        $defaults['name'] = $obj[$taxYearKey] .' CTE';

        // Set the parent property
        $defaults['parent'] = $mainCompany;

        ///set the main contact
        $defaults['main_contact'] = $mainContact['id'];

        // Pass down tags from context
        $defaults['tagged_with'] = array_merge(
            $template['tagged_with']
            , $tagsFromContext['tagged_with']
        );
        $defaults['shared_with'] = array_merge(
            $template['shared_with']
            , $tagsFromContext['shared_with']
        );
        $defaults['tagged_with'] = array_unique($defaults['tagged_with']);
        $defaults['shared_with'] = array_unique($defaults['shared_with']);
        $defaults['status'] = 'open';
        $defaults['state_updated_on'] = $now->format('Y-m-d H:i:s');

        array_push($defaults['tagged_with'], $obj['id']);

        ///default state from the template is {state: 18, status: 'open'}
        if ( $generateHoldStatus ) {
            ///Set default state for new project - Hold State is 17
            $defaults['state'] = 15;
            $defaults['status'] = 'onHold';

        }

        $template['tagged_with'] = $defaults['tagged_with'];
        $template['shared_with'] = $defaults['shared_with'];
        array_merge($template['tagged_with'], $obj['tagged_with']);
        array_push($template['tagged_with'], $yearTag['id'], $mainContact['id'], $mainCompany);

        $newProj = $objs->castFromTemplate($template, null, $defaults);

        // Create the Client Service            
        $serviceTags = $newProj['tagged_with'];
        array_push($serviceTags, $newProj['id']);
        $serviceTags = array_unique($serviceTags);

        $serviceObj = $objs->create(
            $servicesBpKey
            , [
                'name' => 					    $obj[$taxYearKey] .' CTE '
                , 'parent' => 					$newProj['id']
                , 'tagged_with' => 				$serviceTags
                , 'shared_with' => 				[$obj[$mainContactKey]]
                , $servicesStateOfIncKey => 	$obj[$stateOfIncKey]
                , $servicesYearKey => 			$obj[$taxYearKey]
            ]
        );

        ///Create Default Univeral Intakes
        $universalSet = $objs->where(
            'entity_type'
            , [
                'name' => 'CTE_Universal SCTE'
            ]
            , ''
            , [
                'bp_name' => true
            ]
        )[0];

        $universalOneToCreate = [
            'name' => 			'01 SCTE Universal Intake'
            , 'parent' => 			$newProj['id']
            , 'tagged_with' => 		$newProj['tagged_with']
            , 'shared_with' => 		[$obj[$mainContactKey]]
            , 'object_bp_type' => 	'#'. $universalSet['bp_name']
        ];

        array_push(
            $universalOneToCreate['tagged_with']
            , $newProj['id']
            , $yearTag['id']
            , $mainContact['id']
            , $mainCompany
        );

        $universalOneToCreate['tagged_with'] = array_unique(
            $universalOneToCreate['tagged_with']
        );

        $uniOne = $objs->create(
            $universalOneToCreate['object_bp_type']
            , $universalOneToCreate
        );

        $setup['options']['coreserviceId'] = $obj['id'];
        $setup['options']['projectId'] = $proj['id'];
        $setup['options']['serviceTags'] = $uniOne['tagged_with'];

        $run(
            'FGcreateCTEActionItems'
            , $setup
            , $run
        );

        // Return message
        return [
            'msg' => '<strong>'. $newProj['name'] .'</strong> <i>Project</i> created.'
        ];

	}
];

?>
