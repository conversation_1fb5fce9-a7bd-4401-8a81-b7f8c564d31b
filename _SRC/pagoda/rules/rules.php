<?php

class Rules {
	
	// Library
	private $actions = 		[];
	private $conditions = 	[];
	
	// State
	public 	$appConfig = 	[];
	public 	$currentLayer = 0;
	private $actionsRun = 	[];
	
	public function __construct ($appConfig = null) 
	{
		
		$rulesDir = APP_ROOT .'rules/';
		$tmp = null;
		
		$this->appConfig = $appConfig;

		// collect conditions
		$dir = new DirectoryIterator(
			$rulesDir .'conditions'
		);
		
		foreach ($dir as $fileinfo) {
			
			if (
				!$fileinfo->isDot()
				&& $fileinfo->getExtension() === 'php'
			) {

				$tmp = require_once(
						$rulesDir .'conditions' .'/'. $fileinfo->getFilename()
					);
				
				$this->conditions[$tmp['name']] = $tmp['process'];
				
			}
		}
		
		// collect actions
		$dir = new DirectoryIterator(
			$rulesDir .'actions'
		);
		
		foreach ($dir as $fileinfo) {
			
			if (
				!$fileinfo->isDot()
				&& $fileinfo->getExtension() === 'php'
			) {
				
				$tmp = require_once(
						$rulesDir .'actions' .'/'. $fileinfo->getFilename()
					);
				
				$this->actions[$tmp['name']] = $tmp['process'];
				
			}
			
		}

		return true;
		
	}

	private function crawlSteps ($step, $old, $new, $objs, $memo, $verbose = false, $trigger = false) 
	{

		$process = null;
		if (!array_key_exists('messages', $memo)) {

			$memo['messages'] = [];
			$memo['updates'] = [];
			$memo['notifications'] = [];
			$memo['run'] = true;
			$memo['updated'] = $new;
			$memo['fieldErrors'] = [];
			
		}
		if (!array_key_exists('memo', $memo)) {
			
			$memo['memo'] = null;
			
		}
		
		// Run actions
		if (is_array($step)) {
			foreach ($step as $key => $processConfig) {
				
				if (is_int($key)) {
					$word = $processConfig['type'];
				} else {
					$word = $key;
				}
				
				if (
					$word !== 	'_if'
					&& $word !== '_do'
					&& array_key_exists(
						$word
						, $this->actions
					)
				) {
					
					// Run process.
					if (
						$processConfig['notification']
					) {
						$processConfig['object_bp_type'] = 'event_type';
					}
					
					$process = $this->actions[$word];
					$updates = $process(
						[
							'objs' => $objs
							, 'old' => $old
							, 'new' => $new
							, 'setup' => $processConfig
							, 'trigger' => $trigger
							, 'appConfig' => $this->appConfig
							, 'memo' => 	$memo['memo']
						]
						, function ($action, $processConfig) use ($objs, $old, $new) {
							
							if (array_key_exists($action, $this->actions)) {
								
								$proc = $this->actions[$action];

								return $proc(
									[
										'objs' => $objs
										, 'old' => $old
										, 'new' => $new
										, 'setup' => $processConfig
										, 'appConfig' => $this->appConfig
										, 'memo' => $memo['memo']
									]
									, function(){}
								);
								
							} 
							
							return false;
							
						}
					);

					if ($updates['memo']) {
						
						$memo['memo'] = $updates['memo'];
						
					}

					// Chain to next actions/conditions
					if (
						$processConfig
						&& $processConfig['next']
						&& $processConfig['next']['pointer']
						&& $processConfig['next']['type']
					) {

						// Get next items
						$nextUp = $objs->getById(
							$processConfig['next']['type']
							, $processConfig['next']['pointer']
						);

						// Run next items, and update the memo
						$this->currentLayer++;
						$memo = $this->crawlSteps(
							[$nextUp]
							, $old
							, $new
							, $objs
							, $memo
							, true
							, $trigger
						);
						$this->currentLayer--;

					}
					
					// !TODO: If notification is supplied here, mark the notification
					// as complete/read.
					array_push($memo['messages'], $updates['msg']);
					if ($updates['update']) {
						
						// Update the response
						if (is_array($updates['update'])) {
							foreach ($updates['update'] as $changedValKey => $changedVal) {
								
								$new[$changedValKey] = $changedVal;
								$memo['updated'] = $new;

							}
						}
						
						array_push($memo['updates'], $updates['update']);
						
					}
					
					if ($updates['notifications']) {
						$memo['notifications'] = array_merge($memo['notifications'], $updates['notifications']);
					}
					
				} else {

					$shouldContinue = true;
					
					// Run conditions
					$checks = $step['_if'];
					$conditionObj = false;
					if (
						empty($checks)
						&& is_array($processConfig)
						&& $processConfig['object_bp_type'] === 'condition'
						&& is_array($processConfig['conditions'])
					) {

						$conditionObj = $processConfig;
						$checks = [];
						if (is_array($processConfig['conditions'])) {
							foreach ($processConfig['conditions'] as $c) {
								
								array_push($checks, $c);
								
							}
						}
						
					}

					if (!empty($checks)) {
						
						if (is_array($checks)) {
							foreach ($checks as $key => $processConfig) {
								
								if (is_int($key)) {
									$word = $processConfig['type'];
								} else {
									$word = $key;
								}

								if (
									$word !== '_do' 
									&& array_key_exists(
										$word
										, $this->conditions
									)
								) {

									$process = $this->conditions[$word];
									$updates = $process(
											[
												'objs' => $objs
												, 'old' => $old
												, 'new' => $new
												, 'setup' => $processConfig
												, 'appConfig' => $this->appConfig
												, 'trigger' => $trigger
											]
											, function(){}
										);

									array_push($memo['fieldErrors'], $updates['fieldError']);
									array_push($memo['messages'], $updates['msg']);
									
									// Condition test failed
									if (
										!$updates
										|| !$updates['continue']
									) {
										
										$memo['run'] = false;
										$shouldContinue = false;
									
									} 
									
								}
								
							}
						}
						
						if ($shouldContinue) {
							
							if (
								array_key_exists('_do', $checks)
								&& is_array($checks['_do'])
							) {

								$memo = $this->crawlSteps($checks['_do'], $old, $new, $objs, $memo, true, $trigger);

							} elseif (
								$conditionObj
								&& $conditionObj['object_bp_type'] === 'condition'
								&& $conditionObj['next']
								&& $conditionObj['next']['pointer']
								&& $conditionObj['next']['type']
							) {

								// Run next items, and update the memo
								$this->currentLayer--;
								switch ($conditionObj['next']['type']) {
									
									case 'event_type':
									case 'condition':
										$nextUp = $objs->getById(
											$conditionObj['next']['type']
											, $conditionObj['next']['pointer']
										);
										$memo = $this->crawlSteps(
											[$nextUp]
											, $old
											, $new
											, $objs
											, $memo
											, true
											, $trigger
										);
										
									break;
										
										case 'state':
											$stateChanged = $objs->update(
												$new['object_bp_type']
												, [
													'id' => $new['id']
													, $trigger['state-field'] => $conditionObj['next']['pointer']
												]
											);
											$memo['updated'][$trigger['state-field']] = $stateChanged[$trigger['state-field']];
											array_push(
												$memo['messages']
												, $obj['name'] .' re-routed in workflow.'
											);
											break;
											
								}
								$this->currentLayer++;
										
							}
								
						} else {
							
							// Chain to next actions/conditions
							if (
								$conditionObj
								&& $conditionObj['object_bp_type'] === 'condition'
								&& $conditionObj['else_do']
								&& $conditionObj['else_do']['pointer']
								&& $conditionObj['else_do']['type']
							) {

								// Run next items, and update the memo
								$this->currentLayer++;
								switch ($conditionObj['else_do']['type']) {
									
									case 'event_type':
										case 'condition':
											$nextUp = $objs->getById(
												$conditionObj['else_do']['type']
												, $conditionObj['else_do']['pointer']
											);
											$memo = $this->crawlSteps(
											[$nextUp]
											, $old
											, $new
											, $objs
											, $memo
											, true
											, $trigger
										);
										
									break;
										
										case 'state':
											$stateChanged = $objs->update(
												$new['object_bp_type']
												, [
													'id' => $new['id']
													, $trigger['state-field'] => $conditionObj['else_do']['pointer']
												]
											);
											$memo['updated'][$trigger['state-field']] = $stateChanged[$trigger['state-field']];
											array_push(
												$memo['messages']
												, $obj['name'] .' re-routed in workflow.'
											);
											break;
											
								}
								$this->currentLayer--;

							}
						
						}

					}

				}
				
			}
		}

		if ($verbose) {
			
			return $memo;
			
		}
		
		return $new;
		
	}

	// user api
	
	public function process ($blueprint, $eventType, $old, $new, $objs) 
	{
		
		$memo = []; 

		// Iterate over fields
		if (is_array($blueprint)) {

			foreach ($blueprint as $key => $field) {
				
				if (
					array_key_exists('on', $field) 
					and is_array($field['on'][$eventType])
				) {
					
					// If value at the field has changed, 
					// execute the steps of the workflow.
					if (
						$old[$key] !== $new[$key]
						&& is_array($field['on'][$eventType])
						&& !empty($field['on'][$eventType])
					) {
						
						// Straight actions w/no config
						if (is_string($field['on'][$eventType][0])) {

							$memo[$key] = $this->crawlSteps(
								$field['on'][$eventType]
								, $old
								, $new
								, $objs
								, []
							);

						// Action objects
						} elseif (is_int($field['on'][$eventType][0])) {

							// Leading action/conditions in chains
							$actions = $objs->getById(
								'' 
								, $field['on'][$eventType]
							);
							$memo[$key] = $this->runSteps(
								$new
								, $actions
								, $objs
								, false
								, 'update'
							);

						}
						
					}

				}
				
			}
			
			return $memo;

		} else {
			
			return false;
			
		}
		
	}
	
	public function shouldProcess ($eventType, $field, &$state) 
	{

		if (
			array_key_exists('on', $field)
			and array_key_exists($eventType, $field['on'])
		) {
			$state['__shouldRunRules'] = true;
		}
		
		return $state;
		
	}
	
	public function runSteps ($obj, $steps, $objs, $verbose = false, $trigger = false) 
	{
		
		$toRun = [];
		$allowAction = true;
		
		// Only allow actions that have not already been run upstream.
		if (is_array($steps)) {
			foreach ($steps as $key => $step) {
				
				$allowAction = true;
				if (
					is_array($step)
					&& $step['object_bp_type'] === 'event_type'
				) {
					
					foreach ($this->actionsRun as $committed) {
						
						if (
							$committed['id'] == $step['id']
							&& $committed['layer'] != $this->currentLayer
						) {
							$allowAction = false;
						}
						
					}
					
					if ($allowAction) {
						$toRun[$key] = $step;
					}
					
				} else {
					
					$toRun[$key] = $step;
					
				}
				
			}
		}
		
		// Track actions being run.
		if (is_array($toRun)) {
			foreach ($toRun as $step) {
				
				if (
					is_array($step)
					&& $step['object_bp_type'] === 'event_type'
				) {
					array_push(
						$this->actionsRun
						, [
							'id' => 		$step['id']
							, 'layer' => 	$this->currentLayer
						]
					);
				}
				
			}
		}

		$this->currentLayer++;
		$ret = $this->crawlSteps(
			$toRun
			, $obj
			, $obj
			, $objs
			, []
			, $verbose
			, $trigger
		);
		$this->currentLayer--;
		return $ret;
		
	}

    public function setAppConfig ($appConfig = null) {

        if ($appConfig !== null && is_array($appConfig)) {

            $this->appConfig = $appConfig;

            return true;

        }

        return false;

    }
	
}

?>
