<?php 

return [
	'name' 		=> 'allAreCompleted'
	, 'process'	=> function ($setup) {
		
		$obj = $setup['new'];
		$objs = $setup['objs'];
		$opts = $setup['setup']['options'];

		$msg = '';
		$isPassed = false;

        // Get records of the given set in  the given context
        $tags = [$obj['id']];
        if (is_array($opts['context'])) {
            $tags = array_merge($opts['context']);
        }

        if (
            count(
                $objs->where(
                    '#.'
                    , [
                        'tagged_with' => $tags
                        , 'status' => [
                            'not_equal' => 'done'
                        ]
                    ]
                )
            ) === 0
        ) {
            $isPassed = true;
        }

		return [
			'continue' => 	$isPassed
		];
		
		return true;
		
	}
];

?>