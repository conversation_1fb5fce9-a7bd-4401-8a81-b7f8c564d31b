<?php 

return [
	'name' 		=> 'isTrue'
	, 'process'	=> function ($setup) {
		
		$obj = $setup['new'];
		$objs = $setup['objs'];
		$opts = $setup['setup']['options'];
		$msg = '';
		$pass = true;
		
		if ($obj[$opts['field']] !== true) {
			
			$pass = false;
			$msg = 'Not true!';
			
			$bp = $objs->getBlueprint($obj['object_bp_type'], false);
			$msg = $bp[$opts['field']]['name'] .' is not checked.';
			
		}
		
		return [
			'msg' => 		$msg
			, 'continue' => 	$pass
		];
		
		return true;
		
	}
];

?>