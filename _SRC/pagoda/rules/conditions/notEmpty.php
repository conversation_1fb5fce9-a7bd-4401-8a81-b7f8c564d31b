<?php 

return [
	'name' 		=> 'isSet'
	, 'process'	=> function ($setup) {
		
		$obj = $setup['new'];
		$objs = $setup['objs'];
		$opts = $setup['setup']['options'];
		$msg = '';
		$pass = true;
		
		if (empty($obj[$opts['field']])) {
			
			$pass = false;
			$msg = 'Not set!!';
			
			$bp = $objs->getBlueprint($obj['object_bp_type'], false);
			$msg = $bp[$opts['field']]['name'] .' is a required field.';
			
		}
		
		return [
			'msg' => 		$msg
			, 'fieldError' => $opts['field']
			, 'continue' => 	$pass
		];
		
		return true;
		
	}
];

?>
