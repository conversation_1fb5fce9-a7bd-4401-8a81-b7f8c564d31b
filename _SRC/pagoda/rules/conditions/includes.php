<?php 

return [
	'name' 		=> 'includes'
	, 'process'	=> function ($setup) {
		
		$obj = $setup['new'];
		$objs = $setup['objs'];
		$opts = $setup['setup']['options'];
		$msg = '';
		$pass = false;

        // Check that each value set in the condition check is in the 
        // relevant field.
        if (
            is_array($opts['value'])
            && is_array($obj[$opts['field']])
        ) {

            $pass = true;
            foreach ($opts['value'] as $val) {

                if (!in_array($val, $obj[$opts['field']])) {

                    $pass = false;

                }

            }

        }

		return [
			'msg' => 		    $msg
			, 'continue' => 	$pass
		];
		
		return true;
		
	}
];

?>