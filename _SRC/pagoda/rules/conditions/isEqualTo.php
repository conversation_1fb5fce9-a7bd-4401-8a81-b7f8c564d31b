<?php 

return [
	'name' 		=> 'isEqualTo'
	, 'process'	=> function ($setup) {
		
		$obj = $setup['new'];
		$objs = $setup['objs'];
		$opts = $setup['setup']['options'];
		$msg = '';
		$pass = true;

        if (
            $obj[$opts['field']] !== $opts['value']
            && $obj[$opts['field']] !== intval($opts['value'])
        ) {
			
			$pass = false;
			$msg = 'Not equal!';
			
			$bp = $objs->getBlueprint($obj['object_bp_type'], false);
			$msg = $bp[$opts['field']]['name'] .' is not equal to the required value.';
			
		}
		
		return [
			'msg' => 		    $msg
			, 'continue' => 	$pass
		];
		
		return true;
		
	}
];

?>