<?php 
	
	return [
		'name' 		=> 'isAvailable',
		'process'	=> function ($setup, $run) {
			
 			//error_reporting(E_ALL);
 			//ini_set('display_errors', '1');
 			
			$objs 	   = $setup['objs'];
			$old 	   = $setup['old']; 
			$new 	   = $setup['new'];
			$options   = $setup['setup'];
			$appConfig = $setup['appConfig'];
			
			$assignmentId = $options['assignment'];
			$date = $options['date'];
			$successfulMsg = $options['successfulMsg'];
			$unsuccessfulMsg = $options['unsuccessfulMsg'];
			
			if(is_numeric($assignmentId)) {
				
				$assignmentObj = $objs->getById(
					'', 
					$assignmentId, 
					[]
				);
				
			} else {
				
				// $assignmentId should be an object here
				$assignmentObj = $assignmentId;	
				
			}
//var_dump($assignmentObj);
			$assignments = $objs->where('groups', [
				'group_type' => 'Shift', 
				'user' => $new['id'],
				'start_date' => [
					'type' => 'between',
					'start' => (new DateTime($assignmentObj['start_date']))->format('U'),
					'end' => (new DateTime($assignmentObj['end_date']))->format('U')	
				]
			]);
			
			if (
				$assignments != null
				|| !empty($assignments)
			) {
				// Assignments overlapping current assignment are found.
				return [
					'continue' => false
					, 'msg' => $unsuccessfulMsg
				];
				
			} 
			
			$availabilityObj = null;
			
			$nonAvailabilityList = $objs->where('staff_availability_requests', [
				'staff' => $new['id']
				, 'request_type' => 'time_off'
				, 'status' => 'approved'
				, 'start_time' => [
					'type' => 'between',
					'start' => (new DateTime($assignmentObj['start_date']))->format('U'),
					'end' => (new DateTime($assignmentObj['end_date']))->format('U')
				]
			]);

			if (empty($nonAvailabilityList)) {

				// Get staff availability requests
				$availabilityList = $objs->where('staff_availability_requests', [
					'staff' => $new['id']
					, 'request_type' => 'time_on'
					, 'status' => 'approved'
					, 'start_time' => [
						'type' => 'between',
						'start' => (new DateTime($assignmentObj['start_date']))->format('U'),
						'end' => (new DateTime($assignmentObj['end_date']))->format('U')
					]
				]);
				
				foreach ($availabilityList as $index => $obj) {
				
					if ( 
					
						(new DateTime($obj['end_time']))->format('U') >= (new DateTime($assignmentObj['end_date']))->format('U')
						&& (new DateTime($obj['start_time']))->format('U') <= (new DateTime($assignmentObj['start_date']))->format('U')
					) {
						
						$availabilityObj = $obj;
						
					}
					
				}
				
				if (
					empty($availabilityList)
					|| $availabilityObj !== null
				) {
					// No availability objects are found.
					// Or A 'time on' availability object compatible with current assignment has been found.
					return [
						'continue' => true
						, 'msg' => $successfulMsg
					];	
					
				} 
				
			} else {

				return [
					'continue' => false
					, 'msg' => $unsuccessfulMsg
				];
				
			}
			
			return [
				'continue' => false
				, 'msg' => $unsuccessfulMsg
			];
			
		}
	];
	
?>