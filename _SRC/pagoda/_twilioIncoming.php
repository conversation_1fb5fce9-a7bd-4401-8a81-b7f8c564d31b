<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header('Access-Control-Allow-Origin: *');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );

require 'vendor/autoload.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';

require_once $_SERVER['DOCUMENT_ROOT'].'/app/_app/_config.php';
require_once APP_ROOT.'_twiml.php';
/*
var_dump('appconfig');
var_dump($appConfig);
*/
if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
}

if($_POST or 1==1){
		
	$instance = $_REQUEST['instance'];
	
	$objects = new Objects($pdo);
	$pgObjects = new pgObjects($pdo, $instance);
	$comms = new Comm($pgObjects, $appConfig);
	$cookies = new Cookies($pgObjects);
	$files = new FileApi($pgObjects, $_SERVER["DOCUMENT_ROOT"].'/_files/_instances/'.$instance);
	
	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
		
	if(!empty($_REQUEST['debug'])){
		
		error_reporting(E_ALL);
		ini_set('display_errors', '1');
		
	}else{
		
		error_reporting(0);
		
	}
	
	$conv = new TwimlConversation($app, $_POST, $instance);
	
	if(!$_REQUEST['conversation']){
		
		// initial contact with the caller
				
		switch($conv->callerType){
			
			case 'contact':
				
				$conv->sayHello();
				$conv->addSentance('You have reached Volts Software.');
				$conv->getKeypadEntry(
					'Press 1 to connect to your agent, '. $conv->caller['manager']['fname'] .' '. $conv->caller['manager']['lname'] .'.',
					1,
					'connect_to_manager',
					2);
				$conv->beginTree('connect_to_staff');
				
				break;
			
			case 'user':
			
				$conv->sayHello();
				$conv->placeOutgoingCall();
							
				break;
				
			default:
			
				// unknown caller
				// save the contact in the app instance
				$contact = [];
				$contact['type'] = 204507;
				$contact['company'] = 202336;
				
				$contact = $conv->app->createNewObject('contacts', $contact, false, 0);
				$contact['contact_info'] = array();
				
				// save phone number
				$phone = array(
					'type'=>202345,
					'title'=>'Phone',
					'object_id'=>$contact['id'],
					'object_type'=>'contacts',
					'is_primary'=>'yes',
					'info'=>$conv->from
				);
				
				$phone = $conv->app->createNewObject('contact_info', $phone, false, 0);
				
				$contact['contact_info'][] = $phone['id'];
				
				// save location if provided
				if($conv->req->FromCity){
					
					$location = array(
						'type'=>204508,
						'title'=>'Address',
						'object_id'=>$contact['id'],
						'object_type'=>'contacts',
						'is_primary'=>'yes',
						'street'=>'',
						'city'=>$conv->req->FromCity,
						'state'=>$conv->req->FromState,
						'zip'=>$conv->req->FromZip,
						'country'=>$conv->req->FromCountry
					);

					$location = $conv->app->createNewObject('contact_info', $location, false, 0);
					
					$contact['contact_info'][] = $location['id'];
														
				}

				$contact = $conv->app->updateObject(array('id'=>$contact['id'], 'contact_info'=>$contact['contact_info']), 'contacts', false, 0);
								
				// add tag(s)
				$tag = array(
					'type'=>'contacts',
					'type_id'=>$contact['id'],
					'tag'=>'Incoming Call'
				);
				
				$conv->app->addTagToObject($tag, false);
				
				$conv->sayHello();
				$conv->addSentance('You have reached Volts Software.');
				$conv->addSentance('Connecting.');
				$conv->forwardCall(4138966080, $conv->from);
			
		}
		
	}else{
		
		// ongoing conversation with the user
		
		switch($_REQUEST['conversation']){
			
			case 'connect_to_manager':
				
				$contact = $conv->app->getObjectsWhere('contacts', array('phone'=>$conv->from), false, 1)[0];
				$manager = $conv->app->getObjectById('staff', $contact['manager'], false);
				
				$conv->addSentance('Connecting you to '. $manager['fname'] .' '. $manager['lname'] .'.');
				$conv->forwardCall($manager['phone'], $this->req['From']);
				
				break;
				
			case 'connect_to_staff':
				
				$staff = $conv->app->getObjectsWhere('staff', array('pin'=>$conv->req['Digits']), false, 1)[0];
				
				$conv->addSentance('Connecting you to '. $staff['fname'] .' '. $staff['lname'] .'.');
				$conv->forwardCall($staff['phone'], $this->req['From']);
				
				break;

			case 'disconnect':
			
				$conv->say .= $this->hangUp();
			
				break;
			
			case 'outgoing_call':
				
				$conv->addSentance('Connecting.');
				$conv->forwardCall($conv->req['Digits'], $conv->req['To']);
			
				break;	
			
		}
		
	}
	
	$conv->speak();
	
}else{
	
	die();
	
}
	
?>