<?php

/*
	header('Access-Control-Allow-Origin: *');
	echo 'connecting...<br />';
	// dev
// 	$postgres = new PDO("pgsql:host=localhost;port=5432;dbname=infinity;user=postgres;password=**************");
	//  production
	$postgres = new PDO("pgsql:host=localhost;dbname=admin_binpostgres", 'binpostgres', 'Ub55a3_b');
	$app = new pgObjects($postgres);

	error_reporting(E_ALL);
	ini_set('display_errors', '1');

	echo $app->test();
*/

	class pgObjectsAdmin {

		private $db;
		private $debug = false;
		private $blueprints = [];
		private $defaultBPProperties = [
				"is_template" => [
					"type" => "int",
					"name" => "template",
					"immutable" => false
				]
			];

		public $instance = '';

		function __construct($dbConn, $instance = 'root'){

			// Set the initial state/config
			$this->instance = $instance;
			$this->instanceObj = null;
			$this->userObj = null;
			$this->bpDIR = APP_ROOT .'blueprints';

			// Set child apis to use
// 			$this->rules = $rules;

			// Set connections to databases
			$this->db = $dbConn;
			$this->docsDb = new PDO(
				"pgsql:host=". $GLOBALS['BENTO_DOCS_DB_PATH'] .";
				port=". $GLOBALS['BENTO_DB_PORT'] .";
				dbname=". $GLOBALS['BENTO_DB_DOCS_NAME'] .";
				sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
				user=". $GLOBALS['BENTO_DB_USER'] .";
				password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
			);

			// Set the write db conn--if no specific path is provided for the
			// write db, fallback to the standard db conn.
			if ($GLOBALS['BENTO_DB_WRITE_PATH']) {

				$this->writeDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_DOCS_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);

			} else {

				$this->writeDb = $dbConn;

			}
			if ($GLOBALS['BENTO_DOCS_DB_WRITE_PATH']) {

				$this->docsWriteDb = new PDO(
					"pgsql:host=". $GLOBALS['BENTO_DOCS_DB_WRITE_PATH'] .";
					port=". $GLOBALS['BENTO_DB_PORT'] .";
					dbname=". $GLOBALS['BENTO_DB_DOCS_NAME'] .";
					sslmode=". $GLOBALS['BENTO_DB_SSL'] .";
					user=". $GLOBALS['BENTO_DB_USER'] .";
					password=". $GLOBALS['BENTO_DB_PASSWORD'] .""
				);

			} else {

				$this->docsWriteDb = $this->docsDb;

			}

		}

		public function test(){
			//var_dump($this->where('staff', array('email' => '<EMAIL>')));

			//echo 'testing';

/*
			$statement = $this->db->prepare('SELECT * FROM object_blueprints;');
			$statement->execute();
			$blueprints = $statement->fetchAll();
			//var_dump($blueprints);
			foreach($blueprints as $i => $datum){

				$datum['blueprint'] = json_decode($datum['blueprint'], true);

				//var_dump($datum['object_type']);

				$statement = $this->db->prepare(
					"INSERT INTO blueprints DEFAULT VALUES RETURNING *;"
				);

				$statement->execute();

				$info = $statement->fetchAll()[0];

				$string = "UPDATE blueprints SET blueprint_type = 'object', instance = 'pagodadev', blueprint_name = '". $datum['object_type'] ."', blueprint = '". trim($this->db->quote(json_encode($datum['blueprint'], JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;";

				$statement = $this->db->prepare($string);

				$statement->execute();

			}
*/


		}

		private function applyBPDefaultProperties($blueprint = null) {

			if($blueprint == null) {
				return false;
			}

			foreach($this->defaultBPProperties as $key => $property) {

				$blueprint[$key] = $property;

			}

			return $blueprint;

		}

		private function createObjectSequencesEntry(){

			$statement = $this->writeDb->prepare(
				"INSERT INTO object_sequences (instance, date_created) VALUES ('". $this->instance ."', DEFAULT) RETURNING *;"
			);

			if($statement->execute()){

				$info = $statement->fetchAll()[0];
				$info['sequences'] = json_decode($info['sequences'], true);
				return $info;

			}else{

				var_dump($statement->errorInfo());

			}

		}

		private function getNextObjectId($objectType){

			$statement = $this->db->prepare(
				"SELECT * FROM object_sequences WHERE instance = '". $this->instance ."';"
			);

			if($statement->execute()){

				$info = $statement->fetchAll()[0];

				// if not yet created, create it
				if($info == null){

					$info = $this->createObjectSequencesEntry();

				}

				// if sequences if null, set it as an object
				if($info['sequences'] == null){
					$info['sequences'] = array();
				}else{
					$info['sequences'] = json_decode($info['sequences'], true);
				}

				// if no entry for object type, create it and start it at 1
				if($info['sequences'][$objectType] == null){

					$info['sequences'][$objectType] = array(
						'lastId' => 1
					);

				}else{

					$info['sequences'][$objectType]['lastId']++;

				}

				$statement = $this->writeDb->prepare(
					"UPDATE object_sequences SET sequences = '". trim($this->writeDb->quote(json_encode($info['sequences'], JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;"
				);

				if($statement->execute()){

					$info = $statement->fetchAll()[0];
// var_dump($statement->fetchAll());
					$sequence = json_decode($info['sequences'], true);
					return $sequence[$objectType]['lastId'];

				}else{

					var_dump($statement->errorInfo());

				}

				var_dump($info);



			}else{

				var_dump($statement->errorInfo());

			}

		}

		private function getSelectClause($selectObj){

			$selectClause = 'SELECT *';

			if( is_numeric($selectObj) ){

				$selectClause = 'SELECT *';

			}else if( is_array($selectObj) ){

				if( count($selectObj) === 0 ){

					$selectClause = 'SELECT *';

				}else{

					$selectClause = 'SELECT id, date_created, object_type, instance, ';

					$i = 1;
					foreach($selectObj as $key => $val){

// 						if($val){

							$selectClause .= "object_data->>'". $key ."' AS $key";
							if( $i < count($selectObj) ){
								$selectClause .= ', ';
// 							}

						}
						$i++;
					}

				}

			}

			return $selectClause;

		}

		private function getObjectTypePhrase($objectType){

			$objectTypePhrase = '';
			if(is_string($objectType)){

				$objectTypePhrase = "object_type = '$objectType'";

			}elseif(is_array($objectType)){

				$objectTypePhrase = "(";

				foreach($objectType as $i => $bpName){

					if($i > 0){
						$objectTypePhrase .= " OR ";
					}
					$objectTypePhrase .= "object_type = '$bpName'";

				}

				$objectTypePhrase .= ")";

			}

			return $objectTypePhrase;

		}

		//!TODO: refactor generation of where clauses into these private funcs
		private function getWhereClause($selectionArr){

		}

		private function getWherePhrase($queryObj){

		}

		private function getIntArrUpdatePhrase($objectData){

			$tagText = "";
			if( is_array($objectData['tagged_with']) ){

				// !also update write_obj column for now (until permissions ui is built into tag comp)
				if( count($objectData['tagged_with']) == 0 ){

					$tagText = ", tagged_with = '{}' ";

					if (!$objectData['write']) {
						$tagText .= ", write_obj = '{}' ";
					}

				}else{

					$tagText = ", tagged_with = '{". implode(',', $this->parseObjectTaggedWith($objectData['tagged_with'])) ."}'";

					if (!$objectData['write']) {
						$tagText .= ", write_obj = '{". implode(',', $this->parseObjectTaggedWith($objectData['tagged_with'])) ."}' ";
					}

				}

			}

			$readPermissionText = "";
			if( is_array($objectData['read']) ){

				if( count($objectData['read']) == 0 ){
					$readPermissionText = ", read_obj = '{}' ";
				}else{
					$readPermissionText = ", read_obj = '{". implode(',', $this->parseObjectTaggedWith($objectData['read'])) ."}' ";
				}

			}

			$writePermissionText = "";
			if( is_array($objectData['write'] && !is_array($objectData['tagged_with']) ) ){

				if( count($objectData['write']) == 0 ){
					$writePermissionText = ", write_obj = '{}' ";
				}else{
					$writePermissionText = ", write_obj = '{". implode(',', $this->parseObjectTaggedWith($objectData['write'])) ."}' ";
				}

			}

			$notifyText = "";
			if( is_array($objectData['notify']) ){

				if( count($objectData['notify']) == 0 ){
					$notifyText = ", notify = '{}' ";
				}else{
					$notifyText = ", notify = '{". implode(',', $this->parseObjectTaggedWith($objectData['notify'])) ."}' ";
				}

			}

			return $tagText . $readPermissionText . $writePermissionText . $notifyText;

		}

		public function changeInstance($instanceKey){

			$this->instance = $instanceKey;

		}

		public function copyInstanceSettingsObjects($fromInstance, $toInstanceKey){

// 			error_reporting(E_ALL);
// 			ini_set('display_errors', '1');
// echo 'testing';
// die();
			/*
$this->changeInstance($toInstanceKey);
			var_dump($this->create('background_process', [
				'payload' => [
					'fromInstance' => $fromInstance,
					'toInstance' => $toInstanceKey
				],
				'run' => 'copyInstanceSettingsObjects',
				'status' => 'not_started'
			], false, 0));
			die();
*/
			$fromInstance = $this->where('instances', ['instance' => $fromInstance])[0];
			$settingsObjectsTypes = explode(',', $fromInstance['settings_objects']);

			// GET BATCH OF SETTINGS TO COPY
			$batchSize = 175;
// 			$batchSize = 2;
// 			var_dump($fromInstance['instance'], $toInstanceKey);
			// LOOP OVER BATCH
			foreach($settingsObjectsTypes as $i => $type){

				$page = 0;
				$continue = true;

				while($continue){

					$this->changeInstance($fromInstance['instance']);
// 					$batch = $this->getSettingsObjectsBatch($settingsObjectsTypes, 0, true, $page*$batchSize, $batchSize);
					$batch = $this->getAll($type, 0, true, $batchSize*$page, 'id', 'ASC', $batchSize, $fromInstance['instance']);

					if(count($batch) == 0){

						$continue = false;

					}else{

						// COPY OBJECTS
						// USE DATA SOURCE ID PROP
						// PUT THE INSTANCE IN THE DATA SOURCE PROP

						// PLACE INSTANCE KEY IN -> data_source AND OLD ID IN -> data_source_id
						if(is_array($batch)){

							foreach($batch as $i => $item){

								$batch[$i]['data_source'] = $fromInstance['id'];
								$batch[$i]['data_source_id'] = $batch[$i]['id'];
								$batch[$i]['instance'] = $toInstanceKey;

							}

						}

						// CREATE OBJECTS
						$this->changeInstance($toInstanceKey);
						$this->create($type, $batch, true, false);

						$page++;

					}

				}

			}

			// LOOP OVER BATCH A SECOND TIME, UPDATING RELATIONSHIPS
			foreach($settingsObjectsTypes as $i => $type){

				$blueprint = $this->getBlueprint($type, false);

				$page = 0;
				$continue = true;
// 				$batch = $this->getSettingsObjectsBatch($settingsObjectsTypes, 0, true, $page*$batchSize, $batchSize);

				while($continue){

					$batch = $this->getAll($type, 0, true, $batchSize*$page, 'id', 'ASC', $batchSize, $toInstanceKey);

					if(count($batch) > 0){

						foreach($blueprint as $propertyKey => $property){

							if($property['type'] === 'objectId' and __::contains($settingsObjectsTypes, $property['objectType'])){

								// GET CHILD OBJS
								$childObjOldIds = __::pluck($batch, $propertyKey);
								$childObjOldIds = __::uniq($childObjOldIds);

								$childObjs = $this->where($property['objectType'], [
									'data_source' => $fromInstance['id'],
									'data_source_id' => [
										'type' => 'or',
										'values' => $childObjOldIds
									]
								]);

								// UPDATE RELATIONSHIPS
								foreach($batch as $i => $obj){

									// ONLY UPDATE IF COPIED FROM THE OTHER INSTANCE
									if($obj['data_source'] == $fromInstance['id']){

										$oldId = $obj[$propertyKey];
										$batch[$i][$propertyKey] = [];

										foreach($childObjs as $k => $childObj){

											if($childObj['data_source_id'] == $oldId){

												$batch[$i][$propertyKey] = $childObj['id'];

											}

										}

									}

								}

							}elseif($property['type'] === 'objectIds' and __::contains($settingsObjectsTypes, $property['objectType'])){

								// GET CHILD OBJS
								$temp = __::pluck($batch, $propertyKey);
								$childObjOldIds = [];
								foreach($temp as $k => $ids){
									foreach($ids as $l => $id){
										array_push($childObjOldIds, $id);
									}
								}

								$childObjOldIds = __::uniq($childObjOldIds);

								$childObjs = $this->where($property['objectType'], [
									'data_source' => $fromInstance['id'],
									'data_source_id' => [
										'type' => 'or',
										'values' => $childObjOldIds
									]
								]);

								// UPDATE RELATIONSHIPS
								foreach($batch as $i => $obj){

									// ONLY UPDATE IF COPIED FROM THE OTHER INSTANCE
									if($obj['data_source'] == $fromInstance['id']){

										$temp = $obj[$propertyKey];
										$batch[$i][$propertyKey] = [];
	// 									var_dump($temp);
										if(is_array($temp)){
											foreach($temp as $j => $oldId){

												foreach($childObjs as $k => $childObj){

													if($childObj['data_source_id'] == $oldId){

														array_push($batch[$i][$propertyKey], $childObj['id']);

													}

												}

											}
										}

									}

								}

							}

						}


						// UPDATE
						$this->changeInstance($toInstanceKey);
						$this->update($type, $batch, false);

						$page++;

					}else{

						$continue = false;

					}

				}

			}
// 			echo 'DONE';
			return true;

		}

		public function create($objectType, $objectData = null, $multiple = 0, $retChildObjs = 0){

			if($_POST->multiple){
				$multiple = $_POST->multiple;
			}

			if($multiple == 0){

				$statement = $this->writeDb->prepare(
					"INSERT INTO objects DEFAULT VALUES RETURNING *;"
				);

				if($statement->execute()){

					$info = $statement->fetchAll()[0];

					// parse object data
					if(is_array($objectData)){

						$obj = $this->parseInput($objectType, $objectData, array(), 1);

						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];
						$obj['created_by'] = intval($_COOKIE['uid']);
						$obj['last_updated'] = $info['date_created'];
						$obj['last_updated_by'] = intval($_COOKIE['uid']);
						$obj['object_uid'] = $this->getNextObjectId($objectType);

						$tagText = $this->getIntArrUpdatePhrase($objectData);

						$string = "UPDATE objects SET object_type = '$objectType', instance = '".$this->instance."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."'". $tagText ." WHERE id = ". $info['id'] ." RETURNING *;";
// 						$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] ." RETURNING *;";

						$statement = $this->writeDb->prepare($string);
						if($statement->execute()){

							return $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0];

						}else{

							return $this->writeDb->errorInfo();

						}

					}else{

						$obj = $this->parseInput($objectType, array(), array(), 1);
						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];
						$obj['created_by'] = intval($_COOKIE['uid']);
						$obj['last_updated'] = $info['date_created'];
						$obj['last_updated_by'] = intval($_COOKIE['uid']);
						$obj['object_uid'] = $this->getNextObjectId($objectType);

						$string = "UPDATE objects SET object_type = '$objectType', instance = '".$this->instance."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] .";";
// 						$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] .";";

						$statement = $this->writeDb->prepare($string);
						if($statement->execute()){

							return $info['id'];

						}elseif($this->debug){

							return $this->writeDb->errorInfo();

						}

					}


				}else{

					return $this->writeDb->errorInfo();

				}

			}else{

				$objectDataArray = $objectData;

				$returnArray = [];

				foreach($objectDataArray as $objectData){

					$statement = $this->writeDb->prepare(
						"INSERT INTO objects DEFAULT VALUES RETURNING *;"
					);

					if($statement->execute()){

						$info = $statement->fetchAll()[0];

						// parse object data
						if(is_array($objectData)){

							$obj = $this->parseInput($objectType, $objectData, array(), 1);

							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated'] = $info['date_created'];
							$obj['last_updated_by'] = intval($_COOKIE['uid']);
							$obj['object_uid'] = $this->getNextObjectId($objectType);

							$tagText = $this->getIntArrUpdatePhrase($objectData);

							$string = "UPDATE objects SET object_type = '$objectType', instance = '".$this->instance."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."'". $tagText ." WHERE id = ". $info['id'] ." RETURNING *;";
// 							$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] ." RETURNING *;";

							$statement = $this->writeDb->prepare($string);
							if($statement->execute()){

								array_push($returnArray, $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0]);

							}

						}else{

							$obj = $this->parseInput($objectType, array(), array(), 1);
							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated'] = $info['date_created'];
							$obj['last_updated_by'] = intval($_COOKIE['uid']);
							$obj['object_uid'] = $this->getNextObjectId($objectType);

							$string = "UPDATE objects SET object_type = '$objectType', instance = '".$this->instance."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] .";";
// 							$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] .";";

							$statement = $this->writeDb->prepare($string);
							if($statement->execute()){

								array_push($returnArray, ['id' => $info['id']]);

							}

						}

					}

				}

				return $returnArray;

			}

		}

		public function createInstance($objectType, $objectData = null, $multiple = 0, $retChildObjs = 0){

			if($_POST->multiple){
				$multiple = $_POST->multiple;
			}

			if($multiple == 0){

				$statement = $this->writeDb->prepare(
					"INSERT INTO objects DEFAULT VALUES RETURNING *;"
				);

				if($statement->execute()){

					$info = $statement->fetchAll()[0];

					// parse object data
					if(is_array($objectData)){

						$obj = $this->parseInput($objectType, $objectData, array(), 1);

						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];
						$obj['created_by'] = intval($_COOKIE['uid']);
						$obj['last_updated'] = $info['date_created'];
						$obj['last_updated_by'] = intval($_COOKIE['uid']);

						$string = "UPDATE objects SET object_type = '$objectType', instance = '". $obj['instance'] ."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;";
// 						$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] ." RETURNING *;";

						$statement = $this->writeDb->prepare($string);
						if($statement->execute()){

							return $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0];

						}else{

							return $this->writeDb->errorInfo();

						}

					}else{

						$obj = $this->parseInput($objectType, array(), array(), 1);
						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];
						$obj['created_by'] = intval($_COOKIE['uid']);
						$obj['last_updated'] = $info['date_created'];
						$obj['last_updated_by'] = intval($_COOKIE['uid']);

						$string = "UPDATE objects SET object_type = '$objectType', instance = '". $obj['instance'] ."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] .";";
// 						$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] .";";

						$statement = $this->writeDb->prepare($string);
						if($statement->execute()){

							return $obj;

						}elseif($this->debug){

							return $this->writeDb->errorInfo();

						}

					}


				}else{

					return $this->writeDb->errorInfo();

				}

			}else{

				$objectDataArray = $objectData;

				$returnArray = [];

				foreach($objectDataArray as $objectData){

					$statement = $this->writeDb->prepare(
						"INSERT INTO objects DEFAULT VALUES RETURNING *;"
					);

					if($statement->execute()){

						$info = $statement->fetchAll()[0];

						// parse object data
						if(is_array($objectData)){

							$obj = $this->parseInput($objectType, $objectData, array(), 1);

							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated'] = $info['date_created'];
							$obj['last_updated_by'] = intval($_COOKIE['uid']);

							$string = "UPDATE objects SET object_type = '$objectType', instance = '". $obj['instance'] ."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;";
// 							$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] ." RETURNING *;";

							$statement = $this->writeDb->prepare($string);
							if($statement->execute()){

								array_push($returnArray, $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0]);

							}

						}else{

							$obj = $this->parseInput($objectType, array(), array(), 1);
							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];
							$obj['created_by'] = intval($_COOKIE['uid']);
							$obj['last_updated'] = $info['date_created'];
							$obj['last_updated_by'] = intval($_COOKIE['uid']);

							$string = "UPDATE objects SET object_type = '$objectType', instance = '". $obj['instance'] ."', object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] .";";
// 							$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] .";";

							$statement = $this->writeDb->prepare($string);
							if($statement->execute()){

								array_push($returnArray, ['id' => $info['id']]);

							}

						}

					}

				}

				return $returnArray;

			}

		}

		public function delete($objectType, $objectId){

			$statement = $this->writeDb->prepare(
				"DELETE FROM objects WHERE id = :objectId;"
			);

			if($statement->execute([':objectId' => $objectId])){

				return true;

			}elseif($this->debug){

				var_dump($statement->errorInfo(), $statement);
				return false;

			}

		}

		public function deleteWhere($objectType = '', $selectionArr = array()){

			$selectString = "DELETE FROM objects WHERE object_type = '$objectType' AND ";

			$i=0;
			foreach($selectionArr as $key => $val){

				if($i > 0){
					$selectString .= " AND ";
				}

				if(is_string($val)){

					$selectString .= 'object_data @>\'{"'.$key.'":"'. trim($val) .'"}\'';
					//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if(is_array($val['value']) and $val['value']['type'] === 'or'){

							$selectString .= '(';
							foreach($val['value']['values'] as $j => $value){
								if($j > 0){
									$selectString .= ' OR ';
								}

								if(is_int($value)){
									$selectString .= "(object_data->'$key')::jsonb @> '[" .$value."]'";
								}else{
									$selectString .= "(object_data->'$key')::jsonb @> '[\"" .$value."\"]'";
								}

							}

							$selectString .= ')';

						}elseif($val['value'] == intval($val['value']) and intval($val['value']) > 0){
							$selectString .= "(object_data->'$key')::jsonb @> '[" .$val['value']."]'";
						}else{
							$selectString .= "object_data->>'$key' ILIKE '%". trim($val['value']) ."%'";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";

					}elseif($val['type'] === 'before'){

						$queryDate = date('Y-m-d H:i:s', $val['date']);
						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') < to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					}elseif($val['type'] === 'after'){

						$queryDate = date('Y-m-d H:i:s', $val['date']);
						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') > to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					}elseif($val['type'] === 'or'){

						$selectString .= '(';
						foreach($val['values'] as $j => $value){
							if($j > 0){
								$selectString .= ' OR ';
							}
							$selectString .= "object_data->>'$key' = '". trim($value) ."'";
						}

						$selectString .= ')';

					}elseif($val['type'] === 'less_than'){

						// !TODO: cast as int and compare
						$selectString .= '(object_data->>\''. $key .'\')::int < '. trim($val['value']);

					}elseif($val['type'] === 'greater_than'){

						$selectString .= '(object_data->>\''. $key .'\')::int > '. trim($val['value']);

					}

				}else{

					$selectString .= 'object_data @>\'{"'.$key.'":'. trim($val) .'}\'';
					//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

				}
				$i++;
			}

			$statement = $this->writeDb->prepare($selectString);

			if($statement->execute()){

				return true;

			}elseif($this->debug){

				var_dump($statement->errorInfo(), $statement);
				return false;

			}

		}

		public function getAll($objectType, $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'date_created', $sortDir = 'asc', $limit = 100, $instance = null){

			if($paged){

				if($instance != null){

					$statement = $this->db->prepare(
						"SELECT *, count(id) over() as full_count FROM objects WHERE instance = '$instance' AND object_type = '$objectType' order by object_data->>'". $sortCol ."' ". $sortDir ." offset ". $offset ." limit ". $limit .";"
					);

				}else{

					$statement = $this->db->prepare(
						"SELECT *, count(id) over() as full_count FROM objects WHERE object_type = '$objectType' order by object_data->>'". $sortCol ."' ". $sortDir ." offset ". $offset ." limit ". $limit .";"
					);

				}

			}else{

				if($instance != null){

					$statement = $this->db->prepare(
						"SELECT * FROM objects WHERE instance = '$instance' AND object_type = '$objectType';"
					);

				}else{

					$statement = $this->db->prepare(
						"SELECT * FROM objects where object_type = '$objectType';"
					);

				}

			}

			if($statement->execute()){

				$data = $statement->fetchAll();
				return $this->parseData($data, $getChildObjects, $objectType);


			}elseif($this->debug){

				return $statement->errorInfo();

			}

		}

		public function getCount($objectType, $whereClause){

			$statement = $this->db->prepare("SELECT count(*) FROM $objectType $whereClause;");

			if($statement->execute()){

				return $statement->fetchAll()[0]['count'];

			}else{

				return $statement->errorInfo();

			}

		}

		public function getDistinct($objectType, $byProperty){

			$statement = $this->db->prepare(
				"SELECT DISTINCT ON (object_data->>'$byProperty') * FROM $objectType;"
			);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), 0, $objectType);

			}else{
				var_dump($statement->errorInfo(), $statement);
				return $statement->errorInfo();

			}

		}

		public function getById($objectType, $objectId, $childObjs = 0){

			$selectClause = $this->getSelectClause($childObjs);

			if( is_array($objectId) ){

				$valuesList = '';
				foreach($objectId as $i => $oid){

					if( is_numeric($oid) ){

						$valuesList .= '('. intval($oid) .')';
						if( $i + 1 < count($objectId) ){
							$valuesList .= ', ';
						}

					}

				}
// 				echo $selectClause ." FROM objects WHERE id = ANY (VALUES  ". $valuesList .")";
				$statement = $this->db->prepare(
					$selectClause ." FROM objects WHERE id = ANY (VALUES  ". $valuesList .") AND instance = '". $this->instance ."' AND is_deleted = false;"
				);

				if( $statement->execute() ){

					$data = $statement->fetchAll();

					if($objectType == null){
						$objectType = $data[0]['object_type'];
					}

					return $this->parseData($data, $childObjs, $objectType);

				}elseif($this->debug){

					return $statement;

				}

			}else{

				$objectId = intval($objectId);
				$statement = $this->db->prepare(
					"SELECT * FROM objects WHERE id = :objectId;"
				);

				if($statement->execute([':objectId' => $objectId])){

					return $this->parseData($statement->fetchAll(), $childObjs, $objectType)[0];

				}elseif($this->debug){

					return $statement;

				}

			}

		}

		public function getWhere($objectType, $whereClause){

			$statement = $this->db->prepare(
				"SELECT * FROM $objectType $whereClause;"
			);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), 0, $objectType);

			}elseif($this->debug){
				var_dump($statement->errorInfo(), $statement);
				return $statement->errorInfo();

			}

		}

		public function getWith($objectType, $selectionArr, $childObj, $childObjKey){

			$whereClause = "";
			$i=0;
			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					$whereClause .= "and a.object_data->>'$key' = '". trim($val) ."'";

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						$whereClause .= "and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";

					}

				}else{

					$whereClause .= "and a.object_data->>'$key' = '". trim($val) ."'";

				}
				$i++;
			}

			$selectString =

				"SELECT a.object_data,  array_agg(b.object_data) as child_data ".
				"FROM objects a ".
				"LEFT JOIN objects b ON b.object_type = '$childObj' AND b.object_data->>'$childObjKey' = a.object_data->>'id' WHERE a.instance = '". $this->instance ."' and a.object_type = '". $objectType ."' $whereClause ".
				"GROUP BY a.object_data;";

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				$rawData = $statement->fetchAll();
				$ret = [];
				$i=0;

				if(is_array($rawData)){
					foreach($rawData as $rawDatum){

						$ret[$i] = json_decode($rawDatum['object_data'], true);
						$temp = json_decode("[". trim($rawDatum['child_data'], "{}") ."]", true);

						$j=0;
						if(is_array($temp)){
							foreach($temp as $tempData){

								$temp[$j] = json_decode($tempData, true);
								$j++;

							}
						}else{
							$temp = array();
						}

						$ret[$i][$childObj] = $temp;
						$i++;

					}
				}

				return $ret;

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function incrementCommentCount($objectId){

			///if comment_count null then run sum call otherwise increment by 1;
			$object = $this->getById(null, $objectId);

			$comments = $this->where(
				'notes', ['type_id' => intval($objectId)], '', 0, true, 0, 'null', 'asc', 1, null
			);

			return $this->update(
				$object['object_bp_type']
				, ['id' => $object['id'] ],
				0,
				['comment_count' => $comments[0]['full_count']]
			);

		}

		public function query($sql){

			$statement = $this->db->prepare($sql);

			if($statement->execute()){

				return $statement->fetchAll();

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function update($objectType, $objectData, $childObjs = 0){

			// if $objectData contains multiple objects, update each
			if(array_keys($objectData) === range(0, count($objectData) - 1)){

				// return data
				$ret = array();
				foreach($objectData as $i => $datum){
					$ret[] = $this->update($datum['object_bp_type'], $datum, $childObjs);
				}
				return $ret;

			// update single object
			}else{

				$blueprint = $this->getBlueprint($objectType);
				$old = $this->getById($objectType, $objectData['id']);
				$obj = $this->parseInput($objectType, $objectData, $old);

				$statement = $this->writeDb->prepare("UPDATE objects SET object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $objectData['id'] ." RETURNING *;");

				if($statement->execute()){

					return $this->parseData($statement->fetchAll(), $childObjs, $objectType)[0];

				}elseif($this->debug){

					var_dump($statement, $statement->errorInfo(), "UPDATE objects SET object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $objectData['id'] ." RETURNING *;");

					return false;

				}

			}

		}

		public function updateInstance($objectType, $objectData, $childObjs = 0){

			// if $objectData contains multiple objects, update each
			if(array_keys($objectData) === range(0, count($objectData) - 1)){

				// return data
				$ret = array();
				foreach($objectData as $i => $datum){
					$ret[] = $this->update($datum['object_bp_type'], $datum, $childObjs);
				}
				return $ret;

			// update single object
			}else{

				$blueprint = $this->getInstanceBlueprint($objectType);
				$old = $this->getById($objectType, $objectData['id']);
				$obj = $this->parseInput($objectType, $objectData, $old);
//var_dump($objectData);
				$statement = $this->writeDb->prepare("UPDATE objects SET object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $objectData['id'] ." RETURNING *;");

				if($statement->execute()){

//var_dump($this->parseData($statement->fetchAll(), $childObjs, $objectType));
					return $this->parseData($statement->fetchAll(), $childObjs, $objectType)[0];

				}elseif($this->debug){

					var_dump($statement, $statement->errorInfo(), "UPDATE objects SET object_data = '". trim($this->writeDb->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $objectData['id'] ." RETURNING *;");

					return false;

				}

			}

		}

		public function where($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null, $getJust = array(), $sortCast = 'string', $instance = null){

			$objectTypePhrase = $this->getObjectTypePhrase($objectType);

			if (array_key_exists('is_deleted', $selectionArr)) {

				if ($selectionArr['is_deleted'] == true) {
					$isDeletedPhrase = 'AND is_deleted = true';
				} else {
					$isDeletedPhrase = 'AND is_deleted = false';
				}
				unset($selectionArr['is_deleted']);
			}else{

				$isDeletedPhrase = '';

			}


			if($paged){

				if (is_string($instance)) {
					$selectString = "SELECT *, COUNT(id) OVER() as full_count FROM objects WHERE instance = '". $instance ."' ". $isDeletedPhrase ." AND $objectTypePhrase ";
				} else {
					$selectString = "SELECT *, COUNT(id) OVER() as full_count FROM objects WHERE $objectTypePhrase ". $isDeletedPhrase ." ";
				}

				if($sum){
					$selectString = "SELECT *, SUM(object_data->>'$sum') as object_data->>'$sum', COUNT(id) OVER() as full_count FROM objects WHERE instance = '". $this->instance ."' AND $objectTypePhrase ". $isDeletedPhrase ." ";
				}

			}else{

				if (is_string($instance)) {

					$selectString = "SELECT * FROM objects WHERE instance = '". $instance ."' AND $objectTypePhrase ". $isDeletedPhrase ." ";

				} else {

					$selectString = "SELECT * FROM objects WHERE $objectTypePhrase ". $isDeletedPhrase ." ";

				}

			}

			$i=0;
			foreach($selectionArr as $key => $val){

// 				if($i > 0){
					$selectString .= " AND ";
// 				}

				if($key === 'tagged_with'){

					if (is_array($val) and $val['type'] == 'any') {
						$selectString .= "tagged_with && ARRAY[". trim(implode(',', $val['values'])) ."]::integer[]";
					}elseif(is_array($val)){
						$selectString .= "tagged_with @> ARRAY[". trim(implode(',', $val)) ."]";
					}elseif(is_int($val)){
						$selectString .= "tagged_with @> ARRAY[". trim($val) ."]";
					}

				}elseif($key === 'read'){

					if (is_array($val) and $val['type'] == 'any') {
						$selectString .= "read_obj && ARRAY[". trim(implode(',', $val['values'])) ."]::integer[]";
					}elseif(is_array($val)){
						$selectString .= "read_obj @> ARRAY[". trim(implode(',', $val)) ."]";
					}elseif(is_int($val)){
						$selectString .= "read_obj @> ARRAY[". trim($val) ."]";
					}

				}elseif($key === 'write'){

					if (is_array($val) and $val['type'] == 'any') {
						$selectString .= "write_obj && ARRAY[". trim(implode(',', $val['values'])) ."]::integer[]";
					}elseif(is_array($val)){
						$selectString .= "write_obj @> ARRAY[". trim(implode(',', $val)) ."]";
					}elseif(is_int($val)){
						$selectString .= "write_obj @> ARRAY[". trim($val) ."]";
					}

				}elseif($key === 'archive' and $val){

					// do nothing

				}elseif(is_string($val)){

					$selectString .= 'object_data @>\'{"'.$key.'":"'. trim($val) .'"}\'';
					//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if(is_array($val['value']) and $val['value']['type'] === 'or'){

							$selectString .= '(';
							foreach($val['value']['values'] as $j => $value){
								if($j > 0){
									$selectString .= ' OR ';
								}

								if(is_int($value)){
									$selectString .= "(object_data->'$key')::jsonb @> '[" .$value."]'";
								}else{
									$selectString .= "(object_data->'$key')::jsonb @> '[\"" .$value."\"]'";
								}

							}

							$selectString .= ')';

						}elseif($val['value'] == intval($val['value']) and intval($val['value']) > 0){
							$selectString .= "(object_data->'$key')::jsonb @> '[" .$val['value']."]'";
						}else{
							$selectString .= "object_data->>'$key' ILIKE '%". trim($val['value']) ."%'";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";

					}elseif($val['type'] === 'before'){

						$queryDate = date('Y-m-d H:i:s', $val['date']);
						$selectString .= "((object_data->>'$key' = '') IS NOT TRUE AND to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') < to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS'))";

					}elseif($val['type'] === 'after'){

						$queryDate = date('Y-m-d H:i:s', $val['date']);
						$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') > to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

					}elseif($val['type'] === 'or'){

						$selectString .= '(';
						foreach($val['values'] as $j => $value){
							if($j > 0){
								$selectString .= ' OR ';
							}
							$selectString .= "object_data->>'$key' = '". trim($value) ."'";
						}

						$selectString .= ')';

					}elseif($val['type'] === 'less_than'){

						// !TODO: cast as int and compare
						$selectString .= '(object_data->>\''. $key .'\')::int < '. trim($val['value']);

					}elseif($val['type'] === 'greater_than'){

						$selectString .= '(object_data->>\''. $key .'\')::int > '. trim($val['value']);

					}elseif($val['type'] === 'not_equal'){

						if (is_string($val['value'])) {

							$selectString .= 'NOT object_data @>\'{"'.$key.'":"'. trim($val['value']) .'"}\'';

						} else {

							$selectString .= 'NOT object_data @>\'{"'.$key.'":'. trim($val['value']) .'}\'';

						}

					}

				} elseif (is_bool($val)) {

					$boolStringVal = ($val) ? 'true' : 'false';
					$selectString .= 'object_data @>\'{"'.$key.'":'. $boolStringVal .'}\'';

				} else {

					$selectString .= 'object_data @>\'{"'.$key.'":'. trim($val) .'}\'';
					//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

				}
				$i++;
			}

			if($paged){

				switch($sortCast){

					case 'int':

						$orderByString = "(object_data->>'". $sortCol ."')::int";

						break;

					default:

						$orderByString = "object_data->>'". $sortCol ."'";

				}

				$selectString .= " ". $additionalClause ." order by ". $orderByString ." ". $sortDir ." offset ". $offset ." limit ". $limit .";";

			}else{

				$selectString .= ' '. $additionalClause .";";

			}

			$statement = $this->db->prepare($selectString);

// 			$this->debug = true;
			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), $getChildObjects, $objectType, $getJust);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function whereAcrossInstances($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null, $getJust = array(), $sortCast = 'string'){

			$selectClause = $this->getSelectClause($getChildObjects);
			$objectTypePhrase = $this->getObjectTypePhrase($objectType);
			if($paged){

				$selectString = $selectClause .", COUNT(id) OVER() as full_count FROM objects WHERE ". $objectTypePhrase ." AND is_deleted = false and ";

				if($sum){

					$selectString = $selectClause .", SUM(object_data->>'$sum') as object_data->>'$sum', COUNT(id) OVER() as full_count FROM objects WHERE ". $objectTypePhrase ." AND is_deleted = false and ";

				}

			}else{

				$selectString = $selectClause ." FROM objects WHERE ". $objectTypePhrase ." AND is_deleted = false and ";

			}

			$i=0;
			foreach($selectionArr as $key => $val){

				if($key != 'childObjs'){

					if($i > 0){
						$selectString .= " AND ";
					}

					if(is_string($val)){

						$selectString .= 'object_data @>\'{"'.$key.'":"'. trim($val) .'"}\'';
						//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

					}elseif(is_array($val)){

						if($val['type'] === 'contains'){

							if(is_array($val['value']) and $val['value']['type'] === 'or'){

								$selectString .= '(';
								foreach($val['value']['values'] as $j => $value){
									if($j > 0){
										$selectString .= ' OR ';
									}

									if(is_int($value) and ($key != 'zip' or $key != 'tracking_number')){
										$selectString .= "(object_data->'$key')::jsonb @> '[" .$value."]'";
									}else{
										$selectString .= "(object_data->'$key')::jsonb @> '[\"" .$value."\"]'";
									}

								}

								$selectString .= ')';

							}elseif($val['value'] == intval($val['value']) and intval($val['value']) > 0 and $key != 'zip'){
								$selectString .= "(object_data->'$key')::jsonb @> '[" .$val['value']."]'";
							}else{
								$selectString .= "object_data->>'$key' ILIKE '%". trim($val['value']) ."%'";
							}

						}elseif($val['type'] === 'contains-id'){

							$selectString .= "(object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";

						}elseif($val['type'] === 'between'){

							$startTime = date('Y-m-d H:i:s', $val['start']);
							$endTime = date('Y-m-d H:i:s', $val['end']);

							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";

						}elseif($val['type'] === 'before'){

							$queryDate = date('Y-m-d H:i:s', $val['date']);
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') < to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

						}elseif($val['type'] === 'after'){

							$queryDate = date('Y-m-d H:i:s', $val['date']);
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') > to_timestamp('". $queryDate ."', 'YYYY-MM-DD HH24:MI:SS')";

						}elseif($val['type'] === 'or'){

							$selectString .= '(';
							foreach($val['values'] as $j => $value){
								if($j > 0){
									$selectString .= ' OR ';
								}
								$selectString .= "object_data->>'$key' = '". trim($value) ."'";
							}

							$selectString .= ')';

						}elseif($val['type'] === 'less_than'){

							// !TODO: cast as int and compare
							$selectString .= '(object_data->>\''. $key .'\')::REAL < '. trim($val['value']);

						}elseif($val['type'] === 'greater_than'){

							$selectString .= '(object_data->>\''. $key .'\')::REAL > '. trim($val['value']);

						}

					}else{

						$selectString .= 'object_data @>\'{"'.$key.'":'. trim($val) .'}\'';
						//$selectString .= "object_data->>'$key' = '". trim($val) ."'";

					}

				}
				$i++;
			}

			if($paged){

				switch($sortCast){

					case 'int':

						$orderByString = "(object_data->>'". $sortCol ."')::int";

						break;

					default:

						$orderByString = "object_data->>'". $sortCol ."'";

				}

				$selectString .= " ". $additionalClause ." order by ". $orderByString ." ". $sortDir ." offset ". $offset ." limit ". $limit .";";

			}else{

				$selectString .= ' '. $additionalClause .";";

			}

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), $getChildObjects, $objectType, $getJust);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function whereAll($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null){

			if($paged){

				$selectString = "SELECT *, COUNT(id) OVER() as full_count FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";

				if($sum){
					$selectString = "SELECT *, SUM($sum) as $sum, COUNT(id) OVER() as full_count FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";
				}

			}else{

				$selectString = "SELECT * FROM objects WHERE object_type = '$objectType' AND is_deleted = false AND ";

			}

			$i=0;
			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					if($i == 0){
// 						$selectString .= "object_data @> '" . '{"'. $key .'":"'. trim($val, "'") .'"'."}'";
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
// 						$selectString .=  " and object_data @> '". '{"'. $key .'":"'. trim($val, "'") . '"' ."}'";
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i == 0){
							$whereClause .= "(a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}else{
							$whereClause .= " and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					}

				}else{

					if($i == 0){
// 						$selectString .= "object_data @> '" . '{"'. $key .'":'. trim($val, "'") ."}'";
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
						//$selectString .= "object_data->'". $key ."':'". trim($val, "'") ."'";
					}else{
// 						$selectString .=  " and object_data @> '". '{"'. $key .'":'. trim($val, "'") ."}'";
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}

			if($paged){

				if($_POST->queryObj->paged->pageLength){

					$limit = $_POST->queryObj->paged->pageLength;

				}

				$selectString .= " ". $additionalClause ." order by object_data->>'". $sortCol ."' ". $sortDir ." offset ". $offset ." limit ". $limit .";";

			}else{

				$selectString .= ' '. $additionalClause .";";

			}

			//echo $selectString;
			//die();

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), $getChildObjects, $objectType);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function whereBy($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0, $paged = false, $offset = 0, $sortCol = 'null', $sortDir = 'asc', $limit = 100, $sum = null){

			if($paged){

				$selectString = "SELECT json_agg(object_data) as object_data, COUNT(id) OVER() as full_count FROM objects WHERE instance = '". $this->instance ."' AND object_type = '$objectType' AND ";

				if($sum){
					$selectString = "SELECT json_agg(object_data) as object_data, SUM(object_data->>'$sum') as object_data->>'$sum', COUNT(id) OVER() as full_count FROM objects WHERE instance = '". $this->instance ."' AND object_type = '$objectType' AND ";
				}

			}else{

				$selectString = "SELECT json_agg(object_data) as object_data FROM objects WHERE instance = '". $this->instance ."' AND object_type = '$objectType' ";

			}

			$i=0;
			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i == 0){
							$selectString .= "object_data->>'$key' like '". trim($val['value']) ."%'";
						}else{
							$selectString .= " and object_data->>'$key' like '". trim($val['value']) ."%'";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					}

				}else{

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}

			if($paged){

				$selectString .= " ". $additionalClause ." order by object_data->>'". $sortCol ."' ". $sortDir ." offset ". $offset ." limit ". $limit .";";
//echo $selectString;
			}else{

				$selectString .= ' '. $additionalClause .";";

			}
echo $selectString;
			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return $statement->fetchAll();

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function getAllBlueprints(){

			$blueprints = [];
			$dir = new DirectoryIterator($this->bpDIR);
			$blueprint_temp = [];

			foreach ($dir as $fileinfo) {

				if (!$fileinfo->isDot()) {

					$blueprint_temp = json_decode( file_get_contents($this->bpDIR .'/'. $fileinfo->getFilename()), true);

					$blueprint_temp['blueprint'] = $this->applyBPDefaultProperties($blueprint_temp['blueprint']);

					array_push(
						$blueprints
						, $blueprint_temp
					);

				}

			}

			return $blueprints;

		}

		public function getBlueprint($objectType, $getOptions = false){

			// custom entity blueprints, stored in the db
			if (substr($objectType, 0, 1) === '#') {

				$blueprint = $this->where(
					'entity_type'
					, [
						'bp_name' => substr($objectType, 1)
					]
				);

				$blueprint = $this->applyBPDefaultProperties(
					$blueprint[0]['blueprint']
					, true
				);
				return $blueprint;

			}

			$blueprint = json_decode(
				file_get_contents($this->bpDIR .'/'. $objectType .'.json')
				, true
			)['blueprint'];

			if($blueprint){

				if($getOptions){

					if(is_array($blueprint)){

						foreach($blueprint as $key => $blueprintDatum){

							if(

								($blueprintDatum['type'] == 'objectId' && !$blueprintDatum['immutable'] && $blueprintDatum['objectType'] !== 'file_meta_data'
								or $blueprintDatum['type'] == 'objectIds' && !$blueprintDatum['immutable'] && $blueprintDatum['objectType'] !== 'file_meta_data'
								or $blueprintDatum['type'] == 'parentId' && !$blueprintDatum['immutable'] && $blueprintDatum['objectType'] !== 'file_meta_data')

								and (!isset($blueprintDatum['objectOverflow']) or $blueprintDatum['objectOverflow'] == false)

							){

								if($blueprintDatum['type'] == 'objectId' or $blueprintDatum['type'] == 'parentId'){
									$blueprint[$key]['type'] = 'select';
								}elseif($blueprintDatum['type'] == 'objectIds'){
									$blueprint[$key]['type'] = 'multi-select';
								}

								$blueprint[$key]['options'] = array();
								$options = $this->getAll($blueprintDatum['objectType']);

								$j=0;
								if(is_array($options)){
									foreach($options as $option){

										$displayName = $blueprint[$key]['selectDisplay'];
										$displayItems = explode('[', $displayName);

										if(is_array($displayItems)){
											$k=1;
											foreach($displayItems as $displayItem){

												$displayName = str_replace('['. explode(']', $displayItems[$k])[0] .']', $option[explode(']', $displayItems[$k])[0]], $displayName);
												$k++;

											}
										}

										$blueprint[$key]['options'][$option['id']] = $displayName;

										$j++;
									}
								}

							}

							$i++;
						}
					}

					$blueprint = $this->applyBPDefaultProperties($blueprint);

					return $blueprint;

				}else{

					$blueprint = $this->applyBPDefaultProperties($blueprint);

					return $blueprint;

				}

			}else{

				return false;

			}

		}

		public function setInstance($instance){

			$this->instance = $instance;

			return true;

		}

		public function sum($objectType = 'requests', $field = 'books', $dateBy = 'month', $groupBy = 'date_created', $start, $end, $offset = 0, $limit = 100, $search){

			$selectString = "";

			$i=1;
			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i == 0){
							$whereClause .= "(a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}else{
							$whereClause .= " and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					}

				}else{

					if($i == 0){
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}

			$query = "select
				date_trunc('". $groupBy ."', CAST(object_data->>'". $groupBy ."' AS date)) AS \"grouped\",
				COUNT(id) as grouped_total,
				SUM (
				     CAST (
				          object_data->>'". $field ."' AS INT
				     )
				),
				AVG (
				     CAST (
				          object_data->>'". $field ."' AS INT
				     )
				)
				FROM objects
				WHERE instance = '". $this->instance ."' AND object_type = '". $objectType ."' and to_timestamp(object_data->>'date_created', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $start ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $end ."' ". $selectString ."
				GROUP BY grouped ORDER BY grouped desc offset ". $offset ." limit ". $limit .";";

			$statement = $this->db->prepare($query);

			if($statement->execute()){

				return $statement->fetchAll();

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		private function getObjectPropertyChildObjs($objectData, $blueprint, $property, $selection){

			$objectIds = [];
			$childObjects = [];
			$subSelection = 0;

			if( is_array($selection) ){
				$subSelection = $selection[$property];
			}elseif( is_int($selection) ){
				$subSelection = $selection - 1;
			}

			if ($subSelection === 'id') {
				return $objectData;
			}

			switch($blueprint[$property]['type']){

				case 'objectId':
				case 'relatedObject':
					// get child objects
					$objectIds = __::uniq(__::pluck($objectData, $property));
					$childObjects = $this->getById($blueprint[$property]['objectType'], $objectIds, $subSelection);

					// merge child objects into data
					foreach($objectData as $i => $datum){

						$selectId = $datum[$property];
						if($selectId){
							$objectData[$i][$property] = __::find($childObjects, function($childObj) use($selectId) {
								return $selectId === $childObj['id'];
							});
						}else{
							$objectData[$i][$property] = null;
						}

						if($blueprint[$property]['objectType'] === 'file_meta_data'){
							$objectData[$i][$property]['loc'] = $this->getFileLocation($objectData[$i][$property]);
						}

					}
					$objectIds = [];
					$childObjects = [];

				break;

				case 'objectIds':

					// get child objs
					$objectIds = [];

					foreach($objectData as $i => $datum){

						if( is_array($datum[$property]) ){
							foreach($datum[$property] as $objId){
								array_push($objectIds, $objId);
							}
						}

					}

					$objectIds = __::uniq($objectIds);
					$childObjects = $this->getById($blueprint[$property]['objectType'], $objectIds, $subSelection);

					// merge child objs into data
					foreach($objectData as $i => $datum){

						$selectIds = $datum[$property];
						if($selectIds){
							$objectData[$i][$property] = __::filter($childObjects, function($childObj) use($selectIds) {
								return in_array($childObj['id'], $selectIds);
							});
						}else{
							$objectData[$i][$property] = [];
						}

					}

					$objectIds = [];
					$childObjects = [];

				break;

				case 'list':

					$listSubSelection = $subSelection;
					if( is_int($listSubSelection) ){
						$listSubSelection++;
					}

					//!TODO: update this with batch calls like rest in this func
					foreach($objectData as $i => $datum){

						foreach($objectData[$i][$property] as $innerKey => $childObj){

							$objectData[$i][$property][$innerKey] = $this->getChildObjsOld($childObj, $blueprint[$property]['blueprint'], $listSubSelection);

						}

					}

				break;

				//!TODO: put this somewhere more fitting
				case 'select':
					foreach($objectData as $i => $datum){
						$objectData[$i][$property . '_name'] = $blueprint[$property]['options'][$datum[$property]];
					}
				break;

			}

			return $objectData;

		}

		private function getChildObjs($objectData, $blueprint, $levels = 0){

			if( is_array($blueprint) ){

				foreach($blueprint as $propertyName => $property){

					if( is_array($levels) and array_key_exists($propertyName, $levels) ){
						$objectData = $this->getObjectPropertyChildObjs($objectData, $blueprint, $propertyName, $levels);
					}elseif( is_int($levels) and $levels > 0 ){
						$objectData = $this->getObjectPropertyChildObjs($objectData, $blueprint, $propertyName, $levels);
					}

				}

			}

			return $objectData;

		}

		private function getFileLocation($file){

			return '../../'.$_REQUEST['pagodaAPIKey'].'/'.$file['oid_type'] .'/'. $file['oid'] .'/'. $file['file_name'];

		}

		public function getInstanceBlueprint($objectType = null, $getOptions = false){

			$statement = $this->db->prepare(
				"SELECT * FROM blueprints WHERE blueprint_type = 'instance' AND blueprint_name = :objType;"
			);

			if($statement->execute(array(':objType' => $objectType))){

				$records = $statement->fetchAll();

				$blueprint = json_decode($records[0]['blueprint'], true);

				if($getOptions){

					if(is_array($blueprint)){

						foreach($blueprint as $key => $blueprintDatum){

							if($blueprintDatum['type'] == 'objectId' && !$blueprintDatum['immutable'] && $blueprintDatum['objectType'] !== 'file_meta_data'
							or $blueprintDatum['type'] == 'objectIds' && !$blueprintDatum['immutable'] && $blueprintDatum['objectType'] !== 'file_meta_data'
							or $blueprintDatum['type'] == 'parentId' && !$blueprintDatum['immutable'] && $blueprintDatum['objectType'] !== 'file_meta_data'){

								if($blueprintDatum['type'] == 'objectId' or $blueprintDatum['type'] == 'parentId'){
									$blueprint[$key]['type'] = 'select';
								}elseif($blueprintDatum['type'] == 'objectIds'){
									$blueprint[$key]['type'] = 'multi-select';
								}

								$blueprint[$key]['options'] = array();
								$options = $this->getAll($blueprintDatum['objectType']);

								$j=0;
								if(is_array($options)){

									foreach($options as $option){

										$displayName = $blueprint[$key]['selectDisplay'];
										$displayItems = explode('[', $displayName);

										if(is_array($displayItems)){
											$k=1;
											foreach($displayItems as $displayItem){

												$displayName = str_replace('['. explode(']', $displayItems[$k])[0] .']', $option[explode(']', $displayItems[$k])[0]], $displayName);
												$k++;

											}
										}

										$blueprint[$key]['options'][$option['id']] = $displayName;

										$j++;
									}

								}

							}

							$i++;
						}
					}

					return $blueprint;

				}else{

					return $blueprint;

				}

			}else{

				return false;

			}

		}

		// TAGS
		private function parseObjectTaggedWith($tags){

			$tagsArray = [];

			foreach($tags as $i => $tagId){

				if(is_numeric($tagId)){

					array_push($tagsArray, intval($tagId));

				}elseif(is_array($tagId) and array_key_exists('id', $tagId) and intval($tagId['id']) > 0){

					array_push($tagsArray, intval($tagId['id']));
				}

			}

			return $tagsArray;

		}

		private function parseTagListFromDB($entry = null){

			if( is_null($entry) ){
				return [];
			}

			if( strlen( substr($entry, 1, -1) ) === 0 ){
				return [];
			}

			return array_map('intval', explode(',', substr($entry, 1, -1)));

		}

		// PRIVATE METHODS

		private function parseData($data, $getChildObjects = 0, $objectType = null, $getJust = array(), $with = null){

			// get necessary blueprints if they are not already cached
			$objectType = __::uniq(__::pluck($data, 'object_type'));
			if( count($objectType) == 1 ){
				$objectType = $objectType[0];
			}
			if(is_string($objectType)){

				if(!$this->blueprints){

					$this->blueprints[$objectType] = $this->getBlueprint($objectType);

				}else{

					if(!array_key_exists($objectType, $this->blueprints)){

						$this->blueprints[$objectType] = $this->getBlueprint($objectType);
					}

				}

			}elseif(is_array($objectType)){

				foreach($objectType as $i => $bpName){

					if(!array_key_exists($bpName, $this->blueprints)){
						$this->blueprints[$bpName] = $this->getBlueprint($bpName);
					}

				}

			}

			$ret = array();


			if(is_array($data)){

				foreach($data as $key => $datum){

					if($with === null){
						$temp = json_decode($datum['object_data'], true);
					}else{
						$temp = $datum;
					}

					if( is_array($getChildObjects) ){

// 						$temp = [];
						$temp['id'] = $datum['id'];
						$temp['date_created'] = $temp['date_created'];
                        $temp['last_updated'] = $temp['last_updated'];
						$temp['object_bp_type'] = $datum['object_type'];
						$temp['full_count'] = $datum['full_count'];
						$temp['instance'] = $datum['instance'];
						$temp['tagged_with'] = $this->parseTagListFromDB($datum['tagged_with']);
						$temp['read'] = $this->parseTagListFromDB($datum['read_obj']);
						$temp['write'] = $this->parseTagListFromDB($datum['write_obj']);
						$temp['notify'] = $this->parseTagListFromDB($datum['notify']);

						$bp = $this->blueprints[$temp['object_bp_type']];

						foreach( $datum as $key => $val ){

							if( array_key_exists($key, $bp) && $bp[$key]['hidden'] !== true ){

								// decrypt private properties, if user has read privelages
								/*
if($bp[$key]['encrypt']){
									$temp[$key] = $this->decrypt_property($temp[$key]);
								}
								if($bp[$key]['hidden']){
									$temp[$key] = null;
								}
*/

								$temp[$key] = $datum[$key];
								switch($bp[$key]['type']){

									case 'int':
									case 'objectId':
									case 'usd':
									case 'parentId':
									case 'relatedObject':
									$temp[$key] = intval($temp[$key]);
									break;

									case 'list':
									case 'objectIds':
									case 'object':
									$temp[$key] = json_decode($temp[$key], true);
									break;

								}
							}

						}

					}else{

						$temp['tagged_with'] = $this->parseTagListFromDB($datum['tagged_with']);
						$temp['read'] = $this->parseTagListFromDB($datum['read_obj']);
						$temp['write'] = $this->parseTagListFromDB($datum['write_obj']);
						$temp['notify'] = $this->parseTagListFromDB($datum['notify']);

						// default to given object type to get bp if bp is not defined in object data
						if(array_key_exists('object_bp_type', $temp)){
							$bp = $this->blueprints[$temp['object_bp_type']];
						}elseif($bp == null and is_string($objectType)){
							$bp = $this->blueprints[$objectType];
						}

						if(is_array($bp)){
							foreach($bp as $key => $prop){

								if($bp[$key]['hidden']){
									unset($temp[$key]);
								}

								// decrypt private properties, if user has read privelages
								/*
if($prop['encrypt']){
									$temp[$key] = $this->decrypt_property($temp[$key]);
								}
								if($prop['hidden']){
									$temp[$key] = null;
								}
*/

							}
						}

						if(!empty($getJust)){

							$just = array(
								'date_created' => $temp['date_created']
							);

							foreach($getJust as $i => $prop){
								$just[$prop] = $temp[$prop];
							}

							$temp = $just;

						}

						$temp['id'] = $datum['id'];
						$temp['date_created'] = $temp['date_created'];
                        $temp['last_updated'] = $temp['last_updated'];
						$temp['object_bp_type'] = $datum['object_type'];
						$temp['full_count'] = $datum['full_count'];
						$temp['instance'] = $datum['instance'];

						$bp = null;

					}

					/*
if(array_key_exists($datum['object_type'], $this->blueprints)){
						$bp = $this->blueprints[$datum['object_type']];
					}
*/

// 					$temp = $this->getChildObjsOld($temp, $bp, $getChildObjects);

					if($with !== null){
						$temp[$with] = $datum[$with];
					}

					array_push($ret, $temp);

				}

				// get and merge in child obj data
				$groups = __::groupBy($ret, 'object_bp_type');
				$ret = [];
				foreach($groups as $objType => $group){
					$ret = array_merge($ret, $this->getChildObjs($group, $this->blueprints[$objType], $getChildObjects));
				}

				return $ret;

			}else{

				return false;

			}


		}

		private function parseInput($objectType, $objectData, $obj, $newObj = 0, $blueprint = null, $depth = 0){

			if($blueprint == null){
				$blueprint = $this->getBlueprint($objectType);
			}

			foreach($blueprint as $key => $field){

				if($key !== 'id' and $key !== 'date_created' and isset($objectData[$key])){

					switch($field['type']){

						case 'date':

							try {
								$date = new DateTime($objectData[$key]);
								$obj[$key] = $date->format('Y-m-d H:i:s');
							}catch (Exception $e){
								$obj[$key] = $objectData[$key];
							}


						break;

						case 'time':

							try {
								$date = new DateTime($objectData[$key]);
								$obj[$key] = $date->format('H:i:s');
							}catch (Exception $e){
								$obj[$key] = $objectData[$key];
							}

						break;

						case 'float':
						$obj[$key] = $objectData[$key];
						break;

						case 'int':
						case 'usd':
						$obj[$key] = intval(preg_replace("/[^0-9]-/","", $objectData[$key]));
						break;

						case 'list':
						// get next id to use, and remove items sharing ids
						$tempList = array();
						$nextId = 1;
						$ids = array();
						if(is_array($objectData[$key])){
							foreach($objectData[$key] as $i => $datum){
								if(intval($datum['id']) >= $nextId){
									$nextId = intval($datum['id']) + 1;
								}
								if(intval($datum['id']) >= 1){

									if(isset($ids[$datum['id']])){
										continue;
									}else{
										array_push($tempList, $datum);
									}
									$ids[$datum['id']] = $i;

								}else{

									array_push($tempList, $datum);

								}
							}
						}

						$objectData[$key] = $tempList;
						$temp = array();
						$tempInner = array();

						if(is_array($objectData[$key])){
							foreach($objectData[$key] as $i => $datum){

								$tempInner = array();
								$tempInner = $this->parseInput($objectType, $datum, $obj[$key][$i], $newObj, $field['blueprint'], $depth + 1);

								// add id if item in list is new
								if(!array_key_exists('id', $tempInner)){
									$tempInner['id'] = $nextId;
									$nextId++;
								}

								array_push($temp, $tempInner);

							}
						}
						$obj[$key] = $temp;
						break;

						case 'objectId':
						case 'parentId':

						if(is_array($objectData[$key]) and array_key_exists('id', $objectData[$key]) and intval($objectData[$key]['id']) > 1){
							$obj[$key] = intval($objectData[$key]['id']);
						}elseif(intval(trim($objectData[$key], "'")) > 0){
							$obj[$key] = intval($objectData[$key]);
						}
						break;

						case 'objectIds':
						$temp = array();
						if(is_array($objectData[$key])){

							foreach($objectData[$key] as $i => $datum){

								if(is_array($objectData[$key][$i]) and array_key_exists('id', $objectData[$key][$i]) and intval($objectData[$key][$i]['id']) > 0){

									$temp[$i] = intval($objectData[$key][$i]['id']);

								}elseif(intval($objectData[$key][$i]) == $objectData[$key][$i]){

									$temp[$i] = intval($objectData[$key][$i]);

								}

							}

						}
						$obj[$key] = $temp;
						break;

						case 'object':
						if(is_array($objectData[$key])){
							$obj[$key] = $objectData[$key];

							if($objectType != 'invoice'){

								foreach($obj[$key] as $innerKey => $innerDatum){

									$obj[$key][$innerKey] = $this->parseArrayForInput($innerDatum);

								}

							}

						}
						break;

						case 'string':

						if(is_string($objectData[$key])){
							$obj[$key] = trim($objectData[$key], "'");
						}
						break;

						default:
						$obj[$key] = $objectData[$key];
						break;

					}

				}elseif($key !== 'id' and $key !== 'date_created' and $newObj == 1){

					switch($field['type']){

						case 'date':
						$date = new DateTime($objectData[$key]);
						$obj[$key] = $date->format('Y-m-d H:i:s');
						break;

						case 'float':
						case 'int':
						case 'usd':
						case 'objectId':
						case 'parentId':
						$obj[$key] = 0;
						break;

						case 'list':
						case 'object':
						$obj[$key] = array();
						break;

						case 'string':
						$obj[$key] = '';
						break;

						default:
						$obj[$key] = $objectData[$key];
						break;

					}

				}elseif($key == 'date_created' and $newObj){

					$date = new DateTime($objectData[$key]);
					$obj[$key] = $date->format('Y-m-d H:i:s');

				}
			}

			$now = new DateTime();
			$obj['last_updated'] = $now->format('Y-m-d H:i:s');
			$obj['last_updated_by'] = intval($_COOKIE['uid']);

			return $obj;

		}

		private function parseArrayForInput($data){

			if(is_array($data)){

				foreach($data as $key => $datum){

					$data[$key] = $this->parseArrayForInput($datum);

				}

			}elseif(is_string($data)){

				$data = trim($data, "'");

			}elseif(is_int($data)){

				$data = preg_replace("/[^0-9]/", "", $data);

			}elseif(is_float($data)){

				$data = $objectData[$key];

			}else{

				$data = $objectData[$key];

			}

			return $data;

		}

	}
