<?php

// !1891: Reference for caldav integration process

/*error_reporting(E_ALL);
ini_set('display_errors', '1');*/


// Error reporting interferes with ics file format so it has to be turned off
error_reporting(0);

header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With, X-Body-Signature, Body-Signature, body-signature");
header('Access-Control-Allow-Credentials: true');
define( "APP_ROOT", $_SERVER["DOCUMENT_ROOT"] . '/api/' );


//date_default_timezone_set('America/Chicago');
date_default_timezone_set('UTC');
	
require APP_ROOT.'DBPATH.php';

require_once APP_ROOT.'lib/_.php';
require_once APP_ROOT.'vendor/autoload.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
// require_once APP_ROOT.'../files/_fileApi.php';
require_once APP_ROOT.'_excel.php';

//set correct content-type-header
header('Content-type: text/calendar; charset=utf-8');
header('Content-Disposition: inline; filename=calendar.ics');

function dateToCal($time) {
	return date('Ymd\THis', $time) . 'Z';
}


$GLOBALS['ROUTE_FROM_GET_EVENTS']=true;

// Grabbing instance name
$instanceName = $_GET['i'];

// Establishing db connection
$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");

$pgObjects = new pgObjects($pdo, $instanceName);

// Builidng query for db call
$queryObj = [
	'group_type' => 'Project'
	, 'is_template' => 0
];

// Identifying layer
$events = array();

if (intval($_GET['id'])) {
	
	$queryObj['tagged_with'] = [intval($_GET['id'])];
	
}

// Fetching all projects on layer
$projects = $pgObjects->where('groups', $queryObj,
				'',
				0,
				false,
				0,
				'null',
				'asc',
				100,
				null,
				[],
				'string',
				$instanceName
			);

$projects = __::filter($projects, function($project) {
	return $project['start_date'] != '';
});

foreach($projects as $project) {

	$startDate = new DateTime($project['start_date']);
	$endDate   = new DateTime($project['end_date']);

	$eventLoc = array();
	
	if (!empty($project['locations'])) {
		
		$eventLoc = $pgObjects->getById('staff_base', intval($project['locations'][0]), 1);
		
	}

	$event                = array();
	
	$event['id']          = $project['id'];
	$event['name']        = $project['name'];
	$event['start']       = dateToCal( intval($startDate->format('U')) );
	$event['end']         = dateToCal( intval($endDate->format('U')) );
	$event['location']    = $eventLoc['name'];
	$event['description'] = $project['description'];
	
	array_push($events, $event); 	
			
}

$ical = "BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//hacksw/handcal//NONSGML v1.0//EN
CALSCALE:GREGORIAN\n\n";

foreach($events as $event) {
	
$ical .= "BEGIN:VEVENT
DTSTART:" . $event['start'] . "
UID:". md5($event['name']) ."
DTSTAMP:". time() ."
LOCATION:". addslashes($event['location']) ."
DESCRIPTION:" . addslashes($event['description']) . "
URL;VALUE=URI: http://mydomain.com/events/" . $event['id'] . "
SUMMARY:" . addslashes($event['name']) . "
DTEND:". $event['end'] ."
END:VEVENT\n\n";
		
}

$ical .= "END:VCALENDAR";

echo $ical;

/*
$event;
$event2;

$event['id'] = 1234567;
$event['title'] = 'This is another title';
$event['description'] = 'This is a test';
$event['datestart'] = 1595894400;
$event['dateend'] = 1595980800;

$event2['id'] = 763867;
$event2['title'] = 'This is a title';
$event2['description'] = 'This is a test';
$event2['datestart'] = 1595894400;
$event2['dateend'] = 1595980800;

// Build the ics file
$ical = 'BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//hacksw/handcal//NONSGML v1.0//EN
CALSCALE:GREGORIAN

BEGIN:VEVENT
DTEND:' . dateToCal($event['dateend']) . '
UID:' . md5($event['title']) . '
DTSTAMP:' . time() . '
LOCATION:' . addslashes('123 Fake St, MyCity, State 12345') . '
DESCRIPTION:' . addslashes($event['description']) . '
URL;VALUE=URI: http://mydomain.com/events/' . $event['id'] . '
SUMMARY:' . addslashes($event['title']) . '
DTSTART:' . dateToCal($event['datestart']) . '
END:VEVENT

BEGIN:VEVENT
DTEND:' . dateToCal($event2['dateend']) . '
UID:' . md5($event2['title']) . '
DTSTAMP:' . time() . '
LOCATION:' . addslashes('123 Fake St, MyCity, State 12345') . '
DESCRIPTION:' . addslashes($event2['description']) . '
URL;VALUE=URI: http://mydomain.com/events/' . $event2['id'] . '
SUMMARY:' . addslashes($event2['title']) . '
DTSTART:' . dateToCal($event2['datestart']) . '
END:VEVENT

END:VCALENDAR';

var_dump($ical);
*/

//echo $ical; 
			
// Exit script
exit();
?> 