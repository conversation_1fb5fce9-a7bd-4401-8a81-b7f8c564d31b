<?php

// error_reporting(E_ALL);
// ini_set('display_errors', '1');

// Error reporting interferes with ics file format so it has to be turned off
error_reporting(0);

if (isset($_SERVER['HTTP_ORIGIN'])) {
    header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Max-Age: 86400');
}

// header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With, X-Body-Signature, Body-Signature, body-signature");
// header('Access-Control-Allow-Credentials: true');
define( "APP_ROOT", $_SERVER["DOCUMENT_ROOT"] . '/api/' );

//date_default_timezone_set('America/Chicago');
date_default_timezone_set('UTC');
	
require APP_ROOT.'DBPATH.php';

require_once APP_ROOT.'lib/_.php';
require_once APP_ROOT.'vendor/autoload.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
// require_once APP_ROOT.'../files/_fileApi.php';
require_once APP_ROOT.'_excel.php';

//set correct content-type-header
header('Content-type: text/calendar; charset=utf-8');
header('Content-Disposition: inline; filename=calendar.ics');

function dateToCal($time) {
	return date('Ymd\THis', $time) . 'Z';
}

function validateRecord($rec, $map, $keys){

    $validatedKeyCount = 0;
    $validated = false;
    
    foreach( $keys as $k){

        $keyedMap = $map[$k];

        if ( isset($rec[ $keyedMap ]) && !empty( $rec[ $keyedMap ])  ){

            $validatedKeyCount++;
        } 

    }

    if ( $validatedKeyCount == count($keys) ) {
        $validated = true;    
    }

    return $validated;

}

$GLOBALS['ROUTE_FROM_GET_EVENTS']=true;

// Grabbing instance name
$instanceName = $_GET['i'];

// Establishing db connection
$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");

$pgObjects = new pgObjects($pdo, $instanceName);

// Identifying layer
$events = array();

$entity_type = $pgObjects->getById('entity_type', intval($_GET['et']), 0);
$caldav_map = $pgObjects->getById('caldav_map', intval($entity_type['caldav_map']), 0);

$selection = array();
$whereOpts = [
    'is_template' =>  0
    , 'tagged_with' =>  [intval($_GET['id'])]
];

$eventObject = array(
    'name' => ''
    , 'event_start' => ''
    , 'event_end' => ''
    , 'event_organizer' => ''
    , 'event_attendees' => ''
    , 'event_desc' => ''
);

///build up selection object for db call
foreach ($eventObject as $arrkey => $arrval) {

    if ( $arrkey == 'name' ) {

        $selection[$arrkey] = true;

    } else {

        if ( is_string( $caldav_map[$arrkey] ) ){
    
            $selection[$caldav_map[$arrkey]] = true;

            ///if working through event_start, setup where clause for this prop
            if ( $arrkey == 'event_start' ) {

                $start = new DateTime();
                $whereOpts[ $caldav_map[$arrkey] ] = [
                    type => 'on_or_after'
                    , date => $start->format('Y-m-d H:i:s')
                ];

            }
    
        } else if ( is_array( $caldav_map[$arrkey] ) ) {
    
            foreach ( $caldav_map[$arrkey] as $k => $v){
    
                $selection[$v] = true;
                
            }
    
        }

    }

}

$records = $pgObjects->where(
    '#'. $entity_type['bp_name']
    , $whereOpts
    , ''
    , $selection
    , false
    , 0
    , 'null'
    , 'asc'
    , 100
    , null
    , []
    , 'string'
    , $instanceName
);

///sanitize data and then format for CALDAV EVENT
foreach ($records as $rec) {

    ///local validate fn that accepts array of keys to check
    if ( validateRecord( $rec, $caldav_map, ['event_start'] ) ){

        $event = array(
            'name' => ''
            , 'event_start' => ''
            , 'event_end' => ''
            , 'event_organizer' => ''
            , 'event_attendees' => array()
            , 'event_desc' => ''
            , 'url' => ''
            , 'id' => 0
        );

        $array_keys = array_keys($event);

        ///Sanitize data for formatting
        foreach ($array_keys as $key => $val) {

            switch($val){

                case 'event_start':
                case 'event_end':

                    if ( isset($rec[$caldav_map[$val]]) && !empty( $rec[$caldav_map[$val]]) ) {

                        $dateVal= new DateTime( $rec[$caldav_map[$val]] );
                        $event[$val] = dateToCal( intval($dateVal->format('U')) );
                        var_dump();

                    } else {

                        $dateVal= new DateTime( $rec[$caldav_map['event_start']] );

                        $dateVal->add(new DateInterval('PT1H'));
                        $defaultEnd = dateToCal( intval($dateVal->format('U')) );
                        $event[$val]= $defaultEnd;

                    }

                    break;

                    case 'event_organizer':

                    $event[$val] = $rec[ $caldav_map[$val] ]['name'];
                    $event['organizer_email'] = $rec[ $caldav_map[$val] ]['email'];

                    break;

                case 'event_attendees':

                    foreach( $caldav_map[$val] as $k => $v){

                        foreach( $rec[$v] as $ky => $vl){

                            if ( is_array($vl) && isset($vl['object_bp_type']) ) {

                                if ($vl['object_bp_type'] == 'users') {
    
                                    array_push(
                                        $event['event_attendees']
                                        , array(
                                            'name' => $vl['name']
                                            , 'email' => $vl['email']
                                        )
                                    );
    
                                }
                                
                            }


                        }

                    }

                    break;

                case 'event_desc':

                    $event[$val] = '';

                    foreach( $caldav_map[$val] as $k => $v){

                        if ( $entity_type['blueprint'][$v]['fieldType'] == 'detail') {

                            $event[$val] .= $rec[$v];

                        } 

                    }

                    break;

                case 'url':

                    foreach( $caldav_map['event_desc'] as $k => $v){

                        if ( $entity_type['blueprint'][$v]['fieldType'] == 'url') {

                            $event[$val] .= $rec[$v];
                                
                        } 

                    }
                    
                    break;

                case 'id':
                    $event[$val] = $rec['id'];
                    break;
                  
                default:

                    $event[$val] = $rec['name'];

                    break;
                    
            }

        }

        $recUrl = "<a href=\"https://" . $_SERVER['HTTP_HOST'] . '/app/' . $instanceName . '#mystuff&1=mst-' . $entity_type['bp_name'] . '-' . urlencode($entity_type['name']) . "&2=e-" . $rec['id'] . "-" . urlencode($rec['name']) ."\">Click here to view in app</a>";

        $event['event_desc'] .= $recUrl;
                
        array_push($events, $event);

    }

}

$ical = "BEGIN:VCALENDAR
PRODID:-//$instanceName//Calendar//EN
VERSION:2.0
CALSCALE:GREGORIAN
METHOD:PUBLISH
X-WR-CALNAME:My ". $entity_type['name'] ." Calendar
X-WR-TIMEZONE:America/New_York\n";

foreach($events as $event) {

$attendeeList = "";

foreach( $event['event_attendees'] as $attend){
    $attendeeList .= "ATTENDEE;CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;PARTSTAT=ACCEPTED;CN=\"". $attend['name']. "\";X-NUM-GUESTS=0:mailto:". $attend['email'] ."\n";
}

$ical .= "BEGIN:VEVENT
DTSTART:" . $event['event_start'] . "
DTEND:". $event['event_end'] . "
DTSTAMP:". dateToCal( time() ) . "
UID:". md5($event['id']) . "
CREATED:". dateToCal( time() ) . "
DESCRIPTION:" . addslashes ($event['event_desc']) . "
LAST-MODIFIED:". dateToCal( time() ) . "
SEQUENCE:0
STATUS:CONFIRMED
SUMMARY:" . addslashes($event['name']) . "
TRANSP:OPAQUE
ORGANIZER;CN=\"". addslashes($event['event_organizer']) ."\":mailto:". $event['organizer_email']. "
URL;VALUE=URI:" . $event['url']. "
". $attendeeList . "
END:VEVENT\n";

}

$ical .= "END:VCALENDAR";


echo $ical;

exit();

?> 