<?php

require_once 'bento.php';

$bento = new BentoAPI('voltzsoftware', 'fcd24744d947001328300c65c530743b0a3b5b3f4dc0524e053efda172f84a86e140a5f2bf2474d9d0071676c28fb742bfddfb0b80c6416878ef862f7723a3ad');

$created = $bento->create(array(
	'objectType'=>'contacts',
	'objectData'=>array(
		'fname'=>'API',
		'lname'=>'Test'		
	)
));
var_dump($created);
echo '<br /><br />';

$obj = $bento->getById($created['id']);
var_dump($obj);
echo '<br /><br />';

$objs = $bento->getById([$created['id'],1529030]);
var_dump($objs);
echo '<br /><br />';

$updated = $bento->update(array(
	'objectType'=>'contacts',
	'objectData'=>array(
		'id'=>$created['id'],
		'fname'=>'Testing'	
	)
));
var_dump($updated);
echo '<br /><br />';

$deleted = $bento->archive('contacts', $created['id']);
	
?>