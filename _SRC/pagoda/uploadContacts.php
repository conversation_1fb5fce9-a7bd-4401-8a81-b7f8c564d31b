<?php
	
// 	error_reporting(E_ALL);
// 	ini_set('display_errors', '1');
	
	header('Access-Control-Allow-Origin: *');
	echo 'connecting...<br />';
	
	if (extension_loaded ('newrelic')) {
		newrelic_set_appname ("Pagoda");
		newrelic_background_job();
		newrelic_ignore_apdex();
	}
	
	define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
	
	require_once $_SERVER['DOCUMENT_ROOT'].'/app/_app/_config.php';
	require_once APP_ROOT.'_pgObjectsMTAdmin.php';
	require_once APP_ROOT.'_objects.php';
	require_once APP_ROOT.'_communications.php';
	require_once APP_ROOT.'_cookiesPG.php';
	require_once APP_ROOT.'files/_fileApi.php';
	require_once APP_ROOT.'stripe/init.php';
	
	require_once $_SERVER['DOCUMENT_ROOT'].'/app/_app/_config.php';
	
	$objects = new Objects($pdo);
	$pgObjects = new pgObjectsAdmin($pdo, $appConfig['instance']);
	$comms = new Comm($pgObjects, $appConfig);
	$cookies = new Cookies($pgObjects);
	$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey']);
	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
	
	$uploader = new contactsUploader($app, $appConfig, $_REQUEST['uploadId']);
	switch($_REQUEST['action']){
		
		case 'create':
			$uploader->createContactData();
			break;
			
		case 'delete':
			$uploader->deleteContactsFromSource();
			break;
		
	}
	
	class contactsUploader {
		
		// private
		private function create_objs(){
			
			// get companies with the same names
			$already_created_companies = $this->app->pgObjects->where('companies', [
				'name' => [
					'type' => 'or',
					'values' => array_map(function($comp){ return $comp['name']; }, $this->companiesBatch)
				]
			]);
			
			// remove companies from to be created batch if they are already in the db
			if(is_array($this->companiesBatch)){
				
				foreach($this->companiesBatch as $i => $company){
					
					foreach($already_created_companies as $j => $already_created){
						
						if($company['name'] === $already_created['name']){
							
							unset($this->companiesBatch[$i]);
							
						}
						
					}
					
				}
				
			}
			
			// create companies
			$created_companies = array();
			if(count($this->companiesBatch) > 0){
				$created_companies = $this->app->pgObjects->create('companies', $this->companiesBatch, 1, 0);
			}
			
			// add companies created previously to list of companies created just now
			foreach($already_created_companies as $i => $comp){
				
				array_push($created_companies, $already_created_companies[$i]);
				
			}
// 			var_dump($created_companies, $this->contactsBatch);
			// place company ids into contacts
			foreach($this->contactsBatch as $i => $contact){
				
				if(is_array($created_companies)){
					foreach($created_companies as $j => $company){
						
						if($company['name'] === $contact['company']){
							
							$this->contactsBatch[$i]['company'] = $company['id'];
							
						}
						
					}
				}
				
			}
			
			// create contacts
			$created_contacts = $this->app->pgObjects->create('contacts', $this->contactsBatch, 1, 0);
			
			// place contact ids into contact infos
			foreach($this->contactInfoBatch as $i => $contactInfo){
				
				if(is_array($created_contacts)){
					foreach($created_contacts as $j => $contact){
						
						if($contact['data_source_id'] === $contactInfo['data_source_id']){
							
							$this->contactInfoBatch[$i]['object_id'] = $contact['id'];
							
						}
						
					}
				}
				
			}
			
			// create contact info
			$created_contact_infos = $this->app->pgObjects->create('contact_info', $this->contactInfoBatch, 1, 0);
			
			// add contact info array to contact objs
			foreach($created_contacts as $i => $created_contact){
				
				$created_contacts[$i]['contact_info'] = array();
				foreach($created_contact_infos as $j => $created_contact_info){
					
					if($created_contact['data_source_id'] === $created_contact_info['data_source_id']){
						
						array_push($created_contacts[$i]['contact_info'], $created_contact_info['id']);
						
					}
					
				}
				
			}
			
			// update contact objs
			$updated_contacts = $this->app->pgObjects->update('contacts', $created_contact_infos, 0);
			
			// clear batches
			$this->contactsBatch = array();
			$this->companiesBatch = array();
			$this->contactInfoBatch = array();
			
			echo $this->batch_size .' rows processed.<br />';
// 			die();
			
		}
		
		private function get_csv_data(){
			
			$loc = $this->appConfig['files']['bucket'] . str_replace('../../', '', $this->map['file']['loc']);
			$row = 0;
			$csvKeys = array();

			if (($handle = fopen($loc, "r")) !== FALSE) {
				
			    while (($data = fgetcsv($handle, 0, ",")) !== FALSE) {

				    // get csv keys
				    if($row === 0){
					    $csvKeys = $data;
				    }else{
					    $this->parse_csv_row($csvKeys, $data, $row);
				    }
				    
				    // create objs
				    if(count($this->contactsBatch) >= $this->batch_size){
// 					    var_dump($this->contactsBatch, $this->contactInfoBatch);
// 					    die();
					    $this->create_objs();
				    }
				    
			        $row++;

			    }
			    
			    fclose($handle);
			    
			}
			
			echo 'COMPLETE';
// 			var_dump($this->map['translation']);
// 			var_dump($this);
			
		}
		
		private function parse_csv_row($keys, $row, $rowId){

			// parse into temp array with keyed row data
			$pre = array();
			$contactObj = array(
				'data_source' => $this->map['id'],
				'data_source_id' => $rowId
			);
			$companyObj = array(
				'data_source' => $this->map['id'],
				'data_source_id' => $rowId
			);
			$contactInfoObjs = array();

			if(is_array($row)){
				
				foreach($row as $i => $val){
// 					echo $keys[$i];
					$pre[$keys[$i]] = $val;
					
				}
				
			}
			
			// company obj
				// check if company exists with that name, if not, create (or add to batch to be created)
			$companyObj['name'] = $pre[$this->map['translation']['company']];

			// contact obj
			$contactObj['fname'] = $pre[$this->map['translation']['fname']];
			$contactObj['lname'] = $pre[$this->map['translation']['lname']];
			$contactObj['type'] = intval($this->map['translation']['type']);
			$contactObj['company'] = $pre[$this->map['translation']['company']];
			
			$contactObj['data_source'] = intval($this->map['id']);
			
			$contactObj['manager'] = 0;
			$contactObj['stripe_id'] = '';
			$contactObj['available_types'] = array();
			
			// contact info objs
			if(is_array($this->map['translation']['available_types'])){
				
				foreach($this->map['translation']['available_types'] as $i => $infoMap){
					
					switch($infoMap['data_type']){
						
						case 'address':
						
							array_push($contactInfoObjs, array(
								
								'data_source' => $this->map['id'],
								'data_source_id' => $rowId,
								'type' => intval($infoMap['type']),
								'is_primary' => $infoMap['is_primary'],
								'street' => $pre[$infoMap['street']],
								'city' => $pre[$infoMap['city']],
								'state' => $pre[$infoMap['state']],
								'zip' => $pre[$infoMap['zip']],
								'country' => $pre[$infoMap['country']]
								
							));
							
							break;
							
						default:
							
							array_push($contactInfoObjs, array(
								
								'data_source' => $this->map['id'],
								'data_source_id' => $rowId,
								'type' => intval($infoMap['type']),
								'is_primary' => $infoMap['is_primary'],
								'info' => $pre[$infoMap['info']]
								
							));
							
							break;
						
						
					}
// 					var_dump($infoMap);
					
				}
				
			}
// 			die();
// 			var_dump($pre);
			
			array_push($this->contactsBatch, $contactObj);
			array_push($this->companiesBatch, $companyObj);
			foreach($contactInfoObjs as $i => $contactInfoObj){
				array_push($this->contactInfoBatch, $contactInfoObj);
			}
// 			var_dump($contactObj);
// 			var_dump($contactInfoObjs);
// 			var_dump($companyObj);			
			
		}
		
		private function update_status($newStatus){
			
			$this->app->pgObjects->update('csv_upload', [
				'id' => $this->map['id'],
				'status' => $newStatus
			]);
			
		}
		
		// public
		public function __construct($app, $config, $transferId){
			
			//$transferId = 952052;
			$this->start_time = date('Y-m-d H:i:s');
			$this->batch_size = 175;
// 			$this->batch_size = 2;
			$this->app = $app;
			$this->map = $this->app->pgObjects->getById('csv_upload', intval($transferId), 1);
			$this->appConfig = $config;
			$this->notify = $this->map['created_by']['email'];
			
			// object batches for db
			$this->contactsBatch = array();
			$this->companiesBatch = array();
			$this->contactInfoBatch = array();
			
		}
		
		public function createContactData(){

			switch($this->map['status']){
				
				case 'not_started':
					$this->update_status('started');
					$this->get_csv_data();
					$this->update_status('complete');
					$this->app->sendEmail($this->notify, null, 'Contacts upload completed!', [
						'TITLE' => 'Contacts upload completed!',
						'BODY' => 'Your contacts upload has been completed! (started at '. $this->start_time .' and completed at '. date('Y-m-d H:i:s') .')',
						'BUTTON' => ''
					], null, 0);
					die();
					break;
					
				case 'started':
					break;
					
				case 'complete':
					break;
					
				case 'deleted':
					break;
				
				default:
					break;
				
			}
			
		}
		
		public function deleteContactsFromSource(){
			
			if($this->app->pgObjects->deleteWhere('contacts', ['data_source' => $this->map['id']])){
				
				$this->app->pgObjects->deleteWhere('companies', ['data_source' => $this->map['id']]);
				$this->app->pgObjects->deleteWhere('contact_info', ['data_source' => $this->map['id']]);
				
				return true;
				
			}else{
				
				return false;
				
			}
			
		}
		
	}
	
?>