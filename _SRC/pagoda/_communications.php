<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

use \DrewM\MailChimp\MailChimp;
use Twilio\Rest\Client;

class Comm {

	protected $twilio = null;
	protected $mandrill = null;
	protected $systemStatus = 2;
	protected $testEmail = "<EMAIL>";

	function __construct($objects, $appConfig){

		$this->appConfig = $appConfig;

		if ($this->appConfig == null) {
			$this->appConfig = [];
		}

		$this->sid = $this->appConfig['twilio']['sid'];
		$this->token = $this->appConfig['twilio']['token'];

		// production
// 		$sid = "**********************************";
// 		$token = "631fcf76d6ddc06a19f3aa3d2c947b66";

		if($this->appConfig['instance'] == 'thelifebook'){
			$this->sid = '**********************************';
			$this->token = '631fcf76d6ddc06a19f3aa3d2c947b66';
			$this->appConfig['twilio']['smsFrom'] = '+***********';
		}

		if($this->sid and $this->token){
			$client = new Client($this->sid, $this->token);
			$this->twilio = $client;
		}

		if(!$this->appConfig['mailchimp_api']){
			$mailchimpAPIKey = '************************************'; // bento key
		}else{
			$mailchimpAPIKey = $this->appConfig['mailchimp_api'];
		}

		$this->mailchimp = new MailChimp($mailchimpAPIKey);

		if(!$this->appConfig['mandrill_api']){
			$mandrilAPIKey = '**********************'; // bento key
		}else{
			$this->appConfig['mandrill_api'] = '**********************'; // bento key
			$mandrilAPIKey = $this->appConfig['mandrill_api'];
		}

		$this->mandrill = new Mandrill($mandrilAPIKey);

		$this->objects = $objects;

		if (empty($this->appConfig)) {
			$this->appConfig = [
				'mandrill_api' => '**********************'
			];
		}

		return true;
	}


	// MAILCHIMP V3.0 METHODS
	public function mailchimpAPI($method, $url, $obj = null){

		if($obj == null){

			$ret = $this->mailchimp->{$method}($url);

		}else{

			$ret = $this->mailchimp->{$method}($url, $obj);

		}

		return $ret;

	}

	public function checkMailchimpAPIKey($key){

		$newMC = new MailChimp($key);

		$result = $newMC->get('lists');

		return $result;

	}

	public function createMailchimpCompany($storeId, $storeName, $listId, $domain, $email, $currency){

		$store = array(
				'id' => $storeId,
				'list_id' => $listId,
				'name' => $storeName,
				'domain' => $domain,
				'email_address' => $email,
				'currency_code' => $currency
			);

		$result = $this->mailchimp->post("ecommerce/stores", $store);

		return $result;

	}

	public function createMailchimpCustomer($storeId, $contactObj){

		$contactObj->id = strval($contactObj->id);

		$result = $this->mailchimp->post("ecommerce/stores/$storeId/customers/", json_decode(json_encode($contactObj, true)));

		return $result;

	}

	public function createMailchimpCustomerOrder($storeId, $order){

		$order->id = strval($order->id);
		$order->customer->id = strval($order->customer->id);

		$result = $this->mailchimp->post("ecommerce/stores/$storeId/orders/", json_decode(json_encode($order), true));

		return $result;

	}

	public function createMailchimpListMember($listId, $contactObj){

		$result = $this->mailchimp->post("lists/$listId/members", json_decode(json_encode($contactObj), true));

		return $result;

	}

	public function createMailchimpProduct($storeId, $productId, $title){

		$product = array(
				'id' => $productId,
				'store_id' => $storeId,
				'title' => $title,
				'variants' => array(
					array(
						'id' => $productId,
						'title' => $title
					)
				)
			);

		$result = $this->mailchimp->post("ecommerce/stores/".$storeId.'/products', $product);

		return $result;

	}

	public function patchMailchimpCustomerOrder($storeId, $orderId, $order){

		$result = $this->mailchimp->patch("ecommerce/stores/$storeId/orders/$orderId", json_decode(json_encode($order), true));

		return $result;

	}

	public function patchMailchimpListMember($listId, $contactObj){

		$contactHash = $this->mailchimp->subscriberHash($contactObj->email_address);

		$result = $this->mailchimp->patch("lists/$listId/members/$contactHash", json_decode(json_encode($contactObj), true));

		return $result;

	}

	public function removeMailchimpCustomerOrder($storeId, $orderId){

		$result = $this->mailchimp->delete("ecommerce/stores/$storeId/orders/$orderId");

		return $result;

	}

	public function removeMailchimpListMember($listId, $contactObj){

		$contactHash = $this->mailchimp->subscriberHash($contactObj->email_address);

		$result = $this->mailchimp->delete("lists/$listId/members/$contactHash", json_decode(json_encode($contactObj), true));

		return $result;

	}



	public function createEmailAddress($church_id = null, $domain_name = "my"){

		if($church_id == null){
			$church_id = "test";
		}

		try {
		    $domain = $domain_name.'.homechurch.tv';
		    $pattern = $church_id;
		    $url = 'http://homechurch.tv/_bot/_incomingEmail.php';
		    $result = $this->mandrill->inbound->addRoute($domain, $pattern, $url);
		    //print_r($result);
		    /*
		    Array
		    (
		        [id] => 7.23
		        [pattern] => mailbox-*
		        [url] => http://example.com/webhook-url
		    )
		    */
		} catch(Mandrill_Error $e) {
		    // Mandrill errors are thrown as exceptions

		    $result = $e->getMessage();
		}

		return $result;

	}

	public function makePhoneCall($to, $from = '+***********'){

		$call = $this->twilio->account->calls->create($from, $to, "http://thebridge.thelifebook.com/new/_callController.xml", array());
		return $call->sid;

	}

	public function saveMandrillEmail($type, $typeId, $mandrillId, $to, $from, $subject, $message, $threadId, $clicked, $taggedWith){

		$toAddresses = '';

		if (is_array($to)) {

			foreach ($to as $i => $email) {

				switch ($email['type']) {

					case 'to':
						$toAddresses .= $email['email'];
						break;

					default:
						$toAddresses .= $email['email'];
				}

			}
		} else {
			$toAddresses = $to;
		}

		if (is_array($typeId) && $typeId['id']) {
			$typeId = $typeId['id'];
		}

		if($newEmail = $this->objects->create('emails', array(
			'type' => 			$type,
			'type_id' => 		$typeId,
			'mandrill_id' => 	$mandrillId,
			'to' => 			$toAddresses,
			'from' => 			$from,
			'subject' => 		$subject,
			'message' => 		$message,
			'thread_id' => 		intval($threadId),
			'clicked' => 		$clicked,
			'tagged_with' => 	$taggedWith
		))){

			return $newEmail;

		}

	}

	public function sendSMS($to, $message, $from = null){

		$to = "+1".$to;

		if($from != null){
			$fromNumber = $from;
		}else{
			$fromNumber = $this->appConfig['twilio']['smsFrom'];
		}

		try{

			$this->twilio->messages->create(
		        $to,
		        array(
		            "from" => $fromNumber,
		            "body" => $message
		        )
		    );

			return true;

		}catch(Exception $e){

			return 'Number has been blacklisted';

		}

	}

	public function sendEmail($to, $from, $subject, $mergevars, $email_tags){

		$email_template = 'newbootstrap';
		$smtp_host = "smtp.mandrillapp.com";
		$smtp_port = 587;
		$smtp_uid = "<EMAIL>";
		$smtp_pwd = "QUHS2qv9VF1rU3J_OTVJcA";

		$bodyText = $mergevars->body;

		$mergevars = json_encode($mergevars);

		$transport = new Swift_SmtpTransport($smtp_host, $smtp_port);
		$transport->setUsername($smtp_uid);
		$transport->setPassword($smtp_pwd);

		$swift = Swift_Mailer::newInstance($transport);

		if ($msg_text=""){ $msg_text = $msg_html; }

		$message = new Swift_Message($subject);
		$message->setFrom($from);
		$message->setTo($to);
		$headers = $message->getHeaders();
		$headers->addTextHeader('X-MC-Tags', $email_tags);
		$headers->addTextHeader('X-MC-Track', 'opens, clicks_htmlonly');
		$headers->addTextHeader('X-MC-GoogleAnalytics', 'thelifebook.com');
		$headers->addTextHeader('X-MC-AutoText', true);
		$headers->addTextHeader('X-MC-Template', $email_template);
		$headers->addTextHeader('X-MC-MergeVars', $mergevars);

		if ($recipients = $swift->send($message, $failures)) {

			return $recipients[0]['_id'];

		} else {

			return false;

		}

		return false;

	}

	public function sendMandrillEmail($to, $from, $subject, $mergevars, $email_tags, $typeId = null, $save = 0, $type = 0, $newThread = false, $threadId = null, $instance, $clicked = 0, $taggedWith){

		$email_template = 'bootstrap';
		$template_content = array();
		$mergevars->INSTANCE_NAME = $this->appConfig['systemName'];
		$merge = array();
		$preserve_recipients = null;
		$emailBody = '';

        /*
            Instance needs to send emails from DBA name (Dream Catering instance, but also DOES BUSINESS AS Daily Dish)
            Feature requires a {object_bp_type: 'contract_system'} (for email body template)
            Daily Dish Event Mangement - Project Type only
        */

        if ( $this->appConfig['instance'] == 'dreamcatering') {

            ///find contract to get its type
            $contract = $this->objects->getById('', intval($typeId));
            ///check project type to determine if its a Daily Dish or Dream workflow
            $project = $this->objects->getById('', intval($contract['parent']));

            /// {id: 12033227} - Daily Dish Project Type on Production, {id: 12261255}-Daily Dish ProjectType on Dev
            if ($project['type'] == 12033227 || $project['type'] == 12261255) {
                $mergevars->INSTANCE_NAME = 'Daily Dish';
            }

        }

		if (is_array($mergevars)) {
			$mergevars->INSTANCE_NAME = $this->appConfig['systemName'];
		}

		if ($mergevars->INSTANCE_NAME === null) {
			$mergevars->INSTANCE_NAME = 'Bento';
		}

		foreach($mergevars as $name => $content){

			if ($name === 'BODY') {
				$emailBody = $content;
			}
			array_push($template_content, array('name'=>$name, 'content'=>$content));

		}

		$sendTo = array();

		if(is_array($to)){

			foreach($to as $email){

				if (is_array($email)) {

					$preserve_recipients = true;
					array_push($sendTo, $email);

				} else {

					array_push($sendTo, array('email' => $email));

				}

			}

		}else{

			array_push($sendTo, array('email' => $to));

		}

		foreach($mergevars as $name => $content){

			array_push($merge, array('name'=>$name, 'content'=>$content));

		}


		$tagArray = array();

		foreach($email_tags as $tag) {
			array_push($tagArray, $tag);
		}

		foreach($taggedWith as $tag) {
			array_push($tagArray, $tag);
		}

		$tagArray = array_unique($tagArray);

		if($newThread){
			$thread = $this->createThread($type, $typeId, $_COOKIE['uid'], $to);
			$threadId = $thread['id'];
		}else{
			$thread = $this->objects->getById('threads', intval($threadId));
		}

		if(!$mergevars->BUTTON_LINK){
			$email_template = 'text';
		}

		// $threadIdHash = '#'. openssl_encrypt($threadId, 'aes-256-cbc', 'email-thread-id');
		$threadIdHash = '';
		$fromEmailAddress = explode('@', $this->appConfig['emailFrom'])[0] . $threadIdHash .'@'. explode('@', $this->appConfig['emailFrom'])[1];

		if($from){
			$fromEmailAddress = $from;
		}

		$message = array(
			'subject' => $subject,
			'from_email' => $fromEmailAddress,
	        'from_name' => $mergevars->INSTANCE_NAME,
	        'to' => $sendTo,
	        'headers' => array('Reply-To' => $fromEmailAddress),
	        'important' => false,
	        'track_opens' => null,
	        'track_clicks' => null,
	        'auto_text' => null,
	        'auto_html' => null,
	        'inline_css' => null,
	        'url_strip_qs' => null,
	        'preserve_recipients' => $preserve_recipients,
	        'view_content_link' => null,
	        'bcc_address' => null,
	        'tracking_domain' => null,
	        'signing_domain' => null,
	        'return_path_domain' => null,
	        'merge' => true,
	        'merge_language' => 'mailchimp',
	        'global_merge_vars' => $merge,
	        'tags' => $tagArray
	    );


	    $async = false;
	    $ip_pool = null;
	    $send_at = null;

		try{

			//$mandrill = new Mandrill($this->appConfig['mandrill_api']);

			$response = $this->mandrill->messages->sendTemplate($email_template, $template_content, $message, $async, $ip_pool, $send_at);

			if($save == 1){

// 				$this->addListenerToThread($type, $typeId, $to);

				$staffMemberInfo = $this->objects->getById('staff', intval($_COOKIE['uid']));
// 				$this->addListenerToThread($type, $typeId, $staffMemberInfo['email']);
// 				$this->addListenerToThread($type, $typeId, $to);

// 				$thread = $this->objects->where('threads', array('type' => $type, 'typeId' => intval($typeId)))[0];
                if ( $thread['listeners'] !== null ) {
                    foreach($thread['listeners'] as $listener){

                        $send = $message;
                        if(is_numeric($listener)){
                            $listener = $this->objects->getById('staff', intval($listener))['email'];

                            if($type !== 'general'){

                                switch($type){

                                    case 'client':
                                    $objData = $this->objects->getById('clients', intval($typeId));
                                    $send['subject'] = $send['subject'] .' (Client: '. $objData['fname'] .' '. $objData['lname'] .')';
                                    break;

                                    case 'event':
                                    $objData = $this->objects->getById('events', $typeId);
                                    $send['subject'] = $send['subject'] .' (Event: '. $objData['event_name'] .')';
                                    break;

                                    case 'staff':
                                    $objData = $this->objects->getById('staff', $typeId);
                                    $send['subject'] = $send['subject'] .' (Staff Member: '. $objData['fname'] .' '. $objData['lname'] .')';
                                    break;

                                    default:
                                    break;

                                }

                            }

                        }else{

                            if($from != $listener and !in_array($listener, explode(',', str_replace(' ', '', $to)))){

                                $message['to'] = array(array('email' => $listener));
                                $this->mandrill->messages->sendTemplate($email_template, $template_content, $send, $async, $ip_pool, $send_at);

                            }

                        }

                    }
                }
				// save the message details
			    return $this->saveMandrillEmail(
			    	$type
			    	, $typeId
			    	, $response[0]['_id']
			    	, $message['to']
			    	, $message['from_email']
			    	, $message['subject']
			    	, $emailBody
			    	, $threadId
			    	, $clicked
			    	, $tagArray
			    );

			}

			return $response[0]['_id'];

		}catch(Mandrill_Error $e) {
		    // Mandrill errors are thrown as exceptions
		    return 'A mandrill error occurred: ' . get_class($e) . ' - ' . $e->getMessage();
		    // A mandrill error occurred: Mandrill_Unknown_Subaccount - No subaccount exists with the id 'customer-123'
		    throw $e;
		}

	}

    public function setInstance($instance){

        $this->instance = $instance;

        return true;

    }

	public function subscribeMailchimpClient($listId, $emailAddress, $mergeTags = array(), $doubleOptIn = false){

		$response = $this->mailchimp->post("lists/$listId/members", [
				'email_address' => $emailAddress,
				'status'        => 'subscribed'
			]);

		$subscriber_hash = $this->mailchimp->subscriberHash($emailAddress);

		$result = $MailChimp->patch("lists/$listId/members/$subscriber_hash", [
				'merge_fields' => $mergeTags
			]);

		return $result;

	}

	public function unsubscribeMailchimpClient($listId, $emailAddress){

		$subscriber_hash = $this->mailchimp->subscriberHash($emailAddress);

		$response = $this->mailchimp->delete("lists/$listId/members/$subscriber_hash");

		return $response;

	}

	public function addListenerToThread($type, $typeId, $email){

		if($thread = $this->objects->where('threads', array('type' => $type, 'typeId' => intval($typeId)))[0]){

			if($staffInfo = $this->objects->where('staff', ['email' => $email])[0]){
				$email = $staffInfo['id'];
			}

			if(!in_array($email, $thread['listeners'])){

				array_push($thread['listeners'], $email);

				if($update = $this->objects->update('threads', array('id' => $thread['id'], 'listeners' => $thread['listeners']))){

					return $update;

				}else{

					return false;

				}

			}else{

				return $thread;

			}

		}else{

			if($thread = $this->objects->create('threads', array(
				'type' => $type,
				'typeId' => $typeId,
				'listeners' => array($email)
			))){

				return $thread;

			}else{

				return false;

			}

		}

	}

	protected function createThread($type, $typeId, $userId, $to){

		$listeners = explode(',', str_replace(' ', '', $to));
		array_push($listeners, intval($userId));

		$create = array(
			'listeners' => $listeners,
			'type' => 'email',
			'object_type' => $type,
			'object_id' => intval($typeId)
		);

		if($threadObj = $this->objects->create('threads', $create)){

			return $threadObj;

		}else{

			return false;

		}

// 		$threadHash = openssl_encrypt($type.'-'.$typeId, 'aes-256-cbc', 'email-thread-id');

// 		return $threadId;

	}

	// returns status or error
	protected function setSystemStatus(){
		require_once '_db.php';
		$data = $mysqli->query("select email from application_settings");
		if($status = $data->fetch_assoc()){
			$this->systemStatus = $status['email'];
			return $this->systemStatus;
		}else{
			return $mysqli->error_get_last();
		}
	}

}
