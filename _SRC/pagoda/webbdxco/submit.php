<?php

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
require_once 'bento.php';

$bento = new BentoAPI('wdtech', '52fa709c6fd4263aa8db0816758f74d21b0a955b06ca3d7cf5b0c6c56c27def8d2aabcc887deac80ae9fcd0ba9ad4f462216ec858602d6c522a458d2cbee535f');

$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, TRUE);

$created = $bento->create(array(
	'objectType'=>'#Form_Submission',
	'objectData'=>array(
		'name'=>$input['Name'],
		'_1'=>$input['Name'],
		'_2'=>$input['Email'],
		'_3'=>$input['Phone'],
		'_4'=>$input['City'],
		'_6'=>$input['Note'],
		'tagged_with'=>array(1813005)
	)
));

var_dump($created);
	
?>