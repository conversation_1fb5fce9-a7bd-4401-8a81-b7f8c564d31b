<?php

/*
	header('Access-Control-Allow-Origin: *');
	echo 'connecting...<br />';
	// dev
// 	$postgres = new PDO("pgsql:host=localhost;port=5432;dbname=infinity;user=postgres;password=**************");
	//  production
	$postgres = new PDO("pgsql:host=localhost;dbname=admin_binpostgres", 'binpostgres', 'Ub55a3_b');
	$app = new pgObjects($postgres);

	error_reporting(E_ALL);
	ini_set('display_errors', '1');

	echo $app->test();
*/

	class pgObjects {

		private $db;
		private $debug = false;

		function __construct($dbConn, $instance = null, $rules = null, $dbWriteConn = null){

			$this->db = $dbConn;

			if($dbWriteConn){
				$this->write = $dbWriteConn;
			}else{
				$this->write = $dbConn;
			}

		}

		public function test(){
			var_dump($this->where('staff', array('email' => '<EMAIL>')));
/*
			var_dump($this->update('clients', array(
				'id' => 1740,
				'fname' => 'updated',
				'status' => 1
			)));
*/
/*
			var_dump($this->create('clients', array(
				'fname' => 'another',
				'lname' => 'one'
			)));
*/
// 			var_dump($this->getSingle('clients', 1));
/*
			var_dump($this->where('clients', [
				'id' => 2,
				'checks_allowed' => 0
			]));
*/
// 			echo $this->delete('clients', 1741);

		}

		public function create($objectType, $objectData = null, $multiple = 0, $retChildObjs = 0){

			if($_POST->multiple){
				$multiple = $_POST->multiple;
			}

			if($multiple == 0){

				$statement = $this->write->prepare(
					"INSERT INTO $objectType DEFAULT VALUES RETURNING *;"
				);

				if($statement->execute()){

					$info = $statement->fetchAll()[0];

					// parse object data
					if(is_array($objectData)){

						$obj = $this->parseInput($objectType, $objectData, array(), 1);

						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];

						$string = "UPDATE $objectType SET object_data = '". trim($this->write->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;";
// 						$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] ." RETURNING *;";

						$statement = $this->write->prepare($string);
						if($statement->execute()){

							return $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0];

						}else{

							return $this->write->errorInfo();

						}

					}else{

						$obj = $this->parseInput($objectType, array(), array(), 1);
						$obj['id'] = intval($info['id']);
						$obj['date_created'] = $info['date_created'];

						$string = "UPDATE $objectType SET object_data = '". trim($this->write->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] .";";
// 						$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] .";";

						$statement = $this->write->prepare($string);
						if($statement->execute()){

							return $info['id'];

						}elseif($this->debug){

							return $this->write->errorInfo();

						}

					}


				}else{

					return $this->write->errorInfo();

				}

			}else{

				$objectDataArray = $objectData;

				$returnArray = [];

				foreach($objectDataArray as $objectData){

					$statement = $this->write->prepare(
						"INSERT INTO $objectType DEFAULT VALUES RETURNING *;"
					);

					if($statement->execute()){

						$info = $statement->fetchAll()[0];

						// parse object data
						if(is_array($objectData)){

							$obj = $this->parseInput($objectType, $objectData, array(), 1);

							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];

							$string = "UPDATE $objectType SET object_data = '". trim($this->write->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] ." RETURNING *;";
// 							$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] ." RETURNING *;";

							$statement = $this->write->prepare($string);
							if($statement->execute()){

								array_push($returnArray, $this->parseData($statement->fetchAll(), $retChildObjs, $objectType)[0]);

							}

						}else{

							$obj = $this->parseInput($objectType, array(), array(), 1);
							$obj['id'] = intval($info['id']);
							$obj['date_created'] = $info['date_created'];

							$string = "UPDATE $objectType SET object_data = '". trim($this->write->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $info['id'] .";";
// 							$string = "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $info['id'] .";";

							$statement = $this->write->prepare($string);
							if($statement->execute()){

								array_push($returnArray, ['id' => $info['id']]);

							}

						}

					}

				}

				return $returnArray;

			}

		}

		public function delete($objectType, $objectId){

			$statement = $this->db->prepare(
				"DELETE FROM $objectType WHERE id = :objectId;"
			);

			if($statement->execute([':objectId' => $objectId])){

				return true;

			}elseif($this->debug){

				var_dump($statement->errorInfo(), $statement);
				return false;

			}

		}

		public function getAll($objectType, $getChildObjects = 0){

			$statement = $this->db->prepare(
				"SELECT * FROM $objectType;"
			);

			if($statement->execute()){

				$data = $statement->fetchAll();
				return $this->parseData($data, $getChildObjects, $objectType);


			}elseif($this->debug){

				return $statement->errorInfo();

			}

		}

		public function getBatch($objectType, $batch){

		}

		public function getCount($objectType, $whereClause){

			$statement = $this->db->prepare("SELECT count(*) FROM $objectType $whereClause;");

			if($statement->execute()){

				return $statement->fetchAll()[0]['count'];

			}else{

				return $statement->errorInfo();

			}

		}

		public function getDistinct($objectType, $byProperty){

			$statement = $this->db->prepare(
				"SELECT DISTINCT ON (object_data->>'$byProperty') * FROM $objectType;"
			);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), 0, $objectType);

			}else{
				var_dump($statement->errorInfo(), $statement);
				return $statement->errorInfo();

			}

		}

		public function getById($objectType, $objectId, $childObjs = 0){

			$statement = $this->db->prepare(
				"SELECT * FROM $objectType WHERE id = :objectId;"
			);

			if($statement->execute([':objectId' => $objectId])){
/*
if($_COOKIE['uid'] == 1){
	var_dump($statement->fetchAll());
}
*/
				return $this->parseData($statement->fetchAll(), $childObjs, $objectType)[0];

			}elseif($this->debug){

				return $statement;

			}

		}

		public function getWhere($objectType, $whereClause){

			$statement = $this->db->prepare(
				"SELECT * FROM $objectType $whereClause;"
			);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), 0, $objectType);

			}elseif($this->debug){
				var_dump($statement->errorInfo(), $statement);
				return $statement->errorInfo();

			}

		}

		public function getWith($objectType, $selectionArr, $childObj, $childObjKey){

			$whereClause = "";
			$i=0;
			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					if($i == 0){
						$whereClause .= "a.object_data->>'$key' = '". trim($val) ."'";
					}else{
						$whereClause .= " and a.object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i == 0){
							$whereClause .= "(a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}else{
							$whereClause .= " and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}

					}

				}else{

					if($i == 0){
						$whereClause .= "a.object_data->>'$key' = '". trim($val) ."'";
					}else{
						$whereClause .= " and a.object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}

			$selectString =

				"SELECT a.object_data,  array_agg(b.object_data) as child_data ".
				"FROM $objectType a ".
				"LEFT JOIN $childObj b ON b.object_data->>'$childObjKey' = a.object_data->>'id' WHERE $whereClause ".
				"GROUP BY a.object_data;";

			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				$rawData = $statement->fetchAll();
				$ret = [];
				$i=0;

				if(is_array($rawData)){
					foreach($rawData as $rawDatum){

						$ret[$i] = json_decode($rawDatum['object_data'], true);
						$temp = json_decode("[". trim($rawDatum['child_data'], "{}") ."]", true);

						$j=0;
						if(is_array($temp)){
							foreach($temp as $tempData){

								$temp[$j] = json_decode($tempData, true);
								$j++;

							}
						}
						$ret[$i][$childObj] = $temp;
						$i++;

					}
				}

				return $ret;

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function update($objectType, $objectData, $childObjs = 0){

			$blueprint = $this->getBlueprint($objectType);
			$old = $this->getById($objectType, $objectData['id']);
			$obj = $this->parseInput($objectType, $objectData, $old);

			$statement = $this->db->prepare("UPDATE $objectType SET object_data = '". trim($this->db->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $objectData['id'] ." RETURNING *;");
// 			$statement = $this->db->prepare("UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $objectData['id'] ." RETURNING *;");

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), $childObjs, $objectType)[0];

			}elseif($this->debug){

				var_dump($statement, $statement->errorInfo(), "UPDATE $objectType SET object_data = '". trim($this->db->quote(json_encode($obj, JSON_UNESCAPED_UNICODE)), "'") ."' WHERE id = ". $objectData['id'] ." RETURNING *;");
// 				var_dump($statement, $statement->errorInfo(), "UPDATE $objectType SET object_data = '". pg_escape_string(json_encode($obj, JSON_UNESCAPED_UNICODE)) ."' WHERE id = ". $objectData['id'] ." RETURNING *;");
				return false;

			}

		}

		public function where($objectType, $selectionArr, $additionalClause = '', $getChildObjects = 0){
			$selectString = "SELECT * FROM $objectType WHERE ";

            $i=0;
			foreach($selectionArr as $key => $val){

				if(is_string($val)){

					if($i == 0){
// 						$selectString .= "object_data @> '" . '{"'. $key .'":"'. trim($val, "'") .'"'."}'";
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
					}else{
// 						$selectString .=  " and object_data @> '". '{"'. $key .'":"'. trim($val, "'") . '"' ."}'";
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}elseif(is_array($val)){

					if($val['type'] === 'contains'){

						if($i == 0){
							$whereClause .= "(a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}else{
							$whereClause .= " and (a.object_data->>'$key')::jsonb @> '\"". trim($val['value']) ."\"'::jsonb";
						}

					}elseif($val['type'] === 'between'){

						$startTime = date('Y-m-d H:i:s', $val['start']);
						$endTime = date('Y-m-d H:i:s', $val['end']);

						if($i == 0){
							$selectString .= "to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}else{
							$selectString .= " and to_timestamp(object_data->>'$key', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('". $startTime ."', 'YYYY-MM-DD HH24:MI:SS') AND '". $endTime ."'";
						}

					}

				}else{

					if($i == 0){
// 						$selectString .= "object_data @> '" . '{"'. $key .'":'. trim($val, "'") ."}'";
						$selectString .= "object_data->>'$key' = '". trim($val) ."'";
						//$selectString .= "object_data->'". $key ."':'". trim($val, "'") ."'";
					}else{
// 						$selectString .=  " and object_data @> '". '{"'. $key .'":'. trim($val, "'") ."}'";
						$selectString .= " and object_data->>'$key' = '". trim($val) ."'";
					}

				}
				$i++;
			}
			$selectString .= ' '. $additionalClause .";";

			//echo $selectString;
			//die();

//var_dump($selectString);
			$statement = $this->db->prepare($selectString);

			if($statement->execute()){

				return $this->parseData($statement->fetchAll(), $getChildObjects, $objectType);

			}elseif($this->debug){

				return var_dump($statement, $statement->errorInfo());

			}

		}

		public function getAllBlueprints(){

			$statement = $this->db->prepare('SELECT * FROM object_blueprints;');

			if($statement->execute()){

				$data = $statement->fetchAll();

				if(is_array($data)){
					foreach($data as $i => $datum){

						$data[$i]['blueprint'] = json_decode($data[$i]['blueprint'], true);

					}
				}

				return $data;

			}else{

				return false;

			}

		}

		public function getBlueprint($objectType, $getOptions = false){

			$statement = $this->db->prepare(
				"SELECT * FROM object_blueprints WHERE object_type = :objType;"
			);

			if($statement->execute(array(':objType' => $objectType))){

				$blueprint = json_decode($statement->fetchAll()[0]['blueprint'], true);
				if($getOptions){
var_dump($blueprint);
					if(is_array($blueprint)){
						foreach($blueprint as $key => $blueprintDatum){

							if($blueprintDatum['type'] == 'objectId' && !$blueprintDatum['immutable'] && $blueprintDatum['objectType'] !== 'file_meta_data'
							or $blueprintDatum['type'] == 'objectIds' && !$blueprintDatum['immutable'] && $blueprintDatum['objectType'] !== 'file_meta_data'){

								if($blueprintDatum['type'] == 'objectId'){
									$blueprint[$key]['type'] = 'select';
								}elseif($blueprintDatum['type'] == 'objectIds'){
									$blueprint[$key]['type'] = 'multi-select';
								}

								$blueprint[$key]['options'] = array();
								$options = $this->getAll($blueprintDatum['objectType']);

								$j=0;
								if(is_array($options)){
									foreach($options as $option){

										$displayName = $blueprint[$key]['selectDisplay'];
										$displayItems = explode('[', $displayName);

										if(is_array($displayItems)){
											$k=1;
											foreach($displayItems as $displayItem){

												$displayName = str_replace('['. explode(']', $displayItems[$k])[0] .']', $option[explode(']', $displayItems[$k])[0]], $displayName);
												$k++;

											}
										}

										$blueprint[$key]['options'][$option['id']] = $displayName;

										$j++;
									}
								}

							}

							$i++;
						}
					}

					return $blueprint;

				}else{

					return $blueprint;

				}

			}else{

				return false;

			}

		}

		private function getChildObjs($objectData, $blueprint, $levels){

			$ret = $objectData;
			if($levels > 0){

				foreach($blueprint as $key => $property){

					if($property['type'] == 'objectId'){

						// $getChildObjects will be int of how many levels of child objects you want to grab
						$ret[$key] = $this->getById($property['objectType'], $ret[$key], $levels - 1);
						if($property['objectType'] === 'file_meta_data'){
							$ret[$key]['loc'] = $this->getFileLocation($ret[$key]);
						}

					}elseif($property['type'] == 'objectIds'){

						foreach($ret[$key] as $innerKey => $childObjId){

							$ret[$key][$innerKey] = $this->getById($property['objectType'], $childObjId, $levels - 1);

						}

					//TODO: get child objects for blueprint 'object' types
					}elseif($property['type'] == 'list'){

						foreach($ret[$key] as $innerKey => $childObj){

							$ret[$key][$innerKey] = $this->getChildObjs($childObj, $property['blueprint'], $levels);

						}

					}

				}

			}

			return $ret;

		}

		private function getFileLocation($file){

			return '../../'.$_REQUEST['pagodaAPIKey'].'/'.$file['oid_type'] .'/'. $file['oid'] .'/'. $file['file_name'];

		}

		private function parseData($data, $getChildObjects = 0, $objectType = null){

			if($getChildObjects > 0){

				$blueprint = $this->getBlueprint($objectType);

			}

			$ret = array();
			if(is_array($data)){

				$i=0;
				foreach($data as $key => $datum){

					$ret[] = json_decode($datum['object_data'], true);
					$ret[$i]['id'] = $datum['id'];
					$ret[$i]['date_created'] = $datum['date_created'];
                    $ret[$i]['last_updated'] = $datum['last_updated'];
                    if($datum['rca_last_updated_comments']){
                        $ret[$i]['rca_last_updated_comments'] = $datum['rca_last_updated_comments'];
                    }
                    if($datum['rca_last_updated_uploads']){
                        $ret[$i]['rca_last_updated_uploads'] = $datum['rca_last_updated_uploads'];
                    }

					$ret[$i] = $this->getChildObjs($ret[$i], $blueprint, $getChildObjects);

					$i++;
				}

/*
				if($objectType == 'timeline_events'){
					$i=0;

					foreach($ret as $datum){

						$j=0;
						foreach($datum['menu'] as $item){

							unset($ret[$i]['menu'][$j]['details']['menuNote']);

							$j++;
						}

						$i++;
					}
				}
*/

				return $ret;

			}else{

				return false;

			}

		}

		private function parseInput($objectType, $objectData, $obj, $newObj = 0, $blueprint = null, $depth = 0){

			if($blueprint == null){
				$blueprint = $this->getBlueprint($objectType);
			}

			foreach($blueprint as $key => $field){

				if($key !== 'id' and $key !== 'date_created' and isset($objectData[$key]) or $depth > 0 and isset($objectData[$key])){

					switch($field['type']){

						case 'date':
//var_dump($objectData[$key]);

							try {
								$date = new DateTime($objectData[$key]);
								$obj[$key] = $date->format('Y-m-d H:i:s');
							}catch (Exception $e){
								$obj[$key] = $objectData[$key];
							}


						break;

						case 'float':
						$obj[$key] = $objectData[$key];
						break;

						case 'int':
						case 'usd':
						$obj[$key] = intval(preg_replace("/[^0-9]-/","", $objectData[$key]));
						break;

						case 'list':
						$obj[$key] = array();
						if(is_array($objectData[$key])){
							foreach($objectData[$key] as $i => $datum){
// 								$datum['id'] = $i;
								array_push($obj[$key], $this->parseInput($objectType, $datum, array(), $newObj, $field['blueprint'], $depth + 1));
							}
						}
						break;

						case 'objectId':
						if(is_int(intval(trim($objectData[$key], "'")))){
							$obj[$key] = intval(trim($objectData[$key], "'"));
						}
						break;

						case 'object':
						if(is_array($objectData[$key])){
							$obj[$key] = $objectData[$key];

							if($objectType != 'invoice'){

								foreach($obj[$key] as $innerKey => $innerDatum){

									$obj[$key][$innerKey] = $this->parseArrayForInput($innerDatum);

								}

							}

						}
						break;

						case 'string':
						if(is_string($objectData[$key])){
							$obj[$key] = trim($objectData[$key], "'");
						}
						break;

						default:
						$obj[$key] = $objectData[$key];
						break;

					}

				}elseif($key !== 'id' and $key !== 'date_created' and $newObj == 1){

					switch($field['type']){

						case 'date':
						$date = new DateTime($objectData[$key]);
						$obj[$key] = $date->format('Y-m-d H:i:s');
						break;

						case 'float':
						case 'int':
						case 'usd':
						case 'objectId':
						$obj[$key] = 0;
						break;

						case 'list':
						case 'object':
						$obj[$key] = array();
						break;

						case 'string':
						$obj[$key] = '';
						break;

						default:
						$obj[$key] = $objectData[$key];
						break;

					}

				}
			}

			return $obj;

		}

		private function parseArrayForInput($data){

			if(is_array($data)){

				foreach($data as $key => $datum){

					$data[$key] = $this->parseArrayForInput($datum);

				}

			}elseif(is_string($data)){

				$data = trim($data, "'");

			}elseif(is_int($data)){

				$data = preg_replace("/[^0-9]/", "", $data);

			}elseif(is_float($data)){

				$data = $objectData[$key];

			}else{

				$data = $objectData[$key];

			}

			return $data;

		}

	}
