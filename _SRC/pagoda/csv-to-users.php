<?php

// 	error_reporting(E_ALL);
// 	ini_set('display_errors', '1');
	echo 'endpoint closed.';
	die();
	header('Access-Control-Allow-Origin: *');
	define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
	date_default_timezone_set('America/Chicago');

	require 'vendor/autoload.php';

	require_once APP_ROOT.'/lib/_.php';
	require_once APP_ROOT.'_objects.php';
	require_once APP_ROOT.'_pgObjectsMT.php';
	require_once APP_ROOT.'_communications.php';
	require_once APP_ROOT.'_cookiesPG.php';
	require_once APP_ROOT.'files/_fileApi.php';

	$instanceID = $_REQUEST['pagodaAPIKey'];

	$instanceID = 'captainds';

	switch($instanceID){

		case '_staging':
		case 'petermikhail':
		case 'joshgantt':
		case 'johnwhittenburg':
		case 'rickyvoltz':
		case 'zachvoltz':

			$apiFolder = 'notify/pagoda';
			$instanceURL = $instanceID;

			break;

		default:

			$apiFolder = 'notify/pagoda';
			$instanceURL = '_production';

	}

	if(!class_exists('App')){
		if(!defined('APP_ROOT')){
			define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
		}

		require_once '_app.php';

	}

	$instanceName = str_replace('/', '', str_replace('/app/', '', $_REQUEST['pagodaAPIKey']));

	if(!class_exists('App') or !class_exists('pgObjects')){
		require_once '_pgObjectsMT.php';
	}

	if(!class_exists('App') or !class_exists('pgObjectsAdmin')){
		require_once '_pgObjectsMTAdmin.php';
	}

	$pdo = new PDO("pgsql:host=localhost;port=5432;dbname=pagoda;user=pagoda;password=***********");

	$pgObjects = new pgObjects($pdo, $instanceName);
	$pgObjectsAdmin = new pgObjects($pdo, $instanceName);

	$instance = $pgObjectsAdmin->where('instances', array('instance'=>$instanceName))[0];

	if(extension_loaded ('newrelic')) {
		newrelic_set_appname ($instance['systemName'].";Pagoda");
	}

	$appConfig = array();

	if(is_array($instance)){
		foreach($instance as $k => $v){

			switch($k){

				case 'components':
				case 'pageModules':
				case 'settingsModules':

					$appConfig[$k] = explode(',', $v);

					break;

				case 'db_post':

					$appConfig['db']['post'] = $v;

					break;

				case 'db_read':

					$appConfig['db']['read'] = $v;

					break;

				case 'db_write':

					$appConfig['db']['write'] = $v;

					break;

				case 'files_bucket':

					$appConfig['files']['bucket'] = $v;

					break;

				case 'files_delete':

					$appConfig['files']['delete'] = $v;

					break;

				case 'files_read':

					$appConfig['files']['read'] = $v;

					break;

				case 'files_write':

					$appConfig['files']['write'] = $v;

					break;

				case 'twilio_sid':

					$appConfig['twilio']['sid'] = $v;

					break;

				case 'twilio_token':

					$appConfig['twilio']['token'] = $v;

					break;

				case 'sms_from':

					$appConfig['twilio']['smsFrom'] = $v;

					break;

				default:

					$appConfig[$k] = $v;

			}

		}
	}

	class MyApp extends App {

		function __construct($appConfig, $dbConn, $objects, $comms, $pgObjects, $cookies, $filesApi){

			$this->appConfig = $appConfig;
			$this->db = $dbConn;
			$this->obj = $objects;
			$this->comm = $comms;
			$this->pgObjects = $pgObjects;
			$this->cookies = $cookies;
			$this->files = $filesApi;

		}

	}

	$objects = new Objects($pdo);

	$pgObjects = new pgObjects($pdo, $appConfig['instance']);

	$comms = new Comm($pgObjects, $appConfig);

	$cookies = new Cookies($pgObjects);

	$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey']);

	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);

	$cookie = $cookies->checkCookie($_COOKIE['uid'], 'user', $_COOKIE['series'], $_COOKIE['token']);

	$uploader = new csvUploader($app, $appConfig, 'tmp-upload-files/db146671_captaind.csv');
	echo 'Starting..<br />';

	var_dump($app->pgObjects->where('users', ['email' => '<EMAIL>']));
	die();

	$action = 'update';
	switch($action){

		case 'create':
// 			$uploader->createUserData();
			break;

		case 'update':
			$uploader->updateUserDataWithIds();
			break;

		case 'delete':
// 			$uploader->deleteContactsFromSource();
			break;

	}

	class csvUploader {

		// private
		private function create_objs(){

			// create batch
// 			$this->app->pgObjects->create('users', $this->currentBatch, 1);

			echo count($this->currentBatch) .' rows created.<br />';

			// clear batches
			$this->currentBatch = array();
			$this->batchStart += $this->batch_size;

		}

		private function update_objs(){

			$updates = [];

			foreach($this->currentBatch as $i => $user){

				$alreadyThere = __::filter($this->users,
					function($alreadyThere) use($user) {
// 						var_dump( $user['email'], $alreadyThere['email']);
						return $alreadyThere['email'] === $user['email'];

					}
				)[0];
// var_dump($user['email']);
// die();
				if($alreadyThere){

					array_push($updates, [
						'id' => $alreadyThere['id'],
						'data_source_id' => $user['data_source_id']
					]);

// 					echo 'id => '. $alreadyThere['id'] .'<br />';
// 					echo 'email => '. $user['email'] .'<br />';
// 					echo 'data_source_id => '. $user['data_source_id'] .'<br /><br /><br />';

				}else{

					echo $user['email'] .' not found!!<br />';

				}

			}

// 			var_dump($updates);
// die();
// 			$this->app->pgObjects->update('users', $updates, 0);

			// clear batches
			$this->currentBatch = array();
			$this->batchStart += $this->batch_size;

		}

		private function get_csv_data($justUpdate = false){

			$loc = $this->filePath;
			$row = 0;
			$csvKeys = array();
			$startFromLast = 0;

			if (($handle = fopen($loc, "r")) !== FALSE) {

			    while (($data = fgetcsv($handle, 0, ",")) !== FALSE) {

				    // get csv keys
				    if($row === 0){
					    $csvKeys = $data;
				    }else if($row > $startFromLast){
					    $this->parse_csv_row($csvKeys, $data, $row);
				    }

				    // create objs
				    if(count($this->currentBatch) >= $this->batch_size){

					    if($justUpdate){
						    $this->update_objs();
					    }else{
						    $this->create_objs();
					    }
					    echo 'Rows '. $this->batchStart .' - '. $row .' processed.<br />';

				    }

			        $row++;

			    }

			    // create last batch of objs
			    if(count($this->currentBatch) >= 0){

				    if($justUpdate){
					    $this->update_objs();
				    }else{
					    $this->create_objs();
				    }
				    echo 'Rows '. $this->batchStart .' - '. $row .' processed.<br />';

			    }

			    fclose($handle);

			}else{

				echo 'Data not found.<br />';

			}

			echo 'COMPLETE';

		}

		private function parse_csv_row($keys, $row, $rowId){

			$temp = [
				/*
'fname' => 'Not set',
				'lname' => 'Not set',
				'nick_name' => $row['name'],
				'type' => 'staff',
				'data_source' => 920421,
				'data_source_id' => $rowId,
				'email' => $temp['email'],
				'enabled' => 1,
				'password' => $this->app->createNewPassword(false, 'seafood')['pwdHash'],
				'service' => [1384868],
				'object_bp_type' => 'users',
				'profiles' => [],
				'instance' => 'captainds'
*/
			];

			if(is_array($row)){
				foreach($row as $i => $val){

					/*
echo 'i = '. $i .'<br />';
					echo 'val = '. $val .'<br />';
					echo 'keys = '. $keys .'<br />';
					var_dump($keys);
					die();
*/
					switch($keys[$i]){

						case 'registered':
						$temp['date_created'] = date('Y-m-d H:i:s', strtotime($val));
						break;

						case 'name':
						$temp['nick_name'] = $val;
						break;

						case 'email':
// 						$temp['email'] = $val;
						break;

						case '<EMAIL>':
						$temp['email'] = $val;
						break;

						case '1':
						$temp['data_source_id'] = $val;
						break;

					}

					if($i === 4){
						$temp['email'] = $val;
// 						echo $val;
					}

				}
			}

			// users with 'storemail.net' in email address get [1384869] as service
			/*
if( strpos($temp['email'], 'storemail.net') !== false ){
				$temp['service'] = [1384869];
				$temp['nick_name'] = 'Store #'. $temp['nick_name'];
			}
*/

			array_push($this->currentBatch, $temp);

		}

		// public
		public function __construct($app, $config, $filePath){

			//$transferId = 952052;
			$this->start_time = date('Y-m-d H:i:s');
			$this->batch_size = 175;
			$this->batch_start = 0;

			$this->app = $app;
			$this->filePath = $filePath;
			$this->appConfig = $config;

			// object batches for db
			$this->currentBatch = array();

		}

		public function createUserData(){

// 			var_dump($this->app->pgObjects->getAll('users'));
// 			die();
			$this->get_csv_data();

		}

		public function updateUserDataWithIds(){

// 			var_dump($this->app->pgObjects->getAll('users'));
// 			die();
			$this->users = $this->app->pgObjects->getAll('users', ['email' => true]);
// 			var_dump(count($this->users), $this->users[0]);
// 			die();
			$this->get_csv_data(true);

		}

		public function deleteContactsFromSource(){

			if($this->app->pgObjects->deleteWhere('contacts', ['data_source' => $this->map['id']])){

				$this->app->pgObjects->deleteWhere('companies', ['data_source' => $this->map['id']]);
				$this->app->pgObjects->deleteWhere('contact_info', ['data_source' => $this->map['id']]);

				return true;

			}else{

				return false;

			}

		}

	}
