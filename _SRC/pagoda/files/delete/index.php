<?php

header('Access-Control-Allow-Origin: *');
/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

define( "FILES_BUCKET", realpath('/var/www/vhosts/pagoda.voltz.software/httpdocs/_repos/_production/pagoda/_files/_instances').'/');

require_once '../../lib/_.php';
require_once '../../_pgObjectsMT.php';
require_once '../../_app.php';
require_once '../_fileApi.php';

if(!empty($_REQUEST['do'])){
	
	require_once'../_config.php';
	$obj = new pgObjects($pdo, $_REQUEST['pagodaAPIKey']);
	
	$files = new FileApi($obj, FILES_BUCKET.$_REQUEST['pagodaAPIKey'], $_REQUEST['pagodaAPIKey']);

	$files->delete(json_decode($_POST['json'], true)['id']);
	
}

?>