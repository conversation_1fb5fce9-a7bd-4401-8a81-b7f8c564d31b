<?php

if (extension_loaded ('newrelic')) {
	newrelic_set_appname ('Files;Pagoda');
}

if(!file_exists('pagoda/_coredev/S3.php')){
	if(!file_exists('S3.php')){
		if(!file_exists('../../S3.php')){
			require(APP_ROOT.'S3.php');
		}else{
			require_once('../../S3.php');
		}
	}else{
		require_once('S3.php');		
	}
}else{
	require_once('pagoda/_coredev/S3.php');
}

//require '../../../../DBPATH.php';

class FileApi {
	
	function __construct($obj, $bucket, $instance){
		
		$this->obj = $obj;
		$this->bucket = $bucket;
		$this->instance = $instance;
		S3::setAuth('HLA54JTNUQ6NPABWUFX2', 'PYgtiTAf7k7WK/+hCctwmc/+vTjsKg51PzUu6xAfByU');
		
	}
	
	// PUBLIC FUNCTIONS
	public function createBucket($bucket){
		
		if(!is_dir($bucket)){
			return mkdir($bucket);
		}
		
	}
	
	public function delete($fileId){

		$metaData = $this->obj->getById('file_meta_data', intval($fileId));
		
		$subDir = '';
		if($metaData['parent'] > 0){
			$subDir = $this->getSubDir($metaData['parent'], '');
		}
		$filePath = $this->bucket .'/'. $metaData['oid_type'] .'/'. $metaData['oid'] . $subDir .'/'. $metaData['file_name'];
		
		if(unlink($filePath) or !file_exists($filePath)){
	
			if($this->obj->delete('file_meta_data', intval($metaData['id']))){
				
				return $this->sendData(true, 1);
				
			}

		}
		
	}
	
	public function getAll($getChildObjs = 0, $json = 1){
		
		if(isset($_REQUEST['get-child-objects'])){
			$getChildObjs = $_REQUEST['get-child-objects'];
		}

		$data = $this->obj->getAll('file_meta_data');
		
		if(is_array($data)){
			foreach($data as $i => $datum){
				
				if($datum['parent'] > 0){
					$subDir = $this->getSubDir($datum['parent'], '', $data);
				}else{
					$subDir = '';
				}
				
				$data[$i]['loc'] = $datum['oid_type'] .'/'. $datum['oid'] . $subDir .'/'. $datum['file_name'];
				
			}
		}
		
		return $this->sendData($data, $json);
		
	}
	
	public function getWhere($queryObj){

		if($data = $this->obj->where('file_meta_data', $queryObj, '', 0)){
			
			if(is_array($data)){
				foreach($data as $i => $datum){
					
					if($datum['parent'] > 0){
						$subDir = $this->getSubDir($datum['parent'], '', $data);
					}else{
						$subDir = '';
					}
					
					$data[$i]['loc'] = $datum['oid_type'] .'/'. $datum['oid'] . $subDir .'/'. $datum['file_name'];
					
				}
			}
			
			return $this->sendData($data, 1);
			
		}else{
			
			return $this->sendData([], 1);
			
		}
		
	}
	
	public function renameFile($metaData, $json = 1){
		
		$space->DownloadFile('pagoda/_instances/'.$this->instance.'/'.$metaData->oid_type.'/'. $metaData->oid . $subDir.'/'.$metaData->name);
		$rename = $space->UploadFile('pagoda/_instances/'.$this->instance.'/'.$metaData->oid_type.'/'. $metaData->oid . $subDir.'/'.$metaData->new_name, "public");
		
/*
		$rename = S3::copyObject(
			'pagoda/_instances/'.$this->instance.'/'.$metaData->oid_type.'/'. $metaData->oid . $subDir, 
			$metaData->name,
			'pagoda/_instances/'.$this->instance.'/'.$metaData->oid_type.'/'. $metaData->oid . $subDir,
			$metaData->new_name
		);
*/

		return $rename;
				
	}

	public function replace($file, $metaData){
		
	}
	
	public function update($file, $metaData){
		
		if($current = $this->obj->getById('file_meta_data', intval($metaData['id']))){
			
			if(is_array($current)){
				foreach($current as $key => $currentDatum){
					if(!array_key_exists($key, $metaData)){
						$metaData[$key] = $currentDatum;
					}
				}
			}else{
				
				return false;
				
			}
			
			// change name of file
			if($file['file_name'] !== $metaData['file_name'] or $file['parent'] !== $metaData['parent'] or $file['oid_type'] != $metaData['oid_type']){
				
				if(!$this->changeFileName($current, $metaData)){
					return false;
				}
				
			}
			
			$updated = $this->obj->update('file_meta_data', $metaData);
			
			$subDir = $this->getSubDir($updated['parent'], '');
			$updated['loc'] = $updated['oid_type'] .'/'. $updated['oid'] . $subDir .'/'. $updated['file_name'];;
			
			return $this->sendData($updated, 1);
			
		}else{
			
			return false;
			
		}
		
	}
	
	public function upload($file, $metaData, $json = 0){
		
		function clean($string) {
		  
		   $string = str_replace(' ', '_', $string); // Replaces all spaces with hyphens.
		
		   return preg_replace('/[^A-Za-z0-9\._]/', '', $string); // Removes special chars.
		}
		
		if($metaData['parent'] > 0){
			
			$subDir = $this->getSubDir($metaData['parent'], '');
			
		}else{
			
			$subDir = '';
			
		}
		
		if($metaData['fileType'] !== 'folder'){
			
			$metaData['fileName'] = clean($file['name']);
			
			$file['name'] = $metaData['fileName'];
			
			$key = "HDDFHSE3NPPNGCNUEVOV";
			$secret = "+wACknEAuRZ0BfTsDj7MHIHF+3XySoaDy1Hl54eRdeg";
			
			$space_name = "pagoda";
			$region = "nyc3";
	
			$space = new SpacesConnect($key, $secret, $space_name, $region);

			$space->UploadFile($file['tmp_name'], "public", "_instances/".$this->instance."/".$metaData['objectType']."/". $metaData['objectId'] . $subDir."/".$file['name']);
			
			$created = $this->createMetaData($metaData);
			$created['loc'] = $metaData['objectType'] .'/'. $metaData['objectId'] . $subDir .'/'. $file['name'];
			
			return $this->sendData($created, $json);
			
		}else{
			
			if($newDir = $this->getDir($metaData['objectType'] .'/'. $metaData['objectId'] . $subDir .'/'. $metaData['fileName'])){
				
				return $this->sendData($this->createMetaData($metaData), $json);
				
			}
			
		}
					
	}
	
	// PRIVATE FUNCTIONS
	private function changeFileName($oldFile, $newFile){
		
		$oldSubDir = $this->getSubDir($oldFile['parent'], '');
		$newSubDir = $this->getSubDir($newFile['parent'], '');
		
		return rename($this->bucket.'/'.$oldFile['oid_type'] .'/'. $oldFile['oid'] . $oldSubDir .'/'. $oldFile['file_name'], $this->bucket.'/'.$newFile['oid_type'] .'/'. $newFile['oid'] . $newSubDir .'/'. $newFile['file_name']);
		
	}
	
	private function createMetaData($metaData){
		
		return $this->obj->create('file_meta_data', array(
			
			'oid' => intval($metaData['objectId']),
			'oid_type' => $metaData['objectType'],
			'file_name' => $metaData['fileName'],
			'file_type' => $metaData['fileType'],
			'is_public' => intval($metaData['isPublic']),
			'is_folder' => intval($metaData['isFolder']),
			'parent' => intval($metaData['parent'])
			
		));
		
	}
	
	private function deleteMetaData(){
		
	}
	
	private function updateMetaData(){
		
	}
	
	private function getDir($targetDir){
		
		$dirArr = explode('/', $targetDir);

		$temp = '';
		if(is_array($dirArr)){
			foreach($dirArr as $dir){
				
				$temp .= '/'.$dir;
				if(!is_dir($this->bucket. $temp)){
					mkdir($this->bucket. $temp);
				}
				
			}
		}
				
		return $this->bucket. $temp;
		
	}
	
	private function getFileLocation($file){
		
		return $this->bucket.'/'.$file['objectType'] .'/'. $file['objectId'] .'/'. $file['file_name'];
		
	}
	
	private function getSubDir($parentId, $dirString, $files = null){
		
		if(is_array($files)){
			
			$parent = $files[array_search($parentId, array_column($files, 'id'))];
			
			$dirString = '/'. $parent['file_name'] .$dirString;
			
			if($parent['parent'] > 0){
				
				return $this->getSubDir($parent['parent'], $dirString, $files);
				
			}else{
				
				return $dirString;
				
			}
			
		}elseif($parent = $this->obj->getById('', intval($parentId))){
			
			if($parent['file_name']){
				
				$dirString = '/'. $parent['file_name'] .$dirString;
				
				if($parent['parent'] > 0){
					
					return $this->getSubDir($parent['parent'], $dirString);
					
				}else{
					
					return $dirString;
					
				}
				
			}else{
				
				return '';
				
			}
			
		}else{
			
			return false;
			
		}
		
	}
	
	private function move(){
		
	}
	
	private function sendData($data, $json){
		if($json === 1){
			header("Content-Type: application/json");
			echo json_encode($data);
		}else{
			return $data;
		}
	}
	
}

?>