<?php

date_default_timezone_set('America/Chicago');

$instanceID = $_REQUEST['pagodaAPIKey'];

switch($instanceID){
	
	case '_staging':
	case 'petermikhail':
	case 'joshgantt':
	case 'johnwhittenburg':
	case 'rickyvoltz':
	case 'zachvoltz':

		$apiFolder = 'notify/pagoda';	
		$instanceURL = $instanceID;

		break;
					
	default:
		
		$apiFolder = 'notify/pagoda';
		$instanceURL = '_production';
	
}

	require_once $_SERVER["DOCUMENT_ROOT"].'/_repos/'.$instanceURL.'/notify/DBPATH.php';


if(!class_exists('App')){
	if(!defined('APP_ROOT')){
		define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
	}

	require_once $_SERVER["DOCUMENT_ROOT"].'/_repos/'.$instanceURL.'/'.$apiFolder.'/_coredev/_app.php';

}

$instanceName = str_replace('/', '', str_replace('/app/', '', $_REQUEST['pagodaAPIKey']));

if(!class_exists('App') or !class_exists('pgObjects')){
	require_once $_SERVER["DOCUMENT_ROOT"].'/_repos/'.$instanceURL.'/'.$apiFolder.'/_coredev/_pgObjectsMT.php';
}

if(!class_exists('App') or !class_exists('pgObjectsAdmin')){
	require_once $_SERVER["DOCUMENT_ROOT"].'/_repos/'.$instanceURL.'/'.$apiFolder.'/_coredev/_pgObjectsMTAdmin.php';
}

$pdo = new PDO("pgsql:host=". $DB_PATH .";port=5432;dbname=pagoda;user=pagoda;password=***********");

$pgObjects = new pgObjects($pdo, 'voltzsoftware');
$pgObjectsAdmin = new pgObjects($pdo, $instanceName);

$instance = $pgObjectsAdmin->where('instances', array('instance'=>$instanceName))[0];

$appConfig = array();

if(is_array($instance)){
	foreach($instance as $k => $v){
		
		switch($k){
			
			case 'components':
			case 'pageModules':
			case 'settingsModules':
			
				$appConfig[$k] = explode(',', $v);
			
				break;
			
			case 'db_post':
			
				$appConfig['db']['post'] = $v;
			
				break;
				
			case 'db_read':
			
				$appConfig['db']['read'] = $v;
			
				break;
				
			case 'db_write':
			
				$appConfig['db']['write'] = $v;
			
				break;
				
			case 'files_bucket':
			
				$appConfig['files']['bucket'] = $v;
			
				break;	
				
			case 'files_delete':
			
				$appConfig['files']['delete'] = $v;
			
				break;
				
			case 'files_read':
			
				$appConfig['files']['read'] = $v;
			
				break;
				
			case 'files_write':
			
				$appConfig['files']['write'] = $v;
			
				break;
				
			case 'twilio_sid':
			
				$appConfig['twilio']['sid'] = $v;	
			
				break;
				
			case 'twilio_token':
			
				$appConfig['twilio']['token'] = $v;
				
				break;	
				
			case 'sms_from':
			
				$appConfig['twilio']['smsFrom'] = $v;
				
				break;						
			
			default:
			
				$appConfig[$k] = $v;
			
		}
	
	}
}
	
class MyApp extends App {
			
	function __construct($appConfig, $dbConn, $objects, $comms, $pgObjects, $cookies, $filesApi){
		
		$this->appConfig = $appConfig;		
		$this->db = $dbConn;
		$this->obj = $objects;
		$this->comm = $comms;
		$this->pgObjects = $pgObjects;
		$this->cookies = $cookies;
		$this->files = $filesApi;
		
	}
		
}
	
?>