<?php 
	
/*
	error_reporting(E_ALL);
	ini_set('display_errors', '1');
*/
die();
	
	header('Access-Control-Allow-Origin: *');
	
	if (extension_loaded ('newrelic')) {
		newrelic_set_appname ("Pagoda");
		newrelic_background_job();
		newrelic_ignore_apdex();
	}

	define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' ); 
	
 	$_REQUEST['pagodaAPIKey'] = 'voltzsoftware';
	
 	require_once APP_ROOT.'vendor/autoload.php';
	require_once APP_ROOT.'lib/_.php';
	require_once APP_ROOT.'_pgObjectsMTAdmin.php';
	require_once APP_ROOT.'DBPATH.php';
	
	$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");
	
	$pgObjects = new pgObjectsAdmin($pdo, 'voltzsoftware');

 	$instances = $pgObjects->where('instances', ['enabled' => 1]);

	$_POST = new stdClass();
	
	foreach($instances as $key => $instance) {
echo($instance['instance'] . '<br>');
		$pgObjects->changeInstance($instance['instance']);
		
		$tasks = $pgObjects->where('groups', [
					'group_type' => 'Task', 
					'state' => 3,
					'status' => [
						type => 'not_equal',
						value => 'done'
					], 
					'is_deleted' => false
				],
					'',
					0,
					false,
					0,
					'null',
					'asc',
					100,
					null,
					[],
					'string',
					$instance['instance']
				);
echo('Number of tasks found: ' . count($tasks) . '<br>');
		$updatedTasks = [];
			
		foreach($tasks as $i => $task) {

			$task['status'] = 'done';
			array_push($updatedTasks, $task);

		}
echo('Number of tasks to be updated: ' . count($updatedTasks) . '<br>');
		$pgObjects->update('groups', $updatedTasks, 0);
		
echo('Update complete');
		
	}
	
?>