<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/
error_reporting(0);

$http_origin = $_SERVER['HTTP_ORIGIN'];

if ($http_origin == "http://localhost:8080" || $http_origin == "http://localhost:8084") {  
    header("Access-Control-Allow-Origin: $http_origin");
} else {
	header("Access-Control-Allow-Origin: $http_origin");
}

header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
define( "FILES_BUCKET", realpath('https://pagoda.nyc3.digitaloceanspaces.com/_instances').'/');
date_default_timezone_set('America/Chicago');

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';
require 'BENTO_ENV.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'rules/rules.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
$pdo = require_once APP_ROOT.'getDbConnection.php';

// $GLOBALS['speed-test'] = true;
if ($GLOBALS['speed-test']) {
	$GLOBALS['req-start-time'] = microtime(true);
}

if(!empty($_REQUEST['do'])){	

	if(!class_exists('App')){

		if(!defined('APP_ROOT')){
			define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
		}
	
		require_once '_app.php';
	
	}
	
	$instanceName = str_replace('/', '', str_replace('/app/', '', $_REQUEST['pagodaAPIKey']));
	
	if(!class_exists('App') or !class_exists('pgObjects')){
		require_once '_pgObjectsMT.php';
	}
	
	if(!class_exists('App') or !class_exists('pgObjectsAdmin')){
		require_once '_pgObjectsMTAdmin.php';
	}
		
	$authHeader = getallheaders()['bento-token'];
	$portalId = getallheaders()['portal'];
	
	if ($portalId) {
		$GLOBALS['portal'] = intval($portalId);
		$GLOBALS['portal'] = boolval($portalId);
	}
	
	$pgObjects = new pgObjects($pdo, $instanceName, $rules, $write, $writeDocsDB);
	$pgObjectsAdmin = new pgObjects($pdo, $instanceName, $rules, $write, $writeDocsDB);
	
	$instance = $pgObjectsAdmin->where('instances', array('instance'=>$instanceName))[0];
	
	$appConfig = array();
	
	if(is_array($instance)){
		foreach($instance as $k => $v){
			
			switch($k){
				
				case 'components':
				case 'pageModules':
				case 'settingsModules':
					$appConfig[$k] = explode(',', $v);
					break;
				
				case 'db_post':
					$appConfig['db']['post'] = $v;
					break;
					
				case 'db_read':
					$appConfig['db']['read'] = $v;
					break;
					
				case 'db_write':
					$appConfig['db']['write'] = $v;
					break;
					
				case 'files_bucket':
					$appConfig['files']['bucket'] = $v;
					break;	
					
				case 'files_delete':
					$appConfig['files']['delete'] = $v;
					break;
					
				case 'files_read':
					$appConfig['files']['read'] = $v;
					break;
					
				case 'files_write':
					$appConfig['files']['write'] = $v;
					break;
					
				case 'twilio_sid':
					$appConfig['twilio']['sid'] = $v;	
					break;
					
				case 'twilio_token':
					$appConfig['twilio']['token'] = $v;
					break;	
					
				case 'sms_from':
					$appConfig['twilio']['smsFrom'] = $v;
					break;						
				
				default:
					$appConfig[$k] = $v;
					break;
				
			}
		
		}
	}
	
	$rules = new Rules($appConfig);
	$pgObjects = new pgObjects($pdo, $instanceName, $rules, $write, $writeDocsDB);

	// Cookie and token check
	$cookies = new Cookies($pgObjects);
	$cookie = $cookies->checkCookie($_COOKIE['uid'], 'user', $_COOKIE['series'], $_COOKIE['token']);
	$tokenCheck = $cookies->checkToken($authHeader);
		
	class MyApp extends App {
				
		function __construct($appConfig, $dbConn, $objects, $comms, $pgObjects, $cookies, $filesApi){
			
			$this->appConfig = $appConfig;		
			$this->db = $dbConn;
			$this->obj = $objects;
			$this->comm = $comms;
			$this->pgObjects = $pgObjects;
			$this->cookies = $cookies;
			$this->files = $filesApi;
			$this->instanceStripeSecretKey = $appConfig['stripe_account_id'];
			
		}
			
	}

	$method = $_REQUEST['do'];
	$service = $_REQUEST['service'];
	$methodOrService = !empty($service) ? $service : $method;

	if(!empty($_REQUEST['json'])){
		$_POST = json_decode($_REQUEST['json']);
	}

	
	$objects = new Objects($pdo);
	$pgObjects = new pgObjects($pdo, $appConfig['instance'], $rules, $write, $writeDocsDB);
	$comms = new Comm($pgObjects, $appConfig);
	$cookies = new Cookies($pgObjects);
	$files = new FileApi($pgObjects, FILES_BUCKET.$appConfig['instance'], $appConfig['instance']);
	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
	$GLOBALS['app'] = &$app;
	
	if (
		$tokenCheck
		|| $MergeService == true
	) {
		
		if(!empty($_REQUEST['debug'])){
		
			error_reporting(E_ALL);
			ini_set('display_errors', '1');
			
		}else{
			
			error_reporting(0);
			
		}

		// Route to services endpoints (&service is set in the request)
		if (!empty($service)) {

			require_once APP_ROOT.'services/'. $service .'.php';
			$classname = $service;
			$serviceModule = new $classname($app);
			echo $serviceModule->$method($_POST);
			
		} else {
			
			echo $app->$method();	
			
		}
		
	} else {
		
		echo 'cookie error';
		die();
		
	}
	
}else{
	
	echo 'nothing to get...';
	
}
	
?>