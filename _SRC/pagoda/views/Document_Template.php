<?php

return [
    'name' => 'Document_Template',

    // View
    'getHtml' =>  function ($api, $subject, $opts, $ancestorTags) {

        // Initialize variables
        $html = '';

        // Check to see if document was selected
        if ($opts['Name']) {

            // Check to see if that same merge tag is nested, if so, bail
            $count = 0;
            foreach ($ancestorTags as $ancestorTag) {

                // Set variables
                $ancestorTagName = $ancestorTag['name'];

                if ($ancestorTagName == $opts['Name']) {
                    $count++;
                }

                if ($count > 1) {
                    return;
                }

            }

            // Get the document template
            $documents = $api['objs']->where('contracts', [
                'name' => $opts['Name'],
                'is_template' => 1,
                'active' => array(
                    'type' => 'not_equal',
                    'value' => 'No'
                )
            ]);

            // Check that a document exists
            if (!empty($documents[0])) {

                // Merge in the nested doc's merge tags
                $merged = $api['run']('merge',
                    [
                        'template'  => $documents[0]['html_string'],
                        'new'       => $subject
                    ]
                );

                if ($merged && $merged['memo']) {
                    $html = $merged['memo'];
                }

            } else {

                $html = '<i>Document Template</i> not found.';

            }

        } else {

            $html = 'No <i>Document Template</i> set.';

        }

        return $html;

    }
];

?>