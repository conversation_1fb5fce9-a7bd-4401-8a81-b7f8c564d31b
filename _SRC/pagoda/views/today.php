<?php

function getCurrentDateTime($opts) {

    if (
        $opts 
        && $opts['mergeVars'] 
        && $opts['mergeVars']['today']
    ) {

        $currentDate = new DateTime($opts['mergeVars']['today']);
        
    } else {
        
        $currentDate = new DateTime();
        $currentDate->setTimezone(new DateTimeZone('UTC'));
        if ($_COOKIE && $_COOKIE['tz_off']) {
            $interval = new DateInterval('PT'. $_COOKIE['tz_off'] .'M');
            $interval->invert = 1;
            $currentDate->add($interval);
        }

    }

    return $currentDate;

}

function getTodayMergeTag($opts) {
    
    // Default date format to easily sortable format
    $dateFormat = 'Y/m/d';
    $dateInterval = '';

    if ($opts) {

        if (is_string($opts['dateFormat'])) {
            $dateFormat = $opts['dateFormat'];
        }

        if (is_string($opts['add'])) {

            $i = 0;
            $addArray = explode(' ', $opts['add']);

            foreach ($addArray as $add) {

                $number = trim($add);
                $interval = trim($addArray[i + 1]);


                if (is_numeric($number)) {

                    if (ctype_alpha($interval)) {

                        if (str_contains($interval, 'day')) {

                            $dateInterval .= intval($number) . 'D';

                        }

                    }

                }

                $i++;

            }

        }

    }

    $currentDate = getCurrentDateTime($opts);

    if (!empty($dateInterval)) {

        $currentDate->add(new DateInterval('P' . $dateInterval));

        // If there is not date format set, default to standard 
        // db-friendly date format.
        if (
            !is_array($opts)
            || !is_string($opts['dateFormat'])
        ) {
            $dateFormat = 'Y-m-d H:i:s';
        }

        $currentDate->setTime(16, 00, 00);

    }

    $txt = $currentDate->format($dateFormat);

    return $txt;

}

return [
    'name' =>       'today'

    // Views
    , 'getHtml' =>  function ($api, $subject, $opts) {

        return getTodayMergeTag($opts);

    }

    , 'getPlainText' =>  function ($api, $subject, $opts) {

        return getTodayMergeTag($opts);

    }

];

?>