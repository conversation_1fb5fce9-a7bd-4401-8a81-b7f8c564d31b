<?php

function getListViewHtml ($list, $subject, $objs, $opts, $api) {

    $html = '';
    $showAsChecklist = false;
    if (!empty($options) && $options['Checklist'] === 'On') {
        $showAsChecklist = true;
    }

    foreach ($list as $i => $item) {
            
        $icon = '';

        if ($opts['entity_type'] && $opts['entity_type']['icon']) {
            $icon = '<i class="ui grey '. $opts['entity_type']['icon'] .' icon"></i>';
        }

        $link = '<a class="bento-link" data-entity="'. $item['id'] .'" data-type="'. $item['object_bp_type'] .'">'. $item['name'] .'</a>';
        
        if ($showAsChecklist) {
            $checklistVal = 'false';
            if ($item['status'] === 'done') {
                $checklistVal = 'true';
            }
            $html .= '<ul data-checked="'. $checklistVal .'"><li>'. $icon . $link;
        } else {
            $html .= '<li>'. $icon . $link;
        }

        // Other fields
        if (is_array($opts['Show'])) {

            $html .= '<ul>';
            foreach ($opts['Show'] as $fieldKey => $field) {

                $html .= 
                        
                        '<li>'. 
                            $field['name'] .': '. getFieldHtml($fieldKey, [], $item, $objs, $opts, $api).
                        '</li>';

            }
            $html .= '</ul>';

        }

        if (
            $opts['Nest']
            && is_array($item[$opts['Nest']])
        ) {

            $html .= '<ul>';
            
            //!TODO: Get this working with single pointers
            foreach ($item[$opts['Nest']] as $j => $nestedItem) {

                //!TODO: Factor this out of view func
                // Create
                if ($opts['Copy'] === 'Nest') {
                    
                    array_push($nestedItem['tagged_with'], $subject['id']);
                    $nestedItem['tagged_with'] = array_unique($nestedItem['tagged_with']);
                    $defaults = [
                        'name' =>               $nestedItem['name']
                        , 'scaleEntityDate' =>    $subject['date_created']
                        , 'tagged_with' =>      $nestedItem['tagged_with']
                        , 'parent' =>           $subject['id']
                    ];
// var_dump('nestedItem::', $nestedItem);
// die();
                    $nestedItem = $objs->castFromTemplate($nestedItem, null, $defaults);
    
                }

                $link = '<a class="bento-link" data-entity="'. $nestedItem['id'] .'" data-type="'. $nestedItem['object_bp_type'] .'">'. $nestedItem['name'] .'</a>';
                $html .= '<li>'. $link .'</li>';

            }

            $html .= '</ul>';

        }

        if ($showAsChecklist) {
            $html .= '</li></ul>';
        } else {
            $html .= '</li>';
        }

    }

    return $html;

}

return [
    'name' => 'List'
    , 'getHtml' => function ($api, $subject, $opts) {

        if (
            is_array($subject)
            && $opts['set'] 
        ) {

            $graph = $api['objs']->getById(
                $subject['object_bp_type']
                , $subject['id']
                , $opts['select']
            );
            
            $list = $graph[$opts['set']];
            $html = '<ul>';

            if ($opts['Group by']) {

                $groups = __::groupBy(
                    $list
                    , function ($entity) use ($opts) {

                        //!TODO: Get this working with other field types
                        return $entity[$opts['Group by']]['id'];

                    }
                );

                foreach ($groups as $groupId => $group) {

                    $html .= '<li>'. $group[0][$opts['Group by']]['name'] .'</li>';
                    $html .= '<ul>';
                    $html .= getListViewHtml($group, $subject, $api['objs'], $opts, $api);
                    $html .= '</ul>';

                }

            } else {
                
                $html .= getListViewHtml($list, $subject, $api['objs'], $opts, $api);

            }

            $html .= '</ul>';

            return $html;

        } else {

            return '<i>No items set</i>';

        }


    }
];

?>