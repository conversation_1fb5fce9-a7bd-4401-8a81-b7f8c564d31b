<?php

// Get Fields
require_once('fields/Fields.php');

return [
    'name' =>       'this'

    // Model

    // View
    , 'getHtml' =>  function ($api, $subject, $opts) {

        if (is_array($opts['field'])) {

            // Get the value to display
            $val = $api['objs']->getValueAtAddressPath(
                $opts['field']
                , $subject
                , [
                    'format' => 'data'
                ]
            );

            // For nested collections views
            if (!empty($opts['view'])) {

                switch ($opts['view']) {
                    
                    case 'List':
                    case 'Table':
                        $opts['select'] = $opts['select'][$opts['field'][0]];
                        return $api['views'][strtolower($opts['view'])]['getHtml'](
                            $api
                            , $val
                            , $opts
                        );
                    

                }

            }

            if (is_array($opts['field'])) {

                $val = getFieldHtml(
                    end($opts['field'])
                    , [
                        'blueprint' => [
                            end($opts['field']) => $opts['fieldDef']
                        ]
                    ]
                    , [
                        end($opts['field']) => $val
                    ]
                    , $api['objs']
                    , $opts
                    , $api
                );

            }

            if (is_array($val) && array_key_exists($val, 'name')) {
                // return $val['name'];
            }

            if (is_array($val) && array_key_exists('name', $val)) {
                // return $val['name'];
            }

            if (empty($val)) {
                return '0';
            }

            return $val;

        } else {

            // To return
            $html = '0';
            if (
                array_key_exists($opts['field'], $subject)
                && !empty($subject[$opts['field']])
            ) {

                $html = $subject[$opts['field']];

            } else {
                $html = '0';
            }

        }

        return $html;

    }

    , 'getPlainText' =>  function ($api, $subject, $opts) {

        if (is_array($opts['field'])) {
            
            if (is_array($opts['field'])) {

                // Get the value to display
                $val = $api['objs']->getValueAtAddressPath(
                    $opts['field']
                    , $subject
                    , [
                        'format' => 'data'
                    ]
                );

// error_reporting(E_ALL);
// ini_set('display_errors', '1');
   
                $txt = getFieldPlainTxt(
                    end($opts['field'])
                    , [
                        'blueprint' => [
                            end($opts['field']) => $opts['fieldDef']
                        ]
                    ]
                    , [
                        end($opts['field']) => $val
                    ]
                    , $api['objs']
                    , $opts
                    , $api
                );

            }
            
            return $txt;

       }

    }

    , 'getValueText' =>  function ($api, $subject, $opts) {

        if (is_array($opts['field'])) {
            
            // Get the value to display
            $val = $api['objs']->getValueAtAddressPath(
                $opts['field']
                , $subject
                , [
                    'format' => 'data'
                ]
            );

            if (is_array($val) && array_key_exists($val, 'name')) {
                return $val['name'];
            }

            if (is_array($val) && array_key_exists('name', $val)) {
                return $val['name'];
            }

            if (empty($val)) {
                return '0';
            }

           return $val;

       }

    }

];

?>