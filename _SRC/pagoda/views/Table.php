<?php

// Get Fields
require_once('fields/Fields.php');

// Local funcs

function getBlueprint() {}

function getList() {}

function groupBy() {}

function getSingleRowHtml($entity, $type, $sortPrefix, $opts, $api, $nestedLvl = 0) {
    
    $tdStyle = 'vertical-align:top; padding:.78571429em .78571429em; border-bottom:1px solid rgba(0,0,0,.1);';

    // Entity name
    $html = '<tr><td style="'.$tdStyle.'" sortValue="'. $sortPrefix . $entity['name'] .'">';
    $nameTxt = $entity['name'];

    // Cross out completed items
    if ($entity['status'] === 'done') {
        $nameTxt = '<strike class="text-muted">'. $entity['name'] .'</strike>';
    }

    if ($nestedLvl === 1) {
        $html .= '<ul><li><a class="bento-link" data-entity="'. $entity['id'] .'" data-type="'. $entity['object_bp_type'] .'">'. $nameTxt .'</a></li></ul>';
    } elseif ($nestedLvl === 2) {
        $html .= '<ul><li class="ql-indent-1" style="padding-left:3em !important;"><a class="bento-link" data-entity="'. $entity['id'] .'" data-type="'. $entity['object_bp_type'] .'">'. $nameTxt .'</a></li></ul>';
    } else {
        $html .= '<a class="bento-link" data-entity="'. $entity['id'] .'" data-type="'. $entity['object_bp_type'] .'">'. $nameTxt .'</a>';
    }
    
    $html .= '</td>';

    // Other fields
    if (is_array($opts['Show'])) {
        foreach ($opts['Show'] as $fieldKey => $field) {
            
            $html .= 
                    
                    '<td style="'.$tdStyle.'">'.
                        getFieldHtml($fieldKey, $type, $entity, $api['objs'], $opts, $api) .
                    '</td>';

        }
    }

    $html .= '</tr>';

    // Show nested items
    if ($opts['Nest'] && !empty($entity[$opts['Nest']])) {

        if (
            $entity[$opts['Nest']]['id'] 
        ) {

            $html .= getSingleRowHtml($entity[$opts['Nest']], $type, $sortPrefix, $opts, $api, $nestedLvl + 1);

        } elseif (
            $entity[$opts['Nest']][0]
            && $entity[$opts['Nest']][0]['id']
        ) {

            foreach ($entity[$opts['Nest']] as $j => $nestedEntity) {

                // Entity name
                $html .= getSingleRowHtml($nestedEntity, $type, $sortPrefix, $opts, $api, $nestedLvl + 1);
                
            }

        }

    }

    return $html;

}

function getRowHtml($type, $list, $api, $opts, $sortPrefix = '') {
    
    // Rows
    foreach ($list as $i => $entity) {
        $html .= getSingleRowHtml($entity, $type, $sortPrefix, $opts, $api);

    }
    
    return $html;

}

return [
    'name' =>       'Table'

    // Model

    // View
    , 'getHtml' =>  function ($api, $subject, $opts) {

        // To return
        $html = '';
        $setKey = $opts['set'];
        
        // If tagged w/instead of parented
        if (substr($setKey, 0, 2) === '##') {
            $setKey = substr($setKey, 1);
        }

        if ($opts['set']) {

            $subject = $api['objs']->getById(
                $subject['object_bp_type']
                , $subject['id']
                , $opts['select']
            );
            
            // Remove nested items, so that we don't repeat them
            if ($opts['Nest']) {
                
                $nestedItems = [];
                if (is_array($subject[$opts['set']])) {
                    foreach ($subject[$opts['set']] as $i => $entity) {

                        if (is_array($entity[$opts['Nest']])) {
                            foreach ($entity[$opts['Nest']] as $j => $nested) {
                                array_push($nestedItems, $nested['id']);
                            }
                        }

                    }

                    $subject[$opts['set']] = __::filter(
                        $subject[$opts['set']]
                        , function ($entity) use ($nestedItems) {

                            $isNested = false;
                            foreach ($nestedItems as $nested) {
                                if ($nested === $entity['id']) {
                                    $isNested = true;
                                }
                            }

                            return !$isNested;

                        }
                    );

                }

            }

            // Get the set name
            switch ($setKey) {

                case '#Action Items':
                    $type = $api['objs']->getBlueprint($setKey);
                    break;

                default:
                    // If its set on the opts, use the blueprint from there -
                    // otherwise, grab a fresh copy.
                    if (
                        !empty($opts)
                        && !empty($opts['entity_type'])
                        && !empty($opts['entity_type']['blueprint'])
                    ) {

                        $type = $opts['entity_type'];

                    } else {

                        $type = $api['objs']->where(
                            'entity_type'
                            , [
                                'bp_name' => substr($setKey, 1)
                            ]
                        )[0];

                    }
                    break;

            }

            if ($type) {

                // The section header
                // !TODO: Place this (collections header) in another merge tag, to be
                // shared across all collection subview views.
                // $html .= '<h2>'. $type['name'] .'</h2>';

                // Content
                if (is_array($type['blueprint'])) {

                    $thStyle = 'text-align:left; padding:.92857143em .78571429em; bottom-bottom:1px solid rgba(34,36,38,.1); border-bottom:1px solid rgba(34,36,38,.1);';
                                
                    // Table headers
                    $html .= '<table class="ui very basic celled sortable table" style="width:100%;">'.
                                '<thead>'.
                                    '<tr>'.
                                        '<th style="'.$thStyle.'">Name</th>';

                    if (is_array($opts['Show'])) {

                        foreach ($opts['Show'] as $fieldKey => $field) {

                            $html .= '<th style="'.$thStyle.' border-left:1px solid rgba(34,36,38,.1);">'. $field['name'] .'</th>';
                            
                            if ($field['fieldType'] === 'state' && is_int($type['blueprint'][$fieldKey]['workflow'])) {

                                //!TODO: Refactor up to rules/actions/merge.php
                                $type['blueprint'][$fieldKey]['workflow'] = $api['objs']->getById(
                                    'entity_workflow'
                                    , $type['blueprint'][$fieldKey]['workflow']
                                    , [
                                        'name' => true
                                        , 'states' => true
                                    ]
                                );

                            }

                        }
                    }

                    $html .=
                                    '</tr>'.
                                '</thead>'.
                                '<tbody>';

                    // $html .= getRowHtml($type, $list, $api, $opts);
                    // !View config details
                    if ($opts['Group by']) {

                        $groups = __::groupBy(
                            $subject[$opts['set']]
                            , function ($entity) use ($opts) {

                                if ($opts['Group by'] === 'Parent') {
                                    return $entity['parent']['id'];
                                }
                                return $entity[$opts['Group by']];

                            }
                        );

                        foreach ($groups as $i => $group) {

                            if ($opts['Group by'] === 'Parent') {

                                $html .=    '<tr><td><h3>'. 
                                                '<a class="bento-link" data-entity="'. $group[0]['parent']['id'] .'" data-type="'. $group[0]['parent']['object_bp_type'] .'">'. $group[0]['parent']['name'] .'</a>'.
                                            '</h3></td></tr>';

                            }

                            $html .= getRowHtml($type, $group, $api, $opts, $group[0]['parent']['name']);

                        }

                    } else {

                        $html .= getRowHtml($type, $subject[$opts['set']], $api, $opts, '');

                    }

                    // Close the table
                    $html .=    '</tbody>'.
                            '<table>';

                }

            } else {

                $html = $opts['set'] . ' <i>Type</i> not found.';

            }

        } else {

            $html = 'No <i>Type</i> set.';

        }

        return $html;

    }
];

?>