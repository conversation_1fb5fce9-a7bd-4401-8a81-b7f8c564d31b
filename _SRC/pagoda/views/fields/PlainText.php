<?php

function getPlainTextHtml ($attachment) {

    $txt = '';

    if (
        !empty($entity[$fieldKey])
        && is_string($entity[$fieldKey])
    ) {

        $txt = $entity[$fieldKey];

    }

    return $txt;

}

function getPlainTextTxt ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $txt = '';

    if (
        !empty($entity[$fieldKey])
        && is_string($entity[$fieldKey])
    ) {

        $txt = $entity[$fieldKey];

    }

    return $txt;

}

?>