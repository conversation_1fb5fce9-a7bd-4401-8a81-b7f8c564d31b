<?php 

function getCurrencyHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '$ 0.00';
    if (is_int($entity[$fieldKey])) {
        
        $html = '$ '. number_format(
            $entity[$fieldKey]/100
            , 2
            , '.'
            , ','
        );

    }

    return $html;

}

function getCurrencyTxt ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '$ 0.00';
    if (is_int($entity[$fieldKey])) {
        
        $html = '$ '. number_format(
            $entity[$fieldKey]/100
            , 2
            , '.'
            , ','
        );

    }

    return $html;

}

?>