<?php 

function getSingleUserBadge ($user) {

    $html = '';
    $imgHtml = '';
    $html .= $user['name'];
    $usrCol = '';
    $usrInitials = '';

    // Show the profile image, if it is set
    if (
        !empty($user['profile_image'])
        && $user['profile_image']['id']
    ) {

        $imgHtml = '<img class="ui circular avatar image" style="width:28px; height:28px; border-radius:50%;" src="https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/'. $user['instance'] .'/'. $user['profile_image']['loc'] .'">';

    } else if ($user) {

        if ($user['color']) {
            $usrCol = $user['color'];
        }
        $usrInitials = $user['fname'][0];
        $usrInitials .= $user['lname'][0];
        
        $imgHtml = '<i class="'. $usrCol .' circle icon user-badge" style="width:28px; height:28px; border-radius:50%;"></i><div class="user-badge-txt">'. $usrInitials .'</div>';

    }

    return '<div style="display:inline-block;" data-tooltip="'. $user['name'] .'" data-inverted="" data-position="top center">'. $imgHtml .'</div>';

}

function getUserFieldHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';

    // Multiple users
    if (
        is_array($entity[$fieldKey])
        && is_array($entity[$fieldKey][0])
    ) {

        foreach ($entity[$fieldKey] as $user) {

            $html .= getSingleUserBadge($user);

        }

    // Single user
    } elseif (
        is_array($entity[$fieldKey])
        && is_numeric($entity[$fieldKey]['id'])
    ) {

        $html = getSingleUserBadge($entity[$fieldKey]);
    
    }

    return $html;

}

?>