<?php 

function getCompanyFieldHtml($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';

    // Do display
    if (!empty($entity[$fieldKey]) && !empty($entity[$fieldKey]['id'])) {

        $html .= $entity[$fieldKey]['name'];

    } else if (!empty($entity[$fieldKey][0]) && !empty($entity[$fieldKey][0]['id'])) {

        if (count($entity[$fieldKey]) == 1) {

            $html .= $entity[$fieldKey][0]['name'];

        } else {

            $html .= '<ul>';

            foreach($entity[$fieldKey] as $i => $value) {

                $html .= '<li>' . $value['name'] . '</li>';

            }

            $html .= '</ul>';

        }

    }

    return $html;

}

?>