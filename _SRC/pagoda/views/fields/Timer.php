<?php

function getTimerHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '0h 0m 0s';
    if (is_int($entity[$fieldKey])) {

        $html = sprintf(
            '%02dh %02dm %02ds'
            , ($entity[$fieldKey]) / 3600
            , (($entity[$fieldKey]) / 60) % 60
            , ($entity[$fieldKey]) % 60
        );

    }

    return $html;

}

function getTimerTxt ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $txt = '0h 0m 0s';
    if (is_int($entity[$fieldKey])) {

        $txt = sprintf(
            '%02dh %02dm %02ds'
            , ($entity[$fieldKey]) / 3600
            , (($entity[$fieldKey]) / 60) % 60
            , ($entity[$fieldKey]) % 60
        );

    }

    return $txt;

}

?>