<?php

function getSingleEdgeHtml($obj) {

    $html = '';
    $loc = '';
    $link = '';
    $icon = 'expand';

    $html .= 
        '<a class="bento-link" data-entity="'. $obj['id'] .'" data-type="'. $obj['object_bp_type'] .'">'. 
            $obj['name'] .
        '</a>';

    return $html;

}

function getSingleEdgeTxt($obj) {

    $txt = '';

    switch ($obj['object_bp_type']) {

        case 'file_meta_data':
            $txt .= $obj['file_name'];
            break;
        
        case 'contacts':
            $txt .= $obj['fname'] .' '. $obj['lname'];
            break;

        default:
            $txt .= $obj['name'];
            break;

    }

    return $txt;

}

function getEdgeFieldHtml($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';

    // Do display
    if (!empty($entity[$fieldKey]) && !empty($entity[$fieldKey]['id'])) {

        $html .= getSingleEdgeHtml($entity[$fieldKey]);

    } else if (!empty($entity[$fieldKey][0]) && !empty($entity[$fieldKey][0]['id'])) {

        if (count($entity[$fieldKey]) == 1) {

            $html .= getSingleEdgeHtml($entity[$fieldKey][0]);

        } else {

            $html .= '<ul class="edge-list">';

            foreach($entity[$fieldKey] as $i => $value) {

                $html .= '<li>' . getSingleEdgeHtml($value) . '</li>';

            }

            $html .= '</ul>';

        }

    }

    return $html;

}

function getEdgeFieldTxt($fieldKey, $type, $entity, $objs, $opts, $api) {

    $txt = '';

    // Do display
    if (!empty($entity[$fieldKey]) && !empty($entity[$fieldKey]['id'])) {

        $txt .= getSingleEdgeTxt($entity[$fieldKey]);

    } else if (!empty($entity[$fieldKey][0]) && !empty($entity[$fieldKey][0]['id'])) {

        if (count($entity[$fieldKey]) == 1) {

            $html .= getSingleEdgeTxt($entity[$fieldKey][0]);

        } else {

            foreach($entity[$fieldKey] as $i => $value) {

                if ($i == 1) {
                    $txt .= getSingleEdgeTxt($value);
                } else {
                    $txt .= ', ' . getSingleEdgeTxt($value);
                }

            }

        }

    }

    return $txt;

}

?>