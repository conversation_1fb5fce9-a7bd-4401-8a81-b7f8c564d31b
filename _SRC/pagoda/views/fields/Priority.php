<?php

function getPriorityHtml($fieldKey, $type, $entity, $objs, $opts, $api) {

    $Levels = [
        0 => [
            'name' => 'Lowest'
            , 'icon' => 'down arrow'
            , 'color' => 'olive'
        ]
        ,  1 => [
            'name' => 'Low'
            , 'icon' => 'down arrow'
            , 'color' => 'green'
        ]
        ,  2 => [
            'name' => 'Medium'
            , 'icon' => 'up arrow'
            , 'color' => 'yellow'
        ]
        ,  3 => [
            'name' => 'High'
            , 'icon' => 'up arrow'
            , 'color' => 'orange'
        ]
        ,  4 => [
            'name' => 'Highest'
            , 'icon' => 'up arrow'
            , 'color' => 'red'
        ]
    ];

    $html = '';
    if ($Levels[$entity[$fieldKey]]) {

        $html = '<i class="ui '. $Levels[$entity[$fieldKey]]['color'] .' '. $Levels[$entity[$fieldKey]]['icon'] .' icon">'.
                '</i>'. $Levels[$entity[$fieldKey]]['name'];
    
    // Default to Lowest
    } else {

        $html = '<i class="ui '. $Levels[$entity[$fieldKey]]['color'] .' '. $Levels[$entity[$fieldKey]]['icon'] .' icon">'.
                '</i>'. $Levels[0]['name'];

    }

    return $html;
    
}

function getPriorityTxt($fieldKey, $type, $entity, $objs, $opts, $api) {

    $Levels = [
        0 => [
            'name' => 'Lowest'
            , 'icon' => 'down arrow'
            , 'color' => 'olive'
        ]
        ,  1 => [
            'name' => 'Low'
            , 'icon' => 'down arrow'
            , 'color' => 'green'
        ]
        ,  2 => [
            'name' => 'Medium'
            , 'icon' => 'up arrow'
            , 'color' => 'yellow'
        ]
        ,  3 => [
            'name' => 'High'
            , 'icon' => 'up arrow'
            , 'color' => 'orange'
        ]
        ,  4 => [
            'name' => 'Highest'
            , 'icon' => 'up arrow'
            , 'color' => 'red'
        ]
    ];

    $html = '';
    if ($Levels[$entity[$fieldKey]]) {

        $html = $Levels[$entity[$fieldKey]]['name'];
    
    // Default to Lowest
    } else {

        $html = $Levels[0]['name'];

    }

    return $html;
    
}

?>