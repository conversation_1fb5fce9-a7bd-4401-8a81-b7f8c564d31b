<?php

function getChecklistValue($option, $opts, $api, $defaultHtml) {

    return getAssociatedValue($option, $opts, $api, $defaultHtml);
    
}

function getChecklistHtml($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';
    $options = [];
    $selectedVals = [];

    // Get the options configured on the select field in the 
    // set's blueprint
    if (
        !empty($type)
        && !empty($type['blueprint'])
        && !empty($type['blueprint'][$fieldKey])
        && !empty($type['blueprint'][$fieldKey]['options'])
        && !empty($type['blueprint'][$fieldKey]['options']['options'])
    ) {

        $fieldOptions = $type['blueprint'][$fieldKey]['options'];
        $options = $type['blueprint'][$fieldKey]['options']['options'];
    
    }

    // Get the values
    foreach ($options as $option) {

        if (!$option['is_archived']) {

            $value = getChecklistValue($option, $opts, $api, $option['name']);

            if ($fieldOptions['single']) {
                
                if (in_array($option['value'], $entity[$fieldKey])) {
                    $html .= '<i class="large circle check icon bento-checked"></i> ' . $value;
                } else {
                    $html .= '<i class="large circle outline icon bento-unchecked"></i> ' . $value;
                }
                
            } else {
                
                if (in_array($option['value'], $entity[$fieldKey])) {
                    $html .= '<i class="large square check icon bento-checked"></i> ' . $value;
                } else {
                    $html .= '<i class="large square outline icon bento-unchecked"></i> ' . $value;
                }
                
            }

            $html .= '</br>';

        }

    }
    
    return $html;

}

function getChecklistTxt($fieldKey, $type, $entity, $objs, $opts, $api) {

    $txt = '';
    $options = [];
    $selectedVals = [];

    // Get the options configured on the select field in the 
    // set's blueprint
    if (
        !empty($type)
        && !empty($type['blueprint'])
        && !empty($type['blueprint'][$fieldKey])
        && !empty($type['blueprint'][$fieldKey]['options'])
        && !empty($type['blueprint'][$fieldKey]['options']['options'])
    ) {

        $fieldOptions = $type['blueprint'][$fieldKey]['options'];
        $options = $type['blueprint'][$fieldKey]['options']['options'];
    
    }

    // Get the values
    $i = 0;
    foreach ($options as $option) {

        if (!$option['is_archived']) {

            if (in_array($option['value'], $entity[$fieldKey])) {
                
                if ($i > 0) {
                    $txt .= ', ';
                }
                $txt .= getChecklistValue($option, $opts, $api, $option['name']);
                $i++;
                
            }

        }

    }
    
    return $txt;

}

?>