<?php

function getDateFieldHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';

    if (
        is_string($entity[$fieldKey])
        && !empty($entity[$fieldKey])
    ) {
        
        try {

            $date = new DateTime($entity[$fieldKey]);
            $html = $date->format('m/d/y');

        } catch (Exception $err) {}
    
    }

    return $html;

}

function getDateFieldTxt ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $txt = '';

    if (
        is_string($entity[$fieldKey])
        && !empty($entity[$fieldKey])
    ) {

        try {

            $date = new DateTime($entity[$fieldKey]);
            $txt = $date->format('m/d/y');
        
        } catch (Exception $err) {
            var_dump($err);
            die();
        }

    }

    return $txt;

}

?>