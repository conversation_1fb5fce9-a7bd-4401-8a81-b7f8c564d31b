<?php

function getSingleAttachmentHtml ($attachment) {

    $html = '';
    $loc = '';
    $link = '';
    $linkType = 'attachment';
    $icon = 'linkify';

    if (!empty($attachment)) {
        switch ($attachment['document_type']) {

            case 'upload':
                $loc = $attachment['file_upload']['loc'];
                $icon = 'download';
                break;

            case 'share':
                $link = $attachment['share_link'];
                break;

            case 'text':
                $linkType = 'document';
                break;

        }
    }

    $html .= 
            '<a class="bento-link" href="'. $link .'" data-entity="'. $attachment['id'] .'" data-type="'. $linkType .'" data-loc="'. $loc .'">'. 
                '<div class="ui basic label"><i class="ui '. $icon .' icon"></i>'. $attachment['name'] .'</div>'.
            '</a>';

    return $html;

}

function getAttachmentHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';

    if (
        !empty($entity[$fieldKey])
        && $entity[$fieldKey]['id']
    ) {

        $html = getSingleAttachmentHtml($entity[$fieldKey]);

    } elseif (
        !empty($entity[$fieldKey])
        && $entity[$fieldKey][0]
        && $entity[$fieldKey][0]['id']
    ) {

        foreach ($entity[$fieldKey] as $i => $attch) {

            if ($i > 0) {
                $html .= ' ';
            }

            $html .= getSingleAttachmentHtml($attch);

        }

    }

    return $html;

}

function getSingleAttachmentTxt ($attachment) {

    $txt .= $attachment['name'];
    return $txt;

}

function getAttachmentTxt ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';

    if (
        !empty($entity[$fieldKey])
        && $entity[$fieldKey]['id']
    ) {

        $html = getSingleAttachmentTxt($entity[$fieldKey]);

    } elseif (
        !empty($entity[$fieldKey])
        && $entity[$fieldKey][0]
        && $entity[$fieldKey][0]['id']
    ) {

        foreach ($entity[$fieldKey] as $i => $attch) {

            if ($i > 0) {
                $html .= ', ';
            }

            $html .= getSingleAttachmentTxt($attch);

        }

    }

    return $html;

}

?>