<?php

// Get Fields
require_once('Address.php');
require_once('Attachments.php');
require_once('Checklist.php');
require_once('Companies.php');
require_once('Currency.php');
require_once('Date.php');
require_once('Edge.php');
require_once('PlainText.php');
require_once('Priority.php');
require_once('Select.php');
require_once('Status.php');
require_once('Timer.php');
require_once('Toggle.php');
require_once('Users.php');

function getAssociatedValue($option, $opts, $api, $defaultHtml) {

    $html = $defaultHtml;
                    
    // Set variables
    $associated = $option['associated'];
    $associatedEntityBPName = $associated['entity']['bp_name'];
    $associatedRecordID = $associated['record']['id'];
    $associatedFieldID = $associated['field']['id'];
    $associatedFieldType = $associated['field']['type'];

    if ($opts['Associated']
        && !empty($associated)
        && !empty($associatedEntityBPName)
        && !empty($associatedRecordID)
        && !empty($associatedFieldID)
        && !empty($associatedFieldType)
    ) {

        // Get the associated record
        $associatedFieldType = $api['objs']->where('entity_type' , ['bp_name' => $associatedEntityBPName])[0];
        $associatedEntity = $api['objs']->getById('' , $associatedRecordID);

        if ( !empty($associatedFieldType) && !empty($associatedEntity) ) {

            $html = getFieldHtml($associatedFieldID, $associatedFieldType, $associatedEntity, $api['objs'], $opts, $api);

        }
        
    }

    return $html;

}

function getFieldHtml($fieldKey, $type, $entity, $objs, $opts, $api) {
    
    $html = '';
    switch ($type['blueprint'][$fieldKey]['fieldType']) {

        case 'attachment':
            $html .= getAttachmentHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'checklist':
            $html .= getChecklistHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'currency':
            $html .= getCurrencyHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;
        
        case 'date':
            $html .= getDateFieldHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;
        
        case 'edge':
            $html .= getEdgeFieldHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'priority':
            $html .= getPriorityHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'select':
            $html .= getSelectHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'state':
            $html .= getStatusHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;
        
        // The overall status value on a record, coelescing the 
        // values set on workflows within its configured fields.
        case 'status': 
            $html .= getOverallStatusHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'user':
        case 'users':
        case 'contacts':
            $html .= getUserFieldHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'companies':
            $html .= getCompanyFieldHtml($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'timer':
            if (is_int($entity[$fieldKey])) {
                $html .= gmdate("H:i:s", $entity[$fieldKey]);
            } else {
                $html .= $entity[$fieldKey];
            }
            break;
        
        default:
            $html .= $entity[$fieldKey];
            break;

    }

    return $html;

}

function getFieldPlainTxt($fieldKey, $type, $entity, $objs, $opts, $api) {

    $fieldType = $type['blueprint'][$fieldKey]['fieldType'];
    if ($fieldKey === 'parent') {
        $fieldType = 'edge';
    }
    $html = '';

    switch ($fieldType) {

        case 'address':
            $html .= getAddressTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'attachment':
            $html .= getAttachmentTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'checklist':
            $html .= getChecklistTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'currency':
            $html .= getCurrencyTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;
        
        case 'date':
        case 'date-rollup':
        case 'next-due-date':
            $html .= getDateFieldTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'edge':
        case 'image':
        case 'locations':
        case 'companies':
        case 'user':
        case 'users':
        case 'contact':
        case 'contacts':
            $html .= getEdgeFieldTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'email-address':
        case 'plain-text':
        case 'formula':
        case 'icon':
        case 'phone':
        case 'quantity':
        case 'detail':
        case 'title':
        case 'url':
            $html .= getPlainTextTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'priority':
            $html .= getPriorityTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'select':
            $html .= getSelectTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'state':
            $html .= getStatusTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'timer':
            $html .= getTimerTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;

        case 'toggle':
            $html .= getToggleTxt($fieldKey, $type, $entity, $objs, $opts, $api);
            break;
        
        // // The overall status value on a record, coelescing the 
        // // values set on workflows within its configured fields.
        // case 'status': 
        //     $html .= getOverallStatusHtml($fieldKey, $type, $entity, $objs, $opts, $api);
        //     break;

        // case 'user':
        // case 'users':
        // case 'contacts':
        //     $html .= getUserFieldHtml($fieldKey, $type, $entity, $objs, $opts, $api);
        //     break;
        
        default:
            $html .= $entity[$fieldKey];
            break;

    }

    return $html;

}

?>