<?php 

function getCurrentState($fieldKey, $type, $entity, $objs, $opts, $api) {

    $currentState = '';
    if ($type['blueprint'][$fieldKey]['workflow']) {
        
        if (is_int($type['blueprint'][$fieldKey]['workflow'])) {

            $type['blueprint'][$fieldKey]['workflow'] = 
                $objs->getById(
                    'entity_workflow'
                    , $type['blueprint'][$fieldKey]['workflow']
                    , ['states' => true]
                );

        }

        if (empty($entity[$fieldKey])) {

            $currentState = __::find($type['blueprint'][$fieldKey]['workflow']['states'], function($state) {
                if (intval($state['isEntryPoint']) == true) {
                    return $state['uid'];
                }
            });

        } else {

            $currentState = __::filter(
                $type['blueprint'][$fieldKey]['workflow']['states']
                , function ($state) use ($entity, $fieldKey) {

                    return (
                        $entity[$fieldKey] === $state['uid']
                    );

                }
            )[0];

        }
        
    }

    return $currentState;

}

function getStatusHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';

    $currentState = getCurrentState($fieldKey, $type, $entity, $objs, $opts, $api);

    if ($currentState) {
        $html = '<label class="ui '. $currentState['color'] .' label"><i class="ui '. $currentState['icon'] .' icon"></i>'. $currentState['name'] .'</label>';
    }

    return $html;
}

function getStatusTxt ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';

    $currentState = getCurrentState($fieldKey, $type, $entity, $objs, $opts, $api);

    if ($currentState) {
        $html = $currentState['name'];
    }

    return $html;
}

function getOverallStatusHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';
    switch ($entity[$fieldKey]) {

        case 'done':
            $html = '<i class="ui large square check icon bento-checked"></i>';
            break;
        
        default:
            $html = '<i class="ui large square outline icon bento-unchecked"></i>';
            break;

    }

    return $html;

}

?>