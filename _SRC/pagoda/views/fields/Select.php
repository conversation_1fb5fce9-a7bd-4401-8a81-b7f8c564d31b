<?php

function getSelectValue($option, $opts, $api, $defaultHtml) {

    return getAssociatedValue($option, $opts, $api, $defaultHtml);
    
}

function getSelectHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';
    $options = [];
    $selectedVals = [];

    // Get the options configured on the select field in the 
    // set's blueprint
    if (
        !empty($type)
        && !empty($type['blueprint'])
        && !empty($type['blueprint'][$fieldKey])
        && !empty($type['blueprint'][$fieldKey]['options'])
        && !empty($type['blueprint'][$fieldKey]['options']['options'])
    ) {

        $options = $type['blueprint'][$fieldKey]['options']['options'];
    
    }

    // Get the selected values
    if (is_numeric($entity[$fieldKey])) {

        foreach ($options as $option) {

            if (intval($option['value']) === intval($entity[$fieldKey])) {

                $defaultHtml = '<label class="ui very basic label">'. $option['name'] .'</label>';

                $html .= getSelectValue($option, $opts, $api, $defaultHtml);

            }

        }

    }
    
    return $html;

}

function getSelectTxt ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '';
    $options = [];
    $selectedVals = [];

    // Get the options configured on the select field in the 
    // set's blueprint
    if (
        !empty($type)
        && !empty($type['blueprint'])
        && !empty($type['blueprint'][$fieldKey])
        && !empty($type['blueprint'][$fieldKey]['options'])
        && !empty($type['blueprint'][$fieldKey]['options']['options'])
    ) {

        $options = $type['blueprint'][$fieldKey]['options']['options'];
    
    }

    // Get the selected values
    if (is_numeric($entity[$fieldKey])) {

        foreach ($options as $option) {

            if (intval($option['value']) === intval($entity[$fieldKey])) {

                $defaultHtml = $option['name'];

                $html .= getSelectValue($option, $opts, $api, $defaultHtml);

            }

        }

    }

    if (empty($html)) {
        $html = 'Not selected';
    }
    
    return $html;

}

?>