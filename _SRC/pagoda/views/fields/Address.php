<?php

function getAddressHtml ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $html = '{{Address Goes Here}}';
    return $html;

}

function getAddressTxt ($fieldKey, $type, $entity, $objs, $opts, $api) {

    $address = $entity[$fieldKey];
    $html = '';

    if (is_array($address)) {
        
        $html = "{$address['street']}, {$address['add2']} {$address['city']}, {$address['state']} {$address['zip']} {$address['country']}";

    }

    return $html;

}

?>