<?php

return [
    'name' =>       'Reservations'
    , 'getHtml' =>  function ($api, $subject, $opts) {

        // To return
        $html = '';

        // Get menu with line items and reservations
        $menu = $api['objs']->getById(
            'inventory_menu'
            , $subject['id']
            , [
                'sections' => [
                    'name' =>       true
                    , 'from' =>     true
                    , 'to' =>       true 
                    , 'items' =>    true
                ]
            ]
        );

        $reservations = $api['objs']->where(
            'item_reservation'
            , [
                'menu' => $menu['id']
            ]
            , ''
            , [
                'status' =>             true
                , 'quantity' =>         true
                , 'quantity_filled' =>  true
                , 'inventory_group' => [
                    'name' =>   true
                ]
                , 'menu_item' =>    'id'
                , 'unit_type' =>    true
                , 'units' =>        true
            ]
        );

        // Show line items
        if (is_array($menu) && is_array($menu['sections'])) {

            date_default_timezone_set('UTC');
            foreach ($menu['sections'] as $i => $section) {

                // Show section header
                $html .= '<h2>'. $section['name'] .'</h2>';
                if (!empty($section['from'])) {

                    $start = (new DateTime($section['from']));
                    if ($_COOKIE && $_COOKIE['tz_off']) {

                        $interval = new DateInterval('PT'. $_COOKIE['tz_off'] .'M');
                        $interval->invert = 1;
                        $start->add($interval);

                    }
                    
                    $start->setTimezone(new DateTimeZone('UTC'));
                    $html .= $start->format('m/d/y h:iA');

                    if (!empty($section['to'])) {

                        $to = (new DateTime($section['to']));
                        if ($_COOKIE && $_COOKIE['tz_off']) {
    
                            $interval = new DateInterval('PT'. $_COOKIE['tz_off'] .'M');
                            $interval->invert = 1;
                            $to->add($interval);
    
                        }
                        
                        $to->setTimezone(new DateTimeZone('UTC'));
                        $html .= ' <i>to</i> '. $to->format('m/d/y h:iA');
    
                    }

                }

                if (is_array($section['items']) && !empty($section['items'])) {

                    // Start table
                    $html .= '<table class="ui very basic celled sortable table">'.
                                '<thead>'.
                                    '<tr>'.
                                        '<th>Item</th>'.
                                        '<th style="width:10%;"><div class="pull-right">Reserved /</div></th>'.
                                        '<th style="width:10%;"><div class="pull-right">Required</div></th>'.
                                    '</tr>'.
                                '</thead>'.
                                '<tbody>';
                    
                    foreach ($section['items'] as $j => $item) {

                        // Parse reservation info
                        $isReserved = true;
                        $reservedQty = 0;
                        $reservedRatio = 1;
                        $lineColor = '';
                        $relevantReservations = __::filter(
                            $reservations
                            , function ($reservation) use($item) {

                                return $reservation['menu_item'] === $item['id'];

                            }
                        );

                        // Show stock items
                        $reservationsHtml = '';
                        foreach ($relevantReservations as $k => $reservation) {
                            
                            if ($reservation['quantity'] === $reservation['quantity_filled']) {
    
                                $isReserved = false;
                                $lineColor = '';

                            } else {
                                
                                $lineColor = 'red';

                            }

                            // Show the units
                            $unitTxt = '';
                            if ($reservation['unit_type'] !== 'quantity') {
                                
                                $units = $GLOBALS['app']->get_unit(
                                    'uscu'
                                    , $reservation['unit_type']
                                );

                                if ($units && $units['base']) {
                                    $unitTxt = '<i>('. $units['base']['abbr'] .')</i>';
                                }

                            }

                            $reservationsHtml .= '<tr>'.
                                    '<td><ul><li><span class="ui '. $lineColor .' text">'. $reservation['inventory_group']['name'] .' '. $unitTxt .'</span></li></ul></td>'.
                                    // '<td><ul data-checked="false"><li>'. $reservation['inventory_group']['name'] .'</li></ul></td>'.
                                    '<td><span class="pull-right ui '. $lineColor .' text">'. $reservation['quantity_filled'] .' /</span></td>'.
                                    '<td><span class="pull-right ui '. $lineColor .' text">'. $reservation['quantity'] .'</span></td>';
                            $reservationsHtml .= '</tr>';

                            // Updated the reserved ratio for the overall item
                            if (($reservation['quantity_filled']/$reservation['quantity']) < $reservedRatio) {
                                $reservedRatio = ($reservation['quantity_filled']/$reservation['quantity']);
                            }

                        }
                        
                        if ($isReserved === true) {
                            
                            $reservedQty = $item['absolute_qty'];
                            
                        } else {

                            $reservedQty = intval($reservedRatio*$item['absolute_qty']);
                            $lineColor = 'red';
                            
                        }

                        $html .= '<tr>'.
                                    '<td><span class="ui '. $lineColor .' text">'. $item['item']['name'] .'</td>'.
                                    '<td><span class="pull-right ui '. $lineColor .' text">'. $reservedQty .' /</span></td>'.
                                    '<td><span class="pull-right ui '. $lineColor .' text">'. $item['absolute_qty'] .'</span></td>'.
                                '</tr>'. $reservationsHtml;

                        
                        
                        $html .=    '</td>'.
                                '</tr>';

                    }

                    $html .= '</tbody>'.
                            '</table>';

                } else {

                    $html .= '<i>No items set</i>';

                }

            }

        } else {

            return 'No line items/reservations found.';

        }

        return $html;

    }
];

?>