<?php

return [
    'name' =>       'me'

    // Model

    // View
    , 'getHtml' =>  function ($api, $subject, $opts) use ($Views) {

        $currentUser = $api['objs']->getById(
            'users'
            , intval($_COOKIE['uid'])
            , [
                'fname' =>      true
                , 'lname' =>    true
                , 'name' =>     true
            ]
        );

        return $currentUser['name'];

    }

    , 'getPlainText' =>  function ($api, $subject, $opts) {

        $currentUser = $api['objs']->getById(
            'users'
            , intval($_COOKIE['uid'])
            , [
                'fname' =>      true
                , 'lname' =>    true
                , 'name' =>     true
            ]
        );

        return $currentUser['name'];

    }

];

?>