<?php

function getCompanyFromContext ($objs, $subject) {

    $company = false;

    $context = $objs->getById(
        ''
        , $subject['id']
        , [
            'tagged_with' => true
        ]
    );
    $tags = $objs->getById(
        'companies'
        , $context['tagged_with']
    );

    foreach ($tags as $tag) {

        if ($tag['object_bp_type'] === 'companies') {
            $company = $tag;
        }

    }
    
    return $company;

}

return [
    'name' =>       'Company'

    // Model

    // View
    , 'getHtml' =>  function ($api, $subject, $opts) {

        $company = getCompanyFromContext($api['objs'], $subject);
        $txt = $company['name'];
        return $txt;

    }

    , 'getPlainText' =>  function ($api, $subject, $opts) {

        $company = getCompanyFromContext($api['objs'], $subject);
        $txt = $company['name'];
        return $txt;

    }

];

?>