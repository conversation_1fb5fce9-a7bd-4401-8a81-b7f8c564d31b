<?php
	
/*
	header('Access-Control-Allow-Origin: *');
	echo 'connecting...<br />';
	
	// dev db
	$pdo = new PDO("pgsql:host=localhost;port=5432;dbname=pagoda_dev;user=postgres;password=********");
	
	error_reporting(E_ALL);
	ini_set('display_errors', '1');
		
	$statement = $pdo->prepare(
		"SELECT * FROM blueprints;"
	);
	
	if($statement->execute()){
		
		echo 'blueprints gathered;<br />';
		
		$blueprints = $statement->fetchAll();
		
		foreach($blueprints as $blueprint){
			
			$blueprint['blueprint'] = json_decode($blueprint['blueprint'], 1);

			$blueprint['blueprint']['data_source'] = array(
				'name' => 'Data Source',
				'type' => 'int',
				'immutable' => true
			);
			
			$blueprint['blueprint']['data_source_id'] = array(
				'name' => 'Data Source Id',
				'type' => 'int',
				'immutable' => true
			);
			
			$blueprint['blueprint'] = json_encode($blueprint['blueprint']);
			
			// update blueprint
			$statement = $pdo->prepare(
				"UPDATE blueprints SET blueprint = '". $blueprint['blueprint'] ."' WHERE id = ". $blueprint['id'] .";"
			);
			
			if($statement->execute()){
				echo $blueprint['blueprint_name'] .' updated;<br />';
			}else{
				var_dump($statement->errorInfo());
			}
			
		}
		
	}
*/
	
?>