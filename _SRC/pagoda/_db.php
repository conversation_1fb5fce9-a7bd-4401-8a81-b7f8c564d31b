<?php

error_reporting(0);

/*
define("HOST", "localhost"); // The host you want to connect to.

define("USER", "root"); // The database username.

define("PASSWORD", "432650Rv"); // The database password. 

define("DATABASE", "infinity");
*/


define("HOST", "localhost"); // The host you want to connect to.

define("USER", "infinity"); // The database username.

define("PASSWORD", "7*66Glep"); // The database password. 

define("DATABASE", "infinity_hospitality");



$connect = mysql_connect(HOST,USER,PASSWORD);

$db = mysql_select_db(DATABASE,$connect);

$mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);

try {



	$pdo = new PDO("mysql:host=localhost;dbname=infinity", 'root', '432650Rv');
	
	//$pdo = new PDO("mysql:host=localhost;dbname=infinity_hospitality", 'infinity', '7*66Glep');
	
	$postgres = new PDO("pgsql:host=localhost;port=5432;dbname=infinity;user=postgres;password=**************");

	

}catch(PDOException $e){



	echo $e->getMessage();



}

			

?>