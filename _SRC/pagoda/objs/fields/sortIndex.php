<?php

if (!class_exists('Rank')) {
class Rank
{

    public const MIN_LENGTH = 12;
    public const MAX_LENGTH = 22;
    public const NEW_DISTANCE = 500;
    public const MIN_CHAR = '0';
    public const MAX_CHAR = 'z';

    private $prev;
    private $next;

    /**
     * Rank constructor.
     */
    public function __construct(string $prev, string $next)
    {
        $this->setPrev($prev);
        $this->setNext($next);
    }

    private function setPrev(string $prev)
    {
        $this->prev = $prev === '' ? self::MIN_CHAR : $prev;
    }

    private function setNext(string $next)
    {
        $this->next = $next === '' ? self::MAX_CHAR : $next;
    }

    private function rmSpecialChars (string $string) {

        return str_replace('\\', ']', $string);

    }

    public function get()
    {
        $rank = '';
        $i = 0;

        while (true) {
            
            if ($i >= self::MAX_LENGTH){
                break;
            }

            $prevChar = $this->getChar($this->prev, $i, self::MIN_CHAR);
            $nextChar = $this->getChar($this->next, $i, self::MAX_CHAR);

            if ($prevChar === $nextChar) {
                $rank .= $prevChar;
                $i++;
                continue;
            }

            $midChar = $this->mid($prevChar, $nextChar);
            if (in_array($midChar, [$prevChar, $nextChar])) {
                $rank .= $prevChar;
                $i++;
                continue;
            }

//            var_dump($midChar);
//            die;

            $rank .= $midChar;
            break;
        }

        return $this->rmSpecialChars($rank);

    }

    public function getAtTop()
    {

        $rank = $this->prev;
        // error_reporting(E_ALL);
        // ini_set('display_errors', '1');	
        if (strlen($rank) < self::MIN_LENGTH) {
            for ($i = 0; $i < self::MIN_LENGTH; $i++) {

                if (!$rank[$i]) {
                    $rank[$i] = self::MIN_CHAR;
                }

            }
        }

        $i = 1;
        while (true) {

            if ($i > self::MIN_LENGTH) {
                break;
            }

            if (ord($rank[self::MIN_LENGTH - $i]) < ord(self::MAX_CHAR)) {

                if (ord($rank[self::MIN_LENGTH - $i]) + 1 === 92) {

                    $rank[self::MIN_LENGTH - $i] = chr(
                        ord($rank[self::MIN_LENGTH - $i]) + 2
                    );

                } else {

                    $rank[self::MIN_LENGTH - $i] = chr(
                        ord($rank[self::MIN_LENGTH - $i]) + 1
                    );

                }
                
                break;

            } else {

                $rank[self::MIN_LENGTH - $i] = self::MIN_CHAR;

            }

            $i++;
            continue;

        }

        return $this->rmSpecialChars($rank);

    }

    public function getAtBottom()
    {

        $rank = $this->prev;
        // error_reporting(E_ALL);
        // ini_set('display_errors', '1');	
        if (strlen($rank) < self::MIN_LENGTH) {
            for ($i = 0; $i < self::MIN_LENGTH; $i++) {

                if (!$rank[$i]) {
                    $rank[$i] = self::MIN_CHAR;
                }

            }
        }

        $i = 1;
        while (true) {

            if ($i > self::MIN_LENGTH) {
                break;
            }

            if (ord($rank[self::MIN_LENGTH - $i]) > ord(self::MIN_CHAR)) {

                if (ord($rank[self::MIN_LENGTH - $i]) - 1 === 92) {

                    $rank[self::MIN_LENGTH - $i] = chr(
                        ord($rank[self::MIN_LENGTH - $i]) - 2
                    );

                } else {

                    $rank[self::MIN_LENGTH - $i] = chr(
                        ord($rank[self::MIN_LENGTH - $i]) - 1
                    );

                }
                
                break;

            } else {

                $rank[self::MIN_LENGTH - $i] = self::MAX_CHAR;

            }

            $i++;
            continue;

        }

        return $this->rmSpecialChars($rank);

    }

//!TODO: Check out if the 'int' type is the cause here
    private function getChar(string $s, int $i, string $defaultChar)
    {
        if ($i === 92) {
            $i++;
        }
        return $s[$i] ?? $defaultChar;
    }

    private function mid(string $prev, string $next)
    {

        $i = (ord($prev) + ord($next)) / 2;
        if ($newVal === 92) {
            $i++;
        }
        return chr($i);
        
    }


}

}

return [
    'name' =>   'sortIndex'
    , 'setNext' =>  function ($obj, $key, $blueprint, $objs, $type = 'top') {

        if ($type === 'bottom') {
            $sortOrder = 'ASC';
        } else {
            $sortOrder = 'DESC';
        }

        $top = $objs->where(
            '#.' // $objectType
            , [
                'sortIndexIsBalanced' => [
                    'type' => 		'not_equal'
                    , 'value' => 	true
                ]
            ] 					// $where
            , '' 				// $additionalClause
            , [
                'name' => 		true
            ] 					// $select
            , true 				// $isPaged
            , 0 		        // $offset
            , 'sortIndex' 	    // $sortCol
            , $sortOrder 		// $sortDir
            , 1 		        // $limit
            , null 				// $sum
            , [] 				// depricated select arr.
            , 'string' 			// $sortCast
            , true 				// $forceLimit
        )[0];

        $prev = '';
        $next = '';
        
        if (is_string($top['sort_index'])) {
            $prev = $top['sort_index'];
        }

        if ($prev === '') {

            return 'UUUUUUUUUUUU';

        }
        $rank = (new Rank($prev, ''))->getAtTop();
        // $rank = (new Rank($prev, $next))->get();

        // var_dump($prev, $rank);
        // die();
        return $rank;

        // $rank = (new Rank($prev, $next))->get();
        // return $rank;

    }
    , 'setBefore' =>  function ($prev) {

        if ($prev === '') {

            return 'UUUUUUUUUUUU';

        }

        $rank = (new Rank($prev, ''))->getAtTop();
        return $rank;

    }
    , 'setAfter' =>  function ($prev) {

        if ($prev === '') {

            return 'UUUUUUUUUUUU';

        }

        $rank = (new Rank($prev, ''))->getAtBottom();
        return $rank;

    }
    , 'setBetween' =>  function ($prev, $next) {
    // , 'getMoveIndex' =>  function ($prev, $next) {

        $rank = (new Rank($prev, $next))->get();
        return $rank;

    }
    
];

?>