<?php

if(!empty($_REQUEST['do'])){

	header('Access-Control-Allow-Origin: *');
// 	error_reporting(E_ALL);
// 	ini_set('display_errors', '1');
	// dev (for blueprints table)
// 	$devPostgres = new PDO("pgsql:host=*********;dbname=infinity", 'postgres', 'OIILdLlsX21Rqn');
	
	// production
// 	$mysql = new PDO("mysql:host=localhost;dbname=infinity_hospitality", 'infinity', '7*66Glep');
/*
	try {
		
		$postgres = new PDO("pgsql:host=localhost;dbname=admin_binpostgres", 'binpostgres', 'Ub55a3_b');
		echo 'postgres connected -- '. $postgres->getAttribute(PDO::ATTR_CONNECTION_STATUS) .';<br />';
		
		try {
			
			$mysql = new PDO("mysql:host=localhost;dbname=infinity_hospitality", 'infinity', '7*66Glep');
			echo 'mysql connected -- '. $mysql->getAttribute(PDO::ATTR_CONNECTION_STATUS) .';<br />';
			
			$transferApp = new dbTransfer($postgres, $mysql);
// 			echo $transferApp->$_REQUEST['do']();
			echo $transferApp->test();
			
		} catch (PDOException $e){
			
			echo $e->getMessage();
			print_r($e->getTrace());
			
		}
		
	} catch (PDOException $e) {
		
	   echo $e->getMessage();
	   print_r($e->getTrace());
	   
	}
*/
	
// 	$transferApp = new dbTransfer($postgres, $devPostgres);
// 	$transferApp = new dbTransfer($postgres, $mysql);

// 	echo $transferApp->$_REQUEST['do']();
// 	phpinfo();
	
}
	
class dbTransfer {
	
	protected $to, $from;
	
	function __construct($pgPDO, $mysqlPDO){
		
		$this->to = $pgPDO;
		$this->from = $mysqlPDO;
		
	}
	
	function test(){

// 		$statement = $this->from->prepare("SELECT * FROM object_blueprints WHERE object_type = 'timeline_events';");
		$statement = $this->from->prepare("SELECT * FROM timeline_events WHERE eventId = 785;");
		if($statement->execute()){
			
			var_dump($statement->fetchAll());
			
		}else{
			
			var_dump($statement->errorInfo());
			
		}
		
	}
	
	public function cloneBlueprintsTable(){
		
		// create new blueprints table
		$statement = $this->to->prepare(
			"CREATE TABLE IF NOT EXISTS object_blueprints (
				id SERIAL PRIMARY KEY, 
				access_level INT,
				blueprint JSONB, 
				object_type CHAR(35),
				date_created TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
			);"
		);
		
		if($statement->execute()){
			
			echo 'blueprints table created';
			
			$statement = $this->from->prepare(
				"SELECT * FROM object_blueprints;"
			);
			
			if($statement->execute()){
				
				echo 'blueprints gathered;<br />';
				
				$blueprints = $statement->fetchAll();
				$count  = count($blueprints);
				$transferred = 0;
				
				foreach($blueprints as $blueprint){
					
					// transfer blueprint
					$statement = $this->to->prepare(
						"INSERT INTO object_blueprints (object_type, access_level, blueprint, date_created) VALUES ('". $blueprint['object_type'] ."', 0, '". $blueprint['blueprint'] ."', DEFAULT);"
					);
					
					if($statement->execute()){
						$transferred++;
					}else{
						var_dump($statement->errorInfo());
					}
					
				}
				
			}
		
		}
		
		echo 'complete; '. $transferred .' / '. $count .' blueprints transferred.';
		
		return true;
		
	}
	
	public function transferBlueprintObject($tableName){

		$tableName = $_REQUEST['table-name'];
		echo 'checking '. $tableName .' table;<br />';
		
		$statement = $this->from->prepare(
			"SELECT * FROM object_blueprints WHERE object_type = '$tableName'"
		);
		
		if($statement->execute()){
			
			$blueprint = $statement->fetchAll()[0];
			if(isset($blueprint['blueprint'])){
				
				echo $tableName .' verified;<br />';
				
				// transfer blueprint
				/*
$statement = $this->to->prepare(
					"INSERT INTO object_blueprints (object_type, access_level, blueprint, date_created) VALUES ('$tableName', 0, '". $blueprint['blueprint'] ."', DEFAULT);"
				);
				
				if($statement->execute()){
					
					echo 'blueprint cloned;<br /><br />';
*/
					
					// create table
					$statement = $this->to->prepare(
						
						"CREATE TABLE IF NOT EXISTS ". $tableName ." (
							id SERIAL PRIMARY KEY, 
							object_data JSONB, 
							date_created TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
						);"
						
					);
					
					if($statement->execute()){
						
						echo 'table created;<br />gathering data...<br />';
						
						// gather data
						$statement = $this->from->prepare(
							"SELECT * FROM $tableName;"
						);
						$total = 0;
						$transferred = 0;
							
						if($statement->execute()){
							
							$objects = $statement->fetchAll();
							if(is_array($objects)){
								foreach($objects as $object){
									
									$statement = $this->to->prepare(
										"INSERT INTO $tableName (id, object_data, date_created) VALUES (". $object['id'] .", '". $object['object_data'] ."', '". $object['date_created'] ."');"
									);

									if($statement->execute()){
										$transferred++;
									}else{
										echo 'ERROR: <br />';
										var_dump($statement);
									}
									
									$total++;
									
								}
							}
							
							
						}
						
						echo $transferred .'/'. $total .' objects transferred;';
						
					}
					
				/*
}else{
					echo 'ERROR:<br />';
					var_dump($statement);
				}
*/
				
			}
			
		}
		
	}
	
	public function transferTable($tableName){
		
		$tableName = $_REQUEST['table-name'];
		echo 'checking ' .$tableName. ' table;<br />';
		// if table exists
		$statement = $this->from->prepare(
			'SELECT 1 FROM '. $tableName
		);
		
		if($statement->execute()){
			
			echo $tableName .' table verified;<br />';
			
			// create blueprint
// 			if($this->createBlueprint($tableName)){
				
// 				echo 'blueprint created;<br />';
				// create table
				$statement = $this->to->prepare(
					
					"CREATE TABLE IF NOT EXISTS ". $tableName ." (
						id SERIAL PRIMARY KEY, 
						object_data JSONB, 
						date_created TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
					);"
					
				);
				
				if($statement->execute()){
					
					echo 'table created;<br />';
					
					if($this->transferTableContents($tableName)){
						
						echo 'data transferred;<br />';
						return true;
						
					}
					
				}
// 			}
			
		}
		
	}
	
	public function createBlueprint($tableName){
		
		// the object blueprint for new system
		$blueprint = array();
		
		// get sample data
		$statement = $this->from->prepare(
			"SELECT * FROM ". $tableName ." LIMIT 1;"
		);
		if($statement->execute()){
			
			$sample = $statement->fetchAll()[0];
			
			// get column types
			$statement = $this->from->prepare(
				"SHOW COLUMNS FROM ". $tableName .";"
			);
			
			if($statement->execute()){
				
				$columnData = $statement->fetchAll();
				
				foreach($columnData as $column){
					
					// every blueprint will have these fields stored in their json 'object_date' column as well
					$blueprint['id'] = array(
						'type' => 'int',
						'name' => 'id',
						'immutable' => true
					);
					$blueprint['date_created'] = array(
						'type' => 'string',
						'name' => 'date_created',
						'immutable' => true
					);
					
					// ignore 'id' and 'date_created' fields, as these will be generated automatically
					if($column['Field'] !== 'id' and $column['Field'] !== 'date_created'){
						
						$blueprint[$column['Field']] = array(
							'name' => $column['Field'],
							'immutable' => false
						);
						
						// get blueprint field type
						if(strpos($column['Type'], 'int') !== false){
							
							$blueprint[$column['Field']]['type'] = 'int';
							
						}elseif(strpos($column['Type'], 'bool') !== false){
							
							$blueprint[$column['Field']]['type'] = 'bool';
							
						}elseif(json_decode($sample[$column['Field']], true) !== null){
							
							if(is_int(json_decode($sample[$column['Field']], true))){
								$blueprint[$column['Field']]['type'] = 'int';
							}else{
								$blueprint[$column['Field']]['type'] = 'object';
							}
							
						}else{
							
							$blueprint[$column['Field']]['type'] = 'string';
							
						}
						
					}
					
				}
				
				$toStatement = $this->to->prepare(
					"INSERT INTO object_blueprints".
					"(".
						"access_level, blueprint, object_type".
					")".
					"VALUES (".
						"0, '". json_encode($blueprint) ."', '". $tableName ."'".
					");"
						
				);
				
				if($toStatement->execute()){
					return true;
				}else{
					return false;
				}
				
			}
			
		}
		
	}
	
	public function transferTableContents($tableName = null){
		
		if($tableName == null){
			$tableName = $_REQUEST['table-name'];
		}		
		
		// get blueprint
		$blueprintStatement = $this->to->prepare(
			"SELECT * FROM object_blueprints WHERE object_type = '". $tableName ."';"
		);
		
		if($blueprintStatement->execute()){
			
			echo 'blueprint retrieved;<br />';
			$blueprint = $blueprintStatement->fetchAll()[0];
			$blueprint = json_decode($blueprint['blueprint'], true);
		
			// get data
			$statement = $this->from->prepare(
				"SELECT * FROM ". $tableName .";"
			);
			/*
$statement = $this->from->prepare(
				"SELECT * FROM ". $tableName ." LIMIT 20000 OFFSET 100000;"
			);
*/
			
			$total = 0;
			$totalAdded = 0;
			$totalDataAdded = 0;
			
			if($statement->execute()){
				
				$data = $statement->fetchAll();
				
				echo 'processing data;<br />';
				if(is_array($data)){
					foreach($data as $datum){
						
						$objectData = array();
						foreach($blueprint as $key => $field){
							
							if($field['type'] == 'object'){
								
								$objectData[$key] = json_decode(pg_escape_string(utf8_encode($datum[$key])));
								
							}elseif($field['type'] == 'int'){
								
								$objectData[$key] = intval($datum[$key]);
								
							}else{
								
								$objectData[$key] = utf8_encode(pg_escape_string($datum[$key]));
								
							}
							
						}

						if(isset($datum['date_created']) and $datum['date_created'] != '0000-00-00 00:00:00'){
							
							$statement = $this->to->prepare(
								"INSERT INTO $tableName (id, date_created) VALUES (". $datum['id'] .", '". $datum['date_created'] ."') RETURNING *;"
							);
							
						}else{
							
							$statement = $this->to->prepare(
								"INSERT INTO $tableName (id, object_data, date_created) VALUES (". $datum['id'] .", '{}', DEFAULT) RETURNING *;"
							);
							
						}
							
						if($statement->execute()){
							
							$info = $statement->fetchAll()[0];
							$objectData['id'] = intval($info['id']);
							$objectData['date_created'] = $info['date_created'];

							$string = "UPDATE $tableName SET object_data = '". json_encode($objectData) ."' WHERE id = ". $objectData['id'] .";";

							$statement = $this->to->prepare($string);

							if(!$statement->execute()){
								echo $this->to->errorInfo();
							}else{
								$totalDataAdded++;
							}
							$totalAdded++;
				
						}else{
							var_dump($statement->errorInfo());
						}

						$total++;
						
					}
				}
				
				
			}else{
				
				var_dump($statement->errorInfo());
				
			}
		}
		
		echo $totalAdded .'/'. $total .' entries created; '. $totalDataAdded .'/'. $total .' entry data cloned;<br />';
		return true;
		
	}
	
	public function updateTableKeys(){
		
		$statement = $this->to->prepare(
			"SELECT table_name
			FROM information_schema.tables
			WHERE table_schema='public'
			AND table_type='BASE TABLE';"
		);
		
		if($statement->execute()){
			
			$tables = $statement->fetchAll();
			$count = count($tables);
			$i=0;
			
			foreach($tables as $table){
				
				$statement = $this->to->prepare("SELECT setval('". $table['table_name'] ."_id_seq', (SELECT MAX(id) FROM ". $table['table_name'] .")+1)");
				if($statement->execute()){
					$i++;
				}else{
					echo $table['table_name'] .' counter updated;<br />';
					echo $statement->errorInfo();
				}
				
			}
			
		}
		
		echo 'complete -- ('. $i .'/'. $count .') table id key counter updated';
		return true;
		
	}
	
}
	
?>