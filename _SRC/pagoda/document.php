<?php
/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

// Suppress warnings while echoing to build html for the page
error_reporting(E_ERROR | E_PARSE);

header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
date_default_timezone_set('America/Chicago');

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'rules/rules.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';

require APP_ROOT.'../app/_go/header.php';

// Get variables
$instanceName = !empty($_GET['instance']) ? $_GET['instance'] : '';
$documentHash = !empty($_GET['document']) ? $_GET['document'] : '';

if ( empty($instanceName) || empty($documentHash) ) {

	echo "There was an error retrieving this document. Code 1.";

} else {

	// Get database object
	$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");
	
	// Build the rules api
	$rules = new Rules($appConfig);

	// Get page objects
	$pgObjects = new pgObjects($pdo, $instanceName, $rules);	

	// Get the instance
	$instance = $pgObjects->where('instances', array('instance' => $instanceName))[0];

	// Get instance logo
	$instanceLogo = $pgObjects->where(
		'company_logo'
		, [
			'is_primary' => 'yes'
		]
		, ''
		, [
			'company_logo' => true
		]
	)[0];

	// Get the document
	$documentID = $documentHash;
	$document = $pgObjects->getById('contracts', $documentID);

	if ( empty($instance) || empty($document) ) {

		echo "There was an error retrieving this document. Code 2.";

	} else {

		$contentHtml = '';

		// If the document was not created specifically for this endpoint 
		// (active = 'No') AND the document is private (is_public = false)
		// then just show an error message saying the document is private
		if (
			$document['active'] !== 'No'
			&& $document['is_public'] !== true
		) {

			$html = '<div class="ui huge icon message">'.
						'<i class="warning icon"></i>'.
						'<div class="content">'.
							'<div class="header">This document is private</div>'.
							'<p>Contact the document owner about making this public.</p>'.
						'</div>'.
					'</div>';
			
			echo $html;
			return;
		
		}

		switch ($document['active']) {

			// Merge active documents
			case 'Yes':

				$context = $pgObjects->getById(
					'contracts'
					, $document['related_object']
				);
				
				$contentHtml = $pgObjects->runSteps(
					$context
					, [
						'merge' => [
							'obj' => 		$context
							, 'template' => $document['html_string']
						]
					]
					, true
				)['memo'];
				break;
			
			// Just use the static html for inactive documents
			default: 
				$contentHtml = $document['html_string'];
				break;

		}

		// Setup the HTML
		echo '<div id="topNav" class="sixteen wide column" style="height:55px; border-bottom:1px solid #ebebeb; margin-bottom:30px;">';
			echo '<div class="ui stackable grid" style="padding:0px 14px 0 14px;">';
				echo '<div class="two wide column" style="margin:0; padding-top:0; padding-bottom:0; padding-left:8px !important; padding-right:8px !important; width:100px !important; text-align:center;">';
					echo '<div style="display:inline-block; height:100%; vertical-align:middle;"></div>';
					echo '<div style="display:inline-block;">';
						echo '<img id="navLogo" class="ui centered image" style="display:inline-block !important;" src="https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/'.$instanceName.'/'.$instanceLogo['company_logo']['loc'].'">';
					echo '</div>';
				echo '</div>';
				echo '<div style="padding-top: 0; padding-bottom: 0; display: flex; flex-direction: column; flex: 1 1 auto;">';
					echo '<div class="ui breadcrumb" style="width:100%; display:block; min-height:55px; max-height:55px; padding:10px; padding-left:0;">';
						echo '<div class="section disabled link">';
							echo $instance['systemName'];
						echo '</div>';
						echo '<div class="divider"><i class="ui chevron right icon" style="margin:0 !important;"></i></div>';
						echo '<div class="active section disabled link">';
							echo $document['name'];
						echo '</div>';
					echo '</div>';
				echo '</div>';
			echo '</div>';
		echo '</div>';	

		$style = 'margin:0 auto; padding:40px !important; box-shadow:0 15px 50px 0px rgb(0 0 0 / 10%) !important;';
		if ($document['orientation'] === 'landscape') {
			$style .= 'max-width:1253px;';
		} else {
			$style .= 'max-width:830px;';
		}
		
		echo '<div style="padding-bottom:60px;"><div id="bentoDocumentEditorPreviewer" class="round-border" style="'.$style.'">';
			echo $contentHtml;
		echo '</div></div>';

	}

}
			
// Exit script
exit();
?>