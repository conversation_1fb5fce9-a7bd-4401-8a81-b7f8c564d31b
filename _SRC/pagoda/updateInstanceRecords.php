<?php

// echo ord('0') .'<br / >';
// echo ord('z') .'<br / >';
// echo ord('a') .'a<br / >';
// echo ord('U') .'U<br / >';
// echo chr(
// 	(ord('0') + ord('z')) / 2
// ) .'<br / >';
die();
header('Access-Control-Allow-Origin: *');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
date_default_timezone_set('America/Chicago');

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
$pdo = require_once APP_ROOT.'getDbConnection.php';

$instanceID = $_REQUEST['pagodaAPIKey'];
$db = new pgObjects($pdo, $instanceID, $rules, $write, $writeDocsDB);

// if (empty($instanceID)) {
	// die();
// }

$pageSize = 100;

$instances = $db->whereAcrossInstances(
	'instances' 		// $objectType
	, [
		'enabled' => 'Enabled'
	] 					// $where
	, '' 				// $additionalClause
	, [
		'is_portal' => 	true
	] 					// $select
	, true 				// $isPaged
	, 0 				// $offset
	, 'date_created' 	// $sortCol
	, 'desc' 			// $sortDir
	, 1000 				// $limit
	, null 				// $sum
	, [] 				// depricated select arr.
	, 'date' 			// $sortCast
	, false 			// $forceLimit
);

$prevIndex = '';

error_reporting(E_ERROR);
ini_set('display_errors', '1');

foreach($instances as $instance){

	$portalAccessToken = $db->whereAcrossInstances('portal_access_token', array('client' => $instance['id']))[0];

	if (!empty($instance['parentInstance'])) {
		
		// Skip instances that already have the property set

	} elseif (!empty($portalAccessToken['instance'])) {

		try {

			$db->setInstance($instance['instance']);
			$resp = $db->update(
				'instances'
				, [
					'id' => $instance['id']
					, 'parentInstance' => $portalAccessToken['instance']
				]
			);
			// var_dump($resp);
			// die();

		} catch (Exception $e) {

			echo $instance['id'] ." NOT UPDATED!";
			var_dump($e);
			die();

		}

	} else {

		// echo $instance['id'] ." NOT UPDATED | No Error!";
		// die();

	}
	
}

echo 'COMPLETE<br />';
die();

?>