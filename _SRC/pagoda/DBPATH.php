<?php 

	$BENTO_DB_PATH = getenv('BENTO_DATABASE_URL');
	$BENTO_DB_PORT = getenv('BENTO_DATABASE_PORT');
	$BENTO_DB_NAME = getenv('BENTO_DATABASE_NAME');
	$BENTO_DB_USER = getenv('BENTO_DATABASE_USER');
	$BENTO_DB_PASSWORD = getenv('BENTO_DATABASE_PASSWORD');
	$BENTO_DB_SSL = getenv('BENTO_DATABASE_SSL_FLAG');
	
	// docs db path
	$GLOBALS['BENTO_DB_DOCS_NAME'] = getenv('BENTO_DOCS_DATABASE_NAME');
	$GLOBALS['BENTO_DOCS_DB_PATH'] = getenv('BENTO_DOCUMENTS_URL');
	$GLOBALS['BENTO_DOCS_DB_WRITE_PATH'] = getenv('BENTO_DOCUMENTS_WRITE_URL');
	
	// db read path
	$GLOBALS['BENTO_DB_PATH'] = getenv('BENTO_DATABASE_URL');
	$GLOBALS['BENTO_DB_PORT'] = getenv('BENTO_DATABASE_PORT');
	$GLOBALS['BENTO_DB_NAME'] = getenv('BENTO_DATABASE_NAME');
	$GLOBALS['BENTO_DB_USER'] = getenv('BENTO_DATABASE_USER');
	$GLOBALS['BENTO_DB_PASSWORD'] = getenv('BENTO_DATABASE_PASSWORD');
	$GLOBALS['BENTO_DB_SSL'] = getenv('BENTO_DATABASE_SSL_FLAG');
	
	// db write path
	$GLOBALS['BENTO_DB_WRITE_PATH'] = getenv('BENTO_DATABASE_WRITE_URL');
	$GLOBALS['BENTO_DB_DOCS_DATABASE_NAME'] = getenv('BENTO_DOCS_DATABASE_NAME');

?>
