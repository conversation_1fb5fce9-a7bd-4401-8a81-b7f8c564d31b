 <?php
/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With, X-Body-Signature, Body-Signature, body-signature");
header('Access-Control-Allow-Credentials: true');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
date_default_timezone_set('America/Chicago');
	
require APP_ROOT.'../DBPATH.php';

require_once APP_ROOT.'../lib/_.php';
require_once APP_ROOT.'../vendor/autoload.php';
require_once APP_ROOT.'../_objects.php';
require_once APP_ROOT.'../_pgObjectsMT.php';
require_once APP_ROOT.'../_communications.php';
require_once APP_ROOT.'../_cookiesPG.php';
// require_once APP_ROOT.'../files/_fileApi.php';
require_once APP_ROOT.'../_excel.php';


// Set headers to JSON
header('Content-type:application/json;charset=utf-8');

// Get the JSON request
$request = @file_get_contents('php://input');

// Decode the JSON request
$json = json_decode($request);

$headers = getallheaders();

var_dump($_POST);
echo '<br/><br/>';
var_dump($request);
echo '<br/><br/>';   
var_dump($json);
echo '<br/><br/>';
var_dump($headers);  


			
// Exit script
exit();
?> 