<?php

// error_reporting(E_ERROR);
// ini_set('display_errors', '1');

header('Access-Control-Allow-Origin: *');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
date_default_timezone_set('America/Chicago');

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';
$pdo = require_once APP_ROOT.'getDbConnection.php';

$instanceID = $_REQUEST['pagodaAPIKey'];
$db = new pgObjects($pdo, $instanceID, $rules, $write, $writeDocsDB);

if (empty($instanceID)) {
	die();
}

$pageSize = 100;

$sets = $db->where(
	'entity_type' 		// $objectType
	, [
		'is_task' =>    true
	] 					// $where
	, '' 				// $additionalClause
	, [
		'name' => 		true
		, 'bp_name' => 	true
	] 					// $select
	, true 				// $isPaged
	, 0 				// $offset
	, 'date_created' 	// $sortCol
	, 'desc' 			// $sortDir
	, 1000 				// $limit
	, null 				// $sum
	, [] 				// depricated select arr.
	, 'date' 			// $sortCast
	, false 			// $forceLimit
);

$prevIndex = '';

// error_reporting(E_ERROR);
// ini_set('display_errors', '1');

foreach ($sets as $set) {

	echo $set['name'] .' | #'. $set['bp_name'] .': ';
    $statement = $pdo->prepare(
        "UPDATE objects * SET is_task = true WHERE instance = '". $instanceID ."' AND object_type = '#". $set['bp_name'] ."' RETURNING 1;"
    );
    
    try {

        $statement->execute();
        $response = $statement->fetchAll()[0];
        echo 'UPDATED';
        
    } catch (Exception $e) {
        var_dump('ERROR: ', $e);
        die();
    }

    echo '<br /><br />';
    // var_dump('test');
    // die();

}

echo 'COMPLETE<br />';
die();

?>