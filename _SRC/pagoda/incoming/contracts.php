<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header('Access-Control-Allow-Origin: *');

if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
}

/*
require_once '../_objects.php';
require_once '../_pgObjectsMT.php';
require_once '../_communications.php';
require_once '../_cookiesPG.php';
require_once '../files/_fileApi.php';
require_once '../../../app/_app/_config.php';

$objects = new Objects($pdo);
$pgObjects = new pgObjects($pdo, $_REQUEST['pak']);
$comms = new Comm($pgObjects, $appConfig);
$cookies = new Cookies($pgObjects);
$files = new FileApi($pgObjects, '../../_files/_instances/'.$_REQUEST['pak']);

$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);

$contractId = $_REQUEST['cid'];

echo $contractId;
*/

// find the contact info object
/*
$contactInfo = $app->getObjectsWhere('contact_info', array('info'=>$contact['email']), 0, 0)[0];
$contact = $app->getObjectById('contacts', $contactInfo['object_id'], 0, 1);
*/
//var_dump($appConfig);
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
echo APP_ROOT.'../../../pagoda/app/_app/_app.php';
require_once(APP_ROOT.'../../../pagoda/app/_app/_app.php');	

?>

<!DOCTYPE html>

<html lang="en">

  <head>

    <title><?php echo $appConfig['systemName'] ?></title>
    
    <script>
		
		var appConfig = <?php echo json_encode($appConfig) ?>;

	</script>

    <?php require_once APP_ROOT.'../../../pagoda/_blocks/header.php'; ?>

	<style>

		.client-container {
			max-width: 100%;
		}
		
		td > div {
			padding-top: 15px;
			padding-bottom: 1px;
		}
		
		.limit-row-height {
			min-height: 325px;
			height: 325px;
			overflow-y: scroll;
		}

	</style>

  </head>

  <body>

    <?php require_once APP_ROOT.'../../../pagoda/_blocks/navigation.php'; ?>

	<br />

	<div class="container-fluid">
		      
		<div class="row row-offcanvas row-offcanvas-left">
		
			<!-- sidebar navigation -->
			<div class="col-sm-12 col-md-2 sidebar-offcanvas" id="sidebar" role="navigation"></div>
		
			<p class="visible-xs">
		
				<button type="button" class="btn btn-primary btn-xs" data-toggle="offcanvas"><i class="glyphicon glyphicon-chevron-left"></i></button>
		
			</p>
		
			<div class="col-sm-12 col-md-10 main"></div>
					          						          
		</div><!--/row-->
		
	</div>
	
	<div class="col-sm-12">
		
		<br />
		<div class="loading-display-container"></div>
<!-- 		<p class="text-center text-default"><small><a onclick="logoutStaff()">logout</a> / <a onclick="PWordRec.getChangePasswordModal(<?php echo $_COOKIE['uid'] ?>)">change password</a></small></p> -->
		
	</div>
		    
    <?php require_once APP_ROOT.'../../../pagoda/_blocks/modal.php'; ?>	
	
	<?php require_once APP_ROOT.'../../../pagoda/_blocks/js.php'; ?>

    <script>
	    	    		
		Factory.register('page', function(sb){
			
			var components = {};
			
			return {

				init: function(){
					
					if(sb.data.url.getPage()){
						
						var activePage = sb.data.url.getPage();
						
						sb.notify({
							type: 'create-sidebar-menu',
							data: activePage
						});
						
						sb.notify({
							type: 'app-change-page',
							data: {
								to: sb.data.url.getPage(),
								pageParams: sb.data.url.getParams()
							}
						});

					}else{
						
						var defaultPage = 'clients';
						
						sb.notify({
							type: 'create-sidebar-menu',
							data: defaultPage
						});
						
						sb.notify({
							type: 'app-change-page',
							data: {
								to: defaultPage,
								pageParams: {}
							}
						});

					}
										
					components.loaderDisplay = sb.createComponent('loading-display');
					components.loaderDisplay.notify({
						type: 'start-loading-display',
						data: {
							domObj: sb.dom.make('.loading-display-container')
						}
					});
					
/*
					components.navSearch = sb.createComponent('search');
					components.navSearch.notify({
						type:'show-search-box',
						data: {
							domObj: {
								selector: '.pagoda-search-form-container'
							},
							attachedResults: true,
							indicies: ['contacts', 'companies', 'staff'],
							resultsList: false
						}
					});
*/

				}

			}

		});

		Factory.startAll();

    </script>
    
  </body>

</html>
