<?php

/*
	error_reporting(E_ALL);
	ini_set('display_errors', '1');
*/

header('Access-Control-Allow-Origin: *');

if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
	newrelic_background_job();
	newrelic_ignore_apdex();
}

$_REQUEST['pagodaAPIKey'] = 'root';

if(!APP_ROOT){
	define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/../' );
}

require_once APP_ROOT.'/lib/EmailReplyParser/src/autoload.php';
require_once APP_ROOT.'/lib/_.php';
use EmailReplyParser\Parser\EmailParser;

if(!class_exists('MyApp')){
require_once APP_ROOT.'/_objects.php';
require_once APP_ROOT.'/_pgObjectsMTAdmin.php';
require_once APP_ROOT.'/_communications.php';
require_once APP_ROOT.'/_cookiesPG.php';
require_once APP_ROOT.'/files/_fileApi.php';
require_once APP_ROOT.'/stripe/init.php';
	require_once $_SERVER['DOCUMENT_ROOT'].'/app/_app/_config.php';
}

$objects = new Objects($pdo);
$pgObjects = new pgObjectsAdmin($pdo, $appConfig['instance']);
$comms = new Comm($pgObjects, $appConfig);
$cookies = new Cookies($pgObjects);
$files = new FileApi($pgObjects, '../_files/_instances/'.$_REQUEST['pagodaAPIKey'],$_REQUEST['pagodaAPIKey']);
$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);


if($_REQUEST['test'] == '1'){
	
	$incoming = new MandrillWebhooks($app);
	$incoming->processIncomingEmail(
		array(
			'ts' => 'testingTimestamp',
			'event' => 'inbound',
			'msg' => array(
				'subject' => 'Re: again from production [ZZY1PaCdl+SY58QKER0MUA==]',
				'to' => $app->emailFromAddress,
				'from_email' => '<EMAIL>',
				'text' => 'this is a test reply'
			)
		)
	);

}

if(empty($_POST) and $_REQUEST['webhook'] != 1){
		
	$process = new MandrillWebhooks($app);
	
	$process->processLog();

/*
	while($process->processLog() == true){

		$process->processLog();

	}
*/

}

if($_REQUEST['webhook'] == 1){

	$incoming = new MandrillWebhooks($app);

	$incoming->saveWebhook($_POST);

}

class MandrillWebhooks {

	public function __construct($app){

		$this->app = $app;

	}

	public function deleteWebhook($webhookId){
		
		if($this->app->pgObjects->delete('mandrill_webhooks', intval($webhookId))){
			
			return true;
			
		}
/*
		$query = "delete from mandrill_webhooks where id = $webhookId";

		if($this->app->db->query($query)){

			return true;

		}
*/

	}

	/**
	* Generates a base64-encoded signature for a Mandrill webhook request.
	* @param string $webhook_key the webhook's authentication key
	* @param string $url the webhook url
	* @param array $params the request's POST parameters
	*/

	public function generateSignature($webhook_key, $url, $params) {

	    $signed_data = $url;
	    ksort($params);

	    foreach ($params as $key => $value) {

	        $signed_data .= $key;
	        $signed_data .= $value;

	    }

	    return base64_encode(hash_hmac('sha1', $signed_data, $webhook_key, true));

	}

	public function getStringBetween($str, $from = '#', $to = ':'){

	    $sub = substr($str, strpos($str,$from)+strlen($from),strlen($str));

	    return substr($sub,0,strpos($sub,$to));

	}

	public function getWebhooks(){
		
		return $this->app->pgObjects->getAll('mandrill_webhooks');

/*
		$query = "select * from mandrill_webhooks order by date_created";

		if($data = $this->app->db->query($query)){

			while($datum = $data->fetch(PDO::FETCH_ASSOC)){

				$ret[] = $datum;

			}

			return $ret;

		}
*/
	}

	public function logClickEvent($eventData){

		

		// posts a note about the click event if it was from a known client

		

		// check if the from email address is a client

		//if($clientData = $this->app->clients->getWhere("where email = ".$eventData[''])){}

		

	}

	public function logInboundEmail($eventData){

		if($clientRaw = $this->app->pgObjects->where('clients', array('email' => $eventData['msg']['from_email']))){
			
			$clientData = $this->app->getClientObject($clientRaw['id'], 0, 1);
			
		}elseif($staffData = $this->app->pgObjects->where('staff', array('email' => $eventData['msg']['from_email']))){
			
			
			
		}

		// check if the email is from a client

		/*
if($clientRaw = $this->app->clients->getWhere("where email = ".$eventData['msg']['from_email'])){

			

			// get the full client object

			$clientData = $this->app->getClientObject($clientRaw['id'], 0, 1);

			

			

			

		}

		

		// check if the email is from a staff member

		if($staffData = $this->app->staff->getWhere("where email = ".$eventData['msg']['from_email'])){

			

			

			

		}
*/

	}

	public function processLog(){

		$webhooks = $this->getWebhooks();
		
		// TESTING
		/*
foreach($webhooks as $key => $val){
			$this->deleteWebhook($val['id']);
		}
*/

		echo "Processing ".count($webhooks)." webhook entries...<br /><br />";
// var_dump(/* $this->app->pgObjects->create('mandrill_webhooks', array('data' => array('test' => 'data', 'more' => 'data'))),  */$webhooks);
// die();
		foreach($webhooks as $webhook){

			echo "Found ".count( $webhook['data']) ." webhooks to process...<br />";

			$i=0;	
			while($i < count($webhook['data'])){

				// decode the json message
				$hookData = $webhook['data'];

				// switch over the webhook type
				switch($hookData[$i]['event']){

					case "click":

						echo "Processing click event...<br />";
						
						// post a note about the click event
						if($hookData[$i]['_id']){

							$emailData = $this->app->getSingleEmail($hookData[$i]['_id'], 0, 1);

							if($emailData){

								$this->app->comm->updateEmailStatus('clicked', 1, "where id = ".$emailData['id']);
								//$this->app->createNote('email', $emailData['id'], 10, 'Link in email was clicked: '.$hookData[$i]['url']);

							}

						}

						echo "Click event processed.<br />";
						break;

					case "deferral":

						echo "Processing deferral...<br />";

						// post a note about the event
						if($hookData[$i]['_id']){

							$emailData = $this->app->getSingleEmail($hookData[$i]['_id'], 0, 1);

							if($emailData){

								//$this->app->comm->updateEmailStatus('opened', 1, "where id = ".$emailData['id']);
								$this->app->createNote('email', $emailData['id'], 10, 'Email delivery was deferred.');

							}

						}

						echo "Deferral event processed.<br />";
						break;

					case "open":

						echo "Processing open event...<br />";

						// post a note about the event
						if($hookData[$i]['_id']){

							$emailData = $this->app->getSingleEmail($hookData[$i]['_id'], 0, 1);

							if($emailData){

								$this->app->comm->updateEmailStatus('read', 1, "where id = ".$emailData['id']);
								$this->app->createNote('email', $emailData['id'], 10, 'Email was opened.');

							}

						}

						echo "Open event processed.<br />";
						break;

					case "spam":

						echo "Processing spam...<br />";

						// post a note about the event
						if($hookData[$i]['_id']){

							$emailData = $this->app->getSingleEmail($hookData[$i]['_id'], 0, 1);

							if($emailData){

								//$this->app->comm->updateEmailStatus('opened', 1, "where id = ".$emailData['id']);
								$this->app->createNote('email', $emailData['id'], 10, 'Email was marked as spam by the receiver.');

							}

						}

						echo "Spam event processed.<br />";
						break;

					case "reject":

						echo "Processing reject...<br />";

						// post a note about the event
						if($hookData[$i]['_id']){

							$emailData = $this->app->getSingleEmail($hookData[$i]['_id'], 0, 1);

							if($emailData){

								//$this->app->comm->updateEmailStatus('opened', 1, "where id = ".$emailData['id']);
								$this->app->createNote('email', $emailData['id'], 10, 'Email was rejected by the receiving server.');

							}

						}

						echo "Reject event processed.<br />";
						break;

					case "hard_bounce":

						echo "Processing hard bounce event...<br />";

						// post a note about the event
						if($hookData[$i]['_id']){

							$emailData = $this->app->getSingleEmail($hookData[$i]['_id'], 0, 1);

							if($emailData){

								//$this->app->comm->updateEmailStatus('opened', 1, "where id = ".$emailData['id']);
								$this->app->createNote('email', $emailData['id'], 10, 'Email was bounced back.');

							}

						}

						echo "Hard bounce event processed.<br />";
						break;

					case "soft_bounce":

						echo "Processing soft bounce event...<br />";

						// post a note about the event
						if($hookData[$i]['_id']){

							$emailData = $this->app->getSingleEmail($hookData[$i]['_id'], 0, 1);

							if($emailData){

								//$this->app->comm->updateEmailStatus('opened', 1, "where id = ".$emailData['id']);
								$this->app->createNote('email', $emailData['id'], 10, 'Email was bounced back.');

							}

						}

						echo "Soft bounce event processed.<br />";
						break;

					case 'inbound':

						echo "Processing incoming email...<br />";
						
						// save the incoming email
						$this->processIncomingEmail($hookData[$i]);

						echo "Incoming email processed.<br />";

						break;							

					default:

						echo "Unprocessed webhook...<br />";	

				}

				$i++;

			}

			$this->deleteWebhook($webhook['id']);

			return true;

			echo "<br />";		

		}
//die();
		echo "<br />";

		sleep(2);

		return true;

	}

	public function processIncomingEmail($emailData){

		/*
$emailData = array(
			'ts' => 'testingTimestamp',
			'event' => 'inbound',
			'msg' => array(
				'subject' => 'Re: TESsting [lTxMkc2qkt08bI9PBkwMvg==]',
				'to' => $this->app->emailFromAddress,
				'from_email' => '<EMAIL>',
				'text' => 'this is a test reply'
			)
		);
*/
		
		$threadId = $this->parseThreadId($emailData['msg']['to'][0][0]);

		if($threadId){
		
			$thread = $this->app->pgObjects->getById('threads', intval($threadId));
// 			$instanceObj = $this->app->pgObjects->where('instances', ['instance' => $thread['instance']])[0];
			
// 			var_dump(explode('<div class="gmail_extra">', $emailData['msg']['html'])[0]);
// 			die();
			
			$parsedBody = $emailData['msg']['text'];
			
			$email = (new EmailParser())->parse($parsedBody);
			$message = $email->getFragments()[0];
			$message = $message->getContent();
			$parsedBody = preg_replace('~On(.*?)wrote:(.*?)$~si', '', $message);

			$this->app->pgObjects->changeInstance($thread['instance']);
			$this->app->pgObjects->create('emails', array(
				'type' => $thread['object_type'],
				'type_id' => $thread['object_id'],
				'mandrill_id' => $emailData['ts'],
				'to' => $emailData['msg']['to'][0][0],
				'from' => $emailData['msg']['from_email'],
				'subject' => $emailData['msg']['subject'],
				'message' => $parsedBody,
				'thread_id' => intval($threadId),
				'sent' => 1
			));
			$this->app->pgObjects->update('threads', array(
				'id' => intval($threadId),
				'replied_to' => 1,
				'not_viewed' => 1
			));
			/*
$this->app->comm->saveMandrillEmail(
				$thread['object_type'], // email type	
				$thread['object_id'], // type id
				$emailData['ts'], // email id (mandrill email or timestamp)
				$emailData['msg']['to'][0][0], // to
				$emailData['msg']['from_email'], // from
				$emailData['msg']['subject'], // subject
				$emailData['msg']['text'],
				$thread['id']
// 				substr($emailData['msg']['text'], 0, strpos( $emailData['msg']['text'], '- - - - -')) // email text	
			);
*/
			
			switch($thread['object_type']){
					
				case 'client':
				$link = 'https://staff.infinityhospitality.net/app/#ClientView?&clientId=2'.$thread['object_id'];
				break;
				
				case 'event':
				$link = 'https://staff.infinityhospitality.net/app/#EventView?&eventId='.$thread['object_id'];
				break;
				
				case 'staff':
				$link = 'https://staff.infinityhospitality.net/app/#StaffView?&staffId='.$thread['object_id'];
				break;
				
				default: 
				break;
				
			}

			$mergevars = array(
				'TITLE' => $emailData['msg']['subject'],
				'BODY' => substr($emailData['msg']['text'], 0, strpos( $emailData['msg']['text'], '- - - - -')),
				'BUTTON' => 'View Online',
				'BUTTON_LINK' => $link
			);
			
			foreach($thread['listeners'] as $listener){
				
				if(is_numeric($listener)){
					$listener = $this->app->pgObjects->getById('staff', intval($listener))['email'];
				}
				
				if($emailData['msg']['from_email'] != $listener){
					
					$this->app->comm->sendMandrillEmail(
						
						$listener,
						$this->app->emailFromAddress,
						$emailData['msg']['subject'],
						$mergevars,
						array('incoming-'.$thread['type'].'-email'),
						$thread['object_id'],
						0,
						$thread['object_type']
						
					);
					
				}
				
			}
			
		}else{
		// old way

return false;
			$from = $emailData['msg']['from_email'];
	
			// check if the from address is a client
			$isClient = $this->app->clients->getWhere("where email = '$from' ");
	
			if($isClient){
	
				// email is from a client			
				// post the email directly to the client and notify staff
				// get full client object
				$clientObject = $this->app->getClientObject($isClient[0]['id'], 0, 1)[0];
	
				$save['id'] = $emailData['ts'];
	
				$save['to'] = $emailData['msg']['to'][0][0];
	
				$save['from'] = $from;
	
				$save['text'] = $save['text'] = substr($emailData['msg']['text'], 0, strpos( $emailData['msg']['text'], '- - - - -'));
	
				$task['title'] = 'New email from '.$from;
	
				$task['dueDate'] = date("Y-m-d H:i:s");
				
				// check if the subject matches any current conversations
				$emailsToClient = $this->app->getEmailList('client', $isClient[0]['id'], 0);
				
				foreach($emailsToClient as $clientEmail){
					
					// remove the RE from the subject line and make the whole string lowercase
					$subject = strtolower($clientEmail['subject']);
					$cleanSubject = str_replace('re: ', '', $subject);
					
					if($cleanSubject == $emailData['msg']['subject']){
						
						$staffObj = $this->app->getStaffObjectBy('email', $clientEmail['from'], 0)[0];
						
						if($staffObj){
							
						}else{
							$staffObj = $this->app->getStaffObjectBy('email', $clientEmail['to'], 0)[0];
						}
						
						if(!$staffObj){
							return false;
						}
						
						// send the email to the staff member
						$save['type'] = 'client';
						$save['typeId'] = $clientObject['id'];	
						$save['subject'] = $emailData['msg']['subject'];
		
						$task['type'] = 'client';
						$task['id'] = $clientObject['id'];
						$task['notify'] = $staffObj['id'];
							
					}
					
				}
				
				if($task['id']){
					
					// save the email
					$this->app->comm->saveMandrillEmail(
						$save['type'], // email type	
						$save['typeId'], // type id
						$save['id'], // email id (mandrill email or timestamp)
						$save['to'], // to
						$save['from'], // from
						$save['subject'], // subject
						$save['text'] // email text	
					);
					
					$mergevars = array(
						'TITLE' => $save['subject'],
						'BODY' => $save['text'],
						'BUTTON' => 'View Online',
						'BUTTON_LINK' => 'https://staff.infinityhospitality.net/clientView?cid='.$save['typeId']
					);
					
					$this->app->comm->sendMandrillEmail(
						$staffObject['email'],
						$this->app->emailFromAddress,
						$save['subject'],
						$mergevars,
						array('incoming-'.$save['type'].'-email'),
						$save['typeId'],
						0
					);
									
				}else{
					
					// check for current events
					if(is_array($clientObject['events'])){
		
						foreach($clientObject['events'] as $event){
		
							$today = new DateTime();
							$startDate = new DateTime($event['event_start_date']);
							$endDate = new DateTime($event['event_end_date']);
		
							if($today < $startDate){
		
								// event hasn't started yet
								// send the email to the event specialist
								$save['type'] = 'event';
								$save['typeId'] = $event['id'];
								$save['subject'] = '#client-'.$clientObject['id'].': '.$emailData['msg']['subject'];
								
								$task['type'] = 'event';
								$task['id'] = $event['id'];
								$task['notify'] = $event['event_specialist']['id'];
		
								break;
		
							}
		
							if($today > $startDate){
		
								// event is in the past
								if($today < $endDate){
		
									// event is currently happening
									// send the email to the event specialist
									$save['type'] = 'event';
									$save['typeId'] = $event['id'];
									$save['subject'] = '#client-'.$clientObject['id'].': '.$emailData['msg']['subject'];
		
									$task['type'] = 'event';
									$task['id'] = $event['id'];
									$task['notify'] = $event['event_specialist']['id'];
		
									break;
		
								}else{
		
									// event has already happened
									// send the email to the sales specialist
									$save['type'] = 'client';
									$save['typeId'] = $clientObject['id'];
									$save['subject'] = '#client-'.$clientObject['id'].': '.$emailData['msg']['subject'];
		
									$task['type'] = 'client';
									$task['id'] = $clientObject['id'];
									$task['notify'] = $clientObject['sales_specialist']['id'];
		
								}
		
							}
		
						}
		
					}else{
		
						// send the email to the sales specialist
						$save['type'] = 'client';
						$save['typeId'] = $clientObject['id'];
						$save['subject'] = '#client-'.$clientObject['id'].': '.$emailData['msg']['subject'];
						
						$task['type'] = 'client';
						$task['id'] = $clientObject['id'];
						$task['notify'] = $clientObject['sales_specialist']['id'];
		
					}
		
					// save the email
					$this->app->comm->saveMandrillEmail(
						$save['type'], // email type
						$save['typeId'], // type id
						$save['id'], // email id (mandrill email or timestamp)
						$save['to'], // to
						$save['from'], // from
						$save['subject'], // subject
						$save['text'] // email text
					);
		
					
		
					// create a follow up task
					$this->app->createTask(
						$task['title'], // task title
						$task['dueDate'], // due date
						$task['id'], // object type id
						$task['type'], // object type
						10, // task author (10 = bot)
						$task['notify'] // notify a staff member (id)
					);
		
					// formward the email to the associated staff member			
					$staffObject = $this->app->getStaffObject($task['notify'], 0)[0];
		
					if($staffObject['fname'] != 'not selected'){
							
						switch($save['type']){
							
							case 'client':
							
								$buttonLink = 'clientView?cid='.$save['typeId'];
							
								break;
								
							case 'event':
							
								$buttonLink = 'eventView?eid='.$save['typeId'];
							
								break;
								
							default:
							
								$buttonLink = '';
							
						}
						
						$mergevars = array(
							'TITLE' => $save['subject'],
							'BODY' => $save['text'],
							'BUTTON' => 'View Online',
							'BUTTON_LINK' => 'http://staff.infinityhospitality.net/'.$buttonLink
						);
						
						$this->app->comm->sendMandrillEmail(
							$staffObject['email'],
							$this->app->emailFromAddress,
							$save['subject'],
							$mergevars,
							array('incoming-'.$save['type'].'-email'),
							$save['typeId'],
							0
						);
		
					}
					
				}
	
			}else{
	
				// email is not from a client
				// check if the email is from a staff member
				$isStaff = $this->app->staff->getWhere("where email = '$from'");
	
				if($isStaff){
	
					
	
					// email is from a staff member
	
					// get the object type and id from the subject line
	
					if (($pos = strpos($emailData['msg']['subject'], "-")) !== FALSE) { 
	
						$parsedSubject = $this->getStringBetween($emailData['msg']['subject']);
	
						$emailType = strtok($parsedSubject, '-');
	
					    $emailTypeId = substr($parsedSubject, strpos($parsedSubject, "-") + 1);
	
					}
	
					//$emailTypeId = 0;
	
					if($emailTypeId > 0){
	
						
	
						// the subject line was parsed correctly
	
						$staffObject = $this->app->getStaffObject($isStaff[0]['id'], 0)[0];
	
						
	
						// get the full object
						$objectData = $this->app->getClientObject($emailTypeId, 0, 0)[0];
	
						$to = $objectData['email'];
						
						$save['id'] = $emailData['ts'];
						$save['to'] = $emailData['msg']['to'][0][0];
						$save['from'] = $from;
						$save['subject'] = str_replace('Re: #client-'.$objectData['id'].': ', '', $emailData['msg']['subject']);
						$save['text'] = substr($emailData['msg']['text'], 0, strpos( $emailData['msg']['text'], '- - - - -'));
						$save['type'] = $emailType;
						$save['typeId'] = $emailTypeId;
											
						switch($save['type']){
							
							case 'client':
							
								$buttonLink = 'clientView?cid='.$save['typeId'];
							
								break;
								
							case 'event':
							
								$buttonLink = 'eventView?eid='.$save['typeId'];
							
								break;
								
							default:
							
								$buttonLink = '';
							
						}
						
						$mergevars = array(
							'TITLE' => '',
							'BODY' => $save['text'],
							'BUTTON' => 'View Online',
							'BUTTON_LINK' => 'http://staff.infinityhospitality.net/'.$buttonLink
						);
						
						$this->app->comm->sendMandrillEmail(
							$to,
							$this->app->emailFromAddress,
							$save['subject'],
							$mergevars,
							array('outgoing-'.$save['type'].'-email'),
							$save['typeId'],
							1
						);
	
						
	
					}else{
	
						
	
						// the subject line was not parsed correctly
	
						
	
					}
	
					
	
				}
	
				
	
			}
			
		}
		
		return true;

	}

	public function saveWebhook($post){

		if($post['mandrill_events']){

			$json = json_decode($post['mandrill_events'], true);
			
			if($insert = $this->app->pgObjects->create('mandrill_webhooks', array('data' => $json))){
				
				return true;
				
			}			

		}

	}
	
	private function parseThreadId($messageSubject){
		
		$threadHash = explode('#', explode('@', $messageSubject)[0])[1];
		$threadId = openssl_decrypt($threadHash, 'aes-256-cbc', 'email-thread-id');
		
		if($threadId){
			
			return $threadId;
			
		}else{
			
			return false;
			
		}		
		
	}

}

?>