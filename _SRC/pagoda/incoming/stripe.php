<?php

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
define( "APP_ROOT", $_SERVER["DOCUMENT_ROOT"] . '/api/' );
require_once '../custom_scripts/bento.php';
require '../vendor/autoload.php';// Get required files
require APP_ROOT.'DBPATH.php';
require_once APP_ROOT.'lib/_.php';
require_once APP_ROOT.'vendor/autoload.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'_excel.php';
include_once APP_ROOT.'_app.php';

// Add error logging for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$stripeSecretKey = getenv('STRIPE_SK');

\Stripe\Stripe::setApiKey($stripeSecretKey);

$payload = @file_get_contents('php://input');
$event = null;

// Add payload validation and logging
if (!$payload) {
    error_log('Error: No payload received from Stripe');
    http_response_code(400);
    exit();
}

error_log('Stripe Webhook Raw Payload: ' . $payload);

// Start database connection
$instanceName = "voltzsoftware";
$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");

// Get page objects
$pgObjects = new pgObjects($pdo, $instanceName);

// Get comm objects
$comms = new Comm($pgObjects, []);

try {
  	$event = \Stripe\Event::constructFrom(
    	json_decode($payload, true)
  	);
} catch(\UnexpectedValueException $e) {
  	// Invalid payload
    error_log('Stripe webhook error: ' . $e->getMessage());
  	http_response_code(400);
  	exit();
}

// Handle the event
switch ($event->type) {
	case 'payment_intent.created':
    	$paymentIntent = $event->data->object;
		UpdateBentoPaymentObject($pgObjects, $comms, $paymentIntent, true, false);
    break;

	case 'payment_intent.processing':
		$paymentIntent = $event->data->object;
		UpdateBentoPaymentObject($pgObjects, $comms, $paymentIntent);
    	break;

  	case 'payment_intent.payment_failed':
		$paymentIntent = $event->data->object;
		UpdateBentoPaymentObject($pgObjects, $comms, $paymentIntent);
    	break;

  	case 'payment_intent.requires_action':
		$paymentIntent = $event->data->object;
		UpdateBentoPaymentObject($pgObjects, $comms, $paymentIntent);
    	break;

  	case 'payment_intent.succeeded':
		$paymentIntent = $event->data->object;
		UpdateBentoPaymentObject($pgObjects, $comms, $paymentIntent, false, true);
    	break;

	case 'payment_intent.canceled':
		$paymentIntent = $event->data->object;
		UpdateBentoPaymentObject($pgObjects, $comms, $paymentIntent);
		break;

	case 'payment_intent.amount_capturable_updated':
		$paymentIntent = $event->data->object;
		UpdateBentoPaymentObject($pgObjects, $comms, $paymentIntent);
		break;

  	default:
		// Unexpected event type
    error_log('Unhandled event type: ' . $event->type);
		break;
}

function UpdateBentoPaymentObject($pgObjects, $comms, $paymentIntent, $notifyManager = false, $receipt = false) {

	$bentoPaymentObj = $pgObjects->whereAcrossInstances(
		'payments', ['stripeId' => $paymentIntent['id']]
		, '', 0, false, 0, 'null', 'asc', 1, null, [], 'string')[0];

	$pgObjects->setInstance($bentoPaymentObj['instance']);

	$details['stripePI'] = $paymentIntent;

	$bentoPaymentObj = $pgObjects->update('payments', [
		'id' => $bentoPaymentObj['id'],
		'status' => $paymentIntent['status'],
		'details' => $details
	]);

    $taggedWith = array();
    $instance = $pgObjects->getAll('instances', 1);
    $taggedWith = array_merge($bentoPaymentObj["tagged_with"], [$instance[0]['id']]);


	if ($notifyManager){

		$proposal = $pgObjects->getById('proposals', $bentoPaymentObj["main_object"]);
		$group = $pgObjects->getById('groups', $proposal["main_object"], 1);
		$groupId = $group["id"];
		$groupObjectUID = $group["object_uid"];
		$startDate = empty(trim($group["start_date"])) ? "No Set Start Date" : trim($group["start_date"]);
		$instanceName = $instance[0]["instance"];
		$managersArray = $group["managers"];
		$managerEmails = [];

        // infinity specific notifications:
        if ($instanceName == 'infinity' || $instanceName == 'nlp' ) {

            // array_push($managerEmails, "<EMAIL>");

            // if (array_key_exists('test_payment', $bentoPaymentObj) && $bentoPaymentObj["test_payment"] == true) {

            //     array_push($managerEmails, "<EMAIL>");

            // } else {
            //     // array_push($managerEmails, "<EMAIL>");
            //     // array_push($managerEmails, );
            // }

        }

        if ( $instanceName == 'dreamcatering') {

            // array_push($managerEmails, "<EMAIL>");

            // if (array_key_exists('test_payment', $bentoPaymentObj) && $bentoPaymentObj["test_payment"] == true) {

            //     array_push($managerEmails, "<EMAIL>");

            // } else {
            // }

        }

		if($managersArray != null) {
            foreach ($managersArray as $manager) {
                array_push($managerEmails, $manager["email"]);
            }
        }

		$projectName = trim($group["name"]);
		$proposalId = $proposal["id"];
		$date = date("Y/m/d");
		$paymentAmount = number_format(floatVal($bentoPaymentObj["amount"])/100, 2);
		$paymentFees = number_format(floatVal($bentoPaymentObj["fee"])/100, 2);
		$totalPayment = number_format(floatVal($bentoPaymentObj["fee"])/100 + floatVal($bentoPaymentObj["amount"])/100, 2);
		$instanceReplyEmail = $instance[0]["emailFrom"];
		$urlText = "bento.infinityhospitality.net/app/". $instanceName . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $groupId . "-" . urlencode($projectName);

		$mergevars = (object) array(
			'TITLE' => '' ,
			'SUBJECT' => 'A new payment has been posted to "'. $projectName .'"',
			'BODY' => "" . $date . "<br/><br/>
			Project Name: " . $projectName . "<br/><br/>
			Event Id: " . $groupObjectUID . "<br/><br/>
			Start Date: " . $startDate . "<br/><br/>
			Payment Amount: $" . $paymentAmount . "<br/><br/>
			Payment Fees: $" . $paymentFees . "<br/><br/>
			Payment Total: $" . $totalPayment . "<br/><br/>
			Project Link: <a href='" . $urlText . "'>" . $urlText . "</a><br/><br/>
			Stripe Payment Id: ". $bentoPaymentObj['stripeId'],
			'INSTANCE_NAME' => $instanceName
		);


		$managerOutbound = $comms->sendMandrillEmail(
			$managerEmails,
			$instanceReplyEmail,
			'A payment has been posted to "'. $projectName .'"',
			$mergevars,
			[],
			null,
			0,
			0,
			false,
			null,
			$bentoPaymentObj['instance'],
			0,
			$taggedWith
		);

	}

	if ($receipt){

		$paymentCreatorId = $bentoPaymentObj["created_by"];
		$paymentContact = $pgObjects->getById('contacts', $paymentCreatorId);
		$contactCompany = $pgObjects->getById('companies', $paymentContact["company"]);
		$contactInfo = $pgObjects->getById('contact_info', $paymentContact["contact_info"][0]);
		$payment = $pgObjects->getById('payments', $bentoPaymentObj["id"]);
		$proposal = $pgObjects->getById('proposals', $bentoPaymentObj["main_object"]);
		$group = $pgObjects->getById('groups', $proposal["main_object"], 1);
		$proposalGroup = $pgObjects->getById('', $proposal['main_object'], 0);
		$mainContact = $proposalGroup["main_contact"];
		$mainContactInfoArray = $pgObjects->where('contact_info', array(
			'object_id' => $mainContact
			, 'is_primary' => "yes"
		), '', 1);
		$mainContactEmail = null;
		foreach ($mainContactInfoArray as $info) {

			if ($info['type']['data_type'] === 'email') {

				$mainContactEmail = $info['info'];

			}

		}
		$stripePIId = $bentoPaymentObj["details"]["stripePI"]["id"];
		$stripeEventId = $paymentIntent["id"];
		$instanceObj = $pgObjects->getAll('instances', 1);
		$instanceInfo = $pgObjects->getAll('invoice_system', 1);

		$bentoPaymentReceipt = $pgObjects->create('payment_receipt', array(
			'payment_creator_id' => $paymentCreatorId,
			'payment_contact' => $paymentContact,
			'contact_company' => $contactCompany,
			'contact_info' => $contactInfo,
			'payment' => $payment,
			'proposal_id' =>$proposal["id"],
			'proposal' => $proposal,
			'stripe_payment_id' => $stripePIId,
			'stripe_event_id' => $stripeEventId,
			'instance_obj' => $instanceObj,
			'instance_info' => $instanceInfo,
		));

		// email variables:
		$emailAddresses = [$bentoPaymentReceipt["payment_contact"]["email"]];
		if ($bentoPaymentReceipt["payment_contact"]["email"] != $mainContactEmail) {
			array_push($emailAddresses,$mainContactEmail);
		}
		// optional 'one-off' receipt recipient
		if (!in_array($bentoPaymentObj["optional_receipt_email"], $emailAddresses)) {
			array_push($emailAddresses, $bentoPaymentObj["optional_receipt_email"]);
		}
		$receiptId = $bentoPaymentReceipt["id"];
		// Instance Info
		$instanceName = $bentoPaymentReceipt["instance_obj"][0]["systemName"];
		$instanceReplyEmail = $bentoPaymentReceipt["instance_obj"][0]["emailFrom"];
		// "instance" address:
		$instanceStreet = $bentoPaymentReceipt["instance_info"][0]["billing_address"]["street"];
		$instanceStreet2 = $bentoPaymentReceipt["instance_info"][0]["billing_address"]["street2"];
		$instanceCity = $bentoPaymentReceipt["instance_info"][0]["billing_address"]["city"];
		$instanceState = $bentoPaymentReceipt["instance_info"][0]["billing_address"]["state"];
		$instanceZip = $bentoPaymentReceipt["instance_info"][0]["billing_address"]["zip"];
		$instanceAddressHTML = "" . $instanceName . "<br/>
			" . $instanceStreet ."<br/>
			" . ($instanceStreet2 != "" ? "" . $instanceStreet2 . "<br/>" : "") . "
			" . $instanceCity . ", " . $instanceState. " " . $instanceZip . "";
		// Company/Client Name/Address
		$contactName = $bentoPaymentReceipt["payment_contact"]["name"];
		$hasAddress = false;
		$contactStreet1 = $bentoPaymentReceipt["contact_info"]["street"];
		$contactStreet2 = $bentoPaymentReceipt["contact_info"]["street2"];
		$contactCity = $bentoPaymentReceipt["contact_info"]["city"];
		$contactState = $bentoPaymentReceipt["contact_info"]["state"];
		$contactZip = $bentoPaymentReceipt["contact_info"]["zip"];
		$addressHtml = '';
		if ($paymentContact["object_bp_type"] == "contacts"){
			$hasAddress = true;
			$addressHtml = "" . $contactName . "<br/>
				" . $contactStreet1 ."<br/>
				" . ($contactStreet2 != "" ? "" . $instanceStreet2 . "<br/>" : "") . "
				" . $contactCity . ", " . $contactState. " " . $contactZip . "";
		}
		// Date
		$date = date("Y/m/d");
		// Proposal Name/Number
		$proposalId = $bentoPaymentReceipt["proposal"]["id"];
		$projectName = trim($group["name"]);
		// Payment Method
		$paymentObj = $bentoPaymentReceipt["payment"]["details"]["stripePI"]["charges"]["data"][0]["source"]["object"];
		$paymentBrand = $bentoPaymentReceipt["payment"]["details"]["stripePI"]["charges"]["data"][0]["source"]["brand"];
		$paymentLast4 = $bentoPaymentReceipt["payment"]["details"]["stripePI"]["charges"]["data"][0]["source"]["last4"];
		// Amount Paid
		$paymentAmount = number_format(floatVal($bentoPaymentReceipt["payment"]["amount"])/100, 2);
		$paymentFees = number_format(floatVal($bentoPaymentReceipt["payment"]["fee"])/100, 2);
		$totalPayment = number_format(floatVal($bentoPaymentReceipt["payment"]["fee"])/100 + floatVal($bentoPaymentReceipt["payment"]["amount"])/100, 2);
		// Date Paid
		$paymentDate = date_format(date_create($bentoPaymentReceipt["payment"]["date_created"]), "Y/m/d");

		$mergevars = (object) array(
			'TITLE' => '' ,
			'SUBJECT' => 'Receipt for ' . $projectName . ' Payment',
			'BODY' => "" . $date . "<br/><br/>
			" . ($hasAddress ? $addressHtml . "<br/><br/>" : "") . "
			This email is the receipt for your payment related to the (Proposal Id: ". $proposalId . ") " . $projectName . " event invoice.<br/></br>
			Receipt Id: " . $receiptId . "<br/>
			Payment Method: " . $paymentBrand . " " . $paymentObj . " ending in " . $paymentLast4 . "<br/>
			Payment Date: " . $paymentDate . "<br/>
			Payment Amount: $" . $paymentAmount . "<br/>
			Payment Fees: $" . $paymentFees . "<br/>
			Payment Total: $" . $totalPayment . "<br/>
			If you have any questions please reach out to your invoice provider.<br/><br/>",
			'INSTANCE_NAME' => 'Bento Systems'
		);

		$receiptOutbound = $comms->sendMandrillEmail(
			$emailAddresses,
			$instanceReplyEmail,
			'Receipt for ' . $projectName . ' Payment',
			$mergevars,
			[],
			null,
			0,
			0,
			false,
			null,
			$bentoPaymentObj['instance'],
			0,
			$taggedWith
		);

	}

    ///SEND LOGGING EMAIL [TEST ONLY]
    /// ob_start() - This function will turn output buffering on. While output buffering is active no output is sent from the script (other than headers)
    ///, instead the output is stored in an internal buffer.
    /// ob_get_contents() - The contents of this internal buffer may be copied into a string variable using ob_get_contents().
    /// ob_end_flush() - To output what is stored in the internal buffer, use ob_end_flush().


    // ob_start();

    // function test ($var) {
    //     echo " " . $var . "<br>";
    // }

    // test("start :>");
    // echo "<pre> INCOMING REQUEST OBJ « \n ", var_dump( json_encode($request , JSON_PRETTY_PRINT) ), "</pre>\n";
    // test("<: end");

    // $content = ob_get_clean();

    // ob_end_flush();

    // $loggingEmail = (object) array(
    //     'TITLE' => '' ,
    //     'SUBJECT' => 'LOG',
    //     'INSTANCE_NAME' => 'Bento Systems',
    //     'BODY' => $content
    // );

    // $this->sb->sendEmail(
    //     '<EMAIL>'
    //     , $instanceReplyEmail
    //     , 'STRIPE LOGGING DETAILS'
    //     , $loggingEmail
    //     , null
    //     , 0
    // );
}

http_response_code(200);
