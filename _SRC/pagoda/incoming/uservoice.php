<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header('Access-Control-Allow-Origin: *');

if (extension_loaded('newrelic')) {
	newrelic_set_appname("Pagoda");
	newrelic_background_job();
	newrelic_ignore_apdex();
}

require_once '../lib/_.php';

require_once '../_objects.php';
require_once '../_pgObjectsMT.php';
require_once '../_communications.php';
require_once '../_cookiesPG.php';
require_once '../files/_fileApi.php';

require_once '../_config.php';

$objects = new Objects($pdo);
$pgObjects = new pgObjects($pdo, $_REQUEST['pagodaAPIKey']);
$comms = new Comm($pgObjects, $appConfig);
$cookies = new Cookies($pgObjects);
$files = new FileApi($pgObjects, '../../_files/_instances/' . $_REQUEST['pagodaAPIKey']);

$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);

$ticket_info = json_decode($_POST['data'], true);

$contact = $ticket_info['ticket']['contact'];

// find the contact info object
$contactInfo = $app->getObjectsWhere('contact_info', array('info' => $contact['email']), 0, 0)[0];

if ($contactInfo) {

	//get the contact
	$contact = $app->getObjectById('contacts', $contactInfo['object_id'], 0, 1);

	if ($contact) {

		// the message is related to a contact in the system
		// determine the type of message
		if ($ticket_info['message']) {

			// staff reply
			$staff = $app->getObjectsWhere('users', array('email' => $ticket_info['message']['sender']['email']), 0, 0)[0];

			$author = array(
				'id' => 0
			);

			if ($staff) {
				$author = $staff;
			}

			$noteSetup = array(
				'note' => 'REPLY FROM USERVOICE<br /><br />SUBJECT: ' . $ticket_info['ticket']['subject'] . '<br /><br />BODY:<br /><br />' . $ticket_info['message']['body'] . '<br /><br />View in Uservoice: ' . $ticket_info['ticket']['url'],
				'type' => 'contacts',
				'author' => $author['id'],
				'created_by' => $author['id'],
				'type_id' => $contact['id'],
				'note_type' => 883665,
				'page_params' => '#contact?&contactId=' . $contact['id']
			);

			$note = $app->createNewObject('notes', $noteSetup, 0, 0);
		} else {

			if ($ticket_info['ticket']['messages']) {

				// new message from known contact
				$staff = $app->getObjectById('users', $contact['manager'], 0, 1);

				$noteSetup = array(
					'note' => 'NEW MESSAGE FROM USERVOICE<br /><br />SUBJECT: ' . $ticket_info['ticket']['subject'] . '<br /><br />BODY:<br /><br />' . $ticket_info['ticket']['messages'][0]['body'] . '<br /><br />View in Uservoice: ' . $ticket_info['ticket']['url'],
					'type' => 'contacts',
					'author' => 0,
					'note_type' => 883665,
					'type_id' => $contact['id'],
					'page_params' => '#contact?&contactId=' . $contact['id']
				);

				$note = $app->createNewObject('notes', $noteSetup, 0, 0);

				$to = array('<EMAIL>', '<EMAIL>', $contact['manager']['email']);
				//$to = '<EMAIL>';

				if ($staff) {
					$to[] = $staff['email'];
				}

				$mergevars = array(
					'TITLE' => 'USERVOICE TICKET: ' . $contact['fname'] . ' ' . $contact['lname'] . ' - ' . $ticket_info['ticket']['subject'],
					'BODY' => 'NEW USERVOICE MESSAGE FROM ' . $contact['fname'] . ' ' . $contact['lname'] . '<br /><br />SUBJECT: ' . $ticket_info['ticket']['subject'] . '<br /><br />BODY:<br /><br />' . $ticket_info['ticket']['messages'][0]['body'] . '<br /><br />View in Uservoice: ' . $ticket_info['ticket']['url'] . '<br /><br />View in Pagoda: https://pagoda.voltz.software/app/' . $appConfig['instance'] . '/#contact?&contactId=' . $contact['id'],
					'INSTANCE_NAME' => $appConfig['instance'],
					'BUTTON' => 'View In Pagoda',
					'BUTTON_LINK' => 'https://pagoda.voltz.software/app/' . $appConfig['instance'] . '/#contact?&contactId=' . $contact['id']
				);

				$email = $app->sendEmail($to, $appConfig['emailFrom'], $ticket_info['ticket']['subject'], $mergevars, array('Uservoice Ticket'));
			} else {

				if ($ticket_info['note']) {

					$staff = $app->getObjectsWhere('users', array('email' => $ticket_info['note']['creator']['email']), 0, 0)[0];

					$author = array(
						'id' => 0
					);

					if ($staff) {
						$author = $staff;
					}

					// note posted by staff
					$noteSetup = array(
						'note' => 'NOTE POSTED FROM USERVOICE<br /><br />NOTE:<br /><br />' . $ticket_info['note']['body'] . '<br /><br />View in Uservoice: ' . $ticket_info['ticket']['url'],
						'type' => 'contacts',
						'note_type' => 883665,
						'author' => $author['id'],
						'created_by' => $author['id'],
						'type_id' => $contact['id'],
						'page_params' => '#contact?&contactId=' . $contact['id']
					);

					$note = $app->createNewObject('notes', $noteSetup, 0, 0);
				}
			}
		}
	}
}
