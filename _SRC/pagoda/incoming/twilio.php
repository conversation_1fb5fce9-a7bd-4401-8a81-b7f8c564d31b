<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header('Access-Control-Allow-Origin: *');

if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
	newrelic_background_job();
	newrelic_ignore_apdex();
}

require '../vendor/autoload.php';
use Twilio\Twiml;

require_once '../lib/_.php';

require_once '../_objects.php';
require_once '../_pgObjectsMT.php';
require_once '../_communications.php';
require_once '../_cookiesPG.php';
require_once '../files/_fileApi.php';

require_once '../../../../../../app/_app/_config.php';

$objects = new Objects($pdo);
$pgObjects = new pgObjects($pdo, $_REQUEST['pagodaAPIKey']);
$comms = new Comm($pgObjects, $appConfig);
$cookies = new Cookies($pgObjects);
$files = new FileApi($pgObjects, '../../_files/_instances/'.$_REQUEST['pagodaAPIKey']);

$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);

$fromNumber = substr($_POST['From'], 2);
$message = $_POST['Body'];

$contactInfo = $app->getObjectsWhere('contact_info', array('info'=>$fromNumber), 0, 0);

$contact = $app->getObjectById('contacts', $contactInfo[0]['object_id'], 0);

$note = array(
	'type_id'=>$contact['id'],
	'type'=>'contacts',
	'note'=>'<small>Gideon Thank You:</small><br /><i>'.$message.'</i>',
	'note_type'=>1218147,
	'author'=>0,
	'notifyUsers'=>array()
);

$app->createNewObject('notes', $note, 0, 0);

$response = new Twiml;
$response->message('Got it! We have received your thanks to The Gideons.');
print $response;

?>