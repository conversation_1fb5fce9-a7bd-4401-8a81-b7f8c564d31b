<?php

//date_default_timezone_set('America/Chicago');

class Cookies {

	private $db;

	function __construct($objectsAPI){

		$this->pgObjects = $objectsAPI;

	}

	public function checkCookie($userId, $type, $series, $token) {
		
		$queryObj = array(
			'uid' => intval($userId),
			'series' => $series,
			'token' => $token
		);
	
		if (!empty($dbToken = $this->pgObjects->where('cookies', $queryObj, 'LIMIT 1')[0])) {

			if ($dbToken['token'] == $token) {
				
				return $dbToken;
				
			} else {

				// cookie theft has occured, delete all user tokens and notify the user
				return -1;

			}

		} else {
			
			return false;

		}

	}

	public function checkToken($token) {
		
		if (empty($token)) {
			return false;
		}

		$queryObj = array(
			'token' => $token
		);

		if (!empty($dbToken = $this->pgObjects->where('cookies', $queryObj)[0])) {

			return $dbToken;

		} else {

			return false;

		}

	}

	public function createCookie($userId = 1, $type, $ipAddress, $fingerprint, $platform, $sendCookie = 1){

		$staff = $this->pgObjects->getById($type, $userId);
		$device = $this->checkDevice($userId, $fingerprint);

		if ($device) {
			
			$ret = $device;
			$ret['uid'] = $userId;
			$ret['instance'] = $staff['instance'];
			$ret['user_type'] = $staff['type'];
						
		} else {

			// create the keys
			$series = $this->getHash();
			$token = $this->getHash();

			// expiration time
			$expTime = strtotime( '+30 days' );
			
			if ($type === 'portal_access_token') {

				$staff = $this->pgObjects->whereAll(
					'portal_access_token'
					, [
						'id' => $userId
					]
				)[0];
				$this->pgObjects->setInstance($staff['instance']);
				
			}
			
			$queryObj = array(
				'uid' => $userId,
				'series' => $series,
				'token' => $token,
				'fingerprint' => $fingerprint,
				'platform' => $platform,
				'ip_address' => $ipAddress,
				'instance' => $staff['instance']
			);
			
			$ret = $this->pgObjects->create('cookies', $queryObj);
			
			$ret['uid'] = $userId;
			$ret['instance'] = $staff['instance'];
			$ret['user_type'] = $staff['type'];
			
		}
		
		if ($sendCookie == 1) {
			
			$some_name = session_name("pagoda");
			//session_set_cookie_params(0, '/');			
			session_start();
			
			// send the cookies
			if ($type === 'portal_access_token') {
				
				setcookie('p_uid', $staff['contact'], false, "/", false);
				setcookie('p_user_type', $ret['type'], false, "/", false);
				setcookie('p_series', $ret['series'], false, "/", false);
				setcookie('p_token', $ret['token'], false, "/", false);
				setcookie('p_instance', $ret['instance'], false, "/", false);
				
				// Get client/company for the contact and send that back too
				$ret['company'] = $staff['company'];
				
			} else {
				
				setcookie('uid', $ret['uid'], false, "/", false);
				setcookie('user_type', $ret['type'], false, "/", false);
				setcookie('series', $ret['series'], false, "/", false);
				setcookie('token', $ret['token'], false, "/", false);
				setcookie('instance', $ret['instance'], false, "/", false);
				
			}

			$_SESSION['uid'] = $userId;
			$_COOKIE['uid'] = $userId;
			
			$this->pgObjects->instance = $staff['instance'];
					
		}
						
		return $ret;

	}

	public function destroyCookie($userId, $type = null, $series = null){
		
		if($series == null){
			
			$seriesData = $this->pgObjects->where('cookies', array(
				'uid' => intval($userId),
				'user_type' => $type
			))[0];
			
			// delete all user tokens
			$this->pgObjects->delete('cookies', $seriesData['id']);

			setcookie('uid', '', time() - 3600, "/", false);
			setcookie('user_type', '', time() - 3600, "/", false);
			setcookie('series', '', time() - 3600, "/", false);
			setcookie('token', '', time() - 3600, "/", false);
			setcookie('instance', '', time() - 3600, "/", false);

			return true;

		}else{
			
			$seriesData = $this->pgObjects->where('cookies', array(
				'uid' => intval($userId),
				'series' => $series
			))[0];
			
			$this->pgObjects->delete('cookies', $seriesData['id']);

			setcookie('uid', '', time() - 3600, "/", false);
			setcookie('user_type', '', time() - 3600, "/", false);
			setcookie('series', '', time() - 3600, "/", false);
			setcookie('token', '', time() - 3600, "/", false);
			setcookie('instance', '', time() - 3600, "/", false);

			return true;

		}

	}

	private function checkDevice($userId, $deviceId){
		
		$deviceQuery = array(
			'fingerprint' => intval($userId),
			'series' => $series,
			'token' => $token
		);
		
		if($device = $this->pgObjects->where('cookies', $deviceQuery, 'LIMIT 1')[0]){
			
			return $device;
			
		}else{
			
			return false;
			
		}
		
	}
	
	private function getHash(){

		$random_key = hash('sha512', uniqid(mt_rand(1, mt_getrandmax()), true));

		return $random_key;

	}

}

?>