<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
date_default_timezone_set('America/Chicago');

require 'vendor/autoload.php';
require 'DBPATH.php';
require 'BUILD_VERSION.php';

require_once APP_ROOT.'/lib/_.php';
require_once APP_ROOT.'_objects.php';
require_once APP_ROOT.'_pgObjectsMT.php';
require_once APP_ROOT.'rules/rules.php';
require_once APP_ROOT.'_communications.php';
require_once APP_ROOT.'_cookiesPG.php';
require_once APP_ROOT.'files/_fileApi.php';

if(!empty($_REQUEST['do'])){

	$instanceID = $_REQUEST['pagodaAPIKey'];
	
	switch($instanceID){
		
		case '_staging':
		case 'petermikhail':
		case 'joshgantt':
		case 'johnwhittenburg':
		case 'rickyvoltz':
		case 'zachvoltz':
	
			$apiFolder = 'notify/pagoda';	
			$instanceURL = $instanceID;
	
			break;
						
		default:
			
			$apiFolder = 'notify/pagoda';
			$instanceURL = '_production';
		
	}
	
	if(!class_exists('App')){
		if(!defined('APP_ROOT')){
			define( "APP_ROOT", realpath( dirname( __FILE__ ) ).'/' );
		}
	
		require_once '_app.php';
	
	}
	
	$instanceName = str_replace('/', '', str_replace('/app/', '', $_REQUEST['pagodaAPIKey']));
		
	if(!class_exists('App') or !class_exists('pgObjects')){
		require_once '_pgObjectsMT.php';
	}
	
	if(!class_exists('App') or !class_exists('pgObjectsAdmin')){
		require_once '_pgObjectsMT.php';
	}
	
	$authHeader = getallheaders()['bento-token'];
	
	$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");

	$pgObjects = new pgObjects($pdo, $instanceName, $rules, $write, $writeDocsDB);
	$pgObjectsAdmin = new pgObjects($pdo, $instanceName, $rules, $write, $writeDocsDB);
	
	$instance = $pgObjectsAdmin->where('instances', array('instance'=>$instanceName))[0];
	
	$appConfig = array();
	
	if(is_array($instance)){
		foreach($instance as $k => $v){
			
			switch($k){
				
				case 'components':
				case 'pageModules':
				case 'settingsModules':
				
					$appConfig[$k] = explode(',', $v);
				
					break;
				
				case 'db_post':
				
					$appConfig['db']['post'] = $v;
				
					break;
					
				case 'db_read':
				
					$appConfig['db']['read'] = $v;
				
					break;
					
				case 'db_write':
				
					$appConfig['db']['write'] = $v;
				
					break;
					
				case 'files_bucket':
				
					$appConfig['files']['bucket'] = $v;
				
					break;	
					
				case 'files_delete':
				
					$appConfig['files']['delete'] = $v;
				
					break;
					
				case 'files_read':
				
					$appConfig['files']['read'] = $v;
				
					break;
					
				case 'files_write':
				
					$appConfig['files']['write'] = $v;
				
					break;
					
				case 'twilio_sid':
				
					$appConfig['twilio']['sid'] = $v;	
				
					break;
					
				case 'twilio_token':
				
					$appConfig['twilio']['token'] = $v;
					
					break;	
					
				case 'sms_from':
				
					$appConfig['twilio']['smsFrom'] = $v;
					
					break;						
				
				default:
				
					$appConfig[$k] = $v;
				
			}
		
		}
	}
	
	$rules = new Rules($appConfig);
	$pgObjects = new pgObjects($pdo, $instanceName, $rules);
	
	class MyApp extends App {
				
		function __construct($appConfig, $dbConn, $objects, $comms, $pgObjects, $cookies, $filesApi){
			
			$this->appConfig = $appConfig;		
			$this->db = $dbConn;
			$this->obj = $objects;
			$this->comm = $comms;
			$this->pgObjects = $pgObjects;
			$this->cookies = $cookies;
			$this->files = $filesApi;
			
		}
			
	}
	
	$objects = new Objects($pdo);

	$pgObjects = new pgObjects($pdo, $appConfig['instance'], $rules);
			
	$comms = new Comm($pgObjects, $appConfig);
	
	$cookies = new Cookies($pgObjects);
	
	$files = new FileApi($pgObjects, '../_files/_instances/'.$instanceName, $instanceName);

	$app = new MyApp($appConfig, $pdo, $objects, $comms, $pgObjects, $cookies, $files);
	
	$cookie = $cookies->checkCookie($_COOKIE['uid'], 'user', $_COOKIE['series'], $_COOKIE['token']);
	
	$tokenCheck = $cookies->checkToken($authHeader);
	
	if(!empty($_REQUEST['json'])){
		$_POST = json_decode($_REQUEST['json']);
	}

	if(1==1){

		if(!empty($_REQUEST['debug'])){
		
			error_reporting(E_ALL);
			ini_set('display_errors', '1');
			
		}else{
			
			error_reporting(0);
			
		}

		$method = $_REQUEST['do'];
		$service = $_REQUEST['service'];

		if (!empty($service)) {

			require_once APP_ROOT.'services/'. $service .'.php';
			
			$classname = $service;

			$serviceModule = new $classname($app);
			
			echo $serviceModule->$method($_POST);
			
		} else {
			
			echo $app->$method();	
			
		}
		
	}else{
		
		echo 'cookie error';
		die();
		
	}
	
}else{
	
	echo 'nothing to get...';
	
}
	
?>