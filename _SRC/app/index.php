<?php

require_once('api/BUILD_VERSION.php');

?>

<!DOCTYPE html>

<html lang="en">

<head>

    <title><PERSON><PERSON></title>

    <meta charset="utf-8">
    <!-- <meta http-equiv="X-UA-Compatible" content="IE=edge"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
    <meta name="description" content="">
    <meta name="author" content="">

    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png?v=alQqr7P5oX">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png?v=alQqr7P5oX">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png?v=alQqr7P5oX">
    <link rel="manifest" href="/site.webmanifest?v=alQqr7P5oX">
    <link rel="mask-icon" href="/safari-pinned-tab.svg?v=alQqr7P5oX" color="#5bbad5">
    <link rel="shortcut icon" href="/favicon.ico?v=alQqr7P5oX">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">

    <link href="css/pagoda.min-<?php echo APP_BUILD ?>.css" rel="stylesheet">
    <link href="/api/stylesheet.php" rel="stylesheet">

    <style>
    /*
		body {
			padding: 0 1em;
		}
		
		input {
			padding: 20px 10px !important;
			font-size: 20px;
		}
		
		.ui.white.segment {
			padding: 40px !important;
		}
*/
    </style>

    <!-- <script src="//rum-static.pingdom.net/pa-5e9fa23e11c070000800095f.js" async></script> -->

    <!-- <script>
    var ga;
    </script> -->

    <!-- Google Analytics -->
    <!-- <script>
    (function(i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function() {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

    ga('create', 'UA-109495396-4', 'auto');
    </script> -->
    <!-- End Google Analytics -->

</head>

<body>

    <nav>

    </nav>

    <br />

    <div class="ui one stackable column centered grid" style="max-width: 1200px; margin: 0 auto;">

        <div class="ui seven wide centered loading column">

            <br /><br />

            <div class="main mainContainerArea" style="margin-bottom: 54px;padding-bottom: 54px;"></div>

            <br /><br />

            <small>
                <p id="version" class="ui tiny centered header grey"></p>
            </small>

        </div>



    </div>

    <script>
    var appConfig = {

        instance: 'voltzsoftware',
        db: {
            post: './api/_post.php?',
            read: './api/_get.php?',
            write: './api/_get.php?'
        },
        form: '.main',
        formButton: '.formAction'

    };
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.4/jspdf.debug.js"></script>

    <script src="js/bento.pagoda.min-<?php echo APP_BUILD ?>.js"></script>

    <script type="text/javascript">
    $('#version').html('Version ' + PAGODA_APP_VERSION);

    Factory.register('voltz-software-form', function(sb) {

        var attempts = 0;
        var adminPath = appConfig.db.write.replace('_get.php', '_getAdmin.php') + 'pagodaAPIKey=' +
            appConfig.instance + '&do=';

        return {

            init: function() {

                sb.listen({
                    'show-multi-login-form': this.load,
                    'reset-password': this.reset,
                    'submit-form': this.submit,
                    'voltz-software-form-destroy': this.destroy
                });

                sb.notify({
                    type: 'show-multi-login-form',
                    data: {
                        form: sb.dom.make(appConfig.form)
                    }
                });

            },

            load: function(setup) {
                
                // Set variables
                var instance = sb.data.cookie.get('instance');
                var redirectURL = sb.data.cookie.get('redirectURL');

                // Bypass login if already logged in
                sb.data.db.checkAuth(function(response) {
                    window.location.href = '/app/' + instance;
                });

                var formDom = sb.dom.make(setup.form.selector);

                formDom.makeNode('login', 'div', {
                    header: 'Enter your login details below:',
                    css: 'ui white basic segment',
                    style: 'box-shadow:0 10px 25px rgba(1,24,33,.15); padding:15px 20px 30px 20px',
                    text: '<style>body{background-color:#f5f5f5}</style>'
                })
                .makeNode('body', 'div', {
                    css: ''
                });

                formDom.login.body.makeNode('logoContainer', 'div', {
                    css: 'ui basic segment',
                    style: 'box-shadow:none !important;'
                });

                formDom.login.body.logoContainer.makeNode('logo', 'div', {
                    text: '<img src="https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/bento/bentoLogo.jpeg" width="25px" class="ui centered image"></div>',
                    style: 'margin-bottom:10px;'
                });

                formDom.login.body.makeNode(
                    'form',
                    'form', {
                        email: {
                            type: 'text',
                            name: 'email',
                            label: 'Email Address:'
                        },
                        password: {
                            type: 'password',
                            name: 'password',
                            label: 'Password:'
                        }
                    });

                // Add tab index and check hit enter on inputs
                $(document).ready(function() {

                    //begin login on hit enter
                    $(formDom.login.body.form.email.selector).keypress(function(e) {
                        if (e.which == 13) {
                            //e.preventDefault();
                            sb.notify({
                                type: 'submit-form',
                                data: {
                                    form: formDom.login.body.form,
                                    dom: formDom
                                }

                            }, sb.moduleId);
                        }
                    });

                    $(formDom.login.body.form.password.selector).keypress(function(e) {
                        if (e.which == 13) {
                            //e.preventDefault();
                            sb.notify({
                                type: 'submit-form',
                                data: {
                                    form: formDom.login.body.form,
                                    dom: formDom
                                }

                            }, sb.moduleId);
                        }
                    });
                    //end login on hit enter

                    $(formDom.login.body.form.email.selector).attr("tabindex", "1");
                    $(formDom.login.body.form.password.selector).attr("tabindex", "2");
                });

                formDom.login.body.makeNode('formBreak', 'div', {
                    text: '<br />'
                });

                formDom.login.body.makeNode('reset', 'div', {
                    text: '<i class="icon unlock"></i> reset password',
                    css: 'ui tertiary button',
                    style: 'margin-left:5px !important; margin-top:10px !important; padding-left:0px; padding-right:0; border:none; color:gray; cursor:pointer;',
                    tag: 'button',
                    data: {
                        tabindex: 4
                    }
                });

                formDom.login.body.makeNode('button', 'div', {
                    text: '<i class="icon key"></i> Sign In',
                    css: 'ui bento-blue button huge',
                    style: 'float:right;',
                    tag: 'button',
                    data: {
                        tabindex: 3
                    }
                });

                formDom.login.body.makeNode('clear', 'div', {
                    style: 'clear:both;',
                });

                formDom.login.body.button.notify('click', {
                    type: 'submit-form',
                    data: {
                        form: formDom.login.body.form,
                        dom: formDom
                    }
                }, sb.moduleId);

                formDom.login.body.reset.notify('click', {
                    type: 'reset-password',
                    data: {
                        form: formDom.login.body.form,
                        dom: formDom
                    }
                }, sb.moduleId);

                formDom.build();

            },

            destroy: function() {

                sb.listen({
                    'voltz-software-form-load': this.load
                });

            },

            reset: function(setup) {

                var formData = setup.form.process();

                if (formData.fields.email.value == '') {
                    sb.dom.alerts.alert('Uh-oh', 'Please provide an email address.', 'error');
                    return;
                }

                sb.dom.alerts.ask({
                    title: 'Are you sure?',
                    text: 'This will generate and send this user a new password.'
                }, function(resp) {

                    if (resp) {

                        swal.disableButtons();

                        sb.data.db.controller('resetPassword&api_webform=1', {
                            email: formData.fields.email.value
                        }, function(done) {

                            if (done) {

                                sb.dom.alerts.alert('Success',
                                    'If this email address has an account, a new password was just sent to them.',
                                    'success');

                            }

                        }, adminPath);

                    }

                });

            },

            submit: function(setup) {

                var formData = setup.form.process();
                var redirectURL = sb.data.cookie.get('redirectURL');

                if (formData.fields.email.value == '' || formData.fields.password.value == '') {
                    sb.dom.alerts.alert('Uh-oh', 'Please fill out the entire form.', 'error');
                    return;
                }

                $(setup.dom.login.body.button.selector).html(
                    '<div class="ui tiny active inline loader" style="margin-right:10px; top:-2px;"></div> Processing'
                );

                sb.data.db.loginUser({
                    email: formData.fields.email.value,
                    password: formData.fields.password.value,
                    multi: true
                }, function(passed) {

                    passed = _.uniq(passed, function(a) {
                        return a.instance;
                    });

                    passed = _.sortBy(passed, 'instance');

                    setTimeout(function() {

                        if (passed != false) {

                            if (redirectURL) {

                                var instanceName = redirectURL.split('/app/').pop().split('#')[0];

                                var account = _.findWhere(passed, {instance: instanceName});

                                if (account) {

                                    sb.notify({
                                        type: 'login-account-selected',
                                        data: {
                                            account: account,
                                            dom: setup.dom,
                                            instance: account.instance
                                        }
                                    });

                                    return;

                                }

                            }

                            if (passed.length == 1) {

                                sb.notify({
                                    type: 'login-account-selected',
                                    data: {
                                        account: passed[0],
                                        dom: setup.dom,
                                        instance: passed[0].instance
                                    }
                                });

                                return;
                            }

                            setup.dom.login.body.empty();

                            setup.dom.login.body.makeNode('break', 'lineBreak', {
                                spaces: 1
                            });

                            setup.dom.login.body.makeNode('accounts', 'div', {})
                                .makeNode('body', 'div', {});

                            setup.dom.login.body.accounts.body.makeNode('title',
                                'headerText', {
                                    text: '<i class="fa fa-users"></i> Choose an account',
                                    size: 'small',
                                    css: 'text-center'
                                });

                            setup.dom.login.body.accounts.body.makeNode('table',
                                'table', {
                                    clearCSS: true,
                                    css: 'ui table',
                                    columns: {
                                        account: 'Accounts',
                                        btns: ''
                                    }
                                });

                            setup.dom.login.body.accounts.body.makeNode('table',
                                'div', {
                                    css: ''
                                });

                            _.each(passed, function(account, i) {

                                var instanceName = account.instance;
                                if (!_.isEmpty(account.instanceName)) {
                                    instanceName = account.instanceName;
                                }

                                setup.dom.login.body.accounts.body.table
                                    .makeNode('acct-' + i, 'div', {
                                        css: 'ui attached fluid basic segment'
                                    });

                                setup.dom.login.body.accounts.body.table[
                                    'acct-' + i].makeNode('name', 'div', {
                                    text: instanceName,
                                    css: 'ui header'
                                });
                                setup.dom.login.body.accounts.body.table[
                                    'acct-' + i].makeNode('button', 'div', {
                                    text: 'Login <i class="fa fa-arrow-right"></i>',
                                    css: 'ui blue fluid button',
                                    tag: 'button'
                                }).notify('click', {
                                    type: 'login-account-selected',
                                    data: {
                                        account: account,
                                        dom: setup.dom,
                                        instance: account.instance
                                    }
                                }, sb.moduleId);

                            });

                            setup.dom.build();

                        } else {

                            attempts++;

                            sb.dom.alerts.alert('Try again', '', 'error');

                            $(setup.dom.login.body.button.selector).html(
                                '<i class="icon key"></i> Sign In');

                        }

                    }, 1000);

                });

            }

        }

    });

    Factory.run();
    </script>

</body>

</html>