<?php

if($appConfig['version']){
	
	switch($appConfig['instance']){
			
		case 'rickyvoltz':
		case 'petermikhail':
		case 'joshgantt':
		case 'johnwhittenburg':
		case 'zachvoltz':
			
			switch($appConfig['version']){
			
				case 'bento':
				case 'bin':
				case 'tlb':
				
					echo '<script src="../js/bento.pagoda.min-'.APP_BUILD.'.js"></script>';
				
					break;
										
				case 'capd':
				
					echo '<script src="../js/captaind.pagoda.min-'.APP_BUILD.'.js"></script>';
				
					break;	
					
				default:
			
					echo '<script src="../js/bento.pagoda.min-'.APP_BUILD.'.js"></script>';
				
			}

			break;
	
		case 'voltzsoftware':
			
			echo '<script src="../js/bento.pagoda.min-'.APP_BUILD.'.js"></script>';
			
			break;
		
		default:
			
			switch($appConfig['version']){
			
				case 'bento':
				case 'bin':
				case 'tlb':
				
					echo '<script src="../js/bento.pagoda.min-'.APP_BUILD.'.js"></script>';
				
					break;
										
				case 'capd':
				
					echo '<script src="../js/captaind.pagoda.min-'.APP_BUILD.'.js"></script>';
				
					break;	
										
				default:
			
					echo '<script src="../js/bento.pagoda.min-'.APP_BUILD.'.js"></script>';	
				
			}
						
	}
			
}else{

	switch($appConfig['instance']){
			
		case 'rickyvoltz':
		case 'petermikhail':
		case 'joshgantt':
		case 'johnwhittenburg':
		case 'zachvoltz':

			echo '<script src="../js/bento.pagoda.min-'.APP_BUILD.'.js"></script>';

			break;
	
		case 'voltzsoftware':
			
			echo '<script src="../js/bento.pagoda.min-'.APP_BUILD.'.js"></script>';
			
			break;
		
		default:

			echo '<script src="../js/bento.pagoda.min-'.APP_BUILD.'.js"></script>';
						
	}
	
}
		
?>