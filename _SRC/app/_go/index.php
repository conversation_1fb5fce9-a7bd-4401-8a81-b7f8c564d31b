<?php

/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

require_once('_app.php');

if (!$instanceName) {
	header("Location: ../");
	die();
}

?>

<!DOCTYPE html>

<html lang="en">

<head>

	<?php require_once 'meta.php'; ?>

	<title><?php echo $appConfig['systemName'] ?></title>

	<link href="../css/pagoda.min-<?php echo APP_BUILD ?>.css" rel="stylesheet">

	<?php

	$instanceKey = $appConfig['instance'];
	if (
		$instanceKey !== 'foundation_group'
		&& $instanceKey !== 'erationalmarketing'
		&& !empty($appConfig['parentInstance'])
	) {
		$instanceKey = $appConfig['parentInstance'];
	}
	switch ($instanceKey) {

		case 'foundation_group':

			echo '<link href="/api/stylesheet-foundation_group.css" rel="stylesheet">';

			break;


		case 'erationalmarketing':

			echo '<link href="/api/stylesheet-erationalmarketing.css" rel="stylesheet">';

			break;

		default:

			echo '<link href="/api/stylesheet.css" rel="stylesheet">';
	}

	?>

	<script>
		var appConfig = <?php echo json_encode($appConfig) ?>;
	</script>

	<!-- warms up the DNS lookup, TCP handshake, and TLS negotiation to the fonts.gstatic.com domain -->
	<link rel="preconnect" href="https://fonts.gstatic.com" />
	<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin />
	<link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,400%3B0,700%3B1,400%3B1,700&subset=latin&display=swap" rel="stylesheet">


	<!-- <link href="/api/stylesheet.php?instance=<?= $appConfig['instance'] ?>" rel="stylesheet"> -->

	<?php

	// Set instance name
	$instanceName = $appConfig['instance'];


	// Get page objects
	$pgObjects = new pgObjects($pdo, $instanceName);

	// Get the instance
	$instance = $pgObjects->where('instances', array('instance' => $instanceName))[0];

	// Get instance logo
	$instanceLogo = $pgObjects->where(
		'company_logo',
		[
			'is_primary' => 'yes'
		],
		'',
		[
			'company_logo' => true
		]
	)[0];

	switch ($appConfig['instance']) {

		case 'contracts':
		case 'documents':
		case 'invoices':
		case 'achverification':
		case 'workorders':
		case 'timeclock';
		case 'rickyvoltz';

			echo '<link href="https://fonts.googleapis.com/css?family=La+Belle+Aurore" rel="stylesheet">';
			break;

		default:
	}

	switch ($appConfig['instance']) {

		case 'erationalmarketing':
		case 'dreamcatering':
		case 'foundation_group':
		case 'infinity':
		case 'nlp':
		case 'thelifebook';
		case 'voltzsoftware';

			$logo_url = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/' . $instanceName . '/' . $instanceLogo['company_logo']['loc'];

			break;

		default:
			$logo_url = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/bento/bentoLogo.jpeg';
	}

	?>



</head>

<body class="" style="height:100%;" ontouchstart="">

	<div id="main-loader" class="ui active text loader">
		<span class="ui small text">Powered by</span></br>
		<?php echo '<img class="ui tiny middle aligned centered image" style="margin-top:10px;" src="' . $logo_url . '">'; ?></br></br>
		<span id="main-loader-text" class="ui grey text"></span>
	</div>

	<div class="main" style="height:100%;"></div>

	<!-- <script>
		if ('serviceWorker' in navigator) {
			navigator.serviceWorker.register('/service-worker.js').then(function(registration) {
				// Registration was successful
				//console.log('ServiceWorker registration successful with scope: ', registration.scope);
			}).catch(function(err) {
				// registration failed :(
				//console.log('ServiceWorker registration failed: ', err);
			});
		}
	</script> -->

	<?php require_once 'js.php'; ?>

	<?php

	switch ($appConfig['instance']) {

		case 'invoices':
		case 'achverification':
		case 'contracts':
		case 'documents':
		case 'workorders':
		case 'timeclock':

			echo '<script src="https://cdn.jsdelivr.net/npm/signature_pad@2.3.2/dist/signature_pad.min.js"></script>';
			echo '<div class="mainCanvas"></div>';

			break;

		default:
	}

	?>

	<input type="hidden" id="isWaitingToSave">


	<script>
		switch (appConfig.instance) {

			case 'contracts':

				Factory.publicLink = true;
				Factory.register('page', function(sb) {

					var components = {};

					return {

						init: function() {

							sb.data.db.setAppConfig(appConfig);

							sb.data.db.setToken(sb.data.cookie.get('token'));

							var domObj = sb.dom.make('.main');

							domObj.makeNode('grid', 'div', {
								css: 'ui stackable grid'
							});

							domObj.build();

							sb.notify({
								type: 'start-signature-portal',
								data: {
									domObj: domObj.grid
								}
							});

						}

					}

				});

				break;

			case 'documents':

				Factory.publicLink = true;
				Factory.register('page', function(sb) {

					var components = {};

					return {

						init: function() {

							sb.data.db.setAppConfig(appConfig);

							sb.data.db.setToken(sb.data.cookie.get('token'));

							var domObj = sb.dom.make('.main');
							domObj.makeNode('content', 'div', {
								css: ''
							});
							domObj.build();

							sb.notify({
								type: 'start-document-portal',
								data: {
									domObj: domObj.content
								}
							});

							$('#main-loader').addClass('animated fadeOut');

						}

					}

				});

				break;

			case 'workorders':

				Factory.publicLink = true;
				Factory.register('page', function(sb) {

					var components = {};

					return {

						init: function() {

							sb.data.db.setAppConfig(appConfig);

							sb.data.db.setToken(sb.data.cookie.get('token'));

							var domObj = sb.dom.make('.main');

							domObj.build();

							sb.notify({
								type: 'start-client-portal',
								data: {
									domObj: domObj
								}
							});

							$('#main-loader').addClass('animated fadeOut');

						}

					}

				});

				break;

			case 'invoices':

				Factory.publicLink = true;
				Factory.register('page', function(sb) {

					var components = {};

					return {

						init: function() {

							sb.data.db.setAppConfig(appConfig);

							sb.data.db.setToken(sb.data.cookie.get('token'));

							var domObj = sb.dom.make('.main');

							domObj.build();

							sb.notify({
								type: 'start-payment-portal',
								data: {
									domObj: domObj
								}
							});

						}

					}

				});

				break;

			case 'ach_verification':

				Factory.publicLink = true;
				Factory.register('page', function(sb) {

					var components = {};

					return {

						init: function() {

							sb.data.db.setAppConfig(appConfig);

							sb.data.db.setToken(sb.data.cookie.get('token'));

							var domObj = sb.dom.make('.main');

							domObj.build();

							sb.notify({
								type: 'start-ach-verification',
								data: {
									domObj: domObj
								}
							});

							$('#main-loader').addClass('animated fadeOut');

						}

					}

				});

				break;

			case 'forms':

				Factory.publicLink = true;
				Factory.register('page', function(sb) {

					var components = {};

					return {

						init: function() {

							appConfig.api_webform = sb.data.url.getParams().i;
							appConfig.instance = sb.data.url.getParams().i;

							sb.data.db.setAppConfig(appConfig);
							sb.data.db.setAPIPath('../../api/_getAdmin.php');

							var domObj = sb.dom.make('.main');

							domObj.makeNode('cont', 'div', {});
							domObj.build();

							sb.notify({
								type: 'display-public-form',
								data: {
									ui: domObj.cont,
									seed: {}
								}
							});

							$('#main-loader').addClass('animated fadeOut');

						}

					}

				});

				break;

			case 'timeclock':

				Factory.publicLink = true;
				Factory.register('page', function(sb) {

					var components = {};

					return {

						init: function() {

							sb.data.db.setAppConfig(appConfig);

							sb.data.db.setToken(sb.data.cookie.get('token'));

							var domObj = sb.dom.make('.main');

							domObj.build();

							sb.notify({
								type: 'start-keypad-portal',
								data: {
									domObj: domObj
								}
							});

							$('#main-loader').addClass('animated fadeOut');

						}

					}

				});

				break;


			default:

				Factory.publicLink = false;
				Factory.register('page', function(sb) {

					var components = {};

					function detectmob() {

						if (
							navigator.userAgent.match(/Android/i) ||
							navigator.userAgent.match(/webOS/i) ||
							navigator.userAgent.match(/iPhone/i) ||
							navigator.userAgent.match(/iPad/i) ||
							navigator.userAgent.match(/iPod/i) ||
							navigator.userAgent.match(/BlackBerry/i) ||
							navigator.userAgent.match(/Windows Phone/i)
						) {
							return true;
						} else {
							return false;
						}

					}

					var is_mobile = detectmob();

					return {

						init: function() {

							sb.data.db.setAppConfig(appConfig);

							sb.data.db.setToken(sb.data.cookie.get('token'));

							var domObj = sb.dom.make('.main');

							domObj.build();
							sb.notify({
								type: 'start-application',
								data: {
									domObj: domObj,
								}
							});

						}

					}

				});

		}

		Factory.startAll();

		var cursorX;
		var cursorY;
		document.onmousemove = function(e) {
			cursorX = e.pageX;
			cursorY = e.pageY;
		}
	</script>

</body>

<!-- <script>
		!function(g,s,q,r,d){r=g[r]=g[r]||function(){(r.q=r.q||[]).push(
		arguments)};d=s.createElement(q);q=s.getElementsByTagName(q)[0];
		d.src='//d1l6p2sc9645hc.cloudfront.net/tracker.js';q.parentNode.
		insertBefore(d,q)}(window,document,'script','_gs');
		
		_gs('GSN-752757-B');
		_gs('set', 'anonymizeIP', true);
		_gs('set', 'trackHash', true);
	</script> -->

<!-- <script src="https://cdn.plaid.com/link/v2/stable/link-initialize.js" defer></script> -->

<script src="https://js.stripe.com/v3/" defer></script>


<!-- <script type="text/javascript" src="https://cdn.statuspage.io/se-v2.js" defer></script> -->


</html>