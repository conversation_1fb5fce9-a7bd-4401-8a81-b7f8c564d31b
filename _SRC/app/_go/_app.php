<?php

if (extension_loaded ('newrelic')) {
	newrelic_set_appname ("Pagoda");
}

define( "APP_ROOT", '../../api/' );

require_once '../../api/lib/_.php';

$trimedURL = rtrim($_SERVER['REQUEST_URI'], '/');
$URLArray = explode('/', $trimedURL);
$instanceName = end($URLArray);

require_once '../../api/rules/rules.php';
require_once '../../api/_pgObjectsMT.php';
require_once '../../api/_pgObjectsMTAdmin.php';
require_once '../../api/DBPATH.php';
require_once '../../api/BUILD_VERSION.php';

$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");

$rules = new Rules();

$pgObjects = new pgObjects($pdo, 'pagodadev', $rules);

$pgObjectsAdmin = new pgObjects($pdo, $instanceName, $rules);

$instance = $pgObjectsAdmin->where('instances', array('instance'=>$instanceName))[0];

$appConfig = array();

if ($instance) {

	foreach ($instance as $k => $v) {
			
		switch($k) {
			
			case 'components':
			case 'pageModules':
			case 'settingsModules':
			
				$appConfig[$k] = explode(',', $v);
			
				break;
			
			case 'db_post':
			
				$appConfig['db']['post'] = $v;
			
				break;
				
			case 'db_read':
			
				$appConfig['db']['read'] = $v;
			
				break;
				
			case 'db_write':
			
				$appConfig['db']['write'] = $v;
			
				break;
				
			case 'files_bucket':
			
				$appConfig['files']['bucket'] = $v;
			
				break;	
				
			case 'files_delete':
			
				$appConfig['files']['delete'] = $v;
			
				break;
				
			case 'files_read':
			
				$appConfig['files']['read'] = $v;
			
				break;
				
			case 'files_write':
			
				$appConfig['files']['write'] = $v;
			
				break;
				
			case 'twilio_sid':
			
				$appConfig['twilio']['sid'] = $v;	
			
				break;
				
			case 'twilio_token':
			
				$appConfig['twilio']['token'] = $v;
				
				break;	
				
			case 'sms_from':
			
				$appConfig['twilio']['smsFrom'] = $v;
				
				break;						
			
			default:
			
				$appConfig[$k] = $v;
			
		}

	}

}

switch ($instanceID) {
	
	case '_staging':
	case 'petermikhail':
	case 'joshgantt':
	case 'johnwhittenburg':
	case 'rickyvoltz':
	case 'zachvoltz':
	case 'jonathanwright':

		break;
					
	default:
		$appConfig['files']['bucket'] = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com';
	
}

$permissions = $pgObjects->getAll('permissions');
$userSettings = $pgObjects->getWhere('user_settings', "where object_data->>'userId'='". $_COOKIE['uid'] ."'")[0];
