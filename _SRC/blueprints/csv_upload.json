{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "date_created", "type": "string"}, "file": {"immutable": false, "name": "File", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "id": {"immutable": true, "name": "id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "rows": {"immutable": false, "name": "Rows of Data", "type": "int"}, "rows_transcribed": {"immutable": false, "name": "Rows Transcribed", "type": "int"}, "rows_transcribed_and_related": {"immutable": false, "name": "Rows Transcribed and Relations Set", "type": "int"}, "source": {"immutable": false, "name": "Source", "objectType": "csv_source", "selectDisplay": "[name]", "type": "objectId"}, "status": {"immutable": false, "name": "Status", "options": {"complete": "Complete", "deleted": "Deleted", "not_started": "Not started", "started": "Started"}, "type": "select"}, "translation": {"immutable": false, "name": "Translation", "type": "object"}}, "blueprint_name": "csv_upload", "blueprint_type": "object", "date_created": "2017-06-08 15:07:27.945331", "id": 302, "instance": "<PERSON><PERSON><PERSON><PERSON>"}