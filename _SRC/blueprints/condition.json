{"blueprint": {"conditions": {"name": "Conditions", "type": "list", "immutable": false, "blueprint": {"conditions": {"name": "Conditions", "type": "object", "immutable": false}, "type": {"name": "Type", "type": "string", "immutable": false}, "operator": {"name": "Operator", "type": "string", "immutable": false}, "options": {"name": "Options", "type": "object", "immutable": false}, "uid": {"name": "<PERSON><PERSON>", "type": "string", "immutable": false}}}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string", "fieldType": "plain-text"}, "next": {"name": "True Path", "type": "object", "immutable": false, "blueprint": {"type": {"name": "Type", "type": "select", "options": {"event_type": "Action", "condition": "Condition", "state": "Workflow State"}, "immutable": false}, "pointer": {"name": "Points to", "type": "int", "immutable": false}}}, "else_do": {"name": "Else", "type": "object", "immutable": false, "blueprint": {"type": {"name": "Type", "type": "select", "options": {"event_type": "Action", "condition": "Condition", "state": "Workflow State"}, "immutable": false}, "pointer": {"name": "Points to", "type": "int", "immutable": false}}}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_after_state_change": {"immutable": false, "name": "Is after state change", "type": "bool"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "name": {"immutable": false, "name": "Name", "type": "string", "fieldType": "title"}, "object": {"immutable": false, "name": "Object", "type": "int"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "state": {"name": "State", "type": "int", "immutable": false}}, "blueprint_name": "condition", "blueprint_type": "object", "date_created": "2019-06-03 19", "instance": "<PERSON><PERSON><PERSON><PERSON>"}