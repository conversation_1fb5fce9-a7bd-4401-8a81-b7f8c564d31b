{"blueprint": {"active": {"immutable": false, "name": "Active", "options": ["Yes", "No"], "type": "select"}, "contract_types": {"immutable": false, "name": "Contract Type", "objectType": "contract_types", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": false, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "html_string": {"immutable": false, "name": "Contract", "type": "document"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "is_public": {"immutable": false, "name": "Is Public?", "type": "bool"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "main_contact": {"immutable": false, "name": "Main Contact", "objectType": "contacts", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "owner": {"immutable": false, "name": "Owner", "objectType": "users", "selectDisplay": "[fname] [lname]", "objectOverflow": true, "type": "objectId"}, "main_client": {"immutable": false, "name": "Main Client", "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true, "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "merge_type": {"immutable": false, "name": "Merge Type", "type": "string"}, "object_type": {"immutable": false, "name": "Object Type", "type": "string"}, "object_uid": {"immutable": false, "name": "Object Id", "type": "int"}, "orientation": {"immutable": false, "name": "Orientation", "type": "string"}, "related_object": {"immutable": true, "name": "Related Object", "type": "relatedObject"}, "restore_object": {"immutable": true, "name": "Restore Object", "type": "text"}, "sent": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}, "notify_list": {"immutable": false, "name": "Notify List", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectIds", "objectOverflow": true}, "space": {"immutable": true, "name": "Related Object", "type": "relatedObject"}, "sent_by": {"immutable": false, "name": "<PERSON><PERSON>", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "sent_on": {"immutable": false, "name": "<PERSON><PERSON>", "type": "date"}, "signed_on": {"immutable": false, "name": "Signed On", "type": "date"}, "signatures": {"immutable": false, "name": "Signature Files", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "signed_copies": {"immutable": false, "name": "Signed Copies", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "signer_email": {"immutable": true, "name": "Signer <PERSON><PERSON>", "type": "text"}, "signer_ip": {"immutable": false, "name": "Signer <PERSON>", "type": "string"}, "signer_name": {"immutable": true, "name": "Signer Name", "type": "text"}, "after_signature": {"immutable": false, "name": "After Signature", "options": ["nothing", "invoices"], "type": "select"}, "status": {"immutable": false, "name": "Status", "options": ["Unsigned", "Approval Requested", "Approved", "Declined", "Out For Signature", "Signing In Process", "Signed"], "type": "select"}, "requires_approval": {"immutable": false, "name": "Requires Approval", "options": ["No", "Yes"], "type": "select"}}, "blueprint_name": "contracts", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.444338", "id": 268, "instance": "<PERSON><PERSON><PERSON><PERSON>"}