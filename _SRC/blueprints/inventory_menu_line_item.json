{"blueprint": {"absolute_price": {"immutable": false, "name": "Absolute Price", "type": "int"}, "absolute_qty": {"immutable": false, "name": "Absolute Quantity", "type": "float"}, "applied_surcharges": {"immutable": false, "name": "Applied Surcharges", "type": "object"}, "applied_taxes": {"immutable": false, "name": "Applied Taxes", "type": "object"}, "choices": {"blueprint": {"choice": {"immutable": false, "name": "Choice", "type": "object"}, "item": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}}, "immutable": false, "name": "Choices", "type": "object"}, "coa": {"name": "Chart of acct", "type": "int", "immutable": false}, "date_of_use": {"immutable": false, "name": "Date of Use", "type": "date"}, "id": {"immutable": false, "name": "id", "type": "int"}, "invoice": {"immutable": false, "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "item": {"immutable": false, "name": "<PERSON><PERSON>", "type": "object"}, "item_reservations": {"immutable": false, "name": "Reservations", "objectType": "item_reservation", "selectDisplay": "[date]", "type": "objectIds"}, "measurement": {"immutable": false, "name": "Measurement", "type": "int"}, "menu": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}, "note": {"immutable": false, "name": "Notes", "type": "string"}, "beo_html": {"immutable": false, "name": "BEO Html", "type": "string"}, "beo_note": {"immutable": false, "name": "BEO Line Item Note", "type": "string"}, "beo_qty": {"immutable": false, "name": "BEO Line Item Quantity", "type": "int"}, "beo_servingstyle": {"immutable": false, "name": "BEO Line Item Serving Style", "type": "string"}, "beo_ingredients": {"blueprint": {"ingredients": {"immutable": false, "name": "BEO Ingredients", "type": "object"}}, "immutable": false, "name": "Choices", "type": "object"}, "nestedIngredients": {"immutable": false, "name": "Nested Ingredients", "type": "object"}, "override_tax_rates": {"immutable": false, "name": "Override category default tax rates?", "type": "int"}, "owner": {"name": "Owner", "type": "int", "immutable": false}, "price_type": {"immutable": false, "name": "Price Type", "options": {"price": "Standard Pricing", "price_per_hour": "Per hour", "price_per_hour_per_person": "Per hour per person", "price_per_person": "Per person"}, "type": "select"}, "price_with_line_item_discounts": {"immutable": false, "name": "Price with Line item discount", "type": "int"}, "product": {"immutable": true, "name": "Product", "type": "int"}, "product_category": {"immutable": false, "name": "Product Category", "type": "int"}, "qty": {"immutable": false, "name": "Quantity", "type": "float"}, "qty_type": {"immutable": false, "name": "Quantity Type", "options": {"absolute": "Total", "guest_count": "Guest Count", "per_guest": "Per Guest"}, "type": "select"}, "section": {"immutable": false, "name": "Section", "type": "int"}, "servings": {"immutable": false, "name": "Servings", "type": "float"}, "sortId": {"immutable": false, "name": "Sort Id", "type": "int"}, "state": {"immutable": false, "name": "state", "options": {"reserved": "Stock items reserved", "selections_completed": "All selections have been made", "selections_open": "Choices are still open"}, "type": "select"}, "surcharges": {"immutable": false, "name": "Surcharges", "objectType": "surcharges", "selectDisplay": "[name]", "type": "objectIds"}, "tax_rates": {"immutable": false, "name": "Tax Rates", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "type": {"immutable": false, "name": "Type", "options": {"budget": "Budget", "estimate": "Estimate", "item": "<PERSON><PERSON>"}, "type": "select"}, "unit_type": {"immutable": false, "name": "Unit type", "type": "string"}, "vendor": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "objectType": "companies", "selectDisplay": "[name]", "type": "objectId"}, "yield_type": {"immutable": false, "name": "Yield type", "type": "string"}, "vendor_approval_status": {"immutable": false, "name": "Vendor Approval Status", "options": {"PENDING": "Pending Approval", "APPROVED": "Approved", "DECLINED": "Declined"}, "type": "select"}}, "blueprint_name": "inventory_menu_line_item", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 37, "instance": "<PERSON><PERSON><PERSON><PERSON>"}