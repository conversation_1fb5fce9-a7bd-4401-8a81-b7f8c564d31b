{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "default": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "options": {"no": "Not Default", "yes": "<PERSON><PERSON><PERSON>"}, "type": "select"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Category Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "permissions": {"immutable": false, "name": "Who can see these?", "objectType": "user_views", "selectDisplay": "[name]", "type": "objectIds"}, "updated_by": {"immutable": true, "name": "Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}}, "blueprint_name": "note_types", "blueprint_type": "object", "date_created": "2017-09-12 19:38:14.693358", "id": 318, "instance": "<PERSON><PERSON><PERSON><PERSON>"}