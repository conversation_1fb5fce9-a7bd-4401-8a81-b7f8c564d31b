{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "group_type": {"immutable": false, "name": "Type", "options": ["Headquarters", "Team", "Project", "Task", "Schedule", "Shift", "MyStuff", "JobType"], "type": "select"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_public": {"immutable": false, "name": "Is Public", "type": "int"}, "is_rightTray": {"immutable": false, "name": "Is Right Tray", "type": "int"}, "box_links": {"immutable": false, "name": "Box Links", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_type": {"immutable": true, "name": "Object Type", "type": "string"}, "object_subtype": {"immutable": true, "name": "Object Subtype", "type": "relatedObject"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "layout_id": {"immutable": true, "name": "Layout ID", "type": "int"}, "set_layout_id": {"immutable": true, "name": "Layout ID", "type": "string"}, "layout": {"immutable": false, "name": "Layout", "type": "object"}, "settings": {"immutable": false, "name": "Settings", "type": "object"}, "tools": {"blueprint": {"added_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "added_on": {"immutable": true, "name": "Date Created", "type": "string"}, "allowed_users": {"immutable": false, "name": "Allowed Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "display_name": {"immutable": false, "name": "Display Name", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_archieved": {"immutable": false, "name": "<PERSON>", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "main_contact": {"immutable": false, "name": "Main contact", "objectType": "contacts", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "order": {"immutable": false, "name": "Order", "type": "int"}, "potential_value": {"immutable": false, "name": "Potential Value", "type": "int"}, "related_contacts": {"immutable": false, "name": "Related contacts", "objectType": "contacts", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "settings": {"immutable": false, "name": "Settings", "type": "object"}, "system_name": {"immutable": false, "name": "System Name", "type": "string"}, "tip": {"immutable": false, "name": "Tip", "type": "string"}}, "immutable": false, "name": "Tools", "type": "list"}, "user": {"immutable": false, "name": "User", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}}, "blueprint_name": "view_settings", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 29, "instance": "<PERSON><PERSON><PERSON><PERSON>"}