{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "fields": {"blueprint": {"name": {"immutable": false, "name": "Name", "type": "string"}}, "immutable": false, "name": "Extra Fields", "type": "list"}, "file_download": {"immutable": false, "name": "File Download", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "need_exp_date": {"immutable": false, "name": "Need Exp Date", "options": {"No": "No", "Yes": "Yes"}, "type": "select"}, "need_file_download": {"immutable": false, "name": "Need Download", "options": {"No": "No", "Yes": "Yes"}, "type": "select"}, "need_file_upload": {"immutable": false, "name": "Need Upload", "options": {"No": "No", "Yes": "Yes"}, "type": "select"}, "not_frequency": {"immutable": false, "name": "Notification Frequency", "type": "int"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "service_type": {"immutable": false, "name": "Service Types", "objectType": "inventory_service", "selectDisplay": "[name]", "type": "objectIds"}}, "blueprint_name": "staff_paperwork_type", "blueprint_type": "object", "date_created": "2017-07-18 13:57:48.742427", "id": 313, "instance": "<PERSON><PERSON><PERSON><PERSON>"}