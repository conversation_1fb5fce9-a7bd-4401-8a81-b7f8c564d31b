{"blueprint": {"authorization_code": {"name": "Code", "immutable": true, "type": "string"}, "client_id": {"name": "Client Id", "immutable": true, "type": "string"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "expires": {"name": "Expires", "immutable": true, "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "id_token": {"name": "ID Token", "immutable": true, "type": "string"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "redirect_uri": {"name": "Redirect URI", "immutable": true, "type": "string"}, "scope": {"name": "<PERSON><PERSON>", "immutable": true, "type": "string"}, "user_id": {"name": "User Id", "immutable": true, "type": "int"}}, "blueprint_name": "oauth_authorization_code", "blueprint_type": "object", "date_created": "2019-07-10 19", "instance": "<PERSON><PERSON><PERSON><PERSON>"}