{"blueprint": {"city": {"immutable": false, "name": "City", "type": "string"}, "country": {"immutable": false, "name": "Country", "type": "string"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "state": {"immutable": false, "name": "State/Province", "type": "string"}, "street": {"immutable": false, "name": "Street", "type": "string"}, "zip": {"immutable": false, "name": "Zip", "type": "string"}}, "blueprint_name": "staff_base", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.425857", "id": 251, "instance": "<PERSON><PERSON><PERSON><PERSON>"}