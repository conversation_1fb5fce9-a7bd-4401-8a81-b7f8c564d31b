{"blueprint": {"color": {"immutable": false, "name": "Color", "type": "text"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data": {"immutable": false, "name": "Data", "type": "object"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "details": {"immutable": false, "name": "Details", "type": "string"}, "event_type": {"immutable": true, "name": "Event type", "objectType": "event_type", "type": "objectId"}, "icon": {"immutable": false, "name": "Icon", "type": "string"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "is_archived": {"immutable": true, "name": "Is Archived?", "type": "int"}, "is_deleted": {"immutable": true, "name": "Is Deleted?", "type": "int"}, "is_viewed": {"immutable": true, "name": "Viewed", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "link": {"immutable": true, "name": "Link", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "producer": {"immutable": false, "name": "Producer", "type": "relatedObject"}, "producer_type": {"immutable": false, "name": "Producer", "type": "relatedObject"}, "title": {"immutable": false, "name": "Title", "type": "string"}, "type": {"immutable": false, "name": "Notification Type", "options": {"assignment": "Assignment", "general": "General", "mention": "Mention", "question": "Question", "reminder": "Reminder", "recent-client-activity": "Recent Client Activity"}, "type": "select"}, "user": {"immutable": true, "name": "User", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}}, "blueprint_name": "notification", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 58, "instance": "<PERSON><PERSON><PERSON><PERSON>"}