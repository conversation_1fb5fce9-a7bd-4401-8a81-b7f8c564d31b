{"blueprint": {"account_id": {"immutable": false, "name": "Account ID", "type": "string"}, "chart_of_accounts_company": {"immutable": false, "name": "Chart of accounts company", "objectType": "chart_of_accounts_companies", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "note": {"immutable": false, "name": "Note", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "quickbooks_account_id": {"immutable": false, "name": "<PERSON><PERSON><PERSON><PERSON> Account Id", "type": "int"}}, "blueprint_name": "chart_of_accounts", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.419449", "id": 245, "instance": "<PERSON><PERSON><PERSON><PERSON>"}