{"blueprint": {"id": {"immutable": true, "name": "ID", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "string", "fieldType": "title"}, "event_desc": {"immutable": false, "name": "Event Description", "type": "object", "fieldType": "object"}, "event_organizer": {"immutable": false, "name": "Event Organizer", "type": "string", "fieldType": "plain-text"}, "event_attendees": {"immutable": false, "name": "Event Attendees", "type": "object", "fieldType": "object"}, "event_start": {"immutable": false, "name": "Event Start Time", "type": "string", "fieldType": "plain-text"}, "event_end": {"immutable": false, "name": "Event End Time", "type": "string", "fieldType": "plain-text"}, "entity_type": {"immutable": false, "name": "Entity Type", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}}, "blueprint_name": "caldav_map", "blueprint_type": "object", "date_created": "2021-09-07T15:42:57Z", "instance": "<PERSON><PERSON><PERSON><PERSON>"}