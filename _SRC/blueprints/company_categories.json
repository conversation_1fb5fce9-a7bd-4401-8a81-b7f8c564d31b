{"blueprint": {"available_types": {"immutable": false, "name": "Available Types", "objectType": "contact_info_types", "selectDisplay": "[name]", "type": "objectIds"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "notEmpty": true, "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "tools": {"immutable": false, "name": "Tools", "objectType": "entity_tool", "type": "objectIds"}}, "blueprint_name": "company_categories", "blueprint_type": "object", "date_created": "2017-10-21 09:46:41.561105", "id": 4, "instance": "<PERSON><PERSON><PERSON><PERSON>"}