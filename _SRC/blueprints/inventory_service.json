{"blueprint": {"can_be_billed": {"immutable": false, "name": "Billing Type", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "chart_of_account": {"immutable": false, "name": "Chart of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description/Model", "type": "string"}, "exclusive_tax": {"immutable": false, "name": "Exclusive Tax", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds", "objectOverflow": true}, "group_object": {"immutable": false, "name": "JobType Group Object", "objectType": "groups", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "id": {"immutable": true, "name": "ID", "type": "int"}, "inclusive_tax": {"immutable": false, "name": "Inclusive Tax", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds", "objectOverflow": true}, "inventory_billable_categories": {"immutable": false, "name": "Inventory Category", "objectType": "inventory_billable_categories", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "is_deleted": {"immutable": true, "name": "Is deleted?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "manager_locations": {"immutable": false, "name": "Manager Locations", "objectType": "staff_base", "selectDisplay": "[name]", "type": "objectIds", "objectOverflow": true}, "max_flat_hours": {"immutable": false, "name": "Max Hours for Flat Rate", "type": "float"}, "min_hours": {"immutable": false, "name": "Min Hours", "type": "float"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "permission": {"immutable": false, "name": "Permission", "objectType": "user_views", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "price": {"immutable": false, "name": "Price", "type": "usd"}, "price_type": {"immutable": false, "name": "Billing Type", "options": {"flat": "Flat", "flat_and_hourly": "Flat + Hourly", "hourly": "Hourly", "non_billable": "Non-Billable"}, "type": "select"}, "rate": {"immutable": false, "name": "Rate ($/hr)", "type": "usd"}, "surcharges": {"immutable": false, "name": "Surcharges", "objectType": "surcharges", "selectDisplay": "[name]", "type": "objectIds", "objectOverflow": true}, "vendor_id": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "objectType": "vendors", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}}, "blueprint_name": "inventory_service", "blueprint_type": "object", "date_created": "2017-03-15 15:48:42.28155", "id": 286, "instance": "<PERSON><PERSON><PERSON><PERSON>"}