{"blueprint": {"id": {"immutable": false, "name": "id", "type": "int"}, "active": {"immutable": false, "name": "Active", "options": ["Yes", "No"], "type": "select"}, "breakdown": {"immutable": false, "name": "Pricing Breakdown", "type": "object"}, "client": {"immutable": false, "name": "Client", "type": "objectId", "objectType": "companies"}, "event_date": {"immutable": false, "name": "Event Date", "type": "date"}, "is_template": {"immutable": false, "name": "Is template", "type": "int"}, "menu": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}, "space": {"immutable": false, "name": "Space (project, team, etc.)", "type": "objectId", "objectType": "groups"}, "total": {"immutable": false, "name": "Total", "type": "int"}}, "blueprint_name": "inventory_menu_pricing_breakdown", "blueprint_type": "object", "date_created": "2020-14-20 00:00:00.000000", "id": 37, "instance": "<PERSON><PERSON><PERSON><PERSON>"}