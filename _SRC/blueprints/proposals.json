{"blueprint": {"approval_notes": {"immutable": false, "name": "Approval Notes", "type": "object"}, "contract": {"immutable": false, "name": "Contracts", "objectType": "contracts", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "end_date": {"immutable": false, "name": "End Date", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "invoices": {"immutable": false, "name": "Invoices", "objectType": "invoices", "selectDisplay": "", "type": "objectIds"}, "is_deleted": {"immutable": true, "name": "Is Deleted", "options": ["No", "Yes"], "type": "select"}, "is_template": {"immutable": false, "name": "Is template?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "main_object": {"immutable": false, "name": "Main Contact", "objectType": "contacts", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "manager": {"immutable": false, "name": "Manager", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "menu": {"immutable": false, "name": "<PERSON><PERSON>", "objectType": "inventory_menu", "selectDisplay": "", "type": "objectId"}, "invoice_template": {"immutable": false, "name": "Selected Invoice Template", "objectType": "payment_schedule_template", "selectDisplay": "[name]", "type": "objectId"}, "schedule_template": {"immutable": false, "name": "Selected Schedule Template", "objectType": "categories", "selectDisplay": "[name]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "pricing": {"immutable": false, "name": "Pricing", "type": "object"}, "schedule": {"immutable": false, "name": "Schedule", "objectType": "staff_schedules", "selectDisplay": "", "type": "objectId"}, "sections": {"immutable": false, "name": "Sections", "type": "object"}, "start_date": {"immutable": false, "name": "Start Date", "type": "date"}, "status": {"immutable": false, "name": "Status", "objectType": "select", "options": ["Editing", "Proposal", "Admin Review", "Approved", "Declined", "Active", "Client Review", "Accepted", "Signed", "Paid", "Complete"], "type": "select"}, "vendors": {"immutable": false, "name": "Vend<PERSON>", "objectType": "event_vendors", "selectDisplay": "[name]", "type": "objectId"}, "venue": {"immutable": false, "name": "Venue", "objectType": "staff_base", "selectDisplay": "[name]", "type": "objectId"}}, "blueprint_name": "proposals", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 47, "instance": "<PERSON><PERSON><PERSON><PERSON>"}