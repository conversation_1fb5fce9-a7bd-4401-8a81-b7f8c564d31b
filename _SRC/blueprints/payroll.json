{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname][lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "flat_rate": {"immutable": false, "name": "Flat rate", "type": "usd"}, "hourly_rate": {"immutable": false, "name": "Hourly rate", "type": "usd"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname][lname]", "type": "objectId"}, "max_flat_hours": {"immutable": false, "name": "Max flat hours", "type": "number"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "pay_style": {"immutable": false, "max_flat_hours": {"immutable": false, "name": "Max Hours for Flat Rate", "type": "float"}, "name": "Pay Style", "options": {"flat": "Flat", "flat_and_hourly": "Flat + Hourly", "hourly": "Hourly", "salary": "Salary"}, "type": "select"}, "rate": {"immutable": false, "name": "Rate", "type": "usd"}, "service": {"immutable": false, "name": "Service", "objectType": "inventory_service", "selectDisplay": "[name]", "type": "objectId"}, "staff": {"immutable": false, "name": "Staff", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}}, "blueprint_name": "payroll", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 31, "instance": "<PERSON><PERSON><PERSON><PERSON>"}