{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "name": {"immutable": false, "name": "Name", "notEmpty": true, "type": "string"}, "steps": {"blueprint": {"name": {"immutable": false, "name": "Step Name", "type": "text"}}, "immutable": false, "name": "Steps", "type": "list"}, "module_id": {"immutable": false, "name": "Module Id", "type": "text"}, "is_viewed": {"immutable": false, "name": "Viewed", "type": "int"}, "user": {"immutable": false, "name": "User", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}}, "blueprint_name": "walkthrough", "blueprint_type": "object", "date_created": "2019-03-28 21:46:50.920443", "id": 29, "instance": "<PERSON><PERSON><PERSON><PERSON>"}