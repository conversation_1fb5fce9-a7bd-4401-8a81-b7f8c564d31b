{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string", "fieldType": "plain-text"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "name": {"immutable": false, "name": "Name", "type": "string", "fieldType": "title"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "source_type": {"immutable": false, "name": "Source Type", "type": "object"}, "states": {"blueprint": {"allowAllTransitions": {"immutable": false, "name": "Allow all transitions?", "type": "bool"}, "color": {"immutable": false, "name": "Name", "type": "text"}, "icon": {"immutable": false, "name": "Icon", "type": "text"}, "isEntryPoint": {"immutable": false, "name": "Is entry point", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "text"}, "next": {"immutable": false, "name": "Next states", "type": "object"}, "previous": {"immutable": false, "name": "Previous states", "type": "object"}, "requiresConfirmation": {"immutable": false, "name": "Require confirmation from the user when transitioning to this state?", "type": "bool"}, "type": {"immutable": false, "name": "Type", "type": "text"}, "uid": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}, "tags": {"immutable": false, "name": "Tags", "type": "objectIds"}, "message": {"immutable": false, "name": "Message", "type": "text"}, "weight": {"immutable": false, "name": "Weight", "type": "float"}}, "immutable": false, "name": "States", "type": "list"}}, "blueprint_name": "entity_workflow", "blueprint_type": "object", "date_created": "2019-06-13 19", "instance": "<PERSON><PERSON><PERSON><PERSON>"}