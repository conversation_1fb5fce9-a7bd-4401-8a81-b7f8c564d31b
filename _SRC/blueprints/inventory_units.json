{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "measurements": {"blueprint": {"base_reference": {"immutable": false, "name": "Base Unit", "type": "int"}, "divisor": {"immutable": false, "name": "Divisor", "type": "float"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "multiplier": {"immutable": false, "name": "Multiplier", "type": "float"}, "name": {"immutable": false, "name": "Name", "type": "string"}}, "immutable": false, "name": "Units", "type": "list"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "inventory_units", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.436148", "id": 261, "instance": "<PERSON><PERSON><PERSON><PERSON>"}