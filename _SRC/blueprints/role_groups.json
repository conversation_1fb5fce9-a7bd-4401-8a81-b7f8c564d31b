{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "role_type": {"immutable": false, "name": "Role name", "type": "text"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "require_approval": {"immutable": false, "name": "Require Approval", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "approval_jobs": {"immutable": false, "name": "Service", "objectType": "inventory_service", "selectDisplay": "[name]", "type": "objectIds"}, "approval_users": {"immutable": false, "name": "Approval users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}}, "blueprint_name": "role_groups", "blueprint_type": "object", "date_created": "2021-07-08 21:46:50.920443", "id": 523, "instance": "<PERSON><PERSON><PERSON><PERSON>"}