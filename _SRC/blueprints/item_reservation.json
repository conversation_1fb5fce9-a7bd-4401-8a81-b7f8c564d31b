{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "current_checkpoint": {"immutable": false, "name": "Current checkpoint", "type": "int"}, "date_created": {"immutable": true, "name": "Last Updated", "type": "date"}, "date_reserved": {"immutable": false, "name": "Date Reserved", "type": "date"}, "details": {"immutable": false, "name": "Details", "type": "string"}, "dropped_off": {"immutable": false, "name": "Dropped Off", "type": "boolean"}, "dropped_off_by": {"immutable": true, "name": "Dropped Off By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "dropped_off_date": {"immutable": false, "name": "Dropped Off Timestamp", "type": "date"}, "end_date": {"immutable": false, "name": "End Date", "type": "date"}, "filled": {"immutable": false, "name": "Filled", "type": "int"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "ingredient": {"immutable": false, "name": "Ingredient", "type": "int"}, "inventory_group": {"immutable": false, "name": "Inventory Group", "objectType": "inventory_groups", "selectDisplay": "[name]", "type": "objectId"}, "inventory_item_ids": {"immutable": false, "name": "Inventory Item Ids", "type": "object"}, "is_active": {"immutable": false, "name": "Is Active", "type": "int"}, "item": {"immutable": true, "name": "<PERSON><PERSON>", "type": "int"}, "item_category": {"immutable": false, "name": "Item Category", "type": "int"}, "items": {"blueprint": {"inventory_item": {"immutable": false, "name": "Inventory Item", "objectType": "inventory_items", "selectDisplay": "[received_date] - [serial_number]", "type": "int"}, "location": {"immutable": false, "name": "Original location", "type": "int"}, "qty": {"immutable": false, "name": "Quantity (in base units)", "type": "float"}}, "immutable": false, "name": "Items", "type": "list"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "location": {"immutable": false, "name": "Location", "objectType": "staff_base", "selectDisplay": "[name]", "type": "objectId"}, "menu": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}, "menu_item": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}, "menu_item_choice": {"immutable": false, "name": "<PERSON>u Item Choice", "type": "int"}, "menu_section": {"immutable": false, "name": "Menu Section", "type": "int"}, "picked_up": {"immutable": false, "name": "Picked Up", "type": "boolean"}, "picked_up_by": {"immutable": true, "name": "Picked Up By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "picked_up_date": {"immutable": false, "name": "Picked Up Timestamp", "type": "date"}, "quantity": {"immutable": false, "name": "Quantity", "type": "float"}, "quantity_filled": {"immutable": false, "name": "Quantity filled", "type": "float"}, "related": {"immutable": false, "name": "Related Object (event id)", "type": "relatedObject"}, "stages": {"immutable": false, "name": "Stages", "type": "object"}, "start_date": {"immutable": false, "name": "Start Date", "type": "date"}, "status": {"immutable": false, "name": "Status", "options": {"at_venue": "At venue", "could_not_reserve": "Could Not Reserve", "in_transit": "In Transit", "reserved": "Reserved"}, "type": "select"}, "type": {"immutable": false, "name": "Type", "options": {"rental": "Rental", "untracked": "Untracked", "use": "Use"}, "type": "select"}, "unit_type": {"immutable": false, "name": "Unit type", "type": "string"}, "units": {"immutable": false, "name": "Units", "type": "int"}}, "blueprint_name": "item_reservation", "blueprint_type": "object", "date_created": "2017-06-20 14:37:30.079759", "id": 310, "instance": "<PERSON><PERSON><PERSON><PERSON>"}