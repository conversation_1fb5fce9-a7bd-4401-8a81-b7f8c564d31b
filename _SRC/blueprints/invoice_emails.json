{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "subject": {"immutable": false, "name": "Subject", "type": "string"}, "body": {"immutable": false, "name": "Body", "type": "string"}, "beforeAfter": {"immutable": false, "name": "Before or After", "type": "string"}, "everyday": {"immutable": false, "name": "Everyday", "type": "string"}, "days": {"immutable": false, "name": "Days", "type": "int"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "invoice_fees", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 39, "instance": "<PERSON><PERSON><PERSON><PERSON>"}