{"blueprint": {"active": {"immutable": false, "name": "Active", "options": ["Yes", "No"], "type": "select"}, "created_by": {"immutable": false, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "owner": {"immutable": false, "name": "Owner", "objectType": "users", "selectDisplay": "[fname] [lname]", "objectOverflow": true, "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_type": {"immutable": false, "name": "Object Type", "type": "string"}, "object_uid": {"immutable": false, "name": "Object Id", "type": "int"}, "related_file": {"immutable": false, "name": "Related File", "type": "relatedObject"}, "related_object": {"immutable": true, "name": "Related Object", "type": "relatedObject"}, "queue": {"immutable": false, "name": "Queue", "type": "object"}}, "blueprint_name": "pdfs", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.444338", "instance": "<PERSON><PERSON><PERSON><PERSON>"}