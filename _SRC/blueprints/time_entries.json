{"blueprint": {"billing_type": {"immutable": false, "name": "Billing Type", "options": {"flat": "Flat", "flat_and_hourly": "Flat + Hourly", "hourly": "Hourly", "non_billable": "Non-Billable", "salary": "Salary"}, "type": "select"}, "can_be_edited": {"immutable": false, "name": "Can be edited", "type": "boolean"}, "compensation": {"immutable": false, "name": "Compensation", "type": "usd"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "cycle_type": {"immutable": false, "name": "Cycle Type", "options": {"15th_and_last_day": "15th and last day", "1st_and_15th": "1st and 15th", "bi_weekly": "Bi Weekly", "monthly": "Monthly", "weekly": "Weekly"}, "type": "select"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "default_payroll": {"immutable": false, "name": "De<PERSON>ult Payroll", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "duration": {"immutable": false, "name": "Duration", "type": "int"}, "end_date": {"immutable": false, "name": "End Date", "type": "date"}, "flat_rate": {"immutable": false, "name": "Flat Rate", "type": "usd"}, "field_name": {"immutable": false, "name": "Field Name", "type": "string"}, "hourly_rate": {"immutable": false, "name": "Hourly Rate", "type": "usd"}, "rate": {"immutable": false, "name": "Rate", "type": "usd"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_correction": {"immutable": true, "name": "Is correction", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "is_salary": {"immutable": false, "name": "Is salary", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "location": {"immutable": false, "name": "Location", "objectType": "staff_base", "selectDisplay": "[name]", "type": "objectId"}, "max_flat_hours": {"immutable": false, "name": "Max Hours for Flat Rate", "type": "float"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "note": {"immutable": false, "name": "Note", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "payment_report": {"immutable": false, "name": "Payment Report", "objectType": "payment_report", "selectDisplay": "[name]", "type": "objectId"}, "service": {"immutable": false, "name": "Service", "objectType": "inventory_service", "selectDisplay": "[name]", "type": "objectId"}, "shift": {"immutable": false, "name": "Shift", "objectType": "groups", "selectDisplay": "[id]", "type": "objectId", "searchAny": true}, "staff": {"immutable": false, "name": "Staff", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "start_date": {"immutable": false, "name": "Start Date", "type": "date"}, "tips": {"immutable": false, "name": "Tips", "type": "usd"}}, "blueprint_name": "time_entries", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.43975", "id": 264, "instance": "<PERSON><PERSON><PERSON><PERSON>"}