{"blueprint": {"allowed_users": {"immutable": false, "name": "Allowed Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "can_be_reassigned": {"immutable": false, "name": "Can Be Reassigned", "options": ["Yes", "No"], "type": "select"}, "color": {"immutable": false, "name": "Color", "type": "text"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "entityType": {"immutable": true, "name": "Entity Type", "type": "objectId", "objectType": "entity_type"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "managers": {"immutable": false, "name": "Managers", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "name": {"immutable": true, "name": "Name", "type": "string"}, "settings": {"immutable": false, "name": "Settings", "type": "object"}, "type": {"immutable": true, "name": "Type", "type": "string"}, "user": {"immutable": false, "name": "User", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "users": {"immutable": false, "name": "Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}}, "blueprint_name": "entity_tool", "blueprint_type": "object", "date_created": "2019-06-20 19", "instance": "<PERSON><PERSON><PERSON><PERSON>"}