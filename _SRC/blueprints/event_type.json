{"blueprint": {"availableToUser": {"type": "bool", "name": "Available to User", "immutable": false}, "allowed_users": {"immutable": false, "name": "Allowed Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string", "fieldType": "plain-text"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "name": {"immutable": false, "name": "Name", "type": "string", "fieldType": "title"}, "next": {"name": "Next", "type": "object", "immutable": false, "blueprint": {"type": {"name": "Type", "type": "select", "options": {"event_type": "Action", "condition": "Condition", "state": "Workflow State"}, "immutable": false}, "pointer": {"name": "Points to", "type": "int", "immutable": false}}}, "object": {"immutable": false, "name": "Object", "type": "int"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "options": {"name": "Options", "type": "object", "immutable": false}, "previous": {"name": "Previous", "type": "object", "immutable": false, "blueprint": {"type": {"name": "Type", "type": "select", "options": {"event_type": "Action", "condition": "Condition", "state": "Workflow State"}, "immutable": false}, "pointer": {"name": "Points to", "type": "int", "immutable": false}}}, "requires_input": {"type": "bool", "name": "Requires input", "immutable": false}, "trigger": {"name": "<PERSON><PERSON>", "type": "select", "immutable": false, "options": {"stateChange": "State Change", "clientCommentPosted": "Client Comment Posted", "spaceSchedule": "Schedules on Spaces", "workflowSchedule": "Schedules on Workflow"}}, "type": {"name": "Type", "type": "string", "immutable": false}, "state": {"name": "State", "type": "int", "immutable": false}, "user": {"immutable": false, "name": "User", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "users": {"immutable": false, "name": "Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}}, "blueprint_name": "event_type", "blueprint_type": "object", "date_created": "2019-06-03 19", "instance": "<PERSON><PERSON><PERSON><PERSON>"}