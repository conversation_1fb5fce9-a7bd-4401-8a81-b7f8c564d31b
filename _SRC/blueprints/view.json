{"blueprint": {"description": {"immutable": false, "name": "Description", "type": "string", "fieldType": "plain-text"}, "conditional_formats": {"immutable": false, "name": "Conditional Formats", "type": "list", "blueprint": {"conditions": {"immutable": false, "name": "Condition", "type": "object"}, "style": {"type": "select", "immutable": false, "name": "Format/Style", "options": {"hidden": "Hidden"}}, "fields": {"type": "object", "name": "Fields", "immutable": false}}}, "blueprint": {"name": "Fields", "type": "map", "immutable": false, "blueprint": {"name": {"name": "Name", "immutable": false, "type": "string"}, "immutable": {"name": "Is immutable", "immutable": false, "type": "bool"}, "type": {"name": "Type", "immutable": false, "type": "string"}, "fieldType": {"name": "Field Type", "immutable": false, "type": "string"}, "format": {"name": "Formatting", "immutable": false, "type": "object"}, "options": {"name": "Options", "immutable": false, "type": "object"}, "permissions": {"name": "Permissions", "immutable": false, "type": "object"}, "select": {"name": "Selection", "immutable": false, "type": "object"}, "index": {"name": "Index", "immutable": false, "type": "int"}, "workflow": {"name": "Workflow", "immutable": false, "type": "objectId", "objectType": "entity_workflow"}, "is_archived": {"name": "Is archived", "immutable": false, "type": "bool"}}}, "hideFrom": {"immutable": false, "name": "Hide from these subsets", "type": "object"}, "icon": {"name": "Icon", "type": "string", "fieldType": "icon", "immutable": false}, "id": {"immutable": true, "name": "ID", "type": "int"}, "index": {"name": "Index", "immutable": false, "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "name": {"immutable": false, "name": "Name", "type": "string", "fieldType": "title"}, "set": {"immutable": false, "name": "Set", "type": "string", "fieldType": "title"}, "type": {"immutable": false, "name": "Type", "options": {"form": "Form"}, "type": "select"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "workflow": {"immutable": true, "name": "Workflow", "objectType": "entity_workflow", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "state": {"immutable": true, "name": "State", "type": "int"}, "isVisible": {"name": "Is visible?", "immutable": false, "type": "bool"}}, "blueprint_name": "view", "blueprint_type": "object", "date_created": "2020-06-17", "instance": "<PERSON><PERSON><PERSON><PERSON>"}