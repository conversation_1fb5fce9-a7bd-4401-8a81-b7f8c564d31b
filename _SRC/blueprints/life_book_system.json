{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "new_order": {"immutable": false, "name": "New Order Notification", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectIds"}, "new_web_form": {"immutable": false, "name": "New Website Form Submission Notification", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectIds"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "life_book_system", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 21, "instance": "<PERSON><PERSON><PERSON><PERSON>"}