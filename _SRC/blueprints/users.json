{"blueprint": {"address": {"immutable": false, "name": "Address", "objectOverflow": true, "objectType": "contact_info", "selectDisplay": "[type]", "type": "objectId"}, "base": {"immutable": false, "name": "Location", "objectType": "staff_base", "selectDisplay": "[name]", "type": "objectIds"}, "city": {"immutable": false, "name": "City", "type": "string"}, "color": {"immutable": false, "name": "Color", "type": "text"}, "company_hired_to": {"immutable": false, "name": "Company Hired To", "objectOverflow": true, "objectType": "companies", "selectDisplay": "[name]", "type": "objectId"}, "country": {"immutable": false, "name": "Country", "type": "string"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "daily_requests": {"immutable": true, "name": "Daily Requests", "type": "int"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": false, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "dependents": {"immutable": false, "name": "Dependents", "type": "int"}, "dob": {"immutable": false, "name": "Date of Birth", "type": "date"}, "doc_link": {"immutable": false, "name": "Document Link", "type": "string"}, "doc_signature": {"immutable": false, "name": "Document Signature", "type": "string"}, "email": {"immutable": false, "name": "Email", "type": "string"}, "enabled": {"immutable": false, "name": "Enabled", "type": "int"}, "go_to_hq": {"immutable": false, "name": "Go to HQ", "type": "bool"}, "filing_status": {"immutable": false, "name": "Filing Status", "options": {"married": "Married", "single": "Single", "single_rate": "Married (Single Rate)"}, "type": "select"}, "fname": {"immutable": false, "name": "First Name", "type": "string"}, "garnishments": {"immutable": false, "name": "Garnishment Amount", "type": "usd"}, "hire_date": {"immutable": false, "name": "Date of Hire", "type": "date"}, "hours_worked": {"immutable": true, "name": "Hours Worked", "type": "int"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "lname": {"immutable": false, "name": "Last Name", "type": "string"}, "nick_name": {"immutable": false, "name": "<PERSON>", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "overlap": {"immutable": false, "name": "Can be assigned to overlapping shifts?", "type": "int"}, "parent": {"immutable": true, "name": "Parent", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "password": {"immutable": false, "name": "Password", "type": "string"}, "payroll": {"immutable": false, "name": "Payroll", "objectType": "payroll", "selectDisplay": "[id]", "type": "objectIds"}, "phone": {"immutable": false, "name": "Phone", "type": "string"}, "pin": {"immutable": false, "name": "Pin Number", "type": "string"}, "profile_image": {"immutable": false, "name": "Profile Image", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "profiles": {"immutable": false, "name": "Profiles", "objectType": "user_views", "selectDisplay": "[name]", "type": "objectIds"}, "rate": {"immutable": false, "name": "Rate", "type": "int"}, "related_object": {"immutable": true, "name": "Related Object", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "service": {"immutable": false, "name": "Service", "objectType": "inventory_service", "selectDisplay": "[name]", "type": "objectIds"}, "sick_days": {"immutable": false, "name": "Sick Days", "objectType": "time_off_vesting_schedule", "type": "objectId"}, "ssn": {"immutable": false, "name": "Social Security #", "type": "string"}, "state": {"immutable": false, "name": "State", "type": "string"}, "status": {"immutable": false, "name": "Employee Status", "objectType": "staff_status", "selectDisplay": "[name]", "type": "objectId"}, "street": {"immutable": false, "name": "Street", "type": "string"}, "termination_date": {"immutable": false, "name": "Termination Date", "type": "date"}, "type": {"immutable": false, "name": "User Type", "options": {"admin": "Admin", "contacts": "Client", "developer": "Developer", "staff": "Staff", "owner": "Owner"}, "type": "select"}, "user_views": {"immutable": true, "name": "User Views", "objectType": "user_views", "selectDisplay": "[name]", "type": "objectIds"}, "vacation_days": {"immutable": false, "name": "Vacation Days", "objectType": "time_off_vesting_schedule", "type": "objectId"}, "work_email": {"immutable": false, "name": "Work Email", "type": "string"}, "work_phone": {"immutable": false, "name": "Work Phone", "type": "string"}, "work_week_start": {"immutable": false, "name": "Work Week Start", "options": {"friday": "Friday", "monday": "Monday", "saturday": "Saturday", "sunday": "Sunday", "thursday": "Thursday", "tuesday": "Tuesday", "wednesday": "Wednesday"}, "type": "select"}, "zip": {"immutable": false, "name": "Zip", "type": "string"}, "canBeNotified": {"immutable": false, "name": "Can be notified?", "type": "bool"}}, "blueprint_name": "users", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.443227", "id": 289, "instance": "<PERSON><PERSON><PERSON><PERSON>"}