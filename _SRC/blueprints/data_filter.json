{"blueprint": {"description": {"immutable": false, "name": "Description", "type": "string", "fieldType": "plain-text"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "index": {"name": "Index", "immutable": false, "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "name": {"immutable": false, "name": "Name", "type": "string", "fieldType": "title"}, "object_type": {"name": "Object Type", "type": "string", "fieldType": "icon", "immutable": false}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "parent_object_type": {"name": "Parent Object Type", "type": "string", "fieldType": "icon", "immutable": false}, "shared_with_instances": {"immutable": false, "name": "Shared with Instances", "type": "object"}, "where": {"immutable": false, "name": "Conditional Formats", "type": "object"}}, "blueprint_name": "data_filter", "blueprint_type": "object", "date_created": "2021-07-13", "instance": "<PERSON><PERSON><PERSON><PERSON>"}