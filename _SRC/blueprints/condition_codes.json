{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "id": {"immutable": true, "name": "id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "recommendations": {"immutable": false, "name": "Recommendations", "objectType": "service_ticket_recommendations", "selectDisplay": "[recommendation]", "type": "objectIds"}}, "blueprint_name": "condition_codes", "blueprint_type": "object", "date_created": "2017-06-04 11:14:54.600947", "id": 299, "instance": "<PERSON><PERSON><PERSON><PERSON>"}