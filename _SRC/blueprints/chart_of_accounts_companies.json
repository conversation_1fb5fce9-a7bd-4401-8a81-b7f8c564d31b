{"blueprint": {"child_ids": {"immutable": true, "name": "Children", "type": "string"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "parent_id": {"immutable": true, "name": "Parent", "type": "int"}}, "blueprint_name": "chart_of_accounts_companies", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 26, "instance": "<PERSON><PERSON><PERSON><PERSON>"}