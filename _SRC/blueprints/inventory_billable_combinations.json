{"blueprint": {"category": {"immutable": false, "name": "Category", "objectType": "inventory_billable_combination_categories", "selectDisplay": "[name]", "type": "objectId"}, "chart_of_account": {"immutable": true, "name": "Chart of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "default_pricing_option": {"immutable": false, "name": "Pricing Style", "options": {"price": "Price", "price_per_hour": "Price per hour", "price_per_hour_per_person": "Price per hour per person", "price_per_person": "Price per person"}, "type": "select"}, "default_qty_option": {"immutable": false, "name": "Quantity Style", "options": {"absolute": "Absolute", "per_guest": "Per guest", "per_hour": "Per hour", "per_hour_per_guest": "Per hour per guest"}, "type": "select"}, "default_tax_rate": {"immutable": false, "name": "Default Tax Rate", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "image": {"immutable": false, "name": "Image", "objectType": "file_meta_data", "selectDisplay": "file_name", "type": "objectId"}, "is_deleted": {"immutable": false, "name": "Is deleted?", "type": "int"}, "is_hidden_from_menu_selections": {"immutable": false, "name": "Is hidden from menu selections?", "type": "int"}, "item_yield": {"measurement": {"immutable": false, "name": "Measurement", "type": "int"}, "quantity": {"immutable": false, "name": "Quantity", "type": "float"}, "servings": {"immutable": false, "name": "Servings", "type": "int"}, "unit_type": {"immutable": false, "name": "Unit type", "options": {"dry_volume": "Dry volume", "liquid_volume": "Liquid volume", "quantity": "Quantity", "weight": "Weight"}, "type": "select"}}, "items": {"blueprint": {"choices": {"blueprint": {"additional_price": {"immutable": false, "name": "Additional Price", "type": "usd"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "divisor": {"immutable": false, "name": "Divisor", "type": "float"}, "inventory_group": {"immutable": false, "name": "Inventory Group", "type": "relatedObject"}, "multiplier": {"immutable": false, "name": "Multiplier", "type": "float"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "qty": {"measurement": {"immutable": false, "name": "Measurement", "type": "int"}, "quantity": {"immutable": false, "name": "Quantity", "type": "float"}, "unit_type": {"immutable": false, "name": "Unit type", "options": {"dry_volume": "Dry volume", "liquid_volume": "Liquid volume", "quantity": "Quantity", "weight": "Weight"}, "type": "select"}}, "units": {"immutable": false, "name": "Units", "type": "int"}}, "immutable": false, "name": "choices", "type": "list"}, "divisor": {"immutable": false, "name": "Divisor", "type": "float"}, "id": {"immutable": false, "name": "ID", "type": "int"}, "inventory_group": {"immutable": false, "name": "Inventory Group", "type": "relatedObject"}, "max_selections": {"immutable": false, "name": "Max Selections", "type": "int"}, "multiplier": {"immutable": false, "name": "Multiplier", "type": "float"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "qty": {"measurement": {"immutable": false, "name": "Measurement", "type": "int"}, "quantity": {"immutable": false, "name": "Quantity", "type": "float"}, "unit_type": {"immutable": false, "name": "Unit type", "options": {"dry_volume": "Dry volume", "liquid_volume": "Liquid volume", "quantity": "Quantity", "weight": "Weight"}, "type": "select"}}, "units": {"immutable": false, "name": "Units", "type": "int"}}, "immutable": false, "name": "Items", "type": "list"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "measurement": {"immutable": false, "name": "Measurement", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "price": {"immutable": false, "name": "Price Per Unit", "type": "usd"}, "price_per_hour": {"immutable": false, "name": "Price Per Hour", "type": "usd"}, "price_per_hour_per_person": {"immutable": false, "name": "Price Per Hour Per Person", "type": "usd"}, "price_per_person": {"immutable": false, "name": "Price Per Person", "type": "usd"}, "quantity": {"immutable": false, "name": "Quantity", "type": "float"}, "selection": {"immutable": false, "name": "Selection", "type": "object"}, "tax_rates": {"immutable": false, "name": "Tax Rates", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "units": {"immutable": false, "name": "Units", "objectType": "inventory_units", "selectDisplay": "[name]", "type": "objectId"}, "uom": {"blueprint": {"config": {"blueprint": {"dry_volume": {"immutable": false, "name": "Dry volume", "type": "int"}, "liquid_volume": {"immutable": false, "name": "Liquid volume", "type": "int"}, "quantity": {"immutable": false, "name": "Quantity", "type": "int"}, "volume_to_weight": {"immutable": false, "name": "Volume to weight ratio", "type": "float"}, "weight": {"immutable": false, "name": "Weight", "type": "int"}}, "immutable": false, "name": "Configuration", "type": "object"}, "dry_volume": {"blueprint": {"default_shipment_measurement": {"immutable": false, "name": "Default shipment measurement", "type": "int"}}, "immutable": false, "name": "Dry volume", "type": "object"}, "liquid_volume": {"blueprint": {"default_shipment_measurement": {"immutable": false, "name": "Default shipment measurement", "type": "int"}}, "immutable": false, "name": "Liquid volume", "type": "object"}, "quantity": {"blueprint": {"default_shipment_measurement": {"immutable": false, "name": "Default shipment measurement", "type": "int"}}, "immutable": false, "name": "Quantity", "type": "object"}, "weight": {"blueprint": {"default_shipment_measurement": {"immutable": false, "name": "Default shipment measurement", "type": "int"}}, "immutable": false, "name": "Weight", "type": "object"}}, "immutable": false, "name": "Units of measure", "type": "object"}}, "blueprint_name": "inventory_billable_combinations", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 33, "instance": "<PERSON><PERSON><PERSON><PERSON>"}