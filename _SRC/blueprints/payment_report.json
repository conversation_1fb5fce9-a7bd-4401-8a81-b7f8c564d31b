{"blueprint": {"cycle_start": {"immutable": false, "name": "Cycle Start", "type": "date"}, "cycle_type": {"immutable": false, "name": "Cycle Type", "options": {"15th_and_last_day": "15th and last day", "1st_and_15th": "1st and 15th", "bi_weekly": "Bi Weekly", "monthly": "Monthly", "weekly": "Weekly"}, "type": "select"}, "end_date": {"immutable": false, "name": "End Date", "type": "date"}, "fiscal_year_start": {"immutable": false, "name": "Fiscal Year Start", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "start_date": {"immutable": false, "name": "Start Date", "type": "date"}, "total_price": {"immutable": false, "name": "Total Price", "type": "usd"}}, "blueprint_name": "payment_report", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 56, "instance": "<PERSON><PERSON><PERSON><PERSON>"}