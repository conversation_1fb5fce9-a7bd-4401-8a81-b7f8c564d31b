{"blueprint": {"available_for_pick_up": {"immutable": false, "name": "Is Available?", "options": ["Yes", "No"], "type": "select"}, "allowed_users": {"immutable": false, "name": "Allowed Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "can_be_reassigned": {"immutable": false, "name": "Can Be Reassigned", "options": ["Yes", "No"], "type": "select"}, "client_priority": {"immutable": false, "name": "Client Priority", "type": "int"}, "color": {"immutable": false, "name": "Color", "type": "text"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "cycle": {"immutable": false, "name": "Cycle", "options": {"daily": "Daily", "monthly": "Monthly", "weekly": "Weekly", "yearly": "Yearly"}, "type": "select"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "date_booked": {"immutable": false, "name": "Date Booked", "type": "date"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "details": {"immutable": false, "name": "Details", "type": "string"}, "end_date": {"immutable": false, "name": "End date", "type": "date"}, "fieldType": {"immutable": true, "name": "Field Type", "type": "string", "value": "State"}, "group_type": {"immutable": false, "name": "Type", "options": ["Headquarters", "Team", "Project", "Task", "Schedule", "Shift", "MyStuff", "JobType"], "type": "select"}, "head_count": {"immutable": false, "name": "Head count", "type": "int"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "initial": {"immutable": false, "name": "Initial", "type": "int"}, "invoice_value": {"immutable": false, "name": "Invoice Value", "type": "int"}, "invoice_value_no_taxes": {"immutable": false, "name": "Invoice Value without taxes", "type": "int"}, "is_active": {"immutable": false, "name": "Active", "options": ["Yes", "No"], "type": "select"}, "is_ongoing": {"immutable": false, "name": "Is ongoing?", "type": "int"}, "is_recurring": {"immutable": false, "name": "Is recurring?", "type": "int"}, "job_type": {"immutable": false, "name": "Job type", "objectType": "inventory_service", "selectDisplay": "name", "type": "objectId", "objectOverflow": true}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "location": {"immutable": false, "name": "Location", "objectType": "staff_base", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "locations": {"immutable": false, "name": "Locations", "objectType": "staff_base", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "main_contact": {"immutable": false, "name": "Main contact", "objectType": "contacts", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true, "on": {"update": {"updateOwner": {}}}}, "owner": {"immutable": false, "name": "Owner", "objectType": "users", "selectDisplay": "[fname] [lname]", "objectOverflow": true, "type": "objectId"}, "main_client": {"immutable": false, "name": "Main Client", "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true, "type": "objectId"}, "managers": {"immutable": false, "name": "Managers", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "sales_managers": {"immutable": false, "name": "Sales Managers", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "parent": {"immutable": false, "name": "Parent", "objectType": "groups", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "parent_description": {"immutable": false, "name": "Description", "type": "string"}, "priority": {"immutable": false, "name": "Priority", "type": "int"}, "potential_value": {"immutable": false, "name": "Potential Value", "type": "int"}, "project_lead": {"immutable": false, "name": "Project lead", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "proposal": {"immutable": true, "name": "Proposal", "objectType": "proposals", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "reimburses_vacation_day": {"immutable": false, "name": "Reimburses Vacation Day", "type": "int"}, "related_contacts": {"immutable": false, "name": "Related contacts", "objectType": "contacts", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "repeat_end_date": {"immutable": false, "name": "Repeat end date", "type": "date"}, "repeat_forever": {"immutable": false, "name": "Should repeat forever", "type": "int"}, "schedule_options": {"immutable": false, "name": "Schedule optoins", "type": "objectIds", "objectOverflow": true}, "source_type": {"immutable": false, "name": "Source Type", "type": "object"}, "start_date": {"immutable": false, "name": "Start date", "type": "date"}, "state": {"immutable": false, "name": "Status", "type": "int", "fieldType": "state", "workflow": "type", "on": {"update": {"applyTags": {"from": "state", "type": "type"}}}}, "status": {"immutable": false, "name": "Comp Status", "objectType": "select", "options": {"accepted": "Accepted", "active": "Active", "complete": "Complete", "done": "Done", "preparing": "Preparing", "open": "Open", "onHold": "On Hold", "pending": "Pending", "actionNeeded": "Action Needed", "notified": "Notified", "paid": "Paid", "proposal": "Proposal", "scheduled": "Scheduled", "signed": "Signed", "unscheduled": "Unscheduled"}, "type": "select"}, "state_updated_on": {"immutable": false, "name": "State updated on", "type": "date"}, "time_estimate": {"immutable": false, "name": "Time Estimate", "type": "int"}, "time_estimate_cumulative": {"immutable": false, "name": "Cumulative Time Estimate", "type": "int"}, "time_logged": {"immutable": false, "name": "Time Logged", "type": "int"}, "hourly_rate": {"immutable": false, "name": "Hourly Rate", "type": "usd"}, "rate": {"immutable": false, "name": "Rate", "type": "usd"}, "tools": {"blueprint": {"added_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "added_on": {"immutable": true, "name": "Date Created", "type": "string"}, "allowed_users": {"immutable": false, "name": "Allowed Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "display_name": {"immutable": false, "name": "Display Name", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_archieved": {"immutable": false, "name": "<PERSON>", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "main_contact": {"immutable": false, "name": "Main contact", "objectType": "contacts", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "order": {"immutable": false, "name": "Order", "type": "int"}, "potential_value": {"immutable": false, "name": "Potential Value", "type": "int"}, "related_contacts": {"immutable": false, "name": "Related contacts", "objectType": "contacts", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "settings": {"immutable": false, "name": "Settings", "type": "object"}, "system_name": {"immutable": false, "name": "System Name", "type": "string"}, "tip": {"immutable": false, "name": "Tip", "type": "string"}}, "immutable": false, "name": "Tools", "type": "list"}, "type": {"immutable": false, "name": "Type", "objectType": "project_types", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "category": {"immutable": false, "name": "Category", "objectType": "categories", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "user": {"immutable": false, "name": "User", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "users": {"immutable": false, "name": "Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}}, "blueprint_name": "groups", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 51, "instance": "<PERSON><PERSON><PERSON><PERSON>"}