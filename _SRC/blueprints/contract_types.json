{"blueprint": {"contract_object_type": {"immutable": false, "name": "Type", "options": {"projects": "Projects"}, "type": "select"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "notEmpty": true, "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "updated_by": {"immutable": true, "name": "Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}}, "blueprint_name": "contract_types", "blueprint_type": "object", "date_created": "2017-09-08 10:43:02.141192", "id": 316, "instance": "<PERSON><PERSON><PERSON><PERSON>"}