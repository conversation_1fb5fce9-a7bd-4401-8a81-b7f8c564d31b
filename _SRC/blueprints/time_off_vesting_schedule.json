{"blueprint": {"copied_from": {"immutable": false, "name": "Copied From", "type": "int"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "does_carry_over": {"immutable": false, "name": "Do days carry over?", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "initial_qty": {"immutable": false, "name": "Initial Quantity", "type": "float"}, "is_template": {"immutable": false, "name": "Is template?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "max_qty": {"immutable": false, "name": "Vacation Day Cap", "type": "float"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "qty_vested_per_period": {"immutable": false, "name": "Days Vested Every Vesting Period", "type": "float"}, "vesting_period_type": {"immutable": false, "name": "Vesting Period", "options": {"2_months": "2 Months", "3_months": "3 Months", "4_months": "4 Months", "6_months": "6 Months", "month": "Month", "year": "Year"}, "type": "select"}, "vesting_start_date": {"immutable": false, "name": "Vesting begins [x] after hire date", "options": {"2_months": "2 Months", "3_months": "3 Months", "4_months": "4 Months", "6_months": "6 Months", "immediately": "Immediately", "month": "1 Month", "year": "Year"}, "type": "select"}}, "blueprint_name": "time_off_vesting_schedule", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 53, "instance": "<PERSON><PERSON><PERSON><PERSON>"}