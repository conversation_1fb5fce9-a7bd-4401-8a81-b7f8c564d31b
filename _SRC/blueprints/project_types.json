{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_default_type": {"name": "Is default type", "type": "int", "immutable": false}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "onFirstSignature": {"immutable": false, "name": "On First Contract Signature", "type": "int"}, "onFirstFullPayment": {"immutable": false, "name": "On First Full Payment Made", "type": "int"}, "source_type": {"immutable": false, "name": "Source Type", "type": "object"}, "states": {"blueprint": {"allowAllTransitions": {"immutable": false, "name": "Allow all transitions?", "type": "bool"}, "color": {"immutable": false, "name": "Name", "type": "text"}, "icon": {"immutable": false, "name": "Icon", "type": "text"}, "isEntryPoint": {"immutable": false, "name": "Is entry point", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "text"}, "next": {"immutable": false, "name": "Next states", "type": "object"}, "previous": {"immutable": false, "name": "Previous states", "type": "object"}, "requiresConfirmation": {"immutable": false, "name": "Require confirmation from the user when transitioning to this state?", "type": "bool"}, "shouldTransitionOnTaskComplete": {"immutable": false, "name": "Move to next state when tasks are complete?", "type": "bool"}, "type": {"immutable": false, "name": "Type", "options": {"done": "Done", "deliveryPending": "Delivery Pending", "finalReview": "Final Review", "informationReceived": "Information Received", "actionNeeded": "Action Needed", "inProgress": "In Progress", "inReview": "In Review", "onHold": "On Hold", "open": "Open"}, "type": "select"}, "uid": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}, "tags": {"immutable": false, "name": "Tags", "type": "objectIds"}, "message": {"immutable": false, "name": "Message", "type": "text"}}, "immutable": false, "name": "States", "type": "list"}, "template": {"immutable": false, "name": "Type", "type": "objectId", "objectType": "groups"}, "type": {"immutable": false, "name": "Type", "type": "select", "options": {"event": "Event", "project": "Project"}}}, "blueprint_name": "project_types", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 46, "instance": "<PERSON><PERSON><PERSON><PERSON>"}