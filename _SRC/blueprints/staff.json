{"blueprint": {"address": {"immutable": false, "name": "Address", "objectOverflow": true, "objectType": "contact_info", "selectDisplay": "[type]", "type": "objectId"}, "base": {"immutable": false, "name": "Location", "objectType": "staff_base", "selectDisplay": "[name]", "type": "objectIds"}, "company_hired_to": {"immutable": false, "name": "Company Hired To", "objectOverflow": true, "objectType": "companies", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "daily_requests": {"immutable": true, "name": "Daily Requests", "type": "int"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "dependents": {"immutable": false, "name": "Dependents", "type": "int"}, "dob": {"immutable": false, "name": "Date of Birth", "type": "date"}, "email": {"immutable": false, "name": "Email", "type": "string"}, "enabled": {"immutable": false, "name": "Enabled", "type": "int"}, "filing_status": {"immutable": false, "name": "Filing Status", "options": {"married": "Married", "single": "Single", "single_rate": "Married (Single Rate)"}, "type": "select"}, "fname": {"immutable": false, "name": "First Name", "type": "string"}, "garnishments": {"immutable": false, "name": "Garnishment Amount", "type": "usd"}, "hire_date": {"immutable": false, "name": "Date of Hire", "type": "date"}, "hours_worked": {"immutable": true, "name": "Hours Worked", "type": "int"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "lname": {"immutable": false, "name": "Last Name", "type": "string"}, "nickname": {"immutable": false, "name": "Nickname", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "parent": {"immutable": true, "name": "Parent", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "password": {"immutable": true, "name": "Password", "type": "string"}, "payroll": {"immutable": false, "name": "Payroll", "objectType": "payroll", "selectDisplay": "[id]", "type": "objectIds"}, "phone": {"immutable": false, "name": "Phone", "type": "string"}, "pin": {"immutable": false, "name": "PIN Number", "type": "string"}, "service": {"immutable": false, "name": "Service", "objectType": "inventory_service", "selectDisplay": "[name]", "type": "objectIds"}, "ssn": {"immutable": false, "name": "Social Security #", "type": "string"}, "status": {"immutable": false, "name": "Employee Status", "objectType": "staff_status", "selectDisplay": "[name]", "type": "objectId"}, "stripe_id": {"immutable": false, "name": "Stripe ID", "type": "string"}, "termination_date": {"immutable": false, "name": "Termination Date", "type": "date"}, "user": {"immutable": false, "name": "User Object", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "user_views": {"immutable": true, "name": "User Views", "objectType": "user_views", "selectDisplay": "[name]", "type": "objectIds"}, "vacation_days": {"immutable": false, "name": "Vacation Days", "type": "int"}, "work_week_start": {"immutable": false, "name": "Work Week Start", "options": {"friday": "Friday", "monday": "Monday", "saturday": "Saturday", "sunday": "Sunday", "thursday": "Thursday", "tuesday": "Tuesday", "wednesday": "Wednesday"}, "type": "select"}}, "blueprint_name": "staff", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.400781", "id": 228, "instance": "<PERSON><PERSON><PERSON><PERSON>"}