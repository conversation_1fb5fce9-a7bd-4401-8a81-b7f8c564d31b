{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": false, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "message": {"immutable": false, "name": "Message", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "sentBy": {"immutable": false, "name": "<PERSON><PERSON>", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "sentTo": {"immutable": false, "name": "<PERSON><PERSON>", "type": "string"}, "type": {"immutable": false, "name": "Type", "type": "string"}, "typeId": {"immutable": false, "name": "Type ID", "type": "int"}}, "blueprint_name": "sms", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.453016", "id": 275, "instance": "<PERSON><PERSON><PERSON><PERSON>"}