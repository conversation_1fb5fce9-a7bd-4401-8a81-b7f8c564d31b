{"blueprint": {"available_types": {"immutable": false, "name": "Available Types", "objectType": "contact_info_types", "selectDisplay": "[name]", "type": "objectIds"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_default_type": {"name": "Is default type", "type": "int", "immutable": false}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "notEmpty": true, "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "states": {"blueprint": {"allowAllTransitions": {"immutable": false, "name": "Allow all transitions?", "type": "bool"}, "color": {"immutable": false, "name": "Name", "type": "text"}, "icon": {"immutable": false, "name": "Icon", "type": "text"}, "isEntryPoint": {"immutable": false, "name": "Is entry point", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "text"}, "next": {"immutable": false, "name": "Next states", "type": "object"}, "previous": {"immutable": false, "name": "Previous states", "type": "object"}, "shouldTransitionOnTaskComplete": {"immutable": false, "name": "Move to next state when tasks are complete?", "type": "bool"}, "type": {"immutable": false, "name": "Type", "type": "text"}, "uid": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}, "tags": {"immutable": false, "name": "Tags", "type": "objectIds"}}, "immutable": false, "name": "States", "type": "list"}, "tools": {"immutable": false, "name": "Tools", "objectType": "entity_tool", "type": "objectIds"}}, "blueprint_name": "contact_types", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.449485", "id": 272, "instance": "<PERSON><PERSON><PERSON><PERSON>"}