{"blueprint": {"bounced": {"immutable": false, "name": "bounced", "type": "int"}, "clicked": {"immutable": false, "name": "clicked", "type": "int"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "date_created", "type": "string"}, "from": {"immutable": false, "name": "from", "type": "string"}, "id": {"immutable": true, "name": "id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "mandrill_id": {"immutable": false, "name": "mandrill_id", "type": "string"}, "message": {"immutable": false, "name": "message", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "opened": {"immutable": false, "name": "opened", "type": "int"}, "read": {"immutable": false, "name": "read", "type": "int"}, "sent": {"immutable": false, "name": "sent", "type": "int"}, "subject": {"immutable": false, "name": "subject", "type": "string"}, "thread_id": {"immutable": false, "name": "thread_id", "type": "int"}, "to": {"immutable": false, "name": "to", "type": "string"}, "type": {"immutable": false, "name": "type", "type": "string"}, "type_id": {"immutable": false, "name": "type_id", "type": "int"}}, "blueprint_name": "emails", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.402709", "id": 230, "instance": "<PERSON><PERSON><PERSON><PERSON>"}