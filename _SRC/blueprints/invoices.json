{"blueprint": {"active": {"immutable": false, "name": "Active", "options": ["Yes", "No"], "type": "select"}, "amount": {"immutable": false, "name": "Amount", "type": "usd"}, "fees": {"immutable": false, "name": "Fees", "type": "usd"}, "balance": {"immutable": true, "name": "Balance", "type": "usd"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "due_date": {"immutable": false, "name": "Due Date", "type": "date"}, "locked": {"immutable": false, "name": "Locked", "type": "select", "options": {"not-locked": "Not Locked", "locked": "Locked"}}, "id": {"immutable": true, "name": "ID", "type": "int"}, "id_hash": {"immutable": true, "name": "ID Hash", "type": "string"}, "invoice_type_list": {"immutable": false, "name": "Invoice Type", "objectType": "invoice_type", "selectDisplay": "[invoice_type]", "type": "objectId"}, "items": {"blueprint": {"amount": {"immutable": false, "name": "Amount", "type": "usd"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "quantity": {"immutable": false, "name": "Quantity", "type": "int"}, "tax_rate": {"immutable": false, "name": "Tax Rate", "type": "float"}}, "immutable": false, "name": "Line Items", "type": "list"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "logo": {"immutable": false, "name": "Logo", "objectType": "company_logo", "selectDisplay": "[name]", "type": "objectId"}, "main_contact": {"immutable": false, "name": "Main Contact", "objectType": "contacts", "selectDisplay": "[fname] [lname]", "objectOverflow": true, "type": "objectId"}, "owner": {"immutable": false, "name": "Owner", "objectType": "users", "selectDisplay": "[fname] [lname]", "objectOverflow": true, "type": "objectId"}, "main_client": {"immutable": false, "name": "Main Client", "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true, "type": "objectId"}, "memo": {"immutable": false, "name": "Memo", "type": "string"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "invoice_template": {"immutable": true, "name": "Template", "type": "int"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "paid": {"immutable": true, "name": "Paid", "type": "usd"}, "payments": {"immutable": true, "name": "Payments", "objectType": "payments", "selectDisplay": "[date_created] - [amount]", "type": "objectIds"}, "related_object": {"immutable": false, "name": "Related Object", "type": "relatedObject"}, "sent": {"immutable": true, "name": "<PERSON><PERSON>", "type": "string"}, "sent_by": {"immutable": true, "name": "<PERSON><PERSON>", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "sent_on": {"immutable": true, "name": "<PERSON><PERSON>", "type": "date"}, "tax_rate": {"immutable": false, "name": "Tax Rate", "type": "float"}, "template": {"immutable": true, "name": "Template", "type": "object"}, "type": {"immutable": true, "name": "Type", "type": "string"}, "type_id": {"immutable": false, "name": "Invoice Type ID", "objectType": "invoice_type", "selectDisplay": "[invoice_type]", "type": "objectId"}}, "blueprint_name": "invoices", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.381255", "id": 210, "instance": "<PERSON><PERSON><PERSON><PERSON>"}