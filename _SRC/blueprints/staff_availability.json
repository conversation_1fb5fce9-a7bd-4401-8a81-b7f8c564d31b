{"blueprint": {"data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "day_of_week": {"immutable": true, "name": "Day Of Week", "options": {"Fri": "<PERSON><PERSON>", "Mon": "Mon", "Sat": "Sat", "Sun": "Sun", "Thu": "<PERSON>hu", "Tue": "<PERSON><PERSON>", "Wed": "Wed"}, "type": "select"}, "end_time": {"immutable": false, "name": "End Time", "type": "date"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "reason": {"immutable": false, "name": "Reason", "type": "string"}, "recurring": {"immutable": false, "name": "Recurring", "options": {"no": "No", "yes_end_date": "Yes, with end date", "yes_forever": "Yes, forever"}, "type": "select"}, "request_type": {"immutable": false, "name": "Request Type", "options": {"request_time_off": "Request Time Off", "request_to_work": "Request To Work"}, "type": "select"}, "staff": {"immutable": false, "name": "Staff", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "start_time": {"immutable": false, "name": "Start Time", "type": "date"}}, "blueprint_name": "staff_availability", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 9, "instance": "<PERSON><PERSON><PERSON><PERSON>"}