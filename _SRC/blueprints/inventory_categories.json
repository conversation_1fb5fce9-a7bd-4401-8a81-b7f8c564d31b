{"blueprint": {"chart_of_account": {"immutable": false, "name": "Chart of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "description", "type": "string"}, "exclusive_tax": {"immutable": false, "name": "Exclusive Tax", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "inclusive_tax": {"immutable": false, "name": "Inclusive Tax", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "is_deleted": {"immutable": true, "name": "Is deleted?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "test_private_val": {"encrypt": true, "immutable": false, "name": "Test", "type": "string"}, "vendor_id": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "objectType": "vendors", "selectDisplay": "[name]", "type": "objectId"}}, "blueprint_name": "inventory_categories", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.432806", "id": 258, "instance": "<PERSON><PERSON><PERSON><PERSON>"}