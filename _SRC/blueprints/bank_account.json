{"blueprint": {"account_number": {"immutable": false, "name": "Account Number", "type": "string"}, "bank_name": {"immutable": false, "name": "Bank Name", "type": "string"}, "chart_of_account": {"immutable": false, "name": "Chart of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "routing_number": {"immutable": false, "name": "Routing Number", "type": "string"}}, "blueprint_name": "bank_account", "blueprint_type": "object", "date_created": "2017-03-10 14:56:25.78082", "id": 284, "instance": "<PERSON><PERSON><PERSON><PERSON>"}