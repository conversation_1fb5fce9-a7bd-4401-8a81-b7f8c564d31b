{"blueprint": {"chart_of_accounts_options": {"immutable": false, "name": "Chart Of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name] - [account_id]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "permissions": {"immutable": false, "name": "View Permissions", "objectType": "permissions", "selectDisplay": "[displayName]", "type": "objectIds"}}, "blueprint_name": "staff_job_types", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.427758", "id": 253, "instance": "<PERSON><PERSON><PERSON><PERSON>"}