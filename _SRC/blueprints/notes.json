{"blueprint": {"activity_type": {"immutable": true, "name": "Activity Type", "options": {"create": "Create", "delete": "Delete", "update": "Update"}, "type": "select"}, "edited": {"immutable": false, "name": "Edited", "type": "int"}, "author": {"immutable": false, "name": "Author", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "date_created", "type": "string"}, "field": {"immutable": false, "name": "Field", "type": "string"}, "icon": {"blueprint": {"icon": {"immutable": false, "name": "Icon", "type": "string"}, "color": {"immutable": false, "name": "Color", "type": "text"}}, "immutable": false, "name": "Icon", "type": "object"}, "id": {"immutable": true, "name": "id", "type": "int"}, "isDeleted": {"immutable": false, "name": "isDeleted", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "log_data": {"blueprint": {"details": {"immutable": false, "name": "Details", "type": "string"}, "objectName": {"immutable": false, "name": "Name", "type": "string"}, "type": {"immutable": true, "name": "Type", "options": {"create": "Create", "delete": "Delete", "state-change": "State Change", "update": "Update"}, "type": "select"}}, "immutable": false, "name": "Log Data", "type": "object"}, "log_type": {"options": {"create": "Create", "delete": "Delete", "state-change": "State Change", "update": "Update", "automated-email": "Automated Email", "recent-client-action": "Recent Client Action"}, "immutable": true, "name": "Log Type", "type": "select"}, "note": {"immutable": false, "name": "note", "type": "string"}, "note_type": {"immutable": false, "name": "Note Type", "objectType": "note_types", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "notify": {"immutable": false, "name": "notify", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectIds", "objectOverflow": true}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "page_params": {"immutable": false, "name": "Page Params", "type": "string"}, "public": {"immutable": false, "name": "public", "type": "int"}, "record_type": {"immutable": true, "name": "Record Type", "options": {"comment": "User Comment", "log": "System Log"}, "type": "select"}, "type": {"immutable": false, "name": "type", "type": "string"}, "type_id": {"immutable": false, "name": "type_id", "type": "objectId", "objectOverflow": true}}, "blueprint_name": "notes", "blueprint_type": "object", "date_created": "2017-07-13 09:19:25.189942", "id": 312, "instance": "<PERSON><PERSON><PERSON><PERSON>"}