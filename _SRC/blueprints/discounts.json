{"blueprint": {"after_taxes": {"immutable": false, "name": "After taxes", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "apply_to": {"immutable": false, "name": "apply_to", "options": {"category": "Category", "line_item": "Line Item", "workorder": "Workorder"}, "type": "select"}, "categories": {"immutable": false, "name": "Categories", "objectType": "inventory_billable_groups", "type": "objectIds"}, "category": {"immutable": false, "name": "Discount Category", "objectType": "discount_categories", "type": "objectId"}, "chart_of_account": {"immutable": true, "name": "Chart of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectId"}, "copied_from": {"immutable": true, "name": "Copied from", "objectType": "discount_templates", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "discount_id": {"immutable": true, "name": "discount_id", "type": "string"}, "factor": {"immutable": false, "name": "Factor", "type": "int"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "item": {"immutable": false, "name": "Item to apply to", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "memo": {"immutable": false, "name": "Memo", "type": "string"}, "menu": {"immutable": false, "name": "Name", "objectType": "inventory_menu", "selectDisplay": "[name]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "source_type": {"immutable": false, "name": "Source Type", "type": "object"}, "type": {"immutable": false, "name": "type", "options": {"amount_off": "Amount off", "percent_off": "Percent off", "replace_amount": "Replacement amount"}, "type": "select"}}, "blueprint_name": "discounts", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 50, "instance": "<PERSON><PERSON><PERSON><PERSON>"}