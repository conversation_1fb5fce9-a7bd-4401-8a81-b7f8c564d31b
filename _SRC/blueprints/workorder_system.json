{"blueprint": {"approval_admin_users": {"immutable": false, "name": "Approval Admin Users", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectIds"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "default_opp_note": {"immutable": false, "name": "Default Opportunity Note", "type": "text"}, "follow_up_time": {"immutable": false, "name": "Follow Up Time", "type": "int"}, "follow_up_type": {"immutable": false, "name": "Follow Up Type", "type": "text"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "request_email": {"immutable": false, "name": "Signture Request Email Template", "type": "text"}, "request_email_subject": {"immutable": false, "name": "Signture Request Email Subject Template", "type": "text"}, "require_approval": {"immutable": false, "name": "Require Approval", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "signature_disclaimer": {"immutable": false, "name": "Signature Disclaimer", "type": "text"}}, "blueprint_name": "workorder_system", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 36, "instance": "<PERSON><PERSON><PERSON><PERSON>"}