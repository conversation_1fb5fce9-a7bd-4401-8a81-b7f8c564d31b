{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "settings_uid": {"immutable": true, "name": "Settings Id", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "settings": {"immutable": false, "name": "Settings", "type": "object"}, "user": {"immutable": false, "name": "User", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}}, "blueprint_name": "view_settings", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 29, "instance": "<PERSON><PERSON><PERSON><PERSON>"}