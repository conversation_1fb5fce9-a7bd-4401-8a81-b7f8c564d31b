{"blueprint": {"parent": {"name": "Parent", "type": "objectId", "objectType": "document", "immutable": false, "objectOverflow": true}, "body": {"immutable": false, "name": "Body", "type": "string"}, "category": {"immutable": false, "name": "Document Category", "objectType": "document_category", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "state": {"immutable": true, "name": "State", "type": "int", "fieldType": "state", "workflow": "type"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "document_type": {"immutable": false, "name": "Document Type", "options": {"custom-file": "Custom File", "google_doc": "Google Doc", "google_other": "Google Other", "google_sheet": "Google Sheet", "google_slide": "Google Slide", "share": "Shareable Link", "text": "Text", "upload": "File Upload"}, "type": "select"}, "fieldType": {"immutable": true, "name": "Field Type", "type": "string", "value": "State"}, "file_type": {"immutable": true, "name": "File Type", "type": "string"}, "file_upload": {"immutable": false, "name": "File", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "is_archived": {"immutable": true, "name": "Is Archived?", "type": "int"}, "is_deleted": {"immutable": true, "name": "Is Deleted?", "type": "int"}, "is_public": {"immutable": true, "name": "Is Public?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "loc": {"immutable": true, "name": "Location", "type": "string"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "related_object": {"immutable": false, "name": "Related Object Id", "type": "relatedObject"}, "share_link": {"immutable": false, "name": "Share Link", "type": "string"}, "storage_type": {"immutable": false, "name": "Storage Type", "options": {"box": "Box File", "dropbox": "Dropbox File", "google": "Google", "onedrive": "Microsoft OneDrive", "other": "Other"}, "type": "select"}, "tagged_users": {"immutable": false, "name": "Tagged Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds"}}, "blueprint_name": "document", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 44, "instance": "<PERSON><PERSON><PERSON><PERSON>"}