{"blueprint": {"chart_of_account": {"immutable": true, "name": "Chart of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "default_pricing_option": {"immutable": false, "name": "Default pricing option", "options": {"price": "Price per unit", "price_per_hour": "Price per hour", "price_per_hour_per_person": "Price per hour per person", "price_per_person": "Price per person"}, "type": "select"}, "default_tax_rate": {"immutable": false, "name": "Default Tax Rate", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_deleted": {"immutable": true, "name": "Is deleted?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "surcharges": {"immutable": false, "name": "Surcharges", "objectType": "surcharges", "selectDisplay": "[name]", "type": "objectIds"}, "tax_rates": {"immutable": false, "name": "Tax Rates", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}}, "blueprint_name": "inventory_billable_combination_categories", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 32, "instance": "<PERSON><PERSON><PERSON><PERSON>"}