{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "parent": {"immutable": false, "name": "Parent", "objectType": "groups", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "source_type": {"immutable": false, "name": "Source Type", "type": "object"}}, "blueprint_name": "categories", "blueprint_type": "object", "date_created": "2020-03-06 21:46:50.920443", "instance": "<PERSON><PERSON><PERSON><PERSON>"}