{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "date_created", "type": "string"}, "id": {"immutable": true, "name": "id", "type": "int"}, "does_not_expire": {"immutable": false, "name": "Does Not Expire", "type": "int"}, "fingerprint": {"immutable": false, "name": "Fingerprint", "type": "string"}, "platform": {"immutable": false, "name": "Platform", "type": "string"}, "ip_address": {"immutable": false, "name": "IP Address", "type": "string"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "series": {"immutable": false, "name": "series", "type": "string"}, "token": {"immutable": false, "name": "token", "type": "string"}, "uid": {"immutable": false, "name": "uid", "type": "int"}, "user_type": {"immutable": false, "name": "user_type", "type": "string"}}, "blueprint_name": "cookies", "blueprint_type": "object", "date_created": "2017-03-27 15:48:51.596768", "id": 287, "instance": "<PERSON><PERSON><PERSON><PERSON>"}