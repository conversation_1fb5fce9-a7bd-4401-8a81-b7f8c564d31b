{"blueprint": {"active": {"immutable": false, "name": "Active", "options": ["Yes", "No"], "type": "select"}, "attachment": {"immutable": false, "name": "Attachment", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "bid_request_status": {"immutable": false, "name": "Bid request status", "options": {"bid-selected": "Bid Selected", "cancelled": "Cancelled", "closed": "Closed", "draft": "Draft", "posted": "Posted"}, "type": "select"}, "budgets": {"blueprint": {"amount": {"immutable": false, "name": "Amount", "type": "usd"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "type": {"immutable": false, "name": "Budget Type", "objectType": "menu_budget_types", "selectDisplay": "[name]", "type": "objectId"}}, "immutable": false, "name": "Budgets", "type": "list"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date": {"immutable": false, "name": "Date", "type": "date"}, "date_created": {"immutable": true, "name": "Last Updated", "type": "date"}, "date_submitted": {"immutable": false, "name": "Date submitted", "type": "date"}, "end_date": {"immutable": false, "name": "End Date", "type": "date"}, "guest_count": {"immutable": false, "name": "Guest Count", "type": "int"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "notes": {"immutable": false, "notes": "Notes", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "owner": {"immutable": false, "name": "Owner", "objectType": "users", "selectDisplay": "[fname] [lname]", "objectOverflow": true, "type": "objectId"}, "quote_status": {"immutable": false, "name": "Quote status", "options": {"accepted": "Accepted", "declined": "Declined", "draft": "Draft", "sent": "<PERSON><PERSON>"}, "type": "select"}, "related": {"immutable": false, "name": "Related Object", "type": "int"}, "sections": {"blueprint": {"details": {"immutable": false, "name": "Details", "type": "string"}, "from": {"immutable": false, "name": "Start Time", "type": "date"}, "hidden_on_invoice": {"immutable": false, "name": "Hidden on Invoice", "type": "select", "options": {"no": "No", "yes": "Yes"}}, "id": {"immutable": false, "name": "id", "type": "int"}, "items": {"immutable": false, "name": "Items", "objectType": "inventory_menu_line_items", "type": "objectIds"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "sortId": {"immutable": false, "name": "Sort Id", "type": "int"}, "to": {"immutable": false, "name": "End Time", "type": "date"}}, "immutable": false, "name": "Sections", "type": "list"}, "start_date": {"immutable": false, "name": "Start Date", "type": "date"}, "type": {"immutable": false, "name": "Type", "options": {"bid": "Bid", "bid-request": "Bid request", "event-menu": "Event Invoice", "quote": "Quote", "standard": "Standard Invoice"}, "type": "select"}, "vendor": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "objectType": "companies", "selectDisplay": "[name]", "type": "objectId"}, "venue": {"immutable": false, "name": "Venue", "objectType": "staff_base", "selectDisplay": "[name]", "type": "objectId"}}, "blueprint_name": "inventory_menu", "blueprint_type": "object", "date_created": "2017-06-20 14:36:54.31303", "id": 309, "instance": "<PERSON><PERSON><PERSON><PERSON>"}