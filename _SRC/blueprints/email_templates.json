{"blueprint": {"body": {"immutable": false, "name": "Body", "type": "string"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "email_templates", "blueprint_type": "object", "date_created": "2017-04-07 12:25:53.256798", "id": 290, "instance": "<PERSON><PERSON><PERSON><PERSON>"}