{"blueprint": {"id": {"immutable": true, "name": "ID", "type": "int"}, "name": {"immutable": false, "name": "Snippet Name", "type": "string", "fieldType": "title"}, "contents": {"immutable": false, "name": "Snippet Contents", "type": "string"}, "entity_type": {"immutable": false, "name": "Entity Type", "objectType": "entity_type", "selectDisplay": "[name]", "type": "objectId", "objectOverflow": true}, "entity_field_key": {"immutable": false, "name": "Field", "type": "string"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}}, "blueprint_name": "snippet", "blueprint_type": "object", "date_created": "2021-09-07T15:42:57Z", "instance": "<PERSON><PERSON><PERSON><PERSON>"}