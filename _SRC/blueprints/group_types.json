{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "source_type": {"immutable": false, "name": "Source Type", "type": "object"}, "states": {"blueprint": {"color": {"immutable": false, "name": "Name", "type": "text"}, "icon": {"immutable": false, "name": "Icon", "type": "text"}, "isEntryPoint": {"immutable": false, "name": "Is entry point", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "text"}, "next": {"immutable": false, "name": "Next states", "type": "object"}, "previous": {"immutable": false, "name": "Previous states", "type": "object"}, "uid": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}}, "immutable": false, "name": "States", "type": "list"}}, "blueprint_name": "group_types", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 52, "instance": "<PERSON><PERSON><PERSON><PERSON>"}