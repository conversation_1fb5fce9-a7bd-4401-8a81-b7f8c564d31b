{"blueprint": {"payment_creator_id": {"immutable": true, "name": "Payment Creator Id", "type": "string"}, "payment_contact": {"immutable": false, "name": "Contact", "type": "object"}, "contact_company": {"immutable": false, "name": "Contact Company", "type": "object"}, "contact_info": {"immutable": false, "name": "Contact Info", "type": "object"}, "payment": {"immutable": false, "name": "payments", "type": "object"}, "proposal_id": {"immutable": true, "name": "Proposal Id", "type": "objectId"}, "proposal": {"immutable": false, "name": "Proposal", "type": "object"}, "stripe_payment_id": {"immutable": true, "name": "Stripe Payment ID", "type": "string"}, "stripe_event_id": {"immutable": true, "name": "Stripe Event ID", "type": "string"}, "instance_obj": {"immutable": false, "name": "Instance Object", "type": "object"}, "instance_info": {"immutable": false, "name": "Instance Info", "type": "object"}}, "blueprint_name": "payment_receipt", "blueprint_type": "object", "date_created": "2021-08-16 10:02:59.744790", "id": 203, "instance": "<PERSON><PERSON><PERSON><PERSON>"}