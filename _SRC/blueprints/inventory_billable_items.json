{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_deleted": {"immutable": true, "name": "Is deleted?", "type": "int"}, "items": {"blueprint": {"divisor": {"immutable": false, "name": "Divisor", "type": "float"}, "id": {"immutable": false, "name": "ID", "type": "int"}, "inventory_group": {"immutable": false, "name": "Inventory Group", "type": "objectId"}, "multiplier": {"immutable": false, "name": "Multiplier", "type": "float"}, "price": {"immutable": false, "name": "Price Per Unit", "type": "usd"}, "units": {"immutable": false, "name": "Units", "type": "int"}}, "immutable": false, "name": "Items", "type": "list"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "inventory_billable_items", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.43724", "id": 262, "instance": "<PERSON><PERSON><PERSON><PERSON>"}