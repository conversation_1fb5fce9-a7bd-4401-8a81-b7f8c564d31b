{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "credit_card_percent": {"immutable": false, "name": "Credit Card Percent", "type": "string"}, "credit_card_flat_fee": {"immutable": false, "name": "Credit Card Flat Fee", "type": "string"}, "ach_percent": {"immutable": false, "name": "ACH Percent", "type": "string"}, "ach_flat_fee": {"immutable": false, "name": "ACH Flat Fee", "type": "string"}, "ICG_credit_card_percent": {"immutable": false, "name": "iCheckGateway Credit Card Percent", "type": "string"}, "ICG_credit_card_flat_fee": {"immutable": false, "name": "iCheckGateway Credit Card Flat Fee", "type": "string"}, "ICG_ach_percent": {"immutable": false, "name": "iCheckGateway ACH Percent", "type": "string"}, "ICG_ach_flat_fee": {"immutable": false, "name": "iCheckGateway ACH Flat Fee", "type": "string"}, "chart_of_account": {"immutable": false, "name": "Chart of Account", "type": "relatedObject"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "invoice_fees", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 39, "instance": "<PERSON><PERSON><PERSON><PERSON>"}