{"blueprint": {"bank_account": {"immutable": false, "name": "Bank Account", "objectType": "bank_account", "selectDisplay": "[bank_name]", "type": "objectId"}, "company_logo": {"immutable": false, "name": "Company logo", "objectType": "file_meta_data", "selectDisplay": "name", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "invoice_type": {"immutable": false, "name": "Name", "type": "string"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "stripe_payout_account": {"immutable": false, "name": "Depo<PERSON>t Account", "type": "string"}}, "blueprint_name": "invoice_type", "blueprint_type": "object", "date_created": "2017-03-10 14:55:41.720645", "id": 282, "instance": "<PERSON><PERSON><PERSON><PERSON>"}