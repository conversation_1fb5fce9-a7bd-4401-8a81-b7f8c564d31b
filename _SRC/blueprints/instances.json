{"blueprint": {"account_signup_code": {"immutable": false, "name": "Account signup code", "type": "string"}, "billing_plan": {"immutable": false, "name": "Billing Plan", "type": "object"}, "billing_type": {"immutable": false, "name": "Billing Type", "type": "string"}, "company_logo": {"immutable": false, "name": "Company logo", "objectType": "file_meta_data", "selectDisplay": "name", "type": "objectId"}, "componentSource": {"immutable": false, "name": "Component Source", "type": "string"}, "components": {"immutable": false, "name": "Components", "type": "string"}, "contract": {"immutable": false, "name": "Contract", "objectType": "contract", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "credit": {"immutable": false, "name": "Credit", "type": "float"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "db_post": {"immutable": false, "name": "Database Post", "type": "string"}, "db_read": {"immutable": false, "name": "Database Read", "type": "string"}, "db_write": {"immutable": false, "name": "Database Write", "type": "string"}, "email": {"immutable": false, "name": "Email Address", "type": "string"}, "emailFrom": {"immutable": false, "name": "Email From", "type": "string"}, "enabled": {"immutable": false, "name": "Enabled", "options": ["Disabled", "Enabled"], "type": "select"}, "factorySource": {"immutable": false, "name": "Factory Source", "type": "string"}, "files_bucket": {"immutable": false, "name": "Files Bucket", "type": "string"}, "files_delete": {"immutable": false, "name": "Files Delete", "type": "string"}, "files_read": {"immutable": false, "name": "Files Read", "type": "string"}, "files_write": {"immutable": false, "name": "Files Write", "type": "string"}, "flat_fee": {"immutable": false, "name": "Flat fee", "type": "int"}, "flat_user_cap": {"immutable": false, "name": "Flat user cap", "type": "int"}, "fname": {"immutable": false, "name": "First Name", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "instance": {"immutable": false, "name": "Slug", "type": "string"}, "instance_type": {"immutable": false, "name": "Instance type", "type": "int"}, "is_template": {"immutable": false, "name": "Is template?", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "is_portal": {"immutable": false, "name": "Is portal?", "type": "bool"}, "last_billing_date": {"immutable": false, "name": "Last billing date", "type": "date"}, "last_invoice": {"immutable": false, "name": "Last invoice", "objectOverflow": true, "objectType": "invoices", "selectDisplay": "[date_created]", "type": "objectId"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "lname": {"immutable": false, "name": "Last Name", "type": "string"}, "mailchimp_api": {"immutable": false, "name": "MailChimp API Key", "type": "string"}, "mailchimp_list_id": {"immutable": false, "name": "MailChimp List ID", "type": "string"}, "main_contact": {"immutable": false, "name": "Main Contact", "objectType": "contacts", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "mandrill_api": {"immutable": false, "name": "Mandrill API Key", "type": "string"}, "moduleSource": {"immutable": false, "name": "Module Source", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "owners": {"immutable": false, "name": "Owners", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectIds"}, "pageModules": {"immutable": false, "name": "<PERSON>", "type": "string"}, "parent": {"immutable": true, "name": "Parent", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "parentInstance": {"immutable": true, "name": "Parent Instance", "objectType": "string", "type": "string"}, "per_user_price": {"immutable": false, "name": "Per User Price", "type": "int"}, "permissions": {"immutable": false, "name": "Permisions", "type": "string"}, "price_per_user": {"immutable": false, "name": "Price per user", "type": "int"}, "qty_of_users": {"immutable": false, "name": "Qty of users", "type": "int"}, "quickbooks_access_token": {"immutable": false, "name": "QuickBooks Access Token", "type": "string"}, "quickbooks_realm_id": {"immutable": false, "name": "QuickBooks Realm ID", "type": "string"}, "quickbooks_refresh_token": {"immutable": false, "name": "QuickBooks Refresh Token", "type": "string"}, "searchObjects": {"immutable": false, "name": "Search Objects", "type": "string"}, "settingSource": {"immutable": false, "name": "Setting Source", "type": "string"}, "settingsModules": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "type": "string"}, "settings_objects": {"immutable": false, "name": "Settings Objects", "type": "string"}, "sms_from": {"immutable": false, "name": "SMS From", "type": "string"}, "sold_by": {"immutable": false, "name": "Sold by", "objectOverflow": true, "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "stripe_account_id": {"immutable": false, "name": "Stripe Account ID", "type": "string"}, "stripe_account_type": {"immutable": false, "name": "Stripe Account Type", "type": "string"}, "systemName": {"immutable": false, "name": "System Name", "type": "string"}, "tax_rate": {"immutable": false, "name": "Tax rate", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectId"}, "temp_user": {"immutable": false, "name": "Temp Root User", "type": "object"}, "trial_end_date": {"immutable": false, "name": "Trial end date", "type": "date"}, "trial_start_date": {"immutable": false, "name": "Trial start date", "type": "date"}, "twilio_sid": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "type": "string"}, "twilio_token": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "type": "string"}, "userSettings": {"immutable": false, "name": "User Settings", "type": "string"}, "version": {"immutable": false, "name": "Version", "type": "string"}}, "blueprint_name": "instances", "blueprint_type": "instance", "date_created": "2017-06-11 14:20:44.48617", "id": 305, "instance": "<PERSON><PERSON><PERSON><PERSON>"}