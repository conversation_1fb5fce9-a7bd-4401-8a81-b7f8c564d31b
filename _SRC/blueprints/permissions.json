{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "displayName": {"immutable": true, "name": "Display Name", "type": "string"}, "edit": {"immutable": true, "name": "Edit", "type": "object"}, "erase": {"immutable": true, "name": "Erase", "type": "object"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "pageModuleId": {"immutable": true, "name": "Page Module ID", "type": "string"}, "staff_types": {"immutable": true, "name": "Staff Types", "objectType": "staff_job_types", "selectDisplay": "[name]", "type": "objectIds"}, "view": {"immutable": true, "name": "View", "type": "object"}}, "blueprint_name": "permissions", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.426812", "id": 252, "instance": "<PERSON><PERSON><PERSON><PERSON>"}