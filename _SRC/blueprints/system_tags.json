{"blueprint": {"color": {"immutable": false, "name": "Color", "type": "text"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "icon": {"immutable": false, "name": "Icon", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_deleted": {"immutable": true, "name": "Is deleted?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "objects": {"immutable": true, "name": "Tagged Objects", "type": "object"}, "tag": {"immutable": false, "name": "Tag", "type": "string"}, "type": {"immutable": false, "name": "Object Type", "type": "string"}}, "blueprint_name": "system_tags", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.387212", "id": 215, "instance": "<PERSON><PERSON><PERSON><PERSON>"}