{"blueprint": {"contact": {"immutable": false, "name": "Contact", "objectOverflow": true, "objectType": "contacts", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "stripe_customer_id": {"immutable": true, "name": "Stripe Customer Id", "type": "string"}, "stripe_bank_account_id": {"immutable": true, "name": "Stripe Bank Account Id", "type": "string"}, "stripe_verification_hash": {"immutable": true, "name": "Stripe Verification Hash", "type": "string"}, "stripe_bank_account_status": {"immutable": true, "name": "Stripe Bank Account Status", "type": "string"}, "verification_email": {"immutable": true, "name": "Verification Email", "type": "string"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "bank_account_verification", "blueprint_type": "object", "date_created": "2017-09-29 15:49:04.315597", "id": 273, "instance": "<PERSON><PERSON><PERSON><PERSON>"}