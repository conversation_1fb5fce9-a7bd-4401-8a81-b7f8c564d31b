{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "templates": {"blueprint": {"before_after": {"immutable": false, "name": "Before or After", "options": {"after": "After", "before": "Before"}, "type": "select"}, "before_after_type": {"immutable": false, "name": "Before or After Type", "options": {"project": "Work Order/Project Date", "proposal": "Proposal Acceptance Date", "today": "Today"}, "type": "select"}, "due_date": {"immutable": false, "name": "Due Date", "type": "int"}, "flat_rate": {"immutable": false, "name": "Flat Rate", "type": "usd"}, "inventory_billable_categories": {"immutable": false, "name": "Menu Item Category", "objectType": "inventory_billable_categories", "selectDisplay": "[name]", "type": "objectIds"}, "chart_of_accounts": {"immutable": false, "name": "Chart of Accounts", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectIds"}, "invoice_type": {"immutable": false, "name": "Invoice Type", "objectType": "invoice_type", "selectDisplay": "[name]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "payment_type": {"immutable": false, "name": "Payment Type", "options": {"flatRate": "Flat Rate", "percentOfTotal": "Percent of Total", "remainingBalance": "Remaining Balance"}, "type": "select"}, "percent_of_total": {"immutable": false, "name": "Percent of Total", "type": "float"}}, "immutable": false, "name": "Templates", "type": "list"}}, "blueprint_name": "payment_schedule_template", "blueprint_type": "object", "date_created": "2017-03-10 14:55:01.8088", "id": 281, "instance": "<PERSON><PERSON><PERSON><PERSON>"}