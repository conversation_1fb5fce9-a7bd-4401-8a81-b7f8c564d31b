{"blueprint": {"client_id": {"name": "Client ID", "type": "string", "immutable": true}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "event_type": {"name": "Event Type", "type": "string", "immutable": true}, "hook_id": {"name": "Hook ID", "type": "string", "immutable": true}, "hook_url": {"name": "Hook URL", "type": "string", "immutable": true}, "id": {"immutable": true, "name": "ID", "type": "int"}, "instance": {"name": "Instance", "type": "string", "immutable": true}, "is_active": {"name": "Is Active", "type": "bool", "immutable": false}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "token": {"name": "Token", "type": "int", "immutable": true}, "user": {"name": "User ID", "type": "int", "immutable": true}}, "blueprint_name": "hook_subscription", "blueprint_type": "object", "date_created": "2019-07-18 19", "instance": "<PERSON><PERSON><PERSON><PERSON>"}