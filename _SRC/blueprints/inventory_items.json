{"blueprint": {"chart_of_account": {"immutable": false, "name": "Chart of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectIds"}, "cost": {"immutable": false, "name": "Cost", "type": "usd"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "exclusive_tax": {"immutable": false, "name": "Exclusive Tax", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "expiration_date": {"immutable": false, "name": "Expiration Date", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "inclusive_tax": {"immutable": false, "name": "Inclusive Tax", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "initial_quantity": {"immutable": false, "name": "Initial Quantity", "type": "float"}, "inventory_group": {"immutable": false, "name": "Inventory Group", "objectType": "inventory_groups", "selectDisplay": "[name]", "type": "objectId"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "location": {"immutable": false, "name": "Location", "objectType": "staff_base", "selectDisplay": "[name]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "picture": {"immutable": false, "name": "Picture", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "quantity": {"immutable": false, "name": "Quantity", "type": "float"}, "received_date": {"immutable": false, "name": "Received Date", "type": "date"}, "serial_number": {"immutable": false, "name": "Serial Number", "type": "string"}, "shelf_tag": {"immutable": false, "name": "<PERSON><PERSON>", "type": "string"}, "unit_type": {"immutable": false, "name": "Unit type", "options": {"dry_volume": "Dry volume", "liquid_volume": "Liquid volume", "quantity": "Quantity", "weight": "Weight"}, "type": "select"}, "units": {"immutable": false, "name": "Units", "type": "int"}, "vendor_id": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "objectOverflow": true, "objectType": "companies", "selectDisplay": "[name]", "type": "objectId"}}, "blueprint_name": "inventory_items", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.417428", "id": 243, "instance": "<PERSON><PERSON><PERSON><PERSON>"}