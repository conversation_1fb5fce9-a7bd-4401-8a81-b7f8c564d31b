{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "endDate": {"immutable": false, "name": "End Date", "type": "string"}, "id": {"immutable": true, "name": "id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "reason": {"immutable": false, "name": "Reason", "type": "string"}, "staffId": {"immutable": true, "name": "Staff ID", "type": "int"}, "startDate": {"immutable": false, "name": "Start Date", "type": "string"}, "status": {"immutable": false, "name": "Status", "type": "string"}, "statusChangedBy": {"immutable": false, "name": "Updated By", "type": "int"}, "statusChangedOn": {"immutable": false, "name": "Updated On", "type": "string"}, "totalDays": {"immutable": true, "name": "Total Days", "type": "int"}, "type": {"immutable": false, "name": "Type", "type": "string"}}, "blueprint_name": "time_off_request", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.418464", "id": 244, "instance": "<PERSON><PERSON><PERSON><PERSON>"}