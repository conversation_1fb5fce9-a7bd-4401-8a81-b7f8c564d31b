{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "inventory_billable_category": {"immutable": false, "name": "Product category", "objectType": "inventory_billable_categories", "selectDisplay": "[name]", "type": "objectId"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "inventory_estimate_type", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 30, "instance": "<PERSON><PERSON><PERSON><PERSON>"}