{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "listeners": {"immutable": false, "name": "listeners", "type": "object"}, "not_viewed": {"immutable": false, "name": "Not Viewed", "type": "int"}, "object_id": {"immutable": false, "name": "object_id", "type": "int"}, "object_type": {"immutable": false, "name": "object_type", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "replied_to": {"immutable": false, "name": "Replied to", "type": "int"}, "type": {"immutable": false, "name": "type", "type": "string"}}, "blueprint_name": "threads", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.412109", "id": 238, "instance": "<PERSON><PERSON><PERSON><PERSON>"}