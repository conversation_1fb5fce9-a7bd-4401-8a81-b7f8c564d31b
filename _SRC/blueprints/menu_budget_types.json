{"blueprint": {"categories": {"immutable": false, "name": "Billable Categories Included", "objectType": "inventory_billable_categories", "selectDisplay": "[name]", "type": "objectIds"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "date_created": {"immutable": true, "name": "Last Updated", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}}, "blueprint_name": "menu_budget_types", "blueprint_type": "object", "date_created": "2017-09-12 07:35:35.29972", "id": 317, "instance": "<PERSON><PERSON><PERSON><PERSON>"}