{"blueprint": {"checkpoints": {"blueprint": {"color": {"immutable": false, "name": "Name", "type": "text"}, "icon": {"immutable": false, "name": "Icon", "type": "text"}, "isEntryPoint": {"immutable": false, "name": "Is entry point", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "text"}, "next": {"immutable": false, "name": "Next states", "type": "object"}, "previous": {"immutable": false, "name": "Previous states", "type": "object"}, "uid": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}}, "immutable": false, "name": "Checkpoints", "type": "list"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "date_created": {"immutable": true, "name": "Last Updated", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "order_id": {"immutable": false, "name": "Order", "type": "int"}}, "blueprint_name": "packlist_checkpoints", "blueprint_type": "object", "date_created": "2017-09-18 21:02:44.290519", "id": 319, "instance": "<PERSON><PERSON><PERSON><PERSON>"}