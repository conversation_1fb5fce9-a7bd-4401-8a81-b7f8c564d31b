{"blueprint": {"company_logo": {"immutable": false, "name": "Company logo", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "is_main_app_logo": {"name": "Main App Logo", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "is_primary": {"name": "Primary", "options": {"no": "No", "yes": "Yes"}, "type": "select"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}}, "blueprint_name": "company_logo", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 39, "instance": "<PERSON><PERSON><PERSON><PERSON>"}