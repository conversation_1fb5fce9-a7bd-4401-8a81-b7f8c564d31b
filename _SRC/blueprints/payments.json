{"blueprint": {"amount": {"immutable": false, "name": "amount", "type": "usd"}, "fee": {"immutable": false, "name": "fee", "type": "usd"}, "stripeId": {"immutable": false, "name": "stripeId", "type": "string"}, "status": {"immutable": false, "name": "status", "type": "string"}, "checkNumber": {"immutable": false, "name": "Check Number", "type": "int"}, "checkImg": {"immutable": false, "name": "Check Image", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "date_created", "type": "string"}, "details": {"immutable": false, "name": "details", "type": "object"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "main_object": {"immutable": false, "name": "Main Object", "objectType": "proposals", "selectDisplay": "[name]", "objectOverflow": true, "type": "objectId"}, "main_contact": {"immutable": false, "name": "Main Contact", "objectType": "contacts", "selectDisplay": "[fname] [lname]", "objectOverflow": true, "type": "objectId"}, "owner": {"immutable": false, "name": "Owner", "objectType": "users", "selectDisplay": "[fname] [lname]", "objectOverflow": true, "type": "objectId"}, "main_client": {"immutable": false, "name": "Main Client", "objectType": "companies", "selectDisplay": "[name]", "objectOverflow": true, "type": "objectId"}, "invoice": {"immutable": false, "name": "Invoice", "objectType": "invoices", "selectDisplay": "[name]", "type": "objectId"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "related_object": {"immutable": false, "name": "Related Object", "type": "int"}, "stripe_payment_id": {"immutable": true, "name": "Stripe Payment ID", "type": "string"}, "icg_payment_id": {"immutable": true, "name": "ICG Payment ID", "type": "string"}, "vendor_id": {"immutable": false, "name": "Vendor Id", "type": "string"}, "test_payment": {"immutable": false, "name": "Test Payment", "type": "bool"}, "manual_payment": {"immutable": false, "name": "Manual Payment", "type": "bool"}, "optional_receipt_email": {"immutable": false, "name": "Optional Receipt Email", "type": "string"}}, "blueprint_name": "payments", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.373708", "id": 203, "instance": "<PERSON><PERSON><PERSON><PERSON>"}