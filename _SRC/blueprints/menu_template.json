{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Last Updated", "type": "date"}, "description": {"immutable": false, "name": "Description", "type": "string"}, "guest_count": {"immutable": false, "name": "Guest Count", "type": "int"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "sections": {"blueprint": {"from": {"immutable": false, "name": "Start Time", "type": "time"}, "id": {"immutable": false, "name": "id", "type": "int"}, "items": {"blueprint": {"choices": {"blueprint": {"choice": {"immutable": false, "name": "Choice", "type": "object"}, "item": {"immutable": false, "name": "<PERSON><PERSON>", "type": "int"}}, "immutable": false, "name": "Choices", "type": "object"}, "id": {"immutable": false, "name": "id", "type": "int"}, "item": {"immutable": false, "name": "<PERSON><PERSON>", "objectType": "inventory_billable_groups", "selectDisplay": "[name]", "type": "objectId"}, "qty": {"immutable": false, "name": "Quantity", "type": "int"}, "sortId": {"immutable": false, "name": "Sort Id", "type": "int"}}, "immutable": false, "name": "Items", "type": "list"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "sortId": {"immutable": false, "name": "Sort Id", "type": "int"}, "to": {"immutable": false, "name": "End Time", "type": "time"}}, "immutable": false, "name": "Sections", "type": "list"}, "type": {"immutable": false, "name": "Type", "options": {"event-menu": "Event Invoice", "standard": "Standard Invoice"}, "type": "select"}}, "blueprint_name": "menu_template", "blueprint_type": "object", "date_created": "2017-06-16 10:16:24.320627", "id": 306, "instance": "<PERSON><PERSON><PERSON><PERSON>"}