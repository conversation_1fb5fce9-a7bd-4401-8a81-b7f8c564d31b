{"blueprint": {"allowed_users": {"immutable": false, "name": "Allowed Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "client_id": {"name": "Client ID", "type": "string", "immutable": true}, "client_secret": {"name": "Client Secret", "type": "string", "immutable": true}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "grant_types": {"name": "Grant Types", "type": "object", "immutable": true}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "redirect_uri": {"name": "Redirect URI", "type": "string", "immutable": true}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "scope": {"name": "<PERSON><PERSON>", "type": "string", "immutable": true}, "user": {"immutable": false, "name": "User", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "user_id": {"name": "User Id", "type": "string", "immutable": true}}, "blueprint_name": "oauth_client", "blueprint_type": "object", "date_created": "2019-07-10 19", "instance": "<PERSON><PERSON><PERSON><PERSON>"}