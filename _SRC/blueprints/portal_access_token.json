{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "client": {"immutable": false, "name": "Client Instance", "type": "int"}, "company": {"immutable": false, "name": "Company", "type": "int"}, "contact": {"immutable": false, "name": "Contact", "type": "int"}, "date_created": {"immutable": true, "name": "Last Updated", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_active": {"immutable": false, "name": "Is Active", "type": "bool"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "user": {"immutable": false, "name": "User", "type": "int"}}, "blueprint_name": "portal_access_token", "blueprint_type": "object", "date_created": "2019-10-03", "id": 319, "instance": "<PERSON><PERSON><PERSON><PERSON>"}