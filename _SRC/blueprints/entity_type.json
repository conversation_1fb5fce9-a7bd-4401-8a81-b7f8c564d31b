{"blueprint": {"_class": {"immutable": false, "name": "Class", "type": "int"}, "allowed_users": {"immutable": false, "name": "Allowed Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "can_be_reassigned": {"immutable": false, "name": "Can Be Reassigned", "options": ["Yes", "No"], "type": "select"}, "color": {"immutable": false, "name": "Color", "type": "text"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description", "type": "string", "fieldType": "plain-text"}, "blueprint": {"name": "Fields", "type": "map", "immutable": false, "blueprint": {"name": {"name": "Name", "immutable": false, "type": "string"}, "immutable": {"name": "Is immutable", "immutable": false, "type": "bool"}, "type": {"name": "Type", "immutable": false, "type": "string"}, "fieldType": {"name": "Field Type", "immutable": false, "type": "string"}, "format": {"name": "Formatting", "immutable": false, "type": "object"}, "options": {"name": "Options", "immutable": false, "type": "object"}, "permissions": {"name": "Permissions", "immutable": false, "type": "object"}, "select": {"name": "Selection", "immutable": false, "type": "object"}, "index": {"name": "Index", "immutable": false, "type": "int"}, "workflow": {"name": "Workflow", "immutable": false, "type": "objectId", "objectType": "entity_workflow"}, "is_archived": {"name": "Is archived", "immutable": false, "type": "bool"}, "on": {"name": "'On' Triggers", "immutable": false, "type": "object", "blueprint": {"update": {"name": "On-Update Rules", "immutable": false, "type": "objectIds", "objectType": "event_type", "objectOverflow": true}}}}}, "bp_name": {"name": "Blueprint Name", "type": "string", "immutable": true}, "icon": {"name": "Icon", "type": "string", "fieldType": "icon", "immutable": false}, "id": {"immutable": true, "name": "ID", "type": "int"}, "is_space": {"name": "Is space?", "immutable": false, "type": "bool", "fieldType": "toggle"}, "is_task": {"name": "Is task?", "immutable": false, "type": "bool", "fieldType": "toggle"}, "is_event": {"name": "Is Event?", "immutable": false, "type": "bool", "fieldType": "toggle"}, "is_uniq_to_parent": {"name": "Is Unique to Parents?", "immutable": false, "type": "bool", "fieldType": "toggle"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId", "objectOverflow": true}, "managers": {"immutable": false, "name": "Managers", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "name": {"immutable": false, "name": "Name", "type": "string", "fieldType": "title"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "source_type": {"immutable": false, "name": "Source Type", "type": "object"}, "caldav_map": {"immutable": false, "name": "CalDAV Map", "type": "objectId", "objectOverflow": true}, "shared_with_instances": {"immutable": false, "name": "Shared with Instances", "type": "object"}, "tools": {"immutable": false, "name": "Tools", "objectType": "entity_tool", "type": "objectIds", "objectOverflow": true}, "user": {"immutable": false, "name": "User", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectId", "objectOverflow": true}, "users": {"immutable": false, "name": "Users", "objectType": "users", "selectDisplay": "[fname][lname]", "type": "objectIds", "objectOverflow": true}, "rca_last_updated_comments": {"immutable": true, "name": "RCA Date Last updated", "type": "date"}, "rca_last_updated_uploads": {"immutable": true, "name": "RCA Date Last updated", "type": "date"}}, "blueprint_name": "entity", "blueprint_type": "object", "date_created": "2019-06-03 19", "instance": "<PERSON><PERSON><PERSON><PERSON>"}