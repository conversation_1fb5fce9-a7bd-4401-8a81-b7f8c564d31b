{"blueprint": {"data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "end_time": {"immutable": false, "name": "End Time", "type": "date"}, "id": {"immutable": true, "name": "Id", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "reason": {"immutable": false, "name": "Reason", "type": "string"}, "recurring": {"immutable": false, "name": "Recurring", "options": {"no": "No", "yes": "Yes", "yes_end_date": "Yes, with end date"}, "type": "select"}, "recurring_end_date": {"immutable": false, "name": "Recurring End Date", "type": "date"}, "recurring_week_days": {"immutable": false, "name": "Recurring Week Days", "options": {"friday": "Friday", "monday": "Monday", "saturday": "Saturday", "sunday": "Sunday", "thursday": "Thursday", "tuesday": "Tuesday", "wednesday": "Wednesday"}, "type": "select"}, "related_object": {"immutable": false, "name": "Object Id", "objectType": "users", "type": "objectId"}, "request_type": {"immutable": false, "name": "Request Type", "options": {"time_off": "Time Off", "time_on": "Time On"}, "type": "select"}, "staff": {"immutable": false, "name": "Staff", "objectType": "users", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "start_time": {"immutable": false, "name": "Start Time", "type": "date"}, "status": {"immutable": false, "name": "Status", "options": {"approved": "Approved", "denied": "Denied", "processing": "Processing"}, "type": "select"}, "used_sick_days": {"immutable": false, "name": "Used vacation days", "type": "int"}, "used_vacation_days": {"immutable": false, "name": "Used vacation days", "type": "int"}}, "blueprint_name": "staff_availability_requests", "blueprint_type": "object", "date_created": "2017-10-05 21:46:50.920443", "id": 24, "instance": "<PERSON><PERSON><PERSON><PERSON>"}