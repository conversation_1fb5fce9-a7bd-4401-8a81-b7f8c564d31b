{"blueprint": {"base_unit": {"immutable": false, "name": "Base Measurement", "objectType": "inventory_units", "selectDisplay": "[name]", "type": "objectId"}, "chart_of_account": {"immutable": false, "name": "Chart of Account", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectId"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "current_qty": {"immutable": false, "name": "Current Quantity", "type": "float"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "string"}, "description": {"immutable": false, "name": "Description/Model", "type": "string"}, "exclusive_tax": {"immutable": false, "name": "Exclusive Tax", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "inclusive_tax": {"immutable": false, "name": "Inclusive Tax", "objectType": "tax_rates", "selectDisplay": "[name]", "type": "objectIds"}, "inventory_categories": {"immutable": false, "name": "Inventory Category", "objectType": "inventory_categories", "selectDisplay": "[name]", "type": "objectId"}, "is_deleted": {"immutable": true, "name": "Is deleted?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "measurements": {"immutable": false, "name": "Units", "type": "int"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "picture": {"immutable": false, "name": "Picture", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "stock_type": {"immutable": false, "name": "Is Perishable?", "options": {"not_perishable": "Not perishable items", "perishable": "Perishable items", "untracked": "Do not track"}, "type": "select"}, "surcharges": {"immutable": false, "name": "Surcharges", "objectType": "surcharges", "selectDisplay": "[name]", "type": "objectIds"}, "uom": {"blueprint": {"config": {"blueprint": {"dry_volume": {"immutable": false, "name": "Dry volume", "type": "int"}, "liquid_volume": {"immutable": false, "name": "Liquid volume", "type": "int"}, "quantity": {"immutable": false, "name": "Quantity", "type": "int"}, "volume_to_weight": {"immutable": false, "name": "Volume to weight ratio", "type": "float"}, "weight": {"immutable": false, "name": "Weight", "type": "int"}}, "immutable": false, "name": "Configuration", "type": "object"}, "dry_volume": {"blueprint": {"default_shipment_measurement": {"immutable": false, "name": "Default shipment measurement", "type": "int"}}, "immutable": false, "name": "Dry volume", "type": "object"}, "liquid_volume": {"blueprint": {"default_shipment_measurement": {"immutable": false, "name": "Default shipment measurement", "type": "int"}}, "immutable": false, "name": "Liquid volume", "type": "object"}, "quantity": {"blueprint": {"default_shipment_measurement": {"immutable": false, "name": "Default shipment measurement", "type": "int"}}, "immutable": false, "name": "Quantity", "type": "object"}, "weight": {"blueprint": {"default_shipment_measurement": {"immutable": false, "name": "Default shipment measurement", "type": "int"}}, "immutable": false, "name": "Weight", "type": "object"}}, "immutable": false, "name": "Units of measure", "type": "object"}, "vendor_id": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "objectType": "vendors", "selectDisplay": "[name]", "type": "objectIds"}}, "blueprint_name": "inventory_groups", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.451721", "id": 274, "instance": "<PERSON><PERSON><PERSON><PERSON>"}