{"blueprint": {"created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "id": {"immutable": true, "name": "ID", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "menu_items": {"immutable": false, "name": "Menu Items", "type": "object"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "permissions": {"immutable": false, "name": "Menu Permissions", "type": "object"}, "type": {"immutable": false, "name": "User Type", "options": {"client": "Client", "staff": "Staff"}, "type": "select"}}, "blueprint_name": "user_views", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.397833", "id": 225, "instance": "<PERSON><PERSON><PERSON><PERSON>"}