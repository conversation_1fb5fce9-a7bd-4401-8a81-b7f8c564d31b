# T1: Add 'Date Booked' to Contracts

## Overview
This task adds a 'Date Booked' field to the contract system to track when an event is officially booked. This requires adding a database field, updating the workflow system, and integrating with the document/contract merge tag system.

## Sub-tasks
| ID    | Task                                      | Status | Feature | Description                                                |
|-------|-------------------------------------------|--------|---------|-----------------------------------------------------------|
| T1.1  | Add date_booked field to companies blueprint | Done | F1   | Add new date field to companies.json blueprint             |
| T1.2  | Update workflow system for booking state  | Doing   | F1      | Track state changes to update date_booked field            |
| T1.3  | Register DATE_BOOKED merge tag            | Todo   | F1      | Create merge tag for date_booked field                     |
| T1.4  | Add merge tag to contract template        | Todo   | F1      | Update standard contract template with new merge tag       |
| T1.5  | Test implementation                       | Todo   | F1      | Verify field population and merge tag functionality        |

## Technical Specifications

### 1. Database Field Addition (T1.1)
- **Field Name:** `date_booked`
- **Field Type:** `date` (matching other date fields in blueprint)
- **Location:** Companies blueprint (`_SRC/blueprints/companies.json`)
- **Default Value:** NULL (no booking date set)
- **Immutable:** `false` (can be updated after creation)
- **Implementation:**
  ```json
  "date_booked": {
      "immutable": false,
      "name": "Date Booked",
      "type": "date"
  }
  ```

### 2. Workflow Integration (T1.2)
- **Trigger:** When an event transitions to state with uid=2 ("Booked")
- **Action:** Update the `date_booked` field with current timestamp
- **Implementation Location:** `_SRC/pagoda/rules/actions/changeWorkflow.php`
- **Implementation Details:**
  - The system already has a mechanism to detect state changes in `_SRC/pagoda/_app.php` (updateState method)
  - We need to add code to detect when an event transitions to the "Booked" state (uid=2)
  - When detected, update the `date_booked` field with the current timestamp
  - Implementation approach:
    1. Identify the section in `changeWorkflow.php` that handles state transitions
    2. Add a condition to check if the new state is "Booked" (uid=2)
    3. If true, update the `date_booked` field with current timestamp
    4. Use the existing `$objs->update()` pattern to update the field

```php
// Example implementation (to be refined based on actual code structure)
if ($newState == 2) { // "Booked" state
    $now = new DateTime('now', new DateTimezone('Africa/Abidjan'));
    $updated = $objs->update(
        $object['object_bp_type'],
        [
            'id' => $object['id'],
            'date_booked' => $now->format('Y-m-d H:i:s')
        ],
        0,
        null
    );
}
```

### 3. Merge Tag Registration (T1.3)
- **Implementation Location:** `_SRC/notify/_components/_bizdev/contracts.js`
- **Tag Format:** `{{DATE_BOOKED}}` (following existing conventions)
- **Implementation Details:**
  - Register a new merge tag using the `sb.notify` pattern with type "register-merge-tag"
  - Provide a data function to fetch the date_booked field from the object
  - Provide a parse function to format the date using moment.js
  - Follow the pattern of existing date-related merge tags

```javascript
// Example implementation for merge tag registration
sb.notify({
  type: "register-merge-tag",
  data: {
    name: "date_booked",
    tag: "DATE_BOOKED",
    data: function (obj, callback) {
      // If obj already has date_booked, use it directly
      if (obj && obj.date_booked) {
        callback(obj.date_booked);
      }
      // Otherwise, fetch it from the database
      else if (obj && obj.id) {
        sb.data.db.obj.getById(
          "groups",
          obj.id,
          function (data) {
            callback(data.date_booked || "");
          },
          {
            date_booked: true
          }
        );
      } else {
        callback("");
      }
    },
    parse: function (dateString, obj) {
      if (!dateString) return "";
      return moment(dateString).format("MM/DD/YYYY");
    }
  }
});
```

### 4. Contract Template Update (T1.4)
- **Implementation Location:** Standard contract template in the system
- **Implementation Details:**
  - Locate the standard contract template in the system
  - Add the `{{DATE_BOOKED}}` merge tag in an appropriate location
  - Ensure proper formatting and layout
  - Example: "Date Booked: {{DATE_BOOKED}}"

## Implementation Details

### T1.1: Add date_booked field to companies blueprint
- **Status:** Ready for implementation
- **File:** `_SRC/blueprints/companies.json`
- **Implementation:**
  ```json
  "date_booked": {
      "immutable": false,
      "name": "Date Booked",
      "type": "date"
  }
  ```

### T1.2: Update workflow system for booking state
- **Status:** Ready for implementation
- **File:** `_SRC/pagoda/_pgObjectsMT.php` (in the `triggerStateActions` method)
- **Implementation Strategy:**
  - Target the specific "Event Management" project type (id: 1625566) in the "infinity" instance
  - Only set `date_booked` field on first transition to "Booked" state (uid=2)
  - For subsequent transitions to "Booked", preserve original date but create a note
  - Ensure all notes get proper tagging (project tags + project ID)
  - Use different visual indicators for initial vs subsequent bookings

- **Implementation Details:**
  ```php
  // Check if this is the infinity instance and Event Management project type
  if (
      $this->instance === 'infinity' &&
      is_array($workflowObj) &&
      $workflowObj['id'] == 1625566 &&
      $workflowObj['object_bp_type'] === 'project_types' &&
      $newState == 2 // Booked state
  ) {
      // Get the object if we only have the ID
      if (!is_array($objectId)) {
          $obj = $this->getById('', $objectId);
      } else {
          $obj = $objectId;
      }

      // Update the date_booked field only if it's not already set
      if (is_array($obj) && (empty($obj['date_booked']) || $obj['date_booked'] === null)) {
          $now = new DateTime('now', new DateTimezone('UTC'));
          $this->update(
              $obj['object_bp_type'],
              [
                  'id' => $obj['id'],
                  'date_booked' => $now->format('Y-m-d H:i:s')
              ],
              0,
              null
          );

          // Get project tags to apply to the note
          $projectTags = isset($obj['tagged_with']) && is_array($obj['tagged_with']) ? $obj['tagged_with'] : [];

          // Always include the project ID in the tags
          if (!in_array($obj['id'], $projectTags)) {
              $projectTags[] = $obj['id'];
          }

          // Log a comment about the initial booking
          $this->postComment([
              'type_id' => $obj['id'],
              'note' => 'Project initially booked on ' . $now->format('Y-m-d H:i:s'),
              'record_type' => 'log',
              'log_type' => 'booking',
              'tagged_with' => $projectTags,
              'icon' => [
                  'icon' => 'calendar check',
                  'color' => 'green'
              ]
          ], 0, 0);
      } else if (is_array($obj)) {
          // Get project tags to apply to the note
          $projectTags = isset($obj['tagged_with']) && is_array($obj['tagged_with']) ? $obj['tagged_with'] : [];

          // Always include the project ID in the tags
          if (!in_array($obj['id'], $projectTags)) {
              $projectTags[] = $obj['id'];
          }

          // If already booked, log a comment about the re-booking
          $now = new DateTime('now', new DateTimezone('UTC'));
          $this->postComment([
              'type_id' => $obj['id'],
              'note' => 'Project returned to Booked state on ' . $now->format('Y-m-d H:i:s') .
                       ' (original booking date: ' . $obj['date_booked'] . ')',
              'record_type' => 'log',
              'log_type' => 'booking',
              'tagged_with' => $projectTags,
              'icon' => [
                  'icon' => 'calendar check',
                  'color' => 'blue'
              ]
          ], 0, 0);
      }
  }
  ```

### T1.3: Register DATE_BOOKED merge tag
- **Status:** Ready for implementation
- **File:** `_SRC/notify/_components/_bizdev/contracts.js` (in the `registerMergeTags` function)
- **Implementation Strategy:**
  - Register a new merge tag using the existing pattern in the system
  - Follow the format of other date-related merge tags
  - Ensure proper date formatting using moment.js
  - Handle both direct object access and database fetching

- **Implementation Details:**
  ```javascript
  // Register the DATE_BOOKED merge tag
  sb.notify({
    type: "register-merge-tag",
    data: {
      name: "date_booked",
      tag: "DATE_BOOKED",
      data: function (obj, callback) {
        // If obj already has date_booked, use it directly
        if (obj && obj.date_booked) {
          callback(obj.date_booked);
        }
        // Otherwise, fetch it from the database
        else if (obj && obj.id) {
          sb.data.db.obj.getById(
            obj.object_bp_type || "groups",
            obj.id,
            function (data) {
              callback(data.date_booked || "");
            },
            {
              date_booked: true
            }
          );
        } else {
          callback("");
        }
      },
      parse: function (dateString, obj) {
        if (!dateString) return "";
        return moment(dateString).format("MM/DD/YYYY");
      }
    }
  });
  ```

### T1.4: Add merge tag to contract template
- **Status:** Ready for implementation
- **Implementation Strategy:**
  - Identify the standard contract template used for events
  - Add the `{{DATE_BOOKED}}` merge tag in an appropriate location
  - Ensure proper formatting and context

- **Implementation Details:**
  1. Log in to the Infinity instance
  2. Navigate to the document templates section
  3. Locate the standard contract template for events
  4. Add the following line in the appropriate section:
     ```
     Date Booked: {{DATE_BOOKED}}
     ```
  5. Save the template

### Key Design Decisions:
1. **Preserve Original Booking Date:** We only set the `date_booked` field on the first transition to "Booked" state, preserving the historical record of when the project was initially booked.

2. **Track Subsequent Bookings:** For projects that return to "Booked" state after being in another state, we create a note with both the current date and the original booking date, maintaining a complete audit trail.

3. **Visual Differentiation:** We use different colored icons (green for initial booking, blue for subsequent bookings) to make it easy to distinguish between these events in the UI.

4. **Proper Tagging:** All notes are tagged with both the project's existing tags and the project ID itself, ensuring they appear in relevant views and searches.

5. **Low-Level Implementation:** By implementing in the `triggerStateActions` method, we catch both manual and automated state transitions, ensuring consistent behavior regardless of how the state change occurs.

6. **Consistent Merge Tag Pattern:** We follow the established pattern for date-related merge tags in the contracts.js file, ensuring consistency with the rest of the system.

7. **Standard Date Formatting:** We use the MM/DD/YYYY format for consistency with other date fields in the system.

## Testing Criteria (T1.5)

### Manual Testing - First Booking
1. Create new project in the "Event Management" project type
2. Verify `date_booked` field is initially NULL
3. Manually transition project to "Booked" state (uid=2)
4. Verify `date_booked` field is populated with current date
5. Verify a note is created with:
   - Text "Project initially booked on [timestamp]"
   - Green calendar check icon
   - All project tags applied to the note
   - Project ID included in the note's tagged_with array

### Manual Testing - Subsequent Booking
1. Take a project that has already been booked (has date_booked set)
2. Transition project to a different state (e.g., "Assigned")
3. Transition project back to "Booked" state
4. Verify `date_booked` field remains unchanged (preserves original date)
5. Verify a new note is created with:
   - Text "Project returned to Booked state on [timestamp] (original booking date: [original date])"
   - Blue calendar check icon
   - All project tags applied to the note
   - Project ID included in the note's tagged_with array

### Automated Testing - Workflow Rules
1. Create a workflow rule that automatically transitions a project to "Booked" state
2. Trigger the rule on a new project
3. Verify `date_booked` field is populated with current date
4. Verify appropriate note is created with all required tags

### Automated Testing - Chained Transitions
1. Create a workflow rule that chains multiple state transitions, ending with "Booked"
2. Trigger the rule on a new project
3. Verify `date_booked` field is populated with current date
4. Verify appropriate note is created with all required tags

### Merge Tag Testing
1. Create a new contract document for a project with a populated `date_booked` field
2. Verify the {{DATE_BOOKED}} merge tag is replaced with the correct formatted date
3. Test with projects that have no booking date to ensure graceful handling of empty values
4. Test with the standard contract template to ensure proper formatting and display

### Edge Cases
1. Test with a project that has no tags
2. Test with a project that has many tags
3. Test with a project that has been moved to "Booked" multiple times
4. Test with projects in other project types to ensure they're not affected

## Notes
- The merge tag system uses a combination of registration, data fetching, and parsing
- Date formatting should follow existing patterns (MM/DD/YYYY format)
- The system supports both direct property access and custom data fetching
- Merge tags are processed at runtime when generating contract documents
